package org.thingsboard.server.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

/**
 * 邮件发送
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-03-03
 */
@Component
public class MailUtil {
    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

     // 收件人邮箱
    @Value("${mail.receiver}")
    private String receiver;

    /***
     * 邮箱发件服务
     * @param subject  主题
     * @param content  内容
     */
    public void sendSimpleMail(String subject, String content) {
        SimpleMailMessage simpleMailMessage = new SimpleMailMessage();
        simpleMailMessage.setFrom(from);
        simpleMailMessage.setTo(receiver.split(","));
        simpleMailMessage.setSubject(subject);
        simpleMailMessage.setText(content);
        try{
            mailSender.send(simpleMailMessage);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
