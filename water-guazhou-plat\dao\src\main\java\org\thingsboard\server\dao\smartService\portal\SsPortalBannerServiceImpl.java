package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBanner;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalBannerMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBannerPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBannerSaveRequest;

@Service
public class SsPortalBannerServiceImpl implements SsPortalBannerService {
    @Autowired
    private SsPortalBannerMapper mapper;

    @Override
    public SsPortalBanner findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalBanner> findAllConditional(SsPortalBannerPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalBanner save(SsPortalBannerSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalBanner entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean active(SsPortalActiveRequest req) {
        return mapper.active(req);
    }

}
