package org.thingsboard.server.controller.workOrder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemWorkOrderType;
import org.thingsboard.server.dao.smartService.system.SystemWorkOrderTypeService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 工单类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@RestController
@RequestMapping("api/ss/workOrder/type")
public class SystemWorkOrderTypeController extends BaseController {
    @Autowired
    private SystemWorkOrderTypeService systemWorkOrderTypeService;


    @GetMapping("tree")
    public IstarResponse getTree(@RequestParam(required = false, defaultValue = "") String isDel) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(systemWorkOrderTypeService.getTree(isDel, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody SystemWorkOrderType systemWorkOrderType) throws ThingsboardException {
        systemWorkOrderType.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(systemWorkOrderTypeService.save(systemWorkOrderType));
    }


    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return systemWorkOrderTypeService.delete(ids);
    }

    @DeleteMapping("hard")
    public IstarResponse deleteHard(@RequestBody List<String> ids) {
        return systemWorkOrderTypeService.deleteHard(ids);
    }
}
