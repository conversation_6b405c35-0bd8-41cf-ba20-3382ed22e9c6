import { createApp } from 'vue';
import print from 'vue3-print-nb'; // 打印
// pinia
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus'; // 引入element-plus库
import esriConfig from '@arcgis/core/config';
import VForm3 from '@/../lib/vform/designer.umd.js'; // 引入VForm 3库
import '../lib/vform/designer.style.css'; // 引入VForm3样式
import VForm3Render from '@/../lib/vform/render.umd.js'; // 引入VForm 3库
import '../lib/vform/render.style.css'; // 引入VForm3样式

import App from './App.vue';
import router from './router';
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets
import './assets/icons/font_lcd/font_lcd.css'; // led数字字体
import './styles/element-plus/index.scss'; // overrided element-plus styles
import './assets/icons/iconfont/iconfont.css'; // iconfont
import './assets/icons/iconfont/iconfont.js';
import 'video.js/dist/video-js.css';
import './styles/index.scss'; // global css
import 'leaflet/dist/leaflet.css';
import installElementPlus from './plugins/element/element';
import './assets/icons/aliIcon/iconfont.css'; // ali icon

// import installAmap from '@/plugins/maps/amap'

import installEcharts from './plugins/echart/index';
/** 模拟数据 */
// import './mock'
// 注册生成svg 雪碧图
// import 'virtual:svg-icons-register';

// esriConfig.assetsPath = window.SITE_CONFIG.GIS_CONFIG.gisSDK + '/@arcgis/core/assets/'
import '@arcgis/core/assets/esri/themes/dark/main.css';
import './plugins/dayjs';
esriConfig.assetsPath = '/Arcgis/assets';
const app = createApp(App);

// 注册pinia
app.use(createPinia());
app.use(router);
installEcharts(app);

app.use(ElementPlus); // 全局注册element-ui
app.use(VForm3); // 全局注册VForm(同时注册了v-form-designer和v-form-render组件)
app.use(VForm3Render); // 全局注册VForm(同时注册了v-form-designer和v-form-render组件)

installElementPlus(app);
app.use(print);
app.mount('#app');

export default app;
