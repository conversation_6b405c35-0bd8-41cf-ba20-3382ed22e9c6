import{h as m}from"./headwaterMonitoring-BgK7jThW.js";import{d as y,r as f,c as n,am as h,bB as x,g as l,n as r,p as s,bh as d,i as c,dy as S,C as w}from"./index-r0dFAfgr.js";const k={class:"wrapper"},I={class:"left"},b={class:"circle"},g={class:"count flex-baseline"},B={class:"value"},T={class:"right",style:{display:"flex"}},N={class:"item-block isActive"},W={class:"text-box"},z={key:1,class:"empty"},A=y({__name:"ZGS",props:{waterSupply:{}},setup(p){const v=f({curItemIndex:0,blockItems:[],curItemName:"梓莲达"}),o=p,t=n();h(()=>o.waterSupply,()=>{t.value=o.waterSupply,u()});const u=async()=>{var i;try{const e=(i=(await m({name:o.waterSupply.name})).data)==null?void 0:i.data;t.value.waterSupplyTotal=e,await x(),v.curItemIndex=0}catch{}},_=n();return(i,a)=>{var e;return l(),r("div",k,[s("div",I,[s("div",b,[s("div",g,[s("span",B,d(((e=c(t))==null?void 0:e.waterSupplyTotal)??"--"),1),a[0]||(a[0]=s("span",{class:"unit"},"m³",-1))]),a[1]||(a[1]=s("div",{class:"text"},[s("span",null," 总供水量 ")],-1))])]),s("div",T,[c(t)?(l(),r("div",{key:0,ref_key:"refWrapper",ref:_,class:"items overlay-y onlyone"},[s("div",N,[a[2]||(a[2]=S('<div class="line horizontal" data-v-52320679><div class="line-frag inclined" data-v-52320679></div><div class="line-frag curve" data-v-52320679></div><div class="line-frag horizontal" data-v-52320679></div></div><div class="shin-block" data-v-52320679></div>',2)),s("div",W,d(c(t).name),1)])],512)):(l(),r("div",z," 暂无总供水量信息 "))])])}}}),G=w(A,[["__scopeId","data-v-52320679"]]);export{G as default};
