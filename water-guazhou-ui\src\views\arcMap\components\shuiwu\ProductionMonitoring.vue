<template>
  <teleport v-if="telport" :to="telport">
    <div v-if="state.marks?.length" class="infowindow-container">
      <ListWindow
        v-for="(pop, j) in state.marks"
        :key="j"
        :ref="'refPop' + pop.attributes.id"
        :view="props.view"
        :config="pop"
        @highlight="highlightPop"
        @more="(attrs) => handleDetail(attrs)"
      >
      </ListWindow>
    </div>
  </teleport>

  <Form ref="refForm" :config="FormConfig"></Form>
  <div class="table-box">
    <FormTable :config="TableConfig"></FormTable>
  </div>
  <Panel
    v-if="telport"
    ref="refPanel"
    :telport="telport"
    :draggable="false"
    :title="TableConfig.currentRow?.name"
    :custom-class="'production-panel-left'"
  >
    <Form ref="refForm_Detail" :config="FormConfig_Detail"></Form>
  </Panel>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js';
import { getConstantsAttributeById } from '@/api/constants';
import {
  GetStationAlarmList,
  GetStationDetail,
  GetStationRealTimeDetail,
  GetStationRecentDataByType
} from '@/api/shuiwureports/zhandian';
import { useBusinessStore } from '@/store';
import { initStationType } from '@/views/basicDataManage/stationManage/data';
import {
  bindViewClick,
  createPictureMarker,
  createTextMarker,
  getGraphicLayer
} from '@/utils/MapHelper';
import { formatDate } from '@/utils/DateFormatter';
import { queryDeviceDataIstar } from '@/api/tsdb';
import { getDeviceByID } from '@/api/device';
import { removeSlash } from '@/utils/removeIdSlash';
import { initStationBasicTableColumn } from '@/views/pipeNetwork/monitoring/data';
import ListWindow from '../popup/ListWindow.vue';
import mapLoacation from '../../../../assets/images/map-location.png';

const { proxy }: any = getCurrentInstance();
const refPanel = ref<IPanelIns>();
const props = defineProps<{
  view?: __esri.MapView;
  telport?: any;
}>();
const state = reactive<{
  curValues: any[];
  marks: IArcMarkerProps[];
}>({
  curValues: [],
  marks: []
});
const staticState: {
  graphicsLayer?: __esri.GraphicsLayer;
  textLayer?: __esri.GraphicsLayer;
} = {};
const refForm = ref<IFormIns>();
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [{ label: '名称', prop: 'name' }],
  handleRowClick: (row) => {
    TableConfig.currentRow = row;
    resetForm_Left(row);
    refPanel.value?.Open();
    props.view?.goTo(
      new Point({
        longitude: row.x,
        latitude: row.y,
        spatialReference: props.view.spatialReference
      })
    );
  },
  pagination: {
    hide: true
  }
});
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'tabs',
          field: 'type',
          label: '',
          tabType: 'simple',
          readonly: () => {
            return !!TableConfig.loading;
          },
          tabs: initStationType(),
          // .map(item => ({
          //   label: item.label,
          //   value: item.value?.toString()
          // })),
          onChange: (val: any) => refreshRightTable(val)
        }
      ]
    }
  ]
});

const refreshRightTable = async (
  type: string = refForm.value?.dataForm?.type
) => {
  if (!type) {
    type =
      (FormConfig.group[0]?.fields[0] as ITabs)?.tabs[0]?.value?.toString() ||
      '';
    refForm.value?.dataForm && (refForm.value.dataForm.type = type);
    // 直接返回，因为修改了type重新触发此方法
    return;
  }

  TableConfig.loading = true;
  const res = await GetStationRecentDataByType({
    stationType: type,
    projectId: useBusinessStore().navSelectedRange?.data?.id
  });
  const colRes = await getConstantsAttributeById({
    type: 'stationInfo',
    key: type
  });
  let dynamicColumns: { label: string; value: string }[] = [];
  try {
    dynamicColumns = colRes.data[0] ? JSON.parse(colRes.data[0].value) : [];
  } catch (error) {
    dynamicColumns = [];
  }

  TableConfig.columns = initTableRightColumn(
    dynamicColumns.map((item) => ({
      minWidth: 120,
      label: item.label,
      prop: item.value
    }))
  );
  closePop();
  state.marks = [];
  TableConfig.dataList =
    res.data?.map((item) => {
      const location = item.location?.split(',');
      item.status = item.status || 'offline';
      item.x = location[0] && parseFloat(location[0]);
      item.y = location[1] && parseFloat(location[1]);
      const markData: IArcMarkerProps = {
        // showMore: true,
        title: item.name,
        visible: false,
        x: item.x,
        y: item.y,
        attributes: {
          id: item.stationId,
          values: []
        },
        symbolConfig: {
          url: mapLoacation
        }
      };
      state.marks.push(markData);
      return item;
    }) || [];
  staticState.graphicsLayer?.removeAll();
  staticState.textLayer?.removeAll();
  addMarks();
  TableConfig.loading = false;
  const tagForm = FormConfig.group[0].fields[0] as IFormRadio;
  tagForm.options?.map((item) => {
    item.data.suffix =
      item.value === ''
        ? res.data.length
        : item.value === 'online'
          ? res.data.filter((a) => a.status === 'online').length
          : item.value === 'warning'
            ? res.data.filter((a) => a.status === 'warning').length
            : item.value === 'offline'
              ? res.data.filter((a) => a.status === 'offline').length
              : 0;
  });
  state.curValues = dynamicColumns;
};

const initTableRightColumn = (
  dynamicColumns: IFormTableColumn[]
): IFormTableColumn[] => {
  return [
    { minWidth: 120, label: '名称', prop: 'name' },
    { minWidth: 160, label: '时间', prop: 'time' },
    ...(dynamicColumns || []),
    {
      width: 70,
      label: '状态',
      prop: 'pressure_status',
      formatter: (row, val) => {
        return val === 'online' ? '在线' : '离线';
      }
    }
  ];
};

const TableConfig_BasicInfo = reactive<ITable>({
  height: 250,
  columns: [
    { label: '名称', prop: 'name' },
    { label: '值', prop: 'value' }
  ],
  dataList: [],
  pagination: { hide: true }
});

const refreshBasicData = async (id: string) => {
  if (!id) TableConfig_BasicInfo.dataList = initStationBasicTableColumn({});
  const res = await GetStationDetail(id);
  TableConfig_BasicInfo.dataList = initStationBasicTableColumn(res.data || {});
};
const TableConfig_RealTime = reactive<ITable>({
  height: 300,
  columns: [
    { minWidth: 100, label: '名称', prop: 'propertyName' },
    {
      minWidth: 140,
      label: '时间',
      prop: 'collectionTime',
      formatter: (row) => formatDate(row.collectionTime)
    },
    { minWidth: 120, label: '监测值', prop: 'value' }
  ],
  dataList: [],
  pagination: { hide: true },
  handleRowClick: (row) => {
    TableConfig_RealTime.currentRow = row;
    refreshHistoryData();
  }
});
const refreshRealTime = async (row: any) => {
  const res = await GetStationRealTimeDetail(row.stationId);
  TableConfig_RealTime.dataList =
    res.data?.map((item) => {
      item.value = (item.value || '') + ' ' + (item.unit || '');
      return item;
    }) || [];
  TableConfig_RealTime.currentRow = TableConfig_RealTime.dataList[0];
  refreshHistoryData();
};
const TableConfig_History = reactive<ITable>({
  height: 300,
  columns: [
    { label: '设备名称', prop: 'name' },
    { label: '时间', prop: 'time' },
    { label: '压力值', prop: 'value' }
  ],
  dataList: [],
  pagination: { hide: true }
});
const refreshHistoryData = async () => {
  const row = TableConfig_RealTime.currentRow;
  if (!row) {
    TableConfig_History.dataList = [];
    return;
  }
  const column = TableConfig_History.columns.find(
    (item) => item.prop === 'value'
  );
  column && (column.label = row.propertyName);
  const deviceId = removeSlash(row.deviceId);
  const prop = row.property;
  if (deviceId && prop) {
    const res = await queryDeviceDataIstar({
      start: moment().subtract(1, 'd').valueOf(),
      end: moment().valueOf(),
      type: '15m',
      attributes: [deviceId + '.' + prop]
    });
    const deviceRes = await getDeviceByID(row.deviceId);
    const data = res.data || {};
    const dataList: any[] = [];
    for (const key in data) {
      const dataItem: any = {
        time: key,
        name: deviceRes.data?.name
      };
      const cData = data[key] || {};
      let index = 0;
      for (const cKey in cData) {
        index++;
        dataItem.value = (cData[cKey] && cData[cKey] + ' ' + row.unit) || '';
        if (index === 1) break;
      }
      dataList.push(dataItem);
    }
    TableConfig_History.dataList = dataList;
  } else {
    TableConfig_History.dataList = [];
  }
};
const TableConfig_Alarm = reactive<ITable>({
  height: 300,
  columns: [
    { label: '名称', prop: 'name' },
    { label: '时间', prop: 'time' },
    { label: '报警值', prop: 'value' },
    { label: '状态', prop: 'status' }
  ],
  dataList: [],
  pagination: { hide: true }
});
const refreshAlarmData = async (id: string) => {
  const res = await GetStationAlarmList(id);
  TableConfig_Alarm.dataList =
    res.data?.data?.map((item) => {
      const record = item.details.record || [];
      return {
        name: record[0]?.info,
        status: item.type,
        remark: item.deviceName + ' ' + (record[0]?.info || ''),
        time: formatDate(item.startTs)
      };
    }) || [];
};
const FormConfig_Detail = reactive<IFormConfig>({
  gutter: 0,
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '基础属性'
      },
      fields: [{ type: 'table', config: TableConfig_BasicInfo }]
    },
    {
      fieldset: {
        type: 'underline',
        desc: '实时数据'
      },
      fields: [{ type: 'table', config: TableConfig_RealTime }]
    },
    {
      fieldset: {
        type: 'underline',
        desc: '历史数据'
      },
      fields: [{ type: 'table', config: TableConfig_History }]
    },
    {
      fieldset: {
        type: 'underline',
        desc: '报警信息'
      },
      fields: [{ type: 'table', config: TableConfig_Alarm }]
    }
  ]
});
const highlightPop = (pop: IArcMarkerProps) => {
  pop.highLight = true;
  // const marks = type?state.markData
  // state.markData.find(item => item.type === state.panelOperate)?.marks.map(item => item.attributes.highLight = item.attributes.id === pop.attributes.id)
};
const resetForm_Left = (row: any) => {
  refreshBasicData(row.stationId);
  refreshAlarmData(row.stationId);
  refreshRealTime(row).then(() => {
    openPop(row.stationId);
  });
};
const addMarks = () => {
  /** *****添加图片标注和文本标注 **** */
  TableConfig.dataList.map((item) => {
    const point = new Point({
      longitude: item.x,
      latitude: item.y,
      spatialReference: props.view?.spatialReference
    });
    const picturemark = createPictureMarker(point.x, point.y, {
      picUrl: mapLoacation,
      spatialReference: props.view?.spatialReference,
      attributes: {
        id: item.stationId,
        row: item
      }
    });
    const textmark = createTextMarker(point.x, point.y, {
      text: item.name,
      spatialReference: props.view?.spatialReference,
      yOffset: -20
    });
    staticState.graphicsLayer?.add(picturemark);
    staticState.textLayer?.add(textmark);
  });
};
const openPop = (id: string) => {
  const pop = proxy.$refs['refPop' + id];
  pop?.length && pop[0]?.open();
  const mark = state.marks.find((item) => item.attributes.id === id);
  if (!mark) return;
  mark.attributes.values = TableConfig_RealTime.dataList.map((item) => {
    return {
      label: item.propertyName,
      value: item.value
    };
  });
};
const closePop = (ids?: string[]) => {
  const marks = ids?.length
    ? state.marks.filter((item) => ids.indexOf(item.attributes.id) !== -1)
    : state.marks;
  marks.map((item) => {
    const id = item.attributes.id;
    if (!id) return;
    const pop = proxy.$refs['refPop' + id];
    pop?.length && pop[0]?.close();
  });
};
const handleDetail = (attrs) => {
  console.log(attrs);
};
onMounted(() => {
  if (!props.view) return;
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'production_monitoring',
    title: '生产监控'
  });
  staticState.textLayer = getGraphicLayer(props.view, {
    id: 'production_monitoring_poi',
    title: '生产监控-注记'
  });
  /** 监听点击事件，并根据情况打开弹窗 * */
  props.view &&
    bindViewClick(props.view, (res) => {
      if (!res.results.length) return;
      res.results.map((item) => {
        if (item.type === 'graphic') {
          const id = item.graphic?.attributes?.id;
          if (id) {
            openPop(id);
            resetForm_Left(item.graphic?.attributes?.row);
          }
        }
      });
    });
  nextTick(() => {
    refreshRightTable();
  });
});
onBeforeUnmount(() => {
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
  staticState.textLayer && props.view?.map.remove(staticState.textLayer);
});
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 80px);
}
</style>
<style lang="scss">
.panel {
  &.production-panel-left {
    left: 0;
  }

  &.production-panel-left {
    top: 0;
    width: 400px;
    height: 100%;

    .content {
      width: 376px;
      height: 100%;
    }
  }
}
</style>
