package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyPlan;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyPlanMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyPlanSaveRequest;

@Service
public class EmergencyPlanServiceImpl implements EmergencyPlanService {
    @Autowired
    private EmergencyPlanMapper mapper;

    @Override
    public IPage<EmergencyPlan> findAllConditional(EmergencyPlanPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public EmergencyPlan save(EmergencyPlanSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(EmergencyPlan entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
