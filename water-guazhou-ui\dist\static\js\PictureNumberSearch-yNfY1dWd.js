import{d as I,c as y,r as O,b as l,bB as _,o as N,g as S,n as D,q as g,i as n,_ as G,C as L}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import{e as C,i as k}from"./QueryHelper-ILO3qZqg.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import B from"./PipeDetail-CTBPYFJW.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-fy91bijz.js";const E=I({__name:"PictureNumberSearch",props:{view:{},telport:{}},setup(v){const s=v,m=y(),c=y(),t=O({pipeLayerOption:[],curOperate:"",tabs:[]}),d=O({group:[{fieldset:{desc:"图层名称"},fields:[{type:"select",field:"layer",options:[]}]},{fieldset:{desc:"图幅号"},fields:[{type:"input",field:"DESIGNNO"},{type:"btn-group",btns:[{perm:!0,loading:()=>t.curOperate==="detailing",text:()=>t.curOperate==="detailing"?"正在查询":"查询",click:()=>w(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),b=()=>{var p,a,i,o;if(!s.view)return;const r=(p=s.view)==null?void 0:p.map.findLayerById("pipelayer");t.pipeLayerOption=[],(a=r==null?void 0:r.sublayers)==null||a.map(u=>{var f;(f=t.pipeLayerOption)==null||f.push({label:u.title,value:u.title,id:u.id})});const e=d.group[0].fields[0];e&&(e.options=t.pipeLayerOption),(i=m.value)!=null&&i.dataForm&&(m.value.dataForm.layer=t.pipeLayerOption&&((o=t.pipeLayerOption[0])==null?void 0:o.value))},w=async()=>{var p,a;const r=t.pipeLayerOption.find(i=>{var o;return i.label===((o=m.value)==null?void 0:o.dataForm.layer)});if(!r){l.warning("请选择图层"),t.curOperate="";return}const e=(a=(p=m.value)==null?void 0:p.dataForm)==null?void 0:a.DESIGNNO;if(!e||e.trim&&e.trim()===""){l.warning("请输入图幅号"),t.curOperate="";return}try{t.curOperate="detailing";const i=await C(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+r.id,k({returnGeometry:!1,where:" DESIGNNO like '%"+e+"%'",orderByFields:["OBJECTID asc"]}));i!=null&&i.length?(t.tabs=[{label:r.label,name:r.label,id:r.id,data:i}],_(()=>{var o;(o=c.value)==null||o.openDialog()})):(l.info("查询结果为空"),t.curOperate="",t.tabs=[])}catch{l.error("查询失败，请检查查询条件是否正确"),t.curOperate=""}},F=async()=>{var r;s.view&&((r=c.value)==null||r.extentTo(s.view))};return N(()=>{b()}),(r,e)=>{const p=G;return S(),D("div",null,[g(p,{ref_key:"refForm",ref:m,config:n(d)},null,8,["config"]),g(B,{ref_key:"refDetail",ref:c,tabs:n(t).tabs,telport:r.telport,onRefreshed:e[0]||(e[0]=()=>n(t).curOperate="viewingDetail"),onRefreshing:e[1]||(e[1]=()=>n(t).curOperate="detailing"),onClose:e[2]||(e[2]=a=>n(t).curOperate=""),onRowdblclick:F},null,8,["tabs","telport"])])}}}),rr=L(E,[["__scopeId","data-v-43065ef9"]]);export{rr as default};
