package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionLossPoint;
import org.thingsboard.server.dao.smartPipe.PartitionLossPointService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-分区漏点记录
 */
@RestController
@RequestMapping("api/spp/dma/partition/lossPoint")
public class PartitionLossPointController extends BaseController {

    @Autowired
    private PartitionLossPointService partitionLossPointService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionLossPoint partitionLossPoint) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionLossPoint.setTenantId(tenantId);

        return IstarResponse.ok(partitionLossPointService.save(partitionLossPoint));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionLossPointService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionLossPointService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
