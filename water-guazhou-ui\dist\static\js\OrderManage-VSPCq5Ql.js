import{d as R,c as n,r as d,s as b,l as i,bH as u,S as I,b as p,o as M,g as L,n as N,q as l,i as U,F as B,b6 as W,b7 as q,aj as A}from"./index-r0dFAfgr.js";import{_ as V}from"./CardTable-rdWOL4_6.js";import{_ as G}from"./CardSearch-CB_HNR-Q.js";import j from"./OrderStepTags-CClNfq4j.js";import z from"./detail-CU6-qhMl.js";import{b as H,g as $,a as J,j as K,S as Q}from"./config-DqqM5K5L.js";import{i as X,c as Y,T as Z}from"./index-CpGhZCTT.js";import{u as ee}from"./useUser-Blb5V02j.js";import{u as te}from"./useStopWorkOrder-8kgjTMvz.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const re={class:"wrapper"},Te=R({__name:"OrderManage",setup(ae){const{getUserOptions:D}=ee(),m=n(),h=n(),g=n(),_=n(),y=n(),O=d({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),S=n(""),T=d({filters:[{field:"title",label:"标题",type:"input"},{field:"date",label:"发起时间",type:"daterange"},{field:"source",label:"来源",type:"select",options:H()},{field:"level",label:"紧急程度",type:"select",options:$()},{field:"type",label:"类型",type:"select-tree",options:J()}],operations:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",icon:"iconfont icon-chaxun",click:()=>s()},{perm:!0,type:"default",text:"重置",svgIcon:b(q),click:()=>w()},{perm:!0,type:"warning",text:"导出",svgIcon:b(A),click:()=>F()}]}],handleSearch:()=>s(),defaultParams:{date:[i().subtract(1,"M").format(u),i().format(u)]}}),r=d({expandable:!0,indexVisible:!0,defaultExpandAll:!0,handleSelectChange:e=>{r.selectList=e},expandComponent:b(j),columns:K(),dataList:[],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,s()}},operationWidth:220,operations:[{perm:!0,text:"详情",isTextBtn:!0,click:e=>k(e)},{perm:!0,text:"变更处理人",isTextBtn:!0,disabled:e=>!e.status||["PENDING","ASSIGN","APPROVED","TERMINATED","SUBMIT"].indexOf(e.status)!==-1,click:e=>{var t;r.currentRow=e,(t=g.value)==null||t.openDrawer()}},{perm:!0,text:"终止",isTextBtn:!0,disabled:e=>Q.includes(e.status),click:e=>{var t;r.currentRow=e,(t=_.value)==null||t.openDrawer()}}]}),f=d({width:500,title:"变更处理人",labelPosition:"top",group:[{fields:[{type:"select",label:"新的处理人：",field:"stepProcessUserId",rules:[{required:!0,message:"请选择新的处理人"}],options:[]}]}],submit:e=>{I("确定变更？","提示信息").then(async()=>{var t;f.submitting=!0;try{const a=await X(r.currentRow.id,e);a.data.code===200?(p.success("操作成功"),s(),(t=g.value)==null||t.closeDrawer()):p.error(a.data.err||"操作失败")}catch{p.error("系统错误")}f.submitting=!1}).catch(()=>{})}}),{FormConfig_Stop:C}=te(async e=>{var a,o;const t=await Z((a=r.currentRow)==null?void 0:a.id,e);t.data.code===200?(p.success("操作成功"),s(),(o=_.value)==null||o.closeDrawer()):p.error(t.data.err||"操作失败")}),k=e=>{var t;S.value=e.id||"",O.title=e.serialNo,(t=y.value)==null||t.openDrawer()},s=async()=>{var e,t,a;r.loading=!0;try{const o=((e=m.value)==null?void 0:e.queryParams)||{},[c,P]=((t=o.date)==null?void 0:t.length)===2?[i(o.date[0],u).valueOf(),i(o.date[1],u).endOf("D").valueOf()]:[i().subtract(1,"M").startOf("D").valueOf(),i().endOf("D").valueOf()],v={page:r.pagination.page||1,size:r.pagination.limit||20,...o,fromTime:c,toTime:P};delete v.date;const x=(a=(await Y(v)).data)==null?void 0:a.data;r.dataList=x.data,r.pagination.total=x.total}catch{}r.loading=!1},w=()=>{var e;(e=m.value)==null||e.resetForm(),s()},F=()=>{var e;(e=h.value)==null||e.exportTable()},E=async()=>{const e=await D(!1,{authority:"CUSTOMER_USER"}),t=f.group[0].fields[0];t.options=e};return M(()=>{E(),s()}),(e,t)=>{const a=G,o=V,c=W;return L(),N("div",re,[l(a,{ref_key:"refSearch",ref:m,config:T},null,8,["config"]),l(o,{ref_key:"refTable",ref:h,class:"card-table",config:r},null,8,["config"]),l(c,{ref_key:"refFormChangeDealer",ref:g,config:f},null,8,["config"]),l(c,{ref_key:"refForm_Stop",ref:_,config:U(C)},null,8,["config"]),l(c,{ref_key:"refdetail",ref:y,config:O},{default:B(()=>[l(z,{id:S.value},null,8,["id"])]),_:1},8,["config"])])}}});export{Te as default};
