<!-- gis车辆监控 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
      style="margin-bottom: 10px"
    ></Cards>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { IFormIns } from '@/components/type'

defineEmits(['highlightMark', 'addMarks'])
defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()

const cardsvalue = ref([
  { label: '0 辆', value: '车辆总数' },
  { label: '0 辆', value: '外出数量' }
])
const TableConfig = reactive<ITable>({
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      minWidth: 120,
      label: '车牌号',
      prop: 'key1'
    },
    {
      minWidth: 120,
      label: '品牌',
      prop: 'key2'
    },
    {
      minWidth: 120,
      label: '用车人',
      prop: 'key3'
    },
    {
      minWidth: 120,
      label: '状态',
      prop: 'key4'
    }
  ],
  handleRowClick: row => {
    TableConfig.currentRow = row
    // refDetail.value?.openDialog()
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 110px);
}
</style>
