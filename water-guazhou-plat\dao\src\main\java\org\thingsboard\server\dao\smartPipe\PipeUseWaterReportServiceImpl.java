package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.DTO.PipeUseWaterReportDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeUseWaterReport;
import org.thingsboard.server.dao.sql.smartPipe.PipeUseWaterReportMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 *
 */
@Service
public class PipeUseWaterReportServiceImpl implements PipeUseWaterReportService {

    @Autowired
    private PipeUseWaterReportMapper pipeUseWaterReportMapper;


    @Override
    public IstarResponse save(PipeUseWaterReport pipeUseWaterReport) {
        if (StringUtils.isBlank(pipeUseWaterReport.getPartitionId())) {
            return IstarResponse.error("请选择填报分区");
        }
        if (StringUtils.isBlank(pipeUseWaterReport.getYm())) {
            return IstarResponse.error("请选择填表年月");
        }


        // 当月是否填报
        if (StringUtils.isBlank(pipeUseWaterReport.getId())) {
            PipeUseWaterReport byPartitionIdAndYm = this.getByPartitionIdAndYm(pipeUseWaterReport.getPartitionId(), pipeUseWaterReport.getYm());
            if (byPartitionIdAndYm != null) {
                return IstarResponse.error("该分区已填报该年月，请查询后重新填写");
            }
            pipeUseWaterReport.setCreateTime(new Date());

            // 统计值
            this.sum(pipeUseWaterReport);
            pipeUseWaterReportMapper.insert(pipeUseWaterReport);
        } else {
            // 统计值
            this.sum(pipeUseWaterReport);
            pipeUseWaterReportMapper.updateById(pipeUseWaterReport);
        }
        return IstarResponse.ok(pipeUseWaterReport);
    }

    @Override
    public PipeUseWaterReportDTO getWaterBalance(String partitionId, String ym) {
        if (ym.length() > 4) {
            PipeUseWaterReportDTO waterBalanceMonth = pipeUseWaterReportMapper.getWaterBalanceMonth(partitionId, ym);
            this.analysis(waterBalanceMonth);

            return waterBalanceMonth;
        }
        PipeUseWaterReportDTO pipeUseWaterReportDTO = pipeUseWaterReportMapper.getWaterBalance(partitionId, ym);
        if (pipeUseWaterReportDTO == null) {
            pipeUseWaterReportDTO = new PipeUseWaterReportDTO();
            this.analysis(pipeUseWaterReportDTO);

            return pipeUseWaterReportDTO;
        }
        // 管道数据
        PipeUseWaterReportDTO systemData = pipeUseWaterReportMapper.getSystemData(partitionId, ym);
        pipeUseWaterReportDTO.setDn75PipeLength(systemData.getDn75PipeLength());
        pipeUseWaterReportDTO.setUnitSupplyPipeLength(systemData.getUnitSupplyPipeLength());
        pipeUseWaterReportDTO.setYearAvgPressure(systemData.getYearAvgPressure());
        pipeUseWaterReportDTO.setCustCopiedWater(systemData.getCustCopiedWater());
        pipeUseWaterReportDTO.setMaxFrozenSoilDepth(systemData.getMaxFrozenSoilDepth());

        this.analysis(pipeUseWaterReportDTO);

        return pipeUseWaterReportDTO;
    }

    @Override
    public List<PipeUseWaterReport> getYearDetail(String partitionId, String year) {
        List<PipeUseWaterReport> result = new ArrayList<>();

        QueryWrapper<PipeUseWaterReport> pipeUseWaterReportQueryWrapper = new QueryWrapper<>();
        pipeUseWaterReportQueryWrapper.eq("partition_id", partitionId);
        pipeUseWaterReportQueryWrapper.like("ym", year);
        pipeUseWaterReportQueryWrapper.orderByAsc("ym");

        List<PipeUseWaterReport> pipeUseWaterReports = pipeUseWaterReportMapper.selectList(pipeUseWaterReportQueryWrapper);
        Map<String, PipeUseWaterReport> stringPipeUseWaterReportMap = new HashMap<>();
        for (PipeUseWaterReport pipeUseWaterReport : pipeUseWaterReports) {
            stringPipeUseWaterReportMap.put(pipeUseWaterReport.getYm(), pipeUseWaterReport);
        }

        String month;
        PipeUseWaterReport pipeUseWaterReport;
        Class<PipeUseWaterReport> pipeUseWaterReportClass = PipeUseWaterReport.class;
        Field[] fields;
        for (int i = 0; i < 12; i++) {
            month = (i + 1) + "";
            if (i < 10) {
                month = "0" + month;
            }
            month = year + "-" + month;
            pipeUseWaterReport = new PipeUseWaterReport();
            if (stringPipeUseWaterReportMap.get(month) != null) {
                pipeUseWaterReport = stringPipeUseWaterReportMap.get(month);
            } else {
                fields = pipeUseWaterReportClass.getFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    if (field.getType().equals(BigDecimal.class)) {
                        try {
                            field.set(pipeUseWaterReport, null);
                        } catch (IllegalAccessException e) {
                        }
                    }
                }
            }

            result.add(pipeUseWaterReport);
        }

        return result;
    }

    private void analysis(PipeUseWaterReportDTO pipeUseWaterReportDTO) {
        try {
            // 收益水量
            pipeUseWaterReportDTO.setIncomeWater(pipeUseWaterReportDTO.getFeeMeteringUseWater().add(pipeUseWaterReportDTO.getFeeNoMeteringUseWater()));
            // 未收益水量
            pipeUseWaterReportDTO.setNoIncomeWater(pipeUseWaterReportDTO.getFreeMeteringUseWater().add(pipeUseWaterReportDTO.getFreeNoMeteringUseWater()).add(pipeUseWaterReportDTO.getLossTotalWater()));
            // 漏失率 漏失水量 * 100 / 供水总量
            try {
                pipeUseWaterReportDTO.setLeakRate(pipeUseWaterReportDTO.getLeakTotalWater().multiply(BigDecimal.valueOf(100)).divide(pipeUseWaterReportDTO.getSupplyTotalWater()));
            } catch (Exception e) {
                pipeUseWaterReportDTO.setLeakRate(BigDecimal.ZERO);
            }
            // 综合漏损率 （供水总量-注册用户用水量）/ 供水总量 X 100%
            try {
                pipeUseWaterReportDTO.setLossRate((pipeUseWaterReportDTO.getSupplyTotalWater().subtract(pipeUseWaterReportDTO.getUseTotalWater())).multiply(BigDecimal.valueOf(100)).divide(pipeUseWaterReportDTO.getSupplyTotalWater(), 2, RoundingMode.HALF_UP));
            } catch (Exception e) {
                pipeUseWaterReportDTO.setLossRate(BigDecimal.ZERO);
            }
            // 产销差率
            try {
                pipeUseWaterReportDTO.setNrwRate((pipeUseWaterReportDTO.getSupplyTotalWater().subtract(pipeUseWaterReportDTO.getFeeMeteringUseWater()).subtract(pipeUseWaterReportDTO.getFeeNoMeteringUseWater())).multiply(BigDecimal.valueOf(100)).divide(pipeUseWaterReportDTO.getSupplyTotalWater(), 2, RoundingMode.HALF_UP));
            } catch (Exception e) {
                pipeUseWaterReportDTO.setNrwRate(BigDecimal.ZERO);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sum(PipeUseWaterReport pipeUseWaterReport) {
        PipeUseWaterReport temp = new PipeUseWaterReport();
        BeanUtils.copyProperties(pipeUseWaterReport, temp);
        // 检查是否有null
        Field[] declaredFields = temp.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            if (BigDecimal.class.equals(field.getClass())) {
                try {
                    if (field.get(temp) == null) {
                        field.set(BigDecimal.class, BigDecimal.ZERO);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }

            // 供水总量
            pipeUseWaterReport.setSupplyTotalWater(temp.getOwnSupplyWater().add(temp.getBuySupplyWater()).subtract(temp.getBatchSaleWater()));

            // 计费未计量用水量
            pipeUseWaterReport.setFeeNoMeteringUseWater(temp.getHuanweiUseWater().add(temp.getLvhuaUseWater().add(temp.getPipeUseWater().add(temp.getSunhuaiUseWater().add(temp.getDingliangUseWater())))));
            // 免费计量用水量
            pipeUseWaterReport.setFreeMeteringUseWater(temp.getBangongUseWater().add(temp.getXiaofangUseWater().add(temp.getJianmianUseWater())));
            // 免费未计量用水
            pipeUseWaterReport.setFreeNoMeteringUseWater(temp.getFreeXiaofangUseWater().add(temp.getFreeWeihuUseWater().add(temp.getFreeBangongUseWater().add(temp.getFreeChongxiUseWater()))));
            // 注册用户用水量
            pipeUseWaterReport.setUseTotalWater(temp.getFeeNoMeteringUseWater().add(temp.getFeeMeteringUseWater()).add(temp.getFreeMeteringUseWater()).add(temp.getFreeNoMeteringUseWater()));

            // 明漏水量
            pipeUseWaterReport.setFrontLeakTotalWater(temp.getFrontPipeLeakWater().add(temp.getFrontPipeLeakWater()));
            // 暗漏水量
            pipeUseWaterReport.setBackendLeakTotalWater(temp.getBackendCheckedLeakWater().add(temp.getBackendNoCheckedWater()));
            // 漏失水量
            pipeUseWaterReport.setLeakTotalWater(temp.getFrontLeakTotalWater().add(temp.getBackendLeakTotalWater()).add(temp.getBackgroundLeakWater()).add(temp.getShuixiangLeakWater()));
            // 计损漏失水量
            pipeUseWaterReport.setMistakeLossTotalWater(temp.getCustMistakeLossWater().add(temp.getNonCustMistakeLossWater()));
            // 未注册用户用水和用户拒查等
            pipeUseWaterReport.setNoRegisterLossWater(temp.getToudaoLossWater().add(temp.getCopyMeterLossWater()).add(temp.getOtherLossWater()));
            // 其他漏失水量
            pipeUseWaterReport.setOtherLossTotalWater(temp.getNoRegisterLossWater().add(temp.getPipeLossWater()));
            // 漏失水量
            pipeUseWaterReport.setLossTotalWater(temp.getLeakTotalWater().add(temp.getMistakeLossTotalWater()).add(temp.getOtherLossTotalWater()));
        }
    }

    public PipeUseWaterReport getByPartitionIdAndYm(String partitionId, String ym) {
        QueryWrapper<PipeUseWaterReport> pipeUseWaterReportQueryWrapper = new QueryWrapper<>();
        pipeUseWaterReportQueryWrapper.eq("partition_id", partitionId);
        pipeUseWaterReportQueryWrapper.eq("ym", ym);

        List<PipeUseWaterReport> pipeUseWaterReports = pipeUseWaterReportMapper.selectList(pipeUseWaterReportQueryWrapper);
        if (pipeUseWaterReports.size() == 0) {
            return null;
        }
        return pipeUseWaterReports.get(0);
    }
}