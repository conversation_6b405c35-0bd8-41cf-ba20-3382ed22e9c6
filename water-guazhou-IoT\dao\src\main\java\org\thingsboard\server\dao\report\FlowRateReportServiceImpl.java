package org.thingsboard.server.dao.report;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.sql.FlowRateReport;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.sql.report.FlowRateReportRepository;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FlowRateReportServiceImpl implements FlowRateReportService {

    @Autowired
    private FlowRateReportRepository flowRateReportRepository;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Override
    public Object getData(Date startTime, Date endTime, String stationId) {
        // 按开始时间和站点数据查询
        List<FlowRateReport> list = flowRateReportRepository.findByStationIdAndTimeBetweenOrderByTimeDesc(stationId, startTime, endTime);
        return list;
    }

    @Override
    public Object supplyByProject(Date start, Date end, String projectId, String stationType) {
        /*
         * 1. 查询要统计的项目下的所有水厂
         * 2. 查询所有水厂指定时间范围的流量数据
         * 3. 统计数据并返回
         */

        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 9999, stationType, projectId);
        if (stationPageData == null || stationPageData.getTotal() == 0) {
            return new ArrayList<>();
        }
        List<StationEntity> stationList = stationPageData.getData();
        List<String> stationIdList = stationList.stream().map(StationEntity::getId).collect(Collectors.toList());

        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdInAndTimeBetweenOrderByTimeDesc(stationIdList, start, end);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        // 对数据进行分组
        Map<Date, List<FlowRateReport>> flowRateListByDate = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            Date time = flowRateReport.getTime();
            List<FlowRateReport> list = new ArrayList<>();
            if (flowRateListByDate.containsKey(time)) {
                list = flowRateListByDate.get(time);
            }
            list.add(flowRateReport);

            flowRateListByDate.put(time, list);
        }

        // 对分组数据进行统计
        List<JSONObject> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (Map.Entry<Date, List<FlowRateReport>> entry : flowRateListByDate.entrySet()) {
            List<FlowRateReport> value = entry.getValue();
            Date key = entry.getKey();

            JSONObject row = new JSONObject();
            row.put("date", dateFormat.format(key));
            if (value != null && !value.isEmpty()) {
                // 总供水量
                BigDecimal totalSupply = new BigDecimal("0");
                // 总电量
                BigDecimal totalEnergyIn = new BigDecimal("0");
                for (FlowRateReport flowRateReport : value) {
                    String stationId = flowRateReport.getStationId();
                    row.put(stationId + "_totalFlow", flowRateReport.getLastTotalFlow());
                    if (flowRateReport.getTotalFlow() != null) {
                        totalSupply = totalSupply.add(flowRateReport.getTotalFlow());
                    }
                    if (flowRateReport.getEnergyIn() != null) {
                        totalEnergyIn = totalEnergyIn.add(flowRateReport.getEnergyIn());
                    }
                }

                // 计算吨水单耗
                BigDecimal unitConsumption = null;
                if (totalSupply.doubleValue() != 0) {
                    unitConsumption = totalEnergyIn.divide(totalSupply, 4, BigDecimal.ROUND_DOWN);
                }

                row.put("totalSupply", totalSupply);
                row.put("totalEnergyIn", totalEnergyIn);
                row.put("unitConsumption", unitConsumption);
            }

            dataList.add(row);
        }

        // 返回动态表头结构
        List<JSONObject> tableInfo = new ArrayList<>();
        JSONObject field = new JSONObject();
        field.put("label", "日期");
        field.put("value", "date");
        tableInfo.add(field);
        for (StationEntity station : stationList) {
            field = new JSONObject();
            field.put("label", "出水累计-" + station.getName());
            field.put("value", station.getId() + "_totalFlow");
            tableInfo.add(field);
        }
        field = new JSONObject();
        field.put("label", "总供水量(m³)");
        field.put("value", "totalSupply");
        tableInfo.add(field);

        field = new JSONObject();
        field.put("label", "总电量(kWh)");
        field.put("value", "totalSupply");
        tableInfo.add(field);

        field = new JSONObject();
        field.put("label", "吨水单耗(kWh/m³)");
        field.put("value", "unitConsumption");
        tableInfo.add(field);

        JSONObject result = new JSONObject();
        result.put("tableInfo", tableInfo);
        result.put("dataList", dataList);

        return result;
    }

    @Override
    public Object supplyByStation(Date startTime, Date endTime, String stationId) {
        /*
         * 1. 查询要统计的站点
         * 2. 查询站点指定时间范围的流量数据
         * 3. 统计数据并返回
         */
        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdAndTimeBetweenOrderByTimeDesc(stationId, startTime, endTime);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        // 对分组数据进行统计
        List<JSONObject> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (FlowRateReport flowRateReport : flowRateReportList) {

            JSONObject row = new JSONObject();
            row.put("date", dateFormat.format(flowRateReport.getTime()));
            // 总供水量
            BigDecimal totalSupply = flowRateReport.getTotalFlow();
            // 总电量
            BigDecimal totalEnergyIn = flowRateReport.getEnergyIn();
            // 计算吨水单耗
            BigDecimal unitConsumption = null;
            if (totalEnergyIn != null && totalSupply != null && totalSupply.doubleValue() != 0) {
                unitConsumption = totalEnergyIn.divide(totalSupply, 4, BigDecimal.ROUND_DOWN);
            }

            // 累计流量以及累计电量
            row.put("lastTotalFlow", flowRateReport.getLastTotalFlow());
            row.put("lastEnergyIn", flowRateReport.getLastEnergyIn());

            // 供水量、用电量、吨水电耗
            row.put("totalSupply", totalSupply);
            row.put("totalEnergyIn", totalEnergyIn);
            row.put("unitConsumption", unitConsumption);

            dataList.add(row);
        }


        return dataList;
    }

    @Override
    public Object findEnergyInByProject(Date startTime, Date endTime, String projectId, String stationType) {
        /*
         * 1. 查询要统计的项目下的所有水厂
         * 2. 查询所有水厂指定时间范围的流量数据
         * 3. 统计数据并返回
         */

        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 9999, stationType, projectId);
        if (stationPageData == null || stationPageData.getTotal() == 0) {
            return new ArrayList<>();
        }
        List<StationEntity> stationList = stationPageData.getData();
        List<String> stationIdList = stationList.stream().map(StationEntity::getId).collect(Collectors.toList());

        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdInAndTimeBetweenOrderByTimeDesc(stationIdList, startTime, endTime);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        // 对数据进行分组
        Map<Date, List<FlowRateReport>> flowRateListByDate = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            Date time = flowRateReport.getTime();
            List<FlowRateReport> list = new ArrayList<>();
            if (flowRateListByDate.containsKey(time)) {
                list = flowRateListByDate.get(time);
            }
            list.add(flowRateReport);

            flowRateListByDate.put(time, list);
        }

        // 对分组数据进行统计
        List<JSONObject> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (Map.Entry<Date, List<FlowRateReport>> entry : flowRateListByDate.entrySet()) {
            List<FlowRateReport> value = entry.getValue();
            Date key = entry.getKey();

            JSONObject row = new JSONObject();
            row.put("date", dateFormat.format(key));
            if (value != null && !value.isEmpty()) {
                for (FlowRateReport flowRateReport : value) {
                    String stationId = flowRateReport.getStationId();
                    row.put(stationId + "_energyIn", flowRateReport.getEnergyIn());
                }
            }

            dataList.add(row);
        }

        // 返回动态表头结构
        List<JSONObject> tableInfo = new ArrayList<>();
        JSONObject field = new JSONObject();
        field.put("label", "日期");
        field.put("value", "date");
        tableInfo.add(field);
        for (StationEntity station : stationList) {
            field = new JSONObject();
            field.put("label", "用电量-" + station.getName() + "(kWh)");
            field.put("value", station.getId() + "_energyIn");
            tableInfo.add(field);
        }

        JSONObject result = new JSONObject();
        result.put("tableInfo", tableInfo);
        result.put("dataList", dataList);

        return result;
    }

    @Override
    public Object findRuntimeList(String projectId, Date startTime, Date endTime) {
        /*
         * 1. 查询要统计的项目下的所有泵站
         * 2. 查询所有泵站指定时间范围的流量数据
         * 3. 统计数据并返回
         */
        String stationType = "泵站";

        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 9999, stationType, projectId);
        if (stationPageData == null || stationPageData.getTotal() == 0) {
            return new ArrayList<>();
        }
        List<StationEntity> stationList = stationPageData.getData();
        List<String> stationIdList = stationList.stream().map(StationEntity::getId).collect(Collectors.toList());

        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdInAndTimeBetweenOrderByTimeDesc(stationIdList, startTime, endTime);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        // 对站点数据进行分组
        Map<String, List<FlowRateReport>> stationDataMap = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            String stationId = flowRateReport.getStationId();
            List<FlowRateReport> list = new ArrayList<>();
            if (stationDataMap.containsKey(stationId)) {
                list = stationDataMap.get(stationId);
            }
            list.add(flowRateReport);

            stationDataMap.put(stationId, list);
        }

        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {
            String stationId = station.getId();
            String stationName = station.getName();

            JSONObject object = new JSONObject();
            object.put("stationId", stationId);
            object.put("stationName", stationName);
            List<FlowRateReport> dataList = stationDataMap.get(stationId);
            if (dataList != null && dataList.size() > 0) {
                BigDecimal totalRuntime = new BigDecimal("0");
                BigDecimal totalFlow = new BigDecimal("0");
                BigDecimal totalEnergyIn = new BigDecimal("0");
                for (FlowRateReport flowRateReport : dataList) {
                    if (flowRateReport.getTotalRunTime() != null) {
                        totalRuntime = totalRuntime.add(flowRateReport.getTotalRunTime());
                    }
                    if (flowRateReport.getTotalFlow() != null) {
                        totalFlow = totalFlow.add(flowRateReport.getTotalFlow());
                    }
                    if (flowRateReport.getEnergyIn() != null) {
                        totalEnergyIn = totalEnergyIn.add(flowRateReport.getEnergyIn());
                    }
                }

                // 计算吨水单耗
                BigDecimal unitConsumption = null;
                if (totalFlow.doubleValue() != 0) {
                    unitConsumption = totalEnergyIn.divide(totalFlow, 4, BigDecimal.ROUND_DOWN);
                }

                object.put("totalRuntime", totalRuntime);
                object.put("totalFlow", totalFlow);
                object.put("totalEnergyIn", totalEnergyIn);
                object.put("unitConsumption", unitConsumption);
            }

            result.add(object);
        }

        return result;
    }

    @Override
    public Object findRuntimeListByStation(String stationId, Date startTime, Date endTime) {
        /*
         * 1. 查询要统计的泵站
         * 2. 查询所有泵站指定时间范围的流量数据
         * 3. 计算数据并返回
         */
        StationEntity station = stationFeignClient.get(stationId);

        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdAndTimeBetweenOrderByTimeDesc(stationId, startTime, endTime);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        for (FlowRateReport flowRateReport : flowRateReportList) {
            String stationName = station.getName();
            flowRateReport.setStationName(stationName);
            BigDecimal totalRunTime = flowRateReport.getTotalRunTime();
            BigDecimal totalStopTime = flowRateReport.getTotalStopTime();
            if (totalRunTime != null && totalStopTime != null) {
                BigDecimal hours = new BigDecimal("24");
                flowRateReport.setCloseTime(hours.subtract(totalRunTime).subtract(totalStopTime));
            }
        }

        return flowRateReportList;
    }

    @Override
    public Object findRuntimeReportByStationList(List<String> stationIdList, Date startTime, Date endTime) {
        /*
         * 1. 查询要统计的泵站
         * 2. 查询所有泵站指定时间范围的流量数据
         * 3. 统计数据并返回
         */
        String stationType = "泵站";

        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);
        if (stationList == null || stationList.size() == 0) {
            return new ArrayList<>();
        }

        stationIdList = stationList.stream().map(StationEntity::getId).collect(Collectors.toList());

        String stationNames = stationList.stream().map(StationEntity::getName).reduce((name1, name2) -> name1 + "," + name2).get();

        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdInAndTimeBetweenOrderByTimeDesc(stationIdList, startTime, endTime);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        // 对站点数据进行分组
        Map<String, List<FlowRateReport>> stationDataMap = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            String stationId = flowRateReport.getStationId();
            List<FlowRateReport> list = new ArrayList<>();
            if (stationDataMap.containsKey(stationId)) {
                list = stationDataMap.get(stationId);
            }
            list.add(flowRateReport);

            stationDataMap.put(stationId, list);
        }

        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {
            String stationId = station.getId();
            String stationName = station.getName();

            JSONObject object = new JSONObject();
            object.put("stationId", stationId);
            object.put("stationNames", stationName);
            List<FlowRateReport> dataList = stationDataMap.get(stationId);
            if (dataList != null && dataList.size() > 0) {
                // 累计流量总值
                BigDecimal totalFlow = new BigDecimal("0");
                // 累计电量总值
                BigDecimal totalEnergyIn = new BigDecimal("0");
                // 运行时间总值
                BigDecimal totalRuntime = new BigDecimal("0");
                // 供水量总值、用电量总值
                BigDecimal totalWaterSupply = new BigDecimal("0");
                BigDecimal totalEnergySupply = new BigDecimal("0");
                // 总平均流量
                BigDecimal totalFlowRateAvg = new BigDecimal("0");
                for (FlowRateReport flowRate : dataList) {
                    if (flowRate.getLastTotalFlow() != null) {
                        totalFlow = totalFlow.add(flowRate.getLastTotalFlow());
                    }
                    if (flowRate.getLastEnergyIn() != null) {
                        totalEnergyIn = totalEnergyIn.add(flowRate.getLastEnergyIn());
                    }
                    if (flowRate.getTotalRunTime() != null) {
                        totalRuntime = totalRuntime.add(flowRate.getTotalRunTime());
                    }
                    if (flowRate.getTotalFlow() != null) {
                        totalWaterSupply = totalWaterSupply.add(flowRate.getTotalFlow());
                    }
                    if (flowRate.getEnergyIn() != null) {
                        totalEnergySupply = totalWaterSupply.add(flowRate.getEnergyIn());
                    }
                    if (flowRate.getFlowRateAvg() != null) {
                        totalFlowRateAvg = totalFlowRateAvg.add(flowRate.getFlowRateAvg());
                    }
                }

                // 计算平均流量
                BigDecimal flowRateAvg = null;
                if (dataList.size() != 0) {
                    flowRateAvg = totalFlowRateAvg.divide(BigDecimal.valueOf(dataList.size()), 2, BigDecimal.ROUND_DOWN);
                }
                // 计算吨水电耗
                BigDecimal unitConsumption = null;
                if (totalWaterSupply.doubleValue() != 0) {
                    unitConsumption = totalEnergyIn.divide(totalWaterSupply, 4, BigDecimal.ROUND_DOWN);
                }

                object.put("totalFlow", totalFlow);
                object.put("totalEnergyIn", totalEnergyIn);
                object.put("totalRuntime", totalRuntime);
                object.put("totalWaterSupply", totalWaterSupply);
                object.put("totalEnergySupply", totalEnergySupply);
                object.put("flowRateAvg", flowRateAvg);
                object.put("unitConsumption", unitConsumption);
            }

            result.add(object);
        }
        return result;
        /*// 按时间分组
        Map<Date, List<FlowRateReport>> flowRateListByDate = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            Date time = flowRateReport.getTime();
            List<FlowRateReport> list = new ArrayList<>();
            if (flowRateListByDate.containsKey(time)) {
                list = flowRateListByDate.get(time);
            }
            list.add(flowRateReport);

            flowRateListByDate.put(time, list);
        }

        // 分组统计
        List<JSONObject> resultList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (Map.Entry<Date, List<FlowRateReport>> entry : flowRateListByDate.entrySet()) {
            Date key = entry.getKey();
            List<FlowRateReport> value = entry.getValue();

            JSONObject obj = new JSONObject();
            obj.put("stationNames", stationNames);
            obj.put("date", dateFormat.format(key));

            if (value != null && value.size() > 0) {
                // 累计流量总值
                BigDecimal totalFlow = new BigDecimal("0");
                // 累计电量总值
                BigDecimal totalEnergyIn = new BigDecimal("0");
                // 运行时间总值
                BigDecimal totalRuntime = new BigDecimal("0");
                // 供水量总值、用电量总值
                BigDecimal totalWaterSupply = new BigDecimal("0");
                BigDecimal totalEnergySupply = new BigDecimal("0");
                // 总平均流量
                BigDecimal totalFlowRateAvg = new BigDecimal("0");
                for (FlowRateReport flowRate : value) {
                    totalFlow = totalFlow.add(flowRate.getLastTotalFlow());
                    totalEnergyIn = totalEnergyIn.add(flowRate.getLastEnergyIn());
                    totalRuntime = totalRuntime.add(flowRate.getTotalRunTime());
                    totalWaterSupply = totalWaterSupply.add(flowRate.getTotalFlow());
                    totalEnergySupply = totalWaterSupply.add(flowRate.getEnergyIn());
                    totalFlowRateAvg = totalFlowRateAvg.add(flowRate.getFlowRateAvg());
                }

                // 计算平均流量
                BigDecimal flowRateAvg = totalFlowRateAvg.divide(BigDecimal.valueOf(value.size()), 2, BigDecimal.ROUND_DOWN);
                // 计算吨水电耗
                BigDecimal unitConsumption = totalEnergyIn.divide(totalWaterSupply, 4, BigDecimal.ROUND_DOWN);

                obj.put("totalFlow", totalFlow);
                obj.put("totalEnergyIn", totalEnergyIn);
                obj.put("totalRuntime", totalRuntime);
                obj.put("totalWaterSupply", totalWaterSupply);
                obj.put("totalEnergySupply", totalEnergySupply);
                obj.put("flowRateAvg", flowRateAvg);
                obj.put("unitConsumption", unitConsumption);

                resultList.add(obj);
            }
        }*/
    }

    @Override
    public void save(FlowRateReport entity) {
        flowRateReportRepository.save(entity);
    }

    @Override
    public Object findRuntimeReportByStation(String stationId, Date startTime, Date endTime) {
        /*
         * 1. 查询要统计的泵站
         * 2. 查询所有泵站指定时间范围的流量数据
         * 3. 统计数据并返回
         */
        String stationType = "泵站";

        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }

        // 查询站点的流量数据
        List<FlowRateReport> flowRateReportList = flowRateReportRepository.findByStationIdAndTimeBetweenOrderByTimeDesc(stationId, startTime, endTime);
        if (flowRateReportList == null || flowRateReportList.isEmpty()) {
            return new ArrayList<>();
        }

        // 对站点数据进行分组
        Map<String, List<FlowRateReport>> stationDataMap = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            List<FlowRateReport> list = new ArrayList<>();
            if (stationDataMap.containsKey(stationId)) {
                list = stationDataMap.get(stationId);
            }
            list.add(flowRateReport);

            stationDataMap.put(stationId, list);
        }

        // 按时间分组
        Map<Date, List<FlowRateReport>> flowRateListByDate = new LinkedHashMap<>();
        for (FlowRateReport flowRateReport : flowRateReportList) {
            Date time = flowRateReport.getTime();
            List<FlowRateReport> list = new ArrayList<>();
            if (flowRateListByDate.containsKey(time)) {
                list = flowRateListByDate.get(time);
            }
            list.add(flowRateReport);

            flowRateListByDate.put(time, list);
        }

        // 分组统计
        List<JSONObject> resultList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (Map.Entry<Date, List<FlowRateReport>> entry : flowRateListByDate.entrySet()) {
            Date key = entry.getKey();
            List<FlowRateReport> value = entry.getValue();

            JSONObject obj = new JSONObject();
            obj.put("stationNames", station.getName());
            obj.put("date", dateFormat.format(key));

            if (value != null && value.size() > 0) {
                // 累计流量总值
                BigDecimal totalFlow = new BigDecimal("0");
                // 累计电量总值
                BigDecimal totalEnergyIn = new BigDecimal("0");
                // 运行时间总值
                BigDecimal totalRuntime = new BigDecimal("0");
                // 供水量总值、用电量总值
                BigDecimal totalWaterSupply = new BigDecimal("0");
                BigDecimal totalEnergySupply = new BigDecimal("0");
                // 总平均流量
                BigDecimal totalFlowRateAvg = new BigDecimal("0");
                for (FlowRateReport flowRate : value) {
                    totalFlow = totalFlow.add(flowRate.getLastTotalFlow());
                    totalEnergyIn = totalEnergyIn.add(flowRate.getLastEnergyIn());
                    totalRuntime = totalRuntime.add(flowRate.getTotalRunTime());
                    totalWaterSupply = totalWaterSupply.add(flowRate.getTotalFlow());
                    totalEnergySupply = totalWaterSupply.add(flowRate.getEnergyIn());
                    totalFlowRateAvg = totalFlowRateAvg.add(flowRate.getFlowRateAvg());
                }

                // 计算平均流量
                BigDecimal flowRateAvg = totalFlowRateAvg.divide(BigDecimal.valueOf(value.size()), 2, BigDecimal.ROUND_DOWN);
                // 计算吨水电耗
                BigDecimal unitConsumption = null;
                if (totalWaterSupply.doubleValue() != 0) {
                    unitConsumption = totalEnergyIn.divide(totalWaterSupply, 4, BigDecimal.ROUND_DOWN);
                }

                obj.put("totalFlow", totalFlow);
                obj.put("totalEnergyIn", totalEnergyIn);
                obj.put("totalRuntime", totalRuntime);
                obj.put("totalWaterSupply", totalWaterSupply);
                obj.put("totalEnergySupply", totalEnergySupply);
                obj.put("flowRateAvg", flowRateAvg);
                obj.put("unitConsumption", unitConsumption);

                resultList.add(obj);
            }
        }

        return resultList;
    }
}
