package org.thingsboard.server.controller.video;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.VideoGroupEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.video.VideoGroupService;

@RestController
@RequestMapping("/api/video/group")
public class VideoGroupController extends BaseController {

    @Autowired
    private VideoGroupService videoGroupService;

    @PostMapping(value = "save")
    public IstarResponse save(@RequestBody VideoGroupEntity videoGroupEntity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        videoGroupEntity.setTenantId(tenantId);
        return videoGroupService.save(videoGroupEntity);
    }

    @GetMapping("tree/{projectId}")
    public IstarResponse getTreeByProjectId(@PathVariable String projectId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(videoGroupService.getTreeByProjectId(projectId, tenantId));
    }

    @DeleteMapping(value = "delete/{id}")
    public IstarResponse delete(@PathVariable("id") String id) {
        return videoGroupService.delete(id);
    }

}
