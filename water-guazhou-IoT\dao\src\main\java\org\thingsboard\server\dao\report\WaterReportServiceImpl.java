package org.thingsboard.server.dao.report;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.DTO.WaterReportDTO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.WaterReport;
import org.thingsboard.server.dao.sql.report.WaterReportRepository;
import org.thingsboard.server.dao.stationData.StationDataService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WaterReportServiceImpl implements WaterReportService {

    @Autowired
    private WaterReportRepository waterReportRepository;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Override
    public List<WaterReportDTO> findMonthReport(String month, String stationId, TenantId tenantId) throws ThingsboardException {
        Date monthStart = DateUtils.str2Date(month, "yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(monthStart);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);

        Date monthEnd = instance.getTime();

        List<WaterReport> waterReportList = waterReportRepository.findByTimeBetweenOrderByTimeAsc(monthStart, monthEnd);

        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return new ArrayList<>();
        }

        // 数据分组
        List<WaterReport> inList = waterReportList.stream().filter(waterReport -> "1".equals(waterReport.getType())).collect(Collectors.toList());
        List<WaterReport> outList = waterReportList.stream().filter(waterReport -> "2".equals(waterReport.getType())).collect(Collectors.toList());

        // 数据初始化
        Map<String, WaterReportDTO> dtoMap = initList(inList, outList, month);
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.TP.getValue());
        attrList.add(DataConstants.DeviceAttrType.TN.getValue());
        attrList.add(DataConstants.DeviceAttrType.PH.getValue());
        attrList.add(DataConstants.DeviceAttrType.COD.getValue());
        attrList.add(DataConstants.DeviceAttrType.BOD.getValue());
        attrList.add(DataConstants.DeviceAttrType.NH3.getValue());
        // 查询站点数据
        DynamicTableVO inData = stationDataService.stationDataQuery(
                stationId, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, "进水口", attrList, tenantId);
        DynamicTableVO outData = stationDataService.stationDataQuery(
                stationId, monthStart.getTime(), monthEnd.getTime(), DateUtils.DAY, "出水口", attrList, tenantId);

        List<WaterReportDTO> resultList = new ArrayList<>();

        // 统计数据
        WaterReportDTO avgData = new WaterReportDTO();
        avgData.setTimeStr("平均值");
        WaterReportDTO minData = new WaterReportDTO();
        minData.setTimeStr("最低");
        WaterReportDTO maxData = new WaterReportDTO();
        maxData.setTimeStr("最高");
        WaterReportDTO totalData = new WaterReportDTO();
        totalData.setTimeStr("合计");

        for (Map.Entry<String, WaterReportDTO> entry : dtoMap.entrySet()) {
            WaterReportDTO dto = entry.getValue();
            if (dto.getStationId() == null) {
                continue;
            }
            // codIn
            if (minData.getCodIn() == null || dto.getCodIn().compareTo(minData.getCodIn()) < 0) {
                minData.setCodIn(dto.getCodIn());
            }
            if (maxData.getCodIn() == null || dto.getCodIn().compareTo(maxData.getCodIn()) > 0) {
                maxData.setCodIn(dto.getCodIn());
            }
            if (totalData.getCodIn() == null ) {
                totalData.setCodIn(dto.getCodIn());
            } else {
                if (dto.getCodIn() != null) {
                    totalData.setCodIn(totalData.getCodIn().add(dto.getCodIn()));
                }
            }

            // codOut
            if (minData.getCodOut() == null || dto.getCodOut().compareTo(minData.getCodOut()) < 0) {
                minData.setCodOut(dto.getCodOut());
            }
            if (maxData.getCodOut() == null || dto.getCodOut().compareTo(maxData.getCodOut()) > 0) {
                maxData.setCodOut(dto.getCodOut());
            }
            if (totalData.getCodOut() == null ) {
                totalData.setCodOut(dto.getCodOut());
            } else {
                if (dto.getCodOut() != null) {
                    totalData.setCodOut(totalData.getCodOut().add(dto.getCodOut()));
                }
            }

            // bodIn
            if (minData.getBodIn() == null || dto.getBodIn().compareTo(minData.getBodIn()) < 0) {
                minData.setBodIn(dto.getBodIn());
            }
            if (maxData.getBodIn() == null || dto.getBodIn().compareTo(maxData.getBodIn()) > 0) {
                maxData.setBodIn(dto.getBodIn());
            }
            if (totalData.getBodIn() == null ) {
                totalData.setBodIn(dto.getBodIn());
            } else {
                if (dto.getBodIn() != null) {
                    totalData.setBodIn(totalData.getBodIn().add(dto.getBodIn()));
                }
            }

            // bodOut
            if (minData.getBodOut() == null || dto.getBodOut().compareTo(minData.getBodOut()) < 0) {
                minData.setBodOut(dto.getBodOut());
            }
            if (maxData.getBodOut() == null || dto.getBodOut().compareTo(maxData.getBodOut()) > 0) {
                maxData.setBodOut(dto.getBodOut());
            }
            if (totalData.getBodOut() == null ) {
                totalData.setBodOut(dto.getBodOut());
            } else {
                if (dto.getBodOut() != null) {
                    totalData.setBodOut(totalData.getBodOut().add(dto.getBodOut()));
                }
            }

            // phIn
            if (minData.getPhIn() == null || dto.getPhIn().compareTo(minData.getPhIn()) < 0) {
                minData.setPhIn(dto.getPhIn());
            }
            if (maxData.getPhIn() == null || dto.getPhIn().compareTo(maxData.getPhIn()) > 0) {
                maxData.setPhIn(dto.getPhIn());
            }
            if (totalData.getPhIn() == null ) {
                totalData.setPhIn(dto.getPhIn());
            } else {
                if (dto.getPhIn() != null) {
                    totalData.setPhIn(totalData.getPhIn().add(dto.getPhIn()));
                }
            }

            // phOut
            if (minData.getPhOut() == null || dto.getPhOut().compareTo(minData.getPhOut()) < 0) {
                minData.setPhOut(dto.getPhOut());
            }
            if (maxData.getPhOut() == null || dto.getPhOut().compareTo(maxData.getPhOut()) > 0) {
                maxData.setPhOut(dto.getPhOut());
            }
            if (totalData.getPhOut() == null ) {
                totalData.setPhOut(dto.getPhOut());
            } else {
                if (dto.getPhOut() != null) {
                    totalData.setPhOut(totalData.getPhOut().add(dto.getPhOut()));
                }
            }

            // tpIn
            if (minData.getTpIn() == null || dto.getTpIn().compareTo(minData.getTpIn()) < 0) {
                minData.setTpIn(dto.getTpIn());
            }
            if (maxData.getTpIn() == null || dto.getTpIn().compareTo(maxData.getTpIn()) > 0) {
                maxData.setTpIn(dto.getTpIn());
            }
            if (totalData.getTpIn() == null ) {
                totalData.setTpIn(dto.getTpIn());
            } else {
                if (dto.getTpIn() != null) {
                    totalData.setTpIn(totalData.getTpIn().add(dto.getTpIn()));
                }
            }

            // tpOut
            if (minData.getTpOut() == null || dto.getTpOut().compareTo(minData.getTpOut()) < 0) {
                minData.setTpOut(dto.getTpOut());
            }
            if (maxData.getTpOut() == null || dto.getTpOut().compareTo(maxData.getTpOut()) > 0) {
                maxData.setTpOut(dto.getTpOut());
            }
            if (totalData.getTpOut() == null ) {
                totalData.setTpOut(dto.getTpOut());
            } else {
                if (dto.getTpOut() != null) {
                    totalData.setTpOut(totalData.getTpOut().add(dto.getTpOut()));
                }
            }

            // tnIn
            if (minData.getTnIn() == null || dto.getTnIn().compareTo(minData.getTnIn()) < 0) {
                minData.setTpIn(dto.getTnIn());
            }
            if (maxData.getTnIn() == null || dto.getTnIn().compareTo(maxData.getTnIn()) > 0) {
                maxData.setTpIn(dto.getTnIn());
            }
            if (totalData.getTnIn() == null ) {
                totalData.setTpIn(dto.getTnIn());
            } else {
                if (dto.getTnIn() != null) {
                    totalData.setTpIn(totalData.getTnIn().add(dto.getTnIn()));
                }
            }

            // tnOut
            if (minData.getTnOut() == null || dto.getTnOut().compareTo(minData.getTnOut()) < 0) {
                minData.setTnOut(dto.getTnOut());
            }
            if (maxData.getTnOut() == null || dto.getTnOut().compareTo(maxData.getTnOut()) > 0) {
                maxData.setTnOut(dto.getTnOut());
            }
            if (totalData.getTnOut() == null ) {
                totalData.setTnOut(dto.getTnOut());
            } else {
                if (dto.getTnOut() != null) {
                    totalData.setTnOut(totalData.getTnOut().add(dto.getTnOut()));
                }
            }

            // nh3In
            if (minData.getNh3In() == null || dto.getNh3In().compareTo(minData.getNh3In()) < 0) {
                minData.setNh3In(dto.getNh3In());
            }
            if (maxData.getNh3In() == null || dto.getNh3In().compareTo(maxData.getNh3In()) > 0) {
                maxData.setNh3In(dto.getNh3In());
            }
            if (totalData.getNh3In() == null ) {
                totalData.setNh3In(dto.getNh3In());
            } else {
                if (dto.getNh3In() != null) {
                    totalData.setNh3In(totalData.getNh3In().add(dto.getNh3In()));
                }
            }

            // nh3Out
            if (minData.getNh3Out() == null || dto.getNh3Out().compareTo(minData.getNh3Out()) < 0) {
                minData.setNh3Out(dto.getNh3Out());
            }
            if (maxData.getNh3Out() == null || dto.getNh3Out().compareTo(maxData.getNh3Out()) > 0) {
                maxData.setNh3Out(dto.getNh3Out());
            }
            if (totalData.getNh3Out() == null ) {
                totalData.setNh3Out(dto.getNh3Out());
            } else {
                if (dto.getNh3Out() != null) {
                    totalData.setNh3Out(totalData.getNh3Out().add(dto.getNh3Out()));
                }
            }

            resultList.add(dto);
        }
        // 计算进水口平均值
        int inSize = inList.size();
        if (totalData.getCodIn() != null && inSize != 0) {
            avgData.setCodIn(totalData.getCodIn().divide(BigDecimal.valueOf(inSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getBodIn() != null && inSize != 0) {
            avgData.setBodIn(totalData.getBodIn().divide(BigDecimal.valueOf(inSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getNh3In() != null && inSize != 0) {
            avgData.setNh3In(totalData.getNh3In().divide(BigDecimal.valueOf(inSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getTpIn() != null && inSize != 0) {
            avgData.setTpIn(totalData.getTpIn().divide(BigDecimal.valueOf(inSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getTnIn() != null && inSize != 0) {
            avgData.setTnIn(totalData.getTnIn().divide(BigDecimal.valueOf(inSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getPhIn() != null && inSize != 0) {
            avgData.setPhIn(totalData.getPhIn().divide(BigDecimal.valueOf(inSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        // 计算出水口平均值
        int outSize = inList.size();
        if (totalData.getCodOut() != null && outSize != 0) {
            avgData.setCodOut(totalData.getCodOut().divide(BigDecimal.valueOf(outSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getBodOut() != null && outSize != 0) {
            avgData.setBodOut(totalData.getBodOut().divide(BigDecimal.valueOf(outSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getNh3Out() != null && outSize != 0) {
            avgData.setNh3Out(totalData.getNh3Out().divide(BigDecimal.valueOf(outSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getTpOut() != null && outSize != 0) {
            avgData.setTpOut(totalData.getTpOut().divide(BigDecimal.valueOf(outSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getTnOut() != null && outSize != 0) {
            avgData.setTnOut(totalData.getTnOut().divide(BigDecimal.valueOf(outSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }
        if (totalData.getPhOut() != null && outSize != 0) {
            avgData.setPhOut(totalData.getPhOut().divide(BigDecimal.valueOf(outSize), 2, BigDecimal.ROUND_HALF_EVEN));
        }

        resultList.add(minData);
        resultList.add(maxData);
        resultList.add(avgData);
        resultList.add(totalData);

        for (WaterReportDTO dto : resultList) {
            dto.setStationName(station.getName());
        }

        return resultList;
    }

    /**
     * 初始化数据
     *
     * @param inList
     * @param outList
     * @param month
     * @return
     */
    private Map<String, WaterReportDTO> initList(List<WaterReport> inList, List<WaterReport> outList, String month) {
        Date monthStart = DateUtils.str2Date(month, "yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(monthStart);
        int dayMax = instance.getActualMaximum(Calendar.DAY_OF_MONTH);

        Map<String, WaterReportDTO> resultMap = new LinkedHashMap<>();
        for (int i = 0; i < dayMax; i++) {
            WaterReportDTO waterReportDTO = new WaterReportDTO();
            String timeStr = month + "-" + String.format("%02d", i + 1);
            waterReportDTO.setTimeStr(timeStr);

            resultMap.put(timeStr, waterReportDTO);
        }

        for (WaterReport in : inList) {
            Date time = in.getTime();
            String timeStr = DateUtils.date2Str(time, "yyyy-MM-dd");
            WaterReportDTO dto = resultMap.get(timeStr);
            dto.setStationId(in.getStationId());
            dto.setStationName(in.getStationName());
            dto.setCodIn(in.getCod());
            dto.setBodIn(in.getBod());
            dto.setPhIn(in.getPh());
            dto.setTnIn(in.getTn());
            dto.setTpIn(in.getTp());
            dto.setNh3In(in.getNh3());

            resultMap.put(timeStr, dto);
        }

        for (WaterReport out : outList) {
            Date time = out.getTime();
            String timeStr = DateUtils.date2Str(time, "yyyy-MM-dd");
            WaterReportDTO dto = resultMap.get(timeStr);
            dto.setStationId(out.getStationId());
            dto.setStationName(out.getStationName());
            dto.setCodOut(out.getCod());
            dto.setBodOut(out.getBod());
            dto.setPhOut(out.getPh());
            dto.setTnOut(out.getTn());
            dto.setTpOut(out.getTp());
            dto.setNh3Out(out.getNh3());

            resultMap.put(timeStr, dto);
        }

        return resultMap;
    }
}
