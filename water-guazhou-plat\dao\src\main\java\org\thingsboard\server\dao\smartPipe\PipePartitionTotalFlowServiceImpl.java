package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.PartitionFlowAnalysisDTO;
import org.thingsboard.server.dao.model.DTO.PartitionSupplyCorrectRecordsDTO;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.request.PartitionTotalFlowRequest;
import org.thingsboard.server.dao.model.sql.DeviceEntity;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataCorrectRecords;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow;
import org.thingsboard.server.dao.sql.device.DeviceRepository;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMapper;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMountMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipeCopyDataCorrectRecordsMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipePartitionTotalFlowMapper;
import org.thingsboard.server.dao.util.InfluxUtil;
import org.thingsboard.server.dao.util.imodel.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class PipePartitionTotalFlowServiceImpl implements PipePartitionTotalFlowService {

    @Autowired
    private PipePartitionTotalFlowMapper pipePartitionTotalFlowMapper;

    @Autowired
    private PipeCopyDataCorrectRecordsMapper pipeCopyDataCorrectRecordsMapper;

    @Autowired
    private InfluxUtil influxUtil;

    @Autowired
    private PartitionMapper partitionMapper;

    @Autowired
    private PartitionMountMapper partitionMountMapper;

    @Autowired
    private DeviceRepository deviceRepository;

    @Override
    public PageData<PipePartitionTotalFlow> getList(PartitionTotalFlowRequest request) {
        Page<PipePartitionTotalFlow> page = new Page<>(request.getPage(), request.getSize());
        if ("3".equals(request.getType()) && !StringUtils.isNullOrEmpty(request.getMonth())) {

            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate now = LocalDate.parse(request.getMonth() + "-01", format);
            request.setStart(now.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
            request.setEnd(now.plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        IPage<PipePartitionTotalFlow> list = pipePartitionTotalFlowMapper.getList(page, request);
        return new PageData<>(list.getTotal(), list.getRecords());
    }

    @Override
    public void correct(PipePartitionTotalFlow pipePartitionTotalFlow, String userId) {
        BigDecimal correctWater = pipePartitionTotalFlow.getCorrectWater();
        pipePartitionTotalFlow = pipePartitionTotalFlowMapper.selectById(pipePartitionTotalFlow.getId());
        if (pipePartitionTotalFlow == null) {
            return;
        }
        pipePartitionTotalFlow.setCorrectWater(correctWater);
        // 添加修改记录
        PipeCopyDataCorrectRecords pipeCopyDataCorrectRecords = new PipeCopyDataCorrectRecords();

        pipeCopyDataCorrectRecords.setType("2");
        pipeCopyDataCorrectRecords.setCopyId(pipePartitionTotalFlow.getId());
        pipeCopyDataCorrectRecords.setCorrectWater(correctWater);
        pipeCopyDataCorrectRecords.setOldValue(pipePartitionTotalFlow.getOriginWater());
        pipeCopyDataCorrectRecords.setNewValue(correctWater);

        pipeCopyDataCorrectRecords.setCreator(userId);
        pipeCopyDataCorrectRecords.setCreateTime(new Date());
        pipeCopyDataCorrectRecords.setTenantId(pipePartitionTotalFlow.getTenantId());
        pipeCopyDataCorrectRecordsMapper.insert(pipeCopyDataCorrectRecords);

        // 抄表记录修改
        pipePartitionTotalFlow.setUpdateUser(userId);
        pipePartitionTotalFlowMapper.updateById(pipePartitionTotalFlow);
    }

    @Override
    public PageData<PartitionSupplyCorrectRecordsDTO> getCorrectRecords(PartitionCustRequest partitionCustRequest) {
        Page<PartitionSupplyCorrectRecordsDTO> custPage = new Page<>(partitionCustRequest.getPage(), partitionCustRequest.getSize());
        IPage<PartitionSupplyCorrectRecordsDTO> pipePartitionCustIPage = pipeCopyDataCorrectRecordsMapper.getSupplyTotalRecords(custPage, partitionCustRequest);
        return new PageData<>(pipePartitionCustIPage.getTotal(), pipePartitionCustIPage.getRecords());
    }

    @Override
    public PageData<PartitionFlowAnalysisDTO> getFlowAnalysis(PartitionTotalFlowRequest request) {
        List<PartitionFlowAnalysisDTO> result = new ArrayList<>();
        // 设备名
        // 是否有分区
        Set<String> allDeviceIdList = new LinkedHashSet<>(request.getDeviceIdList());
        Set<String> deviceIdList = request.getDeviceIdList();
        List<Partition> partitionList = partitionMapper.selectBatchIds(deviceIdList);
        // 分区名
        Map<String, String> partitionIdNameMap = new HashMap<>();
        for (Partition partition : partitionList) {
            partitionIdNameMap.put(partition.getId(), partition.getName());
        }
        // 分区下的设备
        Map<String, String> deviceIdNameMap = new HashMap<>();
        Map<String, List<String>> partitionDeviceIdListMap = new HashMap<>();
        if (partitionList.size() > 0) {
            List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
            List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "", request.getTenantId());
            for (String partitionId : partitionIdList) {
                partitionDeviceIdListMap.put(partitionId, new ArrayList<>());
            }
            for (PartitionMount partitionMount : partitionMountList) {
                partitionDeviceIdListMap.get(partitionMount.getPartitionId()).add(partitionMount.getDeviceId());
                deviceIdList.add(partitionMount.getDeviceId());
            }

            // 从set里面移除分区id
            deviceIdList.removeAll(partitionIdList);
        }
        if (deviceIdList.size() == 0) {
            return new PageData<>();
        }
        List<DeviceEntity> deviceEntityList = deviceRepository.findAllByIdIn(deviceIdList);
        for (DeviceEntity device : deviceEntityList) {
            deviceIdNameMap.put(UUIDConverter.fromTimeUUID(device.getId()), device.getName());
        }

        // 分区查该分区的所有设备
        String interval = request.getInterval();
        String type = request.getType();
        Long start = 0L;
        Long end = 0L;
        switch (type) {
            case "time":
                start = request.getStart() - 1L;
                end = request.getEnd();
                interval = request.getInterval();
                break;
            case "month":
                interval = "1d";
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                start = LocalDate.parse(request.getMonth() + "-01", formatter).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
                end = LocalDate.parse(request.getMonth() + "-01").plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                break;

            case "year":
                interval = "1nc";
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                start = LocalDate.parse(request.getYear() + "-01-01", formatter).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
                end = LocalDate.parse(request.getYear() + "-01-01").plusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        }
        String queryType = ".Instantaneous_flow";
        switch (request.getQueryType()) {
            case "totalFlow":
                queryType = ".total_flow";
                break;
            case "pressure":
                queryType = ".pressure";
        }
        String finalQueryType = queryType;
        List<String> paramDeviceIdList = deviceIdList.stream().map(a -> a + finalQueryType).collect(Collectors.toList());
        JSONObject data = influxUtil.getData(paramDeviceIdList, start, end, interval);
        List<String> timeList = data.keySet().stream().sorted().skip((request.getPage() - 1) * request.getSize()).limit(request.getSize()).collect(Collectors.toList());
        PartitionFlowAnalysisDTO partitionFlowAnalysisDTO;
        JSONObject jsonObject;
        List<String> subDeviceList;
        BigDecimal totalValue;
        int size;
        for (String time : timeList) {
            totalValue = BigDecimal.ZERO;
            size = 0;
            partitionFlowAnalysisDTO = new PartitionFlowAnalysisDTO();
            partitionFlowAnalysisDTO.setTime(time);
            for (String deviceId : allDeviceIdList) {
                jsonObject = new JSONObject();
                jsonObject.put("value", BigDecimal.ZERO);
                // 看是不是分区 分区把分区下的所有设备相加
                if (partitionIdNameMap.get(deviceId) != null) {
                    jsonObject.put("name", partitionIdNameMap.get(deviceId));
                    if (partitionDeviceIdListMap.get(deviceId).size() > 0) {
                        subDeviceList = partitionDeviceIdListMap.get(deviceId);
                        for (String subDeviceId : subDeviceList) {
                            if (data.getJSONObject(time).get(subDeviceId + finalQueryType) != null) {
                                jsonObject.put("value", jsonObject.getBigDecimal("value").add(data.getJSONObject(time).getBigDecimal(subDeviceId + finalQueryType)));
                            }
                        }

                    }
                } else {// 设备
                    jsonObject.put("name", deviceIdNameMap.get(deviceId));
                    if (data.getJSONObject(time).get(deviceId + ".Instantaneous_flow") != null) {
                        jsonObject.put("value", jsonObject.getBigDecimal("value").add(data.getJSONObject(time).getBigDecimal(deviceId + finalQueryType)));
                    }
                }

                jsonObject.put("id", deviceId);

                // 最小站点
                if (partitionFlowAnalysisDTO.getMinValue() == null || partitionFlowAnalysisDTO.getMinValue().compareTo(jsonObject.getBigDecimal("value")) > 0) {
                    partitionFlowAnalysisDTO.setMinValue(jsonObject.getBigDecimal("value"));
                    partitionFlowAnalysisDTO.setMinPoint(jsonObject.getString("name"));
                }

                // 最大站点
                if (partitionFlowAnalysisDTO.getMaxValue() == null || partitionFlowAnalysisDTO.getMaxValue().compareTo(jsonObject.getBigDecimal("value")) < 0) {
                    partitionFlowAnalysisDTO.setMaxValue(jsonObject.getBigDecimal("value"));
                    partitionFlowAnalysisDTO.setMaxPoint(jsonObject.getString("name"));
                }

                // 合计
                totalValue = totalValue.add(jsonObject.getBigDecimal("value"));
                size++;

                partitionFlowAnalysisDTO.getData().add(jsonObject);
            }
            // 平均值
            partitionFlowAnalysisDTO.setSumValue(totalValue);
            if (size > 0) {
                partitionFlowAnalysisDTO.setAvgValue(totalValue.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
            }

            result.add(partitionFlowAnalysisDTO);
        }

        return new PageData<>(data.keySet().size(), result);
    }

    @Override
    public void getSupplyAndCorrectWater(List<String> partitionIdList, Long startTime, Long endTime, Map<String, BigDecimal> supplyWater, Map<String, BigDecimal> correctSupplyWater) {
        List<JSONObject> partitionTotalFlowList = pipePartitionTotalFlowMapper.sumByPartitionId(partitionIdList, startTime, endTime, "2", "in", "1");
        for (JSONObject jsonObject : partitionTotalFlowList) {
            supplyWater.put(jsonObject.getString("id"), jsonObject.getBigDecimal("total"));
            correctSupplyWater.put(jsonObject.getString("id"), jsonObject.getBigDecimal("correctWater"));

        }
    }
}