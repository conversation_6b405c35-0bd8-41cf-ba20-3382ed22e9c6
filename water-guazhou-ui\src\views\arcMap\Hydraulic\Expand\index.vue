<!-- 拓展分析 -->
<template>
  <RightDrawerMap :title="'扩散分析'">
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <el-divider />
    <HydraulicPanel
      :header="['浓度(mg/L)', '图层控制', '定位']"
      :legends="[
        { label: '0~10mg/L', value: 0, checked: true },
        { label: '10~30mg/L', value: 0, checked: true },
        { label: '30~60mg/L', value: 0, checked: true },
        { label: '60~80mg/L', value: 0, checked: true },
        { label: '80~100mg/L', value: 0, checked: true },
        { label: '>100mg/L', value: 0, checked: true }
      ]"
      :unit="'h'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '扩散点',
          field: 'id',
          appendBtns: [{ perm: true, text: '拾取', click: () => pick() }]
        },
        { type: 'select', label: '计算时间', field: 'time' },
        {
          type: 'btn-group',
          btns: [
            { perm: true, text: '追踪', click: () => catchLocas() },
            { perm: true, text: '溯源', click: () => catchSource() }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '80px',
  defaultValue: {},
  submit: (params: any) => {
    // 此处编写提交逻辑
    console.log(params)
  }
})
const catchLocas = () => {
  //
}
const catchSource = () => {
  //
}
const pick = () => {
  console.log('start pick')
}
</script>
<style lang="scss" scoped></style>
