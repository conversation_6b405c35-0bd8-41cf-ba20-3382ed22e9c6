package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectAccept;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.project.SoProjectAcceptService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/projectAccept")
public class SoProjectAcceptController extends BaseController {
    @Autowired
    private SoProjectAcceptService service;


    @GetMapping
    public IPage<SoProjectAccept> findAllConditional(SoProjectAcceptPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoProjectAcceptPageRequest request) {
        return ExcelFileInfo.of("项目总验收列表", findAllConditional(request).getRecords())
                .nextTitle("projectCode", "项目编号")
                .nextTitle("projectName", "项目名称")
                .nextTitle("projectStartTimeName", "启动时间")
                .nextTitle("acceptTimeName", "总验时间");
    }

    @PostMapping
    public SoProjectAccept save(@RequestBody SoProjectAcceptSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoProjectAcceptSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}