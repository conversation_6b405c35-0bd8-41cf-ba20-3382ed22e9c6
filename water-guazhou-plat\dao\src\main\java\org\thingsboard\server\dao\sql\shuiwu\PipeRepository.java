package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.shuiwu.PipeEntity;

import java.util.List;

public interface PipeRepository extends JpaRepository<PipeEntity, String> {

    @Query("SELECT p FROM PipeEntity p " +
            "WHERE p.tenantId = ?2 AND (p.name LIKE %?1% OR p.code LIKE %?1%) AND p.isDel = '0'")
    Page<PipeEntity> findList(String keyword, String tenantId, Pageable pageable);

    @Query("SELECT p FROM PipeEntity p " +
            "WHERE p.tenantId = ?1 AND p.isDel = '0' " +
            "ORDER BY p.createTime DESC")
    List<PipeEntity> findAllByTenantId(String tenantId);
}
