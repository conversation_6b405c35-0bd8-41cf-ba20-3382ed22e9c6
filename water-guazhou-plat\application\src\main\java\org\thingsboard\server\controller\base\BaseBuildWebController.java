package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseBuildWebService;
import org.thingsboard.server.dao.model.sql.base.BaseBuildWeb;
import org.thingsboard.server.dao.util.imodel.query.base.BaseBuildWebPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-Web搭建Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Api(tags = "平台管理-Web搭建")
@RestController
@RequestMapping("api/base/build/web")
public class BaseBuildWebController extends BaseController {

    @Autowired
    private IBaseBuildWebService baseBuildWebService;

    /**
     * 查询平台管理-Web搭建列表
     */
    @MonitorPerformance(description = "平台管理-查询Web搭建列表")
    @ApiOperation(value = "查询Web搭建列表")
    @GetMapping("/list")
    public IstarResponse list(BaseBuildWebPageRequest baseBuildWeb) {
        return IstarResponse.ok(baseBuildWebService.selectBaseBuildWebList(baseBuildWeb));
    }


    /**
     * 获取平台管理-Web搭建详细信息
     */
    @MonitorPerformance(description = "平台管理-查询Web搭建详情")
    @ApiOperation(value = "查询Web搭建详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseBuildWebService.selectBaseBuildWebById(id));
    }

    /**
     * 新增平台管理-Web搭建
     */
    @MonitorPerformance(description = "平台管理-新增Web搭建")
    @ApiOperation(value = "新增Web搭建")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseBuildWeb baseBuildWeb) {
        return IstarResponse.ok(baseBuildWebService.insertBaseBuildWeb(baseBuildWeb));
    }

    /**
     * 修改平台管理-Web搭建
     */
    @MonitorPerformance(description = "平台管理-修改Web搭建")
    @ApiOperation(value = "修改Web搭建")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseBuildWeb baseBuildWeb) {
        return IstarResponse.ok(baseBuildWebService.updateBaseBuildWeb(baseBuildWeb));
    }

    /**
     * 删除平台管理-Web搭建
     */
    @MonitorPerformance(description = "平台管理-删除Web搭建")
    @ApiOperation(value = "删除Web搭建")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseBuildWebService.deleteBaseBuildWebByIds(ids));
    }
}
