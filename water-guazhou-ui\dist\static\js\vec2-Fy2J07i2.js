var v=1e-6,s=typeof Float32Array<"u"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var r=0,n=arguments.length;n--;)r+=arguments[n]*arguments[n];return Math.sqrt(r)});function l(){var r=new s(2);return s!=Float32Array&&(r[0]=0,r[1]=0),r}function y(r,n){var a=new s(2);return a[0]=r,a[1]=n,a}function o(r,n){return r[0]=n[0],r[1]=n[1],r}function g(r,n,a){return r[0]=n,r[1]=a,r}function A(r,n,a){return r[0]=n[0]+a[0],r[1]=n[1]+a[1],r}function h(r,n,a){return r[0]=n[0]-a[0],r[1]=n[1]-a[1],r}function m(r,n){return r[0]=-n[0],r[1]=-n[1],r}function p(r,n){var a=n[0],t=n[1],e=a*a+t*t;return e>0&&(e=1/Math.sqrt(e)),r[0]=n[0]*e,r[1]=n[1]*e,r}function M(r,n){return r[0]*n[0]+r[1]*n[1]}function d(r,n,a,t){var e=n[0],u=n[1];return r[0]=e+t*(a[0]-e),r[1]=u+t*(a[1]-u),r}var w=h;(function(){var r=l();return function(n,a,t,e,u,c){var i,f;for(a||(a=2),t||(t=0),e?f=Math.min(e*a+t,n.length):f=n.length,i=t;i<f;i+=a)r[0]=n[i],r[1]=n[i+1],u(r,r,c),n[i]=r[0],n[i+1]=r[1];return n}})();export{s as A,v as E,A as a,h as b,l as c,M as d,o as e,y as f,g,m as h,d as l,p as n,w as s};
