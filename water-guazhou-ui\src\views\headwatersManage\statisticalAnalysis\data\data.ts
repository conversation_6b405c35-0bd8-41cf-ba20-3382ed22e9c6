export const colors: Record<string, string> = {
  水厂: '#3c8da5',
  泵站: '#8f5c3e',
  压力监测站: '#909c36',
  流量监测站: '#327c53',
  水质监测站: '#5f4894',
  水源地: '#43548f',
  测流压站: '#9b4b62',
  大用户: '#489785'
}
export const initStationType = (type?: 'all', types?: string[]) => {
  const stationTypes: NormalOption[] = []
  if (types?.length) {
    types.map(item => {
      stationTypes.push({ label: item, value: item })
    })
  } else {
    for (const key in colors) {
      stationTypes.push({ label: key, value: key })
    }
  }
  if (type === 'all') {
    stationTypes.unshift({ label: '全部', value: '' })
  }
  return stationTypes
}
