package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * 锦州大屏配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.JINZHOU_SCADA_CONFIG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class JinzhouScadaConfig {


    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.JINZHOU_SCADA_CONFIG_CODE)
    private String code;

    @Column(name = ModelConstants.JINZHOU_SCADA_CONFIG_JSON_DATA)
    private String jsonData;

}
