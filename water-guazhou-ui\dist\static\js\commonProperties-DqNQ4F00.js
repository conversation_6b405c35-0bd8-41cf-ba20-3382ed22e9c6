import{h as i,e as s,y as r,a as p,W as n}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";const t=new i({esriJobMessageTypeInformative:"informative",esriJobMessageTypeProcessDefinition:"process-definition",esriJobMessageTypeProcessStart:"process-start",esriJobMessageTypeProcessStop:"process-stop",esriJobMessageTypeWarning:"warning",esriJobMessageTypeError:"error",esriJobMessageTypeEmpty:"empty",esriJobMessageTypeAbort:"abort"});let e=class extends n{constructor(o){super(o),this.description=null,this.type=null}};s([r({type:String,json:{write:!0}})],e.prototype,"description",void 0),s([r({type:String,json:{read:t.read,write:t.write}})],e.prototype,"type",void 0),e=s([p("esri.rest.support.GPMessage")],e);const c=e,g={type:String,json:{read:{source:"token"},write:{target:"token"}}};export{c as a,g as t};
