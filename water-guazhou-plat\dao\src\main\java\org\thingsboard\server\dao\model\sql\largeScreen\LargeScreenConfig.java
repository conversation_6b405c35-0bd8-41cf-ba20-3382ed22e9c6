package org.thingsboard.server.dao.model.sql.largeScreen;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import java.util.Date;

/**
 * 用户基本信息
 */
@Data
@TableName("tb_large_screen_config")
@NoArgsConstructor
@AllArgsConstructor
public class LargeScreenConfig {

    @Id
    private String id;

    private String name;

    private String type;

    private String content;

    private Date createTime;

    private String tenantId;

}
