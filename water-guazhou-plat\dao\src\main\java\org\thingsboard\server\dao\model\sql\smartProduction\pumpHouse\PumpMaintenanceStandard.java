package org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("sp_pump_maintenance_standard")
// 泵机维保标准
public class PumpMaintenanceStandard {
    // id
    @TableId
    private String id;

    // 文件类型
    private String type;

    // 文件名称
    private String name;

    // 录入人
    private String inputUserName;

    // 备注
    private String remark;

    // 文件url
    private String file;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
