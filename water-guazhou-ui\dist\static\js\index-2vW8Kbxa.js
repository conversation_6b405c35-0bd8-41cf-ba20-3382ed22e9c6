import{d as q,M as Y,c,s as P,r as g,bS as O,x,a8 as C,a9 as k,o as U,g as F,n as z,q as d,i as b,bo as A,F as E,G as J,b6 as j,J as B,bK as $,b7 as G,bR as Q}from"./index-r0dFAfgr.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{_ as K}from"./CardSearch-CB_HNR-Q.js";import{I as h}from"./common-CvK_P_ao.js";import{p as H,e as X,g as Z,a as ee,b as te,c as ae}from"./equipmentPurchase-KOqzaoYr.js";import{r as le,b as ie}from"./equipmentAssetsData-B4Olvyjd.js";import{f as R}from"./DateFormatter-Bm9a68Ax.js";import{l as re}from"./equipmentManage-DuoY00aj.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./xlsx-rVJkW9yq.js";const oe={class:"wrapper"},ve=q({__name:"index",setup(ne){const{$btnPerms:m}=Y(),D=c(),s=c(),w=c(),y=c(),_=c(),v=c([]),S=c({filters:[{label:"合同编号",field:"code",type:"input"},{label:"合同名称",field:"title",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:h.QUERY,click:()=>f()},{type:"default",perm:!0,text:"重置",svgIcon:P(G),click:()=>{var e;(e=D.value)==null||e.resetForm(),f()}},{type:"success",perm:m("RoleManageAdd"),text:"新增",icon:h.ADD,click:()=>N()}]}]}),n=g({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"合同标题",prop:"title"},{label:"合同编号",prop:"code"},{label:"所属采购单",prop:"purchaseName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime"},{label:"文件下载",prop:"file",download:!0,cellStyle:{fontStyle:"italic",whiteSpace:"pre-wrap",width:"100px",cursor:"pointer"},handleClick:e=>{O(e.file)}}],operations:[{text:"编辑",perm:m("RoleManageEdit"),icon:h.EDIT,click:e=>M(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,f()}}}),p=g({title:"新增",labelWidth:"130px",submit:(e,t)=>{var r;if(t){l.getDevice(),(r=y.value)==null||r.openDrawer();return}if(n.dataList.findIndex(i=>i.code===e.code)!==-1&&!e.id){x.warning("合同编号重复");return}let a="新增成功";e.id&&(a="修改成功"),H(e).then(()=>{var i;f(),x.success(a),(i=s.value)==null||i.closeDrawer()})},defaultValue:{},group:[{fields:[{xl:12,disabled:!0,type:"input",label:"所属采购单",field:"purchaseName",rules:[{required:!0,message:"请点击采购单里的查询按钮选择采购单"}],btn:{icon:h.QUERY,type:"primary",perm:!0,click:()=>{var e;(e=_.value)==null||e.openDrawer()}}},{xl:12,type:"input",label:"合同编号",field:"code",rules:[{required:!0,message:"请输入合同编号"}]},{xl:12,type:"input",label:"合同标题",field:"title",rules:[{required:!0,message:"请输入合同标题"}]},{xl:24,type:"file",label:"上传文件",field:"file"},{xl:12,type:"btn-group",label:"设备内容",btns:[{text:"下载模板",type:"primary",perm:m("RoleManageDelete"),click:()=>{X().then(e=>{const t=window.URL.createObjectURL(e.data);console.log(t);const a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","设备模板.xlsx"),document.body.appendChild(a),a.click()}).catch(e=>{x.warning(e)})}},{text:"点击上传",type:"primary",perm:m("RoleManageDelete"),click:()=>{w.value.$el.click()}}]},{type:"table",field:"items",config:{indexVisible:!0,height:"350px",dataList:C(()=>l.detail),titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{var e;(e=s.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"设备编号",prop:"serialId",formItemConfig:{disabled:!0,type:"input"}},{label:"货品名称",prop:"name",formItemConfig:{disabled:!0,type:"input"}},{label:"规格型号",prop:"model",formItemConfig:{disabled:!0,type:"input"}},{label:"单位",prop:"unit",formItemConfig:{disabled:!0,type:"input"}},{label:"数量",prop:"num",tableDataName:"items",formItemConfig:{type:"input",field:"num",rules:[{required:!0,message:"请输入数量"}]}},{label:"单价",prop:"price",tableDataName:"items",formItemConfig:{type:"input",field:"price",rules:[{required:!0,message:"请输入单价"}]}},{label:"税率",prop:"taxRate",tableDataName:"items",formItemConfig:{type:"input",field:"taxRate",rules:[{required:!0,message:"请输入税率"}]}},{label:"备注",prop:"remark",formItemConfig:{type:"input"}}],operations:[{text:"移除",type:"danger",icon:h.DELETE,perm:m("RoleManageDelete"),click:e=>{l.detail=l.detail.filter(t=>t.number!==e.number)}}],pagination:{hide:!0}}}]}]}),V=g({title:"采购单列表",defaultValue:{},group:[{fields:[{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:C(()=>l.purchaseList),handleRowClick:e=>{var t,a;(t=s.value)==null||t.closeDrawer(),p.defaultValue={...p.defaultValue,purchaseName:e.title,purchaseId:e.id},l.getDevicePurchaseItemValue(e.id),(a=_.value)==null||a.closeDrawer(),setTimeout(()=>{var r;(r=s.value)==null||r.openDrawer()},500)},columns:[{label:"采购单标题",prop:"title"},{label:"采购单编码",prop:"code"},{label:"采购部门名称",prop:"tenantName"},{label:"请购人",prop:"userName"},{label:"用途",prop:"useWay"},{label:"申请时间",prop:"preTime",formatter:e=>R(e.preTime,"YYYY-MM-DD")},{label:"采购单创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>R(e.createTime,"YYYY-MM-DD")}],pagination:{hide:!0}}}]}]}),L=g({title:"设备选择",submit:(e,t)=>{var a;if(t)l.getDevice(e);else{const r=k(v.value,"children",{number:"id"}),i=[];r.forEach(o=>{i.some(u=>u.number===o.number)||i.push(o)}),l.detail=[...l.detail,...i],(a=y.value)==null||a.closeDrawer()}},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"设备编码",field:"serialId"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:C(()=>l.deviceValue),selectList:[],handleSelectChange:e=>{v.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"搜索",perm:!0,click:()=>{var e;(e=y.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"设备款式",prop:"model"},{label:"可用年限",prop:"useYear"}],pagination:{hide:!0}}}]}]}),N=()=>{var e;p.title="新增",l.detail=[],v.value=[],p.defaultValue={},(e=s.value)==null||e.openDrawer()},M=e=>{var t;v.value=[],p.title="编辑",l.getdetail(e),p.defaultValue={...e||{}},(t=s.value)==null||t.openDrawer()};async function T(e){console.log(e),le(e).then(t=>{let a=1;t&&t.forEach(o=>{const u={number:a++};for(const I in o)u[ie[I]]=o[I];l.detail.push(u)});const r=l.detail.map(o=>JSON.stringify(o)),i=[...new Set(r)];l.detail=i.map(o=>JSON.parse(o))})}const l=g({deviceValue:[],detail:[],purchaseList:[],getDevice:e=>{const t={size:99999,page:1,...e};re(t).then(a=>{l.deviceValue=a.data.data.data||[]})},getdetail:e=>{const t={page:1,size:99999,mainId:e.id};Z(t).then(a=>{l.detail=k(a.data.data.data||[],"children",{number:"id"})})},getDevicePurchaseItemValue:e=>{ee({mainId:e,page:1,size:20}).then(a=>{l.detail=k(a.data.data.data||[],"children",{number:"id"})})},init:()=>{te({size:99999,page:1}).then(t=>{l.purchaseList=t.data.data.data||[]}).catch(()=>{x.error("获取数据失败")})}}),f=async()=>{var t;const e={size:n.pagination.limit,page:n.pagination.page,...((t=D.value)==null?void 0:t.queryParams)||{}};ae(e).then(a=>{n.dataList=a.data.data.data||[],n.pagination.total=a.data.data.total||0})};return U(()=>{f(),l.init()}),(e,t)=>{const a=K,r=W,i=j,o=B,u=$;return F(),z("div",oe,[d(a,{ref_key:"refSearch",ref:D,config:b(S)},null,8,["config"]),d(r,{config:b(n),class:"card-table"},null,8,["config"]),d(i,{ref_key:"refForm",ref:s,config:b(p)},null,8,["config"]),d(i,{ref_key:"PurchaseOrder",ref:_,config:b(V)},null,8,["config"]),d(i,{ref_key:"refFormEquipment",ref:y,config:b(L)},null,8,["config"]),A(d(u,{ref:"upload",action:"action",accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","show-file-list":!1,"on-change":T},{default:E(()=>[d(o,{ref_key:"fileRef",ref:w,size:"small",type:"primary"},{default:E(()=>t[0]||(t[0]=[J(" 点击上传 ")])),_:1},512)]),_:1},512),[[Q,!1]])])}}});export{ve as default};
