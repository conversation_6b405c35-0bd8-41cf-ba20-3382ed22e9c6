package org.thingsboard.server.dao.sql.smartProduction.safeProduction;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionRunScore;

import java.util.List;

@Mapper
public interface ProductionRunScoreMapper extends BaseMapper<ProductionRunScore> {

    List<ProductionRunScore> findList(@Param("param") SafeProductionRequest request);

    List<JSONObject> yearSum(@Param("year") String year);
}
