import{U as n,d as b,c as k,r as l,b as e,S as y,o as _,g as h,n as w,q as d,i as u,p as L,_ as v,aq as B}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{e as C}from"./ViewHelper-BGCZjxXH.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const F=m=>n({url:"/api/webapp/bookmarks/add",method:"post",data:m}),M=m=>n({url:"/api/webapp/bookmarks",method:"post",data:m}),S=m=>n({url:"/api/webapp/bookmarks/delete",method:"post",data:{id:m}}),D={class:"workspace"},q={class:"table-box"},Jo=b({__name:"WorkSpace",props:{view:{}},setup(m){const c=m,g=k(),a=l({dataList:[],rowKey:"id",columns:[{label:"名称",prop:"markname"},{label:"创建时间",prop:"createdate"}],singleSelect:!0,handleRowDbClick:o=>C(c.view,{xmin:o==null?void 0:o.xmin,ymin:o==null?void 0:o.ymin,xmax:o==null?void 0:o.xmax,ymax:o==null?void 0:o.ymax}),select:(o,t)=>{a.selectList=t?[o]:[]},pagination:{layout:"total,sizes, jumper",refreshData:({page:o,size:t})=>{a.pagination.page=o,a.pagination.limit=t,s()}}}),f=l({labelPosition:"top",gutter:12,group:[{fields:[{type:"input",field:"name",label:"",clearable:!1,prepend:"名称",placeholder:"请输入当前工作空间的名称"},{type:"btn-group",btns:[{perm:!0,text:"添加工作空间",type:"success",styles:{width:"100%"},click:async o=>{var p;const t=o==null?void 0:o.name;if(!t){e.warning("请输入名称");return}const r=(p=c.view)==null?void 0:p.extent,i=await F({markname:t,remark:t,xmin:(r==null?void 0:r.xmin)||0,ymin:(r==null?void 0:r.ymin)||0,xmax:(r==null?void 0:r.xmax)||0,ymax:(r==null?void 0:r.ymax)||0});i.data.code===1e4?(e.success(i.data.message||"添加成功"),s()):e.error(i.data.message||"添加失败")}}]}]},{fieldset:{desc:"我的工作空间",right:[{style:{marginLeft:"auto"},items:[{type:"btn-group",btns:[{perm:!0,type:"danger",size:"small",disabled:()=>{var o;return!((o=a.selectList)!=null&&o.length)},text:"删除",click:()=>{y("确定删除？","提示信息").then(async()=>{var o,t,r;try{const i=((o=a.selectList)==null?void 0:o.length)&&a.selectList[0].id;if(i){const p=await S(i);p.data.code===1e4?(e.success((t=p.data)==null?void 0:t.message),s()):e.error(((r=p.data)==null?void 0:r.message)||"删除失败")}else e.error("参数错误")}catch{e.error("系统错误")}}).catch(()=>{})}}]}]}]},fields:[]}]}),s=async()=>{var t,r,i;const o=await M({pagenumber:a.pagination.page||1,pagesize:a.pagination.limit||20});a.dataList=((r=(t=o.data)==null?void 0:t.result)==null?void 0:r.rows)||[],a.pagination.total=((i=o.data.result)==null?void 0:i.totalnumberofrecords)||0};return _(()=>{s()}),(o,t)=>{const r=v,i=B;return h(),w("div",D,[d(r,{ref_key:"refForm",ref:g,config:u(f)},null,8,["config"]),L("div",q,[d(i,{config:u(a)},null,8,["config"])])])}}});export{Jo as default};
