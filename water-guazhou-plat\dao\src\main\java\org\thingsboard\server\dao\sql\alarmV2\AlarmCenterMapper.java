package org.thingsboard.server.dao.sql.alarmV2;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.request.AlarmCenterListRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;

import java.util.Date;
import java.util.List;

@Mapper
public interface AlarmCenterMapper extends BaseMapper<AlarmCenter> {
    IPage<AlarmCenter> findList(IPage<AlarmCenter> pageRequest, @Param("param") AlarmCenterListRequest request);

    List<AlarmCenter> findByAlarm(@Param("start") Date start, @Param("end") Date end, @Param("tenantId") String tenantId);

    List<CountObjDTO> rankByStation(@Param("start") Date start, @Param("end") Date end, @Param("tenantId") String tenantId, @Param("stationType") String stationType);

    List<AlarmCenter> findAll(@Param("param") AlarmCenterListRequest request);

    AlarmCenter lastAlarmByAlarmRuleId(@Param("id") String id);
}
