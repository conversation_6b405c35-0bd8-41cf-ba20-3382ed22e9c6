import{U as h}from"./pe-B8dP0-Ut.js";import{t as d,b as g,w as y}from"./Point-WxyopZva.js";import{v as m,b as p}from"./MapView-DaoQedLH.js";import{r as f}from"./AnimatedLinesLayer-B2VbV4jv.js";const w=async(e,a,c,n)=>{const t=e.extent.toJSON(),i=e.width,o=e.height;let s,r;if(n.x!==void 0&&n.y!==void 0)s=Math.floor(n.x),r=Math.floor(n.y);else if(n.screenPoint)s=Math.floor(n.screenPoint.x),r=Math.floor(n.screenPoint.y);else{const l=e.toScreen(n.mapPoint);s=Math.floor(l.x),r=Math.floor(l.y)}console.log("GeoServer查询屏幕坐标:",s,r),d({x:t.xmin,y:t.ymin}).x,d({x:t.xmin,y:t.ymin}).y,d({x:t.xmax,y:t.ymax}).x,d({x:t.xmax,y:t.ymax}).y;const u=`${a}?service=WMS&version=1.1.1&request=GetFeatureInfo&layers=${c}&bbox=${t.xmin},${t.ymin},${t.xmax},${t.ymax}&width=${i}&height=${o}&query_layers=${c}&info_format=application/json&x=${s}&y=${r}&feature_count=10&srs=EPSG:3857`;try{return await h(u,{responseType:"json"})}catch(l){return console.error("GeoServer查询失败：",l),null}},F=e=>{if(!e)return null;const a=new g({wkid:3857});switch(e.type){case"Point":return new y({x:e.coordinates[0],y:e.coordinates[1],spatialReference:a});case"MultiPoint":return new y({points:e.coordinates,spatialReference:a});case"LineString":return new p({paths:[e.coordinates],spatialReference:a});case"MultiLineString":return new p({paths:e.coordinates,spatialReference:a});case"Polygon":return new m({rings:e.coordinates,spatialReference:a});case"MultiPolygon":const c=[];return e.coordinates.forEach(n=>{n.forEach(t=>{c.push(t)})}),new m({rings:c,spatialReference:a});default:return console.error("不支持的几何类型:",e.type),null}},G=async e=>{var i,o;const{typeName:a,id:c,workspace:n="guazhou",outputSRS:t="EPSG:3857"}=e;try{const s=`${a}.${c}`,r=await f({url:`/geoserver/${n}/wfs?service=WFS&version=1.0.0&request=GetFeature&featureId=${s}&outputFormat=application/json&srsName=${t}`,method:"get"});return(o=(i=r.data)==null?void 0:i.features)!=null&&o.length?{success:!0,data:r.data,feature:r.data.features[0],coordinateSystem:t}:{success:!1,message:"未找到匹配的要素",data:null,feature:null,coordinateSystem:t}}catch(s){return console.error("查询要素失败:",s),{success:!1,message:s.message||"查询要素失败",error:s,data:null,feature:null,coordinateSystem:t}}},P=async e=>{const a=e.nodeGeometries||[],c=e.outputLayers.split(",");if(a.length===0&&e.flagOIDs){if(console.log("没有提供节点几何信息，尝试从flagOIDs中获取"),!e.nodeLayerName)return console.error("没有提供节点图层名称，无法查询节点几何信息"),{data:{code:1e4,message:"没有提供节点几何信息和节点图层名称",result:[]}};try{const o=e.flagOIDs.split(","),s=`id IN (${o.join(",")}) OR OBJECTID IN (${o.join(",")})`,r=await f({url:`/geoserver/guazhou/wfs?service=WFS&version=1.0.0&request=GetFeature&typeName=${e.nodeLayerName}&CQL_FILTER=${encodeURIComponent(s)}&outputFormat=application/json`,method:"get"});if(r.data&&r.data.features&&r.data.features.length>0)for(const u of r.data.features)a.push(u.geometry);else return console.error("未找到节点的几何信息"),{data:{code:1e4,message:"未找到节点的几何信息",result:[]}}}catch(o){return console.error("查询节点几何信息失败:",o),{data:{code:1e4,message:"查询节点几何信息失败: "+o.message,result:[]}}}}if(a.length<2)return console.error("节点几何信息不足，需要两个节点"),{data:{code:1e4,message:"节点几何信息不足，需要两个节点",result:[]}};const n=a[0].coordinates,t=a[1].coordinates;return{data:{code:0,message:"查询成功",result:await Promise.all(c.map(async o=>{try{const s=`INTERSECTS(the_geom, POINT(${n[0]} ${n[1]})) OR INTERSECTS(the_geom, POINT(${t[0]} ${t[1]}))`;console.log(`使用空间查询图层 ${o}`);const r=await f({url:`/geoserver/guazhou/wfs?service=WFS&version=1.0.0&request=GetFeature&typeName=${o}&CQL_FILTER=${encodeURIComponent(s)}&outputFormat=application/json`,method:"get"});return r.data&&r.data.features&&r.data.features.length>0?(console.log(`成功查询到图层 ${o} 的数据，共 ${r.data.features.length} 条`),{layeralias:o,queryresult:r.data.features.map(u=>({...u.properties,geometry:u.geometry,id:u.id}))}):(console.log(`图层 ${o} 没有与节点相交的要素`),{layeralias:o,queryresult:[]})}catch(s){return console.error(`查询图层 ${o} 失败:`,s),{layeralias:o,queryresult:[]}}}))}}};export{F as c,w as e,P as g,G as q};
