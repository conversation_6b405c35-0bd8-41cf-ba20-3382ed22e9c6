package org.thingsboard.server.dao.model.sql.input;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_INPUT_WULIAO_TABLE)
@NoArgsConstructor
public class InputWuliao {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_PROCESS_WATER)
    private BigDecimal processWater;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_XFLJ_NUM)
    private BigDecimal xfljNum;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_XFLJ_UNIT)
    private BigDecimal xfljUnit;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_XDJ_NUM)
    private BigDecimal xdjNum;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_XDJ_UNIT)
    private BigDecimal xdjUnit;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_TY_NUM)
    private BigDecimal tyNum;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_TY_UNIT)
    private BigDecimal tyUnit;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_CLYJ_NUM)
    private BigDecimal clyjNum;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_CLYJ_UNIT)
    private BigDecimal clyjUnit;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_WNTSJ_NUM)
    private BigDecimal wntsjNum;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_WNTSJ_UNIT)
    private BigDecimal wntsjUnit;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_ENERGY_NUM)
    private BigDecimal energyNum;

    @Column(name = ModelConstants.TB_INPUT_WULIAO_ENERGY_UNIT)
    private BigDecimal energyUnit;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private String timeStr;
}
