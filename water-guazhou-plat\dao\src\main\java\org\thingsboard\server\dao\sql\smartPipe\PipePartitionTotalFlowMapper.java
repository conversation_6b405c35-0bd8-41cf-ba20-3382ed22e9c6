package org.thingsboard.server.dao.sql.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionTotalFlowRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 分区供水量
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-10
 */
@Mapper
public interface PipePartitionTotalFlowMapper extends BaseMapper<PipePartitionTotalFlow> {

    IPage<PipePartitionTotalFlow> getList(IPage<PipePartitionTotalFlow> page, @Param("param") PartitionTotalFlowRequest request);

    void batchSave(List<PipePartitionTotalFlow> pipePartitionTotalFlowLis);

    List<Map> getDayTotalFlow(@Param("list") List<String> deviceOriginList, @Param("start") Long start, @Param("end") Long end, @Param("type") String type);

    void deleteByType(@Param("list") List<String> deviceOriginList, @Param("start") Long start, @Param("end") Long end, @Param("type") String type);

    BigDecimal getPartitionTotalFlow(@Param("partitionId") String partitionId, @Param("start") LocalDate start, @Param("end") LocalDate end, @Param("type") String type, @Param("direction") String direction);

    BigDecimal sumByTime(@Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId, @Param("type") String type, @Param("deviceList") List<String> deviceList);

    List<PipePartitionTotalFlow> getAllByTime(@Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId, @Param("type") String type, @Param("deviceList") List<String> deviceList);

    int countByTime(@Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId, @Param("type") String type, @Param("deviceList") List<String> deviceList);

    List<JSONObject> sumByPartitionId(@Param("partitionIdList") List<String> partitionIdList, @Param("start") Long start, @Param("end") Long end, @Param("type") String type, @Param("direction") String direction, @Param("deviceType") String deviceType);

    List<PipePartitionTotalFlow> getListByPartitionId(@Param("partitionId") String partitionId, @Param("start") Long start, @Param("end") Long end, @Param("type") String type, @Param("direction") String direction, @Param("tenantId") String tenantId);

    BigDecimal getSupplyTotalByPartitionId(@Param("partitionId") String partitionId, @Param("start") Long start, @Param("end") Long end, @Param("type") String type);
}
