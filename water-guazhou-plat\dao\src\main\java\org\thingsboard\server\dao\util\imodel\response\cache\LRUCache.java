package org.thingsboard.server.dao.util.imodel.response.cache;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Consumer;

public class LRUCache<TKey, TVal> implements Cache<TKey, TVal> {
    private Map<TKey, TVal> cache;
    private TKey eldestKey;

    private final Consumer<TKey> onInvalidate;

    public LRUCache() {
        this(1024);
    }

    public LRUCache(int size) {
        this(size, null);
    }

    public LRUCache(int size, Consumer<TKey> onInvalidate) {
        this.onInvalidate = onInvalidate;
        initializeCache(size);
    }


    private void initializeCache(int size) {
        cache = new LinkedHashMap<TKey, TVal>(size, .75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<TKey, TVal> eldest) {
                boolean isFull = size() > size;
                if (isFull) {
                    eldestKey = eldest.getKey();
                }
                return isFull;
            }
        };
    }

    @Override
    public void put(T<PERSON><PERSON> key, TVal val) {
        cache.put(key, val);
        if (eldestKey != null) {
            if (onInvalidate != null) {
                onInvalidate.accept(eldestKey);
            }
            eldestKey = null;
        }
    }

    @Override
    public TVal get(TKey key) {
        return cache.get(key);
    }

    @Override
    public boolean contains(TKey key) {
        return cache.containsKey(key);
    }

    @Override
    public TVal invalidate(TKey tKey) {
        return cache.remove(tKey);
    }
}
