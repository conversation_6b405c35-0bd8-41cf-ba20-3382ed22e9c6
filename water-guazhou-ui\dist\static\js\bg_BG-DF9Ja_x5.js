import{o as p}from"./_commonjsHelpers-DCkdB7M8.js";import{r as a}from"./_commonjs-dynamic-modules-DfYEAvWy.js";function c(e,r){for(var f=0;f<r.length;f++){const t=r[f];if(typeof t!="string"&&!Array.isArray(t)){for(const o in t)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(t,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>t[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var s,u,n={},l={get exports(){return n},set exports(e){n=e}};s=l,(u=function(e,r){Object.defineProperty(r,"__esModule",{value:!0}),r.default={_percentPrefix:null,_percentSuffix:"%","Zoom Out":"Отдалечаване","From %1 to %2":"От %1 до %2","From %1":"От %1","To %1":"До %1"}}(a,n))!==void 0&&(s.exports=u);const b=c({__proto__:null,default:p(n)},[n]);export{b};
