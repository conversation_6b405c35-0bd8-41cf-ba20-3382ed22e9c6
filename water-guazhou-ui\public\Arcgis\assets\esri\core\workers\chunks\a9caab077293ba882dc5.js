"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7476],{28576:(e,t,r)=>{r.d(t,{B:()=>c});var i=r(81153),s=r(17452),o=r(41123),n=r(7628),a=r(31263),l=r(5600),p=r(66094),d=r(25929);function c(e){const t=e?.origins??[void 0];return(r,o)=>{const c=function(e,t,r){if("resource"===e?.type)return function(e,t,r){const o=(0,n.Oe)(t,r);return{type:String,read:(e,t,r)=>{const i=(0,d.r)(e,t,r);return o.type===String?i:"function"==typeof o.type?new o.type({url:i}):void 0},write:{writer(t,n,l,c){if(!c||!c.resources)return"string"==typeof t?void(n[l]=(0,d.t)(t,c)):void(n[l]=t.write({},c));const f=function(e){return null==e?null:"string"==typeof e?e:e.url}(t),g=(0,d.t)(f,{...c,verifyItemRelativeUrls:c&&c.verifyItemRelativeUrls?{writtenUrls:c.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},d.M.NO),m=o.type!==String&&(!(0,i.l)(this)||c&&c.origin&&this.originIdOf(r)>(0,a.M9)(c.origin)),b={object:this,propertyName:r,value:t,targetUrl:g,dest:n,targetPropertyName:l,context:c,params:e};c&&c.portalItem&&g&&!(0,s.YP)(g)?m?function(e){const{context:t,targetUrl:r,params:i,value:o,dest:n,targetPropertyName:a}=e;if(!t.portalItem)return;const l=t.portalItem.resourceFromPath(r),d=h(o,r,t),c=(0,p.B)(d),f=(0,s.Ml)(l.path),g=i?.compress??!1;c===f?(t.resources&&y({...e,resource:l,content:d,compress:g,updates:t.resources.toUpdate}),n[a]=r):u(e)}(b):function({context:e,targetUrl:t,dest:r,targetPropertyName:i}){e.portalItem&&e.resources&&(e.resources.toKeep.push({resource:e.portalItem.resourceFromPath(t),compress:!1}),r[i]=t)}(b):c&&c.portalItem&&(null==g||null!=(0,d.i)(g)||(0,s.jc)(g)||m)?u(b):n[l]=g}}}}(e,t,r);switch(e?.type??"other"){case"other":return{read:!0,write:!0};case"url":{const{read:e,write:t}=d.a;return{read:e,write:t}}}}(e,r,o);for(const e of t){const t=(0,l.CJ)(r,e,o);for(const e in c)t[e]=c[e]}}}function u(e){const{targetUrl:t,params:i,value:n,context:a,dest:l,targetPropertyName:c}=e;if(!a.portalItem)return;const u=(0,d.p)(t),f=u?.filename??(0,o.D)(),g=i?.prefix??u?.prefix,m=h(n,t,a),b=(0,s.v_)(g,f),v=`${b}.${(0,p.B)(m)}`,w=a.portalItem.resourceFromPath(v);(0,s.jc)(t)&&a.resources&&a.resources.pendingOperations.push(async function(e){const t=(await Promise.resolve().then(r.bind(r,3172))).default,{data:i}=await t(e,{responseType:"blob"});return i}(t).then((e=>{w.path=`${b}.${(0,p.B)(e)}`,l[c]=w.itemRelativeUrl})).catch((()=>{})));const I=i?.compress??!1;a.resources&&y({...e,resource:w,content:m,compress:I,updates:a.resources.toAdd}),l[c]=w.itemRelativeUrl}function y({object:e,propertyName:t,updates:r,resource:i,content:s,compress:o}){r.push({resource:i,content:s,compress:o,finish:r=>{!function(e,t,r){"string"==typeof e[t]?e[t]=r.url:e[t].url=r.url}(e,t,r)}})}function h(e,t,r){return"string"==typeof e?{url:t}:new Blob([JSON.stringify(e.toJSON(r))],{type:"application/json"})}},57476:(e,t,r)=>{r.r(t),r.d(t,{default:()=>he});var i=r(43697),s=r(38171),o=r(51773),n=(r(16050),r(12501),r(28756),r(92271),r(72529),r(5499),r(84382),r(81571),r(91423),r(32400)),a=r(3172),l=r(46791),p=r(20102),d=r(92604),c=r(70586),u=r(16453),y=r(95330),h=r(17445),f=r(17452),g=r(5600),m=(r(75215),r(67676),r(1153)),b=r(71715),v=r(52011),w=r(31263),I=r(87085),_=r(54295),C=r(17287),L=r(66361),S=r(38009),F=r(16859),j=r(72965),x=r(20559),O=r(15506),P=r(21506),R=r(61960),E=r(85857),T=r(14147),N=r(53518),A=r(99514),Z=r(35671),D=r(51161),U=r(54306),q=r(30707),Q=r(56765),k=r(96674);let G=class extends k.wq{constructor(){super(...arguments),this.name=null,this.field=null,this.currentRangeExtent=null,this.fullRangeExtent=null,this.type="rangeInfo"}};(0,i._)([(0,g.Cb)({type:String,json:{read:!0,write:!0}})],G.prototype,"name",void 0),(0,i._)([(0,g.Cb)({type:String,json:{read:!0,write:!0}})],G.prototype,"field",void 0),(0,i._)([(0,g.Cb)({type:[Number],json:{read:!0,write:!0}})],G.prototype,"currentRangeExtent",void 0),(0,i._)([(0,g.Cb)({type:[Number],json:{read:!0,write:!0}})],G.prototype,"fullRangeExtent",void 0),(0,i._)([(0,g.Cb)({type:["rangeInfo"],readOnly:!0,json:{read:!1,write:!0}})],G.prototype,"type",void 0),G=(0,i._)([(0,v.j)("esri.layers.support.RangeInfo")],G);var V,J=r(61247),W=r(22974),M=r(28576),B=r(20941),z=(r(80442),r(38913)),$=r(44547);let K=V=class extends((0,k.eC)(l.Z.ofType(z.Z))){constructor(e){super(e)}clone(){return new V(this.items.map((e=>e.clone())))}write(e,t){return this.toJSON(t)}toJSON(e){const t=e?.layer?.spatialReference;return t?this.toArray().map((r=>{if(!t.equals(r.spatialReference)){if(!(0,$.Up)(r.spatialReference,t))return e&&e.messages&&e.messages.push(new B.Z("scenefilter:unsupported","Scene filters with incompatible spatial references are not supported",{modification:this,spatialReference:e.layer.spatialReference,context:e})),null;const i=new z.Z;(0,$.Wt)(r,i,t),r=i}const i=r.toJSON(e);return delete i.spatialReference,i})).filter((e=>null!=e)):(e?.messages&&e.messages.push(new B.Z("scenefilter:unsupported","Writing Scene filters without context layer is not supported",{modification:this,spatialReference:e.layer.spatialReference,context:e})),this.toArray().map((t=>t.toJSON(e))))}static fromJSON(e,t){const r=new V;return e.forEach((e=>r.add(z.Z.fromJSON(e,t)))),r}};K=V=(0,i._)([(0,v.j)("esri.layers.support.PolygonCollection")],K);const Y=K;var H,X=r(25929);let ee=H=class extends k.wq{constructor(e){super(e),this.spatialRelationship="disjoint",this.geometries=new Y,this._geometriesSource=null,this._handles=new J.Z}initialize(){this._handles.add((0,h.on)((()=>this.geometries),"after-changes",(()=>this.geometries=this.geometries),h.Z_))}destroy(){this._handles.destroy()}readGeometries(e,t,r){this._geometriesSource={url:(0,X.f)(e,r),context:r}}async loadGeometries(e,t){if((0,c.Wi)(this._geometriesSource))return;const{url:r,context:i}=this._geometriesSource,s=await(0,a.default)(r,{responseType:"json",signal:(0,c.U2)(t,"signal")}),o=e.toJSON(),n=s.data.map((e=>({...e,spatialReference:o})));this.geometries=Y.fromJSON(n,i),this._geometriesSource=null}clone(){return new H({geometries:(0,W.d9)(this.geometries),spatialRelationship:this.spatialRelationship})}};(0,i._)([(0,g.Cb)({type:["disjoint","contains"],nonNullable:!0,json:{write:!0}})],ee.prototype,"spatialRelationship",void 0),(0,i._)([(0,g.Cb)({type:Y,nonNullable:!0,json:{write:!0}}),(0,M.B)({origins:["web-scene","portal-item"],type:"resource",prefix:"geometries"})],ee.prototype,"geometries",void 0),(0,i._)([(0,b.r)(["web-scene","portal-item"],"geometries")],ee.prototype,"readGeometries",null),ee=H=(0,i._)([(0,v.j)("esri.layers.support.SceneFilter")],ee);const te=ee;var re=r(40555),ie=r(14165),se=r(72245),oe=r(32163),ne=r(77397),ae=r(19833),le=r(65242);const pe=["3DObject","Point"],de=(0,N.v)();let ce=class extends((0,L.o1)((0,x.Vt)((0,C.Y)((0,S.q)((0,F.I)((0,j.M)((0,u.R)((0,_.V)(I.Z))))))))){constructor(...e){super(...e),this.featureReduction=null,this.rangeInfos=null,this.operationalLayerType="ArcGISSceneServiceLayer",this.type="scene",this.fields=null,this.floorInfo=null,this.outFields=null,this.nodePages=null,this.materialDefinitions=null,this.textureSetDefinitions=null,this.geometryDefinitions=null,this.serviceUpdateTimeStamp=null,this.excludeObjectIds=new l.Z,this.definitionExpression=null,this.filter=null,this.path=null,this.labelsVisible=!0,this.labelingInfo=null,this.legendEnabled=!0,this.priority=null,this.semantic=null,this.cachedDrawingInfo={color:!1},this.popupEnabled=!0,this.popupTemplate=null,this.objectIdField=null,this.globalIdField=null,this._fieldUsageInfo={},this.screenSizePerspectiveEnabled=!0}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}getField(e){return this.fieldsIndex.get(e)}getFieldDomain(e,t){const r=this.getFeatureType(t?.feature)?.domains?.[e];return r&&"inherited"!==r.type?r:this.getField(e)?.domain??null}getFeatureType(e){return null!=e&&(0,c.pC)(this.associatedLayer)?this.associatedLayer.getFeatureType(e):null}get types(){return(0,c.pC)(this.associatedLayer)?this.associatedLayer.types??[]:[]}get typeIdField(){return(0,c.pC)(this.associatedLayer)?this.associatedLayer.typeIdField:null}get formTemplate(){return(0,c.pC)(this.associatedLayer)?this.associatedLayer.formTemplate:null}get fieldsIndex(){return new A.Z(this.fields)}readNodePages(e,t,r){return"Point"===t.layerType&&(e=t.pointNodePages),null==e||"object"!=typeof e?null:D.U4.fromJSON(e,r)}set elevationInfo(e){this._set("elevationInfo",e),this.loaded&&this._validateElevationInfo()}get geometryType(){return ye[this.profile]||"mesh"}set renderer(e){(0,Z.YN)(e,this.fieldsIndex),this._set("renderer",e)}readCachedDrawingInfo(e){return null!=e&&"object"==typeof e||(e={}),null==e.color&&(e.color=!1),e}get capabilities(){const e=(0,c.pC)(this.associatedLayer)&&this.associatedLayer.capabilities?this.associatedLayer.capabilities:O.C,{query:t,editing:{supportsGlobalId:r,supportsRollbackOnFailure:i,supportsUploadWithItemId:s,supportsGeometryUpdate:o,supportsReturnServiceEditsInSourceSpatialReference:n},data:{supportsZ:a,supportsM:l,isVersioned:p,supportsAttachment:d},operations:{supportsEditing:u,supportsAdd:y,supportsUpdate:h,supportsDelete:f,supportsQuery:g,supportsQueryAttachments:m}}=e,b=e.operations.supportsChangeTracking,v=(0,c.pC)(this.associatedLayer)&&(0,c.pC)(this.associatedLayer.infoFor3D)&&(0,se.Rx)();return{query:t,editing:{supportsGlobalId:r,supportsReturnServiceEditsInSourceSpatialReference:n,supportsRollbackOnFailure:i,supportsGeometryUpdate:v&&o,supportsUploadWithItemId:s},data:{supportsAttachment:d,supportsZ:a,supportsM:l,isVersioned:p},operations:{supportsQuery:g,supportsQueryAttachments:m,supportsEditing:u&&b,supportsAdd:v&&y&&b,supportsDelete:v&&f&&b,supportsUpdate:h&&b}}}get editingEnabled(){return this._isOverridden("editingEnabled")?this._get("editingEnabled"):this.userHasEditingPrivileges}set editingEnabled(e){this._overrideIfSome("editingEnabled",e)}get infoFor3D(){return(0,c.pC)(this.associatedLayer)?this.associatedLayer.infoFor3D:null}get defaultPopupTemplate(){return(0,c.pC)(this.associatedLayer)||this.attributeStorageInfo?this.createPopupTemplate():null}readObjectIdField(e,t){return!e&&t.fields&&t.fields.some((t=>("esriFieldTypeOID"===t.type&&(e=t.name),!!e))),e||void 0}readGlobalIdField(e,t){return!e&&t.fields&&t.fields.some((t=>("esriFieldTypeGlobalID"===t.type&&(e=t.name),!!e))),e||void 0}get displayField(){return(0,c.pC)(this.associatedLayer)?this.associatedLayer.displayField:null}readProfile(e,t){const r=t.store.profile;return null!=r&&ue[r]?ue[r]:(d.Z.getLogger(this.declaredClass).error("Unknown or missing profile",{profile:r,layer:this}),"mesh-pyramids")}load(e){const t=(0,c.pC)(e)?e.signal:null,r=this.loadFromPortal({supportedTypes:["Scene Service"]},e).catch(y.r9).then((()=>this._fetchService(t))).then((()=>Promise.all([this._fetchIndexAndUpdateExtent(this.nodePages,t),this._setAssociatedFeatureLayer(t),(0,c.pC)(this.filter)?this.filter.loadGeometries(this.spatialReference):null]))).then((()=>this._validateElevationInfo())).then((()=>this._applyAssociatedLayerOverrides())).then((()=>this._populateFieldUsageInfo())).then((()=>(0,re.y)(this,{origin:"service"},t))).then((()=>(0,Z.YN)(this.renderer,this.fieldsIndex))).then((()=>this.finishLoadEditablePortalLayer(e)));return this.addResolvingPromise(r),Promise.resolve(this)}async beforeSave(){(0,c.pC)(this.filter)&&await this.load()}createQuery(){const e=new ie.Z;return"mesh"!==this.geometryType&&(e.returnGeometry=!0,e.returnZ=!0),e.where=this.definitionExpression||"1=1",e.sqlFormat="standard",e.outFields=["*"],e}queryExtent(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryExtent(e||this.createQuery(),t)))}queryFeatureCount(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryFeatureCount(e||this.createQuery(),t)))}queryFeatures(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryFeatures(e||this.createQuery(),t))).then((e=>{if(e?.features)for(const t of e.features)t.layer=this,t.sourceLayer=this;return e}))}async queryCachedAttributes(e,t){const r=(0,Z.Lk)(this.fieldsIndex,await(0,ae.e)(this,(0,ae.V)(this)));return(0,ne.xe)(this.parsedUrl.path,this.attributeStorageInfo??[],e,t,r)}async queryCachedFeature(e,t){const r=await this.queryCachedAttributes(e,[t]);if(!r||0===r.length)throw new p.Z("scenelayer:feature-not-in-cached-data","Feature not found in cached data");const i=new s.Z;return i.attributes=r[0],i.layer=this,i.sourceLayer=this,i}queryObjectIds(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryObjectIds(e||this.createQuery(),t)))}queryAttachments(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryAttachments(e,t)))}getFieldUsageInfo(e){const t={supportsLabelingInfo:!1,supportsRenderer:!1,supportsPopupTemplate:!1,supportsLayerQuery:!1};return this.loaded?this._fieldUsageInfo[e]||t:(d.Z.getLogger(this.declaredClass).error("#getFieldUsageInfo()","Unavailable until layer is loaded"),t)}createPopupTemplate(e){return(0,oe.eZ)(this,e)}_getAssociatedLayerForQuery(){const e=this.associatedLayer;return(0,c.pC)(e)&&e.loaded?Promise.resolve(e):this._loadAssociatedLayerForQuery()}async _loadAssociatedLayerForQuery(){if(await this.load(),(0,c.Wi)(this.associatedLayer))throw new p.Z("scenelayer:query-not-available","SceneLayer queries are not available without an associated feature layer",{layer:this});try{await this.associatedLayer.load()}catch(e){throw new p.Z("scenelayer:query-not-available","SceneLayer associated feature layer could not be loaded",{layer:this,error:e})}return this.associatedLayer}hasCachedStatistics(e){return null!=this.statisticsInfo&&this.statisticsInfo.some((t=>t.name===e))}async queryCachedStatistics(e,t){if(await this.load(t),!this.statisticsInfo)throw new p.Z("scenelayer:no-cached-statistics","Cached statistics are not available for this layer");const r=this.fieldsIndex.get(e);if(!r)throw new p.Z("scenelayer:field-unexisting",`Field '${e}' does not exist on the layer`);for(const e of this.statisticsInfo)if(e.name===r.name){const r=(0,f.v_)(this.parsedUrl.path,e.href);return(0,a.default)(r,{query:{f:"json",token:this.apiKey},responseType:"json",signal:t?t.signal:null}).then((e=>e.data))}throw new p.Z("scenelayer:no-cached-statistics","Cached statistics for this attribute are not available")}async saveAs(e,t){return this._debouncedSaveOperations(x.xp.SAVE_AS,{...t,getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"scene"},e)}async save(){const e={getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"scene"};return this._debouncedSaveOperations(x.xp.SAVE,e)}async applyEdits(e,t){const i=await r.e(7541).then(r.bind(r,87269));if(await this.load(),(0,c.Wi)(this.associatedLayer))throw new p.Z(`${this.type}-layer:not-editable`,"Service is not editable");return await this.associatedLayer.load(),i.applyEdits(this,this.associatedLayer.source,e,t)}on(e,t){return super.on(e,t)}validateLayer(e){if(e.layerType&&!pe.includes(e.layerType))throw new p.Z("scenelayer:layer-type-not-supported","SceneLayer does not support this layer type",{layerType:e.layerType});if(isNaN(this.version.major)||isNaN(this.version.minor))throw new p.Z("layer:service-version-not-supported","Service version is not supported.",{serviceVersion:this.version.versionString,supportedVersions:"1.x, 2.x"});if(this.version.major>2)throw new p.Z("layer:service-version-too-new","Service version is too new.",{serviceVersion:this.version.versionString,supportedVersions:"1.x, 2.x"});!function(e,t){let r=!1,i=!1;if(null==e)r=!0,i=!0;else{const s=t&&t.isGeographic;switch(e){case"east-north-up":case"earth-centered":r=!0,i=s;break;case"vertex-reference-frame":r=!0,i=!s;break;default:r=!1}}if(!r)throw new p.Z("scenelayer:unsupported-normal-reference-frame","Normal reference frame is invalid.");if(!i)throw new p.Z("scenelayer:incompatible-normal-reference-frame","Normal reference frame is incompatible with layer spatial reference.")}(this.normalReferenceFrame,this.spatialReference)}_getTypeKeywords(){const e=[];if("points"===this.profile)e.push("Point");else{if("mesh-pyramids"!==this.profile)throw new p.Z("scenelayer:unknown-profile","SceneLayer:save() encountered an unknown SceneLayer profile: "+this.profile);e.push("3DObject")}return e}_populateFieldUsageInfo(){if(this._fieldUsageInfo={},this.fields)for(const e of this.fields){const t=!(!this.attributeStorageInfo||!this.attributeStorageInfo.some((t=>t.name===e.name))),r=!!((0,c.pC)(this.associatedLayer)&&this.associatedLayer.fields&&this.associatedLayer.fields.some((t=>t&&e.name===t.name))),i={supportsLabelingInfo:t,supportsRenderer:t,supportsPopupTemplate:t||r,supportsLayerQuery:r};this._fieldUsageInfo[e.name]=i}}_applyAssociatedLayerOverrides(){this._applyAssociatedLayerFieldsOverrides(),this._applyAssociatedLayerPopupOverrides()}_applyAssociatedLayerFieldsOverrides(){if((0,c.Wi)(this.associatedLayer)||!this.associatedLayer.fields)return;let e=null;for(const t of this.associatedLayer.fields){const r=this.getField(t.name);r?(!r.domain&&t.domain&&(r.domain=t.domain.clone()),r.editable=t.editable,r.nullable=t.nullable,r.length=t.length):(e||(e=this.fields?this.fields.slice():[]),e.push(t.clone()))}e&&this._set("fields",e)}_applyAssociatedLayerPopupOverrides(){if((0,c.Wi)(this.associatedLayer))return;const e=["popupTemplate","popupEnabled"],t=(0,m.vw)(this);for(let r=0;r<e.length;r++){const i=e[r],s=this.originIdOf(i),o=this.associatedLayer.originIdOf(i);s<o&&(o===w.s3.SERVICE||o===w.s3.PORTAL_ITEM)&&t.setAtOrigin(i,this.associatedLayer[i],o)}}async _setAssociatedFeatureLayer(e){if(!["mesh-pyramids","points"].includes(this.profile))return;const t=new T.W(this.parsedUrl,this.portalItem,this.apiKey,e);try{this.associatedLayer=await t.fetch()}catch(e){(0,y.D_)(e)||this._logWarningOnPopupEnabled()}}async _logWarningOnPopupEnabled(){await(0,h.N1)((()=>this.popupEnabled&&null!=this.popupTemplate));const e=`this SceneLayer: ${this.title}`;null==this.attributeStorageInfo?d.Z.getLogger(this.declaredClass).warn(`Associated FeatureLayer could not be loaded and no binary attributes found. Popups will not work on ${e}`):d.Z.getLogger(this.declaredClass).info(`Associated FeatureLayer could not be loaded. Falling back to binary attributes for Popups on ${e}`)}_validateElevationInfo(){const e=this.elevationInfo;e&&("mesh-pyramids"===this.profile&&"relative-to-scene"===e.mode&&d.Z.getLogger(this.declaredClass).warn(".elevationInfo=","Mesh scene layers don't support relative-to-scene elevation mode"),e.featureExpressionInfo&&"0"!==e.featureExpressionInfo.expression&&d.Z.getLogger(this.declaredClass).warn(".elevationInfo=","Scene layers do not support featureExpressionInfo"))}};(0,i._)([(0,g.Cb)({types:{key:"type",base:R.B,typeMap:{selection:E.Z}},json:{origins:{"web-scene":{name:"layerDefinition.featureReduction",write:!0},"portal-item":{name:"layerDefinition.featureReduction",write:!0}}}})],ce.prototype,"featureReduction",void 0),(0,i._)([(0,g.Cb)({type:[G],json:{read:!1,origins:{"web-scene":{name:"layerDefinition.rangeInfos",write:!0},"portal-item":{name:"layerDefinition.rangeInfos",write:!0}}}})],ce.prototype,"rangeInfos",void 0),(0,i._)([(0,g.Cb)({json:{read:!1}})],ce.prototype,"associatedLayer",void 0),(0,i._)([(0,g.Cb)({type:["show","hide"]})],ce.prototype,"listMode",void 0),(0,i._)([(0,g.Cb)({type:["ArcGISSceneServiceLayer"]})],ce.prototype,"operationalLayerType",void 0),(0,i._)([(0,g.Cb)({json:{read:!1},readOnly:!0})],ce.prototype,"type",void 0),(0,i._)([(0,g.Cb)({...de.fields,readOnly:!0,json:{read:!1,origins:{service:{read:!0}}}})],ce.prototype,"fields",void 0),(0,i._)([(0,g.Cb)()],ce.prototype,"types",null),(0,i._)([(0,g.Cb)()],ce.prototype,"typeIdField",null),(0,i._)([(0,g.Cb)()],ce.prototype,"formTemplate",null),(0,i._)([(0,g.Cb)({readOnly:!0})],ce.prototype,"fieldsIndex",null),(0,i._)([(0,g.Cb)({type:Q.Z,json:{read:{source:"layerDefinition.floorInfo"},write:{target:"layerDefinition.floorInfo"}}})],ce.prototype,"floorInfo",void 0),(0,i._)([(0,g.Cb)(de.outFields)],ce.prototype,"outFields",void 0),(0,i._)([(0,g.Cb)({type:D.U4,readOnly:!0,json:{read:!1}})],ce.prototype,"nodePages",void 0),(0,i._)([(0,b.r)("service","nodePages",["nodePages","pointNodePages"])],ce.prototype,"readNodePages",null),(0,i._)([(0,g.Cb)({type:[D.QI],readOnly:!0})],ce.prototype,"materialDefinitions",void 0),(0,i._)([(0,g.Cb)({type:[D.Yh],readOnly:!0})],ce.prototype,"textureSetDefinitions",void 0),(0,i._)([(0,g.Cb)({type:[D.H3],readOnly:!0})],ce.prototype,"geometryDefinitions",void 0),(0,i._)([(0,g.Cb)({readOnly:!0})],ce.prototype,"serviceUpdateTimeStamp",void 0),(0,i._)([(0,g.Cb)({readOnly:!0})],ce.prototype,"attributeStorageInfo",void 0),(0,i._)([(0,g.Cb)({readOnly:!0})],ce.prototype,"statisticsInfo",void 0),(0,i._)([(0,g.Cb)({type:l.Z.ofType(Number),nonNullable:!0,json:{origins:{service:{read:!1,write:!1}},name:"layerDefinition.excludeObjectIds",write:{enabled:!0}}})],ce.prototype,"excludeObjectIds",void 0),(0,i._)([(0,g.Cb)({type:String,json:{origins:{service:{read:!1,write:!1}},name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],ce.prototype,"definitionExpression",void 0),(0,i._)([(0,g.Cb)({type:te,json:{name:"layerDefinition.polygonFilter",write:{enabled:!0,allowNull:!0},origins:{service:{read:!1,write:!1}}}})],ce.prototype,"filter",void 0),(0,i._)([(0,g.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],ce.prototype,"path",void 0),(0,i._)([(0,g.Cb)(P.PV)],ce.prototype,"elevationInfo",null),(0,i._)([(0,g.Cb)({type:String})],ce.prototype,"geometryType",null),(0,i._)([(0,g.Cb)(P.iR)],ce.prototype,"labelsVisible",void 0),(0,i._)([(0,g.Cb)({type:[U.Z],json:{origins:{service:{name:"drawingInfo.labelingInfo",read:{reader:q.r},write:!1}},name:"layerDefinition.drawingInfo.labelingInfo",read:{reader:q.r},write:!0}})],ce.prototype,"labelingInfo",void 0),(0,i._)([(0,g.Cb)(P.rn)],ce.prototype,"legendEnabled",void 0),(0,i._)([(0,g.Cb)({type:Number,json:{origins:{"web-document":{default:1,write:{enabled:!0,target:{opacity:{type:Number},"layerDefinition.drawingInfo.transparency":{type:Number}}},read:{source:["opacity","layerDefinition.drawingInfo.transparency"],reader(e,t){if("number"==typeof e&&e>=0&&e<=1)return e;const r=t.layerDefinition?.drawingInfo?.transparency;return void 0!==r?(0,le.b)(r):void 0}}},"portal-item":{write:!0},service:{read:!1}}}})],ce.prototype,"opacity",void 0),(0,i._)([(0,g.Cb)({type:["Low","High"],readOnly:!0,json:{read:!1,origins:{service:{read:!0}}}})],ce.prototype,"priority",void 0),(0,i._)([(0,g.Cb)({type:["Labels"],readOnly:!0,json:{read:!1,origins:{service:{read:!0}}}})],ce.prototype,"semantic",void 0),(0,i._)([(0,g.Cb)({types:n.o,json:{origins:{service:{read:{source:"drawingInfo.renderer"}}},name:"layerDefinition.drawingInfo.renderer",write:!0},value:null})],ce.prototype,"renderer",null),(0,i._)([(0,g.Cb)({json:{read:!1}})],ce.prototype,"cachedDrawingInfo",void 0),(0,i._)([(0,b.r)("service","cachedDrawingInfo")],ce.prototype,"readCachedDrawingInfo",null),(0,i._)([(0,g.Cb)({readOnly:!0,json:{read:!1}})],ce.prototype,"capabilities",null),(0,i._)([(0,g.Cb)({type:Boolean,json:{read:!1}})],ce.prototype,"editingEnabled",null),(0,i._)([(0,g.Cb)({readOnly:!0,json:{write:!1,read:!1}})],ce.prototype,"infoFor3D",null),(0,i._)([(0,g.Cb)(P.C_)],ce.prototype,"popupEnabled",void 0),(0,i._)([(0,g.Cb)({type:o.Z,json:{name:"popupInfo",write:!0}})],ce.prototype,"popupTemplate",void 0),(0,i._)([(0,g.Cb)({readOnly:!0,json:{read:!1}})],ce.prototype,"defaultPopupTemplate",null),(0,i._)([(0,g.Cb)({type:String,json:{read:!1}})],ce.prototype,"objectIdField",void 0),(0,i._)([(0,b.r)("service","objectIdField",["objectIdField","fields"])],ce.prototype,"readObjectIdField",null),(0,i._)([(0,g.Cb)({type:String,json:{read:!1}})],ce.prototype,"globalIdField",void 0),(0,i._)([(0,b.r)("service","globalIdField",["globalIdField","fields"])],ce.prototype,"readGlobalIdField",null),(0,i._)([(0,g.Cb)({readOnly:!0,type:String,json:{read:!1}})],ce.prototype,"displayField",null),(0,i._)([(0,g.Cb)({type:String,json:{read:!1}})],ce.prototype,"profile",void 0),(0,i._)([(0,b.r)("service","profile",["store.profile"])],ce.prototype,"readProfile",null),(0,i._)([(0,g.Cb)({readOnly:!0,type:String,json:{origins:{service:{read:{source:"store.normalReferenceFrame"}}},read:!1}})],ce.prototype,"normalReferenceFrame",void 0),(0,i._)([(0,g.Cb)(P.YI)],ce.prototype,"screenSizePerspectiveEnabled",void 0),ce=(0,i._)([(0,v.j)("esri.layers.SceneLayer")],ce);const ue={"mesh-pyramids":"mesh-pyramids",meshpyramids:"mesh-pyramids","features-meshes":"mesh-pyramids",points:"points","features-points":"points",lines:"lines","features-lines":"lines",polygons:"polygons","features-polygons":"polygons"},ye={"mesh-pyramids":"mesh",points:"point",lines:"polyline",polygons:"polygon"},he=ce},66094:(e,t,r)=>{r.d(t,{B:()=>s});var i=r(17452);function s(e){return o[function(e){return e instanceof Blob?e.type:function(e){const t=(0,i.Ml)(e);return l[t]||n}(e.url)}(e)]||a}const o={},n="text/plain",a=o[n],l={png:"image/png",jpeg:"image/jpeg",jpg:"image/jpg",bmp:"image/bmp",gif:"image/gif",json:"application/json",txt:"text/plain",xml:"application/xml",svg:"image/svg+xml",zip:"application/zip",pbf:"application/vnd.mapbox-vector-tile",gz:"application/gzip","bin.gz":"application/octet-stream"};for(const e in l)o[l[e]]=e}}]);