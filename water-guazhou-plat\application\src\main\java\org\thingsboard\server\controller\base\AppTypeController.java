package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.app.AppTypeService;
import org.thingsboard.server.dao.model.sql.AppType;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.List;

@RestController
@RequestMapping("api/app/type")
public class AppTypeController {

    @Autowired
    private AppTypeService appTypeService;

    @GetMapping("{id}")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN')")
    @SysLog(detail = DataConstants.OPERATING_TYPE_APP_TYPE_GET)
    public AppType findById(@PathVariable String id) {
        return appTypeService.findById(id);
    }

    @GetMapping("list")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN')")
    @SysLog(detail = DataConstants.OPERATING_TYPE_APP_TYPE_GET_LIST)
    public List<AppType> findAllList() {
        return appTypeService.findList();
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN')")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_APP_TYPE_ADD)
    public AppType save(@RequestBody AppType appType) {
        return appTypeService.save(appType);
    }

    @PostMapping("edit")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN')")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_APP_TYPE_EDIT)
    public AppType update(@RequestBody AppType appType) {
        return appTypeService.update(appType);
    }

    @DeleteMapping("{id}")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN')")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_APP_TYPE_DELETE)
    public AppType delete(@PathVariable String id) throws ThingsboardException {
        return appTypeService.deleteById(id);
    }

}
