import{_ as P}from"./TreeBox-DDD2iwoR.js";import{_ as $}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as V}from"./CardSearch-CB_HNR-Q.js";import{_ as j}from"./index-BJ-QPYom.js";import{d as q,M as Y,c as D,s as A,r as g,x as n,a8 as x,a9 as T,bN as C,S as B,o as H,g as O,h as W,F as S,q as _,i as I,b7 as U}from"./index-r0dFAfgr.js";import{I as o}from"./common-CvK_P_ao.js";import{p as z,d as G,g as K,a as Q}from"./device-DWHb0XjG.js";import{f as J}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ce=q({__name:"category",setup(X){const{$btnPerms:y}=Y(),p=D(),b=D(),E=D({filters:[{label:"类别编码",field:"serialId",type:"input"},{label:"类别名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新建顶级类别",icon:o.ADD,type:"success",click:()=>N("新建顶级类别")},{type:"default",perm:!0,text:"重置",svgIcon:A(U),click:()=>{var e;(e=b.value)==null||e.resetForm(),v()}},{perm:!0,text:"查询",icon:o.QUERY,click:()=>v(!0)}]}]}),u=g({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"类别编码",prop:"serialId",formatter:e=>e.serialId.replace(/(\d{2})(\d{3})(\d{3})(\d{6})/,"$1-$2-$3-$4")},{label:"类别名称(层级)",prop:"treePath"},{label:"节点级别",prop:"level"},{label:"排序编号",prop:"orderNum"},{label:"备注",prop:"remark"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>J(e.createTime,"YYYY-MM-DD HH:mm:ss")}],operationWidth:"240px",operations:[{disabled:e=>e.level===3,type:"success",text:"新建子集",icon:o.ADD,perm:y("RoleManageAdd"),click:e=>N("新建子集",e)},{disabled:e=>e.level===0,type:"primary",text:"编辑",icon:o.EDIT,perm:y("RoleManageEdit"),click:e=>L(e)},{disabled:e=>e.level===0,type:"danger",text:"删除",perm:y("RoleManageDelete"),icon:o.DELETE,click:e=>F(e)}],dataList:[],pagination:{hide:!0}}),d=g({title:"新增",dialogWidth:"500px",labelWidth:"100px",submit:e=>{const a=e.serialId;if(e.serialId=r.prepend+e.serialIdNum+r.append,a!==e.serialId&&M(e))return;let t="添加成功";e.id&&(t="编辑成功"),z(e).then(()=>{var l;n.success(t),(l=p.value)==null||l.closeDialog(),h()}).catch(l=>{n.warning(l)})},defaultValue:{},group:[{fields:[{type:"select-tree",label:"所属类别",field:"category",readonly:!0,checkStrictly:!0,options:x(()=>T(i.data))},{type:"input",label:"类别编码",field:"serialIdNum",rules:[{required:!0,message:"请输入类别编码"}],prepend:x(()=>r.prepend),append:x(()=>r.append)},{type:"hint",text:"注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)"},{type:"input",label:"类别名称",field:"name",rules:[{required:!0,message:"请输入类别名称"}]},{type:"input",label:"节点级别",field:"level",disabled:!0},{type:"input-number",label:"排序编号",field:"sortNum",rules:[{required:!0,message:"请输入排序编号"}]},{type:"textarea",label:"备注/说明",field:"remark"}]}]});function M(e){if(e.serialId.length!==14)return n.warning("类别编码格式错误"),!0;if(C(i.data||[],"children","serialId").includes(e.serialId))return n.warning("类别编码重复"),!0}function k(e,a){const t=C(e.children||[e]," ","serialId")||[];let l=Math.max(...t);return a===1&&(l+=1e12),a===2&&(l+=1e9),a===3&&(l+=1e6),(l+"").length===13?"0"+l:l+""}const i=g({title:"设备类别",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{i.currentProject=e,v()}}),N=(e,a)=>{var f,m;if(e==="新建顶级类别"&&!i.data.length){n.warning("请等待结构加载完成");return}d.title=e;let t=1;const l=k(i.data[0],t);let s="";if(t===1&&(r.prepend="",r.append=l.slice(2,14),s=l.slice(0,2)),d.defaultValue={category:((f=i.data[0])==null?void 0:f.id)||"",level:"1",serialIdNum:s},e==="新建子集"){t=a.level*1+1;const c=k(a,t);if(t===2&&(r.prepend=c.slice(0,2),r.append=c.slice(5,14),s=c.slice(2,5)),t===3&&(r.prepend=c.slice(0,5),r.append=c.slice(8,14),s=c.slice(5,8)),t>3){n.warning("最多三级");return}d.defaultValue={parentId:a.id,category:a.id,level:t,serialIdNum:s}}(m=p.value)==null||m.openDialog()},L=e=>{var t;d.title="编辑";const a=e.level;a===1&&(r.prepend="",r.append=e.serialId.slice(2,14),e.serialIdNum=e.serialId.slice(0,2)),a===2&&(r.prepend=e.serialId.slice(0,2),r.append=e.serialId.slice(5,14),e.serialIdNum=e.serialId.slice(2,5)),a===3&&(r.prepend=e.serialId.slice(0,5),r.append=e.serialId.slice(8,14),e.serialIdNum=e.serialId.slice(5,8)),d.defaultValue={category:e.parentId,sortNum:e.orderNum,...e||{}},(t=p.value)==null||t.openDialog()},F=e=>{B("确定删除该设备类别","删除提示").then(()=>{G(e.id).then(()=>{n.success("删除成功"),h()}).catch(a=>{n.error(a.toString())})})};function h(){K().then(e=>{i.data=T(e.data.data||[]),i.currentProject=e.data.data[0],u.dataList=i.data})}const r=g({prepend:"",append:""}),v=async e=>{var a;if(e){const t={page:-1,size:20,...((a=b.value)==null?void 0:a.queryParams)||{}};t.serialId&&(t.serialId=t.serialId.replace(/-/g,"")),Q(t).then(l=>{u.dataList=l.data.data.data})}else u.dataList=[i.currentProject]};return H(async()=>{h()}),(e,a)=>{const t=j,l=V,s=R,f=$,m=P;return O(),W(m,null,{tree:S(()=>[_(t,{"tree-data":I(i)},null,8,["tree-data"])]),default:S(()=>[_(l,{ref_key:"refSearch",ref:b,config:I(E)},null,8,["config"]),_(s,{class:"card-table",config:I(u)},null,8,["config"]),_(f,{ref_key:"refForm",ref:p,config:I(d)},null,8,["config"])]),_:1})}}});export{ce as default};
