/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data;

public class CacheConstants {
    public static final String DEVICE_CREDENTIALS_CACHE = "deviceCredentials";
    public static final String RELATIONS_CACHE = "relations";
    public static final String DEVICE_CACHE = "devices";
    public static final String SESSIONS_CACHE = "sessions";
    public static final String ASSET_CACHE = "assets";
    public static final String ENTITY_VIEW_CACHE = "entityViews";
    public static final String ATTRIBUTE_CACHE = "attribute";
    public static final String ALARM_JSON_CACHE = "alarmJson";
    public static final String ALARM_CACHE = "alarm";
    public static final String TS_KV_LAST = "lastData";
}
