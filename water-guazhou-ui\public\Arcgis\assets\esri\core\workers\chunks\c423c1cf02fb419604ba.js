"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1932],{95401:(e,t,n)=>{n.d(t,{e:()=>s});var r,o,i,s={},a={get exports(){return s},set exports(e){s=e}};r=a,o=function(){function e(e,n,o){o=o||2;var i,s,a,c,u,p,h,f=n&&n.length,g=f?n[0]*o:e.length,x=t(e,0,g,o,!0),d=[];if(!x||x.next===x.prev)return d;if(f&&(x=l(e,n,x,o)),e.length>80*o){i=a=e[0],s=c=e[1];for(var m=o;m<g;m+=o)(u=e[m])<i&&(i=u),(p=e[m+1])<s&&(s=p),u>a&&(a=u),p>c&&(c=p);h=0!==(h=Math.max(a-i,c-s))?1/h:0}return r(x,d,o,i,s,h),d}function t(e,t,n,r,o){var i,s;if(o===R(e,t,n,r)>0)for(i=t;i<n;i+=r)s=Z(i,e[i],e[i+1],s);else for(i=n-r;i>=t;i-=r)s=Z(i,e[i],e[i+1],s);if(s&&v(s,s.next)){var a=s.next;_(s),s=a}return s}function n(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!v(r,r.next)&&0!==y(r.prev,r,r.next))r=r.next;else{var o=r.prev;if(_(r),(r=t=o)===r.next)break;n=!0}}while(n||r!==t);return t}function r(e,t,l,c,u,p,h){if(e){!h&&p&&f(e,c,u,p);for(var g,x,d=e;e.prev!==e.next;)if(g=e.prev,x=e.next,p?i(e,c,u,p):o(e))t.push(g.i/l),t.push(e.i/l),t.push(x.i/l),_(e),e=x.next,d=x.next;else if((e=x)===d){h?1===h?r(e=s(n(e),t,l),t,l,c,u,p,2):2===h&&a(e,t,l,c,u,p):r(n(e),t,l,c,u,p,1);break}}}function o(e){var t=e.prev,n=e,r=e.next;if(y(t,n,r)>=0)return!1;for(var o=e.next.next;o!==e.prev;){if(d(t.x,t.y,n.x,n.y,r.x,r.y,o.x,o.y)&&y(o.prev,o,o.next)>=0)return!1;o=o.next}return!0}function i(e,t,n,r){var o=e.prev,i=e,s=e.next;if(y(o,i,s)>=0)return!1;for(var a=o.x<i.x?o.x<s.x?o.x:s.x:i.x<s.x?i.x:s.x,l=o.y<i.y?o.y<s.y?o.y:s.y:i.y<s.y?i.y:s.y,c=o.x>i.x?o.x>s.x?o.x:s.x:i.x>s.x?i.x:s.x,u=o.y>i.y?o.y>s.y?o.y:s.y:i.y>s.y?i.y:s.y,p=g(a,l,t,n,r),h=g(c,u,t,n,r),f=e.prevZ,x=e.nextZ;f&&f.z>=p&&x&&x.z<=h;){if(f!==e.prev&&f!==e.next&&d(o.x,o.y,i.x,i.y,s.x,s.y,f.x,f.y)&&y(f.prev,f,f.next)>=0)return!1;if(f=f.prevZ,x!==e.prev&&x!==e.next&&d(o.x,o.y,i.x,i.y,s.x,s.y,x.x,x.y)&&y(x.prev,x,x.next)>=0)return!1;x=x.nextZ}for(;f&&f.z>=p;){if(f!==e.prev&&f!==e.next&&d(o.x,o.y,i.x,i.y,s.x,s.y,f.x,f.y)&&y(f.prev,f,f.next)>=0)return!1;f=f.prevZ}for(;x&&x.z<=h;){if(x!==e.prev&&x!==e.next&&d(o.x,o.y,i.x,i.y,s.x,s.y,x.x,x.y)&&y(x.prev,x,x.next)>=0)return!1;x=x.nextZ}return!0}function s(e,t,r){var o=e;do{var i=o.prev,s=o.next.next;!v(i,s)&&w(i,o,o.next,s)&&A(i,s)&&A(s,i)&&(t.push(i.i/r),t.push(o.i/r),t.push(s.i/r),_(o),_(o.next),o=e=s),o=o.next}while(o!==e);return n(o)}function a(e,t,o,i,s,a){var l=e;do{for(var c=l.next.next;c!==l.prev;){if(l.i!==c.i&&m(l,c)){var u=T(l,c);return l=n(l,l.next),u=n(u,u.next),r(l,t,o,i,s,a),void r(u,t,o,i,s,a)}c=c.next}l=l.next}while(l!==e)}function l(e,r,o,i){var s,a,l,u=[];for(s=0,a=r.length;s<a;s++)(l=t(e,r[s]*i,s<a-1?r[s+1]*i:e.length,i,!1))===l.next&&(l.steiner=!0),u.push(x(l));for(u.sort(c),s=0;s<u.length;s++)o=n(o=p(u[s],o),o.next);return o}function c(e,t){return e.x-t.x}function u(e){if(e.next.prev===e)return e;let t=e;for(;;){const n=t.next;if(n.prev===t||n===t||n===e)break;t=n}return t}function p(e,t){var r=function(e,t){var n,r=t,o=e.x,i=e.y,s=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var a=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(a<=o&&a>s){if(s=a,a===o){if(i===r.y)return r;if(i===r.next.y)return r.next}n=r.x<r.next.x?r:r.next}}r=r.next}while(r!==t);if(!n)return null;if(o===s)return n;var l,c=n,u=n.x,p=n.y,f=1/0;r=n;do{o>=r.x&&r.x>=u&&o!==r.x&&d(i<p?o:s,i,u,p,i<p?s:o,i,r.x,r.y)&&(l=Math.abs(i-r.y)/(o-r.x),A(r,e)&&(l<f||l===f&&(r.x>n.x||r.x===n.x&&h(n,r)))&&(n=r,f=l)),r=r.next}while(r!==c);return n}(e,t);if(!r)return t;var o=T(r,e),i=n(r,r.next);let s=u(o);return n(s,s.next),i=u(i),u(t===r?i:t)}function h(e,t){return y(e.prev,e,t.prev)<0&&y(t.next,e,e.next)<0}function f(e,t,n,r){var o=e;do{null===o.z&&(o.z=g(o.x,o.y,t,n,r)),o.prevZ=o.prev,o.nextZ=o.next,o=o.next}while(o!==e);o.prevZ.nextZ=null,o.prevZ=null,function(e){var t,n,r,o,i,s,a,l,c=1;do{for(n=e,e=null,i=null,s=0;n;){for(s++,r=n,a=0,t=0;t<c&&(a++,r=r.nextZ);t++);for(l=c;a>0||l>0&&r;)0!==a&&(0===l||!r||n.z<=r.z)?(o=n,n=n.nextZ,a--):(o=r,r=r.nextZ,l--),i?i.nextZ=o:e=o,o.prevZ=i,i=o;n=r}i.nextZ=null,c*=2}while(s>1)}(o)}function g(e,t,n,r,o){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=32767*(e-n)*o)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-r)*o)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function x(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function d(e,t,n,r,o,i,s,a){return(o-s)*(t-a)-(e-s)*(i-a)>=0&&(e-s)*(r-a)-(n-s)*(t-a)>=0&&(n-s)*(i-a)-(o-s)*(r-a)>=0}function m(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&w(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(A(e,t)&&A(t,e)&&function(e,t){var n=e,r=!1,o=(e.x+t.x)/2,i=(e.y+t.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&o<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(y(e.prev,e,t.prev)||y(e,t.prev,t))||v(e,t)&&y(e.prev,e,e.next)>0&&y(t.prev,t,t.next)>0)}function y(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function v(e,t){return e.x===t.x&&e.y===t.y}function w(e,t,n,r){var o=C(y(e,t,n)),i=C(y(e,t,r)),s=C(y(n,r,e)),a=C(y(n,r,t));return o!==i&&s!==a||!(0!==o||!b(e,n,t))||!(0!==i||!b(e,r,t))||!(0!==s||!b(n,e,r))||!(0!==a||!b(n,t,r))}function b(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function C(e){return e>0?1:e<0?-1:0}function A(e,t){return y(e.prev,e,e.next)<0?y(e,t,e.next)>=0&&y(e,e.prev,t)>=0:y(e,t,e.prev)<0||y(e,e.next,t)<0}function T(e,t){var n=new M(e.i,e.x,e.y),r=new M(t.i,t.x,t.y),o=e.next,i=t.prev;return e.next=t,t.prev=e,n.next=o,o.prev=n,r.next=n,n.prev=r,i.next=r,r.prev=i,r}function Z(e,t,n,r){var o=new M(e,t,n);return r?(o.next=r.next,o.prev=r,r.next.prev=o,r.next=o):(o.prev=o,o.next=o),o}function _(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function M(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}function R(e,t,n,r){for(var o=0,i=t,s=n-r;i<n;i+=r)o+=(e[s]-e[i])*(e[i+1]+e[s+1]),s=i;return o}return e.deviation=function(e,t,n,r){var o=t&&t.length,i=o?t[0]*n:e.length,s=Math.abs(R(e,0,i,n));if(o)for(var a=0,l=t.length;a<l;a++){var c=t[a]*n,u=a<l-1?t[a+1]*n:e.length;s-=Math.abs(R(e,c,u,n))}var p=0;for(a=0;a<r.length;a+=3){var h=r[a]*n,f=r[a+1]*n,g=r[a+2]*n;p+=Math.abs((e[h]-e[g])*(e[f+1]-e[h+1])-(e[h]-e[f])*(e[g+1]-e[h+1]))}return 0===s&&0===p?0:Math.abs((p-s)/s)},e.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,o=0;o<e.length;o++){for(var i=0;i<e[o].length;i++)for(var s=0;s<t;s++)n.vertices.push(e[o][i][s]);o>0&&(r+=e[o-1].length,n.holes.push(r))}return n},e},void 0!==(i=o())&&(r.exports=i)},62540:(e,t,n)=>{n.d(t,{Z:()=>y});var r,o=n(43697),i=n(96674),s=n(22974),a=n(92604),l=n(5600),c=n(90578),u=n(52011),p=n(75215),h=n(65542),f=n(60746),g=n(71630);const x="esri.geometry.support.MeshComponent",d=a.Z.getLogger(x);let m=r=class extends i.wq{static from(e){return(0,p.TJ)(r,e)}constructor(e){super(e),this.faces=null,this.material=null,this.shading="source",this.trustSourceNormals=!1}castFaces(e){return(0,g.X)(e,Uint32Array,[Uint16Array],{loggerTag:".faces=",stride:3},d)}castMaterial(e){return(0,p.TJ)(e&&"object"==typeof e&&("metallic"in e||"roughness"in e||"metallicRoughnessTexture"in e)?f.Z:h.Z,e)}clone(){return new r({faces:(0,s.d9)(this.faces),shading:this.shading,material:(0,s.d9)(this.material),trustSourceNormals:this.trustSourceNormals})}cloneWithDeduplication(e,t){const n={faces:(0,s.d9)(this.faces),shading:this.shading,material:this.material?this.material.cloneWithDeduplication(e,t):null,trustSourceNormals:this.trustSourceNormals};return new r(n)}};(0,o._)([(0,l.Cb)({json:{write:!0}})],m.prototype,"faces",void 0),(0,o._)([(0,c.p)("faces")],m.prototype,"castFaces",null),(0,o._)([(0,l.Cb)({type:h.Z,json:{write:!0}})],m.prototype,"material",void 0),(0,o._)([(0,c.p)("material")],m.prototype,"castMaterial",null),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],m.prototype,"shading",void 0),(0,o._)([(0,l.Cb)({type:Boolean})],m.prototype,"trustSourceNormals",void 0),m=r=(0,o._)([(0,u.j)(x)],m);const y=m},65542:(e,t,n)=>{n.d(t,{Z:()=>h});var r,o=n(43697),i=n(22303),s=n(96674),a=n(70586),l=n(5600),c=(n(75215),n(67676),n(52011)),u=n(91024);let p=r=class extends s.wq{constructor(e){super(e),this.color=null,this.colorTexture=null,this.normalTexture=null,this.alphaMode="auto",this.alphaCutoff=.5,this.doubleSided=!0}clone(){return this.cloneWithDeduplication(null,new Map)}cloneWithDeduplication(e,t){const n=(0,a.pC)(e)?e.get(this):null;if(n)return n;const o=new r(this.clonePropertiesWithDeduplication(t));return(0,a.pC)(e)&&e.set(this,o),o}clonePropertiesWithDeduplication(e){return{color:(0,a.pC)(this.color)?this.color.clone():null,colorTexture:(0,a.pC)(this.colorTexture)?this.colorTexture.cloneWithDeduplication(e):null,normalTexture:(0,a.pC)(this.normalTexture)?this.normalTexture.cloneWithDeduplication(e):null,alphaMode:this.alphaMode,alphaCutoff:this.alphaCutoff,doubleSided:this.doubleSided,colorTextureTransform:(0,a.pC)(this.colorTextureTransform)?this.colorTextureTransform:null,normalTextureTransform:(0,a.pC)(this.normalTextureTransform)?this.normalTextureTransform:null}}};(0,o._)([(0,l.Cb)({type:i.Z,json:{write:!0}})],p.prototype,"color",void 0),(0,o._)([(0,l.Cb)({type:u.Z,json:{write:!0}})],p.prototype,"colorTexture",void 0),(0,o._)([(0,l.Cb)({type:u.Z,json:{write:!0}})],p.prototype,"normalTexture",void 0),(0,o._)([(0,l.Cb)({nonNullable:!0,json:{write:!0}})],p.prototype,"alphaMode",void 0),(0,o._)([(0,l.Cb)({nonNullable:!0,json:{write:!0}})],p.prototype,"alphaCutoff",void 0),(0,o._)([(0,l.Cb)({nonNullable:!0,json:{write:!0}})],p.prototype,"doubleSided",void 0),(0,o._)([(0,l.Cb)()],p.prototype,"colorTextureTransform",void 0),(0,o._)([(0,l.Cb)()],p.prototype,"normalTextureTransform",void 0),p=r=(0,o._)([(0,c.j)("esri.geometry.support.MeshMaterial")],p);const h=p},60746:(e,t,n)=>{n.d(t,{Z:()=>h});var r,o=n(43697),i=n(22303),s=n(70586),a=n(5600),l=(n(75215),n(67676),n(52011)),c=n(65542),u=n(91024);let p=r=class extends c.Z{constructor(e){super(e),this.emissiveColor=null,this.emissiveTexture=null,this.occlusionTexture=null,this.metallic=1,this.roughness=1,this.metallicRoughnessTexture=null}clone(){return this.cloneWithDeduplication(null,new Map)}cloneWithDeduplication(e,t){const n=(0,s.pC)(e)?e.get(this):null;if(n)return n;const o=new r(this.clonePropertiesWithDeduplication(t));return(0,s.pC)(e)&&e.set(this,o),o}clonePropertiesWithDeduplication(e){return{...super.clonePropertiesWithDeduplication(e),emissiveColor:(0,s.pC)(this.emissiveColor)?this.emissiveColor.clone():null,emissiveTexture:(0,s.pC)(this.emissiveTexture)?this.emissiveTexture.cloneWithDeduplication(e):null,occlusionTexture:(0,s.pC)(this.occlusionTexture)?this.occlusionTexture.cloneWithDeduplication(e):null,metallic:this.metallic,roughness:this.roughness,metallicRoughnessTexture:(0,s.pC)(this.metallicRoughnessTexture)?this.metallicRoughnessTexture.cloneWithDeduplication(e):null,occlusionTextureTransform:(0,s.pC)(this.occlusionTextureTransform)?this.occlusionTextureTransform:null,emissiveTextureTransform:(0,s.pC)(this.emissiveTextureTransform)?this.emissiveTextureTransform:null,metallicRoughnessTextureTransform:(0,s.pC)(this.metallicRoughnessTextureTransform)?this.metallicRoughnessTextureTransform:null}}};(0,o._)([(0,a.Cb)({type:i.Z,json:{write:!0}})],p.prototype,"emissiveColor",void 0),(0,o._)([(0,a.Cb)({type:u.Z,json:{write:!0}})],p.prototype,"emissiveTexture",void 0),(0,o._)([(0,a.Cb)({type:u.Z,json:{write:!0}})],p.prototype,"occlusionTexture",void 0),(0,o._)([(0,a.Cb)({type:Number,nonNullable:!0,json:{write:!0},range:{min:0,max:1}})],p.prototype,"metallic",void 0),(0,o._)([(0,a.Cb)({type:Number,nonNullable:!0,json:{write:!0},range:{min:0,max:1}})],p.prototype,"roughness",void 0),(0,o._)([(0,a.Cb)({type:u.Z,json:{write:!0}})],p.prototype,"metallicRoughnessTexture",void 0),(0,o._)([(0,a.Cb)()],p.prototype,"occlusionTextureTransform",void 0),(0,o._)([(0,a.Cb)()],p.prototype,"emissiveTextureTransform",void 0),(0,o._)([(0,a.Cb)()],p.prototype,"metallicRoughnessTextureTransform",void 0),p=r=(0,o._)([(0,l.j)("esri.geometry.support.MeshMaterialMetallicRoughness")],p);const h=p},91024:(e,t,n)=>{n.d(t,{Z:()=>v});var r=n(43697);n(80442);let o=null,i=!0;function s(){return o||(o=document.createElement("canvas"),o.width=1,o.height=1),o}function a(e,t,n){return n||(n=s()),n.getContext("2d").createImageData(e,t)}var l,c=n(96674),u=n(5600),p=n(75215),h=(n(67676),n(71715)),f=n(52011),g=n(30556),x=n(25929);const d=new WeakMap;let m=0,y=l=class extends c.wq{constructor(e){super(e),this.wrap="repeat"}get url(){return this._get("url")||null}set url(e){this._set("url",e),e&&this._set("data",null)}get data(){return this._get("data")||null}set data(e){this._set("data",e),e&&this._set("url",null)}writeData(e,t,n,r){if(e instanceof HTMLImageElement){const o={type:"image-element",src:(0,x.t)(e.src,r),crossOrigin:e.crossOrigin};t[n]=o}else if(e instanceof HTMLCanvasElement){const r=e.getContext("2d").getImageData(0,0,e.width,e.height),o={type:"canvas-element",imageData:this._encodeImageData(r)};t[n]=o}else if(e instanceof HTMLVideoElement){const o={type:"video-element",src:(0,x.t)(e.src,r),autoplay:e.autoplay,loop:e.loop,muted:e.muted,crossOrigin:e.crossOrigin,preload:e.preload};t[n]=o}else if(e instanceof ImageData){const r={type:"image-data",imageData:this._encodeImageData(e)};t[n]=r}}readData(e){switch(e.type){case"image-element":{const t=new Image;return t.src=e.src,t.crossOrigin=e.crossOrigin,t}case"canvas-element":{const t=this._decodeImageData(e.imageData),n=document.createElement("canvas");return n.width=t.width,n.height=t.height,n.getContext("2d").putImageData(t,0,0),n}case"image-data":return this._decodeImageData(e.imageData);case"video-element":{const t=document.createElement("video");return t.src=e.src,t.crossOrigin=e.crossOrigin,t.autoplay=e.autoplay,t.loop=e.loop,t.muted=e.muted,t.preload=e.preload,t}default:return}}get transparent(){const e=this.data,t=this.url;if(e instanceof HTMLCanvasElement)return this._imageDataContainsTransparent(e.getContext("2d").getImageData(0,0,e.width,e.height));if(e instanceof ImageData)return this._imageDataContainsTransparent(e);if(t){const e=t.substr(t.length-4,4).toLowerCase(),n=t.substr(0,15).toLocaleLowerCase();if(".png"===e||"data:image/png;"===n)return!0}return!1}set transparent(e){this._overrideIfSome("transparent",e)}get contentHash(){const e="string"==typeof this.wrap?this.wrap:"object"==typeof this.wrap?`${this.wrap.horizontal}/${this.wrap.vertical}`:"",t=(t="")=>`d:${t},t:${this.transparent},w:${e}`;return null!=this.url?t(this.url):null!=this.data?this.data instanceof HTMLImageElement||this.data instanceof HTMLVideoElement?t(this.data.src):(d.has(this.data)||d.set(this.data,++m),t(d.get(this.data))):t()}clone(){const e={url:this.url,data:this.data,wrap:this._cloneWrap()};return new l(e)}cloneWithDeduplication(e){const t=e.get(this);if(t)return t;const n=this.clone();return e.set(this,n),n}_cloneWrap(){return"string"==typeof this.wrap?this.wrap:{horizontal:this.wrap.horizontal,vertical:this.wrap.vertical}}_encodeImageData(e){let t="";for(let n=0;n<e.data.length;n++)t+=String.fromCharCode(e.data[n]);return{data:btoa(t),width:e.width,height:e.height}}_decodeImageData(e){const t=atob(e.data),n=new Uint8ClampedArray(t.length);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);return function(e,t,n,r){if(!t||!n)throw new Error("Cannot construct image data without dimensions");if(i)try{return new ImageData(e,t,n)}catch(e){i=!1}const o=a(t,n,void 0);return o.data.set(e,0),o}(n,e.width,e.height)}_imageDataContainsTransparent(e){for(let t=3;t<e.data.length;t+=4)if(255!==e.data[t])return!0;return!1}static from(e){return"string"==typeof e?new l({url:e}):e instanceof HTMLImageElement||e instanceof HTMLCanvasElement||e instanceof ImageData||e instanceof HTMLVideoElement?new l({data:e}):(0,p.TJ)(l,e)}};(0,r._)([(0,u.Cb)({type:String,json:{write:x.w}})],y.prototype,"url",null),(0,r._)([(0,u.Cb)({json:{write:{overridePolicy(){return{enabled:!this.url}}}}}),(0,u.Cb)()],y.prototype,"data",null),(0,r._)([(0,g.c)("data")],y.prototype,"writeData",null),(0,r._)([(0,h.r)("data")],y.prototype,"readData",null),(0,r._)([(0,u.Cb)({type:Boolean,json:{write:{overridePolicy(){return{enabled:this._isOverridden("transparent")}}}}})],y.prototype,"transparent",null),(0,r._)([(0,u.Cb)({json:{write:!0}})],y.prototype,"wrap",void 0),(0,r._)([(0,u.Cb)({readOnly:!0})],y.prototype,"contentHash",null),y=l=(0,r._)([(0,f.j)("esri.geometry.support.MeshTexture")],y);const v=y},71630:(e,t,n)=>{n.d(t,{Q:()=>f,X:()=>x});var r,o=n(43697),i=n(96674),s=n(22974),a=n(92604),l=n(5600),c=n(90578),u=n(52011);const p="esri.geometry.support.MeshVertexAttributes",h=a.Z.getLogger(p);let f=r=class extends i.wq{constructor(e){super(e),this.color=null,this.position=new Float64Array(0),this.uv=null,this.normal=null,this.tangent=null}castColor(e){return x(e,Uint8Array,[Uint8ClampedArray],{loggerTag:".color=",stride:4},h)}castPosition(e){return e&&e instanceof Float32Array&&h.warn(".position=","Setting position attribute from a Float32Array may cause precision problems. Consider storing data in a Float64Array or a regular number array"),x(e,Float64Array,[Float32Array],{loggerTag:".position=",stride:3},h)}castUv(e){return x(e,Float32Array,[Float64Array],{loggerTag:".uv=",stride:2},h)}castNormal(e){return x(e,Float32Array,[Float64Array],{loggerTag:".normal=",stride:3},h)}castTangent(e){return x(e,Float32Array,[Float64Array],{loggerTag:".tangent=",stride:4},h)}clone(){const e={position:(0,s.d9)(this.position),uv:(0,s.d9)(this.uv),normal:(0,s.d9)(this.normal),tangent:(0,s.d9)(this.tangent),color:(0,s.d9)(this.color)};return new r(e)}clonePositional(){const e={position:(0,s.d9)(this.position),normal:(0,s.d9)(this.normal),tangent:(0,s.d9)(this.tangent),uv:this.uv,color:this.color};return new r(e)}};function g(e,t,n,r){const{loggerTag:o,stride:i}=t;return e.length%i!=0?(r.error(o,`Invalid array length, expected a multiple of ${i}`),new n([])):e}function x(e,t,n,r,o){if(!e)return e;if(e instanceof t)return g(e,r,t,o);for(const i of n)if(e instanceof i)return g(new t(e),r,t,o);if(Array.isArray(e))return g(new t(e),r,t,o);{const r=n.map((e=>`'${e.name}'`));return o.error(`Failed to set property, expected one of ${r}, but got ${e.constructor.name}`),new t([])}}function d(e,t,n){t[n]=function(e){const t=new Array(e.length);for(let n=0;n<e.length;n++)t[n]=e[n];return t}(e)}(0,o._)([(0,l.Cb)({json:{write:d}})],f.prototype,"color",void 0),(0,o._)([(0,c.p)("color")],f.prototype,"castColor",null),(0,o._)([(0,l.Cb)({nonNullable:!0,json:{write:d}})],f.prototype,"position",void 0),(0,o._)([(0,c.p)("position")],f.prototype,"castPosition",null),(0,o._)([(0,l.Cb)({json:{write:d}})],f.prototype,"uv",void 0),(0,o._)([(0,c.p)("uv")],f.prototype,"castUv",null),(0,o._)([(0,l.Cb)({json:{write:d}})],f.prototype,"normal",void 0),(0,o._)([(0,c.p)("normal")],f.prototype,"castNormal",null),(0,o._)([(0,l.Cb)({json:{write:d}})],f.prototype,"tangent",void 0),(0,o._)([(0,c.p)("tangent")],f.prototype,"castTangent",null),f=r=(0,o._)([(0,u.j)(p)],f)},47545:(e,t,n)=>{n.d(t,{d:()=>i});var r=n(22021),o=n(54388);function i(e,t,n){const i=Array.isArray(e),u=i?e.length/t:e.byteLength/(4*t),p=i?e:new Uint32Array(e,0,u*t),h=n?.minReduction??0,f=n?.originalIndices||null,g=f?f.length:0,x=n?.componentOffsets||null;let d=0;if(x)for(let e=0;e<x.length-1;e++){const t=x[e+1]-x[e];t>d&&(d=t)}else d=u;const m=Math.floor(1.1*d)+1;(null==c||c.length<2*m)&&(c=new Uint32Array((0,r.Sf)(2*m)));for(let e=0;e<2*m;e++)c[e]=0;let y=0;const v=!!x&&!!f,w=v?g:u;let b=(0,o.$z)(u/3);const C=new Uint32Array(g),A=1.96;let T=0!==h?Math.ceil(4*A*A/(h*h)*h*(1-h)):w,Z=1,_=x?x[1]:w;for(let e=0;e<w;e++){if(e===T){const t=1-y/e;if(t+A*Math.sqrt(t*(1-t)/e)<h)return null;T*=2}if(e===_){for(let e=0;e<2*m;e++)c[e]=0;if(f)for(let e=x[Z-1];e<x[Z];e++)C[e]=b[f[e]];_=x[++Z]}const n=v?f[e]:e,r=n*t,o=l(p,r,t);let i=o%m,a=y;for(;0!==c[2*i+1];){if(c[2*i]===o){const e=c[2*i+1]-1;if(s(p,r,e*t,t)){a=b[e];break}}i++,i>=m&&(i-=m)}a===y&&(c[2*i]=o,c[2*i+1]=n+1,y++),b[n]=a}if(0!==h&&1-y/u<h)return null;if(v){for(let e=x[Z-1];e<C.length;e++)C[e]=b[f[e]];b=(0,o.mi)(C)}const M=i?new Array(y):new Uint32Array(y*t);y=0;for(let e=0;e<w;e++)b[e]===y&&(a(p,(v?f[e]:e)*t,M,y*t,t),y++);if(f&&!v){const e=new Uint32Array(g);for(let t=0;t<e.length;t++)e[t]=b[f[t]];b=(0,o.mi)(e)}return{buffer:Array.isArray(M)?M:M.buffer,indices:b,uniqueCount:y}}function s(e,t,n,r){for(let o=0;o<r;o++)if(e[t+o]!==e[n+o])return!1;return!0}function a(e,t,n,r,o){for(let i=0;i<o;i++)n[r+i]=e[t+i]}function l(e,t,n){let r=0;for(let o=0;o<n;o++)r=e[t+o]+r|0,r=r+(r<<11)+(r>>>2)|0;return r>>>0}let c=null},21932:(e,t,n)=>{n.r(t),n.d(t,{meshFeatureSetFromJSON:()=>Oe});var r,o,i=n(38171),s=n(70586),a=n(6570),l=n(43697),c=n(20102),u=n(3920),p=n(83379),h=n(92604),f=n(609),g=n(95330),x=n(17445),d=n(5600),m=(n(75215),n(67676),n(52011)),y=n(65617),v=n(9361),w=n(94139),b=n(38913),C=n(3709),A=n(62540),T=n(2674),Z=n(71630),_=n(95401),M=n(86662),R=n(47545),F=n(1533);function D(e,t=!1){return e<=F.DB?t?new Array(e).fill(0):new Array(e):new Float64Array(e)}function L(e,t,n){return Array.isArray(e)?e.slice(t,t+n):e.subarray(t,t+n)}function I(e,t,n){const o=e.length,i=new Array(o),s=new Array(o),a=new Array(o);let l=0,c=0,u=0,p=0;for(let t=0;t<o;++t)p+=e[t].length;const h=D(3*p);let f=0;for(let p=o-1;p>=0;p--){const g=e[p],x=n===r.CCW_IS_HOLE&&O(g);if(x&&1!==o)i[l++]=g;else{let e=g.length;for(let t=0;t<l;++t)e+=i[t].length;const n={index:f,pathLengths:new Array(l+1),count:e,holeIndices:new Array(l)};n.pathLengths[0]=g.length,g.length>0&&(a[u++]={index:f,count:g.length}),f=x?E(g,g.length-1,-1,h,f,g.length,t):E(g,0,1,h,f,g.length,t);for(let e=0;e<l;++e){const r=i[e];n.holeIndices[e]=f,n.pathLengths[e+1]=r.length,r.length>0&&(a[u++]={index:f,count:r.length}),f=E(r,0,1,h,f,r.length,t)}l=0,n.count>0&&(s[c++]=n)}}for(let e=0;e<l;++e){const n=i[e];n.length>0&&(a[u++]={index:f,count:n.length}),f=E(n,0,1,h,f,n.length,t)}return s.length=c,a.length=u,{position:h,polygons:s,outlines:a}}function E(e,t,n,r,o,i,s){o*=3;for(let a=0;a<i;++a){const i=e[t];r[o++]=i[0],r[o++]=i[1],r[o++]=s?i[2]:0,t+=n}return o/3}function O(e){return!(0,M.bu)(e,!1,!1)}(o=r||(r={}))[o.NONE=0]="NONE",o[o.CCW_IS_HOLE=1]="CCW_IS_HOLE";var P=n(44547),j=n(13442),S=n(66459);const U=h.Z.getLogger("esri.geometry.support.meshUtils.centerAt");const z=(0,y.c)(),N=(0,y.c)();var W=n(17452);function k(e){const t=(0,W.Yd)(e.url);return n=>{const r=(0,W.PF)(n,t,t),o=r?r.replace(/^ *\.\//,""):null;return(o?e.files.get(o):null)??n}}async function $(e,t){return e instanceof Blob?B.fromBlob(e):"string"==typeof e?new B(e):Array.isArray(e)?async function(e,t){const n=new Map;let r=null;const o=await(0,g.WW)(e.map((async e=>({name:e.name,source:await $(e instanceof Blob?e:e.source,t)})))),i=[];for(const e of o)e&&((0,g.Hc)(t)?e.source.dispose():i.push(e));(0,g.k_)(t);for(const{name:e,source:t}of i)((0,s.Wi)(r)||/\.(gltf|glb)/i.test(e))&&(r=t.url),n.set(e,t.url),t.files&&t.files.forEach(((e,t)=>n.set(t,e)));if((0,s.Wi)(r))throw new c.Z("mesh-load-external:missing-files","Missing files to load external mesh source");return new B(r,(()=>i.forEach((({source:e})=>e.dispose()))),n)}(e,t):async function(e,t){const{default:r}=await(0,g.Hl)(Promise.resolve().then(n.bind(n,3172)),t),o="string"==typeof e.multipart[0]?await Promise.all(e.multipart.map((async e=>(await r(e,{responseType:"array-buffer"})).data))):e.multipart;return B.fromBlob(new Blob(o))}(e,t)}class B{constructor(e,t=(()=>{}),n=new Map){this.url=e,this.dispose=t,this.files=n}static fromBlob(e){const t=URL.createObjectURL(e);return new B(t,(()=>URL.revokeObjectURL(t)))}}var H=n(21787),G=n(46521),V=n(13598),Y=n(17896),X=n(58995),q=n(56493);function K(e,t){if(e)for(let n=0;n<e.length;n+=3)for(let r=0;r<3;r++)e[n+r]+=t[r]}const J=(0,y.c)(),Q=(0,V.c)(),ee=(0,G.c)(),te={position:[-.5,-.5,0,.5,-.5,0,.5,.5,0,-.5,.5,0],normal:[0,0,1,0,0,1,0,0,1,0,0,1],uv:[0,1,1,1,1,0,0,0],faces:[0,1,2,0,2,3],facingAxisOrderSwap:{east:[3,1,2],west:[-3,-1,2],north:[-1,3,2],south:[1,-3,2],up:[1,2,3],down:[1,-2,-3]}};function ne(e,t,n){e.isPlane||function(e){for(let t=0;t<e.position.length;t+=3)e.position[t+2]+=.5}(e),function(e,t){if(null==t)return;const n="number"==typeof t?[t,t,t]:[null!=t.width?t.width:1,null!=t.depth?t.depth:1,null!=t.height?t.height:1];se[0]=n[0],se[4]=n[1],se[8]=n[2];for(let t=0;t<e.position.length;t+=3){for(let n=0;n<3;n++)ie[n]=e.position[t+n];(0,Y.t)(ie,ie,se);for(let n=0;n<3;n++)e.position[t+n]=ie[n]}if(n[0]!==n[1]||n[1]!==n[2]){se[0]=1/n[0],se[4]=1/n[1],se[8]=1/n[2];for(let t=0;t<e.normal.length;t+=3){for(let n=0;n<3;n++)ie[n]=e.normal[t+n];(0,Y.t)(ie,ie,se),(0,Y.n)(ie,ie);for(let n=0;n<3;n++)e.normal[t+n]=ie[n]}}}(e,n?.size);const{vertexAttributes:r,transform:o}=(0,S.w1)(e,t,n);return{vertexAttributes:new Z.Q({...r,uv:e.uv}),transform:o,components:[new A.Z({faces:e.faces,material:n&&n.material||null})],spatialReference:t.spatialReference}}const re={faceDescriptions:[{axis:[0,-1,0],uvOrigin:[0,.625],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[1,0,0],uvOrigin:[.25,.625],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[0,1,0],uvOrigin:[.5,.625],corners:[[1,-1],[-1,-1],[-1,1],[1,1]]},{axis:[-1,0,0],uvOrigin:[.75,.625],corners:[[1,-1],[-1,-1],[-1,1],[1,1]]},{axis:[0,0,1],uvOrigin:[0,.375],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[0,0,-1],uvOrigin:[0,.875],corners:[[-1,1],[1,1],[1,-1],[-1,-1]]}],uvScales:[[0,0],[1,0],[1,1],[0,1]],faceVertexOffsets:[0,1,2,0,2,3]},oe={south:0,east:1,north:2,west:3,up:4,down:5},ie=(0,y.c)(),se=(0,G.c)();var ae=n(52138);const le=h.Z.getLogger("esri.geometry.support.meshUtils.rotate");function ce(e,t,n,r=y.Z){if(!(0,s.Wi)(e)){(0,ae.d)(fe,(0,C.WH)(t),(0,C.ZZ)(t));for(let t=0;t<e.length;t+=n){for(let n=0;n<3;n++)ue[n]=e[t+n]-r[n];(0,Y.m)(ue,ue,fe);for(let n=0;n<3;n++)e[t+n]=ue[n]+r[n]}}}const ue=(0,y.c)(),pe=(0,y.c)(),he=(0,C.Ue)(),fe=(0,V.c)(),ge=(0,G.c)(),xe=(0,y.c)(),de=h.Z.getLogger("esri.geometry.support.meshUtils.scale");function me(e,t,n=y.Z){if(e)for(let r=0;r<e.length;r+=3){for(let t=0;t<3;t++)ye[t]=e[r+t]-n[t];(0,Y.g)(ye,ye,t);for(let t=0;t<3;t++)e[r+t]=ye[t]+n[t]}}const ye=(0,y.c)(),ve=(0,y.c)(),we=(0,y.c)();var be;const Ce="esri.geometry.Mesh";let Ae=be=class extends((0,u.p)(p.Z.LoadableMixin((0,f.v)(v.Z)))){constructor(e){super(e),this.components=null,this.transform=null,this.external=null,this.hasZ=!0,this.hasM=!1,this.vertexAttributes=new Z.Q,this.type="mesh"}initialize(){((0,s.Wi)(this.external)||this.vertexAttributes.position.length)&&(this.loadStatus="loaded"),this.when((()=>{this.handles.add((0,x.YP)((()=>({vertexAttributes:this.vertexAttributes,components:this.components?.map((e=>e.clone()))})),(()=>this._set("external",null)),{once:!0,sync:!0}))}))}get hasExtent(){return!this.loaded&&(0,s.pC)(this.external)&&(0,s.pC)(this.external.extent)||this.loaded&&this.vertexAttributes.position.length>0&&(!this.components||this.components.length>0)}get _boundingInfo(){const e=this.vertexAttributes.position,t=this.spatialReference;if(0===e.length||this.components&&0===this.components.length)return{extent:new a.Z({xmin:0,ymin:0,zmin:0,xmax:0,ymax:0,zmax:0,spatialReference:t}),center:new w.Z({x:0,y:0,z:0,spatialReference:t})};const n=(0,s.pC)(this.transform)?this.transform.project(e,t):e;let r=1/0,o=1/0,i=1/0,l=-1/0,c=-1/0,u=-1/0,p=0,h=0,f=0;const g=n.length,x=1/(g/3);let d=0;for(;d<g;){const e=n[d++],t=n[d++],s=n[d++];r=Math.min(r,e),o=Math.min(o,t),i=Math.min(i,s),l=Math.max(l,e),c=Math.max(c,t),u=Math.max(u,s),p+=x*e,h+=x*t,f+=x*s}return{extent:new a.Z({xmin:r,ymin:o,zmin:i,xmax:l,ymax:c,zmax:u,spatialReference:t}),center:new w.Z({x:p,y:h,z:f,spatialReference:t})}}get anchor(){if((0,s.pC)(this.transform))return this.transform.getOriginPoint(this.spatialReference);const e=this._boundingInfo;return new w.Z({x:e.center.x,y:e.center.y,z:e.extent.zmin,spatialReference:this.spatialReference})}get origin(){return(0,s.pC)(this.transform)?this.transform.getOriginPoint(this.spatialReference):this._boundingInfo.center}get extent(){return!this.loaded&&(0,s.pC)(this.external)&&(0,s.pC)(this.external.extent)?this.external.extent.clone():this._boundingInfo.extent}addComponent(e){this.loaded?(this.components||(this.components=[]),this.components.push(A.Z.from(e)),this.notifyChange("components")):h.Z.getLogger(this.declaredClass).error("addComponent()","Mesh must be loaded before applying operations")}removeComponent(e){if(this.loaded){if(this.components){const t=this.components.indexOf(e);if(-1!==t)return this.components.splice(t,1),void this.notifyChange("components")}h.Z.getLogger(this.declaredClass).error("removeComponent()","Provided component is not part of the list of components")}else h.Z.getLogger(this.declaredClass).error("removeComponent()","Mesh must be loaded before applying operations")}rotate(e,t,n,r){return(0,C.uT)(Te.x,e,Ze),(0,C.uT)(Te.y,t,_e),(0,C.uT)(Te.z,n,Me),(0,C.qC)(Ze,_e,Ze),(0,C.qC)(Ze,Me,Ze),function(e,t,n){if(!e.vertexAttributes||!e.vertexAttributes.position||0===t[3])return;const r=e.spatialReference;if((0,s.pC)(e.transform)){null!=n?.geographic&&n.geographic!==e.transform.geographic&&le.warn(`Specifying the 'geographic' parameter (${n.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`);const o=n?.origin??e.transform.getOriginPoint(r);!function(e,t,n){const r=(0,Y.s)(ue,n.x,n.y,n.z),o=(0,Y.b)(ue,r,e.origin);e.applyLocalInverse(o,pe),e.rotation=(0,C.qC)(e.rotation,t,(0,C.Ue)()),e.applyLocalInverse(o,o),(0,Y.b)(o,o,pe),e.translation=(0,Y.a)((0,y.c)(),e.translation,o)}(e.transform,t,o)}else{const r=n?.origin??e.origin;(0,j.h)(e.spatialReference,n)?function(e,t,n){const r=e.spatialReference,o=(0,X.rS)(r),i=xe;(0,P.KC)(n,i,o)||(0,P.KC)(e.origin,i,o);const a=e.vertexAttributes.position,l=e.vertexAttributes.normal,c=e.vertexAttributes.tangent,u=new Float64Array(a.length),p=(0,s.pC)(l)?new Float32Array(l.length):null,h=(0,s.pC)(c)?new Float32Array(c.length):null;(0,P.Bm)(o,i,fe,o),(0,H.f)(ge,fe);const f=he;(0,Y.t)((0,C.ZZ)(he),(0,C.ZZ)(t),ge),f[3]=t[3],(0,q.XO)(a,r,u),(0,s.pC)(l)&&(0,s.pC)(p)&&(0,q.Iz)(l,a,u,r,p),(0,s.pC)(c)&&(0,s.pC)(h)&&(0,q.wi)(c,a,u,r,h),ce(u,f,3,i),(0,q.To)(u,a,r),(0,s.pC)(l)&&(0,s.pC)(p)&&(ce(p,f,3),(0,q.Yk)(p,a,u,r,l)),(0,s.pC)(c)&&(0,s.pC)(h)&&(ce(h,f,4),(0,q.M2)(h,a,u,r,c)),e.vertexAttributesChanged()}(e,t,r):function(e,t,n){const r=xe;if(!(0,P.KC)(n,r,e.spatialReference)){const t=e.origin;r[0]=t.x,r[1]=t.y,r[2]=t.z,le.error(`Failed to project specified origin (wkid:${n.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}ce(e.vertexAttributes.position,t,3,r),ce(e.vertexAttributes.normal,t,3),ce(e.vertexAttributes.tangent,t,4),e.vertexAttributesChanged()}(e,t,r)}}(this,Ze,r),this}offset(e,t,n,r){return this.loaded?(Re[0]=e,Re[1]=t,Re[2]=n,function(e,t,n){var r,o;e.vertexAttributes&&e.vertexAttributes.position&&((0,s.pC)(e.transform)?(null!=n?.geographic&&n.geographic!==e.transform.geographic&&h.Z.getLogger("esri.geometry.support.meshUtils.offset").warn(`Specifying the 'geographic' parameter (${n.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`),function(e,t){const n=e.origin;e.origin=(0,Y.a)((0,y.c)(),n,t)}(e.transform,t)):(0,j.h)(e.spatialReference,n)?function(e,t){const n=e.spatialReference,r=e.vertexAttributes.position,o=e.vertexAttributes.normal,i=e.vertexAttributes.tangent,a=new Float64Array(r.length),l=(0,s.pC)(o)?new Float32Array(o.length):null,c=(0,s.pC)(i)?new Float32Array(i.length):null,u=e.extent.center,p=J;(0,P.Bm)(n,[u.x,u.y,u.z],Q,(0,X.rS)(n)),(0,H.f)(ee,Q),(0,Y.t)(p,t,ee),(0,q.XO)(r,n,a),(0,s.pC)(o)&&(0,s.pC)(l)&&(0,q.Iz)(o,r,a,n,l),(0,s.pC)(i)&&(0,s.pC)(c)&&(0,q.wi)(i,r,a,n,c),K(a,p),(0,q.To)(a,r,n),(0,s.pC)(o)&&(0,s.pC)(l)&&(0,q.Yk)(l,r,a,n,o),(0,s.pC)(i)&&(0,s.pC)(c)&&(0,q.M2)(c,r,a,n,i),e.vertexAttributesChanged()}(e,t):(o=t,K((r=e).vertexAttributes.position,o),r.vertexAttributesChanged()))}(this,Re,r),this):(h.Z.getLogger(this.declaredClass).error("offset()","Mesh must be loaded before applying operations"),this)}scale(e,t){return this.loaded?(function(e,t,n){if(!e.vertexAttributes||!e.vertexAttributes.position)return;const r=e.spatialReference;if((0,s.pC)(e.transform)){null!=n?.geographic&&n.geographic!==e.transform.geographic&&de.warn(`Specifying the 'geographic' parameter (${n.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`);const o=n?.origin??e.transform.getOriginPoint(r);!function(e,t,n){const r=(0,Y.s)(ye,n.x,n.y,n.z),o=(0,Y.b)(ye,r,e.origin);e.applyLocalInverse(o,ve);const i=(0,Y.g)((0,y.c)(),e.scale,t);e.scale=i,e.applyLocalInverse(o,o),(0,Y.b)(o,o,ve),e.translation=(0,Y.a)((0,y.c)(),e.translation,o)}(e.transform,t,o)}else{const r=(0,j.h)(e.spatialReference,n),o=n&&n.origin||e.origin;r?function(e,t,n){const r=e.spatialReference,o=(0,X.rS)(r),i=we;(0,P.KC)(n,i,o)||(0,P.KC)(e.origin,i,o);const a=e.vertexAttributes.position,l=e.vertexAttributes.normal,c=e.vertexAttributes.tangent,u=new Float64Array(a.length),p=(0,s.pC)(l)?new Float32Array(l.length):null,h=(0,s.pC)(c)?new Float32Array(c.length):null;(0,q.XO)(a,r,u),(0,s.pC)(l)&&(0,s.pC)(p)&&(0,q.Iz)(l,a,u,r,p),(0,s.pC)(c)&&(0,s.pC)(h)&&(0,q.wi)(c,a,u,r,h),me(u,t,i),(0,q.To)(u,a,r),(0,s.pC)(l)&&(0,s.pC)(p)&&(0,q.Yk)(p,a,u,r,l),(0,s.pC)(c)&&(0,s.pC)(h)&&(0,q.M2)(h,a,u,r,c),e.vertexAttributesChanged()}(e,t,o):function(e,t,n){const r=we;if(!(0,P.KC)(n,r,e.spatialReference)){const t=e.origin;r[0]=t.x,r[1]=t.y,r[2]=t.z,de.error(`Failed to project specified origin (wkid:${n.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}me(e.vertexAttributes.position,t,r),e.vertexAttributesChanged()}(e,t,o)}}(this,e,t),this):(h.Z.getLogger(this.declaredClass).error("scale()","Mesh must be loaded before applying operations"),this)}centerAt(e,t){return this.loaded?(function(e,t,n){if(!e.vertexAttributes||!e.vertexAttributes.position)return;const r=n?.origin??e.origin;(0,s.pC)(e.transform)?(null!=n?.geographic&&n.geographic!==e.transform.geographic&&U.warn(`Specifying the 'geographic' parameter (${n.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`),function(e,t,n){const r=t.x-n.x,o=t.y-n.y,i=t.hasZ&&n.hasZ?t.z-n.z:0,s=e.origin;e.origin=[s[0]+r,s[1]+o,s[2]+i]}(e.transform,t,r)):(0,j.h)(e.spatialReference,n)?function(e,t,n){const r=(0,S.FF)(e.vertexAttributes,n,{geographic:!0}),{position:o,normal:i,tangent:s}=(0,S.iv)(r,t,{geographic:!0});e.vertexAttributes.position=o,e.vertexAttributes.normal=i,e.vertexAttributes.tangent=s,e.vertexAttributesChanged()}(e,t,r):function(e,t,n){const r=N,o=z;if((0,P.KC)(t,o,e.spatialReference)){if(!(0,P.KC)(n,r,e.spatialReference)){const t=e.origin;r[0]=t.x,r[1]=t.y,r[2]=t.z,U.error(`Failed to project specified origin (wkid:${n.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}(function(e,t,n){if(e)for(let r=0;r<e.length;r+=3)for(let o=0;o<3;o++)e[r+o]+=t[o]-n[o]})(e.vertexAttributes.position,o,r),e.vertexAttributesChanged()}else U.error(`Failed to project centerAt location (wkid:${t.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid})`)}(e,t,r)}(this,e,t),this):(h.Z.getLogger(this.declaredClass).error("centerAt()","Mesh must be loaded before applying operations"),this)}load(e){return(0,s.pC)(this.external)&&this.addResolvingPromise(async function(e,t,r){const{loadGLTFMesh:o}=await(0,g.Hl)(Promise.all([n.e(3011),n.e(9070)]).then(n.bind(n,99070)),r),i=await $(t,r),a=o(new w.Z({x:0,y:0,z:0,spatialReference:e.spatialReference}),i.url,{resolveFile:k(i),useTransform:!0,signal:(0,s.pC)(r)?r.signal:null});a.then((()=>i.dispose()),(()=>i.dispose()));const{vertexAttributes:l,components:c}=await a;e.vertexAttributes=l,e.components=c}(this,this.external.source,e)),Promise.resolve(this)}updateExternalSource(e){this._set("external",e)}clone(){let e=null;if(this.components){const t=new Map,n=new Map;e=this.components.map((e=>e.cloneWithDeduplication(t,n)))}const t={components:e,spatialReference:this.spatialReference,vertexAttributes:this.vertexAttributes.clone(),transform:(0,s.pC)(this.transform)?this.transform.clone():null,external:(0,s.pC)(this.external)?{source:this.external.source,extent:(0,s.pC)(this.external.extent)?this.external.extent.clone():null}:null};return new be(t)}vertexAttributesChanged(){this.notifyChange("vertexAttributes")}async toBinaryGLTF(e){const t=n.e(7277).then(n.bind(n,7277)),r=this.load(),o=await Promise.all([t,r]),{toBinaryGLTF:i}=o[0];return i(this,e)}static createBox(e,t){if(!(e instanceof w.Z))return h.Z.getLogger(Ce).error(".createBox()","expected location to be a Point instance"),null;const n=new be(ne(function(){const{faceDescriptions:e,faceVertexOffsets:t,uvScales:n}=re,r=4*e.length,o=new Float64Array(3*r),i=new Float32Array(3*r),s=new Float32Array(2*r),a=new Uint32Array(2*e.length*3);let l=0,c=0,u=0,p=0;for(let r=0;r<e.length;r++){const h=e[r],f=l/3;for(const e of t)a[p++]=f+e;const g=h.corners;for(let e=0;e<4;e++){const t=g[e];let r=0;s[u++]=.25*n[e][0]+h.uvOrigin[0],s[u++]=h.uvOrigin[1]-.25*n[e][1];for(let e=0;e<3;e++)0!==h.axis[e]?(o[l++]=.5*h.axis[e],i[c++]=h.axis[e]):(o[l++]=.5*t[r++],i[c++]=0)}}return{position:o,normal:i,uv:s,faces:a}}(),e,t));return t&&t.imageFace&&"all"!==t.imageFace?function(e,t){const n=e.components[0],r=n.faces,o=oe[t],i=6*o,a=new Array(6),l=new Array(r.length-6);let c=0,u=0;for(let e=0;e<r.length;e++)e>=i&&e<i+6?a[c++]=r[e]:l[u++]=r[e];if((0,s.pC)(e.vertexAttributes.uv)){const t=new Float32Array(e.vertexAttributes.uv),n=4*o*2,r=[0,1,1,1,1,0,0,0];for(let e=0;e<r.length;e++)t[n+e]=r[e];e.vertexAttributes.uv=t}return e.components=[new A.Z({faces:a,material:n.material}),new A.Z({faces:l})],e}(n,t.imageFace):n}static createSphere(e,t){return e instanceof w.Z?new be(ne(function(e=0){const t=Math.round(8*2**e),n=2*t,r=(t-1)*(n+1)+2*n,o=new Float64Array(3*r),i=new Float32Array(3*r),s=new Float32Array(2*r),a=new Uint32Array((t-1)*n*2*3);let l=0,c=0,u=0,p=0;for(let e=0;e<=t;e++){const r=e/t*Math.PI+.5*Math.PI,h=Math.cos(r),f=Math.sin(r);ie[2]=f;const g=0===e||e===t,x=g?n-1:n;for(let r=0;r<=x;r++){const f=r/x*2*Math.PI;ie[0]=-Math.sin(f)*h,ie[1]=Math.cos(f)*h;for(let e=0;e<3;e++)o[l]=.5*ie[e],i[l]=ie[e],++l;s[c++]=(r+(g?.5:0))/n,s[c++]=e/t,0!==e&&r!==n&&(e!==t&&(a[u++]=p,a[u++]=p+1,a[u++]=p-n),1!==e&&(a[u++]=p,a[u++]=p-n,a[u++]=p-n-1)),p++}}return{position:o,normal:i,uv:s,faces:a}}(t&&t.densificationFactor||0),e,t)):(h.Z.getLogger(Ce).error(".createSphere()","expected location to be a Point instance"),null)}static createCylinder(e,t){return e instanceof w.Z?new be(ne(function(e=0){const t=Math.round(16*2**e),n=4*(t+1)+2*t,r=new Float64Array(3*n),o=new Float32Array(3*n),i=new Float32Array(2*n),s=new Uint32Array(4*t*3);let a=0,l=0,c=0,u=0,p=0;for(let e=0;e<=5;e++){const n=0===e||5===e,h=e<=1||e>=4,f=2===e||4===e,g=n?t-1:t;for(let x=0;x<=g;x++){const d=x/g*2*Math.PI,m=n?0:.5;ie[0]=m*Math.sin(d),ie[1]=m*-Math.cos(d),ie[2]=e<=2?.5:-.5;for(let t=0;t<3;t++)r[a++]=ie[t],o[l++]=h?2===t?e<=1?1:-1:0:2===t?0:ie[t]/m;i[c++]=(x+(n?.5:0))/t,i[c++]=e<=1?1*e/3:e<=3?1*(e-2)/3+1/3:1*(e-4)/3+2/3,f||0===e||x===t||(5!==e&&(s[u++]=p,s[u++]=p+1,s[u++]=p-t),1!==e&&(s[u++]=p,s[u++]=p-t,s[u++]=p-t-1)),p++}}return{position:r,normal:o,uv:i,faces:s}}(t&&t.densificationFactor||0),e,t)):(h.Z.getLogger(Ce).error(".createCylinder()","expected location to be a Point instance"),null)}static createPlane(e,t){if(!(e instanceof w.Z))return h.Z.getLogger(Ce).error(".createPlane()","expected location to be a Point instance"),null;const n=t?.facing??"up",r=function(e,t){const n="number"==typeof t?t:null!=t?t.width:1,r="number"==typeof t?t:null!=t?t.height:1;switch(e){case"up":case"down":return{width:n,depth:r};case"north":case"south":return{width:n,height:r};case"east":case"west":return{depth:n,height:r}}}(n,t?.size);return new be(ne(function(e){const t=te.facingAxisOrderSwap[e],n=te.position,r=te.normal,o=new Float64Array(n.length),i=new Float32Array(r.length);let s=0;for(let e=0;e<4;e++){const e=s;for(let a=0;a<3;a++){const l=t[a],c=Math.abs(l)-1,u=l>=0?1:-1;o[s]=n[e+c]*u,i[s]=r[e+c]*u,s++}}return{position:o,normal:i,uv:new Float32Array(te.uv),faces:new Uint32Array(te.faces),isPlane:!0}}(n),e,{...t,size:r}))}static createFromPolygon(e,t){if(!(e instanceof b.Z))return h.Z.getLogger(Ce).error(".createFromPolygon()","expected polygon to be a Polygon instance"),null;const n=function(e){const t=I(e.rings,e.hasZ,r.CCW_IS_HOLE),n=new Array;let o=0,i=0;for(const e of t.polygons){const r=e.count,s=e.index,a=L(t.position,3*s,3*r),l=e.holeIndices.map((e=>e-s)),c=new Uint32Array((0,_.e)(a,l,3));n.push({position:a,faces:c}),o+=a.length,i+=c.length}const s=function(e,t,n){if(1===e.length)return e[0];const r=D(t),o=new Uint32Array(n);let i=0,s=0,a=0;for(const t of e){for(let e=0;e<t.position.length;e++)r[i++]=t.position[e];for(let e=0;e<t.faces.length;e++)o[s++]=t.faces[e]+a;a=i/3}return{position:r,faces:o}}(n,o,i),a=Array.isArray(s.position)?(0,R.d)(s.position,3,{originalIndices:s.faces}):(0,R.d)(s.position.buffer,6,{originalIndices:s.faces});return s.position=new Float64Array(a.buffer),s.faces=a.indices,s}(e);return new be({vertexAttributes:new Z.Q({position:n.position}),components:[new A.Z({faces:n.faces,shading:"flat",material:t?.material??null})],spatialReference:e.spatialReference})}static async createFromGLTF(e,t,r){if(!(e instanceof w.Z))throw h.Z.getLogger(Ce).error(".createfromGLTF()","expected location to be a Point instance"),new c.Z("invalid-input","Expected location to be a Point instance");const{loadGLTFMesh:o}=await(0,g.Hl)(Promise.all([n.e(3011),n.e(9070)]).then(n.bind(n,99070)),r);return new be(await o(e,t,r))}static createWithExternalSource(e,t,n){const r=n?.extent??null,o=n?.transform?.clone()??new T.Z;o.origin=[e.x,e.y,e.z??0];const i=e.spatialReference;return new be({external:{source:t,extent:r},transform:o,spatialReference:i})}static createIncomplete(e,t){const n=t?.transform?.clone()??new T.Z;n.origin=[e.x,e.y,e.z??0];const r=e.spatialReference,o=new be({transform:n,spatialReference:r});return o.addResolvingPromise(Promise.reject(new c.Z("mesh-incomplete","Mesh resources are not complete"))),o}};(0,l._)([(0,d.Cb)({type:[A.Z],json:{write:!0}})],Ae.prototype,"components",void 0),(0,l._)([(0,d.Cb)({type:T.Z,json:{write:!0}})],Ae.prototype,"transform",void 0),(0,l._)([(0,d.Cb)({constructOnly:!0})],Ae.prototype,"external",void 0),(0,l._)([(0,d.Cb)({readOnly:!0})],Ae.prototype,"hasExtent",null),(0,l._)([(0,d.Cb)({readOnly:!0})],Ae.prototype,"_boundingInfo",null),(0,l._)([(0,d.Cb)({readOnly:!0})],Ae.prototype,"anchor",null),(0,l._)([(0,d.Cb)({readOnly:!0})],Ae.prototype,"origin",null),(0,l._)([(0,d.Cb)({readOnly:!0,json:{read:!1}})],Ae.prototype,"extent",null),(0,l._)([(0,d.Cb)({readOnly:!0,json:{read:!1,write:!0,default:!0}})],Ae.prototype,"hasZ",void 0),(0,l._)([(0,d.Cb)({readOnly:!0,json:{read:!1,write:!0,default:!1}})],Ae.prototype,"hasM",void 0),(0,l._)([(0,d.Cb)({type:Z.Q,nonNullable:!0,json:{write:!0}})],Ae.prototype,"vertexAttributes",void 0),Ae=be=(0,l._)([(0,m.j)(Ce)],Ae);const Te={x:(0,y.f)(1,0,0),y:(0,y.f)(0,1,0),z:(0,y.f)(0,0,1)},Ze=(0,C.Ue)(),_e=(0,C.Ue)(),Me=(0,C.Ue)(),Re=(0,y.c)(),Fe=Ae;var De,Le,Ie=n(82971),Ee=n(74889);function Oe(e,t,n){const r=n.features;n.features=[],delete n.geometryType;const o=Ee.Z.fromJSON(n);if(o.geometryType="mesh",!n.assetMaps)return o;const a=function(e,t){const n=new Map;for(const e of t){const t=e.parentGlobalId;if(null==t)continue;const r=e.assetName,o=e.assetURL,i=e.conversionStatus;let s=n.get(t);if(null==s)switch(s={name:r,status:De.FAILED,url:o,projectVertices:je(e.flags).projectVertices},n.set(t,s),i){case"COMPLETED":case"SUBMITTED":s.status=De.COMPLETED;break;case"INPROGRESS":s.status=De.PENDING;break;default:s.status=De.FAILED}else console.warn(`Multiple asset parts not expected. Ignoring additional parts. conflicting assetname: ${e.assetName}`)}return n}(0,n.assetMaps),l=o.spatialReference??Ie.Z.WGS84,c=n.globalIdFieldName,{outFields:u}=e,p=(0,s.pC)(u)&&u.length>0?(h=u.includes("*")?null:new Set(u),({attributes:e})=>{if(!e)return{};if(!h)return e;for(const t in e)h.has(t)||delete e[t];return e}):()=>({});var h;for(const e of r){const n=Pe(e,c,l,t,a);(0,s.pC)(n)&&o.features.push(new i.Z({geometry:n,attributes:p(e)}))}return o}function Pe(e,t,n,r,o){const i=e.attributes[t],s=o.get(i);if(null==s||s.status===De.FAILED||null==s.url)return null;const l=function({attributes:e},t,{transformFieldRoles:n}){return new w.Z({x:e[n.originX],y:e[n.originY],z:e[n.originZ],spatialReference:t})}(e,n,r),c=a.Z.fromJSON(e.geometry);c.spatialReference=n;const u=function(e,{transformFieldRoles:t},n){return new T.Z({translation:[e[t.translationX],-e[t.translationZ],e[t.translationY]],rotation:(0,C.uT)([e[t.rotationX],e[t.rotationZ],e[t.rotationY]],e[t.rotationDeg]),scale:[e[t.scaleX],e[t.scaleY],e[t.scaleZ]],geographic:n})}(e.attributes,r,s.projectVertices);return s.status===De.PENDING?Fe.createIncomplete(l,{extent:c,transform:u}):Fe.createWithExternalSource(l,[{name:s.name,source:s.url}],{extent:c,transform:u})}function je(e){return{projectVertices:e.includes("PROJECT_VERTICES")}}(Le=De||(De={}))[Le.FAILED=0]="FAILED",Le[Le.PENDING=1]="PENDING",Le[Le.COMPLETED=2]="COMPLETED"},54388:(e,t,n)=>{n.d(t,{$z:()=>i,DX:()=>u,mi:()=>o,p:()=>c});var r=n(1533);function o(e){if(Array.isArray(e)){if(e.length<r.DB)return e;for(const t of e)if(t>=65536)return new Uint32Array(e);return new Uint16Array(e)}if(e.length<r.DB)return Array.from(e);if(e.BYTES_PER_ELEMENT===Uint16Array.BYTES_PER_ELEMENT)return e;for(const t of e)if(t>=65536)return e;return new Uint16Array(e)}function i(e){const t=3*e;return t<=r.DB?new Array(t):t<=65536?new Uint16Array(t):new Uint32Array(t)}let s=(()=>{const e=new Uint32Array(131072);for(let t=0;t<e.length;++t)e[t]=t;return e})();const a=[0],l=(()=>{const e=new Uint16Array(65536);for(let t=0;t<e.length;++t)e[t]=t;return e})();function c(e){if(1===e)return a;if(e<r.DB)return Array.from(new Uint16Array(l.buffer,0,e));if(e<l.length)return new Uint16Array(l.buffer,0,e);if(e>s.length){const t=Math.max(2*s.length,e);s=new Uint32Array(t);for(let e=0;e<s.length;e++)s[e]=e}return new Uint32Array(s.buffer,0,e)}function u(e){if(1===e)return a;if(e<r.DB)return Array.from(new Uint16Array(l.buffer,0,e));if(e<l.length)return new Uint16Array(l.slice(0,e));if(e>s.length){const t=new Uint32Array(e);for(let e=0;e<t.length;e++)t[e]=e;return t}return new Uint32Array(s.slice(0,e))}}}]);