package org.thingsboard.server.dao.sql.device;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.DeviceTemplate;

import java.util.List;

public interface DeviceTemplateRepository extends JpaRepository<DeviceTemplate, String> {
    List<DeviceTemplate> findByTenantId(String tenantId);
    List<DeviceTemplate> findByTenantId(String tenantId, Sort sort);

    List<DeviceTemplate> findByTenantIdAndType(String fromTimeUUID, String type, Sort orders);

    List<DeviceTemplate> findByIdIn(List<String> ids);
}
