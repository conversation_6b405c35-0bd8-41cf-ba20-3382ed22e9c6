import{d as Q,r as x,c as D,b as f,a8 as U,S as Y,l as Z,bH as z,Q as ee,g as O,h as te,F as R,i as b,n as re,p as H,q as G,G as q,an as ie,bo as ae,bR as oe,_ as ne,J as pe,X as se,C as le}from"./index-r0dFAfgr.js";import{P as me}from"./gisSetting-CQEP-Q3N.js";import{s as I,g as de,q as ce,d as T,o as ue,m as ye}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{g as F,b as fe,H as ge}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import{i as he,e as ve}from"./IdentifyHelper-RJWmLn49.js";import{g as B,a as we}from"./LayerHelper-Cn-iiqxI.js";import{w as P}from"./Point-WxyopZva.js";import"./project-DUuzYgGl.js";import{s as E,i as be}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{S as Ie}from"./coordinateFormatter-C2XOyrWt.js";import Le from"./RightDrawerMap-D5PhmGFO.js";import{E as xe,c as Ge,d as M}from"./config-DncLSA-r.js";import{i as Fe}from"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-DeAQQ1ej.js";const Pe={key:0,class:"form-wrapper"},_e={class:"add-form overlay-y"},Ae={class:"add-footer"},Ce=Q({__name:"AddPipe",setup(Se){const n=x({layerIds:[],layerInfos:[],curPage:"index",timer:null}),e={vertices:[]},c=D(),L=x({height:198,pagination:{hide:!0},columns:[{minWidth:60,label:"",prop:"name"},{minWidth:100,label:"经度(x)",prop:"x",tableDataName:"tableList",formItemConfig:{type:"input-number",rules:[{required:!0,message:"请输入坐标"}]}},{minWidth:100,label:"纬度(y)",prop:"y",tableDataName:"tableList",formItemConfig:{type:"input-number",rules:[{required:!0,message:"请输入坐标"}]}}],dataList:[{name:"坐标"}]}),_=x({labelPosition:"top",group:[{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",options:[],handleCheckChange:(t,o)=>{o&&(c.value&&(c.value.dataForm.layerid=[t.value]),n.curLayerInfo=n.layerInfos.find(a=>a.layerid===t.value),n.curLayerInfo.geometrytype==="esriGeometryPolyline"?L.dataList=[{name:"起点"},{name:"终点"}]:n.curLayerInfo.geometrytype==="esriGeometryPoint"&&(L.dataList=[{name:"坐标"}]))}}]},{fieldset:{desc:"录入方式"},fields:[{type:"radio",field:"inputType",options:[{label:"地图绘制",value:"map"},{label:"坐标录入",value:"input"}]},{handleHidden:(t,o,a)=>{a.hidden=t.inputType!=="map"},label:"启用捕捉",width:60,type:"switch",field:"capture",activeText:"启用",inActiveText:"关闭"},{type:"select",field:"captureLayerId",label:"捕捉图层",multiple:!0,options:[],handleHidden:(t,o,a)=>{a.hidden=t.capture!==!0||t.inputType!=="map"}},{type:"btn-group",btns:[{perm:!0,text:"绘制",click:()=>X()}],handleHidden:(t,o,a)=>{a.hidden=t.inputType!=="map"}},{type:"table",hidden:!0,config:L,field:"tableList",handleHidden:(t,o,a)=>{a.hidden=t.inputType!=="input"}},{type:"btn-group",hidden:!0,itemContainerStyle:{marginTop:"12px"},btns:[{perm:!0,text:"确定",styles:{marginLeft:"auto"},click:()=>V()}],handleHidden:(t,o,a)=>{a.hidden=t.inputType!=="input"}}]}],defaultValue:{capture:!1,inputType:"map"}}),V=()=>{var t,o,a,r,s,l;if(n.curLayerInfo.geometrytype==="esriGeometryPolyline"){const p=L.dataList.filter(i=>i.x&&i.y).map(i=>{var d;const m=new P({longitude:i.x,latitude:i.y,spatialReference:(d=e.view)==null?void 0:d.spatialReference});return[m.x,m.y]});if(p.length<2){f.warning("请完整输入坐标信息");return}e.graphic=new F({geometry:new fe({paths:[p],spatialReference:(t=e.view)==null?void 0:t.spatialReference}),symbol:I("polyline")}),e.vertices=p,(o=e.graphicsLayer)==null||o.removeAll(),(a=e.graphicsLayer)==null||a.add(e.graphic)}else if(n.curLayerInfo.geometrytype==="esriGeometryPoint"){const p=L.dataList.filter(i=>i.x&&i.y)[0];if(!p){f.warning("请完整输入坐标信息");return}e.graphic=new F({geometry:new P({longitude:p.x,latitude:p.y,spatialReference:(r=e.view)==null?void 0:r.spatialReference}),symbol:I("point")}),(s=e.graphicsLayer)==null||s.removeAll(),(l=e.graphicsLayer)==null||l.add(e.graphic)}de(e.view,e.graphic,{avoidHighlight:!0}),A(),n.curPage="add"},g=D(),v=x({labelPosition:"top",group:[{fieldset:{desc:U(()=>{var t;return(t=n.curLayerInfo)==null?void 0:t.layername})},fields:[]}],defaultValue:{},submit:t=>{Y("该操作变更空间数据，是否继续？","提示信息").then(()=>{var l;if(!e.submitGraphic){f.error("绘制图形丢失，请重新绘制图形");return}const o=(l=c.value)==null?void 0:l.dataForm.layerid;if(o===void 0){f.error("未找到图层信息，请重新选择图层和绘制");return}v.submitting=!0;const a=new ge({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer+"/"+o,id:"feature-layer"}),r=n.curLayerInfo.layername,s={...t,CREATEDDATE:Z().format(z)};e.submitGraphic.attributes=s,a==null||a.applyEdits({addFeatures:[e.submitGraphic]}).then(p=>{var m,d,u;f.success("操作成功"),me({optionName:xe.XINZENGGW,type:Ge.BASICGIS,content:`${M.ADD}${r},OBJECTID:${p.addFeatureResults[0].objectId}`,optionType:M.ADD}).catch(()=>{console.log("生成gis操作日志失败")});const i=(m=e.view)==null?void 0:m.map.findLayerById("pipelayer");i&&i.refresh(),(d=g.value)==null||d.resetForm(),n.curPage="index",(u=e.graphicsLayer)==null||u.removeAll(),e.graphic=void 0}).catch(p=>{console.log(p),f.error("操作失败")}).finally(()=>{v.submitting=!1})}).catch(()=>{})}}),$=()=>{var t;(t=g.value)==null||t.resetForm(),n.curPage="index"},W=async()=>{var l,p;n.layerIds=we(e.view);const t=await se(n.layerIds);n.layerInfos=((p=(l=t.data)==null?void 0:l.result)==null?void 0:p.rows)||[];const o=_.group[0].fields[0],a=_.group[1].fields[2],r=n.layerInfos.filter(i=>i.geometrytype==="esriGeometryPoint").map(i=>({label:i.layername,value:i.layerid,data:i})),s=n.layerInfos.filter(i=>i.geometrytype==="esriGeometryPolyline").map(i=>({label:i.layername,value:i.layerid,data:i}));o&&(o.options=[{label:"管线类",value:-2,children:s,disabled:!0},{label:"管点类",value:-1,children:r,disabled:!0}]),a.options=[...r,...s],c.value&&(c.value.dataForm.layerid=n.layerIds.slice(0,1),n.curLayerInfo=n.layerInfos.find(i=>i.layerid===n.layerIds[0]))},X=()=>{var a,r,s,l,p,i,m,d,u,h;if(!n.curLayerInfo||!e.view)return;const t=((a=n.curLayerInfo)==null?void 0:a.geometrytype)==="esriGeometryPolyline"?"polyline":((r=n.curLayerInfo)==null?void 0:r.geometrytype)==="esriGeometryPoint"?"point":"";if(!t)return;E("crosshair"),((s=c.value)==null?void 0:s.dataForm.capture)?J():(n.timer&&clearTimeout(n.timer),(l=e.moveEvent)==null||l.remove()),e.vertices.length=0,(p=e.drawer)==null||p.destroy(),e.drawer=be(e.view),(i=e.drawAction)==null||i.destroy(),e.drawAction=(m=e.drawer)==null?void 0:m.create(t),t==="polyline"&&((d=e.drawAction)==null||d.on(["vertex-add"],y=>{C(y,t,"add")})),(u=e.drawAction)==null||u.on(["cursor-update"],y=>{C(y,t,"update")}),(h=e.drawAction)==null||h.on("draw-complete",async y=>{C(y,t),E(""),n.curPage="add",A()})},A=async()=>{var t,o,a,r,s;v.submitting=!0;try{const l=(o=(t=c.value)==null?void 0:t.dataForm.layerid)==null?void 0:o[0];if(l===void 0){f.warning("请先选择图层");return}const p=await Fe(l);if(!g.value)return;if(v.group[0].fields=[...p],!e.graphic){f.error("请先绘制图形");return}if(e.submitGraphic=e.graphic,((a=e.graphic)==null?void 0:a.geometry.type)==="polyline"){const i=ce(e.vertices,"meters",(r=e.view)==null?void 0:r.spatialReference);g.value.dataForm.PIPELENGTH=i}else{const i=(s=e.submitGraphic)==null?void 0:s.geometry;g.value.dataForm.X=i.x,g.value.dataForm.Y=i.y}}catch{n.curPage="index",f.error("暂时无法查询到管网服务详情，请稍候再试")}v.submitting=!1},C=(t,o,a)=>{var s,l,p,i,m,d,u,h,y,N,k;(s=e.graphicsLayer)==null||s.removeAll();const r=t.vertices;if(o==="point")if(e.identifyResult){const w=T((l=e.identifyResult.feature)==null?void 0:l.geometry,new P({x:r[r.length-1][0],y:r[r.length-1][1],spatialReference:(p=e.view)==null?void 0:p.spatialReference}));e.graphic=new F({geometry:w,symbol:I("point")}),e.vertices=r,w&&((i=e.graphicsLayer)==null||i.add(e.graphic))}else e.graphic=ue(r,(m=e.view)==null?void 0:m.spatialReference,I("point")),e.vertices=r,e.graphic&&((d=e.graphicsLayer)==null||d.add(e.graphic));else if(o==="polyline"){if(e.identifyResult&&a==="add"){const S=T((u=e.identifyResult.feature)==null?void 0:u.geometry,new P({x:r[r.length-1][0],y:r[r.length-1][1],spatialReference:(h=e.view)==null?void 0:h.spatialReference}));S&&(r[r.length-1][0]=S.x,r[r.length-1][1]=S.y)}const w=ye(r,(y=e.view)==null?void 0:y.spatialReference,I("polyline"));e.graphic=w,e.vertices=r,w&&((N=e.graphicsLayer)==null||N.add(w)),a==="add"&&r.length===2&&((k=e.drawAction)==null||k.destroy(),E(""),n.curPage="add",A())}},J=()=>{var t,o;(t=e.moveEvent)==null||t.remove(),e.moveEvent=(o=e.view)==null?void 0:o.on("pointer-move",a=>{var r;n.timer&&clearTimeout(n.timer),(r=e.textLayer)==null||r.removeAll(),e.identifyResult=void 0,n.timer=setTimeout(()=>{var p,i;if(!((p=c.value)==null?void 0:p.dataForm.capture))return;const l=(i=e.view)==null?void 0:i.toMap(a);j(l,n.timer)},200)})},j=async(t,o)=>{var a,r,s,l,p,i;if(!(!e.view||!t))try{const m=(a=c.value)==null?void 0:a.dataForm.captureLayerId;if(!(m!=null&&m.length))return;const d=he({geometry:t,mapExtent:e.view.extent,layerIds:m}),u=await ve(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,d);if(o!==n.timer||((r=e.textLayer)==null||r.removeAll(),e.identifyResult=(s=u.results)==null?void 0:s[0],!e.identifyResult))return;const h=T((l=e.identifyResult.feature)==null?void 0:l.geometry,t);if(!h)return;const y=new F({geometry:h,symbol:I("text",{yOffset:15,text:e.identifyResult.layerName+":"+e.identifyResult.feature.attributes.新编号})});(p=e.textLayer)==null||p.add(y)}catch{(i=e.textLayer)==null||i.removeAll()}},K=async t=>{Ie(),e.view=t,e.graphicsLayer=B(e.view,{id:"pipe-add",title:"新增管网"}),e.textLayer=B(e.view,{id:"pipe-add-text",title:"新增管网-临时标注"}),await W()};return ee(()=>{var t,o,a,r,s;(t=e.drawAction)==null||t.destroy(),(o=e.drawer)==null||o.destroy(),(a=e.graphicsLayer)==null||a.removeAll(),(r=e.moveEvent)==null||r.remove(),(s=e.textLayer)==null||s.removeAll()}),(t,o)=>{const a=ne,r=pe;return O(),te(Le,{ref:"refMap",title:"新增管网",onMapLoaded:K},{default:R(()=>[b(n).curPage==="add"?(O(),re("div",Pe,[H("div",_e,[G(a,{ref_key:"refFormAdd",ref:g,config:b(v)},null,8,["config"])]),H("div",Ae,[G(r,{type:"default",onClick:$},{default:R(()=>o[1]||(o[1]=[q(" 取消 ")])),_:1}),G(r,{loading:b(v).submitting,type:"primary",onClick:o[0]||(o[0]=()=>{var s;return(s=b(g))==null?void 0:s.Submit()})},{default:R(()=>o[2]||(o[2]=[q(" 确定 ")])),_:1},8,["loading"])])])):ie("",!0),ae(G(a,{ref_key:"refForm",ref:c,config:b(_)},null,8,["config"]),[[oe,b(n).curPage==="index"]])]),_:1},512)}}}),qr=le(Ce,[["__scopeId","data-v-6537bcea"]]);export{qr as default};
