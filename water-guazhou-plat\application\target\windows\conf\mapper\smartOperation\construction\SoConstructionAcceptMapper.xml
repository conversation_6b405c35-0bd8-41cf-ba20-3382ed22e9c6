<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionAcceptMapper">
    <!--suppress SqlShadowingAlias -->
    <sql id="Base_Column_List">
        <!--@sql select -->
        accept.id,
        construction.code                                                  as construction_code,
        construction.name                                                  as construction_name,
        construction.type_id                                               as construction_type_id,
        (select name from so_general_type where id = construction.type_id) as construction_type_name,
        project.code                                                       as project_code,
        project.name                                                       as project_name,
        accept.begin_time,
        accept.end_time,
        accept.applicant_organization,
        accept.applicant,
        accept.applicant_phone,
        accept.construct_organization,
        accept.supervisor_organization,
        accept.audit_organization,
        accept.design_organization,
        info.status,
        accept.remark,
        accept.attachments,
        accept.creator,
        accept.create_time,
        accept.update_user,
        accept.update_time,
        construction.tenant_id<!--@sql from so_construction_accept accept, so_construction construction,
                                      so_construction_task_info info, so_project project -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionAccept">
        <result column="id" property="id"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="applicant_organization" property="applicantOrganization"/>
        <result column="applicant" property="applicant"/>
        <result column="applicant_phone" property="applicantPhone"/>
        <result column="construct_organization" property="constructOrganization"/>
        <result column="supervisor_organization" property="supervisorOrganization"/>
        <result column="audit_organization" property="auditOrganization"/>
        <result column="design_organization" property="designOrganization"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_ACCEPT"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_accept accept
                           on accept.construction_code = construction.code and
                              accept.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id
                               and info.scope = #{scope}
                 left join so_project project
                           on construction.project_code = project.code and
                              construction.tenant_id = project.tenant_id
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="projectStartTimeFrom != null">
                <!--@formatter:off-->
                and (select start_time >= #{projectStartTimeFrom} from so_project where code = project_code and tenant_id = #{tenantId})
                <!--@formatter:on-->
            </if>
            <if test="projectStartTimeTo != null">
                <!--@formatter:off-->
                and (select start_time &lt;= #{projectStartTimeFrom} from so_project where code = project_code and tenant_id = #{tenantId})
                <!--@formatter:on-->
            </if>
            <if test="fromTime != null">
                and accept.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and accept.create_time &lt;= #{toTime}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
        order by construction.create_time desc
    </select>

    <update id="update">
        update so_construction_accept
        <set>
            <!--            <if test="code != null">-->
            <!--                code = #{code},-->
            <!--            </if>-->
            <!--            <if test="constructionCode != null">-->
            <!--                construction_code = #{constructionCode},-->
            <!--            </if>-->
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="applicantOrganization != null">
                applicant_organization = #{applicantOrganization},
            </if>
            <if test="applicant != null">
                applicant = #{applicant},
            </if>
            <if test="applicantPhone != null">
                applicant_phone = #{applicantPhone},
            </if>
            <if test="constructOrganization != null">
                construct_organization = #{constructOrganization},
            </if>
            <if test="supervisorOrganization != null">
                supervisor_organization = #{supervisorOrganization},
            </if>
            <if test="auditOrganization != null">
                audit_organization = #{auditOrganization},
            </if>
            <if test="designOrganization != null">
                design_organization = #{designOrganization},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
            update_user = #{updateUser},
            update_time = #{updateTime}
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_accept
        set begin_time              = #{beginTime},
            end_time                = #{endTime},
            applicant_organization  = #{applicantOrganization},
            applicant               = #{applicant},
            applicant_phone         = #{applicantPhone},
            construct_organization  = #{constructOrganization},
            supervisor_organization = #{supervisorOrganization},
            audit_organization      = #{auditOrganization},
            design_organization     = #{designOrganization},
            remark                  = #{remark},
            attachments             = #{attachments},
            update_user             = #{updateUser},
            update_time             = #{updateTime}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_construction_accept(id,
                                           construction_code,
                                           begin_time,
                                           end_time,
                                           applicant_organization,
                                           applicant,
                                           applicant_phone,
                                           construct_organization,
                                           supervisor_organization,
                                           audit_organization,
                                           design_organization,
                                           remark,
                                           attachments,
                                           creator,
                                           create_time,
                                           update_user,
                                           update_time,
                                           tenant_id)
        VALUES (#{id},
                #{constructionCode},
                #{beginTime},
                #{endTime},
                #{applicantOrganization},
                #{applicant},
                #{applicantPhone},
                #{constructOrganization},
                #{supervisorOrganization},
                #{auditOrganization},
                #{designOrganization},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_accept
        where id = #{id}
    </select>

    <select id="getIdByConstructionCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_construction_accept
        where construction_code = #{constructionCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>