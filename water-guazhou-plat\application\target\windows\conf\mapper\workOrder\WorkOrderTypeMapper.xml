<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.workOrder.WorkOrderTypeMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType">
        select *
        from work_order_type
        <where>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="tenantId != null and status != ''">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="findByResourceId" resultType="org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType">
        select a.*
        from work_order_type a LEFT JOIN work_order_resource_type_relation b ON a.id = b.type_id
        <where>
            <if test="resourceId != null and resourceId != ''">
                and a.resource_id = #{resourceId}
            </if>
            <if test="tenantId != null and status != ''">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>