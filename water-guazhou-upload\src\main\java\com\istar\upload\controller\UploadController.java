package com.istar.upload.controller;

import com.alibaba.fastjson.JSONObject;
import com.istar.upload.config.UploadConfigProperty;
import com.istar.upload.constants.FileTypeConstants;
import com.istar.upload.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("api/upload")
public class UploadController {

    @Autowired
    private UploadService uploadService;

    @Autowired
    private UploadConfigProperty uploadConfigProperty;

    @PostMapping("/image")
    public String uploadImage(@RequestParam("file") MultipartFile file) {
        // 判断图片类型
        if (!uploadConfigProperty.getImageTypes().contains(file.getContentType())) {
            throw new RuntimeException("非法文件类型! fileType: [{" + file.getContentType() + "}]");
        }

        return uploadService.uploadFile(file, FileTypeConstants.IMAGE);
    }

    @PostMapping("/file")
    public String uploadFile(@RequestParam("file") MultipartFile file) {
        return uploadService.uploadFile(file, FileTypeConstants.OTHER);
    }

    @PostMapping("/zipFile")
    public List<JSONObject> uploadZipFile(@RequestParam("file") MultipartFile file) {
        return uploadService.uploadZipFile(file, FileTypeConstants.ZIP);
    }

    @GetMapping("/downloadFile")
    public void downloadFile(@RequestParam("url") String urlPath, HttpServletResponse response) {
        int len = 0;
        try {
            URL url = new URL(urlPath);
            String filename = urlPath.substring(urlPath.lastIndexOf("/") + 1);
            if (urlPath.contains("?name=")) {
                filename = urlPath.substring(urlPath.indexOf("name=") + 5);
            }

            URLConnection urlConnection = url.openConnection();

            //设置response的Header
            String fileNameURL = URLEncoder.encode(filename, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
            response.addHeader("Content-Length", "" + urlConnection.getContentLength());
            response.setContentType("application/force-download");

            InputStream fis = urlConnection.getInputStream();
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            byte[] b = new byte[1024];
            while ((len = fis.read(b)) != -1) {

                //输出缓冲区的内容到浏览器，实现文件下载
                toClient.write(b, 0, len);

            }
            fis.close();
            toClient.flush();
            toClient.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
