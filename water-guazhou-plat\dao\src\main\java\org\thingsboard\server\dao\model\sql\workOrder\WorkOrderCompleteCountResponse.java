package org.thingsboard.server.dao.model.sql.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.statistic.StatisticCountAndPercent;
import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.util.imodel.response.annotations.Flatten;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class WorkOrderCompleteCountResponse {
    // 全年工单总数
    private Integer totalYearly;

    // 当月或自定义时间范围工单总数
    private Integer totalMonthly;

    // 全年完成率
    private Double percentYearly;

    // 当月或自定义时间范围完成率
    private Double percentMonthly;

    // 工单类型占比
    @Flatten
    StatisticItem data;

    public WorkOrderCompleteCountResponse(StatisticCountAndPercent yearly, StatisticCountAndPercent monthly, StatisticItem monthlyData) {
        setYearly(yearly);
        setMonthly(monthly);
        this.data = monthlyData;
    }

    public void setYearly(StatisticCountAndPercent in) {
        totalYearly = in.getTotal();
        percentYearly = in.getPercent();
    }

    public void setMonthly(StatisticCountAndPercent in) {
        totalMonthly = in.getTotal();
        percentMonthly = in.getPercent();
    }
}
