<template>
  <div class="chart-wrapper">
    <VChart :option="state.eventLevenOption"></VChart>
  </div>
</template>
<script lang="ts" setup>
import { GetWorkOrderCountStatistic } from '@/api/workorder'

const generateEventLevelOption = (data: { name: string; value: number }[]) => {
  const xData = data.map(item => item.name)
  const yData = data.map(item => item.value)
  const barWidth = 20
  const barColor = '#0099FF'
  const title = '事件类型'
  return {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: 20,
      right: 20,
      bottom: 10,
      top: 40,
      containLabel: true
    },
    xAxis: [
      {
        boundaryGap: barWidth / 2,
        type: 'category',
        data: xData,
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          // rotate: 45,
          margin: 12
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        }
      }
    ],
    series: [
      {
        name1: 'bar0-0',
        color: barColor,
        silent: true,
        symbol: 'diamond',
        type: 'pictorialBar',
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, -barWidth / 4],
        data: yData.map(item => {
          return {
            value: item,
            symbolPosition: 'end'
          }
        })
      },
      {
        name: title,
        color: barColor,
        type: 'bar',
        data: yData,
        barGap: 0,
        hoverAnimation: false,
        barWidth: barWidth / 2,
        label: {
          show: true,
          position: 'top',
          distance: 10,
          color: '#fff',
          offset: [barWidth / 4, 0]
        },
        itemStyle: {
          opacity: 0.8
        }
      },
      {
        name: title,
        color: barColor,
        type: 'bar',
        data: yData,
        barGap: 0,
        barWidth: barWidth / 2,
        hoverAnimation: false,
        label: {
          show: false,
          position: 'top',
          distance: 10,
          color: '#fff'
        },
        itemStyle: {
          opacity: 0.8
        }
      },
      {
        name1: 'bar0-1',
        color: barColor,
        symbol: 'diamond',
        type: 'pictorialBar',
        silent: true,
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, barWidth / 4],
        data: yData
      }
    ]
  }
}
const state = reactive<{
  eventLevenOption: any
}>({
  eventLevenOption: null
})
const refreshData = () => {
  GetWorkOrderCountStatistic({
    fromTime: moment().startOf('y').valueOf(),
    toTime: moment().valueOf(),
    statisticType: true
  }).then(res => {
    const data = res.data?.data?.types?.data || {}
    const chartData = data.map(item => {
      return { name: item.key, value: item.value }
    })
    state.eventLevenOption = generateEventLevelOption(chartData)
  })
}

onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
