package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRecord;

@Getter
@Setter
public class GuardRecordPageRequest extends PageableQueryEntity<GuardRecord> {
    // 地点id
    private String placeId;

}
