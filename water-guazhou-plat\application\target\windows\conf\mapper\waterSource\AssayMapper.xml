<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.thingsboard.server.dao.sql.waterSource.AssayMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.assay.Assay">
        SELECT
            a.id,
            a.tenant_id as tenantId,
            a.creator,
            a.report_name as reportName,
            a.sampling_location as samplingLocation,
            a.testing_unit as testingUnit,
            a.test_results as testResults,
            a.test_date as testDate,
            a.report_file as reportFile,
            a.create_time as createTime,
            a.update_time as updateTime,
            a.remark,
            a.type
        FROM tb_water_assay a
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND a.tenant_id = #{params.tenantId}
            </if>
            <if test="params.reportName != null and params.reportName != ''">
                AND a.report_name LIKE '%' || #{params.reportName} || '%'
            </if>
            <if test="params.samplingLocation != null and params.samplingLocation != ''">
                AND a.sampling_location LIKE '%' || #{params.samplingLocation} || '%'
            </if>
            <if test="params.testingUnit != null and params.testingUnit != ''">
                AND a.testing_unit LIKE '%' || #{params.testingUnit} || '%'
            </if>
            <if test="params.testDate != null">
                AND TO_CHAR(a.test_date, 'YYYY-MM') = TO_CHAR(to_timestamp(CAST(#{params.testDate} AS BIGINT)/1000), 'YYYY-MM')
            </if>
            <if test="params.type != null and params.type != ''">
                AND a.type = #{params.type}
            </if>
        </where>
        <choose>
            <when test="params.sortField != null and params.sortField != '' and params.sortOrder != null and params.sortOrder != ''">
                <choose>
                    <!-- 处理可能的驼峰命名转换为下划线命名 -->
                    <when test="params.sortField == 'createTime' or params.sortField == 'createtime'">
                        ORDER BY a.create_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'updateTime' or params.sortField == 'updatetime'">
                        ORDER BY a.update_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'testDate' or params.sortField == 'testdate'">
                        ORDER BY a.test_date ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'reportName' or params.sortField == 'reportname'">
                        ORDER BY a.report_name ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'samplingLocation' or params.sortField == 'samplinglocation'">
                        ORDER BY a.sampling_location ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'testingUnit' or params.sortField == 'testingunit'">
                        ORDER BY a.testing_unit ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'testResults' or params.sortField == 'testresults'">
                        ORDER BY a.test_results ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'reportFile' or params.sortField == 'reportfile'">
                        ORDER BY a.report_file ${params.sortOrder}
                    </when>
                    <otherwise>
                        <!-- 如果是其他字段，尝试直接使用 -->
                        ORDER BY a.${params.sortField} ${params.sortOrder}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY a.create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
