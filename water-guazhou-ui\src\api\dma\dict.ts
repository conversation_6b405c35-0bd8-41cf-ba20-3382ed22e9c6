// DMA 字典管理
import { request } from '@/plugins/axios'

/**
 * 列表
 * @param params
 * @returns
 */
export const getDictList = (params?: {
  page:number,
  size:number,
  type?:string,
}) => request({
  url: `/api/spp/dma/dict/list`,
  method: 'get',
  params
})

/**
 * 新增/编辑
 * @param params
 * @returns
 */
export const saveDict = (params:{
    code: string
    type: string
    label: string
    value: string
    orderNum?: number
}) => request({
  url: `/api/spp/dma/dict`,
  method: 'post',
  data: params
})

/**
 * 删除
 * @param params
 * @returns
 */
export const delDict = (ids:any) => request({
  url: `/api/spp/dma/dict`,
  method: 'delete',
  data: ids
})
