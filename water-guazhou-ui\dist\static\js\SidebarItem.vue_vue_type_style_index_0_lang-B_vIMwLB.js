import{d as I,a8 as M,bY as V,ay as z,i as A,g as t,n as a,aB as s,h as m,bg as g,F as r,p as B,bh as C,aw as l,aJ as D,an as N,bZ as J,b_ as L}from"./index-r0dFAfgr.js";const Y=I({__name:"SidebarItem",props:{item:{},isNest:{type:Boolean},icon:{}},setup(S){const e=S,w=M(()=>{var i,o;return V((o=(i=e.item)==null?void 0:i.meta)==null?void 0:o.roles)});return(i,o)=>{var u,d,p,_,h,y,k,b;const c=z("sidebar-item",!0),v=J,E=L;return!((u=e.item)!=null&&u.hidden)&&!((p=(d=e.item)==null?void 0:d.meta)!=null&&p.hidden)&&A(w)?(t(),a(s,{key:0},[(_=e.item.children)!=null&&_.length?(t(),a(s,{key:0},[e.item.children.length===1&&!e.item.alwaysShow?(t(),m(c,{key:0,"is-nest":!0,item:e.item.children[0],icon:((h=e.item.children[0].meta)==null?void 0:h.icon)||e.icon},null,8,["item","icon"])):(t(),m(v,{key:1,index:e.item.path},g({default:r(()=>[(t(!0),a(s,null,D(e.item.children,(n,F)=>{var f,x;return t(),a(s,null,[!n.hidden&&!((f=n==null?void 0:n.meta)!=null&&f.hidden)?(t(),m(c,{key:F,"is-nest":!0,item:n,icon:((x=n.meta)==null?void 0:x.icon)||e.icon},null,8,["item","icon"])):N("",!0)],64)}),256))]),_:2},[(y=e.item.meta)!=null&&y.title?{name:"title",fn:r(()=>[B("span",{class:l("title-text "+e.item.meta.icon)},C(e.item.meta.title),3)]),key:"0"}:void 0]),1032,["index"]))],64)):(t(),m(E,{key:1,index:e.item.path,class:l({"nest-menu":e.isNest}),route:i.item.component?i.item:{name:"NotFound"}},g({_:2},[(b=(k=e.item)==null?void 0:k.meta)!=null&&b.title?{name:"title",fn:r(()=>[B("span",{class:l("title-text "+e.icon)},C(e.item.meta.title),3)]),key:"0"}:void 0]),1032,["index","class","route"]))],64)):N("",!0)}}});export{Y as _};
