package org.thingsboard.server.dao.notify;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SystemNotifyTypeListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotifyType;
import org.thingsboard.server.dao.sql.notify.SystemNotifyTypeMapper;
import org.thingsboard.server.dao.util.RedisUtil;

import java.util.Date;

@Slf4j
@Service
public class SystemNotifyTypeServiceImpl implements SystemNotifyTypeService {

    @Autowired
    private SystemNotifyTypeMapper systemNotifyTypeMapper;

    @Override
    public SystemNotifyType save(SystemNotifyType systemNotifyType) {
        if (StringUtils.isBlank(systemNotifyType.getId())) {
            systemNotifyType.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.NOTIFY_TYPE, ""));
            systemNotifyType.setCreateTime(new Date());
            systemNotifyTypeMapper.insert(systemNotifyType);
        } else {
            systemNotifyTypeMapper.updateById(systemNotifyType);
        }

        return systemNotifyType;
    }

    @Override
    public PageData<SystemNotifyType> findList(SystemNotifyTypeListRequest request) {
        IPage<SystemNotifyType> ipage = systemNotifyTypeMapper.findList(request);
        return new PageData<>(ipage.getTotal(), ipage.getRecords());
    }

    @Override
    public void delete(String id) {
        systemNotifyTypeMapper.deleteById(id);
    }
}

