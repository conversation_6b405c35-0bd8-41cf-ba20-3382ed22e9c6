package org.thingsboard.server.service.task.job;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.service.statistics.StatisticsProcessService;
import org.thingsboard.server.service.utils.SpringContextUtils;

/**
 * <AUTHOR>
 * @date 2020/4/15 11:04
 */
@DisallowConcurrentExecution
public class RestApiJob implements Job {


    private StatisticsProcessService statisticsService = (StatisticsProcessService) SpringContextUtils.getBean("statisticsProcessService");

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        RestApiEntity restApiEntity = (RestApiEntity) jobExecutionContext.getJobDetail().getJobDataMap().get(DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_VALUE);
        statisticsService.processRestApi(restApiEntity);
    }


}
