import{d as h,c as s,r as i,o as b,g as k,n as v,q as n,i as a,t as y,p as C,_ as x,aq as V,C as F}from"./index-r0dFAfgr.js";import{C as M}from"./index-CcDafpIP.js";import{r as B}from"./chart-wy3NEK2T.js";const W={class:"onemap-panel-wrapper"},q={class:"table-box"},N=h({__name:"dma",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(l,{emit:r}){const p=r,d=l,c=s(),o=s([{label:"0 个",value:"分区总数"},{label:"0 %",value:"当前产销差"}]),m=i({indexVisible:!0,dataList:[],pagination:{hide:!0},columns:[{minWidth:100,label:"分区名称",prop:"key1"},{minWidth:100,label:"参考产销差",prop:"key2"},{minWidth:100,label:"类型",prop:"key3"}],handleRowClick:e=>{p("highlightMark",d.menu,e==null?void 0:e.id)}}),u=i({group:[{fieldset:{type:"underline",desc:"漏损分布"},fields:[{type:"vchart",option:B(),style:{width:"100%",height:"150px"}}]},{fields:[{type:"input",field:"layer",append:"刷新"}]}],labelPosition:"top",gutter:12});return b(()=>{}),(e,t)=>{const f=x,_=V;return k(),v("div",W,[n(a(M),{modelValue:a(o),"onUpdate:modelValue":t[0]||(t[0]=g=>y(o)?o.value=g:null),span:12},null,8,["modelValue"]),n(f,{ref_key:"refForm",ref:c,config:a(u)},null,8,["config"]),C("div",q,[n(_,{config:a(m)},null,8,["config"])])])}}}),I=F(N,[["__scopeId","data-v-00895340"]]);export{I as default};
