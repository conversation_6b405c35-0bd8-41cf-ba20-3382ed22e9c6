package org.thingsboard.server.dao.maintainCircuit.circuit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskM;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskMMapper;
import org.thingsboard.server.dao.util.imodel.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class CircuitTaskCServiceImpl implements CircuitTaskCService {

    @Autowired
    private CircuitTaskCMapper circuitTaskCMapper;

    @Autowired
    private CircuitTaskMMapper circuitTaskMMapper;

    @Override
    public void save(CircuitTaskC circuitTaskC) {
        circuitTaskC.setTime(new Date());
        circuitTaskCMapper.updateById(circuitTaskC);
        // 是否全部完成
        Map queryMap = new HashMap<>();
        queryMap.put("main_id", circuitTaskC.getMainId());
        List<CircuitTaskC> list = circuitTaskCMapper.selectByMap(queryMap);
        boolean isComplete = true;
        for (CircuitTaskC circuitTaskC1 : list) {
            if ("0".equals(circuitTaskC1.getStatus())) {
                isComplete = false;
            }
        }
        // 是否第一次，第一次则设置实际开始时间
        CircuitTaskM circuitTaskM = circuitTaskMMapper.selectById(circuitTaskC.getMainId());
        if (circuitTaskM != null) {
            if (circuitTaskM.getRealStartTime() == null) {
                circuitTaskM.setRealStartTime(new Date());
            }
            // 接收任务
            circuitTaskM.setStatus("1");

            // 全部完成  任务状态 0 待接收 1已接收 2按时完成 3超时完成 4未完成
            if (true == isComplete) {
                // 是否按时完成
                circuitTaskM.setRealEndTime(new Date());
                if (circuitTaskM.getEndTime() != null && circuitTaskM.getEndTime().getTime() < circuitTaskM.getRealEndTime().getTime()) {
                    circuitTaskM.setStatus("3");
                } else {
                    circuitTaskM.setStatus("2");
                }
            }
            circuitTaskMMapper.updateById(circuitTaskM);
        }
    }

    @Override
    public Map statistics(String deviceLabelCode) {

        Map result = new HashMap();
        // 保养次数
        int count = circuitTaskCMapper.countByLabelCode(deviceLabelCode);
        result.put("count", count);
        // 最近维修
        result.put("latestCircuitTime", "-");
        CircuitTaskC circuitTaskC = circuitTaskCMapper.selectLatestByDeviceLabelCode(deviceLabelCode);
        if (circuitTaskC != null) {
            result.put("latestCircuitTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(circuitTaskC.getCreateTime()));
        }

        // 今年保养情况
        Date nowYear = new Date();
        SimpleDateFormat yyyyFormat = new SimpleDateFormat("yyyy");
        try {
            nowYear = yyyyFormat.parse(yyyyFormat.format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<Map> nowYearRepair = circuitTaskCMapper.getNowYearCircuitByDeviceLabelCode(deviceLabelCode, nowYear);
        result.put("nowYearCircuit", nowYearRepair);

        return result;
    }

    @Override
    public boolean checkUser(String mainId, String userId) {
        CircuitTaskM circuitTaskM = circuitTaskMMapper.selectById(mainId);
        if (circuitTaskM == null || StringUtils.isNullOrEmpty(circuitTaskM.getUserId()) || !circuitTaskM.getUserId().contains(userId)) {
            return false;
        }
        return true;
    }

}
