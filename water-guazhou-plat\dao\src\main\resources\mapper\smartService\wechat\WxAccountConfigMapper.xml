<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxAccountConfigMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           url,
                           token,
                           name,
                           wx_id,
                           app_id,
                           app_secret,
                           account,
                           mch_id,
                           key,
                           notify_url,
                           tenant_id<!--@sql from wx_account_config -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxAccountConfig">
        <result column="id" property="id"/>
        <result column="url" property="url"/>
        <result column="token" property="token"/>
        <result column="name" property="name"/>
        <result column="wx_id" property="wxId"/>
        <result column="app_id" property="appId"/>
        <result column="app_secret" property="appSecret"/>
        <result column="account" property="account"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByTenantId" resultType="org.thingsboard.server.dao.model.sql.smartService.wechat.WxAccountConfig">
        select
        <include refid="Base_Column_List"/>
        from wx_account_config
        where tenant_id = #{tenantId}
    </select>

    <select id="getIdByTenantId" resultType="java.lang.String">
        select id
        from wx_account_config
        where tenant_id = #{tenantId}
    </select>

    <update id="refreshTokenInfo">
        update wx_account_config
        set token       = #{accessToken},
            expire_time = #{expireTime}
        where tenant_id = #{tenantId}
    </update>

    <select id="getTokenInfo" resultType="org.thingsboard.server.dao.model.sql.smartService.wechat.TokenInfo">
        select token, expire_time
        from wx_account_config
        where tenant_id = #{tenantId}
    </select>
</mapper>