package org.thingsboard.server.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.device.DeviceTemplateAndProtocol;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.device.DeviceTemplateService;
import org.thingsboard.server.dao.model.sql.DeviceTemplate;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 设备协议
 */
@Slf4j
@RestController
@RequestMapping("api/deviceTemplate")
public class DeviceTemplateController extends BaseController {

    @Autowired
    private DeviceTemplateService deviceTemplateService;

    @Autowired
    private DeviceController deviceController;

    /**
     * 添加协议模板
     * @param deviceTemplate
     * @return
     * @throws ThingsboardException
     */
    @PostMapping
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_ADD)
    public DeviceTemplate add(@RequestBody DeviceTemplate deviceTemplate) throws ThingsboardException {
        TenantId tenantId = getTenantId();
        deviceTemplate.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        return deviceTemplateService.add(deviceTemplate);
    }

    /**
     * 编辑协议模板
     * @param deviceTemplate
     * @return
     * @throws ThingsboardException
     */
    @PostMapping("edit")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_EDIT)
    public DeviceTemplate edit(@RequestBody DeviceTemplate deviceTemplate) throws ThingsboardException {
        if (StringUtils.isBlank(deviceTemplate.getId())) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        return deviceTemplateService.edit(deviceTemplate);
    }

    /**
     * 查询协议模板
     *
     * @return
     */
    @GetMapping("{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TEMPLATE_GET)
    public DeviceTemplate findById(@PathVariable String id) throws ThingsboardException {
        if (StringUtils.isBlank(id)) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        return deviceTemplateService.findById(id);
    }

    /**
     * 查询协议模板以及模板的协议详情
     *
     * @return
     */
    @GetMapping("protocol/{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TEMPLATE_GET)
    public DeviceTemplateAndProtocol findDeviceTemplateAndProtocolById(@PathVariable String id) throws ThingsboardException {
        if (StringUtils.isBlank(id)) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        return deviceTemplateService.findDeviceTemplateAndProtocolById(id);
    }

    /**
     * 查询协议模板以及模板的协议详情
     *
     * @return
     */
    @GetMapping("protocol/list/{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TEMPLATE_GET)
    public List<JSONObject> findProtocolById(@PathVariable String id) throws ThingsboardException {
        if (StringUtils.isBlank(id)) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        try {
            return deviceTemplateService.findProtocolById(id, getTenantId());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查询协议模板以及模板的协议详情
     *
     * @return
     */
    @GetMapping("protocol/group/{id}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TEMPLATE_GET)
    public Map<String, List<JSONObject>> findDeviceTemplateAndProtocolGroup(@PathVariable String id) throws Exception {
        if (StringUtils.isBlank(id)) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        return deviceTemplateService.findDeviceTemplateAndProtocolGroup(id, getTenantId());
    }

    /**
     * 获取当前登录租户的协议模板
     *
     * @return
     */
    @GetMapping("list")
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TEMPLATE_GET)
    public List<DeviceTemplate> findListByTenant() throws ThingsboardException {
        TenantId tenantId = getTenantId();
        return deviceTemplateService.findDeviceTemplateByTenant(tenantId);
    }

    /**
     * 获取当前登录租户的协议模板
     *
     * @return
     */
    @GetMapping("list/{type}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_TEMPLATE_GET)
    public List<DeviceTemplate> findListByTenant(@PathVariable String type) throws ThingsboardException {
        TenantId tenantId = getTenantId();
        return deviceTemplateService.findDeviceTemplateByTenantAndType(tenantId, type);
    }

    /**
     * 删除协议模板
     * @param id
     * @return
     * @throws ThingsboardException
     */
    @DeleteMapping("{id}")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_DELETE)
    public DeviceTemplate deleteDeviceTemplate(@PathVariable String id) throws ThingsboardException {
        return deviceTemplateService.deleteById(id);
    }

    /**
     * 保存协议模板以及协议详情
     * @param deviceTemplateAndProtocol
     * @throws ThingsboardException
     */
    @PostMapping("save/deviceTemplateAndProtocol")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_ADD)
    public void saveDeviceTemplateAndProtocol(@RequestBody DeviceTemplateAndProtocol deviceTemplateAndProtocol) throws ThingsboardException {
        if (StringUtils.isBlank(deviceTemplateAndProtocol.getId())) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        deviceTemplateService.saveDeviceTemplateAndProtocol(deviceTemplateAndProtocol);

    }

    /**
     * 复制模版
     * @param deviceTemplateId
     * @return
     * @throws ThingsboardException
     */
    @PostMapping("copy/{deviceTemplateId}")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_COPY)
    public Object copyDeviceTemplate(@PathVariable String deviceTemplateId) throws ThingsboardException {
        JSONObject result = new JSONObject();
        deviceTemplateService.copyDeviceTemplate(deviceTemplateId);

        result.put("result", "操作成功");
        return result;
    }


    /**
     * 导出
     */
    @GetMapping(value = "export")
    public Object exportCsv(@RequestParam("id") String deviceTemplateId) {

        List<DeviceTemplate> deviceTemplateList = deviceTemplateService.findByIds(Arrays.asList(deviceTemplateId.split(",")));
        JSONArray dataList = new JSONArray();
        for (DeviceTemplate deviceTemplate : deviceTemplateList) {
            JSONObject data = JSON.parseObject(JSON.toJSONString(deviceTemplate));
            data.remove("id");
            data.remove("createTime");

            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 导入
     */
    @PostMapping("import/{type}")
    public Object importDeviceTemplate(@PathVariable String type, @RequestParam("file") MultipartFile file) throws IOException, ThingsboardException {
        JSONObject result = new JSONObject();
        if (file == null || file.isEmpty()) {
            result.put("message", "上传的文件不能为空!");
            return result;
        }
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.indexOf(".") + 1);
        if (!"json".equalsIgnoreCase(suffix)) {
            result.put("message", "文件格式错误, 请上传.json文件!");
            return result;
        }

        // 读取文件
        StringBuilder stringBuilder = new StringBuilder();
        InputStream inputStream = file.getInputStream();
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String str;
        while ((str = bufferedReader.readLine()) != null) {
            stringBuilder.append(str);
        }
        bufferedReader.close();
        String s = stringBuilder.toString();

        // 保存数据
        List<DeviceTemplate> data = JSON.parseArray(s, DeviceTemplate.class);
        // 查询数据
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
        List<DeviceTemplate> saveList = new ArrayList<>();
        for (DeviceTemplate deviceTemplate : data) {
            if (!type.equalsIgnoreCase(deviceTemplate.getType())) {
                result.put("message", "导入失败，导入的协议类型不匹配");
                return result;
            }
            deviceTemplate.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            deviceTemplate.setCreateTime(System.currentTimeMillis());
            deviceTemplate.setName(deviceTemplate.getName() + " 导入 " + date);

            saveList.add(deviceTemplate);
        }
        deviceTemplateService.saveAll(saveList);

        result.put("message", "导入成功");
        return result;
    }

    /**
     * 导入
     */
    @PostMapping("importExcel/MQTT")
    public IstarResponse importMQTTDeviceTemplateExcel( @RequestParam("file") MultipartFile file) throws IOException, ThingsboardException {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum() + 1;
        DeviceTemplate template = new DeviceTemplate();
        template.setName(file.getName());
        template.setCreateTime(System.currentTimeMillis());
        template.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        template.setType("MQTT");

        List<JSONObject> protocol = new ArrayList<>();
        for (int i = 2; i < lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row.getCell(0) != null)
                row.getCell(0).setCellType(CellType.STRING);
            if (row.getCell(1) != null)
                row.getCell(1).setCellType(CellType.STRING);
            if (row.getCell(2) != null)
                row.getCell(2).setCellType(CellType.STRING);
            if (row.getCell(3) != null)
                row.getCell(3).setCellType(CellType.STRING);
            if (row.getCell(4) != null)
                row.getCell(4).setCellType(CellType.STRING);
            if (row.getCell(5) != null)
                row.getCell(5).setCellType(CellType.STRING);
            if (row.getCell(6) != null)
                row.getCell(6).setCellType(CellType.STRING);
            if (row.getCell(7) != null)
                row.getCell(7).setCellType(CellType.STRING);
            if (row.getCell(8) != null)
                row.getCell(8).setCellType(CellType.STRING);
            if (row.getCell(9) != null)
                row.getCell(9).setCellType(CellType.STRING);
            if (row.getCell(10) != null)
                row.getCell(10).setCellType(CellType.STRING);
            if (row.getCell(11) != null)
                row.getCell(11).setCellType(CellType.STRING);
            if (row.getCell(12) != null)
                row.getCell(12).setCellType(CellType.STRING);
        }
        // 从第二行开始
        for (int i = 2; i < lastRowNum; i++) {
            JSONObject data = new JSONObject();
            Row row = sheet.getRow(i);
            data.put("name", row.getCell(0) == null ? "" : String.valueOf(row.getCell(0)));
            data.put("propertyCategory", row.getCell(1) == null ? "" : String.valueOf(row.getCell(1)));
            data.put("statType", row.getCell(2) == null ? "" : String.valueOf(row.getCell(2)));
            data.put("dataType", row.getCell(3) == null ? "" : String.valueOf(row.getCell(3)));
            data.put("propertyType", row.getCell(4) == null ? "" : String.valueOf(row.getCell(4)));
            data.put("unit", row.getCell(5) == null ? "" : String.valueOf(row.getCell(5)));
            data.put("dataOffset", row.getCell(7) == null ? "" : String.valueOf(row.getCell(7)));
            data.put("samplingMax", 9999999);
            data.put("samplingMin", -9999999);
            data.put("sampleCoef", row.getCell(10) == null ? "" : String.valueOf(row.getCell(10)));
            data.put("unitCoef", row.getCell(11) == null ? "" : String.valueOf(row.getCell(11)));
            data.put("range", row.getCell(12) == null ? "" : String.valueOf(row.getCell(12)));

            protocol.add(data);
        }

        template.setProtocol(JSON.toJSONString(protocol));

        deviceTemplateService.save(template);

        return IstarResponse.ok();
    }

    /**
     * 导入MQTT模板
     */
    @PostMapping("importExcel/batch/MQTT")
    public IstarResponse importMqttBatchDeviceTemplateExcel( @RequestParam("file") MultipartFile file) throws IOException, ThingsboardException {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());

        int numberOfSheets = workbook.getNumberOfSheets();
        List<DeviceTemplate> list = new ArrayList<>();
        for (int n = 0; n < numberOfSheets; n++) {
            Sheet sheet = workbook.getSheetAt(n);
            int lastRowNum = sheet.getLastRowNum() + 1;
            DeviceTemplate template = new DeviceTemplate();
            template.setName(sheet.getSheetName());
            template.setCreateTime(System.currentTimeMillis());
            template.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            template.setType("MQTT");

            List<JSONObject> protocol = new ArrayList<>();
            for (int i = 2; i < lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row.getCell(0) != null)
                    row.getCell(0).setCellType(CellType.STRING);
                if (row.getCell(1) != null)
                    row.getCell(1).setCellType(CellType.STRING);
                if (row.getCell(2) != null)
                    row.getCell(2).setCellType(CellType.STRING);
                if (row.getCell(3) != null)
                    row.getCell(3).setCellType(CellType.STRING);
                if (row.getCell(4) != null)
                    row.getCell(4).setCellType(CellType.STRING);
                if (row.getCell(5) != null)
                    row.getCell(5).setCellType(CellType.STRING);
                if (row.getCell(6) != null)
                    row.getCell(6).setCellType(CellType.STRING);
                if (row.getCell(7) != null)
                    row.getCell(7).setCellType(CellType.STRING);
                if (row.getCell(8) != null)
                    row.getCell(8).setCellType(CellType.STRING);
                if (row.getCell(9) != null)
                    row.getCell(9).setCellType(CellType.STRING);
                if (row.getCell(10) != null)
                    row.getCell(10).setCellType(CellType.STRING);
                if (row.getCell(11) != null)
                    row.getCell(11).setCellType(CellType.STRING);
                if (row.getCell(12) != null)
                    row.getCell(12).setCellType(CellType.STRING);
            }
            // 从第二行开始
            for (int i = 2; i < lastRowNum; i++) {
                JSONObject data = new JSONObject();
                Row row = sheet.getRow(i);
                data.put("name", row.getCell(0) == null ? "" : String.valueOf(row.getCell(0)));
                data.put("propertyCategory", row.getCell(1) == null ? "" : String.valueOf(row.getCell(1)));
                data.put("statType", row.getCell(2) == null ? "" : String.valueOf(row.getCell(2)));
                data.put("propertyType", row.getCell(3) == null ? "" : String.valueOf(row.getCell(3)));
                data.put("unit", row.getCell(4) == null ? "" : String.valueOf(row.getCell(4)));
                data.put("dataOffset", row.getCell(7) == null ? "" : String.valueOf(row.getCell(7)));
                data.put("samplingMax", 9999999);
                data.put("samplingMin", -9999999);
                data.put("sampleCoef", row.getCell(10) == null ? "" : String.valueOf(row.getCell(10)));
                data.put("unitCoef", row.getCell(11) == null ? "" : String.valueOf(row.getCell(11)));
                data.put("range", row.getCell(12) == null ? "" : String.valueOf(row.getCell(12)));

                protocol.add(data);
            }

            template.setProtocol(JSON.toJSONString(protocol));

//            deviceTemplateService.save(template);
            list.add(template);
        }

        deviceTemplateService.saveAll(list);
        return IstarResponse.ok();
    }
}
