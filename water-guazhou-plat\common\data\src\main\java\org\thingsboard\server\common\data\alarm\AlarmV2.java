package org.thingsboard.server.common.data.alarm;

import lombok.Data;

import java.util.Date;

@Data
public class AlarmV2 {

    // id
    private String id;

    // 报警名称
    private String name;

    // 报警类型
    private String type;

    // 监测项
    private String property;

    // 报警次数
    private Long alarmCount;

    // 当前状态
    private String status;

    // 报警时间
    private Date createTime;

    // 处理时间
    private Date endTime;

    // 报警的设备信息
    private String deviceId;
    private String deviceName;

    // 报警等级
    private String level;


}
