import{z as a}from"./index-r0dFAfgr.js";const e=t=>a({url:"/api/spp/dma/partition",method:"post",data:t}),i=t=>a({url:"/api/spp/dma/partition/all",method:"get",params:t}),o=t=>a({url:"/api/spp/dma/partition/list",method:"get",params:t}),p=t=>a({url:"/api/spp/dma/partition/"+t,method:"get"}),n=t=>a({url:"/api/spp/dma/partition",method:"delete",data:t}),s=t=>a({url:"/api/spp/dma/partition/overview",method:"get",params:t});export{n as D,o as G,e as P,i as a,p as b,s as c};
