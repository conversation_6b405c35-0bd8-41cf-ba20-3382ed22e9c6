package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlan;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.PENDING;

@Getter
@Setter
public class CircuitPlanSaveRequest extends SaveRequest<CircuitPlan> {

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    @NotNullOrEmpty
    private String type;

    // 计划类别。周期性任务、固定日期任务
    @NotNullOrEmpty
    private String planType;

    // 计划名称
    @NotNullOrEmpty
    private String name;

    // 巡检模板ID
    @NotNullOrEmpty
    private String templateId;

    // 固定的执行日期。当计划类别为：固定日期任务。该字段有值。
    @NotNullOrEmpty(condition = "isPlanType 固定日期任务")
    private Date fixedDate;

    // 周期开始时间。当计划类别为：周期性任务。该字段有值。
    @NotNullOrEmpty(condition = "isPlanType 周期性任务")
    private Date startDate;

    // 周期结束时间。当计划类别为：周期性任务。该字段有值。
    // @NotNullOrEmpty(condition = "isPlanType 周期性任务")
    // private Date endDate;

    // 执行巡检人员
    @NotNullOrEmpty
    private String executionUserId;

    // 巡检成果审核人员
    @NotNullOrEmpty
    private String auditUserId;

    // 执行天数。当计划类别为：周期性任务。该字段有值。
    @NotNullOrEmpty(condition = "isPlanType 周期性任务")
    private Integer executionDays;

    // 间隔天数。当计划类别为：周期性任务。该字段有值。
    @NotNullOrEmpty(condition = "isPlanType 周期性任务")
    private Integer intervalDays;

    // 执行次数。当计划类别为：周期性任务。该字段有值。
    @NotNullOrEmpty(condition = "isPlanType 周期性任务")
    private Integer executionNum;

    // 要巡检的站点列表，多个用逗号分隔
    private String stationIds;

    // 备注
    private String remark;

    private boolean isPlanType(String type) {
        return type.equals(this.planType);
    }

    @Override
    public String valid(IStarHttpRequest request) {
        if (isCyclic() && executionNum != null && executionNum <= 0)
            return "[周期性任务] 执行次数需要大于或等于0";

        return null;
    }

    private boolean isFixed() {
        return isPlanType("固定日期任务");
    }

    private boolean isCyclic() {
        return isPlanType("周期性任务");
    }

    @Override
    protected CircuitPlan build() {
        CircuitPlan entity = new CircuitPlan();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected CircuitPlan update(String id) {
        CircuitPlan entity = new CircuitPlan();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitPlan entity) {
        entity.setType(type);
        entity.setPlanType(planType);
        entity.setName(name);
        entity.setTemplateId(templateId);
        entity.setFixedDate(fixedDate);
        entity.setStartDate(startDate);
        if (isCyclic())
            entity.setEndDate(new DateTime(startDate).plusDays(executionNum * (executionDays + intervalDays) + executionDays).toDate());
        entity.setExecutionUserId(executionUserId);
        entity.setAuditUserId(auditUserId);
        entity.setExecutionDays(executionDays);
        entity.setIntervalDays(intervalDays);
        entity.setExecutionNum(executionNum);
        entity.setStationIds(stationIds);
        entity.setRemark(remark);
    }

    public List<CircuitTask> generate() {
        String[] stations = stationIds.split(",");
        ArrayList<CircuitTask> result = new ArrayList<>();
        if (isCyclic()) {
            for (String station : stations) {
                List<CircuitTask> slice = QueryUtil.generateSequence(startDate, executionDays, intervalDays, executionNum,
                        (Date fromDate, Date endDate) -> createTask(fromDate, endDate, station));
                result.addAll(slice);
            }
        } else if (isFixed()) {
            for (String station : stations) {
                result.add(createTask(fixedDate, new DateTime(fixedDate).plusDays(7).toDate(), station));
            }
        }

        return result;
    }

    private CircuitTask createTask(Date fromDate, Date endDate, String stationId) {
        CircuitTask task = new CircuitTask();
        task.setType(type);
        task.setName(name);
        task.setTaskType("常规任务");
        task.setExecutionUserId(executionUserId);
        task.setAuditUserId(auditUserId);
        task.setStartTime(fromDate);
        task.setEndTime(endDate);
        task.setStationId(stationId);
        task.setTemplateId(templateId);
        task.setStatus(PENDING.name());
        task.setCreator(currentUserUUID());
        task.setCreateTime(createTime());
        task.setTenantId(tenantId());
        return task;
    }
}