package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;

@Getter
@Setter
public class PlanTaskCompleteRequest implements AwareCurrentUserUUID {
    // id
    private String id;

    // 盘点结果
    private String result;




    private String currentUser;

    @Override
    public void currentUserId(String uuid) {
        currentUser = uuid;
    }
}
