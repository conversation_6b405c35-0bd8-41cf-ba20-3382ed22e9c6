package org.thingsboard.server.dao.util.imodel.response.model;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.session.SqlSession;
import org.springframework.data.domain.PageImpl;
import org.springframework.jdbc.core.JdbcTemplate;
import org.thingsboard.server.common.data.id.UUIDBased;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.statistic.*;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.department.OrganizationMapper;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.response.DepartmentInfo;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.cache.Cache;
import org.thingsboard.server.dao.util.imodel.response.cache.LRUCache;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Closeable;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Supplier;

import static org.thingsboard.server.dao.util.TimeUtils.formatDateTime;

@SuppressWarnings("deprecation")
public class ReturnHelper implements Closeable {
    private static final String SELECT_USERNAME_SQL = "select first_name from tb_user where id = ?";
    @SuppressWarnings("unused")
    private static final String SELECT_TENANT_NAME_SQL = "select title from tenant where id = ?";
    public static final String EMPTY_NAME = "未指定";
    private final JdbcTemplate jdbcTemplate;
    private final int version;
    private Object value;
    private final IModel model;

    private HttpServletRequest request;
    private HttpServletResponse response;
    private JDBCHelper jdbcHelper;

    private final Cache<Class<?>, Method> customizeMapMethodCache = new LRUCache<>(128);
    private final Environment environment;
    private final SqlSession sqlSession;

    private final DepartmentInfo EMPTY_DEPARTMENT_INFO = new DepartmentInfo(null, EMPTY_NAME, null, 0);

    public ReturnHelper(JdbcTemplate jdbcTemplate, SqlSession sqlSession, Environment environment, IModel model, int version) {
        this.environment = environment;
        this.jdbcTemplate = jdbcTemplate;
        this.model = model;
        this.sqlSession = sqlSession;
        this.version = version;
        setValue(model);
    }

    public Method getCustomizeMapMethod(Class<?> clazz) {
        if (customizeMapMethodCache.contains(clazz)) {
            return customizeMapMethodCache.get(clazz);
        }
        Method result = null;
        for (Method method : clazz.getDeclaredMethods()) {
            if (!method.getName().equals("customizeMap"))
                continue;
            result = method;
            break;
        }
        customizeMapMethodCache.put(clazz, result);
        return result;
    }

    @Override
    public void close() {
        if (jdbcHelper != null)
            jdbcHelper.close();
        jdbcHelper = null;
    }

    public int getVersion() {
        return version;
    }

    private class JDBCHelper implements JdbcHelper {
        private final Cache<String, String> localUsernameCache = new LRUCache<>(32);

        private final Cache<String, DepartmentInfo> localDepartmentCache = new LRUCache<>(64);

        // private final Cache<String, String> localTenantNameCache = new LRUCache<>(32);

        @Override
        public JdbcTemplate getJDBCTemplate() {
            return jdbcTemplate;
        }

        public SqlSession getSqlSession() {
            return sqlSession;
        }

        public <T> T getMapper(Class<T> mapperType) {
            return getSqlSession().getMapper(mapperType);
        }

        @Override
        public String resolveUsername(String id) {
            Cache<String, String> usernameCache = environment.getUsernameCache();
            return getValue(id, usernameCache, localUsernameCache,
                    () -> getJDBCTemplate().queryForObject(SELECT_USERNAME_SQL, String.class, id), EMPTY_NAME);
        }

        @Override
        public String resolveTenantName(String id) {
            // Cache<String, String> tenantNameCache = environment.getTenantNameCache();
            // return getString(id, SELECT_TENANT_NAME_SQL, tenantNameCache, localTenantNameCache);
            return null;
        }

        @Override
        public DepartmentInfo resolveDepartmentInfoByUserId(String userId) {
            if (userId == null) {
                return EMPTY_DEPARTMENT_INFO;
            }
            Cache<String, DepartmentInfo> userToDepartmentCache = environment.getUserIdToDepartmentCache();
            DepartmentInfo result = getValue(userId, userToDepartmentCache, localDepartmentCache,
                    () -> {
                        String departmentId = getMapper(DepartmentMapper.class).getDepartmentId(userId);
                        if (departmentId == null) {
                            return null;
                        }
                        return resolveDepartmentInfo(departmentId);
                    }, EMPTY_DEPARTMENT_INFO);
            if (result.isInvalid()) {
                userToDepartmentCache.invalidate(userId);
                return resolveDepartmentInfoByUserId(userId);
            }
            return result;
        }

        @Override
        public DepartmentInfo resolveDepartmentInfo(String departmentId) {
            if (departmentId == null) {
                return EMPTY_DEPARTMENT_INFO;
            }
            Cache<String, DepartmentInfo> departmentToDepartmentCache = environment.getDepartmentIdToDepartmentCache();
            return getValue(departmentId, departmentToDepartmentCache, localDepartmentCache,
                    () -> {
                        DepartmentInfo info = getMapper(DepartmentMapper.class).getInfoById(departmentId);
                        if (info == null || info.getId() == null) {
                            info = getMapper(OrganizationMapper.class).getInfoById(departmentId);
                        }
                        return info;
                    }, EMPTY_DEPARTMENT_INFO);
        }

        public <T> T getValue(String id, Cache<String, T> globalCache, Cache<String, T> localCache,
                              Supplier<T> valueEvaluator, T defaultValue) {
            if (id == null) {
                return defaultValue;
            }
            T value = globalCache.get(id);
            if (value == null) {
                value = localCache.get(id);
            }
            try {
                if (value == null) {
                    value = valueEvaluator.get();
                    if (value == null) {
                        localCache.put(id, defaultValue);
                    } else {
                        globalCache.put(id, value);
                    }
                }
            } catch (Exception ignore) {
                localCache.put(id, defaultValue);
            }
            if (value == null) {
                return defaultValue;
            }
            return value;
        }

        private void close() {

        }
    }


    public Object getValue() {
        if (value instanceof Responsible)
            return ((Responsible) value).postProcess(this, null);
        return value;
    }

    public void setValue(IModel value) {
        this.value = value;
    }


    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    public IModel getModel() {
        return model;
    }

    public Environment getEnvironment() {
        return environment;
    }

    public JdbcHelper jdbcHelper() {
        if (jdbcHelper == null) {
            jdbcHelper = new JDBCHelper();
        }

        return jdbcHelper;
    }

    public Object process(Object o, Object arg) {
        if (isValidResponseEntity(o)) {
            return processValue(o, arg);
        } else if (o instanceof Collection) {
            return processCollection((Collection<?>) o, arg);
        } else if (o != null && o.getClass().isArray() && isValidResponseEntity(o.getClass().getComponentType())) {
            return processArray((Object[]) o, arg);
        } else if (o instanceof PageImpl) {
            return processPage((PageImpl<?>) o, arg);
        } else if (o instanceof IPage) {
            return processIPage(((IPage<?>) o), arg);
        }
        return o;
    }

    private void InvokeCustomizeMap(ResponseMap map, Object entity, Object arg) {
        Method method = getCustomizeMapMethod(entity.getClass());
        if (method == null)
            return;

        Class<?>[] parameterTypes = method.getParameterTypes();
        Object[] parameters = new Object[parameterTypes.length];
        for (int i = 0; i < parameterTypes.length; i++) {
            Class<?> type = parameterTypes[i];
            if (ResponseMap.class.isAssignableFrom(type)) {
                parameters[i] = map;
            } else if (ReturnHelper.class.isAssignableFrom(type)) {
                parameters[i] = this;
            } else if (JdbcHelper.class.isAssignableFrom(type)) {
                parameters[i] = this.jdbcHelper();
            } else if (arg != null && arg.getClass().isAssignableFrom(type)) {
                parameters[i] = arg;
            } else {
                parameters[i] = null;
            }
        }
        ReflectionUtils.callMethod(method, parameters, entity);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private Object processIPage(IPage<?> page, Object arg) {
        PageData pagedata = new PageData();
        pagedata.setData((List) processCollection(page.getRecords(), arg));
        pagedata.setTotal(page.getTotal());
        return pagedata;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private Object processPage(PageImpl<?> page, Object arg) {
        PageData pagedata = new PageData();
        pagedata.setData((List) processCollection(page.getContent(), arg));
        pagedata.setTotal(page.getTotalElements());
        return pagedata;
    }

    private Object processArray(Object[] arr, Object arg) {
        for (int i = 0; i < arr.length; i++) {
            arr[i] = processValue(arr[i], arg);
        }
        return arr;
    }

    private Object processCollection(Collection<?> collection, Object arg) {
        if (collection.isEmpty()) {
            return collection;
        } else if (collection.stream().findFirst().get().getClass().equals(StatisticLong.class)) {
            return processStatisticList((List<?>) collection);
        }

        Optional<?> first = collection.stream().findFirst();
        if (!(isValidResponseEntity(first.get())))
            return collection;

        List<Object> response = new ArrayList<>();
        for (Object o : collection) {
            if (isValidResponseEntity(o))
                response.add(processValue(o, arg));
            else
                response.add(o);
        }
        return response;
    }

    private Object processValue(Object entity, Object arg) {
        if (entity instanceof Responsible) {
            return ((Responsible) entity).postProcess(this, arg);
        } else if (entity instanceof StatisticItem) {
            return processStatisticItem((StatisticItem) entity);
        }

        return convertToMap(entity, arg);
    }

    public ResponseMap convertToMap(Object entity, Object arg) {
        ResponseMap map = new ResponseMap(entity, this);
        InvokeCustomizeMap(map, entity, arg);
        return map;
    }

    private boolean isValidResponseEntity(Object o) {
        return o != null && isValidResponseEntity(o.getClass());
    }

    private boolean isValidResponseEntity(Class<?> clazz) {
        return clazz.isAnnotationPresent(ResponseEntity.class) || Responsible.class.isAssignableFrom(clazz) || StatisticItem.class.isAssignableFrom(clazz);
    }

    // region 统计工具
    private Object processStatisticItem(StatisticItem value) {
        if (value instanceof ScopeStatisticLongWrapper) {
            return processScopeStatisticLongWrapper((ScopeStatisticLongWrapper) value);
        } else if (value instanceof TimedStatisticLongWrapperSplitter) {
            return processTimedStatisticLongWrapperSplitter((TimedStatisticLongWrapperSplitter) value);
        } else if (value instanceof MultiTimedStatisticLongWrapper) {
            return processMultiTimedStatisticLongWrapper((MultiTimedStatisticLongWrapper) value);
        }

        throw ExceptionUtils.createSilentThrow("StatisticItem type have not support: %s", value.getClass().getName());
    }

    private Object processScopeStatisticLongWrapper(ScopeStatisticLongWrapper wrapper) {
        return processStatisticList(wrapper.getData());
    }

    private Object processTimedStatisticLongWrapperSplitter(TimedStatisticLongWrapperSplitter splitter) {
        List<Object> result = new ArrayList<>();
        for (Map.Entry<String, List<TimedStatisticLongWrapper>> entry : splitter.getRoutes().entrySet()) {
            Object processedObj = processStatisticList(entry.getValue());
            // 无需处理
            if (processedObj instanceof List<?>) {
                ResponseMap map = new ResponseMap();
                map.put("data", processedObj);
                return map;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) processedObj;
            map.put("key", entry.getKey());
            result.add(map);
        }
        return result;
    }


    private ResponseMap processMultiTimedStatisticLongWrapper(MultiTimedStatisticLongWrapper wrapper) {
        ResponseMap map = new ResponseMap();
        List<StatisticLong> statisticLongs = wrapper.getStatisticLongs();
        Object processedValue = processStatisticList(statisticLongs);
        if (processedValue == statisticLongs) {
            map.put("from", formatDateTime(wrapper.getFrom()));
            map.put("to", formatDateTime(wrapper.getTo()));
            map.put("total", 0);
            map.put("data", statisticLongs);
            return map;
        }

        map = (ResponseMap) processedValue;
        map.put("from", formatDateTime(wrapper.getFrom()));
        map.put("to", formatDateTime(wrapper.getTo()));
        return map;
    }

    @SuppressWarnings("unchecked")
    private Object processStatisticList(List<?> list) {
        ResponseMap map = new ResponseMap();
        Iterator<?> iterator = list.iterator();
        Object next = iterator.hasNext() ? iterator.next() : null;
        if (next == null) {
            return list;
        }

        if (next instanceof MultiTimedStatisticLongWrapper) {
            List<Object> processedList = new ArrayList<>();
            while (next != null) {
                processedList.add(processMultiTimedStatisticLongWrapper((MultiTimedStatisticLongWrapper) next));
                next = iterator.hasNext() ? iterator.next() : null;
            }
            return processedList;
        } else if (next instanceof Statisticable) {
            // 处理StatisticLong
            resolveStatisticable(map, next, iterator, list);
        } else if (next instanceof List) {
            List<Object> processedList = new ArrayList<>();
            while (next != null) {
                processedList.add(processStatisticList((List<Object>) next));
                next = iterator.hasNext() ? iterator.next() : null;
            }
            map.put("data", processedList);
        } else {
            return list;
        }

        return map;
    }

    private void resolveStatisticable(ResponseMap map, Object next, Iterator<?> iterator, List<?> list) {
        // region 计算Total
        long total = 0L;
        while (next != null) {
            total += ((Statisticable) next).getValue();
            next = iterator.hasNext() ? iterator.next() : null;
        }
        // endregion
        List<Object> data = new ArrayList<>();
        for (Object item : list) {
            data.add(process(item, total));
        }
        map.put("total", total);
        map.put("data", data);
    }
    // endregion

}
