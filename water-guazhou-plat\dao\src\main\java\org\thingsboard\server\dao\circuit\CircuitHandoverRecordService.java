package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitHandoverRecordSaveRequest;

public interface CircuitHandoverRecordService {
    /**
     * 分页条件查询交接班日志
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitHandoverRecord> findAllConditional(CircuitHandoverRecordPageRequest request);

    /**
     * 保存交接班日志
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitHandoverRecord save(CircuitHandoverRecordSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitHandoverRecord entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
