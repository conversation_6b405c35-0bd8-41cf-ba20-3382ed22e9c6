{"widgetLabel": "天气", "widgetLabelAccessible": "天气微件", "weatherType": "天气类型", "error": {"unsupported": "仅 SceneView 支持天气。", "localScene": "天气仅适用于全球场景。", "notVisible": "天气仅在低海拔区域可见。", "noAtmosphere": "仅在启用大气时才支持天气。"}, "sunny": {"label": "晴", "cloudCover": "云量"}, "cloudy": {"label": "多云", "cloudCover": "云量"}, "rainy": {"label": "雨", "cloudCover": "云量", "precipitation": "降雨量"}, "snowy": {"label": "雪", "cloudCover": "云量", "precipitation": "降雨量", "snowCover": "降雪量", "snowCoverTooltip": "使用雪覆盖表面(底图除外)。"}, "foggy": {"label": "雾", "fogStrength": "雾密度"}}