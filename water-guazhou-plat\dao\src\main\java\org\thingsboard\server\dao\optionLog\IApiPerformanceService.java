package org.thingsboard.server.dao.optionLog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.ApiPerformance;
import org.thingsboard.server.dao.util.imodel.query.base.ApiPerformancePageRequest;

import java.util.List;

/**
 * 公共平台-服务监控Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IApiPerformanceService {
    /**
     * 查询公共平台-服务监控
     *
     * @param id 公共平台-服务监控主键
     * @return 公共平台-服务监控
     */
    public ApiPerformance selectApiPerformanceById(String id);

    /**
     * 查询公共平台-服务监控列表
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 公共平台-服务监控集合
     */
    public IPage<ApiPerformance> selectApiPerformanceList(ApiPerformancePageRequest apiPerformance);

    /**
     * 新增公共平台-服务监控
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 结果
     */
    public int insertApiPerformance(ApiPerformance apiPerformance);

    /**
     * 修改公共平台-服务监控
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 结果
     */
    public int updateApiPerformance(ApiPerformance apiPerformance);

    /**
     * 批量删除公共平台-服务监控
     *
     * @param ids 需要删除的公共平台-服务监控主键集合
     * @return 结果
     */
    public int deleteApiPerformanceByIds(List<String> ids);

    /**
     * 删除公共平台-服务监控信息
     *
     * @param id 公共平台-服务监控主键
     * @return 结果
     */
    public int deleteApiPerformanceById(String id);
}
