package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsDepreciationEntity;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsDepreciationRepository;

import java.math.RoundingMode;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
@Service
@Slf4j
public class AssetsDepreciationServiceImpl implements AssetsDepreciationService{
    @Autowired
    private AssetsDepreciationRepository assetsDepreciationRepository;
    @Override
    public PageData getPage(JSONObject params) {
        int page = 0;
        int limit = 10;
        String deviceNo = "";
        String deviceName = "";
        String depreciationMethod = "";
        Long start = 0l;
        Long end = System.currentTimeMillis();
        String projectId = "";
        if (StringUtils.isNotBlank(params.getString("deviceNo"))) {
            deviceNo = params.getString("deviceNo");
        }
        deviceNo = "%" + deviceNo + "%";

        if (StringUtils.isNotBlank(params.getString("deviceName"))) {
            deviceName = params.getString("deviceName");
        }
        deviceName = "%" + deviceName + "%";

        if (StringUtils.isNotBlank(params.getString("depreciationMethod"))) {
            depreciationMethod = params.getString("depreciationMethod");
        }
        depreciationMethod = "%" + depreciationMethod + "%";

        if (params.getInteger("page") != null) {
            page = params.getInteger("page") - 1;
        }

        if (params.getInteger("limit") != null) {
            limit = params.getInteger("limit");
        }

        if (params.getLong("start") != null) {
            start = params.getLong("start");
        }

        if (params.getLong("end") != null) {
            end = params.getLong("end");
        }
        if (params.getString("projectId") != null) {
            projectId = params.getString("projectId");
        }
        projectId = "%" + projectId + "%";

        String tenantId = params.getString("tenantId");
        PageRequest pageRequest = new PageRequest(page, limit, new Sort(Sort.Direction.DESC, "createTime"));

        Page<AssetsDepreciationEntity> assetsDepreciationEntities = assetsDepreciationRepository.findPage(deviceNo, deviceName, depreciationMethod, tenantId, projectId, start, end, pageRequest);

        assetsDepreciationEntities.getContent().forEach(a -> {
            a.setNetValue(a.getOriginValue().subtract(a.getDepreciationTotal()).setScale(2, RoundingMode.HALF_UP));
        });

        PageData pageData = new PageData(assetsDepreciationEntities.getTotalElements(), assetsDepreciationEntities.getContent());
        return pageData;
    }
}
