<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.DmaCostMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaCost">
        select a.*, b.label as typeName, c.first_name as creatorName
        from tb_pipe_dma_cost a
        left join tb_pipe_dma_dict b on a.type = b.value and b.type = '1'
        left join tb_user c on a.creator = c.id
        <where>
            <if test="param.partitionIdList != null and param.partitionIdList.size() > 0">
                and a.partition_id in
                <foreach collection="param.partitionIdList" item="partitionId" open="(" separator="," close=")">
                    #{partitionId}
                </foreach>
            </if>
            <if test="param.type != null and param.type != ''">
                and a.type like '%' || #{param.type} || '%'
            </if>
            <if test="param.creatorName != null and param.creatorName != ''">
                and c.first_name like '%' || #{param.creatorName} || '%'
            </if>
            <if test="param.start != null">
                and a.create_time >= to_timestamp(#{param.start}::bigint/ 1000)
            </if>
            <if test="param.end != null">
                and a.create_time &lt;= to_timestamp(#{param.end}::bigint/ 1000)
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>