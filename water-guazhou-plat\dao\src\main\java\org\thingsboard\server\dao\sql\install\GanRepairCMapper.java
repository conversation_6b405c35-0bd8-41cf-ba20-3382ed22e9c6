package org.thingsboard.server.dao.sql.install;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.install.GanRepairC;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-07-24
 */
@Mapper
public interface GanRepairCMapper extends BaseMapper<GanRepairC> {

    @Select("select a.*, b.first_name as creatorName, " +
            " case when a.process_user is null or a.process_user = '' then b.first_name else a.process_user end as processUser " +
            " from tb_gan_repair_c a left join tb_user b on a.creator = b.id where a.pid = #{pid} order by step_no ")
    List<GanRepairC> getList(@Param("pid") String pid);
}
