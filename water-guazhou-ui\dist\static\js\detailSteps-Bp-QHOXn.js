import{u as E,d as U,c as S,r as f,am as R,g as n,n as d,q as v,F as _,aB as N,aJ as I,h as x,i as A,dw as C,dx as O,C as P}from"./index-r0dFAfgr.js";/* empty css                */var c;(c=E().user)!=null&&c.name;const h=l=>[{label:"发起",value:"PENDING"},{label:"派单",value:"ASSIGN"},{label:"接单",value:"RESOLVING"},{label:"到场",value:"ARRIVING"},{label:"处理",value:"PROCESSING"},{label:"完成",value:"SUBMIT"},{label:"审核通过",value:"APPROVED"},{label:"审核退回",value:"REJECTED"}],G=l=>{switch(l){case"CREATE":return[[{label:"标题",prop:"title",cols:5}],[{label:"发起人员",prop:"organizerName"},{label:"紧急程度",prop:"level"},{label:"来源",prop:"source"}],[{label:"类型",prop:"type"},{label:"处理级别",prop:"processLevel"},{label:"上报人电话",prop:"uploadPhone"}],[{label:"地址",prop:"address",cols:5}],[{label:"描述",prop:"remark",cols:5}],[{label:"直接分派",prop:"directDispatch",formatter:r=>r.directDispatch===!0?"是":"否"},{label:"预计完成时间",prop:"estimatedFinishTime",cols:3}],[{label:"图片",prop:"imgUrl",image:!0,cols:5}],[{label:"录音",prop:"audioUrl",audio:!0,cols:5}],[{label:"视频",prop:"videoUrl",video:!0,cols:5}],[{label:"附件",prop:"otherFileUrl",file:!0,cols:5}]];case"ARRIVING":case"PROCESSING":case"TERMINATED":return[[{label:"处理人",prop:"nextProcessUserName"}],[{label:"备注",prop:"processRemark"}]];case"SUBMIT":return[[{label:"完成人",prop:"processUserName"},{label:"指定审核人",prop:"nextProcessUserName"}],[{label:"备注",prop:"processRemark",cols:3}]];case"HANDOVER_REVIEW":return[[{label:"申请人",prop:"processUserName"},{label:"转发至",prop:"nextProcessUserName"},{label:"指定审核人",prop:"expectUsername"}],[{label:"备注",prop:"processRemark",cols:5}]];case"CHARGEBACK_REVIEW":return[[{label:"申请人",prop:"processUserName"}],[{label:"审核人",prop:"nextProcessUserName"}]];case"REVIEW":return[[{label:"指定复审人",prop:"nextProcessUserName"}],[{label:"备注",prop:"processRemark"}]];case"APPROVED":case"REJECTED":case"CHARGEBACK":return[[{label:"审核人",prop:"processUserName"}],[{label:"审核备注",prop:"processRemark"}]];case"REASSIGN":return[[{label:"处理人",prop:"nextProcessUserName"}]];default:return[]}},g={class:"detailSteps"},D=U({__name:"detailSteps",props:{config:{}},setup(l){const r=l,p=S(0),a=f({OrderStatus:[],CurStatus:void 0}),i=()=>{var o;let e=h();a.CurStatus=e.find(s=>s.value===r.config.status),((o=a.CurStatus)==null?void 0:o.value)==="REJECTED"?e=e.filter(s=>s.value!=="APPROVED"):e=e.filter(s=>s.value!=="REJECTED"),e.findIndex(s=>s.value===r.config.status)===-1?a.OrderStatus=[{perm:!0,text:r.config.statusName}]:a.OrderStatus=e.map(s=>({perm:!0,text:s.label})),u()};function u(){var e;p.value=0;for(let t=0;t<a.OrderStatus.length;t++){if(((e=a.CurStatus)==null?void 0:e.label)===a.OrderStatus[t].text)return;p.value+=1}}return R(()=>r.config.status,()=>{i()}),(e,t)=>{const o=C,s=O;return n(),d("div",g,[v(s,{class:"steps",active:A(p),"finish-status":"finish",simple:"","process-status":"success"},{default:_(()=>[(n(!0),d(N,null,I(a.OrderStatus,(m,b)=>(n(),x(o,{key:b,title:m.text||""},null,8,["title"]))),128))]),_:1},8,["active"])])}}}),k=P(D,[["__scopeId","data-v-c19ad0a4"]]),B=Object.freeze(Object.defineProperty({__proto__:null,default:k},Symbol.toStringTag,{value:"Module"}));export{B as a,k as d,G as i};
