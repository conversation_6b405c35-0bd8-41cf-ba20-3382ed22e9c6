<template>
  <div class="title-card" :class="size">
    <TitleHeader :title="title" :title-width="titleWidth" :size="size">
      <template #title>
        <slot name="title"> </slot>
      </template>
      <template #right>
        <slot name="title-right"></slot>
      </template>
    </TitleHeader>
    <div class="title-card__main">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import TitleHeader from './TitleHeader.vue';

defineProps<{
  title: string;
  /**
   * 标题占位宽度，默认200,大概能包含6个汉字，最小124,即不能包含汉字，请自行根据具体情况调整宽度
   */
  titleWidth?: number;
  size?: 'small';
}>();
</script>
<style lang="scss" scoped>
.title-card {
  border: 1px solid transparent;
  border-radius: 8px;

  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(
      244.59deg,
      rgba(1, 27, 76, 0.8) 0%,
      rgba(1, 27, 76, 0.8) 100%
    ),
    linear-gradient(30deg, transparent 20%, #4d94ff 100%);
  backdrop-filter: blur(4px);
  &.small {
    .title-card__main {
      height: calc(100% - 24px);
    }
  }
}
.title-card__main {
  height: calc(100% - 36px);
  width: 100%;
}
</style>
