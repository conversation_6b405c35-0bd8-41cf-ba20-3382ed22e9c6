"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6748,9880],{66748:(e,t,n)=>{n.r(t),n.d(t,{registerFunctions:()=>_});var r=n(40330),o=n(77286),s=n(61363),a=n(71201),i=n(6570),l=n(9361),c=n(8667),u=n(65091),f=n(94139),h=n(38913),d=n(58901),m=n(33955),g=n(27535),y=n(33586),w=n(28228),p=n(26520),v=n(67900);function b(e){return 0===r.i8.indexOf("4.")?h.Z.fromExtent(e):new h.Z({spatialReference:e.spatialReference,rings:[[[e.xmin,e.ymin],[e.xmin,e.ymax],[e.xmax,e.ymax],[e.xmax,e.ymin],[e.xmin,e.ymin]]]})}function A(e,t,n){if((0,s.y)(e,2,2,t,n),e[0]instanceof l.Z&&e[1]instanceof l.Z);else if(e[0]instanceof l.Z&&null===e[1]);else if(e[1]instanceof l.Z&&null===e[0]);else if(null!==e[0]||null!==e[1])throw new g.aV(t,g.rH.InvalidParameter,n)}async function k(e,t){if("polygon"!==e.type&&"polyline"!==e.type&&"extent"!==e.type)return 0;let n=1;(e.spatialReference.vcsWkid||e.spatialReference.latestVcsWkid)&&(n=(0,a._R)(e.spatialReference)/(0,v.c9)(e.spatialReference));let r=0;if("polyline"===e.type)for(const t of e.paths)for(let e=1;e<t.length;e++)r+=(0,a.AW)(t[e],t[e-1],n);else if("polygon"===e.type)for(const t of e.rings){for(let e=1;e<t.length;e++)r+=(0,a.AW)(t[e],t[e-1],n);(t[0][0]!==t[t.length-1][0]||t[0][1]!==t[t.length-1][1]||void 0!==t[0][2]&&t[0][2]!==t[t.length-1][2])&&(r+=(0,a.AW)(t[0],t[t.length-1],n))}else"extent"===e.type&&(r+=2*(0,a.AW)([e.xmin,e.ymin,0],[e.xmax,e.ymin,0],n),r+=2*(0,a.AW)([e.xmin,e.ymin,0],[e.xmin,e.ymax,0],n),r*=2,r+=4*Math.abs((0,s.A)(e.zmax,0)*n-(0,s.A)(e.zmin,0)*n));const o=new d.Z({hasZ:!1,hasM:!1,spatialReference:e.spatialReference,paths:[[0,0],[0,r]]});return(0,c.sz)(o,t)}function _(e){"async"===e.mode&&(e.functions.disjoint=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null===o[0]||null===o[1]||(0,c.ED)(o[0],o[1]))))},e.functions.intersects=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null!==o[0]&&null!==o[1]&&(0,c.kK)(o[0],o[1]))))},e.functions.touches=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null!==o[0]&&null!==o[1]&&(0,c.W4)(o[0],o[1]))))},e.functions.crosses=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null!==o[0]&&null!==o[1]&&(0,c.jU)(o[0],o[1]))))},e.functions.within=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null!==o[0]&&null!==o[1]&&(0,c.uh)(o[0],o[1]))))},e.functions.contains=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null!==o[0]&&null!==o[1]&&(0,c.r3)(o[0],o[1]))))},e.functions.overlaps=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null!==o[0]&&null!==o[1]&&(0,c.Nm)(o[0],o[1]))))},e.functions.equals=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>((0,s.y)(o,2,2,t,n),o[0]===o[1]||(o[0]instanceof l.Z&&o[1]instanceof l.Z?(0,c.fS)(o[0],o[1]):!(!(0,s.k)(o[0])||!(0,s.k)(o[1]))&&o[0].equals(o[1])))))},e.functions.relate=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>{if(o=(0,s.G)(o),(0,s.y)(o,3,3,t,n),o[0]instanceof l.Z&&o[1]instanceof l.Z)return(0,c.LV)(o[0],o[1],(0,s.j)(o[2]));if(o[0]instanceof l.Z&&null===o[1])return!1;if(o[1]instanceof l.Z&&null===o[0])return!1;if(null===o[0]&&null===o[1])return!1;throw new g.aV(t,g.rH.InvalidParameter,n)}))},e.functions.intersection=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>(A(o=(0,s.G)(o),t,n),null===o[0]||null===o[1]?null:(0,c.wf)(o[0],o[1]))))},e.functions.union=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{const i=[];if(0===(a=(0,s.G)(a)).length)throw new g.aV(t,g.rH.WrongNumberOfParameters,n);if(1===a.length)if((0,s.m)(a[0])){const e=(0,s.G)(a[0]);for(let r=0;r<e.length;r++)if(null!==e[r]){if(!(e[r]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);i.push(e[r])}}else{if(!(0,s.x)(a[0])){if(a[0]instanceof l.Z)return(0,s.q)((0,o.r1)(a[0]),t.spatialReference);if(null===a[0])return null;throw new g.aV(t,g.rH.InvalidParameter,n)}{const e=(0,s.G)(a[0].toArray());for(let r=0;r<e.length;r++)if(null!==e[r]){if(!(e[r]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);i.push(e[r])}}}else for(let e=0;e<a.length;e++)if(null!==a[e]){if(!(a[e]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);i.push(a[e])}return 0===i.length?null:(0,c.G0)(i)}))},e.functions.difference=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>(A(a=(0,s.G)(a),t,n),null!==a[0]&&null===a[1]?(0,o.r1)(a[0]):null===a[0]?null:(0,c.e5)(a[0],a[1]))))},e.functions.symmetricdifference=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>(A(a=(0,s.G)(a),t,n),null===a[0]&&null===a[1]?null:null===a[0]?(0,o.r1)(a[1]):null===a[1]?(0,o.r1)(a[0]):(0,c.Sp)(a[0],a[1]))))},e.functions.clip=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>{if(o=(0,s.G)(o),(0,s.y)(o,2,2,t,n),!(o[1]instanceof i.Z)&&null!==o[1])throw new g.aV(t,g.rH.InvalidParameter,n);if(null===o[0])return null;if(!(o[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return null===o[1]?null:(0,c.oq)(o[0],o[1])}))},e.functions.cut=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,2,t,n),!(a[1]instanceof d.Z)&&null!==a[1])throw new g.aV(t,g.rH.InvalidParameter,n);if(null===a[0])return[];if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return null===a[1]?[(0,o.r1)(a[0])]:(0,c.z7)(a[0],a[1])}))},e.functions.area=function(t,n){return e.standardFunctionAsync(t,n,(async(e,r,a)=>{if((0,s.y)(a,1,2,t,n),null===(a=(0,s.G)(a))[0])return 0;if((0,s.T)(a[0])){const e=await a[0].sumArea((0,o.EI)((0,s.A)(a[1],-1)),!1,t.abortSignal);if(t.abortSignal.aborted)throw new g.aV(t,g.rH.Cancelled,n);return e}if((0,s.m)(a[0])||(0,s.x)(a[0])){const e=(0,s.J)(a[0],t.spatialReference);return null===e?0:(0,c.CJ)(e,(0,o.EI)((0,s.A)(a[1],-1)))}if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.CJ)(a[0],(0,o.EI)((0,s.A)(a[1],-1)))}))},e.functions.areageodetic=function(t,n){return e.standardFunctionAsync(t,n,(async(e,r,a)=>{if((0,s.y)(a,1,2,t,n),null===(a=(0,s.G)(a))[0])return 0;if((0,s.T)(a[0])){const e=await a[0].sumArea((0,o.EI)((0,s.A)(a[1],-1)),!0,t.abortSignal);if(t.abortSignal.aborted)throw new g.aV(t,g.rH.Cancelled,n);return e}if((0,s.m)(a[0])||(0,s.x)(a[0])){const e=(0,s.J)(a[0],t.spatialReference);return null===e?0:(0,c.Y4)(e,(0,o.EI)((0,s.A)(a[1],-1)))}if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.Y4)(a[0],(0,o.EI)((0,s.A)(a[1],-1)))}))},e.functions.length=function(t,n){return e.standardFunctionAsync(t,n,(async(e,r,a)=>{if((0,s.y)(a,1,2,t,n),null===(a=(0,s.G)(a))[0])return 0;if((0,s.T)(a[0])){const e=await a[0].sumLength((0,o.Lz)((0,s.A)(a[1],-1)),!1,t.abortSignal);if(t.abortSignal.aborted)throw new g.aV(t,g.rH.Cancelled,n);return e}if((0,s.m)(a[0])||(0,s.x)(a[0])){const e=(0,s.H)(a[0],t.spatialReference);return null===e?0:(0,c.sz)(e,(0,o.Lz)((0,s.A)(a[1],-1)))}if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.sz)(a[0],(0,o.Lz)((0,s.A)(a[1],-1)))}))},e.functions.length3d=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if((0,s.y)(a,1,2,t,n),null===(a=(0,s.G)(a))[0])return 0;if((0,s.m)(a[0])||(0,s.x)(a[0])){const e=(0,s.H)(a[0],t.spatialReference);return null===e?0:!0===e.hasZ?k(e,(0,o.Lz)((0,s.A)(a[1],-1))):(0,c.sz)(e,(0,o.Lz)((0,s.A)(a[1],-1)))}if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return!0===a[0].hasZ?k(a[0],(0,o.Lz)((0,s.A)(a[1],-1))):(0,c.sz)(a[0],(0,o.Lz)((0,s.A)(a[1],-1)))}))},e.functions.lengthgeodetic=function(t,n){return e.standardFunctionAsync(t,n,(async(e,r,a)=>{if((0,s.y)(a,1,2,t,n),null===(a=(0,s.G)(a))[0])return 0;if((0,s.T)(a[0])){const e=await a[0].sumLength((0,o.Lz)((0,s.A)(a[1],-1)),!0,t.abortSignal);if(t.abortSignal.aborted)throw new g.aV(t,g.rH.Cancelled,n);return e}if((0,s.m)(a[0])||(0,s.x)(a[0])){const e=(0,s.H)(a[0],t.spatialReference);return null===e?0:(0,c.kQ)(e,(0,o.Lz)((0,s.A)(a[1],-1)))}if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.kQ)(a[0],(0,o.Lz)((0,s.A)(a[1],-1)))}))},e.functions.distance=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{a=(0,s.G)(a),(0,s.y)(a,2,3,t,n);let i=a[0];((0,s.m)(a[0])||(0,s.x)(a[0]))&&(i=(0,s.K)(a[0],t.spatialReference));let u=a[1];if(((0,s.m)(a[1])||(0,s.x)(a[1]))&&(u=(0,s.K)(a[1],t.spatialReference)),!(i instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);if(!(u instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.TE)(i,u,(0,o.Lz)((0,s.A)(a[2],-1)))}))},e.functions.distancegeodetic=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{a=(0,s.G)(a),(0,s.y)(a,2,3,t,n);const i=a[0],l=a[1];if(!(i instanceof f.Z))throw new g.aV(t,g.rH.InvalidParameter,n);if(!(l instanceof f.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const u=new d.Z({paths:[],spatialReference:i.spatialReference});return u.addPath([i,l]),(0,c.kQ)(u,(0,o.Lz)((0,s.A)(a[2],-1)))}))},e.functions.densify=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,3,t,n),null===a[0])return null;if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const u=(0,s.g)(a[1]);if(isNaN(u))throw new g.aV(t,g.rH.InvalidParameter,n);if(u<=0)throw new g.aV(t,g.rH.InvalidParameter,n);return a[0]instanceof h.Z||a[0]instanceof d.Z?(0,c.Cz)(a[0],u,(0,o.Lz)((0,s.A)(a[2],-1))):a[0]instanceof i.Z?(0,c.Cz)(b(a[0]),u,(0,o.Lz)((0,s.A)(a[2],-1))):a[0]}))},e.functions.densifygeodetic=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,3,t,n),null===a[0])return null;if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const u=(0,s.g)(a[1]);if(isNaN(u))throw new g.aV(t,g.rH.InvalidParameter,n);if(u<=0)throw new g.aV(t,g.rH.InvalidParameter,n);return a[0]instanceof h.Z||a[0]instanceof d.Z?(0,c.j2)(a[0],u,(0,o.Lz)((0,s.A)(a[2],-1))):a[0]instanceof i.Z?(0,c.j2)(b(a[0]),u,(0,o.Lz)((0,s.A)(a[2],-1))):a[0]}))},e.functions.generalize=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,4,t,n),null===a[0])return null;if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const i=(0,s.g)(a[1]);if(isNaN(i))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.D$)(a[0],i,(0,s.h)((0,s.A)(a[2],!0)),(0,o.Lz)((0,s.A)(a[3],-1)))}))},e.functions.buffer=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,3,t,n),null===a[0])return null;if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const i=(0,s.g)(a[1]);if(isNaN(i))throw new g.aV(t,g.rH.InvalidParameter,n);return 0===i?(0,o.r1)(a[0]):(0,c.f3)(a[0],i,(0,o.Lz)((0,s.A)(a[2],-1)))}))},e.functions.buffergeodetic=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,3,t,n),null===a[0])return null;if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const i=(0,s.g)(a[1]);if(isNaN(i))throw new g.aV(t,g.rH.InvalidParameter,n);return 0===i?(0,o.r1)(a[0]):(0,c.rd)(a[0],i,(0,o.Lz)((0,s.A)(a[2],-1)))}))},e.functions.offset=function(t,n){return e.standardFunctionAsync(t,n,((e,r,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,2,6,t,n),null===a[0])return null;if(!(a[0]instanceof h.Z||a[0]instanceof d.Z))throw new g.aV(t,g.rH.InvalidParameter,n);const i=(0,s.g)(a[1]);if(isNaN(i))throw new g.aV(t,g.rH.InvalidParameter,n);const l=(0,s.g)((0,s.A)(a[4],10));if(isNaN(l))throw new g.aV(t,g.rH.InvalidParameter,n);const u=(0,s.g)((0,s.A)(a[5],0));if(isNaN(u))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.cv)(a[0],i,(0,o.Lz)((0,s.A)(a[2],-1)),(0,s.j)((0,s.A)(a[3],"round")).toLowerCase(),l,u)}))},e.functions.rotate=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>{o=(0,s.G)(o),(0,s.y)(o,2,3,t,n);let a=o[0];if(null===a)return null;if(!(a instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);a instanceof i.Z&&(a=h.Z.fromExtent(a));const u=(0,s.g)(o[1]);if(isNaN(u))throw new g.aV(t,g.rH.InvalidParameter,n);const d=(0,s.A)(o[2],null);if(null===d)return(0,c.U1)(a,u);if(d instanceof f.Z)return(0,c.U1)(a,u,d);throw new g.aV(t,g.rH.InvalidParameter,n)}))},e.functions.centroid=function(t,n){return e.standardFunctionAsync(t,n,((e,r,c)=>{if(c=(0,s.G)(c),(0,s.y)(c,1,1,t,n),null===c[0])return null;let m=c[0];if(((0,s.m)(c[0])||(0,s.x)(c[0]))&&(m=(0,s.K)(c[0],t.spatialReference)),null===m)return null;if(!(m instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return m instanceof f.Z?(0,s.q)((0,o.r1)(c[0]),t.spatialReference):m instanceof h.Z?m.centroid:m instanceof d.Z?(0,a.s9)(m):m instanceof u.Z?(0,a.Ay)(m):m instanceof i.Z?m.center:null}))},e.functions.multiparttosinglepart=function(t,n){return e.standardFunctionAsync(t,n,(async(e,r,a)=>{a=(0,s.G)(a),(0,s.y)(a,1,1,t,n);const y=[];if(null===a[0])return null;if(!(a[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);if(a[0]instanceof f.Z)return[(0,s.q)((0,o.r1)(a[0]),t.spatialReference)];if(a[0]instanceof i.Z)return[(0,s.q)((0,o.r1)(a[0]),t.spatialReference)];const w=await(0,c.og)(a[0]);if(w instanceof h.Z){const e=[],t=[];for(let n=0;n<w.rings.length;n++)if(w.isClockwise(w.rings[n])){const t=(0,m.im)({rings:[w.rings[n]],hasZ:!0===w.hasZ,hazM:!0===w.hasM,spatialReference:w.spatialReference.toJSON()});e.push(t)}else t.push({ring:w.rings[n],pt:w.getPoint(n,0)});for(let n=0;n<t.length;n++)for(let r=0;r<e.length;r++)if(e[r].contains(t[n].pt)){e[r].addRing(t[n].ring);break}return e}if(w instanceof d.Z){const e=[];for(let t=0;t<w.paths.length;t++){const n=(0,m.im)({paths:[w.paths[t]],hasZ:!0===w.hasZ,hazM:!0===w.hasM,spatialReference:w.spatialReference.toJSON()});e.push(n)}return e}if(a[0]instanceof u.Z){const e=(0,s.q)((0,o.r1)(a[0]),t.spatialReference);for(let t=0;t<e.points.length;t++)y.push(e.getPoint(t));return y}return null}))},e.functions.issimple=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>{if(o=(0,s.G)(o),(0,s.y)(o,1,1,t,n),null===o[0])return!0;if(!(o[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.Gg)(o[0])}))},e.functions.simplify=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>{if(o=(0,s.G)(o),(0,s.y)(o,1,1,t,n),null===o[0])return null;if(!(o[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.og)(o[0])}))},e.functions.convexhull=function(t,n){return e.standardFunctionAsync(t,n,((e,r,o)=>{if(o=(0,s.G)(o),(0,s.y)(o,1,1,t,n),null===o[0])return null;if(!(o[0]instanceof l.Z))throw new g.aV(t,g.rH.InvalidParameter,n);return(0,c.JI)(o[0])}))},e.functions.getuser=function(t,n){return e.standardFunctionAsync(t,n,(async(e,r,o)=>{(0,s.y)(o,0,2,t,n);let a=(0,s.A)(o[1],""),i=!0===a;if(a=!0===a||!1===a?"":(0,s.j)(a),0===o.length||o[0]instanceof w.Z){let e=null;t.services&&t.services.portal&&(e=t.services.portal),o.length>0&&(e=(0,p._)(o[0],e));const n=await(0,p.q)(e,a,i);if(n){const e=JSON.parse(JSON.stringify(n));for(const t of["lastLogin","created","modified"])void 0!==e[t]&&null!==e[t]&&(e[t]=new Date(e[t]));return y.Z.convertObjectToArcadeDictionary(e,(0,s.C)(t))}return null}let l=null;if((0,s.T)(o[0])&&(l=o[0]),l){if(i=!1,a)return null;await l.load();const e=await l.getOwningSystemUrl();if(!e){if(!a){const e=await l.getIdentityUser();return e?y.Z.convertObjectToArcadeDictionary({username:e},(0,s.C)(t)):null}return null}let n=null;t.services&&t.services.portal&&(n=t.services.portal),n=(0,p._)(new w.Z(e),n);const r=await(0,p.q)(n,a,i);if(r){const e=JSON.parse(JSON.stringify(r));for(const t of["lastLogin","created","modified"])void 0!==e[t]&&null!==e[t]&&(e[t]=new Date(e[t]));return y.Z.convertObjectToArcadeDictionary(e,(0,s.C)(t))}return null}throw new g.aV(t,g.rH.InvalidParameter,n)}))})}},26520:(e,t,n)=>{n.d(t,{_:()=>a,q:()=>i});var r=n(40330),o=n(3172),s=n(65587);function a(e,t){return null===e?t:new s.Z({url:e.field("url")})}async function i(e,t,n){const s=r.id?.findCredential(e.restUrl);if(!s)return null;if("loaded"===e.loadStatus&&""===t&&e.user&&e.user.sourceJSON&&!1===n)return e.user.sourceJSON;if(""===t){const t=await(0,o.default)(e.restUrl+"/community/self",{responseType:"json",query:{f:"json",...!1===n?{}:{returnUserLicenseTypeExtensions:!0}}});if(t.data){const e=t.data;if(e&&e.username)return e}return null}const a=await(0,o.default)(e.restUrl+"/community/users/"+t,{responseType:"json",query:{f:"json"}});if(a.data){const e=a.data;return e.error?null:e}return null}},99880:(e,t,n)=>{n.d(t,{V:()=>l});var r=n(68773),o=(n(3172),n(20102)),s=n(92604),a=n(17452);const i=s.Z.getLogger("esri.assets");function l(e){if(!r.Z.assetsPath)throw i.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new o.Z("assets:path-not-set","config.assetsPath is not set");return(0,a.v_)(r.Z.assetsPath,e)}},64830:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(70586);class o{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,r.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,r.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},80903:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(50758),o=n(92604),s=n(95330),a=n(64830),i=n(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new a.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((n,r)=>{let o=!0;const a=e=>{(0,s.k_)(t.signal),o&&(o=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let o=0;o<e.length;++o){const l=e[o];(0,s.y8)(l)?this._clientPromises[o]=l.then((e=>(this._clients[o]=new i.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),a(n),this._clients[o])),(()=>(a(r),null))):(this._clients[o]=new i.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[o]=Promise.resolve(this._clients[o]),a(n))}}))}broadcast(e,t,n){const r=new Array(this._clientPromises.length);for(let o=0;o<this._clientPromises.length;++o){const s=this._clientPromises[o];r[o]=s.then((r=>r?.invoke(e,t,n)))}return r}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,s.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,n){let r;Array.isArray(n)?(o.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),r={transferList:n}):r=n;const a=(0,s.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:r,deferred:a});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return a.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,r.AL)(this._clients.map((n=>n.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let n=t.length;for(let r=0;r<this._clientPromises.length;++r)this._clientPromises[r].then((o=>{o&&(t[r]=o.openPort()),0==--n&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,n)=>{n.d(t,{bA:()=>z});var r=n(20102),o=n(80442),s=n(95330),a=n(80903),i=n(25045),l=n(40330),c=n(92604),u=n(70586),f=n(94362),h=n(99880),d=n(68773),m=(n(2587),n(17452));const g={};function y(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=g.baseUrl),t}var w=n(41213);class p{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...n)=>e[t](...n)}))}}class v{constructor(){this._dispatcher=new p,this._workerPostMessage({type:f.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,w.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,n){this._dispatcher.addEventListener(e,t,n)}removeEventListener(e,t,n){this._dispatcher.removeEventListener(e,t,n)}_workerPostMessage(e){(0,w.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,f.QM)(e);if(t&&t.type===f.Cs.OPEN){const{modulePath:e,jobId:n}=t;let r=await i.default.loadWorker(e);r||(r=await import(e));const o=i.default.connect(r);this._workerPostMessage({type:f.Cs.OPENED,jobId:n,data:o})}}}var b=n(70171),A=n(17202);const k=c.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:_}=f.Cs;let Z,P;const L="Failed to create Worker. Fallback to execute module in main thread";async function I(e){return new Promise((t=>{function n(o){const s=(0,f.QM)(o);s&&s.type===_&&(e.removeEventListener("message",n),e.removeEventListener("error",r),t(e))}function r(t){t.preventDefault(),e.removeEventListener("message",n),e.removeEventListener("error",r),k.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new v).addEventListener("message",n),e.addEventListener("error",r)}e.addEventListener("message",n),e.addEventListener("error",r)}))}function E(){let e;if(null!=d.Z.default){const t={...d.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(d.Z));e.assetsPath=(0,m.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,m.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,b.Kd)(),e.has={"esri-csp-restrictions":(0,o.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,o.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,o.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,o.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,o.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,o.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,o.Z)("esri-atomics"),"esri-shared-array-buffer":(0,o.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,o.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,o.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,o.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,o.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,m.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,m.hF)(e.workers.workerPath):e.workers.workerPath=(0,m.hF)((0,h.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=d.Z.workers.loaderConfig,n=y({baseUrl:t?.baseUrl,locale:(0,b.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),r={version:l.i8,buildDate:A.r,revision:A.$};return JSON.stringify({esriConfig:e,loaderConfig:n,kernelInfo:r})}let H=0;const{ABORT:S,INVOKE:N,OPEN:R,OPENED:M,RESPONSE:C}=f.Cs;class O{static async create(e){const t=await async function(){if(!(0,o.Z)("esri-workers")||((0,o.Z)("mozilla"),0))return I(new v);if(!Z&&!P)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${E()}'`);Z=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){P=e||{}}let e;if(Z)try{e=new Worker(Z,{name:"esri-worker-"+H++})}catch(t){k.warn(L,P),e=new v}else k.warn(L,P),e=new v;return I(e)}();return new O(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),c.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:n}=t,r=(0,f.jt)();return new Promise(((t,o)=>{const a={resolve:t,reject:o,abortHandle:(0,s.$F)(n,(()=>{this._outJobs.delete(r),this._post({type:S,jobId:r})}))};this._outJobs.set(r,a),this._post({type:R,jobId:r,modulePath:e})}))}_onMessage(e){const t=(0,f.QM)(e);if(t)switch(t.type){case M:this._onOpenedMessage(t);break;case C:this._onResponseMessage(t);break;case S:this._onAbortMessage(t);break;case N:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,n=e.jobId,r=t.get(n);r&&(r.controller&&r.controller.abort(),t.delete(n))}_onInvokeMessage(e){const{methodName:t,jobId:n,data:r,abortable:o}=e,a=o?new AbortController:null,i=this._inJobs,c=l.Nv[t];let u;try{if("function"!=typeof c)throw new TypeError(`${t} is not a function`);u=c.call(null,r,{signal:a?a.signal:null})}catch(e){return void this._post({type:C,jobId:n,error:(0,f.AB)(e)})}(0,s.y8)(u)?(i.set(n,{controller:a,promise:u}),u.then((e=>{i.has(n)&&(i.delete(n),this._post({type:C,jobId:n},e))}),(e=>{i.has(n)&&(i.delete(n),e||(e={message:"Error encountered at method"+t}),(0,s.D_)(e)||this._post({type:C,jobId:n,error:(0,f.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:C,jobId:n},u)}_onOpenedMessage(e){const{jobId:t,data:n}=e,r=this._outJobs.get(t);r&&(this._outJobs.delete(t),(0,u.hw)(r.abortHandle),r.resolve(n))}_onResponseMessage(e){const{jobId:t,error:n,data:o}=e,s=this._outJobs.get(t);s&&(this._outJobs.delete(t),(0,u.hw)(s.abortHandle),n?s.reject(r.Z.fromJSON(JSON.parse(n))):s.resolve(o))}_post(e,t,n){return(0,f.oi)(this.worker,e,t,n)}}let V=(0,o.Z)("esri-workers-debug")?1:(0,o.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,o.Z)("host-browser")?navigator.hardwareConcurrency-1:0;V||(V=(0,o.Z)("safari")&&(0,o.Z)("mac")?7:2);let F=0;const x=[];async function j(e,t){const n=new a.Z;return await n.open(e,t),n}async function z(e,t={}){if("string"!=typeof e)throw new r.Z("workers:undefined-module","modulePath is missing");let n=t.strategy||"distributed";if((0,o.Z)("host-webworker")&&!(0,o.Z)("esri-workers")&&(n="local"),"local"===n){let n=await i.default.loadWorker(e);n||(n=await import(e)),(0,s.k_)(t.signal);const r=t.client||n;return j([i.default.connect(n)],{...t,client:r})}if(await async function(){if(J)return J;D=new AbortController;const e=[];for(let t=0;t<V;t++){const n=O.create(t).then((e=>(x[t]=e,e)));e.push(n)}return J=Promise.all(e),J}(),(0,s.k_)(t.signal),"dedicated"===n){const n=F++%V;return j([await x[n].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const n=Math.min(t.maxNumWorkers,V);if(n<V){const r=new Array(n);for(let o=0;o<n;++o){const n=F++%V;r[o]=x[n].open(e,t)}return j(r,t)}}return j(x.map((n=>n.open(e,t))),t)}let D,J=null},8667:(e,t,n)=>{n.d(t,{CJ:()=>V,Cz:()=>C,D$:()=>M,ED:()=>v,G0:()=>E,Gg:()=>k,JI:()=>Z,LV:()=>A,Nm:()=>b,Sp:()=>L,TE:()=>m,U1:()=>R,W4:()=>w,Y4:()=>x,cv:()=>H,e5:()=>P,f3:()=>S,fS:()=>g,j2:()=>O,jU:()=>d,kK:()=>y,kQ:()=>j,og:()=>_,oq:()=>u,r3:()=>h,rd:()=>N,sz:()=>F,uh:()=>p,wf:()=>I,z7:()=>f}),n(66577);var r=n(78346),o=(n(94139),n(33955));function s(e){return Array.isArray(e)?e[0]?.spatialReference:e?.spatialReference}function a(e){return e?Array.isArray(e)?e.map(a):e.toJSON?e.toJSON():e:e}function i(e){return Array.isArray(e)?e.map((e=>(0,o.im)(e))):(0,o.im)(e)}let l;async function c(e,t){return(await async function(){return l||(l=(0,r.bA)("geometryEngineWorker",{strategy:"distributed"})),l}()).invoke("executeGEOperation",{operation:e,parameters:a(t)})}async function u(e,t){return i(await c("clip",[s(e),e,t]))}async function f(e,t){return i(await c("cut",[s(e),e,t]))}function h(e,t){return c("contains",[s(e),e,t])}function d(e,t){return c("crosses",[s(e),e,t])}function m(e,t,n){return c("distance",[s(e),e,t,n])}function g(e,t){return c("equals",[s(e),e,t])}function y(e,t){return c("intersects",[s(e),e,t])}function w(e,t){return c("touches",[s(e),e,t])}function p(e,t){return c("within",[s(e),e,t])}function v(e,t){return c("disjoint",[s(e),e,t])}function b(e,t){return c("overlaps",[s(e),e,t])}function A(e,t,n){return c("relate",[s(e),e,t,n])}function k(e){return c("isSimple",[s(e),e])}async function _(e){return i(await c("simplify",[s(e),e]))}async function Z(e,t=!1){return i(await c("convexHull",[s(e),e,t]))}async function P(e,t){return i(await c("difference",[s(e),e,t]))}async function L(e,t){return i(await c("symmetricDifference",[s(e),e,t]))}async function I(e,t){return i(await c("intersect",[s(e),e,t]))}async function E(e,t=null){const n=function(e,t){let n;return Array.isArray(e)?n=e:(n=[],n.push(e),null!=t&&n.push(t)),n}(e,t);return i(await c("union",[s(n),n]))}async function H(e,t,n,r,o,a){return i(await c("offset",[s(e),e,t,n,r,o,a]))}async function S(e,t,n,r=!1){const o=[s(e),e,t,n,r];return i(await c("buffer",o))}async function N(e,t,n,r,o,a){const l=[s(e),e,t,n,r,o,a];return i(await c("geodesicBuffer",l))}async function R(e,t,n){if(null==e)throw new z;const r=e.spatialReference;if(null==(n=n??function(e){return"xmin"in e?e.center:"x"in e?e:e.extent?.center}(e)))throw new z;const o=e.constructor.fromJSON(await c("rotate",[r,e,t,n]));return o.spatialReference=r,o}async function M(e,t,n,r){return i(await c("generalize",[s(e),e,t,n,r]))}async function C(e,t,n){return i(await c("densify",[s(e),e,t,n]))}async function O(e,t,n,r=0){return i(await c("geodesicDensify",[s(e),e,t,n,r]))}function V(e,t){return c("planarArea",[s(e),e,t])}function F(e,t){return c("planarLength",[s(e),e,t])}function x(e,t,n){return c("geodesicArea",[s(e),e,t,n])}function j(e,t,n){return c("geodesicLength",[s(e),e,t,n])}class z extends Error{constructor(){super("Illegal Argument Exception")}}},2587:(e,t,n)=>{n(90344),n(18848),n(940),n(70171);var r=n(94443),o=n(3172),s=n(20102),a=n(70586);async function i(e){if((0,a.pC)(l.fetchBundleAsset))return l.fetchBundleAsset(e);const t=await(0,o.default)(e,{responseType:"text"});return JSON.parse(t.data)}const l={};var c,u=n(99880);(0,r.tz)((c={pattern:"esri/",location:u.V},new class{constructor({base:e="",pattern:t,location:n=new URL(window.location.href)}){let r;r="string"==typeof n?e=>new URL(e,new URL(n,window.location.href)).href:n instanceof URL?e=>new URL(e,n).href:n,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=r,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,n,o){const a=t.exec(n);if(!a)throw new s.Z("esri-intl:invalid-bundle",`Bundle id "${n}" is not compatible with the pattern "${t}"`);const l=a[1]?`${a[1]}/`:"",c=a[2],u=(0,r.Su)(o),f=`${l}${c}.json`,h=u?`${l}${c}_${u}.json`:f;let d;try{d=await i(e(h))}catch(t){if(h===f)throw new s.Z("intl:unknown-bundle",`Bundle "${n}" cannot be loaded`,{error:t});try{d=await i(e(f))}catch(e){throw new s.Z("intl:unknown-bundle",`Bundle "${n}" cannot be loaded`,{error:e})}}return d}(this.getAssetUrl,this.matcher,e,t)}}(c)))},90344:(e,t,n)=>{n.d(t,{Ze:()=>y,p6:()=>w});var r=n(35454),o=n(70171);const s={year:"numeric",month:"numeric",day:"numeric"},a={year:"numeric",month:"long",day:"numeric"},i={year:"numeric",month:"short",day:"numeric"},l={year:"numeric",month:"long",weekday:"long",day:"numeric"},c={hour:"numeric",minute:"numeric"},u={...c,second:"numeric"},f={"short-date":s,"short-date-short-time":{...s,...c},"short-date-short-time-24":{...s,...c,hour12:!1},"short-date-long-time":{...s,...u},"short-date-long-time-24":{...s,...u,hour12:!1},"short-date-le":s,"short-date-le-short-time":{...s,...c},"short-date-le-short-time-24":{...s,...c,hour12:!1},"short-date-le-long-time":{...s,...u},"short-date-le-long-time-24":{...s,...u,hour12:!1},"long-month-day-year":a,"long-month-day-year-short-time":{...a,...c},"long-month-day-year-short-time-24":{...a,...c,hour12:!1},"long-month-day-year-long-time":{...a,...u},"long-month-day-year-long-time-24":{...a,...u,hour12:!1},"day-short-month-year":i,"day-short-month-year-short-time":{...i,...c},"day-short-month-year-short-time-24":{...i,...c,hour12:!1},"day-short-month-year-long-time":{...i,...u},"day-short-month-year-long-time-24":{...i,...u,hour12:!1},"long-date":l,"long-date-short-time":{...l,...c},"long-date-short-time-24":{...l,...c,hour12:!1},"long-date-long-time":{...l,...u},"long-date-long-time-24":{...l,...u,hour12:!1},"long-month-year":{month:"long",year:"numeric"},"short-month-year":{month:"short",year:"numeric"},year:{year:"numeric"},"short-time":c,"long-time":u},h=(0,r.w)()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"}),d=(h.apiValues,h.toJSON.bind(h),h.fromJSON.bind(h),{ar:"ar-u-nu-latn-ca-gregory"});let m=new WeakMap,g=f["short-date-short-time"];function y(e){return e?f[e]:null}function w(e,t){return function(e){const t=e||g;let n=m.get(t);if(!n){const e=(0,o.Kd)(),r=d[(0,o.Kd)()]||e;n=new Intl.DateTimeFormat(r,t),m.set(t,n)}return n}(t).format(e)}(0,o.Ze)((()=>{m=new WeakMap,g=f["short-date-short-time"]}))},94443:(e,t,n)=>{n.d(t,{ME:()=>d,Su:()=>m,tz:()=>h});var r=n(20102),o=n(95330),s=n(70171);const a=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,i={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function l(e){return i[e]??!1}const c=[],u=new Map;function f(e){for(const t of u.keys())g(e.pattern,t)&&u.delete(t)}function h(e){return c.includes(e)||(f(e),c.unshift(e)),{remove(){const t=c.indexOf(e);t>-1&&(c.splice(t,1),f(e))}}}async function d(e){const t=(0,s.Kd)();u.has(e)||u.set(e,async function(e,t){const n=[];for(const r of c)if(g(r.pattern,e))try{return await r.fetchMessageBundle(e,t)}catch(e){n.push(e)}if(n.length)throw new r.Z("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:n});throw new r.Z("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}(e,t));const n=u.get(e);return n&&await y.add(n),n}function m(e){if(!a.test(e))return null;const t=a.exec(e);if(null===t)return null;const[,n,r]=t,o=n+(r?"-"+r.toUpperCase():"");return l(o)?o:l(n)?n:null}function g(e,t){return"string"==typeof e?t.startsWith(e):e.test(t)}(0,s.Ze)((()=>{u.clear()}));const y=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then((()=>this._decrease()),(()=>this._decrease())),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=(0,o.dD)())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&0===this._numLoading&&(this._dfd.resolve(),this._dfd=null)}}},18848:(e,t,n)=>{n.d(t,{sh:()=>l,uf:()=>c});var r=n(70586),o=n(70171);const s={ar:"ar-u-nu-latn"};let a=new WeakMap,i={};function l(e={}){const t={};return null!=e.digitSeparator&&(t.useGrouping=e.digitSeparator),null!=e.places&&(t.minimumFractionDigits=t.maximumFractionDigits=e.places),t}function c(e,t){return-0===e&&(e=0),function(e){const t=e||i;if(!a.has(t)){const n=(0,o.Kd)(),r=s[(0,o.Kd)()]||n;a.set(t,new Intl.NumberFormat(r,e))}return(0,r.j0)(a.get(t))}(t).format(e)}(0,o.Ze)((()=>{a=new WeakMap,i={}}))},940:(e,t,n)=>{n.d(t,{n:()=>c});var r=n(92604),o=n(78286),s=n(19153),a=n(90344),i=n(18848);const l=r.Z.getLogger("esri.intl.substitute");function c(e,t,n={}){const{format:r={}}=n;return(0,s.gx)(e,(e=>function(e,t,n){let r,s;const a=e.indexOf(":");if(-1===a?r=e.trim():(r=e.slice(0,a).trim(),s=e.slice(a+1).trim()),!r)return"";const i=(0,o.hS)(r,t);if(null==i)return"";const l=(s?n?.[s]:null)??n?.[r];return l?u(i,l):s?f(i,s):h(i)}(e,t,r)))}function u(e,t){switch(t.type){case"date":return(0,a.p6)(e,t.intlOptions);case"number":return(0,i.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),h(e)}}function f(e,t){switch(t.toLowerCase()){case"dateformat":return(0,a.p6)(e);case"numberformat":return(0,i.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,a.p6)(e):/^numberformat/i.test(t)?(0,i.uf)(e):h(e)}}function h(e){switch(typeof e){case"string":return e;case"number":return(0,i.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,a.p6)(e):""}}}}]);