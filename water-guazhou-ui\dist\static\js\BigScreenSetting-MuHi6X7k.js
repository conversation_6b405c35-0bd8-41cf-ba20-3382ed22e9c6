const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/sysPage-CAXRM0Om.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/css/sysPage-DbeBt3nc.css"])))=>i.map(i=>d[i]);
import{d as V,c as m,u as k,a8 as C,o as b,g as f,n as P,p as u,aB as h,aJ as I,G as x,bh as E,q as p,i as N,F as A,b as d,a3 as D,cg as R,ch as F,a2 as J,H as L,E as T,J as M,C as O}from"./index-r0dFAfgr.js";/* empty css                                                                         */const Y={class:"wrapper"},q={class:"big-container-data"},z={class:"data-row"},G={class:"data-row-span"},H=V({__name:"BigScreenSetting",setup(Z){const c=m([]),v=m(k().tenantId),l=C(()=>k().roles[0]==="SYS_ADMIN"),_=m([{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"sysPage1"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"sysPage2"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"sysPage3"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"sysPage4"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"sysPage5"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"sysPage6"}]),S=m([{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"tenantPage1"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"tenantPage2"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"tenantPage3"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"tenantPage4"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"tenantPage5"},{name:"",url:"",routerUrl:"/extendPage/sysPage/",state:!1,key:"tenantPage6"}]),U=()=>{console.log(c.value,"----s");const r={},a=[],s={},o={};for(const t of c.value)if(t.state){if(t.name===""||t.url===""){d.error("开启的菜单 名称 地址 不可为空");return}s[t.name]=t.key,o[t.url]=t.key}for(const[t,e]of c.value.entries())if(e.state){if(o[e.url]&&o[e.url]!==e.key||s[e.name]&&s[e.name]!==e.key){console.log(e,s,o,"item"),d.error("开启的菜单 名称 地址 不可重复");return}if(!/^[0-9a-zA-Z\u4e00-\u9fa5]{1,8}$/.test(e.name)){d.error("菜单名称应在1到8个字符");return}if(/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?/g.test(e.url)){const g={name:e.name,state:e.state},y=encodeURIComponent(e.url);g.url=e.routerUrl+btoa(y),r[e.key]=g;const w={path:g.url,name:e.key,component:()=>D(()=>import("./sysPage-CAXRM0Om.js"),__vite__mapDeps([0,1,2,3])),meta:{title:e.name,icon:"icon-daping"}};a.push(w)}else{const y="开启的 页面"+(t+1)+" url地址格式不正确";d.error(y);return}}else{const n={name:e.name,state:e.state,url:e.url};r[e.key]=n}const i={type:l.value?"sysBigScreen":"tenantBigScreen",key:l.value?"sys_admin":v.value,value:JSON.stringify(r)};R([i]).then(()=>{const t={router:a,currentSYS:l.value};F().setBigScreen(t),d.success("保存成功 稍后生效"),location.reload(),console.log(a,"----newRouter")})},B=()=>{const r=l.value?_.value:S.value,a={type:l.value?"sysBigScreen":"tenantBigScreen",key:l.value?"sys_admin":v.value};J(a).then(s=>{var i;if(!((i=s.data)!=null&&i.length))return;const o=JSON.parse(s.data[0].value);for(const t of r){const e=o[t.key];if(t.name=e.name,t.state=e.state,e.state){const n=e.url.split("/extendPage/sysPage/")[1];t.url=decodeURIComponent(atob(n))}else t.url=e.url}c.value=r,console.log(r)})};return b(()=>{c.value=l.value?_.value:S.value,B()}),(r,a)=>{const s=L,o=T,i=M;return f(),P("div",Y,[a[4]||(a[4]=u("p",{class:"p-title-tips"}," 开启菜单 名称 地址 不可为空 不可重复 ",-1)),u("div",q,[(f(!0),P(h,null,I(N(c),(t,e)=>(f(),P("div",{key:e},[u("div",z,[u("span",G,[a[0]||(a[0]=u("i",{class:"iconfont icon-yemian"},null,-1)),x(" 页面"+E(e+1),1)]),a[1]||(a[1]=u("span",{class:"row-label"},"菜单名称",-1)),p(s,{modelValue:t.name,"onUpdate:modelValue":n=>t.name=n,placeholder:"请输入名称",class:"row-name-input"},null,8,["modelValue","onUpdate:modelValue"]),a[2]||(a[2]=u("span",{class:"row-label"},"地址",-1)),p(s,{modelValue:t.url,"onUpdate:modelValue":n=>t.url=n,placeholder:"请输入地址",class:"row-url-input"},null,8,["modelValue","onUpdate:modelValue"]),p(o,{modelValue:t.state,"onUpdate:modelValue":n=>t.state=n,"active-text":"开启","inactive-text":"关闭","inactive-color":"#C0CCDA","active-color":"#409EFF"},null,8,["modelValue","onUpdate:modelValue"])])]))),128))]),p(i,{class:"edit-primary-blue",onClick:U},{default:A(()=>a[3]||(a[3]=[x(" 保存 ")])),_:1})])}}}),Q=O(H,[["__scopeId","data-v-daa2a621"]]);export{Q as default};
