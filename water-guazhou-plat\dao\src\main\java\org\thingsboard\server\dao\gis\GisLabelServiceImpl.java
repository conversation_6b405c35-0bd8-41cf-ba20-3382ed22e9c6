package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisLabelListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisExceptionUpload;
import org.thingsboard.server.dao.model.sql.gis.GisLabel;
import org.thingsboard.server.dao.sql.gis.GisLabelMapper;
import org.thingsboard.server.dao.user.UserService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GisLabelServiceImpl implements GisLabelService {

    @Autowired
    private GisLabelMapper gisLabelMapper;

    @Autowired
    private UserService userService;

    @Override
    public PageData<GisLabel> findList(GisLabelListRequest request, TenantId tenantId) {
        Page<GisLabel> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<GisLabel> pageResult = gisLabelMapper.findList(pageRequest, request);

        // 查询用户列表
        List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(request.getTenantId())));
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));

        for (GisLabel record : pageResult.getRecords()) {
            // 上报人
            String createuser = record.getCreateuser();
            if (StringUtils.isNotBlank(createuser)) {
                User user = userMap.get(createuser);
                if (user != null) {
                    record.setCreateuserName(user.getFirstName());
                }
            }
        }

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(GisLabel entity, User currentUser) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateuser(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
            entity.setCreatetime(new Date());
            entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

            gisLabelMapper.insert(entity);
        } else {
            entity.setUpdatetime(new Date());

            gisLabelMapper.updateById(entity);
        }
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            gisLabelMapper.deleteById(id);
        }
    }
}
