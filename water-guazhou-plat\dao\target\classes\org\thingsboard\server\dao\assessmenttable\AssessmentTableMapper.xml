<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.assessmenttable.AssessmentTableMapper">

    <sql id="Base_Column_List">
        id, tenant_id, name, region, partition, period, assessment_type, assessment_level, 
        status, total_score, reviewer, review_time, remark, creator, create_time, search_text
    </sql>

    <!-- 分页查询考核表列表 -->
    <select id="selectAssessmentTables" resultType="org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableEntity">
        SELECT
        <include refid="Base_Column_List" />
        FROM assessment_table
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            <if test="searchText != null and searchText != ''">
                AND search_text ILIKE CONCAT('%', #{searchText}, '%')
            </if>
            <if test="assessmentType != null and assessmentType != ''">
                AND assessment_type = #{assessmentType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="period != null and period != ''">
                AND period = #{period}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据ID查询考核表 -->
    <select id="selectById" resultType="org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableEntity">
        SELECT
        <include refid="Base_Column_List" />
        FROM assessment_table
        WHERE id = #{id}
    </select>
    
    <!-- 批量删除考核表 -->
    <delete id="batchDeleteByIds">
        DELETE FROM assessment_table
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 