<!-- 统一工单-我的工单-我上报的 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm_Stop"
      :config="FormConfig_Stop"
    ></DialogForm>
    <SLDrawer
      ref="refdetail"
      :config="detailConfig"
    >
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { Download, Refresh, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { useUserStore } from '@/store'
import { formatDate } from '@/utils/DateFormatter'
import { SLMessage } from '@/utils/Message'
import OrderStepTagsVue from './components/OrderStepTags.vue'
import detail from './components/detail.vue'
import {
  formatWorkOrderStatus,
  getFromOptions,
  // getOrderList,
  getOrderTypeOptions,
  StatusForDisableStop,
  WorkOrderStatus
} from './config'
import { GetWorkOrderPage, TerminateWorkOrder, getWorkOrderEmergencyLevelList } from '@/api/workorder'
import { formatterDate, traverse } from '@/utils/GlobalHelper'
import { removeSlash } from '@/utils/removeIdSlash'
import useStopWorkOrder from './hooks/useStopWorkOrder'

const refForm_Stop = ref<IDialogFormIns>()
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()
const refdetail = ref<ISLDrawerIns>()
// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  group: [],
  cancel: false,
  modalClass: 'lightColor'
})

const selectedId = ref<string>('')

const state = reactive<{
  WorkOrderEmergencyLevelList: any[],
}>({
  WorkOrderEmergencyLevelList: []
})

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then(res => {
    state.WorkOrderEmergencyLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'id' })
  })
}

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '类别',
      labelWidth: 40,
      field: 'organizerId',
      options: [
        { label: '我上报的', value: 'organizerId' },
        { label: '抄送我的', value: 'ccUserId' }
      ],
      onChange: val => {
        const perms = val === 'organizerId' ? ['详情', '终止'] : ['详情']
        const permWidth = val === 'organizerId' ? 100 : 80
        TableConfig.operationWidth = permWidth
        TableConfig.operations?.map(item => {
          const text: string = item.text as string
          item.perm = perms.indexOf(text) !== -1
        })
        refreshData()
      }
    },
    { type: 'input', label: '标题', field: 'title', onChange: () => refreshData() },
    {
      sm: 24,
      xl: 24,
      type: 'radio-button',
      label: '工单状态',
      field: 'status',
      options: WorkOrderStatus(true),
      formatter: formatWorkOrderStatus
    },
    { type: 'daterange', label: '发起时间', field: 'date' },
    { type: 'select', label: '来源', field: 'source', options: getFromOptions() },
    {
      type: 'select-tree',
      label: '类型',
      field: 'type',
      options: getOrderTypeOptions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            // SearchConfig.defaultParams = {}
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ],
  defaultParams: {
    organizerId: 'organizerId',
    date: [moment().subtract(1, 'M').format(formatterDate), moment().format(formatterDate)]
  },
  handleSearch: () => refreshData()
})
const TableConfig = reactive<ICardTable>({
  expandable: true,
  expandComponent: shallowRef(OrderStepTagsVue),
  defaultExpandAll: true,
  columns: [
    { label: '工单编号', prop: 'serialNo' },
    { label: '来源', prop: 'source' },
    { label: '类型', prop: 'type' },
    { label: '标题', prop: 'title' },
    { label: '状态', prop: 'statusName' },
    {
      label: '发起时间',
      prop: 'createTime',
      formatter: row => formatDate(row.createTime)
    },
    {
      label: '紧急程度',
      prop: 'level',
      tag: true,
      tagColor: (row): string => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.color || '',
      formatter: row => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.label
    }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {

      perm: true,
      isTextBtn: true,
      text: '详情',
      click: row => handleDetail(row)
    },
    {
      perm: true,
      isTextBtn: true,
      text: '终止',
      disabled: (row: any): boolean => StatusForDisableStop.includes(row.status),
      click: row => {
        TableConfig.currentRow = row
        refForm_Stop.value?.openDialog()
      }
    }
  ]
})

const { FormConfig_Stop } = useStopWorkOrder(async (params: any) => {
  const res = await TerminateWorkOrder(TableConfig.currentRow?.id, params)
  if (res.data.code === 200) {
    SLMessage.success('操作成功')
    refreshData()
    refForm_Stop.value?.closeDialog()
  } else {
    SLMessage.error(res.data.err || '操作失败')
  }
})
const handleDetail = (row: any) => {
  selectedId.value = row.id || ''
  detailConfig.title = row.serialNo
  refdetail.value?.openDrawer()
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
}
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2 ? [moment(query.date[0], formatterDate).valueOf(), moment(query.date[1], formatterDate).endOf('D').valueOf()] : [moment().subtract(1, 'M').startOf('D').valueOf(), moment().endOf('D').valueOf()]
    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    }
    delete params.date
    if (query.organizerId === 'organizerId') {
      params.organizerId = removeSlash(useUserStore().user?.id?.id || '')
      params.ccUserId = ''
    } else {
      params.organizerId = ''
      params.ccUserId = removeSlash(useUserStore().user?.id?.id || '')
    }
    const res = await GetWorkOrderPage(params)
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
onMounted(() => {
  initOptions()
  refreshData()
})
</script>
<style lang="scss" scoped></style>
