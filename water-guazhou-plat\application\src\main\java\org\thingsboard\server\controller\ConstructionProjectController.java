package org.thingsboard.server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.ConstructionProject;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectSaveRequest;
import org.thingsboard.server.dao.store.ConstructionProjectService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/constructionProject")
public class ConstructionProjectController extends BaseController {
    @Autowired
    private ConstructionProjectService service;


    @GetMapping
    public IPage<ConstructionProject> findAllConditional(ConstructionProjectPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public ConstructionProject save(@RequestBody ConstructionProjectSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody ConstructionProjectSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}