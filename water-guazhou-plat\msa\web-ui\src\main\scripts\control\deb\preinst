#!/bin/sh

if ! getent group ${pkg.user} >/dev/null; then
    addgroup --system ${pkg.user}
fi

if ! getent passwd ${pkg.user} >/dev/null; then
    adduser --quiet \
            --system \
            --ingroup ${pkg.user} \
            --quiet \
            --disabled-login \
            --disabled-password \
            --home ${pkg.installFolder} \
            --no-create-home \
            -gecos "Thingsboard application" \
            ${pkg.user}
fi
