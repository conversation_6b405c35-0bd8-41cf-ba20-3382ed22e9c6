import {
  GetAllStationAttrs,
  GetStationAttrs,
  GetStationList,
  GetStationRealTimeDetail,
  GetStationRecentDataByType,
  GetStationTodayDataList,
  GetStationTree
} from '@/api/shuiwureports/zhandian';
import { useBusinessStore } from '@/store';
import { formatTree } from '@/utils/GlobalHelper';
import { getConstantsAttributeById } from '@/api/constants';
import { GetStationAlarmByProjectId } from '@/api/shuiwureports/bengzhan';
import { getThreeDaysData } from '@/api/secondSupplyManage/monitoringOverview';
import { GetWaterProcessDetailReport } from '@/api/waterFactoryManage/factoryReport';
import { getWaterSupplyDetailReport } from '@/api/headwatersManage/statisticalAnalysis';

export const useStations = () => {
  const stations = ref<any[]>([]);
  const getStations = async (
    type?: string,
    projectId?: string
  ): Promise<any[]> => {
    const res = await GetStationList({
      page: 1,
      size: 999,
      projectId: projectId || '',
      type: type || ''
    });
    stations.value = res.data?.data || [];
    return stations.value;
  };
  return {
    stations,
    getStations
  };
};
export const useStationRealTimeData = () => {
  const realtimeList = ref<any[]>([]);
  const getRealTimeData = async (stationId?: string, type?: string) => {
    if (stationId) {
      const real = await GetStationRealTimeDetail(stationId, type);
      realtimeList.value = real.data || [];
    } else {
      realtimeList.value = [];
    }
    return realtimeList.value;
  };
  return {
    getRealTimeData,
    realtimeList
  };
};
/**
 * 报警类型枚举
 */
export enum EAlarmType {
  'CONFIRM_UNACK' = 'CONFIRM_UNACK',
  'ACTIVE_ACK' = 'ACTIVE_ACK',
  'CONFIRM_ACK' = 'CONFIRM_ACK',
  'RESTORE_ACK' = 'RESTORE_ACK',
  'CLEARED_ACK' = 'CLEARED_ACK',
  'CLEAR_FORCED' = 'CLEAR_FORCED'
}
export const formatAlarmType = (type: string) => {
  switch (type) {
    case 'scope':
      return '值超限';
    case 'offline':
      return '设备掉线';
    default:
      return type;
  }
};
export const formateAlartStatus = (status: string) => {
  if (status === 'CONFIRM_UNACK' || status === 'ACTIVE_ACK') {
    return '未恢复 | 未确认';
  }
  if (status === 'CONFIRM_ACK') {
    return '未恢复 | 已确认';
  }
  if (status === 'RESTORE_ACK') {
    return '已恢复 | 未确认';
  }
};
export const useStationAlarms = () => {
  const alarms = ref<any[]>([]);
  const curAlarm = ref();
  // 未确认 CONFIRM_UNACK   已确认 CONFIRM_ACK   已恢复 RESTORE_ACK
  // 解除 CLEARED_ACK  强制解除 CLEAR_FORCED
  const getAlarmList = async (projectId = '') => {
    try {
      const res = await GetStationAlarmByProjectId({
        projectId
      });
      alarms.value = res.data ?? [];
    } catch (error) {
      console.log('获取站点报警信息失败api/alarm/stationAlarmByProjectId');
    }
  };
  const setCurAlarm = (id?: string) => {
    if (!id) return undefined;
    curAlarm.value = alarms.value.find((item) => item.id.id === id);
  };
  const curAlarmLastRecord = computed(() => {
    const records = curAlarm.value?.details?.record ?? [];
    return records.length === 0 ? undefined : records[records.length - 1];
  });
  return {
    getAlarmList,
    setCurAlarm,
    alarms,
    curAlarm,
    curAlarmLastRecord
  };
};
/**
 * 指定站点类型的最近数据
 * @returns
 */
export const useStationsLatestData = () => {
  const latestData = ref<any[]>([]);
  const getLatestData = async (params: {
    type: string;
    projectId?: string;
  }) => {
    const res = await GetStationRecentDataByType({
      stationType: params.type || '',
      projectId: params.projectId || useBusinessStore().navSelectedRange?.data?.id
    });
    latestData.value = res.data;
    return latestData.value;
  };
  const latestDataFields = ref<any[]>([]);
  const getLatestDataFields = async (type: string) => {
    const res = await getConstantsAttributeById({
      type: 'stationInfo',
      key: type
    });
    if (!res.data?.[0]) {
      latestDataFields.value = [];
    } else {
      latestDataFields.value = JSON.parse(res.data[0].value);
    }
    return latestDataFields.value;
  };
  return {
    getLatestData,
    latestData,
    getLatestDataFields,
    latestDataFields
  };
};
export const useStationThreeDays = () => {
  const data = ref<
    {
      todayDataList: any[];
      yesterdayDataList: any[];
      beforeYesterdayDataList: any[];
    }[]
  >([]);
  const getData = async (params: { deviceId: string; attr: string }) => {
    try {
      const res = await getThreeDaysData(params);
      data.value = res.data?.data;
    } catch (error) {
      console.log(error);
    }
  };
  return {
    data,
    getData
  };
};
/**
 * 使用站点（formatted)
 * @returns
 */
export const useStation = () => {
  const AllStations = ref<NormalOption[]>([]);
  const StationList = ref<NormalOption[]>([]);
  const StationAllAttrs = ref<NormalOption[]>([]);
  const StationAttrGroups = ref<NormalOption[]>([]);
  const getStationOption = async (
    type?: string,
    projectId?: string,
    withFill?: boolean
  ): Promise<NormalOption[]> => {
    const res = await GetStationList({
      page: 1,
      size: 999,
      projectId: projectId || useBusinessStore().navSelectedRange?.value,
      type: type || ''
    });
    const resD =
      res.data?.data?.map((item) => {
        item.isLeaf = true;
        return item;
      }) || [];
    const data = formatTree(
      resD,
      {
        label: 'name',
        id: 'id',
        value: 'id',
        isLeaf: 'isLeaf'
      },
      undefined,
      'station'
    );
    withFill && (StationList.value = data);
    return data;
  };
  const getAllStationOption = async (
    type?: string,
    projectId?: string,
    withFill?: boolean
  ): Promise<NormalOption[]> => {
    const res = await GetStationList({
      page: 1,
      size: 999,
      type: type || '',
      projectId: projectId || ''
    });
    const resD =
      res.data?.data?.map((item) => {
        item.isLeaf = true;
        return item;
      }) || [];
    const data = formatTree(
      resD,
      {
        label: 'name',
        id: 'id',
        value: 'id',
        isLeaf: 'isLeaf'
      },
      undefined,
      'station'
    );
    withFill && (AllStations.value = data);
    return data;
  };
  const getStationAllAttrs = async (
    stationId: string,
    withFill?: boolean
  ): Promise<NormalOption[]> => {
    const res = await GetAllStationAttrs({ stationId });
    const data = formatTree(
      res.data || [],
      {
        label: 'name',
        id: 'id',
        value: 'id',
        children: 'children'
      },
      undefined,
      'stationAttr'
    );
    withFill && (StationAllAttrs.value = data);
    return data;
  };
  const getStationAttrGroups = async (
    stationId: string,
    withFill?: boolean
  ): Promise<NormalOption[]> => {
    const res = await GetStationAttrs({ stationId });
    const data = formatTree(res.data || [], {
      label: 'type',
      value: 'type',
      id: 'type',
      children: 'attrList'
    });
    withFill && (StationAttrGroups.value = data);
    return data;
  };
  // const stationTypeIcon: any = {
  //   泵站: 'iconfont icon-shuibeng',
  //   水源地: 'iconfont icon-liuliangjiance',
  //   水厂: 'iconfont icon-ditutubiao-',
  //   测流压站: 'iconfont icon-liuliangjiance'
  // }
  // 通过类型查询站点树
  const getStationTree = async (
    stationType?: string,
    projectId?: string
  ): Promise<NormalOption[]> => {
    const res = await GetStationTree(stationType);
    const data = formatTree(
      res.data.data || [],
      {
        label: 'name',
        value: 'id',
        id: 'id',
        children: 'children'
      },
      undefined,
      'project',
      (data) => {
        const type = stationType || data.data?.nodeDetail?.type;
        console.log(type);
        data.icon =
          data.data.type === 'Project'
            ? 'iconfont icon-ditutubiao-'
            : data.data.type === 'Station'
              ? 'iconfont icon-tubiaozhizuomoban' // ? stationTypeIcon[type]
              : '';
        return data;
      }
    );
    console.log(data);
    return data;
  };

  // 禁用指定类型树
  const getStationTreeByDisabledType = async (
    treeDataList: any[],
    disabledType?: string[],
    isLazy?: boolean,
    lazyType?: string
  ): Promise<NormalOption[]> => {
    for (const i in treeDataList) {
      if (disabledType?.includes(treeDataList[i].data?.type)) {
        treeDataList[i].disabled = true;
        if (treeDataList[i].children?.length > 0) {
          getStationTreeByDisabledType(
            treeDataList[i].children,
            disabledType,
            isLazy,
            lazyType
          );
        } else if (isLazy && treeDataList[i].data?.type === lazyType) {
          treeDataList[i].children = [{ id: 0 }]; // await getStationAttrGroups(treeDataList[i].data.id, true)
          // await getStationAttrGroups(treeDataList[i].data.id, false)
          getStationTreeByDisabledType(
            treeDataList[i].children,
            disabledType,
            isLazy,
            lazyType
          );
        }
      } else if (!treeDataList[i].children) {
        if (isLazy && treeDataList[i].data?.type === lazyType) {
          treeDataList[i].children = [{ id: 0 }];
          // await getStationAttrGroups(treeDataList[i].data.id, false)
          getStationTreeByDisabledType(
            treeDataList[i].children,
            disabledType,
            isLazy,
            lazyType
          );
        }
      }
    }
    return treeDataList;
  };

  return {
    getStationOption,
    getAllStationOption,
    getStationAllAttrs,
    getStationAttrGroups,
    StationAllAttrs,
    StationList,
    AllStations,
    StationAttrGroups,
    getStationTree,
    getStationTreeByDisabledType
  };
};
export default useStation;
export const useStationTodayList = () => {
  const list = ref<
    {
      key: string;
      name: string;
      unit?: string;
      dataList?: { ts: string; value: string }[];
    }[]
  >([]);
  const getList = async (stationId?: string) => {
    if (!stationId) {
      list.value = [];
      return;
    }
    try {
      const res = await GetStationTodayDataList({
        stationId
      });
      list.value = res.data?.data ?? [];
      return list.value;
    } catch (error) {
      console.log(error);
    }
  };
  return {
    getList,
    list
  };
};
export const useStationWaterInOutList = () => {
  const waterIn = ref<{
    tableInfo?: { columnName: string; columnValue: string; unit: string };
    tableDataList?: any[];
  }>();
  const getWaterIn = async (params?: {
    stationIdList: string;
    queryType?: string;
    start?: number | string;
    end?: string | number;
  }) => {
    try {
      const res = await GetWaterProcessDetailReport(params);
      waterIn.value = res.data.data ?? {};
    } catch (error) {
      console.log(error);
    }
  };
  const waterOut = ref<{
    tableInfo?: { columnName: string; columnValue: string; unit: string };
    tableDataList?: any[];
  }>();
  const getWaterOut = async (params?: {
    stationIdList: string;
    queryType?: string;
    start?: number | string;
    end?: string | number;
  }) => {
    try {
      const res = await getWaterSupplyDetailReport(params);
      waterIn.value = res.data.data ?? {};
    } catch (error) {
      console.log(error);
    }
  };
  return {
    getWaterIn,
    waterIn,
    waterOut,
    getWaterOut
  };
};
