<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
      <!-- <el-form-item label="水厂类别：">
        <el-select v-model="queryParams.waterPlantType" placeholder="请选择" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="自动填报" value="自动填报"></el-option>
          <el-option label="手动填报" value="手动填报"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="名称筛选：">
        <el-input v-model="queryParams.waterPlantName" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="日期范围：">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleDateRangeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="数据来源：">
        <el-select v-model="queryParams.dataSource" placeholder="请选择" clearable>
          <el-option label="自动采集" value="自动采集"></el-option>
          <el-option label="手动填报" value="手动填报"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="table-toolbar">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="success" @click="handleImport">导入</el-button>
      <el-button type="warning" @click="handleExport">导出</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="pressureList" border>
      <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column label="水厂名称" prop="waterPlantName" align="center"></el-table-column>
      <el-table-column label="设备编号" prop="deviceSerial" align="center"></el-table-column>
      <el-table-column label="出厂水压/MPa" prop="pressure" align="center"></el-table-column>
      <el-table-column label="记录时间" align="center">
        <template #default="scope">
          {{ scope.row.recordTime ? dayjs(Number(scope.row.recordTime)).format('YYYY-MM-DD HH:mm:ss') : '' }}
        </template>
      </el-table-column>
      <el-table-column label="数据来源" prop="dataSource" align="center"></el-table-column>
      <el-table-column label="备注" prop="remark" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>



    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" append-to-body destroy-on-close>
      <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="水厂名称" prop="waterPlantName">
          <el-select v-model="formData.waterPlantId" placeholder="请选择水厂" filterable @change="handleWaterPlantChange">
            <el-option
              v-for="item in waterPlantOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="设备编号" prop="deviceSerial">
          <el-input v-model="formData.deviceSerial" placeholder="请输入设备编号"></el-input>
        </el-form-item>
        <el-form-item label="出厂水压" prop="pressure">
          <el-input-number v-model="formData.pressure" :precision="2" :step="0.1" :min="0" placeholder="请输入出厂水压(MPa)"></el-input-number>
          <span class="unit">MPa</span>
        </el-form-item>
        <el-form-item label="记录时间" prop="recordTime">
          <el-date-picker
            v-model="formData.recordTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            :clearable="false"
            :editable="false"
          ></el-date-picker>
        </el-form-item>


        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>


      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog title="导入水厂压力信息" v-model="importDialogVisible" width="500px" append-to-body>
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :http-request="handleUpload"
        :before-upload="beforeUpload"
        :file-list="fileList"
        :limit="1"
        :on-exceed="handleExceed"
        :on-remove="handleRemove"
        accept=".xlsx, .xls"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">只能上传xlsx/xls文件，且不超过10MB</div>
        </template>
      </el-upload>
      <div class="import-template">
        <a @click="downloadTemplate">下载导入模板</a>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmImport">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as XLSX from 'xlsx';
import dayjs from '@/plugins/dayjs';
import {
  getWaterPlantPressureList,
  saveWaterPlantPressure,
  importWaterPlantPressure,
  exportWaterPlantPressure,
  deleteWaterPlantPressure,
  getWaterPlantList
} from '@/api/waterPlant/pressure';

export default defineComponent({
  name: 'WaterPlantPressure',
  setup() {
    const queryForm = ref(null);
    const form = ref(null);

    // 使用ref创建表单数据，而不是放在reactive对象中
    const formData = ref<{
      id: string;
      waterPlantId: string;
      waterPlantName: string;
      pressure: number;
      recordTime: number | string; // 可以是时间戳(number)或字符串
      remark: string;
      deviceSerial: string;
      dataSource: string;
    }>({
      id: '',
      waterPlantId: '',
      waterPlantName: '',
      pressure: 0,
      recordTime: '',
      remark: '',
      deviceSerial: '',
      dataSource: '手动填报'
    });

    // 表单校验规则
    const rules = {
      waterPlantId: [{ required: true, message: '请选择水厂', trigger: 'change' }],
      pressure: [{ required: true, message: '请输入出厂水压', trigger: 'blur' }],
      recordTime: [{ required: true, message: '请选择记录时间', trigger: 'change' }]
    };

    const state = reactive({
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        waterPlantType: '',
        waterPlantName: '',
        startTime: '',
        endTime: '',
        dataSource: ''
      },
      // 日期范围
      dateRange: [],
      // 加载状态
      loading: false,
      // 水厂压力列表
      pressureList: [],
      // 总记录数
      total: 0,
      // 对话框标题
      dialogTitle: '',
      // 对话框可见性
      dialogVisible: false,
      // 水厂选项
      waterPlantOptions: [],
      // 导入对话框可见性
      importDialogVisible: false,
      // 文件列表
      fileList: [],
      // 导入数据
      importData: []
    });

    // 获取水厂压力列表
    const getList = async () => {
      state.loading = true;
      try {
        const res = await getWaterPlantPressureList(state.queryParams);

        // 处理返回的数据结构
        if (res && res.data && res.data.code === 200 && res.data.data) {
          if (res.data.data.data && Array.isArray(res.data.data.data)) {
            state.pressureList = res.data.data.data;
            state.total = res.data.data.total || 0;
          } else {
            state.pressureList = [];
            state.total = 0;
          }
        } else {
          state.pressureList = [];
          state.total = 0;
        }
      } catch (error) {
        ElMessage.error('获取水厂压力列表失败');
        state.pressureList = [];
        state.total = 0;
      } finally {
        state.loading = false;
      }
    };

    // 获取水厂列表
    const getWaterPlants = async () => {
      try {
        const res = await getWaterPlantList({ page: 1, size: 1000 });

        // 处理站点数据 - 适配不同的响应格式
        let stations = [];

        // 检查响应结构，尝试不同的路径提取数据
        if (res && res.data) {
          // 处理标准响应格式
          if (res.data.data && Array.isArray(res.data.data)) {
            stations = res.data.data;
          }
          // 其他可能的数据结构
          else if (res.data.code === 200) {
            if (res.data.data && Array.isArray(res.data.data.data)) {
              stations = res.data.data.data;
            } else if (res.data.data && Array.isArray(res.data.data.records)) {
              stations = res.data.data.records;
            } else if (Array.isArray(res.data.data)) {
              stations = res.data.data;
            }
          } else if (Array.isArray(res.data)) {
            stations = res.data;
          } else if (res.data.records && Array.isArray(res.data.records)) {
            stations = res.data.records;
          }
        } else if (Array.isArray(res)) {
          stations = res;
        }

        state.waterPlantOptions = stations;
      } catch (error) {
        ElMessage.error('获取水厂列表失败');
      }
    };

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1;
      getList();
    };

    // 重置按钮点击事件
    const resetQuery = () => {
      queryForm.value?.resetFields();
      state.dateRange = [];
      state.queryParams.startTime = '';
      state.queryParams.endTime = '';
      handleQuery();
    };

    // 日期范围变化事件
    const handleDateRangeChange = (val: any) => {
      if (val) {
        state.queryParams.startTime = val[0];
        state.queryParams.endTime = val[1];
      } else {
        state.queryParams.startTime = '';
        state.queryParams.endTime = '';
      }
    };

    // 每页条数变化事件
    const handleSizeChange = (val: number) => {
      state.queryParams.size = val;
      getList();
    };

    // 当前页变化事件
    const handleCurrentChange = (val: number) => {
      state.queryParams.page = val;
      getList();
    };

    // 新增按钮点击事件
    const handleAdd = () => {
      state.dialogTitle = '新增水厂压力信息';

      // 使用当前时间的时间戳
      const timestamp = Date.now();



      formData.value = {
        id: '',
        waterPlantId: '',
        waterPlantName: '',
        pressure: 0,
        recordTime: timestamp, // 使用时间戳格式
        remark: '',
        deviceSerial: '',
        dataSource: '手动填报'
      };
      state.dialogVisible = true;
    };

    // 编辑按钮点击事件
    const handleEdit = (row: any) => {
      try {
        // 设置对话框标题
        state.dialogTitle = '编辑水厂压力信息';

        // 深拷贝行数据
        const rowData = JSON.parse(JSON.stringify(row));

        // 处理日期格式 - 直接使用时间戳
        let recordTimeValue: number;
        if (rowData.recordTime && typeof rowData.recordTime === 'number') {
          // 直接使用时间戳
          recordTimeValue = rowData.recordTime;
        } else {
          // 如果没有日期，使用当前时间的时间戳
          recordTimeValue = Date.now();
        }



        // 直接修改formData.value对象
        formData.value = {
          id: rowData.id, // 确保id字段被正确设置，不使用默认值
          waterPlantId: rowData.waterPlantId || '',
          waterPlantName: rowData.waterPlantName || '',
          pressure: rowData.pressure !== null && rowData.pressure !== undefined ? Number(rowData.pressure) : 0,
          deviceSerial: rowData.deviceSerial || '',
          dataSource: rowData.dataSource || '手动填报',
          remark: rowData.remark || '',
          recordTime: recordTimeValue // 使用时间戳格式
        };

        // 打印id字段，用于调试
        console.log('编辑时的ID:', formData.value.id);



        // 打开对话框
        state.dialogVisible = true;
      } catch (error) {
        ElMessage.error('编辑数据时出错');
      }
    };

    // 删除按钮点击事件
    const handleDelete = (row: any) => {
      ElMessageBox.confirm('确认删除该水厂压力信息吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteWaterPlantPressure(row.id);
          ElMessage.success('删除成功');
          getList();
        } catch (error) {
          ElMessage.error('删除失败');
        }
      }).catch(() => {});
    };

    // 水厂选择变化事件
    const handleWaterPlantChange = (value: string) => {
      try {
        const selected = state.waterPlantOptions.find((item: any) => item.id === value);
        if (selected) {
          // 使用name字段作为水厂名称，如果没有name字段，则尝试使用waterPlantName字段
          formData.value.waterPlantName = selected.name || selected.waterPlantName || '';
        }
      } catch (error) {
        ElMessage.error('选择水厂时出错');
      }
    };

    // 提交表单
    const submitForm = () => {
      try {
        const formEl = form.value as any;
        if (formEl) {
          formEl.validate(async (valid: boolean) => {
            if (valid) {
              try {


                // 确保所有必要字段都存在且格式正确
                const submitData = {
                  ...formData.value,
                  // 确保id字段被正确处理
                  id: formData.value.id || undefined, // 如果id为空字符串，则设置为undefined，表示新增
                  // 确保waterPlantId是字符串类型
                  waterPlantId: formData.value.waterPlantId ? formData.value.waterPlantId.toString() : '',
                  // 确保pressure是数字类型
                  pressure: formData.value.pressure !== null ? Number(formData.value.pressure) : 0,
                  // 处理recordTime，确保是字符串类型的时间戳
                  recordTime: formData.value.recordTime ?
                    String(formData.value.recordTime) :
                    String(Date.now()),
                  // 添加deviceSerial字段，如果表单中没有
                  deviceSerial: formData.value.deviceSerial || '',
                  // 添加dataSource字段，如果表单中没有
                  dataSource: formData.value.dataSource || '手动填报'
                };

                // 打印提交的数据，用于调试
                console.log('提交的数据:', submitData);

                await saveWaterPlantPressure(submitData);
                ElMessage.success('保存成功');
                state.dialogVisible = false;
                getList();
              } catch (error) {
                ElMessage.error('保存失败');
              }
            }
          });
        }
      } catch (error) {
        ElMessage.error('表单验证失败');
      }
    };

    // 导入按钮点击事件
    const handleImport = () => {
      state.importDialogVisible = true;
      state.fileList = [];
      state.importData = [];
    };

    // 导出按钮点击事件
    const handleExport = async () => {
      try {
        const res = await exportWaterPlantPressure(state.queryParams);
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = '水厂压力信息.xlsx';
        link.click();
        URL.revokeObjectURL(link.href);
      } catch (error) {
        ElMessage.error('导出失败');
      }
    };

    // 上传前校验
    const beforeUpload = (file: any) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        ElMessage.error('上传文件只能是Excel格式!');
        return false;
      }
      if (!isLt10M) {
        ElMessage.error('上传文件大小不能超过10MB!');
        return false;
      }
      return true;
    };

    // 文件上传
    const handleUpload = (options: any) => {
      const file = options.file;
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const json = XLSX.utils.sheet_to_json(worksheet);
        state.importData = json.map((item: any) => {
          // 将日期字符串转换为时间戳
          let recordTime = item['记录时间'];
          if (recordTime && typeof recordTime === 'string') {
            recordTime = new Date(recordTime).getTime();
          } else {
            recordTime = new Date().getTime();
          }

          return {
            waterPlantName: item['水厂名称'],
            pressure: Number(item['出厂水压(MPa)'] || 0),
            recordTime: recordTime,
            remark: item['备注'] || '',
            deviceSerial: item['设备编号'] || '',
            dataSource: '手动填报'
          };
        });
      };
      reader.readAsArrayBuffer(file);
    };

    // 文件数量超出限制
    const handleExceed = () => {
      ElMessage.warning('最多只能上传1个文件');
    };

    // 移除文件
    const handleRemove = () => {
      state.importData = [];
    };

    // 下载导入模板
    const downloadTemplate = () => {
      const template = [
        {
          '水厂名称': '示例水厂',
          '出厂水压(MPa)': 1.02,
          '记录时间': '2023-01-01 08:00:00',
          '设备编号': 'DEV001',
          '备注': '示例备注'
        }
      ];
      const worksheet = XLSX.utils.json_to_sheet(template);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '水厂压力信息');
      XLSX.writeFile(workbook, '水厂压力信息导入模板.xlsx');
    };

    // 确认导入
    const confirmImport = async () => {
      if (state.importData.length === 0) {
        ElMessage.warning('请先上传文件');
        return;
      }

      try {
        await importWaterPlantPressure(state.importData);
        ElMessage.success('导入成功');
        state.importDialogVisible = false;
        getList();
      } catch (error) {
        ElMessage.error('导入失败');
      }
    };

    onMounted(() => {
      getList();
      getWaterPlants();
    });

    return {
      queryForm,
      form,
      formData, // 直接暴露formData给模板
      rules, // 添加表单校验规则
      dayjs, // 添加dayjs用于日期格式化
      ...toRefs(state),
      handleQuery,
      resetQuery,
      handleDateRangeChange,
      handleSizeChange,
      handleCurrentChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleWaterPlantChange,
      submitForm,
      handleImport,
      handleExport,
      beforeUpload,
      handleUpload,
      handleExceed,
      handleRemove,
      downloadTemplate,
      confirmImport
    };
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.table-toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.unit {
  margin-left: 5px;
}
.import-template {
  margin-top: 10px;
  text-align: right;
}
.import-template a {
  color: #409EFF;
  cursor: pointer;
}
</style>
