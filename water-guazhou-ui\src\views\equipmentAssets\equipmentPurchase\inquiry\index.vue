<!-- 采购询价 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
    <SLDrawer ref="detailRef" :config="detailConfig"></SLDrawer>
    <SLDrawer ref="fileForm" :config="addfileConfig"></SLDrawer>
    <SLDrawer ref="filedetailForm" :config="attachmentInformation"></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import { ICardSearchIns, ISLDrawerIns } from '@/components/type';
import useGlobal from '@/hooks/global/useGlobal';
import {
  getDevicePurchaseItemSearch,
  getDevicePurchaseSearch,
  postDevicePurchaseInquiry,
  postCompleteInquiry,
  getDevicePurchaseInquiry
} from '@/api/equipment_assets/equipmentPurchase';
import { getSupplierSerch } from '@/api/equipment_assets/equipmentManage';
import { inspectionProgress } from '../../equipmentAssetsData';
import { traverse } from '@/utils/GlobalHelper';
import { formatDate } from '@/utils/DateFormatter';
import { downloadUrl } from '@/utils/fileHelper';

const { $btnPerms } = useGlobal();

const refSearch = ref<ICardSearchIns>();

const refForm = ref<ISLDrawerIns>();

const fileForm = ref<ISLDrawerIns>();
const filedetailForm = ref<ISLDrawerIns>();
const detailRef = ref<ISLDrawerIns>();

const check = ref('0');

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备编号', field: 'serialId', type: 'input' },
    { label: '设备名称', field: 'deviceName', type: 'input' },
    { label: '设备型号', field: 'deviceModel', type: 'input' },
    {
      label: '采购单编号',
      field: 'purchaseCode',
      type: 'input',
      labelWidth: '90px'
    },
    {
      type: 'department-user',
      label: '请购人',
      field: 'userId'
    },
    {
      label: '采购单标题',
      field: 'purchaseTitle',
      type: 'input',
      labelWidth: '90px'
    },
    {
      label: '请购单创建时间',
      field: 'time',
      type: 'daterange',
      labelWidth: '120px',
      format: 'YYYY-MM-DD'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  indexAccumulative: true,
  columns: [
    { label: '采购设备名称', prop: 'name' },
    { label: '设备编号', prop: 'serialId' },
    { label: '设备型号', prop: 'model' },
    { label: '所属大类', prop: 'topType' },
    { label: '所属类别', prop: 'type' },
    { label: '采购数量', prop: 'num' },
    {
      label: '计划完成时间',
      prop: 'inquiryEndTime',
      formatter: (row) => formatDate(row.inquiryEndTime, 'YYYY-MM-DD')
    },
    { label: '所属采购单', prop: 'mainName' },
    {
      label: '询价进度',
      prop: 'status',
      tag: true,
      formatter: (row) =>
        inspectionProgress.find((item) => item.value === row.status)?.label ||
        '',
      tagColor: (row): string =>
        inspectionProgress.find((item) => item.value === row.status)?.color ||
        ''
    }
  ],
  operationWidth: '200px',
  operations: [
    {
      hide: (row) => row.status === 2,
      text: '询价',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageDelete'),
      click: (row) => clickEdit(row)
    },
    {
      hide: (row) => row.status !== 2,
      type: 'primary',
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: (row) => openDetails(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '询价',
  labelWidth: '130px',
  submit: (params: any) => {
    if (
      !params.supplier.some(
        (item) =>
          item.supplierId &&
          item.contact &&
          item.contactPhone &&
          item.price &&
          item.inquiryTime
      )
    ) {
      ElMessage.warning('请完整填写供应商信息');
      return;
    }
    params.supplier.forEach((element) => {
      if (element.intentionSupplier === true) {
        completeInquiry(params.id);
      }
    });
    postDevicePurchaseInquiry(params.supplier).then(() => {
      refreshData();
      ElMessage.success('询价成功');
      refForm.value?.closeDrawer();
    });
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '采购单标题',
          field: 'title'
        },

        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '采购单编码',
          field: 'code'
        },
        {
          xl: 8,
          readonly: true,
          type: 'date',
          label: '计划采购时间',
          field: 'preTime'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '用途',
          field: 'useWay'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '请购部门',
          field: 'userDepartmentName'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '请购人',
          field: 'userName'
        },
        {
          type: 'divider',
          text: '供应商信息'
        },
        {
          type: 'table',
          field: 'supplier',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加供应商',
                        perm: true,
                        click: () => {
                          data.selectList.push({
                            numId: guid(),
                            intentionSupplier: false,
                            purchaseDetailId: data.selectedForm.id
                          });
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '供应商',
                prop: 'supplierId',
                formItemConfig: {
                  type: 'select',
                  options: computed(() => data.SupplierList) as any,
                  onChange: (val: string) => {
                    console.log(val)
                    const row =  data.SupplierList.find((item:any)=>item.id === val) as any
                    console.log(row)
                    data.selectList = data.selectList.map((item:any)=>{
                      if(val === item.supplierId){
                       return {
                          ...item,
                          ...row,
                        }
                      }
                    })
                  }
                }
              },
              {
                label: '联系人',
                prop: 'contact',
                formItemConfig: {
                  type: 'input'
                }
              },
              {
                label: '联系方式',
                prop: 'contactPhone',
                formItemConfig: {
                  type: 'input'
                }
              },
              {
                label: '单价',
                prop: 'price',
                formItemConfig: {
                  type: 'input-number'
                }
              },
              {
                label: '询价时间',
                prop: 'inquiryTime',
                formItemConfig: {
                  type: 'datetime',
                  format: 'x'
                }
              },
              {
                label: '意向供应商',
                prop: 'intentionSupplier',
                formItemConfig: {
                  type: 'switch',
                  onChange: () => {
                    const value = data.selectList.filter((item) => {
                      return item.intentionSupplier;
                    });
                    if (value.length > 1) {
                      data.selectList[check.value].intentionSupplier = false;
                    }
                    data.selectList.filter((item, index) => {
                      if (item.intentionSupplier) check.value = index;
                      return item.intentionSupplier;
                    });
                  }
                }
              }
            ],
            operations: [
              {
                text: '附件',
                perm: $btnPerms('RoleManageEdit'),
                icon: ICONS.DETAIL,
                click: (row) => {
                  addfileConfig.defaultValue = { ...(row || {}) };
                  fileForm.value?.openDrawer();
                }
              },
              {
                text: '移除',
                type: 'danger',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: (row) => {
                  data.selectList = data.selectList.filter(
                    (item) => item.numId !== row.numId
                  );
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 上传文件
const addfileConfig = reactive<IDrawerConfig>({
  title: '附件',
  width: '500px',
  labelWidth: '100px',
  submit: (params: any) => {
    data.selectList.map((item) => {
      if (item.id === params.id) {
        item.file = params.file;
      }
    });
    fileForm.value?.closeDrawer();
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'file',
          label: '附件',
          field: 'file'
        }
      ]
    }
  ]
});

// 附件信息
const attachmentInformation = reactive<IDrawerConfig>({
  title: '附件信息',
  width: '500px',
  labelWidth: '100px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'file',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.files) as any,
            columns: [
              {
                label: '附件',
                prop: 'file',
                download: true,
                cellStyle: {
                  fontStyle: 'italic',
                  width: '100px',
                  cursor: 'pointer'
                },
                handleClick: (row) => {
                  downloadUrl(row.file);
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '130px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '采购单标题',
          field: 'title'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '采购单编码',
          field: 'code'
        },
        {
          xl: 8,
          readonly: true,
          type: 'date',
          label: '计划采购时间',
          field: 'preTime'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '用途',
          field: 'useWay'
        },
        {
          xl: 8,
          readonly: true,
          type: 'select',
          label: '请购部门',
          field: 'userDepartmentName'
        },
        {
          xl: 8,
          readonly: true,
          type: 'select',
          label: '请购人',
          field: 'userName'
        },
        {
          type: 'divider',
          text: '供应商信息'
        },
        {
          type: 'table',
          field: 'key7',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              { label: '供应商', prop: 'supplierName' },
              { label: '联系人', prop: 'contact' },
              { label: '联系方式', prop: 'contactPhone' },
              { label: '单价', prop: 'price' },
              {
                label: '询价时间',
                prop: 'inquiryTime',
                formatter: (row) => formatDate(row.inquiryTime, 'YYYY-MM-DD')
              },
              {
                label: '意向供应商',
                prop: 'intentionSupplier',
                formItemConfig: {
                  readonly: true,
                  type: 'switch'
                }
              }
            ],
            operations: [
              {
                text: '附件',
                perm: $btnPerms('RoleManageEdit'),
                icon: ICONS.DETAIL,
                click: (row) => {
                  attachmentInformation.defaultValue = { ...(row || {}) };
                  const files =
                    (row.file &&
                      row.file.length !== 0 &&
                      row.file.split(',')) ||
                    [];
                  data.files = files.map((item) => {
                    return { file: item };
                  });
                  filedetailForm.value?.openDrawer();
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

function guid() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  );
}

const openDetails = (row: { [x: string]: any }) => {
  const params = { page: 1, size: 99999, Id: row.mainId };
  getDevicePurchaseSearch(params).then((res) => {
    const row = res.data.data.data[0] || {};
    for (const i in row) {
      if (row[i] === undefined || row[i] === null) {
        row[i] = ' ';
      }
    }
    detailConfig.defaultValue = { deviceId: row.id, ...row } || {
      deviceId: row.id
    };
    detailRef.value?.openDrawer();
  });
  data.getDevicePurchaseItemValue(row.id);
};

const clickEdit = (row: { [x: string]: any }) => {
  data.selectedForm = row;
  const params = { page: 1, size: 99999, Id: row.mainId };
  getDevicePurchaseSearch(params).then((res) => {
    addOrUpdateConfig.defaultValue = {
      deviceId: row.id,
      ...res.data.data.data[0]
    } || { deviceId: row.id };
    refForm.value?.openDrawer();
  });
  data.getDevicePurchaseItemValue(row.id);
};

function completeInquiry(params: any) {
  postCompleteInquiry(params.id).then(() => {
    refreshData();
  });
}

const data = reactive({
  // 选中供应商
  selectList: [] as any,
  // 选中的表单
  selectedForm: {} as any,
  // 当前查看附件信息
  files: [] as any[],
  // 供应商
  SupplierList: [],
  // 获取请购条目
  getDevicePurchaseItemValue: (id: string) => {
    const params = {
      purchaseDetailId: id,
      page: 1,
      size: 20
    };
    getDevicePurchaseInquiry(params).then((res) => {
      const value = res.data.data.data || [];
      data.selectList = value.map((item, index) => {
        item.numId = index;
        return item;
      });
    });
  },
  // 获取供应商
  getSupplierValue: () => {
    const params = { page: 1, size: 99999 };
    getSupplierSerch(params).then((res) => {
      data.SupplierList = traverse(res.data.data.data || []);
    });
  }
});

const refreshData = async () => {
  const params: {
    size: number | undefined;
    page: number | undefined;
    fromTime?: string;
    toTime?: string;
    time?: string;
  } = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.time && params.time?.length > 1) {
    params.fromTime = (refSearch.value?.queryParams as any).time[0] || '';
    params.toTime = (refSearch.value?.queryParams as any).time[1] || '';
  }
  delete params.time;
  getDevicePurchaseItemSearch(params).then((res) => [
    (TableConfig.dataList = res.data.data.data || []),
    (TableConfig.pagination.total = res.data.data.total || 0)
  ]);
};

onMounted(() => {
  refreshData();
  data.getSupplierValue();
});
</script>
