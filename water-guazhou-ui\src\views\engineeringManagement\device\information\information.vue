<!-- 工程管理-设备管理-设备信息 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <DialogForm
      :key="key"
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import {
  geDeviceTypeTree,
  postDevice,
  getDeviceList,
  deleteDevice,
  deleteDevices
} from '@/api/engineeringManagement/device';
import { traverse } from '@/utils/GlobalHelper';

const { $btnPerms } = useGlobal();

const refForm = ref<IDialogFormIns>();

const refSearch = ref<ICardSearchIns>();

const key = ref(new Date().toString());

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备编码', field: 'serialId', type: 'input' },
    { label: '设备名称', field: 'name', type: 'input' },
    { label: '设备型号', field: 'model', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建')
        },
        {
          perm: true,
          text: '批量删除',
          icon: ICONS.DELETE,
          type: 'danger',
          click: () => {
            if (TableConfig.selectList?.length === 0) {
              ElMessage.warning('请选中需要删除的设备');
              return;
            }
            handleDelete();
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  handleSelectChange: (val) => {
    TableConfig.selectList = val;
  },
  columns: [
    { label: '设备编码', prop: 'serialId' },
    { label: '设备名称', prop: 'name' },
    { label: '设备型号', prop: 'model' },
    { label: '所属大类', prop: 'topTypeName' },
    { label: '所属类别', prop: 'typeName' },
    { label: '设备标识', prop: 'mark' },
    { label: '计量单位', prop: 'unit' },
    { label: '备注', prop: 'remark' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTimeName' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '120px',
  dialogWidth: '600px',
  submit: (params: any) => {
    params.serialId = data.prepend + params.serialIdNum;
    for (const i in TableConfig.dataList) {
      if (
        params.serialId === TableConfig.dataList[i].serialId &&
        params.serialId !== data.serialId
      ) {
        ElMessage.warning('设备编码重复');
        return;
      }
    }
    const list: any = {};
    for (const i in params) {
      if (i.indexOf('autoField.') !== -1) {
        list[i] = params[i];
      }
    }
    postDevice(params)
      .then((res) => {
        if (res.data.code === 500) {
          ElMessage.success(res.data.message);
        } else {
          refForm.value?.closeDialog();
          ElMessage.success('操作成功');
          refreshData();
        }
      })
      .catch((error) => {
        ElMessage.warning(error);
      });
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '所属类别',
          field: 'typeId',
          checkStrictly: true,
          options: computed(() => traverse(TreeData.data)) as any,
          readonly: true
        },
        {
          type: 'hint',
          text: '注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)'
        },
        {
          type: 'input',
          label: '设备编码',
          field: 'serialIdNum',
          prepend: computed(() => data.prepend) as any,
          rules: [
            { required: true, message: '请输入设备编码' },
            { min: 6, max: 6, message: '编码为6位' }
          ]
        },
        {
          type: 'input',
          label: '设备名称',
          field: 'name',
          rules: [{ required: true, message: '请输入设备名称' }]
        },
        {
          type: 'input',
          label: '设备型号',
          field: 'model',
          rules: [{ required: true, message: '请输入设备型号' }]
        },
        {
          type: 'input',
          label: '设备标识',
          field: 'mark'
        },
        {
          type: 'input',
          label: '计量单位',
          field: 'unit'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }
      ]
    }
  ]
});
const TreeData = reactive<SLTreeConfig>({
  title: '设备类别',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    refreshData();
  }
});

const clickCreatedRole = (title?: any) => {
  if (!TreeData.currentProject.id) {
    ElMessage.warning('请选中设备类型');
    return;
  }
  if (TreeData.currentProject.level === 0) {
    ElMessage.warning('请选择设备类别');
    return;
  }
  data.prepend = TreeData.currentProject.serialId.slice(0, 8);
  addOrUpdateConfig.title = title;
  addOrUpdateConfig.defaultValue = {
    typeId: TreeData.currentProject.id || '',
    useYear: '0',
    maintenanceCycle: '0',
    minStock: '0'
  };
  refForm.value?.openDialog();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑';
  data.serialId = row.serialId;
  data.prepend = row.serialId.slice(0, 8);
  row.serialIdNum = row.serialId.slice(8, 14);
  if (addOrUpdateConfig.group[0].fields.length > 16) {
    addOrUpdateConfig.group[0].fields.splice(
      13,
      addOrUpdateConfig.group[0].fields.length - 16
    );
  }
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDialog();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除选中设备', '删除提示').then(() => {
    if (row) {
      deleteDevice(row.id)
        .then(() => {
          ElMessage.success('删除成功');
          init();
        })
        .catch((error) => {
          ElMessage.error(error.toString());
        });
    } else {
      const ids = TableConfig.selectList?.map((node) => node.id) ?? [];
      deleteDevices(ids)
        .then(() => {
          ElMessage.success('删除成功');
          init();
        })
        .catch((error) => {
          ElMessage.error(error.toString());
        });
    }
  });
};

function init() {
  geDeviceTypeTree().then((res) => {
    TreeData.data = traverse(res.data.data || []);
    refreshData();
  });
}

const data = reactive({
  prepend: '',
  serialId: ''
});

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    typeSerialId: TreeData.currentProject?.serialId,
    ...refSearch.value?.queryParams
  };
  getDeviceList(params).then((res) => {
    TableConfig.dataList = res?.data?.data?.data || [];
    TableConfig.pagination.total = res?.data?.data?.total || 0;
  });
};

onMounted(async () => {
  init();
});
</script>
