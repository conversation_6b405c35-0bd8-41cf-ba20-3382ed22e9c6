/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.menu;

import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.HasTenantId;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;

@EqualsAndHashCode(callSuper = true)
public class MenuTenant extends BaseData<MenuTenantId> implements HasTenantId {

    private MenuPoolId menuPoolId;
    private TenantId tenantId;
    private String isExtensionMenu;

    public MenuTenant() {

    }

    public MenuTenant(MenuTenantId id) {
        super(id);
    }

    public MenuTenant(TenantId tenantId) {
        super();
        this.tenantId = tenantId;
    }

    public MenuTenant(MenuTenant menuTenant) {
        super();
        this.menuPoolId = menuTenant.getMenuPoolId();
        this.tenantId = menuTenant.getTenantId();
    }

    @Override
    public TenantId getTenantId() {
        return tenantId;
    }

    public MenuPoolId getMenuPoolId() {
        return menuPoolId;
    }

    public void setMenuPoolId(MenuPoolId menuPoolId) {
        this.menuPoolId = menuPoolId;
    }

    public void setTenantId(TenantId tenantId) {
        this.tenantId = tenantId;
    }

    public String getIsExtensionMenu() {
        return isExtensionMenu;
    }

    public void setIsExtensionMenu(String isExtensionMenu) {
        this.isExtensionMenu = isExtensionMenu;
    }
}
