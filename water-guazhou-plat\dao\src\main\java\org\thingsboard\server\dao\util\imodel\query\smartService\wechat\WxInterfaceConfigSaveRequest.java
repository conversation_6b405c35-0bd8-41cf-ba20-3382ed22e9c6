package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxInterfaceConfig;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxInterfaceConfigSaveRequest extends SaveRequest<WxInterfaceConfig> {
    // 用户信息获取地址
    private String userInfoApi;

    // 欠费缴费接口
    private String billInfoApi;

    // 销账接口
    private String payApi;

    // 对账接口
    private String checkApi;

    // 缴费记录查询接口
    private String paymentRecordApi;

    // 模板推送接口
    private String messageSendApi;

    @Override
    protected WxInterfaceConfig build() {
        WxInterfaceConfig entity = new WxInterfaceConfig();
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxInterfaceConfig update(String id) {
        WxInterfaceConfig entity = new WxInterfaceConfig();
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxInterfaceConfig entity) {
        entity.setUserInfoApi(userInfoApi);
        entity.setBillInfoApi(billInfoApi);
        entity.setPayApi(payApi);
        entity.setCheckApi(checkApi);
        entity.setPaymentRecordApi(paymentRecordApi);
        entity.setMessageSendApi(messageSendApi);
        entity.setTenantId(tenantId());
    }
}