package org.thingsboard.server.dao.optionLog;

import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.DeviceCommandRequest;
import org.thingsboard.server.dao.model.sql.DeviceCommand;
import org.thingsboard.server.dao.sql.optionLog.DeviceCommandMapper;

import java.util.List;

public interface DeviceCommandService {
    PageData<DeviceCommand> findList(DeviceCommandRequest request, TenantId tenantId);

    DeviceCommand findById(String id);

    void cancelCommand(String id, String username) throws ThingsboardException;

    List<DeviceCommand> findByDeviceKeyAndOptionResult(String deviceKey, String optionResult);

    void postCommand(DeviceCommand command) throws Exception;
}
