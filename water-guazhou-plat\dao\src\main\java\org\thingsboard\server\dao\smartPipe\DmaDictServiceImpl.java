package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaDict;
import org.thingsboard.server.dao.sql.smartPipe.DmaDictMapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2024-06-18
 */
@Service
public class DmaDictServiceImpl implements DmaDictService {

    @Autowired
    private DmaDictMapper dmaDictMapper;

    @Override
    public Object save(DmaDict dmaDict) {
        if (StringUtils.isBlank(dmaDict.getId())) {
            dmaDict.setCreateTime(new Date());
            dmaDictMapper.insert(dmaDict);
            if ("1".equals(dmaDict.getType())) {
                dmaDict.setCode("A01");
            } else {
                dmaDict.setCode("A02");
            }
        } else {
            dmaDictMapper.updateById(dmaDict);
        }
        return dmaDict;
    }

    @Override
    public void delete(List<String> idList) {
        dmaDictMapper.deleteBatchIds(idList);
    }

    @Override
    public PageData<DmaDict> getList(Map<String, Object> params) {
        int page = 1;
        int size = 999;
        if (params.get("page") != null) {
            page = Integer.parseInt(params.get("page").toString());
        }
        if (params.get("size") != null) {
            size = Integer.parseInt(params.get("size").toString());
        }
        IPage<DmaDict> pages = new Page<>(page, size);
        pages = dmaDictMapper.getList(params, pages);

        return new PageData<>(pages.getTotal(), pages.getRecords());
    }
}
