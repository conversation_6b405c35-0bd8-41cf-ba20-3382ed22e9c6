import{d as $,j as G,cN as L,r as W,o as j,g as l,n as v,p as O,q as y,F as c,G as g,aB as R,aJ as C,h as b,i as t,aw as T,l as s,bB as w,bE as m,bt as q,bl as H,bm as J,C as P}from"./index-r0dFAfgr.js";import{r as U,c as N}from"./chart-wy3NEK2T.js";import{a as z}from"./index-CcDafpIP.js";import{W as E}from"./workorder-jXNat1mh.js";import{l as K}from"./index-CpGhZCTT.js";import{s as A}from"./sortBy-DDhdj0i5.js";const Q={class:"one-map-detail"},X={class:"detail-row"},Y={key:0,class:"empty"},Z={class:"detail-row"},ee={key:0,class:"empty"},ae={class:"detail-row"},te={key:0,class:"empty"},re=$({__name:"InspectionDetail",emits:["refresh","mounted"],setup(se,{expose:F,emit:S}){const k=G(),{proxy:B}=L(),D=S,e=W({activeName_Bar1:"today",activeName_Bar2:"today",activeName_Ring:"today",tabs:[{label:"本日",value:"today"},{label:"本月",value:"month"},{label:"本年",value:"year"}],detailLoading:!1,option2:U(),option3:N("人员维修工单数量排行"),option4:N("维修人员完成率排行")}),V=async()=>{var i,a,o,p,_;if(!e.curRow)return;const n=e.activeName_Ring==="year"?s().startOf("y").valueOf():e.activeName_Ring==="month"?s().startOf("M").valueOf():s().startOf("D").valueOf(),r=e.activeName_Ring==="year"?s().endOf("y").valueOf():e.activeName_Ring==="month"?s().endOf("M").valueOf():s().endOf("D").valueOf(),d=(o=(a=(i=(await E({page:1,size:0,fromTime:n,toTime:r,processUserId:e.curRow.userId})).data)==null?void 0:i.data)==null?void 0:a.data)==null?void 0:o.map(u=>({name:u.key,nameAlias:u.key,value:u.value,valueAlias:u.value,scale:(u.percentage||0)*100+"%"}));e.option2=U(d,"个"),await w(),(_=(p=B.$refs["refChart2"+e.activeName_Ring])==null?void 0:p[0])==null||_.resize()},I=async()=>{var o,p,_,u;if(!e.curRow)return;const n=e.activeName_Bar1==="year"?s().startOf("y").format(m):e.activeName_Bar1==="month"?s().startOf("M").format(m):s().startOf("D").format(m),r=e.activeName_Bar1==="year"?s().endOf("y").format(m):e.activeName_Bar1==="month"?s().endOf("M").format(m):s().endOf("D").format(m),f=await E({page:1,size:0,fromTime:n,toTime:r,processUserId:e.curRow.userId}),d=[],i=[],a=A(((p=(o=f.data)==null?void 0:o.data)==null?void 0:p.data)||[],"value");if(a.map(h=>{d.push(h.key),i.push(h.value)}),!a.length){e.option3=void 0;return}e.option3=N("人员维修工单数量排行",d,i),await w(),(u=(_=B.$refs["refChart3"+e.activeName_Bar1])==null?void 0:_[0])==null||u.resize()},x=async()=>{var o,p,_,u,h;if(!e.curRow)return;const n=e.activeName_Bar2==="year"?s().startOf("y").format(m):e.activeName_Bar2==="month"?s().startOf("M").format(m):s().startOf("D").format(m),r=e.activeName_Bar2==="year"?s().endOf("y").format(m):e.activeName_Bar2==="month"?s().endOf("M").format(m):s().endOf("D").format(m),f=await K({fromTime:n,toTime:r,processUserId:e.curRow.userId}),d=[],i=[],a=A(((_=(p=(o=f.data)==null?void 0:o.data)==null?void 0:p.data)==null?void 0:_.data)||[],"value");if(a.map(M=>{d.push(M.key),i.push(M.value)}),!(a!=null&&a.length)){e.option4=void 0;return}e.option4=N("人员维修工单数量排行",d,i),await w(),(h=(u=B.$refs["refChart4"+e.activeName_Bar2])==null?void 0:u[0])==null||h.resize()};return F({refreshDetail:async n=>{n&&(e.curRow=n,V(),I(),x(),D("refresh",{...n||{},title:n==null?void 0:n.userName}))}}),j(()=>{D("mounted")}),(n,r)=>{const f=q,d=H,i=J;return l(),v("div",Q,[O("div",X,[y(f,{type:"simple",size:"default",class:"row-title"},{default:c(()=>r[3]||(r[3]=[g(" 人员维修工单数量统计 ")])),_:1}),y(i,{modelValue:t(e).activeName_Ring,"onUpdate:modelValue":r[0]||(r[0]=a=>t(e).activeName_Ring=a),type:"border-card",lazy:!0,class:T(["tabs",{darkblue:t(k).isDark}]),onTabChange:V},{default:c(()=>[(l(!0),v(R,null,C(t(e).tabs,(a,o)=>(l(),b(d,{key:o,label:a.label,name:a.value},{default:c(()=>[t(e).option2?(l(),b(t(z),{key:1,ref_for:!0,ref:"refChart2"+a.value,autoresize:"",theme:"dark",option:t(e).option2},null,8,["option"])):(l(),v("div",Y,"暂无数据"))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","class"])]),O("div",Z,[y(f,{type:"simple",size:"default",class:"row-title"},{default:c(()=>r[4]||(r[4]=[g(" 人员维修工单数量排行 ")])),_:1}),y(i,{modelValue:t(e).activeName_Bar1,"onUpdate:modelValue":r[1]||(r[1]=a=>t(e).activeName_Bar1=a),type:"border-card",class:T(["tabs",{darkblue:t(k).isDark}]),onTabChange:I},{default:c(()=>[(l(!0),v(R,null,C(t(e).tabs,(a,o)=>(l(),b(d,{key:o,label:a.label,name:a.value},{default:c(()=>[t(e).option3?(l(),b(t(z),{key:1,ref_for:!0,ref:"refChart3"+a.value,autoresize:"",theme:"dark",option:t(e).option3},null,8,["option"])):(l(),v("div",ee,"暂无数据"))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","class"])]),O("div",ae,[y(f,{type:"simple",size:"default",class:"row-title"},{default:c(()=>r[5]||(r[5]=[g(" 维修人员完成率排行 ")])),_:1}),y(i,{modelValue:t(e).activeName_Bar2,"onUpdate:modelValue":r[2]||(r[2]=a=>t(e).activeName_Bar2=a),type:"border-card",class:T(["tabs",{darkblue:t(k).isDark}]),onTabChange:x},{default:c(()=>[(l(!0),v(R,null,C(t(e).tabs,(a,o)=>(l(),b(d,{key:o,label:a.label,name:a.value},{default:c(()=>[t(e).option4?(l(),b(t(z),{key:1,ref_for:!0,ref:"refChart4"+a.value,autoresize:"",theme:"dark",option:t(e).option4},null,8,["option"])):(l(),v("div",te,"暂无数据"))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","class"])])])}}}),ce=P(re,[["__scopeId","data-v-0365d264"]]);export{ce as default};
