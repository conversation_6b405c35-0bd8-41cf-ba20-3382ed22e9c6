package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/4/7 16:52
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DATASOURCE_RELATION)
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceRelationEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ALARM_ORIGINATOR_ID_PROPERTY)
    private String originateId;

    @Column(name = ModelConstants.DATASOURCE_ID)
    private String dataSourceId;

    @Column(name = ModelConstants.ENTITY_TYPE_PROPERTY)
    private String entityType;


}
