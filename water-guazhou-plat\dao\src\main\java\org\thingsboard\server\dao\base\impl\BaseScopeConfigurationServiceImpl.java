package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseScopeConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration;
import org.thingsboard.server.dao.sql.base.BaseScopeConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseScopeConfigurationPageRequest;

/**
 * 公共管理平台-范围设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class BaseScopeConfigurationServiceImpl implements IBaseScopeConfigurationService {
    @Autowired
    private BaseScopeConfigurationMapper baseScopeConfigurationMapper;

    /**
     * 查询公共管理平台-范围设置
     *
     * @param id 公共管理平台-范围设置主键
     * @return 公共管理平台-范围设置
     */
    @Override
    public BaseScopeConfiguration selectBaseScopeConfigurationById(String id) {
        return baseScopeConfigurationMapper.selectBaseScopeConfigurationById(id);
    }

    /**
     * 查询公共管理平台-范围设置列表
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 公共管理平台-范围设置
     */
    @Override
    public IPage<BaseScopeConfiguration> selectBaseScopeConfigurationList(BaseScopeConfigurationPageRequest baseScopeConfiguration) {
        return baseScopeConfigurationMapper.selectBaseScopeConfigurationList(baseScopeConfiguration);
    }

    /**
     * 新增公共管理平台-范围设置
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 结果
     */
    @Override
    public int insertBaseScopeConfiguration(BaseScopeConfiguration baseScopeConfiguration) {
        baseScopeConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseScopeConfigurationMapper.insertBaseScopeConfiguration(baseScopeConfiguration);
    }

    /**
     * 修改公共管理平台-范围设置
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 结果
     */
    @Override
    public int updateBaseScopeConfiguration(BaseScopeConfiguration baseScopeConfiguration) {
        return baseScopeConfigurationMapper.updateBaseScopeConfiguration(baseScopeConfiguration);
    }

    /**
     * 批量删除公共管理平台-范围设置
     *
     * @param ids 需要删除的公共管理平台-范围设置主键
     * @return 结果
     */
    @Override
    public int deleteBaseScopeConfigurationByIds(List<String> ids) {
        return baseScopeConfigurationMapper.deleteBaseScopeConfigurationByIds(ids);
    }

    /**
     * 删除公共管理平台-范围设置信息
     *
     * @param id 公共管理平台-范围设置主键
     * @return 结果
     */
    @Override
    public int deleteBaseScopeConfigurationById(String id) {
        return baseScopeConfigurationMapper.deleteBaseScopeConfigurationById(id);
    }
}
