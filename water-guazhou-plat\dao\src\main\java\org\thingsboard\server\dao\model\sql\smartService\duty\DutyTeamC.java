package org.thingsboard.server.dao.model.sql.smartService.duty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 班组人员
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-14
 */
@TableName("tb_service_duty_team_c")
@Data
public class DutyTeamC {

    @TableId
    private String id;

    private String mainId;

    private String person;

    private String tenantId;

}
