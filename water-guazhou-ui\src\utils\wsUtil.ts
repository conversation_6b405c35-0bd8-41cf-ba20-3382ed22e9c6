let webSocket:any = null
let global_callback:any = null

export const initSocket = (url:string) => {
  webSocket = new WebSocket(url)
  webSocket.onmessage = function (e) {
    websocketOnMessage(e)
  }
  webSocket.onclose = function (e) {
    websocketClose(e)
  }
  webSocket.onopen = function () {
    websocketOpen()
  }
  // 连接发生错误的回调方法
  webSocket.onerror = function () {
    console.log('WebSocket连接发生错误')
  }
}

export const getWebSocket = () => {
  return webSocket
}

export const closeWebSocket = () => {
  if (webSocket) {
    webSocket.close()
    webSocket = null
  }
}

// 实际调用的方法
export const sendSock = (agentData:any, callback?:any) => {
  if (callback) { global_callback = callback }
  if (webSocket.readyState === webSocket.OPEN) {
    // 若是ws开启状态
    websocketSend(agentData)
  } else if (webSocket.readyState === webSocket.CONNECTING) {
    // 若是 正在开启状态，则等待1s后重新调用
    setTimeout(() => {
      sendSock(agentData, callback)
    }, 1000)
  } else {
    // 若未开启 ，则等待1s后重新调用
    setTimeout(() => {
      sendSock(agentData, callback)
    }, 5000)
  }
}

// 数据接收
export const websocketOnMessage = e => {
  global_callback(JSON.parse(e.data))
}

// 数据发送
export const websocketSend = agentData => {
  webSocket.send(JSON.stringify(agentData))
}

// 关闭
export const websocketClose = e => {
  console.log('connection closed (' + e.code + ')')
}

export const websocketOpen = (e?:any) => {
  console.log('连接成功', e)
}
