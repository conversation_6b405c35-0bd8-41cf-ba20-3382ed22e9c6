import{_ as H}from"./index-C9hz-UZb.js";import{d as K,M as Q,bF as i,r as g,c as y,a8 as U,s as C,a6 as $,bB as J,o as Y,bu as Z,ah as ee,ay as ae,g as q,n as F,q as d,i as o,F as _,cs as V,h as te,an as B,j as ne,dF as oe,dA as re,aq as se,al as le,b7 as ie,aj as ce,C as de}from"./index-r0dFAfgr.js";import{_ as pe}from"./CardSearch-CB_HNR-Q.js";import{l as me}from"./echart-Bd1EZNhy.js";import{f as ue}from"./flowMonitoring-DtJlPj0G.js";import{u as fe}from"./useStation-DJgnSZIA.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const he={class:"wrapper"},ye=K({__name:"index",setup(_e){const{$messageWarning:N}=Q(),{getStationTree:z}=fe(),O=i().date(),t=g({type:"date",chartOption:null,activeName:"echarts",data:null,dataX:[],stationTree:[],chartName:""}),p=y(),A=y("day"),b=y(),v=y(),P=y(),E=g([]),m=g({defaultParams:{queryType:"day",day:[i().date(O-2),i().date(O)],month:[i(),i()]},filters:[{type:"select-tree",label:"监测点:",defaultExpandAll:!0,field:"stationId",clearable:!1,width:"200px",options:U(()=>t.stationTree),nodeClick:e=>{t.chartName=e.label}},{type:"select",label:"比较类型:",field:"queryType",clearable:!1,width:"200px",options:[{label:"日分时(时间段)",value:"day"},{label:"月分日(时间段)",value:"month"}],itemContainerStyle:{width:"240px"},onChange:e=>{R(e)}},{type:"daterange",label:"日期",field:"day",clearable:!1,handleHidden:(e,a,n)=>{n.hidden=e.queryType==="month"}},{type:"monthrange",label:"日期",field:"month",clearable:!1,handleHidden:(e,a,n)=>{n.hidden=e.queryType==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:C(le),click:()=>{var a;s.pagination.page=1,(((a=p.value)==null?void 0:a.queryParams)||{}).stationId?S():N("选择监测点")}},{type:"default",perm:!0,text:"重置",svgIcon:C(ie),click:()=>{var e;(e=p.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",hide:()=>t.activeName!=="list",svgIcon:C(ce),click:()=>X()}]}]}),L={day:"daterange",month:"monthrange"},R=e=>{var n,r;A.value=e;const a=(n=m.filters)==null?void 0:n.find(l=>l.field==="date");a.type=L[e],e==="monthrange"&&(m.defaultParams={...m.defaultParams,date:[i().add(-1,"month").startOf("month"),i().endOf("month")]},(r=p.value)==null||r.resetForm())},s=g({loading:!1,dataList:[],columns:[],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, sizes, jumper",handleSize:e=>{s.pagination.limit=e},refreshData:({page:e,size:a})=>{s.pagination.page=e,s.pagination.limit=a,s.dataList=E.slice((e-1)*a,e*a)}}}),S=()=>{var l;const e=((l=p.value)==null?void 0:l.queryParams)||{},[a,n]=e[e.queryType]||[],r={stationId:e.stationId,queryType:e.queryType,start:a?i(a).startOf(e.queryType).valueOf():null,end:n?i(n).endOf(e.queryType).valueOf():null};ue(r).then(u=>{var f,h;const c=(f=u.data)==null?void 0:f.data;if(c){t.data=c;const T=[],I=[{prop:"name",label:"监测点名称",align:"center",minWidth:120},{prop:"ts",label:"时间",align:"center",minWidth:120}];let w="";t.dataX=[];for(const x in c){const W=(h=c[x])==null?void 0:h.map((G,k)=>(w||(t.dataX.push(k),I.push({prop:k+"",label:k+"",align:"center"})),G.value));w=x;const D={ts:x,...W,name:t.chartName};console.log(D),T.push(D)}s.columns=I,s.dataList=T,s.pagination.total=T.length,M()}else N("无数据")})},j=()=>{var e;(e=b.value)==null||e.resize()},M=()=>{var r,l;const e=me(600);e.series=[],e.xAxis.data=t.dataX;for(const u in t.data){const c=(r=t.data[u])==null?void 0:r.map(h=>h.value),f={name:u,smooth:!0,data:c,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.series.push(f)}(l=b.value)==null||l.clear();const n=$({callOnAdd:!0});J(()=>{v.value&&n.listenTo(v.value,()=>{t.chartOption=e,j()})})},X=()=>{var e;(e=P.value)==null||e.exportTable()};return Y(async()=>{}),Z(async()=>{var n;const e=await z("压力监测站,测流压站");t.stationTree=e;const a=ee(e);m.defaultParams={...m.defaultParams,stationId:a.id},t.chartName=a.label,(n=p.value)==null||n.resetForm(),S()}),(e,a)=>{const n=pe,r=oe,l=re,u=se,c=ae("VChart"),f=H;return q(),F("div",he,[d(n,{ref_key:"cardSearch",ref:p,config:o(m)},null,8,["config"]),d(f,{class:"card-table",title:o(t).activeName==="list"?"压力分析列表":"压力分析曲线"},{right:_(()=>[d(l,{modelValue:o(t).activeName,"onUpdate:modelValue":a[0]||(a[0]=h=>o(t).activeName=h)},{default:_(()=>[d(r,{label:"echarts"},{default:_(()=>[d(o(V),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),d(r,{label:"list"},{default:_(()=>[d(o(V),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:_(()=>[o(t).activeName==="list"?(q(),te(u,{key:0,ref_key:"refTable",ref:P,config:o(s)},null,8,["config"])):B("",!0),o(t).activeName==="echarts"?(q(),F("div",{key:1,ref_key:"echartsDiv",ref:v,class:"card-ehcarts"},[d(c,{ref_key:"refChart",ref:b,theme:o(ne)().isDark?"dark":"light",class:"card-ehcarts",option:o(t).chartOption},null,8,["theme","option"])],512)):B("",!0)]),_:1},8,["title"])])}}}),Ne=de(ye,[["__scopeId","data-v-44a21a72"]]);export{Ne as default};
