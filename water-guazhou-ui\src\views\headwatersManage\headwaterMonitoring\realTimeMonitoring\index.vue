<!-- 水源管理-水源监测-实时监测 -->
<template>
  <div class="wrapper">
    <SLCard overlay>
      <div class="statistics">
        <div
          v-for="(item, i) in state.Statistics"
          :key="i"
          class="statistics-item"
        >
          <div class="item-inner">
            <div style="padding-right: 8px">
              <Icon
                :icon="item.icon"
                class="iconClass"
                style="font-size: 16px"
              ></Icon>
            </div>
            <span class="title">{{ item.title }}</span>
            <span class="count" :class="item.className">{{ item.count }}</span>
            <span class="unit" :class="item.className">{{ item.unit }}</span>
          </div>
        </div>
      </div>
      <!-- <div style="display: flex;justify-content: flex-end">
        <div>
          <el-input v-model="state.keywords" placeholder="请输入关键字进行筛选">
            <template #append>
              <el-button color="red" :icon="SearchIcon" @click="handleSearch" />
            </template>
          </el-input>
        </div>
      </div> -->
      <div class="title_card">
        <Search ref="refSearch" class="serch" :config="SearchConfig"></Search>
      </div>
    </SLCard>
    <div class="card-box">
      <div class="card-item" :class="{ isDark: useAppStore().isDark }">
        <div
          v-for="(monitor, x) in state.dataGroup"
          :key="x"
          class="card-content"
        >
          <SLCard title=" " class="inner-card left">
            <template #title>
              <div class="monitor-title">
                <div class="title">
                  <el-image
                    :src="state.icon"
                    style="width: 35px; height: 36px"
                  />
                  <span>{{ monitor.title }}</span>
                </div>
                <div @click="showDetail(monitor)">
                  <Icon
                    icon="ph:warning-circle-bold"
                    style="color: #4f7db8; font-size: 18px"
                  ></Icon>
                </div>
              </div>
            </template>
            <div class="monitor-table">
              <div class="monitor">
                <div
                  v-for="(item, t) in monitor.dataList"
                  :key="t"
                  class="box-1"
                >
                  <div>
                    {{ item.propertyName }}
                    {{ item.unit ? '(' + item.unit + ')' : '' }}
                  </div>
                  <div>{{ item.value || '无' }}</div>
                </div>
              </div>
            </div>
          </SLCard>
        </div>
      </div>
    </div>
    <SLDrawer ref="refDrawer" :config="drawerConfig">
      <template #title>
        <el-image :src="state.icon" style="width: 35px; height: 36px" />
        <span style="padding-left: 10px">{{ state.drawerTitle }}</span>
      </template>
      <station-detail-monitoring
        :station-id="state.stationId"
        :monitor="state.monitor"
      >
      </station-detail-monitoring>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { reactive } from 'vue';
import { Icon } from '@iconify/vue';
import { ICONS } from '@/common/constans/common';
import { useAppStore, useBusinessStore } from '@/store';
import useStation from '@/hooks/station/useStation';
import {
  getWaterSupplyInfoView,
  getWaterSupplyInfoDetail
} from '@/api/headwatersManage/headwaterMonitoring';
import stationDetailMonitoring from './components/stationDetailMonitoring.vue';
import Group1 from '@/assets/images/Group1.png';

const refSearch = ref<ISearchIns>();
const { getStationTree } = useStation();
const allStation = ref<any[]>([]);
const state = reactive<{
  Statistics: {
    title: string;
    count: number;
    unit: string;
    className: string;
    icon: string;
  }[];
  ShuiChangDatas: any[];
  dataGroup: any[];
  activeName: string;
  tabsList: any[];
  stationTree: any;
  treeDataType: string;
  monitor: any;
  stationId: string;
  icon: string;
  drawerTitle: string;
  keywords: string;
}>({
  monitor: {},
  drawerTitle: '',
  Statistics: [
    {
      className: 'text-orange',
      title: '总供水量',
      count: 0,
      unit: 'm³',
      icon: 'ic:outline-water-drop'
    },
    {
      className: 'text-green',
      title: '昨日供水量',
      count: 0,
      unit: 'm³',
      icon: 'subway:refresh-time'
    },
    {
      className: 'text-blue',
      title: '今日供水量',
      count: 0,
      unit: 'm³',
      icon: 'subway:time-2'
    },
    {
      className: 'text-green',
      title: '上月供水量',
      count: 0,
      unit: 'm³',
      icon: 'ri:calendar-event-line'
    },
    {
      className: 'text-blue',
      title: '本月供水量',
      count: 0,
      unit: 'm³',
      icon: 'ri:calendar-line'
    },
    {
      className: 'text-red',
      title: '报警数',
      count: 0,
      unit: '个',
      icon: 'ph:bell-simple-bold'
    }
  ],
  ShuiChangDatas: [],
  dataGroup: [],
  activeName: '全部',
  tabsList: [],
  stationTree: [],
  treeDataType: 'Project',
  stationId: '',
  icon: Group1,
  keywords: ''
});

const refDrawer = ref<ISLDrawerIns>();
const drawerConfig = reactive<IDrawerConfig>({
  title: '',
  labelWidth: '130px',
  width: 1400,
  group: [],
  cancel: false,
  // modalClass: 'lightColor',
  onClosed: () => {
    state.treeDataType === 'Project';
    state.stationId = '';
  }
});

const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '区域划分',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data;
      state.treeDataType = data.data.type as string;
      if (state.treeDataType === 'Project') {
        useBusinessStore().SET_selectedProject(data);
        allStation.value = [];
        // state.ShuiChangDatas = []
        await refreshData();
      }
    }
  }
});

const showDetail = (data) => {
  refDrawer.value?.openDrawer();
  nextTick(() => {
    state.drawerTitle = data.title;
    state.monitor = data;
    state.stationId = data.id;
    state.treeDataType = 'Station';
  });
  console.log(data.id);
};
// const SearchConfig = reactive<ISearch>({
//   filters: [
//     {
//       type: 'select-tree',
//       label: '查看范围',
//       field: 'projectId',
//       options: store.business.projectList,
//       nodeClick: data => {
//         store.business.SET_selectedProject(data)
//         refreshData(data)
//       }
//     }
//   ],
//   defaultParams: {
//     projectId: store.business.selectedProject?.value
//   }
// })

const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  stretch: false,
  tabs: [],
  handleTabClick: (tab: any) => {
    state.dataGroup = allStation.value;
    if (tab.props.name !== '全部') {
      const ShuiChangDatas = allStation.value.filter(
        (item) => item.id === tab.props.name || item.title === '全部'
      );
      state.dataGroup = ShuiChangDatas;
      state.ShuiChangDatas = ShuiChangDatas;
    }
  }
});
const refreshData = async (data?: NormalOption) => {
  await getWaterSupplyInfoView({
    projectId: data?.value || useBusinessStore().selectedProject?.value
  })
    .then((res) => {
      state.Statistics[0].count = res.data.data?.yesterdayWaterSupply || 0;
      state.Statistics[1].count = res.data.data?.todayWaterSupply || 0;
      state.Statistics[2].count = res.data.data?.lastMonthWaterSupply || 0;
      state.Statistics[3].count = res.data.data?.monthWaterSupply || 0;
      state.Statistics[4].count = res.data.data?.totalWaterSupply || 0;
      state.Statistics[5].count = res.data.data?.alarmNum || 0;
    })
    .catch(() => {
      state.Statistics[0].count = 0;
      state.Statistics[1].count = 0;
      state.Statistics[2].count = 0;
      state.Statistics[3].count = 0;
      state.Statistics[4].count = 0;
      state.Statistics[5].count = 0;
    });
  const res = await getWaterSupplyInfoDetail({
    projectId: data?.value
  });
  const stationDatas = res.data?.data;
  console.log(stationDatas);
  tabsConfig.tabs = [{ value: '全部', label: '全部' }];
  stationDatas.map((stationData) => {
    tabsConfig.tabs.push({
      value: stationData.stationId,
      label: stationData.name
    });
    allStation.value.push({
      id: stationData.stationId,
      title: stationData.name,
      dataList: stationData.dataList || []
    });
    console.log('dddd', allStation.value);
    state.ShuiChangDatas = allStation.value;
    state.dataGroup = allStation.value;
  });
};

const activeName = ref(0);

const handleClick = (tab: any) => {
  // carditem.value = state.ShuiChangDatas[tab]
};

// 搜索
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '',
      field: 'keywords',
      placeholder: '请输入关键字进行筛选',
      btn: {
        icon: ICONS.QUERY,
        type: 'primary',
        perm: true,
        click: () => {
          const query = refSearch.value?.queryParams || {};
          const cs = query.keywords || '';
          state.dataGroup = state.ShuiChangDatas.filter((monitor) =>
            monitor.title.includes(cs)
          );
        }
      }
    }
  ]
});

onMounted(async () => {
  const treeData = await getStationTree('水源地');
  TreeData.data = treeData;
  TreeData.currentProject = treeData[0];
  await refreshData();
});
// 切换tabs
// const handleClick = val => {
//   console.log('value', val)
// }
</script>
<style lang="scss" scoped>
.dark {
  .iconClass {
    color: #ffffff;
  }

  .monitor-table {
    background-image: linear-gradient(#222536 50%, #313748 50%) !important;
    background-size: 100% 144px !important;

    .monitor-name {
      background: #383e53 !important;
    }

    .monitor {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      background-image: linear-gradient(#222536 50%, #313748 50%) !important;
      background-size: 100% 144px !important;
    }
  }

  .box-1 {
    div:nth-child(1) {
      color: #e8e9ec !important;
    }
  }
}

.title_card {
  padding: 0 20px;
  background-color: var(--el-bg-color-page);
  display: flex;
  align-items: center;
  height: 56px;
  justify-content: space-between;

  .serch {
    justify-content: flex-end;
  }
}

.wrapper-content {
  height: 100%;
}

.title-header {
  margin-right: auto;
}

.statistics {
  display: flex;
  justify-content: space-between;
  // padding: 0px 100px;
}

.statistics-item {
  //width: 16%;
  margin: 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  .item-inner {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    padding: 10px 2px;
    display: flex;
    align-items: center;
    font-size: 16px;

    .item-content {
      text-align: center;
      margin: 0 auto;
      word-break: break-word;
    }

    .total {
      // display: flex;
      align-items: baseline;
      margin-left: auto;
    }

    .item-text {
      margin: 0 8px;
    }

    .title {
      font-size: 16px;
      font-weight: 600;
      //padding: 0 8px;
    }

    .count {
      font-size: 24px;
      font-weight: 600;
    }

    .unit {
      font-size: 16px;
      padding-left: 4px;
    }
  }
}

.text-orange {
  color: #f59b34;
}

.text-green {
  color: #81af79;
}

.text-blue {
  color: #318dff;
}

.text-red {
  color: #ea556f;
}

.card {
  height: calc(100vh - 200px);
}

:deep(.el-card__body) {
  padding: 12px;
  display: grid;
  align-content: center;
  justify-content: space-between;
  flex-wrap: wrap;
  // grid-template-rows: 425px 425px 425px;
  grid-template-columns: 24% 24% 24% 24%;
}

.dark {
  .card-box {
    background-color: #191c27;
  }
}

.card-box {
  width: 100%;
  margin-bottom: 15px;
  height: calc(100% - 100px);
  overflow: auto;

  &:last-child {
    margin-bottom: 0;
  }

  .card-item {
    //height: 500px;
    width: 100%;
    overflow-y: auto;
    margin-bottom: 16px;
    padding: 12px 0;
    display: grid;
    align-content: center;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-template-columns: 24% 24% 24% 24%;

    &:last-child {
      margin-bottom: 0;
    }

    &.isDark {
      :deep(.el-card__header) {
        background-color: #415c88;
      }
    }

    .card-title {
      display: flex;
      align-items: center;
    }

    .card-content {
      margin-bottom: 30px;

      // display: flex;
      // justify-content: space-between;
      // width: calc(50% - 8px);
      .time-box {
        color: #f54141;
        text-align: center;
        // background-color: #222536;
        border-bottom: 1px solid #f54141;
        padding: 5px 0;
      }
    }

    .inner-card {
      // width: calc(50% - 8px);
      // width: 30%;
      padding-left: 0;
      padding-right: 0;
      height: 430px;

      &.left {
        height: 415px;
      }
    }

    //   .chart-item {
    //     height: 200px;
    //     margin-bottom: 15px;
    //     &:last-child {
    //       margin-bottom: 0;
    //     }
    //   }
    .monitor-title {
      display: flex;
      justify-content: space-between;
      padding: 0 17px;
      width: 100%;
      align-items: center;
      z-index: 1;

      .title {
        display: flex;
        align-items: center;

        span {
          padding-left: 10px;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }

    .monitor-table {
      width: 100%;
      height: 100%;
      overflow: auto;

      .monitor-name {
        height: 72px;
        line-height: 72px;
        background: #f9fafe;
      }

      .monitor {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        background-image: linear-gradient(#ffffff 50%, #f9fafe 50%);
        background-size: 100% 144px !important;
      }

      .box-1 {
        text-align: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 72px;
        line-height: 24px;
        overflow-y: auto;
        width: 33.33%;

        div:nth-child(1) {
          color: #7c8295;
          font-size: 12px;
          font-weight: 400;
        }

        div:nth-child(2) {
          color: #4f7db8;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
}

.group-title {
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #409eff;
  margin: 5px 0;
}

.monitor-box {
  display: flex;
  flex-wrap: wrap;

  .monitor-item {
    flex-basis: 25%;
    min-height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border: 1px solid #e8e8ed;
    background-color: #fff;

    &.isDark {
      border: 1px solid #222536;
      background-color: #1a1d2d;
    }

    .count {
      color: #42b8fe;
      font-size: 18px;
      line-height: 30px;
    }

    .total {
      display: flex;
      align-items: baseline;
    }

    .label {
      color: #949ab8;
      font-size: 14px;
    }

    .unit {
      font-size: 12px;
      margin-left: 8px;
    }
  }
}

div::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
</style>
