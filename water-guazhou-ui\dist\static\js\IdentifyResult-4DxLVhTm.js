import{e as t,y as e,W as y,a4 as d,b as m,a as l,o as h,m as v}from"./Point-WxyopZva.js";import{d6 as w,ar as f,w as b,aD as j,g as u,d7 as g}from"./MapView-DaoQedLH.js";import{R as N}from"./index-r0dFAfgr.js";var n;let r=n=class extends y{static from(o){return d(n,o)}constructor(o){super(o),this.dpi=96,this.floors=null,this.gdbVersion=null,this.geometry=null,this.geometryPrecision=null,this.height=400,this.layerIds=null,this.layerOption="top",this.mapExtent=null,this.maxAllowableOffset=null,this.returnFieldName=!0,this.returnGeometry=!1,this.returnM=!1,this.returnUnformattedValues=!0,this.returnZ=!1,this.spatialReference=null,this.sublayers=null,this.timeExtent=null,this.tolerance=null,this.width=400}};t([e({type:Number,json:{write:!0}})],r.prototype,"dpi",void 0),t([e()],r.prototype,"floors",void 0),t([e({type:String,json:{write:!0}})],r.prototype,"gdbVersion",void 0),t([e({types:w,json:{read:f,write:!0}})],r.prototype,"geometry",void 0),t([e({type:Number,json:{write:!0}})],r.prototype,"geometryPrecision",void 0),t([e({type:Number,json:{write:!0}})],r.prototype,"height",void 0),t([e({type:[Number],json:{write:!0}})],r.prototype,"layerIds",void 0),t([e({type:["top","visible","all","popup"],json:{write:!0}})],r.prototype,"layerOption",void 0),t([e({type:b,json:{write:!0}})],r.prototype,"mapExtent",void 0),t([e({type:Number,json:{write:!0}})],r.prototype,"maxAllowableOffset",void 0),t([e({type:Boolean,json:{write:!0}})],r.prototype,"returnFieldName",void 0),t([e({type:Boolean,json:{write:!0}})],r.prototype,"returnGeometry",void 0),t([e({type:Boolean,json:{write:!0}})],r.prototype,"returnM",void 0),t([e({type:Boolean,json:{write:!0}})],r.prototype,"returnUnformattedValues",void 0),t([e({type:Boolean,json:{write:!0}})],r.prototype,"returnZ",void 0),t([e({type:m,json:{write:!0}})],r.prototype,"spatialReference",void 0),t([e()],r.prototype,"sublayers",void 0),t([e({type:j,json:{write:!0}})],r.prototype,"timeExtent",void 0),t([e({type:Number,json:{write:!0}})],r.prototype,"tolerance",void 0),t([e({type:Number,json:{write:!0}})],r.prototype,"width",void 0),r=n=t([l("esri.rest.support.IdentifyParameters")],r);const F=r;let i=class extends y{constructor(o){super(o),this.displayFieldName=null,this.feature=null,this.layerId=null,this.layerName=null}readFeature(o,s){return u.fromJSON({attributes:{...s.attributes},geometry:{...s.geometry}})}writeFeature(o,s){if(!o)return;const{attributes:a,geometry:p}=o;a&&(s.attributes={...a}),N(p)&&(s.geometry=p.toJSON(),s.geometryType=g.toJSON(p.type))}};t([e({type:String,json:{write:!0}})],i.prototype,"displayFieldName",void 0),t([e({type:u})],i.prototype,"feature",void 0),t([h("feature",["attributes","geometry"])],i.prototype,"readFeature",null),t([v("feature")],i.prototype,"writeFeature",null),t([e({type:Number,json:{write:!0}})],i.prototype,"layerId",void 0),t([e({type:String,json:{write:!0}})],i.prototype,"layerName",void 0),i=t([l("esri.rest.support.IdentifyResult")],i);const O=i;export{F as u,O as y};
