package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectAccept;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoProjectAcceptSaveRequest extends SaveRequest<SoProjectAccept> {
    // 所属项目编号
    @NotNullOrEmpty
    private String projectCode;

    // 总验时间
    @NotNullOrEmpty
    private Date acceptTime;

    // 总验说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoProjectAccept build() {
        SoProjectAccept entity = new SoProjectAccept();
        entity.setProjectCode(projectCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoProjectAccept update(String id) {
        SoProjectAccept entity = new SoProjectAccept();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoProjectAccept entity) {
        entity.setAcceptTime(acceptTime);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}