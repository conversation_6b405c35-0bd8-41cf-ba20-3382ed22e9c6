import{d as A,cN as F,r as M,c as O,o as E,ay as Q,g as c,n as u,bo as g,i as o,q as m,p as s,F as V,aB as I,aJ as k,h as W,G as $,bh as v,ab as q,bt as J,dz as j,dA as K,br as X,C as Y}from"./index-r0dFAfgr.js";import{h as Z}from"./chart-wy3NEK2T.js";import{l as tt}from"./onemap-CEunQziB.js";import{f as at,d as et}from"./zhandian-YaGuQZe6.js";import{g as h}from"./echarts-Bhn8T7lM.js";import{u as it}from"./useDetector-BRcb7GRN.js";const ot={class:"one-map-detail"},rt={class:"row1"},nt={class:"pie-charts"},st={class:"pie-chart"},lt={class:"pie-chart"},dt={class:"row2"},ct={class:"detail-attrgrou-radio"},ut={class:"detail-right"},pt={class:"list-items overlay-y"},mt={class:"item-label"},ht={class:"item-content"},_t={class:"chart-box"},ft=A({__name:"WaterQualityMonitoringDetail",emits:["refresh","mounted"],setup(vt,{expose:z,emit:H}){const C=H,{proxy:y}=F(),t=M({curRadio:"",radioGroup:[],pieChart1:h(0,{max:100,title:"浊度(NTU)"}),pieChart2:h(0,{max:100,title:"余氯(mg/L)"}),pieChart3:h(0,{max:1e3,title:"PH(pH)"}),lineChartOption:null,stationRealTimeData:[],detailLoading:!1}),w=async l=>{C("refresh",{title:l.name}),t.curRow=l,Array.from({length:4}).map((a,i)=>{var n;(n=y.$refs["refChart"+(i+1)])==null||n.resize()}),t.detailLoading=!0;try{const a=e=>{const p=q(e);return{value:+p.value.toFixed(2),unit:p.unit}},i=tt({stationId:l.stationId}).then(e=>{var R,b,D,G,N,T;const p=((R=e.data.data.turbidity)==null?void 0:R.map(f=>f.value))||[],_=((b=e.data.data.remainder)==null?void 0:b.map(f=>f.value))||[],r=((D=e.data.data.ph)==null?void 0:D.map(f=>f.value))||[];t.lineChartOption=Z({line1:{data:p,unit:"NTU",name:"浊度"},line2:{data:_,unit:"mg/L",name:"余氯"},line3:{data:r,unit:"",name:"PH"}});const d=a(((G=e.data.data)==null?void 0:G.currentTurbidity)||0),S=a(((N=e.data.data)==null?void 0:N.currentRemainder)||0),U=a(((T=e.data.data)==null?void 0:T.currentPH)||0);t.pieChart1=h(d.value,{max:100,title:"浊度("+(d.unit||"")+"NTU)"}),t.pieChart2=h(S.value,{max:100,title:"余氯("+(d.unit||"")+"mg/L)"}),t.pieChart3=h(U.value,{max:1e3,title:"PH("+(d.unit||"")+"pH)"})}),n=at({stationId:l.stationId}).then(e=>{t.radioGroup=e.data||[],t.curRadio=t.radioGroup[0],x(t.radioGroup[0])});Promise.all([i,n]).finally(()=>{t.detailLoading=!1})}catch{t.detailLoading=!1}},x=async l=>{var i;const a=await et((i=t.curRow)==null?void 0:i.stationId,l);t.stationRealTimeData=a.data||[]};z({refreshDetail:w});const B=()=>{Array.from({length:3}).map((l,a)=>{var i;(i=y.$refs["refChart"+(a+1)])==null||i.resize()})},P=it(),L=O();return E(()=>{C("mounted"),P.listenToMush(L.value,B)}),(l,a)=>{const i=J,n=Q("VChart"),e=j,p=K,_=X;return c(),u("div",ot,[g((c(),u("div",rt,[m(i,{size:"default",title:"水质监测",type:"simple",class:"row-title"}),s("div",nt,[s("div",{ref_key:"refChartDiv",ref:L,class:"pie-chart"},[m(n,{ref:"refChart1",option:o(t).pieChart1},null,8,["option"])],512),s("div",st,[m(n,{ref:"refChart2",option:o(t).pieChart2},null,8,["option"])]),s("div",lt,[m(n,{ref:"refChart3",option:o(t).pieChart3},null,8,["option"])])])])),[[_,o(t).detailLoading]]),s("div",dt,[s("div",ct,[m(p,{modelValue:o(t).curRadio,"onUpdate:modelValue":a[0]||(a[0]=r=>o(t).curRadio=r),onChange:x},{default:V(()=>[(c(!0),u(I,null,k(o(t).radioGroup,(r,d)=>(c(),W(e,{key:d,label:r},{default:V(()=>[$(v(r),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),s("div",ut,[g((c(),u("div",pt,[(c(!0),u(I,null,k(o(t).stationRealTimeData,(r,d)=>(c(),u("div",{key:d,class:"list-item"},[s("div",mt,v(r.propertyName),1),s("div",ht,v(r.value||"--")+" "+v(r.unit),1)]))),128))])),[[_,o(t).detailLoading]]),g((c(),u("div",_t,[m(n,{ref:"refChart4",option:o(t).lineChartOption},null,8,["option"])])),[[_,o(t).detailLoading]])])])])}}}),bt=Y(ft,[["__scopeId","data-v-4c50a290"]]);export{bt as default};
