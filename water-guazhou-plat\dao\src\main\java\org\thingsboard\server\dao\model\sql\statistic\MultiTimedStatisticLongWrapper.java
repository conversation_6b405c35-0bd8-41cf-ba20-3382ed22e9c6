package org.thingsboard.server.dao.model.sql.statistic;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class MultiTimedStatisticLongWrapper implements StatisticItem {
    private Date from;
    private Date to;

    private List<StatisticLong> statisticLongs = new ArrayList<>();


    public MultiTimedStatisticLongWrapper(Date from, Date to) {
        this.from = from;
        this.to = to;
    }

    public Date getFrom() {
        return from;
    }

    public Date getTo() {
        return to;
    }

    public List<StatisticLong> getStatisticLongs() {
        return statisticLongs;
    }

    public void add(StatisticLong statisticLong) {
        statisticLongs.add(statisticLong);
    }

    @Override
    public boolean isEmpty() {
        return statisticLongs.isEmpty();
    }
}
