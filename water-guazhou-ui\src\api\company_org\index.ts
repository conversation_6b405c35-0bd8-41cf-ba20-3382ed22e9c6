import request from '@/plugins/axios';

// 供水单位查询
// const params = {
//   name?: string,
//   parentId?: string
// }
export function getWaterSupplyUnit(params: any) {
  return request({
    url: '/api/organization',
    method: 'get',
    params
  });
}

// 添加供水单位
// const params = {
//   name: string,
//   type: string,
//   phone: string,
//   parentId: string,
//   orderNum: string | number
// }
export function postWaterSupplyUnit(params: any) {
  return request({
    url: '/api/organization',
    method: 'post',
    data: params
  });
}

// 获取供水单位项目树
// depth:string
export function getWaterSupplyTree(depth: string | number) {
  return request({
    url: `/api/organization/tree/${depth}`,
    method: 'get'
  });
}

// 删除供水单位
// id:string
export function deleteWaterSupply(id: string) {
  return request({
    url: `/api/organization/${id}`,
    method: 'delete'
  });
}

// 修改供水单位
// id:string
// const params = {
//   name: string,
//   type: string,
//   phone: string,
//   parentId: string,
//   orderNum: string | number
// }
export function patchWaterSupplyUnit(id: string, params: any) {
  return request({
    url: `/api/organization/${id}`,
    method: 'patch',
    data: params
  });
}

// 获取单位下的部门
// pid:string
export function getDepartmentTree(pid: string | number) {
  return request({
    url: `/api/department/tree/${pid}`,
    method: 'get'
  });
}

// 添加部门
// const params = {
//   name: string,
//   type: string,
//   parentId: string,
//   orderNum: string | number
// }
export function postDepartment(params: any) {
  return request({
    url: '/api/department',
    method: 'post',
    data: params
  });
}

// 删除部门
// id:string
export function deleteDepartment(id: string) {
  return request({
    url: `/api/department/${id}`,
    method: 'delete'
  });
}

// 查询组织架构
// const params = {
//   name: string,
//   parentId: string
// }
export function getDepartment(params: any) {
  return request({
    url: '/api/department',
    method: 'get',
    params
  });
}

// 修改组织架构
// id:string
// const params = {
//   name: string,
//   type: string,
//   parentId: string,
//   orderNum: string | number
// }
export function patchDepartment(id: string, params: any) {
  return request({
    url: `/api/department/${id}`,
    method: 'patch',
    data: params
  });
}

/**
 * 获取所有用户
 */
export function getAllUser() {
  return request({
    url: `/api/user/getAllByName`,
    method: 'get'
  });
}
