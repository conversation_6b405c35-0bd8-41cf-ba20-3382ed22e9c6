<!-- 指令接受 -->
<template>
  <div class="wrapper">
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <CardSearch
      ref="refSearch"
      class="mag_top_10"
      :config="cardSearchConfig"
    />
    <DialogForm
      ref="refForm"
      :config="rejectConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import Cookies from 'js-cookie'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { commandStatus } from '../data'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import { getUserList } from '@/api/user/index'
import { getOrderRecordList, postCommandReception, postCommandRejected } from '@/api/productionScheduling/schedulingInstructions'
import { formatDate } from '@/utils/DateFormatter'
import { removeSlash } from '@/utils/removeIdSlash'

const refSearch = ref<ICardSearchIns>()

const refForm = ref<IDialogFormIns>()

const cardSearchConfig = ref<ISearch>({
  defaultParams: {
    receiveUserId: removeSlash(Cookies.get('userId') || '')
  },
  filters: [
    { label: '', field: 'receiveUserId', type: 'select', options: computed(() => data.UserList) as any, readonly: true },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '接收',
          svgIcon: shallowRef(Plus),
          click: () => commandReception()
        },
        {
          perm: true,
          text: '拒绝',
          type: 'danger',
          click: () => {
            refForm.value?.openDialog()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  handleSelectChange: val => {
    TableConfig.selectList = val
  },
  selectList: [],
  columns: [
    { label: '发送人', prop: 'sendUserName' },
    { label: '发送时间', prop: 'sendTime' },
    { label: '接收站点', prop: 'receiveDeptName' },
    { label: '指令内容', prop: 'sendContent' },
    { label: '执行时间', prop: 'executionTime' },
    { label: '备注', prop: 'remark' },
    { label: '指令状态',
      prop: 'commandStatus',
      tag: true,
      tagColor: (row): string => commandStatus[row.commandStatus]?.color || '',
      formatter: val => commandStatus[val.commandStatus]?.value || '' }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const rejectConfig = reactive<IDialogFormConfig>({
  title: '拒绝',
  dialogWidth: '500px',
  labelWidth: '100px',
  submitting: false,
  submit: (val: any) => {
    rejectConfig.submitting = true
    const ids:string[] = []
    TableConfig.selectList?.forEach(item => {
      ids.push(item.id)
    })
    const params = {
      idList: ids,
      rejectRemark: val.rejectRemark || ''
    }
    postCommandRejected(params).then(() => {
      ElMessage.success('拒绝成功')
      TableConfig.selectList = []
      refForm.value?.closeDialog()
      rejectConfig.submitting = false
      refreshData()
    }).catch(error => {
      ElMessage.warning(error)
      rejectConfig.submitting = false
    })
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          label: '拒绝原因',
          field: 'rejectRemark',
          type: 'input',
          rules: [{ required: true, message: '请输入拒绝原因' }]
        }
      ]
    }
  ]
})

function commandReception() {
  const ids:string[] = []
  TableConfig.selectList?.forEach(item => {
    ids.push(item.id)
  })
  const params = {
    idList: ids,
    receiveUserId: refSearch.value?.queryParams && refSearch.value?.queryParams.receiveUserId || ''
  }
  postCommandReception(params).then(() => {
    ElMessage.success('接收成功')
    TableConfig.selectList = []
    refreshData()
  }).catch(error => {
    ElMessage.warning(error)
  })
}

const data = reactive({
  // 用户列表
  UserList: [],
  // 获取用户列表
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  }
})

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    receiveDeptId: Cookies.get('departmentId') || '',
    commandStatus: 'WAITING_RECEIVE'
  }
  getOrderRecordList(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  refreshData()
  data.getUserListValue(Cookies.get('departmentId') || '')
})

</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100% - 70px);
}
.mag_top_10{
  margin-top: 10px;
}
</style>

<style scoped>
.card-table :deep(.el-table__expand-icon--expanded) {
  width: 6px;
}
</style>
