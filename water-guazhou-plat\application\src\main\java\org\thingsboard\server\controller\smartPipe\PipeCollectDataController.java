package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PipeCollectDataRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollectData;
import org.thingsboard.server.dao.smartPipe.PipeCollectDataService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

/**
 * 智慧管网-管网采集
 */
@IStarController
@RequestMapping("api/spp/collect/data")
public class PipeCollectDataController extends BaseController {

    @Autowired
    private PipeCollectDataService pipeCollectDataService;

    @PostMapping
    public IstarResponse save(@RequestBody List<PipeCollectData> pipeCollectDataList) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return IstarResponse.ok(pipeCollectDataService.save(pipeCollectDataList, tenantId, userId));
    }

    @GetMapping("list")
    public IstarResponse getList(PipeCollectDataRequest pipeCollectDataRequest) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        pipeCollectDataRequest.setTenantId(tenantId);
        return IstarResponse.ok(pipeCollectDataService.getList(pipeCollectDataRequest));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) throws ThingsboardException {
        pipeCollectDataService.delete(ids);
        return IstarResponse.ok();
    }

}
