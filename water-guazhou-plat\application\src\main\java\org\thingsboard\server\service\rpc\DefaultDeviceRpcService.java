/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datastax.driver.core.utils.UUIDs;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.util.concurrent.FutureCallback;
import com.google.protobuf.InvalidProtocolBufferException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.async.DeferredResult;
import org.thingsboard.rule.engine.api.RpcError;
import org.thingsboard.rule.engine.api.msg.ToDeviceActorNotificationMsg;
import org.thingsboard.server.actors.service.ActorService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.VO.BaseResult;
import org.thingsboard.server.common.data.audit.ActionType;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.*;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.rpc.RpcRequest;
import org.thingsboard.server.common.data.rpc.ToDeviceRpcRequestBody;
import org.thingsboard.server.common.data.security.DeviceCredentials;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.common.msg.TbMsgDataType;
import org.thingsboard.server.common.msg.TbMsgMetaData;
import org.thingsboard.server.common.msg.cluster.SendToClusterMsg;
import org.thingsboard.server.common.msg.cluster.ServerAddress;
import org.thingsboard.server.common.msg.cluster.ServerType;
import org.thingsboard.server.common.msg.core.ToServerRpcResponseMsg;
import org.thingsboard.server.common.msg.rpc.ToDeviceRpcRequest;
import org.thingsboard.server.common.msg.system.ServiceToRuleEngineMsg;
import org.thingsboard.server.common.transport.RpcToFyService;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.controller.base.HttpValidationCallback;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.audit.AuditLogService;
import org.thingsboard.server.dao.device.DeviceCredentialsService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.CommandLog;
import org.thingsboard.server.dao.optionLog.CommandLogService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.gen.cluster.ClusterAPIProtos;
import org.thingsboard.server.gen.transport.TransportProtos;
import org.thingsboard.server.service.cluster.routing.ClusterRoutingService;
import org.thingsboard.server.service.cluster.rpc.ClusterRpcService;
import org.thingsboard.server.service.security.AccessValidator;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.telemetry.exception.ToErrorResponseEntity;
import org.thingsboard.server.utils.SecretUtil;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.function.Consumer;

import static org.thingsboard.server.controller.base.RpcController.DEFAULT_TIMEOUT;

/**
 * Created by ashvayka on 27.03.18.
 */
@Service
@Slf4j
public class DefaultDeviceRpcService implements DeviceRpcService {

    private static final ObjectMapper json = new ObjectMapper();

    @Autowired
    private ClusterRoutingService routingService;

    @Autowired
    private ClusterRpcService rpcService;

    @Autowired
    private UserService userService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceCredentialsService deviceCredentialsService;

    @Autowired
    private AttributesService attributesService;

    @Autowired
    private AccessValidator accessValidator;

    @Autowired
    private AuditLogService auditLogService;

    @Autowired
    private TimeseriesService timeseriesService;

    @Autowired
    private CommandLogService commandLogService;

    @Autowired
    @Lazy
    private ActorService actorService;

    private ScheduledExecutorService rpcCallBackExecutor;

    private static final Random random = new Random();


    @Autowired
    private RpcToFyService rpcToFyService;

    private final ConcurrentMap<UUID, Consumer<FromDeviceRpcResponse>> localToRuleEngineRpcRequests = new ConcurrentHashMap<>();
    private final ConcurrentMap<UUID, Consumer<FromDeviceRpcResponse>> localToDeviceRpcRequests = new ConcurrentHashMap<>();

    private final ConcurrentMap<Integer, Consumer<TransportProtos.ToDeviceRpcResponseMsg>> rpcRequests = new ConcurrentHashMap<>();

    @PostConstruct
    public void initExecutor() {
        rpcCallBackExecutor = Executors.newSingleThreadScheduledExecutor();
    }

    @PreDestroy
    public void shutdownExecutor() {
        if (rpcCallBackExecutor != null) {
            rpcCallBackExecutor.shutdownNow();
        }
    }

    @Override
    public void processRestAPIRpcRequestToRuleEngine(ToDeviceRpcRequest request, Consumer<FromDeviceRpcResponse> responseConsumer) {
        log.trace("[{}][{}] Processing REST API call to rule engine [{}]", request.getTenantId(), request.getId(), request.getDeviceId());
        UUID requestId = request.getId();
        localToDeviceRpcRequests.put(requestId, responseConsumer);
        sendRpcRequestToRuleEngine(request);
        scheduleTimeout(request, requestId, localToDeviceRpcRequests);
    }

    @Override
    public void processResponseToServerSideRPCRequestFromRuleEngine(ServerAddress requestOriginAddress, FromDeviceRpcResponse response) {
        log.trace("[{}] Received response to server-side RPC request from rule engine: [{}]", response.getId(), requestOriginAddress);
        if (routingService.getCurrentServer().equals(requestOriginAddress)) {
            UUID requestId = response.getId();
            Consumer<FromDeviceRpcResponse> consumer = localToRuleEngineRpcRequests.remove(requestId);
            if (consumer != null) {
                consumer.accept(response);
            } else {
                log.trace("[{}] Unknown or stale rpc response received [{}]", requestId, response);
            }
        } else {
            ClusterAPIProtos.FromDeviceRPCResponseProto.Builder builder = ClusterAPIProtos.FromDeviceRPCResponseProto.newBuilder();
            builder.setRequestIdMSB(response.getId().getMostSignificantBits());
            builder.setRequestIdLSB(response.getId().getLeastSignificantBits());
            response.getResponse().ifPresent(builder::setResponse);
            if (response.getError().isPresent()) {
                builder.setError(response.getError().get().ordinal());
            } else {
                builder.setError(-1);
            }
            rpcService.tell(requestOriginAddress, ClusterAPIProtos.MessageType.CLUSTER_RPC_FROM_DEVICE_RESPONSE_MESSAGE, builder.build().toByteArray());
        }
    }

    @Override
    public void forwardServerSideRPCRequestToDeviceActor(ToDeviceRpcRequest request, Consumer<FromDeviceRpcResponse> responseConsumer) {
        log.trace("[{}][{}] Processing local rpc call to device actor [{}]", request.getTenantId(), request.getId(), request.getDeviceId());
        UUID requestId = request.getId();
        localToDeviceRpcRequests.put(requestId, responseConsumer);
        sendRpcRequestToDevice(request);
        scheduleTimeout(request, requestId, localToDeviceRpcRequests);
    }

    @Override
    public void processResponseToServerSideRPCRequestFromDeviceActor(FromDeviceRpcResponse response) {
        log.trace("[{}] Received response to server-side RPC request from device actor.", response.getId());
        UUID requestId = response.getId();
        Consumer<FromDeviceRpcResponse> consumer = localToDeviceRpcRequests.remove(requestId);
        if (consumer != null) {
            consumer.accept(response);
        } else {
            log.trace("[{}] Unknown or stale rpc response received [{}]", requestId, response);
        }
    }

    @Override
    public void processResponseToServerSideRPCRequestFromDeviceActor(TransportProtos.ToDeviceRpcResponseMsg response) {
        int requestId = response.getRequestId();
        Consumer<TransportProtos.ToDeviceRpcResponseMsg> consumer = rpcRequests.remove(requestId);
        if (consumer != null) {
            consumer.accept(response);
        } else {
            log.trace("[{}] Unknown or stale rpc response received [{}]", requestId, response);
        }
    }

    @Override
    public void processResponseToServerSideRPCRequestFromRemoteServer(ServerAddress serverAddress, byte[] data) {
        ClusterAPIProtos.FromDeviceRPCResponseProto proto;
        try {
            proto = ClusterAPIProtos.FromDeviceRPCResponseProto.parseFrom(data);
        } catch (InvalidProtocolBufferException e) {
            throw new RuntimeException(e);
        }
        RpcError error = proto.getError() > 0 ? RpcError.values()[proto.getError()] : null;
        FromDeviceRpcResponse response = new FromDeviceRpcResponse(new UUID(proto.getRequestIdMSB(), proto.getRequestIdLSB()), proto.getResponse(), error);
        processResponseToServerSideRPCRequestFromRuleEngine(routingService.getCurrentServer(), response);
    }

    @Override
    public void sendReplyToRpcCallFromDevice(TenantId tenantId, DeviceId deviceId, int requestId, String body) {
        ToServerRpcResponseActorMsg rpcMsg = new ToServerRpcResponseActorMsg(tenantId, deviceId, new ToServerRpcResponseMsg(requestId, body));
        forward(deviceId, rpcMsg);
    }


    /**
     * 工控机修改组态，数据源进行下发
     *
     * @param originatorId
     * @param o
     * @return
     */
    @Override
    public boolean sendDataToMqttDevice(String originatorId, Object o, String dataType) {
        //验证是否为工控机设备
        try {
            Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(originatorId)));
            if (device != null && device.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS)) {
                DeviceCredentials deviceCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(device.getTenantId(), device.getId());
                //下发到工控机
                JSONObject data = new JSONObject();
                data.put(DataConstants.REQUEST_PARAM_DATA, o);
                data.put(DataConstants.REQUEST_PARAM_TYPE, dataType);
                rpcToFyService.sendModelToDevice(deviceCredentials.getCredentialsId(), data.toJSONString());
            } else if (device.getGateWayId() != null) {
                DeviceCredentials deviceCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(device.getTenantId(), device.getGateWayId());
                //从机数据源下发到工控机
                JSONObject data = new JSONObject();
                data.put(DataConstants.REQUEST_PARAM_DATA, o);
                data.put(DataConstants.REQUEST_PARAM_TYPE, dataType);
                rpcToFyService.sendModelToDevice(deviceCredentials.getCredentialsId(), data.toJSONString());
            }
            return false;
        } catch (Exception E) {
            return false;
        }

    }

    @Override
    public BaseResult sendRpcToDevice(Device device, String body) {
        DeviceCredentials deviceCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(device.getTenantId(), device.getId());
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONObject data = jsonObject.getJSONObject("Data");
        String key = data.getString(DataConstants.REQUEST_PARAM_NAME);
        String value = data.getString(DataConstants.VALUE);
        try {
            //先进行数据下发操作
            if (rpcToFyService.sendToDevice(deviceCredentials.getCredentialsId(), device.getHardwareId(), body)) {
                long start = System.currentTimeMillis();
                //等待10s，如果获取到数据值为修改后的值，则返回成功
                while (System.currentTimeMillis() - start < 10 * 1000) {
                    TsKvEntry tsKvEntry = timeseriesService.findLatest(device.getId(), key);
                    if (new BigDecimal(tsKvEntry.getValueAsString()).compareTo(new BigDecimal(value)) == 0) {
                        break;
                    } else {
                        Thread.sleep(1000);
                        rpcToFyService.pushDataNow(deviceCredentials.getCredentialsId(), device.getHardwareId(), body);
                    }
                }
                if (System.currentTimeMillis() - start < 10 * 1000) {
                    return BaseResult.builder().isResult(true).msg("下发指令成功！").build();
                } else {
                    return BaseResult.builder().isResult(false).msg("下发指令超时！").build();
                }
            } else {
                return BaseResult.builder().isResult(false).msg("下发指令失败！").build();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResult.builder().isResult(false).msg("下发指令失败！").build();
        }

    }


    @Override
    public DeferredResult<ResponseEntity> sendRPCToModbus(DeviceId deviceId, String requestBody, DeviceId sendId, SecurityUser userSecurity) throws ThingsboardException {
        try {
            Integer requestId = random.nextInt();
            JSONObject rpcRequestBody = JSON.parseObject(requestBody);
            SecurityUser currentUser = new SecurityUser();
            // 判断token,若是正确的密钥获取其userId
            if (userSecurity != null) {
                currentUser = userSecurity;
            } else if (requestBody.contains("secret")) {
                String userIdStr = SecretUtil.getUserIdBySecret(rpcRequestBody.getString("secret"));
                User user = userService.findUserById(new UserId(UUIDConverter.fromString(userIdStr)));
                if (user == null) {
                    throw new ThingsboardException("secret is not exist", ThingsboardErrorCode.AUTHENTICATION);
                }
                BeanUtils.copyProperties(user, currentUser);
                currentUser.setEnabled(true);
            } else {
                throw new ThingsboardException(ThingsboardErrorCode.AUTHENTICATION);
            }
            String token = null;
            if (!deviceId.getId().toString().equals(sendId.getId().toString())) {
                JSONObject object = JSON.parseObject(rpcRequestBody.get("params").toString());
                String tag = object.get("tag").toString();

                Device device = deviceService.findDeviceById(deviceId);
                // 获取token
                token = deviceCredentialsService.findDeviceCredentialsByDeviceId(currentUser.getTenantId(), sendId).getCredentialsId();
                AttributeKvEntry attributeKvEntry = attributesService.findNotFuture(deviceId, DataConstants.SHARED_SCOPE, "info");
                AttributeKvEntry prop = attributesService.findNotFuture(deviceId, DataConstants.SHARED_SCOPE, "prop");
                if (attributeKvEntry == null || prop == null) {
                    throw new ThingsboardException("Invalid request body", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
                //添加com口信息
                JSONObject jsonObject = JSON.parseObject(attributeKvEntry.getValueAsString());
                if (jsonObject.get("modbusType").toString().equals("rtu")) {
                    object.put("serverName", jsonObject.get("portName"));
                } else {
                    object.put("serverName", jsonObject.get("port"));
                }

                //添加地址信息
                JSONArray array = JSON.parseArray(prop.getValueAsString());
                for (int i = 0; i < array.size(); i++) {
                    if (array.getJSONObject(i).get("propertyCategory").toString().equals(tag)) {
                        object.put("address", array.getJSONObject(i).get("registerAddress"));
                        break;
                    }
                }
                //添加设备名称
                object.put("deviceName", device.getName());
                // 添加设备token信息
                object.put("token", token);
                rpcRequestBody.put("params", object);
            }
            rpcRequestBody.getJSONObject("params").put("requestId", requestId);
            RpcRequest cmd = new RpcRequest(rpcRequestBody.getString("method"),
                    new ObjectMapper().writeValueAsString(rpcRequestBody.get("params")));

            if (rpcRequestBody.containsKey("timeout")) {
                cmd.setTimeout(rpcRequestBody.getLong("timeout"));
            }
            final DeferredResult<ResponseEntity> response = new DeferredResult<>();
            long now = System.currentTimeMillis();
            long timeout = now + (cmd.getTimeout() != null ? cmd.getTimeout() : DEFAULT_TIMEOUT);
            ToDeviceRpcRequestBody body = new ToDeviceRpcRequestBody(cmd.getMethodName(), cmd.getRequestData());
            SecurityUser finalCurrentUser = currentUser;
            String finalToken = token;
            accessValidator.validate(currentUser, sendId, new HttpValidationCallback(response, new FutureCallback<DeferredResult<ResponseEntity>>() {
                @Override
                public void onSuccess(@Nullable DeferredResult<ResponseEntity> result) {
                    ToDeviceRpcRequest rpcRequest = new ToDeviceRpcRequest(UUID.randomUUID(),
                            finalCurrentUser.getTenantId(),
                            sendId,
                            false,
                            timeout,
                            body
                    );
                    rpcToFyService.sendToModbus(finalToken, body, requestId);
                    rpcRequests.put(requestId, ToDeviceRpcResponseMsg -> replyCopy(new LocalRequestMetaData(rpcRequest, finalCurrentUser, result), ToDeviceRpcResponseMsg));
                }

                @Override
                public void onFailure(Throwable e) {
                    ResponseEntity entity;
                    if (e instanceof ToErrorResponseEntity) {
                        entity = ((ToErrorResponseEntity) e).toErrorResponseEntity();
                    } else {
                        entity = new ResponseEntity(HttpStatus.UNAUTHORIZED);
                    }
                    logRpcCall(finalCurrentUser, sendId, body, false, Optional.empty(), e);
                    response.setResult(entity);
                }
            }));
            return response;
        } catch (IOException ioe) {
            throw new ThingsboardException("Invalid request body", ioe, ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException("Invalid request body", e, ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
    }


    @Override
    public DeferredResult<ResponseEntity> handleDeviceRPCRequest(boolean oneWay, DeviceId deviceId, String requestBody, DeviceId sendId, SecurityUser userSecurity) throws ThingsboardException {
        try {
            // 记录操作日志
            CommandLog commandLog = new CommandLog();
            commandLog.setDeviceId(UUIDConverter.fromTimeUUID(deviceId.getId()));
            commandLog.setParam(requestBody);
            commandLog.setTime(new Date());


            UUID requestId = UUID.randomUUID();
            JSONObject rpcRequestBody = JSON.parseObject(requestBody);
            SecurityUser currentUser = new SecurityUser();
            // 判断token,若是正确的密钥获取其userId
            if (userSecurity != null) {
                currentUser = userSecurity;
            } else if (requestBody.contains("secret")) {
                String userIdStr = SecretUtil.getUserIdBySecret(rpcRequestBody.getString("secret"));
                User user = userService.findUserById(new UserId(UUIDConverter.fromString(userIdStr)));
                if (user == null) {
                    throw new ThingsboardException("secret is not exist", ThingsboardErrorCode.AUTHENTICATION);
                }
                BeanUtils.copyProperties(user, currentUser);
                currentUser.setEnabled(true);
            } else {
                throw new ThingsboardException(ThingsboardErrorCode.AUTHENTICATION);
            }
            commandLog.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
            commandLog.setOptionUserId(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
            if (!deviceId.getId().toString().equals(sendId.getId().toString())) {
                JSONObject object = JSON.parseObject(rpcRequestBody.get("params").toString());
                String tag = object.get("tag").toString();
                String value = object.get("value").toString();
                commandLog.setAttr(tag);
                commandLog.setValue(value);

                Device device = deviceService.findDeviceById(deviceId);
                // 获取token
                String token = deviceCredentialsService.findDeviceCredentialsByDeviceId(currentUser.getTenantId(), sendId).getCredentialsId();
                AttributeKvEntry attributeKvEntry = attributesService.findNotFuture(deviceId, DataConstants.SHARED_SCOPE, "info");
                AttributeKvEntry prop = attributesService.findNotFuture(deviceId, DataConstants.SHARED_SCOPE, "prop");
                if (attributeKvEntry == null || prop == null) {
                    throw new ThingsboardException("Invalid request body", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
                //添加com口信息
                JSONObject jsonObject = JSON.parseObject(attributeKvEntry.getValueAsString());
                if (jsonObject.get("modbusType").toString().equals("rtu")) {
                    object.put("serverName", jsonObject.get("portName"));
                } else {
                    object.put("serverName", jsonObject.getString("host") + ":" + jsonObject.getString("port"));
                }

                //添加地址信息
                JSONArray array = JSON.parseArray(prop.getValueAsString());
                for (int i = 0; i < array.size(); i++) {
                    if (array.getJSONObject(i).get("propertyCategory").toString().equals(tag)) {
                        object.put("address", array.getJSONObject(i).get("registerAddress"));
                        break;
                    }
                }
                //添加设备名称
                object.put("deviceName", device.getName());
                // 添加设备token信息
                object.put("token", token);
                rpcRequestBody.put("params", object);
            }
            rpcRequestBody.getJSONObject("params").put("requestId", requestId);
            RpcRequest cmd = new RpcRequest(rpcRequestBody.getString("method"),
                    new ObjectMapper().writeValueAsString(rpcRequestBody.get("params")));

            if (rpcRequestBody.containsKey("timeout")) {
                cmd.setTimeout(rpcRequestBody.getLong("timeout"));
            }
            final DeferredResult<ResponseEntity> response = new DeferredResult<>();
            long now = System.currentTimeMillis();
            long timeout = now + (cmd.getTimeout() != null ? cmd.getTimeout() : DEFAULT_TIMEOUT);
            ToDeviceRpcRequestBody body = new ToDeviceRpcRequestBody(cmd.getMethodName(), cmd.getRequestData());
            SecurityUser finalCurrentUser = currentUser;
            accessValidator.validate(currentUser, sendId, new HttpValidationCallback(response, new FutureCallback<DeferredResult<ResponseEntity>>() {
                @Override
                public void onSuccess(@Nullable DeferredResult<ResponseEntity> result) {
                    ToDeviceRpcRequest rpcRequest = new ToDeviceRpcRequest(requestId,
                            finalCurrentUser.getTenantId(),
                            sendId,
                            oneWay,
                            timeout,
                            body
                    );
                    processRestAPIRpcRequestToRuleEngine(rpcRequest, fromDeviceRpcResponse -> reply(new LocalRequestMetaData(rpcRequest, finalCurrentUser, result), fromDeviceRpcResponse, commandLog));
                }

                @Override
                public void onFailure(Throwable e) {
                    ResponseEntity entity;
                    if (e instanceof ToErrorResponseEntity) {
                        entity = ((ToErrorResponseEntity) e).toErrorResponseEntity();
                    } else {
                        entity = new ResponseEntity(HttpStatus.UNAUTHORIZED);
                    }
                    logRpcCall(finalCurrentUser, sendId, body, oneWay, Optional.empty(), e);
                    response.setResult(entity);
                    commandLog.setResult(entity.getStatusCode().toString());
                }
            }));

            return response;
        } catch (IOException ioe) {
            throw new ThingsboardException("Invalid request body", ioe, ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException("Invalid request body", e, ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
    }


    public void reply(LocalRequestMetaData rpcRequest, FromDeviceRpcResponse response, CommandLog commandLog) {
        Optional<RpcError> rpcError = response.getError();
        DeferredResult<ResponseEntity> responseWriter = rpcRequest.getResponseWriter();
        if (rpcError.isPresent()) {
            logRpcCall(rpcRequest, rpcError, null);
            RpcError error = rpcError.get();
            switch (error) {
                case TIMEOUT:
                    responseWriter.setResult(new ResponseEntity<>(HttpStatus.REQUEST_TIMEOUT));
                    commandLog.setResult("timeout");
                    break;
                case NO_ACTIVE_CONNECTION:
                    responseWriter.setResult(new ResponseEntity<>(HttpStatus.CONFLICT));
                    commandLog.setResult("conflict");
                    break;
                default:
                    responseWriter.setResult(new ResponseEntity<>(HttpStatus.REQUEST_TIMEOUT));
                    commandLog.setResult("timeout");
                    break;
            }
        } else {
            Optional<String> responseData = response.getResponse();
            if (responseData.isPresent() && !StringUtils.isEmpty(responseData.get())) {
                String data = responseData.get();
                try {
                    logRpcCall(rpcRequest, rpcError, null);
                    responseWriter.setResult(new ResponseEntity<>(new ObjectMapper().readTree(data), HttpStatus.OK));
                    commandLog.setResult(data);
                } catch (IOException e) {
                    log.debug("Failed to decode device response: {}", data, e);
                    logRpcCall(rpcRequest, rpcError, e);
                    responseWriter.setResult(new ResponseEntity<>(HttpStatus.NOT_ACCEPTABLE));
                }
            } else {
                logRpcCall(rpcRequest, rpcError, null);
                responseWriter.setResult(new ResponseEntity<>(HttpStatus.OK));
                commandLog.setResult("success");
            }
        }

        // 保存日志
        commandLogService.save(commandLog);
    }

    public void replyCopy(LocalRequestMetaData rpcRequest, TransportProtos.ToDeviceRpcResponseMsg response) {

        DeferredResult<ResponseEntity> responseWriter = rpcRequest.getResponseWriter();

        String responseData = response.getPayload();
        if (responseData != null) {
            String data = responseData;
            try {
                logRpcCall(rpcRequest, null, null);
                responseWriter.setResult(new ResponseEntity<>(new ObjectMapper().readTree(data), HttpStatus.OK));
            } catch (IOException e) {
                log.debug("Failed to decode device response: {}", data, e);
                logRpcCall(rpcRequest, null, e);
                responseWriter.setResult(new ResponseEntity<>(HttpStatus.NOT_ACCEPTABLE));
            }
        } else {
            logRpcCall(rpcRequest, null, null);
            responseWriter.setResult(new ResponseEntity<>(HttpStatus.OK));
        }
    }

    private void logRpcCall(LocalRequestMetaData rpcRequest, Optional<RpcError> rpcError, Throwable e) {
        logRpcCall(rpcRequest.getUser(), rpcRequest.getRequest().getDeviceId(), rpcRequest.getRequest().getBody(), rpcRequest.getRequest().isOneway(), rpcError, null);
    }


    private void logRpcCall(SecurityUser user, EntityId entityId, ToDeviceRpcRequestBody body, boolean oneWay, Optional<RpcError> rpcError, Throwable e) {
        String rpcErrorStr = "";
        if (rpcError != null && rpcError.isPresent()) {
            rpcErrorStr = "RPC Error: " + rpcError.get().name();
        }
        String method = body.getMethod();
        String params = body.getParams();

        auditLogService.logEntityAction(
                user.getTenantId(),
                user.getCustomerId(),
                user.getId(),
                user.getName(),
                (UUIDBased & EntityId) entityId,
                null,
                ActionType.RPC_CALL,
                BaseController.toException(e),
                rpcErrorStr,
                oneWay,
                method,
                params);
    }

    private void sendRpcRequestToRuleEngine(ToDeviceRpcRequest msg) {
        // 处理不同方式的请求
        switch (msg.getBody().getMethod()) {
            case DataConstants.RPC_TO_MQTT:
                sendMsgToMqtt(msg);
                break;
            case DataConstants.RPC_TO_MODBUS:
                sendMsgToModbus(msg);
                break;
            case DataConstants.RPC_TO_MODBUS_TEMPLATE:
                sendMsgToModbus(msg);
                break;
            case DataConstants.RPC_TO_DTU:
                // TODO 发送rpc命令到Dtu
                break;
            default:
                throw new RuntimeException("错误的method类型");
        }
    }

    private void sendMsgToModbus(ToDeviceRpcRequest msg) {
        actorService.onMsg(new SendToClusterMsg(msg.getDeviceId(), new ToDeviceRpcRequestActorMsg(new ServerAddress("**************", 9002, ServerType.CORE), msg)));
        // 记录本次rpc调用
        DataConstants.RPC_RESULT_MAP.put(msg.getId().toString(), Optional.empty());
    }

    private void sendMsgToMqtt(ToDeviceRpcRequest msg) {
        ObjectNode entityNode = json.createObjectNode();
        TbMsgMetaData metaData = new TbMsgMetaData();
        metaData.putValue("requestUUID", msg.getId().toString());
        metaData.putValue("originHost", routingService.getCurrentServer().getHost());
        metaData.putValue("originPort", Integer.toString(routingService.getCurrentServer().getPort()));
        metaData.putValue("expirationTime", Long.toString(msg.getExpirationTime()));
        metaData.putValue("oneway", Boolean.toString(msg.isOneway()));

        entityNode.put("method", msg.getBody().getMethod());
        entityNode.put("params", msg.getBody().getParams());

        try {
            TbMsg tbMsg = new TbMsg(UUIDs.timeBased(), DataConstants.RPC_CALL_FROM_SERVER_TO_DEVICE, msg.getDeviceId(), metaData, TbMsgDataType.JSON
                    , json.writeValueAsString(entityNode)
                    , null, null, 0L);
            actorService.onMsg(new SendToClusterMsg(msg.getDeviceId(), new ServiceToRuleEngineMsg(msg.getTenantId(), tbMsg)));
//            actorService.onMsg(new SendToClusterMsg(msg.getDeviceId(),
//                    new ToDeviceRpcRequestActorMsg(new ServerAddress("**************", 9002, ServerType.CORE), msg)));
            // 记录本次rpc调用
            DataConstants.RPC_RESULT_MAP.put(msg.getId().toString(), Optional.empty());
            log.info("发出：" + System.currentTimeMillis());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private void sendRpcRequestToDevice(ToDeviceRpcRequest msg) {
        ToDeviceRpcRequestActorMsg rpcMsg = new ToDeviceRpcRequestActorMsg(routingService.getCurrentServer(), msg);
        log.trace("[{}] Forwarding msg {} to device actor!", msg.getDeviceId(), msg);
        forward(msg.getDeviceId(), rpcMsg);
    }

    private <T extends ToDeviceActorNotificationMsg> void forward(DeviceId deviceId, T msg) {
        actorService.onMsg(new SendToClusterMsg(deviceId, msg));
    }

    private void scheduleTimeout(ToDeviceRpcRequest request, UUID requestId, ConcurrentMap<UUID, Consumer<FromDeviceRpcResponse>> requestsMap) {
        long timeout = Math.max(0, request.getExpirationTime() - System.currentTimeMillis());
        log.trace("[{}] processing the request: [{}]", this.hashCode(), requestId);
        rpcCallBackExecutor.schedule(() -> {
            log.trace("[{}] timeout the request: [{}]", this.hashCode(), requestId);
            Consumer<FromDeviceRpcResponse> consumer = requestsMap.remove(requestId);
            if (consumer != null) {
                consumer.accept(new FromDeviceRpcResponse(requestId, null, RpcError.TIMEOUT));
            }
        }, timeout, TimeUnit.MILLISECONDS);
    }


}
