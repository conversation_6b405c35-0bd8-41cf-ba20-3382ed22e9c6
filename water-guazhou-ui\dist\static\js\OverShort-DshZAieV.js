import{d as x,c as u,r as h,b as v,Q as F,g as C,h as H,F as S,q as M,i as b,_ as z,X as D}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{a as G}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as P,a as B}from"./LayerHelper-Cn-iiqxI.js";import{g as N}from"./QueryHelper-ILO3qZqg.js";import{e as T}from"./ViewHelper-BGCZjxXH.js";import U from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Qe=x({__name:"OverShort",setup(q){const l=u(),d=u(),r=h({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1}),t={},f=h({group:[{fieldset:{desc:"超短线标准"},fields:[{type:"input-number",prepend:"管长≤",append:"m",placeholder:"请输入标准值",field:"length"}]},{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,type:"default",size:"large",title:"绘制多边形",disabled:()=>r.loading,iconifyIcon:"mdi:shape-shape-polygon-plus",click:()=>n("polygon")},{perm:!0,type:"default",size:"large",title:"绘制矩形",disabled:()=>r.loading,iconifyIcon:"ep:crop",click:()=>n("rectangle")},{perm:!0,type:"default",size:"large",title:"绘制椭圆",disabled:()=>r.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>n("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>r.loading,iconifyIcon:"ep:delete",click:()=>y()}]}]},{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,label:"选择图层",showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",btns:[{perm:!0,text:()=>r.loading?"正在检查，过程稍长，请耐心等待！":"检查",styles:{width:"100%"},loading:()=>r.loading,click:()=>w()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),n=o=>{var e;t.view&&(y(),(e=t.sketch)==null||e.create(o))},y=()=>{var o;(o=t.graphicsLayer)==null||o.removeAll(),t.graphics=void 0},k=async()=>{var a,m,s;r.layerIds=B(t.view);const o=await D(r.layerIds);r.layerInfos=((m=(a=o.data)==null?void 0:a.result)==null?void 0:m.rows)||[];const e=(s=f.group.find(p=>p.id==="layer"))==null?void 0:s.fields[0],i=r.layerInfos.filter(p=>p.geometrytype==="esriGeometryPolyline").map(p=>({label:p.layername,value:p.layerid,data:p}));e&&(e.options=[{label:"管线类",value:-2,disabled:!0,children:i}]),i.length===1&&l.value&&(l.value.dataForm.layerid=i.map(p=>p.value))},w=async()=>{var o,e,i,a,m,s,p,g;v.info("正在检查，请稍候...");try{r.loading=!0,r.tabs.length=0;const c=(e=(o=l.value)==null?void 0:o.dataForm.layerid)==null?void 0:e.filter(_=>_>=0),L=await N(c,r.layerInfos,{where:" PIPELENGTH<="+(((i=l.value)==null?void 0:i.dataForm.length)||0)+" ",geometry:(a=t.graphics)==null?void 0:a.geometry});await((m=d.value)==null?void 0:m.refreshDetail(L)),T(t.view,((p=(s=t.graphics)==null?void 0:s.geometry)==null?void 0:p.extent)||((g=t.view)==null?void 0:g.extent),!0)}catch(c){console.log(c),r.loading=!1,v.error("检查失败")}},I=o=>{t.view=o,t.graphicsLayer=P(t.view,{id:"pipe-overshort",title:"超短线检查"});const{sketch:e,updateHandler:i,createHandler:a}=G(t.view,t.graphicsLayer,m=>{t.graphics=m});t.sketch=e,t.sketchCreateHandler=a,t.sketchUpdateHandler=i,k()};return F(()=>{var o,e,i,a,m;(o=t.sketchCreateHandler)==null||o.remove(),(e=t.sketchUpdateHandler)==null||e.remove(),(i=t.graphicsLayer)==null||i.removeAll(),(a=t.graphicsLayer)==null||a.destroy(),(m=t.sketch)==null||m.destroy()}),(o,e)=>{const i=z;return C(),H(U,{ref_key:"refMap",ref:d,title:"超短线检查",onMapLoaded:I,onDetailRefreshed:e[0]||(e[0]=a=>b(r).loading=!1)},{default:S(()=>[M(i,{ref_key:"refForm",ref:l,config:b(f)},null,8,["config"])]),_:1},512)}}});export{Qe as default};
