{"name": "istart-frame", "version": "1.0.0", "private": true, "scripts": {"dev:comment": "启动本地调试", "dev": "vite", "dev:f:comment": "启动本地调试并强制重新加载依赖", "dev:f": "vite --force", "build:comment": "启动打包程序", "build": "vite build", "mac-build": " node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode production &&vue-tsc --noEmit", "build:mf:comment": "启动打包程序", "build:mf": "yarn mf & yarn build", "build-t:comment": "启动打包程序", "build-t": "yarn mf & yarn type-check & yarn build", "preview:comment": "预览打包结果", "preview": "vite preview", "type-check:comment": "类型检查", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:comment": "类型校验", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix --ignore-path .eslintignore", "lint:fix:comment": "类型校验并进行自动修正", "lint:fix": "eslint src --fix --ext .ts,.tsx,.vue", "lint:fixcache:comment": "类型校验并进行自动修正（只针对暂存区的文件）", "lint:fixcache": "eslint src --fix --cache --ext .ts,.tsx,.vue", "lint:eslint:comment": "类型校验并进行自动修正", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier:comment": "类型校验并进行自动修正（使用prettier）", "lint:prettier": "prettier --write  src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}", "lint:lint-staged:comment": "类型校验自动化命令", "lint:lint-staged": "npx lint-staged --allow-empty", "prepare:comment": "husky预执行命令", "prepare": "husky", "czinit": "npm i -g commitizen", "mf:comment": "最大缓存内存扩展", "mf": "cross-env LIMIT=10240 increase-memory-limit", "docs:dev:comment": "启动文档本地服务", "docs:dev": "vitepress dev docs", "docs:build:comment": "启动文档打包服务", "docs:build": "vitepress build docs", "docs:serve:comment": "启动文档预览服务", "docs:serve": "vitepress serve docs --port 8080"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"src/**/*.{ts,tsx,vue}": ["eslint --fix"], "package.json": ["prettier --write"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/l7": "^2.22.5", "@antv/l7-maps": "^2.22.5", "@arcgis/core": "4.26.5", "@ckpack/vue-color": "^1.2.4", "@codemirror/lang-javascript": "^6.1.8", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-sql": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "^2.3.1", "@iconify-json/ep": "^1.1.6", "@iconify-json/gis": "^1.1.2", "@iconify-json/ic": "^1.1.8", "@iconify-json/material-symbols": "^1.1.12", "@iconify-json/mdi": "^1.1.30", "@iconify-json/pajamas": "^1.1.1", "@iconify-json/quill": "^1.1.1", "@iconify/vue": "^4.1.1", "@orillusion/core": "^0.6.5", "@orillusion/stats": "^0.2.0", "@popperjs/core": "^2.11.7", "@three-ts/orbit-controls": "^1.4.7", "@turf/turf": "^6.5.0", "@types/dat.gui": "^0.7.10", "@vueuse/core": "^10.6.1", "@wangeditor/editor": "^5.1.14", "@wangeditor/editor-for-vue": "^5.1.12", "51superapi": "^6.5.0", "axios": "^0.18.0", "bignumber.js": "^9.1.2", "cesium": "^1.107.2", "codemirror": "^6.0.1", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "d3": "^7.8.5", "dat.gui": "^0.7.9", "dayjs": "^1.11.13", "dplayer": "^1.26.0", "echarts": "^5.1.2", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.0.0", "element-plus": "^2.3.5", "element-resize-detector": "^1.2.4", "exceljs": "^4.2.1", "flv.js": "^1.6.2", "gcoord": "^1.0.5", "gl-matrix": "^3.4.3", "gsap": "^3.12.2", "hls.js": "^1.0.11", "html2canvas": "^1.4.1", "increase-memory-limit": "^1.0.7", "jquery": "^3.6.0", "js-cookie": "^3.0.0", "js-file-download": "^0.4.12", "jspdf": "^2.5.1", "leaflet": "^1.7.1", "leaflet.chinatmsproviders": "^3.0.4", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "moment": "^2.29.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "nzh": "^1.0.11", "pdfjs-dist": "2.6.347", "pinia": "^2.0.22", "print-js": "^1.6.0", "proj4": "^2.9.1", "qrcode.vue": "^3.3.3", "qweather-icons": "^1.3.1", "three": "^0.154.0", "uuid": "^8.3.2", "video.js": "^8.21.0", "vue": "^3.3.4", "vue-clipboard2": "^0.3.1", "vue-codemirror": "^6.1.1", "vue-draggable-plus": "^0.5.4", "vue-echarts": "^6.0.0-rc.6", "vue-router": "^4.2.1", "vue-seamless-scroll": "^1.1.23", "vue-spinner": "^1.0.4", "vue3-print-nb": "^0.1.4", "vue3-treeselect": "^0.1.10", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.20", "xgplayer-flv": "^3.0.20", "xgplayer-hls": "^3.0.20", "xlsx": "^0.17.0"}, "devDependencies": {"@types/codemirror": "^5.60.7", "@types/csvtojson": "^2.0.0", "@types/d3": "^7.4.0", "@types/echarts": "^4.9.9", "@types/element-resize-detector": "^1.1.3", "@types/gl-matrix": "^3.2.0", "@types/jquery": "^3.5.6", "@types/js-cookie": "^2.2.7", "@types/jsdom": "^20.0.0", "@types/leaflet": "^1.7.4", "@types/moment": "^2.13.0", "@types/node": "^16.11.56", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.4.1", "@types/three": "^0.153.0", "@types/video.js": "^7.3.26", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "cz-git": "^1.6.1", "eslint": "^9.9.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.2", "eslint-plugin-vue": "^9.28.0", "husky": "^9.1.5", "jsdom": "^25.0.0", "lint-staged": "^15.2.10", "postcss-px-to-viewport": "github:evrone/postcss-px-to-viewport", "prettier": "^3.3.3", "sass": "^1.78.0", "sharp": "^0.33.5", "svgo": "^3.3.2", "typescript": "^5.5.4", "unplugin-auto-import": "^0.18.2", "unplugin-icons": "^0.19.2", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.2", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-resolve-externals": "^0.2.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.4.0", "vitepress": "^1.3.4", "vue-tsc": "^2.1.4"}}