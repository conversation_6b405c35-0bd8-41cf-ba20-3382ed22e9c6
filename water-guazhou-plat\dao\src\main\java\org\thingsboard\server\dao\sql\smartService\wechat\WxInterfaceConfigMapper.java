package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxInterfaceConfig;

@Mapper
public interface WxInterfaceConfigMapper extends BaseMapper<WxInterfaceConfig> {
    WxInterfaceConfig findByTenantId(String tenantId);

    String getIdByTenantId(String tenantId);

}
