/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.virtual;

import org.thingsboard.server.common.data.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class VirtualUtils {

    public static final String MATH_SYMBOL = "+-*/()><||&&=!=";


    /**
     * 将公式中字符和数学符号分离开
     *
     * @param value
     * @return
     */
    public static List<String> handleVirtualFormula(String value) {
        List<String> list = new ArrayList<>();
        if (!StringUtils.checkNotNull(value)) {
            return list;
        }
        int lastFlag = 0;
        for (int i = 0; i < value.length(); i++) {
            if (MATH_SYMBOL.contains(value.substring(i, i + 1))) {
                list.add(value.substring(lastFlag, i));
                list.add(value.substring(i, i + 1));
                lastFlag = i + 1;
            }
        }
        list.add(value.substring(lastFlag));
        return list;
    }


    public static boolean isMathSymbol(String v) {
        Pattern pattern = Pattern.compile("^(\\-|\\+)?\\d+(\\.\\d+)?$");
        Matcher matcher = pattern.matcher(v);
        return (MATH_SYMBOL.contains(v) || matcher.matches());
    }


    public static void main(String[] args) {
        String s = "*10^de12.energy";
        handleVirtualFormula(s).forEach(a -> {
            //System.out.println(a);
        });
        System.out.println(isMathSymbol("12"));

    }
}
