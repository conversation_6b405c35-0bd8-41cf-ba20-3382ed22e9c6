package org.thingsboard.server.controller.knowledge;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice;
import org.thingsboard.server.dao.smartService.knowledge.KnowledgeNoticeService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping({"api/ss/knowledge/notice"})
public class KnowledgeNoticeController extends BaseController {
    @Autowired
    private KnowledgeNoticeService knowledgeNoticeService;

    public KnowledgeNoticeController() {
    }

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String type, @RequestParam(required = false) Long startTime, @RequestParam(required = false) Long endTime, int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        return IstarResponse.ok(this.knowledgeNoticeService.getList(type, startTime, endTime, page, size, tenantId));
    }

    @GetMapping({"all"})
    public IstarResponse getAll() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        return IstarResponse.ok(this.knowledgeNoticeService.getAll(tenantId));
    }

    @GetMapping({"{id}"})
    public IstarResponse getById(@PathVariable String id) throws ThingsboardException {
        return IstarResponse.ok(this.knowledgeNoticeService.getById(id));
    }

    @PostMapping
    public IstarResponse save(@RequestBody KnowledgeNotice knowledgeNotice) throws ThingsboardException {
        knowledgeNotice.setTenantId(UUIDConverter.fromTimeUUID(this.getTenantId().getId()));
        knowledgeNotice.setCreator(UUIDConverter.fromTimeUUID(((UserId) this.getCurrentUser().getId()).getId()));
        return IstarResponse.ok(this.knowledgeNoticeService.save(knowledgeNotice));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        int i = this.knowledgeNoticeService.delete(ids);
        return i > 0 ? IstarResponse.ok("删除成功") : IstarResponse.error("删除失败");
    }
}
