package org.thingsboard.server.dao.model.sql.revenue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄核数据副本-抄表数据
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.COPY_DATA_READ_METER_DATA_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class CopyDataReadMeterData {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_CODE)
    private String code;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_YM)
    private String ym;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_USER_CODE)
    private String userCode;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_ORG_ID)
    private String orgId;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_METER_ID)
    private String meterId;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_POINT_ID)
    private String pointId;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_METER_COPY_NUMBER)
    private Integer meterCopyNumber;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_LAST_READ_NUM)
    private BigDecimal lastReadNum;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_LAST_READ_WATER)
    private BigDecimal lastReadWater;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_THIS_READ_NUM)
    private BigDecimal thisReadNum;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_THIS_READ_WATER)
    private BigDecimal thisReadWater;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_APPEND_WATER)
    private BigDecimal appendWater;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_TOTAL_WATER)
    private BigDecimal totalWater;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_READ_STATUS)
    private String readStatus;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_EXCEPTION_TYPE)
    private String exceptionType;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_LAST_READ_DATE)
    private Date lastReadDate;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_THIS_READ_DATE)
    private Date thisReadDate;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_RECORD_TYPE)
    private String recordType;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_METER_ADDRESS)
    private String meterAddress;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_REMARK)
    private String remark;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_EXECUTE_USER)
    private String executeUser;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_UPDATE_USER)
    private String updateUser;

    @Column(name = ModelConstants.READ_METER_DATA_IMGS)
    private String imgs;

    @Column(name = ModelConstants.READ_METER_DATA_TYPE)
    private String type;

    @Column(name = ModelConstants.READ_METER_DATA_DATA_TIME)
    private Date dataTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
