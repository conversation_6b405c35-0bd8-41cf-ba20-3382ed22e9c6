package org.thingsboard.server.controller.contract;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.purchase.Contract;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractSaveRequest;
import org.thingsboard.server.dao.purchase.ContractService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/contract")
public class ContractController extends BaseController {
    @Autowired
    private ContractService service;


    @GetMapping
    public IPage<Contract> findAllConditional(ContractPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public Contract save(@RequestBody ContractSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody ContractSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}