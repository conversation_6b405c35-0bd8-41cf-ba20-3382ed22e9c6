package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApply;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoConstructionApplySaveRequest extends SaveRequest<SoConstructionApply> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 所属合同编号
    @NotNullOrEmpty
    private String contractCode;

    // 工期开始时间
    @NotNullOrEmpty
    private Date beginTime;

    // 工期结束时间
    @NotNullOrEmpty
    private Date endTime;

    // 工程负责人
    @NotNullOrEmpty
    private String principal;

    // 联系电话
    private String phone;

    // 施工班组
    private String constructClass;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        if (getBeginTime().after(getEndTime())) {
            return "开始时间不能晚于结束时间";
        }

        return super.valid(request);
    }

    @Override
    protected SoConstructionApply build() {
        SoConstructionApply entity = new SoConstructionApply();
        entity.setCode(code);
        entity.setStatus(SoGeneralTaskStatus.PROCESSING);
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionApply update(String id) {
        SoConstructionApply entity = new SoConstructionApply();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionApply entity) {
        entity.setContractCode(contractCode);
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setPrincipal(principal);
        entity.setPhone(phone);
        entity.setConstructClass(constructClass);
        entity.setRemark(remark);
    }
}