package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.AssayBaseSettingListRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayBaseSetting;

@Mapper
public interface AssayBaseSettingMapper extends BaseMapper<AssayBaseSetting> {

    IPage<AssayBaseSetting> findList(IPage<AssayBaseSetting> pageRequest, @Param("param") AssayBaseSettingListRequest request);

}
