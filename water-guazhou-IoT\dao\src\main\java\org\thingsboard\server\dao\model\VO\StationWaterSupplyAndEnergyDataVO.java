package org.thingsboard.server.dao.model.VO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 站点供水量、耗电量数据
 */
@Data
public class StationWaterSupplyAndEnergyDataVO {

    private String stationId;

    private String name;

    // 本期时间
    private String time;

    // 下期时间
    private String lastTime;

    // 本期供水量
    private BigDecimal totalFlow;

    // 本期耗电量
    private BigDecimal energy;

    // 上期供水量
    private BigDecimal lastTimeTotalFlow;

    // 上期耗电量
    private BigDecimal lastTimeEnergy;

    // 本期吨水电耗
    private BigDecimal unitConsumption;

    // 上期吨水电耗
    private BigDecimal lastTimeUnitConsumption;

    // 吨水电耗差值
    private BigDecimal differenceValue;

    // 变化率
    private BigDecimal changeRate;

}
