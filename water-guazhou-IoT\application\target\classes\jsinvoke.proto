/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto3";
package js;

option java_package = "org.thingsboard.server.gen.js";
option java_outer_classname = "JsInvokeProtos";

enum JsInvokeErrorCode {
  COMPILATION_ERROR = 0;
  RUNTIME_ERROR = 1;
  TIMEOUT_ERROR = 2;
}

message RemoteJsRequest {
  string responseTopic = 1;
  int64 requestIdMSB = 2;
  int64 requestIdLSB = 3;
  JsCompileRequest compileRequest = 4;
  JsInvokeRequest invokeRequest = 5;
  JsReleaseRequest releaseRequest = 6;
}

message RemoteJsResponse {
  int64 requestIdMSB = 1;
  int64 requestIdLSB = 2;
  JsCompileResponse compileResponse = 3;
  JsInvokeResponse invokeResponse = 4;
  JsReleaseResponse releaseResponse = 5;
}

message JsCompileRequest {
  int64 scriptIdMSB = 1;
  int64 scriptIdLSB = 2;
  string functionName = 3;
  string scriptBody = 4;
}

message JsReleaseRequest {
  int64 scriptIdMSB = 1;
  int64 scriptIdLSB = 2;
  string functionName = 3;
}

message JsReleaseResponse {
  bool success = 1;
  int64 scriptIdMSB = 2;
  int64 scriptIdLSB = 3;
}

message JsCompileResponse {
  bool success = 1;
  int64 scriptIdMSB = 2;
  int64 scriptIdLSB = 3;
  JsInvokeErrorCode errorCode = 4;
  string errorDetails = 5;
}

message JsInvokeRequest {
  int64 scriptIdMSB = 1;
  int64 scriptIdLSB = 2;
  string functionName = 3;
  string scriptBody = 4;
  int32 timeout = 5;
  repeated string args = 6;
}

message JsInvokeResponse {
  bool success = 1;
  string result = 2;
  JsInvokeErrorCode errorCode = 3;
  string errorDetails = 4;
}

