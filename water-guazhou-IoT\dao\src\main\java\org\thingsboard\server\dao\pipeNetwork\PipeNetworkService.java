package org.thingsboard.server.dao.pipeNetwork;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.ExceptionByChangeRatioDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByFlowDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByReverseDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByZeroDTO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;

import java.util.Date;
import java.util.List;

public interface PipeNetworkService {

    /**
     * 查询流量统计报表
     *
     * @param stationId 站点ID
     * @param start     开始时间
     * @param end       结束时间
     * @param queryType 查询类型。日、月、年
     * @param tenantId  租户ID
     * @return 数据
     */
    DynamicTableVO getFlowReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询供水报表，可按日、月、年查询
     * 日：按小时返回数据
     * 月：按日返回数据
     * 年：按月返回数据
     *
     * @param stationType   站点类型
     * @param stationIdList 站点ID列表
     * @param start         查询的时间范围开始时间
     * @param end           查询的时间范围结束时间
     * @param queryType     报表类型
     * @param tenantId      租户ID
     * @return 报表数据
     */
    DynamicTableVO getFlowDetailReport(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;


    /**
     * 查询指定站点的瞬时流量数据
     *
     * @param stationType 站点类型
     * @param stationId   站点ID
     * @param start       开始时间
     * @param end         结束时间
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getMeterConfigChart(String stationType, String stationId, Long start, Long end, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的瞬时流量数据
     *
     * @param stationType 站点类型
     * @param stationId   站点ID
     * @param start       开始时间
     * @param end         结束时间
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getFlowPeak(String stationType, String stationId, Long start, Long end, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的瞬时流量数据
     *
     * @param stationType 站点类型
     * @param stationId   站点ID
     * @param start       开始时间
     * @param end         结束时间
     * @param queryType   查询类型。day：日分时；month：月分日
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getFlowPeriod(String stationType, String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的瞬时流量数据
     *
     * @param stationType 站点类型
     * @param stationId   站点ID
     * @param start       开始时间
     * @param end         结束时间
     * @param type        查询类型。1: 同比；2：环比
     * @param tenantId    租户ID
     * @param attr
     * @return 数据
     */
    Object getFlowRatio(String stationType, String stationId, Long start, Long end, String type, TenantId tenantId, String attr) throws ThingsboardException;

    /**
     * 查询指定站点的压力数据
     *
     * @param stationType 站点类型
     * @param stationId   站点ID
     * @param start       开始时间
     * @param end         结束时间
     * @param queryType   查询类型。day：日分时；month：月分日
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getPressurePeriod(String stationType, String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的数据变化情况
     *
     * @param stationId 站点ID
     * @param type      分析类型
     * @param queryType 查询数据间隔
     * @param startTime 开始时间
     * @param endTime   结束事件
     * @param range     按变化百分比筛选
     * @param attr
     * @param tenantId  租户ID
     * @return 数据
     */
    List<ExceptionByChangeRatioDTO> getExceptionChangeRatio(String stationId, String type, String queryType, Date startTime, Date endTime, Integer range, String attr, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的反向流量数据
     *
     * @param stationId 站点ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param range     按反向流量数值筛选
     * @param tenantId  租户ID
     * @return 数据
     */
    List<ExceptionByReverseDTO> getExceptionReverseData(String stationId, Date startTime, Date endTime, Double range, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的反向流量数据
     *
     * @param stationId 站点ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param range     按零流量持续时间筛选
     * @param attr
     * @param tenantId  租户ID
     * @return 数据
     */
    List<ExceptionByZeroDTO> getExceptionZeroData(String stationId, Date startTime, Date endTime, Integer range, String attr, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询监测日报
     *
     * @param stationName 站点名称
     * @param dayTime     查询日期
     * @param tenantId    租户ID
     * @return 数据
     */
    List<JSONObject> getDayReport(String stationName, Date dayTime, TenantId tenantId) throws ThingsboardException;

    List<ExceptionByFlowDTO> getExceptionFlowData(String stationId, Date startTime, Date endTime, Integer divideHour, Integer overloadHour, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的起止读数数据
     *
     * @param stationIds 站点列表
     * @param start      开始毫秒值
     * @param end        结束毫秒值
     * @param tenantId   租户ID
     * @return 数据
     */
    List<JSONObject> getReadingsBetween(String stationIds, Long start, Long end, TenantId tenantId);
}
