<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.MaintainCircuitTeamCMapper">

    <select id="getListByMainId" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamC">

        select a.*, b.first_name as userName, b.email as email
        from tb_maintain_circuit_team_c a
            left join tb_user b on a.user_id = b.id
        where a.main_id = #{mainId}
        order by a.create_time
    </select>

</mapper>