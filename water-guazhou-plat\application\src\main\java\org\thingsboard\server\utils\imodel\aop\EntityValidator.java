package org.thingsboard.server.utils.imodel.aop;

import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.RequestAssistantType;
import org.thingsboard.server.dao.util.imodel.query.annotations.RequestCheckIgnore;
import org.thingsboard.server.dao.util.reflection.BeanWrapper;
import org.thingsboard.server.dao.util.reflection.FieldWrapper;

import java.util.Collection;
import java.util.List;


public class EntityValidator {

    private final List<ValidatePerformer> performers;

    public EntityValidator(List<ValidatePerformer> performers) {
        this.performers = performers;
    }

    public String validate(BeanWrapper bean, List<Object> requestAssistantFields, boolean hasParent) {
        String valid = null;
        Collection<FieldWrapper> fields = bean.getFields();
        for (FieldWrapper field : fields) {
            valid = resolveField(bean, field, requestAssistantFields, hasParent);
            if (valid != null)
                break;
        }
        return valid;
    }

    private String resolveField(BeanWrapper bean, FieldWrapper field, List<Object> requestAssistantFields, boolean hasParent) {
        if (field.isAnnotationPresent(RequestCheckIgnore.class)) {
            return null;
        }

        Object value = field.getValue();
        if (Requestible.getType(value) != RequestAssistantType.NONE) {
            requestAssistantFields.add(value);
            return null;
        }
        boolean isString = CheckStringReturnType(field);
        for (ValidatePerformer performer : performers) {
            if (!performer.match(field))
                continue;

            return isString ?
                    performer.resolveString(bean, field, (String) value, hasParent) :
                    performer.resolveDestType(bean, field, field.getReturnType(), value, hasParent);
        }
        return null;
    }

    private boolean CheckStringReturnType(FieldWrapper field) {
        return field.getReturnType().equals(String.class);
    }


}
