package org.thingsboard.server.dao.model.sql.smartManagement.settings;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

/**
 * 巡检计划配置实体类
 */
@Getter
@Setter
@ResponseEntity
@TableName("sm_inspection_plan_setting")
public class InspectionPlanSetting {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 巡检计划名称
     */
    private String planName;

    /**
     * 巡检类型
     * pipeline: 管网巡检
     * pumpStation: 泵站巡检
     * other: 其他巡检
     */
    private String inspectionType;

    /**
     * 巡检周期
     * weekly_135: 每周一、三、五日执行
     * monthly_51525: 每月5、15、25日执行
     * quarterly_10: 每季度第一个月10日执行
     */
    private String inspectionCycle;

    /**
     * 执行角色
     * maintenance_equipment: 运维组长、设备专员
     * inspector_equipment: 巡检员、设备专员
     * other: 其他
     */
    private String executionRole;

    /**
     * 检查表模板
     */
    private String checklistTemplate;

    /**
     * 状态
     * 1: 启用
     * 0: 停用
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户ID
     */
    private String tenantId;
}
