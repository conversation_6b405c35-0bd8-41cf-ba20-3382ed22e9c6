package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionCustMeter;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPipe;

/**
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-26
 */
@Mapper
public interface PartitionPipeMapper extends BaseMapper<PartitionPipe> {

    IPage<PartitionPipe> getList(IPage<PartitionPipe> page, @Param("param") PartitionMountRequest request);

}
