import{_ as I}from"./index-C9hz-UZb.js";import{_ as g}from"./index-BJ-QPYom.js";import{c as _,ej as v,a1 as h,d as y,r as f,b as i,S as k,f9 as S,ey as T,o as C,fa as b,g as w,h as j,F as d,q as c,i as o,p as M,bh as P,C as N}from"./index-r0dFAfgr.js";import B from"./AouForm-SqVd6IXc.js";import{_ as L}from"./ExportButton.vue_vue_type_script_setup_true_lang-DS7q8jPf.js";import{_ as A}from"./ImportButton.vue_vue_type_script_setup_true_lang-BAV2zz8w.js";import D from"./TreeBox-mfOmxwZJ.js";import"./Preview-C8ycjeyy.js";import"./ImportButton-BXPDxx92.js";/* empty css                                                                     */const F=()=>{const l=_([]);return{init:async()=>{try{const e=await v();l.value=h(e.data,{id:"id",label:"name",value:"url",children:"children"})||[]}catch{l.value=[]}},options:l}},G={class:"title"},O=y({__name:"index",setup(l){const m=_(),e=f({title:"菜单管理",rootId:"",parentId:"",menu:void 0}),r=f({title:"菜单列表",data:[],add:{text:"添加",perm:!0,click:t=>{if(t&&(r.currentProject=t),!r.currentProject){i.warning("请先选择一个菜单");return}e.title="添加子级菜单",e.parentId=t==null?void 0:t.value,e.menu=void 0}},addRoot:{perm:!0,text:"添加根级菜单",click:async()=>{e.title="添加根级菜单",e.parentId=e.rootId,e.menu=void 0}},edit:{perm:!0,text:"编辑",click:t=>{if(t&&(r.currentProject=t),!r.currentProject){i.warning("请先选择一个菜单");return}e.title="编辑菜单",e.parentId="",e.menu=t}},delete:{perm:!0,text:"删除",click:t=>{k("确定删除？","删除提示").then(async()=>{var a;const n=((a=t.id)==null?void 0:a.split(","))||[];if(!n.length){i.warning("请先选择要删除的数据");return}const s=await S(n);s.data.code===200?(i.success("删除成功"),u()):i.error(s.data.message||"删除失败")}).catch(()=>{})}},loading:!1,isFilterTree:!0,expandNodeId:[],defaultProps:{children:"children",label:"name"},defaultExpandAll:!1,expandOnClickNode:!1,treeNodeHandleClick:async t=>{r.currentProject=t,e.parentId="",e.menu=t}}),u=async()=>{var a;const n=((a=(await T()).data)==null?void 0:a.filter(x=>x.type!=="2"))||[],s=h(n,{id:"id",value:"id",label:"label",children:"children"},void 0);r.data=s},p=F();return C(async()=>{u(),b().then(t=>{var n;e.rootId=(n=t.data)==null?void 0:n.id}),p.init()}),(t,n)=>{const s=g,a=I;return w(),j(D,null,{tree:d(()=>[c(s,{ref_key:"refTree",ref:m,"tree-data":o(r)},null,8,["tree-data"])]),default:d(()=>[c(a,{class:"card",title:o(e).title},{title:d(()=>[M("span",G,P(o(e).title),1),c(L),c(A)]),default:d(()=>[c(B,{"menu-root-id":o(e).rootId,sources:o(p).options.value,menu:o(e).menu,menus:o(r).data,"menu-parent-id":o(e).parentId,onSuccess:u},null,8,["menu-root-id","sources","menu","menus","menu-parent-id"])]),_:1},8,["title"])]),_:1})}}}),U=N(O,[["__scopeId","data-v-790ceccb"]]);export{U as default};
