package org.thingsboard.server.dao.model.sql.smartService.knowledge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.thingsboard.server.dao.model.ModelConstants;

import java.util.Date;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-26
 */
@TableName(ModelConstants.SERVICE_KNOWLEDGE_DOCUMENT_TABLE)
@Data
public class KnowledgeDocument {
    
    @TableId
    private String id;

    @TableField(ModelConstants.SERVICE_KNOWLEDGE_DOCUMENT_TYPE_ID)
    private String typeId;

    @TableField(exist = false)
    private String typeName;

    @TableField(ModelConstants.SERVICE_KNOWLEDGE_DOCUMENT_NAME)
    private String name;

    @TableField(ModelConstants.SERVICE_KNOWLEDGE_DOCUMENT_URL)
    private String url;

    @TableField(ModelConstants.REMARK)
    private String remark;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
}
