<!-- 机房信息 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable
      ref="refTable"
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm ref="refForm" :config="FormConfig"> </DialogForm>
    <DialogForm ref="refUploadDialog" :config="uploadConfig">
      <el-form ref="ruleFormRef" :rules="rules" :model="ruleForm">
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <div class="buttons">
                <el-button
                  type="primary"
                  :icon="Download"
                  :plain="true"
                  @click="downloadTemplate"
                >
                  下载模板
                </el-button>
                <el-upload
                  ref="upload"
                  action="action"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                  :show-file-list="true"
                  :auto-upload="false"
                  :on-remove="handleRemove"
                  :limit="1"
                  :on-change="clickUpload"
                >
                  <el-button type="primary" :icon="Upload">
                    添加文件
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      只能导入xlsx文件, 请确保导入的文件单元格格式为文本!
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="泵房" required prop="pumpRoomId">
              <el-select v-model="ruleForm.pumpRoomId" placeholder="请选择泵房">
                <el-option
                  v-for="(item, index) in state.pumpRooms"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <FormTable :config="uploadTableConfig"></FormTable>
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import {
  Delete,
  Edit,
  Download,
  Plus,
  Refresh,
  Search,
  Upload
} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { FormInstance, FormRules, ElMessage } from 'element-plus';
import { SLConfirm } from '@/utils/Message.js';
import {
  moreFilters,
  machineRoomInfoColumn,
  formFields,
  pumpUpload,
  readExcelToJson
} from './data';
import {
  pumpManageList,
  addPumpManage,
  editPumpManage,
  delPumpManage,
  pumpHouseStorageList,
  batchAddPumpManage,
  pumpManageExport,
  pumpManageTemplate
} from '@/api/secondSupplyManage/pumpRoomInfo';

const ruleFormRef = ref<FormInstance>();
const refForm = ref<IDialogFormIns>();
const refTable = ref<ICardTableIns>();
const refUploadDialog = ref<IDialogFormIns>();
const refSearch = ref<ICardSearchIns>();
const state = reactive<{
  pumpRooms: any;
  pumpRoomId: string;
  dataList: any;
}>({
  pumpRooms: [],
  pumpRoomId: '',
  dataList: []
});
const ruleForm = reactive({
  pumpRoomId: ''
});

const rules = reactive<FormRules>({
  pumpRoomId: [
    {
      required: true,
      message: '请选择泵房',
      trigger: 'blur'
    }
  ]
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '泵房编码',
      field: 'pumpRoomCode',
      placeholder: '请输入泵房编码'
    },
    {
      type: 'input',
      label: '泵房名称',
      field: 'pumpRoomName',
      placeholder: '请输入泵房名称'
    },
    {
      type: 'input',
      label: '设备编码',
      field: 'code',
      placeholder: '请输入设备编码'
    },
    ...moreFilters
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
          }
        },
        {
          perm: true,
          text: '添加',
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.title = '新增';
            handleEdit();
          }
        },
        {
          perm: true,
          text: '导入',
          type: 'warning',
          svgIcon: shallowRef(Upload),
          click: () => {
            handleRemove();
            refUploadDialog.value?.openDialog();
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          svgIcon: shallowRef(Download),
          click: () => exportTable()
        }
      ]
    }
  ]
});

// 导入弹框
const uploadConfig = reactive<IDialogFormConfig>({
  title: '导入',
  dialogWidth: 1200,
  group: [],
  cancel: true,
  btns: [
    {
      perm: true,
      text: '确定导入',
      click: async () => {
        await ruleFormRef.value?.validate(async (valid) => {
          if (valid) {
            if (state.dataList.length > 0) {
              console.log(state.dataList);
              state.dataList = state.dataList.map((data) => {
                return (data = {
                  ...data,
                  pumpRoomId: ruleForm.pumpRoomId
                });
              });
              batchAddPumpManage(state.dataList)
                .then((res) => {
                  if (res.data?.code === 200) {
                    handleRemove();
                    refUploadDialog.value?.closeDialog();
                    ElMessage.success('提交成功');
                    refreshData();
                  } else {
                    ElMessage.error('提交失败');
                  }
                })
                .catch((error) => {
                  console.log(error);
                  ElMessage.error('提交失败');
                });
            } else {
              ElMessage.warning('请导入正确的xlsx文件！');
            }
          }
        });
      }
    }
  ]
});

// 解析导入文件数据
const clickUpload = (file: any) => {
  state.dataList = [];
  readExcelToJson(file).then((res: any) => {
    res &&
      res.forEach((el) => {
        const val = {};
        for (const i in el) {
          if (typeof pumpUpload[i] === 'undefined') {
            ElMessage.info(
              '设备编码/设备名称/设备简称/泵个数/厂家名称/设备型号/安装人/安装日期/性能参数/备注; 且每行均有对应数据!'
            );
            return;
          }
          val[pumpUpload[i]] = el[i];
        }
        state.dataList.push(val);
      });
    const strings = state.dataList.map((item) => JSON.stringify(item));
    const removeDupList = [...new Set(strings)]; // 也可以使用Array.from(new Set(strings))
    uploadTableConfig.dataList = removeDupList.map((item: any) =>
      JSON.parse(item)
    );
  });
};
// 导出列表
const exportTable = async () => {
  const query = refSearch.value?.queryParams || {};
  const [installDateFrom, installDateTo] = query.installDateFrom;
  const [fromTime, toTime] = query.fromTime;
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: -1,
    installDateFrom: installDateFrom
      ? dayjs(installDateFrom).startOf('day').valueOf()
      : null,
    installDateTo: installDateTo
      ? dayjs(installDateTo).startOf('day').valueOf()
      : null,
    fromTime,
    toTime
  };
  const res = await pumpManageExport(params);
  const url = window.URL.createObjectURL(res.data);
  console.log(url);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `机房信息列表.xlsx`);
  document.body.appendChild(link);
  link.click();
};
// 下载模板
const downloadTemplate = async () => {
  const res = await pumpManageTemplate();
  const url = window.URL.createObjectURL(res.data);
  console.log(url);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `机房信息模板.xlsx`);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(link.href);
};
// 上传数据列表
const uploadTableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: formFields.slice(1),
  dataList: [],
  pagination: {
    hide: true
  }
});
// 数据列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: machineRoomInfoColumn,
  operations: [
    {
      perm: true,
      text: '修改',
      svgIcon: shallowRef(Edit),
      click: (row) => {
        FormConfig.title = '修改';
        handleEdit(row);
      }
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});
// 表单配置
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  title: '新增',
  labelWidth: 120,
  group: [{ fields: formFields }],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(() => {
        if (FormConfig.title === '新增') {
          addPumpManage(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('提交成功');
            refreshData();
          });
        } else {
          editPumpManage(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('提交成功');
            refreshData();
          });
        }
      })
      .catch(() => {
        //
      });
  }
});
// 编辑弹框
const handleEdit = (row?: any) => {
  FormConfig.defaultValue = row
    ? {
        ...row,
        installDate: dayjs(row.installDate).format()
      }
    : {};
  refForm.value?.openDialog();
};
// 删除数据
const handleDelete = (row?: any) => {
  SLConfirm('确定删除?', '提示信息')
    .then(() => {
      delPumpManage(row.id).then(() => {
        refreshData();
      });
    })
    .catch(() => {
      //
    });
};
// 刷新数据
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {};
  const [installDateFrom, installDateTo] = query.installDateFrom;
  const [fromTime, toTime] = query.fromTime;
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    installDateFrom: installDateFrom
      ? dayjs(installDateFrom).startOf('day').valueOf()
      : null,
    installDateTo: installDateTo
      ? dayjs(installDateTo).startOf('day').valueOf()
      : null,
    fromTime,
    toTime
  };
  const result = await pumpManageList(params);
  console.log(result.data.data.total);
  TableConfig.pagination.total = result.data?.data.total;
  TableConfig.dataList = result.data?.data.data;
};
// 删除导入文件清空数据
const handleRemove = () => {
  state.dataList = [];
  uploadTableConfig.dataList = [];
};
//
onMounted(async () => {
  const result = await pumpHouseStorageList({ size: 999, page: 1 });
  const pumpRooms = result?.data?.data?.data;
  state.pumpRooms = pumpRooms;
  const pumpHouseOptions = pumpRooms.map((pumpHouse) => {
    return { label: pumpHouse.name, value: pumpHouse.id };
  });
  const pumpRoomField = FormConfig.group[0]?.fields?.find(
    (field) => field.field === 'pumpRoomId'
  ) as IFormSelect;
  pumpRoomField.options = pumpHouseOptions;
  refreshData();
});
</script>
<style lang="scss" scoped>
.upload-demo {
}

.buttons {
  display: flex;
  align-items: flex-start;

  button {
    margin-right: 10px;
  }
}
</style>
