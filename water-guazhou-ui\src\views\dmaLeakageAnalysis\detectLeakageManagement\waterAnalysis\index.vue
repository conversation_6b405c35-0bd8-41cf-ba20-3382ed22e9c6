<template>
  <div class="app-container">
    
    <!-- 搜索栏 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <!-- 分区筛选框放入表单项 -->
      <el-form-item label="分区名称" prop="partitionId">
        <el-tree-select
          v-model="selectedPartition"
          :data="partition.Tree.value"
          :props="{ label: 'label', value: 'id', children: 'children' }"
          placeholder="请选择分区"
          style="width: 240px"
          @change="handleQuery"
          check-strictly
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="paginatedData" border>
      <el-table-column label="分区名称" align="center" prop="partitionName" />
      <el-table-column label="总用户数" align="center" prop="totalUsers" />
      <el-table-column label="正常用户" align="center" prop="normalUsers" />
      <el-table-column label="用水异常用户" align="center" prop="abnormalWaterUsers" />
      <el-table-column label="流量异常" align="center" prop="abnormalFlow" />
      <el-table-column label="表计异常" align="center" prop="abnormalMeter" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      :current-page="pagination.page"
      :page-size="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="detailTitle"
      width="80%"
      append-to-body
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="流量异常" name="flow">
          <el-table :data="flowAbnormalData" border v-loading="detailLoading">
            <el-table-column label="水表编号" align="center" prop="meterId" />
            <el-table-column label="用户名称" align="center" prop="userName" />
            <el-table-column label="用户地址" align="center" prop="address" />
            <el-table-column label="异常类型" align="center" prop="abnormalType" />
            <el-table-column label="当前流量" align="center" prop="currentFlow" />
            <el-table-column label="历史平均流量" align="center" prop="avgFlow" />
            <el-table-column label="异常程度" align="center">
              <template #default="scope">
                <el-tag :type="getAbnormalLevel(scope.row.abnormalLevel).type">
                  {{ getAbnormalLevel(scope.row.abnormalLevel).label }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="flowAbnormalData.length > 0"
            :current-page="flowPagination.page"
            :page-size="flowPagination.limit"
            :total="flowTotal"
            layout="total, prev, pager, next"
            @current-change="(val) => handleDetailPageChange(val, 'flow')"
            class="pagination"
          />
        </el-tab-pane>
        <el-tab-pane label="表计异常" name="meter">
          <el-table :data="meterAbnormalData" border v-loading="detailLoading">
            <el-table-column label="水表编号" align="center" prop="meterId" />
            <el-table-column label="用户名称" align="center" prop="userName" />
            <el-table-column label="用户地址" align="center" prop="address" />
            <el-table-column label="异常类型" align="center" prop="abnormalType" />
            <el-table-column label="电池电量" align="center" prop="batteryLevel" />
            <el-table-column label="信号强度" align="center" prop="signalStrength" />
            <el-table-column label="异常程度" align="center">
              <template #default="scope">
                <el-tag :type="getAbnormalLevel(scope.row.abnormalLevel).type">
                  {{ getAbnormalLevel(scope.row.abnormalLevel).label }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="meterAbnormalData.length > 0"
            :current-page="meterPagination.page"
            :page-size="meterPagination.limit"
            :total="meterTotal"
            layout="total, prev, pager, next"
            @current-change="(val) => handleDetailPageChange(val, 'meter')"
            class="pagination"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { usePartition } from '@/hooks/arcgis'
import { getWaterAnalysisList, getAbnormalDetail } from '@/api/leakDetection/waterAnalysis'

const loading = ref(false)
const showSearch = ref(true)
const total = ref(0)

// 分区树相关
const partition = usePartition()
const selectedPartition = ref<string | null>(null)

onMounted(async () => {
  await partition.getTree()
  handleQuery()
})

// 表格数据
const allData = ref<any[]>([])

const queryParams = reactive({
  partitionName: '',
})

const pagination = reactive({
  page: 1,
  limit: 10,
})

// 直接使用后端分页数据
const paginatedData = computed(() => {
  return allData.value || [];
});

async function handleQuery() {
  loading.value = true
  try {
    const params = {
      partitionId: selectedPartition.value,
      page: pagination.page,
      pageSize: pagination.limit
    }
    
    const res = await getWaterAnalysisList(params)
    
    if (res && res.data && res.data.code === 200) {
      const responseData = res.data.data || {}
      
      // 处理不同的数据格式
      if (responseData.content) {
        allData.value = responseData.content
        total.value = responseData.totalElements || 0
      } else if (Array.isArray(responseData)) {
        allData.value = responseData
        total.value = responseData.length
      } else {
        allData.value = responseData.data || [responseData]
        total.value = responseData.total || allData.value.length
      }
    } else {
      ElMessage.error('获取数据失败')
      allData.value = []
      total.value = 0
    }
  } catch (error) {
    ElMessage.error('获取用水分析数据失败')
    allData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

function handleSizeChange(val: number) {
  pagination.limit = val
  handleQuery()
}

function handleCurrentChange(val: number) {
  pagination.page = val
  handleQuery()
}

function resetQuery() {
  queryParams.partitionName = ''
  selectedPartition.value = null
  pagination.page = 1
  handleQuery()
}

// 详情弹窗相关
const detailVisible = ref(false)
const detailTitle = ref('')
const activeTab = ref('flow')
const detailLoading = ref(false)

// 流量异常数据
const flowAbnormalData = ref<any[]>([])
const flowTotal = ref(0)
const flowPagination = reactive({
  page: 1,
  limit: 10
})

// 表计异常数据
const meterAbnormalData = ref<any[]>([])
const meterTotal = ref(0)
const meterPagination = reactive({
  page: 1,
  limit: 10
})

// 当前查看的记录
const currentRecord = ref<any>(null)

function handleView(row: any) {
  detailVisible.value = true
  detailTitle.value = `${row.partitionName} 异常详情`
  currentRecord.value = row
  activeTab.value = 'flow'
  
  // 重置分页
  flowPagination.page = 1
  meterPagination.page = 1
  
  // 加载详情数据
  loadDetailData('flow')
}

// 加载详情数据
function loadDetailData(tabName: string) {
  if (!currentRecord.value) return
  
  detailLoading.value = true
  
  const params = {
    partitionId: currentRecord.value.partitionId,
    type: tabName as 'flow' | 'meter',
    page: tabName === 'flow' ? flowPagination.page : meterPagination.page,
    pageSize: tabName === 'flow' ? flowPagination.limit : meterPagination.limit
  }
  
  getAbnormalDetail(params)
    .then(res => {
      if (res && res.data && res.data.code === 200) {
        const responseData = res.data.data || {}
        
        if (tabName === 'flow') {
          flowAbnormalData.value = responseData.data || []
          flowTotal.value = responseData.total || 0
        } else {
          meterAbnormalData.value = responseData.data || []
          meterTotal.value = responseData.total || 0
        }
      } else {
        ElMessage.error('获取异常详情失败')
        if (tabName === 'flow') {
          flowAbnormalData.value = []
          flowTotal.value = 0
        } else {
          meterAbnormalData.value = []
          meterTotal.value = 0
        }
      }
    })
    .catch(error => {
      console.error('获取异常详情失败', error)
      ElMessage.error('获取异常详情失败')
      if (tabName === 'flow') {
        flowAbnormalData.value = []
        flowTotal.value = 0
      } else {
        meterAbnormalData.value = []
        meterTotal.value = 0
      }
    })
    .finally(() => {
      detailLoading.value = false
    })
}

// 监听标签页切换
watch(activeTab, (newValue) => {
  loadDetailData(newValue)
})

// 详情分页切换
function handleDetailPageChange(page: number, type: string) {
  if (type === 'flow') {
    flowPagination.page = page
    loadDetailData('flow')
  } else {
    meterPagination.page = page
    loadDetailData('meter')
  }
}

// 获取异常级别标签
function getAbnormalLevel(level: number) {
  const levels = [
    { type: 'info' as const, label: '轻微' },
    { type: 'success' as const, label: '一般' },
    { type: 'warning' as const, label: '中度' },
    { type: 'danger' as const, label: '严重' },
    { type: 'danger' as const, label: '极严重' }
  ]
  
  return levels[level] || levels[0]
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.pagination {
  margin-top: 15px;
  text-align: right;
}
</style> 