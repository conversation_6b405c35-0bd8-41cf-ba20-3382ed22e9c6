package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSitePackage;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalSitePackageMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.TreeQuery;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSitePackagePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSitePackageSaveRequest;

import java.util.List;

@Service
public class SsPortalSitePackageServiceImpl implements SsPortalSitePackageService {
    @Autowired
    private SsPortalSitePackageMapper mapper;

    @Override
    public SsPortalSitePackage findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalSitePackage> findAllConditional(SsPortalSitePackagePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalSitePackage save(SsPortalSitePackageSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalSitePackage entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<SsPortalSitePackage> tree(SsPortalSitePackagePageRequest request) {
        List<SsPortalSitePackage> roots = mapper.findRoots(request);
        return QueryUtil.buildMixTree(roots, TreeQuery.of(parentId -> mapper.findChildren(parentId, request.getJumpToUrl(), request.tenantId()), 1));
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

    @Override
    public boolean canSave(SsPortalSitePackageSaveRequest request) {
        if (request.getParentId() == null) {
            return true;
        }

        return mapper.canSave(request);
    }

}
