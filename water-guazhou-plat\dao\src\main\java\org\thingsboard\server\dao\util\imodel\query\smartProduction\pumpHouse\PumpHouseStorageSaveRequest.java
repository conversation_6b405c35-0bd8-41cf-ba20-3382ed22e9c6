package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class PumpHouseStorageSaveRequest extends SaveRequest<PumpHouseStorage> {
    // 泵房编号
    @NotNullOrEmpty
    private String code;

    // 泵房名称
    @NotNullOrEmpty
    private String name;

    // 泵房简称
    @NotNullOrEmpty
    private String nickname;

    // 厂家名称
    @NotNullOrEmpty
    private String companyName;

    // 供水方式
    @NotNullOrEmpty
    private String supplyMethod;

    // 水箱个数
    @NotNullOrEmpty
    private Integer waterBoxNum;

    // 地址
    @NotNullOrEmpty
    private String address;

    // 安装人名称
    @NotNullOrEmpty
    private String installUserName;

    // 安装时间
    @NotNullOrEmpty
    private String installDate;

    // 采集频率（分钟）
    @NotNullOrEmpty
    private Integer collectionFrequency;

    // 存储频率（分钟）
    @NotNullOrEmpty
    private Integer storageFrequency;

    // 备注
    private String remark;


    @Override
    protected PumpHouseStorage build() {
        PumpHouseStorage entity = new PumpHouseStorage();
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        entity.setCreateTime(createTime());
        commonSet(entity);
        return entity;
    }

    @Override
    protected PumpHouseStorage update(String id) {
        PumpHouseStorage entity = new PumpHouseStorage();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(PumpHouseStorage entity) {
        entity.setCode(code);
        entity.setName(name);
        entity.setNickname(nickname);
        entity.setCompanyName(companyName);
        entity.setSupplyMethod(supplyMethod);
        entity.setWaterBoxNum(waterBoxNum);
        entity.setAddress(address);
        entity.setInstallUserName(installUserName);
        entity.setInstallDate(TimeUtils.defaultIfNull(installDate, null));
        entity.setCollectionFrequency(collectionFrequency);
        entity.setStorageFrequency(storageFrequency);
        entity.setRemark(remark);
    }

}