<!-- 故障上报 -->
<template>
  <div class="wrapper">
    <el-card
      class="box-card"
      style="height: 100%;overflow: auto;"
    >
      <Form
        :key="key"
        ref="refForm"
        :config="addOrUpdateConfig"
      ></Form>
    </el-card>
    <!-- 设备选择 -->
    <SLDrawer
      ref="refFormEquipment"
      :config="addEquipment"
    ></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { getWaterSupplyTree } from '@/api/company_org'
import { getUserList } from '@/api/user/index'
import { ISLDrawerIns } from '@/components/type'
import { removeSlash } from '@/utils/removeIdSlash'
import {
  getAreaTreeSearch
} from '@/api/equipment_assets/equipmentManage'
import { getDeviceStorageJournalAll } from '@/api/equipment_assets/ledgerManagement'
import { postFaultReport, getFaultKnowledgeSerch } from '@/api/equipment_assets/malfunctionRepair'

import useGlobal from '@/hooks/global/useGlobal'
import { traverse, uniqueFunc } from '@/utils/GlobalHelper'
import { getWorkOrderResourceList } from '@/api/system/OutsideWorkOrder'
import { getWorkOrderTypeList, getWorkOrderEmergencyLevelList, getWorkOrderProcessLevelList } from '@/api/workorder'

const { $btnPerms } = useGlobal()

const refFormEquipment = ref<ISLDrawerIns>()

const refForm = ref<ISLDrawerIns>()

const chosen = ref([])

const key = ref((new Date()).toString())

const state = reactive<{
  workOrderResourceList: any[]
  workOrderTypeList:any[]
  WorkOrderEmergencyLevelList: any[],
  WorkOrderProcessLevelList:any[]
}>({
  workOrderResourceList: [],
  workOrderTypeList: [],
  WorkOrderEmergencyLevelList: [],
  WorkOrderProcessLevelList: []
})

function initOptions() {
  // 事件来源
  getWorkOrderResourceList({ status: '1', page: 1, size: 999 }).then(res => {
    state.workOrderResourceList = traverse(res.data.data.data || [], 'children', { label: 'name', value: 'name' })
  })

  // 工单类型
  getWorkOrderTypeList('1').then(res => {
    state.workOrderTypeList = traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
  })

  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then(res => {
    state.WorkOrderEmergencyLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'id' })
  })

  // 处理级别
  getWorkOrderProcessLevelList('1').then(res => {
    state.WorkOrderProcessLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
  })
}

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '故障上报',
  labelWidth: '100px',
  submit: (params: any) => {
    const workOrder = { ...params, isDirectDispatch: true }
    params.workOrder = JSON.parse(JSON.stringify(workOrder))
    postFaultReport(params).then(() => {
      ElMessage.success('故障上报成功')
      data.selectList = []
      key.value = (new Date()).toString()
    })
  },

  defaultValue: {
    source :'设备资产',
    type:'设备故障'
  },
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'select',
          label: '紧急程度',
          field: 'level',
          rules: [{ required: true, message: '请输入紧急程度' }],
          options: computed(() => state.WorkOrderEmergencyLevelList) as any
        }, {
          xl: 8,
          type: 'select',
          label: '事件来源',
          field: 'source',
          readonly:true,
          rules: [{ required: true, message: '请选择事件来源' }],
          options: computed(() => state.workOrderResourceList) as any
        }, {
          xl: 8,
          type: 'select-tree',
          label: '事件类型',
          field: 'type',
          readonly:true,
          rules: [{ required: true, message: '请选择事件类型' }],
          options: computed(() => state.workOrderTypeList) as any
        }, {
          xl: 8,
          type: 'select-tree',
          label: '接收部门',
          field: 'key4',
          defaultExpandAll: true,
          checkStrictly: true,
          rules: [{ required: true, message: '请选择接收部门' }],
          options: computed(() => data.WaterSupplyTree) as any,
          onChange: row => { data.getUserListValue(row) }
        }, {
          xl: 8,
          type: 'select',
          label: '接收人员',
          field: 'processUserId',
          rules: [{ required: true, message: '请选择接收人员' }],
          options: computed(() => data.UserList) as any
        }, {
          xl: 8,
          type: 'input',
          label: '事件标题',
          field: 'title',
          rules: [{ required: true, message: '请输入事件标题' }]
        }, {
          xl: 8,
          type: 'textarea',
          label: '事件地址',
          field: 'address',
          rules: [{ required: true, message: '请输入事件地址' }]
        }, {
          xl: 8,
          type: 'textarea',
          label: '事件描述',
          field: 'remark',
          rules: [{ required: true, message: '请输入事件描述' }]
        }, {
          type: 'table',
          field: 'faultReportCList',
          config: {
            indexVisible: true,
            height: '350px',
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加设备',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.openDrawer()
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备型号',
                prop: 'model'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '安装区域',
                prop: 'installAddressName'
              }, {
                label: '安装位置',
                prop: 'actualAddress'
              }
            ],
            operations: [
              {
                text: '移除',
                type: 'danger',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: row => {
                  data.selectList = data.selectList.filter(item => item.id !== row.id)
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }, {
          type: 'divider',
          text: ' '
        }, {
          xl: 8,
          type: 'select',
          label: '故障类别',
          field: 'faultTypeId',
          options: computed(() => data.faultCategory) as any
        }, {
          xl: 8,
          type: 'input',
          label: '故障项目',
          field: 'faultProject'
        }, {
          xl: 8,
          type: 'input',
          label: '故障描述',
          field: 'faultRemark'
        }, {
          xl: 16,
          type: 'image',
          label: '现场图片',
          field: 'imgUrl',
          limit: 5
        }, {
          type: 'file',
          label: '现场录音',
          field: 'audioUrl',
          accept: 'audio/*',
          limit: 5
        }, {
          type: 'hint',
          text: '只能上传音频文件，最多上传5个，且大小不能超过4M'
        }, {
          type: 'file',
          label: '现场视频',
          field: 'videoUrl',
          accept: 'video/*',
          limit: 2
        }, {
          type: 'hint',
          text: '只能上传视频文件，最多上传2个，且大小不能超过100M'
        }, {
          type: 'file',
          label: '其他附件',
          field: 'otherFileUrl',
          limit: 2
        }, {
          type: 'hint',
          text: '附件最多只能上传两个，最多上传2个，且大小不能超过10M'
        }, {
          xl: 8,
          type: 'divider',
          text: '操作'
        }, {
          type: 'btn-group',
          btns: [
            {
              text: '上报',
              perm: true,
              click: () => {
                refForm.value?.Submit()
              }
            },
            {
              type: 'default',
              text: '重置',
              perm: true,
              click: () => {
                data.selectList = []
                refForm.value?.resetForm()
              }
            }
          ]
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDialogFormConfig>({
  title: '设备选择',
  labelWidth: '130px',
  submit: (params: any, status?: boolean) => {
    // 搜索处理
    if (status) {
      delete params.device
      data.getDevice(params)
    } else {
      data.selectList = [...data.selectList, ...chosen.value]
      data.selectList = uniqueFunc(data.selectList, 'id')
      refFormEquipment.value?.closeDrawer()
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '标签编码',
          field: 'deviceLabelCode'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        }, {
          xl: 8,
          label: '安装区域',
          field: 'areaId',
          type: 'select-tree',
          checkStrictly: true,
          options: computed(() => data.installationArea) as any
        },
        //  {
        //   xl: 8,
        //   type: 'select',
        //   label: '安装位置',
        //   field: 'detailInstallAddressName'
        // },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '查询',
                        click: () => {
                          refFormEquipment.value?.Submit(true)
                        },
                        perm: true
                      }, {
                        text: '重置',
                        type: 'primary',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.resetForm()
                          refFormEquipment.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备型号',
                prop: 'model'
              },
              {
                label: '所属大类',
                prop: 'topType'
              },
              {
                label: '所属类别',
                prop: 'type'
              },
              {
                label: '安装区域',
                prop: 'installAddressName'
              }, {
                label: '安装位置',
                prop: 'actualAddress'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const data = reactive({
  // 请购部门
  WaterSupplyTree: [],
  // 用户列表
  UserList: [],
  // 设备列表
  deviceValue: [] as any[],
  // 设备数量
  total: 0,
  // 选中设备
  selectList: [] as any,
  // 故障类别
  faultCategory: [] as any,
  // 安装区域
  installationArea: [],
  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    }
    getDeviceStorageJournalAll(params).then(res => {
      data.deviceValue = res.data.data.data || []
      data.total = res.data.data.total || 0
    })
  },

  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id), userName: item.name }
      })
    })
  },
  // 故障类别
  getFaultKnowledgeValue: () => {
    const params = { size: 99999, page: 1 }
    getFaultKnowledgeSerch(params).then(res => {
      data.faultCategory = traverse(res.data.data.data || [])
    })
  },
  // 获取安装区域
  getAreaTreeValue: () => {
    const params = {
      page: 1,
      size: 99999,
      shortName: ''
    }
    getAreaTreeSearch(params).then(res => {
      data.installationArea = traverse(res.data.data.data || [])
    })
  }
})

onMounted(() => {
  initOptions()
  data.getWaterSupplyTreeValue()
  data.getDevice()
  data.getFaultKnowledgeValue()
  data.getAreaTreeValue()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}</style>
