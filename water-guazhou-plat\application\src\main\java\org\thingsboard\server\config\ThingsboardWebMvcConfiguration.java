package org.thingsboard.server.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ParameterUtils;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.pagination.DialectModel;
import com.baomidou.mybatisplus.extension.plugins.pagination.dialects.IDialect;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;
import org.thingsboard.server.utils.imodel.annotations.EnableIModel;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import static org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity.PageableQueryEntityType.MANUAL;

@Configuration
@EnableIModel
public class ThingsboardWebMvcConfiguration {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL) {
            @Override
            public boolean willDoQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
                IPage<?> page = ParameterUtils.findPage(parameter).orElse(null);
                if (page == null || page.getSize() < 0 || !page.isSearchCount()) {
                    return true;
                }

                if (page instanceof PageableQueryEntity && ((PageableQueryEntity<?>) page).type() == MANUAL) {
                    return true;
                }

                BoundSql countSql;
                MappedStatement countMs = buildCountMappedStatement(ms, page.countId());
                if (countMs != null) {
                    countSql = countMs.getBoundSql(parameter);
                } else {
                    countMs = buildAutoCountMappedStatement(ms);
                    String countSqlStr = autoCountSql(page.optimizeCountSql(), boundSql.getSql());
                    PluginUtils.MPBoundSql mpBoundSql = PluginUtils.mpBoundSql(boundSql);
                    countSql = new BoundSql(countMs.getConfiguration(), countSqlStr, mpBoundSql.parameterMappings(), parameter);
                    PluginUtils.setAdditionalParameter(countSql, mpBoundSql.additionalParameters());
                }

                CacheKey cacheKey = executor.createCacheKey(countMs, parameter, rowBounds, countSql);
                Object result = executor.query(countMs, parameter, rowBounds, resultHandler, cacheKey, countSql).get(0);
                page.setTotal(result == null ? 0L : Long.parseLong(result.toString()));
                return continuePage(page);
            }

            @Override
            public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
                IPage<?> page = ParameterUtils.findPage(parameter).orElse(null);
                if (null == page) {
                    return;
                }

                if (page instanceof PageableQueryEntity && ((PageableQueryEntity<?>) page).type() == MANUAL) {
                    return;
                }

                // 处理 orderBy 拼接
                boolean addOrdered = false;
                String buildSql = boundSql.getSql();
                List<OrderItem> orders = page.orders();
                if (!CollectionUtils.isEmpty(orders)) {
                    addOrdered = true;
                    buildSql = this.concatOrderBy(buildSql, orders);
                }

                // size 小于等于 0 不构造分页sql
                if (page.getSize() <= 0 || page.getRecords() == null ||
                    (ms.getResultMaps().size() > 0 && ms.getResultMaps().get(0).getType().equals(Long.class))) {
                    if (addOrdered) {
                        PluginUtils.mpBoundSql(boundSql).sql(buildSql);
                    }
                    return;
                }

                handlerLimit(page);
                IDialect dialect = findIDialect(executor);

                final org.apache.ibatis.session.Configuration configuration = ms.getConfiguration();
                DialectModel model = dialect.buildPaginationSql(buildSql, page.offset(), page.getSize());
                PluginUtils.MPBoundSql mpBoundSql = PluginUtils.mpBoundSql(boundSql);

                List<ParameterMapping> mappings = mpBoundSql.parameterMappings();
                Map<String, Object> additionalParameter = mpBoundSql.additionalParameters();
                model.consumers(mappings, configuration, additionalParameter);
                mpBoundSql.sql(model.getDialectSql());
                mpBoundSql.parameterMappings(mappings);
            }
        });
        return interceptor;
    }

}
