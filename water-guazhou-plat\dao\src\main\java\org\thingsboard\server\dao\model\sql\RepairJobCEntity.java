package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.DTO.ComponentOptionDTO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.REPAIR_JOB_C_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class RepairJobCEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.REPAIR_JOB_C_MAIN_ID)
    private String mainId;

    @Column(name = ModelConstants.REPAIR_JOB_C_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.REPAIR_JOB_C_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.REPAIR_JOB_C_START_TIME)
    private Date startTime;

    @Column(name = ModelConstants.REPAIR_JOB_C_END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.REPAIR_JOB_C_STATUS)
    private String status;

    @Column(name = ModelConstants.REPAIR_JOB_C_STANDARD_NAME)
    private String standardName;

    @Column(name = ModelConstants.REPAIR_JOB_C_STANDARD_DETAIL)
    private String standardDetail;

    @Column(name = ModelConstants.REPAIR_JOB_C_IMGS)
    private String imgs;

    @Column(name = ModelConstants.REPAIR_JOB_C_VOICE_FILE)
    private String voiceFile;

    @Column(name = ModelConstants.REPAIR_JOB_C_REMARK)
    private String remark;

    @Column(name = ModelConstants.REPAIR_JOB_C_PROCESS_REMARK)
    private String processRemark;

    @Column(name = ModelConstants.REPAIR_JOB_C_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private String projectName;

    @Transient
    private String deviceName;

    @Transient
    private List<ComponentOptionDTO> componentOptions;

}
