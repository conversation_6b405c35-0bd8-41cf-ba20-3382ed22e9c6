package org.thingsboard.server.dao.sql.gis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.GisLabelListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisLabel;

@Mapper
public interface GisLabelMapper extends BaseMapper<GisLabel> {
    IPage<GisLabel> findList(IPage<GisLabel> pageRequest, @Param("param") GisLabelListRequest request);
}
