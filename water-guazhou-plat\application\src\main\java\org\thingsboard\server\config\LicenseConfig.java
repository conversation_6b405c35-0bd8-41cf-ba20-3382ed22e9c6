package org.thingsboard.server.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;


//@Configuration
public class LicenseConfig/* extends WebMvcConfigurerAdapter*/ {

/*    @Value("${license_path}")
    private String LICENSE_PATH;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //注册TestInterceptor拦截器
        InterceptorRegistration registration = registry.addInterceptor(new LicenseInterceptor(LICENSE_PATH));
        registration.addPathPatterns("/**");                      //所有路径都被拦截
    }*/
}