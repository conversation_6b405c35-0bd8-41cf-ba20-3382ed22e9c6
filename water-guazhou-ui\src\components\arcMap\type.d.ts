/**
 * 弹窗配置属性
 */
interface IArcInfoWindowConfig {
  x?: number
  y?: number
  /**
   * 传入模板的props
   */
  attributes?: IArcInfoWindowTempProps
}
/**
 * 弹窗
 */
type TArcInfoWindow = {
  id: string
  domObj: VNode<
    RendererNode,
    RendererElement,
    {
      [key: string]: any
    }
  >
  x: number
  y: number
}
interface IArcWindow {
  /**
   * 当前所有弹窗
   */
  getAllInfoWindows: () => TArcInfoWindow[]
  /**
   * 添加弹窗，如果存在同id的弹窗则刷新该弹窗
   */
  addInfoWindow: (
    mapView: MapView,
    config: IArcInfoWindowConfig
  ) => TArcInfoWindow | undefined
  /**
   * 查找指定id的弹窗
   */
  getInfoWindow: (id: string) => TArcInfoWindow | undefined
  /**
   * 清除所有弹窗
   */
  clearInfoWindow: () => void
  closeInfoWindow: (id: string) => void
  /**
   * 设置弹窗的位置
   */
  setInfoWindowPosition: (mapView: MapView, infoObj: TArcInfoWindow) => void
  resetMultiInfoWindowPosition: (
    mapView?: MapView,
    infoObjs?: TArcInfoWindow[]
  ) => void
  // updateInfoWindow: (id: string, config: IArcInfoWindowConfig) => void
}
interface IMapTools {
  setBarVisible: (id: string, visible?: boolean) => void
  removeBar: (id: string) => void
  toggleBarVisible: (id: string) => void
  initPipeLayer: () => any
  BarIds: Record<string, string>
}
type IStatus =
  | 'warning'
  | 'danger'
  | 'online'
  | 'offline'
  | 'normal'
  | ''
  | undefined
interface IArcMarkerProps {
  showMore?: boolean
  showBack?: boolean
  highLight?: boolean
  bgColor?: string
  status?: IStatus
  title: string
  style?: Record<string, any>
  /** 默认显示 */
  visible?: boolean
  type?: string
  x?: number
  y?: number
  longitude?: number
  latitude?: number
  offsetX?: number
  offsetY?: number
  attributes: {
    row?: any
    id: string
    values?: {
      label?: string
      labelColor?: string
      value?: string | number
      valueColor?: string
      unit?: string
      unitColor?: string
    }[]
    /** 传给自定义组件的props配置 */
    customConfig?: any
    /** 自定义组件 */
    customComponent?: any
  }
  symbolConfig?: {
    /**
     * 图标路径，可以是svg格式的图标
     */
    url: string
    height?: number
    width?: number
    /**
     * 偏移x
     */
    xoffset?: number
    /** 偏移y */
    yoffset?: number
  }
}
interface IArcInfoWindowTempProps {
  /** 指定弹窗的id属性 */
  id?: string
  /**
   * 弹窗标题
   */
  title?: string
  /**
   * 弹窗展示数据列表
   */
  values?: { label: string; value: any; unit?: string }[]
}

interface IArcPopConfig {
  id: string
  visible?: boolean
  showMore?: boolean
  showBack?: boolean
  bgColor?: string
  status?: IStatus
  title: string
  x?: number
  y?: number
  longitude?: number
  latitude?: number
  offsetX?: number
  offsetY?: number
  attributes: Record<string, any>
  /** 自定义组件的props */
  customConfig?: any
  /** 自定义组件 */
  customComponent?: any
  symbolConfig?: {
    /**
     * 图标路径，可以是svg格式的图标
     */
    url: string
    height?: number
    width?: number
    /**
     * 偏移x
     */
    xoffset?: number
    /** 偏移y */
    yoffset?: number
  }
}
/**
 * 绘制操作回调参数
 */
interface ISketchHandlerParameter {
  /**
   * Graphic[]
   */
  graphics: __esri.Graphic[]
  /**
   * for create or update
   *
   * when 'cancel', it's invalid
   */
  state?: 'start' | 'active' | 'complete' | 'cancel'
  /**
   * for create: "point" | "polyline" | "polygon" | "multipoint" | "circle" | "rectangle"
   *
   * for update or update: "move" | "transform" | "reshape"
   *
   * for updo or redo: "point" | "polyline" | "polygon" | "multipoint" | "circle" | "rectangle" | "move" | "transform" | "reshape"
   */
  tool: string
  toolEventInfo?: any
  type: 'create' | 'update' | 'redo' | 'undo' | 'delete'
  aborted?: boolean
}
interface ITDTSearchParams{
  keyWord?: string
  mapBound?: string
  level?: string
  specifyAdminCode?: string
  queryRadius?: string
  pointLonlat?: string
  /**
   * 可选值： 1~7，10
   */
  queryType?: string
  start?: string
  count?: string
  lonlat?: string
  bound?: string
  zoom?: string
  layers?: string
  projection?: string
}
