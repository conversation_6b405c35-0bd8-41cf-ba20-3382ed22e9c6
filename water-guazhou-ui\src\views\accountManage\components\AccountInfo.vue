<template>
  <Form ref="refForm" :config="FormConfig"></Form>
</template>
<script lang="ts" setup>
import { useClipboard } from '@vueuse/core';
import { saveUser } from '@/api/user';
import { useUserStore } from '@/store';
import { SLConfirm, SLMessage } from '@/utils/Message';
import ChangePassword from './changePassword.vue';

const userStore = useUserStore();
const validatePhone = (rule, value, callback) => {
  // const valid = /(13\d|14[579]|15[^4\D]|17[^49\D]|18\d)\d{8}/g
  const valid = /^1\d{10}$/;
  if (valid.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确手机号'));
  }
};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        { type: 'input', label: '电子邮件', field: 'email', readonly: true },
        { type: 'input', label: '账号', field: 'name', readonly: true },
        {
          hidden: userStore.user?.authority !== 'TENANT_ADMIN',
          type: 'switch',
          label: '邮箱接收告警',
          activeColor: '#3E8EF7',
          field: 'alarmFromEmail',
          aInfo: true
        },
        {
          readonly: userStore.user?.authority === 'SYS_ADMIN',
          type: 'input',
          label: '联系手机',
          field: 'phone',
          rules: [
            { required: true, message: '请输入联系手机', trigger: 'blur' },
            { validator: validatePhone, trigger: 'blur' }
          ]
        },
        {
          type: 'switch',
          label: '手机接收告警',
          aInfo: true,
          field: 'alarmFromSms',
          activeColor: '#3E8EF7',
          onChange: () => alarmSwitch()
        },
        {
          type: 'input',
          label: '昵称',
          field: 'lastName',
          disabled: userStore.user?.authority === 'SYS_ADMIN',
          rules: [
            // { validator: validateName, trigger: 'blur' },
            { max: 16, message: '昵称输入不可超过16位', trigger: 'blur' }
          ]
        }
      ]
    },
    {
      fields: [
        {
          type: 'component',
          component: shallowRef(ChangePassword)
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '保存',
              loading: (): boolean => !!FormConfig.submitting,
              click: () => {
                refForm.value?.Submit();
              }
            },
            {
              perm: true,
              type: 'default',
              text: '复制API Token',
              click: () => {
                copyToken();
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '150px',
  defaultValue: {
    ...(userStore.user || {})
  },
  submit: (params: any) => {
    SLConfirm('确定提交', '提示信息')
      .then(async () => {
        try {
          FormConfig.submitting = true;
          const additionalInfo =
            typeof params.additionalInfo === 'string'
              ? JSON.parse(params.additionalInfo)
              : params.additionalInfo;
          const submitParams = {
            ...params,
            lastName: (params.lastName ?? '').trim(),
            additionalInfo: {
              ...(additionalInfo || {})
            }
          };
          await saveUser(submitParams);
          refreshAcoundForm();
          SLMessage.success('修改成功');
        } catch (error) {
          SLMessage.error('系统错误');
        }

        FormConfig.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const refForm = ref<IFormIns>();
const alarmSwitch = () => {
  if (!refForm.value?.dataForm) return;
  if (refForm.value.dataForm.alarmFromSms) {
    if (refForm.value.dataForm.phone === '') {
      SLMessage.error('打开手机接收告警，必须先填写手机号');
      refForm.value.dataForm.alarmFromSms = false;
    }
  }
};
const refreshAcoundForm = async () => {
  try {
    await userStore.GetInfo();
  } catch (error) {
    //
  }
  const additionalInfo = userStore.user?.additionalInfo || {};
  FormConfig.defaultValue = {
    ...(userStore.user || {}),
    alarmFromEmail: additionalInfo.alarmFromEmail,
    alarmFromSms: additionalInfo.alarmFromSms
  };
  refForm.value?.resetForm();
};
const source = ref<string>(userStore.token || '');
const { copy } = useClipboard({ source });
const copyToken = () => {
  copy(source.value).then(() => {
    SLMessage.success('已复制到剪切板');
  });
};
onMounted(() => {
  refreshAcoundForm();
});
onActivated(() => {
  console.log('active');
});
</script>
<style lang="scss" scoped></style>
