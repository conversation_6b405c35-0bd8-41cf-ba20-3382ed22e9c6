import{d as r,c as p,r as d,g as c,n as m,q as s,i as t,t as u,_ as f,C as _}from"./index-r0dFAfgr.js";import{C as h}from"./index-CcDafpIP.js";import{r as a}from"./chart-wy3NEK2T.js";const g={class:"onemap-panel-wrapper"},v=r({__name:"waterVolume",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(y){const e=p([{label:"0 户",value:"用户数"},{label:"0 m³",value:"用水量"}]),l=d({labelPosition:"top",group:[{fieldset:{type:"underline",desc:"用水量占比"},fields:[{type:"vchart",option:a(),style:{width:"100%",height:"150px"}}]},{fieldset:{type:"underline",desc:"用水性质占比"},fields:[{type:"vchart",option:a(),style:{width:"100%",height:"150px"}}]},{fieldset:{type:"underline",desc:"各营业所水量占比"},fields:[{type:"vchart",option:a(),style:{width:"100%",height:"150px"}}]}]});return(w,o)=>{const n=f;return c(),m("div",g,[s(t(h),{modelValue:t(e),"onUpdate:modelValue":o[0]||(o[0]=i=>u(e)?e.value=i:null),span:12},null,8,["modelValue"]),s(n,{config:t(l)},null,8,["config"])])}}}),b=_(v,[["__scopeId","data-v-8b6a81c9"]]);export{b as default};
