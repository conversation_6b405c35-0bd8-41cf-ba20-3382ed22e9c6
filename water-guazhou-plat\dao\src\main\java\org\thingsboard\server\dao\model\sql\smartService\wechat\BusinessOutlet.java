package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("business_outlet")
public class BusinessOutlet {
    // id
    private String id;

    // 图片
    private String image;

    // 名称
    private String name;

    // 联系电话
    private String phone;

    // 经度
    private String lat;

    // 纬度
    private String lon;

    // 地址
    private String address;

    // 上班时间
    private String beginTime;

    // 下班时间
    private String endTime;

    // 备注
    private String remark;

    // 客户id
    private String tenantId;

}
