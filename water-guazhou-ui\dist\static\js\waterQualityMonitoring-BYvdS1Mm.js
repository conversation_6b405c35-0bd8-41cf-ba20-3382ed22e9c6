import{m as a}from"./index-r0dFAfgr.js";const o=t=>a({method:"get",url:"/istar/api/waterQualityStation/getPointMonitor",params:t}),i=t=>a({method:"get",url:"//istar/api/waterQualityStation/getInfoDetail",params:t}),r=t=>a({method:"get",url:"/istar/api/waterQualityStation/getReport",params:t}),n=t=>a({method:"get",url:"/istar/api/waterQualityStation/getMaxAndMinReport",params:t}),s=t=>a({method:"get",url:"//istar/api/waterQualityStation/getReport/export",params:t,responseType:"blob"});export{r as a,i as b,o as c,s as e,n as g};
