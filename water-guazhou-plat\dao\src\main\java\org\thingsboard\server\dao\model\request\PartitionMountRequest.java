package org.thingsboard.server.dao.model.request;

import lombok.Data;

@Data
public class PartitionMountRequest {

    private int page;

    private int size;

    private String type;

    private String partitionId;

    private String findDate;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    private String tenantId;

    private String meterType;

    private String brand;

    private String caliber;

}
