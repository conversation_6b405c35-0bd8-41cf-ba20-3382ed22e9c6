package org.thingsboard.server.dao.sql.smartManagement.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.*;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.StatisticLong;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskWorkOrderPitfallPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskWorkOrderStatisticUserRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskWorkOrderTrendRequest;

import java.util.List;

@Mapper
public interface SMCircuitTaskMapper extends BaseMapper<SMCircuitTask> {
    IPage<SMCircuitTaskResponse> findByPage(SMCircuitTaskPageRequest request);

    boolean update(SMCircuitTask entity);

    int saveAll(List<SMCircuitTask> items);

    // int updateAll(List<SMCircuitTask> items);


    boolean assign(SMCircuitTaskAssignRequest req);

    int assignBatch(SMCircuitTaskBatchAssignRequest req);

    boolean relateWorkOrder(@Param("taskCode") String taskCode, @Param("workOrderSerialNo") String workOrderSerialNo, @Param("pointId") String pointId, @Param("tenantId") String tenantId);

    IPage<SMCircuitTaskWorkOrderPitfallResponse> findPitfallWorkorderInfo(SMCircuitTaskWorkOrderPitfallPageRequest request);

    List<StatisticLong> workOrderCountByTypeTimed(SMCircuitTaskWorkOrderTrendRequest req);

    List<StatisticLong> workOrderCountByUserTimed(SMCircuitTaskWorkOrderStatisticUserRequest req);

    boolean complete(SMCircuitTaskCompleteRequest req);

    List<String> getBatchCode(List<String> idList);

    Integer totalStatusOfUser(@Param("userId") String userId, @Param("status") List<GeneralTaskStatus> status);

    Integer totalOfUser(@Param("userId") String userId);

    String getReportId(@Param("code") String code, @Param("pointId") String pointId, @Param("tenantId") String tenantId);

    SMCircuitTaskWorkOrderPitfallResponse findPitfallWorkorderInfoByPointId(@Param("taskCode") String taskCode, @Param("pointId") String pointId, @Param("tenantId") String tenantId);

    SMCircuitTaskResponse findById(String id);

    boolean isComplete(@Param("code") String code, @Param("tenantId") String tenantId);

    boolean canBeComplete(String id);

    Integer countGisUser(@Param("type") DataConstants.PEOPLE_TYPE type, @Param("tenantId") String tenantId);

    Integer countOutsideCircuitUser(@Param("tenantId") String tenantId);

    Integer countCustomerCount(String tenantId);

    int auditBatch(SMCircuitTaskBatchAuditRequest req);

    int completeTotal();
}
