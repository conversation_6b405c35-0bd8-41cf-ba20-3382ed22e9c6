package org.thingsboard.server.dao.sql.revenue;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.revenue.SysCodeDetail;

import java.util.List;

public interface SysCodeDetailRepository extends JpaRepository<SysCodeDetail, String> {
    List<SysCodeDetail> findByCode(String mainCode);

    List<SysCodeDetail> findByCodeInAndTenantIdOrderByOrderNumber(String[] mainCodeArr, String tenantId);

    SysCodeDetail findOneByKeyAndCodeAndTenantId(String key, String expandingType, String tenantId);

    List<SysCodeDetail> findByCodeInOrderByOrderNumber(String[] mainCodeArr);

    @Query("SELECT s FROM SysCodeDetail s " +
            "WHERE s.code = ?1 AND s.name LIKE %?2% AND s.tenantId = ?3")
    Page<SysCodeDetail> findOptions(String mainCode, String name, String tenantId, Pageable pageable);
}
