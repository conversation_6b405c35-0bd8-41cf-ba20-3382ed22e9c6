package org.thingsboard.server.dao.componentStorage;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ComponentOptionEntity;

public interface ComponentOptionService {
    PageData<ComponentOptionEntity> findList(int page, int size, String code, TenantId tenantId);

    void saveOption(ComponentOptionEntity entity, User currentUser) throws ThingsboardException;

    ComponentOptionEntity getDetail(String id) throws ThingsboardException;
}
