package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionTaskInfo;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionTaskInfoMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoSaveRequest;

@Service
public class SoConstructionTaskInfoServiceImpl implements SoConstructionTaskInfoService {
    @Autowired
    private SoConstructionTaskInfoMapper mapper;

    @Override
    public IPage<SoConstructionTaskInfo> findAllConditional(SoConstructionTaskInfoPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionTaskInfo save(SoConstructionTaskInfoSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionTaskInfo entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean markAsComplete(String id, SoGeneralSystemScope scope) {
        String tableName = scope.getTaskInfoTable().getTableName();
        return mapper.markAsComplete(tableName, id, scope);
    }

    @Override
    public boolean markAsComplete(String constructionCode, String tenantId, SoGeneralSystemScope scope) {
        return mapper.markAsCompleteDirect(constructionCode, tenantId, scope);
    }

    @Override
    public boolean isComplete(String id, SoGeneralSystemScope scope) {
        String tableName = scope.getTaskInfoTable().getTableName();
        return mapper.isComplete(tableName, id, scope);
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId, SoGeneralSystemScope scope) {
        String tableName = scope.getTaskInfoTable().getTableName();
        return mapper.isCompleteDirect(tableName, constructionCode, tenantId, scope);
    }

}
