import{w as U,b as L,e as V,y as v,a as z}from"./Point-WxyopZva.js";import{U as F,h as W,v as $,A as H}from"./pe-B8dP0-Ut.js";import{bM as k,i as I,bk as Y,w as q,bL as K,b$ as J}from"./MapView-DaoQedLH.js";import{aX as X,aW as S,R as D,T as Q}from"./index-r0dFAfgr.js";import{l as Z}from"./widget-BcWKanF2.js";import{b as M,g as tt,d as et}from"./kmlUtils-D7BkQXVZ.js";import{T as it,S as at,R as st}from"./Bitmap-CraE42_6.js";import{a as rt}from"./BitmapContainer-ziwQ7v9F.js";import{f as ot,u as nt}from"./LayerView-BSt9B8Gh.js";import{i as R}from"./GraphicContainer-C86a5RZy.js";import{a as P}from"./GraphicsView2D-DDTEO9AX.js";import{C as lt,$ as pt}from"./rasterProjectionHelper-BvgFmUDx.js";import{n as ht}from"./WGLContainer-Dyx9110G.js";import{I as mt,o as ct}from"./RenderingContext-BRwiSTOs.js";import{P as T,G as E,D as j,L as G,Y as dt,V as gt,f as N}from"./enums-BDQrMlcz.js";import{x as ut}from"./FramebufferObject-8j9PRuxE.js";import{l as _t}from"./rasterUtils-Bn8ImO52.js";import{E as B}from"./Texture-BYqObwfn.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-L38xj_2E.js";import"./BaseGraphicContainer-Cqw9Xlck.js";import"./FeatureContainer-B5oUlI2-.js";import"./AttributeStoreView-B0-phoCE.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./number-CoJp78Rz.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./TileContainer-CC8_A7ZF.js";import"./utils-DPUVnAXL.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./vec3f32-nZdmKIgz.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./normalizeUtilsSync-NMksarRY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./Matcher-v9ErZwmD.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./schemaUtils-DLXXqxNF.js";import"./util-DPgA-H2V.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";import"./vec4f32-CjrfB-0a.js";import"./ProgramTemplate-tdUBoAol.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./webglDeps-BRs4ClOS.js";import"./NestedMap-DgiGbX8E.js";import"./OrderIndependentTransparency-C5Ap76ew.js";import"./basicInterfaces-Dc_Mm1a-.js";import"./doublePrecisionUtils-B0owpBza.js";import"./webgl-debug-BJuvLAW9.js";class h{constructor(t){if(this._ownsRctx=!1,t)this._ownsRctx=!1,this._rctx=t;else{if(h._instance)return h._instanceRefCount++,h._instance;h._instanceRefCount=1,h._instance=this,this._ownsRctx=!0;const i=document.createElement("canvas").getContext("webgl");i.getExtension("OES_texture_float"),this._rctx=new mt(i,{})}const s={applyProjection:!0,bilinear:!1,bicubic:!1},r=ct("raster/reproject","raster/reproject",new Map([["a_position",0]]),s);this._program=this._rctx.programCache.acquire(r.shaders.vertexShader,r.shaders.fragmentShader,r.attributes),this._rctx.useProgram(this._program),this._program.setUniform1f("u_opacity",1),this._program.setUniform1i("u_image",0),this._program.setUniform1i("u_flipY",0),this._program.setUniform1i("u_transformGrid",1),this._quad=new ht(this._rctx,[0,0,1,0,0,1,1,1])}reprojectTexture(t,s,r=!1){const i=k(t.extent,s),a=new U({x:(t.extent.xmax-t.extent.xmin)/t.texture.descriptor.width,y:(t.extent.ymax-t.extent.ymin)/t.texture.descriptor.height,spatialReference:t.extent.spatialReference}),{x:o,y:l}=lt(a,s,t.extent);let p=(o+l)/2;const n=Math.round((i.xmax-i.xmin)/p),d=Math.round((i.ymax-i.ymin)/p);p=(i.width/n+i.height/d)/2;const C=new U({x:p,y:p,spatialReference:i.spatialReference}),w=pt({projectedExtent:i,srcBufferExtent:t.extent,pixelSize:C,hasWrapAround:!0,spacing:[16,16]}),x=_t(this._rctx,w),u=new B(this._rctx,{width:n,height:d,pixelFormat:T.RGBA,dataType:E.UNSIGNED_BYTE,wrapMode:j.CLAMP_TO_EDGE,samplingMode:G.LINEAR,hasMipmap:!1}),m=new ut(this._rctx,{colorTarget:dt.TEXTURE,depthStencilTarget:gt.NONE,width:n,height:d},u);this._rctx.bindFramebuffer(m),this._rctx.setViewport(0,0,n,d),this._rctx.useProgram(this._program),this._rctx.bindTexture(t.texture,0),this._rctx.bindTexture(x,1),this._quad.bind();const{width:y=0,height:f=0}=t.texture.descriptor;if(this._program.setUniform2f("u_srcImageSize",y,f),this._program.setUniform2fv("u_transformSpacing",w.spacing),this._program.setUniform2fv("u_transformGridSize",w.size),this._program.setUniform2f("u_targetImageSize",n,d),this._quad.draw(),this._quad.unbind(),this._rctx.useProgram(null),this._rctx.bindFramebuffer(null),x.dispose(),r){const{width:_=0,height:c=0}=m.descriptor,b=new ImageData(_,c);return m.readPixels(0,0,_,c,T.RGBA,E.UNSIGNED_BYTE,b.data),m.detachColorTexture(N.COLOR_ATTACHMENT0),m.dispose(),{texture:u,extent:i,imageData:b}}return m.detachColorTexture(N.COLOR_ATTACHMENT0),m.dispose(),{texture:u,extent:i}}reprojectBitmapData(t,s){const r=it(t.bitmapData)?at(t.bitmapData):t.bitmapData,i=new B(this._rctx,{width:t.bitmapData.width,height:t.bitmapData.height,pixelFormat:T.RGBA,dataType:E.UNSIGNED_BYTE,wrapMode:j.CLAMP_TO_EDGE,samplingMode:G.LINEAR,hasMipmap:!1},r),a=this.reprojectTexture({texture:i,extent:t.extent},s,!0);a.texture.dispose();const o=document.createElement("canvas"),l=a.imageData;return o.width=l.width,o.height=l.height,o.getContext("2d").putImageData(l,0,0),{bitmapData:o,extent:a.extent}}async loadAndReprojectBitmapData(t,s,r){const i=(await F(t,{responseType:"image"})).data,a=document.createElement("canvas");a.width=i.width,a.height=i.height;const o=a.getContext("2d");o.drawImage(i,0,0);const l=o.getImageData(0,0,a.width,a.height);if(s.spatialReference.equals(r))return{bitmapData:l,extent:s};const p=this.reprojectBitmapData({bitmapData:l,extent:s},r);return{bitmapData:p.bitmapData,extent:p.extent}}destroy(){this._ownsRctx?(h._instanceRefCount--,h._instanceRefCount===0&&(this._quad.dispose(),this._program.dispose(),this._rctx.dispose(),h._instance=null)):(this._quad.dispose(),this._program.dispose())}}h._instanceRefCount=0;class O{constructor(){this.allSublayers=new Map,this.allPoints=[],this.allPolylines=[],this.allPolygons=[],this.allMapImages=[]}}let g=class extends ot(nt){constructor(){super(...arguments),this._bitmapIndex=new Map,this._mapImageContainer=new rt,this._kmlVisualData=new O,this._fetchController=null,this.allVisiblePoints=new I,this.allVisiblePolylines=new I,this.allVisiblePolygons=new I,this.allVisibleMapImages=new Y}async hitTest(e,t){var r,i,a;const s=this.layer;return[(r=this._pointsView)==null?void 0:r.hitTest(e),(i=this._polylinesView)==null?void 0:i.hitTest(e),(a=this._polygonsView)==null?void 0:a.hitTest(e)].flat().filter(Boolean).map(o=>(o.layer=s,o.sourceLayer=s,{type:"graphic",graphic:o,layer:s,mapPoint:e}))}update(e){this._polygonsView&&this._polygonsView.processUpdate(e),this._polylinesView&&this._polylinesView.processUpdate(e),this._pointsView&&this._pointsView.processUpdate(e)}attach(){this._fetchController=new AbortController,this.container.addChild(this._mapImageContainer),this._polygonsView=new P({view:this.view,graphics:this.allVisiblePolygons,requestUpdateCallback:()=>this.requestUpdate(),container:new R(this.view.featuresTilingScheme)}),this.container.addChild(this._polygonsView.container),this._polylinesView=new P({view:this.view,graphics:this.allVisiblePolylines,requestUpdateCallback:()=>this.requestUpdate(),container:new R(this.view.featuresTilingScheme)}),this.container.addChild(this._polylinesView.container),this._pointsView=new P({view:this.view,graphics:this.allVisiblePoints,requestUpdateCallback:()=>this.requestUpdate(),container:new R(this.view.featuresTilingScheme)}),this.container.addChild(this._pointsView.container),this.addAttachHandles([this.allVisibleMapImages.on("change",e=>{e.added.forEach(t=>this._addMapImage(t)),e.removed.forEach(t=>this._removeMapImage(t))}),Z(()=>this.layer.visibleSublayers,e=>{for(const[t,s]of this._kmlVisualData.allSublayers)s.visibility=0;for(const t of e){const s=this._kmlVisualData.allSublayers.get(t.id);s&&(s.visibility=1)}this._refreshCollections()})]),this.updatingHandles.addPromise(this._fetchService(this._fetchController.signal)),this._imageReprojector=new h}detach(){this._fetchController=X(this._fetchController),this._mapImageContainer.removeAllChildren(),this.container.removeAllChildren(),this._bitmapIndex.clear(),this._polygonsView=S(this._polygonsView),this._polylinesView=S(this._polylinesView),this._pointsView=S(this._pointsView),this._imageReprojector=S(this._imageReprojector)}moveStart(){}viewChange(){this._polygonsView.viewChange(),this._polylinesView.viewChange(),this._pointsView.viewChange()}moveEnd(){}isUpdating(){return this._pointsView.updating||this._polygonsView.updating||this._polylinesView.updating}_addMapImage(e){var t,s;((t=this.view.spatialReference)!=null&&t.isWGS84||(s=this.view.spatialReference)!=null&&s.isWebMercator)&&this._imageReprojector.loadAndReprojectBitmapData(e.href,q.fromJSON(e.extent),this.view.spatialReference).then(r=>{const i=new st(r.bitmapData,{immutable:!1,requestRenderOnSourceChangedEnabled:!0});i.x=r.extent.xmin,i.y=r.extent.ymax,i.resolution=r.extent.width/r.bitmapData.width,i.rotation=e.rotation,this._mapImageContainer.addChild(i),this._bitmapIndex.set(e,i)})}async _getViewDependentUrl(e,t){const{viewFormat:s,viewBoundScale:r,httpQuery:i}=e;if(D(s)){if(Q(t))throw new Error("Loading this network link requires a view state.");let a;if(await K(),D(r)&&r!==1){const c=new q(t.extent);c.expand(r),a=c}else a=t.extent;a=k(a,L.WGS84);const o=k(a,L.WebMercator),l=a.xmin,p=a.xmax,n=a.ymin,d=a.ymax,C=t.size[0]*t.pixelRatio,w=t.size[1]*t.pixelRatio,x=Math.max(o.width,o.height),u={"[bboxWest]":l.toString(),"[bboxEast]":p.toString(),"[bboxSouth]":n.toString(),"[bboxNorth]":d.toString(),"[lookatLon]":a.center.x.toString(),"[lookatLat]":a.center.y.toString(),"[lookatRange]":x.toString(),"[lookatTilt]":"0","[lookatHeading]":t.rotation.toString(),"[lookatTerrainLon]":a.center.x.toString(),"[lookatTerrainLat]":a.center.y.toString(),"[lookatTerrainAlt]":"0","[cameraLon]":a.center.x.toString(),"[cameraLat]":a.center.y.toString(),"[cameraAlt]":x.toString(),"[horizFov]":"60","[vertFov]":"60","[horizPixels]":C.toString(),"[vertPixels]":w.toString(),"[terrainEnabled]":"0","[clientVersion]":W,"[kmlVersion]":"2.2","[clientName]":"ArcGIS API for JavaScript","[language]":"en-US"},m=c=>{for(const b in c)for(const A in u)c[b]=c[b].replace(A,u[A])},y=$(s);m(y);let f={};D(i)&&(f=$(i),m(f));const _=J(e.href);return _.query={..._.query,...y,...f},`${_.path}?${H(y)}`}return e.href}async _fetchService(e){const t=new O;await this._loadVisualData(this.layer.url,t,e),this._kmlVisualData=t,this._refreshCollections()}_refreshCollections(){this.allVisiblePoints.removeAll(),this.allVisiblePolylines.removeAll(),this.allVisiblePolygons.removeAll(),this.allVisibleMapImages.removeAll(),this.allVisiblePoints.addMany(this._kmlVisualData.allPoints.filter(e=>this._isSublayerVisible(e.sublayerId)).map(({item:e})=>e)),this.allVisiblePolylines.addMany(this._kmlVisualData.allPolylines.filter(e=>this._isSublayerVisible(e.sublayerId)).map(({item:e})=>e)),this.allVisiblePolygons.addMany(this._kmlVisualData.allPolygons.filter(e=>this._isSublayerVisible(e.sublayerId)).map(({item:e})=>e)),this.allVisibleMapImages.addMany(this._kmlVisualData.allMapImages.filter(e=>this._isSublayerVisible(e.sublayerId)).map(({item:e})=>e))}_isSublayerVisible(e){const t=this._kmlVisualData.allSublayers.get(e);return!!(t!=null&&t.visibility)&&(t.parentFolderId===-1||this._isSublayerVisible(t.parentFolderId))}_loadVisualData(e,t,s){return this._fetchParsedKML(e,s).then(async r=>{for(const i of r.sublayers){t.allSublayers.set(i.id,i);const a=i.points?await M(i.points):[],o=i.polylines?await M(i.polylines):[],l=i.polygons?await M(i.polygons):[],p=i.mapImages||[];if(t.allPoints.push(...a.map(n=>({item:n,sublayerId:i.id}))),t.allPolylines.push(...o.map(n=>({item:n,sublayerId:i.id}))),t.allPolygons.push(...l.map(n=>({item:n,sublayerId:i.id}))),t.allMapImages.push(...p.map(n=>({item:n,sublayerId:i.id}))),i.networkLink){const n=await this._getViewDependentUrl(i.networkLink,this.view.state);await this._loadVisualData(n,t,s)}}})}_fetchParsedKML(e,t){return tt(e,this.layer.spatialReference,this.layer.refreshInterval,t).then(s=>et(s.data))}_removeMapImage(e){const t=this._bitmapIndex.get(e);t&&(this._mapImageContainer.removeChild(t),this._bitmapIndex.delete(e))}};V([v()],g.prototype,"_pointsView",void 0),V([v()],g.prototype,"_polylinesView",void 0),V([v()],g.prototype,"_polygonsView",void 0),V([v()],g.prototype,"updating",void 0),g=V([z("esri.views.2d.layers.KMLLayerView2D")],g);const ke=g;export{ke as default};
