package org.thingsboard.server.dao.smartPipe;

import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionCust;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface PipePartitionCustService {

    PageData<PipePartitionCust> getList(PartitionCustRequest partitionCustRequest);

    PipePartitionCust save(PipePartitionCust pipePartitionCust);

    void delete(List<String> idList);

    IstarResponse importSave(MultipartFile file, String tenantId);

    /**
     * 检查用户是否存在或重复添加
     *
     * @param pipePartitionCust
     * @return
     */
    String check(PipePartitionCust pipePartitionCust);

    /**
     * 批量保存
     * @param pipePartitionCustList
     * @param tenantId
     * @return
     */
    String batchSave(List<PipePartitionCust> pipePartitionCustList, String tenantId);

    /**
     * 批量取消分区挂接
     * @param idList
     */
    void batchRemove(List<String> idList);
}
