/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.msgLog;

import org.thingsboard.server.dao.model.sql.DeviceLogEntity;

import java.util.List;

public interface DeviceLogDao {
    DeviceLogEntity save(DeviceLogEntity deviceLogEntity);

    List<DeviceLogEntity> findByTenant(String tenantId);

    List<DeviceLogEntity> findByTenantAndTime(String tenantId, long start, long end);

    List<DeviceLogEntity> findByProject(String projectId);

    List<DeviceLogEntity> findByProject(String projectId, Long start, Long end);

    void deleteByDeviceId(String deviceId);
}
