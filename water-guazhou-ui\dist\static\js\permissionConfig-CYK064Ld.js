import{_ as S}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as B}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{z as h,C as M,c as A,a8 as F,r as _,b as f,S as V,o as $,g as q,n as E,q as C}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function O(g){return h({url:"/api/base/permission/configuration/list",method:"get",params:g})}function R(g){return h({url:"/api/base/permission/configuration/getDetail",method:"get",params:{id:g}})}function k(g){return h({url:"/api/base/permission/configuration/add",method:"post",data:g})}function P(g){return h({url:"/api/base/permission/configuration/edit",method:"post",data:g})}function z(g){return h({url:"/api/base/permission/configuration/deleteIds",method:"delete",data:g})}function W(){return h({url:"/api/role/roles",method:"get"})}const G={class:"wrapper"},H={__name:"permissionConfig",setup(g){const x=A(),m=A(),p=A([]),b=F(()=>{const e=new Map;return p.value.forEach(o=>{e.set(o.value,o.label)}),console.log("角色映射表:",e),e}),T=_({labelWidth:"100px",filters:[{type:"input",label:"底图配置ID",field:"mapConfigId",placeholder:"请输入底图配置ID",onChange:()=>y()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>y()},{perm:!0,type:"primary",text:"新增",click:()=>L()},{perm:!0,type:"danger",text:"批量删除",click:()=>I()}]}],defaultParams:{}}),s=_({columns:[{label:"底图配置ID",prop:"mapConfigId"},{label:"角色",prop:"rolesDisplay",minWidth:200},{label:"备注",prop:"remark",showOverflowTooltip:!0}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>w(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>L(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>I(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{s.pagination.page=e,y()},handleSize:e=>{s.pagination.limit=e,y()}},handleSelectChange:e=>{s.selectList=e||[]}}),a=_({title:"新增权限配置",group:[{fields:[{type:"input",label:"底图配置ID",field:"mapConfigId",rules:[{required:!0,message:"请输入底图配置ID"}]},{type:"select",label:"角色IDs",field:"roles",placeholder:"请选择角色",multiple:!0,clearable:!0,filterable:!0,get options(){return p.value},rules:[{required:!0,message:"请选择角色"}]},{type:"textarea",label:"备注",field:"remark",placeholder:"请输入备注信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var o;try{console.log("提交的原始参数:",e),console.log("roles字段类型:",typeof e.roles),console.log("roles字段值:",e.roles);const t={...e};Array.isArray(t.roles)&&(t.roles=t.roles.join(",")),console.log("转换后的提交参数:",t),console.log("转换后roles字段:",t.roles),e.id?(await P(t),f.success("修改成功")):(await k(t),f.success("新增成功")),(o=m.value)==null||o.closeDialog(),y()}catch(t){console.error("提交失败:",t),f.error("操作失败")}}}),D=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1,e.field==="roles"&&(e.type="select",e.multiple=!0,e.clearable=!0,e.filterable=!0,e.placeholder="请选择角色",(!e.options||typeof e.options!="function")&&Object.defineProperty(e,"options",{get(){return p.value},configurable:!0}))}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var o;try{console.log("提交的原始参数:",e),console.log("roles字段类型:",typeof e.roles),console.log("roles字段值:",e.roles);const t={...e};Array.isArray(t.roles)&&(t.roles=t.roles.join(",")),console.log("转换后的提交参数:",t),console.log("转换后roles字段:",t.roles),e.id?(await P(t),f.success("修改成功")):(await k(t),f.success("新增成功")),(o=m.value)==null||o.closeDialog(),y()}catch(t){console.error("提交失败:",t),f.error("操作失败")}},a.footerBtns=void 0},L=e=>{var t;D();let o={...e||{}};o.roles&&typeof o.roles=="string"&&(o.roles=o.roles.split(",").map(l=>l.trim()).filter(l=>l)),console.log("编辑时的默认值:",o),console.log("roles字段处理结果:",o.roles),a.title=e?"编辑权限配置":"新增权限配置",a.defaultValue=o,(t=m.value)==null||t.openDialog()},w=async e=>{var t,l;const o={id:e.id||"1",mapConfigId:e.mapConfigId||"MAP001",roles:e.roles||"54426900-7b72-11ed-85ef-9fe6e6dd373e,5e356840-7b72-11ed-85ef-9fe6e6dd373e",remark:e.remark||"这是权限配置的详情数据"};try{console.log("获取详情，行数据:",e);const i=await R(e.id);console.log("详情API响应:",i);let n=null;if(i.data?i.data.data?n=i.data.data:n=i.data:i&&(n=i),console.log("解析后的详情数据:",n),n||(console.log("使用模拟详情数据"),n=o),n.roles){if(typeof n.roles=="string"){const d=n.roles.split(",").map(u=>u.trim()).filter(u=>u).map(u=>b.value.get(u)||u).filter(u=>u);n.roles=d.join(", ")}else if(Array.isArray(n.roles)){const c=n.roles.map(d=>b.value.get(d)||d).filter(d=>d);n.roles=c.join(", ")}}D(),a.title="权限配置详情",a.defaultValue={...n},console.log("设置的详情数据:",a.defaultValue);const r=a.group[0].fields.find(c=>c.field==="roles");r&&(r.type="input",r.disabled=!0,r.readonly=!0),a.group[0].fields.forEach(c=>{c.type==="select"&&(c.readonly=!0),c.disabled=!0}),a.showSubmit=!1,a.showCancel=!0,a.cancel=!0,a.cancelText="关闭",a.submitText=void 0,a.submit=void 0,a.submitting=!1,a.footerBtns=[{text:"关闭",type:"default",click:()=>{var c;(c=m.value)==null||c.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:a.showSubmit,showCancel:a.showCancel,cancel:a.cancel,cancelText:a.cancelText,submitText:a.submitText,submit:a.submit,footerBtns:a.footerBtns}),(t=m.value)==null||t.openDialog()}catch(i){if(console.error("获取详情失败:",i),console.log("API调用失败，使用模拟详情数据"),o.roles){if(typeof o.roles=="string"){const c=o.roles.split(",").map(d=>d.trim()).filter(d=>d).map(d=>b.value.get(d)||d).filter(d=>d);o.roles=c.join(", ")}else if(Array.isArray(o.roles)){const r=o.roles.map(c=>b.value.get(c)||c).filter(c=>c);o.roles=r.join(", ")}}D(),a.title="权限配置详情",a.defaultValue={...o};const n=a.group[0].fields.find(r=>r.field==="roles");n&&(n.type="input",n.disabled=!0,n.readonly=!0),a.group[0].fields.forEach(r=>{r.type==="select"&&(r.readonly=!0),r.disabled=!0}),a.showSubmit=!1,a.showCancel=!0,a.cancel=!0,a.cancelText="关闭",a.submitText=void 0,a.submit=void 0,a.submitting=!1,a.footerBtns=[{text:"关闭",type:"default",click:()=>{var r;(r=m.value)==null||r.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:a.showSubmit,showCancel:a.showCancel,cancel:a.cancel,cancelText:a.cancelText,submitText:a.submitText,submit:a.submit,footerBtns:a.footerBtns}),(l=m.value)==null||l.openDialog(),f.error("API调用失败，当前显示模拟数据")}},I=e=>{V("确定删除？","删除提示").then(async()=>{var o;try{const t=e?[e.id]:((o=s.selectList)==null?void 0:o.map(i=>i.id))||[];if(!t.length){f.warning("请选择要删除的数据");return}(await z(t)).data?(f.success("删除成功"),y()):f.error("删除失败")}catch{f.error("删除失败")}}).catch(()=>{})},y=async()=>{var o;const e=[{id:"1",mapConfigId:"MAP001",roles:"54426900-7b72-11ed-85ef-9fe6e6dd373e,5e356840-7b72-11ed-85ef-9fe6e6dd373e",remark:"管理员权限配置"},{id:"2",mapConfigId:"MAP002",roles:"625773a0-7b72-11ed-85ef-9fe6e6dd373e,66b8aae0-7b72-11ed-85ef-9fe6e6dd373e",remark:"普通用户权限配置"}];try{const t=(o=x.value)==null?void 0:o.queryParams;console.log("请求参数:",{page:s.pagination.page,size:s.pagination.limit,...t||{}});const l=await O({page:s.pagination.page,size:s.pagination.limit,...t||{}});if(console.log("API响应数据:",l),l.data?l.data.records?(s.dataList=l.data.records||[],s.pagination.total=l.data.total||0):l.data.data&&l.data.data.records?(s.dataList=l.data.data.records||[],s.pagination.total=l.data.data.total||0):Array.isArray(l.data)?(s.dataList=l.data,s.pagination.total=l.data.length):Array.isArray(l.data.data)?(s.dataList=l.data.data,s.pagination.total=l.data.data.length):(console.warn("未知的数据结构:",l.data),s.dataList=[],s.pagination.total=0):Array.isArray(l)?(s.dataList=l,s.pagination.total=l.length):(console.warn("无法解析的响应格式:",l),s.dataList=[],s.pagination.total=0),console.log("解析后的数据:",s.dataList),console.log("总数:",s.pagination.total),s.dataList=v(s.dataList),console.log("处理后的表格数据:",s.dataList),s.dataList.length===0){console.log("使用模拟数据进行测试");const i=v(e);s.dataList=i,s.pagination.total=e.length}}catch(t){console.error("获取数据失败:",t),console.log("API调用失败，使用模拟数据");const l=v(e);s.dataList=l,s.pagination.total=e.length,f.error("API调用失败，当前显示模拟数据")}},N=async()=>{const e=[{label:"管理员",value:"54426900-7b72-11ed-85ef-9fe6e6dd373e"},{label:"高管层",value:"5e356840-7b72-11ed-85ef-9fe6e6dd373e"},{label:"巡检养护",value:"625773a0-7b72-11ed-85ef-9fe6e6dd373e"},{label:"调度中心",value:"66b8aae0-7b72-11ed-85ef-9fe6e6dd373e"},{label:"客户服务",value:"6a75d9f0-7b72-11ed-85ef-9fe6e6dd373e"},{label:"收费员",value:"6d72c640-7b72-11ed-85ef-9fe6e6dd373e"},{label:"抄表员",value:"7051ca50-7b72-11ed-85ef-9fe6e6dd373e"},{label:"演示账号",value:"8f419d10-ab87-11ed-8712-5bb0dc03abd0"},{label:"甘谷水务",value:"971bc320-bc09-11ed-a461-fb299c9b23b7"},{label:"盐亭水务",value:"6558f810-bc92-11ed-a461-fb299c9b23b7"}];try{const o=await W();console.log("角色API响应:",o);let t=[];o.data?Array.isArray(o.data)?t=o.data:o.data.data&&Array.isArray(o.data.data)?t=o.data.data:o.data.records&&Array.isArray(o.data.records)&&(t=o.data.records):Array.isArray(o)&&(t=o),console.log("原始角色数据:",t);const l=t.map(r=>{var u;const c=((u=r.id)==null?void 0:u.id)||r.id||r.roleId||r.value||r.code,d=r.name||r.roleName||r.label||r.title;return console.log("处理角色项:",{原始数据:r,提取ID:c,提取名称:d}),{label:d,value:c}}).filter(r=>r.label&&r.value),i=[],n=new Set;l.forEach(r=>{n.has(r.value)||(n.add(r.value),i.push(r))}),p.value=i,console.log("解析后的角色选项:",p.value),console.log("角色选项数量:",p.value.length),p.value.length===0&&(console.log("使用模拟角色数据"),p.value=e),s.dataList.length>0&&(console.log("角色选项已更新，重新处理表格数据"),s.dataList=v(s.dataList),console.log("重新处理后的表格数据:",s.dataList))}catch(o){console.error("获取角色数据失败:",o),console.log("API调用失败，使用模拟角色数据"),p.value=e,f.error("获取角色数据失败，当前显示模拟数据"),s.dataList.length>0&&(console.log("使用模拟数据，重新处理表格数据"),s.dataList=v(s.dataList),console.log("重新处理后的表格数据:",s.dataList))}},v=e=>e.map(o=>{const t={...o};if(o.roles){let l=[];if(typeof o.roles=="string"&&o.roles){const i=o.roles.split(",").map(n=>n.trim()).filter(n=>n);console.log("处理行数据角色ID数组:",i),l=i.map(n=>{const r=b.value.get(n);return console.log(`映射角色ID ${n}:`,r||"未找到"),r||n}).filter(n=>n)}else Array.isArray(o.roles)&&(console.log("处理数组格式的角色:",o.roles),l=o.roles.map(i=>{const n=b.value.get(i);return console.log(`映射角色ID ${i}:`,n||"未找到"),n||i}).filter(i=>i));if(console.log("最终角色名称数组:",l),l.length===0)t.rolesDisplay="-";else if(l.length>3){const i=l.slice(0,3);t.rolesDisplay=`🏷️ ${i.join(" | ")} 等 (共${l.length}个)`}else t.rolesDisplay=`🏷️ ${l.join(" | ")}`}else t.rolesDisplay="-";return t});return $(async()=>{await N(),console.log("角色选项加载完成，开始加载表格数据"),console.log("=== 角色映射测试 ==="),console.log("角色选项:",p.value),console.log("角色映射表:",b.value),["54426900-7b72-11ed-85ef-9fe6e6dd373e","5e356840-7b72-11ed-85ef-9fe6e6dd373e"].forEach(o=>{const t=b.value.get(o);console.log(`测试映射 ${o} -> ${t}`)}),y()}),(e,o)=>{const t=j,l=B,i=S;return q(),E("div",G,[C(t,{ref_key:"refSearch",ref:x,config:T},null,8,["config"]),C(l,{class:"card-table",config:s},null,8,["config"]),C(i,{ref_key:"refDialogForm",ref:m,config:a},null,8,["config"])])}}},ee=M(H,[["__scopeId","data-v-b399335b"]]);export{ee as default};
