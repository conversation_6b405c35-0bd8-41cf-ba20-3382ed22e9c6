package org.thingsboard.server.dao.model.sql.smartManagement.district;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_district_area")
public class SMCircuitDistrictArea {
    // id
    @TableId
    private String id;

    // 类型：区域或路线
    private String type;

    // 片区名称
    private String name;

    // 所属片区Id
    private String districtId;

    // 点阵JSON
    private String points;

    // 缓冲距离
    private String buffer;

    // 租户Id
    private String tenantId;

}
