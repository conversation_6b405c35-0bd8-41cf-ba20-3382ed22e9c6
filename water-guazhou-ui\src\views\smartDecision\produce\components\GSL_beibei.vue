<template>
  <div class="gsl-wrapper">
    <div class="supply-items left">
      <SupplyItem
        v-for="(item, i) in state.leftItems"
        :key="i"
        class="supply-item"
        :config="item"
      ></SupplyItem>
    </div>
    <div
      class="supply-items right overlay-y"
      :class="{ 'only-one': state.rightItems.length === 1 }"
    >
      <SupplyItem
        v-for="(item, i) in state.rightItems"
        :key="i"
        class="supply-item"
        :config="item"
      ></SupplyItem>
    </div>
    <div class="center_item total">
      总供水量
    </div>
    <div class="center_item plant">
      水厂供水量
    </div>
    <div class="chart">
      <VChart
        ref="refChart"
        :option="state.centerOption"
      ></VChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { 两层圆环 } from '../charts'
// import { gszbData } from '../../smartDecisionData'
import SupplyItem from './SupplyItem.vue'
import { GetLookBoardCountStatistic } from '@/api/shuiwureports/shuichangzonglan'
import { useBusinessStore } from '@/store'
import { GetWaterPlantMonthFlowTotal } from '@/api/smartDesision'

const state = reactive<{
  leftItems: ITargetItem[]
  rightItems: ITargetItem[]
  centerOption: any
}>({
  leftItems: [
    {
      label: '本月供水量',
      unit: '万m³',
      // status: 'up',
      value: '0.00'
      // scale: '--'
    },
    {
      label: '上月供水量',
      unit: '万m³',
      value: '0.00'
    },
    {
      label: '年累计供水量',
      unit: '万m³',
      value: '0.00'
    }
  ],
  rightItems: [
    // {
    //   label: '一水厂',
    //   unit: '万m³',
    //   status: 'up',
    //   value: '9.95',
    //   scale: '1.9%'
    // },
    // {
    //   label: '二水厂',
    //   unit: '万m³',
    //   status: 'up',
    //   value: '2.64',
    //   scale: '4.1%'
    // },
    // {
    //   label: '三水厂',
    //   unit: '万m³',
    //   value: '813.92',
    //   status: 'down',
    //   scale: '20.9%'
    // }, {
    //   label: '一水厂',
    //   unit: '万m³',
    //   status: 'up',
    //   value: '9.95',
    //   scale: '1.9%'
    // },
    // {
    //   label: '二水厂',
    //   unit: '万m³',
    //   status: 'up',
    //   value: '2.64',
    //   scale: '4.1%'
    // },
    // {
    //   label: '三水厂',
    //   unit: '万m³',
    //   value: '813.92',
    //   status: 'down',
    //   scale: '20.9%'
    // }
  ],
  centerOption: null
})
const refreshLeftItems = () => {
  GetLookBoardCountStatistic({
    stationType: '水厂',
    projectId: useBusinessStore().selectedProject?.value
  })
    .then(res => {
      state.leftItems[0].value = (
        (res.data?.lastMonthTotal || 0) / 10000
      ).toFixed(2)
      state.leftItems[1].value = ((res.data?.monthTotal || 0) / 10000).toFixed(
        2
      )
      state.leftItems[2].value = ((res.data?.total || 0) / 10000).toFixed(2)
    })
    .catch(() => {
      state.leftItems[0].value = 0
      state.leftItems[1].value = 0
      state.leftItems[2].value = 0
    })
  GetWaterPlantMonthFlowTotal().then(res => {
    console.log(res.data?.data)

    state.rightItems = res.data?.data?.map(item => {
      return {
        label: item.stationName,
        unit: '万m³',
        // status: 'up',
        value: ((item.value ?? 0) / 10000).toFixed(2)
        // scale: '1.9%'
      }
    }) || []
    const chartData = state.rightItems.map(item => {
      return {
        name: item.label,
        value: Number(item.value)
      }
    })
    state.centerOption = 两层圆环(chartData)
  })
}
onMounted(() => {
  refreshLeftItems()
})
</script>
<style lang="scss" scoped>
.gsl-wrapper {
  background-image: url('../imgs/smartproduce_center_item.png');
  background-size: 100% 100%;
  background-position: top;
  background-repeat: no-repeat;
  width: 100%;
  height: 260px;
  padding: 20px;
  position: relative;
  .supply-items {
    height: calc(100% - 40px);
    padding: 0;
    position: absolute;
    &.only-one {
      display: flex;
      align-items: center;
    }
    &.left {
      left: 20px;
    }
    &.right {
      right: 20px;
    }
    .supply-item {
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .center_item {
    position: absolute;
    width: 100px;
    height: 40px;
    background-color: #2e2f78;
    line-height: 40px;
    border-radius: 5px;
    text-align: center;
    &.total {
      top: 20px;
      left: 240px;
    }
    &.plant {
      top: 20px;
      right: 240px;
    }
  }
  .chart {
    position: absolute;
    left: 250px;
    top: 80px;
    width: 400px;
    height: 200px;
  }
}
</style>
