import{_ as m}from"./index-BJ-QPYom.js";import{c as u,a1 as y,s as T,al as _,d as h,f0 as v,o as g,bO as S,g as k,n as C,q as b,i as f,aw as I,j,C as q}from"./index-r0dFAfgr.js";import{G as x}from"./zhandian-YaGuQZe6.js";import{i as P,c as D}from"./data-CLo2TII-.js";import"./index-BggOjNGp.js";const w=()=>{const n=u("add"),l=async e=>{var t;const o=await x({page:1,size:999,...e||{}});s.value.data=y(((t=o.data)==null?void 0:t.data)||[],{label:"name",id:"id",value:"id",children:"children"})},s=u({data:[]});return{init:e=>{s.value={data:[],title:"站点列表",titleRight:[{type:"btn-group",label:"",field:"",btns:[{type:"primary",perm:!0,text:"",size:"small",icon:"iconfont icon-jia",styles:{padding:"5px"},iconStyles:{marginRight:0},click:()=>e.nodeClick()}]}],isFilterTree:!0,filterSize:"small",filterClassName:"simple-input",filterIcon:T(_),filterPlaceHolder:"输入关键字搜索",selectFilter:{key:"type",label:"",options:P("all"),search:!0,handleChange:()=>{e.refreshTree()}},queryParams:{type:""},tags:[{field:"type",effect:"light",color:o=>D[o]||"#333",hit:!1}],treeNodeHandleClick:e.nodeClick}},TreeData:s,optType:n,refreshStationTree:l}},z=h({__name:"StationTree",props:{projectId:{}},emits:["add","edit"],setup(n,{expose:l,emit:s}){const c=s,e=u(),o=n,t=w(),p=()=>{var a,r;t.refreshStationTree({type:(r=(a=e.value)==null?void 0:a.queryParams)==null?void 0:r.type,projectId:o.projectId})};return v(()=>{p()}),g(()=>{var a,r,i;(a=e.value)!=null&&a.queryParams&&(e.value.queryParams.type=(i=(r=S.currentRoute.value.query)==null?void 0:r.type)==null?void 0:i.toString()),t.init({refreshTree:p,nodeClick:d=>{t.TreeData.value.currentProject=d,d?(c("edit",d),t.optType.value="edit"):(c("add"),t.optType.value="add")}})}),l({refreshTree:p}),(a,r)=>{const i=m;return k(),C("div",{class:I(["station-tree",{isDark:f(j)().isDark}])},[b(i,{ref_key:"refInnerTree",ref:e,"tree-data":f(t).TreeData.value},null,8,["tree-data"])],2)}}}),G=q(z,[["__scopeId","data-v-4f7cf7eb"]]);export{G as default};
