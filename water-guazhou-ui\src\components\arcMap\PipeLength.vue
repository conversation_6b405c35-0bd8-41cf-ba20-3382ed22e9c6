<template>
  <Form ref="refForm" :config="FormConfig"></Form>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice';
import { PipeStatistics } from '@/api/mapservice/pipe';
import { useSketch } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  getGraphicLayer,
  getSubLayerIds
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  measuring: boolean;
  disabled: boolean;
}>({
  measuring: false,
  disabled: true
});
const staticState: {
  sketch?: __esri.SketchViewModel;
  queryGeometry?: __esri.Geometry;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select',
          clearable: false,
          options: [],
          label: '管线图层',
          field: 'pipeLayer'
        },
        { type: 'text', field: 'pipeLength', label: '管线长度', unit: '米' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新测量',
              type: 'default',
              loading: () => state.measuring,
              disabled: () => state.disabled,
              click: () => startMeasure()
            }
          ]
        }
      ]
    }
  ],
  gutter: 12
});
const { initSketch, destroySketch } = useSketch();

const resolveMeasure = async () => {
  if (!staticState.queryGeometry) return;
  state.measuring = true;

  const pipeLayerId = refForm.value?.dataForm.pipeLayer;
  try {
    const res = await PipeStatistics({
      usertoken: useGisStore().gToken,
      layerids: JSON.stringify(pipeLayerId !== undefined ? [pipeLayerId] : []),
      group_fields: JSON.stringify([]),
      statistic_field: EStatisticField.ShapeLen,
      statistic_type: '2',
      where: '1=1',
      geometry: staticState.queryGeometry,
      f: 'pjson'
    });
    if (res.data.code === 10000) {
      const data = res.data?.result?.rows;
      const params = data.length && data[0].rows?.length && data[0].rows[0];
      refForm.value &&
        (refForm.value.dataForm.pipeLength =
          params && params[EStatisticField.ShapeLen]);
    } else {
      SLMessage.error('统计失败');
      refForm.value && (refForm.value.dataForm.pipeLength = undefined);
    }
  } catch (error) {
    console.dir(error);
  }
  state.measuring = false;
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.queryGeometry = result.graphics[0]?.geometry;
    resolveMeasure();
  }
};
const startMeasure = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.sketch?.create('polygon');
};
const initPipeLayerOption = async () => {
  if (!props.view) return;
  const sublayers = getSubLayerIds(props.view);
  const layersres = await queryLayerClassName(sublayers);
  const layers = layersres.data?.result?.rows || [];
  const pipeLayerOption: any[] = [];
  layers.map((item) => {
    if (
      item.geometrytype === 'esriGeometryPolyline' ||
      item.layername.indexOf('立管') > -1
    ) {
      pipeLayerOption?.push({
        label: item.layername,
        value: item.layerid,
        id: item.layerid,
        data: item
      });
    }
  });
  if (!pipeLayerOption.length) {
    SLMessage.error('未加载管线服务,功能不可用');
    return;
  }
  state.disabled = false;

  const field = FormConfig.group[0].fields[0] as IFormSelect;
  field && (field.options = pipeLayerOption);
  refForm.value &&
    (refForm.value.dataForm.pipeLayer = pipeLayerOption[0]?.value);
};
onMounted(async () => {
  if (!props.view) return;
  initPipeLayerOption();
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'pipe-length',
    title: '管线长度测量'
  });
  staticState.sketch = initSketch(props.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
});
onBeforeUnmount(() => {
  destroySketch();
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
});
</script>
<style lang="scss" scoped></style>
