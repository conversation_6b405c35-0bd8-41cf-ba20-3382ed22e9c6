<!-- 图幅号查询 -->
<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="telport"
      @refreshed="() => (state.curOperate = 'viewingDetail')"
      @refreshing="() => (state.curOperate = 'detailing')"
      @close="state.curOperate = ''"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import { IFormIns } from '@/components/type'
import { excuteQueryForIds, initQueryParams } from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import PipeDetail from '../common/PipeDetail.vue'

const props = defineProps<{
  view?: __esri.MapView
  telport?: string
}>()
const refForm = ref<IFormIns>()
const refDetail = ref<InstanceType<typeof PipeDetail>>()
const state = reactive<{
  pipeLayerOption: NormalOption[]
  curOperate: 'detailing' | 'viewingDetail' | ''
  tabs: IPipeDetailTab[]
}>({
  pipeLayerOption: [],
  curOperate: '',
  tabs: []
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '图层名称'
      },
      fields: [
        {
          type: 'select',
          field: 'layer',
          options: []
        }
      ]
    },
    {
      fieldset: {
        desc: '图幅号'
      },
      fields: [
        {
          type: 'input',
          field: 'DESIGNNO'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              loading: () => state.curOperate === 'detailing',
              text: () => (state.curOperate === 'detailing' ? '正在查询' : '查询'),
              click: () => startSearch(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const initBaseLayer = () => {
  if (!props.view) return
  const pipeLayer: any = props.view?.map.findLayerById('pipelayer')
  state.pipeLayerOption = []
  pipeLayer?.sublayers?.map(item => {
    state.pipeLayerOption?.push({
      label: item.title,
      value: item.title,
      id: item.id
    })
  })
  const layerField = FormConfig.group[0].fields[0] as IFormSelect
  layerField && (layerField.options = state.pipeLayerOption)
  refForm.value?.dataForm
    && (refForm.value.dataForm.layer = state.pipeLayerOption && state.pipeLayerOption[0]?.value)
}

const startSearch = async () => {
  const layer = state.pipeLayerOption.find(
    item => item.label === refForm.value?.dataForm.layer
  )

  if (!layer) {
    SLMessage.warning('请选择图层')
    state.curOperate = ''
    return
  }
  const val = refForm.value?.dataForm?.DESIGNNO
  if (!val || (val.trim && val.trim() === '')) {
    SLMessage.warning('请输入图幅号')
    state.curOperate = ''
    return
  }
  try {
    state.curOperate = 'detailing'
    const res = await excuteQueryForIds(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layer.id,
      initQueryParams({
        returnGeometry: false,
        where: " DESIGNNO like '%" + val + "%'",
        orderByFields: ['OBJECTID asc']
      })
    )
    if (!res?.length) {
      SLMessage.info('查询结果为空')
      state.curOperate = ''
      state.tabs = []
    } else {
      state.tabs = [
        {
          label: layer.label,
          name: layer.label,
          id: layer.id,
          data: res
        }
      ]
      nextTick(() => {
        refDetail.value?.openDialog()
      })
    }
  } catch (error) {
    SLMessage.error('查询失败，请检查查询条件是否正确')
    state.curOperate = ''
  }
}
const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view)
}
onMounted(() => {
  initBaseLayer()
})
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
</style>
