package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-底图配置对象 base_map_configuration
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@ApiModel(value = "底图配置", description = "平台管理-底图配置实体类")
@Data
public class BaseMapConfigurationPageRequest extends PageableQueryEntity<BaseMapConfiguration> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 底图名称
     */
    @ApiModelProperty(value = "底图名称")
    private String name;

    /**
     * 底图类型
     */
    @ApiModelProperty(value = "底图类型")
    private String type;

    /**
     * 底图服务地址模板
     */
    @ApiModelProperty(value = "底图服务地址模板")
    private String url;

    /**
     * 底图状态（0-禁用，1-启用）
     */
    @ApiModelProperty(value = "底图状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
