package org.thingsboard.server.dao.sql.logicalFlow;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.thingsboard.server.dao.model.sql.LogicalFlowHistory;

import java.util.List;

public interface LogicalFlowHistoryRepository extends JpaRepository<LogicalFlowHistory, String>, JpaSpecificationExecutor<LogicalFlowHistory> {
    Page<LogicalFlowHistory> findByLogicalFlowIdIn(List<String> logicalFlowIdList, Pageable pageable);

    void deleteByLogicalFlowId(String logicalFlowId);
}
