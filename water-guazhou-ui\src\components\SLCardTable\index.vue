<template>
  <SLCard
    class="card-box"
    :title="config?.title||''"
  >
    <template #title>
      <slot name="title"></slot>
    </template>
    <template #query>
      <div class="query-wrapper">
        <el-form
          inline
          size="default"
        >
          <el-form-item
            v-for="(item, i) in config.headerQuery"
            :key="i"
            :label="item.label"
          >
            <SLFormItem
              v-if="item.type !== 'table'"
              v-model="queryParams[item.field]"
              :config="item"
              @change="() => handleChange()"
            ></SLFormItem>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #right>
      <div class="btn-wrapper">
        <template
          v-for="(btn, i) in config.headerBtns || []"
          :key="i"
        >
          <SLButton :config="btn"></SLButton>
        </template>
      </div>
    </template>
    <SLTable
      ref="refTable"
      :config="config"
    ></SLTable>
  </SLCard>
</template>
<script lang="ts">
import { defineComponent, PropType, ref } from 'vue'
import { ISLCardTable } from './type'
import SLTable from '../SLTable/index.vue'

export default defineComponent({
  name: 'SLCardTable',
  props: {
    config: {
      type: Object as PropType<ISLCardTable>,
      default: () => {
        //
      }
    }
  },
  emits: ['change'],
  setup(props, ctx) {
    const refTable = ref<InstanceType<typeof SLTable>>()
    const queryParams = ref<any>({
      ...(props.config.headerQueryDefault || {})
    })
    const handleChange = () => {
      props.config.handleQuery && props.config.handleQuery(queryParams)
      ctx.emit('change', queryParams)
    }
    const exportTable = () => {
      refTable.value?.exportTable()
    }
    return {
      queryParams,
      handleChange,
      exportTable,
      refTable
    }
  }
})
</script>
<style scoped lang="scss">
.card-box {
  height: calc(100% - 100px);
  width: 100%;
}
.btn-wrapper,
.query-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  align-content: center;
  margin-left: 8px;
}
.el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}
</style>
