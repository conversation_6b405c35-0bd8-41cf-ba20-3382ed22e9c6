<!-- 工程管理-工程管理-费用管理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getProjectType,
  getConstructionExpenseList,
  postConstructionExpense,
  getconstructionExpenseExport,
  getConstructionExpenseGlobalExport,
  getConstructionContractOptions,
  postConstructionExpenseComplete
} from '@/api/engineeringManagement/manage';
import detail from './components/detail.vue';
import { StatusType, TypesFee, PaymentMethod } from '../../data';
import { traverse, GenNonDuplicateID } from '@/utils/GlobalHelper';

const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '工程编号', field: 'constructionCode', type: 'input' },
    { label: '工程名称', field: 'constructionName', type: 'input' },
    {
      label: '工程类别',
      field: 'constructionTypeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getConstructionExpenseGlobalExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `费用管理.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: false,
  indexVisible: true,
  expandable: true,
  expandComponent: shallowRef(detail),
  extendedReturn: () => {
    refreshData();
  },
  rowKey: 'constructionCode',
  columns: [
    { label: '工程编号', prop: 'constructionCode' },
    { label: '工程名称', prop: 'constructionName' },
    { label: '合同总金额(万元)', prop: 'contractTotalCost' },
    { label: '结算总金额(万元)', prop: 'finalReportCost' },
    { label: '已付总金额(万元)', prop: 'totalCost' },
    {
      label: '工作状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string =>
        StatusType.find((item) => item.value === row.status)?.color || '',
      formatter: (row) =>
        StatusType.find((item) => item.value === row.status)?.label
    }
  ],
  operationWidth: '360px',
  operations: [
    {
      disabled: (val) => val.status === 'COMPLETED',
      isTextBtn: false,
      type: 'primary',
      text: '新增费用明细',
      perm: true,
      click: (row) => {
        clickAdd(row);
      }
    },
    {
      disabled: (val) => val.status === 'COMPLETED',
      isTextBtn: false,
      type: 'success',
      text: '完成',
      perm: true,
      click: (row) => {
        if (row.items.length === 0) {
          ElMessage.warning('无费用明细');
          return;
        }
        postConstructionExpenseComplete(row.constructionCode).then((res) => {
          if (res.data.code === 200) {
            ElMessage.success('已完成');
          } else {
            ElMessage.warning('完成失败');
          }
          refreshData();
        });
      }
    },
    {
      isTextBtn: false,
      text: '导出支付情况',
      perm: true,
      click: (row) => {
        getconstructionExpenseExport(row.constructionCode).then((res) => {
          const url = window.URL.createObjectURL(res.data);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', `${row.constructionName}费用管理.xlsx`);
          document.body.appendChild(link);
          link.click();
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '编辑签证',
  appendToBody: true,
  labelWidth: '150px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionExpense(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '资金编号',
          field: 'code',
          disabled: true
        },
        {
          xs: 12,
          type: 'select',
          label: '费用类型',
          field: 'type',
          rules: [{ required: true, message: '请选择费用类型' }],
          options: TypesFee
        },
        {
          xs: 12,
          type: 'select',
          label: '所属合同名称',
          field: 'contractCode',
          options: computed(() => data.contractList) as any,
          rules: [{ required: true, message: '请选择所属合同名称' }]
        },
        {
          xs: 12,
          type: 'number',
          label: '合同金额(万元)',
          field: 'contractTotalCost',
          readonly: true,
          min: 0
        },
        {
          xs: 12,
          type: 'select',
          label: '支付方式',
          field: 'paymentType',
          options: PaymentMethod
        },
        {
          xs: 12,
          type: 'number',
          label: '金额(万元)',
          field: 'cost',
          rules: [{ required: true, message: '请输入金额' }],
          min: 0
        },
        {
          xs: 12,
          type: 'date',
          label: '报批时间',
          field: 'approvalTime',
          format: 'x',
          rules: [{ required: true, message: '请输入报批时间' }]
        },
        {
          xs: 12,
          type: 'date',
          label: '提交财务处理时间',
          field: 'submitFinanceTime',
          format: 'x',
          rules: [{ required: true, message: '请输入提交财务处理时间' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '一审审核金额(万元)',
          field: 'firstVerifyCost'
        },
        {
          xs: 12,
          type: 'input',
          label: '一审结果单位',
          field: 'firstVerifyOrganization'
        },
        {
          xs: 12,
          type: 'input',
          label: '二审审核金额(万元)',
          field: 'secondVerifyCost'
        },
        {
          xs: 12,
          type: 'input',
          label: '二审结果单位',
          field: 'secondVerifyOrganization'
        },
        {
          xs: 12,
          type: 'input',
          label: '代收款信息',
          field: 'payeeInfo'
        },
        {
          xs: 12,
          type: 'input',
          label: '收款单位',
          field: 'payeeOrganization'
        },
        {
          type: 'textarea',
          label: '说明',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const clickAdd = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '新增费用明细';
  addOrUpdateConfig.defaultValue = {
    constructionCode: row.constructionCode,
    code: `${row.constructionCode.replace('S', 'Z')}-${GenNonDuplicateID()}`,
    type: '预付款',
    contractTotalCost: row.contractTotalCost,
    paymentType: '其它方式',
    cost: 0,
    approvalTime: new Date(),
    submitFinanceTime: new Date(),
    firstVerifyCost: 0,
    secondVerifyCost: 0
  };
  data.getConstructionContract(row.constructionCode);
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目类别
  projectType: [],
  // 合同列表
  contractList: [],
  getOptions: () => {
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || [], 'children');
    });
  },
  getConstructionContract: (val: string) => {
    getConstructionContractOptions(val).then((res) => {
      data.contractList = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'code'
      });
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  getConstructionExpenseList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
});
</script>

<style>
.cs {
  margin-top: 10px;
  padding-top: 20px;
}
</style>
