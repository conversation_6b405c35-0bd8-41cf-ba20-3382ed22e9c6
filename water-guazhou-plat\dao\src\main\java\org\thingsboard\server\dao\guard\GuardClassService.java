package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardClass;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardClassPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardClassSaveRequest;

public interface GuardClassService {
    GuardClass findById(String id);

    IPage<GuardClass> findAllConditional(GuardClassPageRequest request);

    GuardClass save(GuardClassSaveRequest entity);

    boolean delete(String id);

}
