<!-- 配表分析 -->
<template>
  <div
    class="view overlay-y"
  >
    <Search
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card-table overlay-y"
      title=" "
    >
      <template #title>
        {{ props.stationName }}
      </template>
      <AttrTable
        :columns="state.columns"
        :data="state.data"
      ></AttrTable>
    </SLCard>
    <SLCard
      class="card-ehcarts"
      title="图表分析"
    >
      <!-- 图表模式 -->
      <div
        ref="agriEcoDev"
        class="chart-box"
      >
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':''"
          :option="state.chartOption"
        ></VChart>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useAppStore } from '@/store'
import { lineOption } from '../../data/echart'
import { IECharts } from '@/plugins/echart'
import {
  getMeterConfigChart
} from '@/api/pipeNetworkMonitoring/flowMonitoring'
import { formatDate } from '@/utils/DateFormatter'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageWarning } = useGlobal()
const erd = elementResizeDetectorMaker()
const loading = ref<boolean>(false)
const refChart = ref<IECharts>()
const props = defineProps<{
  stationName?: string,
  stationId?: string
}>()

const cardSearch = ref<ISearchIns>()
const agriEcoDev = ref<any>()

const state = reactive<{
  chartOption: any
  columns: IAttrTableRow[][],
  data: any
}>({
  chartOption: null,
  data: {},
  columns: [
    [
      { label: '站点名称', prop: 'name', value: '无数据' },
      { label: '站点类型', prop: 'type', value: '无数据' }
    ],
    [
      { label: '站点地址', prop: 'address', value: '无数据' },
      { label: '坐标定位', prop: 'location', value: '无数据' }
    ],
    [
      { label: '更新时间', prop: 'createTime', value: '无数据', formatter: item => formatDate(item.createTime) },
      { label: '备注', prop: 'remark', value: '无数据' }
    ]
    // [
    //   { label: '口径流量平均值', prop: 'name', value: '无数据' },
    //   { label: '口径信息提示', prop: 'name', value: '无数据' }
    // ]
  ]
})

watch(
  () => props.stationId,
  () => {
    refreshData()
  }
)

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [dayjs().add(-7, 'day').format(), dayjs().format()]
  },
  filters: [
    { type: 'daterange', label: '日期', field: 'date', clearable: false },
    {
      type: 'btn-group',
      btns: [
        { perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            if (props.stationId) {
              refreshData()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        }
        // {
        //   perm: true,
        //   text: '导出',
        //   type: 'primary',
        //   svgIcon: shallowRef(Download),
        //   click: () => {
        //     // refTable.value?.exportTable()
        //   }
        // }
      ]
    }
  ]
})
// 配置加载图表数据
const refuseChart = (data:any) => {
  const chartOption = lineOption()
  chartOption.series = []
  for (const key in data) {
    const newData = data[key]?.map(res => { return res.value })
    const dataX = data[key]?.map(res => { return res.ts.substring(res.ts.length - 2, 20) + ':00' })
    const serie = {
      name: key,
      smooth: true,
      data: newData,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
    chartOption.series.push(serie)
    chartOption.yAxis[0].name = '流量(m³)'
    chartOption.xAxis.data = dataX
  }
  refChart.value?.clear()
  nextTick(() => {
    erd.listenTo(agriEcoDev.value, () => {
      state.chartOption = chartOption
      refChart.value?.resize()
    })
  })
}
const refreshData = async () => {
  //
  loading.value = true
  const query = cardSearch.value?.queryParams || {}
  const [start, end] = query.date
  const params = {
    start: start ? dayjs(start).startOf('day').valueOf() : null,
    end: end ? dayjs(end).endOf('day').valueOf() : null,
    stationId: props.stationId as string
  }
  const res = await getMeterConfigChart(params)
  const data = res.data?.data
  state.data = data?.stationInfo
  refuseChart(data?.lineData)
  loading.value = false
}
onMounted(async () => {
  if (props.stationId) {
    await refreshData()
  }
})
</script>
<style lang="scss" scoped>
.view{
  height: 100%;
}
.card-table {
  margin-top: 10px;
  height: 34%;
}

.card-ehcarts {
  margin-top: 15px;
  height: calc(66% - 62px);
  width: 100%;

  .chart-box {
    height: 100%;
    width: 100%;
  }
}
</style>
