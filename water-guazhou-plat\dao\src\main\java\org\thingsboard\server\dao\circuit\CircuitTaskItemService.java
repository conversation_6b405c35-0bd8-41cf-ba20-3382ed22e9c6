package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTaskItem;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemSaveRequest;

import java.util.List;

public interface CircuitTaskItemService {
    /**
     * 分页条件查询巡检任务单任务条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitTaskItem> findAllConditional(CircuitTaskItemPageRequest request);

    /**
     * 保存巡检任务单条目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    List<CircuitTaskItem> save(List<CircuitTaskItemSaveRequest> entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitTaskItem entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 完成任务条目
     *
     * @param id  任务条目的id
     * @param req 任务条目完成的明细
     * @return 是否成功
     */
    boolean complete(String id, CircuitTaskItemCompleteRequest req);

    /**
     * 批量删除
     *
     * @param idList 唯一标识列表
     * @return 是否删除成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 通过{@link CircuitConfig#getId() 配置模板的id}获取对应{@link CircuitConfig 配置模板}来对指定的{@link CircuitTask 巡检任务}生成任务条目
     *
     * @param settings 配置模板的id列表
     * @param mainId   {@link CircuitTask#getId() 所属巡检任务id}
     * @return 是否成功
     */
    boolean saveAllToSettings(List<String> settings, String mainId);

}
