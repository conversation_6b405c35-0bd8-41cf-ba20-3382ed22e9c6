import TileLayer from '@arcgis/core/layers/TileLayer.js'
import MapImageLayer from '@arcgis/core/layers/MapImageLayer.js'
import FeatureLayer from '@arcgis/core/layers/FeatureLayer.js'
import WMSLayer from '@arcgis/core/layers/WMSLayer.js';
import TintLayer from './Layers/TintLayer.js'

export const createServiceLayer = (
  view: __esri.MapView,
  type: 'tiled' | 'MapImage' | 'Feature' | 'WMSLayer',
  options: { url?: string; title?: string; id?: string }
) => {
  switch (type) {
    case 'MapImage':
      return new MapImageLayer({
        ...options
        // legendEnabled: false
        // listMode: 'hide-children'
      })
    case 'tiled':
      return new TileLayer(options)
    case 'Feature':
      return new FeatureLayer(options)
    case 'WMSLayer':
      return new WMSLayer(options);
    default:
      break
  }
}
export const createTdtLayer = (params: {
  type:
    | 'vec_c'
    | 'vec_w'
    | 'cva_c'
    | 'cva_w'
    | 'img_c'
    | 'img_w'
    | 'cia_c'
    | 'cia_w'
    | 'ter_c'
    | 'ter_w'
    | 'cta_c'
    | 'cta_w'
    | 'ibo_c'
    | 'ibo_w'
    | 'eva_c'
    | 'eva_w'
    | 'eia_c'
    | 'eia_w'
  color?: any
  filter?: string
  urlTemplate?: string
}) => {
  const pTypes = params.type || 'vec_w'
  const types = pTypes.split('_')
  const tiledLayer = new TintLayer({
    urlTemplate:
      params.urlTemplate
      || `http://t0.tianditu.gov.cn/${pTypes}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${types[0]}&STYLE=default&TILEMATRIXSET=${types[1]}&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}`,

    // urlTemplate: `http://{subDomain}.tianditu.gov.cn/DataServer?T=${type}&x={col}&y={row}&l={level}&tk=${tdtKey}`,
    subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
    color: pTypes === 'img_c' || pTypes === 'img_w' ? '' : params.color,
    filter: params.filter
  })
  return tiledLayer
}

export const createGDLayer = (
  type?: '6' | '8',
  color?: any,
  urlTemplate?: string
) => {
  const tiledLayer = new TintLayer({
    urlTemplate:
      urlTemplate
      || `http://webst01.is.autonavi.com/appmaptile?style=${type}&x={col}&y={row}&z={level}`,

    // urlTemplate: `http://{subDomain}.tianditu.gov.cn/DataServer?T=${type}&x={col}&y={row}&l={level}&tk=${tdtKey}`,
    subDomains: [],
    color
  })
  return tiledLayer
}
