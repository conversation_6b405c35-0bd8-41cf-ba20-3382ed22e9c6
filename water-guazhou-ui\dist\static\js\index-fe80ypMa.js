import{d as R,a0 as U,c as I,r as m,bB as k,s as T,bF as K,o as Q,g as v,n as y,q as i,F as f,p as l,aw as X,i as F,j as Z,aB as G,aJ as z,bh as x,cs as tt,an as J,h as at,c5 as et,_ as ot,aq as st,cU as nt,b6 as lt,d$ as it,al as rt,b7 as ct,aj as pt,C as dt}from"./index-r0dFAfgr.js";import{_ as ut}from"./index-C9hz-UZb.js";import mt from"./stationDetailMonitoring-HU5y2sIb.js";import{p as ft}from"./echart-DxEZmJvB.js";import{u as gt}from"./useStation-DJgnSZIA.js";import{e as _t}from"./flowMonitoring-DtJlPj0G.js";import{G as bt,e as vt,k as ht}from"./zhandian-YaGuQZe6.js";import{G as yt}from"./onemap-CEunQziB.js";import{r as M}from"./chart-wy3NEK2T.js";import{G as wt}from"./Group1-DZYehK7n.js";import{a as Dt,b as xt,p as It}from"./alarm-DY6-LWP-.js";import"./Search-NSrhrIa_.js";import"./headwaterMonitoring-BgK7jThW.js";const Tt={class:"wrapper"},Ct={class:"tab-content"},St={key:0,class:"monitoring-tab"},kt={class:"top-box"},jt={class:"bottom-box"},Lt={class:"monitor-title"},Nt={class:"title"},Pt=["onClick"],Vt={class:"monitor-table"},Bt={class:"monitor"},Ft={style:{"padding-left":"10px"}},Gt=R({__name:"index",setup(zt){var B;const h=U(),C=I([]),{getStationTree:O}=gt(),H=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],e=m({drawerTitle:"",activeName:"监测模式",tabsList:[],stationTree:[],treeDataType:"Project",stationId:"",stationInfo:{},pieOption:null,JianCeDatas:[],alarmList:[],icon:wt}),j=I(),L=I(),N=I(),g=m({data:[],loading:!1,title:"区域划分",expandOnClickNode:!1,treeNodeHandleClick:async t=>{g.currentProject!==t&&(g.loading=!0,g.currentProject=t,e.treeDataType=t.data.type,e.treeDataType==="Project"?(h.SET_selectedProject(t),C.value=[],e.JianCeDatas=[],await S(),e.stationId=""):k(()=>{console.log("data",t),e.stationId=t.id,e.stationInfo=t}))}}),Y=()=>{g.loading=!1};m({filters:[{type:"input",label:"监测点名称",labelWidth:120,field:"name1"},{type:"btn-group",btns:[{type:"default",perm:!0,svgIcon:T(it),text:"",isBlockBtn:!0,click:()=>{var t;(t=N.value)==null||t.toggleMore()}},{perm:!0,text:"查询",svgIcon:T(rt),click:()=>S()},{type:"default",perm:!0,text:"重置",svgIcon:T(ct),click:()=>{var t;(t=N.value)==null||t.resetForm()}},{perm:!0,type:"warning",text:"导出",svgIcon:T(pt),click:()=>{}}]}],moreFilters:[{type:"select",label:"排序顺序",field:"name1",options:[]},{type:"select",label:"筛选条件",field:"name1",options:[{label:"正常",value:"正常"}]}],defaultParams:{projectId:(B=h.selectedProject)==null?void 0:B.value}});const $=m({title:"",labelWidth:"130px",width:"75%",group:[],cancel:!1,onClosed:()=>{e.treeDataType,e.stationId=""}}),A=t=>{var a;(a=L.value)==null||a.openDrawer(),k(()=>{e.drawerTitle=t.title,e.stationInfo=t,e.stationId=t.id,e.treeDataType="Station"}),console.log(t.id)},E=m({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"监测模式",value:"监测模式"},{label:"列表模式",value:"列表模式"}],handleTabClick:t=>{}}),P=async()=>{var r,s;const t={alarmStatus:"1",stationType:"流量监测站,测流压站",projectId:(r=h.selectedProject)==null?void 0:r.value,size:_.pagination.limit||20,page:_.pagination.page||1},o=(s=(await ht(t)).data)==null?void 0:s.data;console.log(o),_.dataList=(o==null?void 0:o.data)||[],_.pagination.total=(o==null?void 0:o.total)||0},V=m({group:[{id:"chart",fields:[{type:"vchart",option:M(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),S=async()=>{var p,d,b,n;await P(),g.loading=!1;const a=(d=(await bt({page:w.pagination.page||1,size:w.pagination.limit||20,type:"流量监测站,测流压站",projectId:(p=h.selectedProject)==null?void 0:p.value})).data)==null?void 0:d.data,o=await vt({stationType:"流量监测站,测流压站",projectId:(b=h.selectedProject)==null?void 0:b.value});w.dataList=o.data,w.pagination.total=o.data.length;const s=(await _t({stationType:"流量监测站,测流压站",projectId:(a==null?void 0:a.value)||((n=h.selectedProject)==null?void 0:n.value)})).data;console.log(s),s==null||s.map(c=>{C.value.push({id:c.stationId,title:c.name,monitorData:c.dataList||[]}),e.JianCeDatas=C.value}),g.loading=!1,await q(),await W()},W=async()=>{var p,d,b;const t=await yt({status:""}),a=V.group[0].fields[0],o=((d=(p=t.data)==null?void 0:p.data)==null?void 0:d.length)||0,r=[],s=(b=t.data)==null?void 0:b.data;s==null||s.map(n=>{let c=r.find(D=>D.status===n.status);const{label:u}=H.find(D=>D.name===n.status)||{};c?c.value++:(c={name:u,status:n.status,nameAlias:u,value:1,scale:"0%"},r.push(c))}),r.map(n=>(n.scale=o===0?"0%":Number(n.value)/o*100+"%",n)),a&&(a.option=M(r,"个"))},q=async()=>{var t;(t=j.value)==null||t.clear(),k(()=>{var a;e.pieOption=ft(),(a=j.value)==null||a.resize()})},_=m({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"alarmInfo",label:"报警描述"},{prop:"time",label:"报警时间",formatter:t=>K(t.time).format("YYYY-MM-DD HH:mm:ss")},{prop:"alarmType",label:"报警类型",formatter:t=>{var a;return(a=Dt.find(o=>o.value===t.alarmType))==null?void 0:a.label}},{prop:"alarmStatus",label:"报警状态",formatter:t=>{var a;return(a=xt.find(o=>o.value===t.alarmStatus))==null?void 0:a.label}},{prop:"processStatus",label:"处理状态",formatter:t=>{var a;return(a=It.find(o=>o.value===t.alarmStatus))==null?void 0:a.label}}],operations:[],pagination:{refreshData:({page:t,size:a})=>{_.pagination.page=t,_.pagination.limit=a,P()}}}),w=m({loading:!1,dataList:[{}],indexVisible:!0,columns:[{prop:"name",label:"监测点名称",align:"center",sortable:!0},{prop:"time",label:"读取时间",align:"center",sortable:!0},{prop:"Instantaneous_flow",label:"瞬时流量",unit:"(m³/h)",align:"center",sortable:!0},{prop:"total_flow",label:"累计流量",unit:"(m³)",align:"center",sortable:!0}],operations:[],pagination:{hide:!0}});return Q(async()=>{const t=["流量监测站,测流压站"].join(","),a=await O(t);g.data=a,await S()}),(t,a)=>{const o=et,r=ot,s=ut,p=st,d=nt,b=lt;return v(),y("div",Tt,[i(s,{class:"wrapper-content",title:" "},{title:f(()=>[i(o,{modelValue:e.activeName,"onUpdate:modelValue":a[0]||(a[0]=n=>e.activeName=n),config:E},null,8,["modelValue","config"])]),default:f(()=>[l("div",Ct,[e.activeName==="监测模式"?(v(),y("div",St,[l("div",kt,[i(s,{class:"card",title:"监测状态统计"},{default:f(()=>[i(r,{ref:"refForm",config:V},null,8,["config"])]),_:1}),i(s,{class:"table",title:""},{default:f(()=>[i(p,{class:"card-table",config:_},null,8,["config"])]),_:1})]),l("div",jt,[l("div",{class:X(["card-item",{isDark:F(Z)().isDark}])},[(v(!0),y(G,null,z(e.JianCeDatas,(n,c)=>(v(),y("div",{key:c,class:"card-content"},[i(s,{title:" ",class:"inner-card left"},{title:f(()=>[l("div",Lt,[l("div",Nt,[i(d,{src:e.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),l("span",null,x(n.title),1)]),l("div",{onClick:u=>A(n)},[i(F(tt),{icon:"ph:warning-circle-bold",style:{color:"#4f7db8","font-size":"18px"}})],8,Pt)])]),default:f(()=>[l("div",Vt,[l("div",Bt,[(v(!0),y(G,null,z(n.monitorData,(u,D)=>(v(),y("div",{key:D,class:"box-1"},[l("div",null,x(u.propertyName)+" "+x(u.unit?"("+u.unit+")":""),1),l("div",null,x(u.value||"无"),1)]))),128))])])]),_:2},1024)]))),128))],2)])])):J("",!0),e.activeName==="列表模式"?(v(),at(p,{key:1,class:"table",config:w},null,8,["config"])):J("",!0)])]),_:1}),i(b,{ref_key:"refDrawer",ref:L,config:$},{title:f(()=>[i(d,{src:e.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),l("span",Ft,x(e.drawerTitle),1)]),default:f(()=>[i(mt,{"station-id":e.stationId,"station-detail":e.stationInfo,onHiddenLoading:Y},null,8,["station-id","station-detail"])]),_:1},8,["config"])])}}}),Qt=dt(Gt,[["__scopeId","data-v-f69cf510"]]);export{Qt as default};
