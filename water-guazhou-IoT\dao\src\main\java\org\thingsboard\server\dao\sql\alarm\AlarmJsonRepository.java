/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.alarm;

import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.AlarmJsonEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/21/2017.
 */
@SqlDao
public interface AlarmJsonRepository extends CrudRepository<AlarmJsonEntity, String> {

//    @Query("SELECT a FROM AlarmEntity a WHERE a.tenantId = :tenantId AND a.originatorId = :originatorId " +
//            "AND a.originatorType = :entityType AND a.type = :alarmType ORDER BY a.startTs DESC ,a.type ASC, a.id DESC")
//    List<AlarmEntity> findLatestByOriginatorAndType(@Param("tenantId") String tenantId,
//                                                    @Param("originatorId") String originatorId,
//                                                    @Param("entityType") EntityType entityType,
//                                                    @Param("alarmType") String alarmType,
//                                                    Pageable pageable);


    List<AlarmJsonEntity> findByTenantId(@Param("tenant_id") String tenantId);

    List<AlarmJsonEntity> findByAlarmName(@Param("alarm_name") String alarmName);

    List<AlarmJsonEntity> findByTenantIdAndDeviceId(@Param("tenant_id") String tenantId,
                                                    @Param("device_id") String deviceId);

    List<AlarmJsonEntity> findByTenantIdAndDeviceIdAndAttribute(@Param("tenant_id") String tenantId,
                                                                @Param("device_id") String deviceId,
                                                                @Param("attribute") String attribute);

    List<AlarmJsonEntity> findByDeviceIdAndAttribute(@Param("device_id") String deviceId,
                                                     @Param("attribute") String attribute);

    List<AlarmJsonEntity> findByDeviceId(@Param("device_id") String deviceId);

    List<AlarmJsonEntity> findByDeviceIdAndAttributeAndSeverity(@Param("device_id") String deviceId,
                                                                @Param("attribute") String attribute,
                                                                @Param("severity") String level);


}
