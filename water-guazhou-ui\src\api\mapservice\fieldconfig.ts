import { request } from '@/plugins/axios/gis';
import { request as requestByGeoserver } from '@/plugins/axios/geoserver';

import { requestPipe } from '@/plugins/axios/gisService';
import { useGisStore } from '@/store';

/**
 * 获取指定图层的字段配置
 * @param layername
 * @returns
 */
export const GetFieldConfig = (layername: string) => {
  return request({
    url: '/api/webapp/fieldconfig',
    method: 'post',
    data: {
      layername
    }
  });
};

export const GetAllFieldConfig = () => {
  return request({
    url: '/api/pda/fieldconfigall',
    method: 'post'
  });
};
/**
 * 配置图层的显示字段
 * @param params
 * @returns
 */
export const SetFieldConfig = (params: {
  layername: string;
  fields: {
    name: string;
    alias: string;
    type: string | null;
    visible: boolean;
  }[];
  f: 'pjson';
}) => {
  return request({
    url: '/api/webapp/fieldconfig/set',
    method: 'post',
    data: params
  });
};
/**
 * 获取字段唯一值
 */
export const GetFieldUniqueValue = (params: {
  usertoken?: string;
  layerid?: string | number;
  field_name?: string;
  f?: 'pjson';
}) => {
  return requestPipe({
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService +
      '/exts/TFGeo/apiSOE/getFieldUniqueValue',
    method: 'get',
    params: {
      usertoken: useGisStore().gToken,
      f: 'pjson',
      ...params
    }
  });
};


export const QuertByGeoserver = (params: {
  layerName: string;
  sql: string;
}) => {
  return requestByGeoserver({
    url:`/geoserver/ows?service=WFS&version=1.1.0&request=GetFeature&typeName=${params.layerName}&CQL_FILTER=${params.sql}&outputFormat=application/json`,
    method: 'get',
  });
};