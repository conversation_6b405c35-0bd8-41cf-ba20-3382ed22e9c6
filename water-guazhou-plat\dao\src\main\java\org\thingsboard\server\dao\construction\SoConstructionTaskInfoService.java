package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionTaskInfo;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoSaveRequest;

public interface SoConstructionTaskInfoService {
    /**
     * 分页条件查询工程任务状态
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionTaskInfo> findAllConditional(SoConstructionTaskInfoPageRequest request);

    /**
     * 保存工程任务状态
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionTaskInfo save(SoConstructionTaskInfoSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionTaskInfo entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 标记为完成状态
     *
     * @param id    唯一标识
     * @param scope 作用域，用于校验
     * @return 是否成功
     */
    boolean markAsComplete(String id, SoGeneralSystemScope scope);

    /**
     * 标记为完成状态
     *
     * @param constructionCode 所属工程编码
     * @param tenantId         客户id
     * @param scope            作用域，用于校验
     * @return 是否成功
     */
    boolean markAsComplete(String constructionCode, String tenantId, SoGeneralSystemScope scope);

    /**
     * 是否已完成
     *
     * @param id    唯一标识
     * @param scope 作用域
     * @return 是否已完成
     */
    boolean isComplete(String id, SoGeneralSystemScope scope);

    /**
     * 是否已完成
     *
     * @param constructionCode 所属工程编码
     * @param tenantId         客户id
     * @param scope            作用域
     * @return 是否已完成
     */
    boolean isComplete(String constructionCode, String tenantId, SoGeneralSystemScope scope);

}
