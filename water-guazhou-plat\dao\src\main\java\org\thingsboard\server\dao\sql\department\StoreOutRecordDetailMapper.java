package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetail;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetailResponse;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailPageRequest;

import java.util.List;

@Mapper
public interface StoreOutRecordDetailMapper extends BaseMapper<StoreOutRecordDetail> {
    IPage<StoreOutRecordDetailResponse> findByPage(StoreOutRecordDetailPageRequest request);

    boolean update(StoreOutRecordDetail attr);

    int saveAll(List<StoreOutRecordDetail> list);

    int updateAll(List<StoreOutRecordDetail> list);

    int removeAllByMainOnIdNotIn(@Param("id") String id, @Param("idList") List<String> idList);

    List<String> differentIdListTo(@Param("mainId") String mainId, @Param("excludeList") List<String> excludeList);

}
