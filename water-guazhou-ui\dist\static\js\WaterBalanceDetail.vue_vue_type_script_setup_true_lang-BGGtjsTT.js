import{d as u,c as b,r as j,g as p,h as P,i as l,aq as i}from"./index-r0dFAfgr.js";const k=u({__name:"WaterBalanceDetail",setup(d,{expose:o}){const s=b(),a=j({pagination:{hide:!0},height:500,dataList:[{project:"供水总量",subProject1:"自产供水量",source:"按各水厂出厂流量计",depart:"水厂及计量管理部门"},{project:"",subProject1:"外购供水量",source:"按区域间贸易流量计",depart:"客服及计量管理部门"},{project:"注册用户用水量",subProject1:"计费用水量",subProject2:"计费计量用水量",source:"按营收系统",depart:"客服部门"},{project:"",subProject1:"",subProject2:"计费未计量用水量",subProject3:"环卫用水",source:"按车辆容积、数量、使用频次等估算或按用水协议",depart:"客服部门"},{project:"",subProject1:"",subProject2:"",subProject3:"绿化用水",source:"按取水点数量，使用频次，定额流量等估算或接用水协议",depart:"客服部门"},{project:"",subProject1:"",subProject2:"",subProject3:"新建管道冲洗",source:"按管道口径、流速、冲洗时间等估算",depart:"工程及管网管理部门、调度中心"},{project:"",subProject1:"",subProject2:"",subProject3:"第三方资产损坏",source:"按水量损失赔偿依据",depart:"客服及用水稽查部门"},{project:"",subProject1:"",subProject2:"",subProject3:"定量户用水",source:"按定量标准",depart:"客服部门"},{project:"",subProject1:"免费用水量",subProject2:"免费计量用水量",subProject3:"自来水企业生产办公用水",source:"按计量表",depart:"客服部门"},{project:"",subProject1:"",subProject2:"",subProject3:"消防用水",source:"按计量表",depart:"客服部门"},{project:"",subProject1:"",subProject2:"",subProject3:"政策性减免",source:"按减免政策、减免对象",depart:"客服部门"},{project:"",subProject1:"",subProject2:"免费未计量用水量",subProject3:"消防用水量",source:"按消防出警量、平均消火栓使用数量、时间及消防演练次数等估算",depart:"客服部门"},{project:"",subProject1:"",subProject2:"",subProject3:"管网维护和冲洗水量",source:"按管道口径、流速、冲洗时间等估算",depart:"管网管理部门、调度中心"},{project:"",subProject1:"",subProject2:"",subProject3:"自来水企业生活办公用水",source:"按办公人员数及用水定额标准估算",depart:"行政部门"},{project:"",subProject1:"",subProject2:"",subProject3:"二次供水泵房冲洗",source:"安水箱容积、冲洗次数等估算",depart:"客服及管网管理部门"},{project:"漏损水量",subProject1:"漏失水量",subProject2:"明漏水量",subProject3:"明漏点漏失水量",source:"按明漏流量计自发现至关闸止水的事件估算",depart:"管网管理部门"},{project:"",subProject1:"",subProject2:"",subProject3:"爆管漏失水量",source:"按爆管流量计自爆管至关闸止水的时间估算",depart:"管网管理部门、调度中心"},{project:"",subProject1:"",subProject2:"暗漏水量",subProject3:"已检出暗漏点水量",source:"按检出暗漏点漏量及检漏周期估算",depart:"管网管理部门"},{project:"",subProject1:"",subProject2:"",subProject3:"未检出暗漏点水量",source:"按每月（年）检出暗漏点和发现埋地管明漏点的数量、漏量及管网改造情况等估算",depart:"管网管理部门"},{project:"",subProject1:"",subProject2:"背景漏失水量",subProject3:"",source:"单位管长夜间最小流量（根据DMA测定）* 管网总长 * 时间",depart:"管网管理部门"},{project:"",subProject1:"",subProject2:"水箱、水池的渗漏和溢流水量",subProject3:"",source:"根据实际情况估算",depart:"管网管理部门"},{project:"",subProject1:"计量损失水量",subProject2:"居民用户总分表差损失水量",subProject3:"",source:"按评定标准内计算公式结算，差率按8%",depart:"客服部门"},{project:"",subProject1:"",subProject2:"非居民用户表具误差损失水量",subProject3:"",source:"按评定标准内计算公式结算，差率按水表计量抽检平均值估算",depart:"计量管理部门"},{project:"",subProject1:"其他损失水量",subProject2:"未注册用户用水和用户拒查等管理因素导致的损失水量",subProject3:"偷盗用水量",source:"按已查处偷盗等用水量的一定比例进行推算（经验估算2-3倍）",depart:"用水稽查部门"},{project:"",subProject1:"",subProject2:"",subProject3:"抄表误差水量",source:"按抄表稽查依据估算",depart:"用水稽查部门"},{project:"",subProject1:"",subProject2:"",subProject3:"其他损失水量",source:"按实际情况估算",depart:"客服部门等"},{project:"系统数据",subProject1:"DN75（含）以上管道长度",source:"管道口径DN75以上长度",depart:"水厂及计量管理部门"},{project:"",subProject1:"单位供水量管长",source:"供水总量除以管道口径DN75以上长度",depart:"水厂及计量管理部门"},{project:"",subProject1:"年平均出厂压力",source:"水厂年平均压力值",depart:"水厂及计量管理部门"},{project:"",subProject1:"居民抄表到户水量",source:"小区居民抄表水量",depart:"水厂及计量管理部门"},{project:"",subProject1:"最大冻土深度",source:"历年冻土深度最大值中的最大值",depart:"水厂及计量管理部门"}],columns:[{minWidth:160,align:"center",label:"项目构成",prop:"project"},{align:"center",label:"细分项目",prop:"field2",subColumns:[{minWidth:160,align:"left",label:"",prop:"subProject1"},{minWidth:160,align:"left",label:"",prop:"subProject2"},{minWidth:160,align:"left",label:"",prop:"subProject3"}]},{minWidth:160,align:"center",label:"数据来源",prop:"source"},{minWidth:160,align:"center",label:"责任部门",prop:"depart"}],spanMethod({rowIndex:c,columnIndex:t}){let e=1,r=1;if(t===0)switch(c){case 0:e=2;break;case 2:e=13;break;case 15:e=11;break;case 26:e=5;break;default:e=0,r=0;break}else if(t===1)switch(c){case 0:case 1:r=3,e=1;break;case 2:e=6;break;case 8:e=7;break;case 15:e=6;break;case 21:e=2;break;case 23:e=3;break;case 26:case 27:case 28:case 29:case 30:e=1,r=3;break;default:r=0,e=0;break}else if(t===2)switch(c){case 2:e=1,r=2;break;case 3:e=5;break;case 8:e=3;break;case 11:e=4;break;case 15:case 17:e=2;break;case 22:case 19:case 21:case 20:e=1,r=2;break;case 23:e=3;break;default:e=0,r=0;break}else if(t===3)switch(c){case 0:case 1:case 2:case 19:case 20:case 21:case 22:case 26:case 27:case 28:case 29:case 30:r=0,e=0;break}return{rowspan:e,colspan:r}}});return o({refTable:s}),(c,t)=>{const e=i;return p(),P(e,{ref_key:"refTable",ref:s,config:l(a)},null,8,["config"])}}});export{k as _};
