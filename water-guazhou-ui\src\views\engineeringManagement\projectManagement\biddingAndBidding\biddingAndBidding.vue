<!-- 工程管理-项目管理-招投标流程 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="2"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import { getProjectType } from '@/api/engineeringManagement/manage';
import {
  getProjectList,
  getBidding,
  getBiddingCompany,
  postBidding,
  getprojectBiddingExport
} from '@/api/engineeringManagement/projectManagement';
import useGlobal from '@/hooks/global/useGlobal';
import { traverse } from '@/utils/GlobalHelper';
import detail from '../../components/detail.vue';
import { formatDate } from '@/utils/DateFormatter';

const { $btnPerms } = useGlobal();
const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '项目编号', field: 'code', type: 'input' },
    { label: '项目名称', field: 'name', type: 'input' },
    {
      label: '项目类别',
      field: 'typeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    },
    { label: '启动时间', field: 'time', type: 'daterange', format: 'x' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getprojectBiddingExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `招投标流程.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '项目编号', prop: 'code' },
    { label: '项目名称', prop: 'name' },
    { label: '项目类别', prop: 'typeName' },
    { label: '项目概算(万元)', prop: 'estimate' },
    {
      label: '启动时间',
      prop: 'startTime',
      formatter: (row) => formatDate(row.startTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      label: '预计结束时间',
      prop: 'expectEndTime',
      formatter: (row) => formatDate(row.expectEndTime, 'YYYY-MM-DD HH:mm:ss')
    },
    { label: '项目负责人', prop: 'principal' }
  ],
  operationWidth: '200px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '更新招投标流程',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      isTextBtn: false,
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '更新招投标流程',
  labelWidth: '100px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    params.items.map((item) => {
      item.biddingId = params.biddingId;
    });
    postBidding(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success('更新成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning('更新失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'projectCode'
        },
        {
          xs: 12,
          type: 'input',
          label: '代理招标公司',
          field: 'proxyBiddingCompany'
        },
        {
          type: 'table',
          field: 'items',
          label: '参与投标公司',
          config: {
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加',
                        perm: true,
                        click: () => {
                          data.BiddingCompany.push({
                            num: data.BiddingCompany.length,
                            name: '',
                            contactUser: '',
                            phone: ''
                          });
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            indexVisible: true,
            height: '200px',
            dataList: computed(() => data.BiddingCompany) as any,
            columns: [
              {
                label: '公司名称',
                prop: 'name',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'input',
                  field: 'name',
                  rules: [{ required: true, message: '请输入公司名称' }]
                }
              },
              {
                label: '联系人',
                prop: 'contactUser',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'input',
                  field: 'contactUser',
                  rules: [{ required: true, message: '请输入联系人' }]
                }
              },
              {
                label: '联系电话',
                prop: 'phone',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'input',
                  field: 'phone',
                  rules: [{ required: true, message: '请输入联系电话' }]
                }
              }
            ],
            operations: [
              {
                type: 'danger',
                perm: true,
                text: '删除',
                icon: ICONS.DELETE,
                click: (row) => {
                  data.BiddingCompany = data.BiddingCompany.filter(
                    (item) => item.num !== row.num
                  );
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        },
        {
          type: 'select',
          label: '中标公司',
          field: 'preferCompanyId',
          options: computed(() =>
            traverse(
              data.BiddingCompany.filter((item) => item.id),
              'children',
              { label: 'name', value: 'id' }
            )
          ) as any
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  cancel: false
});

const clickEdit = (row: { [x: string]: any }) => {
  data.BiddingCompany = [];
  data.getbiddingInformation(row.code, row);
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目类别
  projectType: [],
  // 招标公司
  BiddingCompany: [] as any[],
  selected: {},
  getOptions: () => {
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || []);
    });
  },
  getbiddingInformation: (projectCode: string, row) => {
    getBidding({ page: 1, size: -1, projectCode }).then((res) => {
      refForm.value!.refForm!.dataForm = {
        projectCode: row.code,
        biddingId: res.data.data.data[0]?.id,
        ...(res.data.data.data[0] || {})
      };
      data.getBiddingCompanyValue(res.data.data.data[0]?.projectCode);
    });
  },
  getBiddingCompanyValue: (projectCode: string) => {
    getBiddingCompany({ page: 1, size: -1, projectCode }).then((res) => {
      data.BiddingCompany = (res.data.data.data || []).map((item, index) => {
        item.num = index;
        return item;
      });
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params?.time) {
    params.startTimeFrom = params.time[0];
    params.startTimeTo = params.time[1];
    delete params.time;
  }
  getProjectList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
});
</script>
