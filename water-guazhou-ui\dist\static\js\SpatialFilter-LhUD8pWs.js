import{f as I,s as _,t as f,o as m,n as y}from"./WhereClause-CNjGNHY9.js";import{c as J,T as j}from"./arcadeTimeUtils-CyWQANWo.js";import{a$ as L}from"./index-r0dFAfgr.js";import{af as h,ag as x,ah as O,ai as W,aj as z,ak as Z,al as X,am as b,an as ee,ao as te,ap as G,aq as M,ar as $}from"./arcadeUtils-1twpZNeO.js";import{dS as v,c7 as ae}from"./MapView-DaoQedLH.js";import{b as re,a3 as U}from"./Point-WxyopZva.js";import{b as se,K as ne,W as ie,M as le,F as ce,R as ue,m as oe,S as he,x as de,O as pe,p as _e,h as B}from"./geometryEngineAsync-Cl4J6jqd.js";class A{constructor(){this._databaseTypeMetaData={},this._layerInfo={}}clearDatabaseType(e){this._databaseTypeMetaData[e]===void 0&&delete this._databaseTypeMetaData[e]}getDatabaseType(e){return e==="MUSTBESET"||this._databaseTypeMetaData[e]===void 0?null:this._databaseTypeMetaData[e]}setDatabaseType(e,t){this._databaseTypeMetaData[e]=t}getLayerInfo(e){return this._layerInfo[e]===void 0?null:this._layerInfo[e]}setLayerInfo(e,t){this._layerInfo[e]=t}clearLayerInfo(e){this._layerInfo[e]!==void 0&&delete this._layerInfo[e]}}A.applicationCache=null;class fe{constructor(e,t){this._lastId=-1,this._progress=t,this._parent=e}reset(){this._lastId=-1}nextBatch(e){if(this._parent._mainSetInUse!==null)return this._parent._mainSetInUse.then(r=>this.nextBatch(e),r=>this.nextBatch(e));const t={returnpromise:null,hasset:!1},a=[];return t.returnpromise=new Promise((r,s)=>{this._parent._getSet(this._progress).then(i=>{const l=L(i._known,"known");let c=l.length-1;if(l[l.length-1]==="GETPAGES"&&(c-=1),this._lastId+e>c&&l.length>0&&l[l.length-1]==="GETPAGES")return void this._parent._expandPagedSet(i,this._parent._maxQueryRate(),0,0,this._progress).then(u=>{t.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(e).then(r,s)},u=>{t.hasset=!0,this._parent._mainSetInUse=null,s(u)});const p=L(i._candidates,"candidates");if(c>=this._lastId+e||p.length===0){for(let u=0;u<e;u++){const g=u+this._lastId+1;if(g>=l.length)break;a[u]=l[g]}return this._lastId+=a.length,a.length===0&&(t.hasset=!0,this._parent._mainSetInUse=null,r([])),void this._parent._getFeatureBatch(a,this._progress).then(u=>{t.hasset=!0,this._parent._mainSetInUse=null,r(u)},u=>{t.hasset=!0,this._parent._mainSetInUse=null,s(u)})}this._parent._refineSetBlock(i,this._parent._maxProcessingRate(),this._progress).then(()=>{t.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(e).then(r,s)},u=>{t.hasset=!0,this._parent._mainSetInUse=null,s(u)})},i=>{t.hasset=!0,this._parent._mainSetInUse=null,s(i)})}),t.hasset===!1&&(this._parent._mainSetInUse=t.returnpromise,t.hasset=!0),t.returnpromise}next(){if(this._parent._mainSetInUse!==null)return this._parent._mainSetInUse.then(t=>this.next(),t=>this.next());const e={returnpromise:null,hasset:!1};return e.returnpromise=new Promise((t,a)=>{this._parent._getSet(this._progress).then(r=>{const s=L(r._known,"known");this._lastId<s.length-1?s[this._lastId+1]==="GETPAGES"?this._parent._expandPagedSet(r,this._parent._maxQueryRate(),0,0,this._progress).then(i=>(e.hasset=!0,this._parent._mainSetInUse=null,this.next())).then(t,a):(this._lastId+=1,this._parent._getFeature(r,s[this._lastId],this._progress).then(i=>{e.hasset=!0,this._parent._mainSetInUse=null,t(i)},i=>{e.hasset=!0,this._parent._mainSetInUse=null,a(i)})):L(r._candidates,"candidates").length>0?this._parent._refineSetBlock(r,this._parent._maxProcessingRate(),this._progress).then(()=>{e.hasset=!0,this._parent._mainSetInUse=null,this.next().then(t,a)},i=>{e.hasset=!0,this._parent._mainSetInUse=null,a(i)}):(e.hasset=!0,this._parent._mainSetInUse=null,t(null))},r=>{e.hasset=!0,this._parent._mainSetInUse=null,a(r)})}),e.hasset===!1&&(this._parent._mainSetInUse=e.returnpromise,e.hasset=!0),e.returnpromise}async count(){if(this._parent._totalCount!==-1)return this._parent._totalCount;const e=await this._parent._getSet(this._progress),t=await this._refineAllSets(e);return this._parent._totalCount=t._known.length,this._parent._totalCount}async _refineAllSets(e){if(e._known.length>0&&e._known[e._known.length-1]==="GETPAGES")return await this._parent._expandPagedSet(e,this._parent._maxQueryRate(),0,1,this._progress),this._refineAllSets(e);if(e._candidates.length>0){if(e._known[e._candidates.length-1]==="GETPAGES")return await this._parent._expandPagedSet(e,this._parent._maxQueryRate(),0,2,this._progress),this._refineAllSets(e);const t=await this._parent._refineSetBlock(e,this._parent._maxProcessingRate(),this._progress);return t._candidates.length>0?this._refineAllSets(t):t}return e}}class R{constructor(e,t,a,r){this._lastFetchedIndex=0,this._ordered=!1,this.pagesDefinition=null,this._candidates=e,this._known=t,this._ordered=a,this.pagesDefinition=r}}function H(n,e){return o(n==null?void 0:n.parseTree,e,n==null?void 0:n.parameters)}function Me(n,e,t){return o(n,e,t)}function $e(n,e,t,a){return I.create(o(n.parseTree,h.Standardised,n.parameters,e,t),a)}function Ue(n,e,t="AND"){return I.create("(("+H(n,h.Standardised)+")"+t+"("+H(e,h.Standardised)+"))",n.fieldsIndex)}function o(n,e,t,a=null,r=null,s=null){let i,l,c,p;switch(n.type){case"interval":return we(o(n.value,e,t,a,r,s),n.qualifier,n.op);case"case-expression":{let u=" CASE ";n.format==="simple"&&(u+=o(n.operand,e,t,a,r,s));for(let g=0;g<n.clauses.length;g++)u+=" WHEN "+o(n.clauses[g].operand,e,t,a,r,s)+" THEN "+o(n.clauses[g].value,e,t,a,r,s);return n.else!==null&&(u+=" ELSE "+o(n.else,e,t,a,r,s)),u+=" END ",u}case"parameter":{const u=t[n.value.toLowerCase()];if(typeof u=="string")return"'"+t[n.value.toLowerCase()].toString().replace(/'/g,"''")+"'";if(x(u))return k(u,e,s);if(O(u))return Y(u,e,s);if(u instanceof Array){const g=[];for(let T=0;T<u.length;T++)typeof u[T]=="string"?g.push("'"+u[T].toString().replace(/'/g,"''")+"'"):x(u[T])?g.push(k(u[T],e,s)):O(u[T])?g.push(Y(u[T],e,s)):g.push(u[T].toString());return g}return u.toString()}case"expression-list":l=[];for(const u of n.value)l.push(o(u,e,t,a,r,s));return l;case"unary-expression":return" ( NOT "+o(n.expr,e,t,a,r,s)+" ) ";case"binary-expression":switch(n.operator){case"AND":return" ("+o(n.left,e,t,a,r,s)+" AND "+o(n.right,e,t,a,r,s)+") ";case"OR":return" ("+o(n.left,e,t,a,r,s)+" OR "+o(n.right,e,t,a,r,s)+") ";case"IS":if(n.right.type!=="null")throw new _(f.UnsupportedIsRhs);return" ("+o(n.left,e,t,a,r,s)+" IS NULL )";case"ISNOT":if(n.right.type!=="null")throw new _(f.UnsupportedIsRhs);return" ("+o(n.left,e,t,a,r,s)+" IS NOT NULL )";case"IN":return i=[],n.right.type==="expression-list"?(i=o(n.right,e,t,a,r)," ("+o(n.left,e,t,a,r,s)+" IN ("+i.join(",")+")) "):(p=o(n.right,e,t,a,r,s),p instanceof Array?" ("+o(n.left,e,t,a,r,s)+" IN ("+p.join(",")+")) ":" ("+o(n.left,e,t,a,r,s)+" IN ("+p+")) ");case"NOT IN":return i=[],n.right.type==="expression-list"?(i=o(n.right,e,t,a,r)," ("+o(n.left,e,t,a,r,s)+" NOT IN ("+i.join(",")+")) "):(p=o(n.right,e,t,a,r,s),p instanceof Array?" ("+o(n.left,e,t,a,r,s)+" NOT IN ("+p.join(",")+")) ":" ("+o(n.left,e,t,a,r,s)+" NOT IN ("+p+")) ");case"BETWEEN":return c=o(n.right,e,t,a,r,s)," ("+o(n.left,e,t,a,r,s)+" BETWEEN "+c[0]+" AND "+c[1]+" ) ";case"NOTBETWEEN":return c=o(n.right,e,t,a,r,s)," ("+o(n.left,e,t,a,r,s)+" NOT BETWEEN "+c[0]+" AND "+c[1]+" ) ";case"LIKE":return n.escape!==""?" ("+o(n.left,e,t,a,r,s)+" LIKE "+o(n.right,e,t,a,r,s)+" ESCAPE '"+n.escape+"') ":" ("+o(n.left,e,t,a,r,s)+" LIKE "+o(n.right,e,t,a,r,s)+") ";case"NOT LIKE":return n.escape!==""?" ("+o(n.left,e,t,a,r,s)+" NOT LIKE "+o(n.right,e,t,a,r,s)+" ESCAPE '"+n.escape+"') ":" ("+o(n.left,e,t,a,r,s)+" NOT LIKE "+o(n.right,e,t,a,r,s)+") ";case"<>":case"<":case">":case">=":case"<=":case"=":case"*":case"-":case"+":case"/":return" ("+o(n.left,e,t,a,r,s)+" "+n.operator+" "+o(n.right,e,t,a,r,s)+") ";case"||":return" ("+o(n.left,e,t,a,r,s)+" "+(e===h.SqlServer?"+":n.operator)+" "+o(n.right,e,t,a,r,s)+") "}throw new _(f.UnsupportedOperator,{operator:n.operator});case"null":return"null";case"boolean":return n.value===!0?"1":"0";case"string":return"'"+n.value.toString().replace(/'/g,"''")+"'";case"timestamp":case"date":return k(n.value,e,s);case"number":return n.value.toString();case"current-time":return me(n.mode==="date",e);case"column-reference":return a&&a.toLowerCase()===n.column.toLowerCase()?"("+r+")":n.column;case"data-type":return n.value;case"function":{const u=o(n.args,e,t,a,r,s);return ge(n.name,u,e)}}throw new _(f.UnsupportedSyntax,{node:n.type})}function ge(n,e,t){switch(n.toLowerCase().trim()){case"cos":case"sin":case"tan":case"cosh":case"tanh":case"sinh":case"acos":case"asin":case"atan":case"floor":case"log10":case"log":case"abs":if(e.length!==1)throw new _(f.InvalidFunctionParameters,{function:n.toLowerCase().trim()});return`${n.toUpperCase().trim()}(${e[0]})`;case"ceiling":case"ceil":if(e.length!==1)throw new _(f.InvalidFunctionParameters,{function:"ceiling"});switch(t){case h.Standardised:case h.StandardisedNoInterval:}return"CEILING("+e[0]+")";case"mod":case"power":case"nullif":if(e.length!==2)throw new _(f.InvalidFunctionParameters,{function:n.toLowerCase().trim()});return`${n.toUpperCase().trim()}(${e[0]},${e[1]})`;case"round":if(e.length===2)return"ROUND("+e[0]+","+e[1]+")";if(e.length===1)return"ROUND("+e[0]+")";throw new _(f.InvalidFunctionParameters,{function:"round"});case"truncate":if(e.length<1||e.length>2)throw new _(f.InvalidFunctionParameters,{function:"truncate"});return t===h.SqlServer?"ROUND("+e[0]+(e.length===1?"0":","+e[1])+",1)":"TRUNCATE("+e[0]+(e.length===1?")":","+e[1]+")");case"char_length":case"len":if(e.length!==1)throw new _(f.InvalidFunctionParameters,{function:"char_length"});switch(t){case h.SqlServer:return"LEN("+e[0]+")";case h.Oracle:return"LENGTH("+e[0]+")";default:return"CHAR_LENGTH("+e[0]+")"}case"coalesce":case"concat":{if(e.length<1)throw new _(f.InvalidFunctionParameters,{function:n.toLowerCase()});let a=n.toUpperCase().trim()+"(";for(let r=0;r<e.length;r++)r!==0&&(a+=","),a+=e[r];return a+=")",a}case"lower":case"lcase":if(e.length!==1)throw new _(f.InvalidFunctionParameters,{function:"lower"});return"LOWER("+e[0]+")";case"upper":case"ucase":if(e.length!==1)throw new _(f.InvalidFunctionParameters,{function:"upper"});return"UPPER("+e[0]+")";case"substring":{let a="";switch(t){case h.Oracle:return a="SUBSTR("+e[0]+","+e[1],e.length===3&&(a+=","+e[2]),a+=")",a;case h.SqlServer:return a=e.length===3?"SUBSTRING("+e[0]+","+e[1]+","+e[2]+")":"SUBSTRING("+e[0]+",  "+e[1]+", LEN("+e[0]+") - "+e[1]+")",a;default:return a="SUBSTRING("+e[0]+" FROM "+e[1],e.length===3&&(a+=" FOR "+e[2]),a+=")",a}}case"extract":return"EXTRACT("+e[0].replace(/\'/g,"")+" FROM "+e[1]+")";case"cast":{let a="";switch(t){case h.Oracle:switch(e[1].type){case"date":a="DATE";break;case"float":a="DOUBLE";break;case"integer":a="INTEGER";break;case"real":a="REAL";break;case"smallint":a="SMALLINT";break;case"timestamp":a="TIMESTAMP";break;case"varchar":a="VARCHAR("+e[1].size.toString()+")"}return`CAST(${e[0]} AS ${a})`;case h.Postgres:switch(e[1].type){case"date":a="DATE";break;case"float":a="DOUBLE PRECISION";break;case"integer":a="INT";break;case"real":a="REAL";break;case"smallint":a="SMALLINT";break;case"timestamp":a="TIMESTAMP";break;case"varchar":a="VARCHAR("+e[1].size.toString()+")"}return`CAST(${e[0]} AS ${a})`;case h.SqlServer:switch(e[1].type){case"date":a="DATE";break;case"float":a="FLOAT";break;case"integer":a="INT";break;case"real":a="REAL";break;case"smallint":a="SMALLINT";break;case"timestamp":a="DATETIME";break;case"varchar":a="VARCHAR("+e[1].size.toString()+")"}return`CAST(${e[0]} AS ${a})`;default:switch(e[1].type){case"date":a="DATE";break;case"float":a="FLOAT";break;case"integer":a="INTEGER";break;case"real":a="REAL";break;case"smallint":a="SMALLINT";break;case"timestamp":a="TIMESTAMP";break;case"varchar":a="VARCHAR("+e[1].size.toString()+")"}return`CAST(${e[0]} AS ${a})`}}}throw new _(f.InvalidFunctionParameters,{function:n})}function Y(n,e,t,a){t!=null&&t.outputTimeReference&&(n=J.arcadeDateAndZoneToArcadeDate(n,t==null?void 0:t.outputTimeReference));const r=n.toDateTime(),s=r.hour===0&&r.minute===0&&r.second===0&&r.millisecond===0;switch(e){case h.FILEGDB:case h.Standardised:case h.StandardisedNoInterval:return s?`date '${r.toFormat("yyyy-LL-dd")}'`:`date '${r.toFormat("yyyy-LL-dd HH:mm:ss")}'`;case h.Oracle:return s?`TO_DATE('${r.toFormat("yyyy-LL-dd")}','YYYY-MM-DD')`:`TO_DATE('${r.toFormat("yyyy-LL-dd HH:mm:ss")}','YYYY-MM-DD HH24:MI:SS')`;case h.SqlServer:return`'${r.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;case h.PGDB:return`#${r.toFormat(s?"LL-dd-yyyy":"LL-dd-yyyy HH:mm:ss")}#`;case h.Postgres:return`TIMESTAMP '${r.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;default:return`date '${r.toFormat("yyyy-LL-dd HH:mm:ss")}'`}}function k(n,e,t,a){let r=x(n)?v.fromJSDate(n):v.fromSQL(n);const s=r.hour===0&&r.minute===0&&r.second===0&&r.millisecond===0;switch(t!=null&&t.inputTimeReference&&(r=v.fromObject({day:r.day,year:r.year,month:r.month,hour:r.hour,minute:r.minute,second:r.second,millisecond:r.millisecond},{zone:t.inputTimeReference})),t!=null&&t.outputTimeReference&&(r=r.setZone(t.outputTimeReference)),e){case h.FILEGDB:case h.Standardised:case h.StandardisedNoInterval:return s?`date '${r.toFormat("yyyy-LL-dd")}'`:`date '${r.toFormat("yyyy-LL-dd HH:mm:ss")}'`;case h.Oracle:return s?`TO_DATE('${r.toFormat("yyyy-LL-dd")}','YYYY-MM-DD')`:`TO_DATE('${r.toFormat("yyyy-LL-dd HH:mm:ss")}','YYYY-MM-DD HH24:MI:SS')`;case h.SqlServer:return`'${r.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;case h.PGDB:return`#${r.toFormat(s?"LL-dd-yyyy":"LL-dd-yyyy HH:mm:ss")}#`;case h.Postgres:return`TIMESTAMP '${r.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;default:return`date '${r.toFormat("yyyy-LL-dd HH:mm:ss")}'`}}function me(n,e){switch(e){case h.FILEGDB:case h.Standardised:case h.StandardisedNoInterval:case h.Oracle:return n?"CURRENT_DATE":"CURRENT_TIMESTAMP";case h.SqlServer:return n?"CAST(GETDATE() AS DATE)":"GETDATE()";case h.PGDB:case h.Postgres:default:return n?"CURRENT_DATE":"CURRENT_TIMESTAMP"}}function ye(n,e,t={}){const a={},r={},s={esriFieldTypeSmallInteger:"integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"double",esriFieldTypeDouble:"double",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"integer",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"guid",oid:"integer",long:"integer","small-integer":"integer",integer:"integer",single:"double",double:"double",date:"date",guid:"guid",globalid:"guid",string:"string"};for(const i of e){const l=i.type?s[i.type]:void 0;a[i.name.toLowerCase()]=l===void 0?"":l}for(const i in t){const l=s[t[i]];r[i.toLowerCase()]=l===void 0?"":l}switch(S(a,n.parseTree,n.parameters,r)){case"double":return"double";case"integer":return"integer";case"date":return"date";case"string":return"string";case"global-id":case"guid":return"guid"}return""}function S(n,e,t,a){var s,i;let r;switch(e.type){case"interval":return"integer";case"case-expression":{const l=[];if(e.format==="simple"){for(let c=0;c<e.clauses.length;c++)l.push(S(n,e.clauses[c].value,t,a));e.else!==null&&l.push(S(n,e.else,t,a))}else{for(let c=0;c<e.clauses.length;c++)l.push(S(n,e.else,t,a));e.else!==null&&l.push(S(n,e.else,t,a))}return N(l)}case"parameter":{const l=a[e.value.toLowerCase()];if(l===void 0&&t){const c=t[e.value.toLowerCase()];if(c===void 0||c===null)return"";if(typeof c=="string"||c instanceof String)return"string";if(typeof c=="boolean")return"boolean";if(x(c)||O(c))return"date";if(typeof c=="number")return c%1==0?"integer":"double"}return l===void 0?"":l}case"expression-list":{const l=[];for(const c of e.value)l.push(S(n,c,t,a));return l}case"unary-expression":return"boolean";case"binary-expression":switch(e.operator){case"AND":case"OR":case"IN":case"NOT IN":case"BETWEEN":case"NOTBETWEEN":case"LIKE":case"NOT LIKE":case"<>":case"<":case">":case">=":case"<=":case"=":return"boolean";case"IS":case"ISNOT":if(e.right.type!=="null")throw new _(f.UnsupportedIsRhs);return"boolean";case"*":case"-":case"+":case"/":return N([S(n,e.left,t,a),S(n,e.right,t,a)]);case"||":return"string";default:throw new _(f.UnsupportedOperator,{operator:e.operator})}case"null":return"";case"boolean":return"boolean";case"string":return"string";case"number":return e.value===null?"":e.value%1==0?"integer":"double";case"date":case"timestamp":case"current-time":return"date";case"column-reference":{const l=n[e.column.toLowerCase()];return l===void 0?"":l}case"function":switch(e.name.toLowerCase()){case"cast":switch(((i=(s=e.args)==null?void 0:s.value[1])==null?void 0:i.value.type)??""){case"integer":case"smallint":return"integer";case"real":case"float":return"double";case"date":case"timestamp":return"date";case"varchar":return"string";default:return""}case"position":case"extract":case"char_length":case"mod":return"integer";case"round":if(r=S(n,e.args,t,a),r instanceof Array){if(r.length<=0)return"double";r=r[0]}return r;case"sign":return"integer";case"ceiling":case"floor":case"abs":return r=S(n,e.args,t,a),r instanceof Array&&(r=N(r)),r==="integer"||r==="double"?r:"double";case"area":case"length":case"log":case"log10":case"sin":case"cos":case"tan":case"asin":case"acos":case"atan":case"cosh":case"sinh":case"tanh":case"power":return"double";case"substring":case"trim":case"concat":case"lower":case"upper":return"string";case"truncate":return"double";case"nullif":case"coalesce":return r=S(n,e.args,t,a),r instanceof Array?r.length>0?r[0]:"":r}return""}throw new _(f.UnsupportedSyntax,{node:e.type})}const Q={boolean:1,string:2,integer:3,double:4,date:5};function N(n){if(n){let e="";for(const t of n)t!==""&&(e=e===""||Q[e]<Q[t]?t:e);return e}return""}function Be(n,e){return w(n.parseTree,e)}function Se(n){return(n==null?void 0:n.parseTree.type)==="column-reference"}function w(n,e){if(n==null)return!1;switch(n.type){case"when-clause":return w(n.operand,e)||w(n.value,e);case"case-expression":for(const t of n.clauses)if(w(t,e))return!0;return!(n.format!=="simple"||!w(n.operand,e))||!(n.else===null||!w(n.else,e));case"parameter":case"null":case"boolean":case"date":case"timestamp":case"string":case"number":return!1;case"expression-list":for(const t of n.value)if(w(t,e))return!0;return!1;case"unary-expression":return w(n.expr,e);case"binary-expression":return w(n.left,e)||w(n.right,e);case"column-reference":return e.toLowerCase()===n.column.toLowerCase();case"function":return w(n.args,e)}return!1}function C(n){let e="";return e+=n.period.toUpperCase(),e}function we(n,e,t){let a="";return a=e.type==="interval-period"?C(e):C(e.start)+" TO "+C(e.end),"INTERVAL "+t+" "+n+" "+a}function Ie(n){return n=+n,isFinite(n)?n-n%1||(n<0?-0:n===0?n:0):n}function P(n){let e=0;for(let t=0;t<n.length;t++)e+=n[t];return e/n.length}function K(n){const e=P(n);let t=0;for(let a=0;a<n.length;a++)t+=(e-n[a])**2;return t/n.length}function V(n){const e=P(n);let t=0;for(let a=0;a<n.length;a++)t+=(e-n[a])**2;return t/(n.length-1)}function q(n){let e=0;for(let t=0;t<n.length;t++)e+=n[t];return e}function Te(n,e){const t=[],a={},r=[];for(let s=0;s<n.length;s++){if(n[s]!==void 0&&n[s]!==null){const i=n[s];if(W(i)||z(i))a[i]===void 0&&(t.push(i),a[i]=1);else{let l=!1;for(let c=0;c<r.length;c++)Z(r[c],i)===!0&&(l=!0);l===!1&&(r.push(i),t.push(i))}}if(t.length>=e&&e!==-1)return t}return t}function He(n){switch(n.toLowerCase()){case"distinct":return"distinct";case"avg":case"mean":return"avg";case"min":return"min";case"sum":return"sum";case"max":return"max";case"stdev":case"stddev":return"stddev";case"var":case"variance":return"var";case"count":return"count"}return""}function Ye(n,e,t=1e3){switch(n.toLowerCase()){case"distinct":return Te(e,t);case"avg":case"mean":return P(e);case"min":return Math.min.apply(Math,e);case"sum":return q(e);case"max":return Math.max.apply(Math,e);case"stdev":case"stddev":return Math.sqrt(K(e));case"var":case"variance":return K(e);case"count":return e.length}return 0}async function Fe(n,e,t){const a=await D(n,e,t,!0);return a.length===0?null:Math.min.apply(Math,a)}async function be(n,e,t){const a=await D(n,e,t,!0);return a.length===0?null:Math.max.apply(Math,a)}async function Ee(n,e,t){let a="";e&&!Se(e)&&(a=ye(e,n.fields));const r=await D(n,e,t,!0);if(r.length===0)return null;const s=P(r);return s===null?s:a==="integer"?Ie(s):s}async function Re(n,e,t){const a=await D(n,e,t,!0);return a.length===0?null:V(a)}async function Ae(n,e,t){const a=await D(n,e,t,!0);return a.length===0?null:Math.sqrt(V(a))}async function De(n,e,t){const a=await D(n,e,t,!0);return a.length===0?null:q(a)}async function Le(n,e){return n.iterator(e).count()}async function D(n,e,t,a=!1){const r=n.iterator(t),s=[],i={ticker:0};let l=await r.next();for(;l!==null;){if(i.ticker++,t.aborted)throw new m(y.Cancelled);i.ticker%100==0&&(i.ticker=0,await new Promise(p=>{setTimeout(p,0)}));const c=e==null?void 0:e.calculateValue(l);c===null?a===!1&&(s[s.length]=c):s[s.length]=c,l=await r.next()}return s}async function xe(n,e,t=1e3,a=null){const r=n.iterator(a),s=[],i={},l={ticker:0};let c=await r.next();for(;c!==null;){if(l.ticker++,a&&a.aborted)throw new m(y.Cancelled);l.ticker%100==0&&(l.ticker=0,await new Promise(u=>{setTimeout(u,0)}));const p=e==null?void 0:e.calculateValue(c);if(p!=null&&i[p]===void 0&&(s.push(p),i[p]=1),s.length>=t&&t!==-1)return s;c=await r.next()}return s}class d{constructor(e){this.recentlyUsedQueries=null,this.featureSetQueryInterceptor=null,this._idstates=[],this._parent=null,this._wset=null,this._mainSetInUse=null,this._maxProcessing=200,this._maxQuery=500,this._totalCount=-1,this._databaseType=h.NotEvaluated,this._databaseTypeProbed=null,this.declaredRootClass="esri.arcade.featureset.support.FeatureSet",this._featureCache=[],this.typeIdField=null,this.types=null,this.fields=null,this.geometryType="",this.objectIdField="",this.globalIdField="",this.spatialReference=null,this.hasM=!1,this.hasZ=!1,this._transparent=!1,this.loaded=!1,this._loadPromise=null,this._fieldsIndex=null,this._dateFieldIndex=null,e&&e.lrucache&&(this.recentlyUsedQueries=e.lrucache),e&&e.interceptor&&(this.featureSetQueryInterceptor=e.interceptor)}optimisePagingFeatureQueries(e){this._parent&&this._parent.optimisePagingFeatureQueries(e)}_hasMemorySource(){return!0}prop(e,t){return t===void 0?this[e]:(this[e]!==void 0&&(this[e]=t),this)}end(){return this._parent!==null&&this._parent._transparent===!0?this._parent.end():this._parent}_ensureLoaded(){return this.load()}load(){return this._loadPromise===null&&(this._loadPromise=this.loadImpl()),this._loadPromise}async loadImpl(){var e,t;return((e=this._parent)==null?void 0:e.loaded)===!0?(this._initialiseFeatureSet(),this):(await((t=this._parent)==null?void 0:t.load()),this._initialiseFeatureSet(),this)}_initialiseFeatureSet(){this._parent!==null?(this.fields=this._parent.fields.slice(0),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.fields=[],this.typeIdField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new re({wkid:4326}),this.geometryType=X.point)}getField(e,t){let a;return(t=t||this.fields)&&(e=e.toLowerCase(),t.some(r=>(r&&r.name.toLowerCase()===e&&(a=r),!!a))),a}getFieldsIndex(){return this._fieldsIndex===null&&(this._fieldsIndex=new ae(this.fields)),this._fieldsIndex}_maxProcessingRate(){return this._parent!==null?Math.min(this._maxProcessing,this._parent._maxProcessingRate()):Math.min(this._maxProcessing,this._maxQueryRate())}_maxQueryRate(){return this._parent!==null?Math.max(this._maxQuery,this._parent._maxQueryRate()):this._maxQuery}_checkCancelled(e){if(e!=null&&e.aborted)throw new m(y.Cancelled)}nativeCapabilities(){return this._parent.nativeCapabilities()}async _canDoAggregates(e,t,a,r,s){return this._parent!==null&&this._parent._canDoAggregates(e,t,a,r,s)}async _getAggregatePagesDataSourceDefinition(e,t,a,r,s,i,l){if(this._parent===null)throw new m(y.NeverReach);return this._parent._getAggregatePagesDataSourceDefinition(e,t,a,r,s,i,l)}async _getAgregagtePhysicalPage(e,t,a){if(this._parent===null)throw new m(y.NeverReach);return this._parent._getAgregagtePhysicalPage(e,t,a)}async databaseType(){if(this._databaseType===h.NotEvaluated){if(A.applicationCache!==null){const e=A.applicationCache.getDatabaseType(this._cacheableFeatureSetSourceKey());if(e!==null)return e}if(this._databaseTypeProbed!==null)return this._databaseTypeProbed;try{this._databaseTypeProbed=this._getDatabaseTypeImpl(),A.applicationCache!==null&&A.applicationCache.setDatabaseType(this._cacheableFeatureSetSourceKey(),this._databaseTypeProbed)}catch(e){throw A.applicationCache!==null&&A.applicationCache.clearDatabaseType(this._cacheableFeatureSetSourceKey()),e}return this._databaseTypeProbed}return this._databaseType}async _getDatabaseTypeImpl(){const e=[{thetype:h.SqlServer,testwhere:"(CAST( '2015-01-01' as DATETIME) = CAST( '2015-01-01' as DATETIME)) AND OBJECTID<0"},{thetype:h.Oracle,testwhere:"(TO_DATE('2003-11-18','YYYY-MM-DD') = TO_DATE('2003-11-18','YYYY-MM-DD')) AND OBJECTID<0"},{thetype:h.StandardisedNoInterval,testwhere:"(date '2015-01-01 10:10:10' = date '2015-01-01 10:10:10') AND OBJECTID<0"}];for(const t of e)if(await this._runDatabaseProbe(t.testwhere)===!0)return t.thetype;return h.StandardisedNoInterval}_cacheableFeatureSetSourceKey(){return"MUSTBESET"}async _runDatabaseProbe(e){if(this._parent!==null)return this._parent._runDatabaseProbe(e);throw new m(y.NotImplemented)}isTable(){var e;return((e=this._parent)==null?void 0:e.isTable())??!1}_featureFromCache(e){if(this._featureCache[e]!==void 0)return this._featureCache[e]}_isInFeatureSet(e){return b.Unknown}_getSet(e){throw new m(y.NotImplemented)}async _getFeature(e,t,a){if(this._checkCancelled(a),this._featureFromCache(t)!==void 0)return this._featureFromCache(t);if(await this._getFeatures(e,t,this._maxProcessingRate(),a),this._checkCancelled(a),this._featureFromCache(t)!==void 0)return this._featureFromCache(t);throw new m(y.MissingFeatures)}async _getFeatureBatch(e,t){this._checkCancelled(t);const a=new R([],e,!1,null),r=[];await this._getFeatures(a,-1,e.length,t),this._checkCancelled(t);for(const s of e)this._featureFromCache(s)!==void 0&&r.push(this._featureFromCache(s));return r}async _getFeatures(e,t,a,r){return"success"}_getFilteredSet(e,t,a,r,s){throw new m(y.NotImplemented)}async _refineSetBlock(e,t,a){if(this._checkIfNeedToExpandCandidatePage(e,this._maxQueryRate())===!0)return await this._expandPagedSet(e,this._maxQueryRate(),0,0,a),this._refineSetBlock(e,t,a);this._checkCancelled(a);const r=e._candidates.length;this._refineKnowns(e,t);let s=r-e._candidates.length;if(e._candidates.length===0||s>=t)return e;if(await this._refineIfParentKnown(e,t-s,a),this._checkCancelled(a),this._refineKnowns(e,t-s),s=r-e._candidates.length,s<t&&e._candidates.length>0){const i=t-s,l=this._prepareFetchAndRefineSet(e._candidates);return await this._fetchAndRefineFeatures(l,l.length>i?i:e._candidates.length,a),this._checkCancelled(a),this._refineKnowns(e,t-s),e}return e}_fetchAndRefineFeatures(e,t,a){return null}_prepareFetchAndRefineSet(e){const t=[];for(let a=0;a<e.length;a++)this._isPhysicalFeature(e[a])&&t.push(e[a]);return t}_isPhysicalFeature(e){return this._parent===null||this._parent._isPhysicalFeature(e)}_refineKnowns(e,t){let a=0,r=null;const s=[];t=this._maxQueryRate();for(let i=0;i<e._candidates.length&&e._candidates[i]!=="GETPAGES";i++){let l=!1;const c=this._candidateIdTransform(e._candidates[i]);c!==e._candidates[i]&&(l=!0);const p=this._isInFeatureSet(c);if(p===b.InFeatureSet)l===!0?e._known.includes(c)||(e._known.push(c),a+=1):(e._known.push(e._candidates[i]),a+=1),r===null?r={start:i,end:i}:r.end===i-1?r.end=i:(s.push(r),r={start:i,end:i});else if(p===b.NotInFeatureSet)r===null?r={start:i,end:i}:r.end===i-1?r.end=i:(s.push(r),r={start:i,end:i}),a+=1;else if(p===b.Unknown&&(a+=1,e._ordered===!0))break;if(a>=t)break}r!==null&&s.push(r);for(let i=s.length-1;i>=0;i--)e._candidates.splice(s[i].start,s[i].end-s[i].start+1)}_refineIfParentKnown(e,t,a){const r=new R([],[],e._ordered,null);return r._candidates=e._candidates.slice(0),this._parent._refineSetBlock(r,t,a)}_candidateIdTransform(e){return this._parent._candidateIdTransform(e)}_checkIfNeedToExpandKnownPage(e,t){if(e.pagesDefinition===null)return!1;let a=0;for(let r=e._lastFetchedIndex;r<e._known.length;r++){if(e._known[r]==="GETPAGES")return!0;if(this._featureCache[e._known[r]]===void 0&&(a+=1,a>=t))break}return!1}_checkIfNeedToExpandCandidatePage(e,t){if(e.pagesDefinition===null)return!1;let a=0;for(let r=0;r<e._candidates.length;r++){if(e._candidates[r]==="GETPAGES")return!0;if(a+=1,a>=t)break}return!1}async _expandPagedSet(e,t,a,r,s){if(this._parent===null)throw new m(y.NotImplemented);return this._parent._expandPagedSet(e,t,a,r,s)}async _expandPagedSetFeatureSet(e,t,a,r,s){if(e._known.length>0&&e._known[e._known.length-1]==="GETPAGES"&&(r=1),r===0&&e._candidates.length>0&&e._candidates[e._candidates.length-1]==="GETPAGES"&&(r=2),r===0)return"finished";const i=await this._getPage(e,r,s);return a+i<t?this._expandPagedSet(e,t,a+i,0,s):"success"}async _getPage(e,t,a){const r=t===1?e._known:e._candidates;if(e.pagesDefinition.internal.set.length>e.pagesDefinition.resultOffset||e.pagesDefinition.internal.fullyResolved===!0){r.length=r.length-1;let s=0;for(let l=0;l<e.pagesDefinition.resultRecordCount&&!(e.pagesDefinition.resultOffset+l>=e.pagesDefinition.internal.set.length);l++)r[r.length]=e.pagesDefinition.internal.set[e.pagesDefinition.resultOffset+l],s++;e.pagesDefinition.resultOffset+=s;let i=!1;return e.pagesDefinition.internal.fullyResolved===!0&&e.pagesDefinition.internal.set.length<=e.pagesDefinition.resultOffset&&(i=!0),i===!1&&r.push("GETPAGES"),s}return await this._getPhysicalPage(e,t,a),this._getPage(e,t,a)}_getPhysicalPage(e,t,a){return null}_clonePageDefinition(e){return this._parent===null?null:this._parent._clonePageDefinition(e)}_first(e){return this.iterator(e).next()}first(e){return this._first(e)}async calculateStatistic(e,t,a,r){await this._ensureLoaded();let s=await this._stat(e,t,"",null,null,a,r);return s.calculated===!1&&(s=await this._manualStat(e,t,a,r)),s.result}async _manualStat(e,t,a,r){let s=null;switch(e.toLowerCase()){case"count":return s=await Le(this,r),{calculated:!0,result:s};case"distinct":return s=await xe(this,t,a,r),{calculated:!0,result:s};case"avg":case"mean":return s=await Ee(this,t,r),{calculated:!0,result:s};case"stdev":return s=await Ae(this,t,r),{calculated:!0,result:s};case"variance":return s=await Re(this,t,r),{calculated:!0,result:s};case"sum":return s=await De(this,t,r),{calculated:!0,result:s};case"min":return s=await Fe(this,t,r),{calculated:!0,result:s};case"max":return s=await be(this,t,r),{calculated:!0,result:s};default:return{calculated:!0,result:0}}}async _stat(e,t,a,r,s,i,l){const c=await this._parent._stat(e,t,a,r,s,i,l);return c.calculated===!1?s===null&&a===""&&r===null?this._manualStat(e,t,i,l):{calculated:!1}:c}_unionAllGeomSelf(e){const t=this.iterator(this._defaultTracker(e)),a=[];return new Promise((r,s)=>{this._unionShapeInBatches(a,t,r,s)})}_unionAllGeom(e){return new Promise((t,a)=>{const r=this.iterator(this._defaultTracker(e)),s=[];this._unionShapeInBatches(s,r,t,a)})}_unionShapeInBatches(e,t,a,r){t.next().then(s=>{try{s!==null&&s.geometry!==null&&e.push(s.geometry),e.length>30||s===null&&e.length>1?se(e).then(i=>{try{s===null?a(i):(e=[i],this._unionShapeInBatches(e,t,a,r))}catch(l){r(l)}},r):s===null?e.length===1?a(e[0]):a(null):this._unionShapeInBatches(e,t,a,r)}catch(i){r(i)}},r)}iterator(e){return new fe(this,e)}intersection(e,t=!1){return d._featuresetFunctions.intersection.bind(this)(e,t)}difference(e,t=!1,a=!0){return d._featuresetFunctions.difference.bind(this)(e,t,a)}symmetricDifference(e,t=!1,a=!0){return d._featuresetFunctions.symmetricDifference.bind(this)(e,t,a)}morphShape(e,t,a="unknown",r=null){return d._featuresetFunctions.morphShape.bind(this)(e,t,a,r)}morphShapeAndAttributes(e,t,a="unknown"){return d._featuresetFunctions.morphShapeAndAttributes.bind(this)(e,t,a)}union(e,t=!1){return d._featuresetFunctions.union.bind(this)(e,t)}intersects(e){return d._featuresetFunctions.intersects.bind(this)(e)}envelopeIntersects(e){return d._featuresetFunctions.envelopeIntersects.bind(this)(e)}contains(e){return d._featuresetFunctions.contains.bind(this)(e)}overlaps(e){return d._featuresetFunctions.overlaps.bind(this)(e)}relate(e,t){return d._featuresetFunctions.relate.bind(this)(e,t)}within(e){return d._featuresetFunctions.within.bind(this)(e)}touches(e){return d._featuresetFunctions.touches.bind(this)(e)}top(e){return d._featuresetFunctions.top.bind(this)(e)}crosses(e){return d._featuresetFunctions.crosses.bind(this)(e)}buffer(e,t,a,r=!0){return d._featuresetFunctions.buffer.bind(this)(e,t,a,r)}filter(e,t=null){return d._featuresetFunctions.filter.bind(this)(e,t)}orderBy(e){return d._featuresetFunctions.orderBy.bind(this)(e)}dissolve(e,t){return d._featuresetFunctions.dissolve.bind(this)(e,t)}groupby(e,t){return d._featuresetFunctions.groupby.bind(this)(e,t)}reduce(e,t=null,a){return new Promise((r,s)=>{this._reduceImpl(this.iterator(this._defaultTracker(a)),e,t,0,r,s,0)})}_reduceImpl(e,t,a,r,s,i,l){try{if(++l>1e3)return void setTimeout(()=>{l=0,this._reduceImpl(e,t,a,r,s,i,l)});e.next().then(c=>{try{if(c===null)s(a);else{const p=t(a,c,r,this);U(p)?p.then(u=>{this._reduceImpl(e,t,u,r+1,s,i,l)},i):this._reduceImpl(e,t,p,r+1,s,i,l)}}catch(p){i(p)}},i)}catch(c){i(c)}}removeField(e){return d._featuresetFunctions.removeField.bind(this)(e)}addField(e,t,a=null){return d._featuresetFunctions.addField.bind(this)(e,t,a)}sumArea(e,t=!1,a){const r=ee(e);return this.reduce((s,i)=>i.geometry===null?0:t?ne(i.geometry,r).then(l=>s+l):ie(i.geometry,r).then(l=>s+l),0,a)}sumLength(e,t=!1,a){const r=te(e);return this.reduce((s,i)=>i.geometry===null?0:t?le(i.geometry,r).then(l=>s+l):ce(i.geometry,r).then(l=>s+l),0,a)}_substituteVars(e,t){if(t!==null){const a={};for(const r in t)a[r.toLowerCase()]=t[r];e.parameters=a}}async distinct(e,t=1e3,a=null,r){await this.load();const s=I.create(e,this.getFieldsIndex());return this._substituteVars(s,a),this.calculateStatistic("distinct",s,t,this._defaultTracker(r))}async min(e,t=null,a){await this.load();const r=I.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("min",r,-1,this._defaultTracker(a))}async max(e,t=null,a){await this.load();const r=I.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("max",r,-1,this._defaultTracker(a))}async avg(e,t=null,a){await this.load();const r=I.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("avg",r,-1,this._defaultTracker(a))}async sum(e,t=null,a){await this.load();const r=I.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("sum",r,-1,this._defaultTracker(a))}async stdev(e,t=null,a){await this.load();const r=I.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("stdev",r,-1,this._defaultTracker(a))}async variance(e,t=null,a){await this.load();const r=I.create(e,this.getFieldsIndex());return this._substituteVars(r,t),this.calculateStatistic("variance",r,-1,this._defaultTracker(a))}async count(e){return await this.load(),this.calculateStatistic("count",I.create("1",this.getFieldsIndex()),-1,this._defaultTracker(e))}_defaultTracker(e){return e||{aborted:!1}}forEach(e,t){return new Promise((a,r)=>{this._forEachImpl(this.iterator(this._defaultTracker(t)),e,this,a,r,0)})}_forEachImpl(e,t,a,r,s,i){try{if(++i>1e3)return void setTimeout(()=>{i=0,this._forEachImpl(e,t,a,r,s,i)},0);e.next().then(l=>{try{if(l===null)r(a);else{const c=t(l);c==null?this._forEachImpl(e,t,a,r,s,i):U(c)?c.then(()=>{try{this._forEachImpl(e,t,a,r,s,i)}catch(p){s(p)}},s):this._forEachImpl(e,t,a,r,s,i)}}catch(c){s(c)}},s)}catch(l){s(l)}}convertToJSON(e){const t={layerDefinition:{geometryType:this.geometryType,fields:[]},featureSet:{features:[],geometryType:this.geometryType}};for(let a=0;a<this.fields.length;a++)t.layerDefinition.fields.push(G(this.fields[a]));return this.reduce((a,r)=>{const s={geometry:r.geometry&&r.geometry.toJSON(),attributes:{}};for(const i in r.attributes)s.attributes[i]=r.attributes[i];return t.featureSet.features.push(s),1},0,e).then(()=>t)}castToText(e=!1){return"object, FeatureSet"}queryAttachments(e,t,a,r,s){return this._parent.queryAttachments(e,t,a,r,s)}serviceUrl(){return this._parent.serviceUrl()}subtypes(){return this.typeIdField?{subtypeField:this.typeIdField,subtypes:this.types?this.types.map(e=>({name:e.name,code:e.id})):[]}:null}relationshipMetaData(){return this._parent.relationshipMetaData()}get gdbVersion(){return this._parent?this._parent.gdbVersion:""}schema(){const e=[];for(const t of this.fields)e.push(G(t));return{objectIdField:this.objectIdField,globalIdField:this.globalIdField,geometryType:M[this.geometryType]===void 0?"esriGeometryNull":M[this.geometryType],fields:e}}async convertToText(e,t){if(e==="schema")return await this._ensureLoaded(),JSON.stringify(this.schema());if(e==="featureset"){await this._ensureLoaded();const a=[];await this.reduce((s,i)=>{const l={geometry:i.geometry?i.geometry.toJSON():null,attributes:i.attributes};return l.geometry!==null&&l.geometry.spatialReference&&delete l.geometry.spatialReference,a.push(l),1},0,t);const r=this.schema();return r.features=a,r.spatialReference=this.spatialReference.toJSON(),JSON.stringify(r)}return this.castToText()}getFeatureByObjectId(e,t){return this._parent.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._parent.getOwningSystemUrl()}getIdentityUser(){return this._parent.getIdentityUser()}getRootFeatureSet(){return this._parent!==null?this._parent.getRootFeatureSet():this}getDataSourceFeatureSet(){return this._parent!==null?this._parent.getDataSourceFeatureSet():this}castAsJson(e=null){return(e==null?void 0:e.featureset)==="keeptype"?this:(e==null?void 0:e.featureset)==="none"?null:{type:"FeatureSet"}}async castAsJsonAsync(e=null,t=null){var s;if((t==null?void 0:t.featureset)==="keeptype")return this;if((t==null?void 0:t.featureset)==="schema")return await this._ensureLoaded(),JSON.parse(JSON.stringify(this.schema()));if((t==null?void 0:t.featureset)==="none")return null;await this._ensureLoaded();const a=[];await this.reduce((i,l)=>{const c={geometry:l.geometry?(t==null?void 0:t.keepGeometryType)===!0?l.geometry:l.geometry.toJSON():null,attributes:l.attributes};return c.geometry!==null&&c.geometry.spatialReference&&(t==null?void 0:t.keepGeometryType)!==!0&&delete c.geometry.spatialReference,a.push(c),1},0,e);const r=this.schema();return r.features=a,r.spatialReference=(t==null?void 0:t.keepGeometryType)===!0?this.spatialReference:(s=this.spatialReference)==null?void 0:s.toJSON(),r}get dateTimeReferenceFieldIndex(){return this._dateFieldIndex===null&&(this._dateFieldIndex=j.create(this.getFieldsIndex(),this)),this._dateFieldIndex}fieldTimeZone(e){return this.dateTimeReferenceFieldIndex.fieldTimeZone(e)}get preferredTimeReference(){var e;return((e=this._parent)==null?void 0:e.preferredTimeReference)??null}get dateFieldsTimeReference(){var e;return((e=this._parent)==null?void 0:e.dateFieldsTimeReference)??null}get datesInUnknownTimezone(){return this._parent.datesInUnknownTimezone}get editFieldsInfo(){var e;return((e=this._parent)==null?void 0:e.editFieldsInfo)??null}get timeInfo(){var e;return((e=this._parent)==null?void 0:e.timeInfo)??null}}d._featuresetFunctions={};class E extends d{constructor(e){super(e),this.declaredClass="esri.layers.featureset.sources.Empty",this._maxProcessing=1e3,this._wset=new R([],[],!1,null),this._parent=e.parentfeatureset,this._databaseType=h.Standardised}async _getSet(){return this._wset}optimisePagingFeatureQueries(){}_isInFeatureSet(){return b.NotInFeatureSet}async _getFeature(){throw new m(y.NeverReach)}async queryAttachments(){return[]}async _getFeatures(){return"success"}_featureFromCache(){return null}async _fetchAndRefineFeatures(){throw new m(y.NeverReach)}async _getFilteredSet(){return new R([],[],!1,null)}_stat(e,t,a,r,s,i,l){return this._manualStat(e,t,i,l)}async _canDoAggregates(){return!1}}class F extends d{constructor(e){super(e),this._relation="",this._relationGeom=null,this._relationString="",this.declaredClass="esri.arcade.featureset.actions.SpatialFilter",this._relationString=e.relationString,this._parent=e.parentfeatureset,this._maxProcessing=40,this._relation=e.relation,this._relationGeom=e.relationGeom}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._parent._getFilteredSet(this._relation!=="esriSpatialRelRelation"?this._relation:this._relation+":"+this._relationString,this._relationGeom,null,null,e);return this._checkCancelled(e),this._wset=new R(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){let t=this._parent._isInFeatureSet(e);return t===b.NotInFeatureSet?t:(t=this._idstates[e],t===void 0?b.Unknown:t)}_getFeature(e,t,a){return this._parent._getFeature(e,t,a)}_getFeatures(e,t,a,r){return this._parent._getFeatures(e,t,a,r)}_featureFromCache(e){return this._parent._featureFromCache(e)}async executeSpatialRelationTest(e){if(e.geometry===null)return!1;switch(this._relation){case"esriSpatialRelEnvelopeIntersects":{const t=$(this._relationGeom),a=$(e.geometry);return B(t,a)}case"esriSpatialRelIntersects":return B(this._relationGeom,e.geometry);case"esriSpatialRelContains":return _e(this._relationGeom,e.geometry);case"esriSpatialRelOverlaps":return pe(this._relationGeom,e.geometry);case"esriSpatialRelWithin":return de(this._relationGeom,e.geometry);case"esriSpatialRelTouches":return he(this._relationGeom,e.geometry);case"esriSpatialRelCrosses":return oe(this._relationGeom,e.geometry);case"esriSpatialRelRelation":return ue(this._relationGeom,e.geometry,this._relationString??"")}}async _fetchAndRefineFeatures(e,t,a){var l;const r=new R([],e,!1,null),s=Math.min(t,e.length);await((l=this._parent)==null?void 0:l._getFeatures(r,-1,s,a)),this._checkCancelled(a);const i=[];for(let c=0;c<s;c++){const p=this._parent._featureFromCache(e[c]);i.push(await this.executeSpatialRelationTest(p))}for(let c=0;c<t;c++)i[c]===!0?this._idstates[e[c]]=b.InFeatureSet:this._idstates[e[c]]=b.NotInFeatureSet;return"success"}async _getFilteredSet(e,t,a,r,s){await this._ensureLoaded();const i=await this._parent._getFilteredSet(this._relation!=="esriSpatialRelRelation"?this._relation:this._relation+":"+this._relationString,this._relationGeom,a,r,s);let l;return this._checkCancelled(s),l=t!==null?new R(i._candidates.slice(0).concat(i._known.slice(0)),[],i._ordered,this._clonePageDefinition(i.pagesDefinition)):new R(i._candidates.slice(0),i._known.slice(0),i._ordered,this._clonePageDefinition(i.pagesDefinition)),l}async _stat(e,t,a,r,s,i,l){if(a!=="")return{calculated:!1};const c=await this._parent._stat(e,t,this._relation!=="esriSpatialRelRelation"?this._relation:this._relation+":"+this._relationString,this._relationGeom,s,i,l);return c.calculated===!1?s===null&&a===""&&r===null?this._manualStat(e,t,i,l):{calculated:!1}:c}async _canDoAggregates(e,t,a,r,s){return a===""&&r===null&&this._parent!==null&&this._parent._canDoAggregates(e,t,this._relation!=="esriSpatialRelRelation"?this._relation:this._relation+":"+this._relationString,this._relationGeom,s)}async _getAggregatePagesDataSourceDefinition(e,t,a,r,s,i,l){if(this._parent===null)throw new m(y.NeverReach);return this._parent._getAggregatePagesDataSourceDefinition(e,t,this._relation!=="esriSpatialRelRelation"?this._relation:this._relation+":"+this._relationString,this._relationGeom,s,i,l)}static registerAction(){d._featuresetFunctions.intersects=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelIntersects",relationGeom:e})},d._featuresetFunctions.envelopeIntersects=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelEnvelopeIntersects",relationGeom:e})},d._featuresetFunctions.contains=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelContains",relationGeom:e})},d._featuresetFunctions.overlaps=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelOverlaps",relationGeom:e})},d._featuresetFunctions.within=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelWithin",relationGeom:e})},d._featuresetFunctions.touches=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelTouches",relationGeom:e})},d._featuresetFunctions.crosses=function(e){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelCrosses",relationGeom:e})},d._featuresetFunctions.relate=function(e,t){return e==null?new E({parentfeatureset:this}):new F({parentfeatureset:this,relation:"esriSpatialRelRelation",relationGeom:e,relationString:t})}}}export{Se as A,me as L,Be as N,we as R,ye as S,k as T,H as a,Me as b,Ye as c,He as d,A as e,F as f,ge as g,Y as h,Ue as m,$e as p,R as t,E as u,d as v};
