import{_ as M}from"./TreeBox-DDD2iwoR.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as V}from"./CardSearch-CB_HNR-Q.js";import{_ as J}from"./index-BJ-QPYom.js";import{a as O,b as Y}from"./partitionSupply-B-5CoWCs.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as $,c as W,r as x,l as b,bJ as z,bI as E,bH as q,o as K,g as Q,h as U,F as H,q as P,i as C,C as X}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as Z}from"./usePartition-DkcY9fQ2.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./index-0NlGN6gS.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const v=$({__name:"index",setup(tt){const D=W(),h=W(),o=x({data:[],title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:t=>{o.currentProject!==t&&(o.currentProject=t,d())}}),g=(t,p,a)=>a.hidden=t.type!==a.field,B=x({defaultParams:{type:"month",year:b().format(z),month:b().format(E),day:[b().format(q),b().format(q)]},filters:[{type:"select",field:"type",clearable:!1,options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"day"}],label:"选择方式"},{handleHidden:g,type:"year",label:"",field:"year",clearable:!1,disabledDate(t){return new Date<t}},{handleHidden:g,type:"month",label:"",field:"month",clearable:!1,disabledDate(t){return new Date<t}},{handleHidden:g,type:"daterange",label:"",field:"day",clearable:!1,disabledDate(t){return new Date<t}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>d()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var t;(t=h.value)==null||t.resetForm(),d()}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>{var t;(t=D.value)==null||t.exportTable()}}]}]}),T=(t,p)=>[{prop:"title",label:t,align:"center",subColumns:[{prop:"date",label:"日期",minWidth:120,align:"center",fixed:"left"},{prop:"sum",label:"总水量",minWidth:120,align:"center",formatter(a,e){return e==null?void 0:e.toFixed(2)}},{prop:"title",label:"子分区",align:"center",subColumns:[...p||[],{prop:"sumAll",label:"合计",minWidth:160,align:"center",formatter(a,e){return e==null?void 0:e.toFixed(2)}}]},{prop:"difference",label:"差值",minWidth:120,align:"center",fixed:"right",formatter(a,e){return e==null?void 0:e.toFixed(2)}}]}],c=x({loading:!1,dataList:[],indexVisible:!0,columns:T("分区供水量报表",[]),pagination:{hide:!0,refreshData:({page:t,size:p})=>{c.pagination.page=t,c.pagination.limit=p,d()}}}),d=async()=>{var t,p,a,e,u,y,w,j,I,S;if(o.currentProject){try{const i=((t=h.value)==null?void 0:t.queryParams)||{};c.loading=!0;const L=O({partitionId:(p=o.currentProject)==null?void 0:p.value}),N=Y({partitionId:(a=o.currentProject)==null?void 0:a.value,type:i.type,date:i.type==="month"?i.month:i.type==="year"?i.year:void 0,start:i.type==="day"?(e=i.day)==null?void 0:e[0]:void 0,end:i.type==="day"?(u=i.day)==null?void 0:u[1]:void 0}),F=await Promise.all([L,N]),_=(w=(y=F[0].data)==null?void 0:y.data)==null?void 0:w.map(r=>({label:r,prop:r,minWidth:160,formatter(l,f){return f==null?void 0:f.toFixed(2)}})),G=T((j=o.currentProject)==null?void 0:j.label,_);c.columns=G;const m=((S=(I=F[1])==null?void 0:I.data)==null?void 0:S.data)||[],n=m.length,s={date:"合计",sumAll:m.reduce((r,l)=>l.sumAll+r,0),sum:m.reduce((r,l)=>l.sum+r,0),difference:m.reduce((r,l)=>l.difference+r,0)},A={date:"平均",sumAll:n?s.sumAll/n:0,sum:n?s.sum/n:0,difference:n?s.difference/n:0};_==null||_.map(r=>{s[r.prop]=n?m.reduce((l,f)=>l+f[r.prop],0):0,A[r.prop]=n?s[r.prop]/n:0}),m.push(s),m.push(A),c.dataList=m}catch{}c.loading=!1}},k=Z();return K(async()=>{await k.getTree(),o.data=k.Tree.value,o.currentProject=o.data[0],d()}),(t,p)=>{const a=J,e=V,u=R,y=M;return Q(),U(y,null,{tree:H(()=>[P(a,{ref:"refTree","tree-data":C(o)},null,8,["tree-data"])]),default:H(()=>[P(e,{ref_key:"refSearch",ref:h,config:C(B)},null,8,["config"]),P(u,{ref_key:"refTable",ref:D,class:"card-table",config:C(c)},null,8,["config"])]),_:1})}}}),Te=X(v,[["__scopeId","data-v-57fee1cb"]]);export{Te as default};
