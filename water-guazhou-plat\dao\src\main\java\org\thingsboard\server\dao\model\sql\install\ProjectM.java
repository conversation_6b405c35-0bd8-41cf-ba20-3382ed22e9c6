package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_project_m")
@Data
public class ProjectM {
    @TableId
    private String id;

    private String code;

    private Date createTime;

    private Date receiveTime;

    private String receiver;

    private transient String receiverName;

    private String address;

    private String type;

    private transient String typeName;

    private String status;

    private String remark;

    private String currentStep;

    private String currentStepId;

    private String tenantId;

}
