import{z as a}from"./index-r0dFAfgr.js";const o=t=>a({url:"/api/station/list",method:"get",params:t}),l=t=>a({url:"/istar/api/station/data/detailList",method:"get",params:t}),n=t=>a({url:"/api/station/stationAttrList",method:"get",params:t}),i=t=>a({url:"/api/station/allAttrList",method:"get",params:t}),m=t=>a({url:`/api/station/${t}`,method:"get"}),u=(t,e)=>a({url:`/istar/api/station/data/detail/${t}`,method:"get",params:{type:e||""}}),p=t=>a({url:"/api/station/attrGroupNames",method:"get",params:t}),d=(t,e,r)=>a({url:`/api/alarm/station/${t}`,method:"get",params:{start:e||"",end:r||""}}),c=t=>a({url:"/api/alarmV2/alarmCenter/list",method:"get",params:t}),h=t=>a({url:"/api/station/simpleTree",method:"get",params:{type:t||null}}),g=t=>a({url:"/api/station/save",method:"post",data:t}),A=t=>a({url:"/api/station/remove",method:"delete",data:t}),y=t=>a({url:"/api/station/typeCount",method:"get",params:t}),G=t=>a({url:"/api/alarmV2/alarmRule/list",method:"get",params:t}),S=t=>a({url:`/api/alarmV2/alarmRule/${t}`,method:"get"}),R=t=>a({url:"/api/alarmV2/alarmRule/save",method:"post",data:t}),V=t=>a({url:"/api/alarmV2/alarmRule/remove",method:"delete",data:t}),C=t=>a({url:"/api/alarmV2/alarmCenter/list",method:"get",params:t}),D=t=>a({url:"/api/alarmV2/alarmCenter/clearAlarm",method:"post",data:t}),B=t=>a({url:"/api/alarmV2/alarmCenter/report",method:"get",params:t}),L=(t,e)=>a({url:`/api/alarmV2/alarmCenter/createWorkOrder/${t}`,method:"post",data:e}),T=t=>a({url:"/istar/api/station/data/stationAttrDataQueryGroupByDay",method:"get",params:t}),k=t=>a({url:"/api/alarmV2/alarmCenter/rankByStation",method:"get",params:t}),$=t=>a({url:"/api/alarmV2/alarmCenter/list/export",params:t,responseType:"blob"});export{A as D,$ as E,o as G,g as P,n as a,i as b,h as c,u as d,l as e,p as f,d as g,k as h,B as i,T as j,C as k,S as l,D as m,R as n,V as o,L as p,G as q,y as r,m as s,c as t};
