package org.thingsboard.server.dao.model.sql.groundwater;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;

/**
 * 地下水水位数据
 */
@Data
@TableName("tb_groundwater_level")
@ApiModel(value = "地下水水位数据", description = "地下水水位监测数据记录")
public class GroundwaterLevel {

    @TableId
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    @ApiModelProperty(value = "测点ID")
    private String stationId;
    
    @ApiModelProperty(value = "测点名称")
    @TableField(exist = false)
    private String stationName;

    @ApiModelProperty(value = "测点位置")
    @TableField(exist = false)
    private String stationLocation;

    @ApiModelProperty(value = "水位高度(米)")
    private BigDecimal waterLevel;
    
    @ApiModelProperty(value = "水位变化量(米)")
    private BigDecimal levelChange;
    
    @ApiModelProperty(value = "地下水补给量(立方米)")
    private BigDecimal rechargeAmount;
    
    @ApiModelProperty(value = "降雨量(毫米)")
    private BigDecimal rainfallAmount;
    
    @ApiModelProperty(value = "记录时间")
    private Date recordTime;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "水位状态 (1-正常, 2-偏低, 3-偏高)")
    private Integer status;
    
    @ApiModelProperty(value = "数据来源 (1-手动录入, 2-设备采集)")
    private Integer dataSource;
} 