import{w as m}from"./MapView-DaoQedLH.js";import{T as h,R as M}from"./index-r0dFAfgr.js";import{$ as c}from"./Point-WxyopZva.js";function u(e,t){return t?"xoffset"in t&&t.xoffset?Math.max(e,Math.abs(t.xoffset)):"yoffset"in t&&t.yoffset?Math.max(e,Math.abs(t.yoffset||0)):e:e}function b(e){let t=0,n=0;for(let r=0;r<e.length;r++){const s=e[r].size;typeof s=="number"&&(t+=s,n++)}return t/n}function p(e,t){return typeof e=="number"?e:e&&e.stops&&e.stops.length?b(e.stops):t}function v(e,t){if(!t)return e;const n=t.filter(o=>o.type==="size").map(o=>{const{maxSize:a,minSize:f}=o;return(p(a,e)+p(f,e))/2});let r=0;const s=n.length;if(s===0)return e;for(let o=0;o<s;o++)r+=n[o];const i=Math.floor(r/s);return Math.max(i,e)}function R(e){var s;const t=e&&e.renderer,n=(e&&e.event&&e.event.pointerType)==="touch"?9:6;if(!t)return n;const r="visualVariables"in t?v(n,t.visualVariables):n;if(t.type==="simple")return u(r,t.symbol);if(t.type==="unique-value"){let i=r;return(s=t.uniqueValueInfos)==null||s.forEach(o=>{i=u(i,o.symbol)}),i}if(t.type==="class-breaks"){let i=r;return t.classBreakInfos.forEach(o=>{i=u(i,o.symbol)}),i}return t.type==="dot-density"||t.type,r}function d(e,t,n,r=new m){let s=0;if(n.type==="2d")s=t*(n.resolution??0);else if(n.type==="3d"){const x=n.overlayPixelSizeInMapUnits(e),l=n.basemapSpatialReference;s=M(l)&&!l.equals(n.spatialReference)?c(l)/c(n.spatialReference):t*x}const i=e.x-s,o=e.y-s,a=e.x+s,f=e.y+s,{spatialReference:y}=n;return r.xmin=Math.min(i,a),r.ymin=Math.min(o,f),r.xmax=Math.max(i,a),r.ymax=Math.max(o,f),r.spatialReference=y,r}function q(e,t,n){const r=n.toMap(e);return h(r)?!1:d(r,R(),n,z).intersects(t)}const z=new m;export{d as a,q as o,R as s};
