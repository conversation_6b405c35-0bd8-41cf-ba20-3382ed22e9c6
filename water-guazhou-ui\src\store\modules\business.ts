import { getConstantsAttributeById } from '@/api/constants';
import useAreaTree from '@/hooks/project/useProject';
import storage from '@/utils/storage';
import { useUserStore } from './user';

export const useBusinessStore = defineStore('business', () => {
  const initialState: Store_Business_State = {
    navSelectedRange: storage.get('navSelectedRange'),
    selectedProject: storage.get('navSelectedRange'),
    curNavs: storage.get<NavResult[]>('curNavs') || [],
    navAppsNames: [],
    navApps: [],
    projectList: storage.get('projectList') || [],
    shouldRefreshSideBar: !!localStorage.getItem('shouldRefreshSideBar'),
    shouldToDefaultFirstRout: true,
    useprojectapp: localStorage.getItem('useprojectapp') === 'true',
    usePortal: localStorage.getItem('useportal') === 'true'
  };
  const state = reactive<Store_Business_State>(initialState);
  const actions = {
    SET_selectedProject: (payload) => {
      state.selectedProject = payload;
    },
    SET_projectList: (payload) => {
      state.projectList = payload || [];
      storage.set('projectList', payload || []);
    },
    SET_shouldRefreshSideBar: (payload) => {
      state.shouldRefreshSideBar = !!payload;
      localStorage.setItem('shouldRefreshSideBar', payload ? 'true' : 'false');
    },
    SET_shouldToDefaultFirstRout: (payload: boolean) => {
      state.shouldToDefaultFirstRout = payload;
    },
    SET_curNavs: () => {
      state.curNavs = state.navApps || [];
      storage.set('curNavs', state.curNavs);
    },
    SET_navApps: (payload) => {
      state.navApps = payload;
    },
    SET_BUSINESS_NAVSELECTEDRANGE: (payload) => {
      state.navSelectedRange = payload;
      state.selectedProject = payload;
      localStorage.setItem('navSelectedRange', JSON.stringify(payload));
    },
    SET_navAppsNames: (payload) => {
      state.navAppsNames = payload;
    },
    // 站点树
    SET_stationTree: (payload) => {
      state.projectList = payload || [];
      localStorage.setItem('stationTree', JSON.stringify(payload || []));
    },
    INIT_navAppsName: async () => {
      const userStore = useUserStore();
      if (!userStore.token) return;
      try {
        const res = await getConstantsAttributeById({
          type: 'appNameSetting',
          key: 'appNameSetting'
        });
        if (res.status === 200) {
          const data = JSON.parse(res.data[0].value);
          actions.SET_navAppsNames(data);
        }
      } catch (error) {
        console.dir(error);
      }
    },
    /**
     * 初始化项目树和当前选中项目
     * @param always 是否每次执行都刷新项目信息
     */
    INIT_projectInfo: async (always?: boolean) => {
      if (always || !state.navSelectedRange || !state.projectList.length) {
        const userStore = useUserStore();
        const { getProjectTreeData, getCurUserProjectTreeData } = useAreaTree();
        const res = await getProjectTreeData();
        const roles = userStore.user?.authority?.split(',') || [];
        actions.SET_projectList(res);
        if (roles.indexOf('CUSTOMER_USER') !== -1) {
          const uRes = await getCurUserProjectTreeData();
          actions.SET_BUSINESS_NAVSELECTEDRANGE(uRes[0]);
        } else {
          actions.SET_BUSINESS_NAVSELECTEDRANGE(res[0]);
        }
      }
    },
    INIT_horizontalMenu: async () => {
      state.curNavs.length;
    }
  };
  return { ...toRefs(state), ...actions };
});
