package org.thingsboard.server.dao.device;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-17
 */
public interface DeviceModelInfoService {
    PageData<DeviceModelInfoEntity> getList(DeviceModelInfoEntity deviceModelInfoEntity, int page, int size);

    DeviceModelInfoEntity save(DeviceModelInfoEntity deviceModelInfoEntity);

    IstarResponse delete(List<String> ids);

    DeviceModelInfoEntity getByDeviceId(String id);
}
