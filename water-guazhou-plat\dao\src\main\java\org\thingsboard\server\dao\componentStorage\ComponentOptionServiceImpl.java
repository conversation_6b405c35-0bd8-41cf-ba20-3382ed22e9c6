package org.thingsboard.server.dao.componentStorage;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ComponentOptionCEntity;
import org.thingsboard.server.dao.model.sql.ComponentOptionEntity;
import org.thingsboard.server.dao.model.sql.ComponentStorageEntity;
import org.thingsboard.server.dao.sql.componentStorage.ComponentOptionCRepository;
import org.thingsboard.server.dao.sql.componentStorage.ComponentOptionRepository;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ComponentOptionServiceImpl implements ComponentOptionService {

    @Autowired
    private ComponentOptionRepository componentOptionRepository;
    @Autowired
    private ComponentOptionCRepository componentOptionCRepository;
    @Autowired
    private ComponentStorageService componentStorageService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public PageData<ComponentOptionEntity> findList(int page, int size, String code, TenantId tenantId) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        // 查询
        Page<ComponentOptionEntity> pageResult = componentOptionRepository.findList(code, UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);
        long totalElements = pageResult.getTotalElements();
        List<ComponentOptionEntity> content = pageResult.getContent();
        if (content != null && content.size() > 0) {
            // 备件库列表
            List<ComponentStorageEntity> componentList = componentStorageService.all(tenantId);
            Map<String, ComponentStorageEntity> componentMap = new HashMap<>();
            if (componentList != null && componentList.size() > 0) {
                componentList.forEach(c -> componentMap.put(c.getId(), c));
            }

            for (ComponentOptionEntity option : content) {
                List<ComponentOptionCEntity> childList = componentOptionCRepository.findByMainId(option.getId());
                if (childList != null && childList.size() > 0) {
                    String nameList = childList.stream().map(c -> {
                        ComponentStorageEntity component = componentMap.get(c.getComponentId());
                        return component.getName();
                    }).filter(StringUtils::isNotBlank).reduce((name1, name2) -> name1 + "、" + name2).get();
                    option.setNameList(nameList);
                }
            }
        }

        return new PageData<>(totalElements, content);
    }

    @Override
    @Transactional
    public void saveOption(ComponentOptionEntity entity, User currentUser) throws ThingsboardException {
        // 保存主表记录
        entity.setCreator(currentUser.getFirstName());
        entity.setCreateTime(new Date());
        entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

        // 生成出库单号
        String code = getCode(entity.getType(), entity.getCreateTime());
        entity.setCode(code);

        ComponentOptionEntity save = componentOptionRepository.save(entity);

        // 保存子表记录并变更对应备件的库存数量
        List<ComponentOptionCEntity> details = entity.getDetails();
        // 备件库列表
        List<ComponentStorageEntity> componentList = componentStorageService.all(currentUser.getTenantId());
        Map<String, ComponentStorageEntity> componentMap = new HashMap<>();
        if (componentList != null && componentList.size() > 0) {
            componentList.forEach(c -> componentMap.put(c.getId(), c));
        }
        for (ComponentOptionCEntity detail : details) {
            detail.setMainId(save.getId());
            detail.setTenantId(save.getTenantId());

            ComponentStorageEntity component = componentMap.get(detail.getComponentId());
            if ("1".equals(entity.getType())) {// 入库
                component.setNumber(component.getNumber() + detail.getNumber());
            }
            if ("2".equals(entity.getType())) {// 出库
                if (component.getNumber() - detail.getNumber() >= 0) {
                    component.setNumber(component.getNumber() - detail.getNumber());
                } else {// 库存不足
                    throw new ThingsboardException(component.getName() + "库存不足", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
            }
            componentStorageService.save(component);

        }
        componentOptionCRepository.save(details);

    }

    private String getCode(String type, Date createTime) {
        BoundValueOperations crk_no_incr = redisTemplate.boundValueOps("CRK_NO_INCR");
        Long increment = crk_no_incr.increment(1);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = simpleDateFormat.format(createTime);

        String prefix = "";
        if ("1".equals(type)) {
            prefix = "RK";
        }
        if ("2".equals(type)) {
            prefix = "CK";
        }

        return prefix + format + String.format("%04d", increment);
    }

    @Override
    public ComponentOptionEntity getDetail(String id) throws ThingsboardException {
        // 查询主表
        ComponentOptionEntity option = componentOptionRepository.findOne(id);
        if (option == null) {
            throw new ThingsboardException(ThingsboardErrorCode.ITEM_NOT_FOUND);
        }

        // 查询子表
        List<ComponentOptionCEntity> details = componentOptionCRepository.findByMainId(id);

        if (details != null && details.size() > 0) {
            // 备件库列表
            List<ComponentStorageEntity> componentList = componentStorageService.all(new TenantId(UUIDConverter.fromString(option.getTenantId())));
            Map<String, ComponentStorageEntity> componentMap = new HashMap<>();
            if (componentList != null && componentList.size() > 0) {
                componentList.forEach(c -> componentMap.put(c.getId(), c));
            }

            for (ComponentOptionCEntity detail : details) {
                ComponentStorageEntity component = componentMap.get(detail.getComponentId());
                detail.setComponent(component);
            }
        }

        option.setDetails(details);

        return option;
    }
}
