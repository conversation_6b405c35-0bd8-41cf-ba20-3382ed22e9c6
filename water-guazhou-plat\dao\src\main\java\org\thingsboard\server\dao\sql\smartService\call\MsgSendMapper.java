package org.thingsboard.server.dao.sql.smartService.call;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.call.MsgSend;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface MsgSendMapper extends BaseMapper<MsgSend> {

    List<MsgSend> getList(@Param("receivePhone") String receivePhone, @Param("keywords") String keywords, @Param("status") String status, @Param("start") Long start, @Param("end") Long end, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("receivePhone") String receivePhone, @Param("keywords") String keywords, @Param("status") String status, @Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId);
}
