package org.thingsboard.server.dao.sql.gis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.GisOptionLogListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisOptionLog;

@Mapper
public interface GisOptionLogMapper extends BaseMapper<GisOptionLog> {
    IPage<GisOptionLog> findList(IPage<GisOptionLog> pageRequest, @Param("param") GisOptionLogListRequest request);
}
