package org.thingsboard.server.dao.model.sql.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.purchase.DevicePurchaseMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class Contract {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 所属采购单ID
    @ParseViaMapper(DevicePurchaseMapper.class)
    private String purchaseId;

    // 合同编号
    private String code;

    // 合同标题
    private String title;

    // 合同文件
    private String file;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
