package org.thingsboard.server.common.data.dataSource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:45
 *
 * 数据源公用类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class BaseDataSource {

    private String id;
    private String name;
    private DataSourceType type;
    private long updateTime;
    private String format;
    private int enable;
    private long order;
}
