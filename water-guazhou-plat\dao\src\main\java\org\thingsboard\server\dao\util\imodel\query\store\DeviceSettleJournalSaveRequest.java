package org.thingsboard.server.dao.util.imodel.query.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceSettleJournal;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DeviceSettleJournalSaveRequest extends SaveRequest<DeviceSettleJournal> {
    // 设备标签
    private String deviceLabelCode;

    // 安装人员ID
    private String installUserId;

    // 施工项目ID
    private String projectId;

    // 安装时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date installTime;

    // 安装位置ID
    @NotNullOrEmpty
    private String installAddressId;

    // 安装位置描述
    @NotNullOrEmpty
    private String address;

    // 备注
    private String remark;

    @Override
    protected DeviceSettleJournal build() {
        DeviceSettleJournal entity = new DeviceSettleJournal();
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected DeviceSettleJournal update(String id) {
        DeviceSettleJournal entity = new DeviceSettleJournal();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(DeviceSettleJournal entity) {
        entity.setDeviceLabelCode(deviceLabelCode);
        entity.setInstallUserId(installUserId);
        entity.setProjectId(projectId);
        entity.setInstallTime(installTime);
        entity.setInstallAddressId(installAddressId);
        entity.setAddress(address);
        entity.setRemark(remark);
    }
}