package org.thingsboard.server.service.utils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;

/**
 * JS 执行工具类
 */
public final class JsExecuteUtil {

    private static ScriptEngine jsEngine;

    static {
        ScriptEngineManager scriptEngine = new ScriptEngineManager();
        jsEngine = scriptEngine.getEngineByName("js");
    }

    public static Boolean getBooleanByResult(String script) throws ScriptException {
        Object eval = jsEngine.eval(script);
        return (boolean) eval;
    }

    public static BigDecimal getBigDecimalByResult(String script) throws ScriptException {
        Object eval = jsEngine.eval(script);
        return new BigDecimal((String) eval);
    }

    public static Object getOriginalByResult(String script) throws ScriptException {
        return jsEngine.eval(script);
    }

}
