package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseConnectionPageRequest;

import java.util.List;

/**
 * 平台管理-数据库连接Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IBaseDatabaseConnectionService {
    /**
     * 查询平台管理-数据库连接
     *
     * @param id 平台管理-数据库连接主键
     * @return 平台管理-数据库连接
     */
    public BaseDatabaseConnection selectBaseDatabaseConnectionById(String id);

    /**
     * 查询平台管理-数据库连接列表
     *
     * @param baseDatabaseConnection 平台管理-数据库连接
     * @return 平台管理-数据库连接集合
     */
    public IPage<BaseDatabaseConnection> selectBaseDatabaseConnectionList(BaseDatabaseConnectionPageRequest baseDatabaseConnection);

    /**
     * 新增平台管理-数据库连接
     *
     * @param baseDatabaseConnection 平台管理-数据库连接
     * @return 结果
     */
    public int insertBaseDatabaseConnection(BaseDatabaseConnection baseDatabaseConnection);

    /**
     * 修改平台管理-数据库连接
     *
     * @param baseDatabaseConnection 平台管理-数据库连接
     * @return 结果
     */
    public int updateBaseDatabaseConnection(BaseDatabaseConnection baseDatabaseConnection);

    /**
     * 批量删除平台管理-数据库连接
     *
     * @param ids 需要删除的平台管理-数据库连接主键集合
     * @return 结果
     */
    public int deleteBaseDatabaseConnectionByIds(List<String> ids);

    /**
     * 删除平台管理-数据库连接信息
     *
     * @param id 平台管理-数据库连接主键
     * @return 结果
     */
    public int deleteBaseDatabaseConnectionById(String id);
}
