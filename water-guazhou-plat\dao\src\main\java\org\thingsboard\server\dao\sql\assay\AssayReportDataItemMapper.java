package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.assay.AssayReportDataItem;

import java.util.List;

@Mapper
public interface AssayReportDataItemMapper extends BaseMapper<AssayReportDataItem> {
    void batchInsert(List<AssayReportDataItem> assayReportDataItemList);

    List<AssayReportDataItem> getListByPid(@Param("pid") String pid);
}
