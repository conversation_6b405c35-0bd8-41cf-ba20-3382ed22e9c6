<template>
  <div>
    <el-button
      type="default"
      :text="true"
      @click="state.dialogVisible = true"
    >
      修改密码
    </el-button>
    <el-dialog
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      title="修改密码"
      :close-on-click-modal="false"
      class="alarm-design"
      width="40%"
      @close="dialogClose"
    >
      <el-form
        ref="refForm"
        :model="state.password"
        label-width="100px"
        :rules="state.pwdForm"
        class="change-pwd"
      >
        <el-form-item
          label="当前密码"
          prop="currentPassword"
        >
          <el-input
            v-model="state.password.currentPassword"
            type="password"
            placeholder="请输入当前密码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="新密码"
          prop="newPassword"
        >
          <el-input
            v-model="state.password.newPassword"
            type="password"
            placeholder="请输入新密码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="确认新密码"
          prop="verifyPassword"
        >
          <el-input
            v-model="state.password.verifyPassword"
            type="password"
            placeholder="请输入新密码"
          ></el-input>
        </el-form-item>
        <el-form-item class="pwd-operation">
          <el-button
            type="primary"
            @click="onSubmit"
          >
            修改密码
          </el-button>
          <el-button
            type="default"
            @click="onReturn"
          >
            取 消
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { changePassword } from '@/api/auth'
import { IElForm } from '@/common/types/element-plus.js'
import { SLMessage } from '@/utils/Message.js'

const refForm = ref<IElForm>()
const validateNewPwd = (rule, value, callback) => {
  // const intensity4 = /^[0-9]{1,20}$/
  if (value) {
    // if (intensity4.test(value)) {
    //   callback(new Error('纯数字密码过于简单，请重新输入'))
    // }
    if (state.password.verifyPassword !== '') {
      refForm.value?.validateField('verifyPassword')
    }
    callback()
  } else {
    callback(new Error('请输入新密码'))
  }
}
const validateVerifyPwd = (rule, value, callback) => {
  if (value !== state.password.newPassword) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}
const state = reactive<{
dialogVisible:boolean
password:{
  currentPassword: string,
    newPassword: string,
    verifyPassword: string
}
pwdForm:{
  currentPassword:any
  newPassword:any
  verifyPassword:any
}
}>({
  dialogVisible: false,
  password: {
    currentPassword: '',
    newPassword: '',
    verifyPassword: ''
  },
  pwdForm: {
    currentPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { validator: validateNewPwd, trigger: 'blur' },
      {
        min: 8,
        max: 20,
        message: '密码不能小于8位，大于20位',
        trigger: 'blur'
      }
    ],
    verifyPassword: [
      { required: true, message: '请输入再次新密码', trigger: 'blur' },
      { validator: validateVerifyPwd, trigger: 'blur' },
      {
        min: 8,
        max: 20,
        message: '密码不能小于8位，大于20位',
        trigger: 'blur'
      }
    ]
  }
})

const onSubmit = () => {
  refForm.value?.validate(valid => {
    if (valid) {
      if (state.password.currentPassword === state.password.newPassword) {
        SLMessage.error('新密码与原密码一致，请重新输入')
        return false
      }
      changePassword(state.password.currentPassword, state.password.newPassword)
        .then(() => {
          SLMessage.success('修改成功')
          state.dialogVisible = false
          dialogClose()
        })
        .catch(err => {
          SLMessage.error(err.data.message)
          console.log(err)
        })
    } else {
      SLMessage.error('请按提示输入信息')
      return false
    }
  })
}
const onReturn = () => {
  state.dialogVisible = false
  state.password.currentPassword = ''
  state.password.newPassword = ''
  state.password.verifyPassword = ''
}
const dialogClose = () => {
  state.password.currentPassword = ''
  state.password.newPassword = ''
  state.password.verifyPassword = ''
}
</script>

<style lang="scss" scoped>
.change-pwd {
  .el-form-item {
    margin-bottom: 20px;
  }
  .pwd-operation {
    margin: 10px 0;
  }
}
:deep(.el-dialog__header) {
  padding: 10px;
}
//  FIXME: element margin bug
</style>
