package org.thingsboard.server.dao.util.imodel.query;

import org.hibernate.jpa.criteria.OrderImpl;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CriteriaBuilderWrapper {
    private final CriteriaBuilder builder;
    private final CriteriaQuery<?> query;
    private final List<Predicate> predicateList = new ArrayList<>();
    private final Root<?> root;

    private Order order;

    public CriteriaBuilderWrapper(Root<?> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
        this.query = query;
        this.builder = builder;
        this.root = root;
    }

    public void dateGreaterThanOrEqualTo(String name, Date value) {
        if (value == null)
            return;
        Predicate predicate = builder.greaterThanOrEqualTo(getname(name).as(Date.class), value);
        predicateList.add(predicate);
    }

    public void dateLessThanOrEqualTo(String name, Date value) {
        if (value == null)
            return;
        Predicate predicate = builder.lessThanOrEqualTo(getname(name).as(Date.class), value);
        predicateList.add(predicate);
    }

    public void equalTo(String name, Object value) {
        equalTo(name, value, false);
    }

    public void equalToAny(Object value, String... names) {
        Predicate predicate = null;
        for (String name : names) {
            if (predicate == null)
                predicate = value == null ? builder.isNull(getname(name)) : builder.equal(getname(name), value);
            else
                predicate = builder.or(predicate, value == null ? builder.isNull(getname(name)) : builder.equal(getname(name), value));
        }
        predicateList.add(builder.and(predicate));
    }

    public void equalTo(String name, Object value, boolean or) {
        Predicate predicate = value == null ? builder.isNull(getname(name)) : builder.equal(getname(name), value);
        if (or)
            predicate = builder.or(predicate);
        predicateList.add(predicate);
    }

    private Expression<?> getname(String name) {
        Path<Object> path;
        if (name.contains(".")) {
            String[] names = name.split("\\.");
            path = root.get(names[0]);
            for (int i = 1; i < names.length; i++) {
                path = root.get(names[i]);
            }
        } else {
            path = root.get(name);
        }

        return path;
    }

    public void in(String name, Object value) {
        if (value == null)
            return;
        in(name, new Object[]{value});
    }

    public void in(String name, Object[] values) {
        if (values == null || values.length == 0)
            return;
        CriteriaBuilder.In<Object> in = builder.in(getname(name));
        for (Object val : values) {
            in.value(val);
        }
        predicateList.add(in);
    }

    public void orderByDate(String name, boolean asc) {
        order = new OrderImpl(getname(name).as(Date.class), asc);
    }

    @SuppressWarnings("unchecked")
    public void like(String name, String value, boolean or) {
        Predicate predicate = builder.like((Expression<String>) getname(name), value);
        if (or)
            predicate = builder.or(predicate);
        predicateList.add(predicate);
    }

    public Predicate build() {
        Predicate[] pre = predicateList.toArray(new Predicate[0]);
        query.where(pre);
        if (order != null)
            query.orderBy(order);

        return query.getRestriction();
    }
}
