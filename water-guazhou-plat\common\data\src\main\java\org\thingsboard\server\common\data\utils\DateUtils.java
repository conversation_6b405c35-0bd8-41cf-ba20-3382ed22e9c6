/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.utils;

import org.json.JSONObject;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.time.InfluxSearchTs;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtils {
    public static final String DATE_FORMATE_DAY = "yyyy-MM-dd";
    public static final String DATE_FORMATE_MONTH = "yyyy-MM";
    public static final String DATE_FORMATE_YEAR = "yyyy";
    public static final String DATE_FORMATE_DEFAULT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMATE_DEFAULT_2 = "yyyy/MM/dd HH:mm:ss";
    public static final String DATE_FORMATE_DEFAULT_1 = "yyyy/MM/dd HH:mm";
    public static final String DATE_FORMATE_MINUTE = "yyyy-MM-dd HH:mm";
    public static final String DATE_FORMATE_HOUR = "yyyy-MM-dd HH";
    public static final String DATE_FORMATE_DATE = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static final String DAY = "day";
    public static final String MONTH = "month";
    public static final String YEAR = "year";
    public static final String HOUR = "hour";
    public static final String WEEK = "week";
    public static final String SEASON = "season";
    public static final String DATE = "date";
    public static final String FIFTEEN_MINUTE = "15m";
    public static final String THIRTY_MINUTE = "30m";
    public static final String TEN_MINUTE = "10m";
    public static final String FIVE_MINUTE = "5m";
    public static final String MINUTE = "1m";
    public static final String MINUTE_S = "minute";
    public static final String SECOND = "30s";
    public static final String MINUTE_SPC = "m";
    public static final String HOUR_SPC = "h";
    public static final String DAY_SPC = "d";
    public static final String MONTH_SPC = "mon";


    public static final long DAY_TIME = 1000 * 60 * 60 * 24;
    public static final long MONTH_TIME = 1000 * 60 * 60;
    public static final long YEAR_TIME = 1000 * 60 * 60;
    public static final long FIFTEEN_MINUTE_TIMESTAMP = 1000 * 60 * 15;
    public static final long MINUTE_TIME = 60000;
    public static final long TWO_MINUTE_TIME = 120000;

    public static final long DAY_TIME_S = 60 * 60 * 24;
    public static final long MONTH_TIME_S = 60 * 60;
    public static final long YEAR_TIME_S = 60 * 60;
    public static final long FIFTEEN_MINUTE_TIMESTAMP_S = 60 * 15;


    public static String date2Str(Date d, String format) {// yyyy-MM-dd HH:mm:ss
        if (d == null) {
            return null;
        }
        if (format == null || format.length() == 0) {
            format = DATE_FORMATE_DEFAULT;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String s = sdf.format(d);
        return s;
    }

    public static String date2StrByType(Long l, String type) {
        if (l.toString().length() < 13) {
            l = l * 1000;
        }
        Date date = new Date(l);
        switch (type) {
            case DateUtils.SECOND: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_DEFAULT);
            }
            case DateUtils.MINUTE:
            case DateUtils.FIVE_MINUTE:
            case DateUtils.TEN_MINUTE:
            case DateUtils.FIFTEEN_MINUTE:
            case DateUtils.THIRTY_MINUTE:
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_MINUTE);
            case "1h":
            case DateUtils.HOUR: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_HOUR);
            }
            case "1nc":
            case DateUtils.MONTH: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_MONTH);
            }
            case "1yc":
            case DateUtils.YEAR: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_YEAR);
            }
            default: {
                return DateUtils.date2Str(date, DateUtils.DATE_FORMATE_DAY);
            }
        }
    }


    public static Date str2DateByTyPe(String str, String type) {
        String format = null;
        switch (type) {
            case DateUtils.HOUR:
                format = DATE_FORMATE_HOUR;
                break;
            case DateUtils.DAY:
                format = DATE_FORMATE_DAY;
                break;
            case DateUtils.MONTH:
                format = DATE_FORMATE_MONTH;
                break;
            case YEAR:
                format = DATE_FORMATE_YEAR;
                break;
            default:
                format = DATE_FORMATE_MINUTE;
                break;
        }
        if (str == null || str.length() == 0) {
            return null;
        }
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            date = sdf.parse(str);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }


    public static String date2Str(Long l, String format) {
        if (l.toString().length() < 13) {
            l = l * 1000;
        }
        Date date = new Date(l);
        return date2Str(date, format);
    }

    /**
     * 根据时间和抄表时间返回周几
     *
     * @param date
     * @param meterReadTime
     * @return
     */
    public static String getWeekday(String date, String meterReadTime) {// 必须yyyy-MM-dd
        int hour = 0;
        int minute = 0;
        if (meterReadTime != null) {
            hour = Integer.parseInt(meterReadTime.split(":")[0]);
            minute = Integer.parseInt(meterReadTime.split(":")[1]);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(str2Date(date, DATE_FORMATE_MINUTE));
        if (calendar.get(Calendar.HOUR_OF_DAY) < hour || (calendar.get(Calendar.HOUR_OF_DAY) == hour && calendar.get(Calendar.MINUTE) <= minute)) {
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
        }
        switch (calendar.get(Calendar.DAY_OF_WEEK)) {
            case 1:
                return "星期日";
            case 2:
                return "星期一";
            case 3:
                return "星期二";
            case 4:
                return "星期三";
            case 5:
                return "星期四";
            case 6:
                return "星期五";
            case 7:
                return "星期六";
        }
        return "星期一";
    }

    /**
     * 获取当前时间向前推进一个月的时间
     *
     * @param l
     * @return
     */
    public static long timeBeforeMonth(long l) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(l);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        return calendar.getTime().getTime();
    }

    public static Date str2DateByLength(String str) {
        switch (str.length()) {
            case 4:
                return str2Date(str, DATE_FORMATE_YEAR);
            case 7:
                return str2Date(str, DATE_FORMATE_MONTH);
            case 10:
                return str2Date(str, DATE_FORMATE_DAY);
            case 13:
                return str2Date(str, DATE_FORMATE_HOUR);
            case 16:
                return str2Date(str, DATE_FORMATE_MINUTE);
            default:
                return str2Date(str, DATE_FORMATE_DEFAULT);
        }
    }

    public static Date str2Date(String str, String format) {
        if (str == null || str.length() == 0) {
            return null;
        }
        if (format == null || format.length() == 0) {
            format = DATE_FORMATE_DEFAULT;
        }
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            date = sdf.parse(str);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;

    }


    public static Long nextDay(Long timeStamp) {
        return timeStamp + DAY_TIME;
    }

    public static Long lastDay(Long timestamp) {
        return timestamp - DAY_TIME;
    }


    //获取当前时间向前推进一年的时间
    public static long timeBeforeYear(long l) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(l);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
        return calendar.getTime().getTime();
    }


    /**
     * 获取当前时间向前推进一天的时间
     *
     * @return
     */
    public static Long oneDayAgo(Date date) {
        return date.getTime() - DAY_TIME;
    }

    /**
     * 将时间转换为当天的0点0分0秒
     * 为了使每天的统计数据timestamp保持一致性，方便查询操作
     *
     * @param date
     * @return
     */
    public static Date format2Day(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 根据租户设定的抄表时间转换初始起始时间
     *
     * @param date
     * @param hour
     * @return
     */
    public static Date getMeterReadingTime(Date date, String hour, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (hour != null) {
            String[] time = hour.split(":");
            switch (type) {
                case DAY: {
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time[0]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(time[1]));
                    break;
                }
                case MONTH: {
                    calendar.set(Calendar.DAY_OF_MONTH, 1);
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time[0]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(time[1]));
                    break;
                }
                case YEAR: {
                    calendar.set(Calendar.DAY_OF_YEAR, 1);
                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time[0]));
                    calendar.set(Calendar.MINUTE, Integer.parseInt(time[1]));
                    break;
                }
            }

        }
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }


    //获取自然月的天数
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }


    //获取自然年的天数
    public static int getDaysOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_YEAR);
    }

    //获取自然年的天数
    public static int getDaysOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    public static List<Long> getTimesByWeek(Date date, int days) {
        List<Long> times = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int count = 1;
        while (count <= days) {
            calendar.set(Calendar.DAY_OF_WEEK, count++);
            times.add(calendar.getTimeInMillis());
        }
        return times;
    }

    public static List<Long> getTimesByMouth(Date date, int days) {
        List<Long> times = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int count = 1;
        while (count <= days) {
            calendar.set(Calendar.DAY_OF_MONTH, count++);
            times.add(calendar.getTimeInMillis());
        }
        return times;
    }

    public static List<Long> getTimesByYear(Date date, int days) {
        List<Long> times = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int count = 1;
        while (count <= days) {
            calendar.set(Calendar.DAY_OF_YEAR, count++);
            times.add(calendar.getTimeInMillis());
        }
        return times;
    }


    /**
     * 根据类别解析时间数据
     *
     * @param date
     * @param type
     * @return
     */
    public static long convertDateToDate(String date, String type) {
        switch (type) {
            case SECOND: {
                return str2Date(date, DATE_FORMATE_DEFAULT).getTime();
            }
            case "all": {
                return System.currentTimeMillis();
            }
            case MINUTE:
            case FIVE_MINUTE:
            case TEN_MINUTE:
            case THIRTY_MINUTE:
            case FIFTEEN_MINUTE: {
                return str2Date(date, DATE_FORMATE_MINUTE).getTime();
            }
            case "1h":
            case HOUR: {
                return str2Date(date, DATE_FORMATE_HOUR).getTime();
            }
            case "1nc":
            case MONTH: {
                return str2Date(date, DATE_FORMATE_MONTH).getTime();
            }
            case "1yc":
            case YEAR: {
                return str2Date(date, DATE_FORMATE_YEAR).getTime();
            }
            default: {
                return str2Date(date, DATE_FORMATE_DAY).getTime();
            }
        }
    }


    /**
     * 获取某段时这里写代码片间内的所有日期
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static ArrayList<String> findDates(long dBegin, long dEnd, String type) {
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTimeInMillis(dBegin);
        calBegin.set(Calendar.MILLISECOND, 0);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTimeInMillis(dEnd);
        // 测试此日期是否在指定日期之后
        ArrayList<String> array = new ArrayList<>();
        Boolean isFirst = true;
        while (calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type)) {
            if (isFirst && !type.equalsIgnoreCase("all")) {
                array.add(date2StrByType(dBegin, type));
            }
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            switch (type) {
                case "all": {
                    calBegin.setTimeInMillis(getNextTime(calEnd.getTimeInMillis(), type));
                    array.add("all");
                    break;
                }
                case SECOND: {
                    calBegin.set(Calendar.SECOND, calBegin.get(Calendar.SECOND) + 30);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_DEFAULT));
                    break;
                }
                case MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 1);
                    array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case FIVE_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 5);
                    //array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case TEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 10);
                    //array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case FIFTEEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 15);
                    //array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case THIRTY_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 30);
                    //array.add(date2Str(calBegin.getTime(), DATE_FORMATE_MINUTE));
                    break;
                }
                case "1h":
                case HOUR: {
                    calBegin.set(Calendar.HOUR_OF_DAY, calBegin.get(Calendar.HOUR_OF_DAY) + 1);
                    break;
                }
                case "1nc":
                case MONTH: {
                    calBegin.set(Calendar.DAY_OF_MONTH, 1);
                    calBegin.set(Calendar.MONTH, calBegin.get(Calendar.MONTH) + 1);
                    break;
                }
                case "1yc":
                case YEAR: {
                    calBegin.set(Calendar.DAY_OF_YEAR, 1);
                    calBegin.set(Calendar.YEAR, calBegin.get(Calendar.YEAR) + 1);
                    break;
                }
                default: {
                    calBegin.set(Calendar.DAY_OF_YEAR, calBegin.get(Calendar.DAY_OF_YEAR) + 1);
                    break;
                }

            }
            if ( !type.equalsIgnoreCase(SECOND) && !type.equalsIgnoreCase(MINUTE)
                    && calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type)) {
                array.add(date2StrByType(calBegin.getTimeInMillis(), type));
            }
            isFirst = false;
        }
        return array;
    }


    /**
     * 获取某段时这里写代码片间内的所有日期
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static ArrayList<Long> findTimeStamp(long dBegin, long dEnd, String type) {
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(new Date(dBegin));
        calBegin.set(Calendar.MILLISECOND, 0);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(new Date(dEnd));
        // 测试此日期是否在指定日期之后
        ArrayList<Long> array = new ArrayList<>();
        Boolean isFirst = true;
        while (calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type)) {
            if (isFirst) {
                array.add(dBegin);
            }
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            switch (type) {
                case SECOND: {
                    calBegin.set(Calendar.SECOND, calBegin.get(Calendar.SECOND) + 30);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 1);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case FIVE_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 5);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case TEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 10);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case FIFTEEN_MINUTE: {
                    calBegin.set(Calendar.MINUTE, calBegin.get(Calendar.MINUTE) + 15);
                    array.add(calBegin.getTimeInMillis());
                    break;
                }
                case HOUR: {
                    calBegin.set(Calendar.HOUR_OF_DAY, calBegin.get(Calendar.HOUR_OF_DAY) + 1);
                    break;
                }
                case "1nc":
                case MONTH: {
                    calBegin.set(Calendar.DAY_OF_MONTH, 1);
                    calBegin.set(Calendar.MONTH, calBegin.get(Calendar.MONTH) + 1);
                    break;
                }
                case "1yc":
                case YEAR: {
                    calBegin.set(Calendar.DAY_OF_YEAR, 1);
                    calBegin.set(Calendar.YEAR, calBegin.get(Calendar.YEAR) + 1);
                    break;
                }
                default: {
                    calBegin.set(Calendar.DAY_OF_YEAR, calBegin.get(Calendar.DAY_OF_YEAR) + 1);
                    break;
                }

            }
            if (!type.equalsIgnoreCase(FIFTEEN_MINUTE) && !type.equalsIgnoreCase(SECOND) && !type.equalsIgnoreCase(MINUTE) && !type.equalsIgnoreCase(FIVE_MINUTE) && !type.equalsIgnoreCase(TEN_MINUTE)
                    && calBegin.getTimeInMillis() < getNextTime(calEnd.getTimeInMillis(), type)) {
                array.add(calBegin.getTimeInMillis());
            }
            isFirst = false;
        }
        return array;
    }


    /**
     * 根据开始和结束时间返回influxDB查询的起始和结束时间
     *
     * @param start
     * @param end
     * @param type
     * @return
     */
    public static List<InfluxSearchTs> getInfluxSearchTs(long start, long end, String type) {
        List<String> dates = findDates(start, end, type);
        List<InfluxSearchTs> influxSearchTs = new ArrayList<>();
        if (dates.size() == 1) {
            dates = findDates(start, end+1000, type);
        }
        for (int i = 1; i < dates.size(); i++) {
            influxSearchTs.add(InfluxSearchTs.builder().start(str2DateByTyPe(dates.get(i - 1), type).getTime())
                    .end(str2DateByTyPe(dates.get(i), type).getTime() + 1000 * 60)
                    .type(type).build());
            if (str2DateByTyPe(dates.get(i), type).getTime() > System.currentTimeMillis()) {
                break;
            }
            //最后一条时间，加入下一个时间段的第一个时间点
            if (i == dates.size() - 1) {
                influxSearchTs.add(InfluxSearchTs.builder().start(str2DateByTyPe(dates.get(i), type).getTime())
                        .end(str2DateByTyPe(dates.get(i), type).getTime() + DAY_TIME)
                        .type(type).build());
            }
        }
        return influxSearchTs;

    }

    /**
     * 根据类型为结束时间做限制
     *
     * @param time
     * @param type
     * @return
     */
    public static Long getNextTime(long time, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        calendar.set(Calendar.MILLISECOND, 0);
        switch (type) {
            case SECOND: {
                return str2Date(TimeDiff.getTrulyMinute(time, type), DATE_FORMATE_DEFAULT).getTime();
            }
            case MINUTE:
            case FIVE_MINUTE:
            case TEN_MINUTE:
            case THIRTY_MINUTE:
            case FIFTEEN_MINUTE:
                return str2Date(TimeDiff.getTrulyMinute(time, type), DATE_FORMATE_MINUTE).getTime();
            case HOUR: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + 1);
                }
                return calendar.getTimeInMillis();
            }
            case "1d":
            case DAY: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0 && calendar.get(Calendar.HOUR_OF_DAY) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + 1);
                }
                return calendar.getTimeInMillis() - 1;
            }
            case "1nc":
            case MONTH: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0 && calendar.get(Calendar.HOUR_OF_DAY) != 0
                        && calendar.get(Calendar.DAY_OF_MONTH) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.DAY_OF_MONTH, 0);
                    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
                }
                return calendar.getTimeInMillis() - 1;
            }
            case "1yc":
            case YEAR: {
                if (calendar.get(Calendar.MINUTE) != 0 && calendar.get(Calendar.SECOND) != 0 && calendar.get(Calendar.MILLISECOND) != 0 && calendar.get(Calendar.HOUR_OF_DAY) != 0
                        && calendar.get(Calendar.DAY_OF_YEAR) != 0) {
                    calendar.set(Calendar.SECOND, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.DAY_OF_YEAR, 0);
                    calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 1);
                }
                return calendar.getTimeInMillis() - 1;
            }
            default: {
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + 1);
                return calendar.getTimeInMillis() - 1;
            }

        }
    }

    /**
     * 获取当年的第一天
     *
     * @return
     */
    public static Date getCurrYearFirst() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearFirst(currentYear);
    }

    /**
     * 获取当年的最后一天
     *
     * @return
     */
    public static Date getCurrYearLast() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearLast(currentYear);
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();

        return currYearLast;
    }


    /**
     * 获取系统内置量数据
     *
     * @param start
     * @param type
     * @return
     */
    public static long getSysDataSource(long start, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(start);
        switch (type) {
            case DateUtils.YEAR: {
                return calendar.get(Calendar.YEAR);
            }
            case DateUtils.MONTH: {
                return calendar.get(Calendar.MONTH) + 1;
            }
            case DateUtils.DAY: {
                return calendar.get(Calendar.DAY_OF_MONTH);
            }
            case DateUtils.HOUR: {
                return calendar.get(Calendar.HOUR_OF_DAY);
            }
            case DateUtils.WEEK: {
                return calendar.get(Calendar.WEEK_OF_MONTH);
            }
            default:
            case DateUtils.MINUTE_S: {
                return calendar.get(Calendar.MINUTE);
            }
        }
    }

    public static long between(Date date1, Date date2) {
        Calendar instance1 = Calendar.getInstance();
        Calendar instance2 = Calendar.getInstance();
        instance1.setTime(date1);
        instance2.setTime(date2);


        int date1Year = instance1.get(Calendar.YEAR);
        int date2Year = instance2.get(Calendar.YEAR);
        int date1Month = instance1.get(Calendar.MONTH);
        int date2Month = instance2.get(Calendar.MONTH);
        if (date1Year == date2Year) {
            return date2Month - date1Month;
        } else {
            return (date2Year - date1Year) * (12 - date1Month + date2Month);
        }
    }

    /**
     * 获取生产统计量开始时间
     *
     * @param start    开始时间
     * @param type     统计时间类别
     * @param template 统计数据源的统计模组
     * @return 开始时间
     */
    public static long getStatisticsStart(long start, String type, String template) {
        long result = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(start);
        //时间清理到该分钟的开始
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        start = calendar.getTimeInMillis();
        switch (type) {
            default:
            case MINUTE: {
                result = start - 1000 * 60;
                break;
            }
            case "5m": {
                result = start - 1000 * 60 * 5;
                break;
            }
            case "10m": {
                result = start - 1000 * 60 * 10;
                break;
            }
            case "30m": {
                result = start - 1000 * 60 * 30;
                break;
            }
            case "1h": {
                result = start - 1000 * 60 * 60;
                break;
            }
            case "1d": {
                result = start - 1000 * 60 * 60 * 24;
                break;
            }
        }
        //根据不同的统计模组设置新的开始时间
        if (template.equalsIgnoreCase(DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_CHANGE)) {
            result = result - 1000 * 60;
        }
        return result;
    }


    /**
     * 获取生产统计量保存时间
     *
     * @param start 开始时间
     * @param type  统计时间类别
     * @return 开始时间
     */
    public static long getStatisticsSave(long start, String type) {
        long result = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(start);
        //时间清理到该分钟的开始
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        start = calendar.getTimeInMillis();
        switch (type) {
            default:
            case MINUTE:
            case "5m":
            case "10m":
            case "30m": {
                result = start - TWO_MINUTE_TIME;
                break;
            }
            //小时和天，需要进入到上一个时间段，所以多减去一秒
            case "1h":
            case "1d": {
                result = start - (TWO_MINUTE_TIME + 1000);
                break;
            }
        }
        return result;
    }

    /**
     * 获取生产统计量结束时间
     *
     * @param start
     * @return
     */
    public static long getStatisticsEnd(long start, boolean orgin) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(start);
        //时间清理到该分钟的开始
        if (!orgin) {
            calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) - 1);
        }
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        start = calendar.getTimeInMillis();
        return start - 1000;
    }


    public static void main(String[] args) {
        findDates(Long.parseLong("1594137600000"), Long.parseLong("1594223999999"), "1d").forEach(str -> {
            System.out.println(str);
        });
    }

}
