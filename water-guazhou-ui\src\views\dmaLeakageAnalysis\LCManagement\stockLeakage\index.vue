<!-- 存量漏失评估 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <stationDetailMonitoring
      v-if="state.isDetail"
      :device="TableConfig.currentRow"
      @hiddenLoading="hiddenLoading"
      @back="state.isDetail = false"
    >
    </stationDetailMonitoring>
    <template v-else>
      <CardSearch
        ref="refSearch"
        :config="SearchConfig"
      ></CardSearch>
      <CardTable
        ref="refTable"
        class="card-table"
        :config="TableConfig"
      ></CardTable>
    </template>
    <NewOrder
      ref="refDialog"
      :default-values="{
        partitionId: TableConfig.selectList?.[0]?.partitionId
      }"
    ></NewOrder>
  </TreeBox>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import stationDetailMonitoring from './components/stationDetailMonitoring.vue'
import { ICardTableIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { usePartition } from '@/hooks/arcgis'
import {
  EditDMAPartitionStatus,
  EDMAStatus,
  GetDMAStockLossControlList
} from '@/api/mapservice/dma'
import { formatterDate } from '@/utils/GlobalHelper'
import NewOrder from '../components/NewOrder.vue'

const state = reactive<{
  isDetail: boolean
}>({
  isDetail: false
})
const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data
      // state.treeDataType = data.data.type as string
      // if (state.treeDataType === 'Project') {
      //   store.business.SET_selectedProject(data)
      await refreshData()
      // } else {
      //   nextTick(() => {
      //     state.stationId = data.id as string
      //   })
      // }
    }
  }
})
// 显示加载遮层
const hiddenLoading = () => {
  TreeData.loading = false
}

// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    date: moment().format(formatterDate)
  },
  filters: [
    {
      type: 'date',
      label: '日期',
      field: 'date',
      clearable: false
    },
    {
      type: 'input',
      label: '分区名称',
      field: 'name'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '工单',
          type: 'success',
          iconifyIcon: 'ep:plus',
          click: () => {
            if (!TableConfig.selectList?.length) {
              SLMessage.warning('请选择一条数据')
            } else if (TableConfig.selectList.length > 1) {
              SLMessage.warning('只能选择一条数据')
            } else {
              refDialog.value?.openDialog()
            }
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})

// 列表
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  handleRowDbClick(row) {
    TableConfig.currentRow = row
    state.isDetail = true
  },
  handleSelectChange(val) {
    TableConfig.selectList = val
  },
  columns: [
    { prop: 'partitionName', label: '分区名称', minWidth: 120 },
    { prop: 'statusName', label: '分区状态', minWidth: 120 },
    { prop: 'userNum', label: '用户数(户)', minWidth: 120 },
    { prop: 'inlet', label: '进水口(个)', minWidth: 120 },
    // { prop: 'date', label: '日期', minWidth: 120 },
    {
      prop: 'supplyTotal',
      label: '供水量',
      unit: '(m³)',
      minWidth: 120
    },
    {
      prop: 'minValue',
      label: 'MNF夜间最小流',
      unit: '(m³/h)',
      minWidth: 220,
      sortable: true
    },
    {
      prop: 'minFlow',
      label: '夜间最小水量值',
      unit: '(m³)',
      minWidth: 210,
      sortable: true
    },
    {
      prop: 'legalUseWater',
      label: '用户合法用水量',
      unit: '(m³/h)',
      minWidth: 210,
      sortable: true
    },
    {
      prop: 'netNightFlow',
      label: '净夜间流量',
      unit: '(m³/h)',
      minWidth: 180,
      sortable: true
    },
    {
      prop: 'lossWater',
      label: '漏失水量',
      unit: '(m³)',
      minWidth: 240,
      sortable: true
    },
    {
      prop: 'mainLineLength',
      label: '管线长度',
      unit: '(km)',
      minWidth: 240,
      sortable: true
    },
    {
      prop: 'unitPipeNightFlowMin',
      label: '单位管线夜间小流',
      unit: '(m³/h/km)',
      minWidth: 240,
      sortable: true
    },
    {
      prop: 'avgDayFlow',
      label: '日均流量',
      unit: '(m³/h)',
      minWidth: 150,
      sortable: true
    },
    {
      prop: 'mnfDivideAvgDayFlow',
      label: 'MNF/日均流量',
      unit: '(%)',
      minWidth: 180,
      sortable: true
    },
    {
      prop: 'lossValuation',
      label: '漏损评估',
      tag: true,
      tagColor(row, val) {
        return val === '一般'
          ? '#e6a23c'
          : val === '较好'
            ? '#318DFF'
            : val === '较差'
              ? '#f56c6c'
              : '#909399'
      },
      formatter(row, value) {
        return value || '--'
      }
    }
  ],
  operations: [
    {
      text: '切营运',
      isTextBtn: true,
      perm: true,
      iconifyIcon: 'ep:edit',
      click: row => shiftStatus(row)
    }
  ],
  singleSelect: true,
  select(row) {
    if (
      TableConfig.selectList?.length
      && TableConfig.selectList.findIndex(
        item => item.partitionId === row.partitionId
      ) !== -1
    ) {
      TableConfig.selectList = []
    } else {
      TableConfig.selectList = [row]
    }
  },
  operationWidth: '100px',
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const shiftStatus = (row: any) => {
  SLConfirm('是否将状态修改为营运中?', '提示信息')
    .then(async () => {
      try {
        const res = await EditDMAPartitionStatus({
          id: row.partitionId,
          status: EDMAStatus.YingYunZhong
        })
        if (res.data.code === 200) {
          SLMessage.success('操作成功')
          refreshData()
        } else {
          SLMessage.success('操作失败')
        }
      } catch (error) {
        SLMessage.error('操作失败')
      }
    })
    .catch(() => {
      //
    })
}
const refDialog = ref<InstanceType<typeof NewOrder>>()

// 数据获取
const refreshData = async () => {
  if (!TreeData.currentProject) return
  const query = refSearch.value?.queryParams || {}
  const res = await GetDMAStockLossControlList({
    ...query,
    partitionId: TreeData.currentProject?.value
  })
  TableConfig.dataList = res.data?.data || []
  TableConfig.pagination.total = res.data?.total || 0
}

const partition = usePartition()
onMounted(async () => {
  const p1 = partition.getTree()
  const p2 = partition.getList()
  await Promise.all([p1, p2])

  TreeData.data = partition.Tree.value || []
  TreeData.currentProject = TreeData.data[0]
  refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
