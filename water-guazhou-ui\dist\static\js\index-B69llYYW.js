import{d as R,r as K,a8 as Q,am as X,bu as Y,o as Z,bA as $,bv as ee,g as y,n as g,p as l,q as S,an as w,e4 as ae,C as te}from"./index-r0dFAfgr.js";import{u as oe}from"./useAmap-D6DJ1T90.js";import{a as ne}from"./URLHelper-B9aplt5w.js";const se={class:"location-map"},le={key:0,class:"location-search-box"},ie={class:"location-input-box"},re={key:0,class:"message-text"},de=R({__name:"index",props:{modelValue:{default:()=>[]},initCenterMark:{type:Boolean,default:!1},light:{type:Boolean,default:!0},hideInput:{type:Boolean,default:!1},polylines:{default:()=>[]},resultType:{default:"arrStr"},required:{type:<PERSON>olean,default:!1},disabled:{type:Boolean,default:!1},markers:{default:()=>[]},maplayer:{default:()=>[]},useHeatMap:{type:Boolean,default:!1},heatMapData:{default:()=>[]}},emits:["update:modelValue","onmaploaded","getplacename"],setup(B,{expose:A,emit:E}){const o=B,d=E;let c;const p=window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter||[106.5,29.84],t=K({x:void 0,y:void 0,centerMarker:null,amap:null}),{setMarker:u,setCenter:M,initAMap:F,setPolylines:O,setInfoWindow:P,setInfoWindows:T,setMultiInfoWindows:H,clearInfoWindow:v,setMarkers:W,initHeatMap:D,resetHeatMap:k,toggleHeatMap:G,setListInfoWindow:J,clear:U,getGeocoder:z}=oe(),m=ne(),V=()=>{t.x!==void 0&&t.y!==void 0?t.centerMarker=u([t.x,t.y],{icon:m}):t.centerMarker=u(p,{icon:m})},i=Q(()=>t.x!==void 0&&t.y!==void 0);X(()=>o.modelValue,()=>{f(),r()});const r=()=>{const e=x();h(e)},b=async()=>{const e={events:{mapmove:a=>{if(!o.disabled){const n=a.getCenter();t.x=n.lng,t.y=n.lat,h([t.x,t.y]),C()}},zoomend:()=>{o.useHeatMap&&k(o.heatMapData)}},search:{input:"innerAmapSearch",select:a=>{M(a)}},mapStyle:o.light===!1?"amap://styles/darkblue":"",mapLayer:o.maplayer?o.maplayer:[]};t.amap=await F("innerAmap",e),q(e),r(),d("onmaploaded"),N()},q=e=>{O(o.polylines,e)},x=()=>{const e=i.value?[t.x,t.y]:p;return t.amap&&t.amap.setCenter(e),e},h=e=>{var a;(!e||e[0]===void 0||e[1]===void 0)&&(e=p),!t.centerMarker&&o.initCenterMark&&V(),typeof((a=t.centerMarker)==null?void 0:a.setPosition)=="function"&&t.centerMarker.setPosition(e)},C=()=>{let e="";o.resultType==="arrStr"?e=i.value?JSON.stringify([t.x,t.y]):JSON.stringify(""):e=i.value?[t.x,t.y].join(","):JSON.stringify(""),L(()=>{z("1",a=>{d("getplacename",a)})},200),d("update:modelValue",e)},f=()=>{var e,a;if(o.modelValue instanceof Array&&o.modelValue.length===2)t.x=parseFloat((e=o.modelValue[0])==null?void 0:e.toString()),t.y=parseFloat((a=o.modelValue[1])==null?void 0:a.toString());else{const n=o.modelValue&&o.modelValue.toString();try{const s=JSON.parse(n);s instanceof Array&&I(s)}catch{const j=n==null?void 0:n.trim().split(",");I(j)}}},I=e=>{if((e==null?void 0:e.length)===2){const a=parseFloat(e[0]),n=parseFloat(e[1]);t.x=isNaN(a)?void 0:a,t.y=isNaN(n)?void 0:n}},N=e=>{console.log(o.markers),o.markers.forEach(a=>{a.icon||(a.icon=m),t.centerMarker=W(a.items?a.items:[],{icon:a.icon},e)})},L=(e,a)=>{c&&clearTimeout(c),c=setTimeout(()=>{e()},a)};Y(()=>{f()});const _=e=>{var n;const a=e.target;(n=a==null?void 0:a.className)!=null&&n.includes&&a.className.includes("close-wrapper")&&v()};return Z(async()=>{b(),document.addEventListener("click",_)}),$(()=>{document.removeEventListener("click",_)}),A({...ee(t),formateModelValue:f,handleLocation:x,initMap:b,positionValid:i,updateValue:C,setInfoWindow:P,setInfoWindows:T,clearInfoWindow:v,setMultiInfoWindows:H,setMark:V,setMarker:u,setCenter:M,initHeatMap:D,resetHeatMap:k,toggleHeatMap:G,multiplePunctuation:N,setListInfoWindow:J,clear:U}),(e,a)=>{const n=ae;return y(),g("div",se,[e.hideInput?w("",!0):(y(),g("div",le,[l("div",ie,[a[2]||(a[2]=l("span",{class:"location-label"},"经度：",-1)),S(n,{modelValue:t.x,"onUpdate:modelValue":a[0]||(a[0]=s=>t.x=s),disabled:e.disabled,precision:4,size:"small",class:"location-input",onChange:r},null,8,["modelValue","disabled"]),a[3]||(a[3]=l("span",{class:"location-label margin-l-10"},"纬度：",-1)),S(n,{modelValue:t.y,"onUpdate:modelValue":a[1]||(a[1]=s=>t.y=s),disabled:e.disabled,precision:4,size:"small",class:"location-input",onChange:r},null,8,["modelValue","disabled"])]),e.disabled?w("",!0):(y(),g("p",re," 提示：请拖动地图 设置位置信息 "))])),a[4]||(a[4]=l("div",{class:"get-location-box amap-container"},[l("div",{id:"innerAmap",style:{height:"100%",width:"100%"}})],-1))])}}}),me=te(de,[["__scopeId","data-v-5d6cee25"]]);export{me as _};
