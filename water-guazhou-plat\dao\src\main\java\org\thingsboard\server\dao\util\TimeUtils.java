package org.thingsboard.server.dao.util;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

public class TimeUtils {
    public static Date lastOneMonth() {
        return new Date(new Date().getTime() - TimeUnit.DAYS.toMillis(30));
    }

    public static Date firstDayOfMonthByNow() {
        return DateTime.now().dayOfMonth().withMinimumValue().toDate();
    }

    public static Date lastDayOfMonthByNow() {
        return DateTime.now().dayOfMonth().withMaximumValue().toDate();
    }

    public static Date firstDayOfYearByNow() {
        return DateTime.now().dayOfYear().withMinimumValue().toDate();
    }

    public static Date lastDayOfYearByNow() {
        return DateTime.now().dayOfYear().withMaximumValue().toDate();
    }

    public static Date computeDefaultIfNull(Date timeStamp, Supplier<Date> delegate) {
        if (timeStamp == null) {
            return delegate.get();
        }

        return timeStamp;
    }

    public static Date computeDefaultIfNull(Object timeStamp, Supplier<Date> delegate) {
        if (timeStamp == null) {
            return delegate.get();
        }

        return evaluteDate(timeStamp);
    }

    public static Date defaultIfNull(Object timeStamp, Date defaultValue) {
        if (timeStamp == null) {
            return defaultValue;
        }

        return evaluteDate(timeStamp);
    }

    public static Date evaluteDate(Object timeStamp) {
        if (timeStamp instanceof Date) {
            return (Date) timeStamp;
        }

        if (timeStamp instanceof Long)
            return new Date((Long) timeStamp);
        else if (timeStamp instanceof String) {
            try {
                return new Date(Long.parseLong((String) timeStamp));
            } catch (Exception e) {
                try {
                    return new DateTime(timeStamp).toDate();
                } catch (Exception e2) {
                    return DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").withOffsetParsed().parseDateTime((String) timeStamp).toDate();
                }
            }
        }

        return null;
    }

    public static String formatDateTime(Date date) {
        if (date == null)
            return null;
        return new DateTime(date).toString("yyyy-MM-dd HH:mm:ss");
    }

    public static String formatDate(Date date) {
        if (date == null)
            return null;
        return new DateTime(date).toString("yyyy-MM-dd");
    }

    public static DateTime ceilSeason(DateTime time) {
        int monthOfYear = time.getMonthOfYear();
        switch (monthOfYear) {
            case 11:
            case 12:
                return time.withMonthOfYear(1);
            case 2:
            case 3:
                return time.withMonthOfYear(4);
            case 5:
            case 6:
                return time.withMonthOfYear(7);
            case 8:
            case 9:
                return time.withMonthOfYear(10);
            default:
                return time;
        }
    }

    public static String timeUnitToChinese(TimeUnit unit) {
        switch (unit) {
            case NANOSECONDS:
                return "纳秒";
            case MICROSECONDS:
                return "微秒";
            case MILLISECONDS:
                return "毫秒";
            case SECONDS:
                return "秒";
            case MINUTES:
                return "分钟";
            case HOURS:
                return "小时";
            case DAYS:
                return "天";
            default:
                return "未知单位";
        }
    }

    public static Map<String, LocalDate> getLocalDateByType(String type, String date, String start, String end) {
        LocalDate endDate = LocalDate.now().plusDays(1L);
        LocalDate startDate = endDate.minusMonths(1L);
        switch (type) {
            case "year":
                startDate = java.time.LocalDate.of(Integer.valueOf(date), 1, 1);
                endDate = startDate.plusYears(1L);
                if (LocalDate.now().getYear() == startDate.getYear() && endDate.isBefore(LocalDate.now())) {
                    endDate = LocalDate.now().plusMonths(1L);
                }
                break;

            case "month":
                startDate = java.time.LocalDate.parse(start + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                endDate = java.time.LocalDate.parse(end + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusMonths(1L);
                break;

            case "monthOne":
                startDate = java.time.LocalDate.parse(date + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                endDate = startDate.plusMonths(1L);
                break;

            case "day":
                startDate = java.time.LocalDate.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                endDate = java.time.LocalDate.parse(end, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1L);
                break;

            case "yearInterval": // 时间区间-年:
                startDate = java.time.LocalDate.of(Integer.valueOf(start), 1, 1);
                endDate = java.time.LocalDate.of(Integer.valueOf(end), 1, 1).plusYears(1L);
                break;
        }

        Map<String, LocalDate> result = new HashMap<>();
        result.put("start", startDate);
        result.put("end", endDate);

        return result;
    }

    public static Date floorMonth(Date date) {
        return new DateTime(date).toInstant().toDate();
    }

    public static Date ceilMonth(Date date) {
        return new DateTime(date).plusMonths(1).minusMillis(1).toDate();
    }

    public static Date floorDay(Date dayTime) {
        return new DateTime(dayTime).millisOfDay().withMinimumValue().toDate();
    }

}
