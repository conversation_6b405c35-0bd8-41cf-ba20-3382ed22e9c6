import { GetGisSchemeList, PostGisScheme } from '@/api/mapservice/scheme'
import { SLConfirm, SLMessage } from '@/utils/Message'

export const useScheme = (type: ISchemeType) => {
  const schemeType = ref<ISchemeType>(type)
  const refSaveScheme = ref<InstanceType<typeof import('@/views/arcMap/components/search/Scheme/SaveScheme.vue')['default']>>()
  const refSchemeManage = ref<InstanceType<typeof import('@/views/arcMap/components/search/Scheme/SchemeManage.vue')['default']>>()
  const getSaveSchemeRef = (el: any) => {
    refSaveScheme.value = el
  }
  const getSchemeManageRef = (el: any) => {
    refSchemeManage.value = el
  }
  const openSaveDialog = () => {
    refSaveScheme.value?.openDialog()
  }
  const openManagerDialog = () => {
    refSchemeManage.value?.openDialog()
  }
  const list = ref<any[]>([])
  const total = ref<number>(0)
  const getList = async (
    params: Partial<{
      name: string
      type: string
    }>
  ) => {
    try {
      const res = await GetGisSchemeList(params)
      list.value = res.data.data.data || []
      total.value = res.data.data.total || 0
      return {
        data: list.value,
        total: total.value
      }
    } catch (error) {
      return {
        data: [],
        total: 0
      }
    }
  }
  const parseScheme = (row: any) => {
    const detail = row.detail ? JSON.parse(row.detail) : undefined
    if (!detail) {
      SLMessage.error('当前方案已失效')
      return
    }
    return detail
  }
  const submitScheme = params => {
    SLConfirm('确定保存为方案？', '提示信息')
      .then(async () => {
        try {
          const res = await PostGisScheme(params)
          if (res.data.code === 200) {
            SLMessage.success('方案保存成功')
            refSaveScheme.value?.closeDialog()
          } else {
            SLMessage.error('方案保存失败')
            console.log(res.data.message)
          }
        } catch (error) {
          SLMessage.error('方案保存失败')
          console.log(error)
        }
      })
      .catch(() => {
        //
      })
  }
  return {
    schemeType,
    list,
    refSaveScheme,
    refSchemeManage,
    getList,
    getSaveSchemeRef,
    getSchemeManageRef,
    submitScheme,
    parseScheme,
    openManagerDialog,
    openSaveDialog
  }
}
