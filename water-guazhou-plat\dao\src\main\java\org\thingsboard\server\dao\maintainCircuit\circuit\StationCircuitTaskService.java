package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationCircuitTaskListRequest;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask;

import java.util.List;

public interface StationCircuitTaskService {

    void save(List<StationCircuitTask> taskList);

    /**
     * 分页查询站点养护任务
     *
     * @param request 请求参数
     * @return 数据
     */
    PageData<StationCircuitTask> findList(StationCircuitTaskListRequest request);

    void save(StationCircuitTask entity);

    void remove(List<String> ids);

    void receive(String id, User current) throws ThingsboardException;

    void complete(String id, User currentUser) throws ThingsboardException;

    void audit(StationCircuitTask task, User currentUser) throws ThingsboardException;

    void resend(StationCircuitTask task, User currentUser) throws ThingsboardException;
}
