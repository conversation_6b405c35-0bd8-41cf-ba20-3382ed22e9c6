package org.thingsboard.server.dao.assay;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.assay.AssayReportAddress;
import org.thingsboard.server.dao.model.sql.assay.AssayReportItem;
import org.thingsboard.server.dao.model.sql.assay.AssayReportType;
import org.thingsboard.server.dao.sql.assay.AssayReportAddressMapper;
import org.thingsboard.server.dao.sql.assay.AssayReportItemMapper;
import org.thingsboard.server.dao.sql.assay.AssayReportTypeMapper;

import java.util.*;

@Slf4j
@Service
public class AssayReportTypeServiceImpl implements AssayReportTypeService {

    @Autowired
    private AssayReportTypeMapper assayReportTypeMapper;

    @Autowired
    private AssayReportItemMapper assayReportItemMapper;

    @Autowired
    private AssayReportAddressMapper assayReportAddressMapper;

    @Override
    public List<AssayReportType> findList(String name, String tenantId) {
        QueryWrapper<AssayReportType> assayReportTypeQueryWrapper = new QueryWrapper<>();
        assayReportTypeQueryWrapper.eq("tenant_id", tenantId);
        assayReportTypeQueryWrapper.like("name", name);
        assayReportTypeQueryWrapper.orderByDesc("create_time");
        return assayReportTypeMapper.selectList(assayReportTypeQueryWrapper);
    }

    @Override
    public void save(AssayReportType entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            assayReportTypeMapper.insert(entity);
        } else {
            assayReportTypeMapper.updateById(entity);
        }
    }

    @Override
    public List<AssayReportItem> getItemList(String reportId) {
        List<AssayReportItem> assayReportItems = assayReportItemMapper.getList(reportId);

        return assayReportItems;
    }

    @Override
    public void saveItemList(List<AssayReportItem> entityList, String tenantId) {
        if (entityList.size() == 0) {
            return;
        }

    }

    @Override
    public List<AssayReportAddress> getAddressList(String reportId) {
        List<AssayReportAddress> assayReportItems = assayReportAddressMapper.getList(reportId);

        return assayReportItems;
    }

    @Override
    @Transactional
    public void saveChildList(JSONObject jsonObject, String tenantId) {
        String reportId = jsonObject.getString("reportId");
        List<String> itemIdList = jsonObject.getJSONArray("itemIdList").toJavaList(String.class);
        // 先清除所有的
        Map deleteMap = new HashMap();
        deleteMap.put("report_id", reportId);
        assayReportAddressMapper.deleteByMap(deleteMap);
        AssayReportAddress assayReportAddress;
        List<AssayReportAddress> assayReportAddresses = new ArrayList<>();
        List<String> addressIdList = jsonObject.getJSONArray("addressIdList").toJavaList(String.class);
        AssayReportItem assayReportItem;
        List<AssayReportItem> assayReportItemList = new ArrayList<>();
        for (String id : itemIdList) {
            assayReportItem = new AssayReportItem();
            assayReportItem.setReportId(reportId);
            assayReportItem.setItemId(id);
            assayReportItem.setTenantId(tenantId);
            assayReportItem.setCreateTime(new Date());
            assayReportItemList.add(assayReportItem);
        }

        // 先清除所有的
        assayReportItemMapper.deleteByMap(deleteMap);

        for (String id : addressIdList) {
            assayReportAddress = new AssayReportAddress();
            assayReportAddress.setReportId(reportId);
            assayReportAddress.setAddressId(id);
            assayReportAddress.setTenantId(tenantId);
            assayReportAddress.setCreateTime(new Date());

            assayReportAddresses.add(assayReportAddress);
        }

        // 保存
        if (assayReportItemList.size() > 0) {
            assayReportItemMapper.batchInsert(assayReportItemList);
        }

        // 保存
        if (assayReportAddresses.size() > 0) {
            assayReportAddressMapper.batchInsert(assayReportAddresses);
        }
    }


    @Override
    public void remove(List<String> ids) {
        assayReportTypeMapper.deleteBatchIds(ids);
    }
}
