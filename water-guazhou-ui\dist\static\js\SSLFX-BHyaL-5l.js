import{_ as P}from"./index-C9hz-UZb.js";import{d as B,j as I,c as i,r as _,o as W,ay as j,g as d,h as b,F as D,p as c,q as T,i as l,aq as z,C as G}from"./index-r0dFAfgr.js";import{_ as M}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import{u as N}from"./useDetector-BRcb7GRN.js";import{r as X}from"./echarts-DP4wVWSW.js";import{d as H}from"./statistics-CeyexT_5.js";const J={class:""},K={ref:"agriEcoDev",class:"chart-box"},Q={ref:"agriEcoDev",class:"chart-box"},R=B({__name:"SSLFX",setup(U){const y=I(),g=i(),r=_({pieChartOption:null,lineChartOption:null,showTable:!1}),w=_({defaultParams:{type:"month",grade:"1",tc:"c"},filters:[{label:"",field:"grade",type:"select",options:[{label:"一级分区",value:"1"},{label:"二级分区",value:"2"}],onChange:()=>{u()}},{label:"",field:"type",type:"radio-button",options:[{label:"月",value:"month"},{label:"季度",value:"quarter"},{label:"年",value:"year"}],onChange:()=>{u()}},{label:"",field:"tc",type:"radio-button",options:[{label:"",iconifyIcon:"ep:pie-chart",value:"c"},{label:"",iconifyIcon:"ep:grid",value:"t"}],onChange:a=>{r.showTable=a==="t"}}]}),p=_({indexVisible:!0,dataList:[],columns:[{label:"区域名称",prop:"name"}],pagination:{hide:!0}}),q=(a,o)=>{const s=o.map(n=>({type:"line",data:n.y||[],name:n.name,smooth:!0,symbol:"none"}));return{backgroundColor:y.isDark?"transparent":"#F4F7FA",tooltip:{trigger:"axis"},legend:{top:"top",left:"right",textStyle:{color:y.isDark?"#fff":"#333"}},grid:{left:60,right:40,top:40,bottom:30,containLabel:!0},xAxis:{axisLabel:{color:"#409EFF"},boundaryGap:!1,type:"category",data:a,axisTick:{show:!1}},yAxis:{type:"value",axisLabel:{color:"#409EFF"},name:"各区域供水量(m³)",axisTick:{show:!1},axisLine:{show:!1},splitLine:{lineStyle:{color:"#409EFF",type:"dashed"}}},series:s}},u=()=>{var o,s,n;(o=h.value)==null||o.clear(),(s=f.value)==null||s.clear();const a=((n=g.value)==null?void 0:n.queryParams)||{};H({...a}).then(m=>{var C,x,S;const t=m.data.data||[],E=t==null?void 0:t.map(e=>({name:e.name,value:e.total,scale:e.rate}));r.pieChartOption=X(E,"m³"),r.lineChartOption=q(((C=t[0])==null?void 0:C.x)||[],t||[]),p.columns=[{fixed:"left",minWidth:160,label:"区域名称",prop:"name"},...((S=(x=t[0])==null?void 0:x.x)==null?void 0:S.map(e=>({minWidth:120,label:e,prop:e})))||[]],p.dataList=t.map(e=>{var F;const k={name:e.name};return(F=e.x)==null||F.map((V,A)=>{var L;k[V]=(L=e.y)==null?void 0:L[A]}),k})})},O=N(),f=i(),h=i(),v=i();return W(()=>{u(),O.listenToMush(v.value,()=>{var a,o;(a=f.value)==null||a.resize(),(o=h.value)==null||o.resize()})}),(a,o)=>{const s=M,n=j("VChart"),m=z,t=P;return d(),b(t,{title:"售水量分析"},{query:D(()=>[c("div",J,[T(s,{ref_key:"refSearch",ref:g,config:l(w)},null,8,["config"])])]),default:D(()=>[c("div",{ref_key:"refDiv",ref:v,class:"charts-box"},[c("div",K,[T(n,{ref_key:"refChart1",ref:f,option:l(r).pieChartOption},null,8,["option"])],512),c("div",Q,[l(r).showTable?(d(),b(m,{key:0,config:l(p)},null,8,["config"])):(d(),b(n,{key:1,ref_key:"refChart2",ref:h,option:l(r).lineChartOption},null,8,["option"]))],512)],512)]),_:1})}}}),ne=G(R,[["__scopeId","data-v-25a3327b"]]);export{ne as default};
