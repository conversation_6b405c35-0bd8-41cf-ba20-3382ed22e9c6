package org.thingsboard.server.dao.util.imodel;

import org.thingsboard.server.dao.util.imodel.response.DepartmentInfo;
import org.thingsboard.server.dao.util.imodel.response.cache.Cache;
import org.thingsboard.server.dao.util.imodel.response.cache.CacheGroup;
import org.thingsboard.server.dao.util.imodel.response.cache.CacheType;

import java.util.function.BiConsumer;

public class Environment {

    public static final BiConsumer<String, DepartmentInfo> DEPARTMENT_REMOVE_DETECTOR =
            (key, departmentInfo) -> departmentInfo.invalid();
    public static final BiConsumer<String, String> USER_REMOVE_DETECTOR = (key, name) ->
            getEnvironment().getUserIdToDepartmentCache().invalidate(key);

    public void invalidateUsernameCache(String uuid) {
        getUsernameCache().invalidate(uuid);
    }

    public void invalidDepartmentCache(String id) {
        if (id == null) {
            return;
        }
        getDepartmentIdToDepartmentCache().invalidate(id);
    }

    private static class EnvironmentHolder {
        private static final Environment INSTANCE = new Environment();
    }

    private final CacheGroup cacheGroup = new CacheGroup();


    public static Environment getEnvironment() {
        return EnvironmentHolder.INSTANCE;
    }

    @Deprecated
    public Cache<String, String> getTenantNameCache() {
        return cacheGroup.get(CacheType.TENANT_NAME);
    }

    public Cache<String, String> getUsernameCache() {
        return cacheGroup.get(CacheType.USERNAME, USER_REMOVE_DETECTOR);
    }

    public Cache<String, DepartmentInfo> getUserIdToDepartmentCache() {
        return cacheGroup.get(CacheType.USERID_TO_DEPARTMENT);
    }

    public Cache<String, DepartmentInfo> getDepartmentIdToDepartmentCache() {
        return cacheGroup.get(CacheType.DEPARTMENT_ID_TO_DEPARTMENT, DEPARTMENT_REMOVE_DETECTOR);
    }
}
