package org.thingsboard.server.dao.operatingIncomeInput;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.OperatingIncomeInput;

import java.util.List;

public interface OperatingIncomeInputService {

    /**
     * 查询指定年份的营收数据录入情况
     *
     * @param stationId 站点ID
     * @param year      年份
     * @param tenantId  租户ID
     * @return 数据
     */
    List<OperatingIncomeInput> findList(String stationId, String year, TenantId tenantId);

    void update(OperatingIncomeInput entity);

    /**
     * 查询指定站点的数据详情
     * 包含：本月售水量、本月水费、累计水费
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    JSONObject getDetail(String stationId, TenantId tenantId);

    /**
     * 查询指定站点的趋势数据
     * 包含：售水量同比趋势、水费同比趋势、水费月报
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    JSONObject getTrend(String stationId, TenantId tenantId);
}
