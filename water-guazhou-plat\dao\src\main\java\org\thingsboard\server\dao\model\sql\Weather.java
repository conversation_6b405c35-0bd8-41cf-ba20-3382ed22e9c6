package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_weather")
public class Weather {

    private String id;

    private String date;

    private String week;

    private String dayweather;

    private String nightweather;

    private String daytemp;

    private String nighttemp;

    private String daywind;

    private String nightwind;

    private String daypower;

    private String nightpower;

    private String daytemp_float;

    private String nighttemp_float;

    private Date createTime;

    private String tenantId;



}
