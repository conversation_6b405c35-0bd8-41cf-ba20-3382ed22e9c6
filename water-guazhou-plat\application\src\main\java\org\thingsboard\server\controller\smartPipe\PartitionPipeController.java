package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPipe;
import org.thingsboard.server.dao.smartPipe.PartitionPipeService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-管网信息
 */
@RestController
@RequestMapping("api/spp/dma/partition/partitionPipe")
public class PartitionPipeController extends BaseController {

    @Autowired
    private PartitionPipeService partitionPipeService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionPipe partitionPipe) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionPipe.setTenantId(tenantId);

        return IstarResponse.ok(partitionPipeService.save(partitionPipe));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionPipeService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionPipeService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
