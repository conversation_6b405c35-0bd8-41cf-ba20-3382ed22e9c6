package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRearrangeRecord;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

@Getter
@Setter
public class GuardRearrangeRecordPageRequest extends PageableQueryEntity<GuardRearrangeRecord> {
    // 地点id
    private String placeId;

}
