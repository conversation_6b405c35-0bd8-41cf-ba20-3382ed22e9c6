<template>
  <div class="video_box">
    <video
      ref="videoPlayer"
      class="video-js"
      poster="/src/assets/images/other/video-icon.png"
      notSupportedMessage="无效视频地址"
      x-webkit-airplay="allow"
      webkit-playsinline
      playsinline
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      preload="auto"
    >
      <source :src="videoInfo.videoUrl.m3u8uri" />
      <!-- application/x-mpegURL  video/mp4  -->
    </video>
  </div>
</template>

<script>
import useGlobal from '@/hooks/global/useGlobal'

const { $video } = useGlobal()
export default {
  name: 'VideoPlayer',
  // eslint-disable-next-line vue/require-prop-types
  props: ['videoInfo'],
  data() {
    return {
      player: null
    }
  },
  mounted() {
    // 播放参数
    const options = {
      controls: true, // 是否显示底部控制栏
      preload: 'auto', // 加载<video>标签后是否加载视频
      autoplay: 'muted', // 静音播放
      // playbackRates: [0.5, 1, 1.5, 2], // 倍速播放
      width: '640',
      height: '247',
      controlBar: {
        // 自定义按钮的位置
        children: [
          {
            name: 'playToggle'
          },
          {
            name: 'progressControl'
          },
          {
            name: 'currentTimeDisplay'
          },
          {
            name: 'timeDivider'
          },
          {
            name: 'durationDisplay'
          },

          {
            name: 'volumePanel', // 音量调整方式横线条变为竖线条
            inline: false
          },
          {
            name: 'pictureInPictureToggle' // 画中画播放模式
          },
          {
            name: 'fullscreenToggle'
          }
        ]
      }
    }
    this.player = $video(this.$refs.videoPlayer, options, function onPlayerReady() {
      console.log('onPlayerReady', this)
    })
  },
  beforeUnmount() {
    if (this.player) {
      this.player.dispose()
    }
  },
  methods: {}
}
</script>

<style scoped>
.video_box {
  width: 100%;
  height: 100%;
}
.video-js {
  width: 100%;
  height: 100%;
}
</style>
