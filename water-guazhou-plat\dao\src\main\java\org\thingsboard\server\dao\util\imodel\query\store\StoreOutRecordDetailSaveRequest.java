package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class StoreOutRecordDetailSaveRequest extends SaveRequest<StoreOutRecordDetail> {
    // 设备序列号
    @NotNullOrEmpty
    private String serialId;

    // 出库单主表ID
    @NotNullOrEmpty(parentIgnore = true)
    private String mainId;

    // 货架Id
    @NotNullOrEmpty
    private String shelvesId;

    // 目标出货数量
    @NotNullOrEmpty
    private Integer num;

    @Override
    public String valid(IStarHttpRequest request) {
        if (num == null || num <= 0) {
            return "含有非法的出库数量";
        }
        return super.valid(request);
    }

    public StoreOutRecordDetail build() {
        StoreOutRecordDetail entity = new StoreOutRecordDetail();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public StoreOutRecordDetail update(String id) {
        StoreOutRecordDetail entity = new StoreOutRecordDetail();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(StoreOutRecordDetail entity) {
        entity.setSerialId(serialId);
        entity.setMainId(mainId);
        entity.setNum(num);
        entity.setShelvesId(shelvesId);
    }
}