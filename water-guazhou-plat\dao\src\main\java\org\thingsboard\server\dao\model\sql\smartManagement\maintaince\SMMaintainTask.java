package org.thingsboard.server.dao.model.sql.smartManagement.maintaince;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import javax.persistence.Id;
import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sm_maintain_task")
public class SMMaintainTask {
    // id
    @Id
    private String id;

    // 任务名称
    private String name;

    // 设备Id
    private String device;

    // 设备名称
    private String deviceName;

    // 养护人员Id
    @ParseUsername(withDepartment = true)
    private String maintainUser;

    // 开始时间
    private Date beginTime;

    // 结束时间
    private Date endTime;

    // 任务状态
    private GeneralTaskStatus status;

    // 备注
    private String remark;

    // 创建人Id
    @ParseUsername(withDepartment = true)
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户Id
    private String tenantId;

    // 总共的任务数
    private Integer totalTaskItemCount;

    // 完成的任务数
    private Integer completedTaskItemCount;

}
