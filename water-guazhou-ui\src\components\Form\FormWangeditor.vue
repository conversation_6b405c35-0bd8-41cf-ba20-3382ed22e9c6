<template>
  <div class="wangborder">
    <Toolbar
      class="wangborderbottom"
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <Editor
      ref="editor"
      v-model="valueHtml"
      style="height: 400px; overflow-y: hidden"
      :default-config="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
    />
  </div>
</template>

<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { onBeforeUnmount, shallowRef } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { SlateElement, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { ElMessage } from 'element-plus'
import { useAppStore, useUserStore } from '@/store'
import { postImg } from '@/api/image/index'

const editor = ref<any>()

type ImageElement = SlateElement & {
  src: string
  alt: string
  url: string
  href: string
}

type InsertFnType = (url: string, alt: string, href: string) => void

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  // 图片上传地址
  url: {
    type: String,
    default: ''
  },
  // 菜单模式
  mode: {
    type: String,
    default: 'simple'
  }
})

const emits = defineEmits(['update:modelValue'])

// 内容 HTML
const valueHtml = computed({
  get: () => props.modelValue,
  set: nv => {
    if (nv === '<p><br></p>') { emits('update:modelValue', '') } else {
      emits('update:modelValue', nv)
    }
  }
})

// 图片上传地址
const imgActionUrl = props.url && props.url !== ''
  ? props.url
  : useAppStore().actionUrl + 'file/api/upload/image'

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

const toolbarConfig: Partial<IToolbarConfig> = {
  /* 工具栏配置 */
}

// 提示信息
const editorConfig: Partial<IEditorConfig> = {
  MENU_CONF: {},
  placeholder: '请输入内容...'
}

if (editorConfig.MENU_CONF) {
  editorConfig.MENU_CONF['uploadImage'] = {
    // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
    allowedFileTypes: ['image/*'],
    async customUpload(file: File, insertFn: InsertFnType) {
      console.log(file)
      const data = new FormData()
      data.append('file', file) // file 即选中的文件
      postImg(data, imgActionUrl, useUserStore().token || '').then(res => {
        insertFn(res.data, res.data, res.data)
      }).catch(error => {
        ElMessage.warning(error + '支持格式png、jpg、jpeg')
      })
    }
  }
}

// 自定义校验图片
function customCheckImageFn(
  src: string,
  alt: string,
  url: string
): boolean | undefined | string {
  if (!src) {
    return
  }
  if (src.indexOf('http') !== 0) {
    return '图片网址必须以 http/https 开头'
  }
  console.log(alt, url)
  return true
}

// 转换图片链接
function customParseImageSrc(src: string): string {
  if (src.indexOf('http') !== 0) {
    return `http://${src}`
  }
  return src
}

// 插入图片
if (editorConfig.MENU_CONF) {
  editorConfig.MENU_CONF['insertImage'] = {
    onInsertedImage(imageNode: ImageElement | null) {
      if (imageNode == null) return
      const { src, alt, url, href } = imageNode
      console.log('inserted image', src, alt, url, href)
    },
    checkImage: customCheckImageFn, // 也支持 async 函数
    parseImageSrc: customParseImageSrc // 也支持 async 函数
  }
}

// 编辑图片
if (editorConfig.MENU_CONF) {
  editorConfig.MENU_CONF['editImage'] = {
    onUpdatedImage(imageNode: ImageElement | null) {
      if (imageNode == null) return
      const { src, alt, url } = imageNode
      console.log('updated image', src, alt, url)
    },
    checkImage: customCheckImageFn, // 也支持 async 函数
    parseImageSrc: customParseImageSrc // 也支持 async 函数
  }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = editor => {
  editorRef.value = editor // 记录 editor 实例，重要！
}

</script>

<style lang="scss">
.w-e-toolbar {
  background-color: var(--el-bg-color) !important;
}

html.dark {
  --w-e-textarea-bg-color: #22252f;
  --w-e-textarea-color: #fff;
  /* ...其他... */
}

.wangborder {
  border: 1px solid #747886
}

.wangborderbottom {
  border-bottom: 1px solid #747886
}
</style>
