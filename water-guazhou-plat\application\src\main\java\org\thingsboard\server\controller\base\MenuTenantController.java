/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuTenant;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.menu.MenuTenantService;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/menu")
public class MenuTenantController extends BaseController {

    public static final String MENU_TENANT_ID = "id";
    public static final String MENU_TENANT_TENANT_ID = "tenantId";

    @Autowired
    private MenuTenantService menuTenantService;


    @PreAuthorize("hasAuthority('SYS_ADMIN')")
    @RequestMapping(value = "/tenant", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_MENU_ADD)
    public List<MenuTenant> saveMenuTenant(@RequestBody Map map) throws ThingsboardException {
        List<String> ids = (List<String>) map.get("ids");
        // 获取tenantID
        String strTenantId = (String) map.get("tenantId");
        if (StringUtils.isBlank(strTenantId)) {
            throw new ThingsboardException("invalid arguments",
                    ThingsboardErrorCode.INVALID_ARGUMENTS);
        }
        List<MenuPoolId> menuPoolIdList = new ArrayList<>();
        for (String id : ids) {
            menuPoolIdList.add(new MenuPoolId(toUUID(id)));
        }

        TenantId tenantId = new TenantId(toUUID(strTenantId));

        return checkNotNull(menuTenantService.saveMenuTenant(menuPoolIdList, tenantId));
    }


    @PreAuthorize("hasAuthority('SYS_ADMIN')")
    @RequestMapping(value = "/tenant/{id}", method = RequestMethod.GET)
    public Menu getById(@PathVariable(MENU_TENANT_ID) String strMenuTenantId) throws ThingsboardException {
        checkParameter(MENU_TENANT_ID, strMenuTenantId);
        try {
            MenuTenantId menuTenantId = new MenuTenantId(toUUID(strMenuTenantId));
            return menuTenantService.findMenuById(menuTenantId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAuthority('SYS_ADMIN')")
    @RequestMapping(value = "/tenant/findByTenant/{tenantId}", method = RequestMethod.GET)
    public List<Menu> findByTenant(@PathVariable(value = MENU_TENANT_TENANT_ID)String strTenantId) throws ThingsboardException {
        checkParameter(MENU_TENANT_TENANT_ID, strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            if (tenantId.isNullUid()) {
                throw new ThingsboardException("invalid arguments",
                        ThingsboardErrorCode.INVALID_ARGUMENTS);
            }
            return menuTenantService.findMenuByTenantId(tenantId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority( 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/tenant/findByCurrentUser", method = RequestMethod.GET)
    public List<Menu> findByTenant() throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        if (user.getAuthority() != Authority.TENANT_ADMIN && user.getAuthority() != Authority.TENANT_SYS) {
            throw new ThingsboardException("You don't have permission to perform this operation!",
                    ThingsboardErrorCode.PERMISSION_DENIED);
        }
        return menuTenantService.findMenuByTenantId(getTenantId());
    }

    @PreAuthorize("hasAuthority('SYS_ADMIN')")
    @RequestMapping(value = "/tenant/getTreeByTenantId/{tenantId}", method = RequestMethod.GET)
    public List<String> getTreeByTenantId(@PathVariable(value = MENU_TENANT_TENANT_ID)String strTenantId) throws ThingsboardException {
        checkParameter(MENU_TENANT_TENANT_ID, strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            if (tenantId.isNullUid()) {
                throw new ThingsboardException("invalid arguments",
                        ThingsboardErrorCode.INVALID_ARGUMENTS);
            }

            return menuTenantService.getTreeByTenantId(tenantId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }


}
