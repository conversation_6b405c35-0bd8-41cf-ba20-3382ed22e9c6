<template>
  <div
    v-if="row.supply?.length"
    class="card-wrapper"
    :class="useAppStore().isDark ? 'darkblue' : ''"
  >
    <div v-for="(item, i) in row.supply" :key="i" class="card watersupply">
      <div class="header">
        <img class="image" :src="item.img" alt="" />
        <span class="title">{{ item.title }}</span>
      </div>
      <div class="content">
        <span class="count">{{ item.count }}</span>
        <span class="unit">{{ item.unit }}</span>
      </div>
    </div>
  </div>
  <div v-else class="empty">
    <span class="empty-text">暂无数据</span>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store';

defineProps<{
  row: any;
  movelValue?: any;
}>();
</script>
<style lang="scss" scoped>
.card-wrapper {
  display: flex;
  flex-wrap: wrap;
  &.darkblue {
    .card {
      color: #fff;
      background-color: #283e55;
    }
  }
}

.card {
  color: #333;
  padding: 12px;
  background-color: #c7c7c7;

  &.watersupply {
    width: calc(50% - 6px);
    margin-bottom: 12px;
    &:not(:nth-child(even)) {
      margin-right: 12px;
    }
    .header {
      font-size: 12px;
      height: 40px;
      display: flex;
      align-items: center;
      .image {
        width: 2em;
        height: 2em;
        margin-right: 12px;
      }
    }
    .content {
      color: #3fa6d6;
      .count {
        font-size: 20px;
      }
      .unit {
        font-size: 12px;
      }
    }
  }
}
.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  .empty-text {
    margin: auto;
  }
}
</style>
