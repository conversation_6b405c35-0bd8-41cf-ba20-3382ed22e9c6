import{d as J,a8 as T,r as $,c as C,am as P,g as b,h as F,F as s,p as n,q as l,G as _,n as z,aJ as K,aB as M,x as h,H as q,I as G,K as H,aK as W,aL as j,N as Q,J as X,O as Y,bU as Z,bW as ee,L as ae,C as le}from"./index-r0dFAfgr.js";import{p as oe}from"./pumpRoomInfo-DV75B9MO.js";const te={class:"scheme-dialog"},se={class:"pump-config-section"},de={class:"add-pump-btn"},me={class:"scheme-description"},ne={class:"description-item"},pe={class:"description-item"},ie={class:"dialog-footer"},ue=J({__name:"AddSchemeDialog",props:{visible:{type:Boolean,default:!1},pumpRoomId:{type:String,default:""}},emits:["update:visible","confirm"],setup(D,{emit:k}){const i=D,w=k,v=T({get:()=>i.visible,set:a=>w("update:visible",a)}),d=$({schemeName:"",schemeDescription:"",schemeRemark:""}),u=C([]),r=C([{pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""},{pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""}]),g=async()=>{if(!i.pumpRoomId){u.value=[];return}try{const a=await oe({page:1,size:1e3,pumpRoomId:i.pumpRoomId});console.log("泵机接口返回数据:",a);let e=[];a&&a.data&&a.data.code===200&&(a.data.data&&Array.isArray(a.data.data.data)?e=a.data.data.data:a.data.data&&Array.isArray(a.data.data.records)&&(e=a.data.data.records)),u.value=e.map(o=>({label:`${o.name} (${o.code})`,value:o.id,data:o})),console.log("解析后的泵机选项:",u.value)}catch(a){console.error("获取泵机列表失败:",a),h.error("获取泵机列表失败"),u.value=[]}},E=(a,e)=>{if(e){const o=u.value.find(p=>p.value===e);if(o&&o.data){if(o.data.performanceParameters)try{const p=JSON.parse(o.data.performanceParameters);a.fixedPower=p.power||"",a.fixedFlow=p.flow||""}catch(p){console.log("解析性能参数失败:",p)}a.pumpDisplayName=o.label}}else a.fixedPower="",a.fixedFlow="",a.pumpDisplayName=""},U=()=>{r.value.push({pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""})},R=a=>{r.value.length>1&&r.value.splice(a,1)},V=()=>{v.value=!1,x()},I=()=>{if(!d.schemeName.trim()){h.warning("请输入方案名称");return}const a=r.value.filter(o=>o.pumpName&&o.fixedPower&&o.fixedFlow);if(a.length===0){h.warning("请至少配置一个泵组");return}const e={...d,pumpConfigs:a};console.log("提交方案数据:",e),w("confirm",e),x()},x=()=>{d.schemeName="",d.schemeDescription="",d.schemeRemark="",r.value=[{pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""},{pumpName:"",fixedPower:"",fixedFlow:"",pumpDisplayName:""}]};return P(()=>i.visible,a=>{a&&(x(),g())}),P(()=>i.pumpRoomId,a=>{a&&i.visible&&g()}),(a,e)=>{const o=q,p=G,B=H,y=W,A=j,c=Q,f=X,L=Y,N=Z,S=ee,O=ae;return b(),F(O,{modelValue:v.value,"onUpdate:modelValue":e[3]||(e[3]=t=>v.value=t),title:"泵组方案",width:"800px","before-close":V},{footer:s(()=>[n("div",ie,[l(f,{onClick:V},{default:s(()=>e[9]||(e[9]=[_("取消")])),_:1}),l(f,{type:"primary",onClick:I},{default:s(()=>e[10]||(e[10]=[_("提交")])),_:1})])]),default:s(()=>[n("div",te,[l(B,{model:d,"label-width":"100px"},{default:s(()=>[l(p,{label:"方案名称:"},{default:s(()=>[l(o,{modelValue:d.schemeName,"onUpdate:modelValue":e[0]||(e[0]=t=>d.schemeName=t),placeholder:"请输入...",style:{width:"300px"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),n("div",se,[e[6]||(e[6]=n("div",{class:"section-title"},"泵组配置",-1)),l(L,{data:r.value,border:"",style:{width:"100%"}},{default:s(()=>[l(c,{prop:"pumpName",label:"启用水泵",width:"200"},{default:s(({row:t})=>[l(A,{modelValue:t.pumpName,"onUpdate:modelValue":m=>t.pumpName=m,placeholder:"请选择泵机",onChange:m=>E(t,m)},{default:s(()=>[l(y,{label:"请选择",value:""}),(b(!0),z(M,null,K(u.value,m=>(b(),F(y,{key:m.value,label:m.label,value:m.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(c,{prop:"fixedPower",label:"额定功率",width:"150"},{default:s(({row:t})=>[l(o,{modelValue:t.fixedPower,"onUpdate:modelValue":m=>t.fixedPower=m,placeholder:"请输入..."},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(c,{prop:"fixedFlow",label:"额定流量",width:"150"},{default:s(({row:t})=>[l(o,{modelValue:t.fixedFlow,"onUpdate:modelValue":m=>t.fixedFlow=m,placeholder:"请输入..."},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(c,{label:"操作",width:"100"},{default:s(({$index:t})=>[l(f,{type:"primary",link:"",size:"small",onClick:m=>R(t)},{default:s(()=>e[4]||(e[4]=[_("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),n("div",de,[l(f,{type:"primary",onClick:U},{default:s(()=>e[5]||(e[5]=[_("添加水泵")])),_:1})])]),n("div",me,[l(S,{gutter:20},{default:s(()=>[l(N,{span:12},{default:s(()=>[n("div",ne,[e[7]||(e[7]=n("label",null,"方案描述:",-1)),l(o,{modelValue:d.schemeDescription,"onUpdate:modelValue":e[1]||(e[1]=t=>d.schemeDescription=t),type:"textarea",rows:3,placeholder:"通过实时水泵运行计算组团"},null,8,["modelValue"])])]),_:1}),l(N,{span:12},{default:s(()=>[n("div",pe,[e[8]||(e[8]=n("label",null,"方案备注:",-1)),l(o,{modelValue:d.schemeRemark,"onUpdate:modelValue":e[2]||(e[2]=t=>d.schemeRemark=t),type:"textarea",rows:3,placeholder:"通过实时水泵运行计算组团"},null,8,["modelValue"])])]),_:1})]),_:1})])])]),_:1},8,["modelValue"])}}}),fe=le(ue,[["__scopeId","data-v-c7dd6fab"]]);export{fe as default};
