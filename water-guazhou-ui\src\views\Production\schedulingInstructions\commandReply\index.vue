<!-- 指令回复 -->
<template>
  <div class="wrapper">
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <CardSearch
      ref="refForm"
      class="mag_top_10"
      :config="cardSearchConfig"
    />
  </div>
</template>

<script lang="ts" setup>
import Cookies from 'js-cookie'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { commandStatus, finishedCondition } from '../data'
import { ICardSearchIns } from '@/components/type'
import router from '@/router'
import { getOrderRecordList, postcommandReply } from '@/api/productionScheduling/schedulingInstructions'
import { formatDate } from '@/utils/DateFormatter'

const refForm = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '确认完成', field: 'isComplete', type: 'switch' },
    { label: '确认备注',
      field: 'replyContent',
      type: 'input',
      inputStyle: {
        width: '600px'
      } },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '确认',
          icon: ICONS.ADD,
          click: () => commandReply()
        },
        {
          perm: true,
          text: '跳转查询',
          type: 'success',
          click: () => routingJump()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  handleSelectChange: val => {
    TableConfig.selectList = val
  },
  columns: [
    { label: '回复状态',
      prop: 'commandStatus',
      tag: true,
      tagColor: (row): string => commandStatus[row.commandStatus]?.color || '',
      formatter: val => commandStatus[val.commandStatus]?.value || '' },
    { label: '执行时间', prop: 'executionTime' },
    { label: '指令内容', prop: 'sendContent' },
    { label: '回复内容', prop: 'replyContent' },
    { label: '回复时间', prop: 'replyTime' },
    { label: '完成状态',
      prop: 'completeStatus',
      tag: true,
      tagColor: (row): string => finishedCondition[row.completeStatus]?.color || '',
      formatter: val => finishedCondition[val.completeStatus]?.value || '' }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

function commandReply() {
  const ids:string[] = []
  TableConfig.selectList?.forEach(item => {
    ids.push(item.id)
  })
  const params = {
    idList: ids,
    isComplete: refForm.value?.queryParams && refForm.value?.queryParams.isComplete || false,
    replyContent: refForm.value?.queryParams && refForm.value?.queryParams.replyContent || false
  }
  postcommandReply(params).then(() => {
    ElMessage.success('回复成功')
    TableConfig.selectList = []
    refreshData()
  }).catch(error => {
    ElMessage.warning(error)
  })
}

function routingJump(row?: any) {
  router.push({
    name: 'SCDD010204',
    query: row
  })
}

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    receiveDeptId: Cookies.get('departmentId') || '',
    commandStatus: 'WAITING_REPLY'
  }
  getOrderRecordList(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  refreshData()
})

</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100% - 70px);
}
.mag_top_10{
  margin-top: 10px;
}
</style>

<style scoped>
.card-table :deep(.el-table__expand-icon--expanded) {
  width: 6px;
}
</style>
