package org.thingsboard.server.dao.model.sql.statistic;

import java.util.List;

public class ScopeStatisticLongWrapper implements StatisticItem {
    private final List<StatisticLong> data;

    public ScopeStatisticLongWrapper(List<StatisticLong> data) {
        this.data = data;
    }

    public List<StatisticLong> getData() {
        return data;
    }

    @Override
    public boolean isEmpty() {
        return data.isEmpty();
    }

    @Override
    public boolean flattenSplittable() {
        return true;
    }
}
