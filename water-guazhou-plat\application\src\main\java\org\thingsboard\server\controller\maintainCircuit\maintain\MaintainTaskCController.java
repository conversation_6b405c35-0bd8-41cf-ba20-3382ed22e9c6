package org.thingsboard.server.controller.maintainCircuit.maintain;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.maintain.MaintainTaskCService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskC;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/maintain/task/c")
public class MaintainTaskCController extends BaseController {

    @Autowired
    private MaintainTaskCService maintainTaskCService;

    @PostMapping
    public IstarResponse reviewer(@RequestBody MaintainTaskC maintainTaskC) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        boolean checkUser = maintainTaskCService.checkUser(maintainTaskC.getMainId(), userId);
        if (!checkUser) {
            return IstarResponse.error("您没有该任务的保养权限");
        }
        maintainTaskCService.save(maintainTaskC);
        return IstarResponse.ok("保存成功");
    }

    /**
     * 保养信息
     * @param deviceLabelCode
     * @return
     */
    @GetMapping("statistics/{deviceLabelCode}")
    public IstarResponse statistics(@PathVariable String deviceLabelCode) {
        return IstarResponse.ok(maintainTaskCService.statistics(deviceLabelCode));
    }

}
