package org.thingsboard.server.dao.model.VO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 供水数据VO
 */
@Data
public class WaterSupplyVO {

    private String stationId;

    private String name;

    private String ts;

    // 供水量
    private BigDecimal totalFlow;

    // 耗电
    private BigDecimal energy;

    // 吨水耗电
    private BigDecimal unitConsumption;

    // 运行时长
    private BigDecimal runtime;

}
