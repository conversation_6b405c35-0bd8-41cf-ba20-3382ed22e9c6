package org.thingsboard.server.dao.shuiwu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.shuiwu.MeterIemiRelationEntity;
import org.thingsboard.server.dao.sql.shuiwu.WaterMeterIemiRepository;

@Slf4j
@Service
public class WaterMeterIemiServiceImpl implements WaterMeterIemiService {

    @Autowired
    private WaterMeterIemiRepository waterMeterIemiRepository;


    @Override
    public MeterIemiRelationEntity findById(String meterCode) {
        return waterMeterIemiRepository.findOne(meterCode);
    }
}
