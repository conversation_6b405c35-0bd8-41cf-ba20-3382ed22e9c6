const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/SymbolTileRenderer-B5TrSrP8.js","static/js/Point-WxyopZva.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/CircularArray-CFz2ft5h.js","static/js/enums-L38xj_2E.js","static/js/color-DAS1c3my.js","static/js/enums-B5k73o5q.js","static/js/enums-BDQrMlcz.js","static/js/VertexElementDescriptor-BOD-G50G.js","static/js/number-CoJp78Rz.js","static/js/AttributeStoreView-B0-phoCE.js","static/js/definitions-826PWLuy.js","static/js/TiledDisplayObject-C5kAiJtw.js","static/js/Container-BwXq1a-x.js","static/js/Texture-BYqObwfn.js","static/js/visualVariablesUtils-0WgcmuMn.js","static/js/visualVariablesUtils-7_6yXvXo.js","static/js/FramebufferObject-8j9PRuxE.js","static/js/BufferPool-BAwXXd5w.js","static/js/schemaUtils-DLXXqxNF.js","static/js/utils-DPUVnAXL.js","static/js/MaterialKey-BYd7cMLJ.js","static/js/alignmentUtils-CkNI7z7C.js","static/js/cimAnalyzer-CMgqZsaO.js","static/js/fontUtils-BuXIMW9g.js","static/js/BidiEngine-CsUYIMdL.js","static/js/GeometryUtils-B7ExOJII.js","static/js/Rect-CUzevAry.js","static/js/callExpressionWithFeature-DgtD4TSq.js","static/js/quantizationUtils-DtI9CsYu.js","static/js/floatRGBA-PQQNbO39.js","static/js/ExpandedCIM-C1laM-_7.js","static/js/util-DPgA-H2V.js","static/js/BaseTileRenderer-uWU_bsvU.js","static/js/WGLContainer-Dyx9110G.js","static/js/vec4f32-CjrfB-0a.js","static/js/ProgramTemplate-tdUBoAol.js","static/js/StyleDefinition-Bnnz5uyC.js","static/js/config-MDUrh2eL.js","static/js/GeometryUtils-BRRfazic.js","static/js/earcut-BJup91r2.js","static/js/FeatureContainer-B5oUlI2-.js","static/js/TileContainer-CC8_A7ZF.js","static/js/HeatmapTileRenderer-BVdzfQjx.js","static/js/BitmapTileContainer-CnUUv4uK.js","static/js/Bitmap-CraE42_6.js"])))=>i.map(i=>d[i]);
import{e as u,y as h,a as R,v as de,o as ye,b as fe,a9 as ge,aF as b,ab as _e,i as x,u as W,s as q,x as me,j as C}from"./Point-WxyopZva.js";import{g as G,bH as D,gr as ve,bY as we,bs as Z,gs as be,gt as Re,bX as ce,j as Fe,bG as X,fG as Y,db as H,bE as xe,gu as ee,gv as Se,gw as te,gx as Ee,gy as qe,gz as L,gA as Q,gB as Ie,dZ as z,bk as Oe,w as Ce,gC as Te,gD as Ue,f$ as Ae,gE as ke,g0 as Ve,dO as $e}from"./MapView-DaoQedLH.js";import{a4 as S,aO as U,R as p,a3 as ie,T as P,b2 as Pe,aW as re,a5 as Ne,a_ as Je}from"./index-r0dFAfgr.js";import{_ as He,l as N,w as he,a as se,k as Le,B as Qe}from"./widget-BcWKanF2.js";import{a as ae}from"./Container-BwXq1a-x.js";import{S as ze}from"./definitions-826PWLuy.js";import{f as je,u as Me}from"./LayerView-BSt9B8Gh.js";import{I as Be,h as Ge}from"./schemaUtils-DLXXqxNF.js";import{e as De}from"./util-DPgA-H2V.js";import{o as ne}from"./floorFilterUtils-DZ5C6FQv.js";import{s as j,d as Ke}from"./popupUtils-BjdidZV3.js";import{i as We}from"./RefreshableLayerView-DUeNHzrW.js";import"./pe-B8dP0-Ut.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./enums-L38xj_2E.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./number-CoJp78Rz.js";import"./utils-DPUVnAXL.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./ExpandedCIM-C1laM-_7.js";let J=class extends G{constructor(){super(...arguments),this.isAggregate=!0}getEffectivePopupTemplate(t=!1){if(this.popupTemplate)return this.popupTemplate;const i=this.sourceLayer&&this.sourceLayer.featureReduction;return i&&"popupTemplate"in i&&i.popupEnabled?i.popupTemplate:null}getObjectId(){return this.attributes.aggregateId}};u([h({type:Boolean})],J.prototype,"isAggregate",void 0),J=u([R("esri.AggregateGraphic")],J);const K=J;let m=class extends de{constructor(e){super(e),this._filter=null,this.duration=S("mapview-transitions-duration"),this._excludedEffectView=new ae(e),this._includedEffectView=new ae(e)}get excludedEffects(){return this._excludedEffectView.effects}set featureEffect(e){this._get("featureEffect")!==e&&this._transitionTo(e)}get filter(){var e;return this._filter||((e=U(this.featureEffect))==null?void 0:e.filter)||null}get hasEffects(){return this._excludedEffectView.hasEffects||this._includedEffectView.hasEffects}get includedEffects(){return this._includedEffectView.effects}set scale(e){this._set("scale",e),this._excludedEffectView.scale=e,this._includedEffectView.scale=e}get transitioning(){return this._excludedEffectView.transitioning||this._includedEffectView.transitioning}transitionStep(e,t){this._set("scale",t),this.transitioning?(this._includedEffectView.transitionStep(e,t),this._excludedEffectView.transitionStep(e,t),this.transitioning||(this._filter=null)):(this._excludedEffectView.scale=t,this._includedEffectView.scale=t)}endTransitions(){this._includedEffectView.endTransitions(),this._excludedEffectView.endTransitions(),this._filter=null}_transitionTo(e){const t=this._get("featureEffect"),i=U(e),r=U(i==null?void 0:i.includedEffect),s=U(i==null?void 0:i.excludedEffect),n=this._includedEffectView.canTransitionTo(r)&&this._excludedEffectView.canTransitionTo(s);this._includedEffectView.effect=r,this._excludedEffectView.effect=s,this._set("featureEffect",i),this._filter=(i==null?void 0:i.filter)||(t==null?void 0:t.filter)||null,n||this.endTransitions()}};u([h()],m.prototype,"_filter",void 0),u([h()],m.prototype,"_excludedEffectView",void 0),u([h()],m.prototype,"_includedEffectView",void 0),u([h()],m.prototype,"duration",void 0),u([h()],m.prototype,"excludedEffects",null),u([h()],m.prototype,"featureEffect",null),u([h()],m.prototype,"filter",null),u([h()],m.prototype,"hasEffects",null),u([h()],m.prototype,"includedEffects",null),u([h({value:0})],m.prototype,"scale",null),u([h()],m.prototype,"transitioning",null),m=u([R("esri.layers.effects.FeatureEffectView")],m);const Ze=m;let A=class extends D{constructor(){super(...arguments),this.features=[]}readFeatures(e,t){var s;const i=fe.fromJSON(t.spatialReference),r=[];for(let n=0;n<e.length;n++){const a=e[n],l=K.fromJSON(a),o=a.geometry&&a.geometry.spatialReference;p(l.geometry)&&!o&&(l.geometry.spatialReference=i);const c=a.aggregateGeometries,d=l.aggregateGeometries;if(c&&p(d))for(const f in d){const y=d[f],g=(s=c[f])==null?void 0:s.spatialReference;p(y)&&!g&&(y.spatialReference=i)}r.push(l)}return r}};u([h({type:[K],json:{write:!0}})],A.prototype,"features",void 0),u([ye("features")],A.prototype,"readFeatures",null),A=u([R("esri.rest.support.AggregateFeatureSet")],A);const Xe=A;async function Ye(e,t){if(!e)return null;switch(e.type){case"symbol":return new(await ie(async()=>{const{default:i}=await import("./SymbolTileRenderer-B5TrSrP8.js");return{default:i}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46]))).default(t);case"heatmap":return new(await ie(async()=>{const{default:i}=await import("./HeatmapTileRenderer-BVdzfQjx.js");return{default:i}},__vite__mapDeps([47,1,2,3,4,5,6,48,49,17,15,11,18,16,38,21,12,39,9,10,8,13,40,25,26,24,41,42,43,44,46,37]))).default(t)}}function M(e){return e.some(t=>t.globalId)}function T(e){return e.filter(t=>!t.error).map(t=>t.objectId??t.globalId).filter(t=>t!=null)}function oe(e,t){const i=new Set(e);for(const r of t.values())i.add(r);return i}function le(e,t){const i=new Set(e);for(const r of t.values())i.delete(r);return i}let k=class extends de{constructor(t){super(t),this._hasGlobalIds=!1,this._notifyUpdating=()=>{this.notifyChange("updating")}}normalizeCtorArgs(t){return this._queueProcessor=new ve({concurrency:1,process:t.process}),{}}destroy(){this.clear()}get updating(){return this._queueProcessor.length>0}clear(){this._queueProcessor.clear()}push(t){const i=this._queueProcessor,r=i.last();switch(t.type){case"update":case"refresh":if((r==null?void 0:r.type)===t.type)return;i.push(t).then(this._notifyUpdating,this._notifyUpdating);break;case"edit":{const s=(r==null?void 0:r.type)==="processed-edit"?r:null;s&&i.popLast();const n=this._mergeEdits(s,t);for(const a of n)a&&i.push(a).then(this._notifyUpdating,this._notifyUpdating);break}}this.notifyChange("updating")}_mergeEdits(t,i){const{addedFeatures:r,deletedFeatures:s,updatedFeatures:n}=i.edits;if(this._hasGlobalIds=this._hasGlobalIds||M(r)||M(n)||M(s),this._hasGlobalIds)return[t,{type:"processed-edit",edits:{addOrModified:[...r,...n],removed:s}}];const a=new Set(T((t==null?void 0:t.edits.addOrModified)??[])),l=new Set(T((t==null?void 0:t.edits.removed)??[])),o=new Set([...T(r),...T(n)]),c=new Set(T(s));return[{type:"processed-edit",edits:{addOrModified:Array.from(oe(le(a,c),o)).map(d=>({objectId:d})),removed:Array.from(oe(le(l,o),c)).map(d=>({objectId:d}))}}]}};u([h({readOnly:!0})],k.prototype,"updating",null),u([h()],k.prototype,"process",void 0),k=u([R("esri.views.2d.layers.support.FeatureCommandQueue")],k);const et=k;function tt(e){return Array.isArray(e)}let E=class extends He{constructor(e){super(e),this._startupResolver=ge(),this.isReady=!1}initialize(){this._controller=new AbortController,this.addResolvingPromise(this._startWorker(this._controller.signal))}destroy(){this._controller.abort(),this._connection&&this._connection.close()}set tileRenderer(e){this.client.tileRenderer=e}get closed(){return this._connection.closed}async startup(e,t,i,r){await this.when();const s=this._controller.signal,n=tt(i.source)?{transferList:i.source,signal:s}:void 0,a={service:i,config:t,tileInfo:e.tileInfo.toJSON(),tiles:r};await this._connection.invoke("startup",a,n),this._startupResolver.resolve(),this._set("isReady",!0)}async updateTiles(e){return await this._startupResolver.promise,b(this._connection.invoke("updateTiles",e))}async update(e){const t={config:e};return await this._startupResolver.promise,this._connection.invoke("update",t)}async applyUpdate(e){return await this._startupResolver.promise,this._connection.invoke("applyUpdate",e)}async setHighlight(e){return await this._startupResolver.promise,b(this._connection.invoke("controller.setHighlight",e))}async stop(){if(await this._startupResolver.promise,!this.closed)return b(this._connection.invoke("stop"))}async refresh(e){return await this._startupResolver.promise,b(this._connection.invoke("controller.refresh",e))}async querySummaryStatistics(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.querySummaryStatistics",{query:e.toJSON(),params:t},i)}async queryAggregateSummaryStatistics(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregateSummaryStatistics",{query:e.toJSON(),params:t},i)}async queryUniqueValues(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryUniqueValues",{query:e.toJSON(),params:t},i)}async queryAggregateUniqueValues(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregateUniqueValues",{query:e.toJSON(),params:t},i)}async queryClassBreaks(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryClassBreaks",{query:e.toJSON(),params:t},i)}async queryAggregateClassBreaks(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregateClassBreaks",{query:e.toJSON(),params:t},i)}async queryHistogram(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryHistogram",{query:e.toJSON(),params:t},i)}async queryAggregateHistogram(e,t,i){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregateHistogram",{query:e.toJSON(),params:t},i)}async queryFeatures(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryFeatures",e==null?void 0:e.toJSON(),t)}async queryVisibleFeatures(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryVisibleFeatures",e==null?void 0:e.toJSON(),t)}async queryObjectIds(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryObjectIds",e==null?void 0:e.toJSON(),t)}async queryFeatureCount(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryFeatureCount",e==null?void 0:e.toJSON(),t)}async queryExtent(e,t){return this._connection.invoke("controller.queryExtent",e.toJSON(),t)}async queryLatestObservations(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryLatestObservations",e.toJSON(),t)}async queryStatistics(e){return await this._startupResolver.promise,this._connection.invoke("controller.queryStatistics",e)}async queryAggregates(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregates",e==null?void 0:e.toJSON(),t)}async queryAggregateCount(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregateCount",e==null?void 0:e.toJSON(),t)}async queryAggregateIds(e,t){return await this._startupResolver.promise,this._connection.invoke("controller.queryAggregateIds",e==null?void 0:e.toJSON(),t)}async getObjectId(e){return await this._startupResolver.promise,this._connection.invoke("controller.getObjectId",e)}async getDisplayId(e){return await this._startupResolver.promise,this._connection.invoke("controller.getDisplayId",e)}async getFeatures(e){return await this._startupResolver.promise,this._connection.invoke("controller.getFeatures",e)}async getAggregates(){return await this._startupResolver.promise,this._connection.invoke("controller.getAggregates")}async getAggregateValueRanges(){return await this._startupResolver.promise,this._connection.invoke("controller.getAggregateValueRanges")}async mapValidDisplayIds(e){return await this._startupResolver.promise,this._connection.invoke("controller.mapValidDisplayIds",e)}async onEdits(e){return await this._startupResolver.promise,b(this._connection.invoke("controller.onEdits",e))}async enableEvent(e,t){return await this._startupResolver.promise,b(this._connection.invoke("controller.enableEvent",{name:e,value:t}))}async pauseStream(){return await this._startupResolver.promise,b(this._connection.invoke("controller.pauseStream"))}async resumeStream(){return await this._startupResolver.promise,b(this._connection.invoke("controller.resumeStream"))}async sendMessageToSocket(e){return await this._startupResolver.promise,b(this._connection.invoke("controller.sendMessageToSocket",e))}async sendMessageToClient(e){return await this._startupResolver.promise,b(this._connection.invoke("controller.sendMessageToClient",e))}async updateCustomParameters(e){return await this._startupResolver.promise,b(this._connection.invoke("controller.updateCustomParameters",e))}async _startWorker(e){try{this._connection=await we("Pipeline",{client:this.client,strategy:"dedicated",signal:e})}catch(t){_e(t)}}};u([h()],E.prototype,"isReady",void 0),u([h({constructOnly:!0})],E.prototype,"client",void 0),u([h()],E.prototype,"tileRenderer",null),E=u([R("esri.views.2d.layers.support.FeatureLayerProxy")],E);const it=E,rt=1e-6;class st{constructor(t){this._tiles=new Map,this.buffer=0,this.acquireTile=t.acquireTile,this.releaseTile=t.releaseTile,this.tileInfoView=t.tileInfoView,this.buffer=t.buffer}destroy(){}clear(){this._tiles.forEach(t=>this._releaseTile(t))}tileKeys(){const t=[];return this._tiles.forEach((i,r)=>t.push(r)),t}update(t){const i=this.tileInfoView.getTileCoverage(t.state,this.buffer,"closest"),{spans:r,lodInfo:s}=i,{level:n}=s,a=[],l=[],o=new Set,c=new Set;for(const{row:d,colFrom:f,colTo:y}of r)for(let g=f;g<=y;g++){const v=Z.getId(n,d,s.normalizeCol(g),s.getWorldForColumn(g)),w=this._getOrAcquireTile(a,v);o.add(v),w.isReady?w.visible=!0:c.add(w.key)}return c.forEach(d=>this._addPlaceholders(o,d)),this._tiles.forEach(d=>{o.has(d.key.id)||(l.push(d.key.id),this._releaseTile(d))}),be.pool.release(i),{hasMissingTiles:c.size>0,added:a,removed:l}}_getOrAcquireTile(t,i){if(!this._tiles.has(i)){const r=this.acquireTile(new Z(i));t.push(i),this._tiles.set(i,r),r.visible=!1}return this._tiles.get(i)}_getTile(t){return this._tiles.get(t)}_releaseTile(t){this._tiles.delete(t.key.id),this.releaseTile(t)}_addPlaceholders(t,i){const r=this._addPlaceholderChildren(t,i);Math.abs(1-r)<rt||this._addPlaceholderParent(t,i)||(this._getTile(i.id).visible=!0)}_addPlaceholderChildren(t,i){let r=0;return this._tiles.forEach(s=>{r+=this._addPlaceholderChild(t,s,i)}),r}_addPlaceholderChild(t,i,r){return i.key.level<=r.level||!i.hasData||!r.contains(i.key)?0:(i.visible=!0,t.add(i.key.id),1/(1<<2*(i.key.level-r.level)))}_addPlaceholderParent(t,i){let r=i.getParentKey(),s=0,n=null;for(;p(r);){if(t.has(r.id))return!0;const a=this._getTile(r.id);if(a!=null&&a.isReady){for(const l of t){const o=this._getTile(l);o&&r.contains(o.key)&&(o.visible=!1)}return a.visible=!0,t.add(a.key.id),!0}a!=null&&a.hasData&&a.patchCount>s&&(s=a.patchCount,n=a),r=r.getParentKey()}return!!n&&(n.visible=!0,t.add(n.key.id),!0)}}const pe="esri.views.layers.FeatureLayerView",B=x.getLogger(pe),at=e=>{let t=class extends e{constructor(...i){super(...i),this._updatingRequiredFieldsPromise=null,this.filter=null,this.timeExtent=null,this.layer=null,this.requiredFields=[],this.view=null}initialize(){this.handles.add([N(()=>{var r;const i=this.layer;return[(r=i==null?void 0:i.elevationInfo)==null?void 0:r.featureExpressionInfo,i&&"displayField"in i?i.displayField:null,i&&"timeInfo"in i&&i.timeInfo,i&&"renderer"in i&&i.renderer,i&&"labelingInfo"in i&&i.labelingInfo,i&&"floorInfo"in i&&i.floorInfo,this.filter,this.featureEffect,this.timeExtent]},()=>this._handleRequiredFieldsChange(),he),se(()=>{var i;return(i=this.view)==null?void 0:i.floors},"change",()=>this._handleRequiredFieldsChange()),se(()=>{const i=this.layer;return i&&"sublayers"in i?i.sublayers:null},"change",()=>this._handleRequiredFieldsChange())])}get availableFields(){const{layer:i,layer:{fieldsIndex:r},requiredFields:s}=this;return"outFields"in i&&i.outFields?X(r,[...Y(r,i.outFields),...s]):X(r,s)}get featureEffect(){return this.layer&&"featureEffect"in this.layer?this.layer.featureEffect:null}set featureEffect(i){this._override("featureEffect",i)}get maximumNumberOfFeatures(){return 0}set maximumNumberOfFeatures(i){B.error("#maximumNumberOfFeatures=","Setting maximum number of features is not supported")}get maximumNumberOfFeaturesExceeded(){return!1}highlight(i){throw new Error("missing implementation")}createQuery(){const i={outFields:["*"],returnGeometry:!0,outSpatialReference:this.view.spatialReference},r=p(this.filter)?this.filter.createQuery(i):new H(i);if(this.layer.type==="feature"){const s=ne(this);p(s)&&(r.where=r.where?`(${r.where}) AND (${s})`:s)}return p(this.timeExtent)&&(r.timeExtent=p(r.timeExtent)?r.timeExtent.intersection(this.timeExtent):this.timeExtent.clone()),r}createAggregateQuery(){const i={outFields:["*"],returnGeometry:!0,outSpatialReference:this.view.spatialReference};return new H(i)}queryFeatures(i,r){throw new Error("missing implementation")}queryObjectIds(i,r){throw new Error("missing implementation")}queryFeatureCount(i,r){throw new Error("missing implementation")}queryExtent(i,r){throw new Error("missing implementation")}async fetchPopupFeatures(i,r){const s=this.validateFetchPopupFeatures(r);if(s)throw s;return this.fetchClientPopupFeatures(r)}_loadArcadeModules(i){return i.get("expressionInfos.length")||Array.isArray(i.content)&&i.content.some(r=>r.type==="expression")?xe():Promise.resolve()}_handleRequiredFieldsChange(){const i=this._updateRequiredFields();this._set("_updatingRequiredFieldsPromise",i),i.then(()=>{this._updatingRequiredFieldsPromise===i&&this._set("_updatingRequiredFieldsPromise",null)})}async _updateRequiredFields(){if(!this.layer||!this.view)return;const i=this.view.type==="3d",{layer:r,layer:{fieldsIndex:s,objectIdField:n}}=this,a="renderer"in r&&r.renderer,l="orderBy"in r&&r.orderBy,o="featureReduction"in r?r.featureReduction:null,c=new Set,d=await W([a?a.collectRequiredFields(c,s):null,ee(c,r),i?Se(c,r):null,p(this.filter)?te(c,r,this.filter):null,p(this.featureEffect)?te(c,r,this.featureEffect.filter):null,o?Ee(c,r,o):null,l?qe(c,r,l):null]);if("timeInfo"in r&&r.timeInfo&&this.timeExtent&&L(c,r.fieldsIndex,[r.timeInfo.startField,r.timeInfo.endField]),r.type==="feature"&&(r.floorInfo&&L(c,r.fieldsIndex,[r.floorInfo.floorField]),i&&p(r.infoFor3D)&&(r.globalIdField==null&&B.error("globalIdField missing on 3DObjectFeatureLayer"),L(c,r.fieldsIndex,[r.globalIdField]))),r.type==="subtype-group"){Q(c,s,r.subtypeField);const y=r.sublayers.map(g=>{var v;return Promise.all([(v=g.renderer)==null?void 0:v.collectRequiredFields(c,s),ee(c,g)])});await W(y)}for(const y of d)y.error&&B.error(y.error);Q(c,s,n),i&&"displayField"in r&&r.displayField&&Q(c,s,r.displayField);const f=Array.from(c).sort();this._set("requiredFields",f)}validateFetchPopupFeatures(i){if(P(i))return null;for(const r of i.clientGraphics??[]){const s=r.layer;if("popupEnabled"in s&&!s.popupEnabled)return new q("featurelayerview:fetchPopupFeatures","Popups are disabled",{layer:s});if(r.isAggregate){const n="featureReduction"in s?s.featureReduction:null;if(!(n&&"popupTemplate"in n&&n.popupEnabled&&n.popupTemplate))return new q("featurelayerview:fetchPopupFeatures","Popups are disabled",{layer:s})}else if("popupTemplate"in s&&!j(s,i))return new q("featurelayerview:fetchPopupFeatures","Layer does not define a popup template",{layer:s})}}async fetchClientPopupFeatures(i){const r=p(i)?i.clientGraphics:null;if(!r||r.length===0)return[];const s=new Array(r.length),n=new Map,a=await this.createPopupQuery(i);for(let l=0;l<r.length;l++){const o=r[l];if(o.isAggregate){s[l]=o;continue}const c=o.layer;if(!("popupEnabled"in c))continue;const d=Y(this.layer.fieldsIndex,a.outFields),f=j(c,i);if(P(f))continue;const y=await this._loadArcadeModules(f);y&&y.arcadeUtils.hasGeometryOperations(f)||!Ie(d,o)?n.set(o.getObjectId(),{graphic:o,index:l}):s[l]=o}if(this.layer.type==="stream"||n.size===0)return s.filter(Boolean);a.objectIds=Array.from(n.keys());try{const l=await this.layer.queryFeatures(a);for(const o of l.features){const{graphic:{geometry:c},index:d}=n.get(o.getObjectId());o.geometry||(o.geometry=c),s[d]=o}}catch{}return s.filter(Boolean)}async createPopupQuery(i){const r=this.layer.createQuery(),s=new Set;let n=!1;const a=p(i)&&i.clientGraphics?i.clientGraphics.map(l=>l.layer):[this.layer];for(const l of a){if(!("popupEnabled"in l))continue;const o=j(l,i);if(P(o))continue;const c=await this._loadArcadeModules(o),d=c&&c.arcadeUtils.hasGeometryOperations(o);n=!(this.layer.geometryType!=="point"&&!d);const f=await Ke(this.layer,o);for(const y of f)s.add(y)}if(r.returnGeometry=n,r.returnZ=n,r.returnM=n,r.outFields=Array.from(s),r.outSpatialReference=this.view.spatialReference,this.layer.type==="feature"){const l=ne(this);p(l)&&(r.where=r.where?`(${r.where}) AND (${l})`:l)}return r}canResume(){return!!super.canResume()&&(!p(this.timeExtent)||!this.timeExtent.isEmpty)}};return u([h()],t.prototype,"_updatingRequiredFieldsPromise",void 0),u([h({readOnly:!0})],t.prototype,"availableFields",null),u([h({type:Re})],t.prototype,"featureEffect",null),u([h({type:ce})],t.prototype,"filter",void 0),u([h(Fe)],t.prototype,"timeExtent",void 0),u([h()],t.prototype,"layer",void 0),u([h({type:Number})],t.prototype,"maximumNumberOfFeatures",null),u([h({readOnly:!0,type:Boolean})],t.prototype,"maximumNumberOfFeaturesExceeded",null),u([h({readOnly:!0})],t.prototype,"requiredFields",void 0),u([h()],t.prototype,"suspended",void 0),u([h()],t.prototype,"view",void 0),t=u([R(pe)],t),t};function ue(e){return e&&"openPorts"in e}let _=class extends at(We(je(Me))){constructor(){super(...arguments),this._pipelineIsUpdating=!0,this._commandsQueue=new et({process:e=>{switch(e.type){case"processed-edit":return this._doEdit(e);case"refresh":return this._doRefresh(e.dataChanged);case"update":return this._doUpdate()}}}),this._visibilityOverrides=new Set,this._highlightIds=new Map,this._updateHighlight=me(async()=>this._proxy.setHighlight(Array.from(this._highlightIds.keys()))),this._uploadsLocked=!1,this._needsClusterSizeUpdate=!1,this.featureEffectView=new Ze,this._lastDefinitionExpression=null}destroy(){var e;Pe(this._updateClusterSizeTask,t=>t.remove()),(e=this._proxy)==null||e.destroy(),this._commandsQueue.destroy()}initialize(){this.addResolvingPromise(Promise.all([this._initProxy(),this._initServiceOptions()])),this.addHandles([this.on("valueRangesChanged",e=>{this._set("_aggregateValueRanges",e.valueRanges)}),N(()=>this.featureEffect,e=>{this.featureEffectView.featureEffect=e},he)],"constructor"),this.featureEffectView.endTransitions()}async _initProxy(){var i;const e=this.layer;if("isTable"in e&&e.isTable)throw new q("featurelayerview:table-not-supported","table feature layer can't be displayed",{layer:this.layer});if((e.type==="feature"||e.type==="subtype-group")&&!((i=z(e))!=null&&i.operations.supportsQuery))throw new q("featurelayerview:query-not-supported","layer view requires a layer with query capability",{layer:e});this._proxy&&this._proxy.destroy();const t=this._createClientOptions();return this._set("_proxy",new it({client:t})),this._proxy.when()}async _initServiceOptions(){return this._set("_serviceOptions",await this._createServiceOptions()),this._serviceOptions}get _effectiveFeatureReduction(){if(!("featureReduction"in this.layer))return null;const e=this.layer.featureReduction;return e&&(!("maxScale"in e)||!e.maxScale||e.maxScale<this.view.scale)?e:null}get orderByFields(){var e,t;return((e=this._serviceOptions)==null?void 0:e.type)!=="stream"?(t=this._serviceOptions)==null?void 0:t.orderByFields:void 0}get labelsVisible(){const e=this.layer.type==="subtype-group"?this.layer.sublayers.items:[this.layer];return!this.suspended&&e.some(t=>t.labelingInfo&&t.labelsVisible)}get queryMode(){var e;return(e=this._serviceOptions)==null?void 0:e.type}get renderingConfigHash(){var I,O;if(!this.layer)return null;const e=this.availableFields,t=this.layer,i=this.view.floors,{definitionExpression:r}=t,s=this.layer.type!=="subtype-group"&&this.layer.labelsVisible&&this.layer.labelingInfo,n="renderer"in t&&t.renderer,a="gdbVersion"in t?t.gdbVersion:void 0,l="historicMoment"in t?(I=t.historicMoment)==null?void 0:I.getTime():void 0,{timeExtent:o}=this,c="customParameters"in t?JSON.stringify(t.customParameters):void 0,d="apiKey"in t?t.apiKey:void 0,f=t.type==="stream"?`${JSON.stringify(t.geometryDefinition)}${t.definitionExpression}`:null,y=JSON.stringify(this.clips),g=(O=this._effectiveFeatureReduction)==null?void 0:O.toJSON(),v="orderBy"in this.layer&&JSON.stringify(this.layer.orderBy),w="sublayers"in this.layer&&this.layer.sublayers.items.reduce((F,$)=>F+`${$.visible?1:0}.${JSON.stringify($.renderer)}.${$.labelsVisible}
.${JSON.stringify($.labelingInfo)}`,""),V="subtypeCode"in this.layer&&this.layer.subtypeCode;return JSON.stringify({orderBy:v,sublayerHash:w,subtypeCode:V,filterHash:p(this.filter)&&this.filter.toJSON(),effectHash:p(this.featureEffect)&&this.featureEffect.toJSON(),streamFilter:f,gdbVersion:a,definitionExpression:r,historicMoment:l,availableFields:e,renderer:n,labelingInfo:s,timeExtent:o,floors:i,clipsHash:y,featureReduction:g,customParameters:c,apiKey:d})}highlight(e){let t;e instanceof G?t=[e.getObjectId()]:typeof e=="number"||typeof e=="string"?t=[e]:Oe.isCollection(e)&&e.length>0?t=e.map(r=>r==null?void 0:r.getObjectId()).toArray():Array.isArray(e)&&e.length>0&&(t=typeof e[0]=="number"||typeof e[0]=="string"?e:e.map(r=>r==null?void 0:r.getObjectId()));const i=t==null?void 0:t.filter(p);return i&&i.length?(this._addHighlight(i),{remove:()=>this._removeHighlight(i)}):{remove:()=>{}}}hasHighlight(){return!!this._highlightIds.size}async hitTest(e,t){if(!this.tileRenderer)return null;const i=await this.tileRenderer.hitTest(t);if(i.length===0)return null;const{features:r,aggregates:s}=await this._proxy.getFeatures(i);return[...s.map(n=>this._createGraphicHit(e,K.fromJSON(n))),...r.map(n=>this._createGraphicHit(e,G.fromJSON(n)))]}queryStatistics(){return this._proxy.queryStatistics()}async querySummaryStatistics(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.querySummaryStatistics(this._cleanUpQuery(e),r,i)}async queryAggregateSummaryStatistics(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryAggregateSummaryStatistics(this._cleanUpAggregateQuery(e),r,i)}async queryUniqueValues(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryUniqueValues(this._cleanUpQuery(e),r,i)}async queryAggregateUniqueValues(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryAggregateUniqueValues(this._cleanUpAggregateQuery(e),r,i)}async queryClassBreaks(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryClassBreaks(this._cleanUpQuery(e),r,i)}async queryAggregateClassBreaks(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryAggregateClassBreaks(this._cleanUpAggregateQuery(e),r,i)}async queryHistogram(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryHistogram(this._cleanUpQuery(e),r,i)}async queryAggregateHistogram(e,t,i){const r={...t,scale:this.view.scale};return this._proxy.queryAggregateHistogram(this._cleanUpAggregateQuery(e),r,i)}queryFeatures(e,t){return this.queryFeaturesJSON(e,t).then(i=>{const r=D.fromJSON(i);return r.features.forEach(s=>this._setLayersForFeature(s)),r})}queryVisibleFeatures(e,t){return this._proxy.queryVisibleFeatures(this._cleanUpQuery(e),t).then(i=>{const r=D.fromJSON(i);return r.features.forEach(s=>this._setLayersForFeature(s)),r})}async queryAggregates(e,t){const i=await this._proxy.queryAggregates(this._cleanUpAggregateQuery(e),t),r=Xe.fromJSON(i);return r.features.forEach(s=>this._setLayersForFeature(s)),r}queryAggregateIds(e,t){return this._proxy.queryAggregateIds(this._cleanUpAggregateQuery(e),t)}queryAggregateCount(e,t){return this._proxy.queryAggregateCount(this._cleanUpAggregateQuery(e),t)}queryAggregateJSON(e,t){return this._proxy.queryAggregates(this._cleanUpAggregateQuery(e),t)}queryFeaturesJSON(e,t){return this._proxy.queryFeatures(this._cleanUpQuery(e),t)}queryObjectIds(e,t){return this._proxy.queryObjectIds(this._cleanUpQuery(e),t)}queryFeatureCount(e,t){return this._proxy.queryFeatureCount(this._cleanUpQuery(e),t)}queryExtent(e,t){return this._proxy.queryExtent(this._cleanUpQuery(e),t).then(i=>({count:i.count,extent:Ce.fromJSON(i.extent)}))}setVisibility(e,t){t?this._visibilityOverrides.delete(e):this._visibilityOverrides.add(e),this._update()}update(e){if(!this._tileStrategy||!this.tileRenderer)return;const{hasMissingTiles:t,added:i,removed:r}=this._tileStrategy.update(e);(i.length||r.length)&&this._proxy.updateTiles({added:i,removed:r}),t&&this.requestUpdate(),this.notifyChange("updating")}attach(){this.view.timeline.record(`${this.layer.title} (FeatureLayer) Attach`),this._tileStrategy=new st({acquireTile:e=>this._acquireTile(e),releaseTile:e=>this._releaseTile(e),tileInfoView:this.view.featuresTilingScheme,buffer:0}),this.addAttachHandles(N(()=>this.renderingConfigHash,()=>this._update(),Le)),this.layer.type!=="stream"&&this.addAttachHandles(this.layer.on("edits",e=>this._edit(e)))}detach(){var e,t;this._commandsQueue.clear(),(e=this._proxy)==null||e.stop(),this.container.removeAllChildren(),(t=this.tileRenderer)==null||t.uninstall(this.container),this.tileRenderer=null,this._tileStrategy=re(this._tileStrategy),this._tileRendererHash=null}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}isUpdating(){var l;const e="renderer"in this.layer&&this.layer.renderer!=null,t=this._commandsQueue.updating,i=this._updatingRequiredFieldsPromise!=null,r=!this._proxy||!this._proxy.isReady,s=this._pipelineIsUpdating,n=this.tileRenderer==null||((l=this.tileRenderer)==null?void 0:l.updating),a=e&&(t||i||r||s||n);return S("esri-2d-log-updating")&&console.log(`Updating FLV2D: ${a}
  -> hasRenderer ${e}
  -> hasPendingCommand ${t}
  -> updatingRequiredFields ${i}
  -> updatingProxy ${r}
  -> updatingPipeline ${s}
  -> updatingTileRenderer ${n}
`),a}_createClientOptions(){return{setUpdating:e=>{this._set("_pipelineIsUpdating",e)},emitEvent:e=>{this.emit(e.name,e.event)}}}async _detectQueryMode(e){var l;const t="path"in e,{layer:i}=this,r="editingInfo"in i&&((l=i.editingInfo)==null?void 0:l.lastEditDate),s="refreshInterval"in i&&!!i.refreshInterval,n=!r&&s,a=z(i);if(t&&(i.type==="feature"||i.type==="subtype-group")&&i.geometryType==="point"&&(a!=null&&a.query.supportsPagination)&&!(a!=null&&a.operations.supportsEditing)&&!n&&S("featurelayer-snapshot-enabled"))try{const o=await i.queryFeatureCount();if(o<=S("featurelayer-snapshot-point-min-threshold"))return{mode:"snapshot",featureCount:o};const c=S("featurelayer-snapshot-point-max-threshold"),d=S("featurelayer-snapshot-point-coverage"),f=this.view.extent,y=U(i.fullExtent),g=y==null?void 0:y.clone().intersection(f),v=p(g)?g.width*g.height:0,w=(y==null?void 0:y.width)*(y==null?void 0:y.height);if(o<=c&&(w===0?0:v/w)>=d)return{mode:"snapshot",featureCount:o}}catch(o){x.getLogger(this.declaredClass).warn("mapview-feature-layer","Encountered an error when querying for featureCount",{error:o})}return{mode:"on-demand"}}async _createServiceOptions(){var V,I,O;const e=this.layer;if(e.type==="stream")return null;const t=z(e),{capabilities:i,objectIdField:r}=e,s=e.fields.map(F=>F.toJSON()),n=p(e.fullExtent)?e.fullExtent.toJSON():null,a=De(e.geometryType),l="timeInfo"in e&&e.timeInfo&&e.timeInfo.toJSON()||null,o=e.spatialReference?e.spatialReference.toJSON():null,c=e.type==="feature"?e.globalIdField:null;let d;e.type==="ogc-feature"?d=e.source.getSource():ue(e.source)?d=await e.source.openPorts():e.parsedUrl&&(d=Ne(e.parsedUrl),"dynamicDataSource"in e&&e.dynamicDataSource&&(d.query={layer:JSON.stringify({source:e.dynamicDataSource})}));const f="datesInUnknownTimezone"in this.layer&&this.layer.datesInUnknownTimezone,y=("subtypeField"in this.layer?this.layer.subtypeField:null)??null,{mode:g,featureCount:v}=await this._detectQueryMode(d);let w=this.layer.objectIdField;if(this.layer.type==="feature"&&p(this.layer.orderBy)&&this.layer.orderBy.length){const F=!this.layer.orderBy[0].valueExpression&&this.layer.orderBy[0].field;F&&(w=F)}return{type:g,timeReferenceUnknownClient:f,subtypeField:y,featureCount:v,globalIdField:c,maxRecordCount:i.query.maxRecordCount,tileMaxRecordCount:i.query.tileMaxRecordCount,capabilities:i,effectiveCapabilities:t,fields:s,fullExtent:n,geometryType:a,objectIdField:r,source:d,timeInfo:l,spatialReference:o,orderByFields:w,datesInUnknownTimezone:f,dateFieldsTimeReference:("dateFieldsTimeReference"in this.layer?(V=this.layer.dateFieldsTimeReference)==null?void 0:V.toJSON():null)||null,preferredTimeReference:("preferredTimeReference"in this.layer?(I=this.layer.preferredTimeReference)==null?void 0:I.toJSON():null)||null,editFieldsInfo:"editFieldsInfo"in this.layer?(O=this.layer.editFieldsInfo)==null?void 0:O.toJSON():null}}async _createMemoryServiceOptions(e){const t=await e.openPorts();return{...this._createServiceOptions(),type:"memory",source:t}}_cleanUpQuery(e){const t=H.from(e)||this.createQuery();return t.outSpatialReference||(t.outSpatialReference=this.view.spatialReference),t}_cleanUpAggregateQuery(e){const t=H.from(e)||this.createAggregateQuery();return t.outSpatialReference||(t.outSpatialReference=this.view.spatialReference),t}async _update(){return this._commandsQueue.push({type:"update"})}async _edit(e){return this.suspended?void this._clearTiles():this._validateEdit(e)?this._commandsQueue.push({type:"edit",edits:e}):void 0}async doRefresh(e){if(this.attached&&this._tileStrategy.tileKeys().length)return this.suspended&&e?void this._clearTiles():this._commandsQueue.push({type:"refresh",dataChanged:e})}_clearTiles(){this._tileStrategy.tileKeys().length&&(this._proxy.updateTiles({added:[],removed:this._tileStrategy.tileKeys()}),this._tileStrategy.clear(),this.requestUpdate(),this._commandsQueue.clear(),this._update())}_validateEdit(e){const t="globalIdField"in this.layer&&this.layer.globalIdField,i=e.deletedFeatures.some(s=>s.objectId===-1||!s.objectId),r=t&&this.availableFields.includes(t);return i&&!r?(x.getLogger(this.declaredClass).error(new q("mapview-apply-edits",`Editing the specified service requires the layer's globalIdField, ${t} to be included the layer's outFields for updates to be reflected on the map`)),null):e}async _doUpdate(){var e,t;try{if(this.destroyed||!this._hasRequiredSupport(this.layer)||!this._tileStrategy)return;const{featureEffectView:i,filter:r}=this;if(await this._updateRequiredFields(),this.destroyed)return;const{renderer:s}=this._getEffectiveRenderer();this._set("_effectiveRenderer",s);const n=this._createSchemaConfig(),a=this._createConfiguration(n,r,i.filter),l=this._lastDefinitionExpression!==a.schema.source.definitionExpression;this._lastDefinitionExpression=a.schema.source.definitionExpression;const o=a.schema.tileRenderer,c=this._createTileRendererHash(o);if(this._serviceOptions.type==="snapshot"&&(a.schema.source.initialFeatureCount=this._serviceOptions.featureCount),c!==this._tileRendererHash){await this._initTileRenderer(o,s);const d=this.layer,f=d.type==="stream"?await this._initServiceOptions():this._serviceOptions;this.tileRenderer.onConfigUpdate(s),d.type!=="stream"&&ue(d.source)&&(f.source=await d.source.openPorts());const y={added:this._tileStrategy.tileKeys(),removed:[]};await this._proxy.startup(this.view.featuresTilingScheme,a,f,y),this.hasHighlight()&&await this._proxy.setHighlight(Array.from(this._highlightIds.keys())),await this._onceTilesUpdated(),this.tileRenderer.onConfigUpdate(s)}else{this._serviceOptions.type==="snapshot"&&l&&(a.schema.source.changedFeatureCount=await this.layer.queryFeatureCount());const d=await this._proxy.update(a);(d.mesh||(e=d.targets)!=null&&e.aggregate)&&this._lockGPUUploads();try{await this._proxy.applyUpdate(d)}catch(f){C(f)||x.getLogger(this.declaredClass).error(f)}(d.mesh||(t=d.targets)!=null&&t.aggregate)&&this._unlockGPUUploads(),this.tileRenderer.onConfigUpdate(s),this._updateClusterSizeVariable(),this._forceAttributeTextureUpload()}this._tileRendererHash=c,this.tileRenderer.invalidateLabels(),this.requestUpdate()}catch{}}async _doEdit(e){try{await this._proxy.onEdits(e)}catch(t){C(t)}}async _doRefresh(e){this._lockGPUUploads();try{let t;e&&this.queryMode==="snapshot"&&"queryFeatureCount"in this.layer&&(t=await this.layer.queryFeatureCount()),await this._proxy.refresh({dataChanged:e,featureCount:t})}catch(t){C(t)}this._unlockGPUUploads(),this._effectiveFeatureReduction&&this._updateClusterSizeVariable()}_updateClusterSizeVariable(){this._needsClusterSizeUpdate&&(this.tileRenderer.onConfigUpdate(this._effectiveRenderer),this._needsClusterSizeUpdate=!1)}_createUpdateClusterSizeTask(e,t){return N(()=>this._aggregateValueRanges,i=>{this._updateClusterEffectiveRendererSizeVariable(e,t,i),this._needsClusterSizeUpdate=!0,this._uploadsLocked||this._updateClusterSizeVariable()})}async _updateClusterEffectiveRendererSizeVariable(e,t,i){if(e.dynamicClusterSize&&"visualVariables"in e&&e.visualVariables){const r=Te(e.visualVariables);if(p(r)&&r.field==="cluster_count"){const s=e.visualVariables.indexOf(r);e.visualVariables[s]=Ue(t,i);const n=e.clone();n.dynamicClusterSize=!0,this._set("_effectiveRenderer",n)}}}_getEffectiveRenderer(){var r;const e=this.layer,t="renderer"in e?e.renderer:null,i=this._effectiveFeatureReduction;if(this._updateClusterSizeTask=Je(this._updateClusterSizeTask),i&&"renderer"in i&&i.renderer&&!((r=i.renderer.authoringInfo)!=null&&r.isAutoGenerated)){const s=i.fields;if(i.type==="cluster"){const{renderer:n,didInject:a}=Ae(i.renderer,i,this._aggregateValueRanges);return a&&(this._updateClusterSizeTask=this._createUpdateClusterSizeTask(n,i)),{renderer:n,aggregateFields:s,featureReduction:i}}return{renderer:i.renderer,aggregateFields:s,featureReduction:i}}if(i&&i.type==="cluster"&&t&&ke(t)){const s=i,n=[],a=Ve(n,t,s,this._aggregateValueRanges,!0);return this._updateClusterSizeTask=this._createUpdateClusterSizeTask(a,s),{renderer:a,aggregateFields:n,featureReduction:i}}return{renderer:t,aggregateFields:[],featureReduction:null}}_acquireTile(e){const t=this.tileRenderer.acquireTile(e);return t.once("attach",()=>{this.requestUpdate()}),t}_releaseTile(e){this.tileRenderer.releaseTile(e)}async _initTileRenderer(e,t){const i=await Ye(e,{layerView:this,tileInfoView:this.view.featuresTilingScheme,layer:this.layer});return this.tileRenderer&&(this._tileStrategy.clear(),this.tileRenderer.uninstall(this.container),this.tileRenderer=re(this.tileRenderer)),this.destroyed?null:(this._proxy.tileRenderer=i,this._set("tileRenderer",i),this.tileRenderer.install(this.container),this.tileRenderer.onConfigUpdate(t),this.requestUpdate(),this.tileRenderer)}_createTileRendererHash(e){return`${e.type}`}get hasFilter(){const e=!!("floorInfo"in this.layer&&this.layer.floorInfo&&this.view.floors&&this.view.floors.length);return!!this.filter||e||!!this._visibilityOverrides.size||!!this.timeExtent}_injectOverrides(e){const t=p(e)?e.timeExtent:null,i=p(this.timeExtent)&&p(t)?this.timeExtent.intersection(t):this.timeExtent||t;let r=null;const s="floorInfo"in this.layer&&this.layer.floorInfo;if(s){const a=p(e)?e.where:null;r=this._addFloorFilterClause(a)}if(!this._visibilityOverrides.size&&!i&&!s)return p(e)?e.toJSON():null;(e=p(e)&&e.clone()||new ce).timeExtent=i,r&&(e.where=r);const n=e.toJSON();return n.hiddenIds=Array.from(this._visibilityOverrides),n}_addFloorFilterClause(e){var r;const t=this.layer;let i=e||"";if("floorInfo"in t&&t.floorInfo){let s=this.view.floors;if(!s||!s.length)return i;(r=t.floorInfo.viewAllLevelIds)!=null&&r.length&&(s=t.floorInfo.viewAllLevelIds);const n=s.filter(o=>o!=="").map(o=>"'"+o+"'");n.push("''");const a=t.floorInfo.floorField;let l="("+a+" IN ({ids}) OR "+a+" IS NULL)";if(l=l.replace("{ids}",n.join(", ")),p(i)&&i.includes(a)){let o=new RegExp("AND \\("+a+".*NULL\\)","g");i=i.replace(o,""),o=new RegExp("\\("+a+".*NULL\\)","g"),i=i.replace(o,""),i=i.replace(/\s+/g," ").trim()}i=i!==""?"("+i+") AND "+l:l}return i!==""?i:null}_createConfiguration(e,t,i){const r=this.layer.type==="feature"&&this.layer.historicMoment?this.layer.historicMoment.getTime():void 0,s=this.layer.type==="feature"?this.layer.gdbVersion??void 0:void 0,n=new Array(ze),a=this._injectOverrides(t);n[0]=a,n[1]=p(i)?i.toJSON():null;const l=Be(e);if(P(l))return null;const o=$e("2d");return{availableFields:this.availableFields,gdbVersion:s,historicMoment:r,devicePixelRatio:window.devicePixelRatio||1,filters:n,schema:l,supportsTextureFloat:o.supportsTextureFloat,maxTextureSize:o.maxTextureSize}}_hasRequiredSupport(e){return!("renderer"in e)||Ge(e.renderer)}_onceTilesUpdated(){return this.requestUpdate(),Qe(()=>!this._pipelineIsUpdating)}_lockGPUUploads(){this.tileRenderer&&(this._uploadsLocked=!0,this.tileRenderer.lockGPUUploads())}_unlockGPUUploads(){this.tileRenderer&&(this._uploadsLocked=!1,this.tileRenderer.unlockGPUUploads())}_forceAttributeTextureUpload(){this.tileRenderer&&this.tileRenderer.forceAttributeTextureUpload()}_createSchemaConfig(){const e=this.layer;return{renderer:"renderer"in e?e.renderer:null,spatialReference:e.spatialReference,timeExtent:"timeExtent"in e?e.timeExtent:null,definitionExpression:e.definitionExpression,featureReduction:this._effectiveFeatureReduction,fields:e.fields,geometryType:e.geometryType,historicMoment:"historicMoment"in e?e.historicMoment:null,labelsVisible:"labelsVisible"in e&&e.labelsVisible,labelingInfo:"labelingInfo"in e?e.labelingInfo:null,availableFields:this.availableFields,featureEffect:this.featureEffect,filter:this.filter,gdbVersion:"gdbVersion"in e?e.gdbVersion:null,pixelBuffer:0,orderBy:"orderBy"in e&&e.orderBy?e.orderBy:null,customParameters:{..."customParameters"in e?e.customParameters:void 0,token:"apiKey"in e?e.apiKey??void 0:void 0},subtypeCode:"subtypeCode"in e?e.subtypeCode:void 0,subtypeField:"subtypeField"in e?e.subtypeField:void 0}}_addHighlight(e){for(const t of e)if(this._highlightIds.has(t)){const i=this._highlightIds.get(t);this._highlightIds.set(t,i+1)}else this._highlightIds.set(t,1);this._updateHighlight().catch(t=>{C(t)||x.getLogger(this.declaredClass).error(t)})}_removeHighlight(e){for(const t of e)if(this._highlightIds.has(t)){const i=this._highlightIds.get(t)-1;i===0?this._highlightIds.delete(t):this._highlightIds.set(t,i)}this._updateHighlight().catch(t=>{C(t)||x.getLogger(this.declaredClass).error(t)})}_setLayersForFeature(e){const t=this.layer;e.layer=t,e.sourceLayer=t}_createGraphicHit(e,t){return this._setLayersForFeature(t),p(t.geometry)&&(t.geometry.spatialReference=this.view.spatialReference),{type:"graphic",graphic:t,layer:this.layer,mapPoint:e}}};u([h()],_.prototype,"_serviceOptions",void 0),u([h()],_.prototype,"_proxy",void 0),u([h()],_.prototype,"_pipelineIsUpdating",void 0),u([h()],_.prototype,"_effectiveRenderer",void 0),u([h()],_.prototype,"_effectiveFeatureReduction",null),u([h()],_.prototype,"_aggregateValueRanges",void 0),u([h()],_.prototype,"_commandsQueue",void 0),u([h()],_.prototype,"featureEffectView",void 0),u([h()],_.prototype,"labelsVisible",null),u([h({readOnly:!0})],_.prototype,"queryMode",null),u([h()],_.prototype,"renderingConfigHash",null),u([h()],_.prototype,"tileRenderer",void 0),u([h()],_.prototype,"updating",void 0),_=u([R("esri.views.2d.layers.FeatureLayerView2D")],_);const Lt=_;export{Lt as default};
