package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataCorrectRecords;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData;
import org.thingsboard.server.dao.sql.smartPipe.PipeCopyDataCorrectRecordsMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipeCopyDataReadMeterDataMapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class PipeCopyDataReadMeterDataServiceImpl implements PipeCopyDataReadMeterDataService {

    @Autowired
    private PipeCopyDataReadMeterDataMapper pipeCopyDataReadMeterDataMapper;

    @Autowired
    private PipeCopyDataCorrectRecordsMapper pipeCopyDataCorrectRecordsMapper;


    @Override
    public PageData<PipeCopyDataReadMeterData> getList(PartitionCustRequest partitionCustRequest) {
        Page<PipeCopyDataReadMeterData> custPage = new Page<>(partitionCustRequest.getPage(), partitionCustRequest.getSize());
        IPage<PipeCopyDataReadMeterData> pipePartitionCustIPage = pipeCopyDataReadMeterDataMapper.getList(custPage, partitionCustRequest);
        return new PageData<>(pipePartitionCustIPage.getTotal(), pipePartitionCustIPage.getRecords());
    }

    @Override
    public PipeCopyDataReadMeterData correct(PipeCopyDataReadMeterData pipeCopyDataReadMeterData, String userId) {
        BigDecimal correctWater = pipeCopyDataReadMeterData.getCorrectWater();
        pipeCopyDataReadMeterData = pipeCopyDataReadMeterDataMapper.selectById(pipeCopyDataReadMeterData.getId());
        if (pipeCopyDataReadMeterData == null) {
            return null;
        }
        pipeCopyDataReadMeterData.setCorrectWater(correctWater);
        // 添加修改记录
        PipeCopyDataCorrectRecords pipeCopyDataCorrectRecords = new PipeCopyDataCorrectRecords();

        pipeCopyDataCorrectRecords.setCopyId(pipeCopyDataReadMeterData.getId());
        pipeCopyDataCorrectRecords.setType("1");
        pipeCopyDataCorrectRecords.setCorrectWater(correctWater);
        pipeCopyDataCorrectRecords.setOldValue(pipeCopyDataReadMeterData.getOriginWater());
        pipeCopyDataCorrectRecords.setNewValue(pipeCopyDataReadMeterData.getOriginWater().add(correctWater));

        pipeCopyDataCorrectRecords.setCreator(userId);
        pipeCopyDataCorrectRecords.setCreateTime(new Date());
        pipeCopyDataCorrectRecords.setTenantId(pipeCopyDataReadMeterData.getTenantId());
        pipeCopyDataCorrectRecordsMapper.insert(pipeCopyDataCorrectRecords);

        // 抄表记录修改
        pipeCopyDataReadMeterData.setTotalWater(pipeCopyDataCorrectRecords.getNewValue());
        pipeCopyDataReadMeterData.setUpdateUser(userId);
        pipeCopyDataReadMeterDataMapper.updateById(pipeCopyDataReadMeterData);
        return pipeCopyDataReadMeterData;
    }

    @Override
    public PageData<PipeCopyDataCorrectRecords> getCorrectRecords(PartitionCustRequest partitionCustRequest) {
        Page<PipeCopyDataCorrectRecords> custPage = new Page<>(partitionCustRequest.getPage(), partitionCustRequest.getSize());
        IPage<PipeCopyDataCorrectRecords> pipePartitionCustIPage = pipeCopyDataCorrectRecordsMapper.getCopyCorrectRecords(custPage, partitionCustRequest);
        return new PageData<>(pipePartitionCustIPage.getTotal(), pipePartitionCustIPage.getRecords());
    }

    @Override
    public void getUseAndCorrectWater(List<String> partitionIdList, Long startTime, Long endTime, Map<String, BigDecimal> copyWater, Map<String, BigDecimal> correctCopyWater) {
        List<JSONObject> pipeCopyDataReadMeterDataList = pipeCopyDataReadMeterDataMapper.sumCorrectByPartitionId(partitionIdList, startTime, endTime);
        for (JSONObject object : pipeCopyDataReadMeterDataList) {
            copyWater.put(object.getString("id"), object.getBigDecimal("total"));
            correctCopyWater.put(object.getString("id"), object.getBigDecimal("correctWater"));
        }
    }
}