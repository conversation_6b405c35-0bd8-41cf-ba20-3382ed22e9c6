package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.TimeUtils;

import java.util.Date;

@Getter
@Setter
public class WorkOrderCompleteCountRequest extends MutableWorkOrderCountOfStatusRequest {
    @Override
    public Date getFromTimeOrDefault(Object fromTime) {
        return TimeUtils.computeDefaultIfNull(fromTime, TimeUtils::firstDayOfMonthByNow);
    }

    @Override
    public Date getToTimeOrDefault(Object toTime) {
        return TimeUtils.computeDefaultIfNull(toTime, TimeUtils::lastDayOfMonthByNow);
    }

    @Override
    public WorkOrderCompleteCountRequest changeTimeRange(Date from, Date to) {
        return (WorkOrderCompleteCountRequest) super.changeTimeRange(from, to);
    }
}
