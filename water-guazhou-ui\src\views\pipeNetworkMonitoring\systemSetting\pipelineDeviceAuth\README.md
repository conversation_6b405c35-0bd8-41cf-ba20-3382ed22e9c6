# 管网设备权限管理

## 功能概述

管网设备权限管理模块用于管理管网设备（压力计、流量计、水质检测仪）的用户访问权限，支持按用户分组进行权限分配和管理。

## 主要功能

### 1. 设备类型筛选
- 压力计：用于监测管网压力
- 流量计：用于监测管网流量
- 水质检测仪：用于监测水质参数

### 2. 用户分组管理
- 系统管理员组：拥有所有设备的完全控制权限
- 设备操作员组：可以进行设备操作和参数设置
- 数据查看员组：只能查看设备数据

### 3. 权限类型
1. **完全控制**：可以进行所有操作
2. **只读访问**：只能查看设备信息和数据
3. **数据下载**：可以下载设备历史数据
4. **参数设置**：可以修改设备参数配置

### 4. 批量操作
- 批量绑定用户：可以同时为多个设备绑定相同的用户权限
- 批量移除权限：可以同时移除多个设备的所有用户权限

## 使用方法

### 添加设备权限
1. 在设备列表中选择要配置权限的设备
2. 点击"用户绑定"按钮
3. 选择要绑定的用户
4. 选择权限类型
5. 点击"确定"保存

### 管理现有权限
1. 点击设备行的展开按钮查看当前权限列表
2. 点击"权限管理"按钮进入详细管理页面
3. 可以添加新用户、修改权限类型或移除用户权限

### 批量操作
1. 使用复选框选择多个设备
2. 点击"批量绑定用户"进行批量权限分配
3. 点击"批量移除权限"清除选中设备的所有权限

## 后端接口要求

### 必需的API接口

#### 1. 获取管网设备列表
```
GET /api/pipelineDevice/list
参数：page, size, deviceType, deviceName, deviceCode, location, status
```

#### 2. 获取设备权限列表
```
GET /api/pipelineDeviceAuth/device/{deviceId}
```

#### 3. 批量保存设备权限
```
POST /api/pipelineDeviceAuth/batch
Body: { deviceId, deviceName, deviceSerial, userAuthList }
```

#### 4. 获取用户分组树
```
GET /api/userGroup/tree
```

#### 5. 获取用户列表
```
GET /api/user/getAll
```

### 数据库表结构建议

#### 管网设备表 (pipeline_device)
- id: 设备ID
- name: 设备名称
- code: 设备编号
- type: 设备类型（pressure/flow/quality）
- location: 安装位置
- status: 设备状态
- install_date: 安装日期
- manufacturer: 厂商
- model: 型号

#### 设备权限表 (pipeline_device_auth)
- id: 权限ID
- device_id: 设备ID
- user_id: 用户ID
- user_name: 用户名称
- user_group_id: 用户组ID
- user_group_name: 用户组名称
- auth_type: 权限类型（1-4）
- create_time: 创建时间
- update_time: 更新时间

## 权限验证

在使用设备相关功能时，可以通过以下API检查用户权限：

```typescript
import { checkPipelineDeviceAuth } from '@/api/systemConfiguration/pipelineDeviceAuth';

// 检查用户是否有设备权限
const hasAuth = await checkPipelineDeviceAuth(deviceId, userId);
```

## 注意事项

1. **权限继承**：用户组权限会影响组内所有用户的设备访问权限
2. **权限优先级**：个人权限优先于组权限
3. **安全性**：敏感操作（如参数设置）需要进行二次确认
4. **审计日志**：所有权限变更操作都应记录操作日志

## 扩展功能

### 1. 权限模板
可以创建权限模板，快速应用到多个设备：
```typescript
// 创建权限模板
const template = {
  name: '运维人员模板',
  authType: AuthType.PARAM_SETTING,
  userGroups: ['group1', 'group2']
};
```

### 2. 定时权限
支持设置临时权限，到期自动回收：
```typescript
// 设置临时权限
const tempAuth = {
  userId: 'user1',
  deviceId: 'device1',
  authType: AuthType.READ_ONLY,
  expireTime: '2024-12-31 23:59:59'
};
```

### 3. 权限报表
支持导出设备权限分配报表，便于权限审计和管理。

## 故障排除

### 常见问题
1. **设备列表不显示**：检查API接口是否正常，确认设备数据格式
2. **权限保存失败**：检查用户权限和网络连接
3. **用户列表为空**：确认用户管理模块是否正常工作

### 调试信息
- 开启浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 确认后端接口返回数据格式 