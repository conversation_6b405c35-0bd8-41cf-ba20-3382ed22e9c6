import{d as ue,M as pe,u as ve,j as me,a8 as B,c as d,r as V,am as ge,o as fe,g as c,n as h,p as u,q as a,F as o,aB as M,aJ as N,h as z,G as p,bo as _e,av as ye,bh as be,i as F,ak as he,y as Ce,aK as Te,aL as we,I as Se,J as ke,K as Ue,N as xe,cU as Ve,O as ze,P as Ee,cE as De,bK as Ie,dN as Be,L as Me,br as Ne,C as Fe}from"./index-r0dFAfgr.js";import{c as Le}from"./equipmentManage-DuoY00aj.js";import{g as je,d as qe,s as Oe}from"./index-DTYSQc9n.js";import{f as Pe}from"./DateFormatter-Bm9a68Ax.js";const Ae={class:"app-container"},Ge={class:"device-icon-container"},$e={class:"device-list-container"},Ye={class:"action-bar"},Je={style:{display:"flex","justify-content":"center",gap:"10px"}},Ke={class:"pagination-container"},He=["src"],Qe={key:1,class:"avatar-uploader-icon"},We={class:"dialog-footer"},Xe=ue({__name:"index",setup(Re){const{$messageSuccess:E,$messageError:f,$messageWarning:Ze}=pe(),L=ve(),j=me(),q=B(()=>j.actionUrl+"file/api/upload/image"),O=B(()=>L.token),T=d([]),b=d(null),w=d(!1),S=d(!1),P=d([]),C=d(0),k=d([]),_=d(!1),U=d(""),A=d(1),s=V({page:1,size:20,deviceType:void 0,deviceStatus:void 0,fromTime:void 0,toTime:void 0}),i=V({id:void 0,deviceType:void 0,iconUrl:void 0,deviceStatus:"正常",statusColor:"#67C23A"}),G=V({deviceType:[{required:!0,message:"设备类型不能为空",trigger:"change"}],iconUrl:[{required:!0,message:"设备图标不能为空",trigger:"change"}],deviceStatus:[{required:!0,message:"设备状态不能为空",trigger:"change"}],statusColor:[{required:!0,message:"状态颜色不能为空",trigger:"change"}]}),$=async()=>{try{const t=await Le();if(t.data&&t.data.data){const e=[],n=y=>{y.forEach(r=>{r.level==="3"&&e.push({label:r.name,value:r.name}),r.children&&r.children.length>0&&n(r.children)})};n(t.data.data),T.value=e}}catch(t){console.error("获取设备类型选项失败",t),f("获取设备类型选项失败")}},v=async()=>{w.value=!0;try{const t=await je({page:s.page,size:s.size,deviceType:s.deviceType,deviceStatus:s.deviceStatus,fromTime:s.fromTime,toTime:s.toTime});t.data&&t.data.data?(k.value=t.data.data.records||[],C.value=t.data.data.total||0):(k.value=[],C.value=0)}catch(t){console.error("获取设备图标列表失败",t),f("获取设备图标列表失败")}finally{w.value=!1}},D=()=>{s.page=1,v()},Y=()=>{s.page=1,s.size=20,s.deviceType=void 0,s.deviceStatus=void 0,s.fromTime=void 0,s.toTime=void 0,D()},J=t=>{P.value=t.map(e=>e.id)},K=()=>{x(),_.value=!0,U.value="添加设备图标"},H=t=>{x(),Object.assign(i,t),_.value=!0,U.value="修改设备图标"},Q=t=>{Ce.confirm("是否确认删除该设备图标?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await qe(t.id),E("删除成功"),v()}catch(e){console.error("删除设备图标失败",e),f("删除设备图标失败")}}).catch(()=>{})},x=()=>{Object.assign(i,{id:void 0,deviceType:void 0,iconUrl:void 0,deviceStatus:"正常",statusColor:"#67C23A"}),b.value&&b.value.resetFields()},W=()=>{_.value=!1,x()},X=t=>{const e=t.type==="image/jpeg"||t.type==="image/png"||t.type==="image/gif"||t.type==="image/x-icon",n=t.size/1024/1024<10;return e||f("上传图标只能是JPG/PNG/GIF/ICO格式!"),n||f("上传图标大小不能超过10MB!"),e&&n},R=(t,e)=>{i.iconUrl=t.data||t},Z=async()=>{b.value&&await b.value.validate(async t=>{if(t){S.value=!0;try{const e={deviceType:i.deviceType,iconUrl:i.iconUrl,deviceStatus:i.deviceStatus,statusColor:i.statusColor};i.id&&(e.id=i.id),await Oe(e),E(i.id?"修改成功":"新增成功"),_.value=!1,v()}catch(e){console.error("保存设备图标失败",e),f("保存设备图标失败")}finally{S.value=!1}}})},ee=t=>{s.page=t,v()},ae=t=>{s.size=t,s.page=1,v()};return ge(A,t=>{if(t&&!isNaN(Number(t))){const e=parseInt(t.toString());e>0&&e<=Math.ceil(C.value/s.size)&&(s.page=e,v())}}),fe(()=>{$(),v()}),(t,e)=>{const n=Te,y=we,r=Se,m=ke,I=Ue,g=xe,te=Ve,le=ze,oe=Ee,se=De,ie=Ie,ne=Be,re=Me,de=Ne;return c(),h("div",Ae,[u("div",Ge,[u("div",$e,[a(I,{model:s,ref:"queryForm",inline:!0,class:"search-form"},{default:o(()=>[a(r,{label:"设备类型："},{default:o(()=>[a(y,{modelValue:s.deviceType,"onUpdate:modelValue":e[0]||(e[0]=l=>s.deviceType=l),placeholder:"请选择",clearable:"",style:{width:"180px"}},{default:o(()=>[(c(!0),h(M,null,N(T.value,l=>(c(),z(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"设备状态："},{default:o(()=>[a(y,{modelValue:s.deviceStatus,"onUpdate:modelValue":e[1]||(e[1]=l=>s.deviceStatus=l),placeholder:"请选择",clearable:"",style:{width:"180px"}},{default:o(()=>[a(n,{label:"正常",value:"正常"}),a(n,{label:"离线",value:"离线"}),a(n,{label:"故障",value:"故障"}),a(n,{label:"维修中",value:"维修中"})]),_:1},8,["modelValue"])]),_:1}),a(r,null,{default:o(()=>[a(m,{type:"primary",onClick:D},{default:o(()=>e[6]||(e[6]=[p("查询")])),_:1}),a(m,{onClick:Y},{default:o(()=>e[7]||(e[7]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"]),u("div",Ye,[a(m,{type:"primary",size:"default",onClick:K},{default:o(()=>e[8]||(e[8]=[p("新增")])),_:1})]),_e((c(),z(le,{data:k.value,border:"",onSelectionChange:J},{default:o(()=>[a(g,{type:"selection",width:"50"}),a(g,{label:"设备类型","min-width":"120",align:"center",prop:"deviceType"}),a(g,{label:"设备图标","min-width":"120",align:"center"},{default:o(l=>[a(te,{style:{width:"40px",height:"40px"},src:l.row.iconUrl,fit:"contain","preview-src-list":[l.row.iconUrl]},null,8,["src","preview-src-list"])]),_:1}),a(g,{label:"设备状态","min-width":"120",align:"center",prop:"deviceStatus"}),a(g,{label:"状态颜色","min-width":"120",align:"center"},{default:o(l=>[u("div",{class:"color-box",style:ye({backgroundColor:l.row.statusColor})},null,4)]),_:1}),a(g,{label:"创建时间","min-width":"160",align:"center"},{default:o(l=>[p(be(F(Pe)(l.row.createTime,"YYYY-MM-DD HH:mm:ss")),1)]),_:1}),a(g,{label:"操作",width:"200",align:"center"},{default:o(l=>[u("div",Je,[a(m,{type:"primary",size:"small",onClick:ce=>H(l.row)},{default:o(()=>e[9]||(e[9]=[p("编辑")])),_:2},1032,["onClick"]),a(m,{type:"danger",size:"small",onClick:ce=>Q(l.row)},{default:o(()=>e[10]||(e[10]=[p("删除")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[de,w.value]]),u("div",Ke,[a(oe,{onSizeChange:ae,onCurrentChange:ee,"current-page":s.page,"page-sizes":[10,20,50,100],"page-size":s.size,layout:"total, sizes, prev, pager, next, jumper",total:C.value},null,8,["current-page","page-size","total"])])])]),a(re,{title:U.value,modelValue:_.value,"onUpdate:modelValue":e[5]||(e[5]=l=>_.value=l),width:"500px","append-to-body":"","destroy-on-close":""},{footer:o(()=>[u("div",We,[a(m,{onClick:W},{default:o(()=>e[12]||(e[12]=[p("取 消")])),_:1}),a(m,{type:"primary",onClick:Z,loading:S.value},{default:o(()=>e[13]||(e[13]=[p("确 定")])),_:1},8,["loading"])])]),default:o(()=>[a(I,{ref_key:"form",ref:b,model:i,rules:G,"label-width":"100px",class:"form-container"},{default:o(()=>[a(r,{label:"设备类型",prop:"deviceType"},{default:o(()=>[a(y,{modelValue:i.deviceType,"onUpdate:modelValue":e[2]||(e[2]=l=>i.deviceType=l),placeholder:"请选择",style:{width:"100%"}},{default:o(()=>[(c(!0),h(M,null,N(T.value,l=>(c(),z(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"设备图标",prop:"iconUrl"},{default:o(()=>[a(ie,{class:"avatar-uploader",action:q.value,"show-file-list":!1,"on-success":R,"before-upload":X,headers:{"X-Authorization":"Bearer "+O.value}},{default:o(()=>[i.iconUrl?(c(),h("img",{key:0,src:i.iconUrl,class:"avatar"},null,8,He)):(c(),h("div",Qe,[a(se,null,{default:o(()=>[a(F(he))]),_:1})]))]),_:1},8,["action","headers"]),e[11]||(e[11]=u("div",{class:"el-upload__tip"},"仅支持jpg/gif/png/ico格式文件，且文件大小不超过10MB",-1))]),_:1}),a(r,{label:"设备状态",prop:"deviceStatus"},{default:o(()=>[a(y,{modelValue:i.deviceStatus,"onUpdate:modelValue":e[3]||(e[3]=l=>i.deviceStatus=l),placeholder:"请选择",style:{width:"100%"}},{default:o(()=>[a(n,{label:"正常",value:"正常"}),a(n,{label:"离线",value:"离线"}),a(n,{label:"故障",value:"故障"}),a(n,{label:"维修中",value:"维修中"})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"状态颜色",prop:"statusColor"},{default:o(()=>[a(ne,{modelValue:i.statusColor,"onUpdate:modelValue":e[4]||(e[4]=l=>i.statusColor=l),"show-alpha":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),oa=Fe(Xe,[["__scopeId","data-v-95fc082d"]]);export{oa as default};
