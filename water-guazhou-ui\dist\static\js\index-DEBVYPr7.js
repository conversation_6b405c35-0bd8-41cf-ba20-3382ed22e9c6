import{_ as T}from"./TreeBox-DDD2iwoR.js";import{_ as x}from"./CardTable-rdWOL4_6.js";import{_ as W}from"./CardSearch-CB_HNR-Q.js";import{_ as v}from"./index-BJ-QPYom.js";import{d as I,c as h,r as d,l as C,bJ as S,o as B,ar as P,a9 as w,g as D,h as N,F as k,q as f,i as u,C as L}from"./index-r0dFAfgr.js";import"./index-0NlGN6gS.js";import{g as Y,a as j}from"./index-D041o0fJ.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as q}from"./usePartition-DkcY9fQ2.js";import{a as F}from"./index-DfMgLJKp.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const M=I({__name:"index",setup(O){const _=h(),c=h(),n=d({data:[],title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:t=>{n.currentProject!==t&&(n.currentProject=t,m())}}),g=d({defaultParams:{type:"year",year:C().format(S)},filters:[{hidden:!0,type:"select",label:"选择方式",clearable:!1,field:"type",options:[{label:"按年",value:"year"}]},{type:"year",label:"选择年份",format:"YYYY",clearable:!1,field:"year",disabledDate(t){return new Date<t}},{type:"input",label:"用户户号",field:"custCode",clearable:!1},{type:"select",label:"册本名称",field:"meterBookId",options:[]},{type:"select",label:"用水性质",field:"WaterCategory",autoFillOptions(t){Y("WaterCategoryType").then(o=>{t.options=o||[]})}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",loading:()=>!!e.loading,click:()=>m()},{type:"default",perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>{var t;(t=c.value)==null||t.resetForm(),m()}},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",click:()=>{var t;(t=_.value)==null||t.exportTable()}}]}]}),b=t=>[{prop:"custName",label:"用户名称",minWidth:120},{prop:"custCode",label:"用户户号",minWidth:120},{prop:"partitionName",label:"挂接名称",minWidth:120},{prop:"meterBookName",label:"册本名称",minWidth:120},{prop:"WaterCategory",label:"用水性质",minWidth:120},{prop:"title",label:"销水量(m³)",align:"center",subColumns:[...t||Array.from({length:C().get("M")+1}).map((o,r)=>({label:r+1+"月",prop:r+1+"月",minWidth:120})),{prop:"sum",label:"合计",minWidth:120},{prop:"avg",label:"平均",minWidth:120}]}],e=d({loading:!1,dataList:[],indexVisible:!0,columns:b(),pagination:{hide:!0,refreshData:({page:t,size:o})=>{e.pagination.page=t,e.pagination.limit=o,m()}}}),m=async()=>{var t,o,r,a;try{e.loading=!1;const i=((t=c.value)==null?void 0:t.queryParams)||{},p=(r=(await F({page:e.pagination.page||1,size:e.pagination.limit||20,partitionId:(o=n.currentProject)==null?void 0:o.value,...i})).data)==null?void 0:r.data;e.pagination.total=p.total||0,e.dataList=p.dataList||[],e.columns=b((a=p.monthList)==null?void 0:a.map(s=>({label:s.label,prop:s.value,minWidth:120})))}catch{}e.loading=!1},y=q();return B(async()=>{await y.getTree(),n.data=y.Tree.value,m(),P(1).then(t=>{var r,a;const o=(a=(r=t.data)==null?void 0:r.data[0])==null?void 0:a.id;j({orgId:o,value:"code"}).then(i=>{var p;const l=(p=g.filters)==null?void 0:p.find(s=>s.field==="meterBookId");l&&(l.options=w(i)||[])})})}),(t,o)=>{const r=v,a=W,i=x,l=T;return D(),N(l,null,{tree:k(()=>[f(r,{ref:"refTree","tree-data":u(n)},null,8,["tree-data"])]),default:k(()=>[f(a,{ref_key:"refSearch",ref:c,config:u(g)},null,8,["config"]),f(i,{ref_key:"refTable",ref:_,class:"card-table",config:u(e)},null,8,["config"])]),_:1})}}}),se=L(M,[["__scopeId","data-v-ae73e21e"]]);export{se as default};
