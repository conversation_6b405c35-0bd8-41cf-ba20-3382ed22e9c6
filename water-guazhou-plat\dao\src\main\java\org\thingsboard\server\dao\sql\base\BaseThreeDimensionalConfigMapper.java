package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig;
import org.thingsboard.server.dao.util.imodel.query.base.BaseThreeDimensionalConfigPageRequest;

import java.util.List;

/**
 * 公共管理平台-三维配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Mapper
public interface BaseThreeDimensionalConfigMapper {
    /**
     * 查询公共管理平台-三维配置
     *
     * @param id 公共管理平台-三维配置主键
     * @return 公共管理平台-三维配置
     */
    public BaseThreeDimensionalConfig selectBaseThreeDimensionalConfigById(String id);

    /**
     * 查询公共管理平台-三维配置列表
     *
     * @param baseThreeDimensionalConfig 公共管理平台-三维配置
     * @return 公共管理平台-三维配置集合
     */
    public IPage<BaseThreeDimensionalConfig> selectBaseThreeDimensionalConfigList(BaseThreeDimensionalConfigPageRequest baseThreeDimensionalConfig);

    /**
     * 新增公共管理平台-三维配置
     *
     * @param baseThreeDimensionalConfig 公共管理平台-三维配置
     * @return 结果
     */
    public int insertBaseThreeDimensionalConfig(BaseThreeDimensionalConfig baseThreeDimensionalConfig);

    /**
     * 修改公共管理平台-三维配置
     *
     * @param baseThreeDimensionalConfig 公共管理平台-三维配置
     * @return 结果
     */
    public int updateBaseThreeDimensionalConfig(BaseThreeDimensionalConfig baseThreeDimensionalConfig);

    /**
     * 删除公共管理平台-三维配置
     *
     * @param id 公共管理平台-三维配置主键
     * @return 结果
     */
    public int deleteBaseThreeDimensionalConfigById(String id);

    /**
     * 批量删除公共管理平台-三维配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseThreeDimensionalConfigByIds(@Param("array") List<String> ids);
}
