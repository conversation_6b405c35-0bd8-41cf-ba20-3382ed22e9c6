"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1423],{88669:(e,t,i)=>{function s(){return[0,0,0,0]}function r(e,t,i,s){return[e,t,i,s]}function o(e,t){return new Float64Array(e,t,4)}function n(){return r(1,1,1,1)}function l(){return r(1,0,0,0)}function a(){return r(0,1,0,0)}function u(){return r(0,0,1,0)}function p(){return r(0,0,0,1)}i.d(t,{a:()=>o,c:()=>s,f:()=>r});const c=n(),d=l(),y=a(),h=u(),b=p();Object.freeze(Object.defineProperty({__proto__:null,ONES:c,UNIT_W:b,UNIT_X:d,UNIT_Y:y,UNIT_Z:h,ZEROS:[0,0,0,0],clone:function(e){return[e[0],e[1],e[2],e[3]]},create:s,createView:o,fromArray:function(e){const t=[0,0,0,0],i=Math.min(4,e.length);for(let s=0;s<i;++s)t[s]=e[s];return t},fromValues:r,ones:n,unitW:p,unitX:l,unitY:a,unitZ:u,zeros:function(){return[0,0,0,0]}},Symbol.toStringTag,{value:"Module"}))},13867:(e,t,i)=>{i.d(t,{Z:()=>r});var s=i(69801);class r{constructor(e,t){this._storage=new s.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t,i){this._storage.put(e,t,i,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}get maxSize(){return this._storage.maxSize}set maxSize(e){this._storage.maxSize=e}}},12501:(e,t,i)=>{i.d(t,{Z:()=>h});var s,r=i(43697),o=i(22974),n=i(5600),l=(i(75215),i(52011)),a=i(30556),u=i(35671),p=i(5499),c=i(41733),d=i(84475);let y=s=class extends((0,c.W)(p.Z)){constructor(e){super(e),this.config=null,this.fieldMap=null,this.scaleExpression=null,this.scaleExpressionTitle=null,this.url=null,this.type="dictionary"}get _loader(){return new d.DictionaryLoader(this.url,this.config,this.fieldMap)}writeData(e,t){e&&(t.scalingExpressionInfo={expression:e,returnType:"number"})}writeVisualVariables(e,t,i,s){s?.origin||super.writeVisualVariables(e,t,i,s)}clone(){return new s({config:(0,o.d9)(this.config),scaleExpression:this.scaleExpression,scaleExpressionTitle:this.scaleExpressionTitle,fieldMap:(0,o.d9)(this.fieldMap),url:(0,o.d9)(this.url),visualVariables:(0,o.d9)(this.visualVariables)})}async getSymbolAsync(e,t){return this._loader.getSymbolAsync(e,t)}async collectRequiredFields(e,t){await this.collectVVRequiredFields(e,t),this.scaleExpression&&await(0,u.io)(e,t,this.scaleExpression);for(const i in this.fieldMap){const s=this.fieldMap[i];t.has(s)&&e.add(s)}}get arcadeRequired(){return!0}getSymbol(){return null}getSymbols(){return[]}getAttributeHash(){return this.visualVariables&&this.visualVariables.reduce(((e,t)=>e+t.getAttributeHash()),"")}getMeshHash(){return`${this.url}-${JSON.stringify(this.fieldMap)}`}getSymbolFields(){return this._loader.getSymbolFields()}};(0,r._)([(0,n.Cb)({type:d.DictionaryLoader})],y.prototype,"_loader",null),(0,r._)([(0,n.Cb)({type:Object,json:{read:{source:"configuration"},write:{target:"configuration"}}})],y.prototype,"config",void 0),(0,r._)([(0,n.Cb)({type:Object,json:{write:!0}})],y.prototype,"fieldMap",void 0),(0,r._)([(0,n.Cb)({type:String,json:{read:{source:"scalingExpressionInfo.expression"},write:!0}})],y.prototype,"scaleExpression",void 0),(0,r._)([(0,a.c)("scaleExpression")],y.prototype,"writeData",null),(0,r._)([(0,n.Cb)({type:String,json:{read:{source:"scalingExpressionInfo.title"},write:{target:"scalingExpressionInfo.title",overridePolicy(e){return{enabled:!!e&&!!this.scaleExpression}}}}})],y.prototype,"scaleExpressionTitle",void 0),(0,r._)([(0,n.Cb)({type:String,json:{write:!0}})],y.prototype,"url",void 0),(0,r._)([(0,a.c)("visualVariables")],y.prototype,"writeVisualVariables",null),y=s=(0,r._)([(0,l.j)("esri.renderers.DictionaryRenderer")],y);const h=y},28756:(e,t,i)=>{i.d(t,{Z:()=>v});var s,r=i(43697),o=i(22303),n=i(22974),l=i(5600),a=(i(75215),i(36030)),u=i(52011),p=i(35671),c=i(5499),d=i(41733),y=i(79887),h=i(96674);i(67676);let b=s=class extends h.wq{constructor(){super(...arguments),this.unit=null}clone(){return new s({unit:this.unit})}};(0,r._)([(0,l.Cb)({type:String,json:{write:!0}})],b.prototype,"unit",void 0),b=s=(0,r._)([(0,u.j)("esri.renderers.support.DotDensityLegendOptions")],b);const m=b;var g,f=i(20256),_=i(4095);let w=g=class extends((0,d.W)(c.Z)){constructor(e){super(e),this.attributes=null,this.backgroundColor=new o.Z([0,0,0,0]),this.dotBlendingEnabled=!0,this.dotShape="square",this.dotSize=1,this.legendOptions=null,this.outline=new _.Z,this.dotValue=null,this.referenceScale=null,this.seed=1,this.type="dot-density"}calculateDotValue(e){if(null==this.referenceScale)return this.dotValue;const t=e/this.referenceScale*this.dotValue;return t<1?1:t}getSymbol(){return new f.Z({outline:this.outline})}async getSymbolAsync(){return this.getSymbol()}getSymbols(){return[this.getSymbol()]}getAttributeHash(){return this.attributes?.reduce(((e,t)=>e+t.getAttributeHash()),"")??""}getMeshHash(){return JSON.stringify(this.outline)}clone(){return new g({attributes:(0,n.d9)(this.attributes),backgroundColor:(0,n.d9)(this.backgroundColor),dotBlendingEnabled:(0,n.d9)(this.dotBlendingEnabled),dotShape:(0,n.d9)(this.dotShape),dotSize:(0,n.d9)(this.dotSize),dotValue:(0,n.d9)(this.dotValue),legendOptions:(0,n.d9)(this.legendOptions),outline:(0,n.d9)(this.outline),referenceScale:(0,n.d9)(this.referenceScale),visualVariables:(0,n.d9)(this.visualVariables),authoringInfo:this.authoringInfo&&this.authoringInfo.clone()})}getControllerHash(){const e=this.attributes?.map((e=>e.field||e.valueExpression||""));return`${e}-${this.outline&&JSON.stringify(this.outline.toJSON())||""}`}async collectRequiredFields(e,t){await this.collectVVRequiredFields(e,t);for(const i of this.attributes??[])i.valueExpression&&await(0,p.io)(e,t,i.valueExpression),i.field&&e.add(i.field)}};(0,r._)([(0,l.Cb)({type:[y.Z],json:{write:!0}})],w.prototype,"attributes",void 0),(0,r._)([(0,l.Cb)({type:o.Z,json:{write:!0}})],w.prototype,"backgroundColor",void 0),(0,r._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],w.prototype,"dotBlendingEnabled",void 0),(0,r._)([(0,l.Cb)({type:String,json:{write:!1}})],w.prototype,"dotShape",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],w.prototype,"dotSize",void 0),(0,r._)([(0,l.Cb)({type:m,json:{write:!0}})],w.prototype,"legendOptions",void 0),(0,r._)([(0,l.Cb)({type:_.Z,json:{default:null,write:!0}})],w.prototype,"outline",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],w.prototype,"dotValue",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],w.prototype,"referenceScale",void 0),(0,r._)([(0,l.Cb)({type:Number,json:{write:!0}})],w.prototype,"seed",void 0),(0,r._)([(0,a.J)({dotDensity:"dot-density"})],w.prototype,"type",void 0),w=g=(0,r._)([(0,u.j)("esri.renderers.DotDensityRenderer")],w);const v=w},92271:(e,t,i)=>{i.d(t,{Z:()=>E});var s,r=i(43697),o=i(22303),n=(i(9790),i(60235)),l=i(22974),a=i(92604),u=i(1654),p=i(62357),c=i(5600),d=i(75215),y=i(1153),h=i(36030),b=i(52011),m=i(35671),g=i(5499),f=i(69237),_=i(96674);i(67676);let w=s=class extends _.wq{constructor(e){super(e),this.color=null,this.ratio=null}clone(){return new s({color:this.color,ratio:this.ratio})}};(0,r._)([(0,c.Cb)({type:o.Z,json:{type:[d.z8],default:null,write:!0}})],w.prototype,"color",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],w.prototype,"ratio",void 0),w=s=(0,r._)([(0,b.j)("esri.renderers.support.HeatmapColorStop")],w);const v=w;var S=i(2368);let x=class extends((0,S.J)(_.wq)){constructor(){super(...arguments),this.minLabel=null,this.maxLabel=null,this.title=null}};(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],x.prototype,"minLabel",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],x.prototype,"maxLabel",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],x.prototype,"title",void 0),x=(0,r._)([(0,b.j)("esri.renderers.support.HeatmapLegendOptions")],x);var C,j=i(73572),I=i(77987);const Z="esri.renderers.HeatmapRenderer",P=a.Z.getLogger(Z);function M(e){if(null!=e){const{maxDensity:t,minDensity:i,radius:s}=e;if(null!=t||null!=i||null!=s){const{blurRadius:t,maxPixelIntensity:i,minPixelIntensity:s,...r}=e;return r}}return e}let D=C=class extends g.Z{constructor(e){super(e),this.authoringInfo=null,this.colorStops=[new v({ratio:0,color:new o.Z("rgba(255, 140, 0, 0)")}),new v({ratio:.75,color:new o.Z("rgba(255, 140, 0, 1)")}),new v({ratio:.9,color:new o.Z("rgba(255, 0,   0, 1)")})],this.field=null,this.fieldOffset=0,this.legendOptions=null,this.maxDensity=.04,this.minDensity=0,this.radius=18,this.referenceScale=0,this.type="heatmap",this.valueExpression=null,this.valueExpressionTitle=null,this._warnedProps={blurRadius:!1,maxPixelIntensity:!1,minPixelIntensity:!1}}normalizeCtorArgs(e){return M(e)}get blurRadius(){return(0,j.AJ)(this.radius)}set blurRadius(e){const t=this.maxPixelIntensity,i=this.minPixelIntensity;this._set("radius",(0,j.k0)(e)),this._warnAboutDeprecatedGaussianBlurProp("blurRadius","radius"),this._set("maxDensity",t*this._pixelIntensityToDensity),this._set("minDensity",i*this._pixelIntensityToDensity)}get maxPixelIntensity(){return this.maxDensity/this._pixelIntensityToDensity}set maxPixelIntensity(e){this._set("maxDensity",e*this._pixelIntensityToDensity),this._warnAboutDeprecatedGaussianBlurProp("maxPixelIntensity","maxDensity")}get minPixelIntensity(){return this.minDensity/this._pixelIntensityToDensity}set minPixelIntensity(e){this._set("minDensity",e*this._pixelIntensityToDensity),this._warnAboutDeprecatedGaussianBlurProp("minPixelIntensity","minDensity")}get _pixelIntensityToDensity(){return 24/(j.nu**2*this.blurRadius**4)}_warnAboutDeprecatedGaussianBlurProp(e,t){this._warnedProps[e]||"user"===(0,y.vw)(this).getDefaultOrigin()&&(this._warnedProps[e]=!0,(0,u.Os)((()=>{(0,n.Mr)(P,e,{replacement:`${String(t)} (suggested value: ${this._get(t)})`,version:"4.24"})})))}read(e,t){e=M(e),super.read(e,t)}getSymbol(){return new I.Z}async getSymbolAsync(){return this.getSymbol()}getSymbols(){return[this.getSymbol()]}async collectRequiredFields(e,t){const i=this.field,s=this.valueExpression;i&&"string"==typeof i&&await(0,m.AB)(e,t,i),s&&"string"==typeof s&&await(0,m.io)(e,t,s)}getAttributeHash(){return null}getMeshHash(){return`${JSON.stringify(this.colorStops)}.${this.blurRadius}.${this.field}`}clone(){return new C({authoringInfo:this.authoringInfo&&this.authoringInfo.clone(),colorStops:(0,l.d9)(this.colorStops),field:this.field,legendOptions:(0,l.d9)(this.legendOptions),maxDensity:this.maxDensity,minDensity:this.minDensity,radius:this.radius,referenceScale:this.referenceScale,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle})}};(0,r._)([(0,c.Cb)({type:f.Z,json:{write:!0,origins:{"web-scene":{write:!1,read:!1}}}})],D.prototype,"authoringInfo",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{origins:{"portal-item":{write:!0},"web-map":{write:!0}}}})],D.prototype,"blurRadius",null),(0,r._)([(0,c.Cb)({type:[v],json:{write:!0}})],D.prototype,"colorStops",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],D.prototype,"field",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:{overridePolicy:(e,t,i)=>({enabled:null==i})},origins:{"web-scene":{write:!1}}}})],D.prototype,"fieldOffset",void 0),(0,r._)([(0,c.Cb)({type:x,json:{write:!0}})],D.prototype,"legendOptions",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],D.prototype,"maxDensity",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{origins:{"portal-item":{write:!0},"web-map":{write:!0}}}})],D.prototype,"maxPixelIntensity",null),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],D.prototype,"minDensity",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{origins:{"portal-item":{write:!0},"web-map":{write:!0}}}})],D.prototype,"minPixelIntensity",null),(0,r._)([(0,c.Cb)({type:Number,cast:p.t_,json:{write:!0}})],D.prototype,"radius",void 0),(0,r._)([(0,c.Cb)({type:Number,range:{min:0},json:{default:0,write:!0}})],D.prototype,"referenceScale",void 0),(0,r._)([(0,h.J)({heatmap:"heatmap"})],D.prototype,"type",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0,origins:{"web-document":{write:!1},"portal-item":{write:!1}}}})],D.prototype,"valueExpression",void 0),(0,r._)([(0,c.Cb)({type:String})],D.prototype,"valueExpressionTitle",void 0),(0,r._)([(0,c.Cb)({readOnly:!0})],D.prototype,"_pixelIntensityToDensity",null),D=C=(0,r._)([(0,b.j)(Z)],D);const E=D},72529:(e,t,i)=>{i.d(t,{Z:()=>S});var s=i(43697),r=i(22303),o=(i(9790),i(2368)),n=i(70586),l=i(62357),a=i(5600),u=(i(75215),i(67676),i(36030)),p=i(52011),c=i(35671),d=i(5499),y=i(41733),h=i(79887),b=i(96674);let m=class extends((0,o.J)(b.wq)){constructor(){super(...arguments),this.color=new r.Z([0,0,0,0]),this.label=null,this.threshold=0}};(0,s._)([(0,a.Cb)({type:r.Z,json:{write:!0}})],m.prototype,"color",void 0),(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],m.prototype,"label",void 0),(0,s._)([(0,a.Cb)({type:Number,range:{min:0,max:1},json:{write:!0}})],m.prototype,"threshold",void 0),m=(0,s._)([(0,p.j)("esri.renderers.support.OthersCategory")],m);let g=class extends((0,o.J)(b.wq)){constructor(){super(...arguments),this.title=null}};(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],g.prototype,"title",void 0),g=(0,s._)([(0,p.j)("esri.renderers.support.PieChartLegendOptions")],g);var f=i(77987),_=i(20256),w=i(4095);let v=class extends((0,y.W)((0,o.J)(d.Z))){constructor(e){super(e),this.attributes=null,this.backgroundFillSymbol=null,this.defaultColor=new r.Z([0,0,0,0]),this.defaultLabel=null,this.holePercentage=0,this.othersCategory=new m,this.legendOptions=null,this.outline=null,this.size=12,this.type="pie-chart"}getSymbol(){return new f.Z({size:this.size?this.size/2+(this.outline?.width||0):0})}async getSymbolAsync(){return this.getSymbol()}getSymbols(){return[this.getSymbol(),this.backgroundFillSymbol].filter(n.pC)}getAttributeHash(){return this.visualVariables&&this.visualVariables.reduce(((e,t)=>e+t.getAttributeHash()),"")}getMeshHash(){return this.getSymbols().reduce(((e,t)=>e+JSON.stringify(t)),"")}async collectRequiredFields(e,t){await this.collectVVRequiredFields(e,t);for(const i of this.attributes)i.valueExpression&&await(0,c.io)(e,t,i.valueExpression),i.field&&e.add(i.field)}};(0,s._)([(0,a.Cb)({type:[h.Z],json:{write:!0}})],v.prototype,"attributes",void 0),(0,s._)([(0,a.Cb)({type:_.Z,json:{default:null,write:!0}})],v.prototype,"backgroundFillSymbol",void 0),(0,s._)([(0,a.Cb)({type:r.Z,json:{write:!0}})],v.prototype,"defaultColor",void 0),(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],v.prototype,"defaultLabel",void 0),(0,s._)([(0,a.Cb)({type:Number,range:{min:0,max:1},json:{write:!0}})],v.prototype,"holePercentage",void 0),(0,s._)([(0,a.Cb)({type:m,json:{write:!0}})],v.prototype,"othersCategory",void 0),(0,s._)([(0,a.Cb)({type:g,json:{write:!0}})],v.prototype,"legendOptions",void 0),(0,s._)([(0,a.Cb)({type:w.Z,json:{default:null,write:!0}})],v.prototype,"outline",void 0),(0,s._)([(0,a.Cb)({type:Number,cast:l.t_,json:{write:!0}})],v.prototype,"size",void 0),(0,s._)([(0,u.J)({pieChart:"pie-chart"})],v.prototype,"type",void 0),v=(0,s._)([(0,p.j)("esri.renderers.PieChartRenderer")],v);const S=v},84382:(e,t,i)=>{i.d(t,{Z:()=>y});var s,r=i(43697),o=i(22974),n=i(5600),l=(i(75215),i(36030)),a=i(52011),u=i(5499),p=i(41733),c=i(9833);let d=s=class extends((0,p.W)(u.Z)){constructor(e){super(e),this.description=null,this.label=null,this.symbol=null,this.type="simple"}async collectRequiredFields(e,t){await Promise.all([this.collectSymbolFields(e,t),this.collectVVRequiredFields(e,t)])}async collectSymbolFields(e,t){await Promise.all(this.getSymbols().map((i=>i.collectRequiredFields(e,t))))}getSymbol(e,t){return this.symbol}async getSymbolAsync(e,t){return this.symbol}getSymbols(){return this.symbol?[this.symbol]:[]}getAttributeHash(){return this.visualVariables&&this.visualVariables.reduce(((e,t)=>e+t.getAttributeHash()),"")}getMeshHash(){return this.getSymbols().reduce(((e,t)=>e+JSON.stringify(t)),"")}get arcadeRequired(){return this.arcadeRequiredForVisualVariables}clone(){return new s({description:this.description,label:this.label,symbol:this.symbol&&this.symbol.clone(),visualVariables:(0,o.d9)(this.visualVariables),authoringInfo:this.authoringInfo&&this.authoringInfo.clone()})}};(0,r._)([(0,n.Cb)({type:String,json:{write:!0}})],d.prototype,"description",void 0),(0,r._)([(0,n.Cb)({type:String,json:{write:!0}})],d.prototype,"label",void 0),(0,r._)([(0,n.Cb)(c.Gn)],d.prototype,"symbol",void 0),(0,r._)([(0,l.J)({simple:"simple"})],d.prototype,"type",void 0),d=s=(0,r._)([(0,a.j)("esri.renderers.SimpleRenderer")],d);const y=d},79887:(e,t,i)=>{i.d(t,{Z:()=>y});var s,r=i(43697),o=i(22303),n=i(96674),l=i(92604),a=i(5600),u=i(90578),p=(i(67676),i(52011)),c=i(75215);let d=s=class extends n.wq{constructor(e){super(e),this.color=null,this.field=null,this.label=null,this.valueExpression=null,this.valueExpressionTitle=null}castField(e){return null==e?e:"function"==typeof e?(l.Z.getLogger(this.declaredClass).error(".field: field must be a string value"),null):(0,c.Zs)(e)}getAttributeHash(){return`${this.field}-${this.valueExpression}`}clone(){return new s({color:this.color&&this.color.clone(),field:this.field,label:this.label,valueExpression:this.valueExpression,valueExpressionTitle:this.valueExpressionTitle})}};(0,r._)([(0,a.Cb)({type:o.Z,json:{type:[Number],write:!0}})],d.prototype,"color",void 0),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],d.prototype,"field",void 0),(0,r._)([(0,u.p)("field")],d.prototype,"castField",null),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],d.prototype,"label",void 0),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],d.prototype,"valueExpression",void 0),(0,r._)([(0,a.Cb)({type:String,json:{write:!0}})],d.prototype,"valueExpressionTitle",void 0),d=s=(0,r._)([(0,p.j)("esri.renderers.support.AttributeColorInfo")],d);const y=d},84475:(e,t,i)=>{i.r(t),i.d(t,{DictionaryLoader:()=>b});var s=i(22303),r=i(3172),o=i(20102),n=i(92604),l=i(13867),a=i(70586),u=i(95330),p=i(19153),c=i(59266),d=i(17386);const y="esri.renderers.support.DictionaryLoader",h={type:"CIMSimpleLineCallout",lineSymbol:{type:"CIMLineSymbol",symbolLayers:[{type:"CIMSolidStroke",width:.5,color:[0,0,0,255]}]}};class b{constructor(e,t,i){this.config=null,this.fieldMap=null,this.url=null,this._ongoingRequests=new Map,this._symbolCache=new l.Z(100),this._dictionaryPromise=null,this.url=e,this.config=t,this.fieldMap=i}getSymbolFields(){return this._symbolFields}async getSymbolAsync(e,t){let i;this._dictionaryPromise||(this._dictionaryPromise=this.fetchResources(t));try{i=await this._dictionaryPromise}catch(e){if((0,u.D_)(e))return this._dictionaryPromise=null,null}const r={};if(this.fieldMap)for(const t of this._symbolFields){const i=this.fieldMap[t];if(i&&null!=e.attributes[i]){const s=""+e.attributes[i];r[t]=s}else r[t]=""}const o=i?.(r,t);if(!o||"string"!=typeof o)return null;const n=(0,p.hP)(o).toString(),l=this._symbolCache.get(n);if(l)return l.catch((()=>{this._symbolCache.pop(n)})),l;const c=o.split(";"),d=[],y=[];for(const e of c)if(e)if(e.includes("po:")){const t=e.substr(3).split("|");if(3===t.length){const e=t[0],i=t[1];let r=t[2];if("DashTemplate"===i)r=r.split(" ").map((e=>Number(e)));else if("Color"===i){const e=new s.Z(r).toRgba();r=[e[0],e[1],e[2],255*e[3]]}else r=Number(r);y.push({primitiveName:e,propertyName:i,value:r})}}else if(e.includes("|")){for(const t of e.split("|"))if(this._itemNames.has(t)){d.push(t);break}}else this._itemNames.has(e)&&d.push(e);const h=!(0,a.pC)(e.geometry)||!e.geometry.hasZ&&"point"===e.geometry.type,b=this._cimPartsToCIMSymbol(d,y,h,t);return this._symbolCache.put(n,b,1),b}async fetchResources(e){if(this._dictionaryPromise)return this._dictionaryPromise;if(!this.url)return void n.Z.getLogger(y).error("no valid URL!");const t=(0,r.default)(this.url+"/resources/styles/dictionary-info.json",{responseType:"json",query:{f:"json"},signal:(0,a.pC)(e)?e.signal:null}),[{data:i}]=await Promise.all([t,(0,c.LC)()]);if(!i)throw this._dictionaryPromise=null,new o.Z("esri.renderers.DictionaryRenderer","Bad dictionary data!");const s=i.expression,l=i.authoringInfo;this._refSymbolUrlTemplate=this.url+"/"+i.cimRefTemplateUrl,this._itemNames=new Set(i.itemsNames),this._symbolFields=l.symbol;const u={};if(this.config){const e=this.config;for(const t in e)u[t]=e[t]}if(l.configuration)for(const e of l.configuration)u.hasOwnProperty(e.name)||(u[e.name]=e.value);const p=[];if((0,a.pC)(e)&&e.fields&&this.fieldMap)for(const t of this._symbolFields){const i=this.fieldMap[t],s=e.fields.filter((e=>e.name===i));s.length>0&&p.push({...s[0],name:t})}const d=(0,c.pp)(s,(0,a.pC)(e)?e.spatialReference:null,p,u).then((e=>{const t={scale:0};return(i,s)=>{if((0,a.Wi)(e))return null;const r=e.repurposeFeature({geometry:null,attributes:i});return t.scale=(0,a.pC)(s)?s.scale??void 0:void 0,e.evaluate({$feature:r,$view:t})}})).catch((e=>(n.Z.getLogger(y).error("Creating dictinoary expression failed:",e),null)));return this._dictionaryPromise=d,d}async _cimPartsToCIMSymbol(e,t,i,s){const r=new Array(e.length);for(let t=0;t<e.length;t++)r[t]=this._getSymbolPart(e[t],s);const o=await Promise.all(r),n=this.fieldMap;if(n)for(const e of o)m(e,n);return new d.Z({data:this._combineSymbolParts(o,t,i)})}async _getSymbolPart(e,t){if(this._ongoingRequests.has(e))return this._ongoingRequests.get(e).then((e=>e.data));const i=this._refSymbolUrlTemplate.replace(/\{itemName\}/gi,e),s=(0,r.default)(i,{responseType:"json",query:{f:"json"},...t});this._ongoingRequests.set(e,s);try{return(await s).data}catch(t){throw this._ongoingRequests.delete(e),t}}_combineSymbolParts(e,t,i){if(!e||0===e.length)return null;const s={...e[0]};if(e.length>1){s.symbolLayers=[];for(const t of e){const e=t;s.symbolLayers.unshift(...e.symbolLayers)}}return i&&(s.callout=h),{type:"CIMSymbolReference",symbol:s,primitiveOverrides:t}}}function m(e,t){if(!e)return;const i=e.symbolLayers;if(!i)return;let s=i.length;for(;s--;){const e=i[s];e&&!1!==e.enable&&"CIMVectorMarker"===e.type&&g(e,t)}}function g(e,t){const i=e.markerGraphics;if(i)for(const e of i){if(!e)continue;const i=e.symbol;if(i)switch(i.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":m(i,t);break;case"CIMTextSymbol":i.fieldMap=t}}}},73572:(e,t,i)=>{i.d(t,{AJ:()=>n,If:()=>a,QM:()=>l,k0:()=>o,nu:()=>r,wx:()=>u}),i(22021);var s=i(62357);i(98766),i(88669);const r=2.4;function o(e){return(0,s.Wz)(e*r)}function n(e){return(0,s.F2)(e)/r}function l(e,t,i,r){const{radius:o,fieldOffset:n,field:l}=t,u=Math.round((0,s.F2)(o)),p=new Float64Array(i*r);let c,d=Number.NEGATIVE_INFINITY;const y=function(e,t){return null!=e?"string"==typeof t?t=>-1*+t.readAttribute(e):i=>+i.readAttribute(e)+t:e=>1}(l,n),h=new Set;for(const t of e){const e=t.getCursor();for(;e.next();){const t=e.getObjectId();if(h.has(t))continue;h.add(t);const s=e.readLegacyPointGeometry(),o=128;if(s.x<-o||s.x>=i+o||s.y<-o||s.y>r+o)continue;const n=+y(e),l=Math.max(0,Math.round(s.x)-u),b=Math.max(0,Math.round(s.y)-u),m=Math.min(r,Math.round(s.y)+u),g=Math.min(i,Math.round(s.x)+u);for(let e=b;e<m;e++)for(let t=l;t<g;t++){const r=e*i+t,o=a(s.x-t,s.y-e,u);c=p[r]+=o*n,c>d&&(d=c)}}}return{matrix:p.buffer,max:d}}function a(e,t,i){const s=Math.sqrt(e**2+t**2)/i;return s>1?0:3/(Math.PI*i**2)*(1-s**2)**2}function u(e,t){return"function"==typeof e?e:e?"string"==typeof t?t=>-1*+t[e]:i=>+i[e]+t:()=>1}},91423:(e,t,i)=>{i.d(t,{a:()=>l});var s=i(20941),r=i(63213),o=i(32400);const n=(0,r.d)({types:o.A});function l(e,t,i){return e?e&&(e.styleName||e.styleUrl)&&"uniqueValue"!==e.type?(i&&i.messages&&i.messages.push(new s.Z("renderer:unsupported","Only UniqueValueRenderer can be referenced from a web style, but found '"+e.type+"'",{definition:e,context:i})),null):n(e,t,i):null}},32400:(e,t,i)=>{i.d(t,{A:()=>c,o:()=>d});var s=i(16050),r=i(12501),o=i(28756),n=i(92271),l=i(72529),a=i(5499),u=i(84382),p=i(81571);const c={key:"type",base:a.Z,typeMap:{heatmap:n.Z,simple:u.Z,"unique-value":p.Z,"class-breaks":s.Z,"dot-density":o.Z,dictionary:r.Z,"pie-chart":l.Z},errorContext:"renderer"},d={key:"type",base:a.Z,typeMap:{simple:u.Z,"unique-value":p.Z,"class-breaks":s.Z,heatmap:n.Z},errorContext:"renderer"}}}]);