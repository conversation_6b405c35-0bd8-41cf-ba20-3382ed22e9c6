/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.virtual;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.VirtualEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/21/2017.
 */
@SqlDao
public interface VirtualRepository extends CrudRepository<VirtualEntity, String> {


    @Query("SELECT a FROM VirtualEntity a WHERE a.tenantId = :tenantId " +
            "AND a.name = :name")
    List<VirtualEntity> findByTenantIdAndName(@Param("tenantId") String tenantId,
                                              @Param("name") String name);

    @Query("SELECT a FROM VirtualEntity a WHERE a.id = :virtualId ")
    VirtualEntity findById(@Param("virtualId") String virtualId);


    @Query("SELECT a FROM VirtualEntity a WHERE a.tenantId = :tenantId order by a.id")
    List<VirtualEntity> findByTenantId(@Param("tenantId") String tenantId);

    @Query("SELECT a FROM VirtualEntity a WHERE a.serialNumber = :serialNumber")
    List<VirtualEntity> findBySerialNumber(@Param("serialNumber") String serialNumber);

    List<VirtualEntity> findByTenantIdAndType(@Param("tenantId") String tenantId, @Param("type") String type);

}
