package org.thingsboard.server.dao.util.imodel.query.purchase;

import org.thingsboard.server.dao.model.sql.purchase.ContractDetail;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ContractDetailPageRequest extends AdvancedPageableQueryEntity<ContractDetail, ContractDetailPageRequest> {
    // 合同主表ID
    private String mainId;
}
