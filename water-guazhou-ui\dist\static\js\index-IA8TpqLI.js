import{_ as F}from"./index-C9hz-UZb.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{d as H,a6 as W,r as D,bF as l,c as L,am as U,bB as k,a8 as $,bX as G,s as w,o as J,ah as X,bA as K,ay as Q,g as Z,n as ee,q as d,i as s,F as v,cs as P,bo as V,bR as j,p as E,j as te,dF as ae,dA as oe,al as re,aj as ne,bD as le,C as se}from"./index-r0dFAfgr.js";import{_ as ie}from"./CardSearch-CB_HNR-Q.js";import{f as ce}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as de}from"./useStation-DJgnSZIA.js";import{p as ue}from"./printUtils-C-AxhDcd.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function pe(){return{title:{text:"",textStyle:{color:"#5470C6",fontSize:"14px"},top:10},grid:{left:90,right:90,top:70,bottom:80},legend:{top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(g){let _=g[0].name+"<br/>";return g.forEach(a=>{_+=a.marker+a.seriesName+": "+a.value+"<br/>"}),_}},xAxis:{type:"category",data:[],axisLabel:{rotate:45,interval:0,fontSize:11,margin:8},axisTick:{alignWithLabel:!0}},yAxis:[{position:"left",type:"value",name:"数值",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[]}}const me={class:"wrapper"},fe=H({__name:"index",setup(q){const{getStationTree:g}=de(),_=W(),a=D({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,dataList:{}});l().date();const A=L(),N=L(),h=L(),b=L();U(()=>a.activeName,()=>{a.activeName==="echarts"&&k(()=>{I()})});const p=D({data:[],currentProject:{}}),S=D({defaultParams:{type:"day",year:[l().format("YYYY"),l().format("YYYY")],month:[l().format("YYYY-MM"),l().format("YYYY-MM")],day:[l().format("YYYY-MM-DD"),l().format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:$(()=>p.data),label:"站点选择",onChange:t=>{const e=G(p.data,"children","id",t);p.currentProject=e,a.treeDataType=e.data.type,a.treeDataType==="Station"&&(a.stationId=e.id)}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(t,e,o)=>{o.hidden=t.type==="month"||t.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(t,e,o)=>{o.hidden=t.type==="day"||t.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(t,e,o)=>{o.hidden=t.type==="month"||t.type==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:w(re),click:()=>O()},{perm:!0,text:"导出",type:"success",svgIcon:w(ne),click:()=>z()},{perm:!0,text:"打印",type:"warning",svgIcon:w(le),click:()=>B()}]}]}),i=D({loading:!1,columns:[],dataList:[],pagination:{hide:!0}}),O=()=>{var f,y;i.loading=!0;const t=((f=N.value)==null?void 0:f.queryParams)||{},{type:e="day"}=t,o=t[e];if(!o||!o[0]||!o[1]){i.loading=!1;return}a.title="水源地运行概况("+(e==="day"?"日报":e==="month"?"月报":"年报")+l(o[0]).format(e==="day"?"YYYY-MM-DD":e==="month"?"YYYY-MM":"YYYY")+"至"+l(o[1]).format(e==="day"?"YYYY-MM-DD":e==="month"?"YYYY-MM":"YYYY")+")";const[c,m]=o,n={start:l(c).startOf(e==="day"?"day":e==="month"?"month":"year").valueOf(),end:l(m).endOf(e==="day"?"day":e==="month"?"month":"year").valueOf(),queryType:e,projectId:((y=p.currentProject)==null?void 0:y.id)||""};ce(n).then(u=>{var Y,x;const r=(Y=u.data)==null?void 0:Y.data;a.dataList=r;const M=(x=r==null?void 0:r.tableInfo)==null?void 0:x.map(C=>({prop:C.columnValue,label:C.columnName,unit:C.unit?"("+C.unit+")":""}));i.columns=M,i.dataList=r==null?void 0:r.tableDataList,i.loading=!1,a.activeName==="echarts"&&I()}).catch(u=>{console.error("查询运行概况数据失败:",u),i.loading=!1})};J(async()=>{var e;const t=await g("水源地");p.data=t,p.currentProject=X(t),S.defaultParams={...S.defaultParams,treeData:p.currentProject},(e=N.value)==null||e.resetForm(),O(),k(()=>{b.value&&_.listenTo(b.value,T)})});const z=()=>{var t;(t=A.value)==null||t.exportTable()},B=()=>{ue({title:a.title,data:i.dataList,titleList:i.columns})},T=()=>{var t;(t=h.value)==null||t.resize()},I=()=>{var t,e;if(!(!((t=a.dataList)!=null&&t.tableDataList)||!((e=a.dataList)!=null&&e.tableInfo)||!h.value))try{h.value.clear(),k(()=>{var f,y,u;if(!h.value)return;const o=pe(),c=(f=a.dataList)==null?void 0:f.tableDataList,m=(y=a.dataList)==null?void 0:y.tableInfo;if(!c||!m||!Array.isArray(c)||!Array.isArray(m))return;const n=m.filter(r=>r.columnName!=="数据时间"&&r.columnValue!=="ts"&&r.columnValue!=="stationName"&&!["站点名称","水源地名称"].includes(r.columnName));o.xAxis.data=c.map(r=>r.stationName||r.name||"未知站点"),n.length>0&&(o.yAxis[0].name=n[0].unit?`${n[0].columnName}(${n[0].unit})`:n[0].columnName),o.series=n.map((r,M)=>{const Y=["#5470C6","#91CC75","#FAC858","#EE6666","#73C0DE","#3BA272","#FC8452","#9A60B4","#EA7CCC"];return{name:r.columnName,type:"bar",data:c.map(x=>x[r.columnValue]||0),itemStyle:{color:Y[M%Y.length]},label:{show:!1},barWidth:n.length===1?"60%":void 0}}),a.chartOption=o,(u=h.value)==null||u.setOption(o)})}catch(o){console.error("图表渲染错误:",o)}};return K(()=>{b.value&&_.removeListener(b.value,T)}),(t,e)=>{const o=ie,c=ae,m=oe,n=Q("VChart"),f=R,y=F;return Z(),ee("div",me,[d(o,{ref_key:"cardSearch",ref:N,config:s(S)},null,8,["config"]),d(y,{class:"card",title:s(a).activeName==="list"?"运行概况列表":"运行概况图表"},{query:v(()=>[d(m,{modelValue:s(a).activeName,"onUpdate:modelValue":e[0]||(e[0]=u=>s(a).activeName=u)},{default:v(()=>[d(c,{label:"echarts"},{default:v(()=>[d(s(P),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:bar-chart-line"})]),_:1}),d(c,{label:"list"},{default:v(()=>[d(s(P),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:v(()=>[V(E("div",{ref_key:"chartContainer",ref:b,class:"chart-box"},[d(n,{ref_key:"refChart",ref:h,theme:s(te)().isDark?"dark":"light",option:s(a).chartOption},null,8,["theme","option"])],512),[[j,s(a).activeName==="echarts"]]),V(E("div",null,[d(f,{id:"print",ref_key:"refTable",ref:A,class:"card-table",config:s(i)},null,8,["config"])],512),[[j,s(a).activeName==="list"]])]),_:1},8,["title"])])}}}),De=se(fe,[["__scopeId","data-v-6f118f61"]]);export{De as default};
