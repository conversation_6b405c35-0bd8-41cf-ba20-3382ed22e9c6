/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.alarm;

import lombok.Data;

@Data
public class PropAlarm {
    private String alarmType;
    private String energyType;
    private String alarmValue;
    private String alarmRemarks;
    private String deviceName;
    private String attribute;
    private String level1;
    private String level2;
    private String level3;
    private String period;

    public PropAlarm(String alarmType, String energyType, String alarmValue, String alarmRemarks, String deviceName, String attribute, String level1, String level2, String level3, String period) {
        this.alarmType = alarmType;
        this.energyType = energyType;
        this.alarmValue = alarmValue;
        this.alarmRemarks = alarmRemarks;
        this.deviceName = deviceName;
        this.attribute = attribute;
        this.level1 = level1;
        this.level2 = level2;
        this.level3 = level3;
        this.period = period;
    }

}
