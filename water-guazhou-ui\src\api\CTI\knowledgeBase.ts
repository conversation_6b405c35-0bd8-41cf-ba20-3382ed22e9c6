// 知识库
import request from '@/plugins/axios';

// 获取知识库公告列表
export const knowledgeNoticeList = (params: {
  page: number;
  size: number;
  type?: string;
  startTime?: number | null;
  endTime?: number | null;
}) => {
  return request({
    url: '/api/ss/knowledge/notice',
    method: 'get',
    params
  });
};

/**
 * 保存日程安排
 * @param params
 * @returns
 */
export const postSchedule = (params: {
  time: string;
  title: string;
  content: string;
}) => {
  return request({
    url: '/api/userSchedule/save',
    method: 'post',
    data: params
  });
};

// 查询当前用户的日程列表
export const getAgendaList = (params: {
  beginTime: string;
  endTime: string;
}) => {
  return request({
    url: '/api/userSchedule/list',
    method: 'get',
    params
  });
};
