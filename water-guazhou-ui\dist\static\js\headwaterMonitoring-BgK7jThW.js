import{m as a}from"./index-r0dFAfgr.js";function r(t){return a({url:"/istar/api/water/source/getWaterSupplyInfo",method:"get",params:t})}function o(t){return a({url:"/istar/api/water/source/getWaterSupplyDetail",method:"get",params:{stationId:t||""}})}function u(t){return a({url:"/istar/api/water/source/getWaterSupplyInfoView",method:"get",params:t})}function i(t){return a({url:"/istar/api/water/source/getWaterSupplyInfoDetail",method:"get",params:t})}function p(t){return a({url:"/istar/api/station/data/getThreeDaysData",method:"get",params:t})}function n(t){return a({url:"/istar/api/station/data/stationDayDataQuery",method:"get",params:t})}function l(t){return a({url:"/istar/api/water/source/getWaterSupplyTotal",method:"get",params:t})}function s(){return a({url:"/istar/api/boosterPumpStation/getWaterSupplyInfoTotal",method:"get"})}function g(t){return a({url:"/istar/api/water/source/getWaterSupplyAllTotal",method:"get",params:t})}function y(t){return a({url:"/istar/api/boosterPumpStation/getWaterSupplyDetailTotal",method:"get",params:t})}export{p as a,u as b,i as c,s as d,y as e,l as f,r as g,g as h,o as i,n as s};
