package org.thingsboard.server.dao.smartService.knowledge;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice;
import org.thingsboard.server.dao.sql.smartService.knowledge.KnowledgeNoticeMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KnowledgeNoticeServiceImpl implements KnowledgeNoticeService {
    @Autowired
    private KnowledgeNoticeMapper knowledgeNoticeMapper;

    @Override
    public PageData getList(String type, Long startTime, Long endTime, int page, int size, String tenantId) {

        List<KnowledgeNotice> knowledgeNotices = knowledgeNoticeMapper.getList(type, startTime, endTime, page, size, tenantId);

        int total = knowledgeNoticeMapper.getListCount(type, startTime, endTime, tenantId);

        return new PageData(total, knowledgeNotices);

    }

    @Override
    public KnowledgeNotice save(KnowledgeNotice knowledgeNotice) {
        knowledgeNotice.setUpdateTime(new Date());
        if (StringUtils.isBlank(knowledgeNotice.getId())) {
            knowledgeNotice.setCreateTime(new Date());
            knowledgeNoticeMapper.insert(knowledgeNotice);
        } else {
            knowledgeNoticeMapper.updateById(knowledgeNotice);
        }

        return knowledgeNotice;
    }

    @Override
    public int delete(List<String> ids) {
        return knowledgeNoticeMapper.deleteBatchIds(ids);
    }

    @Override
    public List<KnowledgeNotice> getAll(String tenantId) {
        return knowledgeNoticeMapper.getAll(tenantId);
    }

    @Override
    public KnowledgeNotice getById(String id) {
        return knowledgeNoticeMapper.selectById(id);
    }
}
