/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 */
public class DataConstants {


    public static final String SYSTEM = "SYSTEM";
    public static final String TENANT = "TENANT";
    public static final String PROJECT = "project";
    public static final String CUSTOMER = "CUSTOMER";
    public static final String DEVICE = "DEVICE";
    public static final String DEVICE_ID = "deviceId";
    public static final String DEVICE_NAME = "deviceName";
    public static final String PROPERTY = "property";
    public static final String RESOURCE_NUMBER = "resourceNumber";
    public static final String ROOT = "root";
    public static final String ORGIN = "_orgin";
    public static List<String> DeviceTypeParam = Arrays.asList("MQTT", "NBMQTT", "DTU", "NBDTU", "MODBUS", "Gateway");
    public static final ConcurrentHashMap<Integer, Object> sendMap = new ConcurrentHashMap<>();
    public static final ConcurrentMap<UUID, Object> localToDeviceRpcRequests = new ConcurrentHashMap<>();
    public static final ConcurrentMap<UUID, Object> localToRuleEngineRpcRequests = new ConcurrentHashMap<>();
    public static final ConcurrentMap<String, String> machineCodeMap = new ConcurrentHashMap<>();
    //排序类型
    public static final String ORDER = "order";
    /**
     * 排序-时间降序
     */
    public static final String ORDER_TIME_DESC = "timeDescending";
    /**
     * 排序-时间升序序
     */
    public static final String ORDER_TIME_ASC = "timeAscending";
    /**
     * 排序-编号降序
     */
    public static final String ORDER_NUMBER_DESC = "numberDescending";
    /**
     * 排序-编号升序
     */
    public static final String ORDER_NUMBER_ASC = "numberAscending";
    /**
     * 排序-数据降序
     */
    public static final String ORDER_VALUE_DESC = "temperatureDescending";
    /**
     * 排序-数据升序
     */
    public static final String ORDER_VALUE_ASC = "temperatureAscending";
    /**
     * 是否进行分组
     */
    public static final String IS_GROUP = "isGroup";
    /**
     * 参数名称
     */
    public static final String PROP_NAME = "propertyName";
    /**
     * 参数单位
     */
    public static final String PROP_UNIT = "propertyUnit";
    /**
     * 步长
     */
    public static final String TIME_LIMIT = "timeLimit";

    public static final String DEVICE_LOG_OFFLINE = "设备下线";
    public static final String DEVICE_LOG_ONLINE = "设备上线";
    /**
     * 报警状态类别-未恢复
     */
    public static final String UN_RESTORE = "unRestore";
    /**
     * 报警状态类别-实时
     */
    public static final String ONLINE = "online";
    /**
     * 报警状态类别-未解除
     */
    public static final String UN_CLEAR = "unClear";

    /**
     * fanyi 盒子ID
     */
    public static final String BOX_ID = "boxId";
    public static final String TOPIC_FY_BASE = "Topic/flexem/fbox/";
    public static final String TOPIC_FY_SET_MSG = "/system/WriteData";
    public static final String TOP_FLUSH_DATA = "/system/MDataPubNow";

    public static final String TOPIC_DATA_UPDATE = "v1/devices/me/data/request/1";

    /**
     * REST调用方法，GET方法
     */
    public static final String REST_METHOD_GET = "get";

    public static final String CLIENT_SCOPE = "CLIENT_SCOPE";
    public static final String SERVER_SCOPE = "SERVER_SCOPE";
    public static final String SHARED_SCOPE = "SHARED_SCOPE";
    public static final String RPC_TO_MQTT = "writeToMqtt";
    public static final String RPC_TO_MODBUS = "writeToModbus";
    public static final String RPC_TO_DTU = "writeToDtu";
    public static final String RPC_TO_MODBUS_TEMPLATE = "writeToTemplate";

    public static final String[] allScopes() {
        return new String[]{CLIENT_SCOPE, SHARED_SCOPE, SERVER_SCOPE};
    }

    public static final String ALARM = "ALARM";
    public static final String ALARM_TIME = "alarmTime";
    public static final String ERROR = "ERROR";
    public static final String LC_EVENT = "LC_EVENT";
    public static final String STATS = "STATS";
    public static final String DEBUG_RULE_NODE = "DEBUG_RULE_NODE";
    public static final String RESTORE = "restore";

    public static final String ONEWAY = "ONEWAY";
    public static final String TWOWAY = "TWOWAY";
    public static final String INFLUX_DEVICE_DATA="device";

    public static final String IN = "IN";
    public static final String OUT = "OUT";
    public static final String TRUE = "true";

    public static final String INACTIVITY_EVENT = "INACTIVITY_EVENT";
    public static final String CONNECT_EVENT = "CONNECT_EVENT";
    public static final String DISCONNECT_EVENT = "DISCONNECT_EVENT";
    public static final String ACTIVITY_EVENT = "ACTIVITY_EVENT";
    public static final String ATTRIBUTE_KEY_RESOURCE_GROUP = "resourceGroup";

    public static final String ENTITY_CREATED = "ENTITY_CREATED";
    public static final String ENTITY_UPDATED = "ENTITY_UPDATED";
    public static final String ENTITY_DELETED = "ENTITY_DELETED";
    public static final String ENTITY_ASSIGNED = "ENTITY_ASSIGNED";
    public static final String ENTITY_UNASSIGNED = "ENTITY_UNASSIGNED";
    public static final String ATTRIBUTES_UPDATED = "ATTRIBUTES_UPDATED";
    public static final String ATTRIBUTES_DELETED = "ATTRIBUTES_DELETED";

    public static final String RPC_CALL_FROM_SERVER_TO_DEVICE = "RPC_CALL_FROM_SERVER_TO_DEVICE";
    public static final String ATTRIBUTE_CHANGE_METER = "changeMeter";
    public static final String ATTRIBUTE_PROP = "prop";
    public static final String ATTRIBUTE_INFO = "info";
    public static final String INPUT_KV = "inputKv";
    public static final String ATTRIBUTE_COST = "成本";
    public static final String VALUE = "value";
    public static final String CREATE_DEVICE_LIMIT = "createDeviceLimit";
    public static final int RESPONSE_CODE_SUCCESS = 200;
    public static final int RESPONSE_CODE_FAILURE = 500;
    public static final int RESPONSE_CODE_PARAMERROR = 405;
    public static final int RESPONSE_NOTHING_DATA = 406;
    public static final String RESPONSE_INFO_LOCAL_SUCCESS = "OK";
    public static final String RESPONSE_INFO_DATABASE_ERROR = "数据库操作失败！";
    public static final String RESPONSE_SECRET_FAILED = "验证秘钥失败!";
    public static final String RESPONSE_PARAMS_ERROR = "参数错误！";
    public static final String RESPONSE_ERROR_UN_KNOW = "未定义的异常";
    public static final String RESPONSE_ERROR_GET_COST = "解析能源费用出现异常，请检查能源费用字段!";
    public static final String RESPONSE_ERROR_COST_ERROR = "获取成本费用失败！";
    public static final String RESPONSE_ERROR_GET_DEVICE_INFO = "获取设备属性失败！";
    public static final String RESPONSE_ERROR_GET_DATA_FALIED = "从OPENTSDB获取数据失败";
    public static final String REQUEST_PARAM_START = "start";
    public static final String REQUEST_PARAM_END = "end";
    public static final String REQUEST_PARAM_ATTRIBUTES = "attributes";
    public static final String REQUEST_PARAM_ATTRIBUTE = "attribute";
    public static final String REQUEST_PARAM_TIME_SHARING_NAME = "timeSharingName";
    public static final String REQUEST_PARAM_TYPE = "type";
    public static final String REQUEST_PARAM_FORMULA = "formula";
    public static final String REQUEST_PARAM_FORMULA_LIST = "formulaList";
    public static final String REQUEST_PARAM_NAME = "name";
    public static final String REQUEST_PARAM_DATA = "data";
    public static final String REQUEST_PARAM_KEY = "key";
    public static final String REQUEST_PARAM_VALUE = "value";
    public static final String REQUEST_PARAM_START_TIME = "startTime";
    public static final String REQUEST_PARAM_PRICE = "price";
    public static final String REQUEST_PARAM_INTERVAL = "interval";
    public static final String REQUEST_PARAM_ASSETS = "assets";
    public static final String REQUEST_PARAM_ENERGY = "energy";
    public static final String REQUEST_PARAM_SECRET = "secret";
    public static final String REQUEST_PARAM_TENANT_ID = "tenantId";
    public static final String REQUEST_PARAM_ENERGY_ALL = "energyAll";
    public static final String REQUEST_PARAM_ASSET_ID = "assetId";
    public static final String REQUEST_PARAM_ASSET_IDS = "assetIds";
    public static final String REQUEST_PARAM_ENERGY_CATAGORY = "energyCatagory";
    public static final String REQUEST_PARAM_ENERGY_DEFINITION = "energyDefinition";
    public static final String REQUEST_PARAM_COAL_RATIO = "coalRatio";
    public static final String REQUEST_PARAM_CARBON_RATIO = "carbonRatio";
    public static final String REQUEST_PARAM_CHILDREN = "children";
    public static final String RESPONSE_ENERGY_DAY_DATA = "本日累计用能";
    public static final String RESPONSE_ENERGY_MONTH_DATA = "本月累计用能";
    public static final String RESPONSE_ENERGY_YEAR_DATA = "本年累计用能";
    public static final String RESPONSE_ENERGY_MONTH_COST_DATA = "本月累计成本";
    public static final String RESPONSE_ENERGY_OPEN_TS_DB_NONE = "none";
    public static final String RESPONSE_ENERGY_PRICE = "energyPrice";
    public static final String PARAMS_SHOW_DATA = "showData";
    public static final String PARAMS_ATTRIBUTE_NAME = "propertyCategory";
    public static final String PARAMS_UNIT = "unit";

    public static final String DEFAULT_GATEWAY = "默认网关";
    public static final String GATEWAY_NAME = "gateWay";
    public static final String GATEWAY_MQTT = "MQTT";
    public static final String DEFAULT_CUSTOMER_USER_ID = "1b21dd2138140008080808080808080";

    public static final String DEFAULT_LOGO = "https://image.istarscloud.com/hanke.png";


    public static final String REQUEST_PARAM_RANGE = "range";
    public static final String REQUEST_PARAM_FROM = "from";
    public static final String REQUEST_PARAM_TO = "to";
    public static final String REQUEST_PARAM_TARGETS = "targets";
    public static final String REQUEST_PARAM_TARGET = "target";
    public static final String REQUEST_PARAM_PROP = "prop";
    public static final String REQUEST_PARAM_REF_ID = "refId";
    public static final String REQUEST_PARAM_ENERGY_TYPE_ASSET = "asset";
    public static final String REQUEST_PARAM_ENERGY_TYPE_DEVICE = "device";
    public static final String REQUEST_PARAM_ENERGY_TYPE_VIRTUAL = "virtual";
    public static final String REQUEST_ALARM_USE_LAST_DATA = "useLastData";
    public static final String REQUEST_ALARM_LAST_DATA = "lastData";
    public static final String REQUEST_DATA_STEPSIZE = "stepSize";
    public static final String REQUEST_DATA_STEPSIZE_DEFAULT = "15m";
    public static final String REQUEST_DATA_STEPSIZE_SECOND = "30s";
    public static final String REQUEST_DATA_STEPSIZE_MINUTE = "1m";
    public static final String REQUEST_DATA_STEPSIZE_FIVE_MINUTE = "5m";
    public static final String REQUEST_DATA_STEPSIZE_TEN_MINUTE = "10m";

    //操作类型
    public static final String OPERATING_TYPE_ASSET_DATA = "查询ASSET数据！";
    public static final String OPERATING_TYPE_ASSET_DATA_AND_PRICE = "查询ASSET数据和成本！";
    public static final String OPERATING_TYPE_ASSET_DATA_TIME_SHARING = "查询ASSET的分时数据！";
    public static final String OPERATING_TYPE_ASSET_DATA_ENERGY_TIME_SHARING = "查询ASSET分时总能耗！";
    public static final String OPERATING_TYPE_ASSET_TYPE = "获取ASSET的类型！";
    public static final String OPERATING_TYPE_ASSET_SAVE = "保存ASSET！";
    public static final String OPERATING_TYPE_ASSET_QUERY = "查询ASSET列表！";
    public static final String OPERATING_TYPE_ASSET_TO_CUSTOMER = "分配ASSET给用户！";
    public static final String OPERATING_TYPE_ASSET_TO_CUSTOMER_RESET = "重置ASSET到用户！";
    public static final String OPERATING_TYPE_ASSET_DEL = "删除ASSET！";
    public static final String OPERATING_TYPE_VIRTUAL_GET = "获取VIRTUAL列表";
    public static final String OPERATING_TYPE_VIRTUAL_DATA = "获取VIRTUAL数据";
    public static final String OPERATING_TYPE_VIRTUAL_ADD = "新增VIRTUAL";
    public static final String OPERATING_TYPE_VIRTUAL_DEL = "删除VIRTUAL";
    public static final String OPERATING_TYPE_FORMULA_DATA = "根据公式获取数据";
    public static final String OPERATING_TYPE_INPUT_KV_ADD = "添加输入值";
    public static final String OPERATING_TYPE_INPUT_KV_GET = "获取输入值";
    public static final String OPERATING_TYPE_INPUT_KV_DEL = "删除输入值";
    public static final String OPERATING_TYPE_DEVICE_REAL_TIME = "获取设备实时数据";
    public static final String OPERATING_TYPE_DEVICE_INFO = "获取设备信息·";
    public static final String OPERATING_TYPE_DEVICE_LIST = "获取设备列表";
    public static final String OPERATING_TYPE_GATEWAY_LIST = "获取网关列表";
    public static final String OPERATING_TYPE_MOUNT_DEVICE = "挂载设备到网关";
    public static final String OPERATING_TYPE_GET_NO_GATEWAY_DEVICE = "获取尚未挂载的设备";
    public static final String OPERATING_TYPE_GET_GATEWAY_DEVICE = "获取网关下挂载的设备";
    public static final String OPERATING_TYPE_DEVICE_ONLINE = "获取设备在线状态";
    public static final String OPERATING_TYPE_DEVICE_CREDENTIALS_ADD = "创建设备秘钥";
    public static final String OPERATING_TYPE_DEVICE_CREDENTIALS_INFO = "获取设备秘钥";
    public static final String OPERATING_TYPE_DEVICE_TYPE = "获取设备类型";
    public static final String OPERATING_TYPE_DEVICE_DATA = "获取设备数据";
    public static final String OPERATING_TYPE_DEVICE_TO_COUSTMER = "绑定设备到用户";
    public static final String OPERATING_TYPE_DEVICE_ADD = "添加设备";
    public static final String OPERATING_TYPE_DEVICE_DEL = "删除设备";
    public static final String OPERATING_TYPE_TENANT_GET = "获取TENANT";
    public static final String OPERATING_TYPE_TENANT_ADD = "新增TENANT";
    public static final String OPERATING_TYPE_TENANT_DEL = "删除TENANT";
    public static final String OPERATING_TYPE_TENANT_ATTRIBUTE = "TENANT添加属性";
    public static final String OPERATING_TYPE_USER_INFO = "获取USER信息";
    public static final String OPERATING_TYPE_USER_LIST = "获取USER列表";
    public static final String OPERATING_TYPE_USER_ADD = "添加USER";
    public static final String OPERATING_TYPE_USER_DEL = "删除USER";
    public static final String OPERATING_TYPE_USER_SEND_EMAIL = "发送USER验证邮件";
    public static final String OPERATING_TYPE_USER_ACTIVE = "激活USER";
    public static final String OPERATING_TYPE_ALARM_INFO = "获取报警列表";
    public static final String OPERATING_TYPE_ALARM_CLEAR = "解除报警";
    public static final String OPERATING_TYPE_LOG_INFO = "查看登陆日志";
    public static final String OPERATING_TYPE_OPTIONS_LOG_INFO = "查看操作日志";
    public static final String OPERATING_TYPE_TELEMETRY_DEL = "删除属性";
    public static final String OPERATING_TYPE_TELEMETRY_ADD = "添加属性";
    public static final String OPERATING_TYPE_ROLE_ADD = "添加角色";
    public static final String OPERATING_TYPE_ROLE_DEL = "删除角色";
    public static final String OPERATING_TYPE_ROLE_MENU_ADD = "为角色分配菜单权限";
    public static final String OPERATING_TYPE_ROLE_USER_ADD = "为用户分配角色权限";
    public static final String OPERATING_TYPE_ROLE_INFO_GET = "获取角色信息";
    public static final String OPERATING_TYPE_MENU_ADD = "添加菜单";
    public static final String OPERATING_TYPE_MENU_DEL = "删除菜单";
    public static final String OPERATING_TYPE_MENU_POOL_ADD = "添加菜单池";
    public static final String OPERATING_TYPE_MENU_POOL_DEL = "删除菜单池";
    public static final String OPERATING_TYPE_ENERGY_ADD = "添加能源";
    public static final String OPERATING_TYPE_ENERGY_GET = "获取能源";
    public static final String OPERATING_TYPE_ENERGY_DEL = "删除能源";
    public static final String OPERATING_TYPE_SCADA_SAVE_URL = "保存/修改组态URL";
    public static final String OPERATING_TYPE_SCADA_QUERY_URL = "查询组态URL";
    public static final String OPERATING_TYPE_SCADA_DELETE_URL = "删除组态URL";
    public static final String OPERATING_TYPE_APP_TYPE_GET = "查询应用信息";
    public static final String OPERATING_TYPE_APP_TYPE_GET_LIST = "查询应用列表";
    public static final String OPERATING_TYPE_APP_TYPE_ADD = "新增应用";
    public static final String OPERATING_TYPE_APP_TYPE_EDIT = "修改应用信息";
    public static final String OPERATING_TYPE_APP_TYPE_DELETE = "删除应用";
    public static final String OPERATING_TYPE_APP_TYPE_RELATION_ADD = "修改应用包含的菜单";
    public static final String OPERATING_TYPE_APP_TYPE_RELATION_GET = "查询应用包含的菜单";
    public static final String OPERATING_TYPE_ALARM_JSON_LIST_GET = "查询报警设置列表";
    public static final String OPERATING_TYPE_AUTH_USER_GET = "查询用户";
    public static final String OPERATING_TYPE_MAINTAIN_GET = "查询维护保养设置";
    public static final String OPERATING_TYPE_MAINTAIN_RECORD_GET = "查询维护保养记录";
    public static final String OPERATING_TYPE_PROJECT_GET = "查询项目信息";
    public static final String OPERATING_TYPE_DEVICE_TEMPLATE_GET = "查询协议模板信息";


    public static final String DATA_BACKUP_AUTO = "AUTO";
    public static final String DATA_BACKUP_NO_AUTO = "NO_AUTO";

    /**
     * 操作日志 Options 类型
     */
    public enum OPERATING_TYPE {
        LOGIN("登录"),

        OPERATING_TYPE_OPTIONS_APP_TYPE_ADD("新增应用"),
        OPERATING_TYPE_OPTIONS_APP_TYPE_EDIT("修改应用"),
        OPERATING_TYPE_OPTIONS_APP_TYPE_DELETE("删除应用"),
        OPERATING_TYPE_OPTIONS_APP_TYPE_EDIT_MENU("编辑应用的菜单"),

        OPERATING_TYPE_OPTIONS_DEVICE_ADD("新增设备"),
        OPERATING_TYPE_OPTIONS_DEVICE_EDIT("修改设备"),
        OPERATING_TYPE_OPTIONS_DEVICE_DELETE("删除设备"),
        OPERATING_TYPE_OPTIONS_DEVICE_MOUNT_TO_GATEWAY("挂载设备到网关"),
        OPERATING_TYPE_OPTIONS_DEVICE_UNMOUNT_TO_GATEWAY("挂载设备到网关"),
        OPERATING_TYPE_OPTIONS_DEVICE_COPY_GATEWAY("复制主机"),
        OPERATING_TYPE_OPTIONS_DEVICE_DELETE_GATEWAY("删除主机（同步删除从机）"),

        OPERATING_TYPE_OPTIONS_PROJECT_ADD("新增项目"),
        OPERATING_TYPE_OPTIONS_PROJECT_EDIT("修改项目"),
        OPERATING_TYPE_OPTIONS_PROJECT_DELETE("删除项目"),
        OPERATING_TYPE_OPTIONS_PROJECT_MOUNT("挂载资源到项目"),

        OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE("新增告警设置"),
        OPERATING_TYPE_OPTIONS_SETTING_ALARM_DELETE("删除告警设置"),

        OPERATING_TYPE_OPTIONS_ALARM_ADD("新增告警"),
        OPERATING_TYPE_OPTIONS_ALARM_EDIT("确认告警"),
        OPERATING_TYPE_OPTIONS_ALARM_CLEAR("清除告警"),
        OPERATING_TYPE_OPTIONS_ALARM_ACK("确认告警"),

        OPERATING_TYPE_OPTIONS_USER_ADD("新增账号"),
        OPERATING_TYPE_OPTIONS_USER_EDIT("修改账号"),
        OPERATING_TYPE_OPTIONS_USER_CHANGE_PASSWORD("修改密码"),
        OPERATING_TYPE_OPTIONS_USER_CHECK_ACTIVE_CODE("校验验证码"),
        OPERATING_TYPE_OPTIONS_USER_ACTIVE("激活账号"),
        OPERATING_TYPE_OPTIONS_USER_DELETE("删除账号"),

        OPERATING_TYPE_OPTIONS_ROLE_ADD("新增角色"),
        OPERATING_TYPE_OPTIONS_ROLE_EDIT("修改角色"),
        OPERATING_TYPE_OPTIONS_ROLE_DELETE("删除角色"),
        OPERATING_TYPE_OPTIONS_ROLE_MENU_ADD("为角色分配菜单权限"),
        OPERATING_TYPE_OPTIONS_ROLE_USER_ADD("为用户分配角色权限"),

        OPERATING_TYPE_OPTIONS_SCADA_SAVE("保存组态"),
        OPERATING_TYPE_OPTIONS_SCADA_EDIT("修改组态"),
        OPERATING_TYPE_OPTIONS_SCADA_DELETE("删除组态"),

        OPERATING_TYPE_OPTIONS_MAINTAIN_ADD("新增维护保养设置"),
        OPERATING_TYPE_OPTIONS_MAINTAIN_EDIT("修改维护保养设置"),
        OPERATING_TYPE_OPTIONS_MAINTAIN_DELETE("删除维护保养设置"),

        OPERATING_TYPE_OPTIONS_SYSTEM_SETTING_ADD("新增企业设置"),
        OPERATING_TYPE_OPTIONS_SYSTEM_SETTING_EDIT("修改企业设置"),
        OPERATING_TYPE_OPTIONS_SYSTEM_SETTING_DELETE("删除企业设置"),

        OPERATING_TYPE_OPTIONS_DATA_ADD("录入数据"),
        OPERATING_TYPE_OPTIONS_DATA_DELETE("删除数据"),
        OPERATING_TYPE_OPTIONS_DATA_BACKUP("数据备份"),

        OPERATING_TYPE_OPTIONS_SOURCE_DATA_SAVE("保存数据源"),
        OPERATING_TYPE_OPTIONS_SOURCE_DATA_DELETE("删除数据源"),
        OPERATING_TYPE_UPDATE_DATA_SOURCE("更新常用数据源设置"),

        OPERATING_TYPE_OPTIONS_TENANT_SAVE("保存企业"),
        OPERATING_TYPE_OPTIONS_TENANT_DELETE("删除企业"),
        OPERATING_TYPE_OPTIONS_TENANT_UPLOAD_LOGO("上传LOGO"),

        OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_ADD("新增设备协议模板"),
        OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_SAVE("保存设备协议模板"),
        OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_COPY("复制设备协议模板"),
        OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_EDIT("修改设备协议模板"),
        OPERATING_TYPE_OPTIONS_TEMPLATE_DEVICE_DELETE("删除设备协议模板"),

        OPERATING_TYPE_OPTIONS_LOGIN("登录"),
        OPERATING_TYPE_OPTIONS_LOGOUT("登出"),
        OPERATING_TYPE_OTHER("其他"),

        OPERATING_TYPE_ADD_EXTRA_USER("更新外部联系人"),
        OPERATING_TYPE_DELETE_EXTRA_USER("删除外部联系人"),

        OPERATING_TYPE_ADD_DASH_CHART("更新组态"),
        OPERATING_TYPE_DELETE_DASH_CHART("删除组态"),
        OPERATING_TYPE_DOWNLOAD_DASH_CHART("导出组态"),
        OPERATING_TYPE_UPLOAD_DASH_CHART("导入组态"),
        ;
        private final String description;

        OPERATING_TYPE(String description) {
            this.description = description;
        }

        public String getDescription() {
            return this.description;
        }
    }

    /**
     * 企业数量
     */
    public static final String TENANT_NUMBERS = "tenantNumbers";

    /**
     * 项目数量
     */
    public static final String PROJECT_NUMBER = "projectNumbers";

    /**
     * 主机数量
     */
    public static final String GATEWAY_NUMBER = "gatewayNumbers";

    /**
     * 从机数量
     */
    public static final String DEVICE_NUMBER = "deviceNumbers";

    /**
     * 用户数量
     */
    public static final String USER_NUMBER = "userNumbers";

    /**
     * 直连设备数量
     */
    public static final String DIRECT_NUMBER = "directNumbers";

    /**
     * 透传设备数量
     */
    public static final String PENETRATE_NUMBER = "penetrateNumbers";

    /**
     * 在线设备数量
     */
    public static final String ONLINE_DEVICE_NUMBER = "onlineDeviceNumbers";

    /**
     * 离线设备数量
     */
    public static final String OFFLINE_DEVICE_NUMBER = "offlineDeviceNumbers";

    /**
     * 在线主机数量
     */
    public static final String ONLINE_GATEWAY_NUMBER = "onlineGateWayNumbers";

    /**
     * 离线主机数量
     */
    public static final String OFFLINE_GATEWAY_NUMBER = "offlineGatewayNumbers";

    /**
     * 报警设备数量
     */
    public static final String ALARM_DEVICE_NUMBER = "alarmNumbers";
    /**
     * 企业下报警总数
     */
    public static final String ALL_ALARM_NUMBER = "allAlarmNumbers";

    /**
     * 模拟输入点数量
     */
    public static final String SIMULATION_IN_NUMBER = "simulationInNumbers";

    /**
     * 模拟输出点数量
     */
    public static final String SIMULATION_OUT_NUMBER = "simulationOutNumbers";

    /**
     * 开关输入点数量
     */
    public static final String BOOLE_IN_NUMBER = "booleInNumber";

    /**
     * 开关输出点数量
     */
    public static final String BOOLE_OUT_NUMBER = "booleOutNumber";

    /**
     * 总数据点数量
     */
    public static final String COLLECTION_NUMBER = "collectionNumbers";


    public static final String MAINTAIN_STATUS_WAIT = "wait";
    public static final String MAINTAIN_STATUS_START = "start";
    public static final String MAINTAIN_STATUS_FINISH = "finish";
    public static final String MAINTAIN_TYPE_ONCE = "once";
    public static final String MAINTAIN_TYPE_CYCLE = "cycle";
    /**
     * new alarm
     */
    public static final String ALARM_PARAMS = "params";
    public static final String ALARM_CHECK_SCRIPTS = "alarmScript";
    public static final String ALARM_RESTORE_SCRIPT = "restoreScript";
    public static final String ALARM_TYPE = "alarmType";
    public static final String ALARM_SEVERITY = "severity";
    public static final String ALARM_PROPAGATE = "propagate";
    public static final String ALARM_RECORDING = "record";
    public static final String ALARM_SEND_SMS = "sms";
    public static final String ALARM_SEND_EMAIL = "email";
    /**
     * 报警类型-周期
     */
    public static final String ALARM_TYPE_CYCLE = "cycle";

    public static final String DEVICE_TYPE_PORT = "MODBUS";
    public static final String DEVICE_TYPE_DTU = "DTU";


    public static final String PROTOCOL_TYPE_MODBUS_RTU = "rtu";
    public static final String PROTOCOL_TYPE_MODBUS_TCP = "tcp";
    public static final String PROTOCOL_TYPE_MODBUS = "MODBUS";

    // 设备列表
    public static final String DATAV_CHARTS_TYPE_DEVICE_LIST = "deviceList";
    // 告警统计表格
    public static final String DATAV_CHARTS_TYPE_ALARM_TABLE = "alarmTable";
    // 告警列表
    public static final String DATAV_CHARTS_TYPE_ALARM_LIST = "alarmList";
    // 饼图
    public static final String DATAV_CHARTS_TYPE_PIE = "pie";
    // 柱状/折线图
    public static final String DATAV_CHARTS_TYPE_LINE_OR_BAR = "lineOrBar";
    // 资产 柱状/折线图
    public static final String DATAV_CHARTS_TYPE_ASSET_LINE_OR_BAR = "assetLineOrBar";

    // 设备数据
    public static final String DATAV_DATA_TYPE_DEVICE = "deviceData";
    // 资产数据
    public static final String DATAV_DATA_TYPE_ASSET = "ASSET";

    public static final String START = "start";
    public static final String END = "end";
    public static final String CONSUMPTION = "consumption";
    //指数运算符
    public static final String INDEX = "\\^";
    public static final String FORMULA_SELF = "A";
    public static final String ALARM_TIMESTAMP = "ts";
    public static final String ALARM_INFO = "info";
    public static final String ALARM_STATUS = "status";

    public static final String IS_DELETE_YES = "1";
    public static final String IS_DELETE_NO = "0";

    public static final String SEND_MSG_SUCCESS = "成功";

    public static final String SEND_MSG_FAILED = "失败";

    public static final String LOGO_URL = "logoUrl";

    // grafana数据类型 资产列表
    public static final String GRAFANA_RESOURCE_ASSET_LIST = "assetList";
    // grafana数据类型 设备列表
    public static final String GRAFANA_RESOURCE_DEVICE_LIST = "deviceList";
    // grafana数据类型 虚拟表列表
    public static final String GRAFANA_RESOURCE_VIRTUAL_LIST = "virtualList";

    // grafana数据属性类型 资产列表
    public static final String GRAFANA_ATTRIBUTES_ASSET_LIST = "assetAttributes";
    // grafana数据属性类型 设备列表
    public static final String GRAFANA_ATTRIBUTES_DEVICE_LIST = "deviceAttributes";
    // grafana数据属性类型 虚拟表列表
    public static final String GRAFANA_ATTRIBUTES_VIRTUAL_LIST = "virtualAttributes";

    public static final String CONSTANTS_DEVICE_TEMPLATE = "deviceTemplate";

    /**
     * 扩展菜单标识
     */
    public static final String IS_EXTENSION_MENU_TRUE = "1";
    public static final String IS_EXTENSION_MENU_FALSE = "0";
    public static final int MENU_POOL_STATUS_IS_EXTENSION_MENU = 5;

    // 企业默认设备数
    public static final int DEFAULT_DEVICE_SIZE = 15;
    /**
     * 默认设备报警详情数
     */
    public static final int DEFAULT_ALARM_CONF = 50;
    //数据上报IP
    public static final String HITSDB_IP = "http://*************";
    public static final String HITSDB_PORT = "4242"; //实例端口
    public static final String PUT_URL = HITSDB_IP + ":" + HITSDB_PORT +
            "/api/put";
    public static final String GET_URL = HITSDB_IP + ":" + HITSDB_PORT +
            "/api/query";

    // public static final String INFLUX_PORT = "8086";
    // public static final String INFLUX_URL = HITSDB_IP + ":" + INFLUX_PORT;

    public static final String DASHBOARD_IP = "http://localhost:8023";
    public static final String DASHBOARD_LIST = "/dashboard-list";
    public static final String DASHBOARD_CREATE = "/dashboards";
    public static final String DASHBOARD__CREAT_LIST = "/dashboard-list";
    public static final String DASHBOARD_GET_JSON = "/dashboards";
    public static final String DATA_TYPE_DASH_CHART = "dashChart";
    public static final String DATA_TYPE_DATA_SOURCE = "dataSource";
    public static final String DATA_TYPE_LOGIC = "logic";
    public static final String DATA_TYPE_MID_DATA_SOURCE = "midDataSource";
    public static final String DATA_TYPE_REST_API_DATA_SOURCE = "apiDataSource";
    public static final String DATA_TYPE_ORIGIN_DATA = "origin";

    public static final String DASHBOARD_SAVE_DATA = "{\"data\":{\"pageConfig\":{\"static\":{\"width\":1280,\"height\":720,\"background\":\"#fff\"},\"dyamic\":\"return {}\",\"style\":{\"width\":1280,\"height\":720,\"background\":\"#fff\"}},\"panels\":{\"panelList\":{\"root\":{\"panelName\":\"root\",\"panelId\":\"root\",\"panelType\":\"root\",\"isDashboardBase\":true,\"dyamic\":\"\"}}},\"singletonPanel\":{},\"dashboardBases\":{\"dashboardBasesList\":{\"root\":[{\"internalPanels\":[],\"pageStyle\":\"return {};\"}]}},\"codeSnippet\":[{\"funcName\":\"queryFunc\",\"func\":\"return {}\",\"id\":\"func1\"}],\"assetsLibrary\":[],\"dependencies\":[],\"cdnJsLibrary\":[]}}";


    public static final String JERRY_EMAIL = "<EMAIL>";
    public static final String JERRY_PHONE = "18408226899";

    // rpc调用结果map
    public static ConcurrentMap<String, Optional> RPC_RESULT_MAP = new ConcurrentHashMap<>();

    // 登录验证码map
    public static ConcurrentMap<String, String> VERIFY_CODE_MAP = new ConcurrentHashMap<>();

    public static ConcurrentMap<String, Integer> LOGIN_TIME_CHECK = new ConcurrentHashMap<>();

    /**
     * 云片网apiKey
     */
    public static final String YUNPIAN_APIKEY = "5a0d88c52f1be43046855b795bfaa9b3";
    /**
     * 云片网设备apiKey
     */
    public static final String YUNPIAN_DEVICE_KEY = "3796980";
    /**
     * 云片网流程apiKey
     */
    public static final String YUNPIAN_MODEL_KEY = "3796982";
    /**
     * 云片网流程apiKey
     */
    public static final String YUNPIAN_CAPTCHA_KEY = "3796976";
    /**
     * 云片网发送短信地址
     */
    public static final String YUNPIAN_SEND_URL = "http://sms.yunpian.com/v2/sms/tpl_single_send.json";

    /**
     * 统计量-求和
     */
    public static final String STATISTICS_DATA_SOURCE_TEMPLATE_SUM = "sum";
    /**
     * 统计量-平均值
     */
    public static final String STATISTICS_DATA_SOURCE_TEMPLATE_SAVERAGE = "average";
    /**
     * 统计量-变动
     */
    public static final String STATISTICS_DATA_SOURCE_TEMPLATE_CHANGE = "change";
    /**
     * 统计量-值类型
     */
    public static final String STATISTICS_DATA_SOURCE_TEMPLATE_VALUE = "value";
    /**
     * 统计量-保持
     */
    public static final String STATISTICS_DATA_SOURCE_KEEP_TIME = "keep";
    /**
     * 统计量-达标次数
     */
    public static final String STATISTICS_DATA_SOURCE_UPSTANDARD = "standard";

    public static final String LOG_TYPE_DEVICE_OFFLINE = "设备离线告警";

    public static final String LOG_TYPE_DEVICE_ALARM = "设备数据告警";

    public static final String LOG_TYPE_DEVICE_REANGE = "设备量程告警";

    public static final String LOG_TYPE_LOGICAL_FLOW_ALARM = "触发器触发报警";

    /**
     * 控制日志-开启控制模式
     */
    public static final String ORERATE_TYPE_START_OPERATE = "startOperate";

    /**
     * 控制日志-结束控制模式
     */
    public static final String ORERATE_TYPE_FINISH_OPERATE = "finishOperate";

    /**
     * 控制日志-下发控制
     */
    public static final String ORERATE_TYPE_OPERATE = "operate";

    //lot 模块相关
    public static final String TENCENT_LOT_CARD_IP = "ic.tencentcloudapi.com";
    public static final String TENCENT_LOT_CARD_ACTION = "DescribeCard";
    public static final String TENCENT_LOT_CARD_VERSION = "2019-03-07";

    //project项目
    public static final String ROOT_PROJECT_PARENT_ID = "0";

    public static final String DEVICE_SOURCE = "DEVICE_SOURCE";
    public static final String MID_SOURCE = "MID_SOURCE";
    public static final String STATISTICS_SOURCE = "STATISTICS_SOURCE";
    public static final String RESTAPI_SOURCE = "RESTAPI_SOURCE";
    public static final String SYSTEM_SOURCE = "SYSTEM_SOURCE";
    public static final String PREPARATION_SOURCE = "PREPARATION_SOURCE";


    /**
     * 可挂载到项目的资源类型
     */
    public enum ProjectRelationEntityType {

        DEVICE("设备"),
        MENU("菜单"),
        SCADA("组态"),
        IPC("工控机"),
        VIRTUAL("虚拟表"),
        //        ENERGY("能源"),
//        ASSET("区域"),
        INPUT_KV("产量录入"),
        USER("用户"),
        ALARM_SETTING("告警设置"),
        ALARM("告警记录"),
        DATA_SOURCE("数据源"),
        LOGICAL_FLOW("逻辑流程"),
        ;


        private final String description;

        ProjectRelationEntityType(final String description) {
            this.description = description;
        }

        public String toString() {
            return description;
        }
    }

    /**
     * logical flow node 节点类型
     */
    public enum LogicalFlowNodeType {
        CONTINUOUS("持续"),
        POLLING("间隔轮询"),
        SIMPLE("普通"),
        ALARM("报警"),
        RUN("执行"),
        WAITING("等待"),
        CONTINUOUS_JUDGMENT("持续判断"),;
        private final String name;
        LogicalFlowNodeType(String name) {
            this.name = name;
        }
        public String getName() {
            return this.name;
        }
    }

    public enum dataSourceVariableType {
        BETWIXT("中间变量", "#"),
        DEVICE("设备变量", "$"),
        ;

        private final String description;
        private final String prefix;

        dataSourceVariableType(String description, String prefix) {
            this.description = description;
            this.prefix = prefix;
        }

        public String getDescription() {
            return this.description;
        }

        public String getPrefix() {
            return this.prefix;
        }
    }

    /**
     * logical flow 触发类型
     */
    public enum TriggerType {
        CLOUD("云端触发"),
        LOCAL("本地触发"),
        ;

        private final String name;

        TriggerType(String name) {
            this.name = name;
        }

        public String getName() {
            return this.name;
        }
    }

    /**
     * GIS一张图 人员类型
     */
    public enum PEOPLE_TYPE {
        XUN_JIAN_REN_YUAN("巡检人员", "XUNJIANRENYUAN"),
        QIANG_XIU_REN_YUAN("抢修人员", "QIANGXIURENYUAN"),
        CHAO_BIAO_YUAN("抄表员", "CHAOBIAOYUAN"),
        ;

        private final String name;
        private final String value;

        PEOPLE_TYPE(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }

        public static PEOPLE_TYPE fromWord(String word) {
            if (word == null) {
                return null;
            }

            if (!word.contains("_")) {
                word = word.charAt(0)
                       + word.substring(1).replaceAll("([A-Z])", "_$1");
            }

            return PEOPLE_TYPE.valueOf(word.toUpperCase());
        }
    }

    /**
     * 树节点类型
     */
    public enum TREE_NODE_TYPE {
        STATION("Station", "站点"),
        PROJECT("Project","项目"),
        ;
        private final String name;
        private final String value;

        TREE_NODE_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表册状态
     */
    public enum METER_BOOK_STATUS {
        CANCELED("1", "已注销"),
        ACTIVE("2","正常"),
        ;
        private final String name;
        private final String value;

        METER_BOOK_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表数据来源
     */
    public enum METER_READ_RECORD_TYPE {
        NEW_METER("1","新建水表"),
        METER_BOTTOM_EDIT("2","档案管理表底修改"),
        INFO_EDIT("3","资料维护表底修改"),
        POINT_CHANGE("4","计量装置变更表底修改"),
        MOBILE_METER_COPY("5","手机抄表"),
        ;
        private final String name;
        private final String value;

        METER_READ_RECORD_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表计划类型
     */
    public enum METER_COPY_EVENT_TYPE {
        PLAN("1", "计划抄表"),
        TEMP("2","临时抄表"),
        ;
        private final String name;
        private final String value;

        METER_COPY_EVENT_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收水费详情类型
     */
    public enum WATER_BILL_DETAIL_TYPE {
        WATER_PRICE("1", "水价清单"),
        SING_PRICE("2","代征价清单"),
        ;
        private final String name;
        private final String value;

        WATER_BILL_DETAIL_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表计划 计划状态
     * 00初始化、01数据准备、02抄表、03水量复核、04水费计算、05水费复核、09发行
     */
    public enum METER_COPY_PLAN_PLAN_STATUS {
        INIT("00", "制定抄表计划"),
        DATA_PREPARE("01","数据准备"),
        METER_COPY("02","抄表"),
        WATER_CHECK("03","水量复核"),
        WATER_BILL_CALCULATE("04","水费计算"),
        WATER_BILL_CHECK("05","水费复核"),
        WATER_BILL_SEND("09","水费发行"),
        WATER_BILL_COMPLETE("10","抄表计划完成"),
        WATER_BILL_END("11","终止"),
        ;
        private final String name;
        private final String value;

        METER_COPY_PLAN_PLAN_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }
    /**
     * 营收抄表计划状态
     * 1、新建
     * 2、执行中
     * 3、执行完毕
     * 4、终止
     */
    public enum METER_COPY_PLAN_STATUS {
        CREATED("1","新建"),
        IN_EXECUTION("2","执行中"),
        COMPLETE("3","执行完成"),
        END("4","终止"),
        ;
        private final String name;
        private final String value;

        METER_COPY_PLAN_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }


    /**
     * DMA流量数据类型
     */
    public enum DMA_TOTAL_FLOW_TYPE {
        HOUR("1","时"),
        DAY("2","日"),
        MONTH("3","月"),
        ;
        private final String name;
        private final String value;

        DMA_TOTAL_FLOW_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }


    /**
     * 营收工作任务状态
     */
    public enum CUST_STATUS {
        DEACTIVATE("4","已停用客户"),
        ACCOUNT_CANCEL("5","已销户客户"),
        NORMAL("1","正常用水用户"),
        ;
        private final String name;
        private final String value;

        CUST_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收工作任务状态
     */
    public enum WORK_TASK_STATUS {
        CREATED("0","待接收"),
        END("7","结束"),
        WAIT_APPROVE("8","待审核"),
        COMPLETE("9","执行完成"),
        ;
        private final String name;
        private final String value;

        WORK_TASK_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收工作任务流转节点状态
     */
    public enum WORK_TASK_DETAIL_STATUS {
        CREATED("0","未开始"),
        IN_EXECUTION("1","执行中"),
        BACK("6","回退"),
        END("7","结束"),
        COMPLETE("9","执行完成"),
        ;
        private final String name;
        private final String value;

        WORK_TASK_DETAIL_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收工业扩报装状态
     */
    public enum EXPANDING_STATUS {
        CREATED("0","待审批"),
        PASS("1","审批通过"),
        REJECT("2","审批未通过"),
        END("3","结束"),
        ;
        private final String name;
        private final String value;

        EXPANDING_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收水务账期
     * 1、生效
     * 2、失效
     */
    public enum WATER_FEE_YM_STATUS {
        ACTIVE("0","生效"),
        DISABLE("1","失效"),
        ;
        private final String name;
        private final String value;

        WATER_FEE_YM_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表状态
     * 1、未抄
     * 2、已抄
     */
    public enum WATER_METER_READ_STATUS {
        NOT_COPY("1","未抄"),
        COPIED("2","已抄"),
        ;
        private final String name;
        private final String value;

        WATER_METER_READ_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表数据发行状态
     * 1、未发行
     * 2、已发行
     */
    public enum WATER_METER_SEND_STATUS {
        UN_SEND("1","未发行"),
        SEND("2","已发行"),
        ;
        private final String name;
        private final String value;

        WATER_METER_SEND_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表数据来源
     */
    public enum WATER_METER_RECORD_TYPE {
        NEW_METER("1","新建表具"),
        METER_COPY("2","抄核抄表"),
        ;
        private final String name;
        private final String value;

        WATER_METER_RECORD_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收抄表方式
     */
    public enum WATER_METER_COPY_TYPE {
        NEW_METER("1","新建表底"),
        METER_COPY("2","手工录入"),
        AUTO_METER_COPY("3","自动抄表"),
        ;
        private final String name;
        private final String value;

        WATER_METER_COPY_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 计量点计算漏损的方式
     */
    public enum POINT_LOST_TYPE {
        DISABLE("1","不计算漏损"),
        RATE("2","定比计算"),
        FIXED("3","定量计算"),
        ;
        private final String name;
        private final String value;

        POINT_LOST_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收应收水费 费用状态
     * 1、非锁定
     * 2、锁定(代扣在途)
     * 3、锁定(走收在途)
     * 4、锁定(托收在途)
     */
    public enum RECEIVABLE_MONEY_STATUS {
        UNLOCK("1","未锁定"),
        LOCK_PROXY("2","锁定(代扣在途)"),
        LOCK_TAKE("3","锁定(走收在途)"),
        LOCK_ENTRUST("4","锁定(托收在途)"),
        ;
        private final String name;
        private final String value;

        RECEIVABLE_MONEY_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收应收水费记录类型
     * 1、抄核应收
     */
    public enum RECEIVABLE_MONEY_TYPE {
        METER_COPY_PLAN("1","抄核应收"),
        RS_RECORD("2","退补水费"),
        ;
        private final String name;
        private final String value;

        RECEIVABLE_MONEY_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收应收水费 结清标识
     * 1、欠费
     * 2、部分结清
     * 3、全部结清
     */
    public enum RECEIVABLE_MONEY_SETTLE_FLAG {
        UNPAID("1","欠费"),
        PARTIAL_PAYMENT("2","部分结清"),
        PAID("3","已结清"),
        ;
        private final String name;
        private final String value;

        RECEIVABLE_MONEY_SETTLE_FLAG(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收实收水费 结算方式
     * 1、现金
     * 2、预收冲抵
     */
    public enum RECEIVED_MONEY_PAY_TYPE {
        CASH("1","现金"),
        PRE_PAY("2","预收冲抵"),
        INSIDE("12","内部往来"),
        ;
        private final String value;
        private final String name;

        RECEIVED_MONEY_PAY_TYPE(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收预收结转类型 0结转  1撤还
     * 1、现金
     * 2、预收冲抵
     */
    public enum CARRY_OVER_TYPE {
        OVER("1","结转"),
        BACK("2","撤还"),
        OVER_BACK("3","结转已撤还"),
        ;
        private final String value;
        private final String name;

        CARRY_OVER_TYPE(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收退补水费类型
     * 1、退补水费
     * 2、全减另发
     */
    public enum RS_RECORD_TYPE {
        REFUND_SUPPLEMENT("1","退补水费"),
        REDUCE_RESEND("2","全减另发"),
        ;
        private final String value;
        private final String name;

        RS_RECORD_TYPE(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收退补水费状态
     * 1、生效
     * 2、未生效
     */
    public enum RS_RECORD_STATUS {
        ACTIVE("1","生效"),
        UN_ACTIVE("2","未生效"),
        ;
        private final String value;
        private final String name;

        RS_RECORD_STATUS(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收退补水费状态
     * 1、未审核
     * 2、审核通过
     * 3、审核拒绝
     */
    public enum RS_RECORD_APPROVAL_STATUS {
        CREATE("1","未审核"),
        PASS("2","审核通过"),
        REFUSE("3","审核拒绝"),
        ;
        private final String value;
        private final String name;

        RS_RECORD_APPROVAL_STATUS(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收退补水费状态
     * 1、未审核
     * 2、审核通过
     * 3、审核拒绝
     */
    public enum CUSTOMER_POINT_CALCULATION_METHOD {
        METER_COPY("1","实抄"),
        FIXED_MONEY("2","定额"),
        FIXED_AMOUNT("3","定量"),
        FIXED_RATE("4","定比"),
        ;
        private final String value;
        private final String name;

        CUSTOMER_POINT_CALCULATION_METHOD(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 营收实收水费 收费方式
     * 1、预收
     * 2、预付费充值
     * 3、预收冲抵
     * 4、收欠费
     */
    public enum RECEIVED_MONEY_CHARGE_TYPE {
        PRE_CHARGE("1","预收"),
        PRE_CHARGE_2("2","预付费充值"),
        PRE_PAY("3","预收冲抵"),
        COLLECT_OWED_FEE("4","收欠费"),
        RS("5","退补水费"),
        ;
        private final String value;
        private final String name;

        RECEIVED_MONEY_CHARGE_TYPE(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 业扩报装类型
     */
    public enum EXPANDING_TYPE {
        MANY_SPECIAL_USER("1", "一户多人口优惠"),
        CHANGE_POINT("2", "改类"),
        CHANGE_NAME("3", "更名"),
        CHANGE_POPULATION("4", "人口数变更"),
        CHANGE_METER("5", "计量装置变更"),
        CHANGE_BASE_INFO("6", "基础信息变更"),
        ADD_CUST_USER("7", "新户报装"),
        DELETE_CUST_USER("8", "销户"),
        BATCH_ADD_CUST_USER("9", "批量立户报装"),
        SPACIAL_USER("10", "优惠用户认证"),
        MOVE_CUST_USER("11", "用户过户"),
        MANY_GRADE_CHANGE_METER("12", "多级改类"),
        CHONGZHENG("13", "冲正流程"),
        ;
        private final String name;
        private final String value;

        EXPANDING_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static EXPANDING_TYPE getByValue(String value) {
            EXPANDING_TYPE[] values = EXPANDING_TYPE.values();
            for (EXPANDING_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 维护类型类型
     */
    public enum INFO_EDIT_TYPE {
        CONTACTS("1", "联系方式修改"),
        READ_BIT("2", "表位数修改"),
        BANK_NUMBER("3", "银行账号修改"),
        CURRENT_READ("4", "表底信息维护"),
        BALANCE("5", "余额调整"),
        CUST_STATUS("6", "用户报停（恢复）"),
        ORG_UNBIND("7", "集团户解绑绑定"),
        ADDITIONAL_PRICE("8", "附加费调整"),
        CUST_BASE_INFO("9", "基础信息维护"),
        COPY_METER_APP_CONTACTS("10", "抄表APP修改联系方式"),
        CUST_REMOVE("11", "销户"),
        CUST_ALL_INFO("12", "用户资料维护"),
        ;
        private final String name;
        private final String value;

        INFO_EDIT_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static INFO_EDIT_TYPE getByValue(String value) {
            INFO_EDIT_TYPE[] values = INFO_EDIT_TYPE.values();
            for (INFO_EDIT_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 逻辑流程根节点 父ID
     */
    public static final String LOGICAL_FLOW_NODE_ROOT_PARENT_ID = "0";

    /**
     * 逻辑流程状态
     */
    public static final String LOGICAL_FLOW_ENABLE = "enable";
    public static final String LOGICAL_FLOW_DISABLE = "disable";

    /**
     * 收费类型
     */
    public enum PREPAY_TYPE {
        CHARGE("10", "收费"),
        CHARGE_WATER("11", "收水费"),
        CHARGE_BUSINESS("12", "收业务费"),
        CHARGE_BOTH("13", "收水费与业务费"),
        CHARGE_PREPAID("14", "预付费充值"),
        CHARGE_PREPAY("15", "预收"),
        CHARGE_PREPAY_OFFSET("16", "预收冲抵"),
        REFUND("20", "退款"),
        REFUND_WATER("21", "退水费"),
        REFUND_BUSINESS("22", "退业务费"),
        REFUND_PREPAY("23", "退预收费"),
        TRANSFER("30", "转账"),
        ;
        private final String name;
        private final String value;

        PREPAY_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PREPAY_TYPE getByValue(String value) {
            PREPAY_TYPE[] values = PREPAY_TYPE.values();
            for (PREPAY_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 缴费类型
     */
    public enum PAY_TYPE {
        WECHAT("1", "微信公众号"),
        ALIPAY_CITY_SERVICE("2", "支付宝城市服务"),
        ALIPAY_LIFE_PREPAY("3", "支付宝生活缴费"),
        BANK_WITHHOLD("4", "银行代扣"),
        BANK_COLLECTION("5", "银行托收"),
        AUTO_PAY_MACHINE("6", "自助缴费机"),
        MOBILE_APP("7", "手机APP"),
        WALK_CHARGE("8", "走收"),
        COUNTER_CHARGE("9", "坐收/柜台收费"),
        WATER_ORGANIZATION("10", "水务机构收费"),
        WECHAT_LIFE_PREPAY("11", "微信生活缴费"),
        INSIDE("12", "内部往来"),
        ;
        private final String name;
        private final String value;

        PAY_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PAY_TYPE getByValue(String value) {
            PAY_TYPE[] values = PAY_TYPE.values();
            for (PAY_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 水流方向
     */
    public enum PARTITION_MOUNT_DIRECTION {
        POSITIVE_IN_NEGATIVE_OUT ("1", "正进负出"),
        NEGATIVE_IN_POSITIVE_OUT("2", "负进正出"),
        IN("3", "入口表"),
        OUT("4", "出口表"),
        ;
        private final String name;
        private final String value;

        PARTITION_MOUNT_DIRECTION(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_MOUNT_DIRECTION getByValue(String value) {
            PARTITION_MOUNT_DIRECTION[] values = PARTITION_MOUNT_DIRECTION.values();
            for (PARTITION_MOUNT_DIRECTION A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }


    /**
     * 挂载设备类型
     */
    public enum PARTITION_MOUNT_DEVICE_TYPE {
        FLOW("1", "流量"),
        PRESSURE("2", "压力"),
        BIGU_SER("3", "大用户"),
        ;
        private final String name;
        private final String value;

        PARTITION_MOUNT_DEVICE_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_MOUNT_DEVICE_TYPE getByValue(String value) {
            PARTITION_MOUNT_DEVICE_TYPE[] values = PARTITION_MOUNT_DEVICE_TYPE.values();
            for (PARTITION_MOUNT_DEVICE_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 分区类型
     */
    public enum PARTITION_TYPE {
        METERING("1", "计量分区"),
        DMA("2", "DMA分区"),
        ;
        private final String name;
        private final String value;

        PARTITION_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_TYPE getByValue(String value) {
            PARTITION_TYPE[] values = PARTITION_TYPE.values();
            for (PARTITION_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 分区用户类型
     */
    public enum PARTITION_USER_TYPE {
        BUSINESS_CUST("1", "商居混合"),
        CUST("2", "居民区"),
        BUSINESS("3", "商业区"),
        ;
        private final String name;
        private final String value;

        PARTITION_USER_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_USER_TYPE getByValue(String value) {
            PARTITION_USER_TYPE[] values = PARTITION_USER_TYPE.values();
            for (PARTITION_USER_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 分区状态 1规划中 2建制中 3评估中 4检修漏 5营运中
     */
    public enum PARTITION_STATUS {
        PLAN("1", "规划中"),
        BUILD("2", "建制中"),
        EVALUATION("3", "评估中"),
        REPAIR("4", "检修漏"),
        RUN("5", "营运中"),
        ;
        private final String name;
        private final String value;

        PARTITION_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_STATUS getByValue(String value) {
            PARTITION_STATUS[] values = PARTITION_STATUS.values();
            for (PARTITION_STATUS A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 分区流量表信息 类型 1考核总表  2大用户表 3消防表  4绿化表
     */
    public enum PARTITION_FLOW_METER_TYPE {
        KAOHE("1", "考核总表"),
        DAYONGHU("2", "大用户表"),
        XIAOFANG("3", "消防表"),
        LVHUA("4", "绿化表"),
        ;
        private final String name;
        private final String value;

        PARTITION_FLOW_METER_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_FLOW_METER_TYPE getByValue(String value) {
            PARTITION_FLOW_METER_TYPE[] values = PARTITION_FLOW_METER_TYPE.values();
            for (PARTITION_FLOW_METER_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 报表工具数据库类型 1 PostgreSql 2 SQLServer 3 MySQL 4 Oracle
     */
    public enum REPORT_DATABASE_TYPE {
        POSTGRESQL("1", "POSTGRESQL"),
        SQLSERVER("2", "SQLSERVER"),
        MYSQL("3", "MYSQL"),
        ORACLE("4", "ORACLE"),
        ;
        private final String name;
        private final String value;

        REPORT_DATABASE_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static REPORT_DATABASE_TYPE getByValue(String value) {
            REPORT_DATABASE_TYPE[] values = REPORT_DATABASE_TYPE.values();
            for (REPORT_DATABASE_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 报表工具组件数据类型 1 静态数据 2 API接口 3 数据库数据
     */
    public enum REPORT_COMPONENT_DATA_TYPE {
        STATIC("1", "静态数据"),
        API("2", "API数据"),
        DATABASE("3", "数据库数据"),
        ;
        private final String name;
        private final String value;

        REPORT_COMPONENT_DATA_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static REPORT_COMPONENT_DATA_TYPE getByValue(String value) {
            REPORT_COMPONENT_DATA_TYPE[] values = REPORT_COMPONENT_DATA_TYPE.values();
            for (REPORT_COMPONENT_DATA_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 分区阀门信息类型  1边界阀门  2分支管线阀门
     */
    public enum PARTITION_VALUE_TYPE {
        BIANJIE("1", "边界阀门"),
        FENZHI("2", "分支管线阀门"),
        ;
        private final String name;
        private final String value;

        PARTITION_VALUE_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public static PARTITION_VALUE_TYPE getByValue(String value) {
            PARTITION_VALUE_TYPE[] values = PARTITION_VALUE_TYPE.values();
            for (PARTITION_VALUE_TYPE A : values) {
                if (A.getValue().equals(value)) {
                    return A;
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }


    /**
     * Redis获取id的key
     */
    public static final String REDIS_ID_KEY_EXPANDING = "workTask";
    public static final String REDIS_ID_KEY_EDIT_INFO = "editInfo";
    public static final String REDIS_ID_KEY_METER_COPY = "meterCopy";

    public enum REDIS_KEY {
        EXPANDING("workTask", "工作单"),
        EDIT_INFO("editInfo", "信息维护"),
        METER_COPY("meterCopy", "抄表"),
        RECEIVABLE_MONEY("receivable_money", "应收水费"),
        RS_RECORD("rs_record", "退补水费记录"),
        PARTITION("partition", "分区编号"),
        PREPAY("prepay", "充值"),
        CUST_CODE("custCode", "用户编号"),
        REMOTE_BRAND_APP_ID("appId", "远传表厂商AppID"),
        SYSTEM_NOTIFY("system_notify", "系统消息通知"),
        GAN_INSTALL_CODE("ganInstall", "赣服通报装"),
        GAN_REPAIR_CODE("ganRepair", "赣服通报修"),
        YIXIAN_MSG("yixianMsg", "义县短信发送"),
        INSTALL_WECHAT("install_WECHAT", "微信报装"),
        PIPE_COLLECT("pipe_collect", "管网采集"),
        WORK_ORDER_SERVICE("work_order_service", "话务工单"),
        NOTIFY_TYPE("notify_type", "消息类型"),
        PRODUCTION_TASK("production_task", "安全生产"),
        PRODUCTION_HIDDEN_DANGER("production_hidden_danger", "安全隐患"),
        WORK_ORDER("work_order", "工单"),
        ;
        private final String name;
        private final String value;

        REDIS_KEY(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }



    /**
     * 报警V2 报警状态
     */
    public enum ALARMV2_ALARM_STATUS {
        NEW("1", "报警中"),
        RESTORE("2", "已恢复"),
        CLEARED("3", "已解除"),
        ;
        private final String name;
        private final String value;

        ALARMV2_ALARM_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 报警V2 处理状态
     */
    public enum ALARMV2_PROCESS_STATUS {
        NEW("1", "未处理"),
        PROCESSING("2", "处理中"),
        COMPLETE("3", "已处理"),
        ;
        private final String name;
        private final String value;

        ALARMV2_PROCESS_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 站点养护任务状态
     */
    public enum STATION_CIRCUIT_TASK_STATUS {
        NEW("未开始"),
        PROCESSING("处理中"),
        PROCESS_COMPLETE("处理完成"),
        AUDIT_FAIL("审核未完成"),
        COMPLETE("按时完成"),
        TIMEOUT_COMPLETE("超时完成"),
        ;
        private final String value;

        STATION_CIRCUIT_TASK_STATUS(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 系统消息通知状态
     */
    public enum SYSTEM_NOTIFY_STATUS {
        UN_READ("0", "未读"),
        HAVE_READ("1", "已读"),
        ;
        private final String name;
        private final String value;

        SYSTEM_NOTIFY_STATUS(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 系统消息通知类型
     */
    public enum SYSTEM_NOTIFY_TYPE {
        NOTIFY("0", "通知"),
        ALARM("1", "报警"),
        PENDING("2", "待办"),
        ;
        private final String name;
        private final String value;

        SYSTEM_NOTIFY_TYPE(String value, String name) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 系统消息通知主题
     */
    public enum SYSTEM_NOTIFY_TOPIC {
        REVENUE_REPAIR("报修任务", "有新的报修任务, 请及时处理!", "REVENUE_REPAIR"),
        WORK_ORDER_DISPATCH("工单平台 | 工单分派", "您有一条待分派的工单, 请及时分派!", "WORK_ORDER"),
        WORK_ORDER_PENDING("工单平台 | 工单待处理", "您有一条工单待处理, 请及时处理!", "WORK_ORDER"),
        ALARM_CENTER("报警中心 | 异常告警", "站点触发异常告警, 请及时处理!", "ALARM_CENTER"),
        ;
        private final String value;
        private final String msg;
        private final String type;

        SYSTEM_NOTIFY_TOPIC(String value, String msg, String type) {
            this.value = value;
            this.msg = msg;
            this.type = type;
        }

        public String getValue() {
            return value;
        }

        public String getMsg() {
            return msg;
        }

        public String getType() {
            return type;
        }
    }

    /**
     * 正在执行中的流程节点MAP
     */
    public static final ConcurrentHashMap<String, Boolean> runningNode = new ConcurrentHashMap<>();

    /**
     * 正在执行中的流程节点MAP
     */
    public static final ConcurrentHashMap<String, Boolean> continuousRunningNode = new ConcurrentHashMap<>();

    /**
     * 逻辑流程相关redis key
     */
    public static final String LOGICAL_FLOW_CONTINUOUS_RUNNING_NODE_KEY = "logical_flow:continuousRunningNode";
    public static final String LOGICAL_FLOW_RUNNING_NODE_KEY = "logical_flow:runningNode";

    public static ConcurrentHashMap<String, Integer> requestIdMap = new ConcurrentHashMap<>();


}
