package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_project_c")
@Data
public class ProjectC {
    @TableId
    private String id;

    private String mainId;

    private String title;

    private String content;

    private String formData;

    private Integer orderNum;

    private String users;

    private String type;

    private String status;

    private String tenantId;

    private Date startTime;

    private Date endTime;

    private String operator;

    private transient String operatorName;

}
