<template>
  <div
    class="error-pop-table"
    :class="[props.config?.row ? 'editable' : '']"
  >
    <div
      class="content-box"
      :class="[config?.hideFooter ? 'table-full' : '']"
    >
      <div class="table-box">
        <FormTable :config="TableConfig"></FormTable>
      </div>
      <div class="extrainfo">
        <div
          v-if="props.config?.row"
          class="extrainfo-item"
        >
          <label for="status"> 当前状态： </label>
          <Tag :color="tagColors[props.config?.row?.status]">
            {{ reportType[props.config?.row?.status] || '待处理' }}
          </Tag>
        </div>
        <div class="extrainfo-item">
          <label for="remark"> 现场图片： </label>
          <el-image
            v-if="props.config?.row"
            class="img-box"
            :src="props.config?.img"
            :preview-src-list="[props.config?.img]"
            :initial-index="0"
            :preview-teleported="true"
            :fit="'contain'"
            :hide-on-click-modal="true"
          >
            <template #error>
              <div class="image-slot">
                <span>暂无图片</span>
              </div>
            </template>
          </el-image>
          <SLUploader
            v-else
            v-model="imgUrl"
            :limit="1"
            :url="useAppStore().actionUrl + 'file/api/upload/file'"
          >
          </SLUploader>
        </div>
        <div class="extrainfo-item remark">
          <label for="remark"> 备注信息： </label>
          <span
            v-if="props.config?.row"
            id="remark"
          >{{ props.config?.row?.remark }}</span>
          <el-input
            v-else
            id="remark"
            v-model="remark"
          ></el-input>
        </div>
      </div>
    </div>
    <div
      v-if="!config?.hideFooter"
      class="table-footer"
    >
      <el-button
        type="primary"
        @click="emit('report', TableConfig.dataList, config, { img: imgUrl, remark: remark })"
      >
        上报
      </el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store'
import { reportType, tagColors } from '../data'

const emit = defineEmits(['report'])
const props = defineProps<{
  config?: Record<string, any>
}>()
const imgUrl = ref<string>('')
const remark = ref<string>('')
const TableConfig = computed<ITable>(() => {
  return {
    pagination: { hide: true },
    dataList: props.config?.dataList.filter(item => item.name !== 'img') || [],
    spanMethod: params => {
      const rowspan = 1
      let colspan = 1
      if (params.row.alias === '上报备注') {
        colspan = params.columnIndex === 1 ? 2 : params.columnIndex === 2 ? 0 : 1
      }
      return { rowspan, colspan }
    },
    columns: [
      { label: '属性', prop: 'alias' },
      { label: '原始值', prop: 'oldvalue' },
      {
        label: '上报值',
        prop: 'newvalue',
        formItemConfig: {
          type: 'input',
          placeholder: (value, row): string => (row.editable ? '请输入上报值' : ' '),
          disabled: (value, row): boolean => !row.editable
        }
      }
    ]
  }
})
watch(
  () => props.config,
  () => {
    imgUrl.value = ''
    remark.value = ''
  }
)
</script>
<style lang="scss" scoped>
.error-pop-table {
  width: 100%;
  height: 100%;
  &.editable {
    .table-full {
      height: 100%;
    }
  }
  .content-box {
    height: calc(100% - 50px);
  }
  .table-box {
    min-height: 300px;
    height: calc(100% - 370px);
  }
  .table-full {
    height: 100%;
  }
  .extrainfo {
    padding: 12px 0;
  }
  .table-footer {
    height: 50px;
    align-items: center;
    display: flex;
    justify-content: flex-end;
  }
  .extrainfo-item {
    margin-bottom: 12px;
    line-height: 1.5;
    &.remark {
      height: 68px;
      span {
        word-break: break-all;
      }
    }
  }
  .img-box {
    width: 100%;
    height: 160px;
  }
  .image-slot {
    color: var(--el-text-color-placeholder);
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .extrainfo-item {
    font-size: 14px;
  }
  #remark {
    color: var(--el-text-color-regular);
  }
}
</style>
