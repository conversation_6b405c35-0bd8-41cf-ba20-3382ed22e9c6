// 水质分布相关类型定义

// 水质监测站点信息
export interface WaterQualityStation {
  id: string
  stationId: string
  name: string
  stationName?: string
  location: string // 经纬度，格式：'longitude,latitude'
  longitude?: number
  latitude?: number
  status: number // 1: 正常, 0: 异常
  projectId?: string
  projectName?: string
  stationType?: string
  createTime?: string
  updateTime?: string
}

// 水质指标数据
export interface WaterQualityIndicator {
  id?: string
  stationId: string
  propertyName: string // 指标名称，如：COD、BOD5、氨氮等
  propertyCode?: string // 指标代码
  value: number // 指标值
  unit: string // 单位
  standardValue?: number // 标准值
  status?: boolean | null // 达标状态：true-达标，false-超标，null-未知
  testTime?: string // 检测时间
  sampleTime?: string // 采样时间
}

// 水质监测站点详细信息
export interface WaterQualityStationDetail extends WaterQualityStation {
  indicators: WaterQualityIndicator[] // 水质指标列表
  todayData?: WaterQualityIndicator[] // 今日数据
  yesterdayData?: WaterQualityIndicator[] // 昨日数据
  monthData?: WaterQualityIndicator[] // 本月数据
  lastUpdateTime?: string // 最后更新时间
}

// 查询参数
export interface WaterQualityQueryParams {
  projectId?: string
  stationId?: string
  stationIds?: string
  name?: string // 站点名称模糊查询
  status?: string // 状态筛选
  startTime?: string
  endTime?: string
  dateRange?: string[]
  queryType?: 'hour' | 'day' | 'month' | 'year'
  pageNum?: number
  pageSize?: number
}

// 水质统计数据
export interface WaterQualityStatistics {
  totalStations: number // 总站点数
  normalStations: number // 正常站点数
  abnormalStations: number // 异常站点数
  offlineStations: number // 离线站点数
  complianceRate: number // 达标率
  lastUpdateTime: string // 最后更新时间
}

// 地图弹窗数据
export interface MapPopupData {
  id: string
  title: string
  visible: boolean
  x: number
  y: number
  offsetX?: number
  offsetY?: number
  attributes: {
    values: Array<{
      label: string
      value: string | number
      unit?: string
      status?: string
    }>
    id: string
  }
}

// API响应数据结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success?: boolean
}

// 分页响应数据
export interface PageResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// 图表数据配置
export interface ChartOption {
  title?: any
  tooltip?: any
  legend?: any
  xAxis?: any
  yAxis?: any
  series?: any[]
  grid?: any
  color?: string[]
}

// 水质指标配置
export interface WaterQualityIndicatorConfig {
  code: string
  name: string
  unit: string
  standardValue?: number
  normalRange?: [number, number]
  color?: string
  description?: string
}

// 常用水质指标配置
export const WATER_QUALITY_INDICATORS: WaterQualityIndicatorConfig[] = [
  { code: 'cod', name: 'COD', unit: 'mg/L', standardValue: 50, color: '#5470C6' },
  { code: 'bod5', name: 'BOD5', unit: 'mg/L', standardValue: 10, color: '#91CC75' },
  { code: 'nh3n', name: '氨氮', unit: 'mg/L', standardValue: 5, color: '#FAC858' },
  { code: 'tn', name: '总氮', unit: 'mg/L', standardValue: 15, color: '#EE6666' },
  { code: 'tp', name: '总磷', unit: 'mg/L', standardValue: 0.5, color: '#73C0DE' },
  { code: 'ph', name: 'pH值', unit: '', normalRange: [6.5, 8.5], color: '#3BA272' },
  { code: 'ss', name: '悬浮物', unit: 'mg/L', standardValue: 10, color: '#FC8452' },
  { code: 'turbidity', name: '浊度', unit: 'NTU', standardValue: 1, color: '#9A60B4' }
]
