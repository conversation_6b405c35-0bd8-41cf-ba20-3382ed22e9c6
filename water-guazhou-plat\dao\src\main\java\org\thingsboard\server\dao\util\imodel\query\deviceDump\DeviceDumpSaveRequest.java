package org.thingsboard.server.dao.util.imodel.query.deviceDump;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDump;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DeviceDumpSaveRequest extends ComplexSaveRequest<DeviceDump, DeviceDumpDetailSaveRequest> {
    // 报废单编码
    @NotNullOrEmpty
    private String code;

    // 报废单标题
    @NotNullOrEmpty
    private String name;

    // 报废申请上报时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;

    // 仓库ID
    @NotNullOrEmpty
    private String storehouseId;

    // 保费申请人
    @NotNullOrEmpty
    private String uploadUserId;

    // 经办人
    @NotNullOrEmpty
    private String handleUserId;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        return checkItemExistence("报废单应至少报废一个设备");
    }

    @Override
    protected DeviceDump build() {
        DeviceDump entity = new DeviceDump();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected DeviceDump update(String id) {
        disallowUpdate();
        DeviceDump entity = new DeviceDump();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(DeviceDump entity) {
        entity.setCode(code);
        entity.setName(name);
        entity.setUploadTime(uploadTime);
        entity.setStorehouseId(storehouseId);
        entity.setUploadUserId(uploadUserId);
        entity.setHandleUserId(handleUserId);
        entity.setRemark(remark);
    }

    @Override
    protected StringSetter<DeviceDumpDetailSaveRequest> parentSetter() {
        return DeviceDumpDetailSaveRequest::setMainId;
    }
}