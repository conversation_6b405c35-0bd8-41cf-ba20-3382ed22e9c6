<template>
  <ElConfigProvider :locale="zhCn">
    <router-view></router-view>
  </ElConfigProvider>
</template>

<script lang="ts" setup>
import { ElConfigProvider } from 'element-plus';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import { useAppStore } from './store';
const appStore = useAppStore();
const checkMediaTheme = () => {
  const saved = localStorage.getItem('vueuse-color-scheme');
  if (
    saved === 'auto'
      ? window.matchMedia(`(prefers-color-scheme: dark)`).matches
      : saved === 'dark'
  ) {
    document.documentElement.classList.add('dark');
    appStore.SET_isDark(true);
  } else {
    appStore.SET_isDark(false);
  }
};
const isDark = useDark();

const toggleDark = useToggle(isDark);
watch(
  () => appStore.isDark,
  (value) => {
    toggleDark(value);
  }
);
onMounted(() => {
  checkMediaTheme();
  toggleDark(appStore.isDark);
});
</script>

<style lang="scss">
.el-message,
.el-message--success {
  top: 100px !important;
}

.symbolIcon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

p {
  margin: 0;
}
</style>
