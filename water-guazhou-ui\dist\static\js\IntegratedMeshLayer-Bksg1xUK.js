import{e as t,y as a,m as $,a as f,W as O,a0 as x,Y as L,M as R,Z as N,s as d,o as b}from"./Point-WxyopZva.js";import{a5 as M,aY as S,R as I,T as A}from"./index-r0dFAfgr.js";import{v as w,as as J,eP as U,bk as V,eg as j,Q as D,R as P,t as K,V as E,ef as k,eQ as z,dl as G,e as Q}from"./MapView-DaoQedLH.js";import{a as Y,U as q}from"./widget-BcWKanF2.js";import{g as m}from"./persistable-CIG2ELSD.js";import{E as C,L as v}from"./SceneService-C2M-OzUU.js";import{s as F,l as W,u as Z,m as B}from"./I3SLayerDefinitions-DSReL3XN.js";import{L as H,U as X}from"./pe-B8dP0-Ut.js";import"./multiOriginJSONSupportUtils-C0wm8_Yw.js";import"./resourceExtension-DfSw5lpL.js";import"./originUtils-DOOsZebp.js";import"./resourceUtils-DVO9IyPB.js";var h;let n=h=class extends O{constructor(e){super(e),this.geometry=null,this.type="clip"}writeGeometry(e,i,s,o){if(o.layer&&o.layer.spatialReference&&!o.layer.spatialReference.equals(this.geometry.spatialReference)){if(!J(e.spatialReference,o.layer.spatialReference))return void(o&&o.messages&&o.messages.push(new x("scenemodification:unsupported","Scene modifications with incompatible spatial references are not supported",{modification:this,spatialReference:o.layer.spatialReference,context:o})));const l=new w;U(e,l,o.layer.spatialReference),i[s]=l.toJSON(o)}else i[s]=e.toJSON(o);delete i[s].spatialReference}clone(){return new h({geometry:M(this.geometry),type:this.type})}};t([a({type:w}),m()],n.prototype,"geometry",void 0),t([$(["web-scene","portal-item"],"geometry")],n.prototype,"writeGeometry",null),t([a({type:["clip","mask","replace"],nonNullable:!0}),m()],n.prototype,"type",void 0),n=h=t([f("esri.layers.support.SceneModification")],n);const c=n;var p;let y=p=class extends L(V.ofType(c)){constructor(e){super(e),this.url=null}clone(){return new p({url:this.url,items:this.items.map(e=>e.clone())})}toJSON(e){return this.toArray().map(i=>i.toJSON(e)).filter(i=>!!i.geometry)}static fromJSON(e,i){const s=new p;for(const o of e)s.add(c.fromJSON(o,i));return s}static async fromUrl(e,i,s){const o={url:H(e),origin:"service"},l=await X(e,{responseType:"json",signal:S(s,"signal")}),_=i.toJSON(),u=[];for(const g of l.data)u.push(c.fromJSON({...g,geometry:{...g.geometry,spatialReference:_}},o));return new p({url:e,items:u})}};t([a({type:String})],y.prototype,"url",void 0),y=p=t([f("esri.layers.support.SceneModifications")],y);const T=y;let r=class extends C(j(D(P(K(E(k(Q))))))){constructor(...e){super(...e),this._handles=new R,this.geometryType="mesh",this.operationalLayerType="IntegratedMeshLayer",this.type="integrated-mesh",this.nodePages=null,this.materialDefinitions=null,this.textureSetDefinitions=null,this.geometryDefinitions=null,this.serviceUpdateTimeStamp=null,this.profile="mesh-pyramids",this.modifications=null,this._modificationsSource=null,this.elevationInfo=null,this.path=null}destroy(){this._handles.destroy()}initialize(){this._handles.add(Y(()=>this.modifications,"after-changes",()=>this.modifications=this.modifications,q))}normalizeCtorArgs(e,i){return typeof e=="string"?{url:e,...i}:e}readModifications(e,i,s){this._modificationsSource={url:z(e,s),context:s}}async load(e){return this.addResolvingPromise(this._doLoad(e)),this}async _doLoad(e){const i=S(e,"signal");try{await this.loadFromPortal({supportedTypes:["Scene Service"]},e)}catch(s){N(s)}if(await this._fetchService(i),I(this._modificationsSource)){const s=await T.fromUrl(this._modificationsSource.url,this.spatialReference,e);this.setAtOrigin("modifications",s,this._modificationsSource.context.origin),this._modificationsSource=null}await this._fetchIndexAndUpdateExtent(this.nodePages,i)}beforeSave(){if(!A(this._modificationsSource))return this.load().then(()=>{},()=>{})}async saveAs(e,i){return this._debouncedSaveOperations(v.SAVE_AS,{...i,getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"integrated-mesh"},e)}async save(){const e={getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"integrated-mesh"};return this._debouncedSaveOperations(v.SAVE,e)}validateLayer(e){if(e.layerType&&e.layerType!=="IntegratedMesh")throw new d("integrated-mesh-layer:layer-type-not-supported","IntegratedMeshLayer does not support this layer type",{layerType:e.layerType});if(isNaN(this.version.major)||isNaN(this.version.minor))throw new d("layer:service-version-not-supported","Service version is not supported.",{serviceVersion:this.version.versionString,supportedVersions:"1.x"});if(this.version.major>1)throw new d("layer:service-version-too-new","Service version is too new.",{serviceVersion:this.version.versionString,supportedVersions:"1.x"})}_getTypeKeywords(){return["IntegratedMeshLayer"]}};t([a({type:String,readOnly:!0})],r.prototype,"geometryType",void 0),t([a({type:["show","hide"]})],r.prototype,"listMode",void 0),t([a({type:["IntegratedMeshLayer"]})],r.prototype,"operationalLayerType",void 0),t([a({json:{read:!1},readOnly:!0})],r.prototype,"type",void 0),t([a({type:F,readOnly:!0})],r.prototype,"nodePages",void 0),t([a({type:[W],readOnly:!0})],r.prototype,"materialDefinitions",void 0),t([a({type:[Z],readOnly:!0})],r.prototype,"textureSetDefinitions",void 0),t([a({type:[B],readOnly:!0})],r.prototype,"geometryDefinitions",void 0),t([a({readOnly:!0})],r.prototype,"serviceUpdateTimeStamp",void 0),t([a({type:T}),m({origins:["web-scene","portal-item"],type:"resource",prefix:"modifications"})],r.prototype,"modifications",void 0),t([b(["web-scene","portal-item"],"modifications")],r.prototype,"readModifications",null),t([a(G)],r.prototype,"elevationInfo",void 0),t([a({type:String,json:{origins:{"web-scene":{read:!0,write:!0},"portal-item":{read:!0,write:!0}},read:!1}})],r.prototype,"path",void 0),r=t([f("esri.layers.IntegratedMeshLayer")],r);const ce=r;export{ce as default};
