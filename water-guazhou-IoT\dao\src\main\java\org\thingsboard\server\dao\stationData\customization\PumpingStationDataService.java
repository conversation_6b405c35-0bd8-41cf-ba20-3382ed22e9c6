package org.thingsboard.server.dao.stationData.customization;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.AssayRecordFeignClient;
import org.thingsboard.server.dao.client.DeviceFeignClient;
import org.thingsboard.server.dao.client.MedicineManageFeignClient;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.stationData.StationDataService;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class PumpingStationDataService {

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Autowired
    private MedicineManageFeignClient medicineManageFeignClient;

    @Autowired
    private AssayRecordFeignClient assayRecordFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;


    /**
     * 泵房统计
     */
    public Object pumpingStationCount(String projectId, TenantId tenantId) {
        // 查询指定类型的站点列表
        PageData<StationEntity> stationEntityPageData = stationFeignClient.list(1, 99999, "泵站", projectId);
        List<StationEntity> stationList = stationEntityPageData.getData();

        if (stationList == null) {
            return new ArrayList<>();
        }

        // 统计数量
        int total = 0;


        BigDecimal supplyWater = new BigDecimal("0");

        for (StationEntity station : stationList) {
            total++;
        }
        int ok = total;
        int warn = 0;
        int stop = 0;

        JSONObject result = new JSONObject();
        result.put("total", total);
        result.put("ok", ok);
        result.put("warn", warn);
        result.put("stop", stop);

        return result;
    }

    /**
     * 压力合格率
     */
    public Object pressurePassRate(String projectId, TenantId tenantId) {
        // 查询指定项目下的泵站列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 999999, "泵站", projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return null;
        }

        // 查询数据
        Map<String, StationEntity> keyMap = new HashMap<>();
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();

        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            // 压力数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("pressure".equals(stationAttr.getAttr())) {// 压力
                        keyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                        stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
                    }
                }
            }
        }

        // 查询压力数据
        List<String> attrs = new ArrayList<>(keyMap.keySet());

        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date startTime = instance.getTime();

        // 执行查询
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> pressureDataMap
                = obtainDataService.getDeviceOriginalData(attrs, startTime.getTime(), now.getTime(), null, tenantId);

        // 统计数据
        int total = 0;
        int pass = 0;
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : pressureDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> entryValue = entry.getValue();
            if (entryValue != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : entryValue.entrySet()) {
                    total++;

                    String attr = dataEntry.getKey();
                    BigDecimal value = dataEntry.getValue();

                    StationAttrEntity stationAttr = stationAttrMap.get(attr);
                    if (stationAttr == null) {
                        continue;
                    }

                    String range = stationAttr.getRange();
                    if (StringUtils.isBlank(range)) {
                        continue;
                    }

                    String[] rangeArray = range.split(",");

                    // 校验是否在范围内
                    if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                        if (Double.parseDouble(rangeArray[0]) < value.doubleValue() && Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                            pass++;
                        }
                    }
                    if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isBlank(rangeArray[1])) {
                        if (Double.parseDouble(rangeArray[0]) < value.doubleValue()) {
                            pass++;
                        }
                    }
                    if (StringUtils.isBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                        if (Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                            pass++;
                        }
                    }
                }
            }
        }

        // 计算合格率
        JSONObject result = new JSONObject();
        result.put("total", total);
        result.put("pass", pass);
        if (total != 0) {
            result.put("passRate", BigDecimal.valueOf(pass).divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_DOWN).multiply(new BigDecimal("100")));
        }

        return result;
    }

    /**
     * 统计指定项目下的水泵供水量以及能耗情况
     */
    public Object energyInAndWaterSupply(String projectId, TenantId tenantId) throws ThingsboardException {
        // 查询指定项目下的泵站列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 999999, "泵站", projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return null;
        }

        // 查询数据
        Map<String, StationEntity> energyInKeyMap = new HashMap<>();
        Map<String, StationEntity> totalFlowKeyMap = new HashMap<>();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            // 有功电能数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("ENERGY_IN".equals(stationAttr.getAttr())) {// 能耗
                        energyInKeyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
            // 累计流量数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("total_flow".equals(stationAttr.getAttr())) {// 累计流量
                        totalFlowKeyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
        }
        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, 0);
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date yearStart = instance.getTime();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");

        // 查询今年的能耗数据
        List<String> energyInAttrs = new ArrayList<>(energyInKeyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> energyInDataMap
                = obtainDataService.getDeviceData(energyInAttrs, yearStart.getTime(), now.getTime(), "month", null, tenantId);

        // 统计年数据
        BigDecimal yearEnergyInTotal = new BigDecimal("0");
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : energyInDataMap.entrySet()) {
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null && !dataMap.isEmpty()) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    BigDecimal value = dataEntry.getValue();
                    if (value != null) {
                        yearEnergyInTotal = yearEnergyInTotal.add(value);
                    }
                }
            }
        }

        // 统计月数据
        LinkedHashMap<String, BigDecimal> energyInMonthDataMap = energyInDataMap.get(dateFormat.format(now));
        BigDecimal monthEnergyInTotal = new BigDecimal("0");
        if (energyInMonthDataMap != null && !energyInMonthDataMap.isEmpty()) {
            for (Map.Entry<String, BigDecimal> entry : energyInMonthDataMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (value != null) {
                    monthEnergyInTotal = monthEnergyInTotal.add(value);
                }
            }
        }



        // 查询今年的累计流量数据
        List<String> totalFlowAttrs = new ArrayList<>(totalFlowKeyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowDataMap
                = obtainDataService.getDeviceData(totalFlowAttrs, yearStart.getTime(), now.getTime(), "month", null, tenantId);

        // 统计年数据
        BigDecimal yearTotalFlowTotal = new BigDecimal("0");
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowDataMap.entrySet()) {
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null && !dataMap.isEmpty()) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    BigDecimal value = dataEntry.getValue();
                    if (value != null) {
                        yearTotalFlowTotal = yearTotalFlowTotal.add(value);
                    }
                }
            }
        }

        // 统计月数据
        LinkedHashMap<String, BigDecimal> totalFlowMonthDataMap = energyInDataMap.get(dateFormat.format(now));
        BigDecimal monthTotalFlowTotal = new BigDecimal("0");
        if (totalFlowMonthDataMap != null && !totalFlowMonthDataMap.isEmpty()) {
            for (Map.Entry<String, BigDecimal> entry : totalFlowMonthDataMap.entrySet()) {
                BigDecimal value = entry.getValue();
                if (value != null) {
                    monthTotalFlowTotal = monthTotalFlowTotal.add(value);
                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("energyInYear", yearEnergyInTotal);
        result.put("energyInMonth", monthEnergyInTotal);
        result.put("totalFlowYear", yearTotalFlowTotal);
        result.put("totalFlowMonth", monthTotalFlowTotal);

        return result;
    }

    public Object todayEnergyInAndWaterSupply(String projectId, TenantId tenantId) throws ThingsboardException {
        // 查询指定项目下的泵站列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 999999, "泵站", projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return null;
        }

        // 查询数据
        Map<String, StationEntity> energyInKeyMap = new HashMap<>();
        Map<String, StationEntity> totalFlowKeyMap = new HashMap<>();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            // 有功电能数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("ENERGY_IN".equals(stationAttr.getAttr())) {// 能耗
                        energyInKeyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
            // 累计流量数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("total_flow".equals(stationAttr.getAttr())) {// 累计流量
                        totalFlowKeyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
        }
        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date yesterdayStart = new Date(instance.getTimeInMillis() - (24 * 60 * 60 * 1000));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String todayStr = dateFormat.format(now);
        String yesterdayStr = dateFormat.format(yesterdayStart);

        // 查询能耗数据
        List<String> energyInAttrs = new ArrayList<>(energyInKeyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> energyInDataMap
                = obtainDataService.getDeviceData(energyInAttrs, yesterdayStart.getTime(), now.getTime(), "day", null, tenantId);

        // 统计今日、昨日数据
        BigDecimal todayEnergyInTotal = new BigDecimal("0");
        BigDecimal yesterdayEnergyInTotal = new BigDecimal("0");
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : energyInDataMap.entrySet()) {
            if (entry.getKey().equals(todayStr)) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                if (dataMap != null && !dataMap.isEmpty()) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        BigDecimal value = dataEntry.getValue();
                        if (value != null) {
                            todayEnergyInTotal = todayEnergyInTotal.add(value);
                        }
                    }
                }
            }
            if (entry.getKey().equals(yesterdayStr)) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                if (dataMap != null && !dataMap.isEmpty()) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        BigDecimal value = dataEntry.getValue();
                        if (value != null) {
                            yesterdayEnergyInTotal = yesterdayEnergyInTotal.add(value);
                        }
                    }
                }
            }
        }

        // 查询今日的累计流量数据
        List<String> totalFlowAttrs = new ArrayList<>(totalFlowKeyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowDataMap
                = obtainDataService.getDeviceData(totalFlowAttrs, yesterdayStart.getTime(), now.getTime(), "day", null, tenantId);

        // 统计今日、昨日数据
        BigDecimal todayTotalFlowTotal = new BigDecimal("0");
        BigDecimal yesterdayTotalFlowTotal = new BigDecimal("0");
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowDataMap.entrySet()) {
            if (entry.getKey().equals(todayStr)) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                if (dataMap != null && !dataMap.isEmpty()) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        BigDecimal value = dataEntry.getValue();
                        if (value != null) {
                            todayTotalFlowTotal = todayTotalFlowTotal.add(value);
                        }
                    }
                }
            }
            if (entry.getKey().equals(yesterdayStr)) {
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                if (dataMap != null && !dataMap.isEmpty()) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        BigDecimal value = dataEntry.getValue();
                        if (value != null) {
                            yesterdayTotalFlowTotal = yesterdayTotalFlowTotal.add(value);
                        }
                    }
                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("todayEnergyInTotal", todayEnergyInTotal);
        result.put("yesterdayEnergyInTotal", yesterdayEnergyInTotal);
        result.put("todayTotalFlowTotal", todayTotalFlowTotal);
        result.put("yesterdayTotalFlowTotal", yesterdayTotalFlowTotal);

        return result;
    }

    /**
     * 查询指定时间内的用电量和供水量曲线
     */
    public Object todayEnergyInAndWaterSupplyTrend(String projectId, long start, long end, TenantId tenantId) throws ThingsboardException {
        // 查询指定项目下的泵站列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 999999, "泵站", projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return null;
        }

        // 查询数据
        Map<String, StationEntity> energyInKeyMap = new HashMap<>();
        Map<String, StationEntity> totalFlowKeyMap = new HashMap<>();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            // 有功电能数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("ENERGY_IN".equals(stationAttr.getAttr())) {// 能耗
                        energyInKeyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
            // 累计流量数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("total_flow".equals(stationAttr.getAttr())) {// 累计流量
                        totalFlowKeyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
        }

        // 查询能耗数据
        List<String> energyInAttrs = new ArrayList<>(energyInKeyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> energyInDataMap
                = obtainDataService.getDeviceData(energyInAttrs, start, end, "day", null, tenantId);

        // 查询今日的累计流量数据
        List<String> totalFlowAttrs = new ArrayList<>(totalFlowKeyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowDataMap
                = obtainDataService.getDeviceData(totalFlowAttrs, start, end, "day", null, tenantId);

        Set<String> keySet = energyInDataMap.keySet();

        List<JSONObject> resultList = new ArrayList<>();
        for (String key : keySet) {
            JSONObject obj = new JSONObject();
            obj.put("time", key);
            // 用电量数据
            LinkedHashMap<String, BigDecimal> energyInData = energyInDataMap.get(key);
            BigDecimal energyIn = null;
            if (energyInData != null && !energyInData.isEmpty()) {
                energyIn = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> entry : energyInData.entrySet()) {
                    if (entry.getValue() != null) {
                        energyIn = energyIn.add(entry.getValue());
                    }
                }
            }
            obj.put("energyIn", energyIn);

            // 供水量数据
            LinkedHashMap<String, BigDecimal> totalFlowData = totalFlowDataMap.get(key);
            BigDecimal totalFlow = null;
            if (totalFlowData != null && !totalFlowData.isEmpty()) {
                totalFlow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> entry : totalFlowData.entrySet()) {
                    if (entry.getValue() != null) {
                        totalFlow = totalFlow.add(entry.getValue());
                    }
                }
            }
            obj.put("totalFlow", totalFlow);

            resultList.add(obj);
        }

        return resultList;
    }

    /**
     * 水质合格率
     */
    public Object waterQualityPassRate(String projectId, TenantId tenantId) {
        // 查询指定项目下的泵站列表
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 999999, "泵站", projectId);
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return null;
        }

        // 查询数据
        Map<String, StationEntity> keyMap = new HashMap<>();
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();

        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            // 数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("ph".equals(stationAttr.getAttr())) {// PH
                        keyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                        stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
                    }
                    if ("remainder".equals(stationAttr.getAttr())) {// 余氯
                        keyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                        stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
                    }
                    if ("turbidity".equals(stationAttr.getAttr())) {// 浊度
                        keyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                        stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
                    }
                }
            }
        }

        List<String> attrs = new ArrayList<>(keyMap.keySet());

        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date startTime = instance.getTime();

        // 执行查询
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap
                = obtainDataService.getDeviceOriginalData(attrs, startTime.getTime(), now.getTime(), null, tenantId);

        int phTotal = 0;
        int phPass = 0;
        int remainderTotal = 0;
        int remainderPass = 0;
        int turbidityTotal = 0;
        int turbidityPass = 0;

        // 统计数据
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> entryValue = entry.getValue();
            if (entryValue != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : entryValue.entrySet()) {
                    StationAttrEntity stationAttr = stationAttrMap.get(dataEntry.getKey());
                    if (stationAttr == null) {
                        continue;
                    }
                    BigDecimal value = dataEntry.getValue();

                    String range = stationAttr.getRange();
                    if (StringUtils.isBlank(range)) {
                        continue;
                    }

                    String[] rangeArray = range.split(",");

                    if ("ph".equals(stationAttr.getAttr())) {// PH
                        phTotal++;
                        // 校验是否在范围内
                        if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[0]) < value.doubleValue() && Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                                phPass++;
                            }
                        }
                        if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[0]) < value.doubleValue()) {
                                phPass++;
                            }
                        }
                        if (StringUtils.isBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                                phPass++;
                            }
                        }
                    }
                    if ("remainder".equals(stationAttr.getAttr())) {// 余氯
                        remainderTotal++;
                        // 校验是否在范围内
                        if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[0]) < value.doubleValue() && Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                                remainderPass++;
                            }
                        }
                        if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[0]) < value.doubleValue()) {
                                remainderPass++;
                            }
                        }
                        if (StringUtils.isBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                                remainderPass++;
                            }
                        }
                    }
                    if ("turbidity".equals(stationAttr.getAttr())) {// 浊度
                        turbidityTotal++;
                        // 校验是否在范围内
                        if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[0]) < value.doubleValue() && Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                                turbidityPass++;
                            }
                        }
                        if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[0]) < value.doubleValue()) {
                                turbidityPass++;
                            }
                        }
                        if (StringUtils.isBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                            if (Double.parseDouble(rangeArray[1]) > value.doubleValue()) {
                                turbidityPass++;
                            }
                        }
                    }

                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("phTotal", phTotal);
        result.put("phPass", phPass);
        if (phTotal != 0) {
            result.put("phPassRate", BigDecimal.valueOf(phPass).divide(BigDecimal.valueOf(phTotal), 2, BigDecimal.ROUND_DOWN).multiply(new BigDecimal("100")));
        }
        result.put("remainderTotal", remainderTotal);
        result.put("remainderPass", remainderPass);
        if (remainderTotal != 0) {
            result.put("remainderPassRate", BigDecimal.valueOf(remainderPass).divide(BigDecimal.valueOf(remainderTotal), 2, BigDecimal.ROUND_DOWN).multiply(new BigDecimal("100")));
        }
        result.put("turbidityTotal", turbidityTotal);
        result.put("turbidityPass", turbidityPass);
        if (phTotal != 0) {
            result.put("turbidityPassRate", BigDecimal.valueOf(turbidityPass).divide(BigDecimal.valueOf(turbidityTotal), 2, BigDecimal.ROUND_DOWN).multiply(new BigDecimal("100")));
        }

        return result;
    }
}
