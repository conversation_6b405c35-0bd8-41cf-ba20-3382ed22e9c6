<!--  操作日志-->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    ></CardSearch>
    <CardTable
      :config="cardTableConfig"
      class="card-table"
    ></CardTable>
  </div>
</template>
<script lang="ts" setup>

import dayjs from 'dayjs'
import { Refresh, Search as SearchIcon } from '@element-plus/icons-vue'
import { ICardSearchIns } from '@/components/type'

const refSearch = ref<ICardSearchIns>()
const cardSearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '操作人',
      field: 'name'
    },
    { type: 'daterange', label: '时间查询', field: 'date' },
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', svgIcon: shallowRef(SearchIcon), click: () => refreshData() },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
          }
        }
      ]
    }
  ]
})
const cardTableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: [
    { label: '操作模块', minWidth: 120, prop: 'name1' },
    { label: '业务类型', minWidth: 120, prop: 'name' },
    { label: '操作人员', minWidth: 120, prop: 'method' },
    { label: '请求参数', minWidth: 120, prop: 'require' },
    { label: '返回参数', minWidth: 120, prop: 'require' },
    { label: '错误信息', minWidth: 120, prop: 'require' },
    { label: '操作地址', minWidth: 120, prop: 'require' },
    { label: '操作地址', minWidth: 120, prop: 'require' },
    { label: '操作状态', minWidth: 120, prop: 'require' },
    { label: '操作时间', minWidth: 120, prop: 'require', formatter: (row: any, value: any) => { return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '' } }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page
      cardTableConfig.pagination.limit = size
      refreshData()
    }
  }
})
// 获取配置数据列表
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}
  const params = {
    ...query
  }
  console.log(params)
  //
}

onMounted(async () => {
  await refreshData()
})
</script>
