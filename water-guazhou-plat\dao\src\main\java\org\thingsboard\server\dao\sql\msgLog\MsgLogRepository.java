package org.thingsboard.server.dao.sql.msgLog;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.MsgLogEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface MsgLogRepository extends CrudRepository<MsgLogEntity, String> {

    List<MsgLogEntity> findByTenantIdAndMsgTypeOrderByUpdateTimeDesc(@Param("tenantId") String tenantId,
                                                                     @Param("msgType") String type);

    @Query("FROM MsgLogEntity d WHERE d.tenantId = ?1 AND d.msgType = ?2 AND d.updateTime > ?3 AND d.updateTime < ?4 order by d.updateTime DESC ")
    List<MsgLogEntity> findByTenantIdAndUpdateTime(String tenantId, String type, Long start, Long end);


}
