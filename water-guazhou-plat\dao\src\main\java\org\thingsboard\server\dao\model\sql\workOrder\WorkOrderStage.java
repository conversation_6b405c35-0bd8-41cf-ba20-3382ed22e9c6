package org.thingsboard.server.dao.model.sql.workOrder;

import java.util.EnumSet;
import java.util.Set;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.*;

public enum WorkOrderStage {
    // 待处理
    WAITED(ASSIGN),
    // 已接收
    RECEIVED(RESOLVING, ARRIVING, PROCESSING, REJECTED),
    // 处理中
    PROCESSED(RESOLVING, ARRIVING, PROCESSING, REJECTED),
    // 审核中
    REVIEW(SUBMIT, WorkOrderStatus.REVIEW, CHARGEBACK_REVIEW, HANDOVER_REVIEW),
    // 完成
    COMPLETED(APPROVED, TERMINATED, COMPLETE),
    // 活动中
    ACTIVE(ASSIGN, RESOLVING, ARRIVING, PROCESSING, REJECTED, SUBMIT,
            WorkOrderStatus.REVIEW, CHARGEBACK_REVIEW, HANDOVER_REVIEW),

    ACTIVE1(ASSIGN, RESOLVING, ARRIVING, PROCESSING, REJECTED);

    public EnumSet<WorkOrderStatus> getStatusSet() {
        return statusSet;
    }

    private final EnumSet<WorkOrderStatus> statusSet;

    WorkOrderStage(WorkOrderStatus... status) {
        statusSet = EnumSet.of(status[0], status);
    }

    public Set<WorkOrderStatus> getStatusBetween() {
        return statusSet;
    }

    public boolean contains(WorkOrderStatus status) {
        return statusSet.contains(status);
    }
}
