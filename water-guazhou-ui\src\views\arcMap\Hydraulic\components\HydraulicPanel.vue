<template>
  <div class="hydraulicpanel">
    <FieldSet
      :icon="'mdi:view-dashboard'"
      :title="'图例'"
      :type="'simple'"
    ></FieldSet>

    <Legends
      :data="props.legends"
      :header="props.header"
    ></Legends>
    <el-divider />
    <SimplePie
      :unit="props.unit"
      :height="300"
      :data="
        props.legends.map(item => {
          return {
            name: item.label,
            value: item.value
          }
        })
      "
    ></SimplePie>
  </div>
</template>
<script lang="ts" setup>
import Legends from './Legends.vue'

const props = defineProps<{
  legends: { label: string; checked: boolean; value: any }[]
  header: string[]
  unit?: string
}>()
</script>
<style lang="scss" scoped></style>
