/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.inputKv;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.inputKv.InputKv;
import org.thingsboard.server.dao.feignService.ProjectRelationApi;
import org.thingsboard.server.dao.inputKv.InputKvDao;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
public class InputKvServiceImpl implements InputKvService {

    @Autowired
    private InputKvDao inputKvDao;

    @Autowired
    private ProjectRelationApi projectRelationApi;

    @Override
    public List<InputKv> getInputKvByEntityId(String entityId) {
        return inputKvDao.getInputKvByEntityId(entityId);
    }

    @Override
    public InputKv inputKvAdd(InputKv inputKv) {
        // 更新资源项目关系
        if (StringUtils.isNotBlank(inputKv.getProjectId())) {
            projectRelationApi.mountEntityToProject(DataConstants.ProjectRelationEntityType.INPUT_KV.name(),
                    inputKv.getProjectId(),
                    Collections.singletonList(UUIDConverter.fromTimeUUID(UUID.fromString(inputKv.getEntityId()))));
        }
        return inputKvDao.save(inputKv);
    }

    @Override
    public void deleteInputKvByTime(String entityId,long startTime, long endTime) {
        inputKvDao.deleteInputKvByTime(entityId,startTime,endTime);
        // 更新资源项目关系
        projectRelationApi.mountEntityToProject(DataConstants.ProjectRelationEntityType.INPUT_KV.name(),
                "",
                Collections.singletonList(entityId));
    }

    @Override
    public List<InputKv> getInputKvByEntityIdAndTime(String entityId, long start, long end) {
        return inputKvDao.getInputKvByEntityIdAndTime(entityId,start,end);
    }

    @Override
    public boolean delInputKv(UUID id) {
        return inputKvDao.removeById(id);
    }

}
