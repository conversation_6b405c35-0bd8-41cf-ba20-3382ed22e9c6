package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSystemInfo;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class SsPortalSystemInfoSaveRequest extends SaveRequest<SsPortalSystemInfo> {
    // 主站名称
    private String name;

    // 主站域名
    private String domain;

    // 备案号
    private String recordNo;


    @Override
    protected SsPortalSystemInfo build() {
        SsPortalSystemInfo entity = new SsPortalSystemInfo();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalSystemInfo update(String id) {
        SsPortalSystemInfo entity = new SsPortalSystemInfo();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalSystemInfo entity) {
        entity.setName(name);
        entity.setDomain(domain);
        entity.setRecordNo(recordNo);
    }

}