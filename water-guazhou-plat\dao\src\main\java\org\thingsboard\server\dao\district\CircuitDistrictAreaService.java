package org.thingsboard.server.dao.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaSaveRequest;

public interface CircuitDistrictAreaService {
    /**
     * 分页条件查询区域/路线
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMCircuitDistrictArea> findAllConditional(CircuitDistrictAreaPageRequest request);

    /**
     * 保存区域/路线
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SMCircuitDistrictArea save(CircuitDistrictAreaSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMCircuitDistrictArea entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 验证指定片区是否允许创建区域/路线，具体来说，只要不是根节点就可以创建
     *
     * @param districtId 片区id
     * @return 是否允许创建
     */
    boolean isValidDistrictId(String districtId);

    /**
     * 获取点阵信息
     *
     * @param id 区域/路线id
     * @return 点阵信息
     */
    String getPoints(String id);

}
