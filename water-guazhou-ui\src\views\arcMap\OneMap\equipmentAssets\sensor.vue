<!-- gis传感器 -->
<template>
  <div>
    <Cards v-model="cardsvalue"></Cards>
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components';
import { ring, oneHistogram } from '../../components/components/chart';
import {
  excuteQueryForCount,
  getSubLayerIds,
  initQueryParams
} from '@/utils/MapHelper';
import { PipeStatistics } from '@/api/mapservice/pipe';
import { useAppStore } from '@/store';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();

const refForm = ref<IFormIns>();

const cardsvalue = ref([
  { label: '0 个', value: '设备总数' },
  { label: '0 个', value: '通信正常' },
  { label: '0 个', value: '通信异常' }
]);

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'DEVICETYPE',
      fieldset: {
        type: 'underline',
        desc: '设备占比图'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          },
          handleClick: (params: any) => {
            emit(
              'highlightMark',
              props.menu,
              params.data.name === '未知'
                ? ''
                : ' DEVICETYPE=' + (params.data.name || '') + ' ',
              params.data.nameAlias
            );
          }
          // querySensor(
          //   params.data.name === '未知'
          //     ? ''
          //     : ' DEVICETYPE=' + (params.data.name || '') + ' '
          // )
        }
      ]
    },
    {
      id: 'STATUS',
      fieldset: {
        type: 'underline',
        desc: '通信情况占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          },
          handleClick: (params: any) => {
            emit(
              'highlightMark',
              props.menu,
              params.data.name === '未知'
                ? ''
                : ' STATUS=' + (params.data.name || '') + ' ',
              params.data.nameAlias
            );
          }
          // querySensor(
          // params.data.name === '未知'
          //   ? ''
          //   : ' STATUS=' + (params.data.name || '') + ' '
          // )
        }
      ]
    },
    {
      id: 'DEVICETYPEBAR',
      fieldset: {
        type: 'underline',
        desc: '设备数量占比图'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          }
        }
      ]
    },
    {
      id: 'MANUALFACTORY',
      fieldset: {
        type: 'underline',
        desc: '按流量计厂家统计传感器数量'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});

// const querySensor = async (sql?: string) => {
//   if (!props.view) return
//   const layerIds = getSubLayerIds(props.view, undefined, undefined, '传感器')
//   if (!layerIds.length) return
//   const res = await excuteQuery(
//     window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layerIds[0],
//     initQueryParams({
//       returnGeometry: true,
//       where: sql || ''
//     })
//   )
//   const symbol = setSymbol('point')

//   const layer = getGraphicLayer(props.view, {
//     id: props.menu.path,
//     title: props.menu.meta.title
//   })
//   layer?.removeAll()
//   res.features.map(item => (item.symbol = symbol))
//   layer?.addMany(res.features)

//   SLMessage.success('查询成功！')
// }

const querySensorCount = async (sql?: string) => {
  if (!props.view) return;
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '传感器');
  if (!layerIds.length) return;
  const res = await excuteQueryForCount(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
      '/' +
      layerIds[0],
    initQueryParams({
      returnGeometry: false,
      where: sql || ''
    })
  );
  return res;
};

const refreshSensorTypeChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find(
    (item) => item.id === 'DEVICETYPE'
  )?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '传感器');
  const res = await PipeStatistics({
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['DEVICETYPE']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: ''
  });
  const data = res.data.result.rows[0]?.rows?.map((item) => {
    return {
      name: item.DEVICETYPE || '未知',
      nameAlias: item.DEVICETYPE || '未知',
      value: item.ObjectId || 0
    };
  });
  field && (field.option = ring(data, '个', '', 0));
};

const refreshSensorStatusChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find((item) => item.id === 'STATUS')?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '传感器');
  const res = await PipeStatistics({
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['STATUS']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: ''
  });
  const data = res.data?.result?.rows[0]?.rows?.map((item) => {
    return {
      name: item.STATUS || '未知',
      nameAlias: item.STATUS || '未知',
      value: item.ObjectId || 0
    };
  });
  field && (field.option = ring(data, '个', '', 0));
};
const refreshSensorTypeBarChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find(
    (item) => item.id === 'DEVICETYPEBAR'
  )?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '传感器');
  const res = await PipeStatistics({
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['DEVICETYPE']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: ''
  });
  const data = res.data.result.rows[0]?.rows?.map((item) => {
    return {
      name: item.DEVICETYPE || '未知',
      nameAlias: item.DEVICETYPE || '未知',
      value: item.ObjectId || 0
    };
  });
  field && (field.option = oneHistogram(data, '个'));
};
const refreshSensorMANUALFACTORYBarChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find(
    (item) => item.id === 'MANUALFACTORY'
  )?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '传感器');
  const res = await PipeStatistics({
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['MANUALFACTORY']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: ''
  });
  const data = res.data.result.rows[0]?.rows?.map((item) => {
    return {
      name: item.MANUALFACTORY || '未知',
      nameAlias: item.MANUALFACTORY || '未知',
      value: item.ObjectId || 0
    };
  });
  field && (field.option = oneHistogram(data, '个'));
};
const refreshData = async () => {
  refreshSensorStatusChart();
  refreshSensorTypeChart();
  refreshSensorTypeBarChart();
  refreshSensorMANUALFACTORYBarChart();
  const Count = await querySensorCount();
  cardsvalue.value[0].label = (Count || '0') + ' 个';
};
onMounted(() => {
  refreshData();
});
watch(
  () => useAppStore().isDark,
  () => refreshData()
);
</script>

<style lang="scss" scoped></style>
