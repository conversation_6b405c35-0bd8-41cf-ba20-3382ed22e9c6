package org.thingsboard.server.dao.sql.report;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.ReportRequest;
import org.thingsboard.server.dao.model.sql.report.ReportComponent;

@Mapper
public interface ReportComponentMapper extends BaseMapper<ReportComponent> {
    IPage<ReportComponent> getList(IPage<ReportComponent> page, @Param("param") ReportRequest request);
}
