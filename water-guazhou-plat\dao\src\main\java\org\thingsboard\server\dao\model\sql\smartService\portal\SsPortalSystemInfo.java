package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_system_info")
public class SsPortalSystemInfo {
    // id
    private String id;

    // 主站名称
    private String name;

    // 主站域名
    private String domain;

    // 备案号
    private String recordNo;

    // 客户id
    private String tenantId;

}
