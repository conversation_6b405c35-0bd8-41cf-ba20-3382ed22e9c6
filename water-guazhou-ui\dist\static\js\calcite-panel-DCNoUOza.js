import{P as t,d as o}from"./panel-DHmkOIYq.js";import"./widget-BcWKanF2.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./interactive-crkFkZAr.js";import"./loadable-DZS8sRBo.js";import"./observers-D10wq1Ib.js";import"./action-menu-i7jt7xb8.js";import"./guid-DO7TRjsS.js";import"./key-7hamXU9f.js";import"./action-CK67UTEO.js";import"./t9n-B2bWcUZc.js";import"./icon-vUORPQEt.js";import"./loader-DYvscnHN.js";import"./openCloseComponent-aiDFLC5b.js";import"./debounce-x6ZvqDEC.js";import"./scrim-Eo5BG2Ie.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const b=t,g=o;export{b as CalcitePanel,g as defineCustomElement};
