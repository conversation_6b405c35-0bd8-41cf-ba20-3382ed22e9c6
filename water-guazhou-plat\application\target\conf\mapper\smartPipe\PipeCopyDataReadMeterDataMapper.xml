<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeCopyDataReadMeterDataMapper">
    <insert id="batchInsert">
        INSERT INTO tb_pipe_copy_data_read_meter_data(
        id,
        code,
        ym,
        user_code,
        org_id,
        meter_id,
        point_id,
        meter_copy_number,
        last_read_num,
        last_read_water,
        this_read_num,
        this_read_water,
        append_water,
        total_water,
        read_status,
        exception_type,
        last_read_date,
        this_read_date,
        record_type,
        meter_address,
        tenant_id,
        remark,
        execute_user,
        update_user,
        type,
        data_time,
        imgs,
        correct_water,
        origin_water
        ) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.code},
            #{element.ym},
            #{element.userCode},
            #{element.orgId},
            #{element.meterId},
            #{element.pointId},
            #{element.meterCopyNumber},
            #{element.lastReadNum},
            #{element.lastReadWater},
            #{element.thisReadNum},
            #{element.thisReadWater},
            #{element.appendWater},
            #{element.totalWater},
            #{element.readStatus},
            #{element.exceptionType},
            #{element.lastReadDate},
            #{element.thisReadDate},
            #{element.recordType},
            #{element.meterAddress},
            #{element.tenantId},
            #{element.remark},
            #{element.executeUser},
            #{element.updateUser},
            #{element.type},
            #{element.dataTime},
            #{element.imgs},
            #{element.correctWater},
            #{element.originWater}
            )
        </foreach>
    </insert>

    <delete id="deleteByTime">
        delete from revenue.tb_pipe_copy_data_read_meter_data where data_time between #{start} and #{end}
    </delete>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData">
        select a.*, b.code as custCode, b.name as custName, b.phone, tpp.name as partitionName
        from revenue.tb_pipe_copy_data_read_meter_data a
        left join revenue.tb_water_meter wm on a.meter_id = wm.meter_code
        left join revenue.tb_cust_info b on wm.cust_code = b.code and wm.tenant_id = b.tenant_id
        left join tb_pipe_partition_cust tppc on b.code = tppc.cust_code and a.tenant_id = tppc.tenant_id
        left join tb_pipe_partition tpp on tppc.partition_id = tpp.id
        where a.tenant_id = #{param.tenantId} and tppc.partition_id = #{param.partitionId} and a.total_water is not null
        <if test="param.custCode != null and param.custCode != ''">
            and a.user_code like '%' || #{param.custCode} || '%'
        </if>
        <if test="param.custName != null and param.custName != ''">
            and b.name like '%' || #{param.custName} || '%'
        </if>
        <if test="param.start != null and param.start != ''">
            and a.this_read_date &gt;= to_timestamp(#{param.start} / 1000)
        </if>
        <if test="param.end != null and param.end != ''">
            and a.this_read_date &lt;= to_timestamp(#{param.end} / 1000)
        </if>
        order by a.this_read_date desc
    </select>

    <select id="sumCorrectByPartitionId" resultType="com.alibaba.fastjson.JSONObject">
        select parti.id, ifnull(sum(copyData.total_water), 0) as total, ifnull(sum(copyData.correct_water), 0) as "correctWater"
        from tb_pipe_partition parti
        left join tb_pipe_partition_cust tppc on parti.id = tppc.partition_id
        left join revenue.tb_water_meter twm on tppc.cust_code = twm.cust_code
        left join revenue.tb_pipe_copy_data_read_meter_data copyData on twm.meter_code = copyData.meter_id and twm.tenant_id = copyData.tenant_id
        <where>
            total_water > 0 and total_water &lt; 8000
            <if test="partitionIdList != null and partitionIdList.size() > 0">
                and parti.id in
                <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="start != null and end != null">
                and copyData.this_read_date between to_timestamp(#{start} / 1000) and to_timestamp(#{end} / 1000)
            </if>
        </where>
        group by parti.id
    </select>
    <select id="getListByPartitionId" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData">
        select parti.id as partitionid, copyData.*
        from tb_pipe_partition parti
        left join tb_pipe_partition_cust tppc on parti.id = tppc.partition_id
        left join revenue.tb_water_meter twm on tppc.cust_code = twm.cust_code
        left join revenue.tb_pipe_copy_data_read_meter_data copyData on twm.meter_code = copyData.meter_id and tppc.tenant_id = copyData.tenant_id
        <where>
            total_water > 0 and total_water &lt; 8000
            <if test="partitionIdList != null and partitionIdList.size() > 0">
                and parti.id in
                <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="start != null and end != null">
                and copyData.this_read_date between to_timestamp(#{start} / 1000) and to_timestamp(#{end} / 1000)
            </if>
        </where>

    </select>

</mapper>