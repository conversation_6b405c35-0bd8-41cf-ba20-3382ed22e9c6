import{v as w,V as b,S as c,e as a,y as l,a as k,U as E}from"./Point-WxyopZva.js";import{R as d,aO as F,T as h,b1 as C}from"./index-r0dFAfgr.js";import{f as $,bp as A,_ as R,v as G,cu as y,as as P,bM as x,ca as I,an as O,cv as z}from"./MapView-DaoQedLH.js";import{k as g,l as H,a as Z}from"./widget-BcWKanF2.js";import{p as L}from"./normalizeUtilsSync-NMksarRY.js";import{g as M}from"./FeatureStore-BG3NYFyq.js";import{e as W}from"./QueryEngine-qET-Q1Qx.js";import{y as T}from"./elevationInfoUtils-5B4aSzEU.js";import{z as j,y as q}from"./AnimatedLinesLayer-B2VbV4jv.js";import{i as B,p as U}from"./queryEngineUtils-zApNHdaJ.js";import{r as _,a as V,n as f}from"./symbologySnappingCandidates-CZjQb_7m.js";import"./pe-B8dP0-Ut.js";import"./BoundsStore-wYOD4ytd.js";import"./PooledRBush-CoOUdN-a.js";import"./optimizedFeatureQueryEngineAdapter-VytK6WwF.js";import"./centroid-UTistape.js";import"./utils-dKbgHYZY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./timeSupport-vHbsRqQz.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./VertexSnappingCandidate-CgwNICNk.js";const v="graphics-collections";let n=class extends $(w){get updating(){return this.updatingHandles.updating}get _hasZ(){const t=this.view;return d(t)&&t.type==="3d"&&this.layerSource.layer.type!=="map-notes"}get _snappingElevationAligner(){const{view:t}=this,{layer:e}=this.layerSource,i=d(t)&&t.type==="3d";if(!i||e.type==="map-notes")return _();const o=async(s,r)=>(await E(t.whenLayerView(e),r)).elevationAlignPointsInFeatures(s,r);return _(i,{elevationInfo:e.elevationInfo,alignPointsInFeatures:o,spatialReference:t.spatialReference})}get _snappingElevationFilter(){const{view:t}=this,e=d(t)&&t.type==="3d"&&this.layerSource.layer.type!=="map-notes";return V(e)}get _symbologySnappingFetcher(){const{view:t}=this,{layer:e}=this.layerSource,i=d(t)&&t.type==="3d",o=this._extrudedPolygonSymbolsCount>0;return i&&e.type!=="map-notes"&&o?f(o,async(s,r)=>{const p=await t.whenLayerView(e);return c(r),p.queryForSymbologySnapping({candidates:s,spatialReference:t.spatialReference},r)}):f()}constructor(t){super(t),this.availability=1,this._sources={multipoint:null,point:null,polygon:null,polyline:null},this._loadedWkids=new Set,this._loadedWkts=new Set,this._pendingAdds=[],this._extrudedPolygonSymbolsCount=0}destroy(){for(const t of this._pendingAdds)t.task.abort();this._pendingAdds.length=0,this._mapSources(t=>this._destroySource(t))}initialize(){this.updatingHandles.add(()=>this.getGraphicsLayers(),i=>{this.updatingHandles.removeHandles(v);for(const o of i)this._addMany(o.graphics.toArray()),this.handles.add([o.on("graphic-update",s=>this._onGraphicUpdate(s)),this.updatingHandles.addOnCollectionChange(()=>o.graphics,s=>this._onGraphicsChanged(s))],v)},g);const{view:t}=this,{layer:e}=this.layerSource;d(t)&&t.type==="3d"&&e.type!=="map-notes"&&this.addHandles([t.elevationProvider.on("elevation-change",({context:i})=>{T(i,e.elevationInfo)&&this._snappingElevationAligner.notifyElevationSourceChange()}),H(()=>e.elevationInfo,()=>this._snappingElevationAligner.notifyElevationSourceChange(),g),Z(()=>e,["edits","apply-edits","graphic-update"],()=>this._symbologySnappingFetcher.notifySymbologyChange())])}async fetchCandidates(t,e){const{point:i}=t,o=await b(this._mapSources(p=>this._fetchCandidatesForSource(p,t,e)));c(e);const s=this._getGroundElevation,r=o.flat().map(p=>B(p,s));return j(i,r),r}get _getGroundElevation(){return U(this.view)}async _fetchCandidatesForSource(t,e,i){var m;const o=q(e,((m=F(this.view))==null?void 0:m.type)??"2d"),s=await t.queryEngine.executeQueryForSnapping(o,i);c(i);const r=await this._snappingElevationAligner.alignCandidates(s.candidates,i);c(i);const p=await this._symbologySnappingFetcher.fetch(r,i);c(i);const S=p.length===0?r:[...r,...p];return this._snappingElevationFilter.filter(o,S)}refresh(){}_onGraphicUpdate(t){if(this.getGraphicsLayers().some(e=>e.graphics.includes(t.graphic)))switch(t.property){case"geometry":case"visible":this._remove(t.graphic),this._addMany([t.graphic])}}_onGraphicsChanged(t){for(const e of t.removed)this._remove(e);this._addMany(t.added)}_addMany(t){const e=[],i=new Map;for(const o of t)h(o.geometry)||(this._needsInitializeProjection(o.geometry.spatialReference)?(e.push(o.geometry.spatialReference),i.set(o.uid,o)):this._add(o));this._createPendingAdd(e,i)}_createPendingAdd(t,e){if(!t.length)return;const i=A(async r=>{await R(t.map(p=>({source:p,dest:this.spatialReference})),{signal:r}),this._markLoadedSpatialReferences(t);for(const[,p]of e)this._add(p)});this.updatingHandles.addPromise(i.promise);const o={task:i,graphics:e},s=()=>C(this._pendingAdds,o);i.promise.then(s,s),this._pendingAdds.push(o)}_markLoadedSpatialReferences(t){for(const e of t)e.wkid!=null&&this._loadedWkids.add(e.wkid),e.wkt!=null&&this._loadedWkts.add(e.wkt)}_add(t){if(h(t.geometry)||!t.visible)return;let e=t.geometry;if(e.type==="mesh")return;e.type==="extent"&&(e=G.fromExtent(e));const i=this._ensureSource(e.type);if(h(i))return;const o=this._createOptimizedFeature(t.uid,e);h(o)||(i.featureStore.add(o),y(t.symbol)&&this._extrudedPolygonSymbolsCount++)}_needsInitializeProjection(t){return(t.wkid==null||!this._loadedWkids.has(t.wkid))&&(t.wkt==null||!this._loadedWkts.has(t.wkt))&&!P(t,this.spatialReference)}_createOptimizedFeature(t,e){const i=x(L(e),this.spatialReference);if(!i)return null;const o=this._ensureGeometryHasZ(i),s=I(o,this._hasZ,!1);return new O(s,{[u]:t},null,t)}_ensureGeometryHasZ(t){if(!this._hasZ)return t;const e=o=>{for(;o.length<3;)o.push(0)},i=t.clone();switch(i.hasZ=!0,i.type){case"point":i.z=i.z??0;break;case"multipoint":i.points.forEach(e);break;case"polyline":i.paths.forEach(o=>o.forEach(e));break;case"polygon":i.rings.forEach(o=>o.forEach(e))}return i}_ensureSource(t){const e=this._sources[t];if(d(e))return e;const i=this._createSource(t);return this._sources[t]=i,i}_createSource(t){const e=z.toJSON(t),i=this._hasZ,o=new M({geometryType:e,hasZ:i,hasM:!1});return{featureStore:o,queryEngine:new W({featureStore:o,fields:[{name:u,type:"esriFieldTypeOID",alias:u}],geometryType:e,hasM:!1,hasZ:i,objectIdField:u,spatialReference:this.spatialReference,scheduler:d(this.view)&&this.view.type==="3d"?this.view.resourceController.scheduler:null}),type:t}}_remove(t){this._mapSources(e=>this._removeFromSource(e,t));for(const e of this._pendingAdds)e.graphics.delete(t.uid),e.graphics.size===0&&e.task.abort()}_removeFromSource(t,e){const i=e.uid;t.featureStore.has(i)&&(t.featureStore.removeById(e.uid),y(e.symbol)&&this._extrudedPolygonSymbolsCount--)}_destroySource(t){t.queryEngine.destroy(),this._sources[t.type]=null}_mapSources(t){const{point:e,polygon:i,polyline:o,multipoint:s}=this._sources,r=[];return d(e)&&r.push(t(e)),d(i)&&r.push(t(i)),d(o)&&r.push(t(o)),d(s)&&r.push(t(s)),r}};a([l()],n.prototype,"getGraphicsLayers",void 0),a([l({constructOnly:!0})],n.prototype,"layerSource",void 0),a([l({constructOnly:!0})],n.prototype,"spatialReference",void 0),a([l({constructOnly:!0})],n.prototype,"view",void 0),a([l({readOnly:!0})],n.prototype,"updating",null),a([l({readOnly:!0})],n.prototype,"availability",void 0),a([l()],n.prototype,"_hasZ",null),a([l()],n.prototype,"_snappingElevationAligner",null),a([l()],n.prototype,"_snappingElevationFilter",null),a([l()],n.prototype,"_symbologySnappingFetcher",null),a([l()],n.prototype,"_extrudedPolygonSymbolsCount",void 0),a([l()],n.prototype,"_getGroundElevation",null),n=a([k("esri.views.interactive.snapping.featureSources.GraphicsSnappingSource")],n);const u="OBJECTID";export{n as GraphicsSnappingSource};
