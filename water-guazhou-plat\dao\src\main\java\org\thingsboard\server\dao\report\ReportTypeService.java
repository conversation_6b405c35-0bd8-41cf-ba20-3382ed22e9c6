package org.thingsboard.server.dao.report;

import org.thingsboard.server.dao.model.sql.report.ReportType;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-07
 */
public interface ReportTypeService {

    ReportType save(ReportType reportType);

    List<ReportType> getList(String tenantId);

    IstarResponse delete(List<String> ids);
}
