import{d as _,g as u,n as v,p as s,bh as a,aw as m,i as w,j as f,C}from"./index-r0dFAfgr.js";const k={class:"header"},h={class:"info"},B={class:"count"},D={class:"content"},V={class:"content-item"},b={class:"count"},g={class:"content-item"},A={class:"count"},S={class:"content-item"},j={class:"count"},y=_({__name:"CountCard",props:{row:{},modelValue:{}},setup(z){return(o,t)=>{var n,e,i,l,r,d,p,c;return u(),v("div",{class:m(["card",w(f)().isDark?"darkblue":""])},[s("div",k,[t[1]||(t[1]=s("span",{class:"title"},"DMA总数",-1)),s("span",h,[s("span",B,a(((e=(n=o.row)==null?void 0:n.overview)==null?void 0:e.total)||0),1),t[0]||(t[0]=s("span",{class:"unit"},"个",-1))])]),s("div",D,[s("div",V,[t[2]||(t[2]=s("span",{class:"title"},"已运营",-1)),s("span",b,a(((l=(i=o.row)==null?void 0:i.overview)==null?void 0:l.start)||0),1)]),s("div",g,[t[3]||(t[3]=s("span",{class:"title"},"规划中",-1)),s("span",A,a(((d=(r=o.row)==null?void 0:r.overview)==null?void 0:d.plan)||0),1)]),s("div",S,[t[4]||(t[4]=s("span",{class:"title"},"已停运",-1)),s("span",j,a(((c=(p=o.row)==null?void 0:p.overview)==null?void 0:c.stop)||0),1)])])],2)}}}),I=C(y,[["__scopeId","data-v-2f6aa71a"]]);export{I as default};
