package org.thingsboard.server.dao.report;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.ReportTableDTO;
import org.thingsboard.server.dao.model.request.ReportRequest;
import org.thingsboard.server.dao.model.sql.report.Report;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-20
 */
public interface ReportService {

    Report save(Report report);

    PageData<Report> getList(ReportRequest request);

    /**
     * 软删除
     * @param ids
     */
    void softDelete(List<String> ids);

    ReportTableDTO getDetail(String pid);

    void delete(List<String> idList);

    ReportTableDTO saveDetail(ReportTableDTO reportTableDTO);

    /**
     * 从回收站恢复
     * @param idList
     */
    void recover(List<String> idList);
}
