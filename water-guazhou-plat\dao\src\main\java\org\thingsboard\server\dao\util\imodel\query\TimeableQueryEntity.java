package org.thingsboard.server.dao.util.imodel.query;

import lombok.Getter;
import org.joda.time.DateTime;
import org.thingsboard.server.dao.util.TimeUtils;

import java.util.Date;

public class TimeableQueryEntity implements Requestible, AwareTenantUUID {

    // 何时开始
    private Object fromTime;

    // 何时结束
    private Object toTime;

    @Getter
    private String tenantId;

    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    public final Date getToTime() {
        return getToTimeOrDefault(toTime);
    }

    protected Date getToTimeOrDefault(Object toTime) {
        if (toTime == null) {
            return null;
        }
        if (clampToDay()) {
            return new DateTime(TimeUtils.defaultIfNull(fromTime, null)).millisOfDay().withMaximumValue().toDate();
        }

        if (toTime.getClass().equals(String.class) && String.valueOf(toTime).length() == 10) {
            toTime = ((String) toTime).substring(0, 10) + " 23:59:59";
        }

        return TimeUtils.defaultIfNull(toTime, null);
    }

    public void setToTime(String toTime) {
        this.toTime = toTime;
    }

    public final Date getFromTime() {
        return getFromTimeOrDefault(fromTime);
    }

    protected Date getFromTimeOrDefault(Object fromTime) {
        if (fromTime == null) {
            return null;
        }
        if (clampToDay()) {
            return new DateTime(TimeUtils.defaultIfNull(toTime, null)).millisOfDay().withMinimumValue().toDate();
        }
        return TimeUtils.defaultIfNull(fromTime, null);
    }

    @Override
    public void tenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String tenantId() {
        return tenantId;
    }

    protected final Date toDate(String pattern) {
        return TimeUtils.defaultIfNull(pattern, null);
    }

    public TimeableQueryEntity changeTimeRange(Date from, Date to) {
        this.fromTime = from;
        this.toTime = to;
        return this;
    }

    /**
     * 将fromTime和toTime限制到日查询
     * @return 是否限制
     */
    protected boolean clampToDay() {
        return false;
    }

}
