package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.springframework.format.annotation.DateTimeFormat;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA分析
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-24
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_WARN_CONFIG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaWarnConfigEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_PARTITION_ID)
    private String partitionId;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_START_TIME)
    private Date startTime;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_INFO_VALUE)
    private Float infoValue;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_WARN_VALUE)
    private Float warnValue;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_ERROR_VALUE)
    private Float errorValue;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_PRE_DAY)
    private Integer preDay;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_MIN_FLOW)
    private Float minFlow;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_MAX_FLOW)
    private Float maxFlow;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_WARN_MULTIPLE)
    private Integer warnMultiple;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_ERROR_MULTIPLE)
    private Integer errorMultiple;

    @Column(name = ModelConstants.DMA_WARN_CONFIG_TYPE)
    private String type;


}
