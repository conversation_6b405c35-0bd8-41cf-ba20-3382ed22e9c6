/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.video;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.VideoGroupEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.sql.video.VideoGroupMapper;
import org.thingsboard.server.dao.util.TreeUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VideoGroupServiceImpl implements VideoGroupService {


    @Autowired
    private VideoGroupMapper videoGroupMapper;

    @Autowired
    private ProjectService projectService;

    @Override
    public IstarResponse save(VideoGroupEntity videoConfigEntity) {
        if (StringUtils.isBlank(videoConfigEntity.getId())) {
            videoConfigEntity.setCreateTime(new Date());
            videoGroupMapper.insert(videoConfigEntity);
        } else {
            videoGroupMapper.updateById(videoConfigEntity);
        }

        return IstarResponse.ok(videoConfigEntity);
    }

    @Override
    public IstarResponse delete(String id) {
        QueryWrapper<VideoGroupEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", id);
        List<VideoGroupEntity> groupEntities = videoGroupMapper.selectList(queryWrapper);
        if (groupEntities.size() > 0) {
            return IstarResponse.error("请先删除下级分组");
        }
        videoGroupMapper.deleteById(id);
        return IstarResponse.ok();
    }

    @Override
    public List<TreeNodeDTO> getTreeByProjectId(String projectId, String tenantId) {
        List<TreeNodeDTO> treeNodeDTOS;

        if (StringUtils.isBlank(projectId)) {
            treeNodeDTOS = getTreeByAllProject(tenantId);
            treeNodeDTOS = TreeUtil.listToTree(treeNodeDTOS, "0");
        } else {
            List<TreeNodeDTO> allList = this.getAllByParentId(projectId, tenantId);

            // 组成树
            treeNodeDTOS = TreeUtil.listToTree(allList, projectId);
        }

        return treeNodeDTOS;
    }

    public List<TreeNodeDTO> getTreeByAllProject(String tenantId) {
        List<TreeNodeDTO> treeNodeDTOS = new ArrayList<>();
        List<ProjectEntity> projectEntityList = projectService.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId)));
        List<TreeNodeDTO> projectList = projectEntityList.stream().map(projectEntity -> TreeNodeDTO.builder()
                .id(projectEntity.getId())
                .parentId(projectEntity.getParentId())
                .name(projectEntity.getName())
                .type("project")
                .typeName("项目")
                .nodeDetail(projectEntity).build()).collect(Collectors.toList());
        treeNodeDTOS.addAll(projectList);
        for (TreeNodeDTO treeNodeDTO : projectList) {
            treeNodeDTOS.addAll(this.getAllByParentId(treeNodeDTO.getId(), tenantId));
        }

        return treeNodeDTOS;
    }

    private List<TreeNodeDTO> getAllByParentId(String projectId, String tenantId) {
        List<TreeNodeDTO> result = new ArrayList<>();
        List<VideoGroupEntity> groupEntities = videoGroupMapper.getList(projectId);
        for (VideoGroupEntity videoGroupEntity : groupEntities) {
            result.addAll(this.getAllByParentId(videoGroupEntity.getId(), tenantId));
        }

        if (groupEntities != null && groupEntities.size() > 0) {
            result.addAll(groupEntities.stream().map(groupEntity -> TreeNodeDTO.builder()
                    .id(groupEntity.getId())
                    .parentId(groupEntity.getParentId())
                    .name(groupEntity.getName())
                    .type("group")
                    .typeName("分组")
                    .num(groupEntity.getVideoNum())
                    .nodeDetail(groupEntity).build()).collect(Collectors.toList()));
        }
        return result;
    }

}
