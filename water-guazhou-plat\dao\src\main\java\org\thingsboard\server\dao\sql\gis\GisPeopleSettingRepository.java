package org.thingsboard.server.dao.sql.gis;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.gis.GisPeopleSettingEntity;

import java.util.List;

public interface GisPeopleSettingRepository  extends JpaRepository<GisPeopleSettingEntity, String>, JpaSpecificationExecutor<GisPeopleSettingEntity> {

    @Query("SELECT g FROM GisPeopleSettingEntity g " +
            "WHERE g.tenantId = ?2 AND g.name LIKE %?1%")
    Page<GisPeopleSettingEntity> findList(String name, String tenantId, Pageable pageable);

    List<GisPeopleSettingEntity> findByTypeAndTenantId(String type, String tenantId);
}
