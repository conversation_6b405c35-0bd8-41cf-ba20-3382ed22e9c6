package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsTransferEntity;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
public interface AssetsTransferService {
    PageData getPage(JSONObject params);

    AssetsTransferEntity save(AssetsTransferEntity assetsTransferEntity);

    Map reviewer(String id, String reviewerId, String status);

    void delete(List<String> idList);
}
