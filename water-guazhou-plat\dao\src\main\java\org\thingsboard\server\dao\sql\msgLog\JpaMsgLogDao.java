package org.thingsboard.server.dao.sql.msgLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.MsgLogEntity;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.msgLog.MsgLogDao;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:05
 */
@Service
@Transactional
public class JpaMsgLogDao implements MsgLogDao {


    @Autowired
    private MsgLogRepository msgLogRepository;


    @Override
    public MsgLogEntity save(MsgLogEntity msgLogEntity) {
        return msgLogRepository.save(msgLogEntity);
    }

    @Override
    public List<MsgLogEntity> findByTenant(String tenantId,String type) {
        return msgLogRepository.findByTenantIdAndMsgTypeOrderByUpdateTimeDesc(tenantId,type);
    }

    @Override
    public List<MsgLogEntity> findByTenantAndTime(String tenantId, String type, long start, long end) {
        return msgLogRepository.findByTenantIdAndUpdateTime(tenantId,type,start,end);
    }
}
