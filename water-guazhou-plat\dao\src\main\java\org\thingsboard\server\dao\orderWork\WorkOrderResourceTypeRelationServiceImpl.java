package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResourceTypeRelation;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderResourceTypeRelationMapper;

@Slf4j
@Service
public class WorkOrderResourceTypeRelationServiceImpl implements WorkOrderResourceTypeRelationService {

    @Autowired
    private WorkOrderResourceTypeRelationMapper workOrderResourceTypeRelationMapper;

    @Override
    @Transactional
    public void save(WorkOrderResourceTypeRelation entity) {
        // 先删除
        String resourceId = entity.getResourceId();
        QueryWrapper<WorkOrderResourceTypeRelation> deleteQueryWrapper = new QueryWrapper<>();
        deleteQueryWrapper.eq("resource_id", resourceId);
        workOrderResourceTypeRelationMapper.delete(deleteQueryWrapper);

        // 保存
        String typeIds = entity.getTypeId();
        String[] typeArray = typeIds.split(",");
        for (String typeId : typeArray) {
            WorkOrderResourceTypeRelation relation = new WorkOrderResourceTypeRelation();
            relation.setTypeId(typeId);
            relation.setResourceId(resourceId);
            workOrderResourceTypeRelationMapper.insert(relation);
        }


    }
}
