# Created at 2025-07-10T14:56:11.797
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\surefire-booter\3.0.0-M1\surefire-booter-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.798
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\surefire-api\3.0.0-M1\surefire-api-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.799
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\surefire-logger-api\3.0.0-M1\surefire-logger-api-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.799
Boot Manifest-JAR contains absolute paths in classpath D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\common\data\target\test-classes
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.801
Boot Manifest-JAR contains absolute paths in classpath D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\common\data\target\classes
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.802
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\slf4j\slf4j-api\1.7.7\slf4j-api-1.7.7.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.803
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\slf4j\log4j-over-slf4j\1.7.7\log4j-over-slf4j-1.7.7.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.803
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.804
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.805
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-databind\2.8.11.1\jackson-databind-2.8.11.1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.806
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-annotations\2.8.0\jackson-annotations-2.8.0.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.807
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-core\2.8.10\jackson-core-2.8.10.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.808
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\junit\junit\4.12\junit-4.12.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.809
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.810
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\mockito\mockito-all\1.9.5\mockito-all-1.9.5.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.812
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\datastax\cassandra\cassandra-driver-core\3.5.0\cassandra-driver-core-3.5.0.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.813
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\netty\netty-handler\4.1.22.Final\netty-handler-4.1.22.Final.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.813
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\netty\netty-buffer\4.1.22.Final\netty-buffer-4.1.22.Final.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.814
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\netty\netty-common\4.1.22.Final\netty-common-4.1.22.Final.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.815
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\netty\netty-transport\4.1.22.Final\netty-transport-4.1.22.Final.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.816
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\netty\netty-resolver\4.1.22.Final\netty-resolver-4.1.22.Final.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.817
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\netty\netty-codec\4.1.22.Final\netty-codec-4.1.22.Final.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.817
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\google\guava\guava\21.0\guava-21.0.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.818
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\io\dropwizard\metrics\metrics-core\3.2.2\metrics-core-3.2.2.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.818
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-ffi\2.1.7\jnr-ffi-2.1.7.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.820
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\github\jnr\jffi\1.2.16\jffi-1.2.16.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.820
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\github\jnr\jffi\1.2.16\jffi-1.2.16-native.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.821
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm\5.0.3\asm-5.0.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.822
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-commons\5.0.3\asm-commons-5.0.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.823
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-analysis\5.0.3\asm-analysis-5.0.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.823
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-tree\5.0.3\asm-tree-5.0.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.823
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-util\5.0.3\asm-util-5.0.3.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.825
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-x86asm\1.0.2\jnr-x86asm-1.0.2.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.826
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-posix\3.0.44\jnr-posix-3.0.44.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.826
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-constants\0.9.9\jnr-constants-0.9.9.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.827
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\projectlombok\lombok\1.18.10\lombok-1.18.10.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.828
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\surefire-junit4\3.0.0-M1\surefire-junit4-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.829
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\common-java5\3.0.0-M1\common-java5-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.830
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\common-junit3\3.0.0-M1\common-junit3-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.831
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\surefire\common-junit4\3.0.0-M1\common-junit4-3.0.0-M1.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


# Created at 2025-07-10T14:56:11.833
Boot Manifest-JAR contains absolute paths in classpath D:\apache-maven-3.6.3\.m2\org\apache\maven\shared\maven-shared-utils\3.1.0\maven-shared-utils-3.1.0.jar
java.lang.IllegalArgumentException: 'other' has different root
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:392)
	at sun.nio.fs.WindowsPath.relativize(WindowsPath.java:44)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.toClasspathElementUri(JarManifestForkConfiguration.java:153)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.createJar(JarManifestForkConfiguration.java:120)
	at org.apache.maven.plugin.surefire.booterclient.JarManifestForkConfiguration.resolveClasspath(JarManifestForkConfiguration.java:75)
	at org.apache.maven.plugin.surefire.booterclient.DefaultForkConfiguration.createCommandLine(DefaultForkConfiguration.java:147)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.fork(ForkStarter.java:580)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:283)
	at org.apache.maven.plugin.surefire.booterclient.ForkStarter.run(ForkStarter.java:246)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeProvider(AbstractSurefireMojo.java:1149)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.executeAfterPreconditionsChecked(AbstractSurefireMojo.java:991)
	at org.apache.maven.plugin.surefire.AbstractSurefireMojo.execute(AbstractSurefireMojo.java:837)
	at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo(DefaultBuildPluginManager.java:137)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:210)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:156)
	at org.apache.maven.lifecycle.internal.MojoExecutor.execute(MojoExecutor.java:148)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:117)
	at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject(LifecycleModuleBuilder.java:81)
	at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build(SingleThreadedBuilder.java:56)
	at org.apache.maven.lifecycle.internal.LifecycleStarter.execute(LifecycleStarter.java:128)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:305)
	at org.apache.maven.DefaultMaven.doExecute(DefaultMaven.java:192)
	at org.apache.maven.DefaultMaven.execute(DefaultMaven.java:105)
	at org.apache.maven.cli.MavenCli.execute(MavenCli.java:957)
	at org.apache.maven.cli.MavenCli.doMain(MavenCli.java:289)
	at org.apache.maven.cli.MavenCli.main(MavenCli.java:193)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced(Launcher.java:282)
	at org.codehaus.plexus.classworlds.launcher.Launcher.launch(Launcher.java:225)
	at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode(Launcher.java:406)
	at org.codehaus.plexus.classworlds.launcher.Launcher.main(Launcher.java:347)
	at org.codehaus.classworlds.Launcher.main(Launcher.java:47)


