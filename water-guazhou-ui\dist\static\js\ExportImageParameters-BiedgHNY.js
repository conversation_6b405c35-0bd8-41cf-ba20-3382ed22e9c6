import{e as i,y as l,a as p,v as u}from"./Point-WxyopZva.js";import{f as b,h as d,j as f}from"./MapView-DaoQedLH.js";import{R as g}from"./index-r0dFAfgr.js";import{n as m}from"./floorFilterUtils-DZ5C6FQv.js";import{i as v}from"./sublayerUtils-bmirCD0I.js";const S={visible:"visibleSublayers",definitionExpression:"layerDefs",labelingInfo:"hasDynamicLayers",labelsVisible:"hasDynamicLayers",opacity:"hasDynamicLayers",minScale:"visibleSublayers",maxScale:"visibleSublayers",renderer:"hasDynamicLayers",source:"hasDynamicLayers"};let t=class extends b(u){constructor(e){super(e),this.floors=null,this.scale=0}destroy(){this.layer=null}get dynamicLayers(){if(!this.hasDynamicLayers)return null;const e=this.visibleSublayers.map(s=>{const o=m(this.floors,s);return s.toExportImageJSON(o)});return e.length?JSON.stringify(e):null}get hasDynamicLayers(){return this.layer&&v(this.visibleSublayers,this.layer.serviceSublayers,this.layer.gdbVersion)}set layer(e){this._get("layer")!==e&&(this._set("layer",e),this.handles.remove("layer"),e&&this.handles.add([e.allSublayers.on("change",()=>this.notifyChange("visibleSublayers")),e.on("sublayer-update",s=>this.notifyChange(S[s.propertyName]))],"layer"))}get layers(){const e=this.visibleSublayers;return e?e.length?"show:"+e.map(s=>s.id).join(","):"show:-1":null}get layerDefs(){var o;const e=!!((o=this.floors)!=null&&o.length),s=this.visibleSublayers.filter(a=>a.definitionExpression!=null||e&&a.floorInfo!=null);return s.length?JSON.stringify(s.reduce((a,r)=>{const n=m(this.floors,r),y=d(n,r.definitionExpression);return g(y)&&(a[r.id]=y),a},{})):null}get version(){this.commitProperty("layers"),this.commitProperty("layerDefs"),this.commitProperty("dynamicLayers"),this.commitProperty("timeExtent");const e=this.layer;return e&&(e.commitProperty("dpi"),e.commitProperty("imageFormat"),e.commitProperty("imageTransparency"),e.commitProperty("gdbVersion")),(this._get("version")||0)+1}get visibleSublayers(){const e=[];if(!this.layer)return e;const s=this.layer.sublayers,o=r=>{const n=this.scale,y=n===0,h=r.minScale===0||n<=r.minScale,c=r.maxScale===0||n>=r.maxScale;r.visible&&(y||h&&c)&&(r.sublayers?r.sublayers.forEach(o):e.unshift(r))};s&&s.forEach(o);const a=this._get("visibleSublayers");return!a||a.length!==e.length||a.some((r,n)=>e[n]!==r)?e:a}toJSON(){const e=this.layer;let s={dpi:e.dpi,format:e.imageFormat,transparent:e.imageTransparency,gdbVersion:e.gdbVersion||null};return this.hasDynamicLayers&&this.dynamicLayers?s.dynamicLayers=this.dynamicLayers:s={...s,layers:this.layers,layerDefs:this.layerDefs},s}};i([l({readOnly:!0})],t.prototype,"dynamicLayers",null),i([l()],t.prototype,"floors",void 0),i([l({readOnly:!0})],t.prototype,"hasDynamicLayers",null),i([l()],t.prototype,"layer",null),i([l({readOnly:!0})],t.prototype,"layers",null),i([l({readOnly:!0})],t.prototype,"layerDefs",null),i([l({type:Number})],t.prototype,"scale",void 0),i([l(f)],t.prototype,"timeExtent",void 0),i([l({readOnly:!0})],t.prototype,"version",null),i([l({readOnly:!0})],t.prototype,"visibleSublayers",null),t=i([p("esri.layers.mixins.ExportImageParameters")],t);export{t as c};
