package org.thingsboard.server.dao.model.sql.gis;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_TABLE)
@TableName(ModelConstants.TB_GIS_EXCEPTION_UPLOAD_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class GisExceptionUpload {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_LAYER)
    private String layer;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_FID)
    private String fid;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_UPLOAD_USER)
    private String uploadUser;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_UPLOAD_TIME)
    private Date uploadTime;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_UPLOAD_CONTENT)
    private String uploadContent;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_STATUS)
    private String status;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_REMARK)
    private String remark;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_APPROVAL_USER)
    private String approvalUser;

    @Column(name = ModelConstants.TB_GIS_EXCEPTION_UPLOAD_APPROVAL_TIME)
    private Date approvalTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String uploadUserName;

    @Transient
    @TableField(exist = false)
    private String approvalUserName;

}
