import{d as w,M as D,c as h,r as m,s as f,a0 as P,S as x,o as j,a1 as q,bo as B,i as u,g as F,h as $,F as k,q as g,br as A,ak as N,al as z,bM as E,bq as M}from"./index-r0dFAfgr.js";import{_ as V}from"./TreeBox-DDD2iwoR.js";import{_ as G}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{_ as J}from"./index-BJ-QPYom.js";import{e as K,d as O,s as Q,a as U,p as X}from"./process-DWVjEFpZ.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const le=w({__name:"index",setup(Y){const{$messageError:T,$messageSuccess:b,$messageWarning:S}=D(),W=h(),y=h(),_=h(),C=m({filters:[{type:"input",label:"节点名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新增",svgIcon:f(N),click:()=>v()},{perm:!0,text:"查询",svgIcon:f(z),click:()=>d()}]}]}),p=m({loading:!0,indexVisible:!0,columns:[{label:"编号",prop:"code",minWidth:120,align:"center"},{label:"工程类型",prop:"mainName",minWidth:120,align:"center"},{label:"步骤名称",prop:"stepName",minWidth:120,align:"center"},{label:"附件名称",prop:"name",minWidth:120,align:"center"},{label:"必须数量",prop:"num",minWidth:120,align:"center"},{label:"详细说明",prop:"remark",minWidth:120,align:"center"}],dataList:[],operationFixed:"right",operationWidth:180,operations:[{perm:!0,text:"编辑",isTextBtn:!1,svgIcon:f(E),click:e=>v(e)},{perm:!0,text:"删除",isTextBtn:!1,type:"danger",svgIcon:f(M),click:e=>I([e.id])}],pagination:{hide:!0}}),i=m({title:"新增",labelWidth:120,dialogWidth:500,group:[{fields:[{type:"select",label:"工程步骤",field:"stepId",options:[],rules:[{required:!0,message:"请选择工程步骤"}]},{type:"input",label:"附件名称",field:"name",rules:[{required:!0,message:"请填写附件名称"}],placeholder:"请填写附件名称"},{type:"input-number",label:"必须数量",field:"num",rules:[{required:!0,message:"请填写数量"}],placeholder:"请填写数量"},{type:"textarea",label:"详细说明",field:"remark",placeholder:"请填写详细说明"}]}]}),r=m({title:"工程类型",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{r.currentProject=e,P().SET_selectedProject(e),d()}}),v=async e=>{var n;const a=(await L()).map(t=>({label:t.name,value:t.id})),s=i.group[0].fields.find(t=>t.field==="stepId");s.options=a,i.defaultValue={...e||{}},i.submit=t=>{x("确定提交？","提示信息").then(()=>{var l;i.submitting=!0,t={...t,mainId:(l=r.currentProject)==null?void 0:l.value},K(t).then(()=>{var c;(c=_.value)==null||c.closeDialog(),i.submitting=!1,b("保存成功"),d()}).catch(c=>{T(c),i.submitting=!1})})},(n=_.value)==null||n.openDialog()},I=e=>{x("确定删除？","提示信息").then(()=>{O(e).then(()=>{b("删除成功"),d()}).catch(o=>{S(o)})})},d=async()=>{var s,n,t,l;p.loading=!0;const e=((s=y.value)==null?void 0:s.queryParams)||{},o={page:1,size:9999,mainId:(n=r.currentProject)==null?void 0:n.value,...e},a=await Q(o);p.dataList=(l=(t=a.data)==null?void 0:t.data)==null?void 0:l.data,p.loading=!1},L=async()=>{var a,s,n;const e={page:1,size:9999,mainId:(a=r.currentProject)==null?void 0:a.value};return(n=(s=(await U(e)).data)==null?void 0:s.data)==null?void 0:n.data};return j(async()=>{var a;const o=(a=(await X({page:1,size:9999})).data)==null?void 0:a.data.data;r.data=q(o||[]),r.currentProject=r.data[0],await d()}),(e,o)=>{const a=J,s=R,n=H,t=G,l=V,c=A;return B((F(),$(l,null,{tree:k(()=>[g(a,{"tree-data":u(r)},null,8,["tree-data"])]),default:k(()=>[g(s,{ref_key:"refSearch",ref:y,config:u(C)},null,8,["config"]),g(n,{ref_key:"refTable",ref:W,config:u(p),class:"card-table"},null,8,["config"]),g(t,{ref_key:"refForm",ref:_,config:u(i)},null,8,["config"])]),_:1})),[[c,!!u(r).loading]])}}});export{le as default};
