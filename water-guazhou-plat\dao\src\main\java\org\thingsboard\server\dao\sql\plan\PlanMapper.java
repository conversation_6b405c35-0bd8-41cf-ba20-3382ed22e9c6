package org.thingsboard.server.dao.sql.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.plan.Plan;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanPageRequest;

@Mapper
public interface PlanMapper extends BaseMapper<Plan> {
    IPage<Plan> findByPage(PlanPageRequest request);

    boolean update(Plan plan);


}
