package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderCollaboration;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class WorkOrderCollaborationPageRequest extends AdvancedPageableQueryEntity<WorkOrderCollaboration, WorkOrderCollaborationPageRequest> {
    // 需要协作的工单ID
    private String orderId;

    // 协作工单发起类型。直接发起工单协作/申请协作
    private String type;

    // 申请人ID
    private String userId;

    // 状态。待审核/通过/未通过
    private String status;
}
