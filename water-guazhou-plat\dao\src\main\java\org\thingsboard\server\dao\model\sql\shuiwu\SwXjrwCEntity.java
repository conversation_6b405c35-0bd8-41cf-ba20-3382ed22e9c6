package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 水务-巡检任务子表
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_XJRW_C_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SwXjrwCEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_MAIN_ID)
    private String mainId;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_C_ORDER_NUMBER)
    private Integer orderNum;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.SHUIWU_CRITERION_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_CRITERION_NAME)
    private String criterionName;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_CRITERION_DETAIL)
    private String criterionDetail;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_STATUS)
    private String status;

    @Column(name = ModelConstants.START_TIME)
    private Date startTime;

    @Column(name = ModelConstants.END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.USER_ID_PROPERTY)
    private String userId;

    @Column(name = ModelConstants.REMARK)
    private String remark;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_RECORD_STATUS)
    private String recordStatus;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_IMGS)
    private String imgs;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_VOICE_FILE)
    private String voiceFile;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_OK)
    private Boolean ok;

    @Column(name = ModelConstants.SHUIWU_XJRW_C_JOB_REMARK)
    private String  jobRemark;

    @Transient
    private String userName;

    @Transient
    private String deviceName;

    @Transient
    private String projectName;

    @Transient
    private String deviceAddress;

    @Transient
    private Double latitude;

    @Transient
    private Double longitude;

    public static SwXjrwCEntity buildXJRWC(SwXjrwglCEntity xjrwglc) {
        SwXjrwCEntity xjrwcEntity = new SwXjrwCEntity();
        xjrwcEntity.setProjectId(xjrwglc.getProjectId());
        xjrwcEntity.setDeviceId(xjrwglc.getDeviceId());
        xjrwcEntity.setCreateTime(new Date());
        xjrwcEntity.setTenantId(xjrwglc.getTenantId());
        xjrwcEntity.setOrderNum(xjrwglc.getOrderNum());
        xjrwcEntity.setDeviceType(xjrwglc.getDeviceType());
        xjrwcEntity.setJobRemark(xjrwglc.getRemark());

        return xjrwcEntity;
    }
}
