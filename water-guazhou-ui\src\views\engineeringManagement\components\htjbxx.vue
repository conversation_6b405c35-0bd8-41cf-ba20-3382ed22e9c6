<!-- 工程管理-详情-合同基本信息 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '合同基本信息',
  fields: [
    { type: 'text', label: '合同编号:', field: 'code' },
    { type: 'text', label: '合同名称:', field: 'name' },
    { type: 'text', label: '合同类型:', field: 'typeName' },
    { type: 'text', label: '甲方单位:', field: 'firstpartOrganization' },
    { type: 'text', label: '甲方代表:', field: 'firstpartRepresentative' },
    { type: 'text', label: '甲方代表联系电话:', field: 'firstpartPhone' },
    { type: 'text', label: '乙方单位:', field: 'secondpartOrganization' },
    { type: 'text', label: '乙方联系人:', field: 'secondpartRepresentative' },
    { type: 'text', label: '乙方联系电话:', field: 'secondpartPhone' },
    { type: 'text', label: '合同金额(万元):', field: 'cost' },
    { type: 'text', label: '合同工期(开始时间):', field: 'workTimeBeginName' },
    { type: 'text', label: '合同工期(完成时间):', field: 'workTimeEndName' },
    { type: 'text', label: '签署时间:', field: 'signTimeName' },
    { type: 'text', label: '详细说明:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
