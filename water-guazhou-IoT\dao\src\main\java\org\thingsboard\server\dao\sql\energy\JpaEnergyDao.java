/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.energy;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.energy.Energy;
import org.thingsboard.server.common.data.id.EnergyId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.energy.EnergyDao;
import org.thingsboard.server.dao.model.sql.EnergyEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
public class JpaEnergyDao extends JpaAbstractSearchTextDao<EnergyEntity, Energy> implements EnergyDao {

    @Autowired
    private EnergyRepository energyRepository;

    @Override
    public ListenableFuture<List<Energy>> findByTenantId(TenantId tenantId) {
        return service.submit(() -> DaoUtil.convertDataList(energyRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()))));
    }

    @Override
    public ListenableFuture<Energy> findById(EnergyId energyId) {
        return service.submit(()->findById(energyId.getId()));
    }

    @Override
    protected Class<EnergyEntity> getEntityClass() {
        return EnergyEntity.class;
    }

    @Override
    protected CrudRepository<EnergyEntity, String> getCrudRepository() {
        return energyRepository;
    }
}
