// import Extent from '@arcgis/core/geometry/Extent.js'
import { queryLayerClassName } from '@/api/mapservice';
import {
  excuteQuery,
  getGraphicLayer,
  getPipeMapLayerMinIndex,
  getSubLayerIds,
  initQueryParams,
  setSymbol,
  submitBurstAnalysGPJob,
  submitExtendShutValvesGPJob
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { GetFieldConfig } from '@/api/mapservice/fieldconfig';
import { useGisStore } from '@/store';

export const useAnalys = () => {
  const isAnalys = ref<boolean>(false);
  const isExtendAnalys = ref<boolean>(false);
  const tabs = ref<any[]>([]);
  const mustshutOids = ref<any[]>([]);
  const staticState: {
    view?: __esri.MapView;
    resultLayer?: __esri.MapImageLayer;
    mustShutValveLayer?: __esri.GraphicsLayer;
    mustShutVolveFeatures: __esri.Graphic[];
    resultSummary?: any;
  } = {
    mustShutVolveFeatures: []
  };

  const TableConfig_EffectRange = ref<IAttrTable>({
    data: [],
    columns: []
  });

  const TableConfig_MustShut = ref<ITable>({
    handleSelectChange: (rows: any[]) => {
      if (!staticState.view) return;
      TableConfig_MustShut.value.selectList = rows || [];
      const sids = rows.map((item) => item.SID);
      const features = staticState.mustShutVolveFeatures
        .filter((item) => sids.indexOf(item.attributes.SID) !== -1)
        .map((item) => {
          item.symbol = setSymbol('point', {
            color: [0, 255, 255],
            outlineColor: [255, 0, 255],
            outlineWidth: 2
          });
          return item;
        });
      staticState.mustShutValveLayer?.removeAll();
      staticState.mustShutValveLayer?.addMany(features);
      // staticState.mustShutValveLayer = getGraphicLayer(staticState.view, {
      //   id: 'extentMustShutValveLayer',
      //   title: '二次关阀'
      // })
      // staticState.extentMustShutValveLayer?.removeAll()
      // staticState.extentMustShutValveLayer?.addMany(features)
    },
    handleRowClick: (row) => {
      const feature = staticState.mustShutVolveFeatures.find(
        (item) => item.attributes.SID === row.SID
      );
      feature && staticState.view?.goTo(feature);
    },
    dataList: [],
    columns: [
      { label: '编号', prop: 'SID' },
      { label: '阀门级别', prop: 'VALVECLASS' }
    ],
    pagination: {
      hide: true
    }
  });
  // const TableConfig_ExtentAnalys = ref<ITable>({
  //   handleSelectChange: (rows: any[]) => {
  //     if (!staticState.view) return
  //     TableConfig_ExtentAnalys.value.selectList = rows || []
  //     const sids = rows.map(item => item.SID)
  //     const features = staticState.mustShutVolveFeatures
  //       .filter(item => sids.indexOf(item.attributes.SID) !== -1)
  //       .map(item => {
  //         item.symbol = setSymbol('point', {
  //           color: [0, 255, 255],
  //           outlineColor: [255, 0, 255],
  //           outlineWidth: 2
  //         })
  //         return item
  //       })
  //     staticState.mustShutValveLayer?.removeAll()
  //     staticState.mustShutValveLayer?.addMany(features)
  //     // staticState.mustShutValveLayer = getGraphicLayer(staticState.view, {
  //     //   id: 'extentMustShutValveLayer',
  //     //   title: '二次关阀'
  //     // })
  //     // staticState.extentMustShutValveLayer?.removeAll()
  //     // staticState.extentMustShutValveLayer?.addMany(features)
  //   },
  //   dataList: [],
  //   columns: [
  //     { label: '编号', prop: 'SID' },
  //     { label: '阀门级别', prop: 'VALVECLASS' }
  //   ],
  //   pagination: {
  //     hide: true
  //   }
  // })
  const init = async (
    mapView?: __esri.MapView,
    isExtend?: boolean,
    layerId?: number,
    oId?: string | string[]
  ) => {
    if (!mapView) return;
    staticState.view = mapView;
    try {
      staticState.resultLayer &&
        staticState.view?.map.remove(staticState.resultLayer);
      // staticState.extendResultLayer
      //   && staticState.view?.map.remove(staticState.extendResultLayer)
      let jobinfo: __esri.JobInfo | undefined;
      if (!isExtend) {
        if (layerId === undefined) {
          isAnalys.value = false;
          return;
        }
        isAnalys.value = true;
        const res = await queryLayerClassName(layerId);
        const datas = res.data?.result?.rows;
        const layerdbName = datas?.length && datas[0].layerdbname;
        jobinfo = await submitBurstAnalysGPJob(layerdbName, oId);
      } else {
        isExtendAnalys.value = true;
        const valves = typeof oId === 'string' ? oId : oId?.join(',');
        if (!valves?.length) {
          isExtendAnalys.value = false;
          return;
        }
        const valveLayerDbName = 'valve';
        jobinfo = await submitExtendShutValvesGPJob({
          bysource: true,
          usertoken: useGisStore().gToken,
          valves: valveLayerDbName + ':' + (valves || '')
        });
      }

      await jobinfo.waitForJobCompletion();
      if (jobinfo.jobStatus === 'job-succeeded') {
        staticState.resultLayer &&
          staticState.view.map.remove(staticState.resultLayer);
        staticState.resultLayer = await jobinfo.fetchResultMapImageLayer(
          jobinfo.jobId
        );
        staticState.resultLayer.title = isExtend
          ? '扩展分析结果'
          : '爆管分析结果';
        const pipeMapIndex = getPipeMapLayerMinIndex(staticState.view);
        staticState.view?.map.add(staticState.resultLayer, pipeMapIndex);

        const res = await jobinfo.fetchResultData('summary');

        const value: any = res.value;
        if (value?.code !== 10000) {
          SLMessage.error(value.error);
        } else {
          staticState.resultSummary = value?.result?.summary || [];
          showAnalyzsSummary();
          // 查询必关阀
          tabs.value =
            staticState.resultSummary?.layersummary.map((item) => {
              return {
                label: item.layername,
                name: item.layername,
                data: []
              };
            }) || [];
          mustshutOids.value.length = 0;
          await setTabOids(tabs.value, 0);
          await getShutValves();
          staticState.mustShutValveLayer = getGraphicLayer(staticState.view, {
            id: 'mustShutValveLayer',
            title: '必关阀'
          });
          staticState.mustShutValveLayer?.addMany(
            staticState.mustShutVolveFeatures
          );
        }
      } else if (jobinfo.jobStatus === 'job-cancelled') {
        SLMessage.info('已取消分析');
      } else if (jobinfo.jobStatus === 'job-cancelling') {
        SLMessage.info('任务正在取消');
      } else if (jobinfo.jobStatus === 'job-failed') {
        SLMessage.info('分析失败，请联系管理员');
      }
    } catch (error) {
      SLMessage.info('分析失败，请联系管理员');
    }
    isAnalys.value = false;
    isExtendAnalys.value = false;
  };

  const showAnalyzsSummary = () => {
    const summary = staticState.resultSummary;
    if (summary?.layersummary?.length) {
      const summaryTData: any = {};
      const columns: IAttrTableRow[][] = [];
      summary.layersummary.forEach((value) => {
        summaryTData[value.layerdbname] =
          value.geometrytype === 'esriGeometryPoint'
            ? value.count + '个'
            : value.length + '米';
        columns.push([{ label: value.layername, prop: value.layerdbname }]);
      });
      TableConfig_EffectRange.value.data = summaryTData;
      TableConfig_EffectRange.value.columns = columns;
    }
    // let xmin: number = summary.xmin || staticState.view?.extent?.xmin
    // let xmax: number = summary.xmax || staticState.view?.extent?.xmax
    // let ymin: number = summary.ymin || staticState.view?.extent?.ymin
    // let ymax: number = summary.ymax || staticState.view?.extent?.ymax
    // const width = xmax - xmin
    // const height = ymax - ymin
    // xmin -= width / 2
    // xmax += width / 2
    // ymin -= height / 2
    // ymax += height / 2
    // staticState.view?.goTo(
    //   new Extent({
    //     xmin,
    //     ymin,
    //     xmax,
    //     ymax,
    //     spatialReference: staticState.view?.spatialReference
    //   })
    // )
  };

  const setTabOids = async (tabs, index) => {
    if (index < tabs.length) {
      const tab = tabs[index];
      tab.data = await getTempOids(tab.name);
      index < tabs.length - 1 && (await setTabOids(tabs, ++index));
    }
  };
  const getTempOids = async (tab: string) => {
    try {
      const geometrytype = staticState.resultSummary?.layersummary?.find(
        (item) => item.layername === tab
      )?.geometrytype;
      const layerIndex =
        geometrytype === 'esriGeometryPolyline'
          ? 1
          : geometrytype === 'esriGeometryPoint'
            ? 0
            : -1;
      const res = await excuteQuery(
        (staticState.resultLayer?.url || '') + '/' + layerIndex,
        initQueryParams({
          where: "layername='" + tab + "'",
          outFields:
            geometrytype === 'esriGeometryPoint'
              ? ['sourceoid', 'mustshut']
              : ['sourceoid'],
          returnGeometry: false
        })
      );
      const mustshut = res.features
        ?.filter((item) => item.attributes.mustshut === 1)
        .map((item) => item.attributes.sourceoid);
      mustshutOids.value = mustshutOids.value.concat(...mustshut);
      let alloids = res.features?.map((item) => item.attributes.sourceoid);
      if (alloids === null) {
        alloids = await getTempOids(tab);
      }
      return alloids;
    } catch (error) {
      return [];
    }
  };

  const getShutValves = async () => {
    if (!staticState.view) return;
    isAnalys.value && staticState.mustShutValveLayer?.removeAll();
    try {
      TableConfig_MustShut.value.loading = true;
      const tableData: any[] = [];
      if (mustshutOids.value.length) {
        const layerIndex = getSubLayerIds(
          staticState.view,
          undefined,
          undefined,
          '阀门'
        );
        const outFields = await GetFieldConfig('阀门');
        const valves = await excuteQuery(
          window.SITE_CONFIG.GIS_CONFIG.gisService +
            window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
            '/' +
            layerIndex[0],
          initQueryParams({
            objectIds: mustshutOids.value,
            outFields:
              outFields.data?.result?.rows
                ?.filter((item) => item.visible)
                .map((item) => item.name) || [],
            returnGeometry: true
          })
        );
        valves.features?.map((item) => {
          tableData.push({
            ...(item.attributes || {}),
            layerId: layerIndex[0]
          });
          item.symbol = setSymbol('point', {
            color: [0, 255, 255],
            outlineColor: [255, 0, 255],
            outlineWidth: 2
          });
        });
        staticState.mustShutVolveFeatures = valves.features;
      } else {
        staticState.mustShutVolveFeatures = [];
      }

      TableConfig_MustShut.value.dataList = tableData;
      // TableConfig_ExtentAnalys.value.dataList = tableData
      TableConfig_MustShut.value.selectList = tableData;
    } catch (error) {
      SLMessage.error('必关阀查询失败');
      console.dir(error);
    }
    TableConfig_MustShut.value.loading = false;
    // TableConfig_ExtentAnalys.value.loading = false
  };
  const destroy = () => {
    staticState.resultLayer &&
      staticState.view?.map.remove(staticState.resultLayer);
    staticState.mustShutValveLayer &&
      staticState.view?.map.remove(staticState.mustShutValveLayer);
    staticState.mustShutVolveFeatures.length = 0;
    staticState.resultSummary = undefined;
    staticState.view = undefined;
    TableConfig_EffectRange.value.data = [];
    TableConfig_MustShut.value.dataList = [];
    TableConfig_MustShut.value.selectList = [];
  };
  const toggleResultLayer = (val?: boolean) => {
    staticState.resultLayer && (staticState.resultLayer.visible = !!val);
  };
  onBeforeUnmount(() => {
    destroy();
  });
  return {
    init,
    destroy,
    tabs,
    isAnalys,
    isExtendAnalys,
    TableConfig_EffectRange,
    TableConfig_MustShut,
    toggleResultLayer,
    staticState
  };
};

export default useAnalys;
