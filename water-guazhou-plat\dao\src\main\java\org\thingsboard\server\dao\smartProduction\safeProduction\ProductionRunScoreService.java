package org.thingsboard.server.dao.smartProduction.safeProduction;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionRunScore;

import java.util.List;
import java.util.Map;

public interface ProductionRunScoreService {

    List<ProductionRunScore> findList(SafeProductionRequest request);

    String batchSave(List<ProductionRunScore> productionRunScores);

    List<JSONObject> yearCount(String year);

    Object yearSum(String year);

    List<JSONObject> scoreRanking(String year);

    Map<String, Map<String, ProductionRunScore>> getMap(SafeProductionRequest request);
}
