import{d as n,a0 as c,c as i,o as v,Q as r,g as p,n as f,p as t,dy as b,i as l,C as _}from"./index-r0dFAfgr.js";import{r as o}from"./水泵_在线-2IzRX-2K.js";const x={class:"main"},h={class:"card zutai-card"},u={class:"card-content",style:{top:"36%",right:"15%",width:"120px"}},g={class:"row"},y={class:"status"},w=["src"],m={class:"card-content",style:{top:"47%",right:"15%",width:"120px"}},B={class:"row"},I={class:"status"},S=["src"],j=n({__name:"qsc_overview1",setup(k){const a=c();i({});const e=i(),d=()=>{console.log(a.projectList),a.projectList[0].id};return v(()=>{d(),e.value=setInterval(()=>{d()},3e4)}),r(()=>{clearInterval(e.value)}),(q,s)=>(p(),f("div",x,[t("div",h,[s[4]||(s[4]=b('<div class="card-content" style="top:20%;left:47%;width:140px;" data-v-461590eb><div class="card-title" data-v-461590eb><span style="color:#d8feff;text-align:center;" data-v-461590eb>清水池1</span></div></div><div class="card-content" style="top:37%;right:2%;width:170px;" data-v-461590eb><div class="card-title" data-v-461590eb><span style="color:#d8feff;text-align:center;" data-v-461590eb>周河生活用水</span></div></div><div class="card-content" style="bottom:20%;right:1%;width:170px;" data-v-461590eb><div class="card-title" data-v-461590eb><span style="color:#d8feff;text-align:center;" data-v-461590eb>消防用水</span></div></div><div class="card-content" style="top:58%;right:15%;width:120px;" data-v-461590eb><div class="card-title" data-v-461590eb><span style="color:#d8feff;text-align:center;" data-v-461590eb>1#出水电动阀</span></div><div class="row" data-v-461590eb><div class="label" data-v-461590eb>状态：</div><div class="status online" data-v-461590eb></div></div></div><div class="card-content" style="top:62%;right:25%;width:120px;" data-v-461590eb><div class="card-title" data-v-461590eb><span style="color:#d8feff;text-align:center;" data-v-461590eb>2#出水电动阀</span></div><div class="row" data-v-461590eb><div class="label" data-v-461590eb>状态：</div><div class="status online" data-v-461590eb></div></div></div>',5)),t("div",u,[s[1]||(s[1]=t("div",{class:"card-title"},[t("span",{style:{color:"#d8feff","text-align":"center"}},"1#出水泵")],-1)),t("div",g,[s[0]||(s[0]=t("div",{class:"label"},"状态：",-1)),t("div",y,[t("img",{src:l(o),style:{width:"15px",height:"15px"}},null,8,w)])])]),t("div",m,[s[3]||(s[3]=t("div",{class:"card-title"},[t("span",{style:{color:"#d8feff","text-align":"center"}},"2#出水泵")],-1)),t("div",B,[s[2]||(s[2]=t("div",{class:"label"},"状态：",-1)),t("div",I,[t("img",{src:l(o),style:{width:"15px",height:"15px"}},null,8,S)])])])])]))}}),N=_(j,[["__scopeId","data-v-461590eb"]]);export{N as default};
