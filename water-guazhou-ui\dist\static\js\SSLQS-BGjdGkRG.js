import{d as a,r as o,ay as s,g as n,n as r,q as i,i as l,C as p}from"./index-r0dFAfgr.js";const c={class:"chart-wrapper"},d=a({__name:"SSLQS",setup(x){const e=o({option:{tooltip:{trigger:"axis"},legend:{textStyle:{color:"rgb(60, 148, 221)"},bottom:20},grid:{containLabel:!0,left:20,top:40,right:20,bottom:40},xAxis:{type:"category",data:["01月","02月","03月","04月"],boundaryGap:!1},yAxis:[{type:"value",name:"m³",axisLine:{show:!1},axisTick:{show:!1},splitLine:{lineStyle:{color:"rgb(60, 148, 221)"}}},{type:"value",name:"元",axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}}],series:[{name:"水量",type:"bar",xAxisIndex:0,data:[100,200,300,100],barWidth:15},{name:"同期水量",type:"bar",yAxisIndex:0,data:[178,321,456,211],barWidth:15},{name:"水费",type:"line",yAxisIndex:1,smooth:!0,symbol:"none",data:[200,400,600,200]}]}});return(m,_)=>{const t=s("VChart");return n(),r("div",c,[i(t,{ref:"refChart",option:l(e).option},null,8,["option"])])}}}),h=p(d,[["__scopeId","data-v-456807e3"]]);export{h as default};
