package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTileConfigurationPageRequest;

import java.util.List;

/**
 * 公共管理平台-瓦片数据配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Mapper
public interface BaseTileConfigurationMapper {
    /**
     * 查询公共管理平台-瓦片数据配置
     *
     * @param id 公共管理平台-瓦片数据配置主键
     * @return 公共管理平台-瓦片数据配置
     */
    public BaseTileConfiguration selectBaseTileConfigurationById(String id);

    /**
     * 查询公共管理平台-瓦片数据配置列表
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 公共管理平台-瓦片数据配置集合
     */
    public IPage<BaseTileConfiguration> selectBaseTileConfigurationList(BaseTileConfigurationPageRequest baseTileConfiguration);

    /**
     * 新增公共管理平台-瓦片数据配置
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 结果
     */
    public int insertBaseTileConfiguration(BaseTileConfiguration baseTileConfiguration);

    /**
     * 修改公共管理平台-瓦片数据配置
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 结果
     */
    public int updateBaseTileConfiguration(BaseTileConfiguration baseTileConfiguration);

    /**
     * 删除公共管理平台-瓦片数据配置
     *
     * @param id 公共管理平台-瓦片数据配置主键
     * @return 结果
     */
    public int deleteBaseTileConfigurationById(String id);

    /**
     * 批量删除公共管理平台-瓦片数据配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseTileConfigurationByIds(@Param("array") List<String> ids);

    public List<BaseTileConfiguration> selectAllBaseTileConfiguration();
}
