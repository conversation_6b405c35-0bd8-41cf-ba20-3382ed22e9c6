package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement;
import org.thingsboard.server.dao.base.IBaseSchemeManagementService;
import org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseSchemeManagementPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-方案管理Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Api(tags = "平台管理-方案管理")
@RestController
@RequestMapping("api/base/scheme/management")
public class BaseSchemeManagementController extends BaseController {

    @Autowired
    private IBaseSchemeManagementService baseSchemeManagementService;

    /**
     * 查询平台管理-方案管理列表
     */
    @MonitorPerformance(description = "平台管理-查询方案管理列表接口")
    @ApiOperation(value = "查询方案管理列表")
    @GetMapping("/list")
    public IstarResponse list(BaseSchemeManagementPageRequest baseSchemeManagement) {
        return IstarResponse.ok(baseSchemeManagementService.selectBaseSchemeManagementList(baseSchemeManagement));
    }

    /**
     * 获取平台管理-方案管理详细信息
     */
    @MonitorPerformance(description = "平台管理-查询方案管理详情接口")
    @ApiOperation(value = "查询方案管理详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseSchemeManagementService.selectBaseSchemeManagementById(id));
    }

    /**
     * 新增平台管理-方案管理
     */
    @MonitorPerformance(description = "平台管理-新增方案管理接口")
    @ApiOperation(value = "新增方案管理")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseSchemeManagement baseSchemeManagement) {
        return IstarResponse.ok(baseSchemeManagementService.insertBaseSchemeManagement(baseSchemeManagement));
    }

    /**
     * 修改平台管理-方案管理
     */
    @MonitorPerformance(description = "平台管理-修改方案管理接口")
    @ApiOperation(value = "修改方案管理")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseSchemeManagement baseSchemeManagement) {
        return IstarResponse.ok(baseSchemeManagementService.updateBaseSchemeManagement(baseSchemeManagement));
    }

    /**
     * 删除平台管理-方案管理
     */
    @MonitorPerformance(description = "平台管理-删除方案管理接口")
    @ApiOperation(value = "删除方案管理")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseSchemeManagementService.deleteBaseSchemeManagementByIds(ids));
    }

    /**
     * 获取所有的方案管理数据
     * @return
     */
    @MonitorPerformance(description = "平台管理-获取所有的方案管理数据接口")
    @ApiOperation(value = "获取所有的方案管理数据")
    @GetMapping("/getAllBaseSchemeManagement")
    public List<BaseSchemeManagement> getAllBaseSchemeManagement() {
        return baseSchemeManagementService.getAllBaseSchemeManagement();
    }
}
