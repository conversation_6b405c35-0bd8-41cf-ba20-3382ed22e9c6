package org.thingsboard.server.utils.imodel.validate;

import org.springframework.stereotype.Component;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNull;
import org.thingsboard.server.dao.util.reflection.BeanWrapper;
import org.thingsboard.server.dao.util.reflection.FieldWrapper;
import org.thingsboard.server.utils.imodel.aop.ValidatePerformer;

@Deprecated
@Component
public class NotNullValidator implements ValidatePerformer {
    @Override
    public boolean match(FieldWrapper field) {
        return field.isAnnotationPresent(NotNull.class);
    }

    @Override
    public String resolveString(BeanWrapper bean, FieldWrapper field, String value, boolean hasParent) {
        return checkNonNull(bean, field, value);
    }

    @Override
    public String resolveDestType(BeanWrapper bean, FieldWrapper field, Class<?> type, Object value, boolean hasParent) {
        return checkNonNull(bean, field, value);
    }

    private String checkNonNull(BeanWrapper bean, FieldWrapper field, Object value) {
        String condition = field.getAnnotation(NotNull.class).condition();
        if (callConditionalGetter(bean, condition))
            return null;

        return value == null ? "必要参数未传入：" + field.getName() : null;
    }

    @Override
    public int getOrder() {
        return 1000;
    }
}
