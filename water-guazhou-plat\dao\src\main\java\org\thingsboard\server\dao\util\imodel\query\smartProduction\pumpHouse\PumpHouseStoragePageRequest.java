package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class PumpHouseStoragePageRequest extends AdvancedPageableQueryEntity<PumpHouseStorage, PumpHouseStoragePageRequest> {

    // 泵房编号
    private String code;

    // 泵房名称
    private String name;

    // 泵房简称
    private String nickname;

    // 厂家名称
    private String companyName;

    // 供水方式
    private String supplyMethod;

    // 水箱个数
    private Integer waterBoxNum;

    // 安装人名称
    private String installUserName;

    // 安装时间
    private String installDateFrom;

    // 安装时间
    private String installDateTo;

    // 采集频率（分钟）
    private Integer collectionFrequency;

    // 存储频率（分钟）
    private Integer storageFrequency;

    public Date getInstallDateFrom() {
        return toDate(installDateFrom);
    }

    public Date getInstallDateTo() {
        return toDate(installDateTo);
    }

}
