package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceSettleJournal;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class DeviceSettleJournalPageRequest extends AdvancedPageableQueryEntity<DeviceSettleJournal, DeviceSettleJournalPageRequest> {
    // 设备标签
    private String deviceLabelCode;

    // 安装人员ID
    private String installUserId;

    // 施工项目ID
    private String projectId;

    // 安装时间
    private String installTime;

    // 安装位置ID
    private String installAddressId;

    // 安装位置描述
    private String address;

    public Date getInstallTime() {
        return toDate(installTime);
    }
}
