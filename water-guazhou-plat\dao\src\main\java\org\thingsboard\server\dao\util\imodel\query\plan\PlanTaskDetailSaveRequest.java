package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.plan.PlanTaskDetail;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class PlanTaskDetailSaveRequest extends SaveRequest<PlanTaskDetail> {

    // 主表ID
    @NotNullOrEmpty(parentIgnore = true)
    private String mainId;

    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 货架ID
    @NotNullOrEmpty
    private String shelvesId;

    // 盘点数量（正常）
    private Double normalNum;

    // 盘点数量（异常）
    private Double exceptionNum;

    // 盘点说明
    private String remark;

    public PlanTaskDetail build() {
        PlanTaskDetail entity = new PlanTaskDetail();
        entity.setTenantId(tenantId());
        entity.setMainId(mainId);
        commonSet(entity);
        return entity;
    }

    public PlanTaskDetail update(String id) {
        disallowUpdate();
        PlanTaskDetail entity = new PlanTaskDetail();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(PlanTaskDetail entity) {
        entity.setSerialId(serialId);
        entity.setShelvesId(shelvesId);
        entity.setNormalNum(normalNum);
        entity.setExceptionNum(exceptionNum);
        entity.setRemark(remark);
    }
}