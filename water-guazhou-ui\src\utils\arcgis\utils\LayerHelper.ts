import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import MapImageLayer from '@arcgis/core/layers/MapImageLayer';
import { QueryPipeDataService } from '@/api/mapservice/pipe';
import { queryLayerClassName } from '@/api/mapservice';

/**
 * 查询layer,没有会自动添加
 * @param view
 * @param options
 */
export const getGraphicLayer = (
  view?: __esri.MapView,
  options?: {
    id?: string;
    title?: string;
  }
): __esri.GraphicsLayer | undefined => {
  if (options?.id) {
    let layer = view?.map.findLayerById(options.id) as __esri.GraphicsLayer;
    if (!layer) {
      layer = new GraphicsLayer({
        ...(options || {})
      });
      view?.map.add(layer);
    }
    return layer;
  }
};

export const getFeatureLayer = (
  view?: __esri.MapView,
  options?: {
    url?: string;
    definitionExpression?: string;
    outFields?: string[];
    id: string;
    title?: string;
  }
): __esri.FeatureLayer | undefined => {
  if (options?.id) {
    let layer = view?.map.findLayerById(options.id) as __esri.FeatureLayer;
    if (!layer) {
      layer = new FeatureLayer({
        outFields: ['*'],
        spatialReference: view?.spatialReference,
        ...(options || {})
      });
      view?.map.add(layer);
    }
    return layer;
  }
};
export const createFeatureLayer = (options: __esri.FeatureLayerProperties) => {
  return new FeatureLayer({
    outFields: ['*'],
    ...options
  }) as __esri.FeatureLayer;
};

export const getLayer = (
  view: __esri.MapView,
  options: {
    id?: string;
    title?: string;
    type?: 'MapImageLayer' | 'GraphicsLayer';
    url?: string;
  }
): __esri.Layer | undefined => {
  if (options.id) {
    let layer = view.map.findLayerById(options.id);
    if (!layer) {
      if (options.type === 'GraphicsLayer') {
        layer = new GraphicsLayer({
          id: options.id,
          title: options.title
        });
      } else if (options.type === 'MapImageLayer') {
        layer = new MapImageLayer({
          url: options.url,
          id: options.id,
          title: options.title
        });
      }
      layer && view.map.add(layer);
    }
    return layer;
  }
};
/**
 * 获取管线图层最小图层号
 * @param view 地图视图
 * @returns number
 */
export const getPipeMapLayerMinIndex = (view?: __esri.MapView) => {
  let layerIndex = 100;
  view?.map.layers.forEach((layer: any, index) => {
    if (layer.id === '"pipelayer"') {
      if (layerIndex > index) layerIndex = index;
    }
  });
  return layerIndex;
};

/**
 * 获取图层子数组
 * @param view
 * @param pipeLayerId
 * @param visible 是否只获取可见/不可见的子图层id
 */
export const getSubLayers = (
  view?: __esri.MapView,
  visible?: boolean,
  pipeLayerId?: string
) => {
  const pipeLayer = view?.map.findLayerById(
    pipeLayerId || 'pipelayer'
  ) as __esri.MapImageLayer;
  const subLayers: any[] = [];
  pipeLayer.sublayers
    .filter((item) => {
      if (visible === undefined) return true;
      return item.visible === visible;
    })
    .map((item) => {
      subLayers.push(item);
    });
  return subLayers;
};
/**
 * 获取图层子id数组
 * @param view
 * @param pipeLayerId
 * @param visible 是否只获取可见/不可见的子图层id
 */
export const getSubLayerIds = (
  view?: __esri.MapView,
  visible?: boolean,
  pipeLayerId?: string,
  layername?: string
) => {
  const pipeLayer = view?.map.findLayerById(pipeLayerId || 'pipelayer') as
    | __esri.MapImageLayer
    | undefined;
  const subLayerIds: (number | string)[] = [];
  pipeLayer?.sublayers
    ?.filter((item) => {
      if (visible === undefined) {
        return layername ? item.title === layername : true;
      }
      return (
        item.visible === visible &&
        (layername ? item.title === layername : true)
      );
    })
    .map((item) => {
      subLayerIds.push(item.id);
    });
  return subLayerIds;
};

/**
 * 查询指定图层名对应的索引
 * @param layername
 * @returns index 不存在则返回-1
 */
export const querySourceLayerIndex = async (layername) => {
  const res = await QueryPipeDataService();
  const data = res.data?.layers || [];
  // 获取图层信息，查找指定图层的id
  const index = data.findIndex((o: any) => {
    return o.name === layername;
  });
  return index;
};

/**
 * 获取管线图层Option
 * @param view
 * @returns
 */
export const getPipeLineLayerOption = async (view?: __esri.MapView) => {
  if (!view) return [];
  const pipeLayerOption: NormalOption[] = [];
  const sublayers = getSubLayerIds(view);
  const layersres = await queryLayerClassName(sublayers);
  const layers = layersres.data?.result?.rows || [];
  layers.map((item) => {
    if (
      item.geometrytype === 'esriGeometryPolyline' ||
      item.layername.indexOf('立管') > -1
    ) {
      pipeLayerOption?.push({
        label: item.layername,
        value: item.layername,
        id: item.layerid,
        data: item
      });
    }
  });
  return pipeLayerOption;
};
/**
 * 获取管网图层下拉框选项
 * @param view
 * @returns
 */
export const getPipeOptions = async (view?: __esri.MapView) => {
  if (!view) return [];
  const sublayers = getSubLayerIds(view);
  const layersres = await queryLayerClassName(sublayers);
  const layers = layersres.data?.result?.rows || [];
  return layers.map((item) => {
    return {
      label: item.layername,
      value: item.layerid,
      id: item.layerid,
      data: item
    };
  });
};

export const refreshPipeLayer = (view?: __esri.MapView, layerId?: string) => {
  const pipelayer = view?.map.findLayerById(
    layerId || 'pipelayer'
  ) as __esri.MapImageLayer;
  pipelayer && pipelayer.refresh();
};

/**
 * 往地图添加管网图层
 * @param view
 */
export const getPipeLayer = async (view: __esri.MapView) => {
  const pipeLayer = new MapImageLayer({
    id: 'pipelayer',
    title: '管网',
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService
  });
  view?.map.add(pipeLayer);
  await pipeLayer.when();
  view?.goTo(pipeLayer.fullExtent);
  return pipeLayer;
};

export const applyEdits = async (
  layerid: number,
  params: __esri.FeatureLayerBaseApplyEditsEdits,
  options?: __esri.FeatureLayerBaseApplyEditsOptions
) => {
  const editFeatureLayer = createFeatureLayer({
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer +
      '/' +
      layerid
  });
  try {
    const res = await editFeatureLayer.applyEdits(params, options);

    editFeatureLayer.destroy();
    return res;
  } catch (error: any) {
    editFeatureLayer.destroy();
    throw new Error(error);
  }
};
