import{_ as G}from"./index-qoWsDjz-.js";import{c as z,d as J,ak as R,j as H,u as K,r as W,a8 as X,o as Y,bB as Q,bu as Z,bx as M,bv as x,b as h,m as ee,C as ae,ay as le,g as o,h as n,F as i,q as U,n as p,aJ as N,aB as C,bh as v,an as d,G as I,p as oe,av as se,H as ne,e4 as de,aK as te,aL as re,dz as ue,dA as ie,E as ce,c2 as pe,f3 as fe,eL as ye,cE as me,bK as be,J as ge,e_ as he,dM as ke,I as Fe,K as _e}from"./index-r0dFAfgr.js";import{_ as ve}from"./index-B69llYYW.js";import{D as $}from"./DateFormatter-Bm9a68Ax.js";const Ue=(a,y)=>{const k=z({visible:!1,title:"",scrollbar:!0,confirm:{text:"确 定",show:!!a,handler:a},cancel:{text:"关 闭",handler:async()=>{k.value.visible=!1,y&&await y()}}});return{slDialogConfig:k}},Ve=J({name:"AddUpdateDialog",components:{Plus:R},props:{config:{type:Object,default:()=>{}}},emits:["refreshData","close"],setup(a,y){const k=H(),T=K(),{slDialogConfig:g}=Ue(async()=>await V(),async()=>await a.config.close());g.value.title=a.config.title||"",g.value.width=a.config.width||"30%",g.value.contentHeight=a.config.height||"600px",g.value.contentMaxHeight=a.config.maxHeight||"600px",g.value.visible=!0;const f=W({imgActionUrl:"",fileActionUrl:"",uploadFileName:"",headers:{},dataForm:{id:null}}),m=z(),w=X(()=>{const s={};return(a.config.columns||[]).forEach(c=>{s[c.key]=c.rules}),s}),V=()=>{var s;(s=m.value)==null||s.validate(async c=>{if(!c)return!1;let u="新增成功",b=a.config.addUrl;if(f.dataForm.id){if(!a.config.submit&&!a.config.editUrl){console.log("没有配置请求路径或方法"),a.config.close();return}u="修改成功",b=a.config.editUrl}else if(!a.config.submit&&!a.config.addUrl){a.config.close();return}const F={};let r=M({...f.dataForm});for(const t of a.config.columns){if(t.type==="date"){let _="";switch(t.dateType){case"date":_="Y-M-D";break;default:_=void 0;break}r[t.key]instanceof Array?r[t.key]=r[t.key].map(P=>$(P,_)):r[t.key]=$(r[t.key],_)}t.aInfo&&(F[t.key]=r[t.key],delete r[t.key])}if(r={...r,...a.config.externalParams||{}},Object.values(F).length>0&&(r.additionalInfo=JSON.stringify(F)),a.config.setSubmitParams&&(r=a.config.setSubmitParams(r)),a.config.submit)(await a.config.submit(r)).status===200?(h.success(u),y.emit("refreshData"),a.config.close()):h.error("请求失败");else try{(await ee({url:b,method:"post",data:r})).status===200?(h.success(u),y.emit("refreshData"),a.config.close()):h.error("请求失败")}catch(t){h.error(t.message||t.data.message)}})},E=(s,c,u)=>{console.log(s,"handleUploadSuccess"),f.dataForm[u]=s},B=s=>{const c=s.type==="image/jpeg"||s.type==="image/png",u=s.size/1024/1024<2;return c||h.error("上传图片只能是 JPG/PNG 格式!"),u||h.error("上传图片大小不能超过 2MB!"),c&&u},A=s=>(f.uploadFileName=s.name,!0);Y(async()=>{Q(()=>{var s;(s=m.value)==null||s.clearValidate()})});const D=(s,c)=>{var u;f.dataForm[c]=s.map(b=>b.id).join(","),(u=m.value)==null||u.validate(async b=>{if(!b)return!1})};Z(()=>{f.imgActionUrl=k.actionUrl+"file/api/upload/image",f.fileActionUrl=k.actionUrl+"file/api/upload/file",f.headers["X-Authorization"]="Bearer "+T.token,S()});const S=()=>{a.config.defaultValue&&(f.dataForm=M(a.config.defaultValue)),a.config.open&&a.config.open()};return{...x(f),rules:w,submit:V,handleUploadSuccess:E,beforeAvatarUpload:B,beforeFileUpload:A,checkUsers:D,ruleForm:m,slDialogConfig:g}}}),Ce={key:0,class:"inputUnit",style:{"margin-right":"10px"}},we={key:0,class:"inputUnit",style:{"margin-right":"10px"}},Ee={key:0,class:"inputUnit",style:{"margin-right":"10px"}},Be=["src"],Ae={key:12,class:"fileUpload"},De={key:0,class:"fileBox"},Se=["onClick"],Pe={key:15,class:"amap-wrapper"};function Le(a,y,k,T,g,f){const m=ne,w=de,V=te,E=re,B=ue,A=ie,D=ce,S=pe,s=fe,c=ye,u=le("Plus"),b=me,F=be,r=ge,t=he,_=ke,P=ve,O=Fe,j=_e,q=G;return a.slDialogConfig.visible?(o(),n(q,{key:0,config:a.slDialogConfig,onClose:y[0]||(y[0]=e=>a.$emit("close"))},{default:i(()=>[U(j,{ref:"ruleForm",rules:a.rules,"label-width":a.config.labelWidth||"120px","label-position":"top",model:a.dataForm,class:"dialogform addOrUpdateDialog"},{default:i(()=>[(o(!0),p(C,null,N(a.config.columns,e=>(o(),p(C,null,[e.type!=="none"?(o(),n(O,{key:e.key,label:e.label,prop:e.key},{default:i(()=>[e.type==="input"?(o(),n(m,{key:0,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,placeholder:"请输入"+e.label},{suffix:i(()=>[e.unit?(o(),p("i",Ce,v(e.unit),1)):d("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])):d("",!0),e.type==="password"?(o(),n(m,{key:1,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,type:"password",disabled:e.disabled,placeholder:"请输入"+e.label},null,8,["modelValue","onUpdate:modelValue","disabled","placeholder"])):d("",!0),e.type==="input-number"?(o(),n(w,{key:2,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,placeholder:"请输入"},{suffix:i(()=>[e.unit?(o(),p("i",we,v(e.unit),1)):d("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])):e.type==="number"?(o(),n(m,{key:3,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,placeholder:e.label,onkeyup:"value=value.replace(/[^\\d]/g,'')",style:{width:"100%"}},{suffix:i(()=>[e.unit?(o(),p("i",Ee,v(e.unit),1)):d("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])):e.type==="textarea"?(o(),n(m,{key:4,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,type:"textarea",resize:"none",rows:e.rows,placeholder:"请输入"+e.label},null,8,["modelValue","onUpdate:modelValue","disabled","rows","placeholder"])):e.type==="select"?(o(),n(E,{key:5,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,multiple:e.multiple,"multiple-limit":e.multipleLimit,"default-first-option":e.defaultFirst,"collapse-tags":"","allow-create":e.allowCreate,placeholder:"请选择"+e.label,style:{width:"100%"},filterable:e.search,onChange:e.handleChange},{default:i(()=>[(o(!0),p(C,null,N(e.options,l=>(o(),n(V,{key:l.value,value:l.value,label:l.label},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","multiple","multiple-limit","default-first-option","allow-create","placeholder","filterable","onChange"])):d("",!0),e.type==="radio"?(o(),n(A,{key:6,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,onChange:e.handleChange},{default:i(()=>[(o(!0),p(C,null,N(e.options,l=>(o(),n(B,{key:l.value,style:{"margin-bottom":"0"},label:l.value},{default:i(()=>[I(v(l.label),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])):d("",!0),e.type==="switch"?(o(),n(D,{key:7,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,"inline-prompt":"",disabled:e.disabled,"active-color":e.activeColor||"#1DCF8E","inactive-color":e.inActiveColor||"#3A3E56","active-text":e.activeText||"是","inactive-text":e.inActiveText||"否","active-value":e.activeValue||!0,"inactive-value":e.inActiveValue||!1},null,8,["modelValue","onUpdate:modelValue","disabled","active-color","inactive-color","active-text","inactive-text","active-value","inactive-value"])):d("",!0),e.type==="date"?(o(),n(S,{key:8,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,"disabled-date":e.disabledDate,type:e.dateType,class:"date-picker-input","range-separator":"至","start-placeholder":"开始日期",placeholder:"请选择","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","disabled","disabled-date","type"])):e.type==="time"?(o(),n(s,{key:9,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,"is-range":e.range,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:e.format,"value-format":e.valFormat,placeholder:"请选择",style:{width:"100%"},onChange:e.handleChange},null,8,["modelValue","onUpdate:modelValue","disabled","is-range","format","value-format","onChange"])):d("",!0),e.type==="cascader"?(o(),n(c,{key:10,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,options:e.cascaderOptions,clearable:"","collapse-tags":"",filterable:"",style:{width:"100%"},props:e.props,onChange:e.handleChange},null,8,["modelValue","onUpdate:modelValue","disabled","options","props","onChange"])):d("",!0),e.type==="image"?(o(),n(F,{key:11,class:"avatar-uploader",disabled:e.disabled,action:e.imgActionUrl||a.imgActionUrl,headers:e.headers||a.headers,"show-file-list":!1,"on-success":(l,L)=>a.handleUploadSuccess(l,L,e.key),"before-upload":a.beforeAvatarUpload},{default:i(()=>[a.dataForm[e.key]?(o(),p("img",{key:0,src:a.dataForm[e.key],class:"avatar"},null,8,Be)):(o(),n(b,{key:1,class:"el-icon-plus avatar-uploader-icon"},{default:i(()=>[U(u)]),_:1}))]),_:2},1032,["disabled","action","headers","on-success","before-upload"])):d("",!0),e.type==="file"?(o(),p("div",Ae,[U(F,{class:"upload-demo",disabled:e.disabled,action:e.fileActionUrl||a.fileActionUrl,headers:e.headers||a.headers,"show-file-list":!1,"on-success":(l,L)=>a.handleUploadSuccess(l,L,e.key),"before-upload":a.beforeFileUpload},{default:i(()=>[U(r,{size:"small",type:"primary"},{default:i(()=>y[1]||(y[1]=[I(" 点击上传 ")])),_:1})]),_:2},1032,["disabled","action","headers","on-success","before-upload"]),a.dataForm[e.key]?(o(),p("div",De,[I(v(a.uploadFileName)+" ",1),oe("span",{onClick:l=>a.dataForm[e.key]=""},"×",8,Se)])):d("",!0)])):d("",!0),e.type==="image_sl"?(o(),n(t,{key:13,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,limit:e.limit,disabled:e.disabled,url:e.url||a.fileActionUrl,multiple:e.multiple},null,8,["modelValue","onUpdate:modelValue","limit","disabled","url","multiple"])):d("",!0),e.type==="userByRole"?(o(),n(_,{key:14,width:"100%",height:"48px",onCheckUsers:l=>a.checkUsers(l,e.key)},null,8,["onCheckUsers"])):d("",!0),e.type==="location"?(o(),p("div",Pe,[U(P,{modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,"init-center-mark":!0,required:e.required,"result-type":e.returnType},null,8,["modelValue","onUpdate:modelValue","disabled","required","result-type"])])):d("",!0),e.message?(o(),p("p",{key:16,style:se(e.messageStyle)},v(e.message),5)):d("",!0)]),_:2},1032,["label","prop"])):d("",!0)],64))),256))]),_:1},8,["rules","label-width","model"])]),_:1},8,["config"])):d("",!0)}const $e=ae(Ve,[["render",Le],["__scopeId","data-v-a74a7195"]]);export{$e as _};
