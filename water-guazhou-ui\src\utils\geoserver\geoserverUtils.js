import esriRequest from '@arcgis/core/request.js';
import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
import Point from '@arcgis/core/geometry/Point';
import Polyline from '@arcgis/core/geometry/Polyline';
import Polygon from '@arcgis/core/geometry/Polygon';
import { request } from '@/plugins/axios/geoserver';
import SpatialReference from '@arcgis/core/geometry/SpatialReference'

/**
 * 使用GeoServer执行点击查询
 * @param {Object} view ArcGIS MapView对象
 * @param {String} wmsUrl GeoServer WMS服务URL
 * @param {String} layers 图层名称，多个图层用逗号分隔
 * @param {Object} screenPoint 屏幕点击位置
 * @returns {Promise} 查询结果
 */
export const excuteIdentifyByGeoserver = async (view, wmsUrl, layers, event) => {
  // 构造 GetFeatureInfo 请求 URL
  const bbox = view.extent.toJSON(); // 当前地图范围
  const width = view.width; // 地图宽度
  const height = view.height; // 地图高度

  // 获取屏幕坐标
  let x, y;

  // 处理不同类型的事件对象
  if (event.x !== undefined && event.y !== undefined) {
    // 如果直接提供了屏幕坐标
    x = Math.floor(event.x);
    y = Math.floor(event.y);
  } else if (event.screenPoint) {
    // 如果是ArcGIS点击事件
    x = Math.floor(event.screenPoint.x);
    y = Math.floor(event.screenPoint.y);
  } else {
    // 尝试从视图获取点击位置
    const screenPoint = view.toScreen(event.mapPoint);
    x = Math.floor(screenPoint.x);
    y = Math.floor(screenPoint.y);
  }

  console.log('GeoServer查询屏幕坐标:', x, y);

  // 将Web Mercator坐标转换为地理坐标（WGS84）
  const bbox4326 = {
    xmin: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmin, y: bbox.ymin }).x,
    ymin: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmin, y: bbox.ymin }).y,
    xmax: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmax, y: bbox.ymax }).x,
    ymax: webMercatorUtils.webMercatorToGeographic({ x: bbox.xmax, y: bbox.ymax }).y
  };

  // 构建GetFeatureInfo请求URL
  const getFeatureInfoUrl = `${wmsUrl}?` +
    `service=WMS&` +
    `version=1.1.1&` +
    `request=GetFeatureInfo&` +
    `layers=${layers}&` +
    `bbox=${bbox.xmin},${bbox.ymin},${bbox.xmax},${bbox.ymax}&` +
    `width=${width}&` +
    `height=${height}&` +
    `query_layers=${layers}&` +
    `info_format=application/json&` + // 返回格式（支持 text/xml 或 application/json）
    `x=${x}&` +
    `y=${y}&` +
    `feature_count=10&` + // 返回的特征数量
    `srs=EPSG:3857`; // 坐标系

  // 发送 GetFeatureInfo 请求
  try {
    const response = await esriRequest(getFeatureInfoUrl, {
      responseType: "json" // 返回格式为JSON
    });

    return response;
  } catch (error) {
    console.error("GeoServer查询失败：", error);
    return null;
  }
};

/**
 * 将GeoJSON格式的几何对象转换为ArcGIS几何类实例
 * @param {Object} geoJson GeoJSON格式的几何对象
 * @returns {Object} ArcGIS几何类实例
 */
export const convertGeoJSONToArcGIS = (geoJson) => {
  if (!geoJson) return null;

  const spatialReference = new SpatialReference({ wkid: 3857 });

  switch (geoJson.type) {
    case 'Point':
      return new Point({
        x: geoJson.coordinates[0],
        y: geoJson.coordinates[1],
        spatialReference
      });

    case 'MultiPoint':
      return new Point({
        points: geoJson.coordinates,
        spatialReference
      });

    case 'LineString':
      return new Polyline({
        paths: [geoJson.coordinates],
        spatialReference
      });

    case 'MultiLineString':
      return new Polyline({
        paths: geoJson.coordinates,
        spatialReference
      });

    case 'Polygon':
      return new Polygon({
        rings: geoJson.coordinates,
        spatialReference
      });

    case 'MultiPolygon':
      // 多边形需要特殊处理，将多个多边形合并为一个
      const rings = [];
      geoJson.coordinates.forEach(polygonCoords => {
        polygonCoords.forEach(ring => {
          rings.push(ring);
        });
      });

      return new Polygon({
        rings,
        spatialReference
      });

    default:
      console.error('不支持的几何类型:', geoJson.type);
      return null;
  }
};

/**
 * 使用GeoServer进行沿线查询
 * @param {Object} params 查询参数
 * @param {Array} [params.nodeGeometries] 节点几何信息数组
 * @param {String} [params.flagOIDs] 节点ID，逗号分隔（如果没有提供nodeGeometries，将使用此参数查询节点几何信息）
 * @param {String} [params.nodeLayerName] 节点图层名称（如果没有提供nodeGeometries，将使用此参数查询节点几何信息）
 * @param {String} params.outputLayers 输出图层名称，逗号分隔
 * @returns {Promise} 查询结果
 */
/**
 * 使用featureId查询GeoServer要素
 * @param {Object} params 查询参数
 * @param {String} params.typeName 图层名称
 * @param {String|Number} params.id 要素ID
 * @param {String} [params.workspace='anqing'] 工作空间名称
 * @param {String} [params.outputSRS='EPSG:4326'] 输出坐标系，例如'EPSG:4326'或'EPSG:3857'
 * @returns {Promise<Object>} 查询结果，包含要素数据
 */
export const queryFeatureByIdGeoserver = async (params) => {
  const {
    typeName,
    id,
    workspace = 'guazhou',
    outputSRS = 'EPSG:3857' // 默认使用WGS84坐标系
  } = params;

  try {
    // 构建完整的featureId
    const featureId = `${typeName}.${id}`;

    // 直接使用featureId查询，并指定输出坐标系
    const response = await request({
      url: `/geoserver/${workspace}/wfs?service=WFS&version=1.0.0&request=GetFeature&featureId=${featureId}&outputFormat=application/json&srsName=${outputSRS}`,
      method: 'get'
    });

    // 如果featureId查询成功，直接返回结果
    if (response.data?.features?.length) {
      return {
        success: true,
        data: response.data,
        feature: response.data.features[0],
        coordinateSystem: outputSRS // 返回使用的坐标系信息
      };
    }

    // 如果查询失败，返回失败结果
    return {
      success: false,
      message: '未找到匹配的要素',
      data: null,
      feature: null,
      coordinateSystem: outputSRS
    };
  } catch (error) {
    console.error('查询要素失败:', error);
    return {
      success: false,
      message: error.message || '查询要素失败',
      error,
      data: null,
      feature: null,
      coordinateSystem: outputSRS
    };
  }
};

export const getPipeAttachmentByGeoserver = async (params) => {
  // 解析节点几何信息和图层名称
  const nodeGeometries = params.nodeGeometries || []; // 节点的几何信息
  const layerNames = params.outputLayers.split(','); // 确保是数组

  // 如果没有提供节点几何信息，尝试从flagOIDs中获取
  if (nodeGeometries.length === 0 && params.flagOIDs) {
    console.log('没有提供节点几何信息，尝试从flagOIDs中获取');
    // 这种情况下，我们需要先查询节点的几何信息
    // 但这需要知道节点图层的名称，这里我们假设它是params.nodeLayerName
    // 如果没有提供，我们无法继续
    if (!params.nodeLayerName) {
      console.error('没有提供节点图层名称，无法查询节点几何信息');
      return {
        data: {
          code: 10000,
          message: '没有提供节点几何信息和节点图层名称',
          result: []
        }
      };
    }

    // 尝试查询节点的几何信息
    try {
      const nodeIds = params.flagOIDs.split(',');
      // 构建CQL过滤条件，查询节点
      const cqlFilter = `id IN (${nodeIds.join(',')}) OR OBJECTID IN (${nodeIds.join(',')})`;

      const response = await request({
        url: `/geoserver/guazhou/wfs?service=WFS&version=1.0.0&request=GetFeature&typeName=${params.nodeLayerName}&CQL_FILTER=${encodeURIComponent(cqlFilter)}&outputFormat=application/json`,
        method: 'get'
      });

      if (response.data && response.data.features && response.data.features.length > 0) {
        // 提取节点的几何信息
        for (const feature of response.data.features) {
          nodeGeometries.push(feature.geometry);
        }
      } else {
        console.error('未找到节点的几何信息');
        return {
          data: {
            code: 10000,
            message: '未找到节点的几何信息',
            result: []
          }
        };
      }
    } catch (error) {
      console.error('查询节点几何信息失败:', error);
      return {
        data: {
          code: 10000,
          message: '查询节点几何信息失败: ' + error.message,
          result: []
        }
      };
    }
  }

  // 确保我们有两个节点的几何信息
  if (nodeGeometries.length < 2) {
    console.error('节点几何信息不足，需要两个节点');
    return {
      data: {
        code: 10000,
        message: '节点几何信息不足，需要两个节点',
        result: []
      }
    };
  }

  // 提取节点坐标
  const node1Coords = nodeGeometries[0].coordinates;
  const node2Coords = nodeGeometries[1].coordinates;

  // 对每个图层进行查询
  const layerResults = await Promise.all(layerNames.map(async (layerName) => {
    try {
      // 使用空间查询 - 查询与这两个节点相交或接触的管线
      // 我们使用INTERSECTS或TOUCHES空间操作符
      // 构建CQL过滤条件
      const cqlFilter = `INTERSECTS(the_geom, POINT(${node1Coords[0]} ${node1Coords[1]})) OR INTERSECTS(the_geom, POINT(${node2Coords[0]} ${node2Coords[1]}))`;

      console.log(`使用空间查询图层 ${layerName}`);

      // 发送查询请求
      const response = await request({
        url: `/geoserver/guazhou/wfs?service=WFS&version=1.0.0&request=GetFeature&typeName=${layerName}&CQL_FILTER=${encodeURIComponent(cqlFilter)}&outputFormat=application/json`,
        method: 'get'
      });

      // 如果请求成功并且返回了要素
      if (response.data && response.data.features && response.data.features.length > 0) {
        console.log(`成功查询到图层 ${layerName} 的数据，共 ${response.data.features.length} 条`);

        // 处理响应，将GeoServer的响应格式转换为与ArcGIS服务相同的格式
        return {
          layeralias: layerName,
          queryresult: response.data.features.map(feature => {
            // 将GeoJSON格式的要素转换为与ArcGIS服务相同的格式
            return {
              ...feature.properties,
              geometry: feature.geometry,
              id: feature.id
            };
          })
        };
      } else {
        console.log(`图层 ${layerName} 没有与节点相交的要素`);
        return {
          layeralias: layerName,
          queryresult: []
        };
      }
    } catch (error) {
      console.error(`查询图层 ${layerName} 失败:`, error);
      return {
        layeralias: layerName,
        queryresult: []
      };
    }
  }));

  // 返回查询结果
  return {
    data: {
      code: 0,
      message: '查询成功',
      result: layerResults
    }
  };
};