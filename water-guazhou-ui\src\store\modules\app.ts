import Cookies from 'js-cookie';
import { getAppVersion } from '@/api/application';

export const useAppStore = defineStore('app', () => {
  const initialState: Store_App_State = {
    horizontalMenu: localStorage.getItem('horizontalMenu') !== 'false',
    menuType: localStorage.getItem('menuType') || 'top',
    menuShow: false,
    subMenuShow: false,
    subMenuParentRoute: {},
    isDark: false,
    // 消息通知
    MessageNotification: false,
    // 语音播报
    VoiceBroadcast: false,
    // 菜单收起、展开
    sidebar: {
      opened: ~~(Cookies.get('sidebarStatus') as any),
      withoutAnimation: false
    },
    appUrl: '',
    device: 'desktop',
    // 图片上传到系统的链接，固定写死的
    // 例如 'http://api.istarscloud.com:8081/api/device/image/'
    actionUrl: window.SITE_CONFIG.imgUrl, // http://39.104.109.201:8081/file/api/upload/image
    // 组态前端url  显示接'viewer/'  编辑'app/editor/'  那边改了 这边要随时变
    scadaUrl: window.SITE_CONFIG.scadaURL, // 0.138:4200     http://fes.natsuki.cc:8028/  8022
    // theme: Cookies.get('themeColor') ? Cookies.get('themeColor') : '409EFF',
    theme: '252C47',
    // sys跳转标识
    sysSign: sessionStorage.getItem('sysSign')
      ? JSON.parse(sessionStorage.getItem('sysSign') || '')
      : '',
    // 用户可访问项目ID数组
    userProject: Cookies.get('userProject') ? Cookies.get('userProject') : null,
    logo: sessionStorage.getItem('logo') || '',
    appname: sessionStorage.getItem('appname') || '',
    appid: sessionStorage.getItem('appid') || '',
    isFrameApp: JSON.parse(sessionStorage.getItem('isFrameApp') || 'false'),
    frameAppUrl: sessionStorage.getItem('frameAppUrl') || '',
    keepAliveMax: 10
  };
  const state = reactive<Store_App_State>(initialState);
  const actions = {
    REMOVE_appInfos: () => {
      state.appid = '';
      state.appname = '';
      sessionStorage.removeItem('appid');
      sessionStorage.removeItem('appname');
    },
    TOGGLE_menuType: (payload: 'top' | 'left') => {
      state.menuType = payload;
      localStorage.setItem('menuType', payload);
    },
    TOGGLE_menuShow: (payload?: boolean) => {
      if (payload === undefined) state.menuShow = !state.menuShow;
      else state.menuShow = payload;
    },
    SET_subMenuParentRoute: (payload: string) => {
      state.subMenuParentRoute = payload;
    },
    TOGGLE_submenuShow: (payload?: boolean) => {
      if (payload === undefined) state.subMenuShow = !state.subMenuShow;
      else state.subMenuShow = payload;
      if (state.subMenuShow === false) state.subMenuParentRoute = {};
    },
    SET_isDark: (payload: boolean) => {
      state.isDark = payload;
    },
    SET_MessageNotification: (payload: boolean) => {
      state.MessageNotification = payload;
    },
    SET_VoiceBroadcast: (payload: boolean) => {
      state.VoiceBroadcast = payload;
    },
    /** 设置当前应用是否是嵌套在iframe中的 */
    SET_isFrameApp: (payload: boolean) => {
      state.isFrameApp = payload;
      sessionStorage.setItem('isFrameApp', JSON.stringify(payload));
    },
    SET_frameAppUrl: (payload: string) => {
      state.frameAppUrl = payload;
      sessionStorage.setItem('frameAppUrl', payload);
    },
    SET_appname: (payload: string) => {
      state.appname = payload;
      sessionStorage.setItem('appname', payload);
    },
    SET_appid: (payload: string) => {
      state.appid = payload;
      sessionStorage.setItem('appid', payload);
    },
    TOGGLE_SIDEBAR: () => {
      state.sidebar.opened = !state.sidebar.opened;
      state.sidebar.withoutAnimation = false;
      state.sidebar = { ...state.sidebar };
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', '1');
      } else {
        Cookies.set('sidebarStatus', '0');
      }
    },
    TOGGLE_SYS_SIGN: (sysSign) => {
      if (sysSign.returnSys) {
        sessionStorage.removeItem('sysSign');
        state.sysSign = undefined;
      } else {
        sessionStorage.setItem('sysSign', JSON.stringify(sysSign));
        state.sysSign = sysSign;
      }
    },
    TOGGLE_LOGO: (logo) => {
      state.logo = logo;
      sessionStorage.setItem('logo', logo);
    },
    SET_USER_PROJECT: (userProject) => {
      if (userProject.isSet) {
        Cookies.set('userProject', userProject.pData);
        state.userProject = userProject.pData;
      } else {
        Cookies.remove('userProject');
        state.userProject = null;
      }
      console.log(userProject, 'userProject');
    },
    REAL_TIME_MONITOR: (rtmQueryList) => {
      sessionStorage.setItem('rtmQueryList', JSON.stringify(rtmQueryList));
      state.rtmQueryList = rtmQueryList;
    },
    HISTORY_TIME_MONITOR: (htmQueryList) => {
      sessionStorage.setItem('htmQueryList', JSON.stringify(htmQueryList));
      console.log('--------------set-----------', htmQueryList);
      state.htmQueryList = htmQueryList;
    },
    CLOSE_SIDEBAR: (withoutAnimation) => {
      Cookies.set('sidebarStatus', '1');
      state.sidebar.opened = false;
      state.sidebar.withoutAnimation = withoutAnimation;
    },
    TOGGLE_DEVICE: (device) => {
      state.device = device;
    },
    SET_APP_VERSION: (url) => {
      state.appUrl = url;
    },
    ToggleSysSign: (sysSign) => {
      actions.TOGGLE_SYS_SIGN(sysSign);
    },
    ToggleLogo: (logo) => {
      actions.TOGGLE_LOGO(logo);
    },
    SetUserProject: (userProject) => {
      actions.SET_USER_PROJECT(userProject);
    },
    RealTimeMonitor: (rtmQueryList) => {
      actions.REAL_TIME_MONITOR(rtmQueryList);
    },
    HistoryTimeMonitor: (htmQueryList) => {
      actions.HISTORY_TIME_MONITOR(htmQueryList);
    },
    CloseSideBar: ({ withoutAnimation }) => {
      actions.CLOSE_SIDEBAR(withoutAnimation);
    },
    ToggleDevice: (device) => {
      actions.TOGGLE_DEVICE(device);
    },
    SetAppVersion: async () => {
      try {
        const appRes = await getAppVersion();
        if (appRes.status === 200) {
          actions.SET_APP_VERSION(appRes.data.url);
        }
      } catch (error: any) {
        console.log(error.message);
      }
    }
  };
  return { ...toRefs(state), ...actions };
});
