package org.thingsboard.server.dao.fault;

import org.thingsboard.server.dao.model.sql.fault.FaultInfo;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface FaultInfoService {

    List<FaultInfo> getListByMainId(String mainId, String name, String tenantId);

    FaultInfo save(FaultInfo faultInfo);

    IstarResponse delete(List<String> ids);
}
