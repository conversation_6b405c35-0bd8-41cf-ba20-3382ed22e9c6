package org.thingsboard.server.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsDepreciationEntity;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsAccountRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsDepreciationRepository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 折旧信息计算
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-29
 */
@Component
@Slf4j
public class DepreciationUtil {
    @Autowired
    private AssetsAccountRepository assetsAccountRepository;
    @Autowired
    private AssetsDepreciationRepository assetsDepreciationRepository;
    public void generateDepreciation(AssetsAccountEntity a) {
        try {
            if (StringUtils.isNotBlank(a.getId())) {
                assetsDepreciationRepository.deleteByPid(a.getId());
                // 重新设置折旧信息
                a.setDepreciationAmountMonth(BigDecimal.valueOf(0));
                a.setNetValue(a.getOriginValue());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            // 折旧方法 0无 1年限平均法 2年数总和法  3双倍余额递减法
            String depreciationMethod = a.getDepreciationMethod();

            Double depreciationRateYear;
            Double depreciationRateMonth;
            BigDecimal depreciationValueMonth = null;
            if ("0".equals(depreciationMethod)) {
                return;
            }
            // 采购月距今时间
            if (a.getPurchaseTime() == null) return;
            LocalDate purchaseTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(a.getPurchaseTime()), ZoneId.systemDefault()).toLocalDate();
            LocalDate nowLocalDate = LocalDate.now(ZoneId.systemDefault());
            long addMonth = 1l;
            while (purchaseTime.plusMonths(addMonth).isBefore(nowLocalDate)) {
                switch (depreciationMethod) {
                    case "1":
                        depreciationRateYear = (1 - a.getNetResidualRate() / 100) / (a.getUseLife() / 12.0);
                        depreciationRateMonth = depreciationRateYear / 12.0;
                        depreciationValueMonth = a.getOriginValue().multiply(BigDecimal.valueOf(depreciationRateMonth));
                        break;
                    case "2":
                        // 剩余年限 预计使用寿命（月）- （当前日期 - 启用日期（月））
                        Date localDateNow = new Date();
                        Date enableDateMonth = new Date(a.getPurchaseTime().longValue());

                        Calendar start = Calendar.getInstance();
                        Calendar end = Calendar.getInstance();

                        start.setTime(enableDateMonth);
                        end.setTime(localDateNow);

                        int result = end.get(Calendar.MONTH) - start.get(Calendar.MONTH);
                        int month = (end.get(Calendar.YEAR) - start.get(Calendar.YEAR)) * 12;

                        int diffMonth = Math.abs(result + month);

                        // 剩余年限
                        int surplusMoth = a.getUseLife() - diffMonth;
                        int surplusYear = Long.valueOf(Math.round(surplusMoth / 12.0)).intValue();
                        int surplusYearTemp = surplusYear;

                        int surplusYearTotal = 0;
                        // 预计使用年限的年数总和
                        while (surplusYear > 0) {
                            surplusYearTotal += surplusYear;
                            surplusYear--;
                        }

                        depreciationRateYear = surplusYearTemp / Double.valueOf(surplusYearTotal);
                        depreciationRateMonth = depreciationRateYear / 12;

                        // 月折旧额 =（固定资产原价-预计净残值）* 月折旧率
                        depreciationValueMonth = a.getOriginValue().subtract(a.getOriginValue().multiply(BigDecimal.valueOf(a.getNetResidualRate() / 100))).multiply(BigDecimal.valueOf(depreciationRateMonth));
                        break;
                    case "3":
                        depreciationRateYear = 2 / (a.getUseLife() / 12.0);
                        depreciationRateMonth = depreciationRateYear / 12;
                        depreciationValueMonth = a.getNetValue().multiply(BigDecimal.valueOf(depreciationRateMonth));

                }
                if (depreciationValueMonth != null) {
                    String nowMonth = purchaseTime.plusMonths(addMonth).format(DateTimeFormatter.ofPattern("yyyy-MM"));

                    AssetsDepreciationEntity assetsDepreciationEntity = new AssetsDepreciationEntity();
                    assetsDepreciationEntity.setPid(a.getId());
                    assetsDepreciationEntity.setCreateTime(System.currentTimeMillis());
                    assetsDepreciationEntity.setDepreciationMonth(nowMonth);
                    assetsDepreciationEntity.setDepreciationCurrent(depreciationValueMonth.setScale(2, RoundingMode.HALF_UP));
                    assetsDepreciationEntity.setDepreciationTotal(depreciationValueMonth.setScale(2, RoundingMode.HALF_UP));
                    assetsDepreciationEntity.setCountMonth(1);
                    // 获取上个月折旧信息
                    AssetsDepreciationEntity lastAssetsDepreciationEntity = assetsDepreciationRepository.findTopByPidOrderByCreateTimeDesc(a.getId());
                    if (lastAssetsDepreciationEntity != null) {
                        assetsDepreciationEntity.setDepreciationTotal(lastAssetsDepreciationEntity.getDepreciationTotal().add(depreciationValueMonth).setScale(2, RoundingMode.HALF_UP));
                        assetsDepreciationEntity.setCountMonth(lastAssetsDepreciationEntity.getCountMonth() + 1);
                    }
                    assetsDepreciationEntity.setProjectId(a.getProjectId());
                    assetsDepreciationEntity.setTenantId(a.getTenantId());
                    assetsDepreciationRepository.save(assetsDepreciationEntity);

                    // 更改主表折旧信息
                    a.setNetValue(a.getOriginValue().subtract(assetsDepreciationEntity.getDepreciationTotal()).setScale(2, RoundingMode.HALF_UP));
                    a.setDepreciationAmountMonth(assetsDepreciationEntity.getDepreciationCurrent().setScale(2, RoundingMode.HALF_UP));
                    a.setUpdateTime(System.currentTimeMillis());

                    assetsAccountRepository.save(a);

                    addMonth++;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成折旧信息失败：" + e.getMessage());
        }
    }
}
