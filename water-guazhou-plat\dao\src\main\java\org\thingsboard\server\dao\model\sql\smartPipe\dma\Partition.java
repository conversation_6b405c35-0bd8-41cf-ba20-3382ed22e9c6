package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DMA分区设置
 */
@Data
@TableName("tb_pipe_partition")
public class Partition {
    private String id;

    private String pid;

    private String name;

    private String type;

    private Integer orderNum;

    private String copyMeterType;

    private Boolean isMachineMeter;

    private Integer collectRate;

    private String geom;

    private String range;

    private String borderColor;

    private String rangeColor;

    private String userType;

    private String copyMeterUser;

    private String director;

    @TableField(exist = false)
    private Integer bigUserNum;

    @TableField(exist = false)
    private Integer level;

    @TableField(exist = false)
    private Integer revenueUserNum;

    private BigDecimal supplyWaterArea;

    private BigDecimal mainLineLength;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private String status;

    private String villageType;

    private Integer copyMeterCycle;

    private Integer inletNum;

    private BigDecimal inPipeCaliber;

    private String propertyName;

    private Integer minFlowStartHour;

    private Integer minFlowEndHour;

    private BigDecimal legalUseWater;

    private BigDecimal avgPressure;

    private String enableNrw;

    private BigDecimal minPassPressure;

    private BigDecimal maxPassPressure;

    private String zeroPressureTestDetail;

    private String code;

}
