package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class CircuitTemplatePageRequest extends AdvancedPageableQueryEntity<CircuitTemplate, CircuitTemplatePageRequest> {
    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 巡检模板名称
    private String name;

    // 创建人员Id
    private String creator;

    // 关键字
    private String keyword;
}
