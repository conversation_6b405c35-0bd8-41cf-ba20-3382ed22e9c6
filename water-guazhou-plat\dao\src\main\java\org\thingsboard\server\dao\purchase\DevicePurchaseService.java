package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchase;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchasePageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseSaveRequest;

public interface DevicePurchaseService {
    /**
     * 分页条件查询设备采购单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DevicePurchase> findAllConditional(DevicePurchasePageRequest request);

    /**
     * 保存设备采购单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DevicePurchase save(DevicePurchaseSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DevicePurchase entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
