<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.circuit.StationCircuitPlanMapper">

    <!--  多表基础查询  -->
    <sql id="multiple_base_select">
        SELECT a.id,
               a.name,
               a.type,
               a.days,
               a.start_time AS "startTime",
               a.end_time AS "endTime",
               a.audit_dep AS "auditDep",
               a.audit_user_id AS "auditUserId",
               a.option_dep AS "optionDep",
               a.option_user_id AS "optionUserId",
               a.station_ids AS "stationIds",
               a.remark AS "remark",
               a.station_type AS "stationType",
               a.tenant_id AS "tenantId"

    </sql>

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitPlan">
        <include refid="multiple_base_select"/>
        FROM tb_station_circuit_plan a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.optionDep != null and param.optionDep != ''">
                AND a.option_dep = #{param.optionDep}
            </if>
            <if test="param.optionUserId != null and param.optionUserId != ''">
                AND a.option_user_id = #{param.optionUserId}
            </if>
            <if test="param.auditDep != null and param.auditDep != ''">
                AND a.audit_dep = #{param.auditDep}
            </if>
            <if test="param.auditUserId != null and param.auditUserId != ''">
                AND a.audit_user_id = #{param.auditUserId}
            </if>
            <if test="param.stationType != null and param.stationType != ''">
                AND a.station_type = #{param.stationType}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND a.name LIKE '%'|| #{param.keyword}||'%'
            </if>
            <if test="param.beginStartTime != null and param.endStartTime != null">
                AND a.start_time BETWEEN #{beginStartTime} AND #{endStartTime}
            </if>
            <if test="param.beginEndTime != null and param.endEndTime != null">
                AND a.end_time BETWEEN #{beginEndTime} AND #{endEndTime}
            </if>
        </where>
        ORDER BY create_time

    </select>
</mapper>