import{m as e}from"./index-r0dFAfgr.js";function a(t){return e({url:"/api/process/type",method:"get",params:t})}function o(t){return e({url:"/api/process/type",method:"post",data:t})}function n(t){return e({url:"/api/process/type",method:"delete",data:t||[]})}function p(t){return e({url:"/api/process/step",method:"get",params:t})}function u(t){return e({url:"/api/process/step",method:"post",data:t})}function i(t){return e({url:"/api/process/step",method:"delete",data:t||[]})}function c(t){return e({url:"/api/process/step/attachment",method:"get",params:t})}function d(t){return e({url:"/api/process/step/attachment",method:"post",data:t})}function m(t){return e({url:"/api/process/step/attachment",method:"delete",data:t||[]})}function l(t){return e({url:"/api/user/getAllByPidStr",method:"get",params:t})}function h(t){return e({url:"/api/form",method:"get",params:t})}function f(t){return e({url:"/api/form",method:"delete",data:t})}function g(t){return e({url:"/api/form",method:"post",data:t})}function y(t){return e({url:`/api/form/${t}`,method:"get"})}function S(t){return e({url:`/api/install/project/c/${t}`,method:"get"})}function A(t){return e({url:`/api/process/type/${t}`,method:"get"})}function B(t,r){return e({url:`/api/install/project/attachment/${t}`,method:"get",params:{childId:r}})}export{p as a,u as b,i as c,m as d,d as e,h as f,l as g,o as h,n as i,B as j,S as k,A as l,f as m,g as n,y as o,a as p,c as s};
