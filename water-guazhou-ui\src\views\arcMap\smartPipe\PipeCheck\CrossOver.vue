<!-- 相交检查 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'相交检查'"
    :detail-max-min="true"
    :full-content="true"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <template #detail-header>
      <span>相交检查结果{{
        state.curLayerName && ' - ' + state.curLayerName
      }}</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <div class="left">
          <div class="title">
            相交列表
          </div>
          <div class="table-box">
            <FormTable :config="TableConfig"></FormTable>
          </div>
        </div>
        <div class="right">
          <div class="title">
            相交要素详情
            {{
              TableConfig.currentRow?.id
                ? ' - 序号' + TableConfig.currentRow?.id
                : ''
            }}
          </div>
          <div class="table-box">
            <DetailTable
              ref="refDetailTable"
              @row-click="handleRowClick"
              @refresh-data="refreshDetailTable"
            ></DetailTable>
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { getSubLayerIds } from '@/utils/MapHelper'
import { IFormIns } from '@/components/type'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { CheckIntersectLine } from '@/api/mapservice/pipeCheck'
import DetailTable from '../../components/common/DetailTable.vue'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
  curType: 'ellipse' | 'rectangle' | 'polygon' | ''
  curLayerName: string
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false,
  curLayerName: ''
})
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  queryParams: {
    geometry?: __esri.Geometry
    where?: string
  }
} = {
  queryParams: {
    geometry: undefined,
    where: '1=1'
  }
}
const TableConfig = reactive<ITable>({
  dataList: [],
  indexVisible: true,
  columns: [
    {
      label: '相交个数',
      prop: 'intersectCount',
      formatter: row => row.intersectlines?.length || 0
    }
  ],
  pagination: {
    hide: true
  },
  handleRowClick: row => {
    TableConfig.currentRow = row
    refreshDetailTable()
  }
})
const refreshDetailTable = async () => {
  if (!TableConfig.currentRow) return
  await refDetailTable.value?.refreshDetail(
    staticState.view,
    {
      layername: state.curLayerName,
      layerid: state.layerInfos.find(l => l.layername === state.curLayerName)
        ?.layerid,
      oids: TableConfig.currentRow.intersectlines || []
    },
    {
      page: 1
    }
  )
  refMap.value?.toggleCustomDetail(true)
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value])
            }
          }
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () => (state.loading ? '正在检查，过程稍长，请耐心等待！' : '检查'),
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    length: 1
  }
})
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group.find(item => item.id === 'layer')
    ?.fields[0] as IFormTree
  // const points = state.layerInfos
  //   .filter(item => item.geometrytype === 'esriGeometryPoint')
  //   .map(item => {
  //     return {
  //       label: item.layername,
  //       value: item.layerid,
  //       data: item
  //     }
  //   })
  const lines = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  field
    && (field.options = [
      // { label: '管点类', value: -1, disabled: true, children: points },
      { label: '管线类', value: -2, disabled: true, children: lines }
    ])
  refForm.value
    && (refForm.value.dataForm.layerid = [lines.map(item => item.value)[0]])
}
const refDetailTable = ref<InstanceType<typeof DetailTable>>()
const handleRowClick = row => {
  refDetailTable.value?.extentTo(staticState.view, row.OBJECTID)
}
const startQuery = async () => {
  state.loading = true
  SLMessage.info('正在检查，请稍候...')
  refMap.value?.toggleCustomDetail(true)
  refDetailTable.value?.clearTable()
  TableConfig.dataList = []
  TableConfig.currentRow = undefined
  try {
    state.tabs.length = 0
    const layerIds = refForm.value?.dataForm.layerid?.filter(item => item >= 0) || []
    if (!layerIds.length) {
      SLMessage.warning('请选择一个图层')
    } else {
      state.curLayerName = state.layerInfos.find(item => item.layerid === layerIds[0])
        ?.layername || ''
      const res = await CheckIntersectLine({
        layer: state.curLayerName
      })
      if (!res.data.result?.length) {
        SLMessage.success('没有相关内容')
      }
      TableConfig.dataList = res.data.result || []
      TableConfig.currentRow = TableConfig.dataList[0]
      refreshDetailTable()
    }
  } catch (error: any) {
    console.log(error)
    SLMessage.error(error.message)
  }
  state.loading = false
}
const onMapLoaded = view => {
  staticState.view = view
  getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
})
</script>
<style lang="scss" scoped>
.darkblue {
  .detail-wrapper {
    .title {
      background-color: rgba(21, 45, 68, 1);
    }
  }
}
.detail-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  .left {
    width: 300px;
  }
  .right {
    width: calc(100% - 300px);
    border-left: 1px solid var(--el-border-color);
  }
  .title {
    background-color: #ddd;
    height: 40px;
    font-size: 14px;
    display: flex;
    align-items: center;
    padding-left: 10px;
    margin: 0;
  }
  .table-box {
    padding: 8px;
    height: calc(100% - 40px);
    width: 100%;
  }
}
</style>
