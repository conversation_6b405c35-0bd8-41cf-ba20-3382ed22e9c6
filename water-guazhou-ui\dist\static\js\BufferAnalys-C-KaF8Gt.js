import{d as _,r as L,c as P,b as T,bB as x,o as D,Q,g as N,n as E,q as B,i as u,_ as M}from"./index-r0dFAfgr.js";import{a as V,m as R,n as $,o as U,c as J,s as W}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as S}from"./LayerHelper-Cn-iiqxI.js";import{q as j,c as z,e as H,i as K}from"./QueryHelper-ILO3qZqg.js";import{Q as X}from"./pipe-nogVzCHG.js";import{s as b,i as Y}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import Z from"./PipeDetail-CTBPYFJW.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";const Pr=_({__name:"BufferAnalys",props:{view:{},telport:{}},setup(F){const o=F,t=L({curOperate:"drawing",tabs:[],curType:"point"}),r={queryParams:{geometry:void 0,where:void 0}},g=P(),w=P(),k=L({gutter:12,group:[{fieldset:{desc:"分析类型"},fields:[{type:"btn-group",btns:[{styles:{width:"100%"},perm:!0,type:()=>t.curType==="point"?"primary":"default",text:"点要素",disabled:()=>t.curOperate==="analysing"||t.curOperate==="detailing",click:()=>{t.curType="point",d(),y(),f()}},{styles:{width:"100%"},perm:!0,type:()=>t.curType==="polyline"?"primary":"default",text:"线要素",disabled:()=>t.curOperate==="analysing"||t.curOperate==="detailing",click:()=>{t.curType="polyline",d(),y(),f()}},{styles:{width:"100%"},perm:!0,type:()=>t.curType==="polygon"?"primary":"default",text:"面要素",disabled:()=>t.curOperate==="analysing"||t.curOperate==="detailing",click:()=>{t.curType="polygon",d(),y(),f()}}]}]},{fieldset:{desc:"缓冲距离"},fields:[{type:"input-number",field:"distance",append:"米"},{type:"btn-group",itemContainerStyle:{marginTop:"20px",marginBottom:"5px"},btns:[{perm:!0,text:()=>t.curOperate==="analysing"?"正在分析":"开始分析",loading:()=>t.curOperate==="analysing",disabled:()=>t.curOperate!=="drawed"&&t.curOperate!=="analysed"&&t.curOperate!=="viewing",click:()=>q(),styles:{width:"100%"}}]}]}],labelPosition:"top",defaultValue:{type:"point",distance:0}}),d=()=>{var e;o.view&&(r.graphicsLayer=S(o.view,{id:"buffer-analys-mark",title:"缓冲区分析标注"}),(e=r.graphicsLayer)==null||e.removeAll(),r.geometry=void 0,r.vertices=void 0)},y=()=>{var e;o.view&&(r.bufferLayer=S(o.view,{id:"buffer-analys-result",title:"缓冲区分析结果"}),(e=r.bufferLayer)==null||e.removeAll(),r.queryParams.geometry=void 0)},f=()=>{var a,i,s,p,n;if(!o.view)return;t.curOperate="drawing",b("crosshair");const e=t.curType;(a=r.drawAction)==null||a.destroy(),(i=r.drawer)==null||i.destroy(),r.drawer=Y(o.view),r.drawAction=(s=r.drawer)==null?void 0:s.create(e),e!=="point"&&((p=r.drawAction)==null||p.on(["vertex-add","cursor-update"],O)),(n=r.drawAction)==null||n.on("draw-complete",c=>{var l;O(c),r.vertices=c.vertices,b("");const m=V(e,r.vertices,(l=o.view)==null?void 0:l.spatialReference);r.geometry=m,t.curOperate="drawed"})},O=e=>{var s,p,n,c,m,l;const a=t.curType,i=a==="polyline"?R(e.vertices,(s=o.view)==null?void 0:s.spatialReference):a==="polygon"?e.vertices.length<3?R(e.vertices,(p=o.view)==null?void 0:p.spatialReference):$(e.vertices,(n=o.view)==null?void 0:n.spatialReference):U(e.vertices,(c=o.view)==null?void 0:c.spatialReference);(m=r.graphicsLayer)==null||m.removeAll(),i&&((l=r.graphicsLayer)==null||l.add(i))},q=async()=>{var a,i;if(t.curOperate="analysing",!r.vertices){T.warning("请选绘制图形"),t.curOperate="drawing";return}y();const e=((i=(a=g.value)==null?void 0:a.dataForm)==null?void 0:i.distance)||"0";t.tabs=[];try{e==="0"?await A():await C(),x(()=>{var s;t.curOperate="detailing",(s=w.value)==null||s.openDialog()})}catch{T.error("系统错误"),t.curOperate="drawed"}},C=async()=>{var p,n,c,m,l;if(!r.geometry)return;const e=((n=(p=g.value)==null?void 0:p.dataForm)==null?void 0:n.distance)||0,a=await j(z({bufferSpatialReference:(c=o.view)==null?void 0:c.spatialReference,distances:[e],geometries:[r.geometry],outSpatialReference:(m=o.view)==null?void 0:m.spatialReference,geodesic:!0,unit:"meters",unionResults:!1}));r.queryParams.geometry=a[0];const i=J({geometry:a[0],symbol:W("polygon",{color:[0,182,153,.2],outlineColor:"#00B699",outlineWidth:1})});(l=r.bufferLayer)==null||l.add(i);const s=await h();await v(s,0)},I=async()=>{var e;o.view&&((e=w.value)==null||e.extentTo(o.view))},A=async()=>{const e=await h();r.queryParams.geometry=r.geometry,await v(e,0)},h=async()=>{var a;return(a=(await X()).data.layers)==null?void 0:a.filter(i=>!i.subLayerIds)},v=async(e,a)=>{var i,s;try{const p=(i=e[a])==null?void 0:i.id,n=(s=e[a])==null?void 0:s.name,c=window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,m=await H(`${c}/${p}`,K({orderByFields:["OBJECTID asc"],...r.queryParams||{}}));m!==null&&t.tabs.push({label:n,name:n,data:m}),a<e.length-1&&await v(e,++a)}catch{console.log("发生错误，停止递归")}},G=()=>{var e,a,i,s;b(""),(e=r.drawer)==null||e.destroy(),(a=r.drawAction)==null||a.destroy(),r.bufferLayer&&((i=o.view)==null||i.map.remove(r.bufferLayer)),r.graphicsLayer&&((s=o.view)==null||s.map.remove(r.graphicsLayer))};return D(()=>{d(),y(),f()}),Q(()=>{G()}),(e,a)=>{const i=M;return N(),E("div",null,[B(i,{ref_key:"refForm",ref:g,config:u(k)},null,8,["config"]),B(Z,{ref_key:"refDetail",ref:w,"query-params":r.queryParams,tabs:u(t).tabs,telport:e.telport,onRefreshed:a[0]||(a[0]=()=>u(t).curOperate="viewing"),onRefreshing:a[1]||(a[1]=()=>u(t).curOperate="detailing"),onClose:a[2]||(a[2]=()=>u(t).curOperate="analysed"),onRowdblclick:I},null,8,["query-params","tabs","telport"])])}}});export{Pr as default};
