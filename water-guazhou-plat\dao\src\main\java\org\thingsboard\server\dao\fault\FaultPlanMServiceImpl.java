package org.thingsboard.server.dao.fault;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.fault.FaultPlanC;
import org.thingsboard.server.dao.model.sql.fault.FaultPlanM;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskC;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskM;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.fault.FaultPlanCMapper;
import org.thingsboard.server.dao.sql.fault.FaultPlanMMapper;
import org.thingsboard.server.dao.sql.fault.FaultTaskCMapper;
import org.thingsboard.server.dao.sql.fault.FaultTaskMMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class FaultPlanMServiceImpl implements FaultPlanMService {

    @Autowired
    private FaultPlanMMapper faultPlanMMapper;

    @Autowired
    private FaultPlanCMapper faultPlanCMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private FaultTaskMMapper faultTaskMMapper;

    @Autowired
    private FaultTaskCMapper faultTaskCMapper;

    @Override
    public PageData getList(String planName, String teamName, String userName, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId) {

        List<FaultPlanM> faultPlanMList = faultPlanMMapper.getList(planName, teamName, userName, startStartTime, startEndTime, endStartTime, endEndTime, page, size, tenantId);

        int total = faultPlanMMapper.getListCount(planName, teamName, userName, startStartTime, startEndTime, endStartTime, endEndTime, tenantId);

        return new PageData(total, faultPlanMList);
    }

    @Override
    public FaultPlanM getDetail(String mainId) {
        FaultPlanM faultPlanM = faultPlanMMapper.getById(mainId);
        List<FaultPlanC> faultPlanCList = faultPlanCMapper.getList(faultPlanM.getId());
        // 所属分类链表
        for (FaultPlanC faultPlanC : faultPlanCList) {
            this.setType(faultPlanC);
        }

        faultPlanM.setFaultPlanCList(faultPlanCList);

        return faultPlanM;
    }

    @Override
    public FaultPlanM save(FaultPlanM faultPlanM) {
        try {
            if (faultPlanM.getEndTime() == null) {
                int executeDays = faultPlanM.getExecutionDays();
                int intervalDays = faultPlanM.getIntervalDays();
                int executionNum = faultPlanM.getExecutionNum();
                long intervalTime = ((executeDays + intervalDays) * executionNum) * (24 * 3600 * 1000l);
                faultPlanM.setEndTime(new Date(faultPlanM.getStartTime().getTime() + intervalTime));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (StringUtils.isBlank(faultPlanM.getId())) {
            faultPlanM.setStatus("0");
            faultPlanM.setCreateTime(new Date());

            faultPlanMMapper.insert(faultPlanM);
        } else {
            faultPlanMMapper.updateById(faultPlanM);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", faultPlanM.getId());
        faultPlanCMapper.deleteByMap(deleteMap);

        if (faultPlanM.getFaultPlanCList() != null) {
            for (FaultPlanC faultPlanC : faultPlanM.getFaultPlanCList()) {
                faultPlanC.setMainId(faultPlanM.getId());
                faultPlanC.setTenantId(faultPlanM.getTenantId());
                faultPlanC.setCreateTime(new Date());

                faultPlanCMapper.insert(faultPlanC);
            }
        }
        // 保存是生成任务，后续需要审核删除该步骤
        try {
            this.execute(faultPlanM);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return faultPlanM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        faultPlanMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<FaultPlanC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        faultPlanCMapper.delete(queryWrapper);

        return IstarResponse.ok("删除成功");
    }

    @Override
    public void reviewer(FaultPlanM faultPlanM) {
        FaultPlanM faultPlanM1 = new FaultPlanM();
        faultPlanM1.setId(faultPlanM.getId());
        faultPlanM1.setStatus(faultPlanM.getStatus());
        faultPlanM1.setReviewer(faultPlanM.getReviewer());

        faultPlanMMapper.updateById(faultPlanM1);

        // 通过则生成任务
        if ("2".equals(faultPlanM.getStatus())) {
            try {
                faultPlanM = faultPlanMMapper.selectById(faultPlanM.getId());
                this.execute(faultPlanM);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public List<FaultPlanM> findAll() {
        return faultPlanMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public void execute(FaultPlanM faultPlanM) throws ParseException {


        Integer periodTime = faultPlanM.getIntervalDays(); // 间隔天数
        Integer executionDays = faultPlanM.getExecutionDays(); // 执行天数
        Integer executionNum = faultPlanM.getExecutionNum(); // 执行次数
        Date executeTime = faultPlanM.getStartTime(); // 开始时间

        Date nextExecuteTime = null;

        if (executeTime == null) {
            return;
        }

        String type = "";

        if (periodTime == null || periodTime == 0) {
            nextExecuteTime = executeTime;
            type = "临时任务";
        } else{
            type = "计划任务";
            // 是否超过执行时间
            // if (new Date().getTime() > (executeTime.getTime() + (executionNum * periodTime * 4 * 60 * 60 * 1000l))) {
            //     return;
            // }
            boolean result = false;
            for (int i = 0; i < executionNum; i++) {
                nextExecuteTime = new Date(executeTime.getTime() + (periodTime * i * 24 * 60 * 60 * 1000));
                result = checkTimeIsToday(nextExecuteTime);
                if (result) {// 不在今天
                    break;
                }
            }
            if (!result) {// 不在今天
                return;
            }
        }

        // 查询保养设置子表内容
        List<FaultPlanC> faultPlanCList = faultPlanCMapper.getList(faultPlanM.getId());
        faultPlanM.setStartTime(executeTime);
        if (executionDays != null) {
            faultPlanM.setEndTime(new Date(executeTime.getTime() + (executionDays * 24 * 60 * 60 * 1000)));
        } else {
            if (faultPlanM.getEndTime() != null) {
                faultPlanM.setExecutionDays(Long.valueOf((faultPlanM.getEndTime().getTime() - faultPlanM.getStartTime().getTime()) / (24 * 60 * 60 * 1000)).intValue());
            }
        }
        // 生成保养任务
        FaultTaskM faultTaskM = saveFault(faultPlanM, faultPlanCList, type);
    }

    private FaultTaskM saveFault(FaultPlanM faultPlanM, List<FaultPlanC> faultPlanCList, String type) {
        // 保养任务主表
        FaultTaskM faultTaskM = new FaultTaskM();
        BeanUtils.copyProperties(faultPlanM, faultTaskM);
        faultTaskM.setId(null);
        // 新建
        faultTaskM.setCode("WX" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        faultTaskM.setStatus("0");
        faultTaskM.setType(type);
        faultTaskM.setAuditStatus("0");
        faultTaskM.setAuditor(faultPlanM.getReviewer());
        faultTaskM.setAuditorDepartment(faultPlanM.getReviewerDepartment());
        faultTaskM.setCreateTime(new Date());
        faultTaskM.setTenantId(faultPlanM.getTenantId());
        faultTaskM.setStartTime(faultPlanM.getStartTime());
        faultTaskM.setEndTime(faultPlanM.getEndTime());

        faultTaskMMapper.insert(faultTaskM);
        // 保养任务子表
        if (faultPlanCList != null && !faultPlanCList.isEmpty()) {
            for (FaultPlanC faultPlanC : faultPlanCList) {
                FaultTaskC faultTaskC = new FaultTaskC();
                BeanUtils.copyProperties(faultPlanC, faultTaskC);
                faultTaskC.setId(null);
                faultTaskC.setMainId(faultTaskM.getId());
                faultTaskC.setStatus("0");
                faultTaskC.setCreateTime(new Date());
                faultTaskCMapper.insert(faultTaskC);
            }
        }

        return faultTaskM;
    }

    private void setType(FaultPlanC faultPlanC) {
        if (StringUtils.isBlank(faultPlanC.getTypeId())) {
            faultPlanC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = faultPlanC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        faultPlanC.setTopType(topType);
        faultPlanC.setLinkedType(linkedType);
        faultPlanC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                faultPlanC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }

}
