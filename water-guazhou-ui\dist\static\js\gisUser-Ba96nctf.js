import{Z as a,W as o}from"./index-r0dFAfgr.js";const t=a.create({baseURL:"/water"});t.interceptors.request.use(e=>{const s=o().gToken;return e.headers&&(e.headers["Authorization-Token"]=s),e},e=>Promise.reject(e));t.interceptors.response.use(e=>e,e=>Promise.reject(e));const r=t,i=e=>r({url:"/api/GetUserListByValveNumberList",method:"post",data:{vnums:e.join(","),pi:1,ps:2500}}),u=e=>r({url:"/api/GetLastPayDetailByYhbh",method:"get",params:{yhbh:e}}),h=e=>r({url:"/api/GetUserInfoByYhbh",method:"get",params:{yhbh:e}});export{h as a,u as b,i as g};
