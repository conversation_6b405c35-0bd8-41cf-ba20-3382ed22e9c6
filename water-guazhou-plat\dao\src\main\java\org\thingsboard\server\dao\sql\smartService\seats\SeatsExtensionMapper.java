package org.thingsboard.server.dao.sql.smartService.seats;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsExtension;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface SeatsExtensionMapper extends BaseMapper<SeatsExtension> {

    List<SeatsExtension> getList(@Param("keywords") String keywords, @Param("tenantId") String tenantId);

    List<SeatsExtension> getNotBindList(@Param("tenantId") String tenantId);
}
