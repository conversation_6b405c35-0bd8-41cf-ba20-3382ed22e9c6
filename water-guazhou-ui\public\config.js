// 开启横向菜单
localStorage.setItem('horizontalMenu', true);
// 依据项目的应用
localStorage.setItem('useprojectapp', true);
// 开启着陆页
localStorage.setItem('useportal', false);
// 开启着陆页大卡片
localStorage.setItem('showBigPortalCard', false);
// 开启按钮权限
localStorage.setItem('usebtnperms', false);

var GIS_SERVER_SWITCH = true;

window.SITE_CONFIG = {
  // 当前可选值：为''时取默认设置
  SITENAME: '',
  TITLE: '智慧运营',
  SUBTITLE: '管理软件',
  SHORTMESSAGE: true,
  // 视频转码地址
  videoUploadUrl: 'http://127.0.0.1:8080',
  bimUrl: 'http://***********:8086',
  rtspUrl: 'http://***********:8888',
  yinshouUrl: 'http://***********:8011/water_project',
  fineReportURL: 'http://***********:8075',
  radarImgUrl: 'http://***********:8081/monitor/imagesCenter?suffix=',
  //APP下载地址
  appDownloadUrl: 'http://***********:9000/group1/M00/00/00/wKgK0mal5--AVM_VBIxeyrC_1xc819.apk',
  GIS_CONFIG: {
    gisApi: 'http://***********:8001/webapi',
    gisApiKey: 'AAPKbf4e22c18798464f9f15f46254975c13V9DFM0Tj5VyxUNjISsHJoWwb0OgwvhLNnh1HcRrP0-KYTPP3QWTys-xKqahU2Qac',
    gisSDK: 'http://***********:8003/arcgis_js_api/javascript/4.26',
    gisBingKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI0ZmIxMDkxMy0xY2Q0LTRlZjUtYjg1MC01MTU2MDU4OGI0ZGQiLCJpZCI6NjYzOTQsImlhdCI6MTYzMDkxOTE1Nn0.oaYfPD_JHJZT6Zo3zRwygnd9ozYH5U590WNHa6b8_3g',
    gisTdtToken: 'be4a83d8298d7aa84cb5e0ff3e1dad58',
    gisService: 'http://***********:6080/arcgis/rest/services/ANQING/',
    gisProxyService: '/arcgis/rest/services/ANQING/',
    gisUtilitiesService: 'http://***********:6080/arcgis/rest/services/Utilities/',
    gisDefaultBaseMap: 'vec_w',
    gisDefaultPoi: 'cva_w',

    gisDefaultCenter: [107.889659, 26.939568],
    gisDefaultZoom: 14,
    /**
     * 是否显示区域边界，默认false
     */
    gisShowBoundary: false,
    /**
     * 显示的区域边界名称，比如'北京'
     */
    gisBoundaryName: '北京',
    gisDissolvedService: 'PIPE_QY_ANQING_DYNAMIC_DISSOLVED/MapServer',
    /**
     * 显示的区域边界的资源文件路径，本地文件: /THREEJS/geojson/xxx_boundary.geojson
     */
    gisBoundaryFileUrl: '/THREEJS/geojson/beibei_boundary.geojson',
    gisPipeDataService: 'PIPE_QY_ANQING_DYNAMIC/MapServer',
    gisPipeDynamicService: 'PIPE_QY_ANQING_DYNAMIC/MapServer',
    gisBurstGPService: 'BurstPipeAnalyzenanbu/GPServer/BurstPipeAnalyzenanbu',
    gisConnectGPService: 'ConnectedAnalyzenanbu/GPServer/ConnectedAnalyzenanbu',
    gisPathAnalysGPService: 'PathAnalyzenanbu/GPServer/PathAnalyzenanbu',
    gisShutValveAnalysGPService: 'ShutOffValveAnalyzebinzhou/GPServer/ShutOffValveAnalyzebinzhou',
    gisExtendShutValveAnalysGPService: 'ShutOffValveExtendbinzhou/GPServer/ShutOffValveExtendbinzhou',
    gisFangDaGPService: 'PIPE_QY_DEYANG_FANGDA/MapServer',
    gisGeometryService: `Geometry/GeometryServer`,
    gisPrintTemplatePath: 'D:/installation/gis/gisdata/geotool/tfprint/printtemplates',
    gisPrintGPService: 'TFPrint/GPServer/TFPrint',
    gisPrintingToolsGPService: `PrintingTools/GPServer`,
    gisPipeFeatureServiceFeatureServer: 'PIPE_QY_ANQING_DYNAMIC/FeatureServer',
    gisPipeFeatureServiceMapServer: 'PIPE_QY_ANQING_DYNAMIC/MapServer',
    gisShowAlarms: true,
    gisSaveScheme: false,
  },
  ONE_MAP_CONFIG: {
    hideTextSymbol: false,
    textSymbolAside: false,
    hiddenMenus: ['sjcj', 'sphy'],
    hiddenSubMenus: [
      // 'ssjk-gwsc' // 高位水池
      'ssjk-znsb', // 智能水表
      'ssjk-fm'
    ]
  },
  SUPERMAP_CONFIG: {
    iserverurl: 'http://***********:8090/iserver/services/3D-SanWeiGuanWangChangJing/rest/realspace'
  },
  /**
   * 天气插件配置(和风天气)
   */
  WEATHER_CONFIG: {
    KEY: ''
  },
  /**
   * 运营总览大屏
   */
  OPERATION_OVERVIEW: {
    mapConfig: {
      defaultFilter: 'grayscale(0%) invert(100%) opacity(100%)',
      defaultBaseMap: 'vec_w'
    },
    bars: [
      { title: '智慧水务运营总览', text: '运营总览', name: 'yyzl', img: 'yyzl.png' },
      { title: '智慧水务原水专题', text: '原水专题', name: 'yszt', img: 'yszt.png' },
      { title: '智慧水务水厂专题', text: '水厂专题', name: 'sczt', img: 'sczt.png' },
      { title: '智慧水务管网专题', text: '管网专题', name: 'gwzt', img: 'gwzt.png' },
      { title: '智慧水务二供专题', text: '二供专题', name: 'egzt', img: 'egzt.png' },
      { title: '智慧水务营收专题', text: '营收专题', name: 'yins', img: 'yins.png' },
      { title: '智慧水务漏损专题', text: '漏损专题', name: 'lszt', img: 'lszt.png' },
      { title: '智慧水务决策专题', text: '决策专题', name: 'jczt', img: 'jczt.png' },
    ]
  },
  /**
   * 智慧决策配置
   */
  SMART_DECISION_CONFIG: {
    // 智慧决策大屏标题
    title: '智慧水务可视化管理平台',
    bars: [
      {
        text: '公司概况',
        name: 'overview'
      },
      // {
      //   text: '智慧泵房',
      //   name: 'smartPump',
      // },
      {
        text: '智慧生产',
        name: 'produce'
      },
      {
        text: '智慧管网',
        name: 'pipeNetwork'
      },
      {
        text: '智慧营销',
        name: 'smartMarketing'
      },
      {
        text: '智慧运营',
        name: 'operation'
      },
      {
        text: '智慧服务',
        name: 'smartService'
      },
      {
        text: '智慧决策',
        name: 'decisionMaking'
      }
    ],
    d3MapConfig: {

    }
  },
  /**
   * 污水厂配置
   */
  SEWAGE_CONFIG: {
    sewageName: '污水厂',
    content:
      '是处理工业污水量最大和处理工艺最先进的污水厂。',
    /**
     * 大屏配置
     */
    largeScreen: {
      /**
       * 标题
       */
      title: '第二污水厂可视化平台'
    }
  },
  SMART_SWAGE_CRAFT_CONFIG: {
    title: '智慧污水工艺可视化大屏'
  },
  /**
   * 登录页配置
   */
  LOGIN_CONFIG: {
    /**
     * 显示底部信息
     */
    SHOWFOOTER: true,
    /**
     * 显示版权信息
     */
    SHOWCOPYRIGHT: false,
    /**
     * 版权信息
     */
    COPYRIGHT: '',
    /**
     * app链接
     */
    APPURL: 'http://***********:5500/9a84fc253a1f8947294414fddbbdd906.apk',
    /**
     * 微信公众号链接
     */
    WXPUBLICACCOUNTURL: '',
    /**
     * 显示插件
     */
    SHOWPLUGINS: true,
    /**
     * 显示二维码
     */
    SHOWQRCODE: true
  },
  LAYOUT: {
    SHOWBELL: true
  },
  /**
   * 表单里面的特殊配置
   */
  FORM: {
    /**
     * 表单里面的地图默认配置
     */
    GIS_CONFIG: {
      defaultBaseMap: 'vec_w'

    }
  }
};
