"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4499],{32243:(e,t,n)=>{function o(e){return e=e||globalThis.location.hostname,m.some((t=>null!=e?.match(t)))}function r(e,t){return e&&(t=t||globalThis.location.hostname)?null!=t.match(s)||null!=t.match(a)?e.replace("static.arcgis.com","staticdev.arcgis.com"):null!=t.match(l)||null!=t.match(i)?e.replace("static.arcgis.com","staticqa.arcgis.com"):e:e}n.d(t,{XO:()=>o,pJ:()=>r});const s=/^devext.arcgis.com$/,l=/^qaext.arcgis.com$/,a=/^[\w-]*\.mapsdevext.arcgis.com$/,i=/^[\w-]*\.mapsqa.arcgis.com$/,m=[/^([\w-]*\.)?[\w-]*\.zrh-dev-local.esri.com$/,s,l,/^jsapps.esri.com$/,a,i]},72245:(e,t,n)=>{n.d(t,{K3:()=>r,Rx:()=>s});var o=n(80442);const r=()=>!!(0,o.Z)("enable-feature:force-wosr"),s=()=>!!(0,o.Z)("enable-feature:SceneLayer-editing")},21878:(e,t,n)=>{n.d(t,{im:()=>N,cW:()=>Z,vX:()=>C});var o=n(9790),r=n(20102),s=n(70586),l=n(84230),a=n(87223),i=n(59390),m=n(42143),c=n(4095),y=n(98587),u=n(77987),f=n(37898),p=n(20256),b=n(3456),d=n(86114),h=n(78724),g=n(20825);const w={retainId:!1,ignoreDrivers:!1,hasLabelingContext:!0};function S(e,t=w){if(!e)return{symbol:null};const{retainId:n=w.retainId,ignoreDrivers:s=w.ignoreDrivers,hasLabelingContext:l=w.hasLabelingContext,retainCIM:a=w.retainCIM}=t;let S=null;if((0,o.dU)(e)||e instanceof i.Z)S=e.clone();else if("cim"===e.type){const t=e.data?.symbol?.type;if("CIMPointSymbol"!==t)return{error:new r.Z("symbol-conversion:unsupported-cim-symbol",`CIM symbol of type '${t||"unknown"}' is unsupported in 3D`,{symbol:e})};S=a?e.clone():m.Z.fromCIMSymbol(e)}else if(e instanceof c.Z)S=y.Z.fromSimpleLineSymbol(e);else if(e instanceof u.Z)S=m.Z.fromSimpleMarkerSymbol(e);else if(e instanceof f.Z)S=m.Z.fromPictureMarkerSymbol(e);else if(e instanceof p.Z)S=t.geometryType&&"mesh"===t.geometryType?b.Z.fromSimpleFillSymbol(e):d.Z.fromSimpleFillSymbol(e);else{if(!(e instanceof h.Z))return{error:new r.Z("symbol-conversion:unsupported-2d-symbol",`2D symbol of type '${e.type||e.declaredClass}' is unsupported in 3D`,{symbol:e})};S=l?g.Z.fromTextSymbol(e):m.Z.fromTextSymbol(e)}if(n&&S&&"cim"!==S.type&&(S.id=e.id),s&&(0,o.dU)(S))for(let e=0;e<S.symbolLayers.length;++e)S.symbolLayers.getItemAt(e)._ignoreDrivers=!0;return{symbol:S}}function Z(e,t,n,o){const r=$(e,{},{context:o,isLabelSymbol:!1});(0,s.pC)(r)&&(t[n]=r)}function C(e,t,n,o){const r=$(e,{},{context:o,isLabelSymbol:!0});(0,s.pC)(r)&&(t[n]=r)}function v(e){return e instanceof a.Z||e instanceof i.Z}function $(e,t,n){if((0,s.Wi)(e))return null;const{context:o,isLabelSymbol:a}=n,i=o?.origin,m=o?.messages;if("web-scene"===i&&!v(e)){const n=S(e,{retainCIM:!0,hasLabelingContext:a});return(0,s.pC)(n.symbol)?n.symbol.write(t,o):(m?.push(new r.Z("symbol:unsupported",`Symbols of type '${e.declaredClass}' are not supported in scenes. Use 3D symbology instead when working with WebScene and SceneView`,{symbol:e,context:o,error:n.error})),null)}return("web-map"===i||"portal-item"===i&&!(0,l.A2)(o?.layer))&&v(e)?(m?.push(new r.Z("symbol:unsupported",`Symbols of type '${e.declaredClass}' are not supported in web maps and portal items. Use 2D symbology and CIMSymbol instead when working with MapView`,{symbol:e,context:o})),null):e.write(t,o)}function N(e,t){return(0,o.S9)(e,null,t)}},27883:(e,t,n)=>{n.d(t,{EJ:()=>b,KV:()=>f,n2:()=>u,v9:()=>p,wm:()=>g});var o=n(3172),r=n(20102),s=n(70586),l=n(95330),a=n(17452),i=n(65587),m=n(41253),c=n(72245);let y={};function u(e,t,n){return e&&(0,s.pC)(e.styleUrl)?async function(e,t){try{return{data:(await b(e,t)).data,baseUrl:(0,a.Yd)(e),styleUrl:e}}catch(e){return(0,l.r9)(e),null}}(e.styleUrl,n):e&&(0,s.pC)(e.styleName)?function(e,t,n){const o=(0,s.pC)(t.portal)?t.portal:i.Z.getDefault();let l;const a=`${o.url} - ${o.user&&o.user.username} - ${e}`;return y[a]||(y[a]=function(e,t,n){return t.load(n).then((()=>{const o=new m.Z({disableExtraQuery:!0,query:`owner:${d} AND type:${h} AND typekeywords:"${e}"`});return t.queryItems(o,n)})).then((({results:t})=>{let o=null;const s=e.toLowerCase();if(t&&Array.isArray(t))for(const e of t){const t=e.typeKeywords?.some((e=>e.toLowerCase()===s));if(t&&e.type===h&&e.owner===d){o=e;break}}if(!o)throw new r.Z("symbolstyleutils:style-not-found",`The style '${e}' could not be found`,{styleName:e});return o.load(n)}))}(e,o,n).then((e=>(l=e,e.fetchData()))).then((t=>({data:t,baseUrl:l.itemUrl??"",styleName:e})))),y[a]}(e.styleName,t,n):Promise.reject(new r.Z("symbolstyleutils:style-url-and-name-missing","Either styleUrl or styleName is required to resolve a style"))}function f(e){return null===e||"CIMSymbolReference"===e.type?e:{type:"CIMSymbolReference",symbol:e}}function p(e,t){if("cimRef"===t)return e.cimRef;if(e.formatInfos&&!(0,c.K3)())for(const t of e.formatInfos)if("gltf"===t.type)return t.href;return e.webRef}function b(e,t){const n={responseType:"json",query:{f:"json"},...t};return(0,o.default)((0,a.Fv)(e),n)}const d="esri_en",h="Style",g="https://cdn.arcgis.com/sharing/rest/content/items/220936cc6ed342c9937abd8f180e7d1e/resources/styles/cim/{SymbolName}.json?f=json"},74499:(e,t,n)=>{n.r(t),n.d(t,{fetchSymbolFromStyle:()=>b,resolveWebStyleSymbol:()=>p});var o=n(9790),r=n(32243),s=n(20102),l=n(70586),a=n(17452),i=n(65587),m=n(25929),c=n(21878),y=n(71144),u=n(27883),f=n(23203);function p(e,t,n,o){return e.name?e.styleName&&"Esri2DPointSymbolsStyle"===e.styleName?function(e,t,n){const o=u.wm.replace(/\{SymbolName\}/gi,e.name),r=(0,l.pC)(t.portal)?t.portal:i.Z.getDefault();return(0,u.EJ)(o,n).then((e=>{const t=(0,u.KV)(e.data);return(0,c.im)(t,{portal:r,url:(0,a.mN)((0,a.Yd)(o)),origin:"portal-item"})}))}(e,t,o):(0,u.n2)(e,t,o).then((r=>b((0,l.s3)(r),e.name,t,n,o))):Promise.reject(new s.Z("symbolstyleutils:style-symbol-reference-name-missing","Missing name in style symbol reference"))}function b(e,t,n,p,b){const d=e.data,h=n&&(0,l.pC)(n.portal)?n.portal:i.Z.getDefault(),g={portal:h,url:(0,a.mN)(e.baseUrl),origin:"portal-item"},w=d.items.find((e=>e.name===t));if(!w){const e=`The symbol name '${t}' could not be found`;return Promise.reject(new s.Z("symbolstyleutils:symbol-name-not-found",e,{symbolName:t}))}let S=(0,m.f)((0,u.v9)(w,p),g),Z=w.thumbnail?.href??null;const C=w.thumbnail&&w.thumbnail.imageData;(0,r.XO)()&&(S=(0,r.pJ)(S)??"",Z=(0,r.pJ)(Z));const v={portal:h,url:(0,a.mN)((0,a.Yd)(S)),origin:"portal-item"};return(0,u.EJ)(S,b).then((r=>{const s="cimRef"===p?(0,u.KV)(r.data):r.data,l=(0,c.im)(s,v);if(l&&(0,o.dU)(l)){if(Z){const e=(0,m.f)(Z,g);l.thumbnail=new f.p({url:e})}else C&&(l.thumbnail=new f.p({url:`data:image/png;base64,${C}`}));e.styleUrl?l.styleOrigin=new y.Z({portal:n.portal,styleUrl:e.styleUrl,name:t}):e.styleName&&(l.styleOrigin=new y.Z({portal:n.portal,styleName:e.styleName,name:t}))}return l}))}}}]);