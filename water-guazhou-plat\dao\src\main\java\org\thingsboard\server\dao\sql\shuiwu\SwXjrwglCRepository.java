package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwglCEntity;

import java.util.List;

public interface SwXjrwglCRepository extends JpaRepository<SwXjrwglCEntity, String> {

    @Modifying
    void deleteByMainId(String mainId);

    List<SwXjrwglCEntity> findByMainIdOrderByOrderNum(String id);
}
