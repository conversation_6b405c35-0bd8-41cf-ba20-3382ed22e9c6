package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("wx_payment_config")
public class WxPaymentConfig {
    // id
    private String id;

    // 商户号
    private String merchantNo;

    // 支付key
    private String paymentKey;

    // 客户id
    private String tenantId;

}
