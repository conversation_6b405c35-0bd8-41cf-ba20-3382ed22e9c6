import { request } from '@/plugins/axios'

/**
 * 添加管网采集工程
 * @param params
 * @returns
 */
export const PostPipeCollect = (params: any) => {
  return request({
    url: '/api/spp/collect',
    method: 'post',
    data: params
  })
}

/**
 * 查询管网采集列表
 * @param params
 * @returns
 */
export const GetPipeCollectList = (params: any) => {
  return request({
    url: '/api/spp/collect/list',
    method: 'get',
    params
  })
}
/**
 * 删除管网采集工程
 * @param ids
 * @returns
 */
export const DeletePipeCollect = (ids: string[]) => {
  return request({
    url: '/api/spp/collect',
    method: 'delete',
    data: ids
  })
}
/**
 * 审核管网采集审核
 * @param params
 * @returns
 */
export const AuditPipeCollect = (params: {
  id: string
  remark: string
  /**
   * 审核结果  4通过  5不通过
   */
  status: string
}) => {
  return request({
    url: '/api/spp/collect/review',
    method: 'post',
    data: params
  })
}

/**
 * 接收采集任务
 * @param params
 * @returns
 */
export const ReceivePipeCollect = (id: any) => {
  return request({
    url: `/api/spp/collect/receive/${id}`,
    method: 'post'
  })
}
/**
 * 提交采集任务
 * @param params
 * @returns
 */
export const SubmitPipeCollect = (params: any) => {
  return request({
    url: '/api/spp/collect/submit',
    method: 'post',
    data: params
  })
}
/**
 * 派发任务
 * @param params
 * @returns
 */
export const DispatchPipeCollect = (params: { id: string; processUser: string; reviewUser: string }) => {
  return request({
    url: '/api/spp/collect/assign',
    method: 'post',
    data: params
  })
}
/**
 * 查询采集数据列表
 * @param params
 * @returns
 */
export const GetPipeCollectDataList = (params: any) => {
  return request({
    url: '/api/spp/collect/data/list',
    method: 'get',
    params
  })
}
/**
 * 上传采集数据
 * @param params
 * @returns
 */
export const PostPipeCollectData = (params: any) => {
  return request({
    url: '/api/spp/collect/data',
    method: 'post',
    data: params
  })
}
/**
 * 删除采集数据
 * @param ids
 * @returns
 */
export const DeletePipeCollectData = (ids: string[]) => {
  return request({
    url: '/api/spp/collect/data',
    method: 'delete',
    data: ids
  })
}
/**
 * 查询采集数据的图层id列表
 * @param params
 * @returns
 */
export const GetPipeCollectDataLayerids = (id?: string) => {
  return request({
    url: `/api/spp/collect/layerIdList?mainId=${id}`,
    method: 'get'
  })
}
