package org.thingsboard.server.dao.model.sql.workOrder;

public enum WorkOrderCollaborationStatus {
    PENDING("审核中"),
    APPROVED("审核通过"),
    REJECTED("审核驳回"),
    ;

    private final String detailName;

    WorkOrderCollaborationStatus(String detailName) {
        this.detailName = detailName;
    }

    public static WorkOrderCollaborationStatus indexOf(int ordinal) {
        return values()[ordinal];
    }

    @SuppressWarnings("unused")
    public String getDetailName() {
        return detailName;
    }


}
