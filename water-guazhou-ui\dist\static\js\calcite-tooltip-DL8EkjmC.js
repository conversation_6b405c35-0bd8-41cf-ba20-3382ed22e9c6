import{T as o,d as t}from"./tooltip-Bo4Z0HDM.js";import"./widget-BcWKanF2.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./openCloseComponent-aiDFLC5b.js";import"./debounce-x6ZvqDEC.js";import"./guid-DO7TRjsS.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const a=o,c=t;export{a as CalciteTooltip,c as defineCustomElement};
