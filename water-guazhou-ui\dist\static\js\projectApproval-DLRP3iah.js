import{d as P,M as R,c as b,a8 as h,s as S,r as m,S as E,x as o,bF as L,a9 as A,o as W,g as B,n as z,q as u,i as s,F as O,b6 as q,b7 as U}from"./index-r0dFAfgr.js";import{_ as $}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as G}from"./CardTable-rdWOL4_6.js";import{_ as Q}from"./CardSearch-CB_HNR-Q.js";import{I as _}from"./common-CvK_P_ao.js";import{e as H,f as J,h as K,i as X,g as Z}from"./projectManagement-CDcrrCQ1.js";import{g as ee}from"./manage-BReaEVJk.js";import{i as te,f as ae,j as ie}from"./device-DWHb0XjG.js";import{f as V}from"./DateFormatter-Bm9a68Ax.js";import le from"./detail-DEo1RlcF.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const oe={class:"wrapper"},Oe=P({__name:"projectApproval",setup(ne){const{$btnPerms:g}=R(),T=b(),v=b(),k=b(),j=b(),x=b(),w=b({filters:[{label:"项目编号",field:"code",type:"input"},{label:"项目名称",field:"name",type:"input"},{label:"项目类别",field:"typeId",type:"select",options:h(()=>l.projectType)},{label:"启动时间",field:"time",type:"daterange",format:"x"}],operations:[{type:"btn-group",btns:[{perm:g("RoleManageAdd"),text:"新增",type:"success",icon:_.ADD,click:()=>Y()},{type:"default",perm:!0,text:"导出",icon:_.DOWNLOAD,click:()=>{H().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","项目立项.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:S(U),click:()=>{var e;(e=T.value)==null||e.resetForm(),d()}},{perm:!0,text:"查询",icon:_.QUERY,click:()=>d()}]}]}),c=m({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"项目编号",prop:"code"},{label:"项目名称",prop:"name"},{label:"项目类别",prop:"typeName"},{label:"项目概算(万元)",prop:"estimate"},{label:"启动时间",prop:"startTime",formatter:e=>V(e.startTime,"YYYY-MM-DD")},{label:"预计结束时间",prop:"expectEndTime",formatter:e=>V(e.expectEndTime,"YYYY-MM-DD")},{label:"项目负责人",prop:"principal"},{label:"设备状态",prop:"cs"}],operationWidth:"350px",operations:[{isTextBtn:!1,text:"详情",perm:g("RoleManageEdit"),click:e=>{var t;l.selected=e,(t=k.value)==null||t.openDrawer()}},{isTextBtn:!1,text:"查看设备",perm:g("RoleManageEdit"),click:e=>{var t;y.defaultValue={projectCode:e.code},l.getProjectDeviceValue(),(t=j.value)==null||t.openDialog()}},{isTextBtn:!1,type:"success",text:"编辑",perm:g("RoleManageEdit"),click:e=>N(e)},{disabled:e=>!e.canBeDelete,isTextBtn:!1,type:"danger",text:"删除",perm:g("RoleManageEdit"),click:e=>{E("确定删除？","提示信息").then(()=>{J(e.id).then(t=>{var a;((a=t.data)==null?void 0:a.code)===200?(o.success("删除成功"),d()):o.warning("删除失败")}).catch(t=>{o.warning(t)})})}},{isTextBtn:!1,text:"添加设备",perm:g("RoleManageEdit"),click:e=>{var a;const t=n.group[0].fields.find(i=>i.type==="table");t.config.selectList=[],n.defaultValue={projectCode:e.code},l.geDeviceListValue(),(a=x.value)==null||a.openDialog()}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{c.pagination.page=e,c.pagination.limit=t,d()}}}),y=m({title:"查看设备",labelWidth:"130px",dialogWidth:"1000px",defaultValue:{},group:[{fields:[{type:"table",field:"deviceTable",config:{indexVisible:!0,height:"350px",dataList:h(()=>l.deviceInformation),columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"amount"}],operations:[{perm:!0,text:"删除",icon:_.DELETE,click:e=>{E("确定删除该设备","删除提示").then(()=>{K(e.id).then(()=>{o.success("删除成功"),l.getProjectDeviceValue()}).catch(t=>{o.warning(t)})})}}],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{const a=y.group[0].fields.find(i=>i.type==="table");a.config.pagination.page=e,a.config.pagination.limit=t,l.getProjectDeviceValue()}}}}]}]}),p=m({title:"添加项目",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{if(e.expectEndTime&&e.startTime&&e.expectEndTime<e.startTime){o.warning("预计结束时间不能小于开始时间");return}p.submitting=!0;let t="新增";e.id&&(t="修改"),X(e).then(a=>{var i;p.submitting=!1,a.data.code===200?(o.success(t+"成功"),(i=v.value)==null||i.closeDialog(),d()):o.warning(t+"失败")}).catch(a=>{p.submitting=!1,o.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"项目编号",field:"code"},{xs:12,type:"input",label:"项目名称",field:"name",rules:[{required:!0,message:"请输入项目名称"}]},{xs:12,type:"select",label:"项目类别",field:"typeId",options:h(()=>l.projectType)},{xs:12,type:"input",label:"项目规模",field:"scale"},{xs:12,type:"input",label:"项目负责人",field:"principal",rules:[{required:!0,message:"请输入项目负责人"}]},{xs:12,type:"input",label:"联系电话",field:"phone"},{xs:12,type:"input",label:"项目单位",field:"organization",rules:[{required:!0,message:"请输入项目单位"}]},{xs:12,type:"number",label:"项目概算(万元)",field:"estimate"},{xs:12,type:"date",format:"x",label:"项目启动时间",field:"startTime"},{xs:12,type:"date",format:"x",label:"项目预计结算时间",field:"expectEndTime"},{type:"input",label:"详细地址",field:"address"},{type:"input",label:"项目概况",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),n=m({title:"添加设备",labelWidth:"80px",dialogWidth:"1000px",defaultValue:{},submitting:!1,submit:(e,t)=>{var a,i;if(t)l.geDeviceListValue(e);else{let f=!1;const D=n.group[0].fields.find(r=>r.type==="table");((a=D.config.selectList)==null?void 0:a.length)===0&&(o.warning("请选中设备"),f=!0);const F=(i=D.config.selectList)==null?void 0:i.map(r=>((r.amount===0||!r.amount)&&(o.warning("数量最少为1台"),f=!0),{serialId:r.serialId,amount:r.amount||0}));if(f)return;n.submitting=!0,te(e.projectCode,F).then(r=>{var C;n.submitting=!1,r.data.code===200?(o.success("添加成功"),(C=x.value)==null||C.closeDialog(),d()):o.warning("添加失败")}).catch(r=>{n.submitting=!1,o.warning(r)})}},group:[{fields:[{xs:6,type:"input",field:"serialId",label:"设备编码"},{xs:6,type:"input",field:"name",label:"设备名称"},{xs:6,type:"input",field:"model",label:"设备型号"},{xs:6,type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>{var e;(e=x.value)==null||e.Submit(!0)}},{text:"重置",perm:!0,type:"default",click:()=>{var e;(e=x.value)==null||e.resetForm(),l.geDeviceListValue()}}]},{type:"table",config:{indexVisible:!0,height:"350px",dataList:h(()=>l.deviceInformation),handleSelectChange:e=>{const t=n.group[0].fields.find(a=>a.type==="table");t.config.selectList=e},selectList:[],columns:[{label:"设备名称",prop:"name"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topTypeName"},{label:"所属分类",prop:"typeName"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"申请数量",prop:"amount",minWidth:"120px",formItemConfig:{type:"number",field:"amount",min:0}}],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{const a=n.group[0].fields.find(i=>i.type==="table");a.config.pagination.page=e,a.config.pagination.limit=t,l.geDeviceListValue()}}}}]}]}),I=m({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),M=m({defaultValue:h(()=>l.selected),border:!1,direction:"horizontal",column:1,title:"项目基础信息",fields:[{type:"text",label:"项目编号:",field:"code"},{type:"text",label:"项目名称:",field:"name"},{type:"text",label:"项目类别:",field:"typeName"},{type:"text",label:"项目规模:",field:"scale"},{type:"text",label:"项目单位:",field:"organization"},{type:"text",label:"项目负责人:",field:"principal"},{type:"text",label:"联系电话:",field:"phone"},{type:"text",label:"项目概算(万元):",field:"estimate"},{type:"text",label:"项目启动时间:",field:"startTimeName",formatter:e=>L(e).format("YYYY-MM-DD")},{type:"text",label:"项目预计结束时间:",field:"expectEndTimeName",formatter:e=>L(e).format("YYYY-MM-DD")},{type:"text",label:"详细地址:",field:"address"},{type:"text",label:"项目概况:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]}),Y=()=>{var e;p.title="新增",p.defaultValue={},(e=v.value)==null||e.openDialog()},N=e=>{var t;p.title="编辑",p.defaultValue={...e||{}},(t=v.value)==null||t.openDialog()},l=m({projectType:[],deviceInformation:[],selected:{},getOptions:()=>{ee({page:1,size:-1}).then(e=>{l.projectType=A(e.data.data.data||[])})},geDeviceListValue:e=>{l.deviceInformation=[];const t=n.group[0].fields.find(i=>i.type==="table"),a={page:t.config.pagination.page||1,size:t.config.pagination.limit||20,...e};ae(a).then(i=>{l.deviceInformation=i.data.data.data||[],t.config.pagination.total=i.data.data.total||0})},getProjectDeviceValue:()=>{var a;l.deviceInformation=[];const e=y.group[0].fields.find(i=>i.type==="table"),t={page:e.config.pagination.page||1,size:e.config.pagination.limit||20};ie((a=y.defaultValue)==null?void 0:a.projectCode,t).then(i=>{l.deviceInformation=i.data.data.data||[],e.config.pagination.total=i.data.data.total||0})}}),d=async()=>{var t;const e={size:c.pagination.limit||20,page:c.pagination.page||1,...((t=T.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.startTimeFrom=e.time[0],e.startTimeTo=e.time[1],delete e.time),Z(e).then(a=>{c.dataList=a.data.data.data||[],c.pagination.total=a.data.data.total||0})};return W(()=>{d(),l.getOptions(),l.geDeviceListValue()}),(e,t)=>{const a=Q,i=G,f=$,D=q;return B(),z("div",oe,[u(a,{ref_key:"refSearch",ref:T,config:s(w)},null,8,["config"]),u(i,{config:s(c),class:"card-table"},null,8,["config"]),u(f,{ref_key:"refDeviceForm",ref:j,config:s(y)},null,8,["config"]),u(f,{ref_key:"refForm",ref:v,config:s(p)},null,8,["config"]),u(f,{ref_key:"refAddDeviceForm",ref:x,config:s(n)},null,8,["config"]),u(D,{ref_key:"refDetail",ref:k,config:s(I)},{default:O(()=>[u(le,{config:s(l).selected,basic:s(M),show:1},null,8,["config","basic"])]),_:1},8,["config"])])}}});export{Oe as default};
