package org.thingsboard.server.dao.sql.settings;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.QueryTemplate;

import java.util.List;

public interface QueryTemplateRepository extends JpaRepository<QueryTemplate, String> {
    List<QueryTemplate> findByTenantIdOrderByType(String tenantId);

    void deleteByTenantIdAndType(String tenantId, String type);

    QueryTemplate findByTenantIdAndType(String fromTimeUUID, String type);
}
