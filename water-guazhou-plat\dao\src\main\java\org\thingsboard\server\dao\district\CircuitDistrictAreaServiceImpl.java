package org.thingsboard.server.dao.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea;
import org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictAreaMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaSaveRequest;

@Service
public class CircuitDistrictAreaServiceImpl implements CircuitDistrictAreaService {
    @Autowired
    private CircuitDistrictAreaMapper mapper;

    @Override
    public IPage<SMCircuitDistrictArea> findAllConditional(CircuitDistrictAreaPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SMCircuitDistrictArea save(CircuitDistrictAreaSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(SMCircuitDistrictArea entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean isValidDistrictId(String districtId) {
        return mapper.isValidDistrictId(districtId);
    }

    @Override
    public String getPoints(String id) {
        return mapper.getPoints(id);
    }
}
