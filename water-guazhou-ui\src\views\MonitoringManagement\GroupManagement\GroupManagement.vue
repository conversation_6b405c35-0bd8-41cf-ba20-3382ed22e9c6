<!-- 视频监控管理-视频分组 -->
<template>
  <TreeBox>
    <template #tree>
      <div class="tree-box">
        <SLTree ref="refTree" :tree-data="TreeData"></SLTree>
      </div>
    </template>
    <template #default>
      <SLCard style="height: 100%">
        <el-row :gutter="20">
          <el-col :span="6">
            <SLTree ref="refTree" :tree-data="VideoTreeData"></SLTree
          ></el-col>
          <el-col :span="18">
            <div class="form_box">
              <Form ref="refForm" :config="FormConfig"></Form>
              <div>
                <el-button type="primary" @click="resetFormConfig"
                  >重置</el-button
                >
                <el-button type="success" @click="saveForm">保存</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </SLCard>
    </template>
  </TreeBox>
</template>

<script lang="ts" setup>
import {
  GeneralTable,
  CommonRequest,
  UniversalDelete,
  GeneralProcessing
} from '@/utils/GeneralProcessing';
import {
  getCameraGroupTree,
  postCameraGroup,
  deleteCameraGroup
} from '@/api/video';
import { ElMessageBox } from 'element-plus';
import { useBusinessStore } from '@/store';
import { traverse } from '@/utils/GlobalHelper';

const refForm = ref<IFormIns>();

const state = reactive({
  AreaTree: [],
  title: ''
});

const TreeData = reactive<SLTreeConfig>({
  title: '区域划分',
  data: useBusinessStore().projectList,
  currentProject: useBusinessStore().selectedProject,
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    refreshTree();
    FormConfig.defaultValue = {
      parentId: data.id
    };
    refForm.value?.resetForm();
  }
});

const VideoTreeData = reactive<SLTreeConfig>({
  title: '分组管理',
  isFilterTree: true,
  data: [],
  nodeOperations: [
    {
      iconifyIcon: 'material-symbols:add',
      perm: true,
      type: 'success',
      text: '新增分组',
      click: (data) => {
        state.title = '新增分组';
        FormConfig.defaultValue = {
          parentId: data.id
        };
        refForm.value?.resetForm();
      }
    },
    {
      iconifyIcon: 'material-symbols:delete-outline',
      perm: true,
      type: 'danger',
      text: '删除',
      click: (data) => {
        UniversalDelete(data.id, deleteCameraGroup, '确认删除该分组').then(
          () => {
            FormConfig.defaultValue = {};
            refForm.value?.resetForm();
            refreshTree();
          }
        );
      }
    }
  ],
  treeNodeHandleClick: (data) => {
    state.title = data.name;
    FormConfig.defaultValue = {
      ...data,
      parentId: data.parentId,
      orderNum: data.nodeDetail.orderNum
    };
    refForm.value?.resetForm();
  }
});

const FormConfig = reactive<IFormConfig>({
  labelWidth: '80px',
  group: [
    {
      fields: [
        {
          readonly: true,
          type: 'select-tree',
          label: '上级分组',
          field: 'parentId',
          options: computed(() => state.AreaTree) as any
        },
        {
          type: 'input',
          label: '名称',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },
        {
          type: 'input-number',
          label: '排序',
          field: 'orderNum'
        }
      ]
    }
  ],
  submit: (params: any) => {
    let text: string = params.id ? '修改' : '添加';

    ElMessageBox.confirm(`确定${text}分组` + ',是否继续?', `${text}提示`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    }).then(() => {
      GeneralProcessing(
        params,
        postCameraGroup,
        postCameraGroup,
        FormConfig
      ).then(() => {
        FormConfig.defaultValue = {};
        refForm.value?.resetForm();
        refreshTree();
      });
    });
  },
  defaultValue: {}
});

const resetFormConfig = () => {
  if (refForm.value?.dataForm) refForm.value.dataForm.name = '';
};

const saveForm = () => {
  refForm.value?.Submit();
};

const refreshTree = () => {
  const params = {
    projectId: TreeData.currentProject.id
  };
  GeneralTable(params, getCameraGroupTree).then((res) => {
    VideoTreeData.data = res.data;
    const value = [...useBusinessStore().projectList];
    const keys = traverse(res.data);
    state.AreaTree = traverse([...keys, ...value], 'children', {
      label: 'label',
      value: 'id'
    });
  });
};

onMounted(() => {
  refreshTree();
});
</script>

<style lang="scss" scoped>
.form_box {
  height: 100%;
  margin: 20px;
}
</style>
