package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_PRESSURE_REPORT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PressureReport {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_PRESSURE_AVG)
    private BigDecimal pressureAvg;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_PRESSURE_MIN)
    private BigDecimal pressureMin;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_PRESSURE_MIN_TS)
    private Date pressureMinTs;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_PRESSURE_MAX)
    private BigDecimal pressureMax;

    @Column(name = ModelConstants.TB_PRESSURE_REPORT_PRESSURE_MAX_TS)
    private Date pressureMaxTs;



}
