package org.thingsboard.server.dao.model.sql.smartService.system;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-26
 */
@TableName("tb_service_system_dict")
@Data
public class SystemDict {
    
    @TableId
    private String id;

    private String pid;

    private String code;

    private String name;

    private Integer orderNum;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private transient List<SystemDict> children = new ArrayList();
}
