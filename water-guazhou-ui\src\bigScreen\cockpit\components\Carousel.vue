<template>
  <div class="carousel-container" ref="carouselContainer">
    <div 
      class="carousel-slides" 
      :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
    >
      <div 
        v-for="(image, index) in images" 
        :key="index" 
        class="carousel-slide"
      >
        <img :src="image" alt="水厂工艺图" />
        <div class="image-caption">水厂工艺流程 {{index + 1}}</div>
      </div>
    </div>
    <div class="carousel-indicators">
      <span 
        v-for="(_, index) in images" 
        :key="index" 
        :class="['indicator', { active: index === currentIndex }]"
        @click="goToSlide(index)"
      ></span>
    </div>
    <div class="carousel-controls">
      <button class="control-btn prev" @click="prevSlide">
        <i class="arrow left"></i>
      </button>
      <button class="control-btn next" @click="nextSlide">
        <i class="arrow right"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  // 轮播图片列表
  images: {
    type: Array,
    default: () => []
  },
  // 自动播放间隔，单位毫秒
  interval: {
    type: Number,
    default: 5000
  },
  // 是否自动播放
  autoplay: {
    type: Boolean,
    default: true
  }
});

const currentIndex = ref(0);
let timer = null;

// 切换到下一张图片
const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % props.images.length;
};

// 切换到上一张图片
const prevSlide = () => {
  currentIndex.value = (currentIndex.value - 1 + props.images.length) % props.images.length;
};

// 切换到指定图片
const goToSlide = (index) => {
  currentIndex.value = index;
};

// 开始自动播放
const startAutoplay = () => {
  if (props.autoplay && props.images.length > 1) {
    stopAutoplay(); // 先清除可能存在的定时器
    timer = setInterval(nextSlide, props.interval);
  }
};

// 停止自动播放
const stopAutoplay = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

// 组件挂载后启动自动播放
onMounted(() => {
  startAutoplay();
});

// 组件卸载前清除定时器
onUnmounted(() => {
  stopAutoplay();
});
</script>

<style lang="scss" scoped>
.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 4px;
  
  .carousel-slides {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.5s ease;
    
    .carousel-slide {
      position: relative;
      min-width: 100%;
      height: 100%;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .image-caption {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        color: white;
        padding: 40px 20px 20px;
        font-size: 16px;
        text-align: left;
      }
    }
  }
  
  .carousel-indicators {
    position: absolute;
    bottom: 15px;
    right: 20px;
    display: flex;
    gap: 8px;
    
    .indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: background-color 0.3s;
      
      &.active {
        background-color: #1AC6FF;
        box-shadow: 0 0 5px rgba(26, 198, 255, 0.8);
      }
    }
  }
  
  .carousel-controls {
    .control-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s, opacity 0.3s;
      opacity: 0;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.5);
      }
      
      .arrow {
        border: solid white;
        border-width: 0 2px 2px 0;
        padding: 5px;
        
        &.left {
          transform: rotate(135deg);
          margin-left: 5px;
        }
        
        &.right {
          transform: rotate(-45deg);
          margin-right: 5px;
        }
      }
      
      &.prev {
        left: 15px;
      }
      
      &.next {
        right: 15px;
      }
    }
  }
  
  &:hover {
    .control-btn {
      opacity: 1;
    }
  }
}
</style> 