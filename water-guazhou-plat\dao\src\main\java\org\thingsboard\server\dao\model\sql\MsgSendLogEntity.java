package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@TableName("tb_msg_send_log")
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class MsgSendLogEntity {

    @TableId
    private String id;

    private String templateId;

    private String phone;

    private String param;

    private String status;

    private String creator;

    private String remark;

    @TableField(exist = false)
    private String creatorName;

    @TableField(exist = false)
    private String templateName;

    private String content;

    private String custCode;

    private Date createTime;

    private String tenantId;
}
