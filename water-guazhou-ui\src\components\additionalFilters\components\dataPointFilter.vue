<template>
  <div>
    <el-form-item label="数据点">
      <el-select
        v-model="params.model"
        placeholder="请选择"
        @change="paramsChange"
      >
        <el-option
          v-for="item in modelOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <span class="start">第</span>
      <el-select
        v-model="params.day"
        style="width:80px"
      >
        <el-option
          v-for="item in dayOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <span class="end">日</span>
      <span class="start">第</span>
      <el-select
        v-model="params.time"
        style="width:80px"
      >
        <el-option
          v-for="item in timeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <span class="end">点</span>
    </el-form-item>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['config'],
  data() {
    return {
      params: {},
      modelOptions: [
        { label: '固定时间', value: '固定时间' },
        { label: '最大值', value: '最大值' },
        { label: '平均值', value: '平均值' }
      ],
      dayOptions: [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' }
      ],
      timeOptions: [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' }
      ]
    }
  },
  methods: {
    paramsChange() {
      // this.config.handleChange(val)
      console.log(this.params)
    }
  }
}
</script>

<style lang="scss" scoped>
.start{
  display: inline-block;
  margin: 0 12px 0 24px;
}
.end{
  display: inline-block;
  margin: 0 0px 0 12px;
}
</style>
