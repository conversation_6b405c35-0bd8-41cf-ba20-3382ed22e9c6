package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;

import java.util.List;

/**
 * 分区挂机
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface PartitionMountMapper extends BaseMapper<PartitionMount> {

    IPage<PartitionMount> getList(IPage<PartitionMount> page, @Param("param") PartitionMountRequest request);

    List<PartitionMount> getAllByTenantId(@Param("tenantId") String tenantId, @Param("direction") String direction);

    List<PartitionMount> getAllByPartitionId(@Param("partitionIdList") List<String> partitionIdList, @Param("mountType") String mountType, @Param("direction") String direction, @Param("tenantId") String tenantId);
}
