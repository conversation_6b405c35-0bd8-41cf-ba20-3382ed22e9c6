<!-- 设备属性 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <SLDrawer
      :key="key"
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { getDeviceTypeTree, getDeviceListSearch, postDevice, deleteDevice, getCustomize } from '@/api/equipment_assets/equipmentManage'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refForm = ref<ISLDrawerIns>()

const refSearch = ref<ICardSearchIns>()

const key = ref((new Date()).toString())

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备编码', field: 'serialId', type: 'input' },
    { label: '设备名称', field: 'name', type: 'input' },
    { label: '设备型号', field: 'model', type: 'input' }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建')
        },
        {
          perm: true,
          text: '批量删除',
          icon: ICONS.DELETE,
          type: 'danger',
          click: () => {
            if (TableConfig.selectList?.length) { handleDelete() } else {
              ElMessage.warning('请选中需要删除的设备属性')
            }
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  handleSelectChange: val => {
    TableConfig.selectList = val
  },
  columns: [
    { label: '设备编码', prop: 'serialId' },
    { label: '设备名称', prop: 'name' },
    { label: '设备型号', prop: 'model' },
    { label: '所属大类', prop: 'topType' },
    { label: '所属类别', prop: 'linkedType' },
    { label: '设备标识', prop: 'label' },
    { label: '计量单位', prop: 'unit' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '120px',
  submit: (params: any) => {
    params.serialId = data.prepend + params.serialIdNum
    for (const i in TableConfig.dataList) {
      if (params.serialId === TableConfig.dataList[i].serialId && params.serialId !== data.serialId) {
        ElMessage.warning('设备编码重复')
        return
      }
    }
    const list:any = {}
    for (const i in params) {
      if (i.indexOf('autoField.') !== -1) {
        list[i] = params[i]
      }
    }
    params.autoField = JSON.stringify(list)
    postDevice(params).then(res => {
      if (res.data.code === 500) {
        ElMessage.success(res.data.message)
      } else {
        refForm.value?.closeDrawer()
        ElMessage.success('操作成功')
        refreshData()
      }
    }).catch(error => {
      ElMessage.warning(error)
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          text: '基本信息',
          type: 'divider'
        },
        {
          xl: 24,
          type: 'select-tree',
          label: '所属类别',
          field: 'typeId',
          checkStrictly: true,
          options: computed(() => traverse(TreeData.data)) as any,
          readonly: true
        },
        {
          xl: 24,
          type: 'hint',
          text: '注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备编码',
          field: 'serialIdNum',
          prepend: computed(() => data.prepend) as any,
          rules: [{ required: true, message: '请输入设备编码' }, { min: 6, max: 6, message: '编码为6位' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name',
          rules: [{ required: true, message: '请输入设备名称' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model',
          rules: [{ required: true, message: '请输入设备型号' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '设备标识',
          field: 'label'
        },
        {
          xl: 8,
          type: 'number',
          label: '使用年限',
          field: 'useYear',
          min: 0
        },
        {
          xl: 8,
          type: 'number',
          label: '保养周期(天)',
          labelWidth: '100px',
          field: 'maintenanceCycle',
          min: 0
        },
        {
          xl: 8,
          type: 'input',
          label: '计量单位',
          field: 'unit'
        },
        {
          xl: 8,
          type: 'number',
          label: '最小在库量',
          field: 'minStock',
          min: 0
        },
        {
          xl: 16,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          text: '自定义属性',
          type: 'divider'
        },
        {
          text: '上传文件',
          type: 'divider'
        },
        {
          type: 'file',
          label: '设备文件',
          field: 'files'
        },
        {
          type: 'image',
          label: '设备图像',
          field: 'images'
        }
      ]
    }
  ]
})
const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    refreshData()
  }
})

const clickCreatedRole = (title?: any) => {
  if (!TreeData.currentProject.id) {
    ElMessage.warning('请选中设备类型')
    return
  }
  if (TreeData.currentProject.level !== '3') {
    ElMessage.warning('请选择末级，分组无法添加')
    return
  }
  if (addOrUpdateConfig.group[0].fields.length > 16) {
    addOrUpdateConfig.group[0].fields.splice(13, addOrUpdateConfig.group[0].fields.length - 16)
  }

  data.getCustomizeValue(TreeData.currentProject.serialId)
  data.prepend = TreeData.currentProject.serialId.slice(0, 8)
  addOrUpdateConfig.title = title
  addOrUpdateConfig.defaultValue = { typeId: TreeData.currentProject.id || '', useYear: '0', maintenanceCycle: '0', minStock: '0' }
  refForm.value?.openDrawer()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  data.serialId = row.serialId
  data.prepend = row.serialId.slice(0, 8)
  row.serialIdNum = row.serialId.slice(8, 14)
  if (addOrUpdateConfig.group[0].fields.length > 16) {
    addOrUpdateConfig.group[0].fields.splice(13, addOrUpdateConfig.group[0].fields.length - 16)
  }
  data.getCustomizeValue(row.typeSerialId)
  const list = JSON.parse(row.autoField)
  addOrUpdateConfig.defaultValue = { useYear: '0', maintenanceCycle: '0', minStock: '0', ...(row) || {}, ...list }
  refForm.value?.openDrawer()
}

const handleDelete = (row?: any) => {
  SLConfirm('确定删除选中的设备?', '删除提示').then(() => {
    let ids: string[] = []
    if (row) {
      ids = [row.id]
    } else {
      ids = TableConfig?.selectList?.map(node => node.id) ?? []
    }
    deleteDevice(ids).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    })
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    refreshData()
  })
}

const data = reactive({
  prepend: '',
  serialId: '',
  // 自定义属性
  customize: [] as any[],
  getCustomizeValue: id => {
    data.customize = []
    getCustomize(id).then(res => {
      const row = res.data.data || []
      row.forEach(item => {
        const key = {
          xl: 8,
          type: 'input',
          label: item.name,
          field: 'autoField.' + item.code
        }
        data.customize.push(key)
      })

      addOrUpdateConfig.group[0].fields.splice(13, 0, ...data.customize)
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    typeId: TreeData.currentProject.id || '',
    name: '',
    model: '',
    serialId: '',
    ...refSearch.value?.queryParams
  }
  getDeviceListSearch(params).then(res => {
    TableConfig.dataList = res?.data?.data?.data || []
    TableConfig.pagination.total = res?.data?.data?.total || 0
  })
}

onMounted(async () => {
  init()
})

</script>
