<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BasePushSchemeConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration" id="BasePushSchemeConfigurationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="templateId"    column="template_id"    />
        <result property="targetType"    column="target_type"    />
        <result property="triggerType"    column="trigger_type"    />
        <result property="sendStrategy"    column="send_strategy"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBasePushSchemeConfigurationVo">
        select id, name, template_id, target_type, trigger_type, send_strategy, status, create_time from base_push_scheme_configuration
    </sql>

    <select id="selectBasePushSchemeConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration" resultMap="BasePushSchemeConfigurationResult">
        <include refid="selectBasePushSchemeConfigurationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            <if test="targetType != null  and targetType != ''"> and target_type = #{targetType}</if>
            <if test="triggerType != null  and triggerType != ''"> and trigger_type = #{triggerType}</if>
            <if test="sendStrategy != null  and sendStrategy != ''"> and send_strategy = #{sendStrategy}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>
    
    <select id="selectBasePushSchemeConfigurationById" parameterType="String" resultMap="BasePushSchemeConfigurationResult">
        <include refid="selectBasePushSchemeConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBasePushSchemeConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration">
        insert into base_push_scheme_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="templateId != null">template_id,</if>
            <if test="targetType != null">target_type,</if>
            <if test="triggerType != null">trigger_type,</if>
            <if test="sendStrategy != null">send_strategy,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="triggerType != null">#{triggerType},</if>
            <if test="sendStrategy != null">#{sendStrategy},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBasePushSchemeConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration">
        update base_push_scheme_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="targetType != null">target_type = #{targetType},</if>
            <if test="triggerType != null">trigger_type = #{triggerType},</if>
            <if test="sendStrategy != null">send_strategy = #{sendStrategy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasePushSchemeConfigurationById" parameterType="String">
        delete from base_push_scheme_configuration where id = #{id}
    </delete>

    <delete id="deleteBasePushSchemeConfigurationByIds" parameterType="String">
        delete from base_push_scheme_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>