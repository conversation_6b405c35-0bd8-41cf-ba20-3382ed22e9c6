import{m as a}from"./index-r0dFAfgr.js";function p(e){return a({url:"/api/sp/pumpManage",method:"get",params:e})}function n(e){return a({url:"/api/sp/pumpManage",method:"post",data:e})}function i(e){return a({url:`/api/sp/pumpManage/${e.id}`,method:"patch",data:e})}function r(e){return a({url:`/api/sp/pumpManage/${e}`,method:"delete"})}function u(e){return a({url:"/api/sp/pumpManage/batch",method:"post",data:e})}function o(){return a({url:"/api/sp/pumpManage/excel/template",responseType:"blob",method:"get"})}function d(e){return a({url:"/api/sp/pumpManage/excel/export",method:"get",responseType:"blob",params:e})}function s(e){return a({url:"/api/sp/pipeLineDeviceManage",method:"get",params:e})}function c(e){return a({url:"/api/sp/pipeLineDeviceManage",method:"post",data:e})}function m(e){return a({url:`/api/sp/pipeLineDeviceManage/${e.id}`,method:"patch",data:e})}function l(e){return a({url:`/api/sp/pipeLineDeviceManage/${e}`,method:"delete"})}function g(e){return a({url:"/api/sp/pipeLineDeviceManage/batch",method:"post",data:e})}function M(){return a({url:"/api/sp/pipeLineDeviceManage/excel/template",responseType:"blob",method:"get"})}function h(e){return a({url:"/api/sp/pipeLineDeviceManage/excel/export",method:"get",responseType:"blob",params:e})}function f(e){return a({url:"/api/sp/expertInfo",method:"get",params:e})}function x(e){return a({url:"/api/sp/expertInfo",method:"post",data:e})}function v(e){return a({url:"/api/sp/expertInfo/save",method:"post",data:e})}function b(e){return a({url:`/api/sp/expertInfo/${e}`,method:"delete"})}export{v as a,x as b,u as c,b as d,f as e,n as f,i as g,r as h,p as i,o as j,g as k,h as l,c as m,m as n,l as o,d as p,s as q,M as r};
