import{_ as j}from"./index-C9hz-UZb.js";import{_ as w}from"./CardTable-rdWOL4_6.js";import{_ as z}from"./CardSearch-CB_HNR-Q.js";import{d as q,a6 as H,r as f,c as x,bF as l,a8 as B,bX as E,bB as D,s as S,o as V,ah as $,bA as J,ay as U,g as X,n as G,q as m,i as y,p as h,F as k,j as K,al as Q,aj as Z,bD as tt,C as et}from"./index-r0dFAfgr.js";import{c as at}from"./statisticalAnalysis-BoRmiv4A.js";import{u as nt}from"./useStation-DJgnSZIA.js";import{p as ot}from"./printUtils-C-AxhDcd.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function rt(){return{title:{text:"",textStyle:{color:"#5470C6",fontSize:"14px"},top:10},grid:{left:120,right:50,top:50,bottom:50},legend:{top:20,type:"scroll",textStyle:{fontSize:12}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(u){let o=u[0].name+"<br/>";return u.forEach(c=>{o+=c.marker+c.seriesName+": "+c.value+"<br/>"}),o}},xAxis:{type:"value",name:"单位：小时",nameLocation:"end",nameTextStyle:{fontSize:12,color:"#666"},axisLabel:{fontSize:11},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}},yAxis:{type:"category",data:[],axisLabel:{fontSize:11,margin:8},axisTick:{alignWithLabel:!0},inverse:!0},series:[]}}const it={class:"wrapper"},st={class:"content-container"},lt={class:"left-panel"},dt={class:"right-panel"},ct={class:"chart-container"},pt=q({__name:"index",setup(O){const{getStationTree:u}=nt();H();const o=f({type:"date",treeDataType:"Station",stationId:"",title:"",chartOption:null,dataList:{}}),c=x(),_=x(),p=x(),d=f({data:[],currentProject:{}}),g=f({defaultParams:{type:"day",year:[l().format("YYYY"),l().format("YYYY")],month:[l().format("YYYY-MM"),l().format("YYYY-MM")],day:[l().format("YYYY-MM-DD"),l().format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:B(()=>d.data),label:"站点选择",onChange:e=>{var a;const t=E(d.data,"children","id",e);d.currentProject=t,o.treeDataType=(a=t.data)==null?void 0:a.type,o.treeDataType==="Station"&&(o.stationId=t.id,D(()=>{b()}))}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:S(Q),click:()=>b()},{perm:!0,text:"导出",type:"success",svgIcon:S(Z),click:()=>F()},{perm:!0,text:"打印",type:"warning",svgIcon:S(tt),click:()=>A()}]}]}),r=f({loading:!1,columns:[{prop:"pumpName",label:"站点名称",minWidth:120,align:"left"},{prop:"yesterdayRunTime",label:"运行时长",minWidth:130,align:"center"},{prop:"todayRunTime",label:"停机时长",minWidth:130,align:"center"},{prop:"stopTime",label:"停电时长",minWidth:130,align:"center"}],dataList:[],pagination:{hide:!0}}),I=e=>{const t="（小时）";r.columns=[{prop:"pumpName",label:"站点名称",minWidth:120,align:"left"},{prop:"yesterdayRunTime",label:`运行时长${t}`,minWidth:130,align:"center"},{prop:"todayRunTime",label:`停机时长${t}`,minWidth:130,align:"center"},{prop:"stopTime",label:`停电时长${t}`,minWidth:130,align:"center"}]},b=()=>{var v,M;r.loading=!0;const e=((v=_.value)==null?void 0:v.queryParams)||{},{type:t="day"}=e,a=e[t];if(!a||!a[0]||!a[1]){r.loading=!1;return}if(!o.stationId){r.loading=!1;return}const n=((M=d.currentProject)==null?void 0:M.label)||"未知泵站";o.title=`${n}运行状态分析`,I();const[i,s]=a,N={stationId:o.stationId,start:l(i).startOf(t==="day"?"day":t==="month"?"month":"year").valueOf(),end:l(s).endOf(t==="day"?"day":t==="month"?"month":"year").valueOf(),queryType:t};at(N).then(T=>{const Y=T.data.data||[],R=Array.isArray(Y)?Y.filter(W=>W.stationId===o.stationId):[],C=P(R,n,t);r.dataList=C,r.loading=!1,L(C)}).catch(T=>{console.error("查询运行状态数据失败:",T),r.loading=!1})},P=(e,t,a)=>{let n=0,i=0,s=0;return a==="day"?(n=Math.round(18+Math.random()*6),i=Math.round(Math.random()*6),s=24-n-i):a==="month"?(n=Math.round(500+Math.random()*200),i=Math.round(Math.random()*100),s=Math.round(Math.random()*50)):a==="year"&&(n=Math.round(6e3+Math.random()*2e3),i=Math.round(Math.random()*500),s=Math.round(Math.random()*300)),[{pumpName:t,yesterdayRunTime:n.toFixed(1),todayRunTime:i.toFixed(1),stopTime:s.toFixed(1)}]},L=e=>{if(!(!p.value||!e.length))try{p.value.clear(),D(()=>{var a;if(!p.value)return;const t=rt();t.yAxis.data=e.map(n=>n.pumpName),t.series=[{name:"运行时长",type:"bar",data:e.map(n=>parseFloat(n.yesterdayRunTime)),itemStyle:{color:"#5470C6"},barHeight:20},{name:"停机时长",type:"bar",data:e.map(n=>parseFloat(n.todayRunTime)),itemStyle:{color:"#91CC75"},barHeight:20},{name:"停电时长",type:"bar",data:e.map(n=>parseFloat(n.stopTime)),itemStyle:{color:"#FAC858"},barHeight:20}],o.chartOption=t,(a=p.value)==null||a.setOption(t)})}catch(t){console.error("图表渲染错误:",t)}};V(async()=>{var a,n;const e=await u("泵站");d.data=e;const t=$(e);t&&t.id&&(d.currentProject=t,o.treeDataType=((a=t.data)==null?void 0:a.type)||"Station",o.stationId=t.id),g.defaultParams={...g.defaultParams,treeData:d.currentProject},(n=_.value)==null||n.resetForm(),o.stationId&&b()});const F=()=>{var e;(e=c.value)==null||e.exportTable()},A=()=>{ot({title:o.title,data:r.dataList,titleList:r.columns})};return J(()=>{}),(e,t)=>{const a=z,n=w,i=j,s=U("VChart");return X(),G("div",it,[m(a,{ref_key:"cardSearch",ref:_,config:y(g)},null,8,["config"]),h("div",st,[h("div",lt,[m(i,{title:"泵组运行状态分析"},{default:k(()=>[m(n,{ref_key:"refTable",ref:c,class:"card-table",config:y(r)},null,8,["config"])]),_:1})]),h("div",dt,[m(i,{title:""},{default:k(()=>[h("div",ct,[m(s,{ref_key:"refChart",ref:p,theme:y(K)().isDark?"dark":"light",option:y(o).chartOption},null,8,["theme","option"])])]),_:1})])])])}}}),xt=et(pt,[["__scopeId","data-v-6f1e0e60"]]);export{xt as default};
