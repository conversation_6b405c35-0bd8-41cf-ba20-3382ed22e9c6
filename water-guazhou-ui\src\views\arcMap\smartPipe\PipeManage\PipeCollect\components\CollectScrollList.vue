<template>
  <ul
    v-infinite-scroll="handleScrollEnd"
    class="collect-list"
  >
    <li
      v-for="(item, i) in collect.list.value"
      :key="i"
      class="collect-item"
      @click="handleClick(item)"
    >
      <div
        class="item-header"
        :title="item.title"
      >
        <Icon
          :class="item.status"
          :icon="'ep:document'"
        ></Icon>
        <el-popover :trigger="'contextmenu'">
          <template #reference>
            <span class="item-title">{{ item.title }}</span>
          </template>
          <ul class="contextmenu">
            <li>
              <el-button
                style="width: 100%"
                @click="handleClip(item.title)"
              >
                复制
              </el-button>
            </li>
          </ul>
        </el-popover>
        <el-tag
          :type="'success'"
          :size="'small'"
        >
          {{ item.status }}
        </el-tag>
        <!-- <Icon
          class="arrow"
          :icon="'ep:arrow-right'"
        ></Icon> -->
      </div>
      <ul class="item-main">
        <li>
          <label>工程编号：</label><span>{{ item.code }}</span>
        </li>
        <li>
          <label>提交时间：</label><span>{{ item.time }}</span>
        </li>
        <li>
          <label>提交人：</label><span>{{ item.person }}{{ clipBoard.text.value }}</span>
        </li>
      </ul>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useClipboard } from '@vueuse/core'
// import { usePipeCollect } from '../../hooks/usePipeCollect'
import { SLMessage } from '@/utils/Message'

const clipBoard = useClipboard()
clipBoard.text.value
const emit = defineEmits(['click', 'scroll-end'])
// const collect = usePipeCollect()
const refreshData = (params?: { status?: string; title?: string }, append = false) => {
  // collect.getList(params, append)
}
const handleClip = (text?: string) => {
  text
    && clipBoard.copy(text).then(() => {
      SLMessage.success('文本已复制')
    })
}
const handleClick = (item: any) => {
  emit('click', item)
}
const handleScrollEnd = () => {
  emit('scroll-end')
}
defineExpose({
  refreshData
})
</script>
<style lang="scss" scoped>
.collect-list {
  list-style: none;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-y: auto;
}
.collect-item {
  margin-bottom: 20px;
  cursor: pointer;
}
.item-header {
  height: 32px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  .waiting {
    color: orange;
  }
  .dealing {
    color: lightblue;
  }
  .auditing {
    color: orangered;
  }
  .item-title {
    font-weight: bold;
    width: calc(100% - 60px);
    margin-left: 8px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .arrow {
    margin-left: auto;
  }
}
.item-main {
  list-style: none;
  margin: 0;
  li {
    line-height: 1.5em;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
  }
}
.contextmenu {
  list-style: none;
  padding: 0;
  margin: 0;
  li:hover {
    cursor: pointer;
  }
}
</style>
