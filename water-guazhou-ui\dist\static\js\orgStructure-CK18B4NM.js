import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as x}from"./CardTable-rdWOL4_6.js";import{_ as L}from"./CardSearch-CB_HNR-Q.js";import{I as p}from"./common-CvK_P_ao.js";import{d as S,M as k,c as m,r as y,er as E,b as l,es as T,S as C,et as q,eu as I,ar as M,o as N,g as B,n as R,q as f,i as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const V={class:"wrapper"},Q=S({__name:"orgStructure",setup(W){const{$btnPerms:u}=k(),d=m(),s=m(),h=m({filters:[{label:"供水单位/部门名称",field:"name",type:"input",labelWidth:"140px"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:p.QUERY,click:()=>n()}]}]}),r=y({defaultExpandAll:!0,rowKey:"id",columns:[{label:"供水单位/部门标号",prop:"id"},{label:"供水单位名称",prop:"name",formatter:e=>e.layer===1?e.name:" "},{label:"部门名称",prop:"name",formatter:e=>e.layer==="2"||e.layer===2?e.name:" "},{label:"部门类型",prop:"type"},{label:"排序编号",prop:"orderNum"}],operationWidth:"200px",operations:[{type:"success",isTextBtn:!0,color:"#4195f0",text:"增下级",perm:u("RoleManageEdit"),icon:p.ADD,click:e=>b(e)},{type:"primary",isTextBtn:!0,color:"#4195f0",text:"编辑",perm:u("RoleManageEdit"),hide:e=>e.layer===1,icon:p.DETAIL,click:e=>b(e,!0)},{isTextBtn:!0,type:"danger",text:"删除",hide:e=>e.layer===1,icon:p.DELETE,perm:u("RoleManageDelete"),click:e=>v(e)}],dataList:[],pagination:{refreshData:({page:e,size:a})=>{r.pagination.page=e,r.pagination.limit=a}}}),o=y({title:"部门信息",dialogWidth:"500px",submit:e=>{e.id?E(e.id,e).then(a=>{var t;if(a.data.code===200){n(),(t=s.value)==null||t.closeDialog();return}l.error(a.data.message)}).catch(a=>{l.error(a.data.message)}):T(e).then(a=>{var t;if(a.data.code===200){n(),(t=s.value)==null||t.closeDialog();return}l.error(a.data.message)}).catch(a=>{l.error(a.data.message)})},defaultValue:{},group:[{fields:[{type:"input",label:"部门名称",field:"name",rules:[{required:!0,message:"请输入部门名称"}]},{type:"select",label:"上级部门",field:"parentId",options:[],readonly:!0,rules:[{required:!0,message:"请选择上级部门"}]},{type:"select",label:"部门类型",field:"type",options:[{label:"财务",value:"财务"},{label:"报装",value:"报装"},{label:"设备安装",value:"设备安装"},{label:"客服",value:"客服"},{label:"供水",value:"供水"},{label:"单位管理",value:"单位管理"},{label:"营业",value:"营业"},{label:"办公室",value:"办公室"},{label:"企划部",value:"企划部"},{label:"技术开发",value:"技术开发"},{label:"人力资源",value:"人力资源"}],rules:[{required:!0,message:"请选择部门类型"}]},{type:"number",label:"排序",field:"orderNum",min:0}]}]}),b=(e,a=!1)=>{var t;a?(o.defaultValue={...e||{}},o.group[0].fields[1].options=[{label:e.parentName||""||"",value:e.parentId}]):(o.defaultValue={orderNum:"0",parentId:e.id||""||{}},o.group[0].fields[1].options=[{label:e.name||"",value:e.id}]),(t=s.value)==null||t.openDialog()},v=e=>{C("确定删除该部门, 是否继续?","删除提示").then(()=>{q(e.id).then(()=>{n(),l.success("删除成功")}).catch(a=>{console.log(a,"err"),l.error(a.data.message)})})},n=async()=>{var t,i,c,_;let a;(i=(t=d.value)==null?void 0:t.queryParams)!=null&&i.name?a=await I({name:((_=(c=d.value)==null?void 0:c.queryParams)==null?void 0:_.name)||""}):a=await M(2),console.log(a.data),r.dataList=a.data.data||[],r.pagination.total=a.data.total||0};return N(()=>{n()}),(e,a)=>{const t=L,i=x,c=D;return B(),R("div",V,[f(t,{ref_key:"refSearch",ref:d,config:g(h)},null,8,["config"]),f(i,{config:g(r),class:"card-table"},null,8,["config"]),f(c,{ref_key:"refForm1",ref:s,config:g(o)},null,8,["config"])])}}});export{Q as default};
