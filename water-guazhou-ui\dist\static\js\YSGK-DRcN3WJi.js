import{d as u,r as d,g as e,n as c,p as a,aB as o,aJ as p,h as f,aw as _,i as s,q as r,cU as g,C as v}from"./index-r0dFAfgr.js";import w from"./ScrollList-C0VFzhoB.js";import x from"./TargetItem-MDYkIAGN.js";import{e as B}from"./decisionMaking_center_left-CLFn6qN_.js";import"./index-BlG8PIOK.js";const k={class:"scgk-info"},N=u({__name:"YSGK",setup(b){const t=d({data:[{name:"水费回收率(%)",value:"193.1",scale:"-52.5%"},{name:"综合水价（元",value:"9.28",scale:"-13%"},{name:"抢修次数",value:"2.04",scale:"-0.8%"}],info:[{label:"年度目标售水量",value:"2,000.00",unit:"万m3",text:"完成率",scale:"16.2%",className:"annual-water-target-sale",rows:[1,2,3]},{label:"全年日均售水量",value:"3.14",unit:"万m3",text:"同比",status:"down",scale:"3.4%",className:"dayly-max-water-sale",rows:[1,2,3]}]});return(y,l)=>{const i=g;return e(),c(o,null,[a("div",k,[(e(!0),c(o,null,p(s(t).info,(n,m)=>(e(),f(x,{key:m,config:n,class:_(n.className)},null,8,["config","class"]))),128)),r(i,{fit:"contain",class:"ellipse-image",src:s(B),alt:""},null,8,["src"]),l[0]||(l[0]=a("div",{class:"annual-water-sale"},[a("div",{class:"count"}," 318.72 "),a("div",{class:"text"}," 年度累计售水量 (万m3) ")],-1))]),r(w,{data:s(t).data},null,8,["data"])],64)}}}),K=v(N,[["__scopeId","data-v-8915b8c1"]]);export{K as default};
