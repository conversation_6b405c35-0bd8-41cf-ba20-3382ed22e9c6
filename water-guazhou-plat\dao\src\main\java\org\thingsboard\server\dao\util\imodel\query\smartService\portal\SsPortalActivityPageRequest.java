package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalActivity;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SsPortalActivityPageRequest extends AdvancedPageableQueryEntity<SsPortalActivity, SsPortalActivityPageRequest> {
    // 是否启用
    private Boolean active;

    // 标题
    private String title;

}
