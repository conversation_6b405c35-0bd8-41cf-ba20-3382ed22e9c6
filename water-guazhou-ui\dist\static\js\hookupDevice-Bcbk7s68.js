import{z as e}from"./index-r0dFAfgr.js";const i=t=>e({url:"/api/device/getPartitionDevice",method:"get",params:t}),a=t=>e({url:"/api/spp/dma/partition/mount/batchSave",method:"post",data:t}),r=t=>e({url:"/api/spp/dma/partition/mount/list",method:"get",params:t}),p=t=>e({url:"/api/spp/dma/partition/mount",method:"delete",data:t}),n=t=>e({url:`/api/spp/dma/partition/mount/changeDirection/${t}`,method:"post"}),s=t=>e({url:"/api/spp/dma/partition/partitionDeviceTree",method:"get",params:t});export{p as D,r as G,a as H,n as P,s as a,i as b};
