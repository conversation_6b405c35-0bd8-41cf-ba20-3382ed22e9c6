package org.thingsboard.server.dao.util.imodel.query;

import org.apache.commons.lang3.StringUtils;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.util.Date;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Supplier;

public abstract class SaveRequest<T> implements Requestible, Identifiable, AwareTenantUUID, AwareCurrentUserUUID {
    // id 全量更新使用
    private String id;

    private String tenantId;

    private String currentUserUUID;

    private Date createTime;

    public SaveRequest() {

    }

    public void preInitializeCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date createTime() {
        if (createTime == null) {
            createTime = new Date();
        }
        return createTime;
    }

    @Override
    public void tenantId(String id) {
        tenantId = id;
    }

    public String tenantId() {
        return tenantId;
    }

    protected String defaultIfNullOrEmpty(String value, String defaultValue) {
        return isNullOrEmpty(value) ? defaultValue : value;
    }

    @Deprecated
    protected Date parseDate(String pattern) {
        if (isNullOrEmpty(pattern)) {
            return null;
        }
        // return DateTime.parse(pattern, DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")).toDate();
        return new Date(Long.parseLong(pattern));
    }

    protected int parseInt(String number) {
        if (number == null) {
            return 0;
        }
        return Integer.parseInt(number);
    }

    protected boolean parseBoolean(String pattern) {
        return Boolean.parseBoolean(pattern);
    }

    protected boolean isNullOrEmpty(String stage) {
        return StringUtils.isEmpty(stage) || StringUtils.isBlank(stage);
    }

    protected String parseUUID(String rawUUID) {
        if (rawUUID == null) {
            return null;
        }
        UUID uuid;
        try {
            uuid = UUIDConverter.fromString(rawUUID);
        } catch (IllegalArgumentException e) {
            uuid = UUID.fromString(rawUUID);
        }
        return UUIDConverter.fromTimeUUID(uuid);
    }

    public boolean isInsert() {
        return getId() == null;
    }

    public boolean isUpdate() {
        return getId() != null;
    }

    /**
     * 调用时若检测到保存实体为更新操作将会抛出异常
     */
    protected void disallowUpdate() {
        if (isUpdate())
            ExceptionUtils.silentThrow("更新操作已禁止");
    }

    public String currentUserUUID() {
        return currentUserUUID;
    }

    @Override
    public void currentUserId(String uuid) {
        this.currentUserUUID = uuid;
    }

    protected <V> void setIfNotNull(Consumer<V> setter, V value) {
        if (value != null)
            setter.accept(value);
    }

    protected abstract T build();

    protected abstract T update(String id);

    public T unwrap(String id) {
        if (allowUpdate())
            return update(id);
        return build();
    }

    public T unwrap() {
        if (allowUpdate())
            return id == null ? build() : update(id);
        return build();
    }

    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    protected boolean allowUpdate() {
        return true;
    }

    public <U extends SaveRequest<?>> U assimilation(U other) {
        other.tenantId(tenantId);
        other.currentUserId(currentUserUUID);
        other.createTime(createTime);
        return other;
    }

    void createTime(Date createTime) {
        this.createTime = createTime;
    }

    public void toUpdateMode(String id) {
        Objects.requireNonNull(id);
        this.id = id;
    }

    public void toUpdateModeOn(String id) {
        if (id != null) {
            toUpdateMode(id);
        }
    }

    public void preCheckUpdate(Supplier<Boolean> condition, String message) {
        if (condition.get()) {
            return;
        }
        ExceptionUtils.silentThrow(message);
    }
}
