<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardArrangePartnerMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        partner.id,
        partner.arrange_id,
        partner.user_id,
        arrange.class_name,
        arrange.begin_time,
        arrange.end_time,
        (select customer_role.name
         from customer_user_role
                  join customer_role
                       on customer_user_role.role_id = customer_role.id
         where user_id = partner.user_id)                      role_name,
        (select phone from tb_user where id = partner.user_id) phone
        <!--@sql from guard_arrange_partner partner, guard_arrange arrange -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner">
        <result column="id" property="id"/>
        <result column="arrange_id" property="arrangeId"/>
        <result column="class_name" property="className"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="user_id" property="userId"/>
        <result column="role_name" property="roleName"/>
        <result column="phone" property="phone"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_arrange_partner partner,
             guard_arrange arrange
        <where>
            <if test="arrangeId != null and arrangeId != ''">
                and arrange_id in
                <foreach item="item" index="index" collection="arrangeId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and create_time between #{beginTime} and #{endTime}
        </where>

    </select>

    <delete id="deleteAllByArrangeId">
        delete
        from guard_arrange_partner
        where arrange_id = #{arrangeId}
    </delete>

    <insert id="saveAll">
        INSERT INTO guard_arrange_partner(id,
                                          arrange_id,
                                          user_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.arrangeId},
             #{element.userId})
        </foreach>
    </insert>

    <select id="selectPendingArrangePartnerSaveTemplate"
            resultType="org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangePartnerSaveRequest">
        select pending_arrange.a_id        as arrangeId,
               guard_group_partner.user_id as userId
        from guard_group_partner
                 join (select guard_arrange.id, guard_arrange.group_id
                       from guard_arrange
                       where not exists(select 1 from guard_arrange_partner where arrange_id = guard_arrange.id)) pending_arrange(a_id, a_group_id)
                      on guard_group_partner.group_id = pending_arrange.a_group_id
    </select>

    <delete id="removeByArrangeIdIn">
        delete from guard_arrange_partner
        where arrange_id in
        <foreach item="item" index="index" collection="arrangeIdCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>