package org.thingsboard.server.dao.alarmV2;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.AlarmRuleDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleListRequest;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRule;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleUser;
import org.thingsboard.server.dao.sql.alarmV2.AlarmRuleMapper;
import org.thingsboard.server.dao.station.StationAttrService;

import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.ALARM_CACHE;
import static org.thingsboard.server.common.data.CacheConstants.ALARM_RULE_V2_CACHE;

@Slf4j
@Service
public class AlarmRuleServiceImpl implements AlarmRuleService {

    @Autowired
    private AlarmRuleMapper alarmRuleMapper;

    @Autowired
    private StationAttrService stationAttrService;

    @Autowired
    private AlarmRuleUserService alarmRuleUserService;

    @Override
    public PageData<AlarmRuleDTO> findList(AlarmRuleListRequest request, TenantId tenantId) {
        Page<AlarmRule> pageRequest = new Page<>(request.getPage(), request.getSize());
        // 查看是否按照orgId查询
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        IPage<AlarmRuleDTO> pageResult = alarmRuleMapper.findList(pageRequest, request);
        // 获取通知人
        List<AlarmRuleDTO> records = pageResult.getRecords();
        if (records.size() > 0) {
            List<String> ruleIds = records.stream().map(AlarmRuleDTO::getId).collect(Collectors.toList());
            List<AlarmRuleUser> alarmRuleUsers = alarmRuleUserService.findByRuleIdList(ruleIds);
            Map<String, Map<String, List<AlarmRuleUser>>> userListMap = new HashMap<>();
            for (AlarmRuleUser alarmRuleUser : alarmRuleUsers) {
                if (userListMap.get(alarmRuleUser.getMainId()) == null) {
                    Map<String, List<AlarmRuleUser>> map = new HashMap<>();
                    map.put("1", new ArrayList<>());
                    map.put("2", new ArrayList<>());
                    userListMap.put(alarmRuleUser.getMainId(), map);
                }
                userListMap.get(alarmRuleUser.getMainId()).get(alarmRuleUser.getType()).add(alarmRuleUser);
            }
            for (AlarmRuleDTO record : records) {
                record.setRuleParamObj(JSON.parseObject(record.getRuleParam()));
                if (userListMap.get(record.getId()) != null) {
                    record.setMsgList(userListMap.get(record.getId()).get("1"));
                    record.setAppList(userListMap.get(record.getId()).get("2"));
                }
            }
        }

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    @CacheEvict(cacheNames = {ALARM_RULE_V2_CACHE, ALARM_CACHE}, allEntries = true)
    public void save(AlarmRuleDTO alarmRuleDTO, TenantId tenantId) throws ThingsboardException {
        // 查询站点ID
        AlarmRule entity = new AlarmRule();
        BeanUtils.copyProperties(alarmRuleDTO, entity);
        StationAttrEntity stationAttr = stationAttrService.findById(entity.getStationAttrId());
        if (stationAttr == null) {
            throw new ThingsboardException("提交的站点属性不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        entity.setStationId(stationAttr.getStationId());
        entity.setRuleParam(entity.getRuleParamObj() == null ? "" : entity.getRuleParamObj().toJSONString());
        if (StringUtils.isNotBlank(entity.getId())) {
            alarmRuleMapper.updateById(entity);
        } else {
            entity.setCreateTime(new Date());
            entity.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            alarmRuleMapper.insert(entity);
        }

        alarmRuleUserService.save(entity.getId(), alarmRuleDTO.getMsgList(), alarmRuleDTO.getAppList());
    }

    @Override
    @CacheEvict(cacheNames = {ALARM_RULE_V2_CACHE, ALARM_CACHE}, allEntries = true)
    public void remove(List<String> ids) {
        for (String id : ids) {
            alarmRuleMapper.deleteById(id);
        }
    }

    @Override
    @Cacheable(cacheNames = ALARM_RULE_V2_CACHE, key = "{#stationAttrList}")
    public List<AlarmRule> findByStationAttrList(List<String> stationAttrList) {
        QueryWrapper<AlarmRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("station_attr_id", stationAttrList);
        return alarmRuleMapper.selectList(queryWrapper);
    }
}
