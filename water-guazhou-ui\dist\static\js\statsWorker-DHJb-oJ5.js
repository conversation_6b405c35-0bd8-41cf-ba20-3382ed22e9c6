import{bE as C,cx as S,e3 as O,e4 as R}from"./MapView-DaoQedLH.js";import{ad as j,R as A,w as B,b as q}from"./Point-WxyopZva.js";import{aO as G}from"./index-r0dFAfgr.js";import{O as Y}from"./quantizationUtils-DtI9CsYu.js";import{c as E,D as _,m as k,f as U,d as Z,T as H,y as J,x as K,z as L,S as Q,M as W}from"./utils-DcsZ6Otn.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./generateRendererUtils-Bt0vqUD2.js";let y=null;function X(e,a,l){return e.x<0?e.x+=a:e.x>l&&(e.x-=a),e}function aa(e,a,l,o){const n=j(l)?A(l):null,s=n?Math.round((n.valid[1]-n.valid[0])/a.scale[0]):null;return e.map(t=>{const i=new B(G(t.geometry));return Y(a,i,i,i.hasZ,i.hasM),t.geometry=n?X(i,s,o[0]):i,t})}function ea(e,a=18,l,o,n,s){const t=new Float64Array(n*s);a=Math.round(S(a));let i=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY,m=0,f=0,d=0,c=0;const z=O(o,l);for(const{geometry:T,attributes:v}of e){const{x:p,y:u}=T,x=Math.max(0,p-a),F=Math.max(0,u-a),D=Math.min(s,u+a),b=Math.min(n,p+a),g=+z(v);for(let $=F;$<D;$++)for(let w=x;w<b;w++){const M=$*n+w,P=R(w-p,$-u,a),N=t[M];m=t[M]+=P*g;const V=m-N;f+=V,d+=V*V,m<i&&(i=m),m>r&&(r=m),c++}}if(!c)return{mean:0,stddev:0,min:0,max:0,mid:0,count:0};const I=(r-i)/2;return{mean:f/c,stdDev:Math.sqrt((d-f*f/c)/c),min:i,max:r,mid:I,count:c}}async function h(e,a){if(!a)return[];const{field:l,field2:o,field3:n,fieldDelimiter:s}=e,t=e.valueExpression,i=e.normalizationType,r=e.normalizationField,m=e.normalizationTotal,f=[],d=e.viewInfoParams;let c=null,z=null;if(t){if(!y){const{arcadeUtils:v}=await C();y=v}c=y.createFunction(t),z=d&&y.getViewInfo({viewingMode:d.viewingMode,scale:d.scale,spatialReference:new q(d.spatialReference)})}const I=e.fieldInfos,T=!(a[0]&&"declaredClass"in a[0]&&a[0].declaredClass==="esri.Graphic")&&I?{fields:I}:null;return a.forEach(v=>{const p=v.attributes;let u;if(t){const x=T?{...v,layer:T}:v,F=y.createExecContext(x,z);u=y.executeFunction(c,F)}else p&&(u=p[l],o&&(u=`${E(u)}${s}${E(p[o])}`,n&&(u=`${u}${s}${E(p[n])}`)));if(i&&typeof u=="number"&&isFinite(u)){const x=p&&parseFloat(p[r]);u=_(u,i,x,m)}f.push(u)}),f}async function ua(e){const{attribute:a,features:l}=e,{normalizationType:o,normalizationField:n,minValue:s,maxValue:t,fieldType:i}=a,r=await h({field:a.field,valueExpression:a.valueExpression,normalizationType:o,normalizationField:n,normalizationTotal:a.normalizationTotal,viewInfoParams:a.viewInfoParams,fieldInfos:a.fieldInfos},l),m=k({normalizationType:o,normalizationField:n,minValue:s,maxValue:t}),f={value:.5,fieldType:i},d=i==="esriFieldTypeString"?U({values:r,supportsNullCount:m,percentileParams:f}):Z({values:r,minValue:s,maxValue:t,useSampleStdDev:!o,supportsNullCount:m,percentileParams:f});return H(d,i==="esriFieldTypeDate")}async function fa(e){const{attribute:a,features:l}=e,o=await h({field:a.field,field2:a.field2,field3:a.field3,fieldDelimiter:a.fieldDelimiter,valueExpression:a.valueExpression,viewInfoParams:a.viewInfoParams,fieldInfos:a.fieldInfos},l),n=J(o);return K(n,a.domains,a.returnAllCodedValues,a.fieldDelimiter)}async function da(e){const{attribute:a,features:l}=e,{field:o,normalizationType:n,normalizationField:s,normalizationTotal:t,classificationMethod:i}=a,r=await h({field:o,valueExpression:a.valueExpression,normalizationType:n,normalizationField:s,normalizationTotal:t,viewInfoParams:a.viewInfoParams,fieldInfos:a.fieldInfos},l),m=L(r,{field:o,normalizationType:n,normalizationField:s,normalizationTotal:t,classificationMethod:i,standardDeviationInterval:a.standardDeviationInterval,numClasses:a.numClasses,minValue:a.minValue,maxValue:a.maxValue});return Q(m,i)}async function ca(e){const{attribute:a,features:l}=e,{field:o,normalizationType:n,normalizationField:s,normalizationTotal:t,classificationMethod:i}=a,r=await h({field:o,valueExpression:a.valueExpression,normalizationType:n,normalizationField:s,normalizationTotal:t,viewInfoParams:a.viewInfoParams,fieldInfos:a.fieldInfos},l);return W(r,{field:o,normalizationType:n,normalizationField:s,normalizationTotal:t,classificationMethod:i,standardDeviationInterval:a.standardDeviationInterval,numBins:a.numBins,minValue:a.minValue,maxValue:a.maxValue})}async function pa(e){const{attribute:a,features:l}=e,{field:o,radius:n,fieldOffset:s,transform:t,spatialReference:i,size:r}=a,m=aa(l,t,i,r),{count:f,min:d,max:c,mean:z,stdDev:I}=ea(m,n,s,o,r[0],r[1]);return{count:f,min:d,max:c,avg:z,stddev:I}}export{da as classBreaks,pa as heatmapStatistics,ca as histogram,ua as summaryStatistics,fa as uniqueValues};
