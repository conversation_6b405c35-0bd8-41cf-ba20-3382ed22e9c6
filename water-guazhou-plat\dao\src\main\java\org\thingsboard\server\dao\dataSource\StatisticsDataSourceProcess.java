package org.thingsboard.server.dao.dataSource;

import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.dataSource.DataFromDataSource;
import org.thingsboard.server.common.data.dataSource.DataSourceRequest;
import org.thingsboard.server.common.data.dataSource.StatisticsBaseDataSource;

/**
 * <AUTHOR>
 * @date 2020/2/27 14:19
 */
@Service
public class StatisticsDataSourceProcess {


    /**
     * 获取统计数据源数据
     * @param statisticsDataSource 数据源
     * @param dataSourceRequest   请求
     * @return 数据体
     */
    public DataFromDataSource processDeviceDataSource(StatisticsBaseDataSource statisticsDataSource, DataSourceRequest dataSourceRequest){
           return null;
    }
}
