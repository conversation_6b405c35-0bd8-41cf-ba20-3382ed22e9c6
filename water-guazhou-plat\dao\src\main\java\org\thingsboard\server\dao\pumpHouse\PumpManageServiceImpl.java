package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage;
import org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpManageMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManagePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManageSaveRequest;

import java.util.List;

@Service
public class PumpManageServiceImpl implements PumpManageService {
    @Autowired
    private PumpManageMapper mapper;

    @Override
    public IPage<PumpManage> findAllConditional(PumpManagePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public PumpManage save(PumpManageSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(PumpManage entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<PumpManage> saveAll(List<PumpManageSaveRequest> entities) {
        return QueryUtil.saveOrUpdateBatchByRequest(entities, mapper::saveAll, mapper::updateAll);
    }

}
