<template>
  <div class="app-container">
    <div class="pipeline-device-auth-container">
      <!-- 左侧设备类型选择 -->
      <div class="device-type-container">
        <div class="device-type-title">设备类型</div>
        <div class="device-type-content">
          <el-radio-group v-model="selectedDeviceType" @change="handleDeviceTypeChange">
            <el-radio value="">全部设备</el-radio>
            <el-radio value="pressure">压力计</el-radio>
            <el-radio value="flow">流量计</el-radio>
            <el-radio value="quality">水质检测仪</el-radio>
          </el-radio-group>
        </div>
        
        <!-- 分组用户选择 -->
        <div class="user-group-section">
          <div class="user-group-title">用户分组</div>
          <el-tree
            ref="userGroupTree"
            :data="userGroupTreeData"
            :props="userGroupTreeProps"
            node-key="id"
            highlight-current
            default-expand-all
            show-checkbox
            @check="handleUserGroupCheck"
          ></el-tree>
        </div>
      </div>

      <!-- 右侧设备列表 -->
      <div class="device-list-container">
        <!-- 搜索表单 -->
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" class="search-form">
          <el-form-item label="设备名称：">
            <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="设备编号：">
            <el-input v-model="queryParams.deviceCode" placeholder="请输入设备编号" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 批量操作 -->
        <div class="batch-operation">
          <el-button 
            type="primary" 
            :disabled="selectedDevices.length === 0"
            @click="handleBatchUserBind"
          >
            批量绑定用户
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedDevices.length === 0"
            @click="handleBatchRemoveAuth"
          >
            批量移除权限
          </el-button>
        </div>

        <!-- 数据表格 -->
        <el-table 
          v-loading="loading" 
          :data="deviceList" 
          border 
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="expand">
            <template #default="props">
              <div v-loading="props.row.loadingUserAuth">
                <el-table :data="props.row.userAuthList || []" border class="sub-table">
                  <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
                  <el-table-column label="用户组" prop="userGroupName" align="center"></el-table-column>
                  <el-table-column label="权限类型" align="center">
                    <template #default="scope">
                      <div class="auth-type-tags">
                        <el-tag v-if="scope.row.authType === 1" type="danger">完全控制</el-tag>
                        <el-tag v-if="scope.row.authType === 2" type="info">只读访问</el-tag>
                        <el-tag v-if="scope.row.authType === 3" type="warning">数据下载</el-tag>
                        <el-tag v-if="scope.row.authType === 4" type="success">参数设置</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="120">
                    <template #default="scope">
                      <el-button type="danger" size="small" @click="handleDeleteUserAuth(props.row, scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="设备名称" prop="name" align="center"></el-table-column>
          <el-table-column label="设备编号" prop="code" align="center"></el-table-column>
          <el-table-column label="设备类型" align="center">
            <template #default="scope">
              <el-tag :type="getDeviceTypeTagType(scope.row.type)">
                {{ getDeviceTypeName(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="安装位置" prop="location" align="center"></el-table-column>
          <el-table-column label="绑定用户" align="center">
            <template #default="scope">
              <span v-if="scope.row.userAuthList && scope.row.userAuthList.length > 0">
                {{ scope.row.userAuthList.map((item: any) => item.userName).join(', ') }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="权限范围" align="center">
            <template #default="scope">
              <span v-if="scope.row.userAuthList && scope.row.userAuthList.length > 0">
                <el-tag v-if="hasAuthType(scope.row, 1)" type="danger" class="ml-5">完全控制</el-tag>
                <el-tag v-if="hasAuthType(scope.row, 2)" type="info" class="ml-5">只读访问</el-tag>
                <el-tag v-if="hasAuthType(scope.row, 3)" type="warning" class="ml-5">数据下载</el-tag>
                <el-tag v-if="hasAuthType(scope.row, 4)" type="success" class="ml-5">参数设置</el-tag>
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="240">
            <template #default="scope">
              <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleUserBind(scope.row)">用户绑定</el-button>
                <el-button type="success" size="small" @click="handleAuthManage(scope.row)">权限管理</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 用户绑定对话框 -->
    <el-dialog 
      :title="'用户绑定 - ' + (currentDevice?.name || '')" 
      v-model="userBindDialogVisible" 
      width="600px" 
      append-to-body
    >
      <el-form ref="userBindFormRef" :model="userBindForm" label-width="100px">
        <el-form-item label="选择用户" prop="userIds">
          <choose-user-by-role
            :users="selectedUsers"
            @checkUsers="handleUserSelect"
            multiple
          ></choose-user-by-role>
        </el-form-item>
        <el-form-item label="权限类型" prop="authType">
          <div class="auth-type-buttons">
            <el-button
              :type="userBindForm.authType === 1 ? 'danger' : 'default'"
              @click="handleAuthTypeChange(1, 'userBindForm')"
              size="small"
            >完全控制</el-button>
            <el-button
              :type="userBindForm.authType === 2 ? 'info' : 'default'"
              @click="handleAuthTypeChange(2, 'userBindForm')"
              size="small"
            >只读访问</el-button>
            <el-button
              :type="userBindForm.authType === 3 ? 'warning' : 'default'"
              @click="handleAuthTypeChange(3, 'userBindForm')"
              size="small"
            >数据下载</el-button>
            <el-button
              :type="userBindForm.authType === 4 ? 'success' : 'default'"
              @click="handleAuthTypeChange(4, 'userBindForm')"
              size="small"
            >参数设置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userBindDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUserBind">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog 
      :title="'权限管理 - ' + (currentDevice?.name || '')" 
      v-model="authManageDialogVisible" 
      width="800px" 
      append-to-body
    >
      <div v-if="currentDeviceUserAuthList.length === 0" class="empty-data">
        <p>暂无用户权限数据</p>
      </div>
      <el-table v-else :data="currentDeviceUserAuthList" border>
        <el-table-column label="用户名称" align="center">
          <template #default="scope">
            {{ scope.row.userName || '未命名用户' }}
            <div v-if="!scope.row.userName" style="color: #999; font-size: 12px;">
              用户ID: {{ scope.row.userId }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户组" prop="userGroupName" align="center"></el-table-column>
        <el-table-column label="权限类型" align="center">
          <template #default="scope">
            <div class="auth-type-buttons">
              <el-button
                :type="scope.row.authType === 1 ? 'danger' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 1)"
                size="small"
              >完全控制</el-button>
              <el-button
                :type="scope.row.authType === 2 ? 'info' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 2)"
                size="small"
              >只读访问</el-button>
              <el-button
                :type="scope.row.authType === 3 ? 'warning' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 3)"
                size="small"
              >数据下载</el-button>
              <el-button
                :type="scope.row.authType === 4 ? 'success' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 4)"
                size="small"
              >参数设置</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button type="danger" size="small" @click="handleRemoveUserAuth(scope.$index)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="auth-manage-footer">
        <el-button type="primary" @click="handleAddUserAuth">添加用户</el-button>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="authManageDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAuthManage">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加用户对话框 -->
    <el-dialog title="添加用户" v-model="addUserDialogVisible" width="500px" append-to-body>
      <el-form ref="addUserFormRef" :model="addUserForm" label-width="100px">
        <el-form-item label="选择用户" prop="userId">
          <choose-user-by-role
            :users="addSelectedUsers"
            @checkUsers="handleAddUserSelect"
            :multiple="false"
          ></choose-user-by-role>
        </el-form-item>
        <el-form-item label="权限类型" prop="authType">
          <div class="auth-type-buttons">
            <el-button
              :type="addUserForm.authType === 1 ? 'danger' : 'default'"
              @click="handleAuthTypeChange(1, 'addUserForm')"
              size="small"
            >完全控制</el-button>
            <el-button
              :type="addUserForm.authType === 2 ? 'info' : 'default'"
              @click="handleAuthTypeChange(2, 'addUserForm')"
              size="small"
            >只读访问</el-button>
            <el-button
              :type="addUserForm.authType === 3 ? 'warning' : 'default'"
              @click="handleAuthTypeChange(3, 'addUserForm')"
              size="small"
            >数据下载</el-button>
            <el-button
              :type="addUserForm.authType === 4 ? 'success' : 'default'"
              @click="handleAuthTypeChange(4, 'addUserForm')"
              size="small"
            >参数设置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addUserDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAddUser">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量绑定对话框 -->
    <el-dialog title="批量绑定用户" v-model="batchBindDialogVisible" width="600px" append-to-body>
      <el-form ref="batchBindFormRef" :model="batchBindForm" label-width="100px">
        <el-form-item label="选择用户" prop="userIds">
          <choose-user-by-role
            :users="batchSelectedUsers"
            @checkUsers="handleBatchUserSelect"
            multiple
          ></choose-user-by-role>
        </el-form-item>
        <el-form-item label="权限类型" prop="authType">
          <div class="auth-type-buttons">
            <el-button
              :type="batchBindForm.authType === 1 ? 'danger' : 'default'"
              @click="handleAuthTypeChange(1, 'batchBindForm')"
              size="small"
            >完全控制</el-button>
            <el-button
              :type="batchBindForm.authType === 2 ? 'info' : 'default'"
              @click="handleAuthTypeChange(2, 'batchBindForm')"
              size="small"
            >只读访问</el-button>
            <el-button
              :type="batchBindForm.authType === 3 ? 'warning' : 'default'"
              @click="handleAuthTypeChange(3, 'batchBindForm')"
              size="small"
            >数据下载</el-button>
            <el-button
              :type="batchBindForm.authType === 4 ? 'success' : 'default'"
              @click="handleAuthTypeChange(4, 'batchBindForm')"
              size="small"
            >参数设置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchBindDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchBind">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, toRefs, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';
import ChooseUserByRole from '@/components/chooseUserByRole/index.vue';
import {
  getPipelineDeviceUserAuth,
  batchSavePipelineDeviceUserAuth,
  getUserGroupTree,
  getPipelineDeviceList
} from '@/api/systemConfiguration/pipelineDeviceAuth';
import { getUserList } from '@/api/deviceAuth';

// 设备类型映射
const DEVICE_TYPE_MAP = {
  pressure: '压力计',
  flow: '流量计',
  quality: '水质检测仪'
};

// 获取设备列表
const getPipelineDeviceListAPI = async (params: any) => {
  try {
    return await getPipelineDeviceList(params);
  } catch (error) {
    // 如果新接口不可用，使用模拟数据
    console.warn('管网设备接口不可用，使用模拟数据');
    return {
      data: {
        data: {
          data: [
            {
              id: '1',
              name: '管网压力计-001',
              code: 'YLJ001',
              type: 'pressure',
              location: '水厂出水口',
              status: 1,
              userAuthList: []
            },
            {
              id: '2',
              name: '管网流量计-001',
              code: 'LLJ001',
              type: 'flow',
              location: '主干管道A段',
              status: 1,
              userAuthList: []
            },
            {
              id: '3',
              name: '水质检测仪-001',
              code: 'SZJC001',
              type: 'quality',
              location: '用户端监测点',
              status: 1,
              userAuthList: []
            }
          ],
          total: 3
        }
      }
    };
  }
};

// 获取用户分组树
const getUserGroupTreeAPI = async () => {
  try {
    return await getUserGroupTree();
  } catch (error) {
    // 如果新接口不可用，使用模拟数据
    console.warn('用户分组接口不可用，使用模拟数据');
    return {
      data: [
        {
          id: 'group1',
          name: '系统管理员组',
          children: []
        },
        {
          id: 'group2',
          name: '设备操作员组',
          children: []
        },
        {
          id: 'group3',
          name: '数据查看员组',
          children: []
        }
      ]
    };
  }
};

interface DeviceItem {
  id: string;
  name: string;
  code: string;
  type: string;
  location: string;
  status: number;
  userAuthList: any[];
  loadingUserAuth?: boolean;
  userAuthLoaded?: boolean;
}

interface UserAuthItem {
  userId: string;
  userName: string;
  userGroupName?: string;
  authType: number;
}

export default defineComponent({
  name: 'PipelineDeviceAuth',
  components: {
    ChooseUserByRole
  },
  setup() {
    const queryFormRef = ref<FormInstance>();
    const userBindFormRef = ref<FormInstance>();
    const addUserFormRef = ref<FormInstance>();
    const batchBindFormRef = ref<FormInstance>();
    const userGroupTree = ref();

    const state = reactive({
      // 选中的设备类型
      selectedDeviceType: '',
      // 用户分组树数据
      userGroupTreeData: [] as any[],
      // 用户分组树配置
      userGroupTreeProps: {
        label: 'name',
        children: 'children'
      },
      // 选中的用户分组
      selectedUserGroups: [] as string[],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        deviceName: '',
        deviceCode: '',
        deviceType: '',
        userGroupIds: [] as string[]
      },
      // 加载状态
      loading: false,
      // 设备列表
      deviceList: [] as DeviceItem[],
      // 总记录数
      total: 0,
      // 选中的设备
      selectedDevices: [] as DeviceItem[],
      // 当前设备
      currentDevice: null as DeviceItem | null,
      // 用户绑定对话框可见性
      userBindDialogVisible: false,
      // 用户绑定表单
      userBindForm: {
        userIds: [] as string[],
        authType: 0
      },
      // 用户选项
      userOptions: [] as any[],
      // 已选择的用户（用于用户绑定）
      selectedUsers: [] as any[],
      // 已选择的用户（用于添加用户）
      addSelectedUsers: [] as any[],
      // 批量绑定选择的用户
      batchSelectedUsers: [] as any[],
      // 权限管理对话框可见性
      authManageDialogVisible: false,
      // 当前设备用户权限列表
      currentDeviceUserAuthList: [] as UserAuthItem[],
      // 添加用户对话框可见性
      addUserDialogVisible: false,
      // 添加用户表单
      addUserForm: {
        userId: '',
        userName: '',
        authType: 0
      },
      // 批量绑定对话框可见性
      batchBindDialogVisible: false,
      // 批量绑定表单
      batchBindForm: {
        userIds: [] as string[],
        authType: 0
      }
    });

    // 获取设备类型名称
    const getDeviceTypeName = (type: string) => {
      return DEVICE_TYPE_MAP[type] || type;
    };

    // 获取设备类型标签类型
    const getDeviceTypeTagType = (type: string) => {
      const typeMap = {
        pressure: 'danger',
        flow: 'primary',
        quality: 'success'
      };
      return typeMap[type] || 'info';
    };

    // 获取用户分组树数据
    const getUserGroupTreeData = async () => {
      try {
        const res = await getUserGroupTreeAPI();
        state.userGroupTreeData = res.data || [];
      } catch (error) {
        console.error('获取用户分组树失败', error);
        ElMessage.error('获取用户分组树失败');
      }
    };

    // 获取设备列表
    const getList = async () => {
      state.loading = true;
      try {
        const params = { ...state.queryParams };
        
        // 添加设备类型过滤
        if (state.selectedDeviceType) {
          params.deviceType = state.selectedDeviceType;
        }
        
        // 添加用户分组过滤
        if (state.selectedUserGroups.length > 0) {
          params.userGroupIds = state.selectedUserGroups;
        }

        const res = await getPipelineDeviceListAPI(params);

        let deviceList: DeviceItem[] = [];
        if (res.data?.data?.data) {
          deviceList = res.data.data.data || [];
          state.total = res.data.data.total || 0;
        } else if (Array.isArray(res.data)) {
          deviceList = res.data;
          state.total = res.data.length || 0;
        }

        // 过滤设备名称和编号
        if (params.deviceName || params.deviceCode) {
          deviceList = deviceList.filter(device => {
            const nameMatch = !params.deviceName || 
              (device.name && device.name.includes(params.deviceName));
            const codeMatch = !params.deviceCode || 
              (device.code && device.code.includes(params.deviceCode));
            return nameMatch && codeMatch;
          });
          state.total = deviceList.length;
        }

        // 初始化设备列表
        state.deviceList = deviceList.map(device => ({
          ...device,
          userAuthList: [],
          loadingUserAuth: false,
          userAuthLoaded: false
        }));
      } catch (error) {
        console.error('获取设备列表失败', error);
        ElMessage.error('获取设备列表失败');
      } finally {
        state.loading = false;
      }
    };

    // 获取设备用户权限列表
    const getDeviceUserAuth = async (device: DeviceItem) => {
      if (device.userAuthLoaded) {
        return;
      }

      device.loadingUserAuth = true;
      try {
        const res = await getPipelineDeviceUserAuth(device.id);

        let dataArray: any[] = [];
        if (res.data) {
          if (Array.isArray(res.data)) {
            dataArray = res.data;
          } else if (res.data.data && Array.isArray(res.data.data)) {
            dataArray = res.data.data;
          }
        }

        const userAuthList = dataArray.map(item => {
          const authType = typeof item.authType === 'number' ? item.authType : parseInt(item.authType) || 0;
          
          let userName = item.userName || '';
          if (!userName && item.userId) {
            const user = state.userOptions.find(user => 
              String(user.id || '') === String(item.userId || '')
            );
            if (user) {
              userName = user.firstName || user.email || '未命名用户';
            }
          }

          return {
            userId: item.userId,
            userName: userName || '未命名用户',
            userGroupName: item.userGroupName || '未分组',
            authType
          };
        });

        device.userAuthList = userAuthList;
        device.userAuthLoaded = true;
      } catch (error) {
        console.error('获取设备用户权限列表失败', error);
        device.userAuthList = [];
      } finally {
        device.loadingUserAuth = false;
      }
    };

    // 获取用户列表
    const getUserOptions = async () => {
      try {
        const res = await getUserList({});
        state.userOptions = res.data || [];
      } catch (error) {
        console.error('获取用户列表失败', error);
        ElMessage.error('获取用户列表失败');
      }
    };

    // 设备类型变化事件
    const handleDeviceTypeChange = () => {
      state.queryParams.page = 1;
      getList();
    };

    // 用户分组选择事件
    const handleUserGroupCheck = (data: any, checked: any) => {
      state.selectedUserGroups = checked.checkedKeys;
      state.queryParams.page = 1;
      getList();
    };

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1;
      getList();
    };

    // 重置按钮点击事件
    const resetQuery = () => {
      queryFormRef.value?.resetFields();
      state.queryParams.deviceName = '';
      state.queryParams.deviceCode = '';
      handleQuery();
    };

    // 每页条数变化事件
    const handleSizeChange = (val: number) => {
      state.queryParams.size = val;
      getList();
    };

    // 当前页变化事件
    const handleCurrentChange = (val: number) => {
      state.queryParams.page = val;
      getList();
    };

    // 表格选择变化事件
    const handleSelectionChange = (selection: DeviceItem[]) => {
      state.selectedDevices = selection;
    };

    // 处理展开行事件
    const handleExpandChange = (row: DeviceItem, expandedRows: DeviceItem[]) => {
      if (expandedRows.length > 0 && expandedRows.includes(row)) {
        getDeviceUserAuth(row);
      }
    };

    // 用户绑定按钮点击事件
    const handleUserBind = (row: DeviceItem) => {
      state.currentDevice = row;
      state.userBindForm = {
        userIds: [],
        authType: 0
      };
      state.selectedUsers = [];
      state.userBindDialogVisible = true;
    };

    // 批量用户绑定按钮点击事件
    const handleBatchUserBind = () => {
      state.batchBindForm = {
        userIds: [],
        authType: 0
      };
      state.batchSelectedUsers = [];
      state.batchBindDialogVisible = true;
    };

    // 批量移除权限
    const handleBatchRemoveAuth = () => {
      ElMessageBox.confirm(
        `确认要移除选中的 ${state.selectedDevices.length} 个设备的所有用户权限吗？`,
        '批量移除权限',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          for (const device of state.selectedDevices) {
            await batchSavePipelineDeviceUserAuth({
              deviceId: device.id,
              deviceName: device.name,
              deviceSerial: device.code,
              userAuthList: []
            });
          }
          ElMessage.success('批量移除权限成功');
          getList();
        } catch (error) {
          console.error('批量移除权限失败', error);
          ElMessage.error('批量移除权限失败');
        }
      }).catch(() => {});
    };

    // 处理用户选择事件
    const handleUserSelect = (users: any[]) => {
      state.userBindForm.userIds = users.map(user => user.id);
      state.selectedUsers = users.map(user => ({
        id: user.id,
        name: user.firstName || user.email || '未命名用户'
      }));
    };

    // 处理批量用户选择事件
    const handleBatchUserSelect = (users: any[]) => {
      state.batchBindForm.userIds = users.map(user => user.id);
      state.batchSelectedUsers = users.map(user => ({
        id: user.id,
        name: user.firstName || user.email || '未命名用户'
      }));
    };

    // 处理添加用户选择事件
    const handleAddUserSelect = (users: any[]) => {
      if (users && users.length > 0) {
        const user = users[0];
        state.addUserForm.userId = user.id;
        state.addUserForm.userName = user.firstName || user.email || '未命名用户';
        state.addSelectedUsers = [{
          id: user.id,
          name: user.firstName || user.email || '未命名用户'
        }];
      }
    };

    // 处理权限类型变化
    const handleAuthTypeChange = (type: number, formName: string) => {
      if (state[formName].authType === type) {
        state[formName].authType = 0;
      } else {
        state[formName].authType = type;
      }
      // 强制更新表单
      state[formName] = { ...state[formName] };
    };

    // 处理行权限类型变化
    const handleRowAuthTypeChange = (row: UserAuthItem, type: number) => {
      if (row.authType === type) {
        row.authType = 0;
      } else {
        row.authType = type;
      }
      // 强制更新列表
      state.currentDeviceUserAuthList = [...state.currentDeviceUserAuthList];
    };

    // 提交用户绑定
    const submitUserBind = async () => {
      if (!state.userBindForm.userIds || state.userBindForm.userIds.length === 0) {
        ElMessage.warning('请选择用户');
        return;
      }

      if (!state.userBindForm.authType || state.userBindForm.authType === 0) {
        ElMessage.warning('请选择权限类型');
        return;
      }

      try {
        const userAuthList = state.userBindForm.userIds.map(userId => {
          const savedUser = state.selectedUsers.find(item => item.id === userId);
          const user = savedUser || state.userOptions.find(item => item.id === userId);
          return {
            userId,
            userName: user ? (user.name || user.firstName || user.email || '未命名用户') : '未命名用户',
            authType: state.userBindForm.authType
          };
        });

        // 合并现有的用户权限列表
        if (state.currentDevice && state.currentDevice.userAuthList) {
          for (const item of state.currentDevice.userAuthList) {
            if (!userAuthList.some(auth => auth.userId === item.userId)) {
              userAuthList.push(item);
            }
          }
        }

        await batchSavePipelineDeviceUserAuth({
          deviceId: state.currentDevice!.id,
          deviceName: state.currentDevice!.name,
          deviceSerial: state.currentDevice!.code,
          userAuthList
        });

        ElMessage.success('用户绑定成功');
        state.userBindDialogVisible = false;
        getList();
      } catch (error) {
        console.error('用户绑定失败', error);
        ElMessage.error('用户绑定失败');
      }
    };

    // 提交批量绑定
    const submitBatchBind = async () => {
      if (!state.batchBindForm.userIds || state.batchBindForm.userIds.length === 0) {
        ElMessage.warning('请选择用户');
        return;
      }

      if (!state.batchBindForm.authType || state.batchBindForm.authType === 0) {
        ElMessage.warning('请选择权限类型');
        return;
      }

      try {
        for (const device of state.selectedDevices) {
          const userAuthList = state.batchBindForm.userIds.map(userId => {
            const savedUser = state.batchSelectedUsers.find(item => item.id === userId);
            const user = savedUser || state.userOptions.find(item => item.id === userId);
            return {
              userId,
              userName: user ? (user.name || user.firstName || user.email || '未命名用户') : '未命名用户',
              authType: state.batchBindForm.authType
            };
          });

          // 合并现有的用户权限列表
          if (device.userAuthList) {
            for (const item of device.userAuthList) {
              if (!userAuthList.some(auth => auth.userId === item.userId)) {
                userAuthList.push(item);
              }
            }
          }

          await batchSavePipelineDeviceUserAuth({
            deviceId: device.id,
            deviceName: device.name,
            deviceSerial: device.code,
            userAuthList
          });
        }

        ElMessage.success('批量绑定用户成功');
        state.batchBindDialogVisible = false;
        getList();
      } catch (error) {
        console.error('批量绑定失败', error);
        ElMessage.error('批量绑定失败');
      }
    };

    // 权限管理按钮点击事件
    const handleAuthManage = async (row: DeviceItem) => {
      state.currentDevice = row;

      if (state.userOptions.length === 0) {
        await getUserOptions();
      }

      try {
        const res = await getPipelineDeviceUserAuth(row.id);

        let dataArray: any[] = [];
        if (res.data) {
          if (Array.isArray(res.data)) {
            dataArray = res.data;
          } else if (res.data.data && Array.isArray(res.data.data)) {
            dataArray = res.data.data;
          }
        }

        const userAuthList = dataArray.map(item => {
          const authType = typeof item.authType === 'number' ? item.authType : parseInt(item.authType) || 0;
          
          let userName = item.userName || '';
          if (!userName && item.userId) {
            const user = state.userOptions.find(user => 
              String(user.id || '') === String(item.userId || '')
            );
            if (user) {
              userName = user.firstName || user.email || '未命名用户';
            }
          }

          return {
            userId: item.userId,
            userName: userName || '未命名用户',
            userGroupName: item.userGroupName || '未分组',
            authType
          };
        });

        state.currentDeviceUserAuthList = userAuthList;
        row.userAuthList = [...userAuthList];
        row.userAuthLoaded = true;
      } catch (error) {
        console.error('获取设备用户权限列表失败', error);
        state.currentDeviceUserAuthList = [];
        ElMessage.error('获取设备用户权限列表失败');
      }

      state.authManageDialogVisible = true;
    };

    // 添加用户按钮点击事件
    const handleAddUserAuth = () => {
      state.addUserForm = {
        userId: '',
        userName: '',
        authType: 0
      };
      state.addSelectedUsers = [];
      state.addUserDialogVisible = true;
    };

    // 提交添加用户
    const submitAddUser = () => {
      if (!state.addUserForm.userId) {
        ElMessage.warning('请选择用户');
        return;
      }

      if (!state.addUserForm.authType || state.addUserForm.authType === 0) {
        ElMessage.warning('请选择权限类型');
        return;
      }

      const savedUser = state.addSelectedUsers.length > 0 ? state.addSelectedUsers[0] : null;
      const user = savedUser || state.userOptions.find(item => item.id === state.addUserForm.userId);

      state.currentDeviceUserAuthList.push({
        userId: state.addUserForm.userId,
        userName: user ? (user.name || user.firstName || user.email || state.addUserForm.userName || '未命名用户') : '未命名用户',
        userGroupName: '未分组',
        authType: state.addUserForm.authType
      });
      
      state.addUserDialogVisible = false;
    };

    // 移除用户权限
    const handleRemoveUserAuth = (index: number) => {
      state.currentDeviceUserAuthList.splice(index, 1);
    };

    // 提交权限管理
    const submitAuthManage = async () => {
      const invalidUsers = state.currentDeviceUserAuthList.filter(item => 
        !item.authType || item.authType === 0
      );
      if (invalidUsers.length > 0) {
        ElMessage.warning(`有${invalidUsers.length}个用户未选择权限类型，请为所有用户选择权限类型`);
        return;
      }

      try {
        await batchSavePipelineDeviceUserAuth({
          deviceId: state.currentDevice!.id,
          deviceName: state.currentDevice!.name,
          deviceSerial: state.currentDevice!.code,
          userAuthList: state.currentDeviceUserAuthList
        });

        ElMessage.success('权限管理成功');
        state.authManageDialogVisible = false;
        getList();
      } catch (error) {
        console.error('权限管理失败', error);
        ElMessage.error('权限管理失败');
      }
    };

    // 删除用户权限
    const handleDeleteUserAuth = (device: DeviceItem, userAuth: UserAuthItem) => {
      ElMessageBox.confirm('确认删除该用户权限吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const userAuthList = device.userAuthList.filter(item => item.userId !== userAuth.userId);

          await batchSavePipelineDeviceUserAuth({
            deviceId: device.id,
            deviceName: device.name,
            deviceSerial: device.code,
            userAuthList
          });

          ElMessage.success('删除成功');
          getList();
        } catch (error) {
          console.error('删除失败', error);
          ElMessage.error('删除失败');
        }
      }).catch(() => {});
    };

    // 判断是否有指定权限类型
    const hasAuthType = (device: DeviceItem, authType: number) => {
      if (!device.userAuthList) return false;
      return device.userAuthList.some(item => item.authType === authType);
    };

    onMounted(() => {
      getUserGroupTreeData();
      getUserOptions();
      getList();
    });

    return {
      queryFormRef,
      userBindFormRef,
      addUserFormRef,
      batchBindFormRef,
      userGroupTree,
      ...toRefs(state),
      getDeviceTypeName,
      getDeviceTypeTagType,
      handleDeviceTypeChange,
      handleUserGroupCheck,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      handleExpandChange,
      handleUserBind,
      handleBatchUserBind,
      handleBatchRemoveAuth,
      handleUserSelect,
      handleBatchUserSelect,
      handleAddUserSelect,
      handleAuthTypeChange,
      handleRowAuthTypeChange,
      submitUserBind,
      submitBatchBind,
      handleAuthManage,
      handleAddUserAuth,
      submitAddUser,
      handleRemoveUserAuth,
      submitAuthManage,
      handleDeleteUserAuth,
      hasAuthType
    };
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.pipeline-device-auth-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 120px);
}

.device-type-container {
  width: 280px;
  border-right: 1px solid #e6e6e6;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
}

.device-type-title {
  font-size: 16px;
  font-weight: bold;
  padding: 0 20px 15px;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 15px;
}

.device-type-content {
  padding: 0 20px;
}

.device-type-content .el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-type-content .el-radio {
  margin: 0;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.device-type-content .el-radio:hover {
  background-color: #f5f7fa;
}

.device-type-content .el-radio.is-checked {
  background-color: #e6f3ff;
  border-color: #409eff;
}

.user-group-section {
  flex: 1;
  margin-top: 20px;
}

.user-group-title {
  font-size: 14px;
  font-weight: bold;
  padding: 15px 20px 10px;
  border-top: 1px solid #e6e6e6;
  color: #606266;
}

.user-group-section .el-tree {
  padding: 0 20px;
  max-height: 300px;
  overflow-y: auto;
}

.device-list-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.search-form {
  margin-bottom: 20px;
}

.batch-operation {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.sub-table {
  margin: 10px 0;
  border-radius: 4px;
}

.ml-5 {
  margin-left: 5px;
}

.auth-manage-footer {
  margin-top: 20px;
  text-align: right;
}

.auth-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.auth-type-buttons .el-button {
  min-width: 80px;
}

.auth-type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.empty-data {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 40px 20px;
  text-align: center;
  color: #909399;
}

.empty-data p {
  margin: 10px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 设备类型标签样式 */
.el-tag.el-tag--danger {
  background-color: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.el-tag.el-tag--primary {
  background-color: #e6f3ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.el-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #c0e6c0;
  color: #67c23a;
}

/* 权限标签样式 */
.auth-type-tags .el-tag {
  font-size: 12px;
  height: 24px;
  line-height: 22px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table .el-table__header-wrapper {
  background-color: #fafafa;
}

.el-table .el-table__header th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pipeline-device-auth-container {
    flex-direction: column;
  }
  
  .device-type-container {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e6e6e6;
    flex-direction: row;
    justify-content: space-between;
    padding: 15px 20px;
  }
  
  .device-type-content .el-radio-group {
    flex-direction: row;
    gap: 15px;
  }
  
  .user-group-section {
    margin-top: 0;
    margin-left: 20px;
  }
  
  .user-group-title {
    border-top: none;
    padding: 0 0 10px 0;
  }
  
  .user-group-section .el-tree {
    padding: 0;
    max-height: 150px;
  }
}

@media (max-width: 768px) {
  .device-type-container {
    flex-direction: column;
  }
  
  .device-type-content .el-radio-group {
    flex-direction: column;
  }
  
  .user-group-section {
    margin-left: 0;
    margin-top: 15px;
  }
  
  .batch-operation {
    flex-direction: column;
  }
  
  .auth-type-buttons {
    flex-direction: column;
  }
  
  .auth-type-buttons .el-button {
    width: 100%;
  }
}
</style> 