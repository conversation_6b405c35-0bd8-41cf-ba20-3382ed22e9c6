package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.AssayRecord;

import java.util.List;

@Controller
@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface AssayRecordFeignClient {

    @GetMapping("api/assayRecord/listByTenant")
    List<AssayRecord> list() throws ThingsboardException;

}
