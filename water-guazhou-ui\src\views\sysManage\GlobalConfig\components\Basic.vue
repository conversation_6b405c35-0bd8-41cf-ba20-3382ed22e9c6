<template>
  <Form
    ref="refForm"
    :config="FormConfig"
  ></Form>
</template>
<script lang="ts" setup>
import { saveTenant } from '@/api/tenant'
import { useUserStore } from '@/store'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refForm = ref<IFormIns>()
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        { type: 'input', label: '网站名称', field: 'SITENAME' },
        { type: 'input', label: '后端接口地址', field: 'apiURL', readonly: true },
        { type: 'input', label: 'bim接口地址', field: 'bimUrl' },
        { type: 'input', label: 'rtsp视频流地址', field: 'rtspUrl' },
        { type: 'input', label: '营收（旧版本）接口地址', field: 'yinshouUrl' },
        { type: 'input', label: 'fineReport接口地址', field: 'fineReportURL' },
        { type: 'input', label: 'radarImg接口地址', field: 'radarImgUrl' }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            { perm: true, text: '保存', click: () => refForm.value?.Submit() },
            { perm: true, text: '重置', click: () => refForm.value?.resetForm() }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {
    ...(window.SITE_CONFIG || {}),
    ...(useUserStore().tenantInfo?.additionalInfo || {})
  },
  submit: (params: any) => {
    saveConfig(params)
  }
})
const saveConfig = (params: ISiteConfig) => {
  SLConfirm('确定保存？', '提示信息')
    .then(async () => {
      FormConfig.submitting = true
      try {
        console.log(params)
        // await saveTenant(params)
        await useUserStore().InitTenantInfo()
        resetForm()
      } catch (error) {
        SLMessage.error('保存失败')
        console.log(error)
      }
    })
    .catch(() => {
      //
    })
}
const resetForm = () => {
  FormConfig.defaultValue = {
    ...(window.SITE_CONFIG || {}),
    ...(useUserStore().tenantInfo?.additionalInfo || {})
  }
}
</script>
<style lang="scss" scoped></style>
