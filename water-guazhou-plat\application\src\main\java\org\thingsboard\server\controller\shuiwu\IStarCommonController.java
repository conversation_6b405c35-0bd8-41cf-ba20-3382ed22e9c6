package org.thingsboard.server.controller.shuiwu;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.common.CommonService;
import org.thingsboard.server.dao.model.sql.CommonTodoStatistic;
import org.thingsboard.server.dao.model.sql.SoStatistic;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/shuiwu/common")
public class IStarCommonController extends BaseController {
    @Autowired
    private CommonService commonService;

    @GetMapping("/commonTodoStatistic")
    public CommonTodoStatistic getCommonTodoStatistic() throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId());
        return commonService.getCommonTodoStatistic(userId);
    }

    @GetMapping("/so/statistic")
    public SoStatistic soStatistic() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return commonService.soStatistic(tenantId);
    }

}
