import{j as n,m as l}from"./index-r0dFAfgr.js";async function p(t,e="file"){try{const a=new FormData;a.append("file",t);const r=n().actionUrl;if(!r)throw new Error("未配置上传服务器地址");const i=e==="file"?r+"file/api/upload/file":r+"file/api/upload/image",o=await l({url:i,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"}});if(!o.data)throw new Error("文件上传失败：未获取到文件地址");return o.data}catch(a){throw console.error("文件上传失败:",a),a}}export{p as u};
