"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8732],{60437:(t,e,n)=>{n.d(e,{Gv:()=>M,HH:()=>u,JR:()=>g,TC:()=>c,Tn:()=>f,Ue:()=>s,al:()=>i,bZ:()=>y,cS:()=>m,dp:()=>a,fS:()=>I,is:()=>p,op:()=>h,pp:()=>l,t8:()=>d});var r=n(70586),o=n(6570);function s(t=x){return[t[0],t[1],t[2],t[3],t[4],t[5]]}function i(t,e,n,r,o,i,u=s()){return u[0]=t,u[1]=e,u[2]=n,u[3]=r,u[4]=o,u[5]=i,u}function u(t,e){const n=isFinite(t[2])||isFinite(t[5]);return new o.Z(n?{xmin:t[0],xmax:t[3],ymin:t[1],ymax:t[4],zmin:t[2],zmax:t[5],spatialReference:e}:{xmin:t[0],xmax:t[3],ymin:t[1],ymax:t[4],spatialReference:e})}function c(t,e){t[0]=Math.min(t[0],e[0]),t[1]=Math.min(t[1],e[1]),t[2]=Math.min(t[2],e[2]),t[3]=Math.max(t[3],e[3]),t[4]=Math.max(t[4],e[4]),t[5]=Math.max(t[5],e[5])}function l(t,e){t[0]=Math.min(t[0],e[0]),t[1]=Math.min(t[1],e[1]),t[2]=Math.min(t[2],e[2]),t[3]=Math.max(t[3],e[0]),t[4]=Math.max(t[4],e[1]),t[5]=Math.max(t[5],e[2])}function a(t,e=[0,0,0]){return e[0]=function(t){return t[0]>=t[3]?0:t[3]-t[0]}(t),e[1]=function(t){return t[1]>=t[4]?0:t[4]-t[1]}(t),e[2]=function(t){return t[2]>=t[5]?0:t[5]-t[2]}(t),e}function h(t,e,n=t){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n!==t&&(n[3]=t[3],n[4]=t[4],n[5]=t[5]),n}function f(t,e,n=t){return n[3]=e[0],n[4]=e[1],n[5]=e[2],n!==t&&(n[0]=t[0],n[1]=t[1],n[2]=t[2]),t}function d(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function m(t){return t?d(t,M):s(M)}function g(t,e){return t[0]=e[0],t[1]=e[1],t[2]=Number.NEGATIVE_INFINITY,t[3]=e[2],t[4]=e[3],t[5]=Number.POSITIVE_INFINITY,t}function y(t,e,n,r,o){return t[0]=e,t[1]=n,t[2]=Number.NEGATIVE_INFINITY,t[3]=r,t[4]=o,t[5]=Number.POSITIVE_INFINITY,t}function p(t){return 6===t.length}function I(t,e,n){if((0,r.Wi)(t)||(0,r.Wi)(e))return t===e;if(!p(t)||!p(e))return!1;if(n){for(let r=0;r<t.length;r++)if(!n(t[r],e[r]))return!1}else for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}n(24470);const M=[1/0,1/0,1/0,-1/0,-1/0,-1/0],x=[0,0,0,0,0,0];s()},24470:(t,e,n)=>{n.d(e,{Gv:()=>I,HH:()=>l,SO:()=>h,Ue:()=>s,al:()=>u,cS:()=>g,fS:()=>p,jE:()=>f,jn:()=>a,kK:()=>d,oJ:()=>c,r3:()=>m}),n(80442),n(22021);var r=n(70586),o=n(6570);function s(t=M){return[t[0],t[1],t[2],t[3]]}function i(t,e){return t!==e&&(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3]),t}function u(t,e,n,r,o=s()){return o[0]=t,o[1]=e,o[2]=n,o[3]=r,o}function c(t,e=s()){return e[0]=t.xmin,e[1]=t.ymin,e[2]=t.xmax,e[3]=t.ymax,e}function l(t,e){return new o.Z({xmin:t[0],ymin:t[1],xmax:t[2],ymax:t[3],spatialReference:e})}function a(t,e,n){if((0,r.Wi)(e))i(n,t);else if("length"in e)y(e)?(n[0]=Math.min(t[0],e[0]),n[1]=Math.min(t[1],e[1]),n[2]=Math.max(t[2],e[2]),n[3]=Math.max(t[3],e[3])):2!==e.length&&3!==e.length||(n[0]=Math.min(t[0],e[0]),n[1]=Math.min(t[1],e[1]),n[2]=Math.max(t[2],e[0]),n[3]=Math.max(t[3],e[1]));else switch(e.type){case"extent":n[0]=Math.min(t[0],e.xmin),n[1]=Math.min(t[1],e.ymin),n[2]=Math.max(t[2],e.xmax),n[3]=Math.max(t[3],e.ymax);break;case"point":n[0]=Math.min(t[0],e.x),n[1]=Math.min(t[1],e.y),n[2]=Math.max(t[2],e.x),n[3]=Math.max(t[3],e.y)}}function h(t){return function(t){return(0,r.Wi)(t)||t[0]>=t[2]?0:t[2]-t[0]}(t)*function(t){return t[1]>=t[3]?0:t[3]-t[1]}(t)}function f(t,e,n){return e>=t[0]&&n>=t[1]&&e<=t[2]&&n<=t[3]}function d(t,e){return Math.max(e[0],t[0])<=Math.min(e[2],t[2])&&Math.max(e[1],t[1])<=Math.min(e[3],t[3])}function m(t,e){return e[0]>=t[0]&&e[2]<=t[2]&&e[1]>=t[1]&&e[3]<=t[3]}function g(t){return t?i(t,I):s(I)}function y(t){return null!=t&&4===t.length}function p(t,e){return y(t)&&y(e)?t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]:t===e}const I=[1/0,1/0,-1/0,-1/0],M=[0,0,0,0]},70272:(t,e,n)=>{n.d(e,{S6:()=>s,nd:()=>i,u_:()=>o});var r=n(70586);class o{constructor(t=null,e={},n,r){this.geometry=t,this.attributes=e,this.centroid=n,this.objectId=r,this.displayId=0,this.geohashX=0,this.geohashY=0}weakClone(){const t=new o(this.geometry,this.attributes,this.centroid,this.objectId);return t.displayId=this.displayId,t.geohashX=this.geohashX,t.geohashY=this.geohashY,t}}function s(t){return!((0,r.Wi)(t.geometry)||!t.geometry.coords||!t.geometry.coords.length)}class i extends o{}},44876:(t,e,n)=>{n.d(e,{Z:()=>r});class r{constructor(){this.objectIdFieldName=null,this.globalIdFieldName=null,this.geohashFieldName=null,this.geometryProperties=null,this.geometryType=null,this.spatialReference=null,this.hasZ=!1,this.hasM=!1,this.features=[],this.fields=[],this.transform=null,this.exceededTransferLimit=!1,this.uniqueIdField=null,this.queryGeometryType=null,this.queryGeometry=null}weakClone(){const t=new r;return t.objectIdFieldName=this.objectIdFieldName,t.globalIdFieldName=this.globalIdFieldName,t.geohashFieldName=this.geohashFieldName,t.geometryProperties=this.geometryProperties,t.geometryType=this.geometryType,t.spatialReference=this.spatialReference,t.hasZ=this.hasZ,t.hasM=this.hasM,t.features=this.features,t.fields=this.fields,t.transform=this.transform,t.exceededTransferLimit=this.exceededTransferLimit,t.uniqueIdField=this.uniqueIdField,t.queryGeometry=this.queryGeometry,t.queryGeometryType=this.queryGeometryType,t}}},5428:(t,e,n)=>{n.d(e,{Z:()=>r});class r{constructor(t=[],e=[],n=!1){this.lengths=t??[],this.coords=e??[],this.hasIndeterminateRingOrder=n}static fromRect(t){const[e,n,o,s]=t,i=o-e,u=s-n;return new r([5],[e,n,i,0,0,u,-i,0,0,-u])}get isPoint(){return 0===this.lengths.length}get maxLength(){return Math.max(...this.lengths)}get size(){return this.lengths.reduce(((t,e)=>t+e))}forEachVertex(t){let e=0;this.lengths.length||t(this.coords[0],this.coords[1]);for(let n=0;n<this.lengths.length;n++){const r=this.lengths[n];for(let n=0;n<r;n++)t(this.coords[2*(n+e)],this.coords[2*(n+e)+1]);e+=r}}clone(t){return t?(t.set(this.coords),new r(this.lengths.slice(),t,this.hasIndeterminateRingOrder)):new r(this.lengths.slice(),this.coords.slice(),this.hasIndeterminateRingOrder)}}},98732:(t,e,n)=>{n.d(e,{$:()=>nt,$g:()=>rt,EI:()=>V,GH:()=>$,IN:()=>b,Iv:()=>_,J6:()=>q,Jd:()=>x,Nh:()=>Q,RZ:()=>K,Uy:()=>z,XA:()=>O,Yn:()=>U,cn:()=>J,dd:()=>j,di:()=>A,eG:()=>Y,fQ:()=>w,hY:()=>ot,h_:()=>X,lM:()=>B,lz:()=>it,oB:()=>ut,zj:()=>D});var r=n(20102),o=n(92604),s=n(70586),i=n(60437),u=n(24470),c=n(33955),l=n(70272),a=n(44876),h=n(5428);function f(t,e){return t?e?4:3:e?3:2}const d=o.Z.getLogger("esri.layers.graphics.featureConversionUtils"),m={esriGeometryPoint:0,esriGeometryPolyline:2,esriGeometryPolygon:3,esriGeometryMultipoint:0},g=(t,e,n,r,o,s)=>{t[n]=o,t[n+1]=s},y=(t,e,n,r,o,s)=>{t[n]=o,t[n+1]=s,t[n+2]=e[r+2]},p=(t,e,n,r,o,s)=>{t[n]=o,t[n+1]=s,t[n+2]=e[r+3]},I=(t,e,n,r,o,s)=>{t[n]=o,t[n+1]=s,t[n+2]=e[r+2],t[n+3]=e[r+3]};function M(t,e,n,r){if(t){if(n)return e&&r?I:y;if(e&&r)return p}else if(e&&r)return y;return g}function x({scale:t,translate:e},n){return Math.round((n-e[0])/t[0])}function b({scale:t,translate:e},n){return Math.round((e[1]-n)/t[1])}function N({scale:t,translate:e},n,r){return n*t[r]+e[r]}function w(t,e,n){return t?e?n?v(t):G(t):n?P(t):Z(t):null}function Z(t){const e=t.coords;return{x:e[0],y:e[1]}}function T(t,e){return t.coords[0]=e.x,t.coords[1]=e.y,t}function G(t){const e=t.coords;return{x:e[0],y:e[1],z:e[2]}}function F(t,e){return t.coords[0]=e.x,t.coords[1]=e.y,t.coords[2]=e.z,t}function P(t){const e=t.coords;return{x:e[0],y:e[1],m:e[2]}}function C(t,e){return t.coords[0]=e.x,t.coords[1]=e.y,t.coords[2]=e.m,t}function v(t){const e=t.coords;return{x:e[0],y:e[1],z:e[2],m:e[3]}}function R(t,e){return t.coords[0]=e.x,t.coords[1]=e.y,t.coords[2]=e.z,t.coords[3]=e.m,t}function k(t,e){return t&&e?R:t?F:e?C:T}function j(t,e,n=k(null!=e.z,null!=e.m)){return n(t,e)}function _(t,e,n){if((0,s.Wi)(t))return null;const r=f(e,n),o=[];for(let e=0;e<t.coords.length;e+=r){const n=[];for(let o=0;o<r;o++)n.push(t.coords[e+o]);o.push(n)}return e?n?{points:o,hasZ:e,hasM:n}:{points:o,hasZ:e}:n?{points:o,hasM:n}:{points:o}}function E(t,e,n=f(e.hasZ,e.hasM)){t.lengths[0]=e.points.length;const r=t.coords;let o=0;for(const t of e.points)for(let e=0;e<n;e++)r[o++]=t[e];return t}function q(t,e,n){if(!t)return null;const r=f(e,n),{coords:o,lengths:s}=t,i=[];let u=0;for(const t of s){const e=[];for(let n=0;n<t;n++){const t=[];for(let e=0;e<r;e++)t.push(o[u++]);e.push(t)}i.push(e)}return e?n?{paths:i,hasZ:e,hasM:n}:{paths:i,hasZ:e}:n?{paths:i,hasM:n}:{paths:i}}function W(t,e,n=f(e.hasZ,e.hasM)){const{lengths:r,coords:o}=t;let s=0;for(const t of e.paths){for(const e of t)for(let t=0;t<n;t++)o[s++]=e[t];r.push(t.length)}return t}function Y(t,e,n){if(!t)return null;const r=f(e,n),{coords:o,lengths:s}=t,i=[];let u=0;for(const t of s){const e=[];for(let n=0;n<t;n++){const t=[];for(let e=0;e<r;e++)t.push(o[u++]);e.push(t)}i.push(e)}return e?n?{rings:i,hasZ:e,hasM:n}:{rings:i,hasZ:e}:n?{rings:i,hasM:n}:{rings:i}}function z(t,e,n=e.hasZ,r=e.hasM){return function(t,e,n,r){const o=f(n,r),{lengths:s,coords:i}=t;let u=0;ct(t);for(const t of e){for(const e of t)for(let t=0;t<o;t++)i[u++]=e[t];s.push(t.length)}}(t,e.rings,n,r),t}const S=[],L=[];function O(t,e,n,r,o){S[0]=t;const[s]=U(L,S,e,n,r,o);return lt(S),lt(L),s}function U(t,e,n,o,i,u){if(lt(t),!n){for(const n of e){const e=u?n.attributes[u]:void 0;t.push(new l.u_(null,n.attributes,null,e))}return t}switch(n){case"esriGeometryPoint":return function(t,e,n,r,o){const i=k(n,r);for(const{geometry:n,attributes:r}of e){const e=(0,s.pC)(n)?i(new h.Z,n):null;t.push(new l.u_(e,r,null,o?r[o]:void 0))}return t}(t,e,o,i,u);case"esriGeometryMultipoint":return function(t,e,n,r,o){const i=f(n,r);for(const{geometry:n,attributes:r}of e){const e=(0,s.pC)(n)?E(new h.Z,n,i):null;t.push(new l.u_(e,r,null,o?r[o]:void 0))}return t}(t,e,o,i,u);case"esriGeometryPolyline":return function(t,e,n,r,o){const i=f(n,r);for(const{geometry:n,attributes:r}of e){const e=(0,s.pC)(n)?W(new h.Z,n,i):null;t.push(new l.u_(e,r,null,o?r[o]:void 0))}return t}(t,e,o,i,u);case"esriGeometryPolygon":return function(t,e,n,r,o){for(const{geometry:i,centroid:u,attributes:c}of e){const e=(0,s.pC)(i)?z(new h.Z,i,n,r):null,a=o?c[o]:void 0;(0,s.pC)(u)?t.push(new l.u_(e,c,T(new h.Z,u),a)):t.push(new l.u_(e,c,null,a))}return t}(t,e,o,i,u);default:d.error("convertToFeatureSet:unknown-geometry",new r.Z(`Unable to parse unknown geometry type '${n}'`)),lt(t)}return t}function V(t,e,n,r){L[0]=t,H(S,L,e,n,r);const o=S[0];return lt(S),lt(L),o}function $(t,e,n){if((0,s.Wi)(t))return null;const o=new h.Z;return"hasZ"in t&&null==e&&(e=t.hasZ),"hasM"in t&&null==n&&(n=t.hasM),(0,c.wp)(t)?k(null!=e?e:null!=t.z,null!=n?n:null!=t.m)(o,t):(0,c.oU)(t)?z(o,t,e,n):(0,c.l9)(t)?W(o,t,f(e,n)):(0,c.aW)(t)?E(o,t,f(e,n)):void d.error("convertFromGeometry:unknown-geometry",new r.Z(`Unable to parse unknown geometry type '${t}'`))}function A(t,e,n,o){const i=t&&("coords"in t?t:t.geometry);if((0,s.Wi)(i))return null;switch(e){case"esriGeometryPoint":{let t=Z;return n&&o?t=v:n?t=G:o&&(t=P),t(i)}case"esriGeometryMultipoint":return _(i,n,o);case"esriGeometryPolyline":return q(i,n,o);case"esriGeometryPolygon":return Y(i,n,o);default:return d.error("convertToGeometry:unknown-geometry",new r.Z(`Unable to parse unknown geometry type '${e}'`)),null}}function H(t,e,n,o,i){if(lt(t),(0,s.Wi)(n))return function(t,e){for(const n of e)t.push({attributes:n.attributes});return t}(t,e);switch(n){case"esriGeometryPoint":return function(t,e,n,r){let o=Z;n&&r?o=v:n?o=G:r&&(o=P);for(const n of e){const{geometry:e,attributes:r}=n,i=(0,s.pC)(e)?o(e):null;t.push({attributes:r,geometry:i})}return t}(t,e,o,i);case"esriGeometryMultipoint":return function(t,e,n,r){for(const{geometry:o,attributes:i}of e)t.push({attributes:i,geometry:(0,s.pC)(o)?_(o,n,r):null});return t}(t,e,o,i);case"esriGeometryPolyline":return function(t,e,n,r){for(const{geometry:o,attributes:i}of e)t.push({attributes:i,geometry:(0,s.pC)(o)?q(o,n,r):null});return t}(t,e,o,i);case"esriGeometryPolygon":return function(t,e,n,r){for(const{geometry:o,attributes:i,centroid:u}of e){const e=(0,s.pC)(o)?Y(o,n,r):null;if((0,s.pC)(u)){const n=Z(u);t.push({attributes:i,centroid:n,geometry:e})}else t.push({attributes:i,geometry:e})}return t}(t,e,o,i);default:d.error("convertToFeatureSet:unknown-geometry",new r.Z(`Unable to parse unknown geometry type '${n}'`))}return t}function J(t){const{objectIdFieldName:e,spatialReference:n,transform:r,fields:o,hasM:s,hasZ:i,features:u,geometryType:c,exceededTransferLimit:l,uniqueIdField:a,queryGeometry:h,queryGeometryType:f}=t,d={features:H([],u,c,i,s),fields:o,geometryType:c,objectIdFieldName:e,spatialReference:n,uniqueIdField:a,queryGeometry:A(h,f,!1,!1)};return r&&(d.transform=r),l&&(d.exceededTransferLimit=l),s&&(d.hasM=s),i&&(d.hasZ=i),d}function X(t,e){const n=new a.Z,{hasM:o,hasZ:s,features:i,objectIdFieldName:u,spatialReference:c,geometryType:l,exceededTransferLimit:h,transform:f,fields:m}=t;return m&&(n.fields=m),n.geometryType=l??null,n.objectIdFieldName=u??e??null,n.spatialReference=c??null,n.objectIdFieldName?(i&&U(n.features,i,l,s,o,n.objectIdFieldName),h&&(n.exceededTransferLimit=h),o&&(n.hasM=o),s&&(n.hasZ=s),f&&(n.transform=f),n):(d.error(new r.Z("optimized-features:invalid-objectIdFieldName","objectIdFieldName is missing")),n)}function B(t){const{transform:e,features:n,hasM:r,hasZ:o}=t;if(!e)return t;for(const t of n)(0,s.pC)(t.geometry)&&rt(t.geometry,t.geometry,r,o,e),(0,s.pC)(t.centroid)&&rt(t.centroid,t.centroid,r,o,e);return t.transform=null,t}function K(t,e){const{geometryType:n,features:r,hasM:o,hasZ:s}=e;if(!t)return e;for(let e=0;e<r.length;e++){const i=r[e],u=i.weakClone();u.geometry=new h.Z,Q(u.geometry,i.geometry,o,s,n,t),i.centroid&&(u.centroid=new h.Z,Q(u.centroid,i.centroid,o,s,"esriGeometryPoint",t)),r[e]=u}return e.transform=t,e}function Q(t,e,n,r,o,i,u=n,c=r){if(ct(t),(0,s.Wi)(e)||!e.coords.length)return null;const l=m[o],{coords:a,lengths:h}=e,d=f(n,r),g=f(n&&u,r&&c),y=M(n,r,u,c);if(!h.length)return y(t.coords,a,0,0,x(i,a[0]),b(i,a[1])),ct(t,d,0),t;let p,I,N,w,Z=0,T=0,G=T;for(const e of h){if(e<l)continue;let n=0;T=G,N=p=x(i,a[Z]),w=I=b(i,a[Z+1]),y(t.coords,a,T,Z,N,w),n++,Z+=d,T+=g;for(let r=1;r<e;r++,Z+=d)N=x(i,a[Z]),w=b(i,a[Z+1]),N===p&&w===I||(y(t.coords,a,T,Z,N-p,w-I),T+=g,n++,p=N,I=w);n>=l&&(t.lengths.push(n),G=T)}return lt(t.coords,G),t.coords.length?t:null}function D(t,e,n,r,o,s,i=n,u=r){if(ct(t),!e||!e.coords.length)return null;const c=m[o],{coords:l,lengths:a}=e,h=f(n,r),d=f(n&&i,r&&u),g=M(n,r,i,u);if(!a.length)return g(t.coords,l,0,0,l[0],l[1]),ct(t,h,0),t;let y=0;const p=s*s;for(const e of a){if(e<c){y+=e*h;continue}const n=t.coords.length/d,r=y,o=y+(e-1)*h;g(t.coords,l,t.coords.length,r,l[r],l[r+1]),et(t.coords,l,h,p,g,r,o),g(t.coords,l,t.coords.length,o,l[o],l[o+1]);const s=t.coords.length/d-n;s>=c?t.lengths.push(s):lt(t.coords,n*d),y+=e*h}return t.coords.length?t:null}function tt(t,e,n,r){const o=t[e],s=t[e+1],i=t[n],u=t[n+1],c=t[r],l=t[r+1];let a=i,h=u,f=c-a,d=l-h;if(0!==f||0!==d){const t=((o-a)*f+(s-h)*d)/(f*f+d*d);t>1?(a=c,h=l):t>0&&(a+=f*t,h+=d*t)}return f=o-a,d=s-h,f*f+d*d}function et(t,e,n,r,o,s,i){let u,c=r,l=0;for(let t=s+n;t<i;t+=n)u=tt(e,t,s,i),u>c&&(l=t,c=u);c>r&&(l-s>n&&et(t,e,n,r,o,s,l),o(t,e,t.length,l,e[l],e[l+1]),i-l>n&&et(t,e,n,r,o,l,i))}function nt(t,e,n,r){if((0,s.Wi)(e)||!e.coords||!e.coords.length)return null;const o=f(n,r);let c=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY,h=Number.NEGATIVE_INFINITY;if(e&&e.coords){const t=e.coords;for(let e=0;e<t.length;e+=o){const n=t[e],r=t[e+1];c=Math.min(c,n),a=Math.max(a,n),l=Math.min(l,r),h=Math.max(h,r)}}return(0,i.is)(t)?(0,i.bZ)(t,c,l,a,h):(0,u.al)(c,l,a,h,t),t}function rt(t,e,n,r,o){const{coords:i,lengths:u}=e,c=f(n,r);if(!i.length)return t!==e&&ct(t),t;(0,s.O3)(o);const{originPosition:l,scale:a,translate:h}=o,d=at;d.originPosition=l;const m=d.scale;m[0]=a[0]??1,m[1]=-(a[1]??1),m[2]=a[2]??1,m[3]=a[3]??1;const g=d.translate;if(g[0]=h[0]??0,g[1]=h[1]??0,g[2]=h[2]??0,g[3]=h[3]??0,!u.length){for(let e=0;e<c;++e)t.coords[e]=N(d,i[e],e);return t!==e&&ct(t,c,0),t}let y=0;for(let e=0;e<u.length;e++){const n=u[e];t.lengths[e]=n;for(let e=0;e<c;++e)t.coords[y+e]=N(d,i[y+e],e);let r=t.coords[y],o=t.coords[y+1];y+=c;for(let e=1;e<n;e++,y+=c){r+=i[y]*m[0],o+=i[y+1]*m[1],t.coords[y]=r,t.coords[y+1]=o;for(let e=2;e<c;++e)t.coords[y+e]=N(d,i[y+e],e)}}return t!==e&&ct(t,i.length,u.length),t}function ot(t,e,n,r,o,s){if(ct(t),t.lengths.push(...e.lengths),n===o&&r===s)for(let n=0;n<e.coords.length;n++)t.coords.push(e.coords[n]);else{const i=f(n,r),u=M(n,r,o,s),c=e.coords;for(let e=0;e<c.length;e+=i)u(t.coords,c,t.coords.length,e,c[e],c[e+1])}return t}function st(t,e,n,r){let o=0,s=t[r*e],i=t[r*(e+1)];for(let u=1;u<n;u++){const n=s+t[r*(e+u)],c=i+t[r*(e+u)+1],l=(n-s)*(c+i);s=n,i=c,o+=l}return.5*o}function it(t,e){const{coords:n,lengths:r}=t;let o=0,s=0;for(let t=0;t<r.length;t++){const i=r[t];s+=st(n,o,i,e),o+=i}return Math.abs(s)}function ut(t,e){if((0,s.Wi)(t))return null;const n=t.clone(),r=t.coords,o=t.lengths;let i=0;for(let t=0;t<o.length;t++){const s=o[t];let u=r[e*i],c=r[e*i+1];for(let t=1;t<s;t++){const o=u+r[e*(i+t)],s=c+r[e*(i+t)+1];n.coords[e*(i+t)]=o,n.coords[e*(i+t)+1]=s,u=o,c=s}i+=s}return n}function ct(t,e=0,n=0){lt(t.lengths,n),lt(t.coords,e)}function lt(t,e=0){t.length!==e&&(t.length=e)}const at={originPosition:"lowerLeft",scale:[1,1,1,1],translate:[0,0,0,0]}}}]);