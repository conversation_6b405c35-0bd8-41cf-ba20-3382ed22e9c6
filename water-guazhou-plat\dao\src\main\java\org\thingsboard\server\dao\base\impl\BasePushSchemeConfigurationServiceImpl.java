package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBasePushSchemeConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration;
import org.thingsboard.server.dao.sql.base.BasePushSchemeConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BasePushSchemeConfigurationPageRequest;

/**
 * 平台管理-推送方案配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class BasePushSchemeConfigurationServiceImpl implements IBasePushSchemeConfigurationService {

    @Autowired
    private BasePushSchemeConfigurationMapper basePushSchemeConfigurationMapper;

    /**
     * 查询平台管理-推送方案配置
     *
     * @param id 平台管理-推送方案配置主键
     * @return 平台管理-推送方案配置
     */
    @Override
    public BasePushSchemeConfiguration selectBasePushSchemeConfigurationById(String id) {
        return basePushSchemeConfigurationMapper.selectBasePushSchemeConfigurationById(id);
    }

    /**
     * 查询平台管理-推送方案配置列表
     *
     * @param basePushSchemeConfiguration 平台管理-推送方案配置
     * @return 平台管理-推送方案配置
     */
    @Override
    public IPage<BasePushSchemeConfiguration> selectBasePushSchemeConfigurationList(BasePushSchemeConfigurationPageRequest basePushSchemeConfiguration) {
        return basePushSchemeConfigurationMapper.selectBasePushSchemeConfigurationList(basePushSchemeConfiguration);
    }

    /**
     * 新增平台管理-推送方案配置
     *
     * @param basePushSchemeConfiguration 平台管理-推送方案配置
     * @return 结果
     */
    @Override
    public int insertBasePushSchemeConfiguration(BasePushSchemeConfiguration basePushSchemeConfiguration) {
        basePushSchemeConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return basePushSchemeConfigurationMapper.insertBasePushSchemeConfiguration(basePushSchemeConfiguration);
    }

    /**
     * 修改平台管理-推送方案配置
     *
     * @param basePushSchemeConfiguration 平台管理-推送方案配置
     * @return 结果
     */
    @Override
    public int updateBasePushSchemeConfiguration(BasePushSchemeConfiguration basePushSchemeConfiguration) {
        return basePushSchemeConfigurationMapper.updateBasePushSchemeConfiguration(basePushSchemeConfiguration);
    }

    /**
     * 批量删除平台管理-推送方案配置
     *
     * @param ids 需要删除的平台管理-推送方案配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePushSchemeConfigurationByIds(List<String> ids) {
        return basePushSchemeConfigurationMapper.deleteBasePushSchemeConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-推送方案配置信息
     *
     * @param id 平台管理-推送方案配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePushSchemeConfigurationById(String id) {
        return basePushSchemeConfigurationMapper.deleteBasePushSchemeConfigurationById(id);
    }
}
