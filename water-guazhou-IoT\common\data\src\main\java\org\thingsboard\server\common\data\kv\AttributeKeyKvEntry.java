/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.kv;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
public class AttributeKeyKvEntry implements AttributeKvEntry {

    private final AttributeBaseKey key;
    private final String value;

    public AttributeKeyKvEntry(AttributeBaseKey key, String value) {
        this.key = key;
        this.value = value;
    }

    public AttributeBaseKey getAttributeBaseKey(){
        return key;
    }



    @Override
    public String getKey() {
        return key.getAttributeKey();
    }



    @Override
    public DataType getDataType() {
        return DataType.STRING;
    }

    @Override
    public Optional<String> getStrValue() {
        return Optional.ofNullable(value);
    }

    @Override
    public Optional<Long> getLongValue() {
        return Optional.empty();
    }

    @Override
    public Optional<Boolean> getBooleanValue() {
        return Optional.empty();
    }

    @Override
    public Optional<Double> getDoubleValue() {
        return Optional.empty();
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DoubleDataEntry)) return false;
        if (!super.equals(o)) return false;
        AttributeKeyKvEntry that = (AttributeKeyKvEntry) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), value);
    }

    @Override
    public String toString() {
        return "DoubleDataEntry{" +
                "value=" + value +
                "} " + super.toString();
    }

    @Override
    public String getValueAsString() {
        return value;
    }

    @Override
    public long getLastUpdateTs() {
        return System.currentTimeMillis();
    }
}
