<!-- 应急车辆 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { getEmergencyUserTree, getMergencyVehicleList, getEmergencyUserList, postMergencyVehicle, deleteMergencyVehicle } from '@/api/productionScheduling/emergencyDispatch'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refForm = ref<IDialogFormIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '车辆号码', field: 'numberPlate', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '车牌号', prop: 'numberPlate' },
    { label: '车辆品牌', prop: 'carBrand' },
    { label: '使用年限', prop: 'usePeriod' },
    { label: '所属组织', prop: 'carUserOrganizationName' },
    { label: '所属部门', prop: 'carUserDepartmentName' },
    { label: '车辆负责人', prop: 'carUserName' },
    { label: '状态', prop: 'status' },
    // { label: '设备编号', prop: 'treePath' },
    { label: 'SIM卡号', prop: 'simNum' },
    { label: '服务器时间', prop: 'serverTime' },
    { label: '定位器时间', prop: 'gprsTime' },
    { label: '当前速度', prop: 'nowSpeed', unit: '(km/h)', minWidth: '120' },
    { label: '方向', prop: 'direction' },
    { label: '当日里程', prop: 'mileageDay', unit: '(km)' },
    { label: '总里程', prop: 'mileageTotal', unit: '(km)' }
  ],
  operationWidth: '120px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '100px',
  dialogWidth: '500px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true
    let text = '新增成功'
    if (params.id) text = '修改成功'
    postMergencyVehicle(params).then(() => {
      addOrUpdateConfig.submitting = false
      refForm.value?.closeDialog()
      ElMessage.success(text)
      refreshData()
    }).catch(error => {
      addOrUpdateConfig.submitting = false
      ElMessage.warning(error)
    })
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '车牌号',
          field: 'numberPlate',
          rules: [{ required: true, message: '请输入车牌号' }]
        },
        {
          type: 'input',
          label: '车辆品牌',
          field: 'carBrand',
          rules: [{ required: true, message: '请输入车辆品牌' }]
        }, {
          type: 'number',
          label: '使用年限',
          field: 'usePeriod'
        }, {
          type: 'select-tree',
          label: '负责人部门',
          field: 'carUserDepartmentId',
          checkStrictly: true,
          options: computed(() => data.department) as any,
          onChange: val => {
            data.getuserList(val)
          }
        },
        {
          type: 'select',
          label: '车辆负责人',
          field: 'carUserId',
          options: computed(() => data.userList) as any
        }, {
          type: 'input-number',
          label: 'SIM卡号',
          field: 'simNum'
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  addOrUpdateConfig.defaultValue = {}
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { category: row.parentId, ...(row) || {} }
  data.getuserList(row.carUserDepartmentId)
  refForm.value?.openDialog()
}

const handleDelete = (row?: any) => {
  SLConfirm('确定删除该应急车辆', '删除提示').then(() => {
    deleteMergencyVehicle(row.id).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    }).catch(error => {
      ElMessage.error(error)
    })
  })
}

const data = reactive({
  department: [] as any[],
  userList: [] as any[],
  getdepartment: () => {
    getEmergencyUserTree(2).then(res => {
      data.department = traverse(res.data.data || [])
    })
  },
  getuserList: id => {
    const params = {
      size: -1,
      page: 1,
      deptId: id,
      ...(refSearch.value?.queryParams || {})
    }
    getEmergencyUserList(params).then(res => {
      data.userList = traverse(res.data.data.data || [])
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  getMergencyVehicleList(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  }).catch(error => {
    ElMessage.warning('请求遇到错误')
  })
}

onMounted(async () => {
  refreshData()
  data.getdepartment()
})

</script>
