<!-- 统一工单-工单中心-工单查询 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" class="card-table" :config="TableConfig" />
    <SLDrawer ref="refdetail" :config="detailConfig">
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue';
// import router from '@/router'
import OrderStepTagsVue from './components/OrderStepTags.vue';
import detail from './components/detail.vue';
import {
  getEmergencyLevelOpetions,
  getFromOptions,
  // getOrderList,
  getOrderTypeOptions,
  initOrderTableColumns,
  WorkOrderStatus
} from './config';
import { GetWorkOrderPage } from '@/api/workorder';
import { useUserStore } from '@/store';
import { formatterDate } from '@/utils/GlobalHelper';
import { removeSlash } from '@/utils/removeIdSlash';

const refSearch = ref<ICardSearchIns>();
const refTable = ref<ICardTableIns>();
const refdetail = ref<ISLDrawerIns>();

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
});
const selectedId = ref<string>('');

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'stepProcessUserId',
      label: '类别',
      labelWidth: 40,
      type: 'radio-button',
      options: [
        { label: '全部', value: '' },
        { label: '由我处理', value: 'stepProcessUserId' },
        { label: '由我创建', value: 'organizerId' }
      ],
      onChange: () => refreshData()
    },
    {
      field: 'title',
      label: '标题',
      type: 'input',
      onChange: () => refreshData()
    },
    {
      xl: 16,
      itemContainerStyle: {
        width: '100%'
      },
      field: 'status',
      label: '工单状态',
      type: 'radio-button',
      options: WorkOrderStatus(true)
    },
    { field: 'date', label: '发起时间', type: 'daterange' },
    {
      field: 'source',
      label: '来源',
      type: 'select',
      options: getFromOptions()
    },
    {
      field: 'level',
      label: '紧急程度',
      type: 'select',
      options: getEmergencyLevelOpetions()
    },
    {
      field: 'type',
      label: '类型',
      type: 'select-tree',
      options: getOrderTypeOptions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          click: () => exportTable()
        }
      ]
    }
  ],
  handleSearch: () => refreshData(),
  defaultParams: {
    stepProcessUserId: '',
    status: ''
    // date: [
    //   moment().subtract(1, 'M').format(formatterDate),
    //   moment().format(formatterDate)
    // ]
  }
});
const TableConfig = reactive<ICardTable>({
  expandable: true,
  expandComponent: shallowRef(OrderStepTagsVue),
  columns: initOrderTableColumns(),
  defaultExpandAll: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  operations: [
    {
      perm: true,
      text: '详情',
      isTextBtn: true,
      click: (row) => handleDetail(row)
    }
  ]
});
const handleDetail = (row: any) => {
  selectedId.value = row.id || '';
  detailConfig.title = row.serialNo;
  refdetail.value?.openDrawer();
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
};
const refreshData = async () => {
  TableConfig.loading = true;
  try {
    const query = refSearch.value?.queryParams || {};
    // const [fromTime, toTime] = query.date?.length === 2
    //   ? [
    //     moment(query.date[0], formatterDate).valueOf(),
    //     moment(query.date[1], formatterDate).endOf('D').valueOf()
    //   ]
    //   : [
    //     moment().subtract(1, 'M').startOf('D').valueOf(),
    //     moment().endOf('D').valueOf()
    //   ]
    const params: any = {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime: query.date?.[0]
        ? moment(query.date[0], formatterDate).valueOf()
        : undefined,
      toTime: query.date?.[1]
        ? moment(query.date[1], formatterDate).endOf('D').valueOf()
        : undefined
    };
    if (query.stepProcessUserId === 'stepProcessUserId') {
      params.stepProcessUserId = removeSlash(useUserStore().user?.id?.id || '');
      params.organizerId = '';
    } else if (query.stepProcessUserId === 'organizerId') {
      params.organizerId = removeSlash(useUserStore().user?.id?.id || '');
      params.stepProcessUserId = '';
    } else {
      params.organizerId = '';
      params.stepProcessUserId = '';
    }
    delete params.date;
    const res = await GetWorkOrderPage(params);
    const data = res.data?.data;
    TableConfig.dataList = data.data;
    TableConfig.pagination.total = data.total;
  } catch (error) {
    //
  }
  TableConfig.loading = false;
};
const resetForm = () => {
  refSearch.value?.resetForm();
  refreshData();
};
const exportTable = () => {
  refTable.value?.exportTable();
};
onMounted(() => {
  refreshData();
});
// const
</script>
<style lang="scss" scoped></style>
