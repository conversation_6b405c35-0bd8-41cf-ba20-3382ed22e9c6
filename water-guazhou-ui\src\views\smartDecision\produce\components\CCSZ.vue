<template>
  <ScrollView :config="state.data"></ScrollView>
</template>
<script lang="ts" setup>
import { GetStationRecentDataByType } from '@/api/shuiwureports/zhandian'
import { useBusinessStore } from '@/store'
import ScrollView from './scroll.vue'

const state = reactive<{ data: any[] }>({
  data: []
})
const refreshData = async () => {
  const res = await GetStationRecentDataByType({
    stationType: '水质监测站',
    projectId: useBusinessStore().navSelectedRange?.data?.id
  })

  state.data = res.data?.map(item => {
    return {
      ...item,
      time: item.time && moment(item.time, 'YYYY-MM-DD HH:mm:ss').format('HH:mm:ss')
    }
  }) || []
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
