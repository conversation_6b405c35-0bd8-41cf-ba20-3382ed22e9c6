package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.dataBackup.DataBackupService;
import org.thingsboard.server.dao.model.sql.DataBackup;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.List;

@RestController
@RequestMapping("api/databackup")
public class DataBackupController extends BaseController{

    @Autowired
    private DataBackupService dataBackupService;

    @GetMapping("list")
    public List<DataBackup> findList(@RequestParam long start, @RequestParam long end) {
        return dataBackupService.findList(start, end);
    }

    @PostMapping("backup")
    public Object backup() throws ThingsboardException {
        SecurityUser currentUser = getCurrentUser();
        JSONObject result = new JSONObject();
        try {
            DataBackup dataBackup = new DataBackup();
            dataBackup.setCreateTime(System.currentTimeMillis());
            dataBackup.setType(DataConstants.DATA_BACKUP_NO_AUTO);
            dataBackup.setCreateBy(UUIDConverter.fromTimeUUID(currentUser.getId().getId()));
            dataBackup.setCreater(currentUser.getFirstName() + " (" + currentUser.getEmail() + ")");

            dataBackupService.backup(dataBackup);
            result.put("result", "备份完成");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("result", "备份失败");
        }

        return result;
    }

}
