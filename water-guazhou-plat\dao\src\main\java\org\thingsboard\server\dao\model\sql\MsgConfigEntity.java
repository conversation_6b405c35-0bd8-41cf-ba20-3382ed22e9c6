package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@TableName("tb_msg_config")
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class MsgConfigEntity {

    @TableId
    private String id;

        private String accessKeyId;

        private String accessKeySecret;

        private String signName;

    private Date createTime;

    private String tenantId;

}
