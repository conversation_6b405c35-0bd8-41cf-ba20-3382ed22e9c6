import{d as j,M as v,c as g,a8 as b,s as S,r,x as p,dP as O,a9 as x,o as R,g as I,n as M,q as c,i as s,F as P,b6 as V,b7 as B}from"./index-r0dFAfgr.js";import{_ as F}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as Y}from"./CardTable-rdWOL4_6.js";import{_ as q}from"./CardSearch-CB_HNR-Q.js";import{I as h}from"./common-CvK_P_ao.js";import{I as z,J as A,K as G,g as U,L as W}from"./manage-BReaEVJk.js";import{g as w}from"./projectManagement-CDcrrCQ1.js";import{S as D,D as J}from"./data-Dv9-Tstw.js";import{f as $}from"./DateFormatter-Bm9a68Ax.js";import H from"./detail-DEo1RlcF.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const K={class:"wrapper"},Me=j({__name:"designManagement",setup(Q){const{$btnPerms:d}=v(),f=g(),m=g(),y=g(),_=g({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:b(()=>a.projectType)},{label:"创建时间",field:"time",type:"daterange"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:h.DOWNLOAD,click:()=>{z().then(e=>{const t=window.URL.createObjectURL(e.data),i=document.createElement("a");i.style.display="none",i.href=t,i.setAttribute("download","设计管理.xlsx"),document.body.appendChild(i),i.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:S(B),click:()=>{var e;(e=f.value)==null||e.resetForm(),l()}},{perm:!0,text:"查询",icon:h.QUERY,click:()=>l()}]}]}),o=r({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"设计编号",prop:"code"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>$(e.createTime,"YYYY-MM-DD HH:mm:ss")},{label:"工作状态",prop:"status",tag:!0,tagColor:e=>{var t;return((t=D.find(i=>i.value===e.status))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=D.find(i=>i.value===e.status))==null?void 0:t.label}}],operationWidth:"300px",operations:[{disabled:e=>!e.status,isTextBtn:!1,text:"详情",perm:d("RoleManageEdit"),click:e=>{var t;a.selected=e,(t=y.value)==null||t.openDrawer()}},{disabled:e=>e.status!==null,isTextBtn:!1,type:"primary",text:"添加设计",perm:d("RoleManageEdit"),click:e=>{L(e)}},{disabled:e=>e.status!=="PROCESSING",isTextBtn:!1,type:"success",text:"编辑设计",perm:d("RoleManageEdit"),click:e=>N(e)},{disabled:e=>e.status!=="PROCESSING",isTextBtn:!1,text:"完成",perm:d("RoleManageEdit"),click:e=>{A(e.constructionCode).then(t=>{t.data.code===200?p.success("已完成"):p.warning("完成失败"),l()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,l()}}}),n=r({title:"添加工程设计信息",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{n.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=e.pipLengthDesign?JSON.stringify(e.pipLengthDesign):"",G(e).then(i=>{var u;n.submitting=!1,i.data.code===200?(p.success(t+"成功"),(u=m.value)==null||u.closeDialog(),l()):p.warning(t+"失败")}).catch(i=>{n.submitting=!1,p.warning(i)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"设计编号",field:"code",disabled:!0},{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"select",label:"设计分类",field:"type",options:J},{xs:12,type:"number",label:"设计费用(万元)",field:"cost",min:0},{type:"table",field:"pipLengthDesign",label:"设计管长",config:{titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{a.DesignTubeLength.push({num:O(),dn:0,length:0})}}]}]}],indexVisible:!0,height:"200px",dataList:b(()=>a.DesignTubeLength),columns:[{label:"管径(DN)",prop:"dn",tableDataName:"pipLengthDesign",formItemConfig:{type:"number",field:"dn",min:0,rules:[{required:!0,message:"请输入管径"}]}},{label:"管长(米)",prop:"length",tableDataName:"pipLengthDesign",formItemConfig:{type:"number",field:"length",min:0,rules:[{required:!0,message:"请输入管长"}]}}],operations:[{type:"danger",perm:!0,text:"删除",icon:h.DELETE,click:e=>{a.DesignTubeLength=a.DesignTubeLength.filter(t=>t.num!==e.num)}}],pagination:{hide:!0}}},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),C=r({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),T=r({defaultValue:b(()=>a.selected),border:!1,direction:"horizontal",column:1,title:"项目基础信息",fields:[{type:"text",label:"项目编号:",field:"project.code"},{type:"text",label:"项目名称:",field:"project.name"},{type:"text",label:"启动时间:",field:"project.startTimeName"},{type:"text",label:"预计结束时间:",field:"project.expectEndTimeName"},{type:"text",label:"项目负责人:",field:"project.principal"},{type:"divider",text:""},{type:"text",label:"工程编号:",field:"constructionCode"},{type:"text",label:"工程名称:",field:"constructionName"},{type:"text",label:"工程类别:",field:"constructionTypeName"},{type:"text",label:"工程预算(万元):",field:"cost"}]}),L=e=>{var t;n.title="添加工程设计信息",a.DesignTubeLength=[],n.defaultValue={...e||{}},(t=m.value)==null||t.openDialog()},N=e=>{var t;n.title="编辑工程设计信息",a.DesignTubeLength=[],n.defaultValue={...e||{}},a.DesignTubeLength=(e==null?void 0:e.pipLengthDesign)&&JSON.parse((e==null?void 0:e.pipLengthDesign)||"")||[],a.DesignTubeLength.map(i=>(i.dn+="",i)),(t=m.value)==null||t.openDialog()},a=r({projectList:[],projectType:[],DesignTubeLength:[],selected:{},getOptions:()=>{w({page:1,size:-1}).then(e=>{a.projectList=x(e.data.data.data||[],"children",{label:"name",value:"code"})}),U({page:1,size:-1}).then(e=>{a.projectType=x(e.data.data.data||[],"children")})}}),l=async()=>{var t;const e={size:o.pagination.limit||20,page:o.pagination.page||1,...((t=f.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.fromTime=e.time[0],e.toTime=e.time[1],delete e.time),W(e).then(i=>{o.dataList=i.data.data.data||[],o.pagination.total=i.data.data.total||0})};return R(()=>{l(),a.getOptions()}),(e,t)=>{const i=q,u=Y,k=F,E=V;return I(),M("div",K,[c(i,{ref_key:"refSearch",ref:f,config:s(_)},null,8,["config"]),c(u,{config:s(o),class:"card-table"},null,8,["config"]),c(k,{ref_key:"refForm",ref:m,config:s(n)},null,8,["config"]),c(E,{ref_key:"refDetail",ref:y,config:s(C)},{default:P(()=>[c(H,{config:s(a).selected,basic:s(T),show:4},null,8,["config","basic"])]),_:1},8,["config"])])}}});export{Me as default};
