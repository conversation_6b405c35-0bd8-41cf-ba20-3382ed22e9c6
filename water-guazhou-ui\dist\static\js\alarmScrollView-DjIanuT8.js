import{M as m,C as c,be as d,u as i,bf as u,g as p,n as h,p as n}from"./index-r0dFAfgr.js";const{$format:l,$confirm:f}=m(),$={data(){return{topAlarmList:[],baiduTTStoken:"24.13b592de03feb4dd730f9e14583f3566.2592000.1628919383.282335-24413152",alarmCount:0}},async mounted(){f("是否允许播放报警音频?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>this.audioMute=!1),this.getAlarmList();const t=await d({grant_type:"client_credentials",client_id:"sNIj3ILHO3b7bQINQbrx0HMo",client_secret:"kwkWaEOcbLPQuCceCs2mZYhkMIBFIjsU"});console.log(t,"getBaiduTTSToken"),this.baiduTTStoken=t.data.access_token},beforeUnmount(){clearInterval(this.alarmInterval)},methods:{async getAlarmList(){this.$refs.alarmScrollBox.style.transition="",this.$refs.alarmScrollBox.innerHTML="",this.$refs.alarmScrollBox.style.marginTop="0px";const t=this.topAlarmList.length;this.$refs.alarmScrollBox.style.transition="linear all 1s";for(const a of this.topAlarmList){const s=`${l(a.time)} ${a.rangeName} ${a.projectName} ${a.deviceName} ${a.alarmRemark||"故障"}`;this.$refs.alarmScrollBox.innerHTML+=`<span style="margin-right:60px;display:block" data-id='${a.id}' class="alarmText">${s}</span>`}t&&this.topAlarmList.length&&this.setAlarmInterval()},setAlarmInterval(){console.log("setAlarmIntervalsetAlarmIntervalsetAlarmIntervalsetAlarmInterval");const t=this.topAlarmList[0],a=`${l(t.time)} ${t.rangeName} ${t.projectName} ${t.deviceName} ${t.alarmRemark||"故障"}`;setTimeout(()=>this.playAudio({text:a,id:t.id}),3e3)},playNext(){if(console.log(this.alarmCount,"this.alarmCount++"),~~this.alarmCount>=this.topAlarmList.length-1){this.getAlarmList();return}this.alarmCount++,this.$refs.alarmScrollBox.style.marginTop=this.alarmCount*-36+"px";const t=this.topAlarmList[this.alarmCount],a=`${l(t.time)} ${t.rangeName} ${t.projectName} ${t.deviceName} ${t.alarmRemark||"故障"}`;this.playAudio({text:a,id:t.id})},async playAudio({text:t,id:a}){if(i().playedAlarmList.some(o=>o===a)||this.audioMute){setTimeout(()=>this.playNext(),5e3);return}i().addPlayedAlarm(a);const s={tex:encodeURI(encodeURI(t.replace("#",""))),tok:this.baiduTTStoken,cuid:"asdasda",ctp:1,lan:"zh",spd:5,pit:5,vol:15,per:4},r=await u(s);console.log(r,"getBaiduTTS");const e=new Audio;e.setAttribute("src",window.URL.createObjectURL(r.data)),e.addEventListener("ended",()=>{setTimeout(()=>this.playNext(),5e3)},!1),e.muted=this.audioMute,e.play()}}},x={class:"alarmBox"},T={ref:"alarmScrollBox",class:"alarmTextBox"};function g(t,a,s,r,e,o){return p(),h("div",x,[a[0]||(a[0]=n("i",{class:"iconfont icon-laba"},null,-1)),n("div",T,null,512)])}const y=c($,[["render",g],["__scopeId","data-v-95d24fff"]]);export{y as default};
