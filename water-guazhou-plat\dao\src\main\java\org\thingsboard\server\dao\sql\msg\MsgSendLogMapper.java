package org.thingsboard.server.dao.sql.msg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.MsgSendLogEntity;

@Mapper
public interface MsgSendLogMapper extends BaseMapper<MsgSendLogEntity> {

    Page<MsgSendLogEntity> getList(IPage<MsgSendLogEntity> iPage, @Param("start") Long start, @Param("end") Long end, @Param("templateName") String templateName,  @Param("phone") String phone, @Param("status") String status, @Param("tenantId") String tenantId);

}
