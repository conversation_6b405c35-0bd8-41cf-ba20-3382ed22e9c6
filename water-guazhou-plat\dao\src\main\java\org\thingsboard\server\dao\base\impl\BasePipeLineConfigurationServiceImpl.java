package org.thingsboard.server.dao.base.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration;
import org.thingsboard.server.dao.base.IBasePipeLineConfigurationService;
import org.thingsboard.server.dao.sql.base.BasePipeLineConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BasePipeLineConfigurationPageRequest;

import java.util.List;
import java.util.UUID;

/**
 * 平台管理-管网配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class BasePipeLineConfigurationServiceImpl implements IBasePipeLineConfigurationService {
    @Autowired
    private BasePipeLineConfigurationMapper basePipeLineConfigurationMapper;

    /**
     * 查询平台管理-管网配置
     *
     * @param id 平台管理-管网配置主键
     * @return 平台管理-管网配置
     */
    @Override
    public BasePipeLineConfiguration selectBasePipeLineConfigurationById(String id) {
        return basePipeLineConfigurationMapper.selectBasePipeLineConfigurationById(id);
    }

    /**
     * 查询平台管理-管网配置列表
     *
     * @param basePipeLineConfiguration 平台管理-管网配置
     * @return 平台管理-管网配置
     */
    @Override
    public IPage<BasePipeLineConfiguration> selectBasePipeLineConfigurationList(BasePipeLineConfigurationPageRequest basePipeLineConfiguration) {
        return basePipeLineConfigurationMapper.selectBasePipeLineConfigurationList(basePipeLineConfiguration);
    }

    /**
     * 新增平台管理-管网配置
     *
     * @param basePipeLineConfiguration 平台管理-管网配置
     * @return 结果
     */
    @Override
    public int insertBasePipeLineConfiguration(BasePipeLineConfiguration basePipeLineConfiguration) {
        basePipeLineConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return basePipeLineConfigurationMapper.insertBasePipeLineConfiguration(basePipeLineConfiguration);
    }

    /**
     * 修改平台管理-管网配置
     *
     * @param basePipeLineConfiguration 平台管理-管网配置
     * @return 结果
     */
    @Override
    public int updateBasePipeLineConfiguration(BasePipeLineConfiguration basePipeLineConfiguration) {
        return basePipeLineConfigurationMapper.updateBasePipeLineConfiguration(basePipeLineConfiguration);
    }

    /**
     * 批量删除平台管理-管网配置
     *
     * @param ids 需要删除的平台管理-管网配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePipeLineConfigurationByIds(List<String> ids) {
        return basePipeLineConfigurationMapper.deleteBasePipeLineConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-管网配置信息
     *
     * @param id 平台管理-管网配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePipeLineConfigurationById(String id) {
        return basePipeLineConfigurationMapper.deleteBasePipeLineConfigurationById(id);
    }
}
