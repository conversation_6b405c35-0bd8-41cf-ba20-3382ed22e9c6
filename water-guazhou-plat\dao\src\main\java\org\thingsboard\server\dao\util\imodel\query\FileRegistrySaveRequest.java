package org.thingsboard.server.dao.util.imodel.query;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.fileRegistry.FileRegistry;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class FileRegistrySaveRequest extends SaveRequest<FileRegistry> {
    // 文件名
    @NotNullOrEmpty
    private String fileName;

    // 文件地址
    @NotNullOrEmpty
    private String fileAddress;

    // 文件标签
    // @NotNullOrEmpty
    private String label;

    // 文件宿主
    @NotNullOrEmpty(condition = "haveLabel")
    private String host;

    @SuppressWarnings("unused")
    private boolean haveLabel() {
        return label != null;
    }

    @Override
    protected FileRegistry build() {
        FileRegistry entity = new FileRegistry();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected FileRegistry update(String id) {
        FileRegistry entity = new FileRegistry();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(FileRegistry entity) {
        entity.setFileName(fileName);
        entity.setFileAddress(fileAddress);
        entity.setLabel(label);
        entity.setHost(host);
        entity.setUploadTime(new Date());
    }

}