package org.thingsboard.server.dao.stationSetting;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationArchivesListRequest;
import org.thingsboard.server.dao.model.sql.stationSetting.StationArchives;

import java.util.List;

public interface StationArchivesService {
    PageData<StationArchives> findList(StationArchivesListRequest request, TenantId tenantId);

    List<StationArchives> findAll(TenantId tenantId);

    void remove(List<String> ids);

    void save(StationArchives entity);
}
