package org.thingsboard.server.dao.util.imodel.query;

public class TreeQueryOptions {
    // 是否分层
    private boolean pure;

    // 是否剪枝
    private boolean prune = false;

    private int maxDepth;

    private String[] treeLabels;

    private String[] nameMappings;

    private TreeQueryOptions() {

    }

    private TreeQueryOptions(boolean pure) {
        this.pure = pure;
    }

    public static TreeQueryOptions create() {
        return new TreeQueryOptions();
    }

    public static TreeQueryOptions withPure(boolean pure) {
        return new TreeQueryOptions(pure);
    }

    public boolean pure() {
        return pure;
    }

    public TreeQueryOptions pure(boolean pure) {
        this.pure = pure;
        return this;
    }

    public boolean prune() {
        return prune;
    }

    public TreeQueryOptions prune(boolean prune) {
        this.prune = prune;
        return this;
    }

    public int maxDepth() {
        return maxDepth;
    }

    public void maxDepth(int maxDepth) {
        this.maxDepth = maxDepth;
    }

    public TreeQueryOptions treeLabels(String... labels) {
        this.treeLabels = labels;
        return this;
    }

    public TreeQueryOptions nameMappings(String... nameMappings) {
        this.nameMappings = nameMappings;
        return this;
    }

    public String labelOf(int i) {
        if (treeLabels == null || i > treeLabels.length) {
            return null;
        }

        return treeLabels[i];
    }

    public boolean canMapToName(int index) {
        return index < treeLabels.length;
    }

    public String getMappingName(int index) {
        if (nameMappings != null && index < nameMappings.length)
            return nameMappings[index];
        return null;
    }

}
