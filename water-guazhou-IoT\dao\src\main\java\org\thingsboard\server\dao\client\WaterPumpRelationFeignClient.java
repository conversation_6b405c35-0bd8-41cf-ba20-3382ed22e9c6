package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.WaterPumpRelationEntity;

import java.util.List;

@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface WaterPumpRelationFeignClient {


    @GetMapping("api/waterPumpRelation/findByWaterPump")
    List<WaterPumpRelationEntity> findByWaterPump(@RequestParam("waterPumpId") String waterPumpId);

    @GetMapping("api/waterPumpRelation/findAll")
    List<WaterPumpRelationEntity> findAll() throws ThingsboardException;

    @GetMapping("findByType")
    List<WaterPumpRelationEntity> findByType(@RequestParam("type") String type) throws ThingsboardException;

}
