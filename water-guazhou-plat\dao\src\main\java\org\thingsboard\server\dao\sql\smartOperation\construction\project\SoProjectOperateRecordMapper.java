package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordPageRequest;

@Mapper
public interface SoProjectOperateRecordMapper extends BaseMapper<SoProjectOperateRecord> {
    IPage<SoProjectOperateRecord> findByPage(SoProjectOperateRecordPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SoProjectOperateRecord entity);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean updateFully(SoProjectOperateRecord entity);
}
