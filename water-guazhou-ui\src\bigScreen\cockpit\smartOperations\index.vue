<template>
  <div class="operations-panel">
    <div class="panel-vertical left">
      <PanelCard title="用户报装看板">
        <div class="svg1"></div>
      </PanelCard>
      <PanelCard title="实时抄表管理">
        <div class="chart-container">
          <HorizontalBarChart 
            :data="meterReadingData"
            series1Name="完成抄表"
            series2Name="未完成抄表"
            unitLabel="百户"
            :showUnitLabel="true"
            :showLegend="true"
            height="100%"
          />
        </div>
      </PanelCard>
      <PanelCard title="区域用水分析">
        <div class="chart-container">
          <div class="unit-label">单位: 万立方米</div>
          <div class="analysis-btn">分析建议</div>
          <BarChart 
            :data="waterConsumptionData" 
            color1="#00E5BD"
            color2="#FFC61A"
            series1Name="实际用水"
            series2Name="计划用水"
            unit="万立方米"
            height="100%"
            :showDecorators="true"
          />
        </div>
      </PanelCard>
    </div>
    <div class="panel-vertical right">
      <PanelCard title="营收看板">
        <div class="svg2"></div>
      </PanelCard>
      <PanelCard title="区域水费与产销看板">
        <div class="chart-container">
          <div class="unit-label">单位: 万立方米</div>
          <div class="analysis-btn">分析建议</div>
          <BarChart 
            :data="waterUsageData" 
            color1="#47EBEB"
            color2="#FFC61A"
            series1Name="用水量"
            series2Name="供水量"
            unit="万立方米"
            height="100%"
            :showDecorators="false"
          />
        </div>
      </PanelCard>
      <PanelCard title="未闭环工单">
        <ScrollTable :list="tableData" :columns="columns">
          <template #status="{ row }">
            <span class="status-tag" :class="'status-' + row.status">{{ row.statusText }}</span>
          </template>
        </ScrollTable>
      </PanelCard>
    </div>
  </div>
</template>
<script setup>
import PanelCard from '../components/common/PanelCard.vue'
import BarChart from '../components/BarChart.vue'
import HorizontalBarChart from '../components/HorizontalBarChart.vue'
import ScrollTable from '../components/ScrollTable.vue'
import { ref, onMounted, onUnmounted } from 'vue'

// 区域用水量与供水量数据
const waterUsageData = [
  {
    name: '瓜洲东城区',
    value1: 75,
    value2: 50
  },
  {
    name: '瓜洲南部',
    value1: 74,
    value2: 52
  },
  {
    name: '瓜洲北郊',
    value1: 76,
    value2: 52
  },
  {
    name: '瓜洲老城区',
    value1: 76,
    value2: 51
  },
  {
    name: '瓜洲西城区',
    value1: 75,
    value2: 52
  }
]

// 区域用水分析数据
const waterConsumptionData = [
  {
    name: '居民用水',
    value1: 65,
    value2: 62
  },
  {
    name: '商业用水',
    value1: 42,
    value2: 40
  },
  {
    name: '工业用水',
    value1: 58,
    value2: 55
  },
  {
    name: '公共用水',
    value1: 35,
    value2: 32
  },
  {
    name: '特种用水',
    value1: 28,
    value2: 25
  }
]

// 抄表数据
const meterReadingData = [
  {
    name: '瓜洲东城区',
    value1: 6,
    value2: 99
  },
  {
    name: '瓜洲南部',
    value1: 41,
    value2: 39
  },
  {
    name: '瓜洲北郊',
    value1: 33,
    value2: 74
  },
  {
    name: '瓜洲老城区',
    value1: 48,
    value2: 16
  },
  {
    name: '瓜洲西城区',
    value1: 22,
    value2: 22
  }
]

// 表格列配置
const columns = [
  { key: 'id', title: '工单编号', width: 1.2 },
  { key: 'type', title: '工单类型', width: 1 },
  { key: 'time', title: '发起时间', width: 1.2 },
  { key: 'status', title: '状态', width: 0.8, slot: 'status' }
]

// 表格数据
const tableData = [
  { id: 'GD20240615001', type: '管网维修', time: '2024-06-15 08:23', status: 'pending', statusText: '待处理' },
  { id: 'GD20240614089', type: '水表维修', time: '2024-06-14 15:42', status: 'processing', statusText: '处理中' },
  { id: 'GD20240614056', type: '管网巡检', time: '2024-06-14 10:18', status: 'pending', statusText: '待处理' },
  { id: 'GD20240613128', type: '管网维修', time: '2024-06-13 16:35', status: 'processing', statusText: '处理中' },
  { id: 'GD20240613099', type: '水表安装', time: '2024-06-13 14:20', status: 'pending', statusText: '待处理' },
  { id: 'GD20240612201', type: '水质检测', time: '2024-06-12 09:50', status: 'processing', statusText: '处理中' },
  { id: 'GD20240612156', type: '管网维修', time: '2024-06-12 08:22', status: 'pending', statusText: '待处理' },
  { id: 'GD20240611087', type: '水表更换', time: '2024-06-11 13:45', status: 'processing', statusText: '处理中' },
  { id: 'GD20240611042', type: '管网维修', time: '2024-06-11 10:12', status: 'pending', statusText: '待处理' },
  { id: 'GD20240610118', type: '水表维修', time: '2024-06-10 16:30', status: 'pending', statusText: '待处理' },
]
</script>
<style lang="scss" scoped>
.operations-panel{
  .panel-vertical {
    position: absolute;
    top: 24px;
    width: 450px;
    height: calc(100vh - 96px);
    bottom: 0;
    z-index: 99;
    display: flex;
    flex-direction: column;
    >div{
      flex: 1;
      margin-bottom: 24px;
    }
  }
  .chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    
    .analysis-btn {
      position: absolute;
      top: 5px;
      right: 20px;
      padding: 3px 10px;
      background-color: rgba(0, 149, 255, 0.3);
      border: 1px solid #0095FF;
      border-radius: 4px;
      color: #fff;
      font-size: 12px;
      cursor: pointer;
      z-index: 10;
      box-shadow: inset 0 0 4px 1px #0095FF;
      
      &:hover {
        background-color: rgba(0, 149, 255, 0.5);
      }
    }
  }
  .svg1{
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background: url('./用户报装看板.svg');
  }
  .svg2{
    width: 100%;
    height: 100%;
    background: url('./营收看板.svg');
    background-size: cover;
    background-repeat: no-repeat;
  }
  .left{
    left: 24px;
  }
  .right{
    right: 24px;
  }
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
  }
  
  &.status-pending {
    background-color: rgba(255, 198, 26, 0.2);
    border: 1px solid #FFC61A;
    color: #FFC61A;
  }
  
  &.status-processing {
    background-color: rgba(0, 229, 189, 0.2);
    border: 1px solid #00E5BD;
    color: #00E5BD;
  }
}
</style>