import{d as K,j as Q,r as E,a8 as O,c as w,am as X,o as Y,bu as Z,ay as ee,g as _,n as A,q as N,F as te,p as v,aw as P,i,aB as ae,aJ as le,bh as k,c5 as oe,ab as ne,C as se}from"./index-r0dFAfgr.js";import"./index-0NlGN6gS.js";import{u as re}from"./useDetector-BRcb7GRN.js";import{c as ie}from"./dma-SMxrzG7b.js";const ce={class:"division-detail"},ue={class:"tab-content"},fe={class:"list"},de={class:"label",for:""},ve={class:"value"},me={class:"title"},he={key:0,class:"charts"},be={key:1,class:"charts"},ge={class:"chart"},pe={class:"chart"},_e=K({__name:"DivisionDetail",props:{data:{},partitions:{}},setup(G){const b=Q(),j=t=>({title:{text:`{name|合计(个)}
{val|`+t.reduce((a,o)=>(o.value??0)+a,0)+"}",top:"center",left:"29%",textAlign:"center",textStyle:{rich:{name:{fontSize:16,fontWeight:"normal",padding:[8,0],align:"center",color:b.isDark?"#fff":"#2A2A2A"},val:{fontSize:25,fontWeight:"bold",color:b.isDark?"#fff":"#2A2A2A"}}}},tooltip:{trigger:"item"},legend:{type:"scroll",icon:"circle",orient:"vertical",top:"center",right:"left",textStyle:{color:"#318DFF",rich:{name:{align:"left",width:60,fontSize:12,color:b.isDark?"#fff":"#2A2A2A"},value:{align:"left",width:50,fontSize:12,color:"#00ff00"},count:{align:"left",width:40,fontSize:12},upRate:{align:"left",fontSize:12},downRate:{align:"left",fontSize:12,color:"#409EFF"}}},data:t.map(a=>a.name),formatter(a){if(t&&t.length){for(let o=0;o<t.length;o++)if(a===t[o].name)return"{name| "+a+"}{value| "+t[o].value+" 个}{downRate| "+(t[o].scale||"")+"}"}}},series:[{name:"分区状态",type:"pie",radius:["50%","70%"],center:["30%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:0,borderColor:"#fff",borderWidth:2},label:{show:!1},emphasis:{label:{show:!1,fontSize:12,fontWeight:"bold"}},labelLine:{show:!1},data:t}]}),C=(t=[],l,a,o=2)=>{const r=function(n){const s=/(?=(\B)(\d{3})+$)/g;return n.toString().replace(s,",")},f=t.reduce((n,s)=>n+(parseFloat(s.value)||0)*1,0),S=ne(f);return{tooltip:{trigger:"item",formatter:n=>""+n.name+": "+Number(n.value).toFixed(0)+" 户"},legend:{type:"scroll",icon:"circle",orient:"vertical",right:"left",top:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#fff",rich:{name:{align:"left",width:90,fontSize:12,color:b.isDark?"#fff":"#2A2A2A"},value:{align:"left",width:50,fontSize:12,color:"#00ff00"},downRate:{align:"left",fontSize:12,color:"#409EFF"}}},data:t.map(n=>n.name),formatter(n){if(t&&t.length){for(let s=0;s<t.length;s++)if(n===t[s].name)return"{name| "+(t[s].nameAlias||n)+"}{value| "+(t[s].valueAlias||t[s].value)+" }{downRate| "+(t[s].scale||"")+"}"}}},title:[{text:`{name|总数(户)}
{val|`+r(S.value.toFixed(0))+"}",top:"center",left:"19%",textAlign:"center",textStyle:{rich:{name:{fontSize:10,fontWeight:"normal",padding:[8,0],align:"center",color:b.isDark?"#fff":"#2A2A2A"},val:{fontSize:16,fontWeight:"bold",color:b.isDark?"#fff":"#2A2A2A"}}}}],series:[{type:"pie",radius:["35%","50%"],center:["20%","50%"],data:t,hoverAnimation:!0,label:{show:!1,formatter:n=>"{icon|●}{name|"+n.name+"}{value|"+r(Number(n.value||"0").toFixed(o))+"}",padding:[0,-100,25,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:18,fontWeight:"bold"}}}}]}},u=G,q=E({type:"tabs",tabType:"border-card",tabs:[{label:"分区信息",value:"1"}]}),c=E({curTab:"1",option_root:null,option_user_big_user:null,option_user_inhabitant:null,basicInfo:[]}),y=O(()=>{var t,l,a;return((a=(l=(t=u.data)==null?void 0:t.data)==null?void 0:l.path)==null?void 0:a.length)??0}),g=O(()=>{var t,l,a;return(a=(l=(t=u.data)==null?void 0:t.partition)==null?void 0:l.type)==null?void 0:a.toString()}),H=()=>{var l,a,o;let t=0;return(o=(a=(l=u.data)==null?void 0:l.data)==null?void 0:a.children)==null||o.map(r=>{var f;y.value===1?t+=((f=r.children)==null?void 0:f.length)||0:t++}),t},F=async()=>{var l,a,o,r,f,S,x,n,s,B,L,M,R,I,$;if((a=(l=u.data)==null?void 0:l.data)!=null&&a.value)try{const z=await ie({partitionId:(r=(o=u.data)==null?void 0:o.data)==null?void 0:r.value}),e=((f=z.data)==null?void 0:f.data)||{};if(c.basicInfo=[{label:"分区名称",value:(e==null?void 0:e.partitionName)??"--"},{label:"分区类型",value:(e==null?void 0:e.type)??"--"},...y.value<=2?[y.value===1?{label:"一级分区",value:(((n=(x=(S=u.data)==null?void 0:S.data)==null?void 0:x.children)==null?void 0:n.length)||0)+"个"}:{label:"上级分区",value:(e==null?void 0:e.parentName)??"--"},{label:"二级分区",value:H()+" 个"},{label:"DMA分区个数",value:(((s=u.partitions)==null?void 0:s.filter(m=>{var d;return!!m.geom&&m.pid===((d=u.data)==null?void 0:d.partition.id)}).length)??"--")+"个"}]:[],{label:"供水面积",value:((e==null?void 0:e.supplyWaterArea)??"--")+"km²"},{label:"管线长度",value:((e==null?void 0:e.mainLineLength)??"--")+"km"},{label:"用户类型",value:(e==null?void 0:e.userType)??"--"},{label:"挂接用户数",value:((e==null?void 0:e.custNum)??"--")+"户"},{label:"远传大用户数",value:((e==null?void 0:e.bigUserNum)??"--")+"户"},{label:"抄表员",value:(e==null?void 0:e.copyMeterUser)??"--"},{label:"流量监测点",value:`${(e==null?void 0:e.flowNum)??"--"} 个`},{label:"压力监测点",value:`${(e==null?void 0:e.pressureNum)??"--"} 个`},{label:"负责人",value:(e==null?void 0:e.director)??"--"},...y.value<=2?[{label:"分区范围",value:(e==null?void 0:e.range)??"--"}]:[{label:"上级分区",value:(e==null?void 0:e.parentName)??"--"},{label:"入水口",value:`${(e==null?void 0:e.inWaterNum)??"--"} 个`},{label:"分区状态",value:(e==null?void 0:e.status)??"--"},{label:"分区地址",value:(e==null?void 0:e.range)??"--"},{label:"物业公司",value:(e==null?void 0:e.propertyName)??"--"}]],g.value==="1"){const d=(((L=(B=z.data)==null?void 0:B.data)==null?void 0:L.dmaPartitionStatus)??[]).reduce((h,D)=>(D.num??0)+h,0);c.option_root=j(((I=(R=(M=z.data)==null?void 0:M.data)==null?void 0:R.dmaPartitionStatus)==null?void 0:I.map(h=>({value:h.num,name:h.name,scale:d===0?"0%":(h.num/d).toFixed(2)+"%"})))||[])}else{const m=(e==null?void 0:e.custNum)||0,d=(e==null?void 0:e.bigUserNum)||0,h=(($=e==null?void 0:e.custUseWaterType)==null?void 0:$.map(p=>({name:p.name,nameAlias:p.name,value:p.num,valueAlias:p.num+" 户",scale:`${m===0?"0.00":(p.num/m*100).toFixed(2)} %`})))||[{name:"居民用水",nameAlias:"居民用水",value:m,valueAlias:m+" 户",scale:"100.00 %"}],D=[{name:"大用户数",nameAlias:"大用户数",value:d,valueAlias:d+" 户",scale:d===0?"0.00 %":"100.00%"}];c.option_user_big_user=C(D),c.option_user_inhabitant=C(h)}}catch{}},J=re(),T=w(),W=w(),U=w(),V=w();return X(()=>u.data,()=>{F()}),Y(()=>{J.listenToMush(T.value,()=>{var t,l,a;(t=W.value)==null||t.resize(),(l=U.value)==null||l.resize(),(a=V.value)==null||a.resize()})}),Z(()=>{F()}),(t,l)=>{const a=ee("VChart"),o=oe;return _(),A("div",ce,[N(o,{modelValue:i(c).curTab,"onUpdate:modelValue":l[0]||(l[0]=r=>i(c).curTab=r),config:i(q),class:"tabs"},{content:te(()=>[v("div",ue,[v("div",{class:P(["left",i(g)==="1"?"root":"division"])},[v("div",fe,[(_(!0),A(ae,null,le(i(c).basicInfo,(r,f)=>(_(),A("div",{key:f,class:"list-item"},[v("label",de,k(r.label+":"),1),v("span",ve,k(r.value),1)]))),128))])],2),v("div",{ref_key:"refDiv",ref:T,class:P(["right",i(g)==="1"?"root":"division"])},[v("div",me,k(i(g)==="1"?"DMA分区状态":"挂接用户信息"),1),i(g)==="1"?(_(),A("div",he,[N(a,{ref_key:"refChart1",ref:W,option:i(c).option_root},null,8,["option"])])):(_(),A("div",be,[v("div",ge,[N(a,{ref_key:"refChart2",ref:U,option:i(c).option_user_inhabitant},null,8,["option"])]),v("div",pe,[N(a,{ref_key:"refChart3",ref:V,option:i(c).option_user_big_user},null,8,["option"])])]))],2)])]),_:1},8,["modelValue","config"])])}}}),we=se(_e,[["__scopeId","data-v-047132d2"]]);export{we as default};
