<template>
  <div
    v-if="
      appStore.subMenuParentRoute?.children?.length &&
      !appStore.subMenuParentRoute?.meta?.hidden
    "
    class="submenu-bg overlay-y"
  >
    <div class="submenu-bg__header title">
      {{ appStore.subMenuParentRoute?.meta?.title }}
    </div>
    <template v-for="cMenu in subMenuRoutes" :key="cMenu.path">
      <div v-if="!cMenu.meta?.hidden" class="submenu-group">
        <div class="submenu-title">
          <span :class="'title' + ' ' + cMenu.meta.icon">
            {{ cMenu.meta.title }}
          </span>
        </div>
        <template v-if="cMenu.children?.length">
          <AppSubMenuItem
            v-for="cCMenu in cMenu.children"
            :key="cCMenu.path"
            :menu="cCMenu"
          ></AppSubMenuItem>
        </template>
        <template v-else>
          <AppSubMenuItem :key="cMenu.path" :menu="cMenu"></AppSubMenuItem>
        </template>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store';
import { filterRoutes } from '@/utils/RouterHelper';
import AppSubMenuItem from './AppSubMenuItem.vue';

const appStore = useAppStore()
const keyword = ref<string>('');
const subMenuRoutes = computed(() => {
  return filterRoutes(appStore.subMenuParentRoute.children, keyword.value, []);
});
defineExpose({
  keyword
});
</script>
<style lang="scss" scoped>
.title {
  font-family: 'PingFang SC';
  font-style: normal;
}
.submenu-bg {
  background-color: 'var(--el-bg-color-page)';
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.25);
  padding: 30px 40px;
  .submenu-bg__header {
    color: #88bdff;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 30px;
  }
}
.submenu-group__items {
  display: flex;
}
.submenu-group {
  display: inline-block;
  margin-right: 80px;
  margin-bottom: 20px;
}
.submenu-title {
  font-family: 'PingFang SC';
  font-style: normal;
  line-height: 22px;
  color: var(--el-text-color-secondary);
  margin: 0 0 12px 0;
  .title {
    font-size: 14px;
    &.iconfont::before {
      margin-right: 0.5em;
    }
  }
}
</style>
