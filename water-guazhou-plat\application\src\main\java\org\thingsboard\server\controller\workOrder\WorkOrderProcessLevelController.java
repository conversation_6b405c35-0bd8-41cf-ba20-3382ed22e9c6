package org.thingsboard.server.controller.workOrder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderProcessLevel;
import org.thingsboard.server.dao.orderWork.WorkOrderProcessLevelService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

@RestController
@RequestMapping("api/workOrderProcessLevel")
public class WorkOrderProcessLevelController extends BaseController {

    @Autowired
    private WorkOrderProcessLevelService workOrderProcessLevelService;

    @GetMapping("list")
    public IstarResponse findList(@RequestParam(required = false) String status) throws ThingsboardException {
        return IstarResponse.ok(workOrderProcessLevelService.findList(status, getTenantId()));
    }

    @PostMapping("changeStatus")
    public IstarResponse changeStatus(@RequestBody WorkOrderProcessLevel param) {
        workOrderProcessLevelService.changeStatus(param);
        return IstarResponse.ok();
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody WorkOrderProcessLevel entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        workOrderProcessLevelService.save(entity);
        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        workOrderProcessLevelService.remove(ids);
        return IstarResponse.ok();
    }



}
