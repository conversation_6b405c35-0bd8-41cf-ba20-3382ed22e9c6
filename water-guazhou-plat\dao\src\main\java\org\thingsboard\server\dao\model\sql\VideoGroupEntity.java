package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@TableName("video_group")
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class VideoGroupEntity {

    @TableId
    private String id;

    private String name;

    private String parentId;

    private Date createTime;

    private String tenantId;

    private Integer orderNum;

    @TableField(exist = false)
    private Integer videoNum;

}
