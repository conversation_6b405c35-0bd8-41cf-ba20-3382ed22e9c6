package org.thingsboard.server.dao.model.sql.smartManagement.district;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_district_point")
public class CircuitDistrictPoint {
    // id
    @TableId
    private String id;

    // 关键点名称
    private String name;

    // 所属区域、路线(点阵)Id
    private String areaId;

    // 纬度
    private String lat;

    // 经度
    private String lon;

    // 备注
    private String remark;

    // 创建时间
    private Date createTime;

    // 租户Id
    private String tenantId;

}
