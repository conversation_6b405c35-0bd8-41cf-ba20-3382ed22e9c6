package org.thingsboard.server.dao.sql.smartService.kpi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormParam;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface KpiNormParamMapper extends BaseMapper<KpiNormParam> {

    List<KpiNormParam> getList(@Param("source") String source, @Param("type") String type, @Param("code") String code, @Param("name") String name, @Param("page") int page, @Param("size") int size, @Param("enabled") Boolean enabled, @Param("tenantId") String tenantId);

    int getListCount(@Param("source") String source, @Param("type") String type, @Param("code") String code, @Param("name") String name, @Param("enabled") Boolean enabled, @Param("tenantId") String tenantId);
}
