import{aS as s}from"./index-r0dFAfgr.js";import{s as l}from"./Point-WxyopZva.js";import{aI as c}from"./MapView-DaoQedLH.js";import{a as m,n as f}from"./project-DUuzYgGl.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";async function g(e=null,a){var o,r;if(s.geometryServiceUrl)return s.geometryServiceUrl;if(!e)throw new l("internal:geometry-service-url-not-configured");let t;t="portal"in e?e.portal||c.getDefault():e,await t.load({signal:a});const n=(r=(o=t.helperServices)==null?void 0:o.geometry)==null?void 0:r.url;if(!n)throw new l("internal:geometry-service-url-not-configured");return n}async function h(e,a,t=null,n){const o=await g(t,n),r=new m;r.geometries=[e],r.outSpatialReference=a;const i=await f(o,r,{signal:n});if(i&&Array.isArray(i)&&i.length===1)return i[0];throw new l("internal:geometry-service-projection-failed")}export{g as getGeometryServiceURL,h as projectGeometry};
