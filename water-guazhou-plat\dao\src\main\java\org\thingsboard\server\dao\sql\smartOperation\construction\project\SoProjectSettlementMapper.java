package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectSettlement;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementPageRequest;

@Mapper
public interface SoProjectSettlementMapper extends BaseMapper<SoProjectSettlement> {
    IPage<SoProjectSettlement> findByPage(SoProjectSettlementPageRequest request);

    boolean update(SoProjectSettlement entity);

    boolean save(SoProjectSettlement entity);

    boolean updateFully(SoProjectSettlement entity);

    String getIdByProjectCodeAndTenantId(@Param("projectCode")String projectCode,@Param("tenantId")String tenantId);


}
