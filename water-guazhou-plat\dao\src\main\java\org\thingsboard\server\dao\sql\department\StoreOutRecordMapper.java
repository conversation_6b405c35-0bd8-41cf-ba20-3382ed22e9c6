package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecord;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordPageRequest;

import java.util.List;

@Mapper
public interface StoreOutRecordMapper extends BaseMapper<StoreOutRecord> {
    IPage<StoreOutRecord> findByPage(StoreOutRecordPageRequest request);

    boolean update(StoreOutRecord outRecord);

    List<String> childrenId(String id);

    boolean getIsOut(String id);
}
