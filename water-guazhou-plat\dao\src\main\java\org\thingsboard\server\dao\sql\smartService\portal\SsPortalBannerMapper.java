package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBanner;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBannerPageRequest;

@Mapper
public interface SsPortalBannerMapper extends BaseMapper<SsPortalBanner> {
    IPage<SsPortalBanner> findByPage(SsPortalBannerPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalBanner entity);

    boolean updateFully(SsPortalBanner entity);

    boolean active(SsPortalActiveRequest req);

}
