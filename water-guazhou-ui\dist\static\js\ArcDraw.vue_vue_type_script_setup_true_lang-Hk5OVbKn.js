import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as f,ad as A,Q as v,g as _,n as C}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as w}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as G}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const O=f({__name:"ArcDraw",props:{layerid:{},layername:{},multiple:{type:Boolean},drawEnd:{type:Function}},emits:["update","create","delete","undo","redo"],setup(c,{expose:a,emit:d}){const i=d,n=c,l=A("view"),t=w(l,{id:n.layerid,title:n.layername}),{sketch:p,initSketch:s,destroySketch:u}=G(),k=e=>{var o;l&&(!n.multiple&&(t==null||t.removeAll()),(o=p.value)==null||o.create(e))},B=()=>{t==null||t.removeAll()};s(l,t,{updateCallBack:e=>m("update",e),createCallBack:e=>m("create",e),delCallBack:e=>m("delete",e),undoCallBack:e=>m("undo",e),redoCallBack:e=>m("redo",e)});const m=(e,o)=>{var r;i(e,o,(t==null?void 0:t.graphics.toArray())||[]),(o.state==="complete"||o.state==="cancel")&&((r=n.drawEnd)==null||r.call(n,(t==null?void 0:t.graphics.toArray())||[]))};return a({clear:B,initDraw:k,getGraphics:()=>(t==null?void 0:t.graphics.toArray())||[],setGraphics:e=>{t==null||t.removeAll(),t==null||t.addMany(e)}}),v(()=>{t&&(l==null||l.map.remove(t)),t==null||t.removeAll(),t==null||t.destroy(),u()}),(e,o)=>(_(),C("div"))}});export{O as _};
