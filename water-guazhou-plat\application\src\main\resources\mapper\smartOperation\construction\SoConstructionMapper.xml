<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionMapper">
    <sql id="Base_Column_List">
        <bind name="constructionScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION"/>
        <!--@sql select -->
        construction.id,
        construction.code,
        construction.project_code,
        project.name                                                       as project_name,
        construction.name,
        construction.address,
        construction.type_id,
        (select name from so_general_type where id = construction.type_id) as type_name,
        construction.create_time,
        construction.firstpart_name,
        construction.firstpart_phone,
        construction.detail_address,
        construction.remark,
        construction.estimate,
        construction.attachments,
        construction.creator,
        construction.update_time,
        construction.update_user,
        construction.tenant_id,
        (
        select
        <!--无相关流程-->
        (select count(1) = 0
         from so_construction_task_info
         where construction_code = construction.code)
            and
        <!--无关联设备项-->
        (select count(1) = 0
         from so_device_item item
         where item.scope = #{constructionScope}
           and item.identifier = construction.code
           and construction.tenant_id = item.tenant_id)
        )                           can_be_delete
        <!--@sql from so_construction construction, so_project project-->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="name" property="name"/>
        <result column="address" property="address"/>
        <result column="type_id" property="typeId"/>
        <result column="type_name" property="typeName"/>
        <result column="create_time" property="createTime"/>
        <result column="firstpart_name" property="firstpartName"/>
        <result column="firstpart_phone" property="firstpartPhone"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="remark" property="remark"/>
        <result column="estimate" property="estimate"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="can_be_delete" property="canBeDelete"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_project project
                           on construction.project_code = project.code
                               and construction.tenant_id = project.tenant_id
        <where>
            <if test="code != null">
                and construction.code like '%' || #{code} || '%'
            </if>
            <if test="projectCode != null">
                and construction.project_code = #{projectCode}
            </if>
            <if test="name != null">
                and construction.name like '%' || #{name} || '%'
            </if>
            <if test="address != null">
                and construction.address like '%' || #{address} || '%'
            </if>
            <if test="fromTime != null">
                and construction.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and construction.create_time &lt;= #{toTime}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update so_construction
        <set>
            <if test="projectCode != null">
                project_code = #{projectCode},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="typeId != null">
                type_id = #{typeId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="firstpartName != null">
                firstpart_name = #{firstpartName},
            </if>
            <if test="firstpartPhone != null">
                firstpart_phone = #{firstpartPhone},
            </if>
            <if test="detailAddress != null">
                detail_address = #{detailAddress},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="estimate != null">
                estimate = #{estimate},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction
        set project_code    = #{projectCode},
            name            = #{name},
            address         = #{address},
            type_id         = #{typeId},
            create_time     = #{createTime},
            firstpart_name  = #{firstpartName},
            firstpart_phone = #{firstpartPhone},
            detail_address  = #{detailAddress},
            remark          = #{remark},
            estimate        = #{estimate},
            attachments     = #{attachments}
        where id = #{id}
    </update>

    <select id="isCodeExists" resultType="boolean">
        select count(1)
        from so_construction outside
        where outside.code = #{code}
          and tenant_id = #{tenantId}
        <if test="id != null and id != ''">
            <!--需要存在，下方不存在是会返回null，null意味着false(不重复)，会出现假是自己(假不重复)-->
            and (select count(1) > 0 from so_construction where id = #{id})
            <!--两个code相等则意味着是自己，不是自己则重复，是自己则放行(false-是自己所以返回未重复，true-不是自己所以返回重复)-->
            and (select not outside.code = code from so_construction where id = #{id})
        </if>
    </select>

    <select id="generateCode" resultType="java.lang.String">
        <!--@formatter:off-->
        select 'S' || generate_number_reset_different_day_with_simple_date_prefix('so_construction' || #{tenantId}, 'fm000000', 999999)
        <!--@formatter:on-->
    </select>

    <resultMap id="ConstructionWorkflowResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow">
        <result column="scope" property="scope"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="start_time" property="startTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="process_user" property="processUser"/>
        <result column="status" property="status"/>
    </resultMap>
    <select id="completionInfo" resultMap="ConstructionWorkflowResultMap">
        <!--@formatter:off-->
        <bind name="design" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_DESIGN"/>
        <bind name="estimate" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_ESTIMATE"/>
        <bind name="visa" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_VISA"/>
        <bind name="contract" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_CONTRACT"/>
        <bind name="expense" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_EXPENSE"/>
        <bind name="apply" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_APPLY"/>
        <bind name="accept" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_ACCEPT"/>
        <bind name="settlement" value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_SETTLEMENT"/>
        <!--@formatter:on-->
        select #{design}          as scope,
               construction.code  as code,
               construction.name  as name,
               info.create_time   as start_time,
               info.complete_time as complete_time,
               info.creator       as process_user,
               info.status        as status
        from so_construction construction
                 left join so_construction_design main
                           on main.construction_code = construction.code
                               and main.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = main.construction_code
                               and info.tenant_id = main.tenant_id
        where main.tenant_id = #{tenantId}
          and main.construction_code = #{constructionCode}
          and info.scope = #{design}
        union all

        select #{estimate}        as scope,
               construction.code  as code,
               construction.name  as name,
               info.create_time   as start_time,
               info.complete_time as complete_time,
               info.creator       as process_user,
               info.status        as status
        from so_construction construction
                 left join so_construction_estimate main
                           on main.construction_code = construction.code
                               and main.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = main.construction_code
                               and info.tenant_id = main.tenant_id
        where main.tenant_id = #{tenantId}
          and main.construction_code = #{constructionCode}
          and info.scope = #{estimate}
        union all

        select #{visa}                            as scope,
               construction.code                  as code,
               construction.name                  as name,
               info.create_time                   as start_time,
               info.complete_time                 as complete_time,
               (select string_agg(distinct creator, ',')
                from so_construction_visa
                where tenant_id = #{tenantId}
                  and code = #{constructionCode}) as process_user,
               info.status                        as status
        from so_construction construction
                 left join so_construction_task_info info
                           on construction.code = info.construction_code and construction.tenant_id = info.tenant_id
        where construction.tenant_id = #{tenantId}
          and construction.code = #{constructionCode}
          and info.scope = #{visa}
        union all

        select #{contract}                        as scope,
               construction.code                  as code,
               construction.name                  as name,
               info.create_time                   as start_time,
               info.complete_time                 as complete_time,
               (select string_agg(distinct creator, ',')
                from so_construction_contract
                where tenant_id = #{tenantId}
                  and code = #{constructionCode}) as process_user,
               info.status                        as status
        from so_construction construction
                 left join so_construction_task_info info
                           on construction.code = info.construction_code and construction.tenant_id = info.tenant_id
        where construction.tenant_id = #{tenantId}
          and construction.code = #{constructionCode}
          and info.scope = #{contract}
        union all

        select #{expense}         as scope,
               code               as code,
               name               as name,
               info.create_time   as start_time,
               info.complete_time as complete_time,
               info.creator       as process_user,
               info.status        as status
        from so_construction construction
                 left join so_construction_task_info info
                           on construction.code = info.construction_code and construction.tenant_id = info.tenant_id
        where construction.tenant_id = #{tenantId}
          and construction.code = #{constructionCode}
          and info.scope = #{expense}
        union all

        select #{apply}           as scope,
               code               as code,
               name               as name,
               info.create_time   as start_time,
               info.complete_time as complete_time,
               info.creator       as process_user,
               info.status        as status
        from so_construction construction
                 left join so_construction_task_info info
                           on construction.code = info.construction_code and construction.tenant_id = info.tenant_id
        where construction.tenant_id = #{tenantId}
          and construction.code = #{constructionCode}
          and info.scope = #{apply}
        union all

        select #{accept}          as scope,
               construction.code  as code,
               construction.name  as name,
               info.create_time   as start_time,
               info.complete_time as complete_time,
               info.creator       as process_user,
               info.status        as status
        from so_construction construction
                 left join so_construction_accept main
                           on main.construction_code = construction.code
                               and main.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = main.construction_code
                               and info.tenant_id = main.tenant_id
        where main.tenant_id = #{tenantId}
          and main.construction_code = #{constructionCode}
          and info.scope = #{accept}
        union all

        select #{settlement}      as scope,
               construction.code  as code,
               construction.name  as name,
               info.create_time   as start_time,
               info.complete_time as complete_time,
               info.creator       as process_user,
               info.status        as status
        from so_construction construction
                 left join so_construction_settlement main
                           on main.construction_code = construction.code
                               and main.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = main.construction_code
                               and info.tenant_id = main.tenant_id
        where main.tenant_id = #{tenantId}
          and main.construction_code = #{constructionCode}
          and info.scope = #{settlement}
    </select>

    <select id="getAllCodeByProject" resultType="java.lang.String">
        select code
        from so_construction
        where project_code = #{projectCode}
          and tenant_id = #{tenantId}
    </select>

    <select id="getCodeById" resultType="java.lang.String">
        select code
        from so_construction
        where id = #{id}
    </select>

    <select id="getByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_project project
                           on construction.project_code = project.code
                               and construction.tenant_id = project.tenant_id
        where construction.code = #{constuctionCode}
          and construction.tenant_id = #{tenantId}
    </select>

    <resultMap id="SoConstructionCompositeProjectResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionCompositeProject">
        <association property="project" javaType="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject">
            <id column="poj_id" property="id"/>
            <result column="poj_code" property="code"/>
            <result column="poj_name" property="name"/>
            <result column="poj_scale" property="scale"/>
            <result column="poj_phone" property="phone"/>
            <result column="poj_type_id" property="typeId"/>
            <result column="poj_remark" property="remark"/>
            <result column="poj_address" property="address"/>
            <result column="poj_creator" property="creator"/>
            <result column="poj_estimate" property="estimate"/>
            <result column="poj_tenant_id" property="tenantId"/>
            <result column="poj_principal" property="principal"/>
            <result column="poj_start_time" property="startTime"/>
            <result column="poj_create_time" property="createTime"/>
            <result column="poj_update_time" property="updateTime"/>
            <result column="poj_update_user" property="updateUser"/>
            <result column="poj_attachments" property="attachments"/>
            <result column="poj_organization" property="organization"/>
            <result column="poj_expect_end_time" property="expectEndTime"/>
        </association>
        <association property="construction"
                     javaType="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction">
            <id column="cons_id" property="id"/>
            <result column="cons_code" property="code"/>
            <result column="cons_name" property="name"/>
            <result column="cons_type_id" property="typeId"/>
            <result column="cons_type_name" property="typeName"/>
            <result column="cons_remark" property="remark"/>
            <result column="cons_address" property="address"/>
            <result column="cons_creator" property="creator"/>
            <result column="cons_estimate" property="estimate"/>
            <result column="cons_tenant_id" property="tenantId"/>
            <result column="cons_create_time" property="createTime"/>
            <result column="cons_update_time" property="updateTime"/>
            <result column="cons_update_user" property="updateUser"/>
            <result column="cons_project_code" property="projectCode"/>
            <result column="cons_attachments" property="attachments"/>
            <result column="cons_firstpart_name" property="firstpartName"/>
            <result column="cons_detail_address" property="detailAddress"/>
            <result column="cons_firstpart_phone" property="firstpartPhone"/>
        </association>
    </resultMap>
    <select id="getCompositeByConstructionCode"
            resultMap="SoConstructionCompositeProjectResultMap">
        select construction.id                                                       cons_id,
               construction.address                                                  cons_address,
               construction.attachments                                              cons_attachments,
               construction.code                                                     cons_code,
               construction.create_time                                              cons_create_time,
               construction.creator                                                  cons_creator,
               construction.detail_address                                           cons_detail_address,
               construction.estimate                                                 cons_estimate,
               construction.firstpart_name                                           cons_firstpart_name,
               construction.firstpart_phone                                          cons_firstpart_phone,
               construction.name                                                     cons_name,
               construction.project_code                                             cons_project_code,
               construction.remark                                                   cons_remark,
               construction.tenant_id                                                cons_tenant_id,
               construction.type_id                                                  cons_type_id,
               (select name from so_general_type where id = construction.type_id) as cons_type_name,
               construction.update_time                                              cons_update_time,
               construction.update_user                                              cons_update_user,

               project.id                                                            poj_id,
               project.address                                                       poj_address,
               project.attachments                                                   poj_attachments,
               project.code                                                          poj_code,
               project.create_time                                                   poj_create_time,
               project.creator                                                       poj_creator,
               project.estimate                                                      poj_estimate,
               project.expect_end_time                                               poj_expect_end_time,
               project.name                                                          poj_name,
               project.organization                                                  poj_organization,
               project.phone                                                         poj_phone,
               project.principal                                                     poj_principal,
               project.remark                                                        poj_remark,
               project.scale                                                         poj_scale,
               project.start_time                                                    poj_start_time,
               project.tenant_id                                                     poj_tenant_id,
               project.type_id                                                       poj_type_id,
               project.update_time                                                   poj_update_time,
               project.update_user                                                   poj_update_user
        from so_construction construction
                 join so_project project
                      on construction.project_code = project.code and construction.tenant_id = project.tenant_id
        where construction.code = #{constructionCode}
          and construction.tenant_id = #{tenantId}
    </select>

    <select id="canBeDelete" resultType="boolean">
        <bind name="projectScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_PROJECT"/>
        select
        <!--无相关流程-->
        (select count(1) = 0
         from so_construction_task_info
         where construction_code = construction.code)
            and
        <!--无关联设备项-->
        (select count(1) = 0
         from so_device_item item
         where item.scope = #{constructionScope}
           and item.identifier = construction.code
           and construction.tenant_id = item.tenant_id)
        from so_construction construction
        where id = #{id}
    </select>
</mapper>