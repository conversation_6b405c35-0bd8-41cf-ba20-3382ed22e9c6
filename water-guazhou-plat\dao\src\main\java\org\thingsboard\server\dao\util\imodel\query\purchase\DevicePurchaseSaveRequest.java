package org.thingsboard.server.dao.util.imodel.query.purchase;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchase;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DevicePurchaseSaveRequest extends ComplexSaveRequest<DevicePurchase, DevicePurchaseItemSaveRequest> {
    // 采购单编码
    private String code;

    // 采购单标题
    @NotNullOrEmpty
    private String title;

    // 采购人
    @NotNullOrEmpty
    private String userId;

    // 使用方式/用途
    private String useWay;

    // 申请时间

    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;

    // 预计采购日期
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preTime;

    // 预算

    @NotNullOrEmpty
    private Double budget;

    // 是否为补录
    private Boolean addRecord;


    @Override
    protected DevicePurchase build() {
        DevicePurchase devicePurchase = new DevicePurchase();
        devicePurchase.setTenantId(tenantId());
        devicePurchase.setCreator(currentUserUUID());
        devicePurchase.setCreateTime(new Date());
        commonSet(devicePurchase);
        return devicePurchase;
    }

    @Override
    protected DevicePurchase update(String id) {
        DevicePurchase devicePurchase = new DevicePurchase();
        devicePurchase.setId(id);
        commonSet(devicePurchase);
        return devicePurchase;
    }

    private void commonSet(DevicePurchase entity) {
        entity.setCode(code);
        entity.setTitle(title);
        entity.setUserId(userId);
        entity.setUseWay(useWay);
        entity.setUploadTime(uploadTime);
        entity.setPreTime(preTime);
        entity.setBudget(budget);
        entity.setAddRecord(addRecord);
    }

    @Override
    protected StringSetter<DevicePurchaseItemSaveRequest> parentSetter() {
        return DevicePurchaseItemSaveRequest::setMainId;
    }
}
