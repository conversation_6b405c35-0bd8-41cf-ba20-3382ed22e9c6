import{_ as x}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as L,c as h,r as d,b as n,S as P,o as k,g as v,n as I,q as m,i as f,aB as q,aq as S,C}from"./index-r0dFAfgr.js";import{_ as F}from"./Search-NSrhrIa_.js";const M=r=>g({url:"/api/spp/dma/partition/lossPoint/list",method:"get",params:r}),B=r=>g({url:"/api/spp/dma/partition/lossPoint",method:"post",data:r}),N=r=>g({url:"/api/spp/dma/partition/lossPoint",method:"delete",data:r}),z=L({__name:"MoreDetail_LDJL",props:{partition:{}},setup(r){const _=r,p=h(),u=h(),b=d({filters:[{type:"date",label:"日期",field:"findDate"}],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:search",text:"查询",type:"primary",click:()=>s()},{perm:!0,iconifyIcon:"ep:refresh",text:"重置",type:"default",click:()=>{var e;(e=p.value)==null||e.resetForm()}},{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>y()}]}]}),o=d({dataList:[],columns:[{label:"分区名称",prop:"partitionName"},{label:"分区状态",prop:"statusName"},{label:"漏点日期",prop:"findDate"},{label:"漏点数量",prop:"num"}],pagination:{refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,s()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>y(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>D(e)}]}),s=async()=>{var e,t;o.loading=!0;try{const a=((e=p.value)==null?void 0:e.queryParams)||{},l=(await M({...a,partitionId:(t=_.partition)==null?void 0:t.value,page:o.pagination.page||1,size:o.pagination.limit||20})).data.data||{};o.dataList=l.data||[],o.pagination.total=l.total||0}catch{}o.loading=!1},y=e=>{var t;c.defaultValue={...e||{}},c.title=e?"编辑漏点":"添加漏点",(t=u.value)==null||t.openDialog()},D=e=>{const t=e?[e.id]:[];if(!t.length){n.error("请选择要删除的数据");return}P("确定删除?","提示信息").then(async()=>{try{const a=await N(t);a.data.code===200?(n.success("删除成功"),s()):n.error(a.data.message)}catch{n.error("删除失败")}}).catch(()=>{})},c=d({dialogWidth:600,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"date",label:"漏点日期",field:"findDate",rules:[{required:!0,message:"请选择漏点日期"}]},{lg:24,xl:12,type:"input-number",label:"漏点数量",field:"num",rules:[{required:!0,message:"请输入漏点数量"}]}]}],submit:async e=>{var t,a;c.submitting=!0;try{const i=await B({...e,partitionId:(t=_.partition)==null?void 0:t.value});i.data.code===200?(n.success("提交成功"),s(),(a=u.value)==null||a.closeDialog()):n.error(i.data.message)}catch{n.error("提交失败")}c.submitting=!1}});return k(()=>{s()}),(e,t)=>{const a=F,i=S,l=x;return v(),I(q,null,[m(a,{ref_key:"refSearch",ref:p,config:f(b),class:"search"},null,8,["config"]),m(i,{config:f(o),class:"table-box"},null,8,["config"]),m(l,{ref_key:"refDialog",ref:u,config:f(c)},null,8,["config"])],64)}}}),V=C(z,[["__scopeId","data-v-b1c8a19a"]]);export{V as default};
