package org.thingsboard.server.dao.util.imodel.query;

import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;

import java.util.EnumSet;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.APPROVED;
import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.RECEIVED;

public class GeneralTaskPageableQueryEntity<T, S extends AdvancedPageableQueryEntity<T, S>> extends AdvancedPageableQueryEntity<T, S> {

    public GeneralTaskStatus getActualCompleteStatus() {
        return APPROVED;
    }

    public EnumSet<GeneralTaskStatus> getAtAssignedStatus() {
        return EnumSet.of(RECEIVED, APPROVED);
    }
}
