import { request } from '@/plugins/axios'

/**
 * 查询泵站列表
 * @param params
 * @returns
 */
export const GetDmaPartitionCustMeter = (
  params: IQueryPagerParams & {
    caliber?: string
    partitionId?: string
    brand?: string
    type?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/custMeter/list',
    method: 'get',
    params
  })
}
/**
 * 添加户表
 * @param params
 * @returns
 */
export const AddDmaPartitionCustMeter = (params: {
  partitionId: string
  caliber: string
  num: number
  brand: string
  type: string
  isCollectCopy: string
  remark: string
  img: string
}) => {
  return request({
    url: '/api/spp/dma/partition/custMeter',
    method: 'post',
    data: params
  })
}
/**
 * 删除户表
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionCustMeter = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/custMeter',
    method: 'delete',
    data: ids
  })
}
