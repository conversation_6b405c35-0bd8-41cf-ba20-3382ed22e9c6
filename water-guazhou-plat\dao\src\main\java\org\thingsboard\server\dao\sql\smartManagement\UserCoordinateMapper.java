package org.thingsboard.server.dao.sql.smartManagement;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinateGroup;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.DestUserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinatePageRequest;

import java.util.List;

@Mapper
public interface UserCoordinateMapper extends BaseMapper<UserCoordinate> {
    IPage<UserCoordinate> findDestUserByPageConditional(DestUserCoordinatePageRequest request);

    IPage<UserCoordinateGroup> findByPage(UserCoordinatePageRequest request);

    boolean update(UserCoordinate entity);

    IPage<UserCoordinate> findNewest(UserCoordinatePageRequest request);

}
