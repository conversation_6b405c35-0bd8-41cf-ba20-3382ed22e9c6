package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/3/27 13:37
 */
@Data
@Entity
@Table(name = ModelConstants.BULLETIN_TABLE)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class BulletinDataEntity {


    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 数据源ID
     */
    @Column(name = ModelConstants.BULLETIN_TITLE)
    private String title;

    /**
     * 数值
     */
    @Column(name = ModelConstants.BULLETIN_BODY)
    private String body;

    /**
     * 数据更新时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private long updateTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
