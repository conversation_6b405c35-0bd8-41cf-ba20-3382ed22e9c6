import{_ as G}from"./index-C9hz-UZb.js";import{d as H,r as C,bF as c,c as k,am as j,bB as P,a8 as J,bX as K,s as N,b as U,o as Q,ah as X,bA as Z,ay as ee,g as Y,n as B,q as y,i as l,F as D,cs as I,bo as M,bR as A,p as _,bh as F,j as te,aB as ae,aJ as se,h as re,G as oe,dF as ne,dA as le,J as ie,al as ce,aj as de,bD as ue,C as pe}from"./index-r0dFAfgr.js";import{_ as me}from"./CardTable-rdWOL4_6.js";import{_ as fe}from"./CardSearch-CB_HNR-Q.js";import{c as ye}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as he}from"./useStation-DJgnSZIA.js";import{p as _e}from"./printUtils-C-AxhDcd.js";import{r as be}from"./data-D3PIONJl.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const ve={class:"wrapper"},ge={class:"content-container"},De={class:"content-container"},xe={class:"chart-layout"},Se={class:"chart-container"},Ce={class:"chart-title"},Pe={class:"date-selector"},Te={class:"date-buttons"},Le=H({__name:"index",setup(ke){const{getStationTree:O}=he(),t=C({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,availableDates:[],selectedDate:""}),q=c().date(),w=k(),v=k(),x=k();j(()=>t.activeName,()=>{t.activeName==="echarts"&&P(()=>{setTimeout(()=>{S()},100)})}),j(()=>{var e,a;return(a=(e=v.value)==null?void 0:e.queryParams)==null?void 0:a.type},e=>{e&&t.availableDates.length>0&&z(e)});const f=C({data:[],currentProject:{}}),T=C({defaultParams:{type:"day",year:[c().format(),c().format()],month:[c().format(),c().format()],day:[c().date(q-6).format("YYYY-MM-DD"),c().date(q).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:J(()=>f.data),label:"站点选择",onChange:e=>{const a=K(f.data,"children","id",e);f.currentProject=a,t.treeDataType=a.data.type,t.treeDataType==="Station"&&(t.stationId=a.id,L())}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{hidden:!0,type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.type==="month"||e.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>L(),svgIcon:N(ce)},{text:"导出",perm:!0,type:"warning",svgIcon:N(de),click:()=>W()},{perm:!0,text:"打印",type:"success",svgIcon:N(ue),click:()=>E()}]}]}),d=C({loading:!1,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),L=()=>{var p;d.loading=!0;const e=f.currentProject.id,a=((p=v.value)==null?void 0:p.queryParams)||{},o=a[a.type],n=be.find(i=>i.value===a.type);t.title=f.currentProject.label+"单耗报表("+n.label+c(o[0]).format(n.data)+"至"+c(o[1]).format(n.data)+")";const u={stationId:e,queryType:a.type,start:c(o[0]).startOf(a.type).valueOf(),end:c(o[1]).endOf(a.type).valueOf()};ye(u).then(i=>{var V;const m=i.data.data,b=(V=m.tableInfo)==null?void 0:V.map(h=>({prop:h.columnValue,label:h.columnName,unit:h.unit?"("+h.unit+")":""}));d.columns=b,d.dataList=m.tableDataList,d.loading=!1;const r=b.filter(h=>{const g=h.prop;return g!=="ts"&&g!=="min"&&g!=="max"&&g!=="avg"&&g!=="total"&&g.includes("2025-")});t.availableDates=r.map(h=>h.prop);const s=a.type||"day";z(s),t.activeName==="echarts"&&P(()=>{setTimeout(()=>{S()},200)})}).catch(i=>{console.error("获取数据失败:",i),d.loading=!1,U.error("获取数据失败，请稍后重试")})},W=()=>{var e;(e=w.value)==null||e.exportTable()},E=()=>{_e({title:t.title,data:d.dataList,titleList:d.columns})},S=()=>{var b;if(!d.dataList||d.dataList.length===0||!t.selectedDate)return;const e=d.dataList,o=(((b=v.value)==null?void 0:b.queryParams)||{}).type||"day";let n=[],u=[],p="";if(o==="day"?(n=e.filter(r=>{const s=r.ts;return s&&s.includes("时")&&!s.includes("最")&&!s.includes("平均")&&!s.includes("合计")}),u=n.map(r=>r.ts),p="时间"):o==="month"?(n=e.filter(r=>{const s=r.ts;return s&&s.includes("日")&&!s.includes("最")&&!s.includes("平均")&&!s.includes("合计")}),u=n.map(r=>r.ts),p="日期"):o==="year"&&(n=e.filter(r=>{const s=r.ts;return s&&s.includes("月")&&!s.includes("最")&&!s.includes("平均")&&!s.includes("合计")}),u=n.map(r=>r.ts),p="月份"),n.length===0)return;const i=n.map(r=>{const s=r[t.selectedDate];return typeof s=="number"?s:parseFloat(s)||0}),m={title:{left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(r){const s=r[0];return`${s.axisValue}<br/>${s.marker} 单耗: ${s.value} KWh/10³m³`}},grid:{left:"8%",right:"8%",bottom:"10%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:u,name:p,nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,rotate:o==="day"?0:45}},yAxis:{type:"value",name:"单耗值 (KWh/10³m³)",nameTextStyle:{fontSize:12},axisLabel:{fontSize:11,formatter:"{value}"}},series:[{name:"单耗",type:"line",data:i,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3,color:"#5470C6"},itemStyle:{color:"#5470C6"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(84, 112, 198, 0.3)"},{offset:1,color:"rgba(84, 112, 198, 0.1)"}]}}}]};t.chartOption=m,P(()=>{setTimeout(()=>{var r;(r=x.value)==null||r.resize()},200)})},R=e=>{t.selectedDate=e,S()},z=e=>{var u;if(t.availableDates.length===0)return;const o=(((u=v.value)==null?void 0:u.queryParams)||{})[e];if(!o||!o[0]){t.selectedDate=t.availableDates[0];return}let n="";if(e==="day")n=t.availableDates[0];else if(e==="month"){const i=c(o[0]).format("YYYY-MM");n=t.availableDates.find(m=>m.startsWith(i))||t.availableDates[0]}else if(e==="year"){const i=c(o[0]).format("YYYY");n=t.availableDates.find(m=>m.startsWith(i))||t.availableDates[0]}t.selectedDate=n,t.activeName==="echarts"&&P(()=>{setTimeout(()=>{S()},100)})},$=e=>{const a=e.split("-");return a.length===3?`${a[1]}-${a[2]}`:e};return Q(async()=>{var a;const e=await O("水源地");f.data=e,f.currentProject=X(f.data),T.defaultParams={...T.defaultParams,treeData:f.currentProject},(a=v.value)==null||a.resetForm(),L(),window.addEventListener("resize",()=>{setTimeout(()=>{var o;(o=x.value)==null||o.resize()},100)})}),Z(()=>{window.removeEventListener("resize",()=>{var e;(e=x.value)==null||e.resize()})}),(e,a)=>{const o=fe,n=ne,u=le,p=me,i=ee("VChart"),m=ie,b=G;return Y(),B("div",ve,[y(o,{ref_key:"cardSearch",ref:v,config:l(T)},null,8,["config"]),y(b,{class:"card",title:l(t).activeName==="list"?"单耗报表":"单耗趋势图表"},{right:D(()=>[y(u,{modelValue:l(t).activeName,"onUpdate:modelValue":a[0]||(a[0]=r=>l(t).activeName=r)},{default:D(()=>[y(n,{label:"echarts"},{default:D(()=>[y(l(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),y(n,{label:"list"},{default:D(()=>[y(l(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:D(()=>[M(_("div",ge,[y(p,{id:"print",ref_key:"refTable",ref:w,class:"card-table",config:l(d)},null,8,["config"])],512),[[A,l(t).activeName==="list"]]),M(_("div",De,[_("div",xe,[_("div",Se,[_("h4",Ce,F(l(t).selectedDate)+"单耗趋势图表",1),y(i,{ref_key:"refChart",ref:x,theme:l(te)().isDark?"dark":"light",option:l(t).chartOption,class:"line-chart"},null,8,["theme","option"])]),_("div",Pe,[a[1]||(a[1]=_("div",{class:"selector-title"},"选择日期",-1)),_("div",Te,[(Y(!0),B(ae,null,se(l(t).availableDates,r=>(Y(),re(m,{key:r,type:l(t).selectedDate===r?"primary":"default",size:"small",onClick:s=>R(r),class:"date-btn"},{default:D(()=>[oe(F($(r)),1)]),_:2},1032,["type","onClick"]))),128))])])])],512),[[A,l(t).activeName==="echarts"]])]),_:1},8,["title"])])}}}),Ae=pe(Le,[["__scopeId","data-v-a7c0045f"]]);export{Ae as default};
