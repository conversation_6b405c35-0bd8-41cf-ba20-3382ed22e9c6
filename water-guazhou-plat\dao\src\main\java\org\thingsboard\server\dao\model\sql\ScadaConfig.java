package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * 大屏配置
 */
@Data
@Entity
@Table(name = ModelConstants.SCADA_CONFIG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ScadaConfig {


    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SCADA_CONFIG_CODE)
    private String code;

    @Column(name = ModelConstants.SCADA_CONFIG_JSON_DATA)
    private String jsonData;

    @Column(name = ModelConstants.SCADA_CONFIG_TYPE)
    private String type;

}