/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.virtual;

import lombok.Data;
import org.thingsboard.server.common.data.HasName;
import org.thingsboard.server.common.data.HasTenantId;
import org.thingsboard.server.common.data.SearchTextBasedWithAdditionalInfo;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.VirtualId;

@Data
public class Virtual extends SearchTextBasedWithAdditionalInfo<VirtualId> implements HasTenantId, HasName {


    private TenantId tenantId;

    private String name;
    //公式
    private String formula;
    //单位
    private String type;
    //group
    private String virtualGroup;

    private String unit;

    //序列号
    private String serialNumber;


    private VirtualType virtualType;

    private String projectId;

    public Virtual() {
    }

    public Virtual(Virtual virtual){
        super(virtual);
        this.name=virtual.getName();
        this.formula=virtual.getFormula();
        this.type=virtual.getType();
        this.unit=virtual.getUnit();
        this.virtualGroup=virtual.getVirtualGroup();
        this.tenantId=virtual.getTenantId();
    }


    public VirtualType getVirtualType() {
        switch (type) {
            case "inputValue":
                return VirtualType.INPUT_VALUE;
            case "calculation":
                return VirtualType.CALCULATION_VALUE;
            default:
                return null;
        }
    }

    public Virtual(VirtualId virtualId) {
        super(virtualId);
    }


    @Override
    public String getSearchText() {
        return name;
    }


    @Override
    public String getName() {
        return name;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
