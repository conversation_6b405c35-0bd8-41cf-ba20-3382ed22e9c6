package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecord;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordSaveRequest;

public interface StoreOutRecordService {
    /**
     * 分页条件查询出库单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<StoreOutRecord> findAllConditional(StoreOutRecordPageRequest request);

    /**
     * 保存出库单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    StoreOutRecord save(StoreOutRecordSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(StoreOutRecord entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
