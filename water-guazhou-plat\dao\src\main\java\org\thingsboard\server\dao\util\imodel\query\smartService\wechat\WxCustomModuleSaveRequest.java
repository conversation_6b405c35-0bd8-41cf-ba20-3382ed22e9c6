package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxCustomModule;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxCustomModuleSaveRequest extends SaveRequest<WxCustomModule> {
    // icon地址
    private String icon;

    // 模块名
    private String name;

    // 简介
    private String introduction;

    // 模块地址
    private String address;

    // 是否启用模块
    private Boolean isEnabled;

    // 是否启用操作指南
    private Boolean guideMode;

    // 渲染模式。VUE/IFRAME
    private String mode;

    // 页面指南标题
    private String guideTitle;

    // 指南类型
    private String guideType;

    // 指南详细信息数据
    private String guideDescription;

    // 是否在顶部显示
    private Boolean atTop;

    // 是否在快捷栏显示
    private Boolean isPick;

    // 是否在推荐栏显示
    private Boolean isRecommend;

    // 在快捷栏中的顺序编号
    private Integer orderNum;

    @Override
    protected WxCustomModule build() {
        WxCustomModule entity = new WxCustomModule();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxCustomModule update(String id) {
        WxCustomModule entity = new WxCustomModule();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxCustomModule entity) {
        entity.setIcon(icon);
        entity.setName(name);
        entity.setAddress(address);
        entity.setIsEnabled(isEnabled);
        entity.setGuideMode(guideMode);
        entity.setMode(mode);
        entity.setGuideTitle(guideTitle);
        entity.setGuideType(guideType);
        entity.setGuideDescription(guideDescription);
        entity.setAtTop(atTop);
        entity.setIsPick(isPick);
        entity.setIsRecommend(isRecommend);
        entity.setOrderNum(orderNum);
        entity.setIntroduction(introduction);
    }
}