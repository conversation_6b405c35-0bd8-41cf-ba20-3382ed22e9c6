import{m as t}from"./index-r0dFAfgr.js";function a(e){return t({url:"/api/sp/pumpHouseStorage",method:"get",params:e})}function u(e){return t({url:"/api/sp/pumpHouseStorage",method:"post",data:e})}function o(e){return t({url:`/api/sp/pumpHouseStorage/${e.id}`,method:"patch",data:e})}function r(e){return t({url:`/api/sp/pumpHouseStorage/${e}`,method:"delete"})}function n(e){return t({url:"/api/sp/pumpHouseStorage/batch",method:"post",data:e})}function s(){return t({url:"/api/sp/pumpHouseStorage/excel/template",responseType:"blob",method:"get"})}function i(e){return t({url:"/api/sp/pumpHouseStorage/excel/export",method:"get",responseType:"blob",params:e})}function m(e){return t({url:"/api/sp/pumpManage",method:"get",params:e})}function d(e){return t({url:"/api/sp/pumpManage",method:"post",data:e})}function g(e){return t({url:`/api/sp/pumpManage/${e.id}`,method:"patch",data:e})}function l(e){return t({url:`/api/sp/pumpManage/${e}`,method:"delete"})}function c(e){return t({url:"/api/sp/pumpManage/batch",method:"post",data:e})}function h(){return t({url:"/api/sp/pumpManage/excel/template",responseType:"blob",method:"get"})}function f(e){return t({url:"/api/sp/pumpManage/excel/export",method:"get",responseType:"blob",params:e})}function H(e){return t({url:"/api/fileRegistry",method:"get",params:e})}function M(e){return t({url:"/api/fileRegistry",method:"post",data:e})}function S(e){return t({url:`/api/fileRegistry/${e}`,method:"delete"})}export{u as a,n as b,i as c,S as d,o as e,r as f,H as g,a as h,s as i,c as j,f as k,d as l,g as m,l as n,h as o,m as p,M as s};
