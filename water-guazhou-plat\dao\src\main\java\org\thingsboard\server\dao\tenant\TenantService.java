/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.tenant;

import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.security.Authority;

import java.util.List;

public interface TenantService {

    Tenant findTenantById(TenantId tenantId);

    ListenableFuture<Tenant> findTenantByIdAsync(TenantId callerId, TenantId tenantId);

    ListenableFuture<Tenant> findTenantByIdAsync(TenantId tenantId);
    
    Tenant saveTenant(Tenant tenant) ;
    
    void deleteTenant(TenantId tenantId);
    
    TextPageData<Tenant> findTenants(TextPageLink pageLink);

    List<Tenant> findAll();
    
    void deleteTenants();

    List<Tenant> findByAppTypeId(String id);

    Object getAllInfo();

    List<Tenant> getCurrentTenantList(Authority authority, TenantId tenantId, UserId userId);

    void saveUserTenant(User saveUser, Tenant tenant);

    JSONObject getOtherTenantList(String userId);

    void setTenantToUser(String toUserId, List<String> tenantIdList);

    void createDefaultApplicationAndMenu(TenantId id);
}
