<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="143454f2-f002-4f9c-9c7f-b3073c35c581" name="Changes" comment="维修总览和事件总览" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="FxmlFile" />
        <option value="Enum" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="liutong" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/DeviceTypeController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/shuiwu/IStarCommonController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/waterSource/inspection/CircuitConfigController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/NewlyWorkOrderController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/WorkOrderEmergencyLevelController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/WorkOrderProcessLevelController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/WorkOrderTypeController.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2v4uVyqaLHI8jeeXXux4cEVwQZf" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ApiPost:METDOD_SEND_RECORD:application": "{\"/api/spp/dma/partition/monitor\":[{\"url\":\"http://localhost:8080/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":6.0,\"second\":15.0,\"nano\":4.860454E8}}},{\"url\":\"http://localhost:8764/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8764/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":6.0,\"second\":48.0,\"nano\":9.56709E7}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:07:28 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:07:28 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":7.0,\"second\":28.0,\"nano\":5.711763E8}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:08:17 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:08:17 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":8.0,\"second\":17.0,\"nano\":4.711634E8}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:12:05 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:12:05 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":12.0,\"second\":5.0,\"nano\":3.091777E8}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:19:27 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:19:27 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":19.0,\"second\":27.0,\"nano\":4.641812E8}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:20:31 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:20:31 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":20.0,\"second\":31.0,\"nano\":9.073006E8}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:22:31 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:22:32 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":22.0,\"second\":32.0,\"nano\":3.36282E7}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:23:26 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:23:26 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":23.0,\"second\":26.0,\"nano\":3.659017E8}}},{\"url\":\"http://localhost:8765/api/spp/dma/partition/monitor\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"partitionId\",\"value\":\"cd9eaf5bdeb79ecb9af409122f6e3483\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:26:30 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1681\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Tue, 01 Apr 2025 10:26:30 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":1.0},\"time\":{\"hour\":18.0,\"minute\":26.0,\"second\":30.0,\"nano\":4.323167E8}}}],\"/api/assay/\":[{\"url\":\"http://localhost:8080/api/assay/\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8080/api/assay/\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":10.0,\"second\":47.0,\"nano\":5.282469E8}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1744013457605}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Mon, 07 Apr 2025 08:10:57 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":10.0,\"second\":57.0,\"nano\":6.245635E8}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[{\"key\":\"authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1744013486535}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Mon, 07 Apr 2025 08:11:26 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":11.0,\"second\":26.0,\"nano\":5.488352E8}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:11:39 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 400\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1394\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Mon, 07 Apr 2025 08:11:39 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":11.0,\"second\":39.0,\"nano\":2.777018E8}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:11:52 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 400\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1394\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Mon, 07 Apr 2025 08:11:52 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":11.0,\"second\":52.0,\"nano\":2.444029E8}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:15:31 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 400\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1400\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Mon, 07 Apr 2025 08:15:31 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":15.0,\"second\":31.0,\"nano\":3.33629E8}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 16:10:04\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:18:28 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 400\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"1400\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Mon, 07 Apr 2025 08:18:28 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":18.0,\"second\":28.0,\"nano\":7.4076E7}}},{\"url\":\"http://localhost:8765/api/assay/\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"123456\\\",\\n    \\\"creator\\\": \\\"123456\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"111\\\",\\n    \\\"testingUnit\\\": \\\"111\\\",\\n    \\\"testResults\\\": \\\"合格\\\",\\n    \\\"testDate\\\": \\\"2025-04-07\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 16:10:04\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":\\\"\\\",\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744015459776,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:44:19 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":44.0,\"second\":19.0,\"nano\":8.313248E8}}},{\"url\":\"http://localhost:8080/api/assay/\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"json\",\"parameter\":[],\"raw\":\"{\\n    \\\"id\\\": \\\"\\\",\\n    \\\"tenantId\\\": \\\"\\\",\\n    \\\"creator\\\": \\\"\\\",\\n    \\\"reportName\\\": \\\"\\\",\\n    \\\"samplingLocation\\\": \\\"\\\",\\n    \\\"testingUnit\\\": \\\"\\\",\\n    \\\"testResults\\\": \\\"\\\",\\n    \\\"testDate\\\": \\\"2025-04-07 17:48:57\\\",\\n    \\\"reportFile\\\": \\\"\\\",\\n    \\\"createTime\\\": \\\"2025-04-07 17:48:57\\\",\\n    \\\"updateTime\\\": \\\"2025-04-07 17:48:57\\\",\\n    \\\"remark\\\": \\\"\\\"\\n}\",\"raw_para\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"主键ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"租户ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"创建人ID\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportName\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告名称\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"samplingLocation\",\"value\":\"\",\"type\":\"Text\",\"description\":\"采样地点\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testingUnit\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测单位\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testResults\",\"value\":\"\",\"type\":\"Text\",\"description\":\"检测结果\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"testDate\",\"value\":\"2025-04-07 17:48:57\",\"type\":\"Text\",\"description\":\"检测日期\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"reportFile\",\"value\":\"\",\"type\":\"Text\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTime\",\"value\":\"2025-04-07 17:48:57\",\"type\":\"Text\",\"description\":\"创建时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"updateTime\",\"value\":\"2025-04-07 17:48:57\",\"type\":\"Text\",\"description\":\"更新时间\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"remark\",\"value\":\"\",\"type\":\"Text\",\"description\":\"备注\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}]},\"responseBody\":\"Fail to send:http://localhost:8080/api/assay/\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":49.0,\"second\":6.0,\"nano\":5.18818E8}}}],\"/api/assay/{id}/upload\":[{\"url\":\"http://localhost:8080/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":45.0,\"second\":39.0,\"nano\":2.857617E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1744015548548}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Mon, 07 Apr 2025 08:45:48 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":45.0,\"second\":48.0,\"nano\":5.586466E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":null,\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744015581387,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:46:21 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":46.0,\"second\":21.0,\"nano\":4.256226E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":null,\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744015582280,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:46:21 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":46.0,\"second\":22.0,\"nano\":3.087135E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":49.0,\"second\":0.0,\"nano\":4.986993E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":null,\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744015778287,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:49:38 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":49.0,\"second\":38.0,\"nano\":4.32405E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":null,\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744015819023,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:50:19 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":50.0,\"second\":19.0,\"nano\":7.69261E7}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":null,\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744015854397,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:50:54 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":50.0,\"second\":54.0,\"nano\":4.337647E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":\\\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\\\",\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744016031302,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:53:51 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":53.0,\"second\":51.0,\"nano\":3.652453E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":\\\"http://***********:9090/istar/cb6aad30-ba34-4165-9f95-9c3419e861f5\\\",\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744016039013,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:53:59 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":53.0,\"second\":59.0,\"nano\":5.82862E7}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":\\\"http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\\\",\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744016268102,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 08:57:48 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":16.0,\"minute\":57.0,\"second\":48.0,\"nano\":1.919829E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1744073823786}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Tue, 08 Apr 2025 00:57:03 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":8.0},\"time\":{\"hour\":8.0,\"minute\":57.0,\"second\":3.0,\"nano\":7.961515E8}}},{\"url\":\"http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[{\"key\":\"id\",\"value\":\"\",\"type\":\"Text\",\"description\":\"化验记录ID\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"String\"}],\"cookie\":[],\"body\":{\"mode\":\"form-data\",\"parameter\":[{\"key\":\"file\",\"value\":\"C:/Users/<USER>/Desktop/壁纸/1.jpg\",\"type\":\"File\",\"description\":\"报告文件\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"File\"}],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":\\\"http://***********:9090/istar/95b93961-8265-449d-badd-20c56231e381\\\",\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744073851338,\\\"remark\\\":\\\"\\\"}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Tue, 08 Apr 2025 00:57:31 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"POST\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":8.0},\"time\":{\"hour\":8.0,\"minute\":57.0,\"second\":31.0,\"nano\":3.767485E8}}}],\"/api/assay/test\":[{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:36:02 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":36.0,\"second\":2.0,\"nano\":3.230354E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:36:07 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":36.0,\"second\":7.0,\"nano\":6.00545E7}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:40:56 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":40.0,\"second\":56.0,\"nano\":9.973964E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:41:39 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":41.0,\"second\":39.0,\"nano\":4.478667E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:41:58 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":41.0,\"second\":58.0,\"nano\":9.4836E7}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:41:59 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":41.0,\"second\":59.0,\"nano\":6.096745E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:42:01 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":42.0,\"second\":1.0,\"nano\":4.821336E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:46:04 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":46.0,\"second\":4.0,\"nano\":6.868472E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:46:06 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":46.0,\"second\":6.0,\"nano\":7.244202E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:46:07 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":46.0,\"second\":7.0,\"nano\":7.937135E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:46:23 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":46.0,\"second\":23.0,\"nano\":4.453793E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:46:24 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":46.0,\"second\":24.0,\"nano\":5.790009E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:48:08 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":48.0,\"second\":8.0,\"nano\":7.498019E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:48:09 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":48.0,\"second\":9.0,\"nano\":7.780212E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:48:13 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":48.0,\"second\":13.0,\"nano\":2.634036E8}}},{\"url\":\"http://localhost:8080/api/assay/test\",\"header\":[],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/api/assay/test?name\\u003dhttp://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":49.0,\"second\":37.0,\"nano\":7.887934E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/test?name\\u003dhttp://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1744019382869}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Mon, 07 Apr 2025 09:49:42 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":49.0,\"second\":42.0,\"nano\":8.862905E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:50:06 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":50.0,\"second\":6.0,\"nano\":8.781588E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/cb6aad30-ba34-4165-9f95-9c3419e861f5\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 07 Apr 2025 09:50:37 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":7.0},\"time\":{\"hour\":17.0,\"minute\":50.0,\"second\":37.0,\"nano\":9.245975E8}}},{\"url\":\"http://localhost:8765/api/assay/test\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"name\",\"value\":\"http://***********:9090/istar/cb6aad30-ba34-4165-9f95-9c3419e861f5\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":null}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Tue, 08 Apr 2025 00:38:33 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"DELETE\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":8.0},\"time\":{\"hour\":8.0,\"minute\":38.0,\"second\":33.0,\"nano\":9.859309E8}}}],\"/api/assay/list\":[{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/list?KEY\\u003d\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:34:56 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"308\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 10 Apr 2025 09:34:56 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":34.0,\"second\":56.0,\"nano\":1.90743E8}}},{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/list?KEY\\u003d\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:35:19 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"308\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 10 Apr 2025 09:35:19 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":35.0,\"second\":19.0,\"nano\":6.77552E8}}},{\"url\":\"http://localhost:8764/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8764/api/assay/list?KEY\\u003d\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":35.0,\"second\":33.0,\"nano\":9.944442E8}}},{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/list?KEY\\u003d\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:35:40 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"308\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 10 Apr 2025 09:35:40 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":35.0,\"second\":40.0,\"nano\":3.765488E8}}},{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/list\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:36:17 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"308\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 10 Apr 2025 09:36:17 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":36.0,\"second\":17.0,\"nano\":1.799249E8}}},{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/list\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:36:30 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"308\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 10 Apr 2025 09:36:30 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":36.0,\"second\":30.0,\"nano\":2.550196E8}}},{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/assay/list\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:42:20 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"308\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 10 Apr 2025 09:42:20 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":42.0,\"second\":20.0,\"nano\":9.45405E7}}},{\"url\":\"http://localhost:8765/api/assay/list\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"KEY\",\"value\":\"\",\"type\":\"Text\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"total\\\":1,\\\"data\\\":[{\\\"id\\\":\\\"3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\",\\\"tenantId\\\":\\\"e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\",\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"reportName\\\":\\\"\\\",\\\"samplingLocation\\\":\\\"111\\\",\\\"testingUnit\\\":\\\"111\\\",\\\"testResults\\\":\\\"合格\\\",\\\"testDate\\\":1743984000000,\\\"reportFile\\\":\\\"http://***********:9090/istar/256ed866-6204-4165-83ef-db4f164c5c29\\\",\\\"createTime\\\":1744015459776,\\\"updateTime\\\":1744073871274,\\\"remark\\\":\\\"\\\"}]}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 10 Apr 2025 09:47:06 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":10.0},\"time\":{\"hour\":17.0,\"minute\":47.0,\"second\":6.0,\"nano\":6.989981E8}}}],\"/api/sm/circuitTask/\":[{\"url\":\"http://localhost:8080/api/sm/circuitTask/\",\"header\":[],\"query\":[{\"key\":\"isNormalPlan\",\"value\":\"true\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"isReceived\",\"value\":\"true\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"isComplete\",\"value\":\"false\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"beginTimeFrom\",\"value\":\"2025-04-28 15:50:52\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"beginTimeTo\",\"value\":\"2025-04-28 15:50:52\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"receiveUserId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"1ed79f7c4ea3600ac4b4d3910c94b0c\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"collaborateUserId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"keyword\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pageableQueryEntityQueryType\",\"value\":\"AUTO\",\"type\":\"Text\",\"description\":\"AUTO MANUAL\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"records\",\"value\":\"\",\"type\":\"Text\",\"description\":\"查询数据列表\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"total\",\"value\":\"0\",\"type\":\"Text\",\"description\":\"总数\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"size\",\"value\":\"10\",\"type\":\"Text\",\"description\":\"每页显示条数，默认 10\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"page\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"当前页\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"orders\",\"value\":\"\",\"type\":\"Text\",\"description\":\"自定义排序字段信息\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"orderExpression\",\"value\":\"\",\"type\":\"Text\",\"description\":\"排序表达式\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"fromTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"toTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8080/api/sm/circuitTask/?receiveUserId\\u003d\\u0026size\\u003d10\\u0026isReceived\\u003dtrue\\u0026page\\u003d1\\u0026isComplete\\u003dfalse\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":28.0},\"time\":{\"hour\":15.0,\"minute\":52.0,\"second\":51.0,\"nano\":7.499332E8}}},{\"url\":\"http://localhost:8765/api/sm/circuitTask/\",\"header\":[],\"query\":[{\"key\":\"isNormalPlan\",\"value\":\"true\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"isReceived\",\"value\":\"true\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"isComplete\",\"value\":\"false\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"beginTimeFrom\",\"value\":\"2025-04-28 15:50:52\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"beginTimeTo\",\"value\":\"2025-04-28 15:50:52\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"receiveUserId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"1ed79f7c4ea3600ac4b4d3910c94b0c\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"collaborateUserId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"keyword\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pageableQueryEntityQueryType\",\"value\":\"AUTO\",\"type\":\"Text\",\"description\":\"AUTO MANUAL\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"records\",\"value\":\"\",\"type\":\"Text\",\"description\":\"查询数据列表\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"total\",\"value\":\"0\",\"type\":\"Text\",\"description\":\"总数\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"size\",\"value\":\"10\",\"type\":\"Text\",\"description\":\"每页显示条数，默认 10\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"page\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"当前页\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"orders\",\"value\":\"\",\"type\":\"Text\",\"description\":\"自定义排序字段信息\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"orderExpression\",\"value\":\"\",\"type\":\"Text\",\"description\":\"排序表达式\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"fromTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"toTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/sm/circuitTask/?receiveUserId\\u003d\\u0026size\\u003d10\\u0026isReceived\\u003dtrue\\u0026page\\u003d1\\u0026isComplete\\u003dfalse\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1745826778980}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Mon, 28 Apr 2025 07:52:58 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":28.0},\"time\":{\"hour\":15.0,\"minute\":52.0,\"second\":59.0,\"nano\":3.61414E7}}},{\"url\":\"http://localhost:8765/api/sm/circuitTask/\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.poAUfqWfrEB15YH-lZbpQyu8VANFGDbh4RUbrEDOJAf9UdCj6XpVt0HDjya4jeDhJtXGM0B_OjvBngRVfLTk5A\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"isNormalPlan\",\"value\":\"true\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"isReceived\",\"value\":\"true\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"isComplete\",\"value\":\"false\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Boolean\"},{\"key\":\"beginTimeFrom\",\"value\":\"2025-04-28 15:50:52\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"beginTimeTo\",\"value\":\"2025-04-28 15:50:52\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"receiveUserId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"1ed79f7c4ea3600ac4b4d3910c94b0c\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"creator\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"collaborateUserId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"keyword\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pageableQueryEntityQueryType\",\"value\":\"AUTO\",\"type\":\"Text\",\"description\":\"AUTO MANUAL\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"records\",\"value\":\"\",\"type\":\"Text\",\"description\":\"查询数据列表\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"total\",\"value\":\"0\",\"type\":\"Text\",\"description\":\"总数\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"size\",\"value\":\"10\",\"type\":\"Text\",\"description\":\"每页显示条数，默认 10\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"page\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"当前页\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"orders\",\"value\":\"\",\"type\":\"Text\",\"description\":\"自定义排序字段信息\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"orderExpression\",\"value\":\"\",\"type\":\"Text\",\"description\":\"排序表达式\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"fromTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"toTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":{\\\"total\\\":5,\\\"data\\\":[{\\\"id\\\":\\\"7e714bfd26fffc3b30187022ce42df25\\\",\\\"code\\\":\\\"202504270000\\\",\\\"planId\\\":\\\"66ba27b36bfd8008fbf62452a181e20d\\\",\\\"districtAreaId\\\":\\\"ec541b3ae74f7427371e3b4d35112147\\\",\\\"districtAreaName\\\":\\\"天府镇内\\\",\\\"isNormalPlan\\\":false,\\\"isNeedFeedback\\\":false,\\\"moveType\\\":\\\"步行\\\",\\\"devices\\\":\\\"[]\\\",\\\"specialDevices\\\":null,\\\"name\\\":\\\"测试计划任务\\\",\\\"receiveUserId\\\":\\\"1efb2b357578ba08b4b5196e50adaf1\\\",\\\"receiveUserName\\\":\\\"<EMAIL>\\\",\\\"receiveUserDepartmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"receiveUserDepartmentName\\\":\\\"技术部\\\",\\\"collaborateUserId\\\":null,\\\"collaborateUserName\\\":null,\\\"presentDistance\\\":null,\\\"remark\\\":null,\\\"presentState\\\":\\\"0/2\\\",\\\"statusName\\\":\\\"接收\\\",\\\"status\\\":\\\"RECEIVED\\\",\\\"fallbackState\\\":\\\"0/2\\\",\\\"keyPointCount\\\":2,\\\"deviceCount\\\":0,\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"creatorName\\\":\\\"演示账号\\\",\\\"creatorDepartmentId\\\":null,\\\"creatorDepartmentName\\\":\\\"未指定\\\",\\\"createTime\\\":\\\"2025-04-27 14:54:28\\\",\\\"beginTime\\\":\\\"2025-04-27 00:00:00\\\",\\\"endTime\\\":\\\"2025-04-27 23:59:59\\\",\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"auditUserId\\\":\\\"\\\",\\\"auditTime\\\":null,\\\"rejectReason\\\":\\\"123456\\\",\\\"planCircle\\\":\\\"1\\\",\\\"planCircleName\\\":\\\"一天一次\\\"},{\\\"id\\\":\\\"b4bed02f03273a5f33dc645c9a91a1b6\\\",\\\"code\\\":\\\"202411050002\\\",\\\"planId\\\":\\\"33aef1bd2c2c9a97a1be43df69006765\\\",\\\"districtAreaId\\\":\\\"7087ba181b7eba7fe7a2a5f28d979a09\\\",\\\"districtAreaName\\\":\\\"天府新谷区域\\\",\\\"isNormalPlan\\\":true,\\\"isNeedFeedback\\\":true,\\\"moveType\\\":\\\"车巡\\\",\\\"devices\\\":\\\"[]\\\",\\\"specialDevices\\\":null,\\\"name\\\":\\\"巡检任务\\\",\\\"receiveUserId\\\":\\\"1ef8c5219c236a0a5ee8b9796b1e05c\\\",\\\"receiveUserName\\\":\\\"未指定\\\",\\\"receiveUserDepartmentId\\\":null,\\\"receiveUserDepartmentName\\\":\\\"未指定\\\",\\\"collaborateUserId\\\":null,\\\"collaborateUserName\\\":null,\\\"presentDistance\\\":\\\"200\\\",\\\"remark\\\":null,\\\"presentState\\\":\\\"2/2\\\",\\\"statusName\\\":\\\"接收\\\",\\\"status\\\":\\\"RECEIVED\\\",\\\"fallbackState\\\":\\\"2/2\\\",\\\"keyPointCount\\\":2,\\\"deviceCount\\\":0,\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"creatorName\\\":\\\"演示账号\\\",\\\"creatorDepartmentId\\\":null,\\\"creatorDepartmentName\\\":\\\"未指定\\\",\\\"createTime\\\":\\\"2024-11-05 09:39:38\\\",\\\"beginTime\\\":\\\"2024-11-07 00:00:00\\\",\\\"endTime\\\":\\\"2024-11-07 23:59:59\\\",\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"auditUserId\\\":null,\\\"auditTime\\\":null,\\\"rejectReason\\\":null,\\\"planCircle\\\":\\\"1\\\",\\\"planCircleName\\\":\\\"一天一次\\\"},{\\\"id\\\":\\\"16fc1ac05fe8d759a43ec851574b3bcf\\\",\\\"code\\\":\\\"202308210002\\\",\\\"planId\\\":\\\"b4c8d81adc71f5756c63a42f8930e9d4\\\",\\\"districtAreaId\\\":\\\"ec541b3ae74f7427371e3b4d35112147\\\",\\\"districtAreaName\\\":\\\"天府镇内\\\",\\\"isNormalPlan\\\":true,\\\"isNeedFeedback\\\":false,\\\"moveType\\\":\\\"车巡\\\",\\\"devices\\\":\\\"[]\\\",\\\"specialDevices\\\":null,\\\"name\\\":\\\"测试任务\\\",\\\"receiveUserId\\\":\\\"1eed9f9c05cb7d0b3c65d0b7b19ad2b\\\",\\\"receiveUserName\\\":\\\"未指定\\\",\\\"receiveUserDepartmentId\\\":null,\\\"receiveUserDepartmentName\\\":\\\"未指定\\\",\\\"collaborateUserId\\\":null,\\\"collaborateUserName\\\":null,\\\"presentDistance\\\":\\\"10\\\",\\\"remark\\\":null,\\\"presentState\\\":\\\"0/2\\\",\\\"statusName\\\":\\\"接收\\\",\\\"status\\\":\\\"RECEIVED\\\",\\\"fallbackState\\\":\\\"0/2\\\",\\\"keyPointCount\\\":2,\\\"deviceCount\\\":0,\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"creatorName\\\":\\\"演示账号\\\",\\\"creatorDepartmentId\\\":null,\\\"creatorDepartmentName\\\":\\\"未指定\\\",\\\"createTime\\\":\\\"2023-08-21 22:44:45\\\",\\\"beginTime\\\":\\\"2023-08-22 00:00:00\\\",\\\"endTime\\\":\\\"2023-08-22 11:59:59\\\",\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"auditUserId\\\":null,\\\"auditTime\\\":null,\\\"rejectReason\\\":null,\\\"planCircle\\\":\\\"0\\\",\\\"planCircleName\\\":\\\"一天两次\\\"},{\\\"id\\\":\\\"05e51e4f69fa5ac4e1abf164aa44452a\\\",\\\"code\\\":\\\"202306300000\\\",\\\"planId\\\":\\\"b4c8d81adc71f5756c63a42f8930e9d4\\\",\\\"districtAreaId\\\":\\\"ec541b3ae74f7427371e3b4d35112147\\\",\\\"districtAreaName\\\":\\\"天府镇内\\\",\\\"isNormalPlan\\\":true,\\\"isNeedFeedback\\\":false,\\\"moveType\\\":\\\"车巡\\\",\\\"devices\\\":\\\"[]\\\",\\\"specialDevices\\\":null,\\\"name\\\":\\\"巡检\\\",\\\"receiveUserId\\\":\\\"1edbc09c1a09e90a461fb299c9b23b7\\\",\\\"receiveUserName\\\":\\\"未指定\\\",\\\"receiveUserDepartmentId\\\":null,\\\"receiveUserDepartmentName\\\":\\\"未指定\\\",\\\"collaborateUserId\\\":\\\"1edab88e562e90087125bb0dc03abd0\\\",\\\"collaborateUserName\\\":\\\"用户不存在\\\",\\\"presentDistance\\\":\\\"300\\\",\\\"remark\\\":null,\\\"presentState\\\":\\\"0/2\\\",\\\"statusName\\\":\\\"接收\\\",\\\"status\\\":\\\"RECEIVED\\\",\\\"fallbackState\\\":\\\"0/2\\\",\\\"keyPointCount\\\":2,\\\"deviceCount\\\":0,\\\"creator\\\":\\\"1ee172a0444b12097da0936076e546f\\\",\\\"creatorName\\\":\\\"未指定\\\",\\\"creatorDepartmentId\\\":null,\\\"creatorDepartmentName\\\":\\\"未指定\\\",\\\"createTime\\\":\\\"2023-06-30 17:57:23\\\",\\\"beginTime\\\":\\\"2023-06-29 00:00:00\\\",\\\"endTime\\\":\\\"2023-06-29 11:59:59\\\",\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"auditUserId\\\":null,\\\"auditTime\\\":null,\\\"rejectReason\\\":null,\\\"planCircle\\\":\\\"0\\\",\\\"planCircleName\\\":\\\"一天两次\\\"},{\\\"id\\\":\\\"802e19bc8d28b6c9cf572c37c05af3c1\\\",\\\"code\\\":\\\"2212140049\\\",\\\"planId\\\":\\\"f3bbac9686a606d4de65c7f968afcd08\\\",\\\"districtAreaId\\\":\\\"ec541b3ae74f7427371e3b4d35112147\\\",\\\"districtAreaName\\\":\\\"天府镇内\\\",\\\"isNormalPlan\\\":true,\\\"isNeedFeedback\\\":false,\\\"moveType\\\":\\\"车巡\\\",\\\"devices\\\":\\\"[{\\\\\\\"name\\\\\\\":\\\\\\\"阀门\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"阀门\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"阀门\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"阀门\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"阀门\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"2\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"251\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"250\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"253\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"238\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"249\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"8\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"16\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"13\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"246\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"247\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"20\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"11\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"15\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"245\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"244\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"21\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"18\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"237\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"248\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"1\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"2\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"12\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"19\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"17\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"7\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"14\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"9\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"22\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"节点\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"252\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"7\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"1\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"7\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"1\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"7\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"1\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"7\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"1\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"5\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"4\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"7\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"6\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"1\\\\\\\"},{\\\\\\\"name\\\\\\\":\\\\\\\"计量装置\\\\\\\",\\\\\\\"serialId\\\\\\\":\\\\\\\"3\\\\\\\"}]\\\",\\\"specialDevices\\\":null,\\\"name\\\":\\\"天府镇外巡检\\\",\\\"receiveUserId\\\":\\\"1ed7b718bde0e6085ef9fe6e6dd373e\\\",\\\"receiveUserName\\\":\\\"未指定\\\",\\\"receiveUserDepartmentId\\\":null,\\\"receiveUserDepartmentName\\\":\\\"未指定\\\",\\\"collaborateUserId\\\":\\\"1edb0c32291b8809af5fd2a6e92292d\\\",\\\"collaborateUserName\\\":\\\"用户不存在\\\",\\\"presentDistance\\\":\\\"50\\\",\\\"remark\\\":null,\\\"presentState\\\":\\\"0/71\\\",\\\"statusName\\\":\\\"接收\\\",\\\"status\\\":\\\"RECEIVED\\\",\\\"fallbackState\\\":\\\"0/2\\\",\\\"keyPointCount\\\":2,\\\"deviceCount\\\":69,\\\"creator\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"creatorName\\\":\\\"演示账号\\\",\\\"creatorDepartmentId\\\":null,\\\"creatorDepartmentName\\\":\\\"未指定\\\",\\\"createTime\\\":\\\"2022-12-14 14:08:15\\\",\\\"beginTime\\\":\\\"2022-12-14 00:00:00\\\",\\\"endTime\\\":\\\"2022-12-15 23:59:59\\\",\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"auditUserId\\\":null,\\\"auditTime\\\":null,\\\"rejectReason\\\":null,\\\"planCircle\\\":\\\"2\\\",\\\"planCircleName\\\":\\\"隔日一次\\\"}]}}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Mon, 28 Apr 2025 07:53:37 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":4.0,\"day\":28.0},\"time\":{\"hour\":15.0,\"minute\":53.0,\"second\":37.0,\"nano\":5.768198E8}}}],\"/api/getAllUsers\":[{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1749108779749}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Thu, 05 Jun 2025 07:32:59 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":15.0,\"minute\":32.0,\"second\":59.0,\"nano\":7.879974E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1749108825679}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Thu, 05 Jun 2025 07:33:45 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":15.0,\"minute\":33.0,\"second\":45.0,\"nano\":6.913641E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\n{\\\"status\\\":401,\\\"message\\\":\\\"Authorization header cannot be blank!\\\",\\\"errorCode\\\":10,\\\"timestamp\\\":1749108826779}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"105\"],\"Date\":[\"Thu, 05 Jun 2025 07:33:46 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":15.0,\"minute\":33.0,\"second\":46.0,\"nano\":7.889221E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 07:33:55 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":15.0,\"minute\":33.0,\"second\":55.0,\"nano\":2.805905E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 08:34:22 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":34.0,\"second\":22.0,\"nano\":4.74005E7}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 08:36:46 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":36.0,\"second\":46.0,\"nano\":9.606011E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":40.0,\"second\":10.0,\"nano\":1.892569E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":40.0,\"second\":15.0,\"nano\":6.26005E7}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":40.0,\"second\":47.0,\"nano\":8.179684E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 08:41:07 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":41.0,\"second\":7.0,\"nano\":1.52397E7}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Jun 05 16:51:34 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003enested exception is org.apache.ibatis.reflection.ReflectionException: Error instantiating class org.thingsboard.server.dao.model.sql.UserWithPassword with invalid types () or values (). Cause: java.lang.reflect.InvocationTargetException\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"534\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 05 Jun 2025 08:51:34 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":51.0,\"second\":34.0,\"nano\":8.550909E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Jun 05 16:53:35 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003enested exception is org.apache.ibatis.reflection.ReflectionException: Error instantiating class org.thingsboard.server.dao.model.sql.UserWithPassword with invalid types () or values (). Cause: java.lang.reflect.InvocationTargetException\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\",\"responseHeader\":{\"null\":[\"HTTP/1.1 500\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Connection\":[\"close\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Content-Length\":[\"534\"],\"Content-Language\":[\"zh-CN\"],\"Date\":[\"Thu, 05 Jun 2025 08:53:35 GMT\"],\"Content-Type\":[\"text/html;charset\\u003dISO-8859-1\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":53.0,\"second\":35.0,\"nano\":9.63951E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 08:57:27 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":16.0,\"minute\":57.0,\"second\":27.0,\"nano\":4.898924E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 09:04:41 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":17.0,\"minute\":4.0,\"second\":41.0,\"nano\":6.575021E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 09:04:44 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":17.0,\"minute\":4.0,\"second\":44.0,\"nano\":9.004603E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 11:14:03 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":19.0,\"minute\":14.0,\"second\":3.0,\"nano\":1.168407E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\",\"responseHeader\":{},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":19.0,\"minute\":18.0,\"second\":58.0,\"nano\":1.966272E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null,\\\"id\\\":null},{\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null,\\\"id\\\":null},{\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null,\\\"id\\\":null},{\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null,\\\"id\\\":null},{\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null,\\\"id\\\":null},{\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null,\\\"id\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 11:19:36 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":19.0,\"minute\":19.0,\"second\":36.0,\"nano\":1.833397E8}}},{\"url\":\"http://localhost:8765/api/getAllUsers\",\"header\":[{\"key\":\"x-authorization\",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"[{\\\"id\\\":null,\\\"tenantId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"SYS_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"超级管理员\\\",\\\"lastName\\\":\\\"超级管理员\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678901\\\",\\\"serialNo\\\":\\\"2023020018\\\",\\\"loginName\\\":null,\\\"userId\\\":\\\"1e87797f87dc840b342ffe817f06956\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"userId\\\":\\\"1ecc5004052e8d0ad6be5b4311740a7\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"水务管理员\\\",\\\"lastName\\\":\\\"水务管理员\\\",\\\"departmentId\\\":\\\"402882d582c4a73d0182c4aaff430000\\\",\\\"password\\\":\\\"$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18872030442\\\",\\\"serialNo\\\":\\\"2023020047\\\",\\\"loginName\\\":\\\"\\\",\\\"userId\\\":\\\"1ecc5004052e8d0ad6be5b4311740a7\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1b21dd2138140008080808080808080\\\",\\\"authority\\\":\\\"TENANT_ADMIN\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"演示账号\\\",\\\"lastName\\\":\\\"演示账号\\\",\\\"departmentId\\\":null,\\\"password\\\":\\\"$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"17702003323\\\",\\\"serialNo\\\":\\\"2023020046\\\",\\\"loginName\\\":null,\\\"userId\\\":\\\"1ed79f7c4ea3600ac4b4d3910c94b0c\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"<EMAIL>\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"4881c49465eb4e2c36861a960b6abc00\\\",\\\"password\\\":\\\"$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"18888888888\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"userId\\\":\\\"1efb2b357578ba08b4b5196e50adaf1\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null},{\\\"id\\\":null,\\\"tenantId\\\":\\\"1ed79ece3d7b2009c4b6f2427406ab1\\\",\\\"customerId\\\":\\\"1e88100b49ef6e09cbe77a34f885729\\\",\\\"authority\\\":\\\"CUSTOMER_USER\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"searchText\\\":\\\"<EMAIL>\\\",\\\"firstName\\\":\\\"111\\\",\\\"lastName\\\":null,\\\"departmentId\\\":\\\"d700b5674ec2725216afc5ac45b964e3\\\",\\\"password\\\":\\\"$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\",\\\"status\\\":false,\\\"departmentName\\\":null,\\\"organizationName\\\":null,\\\"roleName\\\":null,\\\"additionalInfo\\\":null,\\\"phone\\\":\\\"12345678910\\\",\\\"serialNo\\\":null,\\\"loginName\\\":null,\\\"userId\\\":\\\"1f041283acb3aa0845565fface690dc\\\",\\\"searchTextSource\\\":\\\"<EMAIL>\\\",\\\"nativeId\\\":null}]\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200\"],\"Transfer-Encoding\":[\"chunked\"],\"Cache-Control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"X-Content-Type-Options\":[\"nosniff\"],\"Pragma\":[\"no-cache\"],\"Expires\":[\"0\"],\"X-XSS-Protection\":[\"1; mode\\u003dblock\"],\"Date\":[\"Thu, 05 Jun 2025 11:28:04 GMT\"],\"Content-Type\":[\"application/json;charset\\u003dUTF-8\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":6.0,\"day\":5.0},\"time\":{\"hour\":19.0,\"minute\":28.0,\"second\":4.0,\"nano\":2.628559E8}}}],\"/api/eventOverview/\":[{\"url\":\"http://localhost:5000/api/workOrderType/list?status\\u003d1\\u0026pid\\u003d0\",\"header\":[],\"query\":[{\"key\":\"title\",\"value\":\"\",\"type\":\"Text\",\"description\":\"事件标题\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"source\",\"value\":\"\",\"type\":\"Text\",\"description\":\"来源\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"status\",\"value\":\"\",\"type\":\"Text\",\"description\":\"事件状态\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTimeFrom\",\"value\":\"2025-07-01 10:07:36\",\"type\":\"Text\",\"description\":\"创建时间开始\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTimeTo\",\"value\":\"2025-07-01 10:07:36\",\"type\":\"Text\",\"description\":\"创建时间结束\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"orderBy\",\"value\":\"create_time\",\"type\":\"Text\",\"description\":\"排序字段\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"orderDirection\",\"value\":\"DESC\",\"type\":\"Text\",\"description\":\"排序方向\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pageableQueryEntityQueryType\",\"value\":\"AUTO\",\"type\":\"Text\",\"description\":\"AUTO MANUAL\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"records\",\"value\":\"\",\"type\":\"Text\",\"description\":\"查询数据列表\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"total\",\"value\":\"0\",\"type\":\"Text\",\"description\":\"总数\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"size\",\"value\":\"10\",\"type\":\"Text\",\"description\":\"每页显示条数，默认 10\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"page\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"当前页\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"orders\",\"value\":\"\",\"type\":\"Text\",\"description\":\"自定义排序字段信息\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"orderExpression\",\"value\":\"\",\"type\":\"Text\",\"description\":\"排序表达式\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"fromTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"toTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"Fail to send:http://localhost:5000/api/workOrderType/list?status\\u003d1\\u0026pid\\u003d0\\n\\nError request, response status: 401\",\"responseHeader\":{\"null\":[\"HTTP/1.1 401 Unauthorized\"],\"date\":[\"Tue, 01 Jul 2025 02:07:52 GMT\"],\"content-length\":[\"0\"],\"Access-Control-Allow-Origin\":[\"*\"],\"vary\":[\"Origin, Access-Control-Request-Method, Access-Control-Request-Headers\"],\"content-type\":[\"application/json;charset\\u003dutf-8\"],\"connection\":[\"close\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":7.0,\"day\":1.0},\"time\":{\"hour\":10.0,\"minute\":7.0,\"second\":52.0,\"nano\":8.584235E8}}},{\"url\":\"http://localhost:5000/api/workOrderType/list?status\\u003d1\\u0026pid\\u003d0\",\"header\":[{\"key\":\"x-authorization \",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc1MTMzMTc3MywiZXhwIjoxNzUxOTM2NTczfQ.s53uB4vOnLdxNGp9zHiiQt3hceB05IZYQKj1H0940rmhK7p2iwJrPzsBnpRlLilVl_OI0o759OztT0J2Rauqvg\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"title\",\"value\":\"\",\"type\":\"Text\",\"description\":\"事件标题\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"source\",\"value\":\"\",\"type\":\"Text\",\"description\":\"来源\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"status\",\"value\":\"\",\"type\":\"Text\",\"description\":\"事件状态\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTimeFrom\",\"value\":\"2025-07-01 10:07:36\",\"type\":\"Text\",\"description\":\"创建时间开始\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"createTimeTo\",\"value\":\"2025-07-01 10:07:36\",\"type\":\"Text\",\"description\":\"创建时间结束\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"tenantId\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"orderBy\",\"value\":\"create_time\",\"type\":\"Text\",\"description\":\"排序字段\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"orderDirection\",\"value\":\"DESC\",\"type\":\"Text\",\"description\":\"排序方向\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pageableQueryEntityQueryType\",\"value\":\"AUTO\",\"type\":\"Text\",\"description\":\"AUTO MANUAL\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"records\",\"value\":\"\",\"type\":\"Text\",\"description\":\"查询数据列表\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"total\",\"value\":\"0\",\"type\":\"Text\",\"description\":\"总数\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"size\",\"value\":\"10\",\"type\":\"Text\",\"description\":\"每页显示条数，默认 10\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"page\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"当前页\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Integer\"},{\"key\":\"orders\",\"value\":\"\",\"type\":\"Text\",\"description\":\"自定义排序字段信息\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Array\"},{\"key\":\"orderExpression\",\"value\":\"\",\"type\":\"Text\",\"description\":\"排序表达式\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"fromTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"},{\"key\":\"toTime\",\"value\":\"\",\"type\":\"Text\",\"description\":\"\",\"is_checked\":0.0,\"not_null\":\"0\",\"field_type\":\"Object\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":[{\\\"id\\\":\\\"ldchuzhi\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"漏点处置\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"jksongdong\\\",\\\"parentId\\\":\\\"ldchuzhi\\\",\\\"name\\\":\\\"接口松动或腐蚀\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"gdlaohua\\\",\\\"parentId\\\":\\\"ldchuzhi\\\",\\\"name\\\":\\\"管道老化\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"baoguan\\\",\\\"parentId\\\":\\\"ldchuzhi\\\",\\\"name\\\":\\\"爆管\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"fxpl\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"防汛排涝\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"lmjscz\\\",\\\"parentId\\\":\\\"fxpl\\\",\\\"name\\\":\\\"路面积水处置\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"psbzgz\\\",\\\"parentId\\\":\\\"fxpl\\\",\\\"name\\\":\\\"排水泵站故障\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"sbwt\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"水表问题\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"sbjlyc\\\",\\\"parentId\\\":\\\"sbwt\\\",\\\"name\\\":\\\"水表计量异常\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"sbdl\\\",\\\"parentId\\\":\\\"sbwt\\\",\\\"name\\\":\\\"水表冻裂\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"fmqb\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"阀门启闭\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"fmdwjz\\\",\\\"parentId\\\":\\\"fmqb\\\",\\\"name\\\":\\\"阀门定位校准\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"fmshwx\\\",\\\"parentId\\\":\\\"fmqb\\\",\\\"name\\\":\\\"阀门损坏维修\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"yjqx\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"应急抢修\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"bgjjqx\\\",\\\"parentId\\\":\\\"yjqx\\\",\\\"name\\\":\\\"爆管紧急抢修\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"xfssh\\\",\\\"parentId\\\":\\\"yjqx\\\",\\\"name\\\":\\\"消防栓损坏\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"yswt\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"用水问题\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"sybz\\\",\\\"parentId\\\":\\\"yswt\\\",\\\"name\\\":\\\"水压不足\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"tszx\\\",\\\"parentId\\\":\\\"yswt\\\",\\\"name\\\":\\\"停水咨询\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"szwt\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"水质问题\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"szhz\\\",\\\"parentId\\\":\\\"szwt\\\",\\\"name\\\":\\\"水质混浊/异味\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"ylcb\\\",\\\"parentId\\\":\\\"szwt\\\",\\\"name\\\":\\\"余氯超标\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"jcyj\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"监测预警\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"zxybyc\\\",\\\"parentId\\\":\\\"jcyj\\\",\\\"name\\\":\\\"在线仪表异常\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"dmals\\\",\\\"parentId\\\":\\\"jcyj\\\",\\\"name\\\":\\\"DMA分区漏损报警\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"khfw\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"客户服务\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"sfzy\\\",\\\"parentId\\\":\\\"khfw\\\",\\\"name\\\":\\\"水费争议\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"zdcx\\\",\\\"parentId\\\":\\\"khfw\\\",\\\"name\\\":\\\"账单查询\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]},{\\\"id\\\":\\\"gwwh\\\",\\\"parentId\\\":\\\"0\\\",\\\"name\\\":\\\"管网维护\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[{\\\"id\\\":\\\"gwlsjc\\\",\\\"parentId\\\":\\\"gwwh\\\",\\\"name\\\":\\\"管网漏损检测\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]},{\\\"id\\\":\\\"gdbswx\\\",\\\"parentId\\\":\\\"gwwh\\\",\\\"name\\\":\\\"管道破损维修\\\",\\\"type\\\":null,\\\"typeName\\\":null,\\\"nodeDetail\\\":null,\\\"num\\\":null,\\\"icon\\\":null,\\\"children\\\":[]}]}]}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200 OK\"],\"date\":[\"Tue, 01 Jul 2025 02:08:34 GMT\"],\"expires\":[\"0\"],\"transfer-encoding\":[\"chunked\"],\"x-content-type-options\":[\"nosniff\"],\"x-xss-protection\":[\"1; mode\\u003dblock\"],\"Access-Control-Allow-Origin\":[\"*\"],\"vary\":[\"Origin, Access-Control-Request-Method, Access-Control-Request-Headers\"],\"content-type\":[\"application/json;charset\\u003dUTF-8\"],\"connection\":[\"close\"],\"cache-control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"pragma\":[\"no-cache\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":7.0,\"day\":1.0},\"time\":{\"hour\":10.0,\"minute\":8.0,\"second\":34.0,\"nano\":6.85676E7}}},{\"url\":\"http://localhost:5000/api/workOrderType/list?status\\u003d1\\u0026pid\\u003d0\",\"header\":[{\"key\":\"x-authorization \",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc1MTMzMTc3MywiZXhwIjoxNzUxOTM2NTczfQ.s53uB4vOnLdxNGp9zHiiQt3hceB05IZYQKj1H0940rmhK7p2iwJrPzsBnpRlLilVl_OI0o759OztT0J2Rauqvg\",\"type\":\"Text\",\"is_checked\":1.0,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"status\",\"value\":\"\",\"type\":\"Text\",\"description\":\"事件标题\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pid\",\"value\":\"\",\"type\":\"Text\",\"description\":\"来源\",\"is_checked\":1.0,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":[]}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200 OK\"],\"date\":[\"Tue, 01 Jul 2025 02:09:09 GMT\"],\"expires\":[\"0\"],\"transfer-encoding\":[\"chunked\"],\"x-content-type-options\":[\"nosniff\"],\"x-xss-protection\":[\"1; mode\\u003dblock\"],\"Access-Control-Allow-Origin\":[\"*\"],\"vary\":[\"Origin, Access-Control-Request-Method, Access-Control-Request-Headers\"],\"content-type\":[\"application/json;charset\\u003dUTF-8\"],\"connection\":[\"close\"],\"cache-control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"pragma\":[\"no-cache\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025.0,\"month\":7.0,\"day\":1.0},\"time\":{\"hour\":10.0,\"minute\":9.0,\"second\":9.0,\"nano\":5.850658E8}}},{\"url\":\"http://localhost:5000/api/workOrderType/list?status\\u003d1\\u0026pid\\u003d0\",\"header\":[{\"key\":\"x-authorization \",\"value\":\"bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc1MTMzMTc3MywiZXhwIjoxNzUxOTM2NTczfQ.s53uB4vOnLdxNGp9zHiiQt3hceB05IZYQKj1H0940rmhK7p2iwJrPzsBnpRlLilVl_OI0o759OztT0J2Rauqvg\",\"type\":\"Text\",\"is_checked\":1,\"not_null\":\"1\",\"field_type\":\"string\"}],\"query\":[{\"key\":\"status\",\"value\":\"1\",\"type\":\"Text\",\"description\":\"事件标题\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"},{\"key\":\"pid\",\"value\":\"0\",\"type\":\"Text\",\"description\":\"来源\",\"is_checked\":1,\"not_null\":\"0\",\"field_type\":\"String\"}],\"rest\":[],\"cookie\":[],\"body\":{\"mode\":\"none\",\"parameter\":[],\"raw\":\"\",\"raw_para\":[]},\"responseBody\":\"{\\\"code\\\":200,\\\"message\\\":\\\"操作成功!\\\",\\\"data\\\":[]}\",\"responseHeader\":{\"null\":[\"HTTP/1.1 200 OK\"],\"date\":[\"Tue, 01 Jul 2025 02:09:16 GMT\"],\"expires\":[\"0\"],\"transfer-encoding\":[\"chunked\"],\"x-content-type-options\":[\"nosniff\"],\"x-xss-protection\":[\"1; mode\\u003dblock\"],\"Access-Control-Allow-Origin\":[\"*\"],\"vary\":[\"Origin, Access-Control-Request-Method, Access-Control-Request-Headers\"],\"content-type\":[\"application/json;charset\\u003dUTF-8\"],\"connection\":[\"close\"],\"cache-control\":[\"no-cache, no-store, max-age\\u003d0, must-revalidate\"],\"pragma\":[\"no-cache\"]},\"responseCookieList\":[],\"selectedItem\":\"GET\",\"time\":{\"date\":{\"year\":2025,\"month\":7,\"day\":1},\"time\":{\"hour\":10,\"minute\":9,\"second\":16,\"nano\":307288300}}}]}",
    "Application.ThingsboardServerApplication.executor": "Debug",
    "Maven.thingsboard [clean].executor": "Run",
    "Maven.thingsboard [install].executor": "Run",
    "Maven.thingsboard [verify].executor": "Run",
    "Maven.transport [clean].executor": "Run",
    "Maven.transport [install].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "liutong",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Code-Yanfayun/water/guazhou/water-guazhou-plat/application/src/main/java/org/thingsboard/server/controller",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\contract" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\gis" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\station" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\store" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\resources\mapper\smartManagement\settings" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\maintenance" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\resources\mapper" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\common\file\src\main\java\org\thingsboard" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="ThingsboardServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.thingsboard.server.ThingsboardServerApplication" />
      <module name="application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.thingsboard.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="water-guazhou-plat" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ThingsboardServerApplication" />
        <item itemvalue="Application.ThingsboardServerApplication" />
        <item itemvalue="Application.ThingsboardServerApplication" />
        <item itemvalue="Application.ThingsboardServerApplication" />
        <item itemvalue="Application.ThingsboardServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="143454f2-f002-4f9c-9c7f-b3073c35c581" name="Changes" comment="" />
      <created>1743416694552</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743416694552</updated>
    </task>
    <task id="LOCAL-00005" summary="补全接口,水源地巡检相关">
      <option name="closed" value="true" />
      <created>1744275227206</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1744275227206</updated>
    </task>
    <task id="LOCAL-00006" summary="补全接口,视频和工单相关">
      <option name="closed" value="true" />
      <created>1744283163687</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1744283163687</updated>
    </task>
    <task id="LOCAL-00007" summary="完善化验记录功能">
      <option name="closed" value="true" />
      <created>1744352728573</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1744352728573</updated>
    </task>
    <task id="LOCAL-00008" summary="完成涵养工艺功能，修改化验记录bug">
      <option name="closed" value="true" />
      <created>1744702391451</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744702391451</updated>
    </task>
    <task id="LOCAL-00009" summary="补全代码">
      <option name="closed" value="true" />
      <created>1744707172081</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1744707172081</updated>
    </task>
    <task id="LOCAL-00010" summary="采样记录功能提交">
      <option name="closed" value="true" />
      <created>1744769118341</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1744769118341</updated>
    </task>
    <task id="LOCAL-00011" summary="修改数据库配置">
      <option name="closed" value="true" />
      <created>1745308992366</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1745308992366</updated>
    </task>
    <task id="LOCAL-00012" summary="修改数据库配置">
      <option name="closed" value="true" />
      <created>1745309223999</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1745309223999</updated>
    </task>
    <task id="LOCAL-00013" summary="修复通过分区id查询的的sql">
      <option name="closed" value="true" />
      <created>1745373279094</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1745373279094</updated>
    </task>
    <task id="LOCAL-00014" summary="补充缺失代码">
      <option name="closed" value="true" />
      <created>1745459495624</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1745459495624</updated>
    </task>
    <task id="LOCAL-00015" summary="修改设备属性详情bug">
      <option name="closed" value="true" />
      <created>1745462605192</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1745462605192</updated>
    </task>
    <task id="LOCAL-00016" summary="thingsboard页面相关内容">
      <option name="closed" value="true" />
      <created>1745722153028</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1745722153028</updated>
    </task>
    <task id="LOCAL-00017" summary="完善任务流程内容">
      <option name="closed" value="true" />
      <created>1745823115004</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1745823115004</updated>
    </task>
    <task id="LOCAL-00018" summary="完善任务流程内容">
      <option name="closed" value="true" />
      <created>1745911482968</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1745911482968</updated>
    </task>
    <task id="LOCAL-00019" summary="应要求 添加菜单的同时，添加应用关系">
      <option name="closed" value="true" />
      <created>1746608555698</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1746608555698</updated>
    </task>
    <task id="LOCAL-00020" summary="应要求 添加菜单的同时，添加应用关系">
      <option name="closed" value="true" />
      <created>1746609243351</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1746609243351</updated>
    </task>
    <task id="LOCAL-00021" summary="应要求 删除菜单的同时，删除应用关系">
      <option name="closed" value="true" />
      <created>1746610465307</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1746610465307</updated>
    </task>
    <task id="LOCAL-00022" summary="修改化验记录，支持水源地，供水厂，污水厂">
      <option name="closed" value="true" />
      <created>1746669963358</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1746669963358</updated>
    </task>
    <task id="LOCAL-00023" summary="修复菜单出现的重复提交问题">
      <option name="closed" value="true" />
      <created>1746686846719</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1746686846719</updated>
    </task>
    <task id="LOCAL-00024" summary="调度分析和出水台账">
      <option name="closed" value="true" />
      <created>1746772136391</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1746772136391</updated>
    </task>
    <task id="LOCAL-00025" summary="组织管理更改为水厂管理">
      <option name="closed" value="true" />
      <created>1746779659061</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1746779659061</updated>
    </task>
    <task id="LOCAL-00026" summary="调度相关内容新增管理">
      <option name="closed" value="true" />
      <created>1746783266941</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1746783266941</updated>
    </task>
    <task id="LOCAL-00027" summary="设备权限功能">
      <option name="closed" value="true" />
      <created>1747039033861</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747039033861</updated>
    </task>
    <task id="LOCAL-00028" summary="设备权限实现类目录调整，项目归档记录bug修复">
      <option name="closed" value="true" />
      <created>1747809260228</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747809260229</updated>
    </task>
    <task id="LOCAL-00029" summary="设备图标功能（展示配置）；修规项目归档bug">
      <option name="closed" value="true" />
      <created>1747816406793</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747816406793</updated>
    </task>
    <task id="LOCAL-00030" summary="修改租户id相关内容，进行转化">
      <option name="closed" value="true" />
      <created>1747818196543</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1747818196543</updated>
    </task>
    <task id="LOCAL-00031" summary="修复租户id转化时的bug">
      <option name="closed" value="true" />
      <created>1747894181892</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1747894181892</updated>
    </task>
    <task id="LOCAL-00032" summary="视频监管功能">
      <option name="closed" value="true" />
      <created>1747985586569</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1747985586569</updated>
    </task>
    <task id="LOCAL-00033" summary="门禁管理视频监管功能">
      <option name="closed" value="true" />
      <created>1748228732032</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1748228732032</updated>
    </task>
    <task id="LOCAL-00034" summary="1.删除解密后密码日志&#10;2.完成rabbitmq同步用户消息推送">
      <option name="closed" value="true" />
      <created>1748502756866</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1748502756866</updated>
    </task>
    <task id="LOCAL-00035" summary="1.删除未用到的文件">
      <option name="closed" value="true" />
      <created>1748509959497</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1748509959497</updated>
    </task>
    <task id="LOCAL-00036" summary="用户相关内容修改，包括用户修改，全量查询等">
      <option name="closed" value="true" />
      <created>1749110250389</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1749110250389</updated>
    </task>
    <task id="LOCAL-00037" summary="巡检配置相关内容">
      <option name="closed" value="true" />
      <created>1749120807812</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1749120807812</updated>
    </task>
    <task id="LOCAL-00038" summary="全量用户校准">
      <option name="closed" value="true" />
      <created>1749122975361</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1749122975361</updated>
    </task>
    <task id="LOCAL-00039" summary="巡检配置表结构位置修改，培训配置-巡检计划功能">
      <option name="closed" value="true" />
      <created>1749180916961</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1749180916961</updated>
    </task>
    <task id="LOCAL-00040" summary="外业巡检相关内容完善（巡检表单绑定计划），修改了巡检表单，巡检计划，巡检任务等内容">
      <option name="closed" value="true" />
      <created>1749453572320</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1749453572320</updated>
    </task>
    <task id="LOCAL-00041" summary="alarmJson接口报错修改，不走@Cacheable">
      <option name="closed" value="true" />
      <created>1749535739382</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1749535739382</updated>
    </task>
    <task id="LOCAL-00042" summary="修改采用记录使用全部模块">
      <option name="closed" value="true" />
      <created>1749607495274</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1749607495274</updated>
    </task>
    <task id="LOCAL-00043" summary="涵养水位功能">
      <option name="closed" value="true" />
      <created>1749639811165</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1749639811165</updated>
    </task>
    <task id="LOCAL-00044" summary="平台登陆相关功能">
      <option name="closed" value="true" />
      <created>1750159035940</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750159035940</updated>
    </task>
    <task id="LOCAL-00045" summary="水源地和供水厂功能补充">
      <option name="closed" value="true" />
      <created>1750244412094</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750244412095</updated>
    </task>
    <task id="LOCAL-00046" summary="泵组方案功能">
      <option name="closed" value="true" />
      <created>1750988795456</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750988795456</updated>
    </task>
    <task id="LOCAL-00047" summary="1.登录部分逻辑修改。&#10;2.test依赖错误修复">
      <option name="closed" value="true" />
      <created>1751266106151</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1751266106151</updated>
    </task>
    <task id="LOCAL-00048" summary="事件总览系统功能">
      <option name="closed" value="true" />
      <created>1751509460795</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1751509460795</updated>
    </task>
    <task id="LOCAL-00049" summary="资产相关代码">
      <option name="closed" value="true" />
      <created>1751513822066</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1751513822066</updated>
    </task>
    <task id="LOCAL-00050" summary="设备相关">
      <option name="closed" value="true" />
      <created>1751514856007</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1751514856008</updated>
    </task>
    <task id="LOCAL-00051" summary="设备相关">
      <option name="closed" value="true" />
      <created>1751528422608</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1751528422608</updated>
    </task>
    <task id="LOCAL-00052" summary="删除test相关内容">
      <option name="closed" value="true" />
      <created>1751615421956</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1751615421957</updated>
    </task>
    <task id="LOCAL-00053" summary="维修总览和事件总览">
      <option name="closed" value="true" />
      <created>1751878945250</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1751878945250</updated>
    </task>
    <option name="localTasksCounter" value="54" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/guazhou" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="guazhou" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/guazhou" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="1.删除未用到的文件" />
    <MESSAGE value="6.4" />
    <MESSAGE value="6.4 10.53" />
    <MESSAGE value="用户相关内容修改，包括用户修改，全量查询等" />
    <MESSAGE value="巡检配置相关内容" />
    <MESSAGE value="全量用户校准" />
    <MESSAGE value="巡检配置表结构位置修改，培训配置-巡检计划功能" />
    <MESSAGE value="外业巡检相关内容完善（巡检表单绑定计划），修改了巡检表单，巡检计划，巡检任务等内容" />
    <MESSAGE value="alarmJson接口报错修改，不走@Cacheable" />
    <MESSAGE value="修改采用记录使用全部模块" />
    <MESSAGE value="涵养水位功能" />
    <MESSAGE value="6.11" />
    <MESSAGE value="平台登陆相关功能" />
    <MESSAGE value="水源地和供水厂功能补充" />
    <MESSAGE value="泵组方案功能" />
    <MESSAGE value="6.27" />
    <MESSAGE value="1.登录部分逻辑修改。&#10;2.test依赖错误修复" />
    <MESSAGE value="6.30" />
    <MESSAGE value="事件总览系统功能" />
    <MESSAGE value="资产相关代码" />
    <MESSAGE value="设备相关" />
    <MESSAGE value="7.4" />
    <MESSAGE value="删除test相关内容" />
    <MESSAGE value="维修总览和事件总览" />
    <MESSAGE value="7.7" />
    <option name="LAST_COMMIT_MESSAGE" value="7.7" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/smartPipe/PartitionServiceImpl.java</url>
          <line>417</line>
          <properties>
            <option name="lambda-ordinal" value="-1" />
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/base/UserController.java</url>
          <line>309</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/user/UserServiceImpl.java</url>
          <line>214</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/zutai/ZutaiDashboardServiceImpl.java</url>
          <line>53</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="assignStatus" />
      </configuration>
    </watches-manager>
  </component>
</project>