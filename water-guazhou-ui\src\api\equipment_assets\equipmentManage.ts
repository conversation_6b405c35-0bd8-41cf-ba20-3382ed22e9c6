import { request } from '@/plugins/axios'

// 获取设备类型树
export const getDeviceTypeTree = () => request({
  url: `/api/deviceType/tree`,
  method: 'get'
})

// 获取只定义属性
export const getCustomize = (serialId:string) => request({
  url: `/api/deviceTypeAttr/list/${serialId}`,
  method: 'get'
})

// 查询设备类型树
export const getDeviceTypeTreeSearch = (params?: {
  name?: string
  creator?: string
  page?: string
  size?: string
  fromTime?: string
  toTime?: string
}) => request({
  url: `/api/deviceType`,
  method: 'get',
  params
})

// 新建设备类别
export const postDeviceType = (params?: {
  serialId: string
  name: string
  orderNum: string
  remark?: string
  parentId?: string
  creator?: string
}) => request({
  url: `/api/deviceType`,
  method: 'post',
  data: params
})

// 修改设备类型
export const patchDeviceType = (id:string, params:{ serialId: string
  name: string
  orderNum: string
  remark?: string
  parentId?: string
  creator?: string}) => request({
  url: `/api/deviceType/${id}`,
  method: 'patch',
  data: params
})

// 删除设备类型
export const deleteDeviceType = (id:string) => request({
  url: `/api/deviceType/${id}`,
  method: 'delete'
})

// 查询设备类型属性
export const getDeviceTypeAttrSearch = (params?: {
  serialId?: string
  code?: string
  name?: string
  creator?: string
  page?: string
  size?: string
  fromTime?: string
  toTime?: string
}) => request({
  url: `/api/deviceTypeAttr`,
  method: 'get',
  params
})

// 查询设备列表
export const getDeviceListSearch = (params?: {
  page: number|undefined
  size: number|undefined
  typeId: string
  serialId: string
  name: string
  model: string
}) => request({
  url: `/api/device/m`,
  method: 'get',
  params
})

// 添加设备
export const postDevice = (params?: {
  serialId:string
  name:string
  model:string
  label:string
  unit:string
  maintenanceCycle:string
  minStock:string
  images:string
  files:string
  remark:string
  typeId:string
}) => request({
  url: `/api/device/m`,
  method: 'post',
  data: params
})

// 删除设备
export const deleteDevice = (ids:string[]) => request({
  url: `/api/device/m`,
  method: 'delete',
  data: ids
})

// 添加设备类别属性
export const postDeviceTypeAttr = (params?: {
  serialId: string
  code: string
  name: string
  remark?: string
}) => request({
  url: `/api/deviceTypeAttr`,
  method: 'post',
  data: params
})

// 删除设备类型属性
export const deleteDeviceTypeAttr = (id:string) => request({
  url: `/api/deviceTypeAttr/${id}`,
  method: 'delete'
})

// 修改设备类型属性
export const patchDeviceTypeAttr = (id:string, params?: {
  serialId: string
  code: string
  name: string
  remark?: string
}) => request({
  url: `/api/deviceTypeAttr/${id}`,
  method: 'patch',
  data: params
})

// 安装区域树
export const getAreaTreeSearch = (params?: {
  page: number|undefined
  size: number|undefined
  name?: string
  shortName: string
}) => request({
  url: `/api/area/tree/page`,
  method: 'get',
  params
})

// 新建/修改安装区域
export const postAreaTree = (params?: {
  id?:string
  name: string
  shortName: string
  nickName: string
  img: string
  remark: string
  orderNum: string
  parentId?: string
  serialId?: string
}) => request({
  url: `/api/area`,
  method: 'post',
  data: params
})

// 删除设备安装区域
export const deleteArea = (data:string[]) => request({
  url: `/api/area`,
  method: 'delete',
  data
})

// 获取供应商
export const getSupplierSerch = (params?: {
  page: number|undefined
  size: number|undefined
  name?: string
  address?: string
  status?: string
  importance?: string
}) => request({
  url: `/api/supplier`,
  method: 'get',
  params
})

// 删除供应商
export const deleteSupplier = (data:string[]) => request({
  url: `/api/supplier`,
  method: 'delete',
  data
})

// 新建/修改供应商
export const postSupplier = (params?: {
  id?:string
  name: string
  address: string
  contact: string
  contactPhone: string
  website: string
  importance: string
  companySize: string
  email: string
  deviceTypeId: string
  status: string
  remark: string
  supplierGoodsList: [{
    serialId:string
    num:string
    price:string
    taxRate:string
  }]
  supplierQualificationsList: [{
    name:string
    organization:string
    time:string
    files:string
  }]
}) => request({
  url: `/api/supplier`,
  method: 'post',
  data: params
})

// 获取物资信息
export const getMaterialsSerch = (main: string) => request({
  url: `/api/supplier/goods/getByMainId/${main}`,
  method: 'get'
})

// 获取资质信息
export const getQualificationSerch = (main: string) => request({
  url: `/api/supplier/qualifications/getByMainId/${main}`,
  method: 'get'
})
