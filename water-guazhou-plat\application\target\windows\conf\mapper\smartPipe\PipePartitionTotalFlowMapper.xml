<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipePartitionTotalFlowMapper">
    <insert id="batchSave">
        insert into tb_pipe_partition_total_flow(id, device_id, value, collect_time, type, tenant_id, correct_water, origin_water) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
            #{element.deviceId},
            #{element.value},
            #{element.collectTime},
            #{element.type},
            #{element.tenantId},
            #{element.correctWater},
            #{element.originWater})
        </foreach>

    </insert>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow">
        select distinct flow.*, partition.name as partitionName, d.name as deviceName,
        (case
        when flow.type = '1' then to_char(flow.collect_time, 'YYYY-MM-DD HH24:MI:SS')
        when flow.type = '2' then to_char(flow.collect_time, 'YYYY-MM-DD')
        when flow.type = '3' then to_char(flow.collect_time, 'YYYY-MM') end) as collectTimeStr
        from tb_pipe_partition_total_flow flow
        left join tb_pipe_partition_mount mount on mount.device_id = flow.device_id
        left join tb_pipe_partition partition on mount.partition_id = partition.id
        left join device d on d.id = mount.device_id
        where
            mount.partition_id is not null
            <if test="param.partitionId != null and param.partitionId != ''">
                and partition.id = #{param.partitionId}
            </if>
            <if test="param.type != null and param.type != ''">
                and flow.type = #{param.type}
            </if>
            <if test="param.deviceIdList != null and param.deviceIdList.size() > 0">
                and d.id in
                <foreach collection="param.deviceIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="param.start != null and param.start != ''">
                and flow.collect_time &gt;= to_timestamp(#{param.start} / 1000) and flow.collect_time &lt; to_timestamp(#{param.end} / 1000)
            </if>
            <if test="param.meterName != null and param.meterName != ''">
                and d.name like '%' || #{param.meterName} || '%'
            </if>
        order by flow.collect_time
    </select>

    <select id="getDayTotalFlow" resultType="java.util.Map">
        select device_id as "deviceId", ifnull(sum(value), 0) as total from tb_pipe_partition_total_flow a
        where a.type = #{type}
          <if test="start != null">
            and a.collect_time &gt;= to_timestamp(#{start} / 1000) and a.collect_time &lt; to_timestamp(#{end} / 1000)
          </if>
        <if test="list != null and list.size() > 0">
            and a.device_id in
            <foreach collection="list" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by device_id
    </select>

    <select id="getPartitionTotalFlow" resultType="java.math.BigDecimal">
        select sum(coalesce(value, 0)) as total
        from tb_pipe_partition_total_flow a
        left join tb_pipe_partition_mount tpm on a.device_id = tpm.device_id

        where tpm.partition_id = #{partitionId} and a.type = #{type}
          and a.collect_time &gt;= #{start} and a.collect_time &lt; #{end}
            <if test="direction == 'in'.toString()">
                and tpm.direction != '4'
            </if>
            <if test="direction == 'out'.toString()">
                and tpm.direction = '4'
            </if>
        group by tpm.partition_id
    </select>

    <select id="sumByTime" resultType="java.math.BigDecimal">
        select ifnull(sum(value), 0) from tb_pipe_partition_total_flow a
        where a.type = #{type}
          and a.collect_time &gt;= to_timestamp(#{start} / 1000) and a.collect_time &lt; to_timestamp(#{end} / 1000)
          and a.tenant_id = #{tenantId}
          <if test="deviceList != null and deviceList.size() > 0">
              and a.device_id in
              <foreach collection="deviceList" open="(" separator="," close=")" item="item">
                  #{item}
              </foreach>
          </if>
    </select>
    <select id="getAllByTime" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow">
        select * from tb_pipe_partition_total_flow a
        where a.type = #{type}
        and a.collect_time &gt;= to_timestamp(#{start} / 1000) and a.collect_time &lt; to_timestamp(#{end} / 1000)
        and a.tenant_id = #{tenantId}
        <if test="deviceList != null and deviceList.size() > 0">
            and a.device_id in
            <foreach collection="deviceList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="countByTime" resultType="java.lang.Integer">
        select count(*) from tb_pipe_partition_total_flow a
        where a.type = #{type}
        and a.collect_time &gt;= to_timestamp(#{start} / 1000) and a.collect_time &lt; to_timestamp(#{end} / 1000)
        and a.tenant_id = #{tenantId}
        <if test="deviceList != null and deviceList.size() > 0">
            and a.device_id in
            <foreach collection="deviceList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

<!--    //TODO 2025/4/23 sql报错修复-->
<!--    <select id="sumByPartitionId" resultType="com.alibaba.fastjson.JSONObject">-->
<!--        select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) "correctWater"-->
<!--        from tb_pipe_partition tpp-->
<!--        left join tb_pipe_partition_mount tpm on tpm.partition_id = tpp.id-->
<!--        left join-->
<!--            (select origin_water, device_id, value  from tb_pipe_partition_total_flow a-->
<!--                where-->
<!--                a.type = #{type}-->
<!--                and a.collect_time &gt;= to_timestamp(#{start} / 1000) and a.collect_time &lt; to_timestamp(#{end} / 1000)) a on a.device_id = tpm.device_id-->
<!--        where tpp.id in-->
<!--              <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">-->
<!--                  #{item}-->
<!--              </foreach>-->
<!--            <if test="direction != null">-->
<!--                <if test="direction == 'in'.toString()">-->
<!--                    and tpm.direction != '4'-->
<!--                </if>-->
<!--                <if test="direction == 'out'.toString()">-->
<!--                    and tpm.direction = '4'-->
<!--                </if>-->
<!--            </if>-->
<!--            <if test="deviceType != null and deviceType != ''">-->
<!--                and tpm.type = #{deviceType}-->
<!--            </if>-->
<!--        group by tpp.id, tpp.name-->
<!--    </select>-->
    <select id="sumByPartitionId" resultType="com.alibaba.fastjson.JSONObject">
        select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) "correctWater"
        from tb_pipe_partition tpp
        left join tb_pipe_partition_mount tpm on tpm.partition_id = tpp.id
        left join
        (select origin_water, device_id, value  from tb_pipe_partition_total_flow a
        where
        a.type = #{type}
        and a.collect_time &gt;= to_timestamp(#{start} / 1000) and a.collect_time &lt; to_timestamp(#{end} / 1000)) a on a.device_id = tpm.device_id
        <where>
            <if test="partitionIdList != null and partitionIdList.size() > 0">
                tpp.id in
                <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="direction != null">
                <if test="direction == 'in'.toString()">
                    and tpm.direction != '4'
                </if>
                <if test="direction == 'out'.toString()">
                    and tpm.direction = '4'
                </if>
            </if>
            <if test="deviceType != null and deviceType != ''">
                and tpm.type = #{deviceType}
            </if>
        </where>
        group by tpp.id, tpp.name
    </select>
    <select id="getListByPartitionId" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow">
        select *
        from tb_pipe_partition_total_flow flow
        left join tb_pipe_partition_mount mount on flow.device_id = mount.device_id
        <where>
            <if test="type != null and type != ''">
                and flow.type = #{type}
            </if>
            <if test="direction != null">
                <if test="direction == 'in'.toString()">
                    and mount.direction != '4'
                </if>
                <if test="direction == 'out'.toString()">
                    and mount.direction = '4'
                </if>
            </if>
            <if test="partitionId != null and partitionId != ''">
                and mount.partition_id = #{partitionId}
            </if>
            <if test="start != null">
                and flow.collect_time &gt;= to_timestamp(#{start} / 1000) and flow.collect_time &lt; to_timestamp(#{end} / 1000)
            </if>
        </where>
        order by flow.collect_time
    </select>
    <select id="getSupplyTotalByPartitionId" resultType="java.math.BigDecimal">
        select sum(coalesce(inFlow.value)) - sum(COALESCE(outFlow.value,0))
        from tb_pipe_partition_mount tpm
         left join  tb_pipe_partition_total_flow inFlow on inFlow.device_id = tpm.device_id and tpm.direction = '3' and inFlow.type = #{type}
            and inFlow.collect_time &gt;= to_timestamp(#{start} / 1000) and inFlow.collect_time &lt; to_timestamp(#{end} / 1000)
         left join  tb_pipe_partition_total_flow outFlow on outFlow.device_id = tpm.device_id and tpm.direction = '4' and outFlow.type = #{type}
            and outFlow.collect_time &gt;= to_timestamp(#{start} / 1000) and outFlow.collect_time &lt; to_timestamp(#{end} / 1000)
        where tpm.partition_id = #{partitionId}
    </select>

    <delete id="deleteByType">
        delete from tb_pipe_partition_total_flow a
        where a.type = #{type}
        and a.device_id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and a.collect_time between to_timestamp(#{start} / 1000) and to_timestamp(#{end} / 1000)
    </delete>
</mapper>