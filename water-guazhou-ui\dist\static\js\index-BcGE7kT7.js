import{d as q,M as L,c as g,r as d,s as f,S as I,o as B,g as F,n as $,q as u,i as m,F as A,b6 as E,ak as M,al as V,bL as z,bM as N,bq as P}from"./index-r0dFAfgr.js";import{_ as G}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{p as H}from"./process-DWVjEFpZ.js";import{e as J,d as K,a as O}from"./applyInstall-D-IustB3.js";import{f as Q}from"./DateFormatter-Bm9a68Ax.js";import U from"./detail-UHbHAYbG.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./printUtils-C-AxhDcd.js";const X={class:"wrapper"},pe=q({__name:"index",setup(Y){const{$messageError:C,$messageSuccess:k,$messageWarning:S}=L(),W=g(),v=g(),_=g(),h=g(),b=d({taskInfo:""}),T=d({filters:[{label:"状态",field:"status",type:"select",options:[{label:"进行中",value:"进行中"},{label:"已完成",value:"已完成"},{label:"已结束",value:"已结束"}]}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新增",svgIcon:f(M),click:()=>x()},{perm:!0,text:"查询",svgIcon:f(V),click:()=>p()}]}]}),n=d({loading:!0,indexVisible:!0,columns:[{label:"申请日期",prop:"createTime",minWidth:120,align:"center",formatter:e=>Q(e.createTime)},{label:"申请编号",prop:"code",minWidth:120,align:"center",handleClick:e=>{var t;b.taskInfo=e,y.title=e.code,(t=h.value)==null||t.openDrawer()},cellStyle:{color:"#409eff"}},{label:"工程地址",prop:"address",minWidth:120,align:"center"},{label:"工程类型",prop:"typeName",minWidth:120,align:"center"},{label:"当前步骤",prop:"currentStep",minWidth:120,align:"center"},{label:"当前状态",prop:"status",minWidth:120,align:"center"}],dataList:[],operationFixed:"right",operationWidth:260,operations:[{perm:!0,text:"查看",isTextBtn:!1,type:"success",svgIcon:f(z),click:e=>{var t;y.title=e.code,(t=h.value)==null||t.openDrawer(),b.taskInfo=e}},{perm:!0,text:"编辑",isTextBtn:!1,svgIcon:f(N),click:e=>x(e)},{perm:!0,text:"删除",isTextBtn:!1,type:"danger",svgIcon:f(P),click:e=>w([e.id])}],pagination:{refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,p()}}}),s=d({title:"新增",labelWidth:120,dialogWidth:500,group:[{fields:[{type:"select",label:"工程类型",field:"type",options:[],rules:[{required:!0,message:"请选择工程类型"}],placeholder:"请选择工程类型"},{type:"input",label:"工程地址",field:"address",rules:[{required:!0,message:"请填写工程地址"}],placeholder:"请填写工程地址"}]}]}),y=d({title:"",cancel:!1,width:"80%",group:[]}),x=async e=>{var c,l,i;const t=(c=s.group[0].fields)==null?void 0:c.find(a=>a.field==="type"),r=await H({page:1,size:9999});t.options=(l=r.data)==null?void 0:l.data.data.map(a=>({label:a.name,value:a.id})),s.defaultValue={...e||{}},s.submit=a=>{I("确定提交？","提示信息").then(()=>{s.submitting=!0,a={...a,id:e?e.id:null},J(a).then(()=>{var o;(o=_.value)==null||o.closeDialog(),s.submitting=!1,k("保存成功"),p()}).catch(o=>{C(o),s.submitting=!1})})},(i=_.value)==null||i.openDialog()},w=e=>{I("确定删除？","提示信息").then(()=>{K(e).then(()=>{k("删除成功"),p()}).catch(t=>{S(t)})})},p=async()=>{var l,i,a,o,D;n.loading=!0;const t={...((l=v.value)==null?void 0:l.queryParams)||{},page:n.pagination.page||1,size:n.pagination.limit||20},r=await O(t),c=(a=(i=r.data)==null?void 0:i.data)==null?void 0:a.data;n.pagination.total=(D=(o=r.data)==null?void 0:o.data)==null?void 0:D.total,n.dataList=c,n.loading=!1};return B(async()=>{await p()}),(e,t)=>{const r=j,c=R,l=G,i=E;return F(),$("div",X,[u(r,{ref_key:"refSearch",ref:v,config:m(T)},null,8,["config"]),u(c,{ref_key:"refTable",ref:W,config:m(n),class:"card-table"},null,8,["config"]),u(l,{ref_key:"refForm",ref:_,config:m(s)},null,8,["config"]),u(i,{ref_key:"refDetail",ref:h,config:m(y)},{default:A(()=>[u(U,{"task-info":m(b).taskInfo,"task-id":"1"},null,8,["task-info"])]),_:1},8,["config"])])}}});export{pe as default};
