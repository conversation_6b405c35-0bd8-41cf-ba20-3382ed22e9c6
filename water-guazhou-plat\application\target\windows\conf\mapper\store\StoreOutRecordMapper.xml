<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.StoreOutRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->sout.id,
                           sout.code,
                           sout.title,
                           sout.reimbursement,
                           sout.storehouse_id,
                           sout.receive_user_id,
                           sout.manager,
                           sout."type",
                           sout.construction_project_id,
                           sout.add_record,
                           sout.remark,
                           sout.creator,
                           sout.out_time,
                           sout.create_time,
                           sout.tenant_id<!--@sql from store_out_record sout-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.StoreOutRecord">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="title" property="title"/>
        <result column="reimbursement" property="reimbursement"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="receive_user_id" property="receiveUserId"/>
        <result column="manager" property="manager"/>
        <result column="type" property="type"/>
        <result column="construction_project_id" property="constructionProjectId"/>
        <result column="add_record" property="addRecord"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="out_time" property="outTime"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from store_out_record sout
        <where>
            <if test="code != null and code != ''">
                and sout.code like '%' || #{code} || '%'
            </if>
            <if test="title != null and title != ''">
                and sout.title like '%' || #{title} || '%'
            </if>
            <if test="storehouseId != null and storehouseId != ''">
                and sout.storehouse_id = #{storehouseId}
            </if>
            <if test="isOut != null">
                and store_out_is_out(id) = #{isOut}
            </if>
            <if test="type != null and type != ''">
                and sout.type = #{type}
            </if>
            <if test="constructionProjectId != null and constructionProjectId != ''">
                and sout.construction_project_id = #{constructionProjectId}
            </if>
            <if test="receiveUserId != null and receiveUserId != ''">
                and sout.receive_user_id = #{receiveUserId}
            </if>
            <if test="receiveDepartmentId != null and receiveDepartmentId != ''">
                and is_user_at_department(sout.receive_user_id, #{receiveDepartmentId})
            </if>
            <if test="manager != null and manager != ''">
                and sout.manager = #{manager}
            </if>
            <if test="managerDepartmentId != null and managerDepartmentId != ''">
                and is_user_at_department(sout.manager, #{managerDepartmentId})
            </if>
            <if test="creator != null and creator != ''">
                and sout.creator = #{creator}
            </if>
            <if test="creatorDepartmentId != null and creatorDepartmentId != ''">
                and is_user_at_department(sout.creator, #{creatorDepartmentId})
            </if>
            <if test="outTimeFrom != null">
                and sout.out_time >= #{outTimeFrom}
            </if>
            <if test="outTimeTo != null">
                and sout.out_time &lt;= #{outTimeTo}
            </if>
            <if test="fromTime != null">
                and sout.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and sout.create_time &lt;= #{toTime}
            </if>
            <if test="reimbursement != null">
                and sout.reimbursement = #{reimbursement}
            </if>
            and sout.tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update store_out_record
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="reimbursement != null">
                reimbursement = #{reimbursement},
            </if>
            <if test="storehouseId != null">
                storehouse_id = #{storehouseId},
            </if>
            <if test="receiveUserId != null">
                receive_user_id = #{receiveUserId},
            </if>
            <if test="manager != null">
                manager = #{manager},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="constructionProjectId != null">
                construction_project_id = #{constructionProjectId},
            </if>
            <if test="addRecord != null">
                add_record = #{addRecord},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="childrenId" resultType="java.lang.String">
        select id
        from store_out_record_detail
        where main_id = #{id}
    </select>

    <select id="getIsOut" resultType="boolean">
        select store_out_is_out(#{id})
    </select>
</mapper>