/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Data;

/**
 * ganfrar 获取api用
 */
@Data
public class Target {
    public String target;
    public String formula;
    public String prop;
    public String name;
    public String type;

    public Target() {
    }

    public Target(String target, String formula, String prop, String name, String type) {
        this.target = target;
        this.formula = formula;
        this.prop = prop;
        this.name = name;
        this.type = type;
    }
}
