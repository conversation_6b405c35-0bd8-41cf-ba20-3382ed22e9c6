package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordSaveRequest;

public interface StoreInRecordService {
    /**
     * 分页条件查询入库单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<StoreInRecord> findAllConditional(StoreInRecordPageRequest request);

    /**
     * 保存入库单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    StoreInRecord save(StoreInRecordSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(StoreInRecord entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
