<template>
  <Search
    ref="refSearch"
    :config="SearchConfig"
    class="search"
  ></Search>
  <FormTable
    :config="TableConfig"
    class="table-box"
  ></FormTable>
  <DialogForm
    ref="refDialog"
    :config="DialogConfig"
  ></DialogForm>
</template>
<script lang="ts" setup>
import {
  AddDmaPartitionPumpList,
  DeleteDmaPartitionPumpList,
  GetDmaPartitionPumpList
} from '@/api/mapservice/dma/partitionResources'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'

const props = defineProps<{ partition?: NormalOption }>()
const refSearch = ref<ISearchIns>()
const refDialog = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '管径', field: 'caliber', clearable: false },
    { type: 'input', label: '厂家', field: 'brand', clearable: false }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          iconifyIcon: 'ep:search',
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          perm: true,
          iconifyIcon: 'ep:refresh',
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            // refreshData()
          }
        },
        {
          perm: true,
          iconifyIcon: 'ep:circle-plus',
          text: '新增',
          type: 'success',
          click: () => handleAou()
        }
      ]
    }
  ]
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '泵房类型', prop: 'type' },
    { label: '位置', prop: 'position' },
    { label: '模式', prop: 'mode' },
    { label: '厂家', prop: 'brand' },
    { label: '进水口计量', prop: 'inWaterMetering' },
    { label: '出水口计量', prop: 'outWaterMetering' },
    { label: '管径', prop: 'caliber' },
    { label: '备注', prop: 'remark' },
    { label: '现场图片', prop: 'img', image: true }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '编辑',
      iconifyIcon: 'ep:edit',
      click: row => handleAou(row)
    },
    {
      perm: true,
      text: '删除',
      iconifyIcon: 'ep:delete',
      type: 'danger',
      click: row => handleDelete(row)
    }
  ]
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaPartitionPumpList({
      ...query,
      partitionId: props.partition?.value,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    })
    const data = res.data.data || {}
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const handleAou = (row?: any) => {
  DialogConfig.defaultValue = {
    ...(row || {})
  }
  DialogConfig.title = row ? '编辑泵站' : '添加泵站'
  refDialog.value?.openDialog()
}
const handleDelete = (row?: any) => {
  const ids = row ? [row.id] : []
  if (!ids.length) {
    SLMessage.error('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除?', '提示信息')
    .then(async () => {
      try {
        const res = await DeleteDmaPartitionPumpList(ids)
        if (res.data.code === 200) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      //
    })
}
const DialogConfig = reactive<IDialogFormConfig>({
  title: '添加流量表',
  dialogWidth: 650,
  labelWidth: 130,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '泵房类型',
          field: 'type',
          rules: [{ required: true, message: '请输入泵房类型' }]
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '位置',
          field: 'position'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '模式',
          field: 'mode',
          rules: [{ required: true, message: '请输入模式' }]
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '厂家',
          field: 'brand'
        },
        {
          lg: 24,
          xl: 12,
          type: 'textarea',
          label: '进水口计量情况',
          field: 'inWaterMetering'
        },
        {
          lg: 24,
          xl: 12,
          type: 'textarea',
          label: '出水口计量情况',
          field: 'outWaterMetering'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input-number',
          label: '管径',
          field: 'caliber'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'image',
          label: '现场图片',
          field: 'img'
        }
      ]
    }
  ],
  submit: async params => {
    DialogConfig.submitting = true
    try {
      const res = await AddDmaPartitionPumpList({
        ...params,
        partitionId: props.partition?.value
      })
      if (res.data.code === 200) {
        SLMessage.success('提交成功')
        refreshData()
        refDialog.value?.closeDialog()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('提交失败')
    }
    DialogConfig.submitting = false
  }
})
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.search {
  margin: 0 -20px 10px -20px;
}
.table-box {
  height: calc(100% - 40px);
}
</style>
