import{_ as y}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{d as k}from"./index-CaaU9niG.js";import{d as C,c as _,r as c,S as x,b as p,o as D,g as L,n as W,q as m,i as d,D as F}from"./index-r0dFAfgr.js";import{S as P,G as q}from"./setting-D9qCoDRn.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const B={class:"wrapper"},w=C({__name:"index",setup(z){const u=_(),l=_(),h=c({filters:[{type:"input",label:"名称",field:"name"},{type:"btn-group",btns:[{text:"查询",click:()=>s(),perm:!0},{text:"新增",click:()=>f(),perm:!0}]}]}),i=c({dataList:[],columns:[{minWidth:220,label:"名称",prop:"name"},{minWidth:220,label:"类型",prop:"type"},{minWidth:220,label:"角色",prop:"roles"}],pagination:{refreshData:({page:e,size:t})=>{i.pagination.page=e||1,i.pagination.limit=t||20,s()}},operationWidth:80,operationHeaderAlign:"center",operations:[{perm:!0,text:"编辑",click:e=>f(e)}]}),r=c({dialogWidth:500,title:"新增",labelPosition:"right",group:[{fields:[{type:"input",label:"配置项名称",field:"name",readonly:!0},{type:"input",label:"配置项类型",field:"type",readonly:!0},{type:"select",label:"关联角色",multiple:!0,field:"roles",rules:[{required:!0,message:"请选择关联角色"}]}]}],submit:e=>{x("确定提交？","提示信息").then(()=>{var o;const t={...e,roles:(o=e.roles)==null?void 0:o.join(",")};r.submitting=!0,P(t).then(a=>{var n;a.data.code===200?(p.success(a.data.message),s(),(n=l.value)==null||n.closeDialog()):p.error(a.data.messsage)}).catch(()=>{p.error("系统错误")}).finally(()=>{r.submitting=!1})}).catch(()=>{})}}),f=e=>{var t;r.title=e?"编辑":"新增",r.defaultValue={...e||{}},(t=l.value)==null||t.openDialog()},s=async()=>{var o,a,n,g;const e=(a=(o=u.value)==null?void 0:o.queryParams)==null?void 0:a.name,t=await q({page:i.pagination.page||1,size:i.pagination.limit||20,name:e});i.dataList=(n=t.data)==null?void 0:n.data.data,i.pagination.total=(g=t.data)==null?void 0:g.data.total},b=()=>{k({page:1,size:99999}).then(e=>{var o;const t=r.group[0].fields[2];t&&(t.options=(o=e.data.data)==null?void 0:o.map(a=>{const n=F(a.id.id);return{label:a.name,value:n,id:n}}))})};return D(()=>{b(),s()}),(e,t)=>{const o=v,a=S,n=y;return L(),W("div",B,[m(o,{ref_key:"refSearch",ref:u,config:d(h)},null,8,["config"]),m(a,{config:d(i),class:"card-table"},null,8,["config"]),m(n,{ref_key:"refForm",ref:l,config:d(r)},null,8,["config"])])}}});export{w as default};
