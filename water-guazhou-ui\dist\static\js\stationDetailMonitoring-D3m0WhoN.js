import{_ as _e}from"./CardSearch-CB_HNR-Q.js";import{d as ye,j as ke,a6 as xe,bF as f,r as v,c as _,am as Ie,f1 as we,a8 as Z,s as G,bB as E,D as ee,ay as Te,g as b,n as y,p as u,q as n,av as Ce,i as S,F as p,h as te,aB as Ne,aJ as Se,f2 as ae,an as T,cs as le,c5 as Le,aq as Ae,cE as De,cU as Ye,dB as Oe,dC as Me,dF as Ge,dA as Ee,al as oe,aj as se,C as Fe}from"./index-r0dFAfgr.js";import{_ as Re}from"./index-B69llYYW.js";import{_ as Pe}from"./index-C9hz-UZb.js";/* empty css                         */import{_ as je}from"./Search-NSrhrIa_.js";import{l as ne}from"./echart-BHaUp-st.js";import{u as Be}from"./useStation-DJgnSZIA.js";import{s as Ve,t as ze,d as He}from"./zhandian-YaGuQZe6.js";import{s as Ue,a as Je}from"./headwaterMonitoring-BgK7jThW.js";import{g as Qe}from"./URLHelper-B9aplt5w.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";const We=[{label:"0时",value:0},{label:"1时",value:1},{label:"2时",value:2},{label:"3时",value:3},{label:"4时",value:4},{label:"5时",value:5},{label:"6时",value:6},{label:"7时",value:7},{label:"8时",value:8},{label:"9时",value:9},{label:"10时",value:10},{label:"11时",value:11},{label:"12时",value:12},{label:"13时",value:13},{label:"14时",value:14},{label:"15时",value:15},{label:"16时",value:16},{label:"17时",value:17},{label:"18时",value:18},{label:"19时",value:19},{label:"20时",value:20},{label:"21时",value:21},{label:"22时",value:22},{label:"23时",value:23}],$e={style:{"margin-top":"10px"}},qe={key:0,class:"content"},Ke={class:"top"},Xe={style:{width:"100%",height:"100%"},class:"card-table"},Ze={style:{display:"flex","justify-content":"space-between",height:"85%"}},et={class:"card-table"},tt={class:"bottom"},at={style:{width:"32%",overflow:"hidden"}},lt={class:"image-slot"},ot={key:1,class:"bottom-1"},st={class:"no-pictures"},nt={style:{width:"32%"}},rt={style:{width:"32%"}},it={key:1,class:"content1"},ct={key:0},ut={key:2,class:"content1"},dt={key:3,class:"content"},pt=ye({__name:"stationDetailMonitoring",props:{stationId:{},monitor:{}},emits:["hiddenLoading","update:model-value"],setup(re,{emit:ie}){const C=ke(),{getStationAttrGroups:ce}=Be(),ue=ie,z=xe(),k=re,O=f().date(),a=v({activeName:"status",chartOption:null,chartOption1:null,searchActiveName:"echarts",groupTab:"",currentGroupTabs:[],stationInfo:null,imgs:[],stationInfoColumns:[]}),F=_(),H=_(),U=_(),J=_(),L=_(),R=_(),P=_(),j=_(),Q=_();let A=v([]),i=v([]);Ie(()=>[k.stationId,k.monitor,a.searchActiveName],async(e,t)=>{var l,o,c,s;if(e[0]&&(t[0]!==e[0]||((l=t[1])==null?void 0:l.name)!==((o=e[1])==null?void 0:o.name))){const m=await Ve(k.stationId);a.stationInfo=m.data;const d=await we((c=m.data)==null?void 0:c.projectId);a.stationInfo.projectName=(s=d==null?void 0:d.data)==null?void 0:s.name,a.imgs=a.stationInfo.imgs?a.stationInfo.imgs.split(","):[],console.log(" state.imgs",e),console.log(" state.imgs111",t),ge(),setTimeout(async()=>{de(a.stationInfo)},1e3)}t[2]!==e[2]&&a.searchActiveName==="echarts"&&await V()});const de=async e=>{var l,o;const t=(l=e.location)==null?void 0:l.split(",");(t==null?void 0:t.length)===2&&((o=F.value)==null||o.setMarker(t,{icon:Qe("泵站.png")},()=>{W(e)})),W(e)},W=async e=>{var o,c;const t=[{label:"名称",value:e.name},{label:"类型",value:e.type},{label:"所属项目",value:e.projectName},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"备注",value:e.remark}],l=(o=e.location)==null?void 0:o.split(",");(l==null?void 0:l.length)===2&&((c=F.value)==null||c.setListInfoWindow({point:l,values:t,title:e.name}))},x=v({loading:!0,currentRow:[],currentRowKey:"property",highlightCurrentRow:!0,dataList:[],columns:[{prop:"propertyName",label:"检测项名称"},{prop:"value",label:"检测项数据"},{prop:"collectionTime",label:"采集时间",formatter:e=>e.collectionTime>0?f(e.collectionTime).format("YYYY-MM-DD HH:mm:ss"):"-"}],operations:[],pagination:{hide:!0},handleRowClick:e=>{x.currentRow=e,V()}}),h=v({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"alarmInfo",label:"报警描述"},{prop:"time",label:"报警时间",formatter:(e,t)=>t?f(t).format("YYYY-MM-DD HH:mm:ss"):""}],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:t})=>{h.pagination.page=e,h.pagination.limit=t,h.dataList=A.slice((e-1)*t,e*t)}}}),I=v({loading:!0,dataList:[],indexVisible:!0,columns:[],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:t})=>{var l;I.pagination.page=e,I.pagination.limit=t,I.dataList=(l=i==null?void 0:i.tableDataList)==null?void 0:l.slice((e-1)*t,e*t)}}}),w=v({defaultParams:{start:[f().date(O-2).format("YYYY-MM-DD"),f().date(O).format("YYYY-MM-DD")],filterStart:[0,23],group:"",attributeId:""},filters:[{type:"daterange",label:"选中日期",field:"start",clearable:!1,width:"100px"},{label:"时间",type:"range",rangeType:"select",field:"filterStart",options:JSON.parse(JSON.stringify(We)),startPlaceHolder:"0时",endPlaceHolder:"23时",startOptionDisabled:(e,t)=>t&&Number(t)<e.value,endOptionDisabled:(e,t)=>t&&e.value<=Number(t)},{label:"监测组",field:"group",type:"select",clearable:!1,options:[],onChange:e=>$(e)},{label:"曲线类型",field:"attributeId",type:"select",clearable:!1,options:[],hidden:Z(()=>a.searchActiveName==="list")}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>q(),svgIcon:G(oe)},{perm:!0,type:"warning",text:"导出",svgIcon:G(se),hide:()=>a.searchActiveName!=="list",click:()=>{var e;(e=H.value)==null||e.exportTable()}}]}]}),pe=v({defaultParams:{start:[f().date(O-2).format("YYYY-MM-DD"),f().date(O).format("YYYY-MM-DD")]},filters:[{type:"daterange",label:"选择时间",field:"start",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>B(),svgIcon:G(oe)},{perm:!0,type:"warning",text:"导出",svgIcon:G(se),click:()=>{var e;(e=J.value)==null||e.exportTable()}}]}]}),me=v({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"当前状态",value:"status"},{label:"数据查询",value:"search"},{label:"报警信息",value:"alarm"}],handleTabClick:e=>{console.log(e.props.name),a.activeName=e.props.name,a.activeName==="search"?(console.log(a.currentGroupTabs),E(()=>{var l;const t=(l=w.filters)==null?void 0:l.find(o=>o.field==="group");t.options=a.currentGroupTabs,w.defaultParams={...w.defaultParams,group:a.currentGroupTabs[0].value},$(a.currentGroupTabs[0].value),q()})):a.activeName==="alarm"&&E(()=>{B("range")})}}),$=e=>{var o,c;const t=a.currentGroupTabs.find(s=>s.value===e),l=(o=w.filters)==null?void 0:o.find(s=>(s==null?void 0:s.field)==="attributeId");l.options=t.children.map(s=>({label:s.name,value:s.id,data:ee(s.deviceId)+"."+s.attr,unit:s.unit?"("+s.unit+")":""})),w.defaultParams={...w.defaultParams,attributeId:t.children[0].id},(c=j.value)==null||c.resetForm(),console.log(e)},q=async()=>{var r,D,g,M;const e=((r=j.value)==null?void 0:r.queryParams)||{},[t,l]=e.start||[],[o,c]=e.filterStart||[],s={...e,filterStart:o||0,filterEnd:c||23,queryType:"10m",stationId:k.stationId,group:e==null?void 0:e.group,start:t?f(t).startOf("day").valueOf():null,end:l?f(l).endOf("day").valueOf():null};i=(D=(await Ue(s)).data)==null?void 0:D.data;const d=i==null?void 0:i.tableInfo.map(N=>({prop:N.columnValue,label:N.columnName,unit:N.unit?"("+N.unit+")":""}));console.log(d),I.columns=d,I.dataList=(g=i==null?void 0:i.tableDataList)==null?void 0:g.slice(0*20,20),I.pagination.total=(M=i==null?void 0:i.tableDataList)==null?void 0:M.length,I.loading=!1,fe(e==null?void 0:e.attributeId)},fe=e=>{var s,m,d;const t=ne(),o=(m=((s=w.filters)==null?void 0:s.find(r=>r.field==="attributeId")).options)==null?void 0:m.find(r=>r.value===e);t.yAxis[0].name=o.label+(o.unit?o.unit:""),t.xAxis.data=i==null?void 0:i.tableDataList.map(r=>r.ts),console.log(e+"."+o.data,i==null?void 0:i.tableDataList);const c={name:o.label,smooth:!0,data:i==null?void 0:i.tableDataList.map(r=>r[o.data]),type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:C.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:C.isDark?"#ffffff":"#000000"}}]}};t.series=[c],(d=P.value)==null||d.clear(),E(()=>{L.value&&z.listenTo(L.value,()=>{var r;a.chartOption1=t,(r=P.value)==null||r.resize()})})},K=v({filters:[{type:"radio-button",label:"",field:"groupTab",options:Z(()=>a.currentGroupTabs),onChange:e=>{e&&X(e)}}]}),ge=()=>{a.activeName="status",console.log("refreshData"),be()},be=async()=>{var t,l;const e=await ce(k.stationId);a.currentGroupTabs=e,K.defaultParams={groupTab:(t=e[0])==null?void 0:t.id},(l=Q.value)==null||l.resetForm(),await X(k.monitor.name),await B()},B=async e=>{var m;const t=(m=U.value)==null?void 0:m.queryParams;h.loading=!0;let l=f().startOf("month").valueOf(),o=f().endOf("month").valueOf();const[c,s]=(t==null?void 0:t.start)||[];e==="range"&&(l=l?f(c).startOf("day").valueOf():null,o=o?f(s).endOf("day").valueOf():null),ze({stationId:k.stationId,start:l,end:o,page:1,size:20}).then(d=>{console.log("res",d),A=d.data.data,h.dataList=A==null?void 0:A.data,h.pagination.total=A.total,h.loading=!1})},X=async e=>{x.loading=!0;const t=await He(k.stationId,e);x.dataList=t.data;const l=t==null?void 0:t.data[0];x.currentRow=l,await V(),console.log(x.currentRow),x.loading=!1},V=async()=>{var m,d;const e=x.currentRow,l=(m=(await Je({deviceId:ee(e.deviceId),attr:e.property})).data)==null?void 0:m.data,o=ne(),c=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];o.xAxis.data=l.todayDataList.map(r=>r.ts),o.yAxis[0].name=e.propertyName.concat(e.unit?"("+e.unit+")":""),o.yAxis.length===2&&o.yAxis.pop();const s=c.map(r=>{const D=l[r.key].map(g=>g.value);return{name:r.name,smooth:!0,data:D,type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:C.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:C.isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}}});o.series=s,(d=R.value)==null||d.clear(),await E(()=>{L.value&&z.listenTo(L.value,()=>{var r;a.chartOption=o,(r=R.value)==null||r.resize()})}),ue("hiddenLoading")};return(e,t)=>{const l=Le,o=je,c=Ae,s=Te("VChart"),m=De,d=Ye,r=Oe,D=Me,g=Pe,M=Re,N=Ge,ve=Ee,he=_e;return b(),y("div",null,[u("div",{style:Ce([{position:"absolute",top:"55px",width:"100%","z-index":"9999"},{background:S(C).isDark?"#1A293C":"#FFFFFF"}])},[n(l,{modelValue:a.activeName,"onUpdate:modelValue":t[0]||(t[0]=Y=>a.activeName=Y),config:me},null,8,["modelValue","config"])],4),u("div",$e,[a.activeName==="status"?(b(),y("div",qe,[u("div",Ke,[u("div",Xe,[n(o,{ref_key:"refGroup",ref:Q,config:K},null,8,["config"]),u("div",Ze,[u("div",et,[n(c,{config:x,class:"left-table"},null,8,["config"])]),u("div",{ref_key:"echartsDiv",ref:L,class:"chart-box"},[n(s,{ref_key:"refChart",ref:R,theme:S(C).isDark?"dark":"light",option:a.chartOption},null,8,["theme","option"])],512)])])]),u("div",tt,[u("div",at,[t[3]||(t[3]=u("div",{class:"title"},"现场实景",-1)),n(g,{class:"chart-box",overlay:""},{default:p(()=>[a.imgs.length>0?(b(),te(D,{key:0,trigger:"click",height:"35vh"},{default:p(()=>[n(r,null,{default:p(()=>[(b(!0),y(Ne,null,Se(a.imgs,Y=>(b(),te(d,{key:Y,src:Y,style:{height:"100%",width:"100%"}},{error:p(()=>[u("div",lt,[n(m,null,{default:p(()=>[n(S(ae))]),_:1})])]),_:2},1032,["src"]))),128))]),_:1})]),_:1})):T("",!0),a.imgs.length===0?(b(),y("div",ot,[u("div",st,[u("div",null,[n(m,{size:"110px",color:"#E4E7F1"},{default:p(()=>[n(S(ae))]),_:1})]),t[2]||(t[2]=u("div",{style:{width:"70%",margin:"20px auto"}},[u("span",{style:{color:"#54728f"}},"请前往"),u("span",{style:{color:"#54728f"}},"“数据平台”>“档案基础数据”>“现场实品图”"),u("span",{style:{color:"#54728f"}},"界面上传实景图")],-1))])])):T("",!0)]),_:1})]),u("div",nt,[t[4]||(t[4]=u("div",{class:"title"},"泵房信息",-1)),n(g,{class:"chart-box",title:"",overlay:""},{default:p(()=>[n(M,{ref_key:"refAmap",ref:F,light:!S(C).isDark,"hide-input":!0,"init-center-mark":!1},null,8,["light"])]),_:1})]),u("div",rt,[t[5]||(t[5]=u("div",{class:"title"},"报警信息",-1)),n(g,{class:"chart-box",title:"",overlay:""},{default:p(()=>[n(c,{config:h},null,8,["config"])]),_:1})])])])):T("",!0),a.activeName==="search"?(b(),y("div",it,[n(g,{class:"",title:" ",overlay:""},{title:p(()=>[n(o,{ref_key:"cardSearch",ref:j,config:w},null,8,["config"])]),default:p(()=>[n(g,{title:" ",class:"card-table"},{right:p(()=>[n(ve,{modelValue:a.searchActiveName,"onUpdate:modelValue":t[1]||(t[1]=Y=>a.searchActiveName=Y)},{default:p(()=>[n(N,{label:"echarts"},{default:p(()=>[n(S(le),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),n(N,{label:"list"},{default:p(()=>[n(S(le),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:p(()=>[a.searchActiveName==="list"?(b(),y("div",ct,[n(c,{ref_key:"refTable",ref:H,config:I,class:"chart-box"},null,8,["config"])])):T("",!0),a.searchActiveName==="echarts"?(b(),y("div",{key:1,ref_key:"echartsDiv",ref:L,class:"chart-box"},[n(s,{ref_key:"refChart1",ref:P,option:a.chartOption1},null,8,["option"])],512)):T("",!0)]),_:1})]),_:1})])):T("",!0),a.activeName==="alarm"?(b(),y("div",ut,[n(g,{title:" ",overlay:""},{title:p(()=>[n(he,{ref_key:"alarmCardSearch",ref:U,config:pe},null,8,["config"])]),default:p(()=>[n(c,{ref_key:"refAlarmTable",ref:J,config:h,class:"chart-box"},null,8,["config"])]),_:1})])):T("",!0),a.activeName==="control"?(b(),y("div",dt)):T("",!0)])])}}}),Ct=Fe(pt,[["__scopeId","data-v-0cdc526c"]]);export{Ct as default};
