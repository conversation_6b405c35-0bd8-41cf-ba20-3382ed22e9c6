package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 *
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PartitionPumpHouse {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_TYPE)
    private String type;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_POSITION)
    private String position;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_MODE)
    private String mode;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_BRAND)
    private String brand;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_IN_WATER_METERING)
    private String inWaterMetering;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_OUT_WATER_METERING)
    private String outWaterMetering;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_CALIBER)
    private String caliber;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_REMARK)
    private String remark;

    @TableField(ModelConstants.PIPE_PARTITION_PUMP_HOUSE_IMG)
    private String img;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
