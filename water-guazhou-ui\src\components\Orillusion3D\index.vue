<script setup lang="ts">
import { Color, Vector3 } from '@orillusion/core'
import { Scenes, Model, addText } from './modelClass'

const d3 = ref()

onMounted(() => {
  init()
})

function init() {
  const canvas = document.getElementById('canvas11')
  d3.value = new Scenes({ stats: true })
  d3.value.run()
  const cs = new Model({
    scenes: d3.value,
    url: 'https://cdn.orillusion.com/PBR/SheenChair/SheenChair.gltf'
  })

  setTimeout(() => {
    addText(d3.value, { resize: [100, 100], setXY: [100, 100], text: [{ resize: [40, 20], setXY: [10, 10], text: 'asdasd', color: new Color(1, 1, 1, 1) }] })
    addText(d3.value, { resize: [100, 100], setXY: [-100, -100], text: [] })
  }, 1000)
}

function setCamera() {
  // debugger
  // d3.value.camera.lookAt(new Vector3(120, -20, 0), new Vector3(0, 0, 0), new Vector3(0, 0, 200))
  d3.value.controller.setCamera(15, -15, 2, new Vector3(0, 0, 0))
}
</script>

<template>
  <canvas
    id="canvas11"
    width="1000"
    height="500"
  />
  <div class="btn">
    <el-button @click="setCamera">
      相机位置
    </el-button>
    <el-button type="primary">
      Primary
    </el-button>
    <el-button type="success">
      Success
    </el-button>
    <el-button type="info">
      Info
    </el-button>
    <el-button type="warning">
      Warning
    </el-button>
    <el-button
      type="danger"
      @click="init"
    >
      重置
    </el-button>
  </div>
</template>

<style scoped>
.canvas {
  height: 400px;
  width: 500px;
}

.btn {
  float: right;
}
</style>
