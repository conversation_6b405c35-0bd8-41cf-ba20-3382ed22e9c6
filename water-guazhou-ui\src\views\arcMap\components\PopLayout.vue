<template>
  <div
    ref="refContainer"
    class="arc-infowindow"
    :style="{
      backgroundColor: backgroundColor
    }"
  >
    <div class="arc-infowindow-wrapper">
      <div
        class="title-wrapper"
        @click="highlight"
      >
        <span
          class="title"
          :class="status"
        >
          {{ title }}
        </span>
        <el-icon
          v-if="showMore"
          class="btn btn-more"
          @click.stop="$emit('more')"
        >
          <More />
        </el-icon>
        <el-icon
          v-if="showBack"
          class="btn btn-back"
          @click.stop="$emit('back')"
        >
          <Back />
        </el-icon>
        <el-icon
          class="btn btn-close"
          @click.stop="toggle(false)"
        >
          <Close />
        </el-icon>
      </div>
      <div class="content-wrapper">
        <slot> </slot>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js'
import { Close, More, Back } from '@element-plus/icons-vue'

const refContainer = ref<HTMLDivElement>()
const props = defineProps<{
  title?: string
  x?: number
  y?: number
  longitude?: number
  latitude?: number
  offsetx?: number
  offsety?: number
  backgroundColor?: string
  status?: IStatus
  showMore?: boolean
  showBack?: boolean
}>()
const emit = defineEmits(['more', 'back', 'toggle'])

const highlight = () => {
  refContainer.value?.parentElement?.appendChild(refContainer.value)
}
const refreshPosition = (mapView?: __esri.MapView) => {
  if (!refContainer.value || !mapView) return
  const point = new Point({
    x: props?.x,
    y: props?.y,
    longitude: props?.longitude,
    latitude: props?.latitude,
    spatialReference: mapView?.spatialReference
  })
  const screenPoint = mapView?.toScreen(point)
  refContainer.value.style.left = (screenPoint?.x || 0) + (props.offsetx || 0) + 'px'
  refContainer.value.style.top = (screenPoint?.y || 0) + (props.offsety || 0) - 10 + 'px' // 默认要让出下面的三角的距离，所以要多减10px
}
const toggle = (flag?: boolean) => {
  emit('toggle', flag)
}
defineExpose({
  toggle,
  refreshPosition,
  highlight
})
</script>
<style lang="scss" scoped>
.arc-infowindow {
  user-select: none;
  transform: translateX(-100%) translateY(-100%);
  position: absolute;
  background-color: rgba(21, 45, 68, 0.9);
  box-shadow: 0 0 1px rgba(21, 45, 68, 1);
  border-radius: 4px;
  &::before {
    content: ' ';
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateY(50%) translateX(-50%) rotate(45deg);
    display: block;
    // border: 1px solid #21667b;
    background-color: transparent;
    width: 0;
    height: 0;
    border: 5px solid rgba(21, 45, 68, 0.9);
    border-top-color: transparent;
    border-left-color: transparent;
  }
}
.arc-infowindow-wrapper {
  min-width: 100px;
}
.title-wrapper {
  background-color: #1c2b3f;
  height: 32px;
  padding: 0 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 150px;
  .title {
    overflow: hidden;
    display: block;
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    &.normal,
    &.danger,
    &.online,
    &.offline,
    &.warning {
      padding-left: 15px;
      &::before {
        content: ' ';
        position: absolute;
        width: 8px;
        height: 8px;
        left: 0;
        border-radius: 5px 5px;
        top: 5px;
      }
    }
    &.normal {
      &::before {
        background-color: #33a6ee;
      }
    }
    &.danger {
      &::before {
        background-color: #d5584b;
      }
    }
    &.online {
      &::before {
        background-color: #5cb95c;
      }
    }
    &.offline {
      &::before {
        background-color: #686f77;
      }
    }
    &.warning {
      &::before {
        background-color: #e4a90f;
      }
    }
  }
}
.content-wrapper {
  padding: 8px;
}
.btn {
  cursor: pointer;
  margin-left: 8px;
}
</style>
