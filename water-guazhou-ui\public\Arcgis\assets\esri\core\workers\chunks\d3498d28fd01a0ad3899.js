"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[142],{5732:(e,t,i)=>{i.d(t,{c:()=>n});var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},69801:(e,t,i)=>{i.d(t,{WJ:()=>l,Xq:()=>a});var n,s,r=i(70586),o=i(44553);(s=n||(n={}))[s.ALL=0]="ALL",s[s.SOME=1]="SOME";class a{constructor(e,t,i){this._namespace=e,this._storage=t,this._removeFunc=!1,this._hit=0,this._miss=0,this._storage.register(this),this._namespace+=":",i&&(this._storage.registerRemoveFunc(this._namespace,i),this._removeFunc=!0)}destroy(){this._storage.clear(this._namespace),this._removeFunc&&this._storage.deregisterRemoveFunc(this._namespace),this._storage.deregister(this),this._storage=null}get namespace(){return this._namespace.slice(0,-1)}get hitRate(){return this._hit/(this._hit+this._miss)}get size(){return this._storage.size}get maxSize(){return this._storage.maxSize}resetHitRate(){this._hit=this._miss=0}put(e,t,i,n=0){this._storage.put(this._namespace+e,t,i,n)}get(e){const t=this._storage.get(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}pop(e){const t=this._storage.pop(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}updateSize(e,t,i){this._storage.updateSize(this._namespace+e,t,i)}clear(){this._storage.clear(this._namespace)}clearAll(){this._storage.clearAll()}getStats(){return this._storage.getStats()}resetStats(){this._storage.resetStats()}}class l{constructor(e=10485760){this._maxSize=e,this._db=new Map,this._size=0,this._hit=0,this._miss=0,this._removeFuncs=new o.Z,this._users=new o.Z}destroy(){this.clearAll(),this._removeFuncs.clear(),this._users.clear(),this._db=null}register(e){this._users.push(e)}deregister(e){this._users.removeUnordered(e)}registerRemoveFunc(e,t){this._removeFuncs.push([e,t])}deregisterRemoveFunc(e){this._removeFuncs.filterInPlace((t=>t[0]!==e))}get size(){return this._size}get maxSize(){return this._maxSize}set maxSize(e){this._maxSize=Math.max(e,0),this._checkSizeLimit()}put(e,t,i,s){const r=this._db.get(e);if(r&&(this._size-=r.size,this._db.delete(e),r.entry!==t&&this._notifyRemove(e,r.entry,n.ALL)),i>this._maxSize)return void this._notifyRemove(e,t,n.ALL);if(void 0===t)return void console.warn("Refusing to cache undefined entry ");if(!i||i<0)return void console.warn("Refusing to cache entry with invalid size "+i);const o=1+Math.max(s,-3)- -3;this._db.set(e,{entry:t,size:i,lifetime:o,lives:o}),this._size+=i,this._checkSizeLimit()}updateSize(e,t,i){const s=this._db.get(e);if(s&&s.entry===t){for(this._size-=s.size;i>this._maxSize;){const s=this._notifyRemove(e,t,n.SOME);if(!((0,r.pC)(s)&&s>0))return void this._db.delete(e);i=s}s.size=i,this._size+=i,this._checkSizeLimit()}}pop(e){const t=this._db.get(e);if(t)return this._size-=t.size,this._db.delete(e),++this._hit,t.entry;++this._miss}get(e){const t=this._db.get(e);if(void 0!==t)return this._db.delete(e),t.lives=t.lifetime,this._db.set(e,t),++this._hit,t.entry;++this._miss}getStats(){const e={Size:Math.round(this._size/1048576)+"/"+Math.round(this._maxSize/1048576)+"MB","Hit rate":Math.round(100*this._getHitRate())+"%",Entries:this._db.size.toString()},t={},i=new Array;this._db.forEach(((e,n)=>{const s=e.lifetime;i[s]=(i[s]||0)+e.size,this._users.forAll((i=>{const s=i.namespace;if(n.startsWith(s)){const i=t[s]||0;t[s]=i+e.size}}))}));const n={};this._users.forAll((e=>{const i=e.namespace;if(!isNaN(e.hitRate)&&e.hitRate>0){const s=t[i]||0;t[i]=s,n[i]=Math.round(100*e.hitRate)+"%"}else n[i]="0%"}));const s=Object.keys(t);s.sort(((e,i)=>t[i]-t[e])),s.forEach((i=>e[i]=Math.round(t[i]/2**20)+"MB / "+n[i]));for(let t=i.length-1;t>=0;--t){const n=i[t];n&&(e["Priority "+(t+-3-1)]=Math.round(n/this.size*100)+"%")}return e}resetStats(){this._hit=this._miss=0,this._users.forAll((e=>e.resetHitRate()))}clear(e){this._db.forEach(((t,i)=>{i.startsWith(e)&&(this._size-=t.size,this._db.delete(i),this._notifyRemove(i,t.entry,n.ALL))}))}clearAll(){this._db.forEach(((e,t)=>this._notifyRemove(t,e.entry,n.ALL))),this._size=0,this._db.clear()}_getHitRate(){return this._hit/(this._hit+this._miss)}_notifyRemove(e,t,i){let n;return this._removeFuncs.some((s=>{if(e.startsWith(s[0])){const e=s[1](t,i);return"number"==typeof e&&(n=e),!0}return!1})),n}_checkSizeLimit(){if(!(this._size<=this._maxSize))for(const[e,t]of this._db){if(this._db.delete(e),t.lives<=1){this._size-=t.size;const i=this._notifyRemove(e,t.entry,n.SOME);(0,r.pC)(i)&&i>0&&(this._size+=i,t.lives=t.lifetime,t.size=i,this._db.set(e,t))}else--t.lives,this._db.set(e,t);if(this._size<=.9*this.maxSize)return}}}},66643:(e,t,i)=>{i.d(t,{Ed:()=>c,UI:()=>u,mt:()=>p,q6:()=>f,vr:()=>m});var n=i(43697),s=i(15923),r=i(70586),o=i(95330),a=i(5600),l=(i(75215),i(67676),i(52011));function c(e,t,i){return(0,o.as)(e.map(((e,n)=>t.apply(i,[e,n]))))}async function u(e,t,i){return(await(0,o.as)(e.map(((e,n)=>t.apply(i,[e,n]))))).map((e=>e.value))}function h(e){return{ok:!0,value:e}}function d(e){return{ok:!1,error:e}}async function f(e){if((0,r.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return h(await e)}catch(e){return d(e)}}async function p(e){try{return h(await e)}catch(e){return(0,o.r9)(e),d(e)}}function m(e,t){return new g(e,t)}let g=class extends s.Z{get value(){return e=this._result,(0,r.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,r.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,r.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,r.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:i}=this._abortController;this.promise=e(i),this.promise.then((e=>{this._result=h(e),this._cleanup()}),(e=>{this._result=d(e),this._cleanup()})),this._abortHandle=(0,o.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,r.hw)(this._abortHandle),this._abortController=null}};(0,n._)([(0,a.Cb)()],g.prototype,"value",null),(0,n._)([(0,a.Cb)()],g.prototype,"error",null),(0,n._)([(0,a.Cb)()],g.prototype,"finished",null),(0,n._)([(0,a.Cb)()],g.prototype,"promise",void 0),(0,n._)([(0,a.Cb)()],g.prototype,"_result",void 0),g=(0,n._)([(0,l.j)("esri.core.asyncUtils.ReactiveTask")],g)},24133:(e,t,i)=>{i.d(t,{Q:()=>a});var n=i(67676),s=i(70586),r=i(44553),o=i(88764);class a{constructor(e=9,t){this._compareMinX=h,this._compareMinY=d,this._toBBox=e=>e,this._maxEntries=Math.max(4,e||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),t&&("function"==typeof t?this._toBBox=t:this._initFormat(t)),this.clear()}destroy(){this.clear(),b.prune(),S.prune(),w.prune(),I.prune()}all(e){this._all(this._data,e)}search(e,t){let i=this._data;const n=this._toBBox;if(y(e,i))for(b.clear();i;){for(let s=0,r=i.children.length;s<r;s++){const r=i.children[s],o=i.leaf?n(r):r;y(e,o)&&(i.leaf?t(r):_(e,o)?this._all(r,t):b.push(r))}i=b.pop()}}collides(e){let t=this._data;const i=this._toBBox;if(!y(e,t))return!1;for(b.clear();t;){for(let n=0,s=t.children.length;n<s;n++){const s=t.children[n],r=t.leaf?i(s):s;if(y(e,r)){if(t.leaf||_(e,r))return!0;b.push(s)}}t=b.pop()}return!1}load(e){if(!e.length)return this;if(e.length<this._minEntries){for(let t=0,i=e.length;t<i;t++)this.insert(e[t]);return this}let t=this._build(e.slice(0,e.length),0,e.length-1,0);if(this._data.children.length)if(this._data.height===t.height)this._splitRoot(this._data,t);else{if(this._data.height<t.height){const e=this._data;this._data=t,t=e}this._insert(t,this._data.height-t.height-1,!0)}else this._data=t;return this}insert(e){return e&&this._insert(e,this._data.height-1),this}clear(){return this._data=new B([]),this}remove(e){if(!e)return this;let t,i=this._data,r=null,o=0,a=!1;const l=this._toBBox(e);for(w.clear(),I.clear();i||w.length>0;){if(i||(i=(0,s.j0)(w.pop()),r=w.data[w.length-1],o=I.pop()??0,a=!0),i.leaf&&(t=(0,n.cq)(i.children,e,i.children.length,i.indexHint),-1!==t))return i.children.splice(t,1),w.push(i),this._condense(w),this;a||i.leaf||!_(i,l)?r?(o++,i=r.children[o],a=!1):i=null:(w.push(i),I.push(o),o=0,r=i,i=i.children[0])}return this}toJSON(){return this._data}fromJSON(e){return this._data=e,this}_all(e,t){let i=e;for(S.clear();i;){if(!0===i.leaf)for(const e of i.children)t(e);else S.pushArray(i.children);i=S.pop()??null}}_build(e,t,i,n){const s=i-t+1;let r=this._maxEntries;if(s<=r){const n=new B(e.slice(t,i+1));return l(n,this._toBBox),n}n||(n=Math.ceil(Math.log(s)/Math.log(r)),r=Math.ceil(s/r**(n-1)));const o=new M([]);o.height=n;const a=Math.ceil(s/r),c=a*Math.ceil(Math.sqrt(r));x(e,t,i,c,this._compareMinX);for(let s=t;s<=i;s+=c){const t=Math.min(s+c-1,i);x(e,s,t,a,this._compareMinY);for(let i=s;i<=t;i+=a){const s=Math.min(i+a-1,t);o.children.push(this._build(e,i,s,n-1))}}return l(o,this._toBBox),o}_chooseSubtree(e,t,i,n){for(;n.push(t),!0!==t.leaf&&n.length-1!==i;){let i,n=1/0,s=1/0;for(let r=0,o=t.children.length;r<o;r++){const o=t.children[r],a=f(o),l=m(e,o)-a;l<s?(s=l,n=a<n?a:n,i=o):l===s&&a<n&&(n=a,i=o)}t=i||t.children[0]}return t}_insert(e,t,i){const n=this._toBBox,s=i?e:n(e);w.clear();const r=this._chooseSubtree(s,this._data,t,w);for(r.children.push(e),u(r,s);t>=0&&w.data[t].children.length>this._maxEntries;)this._split(w,t),t--;this._adjustParentBBoxes(s,w,t)}_split(e,t){const i=e.data[t],n=i.children.length,s=this._minEntries;this._chooseSplitAxis(i,s,n);const r=this._chooseSplitIndex(i,s,n);if(!r)return void console.log("  Error: assertion failed at PooledRBush._split: no valid split index");const o=i.children.splice(r,i.children.length-r),a=i.leaf?new B(o):new M(o);a.height=i.height,l(i,this._toBBox),l(a,this._toBBox),t?e.data[t-1].children.push(a):this._splitRoot(i,a)}_splitRoot(e,t){this._data=new M([e,t]),this._data.height=e.height+1,l(this._data,this._toBBox)}_chooseSplitIndex(e,t,i){let n,s,r;n=s=1/0;for(let o=t;o<=i-t;o++){const t=c(e,0,o,this._toBBox),a=c(e,o,i,this._toBBox),l=g(t,a),u=f(t)+f(a);l<n?(n=l,r=o,s=u<s?u:s):l===n&&u<s&&(s=u,r=o)}return r}_chooseSplitAxis(e,t,i){const n=e.leaf?this._compareMinX:h,s=e.leaf?this._compareMinY:d;this._allDistMargin(e,t,i,n)<this._allDistMargin(e,t,i,s)&&e.children.sort(n)}_allDistMargin(e,t,i,n){e.children.sort(n);const s=this._toBBox,r=c(e,0,t,s),o=c(e,i-t,i,s);let a=p(r)+p(o);for(let n=t;n<i-t;n++){const t=e.children[n];u(r,e.leaf?s(t):t),a+=p(r)}for(let n=i-t-1;n>=t;n--){const t=e.children[n];u(o,e.leaf?s(t):t),a+=p(o)}return a}_adjustParentBBoxes(e,t,i){for(let n=i;n>=0;n--)u(t.data[n],e)}_condense(e){for(let t=e.length-1;t>=0;t--){const i=e.data[t];if(0===i.children.length)if(t>0){const s=e.data[t-1],r=s.children;r.splice((0,n.cq)(r,i,r.length,s.indexHint),1)}else this.clear();else l(i,this._toBBox)}}_initFormat(e){const t=["return a"," - b",";"];this._compareMinX=new Function("a","b",t.join(e[0])),this._compareMinY=new Function("a","b",t.join(e[1])),this._toBBox=new Function("a","return {minX: a"+e[0]+", minY: a"+e[1]+", maxX: a"+e[2]+", maxY: a"+e[3]+"};")}}function l(e,t){c(e,0,e.children.length,t,e)}function c(e,t,i,n,s){s||(s=new B([])),s.minX=1/0,s.minY=1/0,s.maxX=-1/0,s.maxY=-1/0;for(let r,o=t;o<i;o++)r=e.children[o],u(s,e.leaf?n(r):r);return s}function u(e,t){e.minX=Math.min(e.minX,t.minX),e.minY=Math.min(e.minY,t.minY),e.maxX=Math.max(e.maxX,t.maxX),e.maxY=Math.max(e.maxY,t.maxY)}function h(e,t){return e.minX-t.minX}function d(e,t){return e.minY-t.minY}function f(e){return(e.maxX-e.minX)*(e.maxY-e.minY)}function p(e){return e.maxX-e.minX+(e.maxY-e.minY)}function m(e,t){return(Math.max(t.maxX,e.maxX)-Math.min(t.minX,e.minX))*(Math.max(t.maxY,e.maxY)-Math.min(t.minY,e.minY))}function g(e,t){const i=Math.max(e.minX,t.minX),n=Math.max(e.minY,t.minY),s=Math.min(e.maxX,t.maxX),r=Math.min(e.maxY,t.maxY);return Math.max(0,s-i)*Math.max(0,r-n)}function _(e,t){return e.minX<=t.minX&&e.minY<=t.minY&&t.maxX<=e.maxX&&t.maxY<=e.maxY}function y(e,t){return t.minX<=e.maxX&&t.minY<=e.maxY&&t.maxX>=e.minX&&t.maxY>=e.minY}function x(e,t,i,n,r){const a=[t,i];for(;a.length;){const t=(0,s.j0)(a.pop()),i=(0,s.j0)(a.pop());if(t-i<=n)continue;const l=i+Math.ceil((t-i)/n/2)*n;(0,o.q)(e,l,i,t,r),a.push(i,l,l,t)}}const b=new r.Z,S=new r.Z,w=new r.Z,I=new r.Z({deallocator:void 0});class v{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}}class F extends v{constructor(){super(...arguments),this.height=1,this.indexHint=new n.SO}}class B extends F{constructor(e){super(),this.children=e,this.leaf=!0}}class M extends F{constructor(e){super(),this.children=e,this.leaf=!1}}},14808:(e,t,i)=>{i.d(t,{Qc:()=>u,WU:()=>a,lt:()=>c});var n=i(19153),s=i(70171);const r={ar:[".",","],bg:[","," "],bs:[",","."],ca:[",","."],cs:[","," "],da:[",","."],de:[",","."],"de-ch":[".","’"],el:[",","."],en:[".",","],"en-au":[".",","],es:[",","."],"es-mx":[".",","],et:[","," "],fi:[","," "],fr:[","," "],"fr-ch":[","," "],he:[".",","],hi:[".",",","#,##,##0.###"],hr:[",","."],hu:[","," "],id:[",","."],it:[",","."],"it-ch":[".","’"],ja:[".",","],ko:[".",","],lt:[","," "],lv:[","," "],mk:[",","."],nb:[","," "],nl:[",","."],pl:[","," "],pt:[",","."],"pt-pt":[","," "],ro:[",","."],ru:[","," "],sk:[","," "],sl:[",","."],sr:[",","."],sv:[","," "],th:[".",","],tr:[",","."],uk:[","," "],vi:[",","."],zh:[".",","]};function o(e=(0,s.Kd)()){let t=(e=e.toLowerCase())in r;if(!t){const i=e.split("-");i.length>1&&i[0]in r&&(e=i[0],t=!0),t||(e="en")}const[i,n,o="#,##0.###"]=r[e];return{decimal:i,group:n,pattern:o}}function a(e,t){const i=o((t={...t}).locale);t.customs=i;const n=t.pattern||i.pattern;return isNaN(e)||Math.abs(e)===1/0?null:function(e,t,i){const n=(i=i||{}).customs.group,s=i.customs.decimal,r=t.split(";"),o=r[0];if((t=r[e<0?1:0]||"-"+o).includes("%"))e*=100;else if(t.includes("‰"))e*=1e3;else{if(t.includes("¤"))throw new Error("currency notation not supported");if(t.includes("E"))throw new Error("exponential notation not supported")}const a=l,c=o.match(a);if(!c)throw new Error("unable to find a number expression in pattern: "+t);return!1===i.fractional&&(i.places=0),t.replace(a,function(e,t,i){!0===(i=i||{}).places&&(i.places=0),i.places===1/0&&(i.places=6);const n=t.split("."),s="string"==typeof i.places&&i.places.indexOf(",");let r=i.places;s?r=i.places.substring(s+1):r>=0||(r=(n[1]||[]).length),i.round<0||(e=Number(e.toFixed(Number(r))));const o=String(Math.abs(e)).split("."),a=o[1]||"";if(n[1]||i.places){s&&(i.places=i.places.substring(0,s));const e=void 0!==i.places?i.places:n[1]&&n[1].lastIndexOf("0")+1;e>a.length&&(o[1]=a.padEnd(Number(e),"0")),r<a.length&&(o[1]=a.substr(0,Number(r)))}else o[1]&&o.pop();const l=n[0].replace(",","");let c=l.indexOf("0");-1!==c&&(c=l.length-c,c>o[0].length&&(o[0]=o[0].padStart(c,"0")),l.includes("#")||(o[0]=o[0].substr(o[0].length-c)));let u,h,d=n[0].lastIndexOf(",");if(-1!==d){u=n[0].length-d-1;const e=n[0].substr(0,d);d=e.lastIndexOf(","),-1!==d&&(h=e.length-d-1)}const f=[];for(let e=o[0];e;){const t=e.length-u;f.push(t>0?e.substr(t):e),e=t>0?e.slice(0,t):"",h&&(u=h,h=void 0)}return o[0]=f.reverse().join(i.group||","),o.join(i.decimal||".")}(e,c[0],{decimal:s,group:n,places:i.places,round:i.round}))}(e,n,t)}const l=/[#0,]*[#0](?:\.0*#*)?/;function c(e){const t=o((e=e||{}).locale),i=e.pattern||t.pattern,s=t.group,r=t.decimal;let a=1;if(i.includes("%"))a/=100;else if(i.includes("‰"))a/=1e3;else if(i.includes("¤"))throw new Error("currency notation not supported");const c=i.split(";");1===c.length&&c.push("-"+c[0]);const u=d(c,(t=>(t="(?:"+(0,n.Qs)(t,".")+")").replace(l,(t=>{const i={signed:!1,separator:e.strict?s:[s,""],fractional:e.fractional,decimal:r,exponent:!1},n=t.split(".");let o=e.places;1===n.length&&1!==a&&(n[1]="###"),1===n.length||0===o?i.fractional=!1:(void 0===o&&(o=e.pattern?n[1].lastIndexOf("0")+1:1/0),o&&null==e.fractional&&(i.fractional=!0),!e.places&&o<n[1].length&&(o+=","+n[1].length),i.places=o);const l=n[0].split(",");return l.length>1&&(i.groupSize=l.pop().length,l.length>1&&(i.groupSize2=l.pop().length)),"("+function(e){"places"in(e=e||{})||(e.places=1/0),"string"!=typeof e.decimal&&(e.decimal="."),"fractional"in e&&!/^0/.test(String(e.places))||(e.fractional=[!0,!1]),"exponent"in e||(e.exponent=[!0,!1]),"eSigned"in e||(e.eSigned=[!0,!1]);const t=h(e),i=d(e.fractional,(t=>{let i="";return t&&0!==e.places&&(i="\\"+e.decimal,e.places===1/0?i="(?:"+i+"\\d+)?":i+="\\d{"+e.places+"}"),i}),!0);let n=t+i;return i&&(n="(?:(?:"+n+")|(?:"+i+"))"),n+d(e.exponent,(t=>t?"([eE]"+h({signed:e.eSigned})+")":""))}(i)+")"}))),!0);return{regexp:u.replace(/[\xa0 ]/g,"[\\s\\xa0]"),group:s,decimal:r,factor:a}}function u(e,t){const i=c(t),n=new RegExp("^"+i.regexp+"$").exec(e);if(!n)return NaN;let s=n[1];if(!n[1]){if(!n[2])return NaN;s=n[2],i.factor*=-1}return s=s.replace(new RegExp("["+i.group+"\\s\\xa0]","g"),"").replace(i.decimal,"."),Number(s)*i.factor}function h(e){return"signed"in(e=e||{})||(e.signed=[!0,!1]),"separator"in e?"groupSize"in e||(e.groupSize=3):e.separator="",d(e.signed,(e=>e?"[-+]":""),!0)+d(e.separator,(t=>{if(!t)return"(?:\\d+)";" "===(t=(0,n.Qs)(t))?t="\\s":" "===t&&(t="\\s\\xa0");const i=e.groupSize,s=e.groupSize2;if(s){const e="(?:0|[1-9]\\d{0,"+(s-1)+"}(?:["+t+"]\\d{"+s+"})*["+t+"]\\d{"+i+"})";return i-s>0?"(?:"+e+"|(?:0|[1-9]\\d{0,"+(i-1)+"}))":e}return"(?:0|[1-9]\\d{0,"+(i-1)+"}(?:["+t+"]\\d{"+i+"})*)"}),!0)}const d=(e,t,i)=>{if(!(e instanceof Array))return t(e);const n=[];for(let i=0;i<e.length;i++)n.push(t(e[i]));return f(n.join("|"),Boolean(i))},f=(e,t)=>"("+(t?"?:":"")+e+")"},17445:(e,t,i)=>{i.d(t,{N1:()=>d,YP:()=>l,Z_:()=>m,gx:()=>c,nn:()=>g,on:()=>h,tX:()=>_});var n=i(91460),s=i(50758),r=i(70586),o=i(95330),a=i(26258);function l(e,t,i={}){return u(e,t,i,f)}function c(e,t,i={}){return u(e,t,i,p)}function u(e,t,i={},n){let s=null;const o=i.once?(e,i)=>{n(e)&&((0,r.hw)(s),t(e,i))}:(e,i)=>{n(e)&&t(e,i)};if(s=(0,a.aQ)(e,o,i.sync,i.equals),i.initial){const t=e();o(t,t)}return s}function h(e,t,i,o={}){let a=null,c=null,u=null;function h(){a&&c&&(c.remove(),o.onListenerRemove?.(a),a=null,c=null)}function d(e){o.once&&o.once&&(0,r.hw)(u),i(e)}const f=l(e,((e,i)=>{h(),(0,n.vT)(e)&&(a=e,c=(0,n.on)(e,t,d),o.onListenerAdd?.(e))}),{sync:o.sync,initial:!0});return u=(0,s.kB)((()=>{f.remove(),h()})),u}function d(e,t){return function(e,t,i){if((0,o.Hc)(i))return Promise.reject((0,o.zE)());const n=e();if(t?.(n))return Promise.resolve(n);let a=null;function l(){a=(0,r.hw)(a)}return new Promise(((n,r)=>{a=(0,s.AL)([(0,o.fu)(i,(()=>{l(),r((0,o.zE)())})),u(e,(e=>{l(),n(e)}),{sync:!1,once:!0},t??f)])}))}(e,p,t)}function f(e){return!0}function p(e){return!!e}i(87538);const m={sync:!0},g={initial:!0},_={sync:!0,initial:!0}},16306:(e,t,i)=>{i.d(t,{aX:()=>I});var n=i(68773),s=i(20102),r=i(92604),o=i(70586),a=i(38913),l=i(58901),c=i(73913),u=i(8744),h=i(40488),d=(i(66577),i(3172)),f=i(33955),p=i(11282),m=i(17452);async function g(e,t,i){const n="string"==typeof e?(0,m.mN)(e):e,s=t[0].spatialReference,r=(0,f.Ji)(t[0]),o={...i,query:{...n.query,f:"json",sr:s.wkid?s.wkid:JSON.stringify(s),geometries:JSON.stringify((l=t,{geometryType:(0,f.Ji)(l[0]),geometries:l.map((e=>e.toJSON()))}))}},{data:a}=await(0,d.default)(n.path+"/simplify",o);var l;return function(e,t,i){const n=(0,f.q9)(t);return e.map((e=>{const t=n.fromJSON(e);return t.spatialReference=i,t}))}(a.geometries,r,s)}const _=r.Z.getLogger("esri.geometry.support.normalizeUtils");function y(e){return"polygon"===e[0].type}function x(e){return"polyline"===e[0].type}function b(e,t,i){if(t){const t=function(e,t){if(!(e instanceof l.Z||e instanceof a.Z)){const e="straightLineDensify: the input geometry is neither polyline nor polygon";throw _.error(e),new s.Z(e)}const i=(0,c.x3)(e),n=[];for(const e of i){const i=[];n.push(i),i.push([e[0][0],e[0][1]]);for(let n=0;n<e.length-1;n++){const s=e[n][0],r=e[n][1],o=e[n+1][0],a=e[n+1][1],l=Math.sqrt((o-s)*(o-s)+(a-r)*(a-r)),c=(a-r)/l,u=(o-s)/l,h=l/t;if(h>1){for(let e=1;e<=h-1;e++){const n=e*t,o=u*n+s,a=c*n+r;i.push([o,a])}const e=(l+Math.floor(h-1)*t)/2,n=u*e+s,o=c*e+r;i.push([n,o])}i.push([o,a])}}return function(e){return"polygon"===e.type}(e)?new a.Z({rings:n,spatialReference:e.spatialReference}):new l.Z({paths:n,spatialReference:e.spatialReference})}(e,1e6);e=(0,h.Sx)(t,!0)}return i&&(e=(0,c.Sy)(e,i)),e}function S(e,t,i){if(Array.isArray(e)){const n=e[0];if(n>t){const i=(0,c.XZ)(n,t);e[0]=n+i*(-2*t)}else if(n<i){const t=(0,c.XZ)(n,i);e[0]=n+t*(-2*i)}}else{const n=e.x;if(n>t){const i=(0,c.XZ)(n,t);e=e.clone().offset(i*(-2*t),0)}else if(n<i){const t=(0,c.XZ)(n,i);e=e.clone().offset(t*(-2*i),0)}}return e}function w(e,t){let i=-1;for(let n=0;n<t.cutIndexes.length;n++){const s=t.cutIndexes[n],r=t.geometries[n],o=(0,c.x3)(r);for(let e=0;e<o.length;e++){const t=o[e];t.some((i=>{if(i[0]<180)return!0;{let i=0;for(let e=0;e<t.length;e++){const n=t[e][0];i=n>i?n:i}i=Number(i.toFixed(9));const n=-360*(0,c.XZ)(i,180);for(let i=0;i<t.length;i++){const t=r.getPoint(e,i);r.setPoint(e,i,t.clone().offset(n,0))}return!0}}))}if(s===i){if(y(e))for(const t of(0,c.x3)(r))e[s]=e[s].addRing(t);else if(x(e))for(const t of(0,c.x3)(r))e[s]=e[s].addPath(t)}else i=s,e[s]=r}return e}async function I(e,t,i){if(!Array.isArray(e))return I([e],t);t&&"string"!=typeof t&&_.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const s="string"==typeof t?t:t?.url??n.Z.geometryServiceUrl;let r,m,y,x,v,F,B,M,E=0;const N=[],z=[];for(const t of e)if((0,o.Wi)(t))z.push(t);else if(r||(r=t.spatialReference,m=(0,u.C5)(r),y=r.isWebMercator,F=y?102100:4326,x=c.UZ[F].maxX,v=c.UZ[F].minX,B=c.UZ[F].plus180Line,M=c.UZ[F].minus180Line),m)if("mesh"===t.type)z.push(t);else if("point"===t.type)z.push(S(t.clone(),x,v));else if("multipoint"===t.type){const e=t.clone();e.points=e.points.map((e=>S(e,x,v))),z.push(e)}else if("extent"===t.type){const e=t.clone()._normalize(!1,!1,m);z.push(e.rings?new a.Z(e):e)}else if(t.extent){const e=t.extent,i=(0,c.XZ)(e.xmin,v)*(2*x);let n=0===i?t.clone():(0,c.Sy)(t.clone(),i);e.offset(i,0),e.intersects(B)&&e.xmax!==x?(E=e.xmax>E?e.xmax:E,n=b(n,y),N.push(n),z.push("cut")):e.intersects(M)&&e.xmin!==v?(E=e.xmax*(2*x)>E?e.xmax*(2*x):E,n=b(n,y,360),N.push(n),z.push("cut")):z.push(n)}else z.push(t.clone());else z.push(t);let C=(0,c.XZ)(E,x),T=-90;const Z=C,R=new l.Z;for(;C>0;){const e=360*C-180;R.addPath([[e,T],[e,-1*T]]),T*=-1,C--}if(N.length>0&&Z>0){const t=w(N,await async function(e,t,i,n){const s=(0,p.en)(e),r=t[0].spatialReference,o={...n,query:{...s.query,f:"json",sr:JSON.stringify(r),target:JSON.stringify({geometryType:(0,f.Ji)(t[0]),geometries:t}),cutter:JSON.stringify(i)}},a=await(0,d.default)(s.path+"/cut",o),{cutIndexes:l,geometries:c=[]}=a.data;return{cutIndexes:l,geometries:c.map((e=>{const t=(0,f.im)(e);return t.spatialReference=r,t}))}}(s,N,R,i)),n=[],r=[];for(let i=0;i<z.length;i++){const s=z[i];if("cut"!==s)r.push(s);else{const s=t.shift(),a=e[i];(0,o.pC)(a)&&"polygon"===a.type&&a.rings&&a.rings.length>1&&s.rings.length>=a.rings.length?(n.push(s),r.push("simplify")):r.push(y?(0,h.$)(s):s)}}if(!n.length)return r;const a=await g(s,n,i),l=[];for(let e=0;e<r.length;e++){const t=r[e];"simplify"!==t?l.push(t):l.push(y?(0,h.$)(a.shift()):a.shift())}return l}const X=[];for(let e=0;e<z.length;e++){const t=z[e];if("cut"!==t)X.push(t);else{const e=N.shift();X.push(!0===y?(0,h.$)(e):e)}}return X}},73913:(e,t,i)=>{i.d(t,{Sy:()=>l,UZ:()=>o,XZ:()=>a,x3:()=>c});var n=i(58901),s=i(82971),r=i(33955);const o={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new n.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator}),minus180Line:new n.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new n.Z({paths:[[[180,-180],[180,180]]],spatialReference:s.Z.WGS84}),minus180Line:new n.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:s.Z.WGS84})}};function a(e,t){return Math.ceil((e-t)/(2*t))}function l(e,t){const i=c(e);for(const e of i)for(const i of e)i[0]+=t;return e}function c(e){return(0,r.oU)(e)?e.rings:e.paths}},70171:(e,t,i)=>{let n;i.d(t,{Kd:()=>o,Ze:()=>u,qe:()=>l});const s=globalThis.esriConfig?.locale??globalThis.dojoConfig?.locale;function r(){return s??globalThis.navigator?.language??"en"}function o(){return void 0===n&&(n=r()),n}const a=[];function l(e){return a.push(e),{remove(){a.splice(a.indexOf(e),1)}}}const c=[];function u(e){return c.push(e),{remove(){a.splice(c.indexOf(e),1)}}}globalThis.addEventListener?.("languagechange",(function(){const e=r();n!==e&&(n=e,[...c].forEach((t=>{t.call(null,e)})),[...a].forEach((t=>{t.call(null,e)})))}))},37549:(e,t,i)=>{i.d(t,{H:()=>a});var n=i(80442),s=i(24133),r=i(24470);const o={minX:0,minY:0,maxX:0,maxY:0};class a{constructor(){this._indexInvalid=!1,this._boundsToLoad=[],this._boundsById=new Map,this._idByBounds=new Map,this._index=new s.Q(9,(0,n.Z)("esri-csp-restrictions")?e=>({minX:e[0],minY:e[1],maxX:e[2],maxY:e[3]}):["[0]","[1]","[2]","[3]"]),this._loadIndex=()=>{if(this._indexInvalid){const e=new Array(this._idByBounds.size);let t=0;this._idByBounds.forEach(((i,n)=>{e[t++]=n})),this._indexInvalid=!1,this._index.clear(),this._index.load(e)}else this._boundsToLoad.length&&(this._index.load(Array.from(new Set(this._boundsToLoad.filter((e=>this._idByBounds.has(e)))))),this._boundsToLoad.length=0)}}get fullBounds(){if(!this._boundsById.size)return null;const e=(0,r.cS)();for(const t of this._boundsById.values())t&&(e[0]=Math.min(t[0],e[0]),e[1]=Math.min(t[1],e[1]),e[2]=Math.max(t[2],e[2]),e[3]=Math.max(t[3],e[3]));return e}get valid(){return!this._indexInvalid}clear(){this._indexInvalid=!1,this._boundsToLoad.length=0,this._boundsById.clear(),this._idByBounds.clear(),this._index.clear()}delete(e){const t=this._boundsById.get(e);this._boundsById.delete(e),t&&(this._idByBounds.delete(t),this._indexInvalid||this._index.remove(t))}forEachInBounds(e,t){this._loadIndex(),function(e,t,i){(function(e){o.minX=e[0],o.minY=e[1],o.maxX=e[2],o.maxY=e[3]})(t),e.search(o,i)}(this._index,e,(e=>t(this._idByBounds.get(e))))}get(e){return this._boundsById.get(e)}has(e){return this._boundsById.has(e)}invalidateIndex(){this._indexInvalid||(this._indexInvalid=!0,this._boundsToLoad.length=0)}set(e,t){if(!this._indexInvalid){const t=this._boundsById.get(e);t&&(this._index.remove(t),this._idByBounds.delete(t))}this._boundsById.set(e,t),t&&(this._idByBounds.set(t,e),this._indexInvalid||(this._boundsToLoad.push(t),this._boundsToLoad.length>5e4&&this._loadIndex()))}}},57191:(e,t,i)=>{i.d(t,{Z:()=>_});var n=i(20102),s=i(32448),r=i(92604),o=i(70586),a=i(60437),l=i(24470),c=i(98732),u=i(37549),h=i(29730),d=i(70272),f=i(5428);const p={getObjectId:e=>e.objectId,getAttributes:e=>e.attributes,getAttribute:(e,t)=>e.attributes[t],cloneWithGeometry:(e,t)=>new d.u_(t,e.attributes,null,e.objectId),getGeometry:e=>e.geometry,getCentroid:(e,t)=>((0,o.Wi)(e.centroid)&&(e.centroid=(0,h.Y)(new f.Z,e.geometry,t.hasZ,t.hasM)),e.centroid)};var m=i(11490);const g=(0,a.Ue)();class _{constructor(e){this.geometryInfo=e,this._boundsStore=new u.H,this._featuresById=new Map,this._markedIds=new Set,this.events=new s.Z,this.featureAdapter=p}get geometryType(){return this.geometryInfo.geometryType}get hasM(){return this.geometryInfo.hasM}get hasZ(){return this.geometryInfo.hasZ}get numFeatures(){return this._featuresById.size}get fullBounds(){return this._boundsStore.fullBounds}get storeStatistics(){let e=0;return this._featuresById.forEach((t=>{(0,o.pC)(t.geometry)&&t.geometry.coords&&(e+=t.geometry.coords.length)})),{featureCount:this._featuresById.size,vertexCount:e/(this.hasZ?this.hasM?4:3:this.hasM?3:2)}}getFullExtent(e){if((0,o.Wi)(this.fullBounds))return null;const[t,i,n,s]=this.fullBounds;return{xmin:t,ymin:i,xmax:n,ymax:s,spatialReference:(0,m.S2)(e)}}add(e){this._add(e),this._emitChanged()}addMany(e){for(const t of e)this._add(t);this._emitChanged()}clear(){this._featuresById.clear(),this._boundsStore.clear(),this._emitChanged()}removeById(e){const t=this._featuresById.get(e);return t?(this._remove(t),this._emitChanged(),t):null}removeManyById(e){this._boundsStore.invalidateIndex();for(const t of e){const e=this._featuresById.get(t);e&&this._remove(e)}this._emitChanged()}forEachBounds(e,t){for(const i of e){const e=this._boundsStore.get(i.objectId);e&&t((0,a.JR)(g,e))}}getFeature(e){return this._featuresById.get(e)}has(e){return this._featuresById.has(e)}forEach(e){this._featuresById.forEach((t=>e(t)))}forEachInBounds(e,t){this._boundsStore.forEachInBounds(e,(e=>{t(this._featuresById.get(e))}))}startMarkingUsedFeatures(){this._boundsStore.invalidateIndex(),this._markedIds.clear()}sweep(){let e=!1;this._featuresById.forEach(((t,i)=>{this._markedIds.has(i)||(e=!0,this._remove(t))})),this._markedIds.clear(),e&&this._emitChanged()}_emitChanged(){this.events.emit("changed",void 0)}_add(e){if(!e)return;const t=e.objectId;if(null==t)return void r.Z.getLogger("esri.layers.graphics.data.FeatureStore").error(new n.Z("featurestore:invalid-feature","feature id is missing",{feature:e}));const i=this._featuresById.get(t);let s;if(this._markedIds.add(t),i?(e.displayId=i.displayId,s=this._boundsStore.get(t),this._boundsStore.delete(t)):(0,o.pC)(this.onFeatureAdd)&&this.onFeatureAdd(e),(0,o.Wi)(e.geometry)||!e.geometry.coords||!e.geometry.coords.length)return this._boundsStore.set(t,null),void this._featuresById.set(t,e);s=(0,c.$)((0,o.pC)(s)?s:(0,l.Ue)(),e.geometry,this.geometryInfo.hasZ,this.geometryInfo.hasM),(0,o.pC)(s)&&this._boundsStore.set(t,s),this._featuresById.set(t,e)}_remove(e){(0,o.pC)(this.onFeatureRemove)&&this.onFeatureRemove(e);const t=e.objectId;return this._markedIds.delete(t),this._boundsStore.delete(t),this._featuresById.delete(t),e}}},27793:(e,t,i)=>{i.r(t),i.d(t,{default:()=>J}),i(66577);var n=i(3172),s=i(66643),r=i(20102),o=i(92604),a=i(95330),l=i(17452),c=i(44547),u=i(37455),h=i(8744),d=i(40488),f=i(70272),p=i(5428),m=i(57191),g=i(37427),_=i(50245),y=i(14808),x=i(35671);const b=/^\s*"([\S\s]*)"\s*$/,S=/""/g,w=[","," ",";","|","\t"];function*I(e,t,i){let n=0;for(;n<=e.length;){const s=e.indexOf(t,n),r=e.substring(n,s>-1?s:void 0);n+=r.length+t.length,i&&!r.trim()||(yield r)}}function v(e){const t=e.includes("\r\n")?"\r\n":"\n";return I(e,t,!0)}function F(e,t){return I(e,t,!1)}function B(e,t,i){e=e.trim(),t=t?.trim();const n=[],s=Array.from(new Set([i?.delimiter,...w])).filter((e=>null!=e));for(const i of s){const s=N(e,i).length,r=N(t,i).length??s;s>1&&n.push({weight:Math.min(s,r),delimiter:i})}const r=n.sort((({weight:e},{weight:t})=>t-e)).map((({delimiter:e})=>e));for(const t of r){const n=C(E(e,t).names,i?.longitudeField,i?.latitudeField);if(n.longitudeFieldName&&n.latitudeFieldName)return{delimiter:t,locationInfo:n}}return{delimiter:r[0],locationInfo:null}}function*M(e,t,i,n=(()=>Object.create(null))){const s=v(e);s.next();let r="",o="",a=0,l=n(),c=0;e:for(const e of s){const s=F(e,i);for(const e of s)if(r+=o+e,o="",a+=z(e),a%2==0){if(a>0){const e=b.exec(r);if(!e){l=n(),c=0,r="",a=0;continue e}l[t[c]]=e[1].replace(S,'"'),c++}else l[t[c]]=r,c++;r="",a=0}else o=i;0===a?(yield l,l=n(),c=0):o="\n"}}function E(e,t){const i=N(e,t).filter((e=>null!=e)),n=i.map((e=>(0,x.q6)(e)));for(let e=n.length-1;e>=0;e--)n[e]||(n.splice(e,1),i.splice(e,1));return{names:n,aliases:i}}function N(e,t){if(!e?.length)return[];const i=[];let n="",s="",r=0;const o=F(e,t);for(const e of o)if(n+=s+e,s="",r+=z(e),r%2==0){if(r>0){const e=b.exec(n);e&&i.push(e[1].replace(S,'"'))}else i.push(n);n="",r=0}else s=t;return i}function z(e){let t=0,i=0;for(i=e.indexOf('"',i);i>=0;)t++,i=e.indexOf('"',i+1);return t}function C(e,t,i){t=(0,x.q6)(t)?.toLowerCase(),i=(0,x.q6)(i)?.toLowerCase();const n=e.map((e=>e.toLowerCase())),s=t?e[n.indexOf(t)]:null,r=i?e[n.indexOf(i)]:null;return{longitudeFieldName:s||e[n.indexOf(k.find((e=>n.includes(e))))],latitudeFieldName:r||e[n.indexOf(A.find((e=>n.includes(e))))]}}function T(e,t,i,n,s){const r=[],o=M(e,i,t),a=[];for(const e of o){if(10===a.length)break;a.push(e)}for(let e=0;e<i.length;e++){const t=i[e],o=n[e];if(t===s.longitudeFieldName||t===s.latitudeFieldName)r.push({name:t,type:"esriFieldTypeDouble",alias:o});else{let e,i;switch(Z(a.map((e=>e[t])))){case"integer":e="esriFieldTypeInteger";break;case"double":e="esriFieldTypeDouble";break;case"date":e="esriFieldTypeDate",i=36;break;default:e="esriFieldTypeString",i=255}r.push({name:t,type:e,alias:o,length:i})}}return r}function Z(e){if(!e.length)return"string";const t=/[^+-.,0-9]/;return e.map((e=>{let i=!1;if(""!==e){if(t.test(e))i=!0;else{let t=X(e);if(!isNaN(t))return/[.,]/.test(e)||!Number.isInteger(t)||t>214783647||t<-214783648?"double":"integer";if(e.includes("E")){if(t=Number(e),!isNaN(t))return"double";if(e.includes(",")){if(e=e.replace(",","."),t=Number(e),!isNaN(t))return"double";i=!0}else i=!0}else i=!0}return i?/^[-]?\d*[.,]?\d*$/.test(e)?"string":R(new Date(e),e)?"date":"string":"string"}})).reduce(((e,t)=>void 0===e?t:void 0===t?e:e===t?t:"string"===e||"string"===t?"string":"double"===e||"double"===t?"double":void 0))}function R(e,t){if(!e||"[object Date]"!==Object.prototype.toString.call(e)||isNaN(e.getTime()))return!1;let i=!0;if(!L&&/\d+\W*$/.test(t)){const e=t.match(/[a-zA-Z]{2,}/);if(e){let t=!1,n=0;for(;!t&&n<=e.length;)t=!O.test(e[n]),n++;i=!t}}return i}const X=function(){const e=(0,y.lt)(),t=new RegExp("^"+e.regexp+"$"),i=new RegExp("["+e.group+"\\s\\xa0]","g"),n=e.factor;return s=>{const r=t.exec(s);if(e.factor=n,!r)return NaN;let o=r[1];if(!r[1]){if(!r[2])return NaN;o=r[2],e.factor*=-1}return o=o.replace(i,"").replace(e.decimal,"."),+o*e.factor}}(),O=/^((jan(uary)?)|(feb(ruary)?)|(mar(ch)?)|(apr(il)?)|(may)|(jun(e)?)|(jul(y)?)|(aug(ust)?)|(sep(tember)?)|(oct(ober)?)|(nov(ember)?)|(dec(ember)?)|(am)|(pm)|(gmt)|(utc))$/i,L=Number.isNaN(new Date("technology 10").getTime()),A=["lat","latitude","latitude83","latdecdeg","lat_dd","y","ycenter","point_y"],k=["lon","lng","long","longitude","longitude83","longdecdeg","long_dd","x","xcenter","point_x"];var q=i(25278),Y=i(99514),j=i(82971);const D=(0,q.bU)("esriGeometryPoint"),P=["csv"],U=[0,0];class W{constructor(e,t){this.x=e,this.y=t}}class J{constructor(){this._queryEngine=null,this._snapshotFeatures=async e=>{const t=await this._fetch(e);return this._createFeatures(t)}}destroy(){this._queryEngine?.destroy(),this._queryEngine=null}async load(e,t={}){this._loadOptions=e;const[i]=await Promise.all([this._fetch(t.signal),this._checkProjection(e?.parsingOptions?.spatialReference)]),n=function(e,t){const i=t.parsingOptions||{},n={delimiter:i.delimiter,layerDefinition:null,locationInfo:{latitudeFieldName:i.latitudeField,longitudeFieldName:i.longitudeField}},s=n.layerDefinition={name:(0,l.vt)(t.url,P)||"csv",drawingInfo:D,geometryType:"esriGeometryPoint",objectIdField:null,fields:[],timeInfo:i.timeInfo,extent:{xmin:Number.POSITIVE_INFINITY,ymin:Number.POSITIVE_INFINITY,xmax:Number.NEGATIVE_INFINITY,ymax:Number.NEGATIVE_INFINITY,spatialReference:i.spatialReference||{wkid:4326}}},o=v(e),a=o.next().value?.trim(),c=o.next().value?.trim();if(!a)throw new r.Z("csv-layer:empty-csv","CSV is empty",{csv:e});const{delimiter:u,locationInfo:h}=B(a,c,i);if(!u)throw new r.Z("csv-layer:invalid-delimiter","Unable to detect the delimiter from CSV",{firstLine:a,secondLine:c,parsingOptions:i});if(!h)throw new r.Z("csv-layer:location-fields-not-found","Unable to identify latitude and longitude fields from the CSV file",{firstLine:a,secondLine:c,parsingOptions:i});n.locationInfo=h,n.delimiter=u;const{names:d,aliases:f}=E(a,u),p=T(e,n.delimiter,d,f,n.locationInfo);if(i.fields?.length){const e=new Y.Z(i.fields);for(const t of p){const i=e.get(t.name);i&&Object.assign(t,i)}}if(!p.some((e=>"esriFieldTypeOID"===e.type&&(s.objectIdField=e.name,!0)))){const e={name:"__OBJECTID",alias:"__OBJECTID",type:"esriFieldTypeOID",editable:!1,nullable:!1};s.objectIdField=e.name,p.unshift(e)}s.fields=p;const m=new Y.Z(s.fields);if(n.locationInfo&&(n.locationInfo.latitudeFieldName=m.get(n.locationInfo.latitudeFieldName).name,n.locationInfo.longitudeFieldName=m.get(n.locationInfo.longitudeFieldName).name),s.timeInfo){const e=s.timeInfo;if(e.startTimeField){const t=m.get(e.startTimeField);t?(e.startTimeField=t.name,t.type="esriFieldTypeDate"):e.startTimeField=null}if(e.endTimeField){const t=m.get(e.endTimeField);t?(e.endTimeField=t.name,t.type="esriFieldTypeDate"):e.endTimeField=null}if(e.trackIdField){const t=m.get(e.trackIdField);e.trackIdField=t?t.name:null}e.startTimeField||e.endTimeField||(s.timeInfo=null)}return n}(i,e);this._locationInfo=n.locationInfo,this._delimiter=n.delimiter,this._queryEngine=this._createQueryEngine(n);const s=await this._createFeatures(i);this._queryEngine.featureStore.addMany(s);const{fullExtent:o,timeExtent:a}=await this._queryEngine.fetchRecomputedExtents();if(n.layerDefinition.extent=o,a){const{start:e,end:t}=a;n.layerDefinition.timeInfo.timeExtent=[e,t]}return n}async applyEdits(){throw new r.Z("csv-layer:editing-not-supported","applyEdits() is not supported on CSVLayer")}async queryFeatures(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQuery(e,t.signal)}async queryFeatureCount(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForCount(e,t.signal)}async queryObjectIds(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForIds(e,t.signal)}async queryExtent(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForExtent(e,t.signal)}async querySnapping(e,t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForSnapping(e,t.signal)}async refresh(e){this._loadOptions.customParameters=e,this._snapshotTask?.abort(),this._snapshotTask=(0,s.vr)(this._snapshotFeatures),this._snapshotTask.promise.then((e=>{this._queryEngine.featureStore.clear(),e&&this._queryEngine.featureStore.addMany(e)}),(e=>{this._queryEngine.featureStore.clear(),(0,a.D_)(e)||o.Z.getLogger("esri.layers.CSVLayer").error(new r.Z("csv-layer:refresh","An error occurred during refresh",{error:e}))})),await this._waitSnapshotComplete();const{fullExtent:t,timeExtent:i}=await this._queryEngine.fetchRecomputedExtents();return{extent:t,timeExtent:i}}async _waitSnapshotComplete(){if(this._snapshotTask&&!this._snapshotTask.finished){try{await this._snapshotTask.promise}catch{}return this._waitSnapshotComplete()}}async _fetch(e){const{url:t,customParameters:i}=this._loadOptions;if(!t)throw new r.Z("csv-layer:invalid-source","url not defined");const s=(0,l.mN)(t);return(await(0,n.default)(s.path,{query:{...s.query,...i},responseType:"text",signal:e})).data}_createQueryEngine(e){const{objectIdField:t,fields:i,extent:n,timeInfo:s}=e.layerDefinition,r=new m.Z({geometryType:"esriGeometryPoint",hasM:!1,hasZ:!1});return new _.q({fields:i,geometryType:"esriGeometryPoint",hasM:!1,hasZ:!1,timeInfo:s,objectIdField:t,spatialReference:n.spatialReference||{wkid:4326},cacheSpatialQueries:!0,featureStore:r})}async _createFeatures(e){const{latitudeFieldName:t,longitudeFieldName:i}=this._locationInfo,{objectIdField:n,fieldsIndex:s,spatialReference:r}=this._queryEngine;let o=[];const a=[],l=s.fields.filter((e=>e.name!==n)).map((e=>e.name));let m=0;const g={};for(const e of s.fields)if("esriFieldTypeOID"!==e.type&&"esriFieldTypeGlobalID"!==e.type){const t=(0,x.os)(e);void 0!==t&&(g[e.name]=t)}const _=M(e,l,this._delimiter,(0,q.Dm)(g,n));for(const e of _){const r=this._parseCoordinateValue(e[t]),l=this._parseCoordinateValue(e[i]);if(null!=l&&null!=r&&!isNaN(r)&&!isNaN(l)){e[t]=r,e[i]=l;for(const n in e)if(n!==t&&n!==i)if(s.isDateField(n)){const t=new Date(e[n]);e[n]=R(t,e[n])?t.getTime():null}else if(s.isNumericField(n)){const t=X(e[n]);isNaN(t)?e[n]=null:e[n]=t}e[n]=m,m++,o.push(new W(l,r)),a.push(e)}}if(!(0,h.fS)({wkid:4326},r))if((0,h.sS)(r))for(const e of o)[e.x,e.y]=(0,d.hG)(e.x,e.y,U);else o=(0,c.oj)(u.N,o,j.Z.WGS84,r,null,null);const y=[];for(let e=0;e<o.length;e++){const{x:t,y:i}=o[e],s=a[e];s[n]=e+1,y.push(new f.u_(new p.Z([],[t,i]),s,null,s[n]))}return y}_parseCoordinateValue(e){if(null==e||""===e)return null;let t=X(e);return(isNaN(t)||Math.abs(t)>181)&&(t=parseFloat(e)),t}async _checkProjection(e){try{await(0,g._W)(h.Zn,e)}catch{throw new r.Z("csv-layer:projection-not-supported","Projection not supported")}}}},25278:(e,t,i)=>{i.d(t,{Dm:()=>u,Hq:()=>h,MS:()=>d,bU:()=>a});var n=i(80442),s=i(22974),r=i(61159),o=i(58333);function a(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?o.I4:"esriGeometryPolyline"===e?o.ET:o.lF}}}const l=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let c=1;function u(e,t){if((0,n.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let i=`this.${t} = null;`;for(const t in e)i+=`this${l.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const n=new Function(`\n      return class AttributesClass$${c++} {\n        constructor() {\n          ${i};\n        }\n      }\n    `)();return()=>new n}catch(i){return()=>({[t]:null,...e})}}function h(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,s.d9)(e)}}]}function d(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:r.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},11282:(e,t,i)=>{i.d(t,{cv:()=>a,en:()=>o,lA:()=>r}),i(68773),i(40330);var n=i(22974),s=i(17452);function r(e,t){return t?{...t,query:{...e??{},...t.query}}:{query:e}}function o(e){return"string"==typeof e?(0,s.mN)(e):(0,n.d9)(e)}function a(e,t,i){const n={};for(const s in e){if("declaredClass"===s)continue;const r=e[s];if(null!=r&&"function"!=typeof r)if(Array.isArray(r)){n[s]=[];for(let e=0;e<r.length;e++)n[s][e]=a(r[e])}else if("object"==typeof r)if(r.toJSON){const e=r.toJSON(i&&i[s]);n[s]=t?e:JSON.stringify(e)}else n[s]=t?r:JSON.stringify(r);else n[s]=r}return n}i(71058)},58333:(e,t,i)=>{i.d(t,{ET:()=>r,I4:()=>s,eG:()=>l,lF:()=>o,lj:()=>u,qP:()=>a,wW:()=>c});const n=[252,146,31,255],s={type:"esriSMS",style:"esriSMSCircle",size:6,color:n,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},r={type:"esriSLS",style:"esriSLSSolid",width:.75,color:n},o={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},c={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},u={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);