"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9327],{92835:(e,t,i)=>{i.d(t,{Z:()=>g});var r,s=i(43697),n=i(96674),o=i(70586),l=i(35463),a=i(5600),u=(i(75215),i(67676),i(71715)),c=i(52011),h=i(30556);let p=r=class extends n.wq{static get allTime(){return d}static get empty(){return y}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(r.allTime)}get isEmpty(){return this.equals(r.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new r({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,o.pC)(this.start)?this.start.getTime():this.start,i=(0,o.pC)(this.end)?this.end.getTime():this.end,r=(0,o.pC)(e.start)?e.start.getTime():e.start,s=(0,o.pC)(e.end)?e.end.getTime():e.end;return t===r&&i===s}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,o.yw)(this.start,(t=>(0,l.JE)(t,e))),i=(0,o.yw)(this.end,(t=>{const i=(0,l.JE)(t,e);return t.getTime()===i.getTime()?i:(0,l.Nm)(i,1,e)}));return new r({start:t,end:i})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return r.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,o.R2)(this.start,-1/0,(e=>e.getTime())),i=(0,o.R2)(this.end,1/0,(e=>e.getTime())),s=(0,o.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,o.R2)(e.end,1/0,(e=>e.getTime()));let l,a;if(s>=t&&s<=i?l=s:t>=s&&t<=n&&(l=t),i>=s&&i<=n?a=i:n>=t&&n<=i&&(a=n),null!=l&&null!=a&&!isNaN(l)&&!isNaN(a)){const e=new r;return e.start=l===-1/0?null:new Date(l),e.end=a===1/0?null:new Date(a),e}return r.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const i=new r,{start:s,end:n}=this;return(0,o.pC)(s)&&(i.start=(0,l.Nm)(s,e,t)),(0,o.pC)(n)&&(i.end=(0,l.Nm)(n,e,t)),i}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return d.clone();const t=(0,o.pC)(this.start)&&(0,o.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,i=(0,o.pC)(this.end)&&(0,o.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new r({start:t,end:i})}};(0,s._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"end",void 0),(0,s._)([(0,u.r)("end")],p.prototype,"readEnd",null),(0,s._)([(0,h.c)("end")],p.prototype,"writeEnd",null),(0,s._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isAllTime",null),(0,s._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isEmpty",null),(0,s._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"start",void 0),(0,s._)([(0,u.r)("start")],p.prototype,"readStart",null),(0,s._)([(0,h.c)("start")],p.prototype,"writeStart",null),p=r=(0,s._)([(0,c.j)("esri.TimeExtent")],p);const d=new p,y=new p({start:void 0,end:void 0}),g=p},46521:(e,t,i)=>{function r(){return[1,0,0,0,1,0,0,0,1]}function s(e,t,i,r,s,n,o,l,a){return[e,t,i,r,s,n,o,l,a]}function n(e,t){return new Float64Array(e,t,9)}i.d(t,{a:()=>n,c:()=>r,f:()=>s}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(e){return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8]]},create:r,createView:n,fromValues:s},Symbol.toStringTag,{value:"Module"}))},51305:(e,t,i)=>{i.d(t,{c:()=>d,g:()=>c,j:()=>I,k:()=>g,m:()=>h,s:()=>u});var r=i(46521),s=i(94961),n=i(65617),o=i(46851),l=i(17896),a=i(98766);function u(e,t,i){i*=.5;const r=Math.sin(i);return e[0]=r*t[0],e[1]=r*t[1],e[2]=r*t[2],e[3]=Math.cos(i),e}function c(e,t){const i=2*Math.acos(t[3]),r=Math.sin(i/2);return r>(0,o.g)()?(e[0]=t[0]/r,e[1]=t[1]/r,e[2]=t[2]/r):(e[0]=1,e[1]=0,e[2]=0),i}function h(e,t,i){const r=t[0],s=t[1],n=t[2],o=t[3],l=i[0],a=i[1],u=i[2],c=i[3];return e[0]=r*c+o*l+s*u-n*a,e[1]=s*c+o*a+n*l-r*u,e[2]=n*c+o*u+r*a-s*l,e[3]=o*c-r*l-s*a-n*u,e}function p(e,t,i,r){const s=t[0],n=t[1],l=t[2],a=t[3];let u,c,h,p,d,y=i[0],g=i[1],m=i[2],f=i[3];return c=s*y+n*g+l*m+a*f,c<0&&(c=-c,y=-y,g=-g,m=-m,f=-f),1-c>(0,o.g)()?(u=Math.acos(c),h=Math.sin(u),p=Math.sin((1-r)*u)/h,d=Math.sin(r*u)/h):(p=1-r,d=r),e[0]=p*s+d*y,e[1]=p*n+d*g,e[2]=p*l+d*m,e[3]=p*a+d*f,e}function d(e,t){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=t[3],e}function y(e,t){const i=t[0]+t[4]+t[8];let r;if(i>0)r=Math.sqrt(i+1),e[3]=.5*r,r=.5/r,e[0]=(t[5]-t[7])*r,e[1]=(t[6]-t[2])*r,e[2]=(t[1]-t[3])*r;else{let i=0;t[4]>t[0]&&(i=1),t[8]>t[3*i+i]&&(i=2);const s=(i+1)%3,n=(i+2)%3;r=Math.sqrt(t[3*i+i]-t[3*s+s]-t[3*n+n]+1),e[i]=.5*r,r=.5/r,e[3]=(t[3*s+n]-t[3*n+s])*r,e[s]=(t[3*s+i]+t[3*i+s])*r,e[n]=(t[3*n+i]+t[3*i+n])*r}return e}function g(e,t,i,r){const s=.5*Math.PI/180;t*=s,i*=s,r*=s;const n=Math.sin(t),o=Math.cos(t),l=Math.sin(i),a=Math.cos(i),u=Math.sin(r),c=Math.cos(r);return e[0]=n*a*c-o*l*u,e[1]=o*l*c+n*a*u,e[2]=o*a*u-n*l*c,e[3]=o*a*c+n*l*u,e}const m=a.c,f=a.s,b=a.a,v=h,_=a.b,w=a.d,S=a.l,C=a.e,x=C,V=a.f,j=V,M=a.n,I=a.g,R=a.h,z=(0,n.c)(),D=(0,n.f)(1,0,0),q=(0,n.f)(0,1,0),A=(0,s.a)(),N=(0,s.a)(),T=(0,r.c)();Object.freeze(Object.defineProperty({__proto__:null,add:b,calculateW:function(e,t){const i=t[0],r=t[1],s=t[2];return e[0]=i,e[1]=r,e[2]=s,e[3]=Math.sqrt(Math.abs(1-i*i-r*r-s*s)),e},conjugate:d,copy:m,dot:w,equals:R,exactEquals:I,fromEuler:g,fromMat3:y,getAxisAngle:c,identity:function(e){return e[0]=0,e[1]=0,e[2]=0,e[3]=1,e},invert:function(e,t){const i=t[0],r=t[1],s=t[2],n=t[3],o=i*i+r*r+s*s+n*n,l=o?1/o:0;return e[0]=-i*l,e[1]=-r*l,e[2]=-s*l,e[3]=n*l,e},len:x,length:C,lerp:S,mul:v,multiply:h,normalize:M,random:function(e){const t=o.R,i=t(),r=t(),s=t(),n=Math.sqrt(1-i),l=Math.sqrt(i);return e[0]=n*Math.sin(2*Math.PI*r),e[1]=n*Math.cos(2*Math.PI*r),e[2]=l*Math.sin(2*Math.PI*s),e[3]=l*Math.cos(2*Math.PI*s),e},rotateX:function(e,t,i){i*=.5;const r=t[0],s=t[1],n=t[2],o=t[3],l=Math.sin(i),a=Math.cos(i);return e[0]=r*a+o*l,e[1]=s*a+n*l,e[2]=n*a-s*l,e[3]=o*a-r*l,e},rotateY:function(e,t,i){i*=.5;const r=t[0],s=t[1],n=t[2],o=t[3],l=Math.sin(i),a=Math.cos(i);return e[0]=r*a-n*l,e[1]=s*a+o*l,e[2]=n*a+r*l,e[3]=o*a-s*l,e},rotateZ:function(e,t,i){i*=.5;const r=t[0],s=t[1],n=t[2],o=t[3],l=Math.sin(i),a=Math.cos(i);return e[0]=r*a+s*l,e[1]=s*a-r*l,e[2]=n*a+o*l,e[3]=o*a-n*l,e},rotationTo:function(e,t,i){const r=(0,l.e)(t,i);return r<-.999999?((0,l.f)(z,D,t),(0,l.u)(z)<1e-6&&(0,l.f)(z,q,t),(0,l.n)(z,z),u(e,z,Math.PI),e):r>.999999?(e[0]=0,e[1]=0,e[2]=0,e[3]=1,e):((0,l.f)(z,t,i),e[0]=z[0],e[1]=z[1],e[2]=z[2],e[3]=1+r,M(e,e))},scale:_,set:f,setAxes:function(e,t,i,r){const s=T;return s[0]=i[0],s[3]=i[1],s[6]=i[2],s[1]=r[0],s[4]=r[1],s[7]=r[2],s[2]=-t[0],s[5]=-t[1],s[8]=-t[2],M(e,y(e,s))},setAxisAngle:u,slerp:p,sqlerp:function(e,t,i,r,s,n){return p(A,t,s,n),p(N,i,r,n),p(e,A,N,2*n*(1-n)),e},sqrLen:j,squaredLength:V,str:function(e){return"quat("+e[0]+", "+e[1]+", "+e[2]+", "+e[3]+")"}},Symbol.toStringTag,{value:"Module"}))},94961:(e,t,i)=>{function r(){return[0,0,0,1]}function s(e){return[e[0],e[1],e[2],e[3]]}function n(e,t){return new Float64Array(e,t,4)}i.d(t,{I:()=>o,a:()=>r,b:()=>s,c:()=>n});const o=[0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:o,clone:s,create:r,createView:n,fromValues:function(e,t,i,r){return[e,t,i,r]}},Symbol.toStringTag,{value:"Module"}))},35270:(e,t,i)=>{i.d(t,{B7:()=>a,St:()=>s,VL:()=>o,h$:()=>n,rW:()=>u});const r={transparent:[0,0,0,0],black:[0,0,0,1],silver:[192,192,192,1],gray:[128,128,128,1],white:[255,255,255,1],maroon:[128,0,0,1],red:[255,0,0,1],purple:[128,0,128,1],fuchsia:[255,0,255,1],green:[0,128,0,1],lime:[0,255,0,1],olive:[128,128,0,1],yellow:[255,255,0,1],navy:[0,0,128,1],blue:[0,0,255,1],teal:[0,128,128,1],aqua:[0,255,255,1],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],blanchedalmond:[255,235,205,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],oldlace:[253,245,230,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],rebeccapurple:[102,51,153,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],whitesmoke:[245,245,245,1],yellowgreen:[154,205,50,1]};function s(e){return r[e]||r[e.toLowerCase()]}function n(e){return r[e]??r[e.toLowerCase()]}function o(e){return[...n(e)]}function l(e,t,i){i<0&&++i,i>1&&--i;const r=6*i;return r<1?e+(t-e)*r:2*i<1?t:3*i<2?e+(t-e)*(2/3-i)*6:e}function a(e,t,i,r=1){const s=(e%360+360)%360/360,n=i<=.5?i*(t+1):i+t-i*t,o=2*i-n;return[Math.round(255*l(o,n,s+1/3)),Math.round(255*l(o,n,s)),Math.round(255*l(o,n,s-1/3)),r]}function u(e){const t=e.length>5,i=t?8:4,r=(1<<i)-1,s=t?1:17,n=t?9===e.length:5===e.length;let o=Number("0x"+e.substr(1));if(isNaN(o))return null;const l=[0,0,0,1];let a;return n&&(a=o&r,o>>=i,l[3]=s*a/255),a=o&r,o>>=i,l[2]=s*a,a=o&r,o>>=i,l[1]=s*a,a=o&r,o>>=i,l[0]=s*a,l}},46791:(e,t,i)=>{i.d(t,{Z:()=>z});var r,s=i(43697),n=i(3894),o=i(32448),l=i(22974),a=i(70586),u=i(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(r||(r={}));var c,h=i(1654),p=i(5600),d=i(75215),y=i(52421),g=i(52011),m=i(58971),f=i(10661);const b=new u.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),v=()=>{};function _(e){return e?e instanceof R?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function w(e){if(e&&e.length)return e[0]}function S(e,t,i,r){const s=Math.min(e.length-i,t.length-r);let n=0;for(;n<s&&e[i+n]===t[r+n];)n++;return n}function C(e,t,i,r){t&&t.forEach(((t,s,n)=>{e.push(t),C(e,i.call(r,t,s,n),i,r)}))}const x=new Set,V=new Set,j=new Set,M=new Map;let I=0,R=c=class extends o.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof c}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new f.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:I++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof c?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,m.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(r.ADD)||(this._splice(0,this.length,_(e)),this._emitAfterChanges(r.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,i={removed:!1,callback:t};return e.push(i),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=v,i.removed=!0,e.splice(e.indexOf(i),1)}}}return this._emitter.on(e,t)}once(e,t){const i=this.on(e,t);return{remove(){i.remove()}}}add(e,t){if((0,m.it)(this._observable),this._emitBeforeChanges(r.ADD))return this;const i=this.getNextIndex(t??null);return this._splice(i,0,[e]),this._emitAfterChanges(r.ADD),this}addMany(e,t=this._items.length){if((0,m.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(r.ADD))return this;const i=this.getNextIndex(t);return this._splice(i,0,_(e)),this._emitAfterChanges(r.ADD),this}at(e){if((0,m.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(r.REMOVE),e}clone(){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.map(l.d9)})}concat(...e){(0,m.it)(this._observable);const t=e.map(_);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return;const i=(0,a.j0)(this._splice(0,this.length)),s=i.length;for(let r=0;r<s;r++)e.call(t,i[r],r,i);this._emitAfterChanges(r.REMOVE)}every(e,t){return(0,m.it)(this._observable),this._items.every(e,t)}filter(e,t){let i;return(0,m.it)(this._observable),i=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:i})}find(e,t){return(0,m.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,m.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,m.it)(this._observable);const i=[];return C(i,this,e,t),new c(i)}forEach(e,t){return(0,m.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,m.it)(this._observable),this._items[e]}getNextIndex(e){(0,m.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,m.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,m.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,m.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,m.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,m.it)(this._observable);const i=this._items.map(e,t);return new c({items:i})}reorder(e,t=this.length-1){(0,m.it)(this._observable);const i=this.indexOf(e);if(-1!==i){if(t<0?t=0:t>=this.length&&(t=this.length-1),i!==t){if(this._emitBeforeChanges(r.MOVE))return e;this._splice(i,1),this._splice(t,0,[e]),this._emitAfterChanges(r.MOVE)}return e}}pop(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return;const e=w(this._splice(this.length-1,1));return this._emitAfterChanges(r.REMOVE),e}push(...e){return(0,m.it)(this._observable),this._emitBeforeChanges(r.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(r.ADD)),this.length}reduce(e,t){(0,m.it)(this._observable);const i=this._items;return 2===arguments.length?i.reduce(e,t):i.reduce(e)}reduceRight(e,t){(0,m.it)(this._observable);const i=this._items;return 2===arguments.length?i.reduceRight(e,t):i.reduceRight(e)}remove(e){return(0,m.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,m.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(r.REMOVE))return;const t=w(this._splice(e,1));return this._emitAfterChanges(r.REMOVE),t}removeMany(e){if((0,m.it)(this._observable),!e||!e.length||this._emitBeforeChanges(r.REMOVE))return[];const t=e instanceof c?e.toArray():e,i=this._items,s=[],n=t.length;for(let e=0;e<n;e++){const r=t[e],n=i.indexOf(r);if(n>-1){const r=1+S(t,i,e+1,n+1),o=this._splice(n,r);o&&o.length>0&&s.push.apply(s,o),e+=r-1}}return this._emitAfterChanges(r.REMOVE),s}reverse(){if((0,m.it)(this._observable),this._emitBeforeChanges(r.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(r.MOVE),this}shift(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return;const e=w(this._splice(0,1));return this._emitAfterChanges(r.REMOVE),e}slice(e=0,t=this.length){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,m.it)(this._observable),this._items.some(e,t)}sort(e){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.MOVE))return this;const t=(0,a.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(r.MOVE),this}splice(e,t,...i){(0,m.it)(this._observable);const s=(t?r.REMOVE:0)|(i.length?r.ADD:0);if(this._emitBeforeChanges(s))return[];const n=this._splice(e,t,i)||[];return this._emitAfterChanges(s),n}toArray(){return(0,m.it)(this._observable),this._items.slice()}toJSON(){return(0,m.it)(this._observable),this.toArray()}toLocaleString(){return(0,m.it)(this._observable),this._items.toLocaleString()}toString(){return(0,m.it)(this._observable),this._items.toString()}unshift(...e){return(0,m.it)(this._observable),!e.length||this._emitBeforeChanges(r.ADD)||(this._splice(0,0,e),this._emitAfterChanges(r.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,i){const r=this._items,s=this.itemType;let n,o;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,h.Os)((()=>this._dispatchChange()))),t){if(o=r.splice(e,t),this.hasEventListener("before-remove")){const t=b.acquire();t.target=this,t.cancellable=!0;for(let i=0,s=o.length;i<s;i++)n=o[i],t.reset(n),this.emit("before-remove",t),t.defaultPrevented&&(o.splice(i,1),r.splice(e,0,n),e+=1,i-=1,s-=1);b.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=b.acquire();e.target=this,e.cancellable=!1;const t=o.length;for(let i=0;i<t;i++)e.reset(o[i]),this.emit("after-remove",e);b.release(e)}}if(i&&i.length){if(s){const e=[];for(const t of i){const i=s.ensureType(t);null==i&&null!=t||e.push(i)}i=e}const t=this.hasEventListener("before-add"),n=this.hasEventListener("after-add"),o=e===this.length;if(t||n){const s=b.acquire();s.target=this,s.cancellable=!0;const l=b.acquire();l.target=this,l.cancellable=!1;for(const a of i)t?(s.reset(a),this.emit("before-add",s),s.defaultPrevented||(o?r.push(a):r.splice(e++,0,a),this._set("length",r.length),n&&(l.reset(a),this.emit("after-add",l)))):(o?r.push(a):r.splice(e++,0,a),this._set("length",r.length),l.reset(a),this.emit("after-add",l));b.release(l),b.release(s)}else{if(o)for(const e of i)r.push(e);else r.splice(e,0,...i);this._set("length",r.length)}}return(i&&i.length||o&&o.length)&&this._notifyChangeEvent(i,o),o}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const i=b.acquire();i.target=this,i.cancellable=!0,i.type=e,this.emit("before-changes",i),t=i.defaultPrevented,b.release(i)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=b.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),b.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;x.clear(),V.clear(),j.clear();for(const{added:t,removed:i}of e){if(t)if(0===j.size&&0===V.size)for(const e of t)x.add(e);else for(const e of t)V.has(e)?(j.add(e),V.delete(e)):j.has(e)||x.add(e);if(i)if(0===j.size&&0===x.size)for(const e of i)V.add(e);else for(const e of i)x.has(e)?x.delete(e):(j.delete(e),V.add(e))}const i=n.Z.acquire();x.forEach((e=>{i.push(e)}));const r=n.Z.acquire();V.forEach((e=>{r.push(e)}));const s=this._items,o=t.items,l=n.Z.acquire();if(j.forEach((e=>{o.indexOf(e)!==s.indexOf(e)&&l.push(e)})),t.listeners&&(i.length||r.length||l.length)){const e={target:this,added:i,removed:r,moved:l},s=t.listeners.length;for(let i=0;i<s;i++){const r=t.listeners[i];r.removed||r.callback.call(this,e)}}n.Z.release(i),n.Z.release(r),n.Z.release(l)}x.clear(),V.clear(),j.clear()}};R.ofType=e=>{if(!e)return c;if(M.has(e))return M.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const i in e.typeMap){const r=e.typeMap[i].prototype.declaredClass;t?t+=` | ${r}`:t=r}let i=class extends c{};return(0,s._)([(0,y.c)({Type:e,ensureType:"function"==typeof e?(0,d.se)(e):(0,d.N7)(e)})],i.prototype,"itemType",void 0),i=(0,s._)([(0,g.j)(`esri.core.Collection<${t}>`)],i),M.set(e,i),i},(0,s._)([(0,p.Cb)()],R.prototype,"length",void 0),(0,s._)([(0,p.Cb)()],R.prototype,"items",null),R=c=(0,s._)([(0,g.j)("esri.core.Collection")],R);const z=R},68251:(e,t,i)=>{i.d(t,{BV:()=>o});var r=i(22021);class s{constructor(e,t){this.min=e,this.max=t,this.range=t-e}ndiff(e,t=0){return Math.ceil((e-t)/this.range)*this.range+t}_normalize(e,t,i,r=0,s=!1){return(i-=r)<e?i+=this.ndiff(e-i):i>t&&(i-=this.ndiff(i-t)),s&&i===t&&(i=e),i+r}normalize(e,t=0,i=!1){return this._normalize(this.min,this.max,e,t,i)}clamp(e,t=0){return(0,r.uZ)(e-t,this.min,this.max)+t}monotonic(e,t,i){return e<t?t:t+this.ndiff(e-t,i)}minimalMonotonic(e,t,i){return this._normalize(e,e+this.range,t,i)}center(e,t,i){return t=this.monotonic(e,t,i),this.normalize((e+t)/2,i)}diff(e,t,i){return this.monotonic(e,t,i)-e}shortestSignedDiff(e,t){e=this.normalize(e);const i=(t=this.normalize(t))-e,r=t<e?this.minimalMonotonic(e,t)-e:t-this.minimalMonotonic(t,e);return Math.abs(i)<Math.abs(r)?i:r}contains(e,t,i){return t=this.minimalMonotonic(e,t),(i=this.minimalMonotonic(e,i))>e&&i<t}}function n(e){for(const t in e){const i=e[t];i instanceof Function&&(e[t]=i.bind(e))}return e}n(new s(0,2*Math.PI)),n(new s(-Math.PI,Math.PI));const o=n(new s(0,360))},28576:(e,t,i)=>{i.d(t,{B:()=>h});var r=i(81153),s=i(17452),n=i(41123),o=i(7628),l=i(31263),a=i(5600),u=i(66094),c=i(25929);function h(e){const t=e?.origins??[void 0];return(i,n)=>{const h=function(e,t,i){if("resource"===e?.type)return function(e,t,i){const n=(0,o.Oe)(t,i);return{type:String,read:(e,t,i)=>{const r=(0,c.r)(e,t,i);return n.type===String?r:"function"==typeof n.type?new n.type({url:r}):void 0},write:{writer(t,o,a,h){if(!h||!h.resources)return"string"==typeof t?void(o[a]=(0,c.t)(t,h)):void(o[a]=t.write({},h));const g=function(e){return null==e?null:"string"==typeof e?e:e.url}(t),m=(0,c.t)(g,{...h,verifyItemRelativeUrls:h&&h.verifyItemRelativeUrls?{writtenUrls:h.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},c.M.NO),f=n.type!==String&&(!(0,r.l)(this)||h&&h.origin&&this.originIdOf(i)>(0,l.M9)(h.origin)),b={object:this,propertyName:i,value:t,targetUrl:m,dest:o,targetPropertyName:a,context:h,params:e};h&&h.portalItem&&m&&!(0,s.YP)(m)?f?function(e){const{context:t,targetUrl:i,params:r,value:n,dest:o,targetPropertyName:l}=e;if(!t.portalItem)return;const a=t.portalItem.resourceFromPath(i),c=y(n,i,t),h=(0,u.B)(c),g=(0,s.Ml)(a.path),m=r?.compress??!1;h===g?(t.resources&&d({...e,resource:a,content:c,compress:m,updates:t.resources.toUpdate}),o[l]=i):p(e)}(b):function({context:e,targetUrl:t,dest:i,targetPropertyName:r}){e.portalItem&&e.resources&&(e.resources.toKeep.push({resource:e.portalItem.resourceFromPath(t),compress:!1}),i[r]=t)}(b):h&&h.portalItem&&(null==m||null!=(0,c.i)(m)||(0,s.jc)(m)||f)?p(b):o[a]=m}}}}(e,t,i);switch(e?.type??"other"){case"other":return{read:!0,write:!0};case"url":{const{read:e,write:t}=c.a;return{read:e,write:t}}}}(e,i,n);for(const e of t){const t=(0,a.CJ)(i,e,n);for(const e in h)t[e]=h[e]}}}function p(e){const{targetUrl:t,params:r,value:o,context:l,dest:a,targetPropertyName:h}=e;if(!l.portalItem)return;const p=(0,c.p)(t),g=p?.filename??(0,n.D)(),m=r?.prefix??p?.prefix,f=y(o,t,l),b=(0,s.v_)(m,g),v=`${b}.${(0,u.B)(f)}`,_=l.portalItem.resourceFromPath(v);(0,s.jc)(t)&&l.resources&&l.resources.pendingOperations.push(async function(e){const t=(await Promise.resolve().then(i.bind(i,3172))).default,{data:r}=await t(e,{responseType:"blob"});return r}(t).then((e=>{_.path=`${b}.${(0,u.B)(e)}`,a[h]=_.itemRelativeUrl})).catch((()=>{})));const w=r?.compress??!1;l.resources&&d({...e,resource:_,content:f,compress:w,updates:l.resources.toAdd}),a[h]=_.itemRelativeUrl}function d({object:e,propertyName:t,updates:i,resource:r,content:s,compress:n}){i.push({resource:r,content:s,compress:n,finish:i=>{!function(e,t,i){"string"==typeof e[t]?e[t]=i.url:e[t].url=i.url}(e,t,i)}})}function y(e,t,i){return"string"==typeof e?{url:t}:new Blob([JSON.stringify(e.toJSON(i))],{type:"application/json"})}},52421:(e,t,i)=>{function r(e){return(t,i)=>{t[i]=e}}i.d(t,{c:()=>r})},70921:(e,t,i)=>{i.d(t,{R:()=>n,Z:()=>s});var r=i(46791);function s(e,t,i=r.Z){return t||(t=new i),t===e||(t.removeAll(),(s=e)&&(Array.isArray(s)||"items"in s&&Array.isArray(s.items))?t.addMany(e):e&&t.add(e)),t;var s}function n(e){return e}},35463:(e,t,i)=>{i.d(t,{JE:()=>o,Nm:()=>n,rJ:()=>l}),i(80442);const r={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},s={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,i){const r=new Date(e.getTime());if(t&&i){const e=s[i],{getter:n,setter:o,multiplier:l}=e;if("months"===i){const e=function(e,t){const i=new Date(e,t+1,1);return i.setDate(0),i.getDate()}(r.getFullYear(),r.getMonth()+t);r.getDate()>e&&r.setDate(e)}r[o](r[n]()+t*l)}return r}function o(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function l(e,t,i){return 0===e?0:e*r[t]/r[i]}},79235:(e,t,i)=>{i.d(t,{Z:()=>v});var r,s=i(43697),n=i(67676),o=i(35454),l=i(96674),a=i(67900),u=i(20941),c=i(5600),h=(i(75215),i(71715)),p=i(52011),d=i(30556);const y=(0,o.w)()({orthometric:"gravity-related-height",gravity_related_height:"gravity-related-height",ellipsoidal:"ellipsoidal"}),g=y.jsonValues.slice();(0,n.e$)(g,"orthometric");const m=(0,o.w)()({meter:"meters",foot:"feet","us-foot":"us-feet","clarke-foot":"clarke-feet","clarke-yard":"clarke-yards","clarke-link":"clarke-links","sears-yard":"sears-yards","sears-foot":"sears-feet","sears-chain":"sears-chains","benoit-1895-b-chain":"benoit-1895-b-chains","indian-yard":"indian-yards","indian-1937-yard":"indian-1937-yards","gold-coast-foot":"gold-coast-feet","sears-1922-truncated-chain":"sears-1922-truncated-chains","50-kilometers":"50-kilometers","150-kilometers":"150-kilometers"});let f=r=class extends l.wq{constructor(e){super(e),this.heightModel="gravity-related-height",this.heightUnit="meters",this.vertCRS=null}writeHeightModel(e,t,i){return y.write(e,t,i)}readHeightModel(e,t,i){return y.read(e)||(i&&i.messages&&i.messages.push(function(e,t){return new u.Z("height-model:unsupported",`Height model of value '${e}' is not supported`,t)}(e,{context:i})),null)}readHeightUnit(e,t,i){return m.read(e)||(i&&i.messages&&i.messages.push(b(e,{context:i})),null)}readHeightUnitService(e,t,i){return(0,a.$C)(e)||m.read(e)||(i&&i.messages&&i.messages.push(b(e,{context:i})),null)}readVertCRS(e,t){return t.vertCRS||t.ellipsoid||t.geoid}clone(){return new r({heightModel:this.heightModel,heightUnit:this.heightUnit,vertCRS:this.vertCRS})}equals(e){return!!e&&(this===e||this.heightModel===e.heightModel&&this.heightUnit===e.heightUnit&&this.vertCRS===e.vertCRS)}static deriveUnitFromSR(e,t){const i=(0,a.cM)(t);return new r({heightModel:e.heightModel,heightUnit:i,vertCRS:e.vertCRS})}write(e,t){return t={origin:"web-scene",...t},super.write(e,t)}static fromJSON(e){if(!e)return null;const t=new r;return t.read(e,{origin:"web-scene"}),t}};function b(e,t){return new u.Z("height-unit:unsupported",`Height unit of value '${e}' is not supported`,t)}(0,s._)([(0,c.Cb)({type:y.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:g,default:"ellipsoidal"}}}})],f.prototype,"heightModel",void 0),(0,s._)([(0,d.c)("web-scene","heightModel")],f.prototype,"writeHeightModel",null),(0,s._)([(0,h.r)(["web-scene","service"],"heightModel")],f.prototype,"readHeightModel",null),(0,s._)([(0,c.Cb)({type:m.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:m.jsonValues,write:m.write}}}})],f.prototype,"heightUnit",void 0),(0,s._)([(0,h.r)("web-scene","heightUnit")],f.prototype,"readHeightUnit",null),(0,s._)([(0,h.r)("service","heightUnit")],f.prototype,"readHeightUnitService",null),(0,s._)([(0,c.Cb)({type:String,constructOnly:!0,json:{origins:{"web-scene":{write:!0}}}})],f.prototype,"vertCRS",void 0),(0,s._)([(0,h.r)("service","vertCRS",["vertCRS","ellipsoid","geoid"])],f.prototype,"readVertCRS",null),f=r=(0,s._)([(0,p.j)("esri.geometry.HeightModelInfo")],f);const v=f},28865:(e,t,i)=>{i.r(t),i.d(t,{default:()=>Ye});var r=i(43697),s=i(46791),n=i(20102),o=i(92604),l=i(70586),a=i(16453),u=i(95330),c=i(5600),h=i(75215),p=(i(67676),i(71715)),d=i(52011),y=i(17896),g=i(65617),m=i(6570),f=i(87085),b=i(54295),v=i(17287),_=i(38009),w=i(16859),S=i(72965),C=i(20559),x=i(66677),V=i(21506),j=i(1231),M=i(2368),I=i(68251),R=i(96674),z=i(28576),D=i(51305),q=i(94961),A=i(46851);const N=(0,g.c)(),T=(0,q.a)(),E=(0,q.a)(),F=(0,q.a)(),Z=(0,g.f)(0,0,1),L=(0,g.f)(0,1,0),O=(0,g.f)(1,0,0);function k(e){(0,y.c)(N,e),(0,y.n)(N,N);const t=Math.atan2(N[1],N[0]),i=(0,D.s)((0,q.a)(),Z,-t);(0,y.q)(N,N,i);const r=-1*Math.atan2(N[2],N[0]);return[(0,A.a)(t)+270,(0,A.a)(r)+90]}function P(e,t){return(0,D.s)(E,Z,(0,A.t)(e-270)),(0,D.s)(F,L,(0,A.t)(t-90)),(0,D.m)(T,E,F),(0,y.c)(N,O),(0,y.q)(N,N,T),(0,y.n)(N,N),[N[0],N[1],N[2]]}var B=i(90578);let U=class extends((0,M.J)(R.wq)){constructor(e){super(e),this.enabled=!0,this.label="",this.normal=null,this.point=null}get orientation(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,t]=k(this.normal);return I.BV.normalize((0,h.q9)(e),0,!0)}set orientation(e){const t=P(e,this.tilt);this._set("normal",t),this._set("orientation",e)}get tilt(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,t]=k(this.normal);return I.BV.normalize((0,h.q9)(t),0,!0)}set tilt(e){const t=P(this.orientation,e);this._set("normal",t),this._set("tilt",e)}};(0,r._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],U.prototype,"enabled",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],U.prototype,"label",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),(0,B.p)((e=>I.BV.normalize((0,h.q9)(e),0,!0)))],U.prototype,"orientation",null),(0,r._)([(0,c.Cb)({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),(0,B.p)((e=>I.BV.normalize((0,h.q9)(e),0,!0)))],U.prototype,"tilt",null),(0,r._)([(0,c.Cb)({type:[Number],json:{write:!0}})],U.prototype,"normal",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:!0}})],U.prototype,"point",void 0),U=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelSlice")],U);const $=U;var J=i(25929);let Y=class extends((0,M.J)(R.wq)){constructor(){super(...arguments),this.enabled=!0,this.href=null,this.id=null,this.label="",this.normal=null,this.point=null,this.sizeInPixel=null,this.slices=null,this.timeId=0,this.variableId=null}get orientation(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,t]=k(this.normal);return I.BV.normalize((0,h.q9)(e),0,!0)}get tilt(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,t]=k(this.normal);return I.BV.normalize((0,h.q9)(t),0,!0)}};(0,r._)([(0,c.Cb)({type:Boolean,json:{default:!0,write:!0}})],Y.prototype,"enabled",void 0),(0,r._)([(0,c.Cb)({type:String,json:{origins:{service:{read:J.r}},write:{enabled:!0,isRequired:!0}}}),(0,z.B)({origins:["web-scene"],type:"resource",prefix:"sections",compress:!0})],Y.prototype,"href",void 0),(0,r._)([(0,c.Cb)({type:h.z8,json:{write:{enabled:!0,isRequired:!0}}})],Y.prototype,"id",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Y.prototype,"label",void 0),(0,r._)([(0,c.Cb)({type:Number,clonable:!1,readOnly:!0,range:{min:0,max:360}})],Y.prototype,"orientation",null),(0,r._)([(0,c.Cb)({type:Number,clonable:!1,readOnly:!0,range:{min:0,max:360}})],Y.prototype,"tilt",null),(0,r._)([(0,c.Cb)({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],Y.prototype,"normal",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],Y.prototype,"point",void 0),(0,r._)([(0,c.Cb)({type:[h.z8],json:{write:{enabled:!0,isRequired:!0}}})],Y.prototype,"sizeInPixel",void 0),(0,r._)([(0,c.Cb)({type:[$],json:{write:!0}})],Y.prototype,"slices",void 0),(0,r._)([(0,c.Cb)({type:h.z8,json:{default:0,write:!0}})],Y.prototype,"timeId",void 0),(0,r._)([(0,c.Cb)({type:h.z8,json:{write:{enabled:!0,isRequired:!0}}})],Y.prototype,"variableId",void 0),Y=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelSection")],Y);const H=Y;let W=class extends R.wq{constructor(){super(...arguments),this.diffuseFactor=.5,this.specularFactor=.5}};(0,r._)([(0,c.Cb)({type:Number,range:{min:0,max:1},json:{default:.5,write:!0}})],W.prototype,"diffuseFactor",void 0),(0,r._)([(0,c.Cb)({type:Number,range:{min:0,max:1},json:{default:.5,write:!0}})],W.prototype,"specularFactor",void 0),W=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelSimpleShading")],W);const X=W;let G=class extends R.wq{constructor(){super(...arguments),this.continuity=null,this.hasNoData=!1,this.noData=0,this.offset=0,this.scale=1,this.type=null}};(0,r._)([(0,c.Cb)({type:["discrete","continuous"],json:{write:!0}})],G.prototype,"continuity",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],G.prototype,"hasNoData",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],G.prototype,"noData",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],G.prototype,"offset",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],G.prototype,"scale",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:{enabled:!0,isRequired:!0}}})],G.prototype,"type",void 0),G=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelFormat")],G);const K=G;let Q=class extends R.wq{constructor(){super(...arguments),this.id=null,this.description="",this.name=null,this.originalFormat=null,this.renderingFormat=null,this.unit="",this.volumeId=0,this.type=null}};(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],Q.prototype,"id",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Q.prototype,"description",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:{enabled:!0,isRequired:!0}}})],Q.prototype,"name",void 0),(0,r._)([(0,c.Cb)({type:K,json:{write:!0}})],Q.prototype,"originalFormat",void 0),(0,r._)([(0,c.Cb)({type:K,json:{write:{enabled:!0,isRequired:!0}}})],Q.prototype,"renderingFormat",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Q.prototype,"unit",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],Q.prototype,"volumeId",void 0),(0,r._)([(0,c.Cb)({type:["stc-hot-spot-results","stc-cluster-outlier-results","stc-estimated-bin","generic-nearest-interpolated"],json:{write:!0}})],Q.prototype,"type",void 0),Q=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelVariable")],Q);const ee=Q;var te=i(22974),ie=i(22303);let re=class extends((0,M.J)(R.wq)){constructor(){super(...arguments),this.color=ie.Z.fromArray([0,0,0,0]),this.value=0,this.enabled=!0,this.label="",this.colorLocked=!1}};(0,r._)([(0,c.Cb)({type:ie.Z,json:{type:[h.z8],write:{enabled:!0,isRequired:!0}}})],re.prototype,"color",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],re.prototype,"value",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{default:!0,write:!0}})],re.prototype,"enabled",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],re.prototype,"label",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{default:!1,write:!0}})],re.prototype,"colorLocked",void 0),re=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelIsosurface")],re);const se=re;var ne=i(70921),oe=i(22021);let le=class extends((0,M.J)(R.wq)){constructor(){super(...arguments),this.color=null,this.position=0}};(0,r._)([(0,c.Cb)({type:ie.Z,json:{type:[h.z8],write:{enabled:!0,isRequired:!0}}})],le.prototype,"color",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],le.prototype,"position",void 0),le=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelColorStop")],le);const ae=le;let ue=class extends((0,M.J)(R.wq)){constructor(){super(...arguments),this.opacity=1,this.position=0}};(0,r._)([(0,c.Cb)({type:Number,json:{name:"alpha",write:{enabled:!0,isRequired:!0}}})],ue.prototype,"opacity",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],ue.prototype,"position",void 0),ue=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelOpacityStop")],ue);const ce=ue;let he=class extends((0,M.J)(R.wq)){constructor(){super(...arguments),this.enabled=!1,this.range=null}};(0,r._)([(0,c.Cb)({type:Boolean,json:{default:!1,write:!0}})],he.prototype,"enabled",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:!0}})],he.prototype,"range",void 0),he=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelRangeFilter")],he);const pe=he;var de,ye;(ye=de||(de={}))[ye.Color=1]="Color",ye[ye.Alpha=2]="Alpha",ye[ye.Both=3]="Both";let ge=class extends((0,M.J)(R.wq)){constructor(e){super(e),this.interpolation=null,this.stretchRange=null,this.rangeFilter=null,this._colorMapSize=256,this.colorStops=new(s.Z.ofType(ae)),this.opacityStops=new(s.Z.ofType(ce))}set colorStops(e){this._set("colorStops",(0,ne.Z)(e,this._get("colorStops"),s.Z.ofType(ae)))}set opacityStops(e){this._set("opacityStops",(0,ne.Z)(e,this._get("opacityStops"),s.Z.ofType(ce)))}getPreviousNext(e,t,i){let r=e;for(;--r>0&&t[r].type!==i&&t[r].type!==de.Both;);let s=e;const n=t.length;for(;++s<n&&t[s].type!==i&&t[s].type!==de.Both;);return[r,s]}get rasterizedTransferFunction(){const e=[];if(this.colorStops.length<2)return e;const t=[],i=[];for(const t of this.colorStops){if(!t.color)return e;i.push({color:{r:t.color.r,g:t.color.g,b:t.color.b,a:Math.round(255*(1-t.color.a))},position:t.position,type:de.Color})}if(0===this.opacityStops.length)for(const e of i)t.push({color:e.color,position:e.position});else{for(const e of this.opacityStops){const t=(0,oe.uZ)(e.position,0,1),r=Math.round(255*(0,oe.uZ)(1-e.opacity,0,1));let s=!1;for(const e of i)if(e.type===de.Color&&Math.abs(e.position-t)<1e-5){e.color.a=r,e.type=de.Both,s=!0;break}s||i.push({color:{r:0,g:0,b:0,a:r},position:e.position,type:de.Alpha})}i.sort(((e,t)=>e.position<t.position?-1:1));const e=i.length;for(let t=0;t<e;++t){const r=i[t];if(r.type!==de.Both)if(r.type===de.Color){const[s,n]=this.getPreviousNext(t,i,de.Alpha);if(-1!==s&&n!==e){const e=(r.position-i[s].position)/(i[n].position-i[s].position);r.color.a=Math.round((0,oe.t7)(i[s].color.a,i[n].color.a,e))}else r.color.a=-1!==s?i[s].color.a:i[n].color.a}else{const[s,n]=this.getPreviousNext(t,i,de.Color);if(-1!==s&&n!==e){const e=(r.position-i[s].position)/(i[n].position-i[s].position),t=i[s].color,o=i[n].color;["r","g","b"].forEach((i=>{r.color[i]=Math.round((0,oe.t7)(t[i],o[i],e))}))}else["r","g","b"].forEach(-1!==s?e=>{r.color[e]=i[s][e]}:e=>{r.color[e]=i[n][e]})}}for(const e of i)t.push({color:e.color,position:e.position})}t[0].position=0,t[t.length-1].position=1;let r=0,s=1;for(let i=0;i<this._colorMapSize;++i){const n=i/this._colorMapSize;for(;n>t[s].position;)r=s++;const o=(n-t[r].position)/(t[s].position-t[r].position),l=t[r].color,a=t[s].color,u=new ie.Z;["r","g","b"].forEach((e=>{u[e]=Math.round((0,oe.t7)(l[e],a[e],o))})),u.a=(0,oe.uZ)(1-(0,oe.t7)(l.a,a.a,o)/255,0,1),e.push(u)}return e}getColorForContinuousDataValue(e,t){const i=this.rasterizedTransferFunction;if(this.colorStops.length<2||!Array.isArray(this.stretchRange)||this.stretchRange.length<2||i.length<256)return null;let r=this.stretchRange[0],s=this.stretchRange[1];if(r>s){const e=r;r=s,s=e}e=(0,oe.uZ)(e,r,s);const n=i[Math.round((e-r)/(s-r)*(this._colorMapSize-1))].clone();return t||(n.a=1),n}};(0,r._)([(0,c.Cb)({type:["linear","nearest"],json:{write:!0}})],ge.prototype,"interpolation",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],ge.prototype,"stretchRange",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(ae),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.colorStops&&this.colorStops.length>0}}}}})],ge.prototype,"colorStops",null),(0,r._)([(0,c.Cb)({type:s.Z.ofType(ce),json:{read:{source:"alphaStops"},write:{enabled:!0,target:"alphaStops",overridePolicy(){return{enabled:!!this.opacityStops&&this.opacityStops.length>0}}}}})],ge.prototype,"opacityStops",null),(0,r._)([(0,c.Cb)({type:pe,json:{write:!0}})],ge.prototype,"rangeFilter",void 0),(0,r._)([(0,c.Cb)({type:[ie.Z],clonable:!1,json:{read:!1}})],ge.prototype,"rasterizedTransferFunction",null),ge=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelTransferFunctionStyle")],ge);const me=ge;let fe=class extends((0,M.J)(R.wq)){constructor(){super(...arguments),this.color=ie.Z.fromArray([0,0,0,0]),this.value=0,this.enabled=!0,this.label=""}};(0,r._)([(0,c.Cb)({type:ie.Z,json:{type:[h.z8],write:{enabled:!0,isRequired:!0}}})],fe.prototype,"color",void 0),(0,r._)([(0,c.Cb)({type:h.z8,json:{write:{enabled:!0,isRequired:!0}}})],fe.prototype,"value",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{default:!0,write:!0}})],fe.prototype,"enabled",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],fe.prototype,"label",void 0),fe=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelUniqueValue")],fe);const be=fe;var ve;let _e=ve=class extends R.wq{constructor(e){super(e),this.variableId=0,this.label="",this.transferFunction=null,this.uniqueValues=null,this.isosurfaces=null,this.uniqueValues=new(s.Z.ofType(be)),this.isosurfaces=new(s.Z.ofType(se))}clone(){return new ve({variableId:this.variableId,label:this.label,transferFunction:(0,te.d9)(this.transferFunction),uniqueValues:(0,te.d9)(this.uniqueValues),isosurfaces:(0,te.d9)(this.isosurfaces)})}};(0,r._)([(0,c.Cb)({type:h.z8,json:{write:{enabled:!0,isRequired:!0}}})],_e.prototype,"variableId",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],_e.prototype,"label",void 0),(0,r._)([(0,c.Cb)({type:me,json:{write:{enabled:!0,overridePolicy(){return{enabled:!this.uniqueValues||this.uniqueValues.length<1}}}}})],_e.prototype,"transferFunction",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(be),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.uniqueValues&&this.uniqueValues.length>0}}}}})],_e.prototype,"uniqueValues",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(se),json:{write:{enabled:!0,overridePolicy(){const e=!this.uniqueValues||this.uniqueValues.length<1,t=!!this.isosurfaces&&this.isosurfaces.length>0;return{enabled:e&&t}}}}})],_e.prototype,"isosurfaces",void 0),_e=ve=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelVariableStyle")],_e);const we=_e;var Se=i(94139),Ce=i(82971),xe=i(8744);let Ve=class extends R.wq{constructor(){super(...arguments),this.values=null}};(0,r._)([(0,c.Cb)({type:[Number],json:{write:!0}})],Ve.prototype,"values",void 0),Ve=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelIrregularSpacing")],Ve);const je=Ve;let Me=class extends R.wq{constructor(){super(...arguments),this.scale=1,this.offset=0}};(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],Me.prototype,"scale",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],Me.prototype,"offset",void 0),Me=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelRegularSpacing")],Me);const Ie=Me;let Re=class extends R.wq{constructor(){super(...arguments),this.irregularSpacing=null,this.isPositiveUp=!0,this.isWrappedDateLine=!1,this.label=null,this.name=null,this.quantity=null,this.regularSpacing=null,this.size=0,this.unit=null}get isRegular(){return(null==this.irregularSpacing||void 0===this.irregularSpacing)&&null!==this.regularSpacing}getRange(){return this.isRegular?[this.regularSpacing.offset,this.regularSpacing.offset+this.regularSpacing.scale*(this.size-1)]:Array.isArray(this.irregularSpacing?.values)&&this.irregularSpacing.values.length>1?[this.irregularSpacing.values[0],this.irregularSpacing.values[this.irregularSpacing.values.length-1]]:[0,0]}};(0,r._)([(0,c.Cb)({type:je,json:{write:!0}})],Re.prototype,"irregularSpacing",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],Re.prototype,"isPositiveUp",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],Re.prototype,"isWrappedDateLine",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Re.prototype,"label",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Re.prototype,"name",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Re.prototype,"quantity",void 0),(0,r._)([(0,c.Cb)({type:Ie,json:{write:!0}})],Re.prototype,"regularSpacing",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:!0}})],Re.prototype,"size",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Re.prototype,"unit",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{read:!1}})],Re.prototype,"isRegular",null),Re=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelDimension")],Re);const ze=Re,De="esri.layers.voxel.VoxelVolume",qe=o.Z.getLogger(De);let Ae=class extends R.wq{constructor(e){super(e),this.id=0,this.dimensions=null,this.spatialReference=Ce.Z.WGS84}get zDimension(){if(!this.dimensions)return-1;if(!Array.isArray(this.dimensions))return-1;if(4!==this.dimensions.length)return-1;for(let e=2;e<4;++e)if(this.dimensions[e].size>0)return e;return-1}get isValid(){return!(!this.dimensions||!Array.isArray(this.dimensions)||4!==this.dimensions.length||this.dimensions[0].size<1||this.dimensions[1].size<1||-1===this.zDimension||this.dimensions[this.zDimension].size<1)}get originInLayerSpace3D(){if(!this.isValid||"xyt"===this.volumeType)return[0,0,0];const e=this.dimensions[0].getRange(),t=this.dimensions[1].getRange(),i=this.dimensions[2],r=i.isRegular?i.getRange():[0,i.size];return[e[0],t[0],r[0]]}get voxelSizeInLayerSpaceSigned(){if(!this.isValid||"xyt"===this.volumeType)return[0,0,0];const e=this.dimensions[0].getRange(),t=this.dimensions[1].getRange(),i=this.dimensions[2],r=i.isRegular?i.getRange():[0,i.size],s=[this.sizeInVoxels[0],this.sizeInVoxels[1],this.sizeInVoxels[2]];for(let e=0;e<3;++e)s[e]<2?s[e]=1:s[e]-=1;return i.isRegular&&!i.isPositiveUp&&(s[2]*=-1),[(e[1]-e[0])/s[0],(t[1]-t[0])/s[1],(r[1]-r[0])/s[2]]}get volumeType(){if(this.isValid){const e=this.dimensions[2].size>0,t=this.dimensions[3].size>0;if(!e&&t)return"xyt";if(e&&t)return"xyzt"}return"xyz"}get sizeInVoxels(){if(!this.isValid)return[0,0,0];const e=this.zDimension;return[this.dimensions[0].size,this.dimensions[1].size,this.dimensions[e].size]}computeVoxelSpaceLocation(e){if(!this.isValid)return[0,0,0];if("xyt"===this.volumeType)return qe.error("computeVoxelSpacePosition cannot be used with XYT volumes."),[0,0,0];if(!(0,xe.fS)(this.spatialReference,e.spatialReference))return qe.error("pos argument should have the same spatial reference as the VoxelLayer."),[0,0,0];const t=(0,g.f)(e.x,e.y,e.z??0);(0,y.b)(t,t,this.originInLayerSpace3D),(0,y.C)(t,t,this.voxelSizeInLayerSpaceSigned);const i=this.dimensions[this.zDimension];if(!i.isRegular&&Array.isArray(i.irregularSpacing?.values)&&i.irregularSpacing.values.length>1){const r=e.z??0,s=i.irregularSpacing.values,n=i.isPositiveUp?1:-1,o=s.reduce(((e,t)=>Math.abs(n*t-r)<Math.abs(n*e-r)?t:e));for(let e=0;e<s.length;++e)if(s[e]===o){t[2]=e;break}}return[t[0],t[1],t[2]]}computeLayerSpaceLocation(e){if(!this.isValid)return new Se.Z({x:0,y:0,spatialReference:this.spatialReference});const t=(0,g.d)(e);if((0,y.B)(t,t,this.voxelSizeInLayerSpaceSigned),(0,y.a)(t,t,this.originInLayerSpace3D),"xyt"===this.volumeType)return new Se.Z({x:t[0],y:t[1],spatialReference:this.spatialReference});const i=this.dimensions[this.zDimension];return i.isRegular||Array.isArray(i.irregularSpacing?.values)&&(e[2]<0?t[2]=i.irregularSpacing.values[0]:e[2]<i.irregularSpacing.values.length?t[2]=i.irregularSpacing.values[e[2]]:t[2]=i.irregularSpacing.values[i.irregularSpacing.values.length-1],i.isPositiveUp||(t[2]*=-1)),new Se.Z({x:t[0],y:t[1],z:t[2],spatialReference:this.spatialReference})}};(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],Ae.prototype,"id",void 0),(0,r._)([(0,c.Cb)({type:[ze],json:{write:{enabled:!0,isRequired:!0}}})],Ae.prototype,"dimensions",void 0),(0,r._)([(0,c.Cb)({type:Ce.Z,json:{read:{enabled:!1}}})],Ae.prototype,"spatialReference",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{read:!1}})],Ae.prototype,"zDimension",null),(0,r._)([(0,c.Cb)({type:[Boolean],json:{read:!1}})],Ae.prototype,"isValid",null),(0,r._)([(0,c.Cb)({type:[Number],json:{read:!1}})],Ae.prototype,"originInLayerSpace3D",null),(0,r._)([(0,c.Cb)({type:[Number],json:{read:!1}})],Ae.prototype,"voxelSizeInLayerSpaceSigned",null),(0,r._)([(0,c.Cb)({type:["xyz","xyzt","xyt"],json:{read:{enabled:!1}}})],Ae.prototype,"volumeType",null),(0,r._)([(0,c.Cb)({type:[Number],json:{read:!1}})],Ae.prototype,"sizeInVoxels",null),Ae=(0,r._)([(0,d.j)(De)],Ae);const Ne=Ae;var Te;let Ee=Te=class extends R.wq{constructor(){super(...arguments),this.apronWidth=1,this.brickSize=[32,32,32],this.maxLodLevel=0,this.nodeSize=[4,4,4]}isValid(){const e=new Te;return e.apronWidth===this.apronWidth&&e.maxLodLevel===this.maxLodLevel&&!!this.brickSize&&!!this.nodeSize&&!(!Array.isArray(this.brickSize)||!Array.isArray(this.nodeSize))&&3===this.brickSize.length&&3===this.nodeSize.length&&32===this.brickSize[0]&&32===this.brickSize[1]&&32===this.brickSize[2]&&4===this.nodeSize[0]&&4===this.nodeSize[1]&&4===this.nodeSize[2]}};(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],Ee.prototype,"apronWidth",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],Ee.prototype,"brickSize",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],Ee.prototype,"maxLodLevel",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],Ee.prototype,"nodeSize",void 0),Ee=Te=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelVolumeIndex")],Ee);const Fe=Ee;let Ze=class extends((0,M.J)(R.wq)){constructor(e){super(e),this.enabled=!0,this.label="",this.normal=null,this.point=null}get orientation(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,t]=k(this.normal);return I.BV.normalize((0,h.q9)(e),0,!0)}set orientation(e){const t=P(e,this.tilt);this._set("normal",t),this._set("orientation",e)}get tilt(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,t]=k(this.normal);return I.BV.normalize((0,h.q9)(t),0,!0)}set tilt(e){const t=P(this.orientation,e);this._set("normal",t),this._set("tilt",e)}};(0,r._)([(0,c.Cb)({type:Boolean,json:{default:!0,write:!0}})],Ze.prototype,"enabled",void 0),(0,r._)([(0,c.Cb)({type:String,json:{write:!0}})],Ze.prototype,"label",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),(0,B.p)((e=>I.BV.normalize((0,h.q9)(e),0,!0)))],Ze.prototype,"orientation",null),(0,r._)([(0,c.Cb)({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),(0,B.p)((e=>I.BV.normalize((0,h.q9)(e),0,!0)))],Ze.prototype,"tilt",null),(0,r._)([(0,c.Cb)({type:[Number],json:{write:!0}})],Ze.prototype,"normal",void 0),(0,r._)([(0,c.Cb)({type:[Number],json:{write:!0}})],Ze.prototype,"point",void 0),Ze=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelDynamicSection")],Ze);const Le=Ze;var Oe;let ke=Oe=class extends R.wq{constructor(e){super(e),this.volumeId=0,this.verticalExaggeration=1,this.exaggerationMode="scale-height",this.verticalOffset=0,this.slices=new(s.Z.ofType($)),this.dynamicSections=new(s.Z.ofType(Le))}set slices(e){this._set("slices",(0,ne.Z)(e,this._get("slices"),s.Z.ofType($)))}set dynamicSections(e){this._set("dynamicSections",(0,ne.Z)(e,this._get("dynamicSections"),s.Z.ofType(Le)))}clone(){return new Oe({volumeId:this.volumeId,verticalExaggeration:this.verticalExaggeration,exaggerationMode:this.exaggerationMode,verticalOffset:this.verticalOffset,slices:(0,te.d9)(this.slices),dynamicSections:(0,te.d9)(this.dynamicSections)})}};(0,r._)([(0,c.Cb)({type:h.z8,json:{write:{enabled:!0,isRequired:!0}}})],ke.prototype,"volumeId",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{default:1,write:!0}})],ke.prototype,"verticalExaggeration",void 0),(0,r._)([(0,c.Cb)({type:["scale-position","scale-height"],json:{default:"scale-height",write:!0}})],ke.prototype,"exaggerationMode",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{default:0,write:!0}})],ke.prototype,"verticalOffset",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType($),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.slices&&this.slices.length>0}}}}})],ke.prototype,"slices",null),(0,r._)([(0,c.Cb)({type:s.Z.ofType(Le),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.dynamicSections&&this.dynamicSections.length>0}}}}})],ke.prototype,"dynamicSections",null),ke=Oe=(0,r._)([(0,d.j)("esri.layers.voxel.VoxelVolumeStyle")],ke);const Pe=ke;var Be=i(32163);const Ue="esri.layers.VoxelLayer",$e=o.Z.getLogger(Ue);let Je=class extends((0,C.Vt)((0,v.Y)((0,_.q)((0,w.I)((0,S.M)((0,a.R)((0,b.V)(f.Z)))))))){constructor(e){super(e),this.serviceRoot="",this.operationalLayerType="Voxel",this.legendEnabled=!0,this.title=null,this.sections=null,this.currentVariableId=0,this.volumeStyles=null,this.renderMode="volume",this.variableStyles=null,this.enableSlices=!0,this.enableSections=!0,this.enableDynamicSections=!0,this.enableIsosurfaces=!0,this.shading=new X,this.opacity=1,this.variables=new s.Z,this.volumes=new s.Z,this.index=null,this.minScale=0,this.maxScale=0,this.type="voxel",this.version={major:Number.NaN,minor:Number.NaN,versionString:""},this.fullExtent=null,this.popupEnabled=!0,this.popupTemplate=null,this.test=null,this.volumeStyles=new(s.Z.ofType(Pe)),this.variableStyles=new(s.Z.ofType(we)),this.sections=new(s.Z.ofType(H)),e?.constantUpscaling&&(this.test={constantUpscaling:!0})}set url(e){this._set("url",(0,x.Nm)(e,$e))}load(e){const t=(0,l.pC)(e)?e.signal:null,i=this.loadFromPortal({supportedTypes:["Scene Service"]},e).catch(u.r9).then((()=>this._fetchService(t))).then((()=>this.serviceRoot=this.url));return this.addResolvingPromise(i),Promise.resolve(this)}read(e,t){super.read(e,t);for(const e of this.volumes)e.spatialReference=this.spatialReference}readVersion(e,t){return super.parseVersionString(e)}validateLayer(e){if(e.layerType&&e.layerType!==this.operationalLayerType)throw new n.Z("voxel-layer:layer-type-not-supported","VoxelLayer does not support this layer type",{layerType:e.layerType});if(isNaN(this.version.major)||isNaN(this.version.minor)||this.version.major<3)throw new n.Z("layer:service-version-not-supported","Service version is not supported.",{serviceVersion:this.version.versionString,supportedVersions:"3.x"});if(this.version.major>3)throw new n.Z("layer:service-version-too-new","Service version is too new.",{serviceVersion:this.version.versionString,supportedVersions:"3.x"})}readFullExtent(e,t,i){if(null!=e&&"object"==typeof e){const r=m.Z.fromJSON(e,i);if(0===r.zmin&&0===r.zmax&&Array.isArray(t.volumes)){const e=Ne.fromJSON(t.volumes[0]);if(e.isValid&&"xyt"!==e.volumeType){const t=e.dimensions[2];if(t.isRegular){let e=t.regularSpacing.offset,i=t.regularSpacing.offset+t.regularSpacing.scale*(t.size-1);if(e>i){const t=e;e=i,i=t}r.zmin=e,r.zmax=i}}}return r}return null}get voxelFields(){const e=[new j.Z({name:"Voxel.ServiceValue",alias:"Value",domain:null,editable:!1,length:128,type:"string"}),new j.Z({name:"Voxel.ServiceVariableLabel",alias:"Variable",domain:null,editable:!1,length:128,type:"string"}),new j.Z({name:"Voxel.Position",alias:"Voxel Position",domain:null,editable:!1,length:128,type:"string"})],t=this.getVolume(null);if((0,l.pC)(t)){if("xyzt"===t.volumeType||"xyt"===t.volumeType){const t=new j.Z({name:"Voxel.ServiceLocalTime",alias:"Local Time",domain:null,editable:!1,length:128,type:"string"});e.push(t);const i=new j.Z({name:"Voxel.ServiceNativeTime",alias:"Native Time",domain:null,editable:!1,length:128,type:"string"});e.push(i)}if("xyt"!==t.volumeType){const t=new j.Z({name:"Voxel.ServiceDepth",alias:"Depth",domain:null,editable:!1,length:128,type:"string"});e.push(t)}}return e}get defaultPopupTemplate(){return this.createPopupTemplate()}createPopupTemplate(e){const t=this.voxelFields,i=this.title;return(0,Be.eZ)({fields:t,title:i},e)}getConfiguration(){const e={layerType:this.operationalLayerType,version:this.version.versionString,name:this.title,spatialReference:this.spatialReference,fullExtent:this.fullExtent,volumes:this.volumes.toJSON(),variables:this.variables.toJSON(),index:this.index?.toJSON(),sections:this.getSections(),style:{volumeStyles:this.getVolumeStyles(),currentVariableId:this.currentVariableId,renderMode:this.renderMode,variableStyles:this.getVariableStyles(),enableSections:this.enableSections,enableDynamicSections:this.enableDynamicSections,enableIsosurfaces:this.enableIsosurfaces,enableSlices:this.enableSlices,shading:this.shading}};return e.index&&this.index?.isValid()?JSON.stringify(e):""}getVariableStyle(e){let t=-1;if(t=(0,l.pC)(e)?e:this.currentVariableId,!this.variableStyles||-1===t)return null;const i=this.variableStyles.findIndex((e=>e.variableId===t));return i<0?null:this.variableStyles.getItemAt(i)}getVariable(e){let t=-1;if(t=(0,l.pC)(e)?e:this.currentVariableId,!this.variables||-1===t)return null;const i=this.variables.findIndex((e=>e.id===t));return i<0?null:this.variables.getItemAt(i)}getVolume(e){const t=this.getVariable(e);return(0,l.pC)(t)?this.volumes.find((({id:e})=>e===t.volumeId)):null}getVolumeStyle(e){const t=this.getVariable(e);return(0,l.pC)(t)?this.volumeStyles.find((({volumeId:e})=>e===t.volumeId)):null}getColorForContinuousDataValue(e,t,i){const r=this.getVariable(e);if((0,l.Wi)(r)||"continuous"!==r.renderingFormat?.continuity)return null;if(!this.variableStyles)return null;const s=this.variableStyles.findIndex((t=>t.variableId===e));if(s<0)return null;const n=this.variableStyles.getItemAt(s);return n.transferFunction?n.transferFunction.getColorForContinuousDataValue(t,i):null}getSections(){const e=[];for(const t of this.sections)e.push(new H({enabled:t.enabled,href:t.href,id:t.id,label:t.label,normal:t.normal,point:t.point,sizeInPixel:t.sizeInPixel,slices:t.slices,timeId:t.timeId,variableId:t.variableId}));return e}getVariableStyles(){const e=[];for(const t of this.variableStyles){const i=this._getVariable(t);if((0,l.pC)(i)){const r=t.clone();r.isosurfaces.length>4&&(r.isosurfaces=r.isosurfaces.slice(0,3),$e.error("A maximum of 4 isosurfaces are supported for Voxel Layers."));for(const e of r.isosurfaces)if(!e.colorLocked){const t=this.getColorForContinuousDataValue(r.variableId,e.value,!1);null===t||t.equals(e.color)||(e.color=t)}if("continuous"===i.renderingFormat.continuity)(null===r.transferFunction||r.transferFunction.colorStops.length<2)&&$e.error(`VoxelVariableStyle for variable ${i.id} is invalid. At least 2 color stops are required in the transferFunction for continuous Voxel Layer variables.`),null!==r.transferFunction&&(Array.isArray(r.transferFunction.stretchRange)&&2===r.transferFunction.stretchRange.length||($e.error(`VoxelVariableStyle for variable ${i.id} is invalid. The stretchRange of the transferFunction for continuous Voxel Layer variables must be of the form [minimumDataValue, maximumDataValue].`),r.transferFunction.stretchRange=[0,1],r.transferFunction.colorStops.removeAll()));else if("discrete"===i.renderingFormat.continuity)if(0===t.uniqueValues.length)$e.error(`VoxelVariableStyle for variable ${i.id} is invalid. Unique values are required for discrete Voxel Layer variables.`);else for(const e of t.uniqueValues)null!==e.label&&void 0!==e.label||null===e.value||void 0===e.value||(e.label=e.value.toString());e.push(r)}else $e.error(`VoxelVariable ID=${t.variableId} doesn't exist, VoxelVariableStyle for this VoxelVariable will be ignored.`)}return e}getVolumeStyles(){const e=[];for(const t of this.volumeStyles){const i=this._getVolumeFromVolumeId(t.volumeId);if((0,l.pC)(i)){const r=t.clone();for(const e of r.slices)this._isPlaneValid(e,[0,1,i.zDimension],i.dimensions)||(e.enabled=!1,e.label="invalid");for(const e of r.dynamicSections)this._isPlaneValid(e,[0,1,i.zDimension],i.dimensions)||(e.enabled=!1,e.label="invalid");e.push(r)}else $e.error(`VoxelVolume ID=${t.volumeId} doesn't exist, VoxelVolumeStyle for this VoxelVolume will be ignored.`)}return e}_getVariable(e){const t=e.variableId;for(const e of this.variables)if(e.id===t)return e;return null}_getVolumeFromVolumeId(e){for(const t of this.volumes)if(t.id===e)return t;return null}_isPlaneValid(e,t,i){if(!e.point)return!1;if(!Array.isArray(e.point)||3!==e.point.length)return!1;if(!e.normal)return!1;if(!Array.isArray(e.normal)||3!==e.normal.length)return!1;for(let r=0;r<3;++r){const s=e.point[r];if(s<0||s>=i[t[r]].size)return!1}const r=(0,g.f)(e.normal[0],e.normal[1],e.normal[2]);return(0,y.n)(r,r),!(Math.abs(r[0])+Math.abs(r[1])+Math.abs(r[2])<1e-6||(e.normal[0]=r[0],e.normal[1]=r[1],e.normal[2]=r[2],0))}};(0,r._)([(0,c.Cb)({type:["Voxel"]})],Je.prototype,"operationalLayerType",void 0),(0,r._)([(0,c.Cb)(V.rn)],Je.prototype,"legendEnabled",void 0),(0,r._)([(0,c.Cb)({json:{write:!0}})],Je.prototype,"title",void 0),(0,r._)([(0,c.Cb)(V.HQ)],Je.prototype,"url",null),(0,r._)([(0,c.Cb)({type:s.Z.ofType(H),json:{origins:{"web-scene":{name:"layerDefinition.sections",write:!0}}}})],Je.prototype,"sections",void 0),(0,r._)([(0,c.Cb)({type:h.z8,json:{origins:{"web-scene":{name:"layerDefinition.style.currentVariableId",write:{enabled:!0,isRequired:!0,ignoreOrigin:!0}},service:{name:"style.currentVariableId"}}}})],Je.prototype,"currentVariableId",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(Pe),json:{origins:{"web-scene":{name:"layerDefinition.style.volumeStyles",write:!0},service:{name:"style.volumeStyles"}}}})],Je.prototype,"volumeStyles",void 0),(0,r._)([(0,c.Cb)({type:["volume","surfaces"],json:{origins:{"web-scene":{name:"layerDefinition.style.renderMode",write:!0},service:{name:"style.renderMode"}}}})],Je.prototype,"renderMode",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(we),json:{origins:{"web-scene":{name:"layerDefinition.style.variableStyles",write:!0},service:{name:"style.variableStyles"}}}})],Je.prototype,"variableStyles",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{origins:{"web-scene":{name:"layerDefinition.style.enableSlices",write:!0},service:{name:"style.enableSlices"}}}})],Je.prototype,"enableSlices",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{origins:{"web-scene":{name:"layerDefinition.style.enableSections",write:!0},service:{name:"style.enableSections"}}}})],Je.prototype,"enableSections",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{origins:{"web-scene":{name:"layerDefinition.style.enableDynamicSections",write:!0},service:{name:"style.enableDynamicSections"}}}})],Je.prototype,"enableDynamicSections",void 0),(0,r._)([(0,c.Cb)({type:Boolean,json:{origins:{"web-scene":{name:"layerDefinition.style.enableIsosurfaces",write:!0},service:{name:"style.enableIsosurfaces"}}}})],Je.prototype,"enableIsosurfaces",void 0),(0,r._)([(0,c.Cb)({type:X,json:{origins:{"web-scene":{name:"layerDefinition.style.shading",write:!0},service:{name:"style.shading"}}}})],Je.prototype,"shading",void 0),(0,r._)([(0,c.Cb)({type:["show","hide"]})],Je.prototype,"listMode",void 0),(0,r._)([(0,c.Cb)({type:Number,range:{min:0,max:1},nonNullable:!0,json:{read:!1,write:!1,origins:{"web-scene":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],Je.prototype,"opacity",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(ee)})],Je.prototype,"variables",void 0),(0,r._)([(0,c.Cb)({type:s.Z.ofType(Ne)})],Je.prototype,"volumes",void 0),(0,r._)([(0,c.Cb)({type:Fe})],Je.prototype,"index",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{name:"layerDefinition.minScale",read:!1,write:!1,origins:{service:{read:!1,write:!1}}}})],Je.prototype,"minScale",void 0),(0,r._)([(0,c.Cb)({type:Number,json:{name:"layerDefinition.maxScale",read:!1,write:!1,origins:{service:{read:!1,write:!1}}}})],Je.prototype,"maxScale",void 0),(0,r._)([(0,c.Cb)({json:{read:!1},readOnly:!0})],Je.prototype,"type",void 0),(0,r._)([(0,c.Cb)({readOnly:!0,json:{name:"serviceVersion"}})],Je.prototype,"version",void 0),(0,r._)([(0,p.r)("service","version")],Je.prototype,"readVersion",null),(0,r._)([(0,c.Cb)({type:m.Z})],Je.prototype,"fullExtent",void 0),(0,r._)([(0,p.r)("service","fullExtent",["fullExtent"])],Je.prototype,"readFullExtent",null),(0,r._)([(0,c.Cb)({readOnly:!0,clonable:!1,json:{read:!1}})],Je.prototype,"voxelFields",null),(0,r._)([(0,c.Cb)(V.C_)],Je.prototype,"popupEnabled",void 0),(0,r._)([(0,c.Cb)({readOnly:!0})],Je.prototype,"defaultPopupTemplate",null),Je=(0,r._)([(0,d.j)(Ue)],Je);const Ye=Je},66094:(e,t,i)=>{i.d(t,{B:()=>s});var r=i(17452);function s(e){return n[function(e){return e instanceof Blob?e.type:function(e){const t=(0,r.Ml)(e);return a[t]||o}(e.url)}(e)]||l}const n={},o="text/plain",l=n[o],a={png:"image/png",jpeg:"image/jpeg",jpg:"image/jpg",bmp:"image/bmp",gif:"image/gif",json:"application/json",txt:"text/plain",xml:"application/xml",svg:"image/svg+xml",zip:"application/zip",pbf:"application/vnd.mapbox-vector-tile",gz:"application/gzip","bin.gz":"application/octet-stream"};for(const e in a)n[a[e]]=e},32163:(e,t,i)=>{i.d(t,{eZ:()=>h});var r=i(51773),s=i(35671),n=i(84649),o=(i(63801),i(48074),i(38745),i(9190)),l=(i(10214),i(71423),i(44951),i(422)),a=i(63061);const u=["oid","global-id"],c=["oid","global-id","guid"];function h({displayField:e,editFieldsInfo:t,fields:i,objectIdField:a,title:u},c){if(!i)return null;const h=function({editFieldsInfo:e,fields:t,objectIdField:i},r){return function(e,t){const i=e;return t&&(e=e.filter((e=>!t.includes(e.type)))),e===i&&(e=e.slice()),e.sort(y),e}(t??[],r?.ignoreFieldTypes||v).map((t=>new l.Z({fieldName:t.name,isEditable:m(t,e),label:t.alias,format:f(t),visible:d(t,{editFieldsInfo:e,objectIdField:i,visibleFieldNames:r?.visibleFieldNames})})))}({editFieldsInfo:t,fields:i,objectIdField:a},c);if(!h.length)return null;const p=function(e){const t=(0,s.O5)(e),{titleBase:i}=e;return t?`${i}: {${t.trim()}}`:i??""}({titleBase:u,fields:i,displayField:e}),g=[new o.Z,new n.Z];return new r.Z({title:p,content:g,fieldInfos:h})}const p=[/^fnode_$/i,/^tnode_$/i,/^lpoly_$/i,/^rpoly_$/i,/^poly_$/i,/^subclass$/i,/^subclass_$/i,/^rings_ok$/i,/^rings_nok$/i,/shape/i,/perimeter/i,/objectid/i,/_i$/i],d=(e,{editFieldsInfo:t,objectIdField:i,visibleFieldNames:r})=>r?r.has(e.name):!(g(e.name,t)||i&&e.name===i||u.includes(e.type)||p.some((t=>t.test(e.name))));function y(e,t){return"oid"===e.type?-1:"oid"===t.type?1:b(e)?-1:b(t)?1:(e.alias||e.name).toLocaleLowerCase().localeCompare((t.alias||t.name).toLocaleLowerCase())}function g(e,t){if(!e||!t)return!1;const{creationDateField:i,creatorField:r,editDateField:s,editorField:n}=t;return[i&&i.toLowerCase(),r&&r.toLowerCase(),s&&s.toLowerCase(),n&&n.toLowerCase()].includes(e.toLowerCase())}function m(e,t){return e.editable&&!c.includes(e.type)&&!g(e.name,t)}function f(e){switch(e.type){case"small-integer":case"integer":case"single":return new a.Z({digitSeparator:!0,places:0});case"double":return new a.Z({digitSeparator:!0,places:2});case"date":return new a.Z({dateFormat:"long-month-day-year"});default:return"string"===e.type&&(0,s.Ec)(e.name)?new a.Z({digitSeparator:!0,places:0}):null}}function b(e){return"name"===(e.name&&e.name.toLowerCase())||"name"===(e.alias&&e.alias.toLowerCase())}const v=["geometry","blob","raster","guid","xml"]}}]);