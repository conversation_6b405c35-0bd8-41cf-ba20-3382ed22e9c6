<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitPlanMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           type,
                           plan_type,
                           name,
                           template_id,
                           fixed_date,
                           start_date,
                           end_date,
                           execution_user_id,
                           (select department_id from tb_user u where u.id = execution_user_id) as execution_user_department_id,
                           (select department_id from tb_user u where u.id = audit_user_id)     as audit_user_department_id,
                           audit_user_id,
                           execution_days,
                           interval_days,
                           execution_num,
                           station_ids,
                           remark,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_circuit_plan -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlanResponse">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="plan_type" property="planType"/>
        <result column="name" property="name"/>
        <result column="template_id" property="templateId"/>
        <result column="fixed_date" property="fixedDate"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execution_user_id" property="executionUserId"/>
        <result column="execution_user_department_id" property="executionUserDepartmentId"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="audit_user_department_id" property="auditUserIdDepartmentId"/>
        <result column="execution_days" property="executionDays"/>
        <result column="interval_days" property="intervalDays"/>
        <result column="execution_num" property="executionNum"/>
        <result column="station_ids" property="stationIds"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <insert id="save">
        INSERT INTO sp_circuit_plan(id,
                                    type,
                                    plan_type,
                                    name,
                                    template_id,
                                    fixed_date,
                                    start_date,
                                    end_date,
                                    execution_user_id,
                                    audit_user_id,
                                    execution_days,
                                    interval_days,
                                    execution_num,
                                    station_ids,
                                    remark,
                                    creator,
                                    create_time,
                                    tenant_id)
        VALUES (#{id},
                #{type},
                #{planType},
                #{name},
                #{templateId},
                #{fixedDate},
                #{startDate},
                #{endDate},
                #{executionUserId},
                #{auditUserId},
                #{executionDays},
                #{intervalDays},
                #{executionNum},
                #{stationIds},
                #{remark},
                #{creator},
                #{createTime},
                #{tenantId})
    </insert>

    <update id="update">
        update sp_circuit_plan
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="planType != null">
                plan_type = #{planType},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="fixedDate != null">
                fixed_date = #{fixedDate},
            </if>
            <if test="startDate != null">
                start_date = #{startDate},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="executionUserId != null">
                execution_user_id = #{executionUserId},
            </if>
            <if test="auditUserId != null">
                audit_user_id = #{auditUserId},
            </if>
            <if test="executionDays != null">
                execution_days = #{executionDays},
            </if>
            <if test="intervalDays != null">
                interval_days = #{intervalDays},
            </if>
            <if test="executionNum != null">
                execution_num = #{executionNum},
            </if>
            <if test="stationIds != null">
                station_ids = #{stationIds},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_circuit_plan
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="planType != null and planType != ''">
                and plan_type = #{planType}
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="templateId != null and templateId != ''">
                and template_id = #{templateId}
            </if>
            <if test="executionUserId != null and executionUserId != ''">
                and execution_user_id = #{executionUserId}
            </if>
            <if test="executionUserDepartmentId != null and executionUserDepartmentId != ''">
                <!--                and (select department_id = #{executionUserDepartmentId} from tb_user where id = execution_user_id)-->
                and is_user_at_department(execution_user_id, #{executionUserDepartmentId})
            </if>
            <if test="auditUserId != null and auditUserId != ''">
                and audit_user_id = #{auditUserId}
            </if>
            <if test="executionDays != null">
                and execution_days = #{executionDays}
            </if>
            <if test="intervalDays != null">
                and interval_days = #{intervalDays}
            </if>
            <if test="executionNum != null">
                and execution_num = #{executionNum}
            </if>
            <if test="stationIds != null and stationIds != ''">
                and station_ids = #{stationIds}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="startDateFrom != null">
                and start_date >= #{startDateFrom}
            </if>
            <if test="startDateTo != null">
                and start_date &lt;= #{startDateTo}
            </if>
            <if test="endDateFrom != null">
                and end_date >= #{endDateFrom}
            </if>
            <if test="endDateTo != null">
                and end_date &lt;= #{endDateTo}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>
</mapper>