import { request } from '@/plugins/axios';

/**
 * 查询gis操作日志
 * @param params
 * @returns
 */
export const GetGisOperateLogs = (params: any) => {
  return request({
    url: '/api/gis/optionLog/list',
    method: 'get',
    params
  });
};

/**
 * 添加gis操作日志
 * @param params
 * @returns
 */
export const PostGisOperateLog = (params: {
  type: string;
  content: string;
  optionType: string;
  optionName: string;
}) => {
  return request({
    url: '/api/gis/optionLog/save',
    method: 'post',
    data: params
  });
};
