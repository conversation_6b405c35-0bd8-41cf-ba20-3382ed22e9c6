package org.thingsboard.server.utils;

import org.thingsboard.server.common.data.EntityType;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;

import java.util.UUID;

/**
 * 时间戳UUID转换类
 */
public class TimeUUIDFormatUtil {

    //System.out.println(UUIDConverter.fromTimeUUID(UUID.fromString("981207a0-1e31-11e9-a509-75ecb9473a12")));

    /**
     * 字符串时间戳UUID格式化为对应的不带-的uuid字符串
     * 注：不是简单的将 “-” 去除
     *
     * @param uuid
     * @return
     */
    public String timeUUIDToString(String uuid) {
        return UUIDConverter.fromTimeUUID(UUID.fromString(uuid));
    }

    /**
     * UUID字符串转换为时间戳UUID
     *
     *
     * @param timeUUIDStr
     * @return
     */
    public UUID uuidStrToTimeUUID(String timeUUIDStr) {
        return new DeviceId(UUIDConverter.fromString(timeUUIDStr)).getId();
    }

}
