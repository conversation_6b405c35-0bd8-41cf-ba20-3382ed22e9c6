<template>
  <div class="app-menu-search">
    <el-input
      v-model="keyword"
      class="search-input"
      placeholder="请输入关键字"
      @change="handleKeyWordChange"
    >
      <template #suffix>
        <el-icon><SearchIcon></SearchIcon></el-icon>
      </template>
    </el-input>
    <div class="recent-item">
      最近访问
    </div>
    <div
      v-for="item in cachedRouters"
      :key="item.path"
      class="recent-item route"
      :class="[router.currentRoute.value.path === item.path ? 'is-active' : '']"
      @click="() => goto(item)"
    >
      {{ item.meta.title }}
    </div>
    <div
      class="close"
      @click="$emit('close')"
    >
      <Icon :icon="'ep:close'"></Icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Search as SearchIcon } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { useAppStore, useTagsStore } from '@/store'

const tagsStore = useTagsStore()
const emit = defineEmits(['change', 'close'])
const keyword = ref<string>('')
const router = useRouter()
const goto = (route: any) => {
  useAppStore().TOGGLE_menuShow(false)
  router.push({ ...route })
}
const cachedRouters = computed(() => {
  const length = tagsStore.cachedRouters.length
  if (length > 5) {
    return tagsStore.cachedRouters.slice(length - 6, length)
  }
  return tagsStore.cachedRouters
})
const handleKeyWordChange = value => {
  emit('change', value)
}
</script>
<style lang="scss" scoped>
.app-menu-search {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 15px 32px 30px 35px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
}
.search-input {
  width: 220px;
  margin-right: 40px;
}
.recent-item {
  color: var(--el-text-color-primary);
  margin-right: 40px;
  &.route {
    color: var(--el-text-color-secondary);
  }
  &.route {
    &:hover,
    &.is-active {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }
}
.close {
  width: 50px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  cursor: pointer;
  &:hover {
    color: var(--el-color-primary);
  }
}
</style>
