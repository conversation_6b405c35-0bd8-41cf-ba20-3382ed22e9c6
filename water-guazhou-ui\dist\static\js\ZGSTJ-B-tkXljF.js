import{_ as v}from"./index-C9hz-UZb.js";import{_ as g}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{d as x,c as r,r as f,o as b,ay as k,g as C,h as T,F as u,p as o,q as n,i as s,cs as w,j as S,C as B}from"./index-r0dFAfgr.js";import"./index-0NlGN6gS.js";import{u as D}from"./useDetector-BRcb7GRN.js";import{e as L}from"./statistics-CeyexT_5.js";const V={class:"card-header"},F={class:"left"},A=x({__name:"ZGSTJ",setup(G){const i=r(),l=f({chartOption:null}),m=f({defaultValue:{dateType:"day"},group:[{fields:[{type:"radio-button",field:"dateType",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"季度",value:"quarter"},{label:"年",value:"year"}],onChange:()=>c()}]}]}),c=()=>{var e;L({type:(e=i.value)==null?void 0:e.dataForm.dateType}).then(t=>{const a=t.data.data||{};l.chartOption={grid:{left:10,right:20,top:30,bottom:10,containLabel:!0},tooltip:{trigger:"axis"},xAxis:{type:"category",data:a.x||[],splitLine:{show:!1},axisTick:{show:!1}},yAxis:{name:"供水量(m³)",type:"value",splitLine:{lineStyle:{type:"dashed"}},axisTick:{show:!1},axisLine:{show:!1}},series:[{name:"供水量",data:a.y||[],type:"line",smooth:!0}]}}).catch(()=>{})},_=D(),p=r(),d=r();return b(()=>{c(),_.listenToMush(d.value,()=>{var e;(e=p.value)==null||e.resize()})}),(e,t)=>{const a=g,h=k("VChart"),y=v;return C(),T(y,{title:"总供水统计"},{title:u(()=>[o("div",V,[o("div",F,[n(s(w),{icon:"material-symbols:water-drop-outline"}),t[0]||(t[0]=o("span",null,"总供水统计",-1))]),n(a,{ref_key:"refTotalWaterForm",ref:i,style:{width:"auto"},config:s(m)},null,8,["config"])])]),default:u(()=>[o("div",{ref_key:"refDiv",ref:d,class:"chart-box"},[n(h,{ref_key:"refChart",ref:p,option:s(l).chartOption,theme:s(S)().isDark?"blackBackground":"whiteBackground"},null,8,["option","theme"])],512)]),_:1})}}}),N=B(A,[["__scopeId","data-v-eb0cbcd2"]]);export{N as default};
