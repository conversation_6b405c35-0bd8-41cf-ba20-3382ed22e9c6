<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.revenue.CustInfoMapper">
    <insert id="batchInsert">
        INSERT INTO revenue.tb_cust_info(id, org_id, meter_book_id, code, name, id_number, phone, address, email, population, water_category, industry_category, user_type, payment_method, pay_type, special_user_type, bill_type, transfer_user_flag, temporary_water_flag, temporary_water_end_date, liquidated_damages, low_consumption_water, contract_number, remark, status, create_time, tenant_id, water_stop_flag
        ) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.orgId},
            #{element.meterBookId},
            #{element.code},
            #{element.name},
            #{element.idNumber},
            #{element.phone},
            #{element.address},
            #{element.email},
            #{element.population},
            #{element.waterCategory},
            #{element.industryCategory},
            #{element.userType},
            #{element.paymentMethod},
            #{element.payType},
            #{element.specialUserType},
            #{element.billType},
            #{element.transferUserFlag},
            #{element.temporaryWaterFlag},
            #{element.temporaryWaterEndDate},
            #{element.liquidatedDamages},
            #{element.lowConsumptionWater},
            #{element.contractNumber},
            #{element.remark},
            #{element.status},
            #{element.createTime},
            #{element.tenantId},
            #{element.waterStopFlag}
            )
        </foreach>
    </insert>

    <select id="getByMap" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select a.*, b.name as orgName, c.name as meterBookName, d.name as waterCategoryName, e.name as industryCategoryName, f.name as userTypeName, g.name as paymentMethodName,
               h.name as payTypeName, i.name as specialUserTypeName, j.name as billTypeName, k.name as transferUserFlagName, l.name as temporaryWaterFlagName, m.name as statusName
        , balance.frozen_balance as frozenBalance, balance.balance
        from revenue.tb_cust_info a
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
        left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
        left join revenue.tb_sys_code_detail f on a.user_type = f.key and f.code = 'UserType'
        left join revenue.tb_sys_code_detail g on a.payment_method = g.key and g.code = 'PaymentMethod'
        left join revenue.tb_sys_code_detail h on a.pay_type = h.key and h.code = 'PayType'
        left join revenue.tb_special_user_type i on a.special_user_type = i.id
        left join revenue.tb_sys_code_detail j on a.bill_type = j.key and j.code = 'BillType'
        left join revenue.tb_sys_code_detail k on a.transfer_user_flag = k.key and k.code = 'TransferCode'
        left join revenue.tb_sys_code_detail l on a.temporary_water_flag = l.key and l.code = 'TemporaryWaterCode'
        left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'
        left join revenue.tb_balance balance on a.code = balance.cust_code and a.tenant_id = balance.tenant_id

        <where>
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>
            <if test="code != null and code != ''">
                and a.code = #{code}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and a.tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="findOneByCodeAndTenantId" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select a.*, b.name as orgName, c.name as meterBookName, d.name as waterCategoryName, e.name as industryCategoryName, f.name as userTypeName, g.name as paymentMethodName,
               h.name as payTypeName, i.name as specialUserTypeName, j.name as billTypeName, k.name as transferUserFlagName, l.name as temporaryWaterFlagName, m.name as statusName
        from revenue.tb_cust_info a
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
        left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
        left join revenue.tb_sys_code_detail f on a.user_type = f.key and f.code = 'UserType'
        left join revenue.tb_sys_code_detail g on a.payment_method = g.key and g.code = 'PaymentMethod'
        left join revenue.tb_sys_code_detail h on a.pay_type = h.key and h.code = 'PayType'
        left join revenue.tb_special_user_type i on a.special_user_type = i.id
        left join revenue.tb_sys_code_detail j on a.bill_type = j.key and j.code = 'BillType'
        left join revenue.tb_sys_code_detail k on a.transfer_user_flag = k.key and k.code = 'TransferCode'
        left join revenue.tb_sys_code_detail l on a.temporary_water_flag = l.key and l.code = 'TemporaryWaterCode'
        left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'

        where a.code = #{code} and a.tenant_id = #{tenantId}
    </select>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select distinct a.*, b.name as orgName, c.name as meterBookName, d.name as waterCategoryName, e.name as industryCategoryName, f.name as userTypeName, g.name as paymentMethodName,
               h.name as payTypeName, i.name as specialUserTypeName, j.name as billTypeName, k.name as transferUserFlagName, l.name as temporaryWaterFlagName, m.name as statusName,
               '管理员' as copyMeterUser, balance.frozen_balance as frozenBalance, balance.balance
        from revenue.tb_cust_info a
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
        left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
        left join revenue.tb_sys_code_detail f on a.user_type = f.key and f.code = 'UserType'
        left join revenue.tb_sys_code_detail g on a.payment_method = g.key and g.code = 'PaymentMethod'
        left join revenue.tb_sys_code_detail h on a.pay_type = h.key and h.code = 'PayType'
        left join revenue.tb_special_user_type i on a.special_user_type = i.id
        left join revenue.tb_sys_code_detail j on a.bill_type = j.key and j.code = 'BillType'
        left join revenue.tb_sys_code_detail k on a.transfer_user_flag = k.key and k.code = 'TransferCode'
        left join revenue.tb_sys_code_detail l on a.temporary_water_flag = l.key and l.code = 'TemporaryWaterCode'
        left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'
        left join revenue.tb_cust_metering_point_info point on point.code = a.code
        left join revenue.tb_water_meter meter on meter.code = point.point_code
        left join revenue.tb_sys_code_detail caliber on meter.caliber = caliber.key and caliber.code = 'MeterCaliber'
        left join revenue.tb_price price on point.price_code = price.code
        left join revenue.tb_balance balance on a.code = balance.cust_code and balance.tenant_id = a.tenant_id

        left join (select info.code as code, count(*) as meter_num from revenue.tb_water_meter meter left join revenue.tb_cust_metering_point_info point
            on meter.code = point.point_code left join revenue.tb_cust_info info on point.code = info.code group by info.code) countMeter on countMeter.code = a.code
        left join (select edit_records.cust_code as cust_code, edit_records.create_time as cancel_time from revenue.tb_info_edit_records edit_records left join revenue.tb_cust_info_temp info_temp on edit_records.cust_code = info_temp.code where
            edit_records.type = '6'  and info_temp.status = '5' order by edit_records.create_time desc offset 0 limit 1) cancel on cancel.cust_code = a.code
        -- todo 违约金 left join revenue.tb_sys_code_detail l on a.temporary_water_flag = l.id
        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.orgIdList != null and param.orgIdList.size() > 0">
                and a.org_id in
                <foreach collection="param.orgIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.meterBookIds != null and param.meterBookIds != ''">
                and a.meter_book_id in
                <foreach collection="param.meterBookIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.code != null and param.code != ''">
                and a.code like '%'||#{param.code}||'%'
            </if>
            <if test="param.name != null and param.name != ''">
                and a.name like '%'||#{param.name}||'%'
            </if>
            <if test="param.userType != null and param.userType != ''">
                and a.user_type like '%'||#{param.userType}||'%'
            </if>
            <if test="param.liquidatedDamages != null and param.liquidatedDamages != ''">
                and a.liquidated_damages = #{param.liquidatedDamages}
            </if>
            <if test='param.isLiquidatedDamages != null and param.isLiquidatedDamages == "1"'>
                and a.liquidated_damages is null
            </if>
            <if test='param.isLiquidatedDamages != null and param.isLiquidatedDamages == "2"'>
                and a.liquidated_damages is not null
            </if>
            <if test="param.paymentMethod != null and param.paymentMethod != ''">
                and a.payment_method like '%'||#{param.paymentMethod}||'%'
            </if>
            <if test="param.phone != null and param.phone != ''">
                and a.phone like '%'||#{param.phone}||'%'
            </if>
            <if test="param.address != null and param.address != ''">
                and a.address like '%'||#{param.address}||'%'
            </if>
            <if test="param.payType != null and param.payType != ''">
                and a.pay_type like '%'||#{param.payType}||'%'
            </if>
            <if test="param.remark != null and param.remark != ''">
                and a.remark like '%'||#{param.remark}||'%'
            </if>
            <if test="param.waterStopFlag != null and param.waterStopFlag != ''">
                and a.water_stop_flag like '%'||#{param.waterStopFlag}||'%'
            </if>
            <if test="param.industryCategory != null and param.industryCategory != ''">
                and a.industry_category like '%'||#{param.industryCategory}||'%'
            </if>
            <if test="param.brand != null and param.brand != ''">
                and meter.brand like '%'||#{param.brand}||'%'
            </if>
            <if test="param.type != null and param.type != ''">
                and meter.type like '%'||#{param.type}||'%'
            </if>
            <if test="param.steelSealNumber != null and param.steelSealNumber != ''">
                and meter.steel_seal_number like '%'||#{param.steelSealNumber}||'%'
            </if>
            <if test="param.caliber != null and param.caliber != ''">
                and meter.caliber like '%'||#{param.caliber}||'%'
            </if>
            <if test="param.meterWellCode != null and param.meterWellCode != ''">
                and meter.meter_well_code like '%'||#{param.meterWellCode}||'%'
            </if>
            <if test="param.billType != null and param.billType != ''">
                and a.bill_type like '%'||#{param.billType}||'%'
            </if>
            <!-- todo 混合用水 -->
            <if test="param.mixUseWater != null and param.mixUseWater != ''">

            </if>
            <if test="param.waterCategory != null and param.waterCategory != ''">
                and a.water_category = #{param.waterCategory}
            </if>
            <if test="param.priceCode != null and param.priceCode != ''">
                and point.price_code = #{param.priceCode}
            </if>
            <if test="param.isMoreMeter != null and param.isMoreMeter != ''">
                <choose>
                    <when test="param.isMoreMeter == '1'.toString()">
                        and countMeter.meter_num &gt; 1
                    </when>
                    <when test="param.isMoreMeter == '0'.toString()">
                        and (countMeter.meter_num &lt; 2 or countMeter.meter_num is null)
                    </when>

                </choose>
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                and a.create_time &gt;= to_timestamp(#{param.createTimeStart} / 1000)
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                and a.create_time &lt;= to_timestamp(#{param.createTimeEnd} / 1000)
            </if>
            <if test="param.changeMeterTimeStart != null and param.changeMeterTimeStart != ''">
                and meter.create_time &gt;= to_timestamp(#{param.changeMeterTimeStart} / 1000)
            </if>
            <if test="param.changeMeterTimeEnd != null and param.changeMeterTimeEnd != ''">
                and meter.create_time &lt;= to_timestamp(#{param.changeMeterTimeEnd} / 1000)
            </if>
            <if test="param.cancelUserTimeStart != null and param.cancelUserTimeStart != ''">
                and a.status = '5' and cancel.cancel_time &gt;= to_timestamp(#{param.cancelUserTimeStart} / 1000)
            </if>
            <if test="param.cancelUserTimeEnd != null and param.cancelUserTimeEnd != ''">
                and a.status = '5' and cancel.cancel_time &lt;= to_timestamp(#{param.changeMeterTimeEnd} / 1000)
            </if>
            <if test="param.status != null and param.status != ''">
                and a.status in 
                <foreach collection="param.status.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.create_time desc, a.code
    </select>

    <select id="getInfoList" resultType="org.thingsboard.server.dao.model.DTO.CustMeterInfoDTO">
        select z.id, a.code, a.name, a.phone, a.address, b.name as orgName, c.name as meterBookName, d.name as waterCategoryName, e.name as industryCategoryName, g.name as paymentMethodName,
        m.name as statusName, price.name as priceType, n.code as priceCode, n.name as priceName,z.meter_code as meterCode, y.meter_copy_order_number as meterCopyOrderNumber,o.name as caliberName, z.current_read as currentRead,
        z.address as installAddress, z.steel_seal_number as steelSealNumber, z.meter_well_code as wellCode, balance.balance, balance.frozen_balance as frozenBalance,
        brand.name as brandName, type.name as typeName
        from revenue.tb_cust_info a
        left join revenue.tb_cust_metering_point_info y on a.code = y.code and a.tenant_id = y.tenant_id
        left join revenue.tb_water_meter z on z.code = y.point_code and z.tenant_id = y.tenant_id
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
        left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
        left join revenue.tb_sys_code_detail g on a.payment_method = g.key and g.code = 'PaymentMethod'
        left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'
        left join revenue.tb_price n on y.price_code = n.code and y.tenant_id = n.tenant_id
        left join revenue.tb_sys_code_detail o on z.caliber = o.key and z.code = 'MeterCaliber'
        left join revenue.tb_sys_code_detail price on n.type = price.key and price.code = 'WaterCategoryType'
        left join revenue.tb_balance balance on balance.cust_code = a.code and  balance.tenant_id = a.tenant_id
        left join revenue.tb_sys_code_detail brand on z.brand = brand.key and brand.code = 'MeterBrand'
        left join revenue.tb_sys_code_detail type on z.type = type.key and type.code = 'MeterType'
        <where>
            z.id is not null and a.tenant_id = #{tenantId}
            <if test="orgIdList != null and orgIdList.size() > 0">
                and a.org_id in
                <foreach collection="orgIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="meterBookIds != null and meterBookIds != ''">
                and a.meter_book_id in
                <foreach collection="meterBookIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="code != null">
                and a.code like '%'||#{code}||'%'
            </if>

            <if test="name != null">
                and a.name like '%'||#{name}||'%'
            </if>

            <if test="address != null">
                and a.address like '%'||#{address}||'%'
            </if>
            <if test="status != null and status != ''">
                and a.status in
                <foreach collection="status.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.create_time desc
        limit #{size} offset (#{page} - 1) * #{size}
    </select>

    <select id="getInfoListCount" resultType="int">
        select count(*)
        from revenue.tb_cust_info a
        left join revenue.tb_cust_metering_point_info y on a.code = y.code and a.tenant_id = y.tenant_id
        left join revenue.tb_water_meter z on z.code = y.point_code and z.tenant_id = y.tenant_id
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
        left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
        left join revenue.tb_sys_code_detail g on a.payment_method = g.key and g.code = 'PaymentMethod'
        left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'
        left join revenue.tb_price n on y.price_code = n.code and y.tenant_id = n.tenant_id
        left join revenue.tb_sys_code_detail o on z.caliber = o.key and z.code = 'MeterCaliber'
        <!-- todo 用户余额和冻结金额 -->
        <where>
            z.id is not null
            <if test="orgIdList != null and orgIdList.size() > 0">
                and a.org_id in
                <foreach collection="orgIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="meterBookIds != null and meterBookIds != ''">
                and a.meter_book_id in
                <foreach collection="meterBookIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="code != null">
                and a.code like '%'||#{code}||'%'
            </if>

            <if test="name != null">
                and a.name like '%'||#{name}||'%'
            </if>

            <if test="address != null">
                and a.address like '%'||#{address}||'%'
            </if>
            <if test="status != null and status != ''">
                and a.status in
                <foreach collection="status.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCredentialsList" resultType="org.thingsboard.server.dao.model.DTO.CustCredentialsDTO">
        select a.id, a.code, a.name as custName, a.address, b.id as orgId, b.name as orgName, c.id as meterBookId, c.name as meterBookName, f.name as waterCategoryName, d.id as credentialsId, d.type, e.name as typeName,
            d.name, d.number
        from revenue.tb_cust_info a
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_cust_credentials d on d.code = a.code
        left join revenue.tb_sys_code_detail e on d.type = e.key and e.code = 'CertTypeCode'
        left join revenue.tb_sys_code_detail f on a.water_category = f.key and f.code = 'WaterCategoryType'
        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.orgIdList != null and param.orgIdList.size() > 0">
                and a.org_id in
                <foreach collection="param.orgIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.meterBookIds != null and param.meterBookIds != ''">
                and a.meter_book_id in
                <foreach collection="param.meterBookIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.code != null">
                and a.code like '%'||#{param.code}||'%'
            </if>

            <if test="param.name != null">
                and a.name like '%'||#{param.name}||'%'
            </if>

            <if test="param.address != null">
                and a.address like '%'||#{param.address}||'%'
            </if>
            <if test="param.status != null and param.status != ''">
                and a.status in
                <foreach collection="param.status.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.create_time desc, d.create_time desc
    </select>

    <select id="getBatchInstallList" resultType="org.thingsboard.server.dao.model.DTO.CustExpandingDTO">
        select distinct a.create_time, a.org_id, b.name as orgName, c.code as meterBookCode, c.name as meterBookName, a.code as custCode, a.name as custName, a.address, a.population, d.name as waterCategoryName, e.name as industryCategoryName, f.name as userTypeName,
               a.contract_number as contractNumber, point.meter_copy_order_number as meterCopyOrderNumber, meter.steel_seal_number as steelSealNumber, meter.meter_code as meterCode, brand.name as brandName,
               type.name as typeName, meter.read_bit as readBit, meter.current_read as currentRead, caliber.name as caliberName, bank_type.name as bankName, bank.number as bankNumber, addtional_price.name as additionalPriceName,
               a.phone as phone, a.id_number as idNumber, a.liquidated_damages as liquidatedDamagesName, case when point.is_ladder = '1' then '是' when point.is_ladder = '0' then '否' else '' end as isLadder,
               meter.remote_meter_address as remoteMeterAddress, meter.address as installAddress, balance.balance, balance.frozen_balance as frozenBalance, position.name as installPositionName, well.name as meterWell, point.price_code as priceCode, price.name as priceName,
               meter.acceptance_lead_seal_number as acceptanceLeadSealNumber, meter.install_lead_seal_number as installLeadSealNumber, meter.measure_lead_seal_number as measureLeadSealNumber, a.tenant_id as tenantId
        from revenue.tb_expanding_c expanding_c
             left join revenue.tb_cust_info a on expanding_c.instance_code = a.code
             left join tb_organization b on a.org_id = b.id
             left join revenue.tb_meter_book c on a.meter_book_id = c.id
             left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
             left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
             left join revenue.tb_sys_code_detail f on a.user_type = f.key and f.code = 'UserType'
             left join revenue.tb_cust_metering_point_info point on point.code = a.code and a.tenant_id = point.tenant_id
             left join revenue.tb_water_meter meter on meter.code = point.point_code and point.tenant_id = meter.tenant_id
             left join revenue.tb_sys_code_detail brand on meter.brand = brand.key and brand.code = 'MeterBrand'
             left join revenue.tb_sys_code_detail type on meter.type = type.key and type.code = 'MeterType'
             left join revenue.tb_sys_code_detail caliber on meter.caliber = caliber.key and caliber.code = 'MeterCaliber'
             left join revenue.tb_sys_code_detail position on meter.install_position = position.key and position.code = 'MeterPositionType'
             left join revenue.tb_cust_bank_info bank on bank.code = a.code and bank.tenant_id = a.tenant_id
             left join revenue.tb_sys_code_detail bank_type on bank.type = bank_type.key and bank_type.code = 'BankType'
             left join revenue.tb_cust_additional_price cust_addtional_price on cust_addtional_price.code = a.code and cust_addtional_price.tenant_id = a.tenant_id
             left join revenue.tb_additional_price addtional_price on addtional_price.code = cust_addtional_price.additional_price_code and addtional_price.tenant_id = a.tenant_id
             left join revenue.tb_meter_well well on meter.meter_well_code = well.code and meter.tenant_id = well.tenant_id
             left join revenue.tb_price price on point.price_code = price.code and price.tenant_id = a.tenant_id
             left join revenue.tb_balance balance on balance.cust_code = a.code and  balance.tenant_id = a.tenant_id

        where expanding_c.expanding_code = #{code} order by a.create_time
    </select>
    <select id="getMaxCode" resultType="java.lang.Integer">
        SELECT count(*) FROM revenue.tb_cust_info where code like #{prefix}||'%' and tenant_id = #{tenantId}
    </select>
    <select id="selectByCode" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select * from revenue.tb_cust_info where code = #{custCode} and tenant_id = #{tenantId}
    </select>

    <select id="findByMeterBookCode" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select tci.*
        from tb_cust_info tci
        left join tb_meter_book tmb on tmb.id = tci.meter_book_id
        where tmb.code = #{meterBookCode} and tci.tenant_id = #{tenantId}
    </select>
    <select id="getCustCodeByMeterIdList" resultType="java.util.Map">
        select c.code as "custCode", a.id as "meterId", a.meter_code as "meterCode", c.org_id as "orgId", b.id as "pointId", b.meter_copy_order_number as "meterCopyOrderNumber"
        from revenue.tb_water_meter a
            left join revenue.tb_cust_metering_point_info b on a.code = b.point_code and a.tenant_id = b.tenant_id
            left join revenue.tb_cust_info c on b.code = c.code and b.tenant_id = c.tenant_id
        <where> a.tenant_id = #{tenantId}
            <if test="ids != null and ids.size() > 0">
                and a.id in
                <foreach collection="ids" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
           <if test="codes != null and codes.size() > 0">
               and a.meter_code in
               <foreach collection="codes" open="(" item="item" separator="," close=")">
                   #{item}
               </foreach>
           </if>
        </where>
    </select>
    <select id="getBalance" resultType="org.thingsboard.server.dao.model.DTO.CustInfoBalanceDTO">
        select a.id, a.code as custCode, a.name as custName, balance.balance, balance.balance as amount, b.id as orgId, b.name as orgName, c.code as meterBookCode, c.id as meterBookId, c.name as meterBookName
        from revenue.tb_cust_info a
             left join tb_organization b on a.org_id = b.id
             left join revenue.tb_meter_book c on a.meter_book_id = c.id
             left join revenue.tb_balance balance on balance.cust_code = a.code and  balance.tenant_id = a.tenant_id
        <where>
            <if test="param.orgIdList != null and param.orgIdList.size() > 0">
                and a.org_id in
                <foreach collection="param.orgIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.meterBookIds != null and param.meterBookIds != ''">
                and a.meter_book_id in
                <foreach collection="param.meterBookIds.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.code != null">
                and a.code like '%'||#{param.code}||'%'
            </if>

            <if test="param.name != null">
                and a.name like '%'||#{param.name}||'%'
            </if>
            <if test="param.refund != null and param.refund = '1'.toString()">
                and balance.balance &gt; 0
            </if>
        </where>
        order by a.create_time desc
    </select>
    <select id="getPriceList" resultType="org.thingsboard.server.dao.model.DTO.CustMeterInfoDTO">
        select z.id, a.code, a.name, a.phone, a.address, b.name as orgName, c.name as meterBookName, d.name as waterCategoryName, e.name as industryCategoryName, g.name as paymentMethodName,
        m.name as statusName, price.name as priceType, n.code as priceCode, n.name as priceName,z.meter_code as meterCode, y.meter_copy_order_number as meterCopyOrderNumber,o.name as caliberName, z.current_read as currentRead,
        z.address as installAddress, z.steel_seal_number as steelSealNumber, z.meter_well_code as wellCode, balance.balance, balance.frozen_balance as frozenBalance
        from revenue.tb_cust_info a
        left join revenue.tb_cust_metering_point_info y on a.code = y.code and a.tenant_id = y.tenant_id
        left join revenue.tb_water_meter z on z.code = y.point_code and z.tenant_id = y.tenant_id
        left join tb_organization b on a.org_id = b.id
        left join revenue.tb_meter_book c on a.meter_book_id = c.id
        left join revenue.tb_sys_code_detail d on a.water_category = d.key and d.code = 'WaterCategoryType'
        left join revenue.tb_sys_code_detail e on a.industry_category = e.key and e.code = 'IndustryCategoryType'
        left join revenue.tb_sys_code_detail g on a.payment_method = g.key and g.code = 'PaymentMethod'
        left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'
        left join revenue.tb_price n on y.price_code = n.code and y.tenant_id = n.tenant_id
        left join revenue.tb_sys_code_detail o on z.caliber = o.key and z.code = 'MeterCaliber'
        left join revenue.tb_sys_code_detail price on n.type = price.key and price.code = 'WaterCategoryType'
        left join revenue.tb_balance balance on balance.cust_code = a.code and  balance.tenant_id = a.tenant_id
        <where>
            z.id is not null and a.tenant_id = #{tenantId} and a.code like '%'||#{custCode}||'%'
        </where>
        order by a.create_time desc
    </select>

    <select id="groupByMeterBook" resultType="org.thingsboard.server.dao.model.DTO.CountObjDTO">
        SELECT meter_book_id AS "key", count(*) AS "count" FROM "tb_cust_info" WHERE tenant_id = #{tenantId} GROUP BY meter_book_id
    </select>

    <select id="groupByMeterBookCode" resultType="org.thingsboard.server.dao.model.DTO.CountObjDTO">
        SELECT
            DATA."meterBookCode" AS "key",
            COUNT ( * ) AS "count"
        FROM
            (
                SELECT
                    tci.meter_book_id,
                    tmb.code AS "meterBookCode",
                    tci.code
                FROM
                    tb_cust_info tci
                        LEFT JOIN tb_meter_book tmb ON tci.meter_book_id = tmb.ID
                WHERE tci.tenant_id = #{param.tenantId}
            ) DATA
        GROUP BY
            DATA."meterBookCode"
    </select>

    <select id="countUseLiquidatedDamagesById" resultType="java.lang.Integer">
        SELECT count(*) AS "count" FROM "tb_cust_info" WHERE liquidated_damages = #{liquidatedDamages}
    </select>

    <select id="sumWaterType" resultType="java.util.Map">
        select detail.name, count(*) as total
        from revenue.tb_cust_info cust
        left join revenue.tb_sys_code_detail detail on detail.code = 'WaterCategoryType' and detail.key = cust.water_category
        <where>
            <if test="custCodeList != null and custCodeList.size() > 0">
                and cust.code in
                <foreach collection="custCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and cust.tenant_id = #{tenantId}
        </where>
        group by detail.name
    </select>

    <select id="getExtendInfoByCustIdList" resultType="com.alibaba.fastjson.JSONObject">
        select a.id, a.code, a.name, b.name as "orgName", c.name as "meterBookName", invoice.phone,
               m.name as "statusName", a.population, bank.name as "bankCustName", bank.number as "bankNumber", bank_type.name as "bankName",
               invoice.name as "invoiceName", invoice.tax_number as "taxNumber", invoice.bank_name as "invoiceBankName", invoice.bank_number as "invoiceBankNumber", invoice.register_address as "registerAddress"
        from revenue.tb_cust_info a
                 left join tb_organization b on a.org_id = b.id
                 left join revenue.tb_meter_book c on a.meter_book_id = c.id
                 left join revenue.tb_sys_code_detail m on a.status = m.key and m.code = 'UserStatusType'
                 left join revenue.tb_cust_bank_info bank on bank.code = a.code and bank.tenant_id = a.tenant_id
                 left join revenue.tb_cust_invoice_info invoice on invoice.code = a.code and invoice.tenant_id = a.tenant_id
                 left join revenue.tb_sys_code_detail bank_type on bank.type = bank_type.key and bank_type.code = 'BankType'
        where a.id in 
        <foreach collection="custIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="getListByPointCodeList" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select a.*
        from revenue.tb_cust_info a
        left join revenue.tb_cust_metering_point_info b on a.code = b.code and a.tenant_id = b.tenant_id
        
        where b.point_code in 
        <foreach collection="pointCodeList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and a.tenant_id = #{tenantId}
    </select>

    <select id="selectByCodeList" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select a.*
        from revenue.tb_cust_info a

        where a.code in
        <foreach collection="custCodeList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and a.tenant_id = #{tenantId}
    </select>

    <select id="findUserListByPlan" resultType="org.thingsboard.server.dao.model.sql.revenue.CustInfo">
        select a.*
        from revenue.tb_cust_info a left join revenue.tb_plan_users b on a.code = b.cust_code
        where b.plan_code = #{planCode}
        and a.tenant_id = #{tenantId}
        <if test="keyword != null and keyword != ''">
            and (a.code like '%'||#{keyword}||'%' or a.name like '%'||#{keyword}||'%' or a.address like '%'||#{keyword}||'%')
        </if>
    </select>
    <select id="getCustNum" resultType="java.lang.Integer">
        select count(code) from revenue.tb_cust_info where tenant_id = #{tenantId}
    </select>
    <select id="findOpenIdListByMeterBookId" resultType="java.lang.String">
        select a.openid,b.name
        from revenue.tb_wechat_bind a
         left join revenue.tb_cust_info b on a.cust_code = b.code
        where b.tenant_id = #{tenantId} b.meter_book_id in
        <foreach collection="meterBookIds.split(',')" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="findOpenIdListByDmaId" resultType="java.lang.String">
        select a.openid,b.name
        from revenue.tb_wechat_bind a
        left join tb_pipe_partition_cust b on a.cust_code = b.cust_code
        where b.tenant_id = #{tenantId} b.partition_id in
        <foreach collection="dmaIds.split(',')" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

</mapper>