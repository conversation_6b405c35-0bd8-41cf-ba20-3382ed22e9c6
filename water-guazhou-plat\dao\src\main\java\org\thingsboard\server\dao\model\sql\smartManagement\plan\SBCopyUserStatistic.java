package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SBCopyUserStatistic {
    // 抄表人员数量
    private Integer userCount;

    // 用户数量
    private Integer customerCount;

    public SBCopyUserStatistic() {

    }

    public SBCopyUserStatistic(Integer userCount, Integer customerCount) {
        this.userCount = userCount;
        this.customerCount = customerCount;
    }

}
