package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 水质化验-基础配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSAY_BASE_SETTING_TABLE)
@TableName(ModelConstants.ASSAY_BASE_SETTING_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayBaseSetting {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSAY_BASE_SETTING_NAME)
    private String name;

    @Column(name = ModelConstants.ASSAY_BASE_SETTING_TYPE)
    private String type;

    @Column(name = ModelConstants.ASSAY_BASE_SETTING_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.ASSAY_BASE_SETTING_REMARK)
    private String remark;

    @Column(name = ModelConstants.ASSAY_BASE_SETTING_CREATE_USER)
    private String createUser;

    @TableField(exist = false)
    private String createUserName;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.UPDATE_TIME)
    private Date updateTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
