import{d as Y,dI as w,r as i,c as y,bF as o,a8 as M,bX as N,s as O,o as E,ah as B,g as P,n as S,bo as F,i as _,p as H,q as k,br as V,bD as W,C as R}from"./index-r0dFAfgr.js";import{_ as z}from"./CardTable-rdWOL4_6.js";import{_ as A}from"./CardSearch-CB_HNR-Q.js";import{r as j}from"./data-D3PIONJl.js";import{b as J}from"./statisticalAnalysis-BoRmiv4A.js";import{u as Q}from"./useStation-DJgnSZIA.js";import{p as X}from"./printUtils-C-AxhDcd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const G={class:"wrapper"},K={class:"main"},U={class:"right"},Z=Y({__name:"index",setup($){const h=new w,{getStationTree:q}=Q(),s=i({queryType:"day",treeDataType:"Station",stationId:"",title:"",sums:{},min:{},max:{},average:{},calculation:[],columns:[]}),C=y(!1),g=o().date(),b=y(),d=y(),n=i({data:[],currentProject:{}}),p=i({defaultParams:{type:"day",year:[o().format(),o().format()],month:[o().format(),o().format()],day:[o().date(g-6).format("YYYY-MM-DD"),o().date(g).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:M(()=>n.data),label:"站点选择",onChange:e=>{const t=N(n.data,"children","id",e);n.currentProject=t,s.treeDataType=t.data.type,s.treeDataType==="Station"&&(s.stationId=t.id,u())}},{type:"radio-button",field:"type",options:j,label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>u(),icon:"iconfont icon-chaxun"},{text:"导出",perm:!0,type:"warning",icon:"iconfont icon-xiazai",click:()=>I()},{perm:!0,text:"打印",type:"success",svgIcon:O(W),click:()=>L()}]}]}),r=i({loading:!1,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),u=()=>{var x;r.loading=!0;const e=((x=d.value)==null?void 0:x.queryParams)||{},t=j.find(m=>m.value===e.type),a=e[t.value];console.log(t),s.title=n.currentProject.label+"水量报表("+t.label+o(a[0]).format(t.data)+"至"+o(a[1]).format(t.data)+")",r.title=s.title;const f={stationId:n.currentProject.id,start:o(a[0]).startOf(e.type).valueOf(),end:o(a[1]).endOf(e.type).valueOf(),queryType:e.type};J(f).then(m=>{var D,T;const c=(D=m.data)==null?void 0:D.data;s.columns=(T=c==null?void 0:c.tableInfo)==null?void 0:T.map(l=>({prop:l.columnValue,label:l.columnName,minwidth:130,unit:l.unit?"("+l.unit+")":""})),r.columns=s.columns,r.dataList=c==null?void 0:c.tableDataList,r.loading=!1,console.log(s.columns.length)})},I=()=>{h.addElTable(b.value),h.export()},L=()=>{X({title:r.title,titleList:r.columns,data:r.dataList})};return E(async()=>{var e;n.data=await q("泵站"),n.currentProject=B(n.data),p.defaultParams={...p.defaultParams,treeData:n.currentProject},(e=d.value)==null||e.resetForm(),u()}),(e,t)=>{const a=A,v=z,f=V;return P(),S("div",G,[F((P(),S("div",K,[H("div",U,[k(a,{ref_key:"cardSearch",ref:d,config:_(p)},null,8,["config"]),k(v,{id:"print",ref_key:"refTable",ref:b,class:"card-table",config:_(r)},null,8,["config"])])])),[[f,_(C)]])])}}}),de=R(Z,[["__scopeId","data-v-dc8c95d8"]]);export{de as default};
