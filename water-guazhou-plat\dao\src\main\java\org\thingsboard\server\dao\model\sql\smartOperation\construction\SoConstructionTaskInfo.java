package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;

import java.util.Date;

@Getter
@Setter
public class SoConstructionTaskInfo {
    // id
    private String id;

    // 作用域
    private SoGeneralSystemScope scope;

    // 所属工程编号
    private String constructionCode;

    // 当前状态
    private SoGeneralTaskStatus status;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 完成时间
    private Date completeTime;

    // 客户id
    private String tenantId;

}
