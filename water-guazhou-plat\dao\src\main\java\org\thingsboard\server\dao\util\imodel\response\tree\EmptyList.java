package org.thingsboard.server.dao.util.imodel.response.tree;

import org.jetbrains.annotations.NotNull;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

@Deprecated
public class EmptyList<T> implements List<T> {
    @SuppressWarnings("rawtypes")
    public final static EmptyList EMPTY_LIST = new EmptyList();

    @SuppressWarnings("rawtypes")
    private final static Iterator EMPTY_ITERATOR = new Iterator() {
        @Override
        public boolean hasNext() {
            return false;
        }

        @Override
        public Object next() {
            return null;
        }
    };

    private EmptyList() {

    }

    @Override
    public int size() {
        return 0;
    }

    @Override
    public boolean isEmpty() {
        return true;
    }

    @Override
    public boolean contains(Object o) {
        return false;
    }

    @NotNull
    @Override
    @SuppressWarnings("unchecked")
    public Iterator<T> iterator() {
        return EMPTY_ITERATOR;
    }

    @NotNull
    @Override
    public Object[] toArray() {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation toArray() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @NotNull
    @Override
    public <T1> T1[] toArray(@NotNull T1[] a) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation toArray() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean add(T t) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation add() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean remove(Object o) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation remove() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean containsAll(@NotNull Collection<?> c) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation containsAll() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean addAll(@NotNull Collection<? extends T> c) {

        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation addAll() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean addAll(int index, @NotNull Collection<? extends T> c) {

        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation addAll() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean removeAll(@NotNull Collection<?> c) {

        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation removeAll() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean retainAll(@NotNull Collection<?> c) {

        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation retainAll() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public void clear() {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation clear() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public boolean equals(Object o) {
        return o == this;
    }

    @Override
    public int hashCode() {
        return 0;
    }

    @Override
    public T get(int index) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation get() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public T set(int index, T element) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation set() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public void add(int index, T element) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation add() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public T remove(int index) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation remove() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @Override
    public int indexOf(Object o) {
        return -1;
    }

    @Override
    public int lastIndexOf(Object o) {
        return -1;
    }

    @NotNull
    @Override
    public ListIterator<T> listIterator() {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation listIterator() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @NotNull
    @Override
    public ListIterator<T> listIterator(int index) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation listIterator() of empty list", ThingsboardErrorCode.GENERAL));
    }

    @NotNull
    @Override
    public List<T> subList(int fromIndex, int toIndex) {
        throw new UndeclaredThrowableException(new ThingsboardException("not allowed operation subList() of empty list", ThingsboardErrorCode.GENERAL));
    }
}
