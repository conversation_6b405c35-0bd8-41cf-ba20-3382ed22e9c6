"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8636],{98636:(e,r,t)=>{t.r(r),t.d(r,{default:()=>x});var i=t(43697),a=t(3172),s=t(20102),l=t(3920),n=t(68668),o=t(70586),p=t(16453),c=t(95330),u=t(17452),h=t(5600),d=t(90578),y=(t(67676),t(71715)),_=t(52011),v=t(30556),f=t(82971),m=t(87085),g=t(54295),b=t(46486),S=t(7944),C=t(17287),T=t(71612),w=t(17017),O=t(38009),R=t(16859),L=t(34760),M=t(72965),k=t(10343),D=t(66677),j=t(21506),B=t(90082),I=t(49867);const U=["Canvas/World_Dark_Gray_Base","Canvas/World_Dark_Gray_Reference","Canvas/World_Light_Gray_Base","Canvas/World_Light_Gray_Reference","Elevation/World_Hillshade","Elevation/World_Hillshade_Dark","Ocean/World_Ocean_Base","Ocean/World_Ocean_Reference","Ocean_Basemap","Reference/World_Boundaries_and_Places","Reference/World_Boundaries_and_Places_Alternate","Reference/World_Transportation","World_Imagery","World_Street_Map","World_Topo_Map"];let W=class extends((0,T.h)((0,k.x)((0,M.M)((0,O.q)((0,R.I)((0,b.Z)((0,S.O)((0,C.Y)((0,p.R)((0,l.p)((0,L.Q)((0,g.V)((0,w.N)(m.Z)))))))))))))){constructor(...e){super(...e),this.listMode="show",this.isReference=null,this.operationalLayerType="ArcGISTiledMapServiceLayer",this.resampling=!0,this.sourceJSON=null,this.spatialReference=null,this.path=null,this.sublayers=null,this.type="tile",this.url=null}normalizeCtorArgs(e,r){return"string"==typeof e?{url:e,...r}:e}load(e){const r=(0,o.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Map Service"]},e).catch(c.r9).then((()=>this._fetchService(r)))),Promise.resolve(this)}get attributionDataUrl(){const e=this.parsedUrl?.path.toLowerCase();return e?this._getDefaultAttribution(this._getMapName(e)):null}readSpatialReference(e,r){return(e=e||r.tileInfo&&r.tileInfo.spatialReference)&&f.Z.fromJSON(e)}writeSublayers(e,r,t,i){if(!this.loaded||!e)return;const a=e.slice().reverse().flatten((({sublayers:e})=>e&&e.toArray().reverse())).toArray(),s=[],l={writeSublayerStructure:!1,...i};a.forEach((e=>{const r=e.write({},l);s.push(r)})),s.some((e=>Object.keys(e).length>1))&&(r.layers=s)}get tileServers(){return this._getDefaultTileServers(this.parsedUrl?.path)}castTileServers(e){return Array.isArray(e)?e.map((e=>(0,u.mN)(e).path)):null}fetchTile(e,r,t,i={}){const{signal:s}=i,l=this.getTileUrl(e,r,t),n={responseType:"image",signal:s,query:{...this.refreshParameters}};return(0,a.default)(l,n).then((e=>e.data))}async fetchImageBitmapTile(e,r,t,i={}){const{signal:s}=i,l=this.getTileUrl(e,r,t),n={responseType:"blob",signal:s,query:{...this.refreshParameters}},{data:o}=await(0,a.default)(l,n);return(0,B.g)(o,l)}getTileUrl(e,r,t){const i=!this.tilemapCache&&this.supportsBlankTile,a=(0,u.B7)({...this.parsedUrl?.query,blankTile:!i&&null,...this.customParameters,token:this.apiKey}),s=this.tileServers;return`${s&&s.length?s[r%s.length]:this.parsedUrl?.path}/tile/${e}/${r}/${t}${a?"?"+a:""}`}loadAll(){return(0,n.G)(this,(e=>{e(this.allSublayers)}))}_fetchService(e){return new Promise(((r,t)=>{if(this.sourceJSON){if(null!=this.sourceJSON.bandCount&&null!=this.sourceJSON.pixelSizeX)throw new s.Z("tile-layer:unsupported-url","use ImageryTileLayer to open a tiled image service");return void r({data:this.sourceJSON})}if(!this.parsedUrl)throw new s.Z("tile-layer:undefined-url","layer's url is not defined");const i=(0,D.Qc)(this.parsedUrl.path);if((0,o.pC)(i)&&"ImageServer"===i.serverType)throw new s.Z("tile-layer:unsupported-url","use ImageryTileLayer to open a tiled image service");(0,a.default)(this.parsedUrl.path,{query:{f:"json",...this.parsedUrl.query,...this.customParameters,token:this.apiKey},responseType:"json",signal:e}).then(r,t)})).then((r=>{let t=this.url;if(r.ssl&&(t=this.url=t.replace(/^http:/i,"https:")),this.sourceJSON=r.data,this.read(r.data,{origin:"service",url:this.parsedUrl}),10.1===this.version&&!(0,D.M8)(t))return this._fetchServerVersion(t,e).then((e=>{this.read({currentVersion:e})})).catch((()=>{}))}))}_fetchServerVersion(e,r){if(!(0,D.B5)(e))return Promise.reject();const t=e.replace(/(.*\/rest)\/.*/i,"$1")+"/info";return(0,a.default)(t,{query:{f:"json",...this.customParameters,token:this.apiKey},responseType:"json",signal:r}).then((e=>{if(e.data&&e.data.currentVersion)return e.data.currentVersion;throw new s.Z("tile-layer:version-not-available")}))}_getMapName(e){const r=e.match(/^(?:https?:)?\/\/(server\.arcgisonline\.com|services\.arcgisonline\.com|ibasemaps-api\.arcgis\.com)\/arcgis\/rest\/services\/([^\/]+(\/[^\/]+)*)\/mapserver/i);return r?r[2]:void 0}_getDefaultAttribution(e){if(null==e)return null;let r;e=e.toLowerCase();for(let t=0,i=U.length;t<i;t++)if(r=U[t],r.toLowerCase().includes(e))return(0,u.hF)("//static.arcgis.com/attribution/"+r);return null}_getDefaultTileServers(e){if(null==e)return[];const r=-1!==e.search(/^(?:https?:)?\/\/server\.arcgisonline\.com/i),t=-1!==e.search(/^(?:https?:)?\/\/services\.arcgisonline\.com/i);return r||t?[e,e.replace(r?/server\.arcgisonline/i:/services\.arcgisonline/i,r?"services.arcgisonline":"server.arcgisonline")]:[]}get hasOverriddenFetchTile(){return!this.fetchTile.__isDefault__}};(0,i._)([(0,h.Cb)({readOnly:!0})],W.prototype,"attributionDataUrl",null),(0,i._)([(0,h.Cb)({type:["show","hide","hide-children"]})],W.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)({json:{read:!0,write:!0}})],W.prototype,"blendMode",void 0),(0,i._)([(0,h.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],W.prototype,"isReference",void 0),(0,i._)([(0,h.Cb)({readOnly:!0,type:["ArcGISTiledMapServiceLayer"]})],W.prototype,"operationalLayerType",void 0),(0,i._)([(0,h.Cb)({type:Boolean})],W.prototype,"resampling",void 0),(0,i._)([(0,h.Cb)()],W.prototype,"sourceJSON",void 0),(0,i._)([(0,h.Cb)({type:f.Z})],W.prototype,"spatialReference",void 0),(0,i._)([(0,y.r)("spatialReference",["spatialReference","tileInfo"])],W.prototype,"readSpatialReference",null),(0,i._)([(0,h.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],W.prototype,"path",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],W.prototype,"sublayers",void 0),(0,i._)([(0,v.c)("sublayers",{layers:{type:[I.Z]}})],W.prototype,"writeSublayers",null),(0,i._)([(0,h.Cb)({json:{read:!1,write:!1}})],W.prototype,"popupEnabled",void 0),(0,i._)([(0,h.Cb)()],W.prototype,"tileServers",null),(0,i._)([(0,d.p)("tileServers")],W.prototype,"castTileServers",null),(0,i._)([(0,h.Cb)({readOnly:!0,json:{read:!1}})],W.prototype,"type",void 0),(0,i._)([(0,h.Cb)(j.HQ)],W.prototype,"url",void 0),W=(0,i._)([(0,_.j)("esri.layers.TileLayer")],W),W.prototype.fetchTile.__isDefault__=!0;const x=W},46486:(e,r,t)=>{t.d(r,{Z:()=>c});var i=t(43697),a=(t(66577),t(5600)),s=(t(75215),t(67676),t(71715)),l=t(52011),n=t(45322),o=t(56608),p=t(82971);const c=e=>{let r=class extends e{constructor(){super(...arguments),this.copyright=null,this.minScale=0,this.maxScale=0,this.spatialReference=null,this.tileInfo=null,this.tilemapCache=null}readMinScale(e,r){return null!=r.minLOD&&null!=r.maxLOD?e:0}readMaxScale(e,r){return null!=r.minLOD&&null!=r.maxLOD?e:0}get supportsBlankTile(){return this.version>=10.2}readTilemapCache(e,r){return r.capabilities&&r.capabilities.includes("Tilemap")?new o.y({layer:this}):null}};return(0,i._)([(0,a.Cb)({json:{read:{source:"copyrightText"}}})],r.prototype,"copyright",void 0),(0,i._)([(0,a.Cb)()],r.prototype,"minScale",void 0),(0,i._)([(0,s.r)("service","minScale")],r.prototype,"readMinScale",null),(0,i._)([(0,a.Cb)()],r.prototype,"maxScale",void 0),(0,i._)([(0,s.r)("service","maxScale")],r.prototype,"readMaxScale",null),(0,i._)([(0,a.Cb)({type:p.Z})],r.prototype,"spatialReference",void 0),(0,i._)([(0,a.Cb)({readOnly:!0})],r.prototype,"supportsBlankTile",null),(0,i._)([(0,a.Cb)(n.h)],r.prototype,"tileInfo",void 0),(0,i._)([(0,a.Cb)()],r.prototype,"tilemapCache",void 0),(0,i._)([(0,s.r)("service","tilemapCache",["capabilities"])],r.prototype,"readTilemapCache",null),(0,i._)([(0,a.Cb)()],r.prototype,"version",void 0),r=(0,i._)([(0,l.j)("esri.layers.mixins.ArcGISCachedService")],r),r}}}]);