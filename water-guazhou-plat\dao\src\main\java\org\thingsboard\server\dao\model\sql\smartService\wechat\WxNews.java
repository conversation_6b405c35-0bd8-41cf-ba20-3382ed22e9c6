package org.thingsboard.server.dao.model.sql.smartService.wechat;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("wx_news")
public class WxNews {
    // id
    private String id;

    // 标题
    private String title;

    // 摘要
    private String summary;

    // 内容
    private String content;

    // 类别id
    private String categoryId;

    // 类别名称
    @TableField(exist = false)
    private String categoryName;

    // 编号
    private String serialNo;

    // 封面图片
    private String cover;

    // 创建时间
    private Date createTime;

    // 排序编号
    private Integer orderNum;

    // 客户id
    private String tenantId;

}
