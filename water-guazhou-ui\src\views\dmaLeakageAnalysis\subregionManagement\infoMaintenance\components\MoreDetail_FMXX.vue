<template>
  <Search
    ref="refSearch"
    :config="SearchConfig"
    class="search"
  ></Search>
  <FormTable
    :config="TableConfig"
    class="table-box"
  ></FormTable>
  <DialogForm
    ref="refDialog"
    :config="DialogConfig"
  ></DialogForm>
</template>
<script lang="ts" setup>
import {
  AddDmaPartitionValve,
  DeleteDmaPartitionValve,
  GetDmaPartitionValve
} from '@/api/mapservice/dma/partitionResources'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'

const props = defineProps<{ partition?: NormalOption }>()
const refSearch = ref<ISearchIns>()
const refDialog = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'select',
      label: '类型',
      field: 'type',
      options: [
        { label: '边界阀门', value: '1' },
        { label: '分支管线阀门', value: '2' }
      ]
    },
    { type: 'input', label: '编号', field: 'code', clearable: false }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          iconifyIcon: 'ep:search',
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          perm: true,
          iconifyIcon: 'ep:refresh',
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            // refreshData()
          }
        },
        {
          perm: true,
          iconifyIcon: 'ep:circle-plus',
          text: '新增',
          type: 'success',
          click: () => handleAou()
        }
      ]
    }
  ]
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '类型', prop: 'typeName' },
    { label: '编号', prop: 'code' },
    { label: '口径', prop: 'caliber' },
    { label: '阀门类型', prop: 'valveType' },
    { label: '地址', prop: 'address' },
    { label: '运行状态', prop: 'runStatus' },
    { label: '备注', prop: 'remark' },
    { label: '现场图片', prop: 'img', image: true }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '编辑',
      iconifyIcon: 'ep:edit',
      click: row => handleAou(row)
    },
    {
      perm: true,
      text: '删除',
      iconifyIcon: 'ep:delete',
      type: 'danger',
      click: row => handleDelete(row)
    }
  ]
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaPartitionValve({
      ...query,
      partitionId: props.partition?.value,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    })
    const data = res.data.data || {}
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const handleAou = (row?: any) => {
  DialogConfig.defaultValue = {
    ...(row || {})
  }
  DialogConfig.title = row ? '编辑阀门表' : '添加阀门表'
  refDialog.value?.openDialog()
}
const handleDelete = (row?: any) => {
  const ids = row ? [row.id] : []
  if (!ids.length) {
    SLMessage.error('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除?', '提示信息')
    .then(async () => {
      try {
        const res = await DeleteDmaPartitionValve(ids)
        if (res.data.code === 200) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      //
    })
}
const DialogConfig = reactive<IDialogFormConfig>({
  title: '添加流量表',
  dialogWidth: 600,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          lg: 24,
          xl: 12,
          type: 'select',
          label: '类型',
          field: 'type',
          options: [
            { label: '边界阀门', value: '1' },
            { label: '分支管线阀门', value: '2' }
          ],
          rules: [{ required: true, message: '请输入类型' }]
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '编号',
          field: 'code'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '阀门类型',
          field: 'valveType',
          rules: [{ required: true, message: '请输入阀门类型' }]
        },
        {
          lg: 24,
          xl: 12,
          type: 'input-number',
          label: '口径',
          field: 'caliber'
        },
        {
          lg: 24,
          xl: 12,
          type: 'textarea',
          label: '地址',
          field: 'address'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '运行状态',
          field: 'runStatus'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'image',
          label: '现场图片',
          field: 'img'
        }
      ]
    }
  ],
  submit: async params => {
    DialogConfig.submitting = true
    try {
      const res = await AddDmaPartitionValve({
        ...params,
        partitionId: props.partition?.value
      })
      if (res.data.code === 200) {
        SLMessage.success('提交成功')
        refreshData()
        refDialog.value?.closeDialog()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('提交失败')
    }
    DialogConfig.submitting = false
  }
})
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.search {
  margin: 0 -20px 10px -20px;
}
.table-box {
  height: calc(100% - 40px);
}
</style>
