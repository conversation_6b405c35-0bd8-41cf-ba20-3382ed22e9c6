package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBusiness;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalBusinessSaveRequest extends SaveRequest<SsPortalBusiness> {
    // 标题
    @NotNullOrEmpty
    private String title;

    // 简介
    @NotNullOrEmpty
    private String introduce;

    // 封面
    private String cover;

    // 是否跳转URL链接
    private Boolean jumpToUrl = false;

    // 链接
    private String link;

    // 业务内容
    private String content;

    // 排序
    private String orderNum;

    @Override
    protected SsPortalBusiness build() {
        SsPortalBusiness entity = new SsPortalBusiness();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalBusiness update(String id) {
        SsPortalBusiness entity = new SsPortalBusiness();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalBusiness entity) {
        entity.setTitle(title);
        entity.setIntroduce(introduce);
        entity.setCover(cover);
        entity.setJumpToUrl(jumpToUrl);
        entity.setLink(link);
        entity.setContent(content);
        entity.setActive(false);
        entity.setOrderNumber(orderNum);
    }

}