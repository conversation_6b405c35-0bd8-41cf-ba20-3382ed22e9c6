import{_ as b}from"./CardTable-rdWOL4_6.js";import{_ as y}from"./CardSearch-CB_HNR-Q.js";import{d as k,bF as r,c as u,r as i,a8 as D,bX as x,o as P,ah as T,g as Y,n as v,q as f,i as m}from"./index-r0dFAfgr.js";import{g as C}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as S}from"./useStation-DJgnSZIA.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const j={class:"wrapper"},A=k({__name:"index",setup(w){const{getStationTree:_}=S(),p=r().date(),c=u(),e=i({data:[],checkedKeys:[],checkedNodes:[]});u(!1);const d=i({defaultParams:{date:[r().date(p-3).format("YYYY-MM-DD"),r().date(p).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",defaultExpandAll:!0,multiple:!0,options:D(()=>e.data),label:"站点选择",onChange:a=>{e.checkedKeys=a||[],e.checkedNodes=[];for(const t in a){const o=x(e.data,"children","id",t);e.checkedNodes.push(o)}l()}},{type:"daterange",label:"选择时间",field:"date"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>l(),icon:"iconfont icon-chaxun"}]}]}),n=i({loading:!1,dataList:[],columns:[{prop:"name",label:"泵房名称"},{prop:"outletTotalFlow",label:"出水"},{prop:"inletTotalFlow",label:"进水"},{prop:"differenceTotalFlow",label:"进出水差值"},{prop:"differenceRate",label:"差值率"}],operations:[],showSummary:!1,operationWidth:"150px",indexVisible:!0,pagination:{hide:!0}}),l=()=>{var s;n.loading=!0;const a=((s=c.value)==null?void 0:s.queryParams)||{},o={stationIdList:e.checkedKeys.join(","),start:r(a.date[0]).valueOf(),end:r(a.date[1]).valueOf()};C(o).then(h=>{const g=h.data.data;n.dataList=g,n.loading=!1})};return P(async()=>{var t;const a=await _("水源地");e.data=a,e.currentProject=T(e.data),d.defaultParams={...d.defaultParams,treeData:[e.currentProject.id]},(t=c.value)==null||t.resetForm(),e.checkedKeys=[e.currentProject.id],l()}),(a,t)=>{const o=y,s=b;return Y(),v("div",j,[f(o,{ref_key:"cardSearch",ref:c,config:m(d)},null,8,["config"]),f(s,{class:"card-table",config:m(n)},null,8,["config"])])}}});export{A as default};
