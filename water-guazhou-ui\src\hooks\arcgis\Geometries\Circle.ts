class Circle {
  constructor(radius: number, x: number, y: number, color?: string) {
    this.x = x
    this.y = y
    this.radius = radius
    this.color = color || '#00ffff'
  }

  x: number

  y: number

  curRadius = 0

  radius: number

  color: string

  setRadius(radius: number) {
    this.radius = radius
  }

  expandRadius(step: number) {
    this.curRadius += step
  }

  setColor(color: string) {
    this.color = color
  }

  draw(context: CanvasRenderingContext2D | null) {
    if (context === null) {
      console.warn('context is null')
      return
    }

    context.beginPath()
    if (this.curRadius + 2 > this.radius) this.curRadius = 0
    context.arc(this.x, this.y, this.curRadius, 0, Math.PI * 2)
    context.closePath()
    context.lineWidth = 2
    context.strokeStyle = this.color
    context.stroke()
  }
}
export default Circle
