package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardClass;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardClassPageRequest;

@Mapper
public interface GuardClassMapper extends BaseMapper<GuardClass> {
    IPage<GuardClass> findByPage(GuardClassPageRequest request);

    boolean updateFully(GuardClass entity);

}
