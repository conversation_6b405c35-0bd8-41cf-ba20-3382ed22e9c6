/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.role.Role;
import org.thingsboard.server.dao.role.RoleService;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/role")
@Slf4j
public class RoleController extends BaseController {

    @Autowired
    private RoleService roleService;


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/saveRole", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ROLE_ADD)
    public Role saveRole(@RequestBody Role role) throws ThingsboardException {
        if (role == null) {
            throw new ThingsboardException("invalid arguments!" ,
                    ThingsboardErrorCode.INVALID_ARGUMENTS);
        }
        role.setTenantId(getTenantId());
        // 保存
        return checkNotNull(roleService.saveRole(role));
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/deleteRole/{roleId}", method = RequestMethod.DELETE)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ROLE_DELETE)
    public void deleteRole(@PathVariable("roleId") String roleId) throws ThingsboardException {
        checkParameter("roleId", roleId);
        try {
            if (roleId == null || roleId.trim().equals("")) {
                throw new ThingsboardException("invalid arguments!" ,
                        ThingsboardErrorCode.INVALID_ARGUMENTS);
            }
            roleService.deleteRole(getTenantId(),new RoleId(toUUID(roleId)));
        } catch (RuntimeException e) {
            log.error("deleteRole error. message = [{}]", e.getMessage());
            throw new ThingsboardException(e.getMessage(), ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/roles", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public List<Role> getRoles() throws ThingsboardException {
        try {
            return checkNotNull(roleService.findByTenantId(getTenantId()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public PageData<Role> findList(@RequestParam Integer page, @RequestParam Integer size, @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        try {
            return roleService.findList(page, size, name, UUIDConverter.fromTimeUUID(getTenantId().getId()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/assignMenuToRole", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ROLE_MENU_ADD)
    public void assignMenuToRole(@RequestBody Map map) throws ThingsboardException {
        List<String> menuIds = (List<String>) map.get("menuIds");
        RoleId roleId = new RoleId(toUUID((String) map.get("roleId")));
        if (roleService.findById(roleId) == null) {
            throw new ThingsboardException("Role does not exist!",
                    ThingsboardErrorCode.INVALID_ARGUMENTS);
        }

        String tenantApplicationId = (String) map.get("tenantApplicationId");
        if (StringUtils.isNotBlank(tenantApplicationId)) {
            roleService.assignMenuToRole(menuIds, roleId, tenantApplicationId);
        } else {
            // 保存
            roleService.assignMenuToRole(menuIds, roleId);
        }

    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/assignTenantApplicationToRole", method = RequestMethod.POST)
    public void assignMenuToRole(@RequestBody JSONObject params) throws ThingsboardException {
        String tenantApplicationId = params.getString("tenantApplicationId");
        String roleId = params.getString("roleId");
        roleService.assignTenantApplicationToRole(roleId, tenantApplicationId);
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/assignRoleToUser", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ROLE_USER_ADD)
    public void assignRoleToUser(@RequestBody Map map) throws ThingsboardException {
        String strUserId = (String) map.get("userId");
        String strRoleId = (String) map.get("roleId");
        try {
            RoleId roleId = null;
            if (strRoleId != null && !strRoleId.trim().equals("")) {
                roleId = new RoleId(toUUID(strRoleId));
            }
            roleService.assignRoleToUser(new UserId(toUUID(strUserId)), roleId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/getTreeByRoleId/{roleId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public List<String> getTreeByRoleId(@PathVariable("roleId") String strRoleId) throws ThingsboardException {
        checkParameter("roleId", strRoleId);
        try {
            RoleId roleId = new RoleId(toUUID(strRoleId));

            return roleService.getTreeByRoleId(roleId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/getTreeByRoleIdAndTenantApplicationId", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public List<String> getTreeByRoleIdAndTenantApplicationId(@RequestParam(name = "roleId") String strRoleId,
                                                              @RequestParam String tenantApplicationId) throws ThingsboardException {
        checkParameter("roleId", strRoleId);
        try {
            RoleId roleId = new RoleId(UUIDConverter.fromString(strRoleId));

            return roleService.getTreeByRoleId(roleId, tenantApplicationId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/getRoleTenantApplicationList", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public List<String> getRoleTenantApplicationList(@RequestParam(name = "roleId") String strRoleId) throws ThingsboardException {
        checkParameter("roleId", strRoleId);
        try {
            return roleService.getRoleTenantApplicationList(strRoleId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/getRoleIdByUserId/{userId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public String getRoleIdByUserId(@PathVariable("userId") String strUserId) throws ThingsboardException {
        checkParameter("userId", strUserId);
        try {
            UserId userId = new UserId(toUUID(strUserId));
            String roleId = roleService.getRoleIdByUserId(userId);
            if (roleId == null || roleId.trim().length() < 1) {
                return "";
            }
            return new RoleId(UUIDConverter.fromString(roleId)).getId().toString();
        } catch (Exception e) {
            return "";
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/getRoleIdsByUserId/{userId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public List<String> getRoleIdsByUserId(@PathVariable("userId") String strUserId) throws ThingsboardException {
        checkParameter("userId", strUserId);
        try {
            UserId userId = new UserId(toUUID(strUserId));
            List<String> roleId = roleService.getRoleIdsByUserId(userId);
            if (roleId == null || roleId.size() < 1) {
                return new ArrayList<>();
            }

            return roleId.stream().map(id -> new RoleId(UUIDConverter.fromString(id)).getId().toString()).collect(Collectors.toList());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "getUserListByRole/{roleId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ROLE_INFO_GET)
    public PageData<User> getUserListByRole(@PathVariable("roleId") String roleId,
                                            @RequestParam(required = false) Integer page,
                                            @RequestParam(required = false) Integer size) throws ThingsboardException {
        List<User> userList = roleService.getUserListByRole(roleId);
        if (userList == null) {
            userList = new ArrayList<>();
        }
        if (page == null || size == null) {
            return new PageData<>(userList.size(), userList);
        } else {
            return PageData.page(userList, page, size);
        }
    }


}
