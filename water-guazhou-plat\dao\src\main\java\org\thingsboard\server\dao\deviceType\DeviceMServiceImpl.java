package org.thingsboard.server.dao.deviceType;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.deviceManage.Device;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.sql.deviceType.DeviceMapper;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DeviceMServiceImpl implements DeviceMService {

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;


    @Override
    public PageData getList(String typeId, String serialId, String name, String model, int page, int size, String tenantId) {
        // 查找所有typeId
        List<String> typeIdList = null;
        if (StringUtils.isNotBlank(typeId)) {
            typeIdList = new ArrayList<>();
            this.getAllChildTypeId(typeId, typeIdList);
            typeIdList.add(typeId);
        }

        List<Device> deviceList = deviceMapper.getList(typeIdList, serialId, name, model, page, size, tenantId);

        //设置所属类
        for (Device device : deviceList) {
            this.setType(device);
        }
        int total = deviceMapper.getListCount(typeIdList, serialId, name, model, tenantId);
        return new PageData(total, deviceList);
    }

    private void getAllChildTypeId(String typeId, List<String> typeIdList) {
        List<DeviceType> children = deviceTypeMapper.findChildren(typeId);
        if (children != null && children.size() > 0) {
            List<String> idList = children.stream().map(a -> a.getId()).collect(Collectors.toList());
            typeIdList.addAll(idList);
            for (String id : idList) {
                this.getAllChildTypeId(id, typeIdList);
            }
        }
    }

    @Override
    public Device save(Device device) {
        device.setUpdateTime(new Date());
        if (StringUtils.isBlank(device.getId())) {
            device.setCreateTime(new Date());

            deviceMapper.insert(device);
        } else {
            deviceMapper.updateById(device);
        }

        return device;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        deviceMapper.deleteBatchIds(ids);
        return IstarResponse.ok("删除成功");
    }

    @Override
    public boolean checkSerialId(Device device, String tenantId) {
        Device device1 = deviceMapper.selectBySerialId(device.getSerialId(), tenantId);
        if (device1 != null && !device1.getId().equals(device.getId())) {
            return false;
        }
        return true;
    }

    private void setType(Device device) {
        if (StringUtils.isBlank(device.getTypeId())) {
            device.setLinkedType("-");
            device.setTopType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = device.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            if (i == 0) {
                device.setTypeSerialId(deviceType.getSerialId());
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        device.setLinkedType(linkedType);
        device.setTopType(topType);
    }
}
