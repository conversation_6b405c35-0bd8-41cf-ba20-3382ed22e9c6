<!-- 系统配置-消息类型 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable class="card-table" :config="TableConfig"></CardTable>
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { getSystemNotifyTypeList, postSystemNotifyType } from '@/api/system/default'
import {
  GetMenu,
  GetMenuTree
} from '@/api/menu/source'
import { traverse } from '@/utils/GlobalHelper'

const refSearch = ref<ICardSearchIns>()
const refForm = ref<IDialogFormIns>()

const stateType = [
  { label: '有效', value: '1', color: '#42eb45' },
  { label: '无效', value: '0', color: '#f02929' }
]

const sendType = [
  { label: '短信', value: '1' },
  { label: '邮件', value: '2' }
]

// 消息类型
const messageType = [
  { label: '通知', value: '0' },
  { label: '告警', value: '1' },
  { label: '待办', value: '2' }
]

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      label: '消息类型',
      field: 'type',
      type: 'select',
      options: messageType
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新增',
          iconifyIcon: 'ep:circle-plus',
          type: 'success',
          click: () => {
            FormConfig.title = '新增消息类型'
            FormConfig.defaultValue = {}
            refForm.value?.openDialog()
          }
        }, {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        }
      ]
    }
  ]
})
const TableConfig = reactive<ITable>({
  dataList: [],
  operationWidth: 220,
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '唯一标识', prop: 'code' },
    { minWidth: 120, label: '业务模块', prop: 'model' },
    { minWidth: 120, label: '消息类型', prop: 'type', formatter: row => messageType.find(item => item.value === row.type)?.label },
    { minWidth: 120, label: '跳转菜单', prop: 'menuName' },
    { minWidth: 120, label: '跳转路由', prop: 'route' },
    { minWidth: 120, label: '发送类型', prop: 'sendType', formatter: row => sendType.find(item => item.value === row.sendType)?.label },
    {
      minWidth: 120,
      label: '状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string => stateType.find(item => item.value === row.status)?.color || '',
      formatter: row => stateType.find(item => item.value === row.status)?.label
    }
  ],
  operations: [
    {
      perm: true,
      text: '编辑',
      iconifyIcon: 'ep:edit-pen',
      click: row => {
        FormConfig.title = '编辑消息类型'
        FormConfig.defaultValue = { ...row }
        refForm.value?.openDialog()
      }
    }
  ],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const FormConfig = reactive<IDialogFormConfig>({
  defaultValue: {
    route: '',
    menuName: ''
  },
  title: '消息类型',
  dialogWidth: '500px',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '名称',
          field: 'model',
          rules: [{ required: true, message: '请输入名称' }]
        }, {
          type: 'select',
          label: '消息类型',
          field: 'type',
          rules: [{ required: true, message: '请选择消息类型' }],
          options: messageType
        }, {
          type: 'select-tree',
          label: '跳转菜单',
          field: 'path',
          rules: [{ required: true, message: '请选择跳转菜单' }],
          options: computed(() => state.data) as any,
          onChange: val => {
            GetMenu(val).then(res => {
              if (refForm.value?.refForm?.dataForm) {
                refForm.value.refForm.dataForm.route = res.data.component
                refForm.value.refForm.dataForm.menuName = res.data.meta.title
              }
            })
          }
        }, {
          type: 'select',
          label: '发送类型',
          field: 'sendType',
          rules: [{ required: true, message: '请选择发送类型' }],
          options: sendType
        }, {
          type: 'select',
          label: '状态',
          field: 'status',
          options: [
            { label: '有效', value: '1' },
            { label: '无效', value: '0' }
          ]
        }
      ]
    }
  ],
  submit: params => {
    let text = '新增'
    if (params.id) {
      text = '编辑'
    }
    postSystemNotifyType(params).then(res => {
      if (res.data.code === 200) {
        ElMessage.success(text + '成功')
        refForm.value?.closeDialog()
        refreshData()
      } else {
        ElMessage.warning(res.data.message)
      }
    }).catch(error => {
      ElMessage.warning(error)
    })
  }
})

const state = reactive({
  data: []
})

const refreshTree = async () => {
  const res = await GetMenuTree()
  state.data = traverse(res.data || [], 'children', { label: 'label', value: 'id' })
}

const refreshData = () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  getSystemNotifyTypeList(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
  refreshTree()
})
</script>
<style lang="scss" scoped></style>
