import{_ as L}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as M}from"./CardTable-rdWOL4_6.js";import{_ as A}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as B,c as b,r as _,l as c,bJ as R,bI as E,bH as F,o as J,g as N,n as O,q as d,i as h,F as U,C as V}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as Y}from"./usePartition-DkcY9fQ2.js";import{G as j,a as z,A as K}from"./AOUMonitoring-D6nOiq_r.js";import"./index-C9hz-UZb.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";const Q={class:"wrapper"},X=B({__name:"index",setup(Z){const g=b(),u=b(),f=(t,n,r)=>r.hidden=t.type!==r.field,I=_({filters:[{type:"radio-button",label:"选择方式",field:"type",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"day"}]},{handleHidden:f,type:"year",label:"",field:"year",clearable:!1,disabledDate(t){return new Date<t}},{handleHidden:f,type:"month",label:"",field:"month",clearable:!1,disabledDate(t){return new Date<t}},{handleHidden:f,type:"daterange",label:"",field:"day",clearable:!1,disabledDate(t){return new Date<t}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>D()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var t;(t=u.value)==null||t.resetForm()}},{perm:!0,text:"添加监测点",type:"success",iconifyIcon:"ep:circle-plus",click:()=>{var t;(t=v.value)==null||t.openDialog()}},{perm:!0,text:"导出",type:"success",iconifyIcon:"ep:download",click:()=>{var t;return(t=g.value)==null?void 0:t.exportTable()}}]}],defaultParams:{type:"year",year:c().format(R),month:c().format(E),day:[c().format(F),c().format(F)]}}),a=_({dataList:[],columns:[{label:"日期",prop:"date"},{label:"总供水",prop:"total"}],pagination:{hide:!0}}),v=b(),q=_({title:"添加监测点",dialogWidth:"80%",group:[],cancel:!1}),D=async()=>{var t,n,r,l,m,x,C,k;try{a.loading=!0;const e=((t=u.value)==null?void 0:t.queryParams)||{},G=j(),H=z({type:e.type,date:e.type==="month"?e.month:e.type==="year"?e.year:void 0,start:e.type==="day"?(n=e.day)==null?void 0:n[0]:void 0,end:e.type==="day"?(r=e.day)==null?void 0:r[1]:void 0}),w=await Promise.all([G,H]),W=((l=w[0].data.data)==null?void 0:l.map(o=>{var s;return{label:o.title,prop:o.title,subColumns:[...((s=o.titleList)==null?void 0:s.map(T=>({label:T.label,prop:T.value,minWidth:160})))||[],{minWidth:140,label:"水厂总进水",prop:`in${o.title}`},{minWidth:140,label:"水厂总出水",prop:`out${o.title}`},{minWidth:140,label:"差值",prop:`difference${o.title}`}]}}))||[];a.columns=[{minWidth:160,label:"日期",prop:"date"},...W,{minWidth:160,label:"总供水",prop:"sum"}];const p=((x=(m=w[1])==null?void 0:m.data)==null?void 0:x.data)||[],y=p.length,i={date:"合计"},P={date:"平均"};(k=(C=W[0])==null?void 0:C.subColumns)==null||k.map(o=>{i[o.prop]=y?p.reduce((S,s)=>S+s[o.prop],0):0,i[o.prop]=i[o.prop].toFixed(2),P[o.prop]=(y?i[o.prop]/y:0).toFixed(2)}),p.push(i),p.push(P),a.dataList=p}catch{}a.loading=!1},$=Y();return J(async()=>{await $.getTree(),D()}),(t,n)=>{const r=A,l=M,m=L;return N(),O("div",Q,[d(r,{ref_key:"refSearch",ref:u,class:"search",config:h(I)},null,8,["config"]),d(l,{ref_key:"refTable",ref:g,class:"card-table",config:h(a)},null,8,["config"]),d(m,{ref_key:"refDialog",ref:v,config:h(q)},{default:U(()=>[d(K)]),_:1},8,["config"])])}}}),ko=V(X,[["__scopeId","data-v-b0a8c0d5"]]);export{ko as default};
