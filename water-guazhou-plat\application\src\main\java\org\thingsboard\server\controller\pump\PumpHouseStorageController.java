package org.thingsboard.server.controller.pump;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage;
import org.thingsboard.server.dao.pumpHouse.PumpHouseStorageService;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStoragePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStorageSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/sp/pumpHouseStorage")
public class PumpHouseStorageController extends BaseController {
    @Autowired
    private PumpHouseStorageService service;


    @GetMapping
    public IPage<PumpHouseStorage> findAllConditional(PumpHouseStoragePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/excel/template")
    public ExcelFileInfo excelTemplate() {
        return ExcelFileInfo.of("泵机台账模板")
                .withDate()
                .withoutTitle()
                .nextTitle("泵房编码")
                .nextTitle("泵房名称")
                .nextTitle("泵房简称")
                .nextTitle("厂家名称")
                .nextTitle("供水类型（可选：箱式/罐式/微型/其他）")
                .nextTitle("水箱个数")
                .nextTitle("地址")
                .nextTitle("安装人")
                .nextTitle("安装日期（示例：2001-01-01）")
                .nextTitle("采集频率（分钟）")
                .nextTitle("存储频率（分钟）")
                .nextTitle("备注");
    }

    @GetMapping("/excel/export")
    public ExcelFileInfo excelExport(PumpHouseStoragePageRequest request) {
        return ExcelFileInfo.of("泵机台账", findAllConditional(request.ignorePage()))
                .withDateTime()
                .nextTitle("code", "泵房编码")
                .nextTitle("name", "泵房名称")
                .nextTitle("nickname", "泵房简称")
                .nextTitle("companyName", "厂家名称")
                .nextTitle("supplyMethod", "供水类型（可选：箱式/罐式/微型/其他）")
                .nextTitle("waterBoxNum", "水箱个数")
                .nextTitle("address", "地址")
                .nextTitle("installUserName", "安装人")
                .nextTitle("installDateName", "安装日期（示例：2001-01-01）")
                .nextTitle("collectionFrequency", "采集频率（分钟）")
                .nextTitle("storageFrequency", "存储频率（分钟）")
                .nextTitle("remark", "备注");
    }

    @PostMapping
    public PumpHouseStorage save(@RequestBody PumpHouseStorageSaveRequest req) {
        return service.save(req);
    }

    @PostMapping("/batch")
    public List<PumpHouseStorage> saveBatch(@RequestBody List<PumpHouseStorageSaveRequest> req) {
        return service.saveAll(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody PumpHouseStorageSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

}