import request from '@/plugins/axios';

/**
 * 获取总id 菜单池的
 * @returns
 */
export function getRootId() {
  return request({
    url: '/api/menu/pool/getRootId',
    method: 'get'
  });
}

/**
 * 查询菜单明细
 * @param id
 * @returns
 */
export const GetMenuDetail = (id: string) =>
  request({
    url: `/api/menu/pool/${id}`,
    method: 'get'
  });

/**
 * 菜单池的 获取用于分配给租户的菜单列表
 * @returns
 */
export function getSelectableTree() {
  return request({
    url: '/api/menu/pool/getSelectableTree',
    method: 'get'
  });
}

/**
 * 菜单池的 新增菜单到菜单池
 * @param params
 * @returns
 */
export function setMenuPool(params: {
  data: {
    id?: string;
    /**
     * 根级为109，子级为110
     */
    type?: string;
    orderNum?: string | number;
    path?: string;
    name?: string;
    component?: string;
    meta?: {
      title?: string;
      icon?: string;
      roles?: string[];
    };
  };
  parentId?: string;
}) {
  return request({
    url: '/api/menu/pool',
    method: 'post',
    data: params
  });
}

/**
 * 获取总id 分配出去的
 * @returns
 */
export function getCustomerRootId() {
  return request({
    url: '/api/menu/customer/getRootId',
    method: 'get'
  });
}

/**
 * 分配菜单给指定租户
 * @param params
 * @returns
 */
export function setTenantMenu(params) {
  return request({
    url: '/api/menu/tenant',
    method: 'post',
    data: params
  });
}

/**
 * tenant admin登录获取扩展菜单
 * @returns
 */
export function getFindByCurrentUser() {
  return request({
    url: '/api/menu/tenant/findByCurrentUser',
    method: 'get'
  });
}

// 分配菜单给指定角色
export function setAssignMenuToRole(params) {
  return request({
    url: '/api/role/assignMenuToRole',
    method: 'post',
    data: params
  });
}

// 获取指定租户已选择的菜单（仅返回ID）
export function getTreeByTenantId(tenantId) {
  return request({
    url: `/api/menu/tenant/getTreeByTenantId/${tenantId}`,
    method: 'get'
  });
}

// 获取指定用户已选择的菜单（仅返回ID）  获取指定角色已选择的菜单（仅返回菜单id）
export function getTreeByRoleId(tenantId) {
  return request({
    url: `/api/role/getTreeByRoleId/${tenantId}`,
    method: 'get'
  });
}

// 获取当前租户拥有的菜单类型 编辑扩展下拉可选类型
export function getTypes() {
  return request({
    url: '/api/menu/customer/getTypes',
    method: 'get'
  });
}

// 对已有菜单进行扩展 新增 编辑
export function saveMenuCustomer(params) {
  return request({
    url: '/api/menu/customer/saveMenuCustomer',
    method: 'post',
    data: params
  });
}

// 扩展菜单删除
export function deleteMenu(id) {
  return request({
    url: `/api/menu/customer/deleteMenu/${id}`,
    method: 'delete'
  });
}

// 获取当前租户的可用菜单
export function getTree() {
  return request({
    url: '/api/menu/customer/getTree',
    method: 'get'
  });
}

// 获取当前登录用户扩展菜单
export function getMenuByCurrentUser() {
  return request({
    url: '/api/menu/customer/findMenuByCurrentUser',
    method: 'get'
  });
}

// 获取指定角色下的用户
export function getUserListByRole(roleId) {
  return request({
    url: `/api/role/getUserListByRole/${roleId}`,
    method: 'get'
  });
}
/** ------------------角 色--------------------- */
// 新增/更新角色
export function saveRole(params) {
  return request({
    url: '/api/role/saveRole',
    method: 'post',
    data: params
  });
}

// 获取当前租户拥有的角色
export function getRoles() {
  return request({
    url: '/api/role/roles',
    method: 'get'
  });
}

// 分页获取角色
export function getRolesByPage(params) {
  return request({
    url: '/api/role/list',
    method: 'get',
    params
  });
}

// 删除角色
export function deleteRole(roleId) {
  return request({
    url: `/api/role/deleteRole/${roleId}`,
    method: 'delete'
  });
}

// 分配角色给指定User
export function assignRoleToUser(params) {
  return request({
    url: '/api/role/assignRoleToUser',
    method: 'post',
    data: params
  });
}

// 分配菜单给指定角色
export function assignMenuToRole(params) {
  return request({
    url: '/api/role/assignMenuToRole',
    method: 'post',
    data: params
  });
}

/**
 * 根据用户ID获取角色ID
 * @param {number} userId - 用户ID
 * @returns {Promise} 返回角色ID的Promise对象
 */
export function getRoleIdByUserId(userId) {
  return request({
    url: `/api/role/getRoleIdByUserId/${userId}`,
    method: 'get'
  });
}

/**
 * 根据用户ID获取用户的角色ID
 * @param {number} userId - 用户ID
 * @returns {Promise} 返回角色ID的Promise对象
 */
export function getRoleIdsByUserId(userId) {
  return request({
    url: `/api/role/getRoleIdsByUserId/${userId}`,
    method: 'get'
  });
}

// 获取指定用户的角色
export function assignProjectsToUser(params) {
  return request({
    url: `/api/assign/user/${params.userId}`,
    method: 'post',
    data: params.data
  });
}

// 获取指定用户的角色
export function assignProjectsToUsers(data) {
  return request({
    url: '/api/assign/users',
    method: 'post',
    data
  });
}
/**
 *查询应用下的菜单
 * @param params
 * @returns
 */
export const findTreeByTenantApplication = (params: {
  tenantApplicationId: string;
}) => {
  return request({
    url: '/api/tenantMenus/getRoleTenantApplicationList',
    method: 'get',
    params
  });
};
/**
 * 查询应用下指定角色的菜单权限
 * @param params
 * @returns
 */
export const GetRoleMenus = (params: {
  roleId: string;
  tenantApplicationId: string;
}) => {
  return request({
    url: '/api/tenantMenus/roleMenus',
    method: 'get',
    params
  });
};
/**
 * 保存用户在指定应用下的菜单权限
 * @param params
 * @returns
 */
export const saveRoleMenus = (params: {
  roleId: string;
  tenantApplicationId: string;
  menus: string[];
}) => {
  return request({
    url: '/api/tenantMenus/saveRoleMenus',
    method: 'post',
    data: params
  });
};
