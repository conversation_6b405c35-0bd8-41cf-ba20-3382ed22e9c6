<template>
  <div></div>
</template>
<script lang="ts" setup>
import { excuteIdentify, getGraphicLayer, initIdentifyParams, setMapCursor, setSymbol } from '@/utils/MapHelper';

interface IPickEmit {
  (
    e: 'picked',
    params: {
      mapPoint?: __esri.Point;
      mapExtent?: __esri.Extent;
      layerIds: number[];
      results: any[];
    }
  );
  (
    e: 'error',
    errir: any,
    params: {
      mapPoint?: __esri.Point;
      mapExtent?: __esri.Extent;
      layerIds: number[];
    }
  );
}
const emit = defineEmits<IPickEmit>();
const props = defineProps<{
  /**
   * 需要点选的图层id
   */
  layerIds?: number[];
  /**
   * 是否自动开启点选功能，false值时需要通过startPick开启
   */
  autoStart?: boolean;
  /**
   * 是否将查询结果添加地图
   */
  addToView?: boolean;
}>();
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  mapClick?: { remove: any };
  identifyResults: { layerName: string; layerId: number; feature: __esri.Graphic }[];
  mapPoint?: __esri.Point;
  mapExtent?: __esri.Extent;
} = {
  view: inject('view'),
  identifyResults: []
};
const startPick = () => {
  if (!staticState.view) return;
  setMapCursor('crosshair');
  staticState.mapClick = staticState.view?.on('click', async (e) => {
    await doIdentify(e);
  });
};
const stopPick = () => {
  staticState.mapClick?.remove();
};
const queryPick = async (mapPoint: __esri.Point, extent?: __esri.Extent) => {
  if (!staticState.view) return;
  staticState.mapExtent = extent ?? staticState.view.extent;
  staticState.mapPoint = mapPoint;
  const layerIds = props.layerIds || [];
  if (!layerIds.length) {
    console.log('请先选择要查询的图层');
    return;
  }
  const queryParams = initIdentifyParams({
    layerIds: layerIds || [],
    geometry: mapPoint,
    mapExtent: extent || staticState.view.extent
  });
  const res = await excuteIdentify(
    window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService,
    queryParams
  );

  staticState.identifyResults = res.results || [];
  staticState.identifyResults.length > 50 && (staticState.identifyResults.length = 50);

  const features = staticState.identifyResults.map((item) => {
    item.feature.symbol = setSymbol(item.feature.geometry.type);
    return item.feature;
  });
  if (props.addToView) {
    staticState.graphicsLayer = getGraphicLayer(staticState.view, {
      id: 'search-pick',
      title: '查询结果'
    });
    staticState.graphicsLayer?.removeAll();
    staticState.graphicsLayer?.addMany(features);
  }
  emit('picked', {
    mapPoint: staticState.mapPoint,
    mapExtent: staticState.mapExtent,
    layerIds: props.layerIds || [],
    results: staticState.identifyResults
  });
};
const doIdentify = async (e: any) => {
  if (!staticState.view) return;
  try {
    await queryPick(e.mapPoint);
  } catch (error) {
    emit('error', error, {
      mapPoint: staticState.mapPoint,
      mapExtent: staticState.mapExtent,
      layerIds: props.layerIds || []
    });
  }
};
onMounted(() => {
  props.autoStart && startPick();
});
onUnmounted(() => {
  staticState.mapClick?.remove();
  staticState.mapClick = undefined;
  staticState.graphicsLayer?.removeAll();
  staticState.graphicsLayer && staticState.view?.map.remove(staticState.graphicsLayer);
});
defineExpose({
  startPick,
  stopPick
});
</script>
<style lang="scss" scoped></style>
