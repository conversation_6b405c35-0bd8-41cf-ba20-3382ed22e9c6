<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="org.thingsboard.server.common.data.UUIDConverterTest" time="1.363" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\Code-Yan<PERSON>yun\water\guazhou\water-guazhou-IoT\common\data\target\test-classes;D:\Code-Yan<PERSON>yun\water\guazhou\water-guazhou-IoT\common\data\target\classes;D:\apache-maven-3.6.3\.m2\org\slf4j\slf4j-api\1.7.7\slf4j-api-1.7.7.jar;D:\apache-maven-3.6.3\.m2\org\slf4j\log4j-over-slf4j\1.7.7\log4j-over-slf4j-1.7.7.jar;D:\apache-maven-3.6.3\.m2\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\apache-maven-3.6.3\.m2\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-databind\2.8.11.1\jackson-databind-2.8.11.1.jar;D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-annotations\2.8.0\jackson-annotations-2.8.0.jar;D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-core\2.8.10\jackson-core-2.8.10.jar;D:\apache-maven-3.6.3\.m2\junit\junit\4.12\junit-4.12.jar;D:\apache-maven-3.6.3\.m2\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;D:\apache-maven-3.6.3\.m2\org\mockito\mockito-all\1.9.5\mockito-all-1.9.5.jar;D:\apache-maven-3.6.3\.m2\com\datastax\cassandra\cassandra-driver-core\3.5.0\cassandra-driver-core-3.5.0.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-handler\4.1.22.Final\netty-handler-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-buffer\4.1.22.Final\netty-buffer-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-common\4.1.22.Final\netty-common-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-transport\4.1.22.Final\netty-transport-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-resolver\4.1.22.Final\netty-resolver-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-codec\4.1.22.Final\netty-codec-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\com\google\guava\guava\21.0\guava-21.0.jar;D:\apache-maven-3.6.3\.m2\io\dropwizard\metrics\metrics-core\3.2.2\metrics-core-3.2.2.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-ffi\2.1.7\jnr-ffi-2.1.7.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jffi\1.2.16\jffi-1.2.16.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jffi\1.2.16\jffi-1.2.16-native.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm\5.0.3\asm-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-commons\5.0.3\asm-commons-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-analysis\5.0.3\asm-analysis-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-tree\5.0.3\asm-tree-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-util\5.0.3\asm-util-5.0.3.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-x86asm\1.0.2\jnr-x86asm-1.0.2.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-posix\3.0.44\jnr-posix-3.0.44.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-constants\0.9.9\jnr-constants-0.9.9.jar;D:\apache-maven-3.6.3\.m2\org\projectlombok\lombok\1.18.10\lombok-1.18.10.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk1.8.0_202\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire4442688531563025767\surefirebooter1389715930338935745.jar C:\Users\<USER>\AppData\Local\Temp\surefire4442688531563025767 2025-07-10T14-56-11_585-jvmRun1 surefire8420020095608298721tmp surefire_01374593440694893840tmp"/>
    <property name="surefire.test.class.path" value="D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\common\data\target\test-classes;D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\common\data\target\classes;D:\apache-maven-3.6.3\.m2\org\slf4j\slf4j-api\1.7.7\slf4j-api-1.7.7.jar;D:\apache-maven-3.6.3\.m2\org\slf4j\log4j-over-slf4j\1.7.7\log4j-over-slf4j-1.7.7.jar;D:\apache-maven-3.6.3\.m2\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\apache-maven-3.6.3\.m2\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-databind\2.8.11.1\jackson-databind-2.8.11.1.jar;D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-annotations\2.8.0\jackson-annotations-2.8.0.jar;D:\apache-maven-3.6.3\.m2\com\fasterxml\jackson\core\jackson-core\2.8.10\jackson-core-2.8.10.jar;D:\apache-maven-3.6.3\.m2\junit\junit\4.12\junit-4.12.jar;D:\apache-maven-3.6.3\.m2\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;D:\apache-maven-3.6.3\.m2\org\mockito\mockito-all\1.9.5\mockito-all-1.9.5.jar;D:\apache-maven-3.6.3\.m2\com\datastax\cassandra\cassandra-driver-core\3.5.0\cassandra-driver-core-3.5.0.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-handler\4.1.22.Final\netty-handler-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-buffer\4.1.22.Final\netty-buffer-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-common\4.1.22.Final\netty-common-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-transport\4.1.22.Final\netty-transport-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-resolver\4.1.22.Final\netty-resolver-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\io\netty\netty-codec\4.1.22.Final\netty-codec-4.1.22.Final.jar;D:\apache-maven-3.6.3\.m2\com\google\guava\guava\21.0\guava-21.0.jar;D:\apache-maven-3.6.3\.m2\io\dropwizard\metrics\metrics-core\3.2.2\metrics-core-3.2.2.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-ffi\2.1.7\jnr-ffi-2.1.7.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jffi\1.2.16\jffi-1.2.16.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jffi\1.2.16\jffi-1.2.16-native.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm\5.0.3\asm-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-commons\5.0.3\asm-commons-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-analysis\5.0.3\asm-analysis-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-tree\5.0.3\asm-tree-5.0.3.jar;D:\apache-maven-3.6.3\.m2\org\ow2\asm\asm-util\5.0.3\asm-util-5.0.3.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-x86asm\1.0.2\jnr-x86asm-1.0.2.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-posix\3.0.44\jnr-posix-3.0.44.jar;D:\apache-maven-3.6.3\.m2\com\github\jnr\jnr-constants\0.9.9\jnr-constants-0.9.9.jar;D:\apache-maven-3.6.3\.m2\org\projectlombok\lombok\1.18.10\lombok-1.18.10.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk1.8.0_202\jre"/>
    <property name="basedir" value="D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\common\data"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire4442688531563025767\surefirebooter1389715930338935745.jar"/>
    <property name="sun.boot.class.path" value="C:\Program Files\Java\jdk1.8.0_202\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_202\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_202\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_202-b08"/>
    <property name="user.name" value="14775"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Program Files\Java\jdk1.8.0_202\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\apache-maven-3.6.3\.m2"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2024.3.4.1"/>
    <property name="java.version" value="1.8.0_202"/>
    <property name="user.dir" value="D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\common\data"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk1.8.0_202\jre\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\YunMai\utils;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Work\Git\cmd;C:\Program Files\Java\jdk1.8.0_202\bin;E:\Bandizip\;D:\apache-maven-3.6.3\bin;D:\Work\nvm;C:\Program Files\nodejs;D:\gradle-2.13\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Work\IntelliJ IDEA Community Edition 2024.3.4.1\bin;C:\Program Files\MySQL\bin;;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Work\IntelliJ IDEA Community Edition 2024.3.4.1\bin;;D:\Work\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;;D:\Work\Microsoft VS Code\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.202-b08"/>
    <property name="java.ext.dirs" value="C:\Program Files\Java\jdk1.8.0_202\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="maven.repo.local" value="D:\apache-maven-3.6.3\.m2"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="nonV1UuidToStringTest" classname="org.thingsboard.server.common.data.UUIDConverterTest" time="0.005"/>
  <testcase name="basicUuidToStringTest" classname="org.thingsboard.server.common.data.UUIDConverterTest" time="0"/>
  <testcase name="basicUuidComperisonTest" classname="org.thingsboard.server.common.data.UUIDConverterTest" time="1.276"/>
  <testcase name="basicStringToUUIDTest" classname="org.thingsboard.server.common.data.UUIDConverterTest" time="0.001"/>
</testsuite>