import { request } from '@/plugins/axios'

/**
 * 查询DMA用水量修正记录
 * @param params
 * @returns
 */
export const GetDMAReadMeterDataRecords = (
  params: IQueryPagerParams & {
    custName?: string
    partitionId?: string
  }
) => {
  return request({
    url: '/api/spp/readMeterData/correct/records',
    method: 'get',
    params
  })
}
/**
 * 导出用水量修正记录
 * @param params
 * @returns
 */
export const ExportDMAReadMeterDataRecords = (params?: any) => {
  return request({
    url: `/api/spp/readMeterData/correct/recordsExport`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 修正DMA用水量
 * @param params
 * @returns
 */
export const CorrectDMAReadMeterData = (params: { id: string; correctWater }) => {
  return request({
    url: '/api/spp/readMeterData/correct',
    method: 'post',
    data: params
  })
}
/**
 * 查询抄表列表
 * @param params
 * @returns
 */
export const GetDMAReadMeterData = (
  params: IQueryPagerParams & {
    custName?: string
    custCode?: string
    partitionId?: string
    start?: string
    end?: string
  }
) => {
  return request({
    url: '/api/spp/readMeterData/list',
    method: 'get',
    params
  })
}
/**
 * 导出用水量列表
 * @param params
 * @returns
 */
export const ExportDMAReadMeterData = (params: any) => {
  return request({
    url: '/api/spp/readMeterData/listExport',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
