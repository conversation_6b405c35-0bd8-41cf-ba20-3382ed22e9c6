package org.thingsboard.server.dao.sql.smartService.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeDocument;

import java.util.List;

@Mapper
public interface KnowledgeDocumentMapper extends BaseMapper<KnowledgeDocument> {

    Page<KnowledgeDocument> getList(IPage<KnowledgeDocument> iPage, @Param("name") String name, @Param("typeIds") List<String> typeIds, @Param("tenantId") String tenantId);
}
