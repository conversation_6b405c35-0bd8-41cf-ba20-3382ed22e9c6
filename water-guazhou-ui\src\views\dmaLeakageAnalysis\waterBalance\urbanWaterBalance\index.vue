<!-- 水量平衡 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <SLCard overlay class="card-table">
      <table class="table">
        <tr>
          <td colspan="9" class="table-title">
            {{ state.dataForm.partitionName }}
            {{ state.dataForm.ym }} 水平衡表（立方米）
          </td>
        </tr>
        <tr>
          <td
            :rowspan="state.isRoot ? 4 : 8"
            style="background: rgb(9 179 205)"
          >
            {{ state.isRoot ? '自产供水量' : '进水量' }} <br />
            {{ state.dataForm.ownSupplyWater ?? '-' }}
          </td>
          <td rowspan="12" style="background: rgb(1 87 155)">
            供水总量 <br />
            {{ state.dataForm.supplyTotalWater ?? '-' }}
          </td>
          <td rowspan="4" style="background: rgb(21 101 192)">
            注册用水量 <br />
            {{ state.dataForm.useTotalWater ?? '-' }}
          </td>

          <td rowspan="2" style="background: rgb(25 118 210)">
            计费用水量 <br />
            {{ state.dataForm.incomeWater ?? '-' }}
          </td>
          <td style="background: rgb(25 118 210)">
            计费计量用水 <br />
            {{ state.dataForm.feeMeteringUseWater ?? '-' }}
          </td>
          <td rowspan="2" style="background: rgb(0 200 83)">
            收益水量 <br />
            {{ state.dataForm.incomeWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(25 118 210)">
            计费未计量用水 <br />
            {{ state.dataForm.feeNoMeteringUseWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="2" style="background: rgb(33 150 243)">
            免费用水量 <br />
            {{
              Number(state.dataForm.freeMeteringUseWater ?? 0) +
                Number(state.dataForm.freeNoMeteringUseWater ?? 0) || '-'
            }}
          </td>
          <td rowspan="1" style="background: rgb(0 200 83)">
            免费计量用水 <br />
            {{ state.dataForm.freeMeteringUseWater ?? '-' }}
          </td>
          <td rowspan="10" style="background: rgb(81 98 120)">
            未收益水量 <br />
            {{ state.dataForm.noIncomeWater ?? '-' }}
          </td>
          <!-- TODO -->
          <td
            rowspan="2"
            colspan="3"
            style="
              background: rgb(0 176 240);
              border-bottom: 1px solid rgb(0 176 240);
            "
          ></td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(0 200 83)">
            免费未计量用水 <br />
            {{ state.dataForm.freeNoMeteringUseWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td
            v-if="state.isRoot"
            rowspan="4"
            style="background: rgb(140, 197, 64)"
          >
            外购供水量 <br />
            {{ state.dataForm.buySupplyWater ?? '-' }}
          </td>
          <td rowspan="8" style="background: rgb(103 128 159)">
            漏损水量 <br />
            {{ state.dataForm.lossTotalWater ?? '-' }}
          </td>
          <td rowspan="4" style="background: rgb(103 128 159)">
            漏失水量 <br />
            {{ state.dataForm.leakTotalWater ?? '-' }}
          </td>
          <td rowspan="1" style="background: rgb(103 128 159)">
            明漏水量 <br />
            {{ state.dataForm.frontLeakTotalWater ?? '-' }}
          </td>
          <td
            rowspan="4"
            style="
              background: rgb(255 230 153);
              border-top: 2px solid #222536;
              border-right: 2px solid #222536;
            "
          >
            漏失率 <br />
            {{ state.dataForm.leakRate ?? '-' }} %
          </td>
          <td
            rowspan="8"
            style="
              background: rgb(146 208 80);
              border-left: 1px solid rgb(146 208 80);
              border-top: 2px solid #222536;
            "
          >
            综合漏损率 <br />
            {{ state.dataForm.lossRate ?? '-' }} %
          </td>
          <td
            rowspan="8"
            style="
              background: rgb(0 176 240);
              border-top: 1px solid rgb(0 176 240);
            "
          >
            产销差率 <br />
            {{ state.dataForm.nrwRate ?? '-' }} %
          </td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(103 128 159)">
            暗漏水量 <br />
            {{ state.dataForm.backendLeakTotalWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(103 128 159)">
            背景水量 <br />
            {{ state.dataForm.backgroundLeakWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(103 128 159)">
            水箱水池渗漏溢流 <br />
            {{ state.dataForm.shuixiangLeakWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="4" style="background: rgb(237, 125, 49)">
            {{ state.isRoot ? '趸售水量' : '出水量' }} <br />
            {{ state.dataForm.batchSaleWater ?? '-' }}
          </td>
          <td rowspan="2" style="background: rgb(116 144 178)">
            计量损失水量 <br />
            {{ state.dataForm.mistakeLossTotalWater ?? '-' }}
          </td>
          <td rowspan="1" style="background: rgb(116 144 178)">
            居民用户表具误差 <br />
            {{ state.dataForm.custMistakeLossWater ?? '-' }}
          </td>
          <td
            rowspan="4"
            style="
              background: rgb(146 208 80);
              border-right: 1px solid rgb(146 208 80);
            "
          ></td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(116 144 178)">
            非居民用户表具误差 <br />
            {{ state.dataForm.nonCustMistakeLossWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="2" style="background: rgb(103 128 159)">
            其他损失水量 <br />
            {{ state.dataForm.otherLossWater ?? '-' }}
          </td>
          <td rowspan="1" style="background: rgb(103 128 159)">
            未注册用水量 <br />
            {{ state.dataForm.noRegisterLossWater ?? '-' }}
          </td>
        </tr>
        <tr>
          <td rowspan="1" style="background: rgb(103 128 159)">
            管理因素导致的损失 <br />
            {{ state.dataForm.pipeLossWater ?? '-' }}
          </td>
        </tr>
      </table>
      <FormTable
        style="margin-top: 20px; padding: 10px"
        :config="TableConfig_Correct"
      ></FormTable>
    </SLCard>

    <DialogForm ref="refDialog" :config="DialogConfig"></DialogForm>
    <DialogForm ref="refDetail" :config="DetailConfig">
      <WaterBalanceDetail
        ref="refDetailTable"
        :partition="TreeData.currentProject"
      ></WaterBalanceDetail>
    </DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { GetWaterBalanceDetail } from '@/api/mapservice/dma/waterBalance';
import { usePartition } from '@/hooks/arcgis';
import { formatterMonth, formatterYear } from '@/utils/GlobalHelper';
import WaterBalanceDetail from './components/WaterBalanceDetail.vue';
import { SLMessage } from '@/utils/Message';

const refSearch = ref<ICardSearchIns>();
const refDetail = ref<IDialogFormIns>();
const refDetailTable = ref<InstanceType<typeof WaterBalanceDetail>>();
const partition = usePartition();
const state = reactive<{
  isRoot: boolean;
  dataForm: {
    id?: any;
    partitionId?: any;
    partitionName?: any;
    uploadName?: any;
    ym?: any;
    ownSupplyWater?: any;
    buySupplyWater?: any;
    batchSaleWater?: any;
    supplyTotalWater?: any;
    feeMeteringUseWater?: any;
    huanweiUseWater?: any;
    lvhuaUseWater?: any;
    pipeUseWater?: any;
    sunhuaiUseWater?: any;
    dingliangUseWater?: any;
    feeNoMeteringUseWater?: any;
    bangongUseWater?: any;
    xiaofangUseWater?: any;
    jianmianUseWater?: any;
    freeMeteringUseWater?: any;
    freeXiaofangUseWater?: any;
    freeWeihuUseWater?: any;
    freeBangongUseWater?: any;
    freeChongxiUseWater?: any;
    freeNoMeteringUseWater?: any;
    useTotalWater?: any;
    frontPointLeakWater?: any;
    frontPipeLeakWater?: any;
    frontLeakTotalWater?: any;
    backendCheckedLeakWater?: any;
    backendNoCheckedWater?: any;
    backendLeakTotalWater?: any;
    backgroundLeakWater?: any;
    shuixiangLeakWater?: any;
    leakTotalWater?: any;
    custMistakeLossWater?: any;
    nonCustMistakeLossWater?: any;
    mistakeLossTotalWater?: any;
    toudaoLossWater?: any;
    copyMeterLossWater?: any;
    otherLossWater?: any;
    noRegisterLossWater?: any;
    pipeLossWater?: any;
    otherLossTotalWater?: any;
    lossTotalWater?: any;
    dn75PipeLength?: any;
    unitSupplyPipeLength?: any;
    yearAvgPressure?: any;
    custCopiedWater?: any;
    maxFrozenSoilDepth?: any;
    createTime?: any;
    creator?: any;
    tenantId?: any;
    incomeWater?: any;
    noIncomeWater?: any;
    leakRate?: any;
    lossRate?: any;
    nrwRate?: any;
  };
}>({
  isRoot: true,
  dataForm: {}
});
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择分区',
  isFilterTree: true,
  expandOnClickNode: false,
  treeNodeHandleClick: (params) => {
    TreeData.currentProject = params;
    refreshData();
  },
  autoFillOptions: () => {
    partition.getTree().then(() => {
      TreeData.data = partition.Tree.value || [];
      TreeData.currentProject = TreeData.data[0];
      refreshData();
    });
  }
});
const handleHidden = (params, query, config) => {
  config.hidden = params.type !== config.field;
};
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'month',
    month: moment().format(formatterMonth),
    year: moment().format(formatterYear)
  },
  filters: [
    {
      type: 'select',
      field: 'type',
      clearable: false,
      options: [
        { label: '按月', value: 'month' },
        { label: '按年', value: 'year' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'month',
      label: '选择月份',
      field: 'month',
      clearable: false
    },
    {
      handleHidden,
      type: 'year',
      label: '选择年份',
      field: 'year',
      clearable: false
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          iconifyIcon: 'ep:search'
        },
        {
          perm: true,
          text: '术语字典',
          type: 'default',
          plain: true,
          click: () => refDialog.value?.openDialog(),
          iconifyIcon: 'ep:info-filled'
        },
        {
          perm: true,
          text: '详情',
          type: 'default',
          plain: true,
          click: () => {
            if (!TreeData.currentProject) {
              SLMessage.warning('请先选择一个分区');
              return;
            }
            refDetail.value?.openDialog();
          },
          iconifyIcon: 'ep:more-filled'
        }
      ]
    }
  ]
});

const TableConfig_Correct = reactive<ITable>({
  height: 'none',
  columns: [
    {
      label: '漏损指标修正值',
      align: 'center',
      prop: 'field1',
      subColumns: [
        { minWidth: 200, label: '类别', prop: 'type' },
        { minWidth: 400, label: '计算公式', prop: 'method' },
        { minWidth: 100, label: '计算值', prop: 'val' }
      ]
    }
  ],
  spanMethod({ rowIndex, columnIndex }) {
    if (columnIndex === 0 || columnIndex === 2) {
      if (rowIndex === 5) return { rowspan: 3, colspan: 1 };
      if (rowIndex === 6) return { rowspan: 0, colspan: 1 };
      if (rowIndex === 7) return { rowspan: 0, colspan: 1 };
    }
    return { rowspan: 1, colspan: 1 };
  },
  dataList: [
    {
      type: '综合漏损率RwL',
      method: '（供水总量-注册用户用水量）/ 供水总量 X 100%'
    },
    { type: '漏损率RBL（修正后，评定值）', method: 'RBL = RwL - Rn' },
    { type: '修正类别', method: '计算公式', val: '修正值' },
    {
      type: '居民抄表到户水量的修正值（R1）',
      method: '0.08X居民抄表到户水量占总供水量比例（%）'
    },
    {
      type: '单位供水量管长的修正值（R2）',
      method: '0.99X（DN75以上管道长度/供水总量-0.0693）X 100%'
    },
    {
      type: '年平均压力修正值（R3）',
      method: '年平均出厂压力大于0.35MPa小于等于0.55MPa时，修正值R3应为0.5%'
    },
    {
      method: '年平均出厂压力大于0.55MPa小于等于0.75MPa时，修正值R3应为1%'
    },
    { method: '年平均出厂压力大于0.75MPa时，修正值R3应为2%' },
    {
      type: '最大冻土深度修正值（R4）',
      method: '最大冻土深度大于1.4m时，修正值应为1%'
    },
    { type: '总修正值（Rn）', method: 'Rn = R1+R2+R3+R4' }
  ],
  pagination: { hide: true }
});
const refDialog = ref<IDialogFormIns>();
const DialogConfig = reactive<IDialogFormConfig>({
  title: '术语字典',
  group: [
    {
      fields: [
        {
          type: 'table',
          config: {
            height: 500,
            columns: [
              { minWidth: 200, label: '术语', prop: 'say' },
              { minWidth: 260, label: '英文名', prop: 'en' },
              { minWidth: 400, label: '术语解释', prop: 'desc' }
            ],
            dataList: [
              {
                say: '供水管网',
                en: 'water distribution system',
                desc: '连接水厂和用户水表（含）之间的管道及其附属设施的总称。'
              },
              {
                say: '自产供水量',
                en: 'self produced water supply',
                desc: '供水单位自有水厂的供水量，可根据流量计设备的水量数据进行统计计算。'
              },
              {
                say: '外购供水量',
                en: 'purchased water supply',
                desc: '供水单位向其他单位购买并输入到管网的供水量，通常情况下从邻近水务公司购买的水。'
              },
              {
                say: '趸售水量',
                en: 'wholesale water supply',
                desc: '供水企业对暂未实现抄表到户的转供单位或物业管理小区的供水。'
              },
              {
                say: '供水管网',
                en: 'water distribution system',
                desc: '连接水厂和用户水表（含）之间的管道及其附属设施的总称。'
              },
              {
                say: '供水总量',
                en: 'system input quantity',
                desc: '进入供水管网中的全部水量之和，包括自产供水量和外购供水量。'
              },
              {
                say: '注册用户用水量',
                en: 'authorized consumption',
                desc: '在供水单位登记注册的用户的计费用水量和免费用水量。'
              },
              {
                say: '计费用水量',
                en: 'billed authorized consumption',
                desc: '在供水单位注册的计费用户的用水量。'
              },
              {
                say: '免费用水量',
                en: 'unbilled authorized consumption',
                desc: '按规定减免收费的注册用户的用水量和用于管网维护和冲洗等的水量。'
              },
              {
                say: '漏损水量',
                en: 'water losses',
                desc: '供水总量和注册用户用水量之间的差值、有漏失水量、计量损失水量和其他随时水量组成。'
              },
              {
                say: '漏失水量',
                en: 'reallosses',
                desc: '各种类型的管线漏点、管网中水箱及水池等渗漏和溢流造成实际漏掉的水量'
              },
              {
                say: '明漏水量',
                en: 'reported leakage',
                desc: '水溢出地面或可见的管网漏点的漏失水量'
              },
              {
                say: '暗漏水量',
                en: 'unreported leakage',
                desc: '指在地面下检测到的管网漏点的漏失水量。'
              },
              {
                say: '背景漏失水量',
                en: 'background leakage',
                desc: '现有技术手段和措施未能检测到的管网漏点的漏失水量。'
              },
              {
                say: '计量损失水量',
                en: 'metering losses',
                desc: '计量表具性能限值或计量方式改变导致计量误差的损失水量。'
              },
              {
                say: '其他损失水量',
                en: 'other losses',
                desc: '未注册用户用水和用户据查等管理因素导致的损失水量。'
              },
              {
                say: '区域管理',
                en: 'zone management',
                desc: '将供水管网划分为若干供水区域，对每个供水区域的水量，水压进行监测控制，实现漏损量化管理的方式。'
              },
              {
                say: '独立计量区',
                en: 'district metered area',
                desc: '将供水管网分割成单独十量的供水区域，规模一般小于区域管理的范围。'
              },
              {
                say: '夜间最小流量',
                en: 'minimum night flow',
                desc: '独立计量区每日夜间用户用水量最小时的进水流量。'
              },
              {
                say: '计量仪表误差',
                en: 'measuring instrument error',
                desc: '由于水厂出厂流量计的误差造成的水量误差。该误差也包括部分出水管道缺少流量计造成的流量减少情况。'
              },
              {
                say: '水箱、水池渗漏溢流水量',
                en: 'leakage and overflow of water tank',
                desc: '由于管网中各类水箱、水池的结构产生裂缝、以及水箱进行水控制阀门失效造成水箱溢流的水量损失。'
              },
              {
                say: '居民用户表具误差',
                en: 'meter error of residential users',
                desc: '由于楼门总表和对应户表的计量水量总会存在一定误差，由于居民用户总表计量水量和分表计量总水量之间的差值产生的水量损失。'
              },
              {
                say: '非居民用户表具误差',
                en: 'not meter error of residential users',
                desc: '非居民用户表具本身存在误差造成的用水量不准或估值，由于表具计量数据的误差导致的非居民用户用水量的损失。'
              },
              {
                say: '未注册用户用水和管理因素导致的损失',
                en: 'not register water and message water',
                desc: '由于非法用水、偷盗水、用户拒查、水表漏立方等造成的损失水量。'
              },
              {
                say: '收益水量',
                en: 'revenue water volume',
                desc: '向用户收取水费使自来水公司获得收益的水量。'
              },
              {
                say: '无收益水量',
                en: 'not revenue water volume',
                desc: '没有为水司带来收益的水量。'
              },
              {
                say: '零压测试',
                en: 'zero-pressure test',
                desc: '为判断独立计量区是否封闭，关闭边界阀门后放水，监测区域内压力是否下降至零。'
              },
              {
                say: '漏损率',
                en: 'water loss rate',
                desc: '管网漏损水量与供水总量之比，通常用百分比表示。'
              },
              {
                say: '漏失率',
                en: 'real loss rate',
                desc: '管网漏失水量与供水量总量之比，通常用百分比表示。'
              },
              {
                say: '产销差率',
                en: 'production and sales difference rate',
                desc: '供水产销差水量与供水总量之比。'
              },
              {
                say: '基本漏损率',
                en: 'benchmark water loss rate',
                desc: '漏损率评定标准修正前的基准控制值。'
              },
              {
                say: '单位供水量管长',
                en: 'pipe length per unit water supply',
                desc: '管径大于等于75mm的管道总长与供水总量之比。'
              },
              {
                say: '水表量程比',
                en: 'tumdown rate',
                desc: '水表常用流量和最小流量的比值。'
              },
              {
                say: '最大冻土深度',
                en: 'maximum frozen soil depth',
                desc: '历年冻土深度最大值中的最大值。'
              }
            ],
            pagination: { hide: true }
          }
        }
      ]
    }
  ]
});
const DetailConfig = reactive<IDialogFormConfig>({
  title: '详情',
  group: [],
  dialogWidth: '80%'
  // submitText: '导出',
  // submit: () => {
  //   refDetailTable.value?.refTable?.exportTable()
  // }
});

const refreshData = async () => {
  const { type, month, year } = refSearch.value?.queryParams || {};
  const date = type === 'month' ? month : type === 'year' ? year : undefined;
  if (!TreeData.currentProject || !date) return;
  state.isRoot = TreeData.currentProject?.path?.length === 1;
  const res = await GetWaterBalanceDetail({
    partitionId: TreeData.currentProject?.value,
    ym: date
  });
  state.dataForm = res.data?.data || {};
  state.dataForm.partitionName = TreeData.currentProject?.label;
};
</script>

<style lang="scss" scoped>
.card {
  height: 100%;
}

.table {
  width: 100%;
  padding: 10px;
  text-align: center;
  border: 0 solid #222536;
  color: #fff;
  border-spacing: 0;
  .table-title {
    color: var(--el-text-color-primary);
  }

  tr {
    height: 55px;
  }

  td {
    min-height: 80px;
    border: 0.5px solid #222536;
  }
}

:deep(.sl-card-content) {
  overflow-y: auto;
}
</style>
