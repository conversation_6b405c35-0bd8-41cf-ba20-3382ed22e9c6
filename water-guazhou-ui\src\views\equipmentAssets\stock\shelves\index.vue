<!-- 货架管理 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'

import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { getGoodsShelfSerch, patchGoodsShelf, postGoodsShelf, deleteGoodsShelf } from '@/api/equipment_assets/equipmentOutStock'
import { objectFlattening } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<IDialogFormIns>()

const temporary = ref('')

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '所属仓库编码', field: 'code', type: 'input', labelWidth: '120px' },
    { label: '所属仓库名称', field: 'name', type: 'input', labelWidth: '120px' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '仓库名称', prop: 'name' },
    { label: '排序', prop: 'orderNum' },
    { label: '备注', prop: 'remark' },
    { label: '编号', prop: 'code' }
  ],
  operationWidth: '240px',
  operations: [
    {
      type: 'success',
      isTextBtn: true,
      color: '#4195f0',
      text: '新增子集',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => clickCreatedRole(row)
    },
    {
      hide: row => row.layer === 1,
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => clickEdit(row)
    },
    {
      hide: row => row.layer === 1,
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      icon: 'iconfont icon-shanchu',
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '',
  labelWidth: '100px',
  dialogWidth: '500px',
  submit: (params: any) => {
    if (codeCheck(params)) return
    if (params.id) {
      patchGoodsShelf(params.id, params).then(() => {
        ElMessage.success('修改成功')
        refreshData()
        refForm.value?.closeDialog()
      })
    } else {
      postGoodsShelf(params).then(() => {
        ElMessage.success('添加成功')
        refreshData()
        refForm.value?.closeDialog()
      })
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input-number',
          label: '编码',
          field: 'code',
          rules: [{ required: true, message: '请输入编码' }]
        },

        {
          type: 'input',
          label: '名称',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },
        {
          type: 'input-number',
          label: '排序',
          field: 'orderNum'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }
      ]
    }
  ]
})

// 类别编码校验
function codeCheck(params) {
  if (params.code === temporary.value) { return false }
  const value = objectFlattening(TableConfig.dataList || [], 'children', 'code')
  if (value.includes(params.code)) {
    ElMessage.warning('编码重复')
    return true
  }
}

const clickCreatedRole = row => {
  addOrUpdateConfig.title = '新增'
  addOrUpdateConfig.defaultValue = { parentId: row.id }
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  temporary.value = row.code
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDialog()
}

const haneleDelete = (row: { id: string }) => {
  SLConfirm('确定删除该货架, 是否继续?', '删除提示').then(() => {
    deleteGoodsShelf(row.id).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    }).catch(error => {
      ElMessage.warning(error)
    })
  })
}

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  getGoodsShelfSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>

<style scoped>
.card-table :deep(.el-table__expand-icon--expanded) {
  width: 6px;
}
</style>
