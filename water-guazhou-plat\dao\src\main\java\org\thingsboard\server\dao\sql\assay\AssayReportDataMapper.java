package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.AssayReportRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayReportData;

@Mapper
public interface AssayReportDataMapper extends BaseMapper<AssayReportData> {
    IPage<AssayReportData> findList(Page<AssayReportData> pageRequest, @Param("param") AssayReportRequest request);
}
