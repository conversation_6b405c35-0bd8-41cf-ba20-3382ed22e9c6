<template>
  <div class="weather">
    <img v-if="props.logo" :src="props.logo" alt="" class="weather-item logo" />
    <i class="weather-item" :class="state.icon"></i>
    <span class="weather-item text">{{ state.text }}</span>
    <span class="weather-item">{{ state.temp }}℃</span>
  </div>
</template>
<script lang="ts" setup>
import axios from 'axios'
const props = defineProps<{ logo?: string }>();
const state = reactive<{
  icon: string;
  text: string;
  temp: string;
}>({
  icon: 'qi-301',
  text: '多云',
  temp: '24'
});
// 通过和风天气接口查询当前城市id
const getCurrentLocate = async () => {
  try {
    const location = window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter.join(',');
    const cityUrl = `https://geoapi.qweather.com/v2/city/lookup?key=${window.SITE_CONFIG.WEATHER_CONFIG.KEY}&location=${location}&lang=zh`;
    const res = await axios.get(cityUrl);
    return res.data?.location?.[0]?.id;
  } catch (error) {
    console.log('获取当前城市信息失败');
  }
};
// 通过和风天气接口查询当前城市天气
const getRealTimeWeather = async () => {
  const city = await getCurrentLocate();
  if (!city) return;
  try {
    const weatherApi = `https://devapi.qweather.com/v7/weather/now?key=${window.SITE_CONFIG.WEATHER_CONFIG.KEY}&location=${city}`;
    const res = await axios.get(weatherApi);
    const now = res.data.now || {};
    state.icon = 'qi-' + now.icon;
    state.text = now.text;
    state.temp = now.temp;
  } catch (error) {
    console.log('获取当前城市天气信息失败');
  }
};
onMounted(() => {
  getRealTimeWeather();
});
</script>
<style lang="scss" scoped>
// 引入和风天气图标
@import 'qweather-icons/font/qweather-icons.css';
.weather {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 25px;

  color: #ffffff;
  display: flex;
  align-items: center;
  .logo {
    height: 25px;
  }
  .weather-item {
    margin-right: 8px;
  }
  .text {
    font-weight: 400;
    color: #b8d2ff;
    margin-right: 12px;
  }
}
</style>
