package org.thingsboard.server.dao.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainTaskMMapper extends BaseMapper<MaintainTaskM> {

    List<MaintainTaskM> getList(@Param("code") String code, @Param("name") String name, @Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("userId") String userId, @Param("status") String status, @Param("auditStatus") String auditStatus, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("code") String code, @Param("name") String name, @Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("userId") String userId, @Param("status") String status, @Param("auditStatus") String auditStatus, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("tenantId") String tenantId);


    List statistics(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("tenantId") String tenantId);

    MaintainTaskM getById(@Param("id") String mainId);

    Integer getNotCompleteNum(@Param("userId") String userId, @Param("tenantId") String tenantId);

    int coutDaijieshou(@Param("userId") String userId);
}
