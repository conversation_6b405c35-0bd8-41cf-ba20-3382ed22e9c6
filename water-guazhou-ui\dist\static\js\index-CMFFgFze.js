import{d as J,a0 as $,c as u,r as U,a8 as k,s as S,o as K,g as v,n as L,q as t,p as a,F as c,aB as X,aJ as Y,bh as s,av as Z,aw as N,h as f,i as b,cw as x,cC as B,G as q,cE as ee,c6 as ae,N as te,J as le,O as ne,al as oe,b7 as se,C as de}from"./index-r0dFAfgr.js";import{_ as re}from"./index-C9hz-UZb.js";import{_ as ce}from"./CardSearch-CB_HNR-Q.js";import{W as ie}from"./index-EuPVwsd1.js";import{c as ue}from"./onemap-CEunQziB.js";import"./Search-NSrhrIa_.js";const me={class:"water-quality-monitoring"},pe={class:"main-content"},ve={class:"left-panel"},_e={class:"indicator-list"},he={class:"indicator-name"},ge={class:"indicator-bar"},fe={class:"bar-container"},be={class:"bar-labels"},we={class:"current-value"},ye={class:"cod-content"},Ce={class:"cod-value"},Ie={class:"value"},Me={class:"unit"},Te={class:"cod-status"},Le={class:"status-text"},Ne={class:"cod-standard"},De={class:"standard-value"},Ve={class:"ph-content"},ke={class:"ph-value"},Se={class:"value"},xe={class:"ph-status"},Be={class:"status-text"},qe={class:"ph-standard"},Pe={class:"standard-value"},Ee={class:"right-panel"},Re={class:"table-container"},Oe=J({__name:"index",setup(Ae){const P=$(),w=u(!1),g=u(!1),y=u(null),C=u(),E=U({defaultParams:{plantId:"",stationId:"",dateRange:[new Date().toISOString().split("T")[0]+" 00:00:00",new Date().toISOString().split("T")[0]+" 23:59:59"]},filters:[{type:"select",label:"污水厂",field:"plantId",placeholder:"全部",options:k(()=>[{label:"全部",value:""},...h.value.map(e=>({label:e.name,value:e.id}))]),onChange:()=>{j()}},{type:"select",label:"采样点位",field:"stationId",placeholder:"全部",options:k(()=>[{label:"全部",value:""},...R.value.map(e=>({label:e.name,value:e.id}))])},{type:"datetimerange",label:"监测时间",field:"dateRange"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:S(oe),click:()=>F()},{perm:!0,text:"重置",type:"default",svgIcon:S(se),click:()=>G()}]}]}),h=u([]),R=u([{id:"1",name:"进水口"},{id:"2",name:"出水口"},{id:"3",name:"中间处理池"}]),O=u([{code:"cod",name:"COD",currentValue:"25.8",unit:"mg/L",percentage:52,level:"normal"},{code:"bod",name:"BOD",currentValue:"12.5",unit:"mg/L",percentage:42,level:"good"},{code:"ss",name:"悬浮物",currentValue:"18.2",unit:"mg/L",percentage:61,level:"normal"},{code:"nh3n",name:"氨氮",currentValue:"2.1",unit:"mg/L",percentage:35,level:"good"},{code:"tn",name:"总氮",currentValue:"8.5",unit:"mg/L",percentage:57,level:"normal"},{code:"tp",name:"总磷",currentValue:"0.8",unit:"mg/L",percentage:80,level:"warning"},{code:"ph",name:"pH值",currentValue:"7.2",unit:"",percentage:45,level:"good"},{code:"fecal",name:"粪大肠菌群",currentValue:"2500",unit:"个/L",percentage:25,level:"good"}]),p=u({value:"45.2",unit:"mg/L",trend:3.8,standard:"GB 18918-2020 一级A标准：50mg/L"}),_=u({value:"9",trend:1,standard:"GB 8978-1996 pH控制：6~9"}),D=u([]),A=e=>({good:"#52c41a",normal:"#faad14",warning:"#ff4d4f",danger:"#a8071a"})[e]||"#d9d9d9",z=e=>({I类:"quality-level-1",II类:"quality-level-2",III类:"quality-level-3",IV类:"quality-level-4",V类:"quality-level-5"})[e]||"quality-level-unknown",F=()=>{var l;const e=((l=C.value)==null?void 0:l.queryParams)||{};console.log("执行查询:",e),I()},G=()=>{var e;(e=C.value)==null||e.resetForm(),I()},H=e=>{console.log("查看检测报告:",e),y.value={stationName:e.stationName,sampleTime:e.sampleTime,waterQualityLevel:e.level,indicators:{outflow:Number(e.outflow)||75+Math.random()*35,inflow:Number(e.inflow)||80+Math.random()*40,cod:Number(e.cod)||15+Math.random()*20,ph:Number(e.ph)||6.5+Math.random()*2,bod5:5+Math.random()*15,ammoniaNitrogen:.2+Math.random()*1.5,totalNitrogen:.3+Math.random()*1.5,totalPhosphorus:.05+Math.random()*.3,fecalColiform:500+Math.random()*15e3}},g.value=!0},Q=()=>{g.value=!1,y.value=null},W=async()=>{var e,l;try{const m=await ue({projectId:(e=P.selectedProject)==null?void 0:e.value}),d=((l=m.data)==null?void 0:l.data)||m.data||[];if(Array.isArray(d)&&d.length>0){const i=d.filter(n=>n.stationType==="污水处理厂"||n.type==="污水处理厂"||n.name&&n.name.includes("污水"));h.value=i.map(n=>({id:n.stationId||n.id,name:n.name||n.stationName}))}}catch(m){console.error("加载污水厂列表失败:",m),h.value=[{id:"1",name:"污水处理厂A"},{id:"2",name:"污水处理厂B"},{id:"3",name:"污水处理厂C"}]}},j=()=>{},I=()=>{w.value=!0,setTimeout(()=>{const e=h.value.length>0?h.value.map(i=>i.name):["污水处理厂A","污水处理厂B","污水处理厂C"],l=["进水口","出水口","中间处理池"],m=["II类","III类","IV类"],d=[];e.forEach((i,n)=>{const r=l[Math.floor(Math.random()*l.length)],M=m[Math.floor(Math.random()*m.length)],T=new Date,o=Math.floor(Math.random()*60),V=new Date(T.getTime()-o*60*1e3);d.push({id:`station-${n+1}`,level:M,plantName:i,stationName:r,sampleTime:V.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-"),waterTemp:Math.floor(Math.random()*10)+15,cod:Math.floor(Math.random()*50)+20,ph:Number((Math.random()*2+6.5).toFixed(1)),inflow:Math.floor(Math.random()*50)+100,outflow:Math.floor(Math.random()*40)+95})}),d.sort((i,n)=>new Date(n.sampleTime).getTime()-new Date(i.sampleTime).getTime()),D.value=d,w.value=!1},500)};return K(async()=>{await W(),I()}),(e,l)=>{const m=ce,d=re,i=ee,n=ae,r=te,M=le,T=ne;return v(),L("div",me,[t(m,{ref_key:"refCardSearch",ref:C,config:E},null,8,["config"]),a("div",pe,[a("div",ve,[t(d,{title:"本日水质均值",class:"indicator-card"},{default:c(()=>[a("div",_e,[(v(!0),L(X,null,Y(O.value,o=>(v(),L("div",{key:o.code,class:"indicator-item"},[a("div",he,s(o.name),1),a("div",ge,[a("div",fe,[a("div",{class:"bar-fill",style:Z({width:o.percentage+"%",backgroundColor:A(o.level)})},null,4),a("div",be,[a("span",we,s(o.currentValue)+s(o.unit),1)])])])]))),128))])]),_:1}),t(d,{title:"出水COD均值",class:"cod-card"},{default:c(()=>[a("div",ye,[a("div",Ce,[a("span",Ie,s(p.value.value),1),a("span",Me,s(p.value.unit),1)]),a("div",Te,[a("span",Le,"较昨日"+s(p.value.trend>0?"+":"")+s(p.value.trend)+s(p.value.unit),1),t(i,{class:N(["trend-icon",p.value.trend>0?"trend-up":"trend-down"])},{default:c(()=>[p.value.trend>0?(v(),f(b(x),{key:0})):(v(),f(b(B),{key:1}))]),_:1},8,["class"])]),a("div",Ne,[l[1]||(l[1]=a("div",{class:"standard-text"},"国标限值：",-1)),a("div",De,s(p.value.standard),1)])])]),_:1}),t(d,{title:"出水PH均值",class:"ph-card"},{default:c(()=>[a("div",Ve,[a("div",ke,[a("span",Se,s(_.value.value),1)]),a("div",xe,[a("span",Be,"较昨日"+s(_.value.trend>0?"+":"")+s(_.value.trend),1),t(i,{class:N(["trend-icon",_.value.trend>0?"trend-up":"trend-down"])},{default:c(()=>[_.value.trend>0?(v(),f(b(x),{key:0})):(v(),f(b(B),{key:1}))]),_:1},8,["class"])]),a("div",qe,[l[2]||(l[2]=a("div",{class:"standard-text"},"国标限值：",-1)),a("div",Pe,s(_.value.standard),1)])])]),_:1})]),a("div",Ee,[t(d,{title:"",class:"table-card"},{default:c(()=>[a("div",Re,[t(T,{data:D.value,loading:w.value,style:{width:"100%"},border:"",stripe:""},{default:c(()=>[t(r,{prop:"level",label:"水质等级",width:"100",align:"center"},{default:c(({row:o})=>[t(n,{class:N(z(o.level))},{default:c(()=>[q(s(o.level),1)]),_:2},1032,["class"])]),_:1}),t(r,{prop:"plantName",label:"污水厂名称","min-width":"120"}),t(r,{prop:"stationName",label:"采样点位","min-width":"100"}),t(r,{prop:"sampleTime",label:"采样时间","min-width":"150"}),t(r,{prop:"waterTemp",label:"水温°C",width:"80",align:"center"}),t(r,{prop:"cod",label:"出水COD mg/L",width:"120",align:"center"}),t(r,{prop:"ph",label:"出水PH值",width:"100",align:"center"}),t(r,{prop:"inflow",label:"进水流量 m³/h",width:"120",align:"center"}),t(r,{prop:"outflow",label:"出水流量 m³/h",width:"120",align:"center"}),t(r,{label:"检测报告",width:"80",align:"center",fixed:"right"},{default:c(({row:o})=>[t(M,{type:"primary",link:"",size:"small",onClick:V=>H(o)},{default:c(()=>l[3]||(l[3]=[q(" 查看 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","loading"])])]),_:1})])]),t(ie,{modelValue:g.value,"onUpdate:modelValue":l[0]||(l[0]=o=>g.value=o),data:y.value,onClose:Q},null,8,["modelValue","data"])])}}}),je=de(Oe,[["__scopeId","data-v-2544110e"]]);export{je as default};
