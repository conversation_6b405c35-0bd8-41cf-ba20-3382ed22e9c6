import{p as y,H as x,m as k,h as t,t as d,r as m,e as z}from"./widget-BcWKanF2.js";import{c as H,d as S}from"./conditionalSlot-BBJHErPk.js";import{g as I}from"./guid-DO7TRjsS.js";import{u as M}from"./interactive-crkFkZAr.js";import{u as $,c as B,a as A,d as T,b as O,s as j}from"./t9n-B2bWcUZc.js";import{H as q,d as K,a as U}from"./action-menu-i7jt7xb8.js";import{d as F}from"./action-CK67UTEO.js";import{s as W,a as R,c as G}from"./loadable-DZS8sRBo.js";import{d as w}from"./icon-vUORPQEt.js";import{d as J}from"./loader-DYvscnHN.js";import{d as P}from"./scrim-Eo5BG2Ie.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./observers-D10wq1Ib.js";import"./key-7hamXU9f.js";import"./openCloseComponent-aiDFLC5b.js";import"./debounce-x6ZvqDEC.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const b={handle:"handle",handleActivated:"handle--activated"},Q={drag:"drag"},V="@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{display:flex}.handle{display:flex;cursor:move;align-items:center;justify-content:center;align-self:stretch;border-style:none;background-color:transparent;outline-color:transparent;color:var(--calcite-ui-border-input);padding-block:0.75rem;padding-inline:0.25rem;line-height:0}.handle:hover{background-color:var(--calcite-ui-foreground-2);color:var(--calcite-ui-text-1)}.handle:focus{color:var(--calcite-ui-text-1);outline:2px solid var(--calcite-ui-brand);outline-offset:-2px}.handle--activated{background-color:var(--calcite-ui-foreground-3);color:var(--calcite-ui-text-1)}.handle calcite-icon{color:inherit}",X=y(class extends x{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.calciteHandleNudge=k(this,"calciteHandleNudge",6),this.handleKeyDown=i=>{switch(i.key){case" ":this.activated=!this.activated,i.preventDefault();break;case"ArrowUp":if(!this.activated)return;i.preventDefault(),this.calciteHandleNudge.emit({direction:"up"});break;case"ArrowDown":if(!this.activated)return;i.preventDefault(),this.calciteHandleNudge.emit({direction:"down"});break}},this.handleBlur=()=>{this.activated=!1},this.activated=!1,this.textTitle="handle"}componentWillLoad(){W(this)}componentDidLoad(){R(this)}async setFocus(){var i;await G(this),(i=this.handleButton)==null||i.focus()}render(){return t("span",{"aria-pressed":d(this.activated),class:{[b.handle]:!0,[b.handleActivated]:this.activated},onBlur:this.handleBlur,onKeyDown:this.handleKeyDown,ref:i=>{this.handleButton=i},role:"button",tabindex:"0",title:this.textTitle},t("calcite-icon",{icon:Q.drag,scale:"s"}))}get el(){return this}static get style(){return V}},[1,"calcite-handle",{activated:[1540],textTitle:[513,"text-title"],setFocus:[64]}]);function C(){if(typeof customElements>"u")return;["calcite-handle","calcite-icon"].forEach(e=>{switch(e){case"calcite-handle":customElements.get(e)||customElements.define(e,X);break;case"calcite-icon":customElements.get(e)||w();break}})}C();/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const a={container:"container",content:"content",headerContainer:"header-container",icon:"icon",statusIcon:"status-icon",toggle:"toggle",toggleIcon:"toggle-icon",title:"title",heading:"heading",header:"header",button:"button",summary:"summary",description:"description",controlContainer:"control-container",valid:"valid",invalid:"invalid",loading:"loading"},r={icon:"icon",control:"control",headerMenuActions:"header-menu-actions"},s={opened:"chevron-up",closed:"chevron-down",valid:"check-circle",invalid:"exclamation-mark-triangle",refresh:"refresh"},Y='@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:host{box-sizing:border-box;background-color:var(--calcite-ui-foreground-1);color:var(--calcite-ui-text-2);font-size:var(--calcite-font-size--1)}:host *{box-sizing:border-box}:host{--calcite-icon-size:1rem;--calcite-spacing-eighth:0.125rem;--calcite-spacing-quarter:0.25rem;--calcite-spacing-half:0.5rem;--calcite-spacing-three-quarters:0.75rem;--calcite-spacing:1rem;--calcite-spacing-plus-quarter:1.25rem;--calcite-spacing-plus-half:1.5rem;--calcite-spacing-double:2rem;--calcite-menu-min-width:10rem;--calcite-header-min-height:3rem;--calcite-footer-min-height:3rem}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host([disabled]){pointer-events:none;cursor:default;-webkit-user-select:none;user-select:none;opacity:var(--calcite-ui-opacity-disabled)}:host{display:flex;flex-shrink:0;flex-grow:0;flex-direction:column;border-width:0px;border-block-end-width:1px;border-style:solid;border-color:var(--calcite-ui-border-3);padding:0px;transition-property:margin;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;transition-timing-function:cubic-bezier(0.215, 0.440, 0.420, 0.880);flex-basis:auto}:host([disabled]) ::slotted([calcite-hydrated][disabled]),:host([disabled]) [calcite-hydrated][disabled]{opacity:1}.header{margin:0px;display:flex;align-content:space-between;align-items:center;fill:var(--calcite-ui-text-2);color:var(--calcite-ui-text-2)}.heading{margin:0px;padding:0px;font-weight:var(--calcite-font-weight-medium)}.header .heading{flex:1 1 auto;padding:0.5rem}.header{justify-content:flex-start;padding:0px}.header,.toggle{grid-area:header}.header-container{display:grid;align-items:stretch;grid-template:auto/auto 1fr auto auto;grid-template-areas:"handle header control menu";grid-column:header-start/menu-end;grid-row:1/2}.toggle{margin:0px;display:flex;cursor:pointer;flex-wrap:nowrap;align-items:center;justify-content:space-between;border-style:none;padding:0px;font-family:inherit;outline-color:transparent;text-align:initial;background-color:transparent}.toggle:hover{background-color:var(--calcite-ui-foreground-2)}.toggle:focus{outline:2px solid var(--calcite-ui-brand);outline-offset:-2px}calcite-loader[inline]{grid-area:control;align-self:center}calcite-handle{grid-area:handle}.title{margin:0px;padding:0.75rem}.header .title .heading{padding:0px;font-size:var(--calcite-font-size--1);font-weight:var(--calcite-font-weight-medium);line-height:1.25;color:var(--calcite-ui-text-2);transition-property:color;transition-duration:150ms;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);word-wrap:break-word;word-break:break-word}.description{margin-block-start:0.125rem;padding:0px;font-size:var(--calcite-font-size--2);color:var(--calcite-ui-text-3);word-wrap:break-word;word-break:break-word}.icon{margin-inline-start:0.75rem;margin-inline-end:0px;margin-block:0.75rem}.status-icon.valid{color:var(--calcite-ui-success)}.status-icon.invalid{color:var(--calcite-ui-danger)}.status-icon.loading{animation:spin calc(var(--calcite-internal-animation-timing-slow) * 2) linear infinite}@keyframes spin{0%{transform:rotate(0deg)}50%{transform:rotate(180deg)}100%{transform:rotate(360deg)}}.toggle-icon{margin-block:0.75rem;align-self:center;justify-self:end;color:var(--calcite-ui-text-3);transition-property:color;transition-duration:150ms;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);margin-inline-end:1rem;margin-inline-start:auto}.toggle:hover .toggle-icon{color:var(--calcite-ui-text-1)}.container{position:relative;display:flex;block-size:100%;flex-direction:column}.content{position:relative;flex:1 1 0%}@keyframes in{0%{opacity:0}100%{opacity:1}}.content{animation:in var(--calcite-internal-animation-timing-slow) ease-in-out;padding-block:var(--calcite-block-padding, 0.5rem);padding-inline:var(--calcite-block-padding, 0.625rem)}.control-container{margin:0px;display:flex;grid-area:control}calcite-action-menu{grid-area:menu}:host([open]){margin-block:0.5rem}:host([open]) .header .title .heading{color:var(--calcite-ui-text-1)}',E=y(class extends x{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.calciteBlockToggle=k(this,"calciteBlockToggle",6),this.guid=I(),this.onHeaderClick=()=>{this.open=!this.open,this.calciteBlockToggle.emit()},this.collapsible=!1,this.disabled=!1,this.dragHandle=!1,this.heading=void 0,this.headingLevel=void 0,this.loading=!1,this.open=!1,this.status=void 0,this.description=void 0,this.messages=void 0,this.messageOverrides=void 0,this.effectiveLocale=void 0,this.defaultMessages=void 0}onMessagesChange(){}effectiveLocaleChange(){$(this,this.effectiveLocale)}connectedCallback(){H(this),B(this),A(this)}disconnectedCallback(){T(this),O(this),S(this)}componentDidRender(){M(this)}async componentWillLoad(){await j(this)}renderScrim(){const{loading:i}=this,e=t("slot",null);return[i?t("calcite-scrim",{loading:i}):null,e]}renderIcon(){const{el:i,status:e}=this,n=this.loading&&!this.open,o=n?s.refresh:s[e],c=m(i,r.icon)||o,l=o?t("calcite-icon",{class:{[a.statusIcon]:!0,[a.valid]:e=="valid",[a.invalid]:e=="invalid",[a.loading]:n},icon:o,scale:"m"}):t("slot",{key:"icon-slot",name:r.icon});return c?t("div",{class:a.icon},l):null}renderTitle(){const{heading:i,headingLevel:e,description:n}=this;return i||n?t("div",{class:a.title},t(q,{class:a.heading,level:e},i),n?t("div",{class:a.description},n):null):null}render(){const{collapsible:i,el:e,loading:n,open:o,messages:c}=this,l=o?c.collapse:c.expand,u=t("header",{class:a.header},this.renderIcon(),this.renderTitle()),h=!!m(e,r.control),g=!!m(e,r.headerMenuActions),D=o?s.opened:s.closed,{guid:p}=this,f=`${p}-region`,v=`${p}-button`,L=t("div",{class:a.headerContainer},this.dragHandle?t("calcite-handle",null):null,i?t("button",{"aria-controls":f,"aria-expanded":i?d(o):null,"aria-label":l,class:a.toggle,id:v,onClick:this.onHeaderClick,title:l},u,!h&&!g?t("calcite-icon",{"aria-hidden":"true",class:a.toggleIcon,icon:D,scale:"s"}):null):u,n?t("calcite-loader",{inline:!0,label:c.loading}):h?t("div",{class:a.controlContainer},t("slot",{name:r.control})):null,g?t("calcite-action-menu",{label:c.options},t("slot",{name:r.headerMenuActions})):null);return t(z,null,t("article",{"aria-busy":d(n),class:{[a.container]:!0}},L,t("section",{"aria-expanded":d(o),"aria-labelledby":v,class:a.content,hidden:!o,id:f},this.renderScrim())))}static get assetsDirs(){return["assets"]}get el(){return this}static get watchers(){return{messageOverrides:["onMessagesChange"],effectiveLocale:["effectiveLocaleChange"]}}static get style(){return Y}},[1,"calcite-block",{collapsible:[516],disabled:[516],dragHandle:[516,"drag-handle"],heading:[1],headingLevel:[514,"heading-level"],loading:[516],open:[1540],status:[513],description:[1],messages:[1040],messageOverrides:[1040],effectiveLocale:[32],defaultMessages:[32]}]);function _(){if(typeof customElements>"u")return;["calcite-block","calcite-action","calcite-action-menu","calcite-handle","calcite-icon","calcite-loader","calcite-popover","calcite-scrim"].forEach(e=>{switch(e){case"calcite-block":customElements.get(e)||customElements.define(e,E);break;case"calcite-action":customElements.get(e)||F();break;case"calcite-action-menu":customElements.get(e)||U();break;case"calcite-handle":customElements.get(e)||C();break;case"calcite-icon":customElements.get(e)||w();break;case"calcite-loader":customElements.get(e)||J();break;case"calcite-popover":customElements.get(e)||K();break;case"calcite-scrim":customElements.get(e)||P();break}})}_();const fe=E,ve=_;export{fe as CalciteBlock,ve as defineCustomElement};
