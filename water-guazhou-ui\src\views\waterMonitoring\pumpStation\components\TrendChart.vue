<template>
  <div class="chart-container">
    <div class="chart-title">泵站能耗: KWh/t</div>
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chart) return
  
  const data = props.chartData
  if (!data || data.length === 0) return
  
  // 提取时间点
  const timePoints = data[0].trends.map(item => item.time)
  
  // 准备系列数据
  const series = data.map(station => {
    return {
      name: station.stationName,
      type: 'line',
      data: station.trends.map(item => item.value),
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2
      }
    }
  })
  
  // 设置颜色
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
  
  const option = {
    color: colors,
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: data.map(station => station.stationName),
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timePoints
    },
    yAxis: {
      type: 'value',
      name: 'KWh/t'
    },
    series: series
  }
  
  chart.setOption(option)
}

watch(() => props.chartData, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
  
  window.addEventListener('resize', () => {
    chart && chart.resize()
  })
})
</script>

<style scoped lang="scss">
.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .chart-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .chart {
    flex: 1;
  }
}
</style>
