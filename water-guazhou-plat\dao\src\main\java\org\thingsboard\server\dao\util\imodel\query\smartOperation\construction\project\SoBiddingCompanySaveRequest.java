package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoBiddingCompanySaveRequest extends SaveRequest<SoBiddingCompany> {
    // 公司名称
    @NotNullOrEmpty
    private String name;

    // 所属招投标id
    private String biddingId;

    // 联系人
    @NotNullOrEmpty
    private String contactUser;

    // 联系电话
    @NotNullOrEmpty
    private String phone;

    @Override
    protected SoBiddingCompany build() {
        SoBiddingCompany entity = new SoBiddingCompany();
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoBiddingCompany update(String id) {
        SoBiddingCompany entity = new SoBiddingCompany();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoBiddingCompany entity) {
        entity.setName(name);
        entity.setBiddingId(biddingId);
        entity.setContactUser(contactUser);
        entity.setPhone(phone);
        entity.setTenantId(tenantId());
    }
}