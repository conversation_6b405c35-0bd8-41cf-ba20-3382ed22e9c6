package org.thingsboard.server.dao.deviceDump;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDump;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpSaveRequest;

public interface DeviceDumpService {
    /**
     * 分页条件查询设备报废单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceDump> findAllConditional(DeviceDumpPageRequest request);

    /**
     * 保存设备报废单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DeviceDump save(DeviceDumpSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceDump entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 报废其下所有设备
     *
     * @param id 报废单id
     * @return 是否 成功
     */
    boolean dump(String id);

    /**
     * 用户是否为报废单的经办人
     *
     * @param id     报废单id
     * @param userId 用户id
     * @return 是否为报废单的经办人
     */
    boolean isUserHandleUser(String id, String userId);

}
