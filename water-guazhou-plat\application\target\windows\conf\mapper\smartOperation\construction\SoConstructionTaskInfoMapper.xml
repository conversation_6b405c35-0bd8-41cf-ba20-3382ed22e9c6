<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionTaskInfoMapper">
    <sql id="Base_Column_List">
        id,
        scope,
        construction_code,
        status,
        creator,
        create_time,
        complete_time,
        tenant_id
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionTaskInfo">
        <result column="id" property="id"/>
        <result column="scope" property="scope"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <insert id="save">
        INSERT INTO so_construction_task_info(id,
                                              scope,
                                              construction_code,
                                              status,
                                              creator,
                                              create_time,
                                              complete_time,
                                              tenant_id)
        VALUES (#{id},
                #{scope},
                #{constructionCode},
                #{status},
                #{creator},
                #{createTime},
                #{completeTime},
                #{tenantId})
        on conflict (scope, construction_code, tenant_id) do nothing
    </insert>

    <update id="markAsComplete">
        <!--@formatter:off-->
        <bind name="status" value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        update so_construction_task_info
        set status        = #{status},
            complete_time = now()
        where construction_code = (select construction_code from ${tableName} <!--$sqlso_construction_contract--> where id = #{id})
          and tenant_id = (select tenant_id from ${tableName} <!--$sqlso_construction_contract--> where id = #{id})
          and scope = #{scope}
          and complete_time is null
        <!--@formatter:on-->
    </update>

    <update id="markAsCompleteDirect">
        <!--@formatter:off-->
        <bind name="status" value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        update so_construction_task_info
        set status        = #{status},
            complete_time = now()
        where construction_code = #{constructionCode}
          and tenant_id = #{tenantId}
          and scope = #{scope}
          and complete_time is null
        <!--@formatter:on-->
    </update>

    <select id="isComplete" resultType="boolean">
        <!--@formatter:off-->
        <bind name="completeStatus" value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        select count(1) = 1
        from so_construction_task_info
        where construction_code = (select construction_code from ${tableName} <!--$sqlso_construction_apply--> where id = #{id})
          and tenant_id = (select tenant_id from ${tableName} <!--$sqlso_construction_apply--> where id = #{id})
          and scope = #{scope}
          and status = #{completeStatus}
        <!--@formatter:on-->
    </select>

    <select id="isCompleteDirect" resultType="boolean">
        <!--@formatter:off-->
        <bind name="completeStatus" value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        select count(1) = 1
        from so_construction_task_info
        where construction_code = #{constructionCode}
          and tenant_id = #{tenantId}
          and scope = #{scope}
          and status = #{completeStatus}
        <!--@formatter:on-->
    </select>
</mapper>