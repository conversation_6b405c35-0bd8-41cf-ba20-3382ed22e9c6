package org.thingsboard.server.common.data.project;

import lombok.Data;
import org.thingsboard.server.common.data.Device;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectTreeVO {

    private String id;
    private String nodeId;
    private String nodeType = "Project";
    private String parentId;
    private String name;
    private Boolean leaf;
    private String additionalInfo;
    private Long createTime;
    private String tenantId;
    private String type;
    private List<Device> gatewayList;
    private List children;

}
