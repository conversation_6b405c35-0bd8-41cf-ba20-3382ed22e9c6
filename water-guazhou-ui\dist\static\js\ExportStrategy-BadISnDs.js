import{e as h,y as m,a as E,v as $,x as P,S as v,R as W,Z as N,u as H}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import{A as q,bs as T,cY as z,z as B,bB as C}from"./MapView-DaoQedLH.js";import{R as I}from"./Bitmap-CraE42_6.js";const A=Math.PI/180;function O(e){return e*A}function U(e,i){const s=O(i.rotation),t=Math.abs(Math.cos(s)),r=Math.abs(Math.sin(s)),[o,n]=i.size;return e[0]=Math.round(n*r+o*t),e[1]=Math.round(n*t+o*r),e}function j(e,i,s,t){const[r,o]=i,[n,a]=t,l=.5*s;return e[0]=r-l*n,e[1]=o-l*a,e[2]=r+l*n,e[3]=o+l*a,e}const u=q(),c=[0,0],_=new T(0,0,0,0),y={container:null,fetchSource:null,requestUpdate:null,imageMaxWidth:2048,imageMaxHeight:2048,imageRotationSupported:!1,imageNormalizationSupported:!1,hidpi:!1};let p=class extends ${constructor(e){super(e),this._imagePromise=null,this.bitmaps=[],this.hidpi=y.hidpi,this.imageMaxWidth=y.imageMaxWidth,this.imageMaxHeight=y.imageMaxHeight,this.imageRotationSupported=y.imageRotationSupported,this.imageNormalizationSupported=y.imageNormalizationSupported,this.update=P(async(i,s)=>{if(v(s),!i.stationary||this.destroyed)return;const t=i.state,r=W(t.spatialReference),o=this.hidpi?i.pixelRatio:1,n=this.imageNormalizationSupported&&t.worldScreenWidth&&t.worldScreenWidth<t.size[0],a=this.imageMaxWidth??0,l=this.imageMaxHeight??0;n?(c[0]=t.worldScreenWidth,c[1]=t.size[1]):this.imageRotationSupported?(c[0]=t.size[0],c[1]=t.size[1]):U(c,t);const S=Math.floor(c[0]*o)>a||Math.floor(c[1]*o)>l,M=r&&(t.extent.xmin<r.valid[0]||t.extent.xmax>r.valid[1]),f=!this.imageNormalizationSupported&&M,w=!S&&!f,x=this.imageRotationSupported?t.rotation:0,b=this.container.children.slice();if(w){const d=n?t.paddedViewState.center:t.center;this._imagePromise&&console.error("Image promise was not defined!"),this._imagePromise=this._singleExport(t,c,d,t.resolution,x,o,s)}else{let d=Math.min(a,l);f&&(d=Math.min(t.worldScreenWidth,d)),this._imagePromise=this._tiledExport(t,d,o,s)}try{const d=await this._imagePromise??[];v(s);const R=[];if(this._imagePromise=null,this.destroyed)return;this.bitmaps=d;for(const g of b)d.includes(g)||R.push(g.fadeOut().then(()=>{g.remove(),g.destroy()}));for(const g of d)R.push(g.fadeIn());await Promise.all(R)}catch(d){this._imagePromise=null,N(d)}},5e3),this.updateExports=P(async i=>{const s=[];for(const t of this.container.children){if(!t.visible||!t.stage)return;s.push(i(t).then(()=>{t.invalidateTexture(),t.requestRender()}))}this._imagePromise=H(s).then(()=>this._imagePromise=null),await this._imagePromise})}destroy(){this.bitmaps.forEach(e=>e.destroy()),this.bitmaps=[]}get updating(){return!this.destroyed&&this._imagePromise!==null}async _export(e,i,s,t,r,o){const n=await this.fetchSource(e,Math.floor(i*r),Math.floor(s*r),{rotation:t,pixelRatio:r,signal:o});v(o);const a=new I(null,{immutable:!0,requestRenderOnSourceChangedEnabled:!0});return a.x=e.xmin,a.y=e.ymax,a.resolution=e.width/i,a.rotation=t,a.pixelRatio=r,a.opacity=0,this.container.addChild(a),await a.setSourceAsync(n,o),v(o),a}async _singleExport(e,i,s,t,r,o,n){j(u,s,t,i);const a=z(u,e.spatialReference);return[await this._export(a,i[0],i[1],r,o,n)]}_tiledExport(e,i,s,t){const r=B.create({size:i,spatialReference:e.spatialReference,scales:[e.scale]}),o=new C(r),n=o.getTileCoverage(e);if(!n)return null;const a=[];return n.forEach((l,S,M,f)=>{_.set(l,S,M,0),o.getTileBounds(u,_);const w=z(u,e.spatialReference);a.push(this._export(w,i,i,0,s,t).then(x=>(f!==0&&(_.set(l,S,M,f),o.getTileBounds(u,_),x.x=u[0],x.y=u[3]),x)))}),Promise.all(a)}};h([m()],p.prototype,"_imagePromise",void 0),h([m()],p.prototype,"bitmaps",void 0),h([m()],p.prototype,"container",void 0),h([m()],p.prototype,"fetchSource",void 0),h([m()],p.prototype,"hidpi",void 0),h([m()],p.prototype,"imageMaxWidth",void 0),h([m()],p.prototype,"imageMaxHeight",void 0),h([m()],p.prototype,"imageRotationSupported",void 0),h([m()],p.prototype,"imageNormalizationSupported",void 0),h([m()],p.prototype,"requestUpdate",void 0),h([m()],p.prototype,"updating",null),p=h([E("esri.views.2d.layers.support.ExportStrategy")],p);const D=p;export{D as v};
