package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 设备协议相关
 */

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.PROTOCOL_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class Protocol {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.PROTOCOL_TEMPLATE_ID)
    private String templateId;

    @Column(name = ModelConstants.PROTOCOL_NUMBER)
    private String number;

    @Column(name = ModelConstants.PROTOCOL_PROPERTY_CATEGORY)
    private String propertyCategory;

    @Column(name = ModelConstants.PROTOCOL_NAME)
    private String name;

    @Column(name = ModelConstants.PROTOCOL_STAT_TYPE)
    private String statType;

    @Column(name = ModelConstants.PROTOCOL_DATA_TYPE)
    private String dataType;

    @Column(name = ModelConstants.PROTOCOL_PROPERTY_TYPE)
    private String propertyType;

    @Column(name = ModelConstants.PROTOCOL_UNIT)
    private String unit;

    @Column(name = ModelConstants.PROTOCOL_REGISTER_TYPE)
    private String registerType;

    @Column(name = ModelConstants.PROTOCOL_FUNCTION_CODE)
    private Integer functionCode;

    @Column(name = ModelConstants.PROTOCOL_REGISTER_ADDRESS)
    private Long registerAddress;

    @Column(name = ModelConstants.PROTOCOL_BYTE_COUNT)
    private Integer byteCount;

    @Column(name = ModelConstants.PROTOCOL_BIT_POSITION)
    private Integer bitPosition;

    @Column(name = ModelConstants.PROTOCOL_REGISTER_SIGN_FLAG)
    private String registerSignFlag;

    @Column(name = ModelConstants.PROTOCOL_SAMPLE_DEVIATION)
    private BigDecimal sampleDeviation;

    @Column(name = ModelConstants.PROTOCOL_ORDER)
    private String order;

    @Column(name = ModelConstants.PROTOCOL_BYTE_ORDER)
    private String byteOrder;

    @Column(name = ModelConstants.PROTOCOL_DATA_OFFSET)
    private Long dataOffset;

    @Column(name = ModelConstants.PROTOCOL_SAMPLING_MAX)
    private Long samplingMax;

    @Column(name = ModelConstants.PROTOCOL_SAMPLING_MIN)
    private Long samplingMin;

    @Column(name = ModelConstants.PROTOCOL_SAMPLE_COEF)
    private BigDecimal sampleCoef;

    @Column(name = ModelConstants.PROTOCOL_UNIT_COEF)
    private BigDecimal unitCoef;

    @Column(name = ModelConstants.PROTOCOL_RANGE)
    private BigDecimal range;

    @Column(name = ModelConstants.PROTOCOL_FORMULA_PROPERTY)
    private String formulaProperty;

    @Column(name = ModelConstants.DEVICE_TEMPLATE_CREATE_TIME)
    private Long createTime;

    private String group;

}
