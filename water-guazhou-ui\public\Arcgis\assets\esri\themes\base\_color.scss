@import "functions";
@import "colors/scss/variables";
// ↳ https: //github.com/Esri/calcite-colors.git

//  Main Colors Vars
$font-color: $Calcite_Gray_700 !default;
$interactive-font-color: $Calcite_Gray_550 !default;

$background-color: $Calcite_Gray_050 !default;

// Inverse
$interactive-font-color--inverse: $Calcite_Gray_050 !default;
$background-color--inverse: $Calcite_Gray_650 !default;

// Hover
$interactive-font-color--hover: offset-foreground-color($interactive-font-color, 25%) !default;
$background-color--hover: offset-background-color($background-color, 4.75%) !default;
$border-color--hover: offset-foreground-color($interactive-font-color, 50%) !default;

// Active
$border-color--active: $Calcite_Highlight_Blue_350 !default;
$background-color--active: $Calcite_Highlight_Blue_100 !default;

// Offset
$background-color--offset: offset-background-color($background-color, 4.75%);
$background-color--offset-subtle: offset-background-color($background-color, 3.5%);

// Buttons
$button-color: $Calcite_Highlight_Blue_350 !default;
$button-color--active: $Calcite_Highlight_Blue_400 !default;
$button-color--bright: $Calcite_Blue_a100 !default;
$button-color--hover: darken($button-color, 10%) !default;
$button-color--inverse: $interactive-font-color--inverse !default;

// Other
$heading-color: $font-color !default;
$background-color--overlay: rgba(0, 0, 0, 0.7) !default;
$opacity--disabled: 0.4 !default;
$opacity--sortable: 0.75 !default;
$interactive-font-color--disabled: rgba($interactive-font-color, $opacity--disabled) !default;
$font-color--placeholder: $interactive-font-color !default;
$border-color: rgba($interactive-font-color, 0.3) !default;
$border-color--subtle: rgba($interactive-font-color, 0.15) !default;
$border-color--contrast: rgba($interactive-font-color, 0.4) !default;
$border-color--input: $Calcite_Gray_450;

$updating: $Calcite_Highlight_Blue_350;
$connection-connected: $Brand_Green_200;
$connection-disconnected: $Brand_Orange_100;

// Error
$border-color--error: $Calcite_Vibrant_Red_200 !default;
$font-color--error: $Calcite_Red_a250 !default;
$background-color--error: rgba($font-color--error, 0.1);

// Smart Mapping Sliders
$smartmapping-slider__thumb-background-color: $interactive-font-color !default;
$smartmapping-slider__ramp-stroke-color: $interactive-font-color !default;
