<template>
  <iframe
    ref="iframe"
    frameborder="0"
    scrolling="auto"
    :src="props.url"
    width="100%"
    height="100%"
    allowfullscreen="true"
  ></iframe>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store'

const props = withDefaults(
  defineProps<{
    url: string
  }>(),
  {
    url: useAppStore().frameAppUrl
  }
)
</script>

<style lang="scss" scoped></style>
