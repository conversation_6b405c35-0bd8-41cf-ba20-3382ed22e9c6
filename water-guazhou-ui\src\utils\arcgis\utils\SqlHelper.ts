/**
 * sql条件枚举
 */
export enum ECalcOperator {
  DENGYU = '1',
  BUDENGYU = '2',
  DAYU = '3',
  DAYUDENGYU = '4',
  XIAOYUDENGYU = '5',
  XIAOYU = '6',
  WEIKONG = '7',
  BUWEIKONG = '8',
  BAOHAN = '9',
  BUBAOHAN = '10'
}
/**
 * sql条件枚举
 */
export const OCalcOperator = {
  1: '等于',
  2: '不等于',
  3: '大于',
  4: '大于等于',
  5: '小于等于',
  6: '小于',
  7: '为空',
  8: '不为空',
  9: '包含',
  10: '不包含'
}
export const OCalcOperatorVal = {
  1: '=',
  2: '<>',
  3: '>',
  4: '>=',
  5: '<=',
  6: '<',
  7: 'is null',
  8: 'is not null',
  9: 'like',
  10: 'not like'
}
export const OGisConditionFields = {
  OBJECTID: 'OBJECTID',
  ELEVATION: '地面高程',
  DEPTH: '埋深',
  DIAMETER: '口径',
  MATERIAL: '材质',
  LANEWAY: '所在道路',
  X: '横坐标',
  Y: '纵坐标',
  SID: '设备编号'
  // FINISHDATE: '竣工日期'
}

export enum EGisConditionLogic {
  AND = '1',
  OR = '2'
}
export const OGisConditionLogic = {
  1: '且',
  2: '或'
}
export const OGisConditionLogicVal = {
  1: 'and',
  2: 'or'
}
