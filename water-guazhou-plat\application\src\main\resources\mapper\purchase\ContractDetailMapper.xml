<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceType.ContractDetailMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           main_id,
                           serial_id,
                           unit,
                           num,
                           price,
                           tax_rate,
                           remark,
                           tenant_id<!--@sql from contract_detail -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.purchase.ContractDetail">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="serial_id" property="serialId"/>
        <result column="unit" property="unit"/>
        <result column="num" property="num"/>
        <result column="price" property="price"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"
                     column="{serialId=serial_id,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoBySerialId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contract_detail
        <where>
            <if test="mainId != null">
                and main_id = #{mainId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update contract_detail
        <set>
            <if test="mainId != null">
                main_id = #{mainId},
            </if>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="unit != null">
                unit = #{unit},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO contract_detail(id,
                                    main_id,
                                    serial_id,
                                    unit,
                                    num,
                                    price,
                                    tax_rate,
                                    remark,
                                    tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.serialId},
             #{element.unit},
             #{element.num},
             #{element.price},
             #{element.taxRate},
             #{element.remark},
             #{element.tenantId})
        </foreach>
        on conflict(id) do update set serial_id = excluded.serial_id,
                                      unit      = excluded.unit,
                                      num       = excluded.num,
                                      price     = excluded.price,
                                      tax_rate  = excluded.tax_rate,
                                      remark    = excluded.remark
    </insert>

    <update id="updateAll">
        update contract_detail
        <set>
            serial_id = valueTable.serialId,
            unit      = valueTable.unit,
            num       = valueTable.num,
            price     = valueTable.price,
            tax_rate  = valueTable.taxRate,
            remark    = valueTable.remark
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.serialId},
             #{element.unit},
             #{element.num},
             #{element.price},
             #{element.taxRate},
             #{element.remark})
        </foreach>
        ) as valueTable(id, serialId, unit, num, price, taxRate, remark)
        where contract_detail.id = valueTable.id
    </update>

    <delete id="removeAllByMainOnIdNotIn">
        delete
        from contract_detail
        where main_id = #{id}
        <if test="idList != null and idList.size() != 0">
            and id not in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>
</mapper>