import{d as _,c as f,r as s,g as d,n as u,q as c,i,p as g,aB as m,aq as h,C as b}from"./index-r0dFAfgr.js";import{_ as y}from"./Search-NSrhrIa_.js";import{a as x}from"./gisUser-Ba96nctf.js";const k={class:"table-box"},v=_({__name:"UserLocate",props:{view:{}},setup(B){const n=f(),e=s({dataList:[],columns:[{label:"用户编号",prop:"count"},{label:"用户姓名",prop:"fee"},{label:"用户地址",prop:"time"}],pagination:{refreshData:({page:t,size:a})=>{e.pagination.page=t,e.pagination.limit=a,r()},layout:"total,sizes, jumper"}}),l=s({filters:[{type:"input",label:"用户编号",field:"yhbh",labelWidth:70},{type:"btn-group",btns:[{perm:!0,text:"查询",type:"default",loading:()=>e.loading===!0,click:()=>r()},{perm:!0,type:"danger",text:"导出",disabled:()=>e.loading===!0,click:()=>{}}]}]}),r=async()=>{var t;e.loading=!0;try{const{yhbh:a}=((t=n.value)==null?void 0:t.queryParams)||{},o=await x(a);e.dataList=o.data}catch(a){console.dir(a)}e.loading=!1};return(t,a)=>{const o=y,p=h;return d(),u(m,null,[c(o,{ref_key:"refSearch",ref:n,style:{padding:"0"},config:i(l)},null,8,["config"]),g("div",k,[c(p,{config:i(e)},null,8,["config"])])],64)}}}),q=b(v,[["__scopeId","data-v-134a43c2"]]);export{q as default};
