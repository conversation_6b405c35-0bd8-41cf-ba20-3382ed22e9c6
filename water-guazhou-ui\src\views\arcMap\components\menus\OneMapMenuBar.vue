<template>
  <div class="onemap-menu-bar">
    <div v-if="menuLength > 1" class="bar-item">
      <el-dropdown @command="handleDropDownClick">
        <div class="dropdown-content">
          <span class="dropdown-title">{{
            state.current?.meta.title || '暂无菜单'
          }}</span>
          <Icon icon="ep:caret-bottom"></Icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <template v-for="menu in props.menus" :key="menu.path">
              <el-dropdown-item
                v-if="
                  state.ONE_MAP_CONFIG.hiddenMenus.indexOf(menu.path) === -1
                "
                :command="menu.path"
              >
                <span>{{ menu.meta.title }}</span>
              </el-dropdown-item>
            </template>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div v-if="menuLength > 1" class="divider"></div>
    <template v-for="menu in state.current?.children" :key="menu.path">
      <div
        v-if="state.ONE_MAP_CONFIG.hiddenSubMenus.indexOf(menu.path) === -1"
        class="bar-item"
        :class="[state.activeMenuItem?.path === menu.path ? 'is-active' : '']"
        @click="(e) => handleMenuItemClick(menu)"
      >
        <Icon v-if="menu.meta.icon" :icon="menu.meta.icon"></Icon>
        <span class="title">{{ menu.meta.title }}</span>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';

const props = defineProps<{
  menus: IMenuItem[];
}>();
const state = reactive<{
  current?: IMenuItem;
  activeMenuItem?: IMenuItem;
  ONE_MAP_CONFIG: IOneMapConfig;
}>({
  current: undefined,
  activeMenuItem: undefined,
  ONE_MAP_CONFIG: {
    ...window.SITE_CONFIG.ONE_MAP_CONFIG,
    hiddenMenus: window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenMenus || [],
    hiddenSubMenus: window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenSubMenus || []
  }
});
const menuLength = computed(() => {
  return props.menus.filter(
    (item) => state.ONE_MAP_CONFIG.hiddenMenus.indexOf(item.path) === -1
  ).length;
});
const emit = defineEmits(['change', 'item-click']);
const handleDropDownClick = (path) => {
  if (!path) return;
  const menu = props.menus.find((item) => item.path === path);
  state.current = menu;
  state.activeMenuItem = menu?.children?.[0];
  emit('change', menu);
};
const handleMenuItemClick = (menu) => {
  if (!menu) return;
  state.activeMenuItem = menu;
  emit('item-click', menu);
};
watch(
  () => props.menus,
  () => {
    state.current = props.menus?.[0];
    handleDropDownClick(state.current?.path);
  }
);
onMounted(() => {
  state.current = props.menus?.[0];
  handleDropDownClick(state.current?.path);
});
</script>
<style lang="scss" scoped>
.onemap-menu-bar {
  background-color: var(--el-bg-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  border-radius: 12px;
  display: flex;
  align-items: center;
  height: 49px;
  padding: 14px 0;
  .bar-item {
    height: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    padding: 0 14px;
    color: var(--el-text-color-secondary);
    &.is-active,
    &:hover {
      color: var(--el-color-primary);
    }
    .title {
      margin: 0;
    }
    .el-dropdown,
    .dropdown-content {
      display: flex;
      align-items: center;
      .dropdown-title {
        margin-right: 12px;
        word-break: keep-all;
      }
    }
  }
  .divider {
    width: 1px;
    height: 20px;
    border: 1px solid var(--el-border-color);
  }
}
</style>
