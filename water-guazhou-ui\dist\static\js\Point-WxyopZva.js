import{fB as cr,aS as Ve,R as w,a5 as k,aT as P,a4 as vn,aP as ce,aZ as wn,fX as $t,fr as bn,aV as $n,aU as Gt,fY as Sn,T as A,a_ as An,fZ as On,aW as kn,fp as Mn,$ as En}from"./index-r0dFAfgr.js";function p(t,r,n,s){var o,a=arguments.length,l=a<3?r:s===null?s=Object.getOwnPropertyDescriptor(r,n):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(t,r,n,s);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(l=(a<3?o(l):a>3?o(r,n,l):o(r,n))||l);return a>3&&l&&Object.defineProperty(r,n,l),l}const Tn=/\{([^\}]+)\}/g;function It(t){return t??""}function Cn(t,r){return t.replace(Tn,typeof r=="object"?(n,s)=>It(cr(s,r)):(n,s)=>It(r(s)))}function gi(t,r){return t.replace(/([\.$?*|{}\(\)\[\]\\\/\+\-^])/g,n=>r&&r.includes(n)?n:`\\${n}`)}function jn(t){let r=0;for(let n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n),r|=0;return r}function yi(t){return new DOMParser().parseFromString(t||"","text/html").body.innerText||""}const Ut={info:0,warn:1,error:2,none:3};let $=class b{constructor(r){this.level=null,this._module="",this._parent=null,this.writer=null,this._loggedMessages={error:new Map,warn:new Map,info:new Map},r.level!=null&&(this.level=r.level),r.writer!=null&&(this.writer=r.writer),this._module=r.module,b._loggers[this.module]=this;const n=this.module.lastIndexOf(".");n!==-1&&(this._parent=b.getLogger(this.module.slice(0,n)))}get module(){return this._module}get parent(){return this._parent}error(...r){this._log("error","always",...r)}warn(...r){this._log("warn","always",...r)}info(...r){this._log("info","always",...r)}errorOnce(...r){this._log("error","once",...r)}warnOnce(...r){this._log("warn","once",...r)}infoOnce(...r){this._log("info","once",...r)}errorOncePerTick(...r){this._log("error","oncePerTick",...r)}warnOncePerTick(...r){this._log("warn","oncePerTick",...r)}infoOncePerTick(...r){this._log("info","oncePerTick",...r)}get test(){const r=this;return{loggedMessages:r._loggedMessages,clearLoggedWarnings:()=>r._loggedMessages.warn.clear()}}static get testSingleton(){return{resetLoggers(r={}){const n=b._loggers;return b._loggers=r,n},set throttlingDisabled(r){b._throttlingDisabled=r}}}static getLogger(r){let n=b._loggers[r];return n||(n=new b({module:r})),n}_log(r,n,...s){if(this._matchLevel(r)){if(n!=="always"&&!b._throttlingDisabled){const o=this._argsToKey(s),a=this._loggedMessages[r].get(o);if(n==="once"&&a!=null||n==="oncePerTick"&&a&&a>=b._tickCounter)return;this._loggedMessages[r].set(o,b._tickCounter),b._scheduleTickCounterIncrement()}for(const o of Ve.log.interceptors)if(o(r,this.module,...s))return;this._inheritedWriter()(r,this.module,...s)}}_parentWithMember(r,n){let s=this;for(;w(s);){const o=s[r];if(w(o))return o;s=s.parent}return n}_inheritedWriter(){return this._parentWithMember("writer",this._consoleWriter)}_consoleWriter(r,n,...s){console[r](`[${n}]`,...s)}_matchLevel(r){const n=Ve.log.level?Ve.log.level:"warn";return Ut[this._parentWithMember("level",n)]<=Ut[r]}_argsToKey(...r){return jn(JSON.stringify(r,(s,o)=>typeof o!="object"||Array.isArray(o)?o:"[Object]"))}static _scheduleTickCounterIncrement(){b._tickCounterScheduled||(b._tickCounterScheduled=!0,Promise.resolve().then(()=>{b._tickCounter++,b._tickCounterScheduled=!1}))}};$._loggers={},$._tickCounter=0,$._tickCounterScheduled=!1,$._throttlingDisabled=!1;function _i(t,r){for(const[n,s]of t)if(r(s,n))return!0;return!1}function mi(t,r){for(const[n,s]of t)if(r(s,n))return s;return null}function Nn(t,r,n){const s=t.get(r);if(s!==void 0)return s;const o=n();return t.set(r,o),o}const J=$.getLogger("esri.core.accessorSupport.ensureTypes");function Pn(t){return t==null?t:new Date(t)}function Rn(t){return t==null?t:!!t}function Gn(t){return t==null?t:t.toString()}function ur(t){return t==null?t:(t=parseFloat(t),isNaN(t)?0:t)}function In(t){return t==null?t:Math.round(parseFloat(t))}function fr(t){return t&&t.constructor&&t.constructor.__accessorMetadata__!==void 0}function Oe(t,r){return r!=null&&t&&!(r instanceof t)}function hr(t){return t&&"isCollection"in t}function xt(t){return t&&t.Type?typeof t.Type=="function"?t.Type:t.Type.base:null}function Un(t,r){if(!r||!r.constructor||!hr(r.constructor))return tt(t,r)?r:new t(r);const n=xt(t.prototype.itemType),s=xt(r.constructor.prototype.itemType);return n?s?n===s?r:n.prototype.isPrototypeOf(s.prototype)?new t(r):(tt(t,r),r):new t(r):r}function tt(t,r){return!!fr(r)&&(J.error("Accessor#set","Assigning an instance of '"+(r.declaredClass||"unknown")+"' which is not a subclass of '"+We(t)+"'"),!0)}function xn(t,r){return r==null?r:hr(t)?Un(t,r):Oe(t,r)?tt(t,r)?r:new t(r):r}function We(t){return t&&t.prototype&&t.prototype.declaredClass||"unknown"}const Dn=new WeakMap;function Wn(t){switch(t){case Number:return ur;case D:return In;case Boolean:return Rn;case String:return Gn;case Date:return Pn;default:return Nn(Dn,t,()=>xn.bind(null,t))}}function I(t,r){const n=Wn(t);return arguments.length===1?n:n(r)}function se(t,r,n){return arguments.length===1?se.bind(null,t):r&&(Array.isArray(r)?r.map(s=>t(s,n)):[t(r,n)])}function qn(t,r){return arguments.length===1?se(I.bind(null,t)):se(I.bind(null,t),r)}function dr(t,r,n){return r!==0&&Array.isArray(n)?n.map(s=>dr(t,r-1,s)):t(n)}function ke(t,r,n){if(arguments.length===2)return ke.bind(null,t,r);if(!n)return n;let s=r,o=n=dr(t,r,n);for(;s>0&&Array.isArray(o);)s--,o=o[0];if(o!==void 0)for(let a=0;a<s;a++)n=[n];return n}function zn(t,r,n){return arguments.length===2?ke(I.bind(null,t),r):ke(I.bind(null,t),r,n)}function pr(t){return!!Array.isArray(t)&&!t.some(r=>{const n=typeof r;return!(n==="string"||n==="number"||n==="function"&&t.length>1)})}function rt(t,r){if(arguments.length===2)return rt(t).call(null,r);const n=new Set,s=t.filter(c=>typeof c!="function"),o=t.filter(c=>typeof c=="function");for(const c of t)typeof c!="string"&&typeof c!="number"||n.add(c);let a=null,l=null;return(c,u)=>{if(c==null)return c;const f=typeof c,h=f==="string"||f==="number";return h&&(n.has(c)||o.some(d=>f==="string"&&d===String||f==="number"&&d===Number))||f==="object"&&o.some(d=>!Oe(c,d))?c:(h&&s.length?(a||(a=s.map(d=>typeof d=="string"?`'${d}'`:`${d}`).join(", ")),J.error("Accessor#set",`'${c}' is not a valid value for this property, only the following values are valid: ${a}`)):typeof c=="object"&&o.length?(l||(l=o.map(d=>We(d)).join(", ")),J.error("Accessor#set",`'${c}' is not a valid value for this property, value must be one of ${l}`)):J.error("Accessor#set",`'${c}' is not a valid value for this property`),u&&(u.valid=!1),null)}}function nt(t,r){if(arguments.length===2)return nt(t).call(null,r);const n={},s=[],o=[];for(const u in t.typeMap){const f=t.typeMap[u];n[u]=I(f),s.push(We(f)),o.push(u)}const a=()=>`'${s.join("', '")}'`,l=()=>`'${o.join("', '")}'`,c=typeof t.key=="string"?u=>u[t.key]:t.key;return u=>{if(t.base&&!Oe(t.base,u)||u==null)return u;const f=c(u)||t.defaultKeyValue,h=n[f];if(!h)return J.error("Accessor#set",`Invalid property value, value needs to be one of ${a()}, or a plain object that can autocast (having .type = ${l()})`),null;if(!Oe(t.typeMap[f],u))return u;if(typeof t.key=="string"&&!fr(u)){const d={};for(const g in u)g!==t.key&&(d[g]=u[g]);return h(d)}return h(u)}}let D=class{};const wi={native:t=>({type:"native",value:t}),array:t=>({type:"array",value:t}),oneOf:t=>({type:"one-of",values:t})};function Bn(t){if(!t||!("type"in t))return!1;switch(t.type){case"native":case"array":case"one-of":return!0}return!1}function gr(t){switch(t.type){case"native":return I(t.value);case"array":return se(gr(t.value));case"one-of":return Fn(t);default:return null}}function Fn(t){let r=null;return(n,s)=>ot(n,t)?n:(r==null&&(r=st(t)),J.error("Accessor#set",`Invalid property value, value needs to be of type ${r}`),s&&(s.valid=!1),null)}function st(t){switch(t.type){case"native":switch(t.value){case Number:return"number";case String:return"string";case Boolean:return"boolean";case D:return"integer";case Date:return"date";default:return We(t.value)}case"array":return`array of ${st(t.value)}`;case"one-of":{const r=t.values.map(n=>st(n));return`one of ${r.slice(0,r.length-1)} or ${r[r.length-1]}`}}return"unknown"}function ot(t,r){if(t==null)return!0;switch(r.type){case"native":switch(r.value){case Number:case D:return typeof t=="number";case Boolean:return typeof t=="boolean";case String:return typeof t=="string"}return t instanceof r.value;case"array":return!!Array.isArray(t)&&!t.some(n=>!ot(n,r.value));case"one-of":return r.values.some(n=>ot(t,n))}}function Ln(t){return St(()=>t.forEach(r=>w(r)&&r.remove()))}function St(t){return{remove:()=>{t&&(t(),t=void 0)}}}function bi(t){return St(()=>{const r=t();w(r)&&r.remove()})}function $i(t){return St(w(t)?()=>t.destroy():void 0)}function z(t){return t?t.__accessor__?t.__accessor__:t.propertyInvalidated?t:null:null}function Jn(t,r){return t!=null&&t.metadatas&&t.metadatas[r]!=null}function ve(t,r,n){return n?Me(t,r,{policy:n,path:""}):Me(t,r,null)}function Me(t,r,n){return r?Object.keys(r).reduce((s,o)=>{let a=null,l="merge";if(n&&(a=n.path?`${n.path}.${o}`:o,l=n.policy(a)),l==="replace"||l==="replace-arrays"&&Array.isArray(s[o]))return s[o]=r[o],s;if(s[o]===void 0)return s[o]=k(r[o]),s;let c=s[o],u=r[o];if(c===u)return s;if(Array.isArray(u)||Array.isArray(s))c=c?Array.isArray(c)?s[o]=c.concat():s[o]=[c]:s[o]=[],u&&(Array.isArray(u)||(u=[u]),u.forEach(f=>{c.includes(f)||c.push(f)}));else if(u&&typeof u=="object")if(n){const f=n.path;n.path=P(a),s[o]=Me(c,u,n),n.path=f}else s[o]=Me(c,u,null);else s.hasOwnProperty(o)&&!r.hasOwnProperty(o)||(s[o]=u);return s},t||{}):t}function yr(t){return Array.isArray(t)?t:t.split(".")}function Dt(t){return t.includes(",")?t.split(",").map(r=>r.trim()):[t.trim()]}function Hn(t){if(Array.isArray(t)){const r=[];for(const n of t)r.push(...Dt(n));return r}return Dt(t)}function _r(t,r,n,s){const o=Hn(r);if(o.length!==1){const a=o.map(l=>s(t,l,n));return Ln(a)}return s(t,o[0],n)}function mr(t){let r=!1;return()=>{r||(r=!0,t())}}function vr(t,r){const n=t[t.length-1]==="?"?t.slice(0,-1):t;if(r.getItemAt!=null||Array.isArray(r)){const o=parseInt(n,10);if(!isNaN(o))return Array.isArray(r)?r[o]:r.getItemAt(o)}const s=z(r);return Jn(s,n)?s.get(n):r[n]}function wr(t,r,n){if(t==null)return t;const s=vr(r[n],t);return!s&&n<r.length-1?void 0:n===r.length-1?s:wr(s,r,n+1)}function ue(t,r,n=0){return typeof r!="string"||r.includes(".")?wr(t,yr(r),n):vr(r,t)}function Ee(t,r){return ue(t,r)}function Wt(t,r){return ue(r,t)!==void 0}function fe(t){let r=t.constructor.__accessorMetadata__;const n=Object.prototype.hasOwnProperty.call(t.constructor,"__accessorMetadata__");if(r){if(!n){r=Object.create(r);for(const s in r)r[s]=k(r[s]);Object.defineProperty(t.constructor,"__accessorMetadata__",{value:r,enumerable:!1,configurable:!0,writable:!0})}}else r={},Object.defineProperty(t.constructor,"__accessorMetadata__",{value:r,enumerable:!1,configurable:!0,writable:!0});return P(t.constructor.__accessorMetadata__)}function qe(t,r){const n=fe(t);let s=n[r];return s||(s=n[r]={}),s}function Vn(t,r){return ve(t,r,Kn)}const Zn=/^(?:[^.]+\.)?(?:value|type|(?:json\.type|json\.origins\.[^.]\.type))$/;function Kn(t){return Zn.test(t)?"replace":"merge"}function Xn(t,r){return t.replace(/\$\{([^\s\:\}]*)(?:\:([^\s\:\}]+))?\}/g,(n,s)=>{if(s==="")return"$";const o=cr(s,r),a=o??"";if(a===void 0)throw new Error(`could not find key "${s}" in template`);return a.toString()})}let br=class $r{constructor(r,n,s){this.name=r,this.details=s,this instanceof $r&&(this.message=(n&&Xn(n,s))??"")}toString(){return"["+this.name+"]: "+this.message}},he=class we extends br{constructor(r,n,s){if(super(r,n,s),!(this instanceof we))return new we(r,n,s)}toJSON(){if(this.details!=null)try{return{name:this.name,message:this.message,details:JSON.parse(JSON.stringify(this.details,(r,n)=>{if(n&&typeof n=="object"&&typeof n.toJSON=="function")return n;try{return k(n)}catch{return"[object]"}}))}}catch(r){throw $.getLogger("esri.core.Error").error(r),r}return{name:this.name,message:this.message,details:this.details}}static fromJSON(r){return new we(r.name,r.message,r.details)}};he.prototype.type="error";function Te(t,r,n){if(t&&r)if(typeof r=="object")for(const s of Object.getOwnPropertyNames(r))Te(t,s,r[s]);else{if(r.includes(".")){const o=r.split("."),a=o.splice(o.length-1,1)[0];return void Te(Ee(t,o),a,n)}const s=t.__accessor__;s!=null&&Yn(r,s),t[r]=n}}function Yn(t,r){if(vn("esri-unknown-property-errors")&&!Qn(t,r))throw new he("set:unknown-property",es(t,r))}function Qn(t,r){return r.metadatas[t]!=null}function es(t,r){return"setting unknown property '"+t+"' on instance of "+r.host.declaredClass}function m(t={}){return(r,n)=>{if(r===Function.prototype)throw new Error(`Inappropriate use of @property() on a static field: ${r.name}.${n}. Accessor does not support static properties.`);const s=Object.getOwnPropertyDescriptor(r,n),o=qe(r,n);s&&(s.get||s.set?(o.get=s.get||o.get,o.set=s.set||o.set):"value"in s&&("value"in t&&$.getLogger("esri.core.accessorSupport.decorators.property").warn(`@property() will redefine the value of "${n}" on "${r.constructor.name}" already defined in the metadata`,t),o.value=t.value=s.value)),t.readOnly!=null&&(o.readOnly=t.readOnly);const a=t.aliasOf;if(a){const u=typeof a=="string"?a:a.source,f=typeof a=="string"?null:a.overridable===!0;let h;o.dependsOn=[u],o.get=function(){let d=Ee(this,u);if(typeof d=="function"){h||(h=u.split(".").slice(0,-1).join("."));const g=Ee(this,h);g&&(d=d.bind(g))}return d},o.readOnly||(o.set=f?function(d){this._override(n,d)}:function(d){Te(this,u,d)})}const l=t.type,c=t.types;o.cast||(l?o.cast=ts(l):c&&(Array.isArray(c)?o.cast=se(nt(c[0])):o.cast=nt(c))),Vn(o,t),t.range&&(o.cast=rs(o.cast,t.range))}}function Sr(t,r,n){const s=qe(t,n);s.json||(s.json={});let o=s.json;return r!==void 0&&(o.origins||(o.origins={}),o.origins[r]||(o.origins[r]={}),o=o.origins[r]),o}function ts(t){let r=0,n=t;if(Bn(t))return gr(t);for(;Array.isArray(n)&&n.length===1&&typeof n[0]!="string"&&typeof n[0]!="number";)n=n[0],r++;const s=n;if(pr(s))return r===0?rt(s):ke(rt(s),r);if(r===1)return qn(s);if(r>1)return zn(s,r);const o=t;return o.from?o.from:I(o)}function rs(t,r){return n=>{let s=+t(n);return r.step!=null&&(s=Math.round(s/r.step)*r.step),r.min!=null&&(s=Math.max(r.min,s)),r.max!=null&&(s=Math.min(r.max,s)),s}}const Ar=Symbol("Accessor-beforeDestroy");var j;(function(t){t[t.INITIALIZING=0]="INITIALIZING",t[t.CONSTRUCTING=1]="CONSTRUCTING",t[t.CONSTRUCTED=2]="CONSTRUCTED"})(j||(j={}));var _;(function(t){t[t.Dirty=1]="Dirty",t[t.Overriden=2]="Overriden",t[t.Computing=4]="Computing",t[t.NonNullable=8]="NonNullable",t[t.HasDefaultValue=16]="HasDefaultValue",t[t.DepTrackingInitialized=32]="DepTrackingInitialized",t[t.AutoTracked=64]="AutoTracked",t[t.ExplicitlyTracking=128]="ExplicitlyTracking"})(_||(_={}));const Ce={onObservableAccessed:()=>{},onTrackingEnd:()=>{}},ee=[];let te=Ce;function ze(t){te.onObservableAccessed(t)}let be=!1,$e=!1;function U(t,r,n){if(be)return At(t,r,n);Or(t);const s=r.call(n);return kr(),s}function ns(t,r){return U(Ce,t,r)}function At(t,r,n){const s=be;be=!0,Or(t);let o=null;try{o=r.call(n)}catch(a){$e&&$.getLogger("esri.core.accessorSupport.tracking").error(a)}return kr(),be=s,o}function Or(t){te=t,ee.push(t)}function kr(){const t=ee.length;if(t>1){const r=ee.pop();te=ee[t-2],r.onTrackingEnd()}else if(t===1){const r=ee.pop();te=Ce,r.onTrackingEnd()}else te=Ce}function Mr(t,r){const n=r.observerObject;if(n.flags&_.DepTrackingInitialized)return;const s=$e;$e=!1,n.flags&_.AutoTracked?At(r,r.metadata.get,t):Er(t,r),$e=s}const ss=[];function Er(t,r){const n=r.observerObject;n.flags&_.ExplicitlyTracking||(n.flags|=_.ExplicitlyTracking,At(r,()=>{const s=r.metadata.dependsOn||ss;for(const o of s)if(typeof o!="string"||o.includes(".")){const a=yr(o);for(let l=0,c=t;l<a.length&&c!=null&&typeof c=="object";++l)c=qt(c,a[l],l!==a.length-1)}else qt(t,o,!1)}),n.flags&=~_.ExplicitlyTracking)}function qt(t,r,n){var a;const s=r[r.length-1]==="?"?r.slice(0,-1):r;if(t.getItemAt!=null||Array.isArray(t)){const l=parseInt(s,10);if(!isNaN(l))return Array.isArray(t)?t[l]:t.getItemAt(l)}const o=(a=z(t))==null?void 0:a.properties.get(s);return o&&(ze(o.observerObject),Mr(t,o)),n?t[s]:void 0}function os(t){if(t.json&&t.json.origins){const r=t.json.origins,n={"web-document":["web-scene","web-map"]};for(const s in n)if(r[s]){const o=r[s];n[s].forEach(a=>{r[a]=o}),delete r[s]}}}let Tr=class it extends br{constructor(r,n,s){if(super(r,n,s),!(this instanceof it))return new it(r,n,s)}};Tr.prototype.type="warning";function Cr(t){return!!t&&t.prototype&&t.prototype.declaredClass&&t.prototype.declaredClass.indexOf("esri.core.Collection")===0}const at=$.getLogger("esri.core.accessorSupport.extensions.serializableProperty.reader");function zt(t,r,n){var s,o;t&&(!n&&!r.read||(s=r.read)!=null&&s.reader||((o=r.read)==null?void 0:o.enabled)===!1||cs(t)&&ce("read.reader",is(t),r))}function is(t){var n,s;const r=t.ndimArray??0;if(r>1)return ls(t);if(r===1)return Bt(t);if("type"in t&&Nr(t.type)){const o=(s=(n=t.type.prototype)==null?void 0:n.itemType)==null?void 0:s.Type,a=Bt(typeof o=="function"?{type:o}:{types:o});return(l,c,u)=>{const f=a(l,c,u);return f&&new t.type(f)}}return Ot(t)}function Ot(t){return"type"in t?as(t.type):us(t.types)}function as(t){return t.prototype.read?(r,n,s)=>{if(r==null)return r;const o=typeof r;if(o!=="object")return void at.error(`Expected JSON value of type 'object' to deserialize type '${t.prototype.declaredClass}', but got '${o}'`);const a=new t;return a.read(r,s),a}:t.fromJSON}function jr(t,r,n,s){return s!==0&&Array.isArray(r)?r.map(o=>jr(t,o,n,s-1)):t(r,void 0,n)}function ls(t){const r=Ot(t),n=jr.bind(null,r),s=t.ndimArray??0;return(o,a,l)=>{if(o==null)return o;o=n(o,l,s);let c=s,u=o;for(;c>0&&Array.isArray(u);)c--,u=u[0];if(u!==void 0)for(let f=0;f<c;f++)o=[o];return o}}function Bt(t){const r=Ot(t);return(n,s,o)=>{if(n==null)return n;if(Array.isArray(n)){const l=[];for(const c of n){const u=r(c,void 0,o);u!==void 0&&l.push(u)}return l}const a=r(n,void 0,o);return a!==void 0?[a]:void 0}}function Nr(t){if(!Cr(t))return!1;const r=t.prototype.itemType;return!(!r||!r.Type)&&(typeof r.Type=="function"?kt(r.Type):Pr(r.Type))}function cs(t){return"types"in t?Pr(t.types):kt(t.type)}function kt(t){return!Array.isArray(t)&&!!t&&t.prototype&&("read"in t.prototype||"fromJSON"in t||Nr(t))}function Pr(t){for(const r in t.typeMap)if(!kt(t.typeMap[r]))return!1;return!0}function us(t){let r=null;const n=t.errorContext??"type";return(s,o,a)=>{if(s==null)return s;const l=typeof s;if(l!=="object")return void at.error(`Expected JSON value of type 'object' to deserialize, but got '${l}'`);r||(r=fs(t));const c=t.key;if(typeof c!="string")return;const u=s[c],f=u?r[u]:t.defaultKeyValue?t.typeMap[t.defaultKeyValue]:void 0;if(!f){const d=`Type '${u||"unknown"}' is not supported`;return a&&a.messages&&s&&a.messages.push(new Tr(`${n}:unsupported`,d,{definition:s,context:a})),void at.error(d)}const h=new f;return h.read(s,a),h}}function fs(t){var n,s;const r={};for(const o in t.typeMap){const a=t.typeMap[o],l=fe(a.prototype);if(typeof t.key=="function")continue;const c=l[t.key];if(!c)continue;(n=c.json)!=null&&n.type&&Array.isArray(c.json.type)&&c.json.type.length===1&&typeof c.json.type[0]=="string"&&(r[c.json.type[0]]=a);const u=(s=c.json)==null?void 0:s.write;if(!u||!u.writer){r[o]=a;continue}const f=u.target,h=typeof f=="string"?f:t.key,d={};u.writer(o,d,h),d[h]&&(r[d[h]]=a)}return r}function hs(t){if(t.json||(t.json={}),Lt(t.json),Jt(t.json),Ft(t.json),t.json.origins)for(const r in t.json.origins)Lt(t.json.origins[r]),Jt(t.json.origins[r]),Ft(t.json.origins[r]);return!0}function Ft(t){t.name&&(t.read&&typeof t.read=="object"?t.read.source===void 0&&(t.read.source=t.name):t.read={source:t.name},t.write&&typeof t.write=="object"?t.write.target===void 0&&(t.write.target=t.name):t.write={target:t.name})}function Lt(t){typeof t.read=="boolean"?t.read={enabled:t.read}:typeof t.read=="function"?t.read={enabled:!0,reader:t.read}:t.read&&typeof t.read=="object"&&t.read.enabled===void 0&&(t.read.enabled=!0)}function Jt(t){typeof t.write=="boolean"?t.write={enabled:t.write}:typeof t.write=="function"?t.write={enabled:!0,writer:t.write}:t.write&&typeof t.write=="object"&&t.write.enabled===void 0&&(t.write.enabled=!0)}function Ht(t,r){if(!r.write||r.write.writer||r.write.enabled===!1&&!r.write.overridePolicy)return;const n=(t==null?void 0:t.ndimArray)??0;t&&(n===1||"type"in t&&Cr(t.type))?r.write.writer=ys:n>1?r.write.writer=_s(n):r.types?Array.isArray(r.types)?r.write.writer=ps(r.types[0]):r.write.writer=ds(r.types):r.write.writer=oe}function ds(t){return(r,n,s,o)=>r?Rr(r,t,o)?oe(r,n,s,o):void 0:oe(r,n,s,o)}function Rr(t,r,n){for(const s in r.typeMap)if(t instanceof r.typeMap[s])return!0;if(n!=null&&n.messages){const s=r.errorContext??"type",o=`Values of type '${(typeof r.key!="function"?t[r.key]:t.declaredClass)??"Unknown"}' cannot be written`;n&&n.messages&&t&&n.messages.push(new he(`${s}:unsupported`,o,{definition:t,context:n})),$.getLogger("esri.core.accessorSupport.extensions.serializableProperty.writer").error(o)}return!1}function ps(t){return(r,n,s,o)=>!r||!Array.isArray(r)?oe(r,n,s,o):oe(r.filter(a=>Rr(a,t,o)),n,s,o)}function oe(t,r,n,s){ce(n,je(t,s),r)}function je(t,r){return t&&typeof t.write=="function"?t.write({},r):t&&typeof t.toJSON=="function"?t.toJSON():typeof t=="number"?gs(t):t}function gs(t){return t===-1/0?-Number.MAX_VALUE:t===1/0?Number.MAX_VALUE:isNaN(t)?null:t}function ys(t,r,n,s){let o;t===null?o=null:t&&typeof t.map=="function"?(o=t.map(a=>je(a,s)),typeof o.toArray=="function"&&(o=o.toArray())):o=[je(t,s)],ce(n,o,r)}function Gr(t,r,n){return n!==0&&Array.isArray(t)?t.map(s=>Gr(s,r,n-1)):je(t,r)}function _s(t){return(r,n,s,o)=>{let a;if(r===null)a=null;else{a=Gr(r,o,t);let l=t,c=a;for(;l>0&&Array.isArray(c);)l--,c=c[0];if(c!==void 0)for(let u=0;u<l;u++)a=[a]}ce(s,a,n)}}function lt(t,r){return Mt(t,"read",r)}function Ir(t,r){return Mt(t,"write",r)}function Mt(t,r,n){let s=t&&t.json;if(t&&t.json&&t.json.origins&&n){const o=n.origin&&t.json.origins[n.origin];o&&(r==="any"||r in o)&&(s=o)}return s}function ms(t){const r=vs(t);if(t.json.origins)for(const n in t.json.origins){const s=t.json.origins[n],o=s.types?ws(s):r;zt(o,s,!1),s.types&&!s.write&&t.json.write&&t.json.write.enabled&&(s.write={...t.json.write}),Ht(o,s)}zt(r,t.json,!0),Ht(r,t.json)}function vs(t){return t.json.types?ct(t.json):t.type?Ur(t):ct(t)}function ws(t){return t.type?Ur(t):ct(t)}function Ur(t){if(!t.type)return;let r=0,n=t.type;for(;Array.isArray(n)&&!pr(n);)n=n[0],r++;return{type:n,ndimArray:r}}function ct(t){if(!t.types)return;let r=0,n=t.types;for(;Array.isArray(n);)n=n[0],r++;return{types:n,ndimArray:r}}function bs(t){hs(t)&&(os(t),ms(t))}const Ze=new Set,Ke=new Set;function K(t){return r=>{r.prototype.declaredClass=t,Ss(r);const n=[],s=[];let o=r.prototype;for(;o;)o.hasOwnProperty("initialize")&&!Ze.has(o.initialize)&&(Ze.add(o.initialize),n.push(o.initialize)),o.hasOwnProperty("destroy")&&!Ke.has(o.destroy)&&(Ke.add(o.destroy),s.push(o.destroy)),o=Object.getPrototypeOf(o);Ze.clear(),Ke.clear();class a extends r{constructor(...c){if(super(...c),this.constructor===a&&typeof this.postscript=="function"){if(n.length&&Object.defineProperty(this,"initialize",{enumerable:!1,configurable:!0,value(){for(let u=n.length-1;u>=0;u--)n[u].call(this)}}),s.length){let u=!1;const f=this[Ar];Object.defineProperty(this,"destroy",{enumerable:!1,configurable:!0,value(){if(!u){u=!0,f.call(this);for(let h=0;h<s.length;h++)s[h].call(this)}}})}this.postscript(...c)}}}return a.__accessorMetadata__=fe(r.prototype),a.prototype.declaredClass=t,a}}function $s(t,r){return r.get==null?function(){const n=this.__accessor__.properties.get(t);if(n===void 0)return;ze(n.observerObject);const s=this.__accessor__.store;return s.has(t)?s.get(t):n.metadata.value}:function(){const n=this.__accessor__.properties.get(t);if(n!==void 0)return n.getComputed()}}function Ss(t){const r=t.prototype,n=fe(r),s={};for(const o of Object.getOwnPropertyNames(n)){const a=n[o];bs(a),s[o]={enumerable:!0,configurable:!0,get:$s(o,a),set(l){const c=this.__accessor__;if(c!==void 0){if(!Object.isFrozen(this)){if(c.initialized&&a.readOnly)throw new TypeError(`[accessor] cannot assign to read-only property '${o}' of ${this.declaredClass}`);if(c.lifecycle===j.CONSTRUCTED&&a.constructOnly)throw new TypeError(`[accessor] cannot assign to construct-only property '${o}' of ${this.declaredClass}`);c.set(o,l)}}else Object.defineProperty(this,o,{enumerable:!0,configurable:!0,writable:!0,value:l})}}}Object.defineProperties(t.prototype,s)}function Si(t){const r=[];return function*(){yield*r;for(const n of t)r.push(n),yield n}}function Ai(t,r){for(const n of t)if(n!=null&&r(n))return n}function Vt(t){return t!=null&&typeof t[Symbol.iterator]=="function"}let As=class{constructor(){this._groups=new Map}destroy(){this.removeAll()}get size(){let r=0;return this._groups.forEach(n=>{r+=n.length}),r}add(r,n){if(Vt(r)){const s=this._getOrCreateGroup(n);for(const o of r)this._isHandle(o)&&s.push(o)}else this._isHandle(r)&&this._getOrCreateGroup(n).push(r);return this}forEach(r,n){if(typeof r=="function")this._groups.forEach(s=>s.forEach(r));else{const s=this._getGroup(r);s&&n&&s.forEach(n)}}has(r){return this._groups.has(this._ensureGroupKey(r))}remove(r){if(typeof r!="string"&&Vt(r)){for(const n of r)this.remove(n);return this}return this.has(r)?(this._removeAllFromGroup(this._getGroup(r)),this._groups.delete(this._ensureGroupKey(r)),this):this}removeAll(){return this._groups.forEach(r=>this._removeAllFromGroup(r)),this._groups.clear(),this}_isHandle(r){return r&&!!r.remove}_getOrCreateGroup(r){if(this.has(r))return this._getGroup(r);const n=[];return this._groups.set(this._ensureGroupKey(r),n),n}_getGroup(r){return P(this._groups.get(this._ensureGroupKey(r)))}_ensureGroupKey(r){return r||"_default_"}_removeAllFromGroup(r){r.forEach(n=>n.remove())}};function Os(t){return t&&t.release&&typeof t.release=="function"}function ks(t){return t&&t.acquire&&typeof t.acquire=="function"}let Be=class ut{constructor(r,n,s,o=1,a=0){if(this._ctor=r,this._acquireFunction=n,this._releaseFunction=s,this.allocationSize=o,this._pool=new Array(a),this._initialSize=a,this._ctor)for(let l=0;l<a;l++)this._pool[l]=new this._ctor;this.allocationSize=Math.max(o,1)}destroy(){this.prune(0)}acquire(...r){let n;if(ut.test.disabled)n=new this._ctor;else{if(this._pool.length===0){const s=this.allocationSize;for(let o=0;o<s;o++)this._pool[o]=new this._ctor}n=this._pool.pop()}return this._acquireFunction?this._acquireFunction(n,...r):ks(n)&&n.acquire(...r),n}release(r){r&&!ut.test.disabled&&(this._releaseFunction?this._releaseFunction(r):Os(r)&&r.release(),this._pool.push(r))}prune(r=this._initialSize){if(!(r>=this._pool.length)){for(let n=r;n<this._pool.length;++n){const s=this._pool[n];this._dispose(s)}this._pool.length=r}}_dispose(r){r.dispose&&typeof r.dispose=="function"&&r.dispose()}};Be.test={disabled:!1};let Ms=class{constructor(r,n){this._observers=r,this._observer=n}remove(){wn(this._observers,this._observer)}},Es=class{constructor(){this._observers=null,this.destroyed=!1}observe(r){if(this.destroyed||r.destroyed)return Ts;this._observers==null&&(this._observers=[]);const n=this._observers;let s=!1,o=!1;const a=n.length;for(let l=0;l<a;++l){const c=n[l];if(c.destroyed)o=!0;else if(c===r){s=!0;break}}return s||(n.push(r),o&&this._removeDestroyedObservers()),new Ms(n,r)}_removeDestroyedObservers(){const r=this._observers;if(!r||r.length===0)return;const n=r.length;let s=0;for(let o=0;o<n;++o){for(;o+s<n&&r[o+s].destroyed;)++s;if(s>0){if(!(o+s<n))break;r[o]=r[o+s]}}r.length=n-s}destroy(){if(this.destroyed)return;this.destroyed=!0;const r=this._observers;if(r!=null){for(const n of r)n.onCommitted();this._observers=null}}};const Ts={remove:()=>{}};var v;(function(t){t[t.DEFAULTS=0]="DEFAULTS",t[t.COMPUTED=1]="COMPUTED",t[t.SERVICE=2]="SERVICE",t[t.PORTAL_ITEM=3]="PORTAL_ITEM",t[t.WEB_SCENE=4]="WEB_SCENE",t[t.WEB_MAP=5]="WEB_MAP",t[t.USER=6]="USER"})(v||(v={}));const Ei=v.USER+1;function xr(t){switch(t){case"defaults":return v.DEFAULTS;case"service":return v.SERVICE;case"portal-item":return v.PORTAL_ITEM;case"web-scene":return v.WEB_SCENE;case"web-map":return v.WEB_MAP;case"user":return v.USER;default:return null}}function ft(t){switch(t){case v.DEFAULTS:return"defaults";case v.SERVICE:return"service";case v.PORTAL_ITEM:return"portal-item";case v.WEB_SCENE:return"web-scene";case v.WEB_MAP:return"web-map";case v.USER:return"user"}return P(void 0)}function Cs(t){return ft(t)}let Zt=class{constructor(r,n,s){this.properties=r,this.propertyName=n,this.metadata=s,this.observerObject=new js,this._accessed=null,this._handles=null,this.observerObject.flags=_.Dirty|(s.nonNullable?_.NonNullable:0)|(s.hasOwnProperty("value")?_.HasDefaultValue:0)|(s.get===void 0?_.DepTrackingInitialized:0)|(s.dependsOn===void 0?_.AutoTracked:0),Kt.register(this,this.observerObject)}destroy(){this.observerObject.destroy(),this._accessed=null,this._clearObservationHandles(),Kt.unregister(this)}getComputed(){const r=this.observerObject;ze(r);const n=this.properties.store,s=this.propertyName,o=r.flags,a=n.get(s);if(o&_.Computing||~o&_.Dirty&&n.has(s))return a;r.flags|=_.Computing;const l=this.properties.host;let c;o&_.AutoTracked?c=U(this,this.metadata.get,l):(Er(l,this),c=this.metadata.get.call(l)),n.set(s,c,v.COMPUTED);const u=n.get(s);return u===a?r.flags&=~_.Dirty:ns(this.commit,this),r.flags&=~_.Computing,u}onObservableAccessed(r){r!==this.observerObject&&(this._accessed===null&&(this._accessed=[]),this._accessed.includes(r)||this._accessed.push(r))}onTrackingEnd(){this._clearObservationHandles();const r=this.observerObject;r.flags|=_.DepTrackingInitialized;const n=this._accessed;if(n===null)return;let s=this._handles;s===null&&(s=this._handles=[]);for(let o=0;o<n.length;++o)s.push(n[o].observe(r));n.length=0}notifyChange(){const r=this.observerObject;r.onInvalidated(),r.onCommitted()}invalidate(){this.observerObject.onInvalidated()}commit(){const r=this.observerObject;r.flags&=~_.Dirty,r.onCommitted()}_clearObservationHandles(){const r=this._handles;if(r!==null){for(let n=0;n<r.length;++n)r[n].remove();r.length=0}}},js=class extends Es{constructor(){super(...arguments),this.flags=0}onInvalidated(){~this.flags&_.Overriden&&(this.flags|=_.Dirty);const r=this._observers;if(r&&r.length>0)for(const n of r)n.onInvalidated()}onCommitted(){const r=this._observers;if(r&&r.length>0){const n=r.slice();for(const s of n)s.onCommitted()}}destroy(){this.flags&_.Dirty&&this.onCommitted(),super.destroy()}};const Kt=new FinalizationRegistry(t=>{t.destroy()});let Ns=class Dr{constructor(){this._values=new Map,this.multipleOriginsSupported=!1}clone(r){const n=new Dr;return this._values.forEach((s,o)=>{r&&r.has(o)||n.set(o,k(s))}),n}get(r){return this._values.get(r)}originOf(){return v.USER}keys(){return[...this._values.keys()]}set(r,n){this._values.set(r,n)}delete(r){this._values.delete(r)}has(r){return this._values.has(r)}forEach(r){this._values.forEach(r)}};function pe(t,r,n){return t!==void 0}function Xt(t,r,n,s){return t!==void 0&&(!(n==null&&t.observerObject.flags&_.NonNullable)||(s.lifecycle,j.INITIALIZING,!1))}function Ps(t){return t&&typeof t.destroy=="function"}$.getLogger("esri.core.accessorSupport.Properties");let Rs=class{constructor(r){this.host=r,this.properties=new Map,this.ctorArgs=null,this.destroyed=!1,this.lifecycle=j.INITIALIZING,this.store=new Ns,this._origin=v.USER;const n=P(this.host.constructor.__accessorMetadata__);for(const s in n){const o=new Zt(this,s,n[s]);this.properties.set(s,o)}this.metadatas=n}initialize(){this.lifecycle=j.CONSTRUCTING}constructed(){this.lifecycle=j.CONSTRUCTED}destroy(){this.destroyed=!0;for(const[r,n]of this.properties){if(n.metadata.autoDestroy){const s=this.internalGet(r);s&&Ps(s)&&(s.destroy(),~n.observerObject.flags&_.NonNullable&&this._internalSet(n,null))}n.destroy()}}get initialized(){return this.lifecycle!==j.INITIALIZING}get(r){const n=this.properties.get(r);if(n.metadata.get)return n.getComputed();ze(n.observerObject);const s=this.store;return s.has(r)?s.get(r):n.metadata.value}originOf(r){const n=this.store.originOf(r);if(n===void 0){const s=this.properties.get(r);if(s!==void 0&&s.observerObject.flags&_.HasDefaultValue)return"defaults"}return ft(n)}has(r){return!!this.properties.has(r)&&this.store.has(r)}keys(){return[...this.properties.keys()]}internalGet(r){const n=this.properties.get(r);if(pe(n))return this.store.has(r)?this.store.get(r):n.metadata.value}internalSet(r,n){const s=this.properties.get(r);pe(s)&&this._internalSet(s,n)}getDependsInfo(r,n,s){const o=this.properties.get(n);if(!pe(o))return"";const a=new Set,l=U({onObservableAccessed:u=>a.add(u),onTrackingEnd:()=>{}},()=>{var u;return(u=o.metadata.get)==null?void 0:u.call(r)});let c=`${s}${r.declaredClass.split(".").pop()}.${n}: ${l}
`;if(a.size===0)return c;s+="  ";for(const u of a){if(!(u instanceof Zt))continue;const f=u.properties.host,h=u.propertyName,d=z(f);c+=d?d.getDependsInfo(f,h,s):`${s}${h}: undefined
`}return c}setAtOrigin(r,n,s){const o=this.properties.get(r);if(pe(o))return this._setAtOrigin(o,n,s)}isOverridden(r){const n=this.properties.get(r);return n!==void 0&&!!(n.observerObject.flags&_.Overriden)}clearOverride(r){const n=this.properties.get(r),s=n==null?void 0:n.observerObject;s&&s.flags&_.Overriden&&(s.flags&=~_.Overriden,n.notifyChange())}override(r,n){const s=this.properties.get(r);if(!Xt(s,r,n,this))return;const o=s.metadata.cast;if(o){const a=this._cast(o,n),{valid:l,value:c}=a;if(Xe.release(a),!l)return;n=c}s.observerObject.flags|=_.Overriden,this._internalSet(s,n)}set(r,n){const s=this.properties.get(r);if(!Xt(s,r,n,this))return;const o=s.metadata.cast;if(o){const l=this._cast(o,n),{valid:c,value:u}=l;if(Xe.release(l),!c)return;n=u}const a=s.metadata.set;a?a.call(this.host,n):this._internalSet(s,n)}setDefaultOrigin(r){this._origin=xr(r)}getDefaultOrigin(){return ft(this._origin)}notifyChange(r){const n=this.properties.get(r);n!==void 0&&n.notifyChange()}invalidate(r){const n=this.properties.get(r);n!==void 0&&n.invalidate()}commit(r){const n=this.properties.get(r);n!==void 0&&n.commit()}_internalSet(r,n){const s=this.lifecycle!==j.INITIALIZING?this._origin:v.DEFAULTS;this._setAtOrigin(r,n,s)}_setAtOrigin(r,n,s){const o=this.store,a=r.propertyName;o.has(a,s)&&$t(n,o.get(a))&&~r.observerObject.flags&_.Overriden&&s===o.originOf(a)||(r.invalidate(),o.set(a,n,s),r.commit(),Mr(this.host,r))}_cast(r,n){const s=Xe.acquire();return s.valid=!0,s.value=n,r&&(s.value=r.call(this.host,n,s)),s}},Gs=class{constructor(){this.value=null,this.valid=!0}acquire(){this.valid=!0}release(){this.value=null}};const Xe=new Be(Gs);function Is(t){t.length=0}let Wr=class{constructor(r=50,n=50){this._pool=new Be(Array,void 0,Is,n,r)}acquire(){return this._pool.acquire()}release(r){this._pool.release(r)}prune(){this._pool.prune(0)}static acquire(){return Ye.acquire()}static release(r){return Ye.release(r)}static prune(){Ye.prune()}};const Ye=new Wr(100);let Us=class extends Be{constructor(){super(...arguments),this._set=new Set}destroy(){super.destroy(),this._set=bn(this._set)}acquire(...r){const n=super.acquire(...r);return this._set.delete(n),n}release(r){r&&!this._set.has(r)&&(super.release(r),this._set.add(r))}_dispose(r){this._set.delete(r),super._dispose(r)}};const ge=[];function qr(t){ge.push(t),ge.length===1&&queueMicrotask(()=>{const r=ge.slice();ge.length=0;for(const n of r)n()})}let zr=class{constructor(r,n=30){this.name=r,this._counter=0,this._samples=new Array(n)}record(r){w(r)&&(this._samples[++this._counter%this._samples.length]=r)}get median(){return this._samples.slice().sort((r,n)=>r-n)[Math.floor(this._samples.length/2)]}get average(){return this._samples.reduce((r,n)=>r+n,0)/this._samples.length}get last(){return this._samples[this._counter%this._samples.length]}};var ht;(function(t){const r=(a,l,c,u)=>{let f=l,h=l;const d=c>>>1,g=a[f-1];for(;h<=d;){h=f<<1,h<c&&u(a[h-1],a[h])<0&&++h;const F=a[h-1];if(u(F,g)<=0)break;a[f-1]=F,f=h}a[f-1]=g},n=(a,l)=>a<l?-1:a>l?1:0;function s(a,l,c,u){l===void 0&&(l=0),c===void 0&&(c=a.length),u===void 0&&(u=n);for(let h=c>>>1;h>l;h--)r(a,h,c,u);const f=l+1;for(let h=c-1;h>l;h--){const d=a[l];a[l]=a[h],a[h]=d,r(a,f,h,u)}}function*o(a,l,c,u){l===void 0&&(l=0),c===void 0&&(c=a.length),u===void 0&&(u=n);for(let h=c>>>1;h>l;h--)r(a,h,c,u),yield;const f=l+1;for(let h=c-1;h>l;h--){const d=a[l];a[l]=a[h],a[h]=d,r(a,f,h,u),yield}}t.sort=s,t.iterableSort=o})(ht||(ht={}));const Yt=ht,xs=1.5,Ds=1.1;let Br=class{constructor(r){this.data=[],this._length=0,this._allocator=void 0,this._deallocator=()=>null,this._shrink=()=>{},this._hint=new $n,r&&(r.initialSize&&(this.data=new Array(r.initialSize)),r.allocator&&(this._allocator=r.allocator),r.deallocator!==void 0&&(this._deallocator=r.deallocator),r.shrink&&(this._shrink=()=>Qt(this)))}toArray(){return this.data.slice(0,this.length)}filter(r){const n=new Array;for(let s=0;s<this._length;s++){const o=this.data[s];r(o)&&n.push(o)}return n}getItemAt(r){if(!(r<0||r>=this._length))return this.data[r]}includes(r,n){const s=this.data.indexOf(r,n);return s!==-1&&s<this.length}get length(){return this._length}set length(r){if(r>this._length){if(this._allocator){for(;this._length<r;)this.data[this._length++]=this._allocator(this.data[this._length]);return}this._length=r}else{if(this._deallocator)for(let n=r;n<this._length;++n)this.data[n]=this._deallocator(this.data[n]);this._length=r,this._shrink()}}clear(){this.length=0}prune(){this.clear(),this.data=[]}push(r){this.data[this._length++]=r}pushArray(r,n=r.length){for(let s=0;s<n;s++)this.data[this._length++]=r[s]}fill(r,n){for(let s=0;s<n;s++)this.data[this._length++]=r}pushNew(){this._allocator&&(this.data[this.length]=this._allocator(this.data[this.length]));const r=this.data[this._length];return++this._length,r}unshift(r){this.data.unshift(r),this._length++,Qt(this)}pop(){if(this.length===0)return;const r=this.data[this.length-1];return this.length=this.length-1,this._shrink(),r}remove(r){const n=Gt(this.data,r,this.length,this._hint);if(n!==-1)return this.data.splice(n,1),this.length=this.length-1,r}removeUnordered(r){return this.removeUnorderedIndex(Gt(this.data,r,this.length,this._hint))}removeUnorderedIndex(r){if(!(r>=this.length||r<0))return this.swapElements(r,this.length-1),this.pop()}removeUnorderedMany(r,n=r.length,s){this.length=Sn(this.data,r,this.length,n,this._hint,s),this._shrink()}front(){if(this.length!==0)return this.data[0]}back(){if(this.length!==0)return this.data[this.length-1]}swapElements(r,n){if(r>=this.length||n>=this.length||r===n)return;const s=this.data[r];this.data[r]=this.data[n],this.data[n]=s}sort(r){Yt.sort(this.data,0,this.length,r)}iterableSort(r){return Yt.iterableSort(this.data,0,this.length,r)}some(r,n){for(let s=0;s<this.length;++s)if(r.call(n,this.data[s],s,this.data))return!0;return!1}find(r,n){for(let s=0;s<this.length;++s){const o=this.data[s];if(r.call(n,o,s))return o}}filterInPlace(r,n){let s=0;for(let o=0;o<this._length;++o){const a=this.data[o];r.call(n,a,o,this.data)&&(this.data[o]=this.data[s],this.data[s]=a,s++)}if(this._deallocator)for(let o=s;o<this._length;o++)this.data[o]=this._deallocator(this.data[o]);return this._length=s,this._shrink(),this}forAll(r,n){const s=this.length,o=this.data;for(let a=0;a<s;++a)r.call(n,o[a],a,o)}forEach(r,n){for(let s=0;s<this.length;++s)r.call(n,this.data[s],s,this.data)}map(r,n){const s=new Array(this.length);for(let o=0;o<this.length;++o)s[o]=r.call(n,this.data[o],o,this.data);return s}reduce(r,n){let s=n;for(let o=0;o<this.length;++o)s=r(s,this.data[o],o,this.data);return s}has(r){const n=this.length,s=this.data;for(let o=0;o<n;++o)if(s[o]===r)return!0;return!1}};function Qt(t){t.data.length>xs*t.length&&(t.data.length=Math.floor(t.length*Ds))}function Ws(t){return{setTimeout:(r,n)=>{const s=t.setTimeout(r,n);return{remove:()=>t.clearTimeout(s)}}}}const qs=Ws(globalThis);function Fr(t){return t&&(typeof t.on=="function"||typeof t.addEventListener=="function")}function zs(t,r,n){if(!Fr(t))throw new TypeError("target is not a Evented or EventTarget object");if("on"in t)return t.on(r,n);if(Array.isArray(r)){const s=r.slice();for(const o of s)t.addEventListener(o,n);return{remove(){for(const o of s)t.removeEventListener(o,n)}}}return t.addEventListener(r,n),{remove(){t.removeEventListener(r,n)}}}function Lr(t,r,n){if(!Fr(t))throw new TypeError("target is not a Evented or EventTarget object");if("once"in t)return t.once(r,n);const s=zs(t,r,o=>{s.remove(),n.call(t,o)});return{remove(){s.remove()}}}const Bs={Win:"Meta",Scroll:"ScrollLock",Spacebar:" ",Down:"ArrowDown",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Del:"Delete",Apps:"ContextMenu",Esc:"Escape",Multiply:"*",Add:"+",Subtract:"-",Decimal:".",Divide:"/"};function Ui({key:t}){return Bs[t]||t}function X(t="Aborted"){return new he("AbortError",t)}function Jr(t,r="Aborted"){if(Hr(t))throw X(r)}function Fe(t){return w(t)?"aborted"in t?t:t.signal:t}function Hr(t){const r=Fe(t);return w(r)&&r.aborted}function xi(t){if(Et(t))throw t}function Di(t){if(!Et(t))throw t}function dt(t,r){const n=Fe(t);if(!A(n)){if(!n.aborted)return Lr(n,"abort",()=>r());r()}}function Wi(t,r){const n=Fe(t);if(!A(n))return Jr(n),Lr(n,"abort",()=>r(X()))}function qi(t,r){const n=Fe(r);return A(n)?t:new Promise((s,o)=>{let a=dt(r,()=>o(X()));const l=()=>a=An(a);t.then(l,l),t.then(s,o)})}function Et(t){return(t==null?void 0:t.name)==="AbortError"}async function zi(t){try{return await t}catch(r){if(!Et(r))throw r;return}}function er(){let t=null;const r=new Promise((n,s)=>{t={promise:void 0,resolve:n,reject:s}});return t.promise=r,t}async function Vr(t){if(!t)return;if(typeof t.forEach!="function"){const n=Object.keys(t),s=n.map(l=>t[l]),o=await Vr(s),a={};return n.map((l,c)=>a[l]=o[c]),a}const r=t;return new Promise(n=>{const s=[];let o=r.length;o===0&&n(s),r.forEach(a=>{const l={promise:a||Promise.resolve(a)};s.push(l),l.promise.then(c=>{l.value=c}).catch(c=>{l.error=c}).then(()=>{--o,o===0&&n(s)})})})}async function Bi(t){return(await Vr(t)).filter(r=>!!r.value).map(r=>r.value)}function Fs(t,r,n){const s=new AbortController;return dt(n,()=>s.abort()),new Promise((o,a)=>{let l=setTimeout(()=>{l=0,o(r)},t);dt(s,()=>{l&&(clearTimeout(l),a(X()))})})}function Ls(t){return t&&typeof t.then=="function"}function tr(t){return Ls(t)?t:Promise.resolve(t)}function Fi(t,r=-1){let n,s,o,a,l=null;const c=(...u)=>{if(n){s=u,a&&a.reject(X()),a=er();const g=P(a.promise);if(l){const F=l;l=null,F.abort()}return g}if(o=a||er(),a=null,r>0){const g=new AbortController;n=tr(t(...u,g.signal));const F=n;Fs(r).then(()=>{n===F&&(a?g.abort():l=g)})}else n=1,n=tr(t(...u));const f=()=>{const g=s;s=o=n=l=null,g!=null&&c(...g)},h=n,d=o;return h.then(f,f),h.then(d.resolve,d.reject),P(d.promise)};return c}function Js(){let t,r;const n=new Promise((o,a)=>{t=o,r=a}),s=o=>{t(o)};return s.resolve=o=>t(o),s.reject=o=>r(o),s.timeout=(o,a)=>qs.setTimeout(()=>s.reject(a),o),s.promise=n,s}async function Li(t){await Promise.resolve(),Jr(t)}function Ji(t){return t}function Hi(t){return .001*t}let Hs=class{constructor(r){this.phases=r,this.paused=!1,this.ticks=-1,this.removed=!1}},Vs=class{constructor(r){this.callback=r,this.isActive=!0}remove(){this.isActive=!1}},pt=0;const Y={time:0,deltaTime:0,elapsedFrameTime:0,frameDuration:0},gt=["prepare","preRender","render","postRender","update","finish"],yt=[],q=new Br;let Zs=class{constructor(r){this._task=r}remove(){this._task.removed=!0}pause(){this._task.paused=!0}resume(){this._task.paused=!1}};const Ne={frameTasks:q,willDispatch:!1,clearFrameTasks:Xs,dispatch:Xr,executeFrameTasks:Ys};function Ks(t){const r=new Vs(t);return yt.push(r),Ne.willDispatch||(Ne.willDispatch=!0,qr(Xr)),r}function Xi(t){const r=new Hs(t);return q.push(r),Pe==null&&(pt=performance.now(),Pe=requestAnimationFrame(Zr)),new Zs(r)}let Pe=null;function Xs(t=!1){q.forAll(r=>{r.removed=!0}),t&&Kr()}function Zr(){const t=performance.now();Pe=null,Pe=q.length>0?requestAnimationFrame(Zr):null,Ne.executeFrameTasks(t)}function Ys(t){const r=t-pt;pt=t;const n=1e3/60,s=Math.max(0,r-n);Y.time=t,Y.frameDuration=n-s;for(let o=0;o<gt.length;o++){const a=performance.now(),l=gt[o];q.forAll(c=>{var u;c.paused||c.removed||(o===0&&c.ticks++,c.phases[l]&&(Y.elapsedFrameTime=performance.now()-t,Y.deltaTime=c.ticks===0?0:r,(u=c.phases[l])==null||u.call(c,Y)))}),Qs[o].record(performance.now()-a)}Kr(),eo.record(performance.now()-t)}const ye=new Br;function Kr(){q.forAll(t=>{t.removed&&ye.push(t)}),q.removeUnorderedMany(ye.data,ye.length),ye.clear()}function Xr(){for(;yt.length;){const t=P(yt.shift());t.isActive&&t.callback()}Ne.willDispatch=!1}function Yi(t=1,r){const n=Js(),s=()=>{Hr(r)?n.reject(X()):t===0?n():(--t,qr(()=>s()))};return s(),n.promise}const Qs=gt.map(t=>new zr(t)),eo=new zr("total");function to(t,r){for(const n of t.entries())if(r(n[0]))return!0;return!1}let ro=0;function Qe(){return++ro}let Tt=class{constructor(r){this._notify=r,this._accessed=[],this._handles=[],this._observerObject=new no(this._notify),rr.register(this,this._observerObject)}destroy(){var r;this._accessed.length=0,(r=this._observerObject)==null||r.destroy(),this.clear(),rr.unregister(this)}onObservableAccessed(r){const n=this._accessed;n.includes(r)||n.push(r)}onTrackingEnd(){const r=this._handles,n=this._accessed,s=this._observerObject;for(let o=0;o<n.length;++o)r.push(n[o].observe(s));n.length=0}clear(){const r=this._handles;for(let n=0;n<r.length;++n)r[n].remove();r.length=0}},no=class{constructor(r){this._notify=r,this._invalidCount=0,this.destroyed=!1}onInvalidated(){this._invalidCount++}onCommitted(){if(this.destroyed)return;const r=this._invalidCount;if(r===1)return this._invalidCount=0,void this._notify();this._invalidCount=r>0?r-1:0}destroy(){this.destroyed=!0,this._notify=so}};const rr=new FinalizationRegistry(t=>{t.destroy()});function so(){}let H=!1;const Re=[];function Yr(t,r){let n=new Tt(a),s=null,o=!1;function a(){if(!n||o)return;if(H)return void en(a);const c=s;n.clear(),H=!0,o=!0,s=U(n,t),o=!1,H=!1,r(s,c),tn()}function l(){n&&(n.destroy(),n=null,s=null)}return o=!0,s=U(n,t),o=!1,{remove:l}}function Qr(t,r){let n=new Tt(o),s=null;function o(){r(s,l)}function a(){n&&(n.destroy(),n=null),s=null}function l(){return n?(n.clear(),s=U(n,t),s):null}return l(),{remove:a}}function t0(t){let r=new Tt(s),n=!1;function s(){r&&!n&&(H?en(s):(r.clear(),H=!0,n=!0,U(r,t),n=!1,H=!1,tn()))}function o(){r&&(r.destroy(),r=null)}return n=!0,U(r,t),n=!1,{remove:o}}function en(t){Re.includes(t)||Re.unshift(t)}function tn(){for(;Re.length;)Re.pop()()}var re;(function(t){t[t.Untracked=0]="Untracked",t[t.Tracked=1]="Tracked"})(re||(re={}));let ie=class{constructor(){this.uid=Qe(),this.removed=!1,this.type=null,this.oldValue=null,this.callback=null,this.getValue=null,this.target=null,this.path=null,this.equals=null}static acquireUntracked(r,n,s,o,a){return this.pool.acquire(re.Untracked,r,n,s,o,a,$t)}static acquireTracked(r,n,s,o){return this.pool.acquire(re.Tracked,r,n,s,null,null,o)}notify(r,n){this.type===re.Untracked?this.callback.call(this.target,r,n,this.path,this.target):this.callback.call(null,r,n)}acquire(r,n,s,o,a,l,c){this.uid=Qe(),this.removed=!1,this.type=r,this.oldValue=n,this.callback=s,this.getValue=o,this.target=a,this.path=l,this.equals=c}release(){this.target=this.path=this.oldValue=this.callback=this.getValue=null,this.uid=Qe(),this.removed=!0}};ie.pool=new Us(ie);const Se=new Wr,N=new Set;let Ge;function Ie(t){N.delete(t),N.add(t),Ge||(Ge=Ks(ao))}function oo(t){if(t.removed)return;const r=t.oldValue,n=t.getValue();t.equals(r,n)||(t.oldValue=n,t.notify(n,r))}function io(t){for(const r of N.values())r.target===t&&(r.removed=!0)}function ao(){let t=10;for(;Ge&&t--;){Ge=null;const r=lo(),n=Se.acquire();for(const s of r){const o=s.uid;oo(s),o===s.uid&&s.removed&&n.push(s)}for(const s of N)s.removed&&(n.push(s),N.delete(s));for(const s of n)ie.pool.release(s);Se.release(n),Se.release(r),_t.forEach(s=>s())}}function lo(){const t=Se.acquire();t.length=N.size;let r=0;for(const n of N)t[r]=n,++r;return N.clear(),t}const _t=new Set;function n0(t){return _t.add(t),{remove(){_t.delete(t)}}}function co(t,r,n){let s=_r(t,r,n,(o,a,l)=>{let c,u,f=Qr(()=>ue(o,a),(h,d)=>{o.__accessor__.destroyed||c&&c.uid!==u?s.remove():(c||(c=ie.acquireUntracked(h,l,d,o,a),u=c.uid),Ie(c))});return{remove:mr(()=>{f.remove(),c&&(c.uid!==u||c.removed||(c.removed=!0,Ie(c)),c=null),s=f=null})}});return s}function uo(t,r,n){const s=_r(t,r,n,(o,a,l)=>{let c=!1;return Yr(()=>ue(o,a),(u,f)=>{o.__accessor__.destroyed?s.remove():c||(c=!0,$t(f,u)||l.call(o,u,f,a,o),c=!1)})});return s}function fo(t,r,n,s=!1){return!t.__accessor__||t.__accessor__.destroyed?{remove(){}}:s?uo(t,r,n):co(t,r,n)}function ho(t,r,n){let s,o,a=Qr(t,(l,c)=>{s&&s.uid!==o?a.remove():(s||(s=ie.acquireTracked(l,r,c,n),o=s.uid),Ie(s))});return{remove:mr(()=>{a.remove(),s&&(s.uid!==o||s.removed||(s.removed=!0,Ie(s)),s=null),a=null})}}function po(t,r,n){let s=!1;return Yr(t,(o,a)=>{s||(s=!0,n(a,o)||r(o,a),s=!1)})}function s0(t,r,n=!1,s=On){return n?po(t,r,s):ho(t,r,s)}function o0(t){return to(N,r=>r.oldValue===t)}var rn,nn;function go(t){var r;if(t==null)return{value:t};if(Array.isArray(t))return{type:[t[0]],value:null};switch(typeof t){case"object":return(r=t.constructor)!=null&&r.__accessorMetadata__||t instanceof Date?{type:t.constructor,value:t}:t;case"boolean":return{type:Boolean,value:t};case"string":return{type:String,value:t};case"number":return{type:Number,value:t};case"function":return{type:t,value:null};default:return}}const x=Symbol("Accessor-Handles"),mt=Symbol("Accessor-Initialized");let yo=class sn{static createSubclass(r={}){if(Array.isArray(r))throw new Error("Multi-inheritance unsupported since 4.16");const{properties:n,declaredClass:s,constructor:o}=r;delete r.declaredClass,delete r.properties,delete r.constructor;const a=this;class l extends a{constructor(...u){super(...u),this.inherited=null,o&&o.apply(this,u)}}fe(l.prototype);for(const c in r){const u=r[c];l.prototype[c]=typeof u=="function"?function(...f){const h=this.inherited;let d;this.inherited=function(...g){if(a.prototype[c])return a.prototype[c].apply(this,g)};try{d=u.apply(this,f)}catch(g){throw this.inherited=h,g}return this.inherited=h,d}:r[c]}for(const c in n){const u=go(n[c]);m(u)(l.prototype,c)}return K(s)(l)}constructor(...r){if(this[rn]=null,this[nn]=!1,this.constructor===sn)throw new Error("[accessor] cannot instantiate Accessor. This can be fixed by creating a subclass of Accessor");Object.defineProperty(this,"__accessor__",{enumerable:!1,value:new Rs(this)}),r.length>0&&this.normalizeCtorArgs&&(this.__accessor__.ctorArgs=this.normalizeCtorArgs.apply(this,r))}postscript(r){const n=this.__accessor__,s=n.ctorArgs||r;n.initialize(),s&&(this.set(s),n.ctorArgs=null),n.constructed(),this.initialize(),this[mt]=!0}initialize(){}[Ar](){this[x]=kn(this[x])}destroy(){this.destroyed||(io(this),this.__accessor__.destroy())}get constructed(){return this.__accessor__&&this.__accessor__.initialized||!1}get initialized(){return this[mt]}get destroyed(){return this.__accessor__&&this.__accessor__.destroyed||!1}commitProperty(r){this.get(r)}get(r){return Ee(this,r)}hasOwnProperty(r){return this.__accessor__?this.__accessor__.has(r):Object.prototype.hasOwnProperty.call(this,r)}keys(){return this.__accessor__?this.__accessor__.keys():[]}set(r,n){return Te(this,r,n),this}watch(r,n,s){return fo(this,r,n,s)}own(r){this.addHandles(r)}addHandles(r,n){let s=this[x];A(s)&&(s=this[x]=new As),s.add(r,n)}removeHandles(r){const n=this[x];A(n)||n.remove(r)}hasHandles(r){const n=this[x];return!!w(n)&&n.has(r)}_override(r,n){n===void 0?this.__accessor__.clearOverride(r):this.__accessor__.override(r,n)}_clearOverride(r){return this.__accessor__.clearOverride(r)}_overrideIfSome(r,n){n==null?this.__accessor__.clearOverride(r):this.__accessor__.override(r,n)}_isOverridden(r){return this.__accessor__.isOverridden(r)}notifyChange(r){this.__accessor__.notifyChange(r)}_get(r){return this.__accessor__.internalGet(r)}_set(r,n){return this.__accessor__.internalSet(r,n),this}};rn=x,nn=mt;let _o=class on{constructor(){this._values=new Map,this.multipleOriginsSupported=!1}clone(r){const n=new on;return this._values.forEach((s,o)=>{r&&r.has(o)||n.set(o,k(s.value),s.origin)}),n}get(r,n){n=this._normalizeOrigin(n);const s=this._values.get(r);return n==null||(s==null?void 0:s.origin)===n?s==null?void 0:s.value:void 0}originOf(r){var n;return((n=this._values.get(r))==null?void 0:n.origin)??v.USER}keys(r){r=this._normalizeOrigin(r);const n=[...this._values.keys()];return r==null?n:n.filter(s=>{var o;return((o=this._values.get(s))==null?void 0:o.origin)===r})}set(r,n,s){if((s=this._normalizeOrigin(s))===v.DEFAULTS){const o=this._values.get(r);if(o&&o.origin!=null&&o.origin>s)return}this._values.set(r,new mo(n,s))}delete(r,n){var s;(n=this._normalizeOrigin(n))!=null&&((s=this._values.get(r))==null?void 0:s.origin)!==n||this._values.delete(r)}has(r,n){var s;return(n=this._normalizeOrigin(n))!=null?((s=this._values.get(r))==null?void 0:s.origin)===n:this._values.has(r)}forEach(r){this._values.forEach(({value:n},s)=>r(n,s))}_normalizeOrigin(r){if(r!=null)return r===v.DEFAULTS?r:v.USER}},mo=class{constructor(r,n){this.value=r,this.origin=n}};function vo(t,r,n){r.keys().forEach(o=>{n.set(o,r.get(o),v.DEFAULTS)});const s=t.metadatas;Object.keys(s).forEach(o=>{t.internalGet(o)&&n.set(o,t.internalGet(o),v.DEFAULTS)})}function wo(t,r,n){if(!t||!t.read||t.read.enabled===!1||!t.read.source)return!1;const s=t.read.source;if(typeof s=="string"){if(s===r||s.includes(".")&&s.indexOf(r)===0&&Wt(s,n))return!0}else for(const o of s)if(o===r||o.includes(".")&&o.indexOf(r)===0&&Wt(o,n))return!0;return!1}function bo(t){return t&&(!t.read||t.read.enabled!==!1&&!t.read.source)}function $o(t,r,n,s,o){let a=lt(r[n],o);bo(a)&&(t[n]=!0);for(const l of Object.getOwnPropertyNames(r))a=lt(r[l],o),wo(a,n,s)&&(t[l]=!0)}function So(t,r,n,s){const o=n.metadatas,a=Mt(o[r],"any",s),l=a&&a.default;if(l===void 0)return;const c=typeof l=="function"?l.call(t,r,s):l;c!==void 0&&n.set(r,c)}const an={origin:"service"};function Ao(t,r,n=an){if(!r||typeof r!="object")return;const s=z(t),o=s.metadatas,a={};for(const l of Object.getOwnPropertyNames(r))$o(a,o,l,r,n);s.setDefaultOrigin(n.origin);for(const l of Object.getOwnPropertyNames(a)){const c=lt(o[l],n).read,u=c&&c.source;let f;f=u&&typeof u=="string"?ue(r,u):r[l],c&&c.reader&&(f=c.reader.call(t,f,r,n)),f!==void 0&&s.set(l,f)}if(!n||!n.ignoreDefaults){s.setDefaultOrigin("defaults");for(const l of Object.getOwnPropertyNames(o))a[l]||So(t,l,s,n)}s.setDefaultOrigin("user")}function a0(t,r,n,s=an){var a;const o={...s,messages:[]};n(o),(a=o.messages)==null||a.forEach(l=>{l.type!=="warning"||t.loaded?s&&s.messages&&s.messages.push(l):t.loadWarnings.push(l)})}function Oo(t,r,n,s,o){var l,c;const a={};return(c=(l=r.write)==null?void 0:l.writer)==null||c.call(t,s,a,n,o),a}function ln(t,r,n,s,o,a){if(!s||!s.write)return!1;const l=t.get(n);if(!o&&s.write.overridePolicy){const c=s.write.overridePolicy.call(t,l,n,a);c!==void 0&&(o=c)}if(o||(o=s.write),!o||o.enabled===!1)return!1;if((l===null&&!o.allowNull&&!o.writerEnsuresNonNull||l===void 0)&&o.isRequired){const c=new he("web-document-write:property-required",`Missing value for required property '${n}' on '${t.declaredClass}'`,{propertyName:n,target:t});return c&&a&&a.messages?a.messages.push(c):c&&!a&&$.getLogger("esri.core.accessorSupport.write").error(c.name,c.message),!1}return!(l===void 0||l===null&&!o.allowNull&&!o.writerEnsuresNonNull||(!r.store.multipleOriginsSupported||r.store.originOf(n)===v.DEFAULTS)&&ko(t,n,a,s,l)||!o.ignoreOrigin&&a&&a.origin&&r.store.multipleOriginsSupported&&r.store.originOf(n)<xr(a.origin))}function ko(t,r,n,s,o){const a=s.default;if(a===void 0)return!1;if(s.defaultEquals!=null)return s.defaultEquals(o);if(typeof a=="function"){if(Array.isArray(o)){const l=a.call(t,r,n);return Mn(l,o)}return!1}return a===o}function l0(t,r,n,s){const o=z(t),a=o.metadatas,l=Ir(a[r],s);return!!l&&ln(t,o,r,l,n,s)}function Mo(t,r,n){var a,l;if(t&&typeof t.toJSON=="function"&&(!t.toJSON.isDefaultToJSON||!t.write))return ve(r,t.toJSON(n));const s=z(t),o=s.metadatas;for(const c in o){const u=Ir(o[c],n);if(!ln(t,s,c,u,void 0,n))continue;const f=t.get(c),h=Oo(t,u,u.write&&typeof u.write.target=="string"?u.write.target:c,f,n);Object.keys(h).length>0&&(r=ve(r,h),(l=(a=n==null?void 0:n.resources)==null?void 0:a.pendingOperations)!=null&&l.length&&n.resources.pendingOperations.push(Promise.all(n.resources.pendingOperations).then(()=>ve(r,h,()=>"replace-arrays"))),n&&n.writtenProperties&&n.writtenProperties.push({target:t,propName:c,oldOrigin:Cs(s.store.originOf(c)),newOrigin:n.origin}))}return r}const Eo=t=>{let r=class extends t{constructor(...n){super(...n);const s=P(z(this)),o=s.store,a=new _o;s.store=a,vo(s,o,a)}read(n,s){Ao(this,n,s)}write(n={},s){return Mo(this,n,s)}toJSON(n){return this.write({},n)}static fromJSON(n,s){return To.call(this,n,s)}};return r=p([K("esri.core.JSONSupport")],r),r.prototype.toJSON.isDefaultToJSON=!0,r};function To(t,r){if(!t)return null;if(t.declaredClass)throw new Error("JSON object is already hydrated");const n=new this;return n.read(t,r),n}let Ue=class extends Eo(yo){};Ue=p([K("esri.core.JSONSupport")],Ue);function cn(t,r,n){let s,o;return r===void 0||Array.isArray(r)?(o=t,n=r,s=[void 0]):(o=r,s=Array.isArray(t)?t:[t]),(a,l)=>{const c=a.constructor.prototype;s.forEach(u=>{const f=Sr(a,u,o);f.read&&typeof f.read=="object"||(f.read={}),f.read.reader=c[l],n&&(f.read.source=(f.read.source||[]).concat(n))})}}let Co=class{constructor(r,n={ignoreUnknown:!1,useNumericKeys:!1}){this._jsonToAPI=r,this._options=n,this.apiValues=[],this.jsonValues=[],this._apiToJSON=this._invertMap(r),this.apiValues=this._getKeysSorted(this._apiToJSON),this.jsonValues=this._getKeysSorted(this._jsonToAPI),this.read=s=>this.fromJSON(s),this.write=(s,o,a)=>{const l=this.toJSON(s);l!==void 0&&ce(a,l,o)},this.write.isJSONMapWriter=!0}toJSON(r){if(r==null)return null;if(this._apiToJSON.hasOwnProperty(r)){const n=this._apiToJSON[r];return this._options.useNumericKeys?+n:n}return this._options.ignoreUnknown?void 0:r}fromJSON(r){return r!=null&&this._jsonToAPI.hasOwnProperty(r)?this._jsonToAPI[r]:this._options.ignoreUnknown?void 0:r}_invertMap(r){const n={};for(const s in r)n[r[s]]=s;return n}_getKeysSorted(r){const n=[];for(const s in r)n.push(s);return n.sort(),n}};function Le(){return function(t,r){return new Co(t,{ignoreUnknown:!0,...r})}}let Ct=class{constructor(r,n,s,o){this.semiMajorAxis=r,this.flattening=n,this.outerAtmosphereRimWidth=s;const a=1-this.flattening;this.semiMinorAxis=this.semiMajorAxis*a,this.halfSemiMajorAxis=this.semiMajorAxis/2,this.halfCircumference=Math.PI*this.semiMajorAxis,this.metersPerDegree=this.halfCircumference/180,this.inverseFlattening=1/(1-this.flattening)-1,this.eccentricitySquared=o||2*this.flattening-this.flattening*this.flattening,this.meanRadiusSemiAxes=(2*this.semiMajorAxis+this.semiMinorAxis)/3}get radius(){return this.semiMajorAxis}};const O=new Ct(6378137,1/298.257223563,3e5,.006694379990137799),Je=new Ct(3396190,1/169.8944472236118,23e4),He=new Ct(1737400,0,0);var V;(function(t){t[t.CGCS2000=4490]="CGCS2000",t[t.GCSMARS2000=104971]="GCSMARS2000",t[t.GCSMARS2000_SPHERE=104905]="GCSMARS2000_SPHERE",t[t.GCSMOON2000=104903]="GCSMOON2000"})(V||(V={}));let e;const i={values:[1,.3048,.3048006096012192,.3047972654,.9143917962,.************,.9143984146160287,.3047994715386762,20.11676512155263,20.11678249437587,.9143985307444408,.91439523,.3047997101815088,20.1168,20.116756,5e4,15e4],units:["Meter","Foot","Foot_US","Foot_Clarke","Yard_Clarke","Link_Clarke","Yard_Sears","Foot_Sears","Chain_Sears","Chain_Benoit_1895_B","Yard_Indian","Yard_Indian_1937","Foot_Gold_Coast","Chain","Chain_Sears_1922_Truncated","50_Kilometers","150_Kilometers"],2066:5,2136:12,2155:2,2157:0,2158:0,2159:12,2160:12,2204:2,2219:0,2220:0,2254:2,2255:2,2256:1,2265:1,2266:1,2267:2,2268:2,2269:1,2270:1,2271:2,2272:2,2273:1,2294:0,2295:0,2314:3,2899:2,2900:2,2901:1,2909:1,2910:1,2911:2,2912:2,2913:1,2914:1,2992:1,2993:0,2994:1,3080:1,3089:2,3090:0,3091:2,3102:2,3141:0,3142:0,3167:14,3359:2,3360:0,3361:1,3362:0,3363:2,3364:0,3365:2,3366:3,3404:2,3405:0,3406:0,3407:3,3439:0,3440:0,3479:1,3480:0,3481:1,3482:0,3483:1,3484:0,3485:2,3486:0,3487:2,3488:0,3489:0,3490:2,3491:0,3492:2,3493:0,3494:2,3495:0,3496:2,3497:0,3498:2,3499:0,3500:2,3501:0,3502:2,3503:0,3504:2,3505:0,3506:2,3507:0,3508:2,3509:0,3510:2,3511:0,3512:2,3513:0,3514:0,3515:2,3516:0,3517:2,3518:0,3519:2,3520:0,3521:2,3522:0,3523:2,3524:0,3525:2,3526:0,3527:2,3528:0,3529:2,3530:0,3531:2,3532:0,3533:2,3534:0,3535:2,3536:0,3537:2,3538:0,3539:2,3540:0,3541:2,3542:0,3543:2,3544:0,3545:2,3546:0,3547:2,3548:0,3549:2,3550:0,3551:2,3552:0,3553:2,3582:2,3583:0,3584:2,3585:0,3586:2,3587:0,3588:1,3589:0,3590:1,3591:0,3592:0,3593:1,3598:2,3599:0,3600:2,3605:1,3606:0,3607:0,3608:2,3609:0,3610:2,3611:0,3612:2,3613:0,3614:2,3615:0,3616:2,3617:0,3618:2,3619:0,3620:2,3621:0,3622:2,3623:0,3624:2,3625:0,3626:2,3627:0,3628:2,3629:0,3630:2,3631:0,3632:2,3633:0,3634:1,3635:0,3636:1,3640:2,3641:0,3642:2,3643:0,3644:1,3645:0,3646:1,3647:0,3648:1,3649:0,3650:2,3651:0,3652:2,3653:0,3654:2,3655:0,3656:1,3657:0,3658:2,3659:0,3660:2,3661:0,3662:2,3663:0,3664:2,3668:2,3669:0,3670:2,3671:0,3672:2,3673:0,3674:2,3675:0,3676:1,3677:2,3678:0,3679:1,3680:2,3681:0,3682:1,3683:2,3684:0,3685:0,3686:2,3687:0,3688:2,3689:0,3690:2,3691:0,3692:2,3696:2,3697:0,3698:2,3699:0,3700:2,3793:0,3794:0,3812:0,3854:0,3857:0,3920:0,3978:0,3979:0,3991:2,3992:2,4026:0,4037:0,4038:0,4071:0,4082:0,4083:0,4087:0,4088:0,4217:2,4414:0,4415:0,4417:0,4434:0,4437:0,4438:2,4439:2,4462:0,4467:0,4471:0,4474:0,4559:0,4647:0,4822:0,4826:0,4839:0,5018:0,5041:0,5042:0,5048:0,5167:0,5168:0,5221:0,5223:0,5234:0,5235:0,5243:0,5247:0,5266:0,5316:0,5320:0,5321:0,5325:0,5337:0,5361:0,5362:0,5367:0,5382:0,5383:0,5396:0,5456:0,5457:0,5469:0,5472:4,5490:0,5513:0,5514:0,5523:0,5559:0,5588:1,5589:3,5596:0,5627:0,5629:0,5641:0,5643:0,5644:0,5646:2,5654:2,5655:2,5659:0,5700:0,5825:0,5836:0,5837:0,5839:0,5842:0,5844:0,5858:0,5879:0,5880:0,5887:0,5890:0,6128:1,6129:1,6141:1,6204:0,6210:0,6211:0,6307:0,6312:0,6316:0,6362:0,6391:1,6405:1,6406:0,6407:1,6408:0,6409:1,6410:0,6411:2,6412:0,6413:2,6414:0,6415:0,6416:2,6417:0,6418:2,6419:0,6420:2,6421:0,6422:2,6423:0,6424:2,6425:0,6426:2,6427:0,6428:2,6429:0,6430:2,6431:0,6432:2,6433:0,6434:2,6435:0,6436:2,6437:0,6438:2,6439:0,6440:0,6441:2,6442:0,6443:2,6444:0,6445:2,6446:0,6447:2,6448:0,6449:2,6450:0,6451:2,6452:0,6453:2,6454:0,6455:2,6456:0,6457:2,6458:0,6459:2,6460:0,6461:2,6462:0,6463:2,6464:0,6465:2,6466:0,6467:2,6468:0,6469:2,6470:0,6471:2,6472:0,6473:2,6474:0,6475:2,6476:0,6477:2,6478:0,6479:2,6484:2,6485:0,6486:2,6487:0,6488:2,6489:0,6490:2,6491:0,6492:2,6493:0,6494:1,6495:0,6496:1,6497:0,6498:0,6499:1,6500:0,6501:2,6502:0,6503:2,6504:0,6505:2,6506:0,6507:2,6508:0,6509:0,6510:2,6515:1,6516:0,6518:0,6519:2,6520:0,6521:2,6522:0,6523:2,6524:0,6525:2,6526:0,6527:2,6528:0,6529:2,6530:0,6531:2,6532:0,6533:2,6534:0,6535:2,6536:0,6537:2,6538:0,6539:2,6540:0,6541:2,6542:0,6543:2,6544:0,6545:1,6546:0,6547:1,6548:0,6549:2,6550:0,6551:2,6552:0,6553:2,6554:0,6555:2,6556:0,6557:1,6558:0,6559:1,6560:0,6561:1,6562:0,6563:2,6564:0,6565:2,6566:0,6567:0,6568:2,6569:0,6570:1,6571:0,6572:2,6573:0,6574:2,6575:0,6576:2,6577:0,6578:2,6582:2,6583:0,6584:2,6585:0,6586:2,6587:0,6588:2,6589:0,6590:2,6591:0,6592:0,6593:2,6594:0,6595:2,6596:0,6597:2,6598:0,6599:2,6600:0,6601:2,6602:0,6603:2,6605:2,6606:0,6607:2,6608:0,6609:2,6610:0,6611:0,6612:2,6613:0,6614:2,6615:0,6616:2,6617:0,6618:2,6633:2,6646:0,6703:0,6784:0,6785:1,6786:0,6787:1,6788:0,6789:1,6790:0,6791:1,6792:0,6793:1,6794:0,6795:1,6796:0,6797:1,6798:0,6799:1,6800:0,6801:1,6802:0,6803:1,6804:0,6805:1,6806:0,6807:1,6808:0,6809:1,6810:0,6811:1,6812:0,6813:1,6814:0,6815:1,6816:0,6817:1,6818:0,6819:1,6820:0,6821:1,6822:0,6823:1,6824:0,6825:1,6826:0,6827:1,6828:0,6829:1,6830:0,6831:1,6832:0,6833:1,6834:0,6835:1,6836:0,6837:1,6838:0,6839:1,6840:0,6841:1,6842:0,6843:1,6844:0,6845:1,6846:0,6847:1,6848:0,6849:1,6850:0,6851:1,6852:0,6853:1,6854:0,6855:1,6856:0,6857:1,6858:0,6859:1,6860:0,6861:1,6862:0,6863:1,6867:0,6868:1,6870:0,6875:0,6876:0,6879:0,6880:2,6884:0,6885:1,6886:0,6887:1,6915:0,6922:0,6923:2,6924:0,6925:2,6962:0,6984:0,6991:0,7128:2,7131:0,7132:2,7142:0,7257:0,7258:2,7259:0,7260:2,7261:0,7262:2,7263:0,7264:2,7265:0,7266:2,7267:0,7268:2,7269:0,7270:2,7271:0,7272:2,7273:0,7274:2,7275:0,7276:2,7277:0,7278:2,7279:0,7280:2,7281:0,7282:2,7283:0,7284:2,7285:0,7286:2,7287:0,7288:2,7289:0,7290:2,7291:0,7292:2,7293:0,7294:2,7295:0,7296:2,7297:0,7298:2,7299:0,7300:2,7301:0,7302:2,7303:0,7304:2,7305:0,7306:2,7307:0,7308:2,7309:0,7310:2,7311:0,7312:2,7313:0,7314:2,7315:0,7316:2,7317:0,7318:2,7319:0,7320:2,7321:0,7322:2,7323:0,7324:2,7325:0,7326:2,7327:0,7328:2,7329:0,7330:2,7331:0,7332:2,7333:0,7334:2,7335:0,7336:2,7337:0,7338:2,7339:0,7340:2,7341:0,7342:2,7343:0,7344:2,7345:0,7346:2,7347:0,7348:2,7349:0,7350:2,7351:0,7352:2,7353:0,7354:2,7355:0,7356:2,7357:0,7358:2,7359:0,7360:2,7361:0,7362:2,7363:0,7364:2,7365:0,7366:2,7367:0,7368:2,7369:0,7370:2,7877:0,7878:0,7882:0,7883:0,7887:0,7899:0,7991:0,7992:0,8035:2,8036:2,8058:0,8059:0,8082:0,8083:0,8088:0,8090:0,8091:2,8092:0,8093:2,8095:0,8096:2,8097:0,8098:2,8099:0,8100:2,8101:0,8102:2,8103:0,8104:2,8105:0,8106:2,8107:0,8108:2,8109:0,8110:2,8111:0,8112:2,8113:0,8114:2,8115:0,8116:2,8117:0,8118:2,8119:0,8120:2,8121:0,8122:2,8123:0,8124:2,8125:0,8126:2,8127:0,8128:2,8129:0,8130:2,8131:0,8132:2,8133:0,8134:2,8135:0,8136:2,8137:0,8138:2,8139:0,8140:2,8141:0,8142:2,8143:0,8144:2,8145:0,8146:2,8147:0,8148:2,8149:0,8150:2,8151:0,8152:2,8153:0,8154:2,8155:0,8156:2,8157:0,8158:2,8159:0,8160:2,8161:0,8162:2,8163:0,8164:2,8165:0,8166:2,8167:0,8168:2,8169:0,8170:2,8171:0,8172:2,8173:0,8177:2,8179:0,8180:2,8181:0,8182:2,8184:0,8185:2,8187:0,8189:2,8191:0,8193:2,8196:0,8197:2,8198:0,8200:2,8201:0,8202:2,8203:0,8204:2,8205:0,8206:2,8207:0,8208:2,8209:0,8210:2,8212:0,8213:2,8214:0,8216:2,8218:0,8220:2,8222:0,8224:2,8225:0,8226:2,8311:0,8312:1,8313:0,8314:1,8315:0,8316:1,8317:0,8318:1,8319:0,8320:1,8321:0,8322:1,8323:0,8324:1,8325:0,8326:1,8327:0,8328:1,8329:0,8330:1,8331:0,8332:1,8333:0,8334:1,8335:0,8336:1,8337:0,8338:1,8339:0,8340:1,8341:0,8342:1,8343:0,8344:1,8345:0,8346:1,8347:0,8348:1,8352:0,8353:0,8379:0,8380:2,8381:0,8382:2,8383:0,8384:2,8385:0,8387:2,8391:0,8395:0,8433:0,8441:0,8455:0,8456:0,8531:2,8682:0,8686:0,8687:0,8692:0,8693:0,8826:0,8903:0,8950:0,8951:0,9039:0,9040:0,9141:0,9149:0,9150:0,9191:0,9221:0,9222:0,9249:0,9250:0,9252:0,9254:0,9265:0,9284:0,9285:0,9300:0,9354:0,9367:0,9373:0,9377:0,9387:0,9391:0,9456:0,9473:0,9498:0,9674:0,9678:0,9680:0,9709:0,9712:0,9713:0,9716:0,9741:0,9748:2,9749:2,9761:0,9766:0,9793:0,9794:0,9869:0,9874:0,9875:0,9880:0,9943:0,9945:0,9947:0,9967:0,9972:0,9977:0,20042:0,20050:1,20499:0,20538:0,20539:0,20790:0,20791:0,21291:0,21292:0,21500:0,21817:0,21818:0,22032:0,22033:0,22091:0,22092:0,22239:0,22240:0,22332:0,22337:0,22338:0,22391:0,22392:0,22639:0,22700:0,22739:0,22770:0,22780:0,22832:0,23090:0,23095:0,23239:0,23240:0,23433:0,23700:0,24047:0,24048:0,24100:3,24200:0,24305:0,24306:0,24382:10,24383:0,24500:0,24547:0,24548:0,24571:9,24600:0,25e3:0,25231:0,25884:0,25932:0,26237:0,26331:0,26332:0,26432:0,26591:0,26592:0,26632:0,26692:0,27120:0,27200:0,27291:6,27292:6,27429:0,27492:0,27493:0,27500:0,27700:0,28232:0,28600:0,28991:0,28992:0,29100:0,29101:0,29220:0,29221:0,29333:0,29635:0,29636:0,29701:0,29738:0,29739:0,29849:0,29850:0,29871:8,29872:7,29873:0,29874:0,30200:5,30339:0,30340:0,30591:0,30592:0,30791:0,30792:0,30800:0,31028:0,31121:0,31154:0,31170:0,31171:0,31370:0,31528:0,31529:0,31600:0,31700:0,31838:0,31839:0,31900:0,31901:0,32061:0,32062:0,32098:0,32099:2,32100:0,32104:0,32161:0,32766:0,53048:0,53049:0,54090:0,54091:0,65061:2,65062:2,65161:0,65163:0,102041:2,102064:11,102068:15,102069:16,102118:2,102119:1,102120:2,102121:2,102217:2,102218:0,102219:2,102220:2,102378:1,102379:1,102380:0,102381:1,102589:2,102599:2,102600:2,102604:2,102647:0,102704:2,102705:2,102706:0,102731:0,102732:0,102759:1,102760:1,102761:2,102762:0,102763:2,102764:0,102765:0,102766:2,102970:1,102974:2,102993:0,102994:0,102995:2,102996:2,103015:0,103016:2,103017:0,103018:2,103025:0,103026:0,103027:2,103028:2,103035:0,103036:0,103037:2,103038:2,103039:0,103040:0,103041:2,103042:2,103043:0,103044:0,103045:2,103046:2,103047:0,103048:0,103049:2,103050:2,103051:0,103052:2,103053:0,103054:2,103055:0,103056:2,103057:0,103058:0,103059:2,103060:2,103061:0,103062:0,103063:2,103064:2,103069:2,103070:0,103071:0,103072:2,103073:2,103086:0,103087:0,103088:2,103089:2,103094:1,103095:0,103096:2,103103:0,103104:2,103105:0,103106:2,103121:0,103122:2,103123:0,103124:0,103125:1,103126:1,103127:0,103128:0,103129:2,103130:2,103131:0,103132:0,103133:2,103134:2,103135:0,103136:0,103137:1,103138:1,103139:0,103140:2,103141:0,103142:2,103143:0,103144:2,103145:0,103146:1,103147:0,103148:0,103149:2,103150:2,103151:0,103152:2,103172:0,103173:2,103174:0,103175:0,103176:2,103177:2,103178:0,103179:0,103180:2,103181:2,103182:0,103183:0,103184:2,103185:2,103228:0,103229:0,103230:2,103231:2,103250:0,103251:2,103252:0,103253:2,103260:0,103261:0,103262:2,103263:2,103270:0,103271:0,103272:2,103273:2,103274:0,103275:0,103276:2,103277:2,103278:0,103279:0,103280:2,103281:2,103282:0,103283:0,103284:2,103285:2,103286:0,103287:2,103288:0,103289:2,103290:0,103291:2,103292:0,103293:0,103294:2,103295:2,103296:0,103297:0,103298:2,103299:2,103376:2,103377:0,103378:0,103379:2,103380:2,103393:0,103394:0,103395:2,103396:2,103472:0,103473:1,103474:0,103475:2,103482:0,103483:2,103484:0,103485:2,103500:0,103501:2,103502:0,103503:0,103504:1,103505:1,103506:0,103507:0,103508:2,103509:2,103510:0,103511:0,103512:2,103513:2,103514:0,103515:2,103516:0,103517:2,103518:0,103519:2,103520:0,103521:1,103522:0,103523:0,103524:2,103525:2,103526:0,103527:2,103561:2,103562:2,103563:0,103564:0,103565:2,103566:2,103567:0,103568:0,103569:2,103570:2,103584:0,103585:2,103586:0,103587:2,103588:1,103589:0,103590:2,103591:1,103592:0,103593:2,103594:1,103695:2};for(e=2e3;e<=2045;e++)i[e]=0;for(e=2056;e<=2065;e++)i[e]=0;for(e=2067;e<=2135;e++)i[e]=0;for(e=2137;e<=2154;e++)i[e]=0;for(e=2161;e<=2170;e++)i[e]=0;for(e=2172;e<=2193;e++)i[e]=0;for(e=2195;e<=2198;e++)i[e]=0;for(e=2200;e<=2203;e++)i[e]=0;for(e=2205;e<=2217;e++)i[e]=0;for(e=2222;e<=2224;e++)i[e]=1;for(e=2225;e<=2250;e++)i[e]=2;for(e=2251;e<=2253;e++)i[e]=1;for(e=2257;e<=2264;e++)i[e]=2;for(e=2274;e<=2279;e++)i[e]=2;for(e=2280;e<=2282;e++)i[e]=1;for(e=2283;e<=2289;e++)i[e]=2;for(e=2290;e<=2292;e++)i[e]=0;for(e=2308;e<=2313;e++)i[e]=0;for(e=2315;e<=2491;e++)i[e]=0;for(e=2494;e<=2866;e++)i[e]=0;for(e=2867;e<=2869;e++)i[e]=1;for(e=2870;e<=2888;e++)i[e]=2;for(e=2891;e<=2895;e++)i[e]=2;for(e=2896;e<=2898;e++)i[e]=1;for(e=2902;e<=2908;e++)i[e]=2;for(e=2915;e<=2920;e++)i[e]=2;for(e=2921;e<=2923;e++)i[e]=1;for(e=2924;e<=2930;e++)i[e]=2;for(e=2931;e<=2962;e++)i[e]=0;for(e=2964;e<=2968;e++)i[e]=2;for(e=2969;e<=2973;e++)i[e]=0;for(e=2975;e<=2991;e++)i[e]=0;for(e=2995;e<=3051;e++)i[e]=0;for(e=3054;e<=3079;e++)i[e]=0;for(e=3081;e<=3088;e++)i[e]=0;for(e=3092;e<=3101;e++)i[e]=0;for(e=3106;e<=3138;e++)i[e]=0;for(e=3146;e<=3151;e++)i[e]=0;for(e=3153;e<=3166;e++)i[e]=0;for(e=3168;e<=3172;e++)i[e]=0;for(e=3174;e<=3203;e++)i[e]=0;for(e=3294;e<=3358;e++)i[e]=0;for(e=3367;e<=3403;e++)i[e]=0;for(e=3408;e<=3416;e++)i[e]=0;for(e=3417;e<=3438;e++)i[e]=2;for(e=3441;e<=3446;e++)i[e]=2;for(e=3447;e<=3450;e++)i[e]=0;for(e=3451;e<=3459;e++)i[e]=2;for(e=3460;e<=3478;e++)i[e]=0;for(e=3554;e<=3559;e++)i[e]=0;for(e=3560;e<=3570;e++)i[e]=2;for(e=3571;e<=3581;e++)i[e]=0;for(e=3594;e<=3597;e++)i[e]=0;for(e=3601;e<=3604;e++)i[e]=0;for(e=3637;e<=3639;e++)i[e]=0;for(e=3665;e<=3667;e++)i[e]=0;for(e=3693;e<=3695;e++)i[e]=0;for(e=3701;e<=3727;e++)i[e]=0;for(e=3728;e<=3739;e++)i[e]=2;for(e=3740;e<=3751;e++)i[e]=0;for(e=3753;e<=3760;e++)i[e]=2;for(e=3761;e<=3773;e++)i[e]=0;for(e=3775;e<=3777;e++)i[e]=0;for(e=3779;e<=3781;e++)i[e]=0;for(e=3783;e<=3785;e++)i[e]=0;for(e=3788;e<=3791;e++)i[e]=0;for(e=3797;e<=3802;e++)i[e]=0;for(e=3814;e<=3816;e++)i[e]=0;for(e=3825;e<=3829;e++)i[e]=0;for(e=3832;e<=3841;e++)i[e]=0;for(e=3844;e<=3852;e++)i[e]=0;for(e=3873;e<=3885;e++)i[e]=0;for(e=3890;e<=3893;e++)i[e]=0;for(e=3907;e<=3912;e++)i[e]=0;for(e=3942;e<=3950;e++)i[e]=0;for(e=3968;e<=3970;e++)i[e]=0;for(e=3973;e<=3976;e++)i[e]=0;for(e=3986;e<=3989;e++)i[e]=0;for(e=3994;e<=3997;e++)i[e]=0;for(e=4048;e<=4051;e++)i[e]=0;for(e=4056;e<=4063;e++)i[e]=0;for(e=4093;e<=4096;e++)i[e]=0;for(e=4390;e<=4398;e++)i[e]=0;for(e=4399;e<=4413;e++)i[e]=2;for(e=4418;e<=4433;e++)i[e]=2;for(e=4455;e<=4457;e++)i[e]=2;for(e=4484;e<=4489;e++)i[e]=0;for(e=4491;e<=4554;e++)i[e]=0;for(e=4568;e<=4589;e++)i[e]=0;for(e=4652;e<=4656;e++)i[e]=0;for(e=4766;e<=4800;e++)i[e]=0;for(e=5014;e<=5016;e++)i[e]=0;for(e=5069;e<=5072;e++)i[e]=0;for(e=5105;e<=5130;e++)i[e]=0;for(e=5173;e<=5188;e++)i[e]=0;for(e=5253;e<=5259;e++)i[e]=0;for(e=5269;e<=5275;e++)i[e]=0;for(e=5292;e<=5311;e++)i[e]=0;for(e=5329;e<=5331;e++)i[e]=0;for(e=5343;e<=5349;e++)i[e]=0;for(e=5355;e<=5357;e++)i[e]=0;for(e=5387;e<=5389;e++)i[e]=0;for(e=5459;e<=5463;e++)i[e]=0;for(e=5479;e<=5482;e++)i[e]=0;for(e=5518;e<=5520;e++)i[e]=0;for(e=5530;e<=5539;e++)i[e]=0;for(e=5550;e<=5552;e++)i[e]=0;for(e=5562;e<=5583;e++)i[e]=0;for(e=5623;e<=5625;e++)i[e]=2;for(e=5631;e<=5639;e++)i[e]=0;for(e=5649;e<=5653;e++)i[e]=0;for(e=5663;e<=5680;e++)i[e]=0;for(e=5682;e<=5685;e++)i[e]=0;for(e=5875;e<=5877;e++)i[e]=0;for(e=5896;e<=5899;e++)i[e]=0;for(e=5921;e<=5940;e++)i[e]=0;for(e=6050;e<=6125;e++)i[e]=0;for(e=6244;e<=6275;e++)i[e]=0;for(e=6328;e<=6348;e++)i[e]=0;for(e=6350;e<=6356;e++)i[e]=0;for(e=6366;e<=6372;e++)i[e]=0;for(e=6381;e<=6387;e++)i[e]=0;for(e=6393;e<=6404;e++)i[e]=0;for(e=6480;e<=6483;e++)i[e]=0;for(e=6511;e<=6514;e++)i[e]=0;for(e=6579;e<=6581;e++)i[e]=0;for(e=6619;e<=6624;e++)i[e]=0;for(e=6625;e<=6627;e++)i[e]=2;for(e=6628;e<=6632;e++)i[e]=0;for(e=6634;e<=6637;e++)i[e]=0;for(e=6669;e<=6692;e++)i[e]=0;for(e=6707;e<=6709;e++)i[e]=0;for(e=6720;e<=6723;e++)i[e]=0;for(e=6732;e<=6738;e++)i[e]=0;for(e=6931;e<=6933;e++)i[e]=0;for(e=6956;e<=6959;e++)i[e]=0;for(e=7005;e<=7007;e++)i[e]=0;for(e=7057;e<=7070;e++)i[e]=2;for(e=7074;e<=7082;e++)i[e]=0;for(e=7109;e<=7118;e++)i[e]=0;for(e=7119;e<=7127;e++)i[e]=1;for(e=7374;e<=7376;e++)i[e]=0;for(e=7528;e<=7586;e++)i[e]=0;for(e=7587;e<=7645;e++)i[e]=2;for(e=7692;e<=7696;e++)i[e]=0;for(e=7755;e<=7787;e++)i[e]=0;for(e=7791;e<=7795;e++)i[e]=0;for(e=7799;e<=7801;e++)i[e]=0;for(e=7803;e<=7805;e++)i[e]=0;for(e=7825;e<=7831;e++)i[e]=0;for(e=7845;e<=7859;e++)i[e]=0;for(e=8013;e<=8032;e++)i[e]=0;for(e=8065;e<=8068;e++)i[e]=1;for(e=8518;e<=8529;e++)i[e]=2;for(e=8533;e<=8536;e++)i[e]=2;for(e=8538;e<=8540;e++)i[e]=2;for(e=8677;e<=8679;e++)i[e]=0;for(e=8836;e<=8840;e++)i[e]=0;for(e=8857;e<=8859;e++)i[e]=0;for(e=8908;e<=8910;e++)i[e]=0;for(e=9154;e<=9159;e++)i[e]=0;for(e=9205;e<=9218;e++)i[e]=0;for(e=9271;e<=9273;e++)i[e]=0;for(e=9295;e<=9297;e++)i[e]=0;for(e=9356;e<=9360;e++)i[e]=0;for(e=9404;e<=9407;e++)i[e]=0;for(e=9476;e<=9482;e++)i[e]=0;for(e=9487;e<=9494;e++)i[e]=0;for(e=9697;e<=9699;e++)i[e]=0;for(e=9821;e<=9865;e++)i[e]=0;for(e=20002;e<=20032;e++)i[e]=0;for(e=20047;e<=20049;e++)i[e]=0;for(e=20062;e<=20092;e++)i[e]=0;for(e=20135;e<=20138;e++)i[e]=0;for(e=20248;e<=20258;e++)i[e]=0;for(e=20348;e<=20358;e++)i[e]=0;for(e=20436;e<=20440;e++)i[e]=0;for(e=20822;e<=20824;e++)i[e]=0;for(e=20904;e<=20932;e++)i[e]=0;for(e=20934;e<=20936;e++)i[e]=0;for(e=21004;e<=21032;e++)i[e]=0;for(e=21035;e<=21037;e++)i[e]=0;for(e=21095;e<=21097;e++)i[e]=0;for(e=21148;e<=21150;e++)i[e]=0;for(e=21207;e<=21264;e++)i[e]=0;for(e=21307;e<=21364;e++)i[e]=0;for(e=21413;e<=21423;e++)i[e]=0;for(e=21453;e<=21463;e++)i[e]=0;for(e=21473;e<=21483;e++)i[e]=0;for(e=21780;e<=21782;e++)i[e]=0;for(e=21891;e<=21894;e++)i[e]=0;for(e=21896;e<=21899;e++)i[e]=0;for(e=22171;e<=22177;e++)i[e]=0;for(e=22181;e<=22187;e++)i[e]=0;for(e=22191;e<=22197;e++)i[e]=0;for(e=22207;e<=22222;e++)i[e]=0;for(e=22234;e<=22236;e++)i[e]=0;for(e=22243;e<=22250;e++)i[e]=0;for(e=22262;e<=22265;e++)i[e]=0;for(e=22307;e<=22322;e++)i[e]=0;for(e=22348;e<=22357;e++)i[e]=0;for(e=22407;e<=22422;e++)i[e]=0;for(e=22462;e<=22465;e++)i[e]=0;for(e=22521;e<=22525;e++)i[e]=0;for(e=22607;e<=22622;e++)i[e]=0;for(e=22641;e<=22646;e++)i[e]=0;for(e=22648;e<=22657;e++)i[e]=0;for(e=22707;e<=22722;e++)i[e]=0;for(e=22762;e<=22765;e++)i[e]=0;for(e=22991;e<=22994;e++)i[e]=0;for(e=23028;e<=23038;e++)i[e]=0;for(e=23830;e<=23853;e++)i[e]=0;for(e=23866;e<=23872;e++)i[e]=0;for(e=23877;e<=23884;e++)i[e]=0;for(e=23886;e<=23894;e++)i[e]=0;for(e=23946;e<=23948;e++)i[e]=0;for(e=24311;e<=24313;e++)i[e]=0;for(e=24342;e<=24347;e++)i[e]=0;for(e=24370;e<=24374;e++)i[e]=10;for(e=24375;e<=24381;e++)i[e]=0;for(e=24718;e<=24721;e++)i[e]=0;for(e=24817;e<=24821;e++)i[e]=0;for(e=24877;e<=24882;e++)i[e]=0;for(e=24891;e<=24893;e++)i[e]=0;for(e=25391;e<=25395;e++)i[e]=0;for(e=25828;e<=25838;e++)i[e]=0;for(e=26191;e<=26195;e++)i[e]=0;for(e=26391;e<=26393;e++)i[e]=0;for(e=26701;e<=26722;e++)i[e]=0;for(e=26729;e<=26799;e++)i[e]=2;for(e=26801;e<=26803;e++)i[e]=2;for(e=26811;e<=26813;e++)i[e]=2;for(e=26847;e<=26870;e++)i[e]=2;for(e=26891;e<=26899;e++)i[e]=0;for(e=26901;e<=26923;e++)i[e]=0;for(e=26929;e<=26946;e++)i[e]=0;for(e=26948;e<=26998;e++)i[e]=0;for(e=27037;e<=27040;e++)i[e]=0;for(e=27205;e<=27232;e++)i[e]=0;for(e=27258;e<=27260;e++)i[e]=0;for(e=27391;e<=27398;e++)i[e]=0;for(e=27561;e<=27564;e++)i[e]=0;for(e=27571;e<=27574;e++)i[e]=0;for(e=27581;e<=27584;e++)i[e]=0;for(e=27591;e<=27594;e++)i[e]=0;for(e=28191;e<=28193;e++)i[e]=0;for(e=28348;e<=28358;e++)i[e]=0;for(e=28402;e<=28432;e++)i[e]=0;for(e=28462;e<=28492;e++)i[e]=0;for(e=29118;e<=29122;e++)i[e]=0;for(e=29168;e<=29172;e++)i[e]=0;for(e=29177;e<=29185;e++)i[e]=0;for(e=29187;e<=29195;e++)i[e]=0;for(e=29900;e<=29903;e++)i[e]=0;for(e=30161;e<=30179;e++)i[e]=0;for(e=30491;e<=30494;e++)i[e]=0;for(e=30729;e<=30732;e++)i[e]=0;for(e=31251;e<=31259;e++)i[e]=0;for(e=31265;e<=31268;e++)i[e]=0;for(e=31275;e<=31279;e++)i[e]=0;for(e=31281;e<=31297;e++)i[e]=0;for(e=31461;e<=31469;e++)i[e]=0;for(e=31491;e<=31495;e++)i[e]=0;for(e=31917;e<=31922;e++)i[e]=0;for(e=31965;e<=32e3;e++)i[e]=0;for(e=32001;e<=32003;e++)i[e]=2;for(e=32005;e<=32031;e++)i[e]=2;for(e=32033;e<=32060;e++)i[e]=2;for(e=32064;e<=32067;e++)i[e]=2;for(e=32074;e<=32077;e++)i[e]=2;for(e=32081;e<=32086;e++)i[e]=0;for(e=32107;e<=32130;e++)i[e]=0;for(e=32133;e<=32159;e++)i[e]=0;for(e=32164;e<=32167;e++)i[e]=2;for(e=32180;e<=32199;e++)i[e]=0;for(e=32201;e<=32260;e++)i[e]=0;for(e=32301;e<=32360;e++)i[e]=0;for(e=32601;e<=32662;e++)i[e]=0;for(e=32664;e<=32667;e++)i[e]=2;for(e=32701;e<=32761;e++)i[e]=0;for(e=53001;e<=53004;e++)i[e]=0;for(e=53008;e<=53019;e++)i[e]=0;for(e=53021;e<=53032;e++)i[e]=0;for(e=53034;e<=53037;e++)i[e]=0;for(e=53042;e<=53046;e++)i[e]=0;for(e=53074;e<=53080;e++)i[e]=0;for(e=54001;e<=54004;e++)i[e]=0;for(e=54008;e<=54019;e++)i[e]=0;for(e=54021;e<=54032;e++)i[e]=0;for(e=54034;e<=54037;e++)i[e]=0;for(e=54042;e<=54046;e++)i[e]=0;for(e=54048;e<=54053;e++)i[e]=0;for(e=54074;e<=54080;e++)i[e]=0;for(e=54098;e<=54101;e++)i[e]=0;for(e=102001;e<=102040;e++)i[e]=0;for(e=102042;e<=102063;e++)i[e]=0;for(e=102065;e<=102067;e++)i[e]=0;for(e=102070;e<=102117;e++)i[e]=0;for(e=102122;e<=102216;e++)i[e]=0;for(e=102221;e<=102377;e++)i[e]=0;for(e=102382;e<=102388;e++)i[e]=0;for(e=102389;e<=102398;e++)i[e]=2;for(e=102399;e<=102444;e++)i[e]=0;for(e=102445;e<=102447;e++)i[e]=2;for(e=102448;e<=102458;e++)i[e]=0;for(e=102459;e<=102468;e++)i[e]=2;for(e=102469;e<=102499;e++)i[e]=0;for(e=102500;e<=102519;e++)i[e]=1;for(e=102520;e<=102524;e++)i[e]=0;for(e=102525;e<=102529;e++)i[e]=2;for(e=102530;e<=102588;e++)i[e]=0;for(e=102590;e<=102598;e++)i[e]=0;for(e=102601;e<=102603;e++)i[e]=0;for(e=102605;e<=102628;e++)i[e]=0;for(e=102629;e<=102646;e++)i[e]=2;for(e=102648;e<=102700;e++)i[e]=2;for(e=102701;e<=102703;e++)i[e]=0;for(e=102707;e<=102730;e++)i[e]=2;for(e=102733;e<=102758;e++)i[e]=2;for(e=102767;e<=102900;e++)i[e]=0;for(e=102901;e<=102933;e++)i[e]=2;for(e=102934;e<=102950;e++)i[e]=13;for(e=102951;e<=102955;e++)i[e]=0;for(e=102961;e<=102963;e++)i[e]=0;for(e=102965;e<=102969;e++)i[e]=0;for(e=102971;e<=102973;e++)i[e]=0;for(e=102975;e<=102989;e++)i[e]=0;for(e=102990;e<=102992;e++)i[e]=1;for(e=102997;e<=103002;e++)i[e]=0;for(e=103003;e<=103008;e++)i[e]=2;for(e=103009;e<=103011;e++)i[e]=0;for(e=103012;e<=103014;e++)i[e]=2;for(e=103019;e<=103021;e++)i[e]=0;for(e=103022;e<=103024;e++)i[e]=2;for(e=103029;e<=103031;e++)i[e]=0;for(e=103032;e<=103034;e++)i[e]=2;for(e=103065;e<=103068;e++)i[e]=0;for(e=103074;e<=103076;e++)i[e]=0;for(e=103077;e<=103079;e++)i[e]=1;for(e=103080;e<=103082;e++)i[e]=0;for(e=103083;e<=103085;e++)i[e]=2;for(e=103090;e<=103093;e++)i[e]=0;for(e=103097;e<=103099;e++)i[e]=0;for(e=103100;e<=103102;e++)i[e]=2;for(e=103107;e<=103109;e++)i[e]=0;for(e=103110;e<=103112;e++)i[e]=2;for(e=103113;e<=103116;e++)i[e]=0;for(e=103117;e<=103120;e++)i[e]=2;for(e=103153;e<=103157;e++)i[e]=0;for(e=103158;e<=103162;e++)i[e]=2;for(e=103163;e<=103165;e++)i[e]=0;for(e=103166;e<=103168;e++)i[e]=1;for(e=103169;e<=103171;e++)i[e]=2;for(e=103186;e<=103188;e++)i[e]=0;for(e=103189;e<=103191;e++)i[e]=2;for(e=103192;e<=103195;e++)i[e]=0;for(e=103196;e<=103199;e++)i[e]=2;for(e=103200;e<=103224;e++)i[e]=0;for(e=103225;e<=103227;e++)i[e]=1;for(e=103232;e<=103237;e++)i[e]=0;for(e=103238;e<=103243;e++)i[e]=2;for(e=103244;e<=103246;e++)i[e]=0;for(e=103247;e<=103249;e++)i[e]=2;for(e=103254;e<=103256;e++)i[e]=0;for(e=103257;e<=103259;e++)i[e]=2;for(e=103264;e<=103266;e++)i[e]=0;for(e=103267;e<=103269;e++)i[e]=2;for(e=103300;e<=103375;e++)i[e]=0;for(e=103381;e<=103383;e++)i[e]=0;for(e=103384;e<=103386;e++)i[e]=1;for(e=103387;e<=103389;e++)i[e]=0;for(e=103390;e<=103392;e++)i[e]=2;for(e=103397;e<=103399;e++)i[e]=0;for(e=103400;e<=103471;e++)i[e]=2;for(e=103476;e<=103478;e++)i[e]=0;for(e=103479;e<=103481;e++)i[e]=2;for(e=103486;e<=103488;e++)i[e]=0;for(e=103489;e<=103491;e++)i[e]=2;for(e=103492;e<=103495;e++)i[e]=0;for(e=103496;e<=103499;e++)i[e]=2;for(e=103528;e<=103543;e++)i[e]=0;for(e=103544;e<=103548;e++)i[e]=2;for(e=103549;e<=103551;e++)i[e]=0;for(e=103552;e<=103554;e++)i[e]=1;for(e=103555;e<=103557;e++)i[e]=2;for(e=103558;e<=103560;e++)i[e]=0;for(e=103571;e<=103573;e++)i[e]=0;for(e=103574;e<=103576;e++)i[e]=2;for(e=103577;e<=103580;e++)i[e]=0;for(e=103581;e<=103583;e++)i[e]=2;for(e=103595;e<=103694;e++)i[e]=0;for(e=103696;e<=103699;e++)i[e]=0;for(e=103700;e<=103793;e++)i[e]=2;for(e=103794;e<=103890;e++)i[e]=0;for(e=103891;e<=103896;e++)i[e]=2;for(e=103900;e<=103971;e++)i[e]=2;for(e=103972;e<=103977;e++)i[e]=0;for(e=112e3;e<=112101;e++)i[e]=0;const jo={102113:!0,102100:!0,3857:!0,3785:!0},No={4326:!0,3785:!0,3857:!0,102113:!0,102100:!0,104905:!0,104971:!0},nr='PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{Central_Meridian}],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0]]',_e=[-20037508342788905e-9,20037508342788905e-9],me=[-20037508342787e-6,20037508342787e-6],un={102113:{wkTemplate:'PROJCS["WGS_1984_Web_Mercator",GEOGCS["GCS_WGS_1984_Major_Auxiliary_Sphere",DATUM["D_WGS_1984_Major_Auxiliary_Sphere",SPHEROID["WGS_1984_Major_Auxiliary_Sphere",6378137.0,0.0]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{Central_Meridian}],PARAMETER["Standard_Parallel_1",0.0],UNIT["Meter",1.0]]',valid:_e,origin:me,dx:1e-5},102100:{wkTemplate:nr,valid:_e,origin:me,dx:1e-5},3785:{wkTemplate:'PROJCS["WGS_1984_Web_Mercator",GEOGCS["GCS_WGS_1984_Major_Auxiliary_Sphere",DATUM["D_WGS_1984_Major_Auxiliary_Sphere",SPHEROID["WGS_1984_Major_Auxiliary_Sphere",6378137.0,0.0]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{Central_Meridian}],PARAMETER["Standard_Parallel_1",0.0],UNIT["Meter",1.0]]',valid:_e,origin:me,dx:1e-5},3857:{wkTemplate:nr,valid:_e,origin:me,dx:1e-5},4326:{wkTemplate:'GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",{Central_Meridian}],UNIT["Degree",0.0174532925199433]]',altTemplate:'PROJCS["WGS_1984_Plate_Carree",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Plate_Carree"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{Central_Meridian}],UNIT["Degrees",111319.491]]',valid:[-180,180],origin:[-180,90],dx:1e-5},104971:{wkTemplate:'GEOGCS["Mars_2000_(Sphere)",DATUM["Mars_2000_(Sphere)",SPHEROID["Mars_2000_(Sphere)",3396190.0,0.0]],PRIMEM["Reference_Meridian",0.0],UNIT["Degree",0.0174532925199433]]',valid:[-180,180],origin:[-180,90],dx:1e-5},104905:{wkTemplate:'GEOGCS["GCS_Mars_2000",DATUM["D_Mars_2000",SPHEROID["Mars_2000_IAU_IAG",3396190.0,169.8944472236118]],PRIMEM["Reference_Meridian",0.0],UNIT["Degree",0.0174532925199433]]',valid:[-180,180],origin:[-180,90],dx:1e-5}};function ae(t,r){return t===r||!A(t)&&!A(r)&&(t.wkid!=null||r.wkid!=null?t.wkid===r.wkid||Z(t)&&Z(r)||r.latestWkid!=null&&t.wkid===r.latestWkid||t.latestWkid!=null&&r.wkid===t.latestWkid:!(!t.wkt||!r.wkt)&&t.wkt.toUpperCase()===r.wkt.toUpperCase())}function Po(t){return R(t)&&t.wkid&&un[t.wkid]||null}function Ro(t){return!!R(t)&&(t.wkid?i[t.wkid]==null:!!t.wkt&&!!/^\s*GEOGCS/i.test(t.wkt))}function Go(t){return!(fn(t)||hn(t))}function xe(t){return R(t)&&t.wkid===4326}function h0(t){return R(t)&&t.wkid===V.CGCS2000}function Z(t){return R(t)&&t.wkid!=null&&jo[t.wkid]===!0}function d0(t){return R(t)&&t.wkid===32662}function jt(t){return t===V.GCSMARS2000||t===V.GCSMARS2000_SPHERE}function fn(t){return R(t)&&t.wkid!=null&&jt(t.wkid)}function Nt(t){return t===V.GCSMOON2000}function hn(t){return R(t)&&t.wkid!=null&&Nt(t.wkid)}function Io(t){return R(t)&&t.wkid!=null&&No[t.wkid]===!0}function R(t){return w(t)&&(t.wkid!=null&&t.wkid>=2e3||t.wkt!=null)}const Uo={wkid:4326,wkt:Cn(un[4326].wkTemplate,{Central_Meridian:"0.0"})},xo={wkid:102100,latestWkid:3857},Do={wkid:32662};function Pt(t){return{wkt:`GEOCCS["Spherical geocentric",
    DATUM["Not specified",
      SPHEROID["Sphere",${t.radius},0]],
    PRIMEM["Greenwich",0.0,
      AUTHORITY["EPSG","8901"]],
    UNIT["m",1.0],
    AXIS["Geocentric X",OTHER],
    AXIS["Geocentric Y",EAST],
    AXIS["Geocentric Z",NORTH]
  ]`}}const p0=Pt(O),Wo=Pt(Je),qo=Pt(He),g0={wkt:`GEOCCS["WGS 84",
  DATUM["WGS_1984",
    SPHEROID["WGS 84",${O.radius},298.257223563,
      AUTHORITY["EPSG","7030"]],
    AUTHORITY["EPSG","6326"]],
  PRIMEM["Greenwich",0,
    AUTHORITY["EPSG","8901"]],
  UNIT["m",1.0,
    AUTHORITY["EPSG","9001"]],
  AXIS["Geocentric X",OTHER],
  AXIS["Geocentric Y",OTHER],
  AXIS["Geocentric Z",NORTH],
  AUTHORITY["EPSG","4978"]
]`};function zo(t){return w(t)&&(fn(t)||ae(t,Wo))?Je:w(t)&&(hn(t)||ae(t,qo))?He:O}function y0(t){return jt(t)?Je:Nt(t)?He:O}const _0=39.37,Bo=O.radius*Math.PI/200,dn=/UNIT\[([^\]]+)\]\]$/i,W=i,pn=/UNIT\[([^\]]+)\]/i,Fo=new Set([4261,4305,4807,4810,4811,4812,4816,4819,4821,4901,4902,37225,104139,104140]),Lo=Le()({meter:"meters",foot:"feet",foot_us:"us-feet",foot_clarke:"clarke-feet",yard_clarke:"clarke-yards",link_clarke:"clarke-links",yard_sears:"sears-yards",foot_sears:"sears-feet",chain_sears:"sears-chains",chain_benoit_1895_b:"benoit-1895-b-chains",yard_indian:"indian-yards",yard_indian_1937:"indian-1937-yards",foot_gold_coast:"gold-coast-feet",chain_sears_1922_truncated:"sears-1922-truncated-chains","50_kilometers":"50-kilometers","150_kilometers":"150-kilometers"}),M=t=>t*t,G=t=>t*t*t,le={length:{baseUnit:"meters",units:{millimeters:{inBaseUnits:.001},centimeters:{inBaseUnits:.01},decimeters:{inBaseUnits:.1},meters:{inBaseUnits:1},kilometers:{inBaseUnits:1e3},inches:{inBaseUnits:.0254},feet:{inBaseUnits:.3048},yards:{inBaseUnits:.9144},miles:{inBaseUnits:1609.344},"nautical-miles":{inBaseUnits:1852},"us-feet":{inBaseUnits:1200/3937}}},area:{baseUnit:"square-meters",units:{"square-millimeters":{inBaseUnits:M(.001)},"square-centimeters":{inBaseUnits:M(.01)},"square-decimeters":{inBaseUnits:M(.1)},"square-meters":{inBaseUnits:1},"square-kilometers":{inBaseUnits:M(1e3)},"square-inches":{inBaseUnits:M(.0254)},"square-feet":{inBaseUnits:M(.3048)},"square-yards":{inBaseUnits:M(.9144)},"square-miles":{inBaseUnits:M(1609.344)},"square-us-feet":{inBaseUnits:M(1200/3937)},acres:{inBaseUnits:.0015625*M(1609.344)},ares:{inBaseUnits:100},hectares:{inBaseUnits:1e4}}},volume:{baseUnit:"liters",units:{liters:{inBaseUnits:1},"cubic-millimeters":{inBaseUnits:1e3*G(.001)},"cubic-centimeters":{inBaseUnits:1e3*G(.01)},"cubic-decimeters":{inBaseUnits:1e3*G(.1)},"cubic-meters":{inBaseUnits:1e3},"cubic-kilometers":{inBaseUnits:1e3*G(1e3)},"cubic-inches":{inBaseUnits:1e3*G(.0254)},"cubic-feet":{inBaseUnits:1e3*G(.3048)},"cubic-yards":{inBaseUnits:1e3*G(.9144)},"cubic-miles":{inBaseUnits:1e3*G(1609.344)}}},angle:{baseUnit:"radians",units:{radians:{inBaseUnits:1},degrees:{inBaseUnits:Math.PI/180}}}},Jo=(()=>{const t={};for(const r in le)for(const n in le[r].units)t[n]=r;return t})();function Ho(t,r,n){return t*le[n].units[r].inBaseUnits}function Vo(t,r,n){return t/le[n].units[r].inBaseUnits}const gn=["metric","imperial","inches","feet","yards","miles","nautical-miles","us-feet","meters","kilometers"],Zo=new Map([["meters","square-meters"],["feet","square-feet"],["us-feet","square-us-feet"]]);function vt(t){const r=Jo[t];if(!r)throw new Error("unknown type");return r}function sr(t,r=null){return r=r||vt(t),le[r].baseUnit===t}function B(t,r,n){if(r===n)return t;const s=vt(r);if(s!==vt(n))throw new Error("incompatible units");const o=sr(r,s)?t:Ho(t,r,s);return sr(n,s)?o:Vo(o,n,s)}function m0(t,r){const n=B(t,r,"meters");return Math.abs(n)<3e3?"meters":"kilometers"}function v0(t,r){const n=B(t,r,"meters");return Math.abs(n)<1e5?"meters":"kilometers"}function w0(t,r){const n=B(t,r,"feet");return Math.abs(n)<1e3?"feet":"miles"}function b0(t,r){const n=B(t,r,"feet");return Math.abs(n)<1e5?"feet":"miles"}function $0(t,r){const n=B(t,r,"square-meters");return Math.abs(n)<3e6?"square-meters":"square-kilometers"}function S0(t,r){const n=B(t,r,"square-feet");return Math.abs(n)<1e6?"square-feet":"square-miles"}function A0(t,r,n){return B(t,r,"meters")/(n*Math.PI/180)}function Ko(t){return Lo.fromJSON(t.toLowerCase())||null}function O0(t){if(w(t)&&!Go(t))return 1;const r=Rt(t);return r>1e5?1:r}function Xo(t){return Rt(t)>=zo(t).metersPerDegree?"meters":de(t)}function Rt(t,r=O.metersPerDegree){return En(Yo(t,!0),r)}function Yo(t,r=!1){const n=w(t)?t.wkid:null,s=w(t)?t.wkt:null;let o=null;if(n){if(jt(n))return Je.metersPerDegree;if(Nt(n))return He.metersPerDegree;o=W.values[W[n]],!o&&r&&Fo.has(n)&&(o=Bo)}else s&&(mn(s)?o=or(dn.exec(s),o):_n(s)&&(o=or(pn.exec(s),o)));return o}function or(t,r){return t&&t[1]?yn(t[1]):r}function yn(t){return parseFloat(t.split(",")[1])}function de(t){const r=w(t)?t.wkid:null,n=w(t)?t.wkt:null;let s=null;if(r)s=W.units[W[r]];else if(n){const o=mn(n)?dn:_n(n)?pn:null;if(o){const a=o.exec(n);a&&a[1]&&(s=ei(a[1]))}}return w(s)?Ko(s):null}function k0(t){const r=de(t);return A(r)||!gn.includes(r)?null:r}function M0(t){const r=Xo(t);return A(r)||!gn.includes(r)?null:r}function E0(t){const r=de(t);return A(r)?null:Zo.get(r)}function _n(t){return/^GEOCCS/i.test(t)}function mn(t){return/^PROJCS/i.test(t)}const Qo=1e-7;function ei(t){const r=/[\\"\\']{1}([^\\"\\']+)/.exec(t);let n=r&&r[1];if(!n||!W.units.includes(n)){const s=yn(t);n=null;const o=W.values;for(let a=0;a<o.length;++a)if(Math.abs(s-o[a])<Qo){n=W.units[a];break}}return n}function T0(t){const r=de(t);if(A(r))return null;switch(r){case"feet":case"us-feet":case"clarke-feet":case"clarke-yards":case"clarke-links":case"sears-yards":case"sears-feet":case"sears-chains":case"benoit-1895-b-chains":case"indian-yards":case"indian-1937-yards":case"gold-coast-feet":case"sears-1922-truncated-chains":return"imperial";case"50-kilometers":case"150-kilometers":case"meters":return"metric"}return null}const ti={esriAcres:"acres",esriAres:"ares",esriHectares:"hectares",esriSquareCentimeters:"square-centimeters",esriSquareDecimeters:"square-decimeters",esriSquareFeet:"square-feet",esriSquareInches:"square-inches",esriSquareKilometers:"square-kilometers",esriSquareMeters:"square-meters",esriSquareMiles:"square-miles",esriSquareMillimeters:"square-millimeters",esriSquareUsFeet:"square-us-feet",esriSquareYards:"square-yards"},ri={esriCentimeters:"centimeters",esriDecimeters:"decimeters",esriFeet:"feet",esriInches:"inches",esriKilometers:"kilometers",esriMeters:"meters",esriMiles:"miles",esriMillimeters:"millimeters",esriNauticalMiles:"nautical-miles",esriYards:"yards"},ni={esriDUDecimalDegrees:"degrees",esriDURadians:"radians"},C0=Le()(ti),j0=Le()(ri),N0=Le()(ni);function wt(t,r,n){let s,o;return r===void 0?(o=t,s=[void 0]):typeof r!="string"?(o=t,s=[void 0],n=r):(o=r,s=Array.isArray(t)?t:[t]),(a,l)=>{const c=a.constructor.prototype;for(const u of s){const f=Sr(a,u,o);f.write&&typeof f.write=="object"||(f.write={}),n&&(f.write.target=n),f.write.writer=c[l]}}}var E;let y=E=class extends Ue{static fromJSON(t){if(!t)return null;if(t.wkid){if(t.wkid===102100)return E.WebMercator;if(t.wkid===4326)return E.WGS84}const r=new E;return r.read(t),r}constructor(t){super(t),this.latestWkid=null,this.wkid=null,this.wkt=null,this.vcsWkid=null,this.latestVcsWkid=null,this.imageCoordinateSystem=null}normalizeCtorArgs(t){return t&&typeof t=="object"?t:{[typeof t=="string"?"wkt":"wkid"]:t}}get isWGS84(){return xe(this)}get isWebMercator(){return Z(this)}get isGeographic(){return Ro(this)}get isWrappable(){return Io(this)}get metersPerUnit(){return Rt(this)}get unit(){return de(this)||(this.isGeographic?"degrees":null)}writeWkt(t,r){this.wkid||(r.wkt=t)}clone(){if(this===E.WGS84)return E.WGS84;if(this===E.WebMercator)return E.WebMercator;const t=new E;return this.wkid!=null?(t.wkid=this.wkid,this.latestWkid!=null&&(t.latestWkid=this.latestWkid),this.vcsWkid!=null&&(t.vcsWkid=this.vcsWkid),this.latestVcsWkid!=null&&(t.latestVcsWkid=this.latestVcsWkid)):this.wkt!=null&&(t.wkt=this.wkt),this.imageCoordinateSystem&&(t.imageCoordinateSystem=k(this.imageCoordinateSystem)),t}equals(t){if(t==null)return!1;if(this.imageCoordinateSystem||t.imageCoordinateSystem){if(this.imageCoordinateSystem==null||t.imageCoordinateSystem==null)return!1;const{id:r,referenceServiceName:n}=t.imageCoordinateSystem,{geodataXform:s}=t.imageCoordinateSystem,o=this.imageCoordinateSystem;return r==null||s?JSON.stringify(o)===JSON.stringify(t.imageCoordinateSystem):n?o.id===r&&o.referenceServiceName===n:o.id===r}return ae(this,t)}toJSON(t){return this.write(void 0,t)}};y.GCS_NAD_1927=null,y.WGS84=null,y.WebMercator=null,y.PlateCarree=null,p([m({readOnly:!0})],y.prototype,"isWGS84",null),p([m({readOnly:!0})],y.prototype,"isWebMercator",null),p([m({readOnly:!0})],y.prototype,"isGeographic",null),p([m({readOnly:!0})],y.prototype,"isWrappable",null),p([m({type:D,json:{write:!0}})],y.prototype,"latestWkid",void 0),p([m({readOnly:!0})],y.prototype,"metersPerUnit",null),p([m({readOnly:!0})],y.prototype,"unit",null),p([m({type:D,json:{write:!0,origins:{"web-scene":{write:{overridePolicy(){return{isRequired:this.wkt===null}}}}}}})],y.prototype,"wkid",void 0),p([m({type:String,json:{origins:{"web-scene":{write:{overridePolicy(){return{isRequired:this.wkid===null}}}}}}})],y.prototype,"wkt",void 0),p([wt("wkt"),wt("web-scene","wkt")],y.prototype,"writeWkt",null),p([m({type:D,json:{write:!0}})],y.prototype,"vcsWkid",void 0),p([m({type:D,json:{write:!0}})],y.prototype,"latestVcsWkid",void 0),p([m()],y.prototype,"imageCoordinateSystem",void 0),y=E=p([K("esri.geometry.SpatialReference")],y),y.prototype.toJSON.isDefaultToJSON=!0,y.GCS_NAD_1927=new y({wkid:4267,wkt:'GEOGCS["GCS_North_American_1927",DATUM["D_North_American_1927",SPHEROID["Clarke_1866",6378206.4,294.9786982]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]]'}),y.WGS84=new y(Uo),y.WebMercator=new y(xo),y.PlateCarree=new y(Do),Object.freeze&&(Object.freeze(y.GCS_NAD_1927),Object.freeze(y.WGS84),Object.freeze(y.WebMercator));const C=y;let T=class extends Ue{constructor(...r){super(...r),this.type=null,this.hasM=!1,this.hasZ=!1,this.spatialReference=C.WGS84}get cache(){return this.commitProperty("spatialReference"),{}}get extent(){return null}readSpatialReference(r,n){if(r instanceof C)return r;if(r!=null){const s=new C;return s.read(r,n),s}return r}clone(){return console.warn(".clone() is not implemented for "+this.declaredClass),null}clearCache(){this.notifyChange("cache")}getCacheValue(r){return this.cache[r]}setCacheValue(r,n){this.cache[r]=n}};p([m()],T.prototype,"type",void 0),p([m({readOnly:!0})],T.prototype,"cache",null),p([m({readOnly:!0})],T.prototype,"extent",null),p([m({type:Boolean,json:{write:{overridePolicy:t=>({enabled:t})}}})],T.prototype,"hasM",void 0),p([m({type:Boolean,json:{write:{overridePolicy:t=>({enabled:t})}}})],T.prototype,"hasZ",void 0),p([m({type:C,json:{write:!0},value:C.WGS84})],T.prototype,"spatialReference",void 0),p([cn("spatialReference")],T.prototype,"readSpatialReference",null),T=p([K("esri.geometry.Geometry")],T);const si=T,oi=Object.prototype.toString;function ii(t){const r="__accessorMetadata__"in t?I(t):t;return function(...n){if(n.push(r),typeof n[2]=="number")throw new Error("Using @cast has parameter decorator is not supported since 4.16");return ai.apply(this,n)}}function ai(t,r,n,s){qe(t,r).cast=s}function li(t){return(r,n)=>{qe(r,t).cast=r[n]}}function ci(...t){if(t.length!==3||typeof t[1]!="string")return t.length===1&&oi.call(t[0])==="[object Function]"?ii(t[0]):t.length===1&&typeof t[0]=="string"?li(t[0]):void 0}function ui(t,r){const n=t.x-r.x,s=t.y-r.y,o=t.hasZ&&r.hasZ?t.z-r.z:0;return Math.sqrt(n*n+s*s+o*o)}const fi=57.29577951308232,hi=.017453292519943;function ir(t){return t*fi}function ar(t){return t*hi}function R0(t){return t/O.radius}function G0(t){return Math.PI/2-2*Math.atan(Math.exp(-t/O.radius))}function bt(t){return t.wkid!=null||t.wkt!=null}const et=[0,0];function De(t,r,n,s,o){const a=t,l=o;if(l.spatialReference=n,"x"in a&&"x"in l)[l.x,l.y]=r(a.x,a.y,et,s);else if("xmin"in a&&"xmin"in l)[l.xmin,l.ymin]=r(a.xmin,a.ymin,et,s),[l.xmax,l.ymax]=r(a.xmax,a.ymax,et,s);else if("paths"in a&&"paths"in l||"rings"in a&&"rings"in l){const c="paths"in a?a.paths:a.rings,u=[];let f;for(let h=0;h<c.length;h++){const d=c[h];f=[],u.push(f);for(let g=0;g<d.length;g++)f.push(r(d[g][0],d[g][1],[0,0],s)),d[g].length>2&&f[g].push(d[g][2]),d[g].length>3&&f[g].push(d[g][3])}"paths"in l?l.paths=u:l.rings=u}else if("points"in a&&"points"in l){const c=a.points,u=[];for(let f=0;f<c.length;f++)u[f]=r(c[f][0],c[f][1],[0,0],s),c[f].length>2&&u[f].push(c[f][2]),c[f].length>3&&u[f].push(c[f][3]);l.points=u}return o}function di(t,r){const n=t&&(bt(t)?t:t.spatialReference),s=r&&(bt(r)?r:r.spatialReference);return!(t&&"type"in t&&t.type==="mesh"||r&&"type"in r&&r.type==="mesh"||!n||!s)&&(!!ae(s,n)||Z(s)&&xe(n)||Z(n)&&xe(s))}function I0(t,r){if(A(t))return null;const n=t.spatialReference,s=r&&(bt(r)?r:r.spatialReference);return di(n,s)?ae(n,s)?k(t):Z(s)?De(t,L,C.WebMercator,!1,k(t)):xe(s)?De(t,ne,C.WGS84,!1,k(t)):null:null}function L(t,r,n=[0,0]){r>89.99999?r=89.99999:r<-89.99999&&(r=-89.99999);const s=ar(r);return n[0]=ar(t)*O.radius,n[1]=O.halfSemiMajorAxis*Math.log((1+Math.sin(s))/(1-Math.sin(s))),n}function ne(t,r,n=[0,0],s=!1){const o=ir(t/O.radius);return n[0]=s?o:o-360*Math.floor((o+180)/360),n[1]=ir(Math.PI/2-2*Math.atan(Math.exp(-r/O.radius))),n}function U0(t,r=!1,n=k(t)){return De(t,L,C.WebMercator,r,n)}function x0(t,r=!1,n=k(t)){return De(t,ne,C.WGS84,r,n)}var Ae;const Q=[0,0];function lr(t){return t&&(t.declaredClass==="esri.geometry.SpatialReference"||t.wkid!=null)}let S=Ae=class extends si{static copy(t,r){r._set("x",t._get("x")),r._set("y",t._get("y")),r._set("z",t._get("z")),r._set("m",t._get("m"));const n=t._get("spatialReference");r._set("spatialReference",Object.isFrozen(n)?n:n.clone())}constructor(...t){super(...t),this.x=0,this.y=0,this.z=void 0,this.m=void 0,this.type="point"}normalizeCtorArgs(t,r,n,s,o){let a;if(Array.isArray(t))a=t,o=r,t=a[0],r=a[1],n=a[2],s=a[3];else if(t&&typeof t=="object"){if(a=t,t=a.x!=null?a.x:a.longitude,r=a.y!=null?a.y:a.latitude,n=a.z,s=a.m,(o=a.spatialReference)&&o.declaredClass!=="esri.geometry.SpatialReference"&&(o=new C(o)),a.longitude!=null||a.latitude!=null){if(a.longitude==null)$.getLogger(this.declaredClass).warn(".longitude=","Latitude was defined without longitude");else if(a.latitude==null)$.getLogger(this.declaredClass).warn(".latitude=","Longitude was defined without latitude");else if(!a.declaredClass&&o&&o.isWebMercator){const c=L(a.longitude,a.latitude,Q);t=c[0],r=c[1]}}}else lr(n)?(o=n,n=null):lr(s)&&(o=s,s=null);const l={x:t,y:r};return l.x==null&&l.y!=null?$.getLogger(this.declaredClass).warn(".y=","Y coordinate was defined without an X coordinate"):l.y==null&&l.x!=null&&$.getLogger(this.declaredClass).warn(".x=","X coordinate was defined without a Y coordinate"),o!=null&&(l.spatialReference=o),n!=null&&(l.z=n),s!=null&&(l.m=s),l}get cache(){return this.commitProperty("x"),this.commitProperty("y"),this.commitProperty("z"),this.commitProperty("m"),this.commitProperty("spatialReference"),{}}get hasM(){return this.m!==void 0}set hasM(t){t!==(this._get("m")!==void 0)&&(this._set("m",t?0:void 0),this._set("hasM",t))}get hasZ(){return this.z!==void 0}set hasZ(t){t!==(this._get("z")!==void 0)&&(this._set("z",t?0:void 0),this._set("hasZ",t))}get latitude(){const{spatialReference:t,x:r,y:n}=this;if(t){if(t.isWebMercator)return ne(r,n,Q)[1];if(t.isGeographic)return n}return null}set latitude(t){const{spatialReference:r,x:n}=this;t!=null&&r&&(r.isWebMercator?this._set("y",L(n,t,Q)[1]):r.isGeographic&&this._set("y",t),this._set("latitude",t))}get longitude(){const{x:t,y:r,spatialReference:n}=this;if(n){if(n.isWebMercator)return ne(t,r,Q)[0];if(n.isGeographic)return t}return null}set longitude(t){const{y:r,spatialReference:n}=this;t!=null&&n&&(n.isWebMercator?this._set("x",L(t,r,Q)[0]):n.isGeographic&&this._set("x",t),this._set("longitude",t))}writeX(t,r,n){r[n]=isNaN(t)?"NaN":t}readX(t){return typeof t=="string"?NaN:t}clone(){const t=new Ae;return t.x=this.x,t.y=this.y,t.z=this.z,t.m=this.m,t.spatialReference=this.spatialReference,t}copy(t){return Ae.copy(t,this),this}equals(t){if(A(t))return!1;const{x:r,y:n,z:s,m:o,spatialReference:a}=this,{z:l,m:c}=t;let{x:u,y:f,spatialReference:h}=t;if(!a.equals(h))if(a.isWebMercator&&h.isWGS84)[u,f]=L(u,f),h=a;else{if(!a.isWGS84||!h.isWebMercator)return!1;[u,f]=ne(u,f),h=a}return r===u&&n===f&&s===l&&o===c&&a.wkid===h.wkid}offset(t,r,n){return this.x+=t,this.y+=r,n!=null&&(this.z=(this.z??0)+n),this}normalize(){if(!this.spatialReference)return this;const t=Po(this.spatialReference);if(!t)return this;let r=this.x;const[n,s]=t.valid,o=2*s;let a;return r>s?(a=Math.ceil(Math.abs(r-s)/o),r-=a*o):r<n&&(a=Math.ceil(Math.abs(r-n)/o),r+=a*o),this._set("x",r),this}distance(t){return ui(this,t)}toArray(){const t=this.hasZ,r=this.hasM;return t&&r?[this.x,this.y,this.z,this.m]:t?[this.x,this.y,this.z]:r?[this.x,this.y,this.m]:[this.x,this.y]}toJSON(t){return this.write({},t)}};p([m({readOnly:!0})],S.prototype,"cache",null),p([m({type:Boolean,json:{read:!1,write:{enabled:!1,overridePolicy:null}}})],S.prototype,"hasM",null),p([m({type:Boolean,json:{read:!1,write:{enabled:!1,overridePolicy:null}}})],S.prototype,"hasZ",null),p([m({type:Number})],S.prototype,"latitude",null),p([m({type:Number})],S.prototype,"longitude",null),p([m({type:Number,json:{type:[Number,String],write:{isRequired:!0,allowNull:!0}}}),ci(t=>isNaN(t)?t:ur(t))],S.prototype,"x",void 0),p([wt("x")],S.prototype,"writeX",null),p([cn("x")],S.prototype,"readX",null),p([m({type:Number,json:{write:!0}})],S.prototype,"y",void 0),p([m({type:Number,json:{write:{overridePolicy(){return{enabled:this.hasZ}}}}})],S.prototype,"z",void 0),p([m({type:Number,json:{write:{overridePolicy(){return{enabled:this.hasM}}}}})],S.prototype,"m",void 0),S=Ae=p([K("esri.geometry.Point")],S),S.prototype.toJSON.isDefaultToJSON=!0;const D0=S;export{Rt as $,Wo as A,qo as B,g0 as C,hn as D,ae as E,Sr as F,qe as G,xr as H,R as I,Br as J,G0 as K,h0 as L,As as M,Nn as N,Ln as O,fn as P,St as Q,Po as R,Jr as S,I0 as T,qi as U,Bi as V,Ue as W,de as X,Eo as Y,xi as Z,xe as _,K as a,R0 as a$,Tr as a0,Ks as a1,si as a2,Ls as a3,xn as a4,I as a5,D as a6,Si as a7,Ai as a8,Js as a9,er as aA,Li as aB,i as aC,y0 as aD,qr as aE,zi as aF,Le as aG,Wr as aH,O as aI,Xi as aJ,j0 as aK,C0 as aL,N0 as aM,rs as aN,s0 as aO,Fr as aP,Tt as aQ,U as aR,vt as aS,T0 as aT,M0 as aU,k0 as aV,U0 as aW,A0 as aX,zo as aY,_i as aZ,Ui as a_,Wi as aa,Di as ab,wi as ac,Io as ad,Qe as ae,yi as af,Cn as ag,is as ah,ci as ai,l0 as aj,dt as ak,Yi as al,Hr as am,X as an,E0 as ao,Be as ap,Ji as aq,zs as ar,O0 as as,B as at,jn as au,In as av,ft as aw,$i as ax,Fs as ay,ur as az,C as b,V as b0,nt as b1,Es as b2,ze as b3,zr as b4,tr as b5,Us as b6,n0 as b7,o0 as b8,He as b9,Je as ba,d0 as bb,Gn as bc,Ei as bd,vo as be,Ao as bf,Mo as bg,Ko as bh,Xo as bi,a0 as bj,t0 as bk,m0 as bl,v0 as bm,w0 as bn,b0 as bo,$0 as bp,S0 as bq,gs as br,qs as bs,Vt as bt,mr as bu,bi as bv,mi as bw,Hi as bx,to as by,L as c,ne as d,p as e,_0 as f,di as g,Co as h,$ as i,Et as j,Z as k,z as l,wt as m,gi as n,cn as o,Ro as p,Uo as q,v as r,he as s,x0 as t,Vr as u,yo as v,D0 as w,Fi as x,m as y,p0 as z};
