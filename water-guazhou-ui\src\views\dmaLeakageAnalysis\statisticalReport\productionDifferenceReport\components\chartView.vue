<!-- 图表视图 -->
<template>
  <TreeBox v-loading="!!TreeData.loading" class="bg-wrapper">
    <template #tree>
      <SLTree ref="refTree" :tree-data="TreeData"></SLTree>
    </template>
    <Search ref="refSearch" :config="SearchConfig"> </Search>
    <!-- 图表模式 -->
    <div ref="refDiv" class="card-ehcarts">
      <VChart ref="refChart" :option="state.chartOption"></VChart>
    </div>
  </TreeBox>
</template>
<script lang="ts" setup>
import { IECharts } from '@/plugins/echart';
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';
import { formatterMonth, formatterYear } from '@/utils/GlobalHelper';
import { GetDmaPSReportDetailDetail } from '@/api/mapservice/dma';
// import { GetDmaPSDetail } from '@/api/mapservice/dma/reports'

const refChart = ref<IECharts>();
const props = defineProps<{ partitions: NormalOption[] }>();
const refSearch = ref<ISearchIns>();
const refDiv = ref<HTMLDivElement>();

const state = reactive<{
  chartOption: any;
}>({
  chartOption: null
});

watch(
  () => props.partitions,
  () => {
    TreeData.data = props.partitions || [];
  }
);
// 获取左边树
const TreeData = reactive<SLTreeConfig>({
  data: props.partitions || [],
  title: '区域划分',
  treeNodeHandleClick: (data: NormalOption) => {
    // TreeData.loading = true
    TreeData.currentProject = data;
    refreshData();
  }
});
const handleHidden = (params, query, config) =>
  (config.hidden = params.type !== config.field);
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'year',
    year: moment().format(formatterYear),
    month: [
      moment().startOf('y').format(formatterMonth),
      moment().format(formatterMonth)
    ]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按年月', value: 'month' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden,
      type: 'monthrange',
      label: '',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm();
          }
        }
      ]
    }
  ]
});
function lineOption(
  dateX: any[] = [],
  ps: any[] = [],
  supply: any[] = [],
  copyMeter: any = [],
  input: any[] = [],
  outPut: any[] = []
) {
  // dateX = Array.from({ length: moment().daysInMonth() }).map((item, i) => moment().startOf('M').add(i, 'd').format(formatterDate))
  // const curDay = moment().get('d')
  // let curPer = (curDay / dateX.length) * 100
  // curPer < 30 && (curPer = 30)
  // supply = Array.from({ length: curDay }).map(() => (Math.random() * 5000 + 12000).toFixed(2))
  // input = Array.from({ length: curDay }).map((item, i) => supply[i])
  // outPut = Array.from({ length: curDay }).map(() => (Math.random() * 3000 + 10000).toFixed(2))
  // copyMeter = Array.from({ length: curDay }).map((item, i) => ((input[i] - outPut[i]) * Math.random() * 0.3).toFixed(2))
  // ps = Array.from({ length: curDay }).map((item, i) => {
  //   return ((copyMeter[i] / (input[i] - outPut[i])) * 100).toFixed(2)
  // })
  const option = {
    // backgroundColor: useAppStore().isDark ? '#222536' : 'transparent',
    title: {
      text: (TreeData.currentProject?.label || '') + ' 分析图',
      textStyle: {
        fontSize: 16,
        color: '#318DFF'
      },
      left: 40,
      top: 10
    },
    grid: {
      left: 50,
      right: 50,
      top: 80,
      bottom: 80
    },
    legend: {
      type: 'scroll',
      width: 500,
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let x = params[0]?.axisValue;
        params.map((item) => {
          x += `
          <br />
          ${item.marker} ${item.seriesName} ${
            item.value + (item.componentSubType === 'line' ? ' %' : ' m³')
          }
`;
        });
        return x;
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: dateX
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: 'm³',
        axisLine: {
          show: true
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      },
      {
        position: 'right',
        type: 'value',
        name: '%',
        axisLine: {
          show: true
        },
        axisLabel: {
          show: true
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      }
    ],
    series: [
      {
        name: '产销差',
        smooth: true,
        data: ps,
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          color: '#318DFF'
        }
      },
      {
        name: '供水总量',
        data: supply,
        type: 'bar',
        barWidth: 5
      },
      {
        name: '用户抄见量',
        data: copyMeter,
        type: 'bar',
        barWidth: 5
      },
      {
        name: '进水量',
        data: input,
        type: 'bar',
        barWidth: 5
      },
      {
        name: '出水量',
        data: outPut,
        type: 'bar',
        barWidth: 5
      }
    ]
  };
  return option;
}
// 配置加载图表数据
const refreshData = async () => {
  try {
    const query = refSearch.value?.queryParams || {};
    const res = await GetDmaPSReportDetailDetail({
      type: query.type,
      date: query.type === 'year' ? query.year : undefined,
      partitionId: TreeData.currentProject?.value,
      start: query.type === 'month' ? query.month?.[0] : undefined,
      end: query.type === 'month' ? query.month?.[1] : undefined
    });
    const data = res.data.data;
    const options = lineOption(
      data.map((item) => item.partitionId),
      data.map((item) => item.correctNrwRate),
      data.map((item) => item.supplyTotal),
      data.map((item) => item.correctUseWater),
      data.map((item) => item.inWater),
      data.map((item) => item.outWater)
    );
    state.chartOption = options;
  } catch (error) {
    state.chartOption = lineOption();
  }
};
const detector = useDetector();
onMounted(async () => {
  TreeData.data = props.partitions || [];
  TreeData.currentProject = TreeData.data[0];
  refreshData();
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize();
  });
});
</script>
<style lang="scss" scoped>
html.dark {
  .bg-wrapper {
    background-color: #1c1e24;
  }
}
.card-ehcarts {
  height: calc(100% - 40px);
  width: 100%;
}
</style>
