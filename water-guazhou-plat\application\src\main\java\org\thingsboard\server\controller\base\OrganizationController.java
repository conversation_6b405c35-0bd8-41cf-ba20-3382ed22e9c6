package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationPageRequest;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationSaveRequest;
import org.thingsboard.server.dao.organization.OrganizationService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/organization")
public class OrganizationController extends BaseController {
    @Autowired
    private OrganizationService organizationService;

    @GetMapping
    public List<Organization> findAllConditional(OrganizationPageRequest request) {
        return organizationService.findAllConditional(request);
    }

    @GetMapping("/tree/{depth}")
    public List<Organization> findTree(@PathVariable Integer depth) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return organizationService.findAllStructure(depth, tenantId);
    }

    @PostMapping
    public Organization save(@RequestBody OrganizationSaveRequest request) throws ThingsboardException {
        // if (!organizationService.canBeAdd(request.getParentId())) {
        //     throw new ThingsboardException("该单位已有部门关联，无法创建下级单位", ThingsboardErrorCode.GENERAL);
        // }
        if (request.getId() != null) {
            invalidDepartmentCache(request.getId());
        }
        return organizationService.save(request);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody OrganizationSaveRequest request, @PathVariable String id) {
        invalidDepartmentCache(id);
        return organizationService.update(request.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) throws ThingsboardException {
        if (!organizationService.canBeDelete(id)) {
            throw new ThingsboardException("该单位不存在或其下有单位或部门，无法删除", ThingsboardErrorCode.GENERAL);
        }

        invalidDepartmentCache(id);
        return organizationService.delete(id);
    }

    private void invalidDepartmentCache(String id) {
        Environment.getEnvironment().invalidDepartmentCache(id);
    }

}
