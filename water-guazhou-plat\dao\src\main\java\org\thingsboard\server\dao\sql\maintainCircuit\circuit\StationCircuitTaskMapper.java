package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.StationCircuitTaskListRequest;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask;

@Mapper
public interface StationCircuitTaskMapper extends BaseMapper<StationCircuitTask> {
    IPage<StationCircuitTask> findList(IPage<StationCircuitTask> pageRequest, @Param("param") StationCircuitTaskListRequest request);
}
