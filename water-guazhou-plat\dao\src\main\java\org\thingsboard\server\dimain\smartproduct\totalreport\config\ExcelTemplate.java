package org.thingsboard.server.dimain.smartproduct.totalreport.config;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.util.List;
import java.util.function.Consumer;

@SuppressWarnings("UnusedReturnValue")
public class ExcelTemplate implements Closeable {
    private final SXSSFWorkbook workbook;

    private SXSSFSheet currentSheet;

    private SXSSFRow currentRow;

    private int currentRowIndex = -1;

    private int currentColumnIndex = -1;

    public ExcelTemplate(SXSSFWorkbook workbook, int[] columnWidths) {
        this(workbook, columnWidths, null);
    }

    public ExcelTemplate(SXSSFWorkbook workbook, int[] columnWidths, String sheetName) {
        this.workbook = workbook;
        this.currentSheet = sheetName == null ? workbook.createSheet() : workbook.createSheet(sheetName);
        if (columnWidths != null) {
            for (int i = 0; i < columnWidths.length; i++) {
                currentSheet.setColumnWidth(i, columnWidths[i] * 256);
            }
        }
    }

    public static ExcelTemplate create(int[] columnWidths) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // workbook.createInformationProperties();
        // workbook.getDocumentSummaryInformation().setCompany("深龙软件");
        // SummaryInformation si = workbook.getSummaryInformation();
        // si.setAuthor("");  // 填加xls文件作者信息
        // si.setApplicationName(""); // 填加xls文件创建程序信息
        // si.setLastAuthor("LFT"); // 填加xls文件最后保存者信息
        // si.setComments(""); // 填加xls文件作者信息
        // si.setTitle(""); // 填加xls文件标题信息
        // si.setSubject("");// 填加文件主题信息
        // si.setCreateDateTime(new Date());

        return new ExcelTemplate(workbook, columnWidths);
    }

    public static ExcelTemplate create(String sheetName, int[] columnWidths) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // workbook.createInformationProperties();
        // workbook.getDocumentSummaryInformation().setCompany("深龙软件");
        // SummaryInformation si = workbook.getSummaryInformation();
        // si.setAuthor("");  // 填加xls文件作者信息
        // si.setApplicationName(""); // 填加xls文件创建程序信息
        // si.setLastAuthor("LFT"); // 填加xls文件最后保存者信息
        // si.setComments(""); // 填加xls文件作者信息
        // si.setTitle(""); // 填加xls文件标题信息
        // si.setSubject("");// 填加文件主题信息
        // si.setCreateDateTime(new Date());

        return new ExcelTemplate(workbook, columnWidths, sheetName);
    }

    public ExcelTemplate withWidthColumnRange(int width, int size) {
        for (int i = 0; i < size; i++) {
            currentSheet.setColumnWidth(i, width);
        }
        return this;
    }

    @Override
    public void close() throws IOException {
        workbook.close();
    }

    public ExcelTemplate nextSheet(String name) {
        currentRowIndex = -1;
        currentSheet = workbook.createSheet(name);
        return this;
    }

    public ExcelTemplate withTitle(String content, int columnSpan) {
        currentRow = null;
        currentRowIndex++;
        SXSSFRow titleRow = currentSheet.createRow(currentRowIndex);
        titleRow.createCell(currentRowIndex).setCellValue(content);
        titleRow.getCell(currentRowIndex).setCellStyle(titleStyle(workbook));
        currentSheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, columnSpan - 1));
        return this;
    }

    public ExcelTemplate nextRow() {
        currentColumnIndex = 0;
        currentRowIndex++;
        currentRow = currentSheet.createRow(currentRowIndex);
        return this;
    }

    public ExcelTemplate columns(List<String> dataList) {
        return columns(0, dataList);
    }

    public ExcelTemplate columns(int fromColumnIndex, List<String> dataArray) {
        for (int i = 0; i < dataArray.size(); i++) {
            String content = dataArray.get(i);
            SXSSFCell cell = currentRow.createCell(fromColumnIndex + i);
            cell.setCellValue(content);
            if (currentSheet.getColumnWidth(i) < content.getBytes().length * 256) {
                currentSheet.setColumnWidth(i, content.getBytes().length * 256);
            }
        }
        return this;
    }

    public ExcelTemplate column(int columnIndex, Object content) {
        return column(columnIndex, content, 0, 0);
    }

    public ExcelTemplate column(int columnIndex, Object content, boolean formula) {
        return column(columnIndex, content, 0, 0, formula);
    }

    public ExcelTemplate hcolumn(int columnIndex, Object content, int columnSpan) {
        return column(columnIndex, content, 0, columnSpan);
    }

    public ExcelTemplate vcolumn(int columnIndex, Object content, int rowSpan) {
        return column(columnIndex, content, rowSpan, 0);
    }

    public ExcelTemplate column(int columnIndex, Object content, int rowSpan, int columnSpan) {
        return column(columnIndex, content, rowSpan, columnSpan, false);
    }

    public ExcelTemplate vcolumn(int columnIndex, Object content, int rowSpan, boolean formula) {
        return column(columnIndex, content, rowSpan, 0, formula);
    }

    public ExcelTemplate column(int columnIndex, Object content, int rowSpan, int columnSpan, boolean formula) {
        return column(columnIndex, hssfCell -> {
            if (content instanceof String) {
                if (formula)
                    hssfCell.setCellFormula((String) content);
                else
                    hssfCell.setCellValue((String) content);
            } else if (content instanceof Number) {
                hssfCell.setCellValue(((Number) content).doubleValue());
                hssfCell.setCellType(CellType.NUMERIC);
            }
        }, rowSpan, columnSpan);
    }

    public ExcelTemplate column(int columnIndex, Consumer<SXSSFCell> content, int rowSpan, int columnSpan) {
        columnSpan = columnSpan == 0 ? 1 : columnSpan;
        if (columnIndex == -1) {
            columnIndex = currentColumnIndex + 1;
            currentColumnIndex += columnSpan > 0 ? columnSpan : 1;
        } else if (columnIndex == -2) {
            columnIndex = currentColumnIndex;
        } else {
            currentColumnIndex = columnIndex + columnSpan - 1;
        }
        SXSSFCell cell = currentRow.createCell(columnIndex);
        cell.setCellStyle(generalStyle(workbook));
        content.accept(cell);
        if (rowSpan > 1 || columnSpan > 1) {
            currentSheet.addMergedRegion(new CellRangeAddress(currentRowIndex,
                    rowSpan > 1 ? currentRowIndex + rowSpan - 1 : currentRowIndex,
                    columnIndex,
                    columnSpan > 1 ? columnIndex + columnSpan - 1 : columnIndex));
        }
        return this;
    }

    public ExcelTemplate skipColumn() {
        currentColumnIndex++;
        return this;
    }

    public static int $I(char c) {
        return Character.toUpperCase(c) - 'A';
    }

    public static int $N() {
        return -1;
    }

    public static int $C() {
        return -2;
    }

    public static String Char(int i) {
        String res;
        if (i > 25) {
            res = (char) ((i - 26) / 26 + 'A') + "" + (char) (i % 26 + 'A');
        } else {
            res = Character.toString((char) ('A' + i));
        }
        return res;
    }

    public ExcelTemplate withRegion(int firstRow, int lastRow, int firstCol, int lastCol) {
        currentSheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        return this;
    }

    public void write(ServletOutputStream outputStream) throws IOException {
        workbook.write(outputStream);
    }

    public ExcelTemplate refreshStyle(int width) {
        for (int i = 0; i < width; i++) {
            currentSheet.setDefaultColumnStyle(i, generalStyle(workbook));
        }
        return this;
    }

    public ExcelTemplate withHeightGlobal(float height) {
        currentSheet.setDefaultRowHeightInPoints(height);
        return this;
    }

    /**
     * current colum char
     */
    public String $CCC() {
        return Char(currentColumnIndex + 1);
    }

    /**
     * previous colum char
     */
    public String $PCC() {
        return Char(currentColumnIndex);
    }
    /**
     * previous n colum char
     */
    public String $PNCC(int n) {
        return Char(currentColumnIndex - n + 1);
    }


    // @FunctionalInterface
    // public interface BatchIndexRowProcessFunction {
    //     void accept(int i, int rowIndex, SXSSFRow row, SXSSFSheet sheet);
    //
    // }

    @FunctionalInterface
    public interface BatchRowProcessFunction<T> {
        void accept(T entity, int rowIndex, SXSSFRow row, SXSSFSheet sheet);

    }

    // public ExcelTemplate createBatchRow(int count, BatchIndexRowProcessFunction function) {
    //     for (int i = 0; i < count; i++) {
    //         nextRow();
    //         function.accept(i, currentRowIndex + 1, currentRow, currentSheet);
    //     }
    //     return this;
    // }

    public <T> ExcelTemplate createBatchRow(List<T> entities, BatchRowProcessFunction<T> function) {
        for (T entity : entities) {
            nextRow();
            function.accept(entity, currentRowIndex, currentRow, currentSheet);
        }
        return this;
    }

    public CellStyle titleStyle(SXSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontName("黑体");
        titleFont.setFontHeightInPoints((short) 16);
        style.setFont(titleFont);
        return style;
    }

    private CellStyle generalStyle(SXSSFWorkbook workbook) {
        return generalStyle(workbook, true);
    }


    public CellStyle generalStyle(SXSSFWorkbook workbook, boolean center) {
        CellStyle style = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 11);
        style.setFont(titleFont);
        if (center) {
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
        } else {
            style.setAlignment(HorizontalAlignment.LEFT);
            style.setVerticalAlignment(VerticalAlignment.TOP);
        }
        style.setWrapText(true);
        return style;
    }

    public SXSSFRow getCurrentRow() {
        return currentRow;
    }

    public int getCurrentRowIndex() {
        return currentRowIndex;
    }

}
