<!-- 巡检计划 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refDialogForm" :config="addOrUpdateConfig"> </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { Refresh } from '@element-plus/icons-vue';
import { ICONS } from '@/common/constans/common';
import { ICardSearchIns, IDialogFormIns } from '@/components/type';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import {
  getCircuitPlan,
  addCircuitPlan,
  delCircuitPlan,
  circuitTemplateList,
  batchDelCircuitPlan
} from '@/api/headwatersManage/waterInspection';
import { removeSlash } from '@/utils/removeIdSlash';
import useDepartment from '@/hooks/department/useDepartment';
import { getUserList } from '@/api/user';
import useStation from '@/hooks/station/useStation';

const { $messageSuccess, $messageError } = useGlobal();
const { getAllStationOption } = useStation();
const { $btnPerms } = useGlobal();
const { getDepartmentTree } = useDepartment();

const state = reactive<{
  departmentTree: any[];
  templateList: any[];
  stationOptionList: any[];
  startDate: any;
  intervalDays: any;
  executionNum: any;
  executionDays: any;
  users: any;
}>({
  departmentTree: [],
  templateList: [],
  stationOptionList: [],
  users: [],
  startDate: '',
  intervalDays: null,
  executionNum: null,
  executionDays: null
});

const refSearch = ref<ICardSearchIns>();
const refDialogForm = ref<IDialogFormIns>();
// 周期性或者固定日期任务表单
const formType = ref<string>('周期性任务');
const cardSearchConfig = reactive<ISearch>({
  filters: [
    {
      label: '计划名称',
      field: 'name',
      type: 'input',
      placeholder: '请输入计划名称'
    },
    {
      label: '巡检部门',
      field: 'executionUserDepartmentId',
      type: 'select-tree',
      checkStrictly: true,
      options: computed(() => state.departmentTree) as any,
      onChange: async (val: any) => {
        console.log(val);
        const executionUser = cardSearchConfig.filters?.find(
          (field) => field.field === 'executionUserId'
        ) as IFormSelect;
        if (val) {
          const userOptions = await userList(val);
          executionUser.options = userOptions;
        } else {
          executionUser.options = [];
        }
        cardSearchConfig.defaultParams = {
          ...cardSearchConfig.defaultParams,
          ...refSearch.value?.queryParams,
          executionUserId: '',
          executionUserDepartmentId: val
        };
        refSearch.value?.resetForm();
      }
    },
    {
      label: '巡检人员',
      field: 'executionUserId',
      type: 'select',
      placeholder: '请选择巡检人员'
    },
    {
      label: '创建人员',
      type: 'department-user',
      field: 'creator'
    },
    {
      label: '审核人员',
      type: 'department-user',
      field: 'auditUserId'
    },
    { label: '开始时间', field: 'startDateFrom', type: 'daterange' },
    { label: '结束时间', field: 'endDateFrom', type: 'daterange' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshDataDetail()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearchConfig.defaultParams = {
              executionUserDepartmentId: ''
            };
            refSearch.value?.resetForm();
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          type: 'success',
          icon: ICONS.ADD,
          click: () => {
            addOrUpdateConfig.title = '新增';
            openDetailDialog();
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '批量删除',
          type: 'danger',
          icon: ICONS.DELETE,
          click: () => {
            SLConfirm('确定批量删除吗？', '提示信息')
              .then(() => {
                const ids = TableConfig.selectList?.map((data) => {
                  return data.id;
                }) as string[];
                console.log(ids);
                batchDelCircuitPlan(ids)
                  .then((res) => {
                    if (res.data?.code === 200) {
                      $messageSuccess('删除成功');
                      refreshDataDetail();
                    } else {
                      $messageError('删除失败');
                    }
                  })
                  .catch((error) => {
                    $messageError(error);
                  });
              })
              .catch(() => {
                //
              });
          }
        }
      ]
    }
  ]
});

watch(
  () => [
    state.startDate,
    state.intervalDays,
    state.executionNum,
    state.executionDays
  ],
  ([nstartDate, nexecutionNum, nintervalDays, nexecutionDays]) => {
    console.log(nstartDate, nexecutionNum, nintervalDays, nexecutionDays);
    addOrUpdateConfig.defaultValue = {
      ...addOrUpdateConfig.defaultValue,
      ...refDialogForm.value?.refForm?.dataForm,
      endDate: dayjs(nstartDate)
        .add(
          Number(nexecutionNum) +
            Number(nintervalDays) +
            Number(nexecutionDays),
          'day'
        )
        .format('YYYY-MM-DD')
    };
    refDialogForm.value?.refForm?.resetForm();
  }
);
const TableConfig = reactive<ICardTable>({
  loading: true,
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  columns: [
    { label: '计划名称', prop: 'name' },
    { label: '计划类型', prop: 'planType' },
    { label: '采用模板', prop: 'templateName' },
    { label: '巡检人员', prop: 'executionUserName' },
    {
      label: '开始日期',
      prop: 'startDate',
      formatter: (row: any, value: any) => {
        return value ? dayjs(value).format('YYYY-MM-DD') : '-';
      }
    },
    {
      label: '结束日期',
      prop: 'endDate',
      formatter: (row: any) => {
        return row.endDate
          ? dayjs(row.endDate).format('YYYY-MM-DD')
          : dayjs(row.fixedDate).format('YYYY-MM-DD');
      }
    },
    {
      label: '巡检时间',
      prop: 'fixedDate',
      formatter: (row: any, value: any) => {
        return value ? dayjs(value).format('YYYY-MM-DD') : '-';
      }
    },
    {
      label: '消耗天数',
      prop: 'executionDays',
      formatter: (row: any, value: any) => {
        return value ? value : '1';
      }
    },
    {
      label: '间隔天数',
      prop: 'intervalDays',
      formatter: (row: any, value: any) => {
        return value ? value : '0';
      }
    },
    {
      label: '执行次数',
      prop: 'executionNum',
      formatter: (row: any, value: any) => {
        return value ? value : '1';
      }
    },
    { label: '创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row: any, value: any) => {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
      }
    }
  ],
  operations: [
    {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        addOrUpdateConfig.title = '详情';
        openDetailDialog(row);
      }
    },
    {
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshDataDetail();
    }
  },
  handleSelectChange: (rows) => {
    console.log(rows);
    TableConfig.selectList = rows;
  }
});

// 弹框表单配置
const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '100px',
  dialogWidth: 800,
  defaultValue: {
    planType: '周期性任务',
    endDate: ''
  },
  group: [
    {
      fields: [
        {
          xs: 20,
          type: 'radio',
          label: '计划类别',
          field: 'planType',
          noBorder: false,
          options: [
            { label: '周期性任务', value: '周期性任务' },
            { label: '固定日期任务', value: '固定日期任务' }
          ],
          onChange: (val: any) => {
            nextTick(() => {
              if (val === '固定日期任务') {
                addOrUpdateConfig.defaultValue = {
                  planType: '周期性任务',
                  executionDays: null,
                  intervalDays: null,
                  executionNum: null
                };
              } else {
                refDialogForm.value?.refForm?.resetForm();
              }
              formType.value = val;
            });
          }
        },
        {
          xs: 12,
          type: 'input',
          label: '计划名称',
          field: 'name',
          rules: [{ required: true, message: '请输入计划名称' }]
        },
        {
          xs: 12,
          type: 'select',
          label: '巡检模板',
          field: 'templateId',
          options: computed(() => state.templateList) as any,
          rules: [{ required: true, message: '请选择模板' }]
        },
        // {
        //   xs: 12,
        //   type: 'monthrange',
        //   label: '计划月份',
        //   field: 'fixedDate',
        //   handleHidden: (params, query, config) => {
        //     config.hidden = params.planType === '周期性任务'
        //   },
        //   rules: [{ required: true, message: '请输入计划开始时间' }],
        //   onChange: (val: any) => {
        //     console.log(val[0])
        //   }
        // },
        {
          xs: 12,
          type: 'date',
          label: '选择日期',
          field: 'fixedDate',
          format: 'YYYY-MM-DD',
          handleHidden: (params, query, config) => {
            config.hidden = params.planType === '周期性任务';
          },
          rules: [{ required: true, message: '请选择任务日期' }]
        },
        {
          xs: 12,
          type: 'date',
          label: '开始时间',
          field: 'startDate',
          format: 'YYYY-MM-DD',
          min: dayjs().format('YYYY-MM-DD'),
          handleHidden: (params, query, config) => {
            config.hidden = params.planType === '固定日期任务';
          },
          rules: [{ required: true, message: '请输入计划开始时间' }],
          onChange: (val) => {
            state.startDate = val;
          }
        },
        {
          xs: 12,
          type: 'date',
          readonly: true,
          label: '结束时间',
          field: 'endDate',
          format: 'YYYY-MM-DD',
          handleHidden: (params, query, config) => {
            config.hidden = params.planType === '固定日期任务';
          }
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '巡检部门',
          clearable: false,
          checkStrictly: true,
          field: 'executionUserDepartmentId',
          options: computed(() => state.departmentTree) as any,
          rules: [{ required: true, message: '请选择巡检部门' }],
          onChange: async (val: any) => {
            const executionUser = addOrUpdateConfig.group[0].fields.find(
              (field) => field.field === 'executionUserId'
            ) as IFormSelect;
            if (val) {
              console.log('4444');
              const userOptions = await userList(val);
              executionUser.options = userOptions;
            } else {
              executionUser.options = [];
            }
            addOrUpdateConfig.defaultValue = {
              ...addOrUpdateConfig.defaultValue,
              executionUserId: '',
              executionUserDepartmentId: val
            };
          }
        },
        {
          xs: 12,
          type: 'select',
          label: '巡检人员',
          clearable: false,
          field: 'executionUserId',
          rules: [{ required: true, message: '请选择巡检人员' }]
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '审核部门',
          clearable: false,
          checkStrictly: true,
          options: computed(() => state.departmentTree) as any,
          field: 'auditUserIdDepartmentId',
          rules: [{ required: true, message: '请选择审核部门' }],
          onChange: async (val: any) => {
            const executionUser = addOrUpdateConfig.group[0].fields.find(
              (field) => field.field === 'auditUserId'
            ) as IFormSelect;
            if (val) {
              console.log('5555');
              const userOptions = await userList(val);
              executionUser.options = userOptions;
            } else {
              executionUser.options = [];
            }
            addOrUpdateConfig.defaultValue = {
              ...addOrUpdateConfig.defaultValue,
              auditUserId: '',
              auditUserIdDepartmentId: val
            };
          }
        },
        {
          xs: 12,
          type: 'select',
          label: '审核人员',
          field: 'auditUserId',
          clearable: false,
          rules: [{ required: true, message: '请选择审核人员' }]
        },
        {
          xs: 8,
          type: 'input-number',
          label: '消耗天数',
          field: 'executionDays',
          handleHidden: (params, query, config) => {
            config.hidden = params.planType === '固定日期任务';
          },
          onChange: (val) => {
            state.executionDays = val;
          },
          rules: [{ required: true, message: '请输入执行天数' }]
        },
        {
          xs: 8,
          type: 'input-number',
          label: '间隔天数',
          field: 'intervalDays',
          handleHidden: (params, query, config) => {
            config.hidden = params.planType === '固定日期任务';
          },
          onChange: (val) => {
            state.intervalDays = val;
          },
          rules: [{ required: true, message: '请输入间隔时间' }]
        },
        {
          xs: 8,
          type: 'input-number',
          label: '执行次数',
          field: 'executionNum',
          handleHidden: (params, query, config) => {
            config.hidden = params.planType === '固定日期任务';
          },
          onChange: (val) => {
            state.executionNum = val;
          },
          rules: [{ required: true, message: '请输入执行次数' }]
        },
        {
          type: 'checkbox',
          field: '',
          label: '水源列表',
          rules: [{ required: true }]
        },
        {
          type: 'checkbox',
          field: 'stationIds',
          label: '',
          rules: [{ required: true, message: '请选择水源列表' }],
          noBorder: false,
          colStyles: {
            height: '120px',
            padding: '10px 10px'
          },
          options: computed(() => state.stationOptionList) as any
        },
        {
          xs: 100,
          type: 'textarea',
          minRow: 5,
          label: '备注',
          field: 'remark',
          colStyles: {
            marginTop: '20px'
          }
        }
      ]
    }
  ]
});

// const userList = async (node:any) => {
//   const res = await getUserList({ pid: node.data.id, status: 1, page: 1, size: 9999 })
//   const users = res.data.data?.data
//   const nUsers = users.map(data => {
//     return {
//       label: data.firstName,
//       value: removeSlash(data.id.id),
//       id: removeSlash(data.id.id),
//       isLeaf: true
//     }
//   })
//   return nUsers
// }
// 切换表单类型
// const changeRadio = (params: any) => {
//   console.log(params)
//   formType.value = params
//   addOrUpdateConfig.group[0].fields.splice(1, addOrUpdateConfig.group[0].fields.length)
//   const formFields = getFormFields(params) as any[]
//   console.log(formFields)
//   // formFields = params === '周期性任务' ? cycleFormFields : fixedFormFields
//   addOrUpdateConfig.group[0].fields = addOrUpdateConfig.group[0].fields!.concat(formFields)
//   console.log(addOrUpdateConfig.group[0].fields)
// }

// 打开详情
const openDetailDialog = (row?: { [x: string]: any }) => {
  addOrUpdateConfig.group.map((res) => {
    res.fields.map((field) => {
      field.readonly = !!row;
      if (field.field === 'endDate') {
        field.readonly = true;
      }
    });
  });
  if (row) {
    addOrUpdateConfig.submit = undefined;
    addOrUpdateConfig.defaultValue = {
      ...row,
      stationIds: row.stationIds?.split(','),
      startDate: dayjs(row?.startDate).format(),
      endDate: dayjs(row?.endDate).format(),
      fixedDate: dayjs(row?.fixedDate).format(),
      auditUserId: row.auditUserName,
      executionUserId: row.executionUserName
    };
  } else {
    addOrUpdateConfig.defaultValue = {
      planType: '周期性任务'
    };
    addOrUpdateConfig.submit = (params: any) => {
      SLConfirm('确定提交？', '提示信息')
        .then(() => {
          params = {
            ...params,
            type: '水源',
            fixedDate: params.fixedDate
              ? dayjs(params?.fixedDate).valueOf()
              : undefined,
            startDate: params.startDate
              ? dayjs(params?.startDate).valueOf()
              : undefined,
            endDate: params.endDate
              ? dayjs(params?.endDate).valueOf()
              : undefined,
            stationIds: params.stationIds.join(',')
            // id: row ? row.id : undefined
          };

          addCircuitPlan(params)
            .then((res) => {
              if (res.data?.code === 200) {
                refDialogForm.value?.refForm?.resetForm();
                refDialogForm.value?.closeDialog();
                refreshDataDetail();
                $messageSuccess('保存成功！');
              } else {
                $messageError('保存失败！');
              }
            })
            .catch((error) => {
              $messageError(error);
            });
        })
        .catch(() => {
          //
        });
    };
  }
  refDialogForm.value?.openDialog();
};

const handleDelete = (row: { id: any }) => {
  SLConfirm('确定删除该计划, 是否继续?', '删除提示').then(() => {
    // 删除计划
    delCircuitPlan(row.id)
      .then(() => {
        refreshDataDetail();
        $messageSuccess('删除成功');
      })
      .catch((err) => {
        $messageSuccess(err.data.message);
      });
  });
};

const refreshDataDetail = async () => {
  TableConfig.loading = true;
  const query = refSearch.value?.queryParams || {
    startDate: [],
    endDate: []
  };
  console.log(refSearch.value?.queryParams);
  const [startForm, startTo] = query.startDateFrom || [];
  const [endForm, endTo] = query.endDateFrom || [];
  const params: any = {
    ...query,
    type: '水源',
    startDateFrom: startForm ? dayjs(startForm).startOf('day').valueOf() : null,
    startDateTo: startTo ? dayjs(startTo).endOf('day').valueOf() : null,
    endDateFrom: endForm ? dayjs(endForm).startOf('day').valueOf() : null,
    endDateTo: endTo ? dayjs(endTo).endOf('day').valueOf() : null,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20
  };

  const result = await getCircuitPlan(params);
  TableConfig.pagination.total = result.data?.data.total;
  TableConfig.dataList = result.data?.data.data;
  TableConfig.loading = false;
};

// 获取部门用户
const userList = async (val: string) => {
  const res = await getUserList({
    pid: val,
    status: 1,
    page: 1,
    size: 999
  });
  const users = res.data?.data?.data;
  const userOptions: INormalOption[] = users?.map((user) => {
    return {
      id: removeSlash(user.id.id),
      label: user.firstName,
      value: removeSlash(user.id.id),
      isLeaf: true
    };
  });
  return userOptions;
};
// 获取模板列表
const getCircuitTemplate = async () => {
  const res = await circuitTemplateList({
    page: 1,
    size: 999,
    type: '水源'
  });
  return res.data?.data?.data;
};

onBeforeMount(async () => {
  // 获取部门树
  state.departmentTree = await getDepartmentTree(2);
  // 获取模板列表
  const templateList = await getCircuitTemplate();
  state.templateList = templateList.map((template) => {
    return {
      id: template.id,
      label: template.name,
      value: template.id
    };
  });
  // const users = await getWaterSupplyTree('2')
  // console.log('users', users)
  // const dataToResolve = formatTree(users.data?.data || [], {
  //   label: 'name',
  //   value: 'id',
  //   id: 'id'
  // })
  // state.users = dataToResolve
  // const executionDepartment = cardSearchConfig.filters?.find(field => field.field === 'executionUserDepartmentId') as IFormSelect
  // executionDepartment.options = state.departmentTree

  state.stationOptionList = await getAllStationOption('水源地');
  await refreshDataDetail();
});
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
