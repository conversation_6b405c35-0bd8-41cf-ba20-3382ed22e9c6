/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.menu;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.MenuPoolEntity;

import java.util.List;

public interface MenuPoolRepository extends CrudRepository<MenuPoolEntity, String> {

    List<MenuPoolEntity> findByParentIdOrderByOrderNumDesc(String parentId);

    @Query("SELECT mp FROM MenuPoolEntity mp,MenuTenantEntity mt WHERE mp.id = mt.menuPoolId AND mt.tenantId = :tenantId AND mp.parentId = :parentId ORDER BY mp.orderNum desc")
    List<MenuPoolEntity> findMenuByTenantId(@Param("tenantId") String tenantId, @Param("parentId") String parentId);

    @Query("SELECT mp FROM MenuPoolEntity mp,MenuTenantEntity mt WHERE mp.id = mt.menuPoolId AND mt.id = :id ORDER BY mp.orderNum desc")
    MenuPoolEntity findMenuById(@Param("id") String menuTenantId);

    @Query("SELECT m.id,m.defaultName FROM MenuPoolEntity m WHERE m.parentId = :parentId ORDER BY orderNum desc")
    List findByParentId(@Param("parentId") String parentId);

    @Query("SELECT mp FROM MenuPoolEntity mp,MenuTenantEntity mt WHERE mp.id = mt.menuPoolId AND mt.tenantId = :tenantId ORDER BY mp.orderNum desc")
    List<MenuPoolEntity> findMenuByTenantId(@Param("tenantId") String tenantId);

    List<MenuPoolEntity> findByType(Integer type);
}
