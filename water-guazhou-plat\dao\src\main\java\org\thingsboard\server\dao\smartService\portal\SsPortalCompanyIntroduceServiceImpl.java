package org.thingsboard.server.dao.smartService.portal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalCompanyIntroduce;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalCompanyIntroduceMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCompanyIntroduceSaveRequest;

@Service
public class SsPortalCompanyIntroduceServiceImpl implements SsPortalCompanyIntroduceService {
    @Autowired
    private SsPortalCompanyIntroduceMapper mapper;

    @Override
    public SsPortalCompanyIntroduce get(String tenantId) {
        return mapper.getByTenantId(tenantId);
    }

    @Override
    public SsPortalCompanyIntroduce save(SsPortalCompanyIntroduceSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalCompanyIntroduce entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
