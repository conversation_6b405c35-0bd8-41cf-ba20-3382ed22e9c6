/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.telemetry;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;
import org.thingsboard.rule.engine.action.TbMsgCountNodeConfiguration;
import org.thingsboard.rule.engine.api.*;
import org.thingsboard.rule.engine.api.util.TbNodeUtils;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.kv.BasicTsKvEntry;
import org.thingsboard.server.common.data.kv.KvEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.plugin.ComponentType;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.common.msg.session.SessionMsgType;
import org.thingsboard.server.common.transport.adaptor.JsonConverter;
import org.thingsboard.server.dao.util.HttpClientFactory;

import java.io.IOException;
import java.util.*;

@Slf4j
@RuleNode(
        type = ComponentType.ACTION,
        name = "消息上报",
        configClazz = TbMsgCountNodeConfiguration.class,
        nodeDescription = "消息上报",
        nodeDetails = "消息上报",
        icon = "functions",
        uiResources = {"static/rulenode/rulenode-core-config.js"},
        configDirective = "tbActionNodeMsgCountConfig"
)
public class TbMsgReportNode implements TbNode {

    private TbMsgCountNodeConfiguration config;

    @Override
    public void init(TbContext ctx, TbNodeConfiguration configuration) throws TbNodeException {
        config = TbNodeUtils.convert(configuration, TbMsgCountNodeConfiguration.class);
    }

    @Override
    public void onMsg(TbContext ctx, TbMsg msg) {
        if (msg.getMetaData().getTsKvEntryList() == null || msg.getMetaData().getTsKvEntryList().size() == 0) {
            long ts = -1;
            String tsStr = msg.getMetaData().getValue("ts");
            if (!StringUtils.isEmpty(tsStr)) {
                try {
                    ts = Long.parseLong(tsStr);
                } catch (NumberFormatException e) {
                }
            } else {
                ts = System.currentTimeMillis();
            }
            String src = "";
            if (msg.getType().equals(SessionMsgType.POST_TELEMETRY_REQUEST.name())) {
                src = msg.getData();
            } else
                src = JSONObject.parseObject(msg.getData()).get("params").toString();
            Map<Long, List<KvEntry>> tsKvMap = JsonConverter.convertToTelemetry(new JsonParser().parse(src), ts);
            if (tsKvMap == null) {
                ctx.tellFailure(msg, new IllegalArgumentException("Msg body is empty: " + src));
                return;
            }
            List<TsKvEntry> tsKvEntryList = new ArrayList<>();
            for (Map.Entry<Long, List<KvEntry>> tsKvEntry : tsKvMap.entrySet()) {
                for (KvEntry kvEntry : tsKvEntry.getValue()) {
                    tsKvEntryList.add(new BasicTsKvEntry(tsKvEntry.getKey(), kvEntry));
                }
            }
            msg.getMetaData().putData(tsKvEntryList);
        }
        reportToServer(ctx, msg, convertReportBody(msg));
    }

    /**
     * 构造上报给业务系统的数据
     *
     * @param msg
     * @return
     */
    public String convertReportBody(TbMsg msg) {
        //构造header
        JSONObject header = new JSONObject();
        header.put("id", UUID.randomUUID());
        header.put("sourceId", UUIDConverter.fromString(msg.getMetaData().getValue("deviceId")));
        header.put("date", System.currentTimeMillis());
        header.put("repeat", 0);
        //构造body
        JSONObject body = new JSONObject();
        for (TsKvEntry tsKvEntry : msg.getMetaData().getTsKvEntryList()) {
            body.put(tsKvEntry.getKey(), tsKvEntry.getValueAsString());
            if (tsKvEntry.getKey().equalsIgnoreCase("name")) {
                //从消息体中获取name并上传
                header.put("name", tsKvEntry.getValueAsString());
            }
        }
        //body加入设备ID
        JSONObject object = new JSONObject();
        object.put("header", header);
        object.put("body", body);
        return object.toJSONString();
    }

    public void reportToServer(TbContext ctx, TbMsg msg, String data) {
        try {
            StringEntity eStringEntity = new StringEntity(data, "utf-8");
            CloseableHttpAsyncClient httpClient = HttpClientFactory.getHttpAsyncClient();
            HttpPost httpPost = new HttpPost(config.getTelemetryPrefix());
            eStringEntity.setContentType("application/json");
            httpPost.setEntity(eStringEntity);
            log.info("上报地址 [{}], 上报数据 [{}]", config.getTelemetryPrefix(), data);
            httpClient.execute(httpPost, new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse httpResponse) {
                    try {
                        int statusCode = httpResponse.getStatusLine().getStatusCode();
                        //上报数据成功之后返回状态码为200,上报到服务器的数据，不进行数据保存
                        if (statusCode == 200 || statusCode == 204) {
                            // String result = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                            ctx.tellNext(msg, TbRelationTypes.SUCCESS);
                            //ctx.getTelemetryService().saveAndNotify(deviceId, msg.getMetaData().getTsKvEntryList(), new TelemetryNodeCallback(ctx, msg));
                        } else {
                            ctx.tellNext(msg, TbRelationTypes.FAILURE);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("数据调用失败");
                    } finally {
                        try {
                            EntityUtils.consume(httpResponse.getEntity());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void failed(Exception e) {
                    ctx.tellNext(msg, TbRelationTypes.FAILURE);
                    log.info("保存数据失败");
                }

                @Override
                public void cancelled() {
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            log.info("异步调用上报失败！" + e.getMessage());
        }
    }

    @Override
    public void destroy() {
    }

}
