package org.thingsboard.server.dao.model.sql.smartOperation.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoBiddingCompany {
    // id
    private String id;

    // 公司名称
    private String name;

    // 所属招投标id
    private String biddingId;

    // 联系人
    private String contactUser;

    // 联系电话
    private String phone;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
