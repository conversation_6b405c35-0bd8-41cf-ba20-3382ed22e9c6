import{_ as I}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as M}from"./CardTable-rdWOL4_6.js";import{_ as N}from"./CardSearch-CB_HNR-Q.js";import{d as R,M as S,c,s as A,r as p,x as d,l as F,S as L,bN as T,o as V,g as q,n as Y,q as m,i as u,b7 as W,C as O}from"./index-r0dFAfgr.js";import{I as r}from"./common-CvK_P_ao.js";import{t as y,u as P,s as B}from"./equipmentManage-DuoY00aj.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const H={class:"wrapper"},Q=R({__name:"index",setup(U){const{$btnPerms:s}=S(),f=c(),n=c(),g=c(),x=c({filters:[{label:"名称",field:"name",type:"input"},{label:"简称",field:"shortName",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:r.QUERY,click:()=>i()},{type:"default",perm:!0,text:"重置",svgIcon:A(W),click:()=>{var e;(e=f.value)==null||e.resetForm(),i()}},{perm:!0,text:"新建顶级区域",icon:r.ADD,type:"success",click:()=>b("新建顶级区域")}]}]}),l=p({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"区域编号",prop:"serialId"},{label:"区域名称",prop:"name"},{label:"区域简称",prop:"nickName"},{label:"排序",prop:"orderNum"},{label:"备注",prop:"remark"}],operationWidth:"300px",operations:[{type:"success",text:"新增子集",icon:r.ADD,perm:s("RoleManageAdd"),click:e=>b("新增子集",e)},{type:"primary",text:"编辑",icon:r.EXPORT,perm:s("RoleManageEdit"),click:e=>k(e)},{type:"primary",text:"图片信息",icon:r.EXPORT,perm:s("RoleManageEdit"),click:e=>D(e)},{type:"danger",text:"删除",perm:s("RoleManageDelete"),icon:r.DELETE,click:e=>E(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{l.pagination.page=e,l.pagination.limit=a,i()}}}),o=p({title:"新增",dialogWidth:"500px",labelWidth:"100px",submit:e=>{y(e).then(a=>{var t;d.success(a.data.message),(t=n.value)==null||t.closeDialog(),i()})},defaultValue:{},group:[{fields:[{readonly:!0,type:"input",label:"编码",field:"serialId",rules:[{required:!0,message:"请输入编码"}]},{type:"input",label:"名称",field:"name",rules:[{required:!0,message:"请输入名称"}]},{type:"input",label:"简称",field:"nickName",rules:[{required:!0,message:"请输入简称"}]},{type:"input",label:"排序",field:"orderNum",rules:[{required:!0,message:"请输入排序"}]},{type:"textarea",label:"备注",field:"remark"}]}]}),_=p({title:"新增",dialogWidth:"500px",labelWidth:"100px",submit:e=>{y(e).then(()=>{var a;d.success("上传成功"),(a=g.value)==null||a.closeDialog(),i()})},defaultValue:{},group:[{fields:[{type:"image",label:"区域图像",field:"img",limit:1}]}]}),b=(e,a)=>{var t;o.title=e||"编辑",o.defaultValue={parentId:(a==null?void 0:a.id)||"",serialId:"QY"+F(new Date).format("YYYYMMDDHHmmss")},(t=n.value)==null||t.openDialog()},k=e=>{var a;o.title="编辑",o.defaultValue={...e||{}},(a=n.value)==null||a.openDialog()},D=e=>{var a;_.defaultValue={...e||{}},(a=g.value)==null||a.openDialog()},E=e=>{L("确定删除该安装区域, 是否继续?","删除提示").then(()=>{P([e.id]).then(()=>{i(),d.success("删除成功")}).catch(a=>{d.error(a.data.message)})})},v=p({areaList:[]}),i=()=>{var a;const e={size:l.pagination.limit,page:l.pagination.page,shortName:"",...((a=f.value)==null?void 0:a.queryParams)||{}};B(e).then(t=>{l.dataList=t.data.data.data||[],v.areaList=T(l.dataList,"children","serialId"),l.pagination.total=t.data.data.total||0})};return V(()=>{i()}),(e,a)=>{const t=N,C=M,h=I;return q(),Y("div",H,[m(t,{ref_key:"refSearch",ref:f,config:u(x)},null,8,["config"]),m(C,{config:u(l),class:"card-table"},null,8,["config"]),m(h,{ref_key:"refForm",ref:n,config:u(o)},null,8,["config"]),m(h,{ref_key:"imageForm",ref:g,config:u(_)},null,8,["config"])])}}}),Z=O(Q,[["__scopeId","data-v-992fb411"]]);export{Z as default};
