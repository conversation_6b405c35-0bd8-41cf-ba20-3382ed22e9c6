import { useAppStore } from '@/store'
import { transNumberUnit } from '@/utils/GlobalHelper'

export const ring = (
  data: {
    name: string
    nameAlias?: string
    value: string
    valueAlias?: string
    scale: string
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数'
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g
    return num.toString().replace(reg, ',')
  }
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1
  }, 0)
  const transedTotal = transNumberUnit(total)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '')
          + params.name
          + ': '
          + Number(params.value).toFixed(percision)
          + ' '
          + unit
        )
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      right: 100,
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 120,
            fontSize: 12,
            color: useAppStore().isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 80,
            fontSize: 12,
            color: '#00ff00'
          },
          downRate: {
            align: 'left',
            fontSize: 12,
            color: '#409EFF'
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| '
                + (data[i].nameAlias || name)
                + '}'
                + '{value| '
                + (data[i].valueAlias || data[i].value)
                + ' '
                + (unit || '')
                + '}'
                + '{downRate| '
                + (data[i].scale || '')
                + '}'
              )
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|'
          + title
          + ((unit && '(' + transedTotal.unit + unit + ')')
            || '(' + transedTotal.unit + ')')
          + '}\n{val|'
          + formatNumber(transedTotal.value.toFixed(percision))
          + '}',
        top: 'center',
        left: '30%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['65%', '80%'],
        center: ['30%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          formatter: params => {
            return (
              '{icon|●}{name|'
              + params.name
              + '}{value|'
              + formatNumber(Number(params.value || '0').toFixed(percision))
              + '}'
            )
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  }
  return option
}
export const lineOption = (dateX?: any, data1?: any, data2?: any) => {
  const option = {
    grid: {
      left: 50,
      right: 50,
      top: 20,
      bottom: 20
    },
    tooltip: {
      trigger: 'axis',
      formatter: ' {b} {a} <br/> {c} m³'
    },
    xAxis: {
      type: 'category',
      data: dateX || ['1', '2', '3', '4', '5', '6', '7']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '供水量',
        data: data1 || [820, 932, 901, 934, 1290, 1330, 1320],
        type: 'line',
        smooth: true
      }
    ]
  }
  return option
}

export const pieOption = () => {
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: 'center',
      right: 'right',
      textStyle: {
        color: '#656b84'
      }
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' }
        ]
      }
    ]
  }
  return option
}

export const barOption = () => {
  const option = {
    legend: {
      width: 400,
      type: 'scroll',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    grid: {
      left: 50,
      right: 50,
      height: '60%'
    },
    xAxis: {
      type: 'category',
      data: [1, 2, 3, 4, 5, 6, 7, 8, 9]
    },
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: '供水量(m³)',
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#ffffff' // '#333'
            types: 'solid'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#303958' : '#ccc',
            type: [5, 10],
            dashOffset: 5
          }
        }
      },
      {
        type: 'value',
        name: 'Temperature',
        min: 0,
        max: 25,
        interval: 5,
        axisLabel: {
          formatter: '{value} °C'
        }
      }
    ],
    series: [
      {
        name: 'Evaporation',
        type: 'bar',
        tooltip: {
          valueFormatter(value) {
            return value + ' ml'
          }
        },
        data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6]
      },
      {
        name: 'Temperature',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter(value) {
            return value + ' °C'
          }
        },
        data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5]
      }
    ]
  }
  return option
}

export const pieEcharts = () => {
  const option = {
    series: [
      {
        name: '瞬时流量',
        emphasis: {
          disabled: true,
          scale: false
        },
        type: 'pie',
        radius: ['60%', '70%'],
        color: ['#70CA61', '#318DFF33'],
        itemStyle: {
          borderCap: 'square'
        },
        labelLine: {
          length: 30
        },
        label: {
          show: false
        },
        data: [
          { value: 80, name: '瞬时流量' },
          { value: 20, name: 'Direct' }
        ]
      },
      {
        name: '供水量',
        type: 'pie',
        emphasis: {
          disabled: true,
          scale: false
        },
        slient: false,
        radius: ['80%', '90%'],
        color: ['#318DFF', '#318DFF33'],
        itemStyle: {
          borderCap: 'round'
        },
        labelLine: {
          length: 30
        },
        label: {
          show: false
        },
        data: [
          { value: 70, name: '供水量' },
          { value: 40, name: 'Direct' }
        ]
      }
    ]
  }
  return option
}
