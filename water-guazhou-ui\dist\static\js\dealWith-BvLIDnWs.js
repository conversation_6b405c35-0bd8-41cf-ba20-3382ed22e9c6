import{_ as R}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{_ as M}from"./index-C9hz-UZb.js";import{d as V,b3 as I,c as u,M as T,r as i,s as q,x as h,S as B,o as L,a8 as k,g as x,n as Y,q as c,F as $,p as d,i as a,cy as N,bh as A,h as K,aw as O,j as U,J as H,_ as j,b7 as z,C as G}from"./index-r0dFAfgr.js";import{I as p}from"./common-CvK_P_ao.js";import{c as J,e as P,f as Q}from"./malfunctionRepair-CM_eL_AA.js";import{f as X}from"./DateFormatter-Bm9a68Ax.js";const Z={class:"header-wrapper"},ee={class:"content-wrapper"},te=V({__name:"dealWith",setup(ae){const m=I(),f=u(),n=u(),_=u(new Date().toString()),{$btnPerms:g}=T(),r=i({id:"",title:""}),y=i({title:"",submit:e=>{l(e)},defaultValue:{},group:[{fields:[{xl:5,type:"input",label:"处理方案",field:"name"},{xl:4,type:"btn-group",btns:[{text:"查询",icon:p.QUERY,perm:!0,click:()=>{var e;(e=f.value)==null||e.Submit()}},{type:"default",text:"重置",svgIcon:q(z),perm:!0,click:()=>{_.value=new Date().toString(),l()}},{type:"success",text:"新增",icon:p.ADD,perm:!0,click:()=>D("新建")}]}]}]}),s=i({title:"新增",labelWidth:"120px",dialogWidth:"500px",submit:e=>{e.mainId=r.id;let t="新增成功";e.id&&(t="修改成功"),J(e).then(()=>{var o;h.success(t),l(),(o=n.value)==null||o.closeDialog()})},defaultValue:{},group:[{fields:[{type:"textarea",label:"解决方案",field:"name",rules:[{required:!0,message:"请输入解决方案"}]},{type:"textarea",label:"故障具体描述",field:"remark",rules:[{required:!0,message:"请输入故障具体描述"}]}]}]}),b=i({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"解决方案",prop:"name"},{label:"故障具体描述",prop:"remark"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTime",formatter:e=>X(e.createTime,"YYYY-MM-DD HH:mm:ss")}],operationWidth:"220px",operations:[{type:"primary",text:"编辑",icon:p.EDIT,perm:g("RoleManageEdit"),click:e=>v(e)},{type:"danger",text:"删除",perm:g("RoleManageDelete"),icon:p.DELETE,click:e=>C(e)}],dataList:[],pagination:{hide:!0}}),D=e=>{var t;s.title=e,s.defaultValue={},(t=n.value)==null||t.openDialog()},v=e=>{var t;s.title="编辑",s.defaultValue={...e||{}},(t=n.value)==null||t.openDialog()},C=e=>{B("确定删除指定处理方案?","删除提示").then(()=>{P([e.id]).then(()=>{h.success("删除成功"),l()})})},l=async e=>{const t={...e||{}};Q(r.id,t).then(o=>{b.dataList=o.data.data||[]})};return L(()=>{r.id=k(()=>m.currentRoute.value.query.id||""),r.title=k(()=>m.currentRoute.value.query.title||""),l()}),(e,t)=>{const o=H,w=j,E=M,F=W,S=R;return x(),Y("div",{class:O(["wrapper",{isDark:a(U)().isDark}])},[c(E,{class:"detail_card",style:{padding:"10px"}},{default:$(()=>[d("div",Z,[c(o,{text:!0,icon:a(N),onClick:t[0]||(t[0]=oe=>e.$router.back())},null,8,["icon"]),d("span",null,A(a(r).title)+" 处理方案",1)]),d("div",ee,[(x(),K(w,{key:a(_),ref_key:"refForm",ref:f,config:a(y)},null,8,["config"]))])]),_:1}),c(F,{config:a(b),class:"card-table"},null,8,["config"]),c(S,{ref_key:"refForm1",ref:n,config:a(s)},null,8,["config"])],2)}}}),ue=G(te,[["__scopeId","data-v-a6fcef38"]]);export{ue as default};
