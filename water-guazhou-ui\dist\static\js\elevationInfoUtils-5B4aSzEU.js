import{T as f,R as s,$ as l}from"./index-r0dFAfgr.js";import{r as m}from"./MapView-DaoQedLH.js";function d(e){return e?x:P}function b(e,t){return f(t)||!t.mode?d(e).mode:t.mode}function p(e,t){return s(t)?t:d(e)}function g(e,t){return b(!!s(e)&&e.hasZ,t)}function $(e,t){return p(!!s(e)&&!!e.hasZ,t)}function E(e){const t=v(e);return g(e.geometry,t)}function w(e){const t=v(e),n=g(e.geometry,t);return{mode:n,offset:s(t)&&n!=="on-the-ground"?l(t.offset,0)*m(l(t.unit,"meters")):0}}function z(e){if(E(e)==="on-the-ground")return!1;const t=v(e),n=s(t)&&t.featureExpressionInfo?t.featureExpressionInfo.expression:null;return!(!n||n==="0")}function v(e){return e.layer&&"elevationInfo"in e.layer?e.layer.elevationInfo:null}function T(e,t,n){if(f(n)||!n.mode)return;const o=e.hasZ?e.z:0,r=s(n.offset)?n.offset:0;switch(n.mode){case"absolute-height":return o-r;case"on-the-ground":return 0;case"relative-to-ground":return o-(l(t.elevationProvider.getElevation(e.x,e.y,o,e.spatialReference,"ground"),0)+r);case"relative-to-scene":return o-(l(t.elevationProvider.getElevation(e.x,e.y,o,e.spatialReference,"scene"),0)+r)}}function j(e,t,n,o=null){return h(e,t.x,t.y,t.hasZ?t.z:0,t.spatialReference,n,o)}function k(e,t,n,o,r=null){return h(e,t[0],t[1],t.length>2?t[2]:0,n,o,r)}function h(e,t,n,o,r,a,i=null){if(f(a))return;const u=s(i)?i.mode:"absolute-height";if(u==="on-the-ground")return 0;const{absoluteZ:c}=Z(t,n,o,r,e,a);return y(c,t,n,o,r,e,i,u)}function Z(e,t,n,o,r,a){const i=s(a.offset)?a.offset:0;switch(a.mode){case"absolute-height":return{absoluteZ:n+i,elevation:0};case"on-the-ground":{const u=l(r.elevationProvider.getElevation(e,t,0,o,"ground"),0);return{absoluteZ:u,elevation:u}}case"relative-to-ground":{const u=l(r.elevationProvider.getElevation(e,t,n,o,"ground"),0);return{absoluteZ:n+u+i,elevation:u}}case"relative-to-scene":{const u=l(r.elevationProvider.getElevation(e,t,n,o,"scene"),0);return{absoluteZ:n+u+i,elevation:u}}}}function y(e,t,n,o,r,a,i,u){const c=s(i)&&s(i.offset)?i.offset:0;switch(u){case"absolute-height":return e-c;case"relative-to-ground":return e-(l(a.elevationProvider.getElevation(t,n,o,r,"ground"),0)+c);case"relative-to-scene":return e-(l(a.elevationProvider.getElevation(t,n,o,r,"scene"),0)+c)}}function q(e,t){if(f(t))return!1;const{mode:n}=t;return s(n)&&(e==="scene"&&n==="relative-to-scene"||e==="ground"&&n!=="absolute-height")}const x={mode:"absolute-height",offset:0},P={mode:"on-the-ground",offset:null};export{x as E,P as Z,$ as a,z as c,T as d,w as f,j as g,k as h,g as i,E as l,p as s,b as u,q as y};
