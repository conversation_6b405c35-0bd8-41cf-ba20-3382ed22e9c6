<template>
  <el-popover
    placement="right"
    trigger="click"
    :width="200"
  >
    <template #reference>
      <span style="cursor: pointer">
        <el-tooltip
          v-if="props.showTooltip"
          :content="'过滤表格字段'"
        >
          <Icon :icon="'ep:filter'"></Icon>
        </el-tooltip>
        <Icon
          v-else
          :icon="'ep:filter'"
        ></Icon>
      </span>
    </template>
    <div class="filter-box">
      <div class="filter-header">
        <el-checkbox
          v-model="checkAll"
          @change="handleCheckAll"
        >
          全选
        </el-checkbox>
      </div>
      <div class="filter-tree">
        <el-tree
          ref="refTree"
          :data="props.columns"
          show-checkbox
          default-expand-all
          node-key="prop"
          :props="defaultProps"
          :draggable="true"
          :check-strictly="true"
          @check-change="handleCheckChange"
          @node-drop="handleNodeDrop"
        />
      </div>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { ElTree } from 'element-plus'

const props = defineProps<{ columns: IFormTableColumn[]; showTooltip?: boolean }>()

const refTree = ref<InstanceType<typeof ElTree>>()
const defaultProps = {
  children: 'subColumns',
  label: 'label'
}
const checkAll = ref<boolean>(true)
const getAllKeys = (column: IFormTableColumn, allKey: string[] = []) => {
  allKey.push(column.prop)
  if (column.subColumns?.length) {
    column.subColumns.map(item => getAllKeys(item, allKey))
  }
}
const handleCheckAll = (checked: any) => {
  const keys = []
  props.columns.map(item => getAllKeys(item, keys))
  if (checked) refTree.value?.setCheckedKeys(keys)
  else refTree.value?.setCheckedKeys([])
}
const handleCheckChange = (data: IFormTableColumn, checked: boolean) => {
  // console.log(checked)
  data.hidden = !checked
}
const handleNodeDrop = (node: any) => {
  refTree.value?.setChecked(node.data.prop, node.checked, false)
}
watch(
  () => props.columns,
  () => {
    checkAll.value = true
    handleCheckAll(true)
  }
)
onMounted(() => {
  handleCheckAll(true)
})
</script>
<style lang="scss" scoped>
.filter-box {
  overflow-y: auto;
  height: 240px;
  .filter-header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 24px;
  }
  .filter-tree {
    height: calc(100% - 40px);
    overflow-y: auto;
  }
}
</style>
