package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataCorrectRecords;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface PipeCopyDataReadMeterDataService {

    PageData<PipeCopyDataReadMeterData> getList(PartitionCustRequest partitionCustRequest);

    PipeCopyDataReadMeterData correct(PipeCopyDataReadMeterData pipeCopyDataReadMeterData, String userId);

    PageData<PipeCopyDataCorrectRecords> getCorrectRecords(PartitionCustRequest partitionCustRequest);

    /**
     * 分区校准用水量
     * @param partitionIdList
     * @param startTime
     * @param endTime
     * @param copyWater
     * @param correctCopyWater
     */
    void getUseAndCorrectWater(List<String> partitionIdList, Long startTime, Long endTime, Map<String, BigDecimal> copyWater, Map<String, BigDecimal> correctCopyWater);

}
