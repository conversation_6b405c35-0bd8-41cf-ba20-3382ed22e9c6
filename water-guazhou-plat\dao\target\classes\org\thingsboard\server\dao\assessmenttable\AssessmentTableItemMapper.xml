<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.assessmenttable.AssessmentTableItemMapper">

    <sql id="Base_Column_List">
        id, assessment_table_id, indicator_name, indicator_desc, indicator_type, indicator_unit, 
        weight, target_value, actual_value, score, scorer, score_time, remark, create_time, sort_num
    </sql>

    <!-- 根据考核表ID查询明细项列表 -->
    <select id="selectByAssessmentTableId" resultType="org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableItemEntity">
        SELECT
        <include refid="Base_Column_List" />
        FROM assessment_table_item
        WHERE assessment_table_id = #{assessmentTableId}
        ORDER BY sort_num ASC
    </select>
    
    <!-- 根据考核表ID删除明细项 -->
    <delete id="deleteByAssessmentTableId">
        DELETE FROM assessment_table_item
        WHERE assessment_table_id = #{assessmentTableId}
    </delete>
    
    <!-- 批量删除明细项（根据考核表ID列表） -->
    <delete id="batchDeleteByAssessmentTableIds">
        DELETE FROM assessment_table_item
        WHERE assessment_table_id IN
        <foreach collection="assessmentTableIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 