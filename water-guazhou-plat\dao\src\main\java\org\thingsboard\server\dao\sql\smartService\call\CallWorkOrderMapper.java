package org.thingsboard.server.dao.sql.smartService.call;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.SeatsCallDTO;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface CallWorkOrderMapper extends BaseMapper<CallWorkOrder> {

    List<CallWorkOrder> getList(@Param("isDispatch") String isDispatch, @Param("seatsId") String seatsId, @Param("type") String type, @Param("topic") String topic, @Param("departmentId") String departmentId, @Param("status") String status, @Param("chaoShi") String chaoShi, @Param("cuiBan") String cuiBan, @Param("zhongZhi") String zhongZhi, @Param("manYi") String manYi, @Param("zenRenFang") String zenRenFang, @Param("jinChang") String jinChang, @Param("chongFa") String chongFa, @Param("heGe") String heGe, @Param("wuPan") String wuPan, @Param("serialNo") String serialNo, @Param("keywords") String keywords, @Param("phone") String phone, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("isDispatch") String isDispatch, @Param("seatsId") String seatsId, @Param("type") String type, @Param("topic") String topic, @Param("departmentId") String departmentId, @Param("status") String status, @Param("chaoShi") String chaoShi, @Param("cuiBan") String cuiBan, @Param("zhongZhi") String zhongZhi, @Param("manYi") String manYi, @Param("zenRenFang") String zenRenFang, @Param("jinChang") String jinChang, @Param("chongFa") String chongFa, @Param("heGe") String heGe, @Param("wuPan") String wuPan, @Param("serialNo") String serialNo, @Param("keywords") String keywords, @Param("phone") String phone, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List getMonthCallLogStatistics(@Param("seatsId") String seatsId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("tenantId") String tenantId);

    List getYearCallLogStatistics(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("tenantId") String tenantId);

    List<SeatsCallDTO> getSeatsCall(@Param("seatsId") String seatsId, @Param("startTime") Long start, @Param("endTime") Long end, @Param("tenantId") String tenantId);

    List<CallWorkOrder> getListByTime(@Param("startTime") Long start, @Param("endTime") Long end, @Param("tenantId") String tenantId);

    List<CallWorkOrder> getListByTimeByWorkOrderType(@Param("startTime") Long start, @Param("endTime") Long end, @Param("tenantId") String tenantId);

    CallWorkOrder getDetail(@Param("workOrderId") String workOrderId);

    int getListByTimeCount(@Param("startTime") Long start, @Param("endTime") Long end);

    List<JSONObject> getKpiList(@Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId);
}
