<template>
  <el-menu
    :default-active="state.activeIndex"
    class="horizontal-menu"
    :class="appStore.isDark ? 'darkblue' : ''"
    mode="horizontal"
    :show-timeout="30"
    :menu-trigger="'click'"
    :unique-opened="true"
    :background-color="appStore.isDark ? 'transparent' : '#fff'"
    :text-color="appStore.isDark ? '#fff' : '#000'"
  >
    <MenuItem
      v-for="(menu, i) in state.menus"
      :key="i"
      :authority="false"
      :item="menu"
      :popper-append-to-body="false"
      @click="(item) => $emit('click', item)"
    ></MenuItem>
  </el-menu>
</template>
<script lang="ts" setup>
import { getMenus } from '../../config';
import { useAppStore } from '@/store';

const appStore = useAppStore();
defineEmits(['click']);
const state = reactive<{
  activeIndex: string;
  menus: any[];
}>({
  activeIndex: '1',
  menus: getMenus()
});
</script>
<style lang="scss" scoped>
.horizontal-menu {
  --el-menu-item-height: 38px;
  display: flex;
  border-radius: 4px;
  align-items: center;
  padding: 0 12px;
  &.darkblue {
    background-color: rgba(21, 45, 68, 0.9);
    &:hover {
      background-color: rgba(21, 45, 68, 1);
    }
  }
  :deep(.el-sub-menu) {
    &.is-active {
      .el-sub-menu__title {
        border-bottom: none;
      }
    }
  }
}
:deep(.el-menu--popup) {
  min-width: 100px;
}
</style>
