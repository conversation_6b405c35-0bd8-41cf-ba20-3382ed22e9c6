import{_ as M}from"./index-C9hz-UZb.js";import{d as j,j as K,dI as Q,a6 as R,r as v,c as f,bF as T,s as J,o as X,bB as E,ay as $,g as G,n as H,q as d,i as c,p as r,F as y,bh as w,aq as Y,al as Z,aj as ee,C as te}from"./index-r0dFAfgr.js";import{_ as ae}from"./CardTable-rdWOL4_6.js";import{_ as se}from"./CardSearch-CB_HNR-Q.js";import{l as ne,b as ie}from"./echart-D5stWtDc.js";import{a as le,b as oe}from"./statisticalAnalysis-D5JxC4wJ.js";import"./Search-NSrhrIa_.js";const re={class:"wrapper"},ce={class:"main"},pe={class:"left"},ue={class:"right"},de={class:"card-title"},me={class:"title"},fe={class:"card-title"},he={class:"title"},ye={class:"card-title"},_e={class:"title"},ge=j({__name:"index",setup(be){const h=K(),z=new Q,A=R(),i=v({queryType:"day",chartOption:null,szChartOption:null,detailTitle:"",zxTitle:"",szTitle:""}),k=f(),O=f(),S=f(),q=f(),I=f(),x=f(),D=f(),L=v({defaultParams:{queryType:"day",date:T().format()},filters:[{type:"input",label:"水源名字",field:"name"},{type:"radio-button",field:"queryType",options:[{label:"日分析",value:"day"},{label:"月分析",value:"month"},{label:"年分析",value:"year"}],label:"分析类型",onChange:e=>{var a;const t=(a=L.filters)==null?void 0:a.find(s=>s.field==="date");t.type=e==="month"?"month":e==="year"?"year":"date",i.queryType=e}},{type:"date",label:"选择时间",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>N(),svgIcon:J(Z)},{text:"导出",perm:!0,type:"warning",svgIcon:J(ee),click:()=>U()}]}]}),_=v({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"name",label:"水源名称",minWidth:100},{prop:"totalFlow",label:"供水量",unit:"(m³)",minWidth:100},{prop:"energy",label:"用电量",unit:"(kw.h)",minWidth:120},{prop:"totalFlow",label:"本期吨水电耗",unit:"(kw.h/m³)",minWidth:180},{prop:"lastTimeUnitConsumption",label:"上期吨水电耗",unit:"(kw.h/m³)",minWidth:180},{prop:"differenceValue",label:"吨水电耗差值",unit:"(kw.h/m³)",minWidth:180},{prop:"changeRate",label:"变化率",unit:"(%)",minWidth:120}],operations:[],showSummary:!1,operationWidth:"150px",handleRowClick:e=>F(e),pagination:{hide:!0}}),g=v({loading:!0,border:!0,dataList:[],currentRowKey:"id",highlightCurrentRow:!1,columns:[{prop:"date",label:"时间",unit:"(日)",width:"100px"},{prop:"totalFlow",label:"供水量",unit:"(m³)",width:"120px"},{prop:"energy",label:"用电量",unit:"(kw.h)",width:"120px"},{prop:"unitConsumption",label:"吨位耗电",unit:"(kw.h/m³)",minWidth:150},{prop:"differenceValue",label:"吨水电耗差值",unit:"(kw.h/m³)",minWidth:180},{prop:"changeRate",label:"变化率",unit:"(%)",width:"100px"}],operations:[],showSummary:!1,pagination:{hide:!0},spanMethod:({rowIndex:e,columnIndex:t})=>{let a=1,s=1;return(t===4||t===5)&&(e===0?(a=g.columns.length-1,s=1):(a=0,s=0)),{rowspan:a,colspan:s}}});X(()=>{N()});const N=()=>{var a;_.loading=!0;const e=((a=S.value)==null?void 0:a.queryParams)||{},t={queryType:e.queryType,start:T(e.date).startOf(i.queryType).valueOf(),end:T(e.date).endOf(i.queryType).valueOf(),name:e.name};le(t).then(s=>{g.loading=!0;const n=s.data.data;_.dataList=n,_.currentRow=n[0],F(n[0])})},F=e=>{var n;const t=((n=S.value)==null?void 0:n.queryParams)||{};i.detailTitle=e.name+"数据详情",i.zxTitle=e.name+"供水量、用电量曲线",i.szTitle=e.name+"吨水电耗树状图";const a=[{date:e.time,totalFlow:e.totalFlow,energy:e.energy,unitConsumption:e.unitConsumption,differenceValue:e.differenceValue,changeRate:e.changeRate},{date:e.lastTime,totalFlow:e.lastTimeTotalFlow,energy:e.lastTimeEnergy,unitConsumption:e.lastTimeUnitConsumption,differenceValue:e.differenceValue,changeRate:e.changeRate}];g.dataList=a;const s={stationId:e.stationId,queryType:t.queryType,start:T(t.date).startOf(i.queryType).valueOf(),end:T(t.date).endOf(i.queryType).valueOf()};oe(s).then(l=>{const o=l.data.data;V(o)}),_.loading=!1,g.loading=!1},V=e=>{const t=e.flowList,a=[];for(const l in t)a.push(i.queryType==="day"?parseInt(l)+"时":i.queryType==="month"?parseInt(l)+1+"日":parseInt(l)+1+"月");const s=ne(null,null,null,80,80);s.yAxis[1].name="电耗(kw/h)",s.yAxis[1].splitNumber=5,s.xAxis.data=a,B(e,s);const n=ie("","",[],[],80,80);n.xAxis.data=a,P(e,n)},B=(e,t)=>{var W;console.log(e.lastTimeFlowList),t.series=[];const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{color:h.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:h.isDark?"#ffffff":"#000000"}}]}},s=e==null?void 0:e.lastTimeFlowList,n=JSON.parse(JSON.stringify(a));n.data=s.map(u=>u.value),n.name="供水量"+s[0].ts,t.series.push(n);const l=e==null?void 0:e.flowList,o=JSON.parse(JSON.stringify(a));o.data=l,o.name="供水量"+l[0].ts,t.series.push(o);const b=e==null?void 0:e.energyList,p=JSON.parse(JSON.stringify(a));p.yAxisIndex=1,p.data=b.map(u=>u.value),p.name="耗电量"+b[0].ts,t.series.push(p);const C=e==null?void 0:e.lastTimeEnergyList,m=JSON.parse(JSON.stringify(a));m.yAxisIndex=1,m.data=C.map(u=>u.value),m.name="耗电量"+C[0].ts,t.series.push(m),console.log("这显然图",t.series),(W=k.value)==null||W.clear(),E(()=>{x.value&&A.listenTo(x.value,()=>{var u;i.chartOption=t,(u=k.value)==null||u.resize()})})},P=(e,t)=>{var b;t.series=[];const a={name:"",type:"bar",barMaxWidth:40,data:[],markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:h.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:h.isDark?"#ffffff":"#000000"}}]}},s=e==null?void 0:e.lastTimeUnitConsumptionList,n=JSON.parse(JSON.stringify(a));n.data=s.map(p=>p.value),n.name="吨水能耗"+s[0].ts,t.series.push(n);const l=e==null?void 0:e.unitConsumption,o=JSON.parse(JSON.stringify(a));o.data=l.map(p=>p.value),o.name="吨水能耗"+l[0].ts,t.series.push(o),(b=O.value)==null||b.clear(),E(()=>{const C=R({callOnAdd:!0});x.value&&C.listenTo(D.value,()=>{var m;i.szChartOption=t,(m=O.value)==null||m.resize()})})},U=()=>{z.addElTable(q.value),z.export()};return(e,t)=>{const a=se,s=ae,n=Y,l=M,o=$("VChart");return G(),H("div",re,[d(a,{ref_key:"cardSearch",ref:S,config:c(L)},null,8,["config"]),r("div",ce,[r("div",pe,[d(s,{ref_key:"refCardTable",ref:q,class:"card-table",config:c(_)},null,8,["config"])]),r("div",ue,[d(l,{class:"card-chart",title:" "},{title:y(()=>[r("div",de,[r("span",me,w(c(i).detailTitle),1)])]),default:y(()=>[d(n,{ref_key:"refDetail",ref:I,class:"card-table",config:c(g)},null,8,["config"])]),_:1}),d(l,{title:" ",class:"card-chart"},{title:y(()=>[r("div",fe,[r("span",he,w(c(i).zxTitle),1)])]),default:y(()=>[r("div",{ref_key:"zxDiv",ref:x,class:"chart-box"},[d(o,{ref_key:"refzxChart",ref:k,theme:c(h).isDark?"blackBackground":"whiteBackground",option:c(i).chartOption},null,8,["theme","option"])],512)]),_:1}),d(l,{title:" ",class:"card-chart"},{title:y(()=>[r("div",ye,[r("span",_e,w(c(i).szTitle),1)])]),default:y(()=>[r("div",{ref_key:"szDiv",ref:D,class:"chart-box"},[d(o,{ref_key:"refszChart",ref:O,theme:c(h).isDark?"blackBackground":"whiteBackground",option:c(i).szChartOption},null,8,["theme","option"])],512)]),_:1})])])])}}}),we=te(ge,[["__scopeId","data-v-d7ef5c5d"]]);export{we as default};
