<template>
  <div class="scheme-header">
    <span>{{ props.title }}</span>
    <el-button
      v-if="gisSaveScheme"
      :size="'small'"
      @click="schemeManage"
    >
      方案管理
    </el-button>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{ title: string }>()
const emit = defineEmits(['schemeClick'])
const schemeManage = () => {
  emit('schemeClick')
}
const gisSaveScheme = ref<boolean>(window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme ?? false)
</script>
<style lang="scss" scoped>
.scheme-header {
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
