const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/objectResourceUtils-vFx_v_2w.js","static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/mat3f64-BVJGbF0t.js","static/js/mat4f64-BCm7QTSd.js","static/js/BufferView-BcX1hwIm.js","static/js/vec33-BEptSvzS.js","static/js/DefaultMaterial_COLOR_GAMMA-BI6mUG8I.js","static/js/types-Cezv0Yl1.js","static/js/enums-BDQrMlcz.js","static/js/Version-Q4YOKegY.js","static/js/quat-CM9ioDFt.js","static/js/quatf64-QCogZAoR.js","static/js/resourceUtils-CLKdXOwM.js","static/js/basicInterfaces-Dc_Mm1a-.js","static/js/Indices-iFKW8TWb.js","static/js/NestedMap-DgiGbX8E.js","static/js/requestImageUtils-DBUrisSF.js","static/js/VertexColor.glsl-CUs3Tjt8.js","static/js/OrderIndependentTransparency-C5Ap76ew.js","static/js/Texture-BYqObwfn.js","static/js/FramebufferObject-8j9PRuxE.js","static/js/Util-sSNWzwlq.js","static/js/triangle-lwOWqU0w.js","static/js/sphere-NgXH-gLx.js","static/js/lineSegment-DQ0q5UHF.js","static/js/VertexAttribute-BAIQI41G.js","static/js/doublePrecisionUtils-B0owpBza.js","static/js/vec3f32-nZdmKIgz.js","static/js/VertexElementDescriptor-BOD-G50G.js","static/js/InterleavedLayout-EYSqXknm.js","static/js/symbolColorUtils-ByJCrvqG.js"])))=>i.map(i=>d[i]);
import{R as a,a$ as h,a3 as d}from"./index-r0dFAfgr.js";import{U as b}from"./pe-B8dP0-Ut.js";import{s as i}from"./Point-WxyopZva.js";import{cq as w,cr as v,cs as z,bQ as g,ct as j}from"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";let s=y();function y(){return new w(50)}function O(){s=y()}function T(e,r){if(e.type==="icon")return m(e,r);if(e.type==="object")return f(e,r);throw new i("symbol3d:unsupported-symbol-layer","computeLayerSize only works with symbol layers of type Icon and Object")}async function k(e,r){if(e.type==="icon")return L(e,r);if(e.type==="object")return B(e,r);throw new i("symbol3d:unsupported-symbol-layer","computeLayerSize only works with symbol layers of type Icon and Object")}async function m(e,r){var t,n;if((t=e.resource)!=null&&t.href)return _(e.resource.href).then(o=>[o.width,o.height]);if((n=e.resource)!=null&&n.primitive)return a(r)?[r,r]:[256,256];throw new i("symbol3d:invalid-symbol-layer","symbol layers of type Icon must have either an href or a primitive resource")}function L(e,r){return m(e,r).then(t=>{if(e.size==null)return t;const n=t[0]/t[1];return n>1?[e.size,e.size/n]:[e.size*n,e.size]})}function _(e){return b(e,{responseType:"image"}).then(r=>r.data)}function f(e,r){return S(e,r).then(t=>v(t))}async function B(e,r){const t=await f(e,r);return z(t,e)}async function S(e,r){var n;if(!e.isPrimitive){const o=h((n=e.resource)==null?void 0:n.href),c=s.get(o);if(c!==void 0)return c;const l=await d(()=>import("./objectResourceUtils-vFx_v_2w.js").then(p=>p.o),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35])),u=await l.fetch(o,{disableTextures:!0});return s.put(o,u.referenceBoundingBox),u.referenceBoundingBox}let t=null;if(e.resource&&e.resource.primitive&&(t=g(j(e.resource.primitive)),a(r)))for(let o=0;o<t.length;o++)t[o]*=r;return t?Promise.resolve(t):Promise.reject(new i("symbol:invalid-resource","The symbol does not have a valid resource"))}export{O as clearBoundingBoxCache,m as computeIconLayerResourceSize,T as computeLayerResourceSize,k as computeLayerSize,f as computeObjectLayerResourceSize};
