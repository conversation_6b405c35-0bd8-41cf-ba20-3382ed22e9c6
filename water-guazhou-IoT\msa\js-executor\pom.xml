<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.thingsboard</groupId>
        <version>2.3.0</version>
        <artifactId>msa</artifactId>
    </parent>
    <groupId>org.thingsboard.msa</groupId>
    <artifactId>js-executor</artifactId>
    <packaging>pom</packaging>

    <name>ThingsBoard JavaScript Executor Microservice</name>
    <url>https://thingsboard.io</url>
    <description>Service executing JavaScript functions in sandboxed environment</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <main.dir>${basedir}/../..</main.dir>
        <pkg.name>tb-js-executor</pkg.name>
        <docker.name>tb-js-executor</docker.name>
        <pkg.user>thingsboard</pkg.user>
        <pkg.unixLogFolder>/var/log/${pkg.name}</pkg.unixLogFolder>
        <pkg.installFolder>/usr/share/${pkg.name}</pkg.installFolder>
        <pkg.linux.dist>${project.build.directory}/package/linux</pkg.linux.dist>
        <pkg.win.dist>${project.build.directory}/package/windows</pkg.win.dist>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sun.winsw</groupId>
            <artifactId>winsw</artifactId>
            <classifier>bin</classifier>
            <type>exe</type>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.0</version>
                <configuration>
                    <installDirectory>target</installDirectory>
                    <workingDirectory>${basedir}</workingDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>install node and npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>v8.11.3</nodeVersion>
                            <npmVersion>6.4.1</npmVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-winsw-service</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.sun.winsw</groupId>
                                    <artifactId>winsw</artifactId>
                                    <classifier>bin</classifier>
                                    <type>exe</type>
                                    <destFileName>service.exe</destFileName>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>${pkg.win.dist}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-linux-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.linux.dist}/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>config</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/unix.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-linux-init</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.linux.dist}/init</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/init</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/unix.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-win-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.win.dist}/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>config</directory>
                                    <excludes>
                                        <exclude>tb-js-executor.conf</exclude>
                                    </excludes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/windows.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-control</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/control</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/control</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/unix.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-windows-control</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.win.dist}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/windows</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/windows.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-docker-config</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.fortasoft</groupId>
                <artifactId>gradle-maven-plugin</artifactId>
                <configuration>
                    <gradleVersion>2.13</gradleVersion>
                    <gradleDistributionUrl>https://gz01-srdart.srdcloud.cn/generic/public/gradle-release-generic-remote/gradle-2.13-bin.zip</gradleDistributionUrl>
                    <tasks>
                        <task>build</task>
                        <task>buildDeb</task>
                        <task>buildRpm</task>
                    </tasks>
                    <args>
                        <arg>-PprojectBuildDir=${project.build.directory}</arg>
                        <arg>-PprojectVersion=${project.version}</arg>
                        <arg>-PpkgName=${pkg.name}</arg>
                        <arg>-PpkgUser=${pkg.user}</arg>
                        <arg>-PpkgInstallFolder=${pkg.installFolder}</arg>
                        <arg>-PpkgLogFolder=${pkg.unixLogFolder}</arg>
                    </args>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>invoke</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <finalName>${pkg.name}</finalName>
                    <descriptors>
                        <descriptor>src/main/assembly/windows.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>build-docker-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${docker.name}</repository>
                            <verbose>true</verbose>
                            <googleContainerRegistryEnabled>false</googleContainerRegistryEnabled>
                            <contextDirectory>${project.build.directory}</contextDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>tag-docker-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>tag</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${docker.name}</repository>
                            <tag>${project.version}</tag>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>npm-start</id>
            <activation>
                <property>
                    <name>npm-start</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>1.0</version>
                        <configuration>
                            <installDirectory>target</installDirectory>
                            <workingDirectory>${basedir}</workingDirectory>
                        </configuration>
                        <executions>
                            <execution>
                                <id>npm start</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>

                                <configuration>
                                    <arguments>start</arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>push-docker-image</id>
            <activation>
                <property>
                    <name>push-docker-image</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>push-latest-docker-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>latest</tag>
                                    <repository>${docker.repo}/${docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-version-docker-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>${project.version}</tag>
                                    <repository>${docker.repo}/${docker.name}</repository>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <repositories>
        <repository>
            <id>jenkins</id>
            <name>Jenkins Repository</name>
            <url>http://repo.jenkins-ci.org/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
