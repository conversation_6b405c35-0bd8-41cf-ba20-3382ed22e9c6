<!-- 单耗报表 -->
<template>
  <div class="wrapper">
    <div
      v-loading="totalLoading"
      class="main"
    >
      <div class="right">
        <CardSearch
          ref="cardSearch"
          :config="cardSearchConfig"
        />
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { Printer } from '@element-plus/icons-vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyConsumptionReport } from '@/api/secondSupplyManage/statisticalAnalysis'
import { reportType } from '../data/data'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'

const { getStationTree } = useStation()
const state = reactive<{
  queryType: 'day' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  title: string;
}>({
  queryType: 'day',
  treeDataType: 'Station',
  stationId: '',
  title: ''
})
const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

const totalLoading = ref<boolean>(false)
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: reportType,
      label: '报告类型',
      onChange: (val: any) => dateTypeChange(val)
    },

    { type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    { type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      } },
    { type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      } },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          icon: 'iconfont icon-xiazai',
          click: () => _exportWaterQuality()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

const dateTypeChange = (val: any) => {
  const date = cardSearchConfig.filters?.find(filter => filter.field === 'date') as any
  date.type = val === 'month' ? 'monthrange' : val === 'year' ? 'yearrange' : 'daterange'
  state.queryType = val
  console.log(dayjs().add(-1, 'month').startOf('month'))
  if (val === 'month') {
    cardSearchConfig.defaultParams = {
      ...cardSearchConfig.defaultParams,
      date: [dayjs().add(-1, 'month').startOf('month'), dayjs().endOf('month')]
    } as any
    cardSearch.value?.resetForm()
  }
}
// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[queryParams.type]
  const type = reportType.find(type => type.value === queryParams.type)
  state.title = TreeData.currentProject.label + '单耗报表' + '(' + type.label + dayjs(date[0]).format(type.data) + '至' + dayjs(date[1]).format(type.data) + ')'
  cardTableConfig.title = state.title
  console.log(queryParams.date)
  const params = {
    stationId,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf(),
    queryType: queryParams.type
  }
  getWaterSupplyConsumptionReport(params).then(res => {
    const data = res.data.data
    const columns = data.tableInfo.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })
    console.log(columns)
    cardTableConfig.columns = columns
    cardTableConfig.dataList = data.tableDataList
    cardTableConfig.loading = false
  })
}

// 导出水量报告
const _exportWaterQuality = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

onMounted(async () => {
  const treeData = await getStationTree('泵站')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()
})
</script>

<style lang="scss" scoped>
.main {
  height: 100%;
  display: flex;

  .left {
    width: 300px;
    border-right: 1px solid var(--el-border-color);
  }

  .right {
    width: 100%;
    // padding-left: 20px;
    height: 100%;
    overflow-y: auto;
  }
}
:deep(.title){
  width: 100%;
  text-align: center;
}
</style>
