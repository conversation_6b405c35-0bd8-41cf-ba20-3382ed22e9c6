package org.thingsboard.server.dao.smartService.call;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.*;
import org.thingsboard.server.dao.model.sql.smartService.call.CallIVRLog;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrderRemind;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrderRevisit;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemDict;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemWorkOrderType;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.sql.smartService.call.CallIVRLogMapper;
import org.thingsboard.server.dao.sql.smartService.call.CallWorkOrderMapper;
import org.thingsboard.server.dao.sql.smartService.call.CallWorkOrderRemindMapper;
import org.thingsboard.server.dao.sql.smartService.call.CallWorkOrderRevisitMapper;
import org.thingsboard.server.dao.sql.smartService.seats.SeatsUserMapper;
import org.thingsboard.server.dao.sql.smartService.system.SystemDictMapper;
import org.thingsboard.server.dao.sql.smartService.system.SystemWorkOrderTypeMapper;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderDetailMapper;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderAssignRequest;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class CallWorkOrderServiceImpl implements CallWorkOrderService {
    @Autowired
    private CallWorkOrderMapper callWorkOrderMapper;

    @Autowired
    protected SystemWorkOrderTypeMapper systemWorkOrderTypeMapper;

    @Autowired
    private NewlyWorkOrderMapper newlyWorkOrderMapper;

    @Autowired
    private WorkOrderDetailMapper workOrderDetailMapper;

    @Autowired
    private CallWorkOrderRevisitMapper callWorkOrderRevisitMapper;

    @Autowired
    private CallWorkOrderRemindMapper callWorkOrderRemindMapper;

    @Autowired
    private SeatsUserMapper seatsUserMapper;

    @Autowired
    private SystemDictMapper systemDictMapper;

    @Autowired
    private CallIVRLogMapper callIVRLogMapper;

    @Override
    public PageData getList(String isDispatch, String seatsId, String type, String topic, String departmentId, String status, String chaoShi, String cuiBan, String zhongZhi, String manYi, String zenRenFang, String jinChang, String chongFa, String heGe, String wuPan, String serialNo, String keywords, String phone, Long startTime, Long endTime, int page, int size, String tenantId) {

        List<CallWorkOrder> workOrderMapperList = callWorkOrderMapper.getList(isDispatch, seatsId, type, topic, departmentId, status, chaoShi, cuiBan, zhongZhi, manYi, zenRenFang, jinChang, chongFa, heGe, wuPan, serialNo, keywords, phone, startTime, endTime, page, size);
        
        for (CallWorkOrder callWorkOrker : workOrderMapperList) {
            callWorkOrker.setStatusName(WorkOrderStatus.valueOf(callWorkOrker.getStatus()).getStageName());
        }
        
        int total = callWorkOrderMapper.getListCount(isDispatch, seatsId, type, topic, departmentId, status, chaoShi, cuiBan, zhongZhi, manYi, zenRenFang, jinChang, chongFa, heGe, wuPan, serialNo, keywords, phone, startTime, endTime);

        return new PageData(total, workOrderMapperList);

    }

    @Override
    public CallWorkOrder save(CallWorkOrder callWorkOrder) {
        if (StringUtils.isBlank(callWorkOrder.getId())) {
            callWorkOrder.setCreateTime(new Date());
            // 简易单
            if ("0".equals(callWorkOrder.getIsDispatch())) {
                callWorkOrder.setSerialNo(RedisUtil.nextId(DataConstants.REDIS_KEY.WORK_ORDER_SERVICE, "JY"));
                callWorkOrderMapper.insert(callWorkOrder);
                return callWorkOrder;
            }

            // 生成工单
            WorkOrder workOrder = new WorkOrder();
            workOrder.setCreateTime(new Date());
            workOrder.setStatus(WorkOrderStatus.ASSIGN);
            if (StringUtils.isNotBlank(callWorkOrder.getType())) {
                SystemWorkOrderType systemWorkOrderType = systemWorkOrderTypeMapper.selectById(callWorkOrder.getType());
                if (systemWorkOrderType != null) {
                    workOrder.setTitle(systemWorkOrderType.getName());
                }
            }
            if (StringUtils.isNotBlank(callWorkOrder.getTopic())) {
                SystemWorkOrderType systemWorkOrderType = systemWorkOrderTypeMapper.selectById(callWorkOrder.getTopic());
                if (systemWorkOrderType != null) {
                    workOrder.setType(systemWorkOrderType.getName());
                }
            }
            workOrder.setAddress(callWorkOrder.getAddress());
            workOrder.setRemark(callWorkOrder.getRemark());
            // 工单来源
            workOrder.setSource("客服热线");
            workOrder.setAddress(callWorkOrder.getAddress());
            workOrder.setLevel(callWorkOrder.getLevel());
            workOrder.setReceiveDepartmentId(callWorkOrder.getReceiveDepartmentId());
            workOrder.setUploadUserId(callWorkOrder.getCreator());
            workOrder.setUploadPhone(callWorkOrder.getPhone());
            workOrder.setUploadAddress(callWorkOrder.getAddress());

            workOrder.setProcessUserId(callWorkOrder.getCreator());
            workOrder.setDirectDispatch(false);
            workOrder.setUploadUserId(callWorkOrder.getCreator());
            workOrder.setOrganizerId(callWorkOrder.getCreator());
            workOrder.setTenantId(callWorkOrder.getTenantId());
            workOrder.setProcessLevel(callWorkOrder.getProcessLevel());
            workOrder.setStepProcessUserId(callWorkOrder.getProcessUserId());
            workOrder.setProcessUserId(callWorkOrder.getCreator());
            workOrder.setEstimatedFinishTime(new Date(workOrder.getCreateTime().getTime() + (workOrder.getProcessLevel() * 60 * 1000)));
            workOrder.setSerialNo(newlyWorkOrderMapper.getSerialNo(workOrder.getId()));
            newlyWorkOrderMapper.insert(workOrder);
            WorkOrderAssignRequest workOrderAssignRequest = new WorkOrderAssignRequest();

            workOrderAssignRequest.setProcessLevel(workOrder.getProcessLevel());
            workOrderAssignRequest.setStepProcessUserId(workOrder.getStepProcessUserId());
            WorkOrderDetail detail = workOrderAssignRequest.process(workOrder);
            newlyWorkOrderMapper.updateById(workOrder);
            detail.setProcessUserId(callWorkOrder.getCreator());
            detail.setProcessRemark("上报");
            workOrderDetailMapper.insert(detail);

            callWorkOrder.setWorkOrderId(workOrder.getId());
            callWorkOrder.setSerialNo(workOrder.getSerialNo());
            callWorkOrderMapper.insert(callWorkOrder);

        } else {
            callWorkOrderMapper.updateById(callWorkOrder);
        }

        return callWorkOrder;
    }

    @Override
    public int delete(List<String> ids) {
        return callWorkOrderMapper.deleteBatchIds(ids);
    }

    @Override
    public Map getDetails(String workOrderId) {
        Map result = new HashMap();

        // 办理内容
        List<WorkOrderDetailDTO> workOrderDetails = workOrderDetailMapper.getDetails(workOrderId);
        result.put("processList", workOrderDetails);

        // 工单内容
        WorkOrderSendDTO workOrderSendDTO = new WorkOrderSendDTO();

        // TODO 办结内容
        WorkOrderConcludeDTO workOrderConcludeDTO = new WorkOrderConcludeDTO();
        result.put("conclude", workOrderConcludeDTO);

        result.put("sendDetail", workOrderSendDTO);
        for (WorkOrderDetailDTO workOrderDetailDTO : workOrderDetails) {
            if (WorkOrderStatus.ASSIGN.equals(workOrderDetailDTO.getType())) {
                workOrderSendDTO.setPerson(workOrderDetailDTO.getProcessUserName());
                workOrderSendDTO.setDepartment(workOrderDetailDTO.getProcessUserDepartment());
                workOrderSendDTO.setSendTime(workOrderDetailDTO.getProcessTime());
                WorkOrder workOrder = newlyWorkOrderMapper.selectById(workOrderId);
                if (workOrder != null) {
                    workOrderSendDTO.setLevel(workOrder.getLevel());
                    Integer workOrderLevel = workOrder.getProcessLevel();
                    if (workOrderLevel != null) {
                        workOrderSendDTO.setEndTime(new Date(workOrderSendDTO.getSendTime().getTime() + (workOrderLevel * 60 * 1000)));
                        workOrderSendDTO.setProcessDuration(workOrder.getProcessLevelLabel());
                        workOrderSendDTO.setCompleteTime(workOrder.getCompleteTime());
                    }
                }
            }
        }

        // 回访记录
        List<CallWorkOrderRevisit> revisitList = callWorkOrderRevisitMapper.getList(workOrderId);
        result.put("revisitList", revisitList);

        // 催办记录
        List<CallWorkOrderRemind> remindList = callWorkOrderRemindMapper.getList(workOrderId);
        result.put("remindList", remindList);

        // 详情
        CallWorkOrder detail = callWorkOrderMapper.getDetail(workOrderId);
        detail.setStatusName(WorkOrderStatus.valueOf(detail.getStatus()).getStageName());
        result.put("detail", detail);

        return result;
    }

    @Override
    public CallWorkOrderRevisit revisit(CallWorkOrderRevisit callWorkOrderRevisit) {
        callWorkOrderRevisit.setCreateTime(new Date());
        callWorkOrderRevisitMapper.insert(callWorkOrderRevisit);
        return callWorkOrderRevisit;
    }

    @Override
    public CallWorkOrderRemind remind(CallWorkOrderRemind callWorkOrderRemind) {
        callWorkOrderRemind.setCreateTime(new Date());
        callWorkOrderRemindMapper.insert(callWorkOrderRemind);
        return callWorkOrderRemind;
    }

    @Override
    public Object getWorkOrderStatus() {
        WorkOrderStatus[] values = WorkOrderStatus.values();
        List<Map> result = new ArrayList();
        Map resultMap = new HashMap();
        resultMap.put("lable", "全部");
        resultMap.put("value", "");
        result.add(resultMap);
        for (WorkOrderStatus workOrderStatus : values) {
            if (StringUtils.isBlank(workOrderStatus.getStageName())) {
                continue;
            }
            resultMap = new HashMap();
            resultMap.put("label", workOrderStatus.getDetailName());
            resultMap.put("value", workOrderStatus.name());
            result.add(resultMap);
        }
        return result;
    }


    @Override
    public List getMonthCallLogStatistics(String seatsId, String year, String tenantId) {
        String start = year + "-" + "01-01 00:00:00";
        String end = year + "-" + "12-31 24:59:59";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime;
        Date endTime;
        try {
            startTime = format.parse(start);
            endTime = format.parse(end);
        } catch (ParseException e) {
            e.printStackTrace();
            return new ArrayList();
        }

        List<Map> monthCallLogStatistics = callWorkOrderMapper.getMonthCallLogStatistics(seatsId, startTime, endTime, tenantId);
        String month;
        for (int i = 1; i <= 12; i++) {
            month = i + "";
            if (month.length() < 2) {
                month = "0" + month;
            }
            month = year + "-" + month;
            Map oneLog;
            if (monthCallLogStatistics.size() < i || !month.equals(monthCallLogStatistics.get(i - 1).get("month"))) {
                oneLog = new HashMap();
                oneLog.put("month", month);
                oneLog.put("count", 0);
                monthCallLogStatistics.add(i - 1, oneLog);
            }
            monthCallLogStatistics.get(i - 1).put("no", i);
        }

        return monthCallLogStatistics;
    }

    @Override
    public List getYearCallLogStatistics(String start, String end, String tenantId) {
        Date endTime = new Date(LocalDate.parse(end + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        Date startTime = new Date(LocalDate.parse(start + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toInstant().getEpochSecond());

        return callWorkOrderMapper.getYearCallLogStatistics(startTime, endTime, tenantId);

    }

    @Override
    public PageData getCallDaily(String seatsId, String startTime, String endTime, int page, int size, String tenantId) {
        // 先找查询日期内坐席的通话记录
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long start;
        Long end;
        try {
            start = simpleDateFormat.parse(startTime).getTime();
            end = simpleDateFormat.parse(endTime).getTime() + (24 * 3600 * 1000L) - 1;
        } catch (Exception e) {
            log.error("日期格式错误");
            return new PageData();
        }

        List<SeatsCallDTO> seatsCallList = callWorkOrderMapper.getSeatsCall(seatsId, start, end, tenantId);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        Map<String, Map<String, CallDailyDTO>> processMap = new HashMap();
        Map<String, CallDailyDTO> subMap;
        CallDailyDTO callDailyDTO;
        String day;
        for (SeatsCallDTO seatsCallDTO : seatsCallList) {
            subMap = processMap.get(seatsCallDTO.getSeatsId());
            if (subMap == null) {
                subMap = new HashMap<>();
                processMap.put(seatsCallDTO.getSeatsId(), subMap);
            }
            day = dateFormat.format(seatsCallDTO.getCallTime());
            callDailyDTO = subMap.get(day);
            if (callDailyDTO == null) {
                callDailyDTO = new CallDailyDTO(seatsCallDTO.getSeatsId(), seatsCallDTO.getSeatsName());
                subMap.put(day, callDailyDTO);
            }
            switch (seatsCallDTO.getDirection()) {
                case "0": // 呼入
                    callDailyDTO.setInCount(callDailyDTO.getInCount() + 1);
                    callDailyDTO.setInDuration(callDailyDTO.getInDuration() + (seatsCallDTO.getListenTime().getTime() - seatsCallDTO.getCallTime().getTime()) / 1000);
                    break;
                case "1": // 呼出
                    callDailyDTO.setOutCount(callDailyDTO.getOutCount() + 1);
                    callDailyDTO.setOutDuration(callDailyDTO.getOutDuration() + (seatsCallDTO.getListenTime().getTime() - seatsCallDTO.getCallTime().getTime()) / 1000);
                    break;
            }
            if (seatsCallDTO.getEndTime() != null) { // 通话次数
                callDailyDTO.setConversationCount(callDailyDTO.getConversationCount() + 1);
                callDailyDTO.setConversationDuration(callDailyDTO.getConversationDuration() + (seatsCallDTO.getEndTime().getTime() - seatsCallDTO.getListenTime().getTime()) / 1000);
                callDailyDTO.setAvgConversationDuration(callDailyDTO.getConversationDuration() / callDailyDTO.getConversationCount());
            }
            callDailyDTO.setDay(day);
        }
        LocalDate endDay = Instant.ofEpochSecond(end / 1000).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate startDay = Instant.ofEpochSecond(start / 1000).atZone(ZoneId.systemDefault()).toLocalDate();
        // 所有坐席
        List<Map<String, String>> seatsMapList = seatsUserMapper.selectSeatsIdList(seatsId, tenantId);
        Map<String, String> seatsMap = seatsMapList.stream().collect(Collectors.toMap(a -> a.get("seats_id"), a -> a.get("seats_name")));
        List<String> seatsIdList = Arrays.asList(seatsMap.keySet().toArray(new String[seatsMapList.size()]));

        while (endDay.isAfter(startDay)) {
            for (String userId : seatsIdList) {
                subMap = processMap.get(userId);
                if (subMap == null) {
                    subMap = new HashMap<>();
                    processMap.put(userId, subMap);
                }
                day = endDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                callDailyDTO = subMap.get(day);
                if (callDailyDTO == null) {
                    callDailyDTO = new CallDailyDTO(userId, seatsMap.get(userId));
                    callDailyDTO.setDay(day);
                    subMap.put(day, callDailyDTO);
                }
            }
            endDay = endDay.minusDays(1L);
        }
        // 获取列表
        List<CallDailyDTO> callDailyDTOS = new ArrayList<>();
        List<List<CallDailyDTO>> collect = processMap.values().stream().map(a -> a.values().stream().collect(Collectors.toList())).collect(Collectors.toList());
        for (List<CallDailyDTO> callDailyDTOS1 : collect) {
            callDailyDTOS.addAll(callDailyDTOS1);
        }
        int total = callDailyDTOS.size();
        callDailyDTOS = callDailyDTOS.stream().sorted(CallDailyDTO::compareTo).skip((page - 1) * size).limit(size).collect(Collectors.toList());

        PageData pageData = new PageData();
        pageData.setData(callDailyDTOS);
        pageData.setTotal(total);

        return pageData;
    }

    @Override
    public List getWorkMonth(String startTime, String endTime, String tenantId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long start;
        Long end;
        try {
            start = simpleDateFormat.parse(startTime).getTime();
            end = simpleDateFormat.parse(endTime).getTime() + (24 * 3600 * 1000L) - 1;
        } catch (Exception e) {
            log.error("日期格式错误");
            return new ArrayList();
        }

        Map<String, WorkMonthDTO> workMonthDTOMap = new HashMap<>();
        // 查找所有工单
        List<CallWorkOrder> callWorkOrderList = callWorkOrderMapper.getListByTimeByWorkOrderType(start, end, tenantId);
        WorkMonthDTO workMonthDTO;
        Map<String, WorkMonthSubDTO> subMap;
        WorkMonthSubDTO workMonthSubDTO;
        for (CallWorkOrder callWorkOrder : callWorkOrderList) {
            workMonthDTO = workMonthDTOMap.get(callWorkOrder.getType());
            if (workMonthDTO == null) {
                workMonthDTO = new WorkMonthDTO();
                workMonthDTO.setType(callWorkOrder.getTypeName());
                workMonthDTO.setSubTotal(0);
                workMonthDTO.setPercent(0);
                workMonthDTOMap.put(callWorkOrder.getType(), workMonthDTO);
            }
            subMap = workMonthDTO.getWorkMonthSubDTOMap();
            workMonthSubDTO = subMap.get(callWorkOrder.getTopicName());
            if (workMonthSubDTO == null) {
                workMonthSubDTO = new WorkMonthSubDTO();
                workMonthSubDTO.setNo(workMonthDTO.getSubList().size() + 1);
                workMonthSubDTO.setTopic(callWorkOrder.getTopicName());
                subMap.put(callWorkOrder.getTopicName(), workMonthSubDTO);
                workMonthDTO.getSubList().add(workMonthSubDTO);
            }

            // 事件数
            if ("市长热线".equals(callWorkOrder.getSourceName())) {
                workMonthSubDTO.setMayorNum(workMonthSubDTO.getMayorNum() + 1);
            } else {
                workMonthSubDTO.setCustomerNum(workMonthSubDTO.getCustomerNum() + 1);
            }
        }

        // 统计
        DecimalFormat decimalFormat = new DecimalFormat("0");
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
        List<WorkMonthDTO> workMonthDTOS = workMonthDTOMap.values().stream().map(a -> {
            a.setSubTotal(a.getSubList().stream().collect(Collectors.summingInt(b -> b.getMayorNum() + b.getCustomerNum())));
            a.setPercent(Integer.valueOf(decimalFormat.format(a.getSubTotal() * 100.0 / callWorkOrderList.size())));
            a.getWorkMonthSubDTOMap().clear();
            return a;
        }).collect(Collectors.toList());

        return workMonthDTOS;
    }

    @Override
    public Map getWorkArea(String startTime, String endTime, String tenantId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long start;
        Long end;
        try {
            start = simpleDateFormat.parse(startTime).getTime();
            end = simpleDateFormat.parse(endTime).getTime() + (24 * 3600 * 1000L) - 1;
        } catch (Exception e) {
            log.error("日期格式错误");
            return new HashMap();
        }
        List<CallWorkOrder> callWorkOrderList = callWorkOrderMapper.getListByTime(start, end, tenantId);
        Map<String, WorkAreaDTO> workAreaDTOMap = new HashMap<>();
        WorkAreaDTO workAreaDTO;
        WorkAreaTopicDTO workAreaTopicDTO;
        WorkAreaTopicAreaDTO workAreaTopicAreaDTO;
        for (CallWorkOrder callWorkOrder : callWorkOrderList) {
            workAreaDTO = workAreaDTOMap.get(callWorkOrder.getType());
            if (workAreaDTO == null) {
                workAreaDTO = new WorkAreaDTO();
                workAreaDTO.setType(callWorkOrder.getTypeName());
                workAreaDTOMap.put(callWorkOrder.getType(), workAreaDTO);
            }
            workAreaTopicDTO = workAreaDTO.getWorkAreaTopicMap().get(callWorkOrder.getTopic());
            if (workAreaTopicDTO == null) {
                workAreaTopicDTO = new WorkAreaTopicDTO();
                workAreaTopicDTO.setTopicName(callWorkOrder.getTopicName());
                workAreaDTO.getWorkAreaTopicMap().put(callWorkOrder.getTopic(), workAreaTopicDTO);
            }
            workAreaTopicAreaDTO = workAreaTopicDTO.getTopicAreaMap().get(callWorkOrder.getArea());
            if (workAreaTopicAreaDTO == null) {
                workAreaTopicAreaDTO = new WorkAreaTopicAreaDTO();
                workAreaTopicAreaDTO.setAreaName(callWorkOrder.getAreaName());
                workAreaTopicAreaDTO.setNum(0);
                workAreaTopicDTO.getTopicAreaMap().put(callWorkOrder.getArea(), workAreaTopicAreaDTO);
            }
            workAreaTopicAreaDTO.setNum(workAreaTopicAreaDTO.getNum() + 1);
        }
        // 获取所有区域名
        List<Map<String, String>> areaList = systemDictMapper.selectAreaList(tenantId);
        Map<String, String> areaMap = areaList.stream().collect(Collectors.toMap(a -> a.get("id"), a -> a.get("name")));
        List<String> areaIdList = Arrays.asList(areaMap.keySet().toArray(new String[areaList.size()]));

        List<WorkAreaDTO> workAreaDTOList = workAreaDTOMap.values().stream().collect(Collectors.toList());
        workAreaDTOList = workAreaDTOList.stream().map(a -> {
            a.setWorkAreaTopicList(a.getWorkAreaTopicMap().values().stream().collect(Collectors.toList()).stream().map(b -> {
                for (String areaId : areaIdList) {
                    if (b.getTopicAreaMap().get(areaId) == null) {
                        b.getTopicAreaMap().put(areaId, new WorkAreaTopicAreaDTO(areaMap.get(areaId)));
                    }
                }
                b.setTopicAreaList(b.getTopicAreaMap().values().stream().collect(Collectors.toList()));
                b.getTopicAreaMap().clear();
                return b;
            }).collect(Collectors.toList()));
            a.getWorkAreaTopicMap().clear();
            return a;
        }).collect(Collectors.toList());

        Map result = new HashMap();
        result.put("areaName", areaMap.values().stream().collect(Collectors.toList()));
        result.put("data", workAreaDTOList);

        return result;
    }

    @Override
    public List getSeatsMonth(String type, String year, String tenantId) {
        // 所有坐席
        List<Map<String, String>> seatsMapList = seatsUserMapper.selectSeatsIdList("", tenantId);
        Map<String, String> seatsMap = seatsMapList.stream().collect(Collectors.toMap(a -> a.get("seats_id"), a -> a.get("seats_name")));
        List<String> seatsIdList = Arrays.asList(seatsMap.keySet().toArray(new String[seatsMapList.size()]));
        // 今年所有工单
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        List<SeatsCallDTO> seatsCallDTOS;
        try {
            seatsCallDTOS = callWorkOrderMapper.getSeatsCall("", simpleDateFormat.parse(year).getTime(), simpleDateFormat.parse(Integer.valueOf(year) + 1 + "").getTime(), tenantId);
        } catch (ParseException e) {
            log.error("日期格式错误");
            return new ArrayList();
        }
        Map<String, SeatsMonthDTO> resultMap = new HashMap<>();
        for (String seatsId : seatsIdList) {
            resultMap.put(seatsId, new SeatsMonthDTO(seatsMap.get(seatsId)));
        }
        // 合计
        SeatsMonthDTO sumSetatsMonth = new SeatsMonthDTO("合计");
        simpleDateFormat.applyPattern("MM");
        int month;
        switch (type) {
            case "通话量":
                List<Integer> monthList;
                for (SeatsCallDTO seatsCallDTO : seatsCallDTOS) {
                    month = Integer.valueOf(simpleDateFormat.format(seatsCallDTO.getCallTime())) - 1;
                    monthList = resultMap.get(seatsCallDTO.getSeatsId()).getMonthList();
                    monthList.set(month, monthList.get(month) + 1);
                    sumSetatsMonth.getMonthList().set(month, sumSetatsMonth.getMonthList().get(month) + 1);
                }
                break;
            case "通话时长":
                for (SeatsCallDTO seatsCallDTO : seatsCallDTOS) {
                    if (seatsCallDTO.getListenTime() == null ||seatsCallDTO.getEndTime() == null) {
                        continue;
                    }
                    month = Integer.valueOf(simpleDateFormat.format(seatsCallDTO.getCallTime())) - 1;
                    monthList = resultMap.get(seatsCallDTO.getSeatsId()).getMonthList();
                    monthList.set(month, monthList.get(month) + (int) ((seatsCallDTO.getEndTime().getTime() - seatsCallDTO.getListenTime().getTime()) / 1000));
                    sumSetatsMonth.getMonthList().set(month, sumSetatsMonth.getMonthList().get(month) + (int) ((seatsCallDTO.getEndTime().getTime() - seatsCallDTO.getListenTime().getTime()) / 1000));
                }
                break;

            case "呼入":
                for (SeatsCallDTO seatsCallDTO : seatsCallDTOS) {
                    if (!"0".equals(seatsCallDTO.getDirection())) {
                        continue;
                    }
                    month = Integer.valueOf(simpleDateFormat.format(seatsCallDTO.getCallTime())) - 1;
                    monthList = resultMap.get(seatsCallDTO.getSeatsId()).getMonthList();
                    monthList.set(month, monthList.get(month) + 1);
                    sumSetatsMonth.getMonthList().set(month, sumSetatsMonth.getMonthList().get(month) + 1);
                }
                break;

            case "呼出":
                for (SeatsCallDTO seatsCallDTO : seatsCallDTOS) {
                    if (!"1".equals(seatsCallDTO.getDirection())) {
                        continue;
                    }
                    month = Integer.valueOf(simpleDateFormat.format(seatsCallDTO.getCallTime())) - 1;
                    monthList = resultMap.get(seatsCallDTO.getSeatsId()).getMonthList();
                    monthList.set(month, monthList.get(month) + 1);
                    sumSetatsMonth.getMonthList().set(month, sumSetatsMonth.getMonthList().get(month) + 1);
                }
                break;
            case "工单统计":
                for (SeatsCallDTO seatsCallDTO : seatsCallDTOS) {
                    month = Integer.valueOf(simpleDateFormat.format(seatsCallDTO.getCallTime())) - 1;
                    monthList = resultMap.get(seatsCallDTO.getSeatsId()).getMonthList();
                    monthList.set(month, monthList.get(month) + 1);
                    sumSetatsMonth.getMonthList().set(month, sumSetatsMonth.getMonthList().get(month) + 1);
                }
                break;
        }

        List<SeatsMonthDTO> seatsMonthDTOS = resultMap.values().stream().map(a -> {
            List<Integer> monthList = a.getMonthList();
            monthList.set(12, monthList.stream().skip(0).limit(12).collect(Collectors.summingInt(b -> b)));
            sumSetatsMonth.getMonthList().set(12, sumSetatsMonth.getMonthList().get(12) + monthList.get(12));
            return a;
        }).collect(Collectors.toList());
        seatsMonthDTOS.add(sumSetatsMonth);

        return seatsMonthDTOS;
    }

    @Override
    public List getSeatsCall(String startTime, String endTime, String tenantId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long start;
        Long end;
        try {
            start = simpleDateFormat.parse(startTime).getTime();
            end = simpleDateFormat.parse(endTime).getTime() + (24 * 3600 * 1000L) - 1;
        } catch (Exception e) {
            log.error("日期格式错误");
            return new ArrayList();
        }
        List<Map<String, String>> seatsMapList = seatsUserMapper.selectSeatsIdList("", tenantId);
        Map<String, String> seatsMap = seatsMapList.stream().collect(Collectors.toMap(a -> a.get("seats_id"), a -> a.get("seats_name")));
        List<String> seatsIdList = Arrays.asList(seatsMap.keySet().toArray(new String[seatsMapList.size()]));
        // 今年所有工单
        List<SeatsCallDTO> seatsCallDTOS = callWorkOrderMapper.getSeatsCall("", start, end, tenantId);
        Map<String, SeatsCallCountDTO> resultMap = new HashMap<>();
        for (String seatsId : seatsIdList) {
            resultMap.put(seatsId, new SeatsCallCountDTO(seatsMap.get(seatsId)));
        }

        DecimalFormat format = new DecimalFormat("#.##");
        for (SeatsCallDTO seatsCallDTO : seatsCallDTOS) {
            if (!"0".equals(seatsCallDTO.getDirection())) {
                continue;
            }
            SeatsCallCountDTO seatsCallCountDTO = resultMap.get(seatsCallDTO.getSeatsId());
            seatsCallCountDTO.setIn(seatsCallCountDTO.getIn() + 1);
            // 丢失数
            if (seatsCallDTO.getListenTime() == null) {
                seatsCallCountDTO.setMiss(seatsCallCountDTO.getMiss() + 1);
            }
            // 通话时长
            if (seatsCallDTO.getListenTime() != null && seatsCallDTO.getEndTime() != null) {
                seatsCallCountDTO.setDuration(seatsCallCountDTO.getDuration() + (int) (seatsCallDTO.getListenTime().getTime() - seatsCallDTO.getCallTime().getTime()) / 1000);
            }
            // 丢失率
            if (seatsCallCountDTO.getIn() != 0) {
                seatsCallCountDTO.setMissRate(format.format(seatsCallCountDTO.getMiss() * 100d / seatsCallCountDTO.getIn()) + "%");
            }
        }

        return resultMap.values().stream().collect(Collectors.toList());
    }

    @Override
    public PageData getIvr(String start, String end, Integer page, Integer size, String tenantId) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Map<String, IvrReportDTO> result = new LinkedHashMap<>();
        LocalDate startTime = LocalDate.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endTime = LocalDate.parse(end, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1L);
        List<CallIVRLog> callIVRLogList = callIVRLogMapper.getAllByTime(startTime.atStartOfDay(ZoneId.systemDefault()).toInstant().getEpochSecond(), endTime.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(), tenantId);
        String time;
        while (startTime.isBefore(endTime)) {
            time = startTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            result.put(time, new IvrReportDTO(time));
            startTime = startTime.plusDays(1L);
        }

        IvrReportDTO ivrReportDTO;
        for (CallIVRLog callIVRLog : callIVRLogList) {
            if (callIVRLog.getFlag() == null || !"0".equals(callIVRLog.getFlag())) {

                continue;
            }
            time = format.format(callIVRLog.getCreateTime());
            ivrReportDTO = result.get(time);
            if (ivrReportDTO == null) {
                continue;
            }
            ivrReportDTO.setIn(ivrReportDTO.getIn() + 1);
            switch (callIVRLog.getKeyInfo()) {
                case "0":
                    ivrReportDTO.setPerson(ivrReportDTO.getPerson() + 1);
                    break;

                case "1":
                    ivrReportDTO.setAuto(ivrReportDTO.getAuto() + 1);
                    break;

                default:
                    ivrReportDTO.setMiss(ivrReportDTO.getMiss() + 1);
            }
        }
        List<IvrReportDTO> ivrReportDTOList = result.values().stream().skip((page - 1) * size).limit(size).collect(Collectors.toList());
        PageData pageData = new PageData(result.values().size(), ivrReportDTOList);
        return pageData;
    }

    @Override
    public List getCallSource(String start, String end, String tenantId) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long startTime = null;
        Long endTime = null;
        try {
            if (start == null) {
                startTime = format.parse(start).getTime();
                endTime = format.parse(end).getTime();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<CallWorkOrder> callWorkOrderList = callWorkOrderMapper.getListByTime(startTime, endTime, tenantId);
        Map<String, CallSourceDTO> resultMap = new HashMap<>();
        // 获取所有呼叫来源类型
        QueryWrapper<SystemDict> queryWrapper = new QueryWrapper<>();

        CallSourceDTO callSourceDTO;
        for (CallWorkOrder callWorkOrder : callWorkOrderList) {
            callSourceDTO = resultMap.get(callWorkOrder.getSource());
            if (callSourceDTO == null) {
                callSourceDTO = new CallSourceDTO(callWorkOrder.getSource());
                resultMap.put(callWorkOrder.getSource(), callSourceDTO);
            }
            callSourceDTO.setNum(callSourceDTO.getNum() + 1);
        }

        return resultMap.values().stream().sorted(Comparator.comparing(CallSourceDTO::getNum).reversed()).collect(Collectors.toList());
    }

    @Override
    public PageData getServiceDetail(String type, String year, String start, String end, Integer page, Integer size, String tenantId) {
        Long startTime;
        Long endTime;
        SimpleDateFormat format = new SimpleDateFormat("yyyy");;
        DateTimeFormatter dateTimeFormatter;
        if ("0".equals(type)) {
            dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMM");
            // 是否传年
            if (StringUtils.isBlank(year)) {
                year = format.format(new Date());
            }
            year = year + "-01-01";
            LocalDate startDate = LocalDate.parse(year, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            endTime = startDate.plusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            format.applyPattern("yyyyMM");
        } else {
            dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            format.applyPattern("yyyy-MM-dd");
            try {
                startTime = format.parse(start).getTime();
                endTime = format.parse(end).getTime() + (24 * 60 * 60 * 1000);
            } catch (ParseException e) {
                return new PageData();
            }
            format.applyPattern("yyyyMMdd");
        }

        List<CallIVRLog> callIVRLogs = callIVRLogMapper.getCallAndIvrLog(startTime, endTime);
        LocalDate startDate = Instant.ofEpochMilli(startTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(endTime).atZone(ZoneId.systemDefault()).toLocalDate();

        Map<String, ServiceDetailDTO> resultMap = new LinkedHashMap<>();
        String time;
        while (startDate.isBefore(endDate)) {
            time = startDate.format(dateTimeFormatter);
            resultMap.put(time, new ServiceDetailDTO(time));
            startDate = startDate.plusDays(1L);
        }
        ServiceDetailDTO serviceDetailDTO;
        Map<String, List<Long>> queueWaitAvgMap = new HashMap<>();
        Map<String, List<Long>> personCallDurationAvgMap = new HashMap<>();
        Map<String, List<Long>> endDurationAvgMap = new HashMap<>();
        Map<String, List<Long>> callAnswerMap = new HashMap<>();
        Map<String, List<Long>> ringAvgMap = new HashMap<>();
        Map<String, Integer> callCenterSolveMap = new HashMap<>();
        Map<String, Map<String, Integer>> serviceLevelMap = new HashMap<>();
        Map callIdMap = new HashMap();
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        for (CallIVRLog callIVRLog : callIVRLogs) {
            time = format.format(callIVRLog.getCallTime());
            serviceDetailDTO = resultMap.get(time);
            // 总电话数
            if (callIdMap.get(callIVRLog.getCallId()) == null) {
                serviceDetailDTO.setAll(serviceDetailDTO.getAll() + 1);
                callIdMap.put(callIVRLog.getCallId(), callIVRLog.getCallId());
            }
            // 客户拨入丢失的数据
            if (callIVRLog.getListenTime() == null && callIVRLog.getFlag() == null) {
                serviceDetailDTO.setInMiss(serviceDetailDTO.getInMiss() + 1);
            }
            // 丢失率
            if (serviceDetailDTO.getAll() > 0) {
                serviceDetailDTO.setInMissRate(decimalFormat.format(serviceDetailDTO.getInMiss() * 100d / serviceDetailDTO.getAll()));
            }
            if ("0".equals(callIVRLog.getFlag())) {
                // 客户拨入连接
                if (callIVRLog.getListenTime() != null) {
                    serviceDetailDTO.setConnect(serviceDetailDTO.getConnect() + 1);
                }
                // 客户拨入队列
                if ("0".equals(callIVRLog.getKeyInfo())) {
                    serviceDetailDTO.setQueue(serviceDetailDTO.getQueue() + 1);
                }
                // 客户拨入队列放弃
                if ("0".equals(callIVRLog.getKeyInfo()) && callIVRLog.getListenTime() == null) {
                    serviceDetailDTO.setAbandon(serviceDetailDTO.getAbandon() + 1);
                }
                // IVR接入数
                serviceDetailDTO.setIvrIn(serviceDetailDTO.getIvrIn() + 1);
                // IVR丢失数
                if (callIVRLog.getListenTime() == null) {
                    serviceDetailDTO.setIvrMiss(serviceDetailDTO.getIvrMiss() + 1);
                } else { // IVR应答数
                    serviceDetailDTO.setIvrAnswer(serviceDetailDTO.getIvrAnswer() + 1);
                }
                //人工应答数
                if ("0".equals(callIVRLog.getKeyInfo()) && callIVRLog.getListenTime() != null) {
                    serviceDetailDTO.setPersonAnswer(serviceDetailDTO.getPersonAnswer() + 1);
                    // 队列平均等待时长
                    if (queueWaitAvgMap.get(time) == null) {
                        queueWaitAvgMap.put(time, new ArrayList<>());
                    }
                    queueWaitAvgMap.get(time).add(callIVRLog.getListenTime().getTime() - callIVRLog.getCreateTime().getTime());
                    serviceDetailDTO.setQueueWaitAvg(decimalFormat.format(queueWaitAvgMap.get(time).stream().collect(Collectors.averagingDouble(a -> a / 1000d))));
                    // 客服代表平均通话时间
                    if (callIVRLog.getEndTime() != null) {
                        if (personCallDurationAvgMap.get(time) == null) {
                            personCallDurationAvgMap.put(time, new ArrayList<>());
                        }
                        personCallDurationAvgMap.get(time).add(callIVRLog.getEndTime().getTime() - callIVRLog.getListenTime().getTime());
                        serviceDetailDTO.setPersonCallDurationAvg(decimalFormat.format(personCallDurationAvgMap.get(time).stream().collect(Collectors.averagingDouble(a -> a / 1000d))));
                        // 平均话后处理时长
                        if (endDurationAvgMap.get(time) == null) {
                            endDurationAvgMap.put(time, new ArrayList<>());
                        }
                        if (callIVRLog.getRealEndTime() != null) {
                            endDurationAvgMap.get(time).add(callIVRLog.getRealEndTime().getTime() - callIVRLog.getEndTime().getTime());
                            serviceDetailDTO.setEndDurationAvg(decimalFormat.format(endDurationAvgMap.get(time).stream().collect(Collectors.averagingDouble(a -> a / 1000d))));
                        }
                    }
                }
                if (serviceDetailDTO.getIvrIn() > 0) {
                    // IVR应答率
                    serviceDetailDTO.setIvrAnswerRate(decimalFormat.format(serviceDetailDTO.getIvrAnswer() * 100d / serviceDetailDTO.getIvrIn()));
                    // 人工应答率
                    serviceDetailDTO.setPersonAnswerRate(decimalFormat.format(serviceDetailDTO.getPersonAnswer() * 100d / serviceDetailDTO.getIvrIn()));
                    // 接通率
                    serviceDetailDTO.setConnectRate(decimalFormat.format((serviceDetailDTO.getIvrIn() - serviceDetailDTO.getPersonAnswer()) * 100d / serviceDetailDTO.getIvrIn()));
                }
                if (StringUtils.isNotBlank(callIVRLog.getFileUrl())) {
                    // 语言信箱使用次数
                    serviceDetailDTO.setVoiceUse(serviceDetailDTO.getVoiceUse() + 1);
                }
                // 呼叫中心直接解决率
                if (callIVRLog.getListenTime() != null) {
                    Integer centerSolve = callCenterSolveMap.get(time);
                    if (centerSolve == null) {
                        callCenterSolveMap.put(time, 0);
                    }
                    callCenterSolveMap.put(time, callCenterSolveMap.get(time) + 1);
                    serviceDetailDTO.setCallCenterSolveRate(decimalFormat.format(callCenterSolveMap.get(time) * 100d / serviceDetailDTO.getAll()));
                }
                // 平均呼应速度
                if (callIVRLog.getListenTime() != null) {
                    if (callAnswerMap.get(time) == null) {
                        callAnswerMap.put(time, new ArrayList<>());
                    }
                    callAnswerMap.get(time).add(callIVRLog.getListenTime().getTime() - callIVRLog.getCallTime().getTime());
                    serviceDetailDTO.setCallAnswerVelocityAvg(decimalFormat.format(callAnswerMap.get(time).stream().collect(Collectors.averagingDouble(a -> a / 1000d))));
                }
                // 电话占有率
                serviceDetailDTO.setCallOccupyRate("100");
                // 平均振铃时间
                if (callIVRLog.getRingTime() != null) {
                    if (ringAvgMap.get(time) == null) {
                        ringAvgMap.put(time, new ArrayList<>());
                    }
                    ringAvgMap.get(time).add(callIVRLog.getListenTime().getTime() - callIVRLog.getRingTime().getTime());
                    serviceDetailDTO.setRingTimeAvg(decimalFormat.format(ringAvgMap.get(time).stream().collect(Collectors.averagingDouble(a -> a / 1000d))));
                }
                // 服务水平
                if (StringUtils.isNotBlank(callIVRLog.getEvaluate())) {
                    if (serviceLevelMap.get(time) == null) {
                        serviceLevelMap.put(time, new HashMap<>());
                        serviceLevelMap.get(time).put("1", 0);// 满意
                        serviceLevelMap.get(time).put("2", 0);// 不满意
                    }
                    if ("1".equals(callIVRLog.getEvaluate())) {
                        serviceLevelMap.get(time).put("1", serviceLevelMap.get(time).get("1") + 1);
                    } else {
                        serviceLevelMap.get(time).put("2", serviceLevelMap.get(time).get("2") + 1);
                    }
                    serviceDetailDTO.setServiceLevel(serviceLevelMap.get(time).get("1") + "/" + (serviceLevelMap.get(time).get("1") + serviceLevelMap.get(time).get("2")));
                }
            }
        }
        List<ServiceDetailDTO> serviceDetailDTOList = resultMap.values().stream().collect(Collectors.toList());
        PageData pageData = new PageData();
        pageData.setTotal(serviceDetailDTOList.size());
        pageData.setData(serviceDetailDTOList.stream().skip((page - 1) * size).limit(size).collect(Collectors.toList()));

        return pageData;
    }

    @Override
    public Map getHotLine(String type, String time, String tenantId) {
        String tempYear = time;
        // 标题
        QueryWrapper<SystemWorkOrderType> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("pid");
        queryWrapper.eq("is_del", "0");
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.orderByAsc("create_time");
        List<SystemWorkOrderType> workOrderTypeList = systemWorkOrderTypeMapper.selectList(queryWrapper);
        Map<String, String> workOrderTypeMap = workOrderTypeList.stream().collect(Collectors.toMap(SystemWorkOrderType::getId, SystemWorkOrderType::getName));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long start;
        Long end;
        Map<String, Object> subResult;
        Map<String, Map<String, Object>> timeTypeMap = new LinkedHashMap<>();
        List resultData = new ArrayList();
        String[] headerIdArr = {};
        String[] headerNameArr = {};
        Map<String, Object> allTotal = new LinkedHashMap<>();
        allTotal.put("time", "合计");
        switch (type) {
            case "year":
                headerIdArr = new String[]{"time", "subTotal", "comparePre"};
                headerNameArr = new String[]{"年", "小计", "与上年相比"};
                subResult = new LinkedHashMap<>();
                subResult.put("time", time);
                HotLineValueMapInit(workOrderTypeMap, subResult);
                HotLineValueMapInit(workOrderTypeMap, allTotal);
                start = LocalDate.parse(time + "-01-01").atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = LocalDate.parse(time + "-12-31").plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                // 工单
                List<CallWorkOrder> callWorkOrders = callWorkOrderMapper.getListByTime(start, end, tenantId);
                for (CallWorkOrder callWorkOrder : callWorkOrders) {
                    if (subResult.get(callWorkOrder.getType()) == null) {
                        subResult.put(callWorkOrder.getType(), 0);
                        allTotal.put(callWorkOrder.getType(), 0);
                    }
                    subResult.put(callWorkOrder.getType(), (Integer) subResult.get(callWorkOrder.getType()) + 1);
                    allTotal.put(callWorkOrder.getType(), (Integer) allTotal.get(callWorkOrder.getType()) + 1);
                }
                resultData.add(subResult);

                break;
            case "halfYear":
                headerIdArr = new String[]{"time", "subTotal", "comparePre", "compareSameTime", "waterGageCompareSameTime"};
                headerNameArr = new String[]{"半年", "小计", "与上次半年相比", "与同期相比", "水压与同期相比"};
                String[] timeArr = {"上半年", "下半年"};
                String[] startArr = {"-01-01", "-07-01"};
                String[] endArr = {"-06-30", "-12-31"};
                HotLineValueMapInit(workOrderTypeMap, allTotal);
                // 分开查询
                for (int i = 0; i < timeArr.length; i++) {
                    subResult = new LinkedHashMap<>();
                    subResult.put("time", time + timeArr[i]);
                    HotLineValueMapInit(workOrderTypeMap, subResult);
                    start = LocalDate.parse(time + startArr[i]).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = LocalDate.parse(time + endArr[i]).plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    callWorkOrders = callWorkOrderMapper.getListByTime(start, end, tenantId);
                    for (CallWorkOrder callWorkOrder : callWorkOrders) {
                        if (subResult.get(callWorkOrder.getType()) == null) {
                            subResult.put(callWorkOrder.getType(), 0);
                        }
                        if (allTotal.get(callWorkOrder.getType()) == null) {
                            allTotal.put(callWorkOrder.getType(), 0);
                        }
                        subResult.put(callWorkOrder.getType(), (Integer) subResult.get(callWorkOrder.getType()) + 1);
                        allTotal.put(callWorkOrder.getType(), (Integer) allTotal.get(callWorkOrder.getType()) + 1);
                    }
                    resultData.add(subResult);
                }
                break;
            case "quarter":
                headerIdArr = new String[]{"time", "subTotal", "comparePre", "compareSameTime", "waterGageCompareSameTime"};
                headerNameArr = new String[]{"季度", "小计", "与上季度相比", "与同期相比", "水压与同期相比"};
                timeArr = new String[]{"第一季度", "第二季度", "第三季度", "第四季度"};
                startArr = new String[]{"-01-01", "-04-01", "-07-01", "-10-01"};
                endArr = new String[]{"-03-31", "-06-30", "-09-30", "-12-31"};
                HotLineValueMapInit(workOrderTypeMap, allTotal);
                // 分开查询
                for (int i = 0; i < timeArr.length; i++) {
                    subResult = new LinkedHashMap<>();
                    subResult.put("time", time + timeArr[i]);
                    HotLineValueMapInit(workOrderTypeMap, subResult);
                    start = LocalDate.parse(time + startArr[i]).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = LocalDate.parse(time + endArr[i]).plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    callWorkOrders = callWorkOrderMapper.getListByTime(start, end, tenantId);
                    for (CallWorkOrder callWorkOrder : callWorkOrders) {
                        if (subResult.get(callWorkOrder.getType()) == null) {
                            subResult.put(callWorkOrder.getType(), 0);
                        }
                        if (allTotal.get(callWorkOrder.getType()) == null) {
                            allTotal.put(callWorkOrder.getType(), 0);
                        }
                        subResult.put(callWorkOrder.getType(), (Integer) subResult.get(callWorkOrder.getType()) + 1);
                        allTotal.put(callWorkOrder.getType(), (Integer) allTotal.get(callWorkOrder.getType()) + 1);
                    }
                    resultData.add(subResult);
                }
                break;
            case "month":
                headerIdArr = new String[]{"time", "subTotal", "comparePre", "compareSameTime", "waterGageCompareSameTime"};
                headerNameArr = new String[]{"月", "小计", "与上月相比", "与同期相比", "水压与同期相比"};
                LocalDate startDate = LocalDate.parse(time + "-01-01");
                LocalDate endDate = LocalDate.parse(time + "-12-31").plusDays(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                simpleDateFormat.applyPattern("MM");
                dateTimeFormatter = DateTimeFormatter.ofPattern("MM");
                while (startDate.isBefore(endDate)) { // 补齐
                    time = dateTimeFormatter.format(startDate);
                    subResult = timeTypeMap.get(time);
                    if (subResult == null) {
                        subResult = new LinkedHashMap<>();
                        subResult.put("time", time);
                        HotLineValueMapInit(workOrderTypeMap, subResult);
                        timeTypeMap.put(time, subResult);
                    }
                    startDate = startDate.plusMonths(1L);
                }
                // 工单
                HotLineValueMapInit(workOrderTypeMap, allTotal);
                callWorkOrders = callWorkOrderMapper.getListByTime(start, end, tenantId);
                for (CallWorkOrder callWorkOrder : callWorkOrders) {
                    time = simpleDateFormat.format(callWorkOrder.getCreateTime());
                    subResult = timeTypeMap.get(time);
                    if (subResult == null) {
                        subResult = new LinkedHashMap<>();
                        subResult.put("time", time);
                        HotLineValueMapInit(workOrderTypeMap, subResult);
                        timeTypeMap.put(time, subResult);
                    }
                    if (subResult.get(callWorkOrder.getType()) == null) {
                        subResult.put(callWorkOrder.getType(), 0);
                    }
                    if(allTotal.get(callWorkOrder.getType()) == null) {
                        allTotal.put(callWorkOrder.getType(), 0);
                    }
                    subResult.put(callWorkOrder.getType(), (Integer) subResult.get(callWorkOrder.getType()) + 1);
                    allTotal.put(callWorkOrder.getType(), (Integer) allTotal.get(callWorkOrder.getType()) + 1);
                }
                resultData = timeTypeMap.values().stream().collect(Collectors.toList());
                break;
            case "week":
                headerIdArr = new String[]{"time", "subTotal", "comparePre", "compareSameTime", "waterGageCompareSameTime"};
                headerNameArr = new String[]{"周", "小计", "与上周相比", "与同期相比", "水压与同期相比"};
                startDate = LocalDate.parse(time + "-01");
                endDate = LocalDate.parse(time + "-01").plusMonths(1L);
                int week = 0;
                String[] weekArr = {"第一周", "第二周", "第三周", "第四周", "第五周", "第六周", "第七周", "第八周", "第九周", "第十周"};
                HotLineValueMapInit(workOrderTypeMap, allTotal);
                while (startDate.isBefore(endDate)) {
                    subResult = new LinkedHashMap<>();
                    subResult.put("time", weekArr[week]);
                    HotLineValueMapInit(workOrderTypeMap, subResult);
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = startDate.plusWeeks(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    callWorkOrders = callWorkOrderMapper.getListByTime(start, end, tenantId);
                    for (CallWorkOrder callWorkOrder : callWorkOrders) {
                        if (subResult.get(callWorkOrder.getType()) == null) {
                            subResult.put(callWorkOrder.getType(), 0);
                        }
                        if(allTotal.get(callWorkOrder.getType()) == null) {
                            allTotal.put(callWorkOrder.getType(), 0);
                        }
                        subResult.put(callWorkOrder.getType(), (Integer) subResult.get(callWorkOrder.getType()) + 1);
                        allTotal.put(callWorkOrder.getType(), (Integer) allTotal.get(callWorkOrder.getType()) + 1);
                    }
                    resultData.add(subResult);
                    week++;
                    startDate = startDate.plusWeeks(1L);
                }
                break;

            case "day":
                headerIdArr = new String[]{"time", "subTotal", "comparePre", "compareSameTime", "waterGageCompareSameTime"};
                headerNameArr = new String[]{"天", "小计", "与上日相比", "与同期相比", "水压与同期相比"};
                startDate = LocalDate.parse(time + "-01");
                endDate = LocalDate.parse(time + "-01").plusMonths(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                simpleDateFormat.applyPattern("dd");
                dateTimeFormatter = DateTimeFormatter.ofPattern("dd");
                HotLineValueMapInit(workOrderTypeMap, allTotal);
                while (startDate.isBefore(endDate)) { // 补齐
                    time = dateTimeFormatter.format(startDate);
                    subResult = timeTypeMap.get(time);
                    if (subResult == null) {
                        subResult = new LinkedHashMap<>();
                        subResult.put("time", time);
                        HotLineValueMapInit(workOrderTypeMap, subResult);
                        timeTypeMap.put(time, subResult);
                    }
                    startDate = startDate.plusDays(1L);
                }
                // 工单
                callWorkOrders = callWorkOrderMapper.getListByTime(start, end, tenantId);
                for (CallWorkOrder callWorkOrder : callWorkOrders) {
                    time = simpleDateFormat.format(callWorkOrder.getCreateTime());
                    subResult = timeTypeMap.get(time);
                    if (subResult == null) {
                        subResult = new LinkedHashMap<>();
                        subResult.put("time", time);
                        HotLineValueMapInit(workOrderTypeMap, subResult);
                        timeTypeMap.put(time, subResult);
                    }
                    if (subResult.get(callWorkOrder.getType()) == null) {
                        subResult.put(callWorkOrder.getType(), 0);
                    }
                    if(allTotal.get(callWorkOrder.getType()) == null) {
                        allTotal.put(callWorkOrder.getType(), 0);
                    }
                    subResult.put(callWorkOrder.getType(), (Integer) subResult.get(callWorkOrder.getType()) + 1);
                    allTotal.put(callWorkOrder.getType(), (Integer) allTotal.get(callWorkOrder.getType()) + 1);
                }
                resultData = timeTypeMap.values().stream().collect(Collectors.toList());
                break;
        }

        // 与同期上次相比
        this.comparePre(resultData, type, tempYear);

        // 合计的小计
        allTotal.put("subTotal", allTotal.values().stream().skip(1).collect(Collectors.summingInt(a -> (Integer) a)));
        // 其他数据

        allTotal.put("comparePre", "");
        allTotal.put("compareSameTime", "");
        allTotal.put("waterGageCompareSameTime", "");
        resultData.add(allTotal);

        Map result = new HashMap();
        List resultHeader = new ArrayList();
        Map map = new HashMap();
        map.put("id", "time");
        map.put("name", headerNameArr[0]);
        resultHeader.add(map);
        for (SystemWorkOrderType workOrderTypeDTO : workOrderTypeList) {
            map = new HashMap();
            map.put("id",workOrderTypeDTO.getId());
            map.put("name", workOrderTypeDTO.getName());
            resultHeader.add(map);
        }

        for (int i = 1; i < headerIdArr.length; i++) {
            map = new HashMap();
            map.put("id", headerIdArr[i]);
            map.put("name", headerNameArr[i]);
            resultHeader.add(map);
        }
        result.put("header", resultHeader);
        result.put("data", resultData);

        return result;
    }

    private void HotLineValueMapInit(Map<String, String> workOrderTypeMap, Map result) {
        workOrderTypeMap.keySet().stream().forEach(a -> result.put(a, 0));
    }

    private void comparePre(List resultData, String type, String time) {
        switch (type) {
            case "year": //与上年相比
                Long start = LocalDate.parse(time + "-01-01").minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                Long end = LocalDate.parse(time + "-12-31").minusYears(1L).plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                // 工单总数
                int sum = callWorkOrderMapper.getListByTimeCount(start, end);
                List valueList = (List) ((Map) resultData.get(0)).values().stream().collect(Collectors.toList());
                int valueSum = 0;
                for (int i = 1; i < valueList.size(); i++) {
                    valueSum += (Integer) valueList.get(i);
                }
                int result = valueSum - sum;
                String symbol = getSymbol(result);
                ((Map) resultData.get(0)).put("subTotal", valueSum);
                ((Map) resultData.get(0)).put("comparePre", symbol + Math.abs(result));
                break;

            case "halfYear":
                String[] startArr = {"-01-01", "-07-01"};
                String[] endArr = {"-06-30", "-12-31"};

                // 先查同期
                List<Integer> oldList = new ArrayList<>();
                List<Integer> nowList = new ArrayList();
                for (int i = 0; i < startArr.length; i++) {
                    start = LocalDate.parse(time + startArr[i]).minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = LocalDate.parse(time + endArr[i]).plusDays(1L).minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    sum = callWorkOrderMapper.getListByTimeCount(start, end);
                    valueList = (List) ((Map) resultData.get(i)).values().stream().collect(Collectors.toList());
                    valueSum = 0;
                    for (int j = 1; j < valueList.size(); j++) {
                        valueSum += (Integer) valueList.get(j);
                    }
                    result = valueSum - sum;
                    symbol = getSymbol(result);
                    ((Map) resultData.get(i)).put("subTotal", valueSum);
                    ((Map) resultData.get(i)).put("compareSameTime", symbol + Math.abs(result));
                    oldList.add(sum);
                    nowList.add(valueSum);
                }
                // 上年第二条是今年第一条上次半年
                sum = nowList.get(0) - oldList.get(1);
                symbol = getSymbol(sum);
                ((Map) resultData.get(0)).put("comparePre", symbol + Math.abs(sum));
                // 今年第一条是今年第二条上次半年
                sum = nowList.get(1) - nowList.get(0);
                symbol = getSymbol(sum);
                ((Map) resultData.get(1)).put("comparePre", symbol + Math.abs(sum));
                break;

            case "quarter":
                startArr = new String[]{"-01-01", "-04-01", "-07-01", "-10-01"};
                endArr = new String[]{"-03-31", "-06-30", "-09-30", "-12-31"};
                // 先查同期
                oldList = new ArrayList<>();
                nowList = new ArrayList();
                for (int i = 0; i < startArr.length; i++) {
                    start = LocalDate.parse(time + startArr[i]).minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = LocalDate.parse(time + endArr[i]).plusDays(1L).minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    sum = callWorkOrderMapper.getListByTimeCount(start, end);
                    valueList = (List) ((Map) resultData.get(i)).values().stream().collect(Collectors.toList());
                    valueSum = 0;
                    for (int j = 1; j < valueList.size(); j++) {
                        valueSum += (Integer) valueList.get(j);
                    }
                    result = valueSum - sum;
                    symbol = getSymbol(result);
                    ((Map) resultData.get(i)).put("subTotal", valueSum);
                    ((Map) resultData.get(i)).put("compareSameTime", symbol + Math.abs(result));
                    oldList.add(sum);
                    nowList.add(valueSum);
                }
                // 上年第四季度是今年第一季度
                sum = nowList.get(0) - oldList.get(3);
                symbol = getSymbol(sum);
                ((Map) resultData.get(0)).put("comparePre", symbol + Math.abs(sum));
                for (int j = 1; j < 4; j++) {
                    // 今年第一季度是今年第二季度
                    // 今年第二季度是今年第三季度
                    // 今年第三季度是今年第四季度
                    sum = nowList.get(j) - nowList.get(j - 1);
                    symbol = getSymbol(sum);
                    ((Map) resultData.get(j)).put("comparePre", symbol + Math.abs(sum));
                }
                break;
            case "month":
                LocalDate startDate = LocalDate.parse(time + "-01-01").minusYears(1L);
                LocalDate endDate = LocalDate.parse(time + "-12-31").plusDays(1L).minusYears(1L);

                oldList = new ArrayList<>();
                nowList = new ArrayList();
                int i = 0;
                while (startDate.isBefore(endDate)) { // 补齐
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = startDate.plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    sum = callWorkOrderMapper.getListByTimeCount(start, end);
                    valueList = (List) ((Map) resultData.get(i)).values().stream().collect(Collectors.toList());
                    valueSum = 0;
                    for (int j = 1; j < valueList.size(); j++) {
                        valueSum += (Integer) valueList.get(j);
                    }
                    result = valueSum - sum;
                    symbol = getSymbol(result);
                    ((Map) resultData.get(i)).put("subTotal", valueSum);
                    ((Map) resultData.get(i)).put("compareSameTime", symbol + Math.abs(result));
                    oldList.add(sum);
                    nowList.add(valueSum);
                    i++;
                    startDate = startDate.plusMonths(1L);
                }

                // 上年第12月是今年第1月
                sum = nowList.get(0) - oldList.get(11);
                symbol = getSymbol(sum);
                ((Map) resultData.get(0)).put("comparePre", symbol + Math.abs(sum));
                for (int j = 1; j < 12; j++) {
                    sum = nowList.get(j) - nowList.get(j - 1);
                    symbol = getSymbol(sum);
                    ((Map) resultData.get(j)).put("comparePre", symbol + Math.abs(sum));
                }
                break;
            case "week":
                // 上个月
                startDate = LocalDate.parse(time + "-01").minusMonths(1L);
                nowList = new ArrayList<>();
                oldList = new ArrayList<>();
                i = 0;
                while (i < resultData.size()) {
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = startDate.plusWeeks(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    sum = callWorkOrderMapper.getListByTimeCount(start, end);
                    valueList = (List) ((Map) resultData.get(i)).values().stream().collect(Collectors.toList());
                    valueSum = 0;
                    for (int j = 1; j < valueList.size(); j++) {
                        valueSum += (Integer) valueList.get(j);
                    }
                    ((Map) resultData.get(i)).put("subTotal", valueSum);
                    result = valueSum - sum;
                    symbol = getSymbol(result);
                    ((Map) resultData.get(i)).put("compareSameTime", symbol + Math.abs(result));
                    oldList.add(sum);
                    nowList.add(valueSum);
                    i++;
                    startDate = startDate.plusWeeks(1L);
                }

                // 上个月最后一周是这个月第一周
                sum = nowList.get(0) - oldList.get(i - 1);
                symbol = getSymbol(sum);
                ((Map) resultData.get(0)).put("comparePre", symbol + Math.abs(sum));
                for (int j = 1; j < i; j++) {
                    sum = nowList.get(j) - nowList.get(j - 1);
                    symbol = getSymbol(sum);
                    ((Map) resultData.get(j)).put("comparePre", symbol + Math.abs(sum));
                }
                break;

            case "day":
                // 上个月
                startDate = LocalDate.parse(time + "-01").minusMonths(1L);
                nowList = new ArrayList<>();
                oldList = new ArrayList<>();
                i = 0;
                while (i < resultData.size()) {
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = startDate.plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    // 工单
                    sum = callWorkOrderMapper.getListByTimeCount(start, end);
                    valueList = (List) ((Map) resultData.get(i)).values().stream().collect(Collectors.toList());
                    valueSum = 0;
                    for (int j = 1; j < valueList.size(); j++) {
                        valueSum += (Integer) valueList.get(j);
                    }
                    ((Map) resultData.get(i)).put("subTotal", valueSum);
                    result = valueSum - sum;
                    symbol = getSymbol(result);
                    ((Map) resultData.get(i)).put("compareSameTime", symbol + Math.abs(result));
                    oldList.add(sum);
                    nowList.add(valueSum);
                    i++;
                    startDate = startDate.plusDays(1L);
                }

                // 上个月最后一天是这个月第一天
                sum = nowList.get(0) - oldList.get(i - 1);
                symbol = getSymbol(sum);
                ((Map) resultData.get(0)).put("comparePre", symbol + Math.abs(sum));
                for (int j = 1; j < i; j++) {
                    sum = nowList.get(j) - nowList.get(j - 1);
                    symbol = getSymbol(sum);
                    ((Map) resultData.get(j)).put("comparePre", symbol + Math.abs(sum));
                }
                break;
        }
    }

    private String getSymbol(int result) {
        String symbol = "";
        if (result > 0) {
            symbol = "↑";
        }
        if (result < 0) {
            symbol = "↓";
        }
        return symbol;
    }
}