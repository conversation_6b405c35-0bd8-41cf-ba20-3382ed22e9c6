package org.thingsboard.server.service.statistics;

import com.googlecode.aviator.AviatorEvaluator;
import com.influxdb.query.FluxTable;
import com.jayway.jsonpath.JsonPath;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.rule.engine.api.TbContext;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.DefaultKeyValue;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.BasicTsKvEntry;
import org.thingsboard.server.common.data.kv.StringDataEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.tsdb.DataPoint;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.utils.StringUtils;
import org.thingsboard.server.common.data.virtual.VirtualUtils;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.dataSource.DataSourceService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.origin.OriginDataService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.RestUtil;

import java.math.BigDecimal;
import java.util.*;

import static org.thingsboard.server.common.data.DataConstants.*;
import static org.thingsboard.server.common.data.utils.DateUtils.MINUTE;
import static org.thingsboard.server.common.data.utils.DateUtils.getStatisticsEnd;
import static org.thingsboard.server.common.data.virtual.VirtualUtils.isMathSymbol;

/**
 * <AUTHOR>
 * @date 2020/4/15 11:33
 */
@Service
@Slf4j
public class StatisticsProcessService {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private OriginDataService originDataService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserService userService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private TimeseriesService timeseriesService;

    @Autowired
    private InfluxService influxService;


    /**
     * 平台级常用统计量获取
     *
     * @param dataSourceEntity 数据源
     */
    public void statisticsRootPreparation(DataSourceEntity dataSourceEntity, boolean isOrigin) {
        if (isOrigin) {
            TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getPreparation() + DataConstants.ORGIN);
            TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(dataSourceEntity.getPreparation(), tsKvEntry.getValueAsString()));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), dataSourceEntity.getId(), dataSourceEntity.getPreparation(), tsKvEntry.getValueAsString()));
        } else {
            String value = null;
            switch (dataSourceEntity.getPreparation()) {
                case DataConstants.TENANT_NUMBERS: {
                    value = (String.valueOf(tenantService.findAll().size()));
                    log.info("保存平台下企业数量" + value);
                    break;
                }
                case DataConstants.PROJECT_NUMBER: {
                    value = (String.valueOf(projectService.findAllCounts()));
                    log.info("保存平台下项目数量" + value);
                    break;
                }
                case DataConstants.GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllGateway().size()));
                    log.info("保存平台下主机数量" + value);
                    break;
                }
                case DataConstants.USER_NUMBER: {
                    value = (String.valueOf(userService.getAllUserCount()));
                    log.info("保存平台下用户数量" + value);
                    break;
                }
                case DataConstants.DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllDevice()));
                    log.info("保存平台下从机数量" + value);
                    break;
                }
                case DataConstants.DIRECT_NUMBER: {
                    value = (String.valueOf(deviceService.findAllModbusDevice()));
                    log.info("保存平台下直连从机数量" + value);
                    break;
                }
                case DataConstants.PENETRATE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllDtuDevice()));
                    log.info("保存平台下透传从机数量" + value);
                    break;
                }
                case DataConstants.ONLINE_DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOnlineDevice()));
                    log.info("保存平台下在线从机数量" + value);
                    break;
                }
                case DataConstants.ONLINE_GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOnlineGateway()));
                    log.info("保存平台下在线主机数量" + value);
                    break;
                }
                case DataConstants.OFFLINE_GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOfflineGateway()));
                    log.info("保存平台下离线主机数量" + value);
                    break;
                }
                case DataConstants.OFFLINE_DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOffLineDevice()));
                    log.info("保存平台下离线从机数量" + value);
                    break;
                }
                case DataConstants.ALARM_DEVICE_NUMBER: {
                    value = (String.valueOf(alarmService.getUnClearAlarm()));
                    log.info("保存平台下报警数量" + value);
                    break;
                }
                case ALL_ALARM_NUMBER: {
                    value = (String.valueOf(alarmService.getAllAlarmByRoot()));
                    log.info("保存平台下报警总数" + value);
                    break;
                }
                default: {
                    break;
                }

            }
            TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(dataSourceEntity.getPreparation() + DataConstants.ORGIN, value));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), dataSourceEntity.getId(), dataSourceEntity.getPreparation() + DataConstants.ORGIN, value));
        }
    }


    /**
     * 项目级常用统计量获取
     *
     * @param dataSourceEntity 数据源
     */
    public void statisticsProjectPreparation(DataSourceEntity dataSourceEntity, String projectId, boolean isOrigin) {
        if (isOrigin) {
            TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getPreparation() + DataConstants.ORGIN);
            TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(dataSourceEntity.getPreparation(), tsKvEntry.getValueAsString()));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), dataSourceEntity.getId(), dataSourceEntity.getPreparation(), tsKvEntry.getValueAsString()));
        } else {
            String value = null;
            switch (dataSourceEntity.getPreparation()) {
                case DataConstants.GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllGatewayByProjectId(projectId).size()));
                    log.info("保存项目下主机数量" + value);
                    break;
                }
                case DataConstants.DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllDeviceByProjectId(projectId)));
                    log.info("保存项目下从机数量" + value);
                    break;
                }
                case DataConstants.DIRECT_NUMBER: {
                    value = (String.valueOf(deviceService.findAllModbusDeviceByProjectId(projectId)));
                    log.info("保存项目下直连从机数量" + value);
                    break;
                }
                case DataConstants.PENETRATE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllDtuDeviceByProjectId(projectId)));
                    log.info("保存项目下透传从机数量" + value);
                    break;
                }
                case DataConstants.ONLINE_DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOnlineDeviceByProjectId(projectId)));
                    log.info("保存项目下在线从机数量" + value);
                    break;
                }
                case DataConstants.OFFLINE_DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOffLineDeviceByProjectId(projectId)));
                    log.info("保存项目下离线从机数量" + value);
                    break;
                }
                case DataConstants.ALARM_DEVICE_NUMBER: {
                    value = (String.valueOf(alarmService.getUnClearAlarmByProjectId(projectId)));
                    log.info("保存项目下报警数量" + value);
                    break;
                }
                case DataConstants.ONLINE_GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOnlineGatewayByProject(projectId)));
                    log.info("保存项目下在线主机数量" + value);
                    break;
                }
                case DataConstants.OFFLINE_GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOfflineGatewayByProject(projectId)));
                    log.info("保存项目下离线主机数量" + value);
                    break;
                }
                case ALL_ALARM_NUMBER: {
                    value = (String.valueOf(alarmService.getAllAlarmByProject(projectId)));
                    log.info("保存项目下报警总数" + value);
                    break;
                }
                default: {
                    break;
                }

            }
            TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(dataSourceEntity.getPreparation() + DataConstants.ORGIN, value));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), dataSourceEntity.getId(), dataSourceEntity.getPreparation() + DataConstants.ORGIN, value));
        }
    }

    /**
     * 企业级常用统计量获取
     *
     * @param dataSourceEntity 数据源
     */
    public void statisticsTenantPreparation(DataSourceEntity dataSourceEntity, String tenantId, boolean isOrigin) {
        if (isOrigin) {
            TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getPreparation() + DataConstants.ORGIN);
            TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(dataSourceEntity.getPreparation(), tsKvEntry.getValueAsString()));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), dataSourceEntity.getId(), dataSourceEntity.getPreparation(), tsKvEntry.getValueAsString()));
        } else {
            String value = null;
            switch (dataSourceEntity.getPreparation()) {
                case DataConstants.PROJECT_NUMBER: {
                    value = (String.valueOf(projectService.countsByTenant(tenantId)));
                    log.info("保存企业下项目数量" + value);
                    break;
                }
                case DataConstants.GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllGatewayByTenantId(tenantId).size()));
                    log.info("保存企业下主机数量" + value);
                    break;
                }
                case DataConstants.USER_NUMBER: {
                    value = (String.valueOf(userService.findUserByTenant(new TenantId(UUIDConverter.fromString(tenantId))).size()));
                    log.info("保存企业下用户数量" + value);
                    break;
                }
                case DataConstants.DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllDeviceByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下从机数量" + value);
                    break;
                }
                case DataConstants.DIRECT_NUMBER: {
                    value = (String.valueOf(deviceService.findAllModbusDeviceByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下直连从机数量" + value);
                    break;
                }
                case DataConstants.PENETRATE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllDtuDeviceByTenantId(tenantId)));
                    log.info("保存企业下透传从机数量" + value);
                    break;
                }
                case DataConstants.ONLINE_DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOnlineDeviceByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下在线从机数量" + value);
                    break;
                }
                case DataConstants.OFFLINE_DEVICE_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOffLineDeviceByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下离线从机数量" + value);
                    break;
                }
                case DataConstants.ALARM_DEVICE_NUMBER: {
                    value = (String.valueOf(alarmService.getUnClearAlarmByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下报警数量" + value);
                    break;
                }
                case DataConstants.ONLINE_GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOnlineGatewayByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下在线主机数量" + value);
                    break;
                }
                case DataConstants.OFFLINE_GATEWAY_NUMBER: {
                    value = (String.valueOf(deviceService.findAllOfflineGatewayByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下离线主机数量" + value);
                    break;
                }
                case ALL_ALARM_NUMBER: {
                    value = (String.valueOf(alarmService.getAllAlarmByTenant(new TenantId(UUIDConverter.fromString(tenantId)))));
                    log.info("保存企业下报警总数" + value);
                    break;
                }
                default: {
                    break;
                }
            }
            TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(dataSourceEntity.getPreparation() + DataConstants.ORGIN, value));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), dataSourceEntity.getId(), dataSourceEntity.getPreparation() + DataConstants.ORGIN, value));
        }
    }

    /**
     * 获取restApi数据
     *
     * @param restApiEntity
     */
    @SneakyThrows
    public void processRestApi(RestApiEntity restApiEntity) {
        Map<String, String> headers = new HashMap<>();
        if (restApiEntity.getHeaders() != null && !"".equals(restApiEntity.getHeaders())) {
            List<DefaultKeyValue> header = new ObjectMapper().readValue(restApiEntity.getHeaders(), new TypeReference<List<DefaultKeyValue>>() {
            });
            header.forEach(defaultMapObject -> {
                headers.put(defaultMapObject.getKey(), defaultMapObject.getValue());
            });

        }
        String result = null;
        if (restApiEntity.getMethod().equalsIgnoreCase(DataConstants.REST_METHOD_GET)) {
            Map<String, String> params = new HashMap<>();
            if (restApiEntity.getParams() != null && !"".equals(restApiEntity.getParams())) {
                List<DefaultKeyValue> header = new ObjectMapper().readValue(restApiEntity.getParams(), new TypeReference<List<DefaultKeyValue>>() {
                });
                header.forEach(defaultMapObject -> {
                    params.put(defaultMapObject.getKey(), defaultMapObject.getValue());
                });
            }
            //调用API
            result = RestUtil.GetMothod(restApiEntity.getURL(), headers, params);
        } else {
            result = RestUtil.PostMothod(restApiEntity.getURL(), headers, restApiEntity.getParams());
        }

        //解析
        List<DataSourceEntity> list = dataSourceService.findAllByOriginatorId(restApiEntity.getId(), DataSourceType.RESTAPI_SOURCE);
        for (DataSourceEntity d : list) {
            try {
                String data = JsonPath.parse(result).read(d.getParsingPath()) + "";
                originDataService.saveOriginData(OriginDataEntity.builder()
                        .dataSourceId(d.getId())
                        .updateTime(System.currentTimeMillis())
                        .value(data)
                        .build());
                TsKvEntry kvEntry = new BasicTsKvEntry(System.currentTimeMillis(), new StringDataEntry(d.getProperty(), data));
                timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(d.getId())), kvEntry);
                influxService.saveDeviceToInflux(convertToPushObject(System.currentTimeMillis(), d.getId(), d.getProperty(), data));
            } catch (Exception e) {
                log.info("解析出错，请检查路径");
            }


        }
    }

    /**
     * 计算统计量
     *
     * @param dataSourceEntity 数据源实体
     * @param isOrigin         是否是源数据
     */
    public void processStatistics(DataSourceEntity dataSourceEntity, boolean isOrigin) {
        if (isOrigin) {
            statisticsOriginData(dataSourceEntity);
        } else {
            try {
                List<FluxTable> fluxTables= influxService.findData(Collections.singletonList(dataSourceEntity.getId() + "." + (dataSourceEntity.getProperty() + ORGIN)),
                        (DateUtils.getStatisticsStart(System.currentTimeMillis(), dataSourceEntity.getFrequency(), dataSourceEntity.getTemplate()) - DateUtils.MINUTE_TIME),
                        DateUtils.getStatisticsEnd(System.currentTimeMillis(), false), REQUEST_DATA_STEPSIZE_MINUTE);
                if (fluxTables != null && fluxTables.size() > 0 && fluxTables.get(0).getRecords().size() > 0) {
                    List<String> list = new ArrayList<>();
                    fluxTables.get(0).getRecords().forEach(fluxRecord -> list.add(String.valueOf(fluxRecord.getValue())));
                    String value = "0";
                    switch (dataSourceEntity.getTemplate()) {
                        case VALUE: {
                            value = list.get(list.size() - 1);
                            break;
                        }
                        case STATISTICS_DATA_SOURCE_UPSTANDARD: {
                            for (String s : list) {
                                value = new BigDecimal(value).add(new BigDecimal(s)).toString();
                            }
                            if (new BigDecimal(value).compareTo(new BigDecimal("0")) > 0) {
                                value = "1";
                            } else {
                                value = "0";
                            }
                            break;
                        }
                        case STATISTICS_DATA_SOURCE_KEEP_TIME:
                        case STATISTICS_DATA_SOURCE_TEMPLATE_SUM: {
                            for (String s : list) {
                                value = new BigDecimal(value).add(new BigDecimal(s)).toString();
                            }
                            break;
                        }
                        case STATISTICS_DATA_SOURCE_TEMPLATE_SAVERAGE: {
                            for (String s : list) {
                                value = new BigDecimal(value).add(new BigDecimal(s)).toString();
                            }
                            value = new BigDecimal(value).divide(new BigDecimal(list.size()), 2, BigDecimal.ROUND_HALF_UP).toString();
                            break;
                        }
                        //变动
                        default: {
                            int count = 0;
                            for (int i = list.size() - 1; i > 0; i--) {
                                if (new BigDecimal(list.get(i)).subtract(new BigDecimal(list.get(i - 1))).compareTo(new BigDecimal("1")) == 0) {
                                    count++;
                                }
                            }
                            value = String.valueOf(count);
                        }
                    }
                    TsKvEntry kvEntry = new BasicTsKvEntry(DateUtils.getStatisticsSave(System.currentTimeMillis(), dataSourceEntity.getFrequency()), new StringDataEntry(dataSourceEntity.getProperty(), value));
                    timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
                    influxService.saveDeviceToInflux(convertToPushObject(DateUtils.getStatisticsSave(System.currentTimeMillis(), dataSourceEntity.getFrequency()), dataSourceEntity.getId(), dataSourceEntity.getProperty(), value));
                    log.info(dataSourceEntity.getName() + "运算统计量数据统计完成，时间：" + System.currentTimeMillis());
                } else {
                    log.info(dataSourceEntity.getName() + "未找到数据，无法运算，时间：" + (DateUtils.getStatisticsStart(System.currentTimeMillis(), dataSourceEntity.getFrequency(), dataSourceEntity.getTemplate()) - 10) + "-" + System.currentTimeMillis());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /*
     * 统计量源数据统计
     */
    private void statisticsOriginData(DataSourceEntity dataSourceEntity) {
        List<String> formulas = new ArrayList<>();
        if (dataSourceEntity.getParams() != null) {
            formulas.addAll(VirtualUtils.handleVirtualFormula(dataSourceEntity.getParams()));
        }
        if (dataSourceEntity.getFormula() != null) {
            formulas.addAll(VirtualUtils.handleVirtualFormula(dataSourceEntity.getFormula()));
        }
        List<String> result = new ArrayList<>();
        if (formulas.size() > 0) {
            formulas.forEach(fo -> {
                if (!isMathSymbol(fo) && !result.contains(fo)) {
                    result.add(fo);
                }
            });
        }
        Map<String, String> data = new HashMap<>();
        result.forEach(id -> {
            DataSourceEntity dataSource = dataSourceService.findById(id);
            if (dataSource != null) {
                String formula = null;
                switch (dataSource.getType()) {
                    case DEVICE_SOURCE: {
                        formula = dataSource.getDeviceId() + "." + dataSource.getProperty();
                        break;
                    }
                    case RESTAPI_SOURCE: {
                        formula = dataSource.getId() + "." + dataSource.getProperty();
                        break;
                    }
                    case PREPARATION_SOURCE: {
                        formula = dataSource.getId() + "." + (dataSource.getPreparation() + DataConstants.ORGIN);
                        break;
                    }
                    case STATISTICS_SOURCE: {
                        formula = dataSource.getId() + "." + (dataSource.getProperty() + DataConstants.ORGIN);
                        break;
                    }
                    case MID_SOURCE: {
                        OriginDataEntity value = originDataService.getLastOriginDataFormId(id);
                        if (value != null) {
                            data.put(id, value.getValue());
                        }
                        break;
                    }
                    case SYSTEM_SOURCE:
                    default: {
                        data.put(id, String.valueOf(DateUtils.getSysDataSource(System.currentTimeMillis(), dataSource.getProperty())));
                        break;
                    }
                }
                try {
                    if (formula != null) {
                        List<FluxTable> fluxTables = influxService.findData(Collections.singletonList(formula),
                                (DateUtils.getStatisticsStart(System.currentTimeMillis(), MINUTE, DataConstants.VALUE)),
                                getStatisticsEnd(System.currentTimeMillis(), true), MINUTE);
                        if (fluxTables != null && fluxTables.size() > 0) {
                            fluxTables.get(0).getRecords().forEach(fluxRecord -> data.put(id, String.valueOf(fluxRecord.getValue())));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        //判断统计条件是否满足
        boolean condition = false;
        if (!StringUtils.checkNotNull(dataSourceEntity.getParams())) {
            condition = true;
        } else {
            String param = dataSourceEntity.getParams();
            try {
                for (Map.Entry<String, String> entry : data.entrySet()) {
                    param = param.replace(entry.getKey(), entry.getValue());
                }
                condition = (Boolean) AviatorEvaluator.execute(param);
            } catch (Exception e) {
                log.info("条件运算异常！");
            }

        }
        String value = null;
        //按照统计模组进行统计
        if (condition) {
            switch (dataSourceEntity.getTemplate()) {
                //值类型
                default:
                case DataConstants.VALUE:
                    //求和
                case DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_SUM:
                    //平均值
                case DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_SAVERAGE: {
                    String formula = dataSourceEntity.getFormula();
                    for (Map.Entry<String, String> entry : data.entrySet()) {
                        formula = formula.replace(entry.getKey(), String.valueOf(Double.parseDouble(entry.getValue())));
                    }
                    value = new BigDecimal(AviatorEvaluator.execute(formula).toString()).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString();
                    break;
                }
                //保持时间
                //达标次数
                case DataConstants.STATISTICS_DATA_SOURCE_UPSTANDARD:
                case DataConstants.STATISTICS_DATA_SOURCE_KEEP_TIME:
                    //变动
                case DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_CHANGE: {
                    value = "1";
                    break;
                }
            }
        } else {
            switch (dataSourceEntity.getTemplate()) {

                //保持时间
                //达标次数
                case DataConstants.STATISTICS_DATA_SOURCE_UPSTANDARD:
                case DataConstants.STATISTICS_DATA_SOURCE_KEEP_TIME:
                    //变动
                case DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_CHANGE: {
                    value = "0";
                    break;
                }
                default: {
                    break;
                }
            }
        }
        if (value != null) {
//            if (DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_CHANGE.equals(dataSourceEntity.getTemplate()) || DataConstants.STATISTICS_DATA_SOURCE_KEEP_TIME.equals(dataSourceEntity.getTemplate())) {
//                TsKvEntry kvEntry = new BasicTsKvEntry((DateUtils.getStatisticsStart(System.currentTimeMillis(), MINUTE, DataConstants.VALUE)) / 1000, new StringDataEntry(dataSourceEntity.getProperty() + DataConstants.ORGIN, value));
//                timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
//                RestUtil.doResetPush(DataConstants.PUT_URL, convertToPushObject((DateUtils.getStatisticsStart(System.currentTimeMillis(), MINUTE, DataConstants.VALUE)) / 1000, dataSourceEntity.getId(), dataSourceEntity.getProperty() + DataConstants.ORGIN, value));
//            } else {
            TsKvEntry kvEntry = new BasicTsKvEntry((DateUtils.getStatisticsStart(System.currentTimeMillis(), MINUTE, DataConstants.VALUE)) / 1000, new StringDataEntry(dataSourceEntity.getProperty() + DataConstants.ORGIN, value));
            timeseriesService.saveTs(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), kvEntry);
            log.info(dataSourceEntity.getName() + "源数据ok，时间：" + System.currentTimeMillis() / 1000);
            influxService.saveDeviceToInflux(convertToPushObject((DateUtils.getStatisticsStart(System.currentTimeMillis(), MINUTE, DataConstants.VALUE)) / 1000, dataSourceEntity.getId(), dataSourceEntity.getProperty() + DataConstants.ORGIN, value));
//            }
        }
    }

    /**
     * 指数计算
     *
     * @param tbContext
     * @param indexFormula
     */
    private String covertByIndex(TbContext tbContext, String indexFormula, Map<String, TsKvEntry> tsKvEntryMap) {
        String string = "0";
        try {
            String[] params = indexFormula.split(DataConstants.INDEX);
            string = String.valueOf(Math.pow(Double.parseDouble(getAttributeValue(tbContext, params[0], tsKvEntryMap)), Double.parseDouble(getAttributeValue(tbContext, params[1], tsKvEntryMap))));
        } catch (Exception e) {
            log.error("指数运算出现错误！公式为" + indexFormula + "，错误信息：" + e.getMessage());
        }
        return string;
    }

    private String getAttributeValue(TbContext tbContext, String formula, Map<String, TsKvEntry> map) {
        try {
            if (!isMathSymbol(formula)) {
                TsKvEntry tsKvEntry = null;
                if (map.containsKey(formula)) {
                    tsKvEntry = map.get(formula);
                } else {
                    String[] params = formula.split("\\.");
                    List<TsKvEntry> results = tbContext.getTimeseriesService().findLatest(new DeviceId(UUIDConverter.fromString(params[0])), Collections.singletonList(params[1])).get();
                    //此处查询只会查询一次，所以直接取第一个
                    if (results != null && results.size() > 0) {
                        tsKvEntry = results.get(0);
                    }
                }
                return tsKvEntry.getValueAsString();
            } else {
                return formula;
            }
        } catch (Exception e) {
            return "0";
        }
    }


//    /**
//     * 构造获取数据体
//     *
//     * @param start
//     * @param end
//     * @param metric
//     * @param prop
//     * @param downsample
//     * @return
//     */
//    private Object convertToQueryObject(long start, long end, String metric, String prop, String downsample) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put(DataConstants.START, start);
//        jsonObject.put(DataConstants.END, end);
//
//        JSONArray queries = new JSONArray();
//        JSONObject query = new JSONObject();
//        query.put("aggregator", DataConstants.RESPONSE_ENERGY_OPEN_TS_DB_NONE);
//        query.put("metric", metric);
//        query.put("downsample", downsample);
//
//        Map<String, String> tags = new HashMap<>();
//        tags.put("prop", prop);
//
//        query.put("tags", tags);
//        queries.add(query);
//        jsonObject.put("queries", queries);
//        return jsonObject;
//    }

    /**
     * 构造数据上传数据体
     *
     * @param metric
     * @param prop
     * @return
     */
    private List<DataPoint> convertToPushObject(long ts, String metric, String prop, String value) {
        List<DataPoint> dataPoints = new ArrayList<>();
//        HashMap<String, String> hashMap = new HashMap<>();
//        hashMap.put("prop", prop);
        dataPoints.add(new DataPoint(metric, ts, value, prop));
        return dataPoints;
    }


}
