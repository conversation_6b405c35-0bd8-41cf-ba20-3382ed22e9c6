// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: jsinvoke.proto

package org.thingsboard.server.gen.js;

public final class JsInvokeProtos {
  private JsInvokeProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code js.JsInvokeErrorCode}
   */
  public enum JsInvokeErrorCode
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>COMPILATION_ERROR = 0;</code>
     */
    COMPILATION_ERROR(0),
    /**
     * <code>RUNTIME_ERROR = 1;</code>
     */
    RUNTIME_ERROR(1),
    /**
     * <code>TIMEOUT_ERROR = 2;</code>
     */
    TIMEOUT_ERROR(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>COMPILATION_ERROR = 0;</code>
     */
    public static final int COMPILATION_ERROR_VALUE = 0;
    /**
     * <code>RUNTIME_ERROR = 1;</code>
     */
    public static final int RUNTIME_ERROR_VALUE = 1;
    /**
     * <code>TIMEOUT_ERROR = 2;</code>
     */
    public static final int TIMEOUT_ERROR_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static JsInvokeErrorCode valueOf(int value) {
      return forNumber(value);
    }

    public static JsInvokeErrorCode forNumber(int value) {
      switch (value) {
        case 0: return COMPILATION_ERROR;
        case 1: return RUNTIME_ERROR;
        case 2: return TIMEOUT_ERROR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<JsInvokeErrorCode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        JsInvokeErrorCode> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<JsInvokeErrorCode>() {
            public JsInvokeErrorCode findValueByNumber(int number) {
              return JsInvokeErrorCode.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.getDescriptor().getEnumTypes().get(0);
    }

    private static final JsInvokeErrorCode[] VALUES = values();

    public static JsInvokeErrorCode valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private JsInvokeErrorCode(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:js.JsInvokeErrorCode)
  }

  public interface RemoteJsRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.RemoteJsRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string responseTopic = 1;</code>
     */
    java.lang.String getResponseTopic();
    /**
     * <code>optional string responseTopic = 1;</code>
     */
    com.google.protobuf.ByteString
        getResponseTopicBytes();

    /**
     * <code>optional int64 requestIdMSB = 2;</code>
     */
    long getRequestIdMSB();

    /**
     * <code>optional int64 requestIdLSB = 3;</code>
     */
    long getRequestIdLSB();

    /**
     * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
     */
    boolean hasCompileRequest();
    /**
     * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest getCompileRequest();
    /**
     * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder getCompileRequestOrBuilder();

    /**
     * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
     */
    boolean hasInvokeRequest();
    /**
     * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest getInvokeRequest();
    /**
     * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder getInvokeRequestOrBuilder();

    /**
     * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
     */
    boolean hasReleaseRequest();
    /**
     * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest getReleaseRequest();
    /**
     * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder getReleaseRequestOrBuilder();
  }
  /**
   * Protobuf type {@code js.RemoteJsRequest}
   */
  public  static final class RemoteJsRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.RemoteJsRequest)
      RemoteJsRequestOrBuilder {
    // Use RemoteJsRequest.newBuilder() to construct.
    private RemoteJsRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RemoteJsRequest() {
      responseTopic_ = "";
      requestIdMSB_ = 0L;
      requestIdLSB_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private RemoteJsRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              responseTopic_ = s;
              break;
            }
            case 16: {

              requestIdMSB_ = input.readInt64();
              break;
            }
            case 24: {

              requestIdLSB_ = input.readInt64();
              break;
            }
            case 34: {
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder subBuilder = null;
              if (compileRequest_ != null) {
                subBuilder = compileRequest_.toBuilder();
              }
              compileRequest_ = input.readMessage(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(compileRequest_);
                compileRequest_ = subBuilder.buildPartial();
              }

              break;
            }
            case 42: {
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder subBuilder = null;
              if (invokeRequest_ != null) {
                subBuilder = invokeRequest_.toBuilder();
              }
              invokeRequest_ = input.readMessage(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(invokeRequest_);
                invokeRequest_ = subBuilder.buildPartial();
              }

              break;
            }
            case 50: {
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder subBuilder = null;
              if (releaseRequest_ != null) {
                subBuilder = releaseRequest_.toBuilder();
              }
              releaseRequest_ = input.readMessage(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(releaseRequest_);
                releaseRequest_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.Builder.class);
    }

    public static final int RESPONSETOPIC_FIELD_NUMBER = 1;
    private volatile java.lang.Object responseTopic_;
    /**
     * <code>optional string responseTopic = 1;</code>
     */
    public java.lang.String getResponseTopic() {
      java.lang.Object ref = responseTopic_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        responseTopic_ = s;
        return s;
      }
    }
    /**
     * <code>optional string responseTopic = 1;</code>
     */
    public com.google.protobuf.ByteString
        getResponseTopicBytes() {
      java.lang.Object ref = responseTopic_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        responseTopic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQUESTIDMSB_FIELD_NUMBER = 2;
    private long requestIdMSB_;
    /**
     * <code>optional int64 requestIdMSB = 2;</code>
     */
    public long getRequestIdMSB() {
      return requestIdMSB_;
    }

    public static final int REQUESTIDLSB_FIELD_NUMBER = 3;
    private long requestIdLSB_;
    /**
     * <code>optional int64 requestIdLSB = 3;</code>
     */
    public long getRequestIdLSB() {
      return requestIdLSB_;
    }

    public static final int COMPILEREQUEST_FIELD_NUMBER = 4;
    private org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest compileRequest_;
    /**
     * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
     */
    public boolean hasCompileRequest() {
      return compileRequest_ != null;
    }
    /**
     * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest getCompileRequest() {
      return compileRequest_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.getDefaultInstance() : compileRequest_;
    }
    /**
     * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder getCompileRequestOrBuilder() {
      return getCompileRequest();
    }

    public static final int INVOKEREQUEST_FIELD_NUMBER = 5;
    private org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest invokeRequest_;
    /**
     * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
     */
    public boolean hasInvokeRequest() {
      return invokeRequest_ != null;
    }
    /**
     * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest getInvokeRequest() {
      return invokeRequest_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.getDefaultInstance() : invokeRequest_;
    }
    /**
     * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder getInvokeRequestOrBuilder() {
      return getInvokeRequest();
    }

    public static final int RELEASEREQUEST_FIELD_NUMBER = 6;
    private org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest releaseRequest_;
    /**
     * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
     */
    public boolean hasReleaseRequest() {
      return releaseRequest_ != null;
    }
    /**
     * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest getReleaseRequest() {
      return releaseRequest_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.getDefaultInstance() : releaseRequest_;
    }
    /**
     * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder getReleaseRequestOrBuilder() {
      return getReleaseRequest();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getResponseTopicBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, responseTopic_);
      }
      if (requestIdMSB_ != 0L) {
        output.writeInt64(2, requestIdMSB_);
      }
      if (requestIdLSB_ != 0L) {
        output.writeInt64(3, requestIdLSB_);
      }
      if (compileRequest_ != null) {
        output.writeMessage(4, getCompileRequest());
      }
      if (invokeRequest_ != null) {
        output.writeMessage(5, getInvokeRequest());
      }
      if (releaseRequest_ != null) {
        output.writeMessage(6, getReleaseRequest());
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getResponseTopicBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, responseTopic_);
      }
      if (requestIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, requestIdMSB_);
      }
      if (requestIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, requestIdLSB_);
      }
      if (compileRequest_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCompileRequest());
      }
      if (invokeRequest_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getInvokeRequest());
      }
      if (releaseRequest_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getReleaseRequest());
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest other = (org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest) obj;

      boolean result = true;
      result = result && getResponseTopic()
          .equals(other.getResponseTopic());
      result = result && (getRequestIdMSB()
          == other.getRequestIdMSB());
      result = result && (getRequestIdLSB()
          == other.getRequestIdLSB());
      result = result && (hasCompileRequest() == other.hasCompileRequest());
      if (hasCompileRequest()) {
        result = result && getCompileRequest()
            .equals(other.getCompileRequest());
      }
      result = result && (hasInvokeRequest() == other.hasInvokeRequest());
      if (hasInvokeRequest()) {
        result = result && getInvokeRequest()
            .equals(other.getInvokeRequest());
      }
      result = result && (hasReleaseRequest() == other.hasReleaseRequest());
      if (hasReleaseRequest()) {
        result = result && getReleaseRequest()
            .equals(other.getReleaseRequest());
      }
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + RESPONSETOPIC_FIELD_NUMBER;
      hash = (53 * hash) + getResponseTopic().hashCode();
      hash = (37 * hash) + REQUESTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestIdMSB());
      hash = (37 * hash) + REQUESTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestIdLSB());
      if (hasCompileRequest()) {
        hash = (37 * hash) + COMPILEREQUEST_FIELD_NUMBER;
        hash = (53 * hash) + getCompileRequest().hashCode();
      }
      if (hasInvokeRequest()) {
        hash = (37 * hash) + INVOKEREQUEST_FIELD_NUMBER;
        hash = (53 * hash) + getInvokeRequest().hashCode();
      }
      if (hasReleaseRequest()) {
        hash = (37 * hash) + RELEASEREQUEST_FIELD_NUMBER;
        hash = (53 * hash) + getReleaseRequest().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.RemoteJsRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.RemoteJsRequest)
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        responseTopic_ = "";

        requestIdMSB_ = 0L;

        requestIdLSB_ = 0L;

        if (compileRequestBuilder_ == null) {
          compileRequest_ = null;
        } else {
          compileRequest_ = null;
          compileRequestBuilder_ = null;
        }
        if (invokeRequestBuilder_ == null) {
          invokeRequest_ = null;
        } else {
          invokeRequest_ = null;
          invokeRequestBuilder_ = null;
        }
        if (releaseRequestBuilder_ == null) {
          releaseRequest_ = null;
        } else {
          releaseRequest_ = null;
          releaseRequestBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsRequest_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest result = new org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest(this);
        result.responseTopic_ = responseTopic_;
        result.requestIdMSB_ = requestIdMSB_;
        result.requestIdLSB_ = requestIdLSB_;
        if (compileRequestBuilder_ == null) {
          result.compileRequest_ = compileRequest_;
        } else {
          result.compileRequest_ = compileRequestBuilder_.build();
        }
        if (invokeRequestBuilder_ == null) {
          result.invokeRequest_ = invokeRequest_;
        } else {
          result.invokeRequest_ = invokeRequestBuilder_.build();
        }
        if (releaseRequestBuilder_ == null) {
          result.releaseRequest_ = releaseRequest_;
        } else {
          result.releaseRequest_ = releaseRequestBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest.getDefaultInstance()) return this;
        if (!other.getResponseTopic().isEmpty()) {
          responseTopic_ = other.responseTopic_;
          onChanged();
        }
        if (other.getRequestIdMSB() != 0L) {
          setRequestIdMSB(other.getRequestIdMSB());
        }
        if (other.getRequestIdLSB() != 0L) {
          setRequestIdLSB(other.getRequestIdLSB());
        }
        if (other.hasCompileRequest()) {
          mergeCompileRequest(other.getCompileRequest());
        }
        if (other.hasInvokeRequest()) {
          mergeInvokeRequest(other.getInvokeRequest());
        }
        if (other.hasReleaseRequest()) {
          mergeReleaseRequest(other.getReleaseRequest());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object responseTopic_ = "";
      /**
       * <code>optional string responseTopic = 1;</code>
       */
      public java.lang.String getResponseTopic() {
        java.lang.Object ref = responseTopic_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          responseTopic_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string responseTopic = 1;</code>
       */
      public com.google.protobuf.ByteString
          getResponseTopicBytes() {
        java.lang.Object ref = responseTopic_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          responseTopic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string responseTopic = 1;</code>
       */
      public Builder setResponseTopic(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        responseTopic_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string responseTopic = 1;</code>
       */
      public Builder clearResponseTopic() {
        
        responseTopic_ = getDefaultInstance().getResponseTopic();
        onChanged();
        return this;
      }
      /**
       * <code>optional string responseTopic = 1;</code>
       */
      public Builder setResponseTopicBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        responseTopic_ = value;
        onChanged();
        return this;
      }

      private long requestIdMSB_ ;
      /**
       * <code>optional int64 requestIdMSB = 2;</code>
       */
      public long getRequestIdMSB() {
        return requestIdMSB_;
      }
      /**
       * <code>optional int64 requestIdMSB = 2;</code>
       */
      public Builder setRequestIdMSB(long value) {
        
        requestIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 requestIdMSB = 2;</code>
       */
      public Builder clearRequestIdMSB() {
        
        requestIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long requestIdLSB_ ;
      /**
       * <code>optional int64 requestIdLSB = 3;</code>
       */
      public long getRequestIdLSB() {
        return requestIdLSB_;
      }
      /**
       * <code>optional int64 requestIdLSB = 3;</code>
       */
      public Builder setRequestIdLSB(long value) {
        
        requestIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 requestIdLSB = 3;</code>
       */
      public Builder clearRequestIdLSB() {
        
        requestIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest compileRequest_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder> compileRequestBuilder_;
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public boolean hasCompileRequest() {
        return compileRequestBuilder_ != null || compileRequest_ != null;
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest getCompileRequest() {
        if (compileRequestBuilder_ == null) {
          return compileRequest_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.getDefaultInstance() : compileRequest_;
        } else {
          return compileRequestBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public Builder setCompileRequest(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest value) {
        if (compileRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          compileRequest_ = value;
          onChanged();
        } else {
          compileRequestBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public Builder setCompileRequest(
          org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder builderForValue) {
        if (compileRequestBuilder_ == null) {
          compileRequest_ = builderForValue.build();
          onChanged();
        } else {
          compileRequestBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public Builder mergeCompileRequest(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest value) {
        if (compileRequestBuilder_ == null) {
          if (compileRequest_ != null) {
            compileRequest_ =
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.newBuilder(compileRequest_).mergeFrom(value).buildPartial();
          } else {
            compileRequest_ = value;
          }
          onChanged();
        } else {
          compileRequestBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public Builder clearCompileRequest() {
        if (compileRequestBuilder_ == null) {
          compileRequest_ = null;
          onChanged();
        } else {
          compileRequest_ = null;
          compileRequestBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder getCompileRequestBuilder() {
        
        onChanged();
        return getCompileRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder getCompileRequestOrBuilder() {
        if (compileRequestBuilder_ != null) {
          return compileRequestBuilder_.getMessageOrBuilder();
        } else {
          return compileRequest_ == null ?
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.getDefaultInstance() : compileRequest_;
        }
      }
      /**
       * <code>optional .js.JsCompileRequest compileRequest = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder> 
          getCompileRequestFieldBuilder() {
        if (compileRequestBuilder_ == null) {
          compileRequestBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder>(
                  getCompileRequest(),
                  getParentForChildren(),
                  isClean());
          compileRequest_ = null;
        }
        return compileRequestBuilder_;
      }

      private org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest invokeRequest_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder> invokeRequestBuilder_;
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public boolean hasInvokeRequest() {
        return invokeRequestBuilder_ != null || invokeRequest_ != null;
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest getInvokeRequest() {
        if (invokeRequestBuilder_ == null) {
          return invokeRequest_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.getDefaultInstance() : invokeRequest_;
        } else {
          return invokeRequestBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public Builder setInvokeRequest(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest value) {
        if (invokeRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          invokeRequest_ = value;
          onChanged();
        } else {
          invokeRequestBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public Builder setInvokeRequest(
          org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder builderForValue) {
        if (invokeRequestBuilder_ == null) {
          invokeRequest_ = builderForValue.build();
          onChanged();
        } else {
          invokeRequestBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public Builder mergeInvokeRequest(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest value) {
        if (invokeRequestBuilder_ == null) {
          if (invokeRequest_ != null) {
            invokeRequest_ =
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.newBuilder(invokeRequest_).mergeFrom(value).buildPartial();
          } else {
            invokeRequest_ = value;
          }
          onChanged();
        } else {
          invokeRequestBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public Builder clearInvokeRequest() {
        if (invokeRequestBuilder_ == null) {
          invokeRequest_ = null;
          onChanged();
        } else {
          invokeRequest_ = null;
          invokeRequestBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder getInvokeRequestBuilder() {
        
        onChanged();
        return getInvokeRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder getInvokeRequestOrBuilder() {
        if (invokeRequestBuilder_ != null) {
          return invokeRequestBuilder_.getMessageOrBuilder();
        } else {
          return invokeRequest_ == null ?
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.getDefaultInstance() : invokeRequest_;
        }
      }
      /**
       * <code>optional .js.JsInvokeRequest invokeRequest = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder> 
          getInvokeRequestFieldBuilder() {
        if (invokeRequestBuilder_ == null) {
          invokeRequestBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder>(
                  getInvokeRequest(),
                  getParentForChildren(),
                  isClean());
          invokeRequest_ = null;
        }
        return invokeRequestBuilder_;
      }

      private org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest releaseRequest_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder> releaseRequestBuilder_;
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public boolean hasReleaseRequest() {
        return releaseRequestBuilder_ != null || releaseRequest_ != null;
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest getReleaseRequest() {
        if (releaseRequestBuilder_ == null) {
          return releaseRequest_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.getDefaultInstance() : releaseRequest_;
        } else {
          return releaseRequestBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public Builder setReleaseRequest(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest value) {
        if (releaseRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          releaseRequest_ = value;
          onChanged();
        } else {
          releaseRequestBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public Builder setReleaseRequest(
          org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder builderForValue) {
        if (releaseRequestBuilder_ == null) {
          releaseRequest_ = builderForValue.build();
          onChanged();
        } else {
          releaseRequestBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public Builder mergeReleaseRequest(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest value) {
        if (releaseRequestBuilder_ == null) {
          if (releaseRequest_ != null) {
            releaseRequest_ =
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.newBuilder(releaseRequest_).mergeFrom(value).buildPartial();
          } else {
            releaseRequest_ = value;
          }
          onChanged();
        } else {
          releaseRequestBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public Builder clearReleaseRequest() {
        if (releaseRequestBuilder_ == null) {
          releaseRequest_ = null;
          onChanged();
        } else {
          releaseRequest_ = null;
          releaseRequestBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder getReleaseRequestBuilder() {
        
        onChanged();
        return getReleaseRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder getReleaseRequestOrBuilder() {
        if (releaseRequestBuilder_ != null) {
          return releaseRequestBuilder_.getMessageOrBuilder();
        } else {
          return releaseRequest_ == null ?
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.getDefaultInstance() : releaseRequest_;
        }
      }
      /**
       * <code>optional .js.JsReleaseRequest releaseRequest = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder> 
          getReleaseRequestFieldBuilder() {
        if (releaseRequestBuilder_ == null) {
          releaseRequestBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder>(
                  getReleaseRequest(),
                  getParentForChildren(),
                  isClean());
          releaseRequest_ = null;
        }
        return releaseRequestBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.RemoteJsRequest)
    }

    // @@protoc_insertion_point(class_scope:js.RemoteJsRequest)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RemoteJsRequest>
        PARSER = new com.google.protobuf.AbstractParser<RemoteJsRequest>() {
      public RemoteJsRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RemoteJsRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RemoteJsRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RemoteJsRequest> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RemoteJsResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.RemoteJsResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 requestIdMSB = 1;</code>
     */
    long getRequestIdMSB();

    /**
     * <code>optional int64 requestIdLSB = 2;</code>
     */
    long getRequestIdLSB();

    /**
     * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
     */
    boolean hasCompileResponse();
    /**
     * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse getCompileResponse();
    /**
     * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder getCompileResponseOrBuilder();

    /**
     * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
     */
    boolean hasInvokeResponse();
    /**
     * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse getInvokeResponse();
    /**
     * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder getInvokeResponseOrBuilder();

    /**
     * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
     */
    boolean hasReleaseResponse();
    /**
     * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse getReleaseResponse();
    /**
     * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder getReleaseResponseOrBuilder();
  }
  /**
   * Protobuf type {@code js.RemoteJsResponse}
   */
  public  static final class RemoteJsResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.RemoteJsResponse)
      RemoteJsResponseOrBuilder {
    // Use RemoteJsResponse.newBuilder() to construct.
    private RemoteJsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RemoteJsResponse() {
      requestIdMSB_ = 0L;
      requestIdLSB_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private RemoteJsResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              requestIdMSB_ = input.readInt64();
              break;
            }
            case 16: {

              requestIdLSB_ = input.readInt64();
              break;
            }
            case 26: {
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder subBuilder = null;
              if (compileResponse_ != null) {
                subBuilder = compileResponse_.toBuilder();
              }
              compileResponse_ = input.readMessage(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(compileResponse_);
                compileResponse_ = subBuilder.buildPartial();
              }

              break;
            }
            case 34: {
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder subBuilder = null;
              if (invokeResponse_ != null) {
                subBuilder = invokeResponse_.toBuilder();
              }
              invokeResponse_ = input.readMessage(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(invokeResponse_);
                invokeResponse_ = subBuilder.buildPartial();
              }

              break;
            }
            case 42: {
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder subBuilder = null;
              if (releaseResponse_ != null) {
                subBuilder = releaseResponse_.toBuilder();
              }
              releaseResponse_ = input.readMessage(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(releaseResponse_);
                releaseResponse_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.Builder.class);
    }

    public static final int REQUESTIDMSB_FIELD_NUMBER = 1;
    private long requestIdMSB_;
    /**
     * <code>optional int64 requestIdMSB = 1;</code>
     */
    public long getRequestIdMSB() {
      return requestIdMSB_;
    }

    public static final int REQUESTIDLSB_FIELD_NUMBER = 2;
    private long requestIdLSB_;
    /**
     * <code>optional int64 requestIdLSB = 2;</code>
     */
    public long getRequestIdLSB() {
      return requestIdLSB_;
    }

    public static final int COMPILERESPONSE_FIELD_NUMBER = 3;
    private org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse compileResponse_;
    /**
     * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
     */
    public boolean hasCompileResponse() {
      return compileResponse_ != null;
    }
    /**
     * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse getCompileResponse() {
      return compileResponse_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.getDefaultInstance() : compileResponse_;
    }
    /**
     * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder getCompileResponseOrBuilder() {
      return getCompileResponse();
    }

    public static final int INVOKERESPONSE_FIELD_NUMBER = 4;
    private org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse invokeResponse_;
    /**
     * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
     */
    public boolean hasInvokeResponse() {
      return invokeResponse_ != null;
    }
    /**
     * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse getInvokeResponse() {
      return invokeResponse_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.getDefaultInstance() : invokeResponse_;
    }
    /**
     * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder getInvokeResponseOrBuilder() {
      return getInvokeResponse();
    }

    public static final int RELEASERESPONSE_FIELD_NUMBER = 5;
    private org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse releaseResponse_;
    /**
     * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
     */
    public boolean hasReleaseResponse() {
      return releaseResponse_ != null;
    }
    /**
     * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse getReleaseResponse() {
      return releaseResponse_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.getDefaultInstance() : releaseResponse_;
    }
    /**
     * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder getReleaseResponseOrBuilder() {
      return getReleaseResponse();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (requestIdMSB_ != 0L) {
        output.writeInt64(1, requestIdMSB_);
      }
      if (requestIdLSB_ != 0L) {
        output.writeInt64(2, requestIdLSB_);
      }
      if (compileResponse_ != null) {
        output.writeMessage(3, getCompileResponse());
      }
      if (invokeResponse_ != null) {
        output.writeMessage(4, getInvokeResponse());
      }
      if (releaseResponse_ != null) {
        output.writeMessage(5, getReleaseResponse());
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (requestIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, requestIdMSB_);
      }
      if (requestIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, requestIdLSB_);
      }
      if (compileResponse_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCompileResponse());
      }
      if (invokeResponse_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getInvokeResponse());
      }
      if (releaseResponse_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getReleaseResponse());
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse other = (org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse) obj;

      boolean result = true;
      result = result && (getRequestIdMSB()
          == other.getRequestIdMSB());
      result = result && (getRequestIdLSB()
          == other.getRequestIdLSB());
      result = result && (hasCompileResponse() == other.hasCompileResponse());
      if (hasCompileResponse()) {
        result = result && getCompileResponse()
            .equals(other.getCompileResponse());
      }
      result = result && (hasInvokeResponse() == other.hasInvokeResponse());
      if (hasInvokeResponse()) {
        result = result && getInvokeResponse()
            .equals(other.getInvokeResponse());
      }
      result = result && (hasReleaseResponse() == other.hasReleaseResponse());
      if (hasReleaseResponse()) {
        result = result && getReleaseResponse()
            .equals(other.getReleaseResponse());
      }
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + REQUESTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestIdMSB());
      hash = (37 * hash) + REQUESTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestIdLSB());
      if (hasCompileResponse()) {
        hash = (37 * hash) + COMPILERESPONSE_FIELD_NUMBER;
        hash = (53 * hash) + getCompileResponse().hashCode();
      }
      if (hasInvokeResponse()) {
        hash = (37 * hash) + INVOKERESPONSE_FIELD_NUMBER;
        hash = (53 * hash) + getInvokeResponse().hashCode();
      }
      if (hasReleaseResponse()) {
        hash = (37 * hash) + RELEASERESPONSE_FIELD_NUMBER;
        hash = (53 * hash) + getReleaseResponse().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.RemoteJsResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.RemoteJsResponse)
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        requestIdMSB_ = 0L;

        requestIdLSB_ = 0L;

        if (compileResponseBuilder_ == null) {
          compileResponse_ = null;
        } else {
          compileResponse_ = null;
          compileResponseBuilder_ = null;
        }
        if (invokeResponseBuilder_ == null) {
          invokeResponse_ = null;
        } else {
          invokeResponse_ = null;
          invokeResponseBuilder_ = null;
        }
        if (releaseResponseBuilder_ == null) {
          releaseResponse_ = null;
        } else {
          releaseResponse_ = null;
          releaseResponseBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_RemoteJsResponse_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse result = new org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse(this);
        result.requestIdMSB_ = requestIdMSB_;
        result.requestIdLSB_ = requestIdLSB_;
        if (compileResponseBuilder_ == null) {
          result.compileResponse_ = compileResponse_;
        } else {
          result.compileResponse_ = compileResponseBuilder_.build();
        }
        if (invokeResponseBuilder_ == null) {
          result.invokeResponse_ = invokeResponse_;
        } else {
          result.invokeResponse_ = invokeResponseBuilder_.build();
        }
        if (releaseResponseBuilder_ == null) {
          result.releaseResponse_ = releaseResponse_;
        } else {
          result.releaseResponse_ = releaseResponseBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse.getDefaultInstance()) return this;
        if (other.getRequestIdMSB() != 0L) {
          setRequestIdMSB(other.getRequestIdMSB());
        }
        if (other.getRequestIdLSB() != 0L) {
          setRequestIdLSB(other.getRequestIdLSB());
        }
        if (other.hasCompileResponse()) {
          mergeCompileResponse(other.getCompileResponse());
        }
        if (other.hasInvokeResponse()) {
          mergeInvokeResponse(other.getInvokeResponse());
        }
        if (other.hasReleaseResponse()) {
          mergeReleaseResponse(other.getReleaseResponse());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long requestIdMSB_ ;
      /**
       * <code>optional int64 requestIdMSB = 1;</code>
       */
      public long getRequestIdMSB() {
        return requestIdMSB_;
      }
      /**
       * <code>optional int64 requestIdMSB = 1;</code>
       */
      public Builder setRequestIdMSB(long value) {
        
        requestIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 requestIdMSB = 1;</code>
       */
      public Builder clearRequestIdMSB() {
        
        requestIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long requestIdLSB_ ;
      /**
       * <code>optional int64 requestIdLSB = 2;</code>
       */
      public long getRequestIdLSB() {
        return requestIdLSB_;
      }
      /**
       * <code>optional int64 requestIdLSB = 2;</code>
       */
      public Builder setRequestIdLSB(long value) {
        
        requestIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 requestIdLSB = 2;</code>
       */
      public Builder clearRequestIdLSB() {
        
        requestIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse compileResponse_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder> compileResponseBuilder_;
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public boolean hasCompileResponse() {
        return compileResponseBuilder_ != null || compileResponse_ != null;
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse getCompileResponse() {
        if (compileResponseBuilder_ == null) {
          return compileResponse_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.getDefaultInstance() : compileResponse_;
        } else {
          return compileResponseBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public Builder setCompileResponse(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse value) {
        if (compileResponseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          compileResponse_ = value;
          onChanged();
        } else {
          compileResponseBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public Builder setCompileResponse(
          org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder builderForValue) {
        if (compileResponseBuilder_ == null) {
          compileResponse_ = builderForValue.build();
          onChanged();
        } else {
          compileResponseBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public Builder mergeCompileResponse(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse value) {
        if (compileResponseBuilder_ == null) {
          if (compileResponse_ != null) {
            compileResponse_ =
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.newBuilder(compileResponse_).mergeFrom(value).buildPartial();
          } else {
            compileResponse_ = value;
          }
          onChanged();
        } else {
          compileResponseBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public Builder clearCompileResponse() {
        if (compileResponseBuilder_ == null) {
          compileResponse_ = null;
          onChanged();
        } else {
          compileResponse_ = null;
          compileResponseBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder getCompileResponseBuilder() {
        
        onChanged();
        return getCompileResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder getCompileResponseOrBuilder() {
        if (compileResponseBuilder_ != null) {
          return compileResponseBuilder_.getMessageOrBuilder();
        } else {
          return compileResponse_ == null ?
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.getDefaultInstance() : compileResponse_;
        }
      }
      /**
       * <code>optional .js.JsCompileResponse compileResponse = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder> 
          getCompileResponseFieldBuilder() {
        if (compileResponseBuilder_ == null) {
          compileResponseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder>(
                  getCompileResponse(),
                  getParentForChildren(),
                  isClean());
          compileResponse_ = null;
        }
        return compileResponseBuilder_;
      }

      private org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse invokeResponse_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder> invokeResponseBuilder_;
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public boolean hasInvokeResponse() {
        return invokeResponseBuilder_ != null || invokeResponse_ != null;
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse getInvokeResponse() {
        if (invokeResponseBuilder_ == null) {
          return invokeResponse_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.getDefaultInstance() : invokeResponse_;
        } else {
          return invokeResponseBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public Builder setInvokeResponse(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse value) {
        if (invokeResponseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          invokeResponse_ = value;
          onChanged();
        } else {
          invokeResponseBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public Builder setInvokeResponse(
          org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder builderForValue) {
        if (invokeResponseBuilder_ == null) {
          invokeResponse_ = builderForValue.build();
          onChanged();
        } else {
          invokeResponseBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public Builder mergeInvokeResponse(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse value) {
        if (invokeResponseBuilder_ == null) {
          if (invokeResponse_ != null) {
            invokeResponse_ =
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.newBuilder(invokeResponse_).mergeFrom(value).buildPartial();
          } else {
            invokeResponse_ = value;
          }
          onChanged();
        } else {
          invokeResponseBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public Builder clearInvokeResponse() {
        if (invokeResponseBuilder_ == null) {
          invokeResponse_ = null;
          onChanged();
        } else {
          invokeResponse_ = null;
          invokeResponseBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder getInvokeResponseBuilder() {
        
        onChanged();
        return getInvokeResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder getInvokeResponseOrBuilder() {
        if (invokeResponseBuilder_ != null) {
          return invokeResponseBuilder_.getMessageOrBuilder();
        } else {
          return invokeResponse_ == null ?
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.getDefaultInstance() : invokeResponse_;
        }
      }
      /**
       * <code>optional .js.JsInvokeResponse invokeResponse = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder> 
          getInvokeResponseFieldBuilder() {
        if (invokeResponseBuilder_ == null) {
          invokeResponseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder>(
                  getInvokeResponse(),
                  getParentForChildren(),
                  isClean());
          invokeResponse_ = null;
        }
        return invokeResponseBuilder_;
      }

      private org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse releaseResponse_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder> releaseResponseBuilder_;
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public boolean hasReleaseResponse() {
        return releaseResponseBuilder_ != null || releaseResponse_ != null;
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse getReleaseResponse() {
        if (releaseResponseBuilder_ == null) {
          return releaseResponse_ == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.getDefaultInstance() : releaseResponse_;
        } else {
          return releaseResponseBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public Builder setReleaseResponse(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse value) {
        if (releaseResponseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          releaseResponse_ = value;
          onChanged();
        } else {
          releaseResponseBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public Builder setReleaseResponse(
          org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder builderForValue) {
        if (releaseResponseBuilder_ == null) {
          releaseResponse_ = builderForValue.build();
          onChanged();
        } else {
          releaseResponseBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public Builder mergeReleaseResponse(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse value) {
        if (releaseResponseBuilder_ == null) {
          if (releaseResponse_ != null) {
            releaseResponse_ =
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.newBuilder(releaseResponse_).mergeFrom(value).buildPartial();
          } else {
            releaseResponse_ = value;
          }
          onChanged();
        } else {
          releaseResponseBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public Builder clearReleaseResponse() {
        if (releaseResponseBuilder_ == null) {
          releaseResponse_ = null;
          onChanged();
        } else {
          releaseResponse_ = null;
          releaseResponseBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder getReleaseResponseBuilder() {
        
        onChanged();
        return getReleaseResponseFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder getReleaseResponseOrBuilder() {
        if (releaseResponseBuilder_ != null) {
          return releaseResponseBuilder_.getMessageOrBuilder();
        } else {
          return releaseResponse_ == null ?
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.getDefaultInstance() : releaseResponse_;
        }
      }
      /**
       * <code>optional .js.JsReleaseResponse releaseResponse = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder> 
          getReleaseResponseFieldBuilder() {
        if (releaseResponseBuilder_ == null) {
          releaseResponseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder>(
                  getReleaseResponse(),
                  getParentForChildren(),
                  isClean());
          releaseResponse_ = null;
        }
        return releaseResponseBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.RemoteJsResponse)
    }

    // @@protoc_insertion_point(class_scope:js.RemoteJsResponse)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RemoteJsResponse>
        PARSER = new com.google.protobuf.AbstractParser<RemoteJsResponse>() {
      public RemoteJsResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RemoteJsResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RemoteJsResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RemoteJsResponse> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.RemoteJsResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JsCompileRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.JsCompileRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 scriptIdMSB = 1;</code>
     */
    long getScriptIdMSB();

    /**
     * <code>optional int64 scriptIdLSB = 2;</code>
     */
    long getScriptIdLSB();

    /**
     * <code>optional string functionName = 3;</code>
     */
    java.lang.String getFunctionName();
    /**
     * <code>optional string functionName = 3;</code>
     */
    com.google.protobuf.ByteString
        getFunctionNameBytes();

    /**
     * <code>optional string scriptBody = 4;</code>
     */
    java.lang.String getScriptBody();
    /**
     * <code>optional string scriptBody = 4;</code>
     */
    com.google.protobuf.ByteString
        getScriptBodyBytes();
  }
  /**
   * Protobuf type {@code js.JsCompileRequest}
   */
  public  static final class JsCompileRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.JsCompileRequest)
      JsCompileRequestOrBuilder {
    // Use JsCompileRequest.newBuilder() to construct.
    private JsCompileRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JsCompileRequest() {
      scriptIdMSB_ = 0L;
      scriptIdLSB_ = 0L;
      functionName_ = "";
      scriptBody_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private JsCompileRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              scriptIdMSB_ = input.readInt64();
              break;
            }
            case 16: {

              scriptIdLSB_ = input.readInt64();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              functionName_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              scriptBody_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder.class);
    }

    public static final int SCRIPTIDMSB_FIELD_NUMBER = 1;
    private long scriptIdMSB_;
    /**
     * <code>optional int64 scriptIdMSB = 1;</code>
     */
    public long getScriptIdMSB() {
      return scriptIdMSB_;
    }

    public static final int SCRIPTIDLSB_FIELD_NUMBER = 2;
    private long scriptIdLSB_;
    /**
     * <code>optional int64 scriptIdLSB = 2;</code>
     */
    public long getScriptIdLSB() {
      return scriptIdLSB_;
    }

    public static final int FUNCTIONNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object functionName_;
    /**
     * <code>optional string functionName = 3;</code>
     */
    public java.lang.String getFunctionName() {
      java.lang.Object ref = functionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        functionName_ = s;
        return s;
      }
    }
    /**
     * <code>optional string functionName = 3;</code>
     */
    public com.google.protobuf.ByteString
        getFunctionNameBytes() {
      java.lang.Object ref = functionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        functionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SCRIPTBODY_FIELD_NUMBER = 4;
    private volatile java.lang.Object scriptBody_;
    /**
     * <code>optional string scriptBody = 4;</code>
     */
    public java.lang.String getScriptBody() {
      java.lang.Object ref = scriptBody_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scriptBody_ = s;
        return s;
      }
    }
    /**
     * <code>optional string scriptBody = 4;</code>
     */
    public com.google.protobuf.ByteString
        getScriptBodyBytes() {
      java.lang.Object ref = scriptBody_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scriptBody_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (scriptIdMSB_ != 0L) {
        output.writeInt64(1, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        output.writeInt64(2, scriptIdLSB_);
      }
      if (!getFunctionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, functionName_);
      }
      if (!getScriptBodyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, scriptBody_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (scriptIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, scriptIdLSB_);
      }
      if (!getFunctionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, functionName_);
      }
      if (!getScriptBodyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, scriptBody_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest other = (org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest) obj;

      boolean result = true;
      result = result && (getScriptIdMSB()
          == other.getScriptIdMSB());
      result = result && (getScriptIdLSB()
          == other.getScriptIdLSB());
      result = result && getFunctionName()
          .equals(other.getFunctionName());
      result = result && getScriptBody()
          .equals(other.getScriptBody());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SCRIPTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdMSB());
      hash = (37 * hash) + SCRIPTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdLSB());
      hash = (37 * hash) + FUNCTIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getFunctionName().hashCode();
      hash = (37 * hash) + SCRIPTBODY_FIELD_NUMBER;
      hash = (53 * hash) + getScriptBody().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.JsCompileRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.JsCompileRequest)
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        scriptIdMSB_ = 0L;

        scriptIdLSB_ = 0L;

        functionName_ = "";

        scriptBody_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileRequest_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest result = new org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest(this);
        result.scriptIdMSB_ = scriptIdMSB_;
        result.scriptIdLSB_ = scriptIdLSB_;
        result.functionName_ = functionName_;
        result.scriptBody_ = scriptBody_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest.getDefaultInstance()) return this;
        if (other.getScriptIdMSB() != 0L) {
          setScriptIdMSB(other.getScriptIdMSB());
        }
        if (other.getScriptIdLSB() != 0L) {
          setScriptIdLSB(other.getScriptIdLSB());
        }
        if (!other.getFunctionName().isEmpty()) {
          functionName_ = other.functionName_;
          onChanged();
        }
        if (!other.getScriptBody().isEmpty()) {
          scriptBody_ = other.scriptBody_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long scriptIdMSB_ ;
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public long getScriptIdMSB() {
        return scriptIdMSB_;
      }
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public Builder setScriptIdMSB(long value) {
        
        scriptIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public Builder clearScriptIdMSB() {
        
        scriptIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long scriptIdLSB_ ;
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public long getScriptIdLSB() {
        return scriptIdLSB_;
      }
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public Builder setScriptIdLSB(long value) {
        
        scriptIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public Builder clearScriptIdLSB() {
        
        scriptIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object functionName_ = "";
      /**
       * <code>optional string functionName = 3;</code>
       */
      public java.lang.String getFunctionName() {
        java.lang.Object ref = functionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          functionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public com.google.protobuf.ByteString
          getFunctionNameBytes() {
        java.lang.Object ref = functionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          functionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder setFunctionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        functionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder clearFunctionName() {
        
        functionName_ = getDefaultInstance().getFunctionName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder setFunctionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        functionName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object scriptBody_ = "";
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public java.lang.String getScriptBody() {
        java.lang.Object ref = scriptBody_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          scriptBody_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public com.google.protobuf.ByteString
          getScriptBodyBytes() {
        java.lang.Object ref = scriptBody_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          scriptBody_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public Builder setScriptBody(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        scriptBody_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public Builder clearScriptBody() {
        
        scriptBody_ = getDefaultInstance().getScriptBody();
        onChanged();
        return this;
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public Builder setScriptBodyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        scriptBody_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.JsCompileRequest)
    }

    // @@protoc_insertion_point(class_scope:js.JsCompileRequest)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<JsCompileRequest>
        PARSER = new com.google.protobuf.AbstractParser<JsCompileRequest>() {
      public JsCompileRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new JsCompileRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JsCompileRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JsCompileRequest> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JsReleaseRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.JsReleaseRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 scriptIdMSB = 1;</code>
     */
    long getScriptIdMSB();

    /**
     * <code>optional int64 scriptIdLSB = 2;</code>
     */
    long getScriptIdLSB();

    /**
     * <code>optional string functionName = 3;</code>
     */
    java.lang.String getFunctionName();
    /**
     * <code>optional string functionName = 3;</code>
     */
    com.google.protobuf.ByteString
        getFunctionNameBytes();
  }
  /**
   * Protobuf type {@code js.JsReleaseRequest}
   */
  public  static final class JsReleaseRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.JsReleaseRequest)
      JsReleaseRequestOrBuilder {
    // Use JsReleaseRequest.newBuilder() to construct.
    private JsReleaseRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JsReleaseRequest() {
      scriptIdMSB_ = 0L;
      scriptIdLSB_ = 0L;
      functionName_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private JsReleaseRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              scriptIdMSB_ = input.readInt64();
              break;
            }
            case 16: {

              scriptIdLSB_ = input.readInt64();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              functionName_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder.class);
    }

    public static final int SCRIPTIDMSB_FIELD_NUMBER = 1;
    private long scriptIdMSB_;
    /**
     * <code>optional int64 scriptIdMSB = 1;</code>
     */
    public long getScriptIdMSB() {
      return scriptIdMSB_;
    }

    public static final int SCRIPTIDLSB_FIELD_NUMBER = 2;
    private long scriptIdLSB_;
    /**
     * <code>optional int64 scriptIdLSB = 2;</code>
     */
    public long getScriptIdLSB() {
      return scriptIdLSB_;
    }

    public static final int FUNCTIONNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object functionName_;
    /**
     * <code>optional string functionName = 3;</code>
     */
    public java.lang.String getFunctionName() {
      java.lang.Object ref = functionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        functionName_ = s;
        return s;
      }
    }
    /**
     * <code>optional string functionName = 3;</code>
     */
    public com.google.protobuf.ByteString
        getFunctionNameBytes() {
      java.lang.Object ref = functionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        functionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (scriptIdMSB_ != 0L) {
        output.writeInt64(1, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        output.writeInt64(2, scriptIdLSB_);
      }
      if (!getFunctionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, functionName_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (scriptIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, scriptIdLSB_);
      }
      if (!getFunctionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, functionName_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest other = (org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest) obj;

      boolean result = true;
      result = result && (getScriptIdMSB()
          == other.getScriptIdMSB());
      result = result && (getScriptIdLSB()
          == other.getScriptIdLSB());
      result = result && getFunctionName()
          .equals(other.getFunctionName());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SCRIPTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdMSB());
      hash = (37 * hash) + SCRIPTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdLSB());
      hash = (37 * hash) + FUNCTIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getFunctionName().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.JsReleaseRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.JsReleaseRequest)
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        scriptIdMSB_ = 0L;

        scriptIdLSB_ = 0L;

        functionName_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseRequest_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest result = new org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest(this);
        result.scriptIdMSB_ = scriptIdMSB_;
        result.scriptIdLSB_ = scriptIdLSB_;
        result.functionName_ = functionName_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest.getDefaultInstance()) return this;
        if (other.getScriptIdMSB() != 0L) {
          setScriptIdMSB(other.getScriptIdMSB());
        }
        if (other.getScriptIdLSB() != 0L) {
          setScriptIdLSB(other.getScriptIdLSB());
        }
        if (!other.getFunctionName().isEmpty()) {
          functionName_ = other.functionName_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long scriptIdMSB_ ;
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public long getScriptIdMSB() {
        return scriptIdMSB_;
      }
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public Builder setScriptIdMSB(long value) {
        
        scriptIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public Builder clearScriptIdMSB() {
        
        scriptIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long scriptIdLSB_ ;
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public long getScriptIdLSB() {
        return scriptIdLSB_;
      }
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public Builder setScriptIdLSB(long value) {
        
        scriptIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public Builder clearScriptIdLSB() {
        
        scriptIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object functionName_ = "";
      /**
       * <code>optional string functionName = 3;</code>
       */
      public java.lang.String getFunctionName() {
        java.lang.Object ref = functionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          functionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public com.google.protobuf.ByteString
          getFunctionNameBytes() {
        java.lang.Object ref = functionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          functionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder setFunctionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        functionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder clearFunctionName() {
        
        functionName_ = getDefaultInstance().getFunctionName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder setFunctionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        functionName_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.JsReleaseRequest)
    }

    // @@protoc_insertion_point(class_scope:js.JsReleaseRequest)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<JsReleaseRequest>
        PARSER = new com.google.protobuf.AbstractParser<JsReleaseRequest>() {
      public JsReleaseRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new JsReleaseRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JsReleaseRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JsReleaseRequest> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JsReleaseResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.JsReleaseResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool success = 1;</code>
     */
    boolean getSuccess();

    /**
     * <code>optional int64 scriptIdMSB = 2;</code>
     */
    long getScriptIdMSB();

    /**
     * <code>optional int64 scriptIdLSB = 3;</code>
     */
    long getScriptIdLSB();
  }
  /**
   * Protobuf type {@code js.JsReleaseResponse}
   */
  public  static final class JsReleaseResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.JsReleaseResponse)
      JsReleaseResponseOrBuilder {
    // Use JsReleaseResponse.newBuilder() to construct.
    private JsReleaseResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JsReleaseResponse() {
      success_ = false;
      scriptIdMSB_ = 0L;
      scriptIdLSB_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private JsReleaseResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              success_ = input.readBool();
              break;
            }
            case 16: {

              scriptIdMSB_ = input.readInt64();
              break;
            }
            case 24: {

              scriptIdLSB_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder.class);
    }

    public static final int SUCCESS_FIELD_NUMBER = 1;
    private boolean success_;
    /**
     * <code>optional bool success = 1;</code>
     */
    public boolean getSuccess() {
      return success_;
    }

    public static final int SCRIPTIDMSB_FIELD_NUMBER = 2;
    private long scriptIdMSB_;
    /**
     * <code>optional int64 scriptIdMSB = 2;</code>
     */
    public long getScriptIdMSB() {
      return scriptIdMSB_;
    }

    public static final int SCRIPTIDLSB_FIELD_NUMBER = 3;
    private long scriptIdLSB_;
    /**
     * <code>optional int64 scriptIdLSB = 3;</code>
     */
    public long getScriptIdLSB() {
      return scriptIdLSB_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (success_ != false) {
        output.writeBool(1, success_);
      }
      if (scriptIdMSB_ != 0L) {
        output.writeInt64(2, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        output.writeInt64(3, scriptIdLSB_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (success_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, success_);
      }
      if (scriptIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, scriptIdLSB_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse other = (org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse) obj;

      boolean result = true;
      result = result && (getSuccess()
          == other.getSuccess());
      result = result && (getScriptIdMSB()
          == other.getScriptIdMSB());
      result = result && (getScriptIdLSB()
          == other.getScriptIdLSB());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuccess());
      hash = (37 * hash) + SCRIPTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdMSB());
      hash = (37 * hash) + SCRIPTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdLSB());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.JsReleaseResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.JsReleaseResponse)
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        success_ = false;

        scriptIdMSB_ = 0L;

        scriptIdLSB_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsReleaseResponse_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse result = new org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse(this);
        result.success_ = success_;
        result.scriptIdMSB_ = scriptIdMSB_;
        result.scriptIdLSB_ = scriptIdLSB_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse.getDefaultInstance()) return this;
        if (other.getSuccess() != false) {
          setSuccess(other.getSuccess());
        }
        if (other.getScriptIdMSB() != 0L) {
          setScriptIdMSB(other.getScriptIdMSB());
        }
        if (other.getScriptIdLSB() != 0L) {
          setScriptIdLSB(other.getScriptIdLSB());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private boolean success_ ;
      /**
       * <code>optional bool success = 1;</code>
       */
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <code>optional bool success = 1;</code>
       */
      public Builder setSuccess(boolean value) {
        
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool success = 1;</code>
       */
      public Builder clearSuccess() {
        
        success_ = false;
        onChanged();
        return this;
      }

      private long scriptIdMSB_ ;
      /**
       * <code>optional int64 scriptIdMSB = 2;</code>
       */
      public long getScriptIdMSB() {
        return scriptIdMSB_;
      }
      /**
       * <code>optional int64 scriptIdMSB = 2;</code>
       */
      public Builder setScriptIdMSB(long value) {
        
        scriptIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdMSB = 2;</code>
       */
      public Builder clearScriptIdMSB() {
        
        scriptIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long scriptIdLSB_ ;
      /**
       * <code>optional int64 scriptIdLSB = 3;</code>
       */
      public long getScriptIdLSB() {
        return scriptIdLSB_;
      }
      /**
       * <code>optional int64 scriptIdLSB = 3;</code>
       */
      public Builder setScriptIdLSB(long value) {
        
        scriptIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdLSB = 3;</code>
       */
      public Builder clearScriptIdLSB() {
        
        scriptIdLSB_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.JsReleaseResponse)
    }

    // @@protoc_insertion_point(class_scope:js.JsReleaseResponse)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<JsReleaseResponse>
        PARSER = new com.google.protobuf.AbstractParser<JsReleaseResponse>() {
      public JsReleaseResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new JsReleaseResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JsReleaseResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JsReleaseResponse> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.JsReleaseResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JsCompileResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.JsCompileResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool success = 1;</code>
     */
    boolean getSuccess();

    /**
     * <code>optional int64 scriptIdMSB = 2;</code>
     */
    long getScriptIdMSB();

    /**
     * <code>optional int64 scriptIdLSB = 3;</code>
     */
    long getScriptIdLSB();

    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
     */
    int getErrorCodeValue();
    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode getErrorCode();

    /**
     * <code>optional string errorDetails = 5;</code>
     */
    java.lang.String getErrorDetails();
    /**
     * <code>optional string errorDetails = 5;</code>
     */
    com.google.protobuf.ByteString
        getErrorDetailsBytes();
  }
  /**
   * Protobuf type {@code js.JsCompileResponse}
   */
  public  static final class JsCompileResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.JsCompileResponse)
      JsCompileResponseOrBuilder {
    // Use JsCompileResponse.newBuilder() to construct.
    private JsCompileResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JsCompileResponse() {
      success_ = false;
      scriptIdMSB_ = 0L;
      scriptIdLSB_ = 0L;
      errorCode_ = 0;
      errorDetails_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private JsCompileResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              success_ = input.readBool();
              break;
            }
            case 16: {

              scriptIdMSB_ = input.readInt64();
              break;
            }
            case 24: {

              scriptIdLSB_ = input.readInt64();
              break;
            }
            case 32: {
              int rawValue = input.readEnum();

              errorCode_ = rawValue;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              errorDetails_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder.class);
    }

    public static final int SUCCESS_FIELD_NUMBER = 1;
    private boolean success_;
    /**
     * <code>optional bool success = 1;</code>
     */
    public boolean getSuccess() {
      return success_;
    }

    public static final int SCRIPTIDMSB_FIELD_NUMBER = 2;
    private long scriptIdMSB_;
    /**
     * <code>optional int64 scriptIdMSB = 2;</code>
     */
    public long getScriptIdMSB() {
      return scriptIdMSB_;
    }

    public static final int SCRIPTIDLSB_FIELD_NUMBER = 3;
    private long scriptIdLSB_;
    /**
     * <code>optional int64 scriptIdLSB = 3;</code>
     */
    public long getScriptIdLSB() {
      return scriptIdLSB_;
    }

    public static final int ERRORCODE_FIELD_NUMBER = 4;
    private int errorCode_;
    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
     */
    public int getErrorCodeValue() {
      return errorCode_;
    }
    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode getErrorCode() {
      org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode result = org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.valueOf(errorCode_);
      return result == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.UNRECOGNIZED : result;
    }

    public static final int ERRORDETAILS_FIELD_NUMBER = 5;
    private volatile java.lang.Object errorDetails_;
    /**
     * <code>optional string errorDetails = 5;</code>
     */
    public java.lang.String getErrorDetails() {
      java.lang.Object ref = errorDetails_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorDetails_ = s;
        return s;
      }
    }
    /**
     * <code>optional string errorDetails = 5;</code>
     */
    public com.google.protobuf.ByteString
        getErrorDetailsBytes() {
      java.lang.Object ref = errorDetails_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorDetails_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (success_ != false) {
        output.writeBool(1, success_);
      }
      if (scriptIdMSB_ != 0L) {
        output.writeInt64(2, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        output.writeInt64(3, scriptIdLSB_);
      }
      if (errorCode_ != org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.COMPILATION_ERROR.getNumber()) {
        output.writeEnum(4, errorCode_);
      }
      if (!getErrorDetailsBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, errorDetails_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (success_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, success_);
      }
      if (scriptIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, scriptIdLSB_);
      }
      if (errorCode_ != org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.COMPILATION_ERROR.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, errorCode_);
      }
      if (!getErrorDetailsBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, errorDetails_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse other = (org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse) obj;

      boolean result = true;
      result = result && (getSuccess()
          == other.getSuccess());
      result = result && (getScriptIdMSB()
          == other.getScriptIdMSB());
      result = result && (getScriptIdLSB()
          == other.getScriptIdLSB());
      result = result && errorCode_ == other.errorCode_;
      result = result && getErrorDetails()
          .equals(other.getErrorDetails());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuccess());
      hash = (37 * hash) + SCRIPTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdMSB());
      hash = (37 * hash) + SCRIPTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdLSB());
      hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
      hash = (53 * hash) + errorCode_;
      hash = (37 * hash) + ERRORDETAILS_FIELD_NUMBER;
      hash = (53 * hash) + getErrorDetails().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.JsCompileResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.JsCompileResponse)
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        success_ = false;

        scriptIdMSB_ = 0L;

        scriptIdLSB_ = 0L;

        errorCode_ = 0;

        errorDetails_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsCompileResponse_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse result = new org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse(this);
        result.success_ = success_;
        result.scriptIdMSB_ = scriptIdMSB_;
        result.scriptIdLSB_ = scriptIdLSB_;
        result.errorCode_ = errorCode_;
        result.errorDetails_ = errorDetails_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse.getDefaultInstance()) return this;
        if (other.getSuccess() != false) {
          setSuccess(other.getSuccess());
        }
        if (other.getScriptIdMSB() != 0L) {
          setScriptIdMSB(other.getScriptIdMSB());
        }
        if (other.getScriptIdLSB() != 0L) {
          setScriptIdLSB(other.getScriptIdLSB());
        }
        if (other.errorCode_ != 0) {
          setErrorCodeValue(other.getErrorCodeValue());
        }
        if (!other.getErrorDetails().isEmpty()) {
          errorDetails_ = other.errorDetails_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private boolean success_ ;
      /**
       * <code>optional bool success = 1;</code>
       */
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <code>optional bool success = 1;</code>
       */
      public Builder setSuccess(boolean value) {
        
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool success = 1;</code>
       */
      public Builder clearSuccess() {
        
        success_ = false;
        onChanged();
        return this;
      }

      private long scriptIdMSB_ ;
      /**
       * <code>optional int64 scriptIdMSB = 2;</code>
       */
      public long getScriptIdMSB() {
        return scriptIdMSB_;
      }
      /**
       * <code>optional int64 scriptIdMSB = 2;</code>
       */
      public Builder setScriptIdMSB(long value) {
        
        scriptIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdMSB = 2;</code>
       */
      public Builder clearScriptIdMSB() {
        
        scriptIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long scriptIdLSB_ ;
      /**
       * <code>optional int64 scriptIdLSB = 3;</code>
       */
      public long getScriptIdLSB() {
        return scriptIdLSB_;
      }
      /**
       * <code>optional int64 scriptIdLSB = 3;</code>
       */
      public Builder setScriptIdLSB(long value) {
        
        scriptIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdLSB = 3;</code>
       */
      public Builder clearScriptIdLSB() {
        
        scriptIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private int errorCode_ = 0;
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
       */
      public int getErrorCodeValue() {
        return errorCode_;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
       */
      public Builder setErrorCodeValue(int value) {
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode getErrorCode() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode result = org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.valueOf(errorCode_);
        return result == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.UNRECOGNIZED : result;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
       */
      public Builder setErrorCode(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        errorCode_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 4;</code>
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object errorDetails_ = "";
      /**
       * <code>optional string errorDetails = 5;</code>
       */
      public java.lang.String getErrorDetails() {
        java.lang.Object ref = errorDetails_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          errorDetails_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string errorDetails = 5;</code>
       */
      public com.google.protobuf.ByteString
          getErrorDetailsBytes() {
        java.lang.Object ref = errorDetails_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          errorDetails_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string errorDetails = 5;</code>
       */
      public Builder setErrorDetails(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        errorDetails_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string errorDetails = 5;</code>
       */
      public Builder clearErrorDetails() {
        
        errorDetails_ = getDefaultInstance().getErrorDetails();
        onChanged();
        return this;
      }
      /**
       * <code>optional string errorDetails = 5;</code>
       */
      public Builder setErrorDetailsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        errorDetails_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.JsCompileResponse)
    }

    // @@protoc_insertion_point(class_scope:js.JsCompileResponse)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<JsCompileResponse>
        PARSER = new com.google.protobuf.AbstractParser<JsCompileResponse>() {
      public JsCompileResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new JsCompileResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JsCompileResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JsCompileResponse> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.JsCompileResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JsInvokeRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.JsInvokeRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 scriptIdMSB = 1;</code>
     */
    long getScriptIdMSB();

    /**
     * <code>optional int64 scriptIdLSB = 2;</code>
     */
    long getScriptIdLSB();

    /**
     * <code>optional string functionName = 3;</code>
     */
    java.lang.String getFunctionName();
    /**
     * <code>optional string functionName = 3;</code>
     */
    com.google.protobuf.ByteString
        getFunctionNameBytes();

    /**
     * <code>optional string scriptBody = 4;</code>
     */
    java.lang.String getScriptBody();
    /**
     * <code>optional string scriptBody = 4;</code>
     */
    com.google.protobuf.ByteString
        getScriptBodyBytes();

    /**
     * <code>optional int32 timeout = 5;</code>
     */
    int getTimeout();

    /**
     * <code>repeated string args = 6;</code>
     */
    java.util.List<java.lang.String>
        getArgsList();
    /**
     * <code>repeated string args = 6;</code>
     */
    int getArgsCount();
    /**
     * <code>repeated string args = 6;</code>
     */
    java.lang.String getArgs(int index);
    /**
     * <code>repeated string args = 6;</code>
     */
    com.google.protobuf.ByteString
        getArgsBytes(int index);
  }
  /**
   * Protobuf type {@code js.JsInvokeRequest}
   */
  public  static final class JsInvokeRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.JsInvokeRequest)
      JsInvokeRequestOrBuilder {
    // Use JsInvokeRequest.newBuilder() to construct.
    private JsInvokeRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JsInvokeRequest() {
      scriptIdMSB_ = 0L;
      scriptIdLSB_ = 0L;
      functionName_ = "";
      scriptBody_ = "";
      timeout_ = 0;
      args_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private JsInvokeRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              scriptIdMSB_ = input.readInt64();
              break;
            }
            case 16: {

              scriptIdLSB_ = input.readInt64();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              functionName_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              scriptBody_ = s;
              break;
            }
            case 40: {

              timeout_ = input.readInt32();
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
                args_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000020;
              }
              args_.add(s);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
          args_ = args_.getUnmodifiableView();
        }
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder.class);
    }

    private int bitField0_;
    public static final int SCRIPTIDMSB_FIELD_NUMBER = 1;
    private long scriptIdMSB_;
    /**
     * <code>optional int64 scriptIdMSB = 1;</code>
     */
    public long getScriptIdMSB() {
      return scriptIdMSB_;
    }

    public static final int SCRIPTIDLSB_FIELD_NUMBER = 2;
    private long scriptIdLSB_;
    /**
     * <code>optional int64 scriptIdLSB = 2;</code>
     */
    public long getScriptIdLSB() {
      return scriptIdLSB_;
    }

    public static final int FUNCTIONNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object functionName_;
    /**
     * <code>optional string functionName = 3;</code>
     */
    public java.lang.String getFunctionName() {
      java.lang.Object ref = functionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        functionName_ = s;
        return s;
      }
    }
    /**
     * <code>optional string functionName = 3;</code>
     */
    public com.google.protobuf.ByteString
        getFunctionNameBytes() {
      java.lang.Object ref = functionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        functionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SCRIPTBODY_FIELD_NUMBER = 4;
    private volatile java.lang.Object scriptBody_;
    /**
     * <code>optional string scriptBody = 4;</code>
     */
    public java.lang.String getScriptBody() {
      java.lang.Object ref = scriptBody_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scriptBody_ = s;
        return s;
      }
    }
    /**
     * <code>optional string scriptBody = 4;</code>
     */
    public com.google.protobuf.ByteString
        getScriptBodyBytes() {
      java.lang.Object ref = scriptBody_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scriptBody_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMEOUT_FIELD_NUMBER = 5;
    private int timeout_;
    /**
     * <code>optional int32 timeout = 5;</code>
     */
    public int getTimeout() {
      return timeout_;
    }

    public static final int ARGS_FIELD_NUMBER = 6;
    private com.google.protobuf.LazyStringList args_;
    /**
     * <code>repeated string args = 6;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getArgsList() {
      return args_;
    }
    /**
     * <code>repeated string args = 6;</code>
     */
    public int getArgsCount() {
      return args_.size();
    }
    /**
     * <code>repeated string args = 6;</code>
     */
    public java.lang.String getArgs(int index) {
      return args_.get(index);
    }
    /**
     * <code>repeated string args = 6;</code>
     */
    public com.google.protobuf.ByteString
        getArgsBytes(int index) {
      return args_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (scriptIdMSB_ != 0L) {
        output.writeInt64(1, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        output.writeInt64(2, scriptIdLSB_);
      }
      if (!getFunctionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, functionName_);
      }
      if (!getScriptBodyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, scriptBody_);
      }
      if (timeout_ != 0) {
        output.writeInt32(5, timeout_);
      }
      for (int i = 0; i < args_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, args_.getRaw(i));
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (scriptIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, scriptIdMSB_);
      }
      if (scriptIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, scriptIdLSB_);
      }
      if (!getFunctionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, functionName_);
      }
      if (!getScriptBodyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, scriptBody_);
      }
      if (timeout_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, timeout_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < args_.size(); i++) {
          dataSize += computeStringSizeNoTag(args_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getArgsList().size();
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest other = (org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest) obj;

      boolean result = true;
      result = result && (getScriptIdMSB()
          == other.getScriptIdMSB());
      result = result && (getScriptIdLSB()
          == other.getScriptIdLSB());
      result = result && getFunctionName()
          .equals(other.getFunctionName());
      result = result && getScriptBody()
          .equals(other.getScriptBody());
      result = result && (getTimeout()
          == other.getTimeout());
      result = result && getArgsList()
          .equals(other.getArgsList());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SCRIPTIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdMSB());
      hash = (37 * hash) + SCRIPTIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScriptIdLSB());
      hash = (37 * hash) + FUNCTIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getFunctionName().hashCode();
      hash = (37 * hash) + SCRIPTBODY_FIELD_NUMBER;
      hash = (53 * hash) + getScriptBody().hashCode();
      hash = (37 * hash) + TIMEOUT_FIELD_NUMBER;
      hash = (53 * hash) + getTimeout();
      if (getArgsCount() > 0) {
        hash = (37 * hash) + ARGS_FIELD_NUMBER;
        hash = (53 * hash) + getArgsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.JsInvokeRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.JsInvokeRequest)
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        scriptIdMSB_ = 0L;

        scriptIdLSB_ = 0L;

        functionName_ = "";

        scriptBody_ = "";

        timeout_ = 0;

        args_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeRequest_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest result = new org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.scriptIdMSB_ = scriptIdMSB_;
        result.scriptIdLSB_ = scriptIdLSB_;
        result.functionName_ = functionName_;
        result.scriptBody_ = scriptBody_;
        result.timeout_ = timeout_;
        if (((bitField0_ & 0x00000020) == 0x00000020)) {
          args_ = args_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.args_ = args_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest.getDefaultInstance()) return this;
        if (other.getScriptIdMSB() != 0L) {
          setScriptIdMSB(other.getScriptIdMSB());
        }
        if (other.getScriptIdLSB() != 0L) {
          setScriptIdLSB(other.getScriptIdLSB());
        }
        if (!other.getFunctionName().isEmpty()) {
          functionName_ = other.functionName_;
          onChanged();
        }
        if (!other.getScriptBody().isEmpty()) {
          scriptBody_ = other.scriptBody_;
          onChanged();
        }
        if (other.getTimeout() != 0) {
          setTimeout(other.getTimeout());
        }
        if (!other.args_.isEmpty()) {
          if (args_.isEmpty()) {
            args_ = other.args_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureArgsIsMutable();
            args_.addAll(other.args_);
          }
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long scriptIdMSB_ ;
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public long getScriptIdMSB() {
        return scriptIdMSB_;
      }
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public Builder setScriptIdMSB(long value) {
        
        scriptIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdMSB = 1;</code>
       */
      public Builder clearScriptIdMSB() {
        
        scriptIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long scriptIdLSB_ ;
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public long getScriptIdLSB() {
        return scriptIdLSB_;
      }
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public Builder setScriptIdLSB(long value) {
        
        scriptIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 scriptIdLSB = 2;</code>
       */
      public Builder clearScriptIdLSB() {
        
        scriptIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object functionName_ = "";
      /**
       * <code>optional string functionName = 3;</code>
       */
      public java.lang.String getFunctionName() {
        java.lang.Object ref = functionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          functionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public com.google.protobuf.ByteString
          getFunctionNameBytes() {
        java.lang.Object ref = functionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          functionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder setFunctionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        functionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder clearFunctionName() {
        
        functionName_ = getDefaultInstance().getFunctionName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string functionName = 3;</code>
       */
      public Builder setFunctionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        functionName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object scriptBody_ = "";
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public java.lang.String getScriptBody() {
        java.lang.Object ref = scriptBody_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          scriptBody_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public com.google.protobuf.ByteString
          getScriptBodyBytes() {
        java.lang.Object ref = scriptBody_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          scriptBody_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public Builder setScriptBody(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        scriptBody_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public Builder clearScriptBody() {
        
        scriptBody_ = getDefaultInstance().getScriptBody();
        onChanged();
        return this;
      }
      /**
       * <code>optional string scriptBody = 4;</code>
       */
      public Builder setScriptBodyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        scriptBody_ = value;
        onChanged();
        return this;
      }

      private int timeout_ ;
      /**
       * <code>optional int32 timeout = 5;</code>
       */
      public int getTimeout() {
        return timeout_;
      }
      /**
       * <code>optional int32 timeout = 5;</code>
       */
      public Builder setTimeout(int value) {
        
        timeout_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 timeout = 5;</code>
       */
      public Builder clearTimeout() {
        
        timeout_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList args_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureArgsIsMutable() {
        if (!((bitField0_ & 0x00000020) == 0x00000020)) {
          args_ = new com.google.protobuf.LazyStringArrayList(args_);
          bitField0_ |= 0x00000020;
         }
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getArgsList() {
        return args_.getUnmodifiableView();
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public int getArgsCount() {
        return args_.size();
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public java.lang.String getArgs(int index) {
        return args_.get(index);
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public com.google.protobuf.ByteString
          getArgsBytes(int index) {
        return args_.getByteString(index);
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public Builder setArgs(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureArgsIsMutable();
        args_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public Builder addArgs(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureArgsIsMutable();
        args_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public Builder addAllArgs(
          java.lang.Iterable<java.lang.String> values) {
        ensureArgsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, args_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public Builder clearArgs() {
        args_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string args = 6;</code>
       */
      public Builder addArgsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureArgsIsMutable();
        args_.add(value);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.JsInvokeRequest)
    }

    // @@protoc_insertion_point(class_scope:js.JsInvokeRequest)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<JsInvokeRequest>
        PARSER = new com.google.protobuf.AbstractParser<JsInvokeRequest>() {
      public JsInvokeRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new JsInvokeRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JsInvokeRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JsInvokeRequest> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JsInvokeResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:js.JsInvokeResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool success = 1;</code>
     */
    boolean getSuccess();

    /**
     * <code>optional string result = 2;</code>
     */
    java.lang.String getResult();
    /**
     * <code>optional string result = 2;</code>
     */
    com.google.protobuf.ByteString
        getResultBytes();

    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
     */
    int getErrorCodeValue();
    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
     */
    org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode getErrorCode();

    /**
     * <code>optional string errorDetails = 4;</code>
     */
    java.lang.String getErrorDetails();
    /**
     * <code>optional string errorDetails = 4;</code>
     */
    com.google.protobuf.ByteString
        getErrorDetailsBytes();
  }
  /**
   * Protobuf type {@code js.JsInvokeResponse}
   */
  public  static final class JsInvokeResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:js.JsInvokeResponse)
      JsInvokeResponseOrBuilder {
    // Use JsInvokeResponse.newBuilder() to construct.
    private JsInvokeResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JsInvokeResponse() {
      success_ = false;
      result_ = "";
      errorCode_ = 0;
      errorDetails_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private JsInvokeResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              success_ = input.readBool();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              result_ = s;
              break;
            }
            case 24: {
              int rawValue = input.readEnum();

              errorCode_ = rawValue;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              errorDetails_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder.class);
    }

    public static final int SUCCESS_FIELD_NUMBER = 1;
    private boolean success_;
    /**
     * <code>optional bool success = 1;</code>
     */
    public boolean getSuccess() {
      return success_;
    }

    public static final int RESULT_FIELD_NUMBER = 2;
    private volatile java.lang.Object result_;
    /**
     * <code>optional string result = 2;</code>
     */
    public java.lang.String getResult() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        result_ = s;
        return s;
      }
    }
    /**
     * <code>optional string result = 2;</code>
     */
    public com.google.protobuf.ByteString
        getResultBytes() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ERRORCODE_FIELD_NUMBER = 3;
    private int errorCode_;
    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
     */
    public int getErrorCodeValue() {
      return errorCode_;
    }
    /**
     * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
     */
    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode getErrorCode() {
      org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode result = org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.valueOf(errorCode_);
      return result == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.UNRECOGNIZED : result;
    }

    public static final int ERRORDETAILS_FIELD_NUMBER = 4;
    private volatile java.lang.Object errorDetails_;
    /**
     * <code>optional string errorDetails = 4;</code>
     */
    public java.lang.String getErrorDetails() {
      java.lang.Object ref = errorDetails_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorDetails_ = s;
        return s;
      }
    }
    /**
     * <code>optional string errorDetails = 4;</code>
     */
    public com.google.protobuf.ByteString
        getErrorDetailsBytes() {
      java.lang.Object ref = errorDetails_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorDetails_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (success_ != false) {
        output.writeBool(1, success_);
      }
      if (!getResultBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, result_);
      }
      if (errorCode_ != org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.COMPILATION_ERROR.getNumber()) {
        output.writeEnum(3, errorCode_);
      }
      if (!getErrorDetailsBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, errorDetails_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (success_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, success_);
      }
      if (!getResultBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, result_);
      }
      if (errorCode_ != org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.COMPILATION_ERROR.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, errorCode_);
      }
      if (!getErrorDetailsBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, errorDetails_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse)) {
        return super.equals(obj);
      }
      org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse other = (org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse) obj;

      boolean result = true;
      result = result && (getSuccess()
          == other.getSuccess());
      result = result && getResult()
          .equals(other.getResult());
      result = result && errorCode_ == other.errorCode_;
      result = result && getErrorDetails()
          .equals(other.getErrorDetails());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuccess());
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult().hashCode();
      hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
      hash = (53 * hash) + errorCode_;
      hash = (37 * hash) + ERRORDETAILS_FIELD_NUMBER;
      hash = (53 * hash) + getErrorDetails().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code js.JsInvokeResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:js.JsInvokeResponse)
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.class, org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.Builder.class);
      }

      // Construct using org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        success_ = false;

        result_ = "";

        errorCode_ = 0;

        errorDetails_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.internal_static_js_JsInvokeResponse_descriptor;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse getDefaultInstanceForType() {
        return org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.getDefaultInstance();
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse build() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse buildPartial() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse result = new org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse(this);
        result.success_ = success_;
        result.result_ = result_;
        result.errorCode_ = errorCode_;
        result.errorDetails_ = errorDetails_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse) {
          return mergeFrom((org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse other) {
        if (other == org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse.getDefaultInstance()) return this;
        if (other.getSuccess() != false) {
          setSuccess(other.getSuccess());
        }
        if (!other.getResult().isEmpty()) {
          result_ = other.result_;
          onChanged();
        }
        if (other.errorCode_ != 0) {
          setErrorCodeValue(other.getErrorCodeValue());
        }
        if (!other.getErrorDetails().isEmpty()) {
          errorDetails_ = other.errorDetails_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private boolean success_ ;
      /**
       * <code>optional bool success = 1;</code>
       */
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <code>optional bool success = 1;</code>
       */
      public Builder setSuccess(boolean value) {
        
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool success = 1;</code>
       */
      public Builder clearSuccess() {
        
        success_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object result_ = "";
      /**
       * <code>optional string result = 2;</code>
       */
      public java.lang.String getResult() {
        java.lang.Object ref = result_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          result_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string result = 2;</code>
       */
      public com.google.protobuf.ByteString
          getResultBytes() {
        java.lang.Object ref = result_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          result_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string result = 2;</code>
       */
      public Builder setResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 2;</code>
       */
      public Builder clearResult() {
        
        result_ = getDefaultInstance().getResult();
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 2;</code>
       */
      public Builder setResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        result_ = value;
        onChanged();
        return this;
      }

      private int errorCode_ = 0;
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
       */
      public int getErrorCodeValue() {
        return errorCode_;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
       */
      public Builder setErrorCodeValue(int value) {
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
       */
      public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode getErrorCode() {
        org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode result = org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.valueOf(errorCode_);
        return result == null ? org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode.UNRECOGNIZED : result;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
       */
      public Builder setErrorCode(org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeErrorCode value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        errorCode_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .js.JsInvokeErrorCode errorCode = 3;</code>
       */
      public Builder clearErrorCode() {
        
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object errorDetails_ = "";
      /**
       * <code>optional string errorDetails = 4;</code>
       */
      public java.lang.String getErrorDetails() {
        java.lang.Object ref = errorDetails_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          errorDetails_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string errorDetails = 4;</code>
       */
      public com.google.protobuf.ByteString
          getErrorDetailsBytes() {
        java.lang.Object ref = errorDetails_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          errorDetails_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string errorDetails = 4;</code>
       */
      public Builder setErrorDetails(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        errorDetails_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string errorDetails = 4;</code>
       */
      public Builder clearErrorDetails() {
        
        errorDetails_ = getDefaultInstance().getErrorDetails();
        onChanged();
        return this;
      }
      /**
       * <code>optional string errorDetails = 4;</code>
       */
      public Builder setErrorDetailsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        errorDetails_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:js.JsInvokeResponse)
    }

    // @@protoc_insertion_point(class_scope:js.JsInvokeResponse)
    private static final org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse();
    }

    public static org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<JsInvokeResponse>
        PARSER = new com.google.protobuf.AbstractParser<JsInvokeResponse>() {
      public JsInvokeResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new JsInvokeResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JsInvokeResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JsInvokeResponse> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.gen.js.JsInvokeProtos.JsInvokeResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_RemoteJsRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_RemoteJsRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_RemoteJsResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_RemoteJsResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_JsCompileRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_JsCompileRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_JsReleaseRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_JsReleaseRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_JsReleaseResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_JsReleaseResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_JsCompileResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_JsCompileResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_JsInvokeRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_JsInvokeRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_js_JsInvokeResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_js_JsInvokeResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016jsinvoke.proto\022\002js\"\334\001\n\017RemoteJsRequest" +
      "\022\025\n\rresponseTopic\030\001 \001(\t\022\024\n\014requestIdMSB\030" +
      "\002 \001(\003\022\024\n\014requestIdLSB\030\003 \001(\003\022,\n\016compileRe" +
      "quest\030\004 \001(\0132\024.js.JsCompileRequest\022*\n\rinv" +
      "okeRequest\030\005 \001(\0132\023.js.JsInvokeRequest\022,\n" +
      "\016releaseRequest\030\006 \001(\0132\024.js.JsReleaseRequ" +
      "est\"\314\001\n\020RemoteJsResponse\022\024\n\014requestIdMSB" +
      "\030\001 \001(\003\022\024\n\014requestIdLSB\030\002 \001(\003\022.\n\017compileR" +
      "esponse\030\003 \001(\0132\025.js.JsCompileResponse\022,\n\016" +
      "invokeResponse\030\004 \001(\0132\024.js.JsInvokeRespon",
      "se\022.\n\017releaseResponse\030\005 \001(\0132\025.js.JsRelea" +
      "seResponse\"f\n\020JsCompileRequest\022\023\n\013script" +
      "IdMSB\030\001 \001(\003\022\023\n\013scriptIdLSB\030\002 \001(\003\022\024\n\014func" +
      "tionName\030\003 \001(\t\022\022\n\nscriptBody\030\004 \001(\t\"R\n\020Js" +
      "ReleaseRequest\022\023\n\013scriptIdMSB\030\001 \001(\003\022\023\n\013s" +
      "criptIdLSB\030\002 \001(\003\022\024\n\014functionName\030\003 \001(\t\"N" +
      "\n\021JsReleaseResponse\022\017\n\007success\030\001 \001(\010\022\023\n\013" +
      "scriptIdMSB\030\002 \001(\003\022\023\n\013scriptIdLSB\030\003 \001(\003\"\216" +
      "\001\n\021JsCompileResponse\022\017\n\007success\030\001 \001(\010\022\023\n" +
      "\013scriptIdMSB\030\002 \001(\003\022\023\n\013scriptIdLSB\030\003 \001(\003\022",
      "(\n\terrorCode\030\004 \001(\0162\025.js.JsInvokeErrorCod" +
      "e\022\024\n\014errorDetails\030\005 \001(\t\"\204\001\n\017JsInvokeRequ" +
      "est\022\023\n\013scriptIdMSB\030\001 \001(\003\022\023\n\013scriptIdLSB\030" +
      "\002 \001(\003\022\024\n\014functionName\030\003 \001(\t\022\022\n\nscriptBod" +
      "y\030\004 \001(\t\022\017\n\007timeout\030\005 \001(\005\022\014\n\004args\030\006 \003(\t\"s" +
      "\n\020JsInvokeResponse\022\017\n\007success\030\001 \001(\010\022\016\n\006r" +
      "esult\030\002 \001(\t\022(\n\terrorCode\030\003 \001(\0162\025.js.JsIn" +
      "vokeErrorCode\022\024\n\014errorDetails\030\004 \001(\t*P\n\021J" +
      "sInvokeErrorCode\022\025\n\021COMPILATION_ERROR\020\000\022" +
      "\021\n\rRUNTIME_ERROR\020\001\022\021\n\rTIMEOUT_ERROR\020\002B/\n",
      "\035org.thingsboard.server.gen.jsB\016JsInvoke" +
      "Protosb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_js_RemoteJsRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_js_RemoteJsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_RemoteJsRequest_descriptor,
        new java.lang.String[] { "ResponseTopic", "RequestIdMSB", "RequestIdLSB", "CompileRequest", "InvokeRequest", "ReleaseRequest", });
    internal_static_js_RemoteJsResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_js_RemoteJsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_RemoteJsResponse_descriptor,
        new java.lang.String[] { "RequestIdMSB", "RequestIdLSB", "CompileResponse", "InvokeResponse", "ReleaseResponse", });
    internal_static_js_JsCompileRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_js_JsCompileRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_JsCompileRequest_descriptor,
        new java.lang.String[] { "ScriptIdMSB", "ScriptIdLSB", "FunctionName", "ScriptBody", });
    internal_static_js_JsReleaseRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_js_JsReleaseRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_JsReleaseRequest_descriptor,
        new java.lang.String[] { "ScriptIdMSB", "ScriptIdLSB", "FunctionName", });
    internal_static_js_JsReleaseResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_js_JsReleaseResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_JsReleaseResponse_descriptor,
        new java.lang.String[] { "Success", "ScriptIdMSB", "ScriptIdLSB", });
    internal_static_js_JsCompileResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_js_JsCompileResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_JsCompileResponse_descriptor,
        new java.lang.String[] { "Success", "ScriptIdMSB", "ScriptIdLSB", "ErrorCode", "ErrorDetails", });
    internal_static_js_JsInvokeRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_js_JsInvokeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_JsInvokeRequest_descriptor,
        new java.lang.String[] { "ScriptIdMSB", "ScriptIdLSB", "FunctionName", "ScriptBody", "Timeout", "Args", });
    internal_static_js_JsInvokeResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_js_JsInvokeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_js_JsInvokeResponse_descriptor,
        new java.lang.String[] { "Success", "Result", "ErrorCode", "ErrorDetails", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
