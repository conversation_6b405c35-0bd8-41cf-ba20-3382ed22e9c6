<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.gis.GisLabelMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.gis.GisLabel">
        SELECT
            a.*
        FROM
            tb_gis_label a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.name != null and param.name != ''">
                AND a.name LIKE '%' || #{param.name} || '%'
            </if>
            <if test="param.beginTime != null">
                AND a.createtime <![CDATA[ >= ]]> #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.createtime <![CDATA[ <= ]]> #{param.endTime}
            </if>
            <if test="param.createuser != null and param.createuser != ''">
                AND a.createuser = #{param.createuser}
            </if>
        </where>
        ORDER BY a.createtime DESC
    </select>

</mapper>