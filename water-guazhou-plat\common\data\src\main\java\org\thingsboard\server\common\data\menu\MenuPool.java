/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.menu;

import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.id.MenuPoolId;

@EqualsAndHashCode(callSuper = true)
public class MenuPool extends BaseData<MenuPoolId> {

    private MenuPoolId parentId;
    private String defaultName;
    private Integer type;
    private Integer orderNum;
    private String params;
    private Integer status;
    private Integer flagDelete;
    private String additionalInfo;
    private String icon;

    public MenuPool(){
        super();
    }

    public MenuPool(MenuPoolId id){
        super(id);
    }

    public MenuPool(MenuPool menuPool) {
        super();
        this.parentId = menuPool.getParentId();
        this.defaultName = menuPool.getDefaultName();
        this.type = menuPool.getType();
        this.orderNum = menuPool.getOrderNum();
        this.params = menuPool.getParams();
        this.status = menuPool.getStatus();
        this.flagDelete = menuPool.getFlagDelete();
        this.additionalInfo = menuPool.getAdditionalInfo();
        this.icon = menuPool.getIcon();
    }


    public String getDefaultName() {
        return defaultName;
    }

    public void setDefaultName(String defaultName) {
        this.defaultName = defaultName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFlagDelete() {
        return flagDelete;
    }

    public void setFlagDelete(Integer flagDelete) {
        this.flagDelete = flagDelete;
    }

    public MenuPoolId getParentId() {
        return parentId;
    }

    public void setParentId(MenuPoolId parentId) {
        this.parentId = parentId;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public String toString() {
        return "MenuPool{" +
                ", defaultName='" + defaultName + '\'' +
                ", type='" + type + '\'' +
                ", orderNum=" + orderNum +
                ", params='" + params + '\'' +
                ", status=" + status +
                ", flagDelete=" + flagDelete +
                '}';
    }
}
