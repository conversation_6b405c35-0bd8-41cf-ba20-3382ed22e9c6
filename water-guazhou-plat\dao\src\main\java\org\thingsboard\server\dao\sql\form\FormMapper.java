package org.thingsboard.server.dao.sql.form;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.Form;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@Mapper
public interface FormMapper extends BaseMapper<Form> {

    List<Form> getList(@Param("name") String name, @Param("tenantId") String tenantId, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("name") String name, @Param("tenantId") String tenantId);
}
