<template>
  <Panel
    ref="refPanel"
    :custom-class="'map-panel'"
    :title="state.curStation?.name"
  >
    <template #left>
      <div class="panel-left">
        <div class="chartbox top">
          <div class="chart-left">
            <VChart :option="chartOptions.TopLeftChart"></VChart>
          </div>
          <div class="chart-right">
            <VChart :option="chartOptions.TopRightChart"></VChart>
          </div>
        </div>
        <div class="attr-box">
          <div class="lefticon yellow">
            <div class="lefticon-text">CL</div>
          </div>
          <span class="name">余氯</span>
          <div class="info">
            <span class="count" :class="state.attrs.remainder_status">{{
              state.attrs.remainder
            }}</span>
            <span class="unit" :class="state.attrs.remainder_status">mg/L</span>
          </div>
        </div>
        <div class="attr-box">
          <div class="lefticon purple">
            <div class="lefticon-text">TUR</div>
          </div>
          <span class="name">浊度</span>
          <div class="info">
            <span class="count" :class="state.attrs.turbidity_status">{{
              state.attrs.turbidity
            }}</span>
            <span class="unit" :class="state.attrs.turbidity_status">NTU</span>
          </div>
        </div>
        <div class="attr-box">
          <div class="lefticon lightblue">
            <div class="lefticon-text">PH</div>
          </div>
          <span class="name">PH</span>
          <div class="info">
            <span class="count" :class="state.attrs.ph_status">{{
              state.attrs.ph
            }}</span>
            <span class="unit" :class="state.attrs.ph_status"></span>
          </div>
        </div>
        <div class="chartbox bottom">
          <VChart :option="chartOptions.BottomChart"></VChart>
        </div>
      </div>
    </template>
    <div class="panel-center">
      <Form ref="refFormTabs" class="tabsForm" :config="FormConfig_Tabs"></Form>
      <div v-if="state.curTab === 'xinxizonglan'" class="tabContent">
        <div class="tab-pane-content ergong_xinxizonglan">
          <div class="left">
            <img :src="state.curStation?.imgs" alt="" />
          </div>
          <div class="right">
            <FormTable :config="TableConfig_xxzl"></FormTable>
          </div>
        </div>
      </div>
      <div
        v-if="state.curTab === 'gongyitu'"
        class="tabContent ergong_gongyiyu"
      >
        <div class="header">
          <div class="gylist">
            <FormItem
              v-model="state.GongYiTuCurImg"
              :config="FormSelectConfig"
            ></FormItem>
          </div>

          <div class="fullscreen">
            <el-icon style="cursor: pointer" @click="handleFullScreen">
              <FullScreen></FullScreen>
            </el-icon>
            <span>全屏</span>
          </div>
        </div>
        <div ref="refGongYiTuFrame" class="content">
          <img
            ref="refGongYiTu"
            width="100%"
            height="100%"
            :src="state.GongYiTuCurImg"
            alt=""
          />
        </div>
      </div>
      <div
        v-if="state.curTab === 'yuanchengkongzhi'"
        class="tabContent ergong_yuanchengkongzhi"
      >
        <div class="left">
          <img width="100%" height="100%" :src="bumpPng" alt="" />
        </div>
        <div class="right">
          <div
            v-for="(item, i) in state.YuanChengKongZhi"
            :key="i"
            class="right-item"
          >
            <div class="top">
              <span class="name" :class="item.status">{{ item.name }}</span>
              <span class="status" :class="item.status">{{
                item.statusName
              }}</span>
            </div>
            <div class="bottom">
              <span class="timetotal"
                >运行时间：{{ item.timetotal || '-' }}</span
              >
              <div class="bottom-switch">
                <FormItem
                  v-model="item.status"
                  :config="FormCheckboxConfig"
                ></FormItem>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="state.curTab === 'shujubaobiao'" class="tabContent">
        <Form ref="refForm_SJBB" :config="FormConfig_SJBB"></Form>
        <FormTable
          ref="refTableSJBB"
          class="sjbb-card-table"
          :config="TableConfig_SJBB"
        ></FormTable>
      </div>
      <div v-if="state.curTab === 'baojinshezhi'" class="tabContent">
        <Form :config="FormConfig_BJSZ"></Form>
      </div>
      <div v-if="state.curTab === 'qushitu'" class="tabContent">
        <Form ref="refForm_QST" :config="FormConfig_QST"></Form>
        <div class="qushituChart">
          <VChart
            ref="refQSTChart"
            :option="chartOptions.QuShiTuOption"
          ></VChart>
        </div>
      </div>
    </div>
  </Panel>
  <Form ref="refForm" :config="FormConfig"></Form>
</template>
<script lang="ts" setup>
import { FullScreen } from '@element-plus/icons-vue';
import moment from 'moment';
import { reactive, ref } from 'vue';
import Point from '@arcgis/core/geometry/Point.js';
import {
  GetStationRealTimeDetail,
  GetStationRecentDataByType
} from '@/api/shuiwureports/zhandian';
import { queryDeviceDataIstar } from '@/api/tsdb';
import { IECharts } from '@/plugins/echart';
import { formatDate } from '@/utils/DateFormatter';
import { formatTree, toggleFullscreen } from '@/utils/GlobalHelper';
import { removeSlash } from '@/utils/removeIdSlash';
import { initStationBasicTableColumn } from '@/views/pipeNetwork/monitoring/data';
import {
  initBJSZTableColumns,
  initBJSZTableData,
  initQuShiTuChart,
  initTopLeftChart,
  initTopRightChart,
  initBottomChart,
  initYuanChengKongZhi
} from '../../../bumb/motionMonitor/data';
import { getConstantsAttributeById } from '@/api/constants';
import { useBusinessStore } from '@/store';
import useStation from '@/hooks/station/useStation';
import {
  bindViewClick,
  createPictureMarker,
  createTextMarker,
  getGraphicLayer
} from '@/utils/MapHelper';

import bumpPng from '../../../../assets/images/bumb/bumb.png';
import bumpPng1 from '../../../../assets/images/bumb/bumb1.png';
import locationPng from '../../../../assets/images/map-location.png';

const { getAllStationOption, getStationAttrGroups } = useStation();

const props = defineProps<{
  view?: __esri.MapView;
  telport?: any;
}>();
const refForm = ref<IFormIns>();
const refPanel = ref<IPanelIns>();
const refTableSJBB = ref<IFormTableIns>();
const refForm_SJBB = ref<IFormIns>();
const refQSTChart = ref<IECharts>();
const refForm_QST = ref<IFormIns>();
const refFormTabs = ref<IFormIns>();
const refGongYiTuFrame = ref<HTMLDivElement>();
const chartOptions = reactive<{
  TopLeftChart: any;
  TopRightChart: any;
  BottomChart: any;
  QuShiTuOption: any;
}>({
  TopLeftChart: null,
  TopRightChart: null,
  BottomChart: null,
  QuShiTuOption: null
});
const state = reactive<{
  attrs: any;
  xinxizonglan: any;
  curTab: string;
  GongYiTuCurImg: any;
  YuanChengKongZhi: any;
  curStation?: any;
  stationGroups: NormalOption[];
  dynamicColumns: any[];
  marks: IArcMarkerProps[];
  stationList: NormalOption[];
}>({
  attrs: {
    remainder: '-',
    remainder_status: 'yellow',
    turbidity: '-',
    turbidity_status: 'purple',
    ph: '-',
    ph_status: 'lightblue'
  },
  curTab: 'xinxizonglan',
  xinxizonglan: [
    // { name: "ObjectID", value: result.a },
    // { name: "新编号", value: "" },
    { name: '泵房名称', value: '一级泵站' },
    { name: '所在区域', value: '槽上水厂' },
    { name: 'X坐标', value: 1345222.223 },
    { name: 'Y坐标', value: 345633.567 },
    { name: '地址', value: '重庆市北碚区' },
    { name: '', value: '-' }
    // {name:'设备类型',value:result.deviceTypeName}
    // { name: "档案号", value: "" },
  ],
  GongYiTuCurImg: bumpPng,
  YuanChengKongZhi: initYuanChengKongZhi(),

  // ShuJuBaoBiao: initShujuBaoBiao(),
  curStation: undefined,
  stationGroups: [],
  dynamicColumns: [],
  marks: [],
  stationList: []
});
const staticState: {
  graphicsLayer?: __esri.GraphicsLayer;
  textLayer?: __esri.GraphicsLayer;
} = {};
const TableConfig = reactive<ITable>({
  height: 300,
  dataList: [],
  columns: [{ label: '名称', prop: 'name' }],
  handleRowClick: (row) => {
    handleTableRowClick(row);
  },
  pagination: {
    hide: true
  }
});

const resetPanel = async () => {
  if (!TableConfig.currentRow.stationId) return;
  refFormTabs.value?.dataForm &&
    (refFormTabs.value.dataForm.curTab = state.curTab);
  refreshBasicData();
  state.stationGroups = await getStationAttrGroups(
    TableConfig.currentRow.stationId
  );
  initTopCharts();
  chartOptions.BottomChart = initBottomChart(
    [0, 3, 6, 9, 12, 15, 18, 21, 24],
    [
      12389 + Number((Math.random() * 10000).toFixed(2)),
      17607 + Number((Math.random() * 10000).toFixed(2)),
      21263 + Number((Math.random() * 10000).toFixed(2)),
      12123 + Number((Math.random() * 10000).toFixed(2)),
      22534 + Number((Math.random() * 10000).toFixed(2)),
      18456 + Number((Math.random() * 10000).toFixed(2)),
      21767 + Number((Math.random() * 10000).toFixed(2)),
      16213 + Number((Math.random() * 10000).toFixed(2)),
      12123 + Number((Math.random() * 10000).toFixed(2))
    ],
    [
      12123 + Number((Math.random() * 10000).toFixed(2)),
      15767 + Number((Math.random() * 10000).toFixed(2)),
      16213 + Number((Math.random() * 10000).toFixed(2)),
      19123 + Number((Math.random() * 10000).toFixed(2)),
      22234 + Number((Math.random() * 10000).toFixed(2)),
      18456 + Number((Math.random() * 10000).toFixed(2)),
      21213 + Number((Math.random() * 10000).toFixed(2)),
      15767 + Number((Math.random() * 10000).toFixed(2)),
      11123 + Number((Math.random() * 10000).toFixed(2))
    ]
  );
};
const AttrTableConfig = reactive<IAttrTable>({
  columns: [],
  data: {}
});
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '泵站列表'
      },
      fields: [
        { type: 'table', style: { height: '300px' }, config: TableConfig }
      ]
    },
    {
      fieldset: {
        desc: '水站状态'
      },
      fields: [{ type: 'attr-table', config: AttrTableConfig }]
    }
  ]
});

const refreshRightTable = async (type = '泵站') => {
  if (!type) {
    type =
      (FormConfig.group[0]?.fields[0] as ITabs)?.tabs[0]?.value?.toString() ||
      '';
    refForm.value?.dataForm && (refForm.value.dataForm.type = type);
    // 直接返回，因为修改了type重新触发此方法
    return;
  }

  staticState.graphicsLayer?.removeAll();
  staticState.textLayer?.removeAll();
  TableConfig.loading = true;
  const res = await GetStationRecentDataByType({
    stationType: type,
    projectId: useBusinessStore().navSelectedRange?.data?.id
  });
  state.stationList = await getAllStationOption();
  const colRes = await getConstantsAttributeById({
    type: 'stationInfo',
    key: type
  });
  let dynamicColumns: { label: string; value: string }[] = [];
  try {
    dynamicColumns = colRes.data[0] ? JSON.parse(colRes.data[0].value) : [];
  } catch (error) {
    dynamicColumns = [];
  }

  TableConfig.columns = initTableRightColumn(
    dynamicColumns.map((item) => ({
      minWidth: 120,
      label: item.label,
      prop: item.value
    }))
  );
  state.marks = [];
  TableConfig.dataList =
    res.data?.map((item) => {
      const location = item.location?.split(',');
      item.status = item.status || 'offline';
      item.x = location[0] && parseFloat(location[0]);
      item.y = location[1] && parseFloat(location[1]);
      const markData: IArcMarkerProps = {
        // showMore: true,
        title: item.name,
        visible: false,
        x: item.x,
        y: item.y,
        attributes: {
          row: item,
          id: item.stationId
        },
        symbolConfig: {
          url: locationPng
        }
      };
      state.marks.push(markData);
      return item;
    }) || [];
  TableConfig.currentRow = TableConfig.dataList[0];
  initValves();
  addMarks();
  refreshBasicData();
  TableConfig.loading = false;
  const tagForm = FormConfig.group[0].fields[0] as IFormRadio;
  tagForm.options?.map((item) => {
    item.data.suffix =
      item.value === ''
        ? res.data.length
        : item.value === 'online'
          ? res.data.filter((a) => a.status === 'online').length
          : item.value === 'warning'
            ? res.data.filter((a) => a.status === 'warning').length
            : item.value === 'offline'
              ? res.data.filter((a) => a.status === 'offline').length
              : 0;
  });
  state.dynamicColumns = dynamicColumns;
};
const initValves = async () => {
  if (!TableConfig.currentRow) return;
  const valveData = await GetStationRealTimeDetail(
    TableConfig.currentRow.stationId,
    '泵阀'
  );
  const data = valveData.data || [];
  AttrTableConfig.columns = [
    [
      {
        label: TableConfig.currentRow.name,
        prop: 'valves',
        formItemConfig: {
          type: 'btn-group',
          style: {
            flexWrap: 'wrap'
          },
          size: 'small',
          btns: data?.map((item) => {
            return {
              perm: true,
              styles: {
                margin: '6px'
              },
              text: item.propertyName,
              type: parseInt(item.value || '0.0') === 0 ? 'default' : 'primary',
              click: () => {
                //
              }
            };
          })
        }
      }
    ]
  ];
};

const addMarks = () => {
  /** *****添加图片标注和文本标注 **** */
  TableConfig.dataList.map((item) => {
    const point = new Point({
      longitude: item.x,
      latitude: item.y,
      spatialReference: props.view?.spatialReference
    });
    const picturemark = createPictureMarker(point.x, point.y, {
      picUrl: locationPng,
      spatialReference: props.view?.spatialReference,
      attributes: {
        row: item
      }
    });
    const textmark = createTextMarker(point.x, point.y, {
      text: item.name,
      spatialReference: props.view?.spatialReference,
      yOffset: -20
    });
    staticState.graphicsLayer?.add(picturemark);
    staticState.textLayer?.add(textmark);
  });
};
const initTableRightColumn = (
  dynamicColumns: IFormTableColumn[]
): IFormTableColumn[] => {
  return [
    { minWidth: 120, label: '名称', prop: 'name' },
    { minWidth: 160, label: '时间', prop: 'time' },
    ...(dynamicColumns || []),
    {
      width: 70,
      label: '状态',
      prop: 'pressure_status',
      formatter: (row, val) => {
        return val === 'online' ? '在线' : '离线';
      }
    }
  ];
};
const useLeft = () => {
  const initTopCharts = async () => {
    if (!TableConfig.currentRow?.stationId) return;
    const res = await GetStationRealTimeDetail(
      TableConfig.currentRow.stationId
    );
    const water_level = res.data?.find(
      (item) => item.property === 'water_level'
    )?.value;
    const pressure = res.data?.find(
      (item) => item.property === 'pressure'
    )?.value;
    chartOptions.TopLeftChart = initTopLeftChart(pressure || 0);
    chartOptions.TopRightChart = initTopRightChart(water_level || 0);
    state.attrs.remainder =
      res.data?.find((item) => item.property === 'remainder')?.value || '-';
    state.attrs.turbidity =
      res.data?.find((item) => item.property === 'turbidity')?.value || '-';
    state.attrs.ph =
      res.data?.find((item) => item.property === 'ph')?.value || '-';
  };
  return {
    initTopCharts
  };
};
const { initTopCharts } = useLeft();
const useBasicTable = () => {
  const TableConfig_xxzl = reactive<ITable>({
    height: 440,
    columns: [
      { label: '属性', prop: 'name' },
      { label: '值', prop: 'value' }
    ],
    dataList: [],
    pagination: { hide: true }
  });
  const refreshBasicData = () => {
    const id = TableConfig.currentRow?.stationId;
    const row = state.stationList.find((item) => item.id === id)?.data;
    TableConfig_xxzl.dataList = initStationBasicTableColumn(
      row || TableConfig.currentRow
    );
  };
  return {
    refreshBasicData,
    TableConfig_xxzl
  };
};
const handleFullScreen = () => {
  refGongYiTuFrame.value && toggleFullscreen(refGongYiTuFrame.value);
};
const { TableConfig_xxzl, refreshBasicData } = useBasicTable();
const useHistory = () => {
  const initSJBBTags = async () => {
    const field = FormConfig_SJBB.group[0].fields[0] as IFormRadio;
    field && (field.options = state.stationGroups);
    FormConfig_SJBB.defaultValue = {
      type: state.stationGroups[0]?.value
    };
    refForm_SJBB.value?.resetForm();
    refreshHistoryData();
  };

  const refreshHistoryData = async () => {
    TableConfig_SJBB.loading = true;
    const type =
      refForm_SJBB.value?.dataForm?.type || FormConfig_SJBB.defaultValue?.type;
    if (!type || !TableConfig.currentRow) {
      TableConfig_SJBB.dataList = [];
    } else {
      try {
        const res = await GetStationRealTimeDetail(
          TableConfig.currentRow?.stationId,
          type
        );
        TableConfig_SJBB.dataList =
          res.data?.map((item) => {
            item.value = (item.value || '-') + ' ' + (item.unit || '');
            return item;
          }) || [];
      } catch (error) {
        //
      }
    }
    TableConfig_SJBB.loading = false;
  };
  const TableConfig_SJBB = reactive<ITable>({
    columns: [
      { minWidth: 100, label: '名称', prop: 'propertyName' },
      {
        minWidth: 140,
        label: '时间',
        prop: 'collectionTime',
        formatter: (row) => formatDate(row.collectionTime)
      },
      { minWidth: 120, label: '监测值', prop: 'value' }
    ],
    pagination: {
      hide: true
    },
    dataList: []
  });
  const FormConfig_SJBB = reactive<IFormConfig>({
    labelPosition: 'top',
    gutter: 0,
    group: [
      {
        fields: [
          {
            xs: 18,
            type: 'radio-button',
            label: '',
            field: 'type',
            options: [],
            onChange: () => refreshHistoryData()
          },
          {
            xs: 6,
            type: 'btn-group',
            style: {
              justifyContent: 'flex-end'
            },
            btns: [
              {
                perm: true,
                type: 'warning',
                text: '导出',
                click: () => {
                  refTableSJBB.value?.exportTable();
                }
              }
            ]
          }
        ]
      }
    ]
  });
  return {
    FormConfig_SJBB,
    TableConfig_SJBB,
    initSJBBTags
  };
};
const { FormConfig_SJBB, TableConfig_SJBB, initSJBBTags } = useHistory();
const useTrend = () => {
  const initQSTTags = async () => {
    const field1 = FormConfig_QST.group[0].fields[0] as IFormRadio;
    field1 && (field1.options = state.stationGroups);
    FormConfig_QST.defaultValue = {
      attrs: '',
      group: state.stationGroups[0]?.value
    };
    refForm_QST.value?.resetForm();
    initQSTAttrs();
  };
  const initQSTAttrs = () => {
    const typeField = FormConfig_QST.group[0].fields[0] as IFormRadio;
    const tOptions = typeField?.options || [];
    const field = FormConfig_QST.group[0].fields[1] as IFormRadio;
    const curType =
      refForm_QST.value?.dataForm?.group || FormConfig_QST.defaultValue?.group;
    curType &&
      field &&
      (field.options = formatTree(
        tOptions.find((item) => item.value === curType)?.data?.attrList || [],
        {
          id: 'id',
          value: 'id',
          label: 'name'
        }
      ));
    FormConfig_QST.defaultValue = {
      ...(refForm_QST.value?.dataForm || FormConfig_QST.defaultValue || {}),
      attrs: (field?.options && field?.options[0]?.id) || ''
    };
    refForm_QST.value?.resetForm();
    refreshTrendData();
  };
  const refreshTrendData = async () => {
    refQSTChart.value?.clear();
    refQSTChart.value?.resize();
    const id =
      refForm_QST.value?.dataForm?.attrs || FormConfig_QST.defaultValue?.attrs;
    const data = (
      FormConfig_QST.group[0]?.fields[1] as IFormRadio
    )?.options?.find((item) => item.value === id)?.data;
    const xData: string[] = [];
    const Data: number[] = [];
    if (data?.deviceId && data?.attr) {
      const deviceId = removeSlash(data.deviceId);
      const res = await queryDeviceDataIstar({
        start: moment().subtract(1, 'd').valueOf(),
        end: moment().valueOf(),
        type: '15m',
        attributes: [deviceId + '.' + data.attr]
      });
      const resData = res.data || {};
      for (const key in resData) {
        xData.push(key);
        const cData: Record<string, number> = resData[key];

        Data.push(Object.values(cData)[0]);
      }
    }
    chartOptions.QuShiTuOption = initQuShiTuChart(data?.name, xData, Data);
    refQSTChart.value?.resize();
  };
  const FormConfig_QST = reactive<IFormConfig>({
    labelPosition: 'top',
    group: [
      {
        fields: [
          {
            type: 'radio-button',
            label: '',
            field: 'group',
            options: [],
            onChange: () => initQSTAttrs()
          },
          {
            type: 'radio-button',
            label: '',
            field: 'attrs',
            options: [],
            onChange: () => refreshTrendData()
          }
        ]
      }
    ],
    defaultValue: {
      group: '',
      attrs: ''
    }
  });

  return {
    FormConfig_QST,
    initQSTTags
  };
};
const { FormConfig_QST, initQSTTags } = useTrend();

const FormConfig_Tabs = reactive<IFormConfig>({
  defaultValue: {
    curTab: state.curTab
  },
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'tabs',
          label: '',
          field: 'curTab',
          tabType: 'simple',
          tabs: [
            { label: '信息总览', value: 'xinxizonglan' },
            { label: '工艺图', value: 'gongyitu' },
            // { label: '远程控制', value: 'yuanchengkongzhi' },
            { label: '数据报表', value: 'shujubaobiao' },
            // { label: '报警设置', value: 'baojinshezhi' },
            { label: '趋势图', value: 'qushitu' }
          ],
          onChange: (val: any) => {
            state.curTab = val;
            switch (val) {
              case 'shujubaobiao':
                initSJBBTags();
                break;
              case 'qushitu':
                initQSTTags();
                break;
              case 'xinxizonglan':
                refreshBasicData();
                break;
              default:
                break;
            }
          }
        }
      ]
    }
  ]
});
const FormSelectConfig = reactive<IFormSelect>({
  type: 'select',
  label: '',
  field: '',
  size: 'small',
  options: [
    {
      label: '工艺一',
      value: bumpPng
    },
    {
      label: '工艺二',
      value: bumpPng1
    }
  ],
  onChange: (val) => {
    state.GongYiTuCurImg = val;
  }
});
const FormCheckboxConfig = reactive<IFormSwitch>({
  type: 'switch',
  label: '',
  size: 'small',
  field: '',
  activeValue: 'online',
  inActiveValue: 'offline'
});

const TableConfig_BJSZ = reactive<ITable>({
  height: 390,
  columns: initBJSZTableColumns(),
  dataList: initBJSZTableData(),
  pagination: { hide: true }
});
const FormConfig_BJSZ = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'btn-group',
          label: '',
          field: '',
          btns: [
            {
              type: 'primary',
              text: '保存',
              perm: true,
              styles: {
                marginLeft: 'auto'
              }
            }
          ]
        },
        {
          type: 'table',
          label: '',
          field: '',
          config: TableConfig_BJSZ
        }
      ]
    }
  ]
});
const handleTableRowClick = (row) => {
  TableConfig.currentRow = row;
  refPanel.value?.Open();
  resetPanel();
  props.view?.goTo(
    new Point({
      longitude: row.x,
      latitude: row.y,
      spatialReference: props.view.spatialReference
    })
  );
};
onMounted(() => {
  refreshRightTable();
  if (!props.view) return;
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'bumb_monitoring',
    title: '泵站监控'
  });
  staticState.textLayer = getGraphicLayer(props.view, {
    id: 'bumb_monitoring_pois',
    title: '泵站监控-注记'
  });
  /** 监听点击事件，并根据情况打开弹窗 * */
  props.view &&
    bindViewClick(props.view, (res) => {
      if (!res.results.length) return;
      res.results.map((item) => {
        if (item.type === 'graphic') {
          const row = item.graphic?.attributes?.row;
          handleTableRowClick(row);
        }
      });
    });
});
onBeforeUnmount(() => {
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
  staticState.textLayer && props.view?.map.remove(staticState.textLayer);
});
</script>
<style lang="scss" scoped>
.tabsForm {
  padding: 0 15px;
  :deep(.el-form-item--default) {
    margin-bottom: 10px;
  }
}

.tabContent {
  height: calc(100% - 50px);
  padding: 0 15px;
  .sjbb-card-table {
    height: calc(100% - 52px);
  }
}
.qushituChart {
  height: calc(100% - 100px);
}
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.panel-center {
  width: 800px;
  height: 100%;
}
.ergong_xinxizonglan {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;

  .left {
    width: 485px;
    height: 440px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .right {
    width: 356px;
    padding: 0 15px;
    height: 100%;

    .td-title {
      background-color: #253953;
    }
  }
}

.ergong_gongyiyu {
  .header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: ccenter;
    .gylist {
      width: 100px;
    }

    .fullscreen {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-left: auto;
      width: 64px;
      height: 24px;
      background-color: #36475e;
    }
  }

  .content {
    width: 100%;
    margin-top: 8px;
    height: calc(100% - 40px);
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.ergong_yuanchengkongzhi {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  justify-content: space-between;
  font-size: 12px;

  .left {
    width: 485px;
    height: 100%;
    margin-right: 15px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .right {
    width: 355px;
    height: 100%;
    overflow: auto;
    padding-right: 8px;

    .right-item {
      width: 100%;
      height: 64px;
      margin: 0 0 12px;
      background-color: #253953;
      border: 1px solid rgba(49, 70, 97, 1);
      border-radius: 2px;

      &:last-child {
        margin-bottom: 0;
      }

      .top,
      .bottom {
        height: 32px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: flex-start;
        align-items: center;
        align-content: center;
        line-height: 1em;
      }

      .top {
        padding: 10px 12px;
        .name {
          position: relative;
          margin-left: 20px;
          &.online {
            &::before {
              background-color: #5cb85c;
              border: 1px solid rgba(150, 232, 150, 1);
            }
          }

          &.offline {
            &::before {
              background: #a9adb2;
              border: 1px solid rgba(218, 218, 218, 1);
            }
          }

          &::before {
            content: ' ';
            position: absolute;
            width: 1em;
            height: 1em;
            border-radius: 50% 50%;
            left: -20px;
          }
        }

        .status {
          margin-left: auto;
          &.online {
            color: #5cb85c;
          }

          &.offline {
            color: #a9adb2;
          }
        }
      }

      .bottom {
        padding: 10px 0 10px 0;
        .timetotal {
          font-size: 1em;
          transform: scale(0.75);
          font-size: 10px;
        }

        &-switch {
          margin-left: auto;
          padding-right: 8px;
        }
      }
    }
  }
}

.ergong_shujubaobiao,
.ergong_baojinshezhi,
.ergong_qushitu {
  .header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-content: center;
    align-items: center;

    .tag {
      width: 64px;
      height: 24px;
      margin: 12px 8px;
      line-height: 24px;
      text-align: center;
      font-size: 12px;
      background-color: #36475e;
      border-radius: 2px;
      cursor: pointer;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }

      &-active {
        background-color: #4098d7;
      }

      &-warning {
        background-color: #e48c43;
      }

      &.exportbtn,
      &.savebhsz {
        margin-left: auto;
      }
    }
  }

  .content {
    height: calc(100% - 48px);

    .table {
      max-height: 100%;

      .formdata {
        max-height: calc(100% - 32px);
        overflow: auto;
      }
    }
    &.qushitu-box {
      width: 100%;
      .chartbox {
        width: 100%;
        height: 100%;
        .chart-left,
        .chart-right {
          height: 160px;
        }
      }
    }
  }
}

.ergong_baojinshezhi {
  .theader-item {
    min-width: 90px;
  }

  .form-input {
    width: 100%;

    &::placeholder {
      color: #6e829b;
    }
  }
}
</style>
<style lang="scss">
html.dark {
  .amap-info-contentContainer.bottom-center {
    &::after {
      border: 5px solid #27394e;
    }
  }
  .amap-pop-tag {
    background-color: #27394e;
  }
  .amap-info-contentContainer {
    background-color: #253953;
    .amap-info-content {
      background-color: #253953;
    }
  }
  .list-item {
    .label,
    .prop {
      border: 1px solid #2b405b;
    }
    .label {
      background-color: #253953;
    }
    .prop {
      background-color: #182536;
    }
  }
}
.amap-info-contentContainer.bottom-center {
  &::after {
    content: ' ';
    width: 0;
    height: 0;
    position: absolute;
    left: 50%;
    transform: rotate(45deg) translateY(50%) translateX(-50%);
    border: 5px solid #fff;
    z-index: -1;
  }
}
.amap-pop-tag {
  // display: none;
  background-color: #ddd;
  min-width: 150px;
  .header {
    padding: 8px;
    height: 32px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    background-color: #5094a8;
    .close-wrapper {
      line-height: 25px;
      font-size: 16px;
      width: 25px;
      height: 25px;
      cursor: pointer;
      text-align: center;
    }
  }
  .map-content {
    padding: 8px;
    .list {
      list-style: none;
      padding-left: 8px;
    }
  }
}
.list-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .label,
  .prop {
    width: 50%;
    padding: 5px 8px;
    font-size: 12px;
    text-align: center;
    border: 1px solid rgb(138, 138, 138);
    line-height: 20px;
  }
  .label {
    background-color: #ddd;
  }
  .prop {
    background-color: rgb(190, 188, 188);
  }
}
.map-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
  .location-map {
    height: 100%;
    position: absolute;
    .get-location-box {
      height: 100%;
    }
  }
}
</style>
<style lang="scss">
.panel {
  &.map-panel {
    color: #fff;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    height: 560px;
    .panel-left-wrapper {
      background-image: linear-gradient(90deg, #174d72 0%, #25738e 100%);
    }
    .panel-left {
      padding: 15px;
      .chartbox,
      .attr-box {
        background-color: #144356;
        width: 100%;
        margin-bottom: 8px;
        .info {
          width: 200px;
          color: #22e837;
          .warning {
            color: #e4a910;
            border-radius: 2px;
          }

          .error {
            color: #d5584b;
            border-radius: 2px;
          }

          // .online {
          //   // color: transparent;
          // }

          .offline {
            color: #707070;
            border-radius: 2px;
          }
        }
      }
      .attr-box {
        .name {
          word-break: keep-all;
        }
      }

      .chartbox {
        height: 160px;
        border-radius: 2px;
        &.top {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          .chart-left,
          .chart-right {
            width: 138px;
            height: 160px;
          }
        }
        &.bottom {
          height: 196px;
          width: 100%;
        }
      }

      .attr-box {
        height: 48px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: flex-start;
        align-content: center;
        align-items: center;
        padding: 12px;

        .lefticon {
          width: 25px;
          height: 25px;
          border-radius: 50% 50%;
          text-align: center;
          line-height: 25px;
          margin: 0;
          padding: 0;
          font-size: 12px;

          &.yellow {
            background-image: linear-gradient(
              180deg,
              rgba(239, 196, 125, 0.8) 0%,
              rgba(185, 122, 13, 0.8) 100%
            );
            box-shadow: 0 0 4px 0 rgba(173, 139, 71, 1);
          }

          &.purple {
            background-image: linear-gradient(
              180deg,
              rgba(221, 125, 239, 0.8) 0%,
              rgba(145, 13, 185, 0.8) 100%
            );
            box-shadow: 0 0 4px 0 rgba(139, 52, 179, 1);
          }

          &.lightblue {
            background-image: linear-gradient(
              180deg,
              rgba(125, 205, 239, 0.8) 0%,
              rgba(13, 115, 185, 0.8) 100%
            );
            box-shadow: 0 0 4px 0 rgba(54, 137, 184, 1);
          }

          .lefticon-text {
            font-size: 1em;

            transform: scale(0.75);
          }
        }

        .name {
          font-size: 12px;
          margin-left: 8px;
        }

        .info {
          margin-left: auto;
          letter-spacing: 0;
          text-align: right;
          font-weight: 400;

          &.green {
            color: #22e837;
          }

          &.red {
            color: #ef3d3d;
          }

          .count {
            font-size: 18px;
          }

          .unit {
            font-size: 12px;
          }
        }
      }
    }
    .warning {
      background-color: #e4a910;
      border-radius: 2px;
    }

    .error {
      background-color: #d5584b;
      border-radius: 2px;
    }

    .online {
      background-color: transparent;
    }

    .offline {
      background-color: #707070;
      border-radius: 2px;
    }
  }
}
</style>
