import ECharts from 'vue-echarts';
import * as echarts from 'echarts';
import { use } from 'echarts/core';

// import ECharts modules manually to reduce bundle size
import { CanvasRenderer } from 'echarts/renderers';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dles<PERSON><PERSON>hart
} from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  ToolboxComponent,
  DataZoomComponent,
  TitleComponent,
  LegendComponent,
  GeoComponent
} from 'echarts/components';
import themeDark from './vintage.json';
import customed from './customed.json';
import whiteBackground from './whiteBackground.json';
import blackBackground from './blackBackground.json';

import 'echarts-gl';
import 'echarts/lib/chart/pie';
import 'echarts-liquidfill';

export type IECharts = InstanceType<typeof ECharts>;
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  GridComponent,
  Tooltip<PERSON>omponent,
  <PERSON><PERSON>hart,
  ToolboxComponent,
  <PERSON>Zoom<PERSON>omponent,
  TitleComponent,
  LegendComponent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Candlestick<PERSON>hart,
  GeoComponent
]);

// register globally (or you can do it locally)
export default (app) => {
  app.component('VChart', ECharts);
};

echarts.registerTheme('dark', themeDark);
echarts.registerTheme('light', customed);
echarts.registerTheme('whiteBackground', whiteBackground);
echarts.registerTheme('blackBackground', blackBackground);
