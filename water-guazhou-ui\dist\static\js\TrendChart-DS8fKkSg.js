import{d as m,c as h,am as f,o as y,g as _,n as v,p as c,c4 as b,C as g}from"./index-r0dFAfgr.js";const x={class:"chart-container"},C=m({__name:"TrendChart",props:{chartData:{type:Array,default:()=>[]}},setup(i){const s=i,n=h(null);let a=null;const l=()=>{n.value&&(a=b(n.value),o())},o=()=>{if(!a)return;const t=s.chartData;if(!t||t.length===0)return;const r=t[0].trends.map(e=>e.time),d=t.map(e=>({name:e.stationName,type:"line",data:e.trends.map(u=>u.value),symbol:"circle",symbolSize:6,lineStyle:{width:2}})),p={color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de"],tooltip:{trigger:"axis"},legend:{data:t.map(e=>e.stationName),top:10},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:r},yAxis:{type:"value",name:"KWh/t"},series:d};a.setOption(p)};return f(()=>s.chartData,()=>{o()},{deep:!0}),y(()=>{l(),window.addEventListener("resize",()=>{a&&a.resize()})}),(t,r)=>(_(),v("div",x,[r[0]||(r[0]=c("div",{class:"chart-title"},"泵站能耗: KWh/t",-1)),c("div",{ref_key:"chartRef",ref:n,class:"chart"},null,512)]))}}),z=g(C,[["__scopeId","data-v-094cbbbf"]]);export{z as default};
