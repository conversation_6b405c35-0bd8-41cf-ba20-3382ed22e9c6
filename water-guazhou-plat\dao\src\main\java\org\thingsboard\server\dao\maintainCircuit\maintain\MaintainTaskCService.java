package org.thingsboard.server.dao.maintainCircuit.maintain;

import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskC;

import java.util.Map;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface MaintainTaskCService {

    void save(MaintainTaskC maintainTaskC);

    Map statistics(String deviceLabelCode);

    boolean checkUser(String mainId, String userId);
}
