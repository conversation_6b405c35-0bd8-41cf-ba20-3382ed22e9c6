package org.thingsboard.server.dao.extraUser;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.ExtraUser;
import org.thingsboard.server.dao.sql.extraUser.ExtraUserDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:05
 */
@Service
public class ExtraUserServiceImpl implements ExtraUserService {

    @Autowired
    private ExtraUserDao extraUserDao;

    @Override
    public ExtraUser save(ExtraUser extraUser) {
        return extraUserDao.save(extraUser);
    }

    @Override
    public List<ExtraUser> findByTenant(String tenantId) {
        return extraUserDao.findByTenant(tenantId);
    }

    @Override
    public boolean delete(String id) {
        return extraUserDao.delete(id);
    }

    @Override
    public boolean deleteAll(List<String> ids) {
        return extraUserDao.deleteAll(ids);
    }

    @Override
    public int checkName(String name, String tenantId) {
        return extraUserDao.checkName(name, tenantId);
    }
}
