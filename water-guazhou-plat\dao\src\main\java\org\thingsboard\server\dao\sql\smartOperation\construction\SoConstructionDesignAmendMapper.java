package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendPageRequest;

@Mapper
public interface SoConstructionDesignAmendMapper extends BaseMapper<SoConstructionDesignAmend> {
    IPage<SoConstructionDesignAmend> findByPage(SoConstructionDesignAmendPageRequest request);

    boolean update(SoConstructionDesignAmend entity);

    boolean updateFully(SoConstructionDesignAmend entity);
}
