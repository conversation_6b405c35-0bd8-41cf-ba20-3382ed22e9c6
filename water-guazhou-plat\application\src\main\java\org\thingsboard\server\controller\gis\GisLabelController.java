package org.thingsboard.server.controller.gis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisLabelService;
import org.thingsboard.server.dao.model.request.GisLabelListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisLabel;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * gis标签
 */
@RestController
@RequestMapping("api/gis/label")
public class GisLabelController extends BaseController {

    @Autowired
    private GisLabelService gisLabelService;

    @GetMapping("list")
    public IstarResponse findList(GisLabelListRequest request) throws ThingsboardException {
        return IstarResponse.ok(gisLabelService.findList(request, getTenantId()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody GisLabel entity) throws ThingsboardException {
        gisLabelService.save(entity, getCurrentUser());
        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        gisLabelService.remove(ids);
        return IstarResponse.ok();
    }


}
