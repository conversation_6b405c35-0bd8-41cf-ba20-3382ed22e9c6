package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisExceptionUploadListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisExceptionUpload;

import java.util.List;

public interface GisExceptionUploadService {
    /**
     * 分页查询官网gis异常上报列表
     *
     * @param request  请求参数
     * @param tenantId 租户ID
     * @return 分页数据
     */
    PageData<GisExceptionUpload> findList(GisExceptionUploadListRequest request, TenantId tenantId);

    void save(GisExceptionUpload entity, User currentUser);

    /**
     * 审核
     *
     * @param id          数据ID
     * @param status      审核状态
     * @param remark      备注
     * @param currentUser 操作人
     */
    void approval(String id, String status, String remark, User currentUser);

    void remove(List<String> ids);
}
