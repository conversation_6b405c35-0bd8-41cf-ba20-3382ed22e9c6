package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.util.imodel.query.organization.OrganizationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.DepartmentInfo;

import java.util.List;

@Mapper
public interface OrganizationMapper extends BaseMapper<Organization> {
    @Select("select * from tb_organization where parent_id is null and tenant_id = #{tenantId}")
    List<Organization> findRoots(String tenantId);

    List<Organization> findChildren(@Param("pid") String pid, @Param("tenantId") String tenantId);

    List<Organization> findAll(OrganizationPageRequest organization);

    boolean canBeDelete(String id);

    boolean canBeAdd(String parentId);

    @Select("select * from tb_organization where id like '%'||#{pid}||'%'")
    Organization findFirstByIdLike(String pid);

    boolean update(Organization updated);

    boolean removeAllOrganization(String tenantId);

    List<String> findChildrenIdString(@Param("orgId") String orgId);

    DepartmentInfo getInfoById(String departmentId);
}
