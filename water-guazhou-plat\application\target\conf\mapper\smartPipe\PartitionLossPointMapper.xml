<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionLossPointMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionLossPoint">
        select a.*, b.name as partitionName, b.status
        from tb_pipe_partition_loss_point a
        left join tb_pipe_partition b on a.partition_id = b.id
        <where>
            <if test="param.partitionId != null and param.partitionId != ''">
                and a.partition_id = #{param.partitionId}
            </if>
            <if test="param.findDate != null and param.findDate != ''">
                and a.find_date = #{param.findDate}
            </if>
            and a.tenant_id = #{param.tenantId}
        </where>
        order by a.create_time desc

    </select>
    <select id="getLossPointReport" resultType="com.alibaba.fastjson.JSONObject">
        select parti.id, parti.status, parti.name, ifnull(point.num, 0) num
        from tb_pipe_partition parti
        left join (select a.partition_id, sum(num) as num from tb_pipe_partition_loss_point a group by a.partition_id) point on parti.id = point.partition_id
        <where>
            parti.tenant_id = #{tenantId}
            <if test="name != null and name != ''">
                and parti.name like '%' ||#{name} || '%'
            </if>
        </where>
        order by parti.create_time
    </select>
</mapper>