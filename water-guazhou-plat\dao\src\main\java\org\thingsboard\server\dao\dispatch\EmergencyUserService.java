package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserSaveRequest;

import java.util.List;

public interface EmergencyUserService {
    /**
     * 分页条件查询应急人员
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<EmergencyUser> findAllConditional(EmergencyUserPageRequest request);

    /**
     * 保存应急人员
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    EmergencyUser save(EmergencyUserSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(EmergencyUser entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 将指定客户的系统用户全部同步到应急用户
     *
     * @param userId   操作人id，会标记为创建人
     * @param tenantId 客户id
     * @return 是否成功
     */
    boolean sync(String userId, String tenantId);

    /**
     * 查询指定客户的应急用户树，最大深度为3，从上至下依次为 单位->部门->应急用户
     *
     * @param tenantId 客户id
     * @param depth    查询深度，从1开始
     * @return 应急用户树
     */
    List<Organization> treeUser(String tenantId, Integer depth);

}
