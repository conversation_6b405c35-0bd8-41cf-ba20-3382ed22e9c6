import{X as l}from"./xlsx-rVJkW9yq.js";const i=[{label:"大型",value:"3"},{label:"中型",value:"2"},{label:"小型",value:"1"},{label:"微型",value:"0"}],v=[{label:"已冻结",value:"3"},{label:"停止合作",value:"2"},{label:"正在合作",value:"1"},{label:"意向供应商",value:"0"}],m=[{label:"五星",value:"5"},{label:"四星",value:"4"},{label:"三星",value:"3"},{label:"二星",value:"2"},{label:"一星",value:"1"}],p=[{label:"领用",value:"0"},{label:"安装",value:"1"},{label:"售出",value:"2"}],d=[{label:"未出库",value:!1},{label:"已出库",value:!0}],g=[{label:"待询价",value:0,color:"#79bbff"},{label:"询价中",value:1,color:"#95d475"},{label:"已完成",value:2,color:"#eebe77"}],f={设备编号:"serialId",货品名称:"name",规格型号:"model",单位:"unit",数量:"num",单价:"price",税率:"taxRate",备注:"remark"},x={设备编码:"serialId",设备名称:"name",规格型号:"model",计算单位:"unit",采购数量:"num","单价(元)":"price","税率(%)":"taxRate"},w=[{required:!0,message:"请输入联系方式",trigger:"blur"},{min:11,max:11,message:"请输入11位手机号码",trigger:"blur"},{pattern:/^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,message:"请输入正确的手机号码"}];function y(r){return new Promise((s,o)=>{console.log(o);const e=new FileReader;e.onload=t=>{const n=new Uint8Array(t.target.result),a=l.read(n,{type:"array"}),u=a.Sheets[a.SheetNames[0]],c=l.utils.sheet_to_json(u);s(c)},e.readAsArrayBuffer(r.raw)})}export{x as a,f as b,i as c,g as d,d as e,p as f,m as i,w as p,y as r,v as s};
