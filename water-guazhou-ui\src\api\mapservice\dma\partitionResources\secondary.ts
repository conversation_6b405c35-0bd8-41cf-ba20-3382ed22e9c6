import { request } from '@/plugins/axios'

/**
 * 查询泵站列表
 * @param params
 * @returns
 */
export const GetDmaPartitionPumpList = (
  params: IQueryPagerParams & {
    caliber?: string
    partitionId?: string
    brand?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/pumpHouse/list',
    method: 'get',
    params
  })
}
/**
 * 添加泵房
 * @param params
 * @returns
 */
export const AddDmaPartitionPumpList = (params: {
  partitionId: string
  type: string
  position: string
  mode: string
  brand: string
  inWaterMetering: string
  outWaterMetering: string
  caliber: string
  remark: string
  img: string
}) => {
  return request({
    url: '/api/spp/dma/partition/pumpHouse',
    method: 'post',
    data: params
  })
}
/**
 * 删除泵房
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionPumpList = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/pumpHouse',
    method: 'delete',
    data: ids
  })
}
