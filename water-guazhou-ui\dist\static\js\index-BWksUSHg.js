import{_ as w}from"./TreeBox-DDD2iwoR.js";import{_ as L}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{_ as M}from"./index-BJ-QPYom.js";import"./index-0NlGN6gS.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as P,c as v,r as f,l as n,bI as R,b as u,bH as T,o as N,g as q,h as A,F as k,q as s,i as _,C as B}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as O}from"./usePartition-DkcY9fQ2.js";import{_ as j}from"./RecordList.vue_vue_type_script_setup_true_lang-hN3SrCoO.js";import{s as H}from"./printUtils-C-AxhDcd.js";import{C as E,a as V,b as $}from"./useWaterCorrect-CpCzjcCp.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";const z=P({__name:"index",setup(G){const y=v(),d=v(),o=f({data:[],loading:!0,title:"选择分区",expandOnClickNode:!1,defaultExpandAll:!0,treeNodeHandleClick:async t=>{o.currentProject!==t&&(o.currentProject=t,await i())}}),C=f({defaultParams:{type:"1",date:[n().subtract(1,"M").format(),n().format()],month:n().format(R)},filters:[{type:"radio-button",field:"type",width:90,clearable:!1,options:[{label:"按时",value:"1"},{label:"按日",value:"2"},{label:"按月",value:"3"}],label:"选择方式",placeholder:"请选择"},{type:"daterange",field:"date",clearable:!1,handleHidden:(t,e,r)=>{r.hidden=t.type==="3"}},{type:"month",field:"month",clearable:!1,handleHidden:(t,e,r)=>{r.hidden=t.type!=="3"}},{type:"input",label:"水表名称",field:"meterName"},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>i()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var t;(t=d.value)==null||t.resetForm()}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>{i(!0)}},{perm:!0,text:"修正记录",type:"default",iconifyIcon:"mdi:eye",click:()=>I()}]}]}),a=f({indexVisible:!0,dataList:[],columns:[{label:"分区名称",minWidth:120,prop:"partitionName"},{label:"水表名称",minWidth:120,prop:"deviceName"},{label:"RTU编号",minWidth:120,prop:"deviceId"},{label:"日期",minWidth:120,prop:"collectTimeStr"},{label:"供水量",minWidth:120,prop:"value"},{label:"追加水量",minWidth:120,prop:"correctWater"}],operationWidth:200,operations:[{perm:!0,text:"编辑",isTextBtn:!1,iconifyIcon:"ep:edit",click:t=>W(t)},{perm:!0,text:"保存",loading:t=>t.isLoading===!0,isTextBtn:!1,iconifyIcon:"ep:success-filled",click:t=>D(t)}],pagination:{refreshData:({page:t,size:e})=>{a.pagination.page=t,a.pagination.limit=e,i()}}}),I=()=>{var t,e;(e=(t=y.value)==null?void 0:t.refRecord)==null||e.openDialog()},W=t=>{t.correctWaterFormItemConfig={type:"input-number"}},D=async t=>{t.isLoading=!0;try{const e=await E({id:t.id,correctWater:t.correctWaterF});e.data.code===200?(u.success("保存成功"),t.correctWaterFormItemConfig=void 0):u.error(e.data.message)}catch{u.error("保存失败")}t.isLoading=!1},i=async t=>{var m,c,l,g,b,x;const e=((m=d.value)==null?void 0:m.queryParams)||{},r={page:a.pagination.page||1,size:a.pagination.limit||20,type:e.type,partitionId:(c=o.currentProject)==null?void 0:c.value};try{if(e.type==="3"&&e.month?r.month=e.month:(e.type==="1"||e.type==="2"&&e.date)&&(r.start=n(e.date[0],T).startOf("D").valueOf(),r.end=n(e.date[1],T).endOf("D").valueOf()),t){const p=await V(r);H(p.data,"供水量")}else{const p=await $(r);a.dataList=((g=(l=p.data)==null?void 0:l.data)==null?void 0:g.data)||[],a.pagination.total=((x=(b=p.data)==null?void 0:b.data)==null?void 0:x.total)||0}}catch{}},S=async()=>{var t;await h.getTree(),o.data=h.Tree.value,o.currentProject=(t=o.data)==null?void 0:t[0],i()},h=O();return N(async()=>{S()}),(t,e)=>{const r=M,m=F,c=L,l=w;return q(),A(l,null,{tree:k(()=>[s(r,{ref:"refTree","tree-data":_(o)},null,8,["tree-data"])]),default:k(()=>[s(m,{ref_key:"refSearch",ref:d,config:_(C)},null,8,["config"]),s(c,{config:_(a),class:"card-table"},null,8,["config"]),s(j,{ref_key:"refRecord",ref:y},null,512)]),_:1})}}}),be=B(z,[["__scopeId","data-v-43b9e6a5"]]);export{be as default};
