package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyUserMapper;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.model.JdbcHelper;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_emergency_vehicle")
public class EmergencyVehicle {
    // id
    @TableId
    private String id;

    // 车牌号
    private String numberPlate;

    // 车辆品牌
    private String carBrand;

    // 使用年限
    private Integer usePeriod;

    // 车辆负责人
    private String carUserId;

    // 状态
    private String status;

    // sim卡号
    private String simNum;

    // 服务器时间
    private Date serverTime;

    // 定位器时间
    private Date gprsTime;

    // 当前速度。km/h
    private Double nowSpeed;

    // 方向
    private String direction;

    // 当日里程
    private Double mileageDay;

    // 总里程
    private Double mileageTotal;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

    @SuppressWarnings("unused")
    private void customizeMap(ResponseMap map, JdbcHelper jdbc) {
        EmergencyUserMapper emergencyUserMapper = jdbc.getMapper(EmergencyUserMapper.class);
        DepartmentMapper departmentMapper = jdbc.getMapper(DepartmentMapper.class);
        EmergencyUser emergencyUser = emergencyUserMapper.selectById(carUserId);
        if (emergencyUser == null) {
            return;
        }
        String deptId = emergencyUser.getDeptId();
        map.put("carUserName", emergencyUser.getName());
        map.put("carUserDepartmentId", deptId);
        if (deptId == null) {
            return;
        }
        map.put("carUserDepartmentName", departmentMapper.getNameById(deptId));
        String orgId = departmentMapper.getDirectOrgByDepartmentId(deptId);
        map.put("carUserOrganizationId", orgId);
        if (orgId == null) {
            return;
        }
        map.put("carUserOrganizationName", departmentMapper.getNameById(orgId));
    }

}
