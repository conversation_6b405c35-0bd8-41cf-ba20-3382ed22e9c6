package org.thingsboard.server.dao.report;


import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.WaterReportDTO;
import org.thingsboard.server.dao.model.sql.WaterReport;

import java.util.List;

public interface WaterReportService {

    List<WaterReportDTO> findMonthReport(String month, String stationId, TenantId tenantId) throws ThingsboardException;

}
