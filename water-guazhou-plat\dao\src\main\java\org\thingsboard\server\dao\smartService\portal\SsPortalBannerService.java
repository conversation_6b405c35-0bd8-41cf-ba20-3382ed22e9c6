package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBanner;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBannerPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBannerSaveRequest;

public interface SsPortalBannerService {
    SsPortalBanner findById(String id);

    IPage<SsPortalBanner> findAllConditional(SsPortalBannerPageRequest request);

    SsPortalBanner save(SsPortalBannerSaveRequest entity);

    boolean update(SsPortalBanner entity);

    boolean delete(String id);

    /**
     * 切换轮播图的激活和非激活状态
     * @param req 请求信息
     * @return 是否设置成功
     */
    boolean active(SsPortalActiveRequest req);

}
