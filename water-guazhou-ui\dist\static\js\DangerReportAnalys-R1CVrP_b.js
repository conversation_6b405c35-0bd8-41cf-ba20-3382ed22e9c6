import{_ as g}from"./index-C9hz-UZb.js";import{d as C,c as d,r as f,s as O,l as n,o as k,Q as M,ay as D,g as w,n as B,q as s,F as Y,p as c,aq as S,al as x,C as q}from"./index-r0dFAfgr.js";import{_ as z}from"./Search-NSrhrIa_.js";import{B as L,P as V}from"./echart-BoVIcYbV.js";import{S as P}from"./workorder-jXNat1mh.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const T={class:"wrapper"},E={class:"top"},H={class:"top-left"},R={class:"top-right"},A={class:"bottom"},F=C({__name:"DangerReportAnalys",setup(I){const m=d(),_=d(),h=d(),v=f({filters:[{type:"radio-button",label:"时间范围",options:[{label:"日",value:"date"},{label:"月",value:"month"},{label:"年",value:"year"}],field:"type",onChange:()=>l()},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="date"},type:"date",label:"",field:"date"},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="month"},type:"month",label:"",field:"month"},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="year"},type:"year",label:"",field:"year"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:O(x),click:()=>l()}]}],defaultParams:{type:"year",month:n().format("YYYY-MM"),year:n().format("YYYY"),date:n().format("YYYY-MM-DD")}}),r=f({indexVisible:!0,columns:[{label:"发起人员",prop:"key"},{label:"发起事件数",prop:"value"}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,l()}}}),i=f({pieOption:null,barOption:null}),l=async()=>{var p,b;const e=((p=m.value)==null?void 0:p.queryParams)||{},t=(e==null?void 0:e.type)||"date";let a;switch(t){case"month":a=n(e[t],"YYYY-MM");break;case"year":a=n(e[t],"YYYY");break;default:a=n(e[t],"YYYY-MM-DD");break}const o=((b=(await P({fromTime:a.startOf(t==="year"?"y":t==="month"?"M":"D").valueOf(),toTime:a.endOf(t==="year"?"y":t==="month"?"M":"D").valueOf()})).data)==null?void 0:b.data)||{};i.barOption=L(o.data||[]),i.pieOption=V(o.data||[],"人员上报分析"),r.dataList=o.data||[],r.pagination.total=o.total||0},u=()=>{var e,t;(e=_.value)==null||e.resize(),(t=h.value)==null||t.resize()};return k(()=>{l(),window.addEventListener("resize",u)}),M(()=>{window.removeEventListener("resize",u)}),(e,t)=>{const a=z,y=S,o=D("VChart"),p=g;return w(),B("div",T,[s(p,{class:"card",title:" ",overlay:""},{title:Y(()=>[s(a,{ref_key:"refSearch",ref:m,config:v},null,8,["config"])]),default:Y(()=>[c("div",E,[c("div",H,[s(y,{config:r},null,8,["config"])]),c("div",R,[s(o,{ref_key:"refChart1",ref:h,option:i.pieOption},null,8,["option"])])]),c("div",A,[s(o,{ref_key:"refChart",ref:_,option:i.barOption},null,8,["option"])])]),_:1})])}}}),Z=q(F,[["__scopeId","data-v-232dcac6"]]);export{Z as default};
