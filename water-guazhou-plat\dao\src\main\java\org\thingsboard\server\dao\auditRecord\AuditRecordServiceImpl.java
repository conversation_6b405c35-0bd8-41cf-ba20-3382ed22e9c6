package org.thingsboard.server.dao.auditRecord;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.auditRecord.BO.AuditRecordBO;
import org.thingsboard.server.dao.model.sql.AuditRecordEntity;
import org.thingsboard.server.dao.sql.auditRecord.AuditRecordRepository;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-01-19
 */

@Slf4j
@Service
@Transactional
public class AuditRecordServiceImpl implements AuditRecordService {

    @Autowired
    private AuditRecordRepository auditRecordRepository;

    @Override
    public AuditRecordEntity save(AuditRecordEntity auditRecordEntity) {
        return auditRecordRepository.save(auditRecordEntity);
    }

    @Override
    public AuditRecordEntity findById(String id) {
        return auditRecordRepository.findOne(id);
    }

    @Override
    public Map<String, Object> getList(AuditRecordBO recordBO) {
        Page<AuditRecordEntity> recordEntities = auditRecordRepository.findAll(new Specification<AuditRecordEntity>() {

            @Override
            public Predicate toPredicate(Root<AuditRecordEntity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                List<Predicate> list = new ArrayList<>();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                try {
                    if (StringUtils.isNotBlank(recordBO.getStartTime())) {
                        list.add(criteriaBuilder.ge(root.get("time").as(Long.class), format.parse(recordBO.getStartTime()).getTime()));
                    }

                    if (StringUtils.isNotBlank(recordBO.getEndTime())) {
                        list.add(criteriaBuilder.le(root.get("time").as(Long.class), format.parse(recordBO.getEndTime()).getTime()));
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                if (StringUtils.isNotBlank(recordBO.getUsername())) {
                    list.add(criteriaBuilder.like(root.get("username").as(String.class), "%" + recordBO.getUsername() + "%"));
                }

                if (StringUtils.isNotBlank(recordBO.getTarget())) {
                    list.add(criteriaBuilder.like(root.get("target").as(String.class), "%" + recordBO.getTarget() + "%"));
                }

                if (StringUtils.isNotBlank(recordBO.getResult())) {
                    list.add(criteriaBuilder.like(root.get("result").as(String.class), "%" + recordBO.getResult() + "%"));
                }

                if (StringUtils.isNotBlank(recordBO.getEventAnalysis())) {
                    list.add(criteriaBuilder.like(root.get("eventAnalysis").as(String.class), "%" + recordBO.getEventAnalysis() + "%"));
                }


                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            }
        }, new PageRequest(recordBO.getPage() - 1, recordBO.getLimit(), new Sort(Sort.Direction.DESC, "createdTime")));
        Map<String, Object> result = new HashMap<>();
        result.put("total", recordEntities.getTotalElements());
        result.put("data", recordEntities.getContent());
        return result;
    }

    @Override
    public void deleteById(String id) {
        auditRecordRepository.delete(id);
    }

}
