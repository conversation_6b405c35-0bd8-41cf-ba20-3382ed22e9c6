package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class StoreInRecordPageRequest extends AdvancedPageableQueryEntity<StoreInRecord, StoreInRecordPageRequest> {

    // 入库单编号
    private String code;

    // 发票编号
    private String invoiceCode;

    // 相关合同编号
    private String contractCode;

    // 入库单标题
    private String title;

    // 目标仓库ID
    private String storehouseId;

    // 验收人ID
    private String acceptor;

    // 验收人部门ID
    private String acceptorDepartmentId;

    // 经办人Id
    private String manager;

    // 经办人部门Id
    private String managerDepartmentId;

}
