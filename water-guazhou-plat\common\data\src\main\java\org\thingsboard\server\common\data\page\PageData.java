package org.thingsboard.server.common.data.page;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页返回
 */
@Data
public class PageData<T> {

    private long total;
    private List<T> data;

    public PageData() {
    }

    public PageData(long total, List<T> data) {
        this.total = total;
        this.data = data;
    }
    public PageData(List<T> data){
        this.data = data;
    }

    public static <T> PageData<T> page(List<T> list, int page, int size) {
        page = page - 1;
        if (!list.isEmpty()) {
            int dataSize = list.size();;
            List<T> pageResultList = new ArrayList<>();
            if (dataSize <= size) {
                pageResultList = list;
            } else {
                int start = page * size;
                int end = ((page + 1) * size) - 1;
                int i = 0;
                for (int j = 0; j < list.size(); j++) {
                    if (j > end) {
                        break;
                    }
                    if (i >= start && i <= end) {
                        pageResultList.add(list.get(j));
                    }
                    i++;
                }
            }

            return new PageData<>(list.size(), pageResultList);
        }

        return new PageData<>(0, new ArrayList<>());
    }
}
