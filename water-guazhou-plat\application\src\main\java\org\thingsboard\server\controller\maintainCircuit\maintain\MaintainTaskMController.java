package org.thingsboard.server.controller.maintainCircuit.maintain;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.maintain.MaintainTaskMService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/maintain/task/m")
public class MaintainTaskMController extends BaseController {

    @Autowired
    private MaintainTaskMService maintainTaskMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String code,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam(required = false, defaultValue = "") String planName,
                                 @RequestParam(required = false, defaultValue = "") String teamName,
                                 @RequestParam(required = false, defaultValue = "") String userName,
                                 @RequestParam(required = false, defaultValue = "") String userId,
                                 @RequestParam(required = false, defaultValue = "") String status,
                                 @RequestParam(required = false, defaultValue = "") String auditStatus,
                                 Long startStartTime,
                                 Long startEndTime,
                                 Long endStartTime,
                                 Long endEndTime,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(maintainTaskMService.getList(code, name, planName, teamName, userName, userId, status, auditStatus, startStartTime == null ? null : new Date(startStartTime), startEndTime == null ? null : new Date(startEndTime), endStartTime == null ? null : new Date(endStartTime), endEndTime == null ? null : new Date(endEndTime), page, size, tenantId));
    }


    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(maintainTaskMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody MaintainTaskM maintainTaskM) throws ThingsboardException {
        maintainTaskM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        maintainTaskM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(maintainTaskMService.save(maintainTaskM));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return maintainTaskMService.delete(ids);
    }


    @PostMapping("changeStatus")
    public IstarResponse changeStatus(@RequestBody MaintainTaskM maintainTaskM) throws ThingsboardException {
        maintainTaskMService.reviewer(maintainTaskM);

        return IstarResponse.ok("操作成功");
    }

    @PostMapping("reviewer")
    public IstarResponse reviewer(@RequestBody MaintainTaskM maintainTaskM) throws ThingsboardException {
        boolean checkAuditor = this.checkAuditor(maintainTaskM.getId(), UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        if (!checkAuditor) {
            return IstarResponse.error("您没有审核权限");
        }
        maintainTaskM.setAuditor(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        maintainTaskMService.reviewer(maintainTaskM);

        return IstarResponse.ok("审核成功");
    }

    @GetMapping("receive/{id}")
    public IstarResponse receive(@PathVariable String id) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return maintainTaskMService.receive(id, userId);
    }

    @GetMapping("notCompleteNum")
    public IstarResponse getNotCompleteNum() throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(maintainTaskMService.getNotCompleteNum(userId, tenantId));
    }

    private boolean checkAuditor(String id, String userId) {
        return maintainTaskMService.checkAuditor(id, userId);
    }

    /**
     * 统计
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("statistics")
    public IstarResponse statistics(Long startTime, Long endTime) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(maintainTaskMService.statistics(startTime, endTime, tenantId));
    }
}
