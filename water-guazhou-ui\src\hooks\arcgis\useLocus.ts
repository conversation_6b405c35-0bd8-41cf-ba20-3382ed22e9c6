import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js'
import Point from '@arcgis/core/geometry/Point.js'
import Polyline from '@arcgis/core/geometry/Polyline.js'
import {
  calcDeltaXY,
  calcLength,
  createGraphic,
  getGraphicLayer,
  gotoAndHighLight,
  setSymbol
} from '@/utils/MapHelper'
import { delay } from '@/utils/GlobalHelper'

export const useLocus = () => {
  const running = ref<boolean>(false)
  let view: __esri.MapView | undefined
  let symBol: __esri.PictureMarkerSymbol | undefined
  let personLayer: __esri.GraphicsLayer | undefined
  /** 轨迹点坐标 */
  let vertices: number[][] = []
  /** `画轨迹线的坐标 */
  const locusVertices: number[][] = []
  let locusLayer: __esri.GraphicsLayer | undefined
  /** 当前经过点的索引 */
  let index = 0
  /** 记录某一直线上按步长一步一步行进时，进行到第几步了 */
  let cIndex = 0
  /** 计时器 */
  // const timer: any = undefined
  /** 速度，单位米/s */
  let step = 3.3
  /** 时间时隔，ms */
  const interval = 33
  /**
   * 对点数组中的坐标分步进行移动
   * @param point Point
   * @param duration 移动时长
   * @param angle 角度
   */
  const gotoPoints = async (
    coords: number[][],
    duration: number,
    angle: number,
    startIndex = 0,
    lastStep?: number
  ) => {
    cIndex = startIndex || 0
    // 检测路径是否跑完重置播放状态
    if (index >= vertices.length) {
      running.value = false
    }
    // 不在播放状态退出
    if (!running.value) return
    // 当前点位是该段线的倒数第二步，则开始计算最后一步，因为最后一步长度一定比步长短，所以需要单独计算
    if (startIndex === coords.length) {
      if (coords.length > 1) {
        const duration = ((lastStep || 0) / step) * interval
        // 最后一步，因为单独计算，只有一个点，所以startIndex设置为0
        await gotoPoints([vertices[index + 1]], duration, angle, 0)
        return
      }
      // 重置起始步数
      cIndex = 0
      // 移动到下一条记录索引
      index++
      // 执行路径行进
      passLine()
      return
    }
    // symBol && (symBol.angle = angle)
    await delay(duration)
    _addMark(coords[startIndex])
    _addLocus(coords[startIndex])
    await gotoPoints(coords, duration, angle, ++startIndex, lastStep)
  }
  const _addLocus = (coord?: number[]) => {
    if (!coord) return
    locusVertices.push(coord)
    const locus = createGraphic({
      geometry: new Polyline({
        paths: [locusVertices],
        spatialReference: view?.spatialReference
      }),
      symbol: setSymbol('polyline', {
        color: '#ff0000',
        width: 2
      })
    })
    locusLayer?.removeAll()
    locusLayer?.add(locus)
  }
  const _addMark = (coord?: number[]) => {
    personLayer?.removeAll()
    if (!coord) return
    const graphic = createGraphic({
      geometry: new Point({
        x: coord[0],
        y: coord[1],
        spatialReference: view?.spatialReference
      }),
      symbol: symBol
    })
    personLayer?.add(graphic)
    return graphic
  }
  /**
   * 通过一条线
   */
  const passLine = async () => {
    if (!running.value) return
    if (index >= vertices.length - 1) {
      running.value = false
      index = 0
      locusVertices.length = 0
      return
    }
    const point1 = vertices[index]
    const point2 = vertices[index + 1]
    const length = calcLength(
      [point1, point2],
      'meters',
      view?.spatialReference
    )
    const angle = _calcAngle([
      [point1[0], point1[1] + 10],
      [point1[0], point1[1]],
      [point2[0], point2[1]]
    ])
    // 如果线长小于步长，由只有两点，从起点到终点，时长为线长与步长比例乘以早间间隔
    if (length < step) {
      const duration = (length / step) * interval
      // 直接往最该线条终点行进
      await gotoPoints([point2], duration, angle, 0)
    } else {
      // 插值，两点间按步长插入多个点
      const stepDouble = length / step
      const stepsInt = Math.floor(stepDouble)
      const lastStep = stepDouble - stepsInt
      const delt = calcDeltaXY(step, angle)
      const points = Array.from({ length: stepsInt }).map((item, i) => {
        return [point1[0] + i * delt.x, point1[1] + i * delt.y]
      })
      // 先行进此线条的完整步长，再行进最后不中步长的一小段
      await gotoPoints(points, interval, angle, cIndex, lastStep)
    }
  }

  /**
   * 计算角度
   * @param vertices 长度必须是三个
   * @param unit
   * @param spatialReference
   * @returns
   */
  const _calcAngle = (vertices: number[][]): number => {
    // 只当有三个点时才能计算结果
    if (vertices?.length !== 3) return 0
    // 起点到中间点的向量
    const point1: number[] = []
    point1.push(vertices[0][0] - vertices[1][0])
    point1.push(vertices[0][1] - vertices[1][1])
    // 终点到中间点的向量
    const point2: number[] = []
    point2.push(vertices[2][0] - vertices[1][0])
    point2.push(vertices[2][1] - vertices[1][1])

    let result: number
    if (point1[0] * point2[1] === point1[1] * point2[0]) {
      result = 180
    } else {
      // 两向量的点乘
      const pointMultiply: number = point1[0] * point2[0] + point1[1] * point2[1]
      // 求出夹角的弧度
      const angle = Math.acos(
        pointMultiply
          / Math.sqrt(point1[0] ** 2 + point1[1] ** 2)
          / Math.sqrt(point2[0] ** 2 + point2[1] ** 2)
      )
      // 转换为弧度
      result = (angle * 180) / Math.PI
      // 处理大于180时的情况
      if (point2[0] < 0) {
        result = 360 - result
      }
    }
    return result
  }
  const start = (startIndex?: number, speed?: number) => {
    index = startIndex === undefined ? index || 0 : startIndex
    step = (speed || 100) * 0.033
    running.value = true
    if (index === 0 && cIndex === 0) {
      locusLayer?.removeAll()
    }
    passLine()
  }
  const setSpeed = async (speed: number) => {
    step = (speed || 100) * 0.033
    if (running.value === true) {
      running.value = false
      cIndex = 0
      await delay(interval)
      running.value = true
      passLine()
    }
  }
  const pause = () => {
    running.value = false
  }
  const stop = async () => {
    running.value = false
    await delay(interval)
    index = 0
    cIndex = 0
    locusVertices.length = 0
    locusLayer?.removeAll()
    personLayer?.removeAll()
    locusVertices.length = 0
    const g = _addMark(vertices[0])
    gotoAndHighLight(view, g, {
      zoom: 15,
      duration: 500,
      avoidHighlight: true
    })
  }
  const init = (options: {
    mapView?: __esri.MapView
    path?: number[][]
    pic?: __esri.PictureMarkerSymbolProperties
  }) => {
    view = options.mapView || view
    vertices = options.path || []
    locusLayer = getGraphicLayer(view, {
      id: 'locus',
      title: '移动轨迹'
    })
    personLayer = getGraphicLayer(view, {
      id: 'person',
      title: '人员'
    })
    stop()
    symBol = new PictureMarkerSymbol(options.pic)
  }
  const destroy = () => {
    personLayer && view?.map?.remove(personLayer)
    locusLayer && view?.map?.remove(locusLayer)
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init,
    pause,
    stop,
    start,
    destroy,
    setSpeed,
    running
  }
}
