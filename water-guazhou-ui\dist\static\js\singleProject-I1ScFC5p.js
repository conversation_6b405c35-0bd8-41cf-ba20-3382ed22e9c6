import{d as S,M as F,c as b,a8 as y,s as W,r as m,S as T,x as o,a9 as V,o as Y,g as A,n as B,q as u,i as c,F as O,b6 as q,b7 as z}from"./index-r0dFAfgr.js";import{_ as U}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as $}from"./CardTable-rdWOL4_6.js";import{_ as G}from"./CardSearch-CB_HNR-Q.js";import{I as _}from"./common-CvK_P_ao.js";import{_ as Q,$ as H,a0 as J,g as K,x as X,c as Z}from"./manage-BReaEVJk.js";import{f as ee,h as te,g as ae}from"./projectManagement-CDcrrCQ1.js";import{j as ie}from"./device-DWHb0XjG.js";import{f as w}from"./DateFormatter-Bm9a68Ax.js";import le from"./detail-DEo1RlcF.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const oe={class:"wrapper"},qe=S({__name:"singleProject",setup(re){const{$btnPerms:g}=F(),C=b(),v=b(),k=b(),x=b(),L=b(),N=b({filters:[{label:"所属项目",field:"projectCode",type:"select",options:y(()=>i.projectList)},{label:"工程编号",field:"code",type:"input"},{label:"工程名称",field:"name",type:"input"},{label:"地址",field:"address",type:"input"},{label:"提交时间",field:"time",type:"daterange"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:_.DOWNLOAD,click:()=>{Q().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","单项工程.xlsx"),document.body.appendChild(a),a.click()})}},{perm:g("RoleManageAdd"),text:"新增",type:"success",icon:_.ADD,click:()=>M()},{type:"default",perm:!0,text:"重置",svgIcon:W(z),click:()=>{var e;(e=C.value)==null||e.resetForm(),f()}},{perm:!0,text:"查询",icon:_.QUERY,click:()=>f()}]}]}),d=m({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"工程名称",prop:"name"},{label:"所属项目",prop:"projectName"},{label:"工程地址",prop:"address"},{label:"工程类别",prop:"typeName"},{label:"工程预算(万元)",prop:"estimate"},{label:"甲方代表",prop:"firstpartName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>w(e.createTime,"YYYY-MM-DD")},{label:"设备状态",prop:"cs"}],operationWidth:"350px",operations:[{isTextBtn:!1,text:"详情",perm:g("RoleManageEdit"),click:e=>{var t;i.selected=e,(t=L.value)==null||t.openDrawer()}},{isTextBtn:!1,text:"查看设备",perm:g("RoleManageEdit"),click:e=>{var t;h.defaultValue={code:e.code},i.getProjectDeviceValue(),(t=k.value)==null||t.openDialog()}},{isTextBtn:!1,type:"success",text:"编辑",perm:g("RoleManageEdit"),click:e=>P(e)},{disabled:e=>!e.canBeDelete,isTextBtn:!1,type:"danger",text:"删除",perm:g("RoleManageEdit"),click:e=>{T("确定删除？","提示信息").then(()=>{ee(e.id).then(t=>{var a;((a=t.data)==null?void 0:a.code)===200?(o.success("删除成功"),f()):o.warning("删除失败")}).catch(t=>{o.warning(t)})})}},{isTextBtn:!1,text:"添加设备",perm:g("RoleManageEdit"),click:e=>{var a;i.selected=e,p.defaultValue={code:e.code};const t=p.group[0].fields.find(l=>l.type==="table");t.config.selectList=[],i.geDeviceListValue(),(a=x.value)==null||a.openDialog()}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{d.pagination.page=e,d.pagination.limit=t,f()}}}),h=m({title:"查看设备",labelWidth:"130px",dialogWidth:"1000px",defaultValue:{},group:[{fields:[{type:"table",field:"deviceTable",config:{indexVisible:!0,height:"350px",dataList:y(()=>i.deviceInformation),columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"amount"}],operations:[{perm:!0,text:"删除",icon:_.DELETE,click:e=>{T("确定删除该设备","删除提示").then(()=>{te(e.id).then(()=>{o.success("删除成功"),i.getProjectDeviceValue()}).catch(t=>{o.warning(t)})})}}],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{const a=h.group[0].fields.find(l=>l.type==="table");a.config.pagination.page=e,a.config.pagination.limit=t,i.getProjectDeviceValue()}}}}]}]}),s=m({title:"添加项目",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{s.submitting=!0;let t="新增";e.id&&(t="修改"),H(e).then(a=>{var l;s.submitting=!1,a.data.code===200?(o.success(t+"成功"),(l=v.value)==null||l.closeDialog(),f()):o.warning(t+"失败")}).catch(a=>{s.submitting=!1,o.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"code"},{xs:12,type:"input",label:"工程名称",field:"name",rules:[{required:!0,message:"请输入工程名称"}]},{xs:12,type:"select",label:"项目名称",field:"projectCode",options:y(()=>i.projectList),rules:[{required:!0,message:"请选择项目名称"}]},{xs:12,type:"input",label:"项目编号",disabled:!0,field:"projectCode"},{xs:12,type:"select",label:"工程类别",field:"typeId",options:y(()=>i.projectType)},{xs:12,type:"date",label:"创建时间",field:"createTime",format:"x"},{xs:12,type:"input",label:"甲方代表",field:"firstpartName",rules:[{required:!0,message:"请输入甲方代表"}]},{xs:12,type:"input",label:"联系电话",field:"firstpartPhone"},{xs:12,type:"input",label:"工程预算(万元)",field:"estimate",rules:[{required:!0,message:"请输入工程预算"}]},{type:"input",label:"详细地址",field:"address"},{type:"textarea",label:"工程概况",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),p=m({title:"添加设备",labelWidth:"80px",dialogWidth:"1000px",defaultValue:{},submitting:!1,submit:(e,t)=>{var a,l;if(t)i.geDeviceListValue(e);else{let r=!1;const D=p.group[0].fields.find(n=>n.type==="table");((a=D.config.selectList)==null?void 0:a.length)===0&&(o.warning("请选中设备"),r=!0);const R=(l=D.config.selectList)==null?void 0:l.map(n=>((n.number===0||!n.number)&&(o.warning("数量最少为1台"),r=!0),n.number>n.rest&&(o.warning("申请数量超过剩余数量"),r=!0),{serialId:n.serialId,amount:n.number||0}));if(r)return;p.submitting=!0,J(e.code,R).then(n=>{var j;p.submitting=!1,n.data.code===200?(o.success("添加成功"),(j=x.value)==null||j.closeDialog(),f()):o.warning("添加失败")}).catch(n=>{p.submitting=!1,o.warning(n)})}},group:[{fields:[{xs:6,type:"input",field:"serialId",label:"设备编码"},{xs:6,type:"input",field:"name",label:"设备名称"},{xs:6,type:"input",field:"model",label:"设备型号"},{xs:6,type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>{var e;(e=x.value)==null||e.Submit(!0)}},{text:"重置",perm:!0,type:"default",click:()=>{var e;(e=x.value)==null||e.resetForm(),i.geDeviceListValue()}}]},{type:"table",config:{indexVisible:!0,height:"350px",dataList:y(()=>i.deviceInformation),handleSelectChange:e=>{const t=p.group[0].fields.find(a=>a.type==="table");t.config.selectList=e},selectList:[],columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"rest"},{label:"申请数量",prop:"number",minWidth:"120px",formItemConfig:{type:"number",field:"number",min:0}}],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{const a=p.group[0].fields.find(l=>l.type==="table");a.config.pagination.page=e,a.config.pagination.limit=t,i.geDeviceListValue()}}}}]}]}),I=m({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),E=m({defaultValue:y(()=>i.selected),border:!1,direction:"horizontal",column:1,title:"项目基础信息",fields:[{type:"text",label:"工程编号:",field:"code"},{type:"text",label:"工程名称:",field:"name"},{type:"text",label:"项目名称:",field:"projectName"},{type:"text",label:"项目编号:",field:"projectCode"},{type:"text",label:"工程类别:",field:"typeName"},{type:"text",label:"甲方代表:",field:"firstpartName"},{type:"text",label:"联系电话:",field:"firstpartPhone"},{type:"text",label:"工程预算(万元):",field:"estimate"},{type:"text",label:"详细地址:",field:"address"},{type:"text",label:"工程概况:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName",formatter:e=>w(e,"YYYY-MM-DD")},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]}),M=()=>{var e;s.title="新增",s.defaultValue={},(e=v.value)==null||e.openDialog()},P=e=>{var t;s.title="编辑",s.defaultValue={...e||{}},(t=v.value)==null||t.openDialog()},i=m({projectList:[],projectType:[],deviceInformation:[],selected:{},getOptions:()=>{ae({page:1,size:-1}).then(e=>{i.projectList=V(e.data.data.data||[],"children",{label:"name",value:"code"})}),K({page:1,size:-1}).then(e=>{i.projectType=V(e.data.data.data||[])})},geDeviceListValue:e=>{var l;i.deviceInformation=[];const t=p.group[0].fields.find(r=>r.type==="table"),a={page:t.config.pagination.page||1,size:t.config.pagination.limit||20,...e};ie((l=i.selected)==null?void 0:l.projectCode,a).then(r=>{i.deviceInformation=r.data.data.data||[],t.config.pagination.total=r.data.data.total||0})},getProjectDeviceValue:()=>{var a;i.deviceInformation=[];const e=h.group[0].fields.find(l=>l.type==="table"),t={page:e.config.pagination.page||1,size:e.config.pagination.limit||20};X((a=h.defaultValue)==null?void 0:a.code,t).then(l=>{i.deviceInformation=l.data.data.data||[],e.config.pagination.total=l.data.data.total||0})}}),f=async()=>{var t;const e={size:d.pagination.limit||20,page:d.pagination.page||1,...((t=C.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.fromTime=e.time[0],e.toTime=e.time[1],delete e.time),Z(e).then(a=>{d.dataList=a.data.data.data||[],d.pagination.total=a.data.data.total||0})};return Y(()=>{f(),i.getOptions(),i.geDeviceListValue()}),(e,t)=>{const a=G,l=$,r=U,D=q;return A(),B("div",oe,[u(a,{ref_key:"refSearch",ref:C,config:c(N)},null,8,["config"]),u(l,{config:c(d),class:"card-table"},null,8,["config"]),u(r,{ref_key:"refDeviceForm",ref:k,config:c(h)},null,8,["config"]),u(r,{ref_key:"refForm",ref:v,config:c(s)},null,8,["config"]),u(r,{ref_key:"refAddDeviceForm",ref:x,config:c(p)},null,8,["config"]),u(D,{ref_key:"refDetail",ref:L,config:c(I)},{default:O(()=>[u(le,{config:c(i).selected,basic:c(E),show:3},null,8,["config","basic"])]),_:1},8,["config"])])}}});export{qe as default};
