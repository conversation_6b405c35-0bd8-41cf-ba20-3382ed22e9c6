package org.thingsboard.server.dao.sql.smartManagement.district;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrict;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPageRequest;

import java.util.List;

@Mapper
public interface CircuitDistrictMapper extends BaseMapper<SMCircuitDistrict> {
    IPage<SMCircuitDistrict> findByPage(CircuitDistrictPageRequest request);

    boolean update(SMCircuitDistrict entity);

    SMCircuitDistrict getTopRoot();

    List<SMCircuitDistrict> findChildren(@Param("parentId") String parentId, @Param("tenantId") String tenantId);

    List<SMCircuitDistrict> findActualRoots(String tenantId);

    int delete(@Param("id")String id);

    int updateFully(SMCircuitDistrict entity);

    String getNameById(String id);
}
