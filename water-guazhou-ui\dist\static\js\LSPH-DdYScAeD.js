import{_ as w}from"./index-C9hz-UZb.js";import{d as S,r as C,j as k,am as L,c as b,o as z,ay as M,g as O,h as j,F as h,p as n,q as d,i as y,cs as V,a7 as A,C as P}from"./index-r0dFAfgr.js";import"./index-0NlGN6gS.js";import{u as B}from"./useDetector-BRcb7GRN.js";import{c as I}from"./statistics-CeyexT_5.js";const T={class:"card-header"},W={class:"left"},G=S({__name:"LSPH",setup(F){const i=C({lsphOption:null}),l=k(),c=(e,o)=>{const a=Math.max(...e)||100,t=l.isDark,g=o.map((r,s)=>({name:r,value:e[s],num:e[s]})),v=o.map((r,s)=>({name:r,value:a,label:{show:!0,position:"right",fontSize:14,color:t?"rgba(255,255,255,0.8)":"rgba(0,0,0,0.8)",offset:[16,0],formatter(){return e[s]}}})),x=o.map((r,s)=>({name:r,value:e[s],label:e[s]}));return{tooltip:{axisPointer:{type:"shadow"}},grid:{top:0,left:10,right:80,bottom:0,containLabel:!0},xAxis:{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},position:"top"},yAxis:{type:"category",data:o,inverse:!0,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:t?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)",fontSize:14,fontFamily:"TencentSans"}}},dataZoom:[{type:"slider",show:!0,backgroundColor:"rgba(0,0,0,0)",fillerColor:t?"rgba(255,255,255,0.3)":"rgba(0,0,0,0.6)",borderColor:"rgb(0,0,0,0.25)",showDetail:!1,startValue:0,endValue:5,yAxisIndex:[0],filterMode:"empty",width:8,right:3,handleSize:0,zoomLoxk:!0,top:10,height:"90%"},{type:"inside",yAxisIndex:[0,1],zoomOnMouseWheel:!1,moveOnMouseMove:!0,moveOnMouseWheel:!0}],series:[{type:"bar",barGap:"-100%",barWidth:14,z:1,itemStyle:{color:new A(0,0,1,0,[{offset:0,color:"rgba(0,255,255,1)"},{offset:1,color:"rgba(255,0,0,1)"}],!1)},data:g},{type:"bar",barWidth:14,z:0,itemStyle:{color:t?"rgba(26, 49, 99, 1)":"rgba(26, 49, 99, 0.1)"},tooltip:{show:!1},data:v},{type:"pictorialBar",symbolRepeat:"fixed",symbolMargin:6,symbol:"rect",z:2,symbolClip:!0,symbolSize:[1,14],symbolPosition:"start",itemStyle:{color:t?"rgba(255,255,255,0.6)":"rgba(0,0,0,0.6)"},data:x}]}},p=()=>{I().then(e=>{const{x:o,y:a}=e.data.data||{};i.lsphOption=c(a||[],o||[])}).catch(()=>{i.lsphOption=c([],[])})};L(()=>l.isDark,()=>{p()});const _=B(),f=b(),m=b();return z(()=>{p(),_.listenToMush(m.value,()=>{var e;(e=f.value)==null||e.resize()})}),(e,o)=>{const a=M("VChart"),t=w;return O(),j(t,{title:" "},{title:h(()=>[n("div",T,[n("div",W,[d(y(V),{icon:"material-symbols:water-drop-outline"}),o[0]||(o[0]=n("span",null,"漏水排行榜",-1))])])]),default:h(()=>[n("div",{ref_key:"refDiv",ref:m,class:"chart-box"},[d(a,{ref_key:"refChart",ref:f,option:y(i).lsphOption},null,8,["option"])],512)]),_:1})}}}),E=P(G,[["__scopeId","data-v-3119196f"]]);export{E as default};
