<template>
  <!--信息弹框页-->
  <SLDialog
    v-if="slDialogConfig.visible"
    :config="slDialogConfig"
  >
    <el-row :gutter="20">
      <el-col :span="3">
        <el-tabs
          v-model="activeName"
          tab-position="left"
          @tab-click="handleClick"
        >
          <el-tab-pane
            label="设备信息"
            name="deviceinfo"
          >
          </el-tab-pane>
          <el-tab-pane
            label="维修信息"
            name="repairinfo"
          >
          </el-tab-pane>
          <el-tab-pane
            label="保养信息"
            name="maintenanceinfo"
          >
          </el-tab-pane>
          <el-tab-pane
            label="巡检信息"
            name="patrolinfo"
          >
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="21">
        <el-scrollbar height="100%">
          <DeviceInfo
            v-if="activeName === 'deviceinfo'"
            :id="props.id"
            :serial-id="props.serialId"
            @changeCurrentId="changeCurrentId"
          />
          <RepairInfo
            v-if="activeName === 'repairinfo'"
            :id="RCode.currentId"
          />
          <MaintenanceInfo
            v-if="activeName === 'maintenanceinfo'"
            :id="RCode.currentId"
          />
          <PatrolInfo
            v-if="activeName === 'patrolinfo'"
            :id="RCode.currentId"
          />
        </el-scrollbar>
      </el-col>
    </el-row>
  </SLDialog>
</template>

<script lang="ts" setup>
// 设备信息
import { PropType } from 'vue'
import DeviceInfo from './deviceInfo.vue'
// 维修信息
import RepairInfo from './repairinfo.vue'
// 保养信息
import MaintenanceInfo from './maintenanceinfo.vue'
// 巡检信息
import PatrolInfo from './patrolinfo.vue'
import { ISLDialogConfig } from '@/components/SLDialog/type'

const props = defineProps({
  width: {
    type: [String, Number],
    default: '60%'
  },
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  close: {
    type: Function as PropType<() => void>,
    default: () => {
      //
    }
  },
  currentId: {
    type: String,
    default: ''
  },
  readonly: {
    type: Boolean,
    default: false
  }, //    编号
  deviceNo: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  serialId: {
    type: String,
    default: ''
  }
})
defineEmits(['sendMsgIds', 'sendMsgList'])

const slDialogConfig = ref<ISLDialogConfig>({
  title: props.title || '设备详情',
  visible: props.visible,
  scrollbar: false,
  cancel: {
    handler: async () => await props.close()
  }
})

watch(
  () => props.visible,
  () => {
    slDialogConfig.value.visible = props.visible
    slDialogConfig.value.cancel = {
      handler: async () => await props.close()
    }
    slDialogConfig.value.width = props.width
  },
  {
    deep: true,
    immediate: true
  }
)

const activeName = ref('deviceinfo')
// 1.定义变量
// 2.方法
// 挂载后再加载数据
onMounted(async () => {
  init()
})
const RCode = ref<any>({
  currentId: '',
  url: ''
})

const init = async () => {
  RCode.value.currentId = props.currentId
}
// 修改当前的设备id
const changeCurrentId = (deviceid: string) => {
  RCode.value.currentId = deviceid
}

const handleClick = (tab: any) => {
  console.log('tab ', tab)
}
</script>

<style lang="scss" scoped></style>
