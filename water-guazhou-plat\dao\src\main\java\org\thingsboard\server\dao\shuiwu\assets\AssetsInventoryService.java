package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsInventoryEntity;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
public interface AssetsInventoryService {
    PageData getPage(JSONObject params);

    AssetsInventoryEntity save(AssetsInventoryEntity inventoryEntity, String userId) throws ThingsboardException;

    void delete(List<String> idList);
}
