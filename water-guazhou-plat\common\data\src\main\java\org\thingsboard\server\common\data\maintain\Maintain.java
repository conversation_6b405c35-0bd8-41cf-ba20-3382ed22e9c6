/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.maintain;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.MaintainId;
import org.thingsboard.server.common.data.id.TenantId;

@Data
public class Maintain extends BaseData<MaintainId> {

    private DeviceId deviceId;
    private Long createTime;
    private String name;
    private JsonNode additionalInfo;
    private TenantId tenantId;
    private String maintainJson;
    private String params;
    //周期类型
    private String type;
    //周期长度
    private String period;
    private String projectId;
    private String deviceName;

    public Maintain() {
        super();
    }

    public Maintain(MaintainId id) {
        super(id);
    }

    public Maintain(Maintain maintain) {
        super();
        this.deviceId = maintain.getDeviceId();
        this.createTime = maintain.getCreateTime();
        this.additionalInfo = maintain.getAdditionalInfo();
        this.tenantId = maintain.getTenantId();
        this.maintainJson = maintain.getMaintainJson();
        this.name = maintain.getName();
        this.params = maintain.getParams();
        this.type = maintain.getType();
        this.period = maintain.getPeriod();
    }

}
