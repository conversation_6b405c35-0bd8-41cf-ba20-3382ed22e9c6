package org.thingsboard.server.dao.stationSetting;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationArchivesListRequest;
import org.thingsboard.server.dao.model.sql.stationSetting.StationArchives;
import org.thingsboard.server.dao.sql.stationSetting.StationArchivesMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class StationArchivesServiceImpl implements StationArchivesService {

    @Autowired
    private StationArchivesMapper stationArchivesMapper;


    @Override
    public void save(StationArchives entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            stationArchivesMapper.insert(entity);
        } else {
            stationArchivesMapper.updateById(entity);
        }
    }

    @Override
    public PageData<StationArchives> findList(StationArchivesListRequest request, TenantId tenantId) {
        Page<StationArchives> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<StationArchives> pageResult = stationArchivesMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void remove(List<String> ids) {
        stationArchivesMapper.deleteBatchIds(ids);
    }

    @Override
    public List<StationArchives> findAll(TenantId tenantId) {
        QueryWrapper<StationArchives> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        return stationArchivesMapper.selectList(queryWrapper);
    }

}
