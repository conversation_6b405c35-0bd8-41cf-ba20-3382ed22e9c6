package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("api/call/")
public class ApiCallController {

    @GetMapping("get")
    public Object callGetRequest(@RequestParam String url) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);

        CloseableHttpResponse execute = httpClient.execute(httpGet);
        String resultString = EntityUtils.toString(execute.getEntity());
        try {
            return JSON.parseArray(resultString);
        } catch (Exception e) {
            return JSON.parseObject(resultString);
        }
    }

    @PostMapping("post")
    public Object callPostRequest(@RequestBody JSONObject params) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpUriRequest http = getHttp(params.getString("method"), params.getString("url"), params.getJSONObject("params"));
        CloseableHttpResponse execute = httpClient.execute(http);
        String resultString = EntityUtils.toString(execute.getEntity());

        try {
            return JSON.parseArray(resultString);
        } catch (Exception e) {
            return JSON.parseObject(resultString);
        }
    }

    private HttpUriRequest getHttp(String method, String url, JSONObject params) throws Exception {
        if ("get".equalsIgnoreCase(method)) {
            return new HttpGet(url);
        } else if ("post".equalsIgnoreCase(method)) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setEntity(new StringEntity(params.toJSONString()));
            return httpPost;
        }
        return null;
    }

}
