// import Layout from '@/views/layout/frame/Layout.vue'

export default [
  // 企业管理
  {
    path: '/tenant',
    component: () => import('@/views/layout/frame/Layout.vue'),
    hidden: false,
    meta: {
      title: '企业管理',
      icon: 'iconfont icon-zutai',
      roles: ['SYS_ADMIN']
    },
    children: [
      {
        path: 'sysTenant',
        name: 'sysTenant',
        component: () => import('@/views/tenant/sysTenant.vue'),
        meta: {
          title: '企业管理',
          icon: 'iconfont icon-zutai',
          roles: ['SYS_ADMIN']
        }
      }
    ]
  },
  // 系统设置
  {
    path: '/sys-admin',
    component: () => import('@/views/layout/frame/Layout.vue'),
    hidden: false,
    alwaysShow: true,
    name: 'sys-admin',
    meta: {
      title: '系统设置',
      icon: 'iconfont icon-shezhi',
      roles: ['SYS_ADMIN']
    },
    children: [
      {
        path: 'user',
        name: 'sys-user',
        component: () => import('@/views/userManage/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'iconfont icon-shezhi',
          roles: ['SYS_ADMIN']
        }
      },
      {
        path: 'role',
        name: 'sys-role',
        component: () => import('@/views/roleManage/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'iconfont icon-shezhi',
          roles: ['SYS_ADMIN']
        }
      },
      {
        path: 'loginLog',
        name: 'sys-login',
        component: () => import('@/views/dailyLog/loginLog.vue'),
        meta: {
          title: '登录日志',
          icon: 'iconfont icon-shezhi',
          roles: ['SYS_ADMIN']
        }
      },
      {
        path: 'operationLog',
        name: 'sys-operation',
        component: () => import('@/views/dailyLog/operationLog.vue'),
        meta: {
          title: '操作日志',
          icon: 'iconfont icon-shezhi',
          roles: ['SYS_ADMIN']
        }
      },
      {
        path: 'backup',
        name: 'backup',
        component: () => import('@/views/sysManage/dataBackup.vue'),
        meta: {
          title: '备份恢复',
          icon: 'iconfont icon-shezhi',
          roles: ['SYS_ADMIN']
        }
      },
      {
        path: '/sourceManage',
        name: 'sourceManage',
        component: () => import('@/views/sysManage/sourceManage.vue'),
        meta: {
          title: '资源管理',
          icon: 'iconfont icon-shezhi',
          roles: ['SYS_ADMIN']
        }
      }
    ]
  },
  // 着陆页
  // {
  //   path: '/Portal',
  //   component: () => import('@/views/layout/frame/Layout.vue'),
  //   name: 'Portal',
  //   meta: {
  //     title: '门户',
  //     icon: 'iconfont icon-xunjianguanli',
  //     roles: ['SYS_ADMIN']
  //   },
  //   children: [
  //     {
  //       path: 'enterprise',
  //       name: 'enterprisemanage',
  //       meta: {
  //         title: '门户管理',
  //         icon: 'iconfont icon-zutai',
  //         roles: ['SYS_ADMIN']
  //       },
  //       component: () => import('@/views/portal/enterprise/index.vue')
  //     }
  //   ]
  // }
];
