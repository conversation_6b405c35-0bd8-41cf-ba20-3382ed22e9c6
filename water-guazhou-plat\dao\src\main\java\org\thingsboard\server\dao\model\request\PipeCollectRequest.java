package org.thingsboard.server.dao.model.request;

import lombok.Data;

/**
 * 管网采集
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-31
 */
@Data
public class PipeCollectRequest {

    private int page;

    private int size;

    private String name;

    private String code;

    private String status;

    private String type;

    private Long start;

    private Long end;

    private String creator;

    private String processUser;

    private String reviewUser;

    private String organizerName;

    private String processUserName;

    private String receiveDepartmentName;

    private String tenantId;
}
