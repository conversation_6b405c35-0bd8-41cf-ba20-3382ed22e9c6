package org.thingsboard.server.dao.model.sql.smartProduction.safeProduction;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("sp_run_score")
@NoArgsConstructor
@AllArgsConstructor
public class ProductionRunScore {

    private String id;

    private String type;

    private String month;

    private Double value;

    private Double score;

    private Date createTime;

}
