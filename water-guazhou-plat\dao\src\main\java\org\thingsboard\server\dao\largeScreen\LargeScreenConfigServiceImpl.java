package org.thingsboard.server.dao.largeScreen;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.largeScreen.LargeScreenConfig;
import org.thingsboard.server.dao.sql.largeScreen.LargeScreenConfigMapper;
import org.thingsboard.server.dao.util.imodel.query.largeScreen.LargeScreenConfigRequest;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class LargeScreenConfigServiceImpl implements LargeScreenConfigService {

    @Autowired
    private LargeScreenConfigMapper largeScreenConfigMapper;

    @Override
    public PageData<LargeScreenConfig> getList(LargeScreenConfigRequest largeScreenConfigRequest) {
        IPage<LargeScreenConfig> iPage = new Page<>(largeScreenConfigRequest.getPage(), largeScreenConfigRequest.getSize());
        iPage = largeScreenConfigMapper.getList(iPage, largeScreenConfigRequest);
        return new PageData<>(iPage.getTotal(), iPage.getRecords());
    }

    @Override
    public LargeScreenConfig save(LargeScreenConfig largeScreenConfig) {
        if (StringUtils.isBlank(largeScreenConfig.getId())) {
            largeScreenConfig.setCreateTime(new Date());
            largeScreenConfigMapper.insert(largeScreenConfig);
        } else {
            largeScreenConfigMapper.updateById(largeScreenConfig);
        }
        return largeScreenConfig;
    }

    @Override
    public void delete(List<String> idList) {
        largeScreenConfigMapper.deleteBatchIds(idList);
    }
}