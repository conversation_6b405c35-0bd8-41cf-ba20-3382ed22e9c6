import{d as X,a6 as Z,c as k,r as L,bB as O,ay as tt,g as at,h as et,F as V,p as M,q as D,i as ot,j as it,_ as rt,bt as st,aq as nt,c5 as pt,a0 as mt,C as lt}from"./index-r0dFAfgr.js";import{w as ct}from"./Point-WxyopZva.js";import{g as B,n as dt,m as ut}from"./MapView-DaoQedLH.js";import{u as ft}from"./useStation-DJgnSZIA.js";import{d as yt}from"./zhandian-YaGuQZe6.js";import gt from"./RightDrawerMap-D5PhmGFO.js";import{g as z}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as bt}from"./ViewHelper-BGCZjxXH.js";import{s as wt}from"./flowMonitoring-DtJlPj0G.js";import{a as vt}from"./headwaterMonitoring-BgK7jThW.js";import{p as G,l as ht}from"./echart-DxEZmJvB.js";import{r as $}from"./data-fN2LTS5W.js";import{G as _t}from"./onemap-CEunQziB.js";import{g as j}from"./URLHelper-B9aplt5w.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Dt={class:"content"},kt={class:"right-box"},Rt=X({__name:"index",setup(Ct){const H=Z(),W=k(),{getAllStationOption:q}=ft(),I=k(),N=k(),T=k(!0),x=k(),U=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],e=L({lineOption:null,pieOption:null,stationStatus:[],curRow:{},activeName:null,data:null,stationLocation:[],tableData:[],windows:[]}),b={},R=async t=>{var i,u,y,g,l,d,_;const o=w.dataList.find(c=>c.stationId===t),s=e.tableData.find(c=>c.stationId===t);f.tabs=(i=s==null?void 0:s.dataList)==null?void 0:i.map((c,v)=>({label:c.propertyName,value:c.property+v,data:c})),e.activeName=f.tabs?f.tabs[0].value:"",e.curRow=o,C(o,f.tabs?f.tabs[0].data:{});let a;if(t?(a=(g=b.view)==null?void 0:g.graphics.find(c=>{var v,h;return((h=(v=c.attributes)==null?void 0:v.row)==null?void 0:h.id)===t}),a&&await z(b.view,a,{zoom:15,avoidHighlight:!0})):a=(y=(u=b.view)==null?void 0:u.graphics)==null?void 0:y.getItemAt(0),!a)return;const n=((l=a.attributes)==null?void 0:l.row)||{},m=((d=(await yt(n.id)).data)==null?void 0:d.map(c=>(c.label=c.propertyName,c.value,c)))||[],r=a==null?void 0:a.geometry;e.windows.length=0,e.windows.push({visible:!1,x:r.x,y:r.y,offsetY:-30,title:n.name,attributes:{values:m,id:n.id}}),await O(),(_=I.value)==null||_.openPop(n.id)},f=L({type:"tabs",tabType:"simple",tabs:[],handleTabClick:t=>{const o=f.tabs.find(s=>s.value===t.props.name)||{};C(e.curRow,o.data)}}),A=L({group:[{id:"chart",fieldset:{desc:"流量监测设备",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:$(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),w=L({loading:!1,dataList:[],columns:[],highlightCurrentRow:!0,currentRowKey:"stationId",handleRowClick:async t=>{var a,n;const o=(a=b.view)==null?void 0:a.graphics.find(p=>{var m,r;return((r=(m=p.attributes)==null?void 0:m.row)==null?void 0:r.id)===t.stationId});o&&await z(b.view,o,{zoom:15,avoidHighlight:!0});const s=e.tableData.find(p=>p.stationId===t.stationId);f.tabs=(n=s==null?void 0:s.dataList)==null?void 0:n.map((p,m)=>({label:p.propertyName,value:p.property+m,data:p})),e.activeName=f.tabs[0].value,C(f.tabs[0].data),w.currentRow=t,e.curRow=t,R(t.stationId)},pagination:{hide:!0}}),Y=async()=>{var t;wt({stationType:"流量监测站,测流压站",projectId:(t=mt().selectedProject)==null?void 0:t.value,customName:!1}).then(o=>{var s,a,n,p,m,r,i;if(e.tableData=o.data,e.tableData){const u=(a=(s=e.tableData[0])==null?void 0:s.dataList)==null?void 0:a.map(l=>({prop:l.property,label:l.propertyName,unit:l.unit,minWidth:120}));w.loading=!1;const y=(n=e.tableData)==null?void 0:n.map(l=>{var c,v;const d=(c=l.dataList)==null?void 0:c.find(h=>h.property==="Instantaneous_flow"),_={name:l.name,stationId:l.stationId,time:d?d.time:"-",unit:d?d.unit:"-"};return(v=l==null?void 0:l.dataList)==null||v.map(h=>{_[h.property+""]=h.value}),_});J(y),w.columns=[{prop:"name",label:"名称",minWidth:200}].concat(u),w.dataList=y,e.activeName=(p=e.tableData[0])==null?void 0:p.dataList[0],w.currentRow=y[0];const g=e.tableData.find(l=>{var d;return l.stationId===((d=y[0])==null?void 0:d.stationId)});f.tabs=(m=g==null?void 0:g.dataList)==null?void 0:m.map((l,d)=>({label:l.propertyName,value:l.property+d,data:l})),e.activeName=f.tabs[0].value,C(f.tabs[0].data),console.log("resetPanel",(r=e.tableData[0])==null?void 0:r.stationId),R((i=e.tableData[0])==null?void 0:i.stationId)}else w.loading=!1,T.value=!1}),await K()},K=async()=>{var p,m,r;const t=await _t({status:""}),o=A.group[0].fields[0],s=((m=(p=t.data)==null?void 0:p.data)==null?void 0:m.length)||0,a=[],n=(r=t.data)==null?void 0:r.data;n==null||n.map(i=>{let u=a.find(g=>g.status===i.status);const{label:y}=U.find(g=>g.name===i.status)||{};u?u.value++:(u={name:y,status:i.status,nameAlias:y,value:1,scale:"0%"},a.push(u))}),a.map(i=>(i.scale=s===0?"0%":Number(i.value)/s*100+"%",i)),o&&(o.option=$(a,"个"))},C=async(t,o)=>{w.currentRow=t,(o||t)&&(await E(o||t),O(async()=>{e.pieOption=G()}),T.value=!1)},E=async t=>{var m,r;const s=(m=(await vt({deviceId:t.deviceId,attr:t.property})).data)==null?void 0:m.data,a=ht(s.todayDataList.map(i=>i.ts),50,40),n=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];a.yAxis[0].name=t.propertyName.concat(t.unit?"("+t.unit+")":"");const p=n.map(i=>{const u=s[i.key].map(y=>y.value);return{name:i.name,smooth:!0,data:u,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}});a.series=p,(r=N.value)==null||r.clear(),await O(()=>{x.value&&H.listenTo(x.value,()=>{var i;e.lineOption=a,e.pieOption=G(),(i=N.value)==null||i.resize()})})},J=async t=>{var s,a,n,p;const o=await q("流量监测站,测流压站");e.stationStatus=o,(a=(s=b.view)==null?void 0:s.graphics)==null||a.removeAll(),o.map(m=>{var c,v,h,F,P;const r=m.data,i=(c=r==null?void 0:r.location)==null?void 0:c.split(","),u=new ct({longitude:i==null?void 0:i[0],latitude:i==null?void 0:i[1],spatialReference:(v=b.view)==null?void 0:v.spatialReference}),g=((h=e.stationStatus)==null?void 0:h.find(S=>S.id===m.id)).status==="online"?j("流量监测站.png"):j("流量监测站.png"),l=new B({geometry:u,symbol:new dt({width:25,height:30,yoffset:15,url:g}),attributes:{row:r}}),d=t==null?void 0:t.find(S=>S.stationId===r.id),_=new B({geometry:u,symbol:new ut({yoffset:-15,color:"#00ff33",text:d?d.name:"-"})});(P=(F=b.view)==null?void 0:F.graphics)==null||P.addMany([l,_])}),e.stationLocation=o,e.curRow=(n=o[0])==null?void 0:n.data,w.currentRow=e.curRow,R((p=e.curRow)==null?void 0:p.id)},Q=async t=>{var o;b.view=t,(o=I.value)==null||o.toggleCustomDetail(!1),await Y(),bt(b.view,s=>{var n,p,m;const a=(n=s.results)==null?void 0:n[0];if(a&&a.type==="graphic"){const r=(m=(p=a.graphic)==null?void 0:p.attributes)==null?void 0:m.row;R(r==null?void 0:r.id)}})};return(t,o)=>{const s=rt,a=st,n=nt,p=pt,m=tt("VChart");return at(),et(gt,{ref_key:"refMap",ref:I,title:"流量监测总览",windows:e.windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":550,onMapLoaded:Q},{"detail-header":V(()=>o[1]||(o[1]=[])),"detail-default":V(()=>o[2]||(o[2]=[])),default:V(()=>{var r;return[M("div",Dt,[D(s,{ref:"refForm",config:A},null,8,["config"]),D(a,{type:"underline",title:"流量数据监测"}),M("div",kt,[D(n,{ref_key:"refCard",ref:W,class:"table-box",config:w},null,8,["config"])]),D(a,{type:"underline",title:(((r=e.curRow)==null?void 0:r.name)||"")+"近三天运行曲线"},null,8,["title"]),M("div",{ref_key:"echartsDiv",ref:x,class:"right-box bottom"},[D(p,{modelValue:e.activeName,"onUpdate:modelValue":o[0]||(o[0]=i=>e.activeName=i),config:f},null,8,["modelValue","config"]),D(m,{ref_key:"refChart",ref:N,theme:ot(it)().isDark?"dark":"light",option:e.lineOption},null,8,["theme","option"])],512)])]}),_:1},8,["windows"])}}}),Me=lt(Rt,[["__scopeId","data-v-4440bee0"]]);export{Me as default};
