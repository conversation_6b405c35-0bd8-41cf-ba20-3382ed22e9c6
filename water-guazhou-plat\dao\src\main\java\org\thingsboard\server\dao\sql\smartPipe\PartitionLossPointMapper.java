package org.thingsboard.server.dao.sql.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionLossPoint;

import java.util.List;

/**
 * 分区挂机
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface PartitionLossPointMapper extends BaseMapper<PartitionLossPoint> {

    IPage<PartitionLossPoint> getList(IPage<PartitionLossPoint> page, @Param("param") PartitionMountRequest request);

    List<JSONObject> getLossPointReport(@Param("name") String name, @Param("tenantId") String tenantId);
}
