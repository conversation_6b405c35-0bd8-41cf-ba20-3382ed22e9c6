package org.thingsboard.server.dao.operateLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.OperateLogEntity;
import org.thingsboard.server.dao.sql.operateLog.OperateLogDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 14:20
 */

@Service
public class OperateLogServiceImpl implements OperateLogService {

    @Autowired
    private OperateLogDao operateLogDao;

    @Override
    public List<OperateLogEntity> findByTenantId(TenantId tenantId) {
        return operateLogDao.findByTenantId(tenantId);
    }

    @Override
    public List<OperateLogEntity> findByPhone(String phone) {
        return operateLogDao.findByPhone(phone);
    }

    @Override
    public List<OperateLogEntity> findByCaptcha(String captcha) {
        return operateLogDao.findByCaptcha(captcha);
    }

    @Override
    public OperateLogEntity findByCaptchaAndInvalid(String captcha, String invalid) {
        return operateLogDao.findByCaptchaAndInvalid(captcha, invalid);
    }

    @Override
    public OperateLogEntity saveLog(OperateLogEntity operateLogEntity) {
        return operateLogDao.save(operateLogEntity);
    }

    @Override
    public OperateLogEntity findByPhoneAndInvalid(String phone, String invalid) {
        return operateLogDao.findByPhoneAndInvalid(phone,invalid);
    }

    @Override
    public OperateLogEntity findByPhoneAndInvalidAndCaptcha(String phone, String invalid, String captcha) {
        return operateLogDao.findByPhoneAndInvalidAndCaptcha(phone, invalid, captcha);
    }

    @Override
    public List<OperateLogEntity> findByTenantIdAndTime(TenantId tenantId, long start, long end) {
        return operateLogDao.findByTenantIdAndTime(tenantId, start, end);
    }
}
