package org.thingsboard.server.service.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.thingsboard.server.config.RabbitMQConfigurationProperties;
import org.thingsboard.server.dao.logicalFlow.BO.LogicalFlowBO;
import org.thingsboard.server.service.ipc.LogicalFlowService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 轮询逻辑流程并执行未运行的逻辑流程
 */
@Slf4j
@Component
@EnableConfigurationProperties(RabbitMQConfigurationProperties.class)
public class PollingLogicalTask {

    @Autowired
    private LogicalFlowService logicalFlowService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private RabbitMQConfigurationProperties rabbitMQConfigurationProperties;

    /**
     * 2分钟轮询一次逻辑流程并执行未处于执行状态的流程
     */
    @Scheduled(cron = "30 0/1 * * * ?")
    public void scheduled() {
        log.debug("准备检查并运行逻辑流程列表");
        // 获取逻辑流程列表
        List<LogicalFlowBO> logicalFlowBOList = logicalFlowService.findLogicalFlowBOList();
        List<LogicalFlowBO> childFlowBOList = logicalFlowService.findChildFlowBOList();
        Map<String, List<LogicalFlowBO>> childMap = new HashMap<>();
        for (LogicalFlowBO child : childFlowBOList) {
            List<LogicalFlowBO> list = new ArrayList<>();
            if (childMap.containsKey(child.getParentId())) {
                list = childMap.get(child.getParentId());
            }
            list.add(child);
            childMap.put(child.getParentId(), list);
        }

        logicalFlowBOList.parallelStream().forEach(logicalFlowBO -> {
            // 检查此流程是否正在执行中
            if (!logicalFlowService.checkRunning(logicalFlowBO.getLogicalFlowNodeBO())) {
                boolean flag = false;
                // 检查子流程是否在执行
                List<LogicalFlowBO> childList = childMap.get(logicalFlowBO.getId());
                if (childList != null && !childList.isEmpty()) {
                    for (LogicalFlowBO flowBO : childList) {
                        if (logicalFlowService.checkRunning(flowBO.getLogicalFlowNodeBO())) {
                            flag = true;
                            break;
                        }
                    }
                }
                if (!flag) {
                    // 执行流程
                    log.debug("准备执行流程. 流程名: [{}]", logicalFlowBO.getName());
                    logicalFlowService.executeRootNode(logicalFlowBO.getName(), logicalFlowBO.getLogicalFlowNodeBO());
                }
            }
        });

    }


}
