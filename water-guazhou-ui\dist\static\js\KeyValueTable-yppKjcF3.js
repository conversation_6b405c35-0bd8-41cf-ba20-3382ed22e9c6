import{d as y,r as u,S as k,am as L,o as b,g as S,n as V,q as p,i as f,aq as x,C}from"./index-r0dFAfgr.js";import{_ as T}from"./Search-NSrhrIa_.js";const w={class:"key-value-table"},I=y({__name:"KeyValueTable",props:{field:{},keyValues:{}},emits:["onSave"],setup(m,{expose:d,emit:_}){const g=_,o=m,h=u({filters:[{type:"btn-group",btns:[{perm:!0,text:"添加行",styles:{marginLeft:"auto"},click:()=>v()},{perm:!0,text:"删除",type:"danger",click:()=>c()}]}],defaultParams:{field:o.field}}),v=()=>{e.dataList.unshift({label:"",value:""})},c=t=>{k("确定移除?","提示信息").then(()=>{var a;if(t===void 0){const n=(a=e.selectList)==null?void 0:a.map(s=>s.value);e.dataList=e.dataList.filter(s=>(n==null?void 0:n.indexOf(s.value))===-1)}else e.dataList.splice(t,1)}).catch(()=>{})},e=u({height:300,columns:[{label:"值",prop:"value",formItemConfig:{type:"input"}},{label:"显示名称",prop:"label",formItemConfig:{type:"input"}}],dataList:[],operations:[{perm:!0,text:"删除",type:"danger",click:(t,a)=>c(a)}],pagination:{hide:!0},handleSelectChange(t){e.selectList=t??[]}}),r=()=>{o.keyValues?e.dataList=typeof o.keyValues=="string"?JSON.parse(o.keyValues):o.keyValues:e.dataList=[]};return L(()=>o.keyValues,()=>r()),d({save:()=>{const t=e.dataList.map(l=>{var i;return l.value=(i=l.value)==null?void 0:i.trim(),l})||[],a=t.map(l=>l.value),n=Array.from(new Set(a)),s=a.length!==n.length;return g("onSave",t,s),s}}),b(()=>{r()}),(t,a)=>{const n=T,s=x;return S(),V("div",w,[p(n,{ref:"refSearch",class:"key-value-search",config:f(h)},null,8,["config"]),p(s,{config:f(e)},null,8,["config"])])}}}),A=C(I,[["__scopeId","data-v-e2185ce4"]]);export{A as default};
