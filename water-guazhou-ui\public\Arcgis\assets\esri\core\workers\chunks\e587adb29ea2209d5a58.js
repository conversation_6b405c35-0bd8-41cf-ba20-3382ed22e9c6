"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9717],{22303:(t,e,i)=>{i.d(e,{Z:()=>f});var s,r,n=i(35270),o=i(22021),a=i(70586),h=i(75215);function l(t){return(0,o.uZ)((0,h.vU)(t),0,255)}function c(t,e,i){return t=Number(t),isNaN(t)?i:t<e?e:t>i?i:t}class u{static blendColors(t,e,i,s=new u){return s.r=Math.round(t.r+(e.r-t.r)*i),s.g=Math.round(t.g+(e.g-t.g)*i),s.b=Math.round(t.b+(e.b-t.b)*i),s.a=t.a+(e.a-t.a)*i,s._sanitize()}static fromRgb(t,e){const i=t.toLowerCase().match(/^(rgba?|hsla?)\(([\s\.\-,%0-9]+)\)/);if(i){const t=i[2].split(/\s*,\s*/),s=i[1];if("rgb"===s&&3===t.length||"rgba"===s&&4===t.length){const i=t[0];if("%"===i.charAt(i.length-1)){const i=t.map((t=>2.56*parseFloat(t)));return 4===t.length&&(i[3]=parseFloat(t[3])),u.fromArray(i,e)}return u.fromArray(t.map((t=>parseFloat(t))),e)}if("hsl"===s&&3===t.length||"hsla"===s&&4===t.length)return u.fromArray((0,n.B7)(parseFloat(t[0]),parseFloat(t[1])/100,parseFloat(t[2])/100,parseFloat(t[3])),e)}return null}static fromHex(t,e=new u){if(4!==t.length&&7!==t.length||"#"!==t[0])return null;const i=4===t.length?4:8,s=(1<<i)-1;let r=Number("0x"+t.substr(1));return isNaN(r)?null:(["b","g","r"].forEach((t=>{const n=r&s;r>>=i,e[t]=4===i?17*n:n})),e.a=1,e)}static fromArray(t,e=new u){return e._set(Number(t[0]),Number(t[1]),Number(t[2]),Number(t[3])),isNaN(e.a)&&(e.a=1),e._sanitize()}static fromString(t,e){const i=(0,n.St)(t)?(0,n.h$)(t):null;return i&&u.fromArray(i,e)||u.fromRgb(t,e)||u.fromHex(t,e)}static fromJSON(t){return t&&new u([t[0],t[1],t[2],t[3]/255])}static toUnitRGB(t){return(0,a.pC)(t)?[t.r/255,t.g/255,t.b/255]:null}static toUnitRGBA(t){return(0,a.pC)(t)?[t.r/255,t.g/255,t.b/255,null!=t.a?t.a:1]:null}constructor(t){this.r=255,this.g=255,this.b=255,this.a=1,t&&this.setColor(t)}get isBright(){return.299*this.r+.587*this.g+.114*this.b>=127}setColor(t){return"string"==typeof t?u.fromString(t,this):Array.isArray(t)?u.fromArray(t,this):(this._set(t.r??0,t.g??0,t.b??0,t.a??1),t instanceof u||this._sanitize()),this}toRgb(){return[this.r,this.g,this.b]}toRgba(){return[this.r,this.g,this.b,this.a]}toHex(){const t=this.r.toString(16),e=this.g.toString(16),i=this.b.toString(16);return`#${t.length<2?"0"+t:t}${e.length<2?"0"+e:e}${i.length<2?"0"+i:i}`}toCss(t=!1){const e=this.r+", "+this.g+", "+this.b;return t?`rgba(${e}, ${this.a})`:`rgb(${e})`}toString(){return this.toCss(!0)}toJSON(){return this.toArray()}toArray(t=u.AlphaMode.ALWAYS){const e=l(this.r),i=l(this.g),s=l(this.b);return t===u.AlphaMode.ALWAYS||1!==this.a?[e,i,s,l(255*this.a)]:[e,i,s]}clone(){return new u(this.toRgba())}hash(){return this.r<<24|this.g<<16|this.b<<8|255*this.a}equals(t){return(0,a.pC)(t)&&t.r===this.r&&t.g===this.g&&t.b===this.b&&t.a===this.a}_sanitize(){return this.r=Math.round(c(this.r,0,255)),this.g=Math.round(c(this.g,0,255)),this.b=Math.round(c(this.b,0,255)),this.a=c(this.a,0,1),this}_set(t,e,i,s){this.r=t,this.g=e,this.b=i,this.a=s}}u.prototype.declaredClass="esri.Color",s=u||(u={}),(r=s.AlphaMode||(s.AlphaMode={}))[r.ALWAYS=0]="ALWAYS",r[r.UNLESS_OPAQUE=1]="UNLESS_OPAQUE";const f=u},4307:(t,e,i)=>{i.d(e,{d:()=>h,l:()=>f,r:()=>m,s:()=>r,t:()=>_});var s=i(46851);function r(t,e,i){return t[0]=e,t[1]=i,t}function n(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function o(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t}function a(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t}function h(t,e){const i=e[0]-t[0],s=e[1]-t[1];return Math.sqrt(i*i+s*s)}function l(t,e){const i=e[0]-t[0],s=e[1]-t[1];return i*i+s*s}function c(t){const e=t[0],i=t[1];return Math.sqrt(e*e+i*i)}function u(t){const e=t[0],i=t[1];return e*e+i*i}function f(t,e,i,s){const r=e[0],n=e[1];return t[0]=r+s*(i[0]-r),t[1]=n+s*(i[1]-n),t}function _(t,e,i){const s=e[0],r=e[1];return t[0]=i[0]*s+i[2]*r+i[4],t[1]=i[1]*s+i[3]*r+i[5],t}function m(t,e,i,s){const r=e[0]-i[0],n=e[1]-i[1],o=Math.sin(s),a=Math.cos(s);return t[0]=r*a-n*o+i[0],t[1]=r*o+n*a+i[1],t}const d=c,p=n,g=o,y=a,x=h,b=l,M=u;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t},angle:function(t,e){const i=t[0],s=t[1],r=e[0],n=e[1];let o=i*i+s*s;o>0&&(o=1/Math.sqrt(o));let a=r*r+n*n;a>0&&(a=1/Math.sqrt(a));const h=(i*r+s*n)*o*a;return h>1?0:h<-1?Math.PI:Math.acos(h)},ceil:function(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},cross:function(t,e,i){const s=e[0]*i[1]-e[1]*i[0];return t[0]=t[1]=0,t[2]=s,t},dist:x,distance:h,div:y,divide:a,dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},equals:function(t,e){const i=t[0],r=t[1],n=e[0],o=e[1],a=(0,s.g)();return Math.abs(i-n)<=a*Math.max(1,Math.abs(i),Math.abs(n))&&Math.abs(r-o)<=a*Math.max(1,Math.abs(r),Math.abs(o))},exactEquals:function(t,e){return t[0]===e[0]&&t[1]===e[1]},floor:function(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t},inverse:function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t},len:d,length:c,lerp:f,max:function(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t},min:function(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t},mul:g,multiply:o,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:function(t,e){const i=e[0],s=e[1];let r=i*i+s*s;return r>0&&(r=1/Math.sqrt(r),t[0]=e[0]*r,t[1]=e[1]*r),t},projectAndScale:function(t,e,i,s,r){let n=e[0]-i[0],o=e[1]-i[1];const a=(s[0]*n+s[1]*o)*(r-1);return n=s[0]*a,o=s[1]*a,t[0]=e[0]+n,t[1]=e[1]+o,t},random:function(t,e){e=e||1;const i=2*(0,s.R)()*Math.PI;return t[0]=Math.cos(i)*e,t[1]=Math.sin(i)*e,t},rotate:m,round:function(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t},scale:function(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t},scaleAndAdd:function(t,e,i,s){return t[0]=e[0]+i[0]*s,t[1]=e[1]+i[1]*s,t},set:r,sqrDist:b,sqrLen:M,squaredDistance:l,squaredLength:u,str:function(t){return"vec2("+t[0]+", "+t[1]+")"},sub:p,subtract:n,transformMat2:function(t,e,i){const s=e[0],r=e[1];return t[0]=i[0]*s+i[2]*r,t[1]=i[1]*s+i[3]*r,t},transformMat2d:_,transformMat3:function(t,e,i){const s=e[0],r=e[1];return t[0]=i[0]*s+i[3]*r+i[6],t[1]=i[1]*s+i[4]*r+i[7],t},transformMat4:function(t,e,i){const s=e[0],r=e[1];return t[0]=i[0]*s+i[4]*r+i[12],t[1]=i[1]*s+i[5]*r+i[13],t}},Symbol.toStringTag,{value:"Module"}))},1265:(t,e,i)=>{function s(){return new Float32Array(2)}function r(t,e){const i=new Float32Array(2);return i[0]=t,i[1]=e,i}function n(){return s()}function o(){return r(1,1)}function a(){return r(1,0)}function h(){return r(0,1)}i.d(e,{O:()=>c,Z:()=>l,c:()=>s,f:()=>r});const l=n(),c=o(),u=a(),f=h();Object.freeze(Object.defineProperty({__proto__:null,ONES:c,UNIT_X:u,UNIT_Y:f,ZEROS:l,clone:function(t){const e=new Float32Array(2);return e[0]=t[0],e[1]=t[1],e},create:s,createView:function(t,e){return new Float32Array(t,e,2)},fromValues:r,ones:o,unitX:a,unitY:h,zeros:n},Symbol.toStringTag,{value:"Module"}))},13867:(t,e,i)=>{i.d(e,{Z:()=>r});var s=i(69801);class r{constructor(t,e){this._storage=new s.WJ,this._storage.maxSize=t,e&&this._storage.registerRemoveFunc("",e)}put(t,e,i){this._storage.put(t,e,i,1)}pop(t){return this._storage.pop(t)}get(t){return this._storage.get(t)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}get maxSize(){return this._storage.maxSize}set maxSize(t){this._storage.maxSize=t}}},32243:(t,e,i)=>{function s(t){return t=t||globalThis.location.hostname,l.some((e=>null!=t?.match(e)))}function r(t,e){return t&&(e=e||globalThis.location.hostname)?null!=e.match(n)||null!=e.match(a)?t.replace("static.arcgis.com","staticdev.arcgis.com"):null!=e.match(o)||null!=e.match(h)?t.replace("static.arcgis.com","staticqa.arcgis.com"):t:t}i.d(e,{XO:()=>s,pJ:()=>r});const n=/^devext.arcgis.com$/,o=/^qaext.arcgis.com$/,a=/^[\w-]*\.mapsdevext.arcgis.com$/,h=/^[\w-]*\.mapsqa.arcgis.com$/,l=[/^([\w-]*\.)?[\w-]*\.zrh-dev-local.esri.com$/,n,o,/^jsapps.esri.com$/,a,h]},62357:(t,e,i)=>{i.d(e,{F2:()=>r,Wz:()=>n,t_:()=>o,vW:()=>a});const s=/^-?(\d+(\.\d+)?)\s*((px)|(pt))?$/i;function r(t){return t?t/72*96:0}function n(t){return t?72*t/96:0}function o(t){if("string"==typeof t){const e=t.match(s);if(e){const i=Number(e[1]),s=e[3]&&e[3].toLowerCase(),r="-"===t.charAt(0),o="px"===s?n(i):i;return r?-o:o}return console.warn("screenUtils.toPt: input not recognized!"),null}return t}function a(t=0,e=0){return{x:t,y:e}}},59255:(t,e,i)=>{var s,r,n;i.d(e,{E9:()=>o,I6:()=>l,Vl:()=>s,bN:()=>h}),(n=s||(s={}))[n.Unknown=0]="Unknown",n[n.Point=1]="Point",n[n.LineString=2]="LineString",n[n.Polygon=3]="Polygon";class o{constructor(t,e){this.x=t,this.y=e}clone(){return new o(this.x,this.y)}equals(t,e){return t===this.x&&e===this.y}isEqual(t){return t.x===this.x&&t.y===this.y}setCoords(t,e){this.x=t,this.y=e}normalize(){const t=this.x,e=this.y,i=Math.sqrt(t*t+e*e);this.x/=i,this.y/=i}rightPerpendicular(){const t=this.x;this.x=this.y,this.y=-t}move(t,e){this.x+=t,this.y+=e}assign(t){this.x=t.x,this.y=t.y}assignAdd(t,e){this.x=t.x+e.x,this.y=t.y+e.y}assignSub(t,e){this.x=t.x-e.x,this.y=t.y-e.y}rotate(t,e){const i=this.x,s=this.y;this.x=i*t-s*e,this.y=i*e+s*t}scale(t){this.x*=t,this.y*=t}length(){const t=this.x,e=this.y;return Math.sqrt(t*t+e*e)}static distance(t,e){const i=e.x-t.x,s=e.y-t.y;return Math.sqrt(i*i+s*s)}static add(t,e){return new o(t.x+e.x,t.y+e.y)}static sub(t,e){return new o(t.x-e.x,t.y-e.y)}}class a{constructor(t,e,i){this.ratio=t,this.x=e,this.y=i}}class h{constructor(t,e,i,s=8,r=8){this._lines=[],this._starts=[],this.validateTessellation=!0,this._pixelRatio=s,this._pixelMargin=r,this._tileSize=512*s,this._dz=t,this._yPos=e,this._xPos=i}setPixelMargin(t){t!==this._pixelMargin&&(this._pixelMargin=t,this.setExtent(this._extent))}setExtent(t){this._extent=t,this._finalRatio=this._tileSize/t*(1<<this._dz);let e=this._pixelRatio*this._pixelMargin;e/=this._finalRatio;const i=t>>this._dz;e>i&&(e=i),this._margin=e,this._xmin=i*this._xPos-e,this._ymin=i*this._yPos-e,this._xmax=this._xmin+i+2*e,this._ymax=this._ymin+i+2*e}reset(t){this._type=t,this._lines=[],this._starts=[],this._line=null,this._start=0}moveTo(t,e){this._pushLine(),this._prevIsIn=this._isIn(t,e),this._moveTo(t,e,this._prevIsIn),this._prevPt=new o(t,e),this._firstPt=new o(t,e),this._dist=0}lineTo(t,e){const i=this._isIn(t,e),s=new o(t,e),r=o.distance(this._prevPt,s);let n,h,l,c,u,f,_,m;if(i)this._prevIsIn?this._lineTo(t,e,!0):(n=this._prevPt,h=s,l=this._intersect(h,n),this._start=this._dist+r*(1-this._r),this._lineTo(l.x,l.y,!0),this._lineTo(h.x,h.y,!0));else if(this._prevIsIn)h=this._prevPt,n=s,l=this._intersect(h,n),this._lineTo(l.x,l.y,!0),this._lineTo(n.x,n.y,!1);else{const t=this._prevPt,e=s;if(t.x<=this._xmin&&e.x<=this._xmin||t.x>=this._xmax&&e.x>=this._xmax||t.y<=this._ymin&&e.y<=this._ymin||t.y>=this._ymax&&e.y>=this._ymax)this._lineTo(e.x,e.y,!1);else{const i=[];if((t.x<this._xmin&&e.x>this._xmin||t.x>this._xmin&&e.x<this._xmin)&&(c=(this._xmin-t.x)/(e.x-t.x),m=t.y+c*(e.y-t.y),m<=this._ymin?f=!1:m>=this._ymax?f=!0:i.push(new a(c,this._xmin,m))),(t.x<this._xmax&&e.x>this._xmax||t.x>this._xmax&&e.x<this._xmax)&&(c=(this._xmax-t.x)/(e.x-t.x),m=t.y+c*(e.y-t.y),m<=this._ymin?f=!1:m>=this._ymax?f=!0:i.push(new a(c,this._xmax,m))),(t.y<this._ymin&&e.y>this._ymin||t.y>this._ymin&&e.y<this._ymin)&&(c=(this._ymin-t.y)/(e.y-t.y),_=t.x+c*(e.x-t.x),_<=this._xmin?u=!1:_>=this._xmax?u=!0:i.push(new a(c,_,this._ymin))),(t.y<this._ymax&&e.y>this._ymax||t.y>this._ymax&&e.y<this._ymax)&&(c=(this._ymax-t.y)/(e.y-t.y),_=t.x+c*(e.x-t.x),_<=this._xmin?u=!1:_>=this._xmax?u=!0:i.push(new a(c,_,this._ymax))),0===i.length)u?f?this._lineTo(this._xmax,this._ymax,!0):this._lineTo(this._xmax,this._ymin,!0):f?this._lineTo(this._xmin,this._ymax,!0):this._lineTo(this._xmin,this._ymin,!0);else if(i.length>1&&i[0].ratio>i[1].ratio)this._start=this._dist+r*i[1].ratio,this._lineTo(i[1].x,i[1].y,!0),this._lineTo(i[0].x,i[0].y,!0);else{this._start=this._dist+r*i[0].ratio;for(let t=0;t<i.length;t++)this._lineTo(i[t].x,i[t].y,!0)}this._lineTo(e.x,e.y,!1)}}this._dist+=r,this._prevIsIn=i,this._prevPt=s}close(){if(this._line.length>2){const t=this._firstPt,e=this._prevPt;t.x===e.x&&t.y===e.y||this.lineTo(t.x,t.y);const i=this._line;let s=i.length;for(;s>=4&&(i[0].x===i[1].x&&i[0].x===i[s-2].x||i[0].y===i[1].y&&i[0].y===i[s-2].y);)i.pop(),i[0].x=i[s-2].x,i[0].y=i[s-2].y,--s}}result(t=!0){return this._pushLine(),0===this._lines.length?null:(this._type===s.Polygon&&t&&c.simplify(this._tileSize,this._margin*this._finalRatio,this._lines),this._lines)}resultWithStarts(){if(this._type!==s.LineString)throw new Error("Only valid for lines");this._pushLine();const t=this._lines,e=t.length;if(0===e)return null;const i=[];for(let s=0;s<e;s++)i.push({line:t[s],start:this._starts[s]||0});return i}_isIn(t,e){return t>=this._xmin&&t<=this._xmax&&e>=this._ymin&&e<=this._ymax}_intersect(t,e){let i,s,r;if(e.x>=this._xmin&&e.x<=this._xmax)s=e.y<=this._ymin?this._ymin:this._ymax,r=(s-t.y)/(e.y-t.y),i=t.x+r*(e.x-t.x);else if(e.y>=this._ymin&&e.y<=this._ymax)i=e.x<=this._xmin?this._xmin:this._xmax,r=(i-t.x)/(e.x-t.x),s=t.y+r*(e.y-t.y);else{s=e.y<=this._ymin?this._ymin:this._ymax,i=e.x<=this._xmin?this._xmin:this._xmax;const n=(i-t.x)/(e.x-t.x),o=(s-t.y)/(e.y-t.y);n<o?(r=n,s=t.y+n*(e.y-t.y)):(r=o,i=t.x+o*(e.x-t.x))}return this._r=r,new o(i,s)}_pushLine(){this._line&&(this._type===s.Point?this._line.length>0&&(this._lines.push(this._line),this._starts.push(this._start)):this._type===s.LineString?this._line.length>1&&(this._lines.push(this._line),this._starts.push(this._start)):this._type===s.Polygon&&this._line.length>3&&(this._lines.push(this._line),this._starts.push(this._start))),this._line=[],this._start=0}_moveTo(t,e,i){this._type!==s.Polygon?i&&(t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line.push(new o(t,e))):(i||(t<this._xmin&&(t=this._xmin),t>this._xmax&&(t=this._xmax),e<this._ymin&&(e=this._ymin),e>this._ymax&&(e=this._ymax)),t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line.push(new o(t,e)),this._isH=!1,this._isV=!1)}_lineTo(t,e,i){let r,n;if(this._type!==s.Polygon)if(i){if(t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line.length>0&&(r=this._line[this._line.length-1],r.equals(t,e)))return;this._line.push(new o(t,e))}else this._line&&this._line.length>0&&this._pushLine();else if(i||(t<this._xmin&&(t=this._xmin),t>this._xmax&&(t=this._xmax),e<this._ymin&&(e=this._ymin),e>this._ymax&&(e=this._ymax)),t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line&&this._line.length>0){r=this._line[this._line.length-1];const i=r.x===t,s=r.y===e;if(i&&s)return;this._isH&&i||this._isV&&s?(r.x=t,r.y=e,n=this._line[this._line.length-2],n.x===t&&n.y===e?(this._line.pop(),this._line.length<=1?(this._isH=!1,this._isV=!1):(n=this._line[this._line.length-2],this._isH=n.x===t,this._isV=n.y===e)):(this._isH=n.x===t,this._isV=n.y===e)):(this._line.push(new o(t,e)),this._isH=i,this._isV=s)}else this._line.push(new o(t,e))}}class l{setExtent(t){this._ratio=4096===t?1:4096/t}get validateTessellation(){return this._ratio<1}reset(t){this._lines=[],this._line=null}moveTo(t,e){this._line&&this._lines.push(this._line),this._line=[];const i=this._ratio;this._line.push(new o(t*i,e*i))}lineTo(t,e){const i=this._ratio;this._line.push(new o(t*i,e*i))}close(){const t=this._line;t&&!t[0].isEqual(t[t.length-1])&&t.push(t[0])}result(){return this._line&&this._lines.push(this._line),0===this._lines.length?null:this._lines}}!function(t){t[t.sideLeft=0]="sideLeft",t[t.sideRight=1]="sideRight",t[t.sideTop=2]="sideTop",t[t.sideBottom=3]="sideBottom"}(r||(r={}));class c{static simplify(t,e,i){if(!i)return;const s=-e,n=t+e,o=-e,a=t+e,h=[],l=[],u=i.length;for(let t=0;t<u;++t){const e=i[t];if(!e||e.length<2)continue;let c,u=e[0];const f=e.length;for(let i=1;i<f;++i)c=e[i],u.x===c.x&&(u.x<=s&&(u.y>c.y?(h.push(t),h.push(i),h.push(r.sideLeft),h.push(-1)):(l.push(t),l.push(i),l.push(r.sideLeft),l.push(-1))),u.x>=n&&(u.y<c.y?(h.push(t),h.push(i),h.push(r.sideRight),h.push(-1)):(l.push(t),l.push(i),l.push(r.sideRight),l.push(-1)))),u.y===c.y&&(u.y<=o&&(u.x<c.x?(h.push(t),h.push(i),h.push(r.sideTop),h.push(-1)):(l.push(t),l.push(i),l.push(r.sideTop),l.push(-1))),u.y>=a&&(u.x>c.x?(h.push(t),h.push(i),h.push(r.sideBottom),h.push(-1)):(l.push(t),l.push(i),l.push(r.sideBottom),l.push(-1)))),u=c}if(0===h.length||0===l.length)return;c.fillParent(i,l,h),c.fillParent(i,h,l);const f=[];c.calcDeltas(f,l,h),c.calcDeltas(f,h,l),c.addDeltas(f,i)}static fillParent(t,e,i){const s=i.length,n=e.length;for(let o=0;o<n;o+=4){const n=e[o],a=e[o+1],h=e[o+2],l=t[n][a-1],c=t[n][a];let f=8092,_=-1;for(let e=0;e<s;e+=4){if(i[e+2]!==h)continue;const s=i[e],n=i[e+1],o=t[s][n-1],a=t[s][n];switch(h){case r.sideLeft:case r.sideRight:if(u(l.y,o.y,a.y)&&u(c.y,o.y,a.y)){const t=Math.abs(a.y-o.y);t<f&&(f=t,_=e)}break;case r.sideTop:case r.sideBottom:if(u(l.x,o.x,a.x)&&u(c.x,o.x,a.x)){const t=Math.abs(a.x-o.x);t<f&&(f=t,_=e)}}}e[o+3]=_}}static calcDeltas(t,e,i){const s=e.length;for(let r=0;r<s;r+=4){const s=[],n=c.calcDelta(r,e,i,s);t.push(e[r]),t.push(e[r+1]),t.push(e[r+2]),t.push(n)}}static calcDelta(t,e,i,s){const r=e[t+3];if(-1===r)return 0;const n=s.length;return n>1&&s[n-2]===r?0:(s.push(r),c.calcDelta(r,i,e,s)+1)}static addDeltas(t,e){const i=t.length;let s=0;for(let e=0;e<i;e+=4){const i=t[e+3];i>s&&(s=i)}for(let n=0;n<i;n+=4){const i=e[t[n]],o=t[n+1],a=s-t[n+3];switch(t[n+2]){case r.sideLeft:i[o-1].x-=a,i[o].x-=a,1===o&&(i[i.length-1].x-=a),o===i.length-1&&(i[0].x-=a);break;case r.sideRight:i[o-1].x+=a,i[o].x+=a,1===o&&(i[i.length-1].x+=a),o===i.length-1&&(i[0].x+=a);break;case r.sideTop:i[o-1].y-=a,i[o].y-=a,1===o&&(i[i.length-1].y-=a),o===i.length-1&&(i[0].y-=a);break;case r.sideBottom:i[o-1].y+=a,i[o].y+=a,1===o&&(i[i.length-1].y+=a),o===i.length-1&&(i[0].y+=a)}}}}const u=(t,e,i)=>t>=e&&t<=i||t>=i&&t<=e},72245:(t,e,i)=>{i.d(e,{K3:()=>r,Rx:()=>n});var s=i(80442);const r=()=>!!(0,s.Z)("enable-feature:force-wosr"),n=()=>!!(0,s.Z)("enable-feature:SceneLayer-editing")},27883:(t,e,i)=>{i.d(e,{EJ:()=>d,KV:()=>_,n2:()=>f,v9:()=>m,wm:()=>y});var s=i(3172),r=i(20102),n=i(70586),o=i(95330),a=i(17452),h=i(65587),l=i(41253),c=i(72245);let u={};function f(t,e,i){return t&&(0,n.pC)(t.styleUrl)?async function(t,e){try{return{data:(await d(t,e)).data,baseUrl:(0,a.Yd)(t),styleUrl:t}}catch(t){return(0,o.r9)(t),null}}(t.styleUrl,i):t&&(0,n.pC)(t.styleName)?function(t,e,i){const s=(0,n.pC)(e.portal)?e.portal:h.Z.getDefault();let o;const a=`${s.url} - ${s.user&&s.user.username} - ${t}`;return u[a]||(u[a]=function(t,e,i){return e.load(i).then((()=>{const s=new l.Z({disableExtraQuery:!0,query:`owner:${p} AND type:${g} AND typekeywords:"${t}"`});return e.queryItems(s,i)})).then((({results:e})=>{let s=null;const n=t.toLowerCase();if(e&&Array.isArray(e))for(const t of e){const e=t.typeKeywords?.some((t=>t.toLowerCase()===n));if(e&&t.type===g&&t.owner===p){s=t;break}}if(!s)throw new r.Z("symbolstyleutils:style-not-found",`The style '${t}' could not be found`,{styleName:t});return s.load(i)}))}(t,s,i).then((t=>(o=t,t.fetchData()))).then((e=>({data:e,baseUrl:o.itemUrl??"",styleName:t})))),u[a]}(t.styleName,e,i):Promise.reject(new r.Z("symbolstyleutils:style-url-and-name-missing","Either styleUrl or styleName is required to resolve a style"))}function _(t){return null===t||"CIMSymbolReference"===t.type?t:{type:"CIMSymbolReference",symbol:t}}function m(t,e){if("cimRef"===e)return t.cimRef;if(t.formatInfos&&!(0,c.K3)())for(const e of t.formatInfos)if("gltf"===e.type)return e.href;return t.webRef}function d(t,e){const i={responseType:"json",query:{f:"json"},...e};return(0,s.default)((0,a.Fv)(t),i)}const p="esri_en",g="Style",y="https://cdn.arcgis.com/sharing/rest/content/items/220936cc6ed342c9937abd8f180e7d1e/resources/styles/cim/{SymbolName}.json?f=json"},31363:(t,e,i)=>{i.d(e,{B1:()=>s,DQ:()=>l,DT:()=>a,JJ:()=>r,Or:()=>c,_U:()=>n,k3:()=>u,sX:()=>f});const s=Number.POSITIVE_INFINITY,r=Math.PI,n=2*r,o=128/r,a=r/180,h=1/Math.LN2;function l(t,e){return(t%=e)>=0?t:t+e}function c(t){return l(t*o,256)}function u(t){return Math.log(t)*h}function f(t,e,i){return t*(1-i)+e*i}},30729:(t,e,i)=>{var s,r;function n(t){switch(t){case"left":return s.Left;case"right":return s.Right;case"center":return s.Center}}function o(t){switch(t){case"top":return r.Top;case"middle":return r.Center;case"baseline":return r.Baseline;case"bottom":return r.Bottom}}function a(t){switch(t){case"above-left":case"esriServerPointLabelPlacementAboveLeft":return[s.Right,r.Bottom];case"above-center":case"above-along":case"esriServerPointLabelPlacementAboveCenter":case"esriServerLinePlacementAboveAlong":return[s.Center,r.Bottom];case"above-right":case"esriServerPointLabelPlacementAboveRight":return[s.Left,r.Bottom];case"center-left":case"esriServerPointLabelPlacementCenterLeft":return[s.Right,r.Center];case"center-center":case"center-along":case"esriServerPointLabelPlacementCenterCenter":case"esriServerLinePlacementCenterAlong":case"always-horizontal":case"esriServerPolygonPlacementAlwaysHorizontal":return[s.Center,r.Center];case"center-right":case"esriServerPointLabelPlacementCenterRight":return[s.Left,r.Center];case"below-left":case"esriServerPointLabelPlacementBelowLeft":return[s.Right,r.Top];case"below-center":case"below-along":case"esriServerPointLabelPlacementBelowCenter":case"esriServerLinePlacementBelowAlong":return[s.Center,r.Top];case"below-right":case"esriServerPointLabelPlacementBelowRight":return[s.Left,r.Top];default:return console.debug(`Found invalid placement type ${t}`),[s.Center,r.Center]}}function h(t){switch(t){case s.Right:return-1;case s.Center:return 0;case s.Left:return 1;default:return console.debug(`Found invalid horizontal alignment ${t}`),0}}function l(t){switch(t){case r.Top:return 1;case r.Center:return 0;case r.Bottom:case r.Baseline:return-1;default:return console.debug(`Found invalid vertical alignment ${t}`),0}}function c(t){switch(t){case"left":return s.Left;case"right":return s.Right;case"center":return s.Center}}function u(t){switch(t){case"above-along":case"below-along":case"center-along":case"esriServerLinePlacementAboveAlong":case"esriServerLinePlacementBelowAlong":case"esriServerLinePlacementCenterAlong":return!0;default:return!1}}i.d(e,{Hd:()=>c,NS:()=>u,TR:()=>r,b7:()=>o,g:()=>h,kH:()=>n,qv:()=>a,tf:()=>l}),function(t){t[t.Left=-1]="Left",t[t.Center=0]="Center",t[t.Right=1]="Right"}(s||(s={})),function(t){t[t.Top=1]="Top",t[t.Center=0]="Center",t[t.Bottom=-1]="Bottom",t[t.Baseline=2]="Baseline"}(r||(r={}))},63523:(t,e,i)=>{i.d(e,{CA:()=>l,Gq:()=>M,Xp:()=>h,a:()=>x,dk:()=>g,hF:()=>u,jj:()=>c,jy:()=>a,m2:()=>f,mE:()=>y,qr:()=>b});var s=i(20102),r=i(30729),n=i(66039);const o=Object.keys(n.mD).filter((t=>"number"==typeof n.mD[t])).reduce(((t,e)=>({...t,[e]:n.mD[e]})),{});function a(t){return t===n.mD.OUTLINE_FILL||t===n.mD.OUTLINE_FILL_SIMPLE}function h(t){return function(t){return t===n.mD.SIMPLE||t===n.mD.OUTLINE_FILL_SIMPLE}(t.symbologyType)}function l(t){return a(t.symbologyType)}function c(t,e){switch(t){case n.LW.FILL:return g.from(e);case n.LW.LINE:return x.from(e);case n.LW.MARKER:return y.from(e);case n.LW.TEXT:return b.from(e);case n.LW.LABEL:return M.from(e);default:throw new Error(`Unable to createMaterialKey for unknown geometryType ${t}`)}}function u(t){switch(f.load(t).geometryType){case n.LW.MARKER:return new y(t);case n.LW.FILL:return new g(t);case n.LW.LINE:return new x(t);case n.LW.TEXT:return new b(t);case n.LW.LABEL:return new M(t)}}class f{static load(t){const e=this.shared;return e.data=t,e}constructor(t){this._data=0,this._data=t}set data(t){this._data=t??0}get data(){return this._data}get geometryType(){return this.bits(8,11)}set geometryType(t){this.setBits(t,8,11)}get mapAligned(){return!!this.bit(20)}set mapAligned(t){this.setBit(20,t)}get sdf(){return!!this.bit(11)}set sdf(t){this.setBit(11,t??!1)}get pattern(){return!!this.bit(12)}set pattern(t){this.setBit(12,t)}get textureBinding(){return this.bits(0,8)}set textureBinding(t){this.setBits(t,0,8)}get symbologyType(){return this.bits(21,26)}set symbologyType(t){this.setBits(t,21,26)}get geometryTypeString(){switch(this.geometryType){case n.LW.FILL:return"fill";case n.LW.MARKER:return"marker";case n.LW.LINE:return"line";case n.LW.TEXT:return"text";case n.LW.LABEL:return"label";default:throw new s.Z(`Unable to handle unknown geometryType: ${this.geometryType}`)}}setBit(t,e){const i=1<<t;e?this._data|=i:this._data&=~i}bit(t){return(this._data&1<<t)>>t}setBits(t,e,i){for(let s=e,r=0;s<i;s++,r++)this.setBit(s,0!=(t&1<<r))}bits(t,e){let i=0;for(let s=t,r=0;s<e;s++,r++)i|=this.bit(s)<<r;return i}hasVV(){return!1}setVV(t,e){}getVariation(){return{mapAligned:this.mapAligned,pattern:this.pattern,sdf:this.sdf,symbologyType:{value:n.mD[this.symbologyType],options:o,namespace:"SYMBOLOGY_TYPE"}}}getVariationHash(){return this._data&~(7&this.textureBinding)}}f.shared=new f(0);const _=t=>class extends t{get vvSizeMinMaxValue(){return 0!==this.bit(16)}set vvSizeMinMaxValue(t){this.setBit(16,t)}get vvSizeScaleStops(){return 0!==this.bit(17)}set vvSizeScaleStops(t){this.setBit(17,t)}get vvSizeFieldStops(){return 0!==this.bit(18)}set vvSizeFieldStops(t){this.setBit(18,t)}get vvSizeUnitValue(){return 0!==this.bit(19)}set vvSizeUnitValue(t){this.setBit(19,t)}hasVV(){return super.hasVV()||this.vvSizeMinMaxValue||this.vvSizeScaleStops||this.vvSizeFieldStops||this.vvSizeUnitValue}setVV(t,e){super.setVV(t,e);const i=function(t,e,i){const s=n.X.SIZE_FIELD_STOPS|n.X.SIZE_MINMAX_VALUE|n.X.SIZE_SCALE_STOPS|n.X.SIZE_UNIT_VALUE,r=(e&(n.mf.FIELD_TARGETS_OUTLINE|n.mf.MINMAX_TARGETS_OUTLINE|n.mf.SCALE_TARGETS_OUTLINE|n.mf.UNIT_TARGETS_OUTLINE))>>>4;return t===n.LW.LINE&&i.isOutline||t===n.LW.FILL&&a(i.symbologyType)?s&r:s&~r}(this.geometryType,t,e)&t;this.vvSizeMinMaxValue=!!(i&n.X.SIZE_MINMAX_VALUE),this.vvSizeFieldStops=!!(i&n.X.SIZE_FIELD_STOPS),this.vvSizeUnitValue=!!(i&n.X.SIZE_UNIT_VALUE),this.vvSizeScaleStops=!!(i&n.X.SIZE_SCALE_STOPS)}},m=t=>class extends t{get vvRotation(){return 0!==this.bit(15)}set vvRotation(t){this.setBit(15,t)}hasVV(){return super.hasVV()||this.vvRotation}setVV(t,e){super.setVV(t,e),this.vvRotation=!e.isOutline&&!!(t&n.X.ROTATION)}},d=t=>class extends t{get vvColor(){return 0!==this.bit(13)}set vvColor(t){this.setBit(13,t)}hasVV(){return super.hasVV()||this.vvColor}setVV(t,e){super.setVV(t,e),this.vvColor=!e.isOutline&&!!(t&n.X.COLOR)}},p=t=>class extends t{get vvOpacity(){return 0!==this.bit(14)}set vvOpacity(t){this.setBit(14,t)}hasVV(){return super.hasVV()||this.vvOpacity}setVV(t,e){super.setVV(t,e),this.vvOpacity=!e.isOutline&&!!(t&n.X.OPACITY)}};class g extends(d(p(_(f)))){static load(t){const e=this.shared;return e.data=t,e}static from(t){const{symbologyType:e,vvFlags:i}=t,s=this.load(0);return s.geometryType=n.LW.FILL,s.symbologyType=e,e!==n.mD.DOT_DENSITY&&s.setVV(i,t),s.data}getVariation(){return{...super.getVariation(),vvColor:this.vvColor,vvOpacity:this.vvOpacity,vvSizeFieldStops:this.vvSizeFieldStops,vvSizeMinMaxValue:this.vvSizeMinMaxValue,vvSizeScaleStops:this.vvSizeScaleStops,vvSizeUnitValue:this.vvSizeUnitValue}}}g.shared=new g(0);class y extends(d(p(m(_(f))))){static load(t){const e=this.shared;return e.data=t,e}static from(t){const{symbologyType:e,vvFlags:i}=t,s=this.load(0);return s.geometryType=n.LW.MARKER,s.symbologyType=e,e!==n.mD.HEATMAP&&s.setVV(i,t),s.data}getVariation(){return{...super.getVariation(),vvColor:this.vvColor,vvRotation:this.vvRotation,vvOpacity:this.vvOpacity,vvSizeFieldStops:this.vvSizeFieldStops,vvSizeMinMaxValue:this.vvSizeMinMaxValue,vvSizeScaleStops:this.vvSizeScaleStops,vvSizeUnitValue:this.vvSizeUnitValue}}}y.shared=new y(0);class x extends(d(p(_(f)))){static load(t){const e=this.shared;return e.data=t,e}static from(t){const e=this.load(0);return e.geometryType=n.LW.LINE,e.symbologyType=t.symbologyType,e.setVV(t.vvFlags,t),e.data}getVariation(){return{...super.getVariation(),vvColor:this.vvColor,vvOpacity:this.vvOpacity,vvSizeFieldStops:this.vvSizeFieldStops,vvSizeMinMaxValue:this.vvSizeMinMaxValue,vvSizeScaleStops:this.vvSizeScaleStops,vvSizeUnitValue:this.vvSizeUnitValue}}}x.shared=new x(0);class b extends(d(p(m(_(f))))){static load(t){const e=this.shared;return e.data=t,e}static from(t){const e=this.load(0);return e.geometryType=n.LW.TEXT,e.symbologyType=t.symbologyType,e.setVV(t.vvFlags,t),e.data}getVariation(){return{...super.getVariation(),vvColor:this.vvColor,vvOpacity:this.vvOpacity,vvRotation:this.vvRotation,vvSizeFieldStops:this.vvSizeFieldStops,vvSizeMinMaxValue:this.vvSizeMinMaxValue,vvSizeScaleStops:this.vvSizeScaleStops,vvSizeUnitValue:this.vvSizeUnitValue}}}b.shared=new b(0);class M extends(_(f)){static load(t){const e=this.shared;return e.data=t,e}static from(t){const e=this.load(0);return e.geometryType=n.LW.LABEL,e.symbologyType=t.symbologyType,e.setVV(t.vvFlags,t),e.mapAligned=(0,r.NS)(t.placement),e.data}getVariation(){return{...super.getVariation(),vvSizeFieldStops:this.vvSizeFieldStops,vvSizeMinMaxValue:this.vvSizeMinMaxValue,vvSizeScaleStops:this.vvSizeScaleStops,vvSizeUnitValue:this.vvSizeUnitValue}}}M.shared=new M(0)},47988:(t,e,i)=>{i.d(e,{Z:()=>h});var s=i(43697),r=i(3920),n=i(5600),o=(i(75215),i(67676),i(52011));let a=class extends r.r{initialize(){}destroy(){}get supportsTileUpdates(){return!1}get spatialReference(){const t=this.get("tileStore.tileScheme.spatialReference");return t&&t.toJSON()||null}};(0,s._)([(0,n.Cb)({readOnly:!0})],a.prototype,"supportsTileUpdates",null),(0,s._)([(0,n.Cb)({constructOnly:!0})],a.prototype,"remoteClient",void 0),(0,s._)([(0,n.Cb)({constructOnly:!0})],a.prototype,"service",void 0),(0,s._)([(0,n.Cb)()],a.prototype,"spatialReference",null),(0,s._)([(0,n.Cb)({constructOnly:!0})],a.prototype,"tileInfo",void 0),(0,s._)([(0,n.Cb)({constructOnly:!0})],a.prototype,"tileStore",void 0),a=(0,s._)([(0,o.j)("esri.views.2d.layers.features.processors.BaseProcessor")],a);const h=a},19717:(t,e,i)=>{i.r(e),i.d(e,{default:()=>yo});var s=i(43697);const r=new(i(79087).Z);function n(t){if(null==t)return["",!1];if(!r.hasBidiChar(t))return[t,!1];let e;return e="rtl"===r.checkContextual(t)?"IDNNN":"ICNNN",[r.bidiTransform(t,e,"VLYSN"),!0]}var o=i(20102),a=i(80442),h=i(92604),l=i(70586),c=i(95330),u=(i(75215),i(67676),i(52011)),f=i(22862),_=i(82971),m=i(61027),d=i(66039),p=i(15923),g=i(5600),y=i(4307),x=i(49733);const b=new Set,M=[],v=new Map,S=[0,0];let w=class extends p.Z{constructor(t){super(t),this._keyToItem=new Map,this.concurrency=6,this.strategy="scale-first",this.tileInfoView=null}initialize(){const{concurrency:t,process:e}=this;this._queue=new x.e({concurrency:t,process:(t,i)=>{const s=this._keyToItem.get(t);return e(s,{signal:i})},peeker:t=>t.values().next().value})}destroy(){this.clear(),this._queue=(0,l.SC)(this._queue)}get length(){return this._queue?this._queue.length:0}get onGoingCount(){return this._keyToItem.size}get updating(){return this.length>0||this.onGoingCount>0}abort(t){const e="string"==typeof t?t:t.id;this._queue.abort(e)}clear(){this._queue.clear(),this._keyToItem.clear(),this.notifyChange("updating")}has(t){return"string"==typeof t?this._keyToItem.has(t):this._keyToItem.has(t.id)}isOngoing(t){const e="string"==typeof t?t:t.id;return this.has(e)&&this._queue.isOngoing(e)}pause(){this._queue.pause()}push(t,e){const i=t.key.id+"-"+e;if(this.has(i))return this.get(i);const s=this._queue.push(i),r=()=>{this._keyToItem.delete(i),this.notifyChange("updating")};return this._keyToItem.set(i,t),s.then(r,r),this.notifyChange("updating"),s}reset(){this._queue.reset(),this.notifyChange("updating")}resume(){this._queue.resume()}_peekByScaleFirst(t){if(!this.state)return t.values().next().value;const e=this.tileInfoView;let i=Number.NEGATIVE_INFINITY,s=Number.POSITIVE_INFINITY;t.forEach((t=>{const e=this._keyToItem.get(t),r=this.tileInfoView.getTileScale(e.key);v.has(r)||(v.set(r,[]),i=Math.max(r,i),s=Math.min(r,s)),v.get(r).push(e.key),b.add(r)}));let r=this.state.scale;v.has(r)||(function(t,e){t.length=0,e.forEach((e=>t.push(e)))}(M,b),M.sort(((t,e)=>t-e)),r=M.reduce(((t,e)=>Math.abs(e-r)<Math.abs(t-r)?e:t),M[0])),r=Math.min(r,i),r=Math.max(r,s);const n=v.get(r),o=e.getClosestInfoForScale(r),a=o.getColumnForX(this.state.center[0]),h=o.getRowForY(this.state.center[1]);return n.sort(((t,e)=>{const i=o.denormalizeCol(t.col,t.world),s=o.denormalizeCol(e.col,e.world);return Math.sqrt((a-i)*(a-i)+(h-t.row)*(h-t.row))-Math.sqrt((a-s)*(a-s)+(h-e.row)*(h-e.row))})),b.clear(),v.clear(),n[0].id}_peekByCenterFirst(t){if(!this.state)return t.values().next().value;const e=this.tileInfoView,i=this.state.center;let s,r=Number.POSITIVE_INFINITY;return t.forEach((t=>{const n=this._keyToItem.get(t);e.getTileCoords(S,n.key);const o=(0,y.d)(S,i);o<r&&(r=o,s=n.key)})),s.id}};(0,s._)([(0,g.Cb)({constructOnly:!0})],w.prototype,"concurrency",void 0),(0,s._)([(0,g.Cb)({constructOnly:!0})],w.prototype,"process",void 0),(0,s._)([(0,g.Cb)()],w.prototype,"state",void 0),(0,s._)([(0,g.Cb)({constructOnly:!0})],w.prototype,"strategy",void 0),(0,s._)([(0,g.Cb)({constructOnly:!0})],w.prototype,"tileInfoView",void 0),(0,s._)([(0,g.Cb)({readOnly:!0})],w.prototype,"updating",null),w=(0,s._)([(0,u.j)("esri.views.2d.tiling.PagedTileQueue")],w),i(83068);var P=i(55415);const C=new Set,L=[],I=new Map,k=[0,0];let T=class extends p.Z{constructor(t){super(t),this._keyToItem=new Map,this.concurrency=6,this.strategy="scale-first",this.tileInfoView=null}initialize(){const{concurrency:t,process:e,strategy:i}=this;this._queue=new x.e({concurrency:t,process:(t,i)=>{const s=this._keyToItem.get(t);return e(s,{signal:i})},peeker:"scale-first"===i?t=>this._peekByScaleFirst(t):t=>this._peekByCenterFirst(t)})}destroy(){this.clear(),this._queue=(0,l.SC)(this._queue)}get length(){return this._queue?this._queue.length:0}get onGoingCount(){return this._keyToItem.size}get updating(){return this.length>0||this.onGoingCount>0}abort(t){const e="string"==typeof t?t:t.id;this._queue.abort(e)}clear(){this._queue.clear(),this._keyToItem.clear(),this.notifyChange("updating")}has(t){return"string"==typeof t?this._keyToItem.has(t):this._keyToItem.has(t.id)}isOngoing(t){const e="string"==typeof t?t:t.id;return this.has(e)&&this._queue.isOngoing(e)}pause(){this._queue.pause()}push(t){const e=t.key.id;if(this._queue.has(e))return this._queue.get(e);const i=this._queue.push(e),s=()=>{this._keyToItem.delete(e),this.notifyChange("updating")};return this._keyToItem.set(e,t),i.then(s,s),this.notifyChange("updating"),i}reset(){this._queue.reset()}resume(){this._queue.resume()}_peekByScaleFirst(t){if(!this.state)return t.values().next().value;const e=this.tileInfoView;let i=Number.NEGATIVE_INFINITY,s=Number.POSITIVE_INFINITY;t.forEach((t=>{const e=this._keyToItem.get(t),r=this.tileInfoView.getTileScale(e.key);I.has(r)||(I.set(r,[]),i=Math.max(r,i),s=Math.min(r,s)),I.get(r).push(e.key),C.add(r)}));let r=this.state.scale;I.has(r)||(function(t,e){t.length=0,e.forEach((e=>t.push(e)))}(L,C),L.sort(((t,e)=>t-e)),r=L.reduce(((t,e)=>Math.abs(e-r)<Math.abs(t-r)?e:t),L[0])),r=Math.min(r,i),r=Math.max(r,s);const n=I.get(r),o=e.getClosestInfoForScale(r),a=o.getColumnForX(this.state.center[0]),h=o.getRowForY(this.state.center[1]);return n.sort(((t,e)=>{const i=o.denormalizeCol(t.col,t.world),s=o.denormalizeCol(e.col,e.world);return Math.sqrt((a-i)*(a-i)+(h-t.row)*(h-t.row))-Math.sqrt((a-s)*(a-s)+(h-e.row)*(h-e.row))})),C.clear(),I.clear(),n[0].id}_peekByCenterFirst(t){if(!this.state)return t.values().next().value;const e=this.tileInfoView,i=this.state.center;let s,r=Number.POSITIVE_INFINITY;return t.forEach((t=>{const n=this._keyToItem.get(t);e.getTileCoords(k,n.key);const o=(0,y.d)(k,i);o<r&&(r=o,s=n.key)})),s.id}};(0,s._)([(0,g.Cb)({constructOnly:!0})],T.prototype,"concurrency",void 0),(0,s._)([(0,g.Cb)({constructOnly:!0})],T.prototype,"process",void 0),(0,s._)([(0,g.Cb)()],T.prototype,"state",void 0),(0,s._)([(0,g.Cb)({constructOnly:!0})],T.prototype,"strategy",void 0),(0,s._)([(0,g.Cb)({constructOnly:!0})],T.prototype,"tileInfoView",void 0),(0,s._)([(0,g.Cb)({readOnly:!0})],T.prototype,"updating",null),T=(0,s._)([(0,u.j)("esri.views.2d.tiling.TileQueue")],T);var z=i(24470);i(67524),new P.Z(0,0,0,0),new Map;var A=i(16534),E=i(26899);const F=new Map;F.set(d.LW.MARKER,{multiplier:1,indicesPerRecord:6,verticesPerRecord:4}),F.set(d.LW.LINE,{multiplier:1,indicesPerRecord:24,verticesPerRecord:8}),F.set(d.LW.FILL,{multiplier:1,indicesPerRecord:10,verticesPerRecord:10}),F.set(d.LW.TEXT,{multiplier:8,indicesPerRecord:6,verticesPerRecord:4}),F.set(d.LW.LABEL,{multiplier:8,indicesPerRecord:6,verticesPerRecord:4});class R{get length(){return this._pos}constructor(t,e){this._pos=0;const i=e?this._roundToNearest(e,t.BYTES_PER_ELEMENT):40;this._array=new ArrayBuffer(i),this._buffer=new t(this._array),this._ctor=t,this._i16View=new Int16Array(this._array)}_roundToNearest(t,e){const i=Math.round(t);return i+(e-i%e)}_ensureSize(t){if(this._pos+t>=this._buffer.length){const e=this._roundToNearest(1.25*(this._array.byteLength+t*this._buffer.BYTES_PER_ELEMENT),this._buffer.BYTES_PER_ELEMENT),i=new ArrayBuffer(e),s=new this._ctor(i);s.set(this._buffer,0),this._array=i,this._buffer=s,this._i16View=new Int16Array(this._array)}}ensureSize(t){this._ensureSize(t)}writeF32(t){this._ensureSize(1);const e=this._pos;return new Float32Array(this._array,4*this._pos,1)[0]=t,this._pos++,e}push(t){this._ensureSize(1);const e=this._pos;return this._buffer[this._pos++]=t,e}writeFixed(t){this._buffer[this._pos++]=t}setValue(t,e){this._buffer[t]=e}i1616Add(t,e,i){this._i16View[2*t]+=e,this._i16View[2*t+1]+=i}getValue(t){return this._buffer[t]}incr(t){if(this._buffer.length<t)throw new Error("Increment index overflows the target buffer");this._buffer[t]++}decr(t){this._buffer[t]--}writeRegion(t){this._ensureSize(t.length);const e=this._pos;return this._buffer.set(t,this._pos),this._pos+=t.length,e}writeManyFrom(t,e,i){this._ensureSize(i-e);for(let s=e;s!==i;s++)this.writeFixed(t._buffer[s])}buffer(){const t=this._array.slice(0,4*this._pos);return this.destroy(),t}toArray(){const t=this._array,e=[];for(let i=0;i<t.byteLength/4;i++)e.push(t[i]);return e}seek(t){this._pos=t}destroy(){this._array=null,this._buffer=null}}class N{constructor(t,e,i){this._start={index:0,vertex:0};const s=function(t,e,i){const{indicesPerRecord:s,multiplier:r,verticesPerRecord:n}=F.get(t);return{recordBytes:i*A.XJ*Uint32Array.BYTES_PER_ELEMENT,indexBytes:r*s*i*Uint32Array.BYTES_PER_ELEMENT,vertexBytes:r*n*i*e}}(t,e,i),r=e/4;this.geometryType=t,this._records=new R(Int32Array,s.recordBytes),this._indices=new R(Uint32Array,s.indexBytes),this._vertices=new R(Uint32Array,s.vertexBytes),this._metrics=new R(Float32Array,0),this._strideInt=r}serialize(t){const e=this._records.buffer(),i=this._indices.buffer(),s=this._vertices.buffer(),r=this._metrics.length?this._metrics.buffer():null,n=4*this._strideInt;return t.push(e,i,s),{stride:n,records:e,indices:i,vertices:s,metrics:r}}get strideInt(){return this._strideInt}get recordCount(){return this._records.length/A.XJ}get vertexCount(){return this._vertices.length/this._strideInt}get indexCount(){return this._indices.length}get indexWriter(){return this._indices}get vertexWriter(){return this._vertices}get metricWriter(){return this._metrics}vertexEnsureSize(t){this._vertices.ensureSize(t)}indexEnsureSize(t){this._indices.ensureSize(t)}recordStart(){this._start.index=this._indices.length,this._start.vertex=this._vertices.length}recordEnd(t,e,i,s,r,n,o,a){this._records.push(t),this._records.push(e??0),this._records.push(i),this._records.push(s),this._records.push(r),this._records.push(n),this._records.push(o),this._records.writeF32(a)}writeIndex(t){this._indices.push(t)}writeVertex(t){this._vertices.push(t)}writeVertexF32(t){this._vertices.writeF32(t)}copyLastFrom(t,e,i){const s=t._records.length-A.XJ,r=t._records.getValue(s),n=t._records.getValue(s+1),o=t._records.getValue(s+2),a=t._records.getValue(s+4),h=t._records.getValue(s+6),l=t._records.getValue(s+7),c=this._vertices.length,u=(t._start.vertex-this._vertices.length)/this._strideInt,f=this._indices.length,_=this.vertexCount;for(let e=t._start.index;e!==t._indices.length;e++){const i=t._indices.getValue(e);this._indices.push(i-u)}for(let e=t._start.vertex;e!==t._vertices.length;e++){const i=t._vertices.getValue(e);this._vertices.push(i)}for(let t=c;t<=this._vertices.length;t+=this._strideInt)this._vertices.i1616Add(t,e,i);this._records.push(r),this._records.push(n),this._records.push(o),this._records.push(f),this._records.push(a),this._records.push(_),this._records.push(h),this._records.push(l)}}var O=i(14867);function W(t){switch(t){case 1:case 8:case 32:return-1;case 2:case 64:return 0;case 4:case 16:case 128:return 1}}function V(t){switch(t){case 1:case 2:case 4:return-1;case 8:case 16:return 0;case 32:case 64:case 128:return 1}}class D{constructor(t,e,i,s,r,n=0){this._hasAggregate=!1,this.hasRecords=!1,this._data={self:new Map,neighbors:new Array},this._version=0,this._current={geometryType:0,writer:null,overlaps:0,start:0,insertAfter:0,sortKey:0,id:0,materialKey:0,indexStart:0,vertStart:0,isDotDensity:!1,bufferingEnabled:!1,metricBoxLenPointer:0},this.hint=e,this.tileKey=t,this._hasAggregate=s,this._pixelBufferEnabled=r,this._version=n,this._symbologyType=i}get hasAggregates(){return this._hasAggregate}get hasPixelBufferEnabled(){return this._pixelBufferEnabled}serialize(t){const e=[];return e.push(this._serializeTileVertexData(this.tileKey,this.tileKey,this._data.self)),this._data.neighbors.forEach(((i,s)=>{const r=1<<s,n=W(r),o=V(r),a=(0,O.M)(new P.Z(this.tileKey),n,o,t),h=this._serializeTileVertexData(this.tileKey,a.id,i.vertexData);h.message.bufferIds=i.displayIds,e.push(h)})),e}_serializeTileVertexData(t,e,i){const s=new Array;return{message:{tileKeyOrigin:t,tileKey:e,data:{[d.LW.MARKER]:i.get(d.LW.MARKER)?.serialize(s),[d.LW.FILL]:i.get(d.LW.FILL)?.serialize(s),[d.LW.LINE]:i.get(d.LW.LINE)?.serialize(s),[d.LW.TEXT]:i.get(d.LW.TEXT)?.serialize(s),[d.LW.LABEL]:i.get(d.LW.LABEL)?.serialize(s)},version:this._version},transferList:s}}featureStart(t,e){this._current.insertAfter=t,this._current.sortKey=e}featureEnd(){}recordStart(t,e,i,s){this._current.writer=this._getVertexWriter(i),this._current.overlaps=0,this._current.indexStart=this._current.writer.indexCount,this._current.vertStart=this._current.writer.vertexCount,this._current.bufferingEnabled=s,this._current.id=t,this._current.materialKey=e,this._current.geometryType=i,this._current.isDotDensity=!1,this._current.writer.recordStart()}recordCount(){return this._current.writer.recordCount}vertexCount(){return this._current.writer.vertexCount}indexCount(){return this._current.writer.indexCount}vertexEnsureSize(t){this._current.writer.vertexEnsureSize(t)}indexEnsureSize(t){this._current.writer.indexEnsureSize(t)}vertexBounds(t,e,i,s){this._current.bufferingEnabled&&this._addOverlap(t,e,i,s)}vertexWrite(t){this._current.writer.writeVertex(t)}vertexWriteF32(t){this._current.writer.writeVertexF32(t)}vertexEnd(){}vertexWriter(){return this._current.writer.vertexWriter}indexWrite(t){this._current.writer.writeIndex(t)}indexWriter(){return this._current.writer.indexWriter}metricWriter(){return this._current.writer.metricWriter}metricStart(t,e,i,s,r,n,o,a){this._current.writer=this._getVertexWriter(d.LW.LABEL);const h=this._current.writer.metricWriter;h.push((0,m.jL)(t)),h.push(e),h.push(i),h.push(s),h.push(r),h.push(n),h.push(o),h.push(a),h.push(255),this._current.metricBoxLenPointer=h.push(0)}metricEnd(){const t=this._current.writer.metricWriter;0===t.getValue(this._current.metricBoxLenPointer)&&t.seek(t.length-10)}metricBoxWrite(t,e,i,s){const r=this._current.writer.metricWriter;r.incr(this._current.metricBoxLenPointer),r.push(0),r.push(0),r.push(t),r.push(e),r.push(i),r.push(s)}recordEnd(){const t=this._current.vertStart,e=this._current.writer.vertexCount-t;if(!e)return!1;this.hasRecords=!0;const i=this._current.indexStart,s=this._current.writer.indexCount-i;if(this._current.writer.recordEnd(this._current.id,this._current.materialKey,this._current.insertAfter,i,s,t,e,this._current.sortKey),!this._pixelBufferEnabled||this._hasAggregate||0===this._current.overlaps||this._current.geometryType===d.LW.LABEL)return!0;const r=this._current.writer;for(let t=0;t<8;t++){const e=1<<t;if(this._current.overlaps&e){this._data.neighbors[t]||(this._data.neighbors[t]={vertexData:new Map,displayIds:new Set});const i=this._data.neighbors[t],s=this._current.geometryType;if(!i.vertexData.has(s)){const t=(0,E.$_)(s,this._symbologyType).geometry,e=new N(s,t,A.Ip);i.vertexData.set(s,e)}const n=i.vertexData.get(this._current.geometryType),o=8,a=512*-W(e)*o,h=512*-V(e)*o;n?.copyLastFrom(r,a,h),i.displayIds.add(this._current.id)}}return!0}_addOverlap(t,e,i,s){const r=255^((t<0+i?148:t>=A.I_-i?41:189)|(e<0+s?224:e>=A.I_-s?7:231));this._current.overlaps|=r}_getVertexWriter(t){if(!this._data.self.has(t)){const e=this._data.self,i=(0,E.$_)(t,this._symbologyType).geometry;e.set(t,new N(t,i,this.hint.records))}return this._data.self.get(t)}}var B=i(65390),U=i(22021),G=i(62357),X=i(46851);function H(t,e,i){const s=e[0],r=e[1],n=e[2],o=e[3],a=e[4],h=e[5],l=i[0],c=i[1],u=i[2],f=i[3],_=i[4],m=i[5];return t[0]=s*l+n*c,t[1]=r*l+o*c,t[2]=s*u+n*f,t[3]=r*u+o*f,t[4]=s*_+n*m+a,t[5]=r*_+o*m+h,t}function Y(t,e,i){const s=e[0],r=e[1],n=e[2],o=e[3],a=e[4],h=e[5],l=Math.sin(i),c=Math.cos(i);return t[0]=s*c+n*l,t[1]=r*c+o*l,t[2]=s*-l+n*c,t[3]=r*-l+o*c,t[4]=a,t[5]=h,t}function J(t,e,i){const s=e[0],r=e[1],n=e[2],o=e[3],a=e[4],h=e[5],l=i[0],c=i[1];return t[0]=s,t[1]=r,t[2]=n,t[3]=o,t[4]=s*l+n*c+a,t[5]=r*l+o*c+h,t}function Z(t,e){const i=Math.sin(e),s=Math.cos(e);return t[0]=s,t[1]=i,t[2]=-i,t[3]=s,t[4]=0,t[5]=0,t}function q(t,e){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=e[0],t[5]=e[1],t}function K(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t[2]=e[2]-i[2],t[3]=e[3]-i[3],t[4]=e[4]-i[4],t[5]=e[5]-i[5],t}const $=H,j=K;function Q(){const t=new Float32Array(6);return t[0]=1,t[3]=1,t}function tt(t,e,i,s){const r=e[s],n=e[s+1];t[s]=i[0]*r+i[2]*n+i[4],t[s+1]=i[1]*r+i[3]*n+i[5]}function et(t,e,i,s=0,r=0,n=2){const o=r||e.length/n;for(let r=s;r<o;r++)tt(t,e,i,r*n)}Object.freeze(Object.defineProperty({__proto__:null,add:function(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t[2]=e[2]+i[2],t[3]=e[3]+i[3],t[4]=e[4]+i[4],t[5]=e[5]+i[5],t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},determinant:function(t){return t[0]*t[3]-t[1]*t[2]},equals:function(t,e){const i=t[0],s=t[1],r=t[2],n=t[3],o=t[4],a=t[5],h=e[0],l=e[1],c=e[2],u=e[3],f=e[4],_=e[5],m=(0,X.g)();return Math.abs(i-h)<=m*Math.max(1,Math.abs(i),Math.abs(h))&&Math.abs(s-l)<=m*Math.max(1,Math.abs(s),Math.abs(l))&&Math.abs(r-c)<=m*Math.max(1,Math.abs(r),Math.abs(c))&&Math.abs(n-u)<=m*Math.max(1,Math.abs(n),Math.abs(u))&&Math.abs(o-f)<=m*Math.max(1,Math.abs(o),Math.abs(f))&&Math.abs(a-_)<=m*Math.max(1,Math.abs(a),Math.abs(_))},exactEquals:function(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]&&t[4]===e[4]&&t[5]===e[5]},frob:function(t){return Math.sqrt(t[0]**2+t[1]**2+t[2]**2+t[3]**2+t[4]**2+t[5]**2+1)},fromRotation:Z,fromScaling:function(t,e){return t[0]=e[0],t[1]=0,t[2]=0,t[3]=e[1],t[4]=0,t[5]=0,t},fromTranslation:q,identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},invert:function(t,e){const i=e[0],s=e[1],r=e[2],n=e[3],o=e[4],a=e[5];let h=i*n-s*r;return h?(h=1/h,t[0]=n*h,t[1]=-s*h,t[2]=-r*h,t[3]=i*h,t[4]=(r*a-n*o)*h,t[5]=(s*o-i*a)*h,t):null},mul:$,multiply:H,multiplyScalar:function(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3]*i,t[4]=e[4]*i,t[5]=e[5]*i,t},multiplyScalarAndAdd:function(t,e,i,s){return t[0]=e[0]+i[0]*s,t[1]=e[1]+i[1]*s,t[2]=e[2]+i[2]*s,t[3]=e[3]+i[3]*s,t[4]=e[4]+i[4]*s,t[5]=e[5]+i[5]*s,t},rotate:Y,scale:function(t,e,i){const s=e[0],r=e[1],n=e[2],o=e[3],a=e[4],h=e[5],l=i[0],c=i[1];return t[0]=s*l,t[1]=r*l,t[2]=n*c,t[3]=o*c,t[4]=a,t[5]=h,t},set:function(t,e,i,s,r,n,o){return t[0]=e,t[1]=i,t[2]=s,t[3]=r,t[4]=n,t[5]=o,t},str:function(t){return"mat2d("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+")"},sub:j,subtract:K,translate:J},Symbol.toStringTag,{value:"Module"})),Object.freeze(Object.defineProperty({__proto__:null,clone:function(t){const e=new Float32Array(6);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e},create:Q,createView:function(t,e){return new Float32Array(t,e,6)},fromValues:function(t,e,i,s,r,n){const o=new Float32Array(6);return o[0]=t,o[1]=e,o[2]=i,o[3]=s,o[4]=r,o[5]=n,o},transform:tt,transformMany:et},Symbol.toStringTag,{value:"Module"}));var it=i(95648),st=i(30729),rt=i(37720),nt=i(12142),ot=i(63523);const at=100;function ht(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function lt(t,e){return Math.sqrt(t*t+e*e)}function ct(t){const e=lt(t[0],t[1]);t[0]/=e,t[1]/=e}function ut(t,e){return lt(t[0]-e[0],t[1]-e[1])}function ft(t){return"function"==typeof t}function _t(t=2){return 1/Math.max(t,1)}function mt(t,e){return[!!t?.minScale&&e.scaleToZoom(t.minScale)||0,!!t?.maxScale&&e.scaleToZoom(t.maxScale)||at]}function dt(t){return t.length-1}function pt(t,e,i=1){const[s,r]=function(t,e){return t[e+1]}(t,e);return Math.sqrt(s*s+r*r)*i}class gt{constructor(t,e,i,s,r){this._segments=t,this._index=e,this._distance=i,this._xStart=s,this._yStart=r,this._done=!1}static create(t){return new gt(t,0,0,t[0][0],t[0][1])}clone(){return new gt(this._segments,this._index,this._distance,this.xStart,this.yStart)}equals(t){return this._index===t._index||t._index===this._index-1&&(0===this._distance||1===t._distance)||t._index===this._index+1&&(1===this._distance||0===t._distance)}leq(t){return this._index<t._index||this._index===t._index&&this._distance<=t._distance}geq(t){return this._index>t._index||this._index===t._index&&this._distance>=t._distance}get _segment(){return this._segments[this._index+1]}get angle(){const t=this.dy,e=(0*t+-1*-this.dx)/(1*this.length);let i=Math.acos(e);return t>0&&(i=2*Math.PI-i),i}get xStart(){return this._xStart}get yStart(){return this._yStart}get x(){return this.xStart+this.distance*this.dx}get y(){return this.yStart+this.distance*this.dy}get dx(){return this._segment[0]}get dy(){return this._segment[1]}get xMidpoint(){return this.xStart+.5*this.dx}get yMidpoint(){return this.yStart+.5*this.dy}get xEnd(){return this.xStart+this.dx}get yEnd(){return this.yStart+this.dy}get length(){const{dx:t,dy:e}=this;return Math.sqrt(t*t+e*e)}get remainingLength(){return this.length*(1-this._distance)}get backwardLength(){return this.length*this._distance}get distance(){return this._distance}get done(){return this._done}hasPrev(){return this._index-1>=0}hasNext(){return this._index+1<dt(this._segments)}next(){return this.hasNext()?(this._xStart+=this.dx,this._yStart+=this.dy,this._distance=0,this._index+=1,this):null}prev(){return this.hasPrev()?(this._index-=1,this._xStart-=this.dx,this._yStart-=this.dy,this._distance=1,this):(this._done=!0,null)}_seekBackwards(t,e){const i=this.backwardLength;if(t<=i)return this._distance=(i-t)/this.length,this;let s=this.backwardLength;for(;this.prev();){if(s+this.length>t)return this._seekBackwards(t-s);s+=this.length}return this._distance=0,e?this:null}seek(t,e=!1){if(t<0)return this._seekBackwards(Math.abs(t),e);if(t<=this.remainingLength)return this._distance=(this.backwardLength+t)/this.length,this;let i=this.remainingLength;for(;this.next();){if(i+this.length>t)return this.seek(t-i,e);i+=this.length}return this._distance=1,e?this:null}}function yt(t,e,i,s=!0){const r=function(t){let e=0;for(let i=0;i<dt(t);i++)e+=pt(t,i);return e}(t),n=gt.create(t),o=r/2;if(!s)return n.seek(o),void i(n.clone(),0,o+0*e,r);const a=Math.max((r-e)/2,0),h=Math.floor(a/e),l=o-h*e;n.seek(l);for(let t=-h;t<=h;t++)n.x<512&&n.x>=0&&n.y<512&&n.y>=0&&i(n.clone(),t,o+t*e,r),n.seek(e)}function xt(t,e){const i=1e-6;if(e<=0)return;const s=t.length;if(s<3)return;const r=[];let n=0;r.push(0);for(let e=1;e<s;e++)n+=ut(t[e],t[e-1]),r.push(n);e=Math.min(e,.2*n);const o=[];o.push(t[0][0]),o.push(t[0][1]);const a=t[s-1][0],h=t[s-1][1],l=ht([0,0],t[0],t[1]);ct(l),t[0][0]+=e*l[0],t[0][1]+=e*l[1],ht(l,t[s-1],t[s-2]),ct(l),t[s-1][0]+=e*l[0],t[s-1][1]+=e*l[1];for(let t=1;t<s;t++)r[t]+=e;r[s-1]+=e;const c=.5*e;for(let n=1;n<s-1;n++){let a=0,h=0,l=0;for(let s=n-1;s>=0&&!(r[s+1]<r[n]-c);s--){const o=c+r[s+1]-r[n],u=r[s+1]-r[s],f=r[n]-r[s]<c?1:o/u;if(Math.abs(f)<i)break;const _=f*f,m=f*o-.5*_*u,d=f*u/e,p=t[s+1],g=t[s][0]-p[0],y=t[s][1]-p[1];a+=d/m*(p[0]*f*o+.5*_*(o*g-u*p[0])-_*f*u*g/3),h+=d/m*(p[1]*f*o+.5*_*(o*y-u*p[1])-_*f*u*y/3),l+=d}for(let o=n+1;o<s&&!(r[o-1]>r[n]+c);o++){const s=c-r[o-1]+r[n],u=r[o]-r[o-1],f=r[o]-r[n]<c?1:s/u;if(Math.abs(f)<i)break;const _=f*f,m=f*s-.5*_*u,d=f*u/e,p=t[o-1],g=t[o][0]-p[0],y=t[o][1]-p[1];a+=d/m*(p[0]*f*s+.5*_*(s*g-u*p[0])-_*f*u*g/3),h+=d/m*(p[1]*f*s+.5*_*(s*y-u*p[1])-_*f*u*y/3),l+=d}o.push(a/l),o.push(h/l)}o.push(a),o.push(h);for(let e=0,i=0;e<s;e++)t[e][0]=o[i++],t[e][1]=o[i++]}var bt=i(98732),Mt=i(22974),vt=i(33955);class St{constructor(){this.setIdentity()}getAngle(){return(null==this.rz||0===this.rz&&1!==this.rzCos&&0!==this.rzSin)&&(this.rz=Math.atan2(this.rzSin,this.rzCos)),this.rz}setIdentity(){this.tx=0,this.ty=0,this.tz=0,this.s=1,this.rx=0,this.ry=0,this.rz=0,this.rzCos=1,this.rzSin=0}setTranslate(t,e){this.tx=t,this.ty=e}setTranslateZ(t){this.tz=t}setRotateCS(t,e){this.rz=void 0,this.rzCos=t,this.rzSin=e}setRotate(t){this.rz=t,this.rzCos=void 0,this.rzSin=void 0}setRotateY(t){this.ry=t}setScale(t){this.s=t}setMeasure(t){this.m=t}}function wt(t){const e=(0,Mt.d9)(t);return(i=e)&&((0,vt.oU)(i)?zt(i.rings):(0,vt.l9)(i)?zt(i.paths):(0,vt.aW)(i)&&Tt(i.points),Pt(i)),e;var i}function Pt(t){t&&((0,vt.wp)(t)?t.y=-t.y:(0,vt.oU)(t)?Lt(t.rings):(0,vt.l9)(t)?Lt(t.paths):(0,vt.aW)(t)&&Ct(t.points))}function Ct(t){if(t){const e=t.length;for(let i=0;i<e;i++)t[i][1]=-t[i][1]}}function Lt(t){if(t)for(const e of t)Ct(e)}function It(t){if(t)for(let e=t.length-1;e>0;--e)t[e][0]-=t[e-1][0],t[e][1]-=t[e-1][1]}function kt(t){if(t)for(const e of t)It(e)}function Tt(t){if(t){const e=t.length;for(let i=1;i<e;++i)t[i][0]+=t[i-1][0],t[i][1]+=t[i-1][1]}}function zt(t){if(t)for(const e of t)Tt(e)}function At(t){if(t)for(const e of t)Et(e)}function Et(t){t&&t.reverse()}function Ft(t,e,i){return[t[0]+(e[0]-t[0])*i,t[1]+(e[1]-t[1])*i]}function Rt(t){return t[4]}function Nt(t,e){t[4]=e}class Ot{constructor(t,e,i,s=0){this.isClosed=!1,this.multiPath=null,this.acceptPolygon=e,this.acceptPolyline=i,this.geomUnitsPerPoint=s,this.pathCount=-1,this.pathIndex=-1,this.iteratePath=!1,t&&((0,vt.oU)(t)?e&&(this.multiPath=t.rings,this.isClosed=!0):(0,vt.l9)(t)?i&&(this.multiPath=t.paths,this.isClosed=!1):(0,vt.YX)(t)&&e&&(this.multiPath=Vt(t).rings,this.isClosed=!0),this.multiPath&&(this.pathCount=this.multiPath.length)),this.internalPlacement=new St}next(){if(!this.multiPath)return null;for(;this.iteratePath||this.pathIndex<this.pathCount-1;){this.iteratePath||this.pathIndex++;const t=this.processPath(this.multiPath[this.pathIndex]);if(t)return t}return this.pathCount=-1,this.pathIndex=-1,this.multiPath=null,null}}class Wt{constructor(t,e,i,s=0){this.isClosed=!1,this.multiPath=null,this.inputGeometries=t,this.acceptPolygon=e,this.acceptPolyline=i,this.geomUnitsPerPoint=s,this.pathCount=-1,this.pathIndex=-1,this.iteratePath=!1}next(){for(;;){if(!this.multiPath){let t=this.inputGeometries.next();for(;t;){if((0,vt.oU)(t)?this.acceptPolygon&&(this.multiPath=t.rings,this.isClosed=!0):(0,vt.l9)(t)?this.acceptPolyline&&(this.multiPath=t.paths,this.isClosed=!1):(0,vt.YX)(t)&&this.acceptPolygon&&(this.multiPath=Vt(t).rings,this.isClosed=!0),this.multiPath){this.pathCount=this.multiPath.length,this.pathIndex=-1;break}t=this.inputGeometries.next()}if(!this.multiPath)return null}for(;this.iteratePath||this.pathIndex<this.pathCount-1;){this.iteratePath||this.pathIndex++;const t=this.processPath(this.multiPath[this.pathIndex]);if(t)return t}this.pathCount=-1,this.pathIndex=-1,this.multiPath=null}}}function Vt(t){return{rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]}}class Dt{static local(){return null===Dt.instance&&(Dt.instance=new Dt),Dt.instance}execute(t,e,i,s,r){return new Bt(t,e,i)}}Dt.instance=null;class Bt{constructor(t,e,i){this._inputGeometries=t,this._angleTolerance=void 0!==e.angleTolerance?e.angleTolerance:120,this._maxCosAngle=Math.cos((1-Math.abs(this._angleTolerance)/180)*Math.PI)}next(){let t=this._inputGeometries.next();for(;t;){if((0,vt.oU)(t)){this._isClosed=!0;const e=(0,Mt.d9)(t);return this._processMultipath(e.rings),e}if((0,vt.l9)(t)){this._isClosed=!1;const e=(0,Mt.d9)(t);return this._processMultipath(e.paths),e}if((0,vt.YX)(t)){if(this._maxCosAngle)return t;this._isClosed=!0;const e=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]];return this._processPath(e),{rings:[e]}}t=this._inputGeometries.next()}return null}_processMultipath(t){if(t)for(const e of t)this._processPath(e)}_processPath(t){if(t){let e,i,s,r,n,o,a=t.length,h=t[0];this._isClosed&&++a;for(let l=1;l<a;++l){let c;c=this._isClosed&&l===a-1?t[0]:t[l];const u=c[0]-h[0],f=c[1]-h[1],_=Math.sqrt(u*u+f*f);l>1&&_>0&&s>0&&(e*u+i*f)/_/s<=this._maxCosAngle&&Nt(h,1),1===l&&(r=u,n=f,o=_),_>0&&(h=c,e=u,i=f,s=_)}this._isClosed&&s>0&&o>0&&(e*r+i*n)/o/s<=this._maxCosAngle&&Nt(t[0],1)}}}const Ut=.03;class Gt{constructor(){this._path=[]}path(){return this._path}addPath(t,e){e||t.reverse(),Array.prototype.push.apply(this._path,t),e||t.reverse()}static mergePath(t,e){e&&Array.prototype.push.apply(t,e)}startPath(t){this._path.push(t)}lineTo(t){this._path.push(t)}close(){const t=this._path;t.length>1&&(t[0][0]===t[t.length-1][0]&&t[0][1]===t[t.length-1][1]||t.push([t[0][0],t[0][1]]))}}class Xt{constructor(t=0,e=!1){}normalize(t){const e=Math.sqrt(t[0]*t[0]+t[1]*t[1]);0!==e&&(t[0]/=e,t[1]/=e)}calculateLength(t,e){const i=e[0]-t[0],s=e[1]-t[1];return Math.sqrt(i*i+s*s)}calculateSegLength(t,e){return this.calculateLength(t[e],t[e+1])}calculatePathLength(t){let e=0;const i=t?t.length:0;for(let s=0;s<i-1;++s)e+=this.calculateSegLength(t,s);return e}calculatePathArea(t){let e=0;const i=t?t.length:0;for(let s=0;s<i-1;++s)e+=(t[s+1][0]-t[s][0])*(t[s+1][1]+t[s][1]);return e/2}getCoord2D(t,e,i){return[t[0]+(e[0]-t[0])*i,t[1]+(e[1]-t[1])*i]}getSegCoord2D(t,e,i){return this.getCoord2D(t[e],t[e+1],i)}getAngle(t,e,i){const s=e[0]-t[0],r=e[1]-t[1];return Math.atan2(r,s)}getSegAngle(t,e,i){return this.getAngle(t[e],t[e+1],i)}getAngleCS(t,e,i){const s=e[0]-t[0],r=e[1]-t[1],n=Math.sqrt(s*s+r*r);return n>0?[s/n,r/n]:[1,0]}getSegAngleCS(t,e,i){return this.getAngleCS(t[e],t[e+1],i)}cut(t,e,i,s){return[i<=0?t[e]:this.getSegCoord2D(t,e,i),s>=1?t[e+1]:this.getSegCoord2D(t,e,s)]}addSegment(t,e,i){i&&t.push(e[0]),t.push(e[1])}getSubCurve(t,e,i){const s=[];return this.appendSubCurve(s,t,e,i)?s:null}appendSubCurve(t,e,i,s){const r=e?e.length-1:0;let n=0,o=!0,a=0;for(;a<r;){const r=this.calculateSegLength(e,a);if(0!==r){if(o){if(n+r>i){const h=(i-n)/r;let l=1,c=!1;n+r>=s&&(l=(s-n)/r,c=!0);const u=this.cut(e,a,h,l);if(u&&this.addSegment(t,u,o),c)break;o=!1}}else{if(n+r>s){const i=this.cut(e,a,0,(s-n)/r);i&&this.addSegment(t,i,o);break}this.addSegment(t,[e[a],e[a+1]],o)}n+=r,++a}else++a}return!0}getCIMPointAlong(t,e){const i=t?t.length-1:0;let s=0,r=-1;for(;r<i;){++r;const i=this.calculateSegLength(t,r);if(0!==i){if(s+i>e){const n=(e-s)/i;return this.getCoord2D(t[r],t[r+1],n)}s+=i}}return null}isEmpty(t,e){if(!t||t.length<=1)return!0;const i=t?t.length-1:0;let s=-1;for(;s<i;){if(++s,t[s+1][0]!==t[s][0]||t[s+1][1]!==t[s][1])return!1;if(e&&t[s+1][2]!==t[s][2])return!1}return!0}offset(t,e,i,s,r){if(!t||t.length<2)return null;let n=0,o=t[n++],a=n;for(;n<t.length;){const e=t[n];e[0]===o[0]&&e[1]===o[1]||(n!==a&&(t[a]=t[n]),o=t[a++]),n++}const h=t[0][0]===t[a-1][0]&&t[0][1]===t[a-1][1];if(h&&--a,a<(h?3:2))return null;const l=[];o=h?t[a-1]:null;let c=t[0];for(let r=0;r<a;r++){const n=r===a-1?h?t[0]:null:t[r+1];if(o)if(n){const t=[n[0]-c[0],n[1]-c[1]];this.normalize(t);const r=[c[0]-o[0],c[1]-o[1]];this.normalize(r);const a=r[0]*t[1]-r[1]*t[0],h=r[0]*t[0]+r[1]*t[1];if(0===a&&1===h){c=n;continue}if(a>=0==e<=0){if(h<1){const i=[t[0]-r[0],t[1]-r[1]];this.normalize(i);const n=Math.sqrt((1+h)/2);if(n>1/s){const t=-Math.abs(e)/n;l.push([c[0]-i[0]*t,c[1]-i[1]*t])}}}else switch(i){case it.id.Mitered:{const i=Math.sqrt((1+h)/2);if(i>0&&1/i<s){const s=[t[0]-r[0],t[1]-r[1]];this.normalize(s);const n=Math.abs(e)/i;l.push([c[0]-s[0]*n,c[1]-s[1]*n]);break}}case it.id.Bevelled:l.push([c[0]+r[1]*e,c[1]-r[0]*e]),l.push([c[0]+t[1]*e,c[1]-t[0]*e]);break;case it.id.Rounded:if(h<1){l.push([c[0]+r[1]*e,c[1]-r[0]*e]);const i=Math.floor(2.5*(1-h));if(i>0){const s=1/i;let n=s;for(let o=1;o<i;o++,n+=s){const i=[r[1]*(1-n)+t[1]*n,-r[0]*(1-n)-t[0]*n];this.normalize(i),l.push([c[0]+i[0]*e,c[1]+i[1]*e])}}l.push([c[0]+t[1]*e,c[1]-t[0]*e])}break;case it.id.Square:default:if(a<0)l.push([c[0]+(r[1]+r[0])*e,c[1]+(r[1]-r[0])*e]),l.push([c[0]+(t[1]-t[0])*e,c[1]-(t[0]+t[1])*e]);else{const i=Math.sqrt((1+Math.abs(h))/2),s=[t[0]-r[0],t[1]-r[1]];this.normalize(s);const n=e/i;l.push([c[0]-s[0]*n,c[1]-s[1]*n])}}}else{const t=[c[0]-o[0],c[1]-o[1]];this.normalize(t),l.push([c[0]+t[1]*e,c[1]-t[0]*e])}else{const t=[n[0]-c[0],n[1]-c[1]];this.normalize(t),l.push([c[0]+t[1]*e,c[1]-t[0]*e])}o=c,c=n}return l.length<(h?3:2)?null:(h&&l.push([l[0][0],l[0][1]]),l)}}const Ht=it.TF.OpenEnded;class Yt{static local(){return null===Yt.instance&&(Yt.instance=new Yt),Yt.instance}execute(t,e,i,s,r){return new Jt(t,e,i)}}Yt.instance=null;class Jt extends Wt{constructor(t,e,i){super(t,!1,!0),this._curveHelper=new Xt,this._width=(void 0!==e.width?e.width:5)*i,this._arrowType=void 0!==e.geometricEffectArrowType?e.geometricEffectArrowType:void 0!==e.arrowType?e.arrowType:Ht,this._offsetFlattenError=Ut*i}processPath(t){switch(this._arrowType){case it.TF.OpenEnded:default:return this._constructSimpleArrow(t,!0);case it.TF.Block:return this._constructSimpleArrow(t,!1);case it.TF.Crossed:return this._constructCrossedArrow(t)}}_constructSimpleArrow(t,e){const i=this._curveHelper.calculatePathLength(t);let s=this._width;i<2*s&&(s=i/2);const r=this._curveHelper.getSubCurve(t,0,i-s);if(!r)return null;const n=s/2;if(this._curveHelper.isEmpty(r,!1))return null;const o=this._constructOffset(r,-n);if(!o)return null;const a=this._constructOffset(r,n);if(!a)return null;const h=this._constructArrowBasePoint(o,-n/2);if(!h)return null;const l=this._constructArrowBasePoint(a,n/2);if(!l)return null;const c=t[t.length-1];e||(this._makeControlPoint(a,!0),this._makeControlPoint(o,!0));const u=new Gt;return u.addPath(a,!0),u.lineTo(l),this._makeControlPoint(u.path()),u.lineTo(c),this._makeControlPoint(u.path()),u.lineTo(h),this._makeControlPoint(u.path()),u.addPath(o,!1),e?{paths:[u.path()]}:(u.close(),{rings:[u.path()]})}_constructCrossedArrow(t){const e=this._curveHelper.calculatePathLength(t);let i=this._width;e<3.732050807568877*i&&(i=e/3.732050807568877);const s=this._curveHelper.getSubCurve(t,0,e-2.732050807568877*i);if(!s)return null;const r=i/2;if(this._curveHelper.isEmpty(s,!1))return null;const n=this._constructOffset(s,r);if(!n)return null;const o=this._constructOffset(s,-r);if(!o)return null;const a=this._curveHelper.getSubCurve(t,0,e-i);if(!a)return null;if(this._curveHelper.isEmpty(a,!1))return null;const h=this._constructOffset(a,r);if(!h)return null;const l=this._constructOffset(a,-r);if(!l)return null;const c=h[h.length-1],u=this._constructArrowBasePoint(h,r/2);if(!u)return null;const f=l[l.length-1],_=this._constructArrowBasePoint(l,-r/2);if(!_)return null;const m=t[t.length-1];this._makeControlPoint(n,!1),this._makeControlPoint(o,!1);const d=new Gt;return d.addPath(n,!0),this._makeControlPoint(d.path()),d.lineTo(f),d.lineTo(_),this._makeControlPoint(d.path()),d.lineTo(m),this._makeControlPoint(d.path()),d.lineTo(u),this._makeControlPoint(d.path()),d.lineTo(c),this._makeControlPoint(d.path()),d.addPath(o,!1),{paths:[d.path()]}}_constructOffset(t,e){return this._curveHelper.offset(t,e,it.id.Rounded,4,this._offsetFlattenError)}_constructArrowBasePoint(t,e){if(!t||t.length<2)return null;const i=t[t.length-2],s=t[t.length-1],r=[s[0]-i[0],s[1]-i[1]];return this._curveHelper.normalize(r),[s[0]+r[1]*e,s[1]-r[0]*e]}_makeControlPoint(t,e=!1){Nt(e?t[0]:t[t.length-1],1)}}i(66577);var Zt=i(59255);let qt;class Kt{constructor(t){this._geometry=t}next(){const t=this._geometry;return this._geometry=null,t}}function $t(t,e){let i,s;qt||(qt=new Zt.bN(0,0,0,1)),qt.reset(Zt.Vl.Polygon),qt.setPixelMargin(e+1),qt.setExtent(512);for(const e of t.rings)if(e&&!(e.length<3)){i=e[0][0],s=-e[0][1],qt.moveTo(i,s);for(let t=1;t<e.length;t++)i=e[t][0],s=-e[t][1],qt.lineTo(i,s);qt.close()}const r=qt.result(!1);if(r){const t=[];for(const e of r){const i=[];t.push(i);for(const t of e)i.push([t.x,-t.y])}return{rings:t}}return{rings:[]}}function jt(t,e){let i,s;qt||(qt=new Zt.bN(0,0,0,1)),qt.reset(Zt.Vl.LineString),qt.setPixelMargin(e+1),qt.setExtent(512);for(const e of t.paths)if(e&&!(e.length<2)){i=e[0][0],s=-e[0][1],qt.moveTo(i,s);for(let t=1;t<e.length;t++)i=e[t][0],s=-e[t][1],qt.lineTo(i,s)}const r=qt.result(!1);if(r){const t=[];for(const e of r){const i=[];t.push(i);for(const t of e)i.push([t.x,-t.y])}return{paths:t}}return{paths:[]}}class Qt{static local(){return null===Qt.instance&&(Qt.instance=new Qt),Qt.instance}execute(t,e,i,s,r){return new te(t,e,i,s,r)}}Qt.instance=null;class te{constructor(t,e,i,s,r){this._inputGeometries=t,this._tileKey=s,this._geometryEngine=r,this._curveHelper=new Xt,this._size=(void 0!==e.size?e.size:1)*i,this._offsetFlattenError=Ut*i}next(){let t;for(;t=this._inputGeometries.next();){if(0===this._size)return t;if((0,vt.YX)(t))if(this._size>0){const e=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]],i=this._curveHelper.offset(e,this._size,it.id.Rounded,4,this._offsetFlattenError);if(i)return{rings:[i]}}else if(this._size<0&&Math.min(t.xmax-t.xmin,t.ymax-t.ymin)+2*this._size>0)return{xmin:t.xmin-this._size,xmax:t.xmax+this._size,ymin:t.ymin-this._size,ymax:t.ymax+this._size};const e=this._geometryEngine;if((0,l.Wi)(e))return null;let i=t;if((!(0,vt.oU)(t)||!this._tileKey||(i=$t(t,Math.abs(this._size)+1),i&&i.rings&&0!==i.rings.length))&&(!(0,vt.l9)(t)||!this._tileKey||(i=jt(t,Math.abs(this._size)+1),i&&i.paths&&0!==i.paths.length)))return e.buffer(_.Z.WebMercator,i,this._size,1)}return null}}class ee{static local(){return null===ee.instance&&(ee.instance=new ee),ee.instance}execute(t,e,i,s,r){return new ie(t,e,i)}}ee.instance=null;class ie{constructor(t,e,i){this._defaultPointSize=20,this._inputGeometries=t,this._geomUnitsPerPoint=i,this._rule=e.rule??it.Em.FullGeometry,this._defaultSize=this._defaultPointSize*i}next(){let t;for(;t=this._inputGeometries.next();){let e;if((0,vt.wp)(t)?e=this._processGeom([[[t.x,t.y]]]):(0,vt.aW)(t)?e=this._processGeom([t.points]):(0,vt.l9)(t)?e=this._processGeom(t.paths):(0,vt.oU)(t)&&(e=this._processGeom(t.rings)),e&&e.length)return{paths:e}}return null}_clone(t){return[t[0],t[1]]}_mid(t,e){return[(t[0]+e[0])/2,(t[1]+e[1])/2]}_mix(t,e,i,s){return[t[0]*e+i[0]*s,t[1]*e+i[1]*s]}_add(t,e){return[t[0]+e[0],t[1]+e[1]]}_add2(t,e,i){return[t[0]+e,t[1]+i]}_sub(t,e){return[t[0]-e[0],t[1]-e[1]]}_dist(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}_norm(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}_normalize(t,e=1){const i=e/this._norm(t);t[0]*=i,t[1]*=i}_leftPerpendicular(t){const e=-t[1],i=t[0];t[0]=e,t[1]=i}_leftPerp(t){return[-t[1],t[0]]}_rightPerpendicular(t){const e=t[1],i=-t[0];t[0]=e,t[1]=i}_rightPerp(t){return[t[1],-t[0]]}_dotProduct(t,e){return t[0]*e[0]+t[1]*e[1]}_crossProduct(t,e){return t[0]*e[1]-t[1]*e[0]}_rotateDirect(t,e,i){const s=t[0]*e-t[1]*i,r=t[0]*i+t[1]*e;t[0]=s,t[1]=r}_makeCtrlPt(t){const e=[t[0],t[1]];return Nt(e,1),e}_addAngledTicks(t,e,i,s){const r=this._sub(i,e);this._normalize(r);const n=this._crossProduct(r,this._sub(s,e));let o;o=n>0?this._rightPerp(r):this._leftPerp(r);const a=Math.abs(n)/2,h=[];h.push([e[0]+(o[0]-r[0])*a,e[1]+(o[1]-r[1])*a]),h.push(e),h.push(i),h.push([i[0]+(o[0]+r[0])*a,i[1]+(o[1]+r[1])*a]),t.push(h)}_addBezier2(t,e,i,s,r){if(0==r--)return void t.push(s);const n=this._mid(e,i),o=this._mid(i,s),a=this._mid(n,o);this._addBezier2(t,e,n,a,r),this._addBezier2(t,a,o,s,r)}_addBezier3(t,e,i,s,r,n){if(0==n--)return void t.push(r);const o=this._mid(e,i),a=this._mid(i,s),h=this._mid(s,r),l=this._mid(o,a),c=this._mid(a,h),u=this._mid(l,c);this._addBezier3(t,e,o,l,u,n),this._addBezier3(t,u,c,h,r,n)}_add90DegArc(t,e,i,s,r){const n=r??this._crossProduct(this._sub(i,e),this._sub(s,e))>0,o=this._mid(e,i),a=this._sub(o,e);n?this._leftPerpendicular(a):this._rightPerpendicular(a),o[0]+=a[0],o[1]+=a[1],this._addBezier3(t,e,this._mix(e,.33333,o,.66667),this._mix(i,.33333,o,.66667),i,4)}_addArrow(t,e,i){const s=e[0],r=e[1],n=e[e.length-1],o=this._sub(s,r);this._normalize(o);const a=this._crossProduct(o,this._sub(n,r)),h=.5*a,l=this._leftPerp(o),c=[n[0]-l[0]*a,n[1]-l[1]*a],u=e.length-1,f=[];f.push(i?[-l[0],-l[1]]:l);let _=[-o[0],-o[1]];for(let t=1;t<u-1;t++){const i=this._sub(e[t+1],e[t]);this._normalize(i);const s=this._dotProduct(i,_),r=this._crossProduct(i,_),n=Math.sqrt((1+s)/2),o=this._sub(i,_);this._normalize(o),o[0]/=n,o[1]/=n,f.push(r<0?[-o[0],-o[1]]:o),_=i}f.push(this._rightPerp(_));for(let i=f.length-1;i>0;i--)t.push([e[i][0]+f[i][0]*h,e[i][1]+f[i][1]*h]);t.push([c[0]+f[0][0]*h,c[1]+f[0][1]*h]),t.push([c[0]+f[0][0]*a,c[1]+f[0][1]*a]),t.push(s),t.push([c[0]-f[0][0]*a,c[1]-f[0][1]*a]),t.push([c[0]-f[0][0]*h,c[1]-f[0][1]*h]);for(let i=1;i<f.length;i++)t.push([e[i][0]-f[i][0]*h,e[i][1]-f[i][1]*h])}_cp2(t,e,i){return t.length>=2?t[1]:this._add2(t[0],e*this._defaultSize,i*this._defaultSize)}_cp3(t,e,i,s){if(t.length>=3)return t[2];const r=this._mix(t[0],1-i,e,i),n=this._sub(e,t[0]);return this._normalize(n),this._rightPerpendicular(n),[r[0]+n[0]*s*this._defaultSize,r[1]+n[1]*s*this._defaultSize]}_arrowPath(t){if(t.length>2)return t;const e=t[0],i=this._cp2(t,-4,0),s=this._sub(e,i);this._normalize(s);const r=this._rightPerp(s);return[e,i,[e[0]+(r[0]-s[0])*this._defaultSize,e[1]+(r[1]-s[1])*this._defaultSize]]}_arrowLastSeg(t){const e=t[0],i=this._cp2(t,-4,0);let s;if(t.length>=3)s=t[t.length-1];else{const t=this._sub(e,i);this._normalize(t);const r=this._rightPerp(t);s=[e[0]+(r[0]-t[0])*this._defaultSize,e[1]+(r[1]-t[1])*this._defaultSize]}return[i,s]}_processGeom(t){if(!t)return null;const e=[];for(const i of t){if(!i||0===i.length)continue;const t=i.length;let s=i[0];switch(this._rule){case it.Em.PerpendicularFromFirstSegment:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,4),n=[];n.push(r),n.push(this._mid(s,t)),e.push(n);break}case it.Em.ReversedFirstSegment:{const t=this._cp2(i,0,-1);e.push([t,s]);break}case it.Em.PerpendicularToSecondSegment:{const t=this._cp2(i,-4,1),r=this._cp3(i,t,.882353,-1.94),n=[];n.push(this._mid(t,r)),n.push(s),e.push(n);break}case it.Em.SecondSegmentWithTicks:{const t=this._cp2(i,-4,1),r=this._cp3(i,t,.882353,-1.94),n=this._sub(r,t);let o;o=this._crossProduct(n,this._sub(s,t))>0?this._rightPerp(o):this._leftPerp(n);const a=[];a.push([t[0]+(o[0]-n[0])/3,t[1]+(o[1]-n[1])/3]),a.push(t),a.push(r),a.push([r[0]+(o[0]+n[0])/3,r[1]+(o[1]+n[1])/3]),e.push(a);break}case it.Em.DoublePerpendicular:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,3),n=this._mid(s,t),o=this._sub(n,r);this._normalize(o);const a=this._crossProduct(o,this._sub(s,r));this._leftPerpendicular(o);const h=[];h.push(s),h.push([r[0]+o[0]*a,r[1]+o[1]*a]),e.push(h);const l=[];l.push([r[0]-o[0]*a,r[1]-o[1]*a]),l.push(t),e.push(l);break}case it.Em.OppositeToFirstSegment:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,3),n=this._mid(s,t),o=this._sub(n,r);this._normalize(o);const a=this._crossProduct(o,this._sub(s,r));this._leftPerpendicular(o);const h=[];h.push([r[0]+o[0]*a,r[1]+o[1]*a]),h.push([r[0]-o[0]*a,r[1]-o[1]*a]),e.push(h);break}case it.Em.TriplePerpendicular:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,4),n=this._mid(s,t),o=this._sub(n,r);this._normalize(o);const a=this._crossProduct(o,this._sub(s,r));this._leftPerpendicular(o);const h=[];h.push([r[0]+o[0]*a*.8,r[1]+o[1]*a*.8]),h.push([n[0]+.8*(s[0]-n[0]),n[1]+.8*(s[1]-n[1])]),e.push(h),e.push([r,n]);const l=[];l.push([r[0]-o[0]*a*.8,r[1]-o[1]*a*.8]),l.push([n[0]+.8*(t[0]-n[0]),n[1]+.8*(t[1]-n[1])]),e.push(l);break}case it.Em.HalfCircleFirstSegment:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,4),n=this._mid(s,t);let o=this._sub(t,s);const a=Math.cos(Math.PI/18),h=Math.sin(Math.PI/18),l=Math.sqrt((1+a)/2),c=Math.sqrt((1-a)/2),u=[];let f;this._crossProduct(o,this._sub(r,s))>0?(u.push(s),o=this._sub(s,n),f=t):(u.push(t),o=this._sub(t,n),f=s),this._rotateDirect(o,l,c),o[0]/=l,o[1]/=l;for(let t=1;t<=18;t++)u.push(this._add(n,o)),this._rotateDirect(o,a,h);u.push(f),e.push(u);break}case it.Em.HalfCircleSecondSegment:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,1,-1);let n=this._sub(s,t);this._normalize(n);const o=this._crossProduct(n,this._sub(r,t))/2;this._leftPerpendicular(n);const a=[t[0]+n[0]*o,t[1]+n[1]*o];n=this._sub(t,a);const h=Math.cos(Math.PI/18);let l=Math.sin(Math.PI/18);o>0&&(l=-l);const c=[t];for(let t=1;t<=18;t++)this._rotateDirect(n,h,l),c.push(this._add(a,n));e.push(c);break}case it.Em.HalfCircleExtended:{const r=this._cp2(i,0,-2),n=this._cp3(i,r,1,-1);let o;if(t>=4)o=i[3];else{const t=this._sub(s,r);o=this._add(n,t)}const a=this._dist(r,n)/2/.75,h=this._sub(r,s);this._normalize(h,a);const l=this._sub(n,o);this._normalize(l,a);const c=[o,n];e.push(c);const u=[this._clone(n)];this._addBezier3(u,n,this._add(n,l),this._add(r,h),r,4),u.push(s),e.push(u);break}case it.Em.OpenCircle:{const t=this._cp2(i,-2,0),r=this._sub(t,s),n=Math.cos(Math.PI/18),o=-Math.sin(Math.PI/18),a=[t];for(let t=1;t<=33;t++)this._rotateDirect(r,n,o),a.push(this._add(s,r));e.push(a);break}case it.Em.CoverageEdgesWithTicks:{const r=this._cp2(i,0,-1);let n,o;if(t>=3)n=i[2];else{const t=this._sub(r,s),e=this._leftPerp(t);n=[s[0]+e[0]-.25*t[0],s[1]+e[1]-.25*t[1]]}if(t>=4)o=i[3];else{const t=this._mid(s,r),e=this._sub(s,r);this._normalize(e),this._leftPerpendicular(e);const i=this._crossProduct(e,this._sub(n,t));this._rightPerpendicular(e),o=[n[0]+e[0]*i*2,n[1]+e[1]*i*2]}const a=this._sub(r,s);let h,l;h=this._crossProduct(a,this._sub(n,s))>0?this._rightPerp(a):this._leftPerp(a),l=[],l.push(n),l.push(s),l.push([s[0]+(h[0]-a[0])/3,s[1]+(h[1]-a[1])/3]),e.push(l),h=this._crossProduct(a,this._sub(o,r))>0?this._rightPerp(h):this._leftPerp(a),l=[],l.push([r[0]+(h[0]+a[0])/3,r[1]+(h[1]+a[1])/3]),l.push(r),l.push(o),e.push(l);break}case it.Em.GapExtentWithDoubleTicks:{const r=this._cp2(i,0,2),n=this._cp3(i,r,0,1);let o;if(t>=4)o=i[3];else{const t=this._sub(r,s);o=this._add(n,t)}this._addAngledTicks(e,s,r,this._mid(n,o)),this._addAngledTicks(e,n,o,this._mid(s,r));break}case it.Em.GapExtentMidline:{const r=this._cp2(i,2,0),n=this._cp3(i,r,0,1);let o;if(t>=4)o=i[3];else{const t=this._sub(r,s);o=this._add(n,t)}const a=[];a.push(this._mid(s,n)),a.push(this._mid(r,o)),e.push(a);break}case it.Em.Chevron:{const r=this._cp2(i,-1,-1);let n;if(t>=3)n=i[2];else{const t=this._sub(r,s);this._leftPerpendicular(t),n=this._add(s,t)}e.push([r,this._makeCtrlPt(s),n]);break}case it.Em.PerpendicularWithArc:{const t=this._cp2(i,0,-2),r=this._cp3(i,t,.5,-1);let n=this._sub(t,s);const o=this._norm(n);n[0]/=o,n[1]/=o;const a=this._crossProduct(n,this._sub(r,s));let h=this._dotProduct(n,this._sub(r,s));h<.05*o?h=.05*o:h>.95*o&&(h=.95*o);const l=[s[0]+n[0]*h,s[1]+n[1]*h];this._leftPerpendicular(n);let c=[];c.push([l[0]-n[0]*a,l[1]-n[1]*a]),c.push([l[0]+n[0]*a,l[1]+n[1]*a]),e.push(c);const u=[t[0]+n[0]*a,t[1]+n[1]*a];n=this._sub(t,u);const f=Math.cos(Math.PI/18);let _=Math.sin(Math.PI/18);a<0&&(_=-_),c=[s,t];for(let t=1;t<=9;t++)this._rotateDirect(n,f,_),c.push(this._add(u,n));e.push(c);break}case it.Em.ClosedHalfCircle:{const t=this._cp2(i,2,0),r=this._mid(s,t),n=this._sub(t,r),o=Math.cos(Math.PI/18),a=Math.sin(Math.PI/18),h=[s,t];for(let t=1;t<=18;t++)this._rotateDirect(n,o,a),h.push(this._add(r,n));e.push(h);break}case it.Em.TripleParallelExtended:{const t=this._cp2(i,0,-2),r=this._cp3(i,t,1,-2),n=this._mid(s,t),o=this._sub(r,t);this._normalize(o);const a=Math.abs(this._crossProduct(o,this._sub(n,t)))/2,h=this._dist(t,r),l=[t,s];l.push([s[0]+o[0]*h*.5,s[1]+o[1]*h*.5]),e.push(l);const c=[];c.push([n[0]-o[0]*a,n[1]-o[1]*a]),c.push([n[0]+o[0]*h*.375,n[1]+o[1]*h*.375]),Nt(c[c.length-1],1),c.push([n[0]+o[0]*h*.75,n[1]+o[1]*h*.75]),e.push(c);const u=[t,r];e.push(u);break}case it.Em.ParallelWithTicks:{const t=this._cp2(i,3,0),r=this._cp3(i,t,.5,-1),n=this._sub(r,t);this._normalize(n);const o=this._crossProduct(n,this._sub(r,s));this._leftPerpendicular(n),this._addAngledTicks(e,s,t,r),this._addAngledTicks(e,this._mix(s,1,n,o),this._mix(t,1,n,o),this._mid(s,t));break}case it.Em.Parallel:{const t=this._cp2(i,3,0),r=this._cp3(i,t,.5,-1),n=this._sub(t,s);this._normalize(n);const o=this._leftPerp(n),a=this._crossProduct(n,this._sub(r,s));let h=[s,t];e.push(h),h=[],h.push([s[0]+o[0]*a,s[1]+o[1]*a]),h.push([t[0]+o[0]*a,t[1]+o[1]*a]),e.push(h);break}case it.Em.PerpendicularToFirstSegment:{const t=this._cp2(i,3,0),r=this._cp3(i,t,.5,-1),n=this._mid(s,t),o=this._sub(t,s);this._normalize(o);const a=this._crossProduct(o,this._sub(r,s));this._leftPerpendicular(o);const h=[];h.push([n[0]-o[0]*a*.25,n[1]-o[1]*a*.25]),h.push([n[0]+o[0]*a*1.25,n[1]+o[1]*a*1.25]),e.push(h);break}case it.Em.ParallelOffset:{const t=this._cp2(i,3,0),r=this._cp3(i,t,.5,-1),n=this._sub(t,s);this._normalize(n);const o=this._crossProduct(n,this._sub(r,s));this._leftPerpendicular(n);const a=[];a.push([s[0]-n[0]*o,s[1]-n[1]*o]),a.push([t[0]-n[0]*o,t[1]-n[1]*o]),e.push(a);const h=[];h.push([s[0]+n[0]*o,s[1]+n[1]*o]),h.push([t[0]+n[0]*o,t[1]+n[1]*o]),e.push(h);break}case it.Em.OffsetOpposite:{const t=this._cp2(i,3,0),r=this._cp3(i,t,.5,-1),n=this._sub(t,s);this._normalize(n);const o=this._crossProduct(n,this._sub(r,s));this._leftPerpendicular(n);const a=[];a.push([s[0]-n[0]*o,s[1]-n[1]*o]),a.push([t[0]-n[0]*o,t[1]-n[1]*o]),e.push(a);break}case it.Em.OffsetSame:{const t=this._cp2(i,3,0),r=this._cp3(i,t,.5,-1),n=this._sub(t,s);this._normalize(n);const o=this._crossProduct(n,this._sub(r,s));this._leftPerpendicular(n);const a=[];a.push([s[0]+n[0]*o,s[1]+n[1]*o]),a.push([t[0]+n[0]*o,t[1]+n[1]*o]),e.push(a);break}case it.Em.CircleWithArc:{let r=this._cp2(i,3,0);const n=this._cp3(i,r,.5,-1);let o,a;if(t>=4)o=i[3],a=this._crossProduct(this._sub(o,r),this._sub(n,r))>0;else{o=r,a=this._crossProduct(this._sub(o,s),this._sub(n,s))>0;const t=24*this._geomUnitsPerPoint,e=this._sub(o,s);this._normalize(e,t);const i=Math.sqrt(2)/2;this._rotateDirect(e,i,a?i:-i),r=this._add(s,e)}const h=this._sub(r,s),l=Math.cos(Math.PI/18),c=Math.sin(Math.PI/18),u=[r];for(let t=1;t<=36;t++)this._rotateDirect(h,l,c),u.push(this._add(s,h));this._add90DegArc(u,r,o,n,a),Nt(u[u.length-8],1),e.push(u);break}case it.Em.DoubleJog:{let r,n,o=this._cp2(i,-3,1);if(r=t>=3?i[2]:this._add(s,this._sub(s,o)),t>=4)n=i[3];else{const t=s;s=o,n=r;const e=this._dist(s,t),i=this._dist(n,t);let a=30*this._geomUnitsPerPoint;.5*e<a&&(a=.5*e),.5*i<a&&(a=.5*i),o=this._mix(s,a/e,t,(e-a)/e),r=this._mix(n,a/i,t,(i-a)/i)}const a=this._mid(s,o),h=this._mid(n,r),l=this._dist(s,o),c=this._dist(r,n);let u=Math.min(l,c)/8;u=Math.min(u,24*this._geomUnitsPerPoint);const f=Math.cos(Math.PI/4);let _=this._sub(s,o);this._normalize(_,u),this._crossProduct(_,this._sub(n,o))>0?this._rotateDirect(_,f,-f):this._rotateDirect(_,f,f);let m=[];m.push(o),m.push(this._add(a,_)),m.push(this._sub(a,_)),m.push(s),e.push(m),_=this._sub(n,r),this._normalize(_,u),this._crossProduct(_,this._sub(s,r))<0?this._rotateDirect(_,f,f):this._rotateDirect(_,f,-f),m=[],m.push(r),m.push(this._add(h,_)),m.push(this._sub(h,_)),m.push(n),e.push(m);break}case it.Em.PerpendicularOffset:{const t=this._cp2(i,-4,1),r=this._cp3(i,t,.882353,-1.94),n=this._sub(r,t);this._crossProduct(n,this._sub(s,t))>0?this._rightPerpendicular(n):this._leftPerpendicular(n);const o=[n[0]/8,n[1]/8],a=this._sub(this._mid(t,r),o);e.push([a,s]);break}case it.Em.LineExcludingLastSegment:{const t=this._arrowPath(i),s=[];let r=t.length-2;for(;r--;)s.push(t[r]);e.push(s);break}case it.Em.MultivertexArrow:{const t=this._arrowPath(i),s=[];this._addArrow(s,t,!1),e.push(s);break}case it.Em.CrossedArrow:{const t=this._arrowPath(i),s=[];this._addArrow(s,t,!0),e.push(s);break}case it.Em.ChevronArrow:{const[t,r]=this._arrowLastSeg(i),n=10*this._geomUnitsPerPoint,o=this._sub(s,t);this._normalize(o);const a=this._crossProduct(o,this._sub(r,t)),h=this._leftPerp(o),l=[r[0]-h[0]*a*2,r[1]-h[1]*a*2],c=[];c.push([r[0]+o[0]*n,r[1]+o[1]*n]),c.push(s),c.push([l[0]+o[0]*n,l[1]+o[1]*n]),e.push(c);break}case it.Em.ChevronArrowOffset:{const[t,r]=this._arrowLastSeg(i),n=this._sub(s,t);this._normalize(n);const o=this._crossProduct(n,this._sub(r,t));this._leftPerpendicular(n);const a=[r[0]-n[0]*o,r[1]-n[1]*o],h=[];h.push([a[0]+n[0]*o*.5,a[1]+n[1]*o*.5]),h.push(this._mid(a,s)),h.push([a[0]-n[0]*o*.5,a[1]-n[1]*o*.5]),e.push(h);break}case it.Em.PartialFirstSegment:{const[t,r]=this._arrowLastSeg(i),n=this._sub(s,t);this._normalize(n);const o=this._crossProduct(n,this._sub(r,t));this._leftPerpendicular(n);const a=[r[0]-n[0]*o,r[1]-n[1]*o];e.push([t,a]);break}case it.Em.Arch:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,1),n=this._sub(s,t),o=this._mix(r,1,n,.55),a=this._mix(r,1,n,-.55),h=[s];this._addBezier2(h,s,o,r,4),this._addBezier2(h,r,a,t,4),e.push(h);break}case it.Em.CurvedParallelTicks:{const t=this._cp2(i,-4,1),r=this._cp3(i,t,.882353,-1.94),n=this._sub(r,t);this._crossProduct(n,this._sub(s,t))>0?this._rightPerpendicular(n):this._leftPerpendicular(n);const o=[n[0]/8,n[1]/8],a=this._sub(this._mid(t,r),o),h=this._sub(this._mix(t,.75,r,.25),o),l=this._sub(this._mix(t,.25,r,.75),o),c=[t];this._addBezier2(c,t,h,a,3),this._addBezier2(c,a,l,r,3),e.push(c);for(let t=0;t<8;t++){const i=c[2*t+1],s=[this._clone(i)];s.push(this._add(i,[n[0]/4,n[1]/4])),e.push(s)}break}case it.Em.Arc90Degrees:{const t=this._cp2(i,0,-1),r=this._cp3(i,t,.5,1),n=[t];this._add90DegArc(n,t,s,r),e.push(n);break}case it.Em.FullGeometry:default:e.push(i)}}return e}}class se{static local(){return null===se.instance&&(se.instance=new se),se.instance}execute(t,e,i,s,r){return new re(t,e,i)}}se.instance=null;class re extends Wt{constructor(t,e,i){super(t,!0,!0),this._curveHelper=new Xt,this._beginCut=(void 0!==e.beginCut?e.beginCut:1)*i,this._endCut=(void 0!==e.endCut?e.endCut:1)*i,this._middleCut=(void 0!==e.middleCut?e.middleCut:0)*i,this._invert=void 0!==e.invert&&e.invert,this._beginCut<0&&(this._beginCut=0),this._endCut<0&&(this._endCut=0),this._middleCut<0&&(this._middleCut=0)}processPath(t){const e=this._beginCut,i=this._endCut,s=this._middleCut,r=this._curveHelper.calculatePathLength(t),n=[];if(this._invert)if(0===e&&0===i&&0===s);else if(e+i+s>=r)n.push(t);else{let o=this._curveHelper.getSubCurve(t,0,e);o&&n.push(o),o=this._curveHelper.getSubCurve(t,.5*(r-s),.5*(r+s)),o&&n.push(o),o=this._curveHelper.getSubCurve(t,r-i,i),o&&n.push(o)}else if(0===e&&0===i&&0===s)n.push(t);else if(e+i+s>=r);else if(0===s){const s=this._curveHelper.getSubCurve(t,e,r-i);s&&n.push(s)}else{let o=this._curveHelper.getSubCurve(t,e,.5*(r-s));o&&n.push(o),o=this._curveHelper.getSubCurve(t,.5*(r+s),r-i),o&&n.push(o)}return 0===n.length?null:{paths:n}}}class ne{constructor(){this._values=[],this.extPtGap=0,this.ctrlPtGap=0,this._length=0,this._currentValue=0}isEmpty(){return 0===this._values.length}size(){return this._values.length}init(t,e,i=!0){if(this._setEmpty(),!t||0===t.length)return!1;for(let e=0;e<t.length;e++){let s=Math.abs(t[e]);i&&s<1e-7&&(s=1e-7),this._values.push(s),this._length+=s}return e&&1&t.length&&(this._length*=2),0!==this._length&&(this.ctrlPtGap=this.extPtGap=0,this._currentValue=-1,!0)}scale(t){const e=this._values?this._values.length:0;for(let i=0;i<e;++i)this._values[i]*=t;this._length*=t,this.extPtGap*=t,this.ctrlPtGap*=t}addValue(t){this._length+=t,this._values.push(t)}firstValue(){return this._values[0]}lastValue(){return this._values[this._values.length-1]}nextValue(){return this._currentValue++,this._currentValue===this._values.length&&(this._currentValue=0),this._values[this._currentValue]}reset(){this._currentValue=-1}length(){return this._length}_setEmpty(){this.extPtGap=this.ctrlPtGap=this._length=0,this._currentValue=-1,this._values.length=0}}class oe{constructor(){this.pt=null,this.ca=0,this.sa=0}}var ae,he;(he=ae||(ae={}))[he.FAIL=0]="FAIL",he[he.END=1]="END",he[he.CONTINUE=2]="CONTINUE";class le{constructor(){this.reset()}reset(){this.segment=-1,this.segmentLength=0,this.abscissa=0,this.isPathEnd=!1,this.isPartEnd=!1}isValid(){return-1!==this.segment}copyTo(t){t.segment=this.segment,t.segmentLength=this.segmentLength,t.abscissa=this.abscissa,t.isPathEnd=this.isPathEnd,t.isPartEnd=this.isPartEnd}}class ce extends Xt{constructor(t=0,e=!1){super(t,e),this._tolerance=Ut,this._currentPosition=new le}updateTolerance(t){this._tolerance=Ut*t}init(t,e,i=!0){return i?(this._patternLength=e.length(),this._partExtPtGap=e.extPtGap,this._partCtrlPtGap=e.ctrlPtGap):(this._patternLength=0,this._partExtPtGap=0,this._partCtrlPtGap=0),this._currentPosition.reset(),this._partSegCount=0,this._path=t,this._seg=-1,this._setPosAtNextPart()}curPositionIsValid(){return this._currentPosition.isValid()}nextPosition(t,e=ae.FAIL){const i=new le;return!!this._nextPosition(t,i,null,e)&&(i.copyTo(this._currentPosition),!0)}curPointAndAngle(t){t.pt=this._getPoint(this._currentPosition);const[e,i]=this._getAngle(this._currentPosition);t.ca=e,t.sa=i}nextPointAndAngle(t,e,i=ae.FAIL){const s=new le;if(!this._nextPosition(t,s,null,i))return!1;s.copyTo(this._currentPosition),e.pt=this._getPoint(s);const[r,n]=this._getAngle(s);return e.ca=r,e.sa=n,!0}nextCurve(t){if(0===t)return null;const e=[],i=new le;return this._nextPosition(t,i,e,ae.END)?(i.copyTo(this._currentPosition),e):null}isPathEnd(){return this._currentPosition.isPathEnd}getPathEnd(){if(-1===this._currentPosition.segment)throw new Error("missing segment");return this._path[this._currentPosition.segment+1]}_nextPosition(t,e,i,s){if(this._currentPosition.isPathEnd)return!1;let r=this._currentPosition.abscissa;for(this._currentPosition.segmentLength>0&&(r/=this._currentPosition.segmentLength),this._currentPosition.copyTo(e);e.abscissa+t*this._partLengthRatio>e.segmentLength+this._tolerance;){if(i){if(0===i.length)if(0===r){const t=this._path[e.segment];i.push([t[0],t[1]])}else i.push(this.getSegCoord2D(this._path,e.segment,r));const t=this._path[e.segment+1];i.push([t[0],t[1]])}if(r=0,t-=(e.segmentLength-e.abscissa)/this._partLengthRatio,this._partSegCount)e.segment=this._nextSegment(),e.segmentLength=this.calculateSegLength(this._path,e.segment),e.abscissa=0,this._partSegCount--;else{if(!this._setPosAtNextPart())return s!==ae.FAIL&&(e.segmentLength=this.calculateSegLength(this._path,e.segment),e.isPartEnd=!0,s===ae.END?(e.abscissa=e.segmentLength,e.isPathEnd=!0):e.abscissa=e.segmentLength+t,!0);this._currentPosition.copyTo(e)}}if(e.abscissa+=t*this._partLengthRatio,i){if(0===i.length)if(0===r){const t=this._path[e.segment];i.push([t[0],t[1]])}else i.push(this.getSegCoord2D(this._path,e.segment,r));const t=e.abscissa/e.segmentLength;if(1===t){const t=this._path[e.segment+1];i.push([t[0],t[1]])}else i.push(this.getSegCoord2D(this._path,e.segment,t))}return this._partSegCount||Math.abs(e.abscissa-e.segmentLength)<this._tolerance&&(e.isPathEnd=this._partIsLast,e.isPartEnd=!0),!0}_getPoint(t){if(-1===t.segment)throw new Error("missing segment");const e=t.segmentLength<=0?0:t.abscissa/t.segmentLength;return this.getSegCoord2D(this._path,t.segment,e)}_getAngle(t){if(-1===t.segment)throw new Error("missing segment");const e=t.segmentLength<=0?0:t.abscissa/t.segmentLength;return this.getSegAngleCS(this._path,t.segment,e)}_setPosAtNextPart(){for(;this._partSegCount;)this._hasNextSegment()&&this._nextSegment(),this._partSegCount--;if(!this._hasNextSegment())return!1;for(this._partLength=0,this._partIsLast=!0,this._partSegCount=0;this._hasNextSegment();)if(this._partLength+=this.calculateSegLength(this._path,this._nextSegment()),this._partSegCount++,1===Rt(this._path[this._getEndPointIndex()])){this._partIsLast=!this._hasNextSegment();break}let t=this._partSegCount;for(;t;)this._previousSegment(),--t;this._currentPosition.segment=this._nextSegment(),this._currentPosition.segmentLength=this.calculateSegLength(this._path,this._currentPosition.segment),this._currentPosition.abscissa=0,this._currentPosition.isPathEnd=this._currentPosition.isPartEnd=!1,--this._partSegCount;const e=this._getStartPointIndex();this._ctrlPtBegin=1===Rt(this._path[e]);let i=e+this._partSegCount+1;if(i>=this._path.length&&(i=0),this._ctrlPtEnd=1===Rt(this._path[i]),this._patternLength>0){const t=this._ctrlPtBegin?this._partCtrlPtGap:this._partExtPtGap,e=this._ctrlPtEnd?this._partCtrlPtGap:this._partExtPtGap;let i=Math.round((this._partLength-(t+e))/this._patternLength);i<=0&&(i=t+e>0?0:1),this._partLengthRatio=this._partLength/(t+e+i*this._patternLength),this._partLengthRatio<.01&&(this._partLengthRatio=1)}else this._partLengthRatio=1;return!0}_hasNextSegment(){return this._seg<this._path.length-2}_previousSegment(){return--this._seg}_nextSegment(){return++this._seg}_getStartPointIndex(){return this._seg}_getEndPointIndex(){return this._seg+1}}class ue{static local(){return null===ue.instance&&(ue.instance=new ue),ue.instance}execute(t,e,i,s,r){return new fe(t,e,i)}}ue.instance=null;class fe extends Wt{constructor(t,e,i){super(t,!0,!0),this._firstCurve=null,this._walker=new ce,this._walker.updateTolerance(i),this._endings=e.lineDashEnding,this._customDashPos=-(e.offsetAlongLine??0)*i,this._offsetAtEnd=(e.customEndingOffset??0)*i,this._pattern=new ne,this._pattern.init(e.dashTemplate,!0),this._pattern.scale(i)}processPath(t){if(0===this._pattern.length())return this.iteratePath=!1,{paths:[t]};if(!this.iteratePath){let e=!0;switch(this._endings){case it.sj.HalfPattern:case it.sj.HalfGap:default:this._pattern.extPtGap=0;break;case it.sj.FullPattern:this.isClosed||(this._pattern.extPtGap=.5*this._pattern.firstValue());break;case it.sj.FullGap:this.isClosed||(this._pattern.extPtGap=.5*this._pattern.lastValue());break;case it.sj.NoConstraint:this.isClosed||(e=!1);break;case it.sj.Custom:this.isClosed||(this._pattern.extPtGap=.5*this._offsetAtEnd)}const i=this._walker.calculatePathLength(t);if(this._pattern.isEmpty()||i<.1*this._pattern.length())return{paths:[t]};if(!this._walker.init(t,this._pattern,e))return{paths:[t]}}let e;if(this.iteratePath)e=this._pattern.nextValue();else{let t;switch(this._endings){case it.sj.HalfPattern:default:t=.5*this._pattern.firstValue();break;case it.sj.HalfGap:t=.5*-this._pattern.lastValue();break;case it.sj.FullGap:t=-this._pattern.lastValue();break;case it.sj.FullPattern:t=0;break;case it.sj.NoConstraint:case it.sj.Custom:t=-this._customDashPos}let i=t/this._pattern.length();i-=Math.floor(i),t=i*this._pattern.length(),this._pattern.reset(),e=this._pattern.nextValue();let s=!1;for(;t>=e;)t-=e,e=this._pattern.nextValue(),s=!s;e-=t,s?(this._walker.nextPosition(e),e=this._pattern.nextValue()):this.isClosed&&(this._firstCurve=this._walker.nextCurve(e),e=this._pattern.nextValue(),this._walker.nextPosition(e),e=this._pattern.nextValue())}let i=this._walker.nextCurve(e);return i?this._walker.isPathEnd()?(this.iteratePath=!1,this._firstCurve&&(this._firstCurve.splice(0,1),Gt.mergePath(i,this._firstCurve),this._firstCurve=null)):(e=this._pattern.nextValue(),!this._walker.nextPosition(e)||this._walker.isPathEnd()?(this.iteratePath=!1,this._firstCurve&&(i=this._firstCurve,this._firstCurve=null)):this.iteratePath=!0):(this.iteratePath=!1,i=this._firstCurve,this._firstCurve=null),{paths:[i]}}}class _e{static local(){return null===_e.instance&&(_e.instance=new _e),_e.instance}execute(t,e,i,s,r){return new me(t,e,i,s,r)}}_e.instance=null;class me{constructor(t,e,i,s,r){switch(this._inputGeometries=t,this._tileKey=s,this._geometryEngine=r,this._width=(void 0!==e.width?e.width:2)*i,e.method){case it.$y.Mitered:case it.$y.Bevelled:case it.$y.Rounded:case it.$y.TrueBuffer:case it.$y.Square:}this._option=e.option}next(){let t;for(;t=this._inputGeometries.next();){if((0,vt.YX)(t)&&this._width>0){if(Math.min(t.xmax-t.xmin,t.ymax-t.ymin)-2*this._width<0)return t;const e=[];return e.push([[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]),e.push([[t.xmin+this._width,t.ymin+this._width],[t.xmax-this._width,t.ymin+this._width],[t.xmax-this._width,t.ymax-this._width],[t.xmin+this._width,t.ymax-this._width],[t.xmin+this._width,t.ymin+this._width]]),{rings:e}}if((0,vt.oU)(t)){let e=null;const i=this._geometryEngine;let s=t;if(this._tileKey&&(s=$t(t,Math.abs(this._width)+1),!s||!s.rings||0===s.rings.length))continue;if((0,l.pC)(i)&&(e=i.buffer(_.Z.WebMercator,s,-this._width,1)),this._width>0){const i=[];for(const e of t.rings)e&&i.push(e);if(e)for(const t of e.rings)t&&i.push(t.reverse());if(i.length)return{rings:i}}}}return null}}class de{static local(){return null===de.instance&&(de.instance=new de),de.instance}execute(t,e,i,s,r){return new pe(t,e,i)}}de.instance=null;class pe extends Wt{constructor(t,e,i){super(t,!1,!0),this._curveHelper=new Xt,this._length=(void 0!==e.length?e.length:20)*i,this._angle=void 0!==e.angle?e.angle:225,this._position=void 0!==e.position?e.position:50,this._length<0&&(this._length=-this._length),this._position<20&&(this._position=20),this._position>80&&(this._position=80),this._mirror=!1}processPath(t){if(this._curveHelper.isEmpty(t,!1))return null;const e=t[0],i=t[t.length-1],s=[i[0]-e[0],i[1]-e[1]];this._curveHelper.normalize(s);const r=[e[0]+(i[0]-e[0])*this._position/100,e[1]+(i[1]-e[1])*this._position/100],n=Math.cos((90-this._angle)/180*Math.PI);let o=Math.sin((90-this._angle)/180*Math.PI);return this._mirror&&(o=-o),this._mirror=!this._mirror,{paths:[[e,[r[0]-this._length/2*n,r[1]-this._length/2*o],[r[0]+this._length/2*n,r[1]+this._length/2*o],i]]}}}class ge{static local(){return null===ge.instance&&(ge.instance=new ge),ge.instance}execute(t,e,i,s,r){return new ye(t,e,i)}}ge.instance=null;class ye{constructor(t,e,i){this._inputGeometries=t,this._offsetX=void 0!==e.offsetX?e.offsetX*i:0,this._offsetY=void 0!==e.offsetY?-e.offsetY*i:0}next(){let t=this._inputGeometries.next();for(;t;){if((0,vt.YX)(t))return{xmin:t.xmin+this._offsetX,xmax:t.xmax+this._offsetX,ymin:t.ymin+this._offsetY,ymax:t.ymax+this._offsetY};if((0,vt.oU)(t)){const e=(0,Mt.d9)(t);return this._moveMultipath(e.rings,this._offsetX,this._offsetY),e}if((0,vt.l9)(t)){const e=(0,Mt.d9)(t);return this._moveMultipath(e.paths,this._offsetX,this._offsetY),e}if((0,vt.aW)(t)){const e=(0,Mt.d9)(t);return this._movePath(e.points,this._offsetX,this._offsetY),e}if((0,vt.wp)(t))return{x:t.x+this._offsetX,y:t.y+this._offsetY};t=this._inputGeometries.next()}return null}_moveMultipath(t,e,i){if(t)for(const s of t)this._movePath(s,e,i)}_movePath(t,e,i){if(t)for(const s of t)s[0]+=e,s[1]+=i}}class xe{static local(){return null===xe.instance&&(xe.instance=new xe),xe.instance}execute(t,e,i,s,r){return new be(t,e,i,s,r)}}xe.instance=null;class be{constructor(t,e,i,s,r){this._inputGeometries=t,this._tileKey=s,this._geometryEngine=r,this._curveHelper=new Xt,this._offset=(e.offset??1)*i,this._method=e.method,this._option=e.option,this._offsetFlattenError=Ut*i}next(){let t;for(;t=this._inputGeometries.next();){if(0===this._offset)return t;if((0,vt.YX)(t)){if(this._method===it.id.Rounded&&this._offset>0){const e=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]],i=this._curveHelper.offset(e,-this._offset,this._method,4,this._offsetFlattenError);return i?{rings:[i]}:null}if(Math.min(t.xmax-t.xmin,t.ymax-t.ymin)+2*this._offset>0)return{xmin:t.xmin-this._offset,xmax:t.xmax+this._offset,ymin:t.ymin-this._offset,ymax:t.ymax+this._offset}}const e=this._geometryEngine;if((0,l.Wi)(e))return null;let i=t;if((0,vt.oU)(t)){if(this._tileKey&&(i=$t(t,Math.abs(this._offset)+1),!i||!i.rings||0===i.rings.length))continue}else if((0,vt.l9)(t)&&this._tileKey&&(i=jt(t,Math.abs(this._offset)+1),!i||!i.paths||0===i.paths.length))continue;return e.offset(_.Z.WebMercator,i,-this._offset,1,this._method,4,this._offsetFlattenError)}return null}}class Me{static local(){return null===Me.instance&&(Me.instance=new Me),Me.instance}execute(t,e,i,s,r){return new ve(t,e,i)}}Me.instance=null;class ve{constructor(t,e,i){this._inputGeometries=t,this._reverse=void 0===e.reverse||e.reverse}next(){let t=this._inputGeometries.next();for(;t;){if(!this._reverse)return t;if((0,vt.l9)(t)){const e=(0,Mt.d9)(t);return At(e.paths),e}t=this._inputGeometries.next()}return null}}var Se=i(20322);class we{static local(){return null===we.instance&&(we.instance=new we),we.instance}execute(t,e,i,s,r){return new Pe(t,e,i)}}we.instance=null;class Pe{constructor(t,e,i){this._inputGeometries=t,this._rotateAngle=void 0!==e.angle?e.angle*Math.PI/180:0}next(){let t=this._inputGeometries.next();for(;t;){if(0===this._rotateAngle)return t;const e=(0,z.Ue)();(0,Se.$P)(e,t);const i=(e[2]+e[0])/2,s=(e[3]+e[1])/2;if((0,vt.YX)(t)){const e={rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]};return this._rotateMultipath(e.rings,i,s),e}if((0,vt.oU)(t)){const e=(0,Mt.d9)(t);return this._rotateMultipath(e.rings,i,s),e}if((0,vt.l9)(t)){const e=(0,Mt.d9)(t);return this._rotateMultipath(e.paths,i,s),e}if((0,vt.aW)(t)){const e=(0,Mt.d9)(t);return this._rotatePath(e.points,i,s),e}if((0,vt.wp)(t))return t;t=this._inputGeometries.next()}return null}_rotateMultipath(t,e,i){if(t)for(const s of t)this._rotatePath(s,e,i)}_rotatePath(t,e,i){if(t){const s=Math.cos(this._rotateAngle),r=Math.sin(this._rotateAngle);for(const n of t){const t=n[0]-e,o=n[1]-i;n[0]=e+t*s-o*r,n[1]=i+t*r+o*s}}}}class Ce{static local(){return null===Ce.instance&&(Ce.instance=new Ce),Ce.instance}execute(t,e,i,s,r){return new Le(t,e,i)}}Ce.instance=null;class Le{constructor(t,e,i){this._inputGeometries=t,this._xFactor=void 0!==e.xScaleFactor?e.xScaleFactor:1.15,this._yFactor=void 0!==e.yScaleFactor?e.yScaleFactor:1.15}next(){let t=this._inputGeometries.next();for(;t;){if(1===this._xFactor&&1===this._yFactor)return t;const e=(0,z.Ue)();(0,Se.$P)(e,t);const i=(e[2]+e[0])/2,s=(e[3]+e[1])/2;if((0,vt.YX)(t)){const e={rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]};return this._scaleMultipath(e.rings,i,s),e}if((0,vt.oU)(t)){const e=(0,Mt.d9)(t);return this._scaleMultipath(e.rings,i,s),e}if((0,vt.l9)(t)){const e=(0,Mt.d9)(t);return this._scaleMultipath(e.paths,i,s),e}if((0,vt.aW)(t)){const e=(0,Mt.d9)(t);return this._scalePath(e.points,i,s),e}if((0,vt.wp)(t))return t;t=this._inputGeometries.next()}return null}_scaleMultipath(t,e,i){if(t)for(const s of t)this._scalePath(s,e,i)}_scalePath(t,e,i){if(t)for(const s of t){const t=(s[0]-e)*this._xFactor,r=(s[1]-i)*this._yFactor;s[0]=e+t,s[1]=i+r}}}class Ie{static local(){return null===Ie.instance&&(Ie.instance=new Ie),Ie.instance}execute(t,e,i,s,r){return new ke(t,e,i)}}Ie.instance=null;class ke{constructor(t,e,i){this._inputGeometries=t,this._height=(void 0!==e.amplitude?e.amplitude:2)*i,this._period=(void 0!==e.period?e.period:3)*i,this._style=e.waveform,this._height<=0&&(this._height=Math.abs(this._height)),this._period<=0&&(this._period=Math.abs(this._period)),this._pattern=new ne,this._pattern.addValue(this._period),this._pattern.addValue(this._period),this._walker=new ce,this._walker.updateTolerance(i)}next(){let t=this._inputGeometries.next();for(;t;){if(0===this._height||0===this._period)return t;if((0,vt.l9)(t)){const e=this._processGeom(t.paths);if(e.length)return{paths:e}}if((0,vt.oU)(t)){const e=this._processGeom(t.rings);if(e.length)return{rings:e}}t=this._inputGeometries.next()}return null}_processGeom(t){const e=[];for(const i of t)if(this._walker.init(i,this._pattern))switch(this._style){case it.zQ.Sinus:default:e.push(this._constructCurve(i,!1));break;case it.zQ.Square:e.push(this._constructSquare(i));break;case it.zQ.Triangle:e.push(this._constructTriangle(i));break;case it.zQ.Random:e.push(this._constructCurve(i,!0))}else e.push(i);return e}_constructCurve(t,e){const i=new Gt,s=this._walker.calculatePathLength(t);let r=Math.round(s/this._period);0===r&&(r=1);const n=16*r+1,o=s/r,a=this._period/16,h=1/n,l=2*Math.PI*s/o,c=2*Math.PI*Math.random(),u=2*Math.PI*Math.random(),f=2*Math.PI*Math.random(),_=.75-Math.random()/2,m=.75-Math.random()/2,d=new oe;this._walker.curPointAndAngle(d),i.startPath(d.pt);let p=0;for(;;){if(!this._walker.nextPointAndAngle(a,d)){i.lineTo(t[t.length-1]);break}{const t=p;let s;if(p+=h,e){const e=this._height/2*(1+.3*Math.sin(_*l*t+c));s=e*Math.sin(l*t+u),s+=e*Math.sin(m*l*t+f),s/=2}else s=.5*this._height*Math.sin(.5*l*t);i.lineTo([d.pt[0]-s*d.sa,d.pt[1]+s*d.ca])}}return i.path()}_constructSquare(t){const e=new Gt,i=this._walker.calculatePathLength(t);Math.round(i/this._period);let s=!0;for(;;){let t=!1;if(this._walker.curPositionIsValid()){const i=new oe;this._walker.curPointAndAngle(i);const r=new oe;if(this._walker.nextPointAndAngle(this._period,r)){const n=new oe;this._walker.nextPointAndAngle(this._period,n)&&(s?(e.startPath(i.pt),s=!1):e.lineTo(i.pt),e.lineTo([i.pt[0]-this._height/2*i.sa,i.pt[1]+this._height/2*i.ca]),e.lineTo([r.pt[0]-this._height/2*r.sa,r.pt[1]+this._height/2*r.ca]),e.lineTo([r.pt[0]+this._height/2*r.sa,r.pt[1]-this._height/2*r.ca]),e.lineTo([n.pt[0]+this._height/2*n.sa,n.pt[1]-this._height/2*n.ca]),t=!0)}}if(!t){e.lineTo(this._walker.getPathEnd());break}}return e.path()}_constructTriangle(t){const e=new Gt,i=this._walker.calculatePathLength(t);Math.round(i/this._period);let s=!0;for(;;){let t=!1;if(this._walker.curPositionIsValid()){const i=new oe;this._walker.curPointAndAngle(i);const r=new oe;if(this._walker.nextPointAndAngle(this._period/2,r)){const n=new oe;this._walker.nextPointAndAngle(this._period,n)&&(this._walker.nextPosition(this._period/2)&&(s?(e.startPath(i.pt),s=!1):e.lineTo(i.pt),e.lineTo([r.pt[0]-this._height/2*r.sa,r.pt[1]+this._height/2*r.ca]),e.lineTo([n.pt[0]+this._height/2*n.sa,n.pt[1]-this._height/2*n.ca])),t=!0)}}if(!t){e.lineTo(this._walker.getPathEnd());break}}return e.path()}}class Te{static local(){return null===Te.instance&&(Te.instance=new Te),Te.instance}execute(t,e,i,s,r){return new ze(t,e,i)}}Te.instance=null;class ze extends Ot{constructor(t,e,i){super(t,!0,!0),this._geometryWalker=new ce,this._geometryWalker.updateTolerance(i),this._angleToLine=e.angleToLine??!0,this._offset=(e.offset?e.offset:0)*i,this._originalEndings=e.endings,this._offsetAtEnd=(e.customEndingOffset?e.customEndingOffset:0)*i,this._position=-(e.offsetAlongLine?e.offsetAlongLine:0)*i,this._pattern=new ne,this._pattern.init(e.placementTemplate,!1),this._pattern.scale(i),this._endings=this._originalEndings}processPath(t){if(this._pattern.isEmpty())return null;let e;if(this.iteratePath)e=this._pattern.nextValue();else{this._originalEndings===it.JS.WithFullGap&&this.isClosed?this._endings=it.JS.WithMarkers:this._endings=this._originalEndings,this._pattern.extPtGap=0;let i,s=!0;switch(this._endings){case it.JS.NoConstraint:i=-this._position,i=this._adjustPosition(i),s=!1;break;case it.JS.WithHalfGap:default:i=-this._pattern.lastValue()/2;break;case it.JS.WithFullGap:i=-this._pattern.lastValue(),this._pattern.extPtGap=this._pattern.lastValue();break;case it.JS.WithMarkers:i=0;break;case it.JS.Custom:i=-this._position,i=this._adjustPosition(i),this._pattern.extPtGap=.5*this._offsetAtEnd}if(!this._geometryWalker.init(t,this._pattern,s))return null;this._pattern.reset();let r=0;for(;i>r;)i-=r,r=this._pattern.nextValue();r-=i,e=r,this.iteratePath=!0}const i=new oe;return this._geometryWalker.nextPointAndAngle(e,i)?this._endings===it.JS.WithFullGap&&this._geometryWalker.isPathEnd()?(this.iteratePath=!1,null):this._endings===it.JS.WithMarkers&&this._geometryWalker.isPathEnd()&&(this.iteratePath=!1,this.isClosed)?null:(this.internalPlacement.setTranslate(i.pt[0]-this._offset*i.sa,i.pt[1]+this._offset*i.ca),this._angleToLine&&this.internalPlacement.setRotateCS(i.ca,i.sa),this.internalPlacement):(this.iteratePath=!1,null)}_adjustPosition(t){let e=t/this._pattern.length();return e-=Math.floor(e),e*this._pattern.length()}}class Ae{static local(){return null===Ae.instance&&(Ae.instance=new Ae),Ae.instance}execute(t,e,i,s,r){return new Ee(t,e,i)}}Ae.instance=null;class Ee extends Ot{constructor(t,e,i){super(t,!1,!0),this._curveHelper=new Xt,this._angleToLine=void 0===e.angleToLine||e.angleToLine,this._offset=void 0!==e.offset?e.offset*i:0,this._type=e.extremityPlacement,this._position=void 0!==e.offsetAlongLine?e.offsetAlongLine*i:0,this._beginProcessed=!1}processPath(t){let e;switch(this._type){case it.Tx.Both:default:this._beginProcessed?(e=this._atExtremities(t,this._position,!1),this._beginProcessed=!1,this.iteratePath=!1):(e=this._atExtremities(t,this._position,!0),this._beginProcessed=!0,this.iteratePath=!0);break;case it.Tx.JustBegin:e=this._atExtremities(t,this._position,!0);break;case it.Tx.JustEnd:e=this._atExtremities(t,this._position,!1);case it.Tx.None:}return e}_atExtremities(t,e,i){const s=t.length;if(s<2)return null;const r=i?1:s-2,n=i?s:-1,o=i?1:-1;let a,h=0,l=i?t[0]:t[s-1];for(let i=r;i!==n;i+=o){a=l,l=t[i];const s=this._curveHelper.calculateLength(a,l);if(h+s>e){const t=(e-h)/s,[i,r]=this._curveHelper.getAngleCS(a,l,t),n=Ft(a,l,t);return this.internalPlacement.setTranslate(n[0]-this._offset*r,n[1]+this._offset*i),this._angleToLine&&this.internalPlacement.setRotateCS(-i,-r),this.internalPlacement}h+=s}return null}}class Fe{static local(){return null===Fe.instance&&(Fe.instance=new Fe),Fe.instance}execute(t,e,i,s,r){return new Re(t,e,i)}}Fe.instance=null;class Re extends Ot{constructor(t,e,i){super(t,!0,!0),this._walker=new ce,this._walker.updateTolerance(i),this._angleToLine=void 0===e.angleToLine||e.angleToLine,this._offset=void 0!==e.offset?e.offset*i:0,this._beginGap=void 0!==e.beginPosition?e.beginPosition*i:0,this._endGap=void 0!==e.endPosition?e.endPosition*i:0,this._flipFirst=void 0===e.flipFirst||e.flipFirst,this._pattern=new ne,this._pattern.init(e.positionArray,!1,!1),this._subPathLen=0,this._posCount=this._pattern.size(),this._isFirst=!0,this._prevPos=0}processPath(t){if(this._pattern.isEmpty())return null;let e;if(this.iteratePath){const t=this._pattern.nextValue()*this._subPathLen,i=this._beginGap+t;e=i-this._prevPos,this._prevPos=i}else{if(this._posCount=this._pattern.size(),this._isFirst=!0,this._prevPos=0,this._subPathLen=this._walker.calculatePathLength(t)-this._beginGap-this._endGap,this._subPathLen<0)return this.iteratePath=!1,null;if(!this._walker.init(t,this._pattern,!1))return null;this._pattern.reset();const i=this._pattern.nextValue()*this._subPathLen,s=this._beginGap+i;e=s-this._prevPos,this._prevPos=s,this.iteratePath=!0}const i=new oe;if(!this._walker.nextPointAndAngle(e,i,ae.END))return this.iteratePath=!1,null;this.internalPlacement.setTranslate(i.pt[0]-this._offset*i.sa,i.pt[1]+this._offset*i.ca);const s=this._isFirst&&this._flipFirst;let r,n;return this._angleToLine?(r=i.ca,n=i.sa):(r=1,n=0),s&&(r=-r,n=-n),this.internalPlacement.setRotateCS(r,n),this._isFirst=!1,this._posCount--,0===this._posCount&&(this.iteratePath=!1),this.internalPlacement}}var Ne=i(77734);const Oe=512,We=24;class Ve{static local(){return null===Ve.instance&&(Ve.instance=new Ve),Ve.instance}execute(t,e,i,s,r){return new De(t,e,i,s,r)}}Ve.instance=null;class De{constructor(t,e,i,s,r){if(this._xMin=0,this._xMax=0,this._yMin=0,this._yMax=0,this._currentX=0,this._currentY=0,this._accelerationMap=null,this._testInsidePolygon=!1,this._verticalSubdivision=!0,this._stepX=Math.abs(e.stepX??16)*i,this._stepY=Math.abs(e.stepY??16)*i,0!==this._stepX&&0!==this._stepY&&t&&function(t){return void 0!==t.rings}(t)&&t.rings){if(this._gridType=e.gridType??it.bj.Fixed,this._gridType===it.bj.Random){const t=e.seed??13,i=1;this._randomLCG=new Ne.Z(t*i),this._randomness=(e.randomness??100)/100,this._gridAngle=0,this._shiftOddRows=!1,this._cosAngle=1,this._sinAngle=0,this._offsetX=0,this._offsetY=0,this._buildRandomValues()}else{if(this._randomness=0,this._gridAngle=e.gridAngle??0,this._shiftOddRows=e.shiftOddRows??!1,this._offsetX=(e.offsetX??0)*i,this._offsetY=(e.offsetY??0)*i,this._cosAngle=Math.cos(this._gridAngle/180*Math.PI),this._sinAngle=-Math.sin(this._gridAngle/180*Math.PI),this._stepX)if(this._offsetX<0)for(;this._offsetX<-.5*this._stepX;)this._offsetX+=this._stepX;else for(;this._offsetX>=.5*this._stepX;)this._offsetX-=this._stepX;if(this._stepY)if(this._offsetY<0)for(;this._offsetY<-.5*this._stepY;)this._offsetY+=this._stepY;else for(;this._offsetY>=.5*this._stepY;)this._offsetY-=this._stepY}if(this._graphicOriginX=0,this._graphicOriginY=0,null!=s){const[t,e,i]=s.split("/"),r=parseFloat(e),n=parseFloat(i);this._graphicOriginX=-n*Oe,this._graphicOriginY=r*Oe,this._testInsidePolygon=!0}this._internalPlacement=new St,this._calculateMinMax(t),this._geometry=t}}next(){return this._geometry?this._nextInside():null}_buildRandomValues(){if(!De._randValues){De._randValues=[];for(let t=0;t<We;t++)for(let t=0;t<We;t++)De._randValues.push(this._randomLCG.getFloat()),De._randValues.push(this._randomLCG.getFloat())}}_calculateMinMax(t){let e,i,s,r,n,o,a,h,l,c,u,f,_,m;this._xMin=0,this._xMax=0,this._yMin=0,this._yMax=0,a=h=_=u=Number.MAX_VALUE,l=c=m=f=-Number.MAX_VALUE;const d=1!==this._cosAngle;let p=0;for(const g of t.rings){const t=g?g.length:0;for(let y=0;y<t;y++)o=g[y][0],n=g[y][1],e=o-this._graphicOriginX-this._offsetX,i=n-this._graphicOriginY-this._offsetY,d?(s=this._cosAngle*e-this._sinAngle*i,r=this._sinAngle*e+this._cosAngle*i):(s=e,r=i),a=Math.min(a,s),l=Math.max(l,s),h=Math.min(h,r),c=Math.max(c,r),u=Math.min(u,n),f=Math.max(f,n),_=Math.min(_,o),m=Math.max(m,o),p++}u=u!==Number.MAX_VALUE?u:-512-this._stepY,f=f!==-Number.MAX_VALUE?f:this._stepY,_=_!==Number.MAX_VALUE?_:-this._stepX,m=m!==-Number.MAX_VALUE?m:Oe+this._stepX;const g=f-u,y=m-_;if(this._verticalSubdivision=g>=y,this._polygonMin=this._verticalSubdivision?u:_,this._testInsidePolygon){let t=0-this._graphicOriginX-this._offsetX-this._stepX,e=Oe-this._graphicOriginX-this._offsetX+this._stepX,i=-512-this._graphicOriginY-this._offsetY-this._stepY,s=0-this._graphicOriginY-this._offsetY+this._stepY;if(d){const r=[[t,i],[t,s],[e,i],[e,s]];t=i=Number.MAX_VALUE,e=s=-Number.MAX_VALUE;for(const n of r){const r=this._cosAngle*n[0]-this._sinAngle*n[1],o=this._sinAngle*n[0]+this._cosAngle*n[1];t=Math.min(t,r),e=Math.max(e,r),i=Math.min(i,o),s=Math.max(s,o)}}a=a!==Number.MAX_VALUE?Math.max(a,t):t,h=h!==Number.MAX_VALUE?Math.max(h,i):i,l=l!==-Number.MAX_VALUE?Math.min(l,e):e,c=c!==-Number.MAX_VALUE?Math.min(c,s):s}this._xMin=Math.round(a/this._stepX),this._xMax=Math.round(l/this._stepX),this._yMin=Math.round(h/this._stepY),this._yMax=Math.round(c/this._stepY),this._currentX=this._xMax+1,this._currentY=this._yMin-1,this._testInsidePolygon&&p>12&&(g>25||y>25)&&this._buildAccelerationMap(t,_,m,u,f)}_buildAccelerationMap(t,e,i,s,r){const{rings:n}=t,o=new Map,a=this._verticalSubdivision,h=a?r-s:i-e;let l=Math.ceil(h/10);if(l<=1)return;const c=Math.floor(h/l);let u,f,_,m,d,p,g,y,x,b;l++,this._delta=c,a?(y=-512-this._stepY,x=this._stepY,b=s):(y=-this._stepX,x=Oe+this._stepX,b=e);for(let t=0;t<n.length;t++)if(u=n[t],!(u.length<2))for(let e=1;e<u.length;e++){if(f=u[e-1],_=u[e],a){if(f[1]===_[1]||f[1]<y&&_[1]<y||f[1]>x&&_[1]>x)continue;m=Math.min(f[1],_[1]),d=Math.max(f[1],_[1])}else{if(f[0]===_[0]||f[0]<y&&_[0]<y||f[0]>x&&_[0]>x)continue;m=Math.min(f[0],_[0]),d=Math.max(f[0],_[0])}for(;m<d;)p=Math.floor((m-b)/c),Be(p,t,e,o),m+=c;g=Math.floor((d-b)/c),g>p&&Be(g,t,e,o)}this._accelerationMap=o}_nextInside(){for(;;){if(this._currentX>this._xMax){if(this._currentY++,this._currentY>this._yMax)return null;this._currentX=this._xMin,this._shiftOddRows&&this._currentY%2&&this._currentX--}let t=this._currentX*this._stepX+this._offsetX;this._shiftOddRows&&this._currentY%2&&(t+=.5*this._stepX);const e=this._currentY*this._stepY+this._offsetY;let i,s;if(this._currentX++,this._gridType===it.bj.Random){const r=(this._currentX%We+We)%We,n=(this._currentY%We+We)%We;i=this._graphicOriginX+t+this._stepX*this._randomness*(.5-De._randValues[n*We+r])*2/3,s=this._graphicOriginY+e+this._stepY*this._randomness*(.5-De._randValues[n*We+r+1])*2/3}else i=this._graphicOriginX+this._cosAngle*t+this._sinAngle*e,s=this._graphicOriginY-this._sinAngle*t+this._cosAngle*e;if(!this._testInsidePolygon||this._isInsidePolygon(i,s,this._geometry))return this._internalPlacement.setTranslate(i,s),this._internalPlacement}}_isInsidePolygon(t,e,i){const{rings:s}=i;if((0,l.Wi)(this._accelerationMap))return function(t,e,i){const{rings:s}=i;let r,n,o,a=0;for(const i of s){r=i.length;for(let s=1;s<r;++s)n=i[s-1],o=i[s],n[1]>e!=o[1]>e&&((o[0]-n[0])*(e-n[1])-(o[1]-n[1])*(t-n[0])>0?a++:a--)}return 0!==a}(t,e,i);const r=this._verticalSubdivision,n=r?e:t,o=Math.floor((n-this._polygonMin)/this._delta),a=this._accelerationMap.get(o);if(!a)return!1;let h,c,u,f,_,m=0;for(const i of a){_=i[0];const n=s[_];if(f=i[1],h=n[f-1],c=n[f],r){if(h[1]>e==c[1]>e)continue;u=(c[0]-h[0])*(e-h[1])-(c[1]-h[1])*(t-h[0])}else{if(h[0]>t==c[0]>t)continue;u=(c[1]-h[1])*(t-h[0])-(c[0]-h[0])*(e-h[1])}u>0?m++:m--}return 0!==m}}function Be(t,e,i,s){let r=s.get(t);r||(r=[],s.set(t,r)),r.push([e,i])}class Ue{static local(){return null===Ue.instance&&(Ue.instance=new Ue),Ue.instance}execute(t,e,i,s,r){return new Ge(t,e,i)}}Ue.instance=null;class Ge extends Ot{constructor(t,e,i){super(t,!0,!0),this._curveHelper=new Xt,this._angleToLine=void 0===e.angleToLine||e.angleToLine,this._offset=void 0!==e.offset?e.offset*i:0,this._relativeTo=e.relativeTo,this._position=void 0!==e.startPointOffset?e.startPointOffset*i:0,this._epsilon=.001*i}processPath(t){const e=this._position;if(this._relativeTo===it.CS.SegmentMidpoint){for(this.iteratePath||(this._segmentCount=t.length,this._curSegment=1,this.iteratePath=!0);this._curSegment<this._segmentCount;){const e=this._curSegment;this._curSegment++;const i=t[e-1],s=t[e],r=this._curveHelper.calculateLength(i,s);if(r<this._epsilon)continue;const n=.5+this._position/r,[o,a]=this._curveHelper.getAngleCS(i,s,n),h=Ft(i,s,n);return this.internalPlacement.setTranslate(h[0]-this._offset*a,h[1]+this._offset*o),this._angleToLine&&this.internalPlacement.setRotateCS(o,a),this.internalPlacement}return this.iteratePath=!1,null}this._relativeTo===it.CS.LineEnd&&Et(t);const i=this.onLine(t,e);return this._relativeTo===it.CS.LineEnd&&Et(t),i}onLine(t,e){let i,s=!1;switch(this._relativeTo){case it.CS.LineMiddle:default:i=this._curveHelper.calculatePathLength(t)/2+e;break;case it.CS.LineBeginning:i=e;break;case it.CS.LineEnd:i=e,s=!0}const r=t.length;let n,o=0,a=t[0];for(let e=1;e<r;++e){n=a,a=t[e];const r=this._curveHelper.calculateLength(n,a);if(o+r>i){const t=(i-o)/r,[e,h]=this._curveHelper.getAngleCS(n,a,t),l=Ft(n,a,t),c=s?-this._offset:this._offset;return this.internalPlacement.setTranslate(l[0]-c*h,l[1]+c*e),this._angleToLine&&(s?this.internalPlacement.setRotateCS(-e,-h):this.internalPlacement.setRotateCS(e,h)),this.internalPlacement}o+=r}return null}}class Xe{static local(){return null===Xe.instance&&(Xe.instance=new Xe),Xe.instance}execute(t,e,i,s,r){return new He(t,e,i)}}Xe.instance=null;class He extends Ot{constructor(t,e,i){super(t,!0,!0),this._curveHelper=new Xt,this._angleToLine=void 0===e.angleToLine||e.angleToLine,this._offset=void 0!==e.offset?e.offset*i:0,this._endPoints=void 0===e.placeOnEndPoints||e.placeOnEndPoints,this._controlPoints=void 0===e.placeOnControlPoints||e.placeOnControlPoints,this._regularVertices=void 0===e.placeOnRegularVertices||e.placeOnRegularVertices,this._tags=[],this._tagIterator=0}processPath(t){if(this.iteratePath||(this._preparePath(t),this.iteratePath=!0),this._tagIterator>=this._tags.length)return this._tags.length=0,this._tagIterator=0,this.iteratePath=!1,null;const e=this._tags[this._tagIterator];this._angleToLine&&this.internalPlacement.setRotate(e[2]);let i=e[0],s=e[1];if(0!==this._offset){const t=Math.cos(e[2]),r=Math.sin(e[2]);i-=this._offset*r,s+=this._offset*t}return this.internalPlacement.setTranslate(i,s),this._tagIterator++,this.internalPlacement}_preparePath(t){this._tags.length=0,this._tagIterator=0;const e=function(t){return!(!t||0===t.length)&&t[0][0]===t[t.length-1][0]&&t[0][1]===t[t.length-1][1]}(t),i=t.length-1;let s,r,n=0,o=0,a=0,h=0,l=0;for(;n<i;){n++,s=t[n-1],r=t[n];const c=Rt(s),u=Rt(r);(this._angleToLine||0!==this._offset)&&(h=this._curveHelper.getAngle(s,r,0)),1===n?e?(o=h,a=c):(this._endPoints||this._controlPoints&&1===c)&&this._tags.push([s[0],s[1],h]):1===c?this._controlPoints&&this._tags.push([s[0],s[1],Ye(l,h)]):this._regularVertices&&this._tags.push([s[0],s[1],Ye(l,h)]),(this._angleToLine||0!==this._offset)&&(l=this._curveHelper.getAngle(s,r,1)),n===i&&(e?1===u||1===a?this._controlPoints&&this._tags.push([r[0],r[1],Ye(l,o)]):this._regularVertices&&this._tags.push([r[0],r[1],Ye(l,o)]):(this._endPoints||this._controlPoints&&1===u)&&this._tags.push([r[0],r[1],l]))}this._tagIterator=0}}function Ye(t,e){const i=Math.PI;for(;Math.abs(e-t)>i+2e-15;)e-t>i?e-=2*i:e+=2*i;return(t+e)/2}var Je=i(35308);class Ze{constructor(t=qe){this._data=[],this._compare=t}get size(){return this._data.length}enqueue(t){if(null==t)return;const{_data:e,_compare:i}=this;e.push(t);let s=e.length-1>>>0;const r=e[s];for(;s>0;){const t=s-1>>1,n=e[t];if(!(i(n,r)<=0))break;e[t]=r,e[s]=n,s=t}}dequeue(){const{_data:t,_compare:e}=this,i=t[0],s=t.pop();if(0===t.length)return i;t[0]=s;let r=0;const n=t.length,o=t[0];let a,h,l=null;for(;;){const i=2*r+1,s=2*r+2;if(l=null,i<n&&(a=t[i],e(a,o)>0&&(l=i)),s<n&&(h=t[s],(null===l&&e(h,o)<=0||null!==l&&e(h,a)<=0)&&(l=s)),null===l)break;t[r]=t[l],t[l]=o,r=l}return i}}const qe=(t,e)=>t<e?-1:t>e?1:0;var Ke=i(86662),$e=i(87416);function je(t,e){const{rings:i}=e;let s=0;for(const e of i){const i=e.length;for(let r=1;r<i;++r){const i=e[r-1],n=e[r];i[1]>t[1]!=n[1]>t[1]&&((n[0]-i[0])*(t[1]-i[1])-(n[1]-i[1])*(t[0]-i[0])>0?s++:s--)}}return 0!==s}function Qe(t,e,i){if(i&&je(t,e))return{coord:t,distance:0};let s=1/0,r=0,n=0;const o=[0,0],{rings:a}=e;for(const e of a)if(!(e.length<2))for(let i=0;i<e.length-1;i++){(0,Ke.Tx)(o,t,e,i);const a=ni(t,o);a<s&&(s=a,r=o[0],n=o[1])}return{coord:[r,n],distance:Math.sqrt(s)}}function ti(t,e,i,s){const r=[e,0];let n=1/0,o=1/0,a=!1,h=!1;const l=[[e,s[1]-1],[e,s[3]+1]],c=[0,0],u=[0,0],f=[0,0],_=[[0,0],[0,0]],m=(0,z.Ue)(),{rings:d}=t;for(const t of d)if(!(t.length<2))for(let e=1;e<t.length;e++){if(_[0][0]=t[e-1][0],_[0][1]=t[e-1][1],_[1][0]=t[e][0],_[1][1]=t[e][1],null===ei(m,_))continue;if(u[0]=l[0][0],u[1]=l[0][1],f[0]=l[1][0],f[1]=l[1][1],0===ii(m,u,f))continue;if(!(0,$e.UT)(l[0],l[1],_[0],_[1],c))continue;const i=c[1];n>o?i<n&&(n=i,a=!0):i<o&&(o=i,h=!0)}return a&&h?r[1]=(n+o)/2:r[0]=r[1]=NaN,r}function ei(t,e){if(e.length<2)return null;t||(t=(0,z.Ue)());const[i,s]=e[0],[r,n]=e[1];return t[0]=Math.min(i,r),t[1]=Math.min(s,n),t[2]=Math.max(i,r),t[3]=Math.max(s,n),t}function ii(t,e,i){let s=si(e,t),r=si(i,t);const n=t[0],o=t[1],a=t[2],h=t[3];if(s&r)return 0;if(!(s|r))return 4;const l=(s?1:0)|(r?2:0);do{const l=i[0]-e[0],c=i[1]-e[1];if(l>c)3&s?(1&s?(e[1]+=c*(n-e[0])/l,e[0]=n):(e[1]+=c*(a-e[0])/l,e[0]=a),s=si(e,t)):3&r?(1&r?(i[1]+=c*(n-i[0])/l,i[0]=n):(i[1]+=c*(a-i[0])/l,i[0]=a),r=si(i,t)):s?(4&s?(e[0]+=l*(o-e[1])/c,e[1]=o):(e[0]+=l*(h-e[1])/c,e[1]=h),s=si(e,t)):(4&r?(i[0]+=l*(o-i[1])/c,i[1]=o):(i[0]+=l*(h-i[1])/c,i[1]=h),r=si(i,t));else if(12&s?(4&s?(e[0]+=l*(o-e[1])/c,e[1]=o):(e[0]+=l*(h-e[1])/c,e[1]=h),s=si(e,t)):12&r?(4&r?(i[0]+=l*(o-i[1])/c,i[1]=o):(i[0]+=l*(h-i[1])/c,i[1]=h),r=si(i,t)):s?(1&s?(e[1]+=c*(n-e[0])/l,e[0]=n):(e[1]+=c*(a-e[0])/l,e[0]=a),s=si(e,t)):(1&r?(i[1]+=c*(n-i[0])/l,i[0]=n):(i[1]+=c*(a-i[0])/l,i[0]=a),r=si(i,t)),s&r)return 0}while(s|r);return l}function si(t,e){return(t[0]<e[0]?1:0)|(t[0]>e[2]?1:0)<<1|(t[1]<e[1]?1:0)<<2|(t[1]>e[3]?1:0)<<3}function ri(t,e,i){return t+(e-t)*i}function ni(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function oi(t,e){if(t<e)return-1;if(t>e)return 1;if(t===e)return 0;const i=isNaN(t),s=isNaN(e);return i<s?-1:i>s?1:0}class ai{constructor(t,e,i,s){this.x=t,this.y=e,this.cellSize=i,this.distancefromCellCenter=(0,Ke.ko)(t,e,s),this.maxDistanceToPolygon=this.distancefromCellCenter+this.cellSize*Math.SQRT2}}class hi{static local(){return null===hi.instance&&(hi.instance=new hi),hi.instance}execute(t,e,i,s,r){return new li(t,e,i)}}hi.instance=null;class li{constructor(t,e,i){this._geometry=t,this._offsetX=void 0!==e.offsetX?e.offsetX*i:0,this._offsetY=void 0!==e.offsetY?e.offsetY*i:0,this._method=void 0!==e.method?e.method:it.Lh.OnPolygon,this._internalPlacement=new St}next(){const t=this._geometry;return this._geometry=null,t&&function(t){return void 0!==t.rings}(t)?this._polygonCenter(t):null}_polygonCenter(t){let e=!1;switch(this._method){case it.Lh.CenterOfMass:{const i=(0,Je.NA)(t);i&&(this._internalPlacement.setTranslate(i[0]+this._offsetX,i[1]+this._offsetY),e=!0)}break;case it.Lh.BoundingBoxCenter:{const i=(0,z.Ue)();(0,Se.$P)(i,t),i&&(this._internalPlacement.setTranslate((i[2]+i[0])/2+this._offsetX,(i[3]+i[1])/2+this._offsetY),e=!0)}break;case it.Lh.OnPolygon:default:{let i;i=(0,a.Z)("polylabel-placement-enabled")?function(t){if(!t||!t.rings||0===t.rings.length)return null;const e=(0,Se.lC)((0,z.Ue)(),t.rings[0]);if(!e)return null;const i=e[2]-e[0],s=e[3]-e[1];if(0===i||0===s)return[e[0]+i/2,e[1]+s/2];const r=Math.max(Math.min(i,s)/100,1),n=new Ze(((t,e)=>e.maxDistanceToPolygon-t.maxDistanceToPolygon)),o=Math.min(i,s);let a=o/2,h=0,c=0;for(h=e[0];h<e[2];h+=o)for(c=e[1];c<e[3];c+=o)n.enqueue(new ai(h+a,c+a,a,t));const u=(0,Je.a)(t.rings,!1);if(null===u)return null;let f,_=new ai(u[0],u[1],0,t);for(;n.size>0;)f=(0,l.Wg)(n.dequeue()),f.distancefromCellCenter>_.distancefromCellCenter&&(_=f),f.maxDistanceToPolygon-_.distancefromCellCenter<=r||(a=f.cellSize/2,n.enqueue(new ai(f.x-a,f.y-a,a,t)),n.enqueue(new ai(f.x+a,f.y-a,a,t)),n.enqueue(new ai(f.x-a,f.y+a,a,t)),n.enqueue(new ai(f.x+a,f.y+a,a,t)));return[_.x,_.y]}(t):function(t){const{rings:e}=t;if(!e||0===e.length)return null;const i=(0,Se.$P)((0,z.Ue)(),t);if(!i)return null;const s=4*(Math.abs(i[0])+Math.abs(i[2])+Math.abs(i[1])+Math.abs(i[3])+1)*222045e-19;let r=0,n=0;for(let t=0;t<e.length;t++){const i=(0,Ke.Sm)(e[t]);i>n&&(n=i,r=t)}if(Math.abs(n)<=2*s*s){const t=(0,Se.lC)((0,z.Ue)(),e[r]);return[(t[0]+t[2])/2,(t[1]+t[3])/2]}const o=(0,Je.DS)(e[r],!1,(0,z.Ue)());if(null===o)return null;if(1===e.length&&e[0].length<4)return o;const a=[[NaN,NaN],[NaN,NaN],[NaN,NaN],[NaN,NaN]],h=[NaN,NaN,NaN,NaN],l=[NaN,NaN,NaN,NaN];let c=!1,u=Qe(o,t,!0);0===u.distance&&(c=!0,a[0][0]=o[0],a[0][1]=o[1],u=Qe(o,t,!1)),h[0]=u.distance,l[0]=0;const f=[NaN,NaN];let _=!1,m=.25,d=-1;const p=(0,Se.lC)((0,z.Ue)(),e[r]);let g=NaN;do{if(g=NaN,a[1]=ti(t,ri(p[0],p[2],m),0,i),isNaN(a[1][0])||isNaN(a[1][1])||(u=Qe(a[1],t,!1),g=u.distance),!isNaN(g)&&g>s&&je(a[1],t))_=!0,h[1]=g,l[1]=ni(a[1],o);else if(!isNaN(g)&&g>d&&(d=g,f[0]=a[1][0],f[1]=a[1][1]),m-=.01,m<.1){if(!(d>=0))break;_=!0,h[1]=d,a[1][0]=f[0],a[1][1]=f[1],l[1]=ni(a[1],o)}}while(!_);_=!1,m=.5,d=-1;let y=.01,x=1;do{if(g=NaN,a[2]=ti(t,ri(p[0],p[2],m),0,i),isNaN(a[2][0])||isNaN(a[2][1])||(u=Qe(a[2],t,!1),g=u.distance),!isNaN(g)&&g>s&&je(a[2],t))_=!0,h[2]=g,l[2]=ni(a[2],o);else if(!isNaN(g)&&g>d)d=g,f[0]=a[2][0],f[1]=a[2][1];else if(g>d&&(d=g,f[0]=a[2][0],f[1]=a[2][1]),m=.5+y*x,y+=.01,x*=-1,m<.3||m>.7){if(!(d>=0))break;_=!0,h[2]=d,a[2][0]=f[0],a[2][1]=f[1],l[2]=ni(a[2],o)}}while(!_);_=!1,m=.75,d=-1;do{if(g=NaN,a[3]=ti(t,ri(p[0],p[2],m),0,i),isNaN(a[3][0])||isNaN(a[3][1])||(u=Qe(a[3],t,!1),g=u.distance),!isNaN(g)&&g>s&&je(a[3],t))_=!0,h[3]=g,l[3]=ni(a[3],o);else if(g>d&&(d=g,f[0]=a[3][0],f[1]=a[3][1]),m+=.01,m>.9){if(!(d>=0))break;_=!0,h[3]=d,a[3][0]=f[0],a[3][1]=f[1],l[3]=ni(a[3],o)}}while(!_);const b=[0,1,2,3],M=c?0:1;let v;for(let t=M;t<4;t++)for(let t=M;t<3;t++){const e=l[t],i=l[t+1];oi(e,i)>0&&(v=b[t],b[t]=b[t+1],b[t+1]=v,l[t]=i,l[t+1]=e)}let S=M,w=0,P=0;for(let t=M;t<4;t++){switch(t){case 0:P=2*h[b[t]];break;case 1:P=1.66666666*h[b[t]];break;case 2:P=1.33333333*h[b[t]];break;case 3:P=h[b[t]]}P>w&&(w=P,S=b[t])}return a[S]}(t),null!==i&&(this._internalPlacement.setTranslate(i[0]+this._offsetX,i[1]+this._offsetY),e=!0)}}return e?this._internalPlacement:null}}function ci(t){if(!t)return null;switch(t.type){case"CIMGeometricEffectAddControlPoints":return Dt.local();case"CIMGeometricEffectArrow":return Yt.local();case"CIMGeometricEffectBuffer":return Qt.local();case"CIMGeometricEffectControlMeasureLine":return ee.local();case"CIMGeometricEffectCut":return se.local();case"CIMGeometricEffectDashes":return ue.local();case"CIMGeometricEffectDonut":return _e.local();case"CIMGeometricEffectJog":return de.local();case"CIMGeometricEffectMove":return ge.local();case"CIMGeometricEffectOffset":return xe.local();case"CIMGeometricEffectReverse":return Me.local();case"CIMGeometricEffectRotate":return we.local();case"CIMGeometricEffectScale":return Ce.local();case"CIMGeometricEffectWave":return Ie.local()}return null}function ui(t){if(!t)return null;switch(t.type){case"CIMMarkerPlacementAlongLineSameSize":return Te.local();case"CIMMarkerPlacementAtExtremities":return Ae.local();case"CIMMarkerPlacementAtRatioPositions":return Fe.local();case"CIMMarkerPlacementInsidePolygon":return Ve.local();case"CIMMarkerPlacementOnLine":return Ue.local();case"CIMMarkerPlacementOnVertices":return Xe.local();case"CIMMarkerPlacementPolygonCenter":return hi.local()}return null}class fi{static getPlacement(t,e,i,s,r){const n=ui(e);if(!n)return null;const o=wt(t);return n.execute(o,e,i,s,r)}}var _i=i(1265),mi=i(87893);class di{constructor(t,e,i,s){this.center=(0,_i.f)(t,e),this.centerT=(0,_i.c)(),this.halfWidth=i/2,this.halfHeight=s/2,this.width=i,this.height=s}get x(){return this.center[0]}get y(){return this.center[1]}get blX(){return this.center[0]+this.halfWidth}get blY(){return this.center[1]+this.halfHeight}get trX(){return this.center[0]-this.halfWidth}get trY(){return this.center[1]-this.halfHeight}get xmin(){return this.x-this.halfWidth}get xmax(){return this.x+this.halfWidth}get ymin(){return this.y-this.halfHeight}get ymax(){return this.y+this.halfHeight}set x(t){this.center[0]=t}set y(t){this.center[1]=t}clone(){return new di(this.x,this.y,this.width,this.height)}serialize(t){return t.writeF32(this.center[0]),t.writeF32(this.center[1]),t.push(this.width),t.push(this.height),t}findCollisionDelta(t,e=4){const i=Math.abs(t.centerT[0]-this.centerT[0]),s=Math.abs(t.centerT[1]-this.centerT[1]),r=(t.halfWidth+this.halfWidth+e)/i,n=(t.halfHeight+this.halfHeight+e)/s,o=Math.min(r,n);return Math.log2(o)}extend(t){const e=Math.min(this.xmin,t.xmin),i=Math.min(this.ymin,t.ymin),s=Math.max(this.xmax,t.xmax)-e,r=Math.max(this.ymax,t.ymax)-i,n=e+s/2,o=i+r/2;this.width=s,this.height=r,this.halfWidth=s/2,this.halfHeight=r/2,this.x=n,this.y=o}static deserialize(t){const e=t.readF32(),i=t.readF32(),s=t.readInt32(),r=t.readInt32();return new di(e,i,s,r)}}const pi=Math.PI/180;class gi{constructor(t,e,i,s){this._rotationT=Q(),this._xBounds=0,this._yBounds=0,this.minZoom=0,this.maxZoom=255,this._bounds=null;const r=i.rect,n=new Float32Array(8);t*=s,e*=s;const o=i.code?r.width*s:i.metrics.width,a=i.code?r.height*s:i.metrics.height;this.width=o,this.height=a,n[0]=t,n[1]=e,n[2]=t+o,n[3]=e,n[4]=t,n[5]=e+a,n[6]=t+o,n[7]=e+a,this._data=n,this._setTextureCoords(r),this._scale=s,this._mosaic=i,this.x=t,this.y=e,this.maxOffset=Math.max(t+o,e+a)}get mosaic(){return this._mosaic}set angle(t){this._angle=t,Z(this._rotationT,-t),this._setOffsets(this._data)}get angle(){return this._angle}get xTopLeft(){return this._data[0]}get yTopLeft(){return this._data[1]}get xBottomRight(){return this._data[6]}get yBottomRight(){return this._data[7]}get texcoords(){return this._texcoords}get textureBinding(){return this._mosaic.textureBinding}get offsets(){return this._offsets||this._setOffsets(this._data),this._offsets}get char(){return String.fromCharCode(this._mosaic.code)}get code(){return this._mosaic.code}get bounds(){if(!this._bounds){const{height:t,width:e}=this._mosaic.metrics,i=e*this._scale,s=Math.abs(t)*this._scale,r=new Float32Array(8);r[0]=this.x,r[1]=this.y,r[2]=this.x+i,r[3]=this.y,r[4]=this.x,r[5]=this.y+s,r[6]=this.x+i,r[7]=this.y+s,et(r,r,H(Q(),this._rotationT,this._transform));let n=1/0,o=1/0,a=0,h=0;for(let t=0;t<4;t++){const e=r[2*t],i=r[2*t+1];n=Math.min(n,e),o=Math.min(o,i),a=Math.max(a,e),h=Math.max(h,i)}const l=a-n,c=h-o,u=n+l/2,f=o+c/2;this._bounds=new di(u,f,l,c)}return this._bounds}setTransform(t){this._transform=t,this._offsets=null}_setOffsets(t){this._offsets||(this._offsets={upperLeft:0,upperRight:0,lowerLeft:0,lowerRight:0});const e=this._offsets,i=new Float32Array(8);et(i,t,H(Q(),this._rotationT,this._transform)),e.upperLeft=(0,nt.UJ)(8*i[0],8*i[1]),e.upperRight=(0,nt.UJ)(8*i[2],8*i[3]),e.lowerLeft=(0,nt.UJ)(8*i[4],8*i[5]),e.lowerRight=(0,nt.UJ)(8*i[6],8*i[7])}_setTextureCoords({x:t,y:e,width:i,height:s}){this._texcoords={upperLeft:(0,nt.UJ)(t,e),upperRight:(0,nt.UJ)(t+i,e),lowerLeft:(0,nt.UJ)(t,e+s),lowerRight:(0,nt.UJ)(t+i,e+s)}}}const yi=(t,e)=>({code:0,page:0,sdf:!0,rect:new mi.Z(0,0,11,8),textureBinding:e,metrics:{advance:0,height:4,width:t,left:0,top:0}});function xi(t,e){return t.forEach((t=>(0,y.t)(t,t,e))),{upperLeft:(0,nt.UJ)(8*t[0][0],8*t[0][1]),upperRight:(0,nt.UJ)(8*t[1][0],8*t[1][1]),lowerLeft:(0,nt.UJ)(8*t[2][0],8*t[2][1]),lowerRight:(0,nt.UJ)(8*t[3][0],8*t[3][1])}}class bi{constructor(t,e,i){this._rotation=0,this._decorate(t,e,i),this.glyphs=t,this.bounds=this._createBounds(t),this.isMultiline=e.length>1,this._hasRotation=0!==i.angle,this._transform=this._createGlyphTransform(this.bounds,i),this._borderLineSize=i.borderLineSize,(i.borderLineSize||i.hasBackground)&&([this.bounds,this.background]=this.shapeBackground(this._transform));for(const e of t)e.setTransform(this._transform)}setRotation(t){if(0===t&&0===this._rotation)return;this._rotation=t;const e=this._transform;H(e,Z(Q(),t),e);for(const t of this.glyphs)t.setTransform(this._transform)}_decorate(t,e,i){if(!i.decoration||"none"===i.decoration||!t.length)return;const s=i.scale,r="underline"===i.decoration?30:20,n=t[0].textureBinding;for(const i of e){const e=i.startX*s,o=i.startY*s,a=(i.width+i.glyphWidthEnd)*s;t.push(new gi(e,o+r*s,yi(a,n),1))}}shapeBackground(t){const e=(1.5+(0,G.F2)(this._borderLineSize||0))/2,i=this._borderLineSize?e:0,{xmin:s,ymin:r,xmax:n,ymax:o,x:a,y:h,width:l,height:c}=this.bounds,u=[s-8,r-8],f=[n+8,r-8],_=[s-8,o+8],m=[n+8,o+8],d=xi([[u[0]-e,u[1]-e],[f[0]+e,f[1]-e],[u[0]+i,u[1]+i],[f[0]-i,f[1]+i]],t),p=xi([[_[0]+i,_[1]-i],[m[0]-i,m[1]-i],[_[0]-e,_[1]+e],[m[0]+e,m[1]+e]],t),g=xi([[u[0]-e,u[1]-e],[u[0]+i,u[1]+i],[_[0]-e,_[1]+e],[_[0]+i,_[1]-i]],t),y=xi([[f[0]-i,f[1]+i],[f[0]+e,f[1]-e],[m[0]-i,m[1]-i],[m[0]+e,m[1]+e]],t),x={main:xi([u,f,_,m],t),top:d,bot:p,left:g,right:y};return[new di(a,h,l+2*e,c+2*e),x]}get boundsT(){const t=this.bounds,e=(0,y.s)((0,_i.c)(),t.x,t.y);if((0,y.t)(e,e,this._transform),this._hasRotation){const i=Math.max(t.width,t.height);return new di(e[0],e[1],i,i)}return new di(e[0],e[1],t.width,t.height)}_createBounds(t){let e=1/0,i=1/0,s=0,r=0;for(const n of t)e=Math.min(e,n.xTopLeft),i=Math.min(i,n.yTopLeft),s=Math.max(s,n.xBottomRight),r=Math.max(r,n.yBottomRight);const n=s-e,o=r-i;return new di(e+n/2,i+o/2,n,o)}_createGlyphTransform(t,e){const i=pi*e.angle,s=Q(),r=(0,_i.c)();return J(s,s,(0,y.s)(r,e.xOffset,-e.yOffset)),e.isCIM?Y(s,s,i):(J(s,s,(0,y.s)(r,t.x,t.y)),Y(s,s,i),J(s,s,(0,y.s)(r,-t.x,-t.y))),s}}class Mi{constructor(t,e,i,s,r,n){this.glyphWidthEnd=0,this.startX=0,this.startY=0,this.start=Math.max(0,Math.min(e,i)),this.end=Math.max(0,Math.max(e,i)),this.end<t.length&&(this.glyphWidthEnd=t[this.end].metrics.width),this.width=s,this.yMin=r,this.yMax=n}}const vi=t=>10===t,Si=t=>32===t;const wi=(0,nt.UJ)(4,4),Pi=(0,nt.UJ)(4,2),Ci=(0,nt.UJ)(4,6),Li=[Pi,Pi,Ci,Ci],Ii=[Pi,Ci,Pi,Ci],ki=[Ci,Ci,wi,wi],Ti=[wi,wi,Ci,Ci],zi=[Ci,wi,Ci,wi],Ai=[wi,Ci,wi,Ci],Ei=t=>class extends t{constructor(...t){super(...t),this._isCIM=!1,this._vertexBoundsScale=1,this.geometryType=d.LW.TEXT,this._aux=(0,nt.Jz)(0,0,this._referenceSize,this._bitset)}bindTextInfo(t,e){t&&t.length?this._shapingInfo=(0,l.yw)(t,(t=>function(t,e,i){const s=i.scale,r=new Array,n=function(t,e,i){const s=new Array,r=1/i.scale,n=i.maxLineWidth*r,o=e?t.length-1:0,a=e?-1:t.length,h=e?-1:1;let l=o,c=0,u=0,f=l,_=f,m=0,d=1/0,p=0;for(;l!==a;){const{code:e,metrics:i}=t[l],r=Math.abs(i.top);if(vi(e)||Si(e)||(d=Math.min(d,r),p=Math.max(p,r+i.height)),vi(e))l!==o&&(s.push(new Mi(t,f,l-h,c,d,p)),d=1/0,p=0),c=0,f=l+h,_=l+h,u=0;else if(Si(e))_=l+h,u=0,m=i.advance,c+=i.advance;else if(c>n){if(_!==f){const e=_-2*h;c-=m,s.push(new Mi(t,f,e,c-u,d,p)),d=1/0,p=0,f=_,c=u}else s.push(new Mi(t,f,l-h,c,d,p)),d=1/0,p=0,f=l,_=l,c=0;c+=i.advance,u+=i.advance}else c+=i.advance,u+=i.advance;l+=h}const g=new Mi(t,f,l-h,c,d,p);return g.start>=0&&g.end<t.length&&s.push(g),s}(t,e,i),o=function(t,e){let i=0;for(let e=0;e<t.length;e++){const{width:s}=t[e];i=Math.max(s,i)}const s="underline"===e.decoration?4:0,r=t[0].yMin;return{x:0,y:r,height:t[t.length-1].yMax+e.lineHeight*(t.length-1)+s-r,width:i}}(n,i),{vAlign:a,hAlign:h}=i,l=a===st.TR.Baseline?1:0,c=l?0:a-1,u=(1-l)*-o.y+c*(o.height/2)+-26*(l?1:0);for(let e=0;e<n.length;e++){const{start:o,end:a,width:l}=n[e];let c=-1*(h+1)*(l/2)-3;const f=e*i.lineHeight+u-3;n[e].startX=c,n[e].startY=f;for(let e=o;e<=a;e++){const i=t[e];if(vi(i.code))continue;const n=new gi(c+i.metrics.left,f-i.metrics.top,i,s);c+=i.metrics.advance,r.push(n)}}return new bi(r,n,i)}(t,e,{scale:this._scale,angle:this._angle,xOffset:this._xOffset,yOffset:this._yOffset,hAlign:this._xAlignD,vAlign:this._yAlignD,maxLineWidth:Math.max(32,Math.min(this._lineWidth,512)),lineHeight:A.xm*Math.max(.25,Math.min(this._lineHeight,4)),decoration:this._decoration,isCIM:this._isCIM,hasBackground:!!this._backgroundColor,borderLineSize:this._borderLineSize}))):this._shapingInfo=null}_write(t,e,i,s){const r=e.getDisplayId();this._writeGeometry(t,e,r,i,s)}_writeGeometry(t,e,i,s,r){const n=this._shapingInfo;if((0,l.Wi)(n))return;if((0,l.pC)(this._textPlacement)){const o=r??e.readLegacyGeometryForDisplay();return this._writePlacedText(t,i,o,n,s)}const o=r?(0,bt.oB)((0,bt.GH)(r),2):"esriGeometryPolygon"===e.geometryType?e.readCentroid():e.readGeometryForDisplay();if(!(0,l.Wi)(o)){if(o.isPoint){const[e,s]=o.coords;if(!t.hasAggregates&&t.hasPixelBufferEnabled&&(e<0||e>=512||s<0||s>=512))return;return this._writeGlyphs(t,i,{x:e,y:s},n)}o.forEachVertex(((e,s)=>this._writeGlyphs(t,i,{x:e,y:s},n)))}}_writePlacedText(t,e,i,s,r){const n=(0,l.Wg)(this._textPlacement),o=fi.getPlacement(i,n,(0,G.F2)(1),t.tileKey,r.geometryEngine);if(!o)return;let a=o.next();for(;null!=a;){const i=-a.getAngle();s.setRotation(i);const r=a.tx,n=-a.ty;r<0||r>=512||n<0||n>=512||(this._writeGlyphs(t,e,{x:r,y:n},s),s.setRotation(-i)),a=o.next()}}_writeGlyphs(t,e,i,s){const r=ot.m2.load(this._materialKey),n=(0,nt.UJ)(Math.round(8*i.x),Math.round(8*i.y)),o=this._vertexBoundsScale,{bounds:a,background:h,glyphs:l}=s;l.length>0&&(this._borderLineColor||this._backgroundColor)&&(r.textureBinding=l[0].textureBinding,t.recordStart(e,r.data,this.geometryType,!0),this._writeBackgroundGeometry(t,e,i,a,h),t.recordEnd());const c=2*Math.max(a.width,a.height);for(const h of s.glyphs)r.textureBinding=h.textureBinding,t.recordStart(e,r.data,this.geometryType,!0),t.vertexBounds(i.x+a.x+this._xOffset,i.y+a.y-this._yOffset,c*o,c*o),this._writeVertices(t,e,n,h),t.recordEnd()}_writeGlyph(t,e,i,s,r){const n=ot.m2.load(this._materialKey),o=(0,nt.UJ)(Math.round(8*i),Math.round(8*s));n.textureBinding=r.textureBinding,t.recordStart(e,n.data,this.geometryType,!0);const a=r.bounds,h=this._vertexBoundsScale;t.vertexBounds(i+a.x*h,s+a.y*h,a.width*h,a.height*h),this._writeVertices(t,e,o,r),t.recordEnd()}_writeVertices(t,e,i,s){const r=t.vertexCount();this._writeVertexCommon(t,e,i,s),t.vertexWrite(s.offsets.upperLeft),t.vertexWrite(s.texcoords.upperLeft),t.vertexEnd(),this._writeVertexCommon(t,e,i,s),t.vertexWrite(s.offsets.upperRight),t.vertexWrite(s.texcoords.upperRight),t.vertexEnd(),this._writeVertexCommon(t,e,i,s),t.vertexWrite(s.offsets.lowerLeft),t.vertexWrite(s.texcoords.lowerLeft),t.vertexEnd(),this._writeVertexCommon(t,e,i,s),t.vertexWrite(s.offsets.lowerRight),t.vertexWrite(s.texcoords.lowerRight),t.vertexEnd(),t.indexWrite(r+0),t.indexWrite(r+1),t.indexWrite(r+2),t.indexWrite(r+1),t.indexWrite(r+3),t.indexWrite(r+2)}_writeVertexCommon(t,e,i,s){const r=this._color,n=this._haloColor,o=(0,nt.Jz)(0,0,this._referenceSize,this._bitset),a=(0,nt.Jz)(0,0,this._size,this._haloSize);t.vertexWrite(i),t.vertexWrite(e),t.vertexWrite(r),t.vertexWrite(n),t.vertexWrite(a),t.vertexWrite(o),t.vertexWrite(this._minMaxZoom)}_writeBackgroundVertex(t,e,i,s,r,n){const o=(0,nt.Jz)(0,1,this._referenceSize,this._bitset),a=(0,nt.Jz)(0,0,this._size,this._haloSize),h=(0,nt.Jz)(0,0,0,0);t.vertexWrite(i),t.vertexWrite(e),t.vertexWrite(s),t.vertexWrite(h),t.vertexWrite(a),t.vertexWrite(o),t.vertexWrite(this._minMaxZoom),t.vertexWrite(r),t.vertexWrite(n),t.vertexEnd()}_writeBackgroundQuad(t,e,i,s,r,n){const o=t.vertexCount();this._writeBackgroundVertex(t,e,i,s,r.upperLeft,n[0]),this._writeBackgroundVertex(t,e,i,s,r.upperRight,n[1]),this._writeBackgroundVertex(t,e,i,s,r.lowerLeft,n[2]),this._writeBackgroundVertex(t,e,i,s,r.lowerRight,n[3]),t.indexWrite(o+0),t.indexWrite(o+1),t.indexWrite(o+2),t.indexWrite(o+1),t.indexWrite(o+3),t.indexWrite(o+2)}_writeBackgroundGeometry(t,e,i,s,r){const n=(0,nt.UJ)(Math.round(8*i.x),Math.round(8*i.y)),{x:o,y:a,width:h,height:l}=s,c=2*Math.max(h,l);if(t.vertexBounds(i.x+o+this._xOffset,i.y+a-this._yOffset,c*this._vertexBoundsScale,c*this._vertexBoundsScale),this._backgroundColor){const i=[wi,wi,wi,wi];this._writeBackgroundQuad(t,e,n,this._backgroundColor,r.main,i)}if(this._borderLineColor||this._backgroundColor){const i=!!this._borderLineColor&&!!this._borderLineSize&&this._borderLineSize>0,[s,o,a,h,l]=i?[Li,Li,Ii,Ii,this._borderLineColor]:[ki,Ti,zi,Ai,this._backgroundColor];this._writeBackgroundQuad(t,e,n,l,r.top,s),this._writeBackgroundQuad(t,e,n,l,r.bot,o),this._writeBackgroundQuad(t,e,n,l,r.left,a),this._writeBackgroundQuad(t,e,n,l,r.right,h)}}};class Fi{static executeEffects(t,e,i,s){const r=wt(e);let n=new Kt(r);for(const e of t){const t=ci(e);t&&(n=t.execute(n,e,1.3333333333333333,i,s))}return n}static next(t){const e=t.next();return function(t){t&&(Pt(t),(0,vt.oU)(t)?kt(t.rings):(0,vt.l9)(t)?kt(t.paths):(0,vt.aW)(t)&&It(t.points))}(e),e}static applyEffects(t,e,i){if(!t)return e;let s=new Kt(e);for(const e of t){const t=ci(e);t&&(s=t.execute(s,e,1,null,i))}let r,n=null;for(;r=s.next();)n?(0,vt.l9)(n)?(0,vt.l9)(r)&&n.paths.push(...r.paths):(0,vt.oU)(n)&&(0,vt.oU)(r)&&n.rings.push(...r.rings):n=r;return n}}class Ri{constructor(){this._materialKey=null}bindFeature(t,e,i){}write(t,e,i,s){if((0,l.Wi)(this._effects)||0===this._effects?.length)return this._write(t,e,s);const r=Fi.executeEffects(this._effects,e.readLegacyGeometryForDisplay(),t.tileKey,s.geometryEngine);let n=Fi.next(r);for(;n;)this._write(t,e,s,n),n=Fi.next(r)}_write(t,e,i,s){}}class Ni extends(Ei(Ri)){constructor(t,e,i,s,r,n,o,a,h,l,c,u,f,_,m,d,p,g,y,x,b,M,v,S){super(),this._xOffset=(0,G.F2)(f),this._yOffset=(0,G.F2)(_),this._decoration=l||"none",this._backgroundColor=M,this._borderLineColor=v,this._borderLineSize=S,this._color=r,this._haloColor=n,this._haloSize=Math.min(Math.floor(5*(0,G.F2)((0,G.t_)(i))),127),this._size=Math.min(Math.round((0,G.F2)(e)),127);const w=Math.min(Math.round((0,G.F2)(s||e)),127);this._referenceSize=Math.round(Math.sqrt(256*w)),this._scale=this._size/A.Ex,this._angle=u,this._justify=(0,st.Hd)(o||"center"),this._xAlignD=(0,st.kH)(o||"center"),this._yAlignD=(0,st.b7)(a||"baseline"),this._baseline="baseline"===(a||"baseline"),this._bitset=(h===it.v2.MAP?1:0)|(c?1:0)<<1;const P=ot.m2.load(t);P.sdf=!0,this._materialKey=P.data,this._lineWidth=(0,G.F2)(m)||512,this._lineHeight=d||1,this._textPlacement=p,this._effects=g,this._isCIM=y??!1,this._minMaxZoom=(0,nt.UJ)(Math.round(x*A.MI),Math.round(b*A.MI))}static fromText(t,e){const i=t.font?.size,s=new Ni(t.materialKey,i,t.haloSize||0,i,t.color&&(0,rt.aH)(t.color)||0,t.haloColor&&(0,rt.aH)(t.haloColor)||0,t.horizontalAlignment,t.verticalAlignment,it.v2.SCREEN,t.font?.decoration,!1,t.angle||0,t.xoffset||0,t.yoffset||0,t.lineWidth||0,t.lineHeight||0,null,null,!1,0,at,t.backgroundColor&&(0,rt.aH)(t.backgroundColor),t.borderLineColor&&(0,rt.aH)(t.borderLineColor),t.borderLineSize),[,r]=n(t.text);return s.bindTextInfo(e??[],r),s._vertexBoundsScale=t.maxVVSize&&i?t.maxVVSize/i:1,s}static fromCIMText(t,e,i){const s=t.scaleFactor||1,r=t.size*t.sizeRatio*s,[o,a]=mt(t.scaleInfo,i),h=new Ni(t.materialKey,r,t.outlineSize*t.sizeRatio,t.referenceSize,(0,rt.t2)(t.color),(0,rt.t2)(t.outlineColor),t.horizontalAlignment,t.verticalAlignment,t.alignment,t.decoration,t.colorLocked??!1,t.angle,t.offsetX*t.sizeRatio*s,t.offsetY*t.sizeRatio*s,512,1,t.markerPlacement,t.effects,!0,o,a,t.backgroundColor?(0,rt.t2)(t.backgroundColor):void 0,t.borderLineColor?(0,rt.t2)(t.borderLineColor):void 0,t.borderLineWidth),[,l]=n(t.text);return h.bindTextInfo(e,l),h._vertexBoundsScale=t.maxVVSize?t.maxVVSize/r:1,h}}const Oi=h.Z.getLogger("esri.views.2d.engine.webgl.WGLLabelTemplate"),Wi=function(t){const e=new Map;return t=>(e.has(t)||e.set(t,(t=>{let e=0;if(0===t)return 1/0;for(;!(t%2);)e++,t/=2;return e})(t)),e.get(t))}(),Vi=t=>Math.floor(127*t+127),Di=t=>Math.floor(10*t),Bi=t=>Math.round(t*(254/360));class Ui extends Ni{constructor(t,e,i,s){super(t,i.font?.size,i.haloSize||0,i.font?.size,i.color&&(0,rt.aH)(i.color)||0,i.haloColor&&(0,rt.aH)(i.haloColor)||0,i.horizontalAlignment,i.verticalAlignment,(0,st.NS)(e.labelPlacement)?it.v2.MAP:it.v2.SCREEN,i.font?.decoration,!1,i.angle||0,i.xoffset,i.yoffset,i.lineWidth,i.lineHeight,null,null,!1,null,null,i.backgroundColor&&(0,rt.aH)(i.backgroundColor),i.borderLineColor&&(0,rt.aH)(i.borderLineColor),i.borderLineSize),this._outLineLabelAngle=0,this._refPlacementPadding=0,this._refPlacementDirX=0,this._refPlacementDirY=0,this._refOffsetX=0,this._refOffsetY=0,this._zoomLevel=0,this.geometryType=d.LW.LABEL,this._allowOverrun=e.allowOverrun??!1,this._repeatLabel=e.repeatLabel??!0,this._labelPosition=e.labelPosition??"curved";const r=function(t,e){const i=!!t.minScale&&e.scaleToZoom(t.minScale)||0;return(0,U.uZ)(i,0,25.5)}(e,s),n=function(t,e){const i=!!t.maxScale&&e.scaleToZoom(t.maxScale)||255;return(0,U.uZ)(i,0,25.5)}(e,s),o=e.labelPlacement,[a,h]=(0,st.qv)(o);this._xAlignD=a,this._yAlignD=h,this._minZoom=r,this._maxZoom=n,this._minBackgroundZoom=r,this._maxBackgroundZoom=n,this._refPlacementPadding=(0,G.F2)(i.haloSize)+A.Iw,this._repeatLabelDistance=e.repeatLabelDistance?(0,G.F2)(e.repeatLabelDistance):128;const l=ot.Gq.load(t);l.sdf=!0,this._materialKey=l.data}static fromLabelClass(t,e){if("esriServerLinePlacementCenterAlong"===t.labelPlacement){const e=t.symbol;e.xoffset=0,e.yoffset=0,e.angle=0,e.font.decoration="none"}return new Ui(t.materialKey,t,t.symbol,e)}get _shapedBox(){return(0,l.Wg)(this._shapingInfo).bounds}setZoomLevel(t){this._zoomLevel=t}bindReferenceTemplate(t){let e=(0,st.g)(this._xAlignD),i=(0,st.tf)(this._yAlignD);if(this._refOffsetX=0,this._refOffsetY=0,(0,l.Wi)(t))return void(this._refSymbolAndPlacementOffset=(0,nt.Jz)(0,0,Vi(e),Vi(i)));if("circle"===t.boundsType&&(e||i)){const t=Math.sqrt(e*e+i*i);e/=t,i/=t}const s=Math.max(t.height,t.width),r=4*this._refPlacementPadding;this._refSymbolAndPlacementOffset=(0,nt.Jz)(r,s,Vi(e),Vi(i)),this._referenceSize=s,this._refPlacementDirX=e,this._refPlacementDirY=i,this._refOffsetX=t.xOffset,this._refOffsetY=t.yOffset}_write(t,e){if((0,l.Wi)(this._shapingInfo))return;const i=this._shapingInfo,s=e.getDisplayId(),r="esriGeometryPolygon"===e.geometryType?e.readLegacyCentroid():e.readLegacyGeometry();if(r)switch(this._current={out:t,inId:s,inShaping:i,zoomLevel:this._zoomLevel},"esriGeometryPolyline"===e.geometryType&&"curved"===this._labelPosition&&(this._borderLineColor||this._backgroundColor)&&Oi.warnOnce("TextSymbol properties 'borderLineColor', 'borderLineSize', and 'backgroundColor' are not supported in curved labels"),e.geometryType){case"esriGeometryPolyline":this._placeLineLabels(r);break;case"esriGeometryPoint":case"esriGeometryPolygon":this._placePointLabels(r);break;default:((t,e="mapview-labeling")=>{Oi.error(new o.Z(e,t))})(`Geometry of type ${e.geometryType} is not supported`)}}_isVisible(t,e){const i=Di(this._current.zoomLevel);return Di(t)<=i&&i<=Di(e)}_placePointLabels(t){const{out:e,inId:i,inShaping:s}=this._current;this._writeGlyphs(e,i,t,s)}_placeLineLabels(t){const e=function(t,e){const i=e;for(let e=0;e<t.length;e++){let s=t[e];const r=[];r.push(s[0]);for(let t=1;t<s.length;t++){let[e,i]=r[t-1];e+=s[t][0],i+=s[t][1],r.push([e,i])}xt(r,i);const n=[];n.push(r[0]);for(let t=1;t<r.length;t++){const[e,i]=r[t-1],[s,o]=r[t],a=Math.round(s-e),h=Math.round(o-i);n.push([a,h])}t[e]=n,s=n}return t}(t.paths,this._current.inShaping.bounds.width),i=this._placeSubdivGlyphs.bind(this),s=(this._shapedBox.width+this._repeatLabelDistance)/2;for(const t of e)yt(t,s,i,this._repeatLabel)}_placeSubdivGlyphs(t,e,i,s){const r=Wi(e),n=this._shapedBox.width/2,o=Math.sqrt(this._repeatLabelDistance)/2,a=Math.min(i,s-i),h=this._current.inShaping.isMultiline?25:Math.log2(a/(o+n/2)),l=0===e?h:Math.min(r,h),c=Math.max(this._minZoom,this._current.zoomLevel+1-l),u=this._current.zoomLevel-c,f=this._shapedBox.width/2*2**u;this._current.inShaping.isMultiline?0===e&&this._placeStraight(t,c):this._allowOverrun&&u<0?this._placeStraightAlong(t,this._minZoom):"parallel"===this._labelPosition?this._placeStraightAlong(t,c):"curved"===this._labelPosition&&this._placeCurved(t,c,f)}_placeStraight(t,e){const{out:i,inId:s,inShaping:r}=this._current,n=Math.ceil(t.angle*(180/Math.PI)%360),o=Math.ceil((t.angle*(180/Math.PI)+180)%360);this._outLineLabelAngle=Bi(n),this._writeGlyphs(i,s,t,r,e),this._outLineLabelAngle=Bi(o),this._writeGlyphs(i,s,t,r,e)}_placeCurved(t,e,i){const{out:s,inId:r}=this._current;s.metricStart(r,e,t.x,t.y,0,0,0,0);const n=t.clone(),o=t.angle*(180/Math.PI)%360,a=(t.angle*(180/Math.PI)+180)%360;this._outLineLabelAngle=Bi(o),this._placeFirst(n,e,1),this._placeBack(t,n,e,i,1),this._placeForward(t,n,e,i,1),this._outLineLabelAngle=Bi(a),this._placeFirst(n,e,0),this._placeBack(t,n,e,i,0),this._placeForward(t,n,e,i,0),s.metricEnd()}_placeStraightAlong(t,e){const{out:i,inId:s,inShaping:r}=this._current;i.metricStart(s,e,t.x,t.y,0,0,0,0);const n=t.clone(),o=t.angle*(180/Math.PI)%360,a=(t.angle*(180/Math.PI)+180)%360,h=r.glyphs.length>0&&(this._borderLineColor||this._backgroundColor);if(this._maxBackgroundZoom=25,this._minBackgroundZoom=Math.max(e,0),h){const e=ot.Gq.load(this._materialKey);e.textureBinding=r.glyphs[0].textureBinding;const n=Z(Q(),-t.angle),[h,l]=r.shapeBackground(n);this._outLineLabelAngle=Bi(o),i.recordStart(s,e.data,this.geometryType,!0),this._writeBackgroundGeometry(i,s,t,h,l),i.recordEnd(),this._outLineLabelAngle=Bi(a),i.recordStart(s,e.data,this.geometryType,!0),this._writeBackgroundGeometry(i,s,t,h,l),i.recordEnd()}this._outLineLabelAngle=Bi(o),this._placeFirst(n,e,1,!0),this._outLineLabelAngle=Bi(a),this._placeFirst(n,e,0,!0),i.metricEnd()}_placeBack(t,e,i,s,r){const n=t.clone();let o=t.backwardLength+0;for(;n.prev()&&!(o>=s);)this._placeOnSegment(n,e,o,i,-1,r),o+=n.length+0}_placeForward(t,e,i,s,r){const n=t.clone();let o=t.remainingLength+0;for(;n.next()&&!(o>=s);)this._placeOnSegment(n,e,o,i,1,r),o+=n.length+0}_placeFirst(t,e,i,s=!1){const r=t,n=this._current.inShaping,o=n.glyphs,a=this._current.zoomLevel,{out:h,inId:l}=this._current;for(const c of o){const o=c.x>n.bounds.x?i:1-i,u=o*t.remainingLength+(1-o)*t.backwardLength,f=Math.abs(c.x+c.width/2-n.bounds.x),_=Math.max(0,a+Math.log2(f/(u+0))),m=Math.max(e,s?0:_);if(c.maxZoom=25,c.angle=t.angle+(1-i)*Math.PI,c.minZoom=m,this._writeGlyph(h,l,r.x,r.y,c),i&&this._isVisible(c.minZoom,c.maxZoom)){const t=c.bounds;h.metricBoxWrite(t.center[0],t.center[1],t.width,t.height)}}}_placeOnSegment(t,e,i,s,r,n){const o=this._current.inShaping.glyphs,{out:a,inId:h}=this._current,l=this._current.inShaping,c=this._current.zoomLevel,u=t.dx/t.length,f=t.dy/t.length,_={x:t.x+i*-r*u,y:t.y+i*-r*f};for(const u of o){const o=u.x>l.bounds.x?n:1-n;if(!(o&&1===r||!o&&-1===r))continue;const f=Math.abs(u.x+u.width/2-l.bounds.x),m=Math.max(0,c+Math.log2(f/i)-.1),d=Math.max(s,c+Math.log2(f/(i+t.length+0)));if(0!==m&&(u.angle=t.angle+(1-n)*Math.PI,u.minZoom=d,u.maxZoom=m,this._writeGlyph(a,h,_.x,_.y,u),n&&this._isVisible(u.minZoom,u.maxZoom))){const i=u.bounds,s=t.x-e.x,r=t.y-e.y;a.metricBoxWrite(i.center[0]+s,i.center[1]+r,i.width,i.height)}}}_writeGlyphs(t,e,i,s,r=this._minZoom){if(i.x<0||i.x>=512||i.y<0||i.y>=512)return;if(s.glyphs.length>0&&(this._borderLineColor||this._backgroundColor)){const r=ot.Gq.load(this._materialKey);r.textureBinding=s.glyphs[0].textureBinding,t.recordStart(e,r.data,this.geometryType,!0),this._writeBackgroundGeometry(t,e,i,s.bounds,s.background),t.recordEnd()}const n=i.x+this._refOffsetX,o=i.y-this._refOffsetY;for(const i of s.glyphs)i.minZoom=r,i.maxZoom=this._maxZoom,this._writeGlyph(t,e,n,o,i);const a=this._refPlacementDirX,h=this._refPlacementDirY,l=s.boundsT;t.metricStart(e,r,n,o,a,h,this._referenceSize,this._materialKey),t.metricBoxWrite(l.center[0],l.center[1],l.width,l.height),t.metricEnd()}_writeVertexCommon(t,e,i,s){const r=this._color,n=this._haloColor,o=(0,nt.Jz)(0,0,this._size,this._haloSize),a=Math.max(s.minZoom,this._minZoom),h=Math.min(s.maxZoom,this._maxZoom),l=(0,nt.Jz)(Di(a),Di(h),this._outLineLabelAngle,0);t.vertexWrite(i),t.vertexWrite(e),t.vertexWrite(r),t.vertexWrite(n),t.vertexWrite(o),t.vertexWrite(this._refSymbolAndPlacementOffset),t.vertexWrite(l)}_writeBackgroundVertex(t,e,i,s,r,n){const o=(0,nt.Jz)(0,0,this._size,this._haloSize),a=(0,nt.Jz)(0,0,0,0),h=(0,nt.Jz)(Di(this._minBackgroundZoom),Di(this._maxBackgroundZoom),this._outLineLabelAngle,1);t.vertexWrite(i),t.vertexWrite(e),t.vertexWrite(s),t.vertexWrite(a),t.vertexWrite(o),t.vertexWrite(this._refSymbolAndPlacementOffset),t.vertexWrite(h),t.vertexWrite(r),t.vertexWrite(n),t.vertexEnd()}}const Gi=3.14159265359/180,Xi=t=>class extends t{constructor(...t){super(...t),this.angle=0,this.xOffset=0,this.yOffset=0,this.width=0,this.height=0,this.boundsType="square",this._anchorX=0,this._anchorY=0,this._computedWidth=0,this._computedHeight=0,this._allowBorrowing=!0,this._vertexBoundsScaleX=1,this._vertexBoundsScaleY=1,this._offsets={xUpperLeft:0,yUpperLeft:0,xUpperRight:0,yUpperRight:0,xBottomLeft:0,yBottomLeft:0,xBottomRight:0,yBottomRight:0},this.geometryType=d.LW.MARKER}_write(t,e,i,s){const r=e.getDisplayId();t.recordStart(r,this._materialKey,this.geometryType,!0),this._writeGeometry(t,e,r,i,s),t.recordEnd()}_writeGeometry(t,e,i,s,r){if((0,l.pC)(this._markerPlacement))return this._writePlacedMarkers(t,e,s,r);if(this._allowBorrowing=!0,!r&&"esriGeometryPoint"===e.geometryType){const s=e.getX(),r=e.getY();if(!t.hasAggregates&&t.hasPixelBufferEnabled&&(s<0||s>=513||r<0||r>=513))return;return this._writeVertices(t,i,this._getPos(s,r),s,r)}const n=r?(0,bt.oB)((0,bt.GH)(r),2):"esriGeometryPolygon"===e.geometryType?e.readCentroid():e.readGeometryForDisplay();if(!(0,l.Wi)(n)){if(n.isPoint){const[e,s]=n.coords;if(!t.hasAggregates&&t.hasPixelBufferEnabled&&(e<0||e>=512||s<0||s>=512))return;return this._writeVertices(t,i,this._getPos(e,s),e,s)}n.forEachVertex(((e,s)=>{const r=2*A.I_;e<-r||e>=r||s<-r||s>=r||this._writeVertices(t,i,this._getPos(e,s),e,s)}))}}_writePlacedMarkers(t,e,i,s){const r=s??e.readLegacyGeometryForDisplay(),n=fi.getPlacement(r,(0,l.Wg)(this._markerPlacement),(0,G.F2)(1),t.tileKey,i.geometryEngine);if(!n)return;this._allowBorrowing="esriGeometryPolygon"!==e.geometryType;const o=e.getDisplayId(),a=(0,_i.c)(),h=Q();let c=n.next();for(;null!=c;){const e=c.tx,i=-c.ty;e>=-128&&e<=640&&i>=-128&&i<=640&&(this._applyTransformation(h,a,-c.getAngle()/Gi),this._writeVertices(t,o,this._getPos(e,i),e,i)),c=n.next()}}_writeVertices(t,e,i,s,r){const n=ot.mE.load(this._materialKey);return n.symbologyType===d.mD.HEATMAP?this._writeHeatmapVertices(t,e,i):this._writeMarkerVertices(t,e,n,i,s,r)}_writeMarkerVertices(t,e,i,s,r,n){const o=i.vvRotation,a=t.vertexCount();let h=this._computedWidth*this._vertexBoundsScaleX,l=this._computedHeight*this._vertexBoundsScaleY;if(this.angle){const t=Math.max(h,l);h=t,l=t}if(o){const t=Math.max(this.xOffset,this.yOffset);h+=t,l+=t}this._allowBorrowing&&t.vertexBounds(r+this.xOffset,n-this.yOffset,h,l),t.vertexWrite(s),t.vertexWrite(this._offsetUpperLeft),t.vertexWrite(this._texUpperLeft),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),t.vertexWrite(s),t.vertexWrite(this._offsetUpperRight),t.vertexWrite(this._texUpperRight),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),t.vertexWrite(s),t.vertexWrite(this._offsetBottomLeft),t.vertexWrite(this._texBottomLeft),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),t.vertexWrite(s),t.vertexWrite(this._offsetBottomRight),t.vertexWrite(this._texBottomRight),t.vertexWrite(this._bitestAndDistRatio),t.vertexWrite(e),t.vertexWrite(this._fillColor),t.vertexWrite(this._outlineColor),t.vertexWrite(this._sizeOutlineWidth),t.vertexWrite(this._minMaxZoom),t.vertexEnd(),this._writeIndices(t,a)}_writeHeatmapVertices(t,e,i){const s=t.vertexCount();t.vertexWrite(i),t.vertexWrite(this._offsetUpperLeft),t.vertexWrite(e),t.vertexEnd(),t.vertexWrite(i),t.vertexWrite(this._offsetUpperRight),t.vertexWrite(e),t.vertexEnd(),t.vertexWrite(i),t.vertexWrite(this._offsetBottomLeft),t.vertexWrite(e),t.vertexEnd(),t.vertexWrite(i),t.vertexWrite(this._offsetBottomRight),t.vertexWrite(e),t.vertexEnd(),this._writeIndices(t,s)}_writeIndices(t,e){t.indexWrite(e+0),t.indexWrite(e+1),t.indexWrite(e+2),t.indexWrite(e+1),t.indexWrite(e+3),t.indexWrite(e+2)}_applyTransformation(t,e,i=0){q(t,(0,_i.f)(this.xOffset,-this.yOffset)),null!=this.angle&&this.angle+i!==0&&Y(t,t,Gi*(this.angle+i));const s=this._computedWidth,r=this._computedHeight,n=-(.5+this._anchorX)*s,o=-(.5-this._anchorY)*r;(0,y.s)(e,n,o),(0,y.t)(e,e,t),this._offsetUpperLeft=(0,nt.UJ)(16*e[0],16*e[1]),this._offsets.xUpperLeft=e[0],this._offsets.yUpperLeft=e[1],(0,y.s)(e,n+s,o),(0,y.t)(e,e,t),this._offsetUpperRight=(0,nt.UJ)(16*e[0],16*e[1]),this._offsets.xUpperRight=e[0],this._offsets.yUpperRight=e[1],(0,y.s)(e,n,o+r),(0,y.t)(e,e,t),this._offsetBottomLeft=(0,nt.UJ)(16*e[0],16*e[1]),this._offsets.xBottomLeft=e[0],this._offsets.yBottomLeft=e[1],(0,y.s)(e,n+s,o+r),(0,y.t)(e,e,t),this._offsetBottomRight=(0,nt.UJ)(16*e[0],16*e[1]),this._offsets.xBottomRight=e[0],this._offsets.yBottomRight=e[1]}_getPos(t,e){return(0,nt.UJ)(Math.round(8*t),Math.round(8*e))}};class Hi extends(Xi(Ri)){constructor(t,e,i,s,r,n,o,a,h,l,c,u,f,_,m,p,g,y,x,b,M,v,S){super(),this.angle=s,this.height=o,this.width=n,this.xOffset=e*x,this.yOffset=i*x,this._markerPlacement=b,this._effects=M,this._anchorX=p,this._anchorY=g,this._minMaxZoom=(0,nt.UJ)(Math.round(v*A.MI),Math.round(S*A.MI));const w=(_===it.v2.MAP?A.Tz:A.CQ)|(c?A.Uh:0)|(f?A.oK:0)|(u?A.e0:0),P=m&&m.sdf,C=ot.mE.load(t);C.sdf=P,C.pattern=!0,C.textureBinding=m.textureBinding,this._materialKey=C.data,this._fillColor=r,this._outlineColor=h,this._sizeOutlineWidth=(0,nt.Jz)(Math.round(Math.min(Math.sqrt(128*n),255)),Math.round(Math.min(Math.sqrt(128*o),255)),Math.round(Math.min(Math.sqrt(128*l),255)),Math.round(Math.min(Math.sqrt(128*a),255)));const L=m.rect.x+A.fL,I=m.rect.y+A.fL,k=L+m.width,T=I+m.height;this._offsets.xUpperLeft=L,this._offsets.yUpperLeft=I,this._offsets.xUpperRight=k,this._offsets.yUpperRight=I,this._offsets.xBottomLeft=L,this._offsets.yBottomLeft=T,this._offsets.xBottomRight=k,this._offsets.yBottomRight=T,C.symbologyType===d.mD.PIE_CHART?(this._texUpperLeft=(0,nt.UJ)(0,1),this._texUpperRight=(0,nt.UJ)(1,1),this._texBottomLeft=(0,nt.UJ)(0,0),this._texBottomRight=(0,nt.UJ)(1,0)):(this._texUpperLeft=(0,nt.UJ)(L,I),this._texUpperRight=(0,nt.UJ)(k,I),this._texBottomLeft=(0,nt.UJ)(L,T),this._texBottomRight=(0,nt.UJ)(k,T)),n*=y,o*=y,n*=x,o*=x;const z=Math.round(64*y);this._bitestAndDistRatio=(0,nt.UJ)(w,z),this._computedWidth=n,this._computedHeight=o;const E=(0,_i.c)(),F=Q();this._applyTransformation(F,E)}static fromCIMMarker(t,e,i){const s=e&&e.width||1,r=e&&e.height||1,n=t.size,o=s/r*t.scaleX,a=t.scaleSymbolsProportionally&&t.frameHeight?n/t.frameHeight:1,h=(0,rt.t2)(t.color),c=(0,rt.t2)(t.outlineColor),u=(0,G.F2)(n),f=u*o,_=(0,G.F2)(t.offsetX||0),m=(0,G.F2)(t.offsetY||0),d=(0,G.F2)(t.outlineWidth||0)*a,p=t.alignment||it.v2.SCREEN,g=(0,G.F2)(t.referenceSize),[y,x]=mt(t.scaleInfo,i);let b=t.rotation||0;t.rotateClockwise||(b=-b);let M=0,v=0;const S=t.anchorPoint;S&&(t.isAbsoluteAnchorPoint?n&&(M=S.x/(n*o),v=S.y/n):(M=S.x,v=S.y));const w=new Hi(t.materialKey,_,m,b,h,f,u,g,c,d,t.colorLocked,t.scaleSymbolsProportionally,!1,p,e,M,v,t.sizeRatio,(0,l.Pt)(t.scaleFactor,1),t.markerPlacement,t.effects,y,x);return w._vertexBoundsScaleX=t.maxVVSize?t.maxVVSize/f:1,w._vertexBoundsScaleY=t.maxVVSize?t.maxVVSize/u:1,w}static fromPictureMarker(t,e){const i=Math.round((0,G.F2)(t.width)),s=Math.round((0,G.F2)(t.height)),r=A.ru,n=Math.round((0,G.F2)(t.xoffset||0)),o=Math.round((0,G.F2)(t.yoffset||0)),a=new Hi(t.materialKey,n,o,t.angle,r,i,s,s,0,0,!1,!1,!1,it.v2.SCREEN,e,0,0,1,1,null,null,0,at);return a._vertexBoundsScaleX=t.maxVVSize?t.maxVVSize/t.width:1,a._vertexBoundsScaleY=t.maxVVSize?t.maxVVSize/t.height:1,a}static fromSimpleMarker(t,e){const i=(0,rt.aH)(t.color),s=Math.round((0,G.F2)(t.size)),r=s,n=Math.round((0,G.F2)(t.xoffset||0)),o=Math.round((0,G.F2)(t.yoffset||0)),a=t.style,h=t.outline,l=0|(h?.color&&(0,rt.aH)(h.color)),c=0|(h?.width&&Math.round((0,G.F2)(h.width))),u=new Hi(t.materialKey,n,o,t.angle,i,s,r,r,l,c,!1,!1,"esriSMSCross"===a||"esriSMSX"===a,it.v2.SCREEN,e,0,0,126/64,1,null,null,0,at);return u.boundsType="esriSMSCircle"===a?"circle":"square",u._vertexBoundsScaleX=t.maxVVSize?t.maxVVSize/t.size:1,u._vertexBoundsScaleY=t.maxVVSize?t.maxVVSize/t.size:1,u}static fromLineSymbolMarker(t,e){const i=(0,rt.aH)(t.color),s=Math.round((0,G.F2)(6*t.lineWidth)),r=s,n="cross"===t.style||"x"===t.style;let o;switch(t.placement){case"begin-end":o=it.Tx.Both;break;case"begin":o=it.Tx.JustBegin;break;case"end":o=it.Tx.JustEnd;break;default:o=it.Tx.None}const a={type:"CIMMarkerPlacementAtExtremities",angleToLine:!0,offset:0,extremityPlacement:o,offsetAlongLine:0},h=new Hi(t.materialKey,0,0,0,i,s,r,r/6,i,n?Math.round((0,G.F2)(t.lineWidth)):0,!1,!1,n,it.v2.MAP,e,0,0,126/64,1,a,null,0,at);return h.boundsType="circle"===t.style?"circle":"square",h}}var Yi=i(58333);function Ji(t){return function(t,e){return(t%=e)>=0?t:t+e}(.7111111111111111*t,256)}Math.PI,Math.LN2,i(95401);var Zi=i(5428);function qi(t,e,i,s,r,n,o){xs=0;const a=(s-i)*n,h=r&&r.length,l=h?(r[0]-i)*n:a;let c,u,f,_,m,d=Ki(e,i,0,0,l,n,!0);if(d&&d.next!==d.prev){if(h&&(d=function(t,e,i,s,r,n){const o=new Array;for(let r=0,a=s.length;r<a;r++){const h=Ki(t,e,0,s[r]*n,r<a-1?s[r+1]*n:i*n,n,!1);h===h.next&&(h.steiner=!0),o.push(ss(h))}o.sort(fs);for(const t of o)r=rs(t,r);return r}(e,i,s,r,d,n)),a>80*n){c=f=e[0+i*n],u=_=e[1+i*n];for(let t=n;t<l;t+=n){const s=e[t+i*n],r=e[t+1+i*n];c=Math.min(c,s),u=Math.min(u,r),f=Math.max(f,s),_=Math.max(_,r)}m=Math.max(f-c,_-u),m=0!==m?1/m:0}ji(d,t,n,c,u,m,o,0)}}function Ki(t,e,i,s,r,n,o){let a;if(o===function(t,e,i,s,r,n){let o=0;for(let i=s,a=r-n;i<r;i+=n)o+=(t[a+e*n]-t[i+e*n])*(t[i+1+e*n]+t[a+1+e*n]),a=i;return o}(t,e,0,s,r,n)>0)for(let i=s;i<r;i+=n)a=es(i+e*n,t[i+e*n],t[i+1+e*n],a);else for(let i=r-n;i>=s;i-=n)a=es(i+e*n,t[i+e*n],t[i+1+e*n],a);return a&&us(a,a.next)&&(is(a),a=a.next),a}function $i(t,e=t){if(!t)return t;let i,s=t;do{if(i=!1,s.steiner||!us(s,s.next)&&0!==os(s.prev,s,s.next))s=s.next;else{if(is(s),s=e=s.prev,s===s.next)break;i=!0}}while(i||s!==e);return e}function ji(t,e,i,s,r,n,o,a){if(!t)return;!a&&n&&(t=ns(t,s,r,n));let h=t;for(;t.prev!==t.next;){const l=t.prev,c=t.next;if(n?ts(t,s,r,n):Qi(t))e.push(l.index/i+o),e.push(t.index/i+o),e.push(c.index/i+o),is(t),t=c.next,h=c.next;else if((t=c)===h){a?1===a?ji(t=_s(t,e,i,o),e,i,s,r,n,o,2):2===a&&ms(t,e,i,s,r,n,o):ji($i(t),e,i,s,r,n,o,1);break}}}function Qi(t){const e=t.prev,i=t,s=t.next;if(os(e,i,s)>=0)return!1;let r=t.next.next;const n=r;let o=0;for(;r!==t.prev&&(0===o||r!==n);){if(o++,hs(e.x,e.y,i.x,i.y,s.x,s.y,r.x,r.y)&&os(r.prev,r,r.next)>=0)return!1;r=r.next}return!0}function ts(t,e,i,s){const r=t.prev,n=t,o=t.next;if(os(r,n,o)>=0)return!1;const a=r.x<n.x?r.x<o.x?r.x:o.x:n.x<o.x?n.x:o.x,h=r.y<n.y?r.y<o.y?r.y:o.y:n.y<o.y?n.y:o.y,l=r.x>n.x?r.x>o.x?r.x:o.x:n.x>o.x?n.x:o.x,c=r.y>n.y?r.y>o.y?r.y:o.y:n.y>o.y?n.y:o.y,u=cs(a,h,e,i,s),f=cs(l,c,e,i,s);let _=t.prevZ,m=t.nextZ;for(;_&&_.z>=u&&m&&m.z<=f;){if(_!==t.prev&&_!==t.next&&hs(r.x,r.y,n.x,n.y,o.x,o.y,_.x,_.y)&&os(_.prev,_,_.next)>=0)return!1;if(_=_.prevZ,m!==t.prev&&m!==t.next&&hs(r.x,r.y,n.x,n.y,o.x,o.y,m.x,m.y)&&os(m.prev,m,m.next)>=0)return!1;m=m.nextZ}for(;_&&_.z>=u;){if(_!==t.prev&&_!==t.next&&hs(r.x,r.y,n.x,n.y,o.x,o.y,_.x,_.y)&&os(_.prev,_,_.next)>=0)return!1;_=_.prevZ}for(;m&&m.z<=f;){if(m!==t.prev&&m!==t.next&&hs(r.x,r.y,n.x,n.y,o.x,o.y,m.x,m.y)&&os(m.prev,m,m.next)>=0)return!1;m=m.nextZ}return!0}function es(t,e,i,s){const r=gs.create(t,e,i);return s?(r.next=s.next,r.prev=s,s.next.prev=r,s.next=r):(r.prev=r,r.next=r),r}function is(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function ss(t){let e=t,i=t;do{(e.x<i.x||e.x===i.x&&e.y<i.y)&&(i=e),e=e.next}while(e!==t);return i}function rs(t,e){const i=function(t,e){let i=e;const s=t.x,r=t.y;let n,o=-1/0;do{if(r<=i.y&&r>=i.next.y&&i.next.y!==i.y){const t=i.x+(r-i.y)*(i.next.x-i.x)/(i.next.y-i.y);if(t<=s&&t>o){if(o=t,t===s){if(r===i.y)return i;if(r===i.next.y)return i.next}n=i.x<i.next.x?i:i.next}}i=i.next}while(i!==e);if(!n)return null;if(s===o)return n.prev;const a=n,h=n.x,l=n.y;let c,u=1/0;for(i=n.next;i!==a;)s>=i.x&&i.x>=h&&s!==i.x&&hs(r<l?s:o,r,h,l,r<l?o:s,r,i.x,i.y)&&(c=Math.abs(r-i.y)/(s-i.x),(c<u||c===u&&i.x>n.x)&&ls(i,t)&&(n=i,u=c)),i=i.next;return n}(t,e);if(!i)return e;const s=ps(i,t);return $i(s,s.next),$i(i,i.next)}function ns(t,e,i,s){let r;for(;r!==t;r=r.next){if(r=r||t,null===r.z&&(r.z=cs(r.x,r.y,e,i,s)),r.prev.next!==r||r.next.prev!==r)return r.prev.next=r,r.next.prev=r,ns(t,e,i,s);r.prevZ=r.prev,r.nextZ=r.next}return t.prevZ.nextZ=null,t.prevZ=null,function(t){let e,i=1;for(;;){let s,r=t;t=null,e=null;let n=0;for(;r;){n++,s=r;let o=0;for(;o<i&&s;o++)s=s.nextZ;let a=i;for(;o>0||a>0&&s;){let i;0===o?(i=s,s=s.nextZ,a--):0!==a&&s?r.z<=s.z?(i=r,r=r.nextZ,o--):(i=s,s=s.nextZ,a--):(i=r,r=r.nextZ,o--),e?e.nextZ=i:t=i,i.prevZ=e,e=i}r=s}if(e.nextZ=null,i*=2,n<2)return t}}(t)}function os(t,e,i){return(e.y-t.y)*(i.x-e.x)-(e.x-t.x)*(i.y-e.y)}function as(t,e,i,s){return!!(us(t,e)&&us(i,s)||us(t,s)&&us(i,e))||os(t,e,i)>0!=os(t,e,s)>0&&os(i,s,t)>0!=os(i,s,e)>0}function hs(t,e,i,s,r,n,o,a){return(r-o)*(e-a)-(t-o)*(n-a)>=0&&(t-o)*(s-a)-(i-o)*(e-a)>=0&&(i-o)*(n-a)-(r-o)*(s-a)>=0}function ls(t,e){return os(t.prev,t,t.next)<0?os(t,e,t.next)>=0&&os(t,t.prev,e)>=0:os(t,e,t.prev)<0||os(t,t.next,e)<0}function cs(t,e,i,s,r){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-i)*r)|t<<8))|t<<4))|t<<2))|t<<1))|(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=32767*(e-s)*r)|e<<8))|e<<4))|e<<2))|e<<1))<<1}function us(t,e){return t.x===e.x&&t.y===e.y}function fs(t,e){return t.x-e.x}function _s(t,e,i,s){let r=t;do{const n=r.prev,o=r.next.next;!us(n,o)&&as(n,r,r.next,o)&&ls(n,o)&&ls(o,n)&&(e.push(n.index/i+s),e.push(r.index/i+s),e.push(o.index/i+s),is(r),is(r.next),r=t=o),r=r.next}while(r!==t);return r}function ms(t,e,i,s,r,n,o){let a=t;do{let t=a.next.next;for(;t!==a.prev;){if(a.index!==t.index&&ds(a,t)){let h=ps(a,t);return a=$i(a,a.next),h=$i(h,h.next),ji(a,e,i,s,r,n,o,0),void ji(h,e,i,s,r,n,o,0)}t=t.next}a=a.next}while(a!==t)}function ds(t,e){return t.next.index!==e.index&&t.prev.index!==e.index&&!function(t,e){let i=t;do{if(i.index!==t.index&&i.next.index!==t.index&&i.index!==e.index&&i.next.index!==e.index&&as(i,i.next,t,e))return!0;i=i.next}while(i!==t);return!1}(t,e)&&ls(t,e)&&ls(e,t)&&function(t,e){let i=t,s=!1;const r=(t.x+e.x)/2,n=(t.y+e.y)/2;do{i.y>n!=i.next.y>n&&i.next.y!==i.y&&r<(i.next.x-i.x)*(n-i.y)/(i.next.y-i.y)+i.x&&(s=!s),i=i.next}while(i!==t);return s}(t,e)}function ps(t,e){const i=gs.create(t.index,t.x,t.y),s=gs.create(e.index,e.x,e.y),r=t.next,n=e.prev;return t.next=e,e.prev=t,i.next=r,r.prev=i,s.next=i,i.prev=s,n.next=s,s.prev=n,s}class gs{constructor(){this.index=0,this.x=0,this.y=0,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}static create(t,e,i){const s=xs<ys.length?ys[xs++]:new gs;return s.index=t,s.x=e,s.y=i,s.prev=null,s.next=null,s.z=null,s.prevZ=null,s.nextZ=null,s.steiner=!1,s}}const ys=new Array;let xs=0;for(let t=0;t<8096;t++)ys.push(new gs);const bs=new Zt.bN(0,0,0,1,0),Ms=new Zt.bN(0,0,0,1,0);function vs(t,e,i){let s=0;for(let r=1;r<i;r++){const i=t[2*(e+r-1)],n=t[2*(e+r-1)+1];s+=(t[2*(e+r)]-i)*(t[2*(e+r)+1]+n)}return s}function Ss(t,e,i,s,r){let n=0;for(let o=i;o<s;o+=3){const i=2*(t[o]-r),s=2*(t[o+1]-r),a=2*(t[o+2]-r);n+=Math.abs((e[i]-e[a])*(e[s+1]-e[i+1])-(e[i]-e[s])*(e[a+1]-e[i+1]))}return n}bs.setExtent(A.I_),Ms.setExtent(A.I_);var ws=i(67327);const Ps=16,Cs=t=>class extends t{constructor(...t){super(...t),this.tessellationProperties={},this._tessellationOptions={halfWidth:0,pixelCoordRatio:1,offset:0},this.geometryType=d.LW.LINE}writeGeometry(t,e,i,s){this._writeGeometry(t,e,i,s)}_initializeTessellator(t){const e=ot.a.load(this._materialKey),i=ot.dk.load(this._materialKey),s=this._tessellationOptions,r=e.vvSizeFieldStops||e.vvSizeMinMaxValue||e.vvSizeScaleStops||e.vvSizeUnitValue,n=this.tessellationProperties._halfWidth<A.tQ&&!t&&!r;this.tessellationProperties.minMaxZoom=this._minMaxZoom,s.wrapDistance=65535,s.textured=this._isDashed||this._hasPattern,s.offset=this.tessellationProperties.offset,s.halfWidth=this.tessellationProperties._halfWidth;const o=n?0:1,a=(0,ot.CA)(i)?Is:Ls;this._lineTessellator=new ws.z(a(this.tessellationProperties,o,o),ks(this.tessellationProperties),n)}_write(t,e,i,s){const r="esriGeometryPoint"===e.geometryType;t.recordStart(e.getDisplayId(),this._materialKey,this.geometryType,r),this._writeGeometry(t,e,s,r),t.recordEnd()}_writeGeometry(t,e,i,s){const r=i??e.readLegacyGeometryForDisplay(),n=this._getLines(r,s);(0,l.Wi)(n)||this._writeVertices(t,e,n)}_getLines(t,e){if((0,l.Wi)(t))return null;const i=t.paths||t.rings;return(0,l.Wi)(i)?null:function(t,e){Ms.setPixelMargin(e);const i=Ms,s=-e,r=A.I_+e;let n=[],o=!1,a=0;for(;a<t.length;){const e=[],h=t[a];if(!h)return null;i.reset(Zt.Vl.LineString);let[l,c]=h[0];if(o)i.moveTo(l,c);else{if(l<s||l>r||c<s||c>r){o=!0;continue}e.push({x:l,y:c})}let u=!1;const f=h.length;for(let t=1;t<f;++t)if(l+=h[t][0],c+=h[t][1],o)i.lineTo(l,c);else{if(l<s||l>r||c<s||c>r){u=!0;break}e.push({x:l,y:c})}if(u)o=!0;else{if(o){const t=i.resultWithStarts();if(t)for(const e of t)n.push(e)}else n.push({line:e,start:0});a++,o=!1}}return n=n.filter((t=>t.line.length>1)),0===n.length?null:n}(i,e?256:16)}_writeVertices(t,e,i){const s=e.getDisplayId(),r=t.vertexCount(),n=this.tessellationProperties,o=this._tessellationOptions;n.out=t,n.id=s,n.indexCount=0,n.vertexCount=0,n.offset=r,o.capType=this._capType,o.joinType=this._joinType;const a=ot.dk.load(this._materialKey);this.tessellationProperties.key=(0,ot.CA)(a)?a:ot.a.load(this._materialKey);for(const{line:t,start:e}of i)o.initialDistance=e%65535,this._lineTessellator.tessellate(t,o)}},Ls=(t,e,i)=>(s,r,n,o,a,h,l,c,u,f,_)=>{const m=(0,nt.UJ)(_,Math.ceil(Ps*t._halfWidth)),d=(0,nt.Jz)(Math.round(Ps*l),Math.round(Ps*c),Math.round(Ps*u),Math.round(Ps*f)),p=(0,nt.Jz)(Ps*a,Ps*h,0,t._bitset),g=t.out;return g.vertexBounds(s,r,e,i),g.vertexWrite((0,nt.UJ)(8*s,8*r)),g.vertexWrite(t.id),g.vertexWrite(t._fillColor),g.vertexWrite(d),g.vertexWrite(m),g.vertexWrite(t._tl),g.vertexWrite(t._br),g.vertexWrite(p),g.vertexWrite((0,nt.UJ)(Math.ceil(Ps*t._halfReferenceWidth),0)),g.vertexWrite(t.minMaxZoom),g.vertexEnd(),t.offset+t.vertexCount++},Is=(t,e,i)=>(s,r,n,o,a,h,l,c,u,f,_)=>{const m=(0,nt.UJ)(Ps*t._halfWidth,Ps*t._halfReferenceWidth),d=(0,nt.Jz)(Ps*l+128,Ps*c+128,Ps*u+128,Ps*f+128),p=t.out,g=t._bitset<<24|t.id;p.vertexBounds(s,r,e,i),p.vertexWrite((0,nt.UJ)(8*s,8*r)),p.vertexWrite(g),p.vertexWrite(t._fillColor);const y=(0,ot.Xp)(t.key);return y||(p.vertexWrite(0),p.vertexWrite(0)),p.vertexWrite(0),p.vertexWrite(m),p.vertexWrite(d),y||p.vertexWrite(t.minMaxZoom),p.vertexEnd(),t.offset+t.vertexCount++},ks=t=>(e,i,s)=>{const r=t.out;r.indexWrite(e),r.indexWrite(i),r.indexWrite(s),t.indexCount+=3};class Ts extends(Cs(Ri)){constructor(t,e,i,s,r,n,o,a,h,l,c,u,f,_,m,d,p,g,y,x){super();const b=ot.a.load(t);e&&(b.sdf=e.sdf,b.pattern=!0,b.textureBinding=e.textureBinding),this._capType=s,this._joinType=r,this._miterLimitCosine=_t(n),this.tessellationProperties._fillColor=o,this.tessellationProperties._tl=a,this.tessellationProperties._br=h,this._hasPattern=l,this._isDashed=c,this._zOrder=p,this._effects=g,this._minMaxZoom=(0,nt.UJ)(Math.round(y*A.MI),Math.round(x*A.MI)),this._materialKey=b.data;const M=(f?A.Uh:0)|(_?A.SD:0)|(u?A._6:0)|(m?A.Iv:0);this.tessellationProperties._bitset=M,this.tessellationProperties._halfWidth=.5*i,this.tessellationProperties._halfReferenceWidth=.5*d,this.tessellationProperties.offset=0,this._initializeTessellator(!1)}static fromCIMLine(t,e,i){const s=t.color,r=t.scaleFactor||1,n=!!t.dashTemplate;let o=t.cap;n&&o===it.RL.ROUND&&(o=it.RL.SQUARE);const a=t.join,h=(0,G.F2)(t.width)*r,l=(0,G.F2)(t.referenceWidth),c=(0,G.F2)(t.miterLimit),u=s&&(0,rt.t2)(s)||0,[f,_]=mt(t.scaleInfo,i);if(!e)return new Ts(t.materialKey,e,h,o,a,c,u,0,0,!1,n,t.scaleDash??!1,t.colorLocked??!1,!1,t.sampleAlphaOnly,l,t.zOrder,t.effects,f,_);const{rect:m,width:d,height:p}=e,g=m.x+A.fL,y=m.y+A.fL,x=g+d,b=y+p,M=(0,nt.UJ)(g,y),v=(0,nt.UJ)(x,b);return new Ts(t.materialKey,e,h,o,a,c,u,M,v,!0,n,t.scaleDash??!1,t.colorLocked??!1,!1,t.sampleAlphaOnly,l,t.zOrder,t.effects,f,_)}static fromFillOutline(t){const e=ot.dk.load(t.materialKey);return(0,ot.CA)(e)&&t.outline&&"esriSLSSolid"===t.outline?.style?Ts.fromSimpleLine({hash:"",materialKey:t.materialKey,...t.outline},null,!0):null}static fromSimpleLine(t,e,i=!1){const{color:s}=t,r="esriSLSSolid"!==t.style&&"esriSLSNull"!==t.style,n=(0,E.ws)(t.cap||"round"),o=(0,E.xV)(t.join||"round");let a=s&&"esriSLSNull"!==t.style&&(0,rt.aH)(s)||0;"esriSLSNull"===t.style&&(a=0);const h=(0,G.F2)(t.width),l=t.miterLimit;if(!e)return new Ts(t.materialKey,e,h,n,o,l,a,0,0,!1,r,!0,!1,i,!1,h,0,null,0,at);const{rect:c,width:u,height:f}=e,_=c.x+A.fL,m=c.y+A.fL,d=_+u,p=m+f,g=(0,nt.UJ)(_,m),y=(0,nt.UJ)(d,p);return new Ts(t.materialKey,e,h,n,o,l,a,g,y,!0,r,!0,!1,i,!1,h,0,null,0,at)}static fromPictureLineSymbol(t,e,i,s){return h.Z.getLogger("esri.views.2d.engine.webgl.WGLLineTemplate").error("PictureLineSymbol support does not exist!"),null}}const zs=t=>class extends t{constructor(...t){super(...t),this.forceLibtess=!1,this._bitset=0,this._lineTemplate=null,this.geometryType=d.LW.FILL}_maybeAddLineTemplate(t){this._lineTemplate=Ts.fromFillOutline(t)}_write(t,e,i,s){const r="esriGeometryPoint"===e.geometryType,n=ot.dk.load(this._materialKey);t.recordStart(e.getDisplayId(),this._materialKey,this.geometryType,r),this._writeGeometry(t,e,n,s,r),(0,ot.CA)(n)&&(0,l.pC)(this._lineTemplate)&&this._lineTemplate.writeGeometry(t,e,s,r),t.recordEnd()}_writeGeometry(t,e,i,s,r){const n=this._getGeometry(e,s,r);if((0,l.Wi)(n))return;const o=[];if(!(n.maxLength>100)&&!this.forceLibtess&&function(t,e){const{coords:i,lengths:s,hasIndeterminateRingOrder:r}=e,n=t;if(r)return!1;let o=0;for(let t=0;t<s.length;){let e=t,r=s[t],a=vs(i,o,r);const h=[];for(;++e<s.length;){const t=s[e],n=vs(i,o+r,t);if(!(n>0))break;a+=n,h.push(o+r),r+=t}const l=n.length;qi(n,i,o,o+r,h,2,0);const c=Ss(n,i,l,n.length,0),u=Math.abs(a);if(Math.abs((c-u)/Math.max(1e-7,u))>1e-5)return n.length=0,!1;t=e,o+=r}return!0}(o,n))return void(o.length&&this._writeVertices(t,e,n.coords,n.lengths,i,o));const a=function(t){const{coords:e,lengths:i}=t,{buffer:s}=(0,B.b)(e,i);return s}(n);this._writeVertices(t,e,a,[a.length/2],i)}_writeVertex(t,e,i,s,r,n){const o=(0,nt.UJ)(1*s,1*r);if(t.vertexBounds(s,r,0,0),t.vertexWrite(o),t.vertexWrite(e),i.symbologyType===d.mD.DOT_DENSITY)t.vertexWriteF32(1/Math.abs(n.readGeometryArea()));else{t.vertexWrite(this.fillColor);const e=(0,ot.Xp)(i);e||(t.vertexWrite(this.tl),t.vertexWrite(this.br)),t.vertexWrite(this.aux21),t.vertexWrite(this.aux22),t.vertexWrite(this.aux3),e||t.vertexWrite(this._minMaxZoom)}}_writeVertices(t,e,i,s,r,n){const o=e.getDisplayId(),a=this._bitset<<24|o,h=s.reduce(((t,e)=>t+e)),l=(0,E.$_)(r.geometryType,r.symbologyType).geometry/4,c=t.vertexCount();t.vertexEnsureSize(l*h);let u=0;if(n)for(const s of n){const n=i[2*s],o=i[2*s+1];this._writeVertex(t,a,r,n,o,e),u++}else for(let s=0;s<i.length;s+=2){const n=Math.round(i[s]),o=Math.round(i[s+1]);this._writeVertex(t,a,r,n,o,e),u++}t.indexEnsureSize(u);for(let e=0;e<u;e++)t.indexWrite(e+c)}_getGeometry(t,e,i){const s=e?(0,bt.oB)((0,bt.GH)(e),2):t.readGeometryForDisplay();return s?function(t,e){if((0,l.Wi)(t))return null;if(!function(t,e,i){let s=0;for(let e=0;e<t.lengths.length;e++){const r=t.lengths[e];for(let e=0;e<r;e++){const r=t.coords[2*(e+s)],n=t.coords[2*(e+s)+1];if(r<-128||r>i||n<-128||n>i)return!0}s+=r}return!1}(t,0,A.I_+128))return t;bs.setPixelMargin(e),bs.reset(Zt.Vl.Polygon);let i=0;for(let e=0;e<t.lengths.length;e++){const s=t.lengths[e];let r=t.coords[2*(0+i)],n=t.coords[2*(0+i)+1];bs.moveTo(r,n);for(let e=1;e<s;e++)r=t.coords[2*(e+i)],n=t.coords[2*(e+i)+1],bs.lineTo(r,n);bs.close(),i+=s}const s=bs.result(!1);if(!s)return null;const r=[],n=[];for(const t of s){let e=0;for(const i of t)n.push(i.x),n.push(i.y),e++;r.push(e)}return new Zi.Z(r,n)}(s,i?256:8):null}};var As=i(22303);function Es(t){if(!t)return"arial-unicode-ms";const e=t.toLowerCase().split(" ").join("-");switch(e){case"serif":return"noto-serif";case"sans-serif":return"arial-unicode-ms";case"monospace":return"ubuntu-mono";case"fantasy":return"cabin-sketch";case"cursive":return"redressed";default:return e}}i(68773),new Map;var Fs=i(19153),Rs=i(59266),Ns=i(71143);class Os{applyColorSubstituition(t,e){if(!e)return t;this._rasterizationCanvas||(this._rasterizationCanvas=document.createElement("canvas"));const{width:i,height:s}=t,r=this._rasterizationCanvas,n=r.getContext("2d");t!==r&&(r.width=i,r.height=s,n.drawImage(t,0,0,i,s));const o=n.getImageData(0,0,i,s).data;if(e)for(const t of e)if(t&&t.oldColor&&4===t.oldColor.length&&t.newColor&&4===t.newColor.length){const[e,i,s,r]=t.oldColor,[n,a,h,l]=t.newColor;if(e===n&&i===a&&s===h&&r===l)continue;for(let t=0;t<o.length;t+=4)e===o[t]&&i===o[t+1]&&s===o[t+2]&&r===o[t+3]&&(o[t]=n,o[t+1]=a,o[t+2]=h,o[t+3]=l)}const a=new ImageData(o,i,s);return n.putImageData(a,0,0),r}tintImageData(t,e){if(!e||e.length<4)return t;this._rasterizationCanvas||(this._rasterizationCanvas=document.createElement("canvas"));const{width:i,height:s}=t,r=this._rasterizationCanvas,n=r.getContext("2d");t!==r&&(r.width=i,r.height=s,n.drawImage(t,0,0,i,s));const o=n.getImageData(0,0,i,s),a=new Uint8Array(o.data),h=[e[0]/255,e[1]/255,e[2]/255,e[3]/255];for(let t=0;t<a.length;t+=4)a[t+0]*=h[0],a[t+1]*=h[1],a[t+2]*=h[2],a[t+3]*=h[3];const l=new ImageData(new Uint8ClampedArray(a.buffer),i,s);return n.putImageData(l,0,0),r}}function Ws(t){const e=t.getFrame(0);if(e instanceof HTMLImageElement||e instanceof HTMLCanvasElement)return e;const i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=i.getContext("2d");return e instanceof ImageData?s.putImageData(e,0,0):s.drawImage(e,0,0),i}class Vs{constructor(t=0,e=0,i=0,s=0){this.x=t,this.y=e,this.width=i,this.height=s}get isEmpty(){return this.width<=0||this.height<=0}union(t){this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.width=Math.max(this.width,t.width),this.height=Math.max(this.height,t.height)}}class Ds{constructor(t){t&&(this._textRasterizationCanvas=t)}rasterizeText(t,e){this._textRasterizationCanvas||(this._textRasterizationCanvas=document.createElement("canvas"));const i=this._textRasterizationCanvas,s=i.getContext("2d");this._setFontProperties(s,e),this._parameters=e,this._textLines=t.split(/\r?\n/),this._lineHeight=this._computeLineHeight();const r=this._computeTextWidth(s,e),{decoration:n,weight:o}=e.font;this._lineThroughWidthOffset=n&&"line-through"===n?.1*this._lineHeight:0;const a=this._lineHeight*this._textLines.length;i.width=r+2*this._lineThroughWidthOffset,i.height=a,this._renderedLineHeight=Math.round(this._lineHeight*e.pixelRatio),this._renderedHaloSize=e.halo.size*e.pixelRatio,this._renderedWidth=r*e.pixelRatio,this._renderedHeight=a*e.pixelRatio,this._lineThroughWidthOffset*=e.pixelRatio;const h=e.color??[0,0,0,0],l=e.halo&&e.halo.color?e.halo.color:[0,0,0,0];this._fillStyle=function(t){return`rgba(${t.slice(0,3).toString()},${t[3]})`}(h),this._haloStyle=function(t){return`rgb(${t.slice(0,3).toString()})`}(l);const c=this._renderedLineHeight,u=this._renderedHaloSize;s.save(),s.clearRect(0,0,i.width,i.height),this._setFontProperties(s,e);const f=function(t,e){return"center"===t?.5*e:"right"===t?e:0}(s.textAlign,this._renderedWidth)+u,_=u,m=u>0;let d=this._lineThroughWidthOffset,p=0;m&&this._renderHalo(s,f,_,d,p,e),p+=_,d+=f;for(const t of this._textLines)m?(s.globalCompositeOperation="destination-out",s.fillStyle="rgb(0, 0, 0)",s.fillText(t,d,p),s.globalCompositeOperation="source-over",s.fillStyle=this._fillStyle,s.fillText(t,d,p)):(s.fillStyle=this._fillStyle,s.fillText(t,d,p)),n&&"none"!==n&&this._renderDecoration(s,d,p,n,o),p+=c;s.restore();const g=this._renderedWidth+2*this._lineThroughWidthOffset,y=this._renderedHeight,x=s.getImageData(0,0,g,y),b=new Uint8Array(x.data);if(e.premultiplyColors){let t;for(let e=0;e<b.length;e+=4)t=b[e+3]/255,b[e]=b[e]*t,b[e+1]=b[e+1]*t,b[e+2]=b[e+2]*t}let M,v;switch(e.horizontalAlignment){case"left":M=-.5;break;case"right":M=.5;break;default:M=0}switch(e.verticalAlignment){case"bottom":v=-.5;break;case"top":v=.5;break;default:v=0}return{size:[g,y],image:new Uint32Array(b.buffer),sdf:!1,simplePattern:!1,anchorX:M,anchorY:v,canvas:i}}_renderHalo(t,e,i,s,r,n){const o=this._renderedWidth,a=this._renderedHeight;this._haloRasterizationCanvas||(this._haloRasterizationCanvas=document.createElement("canvas")),this._haloRasterizationCanvas.width=o,this._haloRasterizationCanvas.height=a;const h=this._haloRasterizationCanvas,l=h.getContext("2d");l.clearRect(0,0,o,a),this._setFontProperties(l,n);const{decoration:c,weight:u}=n.font;l.fillStyle=this._haloStyle,l.strokeStyle=this._haloStyle,l.lineJoin="round",this._renderHaloNative(l,e,i,c,u),t.globalAlpha=this._parameters.halo.color[3],t.drawImage(h,0,0,o,a,s,r,o,a),t.globalAlpha=1}_renderHaloNative(t,e,i,s,r){const n=this._renderedLineHeight,o=this._renderedHaloSize;for(const a of this._textLines){const h=2*o,l=5,c=.1;for(let n=0;n<l;n++){const o=(1-(l-1)*c+n*c)*h;t.lineWidth=o,t.strokeText(a,e,i),s&&"none"!==s&&this._renderDecoration(t,e,i,s,r,o)}i+=n}}_setFontProperties(t,e){const i=Math.max(e.size,.5),s=e.font,r=`${s.style} ${s.weight} ${(0,G.F2)(i*e.pixelRatio).toFixed(1)}px ${s.family}, sans-serif`;let n;switch(t.font=r,t.textBaseline="top",e.horizontalAlignment){case"left":default:n="left";break;case"right":n="right";break;case"center":n="center"}t.textAlign=n}computeTextSize(t,e){this._textRasterizationCanvas||(this._textRasterizationCanvas=document.createElement("canvas"));const i=this._textRasterizationCanvas,s=i.getContext("2d");this._setFontProperties(s,e),this._parameters=e,this._textLines=t.split(/\r?\n/),this._lineHeight=this._computeLineHeight();const r=this._computeTextWidth(s,e),n=this._lineHeight*this._textLines.length;return i.width=r,i.height=n,[r*e.pixelRatio,n*e.pixelRatio]}_computeTextWidth(t,e){let i=0;for(const e of this._textLines)i=Math.max(i,t.measureText(e).width);const s=e.font;return("italic"===s.style||"oblique"===s.style||"string"==typeof s.weight&&("bold"===s.weight||"bolder"===s.weight)||"number"==typeof s.weight&&s.weight>600)&&(i+=.3*t.measureText("w").width),i+=2*this._parameters.halo.size,Math.round(i)}_computeLineHeight(){let t=1.275*this._parameters.size;const e=this._parameters.font.decoration;return e&&"underline"===e&&(t*=1.3),Math.round(t+2*this._parameters.halo.size)}_renderDecoration(t,e,i,s,r,n){const o=.9*this._lineHeight,a="bold"===r?.06:"bolder"===r?.09:.04;switch(t.textAlign){case"center":e-=this._renderedWidth/2;break;case"right":e-=this._renderedWidth}const h=t.textBaseline;if("underline"===s)switch(h){case"top":i+=o;break;case"middle":i+=o/2}else if("line-through"===s)switch(h){case"top":i+=o/1.5;break;case"middle":i+=o/3}const l=n?1.5*n:Math.ceil(o*a);t.save(),t.beginPath(),t.strokeStyle=t.fillStyle,t.lineWidth=l,t.moveTo(e-this._lineThroughWidthOffset,i),t.lineTo(e+this._renderedWidth+2*this._lineThroughWidthOffset,i),t.stroke(),t.restore()}}function Bs(t,e,i,s){return"function"==typeof t?t(e,i,s):t}function Us(t){let e=t.length;for(;e--;)if(!" /-,\n".includes(t.charAt(e)))return!1;return!0}function Gs(t,e){const i=[];let s=0,r=-1;do{if(r=t.indexOf("[",s),r>=s){if(r>s){const e=t.substr(s,r-s);i.push([e,null,Us(e)])}if(s=r+1,r=t.indexOf("]",s),r>=s){if(r>s){const n=e[t.substr(s,r-s)];n&&i.push([null,n,!1])}s=r+1}}}while(-1!==r);if(s<t.length-1){const e=t.substr(s);i.push([e,null,Us(e)])}return i}function Xs(t,e,i){let s="",r=null;for(const i of e){const[e,n,o]=i;if(e)o?r=e:(r&&(s+=r,r=null),s+=e);else{const e=t.attributes[n];e&&(r&&(s+=r,r=null),s+=e)}}return Hs(s,i)}function Hs(t,e){switch("string"!=typeof t&&(t=String(t)),e){case"LowerCase":return t.toLowerCase();case"Allcaps":return t.toUpperCase();default:return t}}function Ys(t){return t?{r:t[0],g:t[1],b:t[2],a:t[3]/255}:{r:0,g:0,b:0,a:0}}function Js(t){return null!=t&&("CIMMarkerPlacementAlongLineRandomSize"===t.type||"CIMMarkerPlacementAlongLineSameSize"===t.type||"CIMMarkerPlacementAlongLineVariableSize"===t.type||"CIMMarkerPlacementAtExtremities"===t.type||"CIMMarkerPlacementAtMeasuredUnits"===t.type||"CIMMarkerPlacementAtRatioPositions"===t.type||"CIMMarkerPlacementOnLine"===t.type||"CIMMarkerPlacementOnVertices"===t.type)}const Zs=(t,e=0)=>null==t||isNaN(t)?e:t,qs=t=>t.tintColor?Ys(t.tintColor):{r:255,g:255,b:255,a:1};function Ks(t){if(!t)return"normal";switch(t.toLowerCase()){case"italic":return"italic";case"oblique":return"oblique";default:return"normal"}}function $s(t){if(!t)return"normal";switch(t.toLowerCase()){case"bold":return"bold";case"bolder":return"bolder";case"lighter":return"lighter";default:return"normal"}}function js(t){let e="",i="";if(t){const s=t.toLowerCase();s.includes("italic")?e="italic":s.includes("oblique")&&(e="oblique"),s.includes("bold")?i="bold":s.includes("light")&&(i="lighter")}return{style:e,weight:i}}function Qs(t){return t.underline?"underline":t.strikethrough?"line-through":"none"}function tr(t){if(!t)return null;switch(t.type){case"CIMPolygonSymbol":if(t.symbolLayers)for(const e of t.symbolLayers){const t=tr(e);if(null!=t)return t}break;case"CIMTextSymbol":return tr(t.symbol);case"CIMSolidFill":return t.color}}function er(t){if(t)switch(t.type){case"CIMPolygonSymbol":case"CIMLineSymbol":{const e=t.symbolLayers;if(e)for(const t of e){const e=er(t);if(null!=e)return e}break}case"CIMTextSymbol":return er(t.symbol);case"CIMSolidStroke":case"CIMSolidFill":return t.color}}function ir(t){if(t)switch(t.type){case"CIMPolygonSymbol":case"CIMLineSymbol":if(t.symbolLayers)for(const e of t.symbolLayers){const t=ir(e);if(void 0!==t)return t}break;case"CIMTextSymbol":return ir(t.symbol);case"CIMSolidStroke":case"CIMGradientStroke":case"CIMPictureStroke":return t.width}}function sr(t){switch(t){case"Left":default:return"left";case"Right":return"right";case"Center":case"Justify":return"center"}}function rr(t){switch(t){case"Top":default:return"top";case"Center":return"middle";case"Baseline":return"baseline";case"Bottom":return"bottom"}}const nr=t=>t.includes("data:image/svg+xml"),or=Math.PI/180,ar=h.Z.getLogger("esri.symbols.cim.CIMSymbolDrawHelper");class hr{constructor(t){this._t=t}static createIdentity(){return new hr([1,0,0,0,1,0])}clone(){const t=this._t;return new hr(t.slice())}transform(t){const e=this._t;return[e[0]*t[0]+e[1]*t[1]+e[2],e[3]*t[0]+e[4]*t[1]+e[5]]}static createScale(t,e){return new hr([t,0,0,0,e,0])}scale(t,e){const i=this._t;return i[0]*=t,i[1]*=t,i[2]*=t,i[3]*=e,i[4]*=e,i[5]*=e,this}scaleRatio(){return Math.sqrt(this._t[0]*this._t[0]+this._t[1]*this._t[1])}static createTranslate(t,e){return new hr([0,0,t,0,0,e])}translate(t,e){const i=this._t;return i[2]+=t,i[5]+=e,this}static createRotate(t){const e=Math.cos(t),i=Math.sin(t);return new hr([e,-i,0,i,e,0])}rotate(t){return hr.multiply(this,hr.createRotate(t),this)}angle(){const t=this._t[0],e=this._t[3],i=Math.sqrt(t*t+e*e);return[t/i,e/i]}static multiply(t,e,i){const s=t._t,r=e._t,n=s[0]*r[0]+s[3]*r[1],o=s[1]*r[0]+s[4]*r[1],a=s[2]*r[0]+s[5]*r[1]+r[2],h=s[0]*r[3]+s[3]*r[4],l=s[1]*r[3]+s[4]*r[4],c=s[2]*r[3]+s[5]*r[4]+r[5],u=i._t;return u[0]=n,u[1]=o,u[2]=a,u[3]=h,u[4]=l,u[5]=c,i}invert(){const t=this._t;let e=t[0]*t[4]-t[1]*t[3];if(0===e)return new hr([0,0,0,0,0,0]);e=1/e;const i=(t[1]*t[5]-t[2]*t[4])*e,s=(t[2]*t[3]-t[0]*t[5])*e,r=t[4]*e,n=-t[1]*e,o=-t[3]*e,a=t[0]*e;return new hr([r,n,i,o,a,s])}}class lr{constructor(t,e){this._resourceManager=t,this._transfos=[],this._sizeTransfos=[],this._geomUnitsPerPoint=1,this._placementPool=new Ns.Z(St,void 0,void 0,100),this._earlyReturn=!1,this._mapRotation=0,this._transfos.push(e||hr.createIdentity()),this._sizeTransfos.push(e?e.scaleRatio():1)}setTransform(t,e){this._transfos=[t||hr.createIdentity()],this._sizeTransfos=[e||(t?t.scaleRatio():1)]}setGeomUnitsPerPoint(t){this._geomUnitsPerPoint=t}transformPt(t){return this._transfos[this._transfos.length-1].transform(t)}transformSize(t){return t*this._sizeTransfos[this._sizeTransfos.length-1]}reverseTransformPt(t){return this._transfos[this._transfos.length-1].invert().transform(t)}reverseTransformSize(t){return t/this._sizeTransfos[this._sizeTransfos.length-1]}getTransformAngle(){return this._transfos[this._transfos.length-1].angle()}geomUnitsPerPoint(){return this.isEmbedded()?1:this._geomUnitsPerPoint}isEmbedded(){return this._transfos.length>1}back(){return this._transfos[this._transfos.length-1]}push(t,e){const i=e?t.scaleRatio():1;hr.multiply(t,this.back(),t),this._transfos.push(t),this._sizeTransfos.push(this._sizeTransfos[this._sizeTransfos.length-1]*i)}pop(){this._transfos.splice(-1,1),this._sizeTransfos.splice(-1,1)}drawSymbol(t,e,i){if(t)switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":this.drawMultiLayerSymbol(t,e);break;case"CIMTextSymbol":this.drawTextSymbol(t,e,i)}}drawMultiLayerSymbol(t,e){if(!t||!e)return;const i=t.symbolLayers;if(!i)return;const s=t.effects;if(s&&s.length>0){const t=this.executeEffects(s,e);if(t){let e=t.next();for(;e;)this.drawSymbolLayers(i,e),e=t.next()}}else this.drawSymbolLayers(i,e)}executeEffects(t,e){const i=this._resourceManager.geometryEngine;let s=new Kt(e);for(const e of t){const t=ci(e);t&&(s=t.execute(s,e,this.geomUnitsPerPoint(),null,i))}return s}drawSymbolLayers(t,e){let i=t.length;for(;i--;){const s=t[i];if(!s||!1===s.enable)continue;const r=s.effects;if(r&&r.length>0){const t=this.executeEffects(r,e);if(t){let e=null;for(;(e=t.next())&&(this.drawSymbolLayer(s,e),!this._earlyReturn););}}else this.drawSymbolLayer(s,e);if(this._earlyReturn)return}}drawSymbolLayer(t,e){switch(t.type){case"CIMSolidFill":this.drawSolidFill(e,t.color);break;case"CIMHatchFill":this.drawHatchFill(e,t);break;case"CIMPictureFill":this.drawPictureFill(e,t);break;case"CIMGradientFill":this.drawGradientFill(e,t);break;case"CIMSolidStroke":this.drawSolidStroke(e,t.color,t.width,t.capStyle,t.joinStyle,t.miterLimit);break;case"CIMPictureStroke":this.drawPictureStroke(e,t);break;case"CIMGradientStroke":this.drawGradientStroke(e,t);break;case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":this.drawMarkerLayer(t,e)}}drawHatchFill(t,e){const i=this._buildHatchPolyline(e,t,this.geomUnitsPerPoint());i&&(this.pushClipPath(t),this.drawMultiLayerSymbol(e.lineSymbol,i),this.popClipPath())}drawPictureFill(t,e){}drawGradientFill(t,e){}drawPictureStroke(t,e){}drawGradientStroke(t,e){}drawMarkerLayer(t,e){const i=t.markerPlacement;if(i){const s=ui(i);if(s){const r="CIMMarkerPlacementInsidePolygon"===i.type||"CIMMarkerPlacementPolygonCenter"===i.type&&i.clipAtBoundary;r&&this.pushClipPath(e);const n=s.execute(e,i,this.geomUnitsPerPoint(),null,this._resourceManager.geometryEngine);if(n){let e=null;for(;(e=n.next())&&(this.drawMarker(t,e),!this._earlyReturn););}r&&this.popClipPath()}}else{const i=this._placementPool.acquire();if((0,vt.wp)(e))i.tx=e.x,i.ty=e.y,this.drawMarker(t,i);else if((0,vt.oU)(e)){const s=(0,Je.tO)(e);s&&([i.tx,i.ty]=s,this.drawMarker(t,i))}else for(const s of e.points)if(i.tx=s[0],i.ty=s[1],this.drawMarker(t,i),this._earlyReturn)break;this._placementPool.release(i)}}drawMarker(t,e){switch(t.type){case"CIMCharacterMarker":case"CIMPictureMarker":this.drawPictureMarker(t,e);break;case"CIMVectorMarker":this.drawVectorMarker(t,e)}}drawPictureMarker(t,e){if(!t)return;const i=this._resourceManager.getResource(t.url),s=t.size??10;if((0,l.Wi)(i)||s<=0)return;const r=i.width,n=i.height;if(!r||!n)return;const o=r/n,a=t.scaleX??1,h=hr.createIdentity(),c=t.anchorPoint;if(c){let e=c.x,i=c.y;"Absolute"!==t.anchorPointUnits&&(e*=s*o*a,i*=s),h.translate(-e,-i)}let u=t.rotation??0;t.rotateClockwise&&(u=-u),this._mapRotation&&(u+=this._mapRotation),u&&h.rotate(u*or);let f=t.offsetX??0,_=t.offsetY??0;if(f||_){if(this._mapRotation){const t=or*this._mapRotation,e=Math.cos(t),i=Math.sin(t),s=f*i+_*e;f=f*e-_*i,_=s}h.translate(f,_)}const m=this.geomUnitsPerPoint();1!==m&&h.scale(m,m);const d=e.getAngle();d&&h.rotate(d),h.translate(e.tx,e.ty),this.push(h,!1),this.drawImage(t,s),this.pop()}drawVectorMarker(t,e){if(!t)return;const i=t.markerGraphics;if(!i)return;const s=t.size??10,r=t.frame,n=r?r.ymax-r.ymin:0,o=s&&n?s/n:1,a=hr.createIdentity();r&&a.translate(.5*-(r.xmax+r.xmin),.5*-(r.ymax+r.ymin));const h=t.anchorPoint;if(h){let e=h.x,i=h.y;"Absolute"!==t.anchorPointUnits?r&&(e*=r.xmax-r.xmin,i*=r.ymax-r.ymin):(e/=o,i/=o),a.translate(-e,-i)}1!==o&&a.scale(o,o);let l=t.rotation??0;t.rotateClockwise&&(l=-l),this._mapRotation&&(l+=this._mapRotation),l&&a.rotate(l*or);let c=t.offsetX??0,u=t.offsetY??0;if(c||u){if(this._mapRotation){const t=or*this._mapRotation,e=Math.cos(t),i=Math.sin(t),s=c*i+u*e;c=c*e-u*i,u=s}a.translate(c,u)}const f=this.geomUnitsPerPoint();1!==f&&a.scale(f,f);const _=e.getAngle();_&&a.rotate(_),a.translate(e.tx,e.ty),this.push(a,t.scaleSymbolsProportionally);for(const t of i)if(t&&t.symbol&&t.geometry||ar.error("Invalid marker graphic",t),this.drawSymbol(t.symbol,t.geometry,t.textString),this._earlyReturn)break;this.pop()}drawTextSymbol(t,e,i){if(!t)return;if(!(0,vt.wp)(e))return;if((t.height??10)<=0)return;const s=hr.createIdentity();let r=t.angle??0;r=-r,r&&s.rotate(r*or);const n=t.offsetX??0,o=t.offsetY??0;(n||o)&&s.translate(n,o);const a=this.geomUnitsPerPoint();1!==a&&s.scale(a,a),s.translate(e.x,e.y),this.push(s,!1),this.drawText(t,i),this.pop()}_buildHatchPolyline(t,e,i){let s=(void 0!==t.separation?t.separation:4)*i,r=void 0!==t.rotation?t.rotation:0;if(0===s)return null;s<0&&(s=-s);let n=0;const o=.5*s;for(;n>o;)n-=s;for(;n<-o;)n+=s;const a=(0,z.Ue)();(0,Se.$P)(a,e),a[0]-=o,a[1]-=o,a[2]+=o,a[3]+=o;const h=[[a[0],a[1]],[a[0],a[3]],[a[2],a[3]],[a[2],a[1]]];for(;r>180;)r-=180;for(;r<0;)r+=180;const l=Math.cos(r*or),c=Math.sin(r*or),u=-s*c,f=s*l;let _,m,d,p;n=(void 0!==t.offsetX?t.offsetX*i:0)*c-(void 0!==t.offsetY?t.offsetY*i:0)*l,_=d=Number.MAX_VALUE,m=p=-Number.MAX_VALUE;for(const t of h){const e=t[0],i=t[1],s=l*e+c*i,r=-c*e+l*i;_=Math.min(_,s),d=Math.min(d,r),m=Math.max(m,s),p=Math.max(p,r)}d=Math.floor(d/s)*s;let g=l*_-c*d-u*n/s,y=c*_+l*d-f*n/s,x=l*m-c*d-u*n/s,b=c*m+l*d-f*n/s;const M=1+Math.round((p-d)/s),v=[];for(let t=0;t<M;t++)g+=u,y+=f,x+=u,b+=f,v.push([[g,y],[x,b]]);return{paths:v}}}class cr extends lr{constructor(t,e){super(t,e),this.reset()}reset(){this._xmin=this._ymin=1/0,this._xmax=this._ymax=-1/0,this._clipCount=0}envelope(){return new Vs(this._xmin,this._ymin,this._xmax-this._xmin,this._ymax-this._ymin)}bounds(){return(0,z.al)(this._xmin,this._ymin,this._xmax,this._ymax)}drawSolidFill(t){if(t&&!(this._clipCount>0))if((0,vt.oU)(t))this._processPath(t.rings,0);else if((0,vt.l9)(t))this._processPath(t.paths,0);else if((0,vt.YX)(t)){const e=_r(t);e&&this._processPath(e.rings,0)}else console.error("drawSolidFill Unexpected geometry type!")}drawSolidStroke(t,e,i){if(!t||this._clipCount>0)return;const s=.5*this.transformSize(i??0);if((0,vt.oU)(t))this._processPath(t.rings,s);else if((0,vt.l9)(t))this._processPath(t.paths,s);else if((0,vt.YX)(t)){const e=_r(t);e&&this._processPath(e.rings,s)}else console.error("drawSolidStroke unexpected geometry type!")}drawMarkerLayer(t,e){(0,vt.oU)(e)&&t.markerPlacement&&("CIMMarkerPlacementInsidePolygon"===t.markerPlacement.type||"CIMMarkerPlacementPolygonCenter"===t.markerPlacement.type&&t.markerPlacement.clipAtBoundary)?this._processPath(e.rings,0):super.drawMarkerLayer(t,e)}drawHatchFill(t,e){this.drawSolidFill(t)}drawPictureFill(t,e){this.drawSolidFill(t)}drawGradientFill(t,e){this.drawSolidFill(t)}drawPictureStroke(t,e){this.drawSolidStroke(t,null,e.width)}drawGradientStroke(t,e){this.drawSolidStroke(t,null,e.width)}pushClipPath(t){this.drawSolidFill(t),this._clipCount++}popClipPath(){this._clipCount--}drawImage(t,e){const{url:i}=t,s=t.scaleX??1;let r=s*e,n=e;const o=this._resourceManager.getResource(i);!e&&(0,l.pC)(o)&&(r=s*o.width,n=o.height),this._merge(this.transformPt([-r/2,-n/2]),0),this._merge(this.transformPt([-r/2,n/2]),0),this._merge(this.transformPt([r/2,-n/2]),0),this._merge(this.transformPt([r/2,n/2]),0)}drawText(t,e){if(!e||0===e.length)return;this._textRasterizer||(this._textRasterizer=new Ds);const i=mr(t),[s,r]=this._textRasterizer.computeTextSize(e,i);let n=0;switch(t.horizontalAlignment){case"Left":n=s/2;break;case"Right":n=-s/2}let o=0;switch(t.verticalAlignment){case"Bottom":o=r/2;break;case"Top":o=-r/2;break;case"Baseline":o=r/6}this._merge(this.transformPt([-s/2+n,-r/2+o]),0),this._merge(this.transformPt([-s/2+n,r/2+o]),0),this._merge(this.transformPt([s/2+n,-r/2+o]),0),this._merge(this.transformPt([s/2+n,r/2+o]),0)}_processPath(t,e){if(t)for(const i of t){const t=i?i.length:0;if(t>1){this._merge(this.transformPt(i[0]),e);for(let s=1;s<t;s++)this._merge(this.transformPt(i[s]),e)}}}_merge(t,e){t[0]-e<this._xmin&&(this._xmin=t[0]-e),t[0]+e>this._xmax&&(this._xmax=t[0]+e),t[1]-e<this._ymin&&(this._ymin=t[1]-e),t[1]+e>this._ymax&&(this._ymax=t[1]+e)}}class ur extends lr{constructor(t,e,i,s){super(e,i),this._applyAdditionalRenderProps=s,this._colorSubstitutionHelper=new Os,this._ctx=t}drawSolidFill(t,e){if(!t)return;if((0,vt.oU)(t))this._buildPath(t.rings,!0);else if((0,vt.l9)(t))this._buildPath(t.paths,!0);else if((0,vt.YX)(t))this._buildPath(_r(t).rings,!0);else{if(!(0,vt.aW)(t))return;console.log("CanvasDrawHelper.drawSolidFill - No implementation!")}const i=this._ctx;i.fillStyle="string"==typeof e?e:"rgba("+Math.round(e[0])+","+Math.round(e[1])+","+Math.round(e[2])+","+(e[3]??255)/255+")",i.fill("evenodd")}drawSolidStroke(t,e,i,s,r,n){if(!t||!e||0===i)return;if((0,vt.oU)(t))this._buildPath(t.rings,!0);else if((0,vt.l9)(t))this._buildPath(t.paths,!1);else{if(!(0,vt.YX)(t))return void console.log("CanvasDrawHelper.drawSolidStroke isn't implemented!");this._buildPath(_r(t).rings,!0)}const o=this._ctx;o.strokeStyle="string"==typeof e?e:"rgba("+Math.round(e[0])+","+Math.round(e[1])+","+Math.round(e[2])+","+(e[3]??255)/255+")",o.lineWidth=Math.max(this.transformSize(i),.5),this._setCapStyle(s),this._setJoinStyle(r),o.miterLimit=n,o.stroke()}pushClipPath(t){if(this._ctx.save(),(0,vt.oU)(t))this._buildPath(t.rings,!0);else if((0,vt.l9)(t))this._buildPath(t.paths,!0);else{if(!(0,vt.YX)(t))return;this._buildPath(_r(t).rings,!0)}this._ctx.clip("evenodd")}popClipPath(){this._ctx.restore()}drawImage(t,e){const{colorSubstitutions:i,url:s,tintColor:r}=t,n=t.scaleX??1,o=this._resourceManager.getResource(s);if((0,l.Wi)(o))return;let a=e*(o.width/o.height),h=e;e||(a=o.width,h=o.height);const c=nr(s)||"src"in o&&nr(o.src);let u="getFrame"in o?Ws(o):o;i&&(u=this._colorSubstitutionHelper.applyColorSubstituition(u,i)),this._applyAdditionalRenderProps&&!c&&r&&(u=this._colorSubstitutionHelper.tintImageData(u,r));const f=this.transformPt([0,0]),[_,m]=this.getTransformAngle(),d=this.transformSize(1),p=this._ctx;p.save(),p.setTransform({m11:n*d*_,m12:n*d*m,m21:-d*m,m22:d*_,m41:f[0],m42:f[1]}),p.drawImage(u,-a/2,-h/2,a,h),p.restore()}drawText(t,e){if(!e||0===e.length)return;this._textRasterizer||(this._textRasterizer=new Ds);const i=mr(t);i.size*=this.transformSize((0,G.Wz)(1));const s=this._textRasterizer.rasterizeText(e,i);if(!s)return;const{size:r,anchorX:n,anchorY:o,canvas:a}=s,h=r[0]*(n+.5),l=r[1]*(o-.5),c=this._ctx,u=this.transformPt([0,0]),[f,_]=this.getTransformAngle();c.save(),c.setTransform({m11:1*f,m12:1*_,m21:-1*_,m22:1*f,m41:u[0]-1*h,m42:u[1]+1*l}),c.drawImage(a,0,0),c.restore()}drawPictureFill(t,e){if(!t)return;let{colorSubstitutions:i,height:s,offsetX:r,offsetY:n,rotation:o,scaleX:a,tintColor:h,url:c}=e;const u=this._resourceManager.getResource(c);if((0,l.Wi)(u))return;if((0,vt.oU)(t))this._buildPath(t.rings,!0);else if((0,vt.l9)(t))this._buildPath(t.paths,!0);else if((0,vt.YX)(t))this._buildPath(_r(t).rings,!0);else{if(!(0,vt.aW)(t))return;console.log("CanvasDrawHelper.drawPictureFill - No implementation!")}const f=this._ctx,_=nr(c)||"src"in u&&nr(u.src);let m,d="getFrame"in u?Ws(u):u;if(i&&(d=this._colorSubstitutionHelper.applyColorSubstituition(d,i)),this._applyAdditionalRenderProps){_||h&&(d=this._colorSubstitutionHelper.tintImageData(d,h)),m=f.createPattern(d,"repeat");const t=this.transformSize(1);o||(o=0),r?r*=t:r=0,n?n*=t:n=0,s&&(s*=t);const e=s?s/u.height:1,i=a&&s?a*s/u.width:1;if(0!==o||1!==e||1!==i||0!==r||0!==n){const t=new DOMMatrix;t.rotateSelf(0,0,-o).translateSelf(r,n).scaleSelf(i,e,1),m.setTransform(t)}}else m=f.createPattern(d,"repeat");f.save(),f.fillStyle=m,f.fill("evenodd"),f.restore()}drawPictureStroke(t,e){if(!t)return;let{colorSubstitutions:i,capStyle:s,joinStyle:r,miterLimit:n,tintColor:o,url:a,width:h}=e;const c=this._resourceManager.getResource(a);if((0,l.Wi)(c))return;let u;if((0,vt.oU)(t))u=t.rings;else if((0,vt.l9)(t))u=t.paths;else{if(!(0,vt.YX)(t))return(0,vt.aW)(t)?void console.log("CanvasDrawHelper.drawPictureStroke - No implementation!"):void 0;u=_r(t).rings}h||(h=c.width);const f=nr(a)||"src"in c&&nr(c.src);let _="getFrame"in c?Ws(c):c;i&&(_=this._colorSubstitutionHelper.applyColorSubstituition(_,i)),this._applyAdditionalRenderProps&&(f||o&&(_=this._colorSubstitutionHelper.tintImageData(_,o)));const m=Math.max(this.transformSize((0,G.F2)(h)),.5),d=m/_.width,p=this._ctx,g=p.createPattern(_,"repeat-y");let y,x;p.save(),this._setCapStyle(s),this._setJoinStyle(r),void 0!==n&&(p.miterLimit=n),p.lineWidth=m;for(let t of u)if(t=(0,Mt.d9)(t),dr(t),t&&!(t.length<=1)){y=this.transformPt(t[0]);for(let e=1;e<t.length;e++){x=this.transformPt(t[e]);const i=fr(y,x),s=new DOMMatrix;s.translateSelf(0,y[1]-m/2).scaleSelf(d,d,1).rotateSelf(0,0,90-i),g.setTransform(s),p.strokeStyle=g,p.beginPath(),p.moveTo(y[0],y[1]),p.lineTo(x[0],x[1]),p.stroke(),y=x}}p.restore()}_buildPath(t,e){const i=this._ctx;if(i.beginPath(),t)for(const s of t){const t=s?s.length:0;if(t>1){let r=this.transformPt(s[0]);i.moveTo(r[0],r[1]);for(let e=1;e<t;e++)r=this.transformPt(s[e]),i.lineTo(r[0],r[1]);e&&i.closePath()}}}_setCapStyle(t){switch(t){case it.kP.Butt:this._ctx.lineCap="butt";break;case it.kP.Round:this._ctx.lineCap="round";break;case it.kP.Square:this._ctx.lineCap="square"}}_setJoinStyle(t){switch(t){case it.r4.Bevel:this._ctx.lineJoin="bevel";break;case it.r4.Round:this._ctx.lineJoin="round";break;case it.r4.Miter:this._ctx.lineJoin="miter"}}}function fr(t,e){const i=e[0]-t[0],s=e[1]-t[1];return 180/Math.PI*Math.atan2(s,i)}const _r=t=>t?{spatialReference:t.spatialReference,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]}:null;function mr(t,e=1){const i=Qs(t),s=js(t.fontStyleName),r=Es(t.fontFamilyName),{weight:n,style:o}=s,a=e*(t.height||5),h=sr(t.horizontalAlignment),l=rr(t.verticalAlignment),c=tr(t),u=er(t.haloSymbol),f=u?e*(0|t.haloSize):0;return{color:c,size:a,horizontalAlignment:h,verticalAlignment:l,font:{family:r,style:Ks(o),weight:$s(n),decoration:i},halo:{size:f||0,color:u,style:o},pixelRatio:1,premultiplyColors:!0}}function dr(t){let e,i,s,r,n,o=t[0],a=1;for(;a<t.length;)e=t[a][0]-o[0],i=t[a][1]-o[1],r=0!==e?i/e:Math.PI/2,void 0!==s&&r-s<=1e-4?(t.splice(a-1,1),o=n):(n=o,o=t[a],a++),s=r}var pr=i(82397);function gr(t,e,i,s,r){if((0,l.Wi)(t))return null;const n=t.referencesGeometry()&&r?function(t,e,i){const{transform:s,hasZ:r,hasM:n}=i;yr.has(e)||yr.set(e,function(t){const e={};switch(t){case"esriGeometryPoint":return(t,i,s,r)=>(0,pr.U1)(i,e,t,s,r);case"esriGeometryPolygon":return(t,i,s,r)=>(0,pr.Ie)(i,e,t,s,r);case"esriGeometryPolyline":return(t,i,s,r)=>(0,pr.G6)(i,e,t,s,r);case"esriGeometryMultipoint":return(t,i,s,r)=>(0,pr.J9)(i,e,t,s,r);default:return h.Z.getLogger("esri.views.2d.support.arcadeOnDemand").error(new o.Z("mapview-arcade",`Unable to handle geometryType: ${t}`)),t=>t}}(e));const a=yr.get(e)(t.geometry,s,r,n);return{...t,geometry:a}}(e,s,r):e,a=t.repurposeFeature(n);try{return t.evaluate({...i,$feature:a})}catch(t){return h.Z.getLogger("esri.views.2d.support.arcadeOnDemand").warn("Feature arcade evaluation failed:",t),null}}const yr=new Map;i(31363);const xr=Math.PI,br=xr/2,Mr=96/72,vr=(Math.PI,h.Z.getLogger("esri.symbols.cim.CIMSymbolHelper"));function Sr(t,e,i){switch(e.type){case"CIMSymbolReference":return Sr(t,e.symbol,i);case"CIMPointSymbol":null==i&&(i={x:0,y:0}),t.drawSymbol(e,i);break;case"CIMLineSymbol":null==i&&(i={paths:[[[0,0],[10,0]]]}),t.drawSymbol(e,i);break;case"CIMPolygonSymbol":null==i&&(i={rings:[[[0,0],[0,10],[10,10],[10,0],[0,0]]]}),t.drawSymbol(e,i);break;case"CIMTextSymbol":{const i={x:0,y:0};t.drawSymbol(e,i);break}case"CIMVectorMarker":{const i=new St;t.drawMarker(e,i);break}}return t.envelope()}class wr{static getEnvelope(t,e,i){if(!t)return null;const s=new cr(i);if(Array.isArray(t)){let i;for(const r of t)i?i.union(Sr(s,r,e)):i=Sr(s,r,e);return i}return Sr(s,t,e)}static getTextureAnchor(t,e){const i=this.getEnvelope(t,null,e);if(!i)return[0,0,0];const s=(i.x+.5*i.width)*Mr,r=(i.y+.5*i.height)*Mr,n=i.width*Mr+2,o=i.height*Mr+2;return[-s/n,-r/o,o]}static rasterize(t,e,i,s,r=!0){const n=i||this.getEnvelope(e,null,s);if(!n)return[null,0,0,0,0];const o=(n.x+.5*n.width)*Mr,a=(n.y+.5*n.height)*Mr;t.width=n.width*Mr,t.height=n.height*Mr,i||(t.width+=2,t.height+=2);const h=t.getContext("2d"),l=hr.createScale(Mr,-Mr);l.translate(.5*t.width-o,.5*t.height+a);const c=new ur(h,s,l);switch(e.type){case"CIMPointSymbol":{const t={type:"point",x:0,y:0};c.drawSymbol(e,t);break}case"CIMVectorMarker":{const t=new St;c.drawMarker(e,t);break}}const u=h.getImageData(0,0,t.width,t.height),f=new Uint8Array(u.data);if(r){let t;for(let e=0;e<f.length;e+=4)t=f[e+3]/255,f[e]=f[e]*t,f[e+1]=f[e+1]*t,f[e+2]=f[e+2]*t}return[f,t.width,t.height,-o/t.width,-a/t.height]}static fromTextSymbol(t){const{angle:e,color:i,font:s,haloColor:r,haloSize:o,horizontalAlignment:a,kerning:h,text:l,verticalAlignment:c,xoffset:u,yoffset:f,backgroundColor:_,borderLineColor:m,borderLineSize:d}=t;let p,g,y,x,b,M;s&&(p=s.family,g=s.style,y=s.weight,x=s.size,b=s.decoration);let v=!1;return l&&(v=n(l)[1]),(_||d)&&(M={type:"CIMBackgroundCallout",margin:null,backgroundSymbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",color:Tr(_)},{type:"CIMSolidStroke",color:Tr(m),width:d}]},accentBarSymbol:null,gap:null,leaderLineSymbol:null,lineStyle:null}),{type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,anchorPointUnits:"Relative",dominantSizeAxis3D:"Y",size:10,billboardMode3D:"FaceNearPlane",frame:{xmin:-5,ymin:-5,xmax:5,ymax:5},markerGraphics:[{type:"CIMMarkerGraphic",geometry:{x:0,y:0},symbol:{type:"CIMTextSymbol",angle:e,blockProgression:it.zV.BTT,depth3D:1,extrapolateBaselines:!0,fontEffects:it.eZ.Normal,fontEncoding:it.DD.Unicode,fontFamilyName:p||"Arial",fontStyleName:zr(g,y),fontType:it.Ky.Unspecified,haloSize:o,height:x,hinting:it.Dd.Default,horizontalAlignment:Ir(a??"center"),kerning:h,letterWidth:100,ligatures:!0,lineGapType:"Multiple",offsetX:Zs(u),offsetY:Zs(f),strikethrough:"line-through"===b,underline:"underline"===b,symbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",enable:!0,color:Tr(i)}]},haloSymbol:{type:"CIMPolygonSymbol",symbolLayers:[{type:"CIMSolidFill",enable:!0,color:Tr(r)}]},shadowColor:[0,0,0,255],shadowOffsetX:1,shadowOffsetY:1,textCase:"Normal",textDirection:v?it.UX.RTL:it.UX.LTR,verticalAlignment:kr(c??"baseline"),verticalGlyphOrientation:it.RS.Right,wordSpacing:100,billboardMode3D:it.UR.FaceNearPlane,callout:M},textString:l}],scaleSymbolsProportionally:!0,respectFrame:!0}],scaleX:1,angleAlignment:"Display"}}static fromPictureFillSymbol(t){const{height:e,outline:i,width:s,xoffset:r,xscale:n,yoffset:o,yscale:a}=t,h=[],l={type:"CIMPolygonSymbol",symbolLayers:h};if(i){const{cap:t,join:e,miterLimit:s,width:r}=i;h.push({type:"CIMSolidStroke",color:Tr(i.color),capStyle:Cr(t),joinStyle:Lr(e),miterLimit:s,width:r})}let c=t.url;"esriPFS"===t.type&&t.imageData&&(c=t.imageData);const u="angle"in t?t.angle??0:0,f=(s??0)*(n||1),_=(e??0)*(a||1);return h.push({type:"CIMPictureFill",invertBackfaceTexture:!1,scaleX:1,textureFilter:it.Qb.Picture,tintColor:null,url:c,height:_,width:f,offsetX:Zs(r),offsetY:Zs(o),rotation:Zs(-u),colorSubstitutions:null}),l}static fromSimpleFillSymbol(t){const{color:e,style:i,outline:s}=t,r=[],n={type:"CIMPolygonSymbol",symbolLayers:r};let o=null;if(s){const{cap:t,join:e,style:i}=s;"solid"!==i&&"none"!==i&&"esriSLSSolid"!==i&&"esriSLSNull"!==i&&(o=[{type:"CIMGeometricEffectDashes",dashTemplate:Fr(i,t),lineDashEnding:"NoConstraint",scaleDash:!0,offsetAlongLine:null}]),r.push({type:"CIMSolidStroke",color:Tr(s.color),capStyle:Cr(t),joinStyle:Lr(e),miterLimit:s.miterLimit,width:s.width,effects:o})}if(i&&"solid"!==i&&"none"!==i&&"esriSFSSolid"!==i&&"esriSFSNull"!==i){const t={type:"CIMLineSymbol",symbolLayers:[{type:"CIMSolidStroke",color:Tr(e),capStyle:it.kP.Butt,joinStyle:it.r4.Miter,width:.75}]};let s=0;const n=(0,G.Wz)(Nr(i)?8:10);switch(i){case"vertical":case"esriSFSVertical":s=90;break;case"forward-diagonal":case"esriSFSForwardDiagonal":case"diagonal-cross":case"esriSFSDiagonalCross":s=-45;break;case"backward-diagonal":case"esriSFSBackwardDiagonal":s=45;break;case"cross":case"esriSFSCross":s=0}r.push({type:"CIMHatchFill",lineSymbol:t,offsetX:0,offsetY:0,rotation:s,separation:n}),"cross"===i||"esriSFSCross"===i?r.push({type:"CIMHatchFill",lineSymbol:(0,Mt.d9)(t),offsetX:0,offsetY:0,rotation:90,separation:n}):"diagonal-cross"!==i&&"esriSFSDiagonalCross"!==i||r.push({type:"CIMHatchFill",lineSymbol:(0,Mt.d9)(t),offsetX:0,offsetY:0,rotation:45,separation:n})}else!i||"solid"!==i&&"esriSFSSolid"!==i||r.push({type:"CIMSolidFill",enable:!0,color:Tr(e)});return n}static fromSimpleLineSymbol(t){const{cap:e,color:i,join:s,marker:r,miterLimit:n,style:o,width:a}=t;let h=null;"solid"!==o&&"none"!==o&&"esriSLSSolid"!==o&&"esriSLSNull"!==o&&(h=[{type:"CIMGeometricEffectDashes",dashTemplate:Fr(o,e),lineDashEnding:"NoConstraint",scaleDash:!0,offsetAlongLine:null}]);const l=[];if(r){let t;switch(r.placement){case"begin-end":t=it.Tx.Both;break;case"begin":t=it.Tx.JustBegin;break;case"end":t=it.Tx.JustEnd;break;default:t=it.Tx.None}const e=wr.fromSimpleMarker(r,a,i).symbolLayers[0];e.markerPlacement={type:"CIMMarkerPlacementAtExtremities",angleToLine:!0,offset:0,extremityPlacement:t,offsetAlongLine:0},l.push(e)}return"none"!==o&&"esriSLSNull"!==o&&l.push({type:"CIMSolidStroke",color:Tr(i),capStyle:Cr(e),joinStyle:Lr(s),miterLimit:n,width:a,effects:h}),{type:"CIMLineSymbol",symbolLayers:l}}static fromPictureMarker(t){const{angle:e,height:i,width:s,xoffset:r,yoffset:n}=t;let o=t.url;return"esriPMS"===t.type&&t.imageData&&(o=t.imageData),{type:"CIMPointSymbol",symbolLayers:[{type:"CIMPictureMarker",invertBackfaceTexture:!1,scaleX:1,textureFilter:it.Qb.Picture,tintColor:null,url:o,size:i,width:s,offsetX:Zs(r),offsetY:Zs(n),rotation:Zs(-e)}]}}static fromSimpleMarker(t,e,i){const{style:s}=t,r=t.color??i;if("path"===s){const e=[];if("outline"in t&&t.outline){const i=t.outline;e.push({type:"CIMSolidStroke",enable:!0,width:(0,G.F2)(Math.round((0,G.Wz)(i.width))),color:Tr(i.color)})}e.push({type:"CIMSolidFill",enable:!0,color:Tr(r),path:t.path});const[i,s]=Rr("square");return{type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,rotation:Zs(-t.angle),size:Zs(t.size||6),offsetX:Zs(t.xoffset),offsetY:Zs(t.yoffset),frame:i,markerGraphics:[{type:"CIMMarkerGraphic",geometry:s,symbol:{type:"CIMPolygonSymbol",symbolLayers:e}}]}]}}const[n,o]=Rr(s);let a;if(o&&n){const i=[];if("outline"in t&&t.outline){const e=t.outline;i.push({type:"CIMSolidStroke",enable:!0,width:null!=e.width&&e.width>.667?(0,G.F2)(Math.round((0,G.Wz)(e.width))):e.width,color:Tr(e.color)})}else!e||"line-marker"!==t.type||"cross"!==t.style&&"x"!==t.style||i.push({type:"CIMSolidStroke",enable:!0,width:e,color:Tr(r)});i.push({type:"CIMSolidFill",enable:!0,color:Tr(r)});const s={type:"CIMPolygonSymbol",symbolLayers:i};a={type:"CIMPointSymbol",symbolLayers:[{type:"CIMVectorMarker",enable:!0,rotation:Zs(-t.angle),size:Zs(t.size||6*e),offsetX:Zs(t.xoffset),offsetY:Zs(t.yoffset),frame:n,markerGraphics:[{type:"CIMMarkerGraphic",geometry:o,symbol:s}]}]}}return a}static fromCIMHatchFill(t,e){const i=e*(t.separation??4),s=i/2,r=(0,Mt.d9)(t.lineSymbol);r.symbolLayers?.forEach((t=>{switch(t.type){case"CIMSolidStroke":null!=t.width&&(t.width*=e),t.effects?.forEach((t=>{"CIMGeometricEffectDashes"===t.type&&(t.dashTemplate=t.dashTemplate.map((t=>t*e)))}));break;case"CIMVectorMarker":{null!=t.size&&(t.size*=e);const i=t.markerPlacement;null!=i&&"placementTemplate"in i&&(i.placementTemplate=i.placementTemplate.map((t=>t*e)));break}}}));let n=this._getLineSymbolPeriod(r)||4;for(;n<4;)n*=2;const o=n/2;return{type:"CIMVectorMarker",frame:{xmin:-o,xmax:o,ymin:-s,ymax:s},markerGraphics:[{type:"CIMMarkerGraphic",geometry:{paths:[[[-o,0],[o,0]]]},symbol:r}],size:i}}static fetchResources(t,e,i){if(t&&e)switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":{const s=t.symbolLayers;if(!s)return;for(const t of s)switch(Wr(t,e,i),t.type){case"CIMPictureFill":case"CIMHatchFill":case"CIMGradientFill":case"CIMPictureStroke":case"CIMGradientStroke":case"CIMCharacterMarker":case"CIMPictureMarker":"url"in t&&t.url&&i.push(e.fetchResource(t.url,null));break;case"CIMVectorMarker":{const s=t.markerGraphics;if(!s)continue;for(const t of s)if(t){const s=t.symbol;s&&wr.fetchResources(s,e,i)}}}}}}static _getLineSymbolPeriod(t){if(t){const e=this._getEffectsRepeat(t.effects);if(e)return e;if(t.symbolLayers)for(const e of t.symbolLayers)if(e){const t=this._getEffectsRepeat(e.effects);if(t)return t;switch(e.type){case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":case"CIMObjectMarker3D":case"CIMglTFMarker3D":{const t=this._getPlacementRepeat(e.markerPlacement);if(t)return t}}}}return 0}static _getEffectsRepeat(t){if(t)for(const e of t)if(e)switch(e.type){case"CIMGeometricEffectDashes":{const t=e.dashTemplate;if(t&&t.length){let e=0;for(const i of t)e+=i;return 1&t.length&&(e*=2),e}break}case"CIMGeometricEffectWave":return e.period;default:vr.error(`unsupported geometric effect type ${e.type}`)}return 0}static _getPlacementRepeat(t){if(t)switch(t.type){case"CIMMarkerPlacementAlongLineSameSize":case"CIMMarkerPlacementAlongLineRandomSize":case"CIMMarkerPlacementAlongLineVariableSize":{const e=t.placementTemplate;if(e&&e.length){let t=0;for(const i of e)t+=+i;return 1&e.length&&(t*=2),t}break}}return 0}static fromCIMInsidePolygon(t){const e=t.markerPlacement,i={...t};i.markerPlacement=null,i.anchorPoint=null;const s=Math.abs(e.stepX),r=Math.abs(e.stepY),n=(e.randomness??100)/100;let o,a,h,l;if("Random"===e.gridType){const t=(0,G.Wz)(A.C1),i=Math.max(Math.floor(t/s),1),c=Math.max(Math.floor(t/r),1);o=i*s/2,a=c*r/2,h=2*a;const u=new Ne.Z(e.seed),f=n*s/1.5,_=n*r/1.5;l=[];for(let t=0;t<i;t++)for(let e=0;e<c;e++){const i=t*s-o+f*(.5-u.getFloat()),n=e*r-a+_*(.5-u.getFloat());l.push({x:i,y:n}),0===t&&l.push({x:i+2*o,y:n}),0===e&&l.push({x:i,y:n+2*a})}}else!0===e.shiftOddRows?(o=s/2,a=r,h=2*r,l=[{x:-o,y:0},{x:o,y:0},{x:0,y:a},{x:0,y:-a}]):(o=s/2,a=r/2,h=r,l=[{x:-s,y:0},{x:0,y:-r},{x:-s,y:-r},{x:0,y:0},{x:s,y:0},{x:0,y:r},{x:s,y:r},{x:-s,y:r},{x:s,y:-r}]);return{type:"CIMVectorMarker",frame:{xmin:-o,xmax:o,ymin:-a,ymax:a},markerGraphics:l.map((t=>({type:"CIMMarkerGraphic",geometry:t,symbol:{type:"CIMPointSymbol",symbolLayers:[i]}}))),size:h}}static getSize(t){if(t)switch(t.type){case"CIMTextSymbol":return t.height;case"CIMPointSymbol":{let e=0;if(t.symbolLayers)for(const i of t.symbolLayers)if(i)switch(i.type){case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":case"CIMObjectMarker3D":case"CIMglTFMarker3D":{const t=i.size;null!=t&&t>e&&(e=t);break}}return e}case"CIMLineSymbol":case"CIMPolygonSymbol":{let e=0;if(t.symbolLayers)for(const i of t.symbolLayers)if(i)switch(i.type){case"CIMSolidStroke":case"CIMPictureStroke":case"CIMGradientStroke":{const t=i.width;null!=t&&t>e&&(e=t);break}case"CIMCharacterMarker":case"CIMPictureMarker":case"CIMVectorMarker":case"CIMObjectMarker3D":case"CIMglTFMarker3D":if(i.markerPlacement&&Js(i.markerPlacement)){const t=i.size;null!=t&&t>e&&(e=t)}}return e}}}static getMarkerScaleRatio(t){if(t&&"CIMVectorMarker"===t.type&&!1!==t.scaleSymbolsProportionally&&t.frame&&null!=t.size){const e=t.frame.ymax-t.frame.ymin;return t.size/e}return 1}}class Pr{static findApplicableOverrides(t,e,i){if(t&&e){if(t.primitiveName){let s=!1;for(const e of i)if(e.primitiveName===t.primitiveName){s=!0;break}if(!s)for(const s of e)s.primitiveName===t.primitiveName&&i.push(s)}switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":if(t.effects)for(const s of t.effects)Pr.findApplicableOverrides(s,e,i);if(t.symbolLayers)for(const s of t.symbolLayers)Pr.findApplicableOverrides(s,e,i);break;case"CIMTextSymbol":break;case"CIMSolidStroke":case"CIMPictureStroke":case"CIMGradientStroke":case"CIMSolidFill":case"CIMPictureFill":case"CIMHatchFill":case"CIMGradientFill":case"CIMVectorMarker":case"CIMCharacterMarker":case"CIMPictureMarker":if(t.effects)for(const s of t.effects)Pr.findApplicableOverrides(s,e,i);if(t.markerPlacement&&Pr.findApplicableOverrides(t.markerPlacement,e,i),"CIMVectorMarker"===t.type){if(t.markerGraphics)for(const s of t.markerGraphics)Pr.findApplicableOverrides(s,e,i),Pr.findApplicableOverrides(s.symbol,e,i)}else"CIMCharacterMarker"===t.type?Pr.findApplicableOverrides(t.symbol,e,i):"CIMHatchFill"===t.type?Pr.findApplicableOverrides(t.lineSymbol,e,i):"CIMPictureMarker"===t.type&&Pr.findApplicableOverrides(t.animatedSymbolProperties,e,i)}}}static findEffectOverrides(t,e,i){if(!e||!t)return;const s=t.length;for(let r=0;r<s;r++){const s=t[r]?.primitiveName;if(s){let t=!1;for(const e of i)if(e.primitiveName===s){t=!0;break}if(!t)for(const t of e)t.primitiveName===s&&i.push(t)}}}static async resolveSymbolOverrides(t,e,i,s,r,n,o){if(!t||!t.symbol)return null;let{symbol:a,primitiveOverrides:h}=t;const l=!!h;if(!l&&!s)return a;a=(0,Mt.d9)(a);let c=!0;if(e||(e={attributes:{}},c=!1),l){if(c||(h=h.filter((t=>!t.valueExpressionInfo?.expression.includes("$feature")))),o||(h=h.filter((t=>!t.valueExpressionInfo?.expression.includes("$view")))),h.length>0){const t=function(t){return(t?Object.keys(t):[]).map((e=>({name:e,alias:e,type:"string"==typeof t[e]?"esriFieldTypeString":"esriFieldTypeDouble"})))}(e.attributes);await Pr.evaluateOverrides(h,e,{spatialReference:i,fields:t,geometryType:r},n,o)}Pr.applyOverrides(a,h)}return s&&Pr.applyDictionaryTextOverrides(a,e,s),a}static async evaluateOverrides(t,e,i,s,r){if(!e)return;let n;for(const o of t){const t=o.valueExpressionInfo;if(t&&i&&i.geometryType){n||(n=[]),o.value=void 0;const a=(0,Rs.Yi)(t.expression,i.spatialReference,i.fields).then((t=>{o.value=gr(t,e,{$view:r},i.geometryType,s)}));n.push(a)}}void 0!==n&&n.length>0&&await Promise.all(n)}static applyDictionaryTextOverrides(t,e,i,s="Normal"){if(t&&t.type)switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":case"CIMTextSymbol":{const r=t.symbolLayers;if(!r)return;for(const n of r)n&&"CIMVectorMarker"===n.type&&Pr.applyDictionaryTextOverrides(n,e,i,"CIMTextSymbol"===t.type?t.textCase:s)}break;case"CIMVectorMarker":{const s=t.markerGraphics;if(!s)return;for(const t of s)t&&Pr.applyDictionaryTextOverrides(t,e,i)}break;case"CIMMarkerGraphic":{const r=t.textString;if(r&&r.includes("[")){const n=Gs(r,i);t.textString=Xs(e,n,s)}}}}static applyOverrides(t,e,i,s){if(t.primitiveName)for(const r of e)if(r.primitiveName===t.primitiveName){const e=Or(r.propertyName);if(s&&s.push({cim:t,nocapPropertyName:e,value:t[e]}),r.expression&&(r.value=Pr.toValue(r.propertyName,r.expression)),i){let e=!1;for(const s of i)s.primitiveName===t.primitiveName&&(e=!0);e||i.push(r)}(0,l.pC)(r.value)&&(t[e]=r.value)}switch(t.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":if(t.effects)for(const r of t.effects)Pr.applyOverrides(r,e,i,s);if(t.symbolLayers)for(const r of t.symbolLayers)Pr.applyOverrides(r,e,i,s);break;case"CIMTextSymbol":break;case"CIMSolidStroke":case"CIMSolidFill":case"CIMVectorMarker":if(t.effects)for(const r of t.effects)Pr.applyOverrides(r,e,i,s);if("CIMVectorMarker"===t.type&&t.markerGraphics)for(const r of t.markerGraphics)Pr.applyOverrides(r,e,i,s),Pr.applyOverrides(r.symbol,e,i,s)}}static restoreOverrides(t){for(const e of t)e.cim[e.nocapPropertyName]=e.value}static buildOverrideKey(t){let e="";for(const i of t)void 0!==i.value&&(e+=`${i.primitiveName}${i.propertyName}${JSON.stringify(i.value)}`);return e}static toValue(t,e){if("DashTemplate"===t)return e.split(" ").map((t=>Number(t)));if("Color"===t){const t=new As.Z(e).toRgba();return t[3]*=255,t}return e}}const Cr=t=>{if(!t)return it.kP.Butt;switch(t){case"butt":return it.kP.Butt;case"square":return it.kP.Square;case"round":return it.kP.Round}},Lr=t=>{if(!t)return it.r4.Miter;switch(t){case"miter":return it.r4.Miter;case"round":return it.r4.Round;case"bevel":return it.r4.Bevel}},Ir=t=>{if((0,l.Wi)(t))return"Center";switch(t){case"left":return"Left";case"right":return"Right";case"center":return"Center"}},kr=t=>{if((0,l.Wi)(t))return"Center";switch(t){case"baseline":return"Baseline";case"top":return"Top";case"middle":return"Center";case"bottom":return"Bottom"}},Tr=t=>{if(!t)return[0,0,0,0];const{r:e,g:i,b:s,a:r}=t;return[e,i,s,255*r]},zr=(t,e)=>{const i=Ar(e),s=Er(t);return i&&s?`${i}-${s}`:`${i}${s}`},Ar=t=>{if(!t)return"";switch(t.toLowerCase()){case"bold":case"bolder":return"bold"}return""},Er=t=>{if(!t)return"";switch(t.toLowerCase()){case"italic":case"oblique":return"italic"}return""},Fr=(t,e)=>{const i="butt"===e;switch(t){case"dash":case"esriSLSDash":return i?[4,3]:[3,4];case"dash-dot":case"esriSLSDashDot":return i?[4,3,1,3]:[3,4,0,4];case"dot":case"esriSLSDot":return i?[1,3]:[0,4];case"long-dash":case"esriSLSLongDash":return i?[8,3]:[7,4];case"long-dash-dot":case"esriSLSLongDashDot":return i?[8,3,1,3]:[7,4,0,4];case"long-dash-dot-dot":case"esriSLSDashDotDot":return i?[8,3,1,3,1,3]:[7,4,0,4,0,4];case"short-dash":case"esriSLSShortDash":return i?[4,1]:[3,2];case"short-dash-dot":case"esriSLSShortDashDot":return i?[4,1,1,1]:[3,2,0,2];case"short-dash-dot-dot":case"esriSLSShortDashDotDot":return i?[4,1,1,1,1,1]:[3,2,0,2,0,2];case"short-dot":case"esriSLSShortDot":return i?[1,1]:[0,2];case"solid":case"esriSLSSolid":case"none":return vr.error("Unexpected: style does not require rasterization"),[0,0];default:return vr.error(`Tried to rasterize SLS, but found an unexpected style: ${t}!`),[0,0]}},Rr=t=>{const e=50;let i,s;const r=t;if("circle"===r||"esriSMSCircle"===r){const t=.25;let r=Math.acos(1-t/e),n=Math.ceil(xr/r/4);0===n&&(n=1),r=br/n,n*=4;const o=[];o.push([e,0]);for(let t=1;t<n;t++)o.push([e*Math.cos(t*r),-e*Math.sin(t*r)]);o.push([e,0]),i={rings:[o]},s={xmin:-e,ymin:-e,xmax:e,ymax:e}}else if("cross"===r||"esriSMSCross"===r){const t=0;i={rings:[[[t,e],[t,t],[e,t],[e,-t],[t,-t],[t,-e],[-t,-e],[-t,-t],[-e,-t],[-e,t],[-t,t],[-t,e],[t,e]]]},s={xmin:-e,ymin:-e,xmax:e,ymax:e}}else if("diamond"===r||"esriSMSDiamond"===r)i={rings:[[[-e,0],[0,e],[e,0],[0,-e],[-e,0]]]},s={xmin:-e,ymin:-e,xmax:e,ymax:e};else if("square"===r||"esriSMSSquare"===r)i={rings:[[[-e,-e],[-e,e],[e,e],[e,-e],[-e,-e]]]},s={xmin:-e,ymin:-e,xmax:e,ymax:e};else if("x"===r||"esriSMSX"===r){const t=0;i={rings:[[[0,t],[e-t,e],[e,e-t],[t,0],[e,t-e],[e-t,-e],[0,-t],[t-e,-e],[-e,t-e],[-t,0],[-e,e-t],[t-e,e],[0,t]]]},s={xmin:-e,ymin:-e,xmax:e,ymax:e}}else if("triangle"===r||"esriSMSTriangle"===r){const t=57.735026918962575,e=-t,r=2/3*100,n=r-100;i={rings:[[[e,n],[0,r],[t,n],[e,n]]]},s={xmin:e,ymin:n,xmax:t,ymax:r}}else"arrow"===r&&(i={rings:[[[-50,50],[50,0],[-50,-50],[-33,-20],[-33,20],[-50,50]]]},s={xmin:-e,ymin:-e,xmax:e,ymax:e});return[s,i]},Nr=t=>"vertical"===t||"horizontal"===t||"cross"===t||"esriSFSCross"===t||"esriSFSVertical"===t||"esriSFSHorizontal"===t,Or=t=>t?t.charAt(0).toLowerCase()+t.substr(1):t;function Wr(t,e,s){t.effects&&!(0,l.pC)(e.geometryEngine)&&(e.geometryEnginePromise?s.push(e.geometryEnginePromise):(t=>{if(!t)return!1;for(const e of t)switch(e.type){case"CIMGeometricEffectBuffer":case"CIMGeometricEffectOffset":case"CIMGeometricEffectDonut":return!0}return!1})(t.effects)&&(e.geometryEnginePromise=Promise.all([i.e(5837),i.e(247)]).then(i.bind(i,30247)),s.push(e.geometryEnginePromise),e.geometryEnginePromise.then((t=>e.geometryEngine=t))))}const Vr=new Set(["StartTimeOffset","Duration","RepeatDelay"]);function Dr(t,e){return Vr.has(e)?(i=t,.05*Math.max(Math.round(i/.05),1)):t;var i}const Br=[1/256,1/65536,1/16777216,1/4294967296];function Ur(t){let e=1/0,i=-1/0,s=1/0,r=-1/0;for(const n of t)for(const t of n)t[0]<e&&(e=t[0]),t[0]>i&&(i=t[0]),t[1]<s&&(s=t[1]),t[1]>r&&(r=t[1]);return[e,s,i,r]}function Gr(t){return t?t.rings?Ur(t.rings):t.paths?Ur(t.paths):(0,vt.YX)(t)?[t.xmin,t.ymin,t.xmax,t.ymax]:null:null}function Xr(t,e,i,s,r){const[n,o,a,h]=t;if(a<n||h<o)return[0,0,0];const l=a-n,c=h-o,u=Math.floor(31.5),f=(128-2*(u+1))/Math.max(l,c),_=Math.round(l*f)+2*u,m=Math.round(c*f)+2*u;let d=1;e&&(d=m/f/(e.ymax-e.ymin));let p=0,g=0,y=1;s&&(r?e&&i&&e.ymax-e.ymin>0&&(y=(e.xmax-e.xmin)/(e.ymax-e.ymin),p=s.x/(i*y),g=s.y/i):(p=s.x,g=s.y)),e&&(p=.5*(e.xmax+e.xmin)+p*(e.xmax-e.xmin),g=.5*(e.ymax+e.ymin)+g*(e.ymax-e.ymin)),p-=n,g-=o,p*=f,g*=f,p+=u,g+=u;let x=p/_-.5,b=g/m-.5;return r&&i&&(x*=i*y,b*=i),[d,x,b]}!function(t,e=0){let i=0;for(let s=0;s<4;s++)i+=t[e+s]*Br[s]}(new Uint8ClampedArray([255,255,255,255]));const Hr=53290320,Yr=10,Jr=h.Z.getLogger("esri.symbols.cim.cimAnalyzer");function Zr(t){switch(t){case"Butt":return it.RL.BUTT;case"Square":return it.RL.SQUARE;default:return it.RL.ROUND}}function qr(t){switch(t){case"Bevel":return it.AH.BEVEL;case"Miter":return it.AH.MITER;default:return it.AH.ROUND}}function Kr(t,e,i,s){let r;t[e]?r=t[e]:(r={},t[e]=r),r[i]=s}function $r(t){const e=t.markerPlacement;return e&&e.angleToLine?it.v2.MAP:it.v2.SCREEN}function jr(t,e,i,s,r,n){const o=t.primitiveName,a=Ys(t.color),[h,l]=xn(s,o,e,null,null),c=(0,Fs.hP)(JSON.stringify(t)+l).toString();n.push({type:"fill",templateHash:c,materialHash:h?()=>c:c,cim:t,materialOverrides:null,colorLocked:!!t.colorLocked,color:mn(o,i,"Color",r,a,_n),height:0,angle:0,offsetX:0,offsetY:0,scaleX:1,effects:e,applyRandomOffset:!1,sampleAlphaOnly:!0})}function Qr(t,e,i,s,r,n,o){const a=t.primitiveName,h=qs(t),[c,u]=xn(s,a,e,null,null),f=(0,Fs.hP)(JSON.stringify(t)+u).toString(),_=(0,Fs.hP)(`${t.url}${JSON.stringify(t.colorSubstitutions)}`).toString();let m=Zs(t.scaleX);if("width"in t&&"number"==typeof t.width){const e=t.width;let i=1;const s=n.getResource(t.url);(0,l.pC)(s)&&(i=s.width/s.height),m/=i*(t.height/e)}o.push({type:"fill",templateHash:f,materialHash:c?()=>_:_,cim:t,materialOverrides:null,colorLocked:!!t.colorLocked,effects:e,color:mn(a,i,"TintColor",r,h,_n),height:mn(a,i,"Height",r,t.height),scaleX:mn(a,i,"ScaleX",r,m),angle:mn(a,i,"Rotation",r,Zs(t.rotation)),offsetX:mn(a,i,"OffsetX",r,Zs(t.offsetX)),offsetY:mn(a,i,"OffsetY",r,Zs(t.offsetY)),url:t.url,applyRandomOffset:!1,sampleAlphaOnly:!1})}function tn(t,e,i,s,r,n){const o=["Rotation","OffsetX","OffsetY"],a=s.filter((e=>e.primitiveName!==t.primitiveName||!o.includes(e.propertyName))),h=t.primitiveName;let[l,c]=xn(s,h,e,null,null);const u=(0,Fs.hP)(JSON.stringify(t)+c).toString(),f=(0,Fs.hP)(`${t.separation}${JSON.stringify(t.lineSymbol)}`).toString();let _={r:255,g:255,b:255,a:1},m=!1;const d=t.lineSymbol?.symbolLayers?.find((t=>"CIMSolidStroke"===t.type&&null!=i[t.primitiveName]?.Color));if(d){_=Ys(d.color),_=mn(d.primitiveName,i,"Color",r,_,_n);const t="function"==typeof _;l=l||t,m=null!=d.color||t}n.push({type:"fill",templateHash:u,materialHash:l?yn(f,i,a,r):f,cim:t,materialOverrides:a,colorLocked:!!t.colorLocked,effects:e,color:_,height:mn(h,i,"Separation",r,t.separation),scaleX:1,angle:mn(h,i,"Rotation",r,Zs(t.rotation)),offsetX:mn(h,i,"OffsetX",r,Zs(t.offsetX)),offsetY:mn(h,i,"OffsetY",r,Zs(t.offsetY)),applyRandomOffset:!1,sampleAlphaOnly:!0,hasUnresolvedReplacementColor:!m})}function en(t,e,i,s,r,n){const o=t.primitiveName,[a,h]=xn(s,o,e,null,null),l=(0,Fs.hP)(JSON.stringify(t)+h).toString();n.push({type:"fill",templateHash:l,materialHash:a?yn(l,i,s,r):l,cim:t,materialOverrides:null,colorLocked:!!t.colorLocked,effects:e,color:{r:128,g:128,b:128,a:1},height:0,angle:0,offsetX:0,offsetY:0,scaleX:1,applyRandomOffset:!1,sampleAlphaOnly:!1})}function sn(t,e,i,s,r,n,o,a){const h=t.primitiveName,l=Ys(t.color),c=null!=t.width?t.width:4,u=Zr(t.capStyle),f=qr(t.joinStyle),_=t.miterLimit,[m,d]=xn(s,h,e,null,null),p=(0,Fs.hP)(JSON.stringify(t)+d).toString();let g,y;if(e&&e instanceof Array&&e.length>0){const t=e[e.length-1];if("CIMGeometricEffectDashes"===t.type&&"NoConstraint"===t.lineDashEnding&&null===t.offsetAlongLine){const t=(e=[...e]).pop();g=t.dashTemplate,y=t.scaleDash}}n.push({type:"line",templateHash:p,materialHash:m?()=>p:p,cim:t,materialOverrides:null,isOutline:o,colorLocked:!!t.colorLocked,effects:e,color:mn(h,i,"Color",r,l,_n),width:mn(h,i,"Width",r,c),cap:mn(h,i,"CapStyle",r,u),join:mn(h,i,"JoinStyle",r,f),miterLimit:_&&mn(h,i,"MiterLimit",r,_),referenceWidth:a,zOrder:fn(t.name),dashTemplate:g,scaleDash:y,sampleAlphaOnly:!0})}function rn(t,e,i,s,r,n,o,a){const h=(0,Fs.hP)(`${t.url}${JSON.stringify(t.colorSubstitutions)}`).toString(),l=t.primitiveName,c=qs(t),u=null!=t.width?t.width:4,f=Zr(t.capStyle),_=qr(t.joinStyle),m=t.miterLimit,[d,p]=xn(s,l,e,null,null),g=(0,Fs.hP)(JSON.stringify(t)+p).toString();n.push({type:"line",templateHash:g,materialHash:d?()=>h:h,cim:t,materialOverrides:null,isOutline:o,colorLocked:!!t.colorLocked,effects:e,color:mn(l,i,"TintColor",r,c,_n),width:mn(l,i,"Width",r,u),cap:mn(l,i,"CapStyle",r,f),join:mn(l,i,"JoinStyle",r,_),miterLimit:m&&mn(l,i,"MiterLimit",r,m),referenceWidth:a,zOrder:fn(t.name),dashTemplate:null,scaleDash:!1,url:t.url,sampleAlphaOnly:!1})}function nn(t,e,i,s,r,n,o,a){const h=t.primitiveName,l=null!=t.width?t.width:4,c=Zr(t.capStyle),u=qr(t.joinStyle),f=t.miterLimit,[_,m]=xn(s,h,e,null,null),d=(0,Fs.hP)(JSON.stringify(t)+m).toString();n.push({type:"line",templateHash:d,materialHash:_?yn(d,i,s,r):d,cim:t,materialOverrides:null,isOutline:o,colorLocked:!!t.colorLocked,effects:e,color:{r:128,g:128,b:128,a:1},width:mn(h,i,"Width",r,l),cap:mn(h,i,"CapStyle",r,c),join:mn(h,i,"JoinStyle",r,u),miterLimit:f&&mn(h,i,"MiterLimit",r,f),referenceWidth:a,zOrder:fn(t.name),dashTemplate:null,scaleDash:!1,sampleAlphaOnly:!1})}function on(t,e,i,s,r,n){const{markerPlacement:o,type:a}=t;if(!o||"CIMMarkerPlacementInsidePolygon"!==o.type)return!1;if("CIMVectorMarker"===a||"CIMPictureMarker"===a){const i=t.primitiveName;if(i){const[t,r]=xn(s,i,e,null,null);if(t)return!1}const r=o.primitiveName;if(r){const[t,i]=xn(s,r,e,null,null);if(t)return!1}if("CIMVectorMarker"===a){const{markerGraphics:e}=t;if(e)for(const t of e){const{symbol:e}=t;if("CIMPolygonSymbol"===e?.type&&e.symbolLayers){const{symbolLayers:t}=e;for(const e of t)if("CIMSolidStroke"===e.type)return!1}}}else{const{animatedSymbolProperties:e}=t;if(e)return!1}}const h=o,l=Math.abs(h.stepX),c=Math.abs(h.stepY);if(0===l||0===c)return!0;const u=["Rotation","OffsetX","OffsetY"],f=s.filter((e=>e.primitiveName!==t.primitiveName||!u.includes(e.propertyName))),_="url"in t&&"string"==typeof t.url?t.url:void 0,[m,d]=xn(s,h.primitiveName,e,null,null),p=(0,Fs.hP)(JSON.stringify(t)+d).toString();let g,y,x=null;if("Random"===o.gridType){const t=(0,G.Wz)(A.C1),e=Math.max(Math.floor(t/l),1),i=Math.max(Math.floor(t/c),1);g=c*i,x=t=>t?t*i:0,y=e*l/g}else o.shiftOddRows?(g=2*c,x=t=>t?2*t:0,y=l/c*.5):(g=c,x=null,y=l/c);const b=qs(t);return n.push({type:"fill",templateHash:p,materialHash:m?yn(p,i,f,r):p,cim:t,materialOverrides:f,colorLocked:!!t.colorLocked,effects:e,color:mn(h.primitiveName,i,"TintColor",r,b,_n),height:mn(h.primitiveName,i,"StepY",r,g,x),scaleX:y,angle:mn(h.primitiveName,i,"GridAngle",r,h.gridAngle),offsetX:mn(h.primitiveName,i,"OffsetX",r,Zs(h.offsetX)),offsetY:mn(h.primitiveName,i,"OffsetY",r,Zs(h.offsetY)),url:_,applyRandomOffset:"Random"===o.gridType,sampleAlphaOnly:!_,hasUnresolvedReplacementColor:!0}),!0}function an(t,e,i,s,r,n,o,a,h){const c=t.primitiveName,u=Zs(t.size);let f=Zs(t.scaleX,1);const _=Zs(t.rotation),m=Zs(t.offsetX),d=Zs(t.offsetY),p=qs(t),g=(0,Fs.hP)(`${t.url}${JSON.stringify(t.colorSubstitutions)}${JSON.stringify(t.animatedSymbolProperties)}`).toString(),y=gn(t.markerPlacement,s,i,r),x=function(t,e,i,s){const r=[];if(Pr.findApplicableOverrides(t,e,r),null==t||0===r.length)return t;for(const t of r)if(t.valueExpressionInfo&&s?.geometryType){const e=i[t.primitiveName]&&i[t.primitiveName][t.propertyName];e instanceof Rs.mz&&(t.fn=(t,i,r)=>gr(e,t,{$view:r},s.geometryType,i))}return(e,i,s)=>{for(const t of r)t.fn&&(t.value=t.fn(e,i,s));const n=(0,Mt.d9)(t),o=t.primitiveName;for(const t of r)if(t.primitiveName===o){const e=dn(t.propertyName);if(null!=t.value){const i=Dr(t.value,t.propertyName);i!==n[e]&&(n[e]=i)}}return n}}(t.animatedSymbolProperties,s,i,r),[b,M]=xn(s,c,e,y,x),v=(0,Fs.hP)(JSON.stringify(t)+M).toString(),S=t.anchorPoint??{x:0,y:0};if("width"in t&&"number"==typeof t.width){const e=t.width;let i=1;const s=n.getResource(t.url);(0,l.pC)(s)&&(i=s.width/s.height),f/=i*(u/e)}function w(t,e){return(0,l.pC)(x)?Bs(x,t,e):null}const P=t.animatedSymbolProperties&&!0===t.animatedSymbolProperties.randomizeStartTime?(t,e,i,s)=>{const r=function(t){return Math.floor(function(t,e){let i;if("string"==typeof t)i=(0,Fs.hP)(t+`-seed(${e})`);else{let s=12;i=t^e;do{i=107*(i>>8^i)+s|0}while(0!=--s)}return(1+i/(1<<31))/2}(t,Hr)*Yr)}(s??0),n=w(t,e);return g+`-MATERIALGROUP(${r})`+`-ASP(${JSON.stringify(n)})`}:b?(t,e)=>{const i=w(t,e);return g+`-ASP(${JSON.stringify(i)})`}:g;o.push({type:"marker",templateHash:v,materialHash:P,cim:t,materialOverrides:null,colorLocked:!!t.colorLocked,effects:e,scaleSymbolsProportionally:!1,alignment:a,size:mn(c,i,"Size",r,u),scaleX:mn(c,i,"ScaleX",r,f),rotation:mn(c,i,"Rotation",r,_),offsetX:mn(c,i,"OffsetX",r,m),offsetY:mn(c,i,"OffsetY",r,d),color:mn(c,i,"TintColor",r,p,_n),anchorPoint:{x:S.x,y:-S.y},isAbsoluteAnchorPoint:"Relative"!==t.anchorPointUnits,outlineColor:{r:0,g:0,b:0,a:0},outlineWidth:0,frameHeight:0,rotateClockwise:!!t.rotateClockwise,referenceSize:h,sizeRatio:1,markerPlacement:y,url:t.url,animatedSymbolProperties:x})}function hn(t,e,i,s,r,n,o,a,h,l){const c=t.markerGraphics;if(!c)return;let u=0;if(t.scaleSymbolsProportionally){const e=t.frame;e&&(u=e.ymax-e.ymin)}const f=gn(t.markerPlacement,s,i,r);for(const _ of c)if(_){const c=_.symbol;if(!c)continue;switch(c.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":cn(t,e,f,null,_,s,i,r,n,o,a,h,u,!!l);break;case"CIMTextSymbol":ln(t,e,f,_,i,s,r,n,a,h,u)}}}function ln(t,e,i,s,r,n,o,a,h,l,c){Pr.findApplicableOverrides(s,n,[]);const u=s.geometry;if(!("x"in u)||!("y"in u))return;const f=s.symbol,_=Qs(f),m=js(f.fontStyleName),d=Es(f.fontFamilyName);f.font={family:d,decoration:_,...m};const p=t.frame,g=u.x-.5*(p.xmin+p.xmax),y=u.y-.5*(p.ymin+p.ymax),x=t.size/c,b=t.primitiveName,M=Zs(f.height)*x,v=Zs(f.angle),S=Zs(t.offsetX)+(Zs(f.offsetX)+g)*x,w=Zs(t.offsetY)+(Zs(f.offsetY)+y)*x,P=Ys(tr(f));let C=Ys(er(f)),L=ir(f)??0;L||(C=Ys(tr(f.haloSymbol)),f.haloSize&&(L=f.haloSize*x));let I=null,k=null,T=0;if(f.callout&&"CIMBackgroundCallout"===f.callout.type){const t=f.callout;if(t.backgroundSymbol){const e=t.backgroundSymbol.symbolLayers;if(e)for(const t of e)"CIMSolidFill"===t.type?I=Ys(t.color):"CIMSolidStroke"===t.type&&(k=Ys(t.color),T=Zs(t.width))}}const[z,A]=xn(n,b,e,i,null),E=JSON.stringify(t.effects)+Number(t.colorLocked).toString()+JSON.stringify(t.anchorPoint)+t.anchorPointUnits+JSON.stringify(t.markerPlacement)+t.size.toString(),F=(0,Fs.hP)(JSON.stringify(s)+E+A).toString();let R=mn(s.primitiveName,r,"TextString",o,s.textString??"",Hs,f.textCase);if(null==R)return;const{fontStyleName:N}=f,O=d+(N?"-"+N.toLowerCase():"-regular"),W=O;"string"==typeof R&&R.includes("[")&&f.fieldMap&&(R=function(t,e,i){const s=Gs(e,t);return t=>Xs(t,s,i)}(f.fieldMap,R,f.textCase)),a.push({type:"text",templateHash:F,materialHash:z||"function"==typeof R||R.match(/\[(.*?)\]/)?(t,e,i)=>W+"-"+Bs(R,t,e,i):W+"-"+(0,Fs.hP)(R),cim:f,materialOverrides:null,colorLocked:!!t.colorLocked,effects:e,alignment:h,anchorPoint:{x:t.anchorPoint?t.anchorPoint.x:0,y:t.anchorPoint?t.anchorPoint.y:0},isAbsoluteAnchorPoint:"Relative"!==t.anchorPointUnits,fontName:O,decoration:_,weight:mn(b,r,"Weight",o,m.weight),style:mn(b,r,"Size",o,m.style),size:mn(b,r,"Size",o,M),angle:mn(b,r,"Rotation",o,v),offsetX:mn(b,r,"OffsetX",o,S),offsetY:mn(b,r,"OffsetY",o,w),horizontalAlignment:sr(f.horizontalAlignment),verticalAlignment:rr(f.verticalAlignment),text:R,color:P,outlineColor:C,outlineSize:L,backgroundColor:I,borderLineColor:k,borderLineWidth:T,referenceSize:l,sizeRatio:1,markerPlacement:i})}function cn(t,e,i,s,r,n,o,a,h,c,u,f,_,m){const d=r.symbol,p=d.symbolLayers;if(!p)return;if(m)return void un(t,e,i,s,r,o,n,a,h,c,u,f,_);let g=p.length;if(bn(p))return void function(t,e,i,s,r,n,o,a,h,c,u,f,_){const m=r.geometry,d=n[0],p=n[1],g=Gr(m);if(!g)return;const y="Relative"!==t.anchorPointUnits,[x,b,M]=Xr(g,t.frame,t.size,t.anchorPoint,y),v={type:"sdf",geom:m,asFill:!0},S=t.primitiveName,w=Zs(t.size),P=Zs(t.rotation),C=Zs(t.offsetX),L=Zs(t.offsetY),I=p.path,k=p.primitiveName,T=d.primitiveName,z=Ys(tr(p)),A=Ys(er(d)),E=ir(d)??0;let F=!1,R="";for(const t of o)t.primitiveName!==k&&t.primitiveName!==T&&t.primitiveName!==S||(void 0!==t.value?R+=`-${t.primitiveName}-${t.propertyName}-${JSON.stringify(t.value)}`:t.valueExpressionInfo&&(F=!0));(0,l.pC)(i)&&"function"==typeof i&&(F=!0);const N=JSON.stringify({...t,markerGraphics:null}),O=(0,Fs.hP)(JSON.stringify(v)+I).toString(),W={type:"marker",templateHash:(0,Fs.hP)(JSON.stringify(r)+JSON.stringify(p)+JSON.stringify(d)+N+R).toString(),materialHash:F?()=>O:O,cim:v,materialOverrides:null,colorLocked:!!t.colorLocked,effects:e,scaleSymbolsProportionally:!!t.scaleSymbolsProportionally,alignment:u,anchorPoint:{x:b,y:M},isAbsoluteAnchorPoint:y,size:mn(t.primitiveName,a,"Size",h,w),rotation:mn(t.primitiveName,a,"Rotation",h,P),offsetX:mn(t.primitiveName,a,"OffsetX",h,C),offsetY:mn(t.primitiveName,a,"OffsetY",h,L),scaleX:1,frameHeight:_,rotateClockwise:!!t.rotateClockwise,referenceSize:f,sizeRatio:x,color:mn(k,a,"Color",h,z,_n),outlineColor:mn(T,a,"Color",h,A,_n),outlineWidth:mn(T,a,"Width",h,E),markerPlacement:i,path:I,animatedSymbolProperties:s};c.push(W)}(t,e,i,s,r,p,n,o,a,h,u,f,_);const y=Fi.applyEffects(d.effects,r.geometry,c.geometryEngine);if(y)for(;g--;){const m=p[g];if(m&&!1!==m.enable)switch(m.type){case"CIMSolidFill":case"CIMSolidStroke":{const d=Fi.applyEffects(m.effects,y,c.geometryEngine),p=Gr(d);if(!p)continue;const g="Relative"!==t.anchorPointUnits,[x,b,M]=Xr(p,t.frame,t.size,t.anchorPoint,g),v="CIMSolidFill"===m.type,S={type:"sdf",geom:d,asFill:v},w=t.primitiveName,P=Zs(t.size)??10,C=Zs(t.rotation),L=Zs(t.offsetX),I=Zs(t.offsetY),k=m.path,T=m.primitiveName,z=Ys(v?tr(m):er(m)),A=v?{r:0,g:0,b:0,a:0}:Ys(er(m)),E=ir(m)??0;if(!v&&!E)break;let F=!1,R="";for(const t of n)t.primitiveName!==T&&t.primitiveName!==w||(void 0!==t.value?R+=`-${t.primitiveName}-${t.propertyName}-${JSON.stringify(t.value)}`:t.valueExpressionInfo&&(F=!0));((0,l.pC)(e)&&"function"==typeof e||(0,l.pC)(i)&&"function"==typeof i)&&(F=!0);const N=JSON.stringify({...t,markerGraphics:null}),O=(0,Fs.hP)(JSON.stringify(S)+k).toString(),W={type:"marker",templateHash:(0,Fs.hP)(JSON.stringify(r)+JSON.stringify(m)+N+R).toString(),materialHash:F?()=>O:O,cim:S,materialOverrides:null,colorLocked:!!t.colorLocked,effects:e,scaleSymbolsProportionally:!!t.scaleSymbolsProportionally,alignment:u,anchorPoint:{x:b,y:M},isAbsoluteAnchorPoint:g,size:mn(t.primitiveName,o,"Size",a,P),rotation:mn(t.primitiveName,o,"Rotation",a,C),offsetX:mn(t.primitiveName,o,"OffsetX",a,L),offsetY:mn(t.primitiveName,o,"OffsetY",a,I),scaleX:1,frameHeight:_,rotateClockwise:!!t.rotateClockwise,referenceSize:f,sizeRatio:x,color:mn(T,o,"Color",a,z,_n),outlineColor:mn(T,o,"Color",a,A,_n),outlineWidth:mn(T,o,"Width",a,E),markerPlacement:i,animatedSymbolProperties:s,path:k};h.push(W);break}default:un(t,e,i,s,r,o,n,a,h,c,u,f,_)}}}function un(t,e,i,s,r,n,o,a,h,c,u,f,_){const m=function(t,e){return{type:t.type,enable:!0,name:t.name,colorLocked:t.colorLocked,primitiveName:t.primitiveName,anchorPoint:t.anchorPoint,anchorPointUnits:t.anchorPointUnits,offsetX:0,offsetY:0,rotateClockwise:t.rotateClockwise,rotation:0,size:t.size,billboardMode3D:t.billboardMode3D,depth3D:t.depth3D,frame:t.frame,markerGraphics:[e],scaleSymbolsProportionally:t.scaleSymbolsProportionally,respectFrame:t.respectFrame,clippingPath:t.clippingPath}}(t,r),d=["Rotation","OffsetX","OffsetY"],p=o.filter((e=>e.primitiveName!==t.primitiveName||!d.includes(e.propertyName)));let g="";for(const t of o)void 0!==t.value&&(g+=`-${t.primitiveName}-${t.propertyName}-${JSON.stringify(t.value)}`);const[y,x,b]=wr.getTextureAnchor(m,c),M=t.primitiveName,v=Zs(t.rotation),S=Zs(t.offsetX),w=Zs(t.offsetY),P=(0,Fs.hP)(JSON.stringify(m)+g).toString(),C={type:"marker",templateHash:P,materialHash:p.length>0||(0,l.pC)(e)&&"function"==typeof e?yn(P,n,p,a):P,cim:m,materialOverrides:p,colorLocked:!!t.colorLocked,effects:e,scaleSymbolsProportionally:!!t.scaleSymbolsProportionally,alignment:u,anchorPoint:{x:y,y:x},isAbsoluteAnchorPoint:!1,size:Zs(t.size),rotation:mn(M,n,"Rotation",a,v),offsetX:mn(M,n,"OffsetX",a,S),offsetY:mn(M,n,"OffsetY",a,w),color:{r:255,g:255,b:255,a:1},outlineColor:{r:0,g:0,b:0,a:0},outlineWidth:0,scaleX:1,frameHeight:_,rotateClockwise:!!t.rotateClockwise,referenceSize:f,sizeRatio:b/(0,G.F2)(t.size),markerPlacement:i,animatedSymbolProperties:s,avoidSDFRasterization:!0};h.push(C)}function fn(t){if(t&&0===t.indexOf("Level_")){const e=parseInt(t.substr(6),10);if(!isNaN(e))return e}return 0}function _n(t){if(!t||0===t.length)return null;const e=new As.Z(t).toRgba();return{r:e[0],g:e[1],b:e[2],a:e[3]}}function mn(t,e,i,s,r,n,o){if(null==t)return r;const a=e[t];if(a){const t=a[i];if("string"==typeof t||"number"==typeof t||t instanceof Array)return n?n.call(null,t,o):t;if(null!=t&&t instanceof Rs.mz&&s?.geometryType)return(e,i,a)=>{let h=gr(t,e,{$view:a},s.geometryType,i);return null!==h&&n&&(h=n.call(null,h,o)),null!==h?h:r}}return r}function dn(t){return t?t.charAt(0).toLowerCase()+t.substr(1):t}function pn(t,e,i,s){for(const t of e)if(t.valueExpressionInfo&&s?.geometryType){const e=i[t.primitiveName]&&i[t.primitiveName][t.propertyName];e instanceof Rs.mz&&(t.fn=(t,i,r)=>gr(e,t,{$view:r},s.geometryType,i))}return(i,s,r)=>{for(const t of e)t.fn&&(t.value=t.fn(i,s,r));const n=[];for(let i of t){const t=i?.primitiveName;if(t){let s=!1;for(const r of e)if(r.primitiveName===t){const t=dn(r.propertyName);null!=r.value&&r.value!==i[t]&&(s||(i=(0,Mt.d9)(i),s=!0),i[t]=r.value)}}n.push(i)}return n}}function gn(t,e,i,s){const r=[];if(Pr.findApplicableOverrides(t,e,r),null==t||0===r.length)return t;for(const t of r)if(t.valueExpressionInfo&&s?.geometryType){const e=i[t.primitiveName]&&i[t.primitiveName][t.propertyName];e instanceof Rs.mz&&(t.fn=(t,i,r)=>gr(e,t,{$view:r},s.geometryType,i))}return(e,i,s)=>{for(const t of r)t.fn&&(t.value=t.fn(e,i,s));const n=(0,Mt.d9)(t),o=t.primitiveName;for(const t of r)if(t.primitiveName===o){const e=dn(t.propertyName);null!=t.value&&t.value!==n[e]&&(n[e]=t.value)}return n}}function yn(t,e,i,s){for(const t of i)if(t.valueExpressionInfo&&s?.geometryType){const i=e[t.primitiveName]&&e[t.primitiveName][t.propertyName];i instanceof Rs.mz&&(t.fn=(t,e,r)=>gr(i,t,{$view:r},s.geometryType,e))}return(e,s,r)=>{for(const t of i)t.fn&&(t.value=t.fn(e,s,r));return(0,Fs.hP)(t+Pr.buildOverrideKey(i)).toString()}}function xn(t,e,i,s,r){let n=!1,o="";for(const i of t)i.primitiveName===e&&(void 0!==i.value?o+=`-${i.primitiveName}-${i.propertyName}-${JSON.stringify(i.value)}`:i.valueExpressionInfo&&(n=!0));return(0,l.pC)(i)&&"function"==typeof i&&(n=!0),(0,l.pC)(s)&&"function"==typeof s&&(n=!0),(0,l.pC)(r)&&"function"==typeof r&&(n=!0),[n,o]}const bn=t=>t&&2===t.length&&t[0].enable&&t[1].enable&&"CIMSolidStroke"===t[0].type&&"CIMSolidFill"===t[1].type&&!t[0].effects&&!t[1].effects,Mn=h.Z.getLogger("esri.views.2d.engine.webgl.WGLDynamicMeshTemplate");class vn extends Ri{constructor(t){super(),this._ongoingMaterialRequestMap=new Map,this._materialCache=new Map,this._dynamicPropertyMap=new Map,this._cimLayer=t}analyze(t,e,i,s,r){if(r&&0===r.length)return null;const n=r&&r.length>0,o=e.readLegacyFeature(),a=e.getObjectId(),h=this._materialCache,l=this._cimLayer.materialHash;if(!l)return Mn.error("A Dynamic mesh template must have a material hash value or function!"),Promise.reject(null);const c="function"==typeof l?l(o,i,s,a):l,u=h.get(c);if(null!=u)return Promise.resolve(u);const f=this._ongoingMaterialRequestMap.get(c);if(f)return f;const _=this._cimLayer,m=function(t,e){if(!e||0===e.length)return t;const i=(0,Mt.d9)(t);return Pr.applyOverrides(i,e),i}(_.cim,this._cimLayer.materialOverrides);m.mosaicHash=c;const{type:d,url:p}=_,g={cim:m,type:d,mosaicHash:c,url:p,size:null,dashTemplate:null,text:null,fontName:null,objectId:a,animatedSymbolProperties:null};switch(d){case"marker":g.size=Bs(_.size,o,i,s),g.animatedSymbolProperties=Bs(_.animatedSymbolProperties,o,i,s);break;case"line":g.dashTemplate=_.dashTemplate;break;case"text":g.text=Bs(_.text,o,i,s),g.fontName=Bs(_.fontName,o,i,s)}const y=t.getMosaicItem(g,r).then((t=>(n||(this._ongoingMaterialRequestMap.delete(c),h.set(c,t)),t))).catch((t=>(this._ongoingMaterialRequestMap.delete(c),Mn.error(".analyze()",t.message),null)));return n||this._ongoingMaterialRequestMap.set(c,y),y}}function Sn(t,e){if(t&&"name"in t){const i=t;return e&&e.error(new o.Z(i.name,i.message,i.details)),!1}return!0}class wn extends(zs(vn)){constructor(t,e,i){if(super(t),this._minMaxZoom=(0,nt.UJ)(Math.round(e*A.MI),Math.round(i*A.MI)),ft(t.color)){const e=(e,i,s)=>{const r=t.color(e,i,s);return r&&(0,rt.t2)(r)||0};this._dynamicPropertyMap.set("fillColor",e)}else{const e=t.color;this.fillColor=e&&(0,rt.t2)(e)||0}const s="CIMMarkerPlacementInsidePolygon"===t.cim.placement?.type&&t.cim.placement.shiftOddRows?2:1,r=t.height;if(ft(r)){const t=(t,e,i)=>r(t,e,i)*s;this._dynamicPropertyMap.set("_height",t)}else this._height=(r||0)*s;const n=t.offsetX;if(ft(n)){const t=(t,e,i)=>(0,G.F2)(n(t,e,i));this._dynamicPropertyMap.set("_offsetX",t)}else this._offsetX=(0,G.F2)(n||0);const o=t.offsetY;if(ft(o)){const t=(t,e,i)=>(0,G.F2)(-o(t,e,i));this._dynamicPropertyMap.set("_offsetY",t)}else this._offsetY=(0,G.F2)(-o||0);const a=t.scaleX;ft(a)?this._dynamicPropertyMap.set("_scaleX",a):this._scaleX=a||1;const h=t.angle;if(ft(h)){const t=(t,e,i)=>Ji(h(t,e,i));this._dynamicPropertyMap.set("_angle",t)}else this._angle=Ji(h)||0;if((0,l.pC)(t.effects)){const e=t.effects;ft(e)?this._dynamicPropertyMap.set("_effects",e):this._effects=e}this._cimFillLayer=t,this._bitset=(t.colorLocked?A.Uh:0)|(t.applyRandomOffset?A.jk:0)|(t.sampleAlphaOnly?A.Iv:0)|(t.hasUnresolvedReplacementColor?A.vw:0),this._fillMaterialKey=t.materialKey}static fromCIMFill(t,e){const[i,s]=mt(t.scaleInfo,e);return new wn(t,i,s)}bindFeature(t,e,i){const s=t.readLegacyFeature();this._dynamicPropertyMap.forEach(((t,r)=>{this[r]=t(s,e,i)}));const r=ot.dk.load(this._fillMaterialKey),n=this._materialCache,o=(0,this._cimFillLayer.materialHash)(s,e,i),a=n.get(o);let h=null;if(a&&Sn(a.spriteMosaicItem)&&(h=a.spriteMosaicItem),h){const{rect:t,width:e,height:i}=h,s=t.x+A.fL,n=t.y+A.fL,o=s+e,a=n+i;let l=Math.round((0,G.F2)(this._height));l<=0&&(l=a-n);let c=Math.round((0,G.F2)(this._height/i*e||0));c<=0&&(c=o-s);const u=this._scaleX,f=1;this.tl=(0,nt.UJ)(s,n),this.br=(0,nt.UJ)(o,a),this.aux21=(0,nt.UJ)(c,l),this.aux22=(0,nt.UJ)(this._offsetX,this._offsetY),this.aux3=(0,nt.Jz)(128*u,128*f,this._angle,0),r.sdf=h.sdf,r.pattern=!0,r.textureBinding=h.textureBinding}else this.tl=0,this.br=0,this.aux21=0,this.aux22=0,this.aux3=0,r.sdf=!1,r.pattern=!1,r.textureBinding=0;this._materialKey=r.data}}class Pn extends(Cs(vn)){constructor(t,e,i){super(t),this._minMaxZoom=(0,nt.UJ)(Math.round(e*A.MI),Math.round(i*A.MI)),this._cimLineLayer=t;let s=0;ft(t.width)||(s=.5*(0,G.F2)(t.width)),this._dynamicPropertyMap.set("_halfWidth",((e,i,r)=>ft(t.width)?.5*(0,G.F2)(t.width(e,i,r)):s)),ft(t.cap)?this._dynamicPropertyMap.set("_capType",t.cap):this._capType=t.cap,ft(t.join)?this._dynamicPropertyMap.set("_joinType",t.join):this._joinType=t.join;const r=t.color;if(ft(r)){const t=(t,e,i)=>(0,rt.t2)(r(t,e,i));this._dynamicPropertyMap.set("_fillColor",t)}else this._fillColor=r&&(0,rt.t2)(r)||0;const n=t.miterLimit;if(ft(n)){const t=(t,e,i)=>_t(n(t,e,i));this._dynamicPropertyMap.set("_miterLimitCosine",t)}else this._miterLimitCosine=_t(n);if((0,l.pC)(t.effects)){const e=t.effects;ft(e)?this._dynamicPropertyMap.set("_effects",e):this._effects=e}this._scaleFactor=t.scaleFactor||1,this._isDashed=null!=t.dashTemplate;const o=t.colorLocked?A.Uh:0,a=t.scaleDash?A._6:0,h=t.sampleAlphaOnly?A.Iv:0;this.tessellationProperties._bitset=o|a|h,this._materialKey=t.materialKey,this._initializeTessellator(!0)}static fromCIMLine(t,e){const[i,s]=mt(t.scaleInfo,e);return new Pn(t,i,s)}bindFeature(t,e,i){const s=t.readLegacyFeature();this._dynamicPropertyMap.forEach(((t,r)=>{this[r]=t(s,e,i)})),this._halfWidth*=this._scaleFactor;const r=this._materialCache,n=(0,this._cimLineLayer.materialHash)(s,e,i),o=r.get(n);let a=null;if(o&&Sn(o.spriteMosaicItem)&&(a=o.spriteMosaicItem),a){this._hasPattern=!0;const{rect:t,width:e,height:i}=a,s=t.x+A.fL,r=t.y+A.fL,n=s+e,o=r+i;this.tessellationProperties._tl=(0,nt.UJ)(s,r),this.tessellationProperties._br=(0,nt.UJ)(n,o)}else this._hasPattern=!1,this.tessellationProperties._tl=0,this.tessellationProperties._br=0;this.tessellationProperties._fillColor=this._fillColor,this.tessellationProperties._halfWidth=this._halfWidth,this.tessellationProperties.offset=0,this.tessellationProperties._halfReferenceWidth=this.tessellationProperties._halfWidth;const h=ot.a.load(this._materialKey);a&&(h.sdf=a.sdf,h.pattern=!0,h.textureBinding=a.textureBinding),this._materialKey=h.data}}const Cn=(0,_i.c)(),Ln=Q();class In extends(Xi(vn)){constructor(t,e,i){super(t),this._cimMarkerLayer=t,this._minMaxZoom=(0,nt.UJ)(Math.round(e*A.MI),Math.round(i*A.MI));const s=t.color;if(ft(s)){const t=(t,e,i)=>(0,rt.t2)(s(t,e,i));this._dynamicPropertyMap.set("_fillColor",t)}else this._fillColor=(0,rt.t2)(s);const r=t.outlineColor;if(ft(r)){const t=(t,e,i)=>(0,rt.t2)(r(t,e,i));this._dynamicPropertyMap.set("_outlineColor",t)}else this._outlineColor=(0,rt.t2)(r);const n=t.size;if(ft(n)){const t=(t,e,i)=>(0,G.F2)(n(t,e,i));this._dynamicPropertyMap.set("_size",t)}else this._size=(0,G.F2)(n)||0;const o=t.scaleX;ft(o)?this._dynamicPropertyMap.set("_scaleX",o):this._scaleX=o;const a=t.offsetX;if(ft(a)){const t=(t,e,i)=>(0,G.F2)(a(t,e,i));this._dynamicPropertyMap.set("xOffset",t)}else this.xOffset=(0,G.F2)(a)||0;const h=t.offsetY;if(ft(h)){const t=(t,e,i)=>(0,G.F2)(h(t,e,i));this._dynamicPropertyMap.set("yOffset",t)}else this.yOffset=(0,G.F2)(h)||0;const c=t.outlineWidth;if(ft(c)){const t=(t,e,i)=>(0,G.F2)(c(t,e,i));this._dynamicPropertyMap.set("_outlineWidth",t)}else this._outlineWidth=(0,G.F2)(c)||0;const u=t.rotation;if(ft(u)?this._dynamicPropertyMap.set("_angle",u):this._angle=u||0,(0,l.pC)(t.effects)){const e=t.effects;ft(e)?this._dynamicPropertyMap.set("_effects",e):this._effects=e}if((0,l.pC)(t.markerPlacement)){const e=t.markerPlacement;ft(e)?this._dynamicPropertyMap.set("_markerPlacement",e):this._markerPlacement=e}this._scaleFactor=(0,l.Pt)(t.scaleFactor,1),this._bitSet=(t.alignment===it.v2.MAP?1:0)|(t.colorLocked?1:0)<<1|(t.scaleSymbolsProportionally?1:0)<<3,this._materialKey=t.materialKey}static fromCIMMarker(t,e){const[i,s]=mt(t.scaleInfo,e);return new In(t,i,s)}bindFeature(t,e,i){const s=t.readLegacyFeature(),r=t.getObjectId();this._dynamicPropertyMap.forEach(((t,r)=>{this[r]=t(s,e,i)}));const n=this._cimMarkerLayer.materialHash,a="function"==typeof n?n(s,e,i,r):n,l=this._materialCache.get(a);if(!l||!Sn(l.spriteMosaicItem)||!l.spriteMosaicItem)return void h.Z.getLogger("esri.views.2d.engine.webgl.WGLDynamicMarkerTemplate").error(new o.Z("mapview-cim","Encountered an error when binding feature"));const c=l.spriteMosaicItem,u=this._cimMarkerLayer.sizeRatio,f=c.width/c.height*this._scaleX,_=this._cimMarkerLayer.rotateClockwise?this._angle:-this._angle;let m=this._size,d=m*f;const p=this.xOffset,g=this.yOffset;this.xOffset*=this._scaleFactor,this.yOffset*=this._scaleFactor;const y=this._cimMarkerLayer.scaleSymbolsProportionally&&this._cimMarkerLayer.frameHeight?this._size/(0,G.F2)(this._cimMarkerLayer.frameHeight):1,x=this._outlineWidth*y,b=(0,G.F2)(this._cimMarkerLayer.referenceSize);let M=0,v=0;const S=this._cimMarkerLayer.anchorPoint;S&&(this._cimMarkerLayer.isAbsoluteAnchorPoint?this._size&&(M=(0,G.F2)(S.x)/(this._size*f),v=(0,G.F2)(S.y)/this._size):(M=S.x,v=S.y)),this._anchorX=M,this._anchorY=v,this._sizeOutlineWidth=(0,nt.Jz)(Math.round(Math.min(Math.sqrt(128*d),255)),Math.round(Math.min(Math.sqrt(128*m),255)),Math.round(Math.min(Math.sqrt(128*x),255)),Math.round(Math.min(Math.sqrt(128*b),255))),this.angle=_;const w=Math.round(64*u);this._bitestAndDistRatio=(0,nt.UJ)(this._bitSet,w);const P=c.rect.x+A.fL,C=c.rect.y+A.fL,L=P+c.width,I=C+c.height;this._texUpperLeft=(0,nt.UJ)(P,C),this._texUpperRight=(0,nt.UJ)(L,C),this._texBottomLeft=(0,nt.UJ)(P,I),this._texBottomRight=(0,nt.UJ)(L,I);const k=ot.mE.load(this._materialKey);k.sdf=c.sdf,k.pattern=!0,k.textureBinding=c.textureBinding,this._materialKey=k.data,d*=u,m*=u,d*=this._scaleFactor,m*=this._scaleFactor,d*=c.rect.width/c.width,m*=c.rect.height/c.height,this._computedWidth=d,this._computedHeight=m,this._applyTransformation(Ln,Cn),this.xOffset=p,this.yOffset=g}}function kn(t){if(null==t)return[];const e=new Array(t.length);for(let i=0;i<t.length;i++)e[i]=t.charCodeAt(i);return e}class Tn extends(Ei(vn)){constructor(t,e,i){super(t),this._horizontalAlignment="center",this._verticalAlignment="middle",this._textToGlyphs=new Map,this._minMaxZoom=(0,nt.UJ)(Math.round(e*A.MI),Math.round(i*A.MI));const s=t.scaleFactor||1;this._cimTextLayer=t;const r=t.color;if(ft(r)){const t=(t,e,i)=>(0,rt.t2)(r(t,e,i));this._dynamicPropertyMap.set("_color",t)}else this._color=(0,rt.t2)(r);const n=t.outlineColor;if(ft(n)){const t=(t,e,i)=>(0,rt.t2)(n(t,e,i));this._dynamicPropertyMap.set("_haloColor",t)}else this._haloColor=(0,rt.t2)(n);let o,a,h;if(ft(t.size)||(o=Math.min(Math.round((0,G.F2)(t.size*t.sizeRatio)),127)),this._dynamicPropertyMap.set("_size",((e,i,s)=>ft(t.size)?Math.min(Math.round((0,G.F2)(t.size(e,i,s)*t.sizeRatio)),127):o)),ft(t.outlineSize)){const e=(e,i,s)=>Math.min(Math.floor(5*(0,G.F2)(t.outlineSize(e,i,s)*t.sizeRatio)),127);this._dynamicPropertyMap.set("_haloSize",e)}else this._haloSize=Math.min(Math.floor(5*(0,G.F2)(t.outlineSize*t.sizeRatio)),127);if(ft(t.offsetX)||(a=Math.round((0,G.F2)(t.offsetX*t.sizeRatio))),this._dynamicPropertyMap.set("_xOffset",((e,i,s)=>ft(t.offsetX)?Math.round((0,G.F2)(t.offsetX(e,i,s)*t.sizeRatio)):a)),ft(t.offsetY)||(h=Math.round((0,G.F2)(t.offsetY*t.sizeRatio))),this._dynamicPropertyMap.set("_yOffset",((e,i,s)=>ft(t.offsetY)?Math.round((0,G.F2)(t.offsetY(e,i,s)*t.sizeRatio)):h)),ft(t.angle)?this._dynamicPropertyMap.set("_angle",t.angle):this._angle=t.angle,ft(t.horizontalAlignment)?this._dynamicPropertyMap.set("_horizontalAlignment",t.horizontalAlignment):this._horizontalAlignment=t.horizontalAlignment,ft(t.verticalAlignment)?this._dynamicPropertyMap.set("_verticalAlignment",t.verticalAlignment):this._verticalAlignment=t.verticalAlignment,(0,l.pC)(t.effects)){const e=t.effects;ft(e)?this._dynamicPropertyMap.set("_effects",e):this._effects=e}if((0,l.pC)(t.markerPlacement)){const e=t.markerPlacement;ft(e)?this._dynamicPropertyMap.set("_markerPlacement",e):this._textPlacement=e}ft(t.text)?this._dynamicPropertyMap.set("_text",t.text):this._text=t.text,this._backgroundColor=t.backgroundColor&&(0,rt.t2)(t.backgroundColor),this._borderLineColor=t.borderLineColor&&(0,rt.t2)(t.borderLineColor),this._borderLineSize=t.borderLineWidth,this._scaleFactor=s;const c=Math.min(Math.round((0,G.F2)(t.referenceSize*t.sizeRatio)),127);this._referenceSize=Math.round(Math.sqrt(256*c)),this._materialKey=t.materialKey;const u=ot.qr.load(this._materialKey);u.sdf=!0,this._bitset=(t.alignment===it.v2.MAP?1:0)|(t.colorLocked?1:0)<<1,this._materialKey=u.data,this._decoration="none",this._lineHeight=1,this._lineWidth=512,this._isCIM=!0}static fromCIMText(t,e){const[i,s]=mt(t.scaleInfo,e);return new Tn(t,i,s)}async analyze(t,e,i,s){const r=e.readLegacyFeature(),n=function(t,e,i,s){return"string"==typeof t.text?t.text:"function"==typeof t.text?t.text(e,i,s)??"":""}(this._cimTextLayer,r,i,s),o=await super.analyze(t,e,i,s,kn(n));return o&&o.glyphMosaicItems&&this._textToGlyphs.set(n,o.glyphMosaicItems),o}bindFeature(t,e,i){const s=t.readLegacyFeature();if(this._dynamicPropertyMap.forEach(((t,r)=>{this[r]=t(s,e,i)})),!this._text||0===this._text.length)return void(this._shapingInfo=null);this._size*=this._scaleFactor,this._scale=this._size/A.Ex,this._xOffset*=this._scaleFactor,this._yOffset*=this._scaleFactor,this._xAlignD=(0,st.kH)((0,l.Pt)(this._horizontalAlignment,"center")),this._yAlignD=(0,st.b7)((0,l.Pt)(this._verticalAlignment,"baseline"));const r=this._textToGlyphs.get(this._text)??[];this.bindTextInfo(r,!1)}}const zn=128;class An extends(zs(Ri)){constructor(t,e,i,s,r,n,o,a,h,l,c,u,f,_,m,d){super(),this._effects=_;const p=ot.dk.load(t);e&&(p.sdf=e.sdf,p.pattern=!0,p.textureBinding=e.textureBinding),this.fillColor=i,this.tl=s,this.br=r,this.aux21=(0,nt.UJ)(n,o),this.aux22=(0,nt.UJ)(a,h),this.aux3=(0,nt.Jz)(l,c,u,0),this._bitset=f,this._minMaxZoom=(0,nt.UJ)(Math.round(m*A.MI),Math.round(d*A.MI)),this._materialKey=p.data}static fromCIMFill(t,e,i){const s=t.color,r=s&&(0,rt.t2)(s)||0,n=t.materialKey,[o,a]=mt(t.scaleInfo,i),h=(t.colorLocked?A.Uh:0)|(t.applyRandomOffset?A.jk:0)|(t.sampleAlphaOnly?A.Iv:0)|(t.hasUnresolvedReplacementColor?A.vw:0);if(!e)return new An(n,null,r,0,0,0,0,0,0,0,0,0,h,t.effects,o,a);const{rect:l,width:c,height:u}=e,f=t.scaleX||1,_=l.x+A.fL,m=l.y+A.fL,d=_+c,p=m+u,g=(0,G.F2)(t.height);let y=f*g;"CIMHatchFill"===t.cim.type&&(y*=c/u);let x=Math.round(g);x<=0&&(x=p-m);let b=Math.round(y);b<=0&&(b=d-_);const M=(0,G.F2)(t.offsetX||0),v=(0,G.F2)(-t.offsetY||0),S=(0,nt.UJ)(_,m),w=(0,nt.UJ)(d,p);return new An(n,e,r,S,w,b,x,M,v,zn,zn,Ji(t.angle),h,t.effects,o,a)}static fromSimpleFill(t,e,i=!1){const{color:s}=t,r=s&&"esriSFSNull"!==t.style&&(0,rt.aH)(s)||0,n=i?A.Uh:0,o=t.materialKey;let a;if(e){const{rect:t,width:i,height:s,pixelRatio:h}=e,l=t.x+A.fL,c=t.y+A.fL,u=l+i,f=c+s,_=(0,nt.UJ)(l,c),m=(0,nt.UJ)(u,f);a=new An(o,e,r,_,m,i/h,s/h,0,0,zn,zn,0,n,null,0,at)}else a=new An(o,null,r,0,0,0,0,0,0,0,0,0,n,null,0,at);return a._maybeAddLineTemplate(t),a}static fromPictureFill(t,e,i=!1){const s=A.ru,{rect:r,width:n,height:o}=e,a=r.x+A.fL,h=r.y+A.fL,l=a+n,c=h+o,u=(0,nt.UJ)(a,h),f=(0,nt.UJ)(l,c),_=Math.round((0,G.F2)(t.width)),m=Math.round((0,G.F2)(t.height)),d=(0,G.F2)(t.xoffset),p=(0,G.F2)(-t.yoffset),g=t.materialKey,y=i?A.Uh:0,x=new An(g,e,s,u,f,_,m,d,p,zn*t.xscale,zn*t.yscale,0,y,null,0,at);return x._maybeAddLineTemplate(t),x}}class En{constructor(){this._resolver=null}isHeld(){return!!this._resolver}async acquire(){this._resolver?(await this._resolver.promise,await this.acquire()):this._resolver=(0,c.hh)()}release(){const t=this._resolver;this._resolver=null,t?.resolve()}}const Fn={marker:d.LW.MARKER,fill:d.LW.FILL,line:d.LW.LINE,text:d.LW.TEXT};class Rn{constructor(t,e,i,s){const r={minScale:e?.minScale,maxScale:e?.maxScale},n=function(t){return t.minScale||t.maxScale?t.minScale+"-"+t.maxScale:""}(r);this.layers=t,this.data=e,this.hash=this._createHash()+n,this.rendererKey=i;const o={isOutline:!1,placement:null,symbologyType:d.mD.DEFAULT,vvFlags:i};for(const e of t){const t=Fn[e.type];o.isOutline="line"===e.type&&e.isOutline,e.materialKey=(0,ot.jj)(t,o),e.maxVVSize=s,e.scaleInfo=r,e.templateHash+=n}}get type(){return"expanded-cim"}_createHash(){let t="";for(const e of this.layers)t+=e.templateHash;return t}}var Nn=i(32243),On=i(17452),Wn=i(65587),Vn=i(25929),Dn=i(27883);async function Bn(t,e,i){if(!t.name)throw new o.Z("style-symbol-reference-name-missing","Missing name in style symbol reference");if(t.styleName&&"Esri2DPointSymbolsStyle"===t.styleName)return async function(t,e){const i=Dn.wm.replace(/\{SymbolName\}/gi,t.name);try{const t=await(0,Dn.EJ)(i,e);return(0,Dn.KV)(t.data)}catch(t){return(0,c.k_)(t),null}}(t,i);try{return async function(t,e,i,s){const r=t.data,n={portal:i&&(0,l.pC)(i.portal)?i.portal:Wn.Z.getDefault(),url:(0,On.mN)(t.baseUrl),origin:"portal-item"},a=r.items.find((t=>t.name===e));if(!a)throw new o.Z("symbolstyleutils:symbol-name-not-found",`The symbol name '${e}' could not be found`,{symbolName:e});let h=(0,Vn.f)((0,Dn.v9)(a,"cimRef"),n);(0,Nn.XO)()&&(h=(0,Nn.pJ)(h));try{const t=await(0,Dn.EJ)(h,s);return(0,Dn.KV)(t.data)}catch(t){return(0,c.k_)(t),null}}(await(0,Dn.n2)(t,e,i),t.name,e,i)}catch(t){return(0,c.k_)(t),null}}const Un=async(t,e,i)=>new Rn(await async function(t,e,i,s,r){const n=s??[];if(!t)return n;let o,a;const h={};if("CIMSymbolReference"!==t.type)return Jr.error("Expect cim type to be 'CIMSymbolReference'"),n;if(o=t.symbol,a=t.primitiveOverrides,a){const t=[];for(const i of a){const s=i.valueExpressionInfo;if(s&&e){const r=s.expression,n=(0,Rs.Yi)(r,e.spatialReference,e.fields).then((t=>{(0,l.Wi)(t)||Kr(h,i.primitiveName,i.propertyName,t)}));t.push(n)}else null!=i.value&&Kr(h,i.primitiveName,i.propertyName,i.value)}t.length>0&&await Promise.all(t)}const c=[];switch(wr.fetchResources(o,i,c),c.length>0&&await Promise.all(c),o?.type){case"CIMPointSymbol":case"CIMLineSymbol":case"CIMPolygonSymbol":!function(t,e,i,s,r,n,o){if(!t)return;const a=t.symbolLayers;if(!a)return;const h=t.effects;let l=it.v2.SCREEN;const c=wr.getSize(t)??0;"CIMPointSymbol"===t.type&&"Map"===t.angleAlignment&&(l=it.v2.MAP);let u=a.length;for(;u--;){const f=a[u];if(!f||!1===f.enable)continue;let _;h&&h.length&&(_=[...h]);const m=f.effects;m&&m.length&&(h?_.push(...m):_=[...m]);const d=[];let p;Pr.findEffectOverrides(_,e,d),p=d.length>0?pn(_,d,i,s):_;const g=[];switch(Pr.findApplicableOverrides(f,e,g),f.type){case"CIMSolidFill":jr(f,p,i,g,s,r);break;case"CIMPictureFill":Qr(f,p,i,g,s,n,r);break;case"CIMHatchFill":tn(f,p,i,g,s,r);break;case"CIMGradientFill":en(f,p,i,g,s,r);break;case"CIMSolidStroke":sn(f,p,i,g,s,r,"CIMPolygonSymbol"===t.type,c);break;case"CIMPictureStroke":rn(f,p,i,g,s,r,"CIMPolygonSymbol"===t.type,c);break;case"CIMGradientStroke":nn(f,p,i,g,s,r,"CIMPolygonSymbol"===t.type,c);break;case"CIMCharacterMarker":if(on(f,p,i,g,s,r))break;break;case"CIMPictureMarker":if(on(f,p,i,g,s,r))break;"CIMLineSymbol"===t.type&&(l=$r(f)),an(f,p,i,g,s,n,r,l,c);break;case"CIMVectorMarker":if(on(f,p,i,g,s,r))break;"CIMLineSymbol"===t.type&&(l=$r(f)),hn(f,p,i,g,s,r,n,l,c,o);break;default:Jr.error("Cannot analyze CIM layer",f.type)}}}(o,a,h,e,n,i,!!r)}return n}(t.data,e,i),t.data,t.rendererKey,t.maxVVSize);async function Gn(t,e,i,s){if(!t)return null;if("cim"===t.type)return Un(t,e,i);if("web-style"===t.type){const r={type:"cim",data:await Bn(t,null,s)??void 0,rendererKey:t.rendererKey,maxVVSize:t.maxVVSize};return Un(r,e,i)}return t}function Xn(t){if(!t)return null;const{avoidSDFRasterization:e,type:i,cim:s,url:r,materialHash:n}=t,o={cim:s,type:i,mosaicHash:n,url:r,size:null,dashTemplate:null,path:null,text:null,fontName:null,animatedSymbolProperties:null,avoidSDFRasterization:e};switch(i){case"marker":o.size=t.size,o.path=t.path,o.animatedSymbolProperties=t.animatedSymbolProperties;break;case"line":o.dashTemplate=t.dashTemplate;break;case"text":o.text=t.text,o.fontName=t.fontName}return o}const Hn=h.Z.getLogger("esri.views.2d.engine.webgl.mesh.templates.WGLTemplateStore"),Yn={sortKey:null,templates:new Array},Jn={isOutline:!1,placement:null,symbologyType:d.mD.DEFAULT,vvFlags:0},Zn={...Yi.eG,hash:JSON.stringify(Yi.eG),materialKey:(0,ot.jj)(d.LW.MARKER,Jn)},qn={...Yi.wW,hash:JSON.stringify(Yi.wW),materialKey:(0,ot.jj)(d.LW.LINE,Jn)},Kn={...Yi.lj,hash:JSON.stringify(Yi.lj),materialKey:(0,ot.jj)(d.LW.FILL,Jn)};function $n(t,e){const i=t.length;return t.push(null),e.then((e=>t[i]=e)),t}function jn(t){return null!=t&&!!(1&t)}class Qn{constructor(t,e){this._idCounter=1,this._templateIdCounter=1,this._idToTemplateGroup=new Map,this._symbolToTemplate=new Map,this._fetchQueue=[],this._idToResolver=new Map,this._cimTemplateCache=new Map,this._cimAnalyses=[],this._lock=new En,this._fetchResource=t,this._tileInfo=e}get _markerError(){return this._errorTemplates.marker[0]}get _fillError(){return this._errorTemplates.fill[0]}get _lineError(){return this._errorTemplates.line[0]}get _textError(){return this._errorTemplates.line[0]}createTemplateGroup(t,e,i=null){this._initErrorTemplates();const s=t.hash,r=this._symbolToTemplate.get(s);if(null!=r)return r;const n=new Array,o={sortKey:i,templates:n};e&&this._createMeshTemplates(n,e,!0),this._createMeshTemplates(n,t,!1);const a=this._createGroupId("expanded-cim"===t.type&&to(t));return this._idToTemplateGroup.set(a,o),this._symbolToTemplate.set(s,a),a}getTemplateGroup(t){return this._idToTemplateGroup.get(t)??Yn}getDynamicTemplateGroup(t){return this._idToTemplateGroup.has(t)?(jn(t)||Hn.error("mapview-template-store",`Id ${t} does not refer to a dynamic template`),this._idToTemplateGroup.get(t)):Yn}getMosaicItem(t,e){const i=this._createTemplateId(),s=new Promise((t=>this._idToResolver.set(i,t)));return this._fetchQueue.push({symbol:t,id:i,glyphIds:e}),s}finalize(t){return this._fetchQueue.length||this._lock.isHeld()?async function(t,e,i){try{await t.acquire(),await e(i),t.release()}catch(e){throw t.release(),e}}(this._lock,this._fetchAllQueuedResources.bind(this),t):Promise.resolve()}_initErrorTemplates(){this._errorTemplates||(this._errorTemplates={fill:this._createMeshTemplates([],Kn,!1),marker:this._createMeshTemplates([],Zn,!1),line:this._createMeshTemplates([],qn,!1)})}_fetchAllQueuedResources(t){if(!this._fetchQueue.length)return Promise.resolve();const e=this._fetchQueue,i=this._cimAnalyses;return this._fetchQueue=[],this._cimAnalyses=[],Promise.all(i).then((()=>this._fetchResource(e,t).then((t=>{for(const{id:e,mosaicItem:i}of t)this._idToResolver.get(e)(i),this._idToResolver.delete(e)})))).catch((t=>{(0,c.D_)(t)?this._fetchQueue=this._fetchQueue.concat(e):"worker:port-closed"===t.name||Hn.error(new o.Z("mapview-template-store","Unable to fetch requested texture resources",t))}))}_createGroupId(t){return this._idCounter++<<1|(t?1:0)}_createTemplateId(){return this._templateIdCounter++}async _createSMS(t){const{spriteMosaicItem:e}=await this.getMosaicItem(t);return Sn(e,Hn)?Hi.fromSimpleMarker(t,e):this._markerError}async _createPMS(t){const{spriteMosaicItem:e}=await this.getMosaicItem(t);return Sn(e,Hn)?Hi.fromPictureMarker(t,e):this._markerError}async _createSFS(t,e){const{spriteMosaicItem:i}=await this.getMosaicItem(t);return Sn(i,Hn)?An.fromSimpleFill(t,i,e):this._fillError}async _createPFS(t,e){const{spriteMosaicItem:i}=await this.getMosaicItem(t);return Sn(i,Hn)?An.fromPictureFill(t,i,e):this._fillError}async _createSLS(t,e){const{spriteMosaicItem:i}=await this.getMosaicItem(t);return Sn(i,Hn)?Ts.fromSimpleLine(t,i):this._lineError}async _createLMS(t){const{spriteMosaicItem:e}=await this.getMosaicItem(t);return Sn(e,Hn)?Hi.fromLineSymbolMarker(t,e):this._markerError}async _createTS(t){const{glyphMosaicItems:e}=await this.getMosaicItem(t);return Ni.fromText(t,e??[])}async _createCIMText(t){const{glyphMosaicItems:e}=await this.getMosaicItem(Xn(t),kn(t.text));return Sn(e,Hn)?Ni.fromCIMText(t,e,this._tileInfo):this._textError}async _createCIMFill(t){const{spriteMosaicItem:e}=await this.getMosaicItem(Xn(t));return Sn(e,Hn)?An.fromCIMFill(t,e,this._tileInfo):this._fillError}async _createCIMLine(t){const{spriteMosaicItem:e}=await this.getMosaicItem(Xn(t));return Sn(e,Hn)?Ts.fromCIMLine(t,e,this._tileInfo):this._lineError}async _createCIMMarker(t){const{spriteMosaicItem:e}=await this.getMosaicItem(Xn(t));return Sn(e,Hn)?Hi.fromCIMMarker(t,e,this._tileInfo):this._markerError}async _createCIM(t){const e=t.templateHash;let i=this._cimTemplateCache.get(e);if(null!=i)return i;switch(t.type){case"marker":i=await this._createCIMMarker(t);break;case"line":i=await this._createCIMLine(t);break;case"fill":i=await this._createCIMFill(t);break;case"text":i=await this._createCIMText(t)}return this._cimTemplateCache.set(e,i),i}async _createDynamicCIM(t){const e=t.templateHash;let i=this._cimTemplateCache.get(e);if(null!=i)return i;switch(t.type){case"marker":i=In.fromCIMMarker(t,this._tileInfo);break;case"line":i=Pn.fromCIMLine(t,this._tileInfo);break;case"fill":i=wn.fromCIMFill(t,this._tileInfo);break;case"text":i=Tn.fromCIMText(t,this._tileInfo)}return this._cimTemplateCache.set(e,i),i}_createPrimitiveMeshTemplates(t,e,i){switch(e.type){case"esriSMS":return $n(t,this._createSMS(e));case"esriPMS":return $n(t,this._createPMS(e));case"esriSFS":return $n(t,this._createSFS(e,i));case"line-marker":return $n(t,this._createLMS(e));case"esriPFS":return $n(t,this._createPFS(e,i));case"esriSLS":return $n(t,this._createSLS(e,!1));case"esriTS":return $n(t,this._createTS(e));default:return Hn.error("Unable to create mesh template for unknown symbol type {: $ }{symbol.type}"),t}}_createMeshTemplates(t,e,i){if(e.type.includes("3d"))return Hn.error("3D symbols are not supported with MapView"),t;if("expanded-cim"===e.type){for(const i of e.layers)"function"==typeof i.materialHash?$n(t,this._createDynamicCIM(i)):$n(t,this._createCIM(i));return t}if("composite-symbol"===e.type){for(const s of e.layers)this._createPrimitiveMeshTemplates(t,s,i);return t}return"cim"===e.type||"label"===e.type||"web-style"===e.type?t:this._createPrimitiveMeshTemplates(t,e,i)}}const to=t=>{if(!t.layers)return!1;for(const e of t.layers)if("function"==typeof e.materialHash)return!0;return!1};class eo{constructor(t,e,i){this._loadPromise=(0,B.j)(),this._geometryType=t,this._idField=e,this._templateStore=i}update(t,e){(0,l.pC)(t.mesh.labels)&&(this._labelTemplates=this._createLabelTemplates(t.mesh.labels,e)),this._schema=t}_createLabelTemplates(t,e){const i=new Map;if("simple"===t.type){for(const s of t.classes){const t=Ui.fromLabelClass(s,e);i.set(s.index,t)}return i}for(const s in t.classes){const r=t.classes[s];for(const t of r){const s=Ui.fromLabelClass(t,e);i.set(t.index,s)}}return i}get templates(){return this._templateStore}async analyze(t,e,i,s,r,n,o){if((0,c.Hc)(o))return;let a;"dictionary"===i?.type&&(a=await i.analyze(this._idField,t.copy(),e,r,n,o));let h=0;for(;t.next();){let e=null;if(e=a?a[h++]:(0,l.pC)(s)&&(0,m.nE)(t.getDisplayId())&&1!==t.readAttribute("cluster_count")?s.match(this._idField,t,this._geometryType,r,n):i.match(this._idField,t,this._geometryType,r,n),t.setGroupId(e),jn(e)){const i=this._templateStore.getDynamicTemplateGroup(e).templates;for(const e of i)e&&e.analyze&&e.analyze(this._templateStore,t,r,n)}}return await this._loadPromise,this._templateStore.finalize(o)}async analyzeGraphics(t,e,i,s,r,n){if((0,c.Hc)(n))return;const o=t.getCursor();for(i&&await i.analyze(this._idField,o.copy(),e,s,r,n);o.next();){let t=o.getGroupId();if(null!=t&&-1!==t||(t=i?.match(this._idField,o,o.geometryType,s,r),o.setGroupId(t)),jn(t)){const e=this._templateStore.getDynamicTemplateGroup(t).templates;for(const t of e)t&&t.analyze&&t.analyze(this._templateStore,o,s,r)}o.setGroupId(t)}return await this._loadPromise,this._templateStore.finalize(n)}writeGraphic(t,e,i,s){const r=e.getGroupId(),n=e.getDisplayId(),o=this._templateStore.getTemplateGroup(r);if(t.featureStart(e.insertAfter,0),null!=n){if(jn(r))for(const t of o.templates)t&&t.bindFeature(e,null,null);if(o){for(const r of o.templates)r&&r.write(t,e,i,s);t.featureEnd()}}}writeCursor(t,e,i,s,r,n,o){const a=e.getGroupId(),h=e.getDisplayId(),c=this._templateStore.getTemplateGroup(a),u=c.templates,f=this._getSortKeyValue(e,c);if(t.featureStart(0,f),null!=h&&u){if(jn(a))for(const t of u)t.bindFeature(e,i,s);for(const i of u)i.write(t,e,r,o);if((0,l.pC)(n)&&t.hasRecords){const i=n&&this._findLabelRef(u);this._writeLabels(t,e,n,i,r,o)}t.featureEnd()}}_getSortKeyValue(t,e){const i=this._schema.mesh.sortKey;if((0,l.Wi)(i))return 0;let s=0;return s=!0===i.byRenderer&&null!=e.sortKey?e.sortKey:null!=i.fieldIndex?t.getComputedNumericAtIndex(i.fieldIndex):null!=i.field?t.readAttribute(i.field):t.readAttribute(this._idField),s*="asc"===i.order?1:-1,null==s||isNaN(s)?0:s}_findLabelRef(t){for(const e of t)if(e instanceof Hi)return e;return null}_writeLabels(t,e,i,s,r,n){for(const o of i)if((0,l.pC)(o)&&o){const{glyphs:i,rtl:a,index:h}=o,l=this._labelTemplates.get(h);if(!l)continue;l.setZoomLevel(r),l.bindReferenceTemplate(s),l.bindTextInfo(i,a),l.write(t,e,null,n)}}}var io=i(13867);const so=h.Z.getLogger("esri/views/2d/engine/webgl/util/Matcher");async function ro(t,e,i,s){switch(t.type){case"simple":case"heatmap":return no.fromBasicRenderer(t,e,i,s);case"map":return ho.fromUVRenderer(t,e,i,s);case"interval":return ao.fromCBRenderer(t,e,i,s);case"dictionary":return uo.fromDictionaryRenderer(t,e,i,s);case"pie-chart":return oo.fromPieChartRenderer(t,e,i,s);case"subtype":return oo.fromSubtypes(t,e,i,s)}}class no{constructor(){this.type="feature",this._defaultResult=null}static async fromBasicRenderer(t,e,i,s){const r=new no;if(t.symbol){const n=await Gn(t.symbol,i,s),o=e.createTemplateGroup(n,null);r.setDefault(o)}return r}static async fromPieChartRenderer(t,e,i,s){const r=new no;if(t.markerSymbol){const n=await Gn(t.markerSymbol,i,s);let o;t.fillSymbol&&(o=await Gn(t.fillSymbol,i,s));const a=e.createTemplateGroup(n,o);r.setDefault(a)}return r}size(){return 1}getDefault(){return this._defaultResult}setDefault(t){this._defaultResult=t}match(t,e,i,s,r){return this.getDefault()}async analyze(t,e,i,s,r,n){return null}}class oo extends no{constructor(t,e){super(),this._subMatchers=t,this._subtypeField=e}static async fromSubtypes(t,e,i,s){const r=new Map,n=[];for(const o in t.renderers){const a=parseInt(o,10),h=ro(t.renderers[o],e,i,s).then((t=>r.set(a,t)));n.push(h)}return await Promise.all(n),new oo(r,t.subtypeField)}match(t,e,i,s,r){const n=e.readAttribute(this._subtypeField),o=this._subMatchers.get(n);return o?o.match(t,e,i,s,r):null}}class ao extends no{constructor(t,e,i,s){super(),this.type="interval",this._intervals=[],this._isMaxInclusive=e,this._fieldIndex=s,this._field=t,this._normalizationInfo=i}static async fromCBRenderer(t,e,i,s){const{isMaxInclusive:r,normalizationField:n,normalizationTotal:o,normalizationType:a}=t,h=t.field,l=new ao(h,r,{normalizationField:n,normalizationTotal:o,normalizationType:a},t.fieldIndex),c=await Gn(t.backgroundFillSymbol,i,s);await Promise.all(t.intervals.map((async t=>{const r=await Gn(t.symbol,i,s),n=await e.createTemplateGroup(r,c),o={min:t.min,max:t.max};l.add(o,n)})));const u=await Gn(t.defaultSymbol,i,s);if(u){const t=await e.createTemplateGroup(u,c);l.setDefault(t)}return l}add(t,e){this._intervals.push({interval:t,result:e}),this._intervals.sort(((t,e)=>t.interval.min-e.interval.min))}size(){return super.size()+this._intervals.length}match(t,e,i,s,r){if(null==this._fieldIndex&&!this._field)return this.getDefault();const n=null!=this._fieldIndex?e.getComputedNumericAtIndex(this._fieldIndex):this._getValueFromField(e);if(null==n||isNaN(n)||n===1/0||n===-1/0)return this.getDefault();for(let t=0;t<this._intervals.length;t++){const{interval:e,result:i}=this._intervals[t],s=n>=e.min,r=this._isMaxInclusive?n<=e.max:n<e.max;if(s&&r)return i}return this.getDefault()}_needsNormalization(){const t=this._normalizationInfo;return t&&(t.normalizationField||t.normalizationTotal||t.normalizationType)}_getValueFromField(t){const e=t.readAttribute(this._field);if(!this._needsNormalization()||null==e)return e;const{normalizationField:i,normalizationTotal:s,normalizationType:r}=this._normalizationInfo,n=t.readAttribute(i)??1;if(r)switch(r){case"esriNormalizeByField":return n?e/n:void 0;case"esriNormalizeByLog":return Math.log(e)*Math.LOG10E;case"esriNormalizeByPercentOfTotal":return e/s*100;default:return void so.error(`Found unknown normalization type: ${r}`)}else so.error("Normalization is required, but no type was set!")}}class ho extends no{constructor(t,e,i){super(),this.type="map",this._nullResult=null,this._resultsMap=new Map,this._fields=[],this._fieldsIndex=i,this._fields=t,this._seperator=e||""}static async fromUVRenderer(t,e,i,s){const r=t.fieldDelimiter,n=[t.field];t.field2&&n.push(t.field2),t.field3&&n.push(t.field3);const o=await Gn(t.backgroundFillSymbol,i,s),a=new ho(n,r,t.fieldIndex);await Promise.all(t.map.map((async(t,r)=>{const n=await Gn(t.symbol,i,s),h=r+1,l=await e.createTemplateGroup(n,o,h);"<Null>"===t.value?a.setNullResult(l):a.add(t.value,l)})));const h=await Gn(t.defaultSymbol,i,s);if(h){const t=Number.MAX_SAFE_INTEGER,i=await e.createTemplateGroup(h,o,t);a.setDefault(i)}return a}setNullResult(t){this._nullResult=t}add(t,e){this._resultsMap.set(t.toString(),e)}size(){return super.size()+this._resultsMap.size}match(t,e,i,s,r){if(null==this._fieldsIndex&&!this._fields)return this.getDefault();const n=null!=this._fieldsIndex?e.getComputedStringAtIndex(this._fieldsIndex):this._getValueFromFields(e);if(null!==this._nullResult&&(null==n||""===n||"<Null>"===n))return this._nullResult;if(null==n)return this.getDefault();const o=n.toString();return this._resultsMap.has(o)?this._resultsMap.get(o):this.getDefault()}_getValueFromFields(t){const e=[];for(const i of this._fields){const s=t.readAttribute(i);null==s||""===s?e.push("<Null>"):e.push(s)}return e.join(this._seperator)}}let lo;async function co(){return lo||(lo=i.e(5329).then(i.bind(i,25329))),lo}class uo extends no{constructor(t,e,i,s,r,n){super(),this.type="dictionary",this._groupIdCache=new io.Z(100),this._loader=t,this._fieldMap=t.fieldMap,this._symbolFields=t.getSymbolFields(),this._templates=e,this._info=i,this._scaleFn=s,this._schemaUtilsModule=r,this._symbolOptions=n}static async fromDictionaryRenderer(t,e,s,r){const[{DictionaryLoader:n},o]=await Promise.all([i.e(4475).then(i.bind(i,84475)),co()]),a=new n(t.url,t.config,t.fieldMap);await a.fetchResources({spatialReference:s.spatialReference,fields:s.fields});const h=await async function(t,e){const i=t||1;if("number"==typeof i)return(t,e,s)=>i;const s=await(0,Rs.Yi)(i,e.spatialReference,e.fields);return(t,i,r)=>gr(s,t,{$view:r},e.geometryType,i)||1}(t.scaleExpression,s);return new uo(a,e,s,h,o,t.symbolOptions)}async _analyzeFeature(t,e,i,s,r){const n=t.readLegacyFeature(),a=this._scaleFn(n,i,s),h=this._attributeHash(n)+"-"+a,l=this._groupIdCache.get(h);if(l)return l;const c={...s,spatialReference:this._info.spatialReference,abortOptions:r,fields:this._info.fields},u=await this._loader.getSymbolAsync(n,c),f=Gn(this._schemaUtilsModule.createSymbolSchema(u,this._symbolOptions),this._info,e,r).then((t=>{if("expanded-cim"!==t?.type)return so.error(new o.Z("mapview-bad-type",`Found unexpected type ${t?.type} in dictionary response`)),null;t.hash+="-"+a;for(const e of t.layers)e.scaleFactor=a,e.templateHash+="-"+a;return this._templates.createTemplateGroup(t,null)}));return this._groupIdCache.put(h,f,1),f}async analyze(t,e,i,s,r,n){const o=e.getCursor(),a=[];for(;o.next();)a.push(this._analyzeFeature(o,i,s,r,n));return Promise.all(a).then((t=>t.filter(l.pC)))}match(t,e,i,s,r){return null}_attributeHash(t){let e="";for(const i of this._symbolFields){const s=this._fieldMap?.[i];s&&(e+=t.attributes[s]+"-")}return e}}var fo=i(47988);class _o{constructor(t){this._remoteClient=t,this._resourceMap=new Map,this._inFlightResourceMap=new Map,this.geometryEngine=null,this.geometryEnginePromise=null}destroy(){}async fetchResource(t,e){const i=this._resourceMap,s=i.get(t);if(s)return s;let r=this._inFlightResourceMap.get(t);if(r)return r;try{r=this._remoteClient.invoke("tileRenderer.fetchResource",{url:t},{...e}),this._inFlightResourceMap.set(t,r),r.then((e=>(this._inFlightResourceMap.delete(t),i.set(t,e),e)))}catch(t){return(0,c.D_)(t)?null:{width:0,height:0}}return r}getResource(t){return this._resourceMap.get(t)??null}}function mo(t,e){return(!t.minScale||t.minScale>=e)&&(!t.maxScale||t.maxScale<=e)}function po(t){const e=t.message,i={message:{data:{},tileKey:e.tileKey,tileKeyOrigin:e.tileKeyOrigin,version:e.version},transferList:new Array};for(const t in e.data){const s=e.data[t];if(i.message.data[t]=null,(0,l.pC)(s)){const e=s.stride,r=s.indices.slice(0),n=s.vertices.slice(0),o=s.records.slice(0),a={stride:e,indices:r,vertices:n,records:o,metrics:(0,l.yw)(s.metrics,(t=>t.slice(0)))};i.transferList.push(r,n,o),i.message.data[t]=a}}return i}let go=class extends fo.Z{constructor(){super(...arguments),this.type="symbol",this._matchers={feature:null,aggregate:null},this._bufferData=new Map,this._bufferIds=new Map}initialize(){this.handles.add([this.tileStore.on("update",this.onTileUpdate.bind(this))]),this._resourceManagerProxy=new _o(this.remoteClient)}destroy(){this._resourceManagerProxy.destroy()}get supportsTileUpdates(){return!0}forEachBufferId(t){this._bufferIds.forEach((e=>{e.forEach(t)}))}async update(t,e){const i=e.schema.processors[0];if("symbol"!==i.type)return;const s=(0,f.Hg)(this._schema,i);((0,f.uD)(s,"mesh")||(0,f.uD)(s,"target"))&&(t.mesh=!0,t.why?.mesh.push("Symbology changed"),this._schema=i,this._factory=this._createFactory(i),this._factory.update(i,this.tileStore.tileScheme.tileInfo))}onTileMessage(t,e,i,s){return(0,c.k_)(s),this._onTileData(t,e,i,s)}onTileClear(t){return this._bufferData.delete(t.key.id),this._bufferIds.delete(t.key.id),this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:t.id,data:{clear:!0}})}onTileError(t,e,i){const s=i.signal,r={tileKey:t.id,error:e};return this.remoteClient.invoke("tileRenderer.onTileError",r,{signal:s})}onTileUpdate(t){for(const e of t.removed)this._bufferData.has(e.key.id)&&this._bufferData.delete(e.key.id),this._bufferIds.has(e.key.id)&&this._bufferIds.delete(e.key.id);for(const e of t.added)this._bufferData.forEach((t=>{for(const i of t)i.message.tileKey===e.id&&this._updateTileMesh("append",e,po(i),[],!1,!1,null)}))}_addBufferData(t,e){this._bufferData.has(t)||this._bufferData.set(t,[]),this._bufferData.get(t)?.push(po(e))}_createFactory(t){const{geometryType:e,objectIdField:i,fields:s}=this.service,r={geometryType:e,fields:s,spatialReference:_.Z.fromJSON(this.spatialReference)},n=new Qn(((t,e)=>this.remoteClient.invoke("tileRenderer.getMaterialItems",t,e)),this.tileStore.tileScheme.tileInfo),{matcher:o,aggregateMatcher:a}=t.mesh;return this._store=n,this._matchers.feature=ro(o,n,r,this._resourceManagerProxy),this._matchers.aggregate=(0,l.yw)(a,(t=>ro(t,n,r,this._resourceManagerProxy))),new eo(e,i,n)}async _onTileData(t,e,i,s){(0,c.k_)(s);const{type:r,addOrUpdate:n,remove:o,clear:a,end:h}=e,u=!!this._schema.mesh.sortKey;if(!n){const e={type:r,addOrUpdate:null,remove:o,clear:a,end:h,sort:u};return this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:t.id,data:e},s)}const f=this._processFeatures(t,n,i,s,e.status?.version);try{const i=await f;if((0,l.Wi)(i)){const e={type:r,addOrUpdate:null,remove:o,clear:a,end:h,sort:u};return this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:t.id,data:e},s)}const n=[];for(const e of i){let i=!1;const s=e.message.bufferIds,r=t.key.id,o=e.message.tileKey;if(r!==o&&(0,l.pC)(s)){if(!this.tileStore.get(o)){this._addBufferData(r,e),n.push(e);continue}let t=this._bufferIds.get(o);t||(t=new Set,this._bufferIds.set(o,t));const a=Array.from(s);for(const e of a){if(t.has(e)){i=!0;break}t.add(e)}}i||(this._addBufferData(r,e),n.push(e))}await Promise.all(n.map((i=>{const n=t.key.id===i.message.tileKey,o=n?e.remove:[],a=n&&e.end;return this._updateTileMesh(r,t,i,o,a,!!e.clear,s.signal)})))}catch(e){this._handleError(t,e,s)}}async _updateTileMesh(t,e,i,s,r,n,o){const a=t,h=i.message.tileKey,u=!!this._schema.mesh.sortKey;h!==e.key.id&&(r=!1);const f=(0,l.yw)(i,(t=>t.message)),_=(0,l.yw)(i,(t=>t.transferList))||[],m={type:a,addOrUpdate:f,remove:s,clear:n,end:r,sort:u},d={transferList:(0,l.Wg)(_)||[],signal:o};return(0,c.k_)(d),this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:h,data:m},d)}async _processFeatures(t,e,i,s,r){if((0,l.Wi)(e)||!e.hasFeatures)return null;const n={transform:t.transform,hasZ:!1,hasM:!1},o=this._factory,a={viewingMode:"",scale:t.scale},h=await this._matchers.feature,u=await this._matchers.aggregate;(0,c.k_)(s);const f=this._getLabelInfos(t,e);return await o.analyze(e.getCursor(),this._resourceManagerProxy,h,u,n,a),(0,c.k_)(s),this._writeFeatureSet(t,e,n,f,o,i,r)}_writeFeatureSet(t,e,i,s,r,n,o){const a=e.getSize(),h=this._schema.mesh.matcher.symbologyType,c=new D(t.key.id,{features:a,records:a,metrics:0},h,n,h!==d.mD.HEATMAP,o),u={viewingMode:"",scale:t.scale},f=e.getCursor();for(;f.next();)try{const e=f.getDisplayId(),n=(0,l.pC)(s)?s.get(e):null;r.writeCursor(c,f,i,u,t.level,n,this._resourceManagerProxy)}catch(t){}const _=t.tileInfoView.tileInfo.isWrappable;return c.serialize(_)}_handleError(t,e,i){if(!(0,c.D_)(e)){const s={tileKey:t.id,error:e.message};return this.remoteClient.invoke("tileRenderer.onTileError",s,{signal:i.signal})}return Promise.resolve()}_getLabelingSchemaForScale(t){const e=this._schema.mesh.labels;if((0,l.Wi)(e))return null;if("subtype"===e.type){const i={type:"subtype",classes:{}};let s=!1;for(const r in e.classes){const n=e.classes[r].filter((e=>mo(e,t.scale)));s=s||!!n.length,i.classes[r]=n}return s?i:null}const i=e.classes.filter((e=>mo(e,t.scale)));return i.length?{type:"simple",classes:i}:null}_getLabels(t,e){if("subtype"===e.type){const i=this.service.subtypeField,s=(0,l.s3)(i,"Expected to find subtype Field"),r=t.readAttribute(s);return null==r?[]:e.classes[r]??[]}return e.classes}_getLabelInfos(t,e){const i=this._getLabelingSchemaForScale(t);if((0,l.Wi)(i))return null;const s=new Map,r=e.getCursor();for(;r.next();){const t=r.getDisplayId(),e=[],o=(0,m.nE)(t),a=o&&1!==r.readAttribute("cluster_count")?"aggregate":"feature",h=this._getLabels(r,i);for(const i of h){if(i.target!==a)continue;const s=r.getStorage(),h=o&&"feature"===a?s.getComputedStringAtIndex(r.readAttribute("referenceId"),i.fieldIndex):s.getComputedStringAtIndex(t,i.fieldIndex);if(!h)continue;const l=n(h.toString()),c=l[0],u=l[1];this._store.getMosaicItem(i.symbol,kn(c)).then((t=>{e[i.index]={glyphs:t.glyphMosaicItems??[],rtl:u,index:i.index}}))}s.set(t,e)}return s}};go=(0,s._)([(0,u.j)("esri.views.2d.layers.features.processors.SymbolProcessor")],go);const yo=go},14867:(t,e,i)=>{function s(t,e,i,s){const r=t.clone(),n=1<<r.level,o=r.col+e,a=r.row+i;return s&&o<0?(r.col=o+n,r.world-=1):o>=n?(r.col=o-n,r.world+=1):r.col=o,r.row=a,r}i.d(e,{M:()=>s})}}]);