<template>
  <div class="chart">
    <VChart
      ref="refChart"
      :option="state.ring"
    ></VChart>
  </div>
</template>
<script lang="ts" setup>
import { GetPartitionDeviceFlowOrPressure } from '@/api/mapservice/dma'
import { usePartition } from '@/hooks/arcgis'
import { IECharts } from '@/plugins/echart'
// import { transNumberUnit } from '@/utils/GlobalHelper'

const refChart = ref<IECharts>()
// 饼图
const ring = (
  data: {
    name: string
    value: string
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  // const title = '总数'
  // const formatNumber = function (num) {
  //   const reg = /(?=(\B)(\d{3})+$)/g
  //   return num.toString().replace(reg, ',')
  // }
  // const total = data.reduce((a, b: any) => {
  //   return a + (parseFloat(b.value) || 0) * 1
  // }, 0)
  // const transedTotal = transNumberUnit(total)
  // data = [
  //   { name: '111', value: '452' },
  //   { name: '121', value: '722' },
  //   { name: '131', value: '222' }
  // ]
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (prefix || '') + params.name + ': ' + Number(params.value).toFixed(percision) + ' ' + (unit || '')
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      right: 40,
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 120,
            fontSize: 12,
            color: '#fff'
          },
          value: {
            align: 'left',
            width: 80,
            fontSize: 12,
            color: '#00ff00'
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        const length = data?.length ?? 0
        for (let i = 0; i < length; i++) {
          if (name === data[i].name) {
            return '{name| ' + name + '}' + '{value| ' + data[i].value + '}'
          }
        }
      }
    },
    // title: [
    //   {
    //     text:
    //       '{name|'
    //       + title
    //       + ((unit && '(' + transedTotal.unit + unit + ')') || '(' + transedTotal.unit + ')')
    //       + '}\n{val|'
    //       + formatNumber(transedTotal.value.toFixed(percision))
    //       + '}',
    //     top: 'center',
    //     left: '30%',
    //     textAlign: 'center',
    //     textStyle: {
    //       rich: {
    //         name: {
    //           fontSize: 10,
    //           fontWeight: 'normal',
    //           padding: [8, 0],
    //           align: 'center',
    //           color: '#fff'
    //         },
    //         val: {
    //           fontSize: 16,
    //           fontWeight: 'bold',
    //           color: '#fff'
    //         }
    //       }
    //     }
    //   }
    // ],
    series: [
      {
        type: 'pie',
        radius: ['35%', '50%'],
        center: ['30%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          // color: '#FFFFFF',
          // fontSize: 8,
          position: 'center'
          // formatter: '{b}\n{d}'
          // formatter: params => {
          //   return (
          //     '{icon|●}{name|'
          //     + params.name
          //     + '}{value|'
          //     + formatNumber(Number(params.value || '0').toFixed(percision))
          //     + '}'
          //   )
          // },
          // padding: [0, -100, 25, -100],
          // rich: {
          //   icon: {
          //     fontSize: 16
          //   },
          //   name: {
          //     fontSize: 14,
          //     padding: [0, 10, 0, 4]
          //   },
          //   value: {
          //     fontSize: 18,
          //     fontWeight: 'bold'
          //   }
          // }
        },
        emphasis: {
          // label: {
          //   show: true,
          //   color: '#fff',
          //   fontSize: '10'
          // }
          label: {
            show: true,
            fontSize: '30',
            fontWeight: 'bold',
            formatter: ['{a|{d}%}', '{b|{b}}'].join('\n'),
            rich: {
              a: {
                // 中间字的数值样式
                color: '#0AE79A',
                fontSize: 20,
                lineHeight: 30,
                verticalAlign: 'bottom'
              },
              b: {
                // 中间字的名称样式
                color: '#fff',
                fontSize: '40%',
                lineHeight: 24
              }
            }
          }
        }
      }
    ]
  }
  return option
}
const state = reactive<{ ring: any; mTime: number }>({
  ring: ring(undefined, 'm³/h'),
  mTime: -1
})
const refreshData = async () => {
  await partition.getDeviceTree({ type: 'flow' })
  const deviceTree = partition.DeviceTree.value
  const devices:any = []
  for(let i = 0; i < deviceTree.length; i++) {
    const children:any = deviceTree[i].children
    for(let j = 0; j < children.length; j++) {
      if(children[j].label === '区域供水'){
        devices.push(...children[j].children || []);
      }
    }
  }
  // const devices = partition.DeviceTree.value?.[0]?.children?.find(item => item.label === '区域供水')?.children || []

  const res = await GetPartitionDeviceFlowOrPressure({
    page: 1,
    size: 20,
    deviceIdList: devices?.map(item => item.data.deviceId),
    type: 'time',
    interval: '1h',
    start: moment().startOf('D').valueOf(),
    end: moment().endOf('D').add(1, 'd').valueOf(),
    queryType: 'flow'
  })
  const hour = moment().get('h')
  console.log(hour)
  const data = res.data?.data?.[hour]?.data
    || devices.map(item => {
      return {
        name: item.label,
        value: 0
      }
    })
  state.ring = ring(data, 'm³/h')
}
const partition = usePartition()
const handleAnimate = () => {
  let index = -1 // 高亮所在下标
  const dataLength = state.ring.series[0].data.length // 当前饼图有多少个扇形
  // 用定时器控制饼图高亮
  state.mTime = setInterval(() => {
    // 清除之前的高亮
    refChart.value
      && refChart.value.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: index
      })
    index = (index + 1) % dataLength
    // 当前下标高亮
    refChart.value
      && refChart.value.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index
      })
    if (index > dataLength) {
      index = 0
    }
  }, 3000)
  // 鼠标划入
  refChart.value?.chart?.on('mouseover', (e: any) => {
    // 停止定时器，清除之前的高亮
    clearInterval(state.mTime)
    refChart.value?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0
    })
    refChart.value?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: e.dataIndex
    })
  })
  refChart.value?.chart?.on('mouseout', e => {
    clearInterval(state.mTime)
    refChart.value?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: e.dataIndex
    }) // 鼠标移出后先把上次的高亮取消
    state.mTime = setInterval(() => {
      // 取消高亮指定的数据图形
      if (refChart.value) {
        refChart.value.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index % dataLength
        })
        index++
        // 高亮指定的数据图形
        refChart.value
          && refChart.value.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: index % dataLength
          })
      }
    }, 3000)
  })
}
onMounted(() => {
  refreshData()
  handleAnimate()
})
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
