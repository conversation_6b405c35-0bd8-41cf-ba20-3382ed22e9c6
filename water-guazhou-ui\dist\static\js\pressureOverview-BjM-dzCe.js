import{C as G,r as g,l,c as x,a8 as M,s as H,o as U,a0 as W,bA as I,g as J,n as K,q as i,F as m,p as n,bh as u,aq as R,b7 as Q,bB as X,c4 as F,bU as Z,bW as tt}from"./index-r0dFAfgr.js";import{_ as et}from"./CardSearch-CB_HNR-Q.js";import{_ as at}from"./index-C9hz-UZb.js";import{G as ot}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const rt={class:"wrapper"},st={class:"card-title"},nt={class:"title"},lt={class:"date"},it={class:"card-content"},ct={class:"overview-section"},dt={class:"stat-card abnormal"},mt={class:"stat-number"},pt={class:"stat-card max-pressure"},ut={class:"stat-number"},ft={class:"stat-card min-pressure"},ht={class:"stat-number"},yt={class:"stat-card alarm-count"},gt={class:"stat-number"},_t={class:"chart-section"},bt={class:"chart-container"},Mt={class:"chart-date"},vt={class:"chart-container"},Yt={class:"table-section"},xt={class:"table-section"},Dt={__name:"pressureOverview",setup(Tt){const e=g({stationList:[],alarmStatusList:[{label:"全部",value:""},{label:"正常",value:"normal"},{label:"异常",value:"abnormal"},{label:"报警",value:"alarm"}],selectedStations:[],alarmStatus:"",time:l().format("YYYY-MM-DD"),reportType:"day",timeRange:[l().subtract(7,"days").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")]}),p=g({currentStatus:"异常",maxPressure:"0.30",minPressure:"0.18",alarmCount:8}),_=x(),v=x(),Y=x();let c=null,d=null;const f=g({defaultParams:{stationIds:[],alarmStatus:"",time:l().format("YYYY-MM-DD"),reportType:"day",timeRange:[l().subtract(7,"days").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")]},filters:[{type:"select",label:"选择点位:",field:"stationIds",multiple:!0,options:M(()=>e.stationList),placeholder:"全部",onChange:()=>{h()}},{type:"select",label:"报警状态:",field:"alarmStatus",options:e.alarmStatusList,placeholder:"全部",onChange:()=>{h()}},{type:"select",label:"报表类型:",field:"reportType",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"自定义",value:"custom"}],onChange:t=>{e.reportType=t,$(),h()}},{type:"date",label:"日期",field:"time",hidden:M(()=>e.reportType!=="day"),onChange:t=>{e.time=t}},{type:"daterange",label:"时间范围",field:"timeRange",hidden:M(()=>e.reportType!=="custom"),onChange:t=>{e.timeRange=t}},{type:"month",label:"月份",field:"time",hidden:M(()=>e.reportType!=="month"),onChange:t=>{e.time=t}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:"iconfont icon-chaxun",click:()=>h()},{perm:!0,text:"重置",svgIcon:H(Q),click:()=>O()},{perm:!0,iconifyIcon:"ep:download",type:"warning",text:"导出",click:()=>q()}]}]}),D=g({height:300,highlightCurrentRow:!1,tableTitle:"管网压力详细数据",showOverflowTooltip:!0,stripe:!0,columns:[{label:"时间",prop:"time",fixed:"left"},{label:"2025-06-25 (MPa)",prop:"day_25",align:"center"},{label:"2025-06-24 (MPa)",prop:"day_24",align:"center"},{label:"2025-06-23 (MPa)",prop:"day_23",align:"center"},{label:"2025-06-22 (MPa)",prop:"day_22",align:"center"},{label:"2025-06-21 (MPa)",prop:"day_21",align:"center"},{label:"2025-06-20 (MPa)",prop:"day_20",align:"center"},{label:"2025-06-19 (MPa)",prop:"day_19",align:"center"},{label:"差值率 (%)",prop:"variance",align:"center"},{label:"变化系数",prop:"coefficient",align:"center"}],dataList:[],pagination:{pageSize:10,pageSizes:[10,20,50],layout:"total, sizes, prev, pager, next, jumper"}}),T=g({height:180,highlightCurrentRow:!1,tableTitle:"管网压力汇总统计",showOverflowTooltip:!0,stripe:!0,columns:[{label:"日期",prop:"date",align:"center"},{label:"最大值",prop:"maxValue",align:"center"},{label:"最大值发生时间",prop:"maxTime",align:"center"},{label:"最小值",prop:"minValue",align:"center"},{label:"最小值发生时间",prop:"minTime",align:"center"},{label:"平均值",prop:"avgValue",align:"center"}],dataList:[],pagination:{hide:!0}}),L=()=>({day:"管网压力日简报",custom:"管网压力自定义报表",month:"管网压力月报"})[e.reportType]||"管网压力简报",k=()=>({day:"日报",month:"月报",custom:"自定义报表"})[e.reportType]||"日报",S=()=>{if(e.reportType==="custom"&&e.timeRange)return`${l(e.timeRange[0]).format("YYYY-MM-DD")} 至 ${l(e.timeRange[1]).format("YYYY-MM-DD")}`;const t={day:"YYYY-MM-DD",month:"YYYY-MM"};return l(e.time).format(t[e.reportType]||"YYYY-MM-DD")},$=()=>{var o;const t=l();switch(e.reportType){case"day":e.time=t.format("YYYY-MM-DD");break;case"custom":e.timeRange=[l().subtract(7,"days").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")];break;case"month":e.time=t.format("YYYY-MM");break}f.defaultParams.reportType=e.reportType,e.reportType==="custom"?(f.defaultParams.timeRange=e.timeRange,delete f.defaultParams.time):(f.defaultParams.time=e.time,delete f.defaultParams.timeRange),(o=_.value)==null||o.resetForm()},E=()=>{X(()=>{v.value&&!c&&(c=F(v.value),P()),Y.value&&!d&&(d=F(Y.value),w())})},P=()=>{if(!c)return;const t=z(),o=C(t.length,.25,.4),r=C(t.length,.1,.3),a={tooltip:{trigger:"axis",formatter:s=>{let y=`${s[0].name}<br/>`;return s.forEach(b=>{y+=`${b.seriesName}: ${b.value}MPa<br/>`}),y}},legend:{data:["最高管压/MPa","最低管压/MPa","告警阈值"],bottom:10},xAxis:{type:"category",data:t,axisLabel:{rotate:45}},yAxis:{type:"value",name:"MPa",min:0,max:.5,axisLabel:{formatter:"{value}"}},series:[{name:"最高管压/MPa",type:"line",data:o,smooth:!0,lineStyle:{color:"#1890ff"},areaStyle:{color:"rgba(24, 144, 255, 0.1)"}},{name:"最低管压/MPa",type:"line",data:r,smooth:!0,lineStyle:{color:"#52c41a"},areaStyle:{color:"rgba(82, 196, 26, 0.1)"}},{name:"告警阈值",type:"line",data:Array(t.length).fill(.3),lineStyle:{color:"#ff4d4f",type:"dashed"},symbol:"none"}]};c.setOption(a)},w=()=>{if(!d)return;const r={tooltip:{trigger:"axis",formatter:a=>`${a[0].name}<br/>报警次数/次: ${a[0].value}`},xAxis:{type:"category",data:["6月19日","6月20日","6月21日","6月22日","6月23日","6月24日","6月25日"],axisLabel:{rotate:45}},yAxis:{type:"value",name:"次",min:0,max:35},series:[{name:"报警次数/次",type:"bar",data:[17,26,11,30,1,15,11],itemStyle:{color:"#faad14"},barWidth:"60%"}]};d.setOption(r)},z=()=>{const t=[];let o=7;if(e.reportType==="custom"&&e.timeRange){const r=l(e.timeRange[0]);o=l(e.timeRange[1]).diff(r,"days")+1}else e.reportType==="month"&&(o=12);for(let r=o-1;r>=0;r--){let a;switch(e.reportType){case"day":a=l().subtract(r,"days").format("6月DD日");break;case"custom":e.timeRange?a=l(e.timeRange[1]).subtract(r,"days").format("6月DD日"):a=l().subtract(r,"days").format("6月DD日");break;case"month":a=l().subtract(r,"months").format("MM月");break;default:a=l().subtract(r,"days").format("6月DD日")}t.push(a)}return t},C=(t,o,r)=>{const a=[];for(let s=0;s<t;s++)a.push((Math.random()*(r-o)+o).toFixed(2));return a},V=()=>{const t=[],o=[];for(let r=0;r<24;r++){const a=`${r.toString().padStart(2,"0")}:00`;t.push(a)}return t.forEach((r,a)=>{const s={time:r};if(s.day_25=(Math.random()*.25+.15).toFixed(1),s.day_24=(Math.random()*.25+.15).toFixed(1),s.day_23=(Math.random()*.25+.15).toFixed(1),s.day_22=(Math.random()*.25+.15).toFixed(1),s.day_21=(Math.random()*.25+.15).toFixed(1),s.day_20=(Math.random()*.25+.15).toFixed(1),s.day_19=(Math.random()*.25+.15).toFixed(1),s.variance=(Math.random()*10+1).toFixed(1)+"%",s.coefficient=(Math.random()*.5+.1).toFixed(2),r==="01:00"&&(s.day_24="0.1",s.day_25="0.4",s._rowClassName="danger-row"),a===6||a===18){const y=Math.random()>.5?"day_23":"day_22";s[y]=Math.random()>.5?"0.1":"0.4",s._rowClassName=Math.random()>.5?"warning-row":"danger-row"}o.push(s)}),o},A=()=>{const t=[];return["2025-06-25","2025-06-24","2025-06-23","2025-06-22","2025-06-21","2025-06-20","2025-06-19"].forEach(r=>{const a=(Math.random()*.3+.25).toFixed(1),s=(Math.random()*.15+.1).toFixed(1),y=((parseFloat(a)+parseFloat(s))/2).toFixed(2),b=Math.floor(Math.random()*24).toString().padStart(2,"0"),B=Math.floor(Math.random()*60).toString().padStart(2,"0"),N=Math.floor(Math.random()*24).toString().padStart(2,"0"),j=Math.floor(Math.random()*60).toString().padStart(2,"0");t.push({date:r,maxValue:a,maxTime:`${b}:${B}`,minValue:s,minTime:`${N}:${j}`,avgValue:y})}),t},h=async()=>{var t;try{const o=(t=_.value)==null?void 0:t.queryParams;let r={stationType:"压力监测站",queryType:e.reportType,...o};e.reportType==="custom"&&e.timeRange&&(r.startTime=e.timeRange[0],r.endTime=e.timeRange[1]),await new Promise(a=>setTimeout(a,500)),p.currentStatus=Math.random()>.5?"正常":"异常",p.maxPressure=(Math.random()*.2+.25).toFixed(2),p.minPressure=(Math.random()*.15+.1).toFixed(2),p.alarmCount=Math.floor(Math.random()*10+1),D.dataList=V(),T.dataList=A(),P(),w()}catch(o){console.error("获取数据失败:",o)}},O=()=>{var t;(t=_.value)==null||t.resetForm(),h()},q=()=>{console.log("导出压力简报")};return U(async()=>{var t;try{const r=(await ot({page:1,size:999,type:"压力监测站",projectId:(t=W().selectedProject)==null?void 0:t.value})).data.data;e.stationList=r.map(a=>({label:a.name,value:a.id}))}catch(o){console.error("获取监测站列表失败:",o)}E(),h(),window.addEventListener("resize",()=>{c==null||c.resize(),d==null||d.resize()})}),I(()=>{c==null||c.dispose(),d==null||d.dispose(),window.removeEventListener("resize",()=>{})}),(t,o)=>{const r=et,a=Z,s=tt;return J(),K("div",rt,[i(r,{ref_key:"refSearch",ref:_,config:f},null,8,["config"]),i(at,{class:"card",title:" "},{title:m(()=>[n("div",st,[n("span",nt,u(L()),1),n("span",lt,"报表时间："+u(S()),1)])]),default:m(()=>[n("div",it,[n("div",ct,[i(s,{gutter:20},{default:m(()=>[i(a,{span:6},{default:m(()=>[n("div",dt,[n("div",mt,u(p.currentStatus),1),o[0]||(o[0]=n("div",{class:"stat-label"},"当前状态",-1))])]),_:1}),i(a,{span:6},{default:m(()=>[n("div",pt,[n("div",ut,u(p.maxPressure)+"MPa",1),o[1]||(o[1]=n("div",{class:"stat-label"},"最大压力",-1))])]),_:1}),i(a,{span:6},{default:m(()=>[n("div",ft,[n("div",ht,u(p.minPressure)+"MPa",1),o[2]||(o[2]=n("div",{class:"stat-label"},"最小压力",-1))])]),_:1}),i(a,{span:6},{default:m(()=>[n("div",yt,[n("div",gt,u(p.alarmCount)+"次",1),o[3]||(o[3]=n("div",{class:"stat-label"},"报警次数",-1))])]),_:1})]),_:1})]),n("div",_t,[i(s,{gutter:20},{default:m(()=>[i(a,{span:12},{default:m(()=>[n("div",bt,[n("h3",null,"管网压力"+u(k())+" - 管压趋势",1),n("div",Mt,"日期 "+u(S()),1),n("div",{ref_key:"pressureTrendChart",ref:v,class:"chart"},null,512)])]),_:1}),i(a,{span:12},{default:m(()=>[n("div",vt,[o[4]||(o[4]=n("h3",null,"报警次数",-1)),n("div",{ref_key:"alarmChart",ref:Y,class:"chart"},null,512)])]),_:1})]),_:1})]),n("div",Yt,[i(R,{config:D},null,8,["config"])]),n("div",xt,[i(R,{config:T},null,8,["config"])])])]),_:1})])}}},Ft=G(Dt,[["__scopeId","data-v-64ac7bec"]]);export{Ft as default};
