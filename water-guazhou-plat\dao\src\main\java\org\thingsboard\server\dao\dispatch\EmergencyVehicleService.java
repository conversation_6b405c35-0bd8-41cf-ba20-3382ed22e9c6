package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyVehicle;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehiclePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehicleSaveRequest;

public interface EmergencyVehicleService {
    /**
     * 分页条件查询应急车辆
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<EmergencyVehicle> findAllConditional(EmergencyVehiclePageRequest request);

    /**
     * 保存应急车辆
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    EmergencyVehicle save(EmergencyVehicleSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(EmergencyVehicle entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
