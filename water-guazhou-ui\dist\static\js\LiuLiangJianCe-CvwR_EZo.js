import{_ as d}from"./index-BlG8PIOK.js";import{d as c,g as t,n as o,p as a,q as _,F as i,aB as m,aJ as v,bh as s,C as f}from"./index-r0dFAfgr.js";const g={class:"card"},C=c({__name:"LiuLiangJianCe",setup(L){const l=[{name:"sds",value:"3434",date:"2022-11-22"},{name:"sds",value:"3434",date:"2022-11-22"},{name:"sds",value:"3434",date:"2022-11-22"}],p={step:.2};return(x,e)=>{const u=d;return t(),o("div",g,[e[0]||(e[0]=a("div",{class:"title card"},[a("span",null,"监测点名称"),a("span",null,"正瞬值"),a("span",null,"负瞬值"),a("span",null,"正累计"),a("span",null,"负累计"),a("span",null,"读取时间")],-1)),_(u,{data:l,"class-option":p,class:"warp"},{default:i(()=>[a("div",null,[(t(),o(m,null,v(l,(n,r)=>a("li",{key:r,class:"title"},[a("span",null,s(n.name),1),a("span",null,s(n.value),1),a("span",null,s(n.date),1),a("span",null,s(n.name),1),a("span",null,s(n.value),1),a("span",null,s(n.date),1)])),64))])]),_:1})])}}}),J=f(C,[["__scopeId","data-v-63295302"]]);export{J as default};
