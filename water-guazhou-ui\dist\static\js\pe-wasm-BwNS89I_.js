function b_(e,J){for(var Z=0;Z<J.length;Z++){const x=J[Z];if(typeof x!="string"&&!Array.isArray(x)){for(const w in x)if(w!=="default"&&!(w in e)){const B=Object.getOwnPropertyDescriptor(x,w);B&&Object.defineProperty(e,w,B.get?B:{enumerable:!0,get:()=>x[w]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var Pr,_e,yr,ue={};Pr={get exports(){return ue},set exports(e){ue=e}},_e=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0,typeof __filename<"u"&&(_e=_e||__filename),yr=function(e){var J,Z;(e=(e=e||{})!==void 0?e:{}).ready=new Promise(function(t,n){J=t,Z=n});var x,w,B,de=Object.assign({},e),ce="./this.program",Ee=typeof window=="object",K=typeof importScripts=="function",be=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",Y="";function mr(t){return e.locateFile?e.locateFile(t,Y):Y+t}if(be){var Oe=require("fs"),ge=require("path");Y=K?ge.dirname(Y)+"/":__dirname+"/",x=(t,n)=>(t=ae(t)?new URL(t):ge.normalize(t),Oe.readFileSync(t,n?void 0:"utf8")),B=t=>{var n=x(t,!0);return n.buffer||(n=new Uint8Array(n)),n},w=(t,n,_)=>{t=ae(t)?new URL(t):ge.normalize(t),Oe.readFile(t,function(o,p){o?_(o):n(p.buffer)})},process.argv.length>1&&(ce=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",function(t){if(!(t instanceof Dr))throw t}),process.on("unhandledRejection",function(t){throw t}),e.inspect=function(){return"[Emscripten Module object]"}}else(Ee||K)&&(K?Y=self.location.href:typeof document<"u"&&document.currentScript&&(Y=document.currentScript.src),_e&&(Y=_e),Y=Y.indexOf("blob:")!==0?Y.substr(0,Y.replace(/[?#].*/,"").lastIndexOf("/")+1):"",x=t=>{var n=new XMLHttpRequest;return n.open("GET",t,!1),n.send(null),n.responseText},K&&(B=t=>{var n=new XMLHttpRequest;return n.open("GET",t,!1),n.responseType="arraybuffer",n.send(null),new Uint8Array(n.response)}),w=(t,n,_)=>{var o=new XMLHttpRequest;o.open("GET",t,!0),o.responseType="arraybuffer",o.onload=()=>{o.status==200||o.status==0&&o.response?n(o.response):_()},o.onerror=_,o.send(null)});var $,Pe,fr=e.print||console.log.bind(console),q=e.printErr||console.warn.bind(console);Object.assign(e,de),de=null,e.arguments&&e.arguments,e.thisProgram&&(ce=e.thisProgram),e.quit&&e.quit,e.wasmBinary&&($=e.wasmBinary),e.noExitRuntime,typeof WebAssembly!="object"&&k("no native wasm support detected");var Te=!1;function ye(t,n){t||k(n)}var Se,H,Q,oe,h,G,pe,ie,Ne=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function he(t,n,_){for(var o=n+_,p=n;t[p]&&!(p>=o);)++p;if(p-n>16&&t.buffer&&Ne)return Ne.decode(t.subarray(n,p));for(var a="";n<p;){var u=t[n++];if(128&u){var c=63&t[n++];if((224&u)!=192){var W=63&t[n++];if((u=(240&u)==224?(15&u)<<12|c<<6|W:(7&u)<<18|c<<12|W<<6|63&t[n++])<65536)a+=String.fromCharCode(u);else{var re=u-65536;a+=String.fromCharCode(55296|re>>10,56320|1023&re)}}else a+=String.fromCharCode((31&u)<<6|c)}else a+=String.fromCharCode(u)}return a}function L(t,n){return t?he(Q,t,n):""}function Me(t,n,_,o){if(!(o>0))return 0;for(var p=_,a=_+o-1,u=0;u<t.length;++u){var c=t.charCodeAt(u);if(c>=55296&&c<=57343&&(c=65536+((1023&c)<<10)|1023&t.charCodeAt(++u)),c<=127){if(_>=a)break;n[_++]=c}else if(c<=2047){if(_+1>=a)break;n[_++]=192|c>>6,n[_++]=128|63&c}else if(c<=65535){if(_+2>=a)break;n[_++]=224|c>>12,n[_++]=128|c>>6&63,n[_++]=128|63&c}else{if(_+3>=a)break;n[_++]=240|c>>18,n[_++]=128|c>>12&63,n[_++]=128|c>>6&63,n[_++]=128|63&c}}return n[_]=0,_-p}function ve(t){for(var n=0,_=0;_<t.length;++_){var o=t.charCodeAt(_);o<=127?n++:o<=2047?n+=2:o>=55296&&o<=57343?(n+=4,++_):n+=3}return n}function De(t){Se=t,e.HEAP8=H=new Int8Array(t),e.HEAP16=oe=new Int16Array(t),e.HEAP32=h=new Int32Array(t),e.HEAPU8=Q=new Uint8Array(t),e.HEAPU16=new Uint16Array(t),e.HEAPU32=G=new Uint32Array(t),e.HEAPF32=pe=new Float32Array(t),e.HEAPF64=ie=new Float64Array(t)}e.INITIAL_MEMORY;var Re=[],Ae=[],Ge=[];function lr(){if(e.preRun)for(typeof e.preRun=="function"&&(e.preRun=[e.preRun]);e.preRun.length;)br(e.preRun.shift());me(Re)}function dr(){me(Ae)}function Er(){if(e.postRun)for(typeof e.postRun=="function"&&(e.postRun=[e.postRun]);e.postRun.length;)Tr(e.postRun.shift());me(Ge)}function br(t){Re.unshift(t)}function Or(t){Ae.unshift(t)}function Tr(t){Ge.unshift(t)}var V=0,ee=null;function Sr(t){V++,e.monitorRunDependencies&&e.monitorRunDependencies(V)}function Nr(t){if(V--,e.monitorRunDependencies&&e.monitorRunDependencies(V),V==0&&ee){var n=ee;ee=null,n()}}function k(t){e.onAbort&&e.onAbort(t),q(t="Aborted("+t+")"),Te=!0,t+=". Build with -sASSERTIONS for more info.";var n=new WebAssembly.RuntimeError(t);throw Z(n),n}var I,hr="data:application/octet-stream;base64,";function Ce(t){return t.startsWith(hr)}function ae(t){return t.startsWith("file://")}function Ie(t){try{if(t==I&&$)return new Uint8Array($);if(B)return B(t);throw"both async and sync fetching of the wasm failed"}catch(n){k(n)}}function Mr(){if(!$&&(Ee||K)){if(typeof fetch=="function"&&!ae(I))return fetch(I,{credentials:"same-origin"}).then(function(t){if(!t.ok)throw"failed to load wasm binary file at '"+I+"'";return t.arrayBuffer()}).catch(function(){return Ie(I)});if(w)return new Promise(function(t,n){w(I,function(_){t(new Uint8Array(_))},n)})}return Promise.resolve().then(function(){return Ie(I)})}function vr(){var t={a:i_};function n(a,u){var c=a.exports;e.asm=c,De((Pe=e.asm.t).buffer),e.asm.Yb,Or(e.asm.u),Nr()}function _(a){n(a.instance)}function o(a){return Mr().then(function(u){return WebAssembly.instantiate(u,t)}).then(function(u){return u}).then(a,function(u){q("failed to asynchronously prepare wasm: "+u),k(u)})}function p(){return $||typeof WebAssembly.instantiateStreaming!="function"||Ce(I)||ae(I)||be||typeof fetch!="function"?o(_):fetch(I,{credentials:"same-origin"}).then(function(a){return WebAssembly.instantiateStreaming(a,t).then(_,function(u){return q("wasm streaming compile failed: "+u),q("falling back to ArrayBuffer instantiation"),o(_)})})}if(Sr(),e.instantiateWasm)try{return e.instantiateWasm(t,n)}catch(a){q("Module.instantiateWasm callback failed with error: "+a),Z(a)}return p().catch(Z),{}}function Dr(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function me(t){for(;t.length>0;)t.shift()(e)}function Rr(t,n="i8"){switch(n.endsWith("*")&&(n="*"),n){case"i1":case"i8":return H[t>>0];case"i16":return oe[t>>1];case"i32":case"i64":return h[t>>2];case"float":return pe[t>>2];case"double":return ie[t>>3];case"*":return G[t>>2];default:k("invalid type for getValue: "+n)}return null}function Ar(t,n,_){return 0}function Gr(t,n,_){}function Cr(t,n,_){return 0}function Ir(t,n,_,o){}function jr(t){}function Lr(t,n){}function Ur(t,n,_){}function Yr(t){return G[t>>2]+4294967296*h[t+4>>2]}function Fr(t){return t%4==0&&(t%100!=0||t%400==0)}Ce(I="pe-wasm.wasm")||(I=mr(I));var wr=[0,31,60,91,121,152,182,213,244,274,305,335],xr=[0,31,59,90,120,151,181,212,243,273,304,334];function Hr(t){return(Fr(t.getFullYear())?wr:xr)[t.getMonth()]+t.getDate()-1}function Xr(t,n){var _=new Date(1e3*Yr(t));h[n>>2]=_.getSeconds(),h[n+4>>2]=_.getMinutes(),h[n+8>>2]=_.getHours(),h[n+12>>2]=_.getDate(),h[n+16>>2]=_.getMonth(),h[n+20>>2]=_.getFullYear()-1900,h[n+24>>2]=_.getDay();var o=0|Hr(_);h[n+28>>2]=o,h[n+36>>2]=-60*_.getTimezoneOffset();var p=new Date(_.getFullYear(),0,1),a=new Date(_.getFullYear(),6,1).getTimezoneOffset(),u=p.getTimezoneOffset(),c=0|(a!=u&&_.getTimezoneOffset()==Math.min(u,a));h[n+32>>2]=c}function je(t){var n=ve(t)+1,_=ar(n);return _&&Me(t,H,_,n),_}function zr(t,n,_){var o=new Date().getFullYear(),p=new Date(o,0,1),a=new Date(o,6,1),u=p.getTimezoneOffset(),c=a.getTimezoneOffset(),W=Math.max(u,c);function re(E_){var gr=E_.toTimeString().match(/\(([A-Za-z ]+)\)$/);return gr?gr[1]:"GMT"}G[t>>2]=60*W,h[n>>2]=+(u!=c);var l_=re(p),d_=re(a),ur=je(l_),cr=je(d_);c<u?(G[_>>2]=ur,G[_+4>>2]=cr):(G[_>>2]=cr,G[_+4>>2]=ur)}function Zr(){k("")}function Br(){return Date.now()}function Wr(t,n,_){Q.copyWithin(t,n,n+_)}function Vr(){return 2147483648}function qr(t){try{return Pe.grow(t-Se.byteLength+65535>>>16),De(Pe.buffer),1}catch{}}function kr(t){var n=Q.length;t>>>=0;var _=Vr();if(t>_)return!1;let o=(u,c)=>u+(c-u%c)%c;for(var p=1;p<=4;p*=2){var a=n*(1+.2/p);if(a=Math.min(a,t+100663296),qr(Math.min(_,o(Math.max(t,a),65536))))return!0}return!1}var fe={};function Jr(){return ce||"./this.program"}function te(){if(!te.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:Jr()};for(var n in fe)fe[n]===void 0?delete t[n]:t[n]=fe[n];var _=[];for(var n in t)_.push(n+"="+t[n]);te.strings=_}return te.strings}function Kr(t,n,_){for(var o=0;o<t.length;++o)H[n++>>0]=t.charCodeAt(o);H[n>>0]=0}function $r(t,n){var _=0;return te().forEach(function(o,p){var a=n+_;G[t+4*p>>2]=a,Kr(o,a),_+=o.length+1}),0}function Qr(t,n){var _=te();G[t>>2]=_.length;var o=0;return _.forEach(function(p){o+=p.length+1}),G[n>>2]=o,0}function e_(t){return 52}function t_(t,n,_,o){return 52}function n_(t,n,_,o,p){return 70}var r_=[null,[],[]];function __(t,n){var _=r_[t];n===0||n===10?((t===1?fr:q)(he(_,0)),_.length=0):_.push(n)}function o_(t,n,_,o){for(var p=0,a=0;a<_;a++){var u=G[n>>2],c=G[n+4>>2];n+=8;for(var W=0;W<c;W++)__(t,Q[u+W]);p+=c}return G[o>>2]=p,0}function p_(t,n,_){var o=ve(t)+1,p=new Array(o);return Me(t,p,0,p.length),p}var i_={c:Ar,p:Gr,f:Cr,d:Ir,n:jr,m:Lr,o:Ur,h:Xr,i:zr,k:Zr,g:Br,s:Wr,l:kr,q:$r,r:Qr,a:e_,e:t_,j:n_,b:o_};vr(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.u).apply(null,arguments)};var Le=e._emscripten_bind_PeObject_getCode_0=function(){return(Le=e._emscripten_bind_PeObject_getCode_0=e.asm.v).apply(null,arguments)},Ue=e._emscripten_bind_PeObject_getName_1=function(){return(Ue=e._emscripten_bind_PeObject_getName_1=e.asm.w).apply(null,arguments)},Ye=e._emscripten_bind_PeObject_getType_0=function(){return(Ye=e._emscripten_bind_PeObject_getType_0=e.asm.x).apply(null,arguments)},Fe=e._emscripten_bind_PeCoordsys_getCode_0=function(){return(Fe=e._emscripten_bind_PeCoordsys_getCode_0=e.asm.y).apply(null,arguments)},we=e._emscripten_bind_PeCoordsys_getName_1=function(){return(we=e._emscripten_bind_PeCoordsys_getName_1=e.asm.z).apply(null,arguments)},xe=e._emscripten_bind_PeCoordsys_getType_0=function(){return(xe=e._emscripten_bind_PeCoordsys_getType_0=e.asm.A).apply(null,arguments)},He=e._emscripten_bind_VoidPtr___destroy___0=function(){return(He=e._emscripten_bind_VoidPtr___destroy___0=e.asm.B).apply(null,arguments)},Xe=e._emscripten_bind_PeDatum_getSpheroid_0=function(){return(Xe=e._emscripten_bind_PeDatum_getSpheroid_0=e.asm.C).apply(null,arguments)},ze=e._emscripten_bind_PeDatum_getCode_0=function(){return(ze=e._emscripten_bind_PeDatum_getCode_0=e.asm.D).apply(null,arguments)},Ze=e._emscripten_bind_PeDatum_getName_1=function(){return(Ze=e._emscripten_bind_PeDatum_getName_1=e.asm.E).apply(null,arguments)},Be=e._emscripten_bind_PeDatum_getType_0=function(){return(Be=e._emscripten_bind_PeDatum_getType_0=e.asm.F).apply(null,arguments)},We=e._emscripten_bind_PeDefs_get_PE_BUFFER_MAX_0=function(){return(We=e._emscripten_bind_PeDefs_get_PE_BUFFER_MAX_0=e.asm.G).apply(null,arguments)},Ve=e._emscripten_bind_PeDefs_get_PE_NAME_MAX_0=function(){return(Ve=e._emscripten_bind_PeDefs_get_PE_NAME_MAX_0=e.asm.H).apply(null,arguments)},qe=e._emscripten_bind_PeDefs_get_PE_MGRS_MAX_0=function(){return(qe=e._emscripten_bind_PeDefs_get_PE_MGRS_MAX_0=e.asm.I).apply(null,arguments)},ke=e._emscripten_bind_PeDefs_get_PE_USNG_MAX_0=function(){return(ke=e._emscripten_bind_PeDefs_get_PE_USNG_MAX_0=e.asm.J).apply(null,arguments)},Je=e._emscripten_bind_PeDefs_get_PE_DD_MAX_0=function(){return(Je=e._emscripten_bind_PeDefs_get_PE_DD_MAX_0=e.asm.K).apply(null,arguments)},Ke=e._emscripten_bind_PeDefs_get_PE_DMS_MAX_0=function(){return(Ke=e._emscripten_bind_PeDefs_get_PE_DMS_MAX_0=e.asm.L).apply(null,arguments)},$e=e._emscripten_bind_PeDefs_get_PE_DDM_MAX_0=function(){return($e=e._emscripten_bind_PeDefs_get_PE_DDM_MAX_0=e.asm.M).apply(null,arguments)},Qe=e._emscripten_bind_PeDefs_get_PE_UTM_MAX_0=function(){return(Qe=e._emscripten_bind_PeDefs_get_PE_UTM_MAX_0=e.asm.N).apply(null,arguments)},et=e._emscripten_bind_PeDefs_get_PE_PARM_MAX_0=function(){return(et=e._emscripten_bind_PeDefs_get_PE_PARM_MAX_0=e.asm.O).apply(null,arguments)},tt=e._emscripten_bind_PeDefs_get_PE_TYPE_NONE_0=function(){return(tt=e._emscripten_bind_PeDefs_get_PE_TYPE_NONE_0=e.asm.P).apply(null,arguments)},nt=e._emscripten_bind_PeDefs_get_PE_TYPE_GEOGCS_0=function(){return(nt=e._emscripten_bind_PeDefs_get_PE_TYPE_GEOGCS_0=e.asm.Q).apply(null,arguments)},rt=e._emscripten_bind_PeDefs_get_PE_TYPE_PROJCS_0=function(){return(rt=e._emscripten_bind_PeDefs_get_PE_TYPE_PROJCS_0=e.asm.R).apply(null,arguments)},_t=e._emscripten_bind_PeDefs_get_PE_TYPE_GEOGTRAN_0=function(){return(_t=e._emscripten_bind_PeDefs_get_PE_TYPE_GEOGTRAN_0=e.asm.S).apply(null,arguments)},ot=e._emscripten_bind_PeDefs_get_PE_TYPE_COORDSYS_0=function(){return(ot=e._emscripten_bind_PeDefs_get_PE_TYPE_COORDSYS_0=e.asm.T).apply(null,arguments)},pt=e._emscripten_bind_PeDefs_get_PE_TYPE_UNIT_0=function(){return(pt=e._emscripten_bind_PeDefs_get_PE_TYPE_UNIT_0=e.asm.U).apply(null,arguments)},it=e._emscripten_bind_PeDefs_get_PE_TYPE_LINUNIT_0=function(){return(it=e._emscripten_bind_PeDefs_get_PE_TYPE_LINUNIT_0=e.asm.V).apply(null,arguments)},at=e._emscripten_bind_PeDefs_get_PE_STR_OPTS_NONE_0=function(){return(at=e._emscripten_bind_PeDefs_get_PE_STR_OPTS_NONE_0=e.asm.W).apply(null,arguments)},st=e._emscripten_bind_PeDefs_get_PE_STR_AUTH_NONE_0=function(){return(st=e._emscripten_bind_PeDefs_get_PE_STR_AUTH_NONE_0=e.asm.X).apply(null,arguments)},ut=e._emscripten_bind_PeDefs_get_PE_STR_AUTH_TOP_0=function(){return(ut=e._emscripten_bind_PeDefs_get_PE_STR_AUTH_TOP_0=e.asm.Y).apply(null,arguments)},ct=e._emscripten_bind_PeDefs_get_PE_STR_NAME_CANON_0=function(){return(ct=e._emscripten_bind_PeDefs_get_PE_STR_NAME_CANON_0=e.asm.Z).apply(null,arguments)},gt=e._emscripten_bind_PeDefs_get_PE_PARM_X0_0=function(){return(gt=e._emscripten_bind_PeDefs_get_PE_PARM_X0_0=e.asm._).apply(null,arguments)},Pt=e._emscripten_bind_PeDefs_get_PE_PARM_ND_0=function(){return(Pt=e._emscripten_bind_PeDefs_get_PE_PARM_ND_0=e.asm.$).apply(null,arguments)},yt=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_1_TO_2_0=function(){return(yt=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_1_TO_2_0=e.asm.aa).apply(null,arguments)},mt=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_2_TO_1_0=function(){return(mt=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_2_TO_1_0=e.asm.ba).apply(null,arguments)},ft=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_P_TO_G_0=function(){return(ft=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_P_TO_G_0=e.asm.ca).apply(null,arguments)},lt=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_G_TO_P_0=function(){return(lt=e._emscripten_bind_PeDefs_get_PE_TRANSFORM_G_TO_P_0=e.asm.da).apply(null,arguments)},dt=e._emscripten_bind_PeDefs_get_PE_HORIZON_RECT_0=function(){return(dt=e._emscripten_bind_PeDefs_get_PE_HORIZON_RECT_0=e.asm.ea).apply(null,arguments)},Et=e._emscripten_bind_PeDefs_get_PE_HORIZON_POLY_0=function(){return(Et=e._emscripten_bind_PeDefs_get_PE_HORIZON_POLY_0=e.asm.fa).apply(null,arguments)},bt=e._emscripten_bind_PeDefs_get_PE_HORIZON_LINE_0=function(){return(bt=e._emscripten_bind_PeDefs_get_PE_HORIZON_LINE_0=e.asm.ga).apply(null,arguments)},Ot=e._emscripten_bind_PeDefs_get_PE_HORIZON_DELTA_0=function(){return(Ot=e._emscripten_bind_PeDefs_get_PE_HORIZON_DELTA_0=e.asm.ha).apply(null,arguments)},Tt=e._emscripten_bind_PeFactory_initialize_1=function(){return(Tt=e._emscripten_bind_PeFactory_initialize_1=e.asm.ia).apply(null,arguments)},St=e._emscripten_bind_PeFactory_factoryByType_2=function(){return(St=e._emscripten_bind_PeFactory_factoryByType_2=e.asm.ja).apply(null,arguments)},Nt=e._emscripten_bind_PeFactory_fromString_2=function(){return(Nt=e._emscripten_bind_PeFactory_fromString_2=e.asm.ka).apply(null,arguments)},ht=e._emscripten_bind_PeFactory_getCode_1=function(){return(ht=e._emscripten_bind_PeFactory_getCode_1=e.asm.la).apply(null,arguments)},Mt=e._emscripten_bind_PeGCSExtent_PeGCSExtent_6=function(){return(Mt=e._emscripten_bind_PeGCSExtent_PeGCSExtent_6=e.asm.ma).apply(null,arguments)},vt=e._emscripten_bind_PeGCSExtent_getLLon_0=function(){return(vt=e._emscripten_bind_PeGCSExtent_getLLon_0=e.asm.na).apply(null,arguments)},Dt=e._emscripten_bind_PeGCSExtent_getSLat_0=function(){return(Dt=e._emscripten_bind_PeGCSExtent_getSLat_0=e.asm.oa).apply(null,arguments)},Rt=e._emscripten_bind_PeGCSExtent_getRLon_0=function(){return(Rt=e._emscripten_bind_PeGCSExtent_getRLon_0=e.asm.pa).apply(null,arguments)},At=e._emscripten_bind_PeGCSExtent_getNLat_0=function(){return(At=e._emscripten_bind_PeGCSExtent_getNLat_0=e.asm.qa).apply(null,arguments)},Gt=e._emscripten_bind_PeGCSExtent___destroy___0=function(){return(Gt=e._emscripten_bind_PeGCSExtent___destroy___0=e.asm.ra).apply(null,arguments)},Ct=e._emscripten_bind_PeGeogcs_getDatum_0=function(){return(Ct=e._emscripten_bind_PeGeogcs_getDatum_0=e.asm.sa).apply(null,arguments)},It=e._emscripten_bind_PeGeogcs_getPrimem_0=function(){return(It=e._emscripten_bind_PeGeogcs_getPrimem_0=e.asm.ta).apply(null,arguments)},jt=e._emscripten_bind_PeGeogcs_getUnit_0=function(){return(jt=e._emscripten_bind_PeGeogcs_getUnit_0=e.asm.ua).apply(null,arguments)},Lt=e._emscripten_bind_PeGeogcs_getCode_0=function(){return(Lt=e._emscripten_bind_PeGeogcs_getCode_0=e.asm.va).apply(null,arguments)},Ut=e._emscripten_bind_PeGeogcs_getName_1=function(){return(Ut=e._emscripten_bind_PeGeogcs_getName_1=e.asm.wa).apply(null,arguments)},Yt=e._emscripten_bind_PeGeogcs_getType_0=function(){return(Yt=e._emscripten_bind_PeGeogcs_getType_0=e.asm.xa).apply(null,arguments)},Ft=e._emscripten_bind_PeGeogtran_isEqual_1=function(){return(Ft=e._emscripten_bind_PeGeogtran_isEqual_1=e.asm.ya).apply(null,arguments)},wt=e._emscripten_bind_PeGeogtran_getGeogcs1_0=function(){return(wt=e._emscripten_bind_PeGeogtran_getGeogcs1_0=e.asm.za).apply(null,arguments)},xt=e._emscripten_bind_PeGeogtran_getGeogcs2_0=function(){return(xt=e._emscripten_bind_PeGeogtran_getGeogcs2_0=e.asm.Aa).apply(null,arguments)},Ht=e._emscripten_bind_PeGeogtran_getParameters_0=function(){return(Ht=e._emscripten_bind_PeGeogtran_getParameters_0=e.asm.Ba).apply(null,arguments)},Xt=e._emscripten_bind_PeGeogtran_loadConstants_0=function(){return(Xt=e._emscripten_bind_PeGeogtran_loadConstants_0=e.asm.Ca).apply(null,arguments)},zt=e._emscripten_bind_PeGeogtran_getCode_0=function(){return(zt=e._emscripten_bind_PeGeogtran_getCode_0=e.asm.Da).apply(null,arguments)},Zt=e._emscripten_bind_PeGeogtran_getName_1=function(){return(Zt=e._emscripten_bind_PeGeogtran_getName_1=e.asm.Ea).apply(null,arguments)},Bt=e._emscripten_bind_PeGeogtran_getType_0=function(){return(Bt=e._emscripten_bind_PeGeogtran_getType_0=e.asm.Fa).apply(null,arguments)},Wt=e._emscripten_bind_PeGTlistExtended_getGTlist_6=function(){return(Wt=e._emscripten_bind_PeGTlistExtended_getGTlist_6=e.asm.Ga).apply(null,arguments)},Vt=e._emscripten_bind_PeGTlistExtended_get_PE_GTLIST_OPTS_COMMON_0=function(){return(Vt=e._emscripten_bind_PeGTlistExtended_get_PE_GTLIST_OPTS_COMMON_0=e.asm.Ha).apply(null,arguments)},qt=e._emscripten_bind_PeGTlistExtendedEntry_getEntries_0=function(){return(qt=e._emscripten_bind_PeGTlistExtendedEntry_getEntries_0=e.asm.Ia).apply(null,arguments)},kt=e._emscripten_bind_PeGTlistExtendedEntry_getSteps_0=function(){return(kt=e._emscripten_bind_PeGTlistExtendedEntry_getSteps_0=e.asm.Ja).apply(null,arguments)},Jt=e._emscripten_bind_PeGTlistExtendedEntry_Delete_1=function(){return(Jt=e._emscripten_bind_PeGTlistExtendedEntry_Delete_1=e.asm.Ka).apply(null,arguments)},Kt=e._emscripten_bind_PeGTlistExtendedGTs_getDirection_0=function(){return(Kt=e._emscripten_bind_PeGTlistExtendedGTs_getDirection_0=e.asm.La).apply(null,arguments)},$t=e._emscripten_bind_PeGTlistExtendedGTs_getGeogtran_0=function(){return($t=e._emscripten_bind_PeGTlistExtendedGTs_getGeogtran_0=e.asm.Ma).apply(null,arguments)},Qt=e._emscripten_bind_PeHorizon_getNump_0=function(){return(Qt=e._emscripten_bind_PeHorizon_getNump_0=e.asm.Na).apply(null,arguments)},en=e._emscripten_bind_PeHorizon_getKind_0=function(){return(en=e._emscripten_bind_PeHorizon_getKind_0=e.asm.Oa).apply(null,arguments)},tn=e._emscripten_bind_PeHorizon_getInclusive_0=function(){return(tn=e._emscripten_bind_PeHorizon_getInclusive_0=e.asm.Pa).apply(null,arguments)},nn=e._emscripten_bind_PeHorizon_getSize_0=function(){return(nn=e._emscripten_bind_PeHorizon_getSize_0=e.asm.Qa).apply(null,arguments)},rn=e._emscripten_bind_PeHorizon_getCoord_0=function(){return(rn=e._emscripten_bind_PeHorizon_getCoord_0=e.asm.Ra).apply(null,arguments)},_n=e._emscripten_bind_PeInteger_PeInteger_1=function(){return(_n=e._emscripten_bind_PeInteger_PeInteger_1=e.asm.Sa).apply(null,arguments)},on=e._emscripten_bind_PeInteger_get_val_0=function(){return(on=e._emscripten_bind_PeInteger_get_val_0=e.asm.Ta).apply(null,arguments)},pn=e._emscripten_bind_PeInteger_set_val_1=function(){return(pn=e._emscripten_bind_PeInteger_set_val_1=e.asm.Ua).apply(null,arguments)},an=e._emscripten_bind_PeInteger___destroy___0=function(){return(an=e._emscripten_bind_PeInteger___destroy___0=e.asm.Va).apply(null,arguments)},sn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_NEW_0=function(){return(sn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_NEW_0=e.asm.Wa).apply(null,arguments)},un=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_OLD_0=function(){return(un=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_OLD_0=e.asm.Xa).apply(null,arguments)},cn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_AUTO_0=function(){return(cn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_AUTO_0=e.asm.Ya).apply(null,arguments)},gn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_180_ZONE_1_PLUS_0=function(){return(gn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_180_ZONE_1_PLUS_0=e.asm.Za).apply(null,arguments)},Pn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_ADD_SPACES_0=function(){return(Pn=e._emscripten_bind_PeNotationMgrs_get_PE_MGRS_ADD_SPACES_0=e.asm._a).apply(null,arguments)},yn=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NONE_0=function(){return(yn=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NONE_0=e.asm.$a).apply(null,arguments)},mn=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_0=function(){return(mn=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_0=e.asm.ab).apply(null,arguments)},fn=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_STRICT_0=function(){return(fn=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_STRICT_0=e.asm.bb).apply(null,arguments)},ln=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_ADD_SPACES_0=function(){return(ln=e._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_ADD_SPACES_0=e.asm.cb).apply(null,arguments)},dn=e._emscripten_bind_PeParameter_getValue_0=function(){return(dn=e._emscripten_bind_PeParameter_getValue_0=e.asm.db).apply(null,arguments)},En=e._emscripten_bind_PeParameter_getCode_0=function(){return(En=e._emscripten_bind_PeParameter_getCode_0=e.asm.eb).apply(null,arguments)},bn=e._emscripten_bind_PeParameter_getName_1=function(){return(bn=e._emscripten_bind_PeParameter_getName_1=e.asm.fb).apply(null,arguments)},On=e._emscripten_bind_PeParameter_getType_0=function(){return(On=e._emscripten_bind_PeParameter_getType_0=e.asm.gb).apply(null,arguments)},Tn=e._emscripten_bind_PePCSInfo_getCentralMeridian_0=function(){return(Tn=e._emscripten_bind_PePCSInfo_getCentralMeridian_0=e.asm.hb).apply(null,arguments)},Sn=e._emscripten_bind_PePCSInfo_getDomainMinx_0=function(){return(Sn=e._emscripten_bind_PePCSInfo_getDomainMinx_0=e.asm.ib).apply(null,arguments)},Nn=e._emscripten_bind_PePCSInfo_getDomainMiny_0=function(){return(Nn=e._emscripten_bind_PePCSInfo_getDomainMiny_0=e.asm.jb).apply(null,arguments)},hn=e._emscripten_bind_PePCSInfo_getDomainMaxx_0=function(){return(hn=e._emscripten_bind_PePCSInfo_getDomainMaxx_0=e.asm.kb).apply(null,arguments)},Mn=e._emscripten_bind_PePCSInfo_getDomainMaxy_0=function(){return(Mn=e._emscripten_bind_PePCSInfo_getDomainMaxy_0=e.asm.lb).apply(null,arguments)},vn=e._emscripten_bind_PePCSInfo_getNorthPoleLocation_0=function(){return(vn=e._emscripten_bind_PePCSInfo_getNorthPoleLocation_0=e.asm.mb).apply(null,arguments)},Dn=e._emscripten_bind_PePCSInfo_getNorthPoleGeometry_0=function(){return(Dn=e._emscripten_bind_PePCSInfo_getNorthPoleGeometry_0=e.asm.nb).apply(null,arguments)},Rn=e._emscripten_bind_PePCSInfo_getSouthPoleLocation_0=function(){return(Rn=e._emscripten_bind_PePCSInfo_getSouthPoleLocation_0=e.asm.ob).apply(null,arguments)},An=e._emscripten_bind_PePCSInfo_getSouthPoleGeometry_0=function(){return(An=e._emscripten_bind_PePCSInfo_getSouthPoleGeometry_0=e.asm.pb).apply(null,arguments)},Gn=e._emscripten_bind_PePCSInfo_isDensificationNeeded_0=function(){return(Gn=e._emscripten_bind_PePCSInfo_isDensificationNeeded_0=e.asm.qb).apply(null,arguments)},Cn=e._emscripten_bind_PePCSInfo_isGcsHorizonMultiOverlap_0=function(){return(Cn=e._emscripten_bind_PePCSInfo_isGcsHorizonMultiOverlap_0=e.asm.rb).apply(null,arguments)},In=e._emscripten_bind_PePCSInfo_isPannableRectangle_0=function(){return(In=e._emscripten_bind_PePCSInfo_isPannableRectangle_0=e.asm.sb).apply(null,arguments)},jn=e._emscripten_bind_PePCSInfo_generate_2=function(){return(jn=e._emscripten_bind_PePCSInfo_generate_2=e.asm.tb).apply(null,arguments)},Ln=e._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_NONE_0=function(){return(Ln=e._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_NONE_0=e.asm.ub).apply(null,arguments)},Un=e._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_DOMAIN_0=function(){return(Un=e._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_DOMAIN_0=e.asm.vb).apply(null,arguments)},Yn=e._emscripten_bind_PePCSInfo_get_PE_POLE_OUTSIDE_BOUNDARY_0=function(){return(Yn=e._emscripten_bind_PePCSInfo_get_PE_POLE_OUTSIDE_BOUNDARY_0=e.asm.wb).apply(null,arguments)},Fn=e._emscripten_bind_PePCSInfo_get_PE_POLE_POINT_0=function(){return(Fn=e._emscripten_bind_PePCSInfo_get_PE_POLE_POINT_0=e.asm.xb).apply(null,arguments)},wn=e._emscripten_bind_PePrimem_getLongitude_0=function(){return(wn=e._emscripten_bind_PePrimem_getLongitude_0=e.asm.yb).apply(null,arguments)},xn=e._emscripten_bind_PePrimem_getCode_0=function(){return(xn=e._emscripten_bind_PePrimem_getCode_0=e.asm.zb).apply(null,arguments)},Hn=e._emscripten_bind_PePrimem_getName_1=function(){return(Hn=e._emscripten_bind_PePrimem_getName_1=e.asm.Ab).apply(null,arguments)},Xn=e._emscripten_bind_PePrimem_getType_0=function(){return(Xn=e._emscripten_bind_PePrimem_getType_0=e.asm.Bb).apply(null,arguments)},zn=e._emscripten_bind_PeProjcs_getGeogcs_0=function(){return(zn=e._emscripten_bind_PeProjcs_getGeogcs_0=e.asm.Cb).apply(null,arguments)},Zn=e._emscripten_bind_PeProjcs_getParameters_0=function(){return(Zn=e._emscripten_bind_PeProjcs_getParameters_0=e.asm.Db).apply(null,arguments)},Bn=e._emscripten_bind_PeProjcs_getUnit_0=function(){return(Bn=e._emscripten_bind_PeProjcs_getUnit_0=e.asm.Eb).apply(null,arguments)},Wn=e._emscripten_bind_PeProjcs_loadConstants_0=function(){return(Wn=e._emscripten_bind_PeProjcs_loadConstants_0=e.asm.Fb).apply(null,arguments)},Vn=e._emscripten_bind_PeProjcs_horizonGcsGenerate_0=function(){return(Vn=e._emscripten_bind_PeProjcs_horizonGcsGenerate_0=e.asm.Gb).apply(null,arguments)},qn=e._emscripten_bind_PeProjcs_horizonPcsGenerate_0=function(){return(qn=e._emscripten_bind_PeProjcs_horizonPcsGenerate_0=e.asm.Hb).apply(null,arguments)},kn=e._emscripten_bind_PeProjcs_getCode_0=function(){return(kn=e._emscripten_bind_PeProjcs_getCode_0=e.asm.Ib).apply(null,arguments)},Jn=e._emscripten_bind_PeProjcs_getName_1=function(){return(Jn=e._emscripten_bind_PeProjcs_getName_1=e.asm.Jb).apply(null,arguments)},Kn=e._emscripten_bind_PeProjcs_getType_0=function(){return(Kn=e._emscripten_bind_PeProjcs_getType_0=e.asm.Kb).apply(null,arguments)},$n=e._emscripten_bind_PeSpheroid_getAxis_0=function(){return($n=e._emscripten_bind_PeSpheroid_getAxis_0=e.asm.Lb).apply(null,arguments)},Qn=e._emscripten_bind_PeSpheroid_getFlattening_0=function(){return(Qn=e._emscripten_bind_PeSpheroid_getFlattening_0=e.asm.Mb).apply(null,arguments)},er=e._emscripten_bind_PeSpheroid_getCode_0=function(){return(er=e._emscripten_bind_PeSpheroid_getCode_0=e.asm.Nb).apply(null,arguments)},tr=e._emscripten_bind_PeSpheroid_getName_1=function(){return(tr=e._emscripten_bind_PeSpheroid_getName_1=e.asm.Ob).apply(null,arguments)},nr=e._emscripten_bind_PeSpheroid_getType_0=function(){return(nr=e._emscripten_bind_PeSpheroid_getType_0=e.asm.Pb).apply(null,arguments)},rr=e._emscripten_bind_PeUnit_getUnitFactor_0=function(){return(rr=e._emscripten_bind_PeUnit_getUnitFactor_0=e.asm.Qb).apply(null,arguments)},_r=e._emscripten_bind_PeUnit_getCode_0=function(){return(_r=e._emscripten_bind_PeUnit_getCode_0=e.asm.Rb).apply(null,arguments)},or=e._emscripten_bind_PeUnit_getName_1=function(){return(or=e._emscripten_bind_PeUnit_getName_1=e.asm.Sb).apply(null,arguments)},pr=e._emscripten_bind_PeUnit_getType_0=function(){return(pr=e._emscripten_bind_PeUnit_getType_0=e.asm.Tb).apply(null,arguments)},ir=e._emscripten_bind_PeVersion_version_string_0=function(){return(ir=e._emscripten_bind_PeVersion_version_string_0=e.asm.Ub).apply(null,arguments)};e._pe_getPeGTlistExtendedEntrySize=function(){return(e._pe_getPeGTlistExtendedEntrySize=e.asm.Vb).apply(null,arguments)},e._pe_getPeGTlistExtendedGTsSize=function(){return(e._pe_getPeGTlistExtendedGTsSize=e.asm.Wb).apply(null,arguments)},e._pe_getPeHorizonSize=function(){return(e._pe_getPeHorizonSize=e.asm.Xb).apply(null,arguments)},e._pe_geog_to_geog=function(){return(e._pe_geog_to_geog=e.asm.Zb).apply(null,arguments)},e._pe_geog_to_proj=function(){return(e._pe_geog_to_proj=e.asm._b).apply(null,arguments)},e._pe_geog_to_dd=function(){return(e._pe_geog_to_dd=e.asm.$b).apply(null,arguments)},e._pe_dd_to_geog=function(){return(e._pe_dd_to_geog=e.asm.ac).apply(null,arguments)},e._pe_geog_to_ddm=function(){return(e._pe_geog_to_ddm=e.asm.bc).apply(null,arguments)},e._pe_ddm_to_geog=function(){return(e._pe_ddm_to_geog=e.asm.cc).apply(null,arguments)},e._pe_geog_to_dms=function(){return(e._pe_geog_to_dms=e.asm.dc).apply(null,arguments)},e._pe_dms_to_geog=function(){return(e._pe_dms_to_geog=e.asm.ec).apply(null,arguments)},e._pe_geog_to_mgrs_extended=function(){return(e._pe_geog_to_mgrs_extended=e.asm.fc).apply(null,arguments)},e._pe_mgrs_to_geog_extended=function(){return(e._pe_mgrs_to_geog_extended=e.asm.gc).apply(null,arguments)},e._pe_geog_to_usng=function(){return(e._pe_geog_to_usng=e.asm.hc).apply(null,arguments)},e._pe_usng_to_geog=function(){return(e._pe_usng_to_geog=e.asm.ic).apply(null,arguments)},e._pe_geog_to_utm=function(){return(e._pe_geog_to_utm=e.asm.jc).apply(null,arguments)},e._pe_utm_to_geog=function(){return(e._pe_utm_to_geog=e.asm.kc).apply(null,arguments)},e._pe_object_to_string_ext=function(){return(e._pe_object_to_string_ext=e.asm.lc).apply(null,arguments)},e._pe_proj_to_geog_center=function(){return(e._pe_proj_to_geog_center=e.asm.mc).apply(null,arguments)};var se,ar=e._malloc=function(){return(ar=e._malloc=e.asm.nc).apply(null,arguments)};function sr(t){function n(){se||(se=!0,e.calledRun=!0,Te||(dr(),J(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),Er()))}V>0||(lr(),V>0||(e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),n()},1)):n()))}if(e._free=function(){return(e._free=e.asm.oc).apply(null,arguments)},e.___start_em_js=1970140,e.___stop_em_js=1970238,e.UTF8ToString=L,e.getValue=Rr,ee=function t(){se||sr(),se||(ee=t)},e.preInit)for(typeof e.preInit=="function"&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();function y(){}function ne(t){return(t||y).__cache__}function E(t,n){var _=ne(n),o=_[t];return o||((o=Object.create((n||y).prototype)).ptr=t,_[t]=o)}function a_(t,n){return E(t.ptr,n)}function s_(t){if(!t.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";t.__destroy__(),delete ne(t.__class__)[t.ptr]}function u_(t,n){return t.ptr===n.ptr}function c_(t){return t.ptr}function g_(t){return t.__class__}sr(),y.prototype=Object.create(y.prototype),y.prototype.constructor=y,y.prototype.__class__=y,y.__cache__={},e.WrapperObject=y,e.getCache=ne,e.wrapPointer=E,e.castObject=a_,e.NULL=E(0),e.destroy=s_,e.compare=u_,e.getPointer=c_,e.getClass=g_;var s={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(s.needed){for(var t=0;t<s.temps.length;t++)e._free(s.temps[t]);s.temps.length=0,e._free(s.buffer),s.buffer=0,s.size+=s.needed,s.needed=0}s.buffer||(s.size+=128,s.buffer=e._malloc(s.size),ye(s.buffer)),s.pos=0},alloc:function(t,n){ye(s.buffer);var _,o=n.BYTES_PER_ELEMENT,p=t.length*o;return p=p+7&-8,s.pos+p>=s.size?(ye(p>0),s.needed+=p,_=e._malloc(p),s.temps.push(_)):(_=s.buffer+s.pos,s.pos+=p),_},copy:function(t,n,_){switch(_>>>=0,n.BYTES_PER_ELEMENT){case 2:_>>>=1;break;case 4:_>>>=2;break;case 8:_>>>=3}for(var o=0;o<t.length;o++)n[_+o]=t[o]}};function le(t){if(typeof t=="string"){var n=p_(t),_=s.alloc(n,H);return s.copy(n,H,_),_}return t}function F(t){if(typeof t=="object"){var n=s.alloc(t,H);return s.copy(t,H,n),n}return t}function P_(t){if(typeof t=="object"){var n=s.alloc(t,oe);return s.copy(t,oe,n),n}return t}function y_(t){if(typeof t=="object"){var n=s.alloc(t,h);return s.copy(t,h,n),n}return t}function m_(t){if(typeof t=="object"){var n=s.alloc(t,pe);return s.copy(t,pe,n),n}return t}function f_(t){if(typeof t=="object"){var n=s.alloc(t,ie);return s.copy(t,ie,n),n}return t}function l(){throw"cannot construct a PeObject, no constructor in IDL"}function D(){throw"cannot construct a PeCoordsys, no constructor in IDL"}function X(){throw"cannot construct a VoidPtr, no constructor in IDL"}function M(){throw"cannot construct a PeDatum, no constructor in IDL"}function r(){throw"cannot construct a PeDefs, no constructor in IDL"}function R(){throw"cannot construct a PeFactory, no constructor in IDL"}function O(t,n,_,o,p,a){t&&typeof t=="object"&&(t=t.ptr),n&&typeof n=="object"&&(n=n.ptr),_&&typeof _=="object"&&(_=_.ptr),o&&typeof o=="object"&&(o=o.ptr),p&&typeof p=="object"&&(p=p.ptr),a&&typeof a=="object"&&(a=a.ptr),this.ptr=Mt(t,n,_,o,p,a),ne(O)[this.ptr]=this}function d(){throw"cannot construct a PeGeogcs, no constructor in IDL"}function m(){throw"cannot construct a PeGeogtran, no constructor in IDL"}function j(){throw"cannot construct a PeGTlistExtended, no constructor in IDL"}function C(){throw"cannot construct a PeGTlistExtendedEntry, no constructor in IDL"}function U(){throw"cannot construct a PeGTlistExtendedGTs, no constructor in IDL"}function b(){throw"cannot construct a PeHorizon, no constructor in IDL"}function S(t){t&&typeof t=="object"&&(t=t.ptr),this.ptr=_n(t),ne(S)[this.ptr]=this}function g(){throw"cannot construct a PeNotationMgrs, no constructor in IDL"}function f(){throw"cannot construct a PeNotationUtm, no constructor in IDL"}function A(){throw"cannot construct a PeParameter, no constructor in IDL"}function i(){throw"cannot construct a PePCSInfo, no constructor in IDL"}function v(){throw"cannot construct a PePrimem, no constructor in IDL"}function P(){throw"cannot construct a PeProjcs, no constructor in IDL"}function T(){throw"cannot construct a PeSpheroid, no constructor in IDL"}function N(){throw"cannot construct a PeUnit, no constructor in IDL"}function z(){throw"cannot construct a PeVersion, no constructor in IDL"}return l.prototype=Object.create(y.prototype),l.prototype.constructor=l,l.prototype.__class__=l,l.__cache__={},e.PeObject=l,l.prototype.getCode=l.prototype.getCode=function(){var t=this.ptr;return Le(t)},l.prototype.getName=l.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(Ue(n,t))},l.prototype.getType=l.prototype.getType=function(){var t=this.ptr;return Ye(t)},D.prototype=Object.create(l.prototype),D.prototype.constructor=D,D.prototype.__class__=D,D.__cache__={},e.PeCoordsys=D,D.prototype.getCode=D.prototype.getCode=function(){var t=this.ptr;return Fe(t)},D.prototype.getName=D.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(we(n,t))},D.prototype.getType=D.prototype.getType=function(){var t=this.ptr;return xe(t)},X.prototype=Object.create(y.prototype),X.prototype.constructor=X,X.prototype.__class__=X,X.__cache__={},e.VoidPtr=X,X.prototype.__destroy__=X.prototype.__destroy__=function(){var t=this.ptr;He(t)},M.prototype=Object.create(l.prototype),M.prototype.constructor=M,M.prototype.__class__=M,M.__cache__={},e.PeDatum=M,M.prototype.getSpheroid=M.prototype.getSpheroid=function(){var t=this.ptr;return E(Xe(t),T)},M.prototype.getCode=M.prototype.getCode=function(){var t=this.ptr;return ze(t)},M.prototype.getName=M.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(Ze(n,t))},M.prototype.getType=M.prototype.getType=function(){var t=this.ptr;return Be(t)},r.prototype=Object.create(y.prototype),r.prototype.constructor=r,r.prototype.__class__=r,r.__cache__={},e.PeDefs=r,r.prototype.get_PE_BUFFER_MAX=r.prototype.get_PE_BUFFER_MAX=function(){var t=this.ptr;return We(t)},Object.defineProperty(r.prototype,"PE_BUFFER_MAX",{get:r.prototype.get_PE_BUFFER_MAX}),r.prototype.get_PE_NAME_MAX=r.prototype.get_PE_NAME_MAX=function(){var t=this.ptr;return Ve(t)},Object.defineProperty(r.prototype,"PE_NAME_MAX",{get:r.prototype.get_PE_NAME_MAX}),r.prototype.get_PE_MGRS_MAX=r.prototype.get_PE_MGRS_MAX=function(){var t=this.ptr;return qe(t)},Object.defineProperty(r.prototype,"PE_MGRS_MAX",{get:r.prototype.get_PE_MGRS_MAX}),r.prototype.get_PE_USNG_MAX=r.prototype.get_PE_USNG_MAX=function(){var t=this.ptr;return ke(t)},Object.defineProperty(r.prototype,"PE_USNG_MAX",{get:r.prototype.get_PE_USNG_MAX}),r.prototype.get_PE_DD_MAX=r.prototype.get_PE_DD_MAX=function(){var t=this.ptr;return Je(t)},Object.defineProperty(r.prototype,"PE_DD_MAX",{get:r.prototype.get_PE_DD_MAX}),r.prototype.get_PE_DMS_MAX=r.prototype.get_PE_DMS_MAX=function(){var t=this.ptr;return Ke(t)},Object.defineProperty(r.prototype,"PE_DMS_MAX",{get:r.prototype.get_PE_DMS_MAX}),r.prototype.get_PE_DDM_MAX=r.prototype.get_PE_DDM_MAX=function(){var t=this.ptr;return $e(t)},Object.defineProperty(r.prototype,"PE_DDM_MAX",{get:r.prototype.get_PE_DDM_MAX}),r.prototype.get_PE_UTM_MAX=r.prototype.get_PE_UTM_MAX=function(){var t=this.ptr;return Qe(t)},Object.defineProperty(r.prototype,"PE_UTM_MAX",{get:r.prototype.get_PE_UTM_MAX}),r.prototype.get_PE_PARM_MAX=r.prototype.get_PE_PARM_MAX=function(){var t=this.ptr;return et(t)},Object.defineProperty(r.prototype,"PE_PARM_MAX",{get:r.prototype.get_PE_PARM_MAX}),r.prototype.get_PE_TYPE_NONE=r.prototype.get_PE_TYPE_NONE=function(){var t=this.ptr;return tt(t)},Object.defineProperty(r.prototype,"PE_TYPE_NONE",{get:r.prototype.get_PE_TYPE_NONE}),r.prototype.get_PE_TYPE_GEOGCS=r.prototype.get_PE_TYPE_GEOGCS=function(){var t=this.ptr;return nt(t)},Object.defineProperty(r.prototype,"PE_TYPE_GEOGCS",{get:r.prototype.get_PE_TYPE_GEOGCS}),r.prototype.get_PE_TYPE_PROJCS=r.prototype.get_PE_TYPE_PROJCS=function(){var t=this.ptr;return rt(t)},Object.defineProperty(r.prototype,"PE_TYPE_PROJCS",{get:r.prototype.get_PE_TYPE_PROJCS}),r.prototype.get_PE_TYPE_GEOGTRAN=r.prototype.get_PE_TYPE_GEOGTRAN=function(){var t=this.ptr;return _t(t)},Object.defineProperty(r.prototype,"PE_TYPE_GEOGTRAN",{get:r.prototype.get_PE_TYPE_GEOGTRAN}),r.prototype.get_PE_TYPE_COORDSYS=r.prototype.get_PE_TYPE_COORDSYS=function(){var t=this.ptr;return ot(t)},Object.defineProperty(r.prototype,"PE_TYPE_COORDSYS",{get:r.prototype.get_PE_TYPE_COORDSYS}),r.prototype.get_PE_TYPE_UNIT=r.prototype.get_PE_TYPE_UNIT=function(){var t=this.ptr;return pt(t)},Object.defineProperty(r.prototype,"PE_TYPE_UNIT",{get:r.prototype.get_PE_TYPE_UNIT}),r.prototype.get_PE_TYPE_LINUNIT=r.prototype.get_PE_TYPE_LINUNIT=function(){var t=this.ptr;return it(t)},Object.defineProperty(r.prototype,"PE_TYPE_LINUNIT",{get:r.prototype.get_PE_TYPE_LINUNIT}),r.prototype.get_PE_STR_OPTS_NONE=r.prototype.get_PE_STR_OPTS_NONE=function(){var t=this.ptr;return at(t)},Object.defineProperty(r.prototype,"PE_STR_OPTS_NONE",{get:r.prototype.get_PE_STR_OPTS_NONE}),r.prototype.get_PE_STR_AUTH_NONE=r.prototype.get_PE_STR_AUTH_NONE=function(){var t=this.ptr;return st(t)},Object.defineProperty(r.prototype,"PE_STR_AUTH_NONE",{get:r.prototype.get_PE_STR_AUTH_NONE}),r.prototype.get_PE_STR_AUTH_TOP=r.prototype.get_PE_STR_AUTH_TOP=function(){var t=this.ptr;return ut(t)},Object.defineProperty(r.prototype,"PE_STR_AUTH_TOP",{get:r.prototype.get_PE_STR_AUTH_TOP}),r.prototype.get_PE_STR_NAME_CANON=r.prototype.get_PE_STR_NAME_CANON=function(){var t=this.ptr;return ct(t)},Object.defineProperty(r.prototype,"PE_STR_NAME_CANON",{get:r.prototype.get_PE_STR_NAME_CANON}),r.prototype.get_PE_PARM_X0=r.prototype.get_PE_PARM_X0=function(){var t=this.ptr;return gt(t)},Object.defineProperty(r.prototype,"PE_PARM_X0",{get:r.prototype.get_PE_PARM_X0}),r.prototype.get_PE_PARM_ND=r.prototype.get_PE_PARM_ND=function(){var t=this.ptr;return Pt(t)},Object.defineProperty(r.prototype,"PE_PARM_ND",{get:r.prototype.get_PE_PARM_ND}),r.prototype.get_PE_TRANSFORM_1_TO_2=r.prototype.get_PE_TRANSFORM_1_TO_2=function(){var t=this.ptr;return yt(t)},Object.defineProperty(r.prototype,"PE_TRANSFORM_1_TO_2",{get:r.prototype.get_PE_TRANSFORM_1_TO_2}),r.prototype.get_PE_TRANSFORM_2_TO_1=r.prototype.get_PE_TRANSFORM_2_TO_1=function(){var t=this.ptr;return mt(t)},Object.defineProperty(r.prototype,"PE_TRANSFORM_2_TO_1",{get:r.prototype.get_PE_TRANSFORM_2_TO_1}),r.prototype.get_PE_TRANSFORM_P_TO_G=r.prototype.get_PE_TRANSFORM_P_TO_G=function(){var t=this.ptr;return ft(t)},Object.defineProperty(r.prototype,"PE_TRANSFORM_P_TO_G",{get:r.prototype.get_PE_TRANSFORM_P_TO_G}),r.prototype.get_PE_TRANSFORM_G_TO_P=r.prototype.get_PE_TRANSFORM_G_TO_P=function(){var t=this.ptr;return lt(t)},Object.defineProperty(r.prototype,"PE_TRANSFORM_G_TO_P",{get:r.prototype.get_PE_TRANSFORM_G_TO_P}),r.prototype.get_PE_HORIZON_RECT=r.prototype.get_PE_HORIZON_RECT=function(){var t=this.ptr;return dt(t)},Object.defineProperty(r.prototype,"PE_HORIZON_RECT",{get:r.prototype.get_PE_HORIZON_RECT}),r.prototype.get_PE_HORIZON_POLY=r.prototype.get_PE_HORIZON_POLY=function(){var t=this.ptr;return Et(t)},Object.defineProperty(r.prototype,"PE_HORIZON_POLY",{get:r.prototype.get_PE_HORIZON_POLY}),r.prototype.get_PE_HORIZON_LINE=r.prototype.get_PE_HORIZON_LINE=function(){var t=this.ptr;return bt(t)},Object.defineProperty(r.prototype,"PE_HORIZON_LINE",{get:r.prototype.get_PE_HORIZON_LINE}),r.prototype.get_PE_HORIZON_DELTA=r.prototype.get_PE_HORIZON_DELTA=function(){var t=this.ptr;return Ot(t)},Object.defineProperty(r.prototype,"PE_HORIZON_DELTA",{get:r.prototype.get_PE_HORIZON_DELTA}),R.prototype=Object.create(y.prototype),R.prototype.constructor=R,R.prototype.__class__=R,R.__cache__={},e.PeFactory=R,R.prototype.initialize=R.prototype.initialize=function(t){var n=this.ptr;s.prepare(),t=t&&typeof t=="object"?t.ptr:le(t),Tt(n,t)},R.prototype.factoryByType=R.prototype.factoryByType=function(t,n){var _=this.ptr;return t&&typeof t=="object"&&(t=t.ptr),n&&typeof n=="object"&&(n=n.ptr),E(St(_,t,n),l)},R.prototype.fromString=R.prototype.fromString=function(t,n){var _=this.ptr;return s.prepare(),t&&typeof t=="object"&&(t=t.ptr),n=n&&typeof n=="object"?n.ptr:le(n),E(Nt(_,t,n),l)},R.prototype.getCode=R.prototype.getCode=function(t){var n=this.ptr;return t&&typeof t=="object"&&(t=t.ptr),ht(n,t)},O.prototype=Object.create(y.prototype),O.prototype.constructor=O,O.prototype.__class__=O,O.__cache__={},e.PeGCSExtent=O,O.prototype.getLLon=O.prototype.getLLon=function(){var t=this.ptr;return vt(t)},O.prototype.getSLat=O.prototype.getSLat=function(){var t=this.ptr;return Dt(t)},O.prototype.getRLon=O.prototype.getRLon=function(){var t=this.ptr;return Rt(t)},O.prototype.getNLat=O.prototype.getNLat=function(){var t=this.ptr;return At(t)},O.prototype.__destroy__=O.prototype.__destroy__=function(){var t=this.ptr;Gt(t)},d.prototype=Object.create(D.prototype),d.prototype.constructor=d,d.prototype.__class__=d,d.__cache__={},e.PeGeogcs=d,d.prototype.getDatum=d.prototype.getDatum=function(){var t=this.ptr;return E(Ct(t),M)},d.prototype.getPrimem=d.prototype.getPrimem=function(){var t=this.ptr;return E(It(t),v)},d.prototype.getUnit=d.prototype.getUnit=function(){var t=this.ptr;return E(jt(t),N)},d.prototype.getCode=d.prototype.getCode=function(){var t=this.ptr;return Lt(t)},d.prototype.getName=d.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(Ut(n,t))},d.prototype.getType=d.prototype.getType=function(){var t=this.ptr;return Yt(t)},m.prototype=Object.create(l.prototype),m.prototype.constructor=m,m.prototype.__class__=m,m.__cache__={},e.PeGeogtran=m,m.prototype.isEqual=m.prototype.isEqual=function(t){var n=this.ptr;return t&&typeof t=="object"&&(t=t.ptr),!!Ft(n,t)},m.prototype.getGeogcs1=m.prototype.getGeogcs1=function(){var t=this.ptr;return E(wt(t),d)},m.prototype.getGeogcs2=m.prototype.getGeogcs2=function(){var t=this.ptr;return E(xt(t),d)},m.prototype.getParameters=m.prototype.getParameters=function(){var t=this.ptr;return Ht(t)},m.prototype.loadConstants=m.prototype.loadConstants=function(){var t=this.ptr;return!!Xt(t)},m.prototype.getCode=m.prototype.getCode=function(){var t=this.ptr;return zt(t)},m.prototype.getName=m.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(Zt(n,t))},m.prototype.getType=m.prototype.getType=function(){var t=this.ptr;return Bt(t)},j.prototype=Object.create(y.prototype),j.prototype.constructor=j,j.prototype.__class__=j,j.__cache__={},e.PeGTlistExtended=j,j.prototype.getGTlist=j.prototype.getGTlist=function(t,n,_,o,p,a){var u=this.ptr;return t&&typeof t=="object"&&(t=t.ptr),n&&typeof n=="object"&&(n=n.ptr),_&&typeof _=="object"&&(_=_.ptr),o&&typeof o=="object"&&(o=o.ptr),p&&typeof p=="object"&&(p=p.ptr),a&&typeof a=="object"&&(a=a.ptr),E(Wt(u,t,n,_,o,p,a),C)},j.prototype.get_PE_GTLIST_OPTS_COMMON=j.prototype.get_PE_GTLIST_OPTS_COMMON=function(){var t=this.ptr;return Vt(t)},Object.defineProperty(j.prototype,"PE_GTLIST_OPTS_COMMON",{get:j.prototype.get_PE_GTLIST_OPTS_COMMON}),C.prototype=Object.create(y.prototype),C.prototype.constructor=C,C.prototype.__class__=C,C.__cache__={},e.PeGTlistExtendedEntry=C,C.prototype.getEntries=C.prototype.getEntries=function(){var t=this.ptr;return E(qt(t),U)},C.prototype.getSteps=C.prototype.getSteps=function(){var t=this.ptr;return kt(t)},C.prototype.Delete=C.prototype.Delete=function(t){var n=this.ptr;t&&typeof t=="object"&&(t=t.ptr),Jt(n,t)},U.prototype=Object.create(y.prototype),U.prototype.constructor=U,U.prototype.__class__=U,U.__cache__={},e.PeGTlistExtendedGTs=U,U.prototype.getDirection=U.prototype.getDirection=function(){var t=this.ptr;return Kt(t)},U.prototype.getGeogtran=U.prototype.getGeogtran=function(){var t=this.ptr;return E($t(t),m)},b.prototype=Object.create(y.prototype),b.prototype.constructor=b,b.prototype.__class__=b,b.__cache__={},e.PeHorizon=b,b.prototype.getNump=b.prototype.getNump=function(){var t=this.ptr;return Qt(t)},b.prototype.getKind=b.prototype.getKind=function(){var t=this.ptr;return en(t)},b.prototype.getInclusive=b.prototype.getInclusive=function(){var t=this.ptr;return tn(t)},b.prototype.getSize=b.prototype.getSize=function(){var t=this.ptr;return nn(t)},b.prototype.getCoord=b.prototype.getCoord=function(){var t=this.ptr;return rn(t)},S.prototype=Object.create(y.prototype),S.prototype.constructor=S,S.prototype.__class__=S,S.__cache__={},e.PeInteger=S,S.prototype.get_val=S.prototype.get_val=function(){var t=this.ptr;return on(t)},S.prototype.set_val=S.prototype.set_val=function(t){var n=this.ptr;t&&typeof t=="object"&&(t=t.ptr),pn(n,t)},Object.defineProperty(S.prototype,"val",{get:S.prototype.get_val,set:S.prototype.set_val}),S.prototype.__destroy__=S.prototype.__destroy__=function(){var t=this.ptr;an(t)},g.prototype=Object.create(y.prototype),g.prototype.constructor=g,g.prototype.__class__=g,g.__cache__={},e.PeNotationMgrs=g,g.prototype.get_PE_MGRS_STYLE_NEW=g.prototype.get_PE_MGRS_STYLE_NEW=function(){var t=this.ptr;return sn(t)},Object.defineProperty(g.prototype,"PE_MGRS_STYLE_NEW",{get:g.prototype.get_PE_MGRS_STYLE_NEW}),g.prototype.get_PE_MGRS_STYLE_OLD=g.prototype.get_PE_MGRS_STYLE_OLD=function(){var t=this.ptr;return un(t)},Object.defineProperty(g.prototype,"PE_MGRS_STYLE_OLD",{get:g.prototype.get_PE_MGRS_STYLE_OLD}),g.prototype.get_PE_MGRS_STYLE_AUTO=g.prototype.get_PE_MGRS_STYLE_AUTO=function(){var t=this.ptr;return cn(t)},Object.defineProperty(g.prototype,"PE_MGRS_STYLE_AUTO",{get:g.prototype.get_PE_MGRS_STYLE_AUTO}),g.prototype.get_PE_MGRS_180_ZONE_1_PLUS=g.prototype.get_PE_MGRS_180_ZONE_1_PLUS=function(){var t=this.ptr;return gn(t)},Object.defineProperty(g.prototype,"PE_MGRS_180_ZONE_1_PLUS",{get:g.prototype.get_PE_MGRS_180_ZONE_1_PLUS}),g.prototype.get_PE_MGRS_ADD_SPACES=g.prototype.get_PE_MGRS_ADD_SPACES=function(){var t=this.ptr;return Pn(t)},Object.defineProperty(g.prototype,"PE_MGRS_ADD_SPACES",{get:g.prototype.get_PE_MGRS_ADD_SPACES}),f.prototype=Object.create(y.prototype),f.prototype.constructor=f,f.prototype.__class__=f,f.__cache__={},e.PeNotationUtm=f,f.prototype.get_PE_UTM_OPTS_NONE=f.prototype.get_PE_UTM_OPTS_NONE=function(){var t=this.ptr;return yn(t)},Object.defineProperty(f.prototype,"PE_UTM_OPTS_NONE",{get:f.prototype.get_PE_UTM_OPTS_NONE}),f.prototype.get_PE_UTM_OPTS_NS=f.prototype.get_PE_UTM_OPTS_NS=function(){var t=this.ptr;return mn(t)},Object.defineProperty(f.prototype,"PE_UTM_OPTS_NS",{get:f.prototype.get_PE_UTM_OPTS_NS}),f.prototype.get_PE_UTM_OPTS_NS_STRICT=f.prototype.get_PE_UTM_OPTS_NS_STRICT=function(){var t=this.ptr;return fn(t)},Object.defineProperty(f.prototype,"PE_UTM_OPTS_NS_STRICT",{get:f.prototype.get_PE_UTM_OPTS_NS_STRICT}),f.prototype.get_PE_UTM_OPTS_ADD_SPACES=f.prototype.get_PE_UTM_OPTS_ADD_SPACES=function(){var t=this.ptr;return ln(t)},Object.defineProperty(f.prototype,"PE_UTM_OPTS_ADD_SPACES",{get:f.prototype.get_PE_UTM_OPTS_ADD_SPACES}),A.prototype=Object.create(l.prototype),A.prototype.constructor=A,A.prototype.__class__=A,A.__cache__={},e.PeParameter=A,A.prototype.getValue=A.prototype.getValue=function(){var t=this.ptr;return dn(t)},A.prototype.getCode=A.prototype.getCode=function(){var t=this.ptr;return En(t)},A.prototype.getName=A.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(bn(n,t))},A.prototype.getType=A.prototype.getType=function(){var t=this.ptr;return On(t)},i.prototype=Object.create(y.prototype),i.prototype.constructor=i,i.prototype.__class__=i,i.__cache__={},e.PePCSInfo=i,i.prototype.getCentralMeridian=i.prototype.getCentralMeridian=function(){var t=this.ptr;return Tn(t)},i.prototype.getDomainMinx=i.prototype.getDomainMinx=function(){var t=this.ptr;return Sn(t)},i.prototype.getDomainMiny=i.prototype.getDomainMiny=function(){var t=this.ptr;return Nn(t)},i.prototype.getDomainMaxx=i.prototype.getDomainMaxx=function(){var t=this.ptr;return hn(t)},i.prototype.getDomainMaxy=i.prototype.getDomainMaxy=function(){var t=this.ptr;return Mn(t)},i.prototype.getNorthPoleLocation=i.prototype.getNorthPoleLocation=function(){var t=this.ptr;return vn(t)},i.prototype.getNorthPoleGeometry=i.prototype.getNorthPoleGeometry=function(){var t=this.ptr;return Dn(t)},i.prototype.getSouthPoleLocation=i.prototype.getSouthPoleLocation=function(){var t=this.ptr;return Rn(t)},i.prototype.getSouthPoleGeometry=i.prototype.getSouthPoleGeometry=function(){var t=this.ptr;return An(t)},i.prototype.isDensificationNeeded=i.prototype.isDensificationNeeded=function(){var t=this.ptr;return!!Gn(t)},i.prototype.isGcsHorizonMultiOverlap=i.prototype.isGcsHorizonMultiOverlap=function(){var t=this.ptr;return!!Cn(t)},i.prototype.isPannableRectangle=i.prototype.isPannableRectangle=function(){var t=this.ptr;return!!In(t)},i.prototype.generate=i.prototype.generate=function(t,n){var _=this.ptr;return t&&typeof t=="object"&&(t=t.ptr),n&&typeof n=="object"&&(n=n.ptr),E(jn(_,t,n),i)},i.prototype.get_PE_PCSINFO_OPTION_NONE=i.prototype.get_PE_PCSINFO_OPTION_NONE=function(){var t=this.ptr;return Ln(t)},Object.defineProperty(i.prototype,"PE_PCSINFO_OPTION_NONE",{get:i.prototype.get_PE_PCSINFO_OPTION_NONE}),i.prototype.get_PE_PCSINFO_OPTION_DOMAIN=i.prototype.get_PE_PCSINFO_OPTION_DOMAIN=function(){var t=this.ptr;return Un(t)},Object.defineProperty(i.prototype,"PE_PCSINFO_OPTION_DOMAIN",{get:i.prototype.get_PE_PCSINFO_OPTION_DOMAIN}),i.prototype.get_PE_POLE_OUTSIDE_BOUNDARY=i.prototype.get_PE_POLE_OUTSIDE_BOUNDARY=function(){var t=this.ptr;return Yn(t)},Object.defineProperty(i.prototype,"PE_POLE_OUTSIDE_BOUNDARY",{get:i.prototype.get_PE_POLE_OUTSIDE_BOUNDARY}),i.prototype.get_PE_POLE_POINT=i.prototype.get_PE_POLE_POINT=function(){var t=this.ptr;return Fn(t)},Object.defineProperty(i.prototype,"PE_POLE_POINT",{get:i.prototype.get_PE_POLE_POINT}),v.prototype=Object.create(l.prototype),v.prototype.constructor=v,v.prototype.__class__=v,v.__cache__={},e.PePrimem=v,v.prototype.getLongitude=v.prototype.getLongitude=function(){var t=this.ptr;return wn(t)},v.prototype.getCode=v.prototype.getCode=function(){var t=this.ptr;return xn(t)},v.prototype.getName=v.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(Hn(n,t))},v.prototype.getType=v.prototype.getType=function(){var t=this.ptr;return Xn(t)},P.prototype=Object.create(D.prototype),P.prototype.constructor=P,P.prototype.__class__=P,P.__cache__={},e.PeProjcs=P,P.prototype.getGeogcs=P.prototype.getGeogcs=function(){var t=this.ptr;return E(zn(t),d)},P.prototype.getParameters=P.prototype.getParameters=function(){var t=this.ptr;return Zn(t)},P.prototype.getUnit=P.prototype.getUnit=function(){var t=this.ptr;return E(Bn(t),N)},P.prototype.loadConstants=P.prototype.loadConstants=function(){var t=this.ptr;return!!Wn(t)},P.prototype.horizonGcsGenerate=P.prototype.horizonGcsGenerate=function(){var t=this.ptr;return E(Vn(t),b)},P.prototype.horizonPcsGenerate=P.prototype.horizonPcsGenerate=function(){var t=this.ptr;return E(qn(t),b)},P.prototype.getCode=P.prototype.getCode=function(){var t=this.ptr;return kn(t)},P.prototype.getName=P.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(Jn(n,t))},P.prototype.getType=P.prototype.getType=function(){var t=this.ptr;return Kn(t)},T.prototype=Object.create(l.prototype),T.prototype.constructor=T,T.prototype.__class__=T,T.__cache__={},e.PeSpheroid=T,T.prototype.getAxis=T.prototype.getAxis=function(){var t=this.ptr;return $n(t)},T.prototype.getFlattening=T.prototype.getFlattening=function(){var t=this.ptr;return Qn(t)},T.prototype.getCode=T.prototype.getCode=function(){var t=this.ptr;return er(t)},T.prototype.getName=T.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(tr(n,t))},T.prototype.getType=T.prototype.getType=function(){var t=this.ptr;return nr(t)},N.prototype=Object.create(l.prototype),N.prototype.constructor=N,N.prototype.__class__=N,N.__cache__={},e.PeUnit=N,N.prototype.getUnitFactor=N.prototype.getUnitFactor=function(){var t=this.ptr;return rr(t)},N.prototype.getCode=N.prototype.getCode=function(){var t=this.ptr;return _r(t)},N.prototype.getName=N.prototype.getName=function(t){var n=this.ptr;return s.prepare(),typeof t=="object"&&(t=F(t)),L(or(n,t))},N.prototype.getType=N.prototype.getType=function(){var t=this.ptr;return pr(t)},z.prototype=Object.create(y.prototype),z.prototype.constructor=z,z.prototype.__class__=z,z.__cache__={},e.PeVersion=z,z.prototype.version_string=z.prototype.version_string=function(){var t=this.ptr;return L(ir(t))},e.ensureCache=s,e.ensureString=le,e.ensureInt8=F,e.ensureInt16=P_,e.ensureInt32=y_,e.ensureFloat32=m_,e.ensureFloat64=f_,e.ready},Pr.exports=yr;const O_=b_({__proto__:null,default:ue},[ue]);export{O_ as p};
