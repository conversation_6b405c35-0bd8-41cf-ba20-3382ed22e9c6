package org.thingsboard.server.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dispatch.EmergencyPlanService;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyPlan;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyPlanSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/emergencyPlan"})
public class EmergencyPlanController extends BaseController {
    @Autowired
    private EmergencyPlanService service;

    public EmergencyPlanController() {
    }

    @GetMapping
    public IPage<EmergencyPlan> findAllConditional(EmergencyPlanPageRequest request) {
        return this.service.findAllConditional(request);
    }

    @PostMapping
    public EmergencyPlan save(@RequestBody EmergencyPlanSaveRequest req) {
        return this.service.save(req);
    }

    @PatchMapping({"/{id}"})
    public boolean edit(@RequestBody EmergencyPlanSaveRequest req, @PathVariable String id) {
        return this.service.update((EmergencyPlan)req.unwrap(id));
    }

    @DeleteMapping({"/{id}"})
    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
