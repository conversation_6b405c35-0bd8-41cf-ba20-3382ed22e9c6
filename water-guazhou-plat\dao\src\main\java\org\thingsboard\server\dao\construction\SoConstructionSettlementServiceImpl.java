package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionSettlement;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionSettlementMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSettlementPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSettlementSaveRequest;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_SETTLEMENT_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_SETTLEMENT;

@Service
public class SoConstructionSettlementServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionSettlement> implements SoConstructionSettlementService {
    @Autowired
    private SoConstructionSettlementMapper mapper;

    @Override
    public IPage<SoConstructionSettlement> findAllConditional(SoConstructionSettlementPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionSettlement save(SoConstructionSettlementSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> commonSave(entity, e), mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionSettlement entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean complete(String constructionCode, String userId, String tenantId) {
        boolean success = taskInfoService.markAsComplete(constructionCode, tenantId, getCurrentScope());
        if (success) {
            recordService.recordComplete(tenantId, userId, constructionCode, getCurrentJournalType());
        }
        return success;
    }

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_SETTLEMENT;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_SETTLEMENT_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionSettlement> getDirectMapper() {
        return mapper;
    }


}
