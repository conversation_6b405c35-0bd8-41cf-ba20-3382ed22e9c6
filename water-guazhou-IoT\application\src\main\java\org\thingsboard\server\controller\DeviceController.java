/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.Utils.TimeDiff;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.*;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.AccessValidator;


import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("/api")
@Slf4j
public class DeviceController extends BaseController {

    @Autowired
    private AttributesService attributesService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    /**
     * 单个设备多个属性趋势
     * @param jsonNode
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN','TENANT_ADMIN', 'TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/data", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_DATA)
    @ResponseBody
    public Map getDeviceData(@RequestBody JsonNode jsonNode) throws ThingsboardException {
        try {
             List<String> attr = new ArrayList<>();
            Iterator<JsonNode> elementIterator = jsonNode.get("attributes").elements();
            elementIterator.forEachRemaining(jsonNode1 -> {
                attr.add(jsonNode1.textValue());
            });
            // 处理时间参数
            long start = jsonNode.get("start").asLong();
            long end = jsonNode.get("end").asLong();
            start = TimeDiff.convertStartByType(start, jsonNode.get(DataConstants.REQUEST_PARAM_TYPE).asText());
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = obtainDataService.getDeviceDataByOriginal(attr, start, end, jsonNode.get("type").asText(), null, getTenantId());
            if (!jsonNode.get("type").asText().equalsIgnoreCase(DateUtils.DAY) && !jsonNode.get("type").asText().equalsIgnoreCase(DateUtils.MONTH) && !jsonNode.get("type").asText().equalsIgnoreCase(DateUtils.YEAR) && result.size() > 1 && !jsonNode.get("type").asText().equalsIgnoreCase(DateUtils.HOUR)) {
                result.remove(result.keySet().iterator().next());
            }

            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN','TENANT_ADMIN', 'TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/device/data/{deviceId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_DEVICE_INFO)
    @ResponseBody
    public LinkedHashMap<String, LinkedHashMap<String, String>> getOneDeviceData(@PathVariable("deviceId") String
                                                                                         deviceId) throws ThingsboardException {
        List<String> attr = new ArrayList<>();
        try {
            Optional<AttributeKvEntry> attributeKvEntry = attributesService.find(getTenantId(), new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
            if (attributeKvEntry.isPresent()) {
                ObjectMapper objectMapper = new ObjectMapper();
                org.codehaus.jackson.JsonNode jsonNode = objectMapper.readTree(attributeKvEntry.get().getValueAsString());
                Iterator<org.codehaus.jackson.JsonNode> iterator = jsonNode.getElements();
                iterator.forEachRemaining(jsonNode1 -> {
                    if (jsonNode1.get("propertyType") != null && jsonNode1.get("propertyType").asText().equalsIgnoreCase("1")) {
                        attr.add(deviceId + "." + jsonNode1.get("propertyCategory").asText());
                    }
                });
            }
            // 获取十五分钟内所有的数据
            LinkedHashMap<String, LinkedHashMap<String, String>> deviceLastAllData = obtainDataService.getDeviceLastAllData(attr, getTenantId());
            LinkedHashMap<String, LinkedHashMap<String, String>> result = new LinkedHashMap<>();
            // 遍历取出时间戳最大值对应的数据，并放入返回结果中
            deviceLastAllData.entrySet().forEach(key -> {
                //LinkedHashMap<String, String> timeValueMap = deviceLastAllData.get(key);
                OptionalLong max = key.getValue().keySet().stream().mapToLong(Long::parseLong).max();
                if (max.isPresent()) {
                    LinkedHashMap<String, String> timeValueMap2 = new LinkedHashMap<>();
                    timeValueMap2.put(max.getAsLong() + "", key.getValue().get(max.getAsLong() + ""));
                    result.put(key.getKey(), timeValueMap2);
                }
            });
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    /**
     * 获取设备实时监测数据
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN','TENANT_ADMIN','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "device/fullData/page/{deviceId}", method = RequestMethod.GET)
    public PageData<DeviceFullData> getDevicesStatusPage(@PathVariable("deviceId") String deviceId,
                                                         @RequestParam(required = false) String group,
                                                         @RequestParam(required = false, defaultValue = "1") int page,
                                                         @RequestParam(required = false, defaultValue = "20") int size) throws ThingsboardException {
        List<DeviceFullData> list = deviceService.getDeviceFullData(getTenantId(), new DeviceId(UUIDConverter.fromString(deviceId)), group);

        List<DeviceFullData> dataList = new ArrayList<>();
        page = page - 1;
        if (!list.isEmpty()) {
            int dataSize = list.size();;
            List<DeviceFullData> pageResultList = new ArrayList<>();
            if (dataSize <= size) {
                pageResultList = list;
            } else {
                int start = page * size;
                int end = ((page + 1) * size) - 1;
                int i = 0;
                for (int j = 0; j < list.size(); j++) {
                    if (j > end) {
                        break;
                    }
                    if (i >= start && i <= end) {
                        pageResultList.add(list.get(j));
                    }
                    i++;
                }
            }

            return new PageData<>(list.size(), pageResultList);
        }

        return new PageData<>(0, dataList);
    }
}
