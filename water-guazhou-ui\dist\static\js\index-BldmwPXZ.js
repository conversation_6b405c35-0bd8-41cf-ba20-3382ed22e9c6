import{_ as C}from"./ArcLayout-CHnHL9Pv.js";import{_ as F}from"./ArcZoomTo.vue_vue_type_script_setup_true_lang-5gUU1gHn.js";import{d as G,W as P,c as u,r as S,a8 as w,bH as A,bS as D,b as i,S as k,o as M,g as q,h as O,F as L,q as f,i as d,aq as $,C as J}from"./index-r0dFAfgr.js";import{_ as N}from"./Search-NSrhrIa_.js";import{a as Z,D as B,G as E}from"./engineeringDocuments-DYprVB7x.js";import{s as U}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{g as W}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{E as l}from"./config-B_00vVdd.js";import{f as z}from"./DateFormatter-Bm9a68Ax.js";import{c as H}from"./geoserverUtils-wjOSMa7E.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";const R=G({__name:"index",setup(V){const g=P(),y=u(),s=u(),c=u(),T=S({filters:[{type:"radio-button",label:"类型",field:"fileType",options:[{label:"全部",value:""},{label:"图片",value:"image"},{label:"视频",value:"video"},{label:"音频",value:"audio"},{label:"文档",value:"file"}]},{type:"select-tree",label:"设备类型",field:"deviceType",options:w(()=>{var e,t;return[{label:"管点类",value:-1,children:(e=g.gLayerOption_Point)==null?void 0:e.map(o=>({...o,value:o.label})),disabled:!0},{label:"管线类",value:-2,children:(t=g.gLayerOption_Line)==null?void 0:t.map(o=>({...o,value:o.label})),disabled:!0}]})},{type:"input",label:"设备编号",field:"deviceCode"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>n()},{perm:!0,text:"重置",iconifyIcon:"ep:refresh",type:"default",click:()=>{var e;return(e=c.value)==null?void 0:e.resetForm()}},{perm:!0,text:"批量还原",type:"success",iconifyIcon:"ep:d-arrow-left",click:()=>_()},{perm:!0,text:"批量删除",iconifyIcon:"ep:delete",type:"danger",click:()=>b()}]}],defaultParams:{}}),a=S({columns:[{label:"文件名称",prop:"name"},{label:"文件类型",prop:"fileType"},{label:"设备类型",prop:"deviceType"},{label:"设备编号",prop:"deviceCode"},{label:"上传时间",prop:"uploadTime",formatter(e,t){return z(t,A)}},{label:"上传人",prop:"uploadUser"},{label:"备注",prop:"remark"},{label:"归档状态",prop:"status",formatter(e,t){return t===l.正常?"正常":t===l.已删除?"已删除":""}}],dataList:[],operationWidth:260,operations:[{perm:!0,iconifyIcon:"ep:location",text:"定位",click:e=>I(e)},{perm:!0,iconifyIcon:"ep:download",type:"primary",text:"下载",click:e=>D(e.file,e.name)},{perm:!0,iconifyIcon:"ep:d-arrow-left",type:"success",text:"还原",click:e=>_(e)},{perm:!0,iconifyIcon:"ep:delete",type:"danger",text:"彻底删除",click:e=>b(e)}],handleSelectChange(e){a.selectList=e||[]},pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e||1,a.pagination.limit=t||20,n()}}}),I=e=>{var t,o,r;debugger;if(e.geo)try{const p=JSON.parse(e.geo);if(!p||!p.type){i.warning("几何信息格式不正确");return}const m=H(p);if(m){const h=new W({geometry:m,symbol:U(m.type)});(o=(t=s.value)==null?void 0:t.refPanel)==null||o.toggleMaxMin("normal"),(r=y.value)==null||r.gotoFeature(h);return}else i.warning("无法解析几何信息")}catch(p){console.error("解析几何信息失败:",p)}else i.warning("没有位置信息")},_=e=>{var o;const t=e?[e.id]:((o=a.selectList)==null?void 0:o.map(r=>r.id))||[];if(!t.length){i.error("请先选择要还原的文件");return}k("确定还原吗？","提示信息").then(()=>{Z(t,l.正常).then(r=>{r.data.code===200?(i.success("还原成功"),n()):i.error("还原失败")}).catch(r=>{console.log(r),i.error("还原失败")})}).catch(()=>{})},b=e=>{var o;const t=e?[e.id]:(o=a.selectList)==null?void 0:o.map(r=>r.id);if(!(t!=null&&t.length)){i.error("请先选择要删除的文件");return}k("彻底删除无法恢复，确定删除？","提示信息").then(()=>{B(t).then(r=>{r.data.code===200?(i.success("删除成功"),n()):i.error("删除失败")}).catch(r=>{console.log(r),i.error("删除失败")})}).catch(()=>{})},n=()=>{var t;const e=((t=c.value)==null?void 0:t.queryParams)||{};E({...e,page:a.pagination.page||1,size:a.pagination.limit||20,status:l.已删除}).then(o=>{a.dataList=o.data.data.data||[],a.pagination.total=o.data.data.total||0}).catch(o=>{console.log(o)})};return M(()=>{n()}),(e,t)=>{const o=N,r=$,p=F,m=C;return q(),O(m,{ref_key:"refArcLayout",ref:s,"panel-title":"多媒体回收站","hide-panel-close":!0,"panel-max-min":!0,"panel-default-maxmin":"normal","panel-default-visible":!0,onMounted:t[0]||(t[0]=h=>{var v,x;return(x=(v=d(s))==null?void 0:v.refPanel)==null?void 0:x.Toggle(!0)})},{"detail-default":L(()=>[f(o,{ref_key:"refSearch",ref:c,style:{"margin-bottom":"10px"},config:d(T)},null,8,["config"]),f(r,{class:"table-box",config:d(a)},null,8,["config"])]),"map-bars":L(()=>[f(p,{ref_key:"refArcZoomTo",ref:y,layerid:"media-recyclebin",layertitle:"多媒体定位点"},null,512)]),_:1},512)}}}),Mt=J(R,[["__scopeId","data-v-ea31d813"]]);export{Mt as default};
