<template>
  <div ref="refDiv" class="pie-charts overlay-y">
    <div v-for="(chart, i) in state.chartOptions" :key="i" class="chart-item">
      <div class="chart">
        <VChart :ref="'refChart'" :option="chart.option"></VChart>
      </div>
      <div class="title">
        {{ chart.title }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';

const props = defineProps<{
  data: any;
}>();
const initOption = (
  data: { name: string; value: number }[] = [],
  unit = 'm³'
) => {
  const total = data
    .map((item) => item.value)
    .reduce((prev, curr) => {
      return prev + curr;
    }, 0);
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        return `${params.marker} ${params.name} ${params.value} ${unit}`;
      }
    },
    legend: {
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      top: 'center',
      right: 20,
      textStyle: {
        color: '#318DFF'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['35%', '50%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          position: 'center',
          color: useAppStore().isDark ? '#fff' : '#333',
          formatter: () => {
            return `合计\n\n${total}${unit}`;
          }
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     fontSize: 12,
        //     fontWeight: 'bold'
        //   }
        // },
        labelLine: {
          show: false
        },
        data
      }
    ]
  };
};
const state = reactive<{ chartOptions: { title: string; option: any }[] }>({
  chartOptions: [
    { title: '上月供水量占比', option: initOption() },
    { title: '上月售水量占比', option: initOption() },
    { title: '上月大用户水量占比', option: initOption() },
    { title: '昨日小区漏失情况统计', option: initOption([], '个') }
  ]
});
const refreshData = () => {
  const lastMonthSupply = props.data?.lastMonthSupply?.x?.map((item, i) => {
    return { value: props.data?.lastMonthSupply?.y?.[i], name: item };
  });
  const lastMonthSale = props.data?.lastMonthSale?.x?.map((item, i) => {
    return { value: props.data?.lastMonthSale?.y?.[i], name: item };
  });
  const lastBigUserSupply = props.data?.lastBigUserSupply?.x?.map((item, i) => {
    return { value: props.data?.lastBigUserSupply?.y?.[i], name: item };
  });
  const lastLossEvaluate = props.data?.lastLossEvaluate?.x?.map((item, i) => {
    return { value: props.data?.lastLossEvaluate?.y?.[i], name: item };
  });
  state.chartOptions = [
    { title: '上月供水量占比', option: initOption(lastMonthSupply) },
    { title: '上月售水量占比', option: initOption(lastMonthSale) },
    { title: '上月大用户水量占比', option: initOption(lastBigUserSupply) },
    {
      title: '昨日小区漏失情况统计',
      option: initOption(lastLossEvaluate, '个')
    }
  ];
};
watch(
  () => props.data,
  () => refreshData()
);
const refDiv = ref<HTMLDivElement>();
const refChart = ref();
const resize = useDetector();
onMounted(() => {
  resize.listenToMush(refDiv.value, () => {
    refChart.value?.map((item) => {
      item.resize();
    });
  });
});
</script>
<style lang="scss" scoped>
.pie-charts {
  width: 600px;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  .chart-item {
    width: 50%;
    .chart {
      width: 100%;
      height: 190px;
    }
    .title {
      display: grid;
      place-content: center;
    }
  }
}
</style>
