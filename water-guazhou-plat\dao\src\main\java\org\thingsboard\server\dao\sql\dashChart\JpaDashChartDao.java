package org.thingsboard.server.dao.sql.dashChart;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.DashChartEntity;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/2 18:31
 */
@Service
public class JpaDashChartDao {

    @Autowired
    private DashChartRepository dashChartRepository;

    public DashChartEntity save(DashChartEntity dashChartEntity) {
        return dashChartRepository.save(dashChartEntity);
    }

    public List<DashChartEntity> findByTenant(String tenantId) {
        return dashChartRepository.findByTenantId(tenantId);
    }

    public List<DashChartEntity> findByOriginatorId(String originatorId) {
        return dashChartRepository.findByOriginatorId(originatorId);
    }

    @Transactional
    public boolean deleteByDashboardId(String dashboardId) {
        dashChartRepository.deleteByDashBoardId(dashboardId);
        return true;

    }

    public boolean delete(String id) {
        dashChartRepository.delete(id);
        return true;
    }

    public DashChartEntity findByJsonId(String jsonId) {
        return dashChartRepository.findByDashBoardJsonId(jsonId);
    }

    public List<DashChartEntity> findByOriginatorIdAndType(String originatorId, String type) {
        return dashChartRepository.findByOriginatorIdAndChartType(originatorId, type);
    }


    public List<DashChartEntity> findByOriginatorIdAndNameLike(String originatorId, String name) {
        return dashChartRepository.findByOriginatorIdAndNameLike(originatorId, name);
    }
}
