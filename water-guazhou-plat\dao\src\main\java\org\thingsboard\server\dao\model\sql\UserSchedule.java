package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.USER_SCHEDULE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class UserSchedule {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.USER_SCHEDULE_TIME)
    private Date time;

    @Column(name = ModelConstants.USER_SCHEDULE_USER_ID)
    private String userId;

    @Column(name = ModelConstants.USER_SCHEDULE_TITLE)
    private String title;

    @Column(name = ModelConstants.USER_SCHEDULE_CONTENT)
    private String content;

    @Column(name = ModelConstants.USER_SCHEDULE_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
