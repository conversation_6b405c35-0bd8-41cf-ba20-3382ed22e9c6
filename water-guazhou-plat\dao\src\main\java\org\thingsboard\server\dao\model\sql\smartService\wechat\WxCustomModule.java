package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("wx_custom_module")
public class WxCustomModule {
    // id
    private String id;

    // icon地址
    private String icon;

    // 模块名
    private String name;

    // 简介
    private String introduction;

    // 模块地址
    private String address;

    // 是否启用模块
    private Boolean isEnabled;

    // 是否启用操作指南
    private Boolean guideMode;

    // 渲染模式。VUE/IFRAME
    private String mode;

    // 页面指南标题
    private String guideTitle;

    // 指南类型
    private String guideType;

    // 指南详细信息数据
    private String guideDescription;

    // 是否在顶部显示
    private Boolean atTop;

    // 是否在快捷栏显示
    private Boolean isPick;

    // 是否在推荐栏显示
    private Boolean isRecommend;

    // 在快捷栏中的顺序编号
    private Integer orderNum;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
