import{v as $,S as y,e as p,y as n,a as E,U as F}from"./Point-WxyopZva.js";import{R as a,b0 as V,aO as C}from"./index-r0dFAfgr.js";import{l as c,k as h,a as D}from"./widget-BcWKanF2.js";import{y as A}from"./elevationInfoUtils-5B4aSzEU.js";import{x as I,y as R}from"./AnimatedLinesLayer-B2VbV4jv.js";import{i as x,p as q}from"./queryEngineUtils-zApNHdaJ.js";import{r as v,a as G,n as d}from"./symbologySnappingCandidates-CZjQb_7m.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./VertexSnappingCandidate-CgwNICNk.js";let r=class extends ${get availability(){return 1}get updating(){return this.layerSource.updating}get _snappingElevationAligner(){const{view:t}=this,{layer:e}=this.layerSource,i=a(t)&&t.type==="3d";if(!i||e.type==="subtype-group")return v();const o=async(s,l)=>(await F(t.whenLayerView(e),l)).elevationAlignPointsInFeatures(s,l);return v(i,{elevationInfo:e.elevationInfo,alignPointsInFeatures:o,spatialReference:t.spatialReference})}get _snappingElevationFilter(){const{view:t}=this,e=a(t)&&t.type==="3d"&&this.layerSource.layer.type!=="subtype-group";return G(e)}get _symbologySnappingFetcher(){const{view:t}=this,{layer:e}=this.layerSource;return a(t)&&t.type==="3d"&&e.type!=="subtype-group"?d(this._symbologySnappingSupported,async(i,o)=>{const s=await t.whenLayerView(e);return y(o),s.queryForSymbologySnapping({candidates:i,spatialReference:t.spatialReference},o)}):d()}get _symbologySnappingSupported(){return a(this._layerView3D)&&this._layerView3D.symbologySnappingSupported}initialize(){const{view:t}=this,{layer:e}=this.layerSource;a(t)&&t.type==="3d"&&e.type!=="subtype-group"&&(t.whenLayerView(e).then(i=>this._layerView3D=i),this.addHandles([t.elevationProvider.on("elevation-change",({context:i})=>{const{elevationInfo:o}=e;A(i,o)&&this._snappingElevationAligner.notifyElevationSourceChange()}),c(()=>e.elevationInfo,()=>this._snappingElevationAligner.notifyElevationSourceChange(),h),c(()=>{var i;return a(this._layerView3D)?(i=this._layerView3D.processor)==null?void 0:i.renderer:null},()=>this._symbologySnappingFetcher.notifySymbologyChange(),h),D(()=>{var i;return(i=V(this._layerView3D))==null?void 0:i.layer},["edits","apply-edits","graphic-update"],()=>this._symbologySnappingFetcher.notifySymbologyChange())]))}constructor(t){super(t),this.view=null,this._layerView3D=null}refresh(){}async fetchCandidates(t,e){var u;const{layer:i}=this.layerSource,o=i.source;if(!(o!=null&&o.querySnapping))return[];const s=I(i),l=R(t,((u=C(this.view))==null?void 0:u.type)??"2d",s),S=await o.querySnapping(l,{signal:e});y(e);const m=await this._snappingElevationAligner.alignCandidates(S.candidates,e);y(e);const g=await this._symbologySnappingFetcher.fetch(m,e);y(e);const f=g.length===0?m:[...m,...g],w=this._snappingElevationFilter.filter(l,f),_=this._getGroundElevation;return w.map(b=>x(b,_))}get _getGroundElevation(){return q(this.view)}};p([n({constructOnly:!0})],r.prototype,"layerSource",void 0),p([n({constructOnly:!0})],r.prototype,"view",void 0),p([n()],r.prototype,"_snappingElevationAligner",null),p([n()],r.prototype,"_snappingElevationFilter",null),p([n()],r.prototype,"_symbologySnappingFetcher",null),p([n()],r.prototype,"_layerView3D",void 0),p([n()],r.prototype,"_symbologySnappingSupported",null),p([n()],r.prototype,"_getGroundElevation",null),r=p([E("esri.views.interactive.snapping.featureSources.FeatureCollectionSnappingSource")],r);export{r as FeatureCollectionSnappingSource};
