<template>
  <div ref="refSmartPipe" class="smartpipe">
    <DrawerBox
      ref="refDrawerBox"
      :right-drawer="true"
      :right-drawer-bar-position="'center'"
      :theme="appStore.isDark ? 'darkblue' : 'light'"
      :class="appStore.isDark ? 'darkblue' : ''"
    >
      <template #right>
        <div class="drawer-right">
          <div class="drawer-right-title">
            <span>{{ state.title || '请选择菜单以展示内容' }}</span>
          </div>
          <div class="drawer-right-content overlay-y">
            <WorkSpace
              v-if="state.panelOperate === 'tygj_gzkj'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <AttrAnnotation
              v-if="state.panelOperate === 'tygj_sxbz'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <Measure
              v-if="state.panelOperate === 'tygj_clgj'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <ThematicMap
              v-if="state.panelOperate === 'tygj_ztt'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <QuestionMark
              v-if="state.panelOperate === 'tygj_ywbs'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <PicturePrint
              v-if="state.panelOperate === 'tygj_ztdy'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <TemplatePrint
              v-if="state.panelOperate === 'tygj_mbdy'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <BurstAnalys
              v-if="state.panelOperate === 'gwjk_gwfx_bg'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <ConnectAnalys
              v-if="state.panelOperate === 'gwjk_gwfx_ltx'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <BufferAnalys
              v-if="state.panelOperate === 'gwjk_gwfx_hcq'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <AcrossAnalys
              v-if="state.panelOperate === 'gwjk_gwfx_hpm'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <ShutValveAnalys
              v-if="state.panelOperate === 'gwjk_gwfx_gf'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <PathAnalys
              v-if="state.panelOperate === 'gwjk_gwfx_zdlj'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <SqlSearch
              v-if="state.panelOperate === 'gwjk_gwcx_sql'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <PointNumberSearch
              v-if="state.panelOperate === 'gwjk_gwcx_dh'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <PictureNumberSearch
              v-if="state.panelOperate === 'gwjk_gwcx_tfh'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <DeviceAdressSearch
              v-if="state.panelOperate === 'gwjk_gwcx_sbdz'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <MaterialSearch
              v-if="state.panelOperate === 'gwjk_gwcx_cz'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <PipeDiameterSearch
              v-if="state.panelOperate === 'gwjk_gwcx_gj'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <CompleteTimeSearch
              v-if="state.panelOperate === 'gwjk_gwcx_jgsj'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <LengthStatistics
              v-if="state.panelOperate === 'gwjk_gwtj_cd'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <CountStatistics
              v-if="state.panelOperate === 'gwjk_gwtj_sl'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <ProductionMonitoring
              v-if="state.panelOperate === 'scjk'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <BumbMonitoring
              v-if="state.panelOperate === 'bzjk'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <DMA
              v-if="state.panelOperate === 'dma'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <WaterSearch
              v-if="state.panelOperate === 'yxjk_yscx'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <UserSearch
              v-if="state.panelOperate === 'yxjk_yhcx'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
            <UserLocate
              v-if="state.panelOperate === 'yxjk_yhdw'"
              :view="staticState.view"
              :telport="'#arcmap-wrapper'"
            />
          </div>
        </div>
      </template>
      <div id="arcmap-wrapper" class="map-wrapper">
        <ArcMap ref="refMap" />
        <div
          id="gis-horizontal-menu"
          class="esri-widget custom-horizontal-menu"
        >
          <HorizontalMenu @click="handleHorizontalMenuSelect" />
        </div>
        <ArcBRTools
          v-if="state.mounted"
          ref="refArcBRTools"
          :view="staticState.view"
        ></ArcBRTools>
      </div>
    </DrawerBox>
  </div>
</template>
<script lang="ts" setup>
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue';
import useWidgets from '@/hooks/arcgis/useWidgets';
import HorizontalMenu from './components/menus/HorizontalMenu.vue';
import { useAppStore, useGisStore } from '@/store';
import { AutoLogin } from '@/api/mapservice';
import { toggleFullscreen } from '@/utils/GlobalHelper';
import ArcBRTools from './components/common/ArcBRTools.vue';
import { useCoordinate, useLayerList, useScaleBar } from '@/hooks/arcgis';

const appStore = useAppStore();
const refDrawerBox = ref<InstanceType<typeof DrawerBox>>();
const { initSearch, addCustomWidget } = useWidgets();
const scale = useScaleBar();
const coordinate = useCoordinate();
const layerList = useLayerList();
const refMap = ref<IArcMapIns>();
const refSmartPipe = ref<HTMLDivElement>();
const refArcBRTools = ref<InstanceType<typeof ArcBRTools>>();
const BurstAnalys = defineAsyncComponent(
  () => import('./components/pipeAnalys/BurstAnalys.vue')
);
const WorkSpace = defineAsyncComponent(
  () => import('./components/common/WorkSpace.vue')
);
const AttrAnnotation = defineAsyncComponent(
  () => import('./components/common/AttrAnnotation.vue')
);
const ConnectAnalys = defineAsyncComponent(
  () => import('./components/pipeAnalys/ConnectAnalys.vue')
);
const BufferAnalys = defineAsyncComponent(
  () => import('./components/pipeAnalys/BufferAnalys.vue')
);
const AcrossAnalys = defineAsyncComponent(
  () => import('./components/pipeAnalys/AcrossAnalys.vue')
);
const ShutValveAnalys = defineAsyncComponent(
  () => import('./components/pipeAnalys/ShutValveAnalys.vue')
);
const PathAnalys = defineAsyncComponent(
  () => import('./components/pipeAnalys/PathAnalys.vue')
);
const SqlSearch = defineAsyncComponent(
  () => import('./components/search/SqlSearch.vue')
);
const PointNumberSearch = defineAsyncComponent(
  () => import('./components/search/PointNumberSearch.vue')
);
const PictureNumberSearch = defineAsyncComponent(
  () => import('./components/search/PictureNumberSearch.vue')
);
const DeviceAdressSearch = defineAsyncComponent(
  () => import('./components/search/DeviceAdressSearch.vue')
);
const MaterialSearch = defineAsyncComponent(
  () => import('./components/search/MaterialSearch.vue')
);
const PipeDiameterSearch = defineAsyncComponent(
  () => import('./components/search/PipeDiameterSearch.vue')
);
const CompleteTimeSearch = defineAsyncComponent(
  () => import('./components/search/CompleteTimeSearch.vue')
);
const LengthStatistics = defineAsyncComponent(
  () => import('./components/statistics/LengthStatistics.vue')
);
const CountStatistics = defineAsyncComponent(
  () => import('./components/statistics/CountStatistics.vue')
);
const Measure = defineAsyncComponent(
  () => import('./components/common/Measure.vue')
);
const ThematicMap = defineAsyncComponent(
  () => import('./components/common/ThematicMap.vue')
);
const QuestionMark = defineAsyncComponent(
  () => import('./components/common/QuestionMark.vue')
);
const ProductionMonitoring = defineAsyncComponent(
  () => import('./components/shuiwu/ProductionMonitoring.vue')
);
const BumbMonitoring = defineAsyncComponent(
  () => import('./components/shuiwu/BumbMonitoring.vue')
);
const DMA = defineAsyncComponent(() => import('./components/shuiwu/DMA.vue'));
const WaterSearch = defineAsyncComponent(
  () => import('./components/yinshou/WaterSearch.vue')
);
const UserSearch = defineAsyncComponent(
  () => import('./components/yinshou/UserSearch.vue')
);
const UserLocate = defineAsyncComponent(
  () => import('./components/yinshou/UserLocate.vue')
);
const TemplatePrint = defineAsyncComponent(
  () => import('./components/common/TemplatePrint.vue')
);
const PicturePrint = defineAsyncComponent(
  () => import('./components/common/PicturePrint.vue')
);
const state = reactive<{
  panelOperate: string;
  defaultSubMenu: string[];
  title: string;
  mounted: boolean;
}>({
  panelOperate: '',
  defaultSubMenu: [],
  title: '',
  mounted: false
});
const staticState: {
  layerListBar: any;
  view?: __esri.MapView;
} = {
  layerListBar: undefined,
  view: undefined
};
const handleHorizontalMenuSelect = (item) => {
  const key = item?.path;
  switch (key) {
    case 'tygj_tckz':
      staticState.layerListBar?.toggle && staticState.layerListBar?.toggle();
      break;
    case 'quanqin':
      refSmartPipe.value && toggleFullscreen(refSmartPipe.value);
      break;
    default:
      state.title = item?.meta?.title;
      state.panelOperate = key;
      refDrawerBox.value?.toggleDrawer('rtl', true);
      break;
  }
};

onBeforeMount(async () => {
  const gisStore = useGisStore();
  if (!gisStore.gToken) {
    const res = await AutoLogin();
    if (res.data?.code === 10000) {
      gisStore.SET_gToken(res.data?.result?.token);
      gisStore.SET_gUserInfo(res.data?.result);
    }
  }
});
onMounted(async () => {
  staticState.view = await refMap.value?.init();
  state.mounted = true;
  staticState.view?.when(() => {
    if (!staticState.view) return;
    // 左上角
    addCustomWidget(staticState.view, 'gis-horizontal-menu', 'top-left');
    // addCustomWidget(staticState.view, 'gis-menubar', 'top-left')
    // addCustomWidget(staticState.view, 'gis-submenu', 'top-left')
    // 右上角
    initSearch(staticState.view, 'top-right');
    staticState.layerListBar = layerList.init(staticState.view);

    // 右下角
    refArcBRTools.value?.init();
    // 左下角
    coordinate.init(staticState.view);
    scale.init(staticState.view);
  });
});
onUnmounted(() => {
  staticState.view?.map.removeAll();
  staticState.view?.map.destroy();
  staticState.view?.destroy();
  staticState.view = undefined;
});
</script>
<style lang="scss" scoped>
.smartpipe {
  width: 100%;
  height: 100%;
}
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;

  :deep(.esri-ui-top-left) {
    flex-flow: row;
  }

  :deep(.esri-ui-bottom-right) {
    flex-flow: column;
  }
}

.custom-menubar,
.custom-horizontal-menu,
.custom-submenubar {
  width: auto;
  height: auto;
  border-radius: 4px;
  background-color: transparent;
  margin: 0;
  margin-right: 12px;
}

:deep(.esri-ui-bottom-right) {
  &.esri-widget--button,
  .esri-widget--button {
    border-top: solid 1px rgba(173, 173, 173, 0.3);
  }
}
.drawer-right {
  width: 100%;
  height: 100%;
}
.drawer-right-title {
  height: 40px;
  background-color: #c7c7c7;
  display: flex;
  align-items: center;
  padding: 0 15px;
  // margin-bottom: 15px;
}
.drawer-right-content {
  height: calc(100% - 40px);
  width: 100%;
  padding: 15px;
}
.darkblue {
  .drawer-right-title {
    background-color: #1f2d40;
  }
}
</style>
