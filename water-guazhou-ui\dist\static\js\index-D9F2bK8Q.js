import{d as Y,M as R,c as p,a8 as r,s as M,r as c,x as y,bQ as T,a9 as u,o as P,g as E,n as N,q as m,i as f,b6 as w,b7 as z}from"./index-r0dFAfgr.js";import{_ as F}from"./CardTable-rdWOL4_6.js";import{_ as G}from"./CardSearch-CB_HNR-Q.js";import{I as x}from"./common-CvK_P_ao.js";import{e as W,f as H,g as B,b as j,h as A}from"./equipmentOutStock-BiNkB8x8.js";import{b as O,c as Q,a as U}from"./equipmentPurchase-KOqzaoYr.js";import{l as $,r as K}from"./equipmentManage-DuoY00aj.js";import{f as v}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const J={class:"wrapper"},ne=Y({__name:"index",setup(X){const{$btnPerms:D}=R(),n=p(),b=p(),S=p(),g=p(),h=p([]),I=p({filters:[{label:"入库单编码",field:"code",type:"input",labelWidth:"90px"},{label:"入库单标题",field:"title",type:"input",labelWidth:"90px"},{label:"发票编号",field:"invoiceCode",type:"input"},{label:"仓库名称",field:"storehouseId",type:"select",labelWidth:"90px",options:r(()=>t.storeList)},{type:"department-user",label:"验收人",field:"acceptor"},{type:"department-user",label:"经办人",field:"manager"},{label:"入库时间",field:"time",type:"daterange",labelWidth:"120px"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:x.QUERY,click:()=>d()},{type:"default",perm:!0,text:"重置",svgIcon:M(z),click:()=>{var e;(e=n.value)==null||e.resetForm(),d()}},{type:"success",perm:!0,text:"新增",icon:x.ADD,click:()=>q()}]}]}),s=c({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"单据号",prop:"code"},{label:"标题",prop:"title"},{label:"批次号",prop:"batchCode"},{label:"发票号",prop:"invoiceCode"},{label:"目标仓库",prop:"storehouseName"},{label:"入库时间",prop:"inTime",formatter:e=>v(e.createTime,"YYYY-MM-DD")},{label:"验收人",prop:"acceptorName"},{label:"经办人",prop:"managerName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>v(e.createTime,"YYYY-MM-DD")},{label:"供应商",prop:"supplierName"}],operationWidth:"160px",operations:[{type:"primary",color:"#4195f0",text:"详情",perm:D("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>k(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{s.pagination.page=e,s.pagination.limit=a,d()}}}),_=c({title:"添加",labelWidth:"100px",width:"1300px",submit:(e,a)=>{var l;if(a){t.getDevice(),(l=g.value)==null||l.openDrawer();return}if(!e.items.some(i=>i.shelvesId&&i.num&&i.price&&i.taxRete)){y.warning("设备信息未填写完整");return}W(e).then(()=>{var i;d(),y.success("添加成功"),(i=b.value)==null||i.closeDrawer()}).catch(i=>{y.warning(i)})},defaultValue:{},group:[{fields:[{readonly:!0,xl:8,type:"input-number",label:"入库单编号",field:"code",rules:[{required:!0,message:"请输入入库单编号"}]},{xl:8,type:"input",label:"入库单标题",field:"title",rules:[{required:!0,message:"请输入入库单标题"}]},{xl:8,type:"input",label:"批次号",field:"batchCode"},{xl:8,type:"select",label:"相关采购单",field:"purchaseId",options:r(()=>t.DevicePurchase),onChange:e=>t.getdetail(e)},{xl:8,type:"select",label:"目标仓库",field:"storehouseId",rules:[{required:!0,message:"请选择目标仓库"}],options:r(()=>t.storeList),onChange:e=>t.getGoodsShelfValue(e)},{xl:8,type:"department-user",label:"验收人",field:"acceptor"},{xl:8,type:"department-user",label:"经办人",field:"manager",rules:[{required:!0,message:"请选择经办人"}]},{xl:8,type:"select",label:"所属合同",field:"contractId",options:r(()=>t.contract)},{xl:8,type:"input-number",label:"发票编号",field:"invoiceCode"},{xl:8,type:"select",label:"供应商",field:"supplierId",rules:[{required:!0,message:"请选择供应商"}],options:r(()=>t.SupplierList)},{xl:8,type:"date",label:"入库时间",field:"inTime",rules:[{required:!0,message:"请输入入库时间"}],format:"YYYY-MM-DD HH:mm:ss"},{xl:8,type:"switch",label:"是否补录",field:"addRecord"},{xl:18,type:"textarea",label:"备注/说明",field:"remark"},{type:"table",field:"items",config:{titleRight:[{style:{justifyContent:"flex-end",marginBottom:"10px"},items:[{type:"btn-group",btns:[{text:"增加设备",perm:!0,click:()=>{var e;(e=b.value)==null||e.Submit(!0)}},{text:"导入",perm:!0,click:()=>{}}]}]}],indexVisible:!0,height:"350px",dataList:r(()=>t.selectList),columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"顶级类别",prop:"topType"},{label:"货架",prop:"shelvesId",formItemConfig:{type:"select-tree",checkStrictly:!0,options:r(()=>t.GoodsShelf)}},{label:"数量",prop:"num",formItemConfig:{type:"number",min:0}},{label:"单价(元)",prop:"price",formItemConfig:{type:"number",min:0}},{label:"税率",prop:"taxRete",formItemConfig:{type:"number",min:0}}],operationWidth:80,operations:[{text:"移除",type:"danger",icon:x.DELETE,perm:D("RoleManageDelete"),click:e=>{t.selectList=t.selectList.filter(a=>a.id!==e.id)}}],pagination:{hide:!0}}}]}]}),L=c({title:"详情",labelWidth:"100px",defaultValue:{},group:[{fields:[{readonly:!0,xl:8,type:"input-number",label:"入库单编号",field:"code",rules:[{required:!0,message:"请输入入库单编号"}]},{disabled:!0,xl:8,type:"input",label:"入库单标题",field:"title",rules:[{required:!0,message:"请输入入库单标题"}]},{disabled:!0,xl:8,type:"input",label:"批次号",field:"batchCode"},{readonly:!0,xl:8,type:"select",label:"相关采购单",field:"purchaseId",options:r(()=>t.DevicePurchase),onChange:e=>t.getdetail(e)},{readonly:!0,xl:8,type:"select",label:"目标仓库",field:"storehouseId",rules:[{required:!0,message:"请选择目标仓库"}],options:r(()=>t.storeList),onChange:e=>t.getGoodsShelfValue(e)},{disabled:!0,xl:8,type:"input",label:"验收人",field:"acceptorName"},{disabled:!0,xl:8,type:"input",label:"经办人",field:"managerName",rules:[{required:!0,message:"请选择经办人"}]},{readonly:!0,xl:8,type:"select",label:"所属合同",field:"contractId",options:r(()=>t.contract)},{readonly:!0,xl:8,type:"input-number",label:"发票编号",field:"invoiceCode"},{readonly:!0,xl:8,type:"select",label:"供应商",field:"supplierId",rules:[{required:!0,message:"请选择供应商"}],options:r(()=>t.SupplierList)},{readonly:!0,xl:8,type:"date",label:"入库时间",field:"inTime",rules:[{required:!0,message:"请输入入库时间"}]},{readonly:!0,xl:8,type:"switch",label:"是否补录",field:"addRecord"},{disabled:!0,xl:18,type:"textarea",label:"备注/说明",field:"remark"},{type:"table",field:"items",config:{indexVisible:!0,height:"350px",dataList:r(()=>t.selectList),columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"顶级类别",prop:"topType"},{label:"货架",prop:"shelvesName"},{label:"数量",prop:"num"},{label:"单价(元)",prop:"price"},{label:"税率",prop:"taxRete"}],pagination:{hide:!0}}}]}]}),V=c({title:"设备选择",submit:(e,a)=>{var l;a?(delete e.device,t.getDevice(e)):(t.selectList=[...t.selectList,...h.value],t.selectList=T(t.selectList,"id"),(l=g.value)==null||l.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"设备编码",field:"serialId"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:r(()=>t.deviceValue),selectList:[],handleSelectChange:e=>{h.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"搜索",perm:!0,click:()=>{var e;(e=g.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"设备款式",prop:"model"},{label:"可用年限",prop:"useYear"}],pagination:{hide:!0}}}]}]}),q=()=>{var e;_.defaultValue={code:"RK"+v(new Date,"YYYYMMDDHHmmss")},h.value=[],t.selectList=[],(e=b.value)==null||e.openDrawer()},k=e=>{var a;for(const l in e)(e[l]===void 0||e[l]===null)&&(e[l]=" ");L.defaultValue={...e||{}},H({page:1,size:99999,mainId:e.id}).then(l=>{t.selectList=l.data.data.data||[]}),(a=S.value)==null||a.openDrawer()},t=c({DevicePurchase:[],storeList:[],deviceValue:[],selectList:[],SupplierList:[],contract:[],GoodsShelf:[],getDevice:e=>{const a={size:99999,page:1,...e};$(a).then(l=>{t.deviceValue=l.data.data.data||[]})},getDevicePurchaseValue:()=>{O({page:1,size:99999}).then(a=>{t.DevicePurchase=u(a.data.data.data||[],"children",{label:"title",value:"id"})})},getstoreSerchValue:()=>{B({page:1,size:99999}).then(a=>{t.storeList=u(a.data.data.data||[])})},getSupplierValue:()=>{K({page:1,size:99999}).then(a=>{t.SupplierList=u(a.data.data.data||[])})},getGoodsShelfValue:e=>{j({page:1,size:99999,id:e}).then(l=>{t.GoodsShelf=u(l.data.data.data[0].children||[],"children",{label:"name",value:"id"})})},getDeviceapiContractDetailValue:()=>{Q({page:1,size:99999}).then(a=>{t.contract=u(a.data.data.data||[],"children",{label:"title",value:"id"})})},getdetail:e=>{if(!e){t.selectList=[];return}U({page:1,size:99999,mainId:e}).then(l=>{t.selectList=l.data.data.data||[]})},init:()=>{t.getDevicePurchaseValue(),t.getstoreSerchValue(),t.getSupplierValue(),t.getDeviceapiContractDetailValue()}}),d=async()=>{var a,l,i,o;const e={size:s.pagination.limit,page:s.pagination.page,...((a=n.value)==null?void 0:a.queryParams)||{}};e.time&&((l=e.time)==null?void 0:l.length)>1&&(e.fromTime=((i=n.value)==null?void 0:i.queryParams).time[0]||"",e.toTime=((o=n.value)==null?void 0:o.queryParams).time[1]||""),delete e.time,A(e).then(C=>{s.dataList=C.data.data.data||[],s.pagination.total=C.data.data.total||0})};return P(()=>{d(),t.init()}),(e,a)=>{const l=G,i=F,o=w;return E(),N("div",J,[m(l,{ref_key:"refSearch",ref:n,config:f(I)},null,8,["config"]),m(i,{config:f(s),class:"card-table"},null,8,["config"]),m(o,{ref_key:"refForm",ref:b,config:f(_)},null,8,["config"]),m(o,{ref_key:"detailForm",ref:S,config:f(L)},null,8,["config"]),m(o,{ref_key:"refFormEquipment",ref:g,config:f(V)},null,8,["config"])])}}});export{ne as default};
