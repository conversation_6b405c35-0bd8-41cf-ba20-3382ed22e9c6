"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6841],{88669:(n,e,t)=>{function i(){return[0,0,0,0]}function a(n,e,t,i){return[n,e,t,i]}function o(n,e){return new Float64Array(n,e,4)}function r(){return a(1,1,1,1)}function l(){return a(1,0,0,0)}function s(){return a(0,1,0,0)}function u(){return a(0,0,1,0)}function f(){return a(0,0,0,1)}t.d(e,{a:()=>o,c:()=>i,f:()=>a});const c=r(),m=l(),d=s(),p=u(),x=f();Object.freeze(Object.defineProperty({__proto__:null,ONES:c,UNIT_W:x,UNIT_X:m,UNIT_Y:d,UNIT_Z:p,ZEROS:[0,0,0,0],clone:function(n){return[n[0],n[1],n[2],n[3]]},create:i,createView:o,fromArray:function(n){const e=[0,0,0,0],t=Math.min(4,n.length);for(let i=0;i<t;++i)e[i]=n[i];return e},fromValues:a,ones:r,unitW:f,unitX:l,unitY:s,unitZ:u,zeros:function(){return[0,0,0,0]}},Symbol.toStringTag,{value:"Module"}))},62357:(n,e,t)=>{t.d(e,{F2:()=>a,Wz:()=>o,t_:()=>r,vW:()=>l});const i=/^-?(\d+(\.\d+)?)\s*((px)|(pt))?$/i;function a(n){return n?n/72*96:0}function o(n){return n?72*n/96:0}function r(n){if("string"==typeof n){const e=n.match(i);if(e){const t=Number(e[1]),i=e[3]&&e[3].toLowerCase(),a="-"===n.charAt(0),r="px"===i?o(t):t;return a?-r:r}return console.warn("screenUtils.toPt: input not recognized!"),null}return n}function l(n=0,e=0){return{x:n,y:e}}},73572:(n,e,t)=>{t.d(e,{AJ:()=>r,If:()=>s,QM:()=>l,k0:()=>o,nu:()=>a,wx:()=>u}),t(22021);var i=t(62357);t(98766),t(88669);const a=2.4;function o(n){return(0,i.Wz)(n*a)}function r(n){return(0,i.F2)(n)/a}function l(n,e,t,a){const{radius:o,fieldOffset:r,field:l}=e,u=Math.round((0,i.F2)(o)),f=new Float64Array(t*a);let c,m=Number.NEGATIVE_INFINITY;const d=function(n,e){return null!=n?"string"==typeof e?e=>-1*+e.readAttribute(n):t=>+t.readAttribute(n)+e:n=>1}(l,r),p=new Set;for(const e of n){const n=e.getCursor();for(;n.next();){const e=n.getObjectId();if(p.has(e))continue;p.add(e);const i=n.readLegacyPointGeometry(),o=128;if(i.x<-o||i.x>=t+o||i.y<-o||i.y>a+o)continue;const r=+d(n),l=Math.max(0,Math.round(i.x)-u),x=Math.max(0,Math.round(i.y)-u),y=Math.min(a,Math.round(i.y)+u),I=Math.min(t,Math.round(i.x)+u);for(let n=x;n<y;n++)for(let e=l;e<I;e++){const a=n*t+e,o=s(i.x-e,i.y-n,u);c=f[a]+=o*r,c>m&&(m=c)}}}return{matrix:f.buffer,max:m}}function s(n,e,t){const i=Math.sqrt(n**2+e**2)/t;return i>1?0:3/(Math.PI*t**2)*(1-i**2)**2}function u(n,e){return"function"==typeof n?n:n?"string"==typeof e?e=>-1*+e[n]:t=>+t[n]+e:()=>1}},36841:(n,e,t)=>{t.r(e),t.d(e,{classBreaks:()=>y,heatmapStatistics:()=>v,histogram:()=>I,summaryStatistics:()=>p,uniqueValues:()=>x}),t(66577),t(20102);var i=t(70586),a=t(62357),o=t(82971),r=t(82397),l=t(8744),s=(t(35671),t(73572)),u=t(24452),f=t(59266),c=t(94139);let m=null;async function d(n,e){if(!e)return[];const{field:t,field2:i,field3:a,fieldDelimiter:r}=n,l=n.valueExpression,s=n.normalizationType,c=n.normalizationField,d=n.normalizationTotal,p=[],x=n.viewInfoParams;let y=null,I=null;if(l){if(!m){const{arcadeUtils:n}=await(0,f.LC)();m=n}y=m.createFunction(l),I=x&&m.getViewInfo({viewingMode:x.viewingMode,scale:x.scale,spatialReference:new o.Z(x.spatialReference)})}const v=n.fieldInfos,h=e[0]&&"declaredClass"in e[0]&&"esri.Graphic"===e[0].declaredClass||!v?null:{fields:v};return e.forEach((n=>{const e=n.attributes;let o;if(l){const e=h?{...n,layer:h}:n,t=m.createExecContext(e,I);o=m.executeFunction(y,t)}else e&&(o=e[t],i&&(o=`${(0,u.wk)(o)}${r}${(0,u.wk)(e[i])}`,a&&(o=`${o}${r}${(0,u.wk)(e[a])}`)));if(s&&"number"==typeof o&&isFinite(o)){const n=e&&parseFloat(e[c]);o=(0,u.fk)(o,s,n,d)}p.push(o)})),p}async function p(n){const{attribute:e,features:t}=n,{normalizationType:i,normalizationField:a,minValue:o,maxValue:r,fieldType:l}=e,s=await d({field:e.field,valueExpression:e.valueExpression,normalizationType:i,normalizationField:a,normalizationTotal:e.normalizationTotal,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},t),f=(0,u.S5)({normalizationType:i,normalizationField:a,minValue:o,maxValue:r}),c={value:.5,fieldType:l},m="esriFieldTypeString"===l?(0,u.H0)({values:s,supportsNullCount:f,percentileParams:c}):(0,u.i5)({values:s,minValue:o,maxValue:r,useSampleStdDev:!i,supportsNullCount:f,percentileParams:c});return(0,u.F_)(m,"esriFieldTypeDate"===l)}async function x(n){const{attribute:e,features:t}=n,i=await d({field:e.field,field2:e.field2,field3:e.field3,fieldDelimiter:e.fieldDelimiter,valueExpression:e.valueExpression,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},t),a=(0,u.eT)(i);return(0,u.Qm)(a,e.domains,e.returnAllCodedValues,e.fieldDelimiter)}async function y(n){const{attribute:e,features:t}=n,{field:i,normalizationType:a,normalizationField:o,normalizationTotal:r,classificationMethod:l}=e,s=await d({field:i,valueExpression:e.valueExpression,normalizationType:a,normalizationField:o,normalizationTotal:r,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},t),f=(0,u.G2)(s,{field:i,normalizationType:a,normalizationField:o,normalizationTotal:r,classificationMethod:l,standardDeviationInterval:e.standardDeviationInterval,numClasses:e.numClasses,minValue:e.minValue,maxValue:e.maxValue});return(0,u.DL)(f,l)}async function I(n){const{attribute:e,features:t}=n,{field:i,normalizationType:a,normalizationField:o,normalizationTotal:r,classificationMethod:l}=e,s=await d({field:i,valueExpression:e.valueExpression,normalizationType:a,normalizationField:o,normalizationTotal:r,viewInfoParams:e.viewInfoParams,fieldInfos:e.fieldInfos},t);return(0,u.oF)(s,{field:i,normalizationType:a,normalizationField:o,normalizationTotal:r,classificationMethod:l,standardDeviationInterval:e.standardDeviationInterval,numBins:e.numBins,minValue:e.minValue,maxValue:e.maxValue})}async function v(n){const{attribute:e,features:t}=n,{field:o,radius:u,fieldOffset:f,transform:m,spatialReference:d,size:p}=e,x=function(n,e,t,a){const o=(0,l.MP)(t)?(0,l.C5)(t):null,s=o?Math.round((o.valid[1]-o.valid[0])/e.scale[0]):null;return n.map((n=>{const t=new c.Z((0,i.Wg)(n.geometry));return(0,r.RF)(e,t,t,t.hasZ,t.hasM),n.geometry=o?function(n,e,t){return n.x<0?n.x+=e:n.x>t&&(n.x-=e),n}(t,s,a[0]):t,n}))}(t,m,d,p),{count:y,min:I,max:v,mean:h,stdDev:z}=function(n,e=18,t,i,o,r){const l=new Float64Array(o*r);e=Math.round((0,a.F2)(e));let u=Number.POSITIVE_INFINITY,f=Number.NEGATIVE_INFINITY,c=0,m=0,d=0,p=0;const x=(0,s.wx)(i,t);for(const{geometry:t,attributes:i}of n){const{x:n,y:a}=t,y=Math.max(0,n-e),I=Math.max(0,a-e),v=Math.min(r,a+e),h=Math.min(o,n+e),z=+x(i);for(let t=I;t<v;t++)for(let i=y;i<h;i++){const r=t*o+i,x=(0,s.If)(i-n,t-a,e),y=l[r];c=l[r]+=x*z;const I=c-y;m+=I,d+=I*I,c<u&&(u=c),c>f&&(f=c),p++}}if(!p)return{mean:0,stddev:0,min:0,max:0,mid:0,count:0};const y=(f-u)/2;return{mean:m/p,stdDev:Math.sqrt((d-m*m/p)/p),min:u,max:f,mid:y,count:p}}(x,u,f,o,p[0],p[1]);return{count:y,min:I,max:v,avg:h,stddev:z}}}}]);