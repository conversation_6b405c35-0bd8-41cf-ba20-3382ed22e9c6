<template>
  <div class="headerbar-menu">
    <template v-for="(route, index) in menuRoutes">
      <!-- 只处理二级及以上的，因为第一级为布局 -->
      <AppHeadMenuItem
        v-if="route.children?.length"
        :key="index"
        :item="route"
        :level="0"
        :icon="route.meta?.icon"
        @submenu-click="handleSubMenuClick"
      ></AppHeadMenuItem>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore, usePermissionStore } from '@/store';
import { filterRoutes } from '@/utils/RouterHelper';
import AppHeadMenuItem from './AppHeadMenuItem.vue';

const appStore = useAppStore();
const router = useRouter();
const keyword = ref<string>('');
const menuRoutes = computed(() => {
  return filterRoutes(usePermissionStore().routers, keyword.value, []);
});

const handleSubMenuClick = async (cMenu?: any) => {
  debugger
  const hasChild = !!cMenu?.children?.length;
  appStore.TOGGLE_submenuShow(hasChild);
  appStore.SET_subMenuParentRoute(cMenu);
  if (!hasChild) {
    router.push({ ...(cMenu || {}) });
    // appStore.TOGGLE_menuShow(false)
  }
};
defineExpose({
  keyword
});
</script>
<style lang="scss" scoped></style>
