package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.MaintenancePlanCEntity;
import org.thingsboard.server.dao.model.sql.RepairPlanCEntity;

import java.util.List;

public interface MaintenancePlanCRepository extends JpaRepository<MaintenancePlanCEntity, String> {
    List<MaintenancePlanCEntity> findByMainIdOrderByOrderNumber(String mainId);

    @Modifying
    @Transactional
    void removeByMainId(String id);
}
