<template>
  <div class="wrapper">
    <CardSearch ref="cardSearch" :config="cardSearchConfig" />
    <CardTable :config="cardTableConfig" class="card-table" />
  </div>
</template>

<script lang="ts" setup>
import moment from 'moment';
import { ref, onBeforeMount, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { getLoginLogs } from '@/api/dailyLog';
import { useUserStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';

const userStore = useUserStore();
const cardSearchConfig = reactive<ISearch>({
  filters: [
    { label: '搜索', field: 'keyword', type: 'input' },
    {
      type: 'btn-group',
      btns: [{ perm: true, text: '查询', click: () => refreshData() }]
    }
    // {
    //   label: '查询时间',
    //   key: 'timerange',
    //   type: 'daterange',
    //   width: '360px'
    // }
  ],
  defaultParams: {
    timerange: [
      moment().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
      moment().format('YYYY-MM-DD HH:mm:ss')
    ]
  }
});

const cardTableConfig = ref<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    {
      prop: 'time',
      label: '时间',
      icon: 'iconfont icon-shijian',
      iconStyle: {
        color: '#69E850',
        display: 'inline-block',
        'font-size': '16px'
      }
    },
    { prop: 'name', label: '用户名' },
    { prop: 'authority', label: '用户权限' },
    { prop: 'options', label: '操作类型' },
    { prop: 'typeDescription', label: '操作内容' }
  ],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    handleSize: (val) => {
      if (cardTableConfig.value.pagination) {
        cardTableConfig.value.pagination.limit = val;
      }
      refreshData();
    },
    handlePage: (val) => {
      if (cardTableConfig.value.pagination) {
        cardTableConfig.value.pagination.page = val;
      }
      refreshData();
    }
  }
});

const cardSearch = ref<any>(null);

const refreshData = async () => {
  cardTableConfig.value.loading = true;
  const paramsObj: any = {
    // timerange: [moment().subtract(1, 'days'), moment()]
  };
  if (cardSearch.value) Object.assign(paramsObj, cardSearch.value.queryParams);
  const param = {
    // startTime: moment(paramsObj.timerange[0]).valueOf(),
    // endTime: moment(paramsObj.timerange[1]).add(1, 'days').valueOf(),
    page: cardTableConfig.value.pagination?.page,
    size: cardTableConfig.value.pagination?.limit,
    keyword: paramsObj.keyword
  };
  try {
    getLoginLogs(param).then((res) => {
      if (res.status !== 200) return;
      console.log(res);
      let resultData = [];
      resultData = res.data.data.map((item) => {
        const resultItem = item;
        resultItem.name = item.firstName;
        resultItem.time = formatDate(item.createTime);
        if (resultItem.authority === 'TENANT_ADMIN') {
          resultItem.authority = '企业管理人员';
        } else if (resultItem.authority === 'TENANT_SYS') {
          resultItem.authority = '企业配置人员';
        } else if (resultItem.authority === 'CUSTOMER_USER') {
          resultItem.authority = '企业用户';
        } else if (resultItem.authority === 'SYS_ADMIN') {
          resultItem.authority = '超级管理员';
        }
        if (resultItem.info === 'Login') {
          resultItem.info = '登录';
        }
        return resultItem;
      });
      if (cardTableConfig.value.pagination) {
        cardTableConfig.value.pagination.total = res.data.total;
      }
      cardTableConfig.value.dataList = resultData;
      cardTableConfig.value.loading = false;
    });
  } catch (error) {
    cardTableConfig.value.loading = false;
  }
};

const hasTenantFilter = ref<boolean>(false);

const getTenants = () => {
  const tenantList = userStore.tenantList;
  if (!tenantList.length) {
    ElMessage.info('该账户下没有企业信息');
    return;
  }
  const filter = cardSearchConfig.filters?.find(
    (item) => item.field === ''
  ) as IFormSelect;
  filter &&
    (filter.options = tenantList.map((tenant) => ({
      label: tenant.title,
      value: tenant.id
    })));
  cardSearch.value.queryParams.tenantId = tenantList[0].id;
  refreshData();
};

onBeforeMount(() => {
  // this.getData()
  hasTenantFilter.value =
    userStore.roles[0] === 'TENANT_SUPPORT' ||
    userStore.roles[0] === 'TENANT_PROMOTE';
  if (hasTenantFilter.value) {
    cardSearchConfig.filters?.splice(1, 0, {
      label: '企业',
      field: 'tenantId',
      type: 'select',
      options: []
    });
    getTenants();
    return;
  }
  refreshData();
});
</script>

<style lang="scss" scoped>
.tree-right-detail-box {
  height: 100%;
  margin: 15px;
}
.cardTable {
  height: calc(100% - 130px);
}
</style>
