<template>
  <Form
    ref="refForm"
    :config="FormConfig"
  ></Form>
  <Form
    ref="refTestForm"
    :config="TestFormConfig"
  ></Form>
</template>
<script lang="ts" setup>
import { getSMKey, sendMes, setMesKey } from '@/api/admin'
import { IFormIns } from '@/components/type'
import { SLMessage } from '@/utils/Message'
import { validatePhone } from '@/utils/formValidate'

const refForm = ref<IFormIns>()
const refTestForm = ref<IFormIns>()
const validateText = (rule, value, callback) => {
  if (value.trim() !== '') {
    callback()
  } else {
    callback(new Error('请输入有效字符 空格无效'))
  }
}
const validateID = (rule, value, callback) => {
  if (/[\u4E00-\u9FA5]/g.test(value)) {
    callback(new Error('不能输入汉字'))
  } else {
    return callback()
  }
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select',
          field: 'service',
          label: '服务提供商',
          placeholder: '请选择短信服务提供商',
          options: [{ label: '云片网', value: 'ypw' }],
          rules: [
            {
              required: true,
              message: '请选择短信服务提供商',
              trigger: 'change'
            }
          ]
        },
        {
          type: 'input',
          field: 'smsDeviceKey',
          label: '设备模板ID',
          rules: [
            { required: true, message: '请输入设备模板ID', trigger: 'blur' },
            { validator: validateID, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '触发模板ID',
          field: 'smsModelKey',
          rules: [
            { required: true, message: '请输入触发模板ID', trigger: 'blur' },
            { validator: validateID, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '验证模板ID',
          field: 'captchaKey',
          rules: [
            { required: true, message: '请输入验证模板ID', trigger: 'blur' },
            { validator: validateID, trigger: 'blur' }
          ]
        },
        {
          type: 'password',
          label: 'API key',
          field: 'smsAppKey',
          autocomplete: false,
          placeholder: '请输入API key',
          rules: [
            { required: true, message: '请输入apiKey', trigger: 'blur' },
            { validator: validateText, trigger: 'blur' }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '140px',
  defaultValue: {
    service: 'ypw',
    smsAppKey: 'dc2b77c6e1f351ab1bef55991642ff16',
    smsModelKey: '3716214', // 暂时写死
    smsDeviceKey: '3715352',
    captchaKey: '3770334',
    sendMes: ''
  },
  submit(params) {
    saveShortMsgConfig(params)
  }
})
const TestFormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '发送短信测试至',
          field: 'sendMes',
          inputType: 'text',
          rules: [
            { required: true, message: '请输入手机号' },
            { validator: validatePhone }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'warning',
              isTextBtn: false,
              text: computed(() => (show.value ? `${count.value} 秒后重新发送` : '发送测试短信')) as any,
              iconifyIcon: computed(() => (show.value ? 'ep:loading' : 'ep:promotion')) as any,
              click: () => {
                if (show.value) return
                refTestForm.value?.Submit()
              }
            },
            {
              perm: true,
              text: '保存',
              click: () => {
                refForm.value?.Submit()
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '140px',
  defaultValue: {},
  submit: (params: any) => {
    handlesendTestMes(params)
  }
})
/**
 * 短信计时时长（秒）
 */
const TIME_COUNT = 60
const timer = ref<any>(null)
const count = ref<number>(TIME_COUNT)
const show = ref<boolean>(false)
/** 发送测试短信 */
const handlesendTestMes = (params: { sendMes: string }) => {
  if (!timer.value) {
    sendTest(params.sendMes)
    count.value = TIME_COUNT
    show.value = false
    timer.value = setInterval(() => {
      if (count.value > 0 && count.value <= TIME_COUNT) {
        count.value--
      } else {
        show.value = true
        clearInterval(timer.value)
        timer.value = null
      }
    }, 1000)
  }
}

const sendTest = (params: any) => {
  sendMes(params)
    .then(res => {
      if (res.data) {
        SLMessage.success('发送成功')
      } else {
        SLMessage.warning('发送短信失败，请检查配置信息是否正确')
      }
    })
    .catch(err => {
      console.log(err)
      SLMessage.error('发送失败，请检查输入的电话号码是否存在！')
    })
}
// 短信配置保存
const saveShortMsgConfig = async (shortMessage: any) => {
  const params = {
    smsAppKey: shortMessage.smsAppKey,
    smsDeviceKey: shortMessage.smsDeviceKey,
    smsModelKey: shortMessage.smsModelKey,
    captchaKey: shortMessage.captchaKey
  }
  setMesKey(params).then(res => {
    if (res.data.captchaKey) {
      SLMessage.success('保存成功')
    }
  })
}
onMounted(() => {
  getSMKey().then(res => {
    FormConfig.defaultValue = {
      smsAppKey: res.data.smsAppKey,
      smsModelKey: res.data.smsModelKey,
      smsDeviceKey: res.data.smsDeviceKey,
      captchaKey: res.data.captchaKey
    }
    refForm.value?.resetForm()
  })
})
</script>
<style lang="scss" scoped></style>
