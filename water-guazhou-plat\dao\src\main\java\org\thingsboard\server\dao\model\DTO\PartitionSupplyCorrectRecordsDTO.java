package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分区产销差
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Data
public class PartitionSupplyCorrectRecordsDTO {

    private String id;

    private String partitionId;

    private String partitionName;

    private String deviceName;

    private String collectTime;

    private BigDecimal correctWater;

    private Date createTime;

    private String updateUser;

}
