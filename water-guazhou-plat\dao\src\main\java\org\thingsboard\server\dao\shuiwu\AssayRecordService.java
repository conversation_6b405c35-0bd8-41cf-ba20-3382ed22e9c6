package org.thingsboard.server.dao.shuiwu;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.AssayRecord;

import java.util.List;

public interface AssayRecordService {
    AssayRecord save(AssayRecord assayRecord);

    List<AssayRecord> findListByStationId(String stationId);

    List<AssayRecord> findListByTenantId(TenantId tenantId);

    Object waterQualityAnalysis(String stationId, Long start, Long end);
}
