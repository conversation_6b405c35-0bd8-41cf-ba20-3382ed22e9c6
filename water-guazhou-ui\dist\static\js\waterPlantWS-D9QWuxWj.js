const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ScadaPop-B0xTH3SS.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/zhandian-YaGuQZe6.js","static/js/DateFormatter-Bm9a68Ax.js","static/css/ScadaPop-CZ2Y6ymc.css"])))=>i.map(i=>d[i]);
import{d as T,c as M,r as m,ab as N,s as k,o as U,am as V,j as A,g as B,n as E,q as x,i as I,p as j,aa as q,_ as $,aq as z,a3 as G,C as O}from"./index-r0dFAfgr.js";import{w as Y}from"./Point-WxyopZva.js";import{c as H}from"./onemap-CEunQziB.js";import{P as J}from"./index-CcDafpIP.js";import{r as P}from"./chart-wy3NEK2T.js";import{g as K}from"./URLHelper-B9aplt5w.js";const Q={class:"onemap-panel-wrapper"},X={class:"table-box"},Z=T({__name:"waterPlantWS",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(W,{emit:F}){const L=q(()=>G(()=>import("./ScadaPop-B0xTH3SS.js"),__vite__mapDeps([0,1,2,3,4,5]))),u=F,d=W,f=M(),g=m({group:[{id:"chart",fieldset:{desc:"今日处水量各处理厂占比图",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:P(),style:{height:"150px"}}]},{fields:[{type:"input",field:"name",appendBtns:[{text:"刷新",perm:!0,click:()=>s()}],onChange:()=>s()}]}],labelPosition:"top",gutter:12}),e=m({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:o})=>{e.pagination.page=t,e.pagination.limit=o},layout:"total,sizes, jumper"},handleRowClick:t=>h(t),columns:[{width:120,label:"名称",prop:"name",sortable:!0},{width:130,label:"今日供水量(m³)",prop:"todayWaterSupply"},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),_=m({dataList:[],columns:[],pagination:{refreshData:({page:t,size:o})=>{_.pagination.page=t,_.pagination.limit=o,s()}}}),s=async()=>{var t,o,l,p,y,b;e.loading=!0;try{const i=await H({name:(t=f.value)==null?void 0:t.dataForm.name});e.dataList=((o=i.data)==null?void 0:o.data)||[];const w=g.group[0].fields[0],D=(p=(l=i.data)==null?void 0:l.data)==null?void 0:p.reduce((a,r,n,c)=>(console.log(a,r,n,c),a+Number(r.todayWaterSupply||"0")),0),v=[],R=((b=(y=i.data)==null?void 0:y.data)==null?void 0:b.map(a=>{var c,C;const r=N(a.todayWaterSupply||0),n=(c=a.location)==null?void 0:c.split(",");if((n==null?void 0:n.length)===2){const S=new Y({longitude:n[0],latitude:n[1],spatialReference:(C=d.view)==null?void 0:C.spatialReference});v.push({id:a.stationId,visible:!1,x:S.x,y:S.y,offsetY:-40,title:a.name,customComponent:k(L),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId,scadaUrl:a.scadaUrl}},attributes:{path:d.menu.path,id:a.stationId,row:a},symbolConfig:{url:K("水厂.png")}}),h(e.dataList[0]),e.currentRow=e.dataList[0]}return{name:a.name,value:a.todayWaterSupply,valueAlias:r.value.toFixed(2)+r.unit,scale:Number(a.todayWaterSupply)/D*100+"%"}}))||[];w&&(w.option=P(R,"m³")),u("addMarks",{windows:v,customWinComp:k(J)})}catch(i){console.dir(i)}e.loading=!1},h=async t=>{u("highlightMark",d.menu,t==null?void 0:t.stationId)};return U(()=>{s()}),V(()=>A().isDark,()=>s()),(t,o)=>{const l=$,p=z;return B(),E("div",Q,[x(l,{ref_key:"refForm",ref:f,config:I(g)},null,8,["config"]),j("div",X,[x(p,{config:I(e)},null,8,["config"])])])}}}),ia=O(Z,[["__scopeId","data-v-77b7ded9"]]);export{ia as default};
