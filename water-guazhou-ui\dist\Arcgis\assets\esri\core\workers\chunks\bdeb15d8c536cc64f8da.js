"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6610],{97575:(e,t,r)=>{r.d(t,{J:()=>s});var o,n,i,s={},a={get exports(){return s},set exports(e){s=e}};o=a,n=function(){var e=function(){function e(e){this.message="JPEG error: "+e}return e.prototype=new Error,e.prototype.name="JpegError",e.constructor=e,e}();return function(){if(!self||!self.Uint8ClampedArray)return null;var t=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),r=4017,o=799,n=3406,i=2276,s=1567,a=3784,l=5793,u=2896;function h(){this.decodeTransform=null,this.colorTransform=-1}function c(e,t){for(var r,o,n=0,i=[],s=16;s>0&&!e[s-1];)s--;i.push({children:[],index:0});var a,l=i[0];for(r=0;r<s;r++){for(o=0;o<e[r];o++){for((l=i.pop()).children[l.index]=t[n];l.index>0;)l=i.pop();for(l.index++,i.push(l);i.length<=r;)i.push(a={children:[],index:0}),l.children[l.index]=a.children,l=a;n++}r+1<s&&(i.push(a={children:[],index:0}),l.children[l.index]=a.children,l=a)}return i[0].children}function f(e,t,r){return 64*((e.blocksPerLine+1)*t+r)}function p(r,o,n,i,s,a,l,u,h){var c=n.mcusPerLine,p=n.progressive,d=o,g=0,y=0;function C(){if(y>0)return y--,g>>y&1;if(255===(g=r[o++])){var t=r[o++];if(t)throw new e("unexpected marker "+(g<<8|t).toString(16))}return y=7,g>>>7}function w(t){for(var r=t;;){if("number"==typeof(r=r[C()]))return r;if("object"!=typeof r)throw new e("invalid huffman sequence")}}function b(e){for(var t=0;e>0;)t=t<<1|C(),e--;return t}function x(e){if(1===e)return 1===C()?1:-1;var t=b(e);return t>=1<<e-1?t:t+(-1<<e)+1}var k,v=0,T=0;function S(e,t,r,o,n){var i=r%c;t(e,f(e,(r/c|0)*e.v+o,i*e.h+n))}function A(e,t,r){t(e,f(e,r/e.blocksPerLine|0,r%e.blocksPerLine))}var I,P,M,E,D,R,L=i.length;R=p?0===a?0===u?function(e,t){var r=w(e.huffmanTableDC),o=0===r?0:x(r)<<h;e.blockData[t]=e.pred+=o}:function(e,t){e.blockData[t]|=C()<<h}:0===u?function(e,r){if(v>0)v--;else for(var o=a,n=l;o<=n;){var i=w(e.huffmanTableAC),s=15&i,u=i>>4;if(0!==s){var c=t[o+=u];e.blockData[r+c]=x(s)*(1<<h),o++}else{if(u<15){v=b(u)+(1<<u)-1;break}o+=16}}}:function(r,o){for(var n,i,s=a,u=l,c=0;s<=u;){var f=t[s];switch(T){case 0:if(c=(i=w(r.huffmanTableAC))>>4,0==(n=15&i))c<15?(v=b(c)+(1<<c),T=4):(c=16,T=1);else{if(1!==n)throw new e("invalid ACn encoding");k=x(n),T=c?2:3}continue;case 1:case 2:r.blockData[o+f]?r.blockData[o+f]+=C()<<h:0==--c&&(T=2===T?3:0);break;case 3:r.blockData[o+f]?r.blockData[o+f]+=C()<<h:(r.blockData[o+f]=k<<h,T=0);break;case 4:r.blockData[o+f]&&(r.blockData[o+f]+=C()<<h)}s++}4===T&&0==--v&&(T=0)}:function(e,r){var o=w(e.huffmanTableDC),n=0===o?0:x(o);e.blockData[r]=e.pred+=n;for(var i=1;i<64;){var s=w(e.huffmanTableAC),a=15&s,l=s>>4;if(0!==a){var u=t[i+=l];e.blockData[r+u]=x(a),i++}else{if(l<15)break;i+=16}}};var _,B,U,G,O=0;for(B=1===L?i[0].blocksPerLine*i[0].blocksPerColumn:c*n.mcusPerColumn;O<B;){var N=s?Math.min(B-O,s):B;for(P=0;P<L;P++)i[P].pred=0;if(v=0,1===L)for(I=i[0],D=0;D<N;D++)A(I,R,O),O++;else for(D=0;D<N;D++){for(P=0;P<L;P++)for(U=(I=i[P]).h,G=I.v,M=0;M<G;M++)for(E=0;E<U;E++)S(I,R,O,M,E);O++}y=0,(_=m(r,o))&&_.invalid&&(console.log("decodeScan - unexpected MCU data, next marker is: "+_.invalid),o=_.offset);var F=_&&_.marker;if(!F||F<=65280)throw new e("marker was not found");if(!(F>=65488&&F<=65495))break;o+=2}return(_=m(r,o))&&_.invalid&&(console.log("decodeScan - unexpected Scan data, next marker is: "+_.invalid),o=_.offset),o-d}function d(t,h,c){var f,p,d,g,m,y,C,w,b,x,k,v,T,S,A,I,P,M=t.quantizationTable,E=t.blockData;if(!M)throw new e("missing required Quantization Table.");for(var D=0;D<64;D+=8)b=E[h+D],x=E[h+D+1],k=E[h+D+2],v=E[h+D+3],T=E[h+D+4],S=E[h+D+5],A=E[h+D+6],I=E[h+D+7],b*=M[D],0!=(x|k|v|T|S|A|I)?(x*=M[D+1],k*=M[D+2],v*=M[D+3],T*=M[D+4],S*=M[D+5],A*=M[D+6],I*=M[D+7],p=(f=(f=l*b+128>>8)+(p=l*T+128>>8)+1>>1)-p,P=(d=k)*a+(g=A)*s+128>>8,d=d*s-g*a+128>>8,C=(m=(m=u*(x-I)+128>>8)+(C=S<<4)+1>>1)-C,y=(w=(w=u*(x+I)+128>>8)+(y=v<<4)+1>>1)-y,g=(f=f+(g=P)+1>>1)-g,d=(p=p+d+1>>1)-d,P=m*i+w*n+2048>>12,m=m*n-w*i+2048>>12,w=P,P=y*o+C*r+2048>>12,y=y*r-C*o+2048>>12,C=P,c[D]=f+w,c[D+7]=f-w,c[D+1]=p+C,c[D+6]=p-C,c[D+2]=d+y,c[D+5]=d-y,c[D+3]=g+m,c[D+4]=g-m):(P=l*b+512>>10,c[D]=P,c[D+1]=P,c[D+2]=P,c[D+3]=P,c[D+4]=P,c[D+5]=P,c[D+6]=P,c[D+7]=P);for(var R=0;R<8;++R)b=c[R],0!=((x=c[R+8])|(k=c[R+16])|(v=c[R+24])|(T=c[R+32])|(S=c[R+40])|(A=c[R+48])|(I=c[R+56]))?(p=(f=4112+((f=l*b+2048>>12)+(p=l*T+2048>>12)+1>>1))-p,P=(d=k)*a+(g=A)*s+2048>>12,d=d*s-g*a+2048>>12,g=P,C=(m=(m=u*(x-I)+2048>>12)+(C=S)+1>>1)-C,y=(w=(w=u*(x+I)+2048>>12)+(y=v)+1>>1)-y,P=m*i+w*n+2048>>12,m=m*n-w*i+2048>>12,w=P,P=y*o+C*r+2048>>12,y=y*r-C*o+2048>>12,b=(b=(f=f+g+1>>1)+w)<16?0:b>=4080?255:b>>4,x=(x=(p=p+d+1>>1)+(C=P))<16?0:x>=4080?255:x>>4,k=(k=(d=p-d)+y)<16?0:k>=4080?255:k>>4,v=(v=(g=f-g)+m)<16?0:v>=4080?255:v>>4,T=(T=g-m)<16?0:T>=4080?255:T>>4,S=(S=d-y)<16?0:S>=4080?255:S>>4,A=(A=p-C)<16?0:A>=4080?255:A>>4,I=(I=f-w)<16?0:I>=4080?255:I>>4,E[h+R]=b,E[h+R+8]=x,E[h+R+16]=k,E[h+R+24]=v,E[h+R+32]=T,E[h+R+40]=S,E[h+R+48]=A,E[h+R+56]=I):(P=(P=l*b+8192>>14)<-2040?0:P>=2024?255:P+2056>>4,E[h+R]=P,E[h+R+8]=P,E[h+R+16]=P,E[h+R+24]=P,E[h+R+32]=P,E[h+R+40]=P,E[h+R+48]=P,E[h+R+56]=P)}function g(e,t){for(var r=t.blocksPerLine,o=t.blocksPerColumn,n=new Int16Array(64),i=0;i<o;i++)for(var s=0;s<r;s++)d(t,f(t,i,s),n);return t.blockData}function m(e,t,r){function o(t){return e[t]<<8|e[t+1]}var n=e.length-1,i=r<t?r:t;if(t>=n)return null;var s=o(t);if(s>=65472&&s<=65534)return{invalid:null,marker:s,offset:t};for(var a=o(i);!(a>=65472&&a<=65534);){if(++i>=n)return null;a=o(i)}return{invalid:s.toString(16),marker:a,offset:i}}return h.prototype={parse:function(r){function o(){var e=r[l]<<8|r[l+1];return l+=2,e}function n(){var e=o(),t=l+e-2,n=m(r,t,l);n&&n.invalid&&(console.log("readDataBlock - incorrect length, next marker is: "+n.invalid),t=n.offset);var i=r.subarray(l,t);return l+=i.length,i}function i(e){for(var t=Math.ceil(e.samplesPerLine/8/e.maxH),r=Math.ceil(e.scanLines/8/e.maxV),o=0;o<e.components.length;o++){O=e.components[o];var n=Math.ceil(Math.ceil(e.samplesPerLine/8)*O.h/e.maxH),i=Math.ceil(Math.ceil(e.scanLines/8)*O.v/e.maxV),s=t*O.h,a=r*O.v*64*(s+1);O.blockData=new Int16Array(a),O.blocksPerLine=n,O.blocksPerColumn=i}e.mcusPerLine=t,e.mcusPerColumn=r}var s,a,l=0,u=null,h=null,f=[],d=[],y=[],C=o();if(65496!==C)throw new e("SOI not found");for(C=o();65497!==C;){var w,b,x;switch(C){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var k=n();65504===C&&74===k[0]&&70===k[1]&&73===k[2]&&70===k[3]&&0===k[4]&&(u={version:{major:k[5],minor:k[6]},densityUnits:k[7],xDensity:k[8]<<8|k[9],yDensity:k[10]<<8|k[11],thumbWidth:k[12],thumbHeight:k[13],thumbData:k.subarray(14,14+3*k[12]*k[13])}),65518===C&&65===k[0]&&100===k[1]&&111===k[2]&&98===k[3]&&101===k[4]&&(h={version:k[5]<<8|k[6],flags0:k[7]<<8|k[8],flags1:k[9]<<8|k[10],transformCode:k[11]});break;case 65499:for(var v=o()+l-2;l<v;){var T=r[l++],S=new Uint16Array(64);if(T>>4==0)for(b=0;b<64;b++)S[t[b]]=r[l++];else{if(T>>4!=1)throw new e("DQT - invalid table spec");for(b=0;b<64;b++)S[t[b]]=o()}f[15&T]=S}break;case 65472:case 65473:case 65474:if(s)throw new e("Only single frame JPEGs supported");o(),(s={}).extended=65473===C,s.progressive=65474===C,s.precision=r[l++],s.scanLines=o(),s.samplesPerLine=o(),s.components=[],s.componentIds={};var A,I=r[l++],P=0,M=0;for(w=0;w<I;w++){A=r[l];var E=r[l+1]>>4,D=15&r[l+1];P<E&&(P=E),M<D&&(M=D);var R=r[l+2];x=s.components.push({h:E,v:D,quantizationId:R,quantizationTable:null}),s.componentIds[A]=x-1,l+=3}s.maxH=P,s.maxV=M,i(s);break;case 65476:var L=o();for(w=2;w<L;){var _=r[l++],B=new Uint8Array(16),U=0;for(b=0;b<16;b++,l++)U+=B[b]=r[l];var G=new Uint8Array(U);for(b=0;b<U;b++,l++)G[b]=r[l];w+=17+U,(_>>4==0?y:d)[15&_]=c(B,G)}break;case 65501:o(),a=o();break;case 65498:o();var O,N=r[l++],F=[];for(w=0;w<N;w++){var V=s.componentIds[r[l++]];O=s.components[V];var z=r[l++];O.huffmanTableDC=y[z>>4],O.huffmanTableAC=d[15&z],F.push(O)}var j=r[l++],W=r[l++],Z=r[l++],J=p(r,l,s,F,a,j,W,Z>>4,15&Z);l+=J;break;case 65535:255!==r[l]&&l--;break;default:if(255===r[l-3]&&r[l-2]>=192&&r[l-2]<=254){l-=3;break}throw new e("unknown marker "+C.toString(16))}C=o()}for(this.width=s.samplesPerLine,this.height=s.scanLines,this.jfif=u,this.eof=l,this.adobe=h,this.components=[],w=0;w<s.components.length;w++){var H=f[(O=s.components[w]).quantizationId];H&&(O.quantizationTable=H),this.components.push({output:g(0,O),scaleX:O.h/s.maxH,scaleY:O.v/s.maxV,blocksPerLine:O.blocksPerLine,blocksPerColumn:O.blocksPerColumn})}this.numComponents=this.components.length},_getLinearizedBlockData:function(e,t){var r,o,n,i,s,a,l,u,h,c,f,p=this.width/e,d=this.height/t,g=0,m=this.components.length,y=e*t*m,C=new Uint8ClampedArray(y),w=new Uint32Array(e),b=4294967288;for(l=0;l<m;l++){for(o=(r=this.components[l]).scaleX*p,n=r.scaleY*d,g=l,f=r.output,i=r.blocksPerLine+1<<3,s=0;s<e;s++)u=0|s*o,w[s]=(u&b)<<3|7&u;for(a=0;a<t;a++)for(c=i*((u=0|a*n)&b)|(7&u)<<3,s=0;s<e;s++)C[g]=f[c+w[s]],g+=m}var x=this.decodeTransform;if(x)for(l=0;l<y;)for(u=0,h=0;u<m;u++,l++,h+=2)C[l]=(C[l]*x[h]>>8)+x[h+1];return C},_isColorConversionNeeded:function(){return this.adobe?!!this.adobe.transformCode:3===this.numComponents?0!==this.colorTransform:1===this.colorTransform},_convertYccToRgb:function(e){for(var t,r,o,n=0,i=e.length;n<i;n+=3)t=e[n],r=e[n+1],o=e[n+2],e[n]=t-179.456+1.402*o,e[n+1]=t+135.459-.344*r-.714*o,e[n+2]=t-226.816+1.772*r;return e},_convertYcckToRgb:function(e){for(var t,r,o,n,i=0,s=0,a=e.length;s<a;s+=4)t=e[s],r=e[s+1],o=e[s+2],n=e[s+3],e[i++]=r*(-660635669420364e-19*r+.000437130475926232*o-54080610064599e-18*t+.00048449797120281*n-.154362151871126)-122.67195406894+o*(-.000957964378445773*o+.000817076911346625*t-.00477271405408747*n+1.53380253221734)+t*(.000961250184130688*t-.00266257332283933*n+.48357088451265)+n*(-.000336197177618394*n+.484791561490776),e[i++]=107.268039397724+r*(219927104525741e-19*r-.000640992018297945*o+.000659397001245577*t+.000426105652938837*n-.176491792462875)+o*(-.000778269941513683*o+.00130872261408275*t+.000770482631801132*n-.151051492775562)+t*(.00126935368114843*t-.00265090189010898*n+.25802910206845)+n*(-.000318913117588328*n-.213742400323665),e[i++]=r*(-.000570115196973677*r-263409051004589e-19*o+.0020741088115012*t-.00288260236853442*n+.814272968359295)-20.810012546947+o*(-153496057440975e-19*o-.000132689043961446*t+.000560833691242812*n-.195152027534049)+t*(.00174418132927582*t-.00255243321439347*n+.116935020465145)+n*(-.000343531996510555*n+.24165260232407);return e},_convertYcckToCmyk:function(e){for(var t,r,o,n=0,i=e.length;n<i;n+=4)t=e[n],r=e[n+1],o=e[n+2],e[n]=434.456-t-1.402*o,e[n+1]=119.541-t+.344*r+.714*o,e[n+2]=481.816-t-1.772*r;return e},_convertCmykToRgb:function(e){for(var t,r,o,n,i=0,s=1/255,a=0,l=e.length;a<l;a+=4)t=e[a]*s,r=e[a+1]*s,o=e[a+2]*s,n=e[a+3]*s,e[i++]=255+t*(-4.387332384609988*t+54.48615194189176*r+18.82290502165302*o+212.25662451639585*n-285.2331026137004)+r*(1.7149763477362134*r-5.6096736904047315*o-17.873870861415444*n-5.497006427196366)+o*(-2.5217340131683033*o-21.248923337353073*n+17.5119270841813)-n*(21.86122147463605*n+189.48180835922747),e[i++]=255+t*(8.841041422036149*t+60.118027045597366*r+6.871425592049007*o+31.159100130055922*n-79.2970844816548)+r*(-15.310361306967817*r+17.575251261109482*o+131.35250912493976*n-190.9453302588951)+o*(4.444339102852739*o+9.8632861493405*n-24.86741582555878)-n*(20.737325471181034*n+187.80453709719578),e[i++]=255+t*(.8842522430003296*t+8.078677503112928*r+30.89978309703729*o-.23883238689178934*n-14.183576799673286)+r*(10.49593273432072*r+63.02378494754052*o+50.606957656360734*n-112.23884253719248)+o*(.03296041114873217*o+115.60384449646641*n-193.58209356861505)-n*(22.33816807309886*n+180.12613974708367);return e},getData:function(t,r,o){if(this.numComponents>4)throw new e("Unsupported color mode");var n=this._getLinearizedBlockData(t,r);if(1===this.numComponents&&o){for(var i=n.length,s=new Uint8ClampedArray(3*i),a=0,l=0;l<i;l++){var u=n[l];s[a++]=u,s[a++]=u,s[a++]=u}return s}if(3===this.numComponents&&this._isColorConversionNeeded())return this._convertYccToRgb(n);if(4===this.numComponents){if(this._isColorConversionNeeded())return o?this._convertYcckToRgb(n):this._convertYcckToCmyk(n);if(o)return this._convertCmykToRgb(n)}return n}},h}()},void 0!==(i=n())&&(o.exports=i)},80899:(e,t,r)=>{r.d(t,{Z:()=>s});var o,n,i,s={},a={get exports(){return s},set exports(e){s=e}};n=a,void 0!==(o=function(){function e(){this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=null}return e.prototype={ensureBuffer:function(e){var t=this.buffer,r=t?t.byteLength:0;if(e<r)return t;for(var o=512;o<e;)o<<=1;for(var n=new Uint8Array(o),i=0;i<r;++i)n[i]=t[i];return this.buffer=n},getByte:function(){for(var e=this.pos;this.bufferLength<=e;){if(this.eof)return null;this.readBlock()}return this.buffer[this.pos++]},getBytes:function(e){var t=this.pos;if(e){this.ensureBuffer(t+e);for(var r=t+e;!this.eof&&this.bufferLength<r;)this.readBlock();var o=this.bufferLength;r>o&&(r=o)}else{for(;!this.eof;)this.readBlock();r=this.bufferLength}return this.pos=r,this.buffer.subarray(t,r)},lookChar:function(){for(var e=this.pos;this.bufferLength<=e;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos])},getChar:function(){for(var e=this.pos;this.bufferLength<=e;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos++])},makeSubStream:function(e,t,r){for(var o=e+t;this.bufferLength<=o&&!this.eof;)this.readBlock();return new Stream(this.buffer,e,t,r)},skip:function(e){e||(e=1),this.pos+=e},reset:function(){this.pos=0}},e}(),i=function(){if(!self||!self.Uint32Array)return null;var e=new Uint32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),t=new Uint32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),r=new Uint32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),n=[new Uint32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],i=[new Uint32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5];function s(e){throw new Error(e)}function a(e){var t=0,r=e[t++],n=e[t++];-1!=r&&-1!=n||s("Invalid header in flate stream"),8!=(15&r)&&s("Unknown compression method in flate stream"),((r<<8)+n)%31!=0&&s("Bad FCHECK in flate stream"),32&n&&s("FDICT bit set in flate stream"),this.bytes=e,this.bytesPos=2,this.codeSize=0,this.codeBuf=0,o.call(this)}return a.prototype=Object.create(o.prototype),a.prototype.getBits=function(e){for(var t,r=this.codeSize,o=this.codeBuf,n=this.bytes,i=this.bytesPos;r<e;)void 0===(t=n[i++])&&s("Bad encoding in flate stream"),o|=t<<r,r+=8;return t=o&(1<<e)-1,this.codeBuf=o>>e,this.codeSize=r-=e,this.bytesPos=i,t},a.prototype.getCode=function(e){for(var t=e[0],r=e[1],o=this.codeSize,n=this.codeBuf,i=this.bytes,a=this.bytesPos;o<r;){var l;void 0===(l=i[a++])&&s("Bad encoding in flate stream"),n|=l<<o,o+=8}var u=t[n&(1<<r)-1],h=u>>16,c=65535&u;return(0==o||o<h||0==h)&&s("Bad encoding in flate stream"),this.codeBuf=n>>h,this.codeSize=o-h,this.bytesPos=a,c},a.prototype.generateHuffmanTable=function(e){for(var t=e.length,r=0,o=0;o<t;++o)e[o]>r&&(r=e[o]);for(var n=1<<r,i=new Uint32Array(n),s=1,a=0,l=2;s<=r;++s,a<<=1,l<<=1)for(var u=0;u<t;++u)if(e[u]==s){var h=0,c=a;for(o=0;o<s;++o)h=h<<1|1&c,c>>=1;for(o=h;o<n;o+=l)i[o]=s<<16|u;++a}return[i,r]},a.prototype.readBlock=function(){function o(e,t,r,o,n){for(var i=e.getBits(r)+o;i-- >0;)t[d++]=n}var a=this.getBits(3);if(1&a&&(this.eof=!0),0!=(a>>=1)){var l,u;if(1==a)l=n,u=i;else if(2==a){for(var h=this.getBits(5)+257,c=this.getBits(5)+1,f=this.getBits(4)+4,p=Array(e.length),d=0;d<f;)p[e[d++]]=this.getBits(3);for(var g=this.generateHuffmanTable(p),m=0,y=(d=0,h+c),C=new Array(y);d<y;){var w=this.getCode(g);16==w?o(this,C,2,3,m):17==w?o(this,C,3,3,m=0):18==w?o(this,C,7,11,m=0):C[d++]=m=w}l=this.generateHuffmanTable(C.slice(0,h)),u=this.generateHuffmanTable(C.slice(h,y))}else s("Unknown block type in flate stream");for(var b=(R=this.buffer)?R.length:0,x=this.bufferLength;;){var k=this.getCode(l);if(k<256)x+1>=b&&(b=(R=this.ensureBuffer(x+1)).length),R[x++]=k;else{if(256==k)return void(this.bufferLength=x);var v=(k=t[k-=257])>>16;v>0&&(v=this.getBits(v)),m=(65535&k)+v,k=this.getCode(u),(v=(k=r[k])>>16)>0&&(v=this.getBits(v));var T=(65535&k)+v;x+m>=b&&(b=(R=this.ensureBuffer(x+m)).length);for(var S=0;S<m;++S,++x)R[x]=R[x-T]}}}else{var A,I=this.bytes,P=this.bytesPos;void 0===(A=I[P++])&&s("Bad block header in flate stream");var M=A;void 0===(A=I[P++])&&s("Bad block header in flate stream"),M|=A<<8,void 0===(A=I[P++])&&s("Bad block header in flate stream");var E=A;void 0===(A=I[P++])&&s("Bad block header in flate stream"),(E|=A<<8)!=(65535&~M)&&s("Bad uncompressed block length in flate stream"),this.codeBuf=0,this.codeSize=0;var D=this.bufferLength,R=this.ensureBuffer(D+M),L=D+M;this.bufferLength=L;for(var _=D;_<L;++_){if(void 0===(A=I[P++])){this.eof=!0;break}R[_]=A}this.bytesPos=P}},a}())&&(n.exports=i)},3892:(e,t,r)=>{function o(e){return"h"in e&&"s"in e&&"v"in e}function n(e){return"l"in e&&"a"in e&&"b"in e}function i(e){return"l"in e&&"c"in e&&"h"in e}r.d(t,{Y3:()=>g,_Y:()=>d,sJ:()=>m,xr:()=>p});const s=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],a=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]];function l(e,t){const r=[];let o,n;if(e[0].length!==t.length)throw new Error("dimensions do not match");const i=e.length,s=e[0].length;let a=0;for(o=0;o<i;o++){for(a=0,n=0;n<s;n++)a+=e[o][n]*t[n];r.push(a)}return r}function u(e){const t=[e.r/255,e.g/255,e.b/255].map((e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4)),r=l(s,t);return{x:100*r[0],y:100*r[1],z:100*r[2]}}function h(e){const t=l(a,[e.x/100,e.y/100,e.z/100]).map((e=>{const t=e<=.0031308?12.92*e:1.055*e**(1/2.4)-.055;return Math.min(1,Math.max(t,0))}));return{r:Math.round(255*t[0]),g:Math.round(255*t[1]),b:Math.round(255*t[2])}}function c(e){const t=[e.x/95.047,e.y/100,e.z/108.883].map((e=>e>(6/29)**3?e**(1/3):1/3*(29/6)**2*e+4/29));return{l:116*t[1]-16,a:500*(t[0]-t[1]),b:200*(t[1]-t[2])}}function f(e){const t=e.l,r=[(t+16)/116+e.a/500,(t+16)/116,(t+16)/116-e.b/200].map((e=>e>6/29?e**3:3*(6/29)**2*(e-4/29)));return{x:95.047*r[0],y:100*r[1],z:108.883*r[2]}}function p(e){return"r"in(t=e)&&"g"in t&&"b"in t?e:i(e)?function(e){return h(f(function(e){const t=e.l,r=e.c,o=e.h;return{l:t,a:r*Math.cos(o),b:r*Math.sin(o)}}(e)))}(e):n(e)?function(e){return h(f(e))}(e):function(e){return"x"in e&&"y"in e&&"z"in e}(e)?h(e):o(e)?function(e){const t=(e.h+360)%360/60,r=e.s/100,o=e.v/100*255,n=o*r,i=n*(1-Math.abs(t%2-1));let s;switch(Math.floor(t)){case 0:s={r:n,g:i,b:0};break;case 1:s={r:i,g:n,b:0};break;case 2:s={r:0,g:n,b:i};break;case 3:s={r:0,g:i,b:n};break;case 4:s={r:i,g:0,b:n};break;case 5:case 6:s={r:n,g:0,b:i};break;default:s={r:0,g:0,b:0}}return s.r=Math.round(s.r+o-n),s.g=Math.round(s.g+o-n),s.b=Math.round(s.b+o-n),s}(e):e;var t}function d(e){return o(e)?e:function(e){const t=e.r,r=e.g,o=e.b,n=Math.max(t,r,o),i=n-Math.min(t,r,o);let s=n,a=0===i?0:n===t?(r-o)/i%6:n===r?(o-t)/i+2:(t-r)/i+4,l=0===i?0:i/s;return a<0&&(a+=6),a*=60,l*=100,s*=100/255,{h:a,s:l,v:s}}(p(e))}function g(e){return n(e)?e:function(e){return c(u(e))}(p(e))}function m(e){return i(e)?e:function(e){return function(e){const t=e.l,r=e.a,o=e.b,n=Math.sqrt(r*r+o*o);let i=Math.atan2(o,r);return i=i>0?i:i+2*Math.PI,{l:t,c:n,h:i}}(c(u(e)))}(p(e))}},5847:(e,t,r)=>{r.d(t,{Z:()=>m});var o=r(43697),n=r(20102),i=r(96674),s=r(22974),a=r(92604),l=r(70586),u=r(5600),h=r(90578),c=r(52011);class f{constructor(e=null,t=null,r=null){this.minValue=e,this.maxValue=t,this.noDataValue=r}}var p,d=r(81578);let g=p=class extends i.wq{static createEmptyBand(e,t){return new(p.getPixelArrayConstructor(e))(t)}static getPixelArrayConstructor(e){let t;switch(e){case"u1":case"u2":case"u4":case"u8":t=Uint8Array;break;case"u16":t=Uint16Array;break;case"u32":t=Uint32Array;break;case"s8":t=Int8Array;break;case"s16":t=Int16Array;break;case"s32":t=Int32Array;break;case"f32":case"c64":case"c128":case"unknown":t=Float32Array;break;case"f64":t=Float64Array}return t}constructor(e){super(e),this.width=null,this.height=null,this.pixelType="f32",this.validPixelCount=null,this.mask=null,this.maskIsAlpha=!1,this.premultiplyAlpha=!1,this.statistics=null,this.depthCount=1}castPixelType(e){if(!e)return"f32";let t=e.toLowerCase();return["u1","u2","u4"].includes(t)?t="u8":["unknown","u8","s8","u16","s16","u32","s32","f32","f64"].includes(t)||(t="f32"),t}getPlaneCount(){return this.pixels?.length}addData(e){if(!e.pixels||e.pixels.length!==this.width*this.height)throw new n.Z("pixelblock:invalid-or-missing-pixels","add data requires valid pixels array that has same length defined by pixel block width * height");this.pixels||(this.pixels=[]),this.statistics||(this.statistics=[]),this.pixels.push(e.pixels),this.statistics.push(e.statistics??new f)}getAsRGBA(){const e=new ArrayBuffer(this.width*this.height*4);switch(this.pixelType){case"s8":case"s16":case"u16":case"s32":case"u32":case"f32":case"f64":this._fillFromNon8Bit(e);break;default:this._fillFrom8Bit(e)}return new Uint8ClampedArray(e)}getAsRGBAFloat(){const e=new Float32Array(this.width*this.height*4);return this._fillFrom32Bit(e),e}updateStatistics(){if(!this.pixels)return;this.statistics=this.pixels.map((e=>this._calculateBandStatistics(e,this.mask)));const e=this.mask;let t=0;if((0,l.pC)(e))for(let r=0;r<e.length;r++)e[r]&&t++;else t=this.width*this.height;this.validPixelCount=t}clamp(e){if(!e||"f64"===e||"f32"===e||!this.pixels)return;const[t,r]=(0,d.r)(e),o=this.pixels,n=this.width*this.height,i=o.length;let s,a,l;const u=[];for(let h=0;h<i;h++){l=p.createEmptyBand(e,n),s=o[h];for(let e=0;e<n;e++)a=s[e],l[e]=a>r?r:a<t?t:a;u.push(l)}this.pixels=u,this.pixelType=e}extractBands(e){const{pixels:t,statistics:r}=this;if((0,l.Wi)(e)||0===e.length||!t||0===t.length)return this;const o=t.length,n=e.some((e=>e>=t.length)),i=o===e.length&&!e.some(((e,t)=>e!==t));return n||i?this:new p({pixelType:this.pixelType,width:this.width,height:this.height,mask:this.mask,validPixelCount:this.validPixelCount,maskIsAlpha:this.maskIsAlpha,pixels:e.map((e=>t[e])),statistics:r&&e.map((e=>r[e]))})}clone(){const e=new p({width:this.width,height:this.height,pixelType:this.pixelType,maskIsAlpha:this.maskIsAlpha,validPixelCount:this.validPixelCount});let t;(0,l.pC)(this.mask)&&(this.mask instanceof Uint8Array?e.mask=new Uint8Array(this.mask):e.mask=this.mask.slice(0));const r=p.getPixelArrayConstructor(this.pixelType);if(this.pixels&&this.pixels.length>0){e.pixels=[];const o=!!this.pixels[0].slice;for(t=0;t<this.pixels.length;t++)e.pixels[t]=o?this.pixels[t].slice(0,this.pixels[t].length):new r(this.pixels[t])}if(this.statistics)for(e.statistics=[],t=0;t<this.statistics.length;t++)e.statistics[t]=(0,s.d9)(this.statistics[t]);return e.premultiplyAlpha=this.premultiplyAlpha,e}_fillFrom8Bit(e){const{mask:t,maskIsAlpha:r,premultiplyAlpha:o,pixels:n}=this;if(!e||!n||!n.length)return void a.Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The input pixel block is empty.");let i,s,u,h;i=s=u=n[0],n.length>=3?(s=n[1],u=n[2]):2===n.length&&(s=n[1]);const c=new Uint32Array(e),f=this.width*this.height;if(i.length===f)if((0,l.pC)(t)&&t.length===f)if(r)for(h=0;h<f;h++){const e=t[h];if(e){const t=e/255;c[h]=o?e<<24|u[h]*t<<16|s[h]*t<<8|i[h]*t:e<<24|u[h]<<16|s[h]<<8|i[h]}}else for(h=0;h<f;h++)t[h]&&(c[h]=255<<24|u[h]<<16|s[h]<<8|i[h]);else for(h=0;h<f;h++)c[h]=255<<24|u[h]<<16|s[h]<<8|i[h];else a.Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The pixelblock is invalid.")}_fillFromNon8Bit(e){const{pixels:t,mask:r,statistics:o}=this;if(!e||!t||!t.length)return void a.Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The input pixel block is empty.");const n=this.pixelType;let i=1,s=0,u=1;if(o&&o.length>0){for(const e of o)if(null!=e.minValue&&(s=Math.min(s,e.minValue)),null!=e.maxValue&&null!=e.minValue){const t=e.maxValue-e.minValue;u=Math.max(u,t)}i=255/u}else{let e=255;"s8"===n?(s=-128,e=127):"u16"===n?e=65535:"s16"===n?(s=-32768,e=32767):"u32"===n?e=4294967295:"s32"===n?(s=-2147483648,e=2147483647):"f32"===n?(s=-34e38,e=34e38):"f64"===n&&(s=-Number.MAX_VALUE,e=Number.MAX_VALUE),i=255/(e-s)}const h=new Uint32Array(e),c=this.width*this.height;let f,p,d,g,m;if(f=p=d=t[0],f.length!==c)return a.Z.getLogger(this.declaredClass).error("getAsRGBA()","Unable to convert to RGBA. The pixelblock is invalid.");if(t.length>=2)if(p=t[1],t.length>=3&&(d=t[2]),(0,l.pC)(r)&&r.length===c)for(g=0;g<c;g++)r[g]&&(h[g]=255<<24|(d[g]-s)*i<<16|(p[g]-s)*i<<8|(f[g]-s)*i);else for(g=0;g<c;g++)h[g]=255<<24|(d[g]-s)*i<<16|(p[g]-s)*i<<8|(f[g]-s)*i;else if((0,l.pC)(r)&&r.length===c)for(g=0;g<c;g++)m=(f[g]-s)*i,r[g]&&(h[g]=255<<24|m<<16|m<<8|m);else for(g=0;g<c;g++)m=(f[g]-s)*i,h[g]=255<<24|m<<16|m<<8|m}_fillFrom32Bit(e){const{pixels:t,mask:r}=this;if(!e||!t||!t.length)return a.Z.getLogger(this.declaredClass).error("getAsRGBAFloat()","Unable to convert to RGBA. The input pixel block is empty.");let o,n,i,s;o=n=i=t[0],t.length>=3?(n=t[1],i=t[2]):2===t.length&&(n=t[1]);const u=this.width*this.height;if(o.length!==u)return a.Z.getLogger(this.declaredClass).error("getAsRGBAFloat()","Unable to convert to RGBA. The pixelblock is invalid.");let h=0;if((0,l.pC)(r)&&r.length===u)for(s=0;s<u;s++)e[h++]=o[s],e[h++]=n[s],e[h++]=i[s],e[h++]=1&r[s];else for(s=0;s<u;s++)e[h++]=o[s],e[h++]=n[s],e[h++]=i[s],e[h++]=1}_calculateBandStatistics(e,t){let r=1/0,o=-1/0;const n=e.length;let i,s=0;if((0,l.pC)(t))for(i=0;i<n;i++)t[i]&&(s=e[i],r=s<r?s:r,o=s>o?s:o);else for(i=0;i<n;i++)s=e[i],r=s<r?s:r,o=s>o?s:o;return new f(r,o)}};(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"width",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"height",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"pixelType",void 0),(0,o._)([(0,h.p)("pixelType")],g.prototype,"castPixelType",null),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"validPixelCount",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"mask",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"maskIsAlpha",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"pixels",void 0),(0,o._)([(0,u.Cb)()],g.prototype,"premultiplyAlpha",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"statistics",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"depthCount",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"noDataValues",void 0),(0,o._)([(0,u.Cb)({json:{write:!0}})],g.prototype,"bandMasks",void 0),g=p=(0,o._)([(0,c.j)("esri.layers.support.PixelBlock")],g);const m=g},48526:(e,t,r)=>{r.d(t,{Z:()=>d});var o,n=r(43697),i=r(96674),s=r(22974),a=r(70586),l=r(5600),u=(r(75215),r(52011)),h=r(6570),c=r(82971),f=r(36679);let p=o=class extends i.wq{constructor(e){super(e),this.attributeTable=null,this.bandCount=null,this.colormap=null,this.extent=null,this.format=void 0,this.height=null,this.width=null,this.histograms=null,this.keyProperties={},this.multidimensionalInfo=null,this.noDataValue=null,this.pixelSize=null,this.pixelType=null,this.isPseudoSpatialReference=!1,this.spatialReference=null,this.statistics=null,this.storageInfo=null,this.transform=null}get dataType(){const e=this.keyProperties?.DataType?.toLowerCase()??"generic";return"stdtime"===e?"standard-time":e}get nativeExtent(){return this._get("nativeExtent")||this.extent}set nativeExtent(e){e&&this._set("nativeExtent",e)}get nativePixelSize(){if((0,a.Wi)(this.transform)||!this.transform.affectsPixelSize)return this.pixelSize;const e=this.nativeExtent;return{x:e.width/this.width,y:e.height/this.height}}get hasMultidimensionalTranspose(){return!!this.storageInfo?.transposeInfo}clone(){return new o({attributeTable:(0,s.d9)(this.attributeTable),bandCount:this.bandCount,colormap:(0,s.d9)(this.colormap),extent:(0,s.d9)(this.extent),nativePixelSize:(0,s.d9)(this.nativePixelSize),format:this.format,height:this.height,width:this.width,histograms:(0,s.d9)(this.histograms),keyProperties:(0,s.d9)(this.keyProperties),multidimensionalInfo:(0,s.d9)(this.multidimensionalInfo),noDataValue:this.noDataValue,pixelSize:(0,s.d9)(this.pixelSize),pixelType:this.pixelType,isPseudoSpatialReference:this.isPseudoSpatialReference,spatialReference:(0,s.d9)(this.spatialReference),statistics:(0,s.d9)(this.statistics),storageInfo:(0,s.d9)(this.storageInfo),transform:(0,s.d9)(this.transform)})}};(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"attributeTable",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"bandCount",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"colormap",void 0),(0,n._)([(0,l.Cb)({type:String,readOnly:!0})],p.prototype,"dataType",null),(0,n._)([(0,l.Cb)({type:h.Z,json:{write:!0}})],p.prototype,"extent",void 0),(0,n._)([(0,l.Cb)({type:h.Z,json:{write:!0}})],p.prototype,"nativeExtent",null),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"nativePixelSize",null),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"format",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"height",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"width",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"hasMultidimensionalTranspose",null),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"histograms",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"keyProperties",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"multidimensionalInfo",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"noDataValue",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"pixelSize",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"pixelType",void 0),(0,n._)([(0,l.Cb)()],p.prototype,"isPseudoSpatialReference",void 0),(0,n._)([(0,l.Cb)({type:c.Z,json:{write:!0}})],p.prototype,"spatialReference",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"statistics",void 0),(0,n._)([(0,l.Cb)({type:f.Z,json:{write:!0}})],p.prototype,"storageInfo",void 0),(0,n._)([(0,l.Cb)({json:{write:!0}})],p.prototype,"transform",void 0),p=o=(0,n._)([(0,u.j)("esri.layers.support.RasterInfo")],p);const d=p},36679:(e,t,r)=>{r.d(t,{Z:()=>c});var o,n=r(43697),i=r(96674),s=r(22974),a=r(5600),l=(r(75215),r(52011)),u=r(94139);let h=o=class extends i.wq{constructor(){super(...arguments),this.blockWidth=void 0,this.blockHeight=void 0,this.compression=null,this.origin=null,this.firstPyramidLevel=null,this.maximumPyramidLevel=null,this.pyramidScalingFactor=2,this.pyramidBlockWidth=null,this.pyramidBlockHeight=null,this.isVirtualTileInfo=!1,this.tileInfo=null,this.transposeInfo=null,this.blockBoundary=null}clone(){return new o({blockWidth:this.blockWidth,blockHeight:this.blockHeight,compression:this.compression,origin:(0,s.d9)(this.origin),firstPyramidLevel:this.firstPyramidLevel,maximumPyramidLevel:this.maximumPyramidLevel,pyramidResolutions:(0,s.d9)(this.pyramidResolutions),pyramidScalingFactor:this.pyramidScalingFactor,pyramidBlockWidth:this.pyramidBlockWidth,pyramidBlockHeight:this.pyramidBlockHeight,isVirtualTileInfo:this.isVirtualTileInfo,tileInfo:(0,s.d9)(this.tileInfo),transposeInfo:(0,s.d9)(this.transposeInfo),blockBoundary:(0,s.d9)(this.blockBoundary)})}};(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"blockWidth",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"blockHeight",void 0),(0,n._)([(0,a.Cb)({type:String,json:{write:!0}})],h.prototype,"compression",void 0),(0,n._)([(0,a.Cb)({type:u.Z,json:{write:!0}})],h.prototype,"origin",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"firstPyramidLevel",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"maximumPyramidLevel",void 0),(0,n._)([(0,a.Cb)({json:{write:!0}})],h.prototype,"pyramidResolutions",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"pyramidScalingFactor",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"pyramidBlockWidth",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],h.prototype,"pyramidBlockHeight",void 0),(0,n._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],h.prototype,"isVirtualTileInfo",void 0),(0,n._)([(0,a.Cb)({json:{write:!0}})],h.prototype,"tileInfo",void 0),(0,n._)([(0,a.Cb)()],h.prototype,"transposeInfo",void 0),(0,n._)([(0,a.Cb)()],h.prototype,"blockBoundary",void 0),h=o=(0,n._)([(0,l.j)("esri.layers.support.RasterStorageInfo")],h);const c=h},20095:(e,t,r)=>{function o(e,t){let r=0,o="",n=0,i=0;const s=e.length;for(;r<s;)i=e[r++],n=i>>4,n<8?n=1:15===n?(n=4,i=(7&i)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++]):14===n?(n=3,i=(15&i)<<12|(63&e[r++])<<6|63&e[r++]):(n=2,i=(31&i)<<6|63&e[r++]),(0!==i||t)&&(o+=String.fromCharCode(i));return o}r.d(t,{f:()=>o})},72955:(e,t,r)=>{r.d(t,{Jx:()=>c,zD:()=>a});var o=r(99880);const n=[{pixelType:"S8",size:1,ctor:Int8Array,range:[-128,127]},{pixelType:"U8",size:1,ctor:Uint8Array,range:[0,255]},{pixelType:"S16",size:2,ctor:Int16Array,range:[-32768,32767]},{pixelType:"U16",size:2,ctor:Uint16Array,range:[0,65536]},{pixelType:"S32",size:4,ctor:Int32Array,range:[-2147483648,2147483647]},{pixelType:"U32",size:4,ctor:Uint32Array,range:[0,4294967296]},{pixelType:"F32",size:4,ctor:Float32Array,range:[-34027999387901484e22,34027999387901484e22]},{pixelType:"F64",size:8,ctor:Float64Array,range:[-17976931348623157e292,17976931348623157e292]}];let i=null,s=null;function a(){return i||(i=r.e(6233).then(r.bind(r,26233)).then((e=>e.l)).then((({default:e})=>e({locateFile:e=>(0,o.V)(`esri/layers/support/rasterFormats/${e}`)}))).then((e=>{(function(e){const{_malloc:t,_free:r,_lerc_getBlobInfo:o,_lerc_getDataRanges:i,_lerc_decode_4D:s,asm:a}=e;let h;const c=Object.values(a).find((t=>t&&"buffer"in t&&t.buffer===e.HEAPU8.buffer)),f=e=>{const r=e.map((e=>function(e){return 16+(e>>3<<3)}(e))),o=r.reduce(((e,t)=>e+t)),n=t(o);h=new Uint8Array(c.buffer);let i=r[0];r[0]=n;for(let e=1;e<r.length;e++){const t=r[e];r[e]=r[e-1]+i,i=t}return r};l.getBlobInfo=e=>{const t=new Uint8Array(48),n=new Uint8Array(24),[s,a,l]=f([e.length,t.length,n.length]);h.set(e,s),h.set(t,a),h.set(n,l);let p=o(s,e.length,a,l,12,3);if(p)throw r(s),new Error(`lerc-getBlobInfo: error code is ${p}`);h=new Uint8Array(c.buffer),u(h,a,t),u(h,l,n);const d=new Uint32Array(t.buffer),g=new Float64Array(n.buffer),[m,y,,C,w,b,x,k,v,T,S]=d,A={version:m,depthCount:T,width:C,height:w,validPixelCount:x,bandCount:b,blobSize:k,maskCount:v,dataType:y,minValue:g[0],maxValue:g[1],maxZerror:g[2],statistics:[],bandCountWithNoData:S};if(S)return A;if(1===T&&1===b)return r(s),A.statistics.push({minValue:g[0],maxValue:g[1]}),A;const I=T*b*8,P=new Uint8Array(I),M=new Uint8Array(I);let E=s,D=0,R=0,L=!1;if(h.byteLength<s+2*I?(r(s),L=!0,[E,D,R]=f([e.length,I,I]),h.set(e,E)):[D,R]=f([I,I]),h.set(P,D),h.set(M,R),p=i(E,e.length,T,b,D,R),p)throw r(E),L||r(D),new Error(`lerc-getDataRanges: error code is ${p}`);h=new Uint8Array(c.buffer),u(h,D,P),u(h,R,M);const _=new Float64Array(P.buffer),B=new Float64Array(M.buffer),U=A.statistics;for(let e=0;e<b;e++)if(T>1){const t=_.slice(e*T,(e+1)*T),r=B.slice(e*T,(e+1)*T),o=Math.min.apply(null,t),n=Math.max.apply(null,r);U.push({minValue:o,maxValue:n,depthStats:{minValues:t,maxValues:r}})}else U.push({minValue:_[e],maxValue:B[e]});return r(E),L||r(D),A},l.decode=(e,t)=>{const{maskCount:o,depthCount:i,bandCount:a,width:l,height:p,dataType:d,bandCountWithNoData:g}=t,m=n[d],y=l*p,C=new Uint8Array(y*a),w=y*i*a*m.size,b=new Uint8Array(w),x=new Uint8Array(a),k=new Uint8Array(8*a),[v,T,S,A,I]=f([e.length,C.length,b.length,x.length,k.length]);h.set(e,v),h.set(C,T),h.set(b,S),h.set(x,A),h.set(k,I);const P=s(v,e.length,o,T,i,l,p,a,d,S,A,I);if(P)throw r(v),new Error(`lerc-decode: error code is ${P}`);h=new Uint8Array(c.buffer),u(h,S,b),u(h,T,C);let M=null;if(g){u(h,A,x),u(h,I,k),M=[];const e=new Float64Array(k.buffer);for(let t=0;t<x.length;t++)M.push(x[t]?e[t]:null)}return r(v),{data:b,maskData:C,noDataValues:M}}})(e),s=!0})),i)}const l={getBlobInfo:null,decode:null};function u(e,t,r){r.set(e.slice(t,t+r.length))}function h(e,t,r,o,n){if(r<2)return e;const i=new o(t*r);if(n)for(let o=0,n=0;o<t;o++)for(let s=0,a=o;s<r;s++,a+=t)i[a]=e[n++];else for(let o=0,n=0;o<t;o++)for(let s=0,a=o;s<r;s++,a+=t)i[n++]=e[a];return i}function c(e,t={}){const r=t.inputOffset??0,o=e instanceof Uint8Array?e.subarray(r):new Uint8Array(e,r),i=l.getBlobInfo(o),{data:s,maskData:a,noDataValues:u}=l.decode(o,i),{width:c,height:f,bandCount:p,depthCount:d,dataType:g,maskCount:m,statistics:y}=i,C=n[g],w=new C.ctor(s.buffer),b=[],x=[],k=c*f,v=k*d;for(let e=0;e<p;e++){const r=w.subarray(e*v,(e+1)*v);if(t.returnInterleaved)b.push(r);else{const e=h(r,k,d,C.ctor,!0);b.push(e)}x.push(a.subarray(e*v,(e+1)*v))}const T=0===m?null:1===m?x[0]:new Uint8Array(k);if(m>1){T.set(x[0]);for(let e=1;e<x.length;e++){const t=x[e];for(let e=0;e<k;e++)T[e]=T[e]&t[e]}}const{noDataValue:S}=t,A=null!=S&&C.range[0]<=S&&C.range[1]>=S;if(m>0&&A)for(let e=0;e<p;e++){const t=b[e],r=x[e]||T;for(let e=0;e<k;e++)0===r[e]&&(t[e]=S)}const I=m===p&&p>1?x:null,{pixelType:P}=C;return{width:c,height:f,bandCount:p,pixelType:P,depthCount:d,statistics:y,pixels:b,mask:T,bandMasks:I,noDataValues:u}}},88781:(e,t,r)=>{function o(){const e=[];for(let t=0;t<=257;t++)e[t]=[t];return e}function n(e,t){for(let r=0;r<t.length;r++)e.push(t[r])}r.d(t,{J:()=>s});const i=new Set;function s(e,t,r,s=!0){const a=function(e,t,r,o=!0){if(t%4!=0||r%4!=0){const n=new ArrayBuffer(4*Math.ceil(r/4)),i=new Uint8Array(n),s=new Uint8Array(e,t,r);if(o)for(let e=0;e<i.length;e+=4)i[e]=s[e+3],i[e+1]=s[e+2],i[e+2]=s[e+1],i[e+3]=s[e];else i.set(s);return new Uint32Array(i.buffer)}if(o){const o=new Uint8Array(e,t,r),n=new Uint8Array(o.length);for(let e=0;e<n.length;e+=4)n[e]=o[e+3],n[e+1]=o[e+2],n[e+2]=o[e+1],n[e+3]=o[e];return new Uint32Array(n.buffer)}return new Uint32Array(e,t,r/4)}(e,t,r,s);let l=9,u=o(),h=32,c=u.length,f=[],p=1,d=a[0],g=0;const m=a.length,y=8*(4*m-r),C=[];for(;null!=d;){if(h>=l)h-=l,g=d>>>32-l,d<<=l;else{g=d>>>32-h,d=a[p++];const e=l-h;h=32-e,g=(g<<e)+(d>>>h),d<<=e}if(257===g)break;if(256===g){l=9,u=o(),c=u.length,f=[];continue}const e=u[g];if(null==e){if(g>u.length)throw new Error("data integrity issue: code does not exist on code page");f.push(f[0]),u[c++]=f.slice(),n(C,f)}else n(C,e),f.push(e[0]),f.length>1&&(u[c++]=f.slice()),f=e.slice();if(i.has(c)&&l++,0===h&&(d=a[p++],h=32),p>m||p===m&&h<=y)break}return new Uint8Array(C)}i.add(511),i.add(1023),i.add(2047),i.add(4095),i.add(8191)},75993:(e,t,r)=>{r.d(t,{J:()=>D,y:()=>E});var o=r(20102),n=r(70586),i=r(5847),s=r(95330),a=r(80899);class l{constructor(e){this._canvas=null,this._ctx=null,e&&(this._canvas=e.canvas,this._ctx=e.ctx||e.canvas&&e.canvas.getContext("2d"))}decode(e,t,r){if(!e||e.byteLength<10)throw new o.Z("imagecanvasdecoder: decode","required a valid encoded data as input.");let{width:n=0,height:i=0,format:a}=t;const{applyJpegMask:u}=t;if(u&&(!n||!i))throw new o.Z("imagecanvasdecoder: decode","image width and height are needed to apply jpeg mask directly to canvas");return new Promise(((o,h)=>{let c=null;"jpg"===a&&u&&(c=l._getMask(e,{width:n,height:i}));const f=new Blob([new Uint8Array(e)],{type:"image/"+a=="jpg"?"jpeg":a}),p=URL.createObjectURL(f),d=new Image;let g;d.src=p,d.onload=()=>{if(URL.revokeObjectURL(p),(0,s.Hc)(r))return void h((0,s.zE)());n=d.width,i=d.height,this._canvas&&this._ctx?(this._canvas.width===n&&this._canvas.height===i||(this._canvas.width=n,this._canvas.height=i),this._ctx.clearRect(0,0,n,i)):(this._canvas=document.createElement("canvas"),this._canvas.width=n,this._canvas.height=i,this._ctx=this._canvas.getContext("2d")),this._ctx.drawImage(d,0,0);const e=this._ctx.getImageData(0,0,n,i);let a;if(g=e.data,t.renderOnCanvas){if(c)for(a=0;a<c.length;a++)c[a]?g[4*a+3]=255:g[4*a+3]=0;return this._ctx.putImageData(e,0,0),void o(null)}const l=n*i,u=new Uint8Array(l),f=new Uint8Array(l),m=new Uint8Array(l);if(c)for(a=0;a<l;a++)u[a]=g[4*a],f[a]=g[4*a+1],m[a]=g[4*a+2];else for(c=new Uint8Array(l),a=0;a<l;a++)u[a]=g[4*a],f[a]=g[4*a+1],m[a]=g[4*a+2],c[a]=g[4*a+3];o({width:n,height:i,pixels:[u,f,m],mask:c,pixelType:"u8"})},d.onerror=()=>{URL.revokeObjectURL(p),h("cannot load image")}}))}static _getMask(e,t){let r=null;try{const o=new Uint8Array(e),n=Math.ceil(o.length/2);let i=0;const s=o.length-2;for(i=n;i<s&&(255!==o[i]||217!==o[i+1]);i++);if(i+=2,i<o.length-1){const e=new a.Z(o.subarray(i)).getBytes();r=new Uint8Array(t.width*t.height);let n=0;for(let t=0;t<e.length;t++)for(let o=7;o>=0;o--)r[n++]=e[t]>>o&1}}catch(e){}return r}}var u=r(97575);class h{static decode(e,t=!1){const r=new Uint8Array(e),o=new u.J;o.parse(r);const{width:n,height:i,numComponents:s,eof:l}=o,h=o.getData(n,i,!0),c=n*i;let f,p=null,d=0,g=0,m=0;if(!t&&l<r.length-1)try{const e=new a.Z(r.subarray(l)).getBytes();p=new Uint8Array(c);let t=0;for(d=0;d<e.length;d++)for(m=7;m>=0;m--)p[t++]=e[d]>>m&1}catch{}if(1===s&&h.length===n*i){const e=new Uint8Array(h.buffer);f=[e,e,e]}else{for(f=[],d=0;d<3;d++)f.push(new Uint8Array(c));for(m=0,g=0;g<c;g++)for(d=0;d<3;d++)f[d][g]=h[m++]}return{width:n,height:i,pixels:f,mask:p}}}var c=r(72955),f=r(88781),p=r(81578);const d=(e,t)=>{const r=t.width*t.height,o=t.pixelType;return Math.floor(e.byteLength/(r*g(o)))},g=e=>{let t=1;switch(e){case Uint8Array:case Int8Array:t=1;break;case Uint16Array:case Int16Array:t=2;break;case Uint32Array:case Int32Array:case Float32Array:t=4;break;case Float64Array:t=8}return t};class m{static decode(e,t){const r=t.pixelType,o=[],n=t.width*t.height,i=d(e,t),{bandIds:s,format:a}=t,l=s&&s.length||d(e,t),u=e.byteLength-e.byteLength%(n*g(r)),h=new r(e,0,n*i);let c,f,p,m,y=null;if("bip"===a)for(c=0;c<l;c++){for(p=new r(n),m=s?s[c]:c,f=0;f<n;f++)p[f]=h[f*i+m];o.push(p)}else if("bsq"===a)for(c=0;c<l;c++)m=s?s[c]:c,o.push(h.subarray(m*n,(m+1)*n));return u<e.byteLength-1&&(y=((e,t)=>{if(8*e.byteLength<t)return null;const r=new Uint8Array(e,0,Math.ceil(t/8)),o=new Uint8Array(t);let n=0,i=0,s=0,a=0;for(s=0;s<r.length-1;s++)for(i=r[s],a=7;a>=0;a--)o[n++]=i>>a&1;for(a=7;n<t-1;)i=r[r.length-1],o[n++]=i>>a&1,a--;return o})(e.slice(u),n)),{pixels:o,mask:y}}}var y=r(94793),C=r(48279),w=function(e){var t,r,o;function n(e){var t,r,o,n,i,s,a,l,u,h,c,f,p;for(this.data=e,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},i=null;;){switch(t=this.readUInt32(),l=function(){var e,t;for(t=[],e=0;e<4;++e)t.push(String.fromCharCode(this.data[this.pos++]));return t}.call(this).join(""),l){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(t);break;case"fcTL":i&&this.animation.frames.push(i),this.pos+=4,i={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},n=this.readUInt16(),o=this.readUInt16()||100,i.delay=1e3*n/o,i.disposeOp=this.data[this.pos++],i.blendOp=this.data[this.pos++],i.data=[];break;case"IDAT":case"fdAT":for("fdAT"===l&&(this.pos+=4,t-=4),e=(null!=i?i.data:void 0)||this.imgData,c=0;0<=t?c<t:c>t;0<=t?++c:--c)e.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(this.transparency.indexed=this.read(t),(u=255-this.transparency.indexed.length)>0)for(f=0;0<=u?f<u:f>u;0<=u?++f:--f)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(t)[0];break;case 2:this.transparency.rgb=this.read(t)}break;case"tEXt":s=(h=this.read(t)).indexOf(0),a=String.fromCharCode.apply(String,h.slice(0,s)),this.text[a]=String.fromCharCode.apply(String,h.slice(s+1));break;case"IEND":return i&&this.animation.frames.push(i),this.colors=function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}.call(this),this.hasAlphaChannel=4===(p=this.colorType)||6===p,r=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*r,this.colorSpace=function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}.call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=t}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}return n.load=function(e,t,r){var o;return"function"==typeof t&&(r=t),(o=new XMLHttpRequest).open("GET",e,!0),o.responseType="arraybuffer",o.onload=function(){var e;return e=new n(new Uint8Array(o.response||o.mozResponseArrayBuffer)),"function"==typeof(null!=t?t.getContext:void 0)&&e.render(t),"function"==typeof r?r(e):void 0},o.send(null)},n.prototype.read=function(e){var t,r;for(r=[],t=0;0<=e?t<e:t>e;0<=e?++t:--t)r.push(this.data[this.pos++]);return r},n.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},n.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},n.prototype.decodePixels=function(e){var t,r,o,n,i,s,l,u,h,c,f,p,d,g,m,y,C,w,b,x,k,v,T;if(null==e&&(e=this.imgData),0===e.length)return new Uint8Array(0);for(e=(e=new a.Z(e)).getBytes(),y=(p=this.pixelBitlength/8)*this.width,d=new Uint8Array(y*this.height),s=e.length,m=0,g=0,r=0;g<s;){switch(e[g++]){case 0:for(n=b=0;b<y;n=b+=1)d[r++]=e[g++];break;case 1:for(n=x=0;x<y;n=x+=1)t=e[g++],i=n<p?0:d[r-p],d[r++]=(t+i)%256;break;case 2:for(n=k=0;k<y;n=k+=1)t=e[g++],o=(n-n%p)/p,C=m&&d[(m-1)*y+o*p+n%p],d[r++]=(C+t)%256;break;case 3:for(n=v=0;v<y;n=v+=1)t=e[g++],o=(n-n%p)/p,i=n<p?0:d[r-p],C=m&&d[(m-1)*y+o*p+n%p],d[r++]=(t+Math.floor((i+C)/2))%256;break;case 4:for(n=T=0;T<y;n=T+=1)t=e[g++],o=(n-n%p)/p,i=n<p?0:d[r-p],0===m?C=w=0:(C=d[(m-1)*y+o*p+n%p],w=o&&d[(m-1)*y+(o-1)*p+n%p]),l=i+C-w,u=Math.abs(l-i),c=Math.abs(l-C),f=Math.abs(l-w),h=u<=c&&u<=f?i:c<=f?C:w,d[r++]=(t+h)%256;break;default:throw new Error("Invalid filter algorithm: "+e[g-1])}m++}return d},n.prototype.decodePalette=function(){var e,t,r,o,n,i,s,a,l;for(r=this.palette,i=this.transparency.indexed||[],n=new Uint8Array((i.length||0)+r.length),o=0,r.length,e=0,t=s=0,a=r.length;s<a;t=s+=3)n[o++]=r[t],n[o++]=r[t+1],n[o++]=r[t+2],n[o++]=null!=(l=i[e++])?l:255;return n},n.prototype.copyToImageData=function(e,t){var r,o,n,i,s,a,l,u,h,c,f;if(o=this.colors,h=null,r=this.hasAlphaChannel,this.palette.length&&(h=null!=(f=this._decodedPalette)?f:this._decodedPalette=this.decodePalette(),o=4,r=!0),u=(n=e.data||e).length,s=h||t,i=a=0,1===o)for(;i<u;)l=h?4*t[i/4]:a,c=s[l++],n[i++]=c,n[i++]=c,n[i++]=c,n[i++]=r?s[l++]:this.transparency.grayscale&&this.transparency.grayscale===c?0:255,a=l;else for(;i<u;)l=h?4*t[i/4]:a,n[i++]=s[l++],n[i++]=s[l++],n[i++]=s[l++],n[i++]=r?s[l++]:this.transparency.rgb&&this.transparency.rgb[1]===s[l-3]&&this.transparency.rgb[3]===s[l-2]&&this.transparency.rgb[5]===s[l-1]?0:255,a=l},n.prototype.decode=function(){var e;return e=new Uint8Array(this.width*this.height*4),this.copyToImageData(e,this.decodePixels()),e},r=e.document&&e.document.createElement("canvas"),o=r&&r.getContext("2d"),t=function(e){var t;return o.width=e.width,o.height=e.height,o.clearRect(0,0,e.width,e.height),o.putImageData(e,0,0),(t=new Image).src=r.toDataURL(),t},n.prototype.decodeFrames=function(e){var r,o,n,i,s,a,l,u;if(this.animation){for(u=[],o=s=0,a=(l=this.animation.frames).length;s<a;o=++s)r=l[o],n=e.createImageData(r.width,r.height),i=this.decodePixels(new Uint8Array(r.data)),this.copyToImageData(n,i),r.imageData=n,u.push(r.image=t(n));return u}},n.prototype.renderFrame=function(e,t){var r,o,n;return r=(o=this.animation.frames)[t],n=o[t-1],0===t&&e.clearRect(0,0,this.width,this.height),1===(null!=n?n.disposeOp:void 0)?e.clearRect(n.xOffset,n.yOffset,n.width,n.height):2===(null!=n?n.disposeOp:void 0)&&e.putImageData(n.imageData,n.xOffset,n.yOffset),0===r.blendOp&&e.clearRect(r.xOffset,r.yOffset,r.width,r.height),e.drawImage(r.image,r.xOffset,r.yOffset)},n.prototype.animate=function(e){var t,r,o,n,i,s,a=this;return r=0,s=this.animation,n=s.numFrames,o=s.frames,i=s.numPlays,(t=function(){var s,l;if(s=r++%n,l=o[s],a.renderFrame(e,s),n>1&&r/n<i)return a.animation._timeout=setTimeout(t,l.delay)})()},n.prototype.stopAnimation=function(){var e;return clearTimeout(null!=(e=this.animation)?e._timeout:void 0)},n.prototype.render=function(e){var t,r;return e._png&&e._png.stopAnimation(),e._png=this,e.width=this.width,e.height=this.height,t=e.getContext("2d"),this.animation?(this.decodeFrames(t),this.animate(t)):(r=t.createImageData(this.width,this.height),this.copyToImageData(r,this.decodePixels()),t.putImageData(r,0,0))},n}(self);const b=new Set(["jpg","png","bmp","gif"]);async function x(e,t){if(!C.f)throw new o.Z("rasterCoded:decode","lerc decoder is not supported on big endian platform");await(0,c.zD)();const{offset:r}=t,{width:n,height:s,pixelType:a,statistics:l,depthCount:u,noDataValues:h,bandMasks:f,pixels:p,mask:d}=(0,c.Jx)(e,{inputOffset:r,returnInterleaved:t.returnInterleaved});return new i.Z({width:n,height:s,pixelType:a.toLowerCase(),pixels:p,mask:d,statistics:l,bandMasks:f,depthCount:u,noDataValues:h})}async function k(e,t){const r=await(0,y.Jx)(e,{...t,noDataValue:null});(0,n.O3)(r);const o=new i.Z({width:r.width,height:r.height,pixels:r.pixels,pixelType:r.pixelType.toLowerCase(),mask:r.mask,statistics:null});return o.updateStatistics(),o}function v(e,t){const r=t.pixelType||"u8",o=i.Z.getPixelArrayConstructor(r),n="u8"===r?e:new o(e.buffer),s=[],a=t.planes||1;if(1===a)s.push(n);else for(let r=0;r<a;r++){const i=(t.width||1)*(t.height||e.length),l=new o(i);for(let e=0;e<i;e++)l[e]=n[e*a+r];s.push(l)}const l=new i.Z({width:t.width||1,height:t.height||e.length,pixels:s,pixelType:r,statistics:null});return l.updateStatistics(),l}function T(e,t){return v(new a.Z(new Uint8Array(e)).getBytes(),t)}function S(e,t){return v((0,f.J)(e,t.offset,t.eof,!t.isInputBigEndian),t)}function A(e,t){const r=h.decode(e,t.hasNoZlibMask??void 0),o=new i.Z({width:r.width,height:r.height,pixels:r.pixels,pixelType:"U8",mask:r.mask,statistics:null});return o.updateStatistics(),o}function I(e,t){const r=new Uint8Array(e),o=new w(r),{width:n,height:s}=t,a=n*s,l=o.decode();let u,h=0,c=0;const f=new Uint8Array(a);for(h=0;h<a;h++)f[h]=l[4*h+3];const p=new i.Z({width:n,height:s,pixels:[],pixelType:"U8",mask:f,statistics:[]});for(h=0;h<3;h++){for(u=new Uint8Array(a),c=0;c<a;c++)u[c]=l[4*c+h];p.addData({pixels:u})}return p.updateStatistics(),p}async function P(e,t,r,o){const n=new l,s={applyJpegMask:!1,format:t,...r},a=await n.decode(e,s,o),u=new i.Z(a);return u.updateStatistics(),u}function M(e){if(null==e)throw new o.Z("rasterCodec:decode","parameter encodeddata is required.");const t=new Uint8Array(e,0,10);let r="";return 255===t[0]&&216===t[1]?r="jpg":137===t[0]&&80===t[1]&&78===t[2]&&71===t[3]?r="png":67===t[0]&&110===t[1]&&116===t[2]&&90===t[3]&&73===t[4]&&109===t[5]&&97===t[6]&&103===t[7]&&101===t[8]&&32===t[9]?r="lerc":76===t[0]&&101===t[1]&&114===t[2]&&99===t[3]&&50===t[4]&&32===t[5]?r="lerc2":73===t[0]&&73===t[1]&&42===t[2]&&0===t[3]||77===t[0]&&77===t[1]&&0===t[2]&&42===t[3]||73===t[0]&&73===t[1]&&43===t[2]&&0===t[3]||77===t[0]&&77===t[1]&&0===t[2]&&43===t[3]?r="tiff":71===t[0]&&73===t[1]&&70===t[2]?r="gif":66===t[0]&&77===t[1]?r="bmp":String.fromCharCode.apply(null,t).toLowerCase().includes("error")&&(r="error"),r}function E(e){let t=M(e);return"lerc2"===t?t="lerc":"error"===t&&(t=""),t}async function D(e,t={},r){if(null==e)throw new o.Z("rasterCodec:decode","missing encodeddata parameter.");let s=t.format&&t.format.toLowerCase();if(!("bsq"!==s&&"bip"!==s||null!=t.width&&null!=t.height))throw new o.Z("rasterCodec:decode","requires width and height in options parameter.");if("tiff"===s&&t.customOptions)return async function(e,t){const r=await(0,y.Uw)(e,t.customOptions),o=new i.Z({width:r.width,height:r.height,pixels:r.pixels,pixelType:r.pixelType.toLowerCase(),mask:r.mask,statistics:null});return o.updateStatistics(),o}(e,t);if((!s||"bsq"!==s&&"bip"!==s&&"deflate"!==s&&"lzw"!==s)&&(s=M(e)),t.useCanvas&&b.has(s))return P(e,s,t,r);const a=function(e){let t=null;switch(e){case"lerc":case"lerc2":t=x;break;case"jpg":t=A;break;case"png":t=I;break;case"bsq":case"bip":t=(t,r)=>function(e,t,r){const{pixelTypeCtor:o}=function(e){let t=null,r=null;switch(e?e.toLowerCase():"f32"){case"u1":case"u2":case"u4":case"u8":r=255,t=Uint8Array;break;case"u16":r=r||65535,t=Uint16Array;break;case"u32":r=r||2**32-1,t=Uint32Array;break;case"s8":r=r||-128,t=Int8Array;break;case"s16":r=r||-32768,t=Int16Array;break;case"s32":r=r||0-2**31,t=Int32Array;break;default:t=Float32Array}return{pixelTypeCtor:t,noDataValue:r}}(t.pixelType),n=(0,m.decode)(e,{width:t.width,height:t.height,pixelType:o,format:r}),s=new i.Z({width:t.width,height:t.height,pixels:n.pixels,pixelType:t.pixelType,mask:n.mask,statistics:null});return s.updateStatistics(),s}(t,r,e);break;case"tiff":t=k;break;case"deflate":t=T;break;case"lzw":t=S;break;case"error":t=()=>{throw new o.Z("rasterCodec:decode","input data contains error")};break;default:t=()=>{throw new o.Z("rasterCodec:decode","unsupported raster format")}}return t}(s);t.isPoint&&(null!=(t={...t}).width&&t.width++,null!=t.height&&t.height++);const l=await a(e,t);return l?("jpg"!==s&&null!=t.noDataValue&&1===l.depthCount&&(0,p.A)(l,t.noDataValue,{customFloatTolerance:t.tolerance}),t.isPoint&&function(e,t=1){if(!e)return;const{pixels:r,width:o,height:s,mask:a}=e;if(!r||0===r.length)return;const l=r.length,u=o-1,h=s-1,c=[];let f,p,d,g,m,y,C=null;const w=i.Z.getPixelArrayConstructor(e.pixelType);if(0===t){for(f=0;f<l;f++){for(m=r[f],y=new w(u*h),p=0;p<h;p++)for(g=p*o,d=0;d<u;d++)y[p*u+d]=m[g+d];c.push(y)}if((0,n.pC)(a))for(C=new Uint8Array(u*h),p=0;p<h;p++)for(g=p*o,d=0;d<u;d++)C[p*u+d]=a[g+d]}else{for(f=0;f<l;f++){for(m=r[f],y=new w(u*h),p=0;p<h;p++)for(g=p*o,d=0;d<u;d++)y[p*u+d]=(m[g+d]+m[g+d+1]+m[g+o+d]+m[g+o+d+1])/4;c.push(y)}if(a)for(C=new Uint8Array(u*h),p=0;p<h;p++)for(g=p*o,d=0;d<u;d++)C[p*u+d]=Math.min.apply(null,[a[g+d],a[g+d+1],a[g+o+d],a[g+o+d+1]])}e.width=u,e.height=h,e.mask=C,e.pixels=c}(l),l):l}},94793:(e,t,r)=>{r.d(t,{Dq:()=>A,FI:()=>I,I7:()=>E,If:()=>P,Jx:()=>O,Uw:()=>G,cK:()=>_,ee:()=>M,vr:()=>B});var o=r(20095),n=r(97575),i=r(72955),s=r(88781),a=r(81578),l=r(9832),u=r(48279),h=r(80899);const c=[0,1,1,2,4,8,1,1,2,4,8,4,8,-1,-1,-1,8,8,8],f=4294967296,p=new Set([1,5,6,7,8,34712,34887]);function d(e,t){let r="unknown";return 3===e?r=64===t?"f64":"f32":1===e?1===t?r="u1":2===t?r="u2":4===t?r="u4":t<=8?r="u8":t<=16?r="u16":t<=32&&(r="u32"):2===e&&(t<=8?r="s8":t<=16?r="s16":t<=32&&(r="s32")),r}function g(e){let t=null;switch(e?e.toLowerCase():"f32"){case"u1":case"u2":case"u4":case"u8":t=Uint8Array;break;case"u16":t=Uint16Array;break;case"u32":t=Uint32Array;break;case"s8":t=Int8Array;break;case"s16":t=Int16Array;break;case"s32":t=Int32Array;break;case"f64":t=Float64Array;break;default:t=Float32Array}return t}function m(e,t){return{x:t[0]*e.x+t[1]*e.y+t[2],y:t[3]*e.x+t[4]*e.y+t[5]}}function y(e,t){return e.get(t)?.values}function C(e,t){return e.get(t)?.values}function w(e,t){return e.get(t)?.values?.[0]}function b(e,t){return e.get(t)?.values?.[0]}function x(e,t,r,o=0,n=l.Z.TIFF_TAGS,i=4){const s=8===i,a=s?D(new DataView(e,r,8),0,t):new DataView(e,r,2).getUint16(0,t),u=4+2*i,h=s?8:2,c=h+a*u;if(r+c>e.byteLength)return{success:!1,ifd:null,nextIFD:null,requiredBufferSize:c};const f=r+c+4<=e.byteLength?L(new DataView(e,r+c,8===i?8:4),0,t,8===i):null,p=r+h,d=new Map;let g,m,y,C,w,b=0,x=0;for(let r=0;r<a;r++){m=new DataView(e,p+u*r,u),y=m.getUint16(0,t),w=m.getUint16(2,t),C=l.Z.getTagName(y,n);const s=[];2===i?(b=m.getUint16(4,t),x=m.getUint16(6,t)):4===i?(b=m.getUint32(4,t),x=m.getUint32(8,t)):8===i&&(b=L(m,4,t,!0),x=L(m,12,t,!0),s.push(m.getUint32(12,t)),s.push(m.getUint32(16,t))),g={id:y,type:w,valueCount:b,valueOffset:x,valueOffsets:s,values:null},A(e,t,g,o,!1,i),d.set(C,g)}return{success:!0,ifd:d,nextIFD:f,requiredBufferSize:c}}function k(e,t){if(1!==t&&2!==t&&4!==t)return e;const r=new Uint8Array(e),o=8/t,n=new Uint8Array(e.byteLength*o);let i=0;const s=2**t-1;for(let e=0;e<r.length;e++){const a=r[e];for(let e=0;e<o;e++)n[i++]=a<<t*e>>>8-t&s}return n.buffer}function v(e,t,r){const o=new n.J;o.parse(e),o.colorTransform=6===r?-1:0;const i=o.getData(o.width,o.height,1!==t);return new Uint8Array(i.buffer)}function T(e){const t=new h.Z(e).getBytes(),r=new ArrayBuffer(t.length),o=new Uint8Array(r);return o.set(t),o}async function S(e,t,r,o,n){const a=u.f===t,l=b(r,"BITSPERSAMPLE"),h=b(r,"SAMPLESPERPIXEL"),c=b(r,"PHOTOMETRICINTERPRETATION"),f=d(b(r,"SAMPLEFORMAT")??1,l),p=b(r,"COMPRESSION")??1,m=g(f);let y,C,w;if(34887===p)return await(0,i.zD)(),((e,t)=>(0,i.Jx)(e,{inputOffset:t}).pixels[0])(e,o);if(1===p)y=e.slice(o,o+n),C=new Uint8Array(y);else if(8===p||32946===p)C=new Uint8Array(e,o,n),C=T(C),y=C.buffer;else if(6===p)C=new Uint8Array(e,o,n),C=v(C,h,c),y=C.buffer;else if(7===p){const t=r.get("JPEGTABLES").values,i=t.length-2;C=new Uint8Array(i+n-2);for(let e=0;e<i;e++)C[e]=t[e];const s=new Uint8Array(e,o+2,n-2);for(let e=0;e<s.length;e++)C[i+e]=s[e];C=v(C,h,c),y=C.buffer}else{if(5!==p)throw new Error("tiff-decode: unsupport compression "+p);C=(0,s.J)(e,o,n,t),y=C.buffer}if(y=k(y,l),"u8"===f||"s8"===f||a)w=new m(y);else{y=new ArrayBuffer(C.length);const e=new Uint8Array(y);switch(f){case"u16":case"s16":for(let t=0;t<C.length;t+=2)e[t]=C[t+1],e[t+1]=C[t];break;case"u32":case"s32":case"f32":for(let t=0;t<C.length;t+=4)e[t]=C[t+3],e[t+1]=C[t+2],e[t+2]=C[t+1],e[t+3]=C[t]}w=new m(y)}return w}function A(e,t,r,o=0,n=!1,i=4){if(r.values)return!0;const s=r.type,a=r.valueCount;let l=r.valueOffset,u=[];const h=c[s],p=8*h,d=a*h,g=a*c[s]*8;let m,y;const C=8===i?64:32,w=r.valueOffsets;if(g>C&&d>(n?e.byteLength:e?e.byteLength-l+o:0))return r.offlineOffsetSize=[l,d],r.values=null,!1;if(g<=C){if(!t)if(C<=32)l>>>=32-g;else{const e=w?.length?w[0]:l>>>0,t=w?.length?w[1]:Math.round((l-e)/f);g<=32?(l=e>>>32-g,w[0]=l):(l=e*2**(32-g)+(t>>>32-g),w[0]=e,w[1]=t>>>32-g)}if(1===a&&p===C)u=[l];else if(64===C){const e=w?.length?w[0]:l>>>0,t=w?.length?w[1]:Math.round((l-e)/f);let r=e,o=32;for(y=1;y<=a;y++){const e=32-p*y%32;if(o<p){const n=r<<e>>>32-o,i=t<<32-o>>>32-o;r=t,u.push(n+i*2**(p-o)),o-=32-(p-o)}else u.push(r<<e>>>32-p),o-=p;0===o&&(o=32,r=t)}}else for(y=1;y<=a;y++){const e=32-p*y;u.push(l<<e>>>32-p)}}else{l-=o,n&&(l=0);for(let r=l;r<l+d;r+=h){switch(s){case 1:case 2:case 7:m=new DataView(e,r,1).getUint8(0);break;case 3:m=new DataView(e,r,2).getUint16(0,t);break;case 4:case 13:m=new DataView(e,r,4).getUint32(0,t);break;case 5:m=new DataView(e,r,4).getUint32(0,t)/new DataView(e,r+4,4).getUint32(0,t);break;case 6:m=new DataView(e,r,1).getInt8(0);break;case 8:m=new DataView(e,r,2).getInt16(0,t);break;case 9:m=new DataView(e,r,4).getInt32(0,t);break;case 10:m=new DataView(e,r,4).getInt32(0,t)/new DataView(e,r+4,4).getInt32(0,t);break;case 11:m=new DataView(e,r,4).getFloat32(0,t);break;case 12:m=new DataView(e,r,8).getFloat64(0,t);break;case 16:case 18:m=D(new DataView(e,r,8),0,t);break;case 17:m=R(new DataView(e,r,8),0,t);break;default:m=null}u.push(m)}}if(2===s){let e="";const t=u;for(u=[],y=0;y<t.length;y++)0===t[y]&&""!==e?(u.push(e),e=""):e+=String.fromCharCode(t[y]);""===e&&0!==u.length||u.push(e)}return r.values=u,!0}function I(e){const t=e[0],r=b(t,"TILEWIDTH"),o=b(t,"TILELENGTH"),n=b(t,"IMAGEWIDTH"),i=b(t,"IMAGELENGTH"),s=b(t,"BITSPERSAMPLE"),a=b(t,"SAMPLESPERPIXEL"),l=b(t,"SAMPLEFORMAT")??1,u=d(l,s),h=P(t),c=y(t,"GDAL_NODATA");let f=null;c?.length&&(f=c.map((e=>parseFloat(e))),f.some((e=>isNaN(e)))&&(f=null));const g=b(t,"COMPRESSION")??1;let x;switch(g){case 1:x="NONE";break;case 2:case 3:case 4:case 32771:x="CCITT";break;case 5:x="LZW";break;case 6:case 7:x="JPEG";break;case 32773:x="PACKBITS";break;case 8:case 32946:x="DEFLATE";break;case 34712:x="JPEG2000";break;case 34887:x="LERC";break;default:x=String(g)}let k=!0,v="";p.has(g)||(k=!1,v+="unsupported tag compression "+g),l>3&&(k=!1,v+="unsupported tag sampleFormat "+l),1!==s&&2!==s&&4!==s&&s%8!=0&&(k=!1,v+="unsupported tag bitsPerSample "+s);const T=w(t,"GEOASCIIPARAMS");let S;if(T){const e=T.split("|").find((e=>e.includes("ESRI PE String = "))),t=e?e.replace("ESRI PE String = ",""):"";S=t.startsWith("COMPD_CS")||t.startsWith("PROJCS")||t.startsWith("GEOGCS")?{wkid:null,wkt:t}:null}const A=C(t,"GEOTIEPOINTS"),I=C(t,"GEOPIXELSCALE"),D=C(t,"GEOTRANSMATRIX"),R=t.has("GEOKEYDIRECTORY")?t.get("GEOKEYDIRECTORY").data:null;let L,_,B=!1,G=!1;if(R){B=2===b(R,"GTRasterTypeGeoKey");const e=b(R,"GTModelTypeGeoKey");if(2===e){const e=b(R,"GeographicTypeGeoKey");e>=1024&&e<=32766&&(S={wkid:e}),S||32767!==e||(G=!0,S={wkid:4326})}else if(1===e){const e=b(R,"ProjectedCSTypeGeoKey");e>=1024&&e<=32766&&(S={wkid:e})}}if(I&&A&&A.length>=6?(L=[I[0],0,A[3]-A[0]*I[0],0,-Math.abs(I[1]),A[4]-A[1]*I[1]],B&&(L[2]-=.5*L[0]+.5*L[1],L[5]-=.5*L[3]+.5*L[4])):D&&16===D.length&&(L=B?[D[0],D[1],D[3]-.5*D[0],D[4],D[5],D[7]-.5*D[5]]:[D[0],D[1],D[3],D[4],D[5],D[7]]),L){const e=[{x:0,y:i},{x:0,y:0},{x:n,y:i},{x:n,y:0}];let t,r=Number.POSITIVE_INFINITY,o=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY,a=Number.NEGATIVE_INFINITY;for(let n=0;n<e.length;n++)t=m(e[n],L),r=t.x>r?r:t.x,s=t.x<s?s:t.x,o=t.y>o?o:t.y,a=t.y<a?a:t.y;_={xmin:r,xmax:s,ymin:o,ymax:a,spatialReference:S}}else _={xmin:-.5,ymin:.5-i,xmax:n-.5,ymax:.5,spatialReference:S};G&&(_.xmax-_.xmin>400||Math.max(Math.abs(_.xmin),Math.abs(_.xmax))>361)&&(S=null,_.spatialReference=null);const O=M(e);let N,F,V,z,j;if(O.length>0){V=Math.round(Math.log(n/b(O[0],"IMAGEWIDTH"))/Math.LN2);const e=O[O.length-1];z=Math.round(Math.log(n/b(e,"IMAGEWIDTH"))/Math.LN2),N=b(e,"TILEWIDTH"),F=b(e,"TILELENGTH")}N=null!=z&&z>0?N||r:null,F=null!=z&&z>0?F||o:null,r&&(j=[{maxCol:Math.ceil(n/r)-1,maxRow:Math.ceil(i/o)-1,minRow:0,minCol:0}],O.forEach((e=>{j.push({maxCol:Math.ceil(b(e,"IMAGEWIDTH")/b(e,"TILEWIDTH"))-1,maxRow:Math.ceil(b(e,"IMAGELENGTH")/b(e,"TILELENGTH"))-1,minRow:0,minCol:0})})));const W=(e=>{if(!e)return null;const t=e.match(/<Item(.*?)Item>/gi);if(!t||0===t.length)return null;const r=new Map;let o,n,i,s,a;for(let e=0;e<t.length;e++)o=t[e],n=o.slice("<Item ".length,o.indexOf(">")),s=o.indexOf("sample="),s>-1&&(a=o.slice(s+'sample="'.length,o.indexOf('"',s+'sample="'.length))),s=o.indexOf("name="),s>-1&&(n=o.slice(s+'name="'.length,o.indexOf('"',s+'name="'.length))),n&&(i=o.slice(o.indexOf(">")+1,o.indexOf("</Item>")).trim(),null!=a?r.has(n)?r.get(n)[a]=i:r.set(n,[i]):r.set(n,i)),a=null;const l=r.get("STATISTICS_MINIMUM"),u=r.get("STATISTICS_MAXIMUM"),h=r.get("STATISTICS_MEAN"),c=r.get("STATISTICS_STDDEV");let f=null;if(l&&u){f=[];for(let e=0;e<l.length;e++)f.push({min:parseFloat(l[e]),max:parseFloat(u[e]),avg:h&&parseFloat(h[e]),stddev:c&&parseFloat(c[e])})}const p=r.get("BandName"),d=r.get("WavelengthMin"),g=r.get("WavelengthMax");let m=null;if(p){m=[];for(let e=0;e<p.length;e++)m.push({BandName:p[e],WavelengthMin:d&&parseFloat(d[e]),WavelengthMax:g&&parseFloat(g[e])})}return{statistics:f,bandProperties:m,dataType:r.get("DataType"),rawMetadata:r}})(w(e[0],"GDAL_METADATA"));return v+=" "+U({width:n,height:i,tileWidth:r,tileHeight:o,planes:a,ifds:e}),{width:n,height:i,tileWidth:r,tileHeight:o,planes:a,isBSQ:h,pixelType:u,compression:x,noData:f,hasMaskBand:E(e).length===O.length+1,isSupported:k,message:v,extent:_,isPseudoGeographic:G,affine:I?null:L,firstPyramidLevel:V,maximumPyramidLevel:z,pyramidBlockWidth:N,pyramidBlockHeight:F,tileBoundary:j,metadata:W}}function P(e,t){const r=y(e,"PLANARCONFIGURATION");return r?2===r[0]:!!t&&t.isBSQ}function M(e){return e.filter((e=>1===b(e,"NEWSUBFILETYPE")))}function E(e){return e.filter((e=>{const t=4==(4&(b(e,"NEWSUBFILETYPE")??0)),r=4===b(e,"PHOTOMETRICINTERPRETATION");return t&&r}))}function D(e,t,r){const o=e.getUint32(t,r),n=e.getUint32(t+4,r);return r?n*f+o:o*f+n}function R(e,t,r){let o=r?e.getInt32(t,r):e.getUint32(t,r),n=r?e.getUint32(t+4,r):e.getInt32(t+4,r);const i=(r?o:n)>=0?1:-1;return r?o*=i:n*=i,i*(r?n*f+o:o*f+n)}function L(e,t,r,o){return o?D(e,t,r):e.getUint32(t,r)}function _(e){const t=new DataView(e,0,16),r=t.getUint16(0,!1);let o=null;if(18761===r)o=!0;else{if(19789!==r)throw new Error("unexpected endianess byte");o=!1}const n=t.getUint16(2,o);if(42!==n&&43!==n)throw new Error("unexpected tiff identifier");let i=4;const s=43===n;if(s){const e=t.getUint16(i,o);if(i+=2,8!==e)throw new Error("unsupported bigtiff version");if(0!==t.getUint16(i,o))throw new Error("unsupported bigtiff version");i+=2}return{littleEndian:o,isBigTiff:s,firstIFDPos:L(t,i,o,s)}}function B(e,t,r,n=0,i=l.Z.TIFF_TAGS,s=4){const a=x(e,t,r,n,i,s);let u;const h=a.ifd;if(h){if(l.Z.ifdTags.forEach(((r,o)=>{h.has(o)&&(u=h.get(o),u.data=x(e,t,u.valueOffset-n,n,r).ifd)})),h.has("GEOKEYDIRECTORY")){u=h.get("GEOKEYDIRECTORY");const r=u.values;if(r&&r.length>4){const o=r[0]+"."+r[1]+"."+r[2];u.data=x(e,t,u.valueOffset+6-n,n,l.Z.GEO_KEYS,2).ifd,u.data&&u.data.set("GEOTIFFVersion",{id:0,type:2,valueCount:1,valueOffset:null,values:[o]})}}if(h.has("XMP")){u=h.get("XMP");const e=u.values;"number"==typeof e[0]&&7===u.type&&(u.values=[(0,o.f)(new Uint8Array(e))])}}return a}function U(e){const{width:t,height:r,tileHeight:o,tileWidth:n}=e,i=e.planes,s=n?n*o:t*r,a=b(e.ifds[0],"BITSPERSAMPLE");let l="";return s*i>2**30/(a>8?a/8:1)&&(l=n?"tiled tiff exceeding 1 gigabits per tile is not supported":"scanline tiff exceeding 1 gigabits is not supported"),l}async function G(e,t){const{headerInfo:r,ifd:o,offsets:n,sizes:i}=t,s=[];for(let t=0;t<n.length;t++){const a=await S(e,r.littleEndian,o,n[t],i[t]||e.byteLength);s.push(a)}const l=P(o,r),u=b(o,"BITSPERSAMPLE"),h=d(b(o,"SAMPLEFORMAT")??1,u),c=b(o,"SAMPLESPERPIXEL")||r.planes,f=g(h),p=b(o,"TILEWIDTH"),m=b(o,"TILELENGTH"),y=b(o,"COMPRESSION")??1,C=p*m;let w;const x=[];let k=s[0];const v=34887===y;for(let e=0;e<c;e++){if(w=new f(C),l&&v){if(k=s[e],k.length)for(let t=0;t<C;t++)w[t]=k[e][t+e]}else if(k.length)if(l||v&&!l)w=k.slice(C*e,C*(e+1));else for(let t=0;t<C;t++)w[t]=k[t*c+e];x.push(w)}const T=r.noData?r.noData[0]:t.noDataValue,A=r.metadata?r.metadata.statistics:null,I=A?A.map((e=>e.min)):null,M=A?A.map((e=>e.max)):null,E={pixelType:h,width:p,height:m,pixels:x,noDataValue:T};return null!=T?(0,a.A)(E,T):I&&M&&t.applyMinMaxConstraint&&(E.mask=((e,t,r)=>{if(!(e&&e.length>0&&t&&r))return null;let o,n,i;const s=e[0].length,a=e.length,l=new Uint8Array(s);for(let u=0;u<a;u++)if(o=e[u],n=t[u],i=r[u],0===u)for(let e=0;e<s;e++)l[e]=o[e]<n||o[e]>i?0:1;else for(let e=0;e<s;e++)l[e]&&(l[e]=o[e]<n||o[e]>i?0:1);return l})(x,I,M)),E}async function O(e,t={}){const r=t.pyramidLevel||0,o=t.headerInfo||function(e){const{littleEndian:t,isBigTiff:r,firstIFDPos:o}=_(e);let n=o;const i=[];do{const o=B(e,t,n,0,l.Z.TIFF_TAGS,r?8:4);if(!o.success)break;i.push(o.ifd),n=o.nextIFD}while(n>0);return{...I(i),littleEndian:t,isBigTiff:r,ifds:i,pyramidIFDs:M(i),maskIFDs:E(i)}}(e),{ifds:n,noData:i}=o;if(0===n.length)throw new Error("no valid image file directory");const h=U(o);if(h)throw h;let c=null;const f=-1===r?n[n.length-1]:n[r],p=i??t.noDataValue;return c=o.tileWidth?await async function(e,t,r){const o=C(r,"TILEOFFSETS");if(void 0===o)return null;const n=C(r,"TILEBYTECOUNTS"),{width:i,height:s,pixelType:a,tileWidth:l,tileHeight:u}=I([r]),h=P(r,t),c=b(r,"SAMPLESPERPIXEL")||t.planes,f=i*s,p=b(r,"BITSPERSAMPLE"),d=34887===(b(r,"COMPRESSION")??1),m=g(a),y=[];for(let e=0;e<c;e++)y.push(new m(f));let w,x,k,v,T,A,M,E,D,R,L,_,B;const U=Math.ceil(i/l);if(p%8==0)if(d&&h&&c>1){const a=Math.round(o.length/c);for(w=0;w<a;w++){A=Math.floor(w/U)*u,M=w%U*l,E=A*i+M;for(let a=0;a<c;a++){const h=w*c+a;if(0!==n[h])for(k=await S(e,t.littleEndian,r,o[h],n[h]),R=0,D=E,_=Math.min(l,i-M),L=Math.min(u,s-A),B=y[a],v=0;v<L;v++)for(D=E+v*i,R=v*l,T=0;T<_;T++,D++,R++)B[D]=k[R]}}}else for(w=0;w<o.length;w++)if(0!==n[w])for(A=Math.floor(w/U)*u,M=w%U*l,E=A*i+M,k=await S(e,t.littleEndian,r,o[w],n[w]),R=0,D=E,_=Math.min(l,i-M),L=Math.min(u,s-A),x=0;x<c;x++)if(B=y[x],h||d)for(v=0;v<L;v++)for(D=E+v*i,R=l*u*x+v*l,T=0;T<_;T++,D++,R++)B[D]=k[R];else for(v=0;v<L;v++)for(D=E+v*i,R=v*l*c+x,T=0;T<_;T++,D++,R+=c)B[D]=k[R];return{width:i,height:s,pixelType:a,pixels:y}}(e,o,f):await((e,t,r)=>{const o=u.f===t.littleEndian,n=C(r,"STRIPOFFSETS");if(void 0===n)return null;const{width:i,height:a,pixelType:l}=I([r]),h=b(r,"SAMPLESPERPIXEL")||t.planes,c=b(r,"PHOTOMETRICINTERPRETATION"),f=i*a,p=b(r,"BITSPERSAMPLE"),d=g(l),m=new d(f*h),y=C(r,"STRIPBYTECOUNTS"),w=b(r,"ROWSPERSTRIP"),x=b(r,"COMPRESSION")??1;let S,A,P,M,E,D,R,L,_,B=w;if(p%8==0)for(S=0;S<n.length;S++){if(E=S*(w*i)*h,B=(S+1)*w>a?a-S*w:w,"u8"===l||"s8"===l||o)8===x||32946===x?(R=new Uint8Array(e,n[S],y[S]),R=T(R),D=R.buffer):6===x?(R=new Uint8Array(e,n[S],y[S]),R=v(R,h,c),D=R.buffer):5===x?(R=(0,s.J)(e,n[S],y[S],t.littleEndian),D=R.buffer):(y[S]!==B*i*h*p/8&&console.log("strip byte counts is different than expected"),D=e.slice(n[S],n[S]+y[S])),D=k(D,p),M=new d(D);else{switch(6===x||8===x||32946===x?(R=new Uint8Array(e,n[S],y[S]),L=T(R),D=L.buffer):(y[S]!==B*i*h*p/8&&console.log("strip byte counts is different than expected"),D=new ArrayBuffer(y[S]),R=new Uint8Array(e,n[S],y[S]),L=new Uint8Array(D)),l){case"u16":case"s16":for(P=0;P<R.length;P+=2)L[P]=R[P+1],L[P+1]=R[P];break;case"u32":case"s32":case"f32":for(P=0;P<R.length;P+=4)L[P]=R[P+3],L[P+1]=R[P+2],L[P+2]=R[P+1],L[P+3]=R[P]}D=k(D,p),M=new d(D)}m.set(M,E)}const U=[];if(1===h)U.push(m);else for(S=0;S<h;S++){for(_=new d(f),A=0;A<f;A++)_[A]=m[A*h+S];U.push(_)}return{width:i,height:a,pixelType:l,pixels:U}})(e,o,f),c?(null!=p&&(0,a.A)(c,p),c):c}},9832:(e,t,r)=>{r.d(t,{Z:()=>a});const o=(()=>{const e=[];return e[254]="NEWSUBFILETYPE",e[255]="SUBFILETYPE",e[256]="IMAGEWIDTH",e[257]="IMAGELENGTH",e[258]="BITSPERSAMPLE",e[259]="COMPRESSION",e[262]="PHOTOMETRICINTERPRETATION",e[263]="THRESHHOLDING",e[264]="CELLWIDTH",e[265]="CELLLENGTH",e[266]="FILLORDER",e[269]="DOCUMENTNAME",e[270]="IMAGEDESCRIPTION",e[271]="MAKE",e[272]="MODEL",e[273]="STRIPOFFSETS",e[274]="ORIENTATION",e[277]="SAMPLESPERPIXEL",e[278]="ROWSPERSTRIP",e[279]="STRIPBYTECOUNTS",e[280]="MINSAMPLEVALUE",e[281]="MAXSAMPLEVALUE",e[282]="XRESOLUTION",e[283]="YRESOLUTION",e[284]="PLANARCONFIGURATION",e[285]="PAGENAME",e[286]="XPOSITION",e[287]="YPOSITION",e[288]="FREEOFFSETS",e[289]="FREEBYTECOUNTS",e[290]="GRAYRESPONSEUNIT",e[291]="GRAYRESPONSECURVE",e[292]="T4OPTIONS",e[293]="T6OPTIONS",e[296]="RESOLUTIONUNIT",e[297]="PAGENUMBER",e[300]="COLORRESPONSEUNIT",e[301]="TRANSFERFUNCTION",e[305]="SOFTWARE",e[306]="DATETIME",e[315]="ARTIST",e[316]="HOSTCOMPUTER",e[317]="PREDICTOR",e[318]="WHITEPOINT",e[319]="PRIMARYCHROMATICITIES",e[320]="COLORMAP",e[321]="HALFTONEHINTS",e[322]="TILEWIDTH",e[323]="TILELENGTH",e[324]="TILEOFFSETS",e[325]="TILEBYTECOUNTS",e[326]="BADFAXLINES",e[327]="CLEANFAXDATA",e[328]="CONSECUTIVEBADFAXLINES",e[330]="SUBIFD",e[332]="INKSET",e[333]="INKNAMES",e[334]="NUMBEROFINKS",e[336]="DOTRANGE",e[337]="TARGETPRINTER",e[338]="EXTRASAMPLES",e[339]="SAMPLEFORMAT",e[340]="SMINSAMPLEVALUE",e[341]="SMAXSAMPLEVALUE",e[342]="TRANSFERRANGE",e[347]="JPEGTABLES",e[512]="JPEGPROC",e[513]="JPEGIFOFFSET",e[514]="JPEGIFBYTECOUNT",e[515]="JPEGRESTARTINTERVAL",e[517]="JPEGLOSSLESSPREDICTORS",e[518]="JPEGPOINTTRANSFORM",e[519]="JPEGQTABLES",e[520]="JPEGDCTABLES",e[521]="JPEGACTABLES",e[529]="YCBCRCOEFFICIENTS",e[530]="YCBCRSUBSAMPLING",e[531]="YCBCRPOSITIONING",e[532]="REFERENCEBLACKWHITE",e[700]="XMP",e[33550]="GEOPIXELSCALE",e[33922]="GEOTIEPOINTS",e[33432]="COPYRIGHT",e[42112]="GDAL_METADATA",e[42113]="GDAL_NODATA",e[50844]="RPCCOEFFICIENT",e[34264]="GEOTRANSMATRIX",e[34735]="GEOKEYDIRECTORY",e[34736]="GEODOUBLEPARAMS",e[34737]="GEOASCIIPARAMS",e[34665]="EXIFIFD",e[34853]="GPSIFD",e[40965]="INTEROPERABILITYIFD",e})(),n=(()=>{const e=o.slice();return e[36864]="ExifVersion",e[40960]="FlashpixVersion",e[40961]="ColorSpace",e[42240]="Gamma",e[37121]="ComponentsConfiguration",e[37122]="CompressedBitsPerPixel",e[40962]="PixelXDimension",e[40963]="PixelYDimension",e[37500]="MakerNote",e[37510]="UserComment",e[40964]="RelatedSoundFile",e[36867]="DateTimeOriginal",e[36868]="DateTimeDigitized",e[36880]="OffsetTime",e[36881]="OffsetTimeOriginal",e[36882]="OffsetTimeDigitized",e[37520]="SubSecTime",e[37521]="SubSecTimeOriginal",e[37522]="SubSecTimeDigitized",e[37888]="Temperature",e[37889]="Humidity",e[37890]="Pressure",e[37891]="WaterDepth",e[37892]="Acceleration",e[37893]="CameraElevationAngle",e[42016]="ImageUniqueID",e[42032]="CameraOwnerName",e[42033]="BodySerialNumber",e[42034]="LensSpecification",e[42035]="LensMake",e[42036]="LensModel",e[42037]="LensSerialNumber",e[33434]="ExposureTime",e[33437]="FNumber",e[34850]="ExposureProgram",e[34852]="SpectralSensitivity",e[34855]="PhotographicSensitivity",e[34856]="OECF",e[34864]="SensitivityType",e[34865]="StandardOutputSensitivity",e[34866]="RecommendedExposureIndex",e[34867]="ISOSpeed",e[34868]="ISOSpeedLatitudeyyy",e[34869]="ISOSpeedLatitudezzz",e[37377]="ShutterSpeedValue",e[37378]="ApertureValue",e[37379]="BrightnessValue",e[37380]="ExposureBiasValue",e[37381]="MaxApertureValue",e[37382]="SubjectDistance",e[37383]="MeteringMode",e[37384]="LightSource",e[37385]="Flash",e[37386]="FocalLength",e[37396]="SubjectArea",e[41483]="FlashEnergy",e[41484]="SpatialFrequencyResponse",e[41486]="FocalPlaneXResolution",e[41487]="FocalPlaneYResolution",e[41488]="FocalPlaneResolutionUnit",e[41492]="SubjectLocation",e[41493]="ExposureIndex",e[41495]="SensingMethod",e[41728]="FileSource",e[41729]="SceneType",e[41730]="CFAPattern",e[41985]="CustomRendered",e[41986]="ExposureMode",e[41987]="WhiteBalance",e[41988]="DigitalZoomRatio",e[41989]="FocalLengthIn35mmFilm",e[41990]="SceneCaptureType",e[41991]="GainControl",e[41992]="Contrast",e[41993]="Saturation",e[41994]="Sharpness",e[41995]="DeviceSettingDescription",e[41996]="SubjectDistanceRange",e})(),i=(()=>{const e=[];return e[1024]="GTModelTypeGeoKey",e[1025]="GTRasterTypeGeoKey",e[1026]="GTCitationGeoKey",e[2048]="GeographicTypeGeoKey",e[2049]="GeogCitationGeoKey",e[2050]="GeogGeodeticDatumGeoKey",e[2051]="GeogPrimeMeridianGeoKey",e[2052]="GeogLinearUnitsGeoKey",e[2053]="GeogLinearUnitSizeGeoKey",e[2054]="GeogAngularUnitsGeoKey",e[2055]="GeogAngularUnitSizeGeoKey",e[2056]="GeogEllipsoidGeoKey",e[2057]="GeogSemiMajorAxisGeoKey",e[2058]="GeogSemiMinorAxisGeoKey",e[2059]="GeogInvFlatteningGeoKey",e[2061]="GeogPrimeMeridianLongGeoKey",e[2060]="GeogAzimuthUnitsGeoKey",e[3072]="ProjectedCSTypeGeoKey",e[3073]="PCSCitationGeoKey",e[3074]="ProjectionGeoKey",e[3075]="ProjCoordTransGeoKey",e[3076]="ProjLinearUnitsGeoKey",e[3077]="ProjLinearUnitSizeGeoKey",e[3078]="ProjStdParallel1GeoKey",e[3079]="ProjStdParallel2GeoKey",e[3080]="ProjNatOriginLongGeoKey",e[3081]="ProjNatOriginLatGeoKey",e[3082]="ProjFalseEastingGeoKey",e[3083]="ProjFalseNorthingGeoKey",e[3084]="ProjFalseOriginLongGeoKey",e[3085]="ProjFalseOriginLatGeoKey",e[3086]="ProjFalseOriginEastingGeoKey",e[3087]="ProjFalseOriginNorthingGeoKey",e[3088]="ProjCenterLongGeoKey",e[3090]="ProjCenterEastingGeoKey",e[3091]="ProjCenterNorthingGeoKey",e[3092]="ProjScaleAtNatOriginGeoKey",e[3093]="ProjScaleAtCenterGeoKey",e[3094]="ProjAzimuthAngleGeoKey",e[3095]="ProjStraightVertPoleLongGeoKey",e[4096]="VerticalCSTypeGeoKey",e[4097]="VerticalCitationGeoKey",e[4098]="VerticalDatumGeoKey",e[4099]="VerticalUnitsGeoKey",e})(),s=new Map;s.set("EXIFIFD",n),s.set("GPSIFD",["GPSVersionID","GPSLatitudeRef","GPSLatitude","GPSLongitudeRef","GPSLongitude","GPSAltitudeRef","GPSAltitude","GPSTimeStamp","GPSSatellites","GPSStatus","GPSMeasureMode","GPSDOP","GPSSpeedRef","GPSSpeed","GPSTrackRef","GPSTrack","GPSImgDirectionRef","GPSImgDirection","GPSMapDatum","GPSDestLatitudeRef","GPSDestLatitude","GPSDestLongitudeRef","GPSDestLongitude","GPSDestBearingRef","GPSDestBearing","GPSDestDistanceRef","GPSDestDistance","GPSProcessingMethod","GPSAreaInformation","GPSDateStamp","GPSDifferential","GPSHPositioningError"]);const a={TIFF_TAGS:o,ifdTags:s,GEO_KEYS:i,getTagName:(e,t)=>{let r=(t||o)[e];return void 0===r&&(r="unknown"+String(e)),r}}},81578:(e,t,r)=>{r.d(t,{A:()=>i,r:()=>n});const o={u1:[0,1],u2:[0,3],u4:[0,15],u8:[0,255],s8:[-128,127],u16:[0,65535],s16:[-32768,32767],u32:[0,4294967295],s32:[-2147483648,2147483647],f32:[-34028234663852886e22,34028234663852886e22],f64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function n(e){return o[e]??[-34028234663852886e22,34028234663852886e22]}function i(e,t,r){if(e.depthCount&&e.depthCount>1)return;const{pixels:o,statistics:i,pixelType:s}=e,a=o[0].length,l=e.bandMasks??[],u=e.mask??new Uint8Array(a).fill(255),h="f32"===s||"f64"===s,c=n(s);let f=!1;for(let e=0;e<o.length;e++){const n="number"==typeof t?t:t[e];if(null==n)continue;const p=i?.[e].minValue??c[0],d=i?.[e].maxValue??c[1];if(p>n+Number.EPSILON||d<n-Number.EPSILON)continue;const g=l[e]||new Uint8Array(a).fill(255),m=o[e],y=r?.customFloatTolerance;if(h&&0!==y){let e=y;e||(e=Math.abs(n)>=9999999e31?2e-7*Math.abs(n):"f32"===s?2**-23:Number.EPSILON);for(let t=0;t<m.length;t++)g[t]&&Math.abs(m[t]-n)<e&&(m[t]=0,g[t]=0,u[t]=0,f=!0)}else for(let e=0;e<m.length;e++)g[e]&&m[e]===n&&(m[e]=0,g[e]=0,u[e]=0,f=!0);l[e]=g}f&&(e.bandMasks=l,e.mask=u),f&&"updateStatistics"in e&&e.updateStatistics()}},48279:(e,t,r)=>{r.d(t,{f:()=>o});const o=(()=>{const e=new ArrayBuffer(4),t=new Uint8Array(e);return new Uint32Array(e)[0]=1,1===t[0]})()},55914:(e,t,r)=>{r.d(t,{CD:()=>n,DX:()=>o,Gd:()=>f,Pz:()=>p,Qh:()=>B,SJ:()=>d,Uk:()=>U,Vl:()=>L,XV:()=>g,a2:()=>C,hd:()=>u,nA:()=>T,nk:()=>h,oB:()=>v,pW:()=>I,qF:()=>c,sS:()=>k,us:()=>A,wV:()=>m,zp:()=>y});var o,n,i,s=r(70586),a=r(5847),l=r(81578);(i=o||(o={}))[i.matchAny=0]="matchAny",i[i.matchAll=1]="matchAll",function(e){e[e.bestMatch=0]="bestMatch",e[e.fail=1]="fail"}(n||(n={}));const u=6;function h(e){return(0,s.pC)(e)&&"esri.layers.support.PixelBlock"===e.declaredClass&&e.pixels&&e.pixels.length>0}function c(e,t){if(!t?.length||!h(e))return e;const r=e.pixels.length;return t&&t.some((e=>e>=r))||1===r&&1===t.length&&0===t[0]?e:r!==t.length||t.some(((e,t)=>e!==t))?new a.Z({pixelType:e.pixelType,width:e.width,height:e.height,mask:e.mask,validPixelCount:e.validPixelCount,maskIsAlpha:e.maskIsAlpha,pixels:t.map((t=>e.pixels[t])),statistics:e.statistics&&t.map((t=>e.statistics[t]))}):e}function f(e){if(!e?.length||e.some((e=>!h(e))))return null;if(1===e.length)return(0,s.pC)(e[0])?e[0].clone():null;const t=e,{width:r,height:o,pixelType:n}=t[0];if(t.some((e=>e.width!==r||e.height!==o)))return null;const i=t.map((({mask:e})=>e)).filter((e=>null!=e));let l=null;i.length&&(l=new Uint8Array(r*o),l.set(i[0]),i.length>1&&x(i.slice(1),l));const u=[];t.forEach((({pixels:e})=>u.push(...e)));const c=t.map((({statistics:e})=>e)).filter((e=>e?.length)),f=[];return c.forEach((e=>f.push(...e))),new a.Z({pixelType:n,width:r,height:o,mask:l,pixels:u,statistics:f.length?f:null})}function p(e){if(!e)return;const t=e.colormap;if(!t||0===t.length)return;const r=t.sort(((e,t)=>e[0]-t[0]));let o=0;r[0][0]<0&&(o=r[0][0]);const n=Math.max(256,r[r.length-1][0]-o+1),i=new Uint8Array(4*n),s=[];let a,l=0,u=0;const h=5===r[0].length;if(n>65536)return r.forEach((e=>{s[e[0]-o]=h?e.slice(1):e.slice(1).concat([255])})),{indexed2DColormap:s,offset:o,alphaSpecified:h};if(e.fillUnspecified)for(a=r[u],l=a[0]-o;l<n;l++)i[4*l]=a[1],i[4*l+1]=a[2],i[4*l+2]=a[3],i[4*l+3]=h?a[4]:255,l===a[0]-o&&(a=u===r.length-1?a:r[++u]);else for(l=0;l<r.length;l++)a=r[l],u=4*(a[0]-o),i[u]=a[1],i[u+1]=a[2],i[u+2]=a[3],i[u+3]=h?a[4]:255;return{indexedColormap:i,offset:o,alphaSpecified:h}}function d(e,t){if(!h(e))return e;if(!t||!t.indexedColormap&&!t.indexed2DColormap)return e;const r=e.clone(),o=r.pixels;let n=r.mask;const i=r.width*r.height;if(1!==o.length)return e;const{indexedColormap:a,indexed2DColormap:l,offset:u,alphaSpecified:c}=t;let f=0;const p=o[0],d=new Uint8Array(p.length),g=new Uint8Array(p.length),m=new Uint8Array(p.length);let y,C=0;if(a){const e=a.length-1;if((0,s.pC)(n))for(f=0;f<i;f++)n[f]&&(C=4*(p[f]-u),C<u||C>e?n[f]=0:(d[f]=a[C],g[f]=a[C+1],m[f]=a[C+2],n[f]=a[C+3]));else{for(n=new Uint8Array(i),f=0;f<i;f++)C=4*(p[f]-u),C<u||C>e?n[f]=0:(d[f]=a[C],g[f]=a[C+1],m[f]=a[C+2],n[f]=a[C+3]);r.mask=n}}else if(l)if((0,s.pC)(n))for(f=0;f<i;f++)n[f]&&(y=l[p[f]],d[f]=y[0],g[f]=y[1],m[f]=y[2],n[f]=y[3]);else{for(n=new Uint8Array(i),f=0;f<i;f++)y=l[p[f]],d[f]=y[0],g[f]=y[1],m[f]=y[2],n[f]=y[3];r.mask=n}return r.pixels=[d,g,m],r.statistics=null,r.pixelType="u8",r.maskIsAlpha=c,r}function g(e,t){if(!h(e))return null;const{pixels:r,mask:o}=e,n=r.length;let i=t.lut;const{offset:s}=t;i&&1===i[0].length&&(i=r.map((()=>i)));const l=[],u=t.outputPixelType||"u8";for(let e=0;e<n;e++){const t=m(r[e],o,i[e],s||0,u);l.push(t)}const c=new a.Z({width:e.width,height:e.height,pixels:l,mask:o,pixelType:u});return c.updateStatistics(),c}function m(e,t,r,o,n){const i=e.length,s=a.Z.createEmptyBand(n,i);if(t)for(let n=0;n<i;n++)t[n]&&(s[n]=r[e[n]-o]);else for(let t=0;t<i;t++)s[t]=r[e[t]-o];return s}function y(e,t){if(!h(e))return null;const r=e.clone(),{pixels:o}=r,n=r.width*r.height,i=t.length,s=Math.floor(i/2),a=t[Math.floor(s)],l=o[0];let u,c,f,p,d,g,m=!1;const y=new Uint8Array(n),C=new Uint8Array(n),w=new Uint8Array(n);let b=r.mask;const x=4===t[0].mappedColor.length;for(b||(b=new Uint8Array(n),b.fill(x?255:1),r.mask=b),d=0;d<n;d++)if(b[d]){for(u=l[d],m=!1,g=s,c=a,f=0,p=i-1;p-f>1;){if(u===c.value){m=!0;break}u>c.value?f=g:p=g,g=Math.floor((f+p)/2),c=t[Math.floor(g)]}m||(u===t[f].value?(c=t[f],m=!0):u===t[p].value?(c=t[p],m=!0):u<t[f].value?(m=!1,c=null):u>t[f].value&&(u<t[p].value?(c=t[f],m=!0):p===i-1?(m=!1,c=null):(c=t[p],m=!0))),m?(y[d]=c.mappedColor[0],C[d]=c.mappedColor[1],w[d]=c.mappedColor[2],b[d]=c.mappedColor[3]):y[d]=C[d]=w[d]=b[d]=0}return r.pixels=[y,C,w],r.mask=b,r.pixelType="u8",r.maskIsAlpha=x,r}function C(e,t){if(!h(e))return null;const{width:r,height:o}=e,{inputRanges:n,outputValues:i,outputPixelType:s,noDataRanges:u,allowUnmatched:c,isLastInputRangeInclusive:f}=t,p=e.pixels[0],d=a.Z.createEmptyBand(s,p.length),g=e.mask,m=new Uint8Array(r*o);g?m.set(g):m.fill(255);const y=e.pixelType.startsWith("f")?1e-6:0,C=n.map((e=>e-y));C[0]=n[0],C[C.length-1]=n[n.length-1]+(f?1e-6:0);const w=n.length/2,[b,x]=(0,l.r)(s);for(let e=0;e<o;e++)for(let t=0;t<r;t++){const o=e*r+t;if(m[o]){const e=p[o];let t=!1;for(let r=w-1;r>=0;r--)if(e===C[2*r]||e>C[2*r]&&e<C[2*r+1]){d[o]=i[r],t=!0;break}t||(c?d[o]=e>x?x:e<b?b:e:m[o]=0)}}if(u?.length)for(let e=0;e<o;e++)for(let t=0;t<r;t++){const o=e*r+t;if(!g||g[o]){const e=p[o];for(let t=0;t<w;t+=2)if(e>=u[t]&&e<=u[t+1]){d[o]=0,m[o]=0;break}}}return new a.Z({width:r,height:o,pixelType:s,pixels:[d],mask:m})}function w(e,t,r,o){const n=null!=r&&r.length>=2?new Set(r):null,i=1===r?.length?r[0]:null,s=!!t?.length;for(let r=0;r<e.length;r++)if(o[r]){const a=e[r];if(s){let e=!1;for(let r=0;r<t.length;r+=2)if(a>=t[r]&&a<=t[r+1]){e=!0;break}e||(o[r]=0)}o[r]&&(a===i||n?.has(a))&&(o[r]=0)}}function b(e,t){const r=e[0].length;for(let o=0;o<r;o++)if(t[o]){let r=!1;for(let t=0;t<e.length;t++)if(e[t][o]){r=!0;break}r||(t[o]=0)}}function x(e,t){const r=e[0].length;for(let o=0;o<r;o++)if(t[o]){let r=!1;for(let t=0;t<e.length;t++)if(0===e[t][o]){r=!0;break}r&&(t[o]=0)}}function k(e,t){if(!h(e))return null;const{width:r,height:o,pixels:n}=e,i=r*o,s=new Uint8Array(i);e.mask?s.set(e.mask):s.fill(255);const l=n.length,{includedRanges:u,noDataValues:c,outputPixelType:f,matchAll:p,lookups:d}=t;if(d){const e=[];for(let t=0;t<l;t++){const r=d[t],o=m(n[t],s,r.lut,r.offset||0,"u8");e.push(o)}1===e.length?s.set(e[0]):p?b(e,s):x(e,s)}else if(p){const e=[];for(let t=0;t<l;t++){const r=new Uint8Array(i);r.set(s),w(n[t],u?.slice(2*t,2*t+2),c?.[t],r),e.push(r)}1===e.length?s.set(e[0]):b(e,s)}else for(let e=0;e<l;e++)w(n[e],u?.slice(2*e,2*e+2),c?.[e],s);return new a.Z({width:r,height:o,pixelType:f,pixels:n,mask:s})}function v(e){const{srcPixelType:t,inputRanges:r,outputValues:o,allowUnmatched:n,noDataRanges:i,isLastInputRangeInclusive:s,outputPixelType:u}=e;if("u8"!==t&&"s8"!==t&&"u16"!==t&&"s16"!==t)return null;const h=t.includes("16")?65536:256,c=t.includes("s")?-h/2:0,f=a.Z.createEmptyBand(u,h),p=new Uint8Array(h);n&&p.fill(255);const[d,g]=(0,l.r)(u);if(r?.length&&o?.length){const e=1e-6,t=r.map((t=>t-e));t[0]=r[0],s&&(t[t.length-1]=r[r.length-1]);for(let e=0;e<t.length;e++){const r=o[e]>g?g:o[e]<d?d:o[e],n=Math.ceil(t[2*e]-c),i=Math.floor(t[2*e+1]-c);for(let e=n;e<=i;e++)f[e]=r,p[e]=255}}if(i?.length)for(let e=0;e<i.length;e++){const t=Math.ceil(i[2*e]-c),r=Math.floor(i[2*e+1]-c);for(let e=t;e<=r;e++)p[e]=0}return{lut:f,offset:c,mask:p}}function T(e,t,r){if("u8"!==e&&"s8"!==e&&"u16"!==e&&"s16"!==e)return null;const o=e.includes("16")?65536:256,n=e.includes("s")?-o/2:0,i=new Uint8Array(o);if(t)for(let e=0;e<t.length;e++){const r=Math.ceil(t[2*e]-n),o=Math.floor(t[2*e+1]-n);for(let e=r;e<=o;e++)i[e]=255}else i.fill(255);if(r)for(let e=0;e<r.length;e++)i[r[e]-n]=0;return{lut:i,offset:n}}function S(e,t,r,o,n,i){const{width:s,height:a}=r.block,{x:l,y:u}=r.offset,{width:h,height:c}=r.mosaic,f=function(e,t,r,o,n,i,s,a){return{xmin:n<=r*e?0:n<r*e+e?n-r*e:e,ymin:i<=o*t?0:i<o*t+t?i-o*t:t,xmax:n+s<=r*e?0:n+s<r*e+e?n+s-r*e:e,ymax:i+a<=o*t?0:i+a<o*t+t?i+a-o*t:t}}(s,a,o,n,l,u,h,c);let p=0,d=0;if(i){const e=i.hasGCSSShiftTransform?360:i.halfWorldWidth??0,t=s*i.resolutionX,r=i.startX+o*t;r<e&&r+t>e?d=i.rightPadding:r>=e&&(p=i.leftMargin-i.rightPadding,d=0)}if(f.xmax-=d,"number"!=typeof t)for(let r=f.ymin;r<f.ymax;r++){const i=(n*a+r-u)*h+(o*s-l)+p,c=r*s;for(let r=f.xmin;r<f.xmax;r++)e[i+r]=t[c+r]}else for(let r=f.ymin;r<f.ymax;r++){const i=(n*a+r-u)*h+(o*s-l)+p;for(let r=f.xmin;r<f.xmax;r++)e[i+r]=t}}function A(e,t,r={}){const{clipOffset:o,clipSize:n,alignmentInfo:i,blockWidths:l}=r;if(l)return function(e,t,r){const o=e.find((e=>(0,s.pC)(e)));if((0,s.Wi)(o))return null;const n=e.some((e=>!(0,s.pC)(e)||!!e.mask)),{width:i,height:l}=t,u=n?new Uint8Array(i*l):null,{blockWidths:c}=r,f=[],p=o.getPlaneCount(),d=a.Z.getPixelArrayConstructor(o.pixelType);if(n)for(let t=0,r=0;t<e.length;r+=c[t],t++){const o=e[t];if(!h(o))continue;const n=(0,s.Wg)(o.mask);for(let e=0;e<l;e++)for(let s=0;s<c[t];s++)u[e*i+s+r]=null==n?255:n[e*o.width+s]}for(let t=0;t<p;t++){const r=new d(i*l);for(let o=0,n=0;o<e.length;n+=c[o],o++){const s=e[o];if(!h(s))continue;const a=s.pixels[t];if(null!=a)for(let e=0;e<l;e++)for(let t=0;t<c[o];t++)r[e*i+t+n]=a[e*s.width+t]}f.push(r)}const g=new a.Z({width:i,height:l,mask:u,pixels:f,pixelType:o.pixelType});return g.updateStatistics(),g}(e,t,{blockWidths:l});const u=e.find((e=>h(e)));if((0,s.Wi)(u))return null;const c=n?n.width:t.width,f=n?n.height:t.height,p=u.width,d=u.height,g=t.width/p,m=t.height/d,y={offset:o||{x:0,y:0},mosaic:n||t,block:{width:p,height:d}},C=u.pixelType,w=a.Z.getPixelArrayConstructor(C),b=u.pixels.length,x=[];let k,v,T;for(let t=0;t<b;t++){v=new w(c*f);for(let r=0;r<m;r++)for(let o=0;o<g;o++){const n=e[r*g+o];h(n)&&(k=n.pixels[t],S(v,k,y,o,r,i))}x.push(v)}if(e.some((e=>(0,s.Wi)(e)||(0,s.pC)(e.mask)&&e.mask.length>0))){T=new Uint8Array(c*f);for(let t=0;t<m;t++)for(let r=0;r<g;r++){const o=e[t*g+r],n=(0,s.pC)(o)?o.mask:null;(0,s.pC)(n)?S(T,n,y,r,t,i):S(T,o?1:0,y,r,t,i)}}const A=new a.Z({width:c,height:f,pixels:x,pixelType:C,mask:T});return A.updateStatistics(),A}function I(e,t,r){if(!h(e))return null;const{width:o,height:n}=e,i=t.x,s=t.y,a=r.width+i,l=r.height+s;if(i<0||s<0||a>o||l>n)return e;if(0===i&&0===s&&a===o&&l===n)return e;e.mask||(e.mask=new Uint8Array(o*n));const u=e.mask;for(let e=0;e<n;e++){const t=e*o;for(let r=0;r<o;r++)u[t+r]=e<s||e>=l||r<i||r>=a?0:1}return e.updateStatistics(),e}function P(e){if(0===e.size)return 0;let t=0,r=-1,o=0;const n=e.keys();let i=n.next();for(;!i.done;)o=e.get(i.value),o>t&&(r=i.value,t=o),i=n.next();return r}function M(e,t,r){if(0===r)return;const o=e.get(t);1===o?e.delete(t):e.set(t,o-1)}function E(e,t,r){0!==r&&e.set(t,e.has(t)?e.get(t)+1:1)}function D(e,t,r){let{x:o,y:n}=t;const{width:i,height:l}=r;if(0===o&&0===n&&l===e.height&&i===e.width)return e;const{width:u,height:c}=e,f=Math.max(0,n),p=Math.max(0,o),d=Math.min(o+i,u),g=Math.min(n+l,c);if(d<0||g<0||!h(e))return null;o=Math.max(0,-o),n=Math.max(0,-n);const{pixels:m}=e,y=i*l,C=m.length,w=[];for(let t=0;t<C;t++){const r=m[t],s=a.Z.createEmptyBand(e.pixelType,y);for(let e=f;e<g;e++){const t=e*u;let a=(e+n-f)*i+o;for(let e=p;e<d;e++)s[a++]=r[t+e]}w.push(s)}const b=new Uint8Array(y),x=(0,s.Wg)(e.mask);for(let e=f;e<g;e++){const t=e*u;let r=(e+n-f)*i+o;for(let e=p;e<d;e++)b[r++]=x?x[t+e]:1}const k=new a.Z({width:r.width,height:r.height,pixelType:e.pixelType,pixels:w,mask:b});return k.updateStatistics(),k}function R(e,t=!0){if(!h(e))return null;const{pixels:r,width:o,height:n,mask:i,pixelType:l}=e,u=[],c=Math.round(o/2),f=Math.round(n/2),p=n-1,d=o-1;for(let e=0;e<r.length;e++){const i=r[e],s=a.Z.createEmptyBand(l,c*f);let h=0;for(let e=0;e<n;e+=2)for(let r=0;r<o;r+=2){const n=i[e*o+r];if(t){const t=r===d?n:i[e*o+r+1],a=e===p?n:i[e*o+r+o],l=r===d?a:e===p?t:i[e*o+r+o+1];s[h++]=(n+t+a+l)/4}else s[h++]=n}u.push(s)}let g=null;if((0,s.pC)(i)){g=new Uint8Array(c*f);let e=0;for(let r=0;r<n;r+=2)for(let n=0;n<o;n+=2){const s=i[r*o+n];if(t){const t=n===d?s:i[r*o+n+1],a=r===p?s:i[r*o+n+o],l=n===d?a:r===p?t:i[r*o+n+o+1];g[e++]=s*t*a*l?1:0}else g[e++]=s}}return new a.Z({width:c,height:f,pixelType:l,pixels:u,mask:g})}function L(e,t,r){if(!h(e))return null;const{width:o,height:n}=t;let{width:i,height:s}=e;const a=new Map,l={x:0,y:0},u=null==r?1:1+r;let c=e;for(let e=0;e<u;e++){const r=Math.ceil(i/o),h=Math.ceil(s/n);for(let i=0;i<h;i++){l.y=i*n;for(let n=0;n<r;n++){l.x=n*o;const r=D(c,l,t);a.set(`${e}/${i}/${n}`,r)}}e<u-1&&(c=R(c)),i=Math.round(i/2),s=Math.round(s/2)}return a}function _(e,t,r,o,n=0){const{width:i,height:s}=e,{width:a,height:l}=t,u=o.cols,h=o.rows,c=Math.ceil(a/u-.1/u),f=Math.ceil(l/h-.1/h);let p,d,g,m,y,C,w;const b=c*u,x=b*f*h,k=new Float32Array(x),v=new Float32Array(x),T=new Uint32Array(x),S=new Uint32Array(x);let A,I,P=0;for(let e=0;e<f;e++)for(let t=0;t<c;t++){p=12*(e*c+t),d=r[p],g=r[p+1],m=r[p+2],y=r[p+3],C=r[p+4],w=r[p+5];for(let r=0;r<h;r++){P=(e*h+r)*b+t*u,I=(r+.5)/h;for(let e=0;e<r;e++)A=(e+.5)/u,k[P+e]=(d*A+g*I+m)*i+n,v[P+e]=(y*A+C*I+w)*s+n,T[P+e]=Math.floor(k[P+e]),S[P+e]=Math.floor(v[P+e])}p+=6,d=r[p],g=r[p+1],m=r[p+2],y=r[p+3],C=r[p+4],w=r[p+5];for(let r=0;r<h;r++){P=(e*h+r)*b+t*u,I=(r+.5)/h;for(let e=r;e<u;e++)A=(e+.5)/u,k[P+e]=(d*A+g*I+m)*i+n,v[P+e]=(y*A+C*I+w)*s+n,T[P+e]=Math.floor(k[P+e]),S[P+e]=Math.floor(v[P+e])}}return{offsets_x:k,offsets_y:v,offsets_xi:T,offsets_yi:S,gridWidth:b}}function B(e,t){const{coefficients:r,spacing:o}=t,{offsets_x:n,offsets_y:i,gridWidth:s}=_(e,e,r,{rows:o[0],cols:o[1]}),{width:a,height:l}=e,u=new Float32Array(a*l),h=180/Math.PI;for(let e=0;e<l;e++)for(let t=0;t<a;t++){const r=e*s+t,o=0===e?r:r-s,c=e===l-1?r:r+s,f=n[o]-n[c],p=i[c]-i[o];if(isNaN(f)||isNaN(p))u[e*a+t]=90;else{let r=Math.atan2(p,f)*h;r=(360+r)%360,u[e*a+t]=r}}return u}function U(e,t,r,o,n="nearest"){if(!h(e))return null;"majority"===n&&(e=function(e){if(!h(e))return null;const t=e.clone(),{width:r,height:o,pixels:n}=e,i=n[0],a=t.pixels[0],l=(0,s.Wg)(e.mask);for(let e=2;e<o-1;e++){const t=new Map;for(let o=e-2;o<e+2;o++)for(let e=0;e<4;e++){const n=o*r+e;E(t,i[n],l?l[n]:1)}a[e*r]=P(t),a[e*r+1]=a[e*r+2]=a[e*r];let o=3;for(;o<r-1;o++){let n=(e-2)*r+o+1;E(t,i[n],l?l[n]:1),n=(e-1)*r+o+1,E(t,i[n],l?l[n]:1),n=e*r+o+1,E(t,i[n],l?l[n]:1),n=(e+1)*r+o+1,E(t,i[n],l?l[n]:1),n=(e-2)*r+o-3,M(t,i[n],l?l[n]:1),n=(e-1)*r+o-3,M(t,i[n],l?l[n]:1),n=e*r+o-3,M(t,i[n],l?l[n]:1),n=(e+1)*r+o-3,M(t,i[n],l?l[n]:1),a[e*r+o]=P(t)}a[e*r+o+1]=a[e*r+o]}for(let e=0;e<r;e++)a[e]=a[r+e]=a[2*r+e],a[(o-1)*r+e]=a[(o-2)*r+e];return t.updateStatistics(),t}(e));const{pixels:i,mask:l,pixelType:u}=e,c=e.width,f=e.height,p=a.Z.getPixelArrayConstructor(u),d=i.length,{width:g,height:m}=t;let y=!1;for(let e=0;e<r.length;e+=3)-1===r[e]&&-1===r[e+1]&&-1===r[e+2]&&(y=!0);const{offsets_x:C,offsets_y:w,offsets_xi:b,offsets_yi:x,gridWidth:k}=_({width:c,height:f},t,r,o,"majority"===n?.5:0);let v;const T=(e,t,r)=>{const o=e instanceof Float32Array||e instanceof Float64Array?0:.5;for(let n=0;n<m;n++){v=n*k;for(let i=0;i<g;i++){if(C[v]<0||w[v]<0)e[n*g+i]=0;else if(r)e[n*g+i]=t[b[v]+x[v]*c];else{const r=Math.floor(C[v]),s=Math.floor(w[v]),a=Math.ceil(C[v]),u=Math.ceil(w[v]),h=C[v]-r,f=w[v]-s;if(!l||l[r+s*c]&&l[r+s*c]&&l[r+u*c]&&l[a+u*c]){const l=(1-h)*t[r+s*c]+h*t[a+s*c],p=(1-h)*t[r+u*c]+h*t[a+u*c];e[n*g+i]=(1-f)*l+f*p+o}else e[n*g+i]=t[b[v]+x[v]*c]}v++}}},S=[];let A;for(let e=0;e<d;e++)A=new p(g*m),T(A,i[e],"nearest"===n||"majority"===n),S.push(A);const I=new a.Z({width:g,height:m,pixelType:u,pixels:S});if((0,s.pC)(l))I.mask=new Uint8Array(g*m),T(I.mask,l,!0);else if(y){I.mask=new Uint8Array(g*m);for(let e=0;e<g*m;e++)I.mask[e]=C[e]<0||w[e]<0?0:1}return I.updateStatistics(),I}},15612:(e,t,r)=>{r.d(t,{AV:()=>d,Hv:()=>f,Oh:()=>p,ZF:()=>h,dy:()=>m,hE:()=>u,oc:()=>l,um:()=>g});var o=r(70586),n=r(5847),i=r(81578),s=r(75509);const a=[.299,.587,.114];function l(e,t=256){t=Math.min(t,256);const{size:r,counts:o}=e,n=new Uint8Array(r),i=o.reduce(((e,r)=>e+r/t),0);let s=0,a=0,l=0,u=i;for(let e=0;e<r;e++)if(l+=o[e],!(e<r-1&&l+o[e+1]<u)){for(;s<t-1&&u<l;)s++,u+=i;for(let t=a;t<=e;t++)n[t]=s;a=e+1}for(let e=a;e<r;e++)n[e]=t-1;return n}function u(e){const{minCutOff:t,maxCutOff:r,gamma:o,pixelType:n,rounding:i}=e,s=e.outMin||0,a=e.outMax||255;if(!["u8","u16","s8","s16"].includes(n))return null;const l=t.length;let u,h,c=0;"s8"===n?c=-127:"s16"===n&&(c=-32767);let f=256;["u16","s16"].includes(n)&&(f=65536);const p=[],d=[],m=a-s;for(u=0;u<l;u++)d[u]=r[u]-t[u],p[u]=0===d[u]?0:m/d[u];let y;const C=[];let w,b,x;if(o&&o.length>=l){const e=g(l,o);for(u=0;u<l;u++){for(x=[],h=0;h<f;h++)if(0!==d[u])if(w=h+c,y=(w-t[u])/d[u],b=1,o[u]>1&&(b-=(1/m)**(y*e[u])),w<r[u]&&w>t[u]){const e=b*m*y**(1/o[u])+s;x[h]="floor"===i?Math.floor(e):"round"===i?Math.round(e):e}else w>=r[u]?x[h]=a:x[h]=s;else x[h]=s;C[u]=x}}else for(u=0;u<l;u++){for(x=[],h=0;h<f;h++)if(w=h+c,w<=t[u])x[h]=s;else if(w>=r[u])x[h]=a;else{const e=(w-t[u])*p[u]+s;x[h]="floor"===i?Math.floor(e):"round"===i?Math.round(e):e}C[u]=x}if(null!=e.contrastOffset){const t=function(e,t){const r=Math.min(Math.max(e,-100),100),o=Math.min(Math.max(t??0,-100),100),n=255;let i=0,s=0;const a=new Uint8Array(256);for(i=0;i<256;i++)r>0&&r<100?s=(200*i-25500+510*o)/(2*(100-r))+128:r<=0&&r>-100?s=(200*i-25500+510*o)*(100+r)/2e4+128:100===r?(s=200*i-25500+256*(100-r)+510*o,s=s>0?n:0):-100===r&&(s=128),a[i]=s>n?n:s<0?0:s;return a}(e.contrastOffset,e.brightnessOffset);for(u=0;u<l;u++)for(x=C[u],h=0;h<f;h++)x[h]=t[x[h]]}return{lut:C,offset:c}}function h(e,t,r){const o=[];for(let n=0;n<t.length;n++){let i=0,s=0,l=0;"min"in t[n]?({min:i,max:s,avg:l}=t[n]):[i,s,l]=t[n];let u=l??0;"u8"!==e&&(u=255*(u-i)/(s-i)),r&&(u*=a[n]),o.push(c(u))}return o}function c(e){if(e<=0||e>=255)return 1;let t=0;150!==e&&(t=e<=150?45*Math.cos(.01047*e):17*Math.sin(.021*e));const r=e+t,o=Math.log(e/255),n=Math.log(r/255);if(0===n)return 1;const i=o/n;return isNaN(i)?1:Math.min(9.9,Math.max(.01,i))}function f(e){if((0,o.Wi)(e)||!e.pixels?.length)return null;const{pixels:t,mask:r,pixelType:n}=e,i=e.width*e.height,s=t.length;let a,l,u,h,c;const f=[],p=[];let d,g,m,y,C,w,b,x,k,v;const T=256;for(h=0;h<s;h++){if(d=new Uint32Array(T),m=t[h],"u8"===n)if(a=-.5,l=255.5,r)for(c=0;c<i;c++)r[c]&&d[m[c]]++;else for(c=0;c<i;c++)d[m[c]]++;else{let t=!1;e.statistics||(e.updateStatistics(),t=!0);const o=e.statistics;if(a=o[h].minValue,l=o[h].maxValue,u=(l-a)/T,0===u){!o||e.validPixelCount||t||e.updateStatistics();const r=(e.validPixelCount||e.width*e.height)/T;for(let e=0;e<T;e++)d[e]=Math.round(r*(e+1))-Math.round(r*e)}else{for(g=new Uint32Array(257),c=0;c<i;c++)r&&!r[c]||g[Math.floor((m[c]-a)/u)]++;for(c=0;c<255;c++)d[c]=g[c];d[255]=g[255]+g[T]}}for(f.push({min:a,max:l,size:T,counts:d}),y=0,C=0,x=0,c=0;c<T;c++)y+=d[c],C+=c*d[c];for(k=C/y,c=0;c<T;c++)x+=d[c]*(c-k)**2;v=Math.sqrt(x/(y-1)),u=(l-a)/T,w=(k+.5)*u+a,b=v*u,p.push({min:a,max:l,avg:w,stddev:b})}return{statistics:p,histograms:f}}function p(e){const t=[];for(let r=0;r<e.length;r++){const{min:o,max:n,size:i,counts:s}=e[r];let a=0,l=0;for(let e=0;e<i;e++)a+=s[e],l+=e*s[e];const u=l/a;let h=0;for(let e=0;e<i;e++)h+=s[e]*(e-u)**2;const c=(n-o)/i,f=(u+.5)*c+o,p=Math.sqrt(h/(a-1))*c;t.push({min:o,max:n,avg:f,stddev:p})}return t}function d(e,t){const{pixelBlock:r,bandIds:n,returnHistogramLut:a,rasterInfo:u}=t;let h=null,c=null,p=e.stretchType;if("number"==typeof p&&(p=s.J[p]),e.dra)if("minMax"===p&&(0,o.pC)(r)&&r.statistics)h=r.statistics.map((e=>[e.minValue,e.maxValue,0,0]));else{const e=f(r);h=(0,o.pC)(e)?e.statistics:null,c=(0,o.pC)(e)?e.histograms:null}else h=e.statistics?.length>0?e.statistics:(0,o.Wg)(u.statistics),c=e.histograms||(0,o.Wg)(u.histograms);"percentClip"!==p&&"histogramEqualization"!==p||c?.length||(p="minMax");const d=h?.length||c?.length||u.bandCount,g=[],m=[];let y,C,w,b,x,k,v,T,S;h&&!Array.isArray(h[0])&&(h=h.map((e=>[e.min,e.max,e.avg,e.stddev])));const[A,I]=(0,i.r)(u.pixelType);if(!h?.length){for(h=[],T=0;T<d;T++)h.push([A,I,1,1]);"standardDeviation"===p&&(p="minMax")}switch(p){case"none":for(T=0;T<d;T++)g[T]=A,m[T]=I;break;case"minMax":for(T=0;T<d;T++)g[T]=h[T][0],m[T]=h[T][1];break;case"standardDeviation":for(T=0;T<d;T++)g[T]=h[T][2]-e.numberOfStandardDeviations*h[T][3],m[T]=h[T][2]+e.numberOfStandardDeviations*h[T][3],g[T]<h[T][0]&&(g[T]=h[T][0]),m[T]>h[T][1]&&(m[T]=h[T][1]);break;case"histogramEqualization":for((0,o.O3)(c),T=0;T<d;T++)g[T]=c[T].min,m[T]=c[T].max;break;case"percentClip":for((0,o.O3)(c),T=0;T<c.length;T++){for(y=c[T],x=new Uint32Array(y.size),b=[...y.counts],b.length>=20&&(b[0]=b[1]=b[2]=b[b.length-1]=b[b.length-2]=0),w=0,C=(y.max-y.min)/y.size,v=-.5===y.min&&1===C?.5:0,S=0;S<y.size;S++)w+=b[S],x[S]=w;for(k=(e.minPercent||0)*w/100,g[T]=y.min+v,S=0;S<y.size;S++)if(x[S]>k){g[T]=y.min+C*(S+v);break}for(k=(1-(e.maxPercent||0)/100)*w,m[T]=y.max+v,S=y.size-2;S>=0;S--)if(x[S]<k){m[T]=y.min+C*(S+2-v);break}if(m[T]<g[T]){const e=g[T];g[T]=m[T],m[T]=e}}break;default:for(T=0;T<d;T++)g[T]=h[T][0],m[T]=h[T][1]}let P,M,E;return"histogramEqualization"===p?((0,o.O3)(c),M=c[0].size||256,P=0,a&&(E=c.map((e=>l(e))))):(M=e.max||255,P=e.min||0),function(e,t){if(null==t||0===t.length)return e;const r=Math.max.apply(null,t),{minCutOff:o,maxCutOff:n,outMin:i,outMax:s,histogramLut:a}=e;return o.length===t.length||o.length<=r?e:{minCutOff:t.map((e=>o[e])),maxCutOff:t.map((e=>n[e])),histogramLut:a?t.map((e=>a[e])):null,outMin:i,outMax:s}}({minCutOff:g,maxCutOff:m,outMax:M,outMin:P,histogramLut:E},n)}function g(e,t){const r=new Float32Array(e);for(let o=0;o<e;o++)t[o]>1?t[o]>2?r[o]=6.5+(t[o]-2)**2.5:r[o]=6.5+100*(2-t[o])**4:r[o]=1;return r}function m(e,t){if((0,o.Wi)(e)||!e.pixels?.length)return e;const{mask:r,width:i,height:s,pixels:a}=e,{minCutOff:l,maxCutOff:u,gamma:h}=t,c=t.outMin||0,f=t.outMax||255,p=i*s,d=t.outputPixelType||"u8",m=e.pixels.map((()=>n.Z.createEmptyBand(d,p))),y=m.length;let C,w,b,x,k;const v=f-c,T=[],S=[];for(C=0;C<y;C++)S[C]=u[C]-l[C],T[C]=0===S[C]?0:v/S[C];const A=d.startsWith("u")||d.startsWith("s"),I=h&&h.length>=y,P=!!t.isRenderer;if(I){const e=g(y,h);for(w=0;w<p;w++)if(null==r||r[w])for(C=0;C<y;C++)if(0!==S[C])if(b=a[C][w],k=(b-l[C])/S[C],x=1,h[C]>1&&(x-=(1/v)**(k*e[C])),b<u[C]&&b>l[C]){const e=x*v*k**(1/h[C])+c;m[C][w]=P?Math.floor(e):A?Math.round(e):e}else b>=u[C]?m[C][w]=f:m[C][w]=c;else m[C][w]=c}else for(w=0;w<p;w++)if(null==r||r[w])for(C=0;C<y;C++)if(b=a[C][w],b<u[C]&&b>l[C]){const e=(b-l[C])*T[C]+c;m[C][w]=P?Math.floor(e):A?Math.round(e):e}else b>=u[C]?m[C][w]=f:m[C][w]=c;const M=new n.Z({width:i,height:s,mask:r,pixels:m,pixelType:d});return M.updateStatistics(),M}},63342:(e,t,r)=>{r.d(t,{M_:()=>h,TK:()=>s,Ub:()=>l,vC:()=>a,yg:()=>u});var o=r(70586),n=r(5847),i=r(55914);function s(e){let{altitude:t,azimuth:r}=e;const{hillshadeType:o,pixelSizePower:n=1,pixelSizeFactor:i=1,scalingType:s,isGCS:a,resolution:l}=e,u="multi-directional"===o?2*e.zFactor:e.zFactor,{x:h,y:c}=l;let f=u/(8*h),p=u/(8*c);if(a&&u>.001&&(f/=111e3,p/=111e3),"adjusted"===s)if(a){const e=111e3*h,t=111e3*c;f=(u+e**n*i)/(8*e),p=(u+t**n*i)/(8*t)}else f=(u+h**n*i)/(8*h),p=(u+c**n*i)/(8*c);let d=(90-t)*Math.PI/180,g=Math.cos(d),m=(360-r+90)*Math.PI/180,y=Math.sin(d)*Math.cos(m),C=Math.sin(d)*Math.sin(m);const w=[315,270,225,360,180,0],b=[60,60,60,60,60,90],x=new Float32Array([3,5,3,2,1,4]),k=x.reduce(((e,t)=>e+t)),v=x.map((e=>e/k)),T="multi-directional"===o?w.length:1,S=new Float32Array(6),A=new Float32Array(6),I=new Float32Array(6);if("multi-directional"===o)for(let e=0;e<T;e++)t=b[e],r=w[e],d=(90-t)*Math.PI/180,g=Math.cos(d),m=(360-r+90)*Math.PI/180,y=Math.sin(d)*Math.cos(m),C=Math.sin(d)*Math.sin(m),S[e]=g,A[e]=y,I[e]=C;else S.fill(g),A.fill(y),I.fill(C);return{resolution:l,factor:[f,p],sinZcosA:y,sinZsinA:C,cosZ:g,sinZcosAs:A,sinZsinAs:I,cosZs:S,weights:v,hillshadeType:["traditional","multi-directional"].indexOf(o)}}function a(e,t){if(!(0,i.nk)(e))return e;const{width:r,height:a,mask:l}=e,u=new Uint8Array(r*a);let h=1;if((0,o.pC)(l)){for(let e=0;e<l.length;e++)if(l[e]){h=l[e];break}u.set(l)}const{factor:c,sinZcosA:f,sinZsinA:p,cosZ:d,sinZcosAs:g,sinZsinAs:m,cosZs:y,weights:C}=s(t),[w,b]=c,{hillshadeType:x}=t,k=e.pixels[0],v=new Uint8Array(r*a);let T,S,A,I,P,M,E,D;const R=(0,o.pC)(l);for(let e=1;e<a-1;e++){const t=e*r;for(let e=1;e<r-1;e++){if(l&&!l[t+e]){v[t+e]=0;continue}let o=8;if(R&&(o=(l[t-r+e-1]+l[t-r+e]+l[t-r+e+1]+l[t+e-1]+l[t+e+1]+l[t+r+e-1]+l[t+r+e]+l[t+r+e+1])/h,o<7)){v[t+e]=0,u[t+e]=0;continue}l&&7===o?(T=l[t-r+e-1]?k[t-r+e-1]:k[t+e],S=l[t-r+e]?k[t-r+e]:k[t+e],A=l[t-r+e+1]?k[t-r+e+1]:k[t+e],I=l[t+e-1]?k[t+e-1]:k[t+e],P=l[t+e+1]?k[t+e+1]:k[t+e],M=l[t+r+e-1]?k[t+r+e-1]:k[t+e],E=l[t+r+e]?k[t+r+e]:k[t+e],D=l[t+r+e+1]?k[t+r+e+1]:k[t+e]):(T=k[t-r+e-1],S=k[t-r+e],A=k[t-r+e+1],I=k[t+e-1],P=k[t+e+1],M=k[t+r+e-1],E=k[t+r+e],D=k[t+r+e+1]);const n=(A+P+P+D-(T+I+I+M))*w,i=(M+E+E+D-(T+S+S+A))*b,s=Math.sqrt(1+n*n+i*i);let a=0;if("traditional"===x){let e=255*(d+p*i-f*n)/s;e<0&&(e=0),a=e}else{const e=m.length;for(let t=0;t<e;t++){let e=255*(y[t]+m[t]*i-g[t]*n)/s;e<0&&(e=0),a+=e*C[t]}}v[t+e]=255&a}}for(let e=0;e<a;e++)v[e*r]=v[e*r+1],v[(e+1)*r-1]=v[(e+1)*r-2];for(let e=1;e<r-1;e++)v[e]=v[e+r],v[e+(a-1)*r]=v[e+(a-2)*r];return new n.Z({width:r,height:a,pixels:[v],mask:l?u:null,pixelType:"u8",validPixelCount:e.validPixelCount,statistics:[{minValue:0,maxValue:255}]})}function l(e,t,r,o){if(!(0,i.nk)(e)||!(0,i.nk)(t))return;const{min:n,max:s}=o,a=e.pixels[0],{pixels:l,mask:u}=t,h=l[0],c=255.00001/(s-n),f=new Uint8ClampedArray(h.length),p=new Uint8ClampedArray(h.length),d=new Uint8ClampedArray(h.length),g=r.length-1;for(let e=0;e<h.length;e++){if(u&&0===u[e])continue;const t=Math.floor((h[e]-n)*c),[o,i]=r[t<0?0:t>g?g:t],s=a[e],l=s*i,m=l*(1-Math.abs(o%2-1)),y=s-l;switch(Math.floor(o)){case 0:f[e]=l+y,p[e]=m+y,d[e]=y;break;case 1:f[e]=m+y,p[e]=l+y,d[e]=y;break;case 2:f[e]=y,p[e]=l+y,d[e]=m+y;break;case 3:f[e]=y,p[e]=m+y,d[e]=l+y;break;case 4:f[e]=m+y,p[e]=y,d[e]=l+y;break;case 5:case 6:f[e]=l+y,p[e]=y,d[e]=m+y}}e.pixels=[f,p,d],e.updateStatistics()}function u(e,t){if(!(0,i.nk)(e))return e;const r=t.zFactor,s=t.pixelSizePower??1,a=t.pixelSizeFactor??1,l=t.slopeType,u=t.isGCS,{width:h,height:c,mask:f}=e,p=e.pixels[0],d=new Uint8Array(h*c);let g=1;if((0,o.pC)(f)){for(let e=0;e<f.length;e++)if(f[e]){g=f[e];break}d.set(f)}const m=new Float32Array(h*c),{x:y,y:C}=t.resolution;let w,b,x,k,v,T,S,A,I=r/(8*y),P=r/(8*C);u&&Math.abs(r-1)<1e-4&&(I/=111e3,P/=111e3),"adjusted"===l&&(I=(r+y**s*a)/(8*y),P=(r+C**s*a)/(8*C));const M=(0,o.pC)(f);for(let e=1;e<c-1;e++){const t=e*h;for(let e=1;e<h-1;e++){if(f&&!f[t+e]){m[t+e]=0;continue}let r=0;if(M&&(r=(f[t-h+e-1]+f[t-h+e]+f[t-h+e+1]+f[t+e-1]+f[t+e+1]+f[t+h+e-1]+f[t+h+e]+f[t+h+e+1])/g,r<7)){m[t+e]=0,d[t+e]=0;continue}f&&7===r?(w=f[t-h+e-1]?p[t-h+e-1]:p[t+e],b=f[t-h+e]?p[t-h+e]:p[t+e],x=f[t-h+e+1]?p[t-h+e+1]:p[t+e],k=f[t+e-1]?p[t+e-1]:p[t+e],v=f[t+e+1]?p[t+e+1]:p[t+e],T=f[t+h+e-1]?p[t+h+e-1]:p[t+e],S=f[t+h+e]?p[t+h+e]:p[t+e],A=f[t+h+e+1]?p[t+h+e+1]:p[t+e]):(w=p[t-h+e-1],b=p[t-h+e],x=p[t-h+e+1],k=p[t+e-1],v=p[t+e+1],T=p[t+h+e-1],S=p[t+h+e],A=p[t+h+e+1]);const o=(x+v+v+A-(w+k+k+T))*I,n=(T+S+S+A-(w+b+b+x))*P,i=Math.sqrt(o*o+n*n);m[t+e]="percent-rise"===l?100*i:57.2957795*Math.atan(i)}}for(let e=0;e<c;e++)m[e*h]=m[e*h+1],m[(e+1)*h-1]=m[(e+1)*h-2];for(let e=1;e<h-1;e++)m[e]=m[e+h],m[e+(c-1)*h]=m[e+(c-2)*h];const E=new n.Z({width:h,height:c,pixels:[m],mask:f?d:null,pixelType:"f32",validPixelCount:e.validPixelCount});return E.updateStatistics(),E}function h(e,t={}){if(!(0,i.nk)(e))return e;const{width:r,height:s,mask:a}=e,l=e.pixels[0],u=new Uint8Array(r*s);(0,o.pC)(a)&&u.set(a);const h=new Float32Array(r*s),{resolution:c}=t,f=c?1/c.x:1,p=c?1/c.y:1;let d,g,m,y,C,w,b,x;const k=(0,o.pC)(a);for(let e=1;e<s-1;e++){const t=e*r;for(let e=1;e<r-1;e++){if(a&&!a[t+e]){h[t+e]=0;continue}let o=0;if(k&&(o=a[t-r+e-1]+a[t-r+e]+a[t-r+e+1]+a[t+e-1]+a[t+e+1]+a[t+r+e-1]+a[t+r+e]+a[t+r+e+1],o<7)){h[t+e]=0,u[t+e]=0;continue}a&&7===o?(d=a[t-r+e-1]?l[t-r+e-1]:l[t+e],g=a[t-r+e]?l[t-r+e]:l[t+e],m=a[t-r+e+1]?l[t-r+e+1]:l[t+e],y=a[t+e-1]?l[t+e-1]:l[t+e],C=a[t+e+1]?l[t+e+1]:l[t+e],w=a[t+r+e-1]?l[t+r+e-1]:l[t+e],b=a[t+r+e]?l[t+r+e]:l[t+e],x=a[t+r+e+1]?l[t+r+e+1]:l[t+e]):(d=l[t-r+e-1],g=l[t-r+e],m=l[t-r+e+1],y=l[t+e-1],C=l[t+e+1],w=l[t+r+e-1],b=l[t+r+e],x=l[t+r+e+1]);const n=(m+C+C+x-(d+y+y+w))*f,i=(w+b+b+x-(d+g+g+m))*p;let s=-1;0===n&&0===i||(s=90-57.29578*Math.atan2(i,-n),s<0&&(s+=360),360===s?s=0:s>360&&(s%=360)),h[t+e]=s}}for(let e=0;e<s;e++)h[e*r]=h[e*r+1],h[(e+1)*r-1]=h[(e+1)*r-2];for(let e=1;e<r-1;e++)h[e]=h[e+r],h[e+(s-1)*r]=h[e+(s-2)*r];return new n.Z({width:r,height:s,pixels:[h],mask:a?u:null,pixelType:"f32",validPixelCount:e.validPixelCount,statistics:[{minValue:0,maxValue:360}]})}},80676:(e,t,r)=>{r.d(t,{KC:()=>g,NL:()=>u,QI:()=>m,Tg:()=>f,Yx:()=>h,xQ:()=>d});var o=r(35454),n=r(70586),i=r(5847),s=r(55914);const a=new Map;a.set("meter-per-second",1),a.set("kilometer-per-hour",.277778),a.set("knots",.514444),a.set("feet-per-second",.3048),a.set("mile-per-hour",.44704);const l=180/Math.PI,u=new o.X({esriMetersPerSecond:"meter-per-second",esriKilometersPerHour:"kilometer-per-hour",esriKnots:"knots",esriFeetPerSecond:"feet-per-second",esriMilesPerHour:"mile-per-hour"});function h(e,t){return a.get(e)/a.get(t)||1}function c(e){return(450-e)%360}function f(e,t="geographic"){const[r,o]=e,n=Math.sqrt(r*r+o*o);let i=Math.atan2(o,r)*l;return i=(360+i)%360,"geographic"===t&&(i=c(i)),[n,i]}function p(e,t="geographic"){let r=e[1];"geographic"===t&&(r=c(r)),r%=360;const o=e[0];return[o*Math.cos(r/l),o*Math.sin(r/l)]}function d(e,t,r,o="geographic"){if(!(0,s.nk)(e)||(0,n.Wi)(r))return e;const i="vector-magdir"===t?e.clone():(0,n.Wg)(g(e,t)),a=i.pixels[1];for(let e=0;e<a.length;e++)a[e]="geographic"===o?(a[e]+r[e]+270)%360:(a[e]+360-r[e])%360;return"vector-magdir"===t?i:g(i,"vector-magdir")}function g(e,t,r="geographic",o=1){if(!(0,s.nk)(e))return e;const{pixels:n,width:a,height:l}=e,u=a*l,h=n[0],c=n[1],d=e.pixelType.startsWith("f")?e.pixelType:"f32",g=i.Z.createEmptyBand(d,u),m=i.Z.createEmptyBand(d,u);let y=0;for(let e=0;e<l;e++)for(let e=0;e<a;e++)"vector-uv"===t?([g[y],m[y]]=f([h[y],c[y]],r),g[y]*=o):([g[y],m[y]]=p([h[y],c[y]],r),g[y]*=o,m[y]*=o),y++;const C=new i.Z({pixelType:d,width:e.width,height:e.height,mask:e.mask,validPixelCount:e.validPixelCount,maskIsAlpha:e.maskIsAlpha,pixels:[g,m]});return C.updateStatistics(),C}function m(e,t,r=1){if(1===r||!(0,s.nk)(e))return e;const o=e.clone(),{pixels:n,width:i,height:a}=o,l=n[0],u=n[1];let h=0;for(let e=0;e<a;e++)for(let e=0;e<i;e++)"vector-uv"===t?(l[h]*=r,u[h]*=r):l[h]*=r,h++;return o.updateStatistics(),o}function y(e,t,r,o=1){const n=Math.sqrt(e*e+t*t)/o,i=(2*Math.PI+Math.atan2(t,e))%(2*Math.PI);return[n,(2*Math.PI+i-r)%(2*Math.PI)]}!function(e=0,t=0,r=Math.PI,o=!0){o&&(r=(2*Math.PI-r)%(2*Math.PI));const n=o?-1:1,i=13*n,s=-7*n,a=-2*n,l=-16*n,u=21.75,[h,c]=y(0,t+i,r,u),[f,p]=y(e-5.5,t+s,r,u),[d,g]=y(e+5.5,t+s,r,u),[m,C]=y(e-1.5,t+a,r,u),[w,b]=y(e+1.5,t+a,r,u),[x,k]=y(e-1.5,t+l,r,u),[v,T]=y(e+1.5,t+l,r,u)}(0,0,0)},72758:(e,t,r)=>{r.d(t,{Z:()=>y});var o=r(43697),n=r(3892),i=r(96674),s=r(92604),a=r(70586),l=r(5600),u=(r(75215),r(67676),r(52011)),h=r(48526),c=r(55914),f=r(15612),p=r(63342),d=r(93010);let g=class extends i.wq{constructor(e){super(e)}bind(){const{rendererJSON:e}=this;if(!e)return{success:!1};let t;switch(this.lookup={rendererJSON:{}},e.type){case"uniqueValue":t=this._updateUVRenderer(e);break;case"rasterColormap":t=this._updateColormapRenderer(e);break;case"rasterStretch":t=this._updateStretchRenderer(e);break;case"classBreaks":t=this._updateClassBreaksRenderer(e);break;case"rasterShadedRelief":t=this._updateShadedReliefRenderer(e);break;case"vectorField":t=this._updateVectorFieldRenderer();break;case"flowRenderer":t=this._updateFlowRenderer()}return t}symbolize(e){let t=e&&e.pixelBlock;if(!m(t))return t;if(e.simpleStretchParams&&"rasterStretch"===this.rendererJSON.type)return this.simpleStretch(t,e.simpleStretchParams);try{let r;switch(t.pixels.length>3&&(t=(0,c.qF)(t,[0,1,2])),this.rendererJSON.type){case"uniqueValue":case"rasterColormap":r=this._symbolizeColormap(t);break;case"classBreaks":r=this._symbolizeClassBreaks(t);break;case"rasterStretch":r=this._symbolizeStretch(t,e.bandIds);break;case"rasterShadedRelief":{const o=e.extent,n=o.spatialReference.isGeographic,i={x:(o.xmax-o.xmin)/t.width,y:(o.ymax-o.ymin)/t.height};r=this._symbolizeShadedRelief(t,{isGCS:n,resolution:i});break}}return r}catch(e){return s.Z.getLogger(this.declaredClass).error("symbolize",e.message),t}}simpleStretch(e,t){if(!m(e))return e;try{return e.pixels.length>3&&(e=(0,c.qF)(e,[0,1,2])),(0,f.dy)(e,{...t,isRenderer:!0})}catch(t){return s.Z.getLogger(this.declaredClass).error("symbolize",t.message),e}}generateWebGLParameters(e){if(["uniqueValue","rasterColormap","classBreaks"].includes(this.rendererJSON.type)){const{indexedColormap:e,offset:t}=this.lookup.colormapLut||{};return{colormap:e,colormapOffset:t,type:"lut"}}const{pixelBlock:t,isGCS:r,resolution:o,bandIds:n}=e,{rendererJSON:i}=this;return"rasterStretch"===i.type?this._generateStretchWebGLParams(t,i,n):"rasterShadedRelief"===i.type?this._generateShadedReliefWebGLParams(i,r,o??void 0):"vectorField"===i.type?this._generateVectorFieldWebGLParams(i):null}_isLUTChanged(e){if(!this.lookup||!this.lookup.rendererJSON)return!0;if("colorRamp"in this.rendererJSON){const t=this.rendererJSON.colorRamp;return e?JSON.stringify(t)!==JSON.stringify(this.lookup.rendererJSON.colorRamp):(this.rendererJSON,this.lookup.rendererJSON,JSON.stringify(this.rendererJSON)!==JSON.stringify(this.lookup.rendererJSON))}return JSON.stringify(this.rendererJSON)!==JSON.stringify(this.lookup.rendererJSON)}_symbolizeColormap(e){return this._isLUTChanged()&&!this.bind().success?e:(0,c.SJ)(e,this.lookup.colormapLut)}_symbolizeClassBreaks(e){const{canUseIndexedLUT:t}=this._analyzeClassBreaks(this.rendererJSON);return this._isLUTChanged()&&!this.bind().success?e:t?(0,c.SJ)(e,this.lookup.colormapLut):(0,c.zp)(e,this.lookup.remapLut??[])}_symbolizeStretch(e,t){const{rasterInfo:r}=this,{pixelType:o,bandCount:n}=r,i=this.rendererJSON,s=["u8","u16","s8","s16"].includes(o);let l,u;const{dra:h}=i,{gamma:p}=this.lookup;if("histogramEqualization"===i.stretchType){const o=h?null:this.lookup?.histogramLut,n=(0,f.AV)(i,{rasterInfo:r,pixelBlock:e,bandIds:t,returnHistogramLut:!o}),s=(0,f.dy)(e,{...n,gamma:p,isRenderer:!0});u=(0,c.XV)(s,{lut:h?n.histogramLut:o,offset:0})}else if(s){if(h){const n=(0,f.AV)(i,{rasterInfo:r,pixelBlock:e,bandIds:t});l=(0,f.hE)({pixelType:o,...n,gamma:p,rounding:"floor"})}else if(this._isLUTChanged()){if(!this.bind().success)return e;l=this.lookup?this.lookup.stretchLut:null}else l=this.lookup?this.lookup.stretchLut:null;if(!l)return e;n>1&&t?.length===(0,a.Wg)(e)?.pixels.length&&l?.lut.length===n&&(l={lut:t.map((e=>l.lut[e])),offset:l.offset}),u=(0,c.XV)(e,l)}else{const o=(0,f.AV)(i,{rasterInfo:r,pixelBlock:e,bandIds:t});u=(0,f.dy)(e,{...o,gamma:p,isRenderer:!0})}if(i.colorRamp){if(this._isLUTChanged(!0)&&!this.bind().success)return e;u=(0,c.SJ)(u,this.lookup?.colormapLut)}return u}_symbolizeShadedRelief(e,t){const r=this.rendererJSON,o={...r,...t},n=(0,p.vC)(e,o);if(!r.colorRamp)return n;let i;if(this._isLUTChanged(!0)){if(!this.bind().success)return n;i=this.lookup?this.lookup.hsvMap:null}else i=this.lookup?this.lookup.hsvMap:null;if(!i)return n;const s=(0,a.Wg)(this.rasterInfo.statistics)?.[0]??{min:0,max:8e3};return(0,p.Ub)(n,e,i,s),n}_isVectorFieldData(){const{bandCount:e,dataType:t}=this.rasterInfo;return 2===e&&("vector-magdir"===t||"vector-uv"===t)}_updateVectorFieldRenderer(){return this._isVectorFieldData()?{success:!0}:{success:!1,error:`Unsupported data type "${this.rasterInfo.dataType}"; VectorFieldRenderer only supports "vector-magdir" and "vector-uv".`}}_updateFlowRenderer(){return this._isVectorFieldData()?{success:!0}:{success:!1,error:`Unsupported data type "${this.rasterInfo.dataType}"; FlowRenderer only supports "vector-magdir" and "vector-uv".`}}_updateUVRenderer(e){const{bandCount:t,attributeTable:r,pixelType:o}=this.rasterInfo,n=e.field1;if(!n)return{success:!1,error:"Unsupported renderer; missing UniqueValueRenderer.field."};const i=e.defaultSymbol,s=1===t&&["u8","s8"].includes(o);if(!function(e,t){const{attributeTable:r,bandCount:o}=e;return!((0,a.Wi)(r)||o>1||t&&null==r.fields.find((e=>e.name.toLowerCase()===t.toLowerCase())))}(this.rasterInfo,n)&&!s)return{success:!1,error:"Unsupported data; UniqueValueRenderer is only supported on single band data with a valid raster attribute table."};const l=[];if((0,a.pC)(r)){const t=r.fields.find((e=>"value"===e.name.toLowerCase()));if(!t)return{success:!1,error:"Unsupported data; the data's raster attribute table does not have a value field."};r.features.forEach((r=>{const o=e.uniqueValueInfos?.find((e=>String(e.value)===String(r.attributes[n]))),s=o?.symbol?.color;s?l.push([r.attributes[t.name]].concat(s)):i&&l.push([r.attributes[t.name]].concat(i.color))}))}else{if("value"!==n.toLowerCase())return{success:!1,error:'Unsupported renderer; UniqueValueRenderer.field must be "Value" when raster attribute table is not availalbe.'};e.uniqueValueInfos?.forEach((e=>{const t=e?.symbol?.color;t?l.push([parseInt(""+e.value,10)].concat(t)):i&&l.push([parseInt(""+e.value,10)].concat(i?.color))}))}if(0===l.length)return{success:!1,error:"Invalid UniqueValueRenderer. Cannot find matching records in the raster attribute table."};const u=(0,c.Pz)({colormap:l});return this.lookup={rendererJSON:e,colormapLut:u},this.canRenderInWebGL=!0,{success:!0}}_updateColormapRenderer(e){if(!function(e){const{bandCount:t,colormap:r}=e;return(0,a.pC)(r)&&r.length>0&&1===t}(this.rasterInfo))return{success:!1,error:"Unsupported data; the data source does not have a colormap."};const t=e.colormapInfos.map((e=>[e.value].concat(e.color))).sort(((e,t)=>e[0]-t[0]));if(!t||0===t.length)return{success:!1,error:"Unsupported renderer; ColormapRenderer must have meaningful colormapInfos."};const r=(0,c.Pz)({colormap:t});return this.lookup={rendererJSON:e,colormapLut:r},this.canRenderInWebGL=!0,{success:!0}}_updateShadedReliefRenderer(e){if(!function(e){const{bandCount:t,dataType:r,pixelType:o}=e;return"elevation"===r||"generic"===r&&1===t&&("s16"===o||"f32"===o||"f64"===o)}(this.rasterInfo))return{success:!1,error:`Unsupported data type "${this.rasterInfo.dataType}"; ShadedReliefRenderer only supports "elevation", or single band float/s16 data.`};if(e.colorRamp){const t=(0,d.Jw)(e.colorRamp,{interpolateAlpha:!0}),r=(0,c.Pz)({colormap:t}),o=[],i=r.indexedColormap;for(let e=0;e<i.length;e+=4){const t=(0,n._Y)({r:i[e],g:i[e+1],b:i[e+2]});o.push([t.h/60,t.s/100,255*t.v/100])}this.lookup={rendererJSON:e,colormapLut:r,hsvMap:o}}else this.lookup=null;return this.canRenderInWebGL=!0,{success:!0}}_analyzeClassBreaks(e){const{attributeTable:t,pixelType:r}=this.rasterInfo,o=(0,a.pC)(t)?t.fields.find((e=>"value"===e.name.toLowerCase())):null,n=(0,a.pC)(t)?t.fields.find((t=>t.name.toLowerCase()===e.field.toLowerCase())):null,i=null!=o&&null!==n;return{canUseIndexedLUT:["u8","u16","s8","s16"].includes(r)||i,tableValueField:o,tableBreakField:n}}_updateClassBreaksRenderer(e){const{attributeTable:t}=this.rasterInfo,{canUseIndexedLUT:r,tableValueField:o,tableBreakField:n}=this._analyzeClassBreaks(e),i=e.classBreakInfos;if(!i?.length)return{success:!1,error:"Unsupported renderer; missing or invalid ClassBreaksRenderer.classBreakInfos."};const s=i.sort(((e,t)=>e.classMaxValue-t.classMaxValue)),l=s[s.length-1];let u=e.minValue;if(!r){const t=[];for(let e=0;e<s.length;e++)t.push({value:s[e].classMinValue??u,mappedColor:s[e].symbol.color}),u=s[e].classMaxValue;return t.push({value:l.classMaxValue,mappedColor:l.symbol.color}),this.lookup={rendererJSON:e,remapLut:t},this.canRenderInWebGL=!1,{success:!0}}const h=[];if((0,a.pC)(t)&&null!=o&&null!==n&&o!==n){const r=o.name,i=n.name,a=s[s.length-1],{classMaxValue:l}=a;u=e.minValue;for(const e of t.features){const t=e.attributes[r],o=e.attributes[i],n=o===l?a:o<u?null:s.find((({classMaxValue:e})=>e>o));n&&h.push([t].concat(n.symbol.color))}}else{u=Math.floor(e.minValue);for(let e=0;e<s.length;e++){const t=s[e];for(let e=u;e<t.classMaxValue;e++)h.push([e].concat(t.symbol.color));u=Math.ceil(t.classMaxValue)}l.classMaxValue===u&&h.push([l.classMaxValue].concat(l.symbol.color))}const f=(0,c.Pz)({colormap:h,fillUnspecified:!1});return this.lookup={rendererJSON:e,colormapLut:f},this.canRenderInWebGL=!0,{success:!0}}_isHistogramRequired(e){return"percentClip"===e||"histogramEqualization"===e}_isValidRasterStatistics(e){return(0,a.pC)(e)&&e.length>0&&null!=e[0].min&&null!=e[0].max}_updateStretchRenderer(e){let{stretchType:t,dra:r}=e;if(!("none"===t||e.statistics?.length||this._isValidRasterStatistics(this.rasterInfo.statistics)||r))return{success:!1,error:"Unsupported renderer; StretchRenderer.statistics is required when dynamic range adjustment is not used."};const o=(0,a.Wg)(e.histograms||this.rasterInfo.histograms);!this._isHistogramRequired(e.stretchType)||o?.length||r||(t="minMax");const{computeGamma:n,useGamma:i,colorRamp:s}=e;let{gamma:l}=e;if(i&&n&&!l?.length){const t=e.statistics?.length?e.statistics:(0,a.Wg)(this.rasterInfo.statistics);l=(0,f.ZF)(this.rasterInfo.pixelType,t)}const u=this.rasterInfo.pixelType,h=!r&&["u8","u16","s8","s16"].includes(u);if("histogramEqualization"===t){const t=o.map((e=>(0,f.oc)(e)));this.lookup={rendererJSON:e,histogramLut:t}}else if(h){const t=(0,f.AV)(e,{rasterInfo:this.rasterInfo}),r=(0,f.hE)({pixelType:u,...t,gamma:i?l:null,rounding:"floor"});this.lookup={rendererJSON:e,stretchLut:r}}if(s){const t=(0,d.Jw)(s,{interpolateAlpha:!0});this.lookup||(this.lookup={rendererJSON:e}),this.lookup.colormapLut=(0,c.Pz)({colormap:t}),this.lookup.rendererJSON=e}return this.lookup.gamma=i&&l?.length?l:null,this.canRenderInWebGL=!0,{success:!0}}_generateStretchWebGLParams(e,t,r){let o=null,n=null;const i=this.lookup&&this.lookup.colormapLut;t.colorRamp&&i&&(o=i.indexedColormap,n=i.offset),"histogramEqualization"===t.stretchType&&(t={...t,stretchType:"minMax"});const{gamma:s}=this.lookup,l=!!(t.useGamma&&s&&s.some((e=>1!==e))),{minCutOff:u,maxCutOff:h,outMin:c,outMax:p}=(0,f.AV)(t,{rasterInfo:this.rasterInfo,pixelBlock:e,bandIds:r});let d=0;(0,a.pC)(e)&&(d=e.getPlaneCount(),2===d&&((e=e.clone()).statistics=[e.statistics[0]],e.pixels=[e.pixels[0]]));const{bandCount:g}=this.rasterInfo,m=Math.min(3,r?.length||d||g,g),y=new Float32Array(m),C=o||l?1:255;let w;for(w=0;w<u.length;w++)y[w]=h[w]===u[w]?0:(p-c)/(h[w]-u[w])/C;const b=new Float32Array(m);if(l&&s)for(w=0;w<m;w++)s[w]>1?s[w]>2?b[w]=6.5+(s[w]-2)**2.5:b[w]=6.5+100*(2-s[w])**4:b[w]=1;return{bandCount:m,outMin:c/C,outMax:p/C,minCutOff:u,maxCutOff:h,factor:y,useGamma:l,gamma:l?s:[1,1,1],gammaCorrection:l?b:[1,1,1],colormap:o,colormapOffset:n,stretchType:t.stretchType,type:"stretch"}}_generateShadedReliefWebGLParams(e,t=!1,r={x:0,y:0}){let o=null,n=null;const i=this.lookup&&this.lookup.colormapLut;e.colorRamp&&i&&(o=i.indexedColormap,n=i.offset);const s={...e,isGCS:t,resolution:r},l=(0,p.TK)(s),u=(0,a.Wg)(this.rasterInfo.statistics)?.[0];return{...l,minValue:u?.min??0,maxValue:u?.max??8e3,hillshadeType:"traditional"===e.hillshadeType?0:1,type:"hillshade",colormap:o,colormapOffset:n}}_generateVectorFieldWebGLParams(e){const{style:t,inputUnit:r,outputUnit:o,visualVariables:n,symbolTileSize:i,flowRepresentation:s}=e;let l;const u=this.rasterInfo.statistics?.[0].min??0,h=this.rasterInfo.statistics?.[0].max??50,c=n?.find((e=>"sizeInfo"===e.type))??{type:"sizeInfo",field:"Magnitude",maxDataValue:h,maxSize:.8*i,minDataValue:u,minSize:.2*i},f=c.minDataValue??u,p=c.maxDataValue??h,d=(0,a.pC)(c.maxSize)&&(0,a.pC)(c.minSize)?[c.minSize/i,c.maxSize/i]:[.2,.8];if("wind_speed"===t){const e=(d[0]+d[1])/2;d[0]=d[1]=e}const g=(0,a.pC)(f)&&(0,a.pC)(p)?[f,p]:null;if("classified_arrow"===t)if((0,a.pC)(f)&&(0,a.pC)(p)&&(0,a.pC)(c)){l=[];const e=(c.maxDataValue-c.minDataValue)/5;for(let t=0;t<6;t++)l.push(c.minDataValue+e*t)}else l=[0,1e-6,3.5,7,10.5,14];const m="flow_to"===s==("ocean_current_kn"===t||"ocean_current_m"===t)?0:Math.PI,y=n?.find((e=>"rotationInfo"===e.type));return{breakValues:l,dataRange:g,inputUnit:r,outputUnit:o,symbolTileSize:i,symbolPercentRange:d,style:t||"single_arrow",rotation:m,rotationType:this.rasterInfo.storageInfo?.tileInfo&&"vector-uv"===this.rasterInfo.dataType?"geographic":y?.rotationType||e.rotationType,type:"vectorField"}}};function m(e){return(0,c.nk)(e)&&0!==e.validPixelCount}(0,o._)([(0,l.Cb)({json:{write:!0}})],g.prototype,"rendererJSON",void 0),(0,o._)([(0,l.Cb)({type:h.Z,json:{write:!0}})],g.prototype,"rasterInfo",void 0),(0,o._)([(0,l.Cb)({json:{write:!0}})],g.prototype,"lookup",void 0),(0,o._)([(0,l.Cb)()],g.prototype,"canRenderInWebGL",void 0),g=(0,o._)([(0,u.j)("esri.renderers.support.RasterSymbolizer")],g);const y=g},93010:(e,t,r)=>{r.d(t,{B4:()=>f,H0:()=>u,Jw:()=>m,Ms:()=>a,Oi:()=>s,Uh:()=>c,XL:()=>g,io:()=>b,pM:()=>y});var o=r(22303),n=r(3892),i=r(35454);const s=["random","ndvi","ndvi2","ndvi3","elevation","gray","hillshade"],a=[{id:"aspect_predefined",type:"multipart",colorRamps:[{fromColor:[190,190,190],toColor:[255,45,8]},{fromColor:[255,45,8],toColor:[255,181,61]},{fromColor:[255,181,61],toColor:[255,254,52]},{fromColor:[255,254,52],toColor:[0,251,50]},{fromColor:[0,251,50],toColor:[255,254,52]},{fromColor:[0,253,255],toColor:[0,181,255]},{fromColor:[0,181,255],toColor:[26,35,253]},{fromColor:[26,35,253],toColor:[255,57,251]},{fromColor:[255,57,251],toColor:[255,45,8]}]},{id:"blackToWhite_predefined",fromColor:[0,0,0],toColor:[255,255,255]},{id:"blueBright_predefined",fromColor:[204,204,255],toColor:[0,0,224]},{id:"blueLightToDark_predefined",fromColor:[211,229,232],toColor:[46,100,140]},{id:"blueGreenBright_predefined",fromColor:[203,245,234],toColor:[48,207,146]},{id:"blueGreenLightToDark_predefined",fromColor:[216,242,237],toColor:[21,79,74]},{id:"brownLightToDark_predefined",fromColor:[240,236,170],toColor:[102,72,48]},{id:"brownToBlueGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[156,85,31],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[33,130,145]}]},{id:"brownToBlueGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[110,70,45],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[48,100,102]}]},{id:"coefficientBias_predefined",fromColor:[214,214,255],toColor:[0,57,148]},{id:"coldToHotDiverging_predefined",type:"multipart",colorRamps:[{fromColor:[69,117,181],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[214,47,39]}]},{id:"conditionNumber_predefined",type:"multipart",colorRamps:[{fromColor:[0,97,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,34,0]}]},{id:"cyanToPurple_predefined",type:"multipart",colorRamps:[{fromColor:[0,245,245],toColor:[0,0,245]},{fromColor:[0,0,245],toColor:[245,0,245]}]},{id:"cyanLightToBlueDark_predefined",type:"multipart",colorRamps:[{fromColor:[182,237,240],toColor:[31,131,224]},{fromColor:[31,131,224],toColor:[9,9,145]}]},{id:"distance_predefined",fromColor:[255,200,0],toColor:[0,0,255]},{id:"elevation1_predefined",type:"multipart",colorRamps:[{fromColor:[175,240,233],toColor:[255,255,179]},{fromColor:[255,255,179],toColor:[0,128,64]},{fromColor:[0,128,64],toColor:[252,186,3]},{fromColor:[252,186,3],toColor:[128,0,0]},{fromColor:[120,0,0],toColor:[105,48,13]},{fromColor:[105,48,13],toColor:[171,171,171]},{fromColor:[171,171,171],toColor:[255,252,255]}]},{id:"elevation2_predefined",type:"multipart",colorRamps:[{fromColor:[118,219,211],toColor:[255,255,199]},{fromColor:[255,255,199],toColor:[255,255,128]},{fromColor:[255,255,128],toColor:[217,194,121]},{fromColor:[217,194,121],toColor:[135,96,38]},{fromColor:[135,96,38],toColor:[150,150,181]},{fromColor:[150,150,181],toColor:[181,150,181]},{fromColor:[181,150,181],toColor:[255,252,255]}]},{id:"errors_predefined",fromColor:[255,235,214],toColor:[196,10,10]},{id:"grayLightToDark_predefined",fromColor:[219,219,219],toColor:[69,69,69]},{id:"greenBright_predefined",fromColor:[204,255,204],toColor:[14,204,14]},{id:"greenLightToDark_predefined",fromColor:[220,245,233],toColor:[34,102,51]},{id:"greenToBlue_predefined",type:"multipart",colorRamps:[{fromColor:[32,204,16],toColor:[0,242,242]},{fromColor:[0,242,242],toColor:[2,33,227]}]},{id:"orangeBright_predefined",fromColor:[255,235,204],toColor:[240,118,5]},{id:"orangeLightToDark_predefined",fromColor:[250,233,212],toColor:[171,65,36]},{id:"partialSpectrum_predefined",type:"multipart",colorRamps:[{fromColor:[242,241,162],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,0,0]},{fromColor:[252,3,69],toColor:[176,7,237]},{fromColor:[176,7,237],toColor:[2,29,173]}]},{id:"partialSpectrum1Diverging_predefined",type:"multipart",colorRamps:[{fromColor:[135,38,38],toColor:[240,149,12]},{fromColor:[240,149,12],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[74,80,181]},{fromColor:[74,80,181],toColor:[39,32,122]}]},{id:"partialSpectrum2Diverging_predefined",type:"multipart",colorRamps:[{fromColor:[115,77,42],toColor:[201,137,52]},{fromColor:[201,137,52],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[91,63,176]},{fromColor:[91,63,176],toColor:[81,13,97]}]},{id:"pinkToYellowGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[158,30,113],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[99,110,45]}]},{id:"pinkToYellowGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[97,47,73],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[22,59,15]}]},{id:"precipitation_predefined",type:"multipart",colorRamps:[{fromColor:[194,82,60],toColor:[237,161,19]},{fromColor:[237,161,19],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[0,219,0]},{fromColor:[0,219,0],toColor:[32,153,143]},{fromColor:[32,153,143],toColor:[11,44,122]}]},{id:"prediction_predefined",type:"multipart",colorRamps:[{fromColor:[40,146,199],toColor:[250,250,100]},{fromColor:[250,250,100],toColor:[232,16,20]}]},{id:"purpleBright_predefined",fromColor:[255,204,255],toColor:[199,0,199]},{id:"purpleToGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[77,32,150],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[20,122,11]}]},{id:"purpleToGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[67,14,89],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[24,79,15]}]},{id:"purpleBlueBright_predefined",fromColor:[223,184,230],toColor:[112,12,242]},{id:"purpleBlueLightToDark_predefined",fromColor:[229,213,242],toColor:[93,44,112]},{id:"purpleRedBright_predefined",fromColor:[255,204,225],toColor:[199,0,99]},{id:"purpleRedLightToDark_predefined",fromColor:[250,215,246],toColor:[143,17,57]},{id:"redBright_predefined",fromColor:[255,204,204],toColor:[219,0,0]},{id:"redLightToDark_predefined",fromColor:[255,224,224],toColor:[143,10,10]},{id:"redToBlueDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[196,69,57],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[48,95,207]}]},{id:"redToBlueDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[107,13,13],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[13,53,97]}]},{id:"redToGreen_predefined",type:"multipart",colorRamps:[{fromColor:[245,0,0],toColor:[245,245,0]},{fromColor:[245,245,0],toColor:[0,245,0]}]},{id:"redToGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[186,20,20],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[54,145,33]}]},{id:"redToGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[97,21,13],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[16,69,16]}]},{id:"slope_predefined",type:"multipart",colorRamps:[{fromColor:[56,168,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,0,0]}]},{id:"spectrumFullBright_predefined",type:"multipart",colorRamps:[{fromColor:[255,0,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[0,255,255]},{fromColor:[0,255,255],toColor:[0,0,255]}]},{id:"spectrumFullDark_predefined",type:"multipart",colorRamps:[{fromColor:[153,0,0],toColor:[153,153,0]},{fromColor:[153,153,0],toColor:[0,153,153]},{fromColor:[0,153,153],toColor:[0,0,153]}]},{id:"spectrumFullLight_predefined",type:"multipart",colorRamps:[{fromColor:[255,153,153],toColor:[255,255,153]},{fromColor:[255,255,153],toColor:[153,255,255]},{fromColor:[153,255,255],toColor:[153,153,255]}]},{id:"surface_predefined",type:"multipart",colorRamps:[{fromColor:[112,153,89],toColor:[242,238,162]},{fromColor:[242,238,162],toColor:[242,206,133]},{fromColor:[242,206,133],toColor:[194,140,124]},{fromColor:[194,140,124],toColor:[255,242,255]}]},{id:"temperature_predefined",type:"multipart",colorRamps:[{fromColor:[255,252,255],toColor:[255,0,255]},{fromColor:[255,0,255],toColor:[0,0,255]},{fromColor:[0,0,255],toColor:[0,255,255]},{fromColor:[0,255,255],toColor:[0,255,0]},{fromColor:[0,255,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,128,0]},{fromColor:[255,128,0],toColor:[128,0,0]}]},{id:"whiteToBlack_predefined",fromColor:[255,255,255],toColor:[0,0,0]},{id:"yellowToDarkRed_predefined",type:"multipart",colorRamps:[{fromColor:[255,255,128],toColor:[242,167,46]},{fromColor:[242,167,46],toColor:[107,0,0]}]},{id:"yellowToGreenToDarkBlue_predefined",type:"multipart",colorRamps:[{fromColor:[255,255,128],toColor:[56,224,9]},{fromColor:[56,224,9],toColor:[26,147,171]},{fromColor:[26,147,171],toColor:[12,16,120]}]},{id:"yellowToRed_predefined",fromColor:[245,245,0],toColor:[255,0,0]},{id:"yellowGreenBright_predefined",fromColor:[236,252,204],toColor:[157,204,16]},{id:"yellowGreenLightToDark_predefined",fromColor:[215,240,175],toColor:[96,107,45]}],l={aspect_predefined:"Aspect",blackToWhite_predefined:"Black to White",blueBright_predefined:"Blue Bright",blueLightToDark_predefined:"Blue Light to Dark",blueGreenBright_predefined:"Blue-Green Bright",blueGreenLightToDark_predefined:"Blue-Green Light to Dark",brownLightToDark_predefined:"Brown Light to Dark",brownToBlueGreenDivergingBright_predefined:"Brown to Blue Green Diverging, Bright",brownToBlueGreenDivergingDark_predefined:"Brown to Blue Green Diverging, Dark",coefficientBias_predefined:"Coefficient Bias",coldToHotDiverging_predefined:"Cold to Hot Diverging",conditionNumber_predefined:"Condition Number",cyanToPurple_predefined:"Cyan to Purple",cyanLightToBlueDark_predefined:"Cyan-Light to Blue-Dark",distance_predefined:"Distance",elevation1_predefined:"Elevation #1",elevation2_predefined:"Elevation #2",errors_predefined:"Errors",grayLightToDark_predefined:"Gray Light to Dark",greenBright_predefined:"Green Bright",greenLightToDark_predefined:"Green Light to Dark",greenToBlue_predefined:"Green to Blue",orangeBright_predefined:"Orange Bright",orangeLightToDark_predefined:"Orange Light to Dark",partialSpectrum_predefined:"Partial Spectrum",partialSpectrum1Diverging_predefined:"Partial Spectrum 1 Diverging",partialSpectrum2Diverging_predefined:"Partial Spectrum 2 Diverging",pinkToYellowGreenDivergingBright_predefined:"Pink to YellowGreen Diverging, Bright",pinkToYellowGreenDivergingDark_predefined:"Pink to YellowGreen Diverging, Dark",precipitation_predefined:"Precipitation",prediction_predefined:"Prediction",purpleBright_predefined:"Purple Bright",purpleToGreenDivergingBright_predefined:"Purple to Green Diverging, Bright",purpleToGreenDivergingDark_predefined:"Purple to Green Diverging, Dark",purpleBlueBright_predefined:"Purple-Blue Bright",purpleBlueLightToDark_predefined:"Purple-Blue Light to Dark",purpleRedBright_predefined:"Purple-Red Bright",purpleRedLightToDark_predefined:"Purple-Red Light to Dark",redBright_predefined:"Red Bright",redLightToDark_predefined:"Red Light to Dark",redToBlueDivergingBright_predefined:"Red to Blue Diverging, Bright",redToBlueDivergingDark_predefined:"Red to Blue Diverging, Dark",redToGreen_predefined:"Red to Green",redToGreenDivergingBright_predefined:"Red to Green Diverging, Bright",redToGreenDivergingDark_predefined:"Red to Green Diverging, Dark",slope_predefined:"Slope",spectrumFullBright_predefined:"Spectrum-Full Bright",spectrumFullDark_predefined:"Spectrum-Full Dark",spectrumFullLight_predefined:"Spectrum-Full Light",surface_predefined:"Surface",temperature_predefined:"Temperature",whiteToBlack_predefined:"White to Black",yellowToDarkRed_predefined:"Yellow to Dark Red",yellowToGreenToDarkBlue_predefined:"Yellow to Green to Dark Blue",yellowToRed_predefined:"Yellow to Red",yellowGreenBright_predefined:"Yellow-Green Bright",yellowGreenLightToDark_predefined:"Yellow-Green Light to Dark"},u=new i.X({Aspect:"aspect","Black to White":"black-to-white","Blue Bright":"blue-bright","Blue Light to Dark":"blue-light-to-dark","Blue-Green Bright":"blue-green-bright","Blue-Green Light to Dark":"blue-green-light-to-dark","Brown Light to Dark":"brown-light-to-dark","Brown to Blue Green Diverging, Bright":"brown-to-blue-green-diverging-right","Brown to Blue Green Diverging, Dark":"brown-to-blue-green-diverging-dark","Coefficient Bias":"coefficient-bias","Cold to Hot Diverging":"cold-to-hot-diverging","Condition Number":"condition-number","Cyan to Purple":"cyan-to-purple","Cyan-Light to Blue-Dark":"cyan-light-to-blue-dark",Distance:"distance","Elevation #1":"elevation1","Elevation #2":"elevation2",Errors:"errors","Gray Light to Dark":"gray-light-to-dark","Green Bright":"green-bright","Green Light to Dark":"green-light-to-dark","Green to Blue":"green-to-blue","Orange Bright":"orange-bright","Orange Light to Dark":"orange-light-to-dark","Partial Spectrum":"partial-spectrum","Partial Spectrum 1 Diverging":"partial-spectrum-1-diverging","Partial Spectrum 2 Diverging":"partial-spectrum-2-diverging","Pink to YellowGreen Diverging, Bright":"pink-to-yellow-green-diverging-bright","Pink to YellowGreen Diverging, Dark":"pink-to-yellow-green-diverging-dark",Precipitation:"precipitation",Prediction:"prediction","urple Bright":"purple-bright","Purple to Green Diverging, Bright":"purple-to-green-diverging-bright","Purple to Green Diverging, Dark":"purple-to-green-diverging-dark","Purple-Blue Bright":"purple-blue-bright","Purple-Blue Light to Dark":"purple-blue-light-to-dark","Purple-Red Bright":"purple-red-bright","Purple-Red Light to Dark":"purple-red-light-to-dark","Red Bright":"red-bright","Red Light to Dark":"red-light-to-dark","Red to Blue Diverging, Bright":"red-to-blue-diverging-bright","Red to Blue Diverging, Dark":"red-to-blue-diverging-dark","Red to Green":"red-to-green","Red to Green Diverging, Bright":"red-to-green-diverging-bright","Red to Green Diverging, Dark":"red-to-green-diverging-dark",Slope:"slope","Spectrum-Full Bright":"spectrum-full-bright","Spectrum-Full Dark":"spectrum-full-dark","Spectrum-Full Light":"spectrum-full-light",Surface:"surface",Temperature:"temperature","White to Black":"white-to-black","Yellow to Dark Red":"yellow-to-dark-red","Yellow to Green to Dark Blue":"yellow-to-green-to-dark-blue","Yellow to Red":"yellow-to-red","Yellow-Green Bright":"yellow-green-bright","Yellow-Green Light to Dark":"yellow-green-light-to-dark"});function h(e,t){if(!e||!t||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]>t[r]+2||e[r]<t[r]-2)return!1;return!0}function c(e){const t=function(e,t){if(!e)return;const r=a;let n=null;return"algorithmic"===e.type?r.some((t=>{if(h(e.fromColor.toRgb(),t.fromColor)&&h(e.toColor.toRgb(),t.toColor))return n=t.id,!0})):"multipart"===e.type&&r.some((t=>{const r=e.colorRamps,i=t.colorRamps;if(r&&i&&r.length===i.length&&!i.some(((e,t)=>{if(!h(r[t].fromColor.toRgb(),new o.Z(e.fromColor).toRgb())||!h(r[t].toColor.toRgb(),new o.Z(e.toColor).toRgb()))return!0}))){if(n)return!0;n=t.id}})),n}(e);return t?l[t]:null}function f(e){const t=u.toJSON(e),r=Object.entries(l).find((e=>e[1]===t))?.[0],o=a.find((e=>e.id===r));return o?o.colorRamps?{type:"multipart",colorRamps:o.colorRamps.map((e=>({type:"algorithmic",algorithm:"esriCIELabAlgorithm",fromColor:e.fromColor,toColor:e.toColor})))}:{type:"algorithmic",algorithm:"esriCIELabAlgorithm",fromColor:o.fromColor,toColor:o.toColor}:null}function p(e){const t=(e=e||{}).numColors||256,r=e.distanceOffset||0,o=null!=e.isCustomInterval?e.isCustomInterval:null!==e.distanceInterval&&e.distanceInterval!==1/(t-1),n=e.distanceInterval||1/(t-1);return{...e,numColors:t,distanceOffset:r,interpolateAlpha:!!e.interpolateAlpha,distanceInterval:n,isCustomInterval:o,weights:e.weights}}function d(e,t){let{fromColor:r,toColor:o}=e;3===r.length&&(r=r.concat([255])),3===o.length&&(o=o.concat([255]));const i=e.algorithm||"esriCIELabAlgorithm",s=p(t),{numColors:a,distanceOffset:l,isCustomInterval:u,interpolateAlpha:h}=s;if(1===a&&0===l)return[r];if(2===a&&0===l&&!u)return[r,o];const c={r:r[0],g:r[1],b:r[2]},f={r:o[0],g:o[1],b:o[2]},d="esriCIELabAlgorithm"===i?function(e,t,r){const{numColors:o,distanceOffset:n,distanceInterval:i,isCustomInterval:s}=r;let{l:a,a:l,b:u}=e;const h=(t.l-a)*i,c=(t.a-l)*i,f=(t.b-u)*i,p=[];if(n){const e=n/i;a+=e*h,l+=e*c,u+=e*f}for(let e=0;e<o-1;e++)p.push({l:a,a:l,b:u}),a+=h,l+=c,u+=f;return p.push(s?{l:a,a:l,b:u}:t),p}((0,n.Y3)(c),(0,n.Y3)(f),s):"esriHSVAlgorithm"===i?function(e,t,r){const{numColors:o,distanceOffset:n,distanceInterval:i,isCustomInterval:s}=r,a=0===e.s,l=0===t.s;let u=e.h,h=t.h;a&&!l?u=h:l&&!a&&(t={...t,h:u},h=u);let c,f=Math.abs(h-u);const p=360;f<180?c=(h-u)*i:(f=p-f,c=u>h?f*i:-f*i);const d=(t.s-e.s)*i,g=(t.v-e.v)*i;let{s:m,v:y}=e,C=u;if(n){const e=n/i;C=(C+e*c+p)%p,m+=e*d,y+=e*g}const w=[];for(let e=0;e<o-1;e++)w.push({h:C,s:m,v:y}),C=(C+c+p)%p,m+=d,y+=g;return w.push(s?{h:C,s:m,v:y}:t),w}((0,n._Y)(c),(0,n._Y)(f),s):function(e,t,r){const{numColors:o,distanceOffset:n,distanceInterval:i,isCustomInterval:s}=r,a=e.h,l=t.h,u=2*Math.PI;let h;if(a<=l){const e=l-a,t=l-a-u;h=Math.abs(t)<Math.abs(e)?t:e}else{const e=l+u-a,t=l-a;h=Math.abs(t)<Math.abs(e)?t:e}const c=h*i,f=(t.l-e.l)*i,p=(t.c-e.c)*i;let{l:d,c:g,h:m}=e;if(n){const e=n/i;d+=e*f,g+=e*p,m=(m+e*c+u)%u}const y=[];for(let e=0;e<o-1;e++)y.push({l:d,c:g,h:m}),d+=f,g+=p,m=(m+c+u)%u;return y.push(s?{l:d,c:g,h:m}:t),y}((0,n.sJ)(c),(0,n.sJ)(f),s),g=[],m=r[3]??255,y=((o[3]??255)-m)/(a-1);for(let e=0;e<a;e++){const{r:t,g:r,b:o}=(0,n.xr)(d[e]),i=h?Math.round(m+y*e):255;g.push([t,r,o,i])}return g}function g(e,t){const r="toJSON"in e?e.toJSON():e;return"multipart"===r.type?function(e,t){const{numColors:r,interpolateAlpha:o}=p(t);let n=t?.weights;const{colorRamps:i}=e;if(n){const e=n.reduce(((e,t)=>e+t));n=n.map((t=>t/e))}else{n=[];for(let e=0;e<i.length;e++)n[e]=1/i.length}const s=[];let a=0,l=0;const u=1/(r-1);let h=!1;for(let e=0;e<i.length;e++){let t=h?0:a*u-l,c=e===i.length-1?r-1-a:(n[e]-t)/u;if(h=Math.ceil(c)===c,c=Math.ceil(c),0===c)continue;t/=n[e];const f=d(i[e],{numColors:c,interpolateAlpha:o,distanceOffset:t,distanceInterval:u/n[e]});a+=f.length,s.push(...f),l+=n[e]}const c=[...i[i.length-1].toColor];return 3===c.length&&c.push(255),s.push(c),s}(r,t):d(r,t)}function m(e,t){const r=g(e,t),o=t?.interpolateAlpha;return r.forEach(((e,t)=>{e.unshift(t),o||e.pop()})),r}function y(e){const t=c(e);if(e){if("algorithmic"===e.type)return{...C(e),Name:t};if(e.colorRamps){const r=e.colorRamps.map(C);return{type:"MultiPartColorRamp",NumColorRamps:r.length,ArrayOfColorRamp:r,Name:t}}}}function C(e){if(e)return{Algorithm:e.toJSON()?.Algorithm||"esriHSVAlgorithm",type:"AlgorithmicColorRamp",FromColor:w(e.fromColor),ToColor:w(e.toColor)}}function w(e){const t=(0,n._Y)(e);return{type:"HsvColor",Hue:t.h,Saturation:t.s,Value:t.v,AlphaValue:255}}function b(e){const t=e.reverse().map((e=>{const t=e.toString(16);return t.length<2?"0"+t:t}));return 4294967295&Number.parseInt(t.join(""),16)}},75509:(e,t,r)=>{r.d(t,{J:()=>n,v:()=>o});const o=new(r(35454).X)({none:"none",standardDeviation:"standard-deviation",histogramEqualization:"histogram-equalization",minMax:"min-max",percentClip:"percent-clip",sigmoid:"sigmoid"}),n={0:"none",3:"standardDeviation",4:"histogramEqualization",5:"minMax",6:"percentClip",9:"sigmoid"}},23808:(e,t,r)=>{r.d(t,{GE:()=>l}),r(66577);var o=r(80442),n=r(92604),i=(r(22021),r(95330)),s=r(77734);r(8744),r(6570);const a=n.Z.getLogger("esri.views.2d.engine.flow.dataUtils");async function l(e,t,r,n){const l=performance.now(),u=function(e,t){const r=function(e,t,r,o){if(0===o)return e;const n=Math.round(3*o),i=new Array(2*n+1);let s=0;for(let e=-n;e<=n;e++){const t=Math.exp(-e*e/(o*o));i[e+n]=t,s+=t}for(let e=-n;e<=n;e++)i[e+n]/=s;const a=new Float32Array(e.length);for(let o=0;o<r;o++)for(let r=0;r<t;r++){let s=0,l=0;for(let a=-n;a<=n;a++){if(r+a<0||r+a>=t)continue;const u=i[a+n];s+=u*e[2*(o*t+(r+a))+0],l+=u*e[2*(o*t+(r+a))+1]}a[2*(o*t+r)+0]=s,a[2*(o*t+r)+1]=l}const l=new Float32Array(e.length);for(let e=0;e<t;e++)for(let o=0;o<r;o++){let s=0,u=0;for(let l=-n;l<=n;l++){if(o+l<0||o+l>=r)continue;const h=i[l+n];s+=h*a[2*((o+l)*t+e)+0],u+=h*a[2*((o+l)*t+e)+1]}l[2*(o*t+e)+0]=s,l[2*(o*t+e)+1]=u}return l}(t.data,t.width,t.height,e.smoothing);return e.interpolate?(e,o)=>{const n=Math.floor(e),i=Math.floor(o);if(n<0||n>=t.width)return[0,0];if(i<0||i>=t.height)return[0,0];const s=e-n,a=o-i,l=n,u=i,h=n<t.width-1?n+1:n,c=i<t.height-1?i+1:i,f=r[2*(u*t.width+l)],p=r[2*(u*t.width+h)],d=r[2*(c*t.width+l)],g=r[2*(c*t.width+h)],m=r[2*(u*t.width+l)+1],y=r[2*(u*t.width+h)+1];return[(f*(1-a)+d*a)*(1-s)+(p*(1-a)+g*a)*s,(m*(1-a)+r[2*(c*t.width+l)+1]*a)*(1-s)+(y*(1-a)+r[2*(c*t.width+h)+1]*a)*s]}:(e,o)=>{const n=Math.round(e),i=Math.round(o);return n<0||n>=t.width||i<0||i>=t.height?[0,0]:[r[2*(i*t.width+n)+0],r[2*(i*t.width+n)+1]]}}(t,r),c=performance.now(),f=h(t,u,r.width,r.height),p=performance.now(),d=function(e,t){const r=new s.Z,o=e.reduce(((e,t)=>e+t.length),0),n=new Float32Array(4*o),i=new Array(e.length);let a=0,l=0;for(const t of e){const e=a;for(const e of t)n[4*a+0]=e.x,n[4*a+1]=e.y,n[4*a+2]=e.t,n[4*a+3]=e.speed,a++;i[l++]={startVertex:e,numberOfVertices:t.length,totalTime:t[t.length-1].t,timeSeed:r.getFloat()}}return{lineVertices:n,lineDescriptors:i}}(f),g=performance.now(),m="Streamlines"===e?function(e,t){const{lineVertices:r,lineDescriptors:o}=e;let n=0,i=0;for(const e of o)n+=2*e.numberOfVertices,i+=6*(e.numberOfVertices-1);const s=new Float32Array(9*n),a=new Uint32Array(i);let l=0,u=0;function h(){a[u++]=l-2,a[u++]=l,a[u++]=l-1,a[u++]=l,a[u++]=l+1,a[u++]=l-1}function c(e,t,r,o,n,i,a,u){const h=9*l;let c=0;s[h+c++]=e,s[h+c++]=t,s[h+c++]=1,s[h+c++]=r,s[h+c++]=i,s[h+c++]=a,s[h+c++]=o/2,s[h+c++]=n/2,s[h+c++]=u,l++,s[h+c++]=e,s[h+c++]=t,s[h+c++]=-1,s[h+c++]=r,s[h+c++]=i,s[h+c++]=a,s[h+c++]=-o/2,s[h+c++]=-n/2,s[h+c++]=u,l++}for(const e of o){const{totalTime:t,timeSeed:o}=e;let n=null,i=null,s=null,a=null,l=null,u=null;for(let f=0;f<e.numberOfVertices;f++){const p=r[4*(e.startVertex+f)+0],d=r[4*(e.startVertex+f)+1],g=r[4*(e.startVertex+f)+2],m=r[4*(e.startVertex+f)+3];let y=null,C=null,w=null,b=null;if(f>0){y=p-n,C=d-i;const e=Math.sqrt(y*y+C*C);if(y/=e,C/=e,f>1){let e=y+l,t=C+u;const r=Math.sqrt(e*e+t*t);e/=r,t/=r;const o=Math.min(1/(e*y+t*C),10);e*=o,t*=o,w=-t,b=e}else w=-C,b=y;null!==w&&null!==b&&(c(n,i,s,w,b,t,o,m),h())}n=p,i=d,s=g,l=y,u=C,a=m}c(n,i,s,-u,l,t,o,a)}return{vertexData:s,indexData:a}}(d):function(e){const{lineVertices:t,lineDescriptors:r}=e;let o=0,n=0;for(const e of r){const t=e.numberOfVertices-1;o+=4*t*2,n+=6*t*2}const i=new Float32Array(16*o),s=new Uint32Array(n);let a,l,u,h,c,f,p,d,g,m,y,C,w,b,x=0,k=0;function v(){s[k++]=x-8,s[k++]=x-7,s[k++]=x-6,s[k++]=x-7,s[k++]=x-5,s[k++]=x-6,s[k++]=x-4,s[k++]=x-3,s[k++]=x-2,s[k++]=x-3,s[k++]=x-1,s[k++]=x-2}function T(e,t,r,o,n,s,a,l,u,h,c,f,p,d){const g=16*x;let m=0;for(const y of[1,2])for(const C of[1,2,3,4])i[g+m++]=e,i[g+m++]=t,i[g+m++]=r,i[g+m++]=o,i[g+m++]=a,i[g+m++]=l,i[g+m++]=u,i[g+m++]=h,i[g+m++]=y,i[g+m++]=C,i[g+m++]=p,i[g+m++]=d,i[g+m++]=n/2,i[g+m++]=s/2,i[g+m++]=c/2,i[g+m++]=f/2,x++}function S(e,t){let r=g+y,o=m+C;const n=Math.sqrt(r*r+o*o);r/=n,o/=n;const i=g*r+m*o;r/=i,o/=i;let s=y+w,x=C+b;const k=Math.sqrt(s*s+x*x);s/=k,x/=k;const S=y*s+C*x;s/=S,x/=S,T(a,l,u,h,-o,r,c,f,p,d,-x,s,e,t),v()}function A(e,t,r,o,n,i){if(g=y,m=C,y=w,C=b,null==g&&null==m&&(g=y,m=C),null!=c&&null!=f){w=e-c,b=t-f;const r=Math.sqrt(w*w+b*b);w/=r,b/=r}null!=g&&null!=m&&S(n,i),a=c,l=f,u=p,h=d,c=e,f=t,p=r,d=o}function I(e,t){g=y,m=C,y=w,C=b,null==g&&null==m&&(g=y,m=C),null!=g&&null!=m&&S(e,t)}for(const e of r){a=null,l=null,u=null,h=null,c=null,f=null,p=null,d=null,g=null,m=null,y=null,C=null,w=null,b=null;const{totalTime:r,timeSeed:o}=e;for(let n=0;n<e.numberOfVertices;n++)A(t[4*(e.startVertex+n)+0],t[4*(e.startVertex+n)+1],t[4*(e.startVertex+n)+2],t[4*(e.startVertex+n)+3],r,o);I(r,o)}return{vertexData:i,indexData:s}}(d),y=performance.now();return(0,o.Z)("esri-2d-profiler")&&(a.info("I.1","_createFlowFieldFromData (ms)",Math.round(c-l)),a.info("I.2","_getStreamlines (ms)",Math.round(p-c)),a.info("I.3","createAnimatedLinesData (ms)",Math.round(g-p)),a.info("I.4","create{Streamlines|Particles}Mesh (ms)",Math.round(y-g)),a.info("I.5","createFlowMesh (ms)",Math.round(y-l)),a.info("I.6","Mesh size (bytes)",m.vertexData.buffer.byteLength+m.indexData.buffer.byteLength)),await Promise.resolve(),(0,i.k_)(n),m}function u(e,t,r,o,n,i,s,a,l){const u=[];let h=r,c=o,f=0,[p,d]=t(h,c);p*=e.velocityScale,d*=e.velocityScale;const g=Math.sqrt(p*p+d*d);let m,y;u.push({x:h,y:c,t:f,speed:g});for(let r=0;r<e.verticesPerLine;r++){let[r,o]=t(h,c);r*=e.velocityScale,o*=e.velocityScale;const p=Math.sqrt(r*r+o*o);if(p<e.minSpeedThreshold)return u;const d=r/p,g=o/p;if(h+=d*e.segmentLength,c+=g*e.segmentLength,f+=e.segmentLength/p,Math.acos(d*m+g*y)>e.maxTurnAngle)return u;if(e.collisions){const e=Math.round(h*l),t=Math.round(c*l);if(e<0||e>s-1||t<0||t>a-1)return u;const r=i[t*s+e];if(-1!==r&&r!==n)return u;i[t*s+e]=n}u.push({x:h,y:c,t:f,speed:p}),m=d,y=g}return u}function h(e,t,r,o){const n=[],i=new s.Z,a=1/Math.max(e.lineCollisionWidth,1),l=Math.round(r*a),h=Math.round(o*a),c=new Int32Array(l*h);for(let e=0;e<c.length;e++)c[e]=-1;const f=[];for(let t=0;t<o;t+=e.lineSpacing)for(let o=0;o<r;o+=e.lineSpacing)f.push({x:o,y:t,sort:i.getFloat()});f.sort(((e,t)=>e.sort-t.sort));for(const{x:r,y:o}of f)if(i.getFloat()<e.density){const i=u(e,t,r,o,n.length,c,l,h,a);if(i.length<2)continue;n.push(i)}return n}}}]);