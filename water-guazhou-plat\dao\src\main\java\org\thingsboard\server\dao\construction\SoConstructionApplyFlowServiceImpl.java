package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionAcceptFlowMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowSaveRequest;

@Service
public class SoConstructionApplyFlowServiceImpl implements SoConstructionApplyFlowService {
    @Autowired
    private SoConstructionAcceptFlowMapper mapper;

    @Override
    public IPage<SoConstructionApplyFlow> findAllConditional(SoConstructionApplyFlowPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionApplyFlow save(SoConstructionApplyFlowSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionApplyFlow entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
