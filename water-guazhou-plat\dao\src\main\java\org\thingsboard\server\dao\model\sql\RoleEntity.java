/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.role.Role;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.CUSTOMER_ROLE_NAME)
public class RoleEntity extends BaseSqlEntity<Role> {

    @Column(name = ModelConstants.CUSTOMER_ROLE_NAME_PROPERTY)
    private String name;

    @Column(name = ModelConstants.CUSTOMER_ROLE_STATUS)
    private Integer status;

    @Column(name = ModelConstants.CUSTOMER_ROLE_FLAG_DELETE)
    private Integer flagDelete;

    @Column(name = ModelConstants.CUSTOMER_ROLE_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.CUSTOMER_ROLE_TENANT_ID)
    private String tenantId;

    public RoleEntity() {
        super();
    }

    public RoleEntity(Role role) {
        if (role.getId() != null) {
            this.setId(role.getId().getId());
        }
        if (role.getTenantId() != null) {
            this.tenantId = toString(role.getTenantId().getId());
        }
        this.name = role.getName();
        this.status = role.getStatus();
        this.flagDelete = role.getFlagDelete();
        this.additionalInfo = role.getAdditionalInfo();
    }

    @Override
    public Role toData() {
        Role role = new Role(new RoleId(getId()));
        role.setName(name);
        role.setStatus(status);
        role.setFlagDelete(flagDelete);
        role.setAdditionalInfo(additionalInfo);
        role.setTenantId(new TenantId(toUUID(tenantId)));
        return role;
    }
}
