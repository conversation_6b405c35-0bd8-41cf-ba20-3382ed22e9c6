<template>
  <template v-if="!item?.hidden && !item?.meta?.hidden && hasAuthority">
    <!-- 有子级 -->
    <template v-if="item.children?.length">
      <!-- 只有一个子级并且隐藏层级 -->
      <template v-if="item.children.length === 1 && !item.alwaysShow">
        <MenuItem
          :item="item.children[0]"
          :icon="item.children[0].meta?.icon || icon"
          :popper-append-to-body="popperAppendToBody"
          @click="item=>$emit('click',item)"
        />
      </template>
      <!-- 多个子级 -->
      <template v-else>
        <el-menu-item-group v-if="item.isGroup">
          <template
            v-if="item?.title || item?.meta?.title"
            #title
          >
            <span :class="'title-text' + ' ' + (item?.meta?.icon || icon || '')">{{ item?.title || item?.meta?.title }}
            </span>
          </template>
          <template v-for="(child, k) in item.children">
            <template v-if="!child.hidden && !child?.meta?.hidden">
              <MenuItem
                :key="k"
                :item="child"
                :icon="child.meta?.icon || icon"
                :popper-append-to-body="popperAppendToBody"
                @click="item=>$emit('click',item)"
              />
            </template>
          </template>
        </el-menu-item-group>
        <el-sub-menu
          v-else
          :index="item.path"
          :popper-append-to-body="popperAppendToBody"
        >
          <template
            v-if="item?.title || item?.meta?.title"
            #title
          >
            <span :class="'title-text' + ' ' + (item?.meta?.icon || icon || '')">{{ item?.title || item?.meta?.title }}
            </span>
          </template>
          <template v-for="(child, k) in item.children">
            <template v-if="!child.hidden && !child?.meta?.hidden">
              <MenuItem
                :key="k"
                :item="child"
                :icon="child.meta?.icon || icon"
                :popper-append-to-body="popperAppendToBody"
                @click="item=>$emit('click',item)"
              />
            </template>
          </template>
        </el-sub-menu>
      </template>
    </template>
    <!-- 无子级 -->
    <template v-else>
      <el-menu-item
        :index="item.path"
        :route="item.component ? item : { name: 'NotFound' }"
        @click="$emit('click',item)"
      >
        <template
          v-if="item?.title || item?.meta?.title"
          #title
        >
          <span :class="'title-text' + ' ' + (item.meta.icon || icon || '')">{{ item?.title || item.meta.title }}</span>
        </template>
      </el-menu-item>
    </template>
  </template>
</template>

<script lang="ts" setup>
import { hasPermission } from '@/utils/RouterHelper'

defineEmits(['click'])
// import path from 'path'
const props = defineProps<{
  item: any
  // path: string
  icon?: string
  /**
   * 是否需要对菜单进行授权
   * default true
   */
  authority?: boolean
  popperAppendToBody?: boolean
}>()

const hasAuthority = computed(() => {
  return props.authority === false ? true : hasPermission(props.item?.meta?.roles)
})
</script>

<style lang="scss">
.title-text {
  &.iconfont::before {
    margin-right: 4px !important;
  }
}
</style>
