package org.thingsboard.server.controller.alarm;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.alarmV2.AlarmRuleSmartService;
import org.thingsboard.server.dao.model.DTO.AlarmRuleSmartSaveDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleSmartRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 报警规则
 */
@RestController
@RequestMapping("api/alarmV3/alarmRuleSmart")
public class AlarmRuleSmartController extends BaseController {

    @Autowired
    private AlarmRuleSmartService alarmRuleUserService;

    @PostMapping("list")
    public IstarResponse findList(@RequestBody AlarmRuleSmartRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return alarmRuleUserService.findList(request, tenantId);
    }

    @PostMapping("listV2")
    public IstarResponse findListV2(@RequestBody AlarmRuleSmartRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return alarmRuleUserService.findListV2(request, tenantId);
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody AlarmRuleSmartSaveDTO entity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        entity.getAlarmRuleSmart().setTenantId(tenantId);
        return alarmRuleUserService.save(entity, tenantId);
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) {
        return alarmRuleUserService.delete(idList);
    }

}
