import{d as k,r as R,e8 as e,W as I,o as w,e9 as C,g as m,n as c,aB as T,aJ as x,p as d,bh as h,an as E,i as L,C as b}from"./index-r0dFAfgr.js";import{Q as j}from"./pipe-nogVzCHG.js";import{r as B}from"./zhandian-YaGuQZe6.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{s as D,E as F}from"./StatisticsHelper-D-s_6AyQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{i as N}from"./data-CLo2TII-.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./index-BggOjNGp.js";const P={class:"stationstatistic"},G={class:"text"},Q={class:"name"},V={key:0,class:"unit"},A={class:"count"},J=k({__name:"StationStatistic",props:{data:{},projectId:{}},setup(f){const _=f,p=R({statisticResult:_.data||[{name:"管线长度",unit:"千米",value:e(0)},{name:"设备总数",unit:"个",value:e(0)},{name:"流量监测点",unit:"个",value:e(0)},{name:"压力监测点",unit:"个",value:e(0)},{name:"水质监测点",unit:"个",value:e(0)},{name:"泵房",unit:"个",value:e(0)}]}),v=async()=>{p.statisticResult.length=0,S(),y()},y=async()=>{var i,r;try{const t=(i=N().map(a=>a.label))==null?void 0:i.join(","),o=(r=(await B({types:t,projectId:_.projectId})).data)==null?void 0:r.data,s=(o==null?void 0:o.filter(a=>!!a.count).map(a=>({name:a.key,value:e(a.count),unit:"个"})))||[],l=o.reduce((a,g)=>g.count+a,0);p.statisticResult.push({name:"设备总数",value:e(l),unit:"个"}),p.statisticResult.push(...s)}catch{}},S=async()=>{var i,r;try{const t=await j(),o=(await D("length",{layerIds:(r=(i=t.data.layers)==null?void 0:i.filter(s=>s.name==="管网管线"))==null?void 0:r.map(s=>s.id)})).reduce((s,l)=>l.rows[0][F.ShapeLen]+s,0);p.statisticResult.unshift({name:"管网管线",value:e((o/1e3).toFixed(2)),unit:"千米"})}catch{}},u=I();return w(async()=>{var i,r,t,n;if(!u.gToken){const o=await C();((i=o.data)==null?void 0:i.code)===1e4&&(u.SET_gToken((t=(r=o.data)==null?void 0:r.result)==null?void 0:t.token),u.SET_gUserInfo((n=o.data)==null?void 0:n.result))}v()}),(i,r)=>(m(),c("div",P,[(m(!0),c(T,null,x(L(p).statisticResult,(t,n)=>(m(),c("div",{key:n,class:"statistic-item"},[d("div",G,[d("span",Q,h(t.name),1),t.unit?(m(),c("span",V,"("+h(t.unit)+")",1)):E("",!0)]),d("div",A,h(t.value),1)]))),128))]))}}),oo=b(J,[["__scopeId","data-v-f6d15b77"]]);export{oo as default};
