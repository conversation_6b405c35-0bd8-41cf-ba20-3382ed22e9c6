package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.DispatchMethodMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodSaveRequest;

@Service
public class DispatchMethodServiceImpl implements DispatchMethodService {
    @Autowired
    private DispatchMethodMapper mapper;

    @Override
    public IPage<DispatchMethod> findAllConditional(DispatchMethodPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public DispatchMethod save(DispatchMethodSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(DispatchMethod entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean switchEnabled(String id, boolean enabled) {
        return mapper.switchEnabled(id, enabled);
    }
}
