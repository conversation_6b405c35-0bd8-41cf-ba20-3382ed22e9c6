import{m as e}from"./index-r0dFAfgr.js";function p(t){return e({url:"/istar/api/production/waterPlant/getWaterSupplyFlowReport",method:"get",params:t})}function o(t){return e({url:"/istar/api/production/waterPlant/getWaterSupplyFlowReport/export",method:"get",params:t,responseType:"blob"})}function a(t){return e({url:"/istar/api/production/waterPlant/getWaterSupplyPressureReport",method:"get",params:t})}function u(t){return e({url:"/istar/api/production/waterPlant/getWaterSupplyPressureReport/export",method:"get",params:t,responseType:"blob"})}function l(t){return e({url:"/istar/api/production/waterPlant/getWaterSupplyQualityReport",method:"get",params:t})}function n(t){return e({url:"/istar/api/production/waterPlant/getWaterSupplyQualityReport/export",method:"get",params:t,responseType:"blob"})}export{a,u as b,l as c,n as d,o as e,p as g};
