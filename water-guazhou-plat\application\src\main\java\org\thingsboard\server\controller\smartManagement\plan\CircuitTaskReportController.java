package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport;
import org.thingsboard.server.dao.plan.CircuitTaskReportService;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/sm/circuitTaskReport")
public class CircuitTaskReportController extends BaseController {
    @Autowired
    private CircuitTaskReportService service;


    @GetMapping
    public IPage<CircuitTaskReport> findAllConditional(CircuitTaskReportPageRequest request) {
        return service.findAllConditional(request);
    }

    // @GetMapping("/{id}")
    @Deprecated
    public CircuitTaskReport findById(@PathVariable String id) {
        return service.findById(id);
    }

    // @GetMapping("/{taskCode}/point/{pointId}")
    @Deprecated
    public CircuitTaskReport findByPoint(@PathVariable String taskCode, @PathVariable String pointId) throws ThingsboardException {
        return service.findByPoint(taskCode, pointId, UUIDConverter.fromTimeUUID(getTenantId().getId()));
    }

    // @PostMapping
    @Deprecated
    public CircuitTaskReport save(@RequestBody CircuitTaskReportSaveRequest req) {
        return service.save(req);
    }

    @PostMapping("/{id}/present")
    public boolean present(@RequestBody CircuitTaskReportCompleteRequest req, @PathVariable String id) {
        req.setReportId(id);
        return service.present(req);
    }


    // @PostMapping("/{id}/fallback")
    @Deprecated
    public boolean fallback(@RequestBody CircuitTaskReportCompleteRequest req, @PathVariable String id) {
        req.setReportId(id);
        return service.fallback(req);
    }

    // @PatchMapping("/{id}")
    @Deprecated
    public boolean edit(@RequestBody CircuitTaskReportSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    @Deprecated
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

}