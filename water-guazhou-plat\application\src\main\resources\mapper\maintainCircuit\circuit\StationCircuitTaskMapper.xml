<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.circuit.StationCircuitTaskMapper">

    <!--  多表基础查询  -->
    <sql id="multiple_base_select">
        SELECT a.id,
               a.code,
               a.name,
               a.type,
               a.station_id AS "stationId",
               a.option_dep AS "optionDep",
               a.option_user_id AS "optionUserId",
               a.start_time AS "startTime",
               a.end_time AS "endTime",
               a.real_start_time AS "realStartTime",
               a.real_end_time AS "realEndTime",
               a.status AS "status",
               a.audit_dep AS "auditDep",
               a.audit_user_id AS "auditUserId",
               a.audit_result AS "auditResult",
               a.audit_remark AS "auditRemark",
               a.audit_time AS "auditTime",
               a.tenant_id AS "tenantId",
               a.create_time AS "createTime"

    </sql>

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask">
        <include refid="multiple_base_select"/>, b.name AS "stationName"
        FROM tb_station_circuit_task a LEFT JOIN tb_station b ON a.station_id = b.id
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.optionDep != null and param.optionDep != ''">
                AND a.option_dep = #{param.optionDep}
            </if>
            <if test="param.code != null and param.code != ''">
                AND a.code = #{param.code}
            </if>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.status = #{param.status}
            </if>
            <if test="param.optionUserId != null and param.optionUserId != ''">
                AND a.option_user_id = #{param.optionUserId}
            </if>
            <if test="param.auditDep != null and param.auditDep != ''">
                AND a.audit_dep = #{param.auditDep}
            </if>
            <if test="param.auditUserId != null and param.auditUserId != ''">
                AND a.audit_user_id = #{param.auditUserId}
            </if>
            <if test="param.stationType != null and param.stationType != ''">
                AND a.station_type = #{param.stationType}
            </if>
            <if test="param.auditResult != null and param.auditResult != ''">
                AND a.audit_result = #{param.auditResult}
            </if>
            <if test="param.auditUserId != null and param.auditUserId != ''">
                AND a.audit_user_id = #{param.auditUserId}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND a.name LIKE '%'|| #{param.keyword}||'%'
            </if>
            <if test="param.beginStartTime != null and param.endStartTime != null">
                AND a.start_time BETWEEN #{beginStartTime} AND #{endStartTime}
            </if>
            <if test="param.beginEndTime != null and param.endEndTime != null">
                AND a.end_time BETWEEN #{beginEndTime} AND #{endEndTime}
            </if>
            <if test="param.beginRealStartTime != null and param.endRealStartTime != null">
                AND a.real_start_time BETWEEN #{beginRealStartTime} AND #{endRealStartTime}
            </if>
            <if test="param.beginRealEndTime != null and param.endRealEndTime != null">
                AND a.real_end_time BETWEEN #{beginRealEndTime} AND #{endRealEndTime}
            </if>
        </where>
        ORDER BY a.start_time


    </select>
</mapper>