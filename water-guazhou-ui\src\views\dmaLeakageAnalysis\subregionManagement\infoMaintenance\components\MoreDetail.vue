<template>
  <DialogForm
    ref="refDialog"
    :config="DialogConfig"
  >
    <div class="tab-wrapper">
      <Tabs
        v-model="currentTab"
        :config="TabConfig"
      >
        <template #content="scope">
          <MoreDetail_LLBXX
            v-if="scope.data.value === ETABS.LiuLiangBiaoXinXi"
            :partition="partition"
          ></MoreDetail_LLBXX>
          <MoreDetail_HBXX
            v-if="scope.data.value === ETABS.HuBiaoXinXi"
            :partition="partition"
          ></MoreDetail_HBXX>
          <MoreDetail_GWXX
            v-if="scope.data.value === ETABS.GuanWangXinXi"
            :partition="partition"
          />
          <MoreDetail_EGBF
            v-if="scope.data.value === ETABS.ErGongXinXi"
            :partition="partition"
          />
          <MoreDetail_FMXX
            v-if="scope.data.value === ETABS.FaMenXinXi"
            :partition="partition"
          />
          <MoreDetail_XHSXX
            v-if="scope.data.value === ETABS.XiaoHuoShuanXinXi"
            :partition="partition"
          />
          <MoreDetail_WHJL
            v-if="scope.data.value === ETABS.WeiHuXinXi"
            :partition="partition"
          />
          <MoreDetail_LDJL
            v-if="scope.data.value === ETABS.LouDianJiLu"
            :partition="partition"
          />
        </template>
      </Tabs>
    </div>
  </DialogForm>
</template>
<script lang="ts" setup>
import { IDialogFormIns } from '@/components/type'
import MoreDetail_LLBXX from './MoreDetail_LLBXX.vue'
import MoreDetail_HBXX from './MoreDetail_HBXX.vue'
import MoreDetail_GWXX from './MoreDetail_GWXX.vue'
import MoreDetail_LDJL from './MoreDetail_LDJL.vue'
import MoreDetail_WHJL from './MoreDetail_WHJL.vue'
import MoreDetail_XHSXX from './MoreDetail_XHSXX.vue'
import MoreDetail_FMXX from './MoreDetail_FMXX.vue'
import MoreDetail_EGBF from './MoreDetail_EGBF.vue'

defineProps<{ partition?: NormalOption }>()
enum ETABS {
  LiuLiangBiaoXinXi,
  HuBiaoXinXi,
  GuanWangXinXi,
  ErGongXinXi,
  FaMenXinXi,
  XiaoHuoShuanXinXi,
  WeiHuXinXi,
  LouDianJiLu
}
const refDialog = ref<IDialogFormIns>()
const DialogConfig = reactive<IDialogFormConfig>({
  title: '分区资料',
  dialogWidth: '70%',
  group: [],
  cancel: false
})
const currentTab = ref<number>(ETABS.LiuLiangBiaoXinXi)
const TabConfig = reactive<ITabs>({
  type: 'tabs',
  fullHeight: true,
  tabType: 'border-card',
  tabs: [
    { label: '流量表信息', value: ETABS.LiuLiangBiaoXinXi },
    { label: '户表信息', value: ETABS.HuBiaoXinXi },
    { label: '管网信息', value: ETABS.GuanWangXinXi },
    { label: '泵站信息', value: ETABS.ErGongXinXi },
    { label: '阀门信息', value: ETABS.FaMenXinXi },
    { label: '消火栓信息', value: ETABS.XiaoHuoShuanXinXi },
    { label: '维护记录', value: ETABS.WeiHuXinXi },
    { label: '漏点记录', value: ETABS.LouDianJiLu }
  ]
})
const openDialog = () => {
  refDialog.value?.openDialog()
}
defineExpose({
  openDialog
})
</script>
<style lang="scss" scoped>
.tab-wrapper {
  height: 600px;
}
</style>
