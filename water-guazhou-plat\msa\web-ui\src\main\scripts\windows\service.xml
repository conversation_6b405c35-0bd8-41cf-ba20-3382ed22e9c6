<!--

    Copyright © 2016-2018 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<service>
    <id>${pkg.name}</id>
    <name>${project.name}</name>
    <description>${project.description}</description>
    <workingdirectory>%BASE%\bin</workingdirectory>
    <logpath>${pkg.winWrapperLogFolder}</logpath>
    <logmode>rotate</logmode>
    <env name="NODE_CONFIG_DIR" value="%BASE%\conf" />
    <env name="LOG_FOLDER" value="${pkg.winWrapperLogFolder}" />
    <env name="NODE_ENV" value="production" />
    <env name="WEB_FOLDER" value="%BASE%\web" />
    <executable>%BASE%\bin\${pkg.name}.exe</executable>
</service>
