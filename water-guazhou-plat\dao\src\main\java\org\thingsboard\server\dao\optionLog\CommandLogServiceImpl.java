package org.thingsboard.server.dao.optionLog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.CommandLogDTO;
import org.thingsboard.server.dao.model.request.CommandListRequest;
import org.thingsboard.server.dao.model.sql.CommandLog;
import org.thingsboard.server.dao.sql.optionLog.CommandLogMapper;
import org.thingsboard.server.dao.user.UserService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommandLogServiceImpl implements CommandLogService {

    @Autowired
    private CommandLogMapper commandLogMapper;

    @Autowired
    private UserService userService;

    @Override
    public PageData<CommandLogDTO> findList(CommandListRequest request, TenantId tenantId) {
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        Page<CommandLogDTO> page = new Page<>(request.getPage(), request.getSize());
        IPage<CommandLogDTO> pageResult = commandLogMapper.findList(page, request);

        // 查询用户列表
        List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(request.getTenantId())));
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));

        for (CommandLogDTO record : pageResult.getRecords()) {
            String optionUserId = record.getOptionUserId();
            if (StringUtils.isNotBlank(optionUserId)) {
                User optionUser = userMap.get(optionUserId);
                if (optionUser != null) {
                    record.setOptionUserName(optionUser.getFirstName());
                } else {
                    record.setOptionUserName(optionUserId);
                }
            }
        }

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(CommandLog commandLog) {
        commandLogMapper.insert(commandLog);
    }
}
