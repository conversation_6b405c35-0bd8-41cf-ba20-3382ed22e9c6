import{d,r as u,o as h,g as a,n as l,p as t,i as r,q as v,F as g,aB as b,aJ as f,h as j,dB as C,dC as x,C as B}from"./index-r0dFAfgr.js";/* empty css                         */import{_ as y,a as I,b as k,c as S,d as w,e as E,f as F,g as G}from"./img_8-BTVxQQlz.js";const L={class:"img-box"},N={class:"img-top"},V=["src"],Y=["src"],$=d({__name:"SCGY_yintao",setup(q){const c=e=>new URL(Object.assign({"../imgs/scroll/img_1.jpg":y,"../imgs/scroll/img_2.jpg":I,"../imgs/scroll/img_3.jpg":k,"../imgs/scroll/img_4.jpg":S,"../imgs/scroll/img_5.jpg":w,"../imgs/scroll/img_6.jpg":E,"../imgs/scroll/img_7.jpg":F,"../imgs/scroll/img_8.jpg":G})[`../imgs/scroll/${e}`],import.meta.url).href,s=u({currentImage:"",imgs:[...Array.from({length:8}).map((e,_)=>{const o=`img_${_+1}.jpg`;return{label:_.toString(),img:c(o)}})]}),i=e=>{s.currentImage=s.imgs[e].img};return h(()=>{i(0)}),(e,_)=>{const o=C,m=x;return a(),l("div",L,[t("div",N,[t("img",{src:r(s).currentImage,alt:""},null,8,V)]),t("div",null,[v(m,{interval:4e3,type:"card",height:"120px",class:"mg_top_10",onChange:i},{default:g(()=>[(a(!0),l(b,null,f(r(s).imgs,(n,p)=>(a(),j(o,{key:p},{default:g(()=>[t("img",{src:n.img,style:{width:"100%",height:"160px"}},null,8,Y)]),_:2},1024))),128))]),_:1})])])}}}),O=B($,[["__scopeId","data-v-75ab737d"]]);export{O as default};
