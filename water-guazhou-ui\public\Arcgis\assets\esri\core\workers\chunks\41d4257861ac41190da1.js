"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6188,3172,9880],{99880:(e,t,r)=>{r.d(t,{V:()=>l});var n=r(68773),s=(r(3172),r(20102)),i=r(92604),o=r(17452);const a=i.Z.getLogger("esri.assets");function l(e){if(!n.Z.assetsPath)throw a.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new s.Z("assets:path-not-set","config.assetsPath is not set");return(0,o.v_)(n.Z.assetsPath,e)}},46851:(e,t,r)=>{r.d(t,{R:()=>i,a:()=>c,g:()=>s,t:()=>l});let n=1e-6;function s(){return n}const i=Math.random,o=Math.PI/180,a=180/Math.PI;function l(e){return e*o}function c(e){return e*a}Object.freeze(Object.defineProperty({__proto__:null,RANDOM:i,equals:function(e,t){return Math.abs(e-t)<=n*Math.max(1,Math.abs(e),Math.abs(t))},getEpsilon:s,setEpsilon:function(e){n=e},toDegree:c,toRadian:l},Symbol.toStringTag,{value:"Module"}))},17896:(e,t,r)=>{r.d(t,{B:()=>h,C:()=>u,a:()=>l,b:()=>c,c:()=>o,d:()=>y,e:()=>_,f:()=>m,g:()=>f,h:()=>b,i:()=>p,j:()=>A,k:()=>v,l:()=>i,m:()=>I,n:()=>x,p:()=>g,q:()=>M,r:()=>P,s:()=>a,t:()=>w,u:()=>S,z:()=>d});var n=r(65617),s=r(46851);function i(e){const t=e[0],r=e[1],n=e[2];return Math.sqrt(t*t+r*r+n*n)}function o(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e}function a(e,t,r,n){return e[0]=t,e[1]=r,e[2]=n,e}function l(e,t,r){return e[0]=t[0]+r[0],e[1]=t[1]+r[1],e[2]=t[2]+r[2],e}function c(e,t,r){return e[0]=t[0]-r[0],e[1]=t[1]-r[1],e[2]=t[2]-r[2],e}function h(e,t,r){return e[0]=t[0]*r[0],e[1]=t[1]*r[1],e[2]=t[2]*r[2],e}function u(e,t,r){return e[0]=t[0]/r[0],e[1]=t[1]/r[1],e[2]=t[2]/r[2],e}function f(e,t,r){return e[0]=t[0]*r,e[1]=t[1]*r,e[2]=t[2]*r,e}function d(e,t,r,n){return e[0]=t[0]+r[0]*n,e[1]=t[1]+r[1]*n,e[2]=t[2]+r[2]*n,e}function p(e,t){const r=t[0]-e[0],n=t[1]-e[1],s=t[2]-e[2];return Math.sqrt(r*r+n*n+s*s)}function y(e,t){const r=t[0]-e[0],n=t[1]-e[1],s=t[2]-e[2];return r*r+n*n+s*s}function g(e){const t=e[0],r=e[1],n=e[2];return t*t+r*r+n*n}function x(e,t){const r=t[0],n=t[1],s=t[2];let i=r*r+n*n+s*s;return i>0&&(i=1/Math.sqrt(i),e[0]=t[0]*i,e[1]=t[1]*i,e[2]=t[2]*i),e}function _(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function m(e,t,r){const n=t[0],s=t[1],i=t[2],o=r[0],a=r[1],l=r[2];return e[0]=s*l-i*a,e[1]=i*o-n*l,e[2]=n*a-s*o,e}function b(e,t,r,n){const s=t[0],i=t[1],o=t[2];return e[0]=s+n*(r[0]-s),e[1]=i+n*(r[1]-i),e[2]=o+n*(r[2]-o),e}function I(e,t,r){const n=t[0],s=t[1],i=t[2];return e[0]=r[0]*n+r[4]*s+r[8]*i+r[12],e[1]=r[1]*n+r[5]*s+r[9]*i+r[13],e[2]=r[2]*n+r[6]*s+r[10]*i+r[14],e}function w(e,t,r){const n=t[0],s=t[1],i=t[2];return e[0]=n*r[0]+s*r[3]+i*r[6],e[1]=n*r[1]+s*r[4]+i*r[7],e[2]=n*r[2]+s*r[5]+i*r[8],e}function M(e,t,r){const n=r[0],s=r[1],i=r[2],o=r[3],a=t[0],l=t[1],c=t[2];let h=s*c-i*l,u=i*a-n*c,f=n*l-s*a,d=s*f-i*u,p=i*h-n*f,y=n*u-s*h;const g=2*o;return h*=g,u*=g,f*=g,d*=2,p*=2,y*=2,e[0]=a+h+d,e[1]=l+u+p,e[2]=c+f+y,e}const T=(0,n.c)(),k=(0,n.c)();function v(e,t){return e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]}function P(e,t,r){const n=r[0]-t[0],s=r[1]-t[1],i=r[2]-t[2];let o=n*n+s*s+i*i;return o>0?(o=1/Math.sqrt(o),e[0]=n*o,e[1]=s*o,e[2]=i*o,e):(e[0]=0,e[1]=0,e[2]=0,e)}const L=c,E=h,D=u,A=p,B=y,S=i,V=g;Object.freeze(Object.defineProperty({__proto__:null,abs:function(e,t){return e[0]=Math.abs(t[0]),e[1]=Math.abs(t[1]),e[2]=Math.abs(t[2]),e},add:l,angle:function(e,t){o(T,e),o(k,t),x(T,T),x(k,k);const r=_(T,k);return r>1?0:r<-1?Math.PI:Math.acos(r)},bezier:function(e,t,r,n,s,i){const o=1-i,a=o*o,l=i*i,c=a*o,h=3*i*a,u=3*l*o,f=l*i;return e[0]=t[0]*c+r[0]*h+n[0]*u+s[0]*f,e[1]=t[1]*c+r[1]*h+n[1]*u+s[1]*f,e[2]=t[2]*c+r[2]*h+n[2]*u+s[2]*f,e},ceil:function(e,t){return e[0]=Math.ceil(t[0]),e[1]=Math.ceil(t[1]),e[2]=Math.ceil(t[2]),e},copy:o,cross:m,direction:P,dist:A,distance:p,div:D,divide:u,dot:_,equals:function(e,t){if(e===t)return!0;const r=e[0],n=e[1],i=e[2],o=t[0],a=t[1],l=t[2],c=(0,s.g)();return Math.abs(r-o)<=c*Math.max(1,Math.abs(r),Math.abs(o))&&Math.abs(n-a)<=c*Math.max(1,Math.abs(n),Math.abs(a))&&Math.abs(i-l)<=c*Math.max(1,Math.abs(i),Math.abs(l))},exactEquals:v,floor:function(e,t){return e[0]=Math.floor(t[0]),e[1]=Math.floor(t[1]),e[2]=Math.floor(t[2]),e},hermite:function(e,t,r,n,s,i){const o=i*i,a=o*(2*i-3)+1,l=o*(i-2)+i,c=o*(i-1),h=o*(3-2*i);return e[0]=t[0]*a+r[0]*l+n[0]*c+s[0]*h,e[1]=t[1]*a+r[1]*l+n[1]*c+s[1]*h,e[2]=t[2]*a+r[2]*l+n[2]*c+s[2]*h,e},inverse:function(e,t){return e[0]=1/t[0],e[1]=1/t[1],e[2]=1/t[2],e},len:S,length:i,lerp:b,max:function(e,t,r){return e[0]=Math.max(t[0],r[0]),e[1]=Math.max(t[1],r[1]),e[2]=Math.max(t[2],r[2]),e},min:function(e,t,r){return e[0]=Math.min(t[0],r[0]),e[1]=Math.min(t[1],r[1]),e[2]=Math.min(t[2],r[2]),e},mul:E,multiply:h,negate:function(e,t){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e},normalize:x,random:function(e,t){t=t||1;const r=s.R,n=2*r()*Math.PI,i=2*r()-1,o=Math.sqrt(1-i*i)*t;return e[0]=Math.cos(n)*o,e[1]=Math.sin(n)*o,e[2]=i*t,e},rotateX:function(e,t,r,n){const s=[],i=[];return s[0]=t[0]-r[0],s[1]=t[1]-r[1],s[2]=t[2]-r[2],i[0]=s[0],i[1]=s[1]*Math.cos(n)-s[2]*Math.sin(n),i[2]=s[1]*Math.sin(n)+s[2]*Math.cos(n),e[0]=i[0]+r[0],e[1]=i[1]+r[1],e[2]=i[2]+r[2],e},rotateY:function(e,t,r,n){const s=[],i=[];return s[0]=t[0]-r[0],s[1]=t[1]-r[1],s[2]=t[2]-r[2],i[0]=s[2]*Math.sin(n)+s[0]*Math.cos(n),i[1]=s[1],i[2]=s[2]*Math.cos(n)-s[0]*Math.sin(n),e[0]=i[0]+r[0],e[1]=i[1]+r[1],e[2]=i[2]+r[2],e},rotateZ:function(e,t,r,n){const s=[],i=[];return s[0]=t[0]-r[0],s[1]=t[1]-r[1],s[2]=t[2]-r[2],i[0]=s[0]*Math.cos(n)-s[1]*Math.sin(n),i[1]=s[0]*Math.sin(n)+s[1]*Math.cos(n),i[2]=s[2],e[0]=i[0]+r[0],e[1]=i[1]+r[1],e[2]=i[2]+r[2],e},round:function(e,t){return e[0]=Math.round(t[0]),e[1]=Math.round(t[1]),e[2]=Math.round(t[2]),e},scale:f,scaleAndAdd:d,set:a,sign:function(e,t){return e[0]=Math.sign(t[0]),e[1]=Math.sign(t[1]),e[2]=Math.sign(t[2]),e},sqrDist:B,sqrLen:V,squaredDistance:y,squaredLength:g,str:function(e){return"vec3("+e[0]+", "+e[1]+", "+e[2]+")"},sub:L,subtract:c,transformMat3:w,transformMat4:I,transformQuat:M},Symbol.toStringTag,{value:"Module"}))},65617:(e,t,r)=>{function n(){return[0,0,0]}function s(e){return[e[0],e[1],e[2]]}function i(e,t,r){return[e,t,r]}function o(e){const t=[0,0,0],r=Math.min(3,e.length);for(let n=0;n<r;++n)t[n]=e[n];return t}function a(e,t){return new Float64Array(e,t,3)}function l(){return i(1,1,1)}function c(){return i(1,0,0)}function h(){return i(0,1,0)}function u(){return i(0,0,1)}r.d(t,{O:()=>d,Z:()=>f,a:()=>s,b:()=>a,c:()=>n,d:()=>o,f:()=>i});const f=[0,0,0],d=l(),p=c(),y=h(),g=u();Object.freeze(Object.defineProperty({__proto__:null,ONES:d,UNIT_X:p,UNIT_Y:y,UNIT_Z:g,ZEROS:f,clone:s,create:n,createView:a,fromArray:o,fromValues:i,ones:l,unitX:c,unitY:h,unitZ:u,zeros:function(){return[0,0,0]}},Symbol.toStringTag,{value:"Module"}))},98766:(e,t,r)=>{r.d(t,{a:()=>o,b:()=>h,c:()=>s,d:()=>g,e:()=>d,f:()=>p,g:()=>_,h:()=>m,l:()=>x,n:()=>y,s:()=>i});var n=r(46851);function s(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e}function i(e,t,r,n,s){return e[0]=t,e[1]=r,e[2]=n,e[3]=s,e}function o(e,t,r){return e[0]=t[0]+r[0],e[1]=t[1]+r[1],e[2]=t[2]+r[2],e[3]=t[3]+r[3],e}function a(e,t,r){return e[0]=t[0]-r[0],e[1]=t[1]-r[1],e[2]=t[2]-r[2],e[3]=t[3]-r[3],e}function l(e,t,r){return e[0]=t[0]*r[0],e[1]=t[1]*r[1],e[2]=t[2]*r[2],e[3]=t[3]*r[3],e}function c(e,t,r){return e[0]=t[0]/r[0],e[1]=t[1]/r[1],e[2]=t[2]/r[2],e[3]=t[3]/r[3],e}function h(e,t,r){return e[0]=t[0]*r,e[1]=t[1]*r,e[2]=t[2]*r,e[3]=t[3]*r,e}function u(e,t){const r=t[0]-e[0],n=t[1]-e[1],s=t[2]-e[2],i=t[3]-e[3];return Math.sqrt(r*r+n*n+s*s+i*i)}function f(e,t){const r=t[0]-e[0],n=t[1]-e[1],s=t[2]-e[2],i=t[3]-e[3];return r*r+n*n+s*s+i*i}function d(e){const t=e[0],r=e[1],n=e[2],s=e[3];return Math.sqrt(t*t+r*r+n*n+s*s)}function p(e){const t=e[0],r=e[1],n=e[2],s=e[3];return t*t+r*r+n*n+s*s}function y(e,t){const r=t[0],n=t[1],s=t[2],i=t[3];let o=r*r+n*n+s*s+i*i;return o>0&&(o=1/Math.sqrt(o),e[0]=r*o,e[1]=n*o,e[2]=s*o,e[3]=i*o),e}function g(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3]}function x(e,t,r,n){const s=t[0],i=t[1],o=t[2],a=t[3];return e[0]=s+n*(r[0]-s),e[1]=i+n*(r[1]-i),e[2]=o+n*(r[2]-o),e[3]=a+n*(r[3]-a),e}function _(e,t){return e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]}function m(e,t){const r=e[0],s=e[1],i=e[2],o=e[3],a=t[0],l=t[1],c=t[2],h=t[3],u=(0,n.g)();return Math.abs(r-a)<=u*Math.max(1,Math.abs(r),Math.abs(a))&&Math.abs(s-l)<=u*Math.max(1,Math.abs(s),Math.abs(l))&&Math.abs(i-c)<=u*Math.max(1,Math.abs(i),Math.abs(c))&&Math.abs(o-h)<=u*Math.max(1,Math.abs(o),Math.abs(h))}const b=a,I=l,w=c,M=u,T=f,k=d,v=p;Object.freeze(Object.defineProperty({__proto__:null,add:o,ceil:function(e,t){return e[0]=Math.ceil(t[0]),e[1]=Math.ceil(t[1]),e[2]=Math.ceil(t[2]),e[3]=Math.ceil(t[3]),e},copy:s,dist:M,distance:u,div:w,divide:c,dot:g,equals:m,exactEquals:_,floor:function(e,t){return e[0]=Math.floor(t[0]),e[1]=Math.floor(t[1]),e[2]=Math.floor(t[2]),e[3]=Math.floor(t[3]),e},inverse:function(e,t){return e[0]=1/t[0],e[1]=1/t[1],e[2]=1/t[2],e[3]=1/t[3],e},len:k,length:d,lerp:x,max:function(e,t,r){return e[0]=Math.max(t[0],r[0]),e[1]=Math.max(t[1],r[1]),e[2]=Math.max(t[2],r[2]),e[3]=Math.max(t[3],r[3]),e},min:function(e,t,r){return e[0]=Math.min(t[0],r[0]),e[1]=Math.min(t[1],r[1]),e[2]=Math.min(t[2],r[2]),e[3]=Math.min(t[3],r[3]),e},mul:I,multiply:l,negate:function(e,t){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=-t[3],e},normalize:y,random:function(e,t){const r=n.R;let s,i,o,a,l,c;t=t||1;do{s=2*r()-1,i=2*r()-1,l=s*s+i*i}while(l>=1);do{o=2*r()-1,a=2*r()-1,c=o*o+a*a}while(c>=1);const h=Math.sqrt((1-l)/c);return e[0]=t*s,e[1]=t*i,e[2]=t*o*h,e[3]=t*a*h,e},round:function(e,t){return e[0]=Math.round(t[0]),e[1]=Math.round(t[1]),e[2]=Math.round(t[2]),e[3]=Math.round(t[3]),e},scale:h,scaleAndAdd:function(e,t,r,n){return e[0]=t[0]+r[0]*n,e[1]=t[1]+r[1]*n,e[2]=t[2]+r[2]*n,e[3]=t[3]+r[3]*n,e},set:i,sqrDist:T,sqrLen:v,squaredDistance:f,squaredLength:p,str:function(e){return"vec4("+e[0]+", "+e[1]+", "+e[2]+", "+e[3]+")"},sub:b,subtract:a,transformMat4:function(e,t,r){const n=t[0],s=t[1],i=t[2],o=t[3];return e[0]=r[0]*n+r[4]*s+r[8]*i+r[12]*o,e[1]=r[1]*n+r[5]*s+r[9]*i+r[13]*o,e[2]=r[2]*n+r[6]*s+r[10]*i+r[14]*o,e[3]=r[3]*n+r[7]*s+r[11]*i+r[15]*o,e},transformQuat:function(e,t,r){const n=t[0],s=t[1],i=t[2],o=r[0],a=r[1],l=r[2],c=r[3],h=c*n+a*i-l*s,u=c*s+l*n-o*i,f=c*i+o*s-a*n,d=-o*n-a*s-l*i;return e[0]=h*c+d*-o+u*-l-f*-a,e[1]=u*c+d*-a+f*-o-h*-l,e[2]=f*c+d*-l+h*-a-u*-o,e[3]=t[3],e}},Symbol.toStringTag,{value:"Module"}))},3894:(e,t,r)=>{r.d(t,{Z:()=>i});var n=r(71143);function s(e){e.length=0}class i{constructor(e=50,t=50){this._pool=new n.Z(Array,void 0,s,t,e)}acquire(){return this._pool.acquire()}release(e){this._pool.release(e)}prune(){this._pool.prune(0)}static acquire(){return o.acquire()}static release(e){return o.release(e)}static prune(){o.prune()}}const o=new i(100)},30175:(e,t,r)=>{function n(e,t,r){const n=e.get(t);if(void 0!==n)return n;const s=r();return e.set(t,s),s}r.d(t,{s1:()=>n})},71143:(e,t,r)=>{r.d(t,{Z:()=>n});class n{constructor(e,t,r,n=1,s=0){if(this._ctor=e,this._acquireFunction=t,this._releaseFunction=r,this.allocationSize=n,this._pool=new Array(s),this._initialSize=s,this._ctor)for(let e=0;e<s;e++)this._pool[e]=new this._ctor;this.allocationSize=Math.max(n,1)}destroy(){this.prune(0)}acquire(...e){let t;if(n.test.disabled)t=new this._ctor;else{if(0===this._pool.length){const e=this.allocationSize;for(let t=0;t<e;t++)this._pool[t]=new this._ctor}t=this._pool.pop()}return this._acquireFunction?this._acquireFunction(t,...e):function(e){return e&&e.acquire&&"function"==typeof e.acquire}(t)&&t.acquire(...e),t}release(e){var t;e&&!n.test.disabled&&(this._releaseFunction?this._releaseFunction(e):(t=e)&&t.release&&"function"==typeof t.release&&e.release(),this._pool.push(e))}prune(e=this._initialSize){if(!(e>=this._pool.length)){for(let t=e;t<this._pool.length;++t){const e=this._pool[t];this._dispose(e)}this._pool.length=e}}_dispose(e){e.dispose&&"function"==typeof e.dispose&&e.dispose()}}n.test={disabled:!1}},75215:(e,t,r)=>{r.d(t,{AY:()=>A,Iu:()=>T,Ls:()=>I,N7:()=>L,TJ:()=>g,V5:()=>D,Zs:()=>l,di:()=>k,gB:()=>v,i$:()=>P,q9:()=>c,rY:()=>w,sY:()=>B,se:()=>b,vU:()=>h,z8:()=>E}),r(80442);var n=r(92604),s=r(30175);const i=n.Z.getLogger("esri.core.accessorSupport.ensureTypes");function o(e){return null==e?e:new Date(e)}function a(e){return null==e?e:!!e}function l(e){return null==e?e:e.toString()}function c(e){return null==e?e:(e=parseFloat(e),isNaN(e)?0:e)}function h(e){return null==e?e:Math.round(parseFloat(e))}function u(e){return e&&e.constructor&&void 0!==e.constructor.__accessorMetadata__}function f(e,t){return null!=t&&e&&!(t instanceof e)}function d(e){return e&&"isCollection"in e}function p(e){return e&&e.Type?"function"==typeof e.Type?e.Type:e.Type.base:null}function y(e,t){return!!u(t)&&(i.error("Accessor#set","Assigning an instance of '"+(t.declaredClass||"unknown")+"' which is not a subclass of '"+x(e)+"'"),!0)}function g(e,t){return null==t?t:d(e)?function(e,t){if(!t||!t.constructor||!d(t.constructor))return y(e,t)?t:new e(t);const r=p(e.prototype.itemType),n=p(t.constructor.prototype.itemType);return r?n?r===n?t:r.prototype.isPrototypeOf(n.prototype)?new e(t):(y(e,t),t):new e(t):t}(e,t):f(e,t)?y(e,t)?t:new e(t):t}function x(e){return e&&e.prototype&&e.prototype.declaredClass||"unknown"}const _=new WeakMap;function m(e){switch(e){case Number:return c;case E:return h;case Boolean:return a;case String:return l;case Date:return o;default:return(0,s.s1)(_,e,(()=>g.bind(null,e)))}}function b(e,t){const r=m(e);return 1===arguments.length?r:r(t)}function I(e,t,r){return 1===arguments.length?I.bind(null,e):t?Array.isArray(t)?t.map((t=>e(t,r))):[e(t,r)]:t}function w(e,t){return 1===arguments.length?I(b.bind(null,e)):I(b.bind(null,e),t)}function M(e,t,r){return 0!==t&&Array.isArray(r)?r.map((r=>M(e,t-1,r))):e(r)}function T(e,t,r){if(2===arguments.length)return T.bind(null,e,t);if(!r)return r;let n=t,s=r=M(e,t,r);for(;n>0&&Array.isArray(s);)n--,s=s[0];if(void 0!==s)for(let e=0;e<n;e++)r=[r];return r}function k(e,t,r){return 2===arguments.length?T(b.bind(null,e),t):T(b.bind(null,e),t,r)}function v(e){return!!Array.isArray(e)&&!e.some((t=>{const r=typeof t;return!("string"===r||"number"===r||"function"===r&&e.length>1)}))}function P(e,t){if(2===arguments.length)return P(e).call(null,t);const r=new Set,n=e.filter((e=>"function"!=typeof e)),s=e.filter((e=>"function"==typeof e));for(const t of e)"string"!=typeof t&&"number"!=typeof t||r.add(t);let o=null,a=null;return(e,t)=>{if(null==e)return e;const l=typeof e,c="string"===l||"number"===l;return c&&(r.has(e)||s.some((e=>"string"===l&&e===String||"number"===l&&e===Number)))||"object"===l&&s.some((t=>!f(e,t)))?e:(c&&n.length?(o||(o=n.map((e=>"string"==typeof e?`'${e}'`:`${e}`)).join(", ")),i.error("Accessor#set",`'${e}' is not a valid value for this property, only the following values are valid: ${o}`)):"object"==typeof e&&s.length?(a||(a=s.map((e=>x(e))).join(", ")),i.error("Accessor#set",`'${e}' is not a valid value for this property, value must be one of ${a}`)):i.error("Accessor#set",`'${e}' is not a valid value for this property`),t&&(t.valid=!1),null)}}function L(e,t){if(2===arguments.length)return L(e).call(null,t);const r={},n=[],s=[];for(const t in e.typeMap){const i=e.typeMap[t];r[t]=b(i),n.push(x(i)),s.push(t)}const o=()=>`'${n.join("', '")}'`,a=()=>`'${s.join("', '")}'`,l="string"==typeof e.key?t=>t[e.key]:e.key;return t=>{if(e.base&&!f(e.base,t))return t;if(null==t)return t;const n=l(t)||e.defaultKeyValue,s=r[n];if(!s)return i.error("Accessor#set",`Invalid property value, value needs to be one of ${o()}, or a plain object that can autocast (having .type = ${a()})`),null;if(!f(e.typeMap[n],t))return t;if("string"==typeof e.key&&!u(t)){const r={};for(const n in t)n!==e.key&&(r[n]=t[n]);return s(r)}return s(t)}}class E{}const D={native:e=>({type:"native",value:e}),array:e=>({type:"array",value:e}),oneOf:e=>({type:"one-of",values:e})};function A(e){if(!e||!("type"in e))return!1;switch(e.type){case"native":case"array":case"one-of":return!0}return!1}function B(e){switch(e.type){case"native":return b(e.value);case"array":return I(B(e.value));case"one-of":return function(e){let t=null;return(r,n)=>V(r,e)?r:(null==t&&(t=S(e)),i.error("Accessor#set",`Invalid property value, value needs to be of type ${t}`),n&&(n.valid=!1),null)}(e);default:return null}}function S(e){switch(e.type){case"native":switch(e.value){case Number:return"number";case String:return"string";case Boolean:return"boolean";case E:return"integer";case Date:return"date";default:return x(e.value)}case"array":return`array of ${S(e.value)}`;case"one-of":{const t=e.values.map((e=>S(e)));return`one of ${t.slice(0,t.length-1)} or ${t[t.length-1]}`}}return"unknown"}function V(e,t){if(null==e)return!0;switch(t.type){case"native":switch(t.value){case Number:case E:return"number"==typeof e;case Boolean:return"boolean"==typeof e;case String:return"string"==typeof e}return e instanceof t.value;case"array":return!!Array.isArray(e)&&!e.some((e=>!V(e,t.value)));case"one-of":return t.values.some((t=>V(e,t)))}}},22021:(e,t,r)=>{r.d(t,{BV:()=>h,Kt:()=>f,Sf:()=>i,Vl:()=>c,ZF:()=>u,_3:()=>b,jE:()=>_,oK:()=>g,oc:()=>m,t7:()=>l,uZ:()=>o,wt:()=>a});var n=r(17896);r(98766);const s=new Float32Array(1);function i(e){--e;for(let t=1;t<32;t<<=1)e|=e>>t;return e+1}function o(e,t,r){return Math.min(Math.max(e,t),r)}function a(e){return 0==(e&e-1)}function l(e,t,r){return e+(t-e)*r}function c(e){return e*Math.PI/180}function h(e){return 180*e/Math.PI}function u(e){return Math.acos(o(e,-1,1))}function f(e){return Math.asin(o(e,-1,1))}function d(e,t,r=1e-6){return e===t||!(!Number.isFinite(e)||!Number.isFinite(t))&&(e>t?e-t:t-e)<=r}const p=new DataView(new ArrayBuffer(Float64Array.BYTES_PER_ELEMENT));const y=BigInt("1000000");function g(e){return x(Math.max(-b,Math.min(e,b)))}function x(e){return s[0]=e,s[0]}function _(e,t){const r=(0,n.l)(e),s=f(e[2]/r),i=Math.atan2(e[1]/r,e[0]/r);return(0,n.s)(t,r,s,i),t}function m(e){const t=e[0]*e[0]+e[1]*e[1]+e[2]*e[2],r=e[3]*e[3]+e[4]*e[4]+e[5]*e[5],n=e[6]*e[6]+e[7]*e[7]+e[8]*e[8];return!(d(t,1)&&d(r,1)&&d(n,1))}!function(e){const t=function(e){return p.setFloat64(0,e),p.getBigInt64(0)}(e=Math.abs(e)),r=function(e){return p.setBigInt64(0,e),p.getFloat64(0)}(t<=y?y:t-y);Math.abs(e-r)}(1);const b=x(34028234663852886e22)},45091:(e,t,r)=>{r.d(t,{Z:()=>h});var n,s,i=r(80442),o=r(71143);(s=n||(n={}))[s.varint=0]="varint",s[s.fixed64=1]="fixed64",s[s.delimited=2]="delimited",s[s.fixed32=5]="fixed32",s[s.unknown=99]="unknown";const a=4294967296,l=new TextDecoder("utf-8"),c=(0,i.Z)("safari")||(0,i.Z)("ios")?6:(0,i.Z)("ff")?12:32;class h{constructor(e,t,r=0,s=(e?e.byteLength:0)){this._tag=0,this._dataType=n.unknown,this._init(e,t,r,s)}_init(e,t,r,n){this._data=e,this._dataView=t,this._pos=r,this._end=n}asUnsafe(){return this}clone(){return new h(this._data,this._dataView,this._pos,this._end)}pos(){return this._pos}move(e){this._pos=e}nextTag(e){for(;;){if(this._pos===this._end)return!1;const t=this._decodeVarint();if(this._tag=t>>3,this._dataType=7&t,!e||e===this._tag)break;this.skip()}return!0}next(){if(this._pos===this._end)return!1;const e=this._decodeVarint();return this._tag=e>>3,this._dataType=7&e,!0}empty(){return this._pos>=this._end}tag(){return this._tag}getInt32(){return this._decodeVarint()}getInt64(){return this._decodeVarint()}getUInt32(){let e=4294967295;return e=(127&this._data[this._pos])>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<7)>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<14)>>>0,this._data[this._pos++]<128?e:(e=(e|(127&this._data[this._pos])<<21)>>>0,this._data[this._pos++]<128?e:(e=(e|(15&this._data[this._pos])<<28)>>>0,this._data[this._pos++]<128?e:void 0))))}getUInt64(){return this._decodeVarint()}getSInt32(){const e=this.getUInt32();if(void 0!==e)return e>>>1^-(1&e)|0}getSInt64(){return this._decodeSVarint()}getBool(){const e=0!==this._data[this._pos];return this._skip(1),e}getEnum(){return this._decodeVarint()}getFixed64(){const e=this._dataView,t=this._pos,r=e.getUint32(t,!0)+e.getUint32(t+4,!0)*a;return this._skip(8),r}getSFixed64(){const e=this._dataView,t=this._pos,r=e.getUint32(t,!0)+e.getInt32(t+4,!0)*a;return this._skip(8),r}getDouble(){const e=this._dataView.getFloat64(this._pos,!0);return this._skip(8),e}getFixed32(){const e=this._dataView.getUint32(this._pos,!0);return this._skip(4),e}getSFixed32(){const e=this._dataView.getInt32(this._pos,!0);return this._skip(4),e}getFloat(){const e=this._dataView.getFloat32(this._pos,!0);return this._skip(4),e}getString(){const e=this._getLength(),t=this._pos,r=this._toString(this._data,t,t+e);return this._skip(e),r}getBytes(){const e=this._getLength(),t=this._pos,r=this._toBytes(this._data,t,t+e);return this._skip(e),r}getLength(){return this._getLengthUnsafe()}processMessageWithArgs(e,t,r,n){const s=this.getMessage(),i=e(s,t,r,n);return s.release(),i}processMessage(e){const t=this.getMessage(),r=e(t);return t.release(),r}getMessage(){const e=this._getLength(),t=h.pool.acquire();return t._init(this._data,this._dataView,this._pos,this._pos+e),this._skip(e),t}release(){h.pool.release(this)}dataType(){return this._dataType}skip(){switch(this._dataType){case n.varint:this._decodeVarint();break;case n.fixed64:this._skip(8);break;case n.delimited:this._skip(this._getLength());break;case n.fixed32:this._skip(4);break;default:throw new Error("Invalid data type!")}}skipLen(e){this._skip(e)}_skip(e){if(this._pos+e>this._end)throw new Error("Attempt to skip past the end of buffer!");this._pos+=e}_decodeVarint(){const e=this._data;let t=this._pos,r=0,n=0;if(this._end-t>=10)do{if(n=e[t++],r|=127&n,0==(128&n))break;if(n=e[t++],r|=(127&n)<<7,0==(128&n))break;if(n=e[t++],r|=(127&n)<<14,0==(128&n))break;if(n=e[t++],r|=(127&n)<<21,0==(128&n))break;if(n=e[t++],r+=268435456*(127&n),0==(128&n))break;if(n=e[t++],r+=34359738368*(127&n),0==(128&n))break;if(n=e[t++],r+=4398046511104*(127&n),0==(128&n))break;if(n=e[t++],r+=562949953421312*(127&n),0==(128&n))break;if(n=e[t++],r+=72057594037927940*(127&n),0==(128&n))break;if(n=e[t++],r+=0x8000000000000000*(127&n),0==(128&n))break;throw new Error("Varint too long!")}while(0);else{let s=1;for(;t!==this._end&&(n=e[t],0!=(128&n));)++t,r+=(127&n)*s,s*=128;if(t===this._end)throw new Error("Varint overrun!");++t,r+=n*s}return this._pos=t,r}_decodeSVarint(){const e=this._data;let t=this._pos,r=0,n=0;const s=1&e[t];if(this._end-t>=10)do{if(n=e[t++],r|=127&n,0==(128&n))break;if(n=e[t++],r|=(127&n)<<7,0==(128&n))break;if(n=e[t++],r|=(127&n)<<14,0==(128&n))break;if(n=e[t++],r|=(127&n)<<21,0==(128&n))break;if(n=e[t++],r+=268435456*(127&n),0==(128&n))break;if(n=e[t++],r+=34359738368*(127&n),0==(128&n))break;if(n=e[t++],r+=4398046511104*(127&n),0==(128&n))break;if(n=e[t++],r+=562949953421312*(127&n),0==(128&n))break;if(n=e[t++],r+=72057594037927940*(127&n),0==(128&n))break;if(n=e[t++],r+=0x8000000000000000*(127&n),0==(128&n))break;throw new Error("Varint too long!")}while(0);else{let s=1;for(;t!==this._end&&(n=e[t],0!=(128&n));)++t,r+=(127&n)*s,s*=128;if(t===this._end)throw new Error("Varint overrun!");++t,r+=n*s}return this._pos=t,s?-(r+1)/2:r/2}_getLength(){if(this._dataType!==n.delimited)throw new Error("Not a delimited data type!");return this._decodeVarint()}_getLengthUnsafe(){return this.getUInt32()}_toString(e,t,r){if((r=Math.min(this._end,r))-t>c){const n=e.subarray(t,r);return l.decode(n)}let n="",s="";for(let i=t;i<r;++i){const t=e[i];128&t?s+="%"+t.toString(16):(n+=decodeURIComponent(s)+String.fromCharCode(t),s="")}return s.length&&(n+=decodeURIComponent(s)),n}_toBytes(e,t,r){return r=Math.min(this._end,r),new Uint8Array(e.buffer,t,r-t)}}h.pool=new o.Z(h,void 0,(e=>{e._data=null,e._dataView=null}))},3172:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var n=r(68773),s=r(40330),i=r(20102),o=r(80442),a=r(22974),l=r(70586),c=r(95330),h=r(17452),u=r(19745),f=r(71058),d=r(85958);async function p(e,t){const a=(0,h.HK)(e),u=(0,h.jc)(e);u||a||(e=(0,h.Fv)(e));const _={url:e,requestOptions:{...(0,l.Wg)(t)}};let m=(0,h.oh)(e);if(m){const e=await async function(e,t){if(null!=e.responseData)return e.responseData;if(e.headers&&(t.requestOptions.headers={...t.requestOptions.headers,...e.headers}),e.query&&(t.requestOptions.query={...t.requestOptions.query,...e.query}),e.before){let r,n;try{n=await e.before(t)}catch(e){r=T("request:interceptor",e,t)}if((n instanceof Error||n instanceof i.Z)&&(r=T("request:interceptor",n,t)),r)throw e.error&&e.error(r),r;return n}}(m,_);if(null!=e)return{data:e,getHeader:I,httpStatus:200,requestOptions:_.requestOptions,url:_.url};m.after||m.error||(m=null)}if(e=_.url,"image"===(t=_.requestOptions).responseType){if((0,o.Z)("host-webworker")||(0,o.Z)("host-node"))throw T("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),_)}else if(a)throw T("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+t.responseType),_);if("head"===t.method){if(t.body)throw T("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),_);if(a||u)throw T("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),_)}if(await async function(){(0,o.Z)("host-webworker")?y||(y=await r.e(9884).then(r.bind(r,29884))):p._abortableFetch||(p._abortableFetch=globalThis.fetch.bind(globalThis))}(),y)return y.execute(e,t);const b=new AbortController;(0,c.fu)(t,(()=>b.abort()));const w={controller:b,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:m,params:_,redoRequest:!1,useIdentity:g.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},M=await async function(e){let t,r;await async function(e){const t=e.params.url,r=e.params.requestOptions,i=e.controller.signal,o=r.body;let a=null,l=null;if(x&&"HTMLFormElement"in globalThis&&(o instanceof FormData?a=o:o instanceof HTMLFormElement&&(a=new FormData(o))),"string"==typeof o&&(l=o),e.fetchOptions={cache:r.cacheBust&&!p._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:r.headers||{},method:"head"===r.method?"HEAD":"GET",mode:"cors",priority:g.priority,redirect:"follow",signal:i},(a||l)&&(e.fetchOptions.body=a||l),"anonymous"===r.authMode&&(e.useIdentity=!1),e.hasToken=!!(/token=/i.test(t)||r.query?.token||a?.get("token")),!e.hasToken&&n.Z.apiKey&&(0,f.r)(t)&&(r.query||(r.query={}),r.query.token=n.Z.apiKey,e.hasToken=!0),e.useIdentity&&!e.hasToken&&!e.credentialToken&&!v(t)&&!(0,c.Hc)(i)){let n;"immediate"===r.authMode?(await k(),n=await s.id.getCredential(t,{signal:i}),e.credential=n):"no-prompt"===r.authMode?(await k(),n=await s.id.getCredential(t,{prompt:!1,signal:i}).catch((()=>{})),e.credential=n):s.id&&(n=s.id.findCredential(t)),n&&(e.credentialToken=n.token,e.useSSL=!!n.ssl)}}(e);try{do{[t,r]=await P(e)}while(!await E(e,t,r))}catch(r){const n=T("request:server",r,e.params,t);throw n.details.ssl=e.useSSL,e.interceptor&&e.interceptor.error&&e.interceptor.error(n),n}const i=e.params.url;if(r&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(i)){if(!e.hasToken&&!e.credentialToken&&r.user?.username&&!(0,h.kl)(i)){const e=(0,h.P$)(i,!0);e&&g.trustedServers.push(e)}Array.isArray(r.authorizedCrossOriginNoCorsDomains)&&(0,d.Hu)(r.authorizedCrossOriginNoCorsDomains)}const o=e.credential;if(o&&s.id){const e=s.id.findServerInfo(o.server);let t=e&&e.owningSystemUrl;if(t){t=t.replace(/\/?$/,"/sharing");const e=s.id.findCredential(t,o.userId);e&&-1===s.id._getIdenticalSvcIdx(t,e)&&e.resources.unshift(t)}}return{data:r,getHeader:t?e=>t?.headers.get(e):I,httpStatus:t?.status??200,requestOptions:e.params.requestOptions,ssl:e.useSSL,url:e.params.url}}(w);return m?.after?.(M),M}let y;const g=n.Z.request,x="FormData"in globalThis,_=[499,498,403,401],m=["COM_0056","COM_0057","SB_0008"],b=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],I=()=>null,w=Symbol();function M(e){const t=(0,h.P$)(e);return!t||t.endsWith(".arcgis.com")||p._corsServers.includes(t)||(0,h.kl)(t)}function T(e,t,r,n){let s="Error";const o={url:r.url,requestOptions:r.requestOptions,getHeader:I,ssl:!1};if(t instanceof i.Z)return t.details?(t.details=(0,a.d9)(t.details),t.details.url=r.url,t.details.requestOptions=r.requestOptions):t.details=o,t;if(t){const e=n&&(e=>n.headers.get(e)),r=n&&n.status,i=t.message;i&&(s=i),e&&(o.getHeader=e),o.httpStatus=(null!=t.httpCode?t.httpCode:t.code)||r||0,o.subCode=t.subcode,o.messageCode=t.messageCode,"string"==typeof t.details?o.messages=[t.details]:o.messages=t.details,o.raw=w in t?t[w]:t}return(0,c.D_)(t)?(0,c.zE)():new i.Z(e,s,o)}async function k(){s.id||await Promise.all([r.e(6261),r.e(1400),r.e(450)]).then(r.bind(r,73660))}function v(e){return b.some((t=>t.test(e)))}async function P(e){let t=e.params.url;const r=e.params.requestOptions,n=e.fetchOptions??{},i=(0,h.jc)(t)||(0,h.HK)(t),a=r.responseType||"json",l=i?0:null!=r.timeout?r.timeout:g.timeout;let f=!1;if(!i){e.useSSL&&(t=(0,h.hO)(t)),r.cacheBust&&"default"===n.cache&&(t=(0,h.ZN)(t,"request.preventCache",Date.now()));let i={...r.query};e.credentialToken&&(i.token=e.credentialToken);let a=(0,h.B7)(i);(0,o.Z)("esri-url-encodes-apostrophe")&&(a=a.replace(/'/g,"%27"));const l=t.length+1+a.length;let c;f="delete"===r.method||"post"===r.method||"put"===r.method||!!r.body||l>g.maxUrlLength;const p=r.useProxy||!!(0,h.ed)(t);if(p){const e=(0,h.b7)(t);c=e.path,!f&&c.length+1+l>g.maxUrlLength&&(f=!0),e.query&&(i={...e.query,...i})}if("HEAD"===n.method&&(f||p)){if(f){if(l>g.maxUrlLength)throw T("request:invalid-parameters",new Error("URL exceeds maximum length"),e.params);throw T("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),e.params)}if(p)throw T("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),e.params)}if(f?(n.method="delete"===r.method?"DELETE":"put"===r.method?"PUT":"POST",r.body?t=(0,h.fl)(t,i):(n.body=(0,h.B7)(i),n.headers||(n.headers={}),n.headers["Content-Type"]="application/x-www-form-urlencoded")):t=(0,h.fl)(t,i),p&&(e.useProxy=!0,t=`${c}?${t}`),i.token&&x&&n.body instanceof FormData&&!(0,u.P)(t)&&n.body.set("token",i.token),r.hasOwnProperty("withCredentials"))e.withCredentials=r.withCredentials;else if(!(0,h.D6)(t,(0,h.TI)()))if((0,h.kl)(t))e.withCredentials=!0;else if(s.id){const r=s.id.findServerInfo(t);r&&r.webTierAuth&&(e.withCredentials=!0)}e.withCredentials&&(n.credentials="include",(0,d.jH)(t)&&await(0,d.jz)(f?(0,h.fl)(t,i):t))}let y,_,m=0,b=!1;l>0&&(m=setTimeout((()=>{b=!0,e.controller.abort()}),l));try{if("native-request-init"===r.responseType)_=n,_.url=t;else if("image"!==r.responseType||"default"!==n.cache||"GET"!==n.method||f||function(e){if(e)for(const t of Object.getOwnPropertyNames(e))if(e[t])return!0;return!1}(r.headers)||!i&&!e.useProxy&&g.proxyUrl&&!M(t)){if(y=await p._abortableFetch(t,n),e.useProxy||function(e){const t=(0,h.P$)(e);t&&!p._corsServers.includes(t)&&p._corsServers.push(t)}(t),"native"===r.responseType)_=y;else if("HEAD"!==n.method)if(y.ok){switch(a){case"array-buffer":_=await y.arrayBuffer();break;case"blob":case"image":_=await y.blob();break;default:_=await y.text()}if(m&&(clearTimeout(m),m=0),"json"===a||"xml"===a||"document"===a)if(_)switch(a){case"json":_=JSON.parse(_);break;case"xml":_=L(_,"application/xml");break;case"document":_=L(_,"text/html")}else _=null;if(_){if("array-buffer"===a||"blob"===a){const e=y.headers.get("Content-Type");if(e&&/application\/json|text\/plain/i.test(e)&&_["blob"===a?"size":"byteLength"]<=750)try{const e=await new Response(_).json();e.error&&(_=e)}catch{}}"image"===a&&_ instanceof Blob&&(_=await D(URL.createObjectURL(_),e,!0))}}else _=await y.text()}else _=await D(t,e)}catch(n){if("AbortError"===n.name){if(b)throw new Error("Timeout exceeded");throw(0,c.zE)("Request canceled")}if(!(!y&&n instanceof TypeError&&g.proxyUrl)||r.body||"delete"===r.method||"head"===r.method||"post"===r.method||"put"===r.method||e.useProxy||M(t))throw n;e.redoRequest=!0,(0,h.tD)({proxyUrl:g.proxyUrl,urlPrefix:(0,h.P$)(t)??""})}finally{m&&clearTimeout(m)}return[y,_]}function L(e,t){let r;try{r=(new DOMParser).parseFromString(e,t)}catch{}if(!r||r.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return r}async function E(e,t,r){if(e.redoRequest)return e.redoRequest=!1,!1;const n=e.params.requestOptions;if(!t||"native"===n.responseType||"native-request-init"===n.responseType)return!0;let i,o;if(!t.ok)throw i=new Error(`Unable to load ${t.url} status: ${t.status}`),i[w]=r,i;r&&(r.error?i=r.error:"error"===r.status&&Array.isArray(r.messages)&&(i={...r},i[w]=r,i.details=r.messages));let a,l=null;i&&(o=Number(i.code),l=i.hasOwnProperty("subcode")?Number(i.subcode):null,a=i.messageCode,a=a&&a.toUpperCase());const c=n.authMode;if(403===o&&(4===l||i.message&&i.message.toLowerCase().includes("ssl")&&!i.message.toLowerCase().includes("permission"))){if(!e.useSSL)return e.useSSL=!0,!1}else if(!e.hasToken&&e.useIdentity&&("no-prompt"!==c||498===o)&&void 0!==o&&_.includes(o)&&!v(e.params.url)&&(403!==o||a&&!m.includes(a)&&(null==l||2===l&&e.credentialToken))){await k();try{const t=await s.id.getCredential(e.params.url,{error:T("request:server",i,e.params),prompt:"no-prompt"!==c,signal:e.controller.signal,token:e.credentialToken});return e.credential=t,e.credentialToken=t.token,e.useSSL=e.useSSL||t.ssl,!1}catch(t){if("no-prompt"===c)return e.credential=void 0,e.credentialToken=void 0,!1;i=t}}if(i)throw i;return!0}function D(e,t,r=!1){const n=t.controller.signal,s=new Image;return t.withCredentials?s.crossOrigin="use-credentials":s.crossOrigin="anonymous",s.alt="",s.fetchPriority=g.priority,s.src=e,(0,d.fY)(s,e,r,n)}p._abortableFetch=null,p._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},71058:(e,t,r)=>{r.d(t,{r:()=>i});var n=r(17452);const s=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function i(e){const t=(0,n.P$)(e,!0);return!!t&&t.endsWith(".arcgis.com")&&!s.includes(t)&&!e.endsWith("/sharing/rest/generateToken")}},85958:(e,t,r)=>{r.d(t,{Hu:()=>h,fY:()=>l,jH:()=>u,jz:()=>f});var n=r(68773),s=r(80442),i=r(70586),o=r(95330),a=r(17452);function l(e,t,r=!1,n){return new Promise(((a,l)=>{if((0,o.Hc)(n))return void l(c());let h=()=>{d(),l(new Error(`Unable to load ${t}`))},u=()=>{const t=e;d(),a(t)},f=()=>{if(!e)return;const t=e;d(),t.src="",l(c())};const d=()=>{(0,s.Z)("esri-image-decode")||(e.removeEventListener("error",h),e.removeEventListener("load",u)),h=null,u=null,e=null,(0,i.pC)(n)&&n.removeEventListener("abort",f),f=null,r&&URL.revokeObjectURL(t)};(0,i.pC)(n)&&n.addEventListener("abort",f),(0,s.Z)("esri-image-decode")?e.decode().then(u,h):(e.addEventListener("error",h),e.addEventListener("load",u))}))}function c(){try{return new DOMException("Aborted","AbortError")}catch{const e=new Error;return e.name="AbortError",e}}function h(e){n.Z.request.crossOriginNoCorsDomains||(n.Z.request.crossOriginNoCorsDomains={});const t=n.Z.request.crossOriginNoCorsDomains;for(let r of e)r=r.toLowerCase(),/^https?:\/\//.test(r)?t[(0,a.P$)(r)??""]=0:(t[(0,a.P$)("http://"+r)??""]=0,t[(0,a.P$)("https://"+r)??""]=0)}function u(e){const t=n.Z.request.crossOriginNoCorsDomains;if(t){let r=(0,a.P$)(e);if(r)return r=r.toLowerCase(),!(0,a.D6)(r,(0,a.TI)())&&t[r]<Date.now()-36e5}return!1}async function f(e){const t=n.Z.request.crossOriginNoCorsDomains,r=(0,a.P$)(e);t&&r&&(t[r.toLowerCase()]=Date.now());const s=(0,a.mN)(e);e=s.path,"json"===s.query?.f&&(e+="?f=json");try{await fetch(e,{mode:"no-cors",credentials:"include"})}catch{}}},56456:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Q});var n=r(95330),s=r(4215),i=r(70586),o=r(59255),a=r(31363);function l(e){return 746===e||747===e||!(e<4352)&&(e>=12704&&e<=12735||e>=12544&&e<=12591||e>=65072&&e<=65103&&!(e>=65097&&e<=65103)||e>=63744&&e<=64255||e>=13056&&e<=13311||e>=11904&&e<=12031||e>=12736&&e<=12783||e>=12288&&e<=12351&&!(e>=12296&&e<=12305||e>=12308&&e<=12319||12336===e)||e>=13312&&e<=19903||e>=19968&&e<=40959||e>=12800&&e<=13055||e>=12592&&e<=12687||e>=43360&&e<=43391||e>=55216&&e<=55295||e>=4352&&e<=4607||e>=44032&&e<=55215||e>=12352&&e<=12447||e>=12272&&e<=12287||e>=12688&&e<=12703||e>=12032&&e<=12255||e>=12784&&e<=12799||e>=12448&&e<=12543&&12540!==e||e>=65280&&e<=65519&&!(65288===e||65289===e||65293===e||e>=65306&&e<=65310||65339===e||65341===e||65343===e||e>=65371&&e<=65503||65507===e||e>=65512&&e<=65519)||e>=65104&&e<=65135&&!(e>=65112&&e<=65118||e>=65123&&e<=65126)||e>=5120&&e<=5759||e>=6320&&e<=6399||e>=65040&&e<=65055||e>=19904&&e<=19967||e>=40960&&e<=42127||e>=42128&&e<=42191)}function c(e){return!(e<11904)&&(e>=12704&&e<=12735||e>=12544&&e<=12591||e>=65072&&e<=65103||e>=63744&&e<=64255||e>=13056&&e<=13311||e>=11904&&e<=12031||e>=12736&&e<=12783||e>=12288&&e<=12351||e>=13312&&e<=19903||e>=19968&&e<=40959||e>=12800&&e<=13055||e>=65280&&e<=65519||e>=12352&&e<=12447||e>=12272&&e<=12287||e>=12032&&e<=12255||e>=12784&&e<=12799||e>=12448&&e<=12543||e>=65040&&e<=65055||e>=42128&&e<=42191||e>=40960&&e<=42127)}function h(e){switch(e){case 10:case 32:case 38:case 40:case 41:case 43:case 45:case 47:case 173:case 183:case 8203:case 8208:case 8211:case 8231:return!0}return!1}function u(e){switch(e){case 9:case 10:case 11:case 12:case 13:case 32:return!0}return!1}var f=r(87893);const d=24;class p{constructor(e,t,r,n,s,i,o){this._glyphItems=e,this._maxWidth=t,this._lineHeight=r,this._letterSpacing=n,this._hAnchor=s,this._vAnchor=i,this._justify=o}getShaping(e,t,r){const n=this._letterSpacing,s=this._lineHeight,i=this._justify,o=this._maxWidth,a=[];let f=0,d=0;const p=e.length;for(let t=0;t<p;t++){const s=e.charCodeAt(t),i=r&&l(s);let o;for(const e of this._glyphItems)if(o=e[s],o)break;a.push({codePoint:s,x:f,y:d,vertical:i,glyphMosaicItem:o}),o&&(f+=o.metrics.advance+n)}let y=f;o>0&&(y=f/Math.max(1,Math.ceil(f/o)));const g=e.includes("​"),x=[];for(let e=0;e<p-1;e++){const t=a[e].codePoint,r=c(t);if(h(t)||r){let n=0;if(10===t)n-=1e4;else if(r&&g)n+=150;else{40!==t&&65288!==t||(n+=50);const r=a[e+1].codePoint;41!==r&&65289!==r||(n+=50)}x.push(this._buildBreak(e+1,a[e].x,y,x,n,!1))}}const _=this._optimalBreaks(this._buildBreak(p,f,y,x,0,!0));let m=0;const b=t?-s:s;let I=0;for(let e=0;e<_.length;e++){const t=_[e];let r=I;for(;r<t&&u(a[r].codePoint);)a[r].glyphMosaicItem=null,++r;let n=t-1;for(;n>r&&u(a[n].codePoint);)a[n].glyphMosaicItem=null,--n;if(r<=n){const e=a[r].x;for(let t=r;t<=n;t++)a[t].x-=e,a[t].y=d;let t=a[n].x;a[n].glyphMosaicItem&&(t+=a[n].glyphMosaicItem.metrics.advance),m=Math.max(t,m),i&&this._applyJustification(a,r,n)}I=t,d+=b}if(a.length>0){const e=_.length-1,r=(i-this._hAnchor)*m;let n=(-this._vAnchor*(e+1)+.5)*s;t&&e&&(n+=e*s);for(const e of a)e.x+=r,e.y+=n}return a.filter((e=>e.glyphMosaicItem))}static getTextBox(e,t){if(!e.length)return null;let r=1/0,n=1/0,s=0,i=0;for(const o of e){const e=o.glyphMosaicItem.metrics.advance,a=o.x,l=o.y-17,c=a+e,h=l+t;r=Math.min(r,a),s=Math.max(s,c),n=Math.min(n,l),i=Math.max(i,h)}return{x:r,y:n,width:s-r,height:i-n}}static getBox(e){if(!e.length)return null;let t=1/0,r=1/0,n=0,s=0;for(const i of e){const{height:e,left:o,top:a,width:l}=i.glyphMosaicItem.metrics,c=i.x,h=i.y-(e-Math.abs(a)),u=c+l+o,f=h+e;t=Math.min(t,c),n=Math.max(n,u),r=Math.min(r,h),s=Math.max(s,f)}return{x:t,y:r,width:n-t,height:s-r}}static addDecoration(e,t){const r=e.length;if(0===r)return;let n=e[0].x+e[0].glyphMosaicItem.metrics.left,s=e[0].y;for(let i=1;i<r;i++){const r=e[i];if(r.y!==s){const o=e[i-1].x+e[i-1].glyphMosaicItem.metrics.left+e[i-1].glyphMosaicItem.metrics.width;e.push({codePoint:0,x:n,y:s+t-3,vertical:!1,glyphMosaicItem:{sdf:!0,rect:new f.Z(4,0,4,8),metrics:{width:o-n,height:8,left:0,top:0,advance:0},page:0,code:0}}),s=r.y,n=r.x+r.glyphMosaicItem.metrics.left}}const i=e[r-1].x+e[r-1].glyphMosaicItem.metrics.left+e[r-1].glyphMosaicItem.metrics.width;e.push({codePoint:0,x:n,y:s+t-3,vertical:!1,glyphMosaicItem:{sdf:!0,rect:new f.Z(4,0,4,8),metrics:{width:i-n,height:8,left:0,top:0,advance:0},page:0,code:0}})}_breakScore(e,t,r,n){const s=(e-t)*(e-t);return n?e<t?s/2:2*s:s+Math.abs(r)*r}_buildBreak(e,t,r,n,s,i){let o=null,a=this._breakScore(t,r,s,i);for(const e of n){const n=t-e.x,l=this._breakScore(n,r,s,i)+e.score;l<=a&&(o=e,a=l)}return{index:e,x:t,score:a,previousBreak:o}}_optimalBreaks(e){return e?this._optimalBreaks(e.previousBreak).concat(e.index):[]}_applyJustification(e,t,r){const n=e[r],s=n.vertical?d:n.glyphMosaicItem?n.glyphMosaicItem.metrics.advance:0,i=(n.x+s)*this._justify;for(let n=t;n<=r;n++)e[n].x-=i}}var y=r(21315);const g=.5;class x{constructor(e,t,r=0,n=-1,s=g){this.x=e,this.y=t,this.angle=r,this.segment=n,this.minzoom=s}}class _{constructor(e,t,r,n,s,i=g,o=a.B1){this.anchor=e,this.labelAngle=t,this.glyphAngle=r,this.page=n,this.alternateVerticalGlyph=s,this.minzoom=i,this.maxzoom=o}}class m{constructor(e,t,r,n,s,i,o,a,l,c,h,u){this.tl=e,this.tr=t,this.bl=r,this.br=n,this.mosaicRect=s,this.labelAngle=i,this.minAngle=o,this.maxAngle=a,this.anchor=l,this.minzoom=c,this.maxzoom=h,this.page=u}}class b{constructor(e){this.shapes=e}}class I{getIconPlacement(e,t,r){const n=new o.E9(e.x,e.y),s=r.rotationAlignment===y.aF.MAP,i=r.keepUpright;let l=r.rotate*a.DT;s&&(l+=e.angle);const c=new b([]);return r.allowOverlap&&r.ignorePlacement||(c.iconColliders=[]),this._addIconPlacement(c,n,t,r,l),s&&i&&this._addIconPlacement(c,n,t,r,l+a.JJ),c}_addIconPlacement(e,t,r,n,s){const i=r.pixelRatio,l=r.width/i,c=r.height/i,h=n.offset;let u=h[0],f=h[1];switch(n.anchor){case y.nR.CENTER:u-=l/2,f-=c/2;break;case y.nR.LEFT:f-=c/2;break;case y.nR.RIGHT:u-=l,f-=c/2;break;case y.nR.TOP:u-=l/2;break;case y.nR.BOTTOM:u-=l/2,f-=c;break;case y.nR.TOP_LEFT:break;case y.nR.BOTTOM_LEFT:f-=c;break;case y.nR.TOP_RIGHT:u-=l;break;case y.nR.BOTTOM_RIGHT:u-=l,f-=c}const d=r.rect,p=2/i,x=u-p,_=f-p,b=x+d.width/i,I=_+d.height/i,w=new o.E9(x,_),M=new o.E9(b,I),T=new o.E9(x,I),k=new o.E9(b,_);if(0!==s){const e=Math.cos(s),t=Math.sin(s);w.rotate(e,t),M.rotate(e,t),T.rotate(e,t),k.rotate(e,t)}const v=new m(w,k,T,M,d,s,0,256,t,g,a.B1,0);if(e.shapes.push(v),!n.allowOverlap||!n.ignorePlacement){const r=n.size,i=n.padding,o={xTile:t.x,yTile:t.y,dxPixels:u*r-i,dyPixels:f*r-i,hard:!n.optional,partIndex:0,width:l*r+2*i,height:c*r+2*i,angle:s,minLod:g,maxLod:a.B1};e.iconColliders.push(o)}}getTextPlacement(e,t,r,n){const s=new o.E9(e.x,e.y),i=n.rotate*a.DT,l=n.rotationAlignment===y.aF.MAP,c=n.keepUpright,h=n.padding;let u=g;const f=l?e.angle:0,x=e.segment>=0&&l,I=n.allowOverlap&&n.ignorePlacement?null:[],w=[],M=!x;let T=Number.POSITIVE_INFINITY,k=Number.NEGATIVE_INFINITY,v=T,P=k;const L=(x||l)&&c,E=n.size/d;let D=!1;for(const e of t)if(e.vertical){D=!0;break}let A,B=0,S=0;if(!x&&D){const e=p.getTextBox(t,n.lineHeight*d);switch(n.anchor){case y.nR.LEFT:B=e.height/2,S=-e.width/2;break;case y.nR.RIGHT:B=-e.height/2,S=e.width/2;break;case y.nR.TOP:B=e.height/2,S=e.width/2;break;case y.nR.BOTTOM:B=-e.height/2,S=-e.width/2;break;case y.nR.TOP_LEFT:B=e.height;break;case y.nR.BOTTOM_LEFT:S=-e.width;break;case y.nR.TOP_RIGHT:S=e.width;break;case y.nR.BOTTOM_RIGHT:B=-e.height}}B+=n.offset[0]*d,S+=n.offset[1]*d;for(const d of t){const t=d.glyphMosaicItem;if(!t||t.rect.isEmpty)continue;const p=t.rect,y=t.metrics,b=t.page;if(I&&M){if(void 0!==A&&A!==d.y){let t,r,s,o;D?(t=-P+B,r=T+S,s=P-v,o=k-T):(t=T+B,r=v+S,s=k-T,o=P-v);const l={xTile:e.x,yTile:e.y,dxPixels:t*E-h,dyPixels:r*E-h,hard:!n.optional,partIndex:1,width:s*E+2*h,height:o*E+2*h,angle:i,minLod:g,maxLod:a.B1};I.push(l),T=Number.POSITIVE_INFINITY,k=Number.NEGATIVE_INFINITY,v=T,P=k}A=d.y}const V=[];if(x){const n=.5*t.metrics.width,s=(d.x+y.left-4+n)*E*8;if(u=this._placeGlyph(e,u,s,r,e.segment,1,d.vertical,b,V),c&&(u=this._placeGlyph(e,u,s,r,e.segment,-1,d.vertical,b,V)),u>=2)break}else V.push(new _(s,f,f,b,!1)),l&&c&&V.push(new _(s,f+a.JJ,f+a.JJ,b,!1));const C=d.x+y.left,R=d.y-17-y.top,O=C+y.width,F=R+y.height;let q,U,N,z,Z,j,G,H;if(!x&&D)if(d.vertical){const e=(C+O)/2-y.height/2,t=(R+F)/2+y.width/2;q=new o.E9(-t-4+B,e-4+S),U=new o.E9(q.x+p.width,q.y+p.height),N=new o.E9(q.x,U.y),z=new o.E9(U.x,q.y)}else q=new o.E9(4-R+B,C-4+S),U=new o.E9(q.x-p.height,q.y+p.width),N=new o.E9(U.x,q.y),z=new o.E9(q.x,U.y);else q=new o.E9(C-4+B,R-4+S),U=new o.E9(q.x+p.width,q.y+p.height),N=new o.E9(q.x,U.y),z=new o.E9(U.x,q.y);for(const t of V){let r,s,a,l;if(t.alternateVerticalGlyph){if(!Z){const e=(R+F)/2+S;Z=new o.E9((C+O)/2+B-y.height/2-4,e+y.width/2+4),j=new o.E9(Z.x+p.height,Z.y-p.width),G=new o.E9(j.x,Z.y),H=new o.E9(Z.x,j.y)}r=Z,s=G,a=H,l=j}else r=q,s=N,a=z,l=U;const c=R,u=F,f=t.glyphAngle+i;if(0!==f){const e=Math.cos(f),t=Math.sin(f);r=r.clone(),s=s?.clone(),a=a?.clone(),l=l?.clone(),r.rotate(e,t),l?.rotate(e,t),s?.rotate(e,t),a?.rotate(e,t)}let g=0,_=256;if(x&&D?d.vertical?t.alternateVerticalGlyph?(g=32,_=96):(g=224,_=32):(g=224,_=96):(g=192,_=64),w.push(new m(r,a,s,l,p,t.labelAngle,g,_,t.anchor,t.minzoom,t.maxzoom,t.page)),I&&(!L||this._legible(t.labelAngle)))if(M)C<T&&(T=C),c<v&&(v=c),O>k&&(k=O),u>P&&(P=u);else if(t.minzoom<2){const r={xTile:e.x,yTile:e.y,dxPixels:(C+B)*E-h,dyPixels:(c+B)*E-h,hard:!n.optional,partIndex:1,width:(O-C)*E+2*h,height:(u-c)*E+2*h,angle:f,minLod:t.minzoom,maxLod:t.maxzoom};I.push(r)}}}if(u>=2)return null;if(I&&M){let t,r,s,o;D?(t=-P+B,r=T+S,s=P-v,o=k-T):(t=T+B,r=v+S,s=k-T,o=P-v);const l={xTile:e.x,yTile:e.y,dxPixels:t*E-h,dyPixels:r*E-h,hard:!n.optional,partIndex:1,width:s*E+2*h,height:o*E+2*h,angle:i,minLod:g,maxLod:a.B1};I.push(l)}const V=new b(w);return I&&I.length>0&&(V.textColliders=I),V}_legible(e){const t=(0,a.Or)(e);return t<65||t>=193}_placeGlyph(e,t,r,n,s,i,l,c,h){let u=i;const f=u<0?(0,a.DQ)(e.angle+a.JJ,a._U):e.angle;let d=0;r<0&&(u*=-1,r*=-1,d=a.JJ),u>0&&++s;let p=new o.E9(e.x,e.y),y=n[s],g=a.B1;if(n.length<=s)return g;for(;;){const e=y.x-p.x,i=y.y-p.y,o=Math.sqrt(e*e+i*i),x=Math.max(r/o,t),m=e/o,b=i/o,I=(0,a.DQ)(Math.atan2(b,m)+d,a._U);if(h.push(new _(p,f,I,c,!1,x,g)),l&&h.push(new _(p,f,I,c,!0,x,g)),x<=t)return x;p=y.clone();do{if(s+=u,n.length<=s||s<0)return x;y=n[s]}while(p.isEqual(y));let w=y.x-p.x,M=y.y-p.y;const T=Math.sqrt(w*w+M*M);w*=o/T,M*=o/T,p.x-=w,p.y-=M,g=x}}}var w,M,T=r(45091),k=r(65390);(M=w||(w={}))[M.moveTo=1]="moveTo",M[M.lineTo=2]="lineTo",M[M.close=7]="close";class v{constructor(e,t){this.values={},this._geometry=void 0,this._pbfGeometry=null;const r=t.keys,n=t.values,s=e.asUnsafe();for(;s.next();)switch(s.tag()){case 1:this.id=s.getUInt64();break;case 2:{const e=s.getMessage().asUnsafe(),t=this.values;for(;!e.empty();){const s=e.getUInt32(),i=e.getUInt32();t[r[s]]=n[i]}e.release();break}case 3:this.type=s.getUInt32();break;case 4:this._pbfGeometry=s.getMessage();break;default:s.skip()}}getGeometry(e){if(void 0!==this._geometry)return this._geometry;if(!this._pbfGeometry)return null;const t=this._pbfGeometry.asUnsafe();let r,n;this._pbfGeometry=null,e?e.reset(this.type):r=[];let s,i=w.moveTo,a=0,l=0,c=0;for(;!t.empty();){if(0===a){const e=t.getUInt32();i=7&e,a=e>>3}switch(a--,i){case w.moveTo:l+=t.getSInt32(),c+=t.getSInt32(),e?e.moveTo(l,c):r&&(n&&r.push(n),n=[],n.push(new o.E9(l,c)));break;case w.lineTo:l+=t.getSInt32(),c+=t.getSInt32(),e?e.lineTo(l,c):n&&n.push(new o.E9(l,c));break;case w.close:e?e.close():n&&!n[0].equals(l,c)&&n.push(n[0].clone());break;default:throw t.release(),new Error("Invalid path operation")}}return e?s=e.result():r&&(n&&r.push(n),s=r),t.release(),this._geometry=s,s}}var P=r(26084);class L extends P.Z{constructor(){super(12)}add(e,t,r){const n=this.array;n.push(e),n.push(t),n.push(r)}}class E{constructor(e){this.extent=4096,this.keys=[],this.values=[],this._pbfLayer=e.clone();const t=e.asUnsafe();for(;t.next();)switch(t.tag()){case 1:this.name=t.getString();break;case 3:this.keys.push(t.getString());break;case 4:this.values.push(t.processMessage(E._parseValue));break;case 5:this.extent=t.getUInt32();break;default:t.skip()}}getData(){return this._pbfLayer}static _parseValue(e){for(;e.next();)switch(e.tag()){case 1:return e.getString();case 2:return e.getFloat();case 3:return e.getDouble();case 4:return e.getInt64();case 5:return e.getUInt64();case 6:return e.getSInt64();case 7:return e.getBool();default:e.skip()}return null}}class D extends P.Z{constructor(e){super(e)}add(e,t,r,n,s,i,o,a,l,c,h,u){const f=this.array;let d=P.Z.i1616to32(e,t);f.push(d);const p=31;d=P.Z.i8888to32(Math.round(p*r),Math.round(p*n),Math.round(p*s),Math.round(p*i)),f.push(d),d=P.Z.i8888to32(Math.round(p*o),Math.round(p*a),Math.round(p*l),Math.round(p*c)),f.push(d),d=P.Z.i1616to32(h,0),f.push(d),u&&f.push(...u)}}class A extends P.Z{constructor(e){super(e)}add(e,t,r){const n=this.array;n.push(P.Z.i1616to32(e,t)),r&&n.push(...r)}}class B extends P.Z{constructor(e){super(e)}add(e,t,r,n,s,i,o){const a=this.array,l=this.index;let c=P.Z.i1616to32(e,t);return a.push(c),c=P.Z.i8888to32(Math.round(15*r),Math.round(15*n),s,i),a.push(c),o&&a.push(...o),l}}class S extends P.Z{constructor(e){super(e)}add(e,t,r,n,s,i,o,l,c,h,u,f){const d=this.array;let p=P.Z.i1616to32(e,t);d.push(p),p=P.Z.i1616to32(Math.round(8*r),Math.round(8*n)),d.push(p),p=P.Z.i8888to32(s/4,i/4,l,c),d.push(p),p=P.Z.i8888to32(0,(0,a.Or)(o),10*h,Math.min(10*u,255)),d.push(p),f&&d.push(...f)}}class V extends P.Z{constructor(e){super(e)}add(e,t,r,n,s){const i=this.array,o=P.Z.i1616to32(2*e+r,2*t+n);i.push(o),s&&i.push(...s)}}class C{constructor(e,t,r){this.layerExtent=4096,this._features=[],this.layer=e,this.zoom=t,this._spriteInfo=r,this._filter=e.getFeatureFilter()}pushFeature(e){this._filter&&!this._filter.filter(e,this.zoom)||this._features.push(e)}hasFeatures(){return this._features.length>0}getResources(e,t,r){}}class R extends C{constructor(e,t,r,n,i){super(e,t,r),this.type=s.al.CIRCLE,this._circleVertexBuffer=n,this._circleIndexBuffer=i}get circleIndexStart(){return this._circleIndexStart}get circleIndexCount(){return this._circleIndexCount}processFeatures(e){const t=this._circleVertexBuffer,r=this._circleIndexBuffer;this._circleIndexStart=3*r.index,this._circleIndexCount=0;const n=this.layer,s=this.zoom;e&&e.setExtent(this.layerExtent);for(const i of this._features){const o=i.getGeometry(e);if(!o)continue;const a=n.circleMaterial.encodeAttributes(i,s,n);for(const e of o)if(e)for(const n of e){const e=t.index;t.add(n.x,n.y,0,0,a),t.add(n.x,n.y,0,1,a),t.add(n.x,n.y,1,0,a),t.add(n.x,n.y,1,1,a),r.add(e+0,e+1,e+2),r.add(e+1,e+2,e+3),this._circleIndexCount+=6}}}serialize(){let e=6;e+=this.layerUIDs.length,e+=this._circleVertexBuffer.array.length,e+=this._circleIndexBuffer.array.length;const t=new Uint32Array(e),r=new Int32Array(t.buffer);let n=0;t[n++]=this.type,t[n++]=this.layerUIDs.length;for(let e=0;e<this.layerUIDs.length;e++)t[n++]=this.layerUIDs[e];t[n++]=this._circleIndexStart,t[n++]=this._circleIndexCount,t[n++]=this._circleVertexBuffer.array.length;for(let e=0;e<this._circleVertexBuffer.array.length;e++)r[n++]=this._circleVertexBuffer.array[e];t[n++]=this._circleIndexBuffer.array.length;for(let e=0;e<this._circleIndexBuffer.array.length;e++)t[n++]=this._circleIndexBuffer.array[e];return t.buffer}}var O=r(3894),F=r(95401);class q extends C{constructor(e,t,r,n,i,o,a){super(e,t,r),this.type=s.al.FILL,this._patternMap=new Map,this._fillVertexBuffer=n,this._fillIndexBuffer=i,this._outlineVertexBuffer=o,this._outlineIndexBuffer=a}get fillIndexStart(){return this._fillIndexStart}get fillIndexCount(){return this._fillIndexCount}get outlineIndexStart(){return this._outlineIndexStart}get outlineIndexCount(){return this._outlineIndexCount}getResources(e,t,r){const n=this.layer,s=this.zoom,i=n.getPaintProperty("fill-pattern");if(i)if(i.isDataDriven)for(const e of this._features)t(i.getValue(s,e),!0);else t(i.getValue(s),!0)}processFeatures(e){this._fillIndexStart=3*this._fillIndexBuffer.index,this._fillIndexCount=0,this._outlineIndexStart=3*this._outlineIndexBuffer.index,this._outlineIndexCount=0;const t=this.layer,r=this.zoom,{fillMaterial:n,outlineMaterial:s,hasDataDrivenFill:i,hasDataDrivenOutline:o}=t;e&&e.setExtent(this.layerExtent);const a=t.getPaintProperty("fill-pattern"),l=a?.isDataDriven;let c=!a&&t.getPaintValue("fill-antialias",r);if(t.outlineUsesFillColor){if(c&&!t.hasDataDrivenOpacity){const e=t.getPaintValue("fill-opacity",r),n=t.getPaintValue("fill-opacity",r+1);e<1&&n<1&&(c=!1)}if(c&&!t.hasDataDrivenColor){const e=t.getPaintValue("fill-color",r),n=t.getPaintValue("fill-color",r+1);e[3]<1&&n[3]<1&&(c=!1)}}const h=this._features,u=e?.validateTessellation;if(l){const i=[];for(const l of h){const h=a.getValue(r,l),f=this._spriteInfo[h];if(!f||!f.rect)continue;const d=n.encodeAttributes(l,r,t,f),p=c&&o?s.encodeAttributes(l,r,t):[],y=l.getGeometry(e);i.push({ddFillAttributes:d,ddOutlineAttributes:p,page:f.page,geometry:y}),i.sort(((e,t)=>e.page-t.page));for(const{ddFillAttributes:e,ddOutlineAttributes:r,page:n,geometry:s}of i)this._processFeature(s,c,t.outlineUsesFillColor,e,r,u,n)}}else for(const a of h){const l=i?n.encodeAttributes(a,r,t):null,h=c&&o?s.encodeAttributes(a,r,t):null,f=a.getGeometry(e);this._processFeature(f,c,t.outlineUsesFillColor,l,h,u)}}serialize(){let e=10;e+=this.layerUIDs.length,e+=this._fillVertexBuffer.array.length,e+=this._fillIndexBuffer.array.length,e+=this._outlineVertexBuffer.array.length,e+=this._outlineIndexBuffer.array.length,e+=3*this._patternMap.size+1;const t=new Uint32Array(e),r=new Int32Array(t.buffer);let n=0;t[n++]=this.type,t[n++]=this.layerUIDs.length;for(let e=0;e<this.layerUIDs.length;e++)t[n++]=this.layerUIDs[e];t[n++]=this._fillIndexStart,t[n++]=this._fillIndexCount,t[n++]=this._outlineIndexStart,t[n++]=this._outlineIndexCount;const s=this._patternMap,i=s.size;if(t[n++]=i,i>0)for(const[e,[r,i]]of s)t[n++]=e,t[n++]=r,t[n++]=i;t[n++]=this._fillVertexBuffer.array.length;for(let e=0;e<this._fillVertexBuffer.array.length;e++)r[n++]=this._fillVertexBuffer.array[e];t[n++]=this._fillIndexBuffer.array.length;for(let e=0;e<this._fillIndexBuffer.array.length;e++)t[n++]=this._fillIndexBuffer.array[e];t[n++]=this._outlineVertexBuffer.array.length;for(let e=0;e<this._outlineVertexBuffer.array.length;e++)r[n++]=this._outlineVertexBuffer.array[e];t[n++]=this._outlineIndexBuffer.array.length;for(let e=0;e<this._outlineIndexBuffer.array.length;e++)t[n++]=this._outlineIndexBuffer.array[e];return t.buffer}_processFeature(e,t,r,n,s,i,o){if(!e)return;const a=e.length,l=!s||0===s.length;if(t&&(!r||l))for(let t=0;t<a;t++)this._processOutline(e[t],s);let c;for(let t=0;t<a;t++){const r=q._area(e[t]);r>32?(void 0!==c&&this._processFill(e,c,n,i,o),c=[t]):r<-32&&void 0!==c&&c.push(t)}void 0!==c&&this._processFill(e,c,n,i,o)}_processOutline(e,t){const r=this._outlineVertexBuffer,n=this._outlineIndexBuffer,s=n.index;let i,a,l;const c=new o.E9(0,0),h=new o.E9(0,0),u=new o.E9(0,0);let f=-1,d=-1,p=-1,y=-1,g=-1,x=!1,_=e.length;if(_<2)return;const m=e[0];let b=e[_-1];for(;_&&b.isEqual(m);)--_,b=e[_-1];if(!(_-0<2)){for(let s=0;s<_;++s){0===s?(i=e[_-1],a=e[0],l=e[1],c.assignSub(a,i),c.normalize(),c.rightPerpendicular()):(i=a,a=l,l=s!==_-1?e[s+1]:e[0],c.assign(h));const o=this._isClipEdge(i,a);-1===y&&(x=o),h.assignSub(l,a),h.normalize(),h.rightPerpendicular();const m=c.x*h.y-c.y*h.x;u.assignAdd(c,h),u.normalize();const b=-u.x*-c.x+-u.y*-c.y;let I=Math.abs(0!==b?1/b:1);I>8&&(I=8),m>=0?(p=r.add(a.x,a.y,c.x,c.y,0,1,t),-1===y&&(y=p),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p),d=r.add(a.x,a.y,I*-u.x,I*-u.y,0,-1,t),-1===g&&(g=d),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p),f=d,d=p,p=r.add(a.x,a.y,u.x,u.y,0,1,t),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p),d=r.add(a.x,a.y,h.x,h.y,0,1,t),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p)):(p=r.add(a.x,a.y,I*u.x,I*u.y,0,1,t),-1===y&&(y=p),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p),d=r.add(a.x,a.y,-c.x,-c.y,0,-1,t),-1===g&&(g=d),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p),f=d,d=p,p=r.add(a.x,a.y,-u.x,-u.y,0,-1,t),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p),f=r.add(a.x,a.y,-h.x,-h.y,0,-1,t),f>=0&&d>=0&&p>=0&&!o&&n.add(f,d,p))}f>=0&&d>=0&&y>=0&&!x&&n.add(f,d,y),f>=0&&y>=0&&g>=0&&!x&&n.add(f,g,y),this._outlineIndexCount+=3*(n.index-s)}}_processFill(e,t,r,n,s){let i;t.length>1&&(i=[]);let o=0;for(const r of t)0!==o&&i.push(o),o+=e[r].length;const a=2*o,l=O.Z.acquire();for(const r of t){const t=e[r],n=t.length;for(let e=0;e<n;++e)l.push(t[e].x,t[e].y)}const c=(0,F.e)(l,i,2);if(F.e.deviation(l,i,2,c)>0){const n=t.map((t=>e[t].length)),{buffer:i,vertexCount:o}=(0,k.b)(l,n);if(o>0){const e=this._fillVertexBuffer.index;for(let e=0;e<o;e++)this._fillVertexBuffer.add(i[2*e],i[2*e+1],r);for(let t=0;t<o;t+=3){const r=e+t;this._fillIndexBuffer.add(r,r+1,r+2)}if(void 0!==s){const e=this._patternMap,t=e.get(s);t?t[1]+=o:e.set(s,[this._fillIndexStart+this._fillIndexCount,o])}this._fillIndexCount+=o}}else{const e=c.length;if(e>0){const t=this._fillVertexBuffer.index;let n=0;for(;n<a;)this._fillVertexBuffer.add(l[n++],l[n++],r);let i=0;for(;i<e;)this._fillIndexBuffer.add(t+c[i++],t+c[i++],t+c[i++]);if(void 0!==s){const t=this._patternMap,r=t.get(s);r?r[1]+=e:t.set(s,[this._fillIndexStart+this._fillIndexCount,e])}this._fillIndexCount+=e}}O.Z.release(l)}_isClipEdge(e,t){return e.x===t.x?e.x<=-64||e.x>=4160:e.y===t.y&&(e.y<=-64||e.y>=4160)}static _area(e){let t=0;const r=e.length-1;for(let n=0;n<r;n++)t+=(e[n].x-e[n+1].x)*(e[n].y+e[n+1].y);return t+=(e[r].x-e[0].x)*(e[r].y+e[0].y),.5*t}}var U=r(67327);class N extends C{constructor(e,t,r,n,i){super(e,t,r),this.type=s.al.LINE,this._tessellationOptions={pixelCoordRatio:8,halfWidth:0,offset:0},this._patternMap=new Map,this.tessellationProperties={_lineVertexBuffer:null,_lineIndexBuffer:null,_ddValues:null},this.tessellationProperties._lineVertexBuffer=n,this.tessellationProperties._lineIndexBuffer=i,this._lineTessellator=new U.z(z(this.tessellationProperties),Z(this.tessellationProperties),e.canUseThinTessellation)}get lineIndexStart(){return this._lineIndexStart}get lineIndexCount(){return this._lineIndexCount}getResources(e,t,r){const n=this.layer,s=this.zoom,i=n.getPaintProperty("line-pattern"),o=n.getPaintProperty("line-dasharray"),a=n.getLayoutProperty("line-cap");if(!i&&!o)return;const l=a?.getValue(s)||0,c=a?.isDataDriven,h=i?.isDataDriven,u=o?.isDataDriven;if(h||u)for(const e of this._features)t(h?i.getValue(s,e):this._getDashArrayKey(e,s,n,o,c,a,l));else if(i)t(i.getValue(s));else if(o){const e=o.getValue(s);t(n.getDashKey(e,l))}}processFeatures(e){this._lineIndexStart=3*this.tessellationProperties._lineIndexBuffer.index,this._lineIndexCount=0;const t=this.layer,r=this.zoom,n=this._features,s=this._tessellationOptions,{hasDataDrivenLine:i,lineMaterial:o}=t;e&&e.setExtent(this.layerExtent);const a=t.getPaintProperty("line-pattern"),l=t.getPaintProperty("line-dasharray"),c=a?.isDataDriven,h=l?.isDataDriven;let u;u=t.getLayoutProperty("line-cap");const f=u?.isDataDriven?u:null,d=f?null:t.getLayoutValue("line-cap",r),p=d||0,y=!!f;u=t.getLayoutProperty("line-join");const g=u?.isDataDriven?u:null,x=g?null:t.getLayoutValue("line-join",r);u=t.getLayoutProperty("line-miter-limit");const _=u?.isDataDriven?u:null,m=_?null:t.getLayoutValue("line-miter-limit",r);u=t.getLayoutProperty("line-round-limit");const b=u?.isDataDriven?u:null,I=b?null:t.getLayoutValue("line-round-limit",r);u=t.getPaintProperty("line-width");const w=u?.isDataDriven?u:null,M=w?null:t.getPaintValue("line-width",r);u=t.getPaintProperty("line-offset");const T=u?.isDataDriven?u:null,k=T?null:t.getPaintValue("line-offset",r);if(c||h){const i=[];for(const s of n){const n=c?a.getValue(r,s):this._getDashArrayKey(s,r,t,l,y,f,p),h=this._spriteInfo[n];if(!h||!h.rect)continue;const u=o.encodeAttributes(s,r,t,h),v=s.getGeometry(e);i.push({ddAttributes:u,page:h.page,cap:f?f.getValue(r,s):d,join:g?g.getValue(r,s):x,miterLimit:_?_.getValue(r,s):m,roundLimit:b?b.getValue(r,s):I,halfWidth:.5*(w?w.getValue(r,s):M),offset:T?T.getValue(r,s):k,geometry:v})}i.sort(((e,t)=>e.page-t.page)),s.textured=!0;for(const{ddAttributes:e,page:t,cap:r,join:n,miterLimit:o,roundLimit:a,halfWidth:l,offset:c,geometry:h}of i)s.capType=r,s.joinType=n,s.miterLimit=o,s.roundLimit=a,s.halfWidth=l,s.offset=c,this._processFeature(h,e,t)}else{if(a){const e=a.getValue(r),t=this._spriteInfo[e];if(!t||!t.rect)return}s.textured=!(!a&&!l),s.capType=d,s.joinType=x,s.miterLimit=m,s.roundLimit=I,s.halfWidth=.5*M,s.offset=k;for(const a of n){const n=i?o.encodeAttributes(a,r,t):null;f&&(s.capType=f.getValue(r,a)),g&&(s.joinType=g.getValue(r,a)),_&&(s.miterLimit=_.getValue(r,a)),b&&(s.roundLimit=b.getValue(r,a)),w&&(s.halfWidth=.5*w.getValue(r,a)),T&&(s.offset=T.getValue(r,a));const l=a.getGeometry(e);this._processFeature(l,n)}}}serialize(){let e=6;e+=this.layerUIDs.length,e+=this.tessellationProperties._lineVertexBuffer.array.length,e+=this.tessellationProperties._lineIndexBuffer.array.length,e+=3*this._patternMap.size+1;const t=new Uint32Array(e),r=new Int32Array(t.buffer);let n=0;t[n++]=this.type,t[n++]=this.layerUIDs.length;for(let e=0;e<this.layerUIDs.length;e++)t[n++]=this.layerUIDs[e];t[n++]=this._lineIndexStart,t[n++]=this._lineIndexCount;const s=this._patternMap,i=s.size;if(t[n++]=i,i>0)for(const[e,[r,i]]of s)t[n++]=e,t[n++]=r,t[n++]=i;t[n++]=this.tessellationProperties._lineVertexBuffer.array.length;for(let e=0;e<this.tessellationProperties._lineVertexBuffer.array.length;e++)r[n++]=this.tessellationProperties._lineVertexBuffer.array[e];t[n++]=this.tessellationProperties._lineIndexBuffer.array.length;for(let e=0;e<this.tessellationProperties._lineIndexBuffer.array.length;e++)t[n++]=this.tessellationProperties._lineIndexBuffer.array[e];return t.buffer}_processFeature(e,t,r){if(!e)return;const n=e.length;for(let s=0;s<n;s++)this._processGeometry(e[s],t,r)}_processGeometry(e,t,r){if(e.length<2)return;let n,s,i=e[0],o=1;for(;o<e.length;)n=e[o].x-i.x,s=e[o].y-i.y,n*n+s*s<1e-6?e.splice(o,1):(i=e[o],++o);if(e.length<2)return;const a=this.tessellationProperties._lineIndexBuffer,l=3*a.index;this._tessellationOptions.initialDistance=0,this._tessellationOptions.wrapDistance=65535,this.tessellationProperties._ddValues=t,this._lineTessellator.tessellate(e,this._tessellationOptions);const c=3*a.index-l;if(void 0!==r){const e=this._patternMap,t=e.get(r);t?t[1]+=c:e.set(r,[l+this._lineIndexCount,c])}this._lineIndexCount+=c}_getDashArrayKey(e,t,r,n,s,i,o){const a=s?i.getValue(t,e):o,l=n.getValue(t,e);return r.getDashKey(l,a)}}const z=e=>(t,r,n,s,i,o,a,l,c,h,u)=>(e._lineVertexBuffer.add(t,r,a,l,n,s,i,o,c,h,u,e._ddValues),e._lineVertexBuffer.index-1),Z=e=>(t,r,n)=>{e._lineIndexBuffer.add(t,r,n)};var j,G=r(79087),H=r(19153),$=r(7333);function K(e,t){return e.iconMosaicItem&&t.iconMosaicItem?e.iconMosaicItem.page===t.iconMosaicItem.page?0:e.iconMosaicItem.page-t.iconMosaicItem.page:e.iconMosaicItem&&!t.iconMosaicItem?1:!e.iconMosaicItem&&t.iconMosaicItem?-1:0}class W extends C{constructor(e,t,r,n,i,o,a,l){super(e,t,l.getSpriteItems()),this.type=s.al.SYMBOL,this._markerMap=new Map,this._glyphMap=new Map,this._glyphBufferDataStorage=new Map,this._isIconSDF=!1,this._iconVertexBuffer=r,this._iconIndexBuffer=n,this._textVertexBuffer=i,this._textIndexBuffer=o,this._placementEngine=a,this._workerTileHandler=l}get markerPageMap(){return this._markerMap}get glyphsPageMap(){return this._glyphMap}get symbolInstances(){return this._symbolInstances}getResources(e,t,r){const n=this.layer,s=this.zoom;e&&e.setExtent(this.layerExtent);const i=n.getLayoutProperty("icon-image"),o=n.getLayoutProperty("text-field");let a=n.getLayoutProperty("text-transform"),l=n.getLayoutProperty("text-font");const c=[];let h,u,f,d;i&&!i.isDataDriven&&(h=i.getValue(s)),o&&!o.isDataDriven&&(u=o.getValue(s)),a&&a.isDataDriven||(f=n.getLayoutValue("text-transform",s),a=null),l&&l.isDataDriven||(d=n.getLayoutValue("text-font",s),l=null);for(const p of this._features){const g=p.getGeometry(e);if(!g||0===g.length)continue;let x,_;i&&(x=i.isDataDriven?i.getValue(s,p):this._replaceKeys(h,p.values),x&&t(x));let m=!1;if(o&&(_=o.isDataDriven?o.getValue(s,p):this._replaceKeys(u,p.values),_)){switch(_=_.replace(/\\n/g,"\n"),a&&(f=a.getValue(s,p)),f){case y._5.LOWERCASE:_=_.toLowerCase();break;case y._5.UPPERCASE:_=_.toUpperCase()}if(W._bidiEngine.hasBidiChar(_)){let e;e="rtl"===W._bidiEngine.checkContextual(_)?"IDNNN":"ICNNN",_=W._bidiEngine.bidiTransform(_,e,"VLYSN"),m=!0}const e=_.length;if(e>0){l&&(d=l.getValue(s,p));for(const t of d){let n=r[t];n||(n=r[t]=new Set);for(let t=0;t<e;t++){const e=_.charCodeAt(t);n.add(e)}}}}if(!x&&!_)continue;const b=n.getLayoutValue("symbol-sort-key",s,p),I={feature:p,sprite:x,label:_,rtl:m,geometry:g,hash:(_?(0,H.hP)(_):0)^(x?(0,H.hP)(x):0),priority:b,textFont:d};c.push(I)}this._symbolFeatures=c}processFeatures(e){e&&e.setExtent(this.layerExtent);const t=this.layer,r=this.zoom,n=t.getLayoutValue("symbol-placement",r),s=n!==y.R.POINT,l=8*t.getLayoutValue("symbol-spacing",r),c=t.getLayoutProperty("icon-image"),h=t.getLayoutProperty("text-field"),u=c?new $._L(t,r,s):null,f=h?new $.nj(t,r,s):null,g=this._workerTileHandler;let _;c&&(_=g.getSpriteItems()),this._iconIndexStart=3*this._iconIndexBuffer.index,this._textIndexStart=3*this._textIndexBuffer.index,this._iconIndexCount=0,this._textIndexCount=0,this._markerMap.clear(),this._glyphMap.clear();const m=[];let b=1;f&&f.size&&(b=f.size/d);const I=f?f.maxAngle*a.DT:0,w=f?8*f.size:0;for(const e of this._symbolFeatures){let t,a;u&&_&&e.sprite&&(t=_[e.sprite],t&&t.sdf&&(this._isIconSDF=!0)),t&&u.update(r,e.feature);let c=0;const h=e.label;if(h){(0,i.O3)(f),f.update(r,e.feature);const t=s&&f.rotationAlignment===y.aF.MAP?f.keepUpright:f.writingMode&&f.writingMode.includes(y.r1.VERTICAL);let n=.5;switch(f.anchor){case y.nR.TOP_LEFT:case y.nR.LEFT:case y.nR.BOTTOM_LEFT:n=0;break;case y.nR.TOP_RIGHT:case y.nR.RIGHT:case y.nR.BOTTOM_RIGHT:n=1}let o=.5;switch(f.anchor){case y.nR.TOP_LEFT:case y.nR.TOP:case y.nR.TOP_RIGHT:o=0;break;case y.nR.BOTTOM_LEFT:case y.nR.BOTTOM:case y.nR.BOTTOM_RIGHT:o=1}let l=.5;switch(f.justify){case y.vL.AUTO:l=n;break;case y.vL.LEFT:l=0;break;case y.vL.RIGHT:l=1}const u=f.letterSpacing*d,x=s?0:f.maxWidth*d,_=f.lineHeight*d,m=e.textFont.map((e=>g.getGlyphItems(e)));if(a=new p(m,x,_,u,n,o,l).getShaping(h,e.rtl,t),a&&a.length>0){let e=1e30,t=-1e30;for(const r of a)e=Math.min(e,r.x),t=Math.max(t,r.x);c=(t-e+48)*b*8}}for(let r of e.geometry){const i=[];if(n===y.R.LINE){if(a?.length&&f?.size){const e=8*f.size*(2+Math.min(2,4*Math.abs(f.offset[1])));r=W._smoothVertices(r,e)}W._pushAnchors(i,r,l,c)}else n===y.R.LINE_CENTER?W._pushCenterAnchor(i,r):e.feature.type===o.Vl.Polygon?W._pushCentroid(i,r):i.push(new x(r[0].x,r[0].y));for(const n of i){if(n.x<0||n.x>4096||n.y<0||n.y>4096)continue;if(s&&c>0&&f?.rotationAlignment===y.aF.MAP&&!W._honorsTextMaxAngle(r,n,c,I,w))continue;const i={shaping:a,line:r,iconMosaicItem:t,anchor:n,symbolFeature:e,textColliders:[],iconColliders:[],textVertexRanges:[],iconVertexRanges:[]};m.push(i),this._processFeature(i,u,f)}}}m.sort(K),this._addPlacedGlyphs(),this._symbolInstances=m}serialize(){let e=11;e+=this.layerUIDs.length,e+=3*this.markerPageMap.size,e+=3*this.glyphsPageMap.size,e+=W._symbolsSerializationLength(this._symbolInstances),e+=this._iconVertexBuffer.array.length,e+=this._iconIndexBuffer.array.length,e+=this._textVertexBuffer.array.length,e+=this._textIndexBuffer.array.length;const t=new Uint32Array(e),r=new Int32Array(t.buffer),n=new Float32Array(t.buffer);let s=0;t[s++]=this.type,t[s++]=this.layerUIDs.length;for(let e=0;e<this.layerUIDs.length;e++)t[s++]=this.layerUIDs[e];t[s++]=this._isIconSDF?1:0,t[s++]=this.markerPageMap.size;for(const[e,[r,n]]of this.markerPageMap)t[s++]=e,t[s++]=r,t[s++]=n;t[s++]=this.glyphsPageMap.size;for(const[e,[r,n]]of this.glyphsPageMap)t[s++]=e,t[s++]=r,t[s++]=n;t[s++]=this._iconVertexBuffer.index/4,t[s++]=this._textVertexBuffer.index/4,s=W.serializeSymbols(t,r,n,s,this._symbolInstances),t[s++]=this._iconVertexBuffer.array.length;for(let e=0;e<this._iconVertexBuffer.array.length;e++)r[s++]=this._iconVertexBuffer.array[e];t[s++]=this._iconIndexBuffer.array.length;for(let e=0;e<this._iconIndexBuffer.array.length;e++)t[s++]=this._iconIndexBuffer.array[e];t[s++]=this._textVertexBuffer.array.length;for(let e=0;e<this._textVertexBuffer.array.length;e++)r[s++]=this._textVertexBuffer.array[e];t[s++]=this._textIndexBuffer.array.length;for(let e=0;e<this._textIndexBuffer.array.length;e++)t[s++]=this._textIndexBuffer.array[e];return t.buffer}static _symbolsSerializationLength(e){let t=0;t+=1;for(const r of e||[]){t+=4,t+=1;for(const e of r.textColliders)t+=10;for(const e of r.iconColliders)t+=10;t+=1,t+=2*r.textVertexRanges.length,t+=1,t+=2*r.iconVertexRanges.length}return t}static serializeSymbols(e,t,r,n,s){s=s||[],t[n++]=s.length;for(const e of s){t[n++]=e.anchor.x,t[n++]=e.anchor.y,t[n++]=e.symbolFeature.hash,t[n++]=e.symbolFeature.priority,t[n++]=e.textColliders.length+e.iconColliders.length;for(const s of e.textColliders)t[n++]=s.xTile,t[n++]=s.yTile,t[n++]=s.dxPixels,t[n++]=s.dyPixels,t[n++]=s.hard?1:0,t[n++]=s.partIndex,r[n++]=s.minLod,r[n++]=s.maxLod,t[n++]=s.width,t[n++]=s.height;for(const s of e.iconColliders)t[n++]=s.xTile,t[n++]=s.yTile,t[n++]=s.dxPixels,t[n++]=s.dyPixels,t[n++]=s.hard?1:0,t[n++]=s.partIndex,r[n++]=s.minLod,r[n++]=s.maxLod,t[n++]=s.width,t[n++]=s.height;t[n++]=e.textVertexRanges.length;for(const[r,s]of e.textVertexRanges)t[n++]=r,t[n++]=s;t[n++]=e.iconVertexRanges.length;for(const[r,s]of e.iconVertexRanges)t[n++]=r,t[n++]=s}return n}_replaceKeys(e,t){return e.replace(/{([^{}]+)}/g,((e,r)=>r in t?t[r]:""))}_processFeature(e,t,r){const{line:n,iconMosaicItem:s,shaping:i,anchor:o}=e,l=this.zoom,c=this.layer,h=!!s;let u=!0;h&&(u=t?.optional||!s);const f=i&&i.length>0,d=!f||r?.optional;let p,g;if(h&&(p=this._placementEngine.getIconPlacement(o,s,t)),(p||u)&&(f&&(g=this._placementEngine.getTextPlacement(o,i,n,r)),g||d)){if(p&&g||(d||u?d||g?u||p||(g=null):p=null:(p=null,g=null)),g){const t=c.hasDataDrivenText?c.textMaterial.encodeAttributes(e.symbolFeature.feature,l,c):null;if(this._storePlacedGlyphs(e,g.shapes,l,r.rotationAlignment,t),g.textColliders){e.textColliders=g.textColliders;for(const e of g.textColliders){e.minLod=Math.max(l+(0,a.k3)(e.minLod),0),e.maxLod=Math.min(l+(0,a.k3)(e.maxLod),25);const t=e.angle;if(t){const r=Math.cos(t),n=Math.sin(t),s=e.dxPixels*r-e.dyPixels*n,i=e.dxPixels*n+e.dyPixels*r,o=(e.dxPixels+e.width)*r-e.dyPixels*n,a=(e.dxPixels+e.width)*n+e.dyPixels*r,l=e.dxPixels*r-(e.dyPixels+e.height)*n,c=e.dxPixels*n+(e.dyPixels+e.height)*r,h=(e.dxPixels+e.width)*r-(e.dyPixels+e.height)*n,u=(e.dxPixels+e.width)*n+(e.dyPixels+e.height)*r,f=Math.min(s,o,l,h),d=Math.max(s,o,l,h),p=Math.min(i,a,c,u),y=Math.max(i,a,c,u);e.dxPixels=f,e.dyPixels=p,e.width=d-f,e.height=y-p}}}}if(p){const r=c.hasDataDrivenIcon?c.iconMaterial.encodeAttributes(e.symbolFeature.feature,l,c):null;if(this._addPlacedIcons(e,p.shapes,l,s.page,t.rotationAlignment===y.aF.VIEWPORT,r),p.iconColliders){e.iconColliders=p.iconColliders;for(const e of p.iconColliders){e.minLod=Math.max(l+(0,a.k3)(e.minLod),0),e.maxLod=Math.min(l+(0,a.k3)(e.maxLod),25);const t=e.angle;if(t){const r=Math.cos(t),n=Math.sin(t),s=e.dxPixels*r-e.dyPixels*n,i=e.dxPixels*n+e.dyPixels*r,o=(e.dxPixels+e.width)*r-e.dyPixels*n,a=(e.dxPixels+e.width)*n+e.dyPixels*r,l=e.dxPixels*r-(e.dyPixels+e.height)*n,c=e.dxPixels*n+(e.dyPixels+e.height)*r,h=(e.dxPixels+e.width)*r-(e.dyPixels+e.height)*n,u=(e.dxPixels+e.width)*n+(e.dyPixels+e.height)*r,f=Math.min(s,o,l,h),d=Math.max(s,o,l,h),p=Math.min(i,a,c,u),y=Math.max(i,a,c,u);e.dxPixels=f,e.dyPixels=p,e.width=d-f,e.height=y-p}}}}}}_addPlacedIcons(e,t,r,n,s,i){const o=Math.max(r-1,0),l=this._iconVertexBuffer,c=this._iconIndexBuffer,h=this._markerMap;for(const u of t){const t=s?0:Math.max(r+(0,a.k3)(u.minzoom),o),f=s?25:Math.min(r+(0,a.k3)(u.maxzoom),25);if(f<=t)continue;const d=u.tl,p=u.tr,y=u.bl,g=u.br,x=u.mosaicRect,_=u.labelAngle,m=u.minAngle,b=u.maxAngle,I=u.anchor,w=l.index,M=x.x,T=x.y,k=M+x.width,v=T+x.height,P=l.index;l.add(I.x,I.y,d.x,d.y,M,T,_,m,b,t,f,i),l.add(I.x,I.y,p.x,p.y,k,T,_,m,b,t,f,i),l.add(I.x,I.y,y.x,y.y,M,v,_,m,b,t,f,i),l.add(I.x,I.y,g.x,g.y,k,v,_,m,b,t,f,i),e.iconVertexRanges.length>0&&e.iconVertexRanges[0][0]+e.iconVertexRanges[0][1]===P?e.iconVertexRanges[0][1]+=4:e.iconVertexRanges.push([P,4]),c.add(w+0,w+1,w+2),c.add(w+1,w+2,w+3),h.has(n)?h.get(n)[1]+=6:h.set(n,[this._iconIndexStart+this._iconIndexCount,6]),this._iconIndexCount+=6}}_addPlacedGlyphs(){const e=this._textVertexBuffer,t=this._textIndexBuffer,r=this._glyphMap;for(const[n,s]of this._glyphBufferDataStorage)for(const i of s){const s=e.index,o=i.symbolInstance,a=i.ddAttributes,l=e.index;e.add(i.glyphAnchor[0],i.glyphAnchor[1],i.tl[0],i.tl[1],i.xmin,i.ymin,i.labelAngle,i.minAngle,i.maxAngle,i.minLod,i.maxLod,a),e.add(i.glyphAnchor[0],i.glyphAnchor[1],i.tr[0],i.tr[1],i.xmax,i.ymin,i.labelAngle,i.minAngle,i.maxAngle,i.minLod,i.maxLod,a),e.add(i.glyphAnchor[0],i.glyphAnchor[1],i.bl[0],i.bl[1],i.xmin,i.ymax,i.labelAngle,i.minAngle,i.maxAngle,i.minLod,i.maxLod,a),e.add(i.glyphAnchor[0],i.glyphAnchor[1],i.br[0],i.br[1],i.xmax,i.ymax,i.labelAngle,i.minAngle,i.maxAngle,i.minLod,i.maxLod,a),o.textVertexRanges.length>0&&o.textVertexRanges[0][0]+o.textVertexRanges[0][1]===l?o.textVertexRanges[0][1]+=4:o.textVertexRanges.push([l,4]),t.add(s+0,s+1,s+2),t.add(s+1,s+2,s+3),r.has(n)?r.get(n)[1]+=6:r.set(n,[this._textIndexStart+this._textIndexCount,6]),this._textIndexCount+=6}this._glyphBufferDataStorage.clear()}_storePlacedGlyphs(e,t,r,n,s){const i=Math.max(r-1,0),o=n===y.aF.VIEWPORT;let l,c,h,u,f,d,p,g,x,_,m;for(const n of t)l=o?0:Math.max(r+(0,a.k3)(n.minzoom),i),c=o?25:Math.min(r+(0,a.k3)(n.maxzoom),25),c<=l||(h=n.tl,u=n.tr,f=n.bl,d=n.br,p=n.labelAngle,g=n.minAngle,x=n.maxAngle,_=n.anchor,m=n.mosaicRect,this._glyphBufferDataStorage.has(n.page)||this._glyphBufferDataStorage.set(n.page,[]),this._glyphBufferDataStorage.get(n.page).push({glyphAnchor:[_.x,_.y],tl:[h.x,h.y],tr:[u.x,u.y],bl:[f.x,f.y],br:[d.x,d.y],xmin:m.x,ymin:m.y,xmax:m.x+m.width,ymax:m.y+m.height,labelAngle:p,minAngle:g,maxAngle:x,minLod:l,maxLod:c,placementLod:i,symbolInstance:e,ddAttributes:s}))}static _pushAnchors(e,t,r,n){r+=n;let s=0;const i=t.length-1;for(let e=0;e<i;e++)s+=o.E9.distance(t[e],t[e+1]);let l=n||r;if(l*=.5,s<=l)return;const c=l/s;let h=0,u=-(r=s/Math.max(Math.round(s/r),1))/2;const f=t.length-1;for(let n=0;n<f;n++){const s=t[n],i=t[n+1],o=i.x-s.x,l=i.y-s.y,f=Math.sqrt(o*o+l*l);let d;for(;u+r<h+f;){u+=r;const t=(u-h)/f,p=(0,a.sX)(s.x,i.x,t),y=(0,a.sX)(s.y,i.y,t);void 0===d&&(d=Math.atan2(l,o)),e.push(new x(p,y,d,n,c))}h+=f}}static _pushCenterAnchor(e,t){let r=0;const n=t.length-1;for(let e=0;e<n;e++)r+=o.E9.distance(t[e],t[e+1]);const s=r/2;let i=0;const l=t.length-1;for(let r=0;r<l;r++){const n=t[r],o=t[r+1],l=o.x-n.x,c=o.y-n.y,h=Math.sqrt(l*l+c*c);if(s<i+h){const t=(s-i)/h,u=(0,a.sX)(n.x,o.x,t),f=(0,a.sX)(n.y,o.y,t),d=Math.atan2(c,l);return void e.push(new x(u,f,d,r,0))}i+=h}}static _deviation(e,t,r){const n=(t.x-e.x)*(r.x-t.x)+(t.y-e.y)*(r.y-t.y),s=(t.x-e.x)*(r.y-t.y)-(t.y-e.y)*(r.x-t.x);return Math.atan2(s,n)}static _honorsTextMaxAngle(e,t,r,n,s){let i=0;const a=r/2;let l=new o.E9(t.x,t.y),c=t.segment+1;for(;i>-a;){if(--c,c<0)return!1;i-=o.E9.distance(e[c],l),l=e[c]}i+=o.E9.distance(e[c],e[c+1]);const h=[];let u=0;const f=e.length;for(;i<a;){const t=e[c];let r,a=c;do{if(++a,a===f)return!1;r=e[a]}while(r.isEqual(t));let l,d=a;do{if(++d,d===f)return!1;l=e[d]}while(l.isEqual(r));const p=this._deviation(t,r,l);for(h.push({deviation:p,distToAnchor:i}),u+=p;i-h[0].distToAnchor>s;)u-=h.shift().deviation;if(Math.abs(u)>n)return!1;i+=o.E9.distance(r,l),c=a}return!0}static _smoothVertices(e,t){if(t<=0)return e;let r=e.length;if(r<3)return e;const n=[];let s=0,i=0;n.push(0);for(let t=1;t<r;t++){const r=o.E9.distance(e[t],e[t-1]);r>0&&(s+=r,n.push(s),i++,i!==t&&(e[i]=e[t]))}if(r=i+1,r<3)return e;t=Math.min(t,.2*s);const a=e[0].x,l=e[0].y,c=e[r-1].x,h=e[r-1].y,u=o.E9.sub(e[0],e[1]);u.normalize(),e[0].x+=t*u.x,e[0].y+=t*u.y,u.assignSub(e[r-1],e[r-2]),u.normalize(),e[r-1].x+=t*u.x,e[r-1].y+=t*u.y,n[0]-=t,n[r-1]+=t;const f=[];f.push(new o.E9(a,l));const d=1e-6,p=.5*t;for(let s=1;s<r-1;s++){let i=0,a=0,l=0;for(let r=s-1;r>=0;r--){const o=p+n[r+1]-n[s];if(o<0)break;const c=n[r+1]-n[r],h=n[s]-n[r]<p?1:o/c;if(h<d)break;const u=h*h,f=h*o-.5*u*c,y=h*c/t,g=e[r+1],x=e[r].x-g.x,_=e[r].y-g.y;i+=y/f*(g.x*h*o+.5*u*(o*x-c*g.x)-u*h*c*x/3),a+=y/f*(g.y*h*o+.5*u*(o*_-c*g.y)-u*h*c*_/3),l+=y}for(let o=s+1;o<r;o++){const r=p-n[o-1]+n[s];if(r<0)break;const c=n[o]-n[o-1],h=n[o]-n[s]<p?1:r/c;if(h<d)break;const u=h*h,f=h*r-.5*u*c,y=h*c/t,g=e[o-1],x=e[o].x-g.x,_=e[o].y-g.y;i+=y/f*(g.x*h*r+.5*u*(r*x-c*g.x)-u*h*c*x/3),a+=y/f*(g.y*h*r+.5*u*(r*_-c*g.y)-u*h*c*_/3),l+=y}f.push(new o.E9(i/l,a/l))}return f.push(new o.E9(c,h)),e[0].x=a,e[0].y=l,e[r-1].x=c,e[r-1].y=h,f}static _pushCentroid(e,t){const r=4096,n=4096,s=t.length-1;let i=0,o=0,a=0,l=t[0].x,c=t[0].y;l>r&&(l=r),l<0&&(l=0),c>n&&(c=n),c<0&&(c=0);for(let e=1;e<s;e++){let s=t[e].x,h=t[e].y,u=t[e+1].x,f=t[e+1].y;s>r&&(s=r),s<0&&(s=0),h>n&&(h=n),h<0&&(h=0),u>r&&(u=r),u<0&&(u=0),f>n&&(f=n),f<0&&(f=0);const d=(s-l)*(f-c)-(u-l)*(h-c);i+=d*(l+s+u),o+=d*(c+h+f),a+=d}i/=3*a,o/=3*a,isNaN(i)||isNaN(o)||e.push(new x(i,o))}}W._bidiEngine=new G.Z,function(e){e[e.INITIALIZED=0]="INITIALIZED",e[e.NO_DATA=1]="NO_DATA",e[e.READY=2]="READY",e[e.MODIFIED=3]="MODIFIED",e[e.INVALID=4]="INVALID"}(j||(j={}));class Y{constructor(e,t,r,n,s){if(this._pbfTiles={},this._tileClippers={},this._client=r,this._tile=t,s){this._styleLayerUIDs=new Set;for(const e of s)this._styleLayerUIDs.add(e)}this._styleRepository=n,this._layers=this._styleRepository?.layers??[];const[i,a,l]=t.tileKey.split("/").map(parseFloat);this._level=i;const c=8+Math.max(5*(this._level-14),0);for(const t of Object.keys(e)){const r=e[t];if(this._pbfTiles[t]=new T.Z(new Uint8Array(r.protobuff),new DataView(r.protobuff)),r.refKey){const[e]=r.refKey.split("/").map(parseFloat),n=i-e;if(n>0){const e=(1<<n)-1,r=a&e,s=l&e;this._tileClippers[t]=new o.bN(n,r,s,8,c)}}this._tileClippers[t]||(this._tileClippers[t]=new o.I6)}}_canParseStyleLayer(e){return!this._styleLayerUIDs||this._styleLayerUIDs.has(e)}async parse(e){const t=(0,k.j)(),r=this._initialize(e),{returnedBuckets:n}=r;this._processLayers(r),this._linkReferences(r),this._filterFeatures(r);const s=[],i=new Set,o=(e,t)=>{i.has(e)||(s.push({name:e,repeat:t}),i.add(e))},a={};for(const e of n)e.getResources(e.tileClipper,o,a);if(this._tile.status===j.INVALID)return[];const l=this._fetchResources(s,a,e);return Promise.all([...l,t]).then((()=>this._processFeatures(r.returnedBuckets)))}_initialize(e){return{signal:e&&e.signal,sourceNameToTileData:this._parseTileData(this._pbfTiles),layers:this._layers,zoom:this._level,sourceNameToTileClipper:this._tileClippers,sourceNameToUniqueSourceLayerBuckets:{},sourceNameToUniqueSourceLayers:{},returnedBuckets:[],layerIdToBucket:{},referencerUIDToReferencedId:new Map}}_processLayers(e){const{sourceNameToTileData:t,layers:r,zoom:n,sourceNameToTileClipper:s,sourceNameToUniqueSourceLayerBuckets:i,sourceNameToUniqueSourceLayers:o,returnedBuckets:a,layerIdToBucket:l,referencerUIDToReferencedId:c}=e;for(let e=r.length-1;e>=0;e--){const h=r[e];if(!this._canParseStyleLayer(h.uid)||h.minzoom&&n<Math.floor(h.minzoom)||h.maxzoom&&n>=h.maxzoom||h.type===y.fR.BACKGROUND)continue;if(!t[h.source]||!s[h.source])continue;const u=t[h.source],f=s[h.source],d=h.sourceLayer,p=u[d];if(p){let e=o[h.source];if(e||(e=o[h.source]=new Set),e.add(h.sourceLayer),h.refLayerId)c.set(h.uid,h.refLayerId);else{const e=this._createBucket(h);if(e){e.layerUIDs=[h.uid],e.layerExtent=p.extent,e.tileClipper=f;let t=i[h.source];t||(t=i[h.source]={});let r=t[d];r||(r=t[d]=[]),r.push(e),a.push(e),l[h.id]=e}}}}}_linkReferences(e){const{layerIdToBucket:t,referencerUIDToReferencedId:r}=e;r.forEach(((e,r)=>{t[e]&&t[e].layerUIDs.push(r)}))}_filterFeatures(e){const{signal:t,sourceNameToTileData:r,sourceNameToUniqueSourceLayerBuckets:s,sourceNameToUniqueSourceLayers:i}=e,o=10*this._level,a=10*(this._level+1),l=[],c=[];for(const e of Object.keys(i))i[e].forEach((t=>{l.push(t),c.push(e)}));for(let e=0;e<l.length;e++){const i=c[e],h=l[e];if(!r[i]||!s[i])continue;const u=r[i][h],f=s[i][h];if(!f||0===f.length)continue;if((0,n.Hc)(t))return;const d=u.getData();for(;d.nextTag(2);){const e=d.getMessage(),t=new v(e,u);e.release();const r=t.values;if(r){const e=r._minzoom;if(e&&e>=a)continue;const t=r._maxzoom;if(t&&t<=o)continue}for(const e of f)e.pushFeature(t)}}}_fetchResources(e,t,r){const n=[],s=this._tile.getWorkerTileHandler();let i,o;e.length>0&&(i=s.fetchSprites(e,this._client,r),n.push(i));for(const e in t){const i=t[e];i.size>0&&(o=s.fetchGlyphs(this._tile.tileKey,e,i,this._client,r),n.push(o))}return n}_processFeatures(e){const t=e.filter((e=>e.hasFeatures()||this._canParseStyleLayer(e.layer.uid)));for(const e of t)e.processFeatures(e.tileClipper);return t}_parseTileData(e){const t={};for(const r of Object.keys(e)){const n=e[r],s={};for(;n.next();)switch(n.tag()){case 3:{const e=n.getMessage(),t=new E(e);e.release(),s[t.name]=t;break}default:n.skip()}t[r]=s}return t}_createBucket(e){switch(e.type){case y.fR.BACKGROUND:return null;case y.fR.FILL:return this._createFillBucket(e);case y.fR.LINE:return this._createLineBucket(e);case y.fR.CIRCLE:return this._createCircleBucket(e);case y.fR.SYMBOL:return this._createSymbolBucket(e)}}_createFillBucket(e){return new q(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new A(e.fillMaterial.getStride()),new L,new B(e.outlineMaterial.getStride()),new L)}_createLineBucket(e){return new N(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new D(e.lineMaterial.getStride()),new L)}_createCircleBucket(e){return new R(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new V(e.circleMaterial.getStride()),new L)}_createSymbolBucket(e){const t=this._tile;return new W(e,this._level,new S(e.iconMaterial.getStride()),new L,new S(e.textMaterial.getStride()),new L,t.placementEngine,t.getWorkerTileHandler())}}class J{constructor(e,t,r,n){this.status=j.INITIALIZED,this.placementEngine=new I,this.tileKey=e,this.refKeys=t,this._workerTileHandler=r,this._styleRepository=n}release(){this.tileKey="",this.refKeys=null,this.status=j.INITIALIZED,this._workerTileHandler=null}async parse(e,t){const r=t&&t.signal;if((0,i.pC)(r)){const e=()=>{r.removeEventListener("abort",e),this.status=j.INVALID};r.addEventListener("abort",e)}let s;const o={bucketsWithData:[],emptyBuckets:null};try{s=await this._parse(e,t)}catch(e){if((0,n.D_)(e))throw e;return{result:o,transferList:[]}}this.status=j.READY;const a=o.bucketsWithData,l=[];for(const e of s)if(e.hasFeatures()){const t=e.serialize();a.push(t)}else l.push(e.layer.uid);const c=[...a];let h=null;return l.length>0&&(h=Uint32Array.from(l),c.push(h.buffer)),o.emptyBuckets=h,{result:o,transferList:c}}setObsolete(){this.status=j.INVALID}getLayers(){return this._workerTileHandler.getLayers()}getWorkerTileHandler(){return this._workerTileHandler}async _parse(e,t){const r=e.sourceName2DataAndRefKey;return 0===Object.keys(r).length?[]:(this.status=j.MODIFIED,new Y(r,this,t.client,this._styleRepository,e.styleLayerUIDs).parse(t))}}var X=r(51785);class Q{constructor(){this._spriteInfo={},this._glyphInfo={}}reset(){return this._spriteInfo={},this._glyphInfo={},Promise.resolve()}getLayers(){return this._styleRepository?.layers??[]}async createTileAndParse(e,t){const{key:r}=e,s={};for(const t of Object.keys(e.sourceName2DataAndRefKey)){const r=e.sourceName2DataAndRefKey[t];s[t]=r.refKey}const i=new J(r,s,this,this._styleRepository);try{return await i.parse(e,t)}catch(e){if(i.setObsolete(),i.release(),!(0,n.D_)(e))throw e;return null}}updateStyle(e){if(!e||0===e.length||!this._styleRepository)return;const t=this._styleRepository;for(const r of e){const e=r.type,n=r.data;switch(e){case s.Fr.PAINTER_CHANGED:t.setPaintProperties(n.layer,n.paint);break;case s.Fr.LAYOUT_CHANGED:t.setLayoutProperties(n.layer,n.layout);break;case s.Fr.LAYER_REMOVED:t.deleteStyleLayer(n.layer);break;case s.Fr.LAYER_CHANGED:t.setStyleLayer(n.layer,n.index);break;case s.Fr.SPRITES_CHANGED:this._spriteInfo={}}}}setStyle(e){this._styleRepository=new X.Z(e),this._spriteInfo={},this._glyphInfo={}}fetchSprites(e,t,r){const n=[],s=this._spriteInfo;for(const t of e)void 0===s[t.name]&&n.push(t);return 0===n.length?Promise.resolve():t.invoke("getSprites",n,{signal:r&&r.signal}).then((e=>{for(const t in e){const r=e[t];s[t]=r}}))}getSpriteItems(){return this._spriteInfo}fetchGlyphs(e,t,r,n,s){const i=[];let o=this._glyphInfo[t];return o?r.forEach((e=>{o[e]||i.push(e)})):(o=this._glyphInfo[t]=[],r.forEach((e=>i.push(e)))),0===i.length?Promise.resolve():n.invoke("getGlyphs",{tileID:e,font:t,codePoints:i},s).then((e=>{for(let t=0;t<e.length;t++)e[t]&&(o[t]=e[t])}))}getGlyphItems(e){return this._glyphInfo[e]}}}}]);