import{_ as h}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as C}from"./CardTable-rdWOL4_6.js";import{d as T,c as k,r as d,a8 as D,bF as u,dP as w,x as l,S as F,o as N,g as Y,n as A,q as r,F as S,i as p,aB as L,bz as M,C as V}from"./index-r0dFAfgr.js";import{d as v}from"./index-CCFuhOrs.js";import{l as B,m as E,n as U}from"./manage-BReaEVJk.js";import{S as q}from"./data-DDQ4eWNr.js";import"./index-C9hz-UZb.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const z=T({__name:"ssxq",props:{id:{},config:{}},setup(m){const s=k(),n=m,g=d({defaultValue:D(()=>n.config),border:!0,direction:"horizontal",column:2,title:"工程实施基础信息",fields:[{type:"text",label:"实施编号:",field:"code"},{type:"text",label:"工程负责人:",field:"principal"},{type:"text",label:"联系电话:",field:"phone"},{type:"text",label:"施工班组:",field:"constructClass"},{type:"text",label:"工期开始时间:",field:"beginTime",formatter:e=>u(e).format("YYYY-MM-DD")},{type:"text",label:"工期结束时间:",field:"endTime",formatter:e=>u(e).format("YYYY-MM-DD")},{type:"text",label:"备注:",field:"remark"}]}),f=d({defaultExpandAll:!0,indexVisible:!0,title:"管理流程明细",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{var e;o.title="添加管理流程",o.defaultValue={code:`${n.config.code||""}-${w()}`,designCode:n.config.code||"",constructionApplyCode:n.config.constructionCode,type:"提高合理化建议"},(e=s.value)==null||e.openDialog()}}]}]}],columns:[{label:"工作名称",prop:"workName"},{label:"开始时间",prop:"beginTimeName"},{label:"结束时间",prop:"endTimeName"},{label:"工作负责人",prop:"headUser"},{label:"负责人电话",prop:"headUserPhone"},{label:"工作阶段",prop:"workStage"},{label:"添加时间",prop:"createTimeName"}],operationWidth:"160px",operations:[{isTextBtn:!1,type:"success",text:"编辑流程",perm:!0,click:e=>b(e)},{isTextBtn:!1,type:"danger",text:"删除",perm:!0,click:e=>x(e)}],dataList:[],pagination:{hide:!0}}),o=d({title:"新增管理流程",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{if(e.beginTime&&e.endTime&&e.endTime<e.beginTime){l.warning("开始时间不能大于结束时间");return}o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),B(e).then(i=>{var a;o.submitting=!1,i.data.code===200?(l.success(t+"成功"),(a=s.value)==null||a.closeDialog(),c()):l.warning(t+"失败")}).catch(i=>{o.submitting=!1,l.warning(i)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"constructionApplyCode",disabled:!0},{xs:12,type:"input",label:"工作编号",field:"code"},{xs:12,type:"input",label:"工作名称",field:"workName"},{xs:12,type:"select",label:"工作阶段",field:"workStage",options:q},{xs:12,type:"datetime",label:"开始时间",field:"beginTime",format:"x"},{xs:12,type:"datetime",label:"结束时间",field:"endTime",format:"x"},{xs:12,type:"input",label:"工作负责人",field:"headUser"},{xs:12,type:"input",label:"负责人电话",field:"headUserPhone"},{type:"textarea",label:"工作内容",field:"workContent"},{type:"textarea",label:"说明",field:"remark"}]}]}),b=e=>{var t;o.title="编辑管理流程",o.defaultValue={...e||{},constructionCode:n.config.constructionCode},(t=s.value)==null||t.openDialog()},x=e=>{F("确定删除？","提示信息").then(()=>{E(e.id).then(t=>{var i,a;((i=t.data)==null?void 0:i.code)===200?(l.success("删除成功"),c()):l.warning((a=t.data)==null?void 0:a.message)})}).catch(()=>{})},c=async()=>{U({page:1,size:-1,constructionCode:n.config.constructionCode,constructionApplyCode:n.config.constructionCode}).then(e=>{f.dataList=e.data.data.data||[]})};return N(()=>{c()}),(e,t)=>{const i=v,a=M,y=C,_=h;return Y(),A(L,null,[r(a,{class:"card"},{default:S(()=>[r(i,{config:p(g)},null,8,["config"])]),_:1}),r(y,{config:p(f),class:"card-table"},null,8,["config"]),r(_,{ref_key:"refForm",ref:s,config:p(o)},null,8,["config"])],64)}}}),K=V(z,[["__scopeId","data-v-44c23581"]]);export{K as default};
