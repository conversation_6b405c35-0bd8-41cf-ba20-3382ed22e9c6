package org.thingsboard.server.dao.organization;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentSaveRequest;

import java.util.List;

@Service
public class DepartmentServiceImpl implements DepartmentService {
    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private UserMapper userMapper;


    @Override
    public List<Department> findAllConditional(DepartmentPageRequest request) {
        return departmentMapper.findAll(request);
    }

    @Override
    public Department save(DepartmentSaveRequest request) {
        return QueryUtil.saveOrUpdateOneByRequest(request, departmentMapper::insert, departmentMapper::update);
    }

    @Override
    public boolean update(Department entity) {
        return departmentMapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        return departmentMapper.deleteWithChildrenRecursive(id) > 0;
    }

    @Override
    public List<Department> findAllStructure(String rootId, Integer depth, String tenantId) {
        if (depth > 1)
            return QueryUtil.buildTree(departmentMapper.findRoots(rootId, tenantId),
                    pid -> departmentMapper.findChildren(pid, tenantId),
                    userMapper::findByDepartmentId);
        return QueryUtil.buildTree(departmentMapper.findRoots(rootId, tenantId),
                pid -> departmentMapper.findChildren(pid, tenantId));
    }

    @Override
    public boolean canBeDelete(String id) {
        return departmentMapper.canBeDelete(id);
    }

    @Override
    public boolean canBeAdd(String parentId) {
        return departmentMapper.canBeAdd(parentId);
    }

    @Override
    public List<Department> findByTenantId(String tenantId) {
        QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        return departmentMapper.selectList(queryWrapper);
    }

    @Override
    public Department findById(String id) {
        return departmentMapper.selectById(id);
    }

}
