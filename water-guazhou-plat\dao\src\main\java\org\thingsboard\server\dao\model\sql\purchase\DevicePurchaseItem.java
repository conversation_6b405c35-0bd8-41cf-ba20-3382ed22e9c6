package org.thingsboard.server.dao.model.sql.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse;
import org.thingsboard.server.dao.sql.purchase.DevicePurchaseItemMapper;
import org.thingsboard.server.dao.sql.purchase.DevicePurchaseMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DevicePurchaseItem {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    // 状态 0-待询价 1-询价中 2-已完成
    @InfoViaMapper(name = "status", mapper = DevicePurchaseItemMapper.class)
    private String id;

    // 采购单ID
    @ParseViaMapper(DevicePurchaseMapper.class)
    private String mainId;

    // 设备编码
    private String serialId;

    // 询价截止日期
    private Date inquiryEndTime;

    // 购买数量
    private Double num;

    // 是否完成询价
    private Boolean inquired;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    @TableField(exist = false)
    private Integer price;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;

}
