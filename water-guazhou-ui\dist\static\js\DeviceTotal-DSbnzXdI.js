import{d,g as o,h as l,F as n,n as i,aJ as f,p as e,bh as t,G as v,av as g,aB as m,bU as b,bW as x,C as k}from"./index-r0dFAfgr.js";const y={class:"unit"},B=d({__name:"DeviceTotal",props:{deviceTotal:{}},setup(r){const c=r;return(C,s)=>{const p=b,u=x;return o(),l(u,{gutter:20},{default:n(()=>[(o(!0),i(m,null,f(c.deviceTotal,(a,_)=>(o(),l(p,{key:_,span:a.span,style:{margin:"10px 0"}},{default:n(()=>[e("div",{class:"total-item",style:g({background:a.bgColor,color:a.color||"#fff"})},[e("p",null,[e("span",null,t(a.value),1),s[0]||(s[0]=v()),e("span",y,t(a.unit),1)]),e("p",null,[e("span",null,t(a.label),1)])],4)]),_:2},1032,["span"]))),128))]),_:1})}}}),h=k(B,[["__scopeId","data-v-54d45817"]]);export{h as default};
