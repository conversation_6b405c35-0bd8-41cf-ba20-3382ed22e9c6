import{d as n,c as r,b4 as c,cr as l,o as d,am as i,C as p,g as u,n as f,p as h}from"./index-r0dFAfgr.js";const m=n({name:"SysPage",setup(){const o=r("https://www.baidu.com/"),t=c(),a=()=>{const e=t.path.split("/extendPage/sysPage/");if(e.length<2)return;console.log(e,e[e.length-1],"---------p");const s=e[e.length-1];o.value=decodeURIComponent(atob(s)),console.log(o.value,"解出的链接")};return l((e,s)=>{console.log(e,s)}),d(()=>{a()}),i(()=>t.params,()=>{a()}),{url:o}}}),g={class:"energyBoard-container"},_=["src"];function b(o,t,a,e,s,w){return u(),f("div",g,[h("iframe",{ref:"iframe",src:o.url,scrolling:"auto",seamless:"",height:"100%",width:"100%",frameborder:"0",allowfullscreen:"",class:"iframe-container"},null,8,_)])}const B=p(m,[["render",b],["__scopeId","data-v-b062cb45"]]);export{B as default};
