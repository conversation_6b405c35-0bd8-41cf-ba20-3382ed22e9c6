package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_video")
public class SsPortalVideo {

    private String id;

    private String title;

    private String coverImg;

    private String vsize;

    private String vformat;

    private String resolution;

    private String isIntroduce;

    private String isPublicize;

    private Integer orderNum;

    private String creator;

    private String url;

    @TableField(exist = false)
    private String creatorName;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
