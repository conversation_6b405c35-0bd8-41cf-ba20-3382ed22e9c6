import{e as a,v as w,y as n,a as G}from"./Point-WxyopZva.js";import{c as z,l as M,U as I}from"./widget-BcWKanF2.js";import{R as r,T as d}from"./index-r0dFAfgr.js";import{cx as b,x as H,dz as E,dA as S,dB as v,as as L,bM as R,dC as O,aS as X,af as Y,dD as k}from"./MapView-DaoQedLH.js";import{l as A}from"./elevationInfoUtils-5B4aSzEU.js";import{o as N}from"./drapedUtils-DJwxIB1g.js";function V(e){let s=0,i=0,t=0;return e?(e.type==="cim"&&e.data.symbol&&"symbolLayers"in e.data.symbol&&e.data.symbol.symbolLayers&&e.data.symbol.symbolLayers.map(o=>{o.type==="CIMVectorMarker"&&o.anchorPoint&&(Math.abs(o.anchorPoint.x)>s&&(s=o.anchorPoint.x),Math.abs(o.anchorPoint.y)>i&&(i=o.anchorPoint.y),r(o.size)&&o.size>t&&(t=o.size))}),s=b(s),i=b(i),t=b(t),{offsetX:s,offsetY:i,size:t}):{offsetX:s,offsetY:i,size:t}}let l=class extends w{set graphic(e){this._circleCollisionCache=null,this._originalSymbol=e.symbol,this._set("graphic",e),this.attachSymbolChanged()}get elevationInfo(){const{layer:e}=this.graphic,s=e&&"elevationInfo"in e?e.elevationInfo:null,i=A(this.graphic),t=s?s.offset:0;return new H({mode:i,offset:t})}set focusedSymbol(e){e!==this._get("focusedSymbol")&&(this._set("focusedSymbol",e),this._updateGraphicSymbol(),this._circleCollisionCache=null)}grabbableForEvent(){return!0}set grabbing(e){e!==this._get("grabbing")&&(this._set("grabbing",e),this._updateGraphicSymbol())}set hovering(e){e!==this._get("hovering")&&(this._set("hovering",e),this._updateGraphicSymbol())}set selected(e){e!==this._get("selected")&&(this._set("selected",e),this._updateGraphicSymbol(),this.events.emit("select-changed",{action:e?"select":"deselect"}))}get _focused(){return this._get("hovering")||this._get("grabbing")}constructor(e){super(e),this.layer=null,this.interactive=!0,this.selectable=!1,this.grabbable=!0,this.dragging=!1,this.cursor=null,this.events=new z.EventEmitter,this._circleCollisionCache=null,this._graphicSymbolChangedHandle=null,this._originalSymbol=null}destroy(){this.detachSymbolChanged(),this._resetGraphicSymbol(),this._set("view",null)}intersectionDistance(e){const s=this.graphic;if(s.visible===!1)return null;const i=s.geometry;if(d(i))return null;const t=this._get("focusedSymbol"),o=r(t)?t:s.symbol;return this.view.type==="2d"?this._intersectDistance2D(this.view,e,i,o):this._intersectDistance3D(this.view,e,s)}attach(){this.attachSymbolChanged(),r(this.layer)&&this.layer.add(this.graphic)}detach(){this.detachSymbolChanged(),this._resetGraphicSymbol(),r(this.layer)&&this.layer.remove(this.graphic)}attachSymbolChanged(){this.detachSymbolChanged(),this._graphicSymbolChangedHandle=M(()=>{var e;return(e=this.graphic)==null?void 0:e.symbol},e=>{r(e)&&e!==this.focusedSymbol&&e!==this._originalSymbol&&(this._originalSymbol=e,this._focused&&r(this.focusedSymbol)&&(this.graphic.symbol=this.focusedSymbol))},I)}detachSymbolChanged(){r(this._graphicSymbolChangedHandle)&&(this._graphicSymbolChangedHandle.remove(),this._graphicSymbolChangedHandle=null)}onElevationChange(){}onViewChange(){}_updateGraphicSymbol(){this.graphic.symbol=this._focused&&r(this.focusedSymbol)?this.focusedSymbol:this._originalSymbol}_resetGraphicSymbol(){this.graphic.symbol=this._originalSymbol}_intersectDistance2D(e,s,i,t){var g,f;if(t=t||E(i),d(t))return null;const o=1;let c=this._circleCollisionCache;if(i.type==="point"&&t.type==="cim"&&((g=t.data.symbol)==null?void 0:g.type)==="CIMPointSymbol"&&t.data.symbol.symbolLayers){const{offsetX:y,offsetY:h,size:p}=V(t),u=S(s,x),m=p/2,_=e.toScreen(i),P=_.x+y,D=_.y+h;return v(u,[P,D])<m*m?o:null}if(i.type!=="point"||t.type!=="simple-marker")return N(s,i,e)?o:null;if(d(c)||!c.originalPoint.equals(i)){const y=i,h=e.spatialReference;if(L(y.spatialReference,h)){const p=R(y,h);c={originalPoint:y.clone(),mapPoint:p,radiusPx:b(t.size)},this._circleCollisionCache=c}}if(r(c)){const y=S(s,x),h=(f=e.toScreen)==null?void 0:f.call(e,c.mapPoint);if(!h)return null;const p=c.radiusPx,u=h.x+b(t.xoffset),m=h.y-b(t.yoffset);return v(y,[u,m])<p*p?o:null}return null}_intersectDistance3D(e,s,i){const t=e.toMap(s,{include:[i]});return t&&O(t,C,e.renderSpatialReference)?X(C,e.state.camera.eye):null}};a([n({constructOnly:!0,nonNullable:!0})],l.prototype,"graphic",null),a([n()],l.prototype,"elevationInfo",null),a([n({constructOnly:!0,nonNullable:!0})],l.prototype,"view",void 0),a([n({value:null})],l.prototype,"focusedSymbol",null),a([n({constructOnly:!0})],l.prototype,"layer",void 0),a([n()],l.prototype,"interactive",void 0),a([n()],l.prototype,"selectable",void 0),a([n()],l.prototype,"grabbable",void 0),a([n({value:!1})],l.prototype,"grabbing",null),a([n()],l.prototype,"dragging",void 0),a([n()],l.prototype,"hovering",null),a([n({value:!1})],l.prototype,"selected",null),a([n()],l.prototype,"cursor",void 0),l=a([G("esri.views.interactive.GraphicManipulator")],l);const C=Y(),x=k();export{l as j,V as t};
