package org.thingsboard.server.dao.sql.role;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.RoleApplicationMenu;

import java.util.List;

public interface MenuApplicationRoleRepository extends JpaRepository<RoleApplicationMenu, String> {
    void deleteByRoleIdAndTenantApplicationId(String roleId, String tenantApplicationId);

    List<RoleApplicationMenu> findByRoleIdAndTenantApplicationId(String roleId, String tenantApplicationId);

    @Query("SELECT t.tenantApplicationId FROM RoleApplicationMenu t " +
            "WHERE t.roleId = ?1 " +
            "GROUP BY t.tenantApplicationId")
    List<String> getRoleTenantApplicationList(String strRoleId);

    @Query("SELECT mr.menuId FROM RoleEntity r, RoleApplicationMenu mr, UserRoleEntity ur " +
            "WHERE r.id = mr.roleId AND r.id = ur.roleId AND ur.userId = :userId AND mr.tenantApplicationId = :tenantApplicationId")
    List<String> findByUserId(@Param("userId") String userId, @Param("tenantApplicationId") String tenantApplicationId);
}
