package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-01
 */
@TableName("tb_supplier_qualifications")
@Data
public class SupplierQualificationsEntity {

    @TableId
    private String id;

    private String mainId;

    private String name;

    private String organization;

    private Date time;

    private String files;

    private Date createTime;

    private String tenantId;
}
