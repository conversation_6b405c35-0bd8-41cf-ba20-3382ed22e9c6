package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordPageRequest;

@Mapper
public interface StoreInRecordMapper extends BaseMapper<StoreInRecord> {
    IPage<StoreInRecord> findByPage(StoreInRecordPageRequest request);

    boolean update(StoreInRecord attr);

    int save(StoreInRecord entity);

    @Override
    default int insert(StoreInRecord entity) {
        return save(entity);
    }

    String getCodeById(String id);
}
