import{_ as k}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as T}from"./CardTable-rdWOL4_6.js";import{_ as L}from"./CardSearch-CB_HNR-Q.js";import{d as D,c as p,a8 as y,s as b,r as d,x as u,dP as N,a9 as m,o as v,g as w,n as A,q as f,i as g,b7 as E}from"./index-r0dFAfgr.js";import{I as C}from"./common-CvK_P_ao.js";import{P as O,Q as R,N as q,g as j,b as B,D as S,R as U}from"./manage-BReaEVJk.js";import I from"./detail-C-Xa-EGR.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./device-DWHb0XjG.js";import"./projectManagement-CDcrrCQ1.js";import"./DateFormatter-Bm9a68Ax.js";import"./data-Dv9-Tstw.js";import"./detail-DEo1RlcF.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const F={class:"wrapper"},Dt=D({__name:"implement",setup(P){const c=p(),l=p(),x=p({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:y(()=>a.projectType)}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:C.DOWNLOAD,click:()=>{O().then(t=>{const e=window.URL.createObjectURL(t.data),o=document.createElement("a");o.style.display="none",o.href=e,o.setAttribute("download","实施管理.xlsx"),document.body.appendChild(o),o.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:b(E),click:()=>{var t;(t=c.value)==null||t.resetForm(),s()}},{perm:!0,text:"查询",icon:C.QUERY,click:()=>s()}]}]}),i=d({defaultExpandAll:!1,indexVisible:!0,expandable:!0,expandComponent:b(I),extendedReturn:()=>{s()},rowKey:"constructionCode",columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"}],operationWidth:"260px",operations:[{isTextBtn:!1,type:"success",text:"新增实施明细",perm:!0,click:t=>{_(t)}},{isTextBtn:!1,text:"导出实施情况",perm:!0,click:t=>{R(t.constructionCode).then(e=>{const o=window.URL.createObjectURL(e.data),n=document.createElement("a");n.style.display="none",n.href=o,n.setAttribute("download",`${t.constructionName}实施管理.xlsx`),document.body.appendChild(n),n.click()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,s()}}}),r=d({title:"添加实施明细",appendToBody:!0,labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:t=>{r.submitting=!0;let e="新增";t.id&&(e="修改"),t.time&&(t.workTimeBegin=t.time[0],t.workTimeEnd=t.time[1],delete t.time),q(t).then(o=>{var n;r.submitting=!1,o.data.code===200?(u.success(e+"成功"),(n=l.value)==null||n.closeDialog()):u.warning(e+"失败"),s()}).catch(o=>{r.submitting=!1,u.warning(o)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"实施编号",field:"code",rules:[{required:!0,message:"请输入实施编号"}],disabled:!0},{xs:12,type:"select",label:"所属合同",field:"contractCode",rules:[{required:!0,message:"请选择所属合同"}],options:y(()=>a.contractList)},{xs:12,type:"date",label:"工期开始时间",field:"beginTime",rules:[{required:!0,message:"请输入工期开始时间"}]},{xs:12,type:"date",label:"工期结束时间",field:"endTime",rules:[{required:!0,message:"请输入工期结束时间"}]},{xs:12,type:"input",label:"工程负责人",field:"principal",rules:[{required:!0,message:"请输入工程负责人"}]},{xs:12,type:"input",label:"联系电话",field:"phone"},{xs:12,type:"input",label:"施工班组",field:"constructClass"},{type:"textarea",label:"详细说明",field:"remark"}]}]}),_=t=>{var e;r.title="添加工程合同",r.defaultValue={code:`${t.constructionCode.replace("S","Q")}-${N()}`,constructionCode:t.constructionCode,constructionName:t.constructionName},a.getConstructionContract(t.constructionCode),(e=l.value)==null||e.openDialog()},a=d({projectType:[],ConstructionContractType:[],contractList:[],getOptions:()=>{j({page:1,size:-1}).then(t=>{a.projectType=m(t.data.data.data||[],"children")}),B({page:1,size:-1}).then(t=>{a.ConstructionContractType=m(t.data.data.data||[],"children")})},getConstructionContract:t=>{S(t).then(e=>{a.contractList=m(e.data.data.data||[],"children",{label:"name",value:"code"})})}}),s=async()=>{var e;const t={size:i.pagination.limit||20,page:i.pagination.page||1,...((e=c.value)==null?void 0:e.queryParams)||{}};U(t).then(o=>{i.dataList=o.data.data.data||[],i.pagination.total=o.data.data.total||0})};return v(()=>{s(),a.getOptions()}),(t,e)=>{const o=L,n=T,h=k;return w(),A("div",F,[f(o,{ref_key:"refSearch",ref:c,config:g(x)},null,8,["config"]),f(n,{config:g(i),class:"card-table"},null,8,["config"]),f(h,{ref_key:"refForm",ref:l,config:g(r)},null,8,["config"])])}}});export{Dt as default};
