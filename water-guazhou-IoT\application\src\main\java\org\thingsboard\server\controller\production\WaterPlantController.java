package org.thingsboard.server.controller.production;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智慧生产-水厂管理
 */
@RestController
@RequestMapping("api/production/waterPlant")
public class WaterPlantController extends BaseController {

    @Autowired
    private ProductionService productionService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @GetMapping("getWaterSupplyInfo")
    public IstarResponse getWaterSupplyInfo(@RequestParam(required = false, defaultValue = "") String projectId,
                                            @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        return IstarResponse.ok(productionService.getWaterSupplyInfo(name, DataConstants.StationType.WATER_PLANT.getValue() + "," + DataConstants.StationType.SEWAGE_PROCESS_STATION.getValue(),
                projectId, getTenantId()));
    }

    @GetMapping("gis/getWaterSupplyDetail")
    public IstarResponse getWaterSupplyDetail(@RequestParam String stationId) throws ThingsboardException {
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return IstarResponse.error("要查询的水厂不存在, 水厂ID: " + stationId);
        }
        // 供水曲线数据
        JSONObject waterSupplyDetail = productionService.getWaterSupplyDetail(stationId, getTenantId());

        // 今日出水压力、今日出口瞬时流量
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.PRESSURE.getValue());
        attrList.add(DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue(), attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        return IstarResponse.ok(waterSupplyDetail);
    }


    /**
     * 获取指定站点的取水供水信息
     * 包含：今日取水量、今日供水量、昨日供水量、本月供水量
     */
    @GetMapping("gis/getWaterInfo")
    public IstarResponse getWaterInfo(@RequestParam String stationId) throws ThingsboardException {
        try {
            return IstarResponse.ok(productionService.getWaterInfo(stationId, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 获取平衡分析数据
     */
    @GetMapping("getBalanceReport")
    public IstarResponse getBalanceReport(@RequestParam String queryType, @RequestParam String stationId,
                                          @RequestParam Long start, @RequestParam Long end) {
        try {
            return IstarResponse.ok(productionService.getBalanceReport(start, end, queryType, stationId, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 获取能耗分析数据
     */
    @GetMapping("getEnergyReport")
    public IstarResponse getEnergyReport(@RequestParam Long start, @RequestParam Long end, @RequestParam String queryType,
                                         @RequestParam(required = false) String stationType) {
        try {
            String type = DataConstants.StationType.WATER_PLANT.getValue() + "," + DataConstants.StationType.SEWAGE_PROCESS_STATION.getValue();
            if (StringUtils.isNotBlank(stationType)) {
                type = stationType;
            }
            return IstarResponse.ok(productionService.getEnergyReport(type, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 获取能耗分析数据详情
     *
     * @param start     查询时间范围的开始时间
     * @param end       查询时间范围的结束时间
     * @param stationId 站点ID
     * @param queryType 查询类型
     * @return 数据
     */
    @GetMapping("getEnergyDetailReport")
    public IstarResponse getEnergyDetailReport(@RequestParam Long start, @RequestParam Long end,
                                               @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getEnergyDetailReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 出厂指标-出水流量
     */
    @GetMapping("getWaterSupplyFlowReport")
    public IstarResponse getWaterSupplyFlowReport(@RequestParam String stationId, @RequestParam String queryType,
                                                  @RequestParam String time, @RequestParam String compareType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyFlowReport(stationId, queryType, time, compareType, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 导出出厂指标-出水流量
     */
    @GetMapping("getWaterSupplyFlowReport/export")
    public IstarResponse getWaterSupplyFlowReport(@RequestParam String stationId, @RequestParam String queryType,
                                                  @RequestParam String time, @RequestParam String compareType,
                                                  HttpServletResponse response) {
        try {
            Object waterSupplyFlowReport = productionService.getWaterSupplyFlowReport(stationId, queryType, time, compareType, getTenantId());
            JSONObject dataObj = JSONObject.parseObject(JSONObject.toJSONString(waterSupplyFlowReport));
            DynamicTableVO dynamicTableVO = dataObj.getJSONObject("baseTable").toJavaObject(DynamicTableVO.class);

            List<DeviceDataTableInfoVO> tableInfo = dynamicTableVO.getTableInfo();
            List<JSONObject> data = dynamicTableVO.getTableDataList();

            // 数据列表
            Map headMap = new LinkedHashMap();
            for (DeviceDataTableInfoVO infoVO : tableInfo) {
                if (StringUtils.isNotBlank(infoVO.getUnit())) {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName() + "("+infoVO.getUnit()+")");
                } else {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName());
                }
            }

            // 水质报表
            String title = "出水流量报表";

            JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
            ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

            return IstarResponse.ok();
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 出厂指标-出水压力
     */
    @GetMapping("getWaterSupplyPressureReport")
    public IstarResponse getWaterSupplyPressureReport(@RequestParam String stationId, @RequestParam String attributeId,
                                                      @RequestParam String time, @RequestParam String compareType,
                                                      @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyPressureReport(stationId, attributeId, queryType, time, compareType, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 导出出厂指标-出水压力
     */
    @GetMapping("getWaterSupplyPressureReport/export")
    public IstarResponse getWaterSupplyPressureReport(@RequestParam String stationId, @RequestParam String attributeId,
                                                      @RequestParam String time, @RequestParam String compareType,
                                                      @RequestParam String queryType, HttpServletResponse response) {
        try {
            Object waterSupplyPressureReport = productionService.getWaterSupplyPressureReport(stationId, attributeId, queryType, time, compareType, getTenantId());
            JSONObject dataObj = JSONObject.parseObject(JSONObject.toJSONString(waterSupplyPressureReport));
            DynamicTableVO dynamicTableVO = dataObj.getJSONObject("baseTable").toJavaObject(DynamicTableVO.class);

            List<DeviceDataTableInfoVO> tableInfo = dynamicTableVO.getTableInfo();
            List<JSONObject> data = dynamicTableVO.getTableDataList();

            // 数据列表
            Map headMap = new LinkedHashMap();
            for (DeviceDataTableInfoVO infoVO : tableInfo) {
                if (StringUtils.isNotBlank(infoVO.getUnit())) {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName() + "("+infoVO.getUnit()+")");
                } else {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName());
                }
            }

            // 水质报表
            String title = "出水压力报表";

            JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
            ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

            return IstarResponse.ok();
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 出厂指标-出水水质
     */
    @GetMapping("getWaterSupplyQualityReport")
    public IstarResponse getWaterSupplyQualityReport(@RequestParam String stationId, @RequestParam String attrType,
                                                     @RequestParam String time, @RequestParam String compareType,
                                                     @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyQualityReport(stationId, attrType, queryType, time, compareType, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 出厂指标-出水水质
     */
    @GetMapping("getWaterSupplyQualityReport/export")
    public IstarResponse getWaterSupplyQualityReport(@RequestParam String stationId, @RequestParam String attrType,
                                                     @RequestParam String time, @RequestParam String compareType,
                                                     @RequestParam String queryType, HttpServletResponse response) {
        try {
            Object waterSupplyQualityReport = productionService.getWaterSupplyQualityReport(stationId, attrType, queryType, time, compareType, getTenantId());
            JSONObject dataObj = JSONObject.parseObject(JSONObject.toJSONString(waterSupplyQualityReport));
            DynamicTableVO dynamicTableVO = dataObj.getJSONObject("baseTable").toJavaObject(DynamicTableVO.class);

            List<DeviceDataTableInfoVO> tableInfo = dynamicTableVO.getTableInfo();
            List<JSONObject> data = dynamicTableVO.getTableDataList();

            // 数据列表
            Map headMap = new LinkedHashMap();
            for (DeviceDataTableInfoVO infoVO : tableInfo) {
                if (StringUtils.isNotBlank(infoVO.getUnit())) {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName() + "("+infoVO.getUnit()+")");
                } else {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName());
                }
            }

            // 水质报表
            String title = "出水水质报表";

            JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
            ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

            return IstarResponse.ok();
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 水厂报表-水厂报表
     * 包含水厂的进出水累计流量并统计最大值最大值时间、最小值最小值时间、平均值、合计
     */
    @GetMapping("getWaterPlantFlowReport")
    public IstarResponse getWaterPlantFlowReport(@RequestParam String stationIds, @RequestParam String queryType,
                                                 @RequestParam String time) {
        try {
            return IstarResponse.ok(productionService.getWaterPlantFlowReport(
                    DataConstants.StationType.WATER_PLANT.getValue() + "," + DataConstants.StationType.SEWAGE_PROCESS_STATION.getValue()
                    , Arrays.stream(stationIds.split(",")).collect(Collectors.toList()), queryType, time, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 水厂报表-生产报表
     */
    @GetMapping("getWaterPlantProductionReport")
    public IstarResponse getWaterPlantProductionReport(@RequestParam String stationId, @RequestParam String groupType,
                                                       @RequestParam String queryType, @RequestParam String time) {
        try {
            return IstarResponse.ok(productionService.getWaterPlantProductionReport(stationId, groupType, queryType, time, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询多个站点的水量报表
     */
    @GetMapping("getWaterSupplyDetailReport")
    public IstarResponse getWaterSupplyDetailReport(@RequestParam Long start, @RequestParam Long end,
                                                    @RequestParam String stationIdList, @RequestParam String queryType) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            return IstarResponse.ok(productionService.getWaterSupplyDetailReport(DataConstants.StationType.WATER_PLANT.getValue() + "," + DataConstants.StationType.SEWAGE_PROCESS_STATION.getValue(),
                    Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, queryType, getTenantId(), true));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 导出多个站点的水量报表
     */
    @GetMapping("getWaterSupplyDetailReport/export")
    public IstarResponse getWaterSupplyDetailReportExport(@RequestParam Long start, @RequestParam Long end,
                                                    @RequestParam String stationIdList, @RequestParam String queryType,
                                                    HttpServletResponse response) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            DynamicTableVO dynamicTableVO = productionService.getWaterSupplyDetailReport(DataConstants.StationType.WATER_PLANT.getValue() + "," + DataConstants.StationType.SEWAGE_PROCESS_STATION.getValue(),
                    Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, queryType, getTenantId(), true);

            List<DeviceDataTableInfoVO> tableInfo = dynamicTableVO.getTableInfo();
            List<JSONObject> data = dynamicTableVO.getTableDataList();

            // 数据列表
            Map headMap = new LinkedHashMap();
            for (DeviceDataTableInfoVO infoVO : tableInfo) {
                if (StringUtils.isNotBlank(infoVO.getUnit())) {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName() + "("+infoVO.getUnit()+")");
                } else {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName());
                }
            }

            // 水质报表
            String title = "供水明细报表";

            JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
            ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

            return IstarResponse.ok();
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询多个站点的水量报表
     */
    @GetMapping("getWaterProcessDetailReport")
    public IstarResponse getWaterProcessDetailReport(@RequestParam Long start, @RequestParam Long end,
                                                    @RequestParam String stationIdList, @RequestParam String queryType) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            return IstarResponse.ok(productionService.getWaterProcessDetailReport(DataConstants.StationType.WATER_PLANT.getValue() + "," + DataConstants.StationType.SEWAGE_PROCESS_STATION.getValue(),
                    Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }


    /**
     * 查询指定站点的指定变量数据列表
     */
    @GetMapping("getStationDataByAttr")
    public IstarResponse getStationDataByAttr(@RequestParam String stationId, @RequestParam String attr,
                                                      @RequestParam Long startTime, @RequestParam Long endTime,
                                                      @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getStationDataByAttr(stationId, attr, queryType, startTime, endTime, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询各个水厂的今日进、出水累积量
     */
    @GetMapping("getWaterPlantDataInfo")
    public IstarResponse getWaterPlantDataInfo() {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyDataInfo(DataConstants.StationType.WATER_PLANT.getValue(), getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 曲线运行-获取实时数据
     */
    @GetMapping("getCurveOperationData")
    public IstarResponse getCurveOperationData(@RequestParam(required = false) String stationId) {
        try {
            return IstarResponse.ok(productionService.getCurveOperationData(stationId, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 曲线运行-获取预测数据
     */
    @GetMapping("getCurveOperationPrediction")
    public IstarResponse getCurveOperationPrediction(@RequestParam(required = false) String type,
                                                     @RequestParam(required = false) String pressureType,
                                                     @RequestParam(required = false) String stationId,
                                                     @RequestParam(required = false, defaultValue = "24") Integer timeRange) {
        try {
            return IstarResponse.ok(productionService.getCurveOperationPrediction(type, pressureType, stationId, timeRange, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 曲线运行-获取出水流量历史数据
     */
    @GetMapping("getOutletFlowHistory")
    public IstarResponse getOutletFlowHistory(@RequestParam(required = false) String stationId,
                                              @RequestParam Long startTime,
                                              @RequestParam Long endTime,
                                              @RequestParam(required = false, defaultValue = "hour") String granularity) {
        try {
            return IstarResponse.ok(productionService.getOutletFlowHistory(stationId, startTime, endTime, granularity, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 曲线运行-获取压力历史数据
     */
    @GetMapping("getPressureHistory")
    public IstarResponse getPressureHistory(@RequestParam(required = false) String stationId,
                                            @RequestParam Long startTime,
                                            @RequestParam Long endTime,
                                            @RequestParam String pressureType,
                                            @RequestParam(required = false, defaultValue = "hour") String granularity) {
        try {
            return IstarResponse.ok(productionService.getPressureHistory(stationId, startTime, endTime, pressureType, granularity, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 曲线运行-获取水体药物浓度数据
     */
    @GetMapping("getWaterConcentrationData")
    public IstarResponse getWaterConcentrationData(@RequestParam(required = false) String stationId,
                                                   @RequestParam Long startTime,
                                                   @RequestParam Long endTime,
                                                   @RequestParam(required = false) String drugType) {
        try {
            return IstarResponse.ok(productionService.getWaterConcentrationData(stationId, startTime, endTime, drugType, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

}
