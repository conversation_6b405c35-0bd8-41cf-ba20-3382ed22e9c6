/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.alarm;

import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.alarm.AlarmJsonDao;
import org.thingsboard.server.dao.model.sql.AlarmJsonEntity;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;


/**
 * Created by jerry 2019-01-02
 */
@Slf4j
@Component
@SqlDao
public class JpaAlarmJsonDao extends JpaAbstractDao<AlarmJsonEntity, AttrAlarmJson> implements AlarmJsonDao {

    @Autowired
    private AlarmJsonRepository alarmRepository;

    @Override
    protected Class<AlarmJsonEntity> getEntityClass() {
        return AlarmJsonEntity.class;
    }

    @Override
    protected CrudRepository<AlarmJsonEntity, String> getCrudRepository() {
        return alarmRepository;
    }


    @Override
    public AttrAlarmJson createOrUpdateAlarmJson(AttrAlarmJson attrAlarmJson) {
        return this.save(attrAlarmJson);
    }

    @Override
    public ListenableFuture<AttrAlarmJson> findAlarmByIdAsync(AlarmJsonId alarmJsonId) {
        return service.submit(() -> this.findById(alarmJsonId.getId()));
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByParams(TenantId tenantId, DeviceId deviceId, String attribute) {
        return (service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByTenantIdAndDeviceIdAndAttribute(UUIDConverter.fromTimeUUID(tenantId.getId()), UUIDConverter.fromTimeUUID(deviceId.getId()), attribute))));
    }

    @Override
    public void deleteAlarmJsonById(AlarmJsonId alarmJsonId) {
        this.removeById(alarmJsonId.getId());
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByDeviceAndAttribute(DeviceId deviceId, String attribute) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByDeviceIdAndAttribute(UUIDConverter.fromTimeUUID(deviceId.getId()), attribute)));
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByTenant(TenantId tenantId) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()))));
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByDevice(DeviceId deviceId) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByDeviceId(UUIDConverter.fromTimeUUID(deviceId.getId()))));
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByDeviceAndPropAndLevel(DeviceId deviceId, String prop, String level) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByDeviceIdAndAttributeAndSeverity(UUIDConverter.fromTimeUUID(deviceId.getId()),prop,level)));
    }
}
