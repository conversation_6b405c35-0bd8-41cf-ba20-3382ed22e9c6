package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigSaveRequest;

import java.util.List;

public interface CircuitConfigService {
    /**
     * 分页条件查询巡检配置模板，巡检配置模板用于生成巡检任务条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitConfig> findAllConditional(CircuitConfigPageRequest request);

    /**
     * 保存巡检配置模板
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitConfig save(CircuitConfigSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitConfig entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 通过客户id来获取当前客户的所有已有的配置模板的巡检项目分类
     *
     * @param tenantId 客户id
     * @return 所有已有的配置模板的巡检项目分类（中文）
     */
    List<String> getTypes(String tenantId);

}
