import{d as M,r as y,s as u,c as p,x as m,y as k,o as B,g as C,n as S,q as f,p as V,aq as I,bL as Y,bM as w,bq as W,C as q}from"./index-r0dFAfgr.js";import{_ as E}from"./Search-NSrhrIa_.js";import L from"./InspectionConfigDialog-C8oMwrK5.js";import{f as A}from"./DateFormatter-Bm9a68Ax.js";import{g as H,d as N}from"./circuitSettings-CHqJCF5w.js";/* empty css                         */const P={class:"inspection-settings"},R={class:"table-box"},j=M({__name:"InspectionSettings",setup(z){const b=y({labelWidth:80,filters:[{type:"input",label:"配置名称",field:"name"},{type:"input",label:"配置编码",field:"code"},{type:"select",label:"配置类型",field:"type",options:[{label:"管网",value:"0"},{label:"泵站",value:"1"},{label:"其他",value:"2"}]},{type:"select",label:"状态",field:"status",options:[{label:"启用",value:"0"},{label:"停用",value:"1"}]},{type:"daterange",label:"创建时间",field:"createTime",format:"YYYY-MM-DD"},{type:"btn-group",btns:[{perm:!0,text:"查询",type:"primary",click:()=>i()},{perm:!0,text:"重置",type:"default",click:()=>{var e;(e=d.value)==null||e.resetForm(),i()}}]}]}),a=y({title:"巡检配置列表",height:"calc(100vh - 180px)",indexVisible:!0,columns:[{minWidth:120,label:"配置名称",prop:"name"},{minWidth:120,label:"配置编码",prop:"code"},{minWidth:120,label:"配置类型",prop:"type",formatter:e=>({0:"管网",1:"泵站",2:"其他"})[e.type]||e.type},{minWidth:160,label:"创建时间",prop:"createTime",formatter:e=>A(e.createTime,"YYYY-MM-DD HH:mm:ss")},{minWidth:100,label:"状态",prop:"status",formatter:e=>e.status==="0"?"启用":"停用"}],dataList:[],loading:!1,pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,i()}},operations:[{perm:!0,text:"查看",isTextBtn:!0,svgIcon:u(Y),click:e=>v(e)},{perm:!0,text:"编辑",isTextBtn:!0,svgIcon:u(w),click:e=>_(e)},{perm:!0,text:"删除",isTextBtn:!0,svgIcon:u(W),click:e=>h(e)}],titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{perm:!0,text:"新建配置",type:"primary",click:()=>x()}]}]}]}),d=p(),l=p(!1),s=p(""),r=p(!1),i=async()=>{var e;try{a.loading=!0;const t=((e=d.value)==null?void 0:e.queryParams)||{};let c="",g="";t.createTime&&Array.isArray(t.createTime)&&(c=t.createTime[0],g=t.createTime[1]);const D={page:a.pagination.page||1,size:a.pagination.limit||20,name:t.name,code:t.code,type:t.type,status:t.status,fromTime:c,toTime:g},o=await H(D);if(o!=null&&o.data){const n=o.data.data||o.data;a.dataList=n.data||n.records||n.content||[],a.pagination.total=n.total||n.totalElements||0}}catch(t){console.error(t),m.error("获取数据失败")}finally{a.loading=!1}},v=e=>{s.value=e.id,r.value=!0,l.value=!0},_=e=>{s.value=e.id,r.value=!1,l.value=!0},h=e=>{k.confirm("确定要删除该巡检配置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await N(e.id),m.success("删除成功"),i()}catch(t){console.error(t),m.error("删除失败")}}).catch(()=>{})},x=()=>{s.value="",r.value=!1,l.value=!0},T=()=>{i()};return B(()=>{i()}),(e,t)=>(C(),S("div",P,[f(E,{ref_key:"refSearch",ref:d,config:b,style:{"margin-bottom":"8px",padding:"12px","background-color":"#fff","border-radius":"4px"}},null,8,["config"]),V("div",R,[f(I,{config:a},null,8,["config"])]),f(L,{modelValue:l.value,"onUpdate:modelValue":t[0]||(t[0]=c=>l.value=c),"edit-id":s.value,readonly:r.value,onSuccess:T},null,8,["modelValue","edit-id","readonly"])]))}}),Q=q(j,[["__scopeId","data-v-dfca286d"]]);export{Q as default};
