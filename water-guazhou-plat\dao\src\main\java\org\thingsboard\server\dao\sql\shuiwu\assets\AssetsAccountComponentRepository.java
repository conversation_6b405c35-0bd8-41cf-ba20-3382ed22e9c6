package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountComponentEntity;

import java.util.List;

/**
 * 设备台账备件关联
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsAccountComponentRepository extends CrudRepository<AssetsAccountComponentEntity, String> {
    List<AssetsAccountComponentEntity> findAllByPidOrderByCreateTimeDesc(String id);

    @Transactional
    void deleteAllByPid(String id);

}
