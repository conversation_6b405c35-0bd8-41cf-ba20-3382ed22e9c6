package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionFireHydrant;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionValve;

/**
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-26
 */
@Mapper
public interface PartitionFireHydrantMapper extends BaseMapper<PartitionFireHydrant> {

    IPage<PartitionFireHydrant> getList(IPage<PartitionFireHydrant> page, @Param("param") PartitionMountRequest request);

}
