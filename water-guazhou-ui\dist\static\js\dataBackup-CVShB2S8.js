import{m as g,C,M as y,l as c,g as u,n as b,q as i,F as o,bo as B,p as a,G as n,J as O,I as L,K as S,bl as E,bm as N,br as Y}from"./index-r0dFAfgr.js";import{_ as w}from"./index-C9hz-UZb.js";import{_ as V}from"./CardTable-rdWOL4_6.js";function H(e){return g({url:`/api/databackup/list?start=${e.start}&end=${e.end}`,method:"GET"})}function P(){return g({url:"/api/databackup/backup",method:"POST"})}const R="/static/png/backup-Bzcd4ph4.png",{$message:f}=y(),U={name:"Backup",data(){return{value:!0,activeName:"first",tableData:[],currentVarPage:1,size:15,loadingState:!1,tableConfig:{loading:!1,height:"420px",dataList:[],columns:[{label:"备份类型",prop:"type"},{label:"备份人",prop:"creater"},{label:"备份开始时间",prop:"createTime"},{label:"备份结束时间",prop:"endTime"}],pagination:{refreshData:({page:e,size:t})=>{this.tableConfig.pagination.page=e,this.tableConfig.pagination.limit=t,refreshTableData()}}}}},created(){this.getDataBackupList()},methods:{getDataBackupList(){this.tableConfig.dataList=[],this.tableData=[];const e={};e.start=c(new Date).subtract(6,"d").startOf("D").valueOf(),e.end=c().valueOf()+36e6,H(e).then(t=>{t.data.forEach(s=>{s.type==="AUTO"&&(s.type="自动备份"),s.type==="NO_AUTO"&&(s.type="手动备份");const p=new Date(s.createTime);s.createTime=c(p).format("YYYY-MM-DD HH:mm:ss");const l=new Date(s.endTime);s.endTime=c(l).format("YYYY-MM-DD HH:mm:ss"),this.tableData.push(s)}),this.tableConfig.dataList=this.tableData.slice(0,15),this.tableConfig.pagination.total=this.tableData.length})},refreshTableData(e){this.tableConfig.dataList=this.tableData.slice(e*15-15,[e*15])},refreshData(){this.getDataBackupList()},backup(){this.loadingState=!0,P().then(e=>{e.data?(this.loadingState=!1,f.success(e.data.result),this.getDataBackupList()):f("备份失败")})}}},z={class:"wrapper"},M={class:"backup-container overlay-y","element-loading-text":"备份中","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(232, 232, 232, 0.8)"},j={class:"short-message-config config-box"},q={class:"backupHead"},F={class:"backup-form"},G={class:"email-config config-box"},I={class:"email-config-form"};function A(e,t,s,p,l,m){const _=O,r=L,h=S,v=V,d=E,k=N,D=w,x=Y;return u(),b("div",z,[i(D,{style:{height:"100%"}},{default:o(()=>[i(k,{modelValue:l.activeName,"onUpdate:modelValue":t[0]||(t[0]=T=>l.activeName=T),class:"backUpTab",onTabClick:e.handleClick},{default:o(()=>[i(d,{label:"数据备份",name:"first"},{default:o(()=>[B((u(),b("div",M,[a("div",j,[t[4]||(t[4]=a("div",{class:"top-title-box"},[a("p",{class:"top-title",style:{background:"none"}},[a("i",{class:"iconfont icon-shuangjiantouyou"}),n("数据备份 ")])],-1)),a("div",q,[a("div",F,[i(h,{"label-position":"right","label-width":"0px"},{default:o(()=>[i(r,{label:""},{default:o(()=>[i(_,{class:"delete-orange",onClick:m.backup},{default:o(()=>t[1]||(t[1]=[a("i",{class:"iconfont icon-backup-hm"},null,-1),n(" 手动备份 ")])),_:1},8,["onClick"])]),_:1}),i(r,{label:""},{default:o(()=>t[2]||(t[2]=[a("div",{class:"tips-box"},[a("div",null,[a("p",null,"自动备份：每天03:00:00进行数据备份")])],-1)])),_:1})]),_:1})]),t[3]||(t[3]=a("div",{class:"backup-img"},[a("img",{src:R,alt:""})],-1))])]),a("div",G,[t[5]||(t[5]=a("div",{class:"top-title-box unified-theme-bg-color"},[a("p",{class:"top-title"},[a("i",{class:"iconfont icon-shuangjiantouyou"}),n("备份记录 ")])],-1)),a("div",I,[i(v,{style:{height:"100%"},config:l.tableConfig},null,8,["config"])])])])),[[x,l.loadingState]])]),_:1}),i(d,{label:"数据恢复",name:"second"},{default:o(()=>t[6]||(t[6]=[a("div",{class:"data-recovery-container"},[a("div",{class:"short-message-config config-box"},[a("div",{class:"top-title-box unified-theme-bg-color"},[a("p",{class:"top-title"},[a("i",{class:"iconfont icon-shuangjiantouyou"}),n("PostgreSQL数据恢复 ")])]),a("div",{class:"post_head"},[a("span",{class:"span_title"},"操作步骤"),a("p",null,"1.登录数据库所在服务器"),a("p",null," 2.备份文件存放位置：/var/lib/backup/备份日期/数据库名称_备份日期.gz "),a("p",null,"3.进入备份文件目录：cd /var/lib/backup/备份日期/"),a("p",null," 4.解压要还原的数据库备份文件压缩包：gunzip 数据库名称_备份日期.gz "),a("p",null," 5.运行还原命令, 命令格式: psql -U postgres --set ON_ERROR_STOP=on -f 备份文件 "),a("p",null," 示例：psql -U postgres --set ON_ERROR_STOP=on -f /var/lib/backup/2020-04-28/istar_cloud_2_2020-04-28 ")])]),a("div",{class:"email-config config-box"},[a("div",{class:"top-title-box unified-theme-bg-color"},[a("p",{class:"top-title"},[a("i",{class:"iconfont icon-shuangjiantouyou"}),n("influxDB数据恢复 ")])]),a("div",{class:"email-config-form"},[a("span",{class:"span_title"},"操作步骤"),a("p",null,"1.使用SSH登录到备份文件所在服务器"),a("p",null," 2.执行命令：influxd restore -database device -metadir /var/lib/influxdb/meta -datadir /var/lib/influxdb/data /var/lib/backup/influxdb/ "),a("p",null,"3.重启influxdb，命令：systemctl restart influxdb.service")])])],-1)])),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1})])}const Q=C(U,[["render",A],["__scopeId","data-v-688826f2"]]);export{Q as default};
