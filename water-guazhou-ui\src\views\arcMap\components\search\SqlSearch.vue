<template>
  <div>
    <Form ref="refForm" :config="FormConfig"> </Form>
    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="telport"
      @close="() => (state.curOperate = '')"
      @refreshed="() => (state.curOperate = 'viewingDetail')"
      @refreshing="() => (state.curOperate = 'detailing')"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import { excuteQueryForIds, initQueryParams } from '@/utils/MapHelper';
import { useGisStore } from '@/store';
import { SLMessage } from '@/utils/Message';
import PipeDetail from '../common/PipeDetail.vue';

const refDetail = ref<InstanceType<typeof PipeDetail>>();
const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  pipeLayerOption: NormalOption[];
  curNode?: any;
  curOperate: 'detailing' | 'viewingDetail' | 'uniqueing' | '';
  tabs: IPipeDetailTab[];
}>({
  pipeLayerOption: [],
  curOperate: '',
  tabs: []
});
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '图层名称'
      },
      fields: [
        {
          type: 'select',
          clearable: false,
          field: 'layer',
          options: [],
          onChange: () => {
            refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
          }
        }
      ]
    },
    {
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layer) return;
            const fields = await GetFieldConfig(row.layer);
            config.data = fields.data?.result?.rows;
          },
          setDataBy: 'layer',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curNode = node;
            appendSQL(node.name);
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.curOperate === 'uniqueing'
                          ? '正在获取唯一值'
                          : '获取唯一值',
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'detailing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除',
              type: 'danger',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            },
            {
              perm: true,
              text: () =>
                state.curOperate === 'detailing' ? '正在查询' : '查询',
              disabled: () => state.curOperate === 'detailing',
              loading: () => state.curOperate === 'detailing',
              click: () => startSearch(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {}
});

const initBaseLayer = () => {
  if (!props.view) return;
  const pipeLayer: any = props.view?.map.findLayerById('pipelayer');
  state.pipeLayerOption = [];
  pipeLayer?.sublayers?.map((item) => {
    state.pipeLayerOption?.push({
      label: item.title,
      value: item.title,
      id: item.id
    });
  });
  const layerField = FormConfig.group[0].fields[0] as IFormSelect;
  layerField && (layerField.options = state.pipeLayerOption);
  refForm.value?.dataForm &&
    (refForm.value.dataForm.layer =
      state.pipeLayerOption && state.pipeLayerOption[0]?.value);
};
const getUniqueValue = async () => {
  if (!state.curNode) return;
  state.curOperate = 'uniqueing';
  try {
    const layerid = state.pipeLayerOption.find(
      (item) => item.label === refForm.value?.dataForm.layer
    )?.id;
    const res = await GetFieldUniqueValue({
      usertoken: useGisStore().gToken,
      layerid,
      f: 'pjson',
      field_name: state.curNode.name
    });
    const extraFormItem = FormConfig.group.find(
      (item) => item.id === 'field-construct'
    )?.fields[0].extraFormItem;
    const field = extraFormItem && (extraFormItem[0] as IFormList);
    field && (field.data = res.data.result.rows);
  } catch (error) {
    SLMessage.error('获取唯一值失败');
  }
  state.curOperate = '';
};
const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};
const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view);
};
const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = ' ');
};
const startSearch = async () => {
  const layer = state.pipeLayerOption.find(
    (item) => item.label === refForm.value?.dataForm.layer
  );

  if (!layer) {
    SLMessage.warning('请选择图层');
    return;
  }
  const sql = refForm.value?.dataForm?.sql;
  if (!sql || (sql.trim && sql.trim() === '')) {
    SLMessage.warning('组合条件不能为空');
    return;
  }
  try {
    state.curOperate = 'detailing';
    const res = await excuteQueryForIds(
      window.SITE_CONFIG.GIS_CONFIG.gisService +
        window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
        '/' +
        layer.id,
      initQueryParams({
        returnGeometry: false,
        where: sql,
        orderByFields: ['OBJECTID asc']
      })
    );
    console.log(res);

    if (!res?.length) {
      SLMessage.info('查询结果为空');
      state.tabs = [];
    } else {
      state.tabs = [
        {
          label: layer.label,
          name: layer.label,
          id: layer.id,
          data: res
        }
      ];
      nextTick(() => {
        refDetail.value?.openDialog();
      });
    }
  } catch (error) {
    SLMessage.error('查询失败，请检查查询条件是否正确');
    state.curOperate = '';
  }
};
onMounted(() => {
  initBaseLayer();
});
onBeforeUnmount(() => {
  state.curOperate = '';
});
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
</style>
