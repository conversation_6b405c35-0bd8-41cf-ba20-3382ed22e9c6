"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[2156],{3920:(e,t,r)=>{r.d(t,{p:()=>p,r:()=>u});var s=r(43697),o=r(15923),n=r(61247),i=r(5600),a=r(52011),l=r(72762);const p=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,s._)([(0,i.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,s._)([(0,i.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,s._)([(0,a.j)("esri.core.HandleOwner")],t),t};let u=class extends(p(o.Z)){};u=(0,s._)([(0,a.j)("esri.core.HandleOwner")],u)},10699:(e,t,r)=>{r.d(t,{IG:()=>i,iv:()=>a});var s=r(43697),o=r(52011);let n=0;const i=e=>{let t=class extends e{constructor(...e){super(...e),Object.defineProperty(this,"uid",{writable:!1,configurable:!1,value:Date.now().toString(16)+"-object-"+n++})}};return t=(0,s._)([(0,o.j)("esri.core.Identifiable")],t),t},a=e=>{let t=class extends e{constructor(...e){super(...e),Object.defineProperty(this,"uid",{writable:!1,configurable:!1,value:n++})}};return t=(0,s._)([(0,o.j)("esri.core.NumericIdentifiable")],t),t};let l=class extends(i(class{})){};l=(0,s._)([(0,o.j)("esri.core.Identifiable")],l)},66643:(e,t,r)=>{r.d(t,{Ed:()=>p,UI:()=>u,mt:()=>y,q6:()=>h,vr:()=>f});var s=r(43697),o=r(15923),n=r(70586),i=r(95330),a=r(5600),l=(r(75215),r(67676),r(52011));function p(e,t,r){return(0,i.as)(e.map(((e,s)=>t.apply(r,[e,s]))))}async function u(e,t,r){return(await(0,i.as)(e.map(((e,s)=>t.apply(r,[e,s]))))).map((e=>e.value))}function d(e){return{ok:!0,value:e}}function c(e){return{ok:!1,error:e}}async function h(e){if((0,n.Wi)(e))return{ok:!1,error:new Error("no promise provided")};try{return d(await e)}catch(e){return c(e)}}async function y(e){try{return d(await e)}catch(e){return(0,i.r9)(e),c(e)}}function f(e,t){return new m(e,t)}let m=class extends o.Z{get value(){return e=this._result,(0,n.pC)(e)&&!0===e.ok?e.value:null;var e}get error(){return e=this._result,(0,n.pC)(e)&&!1===e.ok?e.error:null;var e}get finished(){return(0,n.pC)(this._result)}constructor(e,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=(0,n.IM)(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:r}=this._abortController;this.promise=e(r),this.promise.then((e=>{this._result=d(e),this._cleanup()}),(e=>{this._result=c(e),this._cleanup()})),this._abortHandle=(0,i.fu)(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=(0,n.hw)(this._abortHandle),this._abortController=null}};(0,s._)([(0,a.Cb)()],m.prototype,"value",null),(0,s._)([(0,a.Cb)()],m.prototype,"error",null),(0,s._)([(0,a.Cb)()],m.prototype,"finished",null),(0,s._)([(0,a.Cb)()],m.prototype,"promise",void 0),(0,s._)([(0,a.Cb)()],m.prototype,"_result",void 0),m=(0,s._)([(0,l.j)("esri.core.asyncUtils.ReactiveTask")],m)},42033:(e,t,r)=>{r.d(t,{E:()=>o,_:()=>n});var s=r(70586);async function o(e,t){const{WhereClause:s}=await r.e(1534).then(r.bind(r,41534));return s.create(e,t)}function n(e,t){return(0,s.pC)(e)?(0,s.pC)(t)?`(${e}) AND (${t})`:e:t}},72762:(e,t,r)=>{r.d(t,{t:()=>d});var s=r(43697),o=r(15923),n=r(61247),i=r(70586),a=r(17445),l=r(1654),p=r(5600),u=r(52011);let d=class extends o.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,a.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,a.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:s=!1}={}){const o=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:s?e=>t({added:[],removed:e.toArray()}):void 0})],o),{remove:()=>this._handles.remove(o)}}addPromise(e){if((0,i.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(c)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},s){const o=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,o);const n=s(e,t,r);return this._handles.add(n,o),{remove:()=>this._handles.remove(o)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),s=(0,a.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(s,t),s}_createSyncUpdatingCallback(){return()=>{this._handles.remove(c),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(c))})),c)}}};(0,s._)([(0,p.Cb)({readOnly:!0})],d.prototype,"updating",void 0),d=(0,s._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],d);const c=-42},94443:(e,t,r)=>{r.d(t,{ME:()=>h,Su:()=>y,tz:()=>c});var s=r(20102),o=r(95330),n=r(70171);const i=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,a={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function l(e){return a[e]??!1}const p=[],u=new Map;function d(e){for(const t of u.keys())f(e.pattern,t)&&u.delete(t)}function c(e){return p.includes(e)||(d(e),p.unshift(e)),{remove(){const t=p.indexOf(e);t>-1&&(p.splice(t,1),d(e))}}}async function h(e){const t=(0,n.Kd)();u.has(e)||u.set(e,async function(e,t){const r=[];for(const s of p)if(f(s.pattern,e))try{return await s.fetchMessageBundle(e,t)}catch(e){r.push(e)}if(r.length)throw new s.Z("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:r});throw new s.Z("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}(e,t));const r=u.get(e);return r&&await m.add(r),r}function y(e){if(!i.test(e))return null;const t=i.exec(e);if(null===t)return null;const[,r,s]=t,o=r+(s?"-"+s.toUpperCase():"");return l(o)?o:l(r)?r:null}function f(e,t){return"string"==typeof e?t.startsWith(e):e.test(t)}(0,n.Ze)((()=>{u.clear()}));const m=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then((()=>this._decrease()),(()=>this._decrease())),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=(0,o.dD)())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&0===this._numLoading&&(this._dfd.resolve(),this._dfd=null)}}},87085:(e,t,r)=>{r.d(t,{Z:()=>b});var s=r(43697),o=(r(66577),r(3172)),n=r(20102),i=r(32448),a=r(10699),l=r(83379),p=r(92604),u=r(95330),d=r(17452),c=r(5600),h=(r(75215),r(67676),r(52011)),y=r(68773),f=r(6570),m=r(82971);let g=0,_=class extends(i.Z.EventedMixin((0,a.IG)(l.Z))){constructor(){super(...arguments),this.attributionDataUrl=null,this.fullExtent=new f.Z(-180,-90,180,90,m.Z.WGS84),this.id=Date.now().toString(16)+"-layer-"+g++,this.legendEnabled=!0,this.listMode="show",this.opacity=1,this.parent=null,this.popupEnabled=!0,this.attributionVisible=!0,this.spatialReference=m.Z.WGS84,this.title=null,this.type=null,this.url=null,this.visible=!0}static async fromArcGISServerUrl(e){const t="string"==typeof e?{url:e}:e;return(await r.e(3529).then(r.bind(r,63529))).fromUrl(t)}static fromPortalItem(e){return async function(e){const t="portalItem"in e?e:{portalItem:e},s=await r.e(8008).then(r.bind(r,28008));try{return await s.fromItem(t)}catch(e){const r=t&&t.portalItem,s=r&&r.id||"unset",o=r&&r.portal&&r.portal.url||y.Z.portalUrl;throw p.Z.getLogger("esri.layers.support.fromPortalItem").error("#fromPortalItem()","Failed to create layer from portal item (portal: '"+o+"', id: '"+s+"')",e),e}}(e)}initialize(){this.when().catch((e=>{(0,u.D_)(e)||p.Z.getLogger(this.declaredClass).error("#load()",`Failed to load layer (title: '${this.title??"no title"}', id: '${this.id??"no id"}')`,{error:e})}))}destroy(){if(this.parent){const e=this,t=this.parent;"layers"in t&&t.layers.includes(e)?t.layers.remove(e):"tables"in t&&t.tables.includes(e)?t.tables.remove(e):"baseLayers"in t&&t.baseLayers.includes(e)?t.baseLayers.remove(e):"baseLayers"in t&&t.referenceLayers.includes(e)&&t.referenceLayers.remove(e)}}get hasAttributionData(){return null!=this.attributionDataUrl}get parsedUrl(){return(0,d.mN)(this.url)}async fetchAttributionData(){const e=this.attributionDataUrl;if(this.hasAttributionData&&e)return(await(0,o.default)(e,{query:{f:"json"},responseType:"json"})).data;throw new n.Z("layer:no-attribution-data","Layer does not have attribution data")}};(0,s._)([(0,c.Cb)({type:String})],_.prototype,"attributionDataUrl",void 0),(0,s._)([(0,c.Cb)({type:f.Z})],_.prototype,"fullExtent",void 0),(0,s._)([(0,c.Cb)({readOnly:!0})],_.prototype,"hasAttributionData",null),(0,s._)([(0,c.Cb)({type:String,clonable:!1})],_.prototype,"id",void 0),(0,s._)([(0,c.Cb)({type:Boolean,nonNullable:!0})],_.prototype,"legendEnabled",void 0),(0,s._)([(0,c.Cb)({type:["show","hide","hide-children"]})],_.prototype,"listMode",void 0),(0,s._)([(0,c.Cb)({type:Number,range:{min:0,max:1},nonNullable:!0})],_.prototype,"opacity",void 0),(0,s._)([(0,c.Cb)({clonable:!1})],_.prototype,"parent",void 0),(0,s._)([(0,c.Cb)({readOnly:!0})],_.prototype,"parsedUrl",null),(0,s._)([(0,c.Cb)({type:Boolean})],_.prototype,"popupEnabled",void 0),(0,s._)([(0,c.Cb)({type:Boolean})],_.prototype,"attributionVisible",void 0),(0,s._)([(0,c.Cb)({type:m.Z})],_.prototype,"spatialReference",void 0),(0,s._)([(0,c.Cb)({type:String})],_.prototype,"title",void 0),(0,s._)([(0,c.Cb)({readOnly:!0,json:{read:!1}})],_.prototype,"type",void 0),(0,s._)([(0,c.Cb)()],_.prototype,"url",void 0),(0,s._)([(0,c.Cb)({type:Boolean,nonNullable:!0})],_.prototype,"visible",void 0),_=(0,s._)([(0,h.j)("esri.layers.Layer")],_);const b=_},54295:(e,t,r)=>{r.d(t,{V:()=>i});var s=r(43697),o=r(5600),n=(r(75215),r(67676),r(52011));const i=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,s._)([(0,o.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,s._)([(0,n.j)("esri.layers.mixins.APIKeyMixin")],t),t}},17287:(e,t,r)=>{r.d(t,{Y:()=>p});var s=r(43697),o=r(92604),n=r(70586),i=r(5600),a=(r(75215),r(67676),r(52011)),l=r(66677);const p=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,l.Qc)(this.url);if((0,n.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,l.Nm)(e,o.Z.getLogger(this.declaredClass)))}};return(0,s._)([(0,i.Cb)()],t.prototype,"title",null),(0,s._)([(0,i.Cb)({type:String})],t.prototype,"url",null),t=(0,s._)([(0,a.j)("esri.layers.mixins.ArcGISService")],t),t}},70082:(e,t,r)=>{r.d(t,{Z:()=>d});var s=r(43697),o=r(2368),n=r(35454),i=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));const p=new n.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let u=class extends((0,o.J)(i.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,s._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"name",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"description",void 0),(0,s._)([(0,a.Cb)({json:{read:p.read,write:p.write}})],u.prototype,"drawingTool",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"prototype",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"thumbnail",void 0),u=(0,s._)([(0,l.j)("esri.layers.support.FeatureTemplate")],u);const d=u},16451:(e,t,r)=>{r.d(t,{Z:()=>h});var s=r(43697),o=r(2368),n=r(96674),i=r(5600),a=(r(75215),r(67676),r(71715)),l=r(52011),p=r(30556),u=r(72729),d=r(70082);let c=class extends((0,o.J)(n.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const r of Object.keys(e))t[r]=(0,u.im)(e[r]);return t}writeDomains(e,t){const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,s._)([(0,i.Cb)({json:{write:!0}})],c.prototype,"id",void 0),(0,s._)([(0,i.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,s._)([(0,i.Cb)({json:{write:!0}})],c.prototype,"domains",void 0),(0,s._)([(0,a.r)("domains")],c.prototype,"readDomains",null),(0,s._)([(0,p.c)("domains")],c.prototype,"writeDomains",null),(0,s._)([(0,i.Cb)({type:[d.Z],json:{write:!0}})],c.prototype,"templates",void 0),c=(0,s._)([(0,l.j)("esri.layers.support.FeatureType")],c);const h=c},56765:(e,t,r)=>{r.d(t,{Z:()=>u});var s,o=r(43697),n=r(46791),i=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));let p=s=class extends i.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new n.Z}clone(){return new s({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,o._)([(0,a.Cb)({type:String,json:{write:!0}})],p.prototype,"floorField",void 0),(0,o._)([(0,a.Cb)({json:{read:!1,write:!1}})],p.prototype,"viewAllMode",void 0),(0,o._)([(0,a.Cb)({json:{read:!1,write:!1}})],p.prototype,"viewAllLevelIds",void 0),p=s=(0,o._)([(0,l.j)("esri.layers.support.LayerFloorInfo")],p);const u=p},84230:(e,t,r)=>{r.d(t,{A2:()=>a,S1:()=>d,fb:()=>i,ln:()=>c,oP:()=>u,rQ:()=>l,y2:()=>p});var s=r(40330),o=r(3172),n=r(70586);const i={Point:"SceneLayer","3DObject":"SceneLayer",IntegratedMesh:"IntegratedMeshLayer",PointCloud:"PointCloudLayer",Building:"BuildingSceneLayer"};function a(e){const t=e?.type;return"building-scene"===t||"integrated-mesh"===t||"point-cloud"===t||"scene"===t}function l(e){return"feature"===e?.type&&!e.url&&"memory"===e.source?.type}function p(e){return"feature"===e?.type&&"feature-layer"===e.source?.type}async function u(e,t){const r=s.id?.findServerInfo(e);if(null!=r?.currentVersion)return r.owningSystemUrl||null;const i=e.toLowerCase().indexOf("/rest/services");if(-1===i)return null;const a=`${e.substring(0,i)}/rest/info`,l=(0,n.pC)(t)?t.signal:null,{data:p}=await(0,o.default)(a,{query:{f:"json"},responseType:"json",signal:l});return p?.owningSystemUrl||null}function d(e){return function(e){if(!("capabilities"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"imagery":case"knowledge-graph-sublayer":case"ogc-feature":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":case"wfs":return!0;default:return!1}}(e)?"effectiveCapabilities"in e?e.effectiveCapabilities:e.capabilities:null}function c(e){return!!function(e){if(!("editingEnabled"in e))return!1;switch(e.type){case"csv":case"feature":case"geojson":case"oriented-imagery":case"scene":case"subtype-group":case"subtype-sublayer":return!0;default:return!1}}(e)&&("effectiveEditingEnabled"in e?e.effectiveEditingEnabled:e.editingEnabled)}},72064:(e,t,r)=>{r.d(t,{h:()=>d});var s=r(80442),o=r(70586),n=r(66677);const i={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function a(e,t,r){return!!(e&&e.hasOwnProperty(t)?e[t]:r)}function l(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function p(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function u(e,t){const r=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!r?.includes(t.toLowerCase())}function d(e,t){return{analytics:c(e),attachment:h(e),data:y(e),metadata:f(e),operations:m(e.capabilities,e,t),query:g(e,t),queryRelated:_(e),queryTopFeatures:b(e),editing:v(e)}}function c(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryAnalytics")}}function h(e){const t=e.attachmentProperties,r={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:u(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:a(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=i[e.name];t&&(r[t]=!!e.isEnabled)})),r}function y(e){return{isVersioned:a(e,"isDataVersioned",!1),supportsAttachment:a(e,"hasAttachments",!1),supportsM:a(e,"hasM",!1),supportsZ:a(e,"hasZ",!1)}}function f(e){return{supportsAdvancedFieldProperties:a(e,"supportsFieldDescriptionProperty",!1)}}function m(e,t,r){const s=e?e.toLowerCase().split(",").map((e=>e.trim())):[],i=r?(0,n.Qc)(r):null,l=s.includes((0,o.pC)(i)&&"MapServer"===i.serverType?"data":"query"),p=s.includes("editing")&&!t.datesInUnknownTimezone;let u=p&&s.includes("create"),d=p&&s.includes("delete"),c=p&&s.includes("update");const h=s.includes("changetracking"),y=t.advancedQueryCapabilities;return p&&!(u||d||c)&&(u=d=c=!0),{supportsCalculate:a(t,"supportsCalculate",!1),supportsTruncate:a(t,"supportsTruncate",!1),supportsValidateSql:a(t,"supportsValidateSql",!1),supportsAdd:u,supportsDelete:d,supportsEditing:p,supportsChangeTracking:h,supportsQuery:l,supportsQueryAnalytics:a(y,"supportsQueryAnalytic",!1),supportsQueryAttachments:a(y,"supportsQueryAttachments",!1),supportsQueryTopFeatures:a(y,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:a(t,"supportsAttachmentsResizing",!1),supportsSync:s.includes("sync"),supportsUpdate:c,supportsExceedsLimitStatistics:a(t,"supportsExceedsLimitStatistics",!1)}}function g(e,t){const r=e.advancedQueryCapabilities,o=e.ownershipBasedAccessControlForFeatures,i=e.archivingInfo,d=e.currentVersion,c=t?.includes("MapServer"),h=!c||d>=(0,s.Z)("mapserver-pbf-version-support"),y=(0,n.M8)(t),f=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:a(r,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:a(r,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:a(r,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:p(r),supportsCentroid:a(r,"supportsReturningGeometryCentroid",!1),supportsDistance:a(r,"supportsQueryWithDistance",!1),supportsDistinct:a(r,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:a(r,"supportsReturningQueryExtent",!1),supportsGeometryProperties:a(r,"supportsReturningGeometryProperties",!1),supportsHavingClause:a(r,"supportsHavingClause",!1),supportsOrderBy:a(r,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:a(r,"supportsPagination",!1),supportsQuantization:a(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:a(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:a(e,"supportsReturningQueryGeometry",!1),supportsResultType:a(r,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:a(r,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:a(r,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:a(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:a(r,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:a(o,"allowOthersToQuery",!0),supportsHistoricMoment:a(i,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:h&&f.has("pbf"),supportsDisjointSpatialRelationship:a(r,"supportsDisjointSpatialRel",!1),supportsCacheHint:a(r,"supportsQueryWithCacheHint",!1)||u(r,"query"),supportsDefaultSpatialReference:a(r,"supportsDefaultSR",!1),supportsCompactGeometry:y,supportsFullTextSearch:a(r,"supportsFullTextSearch",!1),maxRecordCountFactor:l(e,"maxRecordCountFactor",void 0),maxRecordCount:l(e,"maxRecordCount",void 0),standardMaxRecordCount:l(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:l(e,"tileMaxRecordCount",void 0)}}function _(e){const t=e.advancedQueryCapabilities,r=a(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:a(t,"supportsQueryRelatedPagination",!1),supportsCount:r,supportsOrderBy:r,supportsCacheHint:u(t,"queryRelated")}}function b(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryTopFilter")}}function v(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:a(e,"allowGeometryUpdates",!0),supportsGlobalId:a(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:a(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:a(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:a(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:a(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:a(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:a(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:a(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:a(t,"allowOthersToUpdate",!0)}}},14661:(e,t,r)=>{r.d(t,{$o:()=>p,Kz:()=>u,Ss:()=>d,_$:()=>a,ck:()=>l,qj:()=>i});var s=r(44547),o=r(82971),n=r(40488);function i(e,t){if(!a(e,t)){const r=e.typeKeywords;r?r.push(t):e.typeKeywords=[t]}}function a(e,t){return!!e.typeKeywords?.includes(t)}function l(e,t){const r=e.typeKeywords;if(r){const e=r.indexOf(t);e>-1&&r.splice(e,1)}}async function p(e){const t=e.clone().normalize();let r;if(t.length>1)for(const e of t)r?e.width>r.width&&(r=e):r=e;else r=t[0];return async function(e){const t=e.spatialReference;if(t.isWGS84)return e.clone();if(t.isWebMercator)return(0,n.Sx)(e);const r=o.Z.WGS84;return await(0,s.iQ)(t,r),(0,s.iV)(e,r)}(r)}const u={DEVELOPER_BASEMAP:"DeveloperBasemap",JSAPI:"ArcGIS API for JavaScript",METADATA:"Metadata",MULTI_LAYER:"Multilayer",SINGLE_LAYER:"Singlelayer",TABLE:"Table"};function d(e){const{portal:t,isOrgItem:r,itemControl:s}=e,o=t.user?.privileges;let n=!o||o.includes("features:user:edit"),i=!!r&&!!o?.includes("features:user:fullEdit");const a="update"===s||"admin"===s;return a?i=n=!0:i&&(n=!0),{features:{edit:n,fullEdit:i},content:{updateItem:a}}}},51706:(e,t,r)=>{var s,o;function n(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function i(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?s.Expression:e.field&&"string"==typeof e.field?s.Field:s.Unknown}function l(e,t){const r=t||a(e),n=e.valueUnit||"unknown";return r===s.Unknown?o.Constant:e.stops?o.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?o.ClampedLinear:"unknown"===n?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?o.Proportional:o.Additive:o.Identity:o.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>l,RY:()=>s,hL:()=>o,iY:()=>n,qh:()=>i}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(s||(s={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(o||(o={}))},56545:(e,t,r)=>{r.d(t,{Z:()=>c});var s,o=r(43697),n=r(96674),i=r(22974),a=r(5600),l=r(75215),p=r(52011),u=r(30556);let d=s=class extends n.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new s((0,i.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,o._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"attachmentTypes",void 0),(0,o._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],d.prototype,"attachmentsWhere",void 0),(0,o._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],d.prototype,"cacheHint",void 0),(0,o._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"keywords",void 0),(0,o._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"globalIds",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,o._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],d.prototype,"num",void 0),(0,o._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"objectIds",void 0),(0,o._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],d.prototype,"returnMetadata",void 0),(0,o._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"size",void 0),(0,o._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],d.prototype,"start",void 0),(0,o._)([(0,u.c)("start"),(0,u.c)("num")],d.prototype,"writeStart",null),(0,o._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],d.prototype,"where",void 0),d=s=(0,o._)([(0,p.j)("esri.rest.support.AttachmentQuery")],d),d.from=(0,l.se)(d);const c=d},74889:(e,t,r)=>{r.d(t,{Z:()=>v});var s,o=r(43697),n=r(66577),i=r(38171),a=r(35454),l=r(96674),p=r(22974),u=r(70586),d=r(5600),c=(r(75215),r(71715)),h=r(52011),y=r(30556),f=r(82971),m=r(33955),g=r(1231);const _=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let b=s=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=f.Z.fromJSON(t.spatialReference),s=[];for(let t=0;t<e.length;t++){const o=e[t],n=i.Z.fromJSON(o),a=o.geometry&&o.geometry.spatialReference;(0,u.pC)(n.geometry)&&!a&&(n.geometry.spatialReference=r);const l=o.aggregateGeometries,p=n.aggregateGeometries;if(l&&(0,u.pC)(p))for(const e in p){const t=p[e],s=l[e]?.spatialReference;(0,u.pC)(t)&&!s&&(t.spatialReference=r)}s.push(n)}return s}writeGeometryType(e,t,r,s){if(e)return void _.write(e,t,r,s);const{features:o}=this;if(o)for(const e of o)if(e&&(0,u.pC)(e.geometry))return void _.write(e.geometry.type,t,r,s)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,s=(0,m.im)(e);return s&&!r&&t.spatialReference&&(s.spatialReference=f.Z.fromJSON(t.spatialReference)),s}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,u.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new s(this.cloneProperties())}cloneProperties(){return(0,p.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const s=t.features[r];if(s.geometry){const t=e&&e[r];s.geometry=t&&t.toJSON()||s.geometry}}return t}quantize(e){const{scale:[t,r],translate:[s,o]}=e,n=this.features,i=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-s)/t)),(e=>Math.round((o-e)/r)));for(let e=0,t=n.length;e<t;e++)i?.((0,u.Wg)(n[e].geometry))||(n.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[s,o],scale:[n,i]}=r,a=this._getHydrationFunction(e,(e=>e*n+s),(e=>o-e*i));for(const{geometry:e}of t)(0,u.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,r){let s,o;const n=[];for(let i=0,a=e.length;i<a;i++){const a=e[i];if(i>0){const e=t(a[0]),i=r(a[1]);e===s&&i===o||(n.push([e-s,i-o]),s=e,o=i)}else s=t(a[0]),o=r(a[1]),n.push([s,o])}return n.length>0?n:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const s=(0,m.oU)(e)?e.rings:e.paths,o=[];for(let e=0,n=s.length;e<n;e++){const n=s[e],i=this._quantizePoints(n,t,r);i&&o.push(i)}return o.length>0?((0,m.oU)(e)?e.rings=o:e.paths=o,e):null}:"multipoint"===e?e=>{const s=this._quantizePoints(e.points,t,r);return s&&s.length>0?(e.points=s,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const s=(0,m.oU)(e)?e.rings:e.paths;let o,n;for(let e=0,i=s.length;e<i;e++){const i=s[e];for(let e=0,s=i.length;e<s;e++){const s=i[e];e>0?(o+=s[0],n+=s[1]):(o=s[0],n=s[1]),s[0]=t(o),s[1]=r(n)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const s=e.points;let o,n;for(let e=0,i=s.length;e<i;e++){const i=s[e];e>0?(o+=i[0],n+=i[1]):(o=i[0],n=i[1]),i[0]=t(o),i[1]=r(n)}}:null}};(0,o._)([(0,d.Cb)({type:String,json:{write:!0}})],b.prototype,"displayFieldName",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"exceededTransferLimit",void 0),(0,o._)([(0,d.Cb)({type:[i.Z],json:{write:!0}})],b.prototype,"features",void 0),(0,o._)([(0,c.r)("features")],b.prototype,"readFeatures",null),(0,o._)([(0,d.Cb)({type:[g.Z],json:{write:!0}})],b.prototype,"fields",void 0),(0,o._)([(0,d.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:_.read}}})],b.prototype,"geometryType",void 0),(0,o._)([(0,y.c)("geometryType")],b.prototype,"writeGeometryType",null),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasM",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasZ",void 0),(0,o._)([(0,d.Cb)({types:n.qM,json:{write:!0}})],b.prototype,"queryGeometry",void 0),(0,o._)([(0,c.r)("queryGeometry")],b.prototype,"readQueryGeometry",null),(0,o._)([(0,d.Cb)({type:f.Z,json:{write:!0}})],b.prototype,"spatialReference",void 0),(0,o._)([(0,y.c)("spatialReference")],b.prototype,"writeSpatialReference",null),(0,o._)([(0,d.Cb)({json:{write:!0}})],b.prototype,"transform",void 0),b=s=(0,o._)([(0,h.j)("esri.rest.support.FeatureSet")],b),b.prototype.toJSON.isDefaultToJSON=!0;const v=b}}]);