<!-- gis水源 -->
<template>
  <div class="onemap-panel-wrapper">
    <Form ref="refForm" :config="FormConfig"></Form>
    <FormTable class="table-box" :config="TableConfig"></FormTable>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point';
import { getStationImageUrl } from '@/utils/URLHelper';
import { useStations } from '@/hooks/station/useStation';
import { PopImage } from '../../components';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();
const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
    },
    layout: 'total,sizes, jumper'
  },
  handleRowClick: (row) => handleLocate(row),
  columns: [
    {
      minWidth: 120,
      label: '名称',
      prop: 'name',
      sortable: true
    },
    {
      minWidth: 120,
      label: '类型',
      prop: 'type',
      sortable: true
    }
  ]
});
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'name',
          itemContainerStyle: { marginBottom: '10px' },
          appendBtns: [
            {
              perm: true,
              text: '刷新',
              click: () => refreshData()
            }
          ],
          onChange: () => refreshData()
        }
      ]
    }
  ],
  defaultValue: {}
});
const refForm = ref<IFormIns>();
const stations = useStations();
const refreshData = async () => {
  TableConfig.loading = true;
  try {
    const res = await stations.getStations();
    const name = refForm.value?.dataForm?.name;
    TableConfig.dataList = res.filter((item) => {
      return name ? item.name?.includes(name) : true;
    });
    const windows: IArcPopConfig[] = [];
    res.map((item) => {
      const location = item.location?.split(',');
      if (location?.length === 2) {
        const point = new Point({
          longitude: location[0],
          latitude: location[1],
          spatialReference: props.view?.spatialReference
        });
        windows.push({
          visible: false,
          id: item.id,
          x: point.x,
          y: point.y,
          offsetY: -40,
          title: item.name,
          customComponent: shallowRef(PopImage),
          customConfig: {
            info: {
              type: 'attrs',
              imageUrl: item.imgs,
              stationId: item.id
            }
          },
          attributes: {
            path: props.menu.path,
            id: item.id,
            row: {
              ...item,
              stationId: item.id,
              fromAllStation: true
            }
          },
          symbolConfig: {
            url: getStationImageUrl(
              item.name?.indexOf('热') !== -1
                ? '测流压站.png'
                : `${item.type}.png`
            )
          }
        });
      }
    });
    emit('addMarks', {
      windows
    });
  } catch (error) {
    console.dir(error);
  }
  TableConfig.loading = false;
};
const handleLocate = async (row?: any) => {
  emit('highlightMark', props.menu, row?.id);
};
onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  width: 100%;
  height: calc(100% - 65px);
}
</style>
