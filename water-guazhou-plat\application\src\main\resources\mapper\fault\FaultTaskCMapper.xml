<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.fault.FaultTaskCMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.fault.FaultTaskC">

        select a.*, b.model as model, b.serial_id as serialId, b.name, b.type_id as typeId, area.name as installAddressName, settle.address detailInstallAddressName
        from tb_device_fault_task_c a
                 left join device_storage_journal journal on a.device_label_code = journal.device_label_code and journal.tenant_id = a.tenant_id
                 left join m_device b on journal.serial_id = b.serial_id and b.tenant_id = a.tenant_id
                 left join device_settle_journal settle on a.device_label_code = settle.device_label_code and settle.tenant_id = a.tenant_id
                 left join tb_area area on settle.install_address_id = area.id and area.tenant_id = a.tenant_id
        where a.main_id = #{mainId}
        order by a.create_time desc

    </select>

    <select id="selectFirstByDeviceLabelCode" resultType="org.thingsboard.server.dao.model.sql.fault.FaultTaskC">
        select * from tb_device_fault_task_c where device_label_code = #{deviceLabelCode} and create_time &lt; #{thisTime} order by create_time desc offset 0 limit 1
    </select>

</mapper>