package org.thingsboard.server.dao.report;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.DTO.WaterReportDTO;
import org.thingsboard.server.dao.model.sql.WuniInput;
import org.thingsboard.server.dao.model.sql.report.WaterReport;
import org.thingsboard.server.dao.sql.report.WaterReportRepository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WaterReportServiceImpl implements WaterReportService {

    @Autowired
    private WaterReportRepository waterReportRepository;

    @Override
    @Transactional
    public void save(WaterReport waterIn, WaterReport waterOut) {
        waterReportRepository.save(waterIn);
        waterReportRepository.save(waterOut);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            waterReportRepository.delete(id);
        }
    }

}
