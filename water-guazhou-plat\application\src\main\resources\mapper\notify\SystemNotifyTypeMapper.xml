<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.notify.SystemNotifyTypeMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.notify.SystemNotifyType">
        select *
        from tb_system_notify_type a
        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.type != null">
                and a.type = #{param.type}
            </if>
            <if test="param.menuName != null">
                and a.menu_name = #{param.menuName}
            </if>
            <if test="param.sendType != null">
                and a.send_type = #{param.sendType}
            </if>
            <if test="param.status != null">
                and a.status = #{param.status}
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>