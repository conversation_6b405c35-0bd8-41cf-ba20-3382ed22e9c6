package org.thingsboard.server.dao.tenant;

import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;

import java.util.List;

public interface TenantApplicationService {
    PageData<TenantApplicationEntity> findList(String tenantId, int page, int size);

    List<TenantApplicationEntity> findAll(String tenantId);

    void saveOrUpdate(TenantApplicationEntity entity);

    void delete(List<String> ids);

    List<String> selectedMenuList(String tenantApplicationId);

    TenantApplicationEntity findById(String tenantApplicationId);

    List<TenantApplicationEntity> findListByUser(UserId id);

    List<TenantApplicationEntity> findListByUser(String resourceType, UserId id);

    List<TenantApplicationEntity> findAll(String resourceType, String tenantId);
}
