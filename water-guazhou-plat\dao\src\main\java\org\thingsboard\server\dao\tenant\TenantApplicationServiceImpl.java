package org.thingsboard.server.dao.tenant;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ApplicationMenuEntity;
import org.thingsboard.server.dao.model.sql.RoleApplication;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;
import org.thingsboard.server.dao.role.RoleService;
import org.thingsboard.server.dao.sql.role.RoleApplicationRepository;
import org.thingsboard.server.dao.sql.tenant.ApplicationMenuRelationRepository;
import org.thingsboard.server.dao.sql.tenant.TenantApplicationRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.TENANT_MENUS_CACHE;

@Slf4j
@Service
public class TenantApplicationServiceImpl implements TenantApplicationService {

    @Autowired
    private TenantApplicationRepository tenantApplicationRepository;
    @Autowired
    private ApplicationMenuRelationRepository applicationMenuRelationRepository;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RoleApplicationRepository roleApplicationRepository;

    @Override
    public PageData<TenantApplicationEntity> findList(String tenantId, int page, int size) {
        PageRequest pageRequest = new PageRequest(page, size);
        Page<TenantApplicationEntity> pageResult = tenantApplicationRepository.findList(tenantId, pageRequest);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public List<TenantApplicationEntity> findAll(String tenantId) {
        return tenantApplicationRepository.findByTenantId(tenantId);
    }

    @Override
    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = TENANT_MENUS_CACHE, key = "{#entity.id}")})
    public void saveOrUpdate(TenantApplicationEntity entity) {
        TenantApplicationEntity save = tenantApplicationRepository.save(entity);

        // 保存菜单列表, 先删除后保存
        applicationMenuRelationRepository.deleteByTenantApplicationId(save.getId());
        List<String> menuIdList = entity.getMenuIdList();

        if (menuIdList != null && menuIdList.size() > 0) {
            List<ApplicationMenuEntity> addList = new ArrayList<>();
            for (String menuId : menuIdList) {
                ApplicationMenuEntity applicationMenuEntity = new ApplicationMenuEntity();
                applicationMenuEntity.setMenuId(menuId);
                applicationMenuEntity.setTenantApplicationId(save.getId());

                addList.add(applicationMenuEntity);
            }
            applicationMenuRelationRepository.save(addList);
        }

    }

    @Override
    public void delete(List<String> ids) {
        for (String id : ids) {
            tenantApplicationRepository.delete(id);
            applicationMenuRelationRepository.deleteByTenantApplicationId(id);
        }
    }

    @Override
    public List<String> selectedMenuList(String tenantApplicationId) {
        List<ApplicationMenuEntity> list = applicationMenuRelationRepository.findByTenantApplicationId(tenantApplicationId);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        return list.stream().map(ApplicationMenuEntity::getMenuId).collect(Collectors.toList());
    }

    @Override
    public TenantApplicationEntity findById(String tenantApplicationId) {
        return tenantApplicationRepository.findOne(tenantApplicationId);
    }

    @Override
    public List<TenantApplicationEntity> findListByUser(UserId userId) {
        // 查询用户所属角色
        String roleId = roleService.getRoleIdByUserId(userId);
        if (roleId == null) {
            return new ArrayList<>();
        }

        // 查询角色所拥有的企业应用ID列表
        List<RoleApplication> roleApplicationList = roleApplicationRepository.findByRoleId(roleId);
        if (roleApplicationList == null || roleApplicationList.isEmpty()) {
            return new ArrayList<>();
        }
        return tenantApplicationRepository.findByIdIn(roleApplicationList.stream().map(RoleApplication::getTenantApplicationId).collect(Collectors.toList()));
    }

    @Override
    public List<TenantApplicationEntity> findListByUser(String resourceType, UserId userId) {
        // 查询用户所属角色
        String roleId = roleService.getRoleIdByUserId(userId);
        if (roleId == null) {
            return new ArrayList<>();
        }

        // 查询角色所拥有的企业应用ID列表
        List<RoleApplication> roleApplicationList = roleApplicationRepository.findByRoleId(roleId);
        if (roleApplicationList == null || roleApplicationList.isEmpty()) {
            return new ArrayList<>();
        }
        return tenantApplicationRepository.findByIdInAndResourceType(roleApplicationList.stream().map(RoleApplication::getTenantApplicationId).collect(Collectors.toList()), resourceType);
    }

    @Override
    public List<TenantApplicationEntity> findAll(String resourceType, String tenantId) {
        return tenantApplicationRepository.findByTenantIdAndResourceType(tenantId, resourceType);
    }
}
