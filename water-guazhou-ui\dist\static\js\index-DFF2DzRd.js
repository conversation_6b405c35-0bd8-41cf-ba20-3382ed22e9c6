import{_ as F}from"./index-C9hz-UZb.js";import{d as L,M as O,a6 as E,c as h,r as _,bF as f,a8 as u,s as x,bu as j,ah as z,ay as R,g as P,n as M,q as i,i as r,F as m,cs as w,h as A,an as T,j as G,bB as J,c5 as U,dF as W,dA as $,aq as X,al as H,aj as K,C as Q}from"./index-r0dFAfgr.js";import{_ as Z}from"./CardSearch-CB_HNR-Q.js";import{l as ee}from"./echart-Bd1EZNhy.js";import{u as ae}from"./useStation-DJgnSZIA.js";import{p as te}from"./flowMonitoring-DtJlPj0G.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const re={class:"wrapper"},oe=L({__name:"index",setup(se){const{$messageWarning:y}=O(),q=E(),v=h(),C=h(),p=h(),g=h(),{getStationTree:S,getStationTreeByDisabledType:I}=ae(),e=_({activeName:"day",activePattern:"echarts",chartName:"",stationTree:[],stationId:"",chartOption:null,data:[]}),V=_({type:"tabs",width:"100%",field:"type",tabs:[{label:"日压力",value:"day"},{label:"月压力",value:"month"},{label:"年压力",value:"year"}],handleTabClick:t=>{var o;e.activeName=t.props.name,(((o=p.value)==null?void 0:o.queryParams)||{}).stationId?Y():y("请选择监测点")}}),b=_({defaultParams:{day:f().format("YYYY-MM-DD"),month:f().format("YYYY-MM"),year:f().format("YYYY"),daterange:[f().format("YYYY-MM-DD"),f().format("YYYY-MM-DD")]},filters:[{type:"select-tree",label:"监测点:",defaultExpandAll:!0,field:"stationId",clearable:!1,width:"200px",options:u(()=>e.stationTree),nodeClick:t=>{e.chartName=t.label}},{type:"date",label:"日期",field:"day",format:"YYYY-MM-DD",clearable:!1,hidden:u(()=>e.activeName==="year"||e.activeName==="month"||e.activeName==="daterange"),width:300},{type:"month",label:"日期",field:"month",format:"YYYY-MM",clearable:!1,hidden:u(()=>e.activeName==="year"||e.activeName==="day"||e.activeName==="daterange"),width:300},{type:"year",label:"日期",field:"year",format:"YYYY",clearable:!1,hidden:u(()=>e.activeName==="day"||e.activeName==="month"||e.activeName==="daterange"),width:300},{type:"daterange",label:"日期",format:"YYYY-MM",field:"daterange",clearable:!1,hidden:u(()=>e.activeName==="year"||e.activeName==="month"||e.activeName==="day"),width:300}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:x(H),click:()=>{var a;(((a=p.value)==null?void 0:a.queryParams)||{}).stationId?Y():y("选择监测点")}},{perm:!0,text:"导出",type:"warning",svgIcon:x(K),hide:()=>e.activePattern!=="list",click:()=>{var a,o;(((a=p.value)==null?void 0:a.queryParams)||{}).stationId?(o=C.value)==null||o.exportTable():y("请选择监测点")}}]}]}),D=_({loading:!1,dataList:[],columns:u(()=>[{prop:"ts",label:"时间",align:"center",formatter:t=>e.activeName==="day"?t.ts+"时":e.activeName==="month"?t.ts+"日":t.ts+"月"},{prop:"value",label:"压力",align:"center"}]),operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),B=()=>{var n,c,d;const t=(n=e.data)==null?void 0:n.tableDataList.map(s=>s.ts),a=ee(t);a.series=[];const o={name:e.chartName,smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}},l=JSON.parse(JSON.stringify(o));l.data=(c=e.data)==null?void 0:c.tableDataList.map(s=>s.value),a.series.push(l),(d=v.value)==null||d.clear(),J(()=>{g.value&&q.listenTo(g.value,()=>{var s;e.chartOption=a,(s=v.value)==null||s.resize()})})},Y=async()=>{var c,d,s;const t=((c=p.value)==null?void 0:c.queryParams)||{},a=t[e.activeName],o={stationId:t.stationId,queryType:e.activeName,date:a},n=(s=(d=(await te(o)).data)==null?void 0:d.data)==null?void 0:s.pressure;n?(console.log(n),D.dataList=n,e.data.tableDataList=n,B()):y("无数据")};return j(async()=>{var l;const t=["压力监测站,测流压站"].join(","),a=await S(t);e.stationTree=a,await I(a,["Project"],!1,"Station");const o=z(a);b.defaultParams={...b.defaultParams,stationId:o.id},(l=p.value)==null||l.resetForm(),await Y()}),(t,a)=>{const o=Z,l=U,n=W,c=$,d=X,s=R("VChart"),k=F;return P(),M("div",re,[i(o,{ref_key:"refSearch",ref:p,config:r(b)},null,8,["config"]),i(k,{class:"card-table",title:" "},{title:m(()=>[i(l,{modelValue:r(e).activeName,"onUpdate:modelValue":a[0]||(a[0]=N=>r(e).activeName=N),config:r(V)},null,8,["modelValue","config"])]),default:m(()=>[i(k,{class:"card",title:r(e).activePattern==="list"?"压力数据列表":"压力曲线"},{right:m(()=>[i(c,{modelValue:r(e).activePattern,"onUpdate:modelValue":a[1]||(a[1]=N=>r(e).activePattern=N)},{default:m(()=>[i(n,{label:"echarts"},{default:m(()=>[i(r(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),i(n,{label:"list"},{default:m(()=>[i(r(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:m(()=>[r(e).activePattern==="list"?(P(),A(d,{key:0,ref_key:"refCard",ref:C,config:r(D)},null,8,["config"])):T("",!0),r(e).activePattern==="echarts"?(P(),M("div",{key:1,ref_key:"agriEcoDev",ref:g,class:"card-ehcarts"},[i(s,{ref_key:"refChart",ref:v,theme:r(G)().isDark?"dark":"light",class:"card-ehcarts",option:r(e).chartOption},null,8,["theme","option"])],512)):T("",!0)]),_:1},8,["title"])]),_:1})])}}}),fe=Q(oe,[["__scopeId","data-v-84d9b72d"]]);export{fe as default};
