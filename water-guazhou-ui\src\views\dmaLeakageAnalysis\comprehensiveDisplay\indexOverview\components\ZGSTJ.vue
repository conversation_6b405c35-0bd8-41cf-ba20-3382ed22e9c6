<template>
  <SLCard title="总供水统计">
    <template #title>
      <div class="card-header">
        <div class="left">
          <Icon icon="material-symbols:water-drop-outline" /><span
            >总供水统计</span
          >
        </div>
        <inline-form
          ref="refTotalWaterForm"
          style="width: auto"
          :config="dateConfig"
        ></inline-form>
      </div>
    </template>
    <div ref="refDiv" class="chart-box">
      <VChart
        ref="refChart"
        :option="state.chartOption"
        :theme="useAppStore().isDark ? 'blackBackground' : 'whiteBackground'"
      ></VChart>
    </div>
  </SLCard>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { GetPartitionWaterSupplyStatistic } from '@/api/mapservice/dma';
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';

const refTotalWaterForm = ref<IInlineFormIns>();
const state = reactive<{
  chartOption: any;
}>({
  chartOption: null
});
const dateConfig = reactive<IFormConfig>({
  defaultValue: {
    dateType: 'day'
  },
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'dateType',
          options: [
            { label: '日', value: 'day' },
            { label: '月', value: 'month' },
            { label: '季度', value: 'quarter' },
            { label: '年', value: 'year' }
          ],
          onChange: () => refreshData()
        }
      ]
    }
  ]
});
const refreshData = () => {
  GetPartitionWaterSupplyStatistic({
    type: refTotalWaterForm.value?.dataForm.dateType
  })
    .then((res) => {
      const data = res.data.data || {};
      state.chartOption = {
        grid: {
          left: 10,
          right: 20,
          top: 30,
          bottom: 10,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis'
          // formatter: ' {a} <br/> {c} m³'
        },
        xAxis: {
          type: 'category',
          data: data.x || [],
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          name: '供水量(m³)',
          type: 'value',
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        series: [
          {
            name: '供水量',
            data: data.y || [],
            type: 'line',
            smooth: true
          }
        ]
      };
    })
    .catch(() => {
      //
    });
};
const detector = useDetector();
const refChart = ref();
const refDiv = ref();
onMounted(() => {
  refreshData();
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize();
  });
});
</script>
<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: 100%;
}
.card-header {
  display: flex;
  align-items: center;
  word-break: keep-all;
  justify-content: space-between;
  width: 100%;
  .left {
    display: flex;
    align-items: center;
  }
  :deep(.el-form-item--default) {
    margin-bottom: 0;
  }
}
</style>
