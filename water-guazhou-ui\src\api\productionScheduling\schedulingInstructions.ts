import request from '@/plugins/axios';

interface postOrderRecordType {
  type: string;
  sendDeptId?: string;
  receiveDeptId: string;
  sendContent: string;
  executionTime: string;
  remark: string;
}

// 调度指令
/** 获取调度指令列表 */
export function getOrderRecordList(params: {
  commandStatus?: string;
  receiveDeptId?: string;
  sendUserId?: string;
  receiveUserId?: string;
  type?: string;
  sendContent?: string;
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/orderRecord`,
    method: 'GET',
    params
  });
}

/** 批量新增调度指令 */
export function postOrderRecord(params: postOrderRecordType[]) {
  return request({
    url: `/api/sp/orderRecord`,
    method: 'POST',
    data: params
  });
}

/** 批量发送指令 */
export function postSendCommand(data: string[]) {
  return request({
    url: `/api/sp/orderRecord/send`,
    method: 'POST',
    data
  });
}

/** 删除指令(仅待发送) */
export function deleteOrderRecord(data: string[]) {
  return request({
    url: `/api/sp/orderRecord`,
    method: 'delete',
    data
  });
}

/** 指令接收 */
export function postCommandReception(params: {
  receiveUserId: string;
  idList: string[];
}) {
  return request({
    url: `/api/sp/orderRecord/receive`,
    method: 'POST',
    data: params
  });
}

/** 指令拒绝 */
export function postCommandRejected(params: {
  rejectRemark: string;
  idList: string[];
}) {
  return request({
    url: `/api/sp/orderRecord/reject`,
    method: 'POST',
    data: params
  });
}

/** 指令回复 */
export function postcommandReply(params: {
  isComplete: boolean;
  replyContent: string;
  idList: string[];
}) {
  return request({
    url: `/api/sp/orderRecord/reply`,
    method: 'POST',
    data: params
  });
}

// 调度类型
/** 获取调度类型列表 */
export function getorderRecordType(params: {
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/orderRecordType`,
    method: 'GET',
    params
  });
}

/** 删除指令类型 */
export function deleteorderRecordType(id: string) {
  return request({
    url: `/api/sp/orderRecordType/${id}`,
    method: 'delete'
  });
}

/** 新增指令类型 */
export function postorderRecordType(params: {
  id?: string;
  name: string;
  deptIdList?: string;
}) {
  return request({
    url: `/api/sp/orderRecordType`,
    method: 'POST',
    data: params
  });
}
