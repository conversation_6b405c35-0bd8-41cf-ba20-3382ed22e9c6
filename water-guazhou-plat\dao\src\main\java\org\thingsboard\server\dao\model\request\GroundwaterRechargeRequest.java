package org.thingsboard.server.dao.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 地下水涵养水位查询参数
 */
@Data
@ApiModel(value = "地下水涵养水位查询参数", description = "地下水涵养水位查询参数")
public class GroundwaterRechargeRequest {

    @ApiModelProperty(value = "页码", example = "1")
    private int page = 1;

    @ApiModelProperty(value = "每页条数", example = "10")
    private int size = 10;
    
    @ApiModelProperty(value = "区域ID")
    private String areaId;
    
    @ApiModelProperty(value = "分析开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "分析结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "分析状态(1-良好,2-一般,3-不足)")
    private Integer status;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 