"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8104],{98722:(t,e,n)=>{n.d(e,{Ue:()=>gn,G8:()=>dn});var s=n(3892),r=n(20102),o=n(22974),i=n(43697),a=n(70586),u=n(5600),l=(n(75215),n(67676),n(52011)),c=(n(92604),n(80442),n(96674));let p=class extends c.wq{constructor(){super(...arguments),this.raster=void 0}};(0,i._)([(0,u.Cb)({json:{write:!0}})],p.prototype,"raster",void 0),p=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.AspectFunctionArguments")],p);const f=p;var h;let m=h=class extends f{clone(){return new h({raster:this.raster})}};m=h=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.AspectFunctionArguments")],m);const d=m;var g=n(90578),y=n(5847),x=n(48526);let b=class extends c.wq{constructor(){super(...arguments),this.functionArguments=null,this.readingBufferSize=0,this.id=-1,this.isNoopProcess=!1,this.rawInputBandIds=[],this.isInputBandIdsSwizzled=!1,this.swizzledBandSelection=[],this.isBranch=!1,this._bindingResult=null}get supportsGPU(){return this._bindingResult.supportsGPU}bind(t,e=!1,n=-1){this.id=n+1;const s=this._getRasterValues();let r=!0;for(let n=0;n<s.length;n++){const o=s[n];if((0,a.pC)(o)&&this._isRasterFunctionValue(o)){const s=o.bind(t,e,this.id+n);if(!s.success)return this._bindingResult=s,s;r=r&&s.supportsGPU}}return!this.rasterInfo||e?(this.sourceRasterInfos=this._getSourceRasterInfos(t),this._bindingResult=this._bindSourceRasters(),this._bindingResult.supportsGPU=r&&this._bindingResult.supportsGPU,this.processInputBandIds(),this._bindingResult):(this._bindingResult={success:!0,supportsGPU:!0},this.processInputBandIds(),this._bindingResult)}process(t){const e=this._getRasterValues(),n=0===e.length?t.pixelBlocks??t.primaryPixelBlocks:e.map((e=>this._readRasterValue(e,t)));return this._processPixels({...t,pixelBlocks:n})}processInputBandIds(){const t=this._getRasterValues().filter(this._isRasterFunctionValue);let e;if(t.length>1){const e=t.map((t=>t.processInputBandIds()[0]));this.rawInputBandIds=e,this.isInputBandIdsSwizzled=this.rawInputBandIds.some(((t,e)=>t!==e));const n=t.filter((t=>"ExtractBand"===t.functionName));return n.length&&n.forEach(((t,e)=>{t.isInputBandIdsSwizzled=!0,t.swizzledBandSelection=[e,e,e]})),this.rawInputBandIds}const n=t[0];if(n){if(e=n.processInputBandIds(),n.isInputBandIdsSwizzled)return this.rawInputBandIds=e,e}else{e=[];const{bandCount:t}=this.sourceRasterInfos[0];for(let n=0;n<t;n++)e.push(n)}const s=this._getInputBandIds(e);return this.isInputBandIdsSwizzled=s.some(((t,e)=>t!==e)),this.rawInputBandIds=s,this.rawInputBandIds}getPrimaryRasters(){const t=[],e=[];return this._getPrimaryRasters(this,t,e),{rasters:t,rasterIds:e}}getWebGLProcessorDefinition(){const t=this._getWebGLParameters(),{raster:e,rasters:n}=this.functionArguments;return n&&Array.isArray(n)&&n.length?(t.rasters=n.map((t=>this._isRasterFunctionValue(t)?t.getWebGLProcessorDefinition():"number"==typeof t?{name:"Constant",parameters:{value:t},pixelType:"f32",id:-1,isNoopProcess:!1}:{name:"Identity",parameters:{value:t},pixelType:"f32",id:-1,isNoopProcess:!1})),t.rasters.some((t=>null!=t))||(t.rasters=null)):this._isRasterFunctionValue(e)&&(t.raster=e.getWebGLProcessorDefinition()),{name:this.functionName,parameters:t,pixelType:this.outputPixelType,id:this.id,isNoopProcess:this.isNoopProcess}}getFlatWebGLFunctionChain(){const t=this.getWebGLProcessorDefinition();if(!t)return null;const e=[t],{parameters:n}=t;let s=n.rasters||n.raster&&[n.raster];for(;s?.length;){e.unshift(...s);const t=[];for(let e=0;e<s.length;e++){const{parameters:n}=s[e],r=n.rasters||n.raster&&[n.raster];r?.length&&t.push(...r)}s=t}for(let t=e.length-1;t>=0;t--)e[t].isNoopProcess&&e.splice(t,1);let r=!1;for(let t=0;t<e.length;t++){const n=e[t];n.id=e.length-t-1;const{rasters:s}=n.parameters;r=r||null!=s&&s.length>1}return{hasBranches:r,functions:e}}_getOutputPixelType(t){return"unknown"===this.outputPixelType?t:this.outputPixelType??t}_getWebGLParameters(){return{}}_getInputBandIds(t){return t}_isOutputRoundingNeeded(){const{outputPixelType:t}=this;return(t?.startsWith("u")||t?.startsWith("s"))??!1}_getRasterValues(){const{rasterArgumentNames:t}=this;return"rasters"===t[0]?this.functionArguments.rasters??[]:t.map((t=>this.functionArguments[t]))}_getSourceRasterInfos(t){const e=this._getRasterValues(),{rasterInfos:n,rasterIds:s}=t;if(0===e.length)return n;const r=e.map((t=>t&&"object"==typeof t&&"bind"in t&&t.rasterInfo?t.rasterInfo:"string"==typeof t&&s.includes(t)?n[s.indexOf(t)]:"number"!=typeof t?n[0]:void 0)),o=r.find((t=>t))??n[0];return r.forEach(((t,e)=>{void 0===t&&(r[e]=o)})),r}_getPrimaryRasterId(t){return t?.url}_getPrimaryRasters(t,e=[],n=[]){for(let s=0;s<t.sourceRasters.length;s++){const r=t.sourceRasters[s];if("number"!=typeof r)if("bind"in r)this._getPrimaryRasters(r,e,n);else{const t=r,s=this._getPrimaryRasterId(t);if(null==s)continue;n.includes(s)||(this.mainPrimaryRasterId===s?(e.unshift(t),n.unshift(s)):(e.push(t),n.push(s)))}}}_isRasterFunctionValue(t){return null!=t&&"object"==typeof t&&"getWebGLProcessorDefinition"in t}_readRasterValue(t,e){const{primaryPixelBlocks:n}=e;if((0,a.Wi)(t)||"$$"===t){const t=n[0];return(0,a.Wi)(t)?null:t.clone()}if("string"==typeof t){const s=e.primaryRasterIds.indexOf(t);return-1===s?null:n[s]}if("number"==typeof t){const e=n[0];if((0,a.Wi)(e))return null;const{width:s,height:r,pixelType:o,mask:i}=e,u=i?new Uint8Array(i):null,l=new Float32Array(s*r);l.fill(t);const c=this.sourceRasterInfos[0].bandCount,p=new Array(c).fill(l);return new y.Z({width:s,height:r,pixelType:o,pixels:p,mask:u})}return t.process(e)}};(0,i._)([(0,u.Cb)({json:{write:!0}})],b.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],b.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}}),(0,g.p)((t=>t?.toLowerCase()))],b.prototype,"outputPixelType",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],b.prototype,"mainPrimaryRasterId",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"sourceRasters",void 0),(0,i._)([(0,u.Cb)({type:[x.Z],json:{write:!0}})],b.prototype,"sourceRasterInfos",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],b.prototype,"rasterInfo",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],b.prototype,"readingBufferSize",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],b.prototype,"id",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"isNoopProcess",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"supportsGPU",null),(0,i._)([(0,u.Cb)()],b.prototype,"rawInputBandIds",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"isInputBandIdsSwizzled",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"swizzledBandSelection",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"isBranch",void 0),(0,i._)([(0,u.Cb)()],b.prototype,"_bindingResult",void 0),b=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.BaseRasterFunction")],b);const w=b;var v=n(63342);let C=class extends w{constructor(){super(...arguments),this.functionName="Aspect",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isGCS=!1}_bindSourceRasters(){const t=this.sourceRasterInfos[0];this.isGCS=t.spatialReference?.isGeographic??!1,this.outputPixelType=this._getOutputPixelType("f32");const e=t.clone();return e.pixelType=this.outputPixelType,e.statistics=[{min:0,max:360,avg:180,stddev:30}],e.histograms=null,e.colormap=null,e.attributeTable=null,e.bandCount=1,this.rasterInfo=e,{success:!0,supportsGPU:!0}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return null;const{extent:n}=t,s=n?{x:n.width/e.width,y:n.height/e.height}:{x:1,y:1};return(0,v.M_)(e,{resolution:s})}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],C.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:d,json:{write:!0,name:"rasterFunctionArguments"}})],C.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],C.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],C.prototype,"isGCS",void 0),C=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.AspectFunction")],C);const _=C;var A=n(36030),R=n(35454);const P=new Set(["+","-","*","/","(",")"]);function N(t,e,n,s){if("number"==typeof n&&"number"==typeof s)return n+s;let r;if("number"==typeof n){r=s.length;const t=n;(n=new Float32Array(r)).fill(t)}else if(r=n.length,s.constructor===Number){const t=s;(s=new Float32Array(r)).fill(t)}const o=new Float32Array(r);switch(e){case"+":for(let e=0;e<r;e++)(null==t||t[e])&&(o[e]=n[e]+s[e]);break;case"-":for(let e=0;e<r;e++)(null==t||t[e])&&(o[e]=n[e]-s[e]);break;case"*":for(let e=0;e<r;e++)(null==t||t[e])&&(o[e]=n[e]*s[e]);break;case"/":for(let e=0;e<r;e++)(null==t||t[e])&&s[e]&&(o[e]=n[e]/s[e]);break;case"(":case")":throw new Error("encountered error with custom band index equation")}return o}function I(t,e){t.splice(e,1);let n=0,s=0;do{n=0,s=0;for(let e=0;e<t.length;e++)if("("===t[e])n=e;else if(")"===t[e]){s=e;break}s===n+1&&t.splice(n,2)}while(s===n+1);return t}function F(t){if(1===t.length)return{opIndex:0,numIndex:0};let e=0,n=0;for(let s=0;s<t.length;s++)if("("===t[s])e=s;else if(")"===t[s]){n=s;break}const s=0===n?t:t.slice(e+1,n);let r=-1;for(let t=0;t<s.length;t++)if("*"===s[t]||"/"===s[t]){r=t;break}if(r>-1)n>0&&(r+=e+1);else{for(let t=0;t<s.length;t++)if("+"===s[t]||"-"===s[t]){r=t;break}n>0&&(r+=e+1)}let o=0;for(let e=0;e<r;e++)"("===t[e]&&o++;return{opIndex:r,numIndex:r-o}}function T(t,e,n){let s,{ops:r,nums:o}=function(t,e){(t=t.replace(/ /g,"")).startsWith("-")&&(t="0"+t),t.startsWith("+")&&(t=t.slice(1,t.length));const n=t.split(""),s=[],r=[];let o="";for(let t=0;t<n.length;t++){const i=n[t];if(P.has(i))""!==o&&r.push(parseFloat(o)),s.push(i),o="";else{if("b"===i.toLowerCase()){t++,o=i.concat(n[t]),r.push(e[parseInt(o[1],10)-1]),o="";continue}o=o.concat(i),t===n.length-1&&r.push(parseFloat(o))}}return{ops:s,nums:r}}(n,e);if(0===r.length){const t=1===o.length?o[0]:e[0];if(t instanceof Float32Array)return[t];const n=new Float32Array(e[0].length);return"number"==typeof t?n.fill(t):n.set(t),[n]}for(;r.length>0;){const{numIndex:e,opIndex:n}=F(r);if(s=N(t,r[n],o[e],o[e+1]),1===r.length)break;r=I(r,n),o.splice(e,2,s)}return[s]}var k=n(55914);const S=new R.X({0:"custom",1:"ndvi",2:"savi",3:"tsavi",4:"msavi",5:"gemi",6:"pvi",7:"gvitm",8:"sultan",9:"vari",10:"gndvi",11:"sr",12:"ndvi-re",13:"sr-re",14:"mtvi2",15:"rtvi-core",16:"ci-re",17:"ci-g",18:"ndwi",19:"evi",20:"iron-oxide",21:"ferrous-minerals",22:"clay-minerals",23:"wndwi",24:"bai",25:"nbr",26:"ndbi",27:"ndmi",28:"ndsi",29:"mndwi"},{useNumericKeys:!0});function M(t){const e=new Float32Array(9);return e[3*t[0]]=1,e[3*t[1]+1]=1,e[3*t[2]+2]=1,e}function B(t,e,n){const s=n.length,r=new Float32Array(s);for(let o=0;o<s;o++)if(null==t||t[o]){const t=e[o],s=n[o],i=t+s;i&&(r[o]=(t-s)/i)}return[r]}var j;let E=j=class extends f{constructor(){super(...arguments),this.method="custom"}clone(){return new j({method:this.method,bandIndexes:this.bandIndexes,raster:(0,o.d9)(this.raster)})}};(0,i._)([(0,u.Cb)({json:{type:String,write:!0}})],E.prototype,"bandIndexes",void 0),(0,i._)([(0,A.J)(S)],E.prototype,"method",void 0),E=j=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.BandArithmeticFunctionArguments")],E);const G=E,D=new Set(["vari","mtvi2","rtvi-core","evi"]);let O=class extends w{constructor(){super(...arguments),this.functionName="BandArithmetic",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){this.outputPixelType=this._getOutputPixelType("f32");const t=this.sourceRasterInfos[0],e=t.clone();return e.pixelType=this.outputPixelType,e.statistics=null,e.histograms=null,e.bandCount="sultan"===this.functionArguments.method?t.bandCount:1,this.rasterInfo=e,{success:!0,supportsGPU:!["custom","gvitm","sultan"].includes(this.functionArguments.method)}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return e;const{method:n,bandIndexes:s}=this.functionArguments,r=s.split(" ").map((t=>parseFloat(t)));return function(t,e){if(!(0,k.nk)(t))return t;const{equation:n,method:s}=e,r=e.bandIndexes.map((t=>t-1)),{pixels:o,mask:i}=t;let u;switch(s){case"gndvi":case"nbr":case"ndbi":case"ndvi":case"ndvi-re":case"ndsi":case"ndmi":case"mndwi":u=B(i,o[r[0]],o[r[1]]);break;case"ndwi":u=B(i,o[r[1]],o[r[0]]);break;case"sr":case"sr-re":case"iron-oxide":case"ferrous-minerals":case"clay-minerals":u=function(t,e,n){const s=n.length,r=new Float32Array(s);for(let o=0;o<s;o++)if(null==t||t[o]){const t=e[o],s=n[o];s&&(r[o]=t/s)}return[r]}(i,o[r[0]],o[r[1]]);break;case"ci-g":case"ci-re":u=function(t,e,n){const s=e.length,r=new Float32Array(s);for(let o=0;o<s;o++)if(null==t||t[o]){const t=e[o],s=n[o];s&&(r[o]=t/s-1)}return[r]}(i,o[r[0]],o[r[1]]);break;case"savi":u=function(t,e,n,s){const r=n.length,o=new Float32Array(r);for(let i=0;i<r;i++)if(null==t||t[i]){const t=n[i],r=e[i],a=r+t+s;a&&(o[i]=(r-t)/a*(1+s))}return[o]}(i,o[r[0]],o[r[1]],r[2]+1);break;case"tsavi":u=function(t,e,n,s,r,o){const i=n.length,a=new Float32Array(i),u=-r*s+o*(1+s*s);for(let o=0;o<i;o++)if(null==t||t[o]){const t=n[o],i=e[o],l=r*i+t+u;l&&(a[o]=s*(i-s*t-r)/l)}return[a]}(i,o[r[0]],o[r[1]],r[2]+1,r[3]+1,r[4]+1);break;case"msavi":u=function(t,e,n){const s=n.length,r=new Float32Array(s);for(let o=0;o<s;o++)if(null==t||t[o]){const t=n[o],s=e[o];r[o]=.5*(2*(s+1)-Math.sqrt((2*s+1)**2-8*(s-t)))}return[r]}(i,o[r[0]],o[r[1]]);break;case"gemi":u=function(t,e,n){const s=n.length,r=new Float32Array(s);for(let o=0;o<s;o++)if(null==t||t[o]){const t=n[o],s=e[o];if(1!==t){const e=(2*(s*s-t*t)+1.5*s+.5*t)/(s+t+.5);r[o]=e*(1-.25*e)-(t-.125)/(1-t)}}return[r]}(i,o[r[0]],o[r[1]]);break;case"pvi":u=function(t,e,n,s,r){const o=n.length,i=new Float32Array(o),a=Math.sqrt(1+s*s);for(let u=0;u<o;u++)if(null==t||t[u]){const t=n[u],o=e[u];i[u]=(o-s*t-r)/a}return[i]}(i,o[r[0]],o[r[1]],r[2]+1,r[3]+1);break;case"gvitm":u=function(t,e){const[n,s,r,o,i,a]=e,u=n.length,l=new Float32Array(u);for(let e=0;e<u;e++)(null==t||t[e])&&(l[e]=-.2848*n[e]-.2435*s[e]-.5436*r[e]+.7243*o[e]+.084*i[e]-1.18*a[e]);return[l]}(i,[o[r[0]],o[r[1]],o[r[2]],o[r[3]],o[r[4]],o[r[5]]]);break;case"sultan":u=function(t,e){const[n,,s,r,o,i]=e,a=n.length,u=new Float32Array(a),l=new Float32Array(a),c=new Float32Array(a);for(let e=0;e<a;e++)(null==t||t[e])&&(u[e]=i[e]?o[e]/i[e]*100:0,l[e]=n[e]?o[e]/n[e]*100:0,c[e]=r[e]?s[e]/r[e]*(o[e]/r[e])*100:0);return[u,l,c]}(i,[o[r[0]],o[r[1]],o[r[2]],o[r[3]],o[r[4]],o[r[5]]]);break;case"vari":u=function(t,e){const[n,s,r]=e,o=n.length,i=new Float32Array(o);for(let e=0;e<o;e++)if(null==t||t[e])for(e=0;e<o;e++){const t=n[e],o=s[e],a=o+t-r[e];a&&(i[e]=(o-t)/a)}return[i]}(i,[o[r[0]],o[r[1]],o[r[2]]]);break;case"mtvi2":u=function(t,e){const[n,s,r]=e,o=n.length,i=new Float32Array(o);for(let e=0;e<o;e++)if(null==t||t[e])for(e=0;e<o;e++){const t=n[e],o=s[e],a=r[e],u=Math.sqrt((2*t+1)**2-6*t-5*Math.sqrt(o)-.5);i[e]=1.5*(1.2*(t-a)-2.5*(o-a))*u}return[i]}(i,[o[r[0]],o[r[1]],o[r[2]]]);break;case"rtvi-core":u=function(t,e){const[n,s,r]=e,o=n.length,i=new Float32Array(o);for(let e=0;e<o;e++)if(null==t||t[e])for(e=0;e<o;e++){const t=n[e],o=s[e],a=r[e];i[e]=100*(t-o)-10*(t-a)}return[i]}(i,[o[r[0]],o[r[1]],o[r[2]]]);break;case"evi":u=function(t,e){const[n,s,r]=e,o=n.length,i=new Float32Array(o);for(let e=0;e<o;e++)if(null==t||t[e])for(e=0;e<o;e++){const t=n[e],o=s[e],a=t+6*o-7.5*r[e]+1;a&&(i[e]=2.5*(t-o)/a)}return[i]}(i,[o[r[0]],o[r[1]],o[r[2]]]);break;case"wndwi":u=function(t,e,n=.5){const[s,r,o]=e,i=r.length,a=new Float32Array(i);for(let e=0;e<i;e++)if(null==t||t[e])for(e=0;e<i;e++){const t=s[e],i=r[e],u=o[e],l=t+n*i+(1-n)*u;l&&(a[e]=(t-n*i-(1-n)*u)/l)}return[a]}(i,[o[r[0]],o[r[1]],o[r[2]]],r[3]?r[3]+1:.5);break;case"bai":u=function(t,e,n){const s=n.length,r=new Float32Array(s);for(let o=0;o<s;o++)if(null==t||t[o])for(o=0;o<s;o++){const t=(.1-e[o])**2+(.06-n[o])**2;t&&(r[o]=1/t)}return[r]}(i,o[r[0]],o[r[1]]);break;case"custom":u=T(i,o,n);break;default:return t}const l=(0,a.pC)(i)?new Uint8Array(i.length):null;(0,a.pC)(i)&&(0,a.pC)(l)&&l.set(i);const c=new y.Z({width:t.width,height:t.height,pixelType:"f32",pixels:u,mask:l});return c.updateStatistics(),c}(e,{method:n,bandIndexes:r,equation:s})}_getWebGLParameters(){const t=this.functionArguments.bandIndexes.split(" ").map((t=>parseFloat(t)-1));2===t.length&&t.push(0);const e=this.isInputBandIdsSwizzled?[0,1,2]:t;let n,s;const r=new Float32Array(3),{method:o}=this.functionArguments;switch(o){case"gndvi":case"nbr":case"ndbi":case"ndvi":case"ndvi-re":case"ndsi":case"ndmi":case"mndwi":n=M([e[0],e[1],0]),s="ndxi";break;case"ndwi":n=M([e[1],e[0],0]),s="ndxi";break;case"sr":case"sr-re":case"iron-oxide":case"ferrous-minerals":case"clay-minerals":n=M([e[0],e[1],0]),s="sr";break;case"ci-g":case"ci-re":n=M([e[0],e[1],0]),s="ci";break;case"savi":n=M([e[0],e[1],0]),s="savi",r[0]=t[2]+1;break;case"tsavi":n=M([e[0],e[1],0]),s="tsavi",r[0]=t[2]+1,r[1]=t[3]+1,r[2]=t[4]+1;break;case"msavi":n=M([e[0],e[1],0]),s="msavi";break;case"gemi":n=M([e[0],e[1],0]),s="gemi";break;case"pvi":n=M([e[0],e[1],0]),s="tsavi",r[0]=t[2]+1,r[1]=t[3]+1;break;case"vari":n=M([e[0],e[1],e[2]]),s="vari";break;case"mtvi2":n=M([e[0],e[1],e[2]]),s="mtvi2";break;case"rtvi-core":n=M([e[0],e[1],e[2]]),s="rtvicore";break;case"evi":n=M([e[0],e[1],e[2]]),s="evi";break;case"wndwi":n=M([e[0],e[1],0]),s="wndwi",r[0]=t[3]?t[3]+1:.5;break;case"bai":n=M([e[1],e[0],0]),s="bai";break;default:n=M([0,1,2]),s="custom"}return{bandIndexMat3:n,indexType:s,adjustments:r}}_getInputBandIds(t){if("custom"===this.functionArguments.method)return t;const e=this.functionArguments.bandIndexes.split(" ").map((t=>parseFloat(t)-1)),n=t.length,s=e.map((t=>t>=n?n-1:t)),r=D.has(this.functionArguments.method)?3:2,o=s.slice(0,r).map((e=>t[e]));return 2===o.length&&o.push(0),o}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],O.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:G,json:{write:!0,name:"rasterFunctionArguments"}})],O.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],O.prototype,"rasterArgumentNames",void 0),O=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.BandArithmeticFunction")],O);const z=O;var Z,V=n(71715),W=n(93010),L=n(94593);let U=Z=class extends f{castColormapName(t){if(!t)return null;const e=t.toLowerCase();return W.Oi.includes(e)?e:null}readColorRamp(t){return(0,L.i)(t)}readColorRampName(t,e){if(!t)return null;const n=W.H0.jsonValues.find((e=>e.toLowerCase()===t.toLowerCase()));return n?W.H0.fromJSON(n):null}clone(){return new Z({colormap:(0,o.d9)(this.colormap),colormapName:this.colormapName,colorRamp:this.colorRamp?.clone(),colorRampName:this.colorRampName})}};(0,i._)([(0,u.Cb)({type:[[Number]],json:{write:!0}})],U.prototype,"colormap",void 0),(0,i._)([(0,u.Cb)({type:String,json:{write:!0}})],U.prototype,"colormapName",void 0),(0,i._)([(0,g.p)("colormapName")],U.prototype,"castColormapName",null),(0,i._)([(0,u.Cb)({types:L.V,json:{write:!0}})],U.prototype,"colorRamp",void 0),(0,i._)([(0,V.r)("colorRamp")],U.prototype,"readColorRamp",null),(0,i._)([(0,u.Cb)({type:W.H0.apiValues,json:{type:W.H0.jsonValues,write:W.H0.write}})],U.prototype,"colorRampName",void 0),(0,i._)([(0,V.r)("colorRampName")],U.prototype,"readColorRampName",null),U=Z=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.ColormapFunctionArguments")],U);const q=U,X=[[36,0,255],[36,0,255],[36,0,255],[36,0,255],[112,75,3],[113,76,3],[114,77,3],[115,77,3],[116,78,3],[117,79,3],[118,79,3],[119,80,3],[121,81,4],[122,82,4],[123,82,4],[124,83,4],[125,84,4],[126,84,4],[127,85,4],[128,86,4],[129,86,4],[130,87,4],[131,88,4],[132,89,4],[133,89,4],[134,90,4],[135,91,4],[136,91,4],[137,92,4],[138,93,4],[139,94,4],[140,94,4],[142,95,5],[143,96,5],[144,96,5],[145,97,5],[146,98,5],[147,99,5],[148,99,5],[149,100,5],[150,101,5],[151,101,5],[152,102,5],[153,103,5],[154,104,5],[155,104,5],[156,105,5],[157,106,5],[158,106,5],[159,107,5],[160,108,5],[161,108,5],[162,109,5],[164,110,6],[165,111,6],[166,111,6],[167,112,6],[168,113,6],[169,113,6],[170,114,6],[171,115,6],[172,116,6],[173,116,6],[174,117,6],[245,0,0],[245,5,0],[245,10,0],[246,15,0],[246,20,0],[246,25,0],[246,30,0],[247,35,0],[247,40,0],[247,45,0],[247,50,0],[247,55,0],[248,60,0],[248,65,0],[248,70,0],[248,75,0],[249,81,0],[249,86,0],[249,91,0],[249,96,0],[250,101,0],[250,106,0],[250,111,0],[250,116,0],[250,121,0],[251,126,0],[251,131,0],[251,136,0],[251,141,0],[252,146,0],[252,151,0],[252,156,0],[252,156,0],[251,159,0],[250,162,0],[249,165,0],[248,168,0],[247,171,0],[246,174,0],[245,177,0],[245,179,0],[244,182,0],[243,185,0],[242,188,0],[241,191,0],[240,194,0],[239,197,0],[238,200,0],[237,203,0],[236,206,0],[235,209,0],[234,212,0],[233,215,0],[232,218,0],[231,221,0],[230,224,0],[230,226,0],[229,229,0],[228,232,0],[227,235,0],[226,238,0],[225,241,0],[224,244,0],[223,247,0],[165,247,0],[163,244,0],[161,240,0],[158,237,0],[156,233,1],[154,230,1],[152,227,1],[149,223,1],[147,220,1],[145,216,1],[143,213,1],[140,210,2],[138,206,2],[136,203,2],[134,200,2],[132,196,2],[129,193,2],[127,189,2],[125,186,3],[123,183,3],[120,179,3],[118,176,3],[116,172,3],[114,169,3],[111,166,3],[109,162,4],[107,159,4],[105,155,4],[103,152,4],[100,149,4],[98,145,4],[96,142,4],[94,138,5],[91,135,5],[89,132,5],[87,128,5],[85,125,5],[82,121,5],[80,118,5],[78,115,6],[76,111,6],[73,108,6],[71,105,6],[69,101,6],[67,98,6],[65,94,6],[62,91,7],[60,88,7],[58,84,7],[56,81,7],[53,77,7],[51,74,7],[49,71,7],[47,67,8],[44,64,8],[42,60,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8]],H=[[36,0,255],[36,0,255],[36,0,255],[36,0,255],[245,20,0],[245,24,0],[245,29,0],[245,31,0],[247,33,0],[247,33,0],[247,37,0],[247,41,0],[247,41,0],[247,41,0],[247,45,0],[247,45,0],[247,47,0],[247,49,0],[247,49,0],[247,54,0],[247,54,0],[247,56,0],[247,58,0],[247,58,0],[250,62,0],[250,62,0],[250,62,0],[250,67,0],[250,67,0],[250,67,0],[250,69,0],[250,71,0],[250,71,0],[250,75,0],[250,75,0],[250,78,0],[250,79,0],[250,79,0],[250,79,0],[250,81,0],[250,83,0],[250,83,0],[250,87,0],[250,87,0],[250,90,0],[250,92,0],[252,93,0],[252,93,0],[252,97,0],[252,97,0],[252,97,0],[252,97,0],[252,101,0],[252,101,0],[252,101,0],[252,101,0],[252,105,0],[252,105,0],[252,107,0],[252,109,0],[252,109,0],[252,113,13],[255,118,20],[255,119,23],[255,121,25],[255,126,33],[255,132,38],[255,133,40],[255,135,43],[255,141,48],[255,144,54],[255,150,59],[255,152,61],[255,153,64],[255,159,69],[255,163,77],[255,165,79],[255,168,82],[255,174,87],[255,176,92],[255,181,97],[255,183,99],[255,186,102],[255,191,107],[255,197,115],[255,201,120],[255,203,123],[255,205,125],[255,209,130],[255,214,138],[255,216,141],[255,218,143],[255,224,150],[255,228,156],[255,234,163],[255,236,165],[255,238,168],[255,243,173],[255,248,181],[255,252,186],[253,252,186],[250,252,187],[244,250,180],[238,247,176],[234,246,173],[231,245,169],[223,240,163],[217,237,157],[211,235,150],[205,233,146],[200,230,142],[195,227,136],[189,224,132],[184,222,126],[180,220,123],[174,217,119],[169,214,114],[163,212,108],[160,210,105],[154,207,101],[148,204,96],[143,201,93],[138,199,88],[134,197,84],[130,194,81],[126,191,77],[117,189,70],[115,186,68],[112,184,64],[106,181,60],[100,179,55],[94,176,49],[92,174,47],[90,173,45],[81,168,37],[75,166,33],[71,163,28],[66,160,24],[62,158,21],[56,156,14],[51,153,0],[51,153,0],[51,153,0],[50,150,0],[50,150,0],[50,150,0],[50,150,0],[49,148,0],[49,148,0],[49,148,0],[48,145,0],[48,145,0],[48,145,0],[48,145,0],[48,143,0],[48,143,0],[48,143,0],[48,143,0],[47,140,0],[47,140,0],[47,140,0],[47,140,0],[46,138,0],[46,138,0],[46,138,0],[46,138,0],[45,135,0],[45,135,0],[45,135,0],[45,135,0],[44,133,0],[44,133,0],[44,133,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[42,128,0],[42,128,0],[42,128,0],[42,125,0],[42,125,0],[42,125,0],[42,125,0],[41,122,0],[41,122,0],[41,122,0],[41,122,0],[40,120,0],[40,120,0],[40,120,0],[40,120,0],[40,120,0],[39,117,0],[39,117,0],[39,117,0],[39,117,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0]];function J(t,e){const n=[],s=[];for(let e=0;e<t.length-1;e++)n.push({type:"algorithmic",algorithm:"esriHSVAlgorithm",fromColor:t[e].slice(1),toColor:t[e+1].slice(1)}),s.push(t[e+1][0]-t[e][0]);const r=t[t.length-1][0];return(0,W.XL)({type:"multipart",colorRamps:n},{numColors:r,weights:e=e??s})}let Y=class extends w{constructor(){super(...arguments),this.functionName="Colormap",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isNoopProcess=!0}_bindSourceRasters(){const t=this.sourceRasterInfos[0];if(t.bandCount>1)return{success:!1,supportsGPU:!1,error:"colormap-function: source data must be single band"};let{colormap:e,colormapName:n,colorRamp:s,colorRampName:r}=this.functionArguments;if(!e?.length)if(s)e=(0,W.Jw)(s,{interpolateAlpha:!0});else if(r){const t=(0,W.B4)(r);t&&(e=(0,W.Jw)(t))}else n&&(e=function(t){let e;switch(t){case"elevation":e=J([[0,0,191,191],[51,0,255,0],[102,255,255,0],[153,255,127,0],[204,191,127,63],[256,20,20,20]]);break;case"gray":e=(0,W.XL)({type:"algorithmic",algorithm:"esriHSVAlgorithm",fromColor:[0,0,0],toColor:[255,255,255]});break;case"hillshade":e=J([[0,38,54,41],[69,79,90,82],[131,156,156,156],[256,253,241,253]],[.268,.238,.495]);break;case"ndvi":e=X;break;case"ndvi2":e=function(){const t=J([[0,255,255,255],[70,0,0,255],[80,205,193,173],[100,150,150,150],[110,120,100,51],[130,120,200,100],[140,28,144,3],[160,6,55,0],[180,10,30,25],[201,6,27,7]]);for(let e=t.length;e<256;e++)t.push([6,27,7]);return t}();break;case"ndvi3":e=H;break;case"random":e=function(){const t=[];for(let e=0;e<256;e++){const e=[];for(let t=0;t<3;t++)e.push(Math.round(255*Math.random()));t.push(e)}return t}()}return e?(e=e.map(((t,e)=>[e,...t])),e):null}(n));if(!e?.length)return{success:!1,supportsGPU:!1,error:"colormap-function: missing colormap argument"};this.outputPixelType=this._getOutputPixelType("u8");const o=t.clone();return o.pixelType=this.outputPixelType,o.colormap=e,o.bandCount=1,this.rasterInfo=o,{success:!0,supportsGPU:!0}}_processPixels(t){return t.pixelBlocks?.[0]}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],Y.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:q,json:{write:!0,name:"rasterFunctionArguments"}})],Y.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],Y.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)()],Y.prototype,"isNoopProcess",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Y.prototype,"indexedColormap",void 0),Y=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.ColormapFunction")],Y);const $=Y;var K,Q=n(30556);let tt=K=class extends f{constructor(){super(...arguments),this.rasters=[]}writeRasters(t,e){e.rasters=t.map((t=>"number"==typeof t||"string"==typeof t?t:t.toJSON()))}clone(){return new K({rasters:(0,o.d9)(this.rasters)})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],tt.prototype,"rasters",void 0),(0,i._)([(0,Q.c)("rasters")],tt.prototype,"writeRasters",null),tt=K=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.CompositeBandFunctionArguments")],tt);const et=tt;let nt=class extends w{constructor(){super(...arguments),this.functionName="CompositeBand",this.functionArguments=null,this.rasterArgumentNames=["rasters"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0];this.outputPixelType=this._getOutputPixelType(e.pixelType);const n=e.clone();if(n.attributeTable=null,n.colormap=null,n.pixelType=this.outputPixelType,n.bandCount=t.map((({bandCount:t})=>t)).reduce(((t,e)=>t+e)),t.every((({statistics:t})=>(0,a.pC)(t)&&t.length))){const e=[];t.forEach((({statistics:t})=>(0,a.pC)(t)&&e.push(...t))),n.statistics=e}if(t.every((({histograms:t})=>(0,a.pC)(t)&&t.length))){const e=[];t.forEach((({histograms:t})=>(0,a.pC)(t)&&e.push(...t))),n.histograms=e}return n.bandCount>1&&(n.colormap=null,n.attributeTable=null),this.rasterInfo=n,{success:!0,supportsGPU:n.bandCount<=3}}_processPixels(t){const{pixelBlocks:e}=t;if(!e)return null;const n=e?.[0];return(0,a.Wi)(n)?null:(0,k.Gd)(e)}_getWebGLParameters(){return{bandCount:this.rasterInfo.bandCount}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],nt.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:et,json:{write:!0,name:"rasterFunctionArguments"}})],nt.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],nt.prototype,"rasterArgumentNames",void 0),nt=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.CompositeBandFunction")],nt);const st=nt,rt={userDefined:-1,lineDetectionHorizontal:0,lineDetectionVertical:1,lineDetectionLeftDiagonal:2,lineDetectionRightDiagonal:3,gradientNorth:4,gradientWest:5,gradientEast:6,gradientSouth:7,gradientNorthEast:8,gradientNorthWest:9,smoothArithmeticMean:10,smoothing3x3:11,smoothing5x5:12,sharpening3x3:13,sharpening5x5:14,laplacian3x3:15,laplacian5x5:16,sobelHorizontal:17,sobelVertical:18,sharpen:19,sharpen2:20,pointSpread:21,none:255},ot={bitwiseAnd:11,bitwiseLeftShift:12,bitwiseNot:13,bitwiseOr:14,bitwiseRightShift:15,bitwiseXOr:16,booleanAnd:17,booleanNot:18,booleanOr:19,booleanXOr:20,equalTo:24,greaterThan:28,greaterThanEqual:29,lessThan:33,lessThanEqual:34,isNull:31,notEqual:46},it={acos:6,asin:7,atan:8,atanh:9,cos:21,cosh:22,sin:51,sinh:52,tan:56,tanh:57,acosh:59,asinh:60,atan2:61},at={setNull:50,conditional:78},ut={plus:1,minus:2,times:3,sqrt:4,power:5,abs:10,divide:23,exp:25,exp10:26,exp2:27,int:30,float:32,ln:35,log10:36,log2:37,mod:44,negate:45,roundDown:48,roundUp:49,square:53,floatDivide:64,floorDivide:65,...ot,...it,majority:38,max:39,mean:40,med:41,min:42,minority:43,range:47,stddev:54,sum:55,variety:58,majorityIgnoreNoData:66,maxIgnoreNoData:67,meanIgnoreNoData:68,medIgnoreNoData:69,minIgnoreNoData:70,minorityIgnoreNoData:71,rangeIgnoreNoData:72,stddevIgnoreNoData:73,sumIgnoreNoData:74,varietyIgnoreNoData:75,...at};var lt=n(81578);const ct=new Map;function pt(t,e,n,s,r,o,i){const a=new Float32Array(e*n),u=o.length,l=i?0:s,c=i?s:0,p=i?1:e;for(let i=l;i<n-l;i++){const n=i*e;for(let i=c;i<e-c;i++){if(r&&!r[n+i])continue;let e=0;for(let r=0;r<u;r++)e+=t[n+i+(r-s)*p]*o[r];a[n+i]=e}}return a}function ft(t,e,n,s,r,o,i){const a=new Float32Array(e*n),u=Math.floor(s/2),l=Math.floor(r/2);for(let c=u;c<n-u;c++){const n=c*e;for(let c=l;c<e-l;c++){if(o&&!o[n+c])continue;let p=0;for(let o=0;o<s;o++)for(let s=0;s<r;s++)p+=t[n+c+(o-u)*e+s-l]*i[o*r+s];a[n+c]=p}}return a}function ht(t,e,n,s,r){const o=Math.floor(s/2);for(let s=0;s<o;s++)for(let o=0;o<e;o++)t[s*e+o]=t[(r-1-s)*e+o],t[(n-1-s)*e+o]=t[(n-r+s)*e+o];const i=Math.floor(r/2);for(let s=0;s<n;s++){const n=s*e;for(let s=0;s<i;s++)t[n+s]=t[n+r-1-s],t[n+e-s-1]=t[n+e+s-r]}}var mt;ct.set(rt.none,[0,0,0,0,1,0,0,0,0]),ct.set(rt.lineDetectionHorizontal,[-1,-1,-1,2,2,2,-1,-1,-1]),ct.set(rt.lineDetectionVertical,[-1,2,-1,-1,2,-1,-1,2,-1]),ct.set(rt.lineDetectionLeftDiagonal,[2,-1,-1,-1,2,-1,-1,-1,2]),ct.set(rt.lineDetectionRightDiagonal,[-1,-1,2,-1,2,-1,2,-1,-1]),ct.set(rt.gradientNorth,[-1,-2,-1,0,0,0,1,2,1]),ct.set(rt.gradientWest,[-1,0,1,-2,0,2,-1,0,1]),ct.set(rt.gradientEast,[1,0,-1,2,0,-2,1,0,-1]),ct.set(rt.gradientSouth,[1,2,1,0,0,0,-1,-2,-1]),ct.set(rt.gradientNorthEast,[0,-1,-2,1,0,-1,2,1,0]),ct.set(rt.gradientNorthWest,[-2,-1,0,-1,0,1,0,1,2]),ct.set(rt.smoothArithmeticMean,[.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111]),ct.set(rt.smoothing3x3,[.0625,.125,.0625,.125,.25,.125,.0625,.125,.0625]),ct.set(rt.smoothing5x5,[1,1,1,1,1,1,4,4,4,1,1,4,12,4,1,1,4,4,4,1,1,1,1,1,1]),ct.set(rt.sharpening3x3,[-1,-1,-1,-1,9,-1,-1,-1,-1]),ct.set(rt.sharpening5x5,[-1,-3,-4,-3,-1,-3,0,6,0,-3,-4,6,21,6,-4,-3,0,6,0,-3,-1,-3,-4,-3,-1]),ct.set(rt.laplacian3x3,[0,-1,0,-1,4,-1,0,-1,0]),ct.set(rt.laplacian5x5,[0,0,-1,0,0,0,-1,-2,-1,0,-1,-2,17,-2,-1,0,-1,-2,-1,0,0,0,-1,0,0]),ct.set(rt.sobelHorizontal,[-1,-2,-1,0,0,0,1,2,1]),ct.set(rt.sobelVertical,[-1,0,1,-2,0,2,-1,0,1]),ct.set(rt.sharpen,[0,-.25,0,-.25,2,-.25,0,-.25,0]),ct.set(rt.sharpen2,[-.25,-.25,-.25,-.25,3,-.25,-.25,-.25,-.25]),ct.set(rt.pointSpread,[-.627,.352,-.627,.352,2.923,.352,-.627,.352,-.627]);let dt=mt=class extends f{constructor(){super(...arguments),this.rows=3,this.cols=3,this.kernel=[0,0,0,0,1,0,0,0,0]}set convolutionType(t){this._set("convolutionType",t);const e=ct.get(t);if(!e||t===rt.userDefined||t===rt.none)return;const n=Math.sqrt(e.length);this._set("kernel",e),this._set("cols",n),this._set("rows",n)}clone(){return new mt({cols:this.cols,rows:this.rows,kernel:[...this.kernel],convolutionType:this.convolutionType,raster:(0,o.d9)(this.raster)})}};(0,i._)([(0,u.Cb)({json:{type:Number,write:!0}})],dt.prototype,"rows",void 0),(0,i._)([(0,u.Cb)({json:{type:Number,write:!0}})],dt.prototype,"cols",void 0),(0,i._)([(0,u.Cb)({json:{name:"type",type:Number,write:!0}})],dt.prototype,"convolutionType",null),(0,i._)([(0,u.Cb)({json:{type:[Number],write:!0}})],dt.prototype,"kernel",void 0),dt=mt=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.ConvolutionFunctionArguments")],dt);const gt=dt;let yt=class extends w{constructor(){super(...arguments),this.functionName="Convolution",this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const{convolutionType:t,rows:e,cols:n,kernel:s}=this.functionArguments;if(!Object.values(rt).includes(t))return{success:!1,supportsGPU:!1,error:`convolution-function: the specified kernel type is not supported ${t}`};if(t!==rt.none&&e*n!==s.length)return{success:!1,supportsGPU:!1,error:"convolution-function: the specified rows and cols do not match the length of the kernel"};const r=this.sourceRasterInfos[0];this.outputPixelType=this._getOutputPixelType(r.pixelType);const o=r.clone();o.pixelType=this.outputPixelType;const i=[rt.none,rt.sharpen,rt.sharpen2,rt.sharpening3x3,rt.sharpening5x5];return"u8"===this.outputPixelType||i.includes(t)||(o.statistics=null,o.histograms=null),o.colormap=null,o.attributeTable=null,this.rasterInfo=o,{success:!0,supportsGPU:s.length<=25}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e)||this.functionArguments.convolutionType===rt.none)return e;let{kernel:n,rows:s,cols:r}=this.functionArguments;const o=n.reduce(((t,e)=>t+e));return 0!==o&&1!==o&&(n=n.map((t=>t/o))),function(t,e){const n=function(t){const e=Math.sqrt(t.length),n=t.slice(0,e),s=[1];for(let n=1;n<e;n++){let r=null;for(let s=0;s<e;s++){const o=t[s+n*e],i=t[s];if(null==r)if(0===i){if(o)return{separable:!1,row:null,col:null}}else r=o/i;else if(o/i!==r)return{separable:!1,row:null,col:null}}if(null==r)return{separable:!1,row:null,col:null};s.push(r)}return{separable:!0,row:n,col:s}}(e.kernel),s=!1!==e.mirrorEdges,r=n.separable?function(t,e,n,s=!0){const{pixels:r,width:o,height:i,pixelType:a,mask:u}=t,l=r.length,c=[],p=e.length,f=n.length,h=Math.floor(p/2),m=Math.floor(f/2);for(let t=0;t<l;t++){let a=pt(r[t],o,i,h,u,e,!0);a=pt(a,o,i,m,u,n,!1),s&&ht(a,o,i,p,f),c.push(a)}return new y.Z({width:o,height:i,pixelType:a,pixels:c,mask:u})}(t,n.row,n.col,s):function(t,e,n=!0){const{pixels:s,width:r,height:o,pixelType:i,mask:a}=t,u=s.length,l=[],{kernel:c,rows:p,cols:f}=e;for(let t=0;t<u;t++){const e=ft(s[t],r,o,p,f,a,c);n&&ht(e,r,o,p,f),l.push(e)}return new y.Z({width:r,height:o,pixelType:i,pixels:l,mask:a})}(t,e,s),{outputPixelType:o}=e;return o&&r.clamp(o),r}(e,{kernel:n,rows:s,cols:r,outputPixelType:this.outputPixelType})}_getWebGLParameters(){let{kernel:t}=this.functionArguments;const e=t.reduce(((t,e)=>t+e));0!==e&&1!==e&&(t=t.map((t=>t/e)));const n=new Float32Array(25);return n.set(t),{kernelRows:this.functionArguments.rows,kernelCols:this.functionArguments.cols,kernel:n,clampRange:(0,lt.r)(this.outputPixelType)}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],yt.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:gt,json:{write:!0,name:"rasterFunctionArguments"}})],yt.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],yt.prototype,"rasterArgumentNames",void 0),yt=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.ConvolutionFunction")],yt);const xt=yt;var bt;let wt=bt=class extends f{constructor(){super(...arguments),this.bandIds=[],this.missingBandAction=k.CD.bestMatch}clone(){return new bt({bandIds:[...this.bandIds],missingBandAction:this.missingBandAction})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],wt.prototype,"bandIds",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],wt.prototype,"missingBandAction",void 0),wt=bt=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.ExtractBandFunctionArguments")],wt);const vt=wt;let Ct=class extends w{constructor(){super(...arguments),this.functionName="ExtractBand",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0],{bandCount:n}=e,{bandIds:s,missingBandAction:r}=this.functionArguments;if(r===k.CD.fail&&s.some((t=>t<0||t>=n)))return{success:!1,supportsGPU:!1,error:"extract-band-function: invalid bandIds"};this.outputPixelType=this._getOutputPixelType("f32");const o=e.clone();o.pixelType=this.outputPixelType,o.bandCount=s.length;const{statistics:i,histograms:u}=o;return(0,a.pC)(i)&&i.length&&(o.statistics=s.map((t=>i[t]||i[i.length-1]))),(0,a.pC)(u)&&u.length&&(o.histograms=s.map((t=>u[t]||u[u.length-1]))),this.rasterInfo=o,{success:!0,supportsGPU:o.bandCount<=3}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return null;const n=e.pixels.length,s=this.functionArguments.bandIds.map((t=>t>=n?n-1:t));return e.extractBands(s)}_getWebGLParameters(){let t;if(this.isInputBandIdsSwizzled)t=this.swizzledBandSelection.length?this.swizzledBandSelection:[0,1,2];else{t=[...this.functionArguments.bandIds],0===t.length?t=[0,1,2]:t.length<3&&(t[1]=t[1]??t[0],t[2]=t[2]??t[1]);for(let e=0;e<3;e++)t[e]=Math.min(t[e],2)}return{bandIndexMat3:M(t)}}_getInputBandIds(t){const e=t.length;return this.functionArguments.bandIds.map((t=>t>=e?e-1:t)).map((e=>t[e]))}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],Ct.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:vt,json:{write:!0,name:"rasterFunctionArguments"}})],Ct.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],Ct.prototype,"rasterArgumentNames",void 0),Ct=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.ExtractBandFunction")],Ct);const _t=Ct;var At;let Rt=At=class extends f{constructor(){super(...arguments),this.rasters=[],this.processAsMultiband=!0}writeRasters(t,e){e.rasters=t.map((t=>"number"==typeof t||"string"==typeof t?t:t.toJSON()))}clone(){return new At({operation:this.operation,processAsMultiband:this.processAsMultiband,rasters:(0,o.d9)(this.rasters)})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],Rt.prototype,"operation",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Rt.prototype,"rasters",void 0),(0,i._)([(0,Q.c)("rasters")],Rt.prototype,"writeRasters",null),(0,i._)([(0,u.Cb)({json:{write:!0}})],Rt.prototype,"processAsMultiband",void 0),Rt=At=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.LocalFunctionArguments")],Rt);const Pt=Rt,Nt=new Map;Nt.set(it.acos,[0,Math.PI]),Nt.set(it.asin,[-Math.PI/2,Math.PI/2]),Nt.set(it.atan,[-Math.PI/2,Math.PI/2]),Nt.set(it.cos,[-1,1]),Nt.set(it.sin,[-1,1]),Nt.set(ot.booleanAnd,[0,1]),Nt.set(ot.booleanNot,[0,1]),Nt.set(ot.booleanOr,[0,1]),Nt.set(ot.booleanXOr,[0,1]),Nt.set(ot.equalTo,[0,1]),Nt.set(ot.notEqual,[0,1]),Nt.set(ot.greaterThan,[0,1]),Nt.set(ot.greaterThanEqual,[0,1]),Nt.set(ot.lessThan,[0,1]),Nt.set(ot.lessThanEqual,[0,1]),Nt.set(ot.isNull,[0,1]);const It=[0,2,2,2,1,2,1,1,1,1,1,2,2,1,2,2,2,2,1,2,2,1,1,2,2,1,1,1,2,2,1,1,1,2,2,1,1,1,999,999,999,999,999,999,2,1,2,999,1,1,2,1,1,1,999,999,1,1,999,1,1,2,999,999,2,2,999,999,999,999,999,999,999,999,999,999,3,999,3];function Ft(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]+r[t]);return i}function Tt(t,e,n){const[s]=t,r=s.length,o=y.Z.createEmptyBand("f32",r);return o.set(s),o}function kt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]-r[t]);return i}function St(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]*r[t]);return i}function Mt(t,e,n){const[s]=t,r=s.length,o=y.Z.createEmptyBand(n,r);for(let t=0;t<r;t++)e&&!e[t]||(o[t]=Math.sign(s[t])*Math.floor(Math.abs(s[t])));return o}function Bt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]/r[t]);return i}function jt(t,e,n){return Bt(t,e,"f32")}function Et(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=Math.floor(s[t]/r[t]));return i}function Gt(t,e,n,s){const r=t[0],o=r.length,i=y.Z.createEmptyBand(n,o);if(s===it.atanh){for(let t=0;t<o;t++)if(e[t]){const n=r[t];Math.abs(n)>=1?e[t]=0:i[t]=Math.atanh(n)}return i}const a=s===it.asin?Math.asin:Math.acos;for(let t=0;t<o;t++)if(e[t]){const n=r[t];Math.abs(n)>1?e[t]=0:i[t]=a(n)}return i}function Dt(t,e,n,s){const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s(r[t]));return i}function Ot(t,e,n,s){const[r,o]=t,i=r.length,a=y.Z.createEmptyBand(n,i);for(let t=0;t<i;t++)e&&!e[t]||(a[t]=s(r[t],o[t]));return a}function zt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]&r[t]);return i}function Zt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]<<r[t]);return i}function Vt(t,e,n){const[s]=t,r=s.length,o=y.Z.createEmptyBand(n,r);for(let t=0;t<r;t++)e&&!e[t]||(o[t]=~s[t]);return o}function Wt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]|r[t]);return i}function Lt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]>>r[t]);return i}function Ut(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]^r[t]);return i}function qt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]&&r[t]?1:0);return i}function Xt(t,e,n){const[s]=t,r=s.length,o=y.Z.createEmptyBand(n,r);for(let t=0;t<r;t++)e&&!e[t]||(o[t]=s[t]?0:1);return o}function Ht(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]||r[t]?1:0);return i}function Jt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=(s[t]?1:0)^(r[t]?1:0));return i}function Yt(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]===r[t]?1:0);return i}function $t(t,e,n,s){const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o),a=s===Math.E;for(let t=0;t<o;t++)e&&!e[t]||(i[t]=a?Math.exp(r[t]):s**r[t]);return i}function Kt(t,e,n){return $t(t,e,n,10)}function Qt(t,e,n){return $t(t,e,n,2)}function te(t,e,n){return $t(t,e,n,Math.E)}function ee(t,e,n,s){const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(r[t]<=0?e[t]=0:i[t]=s(r[t]));return i}function ne(t,e,n){return ee(t,e,n,Math.log10)}function se(t,e,n){return ee(t,e,n,Math.log2)}function re(t,e,n){return ee(t,e,n,Math.log)}function oe(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]>r[t]?1:0);return i}function ie(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]>=r[t]?1:0);return i}function ae(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]<r[t]?1:0);return i}function ue(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]<=r[t]?1:0);return i}function le(t,e,n){const[s]=t,r=s.length,o=y.Z.createEmptyBand(n,r);if(!e)return o;for(let t=0;t<r;t++)o[t]=e[t]?0:1;return o}function ce(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]%r[t]);return i}function pe(t,e,n){const[s]=t,r=s.length,o=y.Z.createEmptyBand(n,r);for(let t=0;t<r;t++)e&&!e[t]||(o[t]=-s[t]);return o}function fe(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e&&!e[t]||(i[t]=s[t]===r[t]?0:1);return i}function he(t,e,n){const[s,r]=t,o=s.length,i=y.Z.createEmptyBand(n,o),a=new Uint8Array(o);for(let t=0;t<o;t++)null!=e&&!e[t]||0!==s[t]||(i[t]=r[t],a[t]=255);return{band:i,mask:a}}function me(t,e,n){const[s,r,o]=t,i=s.length,a=y.Z.createEmptyBand(n,i);for(let t=0;t<i;t++)e&&!e[t]||(a[t]=s[t]?r[t]:o[t]);return a}function de(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let n=0;n<o;n++)if(!e||e[n]){let e=r[n];for(let r=1;r<s;r++){const s=t[r][n];e<s&&(e=s)}i[n]=e}return i}function ge(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let n=0;n<o;n++)if(!e||e[n]){let e=r[n];for(let r=1;r<s;r++){const s=t[r][n];e>s&&(e=s)}i[n]=e}return i}function ye(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let n=0;n<o;n++)if(!e||e[n]){let e=r[n],o=e;for(let r=1;r<s;r++){const s=t[r][n];o<s?o=s:e>s&&(e=s)}i[n]=o-e}return i}function xe(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let n=0;n<o;n++)if(!e||e[n]){let e=0;for(let r=0;r<s;r++)e+=t[r][n];i[n]=e/s}return i}function be(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let n=0;n<o;n++)if(!e||e[n])for(let e=0;e<s;e++){const s=t[e];i[n]+=s[n]}return i}function we(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o);for(let n=0;n<o;n++)if(!e||e[n]){const e=new Float32Array(s);let r=0;for(let o=0;o<s;o++){const s=t[o];r+=s[n],e[o]=s[n]}r/=s;let o=0;for(let t=0;t<s;t++)o+=(e[t]-r)**2;i[n]=Math.sqrt(o/s)}return i}function ve(t,e,n){const s=t.length;if(s<2)return t[0];const r=Math.floor(s/2),[o]=t,i=o.length,a=y.Z.createEmptyBand(n,i),u=new Float32Array(s),l=s%2==1;for(let n=0;n<i;n++)if(!e||e[n]){for(let e=0;e<s;e++)u[e]=t[e][n];u.sort(),a[n]=l?u[r]:(u[r]+u[r-1])/2}return a}function Ce(t,e,n){const[s,r]=t;if(null==r)return s;const o=s.length,i=y.Z.createEmptyBand(n,o);for(let t=0;t<o;t++)e[t]&&(s[t]===r[t]?i[t]=s[t]:e[t]=0);return i}function _e(t,e,n){const s=t.length;if(s<=2)return Ce(t,e,n);const r=t[0].length,o=y.Z.createEmptyBand(n,r),i=new Map;for(let n=0;n<r;n++)if(!e||e[n]){let e;i.clear();for(let r=0;r<s;r++)e=t[r][n],i.set(e,i.has(e)?i.get(e)+1:1);let r=0,a=0;for(const t of i.keys())r=i.get(t),r>a&&(a=r,e=t);o[n]=e}return o}function Ae(t,e,n){const s=t.length;if(s<=2)return Ce(t,e,n);const r=t[0].length,o=y.Z.createEmptyBand(n,r),i=new Map;for(let n=0;n<r;n++)if(!e||e[n]){let e;i.clear();for(let r=0;r<s;r++)e=t[r][n],i.set(e,i.has(e)?i.get(e)+1:1);let r=0,a=t.length;for(const t of i.keys())r=i.get(t),r<a&&(a=r,e=t);o[n]=e}return o}function Re(t,e,n){const s=t.length;if(s<2)return t[0];const[r]=t,o=r.length,i=y.Z.createEmptyBand(n,o),a=new Set;for(let n=0;n<o;n++)if(!e||e[n]){let e;a.clear();for(let r=0;r<s;r++)e=t[r][n],a.add(e);i[n]=a.size}return i}const Pe=new Map,Ne=new Map,Ie=new Map,Fe=new Map;function Te(t,e,n,s){let[r,o]=(0,lt.r)(n);const i=n.startsWith("u")||n.startsWith("s");i&&(r-=1e-5,o+=1e-5);for(let n=0;n<e.length;n++)if(e[n]){const a=t[n];isNaN(a)||a<r||a>o?e[n]=0:s[n]=i?Math.round(a):a}}let ke=class extends w{constructor(){super(...arguments),this.functionName="Local",this.functionArguments=null,this.rasterArgumentNames=["rasters"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0],{bandCount:n}=e,{processAsMultiband:s}=this.functionArguments;if(t.some((t=>t.bandCount!==n)))return{success:!1,supportsGPU:!1,error:"local-function: input rasters do not have same band count"};const{operation:r,rasters:o}=this.functionArguments,i=It[r];if(!(999===i||o.length===i||o.length<=1&&1===i))return{success:!1,supportsGPU:!1,error:`local-function: the length of functionArguments.rasters does not match operation's requirement: ${i}`};this.outputPixelType=this._getOutputPixelType("f32");const a=e.clone();a.pixelType=this.outputPixelType,a.statistics=null,a.histograms=null,a.colormap=null,a.attributeTable=null,a.bandCount=999!==i||s?n:1;const u=function(t){return Nt.get(t)}(r);if(u){a.statistics=[];for(let t=0;t<a.bandCount;t++)a.statistics[t]={min:u[0],max:u[1],avg:(u[0]+u[1])/2,stddev:(u[0]+u[1])/10}}return this.rasterInfo=a,{success:!0,supportsGPU:1===a.bandCount&&i<=3&&(r<11||r>16)}}_processPixels(t){const{pixelBlocks:e}=t;return(0,a.Wi)(e)||e.some((t=>(0,a.Wi)(t)))?null:function(t,e,n={}){Pe.size||(Pe.set(4,Math.sqrt),Pe.set(6,Math.acos),Pe.set(7,Math.asin),Pe.set(8,Math.atan),Pe.set(9,Math.atanh),Pe.set(10,Math.abs),Pe.set(21,Math.cos),Pe.set(22,Math.cosh),Pe.set(48,Math.floor),Pe.set(49,Math.ceil),Pe.set(51,Math.sin),Pe.set(52,Math.sinh),Pe.set(56,Math.tan),Pe.set(57,Math.tanh),Pe.set(59,Math.acosh),Pe.set(60,Math.asinh),Pe.set(65,Math.floor),Ne.set(5,Math.pow),Ne.set(61,Math.atan2),Ie.set(1,Ft),Ie.set(2,kt),Ie.set(3,St),Ie.set(11,zt),Ie.set(12,Zt),Ie.set(12,Zt),Ie.set(13,Vt),Ie.set(14,Wt),Ie.set(15,Lt),Ie.set(16,Ut),Ie.set(17,qt),Ie.set(18,Xt),Ie.set(19,Ht),Ie.set(20,Jt),Ie.set(23,Bt),Ie.set(24,Yt),Ie.set(25,te),Ie.set(26,Kt),Ie.set(27,Qt),Ie.set(28,oe),Ie.set(29,ie),Ie.set(30,Mt),Ie.set(31,le),Ie.set(32,Tt),Ie.set(33,ae),Ie.set(34,ue),Ie.set(35,re),Ie.set(36,ne),Ie.set(37,se),Ie.set(44,ce),Ie.set(45,pe),Ie.set(46,fe),Ie.set(64,jt),Ie.set(65,Et),Ie.set(76,me),Ie.set(78,me),Fe.set(38,_e),Fe.set(39,de),Fe.set(40,xe),Fe.set(41,ve),Fe.set(42,ge),Fe.set(43,Ae),Fe.set(47,ye),Fe.set(54,we),Fe.set(55,be),Fe.set(58,Re),Fe.set(66,_e),Fe.set(67,de),Fe.set(68,xe),Fe.set(69,ve),Fe.set(70,ge),Fe.set(71,Ae),Fe.set(72,ye),Fe.set(73,we),Fe.set(74,be),Fe.set(75,Re));let s=function(t,e=!1){const n=t.map((t=>t.mask)),s=n.filter((t=>(0,a.pC)(t))),r=t[0].pixels[0].length;if(0===s.length)return new Uint8Array(r).fill(255);const o=s[0],i=new Uint8Array(o);if(1===s.length)return i;if(!e){for(let t=1;t<s.length;t++){const e=s[t];for(let t=0;t<i.length;t++)i[t]&&(i[t]=e[t]?255:0)}return i}if(s.length!==n.length)return new Uint8Array(r).fill(255);for(let t=1;t<s.length;t++){const e=s[t];for(let t=0;t<i.length;t++)0===i[t]&&(i[t]=e[t]?255:0)}return i}(t,e>=66&&e<=75);const{outputPixelType:r="f32"}=n,o=!Fe.has(e)||n.processAsMultiband,i=o?t[0].pixels.length:1,u=[];for(let n=0;n<i;n++){const i=Fe.has(e)&&!o?t.flatMap((t=>t.pixels)):t.map((t=>t.pixels[n]));let a,l=!0;if(e===at.setNull){const t=he(i,s,r);a=t.band,s=t.mask,l=!1}else Ie.has(e)?a=Ie.get(e)(i,s,"f64"):Pe.has(e)?a=e===it.asin||e===it.acos||e===it.atanh?Gt(i,s,"f64",e):Dt(i,s,"f64",Pe.get(e)):Ne.has(e)?a=Ot(i,s,"f64",Ne.get(e)):Fe.has(e)?a=Fe.get(e)(i,s,"f64"):(a=i[0],l=!1);if(l&&e!==ot.isNull&&!Nt.has(e)){const t=y.Z.createEmptyBand(r,a.length);s||(s=new Uint8Array(a.length).fill(255)),Te(a,s,r,t),a=t}u.push(a)}const l=t[0];return new y.Z({width:l.width,height:l.height,pixelType:r,mask:e===ot.isNull?null:s,pixels:u})}(e,this.functionArguments.operation,{processAsMultiband:this.functionArguments.processAsMultiband,outputPixelType:this.outputPixelType??void 0})}_getWebGLParameters(){const{operation:t}=this.functionArguments,e=It[t],n=Object.keys(ut).find((e=>ut[e]===t))?.toLowerCase()??"undefined",s=this.outputPixelType??"f32";let[r,o]=(0,lt.r)(s);const i=s.startsWith("u")||s.startsWith("s");return i&&(r-=1e-4,o+=1e-4),{imageCount:e,operationName:n,domainRange:[r,o],isOutputRounded:i}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],ke.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:Pt,json:{write:!0,name:"rasterFunctionArguments"}})],ke.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],ke.prototype,"rasterArgumentNames",void 0),ke=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.LocalFunction")],ke);const Se=ke;var Me,Be=n(22021);let je=Me=class extends f{constructor(){super(...arguments),this.includedRanges=null,this.noDataValues=null,this.noDataInterpretation=k.DX.matchAny}get normalizedNoDataValues(){const{noDataValues:t}=this;if(!t?.length)return null;let e=!1;const n=t.map((t=>{if("number"==typeof t)return e=!0,[t];if("string"==typeof t){const n=t.trim().split(" ").filter((t=>""!==t.trim())).map((t=>Number(t)));return e=e||n.length>0,0===n.length?null:n}return null}));return e?n:null}clone(){return new Me({includedRanges:this.includedRanges?.slice()??[],noDataValues:this.noDataValues?.slice()??[],noDataInterpretation:this.noDataInterpretation})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],je.prototype,"includedRanges",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],je.prototype,"noDataValues",void 0),(0,i._)([(0,u.Cb)()],je.prototype,"normalizedNoDataValues",null),(0,i._)([(0,u.Cb)({json:{write:!0}})],je.prototype,"noDataInterpretation",void 0),je=Me=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.MaskFunctionArguments")],je);const Ee=je;let Ge=class extends w{constructor(){super(...arguments),this.functionName="Mask",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const t=this.sourceRasterInfos[0].clone(),{pixelType:e}=t;this.outputPixelType=this._getOutputPixelType(e),t.pixelType=this.outputPixelType,this.rasterInfo=t;const{includedRanges:n,normalizedNoDataValues:s}=this.functionArguments;if(!n?.length&&!s?.length)return{success:!1,supportsGPU:!1,error:"missing includedRanges or noDataValues argument"};let r=[];for(let o=0;o<t.bandCount;o++){const t=(0,k.nA)(e,n?.slice(2*o,2*o+2),s?.[o]);if(null==t){r=null;break}r.push(t)}this.lookups=r;const o=null!=s&&s.every((t=>t?.length===s[0]?.length));return{success:!0,supportsGPU:(!n||n.length<=2*k.hd)&&(!s||o&&s[0].length<=k.hd)}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return null;const{outputPixelType:n,lookups:s}=this,{includedRanges:r,noDataInterpretation:o,normalizedNoDataValues:i}=this.functionArguments,u=o===k.DX.matchAll;return(0,k.sS)(e,{includedRanges:r,noDataValues:i,outputPixelType:n,matchAll:u,lookups:s})}_getWebGLParameters(){const{includedRanges:t,normalizedNoDataValues:e}=this.functionArguments,n=new Float32Array(k.hd);n.fill(Be._3),e?.[0]?.length&&n.set(e[0]);const s=new Float32Array(k.hd);for(let e=0;e<s.length;e+=2)s[e]=t?.[e]??-Be._3,s[e+1]=t?.[e+1]??Be._3;return t&&t.length&&s.set(t),{bandCount:this.sourceRasterInfos[0].bandCount,noDataValues:n,includedRanges:s}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],Ge.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:Ee,json:{write:!0,name:"rasterFunctionArguments"}})],Ge.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],Ge.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Ge.prototype,"lookups",void 0),Ge=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.MaskFunction")],Ge);const De=Ge;var Oe;let ze=Oe=class extends f{constructor(){super(...arguments),this.visibleBandID=0,this.infraredBandID=1,this.scientificOutput=!1}clone(){const{visibleBandID:t,infraredBandID:e,scientificOutput:n}=this;return new Oe({visibleBandID:t,infraredBandID:e,scientificOutput:n})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],ze.prototype,"visibleBandID",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],ze.prototype,"infraredBandID",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],ze.prototype,"scientificOutput",void 0),ze=Oe=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.NDVIFunctionArguments")],ze);const Ze=ze;let Ve=class extends w{constructor(){super(...arguments),this.functionName="NDVI",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const{scientificOutput:t}=this.functionArguments;this.outputPixelType=this._getOutputPixelType(t?"f32":"u8");const e=this.sourceRasterInfos[0].clone();e.pixelType=this.outputPixelType,e.colormap=null,e.histograms=null,e.bandCount=1;const[n,s,r,o]=t?[-1,1,0,.1]:[0,200,100,10];return e.statistics=[{min:n,max:s,avg:r,stddev:o}],this.rasterInfo=e,{success:!0,supportsGPU:!0}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return null;const{visibleBandID:n,infraredBandID:s,scientificOutput:r}=this.functionArguments;return function(t,e,n,s){const{mask:r,pixels:o,width:i,height:a}=t,u=o[n],l=o[e],c=l.length,p=s?new Uint8Array(c):new Float32Array(c),f=s?100:1,h=s?100.5:0;for(let t=0;t<c;t++)if(null==r||r[t]){const e=u[t],n=l[t],s=e+n;s&&(p[t]=(e-n)/s*f+h)}const m=new y.Z({width:i,height:a,mask:r,pixelType:s?"u8":"f32",pixels:[p]});return m.updateStatistics(),m}(e,n,s,!r)}_getWebGLParameters(){const{visibleBandID:t,infraredBandID:e,scientificOutput:n}=this.functionArguments;return{bandIndexMat3:M(this.isInputBandIdsSwizzled?[0,1,2]:[e,t,0]),scaled:!n}}_getInputBandIds(t){const{visibleBandID:e,infraredBandID:n}=this.functionArguments;return[n,e,0].map((e=>t[e]))}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],Ve.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:Ze,json:{write:!0,name:"rasterFunctionArguments"}})],Ve.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],Ve.prototype,"rasterArgumentNames",void 0),Ve=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.NDVIFunction")],Ve);const We=Ve;var Le;let Ue=Le=class extends f{constructor(){super(...arguments),this.inputRanges=null,this.outputValues=null,this.noDataRanges=null,this.allowUnmatched=!1,this.isLastInputRangeInclusive=!1}clone(){return new Le({inputRanges:[...this.inputRanges],outputValues:[...this.outputValues],noDataRanges:[...this.noDataRanges],allowUnmatched:this.allowUnmatched,isLastInputRangeInclusive:this.isLastInputRangeInclusive})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],Ue.prototype,"inputRanges",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Ue.prototype,"outputValues",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Ue.prototype,"noDataRanges",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Ue.prototype,"allowUnmatched",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Ue.prototype,"isLastInputRangeInclusive",void 0),Ue=Le=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.RemapFunctionArguments")],Ue);const qe=Ue;let Xe=class extends w{constructor(){super(...arguments),this.functionName="Remap",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.lookup=null}_bindSourceRasters(){const t=this.sourceRasterInfos[0].clone(),{pixelType:e}=t;this.outputPixelType=this._getOutputPixelType(e),t.pixelType=this.outputPixelType,t.colormap=null,t.histograms=null,t.bandCount=1,t.attributeTable=null;const{statistics:n}=t,{allowUnmatched:s,outputValues:r,inputRanges:o,noDataRanges:i,isLastInputRangeInclusive:u}=this.functionArguments;if((0,a.pC)(n)&&n.length&&r?.length)if(s){const e=Math.min.apply(null,[...r,n[0].min]),s=Math.max.apply(null,[...r,n[0].max]);t.statistics=[{...n[0],min:e,max:s}]}else{let e=r[0],s=e;for(let t=0;t<r.length;t++)e=e>r[t]?r[t]:e,s=s>r[t]?s:r[t];t.statistics=[{...n[0],min:e,max:s}]}return this.rasterInfo=t,this.lookup=s?null:(0,k.oB)({srcPixelType:e,inputRanges:o,outputValues:r,noDataRanges:i,allowUnmatched:s,isLastInputRangeInclusive:u,outputPixelType:this.outputPixelType}),{success:!0,supportsGPU:(!r||r.length<=k.hd)&&(!i||i.length<=k.hd)}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return null;const{lookup:n,outputPixelType:s}=this;if(n){const t=(0,k.XV)(e,{lut:[n.lut],offset:n.offset,outputPixelType:s});return(0,a.pC)(t)&&n.mask&&(t.mask=(0,k.wV)(e.pixels[0],e.mask,n.mask,n.offset,"u8")),t}const{inputRanges:r,outputValues:o,noDataRanges:i,allowUnmatched:u,isLastInputRangeInclusive:l}=this.functionArguments;return(0,k.a2)(e,{inputRanges:r,outputValues:o,noDataRanges:i,outputPixelType:s,allowUnmatched:u,isLastInputRangeInclusive:l})}_getWebGLParameters(){const{allowUnmatched:t,inputRanges:e,outputValues:n,noDataRanges:s,isLastInputRangeInclusive:r}=this.functionArguments,o=new Float32Array(3*k.hd),i=1e-5,a=n.length;if(e?.length){let t=0,s=0;for(let u=0;u<o.length;u+=3)o[u]=e[t++]??Be._3-1,o[u+1]=e[t++]??Be._3,o[u+2]=n[s++]??0,s<=a&&(u>0&&(o[u]-=i),(s<a||!r)&&(o[u+1]-=i))}const u=new Float32Array(2*k.hd);return u.fill(Be._3),s?.length&&u.set(s),{allowUnmatched:t,rangeMaps:o,noDataRanges:u,clampRange:(0,lt.r)(this.outputPixelType)}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],Xe.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:qe,json:{write:!0,name:"rasterFunctionArguments"}})],Xe.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],Xe.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Xe.prototype,"lookup",void 0),Xe=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.RemapFunction")],Xe);const He=Xe;var Je;const Ye=new R.X({1:"degree",2:"percent-rise",3:"adjusted"},{useNumericKeys:!0});let $e=Je=class extends f{constructor(){super(...arguments),this.slopeType="degree",this.zFactor=1,this.pixelSizePower=.664,this.pixelSizeFactor=.024,this.removeEdgeEffect=!1}clone(){return new Je({slopeType:this.slopeType,zFactor:this.zFactor,pixelSizePower:this.pixelSizePower,pixelSizeFactor:this.pixelSizeFactor,removeEdgeEffect:this.removeEdgeEffect,raster:this.raster})}};(0,i._)([(0,A.J)(Ye)],$e.prototype,"slopeType",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{write:!0}})],$e.prototype,"zFactor",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{name:"psPower",write:!0}})],$e.prototype,"pixelSizePower",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{name:"psZFactor",write:!0}})],$e.prototype,"pixelSizeFactor",void 0),(0,i._)([(0,u.Cb)({type:Boolean,json:{write:!0}})],$e.prototype,"removeEdgeEffect",void 0),$e=Je=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.SlopeFunctionArguments")],$e);const Ke=$e;let Qe=class extends w{constructor(){super(...arguments),this.functionName="Slope",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isGCS=!1}_bindSourceRasters(){this.outputPixelType=this._getOutputPixelType("f32");const t=this.sourceRasterInfos[0].clone();return t.pixelType=this.outputPixelType,t.statistics="percent-rise"!==this.functionArguments.slopeType?[{min:0,max:90,avg:1,stddev:1}]:null,t.histograms=null,t.colormap=null,t.attributeTable=null,t.bandCount=1,this.rasterInfo=t,this.isGCS=t.spatialReference?.isGeographic??!1,{success:!0,supportsGPU:!0}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return null;const{zFactor:n,slopeType:s,pixelSizePower:r,pixelSizeFactor:o}=this.functionArguments,{isGCS:i}=this,{extent:u}=t,l=u?{x:u.width/e.width,y:u.height/e.height}:{x:1,y:1};return(0,v.yg)(e,{zFactor:n,slopeType:s,pixelSizePower:r,pixelSizeFactor:o,isGCS:i,resolution:l})}_getWebGLParameters(){const{zFactor:t,slopeType:e,pixelSizeFactor:n,pixelSizePower:s}=this.functionArguments;return{zFactor:this.isGCS&&t>=1?900900900900901e-20*t:t,slopeType:e,pixelSizeFactor:n??0,pixelSizePower:s??0}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],Qe.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:Ke,json:{write:!0,name:"rasterFunctionArguments"}})],Qe.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],Qe.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],Qe.prototype,"isGCS",void 0),Qe=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.SlopeFunction")],Qe);const tn=Qe;var en;let nn=en=class extends f{constructor(){super(...arguments),this.statistics=null,this.histograms=null}readStatistics(t,e){if(!t?.length)return null;const n=[];return t.forEach((t=>{const e={min:t.min,max:t.max,avg:t.avg??t.mean,stddev:t.stddev??t.standardDeviation};n.push(e)})),n}writeStatistics(t,e,n){if(!t?.length)return;const s=[];t.forEach((t=>{const e={...t,mean:t.avg,standardDeviation:t.stddev};delete e.avg,delete e.stddev,s.push(e)})),e[n]=s}clone(){return new en({statistics:(0,o.d9)(this.statistics),histograms:(0,o.d9)(this.histograms)})}};(0,i._)([(0,u.Cb)({json:{write:!0}})],nn.prototype,"statistics",void 0),(0,i._)([(0,V.r)("statistics")],nn.prototype,"readStatistics",null),(0,i._)([(0,Q.c)("statistics")],nn.prototype,"writeStatistics",null),(0,i._)([(0,u.Cb)({json:{write:!0}})],nn.prototype,"histograms",void 0),nn=en=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.StatisticsHistogramFunctionArguments")],nn);const sn=nn;let rn=class extends w{constructor(){super(...arguments),this.functionName="StatisticsHistogram",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isNoopProcess=!0}_bindSourceRasters(){const t=this.sourceRasterInfos[0];this.outputPixelType=this._getOutputPixelType("u8");const e=t.clone(),{statistics:n,histograms:s}=this.functionArguments;return s&&(e.histograms=s),n&&(e.statistics=n),this.rasterInfo=e,{success:!0,supportsGPU:!0}}_processPixels(t){return t.pixelBlocks?.[0]}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],rn.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:sn,json:{write:!0,name:"rasterFunctionArguments"}})],rn.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],rn.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],rn.prototype,"indexedColormap",void 0),(0,i._)([(0,u.Cb)()],rn.prototype,"isNoopProcess",void 0),rn=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.StatisticsHistogramFunction")],rn);const on=rn;var an;const un=new R.X({0:"none",3:"standard-deviation",4:"histogram-equalization",5:"min-max",6:"percent-clip",9:"sigmoid"},{useNumericKeys:!0});let ln=an=class extends f{constructor(){super(...arguments),this.computeGamma=!1,this.dynamicRangeAdjustment=!1,this.gamma=[],this.histograms=null,this.statistics=null,this.stretchType="none",this.useGamma=!1}writeStatistics(t,e,n){t?.length&&(Array.isArray(t[0])||(t=t.map((t=>[t.min,t.max,t.avg,t.stddev]))),e[n]=t)}clone(){return new an({stretchType:this.stretchType,outputMin:this.outputMin,outputMax:this.outputMax,useGamma:this.useGamma,computeGamma:this.computeGamma,statistics:(0,o.d9)(this.statistics),gamma:(0,o.d9)(this.gamma),sigmoidStrengthLevel:this.sigmoidStrengthLevel,numberOfStandardDeviations:this.numberOfStandardDeviations,minPercent:this.minPercent,maxPercent:this.maxPercent,histograms:(0,o.d9)(this.histograms),dynamicRangeAdjustment:this.dynamicRangeAdjustment,raster:this.raster})}};(0,i._)([(0,u.Cb)({type:Boolean,json:{write:!0}})],ln.prototype,"computeGamma",void 0),(0,i._)([(0,u.Cb)({type:Boolean,json:{name:"dra",write:!0}})],ln.prototype,"dynamicRangeAdjustment",void 0),(0,i._)([(0,u.Cb)({type:[Number],json:{write:!0}})],ln.prototype,"gamma",void 0),(0,i._)([(0,u.Cb)()],ln.prototype,"histograms",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{write:!0}})],ln.prototype,"maxPercent",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{write:!0}})],ln.prototype,"minPercent",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{write:!0}})],ln.prototype,"numberOfStandardDeviations",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{name:"max",write:!0}})],ln.prototype,"outputMax",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{name:"min",write:!0}})],ln.prototype,"outputMin",void 0),(0,i._)([(0,u.Cb)({type:Number,json:{write:!0}})],ln.prototype,"sigmoidStrengthLevel",void 0),(0,i._)([(0,u.Cb)({json:{type:[[Number]],write:!0}})],ln.prototype,"statistics",void 0),(0,i._)([(0,Q.c)("statistics")],ln.prototype,"writeStatistics",null),(0,i._)([(0,A.J)(un)],ln.prototype,"stretchType",void 0),(0,i._)([(0,u.Cb)({type:Boolean,json:{write:!0}})],ln.prototype,"useGamma",void 0),ln=an=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.StretchFunctionArguments")],ln);const cn=ln;var pn=n(15612);let fn=class extends w{constructor(){super(...arguments),this.functionName="Stretch",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.lookup=null,this.cutOffs=null}_bindSourceRasters(){this.lookup=null,this.cutOffs=null;const t=this.sourceRasterInfos[0],{pixelType:e}=t,{functionArguments:n}=this,{dynamicRangeAdjustment:s,gamma:r,useGamma:o}=n;if(!s&&["u8","u16","s8","s16"].includes(e)){const s=(0,pn.AV)(n.toJSON(),{rasterInfo:t}),i=this._isOutputRoundingNeeded()?"round":"float";this.lookup=(0,pn.hE)({pixelType:e,...s,gamma:o?r:null,rounding:i}),this.cutOffs=s}else s||(this.cutOffs=(0,pn.AV)(n.toJSON(),{rasterInfo:t}));this.outputPixelType=this._getOutputPixelType(e);const i=t.clone();return i.pixelType=this.outputPixelType,i.statistics=null,i.histograms=null,i.colormap=null,i.attributeTable=null,"u8"===this.outputPixelType&&(i.keyProperties.DataType="processed"),this.rasterInfo=i,{success:!0,supportsGPU:!s}}_processPixels(t){const e=t.pixelBlocks?.[0];if((0,a.Wi)(e))return e;const{lookup:n}=this;if(n)return(0,k.XV)(e,{...n,outputPixelType:this.rasterInfo.pixelType});const{functionArguments:s}=this,r=this.cutOffs||(0,pn.AV)(s.toJSON(),{rasterInfo:this.sourceRasterInfos[0],pixelBlock:e}),o=s.useGamma?s.gamma:null;return(0,pn.dy)(e,{...r,gamma:o,outputPixelType:this.outputPixelType})}_getWebGLParameters(){const{outputMin:t=0,outputMax:e=255,gamma:n,useGamma:s}=this.functionArguments,r=this.rasterInfo.bandCount>=2?3:1,o=s&&n&&n.length?(0,pn.um)(r,n):[1,1,1],{minCutOff:i,maxCutOff:a}=this.cutOffs??{minCutOff:[0,0,0],maxCutOff:[255,255,255]};1===i.length&&(i[1]=i[2]=i[0],a[1]=a[2]=a[0]);const u=new Float32Array(r);let l;for(l=0;l<r;l++)u[l]=(e-t)/(a[l]-i[l]);const c=this._isOutputRoundingNeeded();return{bandCount:r,outMin:t,outMax:e,minCutOff:i,maxCutOff:a,factor:u,useGamma:s,gamma:s&&n?n:[1,1,1],gammaCorrection:s&&o?o:[1,1,1],stretchType:this.functionArguments.stretchType,isOutputRounded:c,type:"stretch"}}};(0,i._)([(0,u.Cb)({json:{write:!0,name:"rasterFunction"}})],fn.prototype,"functionName",void 0),(0,i._)([(0,u.Cb)({type:cn,json:{write:!0,name:"rasterFunctionArguments"}})],fn.prototype,"functionArguments",void 0),(0,i._)([(0,u.Cb)()],fn.prototype,"rasterArgumentNames",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],fn.prototype,"lookup",void 0),(0,i._)([(0,u.Cb)({json:{write:!0}})],fn.prototype,"cutOffs",void 0),fn=(0,i._)([(0,l.j)("esri.layers.support.rasterFunctions.StretchFunction")],fn);const hn=fn,mn=new Map;function dn(t,e){const{rasterFunctionArguments:n}=t;n&&(n.rasters||[n.raster]).forEach((t=>{t&&"number"!=typeof t&&("string"==typeof t?t.startsWith("http")&&(e.includes(t)||e.push(t)):"rasterFunctionArguments"in t&&dn(t,e))}))}function gn(t,e){if(e=e??{},"function"in(t=(0,o.d9)(t))&&"arguments"in t&&t.arguments&&(t=function(t,e){e&&wn(t,e);const n={};return _n(t,n),n}(t,e)),"rasterFunction"in t)return bn(t=xn(t),e);throw new r.Z("raster-function-helper","unsupported raster function json.")}function yn(t){return!!(t&&"object"==typeof t&&t.rasterFunction&&t.rasterFunctionArguments)}function xn(t){const{rasterFunction:e,rasterFunctionArguments:n}=t,s={};for(const t in n){let e=n[t];const r=t.toLowerCase();if("rasters"===r&&Array.isArray(e))s.rasters=e.map((t=>yn(t)?xn(t):t));else switch(yn(e)&&(e=xn(e)),r){case"dra":s.dra=e;break;case"pspower":s.psPower=e;break;case"pszfactor":s.psZFactor=e;break;case"bandids":s.bandIds=e;break;default:s[t[0].toLowerCase()+t.slice(1)]=e}}return"Local"!==e||s.rasters?.length||(s.rasters=["$$"]),{...t,rasterFunctionArguments:s}}function bn(t,e){const{rasterFunction:n,rasterFunctionArguments:s}=t,o=t.outputPixelType?.toLowerCase();if(null==n||!mn.has(n))throw new r.Z("raster-function-helper",`unsupported raster function: ${n}`);const i=mn.get(n),a=("function"==typeof i.ctor?i.ctor:i.ctor.default).fromJSON({...t,outputPixelType:o}),{rasterArgumentNames:u}=a,l=[],c=function(t,e){return"rasters"===e[0]&&Array.isArray(t.rasters)?t.rasters:e.map((e=>t[e]))}(s,u),p="rasters"===u[0],f=[];for(let t=0;t<c.length;t++){const n=c[t];let s;null==n||"string"==typeof n&&n.startsWith("$")?l.push(e?.raster):"string"==typeof n?e[n]&&l.push(e[n]):"number"!=typeof n&&"rasterFunction"in n&&(s=bn(n,e),p||(a.functionArguments[u[t]]=s),l.push(s)),p&&f.push(s??n)}if(p&&(a.functionArguments.rasters=f),e){a.sourceRasters=l;const t=e.raster?.url;t&&(a.mainPrimaryRasterId=t)}return a}function wn(t,e){if(t&&e)for(const n in t){const s=t[n];s&&"object"==typeof s&&(s.function&&s.arguments?wn(s.arguments,e):"RasterFunctionVariable"===s.type&&null!=e[s.name]&&(s.value=e[s.name]))}}function vn(t){if(!t||"object"!=typeof t)return t;if(Array.isArray(t)&&0===t.length)return 0===t.length?null:["number","string"].includes(typeof t[0])?t:t.map((t=>vn(t)));if("value"in t&&["number","string","boolean"].includes(typeof t.value))return t.value;if(!("type"in t))return t;switch(t.type){case"Scalar":return t.value;case"AlgorithmicColorRamp":return Cn(t);case"MultiPartColorRamp":return{type:"multipart",colorRamps:t.ArrayOfColorRamp.map(Cn)};case"ArgumentArray":return t.elements?.length?"RasterStatistics"===t.elements[0].type?t.elements:"RasterFunctionVariable"===t.elements[0].type?t.elements.map((t=>null!=t.value?vn(t.value):t.name.toLowerCase().includes("raster")?"$$":null)):t:t.elements;default:return t}}function Cn(t){const e=t.algorithm??"esriHSVAlgorithm";let{FromColor:n,ToColor:r}=t;if(!Array.isArray(n)){const{r:t,g:e,b:r}=(0,s.xr)({h:n.Hue,s:n.Saturation,v:n.Value});n=[t,e,r,n.AlphaValue]}if(!Array.isArray(r)){const{r:t,g:e,b:n}=(0,s.xr)({h:r.Hue,s:r.Saturation,v:r.Value});r=[t,e,n,r.AlphaValue]}return{type:"algorithmic",algorithm:e,fromColor:n,toColor:r}}function _n(t,e){if(!t||!e)return;const{function:n,arguments:s}=t;if(!n||!s)return;e.rasterFunction=n.type.replace("Function",""),e.outputPixelType=n.pixelType;const r={};e.rasterFunctionArguments=r;for(const t in s){const n=s[t];"object"==typeof n&&("function"in n&&n.function&&n.arguments?(e.rasterFunctionArguments[t]={},_n(n,e.rasterFunctionArguments[t])):"value"in n&&(r[t]=vn(n.value)))}switch(r.DEM&&!r.Raster&&(r.Raster=r.DEM,delete r.DEM),e.rasterFunction){case"Stretch":!function(t){t.Statistics?.length&&"object"==typeof t.Statistics&&(t.Statistics=t.Statistics.map((t=>[t.min,t.max,t.mean,t.standardDeviation]))),null!=t.NumberOfStandardDeviation&&(t.NumberOfStandardDeviations=t.NumberOfStandardDeviation,delete t.NumberOfStandardDeviation)}(r);break;case"Colormap":!function(t){"randomcolorramp"===t.ColorRamp?.type?.toLowerCase()&&(delete t.ColorRamp,t.ColormapName="Random"),0===t.ColorSchemeType&&delete t.ColorRamp}(r);break;case"Convolution":!function(t){null!=t.ConvolutionType&&(t.Type=t.ConvolutionType,delete t.ConvolutionType)}(r);break;case"Mask":!function(t){t.NoDataValues?.length&&"string"==typeof t.NoDataValues[0]&&(t.NoDataValues=t.NoDataValues.filter((t=>""!==t)).map((t=>Number(t))))}(r)}}mn.set("Aspect",{desc:"Aspect Function",ctor:_,rasterArgumentNames:["raster"]}),mn.set("BandArithmetic",{desc:"Band Arithmetic Function",ctor:z,rasterArgumentNames:["raster"]}),mn.set("Colormap",{desc:"Colormap Function",ctor:$,rasterArgumentNames:["raster"]}),mn.set("CompositeBand",{desc:"CompositeBand Function",ctor:st,rasterArgumentNames:["rasters"]}),mn.set("Convolution",{desc:"Convolution Function",ctor:xt,rasterArgumentNames:["raster"]}),mn.set("ExtractBand",{desc:"ExtractBand Function",ctor:_t,rasterArgumentNames:["raster"]}),mn.set("Local",{desc:"Local Function",ctor:Se,rasterArgumentNames:["rasters"]}),mn.set("Mask",{desc:"Mask Function",ctor:De,rasterArgumentNames:["raster"]}),mn.set("NDVI",{desc:"NDVI Function",ctor:We,rasterArgumentNames:["raster"]}),mn.set("Remap",{desc:"Remap Function",ctor:He,rasterArgumentNames:["raster"]}),mn.set("Slope",{desc:"Slope Function",ctor:tn,rasterArgumentNames:["raster"]}),mn.set("StatisticsHistogram",{desc:"Statistics Histogram Function",ctor:on,rasterArgumentNames:["raster"]}),mn.set("Stretch",{desc:"Stretch Function",ctor:hn,rasterArgumentNames:["raster"]})},73506:(t,e,n)=>{n.d(e,{Hq:()=>G,Mk:()=>m,P_:()=>D,Qp:()=>O,VO:()=>C,kZ:()=>V,kr:()=>W,nF:()=>A,tB:()=>S,ut:()=>B,zD:()=>v}),n(66577);var s,r,o=n(20102),i=n(70586),a=n(67900),u=n(83305),l=n(44547),c=n(6570),p=n(94139),f=n(38913),h=n(82971);function m(t,e,n){return!(0,l.Up)(t,e,n)}function d(t,e,n){const s=m(t,e,n);if(s&&!(0,l.kR)())throw new o.Z("rasterprojectionhelper-project","projection engine is not loaded");return s}(r=s||(s={}))[r.None=0]="None",r[r.North=1]="North",r[r.South=2]="South",r[r.Both=3]="Both";const g=(t,e,n,s=0)=>{if(1===n[0])return[0,0];let r=1,o=-1,i=1,a=-1;for(let e=0;e<t.length;e+=2)isNaN(t[e])||(r=r>t[e]?t[e]:r,o=o>t[e]?o:t[e],i=i>t[e+1]?t[e+1]:i,a=a>t[e+1]?a:t[e+1]);const{cols:u,rows:l}=e,c=(o-r)/u/n[0],p=(a-i)/l/n[1],f=2*s;let h=0,m=!1,d=[0,0];for(let e=0;e<u-3;e++){for(let n=0;n<l-3;n++){const s=e*l*2+2*n,r=(t[s]+t[s+4]+t[s+4*l]+t[s+4*l+4])/4,o=(t[s+1]+t[s+5]+t[s+4*l+1]+t[s+4*l+5])/4,i=Math.abs((r-t[s+2*l+2])/c),a=Math.abs((o-t[s+2*l+3])/p);if(i+a>h&&(h=i+a,d=[i,a]),f&&h>f){m=!0;break}}if(m)break}return d},y={3395:20037508.342789244,3410:17334193.943686873,3857:20037508.342788905,3975:17367530.445161372,4087:20037508.342789244,4088:20015108.787169147,6933:17367530.445161372,32662:20037508.342789244,53001:20015086.79602057,53002:10007543.39801029,53003:20015086.79602057,53004:20015086.79602057,53016:14152803.599503474,53017:17333573.624304302,53034:20015086.79602057,53079:20015114.352186374,53080:20015114.352186374,54001:20037508.342789244,54002:10018754.171394624,54003:20037508.342789244,54004:20037508.342789244,54016:14168658.027268292,54017:17367530.44516137,54034:20037508.342789244,54079:20037508.342789244,54080:20037508.342789244,54100:20037508.342789244,54101:20037508.342789244},x=new Map,b=new Map,w=500;async function v(){(0,l.kR)()||await(0,l.zD)()}function C(t,e,n,s=null){const r=t.spatialReference;if(r.equals(e))return t;d(r,e,s);const o=n.center,u=new c.Z({xmin:o.x-t.x/2,xmax:o.x+t.x/2,ymin:o.y-t.y/2,ymax:o.y+t.y/2,spatialReference:r}),p=(0,l.iV)(u,e,s),f=B(e);let h;if((0,i.Wi)(p)||(0,i.pC)(f)&&p.width>=f){const n=(0,a.c9)(r)/(0,a.c9)(e);h={x:t.x*n,y:t.y*n}}else h={x:p.width,y:p.height};return h}function _(t,e=.01){return(0,a.c9)(t)?e/(0,a.c9)(t):0}function A(t,e,n=null,s=!0){const r=t.spatialReference;if(r.equals(e))return t;d(r,e,n);const o=(0,l.iV)(t,e,n);return s&&o?(R([t],[o],r,e),o):o}function R(t,e,n,s){const r=j(n,!0),o=j(s,!0),a=_(n,w),u=_(s,w);if(a&&(0,i.pC)(r)&&(0,i.pC)(o))for(let n=0;n<t.length;n++){const s=e[n];if(!s)continue;const{x:i}=t[n],{x:l}=s;l>=o[1]-u&&Math.abs(i-r[0])<a?s.x-=o[1]-o[0]:l<=o[0]+u&&Math.abs(i-r[1])<a&&(s.x+=o[1]-o[0])}}function P(t){const{inSR:e,outSR:n,datumTransformation:s,preferPE:r}=t;if(e.equals(n)){const{points:e}=T(t,null);return e}if(e.isWebMercator&&n.isWGS84||e.isWGS84&&n.isWebMercator)return function(t){const{cols:e,rows:n,xres:s,yres:r,usePixelCenter:o,inSR:i,outSR:a}=t;let{xmin:u,ymax:c}=t;o&&(u+=s/2,c-=r/2);const f=[],h=[],m=Math.max(e,n);for(let t=0;t<m;t++){const o=u+s*Math.min(e,t),m=c-r*Math.min(n,t),d=(0,l.iV)(new p.Z({x:o,y:m,spatialReference:i}),a);t<=e&&f.push(d.x),t<=n&&h.push(d.y)}const d=[];for(let t=0;t<e;t++)for(let e=0;e<n;e++)d.push([f[t],h[e]]);return d}(t);if(d(e,n,s)&&r){if(e.isGeographic)return N(t);const n=I(e);if((0,i.pC)(n))return N(t)}return function(t){const{points:e}=T(t,null),{inSR:n,outSR:s,datumTransformation:r}=t,o=e.map((t=>new p.Z(t[0],t[1],n))),i=(0,l.iV)(o,s,r);return r&&R(o,i,n,s),i.map((t=>t?[t.x,t.y]:[NaN,NaN]))}(t)}function N(t){const{inSR:e,outSR:n,datumTransformation:s}=t,r=I(e),{points:o,mask:a}=T(t,r);if(!e.isGeographic){const t=e.wkid?u.e.coordsys(e.wkid):u.e.fromString(e.isGeographic?u.f.PE_TYPE_GEOGCS:u.f.PE_TYPE_PROJCS,e.wkt);u.g.projToGeog(t,o.length,o)}if((0,i.pC)(s)&&s.steps.length){let t;const e=179.9955;if(n.isGeographic&&(t=o.map((([t])=>t>e?1:t<-e?-1:0))),s.steps.forEach((t=>{const e=t.wkid?u.e.geogtran(t.wkid):u.e.fromString(u.f.PE_TYPE_GEOGTRAN,t.wkt);u.h.geogToGeog(e,o.length,o,null,t.isInverse?u.f.PE_TRANSFORM_2_TO_1:u.f.PE_TRANSFORM_1_TO_2)})),t)for(let n=0;n<o.length;n++){const s=t[n],r=o[n][0],i=r>e?1:r<-e?-1:0;s&&i&&s!==i&&(o[n][0]=s>0?r+360:r-360)}}if(!n.isGeographic){const t=I(n,!0),e=(0,i.pC)(t)&&t.isEnvelope?[t.bbox[1],t.bbox[3]]:[-90,90];!function(t,e){const[n,s]=e;for(let e=0;e<t.length;e++){const r=t[e][1];(r<n||r>s)&&(t[e]=[NaN,NaN])}}(o,e);const s=n.wkid?u.e.coordsys(n.wkid):u.e.fromString(n.isGeographic?u.f.PE_TYPE_GEOGCS:u.f.PE_TYPE_PROJCS,n.wkt);u.g.geogToProj(s,o.length,o)}let l=o;if(a&&o.length!==a.length){l=[];for(let t=0,e=0;t<a.length;t++)a[t]?l.push(o[e++]):l.push([NaN,NaN])}return l}function I(t,e=!1){let n=t.wkid||t.wkt;if(!n||t.isGeographic)return null;if(n=String(n),x.has(n)){const t=x.get(n);return e?t?.gcs:t?.pcs}const s=t.wkid?u.e.coordsys(t.wkid):u.e.fromString(t.isGeographic?u.f.PE_TYPE_GEOGCS:u.f.PE_TYPE_PROJCS,t.wkt),r=F(s,_(t,1e-4)),o=F(s,0,!0);return x.set(n,{pcs:r,gcs:o}),e?o:r}function F(t,e=0,n=!1){const s=u.j.generate(t),r=n?t.horizonGcsGenerate():t.horizonPcsGenerate();if(!s||!r?.length)return null;let o=!1,i=r.find((t=>1===t.getInclusive()&&1===t.getKind()));if(!i){if(i=r.find((t=>1===t.getInclusive()&&0===t.getKind())),!i)return null;o=!0}const a=n?0:(2===s.getNorthPoleLocation()?1:0)|(2===s.getSouthPoleLocation()?2:0),l=s.isPannableRectangle(),c=i.getCoord();if(o)return{isEnvelope:o,isPannable:l,vertices:c,coef:null,bbox:[c[0][0]-e,c[0][1]-e,c[1][0]+e,c[1][1]+e],poleLocation:a};let p=0;const f=[];let[h,m]=c[0],[d,g]=c[0];for(let t=0,e=c.length;t<e;t++){p++,p===e&&(p=0);const[n,s]=c[t],[r,o]=c[p];if(o===s)f.push([n,r,s,o,2]);else{const t=(r-n)/(o-s||1e-4),e=n-t*s;s<o?f.push([t,e,s,o,0]):f.push([t,e,o,s,1])}h=h<n?h:n,m=m<s?m:s,d=d>n?d:n,g=g>s?g:s}return{isEnvelope:!1,isPannable:l,vertices:c,coef:f,bbox:[h,m,d,g],poleLocation:a}}function T(t,e){const n=[],{cols:s,rows:r,xres:o,yres:a,usePixelCenter:u}=t;let{xmin:l,ymax:c}=t;if(u&&(l+=o/2,c-=a/2),(0,i.Wi)(e)){for(let t=0;t<s;t++)for(let e=0;e<r;e++)n.push([l+o*t,c-a*e]);return{points:n}}const p=new Uint8Array(s*r);if(e.isEnvelope){const{bbox:[t,i,u,f]}=e;for(let h=0,m=0;h<s;h++){const s=l+o*h,d=e.isPannable||s>=t&&s<=u;for(let t=0;t<r;t++,m++){const e=c-a*t;d&&e>=i&&e<=f&&(n.push([s,e]),p[m]=1)}}return{points:n,mask:p}}const f=e.coef,h=[];for(let t=0;t<r;t++){const e=c-a*t,n=[],s=[];for(let t=0;t<f.length;t++){const[r,o,i,a,u]=f[t];if(e===i&&i===a)n.push(r),n.push(o),s.push(2),s.push(2);else if(e>=i&&e<=a){const t=r*e+o;n.push(t),s.push(u)}}let r=n;if(n.length>2){let t=2===s[0]?0:s[0],e=n[0];r=[];for(let o=1;o<s.length;o++)2===s[o]&&o!==s.length-1||(s[o]!==t&&(r.push(0===t?Math.min(e,n[o-1]):Math.max(e,n[o-1])),t=s[o],e=n[o]),o===s.length-1&&r.push(0===s[o]?Math.min(e,n[o]):Math.max(e,n[o])));r.sort(((t,e)=>t-e))}else n[0]>n[1]&&(r=[n[1],n[0]]);h.push(r)}for(let t=0,e=0;t<s;t++){const s=l+o*t;for(let t=0;t<r;t++,e++){const r=c-a*t,o=h[t];if(2===o.length)s>=o[0]&&s<=o[1]&&(n.push([s,r]),p[e]=1);else if(o.length>2){let t=!1;for(let e=0;e<o.length;e+=2)if(s>=o[e]&&s<=o[e+1]){t=!0;break}t&&(n.push([s,r]),p[e]=1)}}}return{points:n,mask:p}}function k(t){const e=B(t[0].spatialReference);if(t.length<2||(0,i.Wi)(e))return t[0];let{xmin:n,xmax:s,ymin:r,ymax:o}=t[0];for(let n=1;n<t.length;n++){const i=t[n];s=i.xmax+e*n,r=Math.min(r,i.ymin),o=Math.max(o,i.ymax)}return new c.Z({xmin:n,xmax:s,ymin:r,ymax:o,spatialReference:t[0].spatialReference})}function S(t,e,n=null,r=!0){const o=t.spatialReference;if(o.equals(e))return t;const a=G(t),u=B(o,!0),p=B(e);if(0===a||(0,i.Wi)(u)||(0,i.Wi)(p)){const a=M(t,e,n,r);if((0,i.Wi)(u)&&(0,i.pC)(p)&&Math.abs(a.width-p)<_(e)&&(0,l.kR)()){const n=I(o);if((0,i.pC)(n)&&n.poleLocation===s.None&&t.width<(n.bbox[2]-n.bbox[0])/2)return function(t,e){const n=B(e);if((0,i.Wi)(n))return null;let{xmin:s,ymin:r,xmax:o,ymax:a}=t;const u=t.spatialReference,p=new f.Z({spatialReference:u,rings:[[[s,r],[o,r],[o,a],[s,a],[s,r]]]}),h=(0,l.iV)(p,e);if(2!==h.rings.length||!h.rings[0].length||!h.rings[1].length)return null;const{rings:m}=h,d=_(u),g=new c.Z({spatialReference:e});for(let t=0;t<2;t++){s=o=m[t][0][0],r=a=m[t][0][1];for(let e=0;e<m[t].length;e++)s=s>m[t][e][0]?m[t][e][0]:s,o=o<m[t][e][0]?m[t][e][0]:o,r=r>m[t][e][1]?m[t][e][1]:r,a=a<m[t][e][1]?m[t][e][1]:a;if(0===t)g.ymin=r,g.ymax=a,g.xmin=s,g.xmax=o;else if(g.ymin=Math.min(g.ymin,r),g.ymax=Math.max(g.ymax,a),Math.abs(o-n/2)<d)g.xmin=s,g.xmax=g.xmax+n;else{if(!(Math.abs(s+n/2)<d))return null;g.xmax=o+n}}return g}(t,e)||a}return a}const h=t.clone().normalize();if(1===h.length&&t.xmax<u&&t.xmax-u/2>_(o)){const{xmin:e,xmax:n}=t;for(let s=0;s<=a;s++){const r=0===s?e:-u/2,i=s===a?n-u*s:u/2;h[s]=new c.Z({xmin:r,xmax:i,ymin:t.ymin,ymax:t.ymax,spatialReference:o})}}return k(h.map((t=>M(t,e,n,r))).filter(i.pC))}function M(t,e,n=null,s=!0,r=!0){const o=t.spatialReference;if(o.equals(e)||!e)return t;d(o,e,n);const a=(0,l.iV)(t,e,n);if(r&&e.isWebMercator&&a&&(a.ymax=Math.min(20037508.342787,a.ymax),a.ymin=Math.max(-20037508.342787,a.ymin),a.ymin>=a.ymax))return null;if(!s||!a)return a;const u=j(o,!0),c=j(e,!0);if((0,i.Wi)(u)||(0,i.Wi)(c))return a;const f=_(o,.001),h=_(o,w),m=_(e,.001);if(Math.abs(a.xmin-c[0])<m&&Math.abs(a.xmax-c[1])<m){const s=Math.abs(t.xmin-u[0]),r=Math.abs(u[1]-t.xmax);if(s<f&&r>h){a.xmin=c[0];const s=[];s.push(new p.Z(t.xmax,t.ymin,o)),s.push(new p.Z(t.xmax,(t.ymin+t.ymax)/2,o)),s.push(new p.Z(t.xmax,t.ymax,o));const r=s.map((t=>A(t,e,n))).filter((t=>!isNaN(t?.x))).map((t=>t.x));a.xmax=Math.max.apply(null,r)}if(r<f&&s>h){a.xmax=c[1];const s=[];s.push(new p.Z(t.xmin,t.ymin,o)),s.push(new p.Z(t.xmin,(t.ymin+t.ymax)/2,o)),s.push(new p.Z(t.xmin,t.ymax,o));const r=s.map((t=>A(t,e,n))).filter((t=>!isNaN(t?.x))).map((t=>t.x));a.xmin=Math.min.apply(null,r)}}else{const t=_(e,.001);Math.abs(a.xmin-c[0])<t&&(a.xmin=c[0]),Math.abs(a.xmax-c[1])<t&&(a.xmax=c[1])}return a}function B(t,e=!1){if(!t)return null;const n=e?20037508.342787:20037508.342788905;return t.isWebMercator?2*n:t.wkid&&t.isGeographic?360:2*y[t.wkid]||null}function j(t,e=!1){if(t.isGeographic)return[-180,180];const n=B(t,e);return(0,i.pC)(n)?[-n/2,n/2]:null}function E(t,e,n,s){let r=(t-e)/n;return r-Math.floor(r)!=0?r=Math.floor(r):s&&(r-=1),r}function G(t,e=!1){const n=B(t.spatialReference);if((0,i.Wi)(n))return 0;const s=e?0:-n/2,r=_(t.spatialReference),o=!e&&Math.abs(t.xmax-n/2)<r?n/2:t.xmax,a=!e&&Math.abs(t.xmin+n/2)<r?-n/2:t.xmin;return E(o,s,n,!0)-E(a,s,n,!1)}function D(t){const e=t.storageInfo.origin.x,n=B(t.spatialReference,!0);if((0,i.Wi)(n))return{originX:e,halfWorldWidth:null,pyramidsInfo:null};const s=n/2,{nativePixelSize:r,storageInfo:o,extent:a}=t,{maximumPyramidLevel:u,blockWidth:l,pyramidScalingFactor:c}=o;let p=r.x;const f=[],h=(0,i.pC)(t.transform)&&"gcs-shift"===t.transform.type,m=e+(h?0:s),d=h?n-e:s-e;for(let t=0;t<=u;t++){const t=(a.xmax-e)/p/l,n=t-Math.floor(t)==0?t:Math.ceil(t),s=d/p/l,r=s-Math.floor(s)==0?s:Math.ceil(s),o=Math.floor(m/p/l),i=Math.round(m/p)%l,u=(l-Math.round(d/p)%l)%l;f.push({resolutionX:p,blockWidth:l,datsetColumnCount:n,worldColumnCountFromOrigin:r,leftMargin:i,rightPadding:u,originColumnOffset:o}),p*=c}return{originX:e,halfWorldWidth:s,pyramidsInfo:f,hasGCSSShiftTransform:h}}function O(t){const e=t.isAdaptive&&null==t.spacing;let n=t.spacing||[32,32],s=z(t),r={cols:s.size[0]+1,rows:s.size[1]+1};const o=s.outofBoundPointCount>0&&s.outofBoundPointCount<s.offsets.length/2;let a=s.outofBoundPointCount===s.offsets.length/2||e&&o?[0,0]:g(s.offsets,r,n,4);const l=(a[0]+a[1])/2,c=t.projectedExtent.spatialReference,p=t.srcBufferExtent.spatialReference;if(e&&(o||l>4)&&(m(c,p,t.datumTransformation)&&(c.isGeographic||(0,i.pC)(I(c))),n=[4,4],s=z({...t,spacing:n}),r={cols:s.size[0]+1,rows:s.size[1]+1},a=g(s.offsets,r,n,4)),s.error=a,n[0]>1&&(s.coefficients=Z(s.offsets,r,o)),t.includeGCSGrid&&!c.isGeographic&&!c.isWebMercator)if(p.isGeographic)s.gcsGrid={offsets:s.offsets,coefficients:s.coefficients,spacing:n};else{const e=I(c);if((0,i.pC)(e)&&!e.isEnvelope){const e=function(t){if(!t||t.isGeographic)return t;const e=String(t.wkid||t.wkt);let n;return b.has(e)?n=b.get(e):(n=(t.wkid?u.e.coordsys(t.wkid):u.e.fromString(u.f.PE_TYPE_PROJCS,t.wkt)).getGeogcs().getCode(),b.set(e,n)),new h.Z({wkid:n})}(c),i=S(t.projectedExtent,e),{offsets:a}=z({...t,srcBufferExtent:i,spacing:n}),l=Z(a,r,o);s.gcsGrid={offsets:a,coefficients:l,spacing:n}}}return s}function z(t){const{projectedExtent:e,srcBufferExtent:n,pixelSize:s,datumTransformation:r,rasterTransform:o}=t,a=e.spatialReference,u=n.spatialReference,l=d(a,u),{xmin:c,ymin:f,xmax:h,ymax:m}=e,g=B(u),y=(0,i.pC)(g)&&(t.hasWrapAround||"gcs-shift"===o?.type),x=t.spacing||[32,32],b=x[0]*s.x,v=x[1]*s.y,C=1===x[0],A=Math.ceil((h-c)/b-.1/x[0])+(C?0:1),R=Math.ceil((m-f)/v-.1/x[1])+(C?0:1),N=P({cols:A,rows:R,xmin:c,ymax:m,xres:b,yres:v,inSR:a,outSR:u,datumTransformation:r,preferPE:x[0]<=4,usePixelCenter:C}),F=[];let T,k=0;const S=C?-1:NaN,{xmin:M,xmax:j,ymax:E,width:G,height:D}=n,O=_(u,w),z=(0,i.pC)(g)&&M>0&&j>g/2;let Z=!1;if(l){const t=I(a);Z=(0,i.pC)(t)&&t.poleLocation>0}for(let t=0;t<A;t++){const e=[];for(let n=0;n<R;n++){let s=N[t*R+n];if(y&&s[0]>j&&s[0]>g/2-O?s[0]-=g:y&&0===t&&s[0]<0&&z&&!o&&(s[0]+=g),!s||isNaN(s[0])||isNaN(s[1]))F.push(S),F.push(S),e.push(null),k++;else{if(o){const t=o.inverseTransform(new p.Z({x:s[0],y:s[1],spatialReference:u}));s=[t.x,t.y]}e.push(s),t>0&&y&&T[n]&&s[0]<T[n][0]&&(s[0]+=g,Z&&s[0]>j&&s[0]>g&&(s[0]-=g)),F.push((s[0]-M)/G),F.push((E-s[1])/D)}}T=e}return{offsets:F,error:null,coefficients:null,outofBoundPointCount:k,spacing:x,size:C?[A,R]:[A-1,R-1]}}function Z(t,e,n){const{cols:s,rows:r}=e,o=new Float32Array((s-1)*(r-1)*2*6),i=new Float32Array([-0,-1,1,-1,1,-0,1,-0,-0]),a=new Float32Array([-1,1,0,0,-1,1,1,0,0]);for(let e=0;e<s-1;e++){for(let n=0;n<r-1;n++){let u=e*r*2+2*n;const l=t[u],c=t[u+1],p=t[u+2],f=t[u+3];u+=2*r;const h=t[u],m=t[u+1],d=t[u+2],g=t[u+3];let y=0,x=12*(n*(s-1)+e);for(let t=0;t<3;t++)o[x++]=i[y++]*l+i[y++]*p+i[y++]*d;y=0;for(let t=0;t<3;t++)o[x++]=i[y++]*c+i[y++]*f+i[y++]*g;y=0;for(let t=0;t<3;t++)o[x++]=a[y++]*l+a[y++]*h+a[y++]*d;y=0;for(let t=0;t<3;t++)o[x++]=a[y++]*c+a[y++]*m+a[y++]*g}if(n)for(let t=0;t<o.length;t++)isNaN(o[t])&&(o[t]=-1)}return o}function V(t){const e=t.clone().normalize();return 1===e.length?e[0]:k(e)}function W(t,e,n){const{storageInfo:s,pixelSize:r}=e;let o=0,u=!1;const{pyramidResolutions:l}=s;if((0,i.pC)(l)&&l.length){const s=(t.x+t.y)/2,i=l[l.length-1],c=(i.x+i.y)/2,f=(r.x+r.y)/2;if(s<=f)o=0;else if(s>=c)o=l.length,u=s/c>8;else{let t,e=f;for(let r=1;r<=l.length;r++){if(t=(l[r-1].x+l[r-1].y)/2,s<=t){s===t?o=r:"down"===n?(o=r-1,u=s/e>8):o="up"===n||s-e>t-s||s/e>2?r:r-1;break}e=t}}const h=0===o?r:l[o-1];return u&&Math.min(h.x,h.y)*(0,a.c9)(e.spatialReference)>19567&&(u=!1),{pyramidLevel:o,pyramidResolution:new p.Z({x:h.x,y:h.y,spatialReference:e.spatialReference}),excessiveReading:u}}const c=Math.log(t.x/r.x)/Math.LN2,f=Math.log(t.y/r.y)/Math.LN2,h=e.storageInfo.maximumPyramidLevel||0;o="down"===n?Math.floor(Math.min(c,f)):"up"===n?Math.ceil(Math.max(c,f)):Math.round((c+f)/2),o<0?o=0:o>h&&(u=o>h+3,o=h);const m=2**o;return{pyramidLevel:o,pyramidResolution:new p.Z({x:m*e.nativePixelSize.x,y:m*e.nativePixelSize.y,spatialReference:e.spatialReference}),excessiveReading:u}}},48700:(t,e,n)=>{n.d(e,{Z:()=>u});var s=n(43697),r=n(96674),o=n(5600),i=(n(75215),n(67676),n(52011));let a=class extends r.wq{get affectsPixelSize(){return!1}forwardTransform(t){return t}inverseTransform(t){return t}};(0,s._)([(0,o.Cb)()],a.prototype,"affectsPixelSize",null),(0,s._)([(0,o.Cb)({json:{write:!0}})],a.prototype,"spatialReference",void 0),a=(0,s._)([(0,i.j)("esri.layers.support.rasterTransforms.BaseRasterTransform")],a);const u=a},29680:(t,e,n)=>{n.d(e,{Z:()=>l});var s=n(43697),r=n(5600),o=(n(75215),n(67676),n(36030)),i=n(52011),a=n(48700);let u=class extends a.Z{constructor(){super(...arguments),this.type="gcs-shift",this.tolerance=1e-8}forwardTransform(t){return"point"===(t=t.clone()).type?(t.x>180+this.tolerance&&(t.x-=360),t):(t.xmin>=180-this.tolerance?(t.xmax-=360,t.xmin-=360):t.xmax>180+this.tolerance&&(t.xmin=-180,t.xmax=180),t)}inverseTransform(t){return"point"===(t=t.clone()).type?(t.x<-this.tolerance&&(t.x+=360),t):(t.xmin<-this.tolerance&&(t.xmin+=360,t.xmax+=360),t)}};(0,s._)([(0,o.J)({GCSShiftXform:"gcs-shift"})],u.prototype,"type",void 0),(0,s._)([(0,r.Cb)()],u.prototype,"tolerance",void 0),u=(0,s._)([(0,i.j)("esri.layers.support.rasterTransforms.GCSShiftTransform")],u);const l=u},87390:(t,e,n)=>{n.d(e,{Z:()=>d});var s=n(43697),r=(n(66577),n(5600)),o=(n(75215),n(67676),n(36030)),i=n(71715),a=n(52011),u=n(30556),l=n(48700),c=n(94139),p=n(6570);function f(t,e,n){const{x:s,y:r}=e;if(n<2)return{x:t[0]+s*t[2]+r*t[4],y:t[1]+s*t[3]+r*t[5]};if(2===n){const e=s*s,n=r*r,o=s*r;return{x:t[0]+s*t[2]+r*t[4]+e*t[6]+o*t[8]+n*t[10],y:t[1]+s*t[3]+r*t[5]+e*t[7]+o*t[9]+n*t[11]}}const o=s*s,i=r*r,a=s*r,u=o*s,l=o*r,c=s*i,p=r*i;return{x:t[0]+s*t[2]+r*t[4]+o*t[6]+a*t[8]+i*t[10]+u*t[12]+l*t[14]+c*t[16]+p*t[18],y:t[1]+s*t[3]+r*t[5]+o*t[7]+a*t[9]+i*t[11]+u*t[13]+l*t[15]+c*t[17]+p*t[19]}}function h(t,e,n){const{xmin:s,ymin:r,xmax:o,ymax:i,spatialReference:a}=e;let u=[];if(n<2)u.push({x:s,y:i}),u.push({x:o,y:i}),u.push({x:s,y:r}),u.push({x:o,y:r});else{let t=10;for(let e=0;e<t;e++)u.push({x:s,y:r+(i-r)*e/(t-1)}),u.push({x:o,y:r+(i-r)*e/(t-1)});t=8;for(let e=1;e<=t;e++)u.push({x:s+(o-s)*e/t,y:r}),u.push({x:s+(o-s)*e/t,y:i})}u=u.map((e=>f(t,e,n)));const l=u.map((t=>t.x)),c=u.map((t=>t.y));return new p.Z({xmin:Math.min.apply(null,l),xmax:Math.max.apply(null,l),ymin:Math.min.apply(null,c),ymax:Math.max.apply(null,c),spatialReference:a})}let m=class extends l.Z{constructor(){super(...arguments),this.polynomialOrder=1,this.type="polynomial"}readForwardCoefficients(t,e){const{coeffX:n,coeffY:s}=e;if(!n?.length||!s?.length||n.length!==s.length)return null;const r=[];for(let t=0;t<n.length;t++)r.push(n[t]),r.push(s[t]);return r}writeForwardCoefficients(t,e,n){const s=[],r=[];for(let e=0;e<t?.length;e++)e%2==0?s.push(t[e]):r.push(t[e]);e.coeffX=s,e.coeffY=r}get inverseCoefficients(){let t=this._get("inverseCoefficients");const e=this._get("forwardCoefficients");return!t&&e&&this.polynomialOrder<2&&(t=function(t){const[e,n,s,r,o,i]=t,a=s*i-o*r,u=o*r-s*i;return[(o*n-e*i)/a,(s*n-e*r)/u,i/a,r/u,-o/a,-s/u]}(e)),t}set inverseCoefficients(t){this._set("inverseCoefficients",t)}readInverseCoefficients(t,e){const{inverseCoeffX:n,inverseCoeffY:s}=e;if(!n?.length||!s?.length||n.length!==s.length)return null;const r=[];for(let t=0;t<n.length;t++)r.push(n[t]),r.push(s[t]);return r}writeInverseCoefficients(t,e,n){const s=[],r=[];for(let e=0;e<t?.length;e++)e%2==0?s.push(t[e]):r.push(t[e]);e.inverseCoeffX=s,e.inverseCoeffY=r}get affectsPixelSize(){return this.polynomialOrder>0}forwardTransform(t){if("point"===t.type){const e=f(this.forwardCoefficients,t,this.polynomialOrder);return new c.Z({x:e.x,y:e.y,spatialReference:t.spatialReference})}return h(this.forwardCoefficients,t,this.polynomialOrder)}inverseTransform(t){if("point"===t.type){const e=f(this.inverseCoefficients,t,this.polynomialOrder);return new c.Z({x:e.x,y:e.y,spatialReference:t.spatialReference})}return h(this.inverseCoefficients,t,this.polynomialOrder)}};(0,s._)([(0,r.Cb)({json:{write:!0}})],m.prototype,"polynomialOrder",void 0),(0,s._)([(0,r.Cb)()],m.prototype,"forwardCoefficients",void 0),(0,s._)([(0,i.r)("forwardCoefficients",["coeffX","coeffY"])],m.prototype,"readForwardCoefficients",null),(0,s._)([(0,u.c)("forwardCoefficients")],m.prototype,"writeForwardCoefficients",null),(0,s._)([(0,r.Cb)({json:{write:!0}})],m.prototype,"inverseCoefficients",null),(0,s._)([(0,i.r)("inverseCoefficients",["inverseCoeffX","inverseCoeffY"])],m.prototype,"readInverseCoefficients",null),(0,s._)([(0,u.c)("inverseCoefficients")],m.prototype,"writeInverseCoefficients",null),(0,s._)([(0,r.Cb)()],m.prototype,"affectsPixelSize",null),(0,s._)([(0,o.J)({PolynomialXform:"polynomial"})],m.prototype,"type",void 0),m=(0,s._)([(0,a.j)("esri.layers.support.rasterTransforms.PolynomialTransform")],m);const d=m},87521:(t,e,n)=>{n.d(e,{j:()=>h,c:()=>m});var s=n(29680),r=n(43697),o=(n(92604),n(75215),n(67676),n(20102),n(80442),n(36030)),i=n(52011),a=n(48700);let u=class extends a.Z{constructor(){super(...arguments),this.type="identity"}};(0,r._)([(0,o.J)({IdentityXform:"identity"})],u.prototype,"type",void 0),u=(0,r._)([(0,i.j)("esri.layers.support.rasterTransforms.IdentityTransform")],u);const l=u;var c=n(87390);const p={GCSShiftXform:s.Z,IdentityXform:l,PolynomialXform:c.Z},f=Object.keys(p);function h(t){const e=t?.type;return!t||f.includes(e)}function m(t){const e=t?.type;if(!e)return null;const n=p[t?.type];if(n){const e=new n;return e.read(t),e}return null}}}]);