package org.thingsboard.server.dao.stationSetting;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationCategoryListRequest;
import org.thingsboard.server.dao.model.sql.stationSetting.StationCategory;

import java.util.List;

public interface StationCategoryService {
    void save(StationCategory entity);

    PageData<StationCategory> findList(StationCategoryListRequest request, TenantId tenantId);

    void remove(List<String> ids);

    List<StationCategory> findAll(TenantId tenantId);
}
