<template>
  <div class="content">
    <!-- <div class="content_bg"></div> -->
    <!-- top -->
    <div class="content_top">
      <PieCharts
        v-for="(item, i) in dbglData"
        :key="i"
        :config="item"
      />
    </div>
    <!-- center -->
    <div class="content_center">
      <TitleCard
        :title="'生产概况'"
        class="box-top"
      >
        <SCGK></SCGK>
      </TitleCard>
      <TitleCard
        :title="'管网概况'"
        class="box-top"
      >
        <GWGK></GWGK>
      </TitleCard>

      <TitleCard
        :title="'营收概况'"
        class="box-top"
      >
        <YSGK></YSGK>
      </TitleCard>
      <TitleCard
        :title="'客服概况'"
        class="box-top"
      >
        <KFGK></KFGK>
      </TitleCard>
    </div>
    <!-- bottom -->
    <div class="content_bottom">
      <TitleCard
        :title="'水质合格北'"
        class="box-bottom"
      >
        <SZHGL></SZHGL>
      </TitleCard>
      <TitleCard
        :title="'漏损构成'"
        class="box-bottom"
      >
        <LSGC></LSGC>
      </TitleCard>
      <TitleCard
        :title="'售水量'"
        class="box-bottom"
      >
        <SSL></SSL>
      </TitleCard>
      <TitleCard
        :title="'服务类型'"
        class="box-bottom"
      >
        <FWLX></FWLX>
      </TitleCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { dbglData } from '../smartDecisionData'
import PieCharts from './components/PieCharts.vue'
import SCGK from './components/SCGK.vue'
import GWGK from './components/GWGK.vue'
import YSGK from './components/YSGK.vue'
import KFGK from './components/KFGK.vue'
import SZHGL from './components/SZHGL.vue'
import LSGC from './components/LSGC.vue'
import SSL from './components/SSL.vue'
import FWLX from './components/FWLX.vue'
import TitleCard from '../components/TitleCard.vue'
</script>

<style lang="scss" scoped>
.content {
  position: absolute;
  // height: 100%;
  width: 100%;
  padding: 100px 50px 15px;
  background: url(../imgs/bg_blue.png) 0 0 / 100% 100% no-repeat;
  color: #fff;

  .content_center,
  .content_top,
  .content_bottom {
    display: flex;
    justify-content: space-between;
  }
  .content_top {
    margin-bottom: 12px;
  }
  .box-top {
    height: 480px;
    width: calc(25% - 6px);
    margin-bottom: 12px;
  }
  .box-bottom {
    height: 300px;
    width: calc(25% - 6px);
  }
}
</style>
