package org.thingsboard.server.controller.store;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.store.StoreService;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/store")
public class StoreController extends BaseController {
    @Autowired
    private StoreService service;


    @GetMapping
    public IstarResponse findAllConditional(StorePageRequest request) {
        return IstarResponse.ok(service.findAllConditional(request));
    }

    @PostMapping
    public Store save(@RequestBody StoreSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody StoreSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (!service.canBeDelete(id)) {
            ExceptionUtils.silentThrow("检测到有关联数据，禁止删除");
        }
        return service.delete(id);
    }
}