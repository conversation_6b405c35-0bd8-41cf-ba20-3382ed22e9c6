package org.thingsboard.server.dao.util.imodel.response.model;

import org.apache.ibatis.session.SqlSession;
import org.springframework.jdbc.core.JdbcTemplate;
import org.thingsboard.server.dao.util.imodel.response.DepartmentInfo;

public interface JdbcHelper {
    JdbcTemplate getJDBCTemplate();

    String resolveUsername(String id);

    String resolveTenantName(String id);

    DepartmentInfo resolveDepartmentInfoByUserId(String userId);

    DepartmentInfo resolveDepartmentInfo(String departmentId);

    SqlSession getSqlSession();

    <T> T getMapper(Class<T> mapperType);
}
