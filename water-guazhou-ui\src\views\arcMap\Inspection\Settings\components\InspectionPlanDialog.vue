<template>
  <el-dialog
    v-model="visible"
    :title="getDialogTitle()"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 数据加载中的骨架屏 -->
    <div v-if="dataLoading" class="loading-skeleton">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 表单内容 -->
    <el-form
      v-else
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="巡检计划名称" prop="planName">
            <el-input v-model="formData.planName" placeholder="请输入巡检计划名称" :readonly="readonly" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="巡检类型" prop="inspectionType">
            <el-select v-model="formData.inspectionType" placeholder="请选择巡检类型" style="width: 100%" :disabled="readonly">
              <el-option label="管网巡检" value="pipeline" />
              <el-option label="泵站巡检" value="pumpStation" />
              <el-option label="其他巡检" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="巡检周期" prop="inspectionCycle">
            <el-select v-model="formData.inspectionCycle" placeholder="请选择巡检周期" style="width: 100%" :disabled="readonly">
              <el-option label="每周一、三、五日执行" value="weekly_135" />
              <el-option label="每月5、15、25日执行" value="monthly_51525" />
              <el-option label="每季度第一个月10日执行" value="quarterly_10" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行角色" prop="executionRole">
            <el-select v-model="formData.executionRole" placeholder="请选择执行角色" style="width: 100%" :disabled="readonly">
              <el-option label="运维组长、设备专员" value="maintenance_equipment" />
              <el-option label="巡检员、设备专员" value="inspector_equipment" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检查表模板" prop="checklistTemplate">
            <el-select v-model="formData.checklistTemplate" placeholder="请选择检查表模板" style="width: 100%" :disabled="readonly" filterable>
              <el-option
                v-for="template in checklistTemplates"
                :key="template.value"
                :label="template.label"
                :value="template.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%" :disabled="readonly">
              <el-option label="启用" value="1" />
              <el-option label="停用" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          :readonly="readonly"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ readonly ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!readonly" type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { addInspectionPlan, updateInspectionPlan, getInspectionPlanById, getChecklistTemplateList } from '@/api/CircuitSettings/inspectionPlan'

interface Props {
  modelValue: boolean
  editId?: string
  readonly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editId: '',
  readonly: false
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const dataLoading = ref(false) // 数据加载状态
const submitLoading = ref(false) // 提交加载状态
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 检查表模板选项
const checklistTemplates = ref([
  { label: '设备运行状态检查表', value: 'equipment_status' },
  { label: '管网压力检查表', value: 'pipeline_pressure' },
  { label: '泵站运行检查表', value: 'pump_station' },
  { label: '水质检测检查表', value: 'water_quality' },
  { label: '安全隐患检查表', value: 'safety_hazard' }
])

const formData = reactive({
  planName: '',
  inspectionType: '',
  inspectionCycle: '',
  executionRole: '',
  checklistTemplate: '',
  status: '1',
  remark: ''
})

const rules: FormRules = {
  planName: [
    { required: true, message: '请输入巡检计划名称', trigger: 'blur' }
  ],
  inspectionType: [
    { required: true, message: '请选择巡检类型', trigger: 'change' }
  ],
  inspectionCycle: [
    { required: true, message: '请选择巡检周期', trigger: 'change' }
  ],
  executionRole: [
    { required: true, message: '请选择执行角色', trigger: 'change' }
  ],
  checklistTemplate: [
    { required: true, message: '请输入检查表模板', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    isEdit.value = !!props.editId
    if (isEdit.value) {
      await loadEditData()
    } else {
      resetForm()
    }
  }
})

// 监听弹窗关闭
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取弹窗标题
const getDialogTitle = () => {
  if (props.readonly) {
    return '查看巡检计划'
  }
  return isEdit.value ? '编辑巡检计划' : '新建巡检计划'
}

// 重置表单
const resetForm = () => {
  formData.planName = ''
  formData.inspectionType = ''
  formData.inspectionCycle = ''
  formData.executionRole = ''
  formData.checklistTemplate = ''
  formData.status = '1'
  formData.remark = ''
  // 使用 nextTick 确保 DOM 更新后再清除验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 加载编辑数据
const loadEditData = async () => {
  if (!props.editId) return

  try {
    dataLoading.value = true
    const res = await getInspectionPlanById(props.editId)
    if (res?.data) {
      // 处理 IstarResponse 包装的数据格式
      const responseData = res.data.data || res.data
      // 确保数据完整性，避免undefined值
      formData.planName = responseData.planName || ''
      formData.inspectionType = responseData.inspectionType || ''
      formData.inspectionCycle = responseData.inspectionCycle || ''
      formData.executionRole = responseData.executionRole || ''
      formData.checklistTemplate = responseData.checklistTemplate || ''
      formData.status = responseData.status || '1'
      formData.remark = responseData.remark || ''
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    dataLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      await updateInspectionPlan(props.editId, formData)
      ElMessage.success('更新成功')
    } else {
      await addInspectionPlan(formData)
      ElMessage.success('创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error(error)
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 加载检查表模板
const loadChecklistTemplates = async () => {
  try {
    const res = await getChecklistTemplateList()
    if (res?.data) {
      const responseData = res.data.data || res.data
      if (Array.isArray(responseData)) {
        checklistTemplates.value = responseData.map((item: any) => ({
          label: item.name || item.label,
          value: item.id || item.value
        }))
      }
    }
  } catch (error) {
    console.error('加载检查表模板失败:', error)
    // 使用默认模板
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 重置所有状态
  dataLoading.value = false
  submitLoading.value = false
  resetForm()
}

// 组件挂载时加载模板
onMounted(() => {
  loadChecklistTemplates()
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.loading-skeleton {
  padding: 20px 0;
}
</style>
