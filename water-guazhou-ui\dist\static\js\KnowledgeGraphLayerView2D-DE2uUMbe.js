import{e as i,y as a,a as s}from"./Point-WxyopZva.js";import{bk as o,bV as n,bW as l}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import{f as h,u as d}from"./LayerView-BSt9B8Gh.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";let t=class extends h(d){constructor(e){super(e),this.layerViews=new o}set layerViews(e){this._set("layerViews",n(e,this._get("layerViews")))}get updatingProgress(){return this.layerViews.length===0?1:this.layerViews.reduce((e,r)=>e+r.updatingProgress,0)/this.layerViews.length}attach(){this._updateStageChildren(),this.addAttachHandles(this.layerViews.on("after-changes",()=>this._updateStageChildren()))}detach(){this.container.removeAllChildren()}update(e){}moveStart(){}viewChange(){}moveEnd(){}_updateStageChildren(){this.container.removeAllChildren(),this.layerViews.forEach((e,r)=>this.container.addChildAt(e.container,r))}};i([a({cast:l})],t.prototype,"layerViews",null),i([a({readOnly:!0})],t.prototype,"updatingProgress",null),t=i([s("esri.views.2d.layers.KnowledgeGraphLayerView2D")],t);const v=t;export{v as default};
