package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import org.jetbrains.annotations.NotNull;

/**
 * 话务日报
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-19
 */
@Data
public class CallDailyDTO implements Comparable {

    private Integer no;

    private String day;

    private String seatsId;

    private String seatsName;

    private Integer inCount;

    private Long inDuration;

    private Integer outCount;

    private Long outDuration;

    private Integer conversationCount;

    private Long conversationDuration;

    private Long avgConversationDuration;

    public CallDailyDTO() {

    }

    public CallDailyDTO(String seatsId, String seatsName) {
        this.seatsId = seatsId;
        this.seatsName = seatsName;
        this.inCount = 0;
        this.inDuration = 0L;
        this.outCount = 0;
        this.outDuration = 0L;
        this.conversationDuration = 0L;
        this.conversationCount = 0;
        this.avgConversationDuration = 0L;
    }

    @Override
    public int compareTo(@NotNull Object o) {
        return Integer.compare(Integer.valueOf(((CallDailyDTO) o).getDay()), Integer.valueOf(this.getDay()));
    }
}
