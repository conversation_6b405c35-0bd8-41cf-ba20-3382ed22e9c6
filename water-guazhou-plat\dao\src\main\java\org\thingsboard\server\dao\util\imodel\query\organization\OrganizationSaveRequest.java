package org.thingsboard.server.dao.util.imodel.query.organization;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class OrganizationSaveRequest extends SaveRequest<Organization> {

    // "供水单位名称"
    private String name;

    // 单位类型
    private String type;

    // 联系电话
    private String phone;

    // 父级ID
    private String parentId;

    // 排序，升序
    @NotNullOrEmpty
    private Integer orderNum;

    // 位置信息
    private String location;

    // 样式信息
    private String styleInformation;

    // 水厂类型（0水源地，1供水厂，2污水厂，3其他）
    private String orgType;

    @Override
    protected Organization build() {
        Organization organization = new Organization();
        organization.setParentId(parentId);
        organization.setName(name);
        organization.setType(type);
        organization.setPhone(phone);
        organization.setOrderNum(orderNum);
        organization.setLocation(location);
        organization.setStyleInformation(styleInformation);
        organization.setOrgType(orgType);
        organization.setCreateTime(createTime());
        organization.setTenantId(tenantId());
        return organization;
    }

    @Override
    protected Organization update(String id) {
        Organization organization = new Organization();
        organization.setId(id);
        organization.setParentId(parentId);
        organization.setName(name);
        organization.setType(type);
        organization.setPhone(phone);
        organization.setOrderNum(orderNum);
        organization.setLocation(location);
        organization.setStyleInformation(styleInformation);
        organization.setOrgType(orgType);
        return organization;
    }
}
