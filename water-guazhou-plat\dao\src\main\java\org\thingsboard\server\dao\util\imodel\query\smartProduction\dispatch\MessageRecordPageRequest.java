package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class MessageRecordPageRequest extends AdvancedPageableQueryEntity<MessageRecord, MessageRecordPageRequest> {
    // 消息内容，模糊
    private String content;

}
