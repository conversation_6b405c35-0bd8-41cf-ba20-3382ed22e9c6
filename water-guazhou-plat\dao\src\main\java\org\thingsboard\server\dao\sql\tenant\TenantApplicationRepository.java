package org.thingsboard.server.dao.sql.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;

import java.util.List;

public interface TenantApplicationRepository extends JpaRepository<TenantApplicationEntity, String> {

    @Query("SELECT ta FROM TenantApplicationEntity ta WHERE ta.tenantId = ?1 ORDER BY ta.orderNum DESC, ta.createTime ASC")
    Page<TenantApplicationEntity> findList(String tenantId, Pageable pageable);

    @Query("SELECT ta FROM TenantApplicationEntity ta WHERE ta.tenantId = ?1 ORDER BY ta.orderNum DESC, ta.createTime ASC")
    List<TenantApplicationEntity> findByTenantId(String tenantId);

    @Query("SELECT ta FROM TenantApplicationEntity ta WHERE ta.id IN ?1 ORDER BY ta.orderNum DESC, ta.createTime ASC")
    List<TenantApplicationEntity> findByIdIn(List<String> idList);

    @Query("SELECT ta FROM TenantApplicationEntity ta WHERE ta.tenantId = ?1 AND ta.resourceType = ?2 ORDER BY ta.orderNum DESC, ta.createTime ASC")
    List<TenantApplicationEntity> findByTenantIdAndResourceType(String tenantId, String resourceType);

    @Query("SELECT ta FROM TenantApplicationEntity ta WHERE ta.id IN ?1 AND ta.resourceType = ?2 ORDER BY ta.orderNum DESC, ta.createTime ASC")
    List<TenantApplicationEntity> findByIdInAndResourceType(List<String> idList, String resourceType);
}
