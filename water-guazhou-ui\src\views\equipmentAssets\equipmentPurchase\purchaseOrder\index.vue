<!-- 设备采购-采购单 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refFormDetail" :config="detailDialog"></SLDrawer>
    <SLDrawer ref="refForm" :config="addOrUpdateConfig">
    </SLDrawer>
    <SLDrawer ref="refForm1" :config="addEquipment">
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { ICONS } from '@/common/constans/common'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import {
  getDevicePurchaseSearch,
  getDevicePurchaseItem,
  postDevicePurchase,
  deleteDevicePurchase
} from '@/api/equipment_assets/equipmentPurchase'
import { getDeviceListSearch } from '@/api/equipment_assets/equipmentManage'
import { uniqueFunc } from '@/utils/GlobalHelper'
import { formatDate } from '@/utils/DateFormatter'

const { $btnPerms } = useGlobal()
const refSearch = ref<ICardSearchIns>()
const refForm = ref<ISLDrawerIns>()
const refForm1 = ref<ISLDrawerIns>()
const refFormDetail = ref<ISLDrawerIns>()
const chosen = ref([])

// 搜索
const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '采购单编码', field: 'code', type: 'input', labelWidth: '90px' },
    { label: '采购单标题', field: 'title', type: 'input', labelWidth: '90px' },
    { type: 'department-user', label: '请购人', field: 'userId' },
    { label: '请购时间', field: 'time', type: 'daterange', format: 'YYYY-MM-DD' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', icon: ICONS.QUERY, click: () => refreshData() },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        { type: 'success', perm: $btnPerms('RoleManageAdd'), text: '新建采购单', icon: ICONS.ADD, click: () => clickCreatedRole() }
      ]
    }
  ]
})

// 表格
const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '采购单标题', prop: 'title' },
    { label: '采购单编码', prop: 'code' },
    { label: '采购部门名称', prop: 'userDepartmentName' },
    { label: '请购人', prop: 'userName' },
    { label: '用途', prop: 'useWay' },
    {
      label: '申请时间',
      prop: 'preTime',
      formatter: row => formatDate(row.preTime, 'YYYY-MM-DD')
    },
    { label: '采购单创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: row => formatDate(row.createTime, 'YYYY-MM-DD')
    }
  ],
  operations: [
    {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => openDetailDialog(row)
    }, {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => clickEditdRole(row)
    },
    {
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      icon: 'iconfont icon-shanchu',
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 新建采购单
const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新建采购单',
  labelWidth: '130px',
  submit: (params: any, status?: boolean) => {
    if (status) {
      refForm1.value?.openDrawer()
      data.getDevice()
      return
    }

    if (!params.id && TableConfig.dataList.some(element => element.code === params.code)) {
      ElMessage.warning('采购单编码重复')
      return
    }
    if (!params.items.some(item => item.num && item.inquiryEndTime)) {
      ElMessage.warning('询价时间未填写')
      return
    }
    let val = '新增成功'
    if (params.id) {
      val = '修改成功'
    }
    addOrUpdateConfig.submitting = true
    postDevicePurchase(params).then(() => {
      refreshData()
      ElMessage.success(val)
      refForm.value?.closeDrawer()
      addOrUpdateConfig.submitting = false
    })
  },

  defaultValue: { addRecord: false },
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '采购单标题',
          field: 'title',
          rules: [{ required: true, message: '请输入采购单标题' }]
        },
        {
          xl: 8,
          type: 'department-user',
          label: '请购人',
          field: 'userId',
          rules: [{ required: true, message: '请输入请购人' }]
        },
        {
          readonly: true,
          xl: 8,
          type: 'input',
          label: '采购单编码',
          field: 'code',
          rules: [{ required: true, message: '请输入采购单编码' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '用途',
          field: 'useWay'
        },
        {
          xl: 8,
          type: 'input',
          label: '预算',
          field: 'budget',
          rules: [{ required: true, message: '请输入预算' }]
        },
        {
          xl: 8,
          type: 'date',
          label: '申请时间',
          field: 'uploadTime',
          format: 'YYYY-MM-DD HH:mm:ss',
          rules: [{ required: true, message: '请输入申请时间' }]
        },
        {
          xl: 8,
          type: 'date',
          label: '计划采购时间',
          field: 'preTime',
          format: 'YYYY-MM-DD HH:mm:ss',
          rules: [{ required: true, message: '请输入计划采购时间' }]
        },
        {
          xl: 8,
          type: 'switch',
          label: '是否补录',
          field: 'addRecord'
        },
        {
          type: 'table',
          field: 'items',
          config: {
            indexVisible: true,
            height: '350px',
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加设备',
                        perm: true,
                        click: () => {
                          refForm1.value?.openDrawer()
                          data.getDevice({})
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            dataList: computed(() => data.selectList) as any,
            columns: [
              { label: '设备编号', prop: 'serialId' },
              { label: '设备名称', prop: 'name' },
              { label: '规格型号', prop: 'model' },
              { label: '所属大类', prop: 'topType' },
              { label: '所属类别', prop: 'linkedType' },
              {
                label: '购买数量',
                prop: 'num',
                formItemConfig: {
                  type: 'number',
                  min: 0
                }
              },
              {
                label: '询价时间',
                prop: 'inquiryEndTime',
                tableDataName: 'items',
                formItemConfig: {
                  type: 'date',
                  field: 'inquiryEndTime',
                  rules: [{ required: true, message: '请选择询价时间' }],
                  format: 'YYYY-MM-DD HH:mm:ss'
                }
              }
            ],
            operations: [
              {
                text: '移除',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: row => {
                  data.selectList = data.selectList.filter(
                    item => item.id !== row.id
                  )
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDialogFormConfig>({
  title: '设备选择',
  submit: (params: any, status?: boolean) => {
    // 搜索处理
    if (status) {
      data.getDevice(params)
    } else {
      data.selectList = [...data.selectList, ...chosen.value]
      data.selectList = uniqueFunc(data.selectList, 'id')
      data.selectList = data.selectList.map(item => {
        if (!item.num || item.num === null) { item.num = '0' }
        return item
      })
      refForm1.value?.closeDrawer()
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '设备编码',
          field: 'serialId'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '搜索',
                        perm: true,
                        click: () => {
                          refForm1.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备型号',
                prop: 'model'
              },
              {
                label: '可用年限',
                prop: 'useYear'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 详情
const detailDialog = reactive<IDialogFormConfig>({
  title: '详情',
  labelWidth: '130px',
  print: true,
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '采购单编码',
          field: 'code'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '采购单标题',
          field: 'title'
        },
        {
          xl: 8,
          readonly: true,
          type: 'date',
          label: '计划采购日期',
          field: 'uploadTime'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '用途',
          field: 'useWay'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '预算',
          field: 'budget'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '请购部门',
          field: 'userDepartmentName'
        },
        {
          xl: 8,
          disabled: true,
          type: 'input',
          label: '请购人',
          field: 'userName'
        },
        {
          type: 'table',
          field: 'table',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.DevicePurchaseItem || []) as any,
            columns: [
              { label: '设备编号', prop: 'serialId' },
              { label: '设备名称', prop: 'name' },
              { label: '规格型号', prop: 'model' },
              { label: '所属大类', prop: 'topType' },
              { label: '所属类别', prop: 'type' },
              { label: '购买数量', prop: 'num' },
              {
                label: '要求完成询价时间',
                prop: 'inquiryEndTime',
                formatter: row => formatDate(row.inquiryEndTime, 'YYYY-MM-DD')
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '添加'
  data.selectList = []
  chosen.value = []
  addOrUpdateConfig.defaultValue = {
    code: 'CGD' + moment(new Date()).format('YYYYMMDDHHmmss'),
    addRecord: false
  }
  refForm.value?.openDrawer()
}

const clickEditdRole = (row) => {
  addOrUpdateConfig.title = '添加'
  data.selectList = []
  chosen.value = []
  addOrUpdateConfig.defaultValue = {
    ...row
  }
  data.getDevicePurchaseItemValue(row.id)
  refForm.value?.openDrawer()
}

// 打开详情
const openDetailDialog = (row: { [x: string]: any }) => {
  for (const i in row) {
    if (row[i] === null) {
      row[i] = ' '
    }
  }
  addOrUpdateConfig.title = '详情'
  detailDialog.defaultValue = { ...(row || {}) }
  data.getDevicePurchaseItemValue(row.id)
  refFormDetail.value?.openDrawer()
}

const haneleDelete = (row: { id: string }) => {
  SLConfirm('确定删除该采购单, 是否继续?', '删除提示').then(() => {
    deleteDevicePurchase(row.id).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    }).catch(error => {
      ElMessage.warning(error)
    })
  })
}

const data = reactive({
  // 请购条目
  DevicePurchaseItem: [],
  // 设备列表
  deviceValue: [],
  // 当前选中列表
  selectList: [] as any[],
  getDevice: (param?: any) => {
    const params = {
      size: 9999,
      page: 1,
      ...param
    }
    delete params.device
    getDeviceListSearch(params).then(res => {
      data.deviceValue = res.data.data.data || []
    })
  },
  getDevicePurchaseItemValue: (id: string) => {
    const params = {
      mainId: id,
      page: 1,
      size: 20
    }
    getDevicePurchaseItem(params).then(res => {
      data.selectList = res.data.data.data || []
    })
  }
})

const refreshData = async () => {
  const params: {
    size: number | undefined;
    page: number | undefined;
    fromTime?: string;
    toTime?: string;
    department?: string;
    time?: string;
  } = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  if (params.time && params.time?.length > 1) {
    params.fromTime = (refSearch.value?.queryParams as any).time[0] || ''
    params.toTime = (refSearch.value?.queryParams as any).time[1] || ''
  }
  delete params.time
  getDevicePurchaseSearch(params)
    .then(res => {
      TableConfig.dataList = res.data.data.data || []
      TableConfig.pagination.total = res.data.data.total || 0
    })
    .catch(() => {
      ElMessage.error('获取数据失败')
    })
}

onMounted(() => {
  refreshData()
})
</script>
