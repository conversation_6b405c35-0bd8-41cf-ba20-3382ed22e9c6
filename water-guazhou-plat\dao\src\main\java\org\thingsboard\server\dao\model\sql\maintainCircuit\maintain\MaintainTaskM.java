package org.thingsboard.server.dao.model.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 保养任务主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@TableName("tb_device_maintain_task_m")
@Data
public class MaintainTaskM {
    @TableId
    private String id;

    private String code;

    private String name;

    private String type;

    private String teamId;

    private transient String teamName;

    private String userId;

    private transient String userName;

    private Date startTime;

    private Date endTime;

    private Date realStartTime;

    private Date realEndTime;

    private String remark;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private String status;

    private String auditStatus;

    private String auditRemark;

    private Date auditTime;

    private String auditor;

    private transient String auditorName;

    private transient String auditorDepartment;

    private transient String auditorDepartmentName;

    private transient List<MaintainTaskC> maintainTaskCList;

    private String tenantId;

}
