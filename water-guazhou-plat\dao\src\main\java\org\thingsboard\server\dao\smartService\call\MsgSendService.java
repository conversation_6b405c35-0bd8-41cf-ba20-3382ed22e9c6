package org.thingsboard.server.dao.smartService.call;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.MsgSend;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface MsgSendService {
    PageData getList(String receivePhone, String keywords, String status, Long start, Long end, int page, int size, String tenantId);

    MsgSend save(MsgSend msgSend);

    int delete(List<String> ids);

    IstarResponse resend(String id);
}
