package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionCust;
import org.thingsboard.server.dao.smartPipe.PipePartitionCustService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 智慧管网-营收用户管理
 */
@RestController
@RequestMapping("api/spp/partitionCust")
public class PipePartitionCustController extends BaseController {

    @Autowired
    private PipePartitionCustService pipePartitionCustService;

    @GetMapping("list")
    public IstarResponse getList(PartitionCustRequest partitionCustRequest) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);
        return IstarResponse.ok(pipePartitionCustService.getList(partitionCustRequest));
    }

    @PostMapping
    public IstarResponse save(@RequestBody PipePartitionCust pipePartitionCust) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        pipePartitionCust.setTenantId(tenantId);
        String check = pipePartitionCustService.check(pipePartitionCust);
        if (StringUtils.isNotBlank(check)) {
            return IstarResponse.error(check);
        }
        PipePartitionCust pi = pipePartitionCustService.save(pipePartitionCust);
        return IstarResponse.ok(pi);
    }

    @PostMapping("batchSave")
    public IstarResponse save(@RequestBody List<PipePartitionCust> pipePartitionCustList) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String result = pipePartitionCustService.batchSave(pipePartitionCustList, tenantId);
        if (StringUtils.isNotBlank(result)) {
            return IstarResponse.error(result);
        }
        return IstarResponse.ok("添加成功");
    }

    @PostMapping("batchRemove")
    public IstarResponse batchRemove(@RequestBody List<String> idList) throws ThingsboardException {
        pipePartitionCustService.batchRemove(idList);
        return IstarResponse.ok("移除成功");
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) {
        pipePartitionCustService.delete(idList);
        return IstarResponse.ok();
    }

    @GetMapping("template")
    public void getTemplate(HttpServletResponse response) {
        ExcelUtil.getPartitionCustTemplate(response);
    }

    @PostMapping("importSave")
    public IstarResponse importSave(MultipartFile file) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return pipePartitionCustService.importSave(file, tenantId);
    }

    @GetMapping("export")
    public IstarResponse export(PartitionCustRequest partitionCustRequest, HttpServletResponse response) throws ThingsboardException {
        partitionCustRequest.setPage(1);
        partitionCustRequest.setSize(Integer.MAX_VALUE);
        IstarResponse result = this.getList(partitionCustRequest);
        if (result.getCode() != 200) {
            return result;
        }
        PageData<PipePartitionCust> pipePartitionCustPageData = (PageData<PipePartitionCust>) result.getData();
        if (pipePartitionCustPageData.getTotal() > 20000) {
            return IstarResponse.error("数据超过两万条，请筛选后导出");
        }
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        Map headMap = new LinkedHashMap();
        headMap.put("custCode", "用户编号");
        headMap.put("custName", "用户名称");
        headMap.put("phone", "联系方式");
        headMap.put("waterCategory", "用水类型");
        headMap.put("meterBookCode", "抄表册编号");
        headMap.put("meterBookName", "抄表册名称");
        headMap.put("copyMeterUser", "抄表人");
        headMap.put("address", "地址");
        headMap.put("businessHall", "营业所");
        headMap.put("partitionName", "挂接分区");
        headMap.put("createTimeStr", "添加日期");

        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(pipePartitionCustPageData.getData()));

        ExcelUtil.exportExcelXManySheet("分区挂接营收用户信息", headMap, jsonArray, "yyyy-MM-dd HH:mm:ss", 20, "分区挂接营收用户信息", workbook);

        ByteArrayOutputStream os = new ByteArrayOutputStream();

        try {
            // sheet单独设置
            SXSSFSheet sheetAt = workbook.getSheetAt(0);
            sheetAt.setColumnWidth(11, 48);
            workbook.write(os);
            workbook.close();
            workbook.dispose();

            ExcelUtil.exportExcel("分区挂接营收用户信息", os, response);

        } catch (IOException e) {
            e.printStackTrace();
        }

        return IstarResponse.ok();
    }


}
