import{D as le}from"./DrawerBox-CLde5xC8.js";import{d as pe,cN as me,c as B,r as ue,o as ce,Q as de,ay as fe,g as b,h as R,F as g,p as d,q as y,G as w,i as v,n as M,aB as ye,aJ as ge,an as ve,j as h,aw as we,W as _,J as he,bt as _e,dt as Fe,b as F,ab as Ie,dE as V,C as Ce}from"./index-r0dFAfgr.js";import{_ as be}from"./ArcLayout-CHnHL9Pv.js";import{s as A,p as Se,g as Ae}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import{i as xe,b as Ge,e as De}from"./IdentifyHelper-RJWmLn49.js";import{g as S,a as Oe}from"./LayerHelper-Cn-iiqxI.js";import{a as J,i as z}from"./QueryHelper-ILO3qZqg.js";import{s as W,E as x}from"./StatisticsHelper-D-s_6AyQ.js";import{s as ke}from"./ToolHelper-BiiInOzB.js";import{GetFieldUniqueValue as H}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{u as Q}from"./usePipelineGroup-Ba1pmWz_.js";import{o as U}from"./chart-wy3NEK2T.js";import Ee from"./ListWindow-WS05QqV0.js";import{groupLengthStatisticsByField as $}from"./wfsUtils-DXofo3da.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./IdentifyResult-4DxLVhTm.js";import"./project-DUuzYgGl.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./pipe-nogVzCHG.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./WMSLayer-mTaW758E.js";import"./widget-BcWKanF2.js";import"./scaleUtils-DgkF6NQH.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./imageBitmapUtils-Db1drMDc.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./identify-4SBo5EZk.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./PopLayout-BP55MvL7.js";const Le={key:0,class:"infowindow-container"},Ne={class:"left-wrapper"},qe={class:"left-header"},Pe={class:"overview-wrapper"},Te={class:"row1"},Be={class:"left"},Re={class:"table-box"},Me={class:"right"},Ve={class:"chart-box"},Je={class:"row2"},ze={class:"left"},We={class:"chart-box"},He={class:"right"},Qe={class:"chart-box"},Ue=pe({__name:"PipeOverView",setup($e){let G;const{proxy:D}=me(),O=B(),k=B(),m=ue({Attrs:[],diameterOption:null,materialsOption:null,typeOption:null,windows:[]}),a={view:void 0,queryFeatures:{typeFeatures:{},diameterFeatures:{},materialFeatures:{}},identifyResults:[]},j=async()=>{var e;try{const r=await W("count",{layerIds:_().gLayerIds});m.Attrs=r.map(t=>{var i;return{value:(((i=t.rows[0])==null?void 0:i[x.OBJECTID])??0)+" 个",label:t.layername,id:t.layerid}});const s=((e=_().gLayerInfos)==null?void 0:e.filter(t=>t.geometrytype==="esriGeometryPolyline").map(t=>t.layerid))||[],o=r.filter(t=>s.indexOf(t.layerid)===-1).map(t=>{var i;return{value:((i=t.rows[0])==null?void 0:i[x.OBJECTID])??0,name:t.layername,id:t.layerid}});K(o),W("length",{layerIds:s}).then(t=>{t.map(i=>{var u;const n=m.Attrs.find(l=>l.label===i.layername);n&&(n.value=(((u=i.rows[0])==null?void 0:u[x.ShapeLen])??0)+" 米")})}).catch(t=>{console.log(t)})}catch(r){console.log(r)}},K=e=>{const r="总数",s=e.reduce((i,n)=>i+(parseFloat(n.value)||0)*1,0),o=Ie(s),t={tooltip:{trigger:"item",formatter:i=>i.name+": "+Number(i.value).toFixed(0)+" 个"},legend:{type:"scroll",icon:"circle",orient:"vertical",left:"right",top:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#fff",rich:{name:{align:"left",width:60,fontSize:12,color:h().isDark?"#fff":"#2A2A2A"},value:{align:"left",width:120,fontSize:12,color:"#00ff00"}}},data:e.map(i=>i.name),formatter(i){if(e&&e.length){for(let n=0;n<e.length;n++)if(i===e[n].name)return"{name| "+i+"}{value| "+e[n].value+" 个}"}}},title:[{text:"{name|"+r+"("+o.unit+`个)}
{value|`+o.value.toFixed(0)+"}",top:"center",left:"24%",textAlign:"center",textStyle:{rich:{name:{fontSize:10,fontWeight:"normal",padding:[8,0],align:"center",color:h().isDark?"#fff":"#2A2A2A"},value:{fontSize:16,fontWeight:"bold",color:h().isDark?"#fff":"#2A2A2A"}}}}],color:V.color,series:[{type:"pie",radius:["40%","55%"],center:["25%","50%"],data:e,hoverAnimation:!0,label:{show:!1,formatter:i=>"{icon|●}{name|"+i.name+"}{value|"+(i.value||"0")+"}",padding:[0,-100,25,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:18,fontWeight:"bold"}}}}]};m.typeOption=t},X=async()=>{try{const s=(await $("管线","管径","管长")).map(o=>{var t;return{name:((t=o.name)==null?void 0:t.toString())||"未知",nameAlias:"DN"+(o.name||"未知"),value:((o.value||0)/1e3).toFixed(2),valueAlias:((o.value||0)/1e3).toFixed(2)}});m.diameterOption=U(s,"管长(m)",void 0,"长度")}catch(e){console.log(e)}},Y=async()=>{try{const s=(await $("管线","管材","管长")).map(o=>({name:o.name||"未知",value:((o.value||0)/1e3).toFixed(2),valueAlias:((o.value||0)/1e3).toFixed(2)}));m.materialsOption=U(s,"管长(m)",[{offset:0,color:"#547cff"},{offset:1,color:"#424971"}],"长度")}catch(e){console.log(e)}},Z=async e=>{e&&await L({...e,value:e.value,name:e.label,id:e.id})},E=e=>{var t;const r=(t=_().gLayerInfos)==null?void 0:t.filter(i=>i.geometrytype==="esriGeometryPoint").map(i=>i.layerid),s=(r==null?void 0:r.findIndex(i=>i===e))??-1;return V.color[s??0]},L=e=>{var o;const r=(e==null?void 0:e.id)??void 0;if(!a.view||r===void 0)return;const s=S(a.view,{id:"query-pipe-result",title:"管线查询结果"});if((o=a.queryFeatures.typeFeatures[r])!=null&&o.length){s==null||s.removeMany(a.queryFeatures.typeFeatures[r]),a.queryFeatures.typeFeatures[r]=[];return}J(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+r,z({returnGeometry:!0,where:"1=1"})).then(t=>{const i=E(r);t.features.map(n=>n.symbol=A(n.geometry.type,{color:i})),s==null||s.addMany(t.features),a.queryFeatures.typeFeatures[r]=t.features,F.success("查询成功！")}).catch(()=>{F.error("查询失败")})},ee=()=>{const e=S(a.view,{id:"query-pipe-result",title:"管线查询结果"});e==null||e.removeAll(),a.queryFeatures.diameterFeatures={},a.queryFeatures.materialFeatures={},a.queryFeatures.typeFeatures={}},N=async(e,r,s,o)=>{var n,u,l,p;const t=r.name;if(!a.view||!t||(o=o===void 0?(u=(n=_().gLayerInfos)==null?void 0:n.find(c=>c.geometrytype==="esriGeometryPolyline"))==null?void 0:u.layerid:o,o===void 0))return;const i=S(a.view,{id:"query-pipe-result",title:"管线查询结果"});if(e==="diameter"){if((l=a.queryFeatures.diameterFeatures[t])!=null&&l.length){i==null||i.removeMany(a.queryFeatures.diameterFeatures[t]),a.queryFeatures.diameterFeatures[t]=[];return}}else if(e==="material"&&(p=a.queryFeatures.materialFeatures[t])!=null&&p.length){i==null||i.removeMany(a.queryFeatures.materialFeatures[t]),a.queryFeatures.materialFeatures[t]=[];return}J(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+o,z({returnGeometry:!0,where:s||""})).then(c=>{const f=S(a.view,{id:"query-pipe-result",title:"管线查询结果"}),ne=E(o??-1);c.features.map(T=>T.symbol=A(T.geometry.type,{color:ne})),f==null||f.addMany(c.features),e==="diameter"?a.queryFeatures.diameterFeatures[t]=c.features:e==="material"&&(a.queryFeatures.materialFeatures[t]=c.features),F.success("查询成功！")}).catch(()=>{F.error("查询失败")})},te=async()=>{X(),Y(),j()},re=()=>{var e;a.view&&(ke("crosshair"),a.mapClick=(e=a.view)==null?void 0:e.on("click",async r=>{await oe(r)}))},oe=async e=>{var r,s;if(a.view)try{let o;m.windows=[];const t=Oe(a.view,!0),i=xe({layerIds:t,geometry:e.mapPoint,mapExtent:a.view.extent});if(GIS_SERVER_SWITCH){if(o=await Ge(a.view,G,e),!o){F.warning("没有相关数据");return}o=o.data,m.windows.push({visible:!0,longitude:o.features[0].geometry.coordinates[0][0][0],latitude:o.features[0].geometry.coordinates[0][0][1],title:o.features[0].id,attributes:{row:o.features[0].properties,id:o.features[0].properties.OBJECTID}}),q(o.features[0].properties.OBJECTID)}else{o=await De(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,i),console.log("arcgis_result:",o),a.identifyResults=o.results||[],a.identifyResults.length>50&&(a.identifyResults.length=50);const n=[],u=[];a.identifyResults.map(p=>{n.push({layerName:p.layerName,layerId:p.layerId,...p.feature.attributes}),p.feature.symbol=A(p.feature.geometry.type),u.push(p.feature);const c=Se(p.feature.geometry)||[];m.windows.push({visible:!1,x:c[0],y:c[1],title:p.layerName+"("+p.feature.attributes.新编号+")",attributes:{row:p.feature.attributes,id:p.feature.attributes.OBJECTID}})}),await Ae(a.view,a.identifyResults[0].feature);const l=(r=a.identifyResults[0])==null?void 0:r.feature.attributes.OBJECTID;q(l)}}catch(o){console.error(o),(s=a.view)==null||s.graphics.removeAll()}},ie=e=>{e.highLight=!0},q=e=>{var s,o,t;if(P(),(s=O.value)==null||s.toggleAlarmPop(!1),e===void 0)return;const r=D.$refs["refPop"+e];r!=null&&r[0]&&((o=r[0])==null||o.toggle(!0),(t=r[0])==null||t.setPosition(a.view))},P=()=>{var e;(e=m.windows)==null||e.map(r=>{var t,i;const s=(t=r.attributes)==null?void 0:t.id;if(!s)return;const o=D.$refs["refPop"+s];o!=null&&o.length&&((i=o[0])==null||i.toggle(!1))})},I=Q("管线材质分组"),C=Q("管线口径分组"),ae=async e=>{a.view=e,re()},se=async e=>{var s,o;G=e;const r=(o=(s=_().gLayerInfos)==null?void 0:s.find(t=>t.geometrytype==="esriGeometryPolyline"))==null?void 0:o.layerid;H({layerid:r,field_name:"MATERIAL"}).then(t=>{var n,u;I.init(a.view),(((u=(n=t.data)==null?void 0:n.result)==null?void 0:u.rows)||[]).map((l,p)=>{const c=I.genSql("MATERIAL",l),f=I.genColor(p);I.addSubLayer(a.view,r,l,c,f)})}).catch(t=>{console.log(t)}),H({layerid:r,field_name:"DIAMETER"}).then(t=>{var n,u;C.init(a.view),(((u=(n=t.data)==null?void 0:n.result)==null?void 0:u.rows)||[]).map((l,p)=>{const c=C.genSql("DIAMETER",l),f=C.genColor(p);C.addSubLayer(a.view,r,"DN"+l,c,f)})}).catch(t=>{console.log(t)}),te()};return ce(async()=>{var e;(e=k.value)==null||e.toggleDrawer("ltr",!0)}),de(()=>{var e;(e=a.mapClick)==null||e.remove()}),(e,r)=>{const s=be,o=he,t=_e,i=Fe,n=fe("VChart"),u=le;return b(),R(u,{ref_key:"refDrawerBox",ref:k,"left-drawer":!0,"left-drawer-bar-position":"top","left-drawer-absolute":!1,"left-drawer-width":700,theme:v(h)().isDark?"darkblue":"light",class:we(v(h)().isDark?"darkblue":"")},{left:g(()=>[d("div",Ne,[d("div",qe,[r[5]||(r[5]=d("span",null,"管网总览",-1)),y(o,{type:"danger",text:!0,onClick:ee},{default:g(()=>r[4]||(r[4]=[w(" 清除查询 ")])),_:1})]),d("div",Pe,[d("div",Te,[d("div",Be,[y(t,{type:"simple"},{default:g(()=>r[6]||(r[6]=[w(" 管网数量统计 ")])),_:1}),d("div",Re,[y(i,{class:"attr-table",attributes:v(m).Attrs,link:!0,onRowClick:r[0]||(r[0]=l=>Z(l))},null,8,["attributes"])])]),d("div",Me,[y(t,{type:"simple"},{default:g(()=>r[7]||(r[7]=[w(" 按管径统计 ")])),_:1}),d("div",Ve,[y(n,{option:v(m).diameterOption,onClick:r[1]||(r[1]=l=>{N("diameter",l.data,l.data.name&&" DIAMETER='"+l.data.name+"'")})},null,8,["option"])])])]),d("div",Je,[d("div",ze,[y(t,{type:"simple"},{default:g(()=>r[8]||(r[8]=[w(" 管点类型统计 ")])),_:1}),d("div",We,[y(n,{option:v(m).typeOption,onClick:r[2]||(r[2]=l=>{L(l.data)})},null,8,["option"])])]),d("div",He,[y(t,{type:"simple"},{default:g(()=>r[9]||(r[9]=[w(" 按材质统计 ")])),_:1}),d("div",Qe,[y(n,{option:v(m).materialsOption,onClick:r[3]||(r[3]=l=>{N("material",l.data,l.data.name&&" MATERIAL='"+l.data.name+"'")})},null,8,["option"])])])])])])]),default:g(()=>[y(s,{ref_key:"refArcLayout",ref:O,onMapLoaded:ae,onPipeLoaded:se,onAlarmClick:P},{"map-bars":g(()=>{var l;return[(l=v(m).windows)!=null&&l.length?(b(),M("div",Le,[(b(!0),M(ye,null,ge(v(m).windows,(p,c)=>{var f;return b(),R(Ee,{key:c,ref_for:!0,ref:"refPop"+((f=p.attributes)==null?void 0:f.id),view:a.view,config:p,onHighlight:ie},null,8,["view","config"])}),128))])):ve("",!0)]}),_:1},512)]),_:1},8,["theme","class"])}}}),$r=Ce(Ue,[["__scopeId","data-v-3d358b4e"]]);export{$r as default};
