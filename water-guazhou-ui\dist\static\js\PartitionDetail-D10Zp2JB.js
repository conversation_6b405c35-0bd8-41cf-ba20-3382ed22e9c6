import{d as V,c as T,r as x,a8 as U,S as B,b as w,am as I,g as q,n as E,p as c,q as p,i as s,F as _,G as C,t as R,_ as W,J as z,aq as G,c5 as $,C as J}from"./index-r0dFAfgr.js";import{D as j,b as K,c as Q,C as X,I as Y,d as Z,A as ee,U as te,T as le}from"./index-0NlGN6gS.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as re}from"./usePartition-DkcY9fQ2.js";import{P as ie,b as oe}from"./dma-SMxrzG7b.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./hookupDevice-Bcbk7s68.js";const ae={class:"partition-detail"},ne={class:"form-box"},se={class:"form-scroll overlay-y"},pe={class:"form-footer"},me={class:"table-box"},ue=V({__name:"PartitionDetail",props:{treeData:{},currentTreeNode:{}},emits:["edit","draw","success","more"],setup(F,{expose:P,emit:D}){const m=D,n=F,a=T(),u=T("1"),v=x({type:"tabs",tabType:"border-card",fullHeight:!0,tabs:[{label:"流量计",value:"1"},{label:"压力计",value:"2"},{label:"大用户",value:"3"}],onChange:()=>{d()}}),r=(t,e,l)=>{l.hidden=t.type!=="2"},i=x({dataList:[],indexVisible:!0,columns:[{label:"名称",prop:"name"},{label:"水流方向",prop:"direction",tag:!0,tagColor(t,e){return e==="1"?"#318DFF":"#f56c6c"},formatter:(t,e)=>j[e]||"- -"}],pagination:{refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,d()}}}),M=re(),d=async()=>{var t;if(!n.currentTreeNode){i.dataList=[],i.pagination.total=0;return}i.loading=!0;try{const e=u.value,l=await M.getDevices({page:i.pagination.page||1,size:i.pagination.limit||20,partitionId:(t=n.currentTreeNode)==null?void 0:t.value,type:e});i.dataList=l.data||[],i.pagination.total=l.total||0}catch{}i.loading=!1},f=x({labelWidth:120,labelPosition:"right",group:[{fields:[{lg:12,xl:12,type:"input",label:"分区名称",field:"name"},{lg:12,xl:12,type:"select",label:"分区类型",field:"type",clearable:!1,options:K},{lg:12,xl:12,handleHidden:r,type:"select",label:"分区状态",field:"status",clearable:!1,options:Q},{lg:12,xl:12,type:"input-number",label:"分区排序",field:"orderNum"},{lg:12,xl:12,type:"select",label:"分区抄表类型",field:"copyMeterType",clearable:!1,options:X},{lg:12,xl:12,type:"select",label:"是否有机械表",field:"isMachineMeter",clearable:!1,options:Y},{lg:12,xl:12,type:"select",label:"采集频率",field:"collectRate",options:Z,rules:[{required:!0,message:"请选择采集频率"}]},{lg:12,xl:12,handleHidden:r,type:"select",label:"小区类型",field:"villageType",options:ee},{lg:12,xl:12,type:"select-tree",label:"所属分区",field:"pid",checkStrictly:!0,options:U(()=>n.treeData||[]),rules:[{validator(t,e,l){var o;e===((o=a.value)==null?void 0:o.dataForm.id)?l(new Error("所属分区不能是自身ID")):l()}}]},{lg:12,xl:12,type:"select",label:"用户类型",field:"userType",options:te},{handleHidden(t,e,l){l.hidden=t.type!=="1"},type:"input",label:"分区范围",field:"range"},{handleHidden:r,type:"input",label:"小区地址",field:"range"},{lg:12,xl:12,type:"color-picker",label:"边框颜色",field:"borderColor"},{lg:12,xl:12,type:"color-picker",label:"区域颜色",field:"rangeColor"},{type:"btn-group",label:"分区绘制",btns:[{perm:!0,text:"编辑",plain:!0,click:()=>k()},{perm:!0,text:"重绘",plain:!0,type:"danger",click:()=>N()}]},{lg:12,xl:12,type:"input-number",label:"供水面积",field:"supplyWaterArea",suffix:"k㎡",appendBtns:[{perm:!0,text:"计算",click:()=>S()}]},{lg:12,xl:12,type:"input-number",label:"主管线长度",field:"mainLineLength",suffix:"km",appendBtns:[{perm:!0,text:"计算",click:()=>H()}]},{lg:12,xl:12,type:"input",label:"抄表员",field:"copyMeterUser"},{lg:12,xl:12,type:"input",label:"负责人",field:"director"},{lg:12,xl:12,type:"input",disabled:!0,label:"大用户数",suffix:"户",field:"bigUserNum"},{lg:12,xl:12,type:"input",label:"营收用户数",field:"revenueUserNum",suffix:"户",disabled:!0},{lg:12,xl:12,handleHidden:r,type:"input-number",label:"抄表周期",field:"copyMeterCycle"},{lg:12,xl:12,handleHidden:r,type:"input-number",label:"入口数量",suffix:"个",field:"inletNum"},{lg:12,xl:12,handleHidden:r,type:"input-number",label:"小区入管口径",suffix:"mm",field:"inPipeCaliber"},{lg:12,xl:12,handleHidden:r,type:"input",label:"小区物业",field:"propertyName",placeholder:"请输入物业公司"},{lg:12,xl:12,handleHidden:r,type:"range",rangeType:"select",label:"最小流量区间",options:le,field:"minFlowStartHour"},{lg:12,xl:12,handleHidden:r,type:"input-number",label:"合法用水当量",field:"legalUseWater"},{lg:12,xl:12,handleHidden:r,type:"input-number",suffix:"MPa",label:"平均压力",field:"avgPressure"},{lg:12,xl:12,handleHidden:r,type:"select",label:"启用产销差",field:"enableNrw",options:[{label:"是",value:1},{label:"否",value:0}]},{lg:12,xl:12,handleHidden:r,type:"input-number",suffix:"MPa",label:"最小合格压力值",field:"minPassPressure"},{lg:12,xl:12,handleHidden:r,type:"input-number",label:"最大合格压力值",suffix:"MPa",field:"maxPassPressure"},{handleHidden:r,type:"textarea",label:"零压测试详情",field:"zeroPressureTestDetail"},{type:"textarea",label:"备注",field:"remark"}]}],defaultValue:{...n.currentTreeNode||{}},submit:t=>{B("确定提交？","提示信息").then(async()=>{var e,l;try{f.submitting=!0;const o=(e=t.minFlowStartHour)==null?void 0:e[0],b=(l=t.minFlowStartHour)==null?void 0:l[1],g={...t,minFlowStartHour:o,minFlowEndHour:b};(await ie(g)).data&&(w.success("操作成功"),m("success"))}catch(o){console.log(o),w.error("操作失败")}f.submitting=!1}).catch(()=>{})}}),N=()=>{m("draw")},k=()=>{var t;m("edit",(t=a.value)==null?void 0:t.dataForm.id)},S=()=>{},H=()=>{},A=()=>{m("more")},L=async()=>{var l;const e=(await oe((l=n.currentTreeNode)==null?void 0:l.value)).data||{};e.minFlowStartHour=[e.minFlowStartHour,e.minFlowEndHour],a.value&&(a.value.dataForm={...e})},O=async()=>{d(),L()};return I(()=>{var t;return(t=n.currentTreeNode)==null?void 0:t.value},()=>{O()}),P({refForm:a}),(t,e)=>{const l=W,o=z,b=G,g=$;return q(),E("div",ae,[c("div",ne,[c("div",se,[p(l,{ref_key:"refForm",ref:a,config:s(f)},null,8,["config"])]),c("div",pe,[p(o,{type:"primary",onClick:e[0]||(e[0]=y=>{var h;return(h=s(a))==null?void 0:h.Submit()})},{default:_(()=>e[2]||(e[2]=[C(" 保存 ")])),_:1}),p(o,{type:"success",onClick:A},{default:_(()=>e[3]||(e[3]=[C(" 分区资料 ")])),_:1})])]),c("div",me,[p(g,{modelValue:s(u),"onUpdate:modelValue":e[1]||(e[1]=y=>R(u)?u.value=y:null),config:s(v),onChange:s(v).onChange},{content:_(()=>[p(b,{config:s(i)},null,8,["config"])]),_:1},8,["modelValue","config","onChange"])])])}}}),Mt=J(ue,[["__scopeId","data-v-8489bc5d"]]);export{Mt as default};
