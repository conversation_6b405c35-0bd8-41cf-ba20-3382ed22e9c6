import{_ as n}from"./CardTable-rdWOL4_6.js";import{k as p}from"./projectManagement-CDcrrCQ1.js";import{d as c,r as l,o as m,g as i,h as d,i as u,C as f}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const _=c({__name:"ssxmwcqk",props:{id:{}},setup(t){const o=t,a=l({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"业务阶段",prop:"scopeName"},{label:"工程code",prop:"code"},{label:"工程名称",prop:"name"},{label:"开始时间",prop:"startTimeName"},{label:"结束时间",prop:"completeTimeName"},{label:"处理人",prop:"processUserName"},{label:"完成状态",prop:"statusName",tag:!0,tagColor:e=>e.statusName==="完成"?"#67C23A":"#409EFF",formatter:e=>e.statusName}],dataList:[],pagination:{hide:!0}}),s=async()=>{p(o.id).then(e=>{a.dataList=e.data.data||[]})};return m(()=>{s()}),(e,b)=>{const r=n;return i(),d(r,{title:"所属项目完成情况",config:u(a),class:"card-table"},null,8,["config"])}}}),h=f(_,[["__scopeId","data-v-bcf73fe4"]]);export{h as default};
