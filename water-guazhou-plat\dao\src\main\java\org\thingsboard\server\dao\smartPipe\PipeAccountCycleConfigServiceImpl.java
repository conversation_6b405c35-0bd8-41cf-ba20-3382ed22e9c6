package org.thingsboard.server.dao.smartPipe;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeAccountCycleConfig;
import org.thingsboard.server.dao.sql.smartPipe.PipeAccountCycleConfigMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PipeAccountCycleConfigServiceImpl implements PipeAccountCycleConfigService {

    @Autowired
    private PartitionService partitionService;

    @Autowired
    private PipeAccountCycleConfigMapper pipeAccountCycleConfigMapper;

    @Override
    public List<PipeAccountCycleConfig> getListByPartition(String month, String name, String partitionId) {
        List<String> partitionIdList = new ArrayList<>();
        partitionService.getAllChildId(partitionId, partitionIdList);
        return pipeAccountCycleConfigMapper.getListByPartitionIdIn(month, name, partitionIdList);
    }

    @Override
    @Transactional
    public PipeAccountCycleConfig save(PipeAccountCycleConfig pipeAccountCycleConfig) {
        // 自动生成
        pipeAccountCycleConfig.setIsDefault("0");
        if ("1".equals(pipeAccountCycleConfig.getAutoGenerate())) {
            List<String> partitionIdList = partitionService.getAllId(pipeAccountCycleConfig.getTenantId());
            for (String partitionId : partitionIdList) {
                pipeAccountCycleConfig.setId(null);
                pipeAccountCycleConfig.setPartitionId(partitionId);
                pipeAccountCycleConfig.setCreateTime(new Date());

                pipeAccountCycleConfigMapper.insert(pipeAccountCycleConfig);
            }
        } else {
            if (StringUtils.isBlank(pipeAccountCycleConfig.getId())) {
                pipeAccountCycleConfig.setCreateTime(new Date());
                pipeAccountCycleConfigMapper.insert(pipeAccountCycleConfig);
            } else {
                pipeAccountCycleConfigMapper.updateById(pipeAccountCycleConfig);
            }
            return pipeAccountCycleConfig;
        }
        return null;
    }

    @Override
    @Transactional
    public void delete(List<String> idList) {
        pipeAccountCycleConfigMapper.deleteBatchIds(idList);
    }
}
