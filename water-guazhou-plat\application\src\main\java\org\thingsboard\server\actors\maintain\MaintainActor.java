/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.actors.maintain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.thingsboard.rule.engine.api.ScriptEngine;
import org.thingsboard.server.actors.ActorSystemContext;
import org.thingsboard.server.actors.ruleChain.RuleChainManagerActor;
import org.thingsboard.server.actors.service.ContextBasedCreator;
import org.thingsboard.server.actors.shared.rulechain.SystemRuleChainManager;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.RuleNodeId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.maintain.MaintainRecord;
import org.thingsboard.server.common.msg.TbActorMsg;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.common.msg.TbMsgMetaData;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.script.RuleNodeJsScriptEngine;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MaintainActor extends RuleChainManagerActor {
    private ScheduledExecutorService scheduler;

    private ActorSystemContext systemContext;

    private MaintainActor(ActorSystemContext systemContext) {
        super(systemContext, new SystemRuleChainManager(systemContext));
        this.systemContext = systemContext;
    }

    @Override
    public void preStart() {

    }


    public static class ActorCreator extends ContextBasedCreator<MaintainActor> {
        private static final long serialVersionUID = 1L;

        public ActorCreator(ActorSystemContext context) {
            super(context);
        }

        @Override
        public MaintainActor create() throws Exception {
            return new MaintainActor(context);
        }
    }

    @Override
    public void onReceive(Object msg) {
        super.onReceive(msg);
        log.info("开启保养检测Actor");
        start();
    }

    private void start() {
        List<Tenant> tenants = systemContext.getTenantService().findAll();
        scheduler = Executors.newSingleThreadScheduledExecutor();
        tenants.forEach(tenant -> {
            scheduler.scheduleAtFixedRate(() -> {
                checkMaintain(tenant.getId());
            }, 0, 1000 * 60 , TimeUnit.MILLISECONDS);
        });
    }

    @Override
    protected boolean process(TbActorMsg msg) {
        return true;
    }


    @Override
    protected void broadcast(Object msg) {
        super.broadcast(msg);
    }


    /**
     * 检查是否需要保养
     *
     * @param tenantId
     */
    private void checkMaintain(TenantId tenantId) {
        List<Maintain> maintainList = systemContext.getMaintainService().findByTenantId(tenantId);
        if (maintainList != null && maintainList.size() > 0) {
            maintainList.forEach(m -> {
                try {
                    //检查是否需要进行保养检测
                    if (!isCheckMaintain(m))
                        return;
                    ScriptEngine buildDetailsJsEngine = new RuleNodeJsScriptEngine(systemContext.getJsSandbox(), new RuleNodeId(UUIDConverter.fromString("1e8d6a021320dc0a893af28d1b57aeb")), m.getMaintainJson());
                    JsonNode jsonNode = JacksonUtil.toJsonNode(m.getParams());
                    ObjectNode objectNode = (ObjectNode) jsonNode;
                    getLastData(m.getDeviceId(), m.getTenantId()).entrySet().forEach(entry -> {
                                objectNode.put(entry.getKey(), entry.getValue());
                            }
                    );
                    JsonNode createJson = null;
                    //执行js代码
                    createJson = executeJson(buildDetailsJsEngine, JacksonUtil.toString(jsonNode), tenantId);
                    buildDetailsJsEngine.destroy();
                    if (createJson.asText().equals(DataConstants.TRUE)) {
                        createMaintain(m);
                        log.info("触发保养条件成功！");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

        }
    }

    private com.fasterxml.jackson.databind.JsonNode executeJson(ScriptEngine scriptEngine, String params, TenantId tenantId) throws Exception {
        return systemContext.getJsExecutor().executeAsync(() -> {
            JSONObject object = new JSONObject();
            TbMsgMetaData tbMsgMetaData = new TbMsgMetaData();
            if (params != null) {
                Map<String,Object> map=(Map) JSON.parse(params);
                map.entrySet().forEach(entry->{
                    object.put(entry.getKey(), entry.getValue());
                    tbMsgMetaData.putValue(entry.getKey(), JacksonUtil.toString(entry.getValue()));
                });

            }
            TbMsg tbMsg = new TbMsg(UUID.randomUUID(), "TENANT", tenantId, tbMsgMetaData, object.toJSONString(), null, null, 0L);
            return scriptEngine.executeJson(tbMsg);
        }).get();
    }

    /**
     * 检测该条保养设定是否继续进行检测
     *
     * @param maintain
     * @return
     */
    private boolean isCheckMaintain(Maintain maintain) {
        //检查是否已经触发
        List<MaintainRecord> maintainRecords = systemContext.getMaintainRecordService().findByMaintainId(maintain.getId());
        if (maintainRecords.size() < 1)
            return true;
        else {
            if (maintain.getType().equals(DataConstants.MAINTAIN_TYPE_CYCLE) && (maintainRecords.get(maintainRecords.size() - 1).getCreateTime() + Long.valueOf(maintain.getPeriod())) > System.currentTimeMillis())
                return true;
        }
        return false;
    }

    /**
     * 当检测到需要进行保养时，给用户发送邮件和短信
     */
    private void sendEmailAndSms(MaintainRecord maintain, Device device) {
        List<User> users = systemContext.getUserService().findUserByTenant(device.getTenantId());
        List<User> sendEmailUser = new ArrayList<>();
        List<User> sendSmsUser = new ArrayList<>();
        users.forEach(user -> {
            //新增在进行报警时，先判断该用户是否已经激活
            if (systemContext.getUserCredentialsDao().findByUserId(user.getTenantId(), user.getUuidId()) == null
                    || !systemContext.getUserCredentialsDao().findByUserId(user.getTenantId(), user.getUuidId()).isEnabled()) {
                return;
            }
            Map info = JacksonUtil.fromString(user.getAdditionalInfo().asText(), Map.class);
            if (info.get(ModelConstants.ALARM_FORM_EMAIL) != null && info.get(ModelConstants.ALARM_FORM_EMAIL).equals(ModelConstants.ALARM_RELEASE_NOT))
                sendEmailUser.add(user);
            if (info.get(ModelConstants.ALARM_FORM_SMS) != null && info.get(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT))
                sendSmsUser.add(user);
        });
        String subject = "设备保养提醒";
        String emailBody = getEmailBody(device, maintain);
//        systemContext.getMailExecutor().execute(() -> {
//            sendEmailUser.forEach(user -> {
//                if (user.getEmail() != null) {
//                    try {
//                        systemContext.getMailService().sendEmail(user.getEmail(), subject, emailBody);
//                    } catch (ThingsboardException e) {
//                        log.debug("发送邮件失败" + e + "发送人" + user.getName());
//                        e.printStackTrace();
//                    }
//                }
//            });
//            sendSmsUser.forEach(user -> {
//                if (user.getAdditionalInfo().get(ModelConstants.USER_PHONE) != null) {
//                    systemContext.getMailService().sendSMS(user.getAdditionalInfo().get(ModelConstants.USER_PHONE).asText(), device.getName());
//                }
//            });
//        });
    }

    private Map<String, String> getLastData(DeviceId deviceId, TenantId tenantId) {
        Map<String, String> map = new HashMap<>();
        try {
            List<TsKvEntry> kvEntries = systemContext.getTimeseriesService().findAllLatest(tenantId, deviceId).get();
            if (kvEntries != null && kvEntries.size() > 0) {
                kvEntries.forEach(k -> {
                    map.put(k.getKey(), k.getValueAsString());
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //map 中加入系统当前时间
        map.put("sysTime", String.valueOf(System.currentTimeMillis()));
        return map;
    }


    public String getEmailBody(Device device, MaintainRecord alarm) {
        String body = "您的设备" + device.getName() + "于" + DateUtils.date2Str(new Date(alarm.getCreateTime()), DateUtils.DATE_FORMATE_DEFAULT) + "需要进行保养，请尽快前往处理！";
        return body;
    }

    /**
     * 创建新的保养记录
     */
    private void createMaintain(Maintain maintain) {
        MaintainRecord maintainRecord = new MaintainRecord();
        maintainRecord.setStatus(DataConstants.MAINTAIN_STATUS_WAIT);
        maintainRecord.setMaintainId(maintain.getId());
        maintainRecord.setCreateTime(System.currentTimeMillis());
        maintainRecord.setDeviceId(maintain.getDeviceId());
        maintainRecord.setTenantId(maintain.getTenantId());
        maintainRecord.setName(maintain.getName());
        maintainRecord.setProjectId(maintain.getProjectId());
        systemContext.getMaintainRecordService().addMaintainRecord(maintainRecord);
        sendEmailAndSms(maintainRecord, systemContext.getDeviceService().findDeviceById(maintainRecord.getDeviceId()));
    }
}
