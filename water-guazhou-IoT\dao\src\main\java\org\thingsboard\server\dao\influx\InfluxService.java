package org.thingsboard.server.dao.influx;

import com.influxdb.query.FluxTable;
import org.thingsboard.server.common.data.tsdb.DataPoint;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/2 16:03
 */
public interface InfluxService {
    /**
     * 保存设备数据到influxDB
     *
     * @param dataPoints 设备数据点列表
     */
    void saveDeviceToInflux(List<DataPoint> dataPoints);

    /**
     * 从influxDB获取设备数据
     *
     * @param formulas 数据公式，设备ID+"."+设备属性
     */
    List<FluxTable> findDeviceDataFromInflux(List<String> formulas, long start, long end);
}
