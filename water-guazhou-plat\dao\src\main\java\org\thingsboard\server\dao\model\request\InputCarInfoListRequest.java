package org.thingsboard.server.dao.model.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class InputCarInfoListRequest {

    private int page;

    private int size;

    private String carNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTimeEnd;

    private String createUser;

    private String tenantId;

}
