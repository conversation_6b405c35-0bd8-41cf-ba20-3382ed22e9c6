package org.thingsboard.server.dao.model.sql.zutai;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ZUTAI_DASHBOARD_TABLE)
public class ZutaiDashboardEntity {
    @Id
    private String id;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_DATA)
    private String data;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_TENANT_ID)
    private String tenantId;
}
