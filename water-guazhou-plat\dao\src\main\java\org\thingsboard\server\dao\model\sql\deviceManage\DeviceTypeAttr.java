package org.thingsboard.server.dao.model.sql.deviceManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.model.JdbcHelper;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("m_device_type_attr")
public class DeviceTypeAttr {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 设备类别序列号
    private String serialId;

    // 属性编码
    private String code;

    // 类别名称
    private String name;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    private void customizeMap(ResponseMap map, JdbcHelper jdbc) {
        DeviceTypeMapper mapper = jdbc.getMapper(DeviceTypeMapper.class);
        map.put("mainTypeName", mapper.getMainTypeName(serialId, tenantId));
    }
}
