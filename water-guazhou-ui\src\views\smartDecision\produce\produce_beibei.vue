<!-- 智慧生产 -->
<template>
  <div class="content">
    <!-- left -->
    <div class="content_left">
      <TitleCard
        title="费量趋势"
        class="flqs"
      >
        <SSLQS />
      </TitleCard>
      <TitleCard
        :title="'水厂工艺'"
        class="scgy"
      >
        <SCGY></SCGY>
      </TitleCard>
      <CXCTJ class="cxctj"></CXCTJ>
    </div>
    <!-- center -->
    <div class="content_center">
      <GSL></GSL>
      <ScadaView />
    </div>
    <!-- right -->
    <div class="content_left content_right">
      <TitleCard
        :title="'出厂流量'"
        class="ccll mg-12"
      >
        <CCLL></CCLL>
      </TitleCard>
      <TitleCard
        :title="'出厂压力'"
        class="ccll mg-12"
      >
        <CCYL></CCYL>
      </TitleCard>
      <!-- <div class="cards item_bg mg-12">
        <titleVue :config="'出厂压力'"></titleVue>
        <div class="chart">
          <VChart
            ref="refChart"
            :option="折线渐变面积(ccllData)"
          ></VChart>
        </div>
      </div> -->
      <TitleCard
        :title="'出厂水质'"
        class="ccsz"
      >
        <CCSZ></CCSZ>
      </TitleCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CCSZ from './components/CCSZ.vue'
import ScadaView from './components/ScadaView.vue'

import TitleCard from '../components/TitleCard.vue'
import SCGY from './components/SCGY_beibei.vue'
import GSL from './components/GSL_beibei.vue'
import CCLL from './components/CCLL.vue'
import CCYL from './components/CCYL.vue'
import SSLQS from '../smartMarketing/components/SSLQS.vue'
import CXCTJ from './components/CXCTJ.vue'
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  width: 100%;
  padding: 100px 50px 30px;
  display: flex;
  justify-content: space-between;
  // background-color: rgb(0, 10, 20);
  color: #fff;
  padding-top: 92px;

  .content_left {
    width: 479px;
    height: 100%;
    z-index: 2;
    padding: 10px;
    .flqs {
      width: 100%;
      margin-bottom: 12px;
      height: 250px;
    }
    .scgy {
      width: 100%;
      margin-bottom: 12px;
      // height: 66%;
    }
    .cxctj {
      width: 100%;
      height: 200px;
    }
    .hgl {
      width: 100%;
      height: 180px;
    }
    .cards {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;

      .card {
        flex: 1;
      }
    }
  }
  .content_center {
    flex: 1;
    padding: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    .cards {
      width: 100%;
    }
  }
  .content_right {
    .ccll {
      width: 100%;
      height: 270px;
    }
    .ccsz {
      width: 100%;
      height: 375px;
    }
  }
}

.chart {
  width: 100%;
  height: 270px;
}

.mg_top_10 {
  margin-top: 10px;
}

.item_center_big_bg {
  background-size: 100% 100%;
  background-position: top;
  background-repeat: no-repeat;
}
.flex {
  display: flex;
}
.fl_ju_sb {
  justify-content: space-between;
}

.fl_dc_cl {
  flex-direction: column;
}
.center_item {
  position: absolute;
  width: 100px;
  height: 40px;
  background-color: #2e2f78;
  line-height: 40px;
  border-radius: 5px;
  text-align: center;
}
.mg-12 {
  margin-bottom: 12px;
}
.title-text {
  font-weight: 600;
  color: #fff;
  line-height: 36px;
  padding-left: 25px;
}
</style>
