<!-- 调度指挥 -->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="调度指挥"
    width="70%"
    :before-close="handleClose"
  >
    <video
      controls
      width="420"
      height="280"
    >
      <source
        src=""
        type="video/webm"
      />
      <source
        src=""
        type="video/mp4"
      />
      Sorry, your browser doesn't support embedded videos.
    </video>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          @click="dialogVisible = false"
        >关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

defineEmits(['highlightMark', 'addMarks'])
defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const dialogVisible = ref(true)

const handleClose = (done: () => void) => {
  ElMessageBox.confirm('Are you sure to close this dialog?')
    .then(() => {
      done()
    })
    .catch(() => {
      // catch error
    })
}
</script>
<style scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
