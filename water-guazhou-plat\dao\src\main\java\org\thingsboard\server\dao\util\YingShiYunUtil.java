package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 萤石云摄像头控制
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-30
 */
@Slf4j
@Component
public class YingShiYunUtil {
    @Value("${yingshi.appKey}")
    private String appKey;

    @Value("${yingshi.appSecret}")
    private String appSecret;


    public static JSONObject tokenMap = new JSONObject();

    public String getToken() throws IOException {
        String url = "https://open.ys7.com/api/lapp/token/get";
        HttpPost post = new HttpPost(url);

        //供水量
        List list = new ArrayList();
        NameValuePair nameValuePair = new BasicNameValuePair("appKey", appKey);
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("appSecret", appSecret);
        list.add(nameValuePair);
        HttpEntity httpEntity = new UrlEncodedFormEntity(list, StandardCharsets.UTF_8);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");
        post.setEntity(httpEntity);
        CloseableHttpResponse execute = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            execute = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = execute.getEntity();

        String s = null;
        s = EntityUtils.toString(entity);
        JSONObject object = JSONObject.parseObject(s);
        if (object.getString("code").equals("200")) {
            return object.getJSONObject("data").getString("accessToken");
        } else {
            throw new RuntimeException(object.getString("msg"));
        }
    }

    public String getPreviewURL(String deviceSerial) throws IOException {
        String url = "https://open.ys7.com/api/lapp/v2/live/address/get";
        HttpPost post = new HttpPost(url);

        //供水量
        List list = new ArrayList();
        NameValuePair nameValuePair = new BasicNameValuePair("deviceSerial", deviceSerial);
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("protocol", "2");
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("quality", "1");
        list.add(nameValuePair);
        HttpEntity httpEntity = new UrlEncodedFormEntity(list, StandardCharsets.UTF_8);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");
        post.setHeader("accessToken", getToken());
        post.setEntity(httpEntity);
        CloseableHttpResponse execute = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            execute = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = execute.getEntity();

        String s = null;
        s = EntityUtils.toString(entity);
        JSONObject object = JSONObject.parseObject(s);
        if (object.getString("code").equals("200")) {
            return object.getJSONObject("data").getString("url");
        } else {
            throw new RuntimeException(object.getString("msg"));
        }
    }
}
