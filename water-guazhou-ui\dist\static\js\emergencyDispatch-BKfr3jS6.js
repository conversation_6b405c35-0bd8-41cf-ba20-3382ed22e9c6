import{m as r}from"./index-r0dFAfgr.js";function t(e){return r({url:"/api/sp/emergencyPlan",method:"GET",params:e})}function s(e){return r({url:"/api/sp/emergencyPlan",method:"POST",data:e})}function a(e){return r({url:`/api/sp/emergencyPlan/${e}`,method:"delete"})}function c(e){return r({url:`/api/sp/emergencyUser/tree/${e}`,method:"GET"})}function i(e){return r({url:"/api/sp/emergencyUser",method:"GET",params:e})}function u(){return r({url:"/api/sp/emergencyUser/sync",method:"POST"})}function o(e){return r({url:"/api/sp/emergencyUser",method:"POST",data:e})}function m(e){return r({url:`/api/sp/emergencyUser/${e}`,method:"delete"})}function p(e){return r({url:"/api/sp/mergencyVehicle",method:"GET",params:e})}function l(e){return r({url:"/api/sp/mergencyVehicle",method:"POST",data:e})}function d(e){return r({url:`/api/sp/mergencyVehicle/${e}`,method:"delete"})}function g(e){return r({url:"/api/sp/messageRecord",method:"GET",params:e})}function h(e){return r({url:"/api/sp/messageRecord/send",method:"POST",data:e})}export{o as a,c as b,l as c,m as d,d as e,p as f,i as g,s as h,a as i,t as j,g as k,u as p,h as s};
