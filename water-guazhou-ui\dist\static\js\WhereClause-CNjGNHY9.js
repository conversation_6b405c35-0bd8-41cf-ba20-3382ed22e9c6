import{a4 as St}from"./index-r0dFAfgr.js";import{i as Rt}from"./executionError-BOo4jP8A.js";import{n as _o}from"./Point-WxyopZva.js";import{dS as Lo}from"./MapView-DaoQedLH.js";function Mo(l,s){const o=Dt[l.toLowerCase()];if(o==null)throw new Error("Function Not Recognised");if(s.length<o.minParams||s.length>o.maxParams)throw new Error(`Invalid Parameter count for call to ${l.toUpperCase()}`);return o.evaluate(s)}function Ro(l,s){const o=Dt[l.toLowerCase()];return o!=null&&s>=o.minParams&&s<=o.maxParams}const Dt={min:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.min.apply(Math,l[0])},max:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.max.apply(Math,l[0])},avg:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Ot(l[0])},sum:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Do(l[0])},stddev:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.sqrt(Et(l[0]))},count:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:l[0].length},var:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Et(l[0])}};function Ot(l){let s=0;for(let o=0;o<l.length;o++)s+=l[o];return s/l.length}function Do(l){let s=0;for(let o=0;o<l.length;o++)s+=l[o];return s}function Et(l){const s=Ot(l),o=l.length;let p=0;for(const i of l)p+=(i-s)**2;return o>1?p/(o-1):0}var C;(function(l){l.InvalidFunctionParameters="InvalidFunctionParameters",l.UnsupportedSqlFunction="UnsupportedSqlFunction",l.UnsupportedOperator="UnsupportedOperator",l.UnsupportedSyntax="UnsupportedSyntax",l.UnsupportedIsRhs="UnsupportedIsRhs",l.UnsupportedIsLhs="UnsupportedIsLhs",l.InvalidDataType="InvalidDataType",l.CannotCastValue="CannotCastValue",l.MissingStatisticParameters="MissingStatisticParameters"})(C||(C={}));const Oo={[C.MissingStatisticParameters]:"Statistic does not have 1 or 0 Parameters",[C.InvalidFunctionParameters]:"Invalid parameters for call to {function}",[C.UnsupportedIsLhs]:"Unsupported left hand expression in is statement",[C.UnsupportedIsRhs]:"Unsupported right hand expression in is statement",[C.UnsupportedOperator]:"Unsupported operator - {operator}",[C.UnsupportedSyntax]:"Unsupported syntax - {node}",[C.UnsupportedSqlFunction]:"Sql function not found = {function}",[C.InvalidDataType]:"Invalid sql data type",[C.CannotCastValue]:"Cannot cast value to the required data type"};let j=class $t extends Error{constructor(s,o){super(Rt(Oo[s],o)),this.declaredRootClass="esri.arcade.featureset.support.sqlerror",Error.captureStackTrace&&Error.captureStackTrace(this,$t)}};var M;(function(l){l.NeverReach="NeverReach",l.NotImplemented="NotImplemented",l.Cancelled="Cancelled",l.InvalidStatResponse="InvalidStatResponse",l.InvalidRequest="InvalidRequest",l.RequestFailed="RequestFailed",l.MissingFeatures="MissingFeatures",l.AggregationFieldNotFound="AggregationFieldNotFound",l.DataElementsNotFound="DataElementsNotFound"})(M||(M={}));const $o={[M.Cancelled]:"Cancelled",[M.InvalidStatResponse]:"Invalid statistics response from service",[M.InvalidRequest]:"Invalid request",[M.RequestFailed]:"Request failed - {reason}",[M.MissingFeatures]:"Missing features",[M.AggregationFieldNotFound]:"Aggregation field not found",[M.DataElementsNotFound]:"Data elements not found on service",[M.NeverReach]:"Encountered unreachable logic",[M.NotImplemented]:"Not implemented"};let ei=class Jt extends Error{constructor(s,o){super(Rt($o[s],o)),this.declaredRootClass="esri.arcade.featureset.support.featureseterror",Error.captureStackTrace&&Error.captureStackTrace(this,Jt)}},k=class D{constructor(){this.op="+",this.day=0,this.second=0,this.hour=0,this.month=0,this.year=0,this.minute=0,this.millis=0}static _fixDefaults(s){if(s.precision!==null||s.secondary!==null)throw new Error("Primary and Secondary SqlInterval qualifiers not supported")}static _parseSecondsComponent(s,o){if(o.includes(".")){const p=o.split(".");s.second=parseFloat(p[0]),s.millis=parseInt(p[1],10)}else s.second=parseFloat(o)}static createFromMilliseconds(s){const o=new D;return o.second=s/1e3,o}static createFromValueAndQualifer(s,o,p){let i=null;const d=new D;if(d.op=p==="-"?"-":"+",o.type==="interval-period"){D._fixDefaults(o);const m=new RegExp("^[0-9]{1,}$");if(o.period==="year"||o.period==="month")throw new Error("Year-Month Intervals not supported");if(o.period==="second"){if(!/^[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$/.test(s))throw new Error("Illegal Interval");D._parseSecondsComponent(d,s)}else{if(!m.test(s))throw new Error("Illegal Interval");d[o.period]=parseFloat(s)}}else{if(D._fixDefaults(o.start),D._fixDefaults(o.end),o.start.period==="year"||o.start.period==="month")throw new Error("Year-Month Intervals not supported");if(o.end.period==="year"||o.end.period==="month")throw new Error("Year-Month Intervals not supported");switch(o.start.period){case"day":switch(o.end.period){case"hour":if(i=new RegExp("^[0-9]{1,} [0-9]{1,}$"),!i.test(s))throw new Error("Illegal Interval");d[o.start.period]=parseFloat(s.split(" ")[0]),d[o.end.period]=parseFloat(s.split(" ")[1]);break;case"minute":if(i=new RegExp("^[0-9]{1,} [0-9]{1,2}:[0-9]{1,}$"),!i.test(s))throw new Error("Illegal Interval");{d[o.start.period]=parseFloat(s.split(" ")[0]);const m=s.split(" ")[1].split(":");d.hour=parseFloat(m[0]),d.minute=parseFloat(m[1])}break;case"second":if(i=new RegExp("^[0-9]{1,} [0-9]{1,2}:[0-9]{1,2}:[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$"),!i.test(s))throw new Error("Illegal Interval");{d[o.start.period]=parseFloat(s.split(" ")[0]);const m=s.split(" ")[1].split(":");d.hour=parseFloat(m[0]),d.minute=parseFloat(m[1]),D._parseSecondsComponent(d,m[2])}break;default:throw new Error("Invalid Interval.")}break;case"hour":switch(o.end.period){case"minute":if(i=new RegExp("^[0-9]{1,}:[0-9]{1,}$"),!i.test(s))throw new Error("Illegal Interval");d.hour=parseFloat(s.split(":")[0]),d.minute=parseFloat(s.split(":")[1]);break;case"second":if(i=new RegExp("^[0-9]{1,}:[0-9]{1,2}:[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$"),!i.test(s))throw new Error("Illegal Interval");{const m=s.split(":");d.hour=parseFloat(m[0]),d.minute=parseFloat(m[1]),D._parseSecondsComponent(d,m[2])}break;default:throw new Error("Invalid Interval.")}break;case"minute":if(o.end.period!=="second")throw new Error("Invalid Interval.");if(i=new RegExp("^[0-9]{1,}:[0-9]{1,}([.]{1}[0-9]{1,}){0,1}$"),!i.test(s))throw new Error("Illegal Interval");{const m=s.split(":");d.minute=parseFloat(m[0]),D._parseSecondsComponent(d,m[1])}break;default:throw new Error("Invalid Interval.")}}return d}valueInMilliseconds(){return(this.op==="-"?-1:1)*(this.millis+1e3*this.second+60*this.minute*1e3+60*this.hour*60*1e3+24*this.day*60*60*1e3+this.month*(365/12)*24*60*60*1e3+365*this.year*24*60*60*1e3)}};function _e(l,s){const o=Ut[l.toLowerCase()];if(o==null)throw new Error("Function Not Recognised");if(s.length<o.minParams||s.length>o.maxParams)throw new Error(`Invalid Parameter count for call to ${l.toUpperCase()}`);return o.evaluate(s)}function Jo(l,s){const o=Ut[l.toLowerCase()];return o!=null&&s>=o.minParams&&s<=o.maxParams}function Uo(l){return typeof l=="string"||l instanceof String}function ko(l){return l instanceof Date}function Ct(l){const s=new Date(l.getTime());return s.setHours(0,0,0,0),s}function Ft(l,s){if(ko(l))return s?Ct(l):l;if(Uo(l)){const o=Lo.fromSQL(l);if(o.isValid)return s?Ct(o.toJSDate()):o.toJSDate()}throw new j(C.CannotCastValue)}const Ut={extract:{minParams:2,maxParams:2,evaluate:([l,s])=>{if(s==null)return null;if(s instanceof Date)switch(l.toUpperCase()){case"SECOND":return s.getSeconds();case"MINUTE":return s.getMinutes();case"HOUR":return s.getHours();case"DAY":return s.getDate();case"MONTH":return s.getMonth()+1;case"YEAR":return s.getFullYear()}throw new Error("Invalid Parameter for call to EXTRACT")}},substring:{minParams:2,maxParams:3,evaluate:l=>{if(l.length===2){const[s,o]=l;return s==null||o==null?null:s.toString().substring(o-1)}if(l.length===3){const[s,o,p]=l;return s==null||o==null||p==null?null:p<=0?"":s.toString().substring(o-1,o+p-1)}}},position:{minParams:2,maxParams:2,evaluate:([l,s])=>l==null||s==null?null:s.indexOf(l)+1},trim:{minParams:2,maxParams:3,evaluate:l=>{const s=l.length===3,o=s?l[1]:" ",p=s?l[2]:l[1];if(o==null||p==null)return null;const i=`(${_o(o)})`;switch(l[0]){case"BOTH":return p.replace(new RegExp(`^${i}*|${i}*$`,"g"),"");case"LEADING":return p.replace(new RegExp(`^${i}*`,"g"),"");case"TRAILING":return p.replace(new RegExp(`${i}*$`,"g"),"")}throw new Error("Invalid Parameter for call to TRIM")}},abs:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.abs(l[0])},ceiling:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.ceil(l[0])},floor:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.floor(l[0])},log:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.log(l[0])},log10:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.log(l[0])*Math.LOG10E},sin:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.sin(l[0])},cos:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.cos(l[0])},tan:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.tan(l[0])},asin:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.asin(l[0])},acos:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.acos(l[0])},atan:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.atan(l[0])},sign:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:l[0]>0?1:l[1]<0?-1:0},power:{minParams:2,maxParams:2,evaluate:l=>l[0]==null||l[1]==null?null:l[0]**l[1]},mod:{minParams:2,maxParams:2,evaluate:l=>l[0]==null||l[1]==null?null:l[0]%l[1]},round:{minParams:1,maxParams:2,evaluate:l=>{const s=l[0],o=l.length===2?10**l[1]:1;return s==null?null:Math.round(s*o)/o}},truncate:{minParams:1,maxParams:2,evaluate:l=>l[0]==null?null:l.length===1?parseInt(l[0].toFixed(0),10):parseFloat(l[0].toFixed(l[1]))},char_length:{minParams:1,maxParams:1,evaluate:l=>typeof l[0]=="string"||l[0]instanceof String?l[0].length:0},concat:{minParams:1,maxParams:1/0,evaluate:l=>{let s="";for(let o=0;o<l.length;o++){if(l[o]==null)return null;s+=l[o].toString()}return s}},lower:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:l[0].toString().toLowerCase()},upper:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:l[0].toString().toUpperCase()},coalesce:{minParams:1,maxParams:1/0,evaluate:l=>{for(const s of l)if(s!==null)return s;return null}},cosh:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.cosh(l[0])},sinh:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.sinh(l[0])},tanh:{minParams:1,maxParams:1,evaluate:l=>l[0]==null?null:Math.tanh(l[0])},nullif:{minParams:2,maxParams:2,evaluate:l=>{const s=l[0],o=l[1];return(s instanceof Date?s.valueOf():s)===(o instanceof Date?o.valueOf():o)?null:s}},cast:{minParams:2,maxParams:2,evaluate:l=>{const s=l[0],o=l[1];if(s===null)return null;switch(o.type){case"integer":{const p=parseInt(s,10);if(isNaN(p))throw new j(C.CannotCastValue);return p}case"smallint":{const p=parseInt(s,10);if(isNaN(p))throw new j(C.CannotCastValue);if(p>32767||p<-32767)throw new j(C.CannotCastValue);return p}case"float":case"real":{const p=parseFloat(s);if(isNaN(p))throw new j(C.CannotCastValue);return p}case"date":return Ft(s,!0);case"timestamp":return Ft(s,!1);case"varchar":{const p=s.toString();if(p.length>o.size)throw new j(C.CannotCastValue);return p}default:throw new j(C.InvalidDataType)}}}};var Pt,_t,Se={};_t=function(){function l(i,d){function m(){this.constructor=i}m.prototype=d.prototype,i.prototype=new m}function s(i,d,m,t){var N=Error.call(this,i);return Object.setPrototypeOf&&Object.setPrototypeOf(N,s.prototype),N.expected=d,N.found=m,N.location=t,N.name="SyntaxError",N}function o(i,d,m){return m=m||" ",i.length>d?i:(d-=i.length,i+(m+=m.repeat(d)).slice(0,d))}function p(i,d){var m,t={},N=(d=d!==void 0?d:{}).grammarSource,T={start:Ye},_=Ye,q="!",O="=",b=">=",S=">",L="<=",E="<>",Gt="<",Me="!=",fe="+",de="-",Re="||",Wt="*",Yt="/",Kt="@",De="'",Oe="N'",X="''",Qt=".",Zt="null",Xt="true",en="false",tn="in",nn="is",rn="like",an="escape",sn="not",un="and",on="or",ln="between",cn="from",pn="for",fn="substring",dn="extract",vn="trim",hn="position",mn="timestamp",gn="date",yn="leading",wn="trailing",Nn="both",In="cast",bn="as",Tn="integer",xn="smallint",An="float",Sn="real",En="varchar",Cn="to",Fn="interval",Pn="year",_n="month",Ln="day",Mn="hour",Rn="minute",Dn="second",On="case",$n="end",Jn="when",Un="then",kn="else",qn=",",Vn="(",Hn=")",$e="`",Bn=/^[A-Za-z_\x80-\uFFFF]/,jn=/^[A-Za-z0-9_]/,zn=/^[A-Za-z0-9_.\x80-\uFFFF]/,Je=/^[^']/,Gn=/^[0-9]/,Wn=/^[eE]/,Yn=/^[+\-]/,Kn=/^[ \t\n\r]/,Ue=/^[^`]/,Qn=g("!",!1),ke=g("=",!1),Zn=g(">=",!1),Xn=g(">",!1),er=g("<=",!1),tr=g("<>",!1),nr=g("<",!1),rr=g("!=",!1),ve=g("+",!1),he=g("-",!1),ar=g("||",!1),sr=g("*",!1),ur=g("/",!1),or=$([["A","Z"],["a","z"],"_",["","￿"]],!1,!1),ir=$([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),lr=$([["A","Z"],["a","z"],["0","9"],"_",".",["","￿"]],!1,!1),cr=g("@",!1),qe=g("'",!1),pr=g("N'",!1),Ve=g("''",!1),He=$(["'"],!0,!1),fr=g(".",!1),dr=$([["0","9"]],!1,!1),vr=$(["e","E"],!1,!1),hr=$(["+","-"],!1,!1),mr=g("NULL",!0),gr=g("TRUE",!0),yr=g("FALSE",!0),wr=g("IN",!0),Nr=g("IS",!0),Ir=g("LIKE",!0),br=g("ESCAPE",!0),Tr=g("NOT",!0),xr=g("AND",!0),Ar=g("OR",!0),Sr=g("BETWEEN",!0),Er=g("FROM",!0),Cr=g("FOR",!0),Fr=g("SUBSTRING",!0),Pr=g("EXTRACT",!0),_r=g("TRIM",!0),Lr=g("POSITION",!0),Mr=g("TIMESTAMP",!0),Rr=g("DATE",!0),Dr=g("LEADING",!0),Or=g("TRAILING",!0),$r=g("BOTH",!0),Jr=g("CAST",!0),Ur=g("AS",!0),kr=g("INTEGER",!0),qr=g("SMALLINT",!0),Vr=g("FLOAT",!0),Hr=g("REAL",!0),Br=g("VARCHAR",!0),jr=g("TO",!0),zr=g("INTERVAL",!0),Gr=g("YEAR",!0),Wr=g("MONTH",!0),Yr=g("DAY",!0),Kr=g("HOUR",!0),Qr=g("MINUTE",!0),Zr=g("SECOND",!0),Xr=g("CASE",!0),ea=g("END",!0),ta=g("WHEN",!0),na=g("THEN",!0),ra=g("ELSE",!0),aa=g(",",!1),sa=g("(",!1),ua=g(")",!1),oa=$([" ","	",`
`,"\r"],!1,!1),Be=g("`",!1),je=$(["`"],!0,!1),ia=function(e){return e},la=function(e,r){var a={type:"expression-list"},u=Po(e,r);return a.value=u,a},ca=function(e,r){return Q(e,r)},pa=function(e,r){return Q(e,r)},fa=function(e){return Co("NOT",e)},da=function(e,r){return r==""||r==null||r==null?e:r.type=="arithmetic"?Q(e,r.tail):At(r.op,e,r.right,r.escape)},va=function(e){return{type:"arithmetic",tail:e}},ha=function(e,r){return{op:e+"NOT",right:r}},ma=function(e,r){return{op:e,right:r}},ga=function(e,r,a){return{op:"NOT"+e,right:{type:"expression-list",value:[r,a]}}},ya=function(e,r,a){return{op:e,right:{type:"expression-list",value:[r,a]}}},wa=function(e){return e[0]+" "+e[2]},Na=function(e){return e[0]+" "+e[2]},Ia=function(e,r,a){return{op:e,right:r,escape:a.value}},ba=function(e,r){return{op:e,right:r,escape:""}},Ta=function(e,r){return{op:e,right:r}},xa=function(e){return{op:e,right:{type:"expression-list",value:[]}}},Aa=function(e,r){return{op:e,right:r}},Sa=function(e,r){return Q(e,r)},Ea=function(e,r){return Q(e,r)},Ca=function(e){return e.paren=!0,e},Fa=function(e){return/^CURRENT_DATE$/i.test(e)?{type:"current-time",mode:"date"}:/^CURRENT_TIMESTAMP$/i.test(e)?{type:"current-time",mode:"timestamp"}:{type:"column-reference",table:"",column:e}},Pa=function(e){return e},_a=function(e,r){return e+r.join("")},La=function(e,r){return e+r.join("")},Ma=function(e){return{type:"parameter",value:e[1]}},Ra=function(e,r){return{type:"function",name:"extract",args:{type:"expression-list",value:[{type:"string",value:e},r]}}},Da=function(e,r,a){return{type:"function",name:"substring",args:{type:"expression-list",value:a?[e,r,a[2]]:[e,r]}}},Oa=function(e,r){return{type:"function",name:"cast",args:{type:"expression-list",value:[e,r]}}},$a=function(){return{type:"data-type",value:{type:"integer"}}},Ja=function(){return{type:"data-type",value:{type:"smallint"}}},Ua=function(){return{type:"data-type",value:{type:"float"}}},ka=function(){return{type:"data-type",value:{type:"real"}}},qa=function(){return{type:"data-type",value:{type:"date"}}},Va=function(){return{type:"data-type",value:{type:"timestamp"}}},Ha=function(e){return{type:"data-type",value:{type:"varchar",size:parseInt(e)}}},Ba=function(e,r,a){return{type:"function",name:"trim",args:{type:"expression-list",value:[{type:"string",value:e??"BOTH"},r,a]}}},ja=function(e,r){return{type:"function",name:"trim",args:{type:"expression-list",value:[{type:"string",value:e??"BOTH"},r]}}},za=function(e,r){return{type:"function",name:"position",args:{type:"expression-list",value:[e,r]}}},Ga=function(e,r){return{type:"function",name:e,args:r||{type:"expression-list",value:[]}}},Wa=function(e){return{type:"timestamp",value:e.value}},Ya=function(e,r,a){return{type:"interval",value:r,qualifier:a,op:e}},Ka=function(e,r){return{type:"interval",value:e,qualifier:r,op:""}},Qa=function(e,r){return{type:"interval-qualifier",start:e,end:r}},Za=function(e,r){return{type:"interval-period",period:e.value,precision:r,secondary:null}},Xa=function(e){return{type:"interval-period",period:e.value,precision:null,secondary:null}},es=function(e){return{type:"interval-period",period:e.value,precision:null,secondary:null}},ts=function(e,r){return{type:"interval-period",period:"second",precision:e,secondary:r}},ns=function(e){return{type:"interval-period",period:"second",precision:e,secondary:null}},rs=function(){return{type:"interval-period",period:"second",precision:null,secondary:null}},as=function(e,r){return{type:"interval-period",period:e.value,precision:r,secondary:null}},ss=function(e){return{type:"interval-period",period:e.value,precision:null,secondary:null}},us=function(e,r){return{type:"interval-period",period:"second",precision:e,secondary:r}},os=function(e){return{type:"interval-period",period:"second",precision:e,secondary:null}},is=function(){return{type:"interval-period",period:"second",precision:null,secondary:null}},ls=function(){return{type:"string",value:"day"}},cs=function(){return{type:"string",value:"hour"}},ps=function(){return{type:"string",value:"minute"}},fs=function(){return{type:"string",value:"month"}},ds=function(){return{type:"string",value:"year"}},vs=function(e){return parseFloat(e)},hs=function(e){return parseFloat(e)},ms=function(e){return{type:"date",value:e.value}},gs=function(){return{type:"null",value:null}},ys=function(){return{type:"boolean",value:!0}},ws=function(){return{type:"boolean",value:!1}},ze=function(){return"'"},Ns=function(e){return{type:"string",value:e.join("")}},Is=function(e,r){return{type:"case-expression",format:"simple",operand:e,clauses:r,else:null}},bs=function(e,r,a){return{type:"case-expression",format:"simple",operand:e,clauses:r,else:a.value}},Ts=function(e){return{type:"case-expression",format:"searched",clauses:e,else:null}},xs=function(e,r){return{type:"case-expression",format:"searched",clauses:e,else:r.value}},As=function(e,r){return{type:"when-clause",operand:e,value:r}},Ss=function(e,r){return{type:"when-clause",operand:e,value:r}},Es=function(e){return{type:"else-clause",value:e}},Cs=function(e){return{type:"number",value:e}},Fs=function(e,r,a){return parseFloat(e+r+a)},Ps=function(e,r){return parseFloat(e+r)},_s=function(e,r){return parseFloat(e+r)},Ls=function(e){return parseFloat(e)},Ms=function(e,r){return e[0]+r},Rs=function(e){return"."+(e??"")},Ds=function(e,r){return e+r},Os=function(e){return e.join("")},$s=function(e,r){return"e"+(r===null?"":r)},Js=function(){return"IN"},Us=function(){return"IS"},ks=function(){return"LIKE"},qs=function(){return"ESCAPE"},Vs=function(){return"NOT"},Hs=function(){return"AND"},Bs=function(){return"OR"},js=function(){return"BETWEEN"},zs=function(){return"FROM"},Gs=function(){return"FOR"},Ws=function(){return"SUBSTRING"},Ys=function(){return"EXTRACT"},Ks=function(){return"TRIM"},Qs=function(){return"POSITION"},Zs=function(){return"TIMESTAMP"},Xs=function(){return"DATE"},eu=function(){return"LEADING"},tu=function(){return"TRAILING"},nu=function(){return"BOTH"},ru=function(){return"CAST"},au=function(){return"AS"},su=function(){return"INTEGER"},uu=function(){return"SMALLINT"},ou=function(){return"FLOAT"},iu=function(){return"REAL"},lu=function(){return"VARCHAR"},cu=function(){return"TO"},pu=function(){return"INTERVAL"},fu=function(){return"YEAR"},du=function(){return"MONTH"},vu=function(){return"DAY"},hu=function(){return"HOUR"},mu=function(){return"MINUTE"},gu=function(){return"SECOND"},yu=function(){return"CASE"},wu=function(){return"END"},Nu=function(){return"WHEN"},Iu=function(){return"THEN"},bu=function(){return"ELSE"},Tu=function(e){return e},xu=function(e){return e.join("")},n=0,ee=[{line:1,column:1}],R=0,me=[],c=0;if("startRule"in d){if(!(d.startRule in T))throw new Error(`Can't start parsing from rule "`+d.startRule+'".');_=T[d.startRule]}function g(e,r){return{type:"literal",text:e,ignoreCase:r}}function $(e,r,a){return{type:"class",parts:e,inverted:r,ignoreCase:a}}function Au(){return{type:"end"}}function Ge(e){var r,a=ee[e];if(a)return a;for(r=e-1;!ee[r];)r--;for(a={line:(a=ee[r]).line,column:a.column};r<e;)i.charCodeAt(r)===10?(a.line++,a.column=1):a.column++,r++;return ee[e]=a,a}function We(e,r){var a=Ge(e),u=Ge(r);return{source:N,start:{offset:e,line:a.line,column:a.column},end:{offset:r,line:u.line,column:u.column}}}function v(e){n<R||(n>R&&(R=n,me=[]),me.push(e))}function Su(e,r,a){return new s(s.buildMessage(e,r),e,r,a)}function Ye(){var e,r;return e=n,f(),(r=A())!==t?(f(),e=ia(r)):(n=e,e=t),e}function Ke(){var e,r,a,u,h,w,I,x;if(e=n,(r=A())!==t){for(a=[],u=n,h=f(),(w=ce())!==t?(I=f(),(x=A())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);u!==t;)a.push(u),u=n,h=f(),(w=ce())!==t?(I=f(),(x=A())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);e=la(r,a)}else n=e,e=t;return e}function A(){var e,r,a,u,h,w,I,x;if(e=n,(r=ge())!==t){for(a=[],u=n,h=f(),(w=pt())!==t?(I=f(),(x=ge())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);u!==t;)a.push(u),u=n,h=f(),(w=pt())!==t?(I=f(),(x=ge())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);e=ca(r,a)}else n=e,e=t;return e}function ge(){var e,r,a,u,h,w,I,x;if(e=n,(r=te())!==t){for(a=[],u=n,h=f(),(w=oe())!==t?(I=f(),(x=te())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);u!==t;)a.push(u),u=n,h=f(),(w=oe())!==t?(I=f(),(x=te())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);e=pa(r,a)}else n=e,e=t;return e}function te(){var e,r,a,u,h;return e=n,(r=K())===t&&(r=n,i.charCodeAt(n)===33?(a=q,n++):(a=t,c===0&&v(Qn)),a!==t?(u=n,c++,i.charCodeAt(n)===61?(h=O,n++):(h=t,c===0&&v(ke)),c--,h===t?u=void 0:(n=u,u=t),u!==t?r=a=[a,u]:(n=r,r=t)):(n=r,r=t)),r!==t?(a=f(),(u=te())!==t?e=fa(u):(n=e,e=t)):(n=e,e=t),e===t&&(e=Eu()),e}function Eu(){var e,r,a;return e=n,(r=J())!==t?(f(),(a=Cu())===t&&(a=null),e=da(r,a)):(n=e,e=t),e}function Cu(){var e;return(e=Fu())===t&&(e=Mu())===t&&(e=_u())===t&&(e=Pu())===t&&(e=Lu()),e}function Fu(){var e,r,a,u,h,w;if(e=[],r=n,a=f(),(u=Qe())!==t?(h=f(),(w=J())!==t?r=a=[a,u,h,w]:(n=r,r=t)):(n=r,r=t),r!==t)for(;r!==t;)e.push(r),r=n,a=f(),(u=Qe())!==t?(h=f(),(w=J())!==t?r=a=[a,u,h,w]:(n=r,r=t)):(n=r,r=t);else e=t;return e!==t&&(e=va(e)),e}function Qe(){var e;return i.substr(n,2)===b?(e=b,n+=2):(e=t,c===0&&v(Zn)),e===t&&(i.charCodeAt(n)===62?(e=S,n++):(e=t,c===0&&v(Xn)),e===t&&(i.substr(n,2)===L?(e=L,n+=2):(e=t,c===0&&v(er)),e===t&&(i.substr(n,2)===E?(e=E,n+=2):(e=t,c===0&&v(tr)),e===t&&(i.charCodeAt(n)===60?(e=Gt,n++):(e=t,c===0&&v(nr)),e===t&&(i.charCodeAt(n)===61?(e=O,n++):(e=t,c===0&&v(ke)),e===t&&(i.substr(n,2)===Me?(e=Me,n+=2):(e=t,c===0&&v(rr)))))))),e}function Pu(){var e,r,a,u;return e=n,(r=lt())!==t?(f(),(a=K())!==t?(f(),(u=J())!==t?e=ha(r,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=lt())!==t?(f(),(a=J())!==t?e=ma(r,a):(n=e,e=t)):(n=e,e=t)),e}function _u(){var e,r,a,u,h,w;return e=n,(r=K())!==t?(f(),(a=ft())!==t?(f(),(u=J())!==t?(f(),(h=oe())!==t?(f(),(w=J())!==t?e=ga(a,u,w):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=ft())!==t?(f(),(a=J())!==t?(f(),(u=oe())!==t?(f(),(h=J())!==t?e=ya(r,a,h):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)),e}function Ze(){var e,r,a,u,h;return e=n,r=n,(a=K())!==t?(u=f(),(h=ct())!==t?r=a=[a,u,h]:(n=r,r=t)):(n=r,r=t),r!==t&&(r=wa(r)),(e=r)===t&&(e=ct()),e}function ye(){var e,r,a,u,h;return e=n,r=n,(a=K())!==t?(u=f(),(h=xe())!==t?r=a=[a,u,h]:(n=r,r=t)):(n=r,r=t),r!==t&&(r=Na(r)),(e=r)===t&&(e=xe()),e}function Lu(){var e,r,a,u;return e=n,(r=Ze())!==t?(f(),(a=W())!==t?(f(),lo()!==t?(f(),(u=Te())!==t?e=Ia(r,a,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=Ze())!==t?(f(),(a=W())!==t?e=ba(r,a):(n=e,e=t)):(n=e,e=t)),e}function Mu(){var e,r,a,u;return e=n,(r=ye())!==t?(f(),(a=F())!==t?(f(),(u=Ke())!==t?(f(),P()!==t?e=Ta(r,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=ye())!==t?(f(),(a=F())!==t?(f(),(u=P())!==t?e=xa(r):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=ye())!==t?(f(),(a=be())!==t?e=Aa(r,a):(n=e,e=t)):(n=e,e=t))),e}function J(){var e,r,a,u,h,w,I,x;if(e=n,(r=we())!==t){for(a=[],u=n,h=f(),(w=Xe())!==t?(I=f(),(x=we())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);u!==t;)a.push(u),u=n,h=f(),(w=Xe())!==t?(I=f(),(x=we())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);e=Sa(r,a)}else n=e,e=t;return e}function Xe(){var e;return i.charCodeAt(n)===43?(e=fe,n++):(e=t,c===0&&v(ve)),e===t&&(i.charCodeAt(n)===45?(e=de,n++):(e=t,c===0&&v(he)),e===t&&(i.substr(n,2)===Re?(e=Re,n+=2):(e=t,c===0&&v(ar)))),e}function we(){var e,r,a,u,h,w,I,x;if(e=n,(r=Ne())!==t){for(a=[],u=n,h=f(),(w=et())!==t?(I=f(),(x=Ne())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);u!==t;)a.push(u),u=n,h=f(),(w=et())!==t?(I=f(),(x=Ne())!==t?u=h=[h,w,I,x]:(n=u,u=t)):(n=u,u=t);e=Ea(r,a)}else n=e,e=t;return e}function et(){var e;return i.charCodeAt(n)===42?(e=Wt,n++):(e=t,c===0&&v(sr)),e===t&&(i.charCodeAt(n)===47?(e=Yt,n++):(e=t,c===0&&v(ur))),e}function Ne(){var e,r;return(e=ju())===t&&(e=$u())===t&&(e=Ju())===t&&(e=qu())===t&&(e=Vu())===t&&(e=Uu())===t&&(e=Hu())===t&&(e=eo())===t&&(e=Ru())===t&&(e=be())===t&&(e=n,F()!==t?(f(),(r=A())!==t?(f(),P()!==t?e=Ca(r):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)),e}function Ru(){var e;return(e=Du())!==t&&(e=Fa(e)),e}function Du(){var e;return(e=Ou())!==t&&(e=Pa(e)),e}function Ou(){var e,r,a,u;if(e=n,(r=Ie())!==t){for(a=[],u=nt();u!==t;)a.push(u),u=nt();e=_a(r,a)}else n=e,e=t;return e}function tt(){var e,r,a,u;if(e=n,(r=Ie())!==t){for(a=[],u=y();u!==t;)a.push(u),u=y();e=La(r,a)}else n=e,e=t;return e}function Ie(){var e;return Bn.test(i.charAt(n))?(e=i.charAt(n),n++):(e=t,c===0&&v(or)),e}function y(){var e;return jn.test(i.charAt(n))?(e=i.charAt(n),n++):(e=t,c===0&&v(ir)),e}function nt(){var e;return zn.test(i.charAt(n))?(e=i.charAt(n),n++):(e=t,c===0&&v(lr)),e}function be(){var e,r,a;return e=n,i.charCodeAt(n)===64?(r=Kt,n++):(r=t,c===0&&v(cr)),r!==t&&(a=tt())!==t?e=r=[r,a]:(n=e,e=t),e!==t&&(e=Ma(e)),e}function $u(){var e,r,a;return e=n,fo()!==t?(f(),F()!==t?(f(),(r=Bu())!==t?(f(),Ae()!==t?(f(),(a=A())!==t?(f(),P()!==t?e=Ra(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function Ju(){var e,r,a,u,h,w,I;return e=n,po()!==t?(f(),F()!==t?(f(),(r=A())!==t?(f(),Ae()!==t?(f(),(a=A())!==t?(f(),u=n,(h=co())!==t?(w=f(),(I=A())!==t?u=h=[h,w,I,f()]:(n=u,u=t)):(n=u,u=t),u===t&&(u=null),(h=P())!==t?e=Da(r,a,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function Uu(){var e,r,a;return e=n,yo()!==t?(f(),F()!==t?(f(),(r=A())!==t?(f(),wo()!==t?(f(),(a=ku())!==t?(f(),P()!==t?e=Oa(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function ku(){var e,r,a;return e=n,(r=No())!==t&&(r=$a()),(e=r)===t&&(e=n,(r=Io())!==t&&(r=Ja()),(e=r)===t&&(e=n,(r=bo())!==t&&(r=Ua()),(e=r)===t&&(e=n,(r=To())!==t&&(r=ka()),(e=r)===t&&(e=n,(r=ht())!==t&&(r=qa()),(e=r)===t&&(e=n,(r=vt())!==t&&(r=Va()),(e=r)===t&&(e=n,(r=xo())!==t?(f(),F()!==t?(f(),(a=V())!==t?(f(),P()!==t?e=Ha(a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t))))))),e}function qu(){var e,r,a,u;return e=n,dt()!==t?(f(),F()!==t?(f(),(r=rt())===t&&(r=null),f(),(a=A())!==t?(f(),Ae()!==t?(f(),(u=A())!==t?(f(),P()!==t?e=Ba(r,a,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,dt()!==t?(f(),F()!==t?(f(),(r=rt())===t&&(r=null),f(),(a=A())!==t?(f(),P()!==t?e=ja(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)),e}function rt(){var e;return(e=ho())===t&&(e=mo())===t&&(e=go()),e}function Vu(){var e,r,a;return e=n,vo()!==t?(f(),F()!==t?(f(),(r=A())!==t?(f(),xe()!==t?(f(),(a=A())!==t?(f(),P()!==t?e=za(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function Hu(){var e,r,a;return e=n,(r=Eo())!==t?(f(),F()!==t?(f(),(a=Ke())===t&&(a=null),f(),P()!==t?e=Ga(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function Bu(){var e;return(e=gt())===t&&(e=yt())===t&&(e=wt())===t&&(e=Nt())===t&&(e=It())===t&&(e=H()),e}function ju(){var e;return(e=Te())===t&&(e=ro())===t&&(e=Xu())===t&&(e=Zu())===t&&(e=Qu())===t&&(e=zu())===t&&(e=Gu()),e}function zu(){var e,r;return e=n,vt()!==t?(f(),(r=W())!==t?e=Wa(r):(n=e,e=t)):(n=e,e=t),e}function Gu(){var e,r,a,u;return e=n,mt()!==t?(f(),i.charCodeAt(n)===45?(r=de,n++):(r=t,c===0&&v(he)),r===t&&(i.charCodeAt(n)===43?(r=fe,n++):(r=t,c===0&&v(ve))),r!==t?(f(),(a=W())!==t?(f(),(u=at())!==t?e=Ya(r,a,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,mt()!==t?(f(),(r=W())!==t?(f(),(a=at())!==t?e=Ka(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)),e}function at(){var e,r,a;return e=n,(r=Wu())!==t?(f(),Ao()!==t?(f(),(a=Yu())!==t?e=Qa(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=Ku()),e}function Wu(){var e,r,a;return e=n,(r=Y())!==t?(f(),F()!==t?(f(),(a=re())!==t?(f(),P()!==t?e=Za(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=Y())!==t&&(r=Xa(r)),e=r),e}function Yu(){var e,r,a,u;return e=n,(r=Y())!==t&&(r=es(r)),(e=r)===t&&(e=n,(r=H())!==t?(f(),F()!==t?(f(),(a=re())!==t?(f(),ce()!==t?(f(),(u=ne())!==t?(f(),P()!==t?e=ts(a,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=H())!==t?(f(),F()!==t?(f(),(a=re())!==t?(f(),P()!==t?e=ns(a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=H())!==t&&(r=rs()),e=r))),e}function Ku(){var e,r,a,u;return e=n,(r=Y())!==t?(f(),F()!==t?(f(),(a=ne())!==t?(f(),P()!==t?e=as(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=Y())!==t&&(r=ss(r)),(e=r)===t&&(e=n,(r=H())!==t?(f(),F()!==t?(f(),(a=re())!==t?(f(),ce()!==t?(f(),(u=ne())!==t?(f(),P()!==t?e=us(a,u):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=H())!==t?(f(),F()!==t?(f(),(a=ne())!==t?(f(),P()!==t?e=os(a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e===t&&(e=n,(r=H())!==t&&(r=is()),e=r)))),e}function Y(){var e,r;return e=n,(r=wt())!==t&&(r=ls()),(e=r)===t&&(e=n,(r=Nt())!==t&&(r=cs()),(e=r)===t&&(e=n,(r=It())!==t&&(r=ps()),(e=r)===t&&(e=n,(r=yt())!==t&&(r=fs()),(e=r)===t&&(e=n,(r=gt())!==t&&(r=ds()),e=r)))),e}function ne(){var e;return(e=V())!==t&&(e=vs(e)),e}function re(){var e;return(e=V())!==t&&(e=hs(e)),e}function Qu(){var e,r;return e=n,ht()!==t?(f(),(r=W())!==t?e=ms(r):(n=e,e=t)):(n=e,e=t),e}function Zu(){var e;return(e=uo())!==t&&(e=gs()),e}function Xu(){var e,r;return e=n,(r=oo())!==t&&(r=ys()),(e=r)===t&&(e=n,(r=io())!==t&&(r=ws()),e=r),e}function W(){var e;return(e=Te())===t&&(e=be()),e}function Te(){var e,r,a,u,h;if(e=n,i.charCodeAt(n)===39?(r=De,n++):(r=t,c===0&&v(qe)),r===t&&(i.substr(n,2)===Oe?(r=Oe,n+=2):(r=t,c===0&&v(pr))),r!==t){for(a=[],u=n,i.substr(n,2)===X?(h=X,n+=2):(h=t,c===0&&v(Ve)),h!==t&&(h=ze()),(u=h)===t&&(Je.test(i.charAt(n))?(u=i.charAt(n),n++):(u=t,c===0&&v(He)));u!==t;)a.push(u),u=n,i.substr(n,2)===X?(h=X,n+=2):(h=t,c===0&&v(Ve)),h!==t&&(h=ze()),(u=h)===t&&(Je.test(i.charAt(n))?(u=i.charAt(n),n++):(u=t,c===0&&v(He)));i.charCodeAt(n)===39?(u=De,n++):(u=t,c===0&&v(qe)),u!==t?e=Ns(a):(n=e,e=t)}else n=e,e=t;return e}function eo(){var e;return(e=to())===t&&(e=no()),e}function to(){var e,r,a,u,h;if(e=n,ie()!==t)if(f(),(r=A())!==t){for(f(),a=[],u=se();u!==t;)a.push(u),u=se();u=f(),(h=le())!==t?e=Is(r,a):(n=e,e=t)}else n=e,e=t;else n=e,e=t;if(e===t)if(e=n,ie()!==t)if(f(),(r=A())!==t){for(f(),a=[],u=se();u!==t;)a.push(u),u=se();u=f(),(h=st())!==t?(f(),le()!==t?e=bs(r,a,h):(n=e,e=t)):(n=e,e=t)}else n=e,e=t;else n=e,e=t;return e}function no(){var e,r,a,u;if(e=n,ie()!==t){for(f(),r=[],a=ae();a!==t;)r.push(a),a=ae();a=f(),(u=le())!==t?e=Ts(r):(n=e,e=t)}else n=e,e=t;if(e===t)if(e=n,ie()!==t){for(f(),r=[],a=ae();a!==t;)r.push(a),a=ae();a=f(),(u=st())!==t?(f(),le()!==t?e=xs(r,u):(n=e,e=t)):(n=e,e=t)}else n=e,e=t;return e}function ae(){var e,r,a;return e=n,bt()!==t?(f(),(r=A())!==t?(f(),Tt()!==t?(f(),(a=A())!==t?e=As(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function se(){var e,r,a;return e=n,bt()!==t?(f(),(r=A())!==t?(f(),Tt()!==t?(f(),(a=A())!==t?e=Ss(r,a):(n=e,e=t)):(n=e,e=t)):(n=e,e=t)):(n=e,e=t),e}function st(){var e,r;return e=n,So()!==t?(f(),(r=A())!==t?e=Es(r):(n=e,e=t)):(n=e,e=t),e}function ro(){var e,r,a,u;return e=n,(r=ao())!==t?(a=n,c++,u=Ie(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Cs(r):(n=e,e=t)):(n=e,e=t),e}function ao(){var e,r,a,u;return e=n,(r=ue())!==t&&(a=ut())!==t&&(u=ot())!==t?e=Fs(r,a,u):(n=e,e=t),e===t&&(e=n,(r=ue())!==t&&(a=ut())!==t?e=Ps(r,a):(n=e,e=t),e===t&&(e=n,(r=ue())!==t&&(a=ot())!==t?e=_s(r,a):(n=e,e=t),e===t&&(e=n,(r=ue())!==t&&(r=Ls(r)),e=r))),e}function ue(){var e,r,a;return(e=V())===t&&(e=n,i.charCodeAt(n)===45?(r=de,n++):(r=t,c===0&&v(he)),r===t&&(i.charCodeAt(n)===43?(r=fe,n++):(r=t,c===0&&v(ve))),r!==t&&(a=V())!==t?e=Ms(r,a):(n=e,e=t)),e}function ut(){var e,r,a;return e=n,i.charCodeAt(n)===46?(r=Qt,n++):(r=t,c===0&&v(fr)),r!==t?((a=V())===t&&(a=null),e=Rs(a)):(n=e,e=t),e}function ot(){var e,r,a;return e=n,(r=so())!==t&&(a=V())!==t?e=Ds(r,a):(n=e,e=t),e}function V(){var e,r;if(e=[],(r=it())!==t)for(;r!==t;)e.push(r),r=it();else e=t;return e!==t&&(e=Os(e)),e}function it(){var e;return Gn.test(i.charAt(n))?(e=i.charAt(n),n++):(e=t,c===0&&v(dr)),e}function so(){var e,r,a;return e=n,Wn.test(i.charAt(n))?(r=i.charAt(n),n++):(r=t,c===0&&v(vr)),r!==t?(Yn.test(i.charAt(n))?(a=i.charAt(n),n++):(a=t,c===0&&v(hr)),a===t&&(a=null),e=$s(r,a)):(n=e,e=t),e}function uo(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Zt?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(mr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=r=[r,a]:(n=e,e=t)):(n=e,e=t),e}function oo(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Xt?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(gr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=r=[r,a]:(n=e,e=t)):(n=e,e=t),e}function io(){var e,r,a,u;return e=n,i.substr(n,5).toLowerCase()===en?(r=i.substr(n,5),n+=5):(r=t,c===0&&v(yr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=r=[r,a]:(n=e,e=t)):(n=e,e=t),e}function xe(){var e,r,a,u;return e=n,i.substr(n,2).toLowerCase()===tn?(r=i.substr(n,2),n+=2):(r=t,c===0&&v(wr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Js():(n=e,e=t)):(n=e,e=t),e}function lt(){var e,r,a,u;return e=n,i.substr(n,2).toLowerCase()===nn?(r=i.substr(n,2),n+=2):(r=t,c===0&&v(Nr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Us():(n=e,e=t)):(n=e,e=t),e}function ct(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===rn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Ir)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=ks():(n=e,e=t)):(n=e,e=t),e}function lo(){var e,r,a,u;return e=n,i.substr(n,6).toLowerCase()===an?(r=i.substr(n,6),n+=6):(r=t,c===0&&v(br)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=qs():(n=e,e=t)):(n=e,e=t),e}function K(){var e,r,a,u;return e=n,i.substr(n,3).toLowerCase()===sn?(r=i.substr(n,3),n+=3):(r=t,c===0&&v(Tr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Vs():(n=e,e=t)):(n=e,e=t),e}function oe(){var e,r,a,u;return e=n,i.substr(n,3).toLowerCase()===un?(r=i.substr(n,3),n+=3):(r=t,c===0&&v(xr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Hs():(n=e,e=t)):(n=e,e=t),e}function pt(){var e,r,a,u;return e=n,i.substr(n,2).toLowerCase()===on?(r=i.substr(n,2),n+=2):(r=t,c===0&&v(Ar)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Bs():(n=e,e=t)):(n=e,e=t),e}function ft(){var e,r,a,u;return e=n,i.substr(n,7).toLowerCase()===ln?(r=i.substr(n,7),n+=7):(r=t,c===0&&v(Sr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=js():(n=e,e=t)):(n=e,e=t),e}function Ae(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===cn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Er)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=zs():(n=e,e=t)):(n=e,e=t),e}function co(){var e,r,a,u;return e=n,i.substr(n,3).toLowerCase()===pn?(r=i.substr(n,3),n+=3):(r=t,c===0&&v(Cr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Gs():(n=e,e=t)):(n=e,e=t),e}function po(){var e,r,a,u;return e=n,i.substr(n,9).toLowerCase()===fn?(r=i.substr(n,9),n+=9):(r=t,c===0&&v(Fr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Ws():(n=e,e=t)):(n=e,e=t),e}function fo(){var e,r,a,u;return e=n,i.substr(n,7).toLowerCase()===dn?(r=i.substr(n,7),n+=7):(r=t,c===0&&v(Pr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Ys():(n=e,e=t)):(n=e,e=t),e}function dt(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===vn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(_r)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Ks():(n=e,e=t)):(n=e,e=t),e}function vo(){var e,r,a,u;return e=n,i.substr(n,8).toLowerCase()===hn?(r=i.substr(n,8),n+=8):(r=t,c===0&&v(Lr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Qs():(n=e,e=t)):(n=e,e=t),e}function vt(){var e,r,a,u;return e=n,i.substr(n,9).toLowerCase()===mn?(r=i.substr(n,9),n+=9):(r=t,c===0&&v(Mr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Zs():(n=e,e=t)):(n=e,e=t),e}function ht(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===gn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Rr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Xs():(n=e,e=t)):(n=e,e=t),e}function ho(){var e,r,a,u;return e=n,i.substr(n,7).toLowerCase()===yn?(r=i.substr(n,7),n+=7):(r=t,c===0&&v(Dr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=eu():(n=e,e=t)):(n=e,e=t),e}function mo(){var e,r,a,u;return e=n,i.substr(n,8).toLowerCase()===wn?(r=i.substr(n,8),n+=8):(r=t,c===0&&v(Or)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=tu():(n=e,e=t)):(n=e,e=t),e}function go(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Nn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v($r)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=nu():(n=e,e=t)):(n=e,e=t),e}function yo(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===In?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Jr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=ru():(n=e,e=t)):(n=e,e=t),e}function wo(){var e,r,a,u;return e=n,i.substr(n,2).toLowerCase()===bn?(r=i.substr(n,2),n+=2):(r=t,c===0&&v(Ur)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=au():(n=e,e=t)):(n=e,e=t),e}function No(){var e,r,a,u;return e=n,i.substr(n,7).toLowerCase()===Tn?(r=i.substr(n,7),n+=7):(r=t,c===0&&v(kr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=su():(n=e,e=t)):(n=e,e=t),e}function Io(){var e,r,a,u;return e=n,i.substr(n,8).toLowerCase()===xn?(r=i.substr(n,8),n+=8):(r=t,c===0&&v(qr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=uu():(n=e,e=t)):(n=e,e=t),e}function bo(){var e,r,a,u;return e=n,i.substr(n,5).toLowerCase()===An?(r=i.substr(n,5),n+=5):(r=t,c===0&&v(Vr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=ou():(n=e,e=t)):(n=e,e=t),e}function To(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Sn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Hr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=iu():(n=e,e=t)):(n=e,e=t),e}function xo(){var e,r,a,u;return e=n,i.substr(n,7).toLowerCase()===En?(r=i.substr(n,7),n+=7):(r=t,c===0&&v(Br)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=lu():(n=e,e=t)):(n=e,e=t),e}function Ao(){var e,r,a,u;return e=n,i.substr(n,2).toLowerCase()===Cn?(r=i.substr(n,2),n+=2):(r=t,c===0&&v(jr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=cu():(n=e,e=t)):(n=e,e=t),e}function mt(){var e,r,a,u;return e=n,i.substr(n,8).toLowerCase()===Fn?(r=i.substr(n,8),n+=8):(r=t,c===0&&v(zr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=pu():(n=e,e=t)):(n=e,e=t),e}function gt(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Pn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Gr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=fu():(n=e,e=t)):(n=e,e=t),e}function yt(){var e,r,a,u;return e=n,i.substr(n,5).toLowerCase()===_n?(r=i.substr(n,5),n+=5):(r=t,c===0&&v(Wr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=du():(n=e,e=t)):(n=e,e=t),e}function wt(){var e,r,a,u;return e=n,i.substr(n,3).toLowerCase()===Ln?(r=i.substr(n,3),n+=3):(r=t,c===0&&v(Yr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=vu():(n=e,e=t)):(n=e,e=t),e}function Nt(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Mn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Kr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=hu():(n=e,e=t)):(n=e,e=t),e}function It(){var e,r,a,u;return e=n,i.substr(n,6).toLowerCase()===Rn?(r=i.substr(n,6),n+=6):(r=t,c===0&&v(Qr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=mu():(n=e,e=t)):(n=e,e=t),e}function H(){var e,r,a,u;return e=n,i.substr(n,6).toLowerCase()===Dn?(r=i.substr(n,6),n+=6):(r=t,c===0&&v(Zr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=gu():(n=e,e=t)):(n=e,e=t),e}function ie(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===On?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(Xr)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=yu():(n=e,e=t)):(n=e,e=t),e}function le(){var e,r,a,u;return e=n,i.substr(n,3).toLowerCase()===$n?(r=i.substr(n,3),n+=3):(r=t,c===0&&v(ea)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=wu():(n=e,e=t)):(n=e,e=t),e}function bt(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Jn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(ta)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Nu():(n=e,e=t)):(n=e,e=t),e}function Tt(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===Un?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(na)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=Iu():(n=e,e=t)):(n=e,e=t),e}function So(){var e,r,a,u;return e=n,i.substr(n,4).toLowerCase()===kn?(r=i.substr(n,4),n+=4):(r=t,c===0&&v(ra)),r!==t?(a=n,c++,u=y(),c--,u===t?a=void 0:(n=a,a=t),a!==t?e=bu():(n=e,e=t)):(n=e,e=t),e}function ce(){var e;return i.charCodeAt(n)===44?(e=qn,n++):(e=t,c===0&&v(aa)),e}function F(){var e;return i.charCodeAt(n)===40?(e=Vn,n++):(e=t,c===0&&v(sa)),e}function P(){var e;return i.charCodeAt(n)===41?(e=Hn,n++):(e=t,c===0&&v(ua)),e}function f(){var e,r;for(e=[],r=xt();r!==t;)e.push(r),r=xt();return e}function xt(){var e;return Kn.test(i.charAt(n))?(e=i.charAt(n),n++):(e=t,c===0&&v(oa)),e}function Eo(){var e,r,a,u;if(e=n,(r=tt())!==t&&(r=Tu(r)),(e=r)===t)if(e=n,i.charCodeAt(n)===96?(r=$e,n++):(r=t,c===0&&v(Be)),r!==t){if(a=[],Ue.test(i.charAt(n))?(u=i.charAt(n),n++):(u=t,c===0&&v(je)),u!==t)for(;u!==t;)a.push(u),Ue.test(i.charAt(n))?(u=i.charAt(n),n++):(u=t,c===0&&v(je));else a=t;a!==t?(i.charCodeAt(n)===96?(u=$e,n++):(u=t,c===0&&v(Be)),u!==t?e=xu(a):(n=e,e=t)):(n=e,e=t)}else n=e,e=t;return e}function Co(e,r){return{type:"unary-expression",operator:e,expr:r}}function At(e,r,a,u){var h={type:"binary-expression",operator:e,left:r,right:a};return u!==void 0&&(h.escape=u),h}function Fo(e,r){for(var a=[e],u=0;u<r.length;u++)a.push(r[u][3]);return a}function Po(e,r,a){return Fo(e,r)}function Q(e,r){for(var a=e,u=0;u<r.length;u++)a=At(r[u][1],a,r[u][3]);return a}if((m=_())!==t&&n===i.length)return m;throw m!==t&&n<i.length&&v(Au()),Su(me,R<i.length?i.charAt(R):null,R<i.length?We(R,R+1):We(R,R))}return l(s,Error),s.prototype.format=function(i){var d="Error: "+this.message;if(this.location){var m,t=null;for(m=0;m<i.length;m++)if(i[m].source===this.location.source){t=i[m].text.split(/\r\n|\n|\r/g);break}var N=this.location.start,T=this.location.source+":"+N.line+":"+N.column;if(t){var _=this.location.end,q=o("",N.line.toString().length," "),O=t[N.line-1],b=(N.line===_.line?_.column:O.length+1)-N.column||1;d+=`
 --> `+T+`
`+q+` |
`+N.line+" | "+O+`
`+q+" | "+o("",N.column-1," ")+o("",b,"^")}else d+=`
 at `+T}return d},s.buildMessage=function(i,d){var m={literal:function(b){return'"'+N(b.text)+'"'},class:function(b){var S=b.parts.map(function(L){return Array.isArray(L)?T(L[0])+"-"+T(L[1]):T(L)});return"["+(b.inverted?"^":"")+S.join("")+"]"},any:function(){return"any character"},end:function(){return"end of input"},other:function(b){return b.description}};function t(b){return b.charCodeAt(0).toString(16).toUpperCase()}function N(b){return b.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(S){return"\\x0"+t(S)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(S){return"\\x"+t(S)})}function T(b){return b.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(S){return"\\x0"+t(S)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(S){return"\\x"+t(S)})}function _(b){return m[b.type](b)}function q(b){var S,L,E=b.map(_);if(E.sort(),E.length>0){for(S=1,L=1;S<E.length;S++)E[S-1]!==E[S]&&(E[L]=E[S],L++);E.length=L}switch(E.length){case 1:return E[0];case 2:return E[0]+" or "+E[1];default:return E.slice(0,-1).join(", ")+", or "+E[E.length-1]}}function O(b){return b?'"'+N(b)+'"':"end of input"}return"Expected "+q(i)+" but "+O(d)+" found."},{SyntaxError:s,parse:p}},(Pt={get exports(){return Se},set exports(l){Se=l}}).exports&&(Pt.exports=_t());let qo=class{static parse(s){return Se.parse(s)}};const kt=/^(\d{4})-(\d{1,2})-(\d{1,2})$/,Vo=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2}(\.[0-9]+)?)$/,Ho=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2}(\.[0-9]+)?)(\+|\-)(\d{1,2}):(\d{1,2})$/,Bo=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2})(\+|\-)(\d{1,2}):(\d{1,2})$/,jo=/^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2})$/,zo=new Set(["current_timestamp","current_date","current_time"]);function B(l,s){return(l+="").length>=s?l:new Array(s-l.length+1).join("0")+l}function Z(l,s,o="0",p="0",i="0",d="0",m="",t="0",N="0"){if(m==="+"||m==="-"){const T=`${B(parseInt(l,10),4)}-${B(parseInt(s,10),2)}-${B(parseInt(o,10),2)}`;let _="";parseFloat(d)<10&&(_="0");const q=`${B(parseInt(p,10),2)}:${B(parseInt(i,10),2)}:${_+parseFloat(d).toString()}`,O=`${m}${B(parseInt(t,10),2)}:${B(parseInt(N,10),2)}`;return new Date(T+"T"+q+O)}return new Date(parseInt(l,10),parseInt(s,10)-1,parseInt(o,10),parseInt(p,10),parseInt(i,10),parseFloat(d))}class Go{static makeBool(s){return qt(s)}static featureValue(s,o,p,i){return zt(s,o,p,i)}static equalsNull(s){return s===null}static applyLike(s,o,p){return Pe(s,o,p)}static ensureArray(s){return Ce(s)}static applyIn(s,o){return Fe(s,o)}static currentDate(){const s=new Date;return s.setHours(0,0,0,0),s}static makeSqlInterval(s,o,p){return k.createFromValueAndQualifer(s,o,p)}static convertInterval(s){return s instanceof k?s.valueInMilliseconds():s}static currentTimestamp(){return new Date}static compare(s,o,p){return Bt(s,o,p)}static calculate(s,o,p){return jt(s,o,p)}static makeComparable(s){return U(s)}static evaluateFunction(s,o){return _e(s,o)}static lookup(s,o){const p=o[s];return p===void 0?null:p}static between(s,o){return s==null||o[0]==null||o[1]==null?null:s>=o[0]&&s<=o[1]}static notbetween(s,o){return s==null||o[0]==null||o[1]==null?null:s<o[0]||s>o[1]}static ternaryNot(s){return pe(s)}static ternaryAnd(s,o){return Vt(s,o)}static ternaryOr(s,o){return Ht(s,o)}}class Le{constructor(s,o){this.fieldsIndex=o,this._datefields={},this.parameters={},this._hasDateFunctions=void 0,this.parseTree=qo.parse(s);const{isStandardized:p,isAggregate:i,referencedFieldNames:d}=this._extractExpressionInfo(o);this._referencedFieldNames=d,this.isStandardized=p,this.isAggregate=i}static create(s,o){return new Le(s,o)}get fieldNames(){return this._referencedFieldNames}testSet(s,o=z){const p={};for(const i of this.fieldNames)p[i]=s.map(d=>o.getAttribute(d,i));return!!this._evaluateNode(this.parseTree,{attributes:p},z)}calculateValue(s,o=z){const p=this._evaluateNode(this.parseTree,s,o);return p instanceof k?p.valueInMilliseconds()/864e5:p}calculateValueCompiled(s,o=z){return this.parseTree._compiledVersion!=null?this.parseTree._compiledVersion(s,this.parameters,o,this._datefields):St("esri-csp-restrictions")?this.calculateValue(s,o):(this._compileMe(),this.parseTree._compiledVersion(s,this.parameters,o,this._datefields))}testFeature(s,o=z){return!!this._evaluateNode(this.parseTree,s,o)}testFeatureCompiled(s,o=z){return this.parseTree._compiledVersion!=null?!!this.parseTree._compiledVersion(s,this.parameters,o,this._datefields):St("esri-csp-restrictions")?this.testFeature(s,o):(this._compileMe(),!!this.parseTree._compiledVersion(s,this.parameters,o,this._datefields))}get hasDateFunctions(){return this._hasDateFunctions!=null||(this._hasDateFunctions=!1,this._visitAll(this.parseTree,s=>{s.type==="current-time"?this._hasDateFunctions=!0:s.type==="function"&&(this._hasDateFunctions=this._hasDateFunctions||zo.has(s.name.toLowerCase()))})),this._hasDateFunctions}getFunctions(){const s=new Set;return this._visitAll(this.parseTree,o=>{o.type==="function"&&s.add(o.name.toLowerCase())}),Array.from(s)}getExpressions(){const s=new Map;return this._visitAll(this.parseTree,o=>{if(o.type==="function"){const p=o.name.toLowerCase(),i=o.args.value[0];if(i.type==="column-reference"){const d=i.column,m=`${p}-${d}`;s.has(m)||s.set(m,{aggregateType:p,field:d})}}}),[...s.values()]}getVariables(){const s=new Set;return this._visitAll(this.parseTree,o=>{o.type==="parameter"&&s.add(o.value.toLowerCase())}),Array.from(s)}_compileMe(){const s="return this.convertInterval("+this.evaluateNodeToJavaScript(this.parseTree)+")";this.parseTree._compiledVersion=new Function("feature","lookups","attributeAdapter","datefields",s).bind(Go)}_extractExpressionInfo(s){const o=[],p=new Set;let i=!0,d=!0;return this._visitAll(this.parseTree,m=>{switch(m.type){case"column-reference":{const t=s==null?void 0:s.get(m.column);let N,T;t?N=T=t.name??"":(T=m.column,N=T.toLowerCase()),t&&t.name&&(t.type==="date"||t.type==="esriFieldTypeDate")&&(this._datefields[t.name]=1),p.has(N)||(p.add(N),o.push(T)),m.column=T;break}case"function":{const{name:t,args:N}=m,T=N.value.length;i&&(i=Jo(t,T)),d&&(d=Ro(t,T));break}}}),{referencedFieldNames:Array.from(o),isStandardized:i,isAggregate:d}}_visitAll(s,o){if(s!=null)switch(o(s),s.type){case"when-clause":this._visitAll(s.operand,o),this._visitAll(s.value,o);break;case"case-expression":for(const p of s.clauses)this._visitAll(p,o);s.format==="simple"&&this._visitAll(s.operand,o),s.else!==null&&this._visitAll(s.else,o);break;case"expression-list":for(const p of s.value)this._visitAll(p,o);break;case"unary-expression":this._visitAll(s.expr,o);break;case"binary-expression":this._visitAll(s.left,o),this._visitAll(s.right,o);break;case"function":this._visitAll(s.args,o)}}evaluateNodeToJavaScript(s){switch(s.type){case"interval":return"this.makeSqlInterval("+this.evaluateNodeToJavaScript(s.value)+", "+JSON.stringify(s.qualifier)+","+JSON.stringify(s.op)+")";case"case-expression":{let o="";if(s.format==="simple"){const p="this.makeComparable("+this.evaluateNodeToJavaScript(s.operand)+")";o="( ";for(let i=0;i<s.clauses.length;i++)o+=" ("+p+" === this.makeComparable("+this.evaluateNodeToJavaScript(s.clauses[i].operand)+")) ? ("+this.evaluateNodeToJavaScript(s.clauses[i].value)+") : ";s.else!==null?o+=this.evaluateNodeToJavaScript(s.else):o+="null",o+=" )"}else{o="( ";for(let p=0;p<s.clauses.length;p++)o+=" this.makeBool("+this.evaluateNodeToJavaScript(s.clauses[p].operand)+")===true ? ("+this.evaluateNodeToJavaScript(s.clauses[p].value)+") : ";s.else!==null?o+=this.evaluateNodeToJavaScript(s.else):o+="null",o+=" )"}return o}case"parameter":return"this.lookup("+JSON.stringify(s.value.toLowerCase())+",lookups)";case"expression-list":{let o="[";for(const p of s.value)o!=="["&&(o+=","),o+=this.evaluateNodeToJavaScript(p);return o+="]",o}case"unary-expression":return"this.ternaryNot("+this.evaluateNodeToJavaScript(s.expr)+")";case"binary-expression":switch(s.operator){case"AND":return"this.ternaryAnd("+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+" )";case"OR":return"this.ternaryOr("+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+" )";case"IS":if(s.right.type!=="null")throw new Error("Unsupported RHS for IS");return"this.equalsNull("+this.evaluateNodeToJavaScript(s.left)+")";case"ISNOT":if(s.right.type!=="null")throw new Error("Unsupported RHS for IS");return"(!(this.equalsNull("+this.evaluateNodeToJavaScript(s.left)+")))";case"IN":return"this.applyIn("+this.evaluateNodeToJavaScript(s.left)+",this.ensureArray("+this.evaluateNodeToJavaScript(s.right)+"))";case"NOT IN":return"this.ternaryNot(this.applyIn("+this.evaluateNodeToJavaScript(s.left)+",this.ensureArray("+this.evaluateNodeToJavaScript(s.right)+")))";case"BETWEEN":return"this.between("+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+")";case"NOTBETWEEN":return"this.notbetween("+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+")";case"LIKE":return"this.applyLike("+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+","+JSON.stringify(s.escape)+")";case"NOT LIKE":return"this.ternaryNot(this.applyLike("+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+","+JSON.stringify(s.escape)+"))";case"<>":case"<":case">":case">=":case"<=":case"=":return"this.compare("+JSON.stringify(s.operator)+","+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+")";case"*":case"-":case"+":case"/":case"||":return"this.calculate("+JSON.stringify(s.operator)+","+this.evaluateNodeToJavaScript(s.left)+","+this.evaluateNodeToJavaScript(s.right)+")"}throw new Error("Not Supported Operator "+s.operator);case"null":case"boolean":case"string":case"number":return JSON.stringify(s.value);case"date":return"(new Date("+Lt(s.value).getTime().toString()+"))";case"timestamp":return"(new Date("+Ee(s.value).getTime().toString()+"))";case"current-time":return s.mode==="date"?"this.currentDate()":"this.currentTimestamp()";case"column-reference":return"this.featureValue(feature,"+JSON.stringify(s.column)+",datefields,attributeAdapter)";case"function":return"this.evaluateFunction("+JSON.stringify(s.name)+","+this.evaluateNodeToJavaScript(s.args)+")"}throw new Error("Unsupported sql syntax "+s.type)}_evaluateNode(s,o,p){switch(s.type){case"interval":{const i=this._evaluateNode(s.value,o,p);return k.createFromValueAndQualifer(i,s.qualifier,s.op)}case"case-expression":if(s.format==="simple"){const i=U(this._evaluateNode(s.operand,o,p));for(let d=0;d<s.clauses.length;d++)if(i===U(this._evaluateNode(s.clauses[d].operand,o,p)))return this._evaluateNode(s.clauses[d].value,o,p);if(s.else!==null)return this._evaluateNode(s.else,o,p)}else{for(let i=0;i<s.clauses.length;i++)if(qt(this._evaluateNode(s.clauses[i].operand,o,p)))return this._evaluateNode(s.clauses[i].value,o,p);if(s.else!==null)return this._evaluateNode(s.else,o,p)}return null;case"parameter":return this.parameters[s.value.toLowerCase()];case"expression-list":{const i=[];for(const d of s.value)i.push(this._evaluateNode(d,o,p));return i}case"unary-expression":return pe(this._evaluateNode(s.expr,o,p));case"binary-expression":switch(s.operator){case"AND":return Vt(this._evaluateNode(s.left,o,p),this._evaluateNode(s.right,o,p));case"OR":return Ht(this._evaluateNode(s.left,o,p),this._evaluateNode(s.right,o,p));case"IS":if(s.right.type!=="null")throw new Error("Unsupported RHS for IS");return this._evaluateNode(s.left,o,p)===null;case"ISNOT":if(s.right.type!=="null")throw new Error("Unsupported RHS for IS");return this._evaluateNode(s.left,o,p)!==null;case"IN":{const i=Ce(this._evaluateNode(s.right,o,p));return Fe(this._evaluateNode(s.left,o,p),i)}case"NOT IN":{const i=Ce(this._evaluateNode(s.right,o,p));return pe(Fe(this._evaluateNode(s.left,o,p),i))}case"BETWEEN":{const i=this._evaluateNode(s.left,o,p),d=this._evaluateNode(s.right,o,p);return i==null||d[0]==null||d[1]==null?null:i>=U(d[0])&&i<=U(d[1])}case"NOTBETWEEN":{const i=this._evaluateNode(s.left,o,p),d=this._evaluateNode(s.right,o,p);return i==null||d[0]==null||d[1]==null?null:i<U(d[0])||i>U(d[1])}case"LIKE":return Pe(this._evaluateNode(s.left,o,p),this._evaluateNode(s.right,o,p),s.escape);case"NOT LIKE":return pe(Pe(this._evaluateNode(s.left,o,p),this._evaluateNode(s.right,o,p),s.escape));case"<>":case"<":case">":case">=":case"<=":case"=":return Bt(s.operator,this._evaluateNode(s.left,o,p),this._evaluateNode(s.right,o,p));case"-":case"+":case"*":case"/":case"||":return jt(s.operator,this._evaluateNode(s.left,o,p),this._evaluateNode(s.right,o,p))}case"null":case"boolean":case"string":case"number":return s.value;case"date":return Lt(s.value);case"timestamp":return Ee(s.value);case"current-time":{const i=new Date;return s.mode==="date"&&i.setHours(0,0,0,0),i}case"column-reference":return zt(o,s.column,this._datefields,p);case"data-type":return s.value;case"function":{const i=this._evaluateNode(s.args,o,p);return this.isAggregate?Mo(s.name,i):_e(s.name,i)}}throw new Error("Unsupported sql syntax "+s.type)}}function Ee(l){let s=Vo.exec(l);if(s!==null){const[,o,p,i,d,m,t]=s;return Z(o,p,i,d,m,t)}if(s=Ho.exec(l),s!==null){const[,o,p,i,d,m,t,N,T,_]=s;return Z(o,p,i,d,m,t,N,T,_)}if(s=Bo.exec(l),s!==null){const[,o,p,i,d,m,t,N,T]=s;return Z(o,p,i,d,m,"0",t,N,T)}if(s=jo.exec(l),s!==null){const[,o,p,i,d,m]=s;return Z(o,p,i,d,m)}if(s=kt.exec(l),s!==null){const[,o,p,i]=s;return Z(o,p,i)}throw new Error("SQL Invalid Timestamp")}function Lt(l){const s=kt.exec(l);if(s===null)try{return Ee(l)}catch{throw new Error("SQL Invalid Date")}const[,o,p,i]=s;return new Date(parseInt(o,10),parseInt(p,10)-1,parseInt(i,10))}function qt(l){return l===!0}function Ce(l){return Array.isArray(l)?l:[l]}function pe(l){return l!==null?l!==!0:null}function Vt(l,s){return l!=null&&s!=null?l===!0&&s===!0:l!==!1&&s!==!1&&null}function Ht(l,s){return l!=null&&s!=null?l===!0||s===!0:l===!0||s===!0||null}function Fe(l,s){if(l==null)return null;let o=!1;for(const p of s)if(p==null)o=null;else if(l===p){o=!0;break}return o}const Mt="-[]/{}()*+?.\\^$|";var G;function Wo(l,s){const o=s;let p="",i=G.Normal;for(let d=0;d<l.length;d++){const m=l.charAt(d);switch(i){case G.Normal:m===o?i=G.Escaped:Mt.includes(m)?p+="\\"+m:p+=m==="%"?".*":m==="_"?".":m;break;case G.Escaped:Mt.includes(m)?p+="\\"+m:p+=m,i=G.Normal}}return new RegExp("^"+p+"$","m")}function Pe(l,s,o){return l==null?null:Wo(s,o).test(l)}function U(l){return l instanceof Date?l.valueOf():l}function Bt(l,s,o){if(s==null||o==null)return null;const p=U(s),i=U(o);switch(l){case"<>":return p!==i;case"=":return p===i;case">":return p>i;case"<":return p<i;case">=":return p>=i;case"<=":return p<=i}}function jt(l,s,o){if(l==="||")return _e("concat",[s,o]);if(s instanceof k)if(o instanceof Date)switch(l){case"+":return new Date(s.valueInMilliseconds()+o.getTime());case"-":return s.valueInMilliseconds()-o.getTime();case"*":return s.valueInMilliseconds()*o.getTime();case"/":return s.valueInMilliseconds()/o.getTime()}else if(o instanceof k)switch(l){case"+":return k.createFromMilliseconds(s.valueInMilliseconds()+o.valueInMilliseconds());case"-":return k.createFromMilliseconds(s.valueInMilliseconds()-o.valueInMilliseconds());case"*":return s.valueInMilliseconds()*o.valueInMilliseconds();case"/":return s.valueInMilliseconds()/o.valueInMilliseconds()}else s=s.valueInMilliseconds();else if(o instanceof k)if(s instanceof Date)switch(l){case"+":return new Date(o.valueInMilliseconds()+s.getTime());case"-":return new Date(s.getTime()-o.valueInMilliseconds());case"*":return s.getTime()*o.valueInMilliseconds();case"/":return s.getTime()/o.valueInMilliseconds()}else o=o.valueInMilliseconds();else if(s instanceof Date&&typeof o=="number")switch(o=24*o*60*60*1e3,s=s.getTime(),l){case"+":return new Date(s+o);case"-":return new Date(s-o);case"*":return new Date(s*o);case"/":return new Date(s/o)}else if(o instanceof Date&&typeof s=="number")switch(s=24*s*60*60*1e3,o=o.getTime(),l){case"+":return new Date(s+o);case"-":return new Date(s-o);case"*":return new Date(s*o);case"/":return new Date(s/o)}switch(l){case"+":return s+o;case"-":return s-o;case"*":return s*o;case"/":return s/o}}function Yo(l){return l&&typeof l.attributes=="object"}function zt(l,s,o,p){const i=p.getAttribute(l,s);return i!=null&&o[s]===1?new Date(i):i}(function(l){l[l.Normal=0]="Normal",l[l.Escaped=1]="Escaped"})(G||(G={}));const z={getAttribute:(l,s)=>(Yo(l)?l.attributes:l)[s]},ni=Object.freeze(Object.defineProperty({__proto__:null,WhereClause:Le,defaultAttributeAdapter:z},Symbol.toStringTag,{value:"Module"}));export{ni as W,Le as f,M as n,ei as o,j as s,C as t};
