package org.thingsboard.server.service.influx;

import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApi;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.flux.FluxClient;
import com.influxdb.client.flux.FluxClientFactory;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.tsdb.DataPoint;

import org.thingsboard.server.dao.influx.Flux;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.influx.functions.restriction.Restrictions;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/2 16:03
 */
@Service
public class InfluxServiceImpl implements InfluxService {


    /**
     * 保存设备数据点
     *
     * @param dataPoints 设备数据点列表
     */
    @Override
    public void saveDeviceToInflux(List<DataPoint> dataPoints) {
        InfluxDBClient influxDBClient = InfluxFactory.createInFluxClient();
        List<Point> points = new ArrayList<>();
        dataPoints.forEach(dataPoint -> {
            points.add(Point.measurement(dataPoint.getMetric())
                    .addTag(DataConstants.ATTRIBUTE_PROP, dataPoint.getProp())
                    .addField(DataConstants.VALUE, Double.parseDouble(dataPoint.getValue()))
                    .time(Instant.ofEpochMilli(dataPoint.getTimestamp()), WritePrecision.MS));
        });
        try (WriteApi writeApi = influxDBClient.getWriteApi()) {
            writeApi.writePoints(points);
        }
        influxDBClient.close();
    }


    @Override
    public List<FluxTable> findDeviceDataFromInflux(List<String> formulas, long start, long end) {
        InfluxDBClient fluxClient = InfluxFactory.createInFluxClient();
        Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
        for (int i = 0; i < formulas.size(); i++) {
            String[] array = formulas.get(i).split("\\.");
            restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1])));
        }

        Restrictions restrictions = Restrictions.or(restrictionsArray);
        Calendar startInstants = Calendar.getInstance();
        startInstants.setTime(new Date(start));
        Calendar endInstants = Calendar.getInstance();
        endInstants.setTime(new Date(end));
        Flux flux = Flux
                .from(DataConstants.INFLUX_DEVICE_DATA)
                .range(Instant.ofEpochMilli(start),Instant.ofEpochMilli(end))
                .filter(restrictions);

        //simple synchronous query
        return fluxClient.getQueryApi().query(flux.toString());
//        for (FluxTable fluxTable : tables) {
//            List<FluxRecord> records = fluxTable.getRecords();
//            for (FluxRecord fluxRecord : records) {
//                System.out.println(fluxRecord.getTime().atOffset(ZoneOffset.of("+8")).format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMATE_DEFAULT)) + ": " + fluxRecord.getValueByKey("_value"));
//            }
//        }
//        fluxClient.close();
    }


    public static void main1(final String[] args) {

        InfluxDBClient influxDBClient = InfluxFactory.createInFluxClient();

        //
        // Write data
        //
        try (WriteApi writeApi = influxDBClient.getWriteApi()) {

            //
            // Write by Data Point
            //
            Point point = Point.measurement("temperature")
                    .addTag("location", "west")
                    .addField("value", 55D)
                    .time(Instant.now().toEpochMilli(), WritePrecision.MS);

            writeApi.writePoint(point);


            // Write by LineProtocol
            //
            writeApi.writeRecord(WritePrecision.MS, "temperature,location=north value=60.0");

            //
            // Write by POJO
            //
            Temperature temperature = new Temperature();
            temperature.location = "south";
            temperature.value = 62D;
            temperature.time = Instant.now();

            writeApi.writeMeasurement(WritePrecision.MS, temperature);
        }
        influxDBClient.close();
    }


    public static void main(String[] args) {
        FluxClient fluxClient = FluxClientFactory.create("http://47.105.80.231:8086?readTimeout=10000&connectTimeout=10000&logLevel=BASIC");
//        //String flux = "from(bucket:\"mydb\")|> range(start: 0)|> filter(fn: (r) =>r._measurement == \"1ea010742cafea0af8da740d7a05231\"  and r.prop == \"IA\")\n";
//        Restrictions restrictions = Restrictions.or(
//                Restrictions.and(Restrictions.measurement().equal("1ea010742cafea0af8da740d7a05232"), Restrictions.tag("prop").equal("IA")),
//                Restrictions.and(Restrictions.measurement().equal("1ea010742cafea0af8da740d7a05231"), Restrictions.tag("prop").equal("ENERGY_IN"))
//        );
//
//        Flux flux = Flux
//                .from("mydb")
//                .range(Instant.ofEpochSecond(1596441600))
//                .filter(restrictions);
//        //simple synchronous query
//        List<FluxTable> tables = fluxClient.query(flux.toString());
//        for (FluxTable fluxTable : tables) {
//            List<FluxRecord> records = fluxTable.getRecords();
//            for (FluxRecord fluxRecord : records) {
//                System.out.println(fluxRecord.getTime().atOffset(ZoneOffset.of("+8")).format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMATE_DEFAULT)) + ": " + fluxRecord.getValueByKey("_value"));
//            }
//        }

        List<String> formulas = new ArrayList<>();
        formulas.add("test.temperratur");
        Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
        for (int i = 0; i < formulas.size(); i++) {
            String[] array = formulas.get(i).split("\\.");
            restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1]), Restrictions.field().equal("wave")));
        }

        Restrictions restrictions = Restrictions.or(restrictionsArray);
        Flux flux = Flux
                .from("mydb")
                .range(Instant.ofEpochSecond(1621127458), Instant.ofEpochSecond(1621131080))
                .filter(restrictions);
        //simple synchronous query
        List<FluxTable> tables = fluxClient.query(flux.toString());
        for (FluxTable fluxTable : tables) {
            List<FluxRecord> records = fluxTable.getRecords();
            for (FluxRecord fluxRecord : records) {
                System.out.println(fluxRecord.getTime().atOffset(ZoneOffset.of("+8")).format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMATE_DEFAULT)) + ": " + fluxRecord.getValueByKey("_value"));
            }
        }

    }


    @Measurement(name = "temperature")
    private static class Temperature {

        @Column(tag = true)
        String location;

        @Column
        Double value;

        @Column(timestamp = true)
        Instant time;
    }
}
