import{d as D,c as u,r as a,a8 as C,x as p,g as O,n as T,q as s,i as l,F as k,aq as q,b6 as v,C as z}from"./index-r0dFAfgr.js";import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{a1 as B}from"./manage-BReaEVJk.js";import F from"./detail-DEo1RlcF.js";import{f as N}from"./DateFormatter-Bm9a68Ax.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./projectManagement-CDcrrCQ1.js";import"./xmwcqk-Cxfq91Sa.js";import"./CardTable-rdWOL4_6.js";import"./index-C9hz-UZb.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const V={class:"team_table"},L=D({__name:"detail",props:{config:{}},emits:["extendedReturn"],setup(d,{emit:f}){const n=u(),c=u(),g=d,b=f,m=a({selected:{}}),_=a({loading:!1,indexVisible:!0,dataList:C(()=>g.config.items),columns:[{prop:"code",label:"签证编号"},{prop:"constructionName",label:"工程名称"},{prop:"address",label:"施工地点"},{prop:"constructOrganization",label:"施工单位"},{prop:"constructTime",label:"施工时间",formatter:e=>N(e.constructTime,"YYYY-MM-DD HH:mm:ss")},{prop:"buildOrganization",label:"建设单位"},{prop:"supervisorOrganization",label:"监理单位"},{prop:"auditOrganization",label:"审计单位"},{prop:"creatorName",label:"添加人"},{prop:"createTimeName",label:"添加时间"}],operationWidth:"200px",operations:[{isTextBtn:!1,type:"success",text:"编辑",perm:!0,click:e=>y(e)},{isTextBtn:!1,text:"详情",perm:!0,click:e=>{var t;m.selected=e,(t=c.value)==null||t.openDrawer()}}],pagination:{hide:!0}}),i=a({title:"编辑签证",appendToBody:!0,labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{i.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),B(e).then(o=>{var r;i.submitting=!1,o.data.code===200?(p.success(t+"成功"),(r=n.value)==null||r.closeDialog()):p.warning(t+"失败"),b("extendedReturn",{})}).catch(o=>{i.submitting=!1,p.warning(o)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"签证编号",field:"code",rules:[{required:!0,message:"请输入签证编号"}]},{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"施工单位",field:"constructOrganization",rules:[{required:!0,message:"请输入施工单位"}]},{xs:12,type:"input",label:"施工地点",field:"address",rules:[{required:!0,message:"请输入施工地点"}]},{xs:12,type:"date",label:"施工时间",field:"constructTime",format:"x"},{xs:12,type:"input",label:"建设单位",field:"buildOrganization",rules:[{required:!0,message:"请输入建设单位"}]},{xs:12,type:"input",label:"监理单位",field:"supervisorOrganization",rules:[{required:!0,message:"请输入监理单位"}]},{xs:12,type:"input",label:"审计单位",field:"auditOrganization",rules:[{required:!0,message:"请输入审计单位"}]},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),x=a({title:"详情",group:[],width:"80%",modalClass:"lightColor",appendToBody:!0,cancel:!1}),y=e=>{var t;i.title="编辑签证",i.defaultValue={...e||{}},(t=n.value)==null||t.openDialog()};return(e,t)=>{const o=q,r=w,h=v;return O(),T("div",V,[s(o,{config:l(_)},null,8,["config"]),s(r,{ref_key:"refForm",ref:n,class:"dialogForm",config:l(i)},null,8,["config"]),s(h,{ref_key:"refDetail",ref:c,config:l(x)},{default:k(()=>[s(F,{config:l(m).selected,show:6},null,8,["config"])]),_:1},8,["config"])])}}}),de=z(L,[["__scopeId","data-v-75c01076"]]);export{de as default};
