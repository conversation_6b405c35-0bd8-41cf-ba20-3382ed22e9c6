<!-- 坐标转换 -->
<template>
  <SLCard class="coord-card">
    <Tabs
      v-model="state.converterType"
      :config="tabsConfig"
      class="tabs"
    >
    </Tabs>
    <div class="coord-root">
      <div
        v-if="state.converterType === '1'"
        class="coord-converter"
      >
        <div class="left">
          <SideDrawer
            v-model="state.drawerShow"
            :title="'原始数据'"
            :width="500"
            :hide-bar="true"
            :direction="'rtl'"
          >
            <Form
              ref="refFormLeft"
              :config="FormConfigLeft"
            ></Form>
          </SideDrawer>
        </div>
        <div class="center">
          <Form :config="FormConfigCenter"></Form>
        </div>
        <div class="right">
          <SideDrawer
            v-model="state.drawerShow"
            :title="'转换结果'"
            :width="500"
            :hide-bar="true"
            :direction="'ltr'"
          >
            <Form
              ref="refFormRight"
              :config="FormConfigRight"
            ></Form>
          </SideDrawer>
        </div>
      </div>
      <GeoJsonConverter v-else-if="state.converterType === '3'"></GeoJsonConverter>
      <CoordinateConvertVM v-else></CoordinateConvertVM>
    </div>
  </SLCard>
</template>
<script lang="ts" setup>
import { ArrowRight, Delete, Plus } from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid'
import Point from '@arcgis/core/geometry/Point.js'
import SpatialReference from '@arcgis/core/geometry/SpatialReference.js'
import * as geometryService from '@arcgis/core/rest/geometryService.js'
import ProjectParameters from '@arcgis/core/rest/support/ProjectParameters.js'
import SideDrawer from '@/components/DrawerBox/components/SideDrawer.vue'
import { SLConfirm } from '@/utils/Message'
import { IFormIns } from '@/components/type'
import CoordinateConvertVM from './CoordinateConvertVM.vue'
import GeoJsonConverter from './GeoJsonConverter.vue'

const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'border-card',
  tabs: [
    { label: '坐标系转换', value: '1' },
    {
      label: '度分秒转十进制',
      value: '2'
    },
    {
      label: '几何转换',
      value: '3'
    }
  ]
})
const refFormLeft = ref<IFormIns>()
const refFormRight = ref<IFormIns>()
const state = reactive<{
  drawerShow: true
  converterType: '1' | '2' | '3'
}>({
  drawerShow: true,
  converterType: '1'
})
const TableConfigLeft = reactive<ITable>({
  height: 'none',
  indexVisible: true,
  dataList: [{ id: uuidv4(), x: '', y: '' }],
  columns: [
    {
      label: 'x',
      prop: 'x',
      formItemConfig: { type: 'input-number' }
    },
    {
      label: 'y',
      prop: 'y',
      formItemConfig: { type: 'input-number' }
    }
  ],
  operationHeader: {
    type: 'btn-group',
    btns: [
      {
        perm: true,
        text: '新增',
        isTextBtn: true,
        svgIcon: shallowRef(Plus),
        click: () => handleLeftAdd()
      }
    ]
  },
  operations: [
    {
      perm: true,
      text: '删除',
      svgIcon: shallowRef(Delete),
      type: 'danger',
      click: row => handleLeftDelete(row)
    }
  ],
  pagination: { hide: true }
})
const TableConfigRight = reactive<ITable>({
  height: 'none',
  indexVisible: true,
  dataList: [],
  columns: [
    {
      label: 'x',
      prop: 'x',
      formItemConfig: { type: 'input-number' }
    },
    {
      label: 'y',
      prop: 'y',
      formItemConfig: { type: 'input-number' }
    }
  ],
  pagination: { hide: true }
})
const FormConfigLeft = reactive<IFormConfig>({
  labelWidth: 100,
  group: [
    {
      fields: [
        {
          type: 'select',
          field: 'wkid',
          label: '原始坐标系',
          options: [
            {
              label: 'Beijong_1954_3_Degree_GK_CM_114E(2435)',
              value: '2435'
            },
            { label: 'CGCS2000_3_Degree_GK_CM_114E(4547)', value: '4547' },
            { label: 'GCS_WGS_1984(4326)', value: '4326' }
          ]
        },
        {
          type: 'table',
          config: TableConfigLeft
        }
      ]
    }
  ],
  defaultValue: {
    wkid: '2435'
  }
})
const FormConfigRight = reactive<IFormConfig>({
  labelWidth: 100,
  group: [
    {
      fields: [
        {
          type: 'select',
          field: 'wkid',
          label: '转换坐标系',
          options: [
            {
              label: 'WGS_1984_Web_Mercator_Auxiliary_Sphere(3857)',
              value: '3857'
            },
            { label: 'CGCS2000_3_Degree_GK_CM_114E(4547)', value: '4547' },
            { label: 'GCS_WGS_1984(4326)', value: '4326' }
          ]
        },
        {
          type: 'table',
          config: TableConfigRight
        }
      ]
    }
  ],
  defaultValue: {
    wkid: '3857'
  }
})
const FormConfigCenter = reactive<IFormConfig>({
  gutter: 0,

  labelPosition: 'top',
  labelWidth: 100,
  group: [
    {
      fields: [
        // { type: 'switch', label: '偏移' },
        // { type: 'input-number', label: 'x+', field: 'xOffset' },
        // { type: 'input-number', label: 'y+', field: 'yOffset' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '转换坐标',
              svgIcon: shallowRef(ArrowRight),
              styles: { width: '100%' },
              click: () => handleTransCoords()
            }
          ]
        }
        // {
        //   type: 'btn-group',
        //   btns: [
        //     {
        //       perm: true,
        //       text: '上传文档',
        //       svgIcon: shallowRef(Upload),
        //       type: 'success',
        //       styles: { width: '100%' },
        //       click: () => handleUploadFile()
        //     }
        //   ]
        // }
      ]
    }
  ]
})
const handleTransCoords = () => {
  TableConfigRight.dataList = []
  const leftWkid = refFormLeft.value?.dataForm.wkid
  const rightWkid = refFormRight.value?.dataForm.wkid
  const inputPoints = TableConfigLeft.dataList.map(item => {
    const point = new Point({
      x: item.x,
      y: item.y,
      spatialReference: new SpatialReference({
        wkid: leftWkid
      })
    })
    return point
  })

  const params = new ProjectParameters()
  params.outSpatialReference = new SpatialReference({
    wkid: rightWkid
  })
  params.geometries = inputPoints
  geometryService
    .project(
      window.SITE_CONFIG.GIS_CONFIG.gisUtilitiesService + window.SITE_CONFIG.GIS_CONFIG.gisGeometryService,
      params
    )
    .then(res => {
      res.map(item => {
        TableConfigRight.dataList.push({ ...item.toJSON() })
      })
    })
}
const handleLeftAdd = () => {
  TableConfigLeft.dataList.push({ id: uuidv4(), x: '', y: '' })
}
const handleLeftDelete = (row: any) => {
  SLConfirm('确定删除？', '删除提示')
    .then(() => {
      TableConfigLeft.dataList = TableConfigLeft.dataList.filter(item => item.id !== row.id)
    })
    .catch(() => {
      //
    })
}
</script>
<style lang="scss" scoped>
.coord-card {
  height: 100%;
  width: 100%;
  border-radius: 0 !important;
}
.tabs {
  // height: 100%;
  width: 100%;
  :deep(.el-tabs) {
    // height: 100%;
    // width: 100%;
    .el-tabs__content {
      // height: calc(100% - 40px);
      padding: 0;
      // width: 100%;
    }
    // .el-tab-pane {
    //   height: 100%;
    //   width: 100%;
    // }
  }
}
.coord-root{
  height: calc(100% - 40px);
}
.coord-converter {
  width: 100%;
  height: 100%;
  padding: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  .center {
    max-width: 190px;
    padding: 20px;
    height: 50%;
  }
  .left,
  .right {
    position: relative;
    border: 1px solid var(--el-border-color);
    height: 100%;
    overflow: hidden;
  }
}
</style>
