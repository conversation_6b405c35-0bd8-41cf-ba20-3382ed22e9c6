package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.store.GoodsShelf;

import java.util.List;

@Mapper
public interface GoodsShelfMapper extends BaseMapper<GoodsShelf> {
    List<GoodsShelf> findChild(String parentId);

    boolean update(GoodsShelf shelf);

    String getNameById(String id);

    String getCode(String id);

    boolean isCodeExists(@Param("code") String code, @Param("id") String id, @Param("tenantId") String tenantId);

    boolean canBeDelete(String id);

    int deleteWithChildrenRecursive(String id);


}
