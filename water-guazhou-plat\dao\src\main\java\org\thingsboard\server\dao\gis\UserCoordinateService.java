package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinateGroup;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.DestUserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinateSaveRequest;

public interface UserCoordinateService {
    /**
     * 分页条件查询上报的坐标
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<UserCoordinate> findDestUserAllConditional(DestUserCoordinatePageRequest request);

    /**
     * 分页条件查询上报的坐标（通过用户id自动分组）
     *
     * @param request 分页请求
     * @return
     */
    IPage<UserCoordinateGroup> findAllConditional(UserCoordinatePageRequest request);

    /**
     * 保存上报的坐标
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    UserCoordinate save(UserCoordinateSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(UserCoordinate entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 获取用户最新坐标信息
     *
     * @param request 筛选条件
     * @return 用户最新坐标信息
     */
    IPage<UserCoordinate> findNewest(UserCoordinatePageRequest request);

}
