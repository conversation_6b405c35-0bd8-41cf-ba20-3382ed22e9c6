package org.thingsboard.server.dao.util.imodel.query.smartManagement.district;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrict;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class CircuitDistrictSaveRequest extends SaveRequest<SMCircuitDistrict> {
    // 片区名字
    private String name;

    // 父级Id
    @NotNullOrEmpty(condition = "isInsert")
    private String parentId;

    // 备注
    private String remark;

    @Override
    protected SMCircuitDistrict build() {
        SMCircuitDistrict entity = new SMCircuitDistrict();
        entity.setTenantId(tenantId());
        entity.setParentId(parentId);
        commonSet(entity);
        return entity;
    }

    @Override
    protected SMCircuitDistrict update(String id) {
        SMCircuitDistrict entity = new SMCircuitDistrict();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SMCircuitDistrict entity) {
        entity.setName(name);
        entity.setRemark(remark);
    }
}