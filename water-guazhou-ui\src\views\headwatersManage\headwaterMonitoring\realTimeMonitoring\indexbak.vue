<template>
  <TreeBox>
    <template #tree>
      <SLTree ref="refTree" :tree-data="TreeData"></SLTree>
    </template>
    <div class="wrapper-content">
      <div v-if="state.treeDataType === 'Project'">
        <SLCard class="wrapper-content" title="实时监测" overlay>
          <template #title>
            <span class="title title-header">实时监测</span>
            <!-- <Search ref="refSearch" :config="SearchConfig"></Search> -->
          </template>
          <div class="statistics">
            <div
              v-for="(item, i) in state.Statistics"
              :key="i"
              class="statistics-item"
            >
              <div class="item-inner" :class="item.className">
                <Icon :size="60" :icon="'material-symbols:water-drop-outline'"></Icon>
                <div class="item-content">
                  <div class="item-text title">
                    {{ item.title }}
                  </div>
                  <div class="total">
                    <span class="item-text count">{{ item.count }}</span>
                    <!-- <span class="item-text unit">{{ item.unit }}</span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
          <SLCard class="card" style="" title=" ">
            <template #title>
              <Tabs v-model="state.activeName" :config="tabsConfig"></Tabs>
            </template>
            <div class="tab-content">
              <!-- 显示类型九宫格数据 -->
              <div class="card-box">
                <div class="card-item" :class="{ isDark: appStore.isDark }">
                  <div
                    v-for="(carditem, i) in state.ShuiChangDatas"
                    :key="i"
                    class="card-content"
                  >
                    <SLCard
                      :title="carditem.title"
                      class="inner-card left"
                      overlay
                    >
                      <div class="monitor-box">
                        <div
                          v-for="(item, j) in carditem.monitorData"
                          :key="j"
                          class="monitor-item"
                          :class="{ isDark: appStore.isDark }"
                        >
                          <span
                            class="count"
                            :class="
                              item.status === true
                                ? 'text-green'
                                : item.status === false
                                  ? 'text-red'
                                  : 'text-blue'
                            "
                            >{{ item.value }}</span
                          >
                          <span class="total">
                            <span class="label">{{ item.propertyName }}</span>
                            <span v-if="item.unit" class="unit">{{
                              item.unit
                            }}</span>
                          </span>
                        </div>
                      </div>
                    </SLCard>
                    <!-- 读取时间显示 -->
                    <!-- <div class="time-box">
                    <span class="">
                      读取时间：2022-08-28 06:11：11
                    </span>
                  </div> -->
                  </div>
                </div>
              </div>
            </div>
          </SLCard>
          <!-- tabs列表 -->
          <!-- <el-tabs v-model="state.activeName" :stretch="true" type="border-card" class="demo-tabs"
          @tab-click="handleClick">
          <el-tab-pane v-for="item in state.tabsList" :key="item.name" :label="item.label" :name="item.name">
          </el-tab-pane>
        </el-tabs> -->
        </SLCard>
      </div>
      <div
        v-if="state.treeDataType === 'Station'"
        style="height: calc(100% - 100px)"
      >
        <station-detail-monitoring :station-id="state.stationId" monitor="cs">
        </station-detail-monitoring>
      </div>
    </div>
  </TreeBox>
</template>
<script lang="ts" setup>
import { reactive } from 'vue';
import { IStationDatas } from '../data/type';
import { useAppStore, useBusinessStore } from '@/store';
import {
  getWaterSupplyInfoView,
  getWaterSupplyInfoDetail
} from '@/api/headwatersManage/headwaterMonitoring';
import stationDetailMonitoring from './components/stationDetailMonitoring.vue';
import useStation from '@/hooks/station/useStation';
import { Icon } from '@iconify/vue';

const { getStationTree } = useStation();
const appStore = useAppStore();
const allStation = ref<IStationDatas[]>([]);
const state = reactive<{
  Statistics: {
    title: string;
    count: number;
    unit: string;
    className: string;
  }[];
  ShuiChangDatas: IStationDatas[];
  activeName: string;
  tabsList: any[];
  stationTree: any;
  treeDataType: string;
  stationId: string;
}>({
  Statistics: [
    { className: 'orange', title: '昨日供水量', count: 0, unit: 'm³' },
    { className: 'green', title: '今日供水量', count: 0, unit: 'm³' },
    { className: 'blue', title: '上月供水量', count: 0, unit: 'm³' },
    { className: 'purple', title: '本月供水量', count: 0, unit: 'm³' },
    { className: 'red', title: '总供水量', count: 0, unit: 'm³' },
    { className: 'light-blue', title: '报警率', count: 0, unit: '个' }
  ],
  ShuiChangDatas: [],
  activeName: '全部',
  tabsList: [],
  stationTree: [],
  treeDataType: 'Project',
  stationId: ''
});

const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '水源站点',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data;
      state.treeDataType = data.data.type as string;
      if (state.treeDataType === 'Project') {
        useBusinessStore().SET_selectedProject(data);
        allStation.value = [];
        state.ShuiChangDatas = [];
        await refreshData(data);
      } else {
        await nextTick(() => {
          state.stationId = data.id as string;
        });
      }
    }
  }
});

// const SearchConfig = reactive<ISearch>({
//   filters: [
//     {
//       type: 'select-tree',
//       label: '查看范围',
//       field: 'projectId',
//       options: useBusinessStore().projectList,
//       nodeClick: data => {
//         useBusinessStore().SET_selectedProject(data)
//         refreshData(data)
//       }
//     }
//   ],
//   defaultParams: {
//     projectId: useBusinessStore().selectedProject?.value
//   }
// })

const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  stretch: true,
  tabs: [],
  handleTabClick: (tab: any) => {
    console.log('动都不动不得不对不对', tab.props.name);
    state.ShuiChangDatas = allStation.value;
    if (tab.props.name !== '全部') {
      state.ShuiChangDatas = state.ShuiChangDatas.filter(
        (item) => item.title === tab.props.name || item.title === '全部'
      );
    }
  }
});
const refreshData = async (data?: NormalOption) => {
  // TreeData.loading = true
  await getWaterSupplyInfoView({
    projectId: data?.value || useBusinessStore().selectedProject?.value
  })
    .then((res) => {
      state.Statistics[0].count = res.data.data?.yesterdayWaterSupply || 0;
      state.Statistics[1].count = res.data.data?.todayWaterSupply || 0;
      state.Statistics[2].count = res.data.data?.lastMonthWaterSupply || 0;
      state.Statistics[3].count = res.data.data?.monthWaterSupply || 0;
      state.Statistics[4].count = res.data.data?.totalWaterSupply || 0;
      state.Statistics[5].count = res.data.data?.alarmNum || 0;
    })
    .catch(() => {
      state.Statistics[0].count = 0;
      state.Statistics[1].count = 0;
      state.Statistics[2].count = 0;
      state.Statistics[3].count = 0;
      state.Statistics[4].count = 0;
      state.Statistics[5].count = 0;
    });
  const res = await getWaterSupplyInfoDetail({
    projectId: data?.value
  });
  const stationDatas = res.data?.data;
  console.log(stationDatas);
  tabsConfig.tabs = [{ value: '全部', label: '全部' }];
  stationDatas.map((stationData) => {
    tabsConfig.tabs.push({ value: stationData.name, label: stationData.name });
    allStation.value.push({
      id: stationData.stationId,
      title: stationData.name,
      monitorData: stationData.dataList || []
    });
    state.ShuiChangDatas = allStation.value;
  });
};
onMounted(async () => {
  const treeData = await getStationTree('水源地');
  TreeData.data = treeData;
  TreeData.currentProject = treeData[0];
  await refreshData();
});
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}

.title-header {
  margin-right: auto;
}

.statistics {
  display: flex;
  justify-content: space-between;
}

.statistics-item {
  width: 16%;
  margin: 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  .item-inner {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    padding: 10px 2px;
    display: flex;
    align-items: center;
    font-size: 16px;

    .item-content {
      text-align: center;
      margin: 0 auto;
      word-break: break-word;
    }

    .total {
      // display: flex;
      align-items: baseline;
      margin-left: auto;
    }

    .item-text {
      margin: 0 8px;
    }

    .title {
      font-size: 16px;
    }

    .count {
      font-size: 22px;
    }

    .unit {
      font-size: 14px;
    }
  }
}

.blue {
  background: linear-gradient(to right, #399bff, #4ad2fd);
}

.text-blue {
  color: #399bff;
}

.green {
  background: linear-gradient(to right, #42c902, #6be819);
}

.text-green {
  color: #42c902;
}

.text-red {
  color: #af1313;
}

.orange {
  background: linear-gradient(to right, #f68936, #f9bf2f);
}

.purple {
  background: linear-gradient(to right, #8854fe, #8a76fe);
}

.light-blue {
  background: linear-gradient(to right, #57d6f3, #81e4f1);
}

.red {
  background: linear-gradient(to right, #f95252, #fb8d8d);
}
.card {
  height: calc(100vh - 240px);
}
:deep(.el-card__body) {
  padding: 12px;
  display: grid;
  align-content: center;
  justify-content: space-between;
  flex-wrap: wrap;
  // grid-template-rows: 425px 425px 425px;
  grid-template-columns: 32% 32% 32%;
}

.card-box {
  display: flex;
  width: 100%;
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }

  .card-item {
    height: 500px;
    width: 100%;
    overflow-y: auto;
    margin-bottom: 16px;
    padding: 12px;
    display: grid;
    align-content: center;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-template-columns: 32% 32% 32%;

    &:last-child {
      margin-bottom: 0;
    }

    &.isDark {
      :deep(.el-card__header) {
        background-color: #415c88;
      }
    }

    .card-title {
      display: flex;
      align-items: center;
    }

    .card-content {
      margin-bottom: 30px;

      // display: flex;
      // justify-content: space-between;
      // width: calc(50% - 8px);
      .time-box {
        color: #f54141;
        text-align: center;
        // background-color: #222536;
        border-bottom: 1px solid #f54141;
        padding: 5px 0;
      }
    }

    .inner-card {
      // width: calc(50% - 8px);
      // width: 30%;
      height: 430px;

      &.left {
        height: 415px;
      }
    }

    //   .chart-item {
    //     height: 200px;
    //     margin-bottom: 15px;
    //     &:last-child {
    //       margin-bottom: 0;
    //     }
    //   }
  }
}

.monitor-box {
  display: flex;
  flex-wrap: wrap;

  .monitor-item {
    flex-basis: 25%;
    min-height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border: 1px solid #e8e8ed;
    background-color: #fff;

    &.isDark {
      border: 1px solid #222536;
      background-color: #1a1d2d;
    }

    .count {
      color: #42b8fe;
      font-size: 18px;
      line-height: 30px;
    }

    .total {
      display: flex;
      align-items: baseline;
    }

    .label {
      color: #949ab8;
      font-size: 14px;
    }

    .unit {
      font-size: 12px;
      margin-left: 8px;
    }
  }
}
</style>
