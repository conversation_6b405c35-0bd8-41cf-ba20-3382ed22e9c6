package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoDeviceItemMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SoDeviceItemServiceImpl implements SoDeviceItemService {
    @Autowired
    private SoDeviceItemMapper mapper;

    @Override
    public IPage<SoDeviceItem> findAllConditional(SoDeviceItemPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Deprecated
    public SoDeviceItem save(SoDeviceItemSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);
    }

    @Override
    public boolean update(SoDeviceItem entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.remove(id) > 0;
    }

    @Override
    public List<SoDeviceItem> saveAll(List<SoDeviceItemSaveRequest> request, SoGeneralSystemScope scope) {
        if (request.size() == 0) {
            return Collections.emptyList();
        }
        if (!scope.getIsProject()) {
            List<SoDeviceItemSaveRequest> compare = mapper.selectBatchMaxRestCount(request, scope);
            Map<String, SoDeviceItemSaveRequest> priceMap = compare.stream().collect(Collectors.toMap(SoDeviceItemSaveRequest::getSerialId, o -> o));
            for (SoDeviceItemSaveRequest req : request) {
                String serialId = req.getSerialId();
                SoDeviceItemSaveRequest adjustInfo = priceMap.get(serialId);
                if (adjustInfo == null || adjustInfo.getAmount() == null) {
                    req.setAmount(null);
                    continue;
                }
                // 限制最大数量
                req.setAmount(Math.min(adjustInfo.getAmount(), req.getAmount()));
            }
        }
        request = request.stream().filter(x -> x != null && x.getAmount() != null && x.getAmount() > 0).collect(Collectors.toList());
        return QueryUtil.saveOrUpdateBatchByRequest(request, mapper::saveAll, mapper::updateAllFully);
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

}
