<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceType.DeviceMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.deviceManage.Device">
        select a.*, b.first_name as creatorName
        from m_device a
                 left join tb_user b on a.creator = b.id
        where a.serial_id like '%' || #{serialId} || '%'
          and a.name like '%' || #{name} || '%'
          and a.model like '%' || #{model} || '%'
          and a.tenant_id = #{tenantId}
        <if test="typeIdList != null and typeIdList.size() > 0">
            and a.type_id in
            <foreach collection="typeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by a.create_time desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>
    <select id="getListCount" resultType="int">
        select count(*)
        from m_device a
        where a.serial_id like '%' || #{serialId} || '%'
          and a.name like '%' || #{name} || '%'
          and a.model like '%' || #{model} || '%'
          and a.tenant_id = #{tenantId}
        <if test="typeIdList != null and typeIdList.size() > 0">
            and a.type_id in
            <foreach collection="typeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="Info_Column_List">
        <!--@sql select -->
        d.id,
        d.serial_id,
        d.model,
        d.name,
        d.type_id,
        <!--      根类型      -->
        device_get_root_type_name_by_type_id(d.type_id) as root_type,
        <!--    具体类型    -->
            (select indt.name from m_device_type indt where indt.id = d.type_id) as type,
        <!--    余量    -->
        <!--        device_rest_storage_count_by_serial_id(d.serial_id, d.tenant_id) as count,-->
        unit
        <!--@sql from m_device d-->
    </sql>
    <resultMap id="DeviceInfoResponseResultMap" type="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse">
        <result column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="model" property="model"/>
        <result column="name" property="name"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="shelves_code" property="shelvesCode"/>
        <result column="shelves_name" property="shelvesName"/>
        <result column="type_id" property="typeId"/>
        <result column="root_type" property="topType"/>
        <result column="type" property="type"/>
        <result column="unit" property="unit"/>
    </resultMap>
    <select id="getInfoBySerialId" resultMap="DeviceInfoResponseResultMap">
        select
        <include refid="Info_Column_List"/>,
                                               null as shelves_id,
                                               null as shelves_code,
                                               null as shelves_name
    from m_device d
    where serial_id = #{serialId}
      and tenant_id = #{tenantId}
    </select>
    <select id="getInfoByDeviceLabelCode" resultMap="DeviceInfoResponseResultMap">
        select
        <include refid="Info_Column_List"/>,
        <!--@formatter:off-->
        <!--shelves_id-->
(select shelves_id from device_storage_journal
    where device_label_code = #{labelCode} and tenant_id = d.tenant_id) as shelves_id,
        <!--shelves_code-->
(select code from goods_shelf where id =
    (select shelves_id from device_storage_journal where device_label_code = #{labelCode} and tenant_id = d.tenant_id)) as shelves_code,
        <!--shelves_name-->
(select name from goods_shelf where id =
    (select shelves_id from device_storage_journal where device_label_code = #{labelCode} and tenant_id = d.tenant_id)) as shelves_name
        <!--@formatter:on-->
        from m_device d
        where serial_id = (select serial_id from device_storage_journal where device_label_code = #{labelCode})
          and tenant_id = #{tenantId}
    </select>

    <select id="getFlowList" resultType="com.alibaba.fastjson.JSONObject">
        select distinct id, tenant_id from device a
        left join ts_kv_latest b on a.id = b.entity_id
        where b.key = 'total_flow'
    </select>
</mapper>