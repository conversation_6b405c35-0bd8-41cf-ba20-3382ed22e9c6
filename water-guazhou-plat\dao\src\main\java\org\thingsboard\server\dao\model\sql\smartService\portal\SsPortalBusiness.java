package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_business")
public class SsPortalBusiness {
    // id
    private String id;

    // 标题
    private String title;

    // 简介
    private String introduce;

    // 封面
    private String cover;

    // 是否跳转URL链接
    private Boolean jumpToUrl;

    // 链接
    private String link;

    // 业务内容
    private String content;

    // 是否显示
    private Boolean active;

    // 排序
    private String orderNumber;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
