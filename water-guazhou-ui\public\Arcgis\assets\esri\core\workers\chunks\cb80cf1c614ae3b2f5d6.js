"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9771,6502],{3388:(e,t,s)=>{var r;s.d(t,{i:()=>r}),function(e){e[e.PROJECT_VERTICES=1]="PROJECT_VERTICES"}(r||(r={}))},61159:(e,t,s)=>{s.d(t,{g:()=>r});const r={supportsStatistics:!0,supportsPercentileStatistics:!0,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsCentroid:!0,supportsCacheHint:!1,supportsDistance:!0,supportsDistinct:!0,supportsExtent:!0,supportsGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQuantization:!0,supportsQuantizationEditMode:!1,supportsQueryGeometry:!0,supportsResultType:!1,supportsSqlExpression:!0,supportsMaxRecordCountFactor:!1,supportsStandardizedQueriesOnly:!0,supportsTopFeaturesQuery:!1,supportsQueryByOthers:!0,supportsHistoricMoment:!1,supportsFormatPBF:!1,supportsDisjointSpatialRelationship:!0,supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsCompactGeometry:!1,maxRecordCountFactor:void 0,maxRecordCount:void 0,standardMaxRecordCount:void 0,tileMaxRecordCount:void 0}},6502:(e,t,s)=>{s.r(t),s.d(t,{default:()=>A});var r=s(43697),a=(s(66577),s(38171)),o=s(40330),n=s(3172),i=s(92835),u=s(20102),l=s(80442),d=s(35454),c=s(83379),p=s(70586),h=s(78286),y=s(95330),f=s(17452),m=s(5600),g=(s(75215),s(67676),s(52011)),_=s(6570),b=s(33955),R=s(3388),F=s(25278),S=s(74654),C=s(66677),w=s(59431),q=s(82971);const O=new d.X({originalAndCurrentFeatures:"original-and-current-features",none:"none"});async function x(e){return"string"==typeof e?(0,f.sJ)(e)||{data:e}:new Promise(((t,s)=>{const r=new FileReader;r.readAsDataURL(e),r.onload=()=>t((0,f.sJ)(r.result)),r.onerror=e=>s(e)}))}const I=new Set(["Feature Layer","Table"]),T=new d.X({Started:"published",Publishing:"publishing",Stopped:"unavailable"});let v=class extends c.Z{constructor(){super(...arguments),this.type="feature-layer",this.refresh=(0,y.Ds)((async()=>{await this.load();const e=this.sourceJSON.editingInfo?.lastEditDate;if(null==e)return{dataChanged:!0,updates:{}};try{await this._fetchService(null)}catch{return{dataChanged:!0,updates:{}}}const t=e!==this.sourceJSON.editingInfo?.lastEditDate;return{dataChanged:t,updates:t?{editingInfo:this.sourceJSON.editingInfo,extent:this.sourceJSON.extent}:null}}))}load(e){const t=(0,p.pC)(e)?e.signal:null,s=this.layer.sourceJSON;return this.addResolvingPromise(this._fetchService(s,t)),Promise.resolve(this)}get queryTask(){const{capabilities:e,parsedUrl:t,dynamicDataSource:s,infoFor3D:r,gdbVersion:a,spatialReference:o,fieldsIndex:n}=this.layer,i=(0,l.Z)("featurelayer-pbf")&&e?.query.supportsFormatPBF&&(0,p.Wi)(r),u=e?.operations?.supportsQueryAttachments??!1;return new S.Z({url:t.path,pbfSupported:i,fieldsIndex:n,infoFor3D:r,dynamicDataSource:s,gdbVersion:a,sourceSpatialReference:o,queryAttachmentsSupported:u})}async addAttachment(e,t){await this.load();const s=e.attributes[this.layer.objectIdField],r=this.layer.parsedUrl.path+"/"+s+"/addAttachment",a=this._getLayerRequestOptions(),o=this._getFormDataForAttachment(t,a.query);try{const e=await(0,n.default)(r,{body:o});return this._createFeatureEditResult(e.data.addAttachmentResult)}catch(e){throw this._createAttachmentErrorResult(s,e)}}async updateAttachment(e,t,s){await this.load();const r=e.attributes[this.layer.objectIdField],a=this.layer.parsedUrl.path+"/"+r+"/updateAttachment",o=this._getLayerRequestOptions({query:{attachmentId:t}}),i=this._getFormDataForAttachment(s,o.query);try{const e=await(0,n.default)(a,{body:i});return this._createFeatureEditResult(e.data.updateAttachmentResult)}catch(e){throw this._createAttachmentErrorResult(r,e)}}async applyEdits(e,t){await this.load();const s=this.layer.infoFor3D,r=(0,p.pC)(s),a=r||(t?.globalIdUsed??!1),i=e.addFeatures?.map((e=>this._serializeFeature(e,s))).filter(p.pC)??[],u=e.updateFeatures?.map((e=>this._serializeFeature(e,s))).filter(p.pC)??[],l=this._getFeatureIds(e.deleteFeatures,a);(0,w.P)(i,u,this.layer.spatialReference);const d=[],c=[],h=[...e.deleteAttachments??[]];for(const t of e.addAttachments??[])d.push(await this._serializeAttachment(t));for(const t of e.updateAttachments??[])c.push(await this._serializeAttachment(t));const y=d.length||c.length||h.length?{adds:d,updates:c,deletes:h}:null;let f,m=null;if(r){m=new Map;const t=[];for(const s of e.addAssets??[])t.push(this._serializeAssetMapEditAndUploadAssets(s,m));const s=await Promise.all(t);f=s.length?{adds:s,updates:[],deletes:[]}:void 0}const g={gdbVersion:t?.gdbVersion||this.layer.gdbVersion,rollbackOnFailure:t?.rollbackOnFailureEnabled,useGlobalIds:a,returnEditMoment:t?.returnEditMoment,usePreviousEditMoment:t?.usePreviousEditMoment,sessionId:t?.sessionId};t?.returnServiceEditsOption?(g.edits=JSON.stringify([{id:this.layer.layerId,adds:i,updates:u,deletes:l,attachments:y,assetMaps:(0,p.Wg)(f)}]),g.returnServiceEditsOption=O.toJSON(t?.returnServiceEditsOption),g.returnServiceEditsInSourceSR=t?.returnServiceEditsInSourceSR):(g.adds=i.length?JSON.stringify(i):null,g.updates=u.length?JSON.stringify(u):null,g.deletes=l.length?a?JSON.stringify(l):l.join(","):null,g.attachments=y&&JSON.stringify(y),g.assetMaps=(0,p.pC)(f)?JSON.stringify(f):void 0);const _=this._getLayerRequestOptions({method:"post",query:g}),b=t?.returnServiceEditsOption?this.layer.url:this.layer.parsedUrl.path,R=await(0,n.default)(b+"/applyEdits",_);if(!this.layer.capabilities.operations?.supportsEditing&&this.layer.effectiveCapabilities?.operations?.supportsEditing){const e=o.id?.findCredential(this.layer.url);await(e?.refreshToken())}if(r&&null!=R.data&&null!=R.data.assetMaps){const e=R.data,t=this.layer.objectIdField,s=[];for(const t of e.addResults)t.success&&s.push(t.objectId);for(const t of e.updateResults)t.success&&s.push(t.objectId);const r=this._createRequestQueryOptions(),a=await(0,n.default)(b+"/query",{...r,query:{f:"json",formatOf3DObjects:"3D_glb",where:`OBJECTID IN (${s.join(",")})`,outFields:`${t}`}});if(a&&a.data&&a.data.assetMaps&&(0,p.pC)(m)){const e=a.data.assetMaps;for(const t of e){const e=m.get(t.parentGlobalId).geometry;(0,p.pC)(e)&&"mesh"===e.type&&e.updateExternalSource({source:[{name:t.assetName,source:t.assetName}],extent:e.extent})}}}return this._createEditsResult(R)}async deleteAttachments(e,t){await this.load();const s=e.attributes[this.layer.objectIdField],r=this.layer.parsedUrl.path+"/"+s+"/deleteAttachments";try{return(await(0,n.default)(r,this._getLayerRequestOptions({query:{attachmentIds:t.join(",")},method:"post"}))).data.deleteAttachmentResults.map(this._createFeatureEditResult)}catch(e){throw this._createAttachmentErrorResult(s,e)}}fetchRecomputedExtents(e={}){const t=e.signal;return this.load({signal:t}).then((async()=>{const t=this._getLayerRequestOptions({...e,query:{returnUpdates:!0}}),{layerId:s,url:r}=this.layer,{data:a}=await(0,n.default)(`${r}/${s}`,t),{id:o,extent:u,fullExtent:l,timeExtent:d}=a,c=u||l;return{id:o,fullExtent:c&&_.Z.fromJSON(c),timeExtent:d&&i.Z.fromJSON({start:d[0],end:d[1]})}}))}async queryAttachments(e,t={}){await this.load();const s=this._getLayerRequestOptions(t);return this.queryTask.executeAttachmentQuery(e,s)}async queryFeatures(e,t){return await this.load(),this.queryTask.execute(e,{...t,query:this._createRequestQueryOptions(t)})}async queryFeaturesJSON(e,t){return await this.load(),this.queryTask.executeJSON(e,{...t,query:this._createRequestQueryOptions(t)})}async queryObjectIds(e,t){return await this.load(),this.queryTask.executeForIds(e,{...t,query:this._createRequestQueryOptions(t)})}async queryFeatureCount(e,t){return await this.load(),this.queryTask.executeForCount(e,{...t,query:this._createRequestQueryOptions(t)})}async queryExtent(e,t){return await this.load(),this.queryTask.executeForExtent(e,{...t,query:this._createRequestQueryOptions(t)})}async queryRelatedFeatures(e,t){return await this.load(),this.queryTask.executeRelationshipQuery(e,{...t,query:this._createRequestQueryOptions(t)})}async queryRelatedFeaturesCount(e,t){return await this.load(),this.queryTask.executeRelationshipQueryForCount(e,{...t,query:this._createRequestQueryOptions(t)})}async queryTopFeatures(e,t){return await this.load(),this.queryTask.executeTopFeaturesQuery(e,{...t,query:this._createRequestQueryOptions(t)})}async queryTopObjectIds(e,t){return await this.load(),this.queryTask.executeForTopIds(e,{...t,query:this._createRequestQueryOptions(t)})}async queryTopExtents(e,t){return await this.load(),this.queryTask.executeForTopExtents(e,{...t,query:this._createRequestQueryOptions(t)})}async queryTopCount(e,t){return await this.load(),this.queryTask.executeForTopCount(e,{...t,query:this._createRequestQueryOptions(t)})}async fetchPublishingStatus(){if(!(0,C.M8)(this.layer.url))return"unavailable";const e=(0,f.v_)(this.layer.url,"status"),t=await(0,n.default)(e,{query:{f:"json"}});return T.fromJSON(t.data.status)}_createRequestQueryOptions(e){const t={...this.layer.customParameters,token:this.layer.apiKey,...e?.query};return this.layer.datesInUnknownTimezone&&(t.timeReferenceUnknownClient=!0),t}async _fetchService(e,t){if(!e){const{data:s}=await(0,n.default)(this.layer.parsedUrl.path,this._getLayerRequestOptions({query:(0,l.Z)("featurelayer-advanced-symbols")?{returnAdvancedSymbols:!0}:{},signal:t}));e=s}this.sourceJSON=this._patchServiceJSON(e);const s=e.type;if(!I.has(s))throw new u.Z("feature-layer-source:unsupported-type",`Source type "${s}" is not supported`)}_patchServiceJSON(e){if("Table"!==e.type&&e.geometryType&&!e?.drawingInfo?.renderer&&!e.defaultSymbol){const t=(0,F.bU)(e.geometryType).renderer;(0,h.RB)("drawingInfo.renderer",t,e)}return"esriGeometryMultiPatch"===e.geometryType&&e.infoFor3D&&(e.geometryType="mesh"),e}_serializeFeature(e,t){const{geometry:s,attributes:r}=e;if((0,p.pC)(t)&&(0,p.pC)(e.geometry)&&"mesh"===e.geometry.type){const s={...r},a=e.geometry,o=a.origin,n=a.transform;if(s[t.transformFieldRoles.originX]=o.x,s[t.transformFieldRoles.originY]=o.y,s[t.transformFieldRoles.originZ]=o.z,(0,p.pC)(n)){const e=n.translation,r=n.scale,a=n.rotation;s[t.transformFieldRoles.translationX]=e[0],s[t.transformFieldRoles.translationY]=-e[2],s[t.transformFieldRoles.translationZ]=e[1],s[t.transformFieldRoles.scaleX]=r[0],s[t.transformFieldRoles.scaleY]=r[1],s[t.transformFieldRoles.scaleZ]=r[2],s[t.transformFieldRoles.rotationX]=a[0],s[t.transformFieldRoles.rotationY]=a[2],s[t.transformFieldRoles.rotationZ]=a[1],s[t.transformFieldRoles.rotationDeg]=a[3]}return{geometry:null,attributes:s}}return(0,p.Wi)(s)?{attributes:r}:"mesh"===s.type||"extent"===s.type?null:{geometry:s.toJSON(),attributes:r}}async _serializeAttachment(e){const{feature:t,attachment:s}=e,{globalId:r,name:a,contentType:o,data:n,uploadId:i}=s,u={globalId:r,parentGlobalId:null,contentType:null,name:null,uploadId:null,data:null};if(t&&(u.parentGlobalId="attributes"in t?t.attributes&&t.attributes[this.layer.globalIdField]:t.globalId),i)u.uploadId=i;else if(n){const e=await x(n);e&&(u.contentType=e.mediaType,u.data=e.data),n instanceof File&&(u.name=n.name)}return a&&(u.name=a),o&&(u.contentType=o),u}async _serializeAssetMapEditAndUploadAssets(e,t){const s=this.layer.url;let r=null;try{const t=new Blob([e.data],{type:e.mimeType}),a=new FormData;a.append("f","json"),a.append("file",t,`${e.assetName}`);const o={body:a,method:"post",responseType:"json"},{data:i}=await(0,n.default)(`${s}/uploads/upload`,o);if(!i.success)throw new u.Z("feature-layer-source:upload-failure","Expected upload to be successfull.");r={assetType:e.assetType,assetUploadId:i.item.itemID}}catch(e){r=null}if((0,p.Wi)(r)){const t=await x(new Blob([e.data]));if(!t.isBase64)throw new u.Z("feature-layer-source:uploadAssets-failure","Expected gltf data in base64 format after conversion.");r={assetType:e.assetType,assetData:t.data}}if((0,p.Wi)(r))throw new u.Z("feature-layer-source:uploadAssets-failure","Unable to prepare uploadAsset request options.");const a={method:"post",query:{f:"json",assets:JSON.stringify([r])},responseType:"json"},o=await(0,n.default)((0,f.v_)(this.layer.parsedUrl.path,"uploadAssets"),a);if(1!==o.data.uploadResults.length||!o.data.uploadResults[0].success)throw new u.Z("feature-layer-source:uploadAssets-failure","Bad response.");const i=o.data.uploadResults[0].assetHash,l=[];e.flags&R.i.PROJECT_VERTICES&&l.push("PROJECT_VERTICES");const d={globalId:e.assetMapGlobalId,parentGlobalId:e.featureGlobalId,assetName:e.assetName,assetHash:i,flags:l};return t.set(e.featureGlobalId,e.feature),d}_getFeatureIds(e,t){const s=e?.[0];return s?this._canUseGlobalIds(t,e)?this._getGlobalIdsFromFeatureIdentifier(e):"objectId"in s?this._getObjectIdsFromFeatureIdentifier(e):this._getIdsFromFeatures(e):[]}_getIdsFromFeatures(e){const t=this.layer.objectIdField;return e.map((e=>e.attributes&&e.attributes[t]))}_canUseGlobalIds(e,t){return e&&"globalId"in t[0]}_getObjectIdsFromFeatureIdentifier(e){return e.map((e=>e.objectId))}_getGlobalIdsFromFeatureIdentifier(e){return e.map((e=>e.globalId))}_createEditsResult(e){const t=e.data,{layerId:s}=this.layer,r=[];let a=null;if(Array.isArray(t))for(const e of t)r.push({id:e.id,editedFeatures:e.editedFeatures}),e.id===s&&(a={addResults:e.addResults??[],updateResults:e.updateResults??[],deleteResults:e.deleteResults??[],attachments:e.attachments,editMoment:e.editMoment});else a=t;const o=a?.attachments,n={addFeatureResults:a?.addResults?.map(this._createFeatureEditResult,this)??[],updateFeatureResults:a?.updateResults?.map(this._createFeatureEditResult,this)??[],deleteFeatureResults:a?.deleteResults?.map(this._createFeatureEditResult,this)??[],addAttachmentResults:o&&o.addResults?o.addResults.map(this._createFeatureEditResult,this):[],updateAttachmentResults:o&&o.updateResults?o.updateResults.map(this._createFeatureEditResult,this):[],deleteAttachmentResults:o&&o.deleteResults?o.deleteResults.map(this._createFeatureEditResult,this):[]};if(a?.editMoment&&(n.editMoment=a.editMoment),r.length>0){n.editedFeatureResults=[];for(const e of r){const{editedFeatures:t}=e,s=t?.spatialReference?new q.Z(t.spatialReference):null;n.editedFeatureResults.push({layerId:e.id,editedFeatures:{adds:t?.adds?.map((e=>this._createEditedFeature(e,s)))||[],updates:t?.updates?.map((e=>({original:this._createEditedFeature(e[0],s),current:this._createEditedFeature(e[1],s)})))||[],deletes:t?.deletes?.map((e=>this._createEditedFeature(e,s)))||[],spatialReference:s}})}}return n}_createEditedFeature(e,t){return new a.Z({attributes:e.attributes,geometry:(0,b.im)({...e.geometry,spatialReference:t})})}_createFeatureEditResult(e){const t=!0===e.success?null:e.error||{code:void 0,description:void 0};return{objectId:e.objectId,globalId:e.globalId,error:t?new u.Z("feature-layer-source:edit-failure",t.description,{code:t.code}):null}}_createAttachmentErrorResult(e,t){const s=t.details.messages&&t.details.messages[0]||t.message,r=t.details.httpStatus||t.details.messageCode;return{objectId:e,globalId:null,error:new u.Z("feature-layer-source:attachment-failure",s,{code:r})}}_getFormDataForAttachment(e,t){const s=e instanceof FormData?e:e&&e.elements?new FormData(e):null;if(s)for(const e in t){const r=t[e];null!=r&&(s.set?s.set(e,r):s.append(e,r))}return s}_getLayerRequestOptions(e={}){const{parsedUrl:t,gdbVersion:s,dynamicDataSource:r}=this.layer;return{...e,query:{gdbVersion:s,layer:r?JSON.stringify({source:r}):void 0,...t.query,f:"json",...this._createRequestQueryOptions(e)},responseType:"json"}}};(0,r._)([(0,m.Cb)()],v.prototype,"type",void 0),(0,r._)([(0,m.Cb)({constructOnly:!0})],v.prototype,"layer",void 0),(0,r._)([(0,m.Cb)({readOnly:!0})],v.prototype,"queryTask",null),v=(0,r._)([(0,g.j)("esri.layers.graphics.sources.FeatureLayerSource")],v);const A=v},74654:(e,t,s)=>{s.d(t,{Z:()=>A});var r=s(43697),a=s(15923),o=s(20102),n=s(80442),i=s(70586),u=s(95330),l=s(17452),d=s(5600),c=(s(75215),s(67676),s(52011)),p=s(10158),h=s(11282),y=s(41818),f=(s(66577),s(34599)),m=s(14165),g=s(6570),_=s(5396),b=s(4967),R=s(69285),F=s(98732);function S(e,t){return t}function C(e,t,s,r){switch(s){case 0:return x(e,t+r,0);case 1:return"lowerLeft"===e.originPosition?x(e,t+r,1):function({translate:e,scale:t},s,r){return e[r]-s*t[r]}(e,t+r,1)}}function w(e,t,s,r){return 2===s?x(e,t,2):C(e,t,s,r)}function q(e,t,s,r){return 2===s?x(e,t,3):C(e,t,s,r)}function O(e,t,s,r){return 3===s?x(e,t,3):w(e,t,s,r)}function x({translate:e,scale:t},s,r){return e[r]+s*t[r]}class I{constructor(e){this._options=e,this.geometryTypes=["esriGeometryPoint","esriGeometryMultipoint","esriGeometryPolyline","esriGeometryPolygon"],this._previousCoordinate=[0,0],this._transform=null,this._applyTransform=S,this._lengths=[],this._currentLengthIndex=0,this._toAddInCurrentPath=0,this._vertexDimension=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,this._attributesConstructor=class{}}createFeatureResult(){return{fields:[],features:[]}}finishFeatureResult(e){if(this._options.applyTransform&&(e.transform=null),this._attributesConstructor=class{},this._coordinateBuffer=null,this._lengths.length=0,!e.hasZ)return;const t=(0,R.k)(e.geometryType,this._options.sourceSpatialReference,e.spatialReference);if(!(0,i.Wi)(t))for(const s of e.features)t(s.geometry)}createSpatialReference(){return{}}addField(e,t){const s=e.fields;(0,i.O3)(s),s.push(t);const r=s.map((e=>e.name));this._attributesConstructor=function(){for(const e of r)this[e]=null}}addFeature(e,t){e.features.push(t)}prepareFeatures(e){switch(this._transform=e.transform,this._options.applyTransform&&e.transform&&(this._applyTransform=this._deriveApplyTransform(e)),this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++,e.geometryType){case"esriGeometryPoint":this.addCoordinate=(e,t,s)=>this.addCoordinatePoint(e,t,s),this.createGeometry=e=>this.createPointGeometry(e);break;case"esriGeometryPolygon":this.addCoordinate=(e,t,s)=>this._addCoordinatePolygon(e,t,s),this.createGeometry=e=>this._createPolygonGeometry(e);break;case"esriGeometryPolyline":this.addCoordinate=(e,t,s)=>this._addCoordinatePolyline(e,t,s),this.createGeometry=e=>this._createPolylineGeometry(e);break;case"esriGeometryMultipoint":this.addCoordinate=(e,t,s)=>this._addCoordinateMultipoint(e,t,s),this.createGeometry=e=>this._createMultipointGeometry(e)}}createFeature(){return this._lengths.length=0,this._currentLengthIndex=0,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,{attributes:new this._attributesConstructor}}allocateCoordinates(){}addLength(e,t,s){0===this._lengths.length&&(this._toAddInCurrentPath=t),this._lengths.push(t)}addQueryGeometry(e,t){const{queryGeometry:s,queryGeometryType:r}=t,a=(0,F.$g)(s.clone(),s,!1,!1,this._transform),o=(0,F.di)(a,r,!1,!1);e.queryGeometryType=r,e.queryGeometry={...o}}createPointGeometry(e){const t={x:0,y:0,spatialReference:e.spatialReference};return e.hasZ&&(t.z=0),e.hasM&&(t.m=0),t}addCoordinatePoint(e,t,s){const r=(0,i.s3)(this._transform,"transform");switch(t=this._applyTransform(r,t,s,0),s){case 0:e.x=t;break;case 1:e.y=t;break;case 2:"z"in e?e.z=t:e.m=t;break;case 3:e.m=t}}_transformPathLikeValue(e,t){let s=0;t<=1&&(s=this._previousCoordinate[t],this._previousCoordinate[t]+=e);const r=(0,i.s3)(this._transform,"transform");return this._applyTransform(r,e,t,s)}_addCoordinatePolyline(e,t,s){this._dehydratedAddPointsCoordinate(e.paths,t,s)}_addCoordinatePolygon(e,t,s){this._dehydratedAddPointsCoordinate(e.rings,t,s)}_addCoordinateMultipoint(e,t,s){0===s&&e.points.push([]);const r=this._transformPathLikeValue(t,s);e.points[e.points.length-1].push(r)}_createPolygonGeometry(e){return{rings:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createPolylineGeometry(e){return{paths:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createMultipointGeometry(e){return{points:[],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_dehydratedAddPointsCoordinate(e,t,s){0===s&&0==this._toAddInCurrentPath--&&(e.push([]),this._toAddInCurrentPath=this._lengths[++this._currentLengthIndex]-1,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0);const r=this._transformPathLikeValue(t,s),a=e[e.length-1];0===s&&(this._coordinateBufferPtr=0,this._coordinateBuffer=new Array(this._vertexDimension),a.push(this._coordinateBuffer)),this._coordinateBuffer[this._coordinateBufferPtr++]=r}_deriveApplyTransform(e){const{hasZ:t,hasM:s}=e;return t&&s?O:t?w:s?q:C}}var T=s(74889);let v=class extends a.Z{constructor(e){super(e),this.dynamicDataSource=null,this.fieldsIndex=null,this.gdbVersion=null,this.infoFor3D=null,this.pbfSupported=!1,this.queryAttachmentsSupported=!1,this.sourceSpatialReference=null,this.url=null}get parsedUrl(){return(0,l.mN)(this.url)}async execute(e,t){const s=await this.executeJSON(e,t);return this.featureSetFromJSON(e,s,t)}async executeJSON(e,t){const s=this._normalizeQuery(e),r=null!=e.outStatistics?.[0],a=(0,n.Z)("featurelayer-pbf-statistics"),o=!r||a;let i;if(this.pbfSupported&&o)try{i=await async function(e,t,s){const r=(0,h.en)(e),a={...s},o=m.Z.from(t),n=!o.quantizationParameters,{data:i}=await(0,f.qp)(r,o,new I({sourceSpatialReference:o.sourceSpatialReference,applyTransform:n}),a);return i}(this.url,s,t)}catch(e){if("query:parsing-pbf"!==e.name)throw e;this.pbfSupported=!1}return this.pbfSupported&&o||(i=await(0,b.F)(this.url,s,t)),this._normalizeFields(i.fields),i}async featureSetFromJSON(e,t,r){if(!this._queryIs3DObjectFormat(e)||(0,i.Wi)(this.infoFor3D)||!t.assetMaps||!t.features||!t.features.length)return T.Z.fromJSON(t);const{meshFeatureSetFromJSON:a}=await(0,u.Hl)(Promise.all([s.e(6481),s.e(6459),s.e(1932)]).then(s.bind(s,21932)),r);return a(e,this.infoFor3D,t)}executeForCount(e,t){return(0,y.P)(this.url,this._normalizeQuery(e),t)}executeForExtent(e,t){return async function(e,t,s){const r=(0,h.en)(e);return(0,f.Vr)(r,m.Z.from(t),{...s}).then((e=>({count:e.data.count,extent:g.Z.fromJSON(e.data.extent)})))}(this.url,this._normalizeQuery(e),t)}executeForIds(e,t){return(0,_.G)(this.url,this._normalizeQuery(e),t)}async executeRelationshipQuery(e,t){const[{default:r},{executeRelationshipQuery:a}]=await(0,u.Hl)(Promise.all([s.e(5935).then(s.bind(s,75935)),s.e(1073).then(s.bind(s,81073))]),t);return e=r.from(e),(this.gdbVersion||this.dynamicDataSource)&&((e=e.clone()).gdbVersion=e.gdbVersion||this.gdbVersion,e.dynamicDataSource=e.dynamicDataSource||this.dynamicDataSource),a(this.url,e,t)}async executeRelationshipQueryForCount(e,t){const[{default:r},{executeRelationshipQueryForCount:a}]=await(0,u.Hl)(Promise.all([s.e(5935).then(s.bind(s,75935)),s.e(1073).then(s.bind(s,81073))]),t);return e=r.from(e),(this.gdbVersion||this.dynamicDataSource)&&((e=e.clone()).gdbVersion=e.gdbVersion||this.gdbVersion,e.dynamicDataSource=e.dynamicDataSource||this.dynamicDataSource),a(this.url,e,t)}async executeAttachmentQuery(e,t){const{executeAttachmentQuery:r,fetchAttachments:a,processAttachmentQueryResult:o}=await(0,u.Hl)(s.e(540).then(s.bind(s,30540)),t),n=(0,h.en)(this.url);return o(n,await(this.queryAttachmentsSupported?r(n,e,t):a(n,e,t)))}async executeTopFeaturesQuery(e,t){const{executeTopFeaturesQuery:r}=await(0,u.Hl)(s.e(1158).then(s.bind(s,71158)),t);return r(this.parsedUrl,e,this.sourceSpatialReference,t)}async executeForTopIds(e,t){const{executeForTopIds:r}=await(0,u.Hl)(s.e(3992).then(s.bind(s,33992)),t);return r(this.parsedUrl,e,t)}async executeForTopExtents(e,t){const{executeForTopExtents:r}=await(0,u.Hl)(s.e(1790).then(s.bind(s,21790)),t);return r(this.parsedUrl,e,t)}async executeForTopCount(e,t){const{executeForTopCount:r}=await(0,u.Hl)(s.e(4371).then(s.bind(s,14371)),t);return r(this.parsedUrl,e,t)}_normalizeQuery(e){let t=m.Z.from(e);if(t.sourceSpatialReference=t.sourceSpatialReference||this.sourceSpatialReference,(this.gdbVersion||this.dynamicDataSource)&&(t=t===e?t.clone():t,t.gdbVersion=e.gdbVersion||this.gdbVersion,t.dynamicDataSource=e.dynamicDataSource?p.n.from(e.dynamicDataSource):this.dynamicDataSource),(0,i.pC)(this.infoFor3D)&&this._queryIs3DObjectFormat(e)){t=t===e?t.clone():t,t.formatOf3DObjects=null;for(const e of this.infoFor3D.queryFormats){if("3D_glb"===e){t.formatOf3DObjects=e;break}"3D_gltf"!==e||t.formatOf3DObjects||(t.formatOf3DObjects=e)}if(!t.formatOf3DObjects)throw new o.Z("query:unsupported-3d-query-formats","Could not find any supported 3D object query format. Only supported formats are 3D_glb and 3D_gltf");if((0,i.Wi)(t.outFields)||!t.outFields.includes("*")){t=t===e?t.clone():t,(0,i.Wi)(t.outFields)&&(t.outFields=[]);const{originX:s,originY:r,originZ:a,translationX:o,translationY:n,translationZ:u,scaleX:l,scaleY:d,scaleZ:c,rotationX:p,rotationY:h,rotationZ:y,rotationDeg:f}=this.infoFor3D.transformFieldRoles;t.outFields.push(s,r,a,o,n,u,l,d,c,p,h,y,f)}}return t}_normalizeFields(e){if((0,i.pC)(this.fieldsIndex)&&(0,i.pC)(e))for(const t of e){const e=this.fieldsIndex.get(t.name);e&&Object.assign(t,e.toJSON())}}_queryIs3DObjectFormat(e){return(0,i.pC)(this.infoFor3D)&&!0===e.returnGeometry&&"xyFootprint"!==e.multipatchOption&&!e.outStatistics}};(0,r._)([(0,d.Cb)({type:p.n})],v.prototype,"dynamicDataSource",void 0),(0,r._)([(0,d.Cb)()],v.prototype,"fieldsIndex",void 0),(0,r._)([(0,d.Cb)()],v.prototype,"gdbVersion",void 0),(0,r._)([(0,d.Cb)()],v.prototype,"infoFor3D",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],v.prototype,"parsedUrl",null),(0,r._)([(0,d.Cb)()],v.prototype,"pbfSupported",void 0),(0,r._)([(0,d.Cb)()],v.prototype,"queryAttachmentsSupported",void 0),(0,r._)([(0,d.Cb)()],v.prototype,"sourceSpatialReference",void 0),(0,r._)([(0,d.Cb)({type:String})],v.prototype,"url",void 0),v=(0,r._)([(0,c.j)("esri.tasks.QueryTask")],v);const A=v},25278:(e,t,s)=>{s.d(t,{Dm:()=>d,Hq:()=>c,MS:()=>p,bU:()=>i});var r=s(80442),a=s(22974),o=s(61159),n=s(58333);function i(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?n.I4:"esriGeometryPolyline"===e?n.ET:n.lF}}}const u=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let l=1;function d(e,t){if((0,r.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let s=`this.${t} = null;`;for(const t in e)s+=`this${u.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const r=new Function(`\n      return class AttributesClass$${l++} {\n        constructor() {\n          ${s};\n        }\n      }\n    `)();return()=>new r}catch(s){return()=>({[t]:null,...e})}}function c(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,a.d9)(e)}}]}function p(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:o.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},41818:(e,t,s)=>{s.d(t,{P:()=>n});var r=s(11282),a=s(34599),o=s(14165);async function n(e,t,s){const n=(0,r.en)(e);return(0,a.hH)(n,o.Z.from(t),{...s}).then((e=>e.data.count))}},5396:(e,t,s)=>{s.d(t,{G:()=>n});var r=s(11282),a=s(34599),o=s(14165);async function n(e,t,s){const n=(0,r.en)(e);return(0,a.Ev)(n,o.Z.from(t),{...s}).then((e=>e.data.objectIds))}},4967:(e,t,s)=>{s.d(t,{F:()=>u,e:()=>i});var r=s(11282),a=s(34599),o=s(74889),n=s(14165);async function i(e,t,s){const r=await u(e,t,s);return o.Z.fromJSON(r)}async function u(e,t,s){const o=(0,r.en)(e),i={...s},u=n.Z.from(t),{data:l}=await(0,a.JT)(o,u,u.sourceSpatialReference,i);return l}},59431:(e,t,s)=>{s.d(t,{P:()=>i});var r=s(70586),a=s(67900),o=s(8744);function n(e,t,s){if(null==e.hasM||e.hasZ)for(const e of t)for(const t of e)t.length>2&&(t[2]*=s)}function i(e,t,s){if(!e&&!t||!s)return;const r=(0,a._R)(s);u(e,s,r),u(t,s,r)}function u(e,t,s){if(e)for(const r of e)l(r.geometry,t,s)}function l(e,t,s){if((0,r.Wi)(e)||!e.spatialReference||(0,o.fS)(e.spatialReference,t))return;const i=(0,a._R)(e.spatialReference)/s;if(1!==i)if("x"in e)null!=e.z&&(e.z*=i);else if("rings"in e)n(e,e.rings,i);else if("paths"in e)n(e,e.paths,i);else if("points"in e&&(null==e.hasM||e.hasZ))for(const t of e.points)t.length>2&&(t[2]*=i)}},28694:(e,t,s)=>{s.d(t,{p:()=>o});var r=s(70586),a=s(69285);function o(e,t,s){if(!s||!s.features||!s.hasZ)return;const o=(0,a.k)(s.geometryType,t,e.outSpatialReference);if(!(0,r.Wi)(o))for(const e of s.features)o(e.geometry)}},74889:(e,t,s)=>{s.d(t,{Z:()=>R});var r,a=s(43697),o=s(66577),n=s(38171),i=s(35454),u=s(96674),l=s(22974),d=s(70586),c=s(5600),p=(s(75215),s(71715)),h=s(52011),y=s(30556),f=s(82971),m=s(33955),g=s(1231);const _=new i.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let b=r=class extends u.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const s=f.Z.fromJSON(t.spatialReference),r=[];for(let t=0;t<e.length;t++){const a=e[t],o=n.Z.fromJSON(a),i=a.geometry&&a.geometry.spatialReference;(0,d.pC)(o.geometry)&&!i&&(o.geometry.spatialReference=s);const u=a.aggregateGeometries,l=o.aggregateGeometries;if(u&&(0,d.pC)(l))for(const e in l){const t=l[e],r=u[e]?.spatialReference;(0,d.pC)(t)&&!r&&(t.spatialReference=s)}r.push(o)}return r}writeGeometryType(e,t,s,r){if(e)return void _.write(e,t,s,r);const{features:a}=this;if(a)for(const e of a)if(e&&(0,d.pC)(e.geometry))return void _.write(e.geometry.type,t,s,r)}readQueryGeometry(e,t){if(!e)return null;const s=!!e.spatialReference,r=(0,m.im)(e);return r&&!s&&t.spatialReference&&(r.spatialReference=f.Z.fromJSON(t.spatialReference)),r}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:s}=this;if(s)for(const e of s)if(e&&(0,d.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new r(this.cloneProperties())}cloneProperties(){return(0,l.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let s=0;s<t.features.length;s++){const r=t.features[s];if(r.geometry){const t=e&&e[s];r.geometry=t&&t.toJSON()||r.geometry}}return t}quantize(e){const{scale:[t,s],translate:[r,a]}=e,o=this.features,n=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-r)/t)),(e=>Math.round((a-e)/s)));for(let e=0,t=o.length;e<t;e++)n?.((0,d.Wg)(o[e].geometry))||(o.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:s}=this;if(!s)return this;const{translate:[r,a],scale:[o,n]}=s,i=this._getHydrationFunction(e,(e=>e*o+r),(e=>a-e*n));for(const{geometry:e}of t)(0,d.pC)(e)&&i&&i(e);return this.transform=null,this}_quantizePoints(e,t,s){let r,a;const o=[];for(let n=0,i=e.length;n<i;n++){const i=e[n];if(n>0){const e=t(i[0]),n=s(i[1]);e===r&&n===a||(o.push([e-r,n-a]),r=e,a=n)}else r=t(i[0]),a=s(i[1]),o.push([r,a])}return o.length>0?o:null}_getQuantizationFunction(e,t,s){return"point"===e?e=>(e.x=t(e.x),e.y=s(e.y),e):"polyline"===e||"polygon"===e?e=>{const r=(0,m.oU)(e)?e.rings:e.paths,a=[];for(let e=0,o=r.length;e<o;e++){const o=r[e],n=this._quantizePoints(o,t,s);n&&a.push(n)}return a.length>0?((0,m.oU)(e)?e.rings=a:e.paths=a,e):null}:"multipoint"===e?e=>{const r=this._quantizePoints(e.points,t,s);return r&&r.length>0?(e.points=r,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,s){return"point"===e?e=>{e.x=t(e.x),e.y=s(e.y)}:"polyline"===e||"polygon"===e?e=>{const r=(0,m.oU)(e)?e.rings:e.paths;let a,o;for(let e=0,n=r.length;e<n;e++){const n=r[e];for(let e=0,r=n.length;e<r;e++){const r=n[e];e>0?(a+=r[0],o+=r[1]):(a=r[0],o=r[1]),r[0]=t(a),r[1]=s(o)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=s(e.ymin),e.xmax=t(e.xmax),e.ymax=s(e.ymax)}:"multipoint"===e?e=>{const r=e.points;let a,o;for(let e=0,n=r.length;e<n;e++){const n=r[e];e>0?(a+=n[0],o+=n[1]):(a=n[0],o=n[1]),n[0]=t(a),n[1]=s(o)}}:null}};(0,a._)([(0,c.Cb)({type:String,json:{write:!0}})],b.prototype,"displayFieldName",void 0),(0,a._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"exceededTransferLimit",void 0),(0,a._)([(0,c.Cb)({type:[n.Z],json:{write:!0}})],b.prototype,"features",void 0),(0,a._)([(0,p.r)("features")],b.prototype,"readFeatures",null),(0,a._)([(0,c.Cb)({type:[g.Z],json:{write:!0}})],b.prototype,"fields",void 0),(0,a._)([(0,c.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:_.read}}})],b.prototype,"geometryType",void 0),(0,a._)([(0,y.c)("geometryType")],b.prototype,"writeGeometryType",null),(0,a._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasM",void 0),(0,a._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasZ",void 0),(0,a._)([(0,c.Cb)({types:o.qM,json:{write:!0}})],b.prototype,"queryGeometry",void 0),(0,a._)([(0,p.r)("queryGeometry")],b.prototype,"readQueryGeometry",null),(0,a._)([(0,c.Cb)({type:f.Z,json:{write:!0}})],b.prototype,"spatialReference",void 0),(0,a._)([(0,y.c)("spatialReference")],b.prototype,"writeSpatialReference",null),(0,a._)([(0,c.Cb)({json:{write:!0}})],b.prototype,"transform",void 0),b=r=(0,a._)([(0,h.j)("esri.rest.support.FeatureSet")],b),b.prototype.toJSON.isDefaultToJSON=!0;const R=b}}]);