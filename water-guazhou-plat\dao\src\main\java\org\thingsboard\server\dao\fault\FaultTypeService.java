package org.thingsboard.server.dao.fault;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.fault.FaultType;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface FaultTypeService {

    PageData getList(String deviceTypeId, String name, int page, int size, String tenantId);

    FaultType save(FaultType faultType);

    IstarResponse delete(List<String> ids);
}
