package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity;

import java.util.List;

@Controller
@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface DeviceModelInfoClient {

    @RequestMapping(value = "api/device/modelInfo/getByDeviceId/{deviceId}", method = RequestMethod.GET)
    @ResponseBody
    DeviceModelInfoEntity getByDeviceId(@PathVariable("deviceId") String deviceId) throws ThingsboardException;

}
