import{d as K,c as L,r as D,u as C,b as p,D as G,e2 as R,dW as T,o as M,ey as E,a1 as U,g as k,h as V,F as n,p as s,q as l,i as d,G as y,n as Y,aJ as H,bh as b,cs as F,aB as Q,aw as z,j as S,e0 as W,S as X,f8 as N,bU as O,cK as $,cL as ee,cM as te,bW as se,bV as ae,_ as oe,J as ne,C as le}from"./index-r0dFAfgr.js";import{_ as re}from"./index-C9hz-UZb.js";import{D as ie}from"./DrawerBox-CLde5xC8.js";import{f as de}from"./DateFormatter-Bm9a68Ax.js";import"./SideDrawer-CBntChyn.js";const ce="data:image/png;base64,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",ue={class:"manage"},Ae={class:"cs"},pe={class:"cards"},fe={class:"card"},me={class:"title"},ge={class:"title-text"},he={class:"title-buttom"},ye={class:"el-dropdown-link"},be={class:"introduce"},ve={class:"application-img"},_e={class:"icon"},we=["src"],Ce={class:"application-icon"},ke={class:"type"},Be={class:"update-time"},xe={class:"drawer-content"},Le={class:"content overlay-y"},De={class:"footer"},Ge=K({__name:"applicationManage",setup(Ve){const f=L(),m=L(),c=D({apps:[],menus:[],drawerTitle:"添加"}),v=e=>{const t=e==="2"?[{type:"input",label:"应用链接",field:"applicationUrl"}]:e==="3"?[{type:"select-tree",label:"应用菜单",defaultExpandAll:!0,field:"menuIdList",showCheckbox:!1,options:c.menus}]:[{type:"tree",label:"应用菜单",field:"menuIdList",showCheckbox:!0,options:c.menus}];return[{fields:[{type:"input",label:"应用名称",field:"name",rules:[{required:!0,message:"请输入应用名称"}]},{type:"radio-button",label:"应用来源",field:"resourceType",options:[{label:"APP",value:"APP"},{label:"PC",value:"PC"}]},{type:"textarea",label:"应用介绍",field:"remark"},{type:"input-number",label:"应用序号",field:"orderNum"},{type:"avatar",label:"应用图片",field:"img"},{type:"input",label:"应用图标",field:"icon"},{type:"radio-button",label:"应用类型",field:"type",options:[{label:"基础应用",value:"1"},{label:"直接跳转",value:"3"},{label:"新标签打开",value:"2"}],onChange:a=>{var r;const o=(r=m.value)==null?void 0:r.dataForm;o&&(o.menuIdList=a==="1"?A:a==="3"?A.findLast(()=>!0):[],u.group=v(a))}},...t]}]},u=D({group:v("1"),submit:async e=>{var t,a,o,r,g;if(!((a=(t=C().user)==null?void 0:t.tenantId)!=null&&a.id)){p.error("授权失败");return}console.log(e),u.submitting=!0;try{const h={...e||{},menuIdList:e.menuIdList.length?typeof e.menuIdList=="string"?[e.menuIdList]:e.menuIdList:[],tenantId:e.tenantId||G((r=(o=C().user)==null?void 0:o.tenantId)==null?void 0:r.id)},w=await R(h);console.log(w),(g=f.value)==null||g.toggleDrawer("rtl",!1),p.success("操作成功"),u.submitting=!1,_()}catch{p.error("操作失败")}},defaultValue:{resourceType:"PC"}}),I=()=>{var e;(e=f.value)==null||e.toggleDrawer("rtl",!1)},Z=()=>{var e;(e=m.value)==null||e.Submit()};let A=[];const B=async e=>{var a,o;console.log(e),A=[];try{e&&(A=(await W(e.id)).data||[])}catch{console.log("查询选中列表失败")}c.drawerTitle=e?"编辑":"添加";const t=(e==null?void 0:e.type)||"1";u.defaultValue={type:t,resourceType:"PC",...e||{},menuIdList:t==="1"?A:t==="3"?A.findLast(()=>!0):[]},u.group=v(t),(a=f.value)==null||a.toggleDrawer("rtl",!0),(o=m.value)==null||o.resetForm()},j=e=>{e!=null&&e.id&&X("确认要删除该应用？","删除提示").then(async()=>{try{const t=await N(e.id.split(","));console.log(t),p.success("操作成功"),_()}catch{p.error("操作失败")}}).catch(()=>{})},_=async()=>{var t,a;const e=(a=(t=C().user)==null?void 0:t.tenantId)==null?void 0:a.id;if(e)try{const o=G(e),r=await T(o,"ALL");c.apps=r.data||[]}catch{console.log("查询应用列表失败")}},J=e=>{switch(e){case"1":return"基本类型";case"2":return"新标签打开";case"3":return"直接跳转"}};return M(async()=>{_();try{const e=await E();c.menus=U(e.data||[],{id:"id",value:"id",label:"label",children:"children"})}catch{console.log("查询菜单树失败")}}),(e,t)=>{const a=O,o=$,r=ee,g=te,h=se,w=ae,P=re,q=oe,x=ne;return k(),V(ie,{ref_key:"refDrawer",ref:f,"right-drawer-absolute":!0,"right-drawer-width":600,"right-drawer":!0,"right-drawer-title":d(c).drawerTitle,"right-drawer-bar-hide":!0,"right-drawer-modal":!0},{right:n(()=>[s("div",xe,[s("div",Le,[l(q,{ref_key:"refForm",ref:m,config:d(u)},null,8,["config"])]),s("div",De,[l(x,{type:"default",onClick:I},{default:n(()=>t[7]||(t[7]=[y(" 取 消 ")])),_:1}),l(x,{type:"primary",loading:d(u).submitting,onClick:Z},{default:n(()=>t[8]||(t[8]=[y(" 确 定 ")])),_:1},8,["loading"])])])]),default:n(()=>[l(P,{class:"card-box"},{default:n(()=>[s("div",{class:z(["apps",{isDark:d(S)().isDark}])},[t[6]||(t[6]=s("div",{class:"manage-title"},[s("span",null,"应用管理")],-1)),s("div",ue,[l(w,null,{default:n(()=>[s("div",Ae,[s("div",pe,[l(h,{gutter:30},{default:n(()=>[l(a,{xs:24,sm:12,md:8,lg:8,xl:6},{default:n(()=>[s("div",{class:"cardinput",style:{cursor:"pointer"},onClick:t[0]||(t[0]=()=>B())},t[1]||(t[1]=[s("div",{class:"icon"},[s("img",{src:ce})],-1),s("div",{class:"text"}," 添加应用 ",-1)]))]),_:1}),(k(!0),Y(Q,null,H(d(c).apps,i=>(k(),V(a,{key:i.id,xs:24,sm:12,md:8,lg:8,xl:6},{default:n(()=>[s("div",fe,[s("div",me,[s("span",ge,b(i.name),1),s("div",he,[l(g,{trigger:"click"},{dropdown:n(()=>[l(r,null,{default:n(()=>[l(o,{onClick:()=>B(i)},{default:n(()=>t[2]||(t[2]=[y(" 编辑 ")])),_:2},1032,["onClick"]),l(o,{onClick:()=>j(i)},{default:n(()=>t[3]||(t[3]=[y(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),default:n(()=>[s("span",ye,[l(d(F),{icon:"ep:more"})])]),_:2},1024)])]),s("div",be,[s("span",null,b(i.remark),1)]),s("div",ve,[t[4]||(t[4]=s("span",null,"应用图片：",-1)),s("div",_e,[s("img",{src:i.img},null,8,we)])]),s("div",Ce,[t[5]||(t[5]=s("span",null,"应用图标：",-1)),l(d(F),{style:{width:"25px",height:"25px"},icon:i.icon},null,8,["icon"])]),s("div",ke,[s("span",null,"应用类型："+b(J(i.type)),1)]),s("div",Be,[s("span",null,"创建时间："+b(d(de)(i.createTime)),1)])])]),_:2},1024))),128))]),_:1})])])]),_:1})])],2)]),_:1})]),_:1},8,["right-drawer-title"])}}}),Pe=le(Ge,[["__scopeId","data-v-129c741d"]]);export{Pe as default};
