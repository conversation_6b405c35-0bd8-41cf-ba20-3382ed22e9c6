package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_activity")
public class SsPortalActivity {
    // id
    private String id;

    // 标题
    private String title;

    // 封面
    private String image;

    // 活动内容
    private String content;

    // 链接
    private String link;

    // 是否启用
    private Boolean active;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
