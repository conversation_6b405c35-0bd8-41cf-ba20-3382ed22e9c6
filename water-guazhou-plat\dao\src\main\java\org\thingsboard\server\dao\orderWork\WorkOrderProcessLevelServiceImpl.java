package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderProcessLevel;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderProcessLevelMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class WorkOrderProcessLevelServiceImpl implements WorkOrderProcessLevelService {

    @Autowired
    private WorkOrderProcessLevelMapper workOrderProcessLevelMapper;

    @Override
    public List<WorkOrderProcessLevel> findList(String status, TenantId tenantId) {
        QueryWrapper<WorkOrderProcessLevel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.eq("status", status);
        }
        return workOrderProcessLevelMapper.selectList(queryWrapper);
    }

    @Override
    public void changeStatus(WorkOrderProcessLevel param) {
        WorkOrderProcessLevel processLevel = workOrderProcessLevelMapper.selectById(param.getId());
        if (processLevel != null) {
            processLevel.setStatus(param.getStatus());
            workOrderProcessLevelMapper.updateById(processLevel);
        }
    }

    @Override
    public void save(WorkOrderProcessLevel entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            entity.setStatus("1");
            workOrderProcessLevelMapper.insert(entity);
        } else {
            workOrderProcessLevelMapper.updateById(entity);
        }
    }

    @Override
    public void remove(List<String> ids) {
        workOrderProcessLevelMapper.deleteBatchIds(ids);
    }
}
