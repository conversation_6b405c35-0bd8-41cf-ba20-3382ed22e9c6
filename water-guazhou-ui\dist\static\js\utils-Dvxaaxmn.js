import{cN as lt,h4 as Be,h5 as ke,iK as at,hh as Xt,w as Ce}from"./MapView-DaoQedLH.js";import{e as c,y as m,a as x,W as Ot,ai as ge,h as zt,o as gt,m as ut,s as ye,w as Ht}from"./Point-WxyopZva.js";import{R as S,T as k,a5 as E}from"./index-r0dFAfgr.js";import{m as d,k as Se,s as je,t as xe,v as we,A as Me,w as W,y as De,M as _e,p as ve,z as Ee,B as Oe}from"./dataUtils-DovfQoP5.js";import{u as ze,o as Ge,h as dt,i as Ve,k as be,_ as Jt,m as Le,l as Ue,x as bt,q as We,s as qe,M as Xe}from"./RasterSymbolizer-BF_flzvK.js";import{s as vt}from"./pixelRangeUtils-Dr0gmLDH.js";let yt=class extends Ot{constructor(){super(...arguments),this.raster=void 0}};c([m({json:{write:!0}})],yt.prototype,"raster",void 0),yt=c([x("esri.layers.support.rasterFunctions.AspectFunctionArguments")],yt);const j=yt;var At;let $t=At=class extends j{clone(){return new At({raster:this.raster})}};$t=At=c([x("esri.layers.support.rasterFunctions.AspectFunctionArguments")],$t);const He=$t;let I=class extends Ot{constructor(){super(...arguments),this.functionArguments=null,this.readingBufferSize=0,this.id=-1,this.isNoopProcess=!1,this.rawInputBandIds=[],this.isInputBandIdsSwizzled=!1,this.swizzledBandSelection=[],this.isBranch=!1,this._bindingResult=null}get supportsGPU(){return this._bindingResult.supportsGPU}bind(t,e=!1,s=-1){this.id=s+1;const o=this._getRasterValues();let i=!0;for(let a=0;a<o.length;a++){const r=o[a];if(S(r)&&this._isRasterFunctionValue(r)){const u=r.bind(t,e,this.id+a);if(!u.success)return this._bindingResult=u,u;i=i&&u.supportsGPU}}return!this.rasterInfo||e?(this.sourceRasterInfos=this._getSourceRasterInfos(t),this._bindingResult=this._bindSourceRasters(),this._bindingResult.supportsGPU=i&&this._bindingResult.supportsGPU,this.processInputBandIds(),this._bindingResult):(this._bindingResult={success:!0,supportsGPU:!0},this.processInputBandIds(),this._bindingResult)}process(t){const e=this._getRasterValues(),s=e.length===0?t.pixelBlocks??t.primaryPixelBlocks:e.map(o=>this._readRasterValue(o,t));return this._processPixels({...t,pixelBlocks:s})}processInputBandIds(){const t=this._getRasterValues().filter(this._isRasterFunctionValue);let e;if(t.length>1){const i=t.map(r=>r.processInputBandIds()[0]);this.rawInputBandIds=i,this.isInputBandIdsSwizzled=this.rawInputBandIds.some((r,u)=>r!==u);const a=t.filter(r=>r.functionName==="ExtractBand");return a.length&&a.forEach((r,u)=>{r.isInputBandIdsSwizzled=!0,r.swizzledBandSelection=[u,u,u]}),this.rawInputBandIds}const s=t[0];if(s){if(e=s.processInputBandIds(),s.isInputBandIdsSwizzled)return this.rawInputBandIds=e,e}else{e=[];const{bandCount:i}=this.sourceRasterInfos[0];for(let a=0;a<i;a++)e.push(a)}const o=this._getInputBandIds(e);return this.isInputBandIdsSwizzled=o.some((i,a)=>i!==a),this.rawInputBandIds=o,this.rawInputBandIds}getPrimaryRasters(){const t=[],e=[];return this._getPrimaryRasters(this,t,e),{rasters:t,rasterIds:e}}getWebGLProcessorDefinition(){const t=this._getWebGLParameters(),{raster:e,rasters:s}=this.functionArguments;return s&&Array.isArray(s)&&s.length?(t.rasters=s.map(o=>this._isRasterFunctionValue(o)?o.getWebGLProcessorDefinition():typeof o=="number"?{name:"Constant",parameters:{value:o},pixelType:"f32",id:-1,isNoopProcess:!1}:{name:"Identity",parameters:{value:o},pixelType:"f32",id:-1,isNoopProcess:!1}),t.rasters.some(o=>o!=null)||(t.rasters=null)):this._isRasterFunctionValue(e)&&(t.raster=e.getWebGLProcessorDefinition()),{name:this.functionName,parameters:t,pixelType:this.outputPixelType,id:this.id,isNoopProcess:this.isNoopProcess}}getFlatWebGLFunctionChain(){const t=this.getWebGLProcessorDefinition();if(!t)return null;const e=[t],{parameters:s}=t;let o=s.rasters||s.raster&&[s.raster];for(;o!=null&&o.length;){e.unshift(...o);const a=[];for(let r=0;r<o.length;r++){const{parameters:u}=o[r],l=u.rasters||u.raster&&[u.raster];l!=null&&l.length&&a.push(...l)}o=a}for(let a=e.length-1;a>=0;a--)e[a].isNoopProcess&&e.splice(a,1);let i=!1;for(let a=0;a<e.length;a++){const r=e[a];r.id=e.length-a-1;const{rasters:u}=r.parameters;i=i||u!=null&&u.length>1}return{hasBranches:i,functions:e}}_getOutputPixelType(t){return this.outputPixelType==="unknown"?t:this.outputPixelType??t}_getWebGLParameters(){return{}}_getInputBandIds(t){return t}_isOutputRoundingNeeded(){const{outputPixelType:t}=this;return((t==null?void 0:t.startsWith("u"))||(t==null?void 0:t.startsWith("s")))??!1}_getRasterValues(){const{rasterArgumentNames:t}=this;return t[0]==="rasters"?this.functionArguments.rasters??[]:t.map(e=>this.functionArguments[e])}_getSourceRasterInfos(t){const e=this._getRasterValues(),{rasterInfos:s,rasterIds:o}=t;if(e.length===0)return s;const i=e.map(r=>r&&typeof r=="object"&&"bind"in r&&r.rasterInfo?r.rasterInfo:typeof r=="string"&&o.includes(r)?s[o.indexOf(r)]:typeof r!="number"?s[0]:void 0),a=i.find(r=>r)??s[0];return i.forEach((r,u)=>{r===void 0&&(i[u]=a)}),i}_getPrimaryRasterId(t){return t==null?void 0:t.url}_getPrimaryRasters(t,e=[],s=[]){for(let o=0;o<t.sourceRasters.length;o++){const i=t.sourceRasters[o];if(typeof i!="number")if("bind"in i)this._getPrimaryRasters(i,e,s);else{const a=i,r=this._getPrimaryRasterId(a);if(r==null)continue;s.includes(r)||(this.mainPrimaryRasterId===r?(e.unshift(a),s.unshift(r)):(e.push(a),s.push(r)))}}}_isRasterFunctionValue(t){return t!=null&&typeof t=="object"&&"getWebGLProcessorDefinition"in t}_readRasterValue(t,e){const{primaryPixelBlocks:s}=e;if(k(t)||t==="$$"){const o=s[0];return k(o)?null:o.clone()}if(typeof t=="string"){const o=e.primaryRasterIds.indexOf(t);return o===-1?null:s[o]}if(typeof t=="number"){const o=s[0];if(k(o))return null;const{width:i,height:a,pixelType:r,mask:u}=o,l=u?new Uint8Array(u):null,p=new Float32Array(i*a);p.fill(t);const h=this.sourceRasterInfos[0].bandCount,f=new Array(h).fill(p);return new d({width:i,height:a,pixelType:r,pixels:f,mask:l})}return t.process(e)}};c([m({json:{write:!0}})],I.prototype,"functionName",void 0),c([m({json:{write:!0}})],I.prototype,"functionArguments",void 0),c([m()],I.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}}),ge(n=>n==null?void 0:n.toLowerCase())],I.prototype,"outputPixelType",void 0),c([m({json:{write:!0}})],I.prototype,"mainPrimaryRasterId",void 0),c([m()],I.prototype,"sourceRasters",void 0),c([m({type:[ze],json:{write:!0}})],I.prototype,"sourceRasterInfos",void 0),c([m({json:{write:!0}})],I.prototype,"rasterInfo",void 0),c([m({json:{write:!0}})],I.prototype,"readingBufferSize",void 0),c([m({json:{write:!0}})],I.prototype,"id",void 0),c([m()],I.prototype,"isNoopProcess",void 0),c([m()],I.prototype,"supportsGPU",null),c([m()],I.prototype,"rawInputBandIds",void 0),c([m()],I.prototype,"isInputBandIdsSwizzled",void 0),c([m()],I.prototype,"swizzledBandSelection",void 0),c([m()],I.prototype,"isBranch",void 0),c([m()],I.prototype,"_bindingResult",void 0),I=c([x("esri.layers.support.rasterFunctions.BaseRasterFunction")],I);const M=I;let q=class extends M{constructor(){super(...arguments),this.functionName="Aspect",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isGCS=!1}_bindSourceRasters(){var s;const t=this.sourceRasterInfos[0];this.isGCS=((s=t.spatialReference)==null?void 0:s.isGeographic)??!1,this.outputPixelType=this._getOutputPixelType("f32");const e=t.clone();return e.pixelType=this.outputPixelType,e.statistics=[{min:0,max:360,avg:180,stddev:30}],e.histograms=null,e.colormap=null,e.attributeTable=null,e.bandCount=1,this.rasterInfo=e,{success:!0,supportsGPU:!0}}_processPixels(t){var i;const e=(i=t.pixelBlocks)==null?void 0:i[0];if(k(e))return null;const{extent:s}=t,o=s?{x:s.width/e.width,y:s.height/e.height}:{x:1,y:1};return Ge(e,{resolution:o})}};c([m({json:{write:!0,name:"rasterFunction"}})],q.prototype,"functionName",void 0),c([m({type:He,json:{write:!0,name:"rasterFunctionArguments"}})],q.prototype,"functionArguments",void 0),c([m()],q.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}})],q.prototype,"isGCS",void 0),q=c([x("esri.layers.support.rasterFunctions.AspectFunction")],q);const Je=q,Ye=new Set(["+","-","*","/","(",")"]);function Ke(n,t){(n=n.replace(/ /g,"")).startsWith("-")&&(n="0"+n),n.startsWith("+")&&(n=n.slice(1,n.length));const e=n.split(""),s=[],o=[];let i="";for(let a=0;a<e.length;a++){const r=e[a];if(Ye.has(r))i!==""&&o.push(parseFloat(i)),s.push(r),i="";else{if(r.toLowerCase()==="b"){a++,i=r.concat(e[a]),o.push(t[parseInt(i[1],10)-1]),i="";continue}i=i.concat(r),a===e.length-1&&o.push(parseFloat(i))}}return{ops:s,nums:o}}function Ze(n,t,e,s){if(typeof e=="number"&&typeof s=="number")return e+s;let o;if(typeof e=="number"){o=s.length;const a=e;(e=new Float32Array(o)).fill(a)}else if(o=e.length,s.constructor===Number){const a=s;(s=new Float32Array(o)).fill(a)}const i=new Float32Array(o);switch(t){case"+":for(let a=0;a<o;a++)(n==null||n[a])&&(i[a]=e[a]+s[a]);break;case"-":for(let a=0;a<o;a++)(n==null||n[a])&&(i[a]=e[a]-s[a]);break;case"*":for(let a=0;a<o;a++)(n==null||n[a])&&(i[a]=e[a]*s[a]);break;case"/":for(let a=0;a<o;a++)(n==null||n[a])&&s[a]&&(i[a]=e[a]/s[a]);break;case"(":case")":throw new Error("encountered error with custom band index equation")}return i}function Qe(n,t){n.splice(t,1);let e=0,s=0;do{e=0,s=0;for(let o=0;o<n.length;o++)if(n[o]==="(")e=o;else if(n[o]===")"){s=o;break}s===e+1&&n.splice(e,2)}while(s===e+1);return n}function ts(n){if(n.length===1)return{opIndex:0,numIndex:0};let t=0,e=0;for(let a=0;a<n.length;a++)if(n[a]==="(")t=a;else if(n[a]===")"){e=a;break}const s=e===0?n:n.slice(t+1,e);let o=-1;for(let a=0;a<s.length;a++)if(s[a]==="*"||s[a]==="/"){o=a;break}if(o>-1)e>0&&(o+=t+1);else{for(let a=0;a<s.length;a++)if(s[a]==="+"||s[a]==="-"){o=a;break}e>0&&(o+=t+1)}let i=0;for(let a=0;a<o;a++)n[a]==="("&&i++;return{opIndex:o,numIndex:o-i}}function es(n,t,e){let s,{ops:o,nums:i}=Ke(e,t);if(o.length===0){const a=i.length===1?i[0]:t[0];if(a instanceof Float32Array)return[a];const r=new Float32Array(t[0].length);return typeof a=="number"?r.fill(a):r.set(a),[r]}for(;o.length>0;){const{numIndex:a,opIndex:r}=ts(o);if(s=Ze(n,o[r],i[a],i[a+1]),o.length===1)break;o=Qe(o,r),i.splice(a,2,s)}return[s]}const ss=new zt({0:"custom",1:"ndvi",2:"savi",3:"tsavi",4:"msavi",5:"gemi",6:"pvi",7:"gvitm",8:"sultan",9:"vari",10:"gndvi",11:"sr",12:"ndvi-re",13:"sr-re",14:"mtvi2",15:"rtvi-core",16:"ci-re",17:"ci-g",18:"ndwi",19:"evi",20:"iron-oxide",21:"ferrous-minerals",22:"clay-minerals",23:"wndwi",24:"bai",25:"nbr",26:"ndbi",27:"ndmi",28:"ndsi",29:"mndwi"},{useNumericKeys:!0});function ns(n,t){if(!Se(n))return n;const{equation:e,method:s}=t,o=t.bandIndexes.map(p=>p-1),{pixels:i,mask:a}=n;let r;switch(s){case"gndvi":case"nbr":case"ndbi":case"ndvi":case"ndvi-re":case"ndsi":case"ndmi":case"mndwi":r=Yt(a,i[o[0]],i[o[1]]);break;case"ndwi":r=Yt(a,i[o[1]],i[o[0]]);break;case"sr":case"sr-re":case"iron-oxide":case"ferrous-minerals":case"clay-minerals":r=os(a,i[o[0]],i[o[1]]);break;case"ci-g":case"ci-re":r=is(a,i[o[0]],i[o[1]]);break;case"savi":r=as(a,i[o[0]],i[o[1]],o[2]+1);break;case"tsavi":r=us(a,i[o[0]],i[o[1]],o[2]+1,o[3]+1,o[4]+1);break;case"msavi":r=ls(a,i[o[0]],i[o[1]]);break;case"gemi":r=cs(a,i[o[0]],i[o[1]]);break;case"pvi":r=ps(a,i[o[0]],i[o[1]],o[2]+1,o[3]+1);break;case"gvitm":r=hs(a,[i[o[0]],i[o[1]],i[o[2]],i[o[3]],i[o[4]],i[o[5]]]);break;case"sultan":r=ms(a,[i[o[0]],i[o[1]],i[o[2]],i[o[3]],i[o[4]],i[o[5]]]);break;case"vari":r=fs(a,[i[o[0]],i[o[1]],i[o[2]]]);break;case"mtvi2":r=ds(a,[i[o[0]],i[o[1]],i[o[2]]]);break;case"rtvi-core":r=gs(a,[i[o[0]],i[o[1]],i[o[2]]]);break;case"evi":r=ys(a,[i[o[0]],i[o[1]],i[o[2]]]);break;case"wndwi":r=xs(a,[i[o[0]],i[o[1]],i[o[2]]],o[3]?o[3]+1:.5);break;case"bai":r=ws(a,i[o[0]],i[o[1]]);break;case"custom":r=es(a,i,e);break;default:return n}const u=S(a)?new Uint8Array(a.length):null;S(a)&&S(u)&&u.set(a);const l=new d({width:n.width,height:n.height,pixelType:"f32",pixels:r,mask:u});return l.updateStatistics(),l}function rs(n,t,e,s){const{mask:o,pixels:i,width:a,height:r}=n,u=i[e],l=i[t],p=l.length,h=s?new Uint8Array(p):new Float32Array(p),f=s?100:1,w=s?100.5:0;for(let b=0;b<p;b++)if(o==null||o[b]){const R=u[b],Wt=l[b],qt=R+Wt;qt&&(h[b]=(R-Wt)/qt*f+w)}const v=new d({width:a,height:r,mask:o,pixelType:s?"u8":"f32",pixels:[h]});return v.updateStatistics(),v}function P(n){const t=new Float32Array(9);return t[3*n[0]]=1,t[3*n[1]+1]=1,t[3*n[2]+2]=1,t}function Yt(n,t,e){const s=e.length,o=new Float32Array(s);for(let i=0;i<s;i++)if(n==null||n[i]){const a=t[i],r=e[i],u=a+r;u&&(o[i]=(a-r)/u)}return[o]}function os(n,t,e){const s=e.length,o=new Float32Array(s);for(let i=0;i<s;i++)if(n==null||n[i]){const a=t[i],r=e[i];r&&(o[i]=a/r)}return[o]}function is(n,t,e){const s=t.length,o=new Float32Array(s);for(let i=0;i<s;i++)if(n==null||n[i]){const a=t[i],r=e[i];r&&(o[i]=a/r-1)}return[o]}function as(n,t,e,s){const o=e.length,i=new Float32Array(o);for(let a=0;a<o;a++)if(n==null||n[a]){const r=e[a],u=t[a],l=u+r+s;l&&(i[a]=(u-r)/l*(1+s))}return[i]}function us(n,t,e,s,o,i){const a=e.length,r=new Float32Array(a),u=-o*s+i*(1+s*s);for(let l=0;l<a;l++)if(n==null||n[l]){const p=e[l],h=t[l],f=o*h+p+u;f&&(r[l]=s*(h-s*p-o)/f)}return[r]}function ls(n,t,e){const s=e.length,o=new Float32Array(s);for(let i=0;i<s;i++)if(n==null||n[i]){const a=e[i],r=t[i];o[i]=.5*(2*(r+1)-Math.sqrt((2*r+1)**2-8*(r-a)))}return[o]}function cs(n,t,e){const s=e.length,o=new Float32Array(s);for(let i=0;i<s;i++)if(n==null||n[i]){const a=e[i],r=t[i];if(a!==1){const u=(2*(r*r-a*a)+1.5*r+.5*a)/(r+a+.5);o[i]=u*(1-.25*u)-(a-.125)/(1-a)}}return[o]}function ps(n,t,e,s,o){const i=e.length,a=new Float32Array(i),r=Math.sqrt(1+s*s);for(let u=0;u<i;u++)if(n==null||n[u]){const l=e[u],p=t[u];a[u]=(p-s*l-o)/r}return[a]}function hs(n,t){const[e,s,o,i,a,r]=t,u=e.length,l=new Float32Array(u);for(let p=0;p<u;p++)(n==null||n[p])&&(l[p]=-.2848*e[p]-.2435*s[p]-.5436*o[p]+.7243*i[p]+.084*a[p]-1.18*r[p]);return[l]}function ms(n,t){const[e,,s,o,i,a]=t,r=e.length,u=new Float32Array(r),l=new Float32Array(r),p=new Float32Array(r);for(let h=0;h<r;h++)(n==null||n[h])&&(u[h]=a[h]?i[h]/a[h]*100:0,l[h]=e[h]?i[h]/e[h]*100:0,p[h]=o[h]?s[h]/o[h]*(i[h]/o[h])*100:0);return[u,l,p]}function fs(n,t){const[e,s,o]=t,i=e.length,a=new Float32Array(i);for(let r=0;r<i;r++)if(n==null||n[r])for(r=0;r<i;r++){const u=e[r],l=s[r],p=l+u-o[r];p&&(a[r]=(l-u)/p)}return[a]}function ds(n,t){const[e,s,o]=t,i=e.length,a=new Float32Array(i);for(let r=0;r<i;r++)if(n==null||n[r])for(r=0;r<i;r++){const u=e[r],l=s[r],p=o[r],h=Math.sqrt((2*u+1)**2-6*u-5*Math.sqrt(l)-.5);a[r]=1.5*(1.2*(u-p)-2.5*(l-p))*h}return[a]}function gs(n,t){const[e,s,o]=t,i=e.length,a=new Float32Array(i);for(let r=0;r<i;r++)if(n==null||n[r])for(r=0;r<i;r++){const u=e[r],l=s[r],p=o[r];a[r]=100*(u-l)-10*(u-p)}return[a]}function ys(n,t){const[e,s,o]=t,i=e.length,a=new Float32Array(i);for(let r=0;r<i;r++)if(n==null||n[r])for(r=0;r<i;r++){const u=e[r],l=s[r],p=u+6*l-7.5*o[r]+1;p&&(a[r]=2.5*(u-l)/p)}return[a]}function xs(n,t,e=.5){const[s,o,i]=t,a=o.length,r=new Float32Array(a);for(let u=0;u<a;u++)if(n==null||n[u])for(u=0;u<a;u++){const l=s[u],p=o[u],h=i[u],f=l+e*p+(1-e)*h;f&&(r[u]=(l-e*p-(1-e)*h)/f)}return[r]}function ws(n,t,e){const s=e.length,o=new Float32Array(s);for(let i=0;i<s;i++)if(n==null||n[i])for(i=0;i<s;i++){const a=(.1-t[i])**2+(.06-e[i])**2;a&&(o[i]=1/a)}return[o]}var It;let ct=It=class extends j{constructor(){super(...arguments),this.method="custom"}clone(){return new It({method:this.method,bandIndexes:this.bandIndexes,raster:E(this.raster)})}};c([m({json:{type:String,write:!0}})],ct.prototype,"bandIndexes",void 0),c([lt(ss)],ct.prototype,"method",void 0),ct=It=c([x("esri.layers.support.rasterFunctions.BandArithmeticFunctionArguments")],ct);const vs=ct,bs=new Set(["vari","mtvi2","rtvi-core","evi"]);let tt=class extends M{constructor(){super(...arguments),this.functionName="BandArithmetic",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){this.outputPixelType=this._getOutputPixelType("f32");const t=this.sourceRasterInfos[0],e=t.clone();return e.pixelType=this.outputPixelType,e.statistics=null,e.histograms=null,e.bandCount=this.functionArguments.method==="sultan"?t.bandCount:1,this.rasterInfo=e,{success:!0,supportsGPU:!["custom","gvitm","sultan"].includes(this.functionArguments.method)}}_processPixels(t){var a;const e=(a=t.pixelBlocks)==null?void 0:a[0];if(k(e))return e;const{method:s,bandIndexes:o}=this.functionArguments,i=o.split(" ").map(r=>parseFloat(r));return ns(e,{method:s,bandIndexes:i,equation:o})}_getWebGLParameters(){const t=this.functionArguments.bandIndexes.split(" ").map(r=>parseFloat(r)-1);t.length===2&&t.push(0);const e=this.isInputBandIdsSwizzled?[0,1,2]:t;let s,o;const i=new Float32Array(3),{method:a}=this.functionArguments;switch(a){case"gndvi":case"nbr":case"ndbi":case"ndvi":case"ndvi-re":case"ndsi":case"ndmi":case"mndwi":s=P([e[0],e[1],0]),o="ndxi";break;case"ndwi":s=P([e[1],e[0],0]),o="ndxi";break;case"sr":case"sr-re":case"iron-oxide":case"ferrous-minerals":case"clay-minerals":s=P([e[0],e[1],0]),o="sr";break;case"ci-g":case"ci-re":s=P([e[0],e[1],0]),o="ci";break;case"savi":s=P([e[0],e[1],0]),o="savi",i[0]=t[2]+1;break;case"tsavi":s=P([e[0],e[1],0]),o="tsavi",i[0]=t[2]+1,i[1]=t[3]+1,i[2]=t[4]+1;break;case"msavi":s=P([e[0],e[1],0]),o="msavi";break;case"gemi":s=P([e[0],e[1],0]),o="gemi";break;case"pvi":s=P([e[0],e[1],0]),o="tsavi",i[0]=t[2]+1,i[1]=t[3]+1;break;case"vari":s=P([e[0],e[1],e[2]]),o="vari";break;case"mtvi2":s=P([e[0],e[1],e[2]]),o="mtvi2";break;case"rtvi-core":s=P([e[0],e[1],e[2]]),o="rtvicore";break;case"evi":s=P([e[0],e[1],e[2]]),o="evi";break;case"wndwi":s=P([e[0],e[1],0]),o="wndwi",i[0]=t[3]?t[3]+1:.5;break;case"bai":s=P([e[1],e[0],0]),o="bai";break;default:s=P([0,1,2]),o="custom"}return{bandIndexMat3:s,indexType:o,adjustments:i}}_getInputBandIds(t){if(this.functionArguments.method==="custom")return t;const e=this.functionArguments.bandIndexes.split(" ").map(r=>parseFloat(r)-1),s=t.length,o=e.map(r=>r>=s?s-1:r),i=bs.has(this.functionArguments.method)?3:2,a=o.slice(0,i).map(r=>t[r]);return a.length===2&&a.push(0),a}};c([m({json:{write:!0,name:"rasterFunction"}})],tt.prototype,"functionName",void 0),c([m({type:vs,json:{write:!0,name:"rasterFunctionArguments"}})],tt.prototype,"functionArguments",void 0),c([m()],tt.prototype,"rasterArgumentNames",void 0),tt=c([x("esri.layers.support.rasterFunctions.BandArithmeticFunction")],tt);const As=tt;var Ft;let O=Ft=class extends j{castColormapName(n){if(!n)return null;const t=n.toLowerCase();return Ve.includes(t)?t:null}readColorRamp(n){return ke(n)}readColorRampName(n,t){if(!n)return null;const e=dt.jsonValues.find(s=>s.toLowerCase()===n.toLowerCase());return e?dt.fromJSON(e):null}clone(){var n;return new Ft({colormap:E(this.colormap),colormapName:this.colormapName,colorRamp:(n=this.colorRamp)==null?void 0:n.clone(),colorRampName:this.colorRampName})}};c([m({type:[[Number]],json:{write:!0}})],O.prototype,"colormap",void 0),c([m({type:String,json:{write:!0}})],O.prototype,"colormapName",void 0),c([ge("colormapName")],O.prototype,"castColormapName",null),c([m({types:Be,json:{write:!0}})],O.prototype,"colorRamp",void 0),c([gt("colorRamp")],O.prototype,"readColorRamp",null),c([m({type:dt.apiValues,json:{type:dt.jsonValues,write:dt.write}})],O.prototype,"colorRampName",void 0),c([gt("colorRampName")],O.prototype,"readColorRampName",null),O=Ft=c([x("esri.layers.support.rasterFunctions.ColormapFunctionArguments")],O);const $s=O,Is=[[36,0,255],[36,0,255],[36,0,255],[36,0,255],[112,75,3],[113,76,3],[114,77,3],[115,77,3],[116,78,3],[117,79,3],[118,79,3],[119,80,3],[121,81,4],[122,82,4],[123,82,4],[124,83,4],[125,84,4],[126,84,4],[127,85,4],[128,86,4],[129,86,4],[130,87,4],[131,88,4],[132,89,4],[133,89,4],[134,90,4],[135,91,4],[136,91,4],[137,92,4],[138,93,4],[139,94,4],[140,94,4],[142,95,5],[143,96,5],[144,96,5],[145,97,5],[146,98,5],[147,99,5],[148,99,5],[149,100,5],[150,101,5],[151,101,5],[152,102,5],[153,103,5],[154,104,5],[155,104,5],[156,105,5],[157,106,5],[158,106,5],[159,107,5],[160,108,5],[161,108,5],[162,109,5],[164,110,6],[165,111,6],[166,111,6],[167,112,6],[168,113,6],[169,113,6],[170,114,6],[171,115,6],[172,116,6],[173,116,6],[174,117,6],[245,0,0],[245,5,0],[245,10,0],[246,15,0],[246,20,0],[246,25,0],[246,30,0],[247,35,0],[247,40,0],[247,45,0],[247,50,0],[247,55,0],[248,60,0],[248,65,0],[248,70,0],[248,75,0],[249,81,0],[249,86,0],[249,91,0],[249,96,0],[250,101,0],[250,106,0],[250,111,0],[250,116,0],[250,121,0],[251,126,0],[251,131,0],[251,136,0],[251,141,0],[252,146,0],[252,151,0],[252,156,0],[252,156,0],[251,159,0],[250,162,0],[249,165,0],[248,168,0],[247,171,0],[246,174,0],[245,177,0],[245,179,0],[244,182,0],[243,185,0],[242,188,0],[241,191,0],[240,194,0],[239,197,0],[238,200,0],[237,203,0],[236,206,0],[235,209,0],[234,212,0],[233,215,0],[232,218,0],[231,221,0],[230,224,0],[230,226,0],[229,229,0],[228,232,0],[227,235,0],[226,238,0],[225,241,0],[224,244,0],[223,247,0],[165,247,0],[163,244,0],[161,240,0],[158,237,0],[156,233,1],[154,230,1],[152,227,1],[149,223,1],[147,220,1],[145,216,1],[143,213,1],[140,210,2],[138,206,2],[136,203,2],[134,200,2],[132,196,2],[129,193,2],[127,189,2],[125,186,3],[123,183,3],[120,179,3],[118,176,3],[116,172,3],[114,169,3],[111,166,3],[109,162,4],[107,159,4],[105,155,4],[103,152,4],[100,149,4],[98,145,4],[96,142,4],[94,138,5],[91,135,5],[89,132,5],[87,128,5],[85,125,5],[82,121,5],[80,118,5],[78,115,6],[76,111,6],[73,108,6],[71,105,6],[69,101,6],[67,98,6],[65,94,6],[62,91,7],[60,88,7],[58,84,7],[56,81,7],[53,77,7],[51,74,7],[49,71,7],[47,67,8],[44,64,8],[42,60,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8]],Fs=[[36,0,255],[36,0,255],[36,0,255],[36,0,255],[245,20,0],[245,24,0],[245,29,0],[245,31,0],[247,33,0],[247,33,0],[247,37,0],[247,41,0],[247,41,0],[247,41,0],[247,45,0],[247,45,0],[247,47,0],[247,49,0],[247,49,0],[247,54,0],[247,54,0],[247,56,0],[247,58,0],[247,58,0],[250,62,0],[250,62,0],[250,62,0],[250,67,0],[250,67,0],[250,67,0],[250,69,0],[250,71,0],[250,71,0],[250,75,0],[250,75,0],[250,78,0],[250,79,0],[250,79,0],[250,79,0],[250,81,0],[250,83,0],[250,83,0],[250,87,0],[250,87,0],[250,90,0],[250,92,0],[252,93,0],[252,93,0],[252,97,0],[252,97,0],[252,97,0],[252,97,0],[252,101,0],[252,101,0],[252,101,0],[252,101,0],[252,105,0],[252,105,0],[252,107,0],[252,109,0],[252,109,0],[252,113,13],[255,118,20],[255,119,23],[255,121,25],[255,126,33],[255,132,38],[255,133,40],[255,135,43],[255,141,48],[255,144,54],[255,150,59],[255,152,61],[255,153,64],[255,159,69],[255,163,77],[255,165,79],[255,168,82],[255,174,87],[255,176,92],[255,181,97],[255,183,99],[255,186,102],[255,191,107],[255,197,115],[255,201,120],[255,203,123],[255,205,125],[255,209,130],[255,214,138],[255,216,141],[255,218,143],[255,224,150],[255,228,156],[255,234,163],[255,236,165],[255,238,168],[255,243,173],[255,248,181],[255,252,186],[253,252,186],[250,252,187],[244,250,180],[238,247,176],[234,246,173],[231,245,169],[223,240,163],[217,237,157],[211,235,150],[205,233,146],[200,230,142],[195,227,136],[189,224,132],[184,222,126],[180,220,123],[174,217,119],[169,214,114],[163,212,108],[160,210,105],[154,207,101],[148,204,96],[143,201,93],[138,199,88],[134,197,84],[130,194,81],[126,191,77],[117,189,70],[115,186,68],[112,184,64],[106,181,60],[100,179,55],[94,176,49],[92,174,47],[90,173,45],[81,168,37],[75,166,33],[71,163,28],[66,160,24],[62,158,21],[56,156,14],[51,153,0],[51,153,0],[51,153,0],[50,150,0],[50,150,0],[50,150,0],[50,150,0],[49,148,0],[49,148,0],[49,148,0],[48,145,0],[48,145,0],[48,145,0],[48,145,0],[48,143,0],[48,143,0],[48,143,0],[48,143,0],[47,140,0],[47,140,0],[47,140,0],[47,140,0],[46,138,0],[46,138,0],[46,138,0],[46,138,0],[45,135,0],[45,135,0],[45,135,0],[45,135,0],[44,133,0],[44,133,0],[44,133,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[42,128,0],[42,128,0],[42,128,0],[42,125,0],[42,125,0],[42,125,0],[42,125,0],[41,122,0],[41,122,0],[41,122,0],[41,122,0],[40,120,0],[40,120,0],[40,120,0],[40,120,0],[40,120,0],[39,117,0],[39,117,0],[39,117,0],[39,117,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0]];function Gt(n,t){const e=[],s=[];for(let i=0;i<n.length-1;i++)e.push({type:"algorithmic",algorithm:"esriHSVAlgorithm",fromColor:n[i].slice(1),toColor:n[i+1].slice(1)}),s.push(n[i+1][0]-n[i][0]);const o=n[n.length-1][0];return be({type:"multipart",colorRamps:e},{numColors:o,weights:t=t??s})}function Ps(){return Gt([[0,0,191,191],[51,0,255,0],[102,255,255,0],[153,255,127,0],[204,191,127,63],[256,20,20,20]])}function Ns(){const n=Gt([[0,255,255,255],[70,0,0,255],[80,205,193,173],[100,150,150,150],[110,120,100,51],[130,120,200,100],[140,28,144,3],[160,6,55,0],[180,10,30,25],[201,6,27,7]]);for(let t=n.length;t<256;t++)n.push([6,27,7]);return n}function Rs(){return be({type:"algorithmic",algorithm:"esriHSVAlgorithm",fromColor:[0,0,0],toColor:[255,255,255]})}function Ts(){const n=[];for(let t=0;t<256;t++){const e=[];for(let s=0;s<3;s++)e.push(Math.round(255*Math.random()));n.push(e)}return n}function Bs(){return Gt([[0,38,54,41],[69,79,90,82],[131,156,156,156],[256,253,241,253]],[.268,.238,.495])}function ks(n){let t;switch(n){case"elevation":t=Ps();break;case"gray":t=Rs();break;case"hillshade":t=Bs();break;case"ndvi":t=Is;break;case"ndvi2":t=Ns();break;case"ndvi3":t=Fs;break;case"random":t=Ts()}return t?(t=t.map((e,s)=>[s,...e]),t):null}let z=class extends M{constructor(){super(...arguments),this.functionName="Colormap",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isNoopProcess=!0}_bindSourceRasters(){const t=this.sourceRasterInfos[0];if(t.bandCount>1)return{success:!1,supportsGPU:!1,error:"colormap-function: source data must be single band"};let{colormap:e,colormapName:s,colorRamp:o,colorRampName:i}=this.functionArguments;if(!(e!=null&&e.length))if(o)e=Jt(o,{interpolateAlpha:!0});else if(i){const r=Le(i);r&&(e=Jt(r))}else s&&(e=ks(s));if(!(e!=null&&e.length))return{success:!1,supportsGPU:!1,error:"colormap-function: missing colormap argument"};this.outputPixelType=this._getOutputPixelType("u8");const a=t.clone();return a.pixelType=this.outputPixelType,a.colormap=e,a.bandCount=1,this.rasterInfo=a,{success:!0,supportsGPU:!0}}_processPixels(t){var e;return(e=t.pixelBlocks)==null?void 0:e[0]}};c([m({json:{write:!0,name:"rasterFunction"}})],z.prototype,"functionName",void 0),c([m({type:$s,json:{write:!0,name:"rasterFunctionArguments"}})],z.prototype,"functionArguments",void 0),c([m()],z.prototype,"rasterArgumentNames",void 0),c([m()],z.prototype,"isNoopProcess",void 0),c([m({json:{write:!0}})],z.prototype,"indexedColormap",void 0),z=c([x("esri.layers.support.rasterFunctions.ColormapFunction")],z);const Cs=z;var Pt;let pt=Pt=class extends j{constructor(){super(...arguments),this.rasters=[]}writeRasters(n,t){t.rasters=n.map(e=>typeof e=="number"||typeof e=="string"?e:e.toJSON())}clone(){return new Pt({rasters:E(this.rasters)})}};c([m({json:{write:!0}})],pt.prototype,"rasters",void 0),c([ut("rasters")],pt.prototype,"writeRasters",null),pt=Pt=c([x("esri.layers.support.rasterFunctions.CompositeBandFunctionArguments")],pt);const Ss=pt;let et=class extends M{constructor(){super(...arguments),this.functionName="CompositeBand",this.functionArguments=null,this.rasterArgumentNames=["rasters"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0];this.outputPixelType=this._getOutputPixelType(e.pixelType);const s=e.clone();if(s.attributeTable=null,s.colormap=null,s.pixelType=this.outputPixelType,s.bandCount=t.map(({bandCount:o})=>o).reduce((o,i)=>o+i),t.every(({statistics:o})=>S(o)&&o.length)){const o=[];t.forEach(({statistics:i})=>S(i)&&o.push(...i)),s.statistics=o}if(t.every(({histograms:o})=>S(o)&&o.length)){const o=[];t.forEach(({histograms:i})=>S(i)&&o.push(...i)),s.histograms=o}return s.bandCount>1&&(s.colormap=null,s.attributeTable=null),this.rasterInfo=s,{success:!0,supportsGPU:s.bandCount<=3}}_processPixels(t){const{pixelBlocks:e}=t;if(!e)return null;const s=e==null?void 0:e[0];return k(s)?null:je(e)}_getWebGLParameters(){return{bandCount:this.rasterInfo.bandCount}}};c([m({json:{write:!0,name:"rasterFunction"}})],et.prototype,"functionName",void 0),c([m({type:Ss,json:{write:!0,name:"rasterFunctionArguments"}})],et.prototype,"functionArguments",void 0),c([m()],et.prototype,"rasterArgumentNames",void 0),et=c([x("esri.layers.support.rasterFunctions.CompositeBandFunction")],et);const js=et,y={userDefined:-1,lineDetectionHorizontal:0,lineDetectionVertical:1,lineDetectionLeftDiagonal:2,lineDetectionRightDiagonal:3,gradientNorth:4,gradientWest:5,gradientEast:6,gradientSouth:7,gradientNorthEast:8,gradientNorthWest:9,smoothArithmeticMean:10,smoothing3x3:11,smoothing5x5:12,sharpening3x3:13,sharpening5x5:14,laplacian3x3:15,laplacian5x5:16,sobelHorizontal:17,sobelVertical:18,sharpen:19,sharpen2:20,pointSpread:21,none:255},Ms={plus:1,minus:2,times:3,sqrt:4,power:5,abs:10,divide:23,exp:25,exp10:26,exp2:27,int:30,float:32,ln:35,log10:36,log2:37,mod:44,negate:45,roundDown:48,roundUp:49,square:53,floatDivide:64,floorDivide:65},C={bitwiseAnd:11,bitwiseLeftShift:12,bitwiseNot:13,bitwiseOr:14,bitwiseRightShift:15,bitwiseXOr:16,booleanAnd:17,booleanNot:18,booleanOr:19,booleanXOr:20,equalTo:24,greaterThan:28,greaterThanEqual:29,lessThan:33,lessThanEqual:34,isNull:31,notEqual:46},_={acos:6,asin:7,atan:8,atanh:9,cos:21,cosh:22,sin:51,sinh:52,tan:56,tanh:57,acosh:59,asinh:60,atan2:61},Ds={majority:38,max:39,mean:40,med:41,min:42,minority:43,range:47,stddev:54,sum:55,variety:58,majorityIgnoreNoData:66,maxIgnoreNoData:67,meanIgnoreNoData:68,medIgnoreNoData:69,minIgnoreNoData:70,minorityIgnoreNoData:71,rangeIgnoreNoData:72,stddevIgnoreNoData:73,sumIgnoreNoData:74,varietyIgnoreNoData:75},Ae={setNull:50,conditional:78},Kt={...Ms,...C,..._,...Ds,...Ae},A=new Map;function _s(n){const t=Math.sqrt(n.length),e=n.slice(0,t),s=[1];for(let o=1;o<t;o++){let i=null;for(let a=0;a<t;a++){const r=n[a+o*t],u=n[a];if(i==null)if(u===0){if(r)return{separable:!1,row:null,col:null}}else i=r/u;else if(r/u!==i)return{separable:!1,row:null,col:null}}if(i==null)return{separable:!1,row:null,col:null};s.push(i)}return{separable:!0,row:e,col:s}}function Zt(n,t,e,s,o,i,a){const r=new Float32Array(t*e),u=i.length,l=a?0:s,p=a?s:0,h=a?1:t;for(let f=l;f<e-l;f++){const w=f*t;for(let v=p;v<t-p;v++){if(o&&!o[w+v])continue;let b=0;for(let R=0;R<u;R++)b+=n[w+v+(R-s)*h]*i[R];r[w+v]=b}}return r}function Es(n,t,e,s,o,i,a){const r=new Float32Array(t*e),u=Math.floor(s/2),l=Math.floor(o/2);for(let p=u;p<e-u;p++){const h=p*t;for(let f=l;f<t-l;f++){if(i&&!i[h+f])continue;let w=0;for(let v=0;v<s;v++)for(let b=0;b<o;b++)w+=n[h+f+(v-u)*t+b-l]*a[v*o+b];r[h+f]=w}}return r}function Os(n,t,e=!0){const{pixels:s,width:o,height:i,pixelType:a,mask:r}=n,u=s.length,l=[],{kernel:p,rows:h,cols:f}=t;for(let w=0;w<u;w++){const v=Es(s[w],o,i,h,f,r,p);e&&$e(v,o,i,h,f),l.push(v)}return new d({width:o,height:i,pixelType:a,pixels:l,mask:r})}function $e(n,t,e,s,o){const i=Math.floor(s/2);for(let r=0;r<i;r++)for(let u=0;u<t;u++)n[r*t+u]=n[(o-1-r)*t+u],n[(e-1-r)*t+u]=n[(e-o+r)*t+u];const a=Math.floor(o/2);for(let r=0;r<e;r++){const u=r*t;for(let l=0;l<a;l++)n[u+l]=n[u+o-1-l],n[u+t-l-1]=n[u+t+l-o]}}function zs(n,t,e,s=!0){const{pixels:o,width:i,height:a,pixelType:r,mask:u}=n,l=o.length,p=[],h=t.length,f=e.length,w=Math.floor(h/2),v=Math.floor(f/2);for(let b=0;b<l;b++){let R=Zt(o[b],i,a,w,u,t,!0);R=Zt(R,i,a,v,u,e,!1),s&&$e(R,i,a,h,f),p.push(R)}return new d({width:i,height:a,pixelType:r,pixels:p,mask:u})}function Gs(n,t){const e=_s(t.kernel),s=t.mirrorEdges!==!1,o=e.separable?zs(n,e.row,e.col,s):Os(n,t,s),{outputPixelType:i}=t;return i&&o.clamp(i),o}A.set(y.none,[0,0,0,0,1,0,0,0,0]),A.set(y.lineDetectionHorizontal,[-1,-1,-1,2,2,2,-1,-1,-1]),A.set(y.lineDetectionVertical,[-1,2,-1,-1,2,-1,-1,2,-1]),A.set(y.lineDetectionLeftDiagonal,[2,-1,-1,-1,2,-1,-1,-1,2]),A.set(y.lineDetectionRightDiagonal,[-1,-1,2,-1,2,-1,2,-1,-1]),A.set(y.gradientNorth,[-1,-2,-1,0,0,0,1,2,1]),A.set(y.gradientWest,[-1,0,1,-2,0,2,-1,0,1]),A.set(y.gradientEast,[1,0,-1,2,0,-2,1,0,-1]),A.set(y.gradientSouth,[1,2,1,0,0,0,-1,-2,-1]),A.set(y.gradientNorthEast,[0,-1,-2,1,0,-1,2,1,0]),A.set(y.gradientNorthWest,[-2,-1,0,-1,0,1,0,1,2]),A.set(y.smoothArithmeticMean,[.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111]),A.set(y.smoothing3x3,[.0625,.125,.0625,.125,.25,.125,.0625,.125,.0625]),A.set(y.smoothing5x5,[1,1,1,1,1,1,4,4,4,1,1,4,12,4,1,1,4,4,4,1,1,1,1,1,1]),A.set(y.sharpening3x3,[-1,-1,-1,-1,9,-1,-1,-1,-1]),A.set(y.sharpening5x5,[-1,-3,-4,-3,-1,-3,0,6,0,-3,-4,6,21,6,-4,-3,0,6,0,-3,-1,-3,-4,-3,-1]),A.set(y.laplacian3x3,[0,-1,0,-1,4,-1,0,-1,0]),A.set(y.laplacian5x5,[0,0,-1,0,0,0,-1,-2,-1,0,-1,-2,17,-2,-1,0,-1,-2,-1,0,0,0,-1,0,0]),A.set(y.sobelHorizontal,[-1,-2,-1,0,0,0,1,2,1]),A.set(y.sobelVertical,[-1,0,1,-2,0,2,-1,0,1]),A.set(y.sharpen,[0,-.25,0,-.25,2,-.25,0,-.25,0]),A.set(y.sharpen2,[-.25,-.25,-.25,-.25,3,-.25,-.25,-.25,-.25]),A.set(y.pointSpread,[-.627,.352,-.627,.352,2.923,.352,-.627,.352,-.627]);var Nt;let X=Nt=class extends j{constructor(){super(...arguments),this.rows=3,this.cols=3,this.kernel=[0,0,0,0,1,0,0,0,0]}set convolutionType(n){this._set("convolutionType",n);const t=A.get(n);if(!t||n===y.userDefined||n===y.none)return;const e=Math.sqrt(t.length);this._set("kernel",t),this._set("cols",e),this._set("rows",e)}clone(){return new Nt({cols:this.cols,rows:this.rows,kernel:[...this.kernel],convolutionType:this.convolutionType,raster:E(this.raster)})}};c([m({json:{type:Number,write:!0}})],X.prototype,"rows",void 0),c([m({json:{type:Number,write:!0}})],X.prototype,"cols",void 0),c([m({json:{name:"type",type:Number,write:!0}})],X.prototype,"convolutionType",null),c([m({json:{type:[Number],write:!0}})],X.prototype,"kernel",void 0),X=Nt=c([x("esri.layers.support.rasterFunctions.ConvolutionFunctionArguments")],X);const Vs=X,Qt=25;let st=class extends M{constructor(){super(...arguments),this.functionName="Convolution",this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const{convolutionType:t,rows:e,cols:s,kernel:o}=this.functionArguments;if(!Object.values(y).includes(t))return{success:!1,supportsGPU:!1,error:`convolution-function: the specified kernel type is not supported ${t}`};if(t!==y.none&&e*s!==o.length)return{success:!1,supportsGPU:!1,error:"convolution-function: the specified rows and cols do not match the length of the kernel"};const i=this.sourceRasterInfos[0];this.outputPixelType=this._getOutputPixelType(i.pixelType);const a=i.clone();a.pixelType=this.outputPixelType;const r=[y.none,y.sharpen,y.sharpen2,y.sharpening3x3,y.sharpening5x5];return this.outputPixelType==="u8"||r.includes(t)||(a.statistics=null,a.histograms=null),a.colormap=null,a.attributeTable=null,this.rasterInfo=a,{success:!0,supportsGPU:o.length<=Qt}}_processPixels(t){var r;const e=(r=t.pixelBlocks)==null?void 0:r[0];if(k(e)||this.functionArguments.convolutionType===y.none)return e;let{kernel:s,rows:o,cols:i}=this.functionArguments;const a=s.reduce((u,l)=>u+l);return a!==0&&a!==1&&(s=s.map(u=>u/a)),Gs(e,{kernel:s,rows:o,cols:i,outputPixelType:this.outputPixelType})}_getWebGLParameters(){let{kernel:t}=this.functionArguments;const e=t.reduce((o,i)=>o+i);e!==0&&e!==1&&(t=t.map(o=>o/e));const s=new Float32Array(Qt);return s.set(t),{kernelRows:this.functionArguments.rows,kernelCols:this.functionArguments.cols,kernel:s,clampRange:vt(this.outputPixelType)}}};c([m({json:{write:!0,name:"rasterFunction"}})],st.prototype,"functionName",void 0),c([m({type:Vs,json:{write:!0,name:"rasterFunctionArguments"}})],st.prototype,"functionArguments",void 0),c([m()],st.prototype,"rasterArgumentNames",void 0),st=c([x("esri.layers.support.rasterFunctions.ConvolutionFunction")],st);const Ls=st;var Rt;let ht=Rt=class extends j{constructor(){super(...arguments),this.bandIds=[],this.missingBandAction=xe.bestMatch}clone(){return new Rt({bandIds:[...this.bandIds],missingBandAction:this.missingBandAction})}};c([m({json:{write:!0}})],ht.prototype,"bandIds",void 0),c([m({json:{write:!0}})],ht.prototype,"missingBandAction",void 0),ht=Rt=c([x("esri.layers.support.rasterFunctions.ExtractBandFunctionArguments")],ht);const Us=ht;let nt=class extends M{constructor(){super(...arguments),this.functionName="ExtractBand",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0],{bandCount:s}=e,{bandIds:o,missingBandAction:i}=this.functionArguments;if(i===xe.fail&&o.some(l=>l<0||l>=s))return{success:!1,supportsGPU:!1,error:"extract-band-function: invalid bandIds"};this.outputPixelType=this._getOutputPixelType("f32");const a=e.clone();a.pixelType=this.outputPixelType,a.bandCount=o.length;const{statistics:r,histograms:u}=a;return S(r)&&r.length&&(a.statistics=o.map(l=>r[l]||r[r.length-1])),S(u)&&u.length&&(a.histograms=o.map(l=>u[l]||u[u.length-1])),this.rasterInfo=a,{success:!0,supportsGPU:a.bandCount<=3}}_processPixels(t){var i;const e=(i=t.pixelBlocks)==null?void 0:i[0];if(k(e))return null;const s=e.pixels.length,o=this.functionArguments.bandIds.map(a=>a>=s?s-1:a);return e.extractBands(o)}_getWebGLParameters(){let t;if(this.isInputBandIdsSwizzled)t=this.swizzledBandSelection.length?this.swizzledBandSelection:[0,1,2];else{t=[...this.functionArguments.bandIds],t.length===0?t=[0,1,2]:t.length<3&&(t[1]=t[1]??t[0],t[2]=t[2]??t[1]);for(let e=0;e<3;e++)t[e]=Math.min(t[e],2)}return{bandIndexMat3:P(t)}}_getInputBandIds(t){const e=t.length;return this.functionArguments.bandIds.map(s=>s>=e?e-1:s).map(s=>t[s])}};c([m({json:{write:!0,name:"rasterFunction"}})],nt.prototype,"functionName",void 0),c([m({type:Us,json:{write:!0,name:"rasterFunctionArguments"}})],nt.prototype,"functionArguments",void 0),c([m()],nt.prototype,"rasterArgumentNames",void 0),nt=c([x("esri.layers.support.rasterFunctions.ExtractBandFunction")],nt);const Ws=nt;var Tt;let H=Tt=class extends j{constructor(){super(...arguments),this.rasters=[],this.processAsMultiband=!0}writeRasters(n,t){t.rasters=n.map(e=>typeof e=="number"||typeof e=="string"?e:e.toJSON())}clone(){return new Tt({operation:this.operation,processAsMultiband:this.processAsMultiband,rasters:E(this.rasters)})}};c([m({json:{write:!0}})],H.prototype,"operation",void 0),c([m({json:{write:!0}})],H.prototype,"rasters",void 0),c([ut("rasters")],H.prototype,"writeRasters",null),c([m({json:{write:!0}})],H.prototype,"processAsMultiband",void 0),H=Tt=c([x("esri.layers.support.rasterFunctions.LocalFunctionArguments")],H);const qs=H,N=new Map;function Xs(n){return N.get(n)}N.set(_.acos,[0,Math.PI]),N.set(_.asin,[-Math.PI/2,Math.PI/2]),N.set(_.atan,[-Math.PI/2,Math.PI/2]),N.set(_.cos,[-1,1]),N.set(_.sin,[-1,1]),N.set(C.booleanAnd,[0,1]),N.set(C.booleanNot,[0,1]),N.set(C.booleanOr,[0,1]),N.set(C.booleanXOr,[0,1]),N.set(C.equalTo,[0,1]),N.set(C.notEqual,[0,1]),N.set(C.greaterThan,[0,1]),N.set(C.greaterThanEqual,[0,1]),N.set(C.lessThan,[0,1]),N.set(C.lessThanEqual,[0,1]),N.set(C.isNull,[0,1]);const te=[0,2,2,2,1,2,1,1,1,1,1,2,2,1,2,2,2,2,1,2,2,1,1,2,2,1,1,1,2,2,1,1,1,2,2,1,1,1,999,999,999,999,999,999,2,1,2,999,1,1,2,1,1,1,999,999,1,1,999,1,1,2,999,999,2,2,999,999,999,999,999,999,999,999,999,999,3,999,3];function Hs(n,t=!1){const e=n.map(r=>r.mask),s=e.filter(r=>S(r)),o=n[0].pixels[0].length;if(s.length===0)return new Uint8Array(o).fill(255);const i=s[0],a=new Uint8Array(i);if(s.length===1)return a;if(!t){for(let r=1;r<s.length;r++){const u=s[r];for(let l=0;l<a.length;l++)a[l]&&(a[l]=u[l]?255:0)}return a}if(s.length!==e.length)return new Uint8Array(o).fill(255);for(let r=1;r<s.length;r++){const u=s[r];for(let l=0;l<a.length;l++)a[l]===0&&(a[l]=u[l]?255:0)}return a}function Js(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]+o[r]);return a}function Ys(n,t,e){const[s]=n,o=s.length,i=d.createEmptyBand("f32",o);return i.set(s),i}function Ks(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]-o[r]);return a}function Zs(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]*o[r]);return a}function Qs(n,t,e){const[s]=n,o=s.length,i=d.createEmptyBand(e,o);for(let a=0;a<o;a++)t&&!t[a]||(i[a]=Math.sign(s[a])*Math.floor(Math.abs(s[a])));return i}function Ie(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]/o[r]);return a}function tn(n,t,e){return Ie(n,t,"f32")}function en(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=Math.floor(s[r]/o[r]));return a}function sn(n,t,e,s){const o=n[0],i=o.length,a=d.createEmptyBand(e,i);if(s===_.atanh){for(let u=0;u<i;u++)if(t[u]){const l=o[u];Math.abs(l)>=1?t[u]=0:a[u]=Math.atanh(l)}return a}const r=s===_.asin?Math.asin:Math.acos;for(let u=0;u<i;u++)if(t[u]){const l=o[u];Math.abs(l)>1?t[u]=0:a[u]=r(l)}return a}function nn(n,t,e,s){const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s(o[r]));return a}function rn(n,t,e,s){const[o,i]=n,a=o.length,r=d.createEmptyBand(e,a);for(let u=0;u<a;u++)t&&!t[u]||(r[u]=s(o[u],i[u]));return r}function on(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]&o[r]);return a}function ee(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]<<o[r]);return a}function an(n,t,e){const[s]=n,o=s.length,i=d.createEmptyBand(e,o);for(let a=0;a<o;a++)t&&!t[a]||(i[a]=~s[a]);return i}function un(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]|o[r]);return a}function ln(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]>>o[r]);return a}function cn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]^o[r]);return a}function pn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]&&o[r]?1:0);return a}function hn(n,t,e){const[s]=n,o=s.length,i=d.createEmptyBand(e,o);for(let a=0;a<o;a++)t&&!t[a]||(i[a]=s[a]?0:1);return i}function mn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]||o[r]?1:0);return a}function fn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=(s[r]?1:0)^(o[r]?1:0));return a}function dn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]===o[r]?1:0);return a}function Vt(n,t,e,s){const[o]=n,i=o.length,a=d.createEmptyBand(e,i),r=s===Math.E;for(let u=0;u<i;u++)t&&!t[u]||(a[u]=r?Math.exp(o[u]):s**o[u]);return a}function gn(n,t,e){return Vt(n,t,e,10)}function yn(n,t,e){return Vt(n,t,e,2)}function xn(n,t,e){return Vt(n,t,e,Math.E)}function Lt(n,t,e,s){const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(o[r]<=0?t[r]=0:a[r]=s(o[r]));return a}function wn(n,t,e){return Lt(n,t,e,Math.log10)}function vn(n,t,e){return Lt(n,t,e,Math.log2)}function bn(n,t,e){return Lt(n,t,e,Math.log)}function An(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]>o[r]?1:0);return a}function $n(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]>=o[r]?1:0);return a}function In(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]<o[r]?1:0);return a}function Fn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]<=o[r]?1:0);return a}function Pn(n,t,e){const[s]=n,o=s.length,i=d.createEmptyBand(e,o);if(!t)return i;for(let a=0;a<o;a++)i[a]=t[a]?0:1;return i}function Nn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]%o[r]);return a}function Rn(n,t,e){const[s]=n,o=s.length,i=d.createEmptyBand(e,o);for(let a=0;a<o;a++)t&&!t[a]||(i[a]=-s[a]);return i}function Tn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t&&!t[r]||(a[r]=s[r]===o[r]?0:1);return a}function Bn(n,t,e){const[s,o]=n,i=s.length,a=d.createEmptyBand(e,i),r=new Uint8Array(i);for(let u=0;u<i;u++)t!=null&&!t[u]||s[u]!==0||(a[u]=o[u],r[u]=255);return{band:a,mask:r}}function se(n,t,e){const[s,o,i]=n,a=s.length,r=d.createEmptyBand(e,a);for(let u=0;u<a;u++)t&&!t[u]||(r[u]=s[u]?o[u]:i[u]);return r}function ne(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)if(!t||t[r]){let u=o[r];for(let l=1;l<s;l++){const p=n[l][r];u<p&&(u=p)}a[r]=u}return a}function re(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)if(!t||t[r]){let u=o[r];for(let l=1;l<s;l++){const p=n[l][r];u>p&&(u=p)}a[r]=u}return a}function oe(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)if(!t||t[r]){let u=o[r],l=u;for(let p=1;p<s;p++){const h=n[p][r];l<h?l=h:u>h&&(u=h)}a[r]=l-u}return a}function ie(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)if(!t||t[r]){let u=0;for(let l=0;l<s;l++)u+=n[l][r];a[r]=u/s}return a}function ae(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)if(!t||t[r])for(let u=0;u<s;u++){const l=n[u];a[r]+=l[r]}return a}function ue(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)if(!t||t[r]){const u=new Float32Array(s);let l=0;for(let h=0;h<s;h++){const f=n[h];l+=f[r],u[h]=f[r]}l/=s;let p=0;for(let h=0;h<s;h++)p+=(u[h]-l)**2;a[r]=Math.sqrt(p/s)}return a}function le(n,t,e){const s=n.length;if(s<2)return n[0];const o=Math.floor(s/2),[i]=n,a=i.length,r=d.createEmptyBand(e,a),u=new Float32Array(s),l=s%2==1;for(let p=0;p<a;p++)if(!t||t[p]){for(let h=0;h<s;h++)u[h]=n[h][p];u.sort(),r[p]=l?u[o]:(u[o]+u[o-1])/2}return r}function Fe(n,t,e){const[s,o]=n;if(o==null)return s;const i=s.length,a=d.createEmptyBand(e,i);for(let r=0;r<i;r++)t[r]&&(s[r]===o[r]?a[r]=s[r]:t[r]=0);return a}function ce(n,t,e){const s=n.length;if(s<=2)return Fe(n,t,e);const o=n[0].length,i=d.createEmptyBand(e,o),a=new Map;for(let r=0;r<o;r++)if(!t||t[r]){let u;a.clear();for(let h=0;h<s;h++)u=n[h][r],a.set(u,a.has(u)?a.get(u)+1:1);let l=0,p=0;for(const h of a.keys())l=a.get(h),l>p&&(p=l,u=h);i[r]=u}return i}function pe(n,t,e){const s=n.length;if(s<=2)return Fe(n,t,e);const o=n[0].length,i=d.createEmptyBand(e,o),a=new Map;for(let r=0;r<o;r++)if(!t||t[r]){let u;a.clear();for(let h=0;h<s;h++)u=n[h][r],a.set(u,a.has(u)?a.get(u)+1:1);let l=0,p=n.length;for(const h of a.keys())l=a.get(h),l<p&&(p=l,u=h);i[r]=u}return i}function he(n,t,e){const s=n.length;if(s<2)return n[0];const[o]=n,i=o.length,a=d.createEmptyBand(e,i),r=new Set;for(let u=0;u<i;u++)if(!t||t[u]){let l;r.clear();for(let p=0;p<s;p++)l=n[p][u],r.add(l);a[u]=r.size}return a}const F=new Map,wt=new Map,g=new Map,$=new Map;function kn(){F.size||(F.set(4,Math.sqrt),F.set(6,Math.acos),F.set(7,Math.asin),F.set(8,Math.atan),F.set(9,Math.atanh),F.set(10,Math.abs),F.set(21,Math.cos),F.set(22,Math.cosh),F.set(48,Math.floor),F.set(49,Math.ceil),F.set(51,Math.sin),F.set(52,Math.sinh),F.set(56,Math.tan),F.set(57,Math.tanh),F.set(59,Math.acosh),F.set(60,Math.asinh),F.set(65,Math.floor),wt.set(5,Math.pow),wt.set(61,Math.atan2),g.set(1,Js),g.set(2,Ks),g.set(3,Zs),g.set(11,on),g.set(12,ee),g.set(12,ee),g.set(13,an),g.set(14,un),g.set(15,ln),g.set(16,cn),g.set(17,pn),g.set(18,hn),g.set(19,mn),g.set(20,fn),g.set(23,Ie),g.set(24,dn),g.set(25,xn),g.set(26,gn),g.set(27,yn),g.set(28,An),g.set(29,$n),g.set(30,Qs),g.set(31,Pn),g.set(32,Ys),g.set(33,In),g.set(34,Fn),g.set(35,bn),g.set(36,wn),g.set(37,vn),g.set(44,Nn),g.set(45,Rn),g.set(46,Tn),g.set(64,tn),g.set(65,en),g.set(76,se),g.set(78,se),$.set(38,ce),$.set(39,ne),$.set(40,ie),$.set(41,le),$.set(42,re),$.set(43,pe),$.set(47,oe),$.set(54,ue),$.set(55,ae),$.set(58,he),$.set(66,ce),$.set(67,ne),$.set(68,ie),$.set(69,le),$.set(70,re),$.set(71,pe),$.set(72,oe),$.set(73,ue),$.set(74,ae),$.set(75,he))}function Cn(n,t,e,s){let[o,i]=vt(e);const a=e.startsWith("u")||e.startsWith("s");a&&(o-=1e-5,i+=1e-5);for(let r=0;r<t.length;r++)if(t[r]){const u=n[r];isNaN(u)||u<o||u>i?t[r]=0:s[r]=a?Math.round(u):u}}function Sn(n,t,e={}){kn();let s=Hs(n,t>=66&&t<=75);const{outputPixelType:o="f32"}=e,i=!$.has(t)||e.processAsMultiband,a=i?n[0].pixels.length:1,r=[];for(let l=0;l<a;l++){const p=$.has(t)&&!i?n.flatMap(w=>w.pixels):n.map(w=>w.pixels[l]);let h,f=!0;if(t===Ae.setNull){const w=Bn(p,s,o);h=w.band,s=w.mask,f=!1}else g.has(t)?h=g.get(t)(p,s,"f64"):F.has(t)?h=t===_.asin||t===_.acos||t===_.atanh?sn(p,s,"f64",t):nn(p,s,"f64",F.get(t)):wt.has(t)?h=rn(p,s,"f64",wt.get(t)):$.has(t)?h=$.get(t)(p,s,"f64"):(h=p[0],f=!1);if(f&&t!==C.isNull&&!N.has(t)){const w=d.createEmptyBand(o,h.length);s||(s=new Uint8Array(h.length).fill(255)),Cn(h,s,o,w),h=w}r.push(h)}const u=n[0];return new d({width:u.width,height:u.height,pixelType:o,mask:t===C.isNull?null:s,pixels:r})}let rt=class extends M{constructor(){super(...arguments),this.functionName="Local",this.functionArguments=null,this.rasterArgumentNames=["rasters"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0],{bandCount:s}=e,{processAsMultiband:o}=this.functionArguments;if(t.some(p=>p.bandCount!==s))return{success:!1,supportsGPU:!1,error:"local-function: input rasters do not have same band count"};const{operation:i,rasters:a}=this.functionArguments,r=te[i];if(!(r===999||a.length===r||a.length<=1&&r===1))return{success:!1,supportsGPU:!1,error:`local-function: the length of functionArguments.rasters does not match operation's requirement: ${r}`};this.outputPixelType=this._getOutputPixelType("f32");const u=e.clone();u.pixelType=this.outputPixelType,u.statistics=null,u.histograms=null,u.colormap=null,u.attributeTable=null,u.bandCount=r!==999||o?s:1;const l=Xs(i);if(l){u.statistics=[];for(let p=0;p<u.bandCount;p++)u.statistics[p]={min:l[0],max:l[1],avg:(l[0]+l[1])/2,stddev:(l[0]+l[1])/10}}return this.rasterInfo=u,{success:!0,supportsGPU:u.bandCount===1&&r<=3&&(i<11||i>16)}}_processPixels(t){const{pixelBlocks:e}=t;return k(e)||e.some(s=>k(s))?null:Sn(e,this.functionArguments.operation,{processAsMultiband:this.functionArguments.processAsMultiband,outputPixelType:this.outputPixelType??void 0})}_getWebGLParameters(){var u;const{operation:t}=this.functionArguments,e=te[t],s=((u=Object.keys(Kt).find(l=>Kt[l]===t))==null?void 0:u.toLowerCase())??"undefined",o=this.outputPixelType??"f32";let[i,a]=vt(o);const r=o.startsWith("u")||o.startsWith("s");return r&&(i-=1e-4,a+=1e-4),{imageCount:e,operationName:s,domainRange:[i,a],isOutputRounded:r}}};c([m({json:{write:!0,name:"rasterFunction"}})],rt.prototype,"functionName",void 0),c([m({type:qs,json:{write:!0,name:"rasterFunctionArguments"}})],rt.prototype,"functionArguments",void 0),c([m()],rt.prototype,"rasterArgumentNames",void 0),rt=c([x("esri.layers.support.rasterFunctions.LocalFunction")],rt);const jn=rt;var Bt;let J=Bt=class extends j{constructor(){super(...arguments),this.includedRanges=null,this.noDataValues=null,this.noDataInterpretation=we.matchAny}get normalizedNoDataValues(){const{noDataValues:n}=this;if(!(n!=null&&n.length))return null;let t=!1;const e=n.map(s=>{if(typeof s=="number")return t=!0,[s];if(typeof s=="string"){const o=s.trim().split(" ").filter(i=>i.trim()!=="").map(i=>Number(i));return t=t||o.length>0,o.length===0?null:o}return null});return t?e:null}clone(){var n,t;return new Bt({includedRanges:((n=this.includedRanges)==null?void 0:n.slice())??[],noDataValues:((t=this.noDataValues)==null?void 0:t.slice())??[],noDataInterpretation:this.noDataInterpretation})}};c([m({json:{write:!0}})],J.prototype,"includedRanges",void 0),c([m({json:{write:!0}})],J.prototype,"noDataValues",void 0),c([m()],J.prototype,"normalizedNoDataValues",null),c([m({json:{write:!0}})],J.prototype,"noDataInterpretation",void 0),J=Bt=c([x("esri.layers.support.rasterFunctions.MaskFunctionArguments")],J);const Mn=J;let Y=class extends M{constructor(){super(...arguments),this.functionName="Mask",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const t=this.sourceRasterInfos[0].clone(),{pixelType:e}=t;this.outputPixelType=this._getOutputPixelType(e),t.pixelType=this.outputPixelType,this.rasterInfo=t;const{includedRanges:s,normalizedNoDataValues:o}=this.functionArguments;if(!(s!=null&&s.length)&&!(o!=null&&o.length))return{success:!1,supportsGPU:!1,error:"missing includedRanges or noDataValues argument"};let i=[];for(let r=0;r<t.bandCount;r++){const u=Me(e,s==null?void 0:s.slice(2*r,2*r+2),o==null?void 0:o[r]);if(u==null){i=null;break}i.push(u)}this.lookups=i;const a=o!=null&&o.every(r=>{var u;return(r==null?void 0:r.length)===((u=o[0])==null?void 0:u.length)});return{success:!0,supportsGPU:(!s||s.length<=2*W)&&(!o||a&&o[0].length<=W)}}_processPixels(t){var l;const e=(l=t.pixelBlocks)==null?void 0:l[0];if(k(e))return null;const{outputPixelType:s,lookups:o}=this,{includedRanges:i,noDataInterpretation:a,normalizedNoDataValues:r}=this.functionArguments,u=a===we.matchAll;return De(e,{includedRanges:i,noDataValues:r,outputPixelType:s,matchAll:u,lookups:o})}_getWebGLParameters(){var i;const{includedRanges:t,normalizedNoDataValues:e}=this.functionArguments,s=new Float32Array(W);s.fill(at),(i=e==null?void 0:e[0])!=null&&i.length&&s.set(e[0]);const o=new Float32Array(W);for(let a=0;a<o.length;a+=2)o[a]=(t==null?void 0:t[a])??-at,o[a+1]=(t==null?void 0:t[a+1])??at;return t&&t.length&&o.set(t),{bandCount:this.sourceRasterInfos[0].bandCount,noDataValues:s,includedRanges:o}}};c([m({json:{write:!0,name:"rasterFunction"}})],Y.prototype,"functionName",void 0),c([m({type:Mn,json:{write:!0,name:"rasterFunctionArguments"}})],Y.prototype,"functionArguments",void 0),c([m()],Y.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}})],Y.prototype,"lookups",void 0),Y=c([x("esri.layers.support.rasterFunctions.MaskFunction")],Y);const Dn=Y;var kt;let ot=kt=class extends j{constructor(){super(...arguments),this.visibleBandID=0,this.infraredBandID=1,this.scientificOutput=!1}clone(){const{visibleBandID:n,infraredBandID:t,scientificOutput:e}=this;return new kt({visibleBandID:n,infraredBandID:t,scientificOutput:e})}};c([m({json:{write:!0}})],ot.prototype,"visibleBandID",void 0),c([m({json:{write:!0}})],ot.prototype,"infraredBandID",void 0),c([m({json:{write:!0}})],ot.prototype,"scientificOutput",void 0),ot=kt=c([x("esri.layers.support.rasterFunctions.NDVIFunctionArguments")],ot);const _n=ot;let it=class extends M{constructor(){super(...arguments),this.functionName="NDVI",this.functionArguments=null,this.rasterArgumentNames=["raster"]}_bindSourceRasters(){const{scientificOutput:t}=this.functionArguments;this.outputPixelType=this._getOutputPixelType(t?"f32":"u8");const e=this.sourceRasterInfos[0].clone();e.pixelType=this.outputPixelType,e.colormap=null,e.histograms=null,e.bandCount=1;const[s,o,i,a]=t?[-1,1,0,.1]:[0,200,100,10];return e.statistics=[{min:s,max:o,avg:i,stddev:a}],this.rasterInfo=e,{success:!0,supportsGPU:!0}}_processPixels(t){var a;const e=(a=t.pixelBlocks)==null?void 0:a[0];if(k(e))return null;const{visibleBandID:s,infraredBandID:o,scientificOutput:i}=this.functionArguments;return rs(e,s,o,!i)}_getWebGLParameters(){const{visibleBandID:t,infraredBandID:e,scientificOutput:s}=this.functionArguments,o=this.isInputBandIdsSwizzled?[0,1,2]:[e,t,0];return{bandIndexMat3:P(o),scaled:!s}}_getInputBandIds(t){const{visibleBandID:e,infraredBandID:s}=this.functionArguments;return[s,e,0].map(o=>t[o])}};c([m({json:{write:!0,name:"rasterFunction"}})],it.prototype,"functionName",void 0),c([m({type:_n,json:{write:!0,name:"rasterFunctionArguments"}})],it.prototype,"functionArguments",void 0),c([m()],it.prototype,"rasterArgumentNames",void 0),it=c([x("esri.layers.support.rasterFunctions.NDVIFunction")],it);const En=it;var Ct;let G=Ct=class extends j{constructor(){super(...arguments),this.inputRanges=null,this.outputValues=null,this.noDataRanges=null,this.allowUnmatched=!1,this.isLastInputRangeInclusive=!1}clone(){return new Ct({inputRanges:[...this.inputRanges],outputValues:[...this.outputValues],noDataRanges:[...this.noDataRanges],allowUnmatched:this.allowUnmatched,isLastInputRangeInclusive:this.isLastInputRangeInclusive})}};c([m({json:{write:!0}})],G.prototype,"inputRanges",void 0),c([m({json:{write:!0}})],G.prototype,"outputValues",void 0),c([m({json:{write:!0}})],G.prototype,"noDataRanges",void 0),c([m({json:{write:!0}})],G.prototype,"allowUnmatched",void 0),c([m({json:{write:!0}})],G.prototype,"isLastInputRangeInclusive",void 0),G=Ct=c([x("esri.layers.support.rasterFunctions.RemapFunctionArguments")],G);const On=G;let K=class extends M{constructor(){super(...arguments),this.functionName="Remap",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.lookup=null}_bindSourceRasters(){const t=this.sourceRasterInfos[0].clone(),{pixelType:e}=t;this.outputPixelType=this._getOutputPixelType(e),t.pixelType=this.outputPixelType,t.colormap=null,t.histograms=null,t.bandCount=1,t.attributeTable=null;const{statistics:s}=t,{allowUnmatched:o,outputValues:i,inputRanges:a,noDataRanges:r,isLastInputRangeInclusive:u}=this.functionArguments;if(S(s)&&s.length&&(i!=null&&i.length))if(o){const l=Math.min.apply(null,[...i,s[0].min]),p=Math.max.apply(null,[...i,s[0].max]);t.statistics=[{...s[0],min:l,max:p}]}else{let l=i[0],p=l;for(let h=0;h<i.length;h++)l=l>i[h]?i[h]:l,p=p>i[h]?p:i[h];t.statistics=[{...s[0],min:l,max:p}]}return this.rasterInfo=t,this.lookup=o?null:_e({srcPixelType:e,inputRanges:a,outputValues:i,noDataRanges:r,allowUnmatched:o,isLastInputRangeInclusive:u,outputPixelType:this.outputPixelType}),{success:!0,supportsGPU:(!i||i.length<=W)&&(!r||r.length<=W)}}_processPixels(t){var p;const e=(p=t.pixelBlocks)==null?void 0:p[0];if(k(e))return null;const{lookup:s,outputPixelType:o}=this;if(s){const h=ve(e,{lut:[s.lut],offset:s.offset,outputPixelType:o});return S(h)&&s.mask&&(h.mask=Ee(e.pixels[0],e.mask,s.mask,s.offset,"u8")),h}const{inputRanges:i,outputValues:a,noDataRanges:r,allowUnmatched:u,isLastInputRangeInclusive:l}=this.functionArguments;return Oe(e,{inputRanges:i,outputValues:a,noDataRanges:r,outputPixelType:o,allowUnmatched:u,isLastInputRangeInclusive:l})}_getWebGLParameters(){const{allowUnmatched:t,inputRanges:e,outputValues:s,noDataRanges:o,isLastInputRangeInclusive:i}=this.functionArguments,a=new Float32Array(3*W),r=1e-5,u=s.length;if(e!=null&&e.length){let p=0,h=0;for(let f=0;f<a.length;f+=3)a[f]=e[p++]??at-1,a[f+1]=e[p++]??at,a[f+2]=s[h++]??0,h<=u&&(f>0&&(a[f]-=r),(h<u||!i)&&(a[f+1]-=r))}const l=new Float32Array(2*W);return l.fill(at),o!=null&&o.length&&l.set(o),{allowUnmatched:t,rangeMaps:a,noDataRanges:l,clampRange:vt(this.outputPixelType)}}};c([m({json:{write:!0,name:"rasterFunction"}})],K.prototype,"functionName",void 0),c([m({type:On,json:{write:!0,name:"rasterFunctionArguments"}})],K.prototype,"functionArguments",void 0),c([m()],K.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}})],K.prototype,"lookup",void 0),K=c([x("esri.layers.support.rasterFunctions.RemapFunction")],K);const zn=K;var St;const Gn=new zt({1:"degree",2:"percent-rise",3:"adjusted"},{useNumericKeys:!0});let V=St=class extends j{constructor(){super(...arguments),this.slopeType="degree",this.zFactor=1,this.pixelSizePower=.664,this.pixelSizeFactor=.024,this.removeEdgeEffect=!1}clone(){return new St({slopeType:this.slopeType,zFactor:this.zFactor,pixelSizePower:this.pixelSizePower,pixelSizeFactor:this.pixelSizeFactor,removeEdgeEffect:this.removeEdgeEffect,raster:this.raster})}};c([lt(Gn)],V.prototype,"slopeType",void 0),c([m({type:Number,json:{write:!0}})],V.prototype,"zFactor",void 0),c([m({type:Number,json:{name:"psPower",write:!0}})],V.prototype,"pixelSizePower",void 0),c([m({type:Number,json:{name:"psZFactor",write:!0}})],V.prototype,"pixelSizeFactor",void 0),c([m({type:Boolean,json:{write:!0}})],V.prototype,"removeEdgeEffect",void 0),V=St=c([x("esri.layers.support.rasterFunctions.SlopeFunctionArguments")],V);const Vn=V,Ln=1/111e3;let Z=class extends M{constructor(){super(...arguments),this.functionName="Slope",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isGCS=!1}_bindSourceRasters(){var e;this.outputPixelType=this._getOutputPixelType("f32");const t=this.sourceRasterInfos[0].clone();return t.pixelType=this.outputPixelType,t.statistics=this.functionArguments.slopeType!=="percent-rise"?[{min:0,max:90,avg:1,stddev:1}]:null,t.histograms=null,t.colormap=null,t.attributeTable=null,t.bandCount=1,this.rasterInfo=t,this.isGCS=((e=t.spatialReference)==null?void 0:e.isGeographic)??!1,{success:!0,supportsGPU:!0}}_processPixels(t){var p;const e=(p=t.pixelBlocks)==null?void 0:p[0];if(k(e))return null;const{zFactor:s,slopeType:o,pixelSizePower:i,pixelSizeFactor:a}=this.functionArguments,{isGCS:r}=this,{extent:u}=t,l=u?{x:u.width/e.width,y:u.height/e.height}:{x:1,y:1};return Ue(e,{zFactor:s,slopeType:o,pixelSizePower:i,pixelSizeFactor:a,isGCS:r,resolution:l})}_getWebGLParameters(){const{zFactor:t,slopeType:e,pixelSizeFactor:s,pixelSizePower:o}=this.functionArguments;return{zFactor:this.isGCS&&t>=1?t*Ln:t,slopeType:e,pixelSizeFactor:s??0,pixelSizePower:o??0}}};c([m({json:{write:!0,name:"rasterFunction"}})],Z.prototype,"functionName",void 0),c([m({type:Vn,json:{write:!0,name:"rasterFunctionArguments"}})],Z.prototype,"functionArguments",void 0),c([m()],Z.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}})],Z.prototype,"isGCS",void 0),Z=c([x("esri.layers.support.rasterFunctions.SlopeFunction")],Z);const Un=Z;var jt;let Q=jt=class extends j{constructor(){super(...arguments),this.statistics=null,this.histograms=null}readStatistics(n,t){if(!(n!=null&&n.length))return null;const e=[];return n.forEach(s=>{const o={min:s.min,max:s.max,avg:s.avg??s.mean,stddev:s.stddev??s.standardDeviation};e.push(o)}),e}writeStatistics(n,t,e){if(!(n!=null&&n.length))return;const s=[];n.forEach(o=>{const i={...o,mean:o.avg,standardDeviation:o.stddev};delete i.avg,delete i.stddev,s.push(i)}),t[e]=s}clone(){return new jt({statistics:E(this.statistics),histograms:E(this.histograms)})}};c([m({json:{write:!0}})],Q.prototype,"statistics",void 0),c([gt("statistics")],Q.prototype,"readStatistics",null),c([ut("statistics")],Q.prototype,"writeStatistics",null),c([m({json:{write:!0}})],Q.prototype,"histograms",void 0),Q=jt=c([x("esri.layers.support.rasterFunctions.StatisticsHistogramFunctionArguments")],Q);const Wn=Q;let L=class extends M{constructor(){super(...arguments),this.functionName="StatisticsHistogram",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.isNoopProcess=!0}_bindSourceRasters(){const t=this.sourceRasterInfos[0];this.outputPixelType=this._getOutputPixelType("u8");const e=t.clone(),{statistics:s,histograms:o}=this.functionArguments;return o&&(e.histograms=o),s&&(e.statistics=s),this.rasterInfo=e,{success:!0,supportsGPU:!0}}_processPixels(t){var e;return(e=t.pixelBlocks)==null?void 0:e[0]}};c([m({json:{write:!0,name:"rasterFunction"}})],L.prototype,"functionName",void 0),c([m({type:Wn,json:{write:!0,name:"rasterFunctionArguments"}})],L.prototype,"functionArguments",void 0),c([m()],L.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}})],L.prototype,"indexedColormap",void 0),c([m()],L.prototype,"isNoopProcess",void 0),L=c([x("esri.layers.support.rasterFunctions.StatisticsHistogramFunction")],L);const qn=L;var Mt;const Xn=new zt({0:"none",3:"standard-deviation",4:"histogram-equalization",5:"min-max",6:"percent-clip",9:"sigmoid"},{useNumericKeys:!0});let T=Mt=class extends j{constructor(){super(...arguments),this.computeGamma=!1,this.dynamicRangeAdjustment=!1,this.gamma=[],this.histograms=null,this.statistics=null,this.stretchType="none",this.useGamma=!1}writeStatistics(n,t,e){n!=null&&n.length&&(Array.isArray(n[0])||(n=n.map(s=>[s.min,s.max,s.avg,s.stddev])),t[e]=n)}clone(){return new Mt({stretchType:this.stretchType,outputMin:this.outputMin,outputMax:this.outputMax,useGamma:this.useGamma,computeGamma:this.computeGamma,statistics:E(this.statistics),gamma:E(this.gamma),sigmoidStrengthLevel:this.sigmoidStrengthLevel,numberOfStandardDeviations:this.numberOfStandardDeviations,minPercent:this.minPercent,maxPercent:this.maxPercent,histograms:E(this.histograms),dynamicRangeAdjustment:this.dynamicRangeAdjustment,raster:this.raster})}};c([m({type:Boolean,json:{write:!0}})],T.prototype,"computeGamma",void 0),c([m({type:Boolean,json:{name:"dra",write:!0}})],T.prototype,"dynamicRangeAdjustment",void 0),c([m({type:[Number],json:{write:!0}})],T.prototype,"gamma",void 0),c([m()],T.prototype,"histograms",void 0),c([m({type:Number,json:{write:!0}})],T.prototype,"maxPercent",void 0),c([m({type:Number,json:{write:!0}})],T.prototype,"minPercent",void 0),c([m({type:Number,json:{write:!0}})],T.prototype,"numberOfStandardDeviations",void 0),c([m({type:Number,json:{name:"max",write:!0}})],T.prototype,"outputMax",void 0),c([m({type:Number,json:{name:"min",write:!0}})],T.prototype,"outputMin",void 0),c([m({type:Number,json:{write:!0}})],T.prototype,"sigmoidStrengthLevel",void 0),c([m({json:{type:[[Number]],write:!0}})],T.prototype,"statistics",void 0),c([ut("statistics")],T.prototype,"writeStatistics",null),c([lt(Xn)],T.prototype,"stretchType",void 0),c([m({type:Boolean,json:{write:!0}})],T.prototype,"useGamma",void 0),T=Mt=c([x("esri.layers.support.rasterFunctions.StretchFunctionArguments")],T);const Hn=T;let U=class extends M{constructor(){super(...arguments),this.functionName="Stretch",this.functionArguments=null,this.rasterArgumentNames=["raster"],this.lookup=null,this.cutOffs=null}_bindSourceRasters(){this.lookup=null,this.cutOffs=null;const t=this.sourceRasterInfos[0],{pixelType:e}=t,{functionArguments:s}=this,{dynamicRangeAdjustment:o,gamma:i,useGamma:a}=s;if(!o&&["u8","u16","s8","s16"].includes(e)){const u=bt(s.toJSON(),{rasterInfo:t}),l=this._isOutputRoundingNeeded()?"round":"float";this.lookup=We({pixelType:e,...u,gamma:a?i:null,rounding:l}),this.cutOffs=u}else o||(this.cutOffs=bt(s.toJSON(),{rasterInfo:t}));this.outputPixelType=this._getOutputPixelType(e);const r=t.clone();return r.pixelType=this.outputPixelType,r.statistics=null,r.histograms=null,r.colormap=null,r.attributeTable=null,this.outputPixelType==="u8"&&(r.keyProperties.DataType="processed"),this.rasterInfo=r,{success:!0,supportsGPU:!o}}_processPixels(t){var r;const e=(r=t.pixelBlocks)==null?void 0:r[0];if(k(e))return e;const{lookup:s}=this;if(s)return ve(e,{...s,outputPixelType:this.rasterInfo.pixelType});const{functionArguments:o}=this,i=this.cutOffs||bt(o.toJSON(),{rasterInfo:this.sourceRasterInfos[0],pixelBlock:e}),a=o.useGamma?o.gamma:null;return qe(e,{...i,gamma:a,outputPixelType:this.outputPixelType})}_getWebGLParameters(){const{outputMin:t=0,outputMax:e=255,gamma:s,useGamma:o}=this.functionArguments,i=this.rasterInfo.bandCount>=2?3:1,a=o&&s&&s.length?Xe(i,s):[1,1,1],{minCutOff:r,maxCutOff:u}=this.cutOffs??{minCutOff:[0,0,0],maxCutOff:[255,255,255]};r.length===1&&(r[1]=r[2]=r[0],u[1]=u[2]=u[0]);const l=new Float32Array(i);let p;for(p=0;p<i;p++)l[p]=(e-t)/(u[p]-r[p]);const h=this._isOutputRoundingNeeded();return{bandCount:i,outMin:t,outMax:e,minCutOff:r,maxCutOff:u,factor:l,useGamma:o,gamma:o&&s?s:[1,1,1],gammaCorrection:o&&a?a:[1,1,1],stretchType:this.functionArguments.stretchType,isOutputRounded:h,type:"stretch"}}};c([m({json:{write:!0,name:"rasterFunction"}})],U.prototype,"functionName",void 0),c([m({type:Hn,json:{write:!0,name:"rasterFunctionArguments"}})],U.prototype,"functionArguments",void 0),c([m()],U.prototype,"rasterArgumentNames",void 0),c([m({json:{write:!0}})],U.prototype,"lookup",void 0),c([m({json:{write:!0}})],U.prototype,"cutOffs",void 0),U=c([x("esri.layers.support.rasterFunctions.StretchFunction")],U);const Jn=U,B=new Map;function Yn(n,t){const{rasterFunctionArguments:e}=n;e&&(e.rasters||[e.raster]).forEach(s=>{s&&typeof s!="number"&&(typeof s=="string"?s.startsWith("http")&&(t.includes(s)||t.push(s)):"rasterFunctionArguments"in s&&Yn(s,t))})}function Tr(n,t){if(t=t??{},"function"in(n=E(n))&&"arguments"in n&&n.arguments&&(n=Zn(n,t)),"rasterFunction"in n)return Pe(n=Dt(n),t);throw new ye("raster-function-helper","unsupported raster function json.")}function Kn(n,t){return t[0]==="rasters"&&Array.isArray(n.rasters)?n.rasters:t.map(e=>n[e])}function me(n){return!!(n&&typeof n=="object"&&n.rasterFunction&&n.rasterFunctionArguments)}function Dt(n){var o;const{rasterFunction:t,rasterFunctionArguments:e}=n,s={};for(const i in e){let a=e[i];const r=i.toLowerCase();if(r==="rasters"&&Array.isArray(a))s.rasters=a.map(u=>me(u)?Dt(u):u);else switch(me(a)&&(a=Dt(a)),r){case"dra":s.dra=a;break;case"pspower":s.psPower=a;break;case"pszfactor":s.psZFactor=a;break;case"bandids":s.bandIds=a;break;default:s[i[0].toLowerCase()+i.slice(1)]=a}}return t!=="Local"||(o=s.rasters)!=null&&o.length||(s.rasters=["$$"]),{...n,rasterFunctionArguments:s}}function Pe(n,t){var f,w;const{rasterFunction:e,rasterFunctionArguments:s}=n,o=(f=n.outputPixelType)==null?void 0:f.toLowerCase();if(e==null||!B.has(e))throw new ye("raster-function-helper",`unsupported raster function: ${e}`);const i=B.get(e),a=(typeof i.ctor=="function"?i.ctor:i.ctor.default).fromJSON({...n,outputPixelType:o}),{rasterArgumentNames:r}=a,u=[],l=Kn(s,r),p=r[0]==="rasters",h=[];for(let v=0;v<l.length;v++){const b=l[v];let R;b==null||typeof b=="string"&&b.startsWith("$")?u.push(t==null?void 0:t.raster):typeof b=="string"?t[b]&&u.push(t[b]):typeof b!="number"&&"rasterFunction"in b&&(R=Pe(b,t),p||(a.functionArguments[r[v]]=R),u.push(R)),p&&h.push(R??b)}if(p&&(a.functionArguments.rasters=h),t){a.sourceRasters=u;const v=(w=t.raster)==null?void 0:w.url;v&&(a.mainPrimaryRasterId=v)}return a}function Ne(n,t){if(n&&t)for(const e in n){const s=n[e];s&&typeof s=="object"&&(s.function&&s.arguments?Ne(s.arguments,t):s.type==="RasterFunctionVariable"&&t[s.name]!=null&&(s.value=t[s.name]))}}function _t(n){var t;if(!n||typeof n!="object")return n;if(Array.isArray(n)&&n.length===0)return n.length===0?null:["number","string"].includes(typeof n[0])?n:n.map(e=>_t(e));if("value"in n&&["number","string","boolean"].includes(typeof n.value))return n.value;if(!("type"in n))return n;switch(n.type){case"Scalar":return n.value;case"AlgorithmicColorRamp":return fe(n);case"MultiPartColorRamp":return{type:"multipart",colorRamps:n.ArrayOfColorRamp.map(fe)};case"ArgumentArray":return(t=n.elements)!=null&&t.length?n.elements[0].type==="RasterStatistics"?n.elements:n.elements[0].type==="RasterFunctionVariable"?n.elements.map(e=>e.value!=null?_t(e.value):e.name.toLowerCase().includes("raster")?"$$":null):n:n.elements;default:return n}}function fe(n){const t=n.algorithm??"esriHSVAlgorithm";let{FromColor:e,ToColor:s}=n;if(!Array.isArray(e)){const{r:o,g:i,b:a}=Xt({h:e.Hue,s:e.Saturation,v:e.Value});e=[o,i,a,e.AlphaValue]}if(!Array.isArray(s)){const{r:o,g:i,b:a}=Xt({h:s.Hue,s:s.Saturation,v:s.Value});s=[o,i,a,s.AlphaValue]}return{type:"algorithmic",algorithm:t,fromColor:e,toColor:s}}function Zn(n,t){t&&Ne(n,t);const e={};return Re(n,e),e}function Re(n,t){if(!n||!t)return;const{function:e,arguments:s}=n;if(!e||!s)return;t.rasterFunction=e.type.replace("Function",""),t.outputPixelType=e.pixelType;const o={};t.rasterFunctionArguments=o;for(const i in s){const a=s[i];typeof a=="object"&&("function"in a&&a.function&&a.arguments?(t.rasterFunctionArguments[i]={},Re(a,t.rasterFunctionArguments[i])):"value"in a&&(o[i]=_t(a.value)))}switch(o.DEM&&!o.Raster&&(o.Raster=o.DEM,delete o.DEM),t.rasterFunction){case"Stretch":Qn(o);break;case"Colormap":tr(o);break;case"Convolution":er(o);break;case"Mask":sr(o)}}function Qn(n){var t;(t=n.Statistics)!=null&&t.length&&typeof n.Statistics=="object"&&(n.Statistics=n.Statistics.map(e=>[e.min,e.max,e.mean,e.standardDeviation])),n.NumberOfStandardDeviation!=null&&(n.NumberOfStandardDeviations=n.NumberOfStandardDeviation,delete n.NumberOfStandardDeviation)}function tr(n){var t,e;((e=(t=n.ColorRamp)==null?void 0:t.type)==null?void 0:e.toLowerCase())==="randomcolorramp"&&(delete n.ColorRamp,n.ColormapName="Random"),n.ColorSchemeType===0&&delete n.ColorRamp}function er(n){n.ConvolutionType!=null&&(n.Type=n.ConvolutionType,delete n.ConvolutionType)}function sr(n){var t;(t=n.NoDataValues)!=null&&t.length&&typeof n.NoDataValues[0]=="string"&&(n.NoDataValues=n.NoDataValues.filter(e=>e!=="").map(e=>Number(e)))}B.set("Aspect",{desc:"Aspect Function",ctor:Je,rasterArgumentNames:["raster"]}),B.set("BandArithmetic",{desc:"Band Arithmetic Function",ctor:As,rasterArgumentNames:["raster"]}),B.set("Colormap",{desc:"Colormap Function",ctor:Cs,rasterArgumentNames:["raster"]}),B.set("CompositeBand",{desc:"CompositeBand Function",ctor:js,rasterArgumentNames:["rasters"]}),B.set("Convolution",{desc:"Convolution Function",ctor:Ls,rasterArgumentNames:["raster"]}),B.set("ExtractBand",{desc:"ExtractBand Function",ctor:Ws,rasterArgumentNames:["raster"]}),B.set("Local",{desc:"Local Function",ctor:jn,rasterArgumentNames:["rasters"]}),B.set("Mask",{desc:"Mask Function",ctor:Dn,rasterArgumentNames:["raster"]}),B.set("NDVI",{desc:"NDVI Function",ctor:En,rasterArgumentNames:["raster"]}),B.set("Remap",{desc:"Remap Function",ctor:zn,rasterArgumentNames:["raster"]}),B.set("Slope",{desc:"Slope Function",ctor:Un,rasterArgumentNames:["raster"]}),B.set("StatisticsHistogram",{desc:"Statistics Histogram Function",ctor:qn,rasterArgumentNames:["raster"]}),B.set("Stretch",{desc:"Stretch Function",ctor:Jn,rasterArgumentNames:["raster"]});let mt=class extends Ot{get affectsPixelSize(){return!1}forwardTransform(n){return n}inverseTransform(n){return n}};c([m()],mt.prototype,"affectsPixelSize",null),c([m({json:{write:!0}})],mt.prototype,"spatialReference",void 0),mt=c([x("esri.layers.support.rasterTransforms.BaseRasterTransform")],mt);const Ut=mt;let ft=class extends Ut{constructor(){super(...arguments),this.type="gcs-shift",this.tolerance=1e-8}forwardTransform(t){return(t=t.clone()).type==="point"?(t.x>180+this.tolerance&&(t.x-=360),t):(t.xmin>=180-this.tolerance?(t.xmax-=360,t.xmin-=360):t.xmax>180+this.tolerance&&(t.xmin=-180,t.xmax=180),t)}inverseTransform(t){return(t=t.clone()).type==="point"?(t.x<-this.tolerance&&(t.x+=360),t):(t.xmin<-this.tolerance&&(t.xmin+=360,t.xmax+=360),t)}};c([lt({GCSShiftXform:"gcs-shift"})],ft.prototype,"type",void 0),c([m()],ft.prototype,"tolerance",void 0),ft=c([x("esri.layers.support.rasterTransforms.GCSShiftTransform")],ft);const nr=ft;let xt=class extends Ut{constructor(){super(...arguments),this.type="identity"}};c([lt({IdentityXform:"identity"})],xt.prototype,"type",void 0),xt=c([x("esri.layers.support.rasterTransforms.IdentityTransform")],xt);const rr=xt;function Et(n,t,e){const{x:s,y:o}=t;if(e<2)return{x:n[0]+s*n[2]+o*n[4],y:n[1]+s*n[3]+o*n[5]};if(e===2){const f=s*s,w=o*o,v=s*o;return{x:n[0]+s*n[2]+o*n[4]+f*n[6]+v*n[8]+w*n[10],y:n[1]+s*n[3]+o*n[5]+f*n[7]+v*n[9]+w*n[11]}}const i=s*s,a=o*o,r=s*o,u=i*s,l=i*o,p=s*a,h=o*a;return{x:n[0]+s*n[2]+o*n[4]+i*n[6]+r*n[8]+a*n[10]+u*n[12]+l*n[14]+p*n[16]+h*n[18],y:n[1]+s*n[3]+o*n[5]+i*n[7]+r*n[9]+a*n[11]+u*n[13]+l*n[15]+p*n[17]+h*n[19]}}function de(n,t,e){const{xmin:s,ymin:o,xmax:i,ymax:a,spatialReference:r}=t;let u=[];if(e<2)u.push({x:s,y:a}),u.push({x:i,y:a}),u.push({x:s,y:o}),u.push({x:i,y:o});else{let h=10;for(let f=0;f<h;f++)u.push({x:s,y:o+(a-o)*f/(h-1)}),u.push({x:i,y:o+(a-o)*f/(h-1)});h=8;for(let f=1;f<=h;f++)u.push({x:s+(i-s)*f/h,y:o}),u.push({x:s+(i-s)*f/h,y:a})}u=u.map(h=>Et(n,h,e));const l=u.map(h=>h.x),p=u.map(h=>h.y);return new Ce({xmin:Math.min.apply(null,l),xmax:Math.max.apply(null,l),ymin:Math.min.apply(null,p),ymax:Math.max.apply(null,p),spatialReference:r})}function or(n){const[t,e,s,o,i,a]=n,r=s*a-i*o,u=i*o-s*a;return[(i*e-t*a)/r,(s*e-t*o)/u,a/r,o/u,-i/r,-s/u]}let D=class extends Ut{constructor(){super(...arguments),this.polynomialOrder=1,this.type="polynomial"}readForwardCoefficients(n,t){const{coeffX:e,coeffY:s}=t;if(!(e!=null&&e.length)||!(s!=null&&s.length)||e.length!==s.length)return null;const o=[];for(let i=0;i<e.length;i++)o.push(e[i]),o.push(s[i]);return o}writeForwardCoefficients(n,t,e){const s=[],o=[];for(let i=0;i<(n==null?void 0:n.length);i++)i%2==0?s.push(n[i]):o.push(n[i]);t.coeffX=s,t.coeffY=o}get inverseCoefficients(){let n=this._get("inverseCoefficients");const t=this._get("forwardCoefficients");return!n&&t&&this.polynomialOrder<2&&(n=or(t)),n}set inverseCoefficients(n){this._set("inverseCoefficients",n)}readInverseCoefficients(n,t){const{inverseCoeffX:e,inverseCoeffY:s}=t;if(!(e!=null&&e.length)||!(s!=null&&s.length)||e.length!==s.length)return null;const o=[];for(let i=0;i<e.length;i++)o.push(e[i]),o.push(s[i]);return o}writeInverseCoefficients(n,t,e){const s=[],o=[];for(let i=0;i<(n==null?void 0:n.length);i++)i%2==0?s.push(n[i]):o.push(n[i]);t.inverseCoeffX=s,t.inverseCoeffY=o}get affectsPixelSize(){return this.polynomialOrder>0}forwardTransform(n){if(n.type==="point"){const t=Et(this.forwardCoefficients,n,this.polynomialOrder);return new Ht({x:t.x,y:t.y,spatialReference:n.spatialReference})}return de(this.forwardCoefficients,n,this.polynomialOrder)}inverseTransform(n){if(n.type==="point"){const t=Et(this.inverseCoefficients,n,this.polynomialOrder);return new Ht({x:t.x,y:t.y,spatialReference:n.spatialReference})}return de(this.inverseCoefficients,n,this.polynomialOrder)}};c([m({json:{write:!0}})],D.prototype,"polynomialOrder",void 0),c([m()],D.prototype,"forwardCoefficients",void 0),c([gt("forwardCoefficients",["coeffX","coeffY"])],D.prototype,"readForwardCoefficients",null),c([ut("forwardCoefficients")],D.prototype,"writeForwardCoefficients",null),c([m({json:{write:!0}})],D.prototype,"inverseCoefficients",null),c([gt("inverseCoefficients",["inverseCoeffX","inverseCoeffY"])],D.prototype,"readInverseCoefficients",null),c([ut("inverseCoefficients")],D.prototype,"writeInverseCoefficients",null),c([m()],D.prototype,"affectsPixelSize",null),c([lt({PolynomialXform:"polynomial"})],D.prototype,"type",void 0),D=c([x("esri.layers.support.rasterTransforms.PolynomialTransform")],D);const ir=D,Te={GCSShiftXform:nr,IdentityXform:rr,PolynomialXform:ir},ar=Object.keys(Te);function Cr(n){const t=n==null?void 0:n.type;return!n||ar.includes(t)}function Sr(n){if(!(n==null?void 0:n.type))return null;const e=Te[n==null?void 0:n.type];if(e){const s=new e;return s.read(n),s}return null}export{Tr as C,nr as c,Cr as f,Yn as h,Sr as i,ir as m};
