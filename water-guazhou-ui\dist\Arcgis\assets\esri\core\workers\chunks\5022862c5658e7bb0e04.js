"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8239],{37140:(e,t,r)=>{function o(){const e=new Float32Array(4);return e[3]=1,e}function n(e){const t=new Float32Array(4);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}r.d(t,{b:()=>n,c:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,clone:n,create:o,createView:function(e,t){return new Float32Array(e,t,4)},fromValues:function(e,t,r,o){const n=new Float32Array(4);return n[0]=e,n[1]=t,n[2]=r,n[3]=o,n}},Symbol.toStringTag,{value:"Module"}))},3920:(e,t,r)=>{r.d(t,{p:()=>u,r:()=>l});var o=r(43697),n=r(15923),s=r(61247),i=r(5600),a=r(52011),p=r(72762);const u=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new s.Z}get updatingHandles(){return this._get("updatingHandles")||new p.t}};return(0,o._)([(0,i.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,o._)([(0,i.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,o._)([(0,a.j)("esri.core.HandleOwner")],t),t};let l=class extends(u(n.Z)){};l=(0,o._)([(0,a.j)("esri.core.HandleOwner")],l)},42033:(e,t,r)=>{r.d(t,{E:()=>n,_:()=>s});var o=r(70586);async function n(e,t){const{WhereClause:o}=await r.e(1534).then(r.bind(r,41534));return o.create(e,t)}function s(e,t){return(0,o.pC)(e)?(0,o.pC)(t)?`(${e}) AND (${t})`:e:t}},72762:(e,t,r)=>{r.d(t,{t:()=>c});var o=r(43697),n=r(15923),s=r(61247),i=r(70586),a=r(17445),p=r(1654),u=r(5600),l=r(52011);let c=class extends n.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new s.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,a.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,a.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:o=!1}={}){const n=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:o?e=>t({added:[],removed:e.toArray()}):void 0})],n),{remove:()=>this._handles.remove(n)}}addPromise(e){if((0,i.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},o){const n=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,n);const s=o(e,t,r);return this._handles.add(s,n),{remove:()=>this._handles.remove(n)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),o=(0,a.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(o,t),o}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,p.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,o._)([(0,u.Cb)({readOnly:!0})],c.prototype,"updating",void 0),c=(0,o._)([(0,l.j)("esri.core.support.WatchUpdatingTracking")],c);const d=-42},58995:(e,t,r)=>{r.d(t,{rS:()=>u});var o=r(2109),n=r(82971),s=r(8744);const i=new n.Z(o.kU),a=new n.Z(o.JL),p=new n.Z(o.mM);function u(e){return e&&((0,s.BZ)(e)||(0,s.fS)(e,a))?a:e&&((0,s.V2)(e)||(0,s.fS)(e,p))?p:i}new n.Z(o.pn)},62073:(e,t,r)=>{r(22021),r(17896);var o,n,s=r(65617);r(29268),r(61277),r(12981),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(0,s.c)(),(n=o||(o={}))[n.NONE=0]="NONE",n[n.CLAMP=1]="CLAMP",n[n.INFINITE_MIN=4]="INFINITE_MIN",n[n.INFINITE_MAX=8]="INFINITE_MAX",o.INFINITE_MIN,o.INFINITE_MAX,o.INFINITE_MAX},70082:(e,t,r)=>{r.d(t,{Z:()=>c});var o=r(43697),n=r(2368),s=r(35454),i=r(96674),a=r(5600),p=(r(75215),r(67676),r(52011));const u=new s.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let l=class extends((0,n.J)(i.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,o._)([(0,a.Cb)({json:{write:!0}})],l.prototype,"name",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],l.prototype,"description",void 0),(0,o._)([(0,a.Cb)({json:{read:u.read,write:u.write}})],l.prototype,"drawingTool",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],l.prototype,"prototype",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],l.prototype,"thumbnail",void 0),l=(0,o._)([(0,p.j)("esri.layers.support.FeatureTemplate")],l);const c=l},16451:(e,t,r)=>{r.d(t,{Z:()=>y});var o=r(43697),n=r(2368),s=r(96674),i=r(5600),a=(r(75215),r(67676),r(71715)),p=r(52011),u=r(30556),l=r(72729),c=r(70082);let d=class extends((0,n.J)(s.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const r of Object.keys(e))t[r]=(0,l.im)(e[r]);return t}writeDomains(e,t){const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,o._)([(0,i.Cb)({json:{write:!0}})],d.prototype,"id",void 0),(0,o._)([(0,i.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,o._)([(0,i.Cb)({json:{write:!0}})],d.prototype,"domains",void 0),(0,o._)([(0,a.r)("domains")],d.prototype,"readDomains",null),(0,o._)([(0,u.c)("domains")],d.prototype,"writeDomains",null),(0,o._)([(0,i.Cb)({type:[c.Z],json:{write:!0}})],d.prototype,"templates",void 0),d=(0,o._)([(0,p.j)("esri.layers.support.FeatureType")],d);const y=d},14147:(e,t,r)=>{r.d(t,{W:()=>l});var o=r(40330),n=r(3172),s=r(70586),i=r(95330),a=r(19238),p=r(65587),u=r(15235);class l{constructor(e,t,r,o){this._parsedUrl=e,this._portalItem=t,this._apiKey=r,this.signal=o,this._rootDocument=null;const n=this._parsedUrl?.path.match(/^(.*)\/SceneServer\/layers\/([\d]*)\/?$/i);n&&(this._urlParts={root:n[1],layerId:parseInt(n[2],10)})}async fetch(){if(!this._urlParts)return null;const e=this._portalItem??await this._portalItemFromServiceItemId();if((0,s.Wi)(e))return this._loadFromUrl();const t=await this._findAndLoadRelatedPortalItem(e);return(0,s.Wi)(t)?null:this._loadFeatureLayerFromPortalItem(t)}async fetchPortalItem(){if(!this._urlParts)return null;const e=this._portalItem??await this._portalItemFromServiceItemId();return(0,s.Wi)(e)?null:this._findAndLoadRelatedPortalItem(e)}async _fetchRootDocument(){if((0,s.pC)(this._rootDocument))return this._rootDocument;if((0,s.Wi)(this._urlParts))return this._rootDocument={},{};const e={query:{f:"json",token:this._apiKey},responseType:"json",signal:this.signal},t=`${this._urlParts.root}/SceneServer`;try{const r=await(0,n.default)(t,e);this._rootDocument=r.data}catch{this._rootDocument={}}return this._rootDocument}async _fetchServiceOwningPortalUrl(){const e=this._parsedUrl?.path,t=e?o.id?.findServerInfo(e):null;if(t?.owningSystemUrl)return t.owningSystemUrl;const r=e?e.replace(/(.*\/rest)\/.*/i,"$1")+"/info":null;try{const e=(await(0,n.default)(r,{query:{f:"json"},responseType:"json",signal:this.signal})).data.owningSystemUrl;if(e)return e}catch(e){(0,i.r9)(e)}return null}async _findAndLoadRelatedPortalItem(e){try{return(await e.fetchRelatedItems({relationshipType:"Service2Service",direction:"reverse"},{signal:this.signal})).find((e=>"Feature Service"===e.type))||null}catch(e){return(0,i.r9)(e),null}}async _loadFeatureLayerFromPortalItem(e){await e.load({signal:this.signal});const t=await this._findMatchingAssociatedSublayerUrl(e.url??"");return new a.default({url:t,portalItem:e}).load({signal:this.signal})}async _loadFromUrl(){const e=await this._findMatchingAssociatedSublayerUrl(`${this._urlParts?.root}/FeatureServer`);return new a.default({url:e}).load({signal:this.signal})}async _findMatchingAssociatedSublayerUrl(e){const t=e.replace(/^(.*FeatureServer)(\/[\d]*\/?)?$/i,"$1"),r={query:{f:"json"},responseType:"json",authMode:"no-prompt",signal:this.signal},o=this._urlParts?.layerId,s=this._fetchRootDocument(),i=(0,n.default)(t,r),[a,p]=await Promise.all([i,s]),u=p&&p.layers,l=a.data&&a.data.layers;if(!Array.isArray(l))throw new Error("expected layers array");if(Array.isArray(u)){for(let e=0;e<Math.min(u.length,l.length);e++)if(u[e].id===o)return`${t}/${l[e].id}`}else if(null!=o&&o<l.length)return`${t}/${l[o].id}`;throw new Error("could not find matching associated sublayer")}async _portalItemFromServiceItemId(){const e=(await this._fetchRootDocument()).serviceItemId;if(!e)return null;const t=new u.default({id:e,apiKey:this._apiKey}),r=await this._fetchServiceOwningPortalUrl();(0,s.pC)(r)&&(t.portal=new p.Z({url:r}));try{return t.load({signal:this.signal})}catch(e){return(0,i.r9)(e),null}}}},51161:(e,t,r)=>{r.d(t,{H3:()=>g,QI:()=>c,U4:()=>p,Yh:()=>y});var o=r(43697),n=r(96674),s=r(5600),i=(r(75215),r(67676),r(36030)),a=r(52011);let p=class extends n.wq{constructor(){super(...arguments),this.nodesPerPage=null,this.rootIndex=0,this.lodSelectionMetricType=null}};(0,o._)([(0,s.Cb)({type:Number})],p.prototype,"nodesPerPage",void 0),(0,o._)([(0,s.Cb)({type:Number})],p.prototype,"rootIndex",void 0),(0,o._)([(0,s.Cb)({type:String})],p.prototype,"lodSelectionMetricType",void 0),p=(0,o._)([(0,a.j)("esri.layer.support.I3SNodePageDefinition")],p);let u=class extends n.wq{constructor(){super(...arguments),this.factor=1}};(0,o._)([(0,s.Cb)({type:Number,json:{read:{source:"textureSetDefinitionId"}}})],u.prototype,"id",void 0),(0,o._)([(0,s.Cb)({type:Number})],u.prototype,"factor",void 0),u=(0,o._)([(0,a.j)("esri.layer.support.I3SMaterialTexture")],u);let l=class extends n.wq{constructor(){super(...arguments),this.baseColorFactor=[1,1,1,1],this.baseColorTexture=null,this.metallicRoughnessTexture=null,this.metallicFactor=1,this.roughnessFactor=1}};(0,o._)([(0,s.Cb)({type:[Number]})],l.prototype,"baseColorFactor",void 0),(0,o._)([(0,s.Cb)({type:u})],l.prototype,"baseColorTexture",void 0),(0,o._)([(0,s.Cb)({type:u})],l.prototype,"metallicRoughnessTexture",void 0),(0,o._)([(0,s.Cb)({type:Number})],l.prototype,"metallicFactor",void 0),(0,o._)([(0,s.Cb)({type:Number})],l.prototype,"roughnessFactor",void 0),l=(0,o._)([(0,a.j)("esri.layer.support.I3SMaterialPBRMetallicRoughness")],l);let c=class extends n.wq{constructor(){super(...arguments),this.alphaMode="opaque",this.alphaCutoff=.25,this.doubleSided=!1,this.cullFace="none",this.normalTexture=null,this.occlusionTexture=null,this.emissiveTexture=null,this.emissiveFactor=null,this.pbrMetallicRoughness=null}};(0,o._)([(0,i.J)({opaque:"opaque",mask:"mask",blend:"blend"})],c.prototype,"alphaMode",void 0),(0,o._)([(0,s.Cb)({type:Number})],c.prototype,"alphaCutoff",void 0),(0,o._)([(0,s.Cb)({type:Boolean})],c.prototype,"doubleSided",void 0),(0,o._)([(0,i.J)({none:"none",back:"back",front:"front"})],c.prototype,"cullFace",void 0),(0,o._)([(0,s.Cb)({type:u})],c.prototype,"normalTexture",void 0),(0,o._)([(0,s.Cb)({type:u})],c.prototype,"occlusionTexture",void 0),(0,o._)([(0,s.Cb)({type:u})],c.prototype,"emissiveTexture",void 0),(0,o._)([(0,s.Cb)({type:[Number]})],c.prototype,"emissiveFactor",void 0),(0,o._)([(0,s.Cb)({type:l})],c.prototype,"pbrMetallicRoughness",void 0),c=(0,o._)([(0,a.j)("esri.layer.support.I3SMaterialDefinition")],c);let d=class extends n.wq{};(0,o._)([(0,s.Cb)({type:String,json:{read:{source:["name","index"],reader:(e,t)=>null!=e?e:`${t.index}`}}})],d.prototype,"name",void 0),(0,o._)([(0,i.J)({jpg:"jpg",png:"png",dds:"dds","ktx-etc2":"ktx-etc2",ktx2:"ktx2",basis:"basis"})],d.prototype,"format",void 0),d=(0,o._)([(0,a.j)("esri.layer.support.I3STextureFormat")],d);let y=class extends n.wq{constructor(){super(...arguments),this.atlas=!1}};(0,o._)([(0,s.Cb)({type:[d]})],y.prototype,"formats",void 0),(0,o._)([(0,s.Cb)({type:Boolean})],y.prototype,"atlas",void 0),y=(0,o._)([(0,a.j)("esri.layer.support.I3STextureSetDefinition")],y);let h=class extends n.wq{};(0,o._)([(0,i.J)({Float32:"Float32",UInt64:"UInt64",UInt32:"UInt32",UInt16:"UInt16",UInt8:"UInt8"})],h.prototype,"type",void 0),(0,o._)([(0,s.Cb)({type:Number})],h.prototype,"component",void 0),h=(0,o._)([(0,a.j)("esri.layer.support.I3SGeometryAttribute")],h);let f=class extends n.wq{};(0,o._)([(0,i.J)({draco:"draco"})],f.prototype,"encoding",void 0),(0,o._)([(0,s.Cb)({type:[String]})],f.prototype,"attributes",void 0),f=(0,o._)([(0,a.j)("esri.layer.support.I3SGeometryCompressedAttributes")],f);let m=class extends n.wq{constructor(){super(...arguments),this.offset=0}};(0,o._)([(0,s.Cb)({type:Number})],m.prototype,"offset",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"position",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"normal",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"uv0",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"color",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"uvRegion",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"featureId",void 0),(0,o._)([(0,s.Cb)({type:h})],m.prototype,"faceRange",void 0),(0,o._)([(0,s.Cb)({type:f})],m.prototype,"compressedAttributes",void 0),m=(0,o._)([(0,a.j)("esri.layer.support.I3SGeometryBuffer")],m);let g=class extends n.wq{};(0,o._)([(0,i.J)({triangle:"triangle"})],g.prototype,"topology",void 0),(0,o._)([(0,s.Cb)()],g.prototype,"geometryBuffers",void 0),g=(0,o._)([(0,a.j)("esri.layer.support.I3SGeometryDefinition")],g)},56765:(e,t,r)=>{r.d(t,{Z:()=>l});var o,n=r(43697),s=r(46791),i=r(96674),a=r(5600),p=(r(75215),r(67676),r(52011));let u=o=class extends i.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new s.Z}clone(){return new o({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,n._)([(0,a.Cb)({type:String,json:{write:!0}})],u.prototype,"floorField",void 0),(0,n._)([(0,a.Cb)({json:{read:!1,write:!1}})],u.prototype,"viewAllMode",void 0),(0,n._)([(0,a.Cb)({json:{read:!1,write:!1}})],u.prototype,"viewAllLevelIds",void 0),u=o=(0,n._)([(0,p.j)("esri.layers.support.LayerFloorInfo")],u);const l=u},15506:(e,t,r)=>{r.d(t,{C:()=>o});const o={analytics:{supportsCacheHint:!1},attachment:{supportsContentType:!1,supportsExifInfo:!1,supportsKeywords:!1,supportsName:!1,supportsSize:!1,supportsCacheHint:!1,supportsResize:!1},data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:!1},editing:{supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsGeometryUpdate:!1,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:!1,supportsDelete:!1,supportsEditing:!1,supportsChangeTracking:!1,supportsQuery:!1,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:!1,supportsExceedsLimitStatistics:!1},queryRelated:{supportsCount:!1,supportsOrderBy:!1,supportsPagination:!1,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},query:{maxRecordCount:0,maxRecordCountFactor:0,standardMaxRecordCount:0,supportsCacheHint:!1,supportsCentroid:!1,supportsCompactGeometry:!1,supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsDisjointSpatialRelationship:!1,supportsDistance:!1,supportsDistinct:!1,supportsExtent:!1,supportsFormatPBF:!1,supportsGeometryProperties:!1,supportsHavingClause:!1,supportsHistoricMoment:!1,supportsMaxRecordCountFactor:!1,supportsOrderBy:!1,supportsPagination:!1,supportsPercentileStatistics:!1,supportsQuantization:!1,supportsQuantizationEditMode:!1,supportsQueryByOthers:!1,supportsQueryGeometry:!1,supportsResultType:!1,supportsSqlExpression:!1,supportsStandardizedQueriesOnly:!1,supportsTopFeaturesQuery:!1,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsStatistics:!1,tileMaxRecordCount:0}}},72064:(e,t,r)=>{r.d(t,{h:()=>c});var o=r(80442),n=r(70586),s=r(66677);const i={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function a(e,t,r){return!!(e&&e.hasOwnProperty(t)?e[t]:r)}function p(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function u(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function l(e,t){const r=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!r?.includes(t.toLowerCase())}function c(e,t){return{analytics:d(e),attachment:y(e),data:h(e),metadata:f(e),operations:m(e.capabilities,e,t),query:g(e,t),queryRelated:w(e),queryTopFeatures:b(e),editing:C(e)}}function d(e){return{supportsCacheHint:l(e.advancedQueryCapabilities,"queryAnalytics")}}function y(e){const t=e.attachmentProperties,r={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:l(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:a(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=i[e.name];t&&(r[t]=!!e.isEnabled)})),r}function h(e){return{isVersioned:a(e,"isDataVersioned",!1),supportsAttachment:a(e,"hasAttachments",!1),supportsM:a(e,"hasM",!1),supportsZ:a(e,"hasZ",!1)}}function f(e){return{supportsAdvancedFieldProperties:a(e,"supportsFieldDescriptionProperty",!1)}}function m(e,t,r){const o=e?e.toLowerCase().split(",").map((e=>e.trim())):[],i=r?(0,s.Qc)(r):null,p=o.includes((0,n.pC)(i)&&"MapServer"===i.serverType?"data":"query"),u=o.includes("editing")&&!t.datesInUnknownTimezone;let l=u&&o.includes("create"),c=u&&o.includes("delete"),d=u&&o.includes("update");const y=o.includes("changetracking"),h=t.advancedQueryCapabilities;return u&&!(l||c||d)&&(l=c=d=!0),{supportsCalculate:a(t,"supportsCalculate",!1),supportsTruncate:a(t,"supportsTruncate",!1),supportsValidateSql:a(t,"supportsValidateSql",!1),supportsAdd:l,supportsDelete:c,supportsEditing:u,supportsChangeTracking:y,supportsQuery:p,supportsQueryAnalytics:a(h,"supportsQueryAnalytic",!1),supportsQueryAttachments:a(h,"supportsQueryAttachments",!1),supportsQueryTopFeatures:a(h,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:a(t,"supportsAttachmentsResizing",!1),supportsSync:o.includes("sync"),supportsUpdate:d,supportsExceedsLimitStatistics:a(t,"supportsExceedsLimitStatistics",!1)}}function g(e,t){const r=e.advancedQueryCapabilities,n=e.ownershipBasedAccessControlForFeatures,i=e.archivingInfo,c=e.currentVersion,d=t?.includes("MapServer"),y=!d||c>=(0,o.Z)("mapserver-pbf-version-support"),h=(0,s.M8)(t),f=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:a(r,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:a(r,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:a(r,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:u(r),supportsCentroid:a(r,"supportsReturningGeometryCentroid",!1),supportsDistance:a(r,"supportsQueryWithDistance",!1),supportsDistinct:a(r,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:a(r,"supportsReturningQueryExtent",!1),supportsGeometryProperties:a(r,"supportsReturningGeometryProperties",!1),supportsHavingClause:a(r,"supportsHavingClause",!1),supportsOrderBy:a(r,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:a(r,"supportsPagination",!1),supportsQuantization:a(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:a(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:a(e,"supportsReturningQueryGeometry",!1),supportsResultType:a(r,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:a(r,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:a(r,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:a(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:a(r,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:a(n,"allowOthersToQuery",!0),supportsHistoricMoment:a(i,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:y&&f.has("pbf"),supportsDisjointSpatialRelationship:a(r,"supportsDisjointSpatialRel",!1),supportsCacheHint:a(r,"supportsQueryWithCacheHint",!1)||l(r,"query"),supportsDefaultSpatialReference:a(r,"supportsDefaultSR",!1),supportsCompactGeometry:h,supportsFullTextSearch:a(r,"supportsFullTextSearch",!1),maxRecordCountFactor:p(e,"maxRecordCountFactor",void 0),maxRecordCount:p(e,"maxRecordCount",void 0),standardMaxRecordCount:p(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:p(e,"tileMaxRecordCount",void 0)}}function w(e){const t=e.advancedQueryCapabilities,r=a(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:a(t,"supportsQueryRelatedPagination",!1),supportsCount:r,supportsOrderBy:r,supportsCacheHint:l(t,"queryRelated")}}function b(e){return{supportsCacheHint:l(e.advancedQueryCapabilities,"queryTopFilter")}}function C(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:a(e,"allowGeometryUpdates",!0),supportsGlobalId:a(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:a(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:a(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:a(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:a(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:a(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:a(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:a(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:a(t,"allowOthersToUpdate",!0)}}},51706:(e,t,r)=>{var o,n;function s(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function i(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?o.Expression:e.field&&"string"==typeof e.field?o.Field:o.Unknown}function p(e,t){const r=t||a(e),s=e.valueUnit||"unknown";return r===o.Unknown?n.Constant:e.stops?n.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?n.ClampedLinear:"unknown"===s?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?n.Proportional:n.Additive:n.Identity:n.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>p,RY:()=>o,hL:()=>n,iY:()=>s,qh:()=>i}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(o||(o={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(n||(n={}))},28694:(e,t,r)=>{r.d(t,{p:()=>s});var o=r(70586),n=r(69285);function s(e,t,r){if(!r||!r.features||!r.hasZ)return;const s=(0,n.k)(r.geometryType,t,e.outSpatialReference);if(!(0,o.Wi)(s))for(const e of r.features)s(e.geometry)}},56545:(e,t,r)=>{r.d(t,{Z:()=>d});var o,n=r(43697),s=r(96674),i=r(22974),a=r(5600),p=r(75215),u=r(52011),l=r(30556);let c=o=class extends s.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new o((0,i.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,n._)([(0,a.Cb)({type:[String],json:{write:!0}})],c.prototype,"attachmentTypes",void 0),(0,n._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],c.prototype,"attachmentsWhere",void 0),(0,n._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],c.prototype,"cacheHint",void 0),(0,n._)([(0,a.Cb)({type:[String],json:{write:!0}})],c.prototype,"keywords",void 0),(0,n._)([(0,a.Cb)({type:[Number],json:{write:!0}})],c.prototype,"globalIds",void 0),(0,n._)([(0,a.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],c.prototype,"num",void 0),(0,n._)([(0,a.Cb)({type:[Number],json:{write:!0}})],c.prototype,"objectIds",void 0),(0,n._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],c.prototype,"returnMetadata",void 0),(0,n._)([(0,a.Cb)({type:[Number],json:{write:!0}})],c.prototype,"size",void 0),(0,n._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],c.prototype,"start",void 0),(0,n._)([(0,l.c)("start"),(0,l.c)("num")],c.prototype,"writeStart",null),(0,n._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],c.prototype,"where",void 0),c=o=(0,n._)([(0,u.j)("esri.rest.support.AttachmentQuery")],c),c.from=(0,p.se)(c);const d=c},74889:(e,t,r)=>{r.d(t,{Z:()=>C});var o,n=r(43697),s=r(66577),i=r(38171),a=r(35454),p=r(96674),u=r(22974),l=r(70586),c=r(5600),d=(r(75215),r(71715)),y=r(52011),h=r(30556),f=r(82971),m=r(33955),g=r(1231);const w=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let b=o=class extends p.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=f.Z.fromJSON(t.spatialReference),o=[];for(let t=0;t<e.length;t++){const n=e[t],s=i.Z.fromJSON(n),a=n.geometry&&n.geometry.spatialReference;(0,l.pC)(s.geometry)&&!a&&(s.geometry.spatialReference=r);const p=n.aggregateGeometries,u=s.aggregateGeometries;if(p&&(0,l.pC)(u))for(const e in u){const t=u[e],o=p[e]?.spatialReference;(0,l.pC)(t)&&!o&&(t.spatialReference=r)}o.push(s)}return o}writeGeometryType(e,t,r,o){if(e)return void w.write(e,t,r,o);const{features:n}=this;if(n)for(const e of n)if(e&&(0,l.pC)(e.geometry))return void w.write(e.geometry.type,t,r,o)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,o=(0,m.im)(e);return o&&!r&&t.spatialReference&&(o.spatialReference=f.Z.fromJSON(t.spatialReference)),o}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,l.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new o(this.cloneProperties())}cloneProperties(){return(0,u.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const o=t.features[r];if(o.geometry){const t=e&&e[r];o.geometry=t&&t.toJSON()||o.geometry}}return t}quantize(e){const{scale:[t,r],translate:[o,n]}=e,s=this.features,i=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-o)/t)),(e=>Math.round((n-e)/r)));for(let e=0,t=s.length;e<t;e++)i?.((0,l.Wg)(s[e].geometry))||(s.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[o,n],scale:[s,i]}=r,a=this._getHydrationFunction(e,(e=>e*s+o),(e=>n-e*i));for(const{geometry:e}of t)(0,l.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,r){let o,n;const s=[];for(let i=0,a=e.length;i<a;i++){const a=e[i];if(i>0){const e=t(a[0]),i=r(a[1]);e===o&&i===n||(s.push([e-o,i-n]),o=e,n=i)}else o=t(a[0]),n=r(a[1]),s.push([o,n])}return s.length>0?s:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const o=(0,m.oU)(e)?e.rings:e.paths,n=[];for(let e=0,s=o.length;e<s;e++){const s=o[e],i=this._quantizePoints(s,t,r);i&&n.push(i)}return n.length>0?((0,m.oU)(e)?e.rings=n:e.paths=n,e):null}:"multipoint"===e?e=>{const o=this._quantizePoints(e.points,t,r);return o&&o.length>0?(e.points=o,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const o=(0,m.oU)(e)?e.rings:e.paths;let n,s;for(let e=0,i=o.length;e<i;e++){const i=o[e];for(let e=0,o=i.length;e<o;e++){const o=i[e];e>0?(n+=o[0],s+=o[1]):(n=o[0],s=o[1]),o[0]=t(n),o[1]=r(s)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const o=e.points;let n,s;for(let e=0,i=o.length;e<i;e++){const i=o[e];e>0?(n+=i[0],s+=i[1]):(n=i[0],s=i[1]),i[0]=t(n),i[1]=r(s)}}:null}};(0,n._)([(0,c.Cb)({type:String,json:{write:!0}})],b.prototype,"displayFieldName",void 0),(0,n._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"exceededTransferLimit",void 0),(0,n._)([(0,c.Cb)({type:[i.Z],json:{write:!0}})],b.prototype,"features",void 0),(0,n._)([(0,d.r)("features")],b.prototype,"readFeatures",null),(0,n._)([(0,c.Cb)({type:[g.Z],json:{write:!0}})],b.prototype,"fields",void 0),(0,n._)([(0,c.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:w.read}}})],b.prototype,"geometryType",void 0),(0,n._)([(0,h.c)("geometryType")],b.prototype,"writeGeometryType",null),(0,n._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasM",void 0),(0,n._)([(0,c.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],b.prototype,"hasZ",void 0),(0,n._)([(0,c.Cb)({types:s.qM,json:{write:!0}})],b.prototype,"queryGeometry",void 0),(0,n._)([(0,d.r)("queryGeometry")],b.prototype,"readQueryGeometry",null),(0,n._)([(0,c.Cb)({type:f.Z,json:{write:!0}})],b.prototype,"spatialReference",void 0),(0,n._)([(0,h.c)("spatialReference")],b.prototype,"writeSpatialReference",null),(0,n._)([(0,c.Cb)({json:{write:!0}})],b.prototype,"transform",void 0),b=o=(0,n._)([(0,y.j)("esri.rest.support.FeatureSet")],b),b.prototype.toJSON.isDefaultToJSON=!0;const C=b},58333:(e,t,r)=>{r.d(t,{ET:()=>s,I4:()=>n,eG:()=>p,lF:()=>i,lj:()=>l,qP:()=>a,wW:()=>u});const o=[252,146,31,255],n={type:"esriSMS",style:"esriSMSCircle",size:6,color:o,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},s={type:"esriSLS",style:"esriSLSSolid",width:.75,color:o},i={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},p={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},l={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}},91891:(e,t,r)=>{r.d(t,{I_:()=>d,W7:()=>f,qM:()=>g});var o=r(20102),n=r(22974),s=r(92604),i=r(25683),a=r(35065);const p=s.Z.getLogger("esri.views.3d.layers.i3s.I3SBinaryReader");function u(e,t,r){let n="",s=0;for(;s<r;){const i=e[t+s];if(i<128)n+=String.fromCharCode(i),s++;else if(i>=192&&i<224){if(s+1>=r)throw new o.Z("utf8-decode-error","UTF-8 Decode failed. Two byte character was truncated.");const a=(31&i)<<6|63&e[t+s+1];n+=String.fromCharCode(a),s+=2}else if(i>=224&&i<240){if(s+2>=r)throw new o.Z("utf8-decode-error","UTF-8 Decode failed. Multi byte character was truncated.");const a=(15&i)<<12|(63&e[t+s+1])<<6|63&e[t+s+2];n+=String.fromCharCode(a),s+=3}else{if(!(i>=240&&i<248))throw new o.Z("utf8-decode-error","UTF-8 Decode failed. Invalid multi byte sequence.");{if(s+3>=r)throw new o.Z("utf8-decode-error","UTF-8 Decode failed. Multi byte character was truncated.");const a=(7&i)<<18|(63&e[t+s+1])<<12|(63&e[t+s+2])<<6|63&e[t+s+3];if(a>=65536){const e=55296+(a-65536>>10),t=56320+(1023&a);n+=String.fromCharCode(e,t)}else n+=String.fromCharCode(a);s+=4}}}return n}function l(e,t){const r={byteOffset:0,byteCount:0,fields:Object.create(null)};let o=0;for(let n=0;n<t.length;n++){const s=t[n],i=s.valueType||s.type,a=b[i];r.fields[s.property]=a(e,o),o+=w[i].BYTES_PER_ELEMENT}return r.byteCount=o,r}function c(e,t,r){const n=[];let s,i,a=0;for(i=0;i<e;i+=1){if(s=t[i],s>0){if(n.push(u(r,a,s-1)),0!==r[a+s-1])throw new o.Z("string-array-error","Invalid string array: missing null termination.")}else n.push(null);a+=s}return n}function d(e,t){return new(0,w[t.valueType])(e,t.byteOffset,t.count*t.valuesPerElement)}function y(e,t,r){const s=null!=t.header?l(e,t.header):{byteOffset:0,byteCount:0,fields:{count:r}},i={header:s,byteOffset:s.byteCount,byteCount:0,entries:Object.create(null)};let a=s.byteCount;for(let e=0;e<t.ordering.length;e++){const r=t.ordering[e],p=(0,n.d9)(t[r]);if(p.count=s.fields.count??0,"String"===p.valueType){if(p.byteOffset=a,p.byteCount=s.fields[r+"ByteCount"],"UTF-8"!==p.encoding)throw new o.Z("unsupported-encoding","Unsupported String encoding.",{encoding:p.encoding});if(p.timeEncoding&&"ECMA_ISO8601"!==p.timeEncoding)throw new o.Z("unsupported-time-encoding","Unsupported time encoding.",{timeEncoding:p.timeEncoding})}else{if(!C(p.valueType))throw new o.Z("unsupported-value-type","Unsupported binary valueType",{valueType:p.valueType});{const e=v(p.valueType);a+=a%e!=0?e-a%e:0,p.byteOffset=a,p.byteCount=e*p.valuesPerElement*p.count}}a+=p.byteCount??0,i.entries[r]=p}return i.byteCount=a-i.byteOffset,i}function h(e,t,r){if(t!==e&&p.error(`Invalid ${r} buffer size\n expected: ${e}, actual: ${t})`),t<e)throw new o.Z("buffer-too-small","Binary buffer is too small",{expectedSize:e,actualSize:t})}function f(e,t){const r=l(e,t&&t.header);let o=r.byteCount;const n={isDraco:!1,header:r,byteOffset:r.byteCount,byteCount:0,vertexAttributes:{}},s=r.fields,i=null!=s.vertexCount?s.vertexCount:s.count;for(const e of t.ordering){if(!t.vertexAttributes[e])continue;const r={...t.vertexAttributes[e],byteOffset:o,count:i},s=m[e]?m[e]:"_"+e;n.vertexAttributes[s]=r,o+=v(r.valueType)*r.valuesPerElement*i}const a=s.faceCount;if(t.faces&&a){n.faces={};for(const e of t.ordering){if(!t.faces[e])continue;const r={...t.faces[e],byteOffset:o,count:a};n.faces[e]=r,o+=v(r.valueType)*r.valuesPerElement*a}}const p=s.featureCount;if(t.featureAttributes&&t.featureAttributeOrder&&p){n.featureAttributes={};for(const e of t.featureAttributeOrder){if(!t.featureAttributes[e])continue;const r={...t.featureAttributes[e],byteOffset:o,count:p};n.featureAttributes[e]=r,o+=("UInt64"===r.valueType?8:v(r.valueType))*r.valuesPerElement*p}}return h(o,e.byteLength,"geometry"),n.byteCount=o-n.byteOffset,n}const m={position:a.T.POSITION,normal:a.T.NORMAL,color:a.T.COLOR,uv0:a.T.UV0,region:a.T.UVREGION};function g(e,t,r){if("lepcc-rgb"===e.encoding)return(0,i.IT)(t);if("lepcc-intensity"===e.encoding)return(0,i.ti)(t);if(null!=e.encoding&&""!==e.encoding)throw new o.Z("unknown-attribute-storage-info-encoding","Unknown Attribute Storage Info Encoding");e["attributeByteCounts "]&&!e.attributeByteCounts&&(p.warn("Warning: Trailing space in 'attributeByteCounts '."),e.attributeByteCounts=e["attributeByteCounts "]),"ObjectIds"===e.ordering[0]&&e.hasOwnProperty("objectIds")&&(p.warn("Warning: Case error in objectIds"),e.ordering[0]="objectIds");const n=y(t,e,r);h(n.byteOffset+n.byteCount,t.byteLength,"attribute");const s=n.entries.attributeValues||n.entries.objectIds;if(s){if("String"===s.valueType){const e=n.entries.attributeByteCounts,r=d(t,e),o=function(e,t){return new Uint8Array(e,t.byteOffset,t.byteCount)}(t,s);return s.timeEncoding?function(e,t,r){return c(e,t,r).map((e=>{const t=e?Date.parse(e):null;return t&&!Number.isNaN(t)?t:null}))}(e.count,r,o):c(e.count,r,o)}return d(t,s)}throw new o.Z("bad-attribute-storage-info","Bad attributeStorageInfo specification.")}const w={Float32:Float32Array,Float64:Float64Array,UInt8:Uint8Array,Int8:Int8Array,UInt16:Uint16Array,Int16:Int16Array,UInt32:Uint32Array,Int32:Int32Array},b={Float32:(e,t)=>new DataView(e,0).getFloat32(t,!0),Float64:(e,t)=>new DataView(e,0).getFloat64(t,!0),UInt8:(e,t)=>new DataView(e,0).getUint8(t),Int8:(e,t)=>new DataView(e,0).getInt8(t),UInt16:(e,t)=>new DataView(e,0).getUint16(t,!0),Int16:(e,t)=>new DataView(e,0).getInt16(t,!0),UInt32:(e,t)=>new DataView(e,0).getUint32(t,!0),Int32:(e,t)=>new DataView(e,0).getInt32(t,!0)};function C(e){return w.hasOwnProperty(e)}function v(e){return C(e)?w[e].BYTES_PER_ELEMENT:0}},77397:(e,t,r)=>{r.d(t,{xe:()=>C});var o=r(3172),n=(r(67676),r(20102),r(80442),r(95330)),s=r(1533),i=(r(21787),r(46521)),a=(r(52138),r(13598)),p=(r(51305),r(37140)),u=(r(17896),r(65617)),l=(r(2109),r(44547),r(82971),r(58995),r(24470)),c=(r(8744),r(14165),r(91891));r(22303);var d,y,h=r(88669);(y=d||(d={}))[y.INVISIBLE=0]="INVISIBLE",y[y.TRANSPARENT=1]="TRANSPARENT",y[y.OPAQUE=2]="OPAQUE";(0,h.f)(0,0,0,.2),d.OPAQUE;(0,h.f)(0,0,0,.2),d.OPAQUE,r(6206);var f=r(94961),m=r(72119);r(98766),r(60437),r(62073),r(64822),(0,f.a)(),(0,u.c)(),(0,u.c)(),(0,h.c)(),(0,i.c)(),(()=>{const e=new Int8Array(162);let t=0;const r=r=>{for(let o=0;o<r.length;o++)e[t+o]=r[o];t+=6};r([6,2,3,1,5,4]),r([0,2,3,1,5,4]),r([0,2,3,7,5,4]),r([0,1,3,2,6,4]),r([0,1,3,2,0,0]),r([0,1,5,7,3,2]),r([0,1,3,7,6,4]),r([0,1,3,7,6,2]),r([0,1,5,7,6,2]),r([0,1,5,4,6,2]),r([0,1,5,4,0,0]),r([0,1,3,7,5,4]),r([0,2,6,4,0,0]),r([0,0,0,0,0,0]),r([1,3,7,5,0,0]),r([2,3,7,6,4,0]),r([2,3,7,6,0,0]),r([2,3,1,5,7,6]),r([0,1,5,7,6,2]),r([0,1,5,7,6,4]),r([0,1,3,7,6,4]),r([4,5,7,6,2,0]),r([4,5,7,6,0,0]),r([4,5,1,3,7,6]),r([0,2,3,7,5,4]),r([6,2,3,7,5,4]),r([6,2,3,1,5,4])})(),(0,u.c)(),(0,u.c)(),(0,u.c)(),(0,f.a)();var g,w,b=r(51417);function C(e,t,r,s,i){const a=[];for(const o of t)if(o&&i.includes(o.name)){const t=`${e}/nodes/${r}/attributes/${o.key}/0`;a.push({url:t,storageInfo:o})}return(0,n.as)(a.map((e=>(0,o.default)(e.url,{responseType:"array-buffer"}).then((t=>(0,c.qM)(e.storageInfo,t.data)))))).then((e=>{const t=[];for(const r of s){const o={};for(let t=0;t<e.length;t++){const n=e[t].value;null!=n&&(o[a[t].storageInfo.name]=v(n,r))}t.push(o)}return t}))}function v(e,t){if(!e)return null;const r=e[t];return(0,s.z3)(e)?-32768===r?null:r:(0,s.Hx)(e)?-2147483648===r?null:r:r!=r?null:r}(0,l.Ue)(),(w=g||(g={}))[w.OUTSIDE=0]="OUTSIDE",w[w.INTERSECTS_CENTER_OUTSIDE=1]="INTERSECTS_CENTER_OUTSIDE",w[w.INTERSECTS_CENTER_INSIDE=2]="INTERSECTS_CENTER_INSIDE",w[w.INSIDE=3]="INSIDE";const _=new Array(24);new b.a(_,3,!0),(0,u.c)(),(0,u.c)(),(0,i.c)(),(0,a.c)(),(0,p.c)(),(0,l.Ue)(),(0,l.Ue)(),function(e=[0,0,0],t=[-1,-1,-1],r=(0,f.a)()){(0,u.a)(e),(0,m.b)(t),(0,p.b)(r)}(),(0,u.c)(),new Array(72),(0,a.c)()},25683:(e,t,r)=>{r.d(t,{Gi:()=>a,IT:()=>c,ti:()=>y});var o=r(20102);const n=!0;function s(e,t,r){return{identifier:String.fromCharCode.apply(null,new Uint8Array(e,r+0,10)),version:t.getUint16(r+10,n),checksum:t.getUint32(r+12,n)}}function i(e,t){return{sizeLo:e.getUint32(t+0,n),sizeHi:e.getUint32(t+4,n),minX:e.getFloat64(t+8,n),minY:e.getFloat64(t+16,n),minZ:e.getFloat64(t+24,n),maxX:e.getFloat64(t+32,n),maxY:e.getFloat64(t+40,n),maxZ:e.getFloat64(t+48,n),errorX:e.getFloat64(t+56,n),errorY:e.getFloat64(t+64,n),errorZ:e.getFloat64(t+72,n),count:e.getUint32(t+80,n),reserved:e.getUint32(t+84,n)}}function a(e){const t=new DataView(e,0);let r=0;const{identifier:n,version:a}=s(e,t,r);if(r+=16,"LEPCC     "!==n)throw new o.Z("lepcc-decode-error","Bad identifier");if(a>1)throw new o.Z("lepcc-decode-error","Unknown version");const u=i(t,r);if(r+=88,u.sizeHi*2**32+u.sizeLo!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad size");const l=new Float64Array(3*u.count),c=[],d=[],y=[],h=[];if(r=p(e,r,c),r=p(e,r,d),r=p(e,r,y),r=p(e,r,h),r!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad length");let f=0,m=0;for(let e=0;e<c.length;e++){m+=c[e];let t=0;for(let r=0;r<d[e];r++){t+=y[f];const e=h[f];l[3*f]=Math.min(u.maxX,u.minX+2*u.errorX*t),l[3*f+1]=Math.min(u.maxY,u.minY+2*u.errorY*m),l[3*f+2]=Math.min(u.maxZ,u.minZ+2*u.errorZ*e),f++}}return{errorX:u.errorX,errorY:u.errorY,errorZ:u.errorZ,result:l}}function p(e,t,r){const o=[];t=u(e,t,o);const n=[];for(let s=0;s<o.length;s++){n.length=0,t=u(e,t,n);for(let e=0;e<n.length;e++)r.push(n[e]+o[s])}return t}function u(e,t,r){const s=new DataView(e,t),i=s.getUint8(0),a=31&i,p=!!(32&i),u=(192&i)>>6;let l=0;if(0===u)l=s.getUint32(1,n),t+=5;else if(1===u)l=s.getUint16(1,n),t+=3;else{if(2!==u)throw new o.Z("lepcc-decode-error","Bad count type");l=s.getUint8(1),t+=2}if(p)throw new o.Z("lepcc-decode-error","LUT not implemented");const c=Math.ceil(l*a/8),d=new Uint8Array(e,t,c);let y=0,h=0,f=0;const m=-1>>>32-a;for(let e=0;e<l;e++){for(;h<a;)y|=d[f]<<h,h+=8,f+=1;r[e]=y&m,y>>>=a,h-=a,h+a>32&&(y|=d[f-1]>>8-h)}return t+f}function l(e,t){return{sizeLo:e.getUint32(t+0,n),sizeHi:e.getUint32(t+4,n),count:e.getUint32(t+8,n),colorMapCount:e.getUint16(t+12,n),lookupMethod:e.getUint8(t+14),compressionMethod:e.getUint8(t+15)}}function c(e){const t=new DataView(e,0);let r=0;const{identifier:n,version:i}=s(e,t,r);if(r+=16,"ClusterRGB"!==n)throw new o.Z("lepcc-decode-error","Bad identifier");if(i>1)throw new o.Z("lepcc-decode-error","Unknown version");const a=l(t,r);if(r+=16,a.sizeHi*2**32+a.sizeLo!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad size");if((2===a.lookupMethod||1===a.lookupMethod)&&0===a.compressionMethod){if(3*a.colorMapCount+a.count+r!==e.byteLength||a.colorMapCount>256)throw new o.Z("lepcc-decode-error","Bad count");const t=new Uint8Array(e,r,3*a.colorMapCount),n=new Uint8Array(e,r+3*a.colorMapCount,a.count),s=new Uint8Array(3*a.count);for(let e=0;e<a.count;e++){const r=n[e];s[3*e]=t[3*r],s[3*e+1]=t[3*r+1],s[3*e+2]=t[3*r+2]}return s}if(0===a.lookupMethod&&0===a.compressionMethod){if(3*a.count+r!==e.byteLength||0!==a.colorMapCount)throw new o.Z("lepcc-decode-error","Bad count");return new Uint8Array(e,r).slice()}if(a.lookupMethod<=2&&1===a.compressionMethod){if(r+3!==e.byteLength||1!==a.colorMapCount)throw new o.Z("lepcc-decode-error","Bad count");const n=t.getUint8(r),s=t.getUint8(r+1),i=t.getUint8(r+2),p=new Uint8Array(3*a.count);for(let e=0;e<a.count;e++)p[3*e]=n,p[3*e+1]=s,p[3*e+2]=i;return p}throw new o.Z("lepcc-decode-error","Bad method "+a.lookupMethod+","+a.compressionMethod)}function d(e,t){return{sizeLo:e.getUint32(t+0,n),sizeHi:e.getUint32(t+4,n),count:e.getUint32(t+8,n),scaleFactor:e.getUint16(t+12,n),bitsPerPoint:e.getUint8(t+14),reserved:e.getUint8(t+15)}}function y(e){const t=new DataView(e,0);let r=0;const{identifier:n,version:i}=s(e,t,r);if(r+=16,"Intensity "!==n)throw new o.Z("lepcc-decode-error","Bad identifier");if(i>1)throw new o.Z("lepcc-decode-error","Unknown version");const a=d(t,r);if(r+=16,a.sizeHi*2**32+a.sizeLo!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad size");const p=new Uint16Array(a.count);if(8===a.bitsPerPoint){if(a.count+r!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad size");const t=new Uint8Array(e,r,a.count);for(let e=0;e<a.count;e++)p[e]=t[e]*a.scaleFactor}else if(16===a.bitsPerPoint){if(2*a.count+r!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad size");const t=new Uint16Array(e,r,a.count);for(let e=0;e<a.count;e++)p[e]=t[e]*a.scaleFactor}else{const t=[];if(u(e,r,t)!==e.byteLength)throw new o.Z("lepcc-decode-error","Bad size");for(let e=0;e<a.count;e++)p[e]=t[e]*a.scaleFactor}return p}},19833:(e,t,r)=>{r.d(t,{V:()=>i,e:()=>s});var o=r(70586),n=r(35671);async function s(e,t=e.popupTemplate){if((0,o.Wi)(t))return[];const r=await t.getRequiredFields(e.fieldsIndex),{lastEditInfoEnabled:s}=t,{objectIdField:i,typeIdField:a,globalIdField:p,relationships:u}=e;if(r.includes("*"))return["*"];const l=s?await(0,n.CH)(e):[],c=(0,n.Q0)(e.fieldsIndex,[...r,...l]);return a&&c.push(a),c&&i&&e.fieldsIndex?.has(i)&&!c.includes(i)&&c.push(i),c&&p&&e.fieldsIndex?.has(p)&&!c.includes(p)&&c.push(p),u&&u.forEach((t=>{const{keyField:r}=t;c&&r&&e.fieldsIndex?.has(r)&&!c.includes(r)&&c.push(r)})),c}function i(e,t){return e.popupTemplate?e.popupTemplate:(0,o.pC)(t)&&t.defaultPopupTemplateEnabled&&(0,o.pC)(e.defaultPopupTemplate)?e.defaultPopupTemplate:null}}}]);