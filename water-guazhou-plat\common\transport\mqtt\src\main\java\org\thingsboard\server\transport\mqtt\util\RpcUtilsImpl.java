/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.transport.mqtt.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.UnpooledByteBufAllocator;
import io.netty.channel.Channel;
import io.netty.handler.codec.mqtt.MqttFixedHeader;
import io.netty.handler.codec.mqtt.MqttMessageType;
import io.netty.handler.codec.mqtt.MqttPublishMessage;
import io.netty.handler.codec.mqtt.MqttPublishVariableHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.rpc.ToDeviceRpcRequestBody;
import org.thingsboard.server.common.transport.RpcToFyService;
import org.thingsboard.server.transport.mqtt.MqttTopics;

import java.util.Random;

import static com.alibaba.fastjson.util.IOUtils.UTF8;
import static io.netty.handler.codec.mqtt.MqttQoS.AT_LEAST_ONCE;

/**
 * <AUTHOR>
 * @date 2020/1/17 10:41
 */
@Service
@Slf4j
public class RpcUtilsImpl implements RpcToFyService {
    private static final ByteBufAllocator ALLOCATOR = new UnpooledByteBufAllocator(false);

    @Override
    public boolean sendToDevice(String deviceSecret, String boxId, String body) {
        try {
            String sendTopic = DataConstants.TOPIC_FY_BASE + boxId + DataConstants.TOPIC_FY_SET_MSG;
            String flushData = DataConstants.TOPIC_FY_BASE + boxId + DataConstants.TOP_FLUSH_DATA;
            //先下发设定指令
            MqttFixedHeader mqttFixedHeader =
                    new MqttFixedHeader(MqttMessageType.PUBLISH, false, AT_LEAST_ONCE, false, 0);
            MqttPublishVariableHeader header = new MqttPublishVariableHeader(sendTopic, 0);
            ByteBuf payload = new UnpooledByteBufAllocator(false).buffer();
            payload.writeBytes(body.getBytes(UTF8));
            MqttPublishMessage message = new MqttPublishMessage(mqttFixedHeader, header, payload);
            DeviceSessionUtils.sessionMap.get(deviceSecret).writeAndFlush(message);
            Channel channel = DeviceSessionUtils.sessionMap.get(deviceSecret);
            if (channel == null) {
                log.info("session不存在！sessionId:" + deviceSecret);
                return false;
            }
            log.info("发送指令地址：" + sendTopic);
            log.info("发送数据" + body);
            //下发指令让盒子立即推送
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            MqttFixedHeader mqttFlushHeader =
                    new MqttFixedHeader(MqttMessageType.PUBLISH, false, AT_LEAST_ONCE, false, 0);
            MqttPublishVariableHeader flushHeader = new MqttPublishVariableHeader(flushData, 0);
            ByteBuf flushPayload = new UnpooledByteBufAllocator(false).buffer();
            flushPayload.writeBytes(body.getBytes(UTF8));
            MqttPublishMessage flushMessage = new MqttPublishMessage(mqttFixedHeader, flushHeader, flushPayload);
            DeviceSessionUtils.sessionMap.get(deviceSecret).writeAndFlush(flushMessage);
            return true;
        } catch (Exception e) {
            log.info("下发异常！" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean pushDataNow(String deviceSecret, String boxId, String body) {
        try {
            String flushData = DataConstants.TOPIC_FY_BASE + boxId + DataConstants.TOP_FLUSH_DATA;
            //先下发设定指令
            log.info("获取数据地址：" + flushData);
            log.info("发送数据" + body);
            //下发指令让盒子立即推送
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            MqttFixedHeader mqttFlushHeader =
                    new MqttFixedHeader(MqttMessageType.PUBLISH, false, AT_LEAST_ONCE, false, 0);
            MqttPublishVariableHeader flushHeader = new MqttPublishVariableHeader(flushData, 0);
            ByteBuf flushPayload = new UnpooledByteBufAllocator(false).buffer();
            flushPayload.writeBytes(body.getBytes(UTF8));
            MqttPublishMessage flushMessage = new MqttPublishMessage(mqttFlushHeader, flushHeader, flushPayload);
            DeviceSessionUtils.sessionMap.get(deviceSecret).writeAndFlush(flushMessage);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 发送组态，数据源数据到工控机
     * @param deviceSecret
     * @param body
     * @return
     */
    @Override
    public boolean sendModelToDevice(String deviceSecret, String body) {
        try {
            MqttFixedHeader mqttFixedHeader =
                    new MqttFixedHeader(MqttMessageType.PUBLISH, false, AT_LEAST_ONCE, false, 0);
            MqttPublishVariableHeader header = new MqttPublishVariableHeader(DataConstants.TOPIC_DATA_UPDATE, 1);
            ByteBuf payload = ALLOCATOR.buffer();
            payload.writeBytes(body.getBytes(UTF8));
            MqttPublishMessage flushMessage = new MqttPublishMessage(mqttFixedHeader, header, payload);
            DeviceSessionUtils.sessionMap.get(deviceSecret).writeAndFlush(flushMessage);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public Object sendToModbus(String deviceSecret, ToDeviceRpcRequestBody body,int requestId) {
        try {
            MqttFixedHeader mqttFixedHeader =
                    new MqttFixedHeader(MqttMessageType.PUBLISH, false, AT_LEAST_ONCE, false, 0);
            MqttPublishVariableHeader header = new MqttPublishVariableHeader(MqttTopics.DEVICE_RPC_REQUESTS_TOPIC+requestId,1);
            ByteBuf payload = ALLOCATOR.buffer();
            payload.writeBytes(toString(body).getBytes());
            MqttPublishMessage flushMessage = new MqttPublishMessage(mqttFixedHeader, header, payload);
            DeviceSessionUtils.sessionMap.get(deviceSecret).writeAndFlush(flushMessage);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    public static String toString(Object value) {
        try {
            return new ObjectMapper().writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("The given Json object value: "
                    + value + " cannot be transformed to a String");
        }
    }


//    @Override
//    public void processResponseToServerSideRPCRequestFromDeviceActor(FromDeviceRpcResponse response) {
//        log.trace("[{}] Received response to server-side RPC request from device actor.", response.getId());
//        UUID requestId = response.getId();
//        Consumer<FromDeviceRpcResponse> consumer = localToDeviceRpcRequests.remove(requestId);
//        if (consumer != null) {
//            consumer.accept(response);
//        } else {
//            log.trace("[{}] Unknown or stale rpc response received [{}]", requestId, response);
//        }
//    }
}
