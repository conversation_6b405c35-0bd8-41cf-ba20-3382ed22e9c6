<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionVisaMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        visa.id,
        visa.code,
        construction.code                                                  as construction_code,
        construction.name                                                  as construction_name,
        construction.type_id                                               as construction_type_id,
        (select name from so_general_type where id = construction.type_id) as construction_type_name,
        visa.budgeter,
        visa.construct_organization,
        combied_firstpart_organization                                     as firstpart_organization,
        combied_secondpart_organization                                    as secondpart_organization,
        combied_supervisor_organization                                    as combied_supervisor_organization,
        combied_construct_organization                                     as combied_construct_organization,
        contract_total_cost                                                as contract_total_cost,
        visa.address,
        visa.construct_time,
        visa.build_organization,
        visa.supervisor_organization,
        visa.audit_organization,
        info.status                                                        as status,
        visa.remark,
        visa.attachments,
        visa.creator,
        visa.create_time,
        construction.tenant_id
        <!--@sql from so_construction_visa visa,so_construction construction, so_construction_task_info info, so_construction_details details -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisa">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="budgeter" property="budgeter"/>
        <result column="construct_organization" property="constructOrganization"/>
        <result column="address" property="address"/>
        <result column="construct_time" property="constructTime"/>
        <result column="build_organization" property="buildOrganization"/>
        <result column="supervisor_organization" property="supervisorOrganization"/>
        <result column="audit_organization" property="auditOrganization"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="attachments" property="attachments"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="SoConstructionVisaContainer"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisaContainer">
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>

        <result column="firstpart_organization" property="firstpartOrganization"/>
        <result column="secondpart_organization" property="secondpartOrganization"/>
        <result column="combied_supervisor_organization" property="supervisorOrganization"/>
        <result column="combied_construct_organization" property="constructionOrganization"/>
        <result column="contract_total_cost" property="contractTotalCost"/>

        <result column="status" property="status"/>
        <collection column="id" property="items" resultMap="BaseResultMap"/>
    </resultMap>
    <select id="findByPage" resultMap="SoConstructionVisaContainer">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_CONTRACT"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_visa visa
                           on visa.construction_code = construction.code and visa.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id
                               and info.scope = #{scope}
                 left join so_construction_details details
                           on construction.code = details.current_construction_code and
                              construction.tenant_id = details.current_tenant_id
        where construction.code in (
        <include refid="pageInfoSubQuery"/>
        <if test="size > 0">
            offset #{offset} limit #{size}
        </if>
        )
          and construction.tenant_id = #{tenantId}
        order by construction.create_time desc
    </select>

    <select id="countByPage" resultType="long">
        select count(1) from (<include refid="pageInfoSubQuery"/>) a
    </select>

    <sql id="pageInfoSubQuery">
        select distinct construction.code
        from so_construction construction
                 left join so_construction_visa visa
                           on visa.construction_code = construction.code and visa.tenant_id = construction.tenant_id
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="firstPartOrganization != null and firstPartOrganization != ''">
                <!--@formatter:off-->
                and (select count(1) > 0 from so_construction_contract c
                where c.construction_code = visa.construction_code
                and c.firstpart_organization like '%' || #{firstPartOrganization} || '%')
                <!--@formatter:on-->
            </if>
            <if test="supervisorOrganization != null and supervisorOrganization != ''">
                and supervisor_organization like '%' || #{supervisorOrganization} || '%'
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
    </sql>

    <update id="update">
        update so_construction_visa
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="constructionCode != null">
                construction_code = #{constructionCode},
            </if>
            <if test="budgeter != null">
                budgeter = #{budgeter},
            </if>
            <if test="constructOrganization != null">
                construct_organization = #{constructOrganization},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="constructTime != null">
                construct_time = #{constructTime},
            </if>
            <if test="buildOrganization != null">
                build_organization = #{buildOrganization},
            </if>
            <if test="supervisorOrganization != null">
                supervisor_organization = #{supervisorOrganization},
            </if>
            <if test="auditOrganization != null">
                audit_organization = #{auditOrganization},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_visa
        set code                    = #{code},
            budgeter                = #{budgeter},
            construct_organization  = #{constructOrganization},
            address                 = #{address},
            construct_time          = #{constructTime},
            build_organization      = #{buildOrganization},
            supervisor_organization = #{supervisorOrganization},
            audit_organization      = #{auditOrganization},
            remark                  = #{remark},
            attachments             = #{attachments}
        where id = #{id}
    </update>

    <select id="isCodeExists" resultType="boolean">
        select count(1)
        from so_construction_visa outside
        where outside.code = #{code}
          and tenant_id = #{tenantId}
        <if test="id != null and id != ''">
            <!--需要存在，下方不存在是会返回null，null意味着false(不重复)，会出现假是自己(假不重复)-->
            and (select count(1) > 0 from so_construction_visa where id = #{id})
            <!--两个code相等则意味着是自己，不是自己则重复，是自己则放行(false-是自己所以返回未重复，true-不是自己所以返回重复)-->
            and (select not outside.code = code from so_construction_visa where id = #{id})
        </if>
    </select>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_visa
        where id = #{id}
    </select>
</mapper>