import{_ as re}from"./ArcView-DpMnCY82.js";import{_ as U}from"./Panel-DyoxrWMd.js";import{_ as le}from"./ArcStationWarning-BF9YrSzF.js";import{g as ie}from"./MapView-DaoQedLH.js";import{w as ce}from"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{m as pe,A as ue}from"./AnimatedLinesLayer-B2VbV4jv.js";import{d as P,ad as T,o as S,g as f,n as k,r as V,fV as me,q as i,F as B,i as y,cs as de,h as E,bo as _e,bR as fe,p as R,d3 as ge,cE as ye,C as z,c as x,Q as W,bA as Q,an as Z,_ as X,X as he,W as ve,b as J,bh as N,aK as be,aL as we,d7 as Ce,f as ke,bB as H,fW as Ae,ax as D}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{b as xe,c as Pe,e as Le,a as Te,_ as Y,u as ee,d as Me}from"./arcWidgetButton-0glIxrt7.js";import"./index-0NlGN6gS.js";import{u as j}from"./useWidgets-BRE-VQU9.js";import{u as Be}from"./useBasemapGallary-Bk4zNUJL.js";import{e as Se,s as $e,g as Oe}from"./FeatureHelper-Da16o0mu.js";import{g as te,a as Fe}from"./LayerHelper-Cn-iiqxI.js";import{P as Ve}from"./pipe-nogVzCHG.js";import{E as K}from"./StatisticsHelper-D-s_6AyQ.js";import{u as Ie}from"./useLayerList-DmEwJ-ws.js";/* empty css                        */import{I as Ge,q as qe}from"./utils-D5nxoMq3.js";import{a as De}from"./URLHelper-B9aplt5w.js";import{_ as Ee}from"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";const We={class:"wrapper"},Ne=P({__name:"ArcHome",setup(b,{expose:o}){const t=T("view"),e=xe();let n;return o({overrideGoto:u=>{n.goToOverride=()=>{u&&(t==null||t.goTo(u))}}}),S(()=>{t==null||t.when(()=>{n=e.init(t)})}),(u,L)=>(f(),k("div",We))}}),He={id:"overviewmap",class:"overviewmap"},Re=P({__name:"ArcOverview",setup(b){const{addCustomWidget:o}=j(),t=T("view"),e=V({showOverViewMap:!1,mounted:!1}),n=Pe();return S(()=>{t==null||t.when(()=>{n.init(t,"overviewmap"),o(t,"gis-overview","bottom-right")})}),me(()=>{e.showOverViewMap=!1}),(a,u)=>{const L=ye;return f(),k("div",{id:"gis-overview",class:"esri-widget esri-expand esri-component esri-widget--button custom-toolbar",title:"鹰眼图",onClick:u[0]||(u[0]=()=>y(e).showOverViewMap=!y(e).showOverViewMap)},[i(L,{size:16,class:"tool-icon"},{default:B(()=>[i(y(de),{icon:y(e).showOverViewMap?"ep:d-arrow-right":"mdi:earth"},null,8,["icon"])]),_:1}),(f(),E(ge,{to:"body"},[_e(R("div",He,null,512),[[fe,y(e).showOverViewMap]])]))])}}}),Ue=z(Re,[["__scopeId","data-v-3a1114a1"]]),ze=P({__name:"ArcBasemapGallary",props:{onBasemapChange:{type:Function}},setup(b){const o=b,t=T("view");return Be(o.onBasemapChange).init(t),(n,a)=>(f(),k("div"))}}),Ze=P({__name:"ArcLegend",setup(b){const o=T("view"),t=Le();return S(()=>{o==null||o.when(()=>{t.init(o,"bottom-right")})}),(e,n)=>(f(),k("div"))}}),je={class:"wrapper"},Je=P({__name:"ArcPrint",setup(b){const o=Te(),t=T("view");return S(()=>{t==null||t.when(()=>{o.init(t,"","bottom-right")})}),(e,n)=>(f(),k("div",je))}}),Ke=P({__name:"ArcAreaMeasure",setup(b){const o=x(),t=T("view"),e=te(t,{id:"measure-area",title:"面积测量"}),n={rings:[]},a=V({showOverViewMap:!1,toolPanelTitle:"",toolPanelOperate:"",measuring:!1}),u=x(),L=async(s,p,h,C)=>{var v,M;h?(M=o.value)==null||M.Close():(a.toolPanelOperate=s,a.toolPanelTitle=p,(v=o.value)==null||v.Open())},c=()=>{var s,p;e==null||e.removeAll(),(s=_.value)==null||s.cancel(),(p=u.value)==null||p.toggle(!0)},r=x(),w=V({labelWidth:60,group:[{fields:[{type:"text",field:"area",label:"面积",style:{marginRight:"12px"},extraFormItem:[{type:"select",field:"unit",width:"100px",options:[{label:"公顷",value:"hectares"},{label:"平方米",value:"square-meters"},{label:"平方公里",value:"square-kilometers"}],onChange:()=>m()}]},{type:"btn-group",btns:[{perm:!0,text:"新测量",type:"default",loading:()=>a.measuring,click:()=>I()}]}]}],gutter:12,defaultValue:{unit:"square-meters"}}),{initSketch:d,destroySketch:A,sketch:_}=ee(),m=async()=>{if(!(!n||!r.value)){a.measuring=!0;try{const s=Se(n.rings[0],r.value.dataForm.unit||"square-meters",t==null?void 0:t.spatialReference);r.value.dataForm.area=s.toFixed(2)}catch(s){console.dir(s)}a.measuring=!1}},g=s=>{var p;s.state==="complete"&&(n.rings=((p=s.graphics[0])==null?void 0:p.geometry).rings,m())},I=()=>{var s;e==null||e.removeAll(),(s=_.value)==null||s.create("polygon")},G=()=>{var s;q(t,"tool-areameasure"),e==null||e.removeAll(),A(),(s=o.value)==null||s.Close(),e&&(t==null||t.map.remove(e))},{addCustomWidget:O,removeCustomWidget:q}=j(),l=x();return S(()=>{l.value=!0,O(t,"tool-areameasure","bottom-right"),d(t,e,{updateCallBack:g,createCallBack:g})}),W(()=>{G()}),Q(()=>{l.value=!1}),(s,p)=>{const h=X,C=U;return f(),k("div",null,[i(Y,{id:"tool-areameasure",ref_key:"refBtn",ref:u,icon:"gis:measure-area-alt",title:"面积测量",onClick:p[0]||(p[0]=v=>L("areameasure","面积测量",v))},null,512),y(l)?(f(),E(C,{key:0,ref_key:"refToolPanel",ref:o,"custom-class":"tool-area-measure-panel",telport:"#arcmap-wrapper",title:y(a).toolPanelTitle,"destroy-by-close":!0,"before-close":c},{default:B(()=>[i(h,{ref_key:"refForm",ref:r,config:y(w)},null,8,["config"])]),_:1},8,["title"])):Z("",!0)])}}}),Qe=P({__name:"ArcLengthMeasure",setup(b){const o=T("view"),t=x(),e=x(),n=V({showOverViewMap:!1,toolPanelTitle:"",toolPanelOperate:"",measuring:!1}),a={},u=x(),L=V({group:[{fields:[{type:"select",clearable:!1,options:[],label:"管线图层",field:"pipeLayer"},{type:"text",field:"pipeLength",label:"管线长度",unit:"米"},{type:"btn-group",btns:[{perm:!0,text:"新测量",type:"default",loading:()=>n.measuring,click:()=>m()}]}]}],gutter:12}),c=async(l,s,p,h)=>{var C,v;p?(v=t.value)==null||v.Close():(n.toolPanelOperate=l,n.toolPanelTitle=s,(C=t.value)==null||C.Open())},r=()=>{var l,s,p;(l=a.graphicsLayer)==null||l.removeAll(),(s=a.sketch)==null||s.cancel(),(p=e.value)==null||p.toggle(!0)},{initSketch:w,destroySketch:d}=ee(),A=async()=>{var s,p,h,C;if(!a.queryGeometry)return;n.measuring=!0;const l=(s=u.value)==null?void 0:s.dataForm.pipeLayer;try{const v=await Ve({usertoken:ve().gToken,layerids:JSON.stringify(l!==void 0?[l]:[]),group_fields:JSON.stringify([]),statistic_field:K.ShapeLen,statistic_type:"2",where:"1=1",geometry:a.queryGeometry,f:"pjson"});if(v.data.code===1e4){const M=(h=(p=v.data)==null?void 0:p.result)==null?void 0:h.rows,F=M.length&&((C=M[0].rows)==null?void 0:C.length)&&M[0].rows[0];u.value&&(u.value.dataForm.pipeLength=F&&F[K.ShapeLen])}else J.error("统计失败"),u.value&&(u.value.dataForm.pipeLength=void 0)}catch(v){console.dir(v),J.error("统计失败")}n.measuring=!1},_=l=>{var s;l.state==="complete"&&(a.queryGeometry=(s=l.graphics[0])==null?void 0:s.geometry,A())},m=()=>{var l,s;(l=a.graphicsLayer)==null||l.removeAll(),(s=a.sketch)==null||s.create("polygon")},g=async()=>{var v,M,F;if(!o)return;const l=Fe(o),p=((M=(v=(await he(l)).data)==null?void 0:v.result)==null?void 0:M.rows)||[],h=[];p.map($=>{($.geometrytype==="esriGeometryPolyline"||$.layername.indexOf("立管")>-1)&&(h==null||h.push({label:$.layername,value:$.layerid,id:$.layerid,data:$}))});const C=L.group[0].fields[0];C&&(C.options=h),u.value&&(u.value.dataForm.pipeLayer=(F=h[0])==null?void 0:F.value)},{addCustomWidget:I,removeCustomWidget:G}=j(),O=x();S(()=>{O.value=!0,I(o,"tool-pipelength","bottom-right"),a.graphicsLayer=te(o,{id:"pipe-length",title:"管线长度测量"}),a.sketch=w(o,a.graphicsLayer,{updateCallBack:_,createCallBack:_})});const q=()=>{var l,s;G(o,"tool-pipelength"),(l=a.graphicsLayer)==null||l.removeAll(),d(),(s=t.value)==null||s.Close(),a.graphicsLayer&&(o==null||o.map.remove(a.graphicsLayer))};return W(()=>{q()}),Q(()=>{O.value=!1}),(l,s)=>{const p=X,h=U;return f(),k("div",null,[i(Y,{id:"tool-pipelength",ref_key:"refBtn",ref:e,icon:"mdi:ruler",title:"管线长度",onClick:s[0]||(s[0]=C=>c("pipelength","管线长度测量",C))},null,512),y(O)?(f(),E(h,{key:0,ref_key:"refToolPanel",ref:t,"custom-class":"tool-pipelength-measure-panel",telport:"#arcmap-wrapper",title:y(n).toolPanelTitle,"destroy-by-close":!0,"before-close":()=>r(),"before-open":()=>g()},{default:B(()=>[i(p,{ref_key:"refForm",ref:u,config:y(L)},null,8,["config"])]),_:1},8,["title","before-close","before-open"])):Z("",!0)])}}}),Xe={class:"zoom"},Ye=P({__name:"ArcZoom",setup(b){const o=T("view"),t=Me();return o&&t.init(o,"bottom-right"),(e,n)=>(f(),k("div",Xe))}}),et={class:"scale"},tt=P({__name:"ArcScale",props:{position:{}},setup(b){const o=b,t=T("view"),e=new pe({view:t,unit:"metric",style:"ruler"});return t==null||t.ui.add(e,o.position||"bottom-left"),W(()=>{e.destroy(),t==null||t.ui.remove(e)}),(n,a)=>(f(),k("div",et))}}),ot={class:"wrapper"},at=P({__name:"ArcCoordinate",props:{position:{}},setup(b){const o=b,t=T("view"),e=new ue({view:t});return t==null||t.ui.add(e,o.position||"bottom-left"),W(()=>{e==null||e.destroy(),t==null||t.ui.remove(e)}),(n,a)=>(f(),k("div",ot))}}),st={class:"layerlist"},nt=P({__name:"ArcLayerList",setup(b){const o=T("view"),t=Ie();return S(()=>{o==null||o.when(()=>{t.init(o)})}),(e,n)=>(f(),k("div",st))}}),rt={id:"tool-search-poi",class:"esri-widget"},lt={class:"poi-item"},it={class:"poi-text"},ct={key:0,class:"poi-address"},pt={key:1,class:"poi-address"},ut=P({__name:"ArcPoi",setup(b){const o=T("view"),t=x(),e=V({value:"",loading:!1,options:[],queryType:"7",curItem:void 0});let n;const a=(c,r)=>{clearTimeout(n),n=setTimeout(()=>{c?(e.loading=!0,qe({keyWord:c,queryType:e.queryType,specifyAdminCode:e.specifyAdminCode}).then(w=>{var A,_,m;const d=w.data||{};e.returnType=d.resultType,d.resultType===1?e.options=((A=d.pois)==null?void 0:A.map(g=>({value:g.name,data:g})))||[]:d.resultType===2&&(e.options=(m=(_=d==null?void 0:d.statistics)==null?void 0:_.priorityCitys)==null?void 0:m.map(g=>({value:d.keyWord,data:g}))),r(e.options)}).finally(()=>{e.loading=!1})):(e.options=[],r(e.options))},300)},u=c=>{var r,w;if(c&&(e.curItem=c),!(e.loading||!e.curItem))if(e.returnType===2)e.value=c.data.name,(r=t.value)==null||r.focus();else{const d=(w=e.curItem.data.lonlat)==null?void 0:w.split(",");L(d)}},L=c=>{if(!o||c.length!==2)return;const r=new ie({geometry:new ce({longitude:c==null?void 0:c[0],latitude:c==null?void 0:c[1],spatialReference:o.spatialReference}),symbol:$e("picture",{url:De(),yOffset:-8})});o==null||o.graphics.removeAll(),o==null||o.graphics.add(r),Oe(o,r,{avoidHighlight:!0,zoom:15})};return S(()=>{var c,r,w,d;o==null||o.when(()=>{o.ui.add("tool-search-poi","top-right")}),Ge({lon:(r=(c=window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter)==null?void 0:c[0])==null?void 0:r.toString(),lat:(d=(w=window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter)==null?void 0:w[1])==null?void 0:d.toString()}).then(A=>{var _,m,g;e.specifyAdminCode=(g=(m=(_=A.data)==null?void 0:_.result)==null?void 0:m.addressComponent)==null?void 0:g.city_code}).catch(A=>{console.log(A)})}),(c,r)=>{const w=be,d=we,A=Ce;return f(),k("div",rt,[i(d,{modelValue:y(e).queryType,"onUpdate:modelValue":r[0]||(r[0]=_=>y(e).queryType=_),placeholder:"Select",class:"poi-selector"},{default:B(()=>[i(w,{label:"poi",value:"1"}),i(w,{label:"地名",value:"7"})]),_:1},8,["modelValue"]),i(A,{ref_key:"refAutoCom",ref:t,modelValue:y(e).value,"onUpdate:modelValue":r[1]||(r[1]=_=>y(e).value=_),"fetch-suggestions":a,"popper-class":"my-autocomplete",placeholder:"请输入",onSelect:u},{default:B(({item:_})=>[R("div",lt,[R("div",it,N(_.value),1),y(e).returnType===1?(f(),k("div",ct," 地址："+N(_.data.address),1)):(f(),k("div",pt," 城市："+N(_.data.name),1))])]),_:1},8,["modelValue"])])}}}),mt=z(ut,[["__scopeId","data-v-d1e6874d"]]),dt=P({__name:"ArcLayout",props:{panelClass:{},panelFullContent:{type:Boolean},penelExtra:{type:Boolean},hidePanelClose:{type:Boolean},panelMaxMin:{type:Boolean},panelTitle:{},panelTelport:{},panelDefaultVisible:{type:Boolean},panelPosition:{},panelDragable:{type:Boolean},panelDefaultMaxmin:{},beforePanelClose:{type:Function},panelOpend:{type:Function},panelMaxminChanged:{type:Function}},emits:["click","pipeLoaded","alarm-click","mapLoaded","mounted"],setup(b,{expose:o,emit:t}){const e=x(),n=t,a=b,u=x(),L=x(),c=x(),r=V({mounted:!1}),w=async m=>{n("pipeLoaded",m)},d=async m=>{await H(),n("mapLoaded",m)},A=m=>{var g;(g=L.value)==null||g.togglePop(m)},_=()=>{n("alarm-click")};return o({toggleAlarmPop:A,refPanel:u}),ke(()=>{console.log("actived"),r.mounted=!0,H().then(()=>{n("mounted")})}),S(()=>{console.log("mounted"),r.mounted=!0,H().then(()=>{n("mounted")})}),Ae(()=>{console.log("deactived"),r.mounted=!1}),(m,g)=>{const I=Ee,G=mt,O=nt,q=at,l=tt,s=Ye,p=Qe,h=Ke,C=Je,v=Ze,M=ze,F=Ue,$=Ne,oe=le,ae=U,se=re;return f(),k("div",{id:"map-outer-box",ref_key:"refDom",ref:e,class:"map-wrapper",onClick:g[0]||(g[0]=ne=>n("click",ne))},[i(se,{ref:"refMap",onLoaded:d},{default:B(()=>[D(m.$slots,"default",{},void 0,!0),D(m.$slots,"map-bars",{},void 0,!0),i(I,{onPipeLoaded:w}),i(G),i(O),i(q),i(l),i(s),i(p),i(h),i(C),i(v),i(M),i(F),i($,{ref_key:"refHome",ref:c},null,512),i(oe,{ref_key:"refAlarms",ref:L,onClick:_},null,512),y(r).mounted?(f(),E(ae,{key:0,ref_key:"refPanel",ref:u,"custom-class":a.panelClass||"gis-detail-panel",telport:y(e),draggable:a.panelDragable,dragout:!0,position:a.panelPosition,"full-content":a.panelFullContent,"destroy-by-close":!0,"after-open":a.panelOpend,extra:a.penelExtra,"show-close":!a.hidePanelClose,"max-min":a.panelMaxMin===void 0?!0:a.panelMaxMin,"before-close":a.beforePanelClose,title:m.panelTitle,"default-visible":a.panelDefaultVisible,"default-maxmin":a.panelDefaultMaxmin,"maxmin-changed":a.panelMaxminChanged},{extra:B(()=>[D(m.$slots,"detail-extra",{},void 0,!0)]),header:B(()=>[D(m.$slots,"detail-header",{},void 0,!0)]),default:B(()=>[D(m.$slots,"detail-default",{},void 0,!0)]),_:3},8,["custom-class","telport","draggable","position","full-content","after-open","extra","show-close","max-min","before-close","title","default-visible","default-maxmin","maxmin-changed"])):Z("",!0)]),_:3},512)],512)}}}),Nt=z(dt,[["__scopeId","data-v-82f755b5"]]);export{Nt as _};
