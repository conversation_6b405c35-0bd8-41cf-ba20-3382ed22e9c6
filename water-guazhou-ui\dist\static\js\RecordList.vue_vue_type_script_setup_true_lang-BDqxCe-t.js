import{_ as h}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./index-0NlGN6gS.js";import{s as _}from"./printUtils-C-AxhDcd.js";import{z as i,d as b,c as y,r as u,o as D,g as M,h as x,i as R}from"./index-r0dFAfgr.js";const W=t=>i({url:"/api/spp/readMeterData/correct/records",method:"get",params:t}),k=t=>i({url:"/api/spp/readMeterData/correct/recordsExport",method:"get",params:t,responseType:"blob"}),F=t=>i({url:"/api/spp/readMeterData/list",method:"get",params:t}),L=t=>i({url:"/api/spp/readMeterData/listExport",method:"get",params:t,responseType:"blob"}),v=b({__name:"RecordList",setup(t,{expose:f}){const p=y(),e=u({dataList:[],columns:[{minWidth:120,label:"抄表记号",prop:"custCode"},{minWidth:120,label:"用户名称",prop:"custName"},{minWidth:120,label:"联系方式",prop:"phone"},{minWidth:120,label:"抄表年月",prop:"ym"},{minWidth:120,label:"修正水量",prop:"correctWater"}],height:400,pagination:{refreshData:({page:s,size:r})=>{e.pagination.page=s,e.pagination.limit=r,o()}}}),o=async s=>{var r,n,l,d;try{const c={page:e.pagination.page||1,size:e.pagination.limit||20,custName:(l=(n=(r=p.value)==null?void 0:r.refForm)==null?void 0:n.dataForm)==null?void 0:l.name};if(s){const m=await k(c);_(m.data,"用水量修正记录")}else{e.loading=!0;const a=(d=(await W(c)).data)==null?void 0:d.data;e.dataList=(a==null?void 0:a.data)||[],e.pagination.total=(a==null?void 0:a.total)||0}}catch{}e.loading=!1},g=u({title:"修正记录",labelWidth:70,group:[{fields:[{type:"input",label:"用户名称",field:"name",inputStyle:{width:"200px"},extraFormItem:[{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{marginLeft:"20px"},iconifyIcon:"ep:search",click:()=>o()},{perm:!0,text:"导出",type:"success",iconifyIcon:"ep:download",click:()=>o(!0)}]}]}]},{fields:[{type:"table",config:e}]}]});return D(()=>{o()}),f({refRecord:p}),(s,r)=>{const n=h;return M(),x(n,{ref_key:"refRecord",ref:p,config:R(g)},null,8,["config"])}}});export{L as E,F as G,v as _};
