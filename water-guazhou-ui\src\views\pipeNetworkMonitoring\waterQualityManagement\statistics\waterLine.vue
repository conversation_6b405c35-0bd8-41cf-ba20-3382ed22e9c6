<!--水质管理-水质曲线-->
<template>
  <div class="wrapper">
    <SLCard
      class="card"
      title=" "
    >
      <template #title>
        <Tabs
          v-model="state.activeName"
          :config="topTabsConfig"
        ></Tabs>
      </template>
      <CardSearch
        ref="refSearch"
        :config="searchConfig"
      >
      </CardSearch>
      <SLCard
        class="SCard"
        :title="state.activePattern === 'list' ? '水质数据列表' : '水质曲线'"
      >
        <template #right>
          <el-radio-group v-model="state.activePattern">
            <el-radio-button label="echarts">
              <Icon
                style="margin-right: 1px;font-size: 16px"
                icon="clarity:line-chart-line"
              />
            </el-radio-button>
            <el-radio-button label="list">
              <Icon
                style="margin-right: 1px;font-size: 16px"
                icon="material-symbols:table"
              />
            </el-radio-button>
          </el-radio-group>
        </template>
        <!-- 列表模式 -->
        <div
          v-show="state.activePattern === 'list'"
          class="chart-box"
        >
          <FormTable
            ref="refCard"
            :config="cardTableConfig"
          ></FormTable>
        </div>
        <!-- 图表模式 -->
        <div
          v-show="state.activePattern === 'echarts'"
          ref="agriEcoDev"
          class="chart-box"
        >
          <VChart
            ref="refChart"
            :theme="useAppStore().isDark ? 'dark' : 'light'"
            :option="state.chartOption"
          ></VChart>
        </div>
      </SLCard>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Search as SearchIcon } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { Icon } from '@iconify/vue'
import { lineOption } from '../echarts'
import { IECharts } from '@/plugins/echart'
import useStation from '@/hooks/station/useStation'

import { formatColumn } from '@/utils/formartColumn'
import { getPointMonitor } from '@/api/waterQualityManage/waterQualityMonitoring'
import useGlobal from '@/hooks/global/useGlobal'
import { useAppStore } from '@/store'
import { getFormatTreeNodeDeepestChild } from '@/utils/GlobalHelper'
import { attr } from '../data'

const { $messageWarning } = useGlobal()
const erd = elementResizeDetectorMaker()
const refChart = ref<IECharts>()
const refCard = ref<IFormTableIns>()
const refSearch = ref<ISearchIns>()
const agriEcoDev = ref<any>()
let tableData = reactive<any[]>([])
const { getStationTree, getStationTreeByDisabledType } = useStation()
const state = reactive<{
  activeName: string,
  activePattern: string,
  stationId: string,
  chartOption: any,
  stationTree: any,
  chartName: string,
  data: any
}>({
  activeName: 'day',
  activePattern: 'echarts',
  stationId: '',
  chartOption: null,
  data: [],
  stationTree: [],
  chartName: ''
})

// tabs
const topTabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  field: 'type',
  tabs: [
    {
      label: '日水质',
      value: 'day'
    },
    {
      label: '月水质',
      value: 'month'
    },
    {
      label: '年水质',
      value: 'year'
    }
    // {
    //   label: '多日水质',
    //   value: 'daterange'
    // }
  ],
  handleTabClick: (tab: any) => {
    console.log('选择', tab.props.name)
    state.activeName = tab.props.name
    const queryParams = refSearch.value?.queryParams as any || {}
    if (queryParams.stationIds) {
      refreshData()
    } else {
      // $messageWarning('请选择监测点')
    }
  }
})

// 搜索栏初始化配置
const searchConfig = reactive<ISearch>({
  defaultParams: {
    day: dayjs().format('YYYY-MM-DD'),
    month: dayjs().format('YYYY-MM'),
    year: dayjs().format('YYYY'),
    daterange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    attr: 'remainder'
  },
  filters: [
    {
      type: 'select-tree',
      label: '监测点:',
      defaultExpandAll: true,
      field: 'stationIds',
      clearable: false,
      multiple: true,
      width: '200px',
      showCheckbox: true,
      options: computed(() => state.stationTree) as any,
      onChange: () => {
        refreshData()
      }
    },
    {
      type: 'select',
      label: '统计类型',
      field: 'attr',
      width: '140px',
      options: [
        { label: '余氯', value: 'remainder' },
        { label: '浊度', value: 'turbidity' },
        { label: 'PH', value: 'ph' },
        { label: '溶氧', value: 'oxygen' },
        { label: '电导率', value: 'conductance' }
      ]
    },
    {
      type: 'date',
      label: '日期',
      field: 'day',
      format: 'YYYY-MM-DD',
      clearable: false,
      hidden: computed(() => state.activeName === 'year' || state.activeName === 'month' || state.activeName === 'daterange') as any,
      width: 300
    },
    {
      type: 'month',
      label: '日期',
      field: 'month',
      format: 'YYYY-MM',
      clearable: false,
      hidden: computed(() => state.activeName === 'year' || state.activeName === 'day' || state.activeName === 'daterange') as any,
      width: 300
    },
    {
      type: 'year',
      label: '日期',
      field: 'year',
      format: 'YYYY',
      clearable: false,
      hidden: computed(() => state.activeName === 'day' || state.activeName === 'month' || state.activeName === 'daterange') as any,
      width: 300
    },
    {
      type: 'daterange',
      label: '日期',
      format: 'YYYY-MM',
      field: 'daterange',
      clearable: false,
      hidden: computed(() => state.activeName === 'year' || state.activeName === 'month' || state.activeName === 'day') as any,
      width: 300
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            const queryParams = refSearch.value?.queryParams as any || {}
            if (queryParams.stationIds) {
              refreshData()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          hide: () => { return state.activePattern !== 'list' },
          click: () => {
            const queryParams = refSearch.value?.queryParams as any || {}
            if (queryParams.stationIds) {
              refCard.value?.exportTable()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 配置加载图表数据
const refuseChart = () => {
  const tableDataList = state.data.tableDataList.filter(table => !/[\u4E00-\u9FA5]/g.test(table.ts))
  const dataX = tableDataList.map(table => table.ts)
  console.log(dataX)
  const chartOption = lineOption(dataX)
  chartOption.series = []
  const serie = {
    name: state.chartName,
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        { type: 'max', name: '最大值' },
        { type: 'min', name: '最小值' }
      ]
    },
    markLine: {
      data: [{ type: 'average', name: '平均值' }]
    }
  }
  const newSerie = JSON.parse(JSON.stringify(serie)) as any
  newSerie.data = state.data?.tableDataList.map(table => table.value)
  chartOption.series.push(newSerie)
  state.data?.tableInfo.map(info => {
    if (info.columnValue !== 'ts') {
      const newSerie = JSON.parse(JSON.stringify(serie)) as any
      newSerie.name = info.columnName
      newSerie.data = state.data?.tableDataList.map(table => table[info.columnValue])
      chartOption.series.push(newSerie)
    }
  })
  const queryParams = refSearch.value?.queryParams as any || {}
  const currentAttr = attr.find(ad => ad.value === queryParams.attr)
  chartOption.yAxis[0].name = currentAttr?.label + '（' + currentAttr?.unit + '）'
  refChart.value?.clear()
  nextTick(() => {
    if (agriEcoDev.value) {
      erd.listenTo(agriEcoDev.value, () => {
        state.chartOption = chartOption
        refChart.value?.resize()
      })
    }
  })
}

// 条件查询数据列表
const refreshData = async () => {
  const queryParams = refSearch.value?.queryParams || {}
  // const attributes = TreeData.checkedKeys as any[]
  const date = queryParams[state.activeName]
  const params: any = {
    stationIds: queryParams.stationIds.join(','),
    queryType: state.activeName,
    time: date,
    attr: queryParams.attr,
    stationType: '水质监测站'
  }
  const res = await getPointMonitor(params)
  const data = res.data?.data

  const columns = formatColumn(data?.tableInfo)
  tableData = data?.tableDataList as any[]
  cardTableConfig.columns = columns
  cardTableConfig.dataList = tableData
  cardTableConfig.pagination.total = data?.tableDataList.length
  state.data = data
  refuseChart()
}

onBeforeMount(async () => {
  const type = ['水质监测站'].join(',')
  const treeData = await getStationTree(type) as any[]
  state.stationTree = treeData
  await getStationTreeByDisabledType(treeData, ['Project'], false, 'Station')
  const currentStation = getFormatTreeNodeDeepestChild(
    treeData
  ) as any
  searchConfig.defaultParams = {
    ...searchConfig.defaultParams
    // stationId: currentStation.id
  }

  refSearch.value?.resetForm()
  // await refreshData()
})
</script>
<style lang="scss" scoped>
.card {

  height: 100%;
}

.tab {
  height: 100%;
  width: 100%;
}

.SCard {
  height: calc(100% - 70px);
}

.chart-box {
  height: calc(100%);
}

.card-table {
  height: calc(100% - 70px);
}

:deep(.el-select__tags) {
  max-width: 100% !important;
}
</style>
