/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.virtual;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningScheduledExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.AttributeConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.Utils.TimeDiff;
import org.thingsboard.server.common.data.constantsAttribute.LoocalMap;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.device.DeviceRealTime;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.VirtualId;
import org.thingsboard.server.common.data.inputKv.InputKv;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.telemetryAttribute.RequestTs;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.virtual.Virtual;
import org.thingsboard.server.common.data.virtual.VirtualData;
import org.thingsboard.server.dao.attributes.AttributesDao;
import org.thingsboard.server.dao.device.DeviceDao;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.exception.DataValidationException;
import org.thingsboard.server.dao.feignService.ProjectRelationApi;
import org.thingsboard.server.dao.inputKv.InputKvDao;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.service.DataValidator;
import org.thingsboard.server.dao.virtual.VirtualDao;
import org.thingsboard.server.service.obtain.ResponseUtil;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;

import static org.thingsboard.server.common.data.Utils.DateUtils.*;
import static org.thingsboard.server.common.data.virtual.VirtualUtils.handleVirtualFormula;
import static org.thingsboard.server.common.data.virtual.VirtualUtils.isMathSymbol;
import static org.thingsboard.server.service.rest.RestUtil.doResetPost;

@Service
@Slf4j
public class VirtualServiceImpl extends AbstractEntityService implements VirtualService {

    @Autowired
    private VirtualDao virtualDao;
    @Autowired
    private InputKvDao inputKvDao;
    @Autowired
    private AttributesDao attributesDao;
    @Autowired
    private DeviceDao deviceDao;

    @Autowired
    private BaseObtainDataService obtainService;

    @Autowired
    private ProjectRelationApi projectRelationApi;


    private ListeningScheduledExecutorService queueExecutor;

    @Value("${tsdb.HITSDB_IP}")
    private String TSDB_IP;

    @Value("${tsdb.HITSDB_PORT}")
    private String TSDB_PORT;


    //租户虚拟表集合
    private List<Virtual> virtualList = new ArrayList<>();
    //租户虚拟表编号集合
    private List<String> serialNumberList = new ArrayList<>();

    @PostConstruct
    public void init() {
        queueExecutor = MoreExecutors.listeningDecorator(Executors.newSingleThreadScheduledExecutor());
    }

    @Override
    public ListenableFuture<List<Virtual>> findVirtualByTenant(TenantId tenantId) {
        return virtualDao.findVirtualByTenant(tenantId);
    }

    @Override
    public ListenableFuture<List<Virtual>> findVirtualByTenantAndType(TenantId tenantId, String type) {
        return virtualDao.findVirtualByTenantAndType(tenantId, type);
    }


    @Override
    public ListenableFuture<Virtual> findByVirtualId(String virtualId) {
        return virtualDao.findByVirtualId(virtualId);
    }

    @Override
    public ListenableFuture<List<Virtual>> findAll() {
        return virtualDao.findAll();
    }

    @Override
    public boolean delVirtual(TenantId tenantId, List<String> virtuals) {
        virtuals.forEach(virtual -> {
            deleteEntityRelations(tenantId, new VirtualId(UUID.fromString(virtual)));
            virtualDao.removeById((UUID.fromString(virtual)));
            // 更新资源项目关系
            projectRelationApi.mountEntityToProject(DataConstants.ProjectRelationEntityType.VIRTUAL.name(),
                    "",
                    Collections.singletonList(UUIDConverter.fromTimeUUID(UUID.fromString(virtual))));
        });
        return true;
    }

    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getFormulaData(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException {
        //解析公式
        try {
            List<String> formulas = new ArrayList<>();
            formula.forEach(f -> {
                List<String> fo = handleVirtualFormula(f);
                fo.forEach(s -> {
                    if (!isMathSymbol(s))
                        formulas.add(s);
                });
            });
            List<ResponseTs> responseTsList = obtainService.getDeviceDataFromOpenTSDB(start, end, formulas, tenantId, null);
            return obtainService.convertResponseTsByFormula(tenantId, responseTsList, formula, type, start, end, false);
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }

    }

    /**
     * 针对市平台上报数据做的修改，当从opentadb没有查询到数据时，直接从数据库里面进行查询
     *
     * @param formula
     * @param start
     * @param end
     * @param type
     * @param tenantId
     * @return
     * @throws ThingsboardException
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getFormulaLastData(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException {
        //解析公式
        try {
            List<String> formulas = new ArrayList<>();
            formula.forEach(f -> {
                List<String> fo = handleVirtualFormula(f);
                fo.forEach(s -> {
                    if (!isMathSymbol(s))
                        formulas.add(s);
                });
            });
            List<ResponseTs> responseTsList = obtainService.getDeviceDataFromOpenTSDB(start, end, formulas, tenantId, null);
            if (responseTsList != null && !responseTsList.isEmpty()) {
                return obtainService.convertResponseTsByFormula(tenantId, responseTsList, formula, type, start, end, true);
            } else {
                return obtainService.convertResponseTsByFormula(tenantId, formula, start);
            }

        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getVirtualData(long start, long end, List<String> virtualId, String type, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        try {
            virtualList = findVirtualByTenant(tenantId).get();
            virtualList.forEach(virtual -> {
                serialNumberList.add(virtual.getSerialNumber());
            });
            List<String> formulas = getFormulaByVirtual(virtualList, virtualId);
            start = DateUtils.getMeterReadingTime(new Date(start), readmeterTime, DateUtils.DAY).getTime();
            end = DateUtils.getMeterReadingTime(new Date(end), readmeterTime, DateUtils.DAY).getTime();
            List<ResponseTs> responseTsList = obtainService.getDeviceDataFromOpenTSDB(start, end, formulas, tenantId, null);
            return obtainService.convertResponseTsByVirtuals(tenantId, responseTsList, virtualId, type, readmeterTime, virtualList, start, end);
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    public List<String> getFormulaByVirtual(List<Virtual> virtuals, List<String> virtualList) {
        List<String> list = new ArrayList<>();
        virtualList.forEach(vir -> {
            virtuals.forEach(virtual -> {
                if (UUIDConverter.fromTimeUUID(virtual.getUuidId()).equals(vir)) {
                    if (virtual.getFormula() == null)
                        return;
                    List<String> strings = handleVirtualFormula(virtual.getFormula());
                    strings.forEach(s -> {
                        if (serialNumberList.contains(s)) {
                            for (Virtual v : virtuals) {
                                if (v.getSerialNumber().equals(s))
                                    list.addAll(getFormulaByVirtual(virtuals, Collections.singletonList(UUIDConverter.fromTimeUUID(v.getId().getId()))));
                            }
                        } else if (!isMathSymbol(s) && !list.contains(s))
                            list.add(s);
                    });
                }
            });
        });

        return list;
    }

    public List<String> getAllFormulaByVirtual(List<Virtual> virtuals, List<String> virtualList) {
        List<String> list = new ArrayList<>();
        virtualList.forEach(vir -> {
            virtuals.forEach(virtual -> {
                if (UUIDConverter.fromTimeUUID(virtual.getUuidId()).equals(vir)) {
                    if (virtual.getFormula() == null)
                        return;
                    list.addAll(handleVirtualFormula(virtual.getFormula()));
                }
            });
        });

        return list;
    }


    private Date getmeterTime(Date date, Optional<AttributeKvEntry> attributeKvEntry) {
        if (attributeKvEntry.isPresent())
            date = DateUtils.getMeterReadingTime(date, attributeKvEntry.get().getValueAsString(), DateUtils.DAY);
        return date;
    }


    /**
     * 根据时间段和查询类型处理虚拟表数据
     *
     * @param start
     * @param end
     * @param virtual
     * @param type
     * @return
     */
    private ListenableFuture<VirtualData> getVirtualData(TenantId tenantId, Date start, Date end, Virtual virtual, String type, String readmeterTime) {
        return queueExecutor.submit(() -> {
            List<String> formulaList = handleVirtualFormula(virtual.getFormula());
            LinkedHashMap<String, BigDecimal> LinkedHashMap = new LinkedHashMap<>();
            LinkedHashMap.putAll(getTelemetryData(tenantId, start.getTime(), end.getTime(), formulaList, type, readmeterTime));
            VirtualData virtualData = new VirtualData(virtual);
            virtualData.setData(LinkedHashMap);
            return virtualData;
        });
    }


    /**
     * 获取公式中非符号的编号或设备对应属性值
     *
     * @param
     * @return
     */
    public LinkedHashMap<String, BigDecimal> getTelemetryData(TenantId tenantId, long start, long end, List<String> formulaList, String type, String readmeterTime) {
        //如果是编号则则进行编号的解析
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
        formulaList.forEach(formula -> {
            if (serialNumberList.contains(formula)) {
                LinkedHashMap<String, BigDecimal> data = new LinkedHashMap<>();
                for (Virtual virtual : virtualList) {
                    if (virtual.getSerialNumber().equals(formula)) {
                        switch (virtual.getVirtualType()) {
                            case INPUT_VALUE: {
                                List<InputKv> inputKvList = inputKvDao.getInputKvByEntityIdAndTime(virtual.getId().toString(), start, end);
                                inputKvList.forEach(inputKv -> {
                                    data.put(formula, new BigDecimal(inputKv.getValue()));
                                    switch (type) {
                                        case DAY: {
                                            result.put(DateUtils.date2Str(inputKv.getTs(), DateUtils.DATE_FORMATE_DAY), data);
                                            break;
                                        }
                                        case MONTH: {
                                            result.put(DateUtils.date2Str(inputKv.getTs(), DateUtils.DATE_FORMATE_MONTH), data);
                                            break;
                                        }
                                        case YEAR: {
                                            result.put(DateUtils.date2Str(inputKv.getTs(), DateUtils.DATE_FORMATE_YEAR), data);
                                            break;
                                        }
                                    }
                                });
                                break;
                            }
                            case CALCULATION_VALUE: {
                                List<String> formulas = handleVirtualFormula(virtual.getFormula());
                                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> LinkedHashMap = new LinkedHashMap<>();
                                formulas.forEach(f -> {
                                    if (!isMathSymbol(f))
                                        LinkedHashMap.put(f, getTelemetryData(tenantId, start, end, formulas, type, readmeterTime));
                                });
                            }
                        }
                    }
                }  //非编号则为设备属性
            } else if (!isMathSymbol(formula)) {
                LinkedHashMap<String, BigDecimal> responseTs = getDataFromTsdb(tenantId, start, end, formula, type, readmeterTime);
                //获取的数据需要进行数据处理
                responseTs.keySet().forEach(key -> {
                    LinkedHashMap<String, BigDecimal> data = new LinkedHashMap<>();
                    data.put(formula, responseTs.get(key));
                    switch (type) {
                        case DAY: {
                            result.put(key, data);
                            break;
                        }
                        case MONTH: {
                            result.put(key, data);
                            break;
                        }
                        case YEAR: {
                            result.put(key, data);
                            break;
                        }
                    }

                });
            }
        });
        return handleData(result, formulaList);
    }


    public LinkedHashMap<String, BigDecimal> handleData
            (LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> LinkedHashMap, List<String> formulaList) {
        LinkedHashMap<String, BigDecimal> result = new LinkedHashMap<>();
        LinkedHashMap.keySet().forEach(key -> result.put(key, new BigDecimal(handleDataByFormula(LinkedHashMap.get(key), formulaList))));
        return result;
    }

    /**
     * 处理公式数据并返回
     *
     * @param data
     * @param formulaList
     * @return
     */
    @Override
    public String handleDataByFormula(LinkedHashMap<String, BigDecimal> data, List<String> formulaList) {
        try {
            String formula = "";
            for (String fou : formulaList) {
                if (isMathSymbol(fou)) {
                    formula = formula + fou;
                } else {
                    formula = new StringBuffer().append(formula).append((data.get(fou).toPlainString())).toString();
                }
            }
            return String.valueOf(AviatorEvaluator.execute(formula));
        } catch (Exception e) {
            log.info("数据计算失败！" + e);
            return null;
        }
    }

    @Override
    @Transactional
    public List<Virtual> virtualAdd(List<Virtual> virtuals) {
        List<Virtual> saveList = new ArrayList<>();
        try {
            virtuals.forEach(virtual -> {
                try {
                    if (virtual.getId() == null) {
                        deviceValidator.validate(virtual, Virtual::getTenantId);
                    }
                    Virtual saveV = virtualDao.virtualSave(virtual.getTenantId(), virtual).get();
                    saveList.add(saveV);
                    // 更新资源项目关系
                    if (StringUtils.isNotBlank(virtual.getProjectId())) {
                        projectRelationApi.mountEntityToProject(DataConstants.ProjectRelationEntityType.VIRTUAL.name(),
                                virtual.getProjectId(),
                                Collections.singletonList(UUIDConverter.fromTimeUUID(UUID.fromString(saveV.getId().toString()))));
                    }
                } catch (Exception e) {
                    return;
                }
            });

            return saveList;
        } catch (Exception e) {
            e.printStackTrace();
            return saveList;
        }

    }

    @Override
    public ListenableFuture<List<Virtual>> findBySerialNumber(String serialNumber) {
        return virtualDao.findBySerialNumber(serialNumber);
    }

    @Override
    public LinkedHashMap<String, DeviceRealTime> getRealTimeData(List<String> formula, long start, long end, TenantId tenantId, String timeLimit) throws ThingsboardException {
        try {
            LinkedHashMap<String, DeviceRealTime> result = new LinkedHashMap<>();
            List<ResponseTs> response = obtainService.getDeviceDataFromOpenTSDB(start, end, formula, tenantId, timeLimit);
            if (response == null || response.isEmpty()) {
                return result;
            }
            response.forEach(responseTs -> {
                //对每一个数据检查是否有换表时间
                LinkedHashMap<String, String> data = new LinkedHashMap<>();
                DeviceId deviceId = new DeviceId(UUIDConverter.fromString(responseTs.getMetric().split("\\.")[1]));
                LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
                Optional<AttributeKvEntry> changemeterAttr = null;
                try {
                    changemeterAttr = attributesDao.find(tenantId, deviceId, DataConstants.SERVER_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                changemeterAttr.ifPresent(Attr ->
                        changemeterMap.putAll(TimeDiff.changemeter(Attr.getValueAsString())));
                //筛选出换表数据
                ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(responseTs, changemeterMap);
                String tag = responseTs.getMetric().split("\\.")[1] + "." + responseTs.getTags().get(DataConstants.ATTRIBUTE_PROP);
                BigDecimal bigDecimal = new BigDecimal(0);
                if (responseTs.getDps().size() > 0) {
                    //计算用量

                    for (ArrayList<LoocalMap> maps : linkedHashMaps) {
                        maps.forEach(m -> {
                            data.put(String.valueOf(m.getKey()), String.valueOf(m.getData()));
                        });
                        bigDecimal = bigDecimal.add(maps.get(maps.size() - 1).getData().subtract(maps.get(0).getData()));
                    }
                }
                result.put(tag, new DeviceRealTime(data, String.valueOf(bigDecimal)));
            });

            return result;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }

    }


//    /**
//     * 获取逻辑抄表数据
//     *
//     * @param formula
//     * @param start
//     * @param end
//     * @param type
//     * @param tenantId
//     * @return
//     * @throws ThingsboardException
//     */
//    @Override
//    public Object getLogicReading(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException {
//        List<String> formulas = new ArrayList<>();
//        formula.forEach(f -> {
//            List<String> fo = handleVirtualFormula(f);
//            fo.forEach(s -> {
//                if (!isMathSymbol(s))
//                    formulas.add(s);
//            });
//        });
//        List<ResponseTs> responseTsList = obtainService.getDeviceDataFromOpenTSDB(start, end, formulas, tenantId, null);
//        Map<String, LinkedHashMap<String, BigDecimal>> resultMap = new LinkedHashMap<>();
//        resultMap.put(DataConstants.START, new LinkedHashMap());
//        resultMap.put(DataConstants.END, new LinkedHashMap());
//        if (responseTsList != null && responseTsList.size() > 0)
//            responseTsList.forEach(responseTs -> {
//                String tag = responseTs.getMetric().split("\\.")[1] + "." + responseTs.getTags().get(DataConstants.ATTRIBUTE_PROP);
//                if (responseTs.getDps().size() > 0) {
//                    resultMap.get(DataConstants.START).put(tag, new BigDecimal(responseTs.getDps().get(responseTs.getDps().keySet().toArray()[0])));
//                    resultMap.get(DataConstants.END).put(tag, new BigDecimal(responseTs.getDps().get(responseTs.getDps().keySet().toArray()[responseTs.getDps().size() - 1])));
//                } else {
//                    resultMap.get(DataConstants.START).put(tag, new BigDecimal(0));
//                    resultMap.get(DataConstants.END).put(tag, new BigDecimal(0));
//                }
//            });
//        Map<String, LinkedHashMap> result = new LinkedHashMap<>();
//        formula.forEach(f -> {
//            List<String> noSernumber = new ArrayList<>();
//            List<String> fm = handleVirtualFormula(f);
//            fm.forEach(fo -> {
//                noSernumber.add(fo);
//            });
//            result.put(f, new LinkedHashMap());
//            resultMap.entrySet().forEach(time -> {
//                result.get(f).put(time.getKey(), handleDataByFormula(time.getValue(), noSernumber));
//            });
//        });
//        return result;
//    }

    /**
     * 从tsdb获取降采样rate数据
     * 使用rate即为（v2-v1）/t2-t1类型数据，故此处使用降采样后应当对数据进行复原操作
     * 0817 使用rate会带来精度不准确的问题，弃用rate方案
     *
     * @param start
     * @param end
     * @param formal
     * @return
     */
    @Override
    public LinkedHashMap<String, BigDecimal> getDataFromTsdb(TenantId tenantId, long start, long end, String formal, String type, String readmeterTime) {
        LinkedHashMap<String, BigDecimal> result = new LinkedHashMap<>();
        LinkedHashMap<String, Object> query = new LinkedHashMap<>();
        query.put(AttributeConstants.TSDB_AGGREGATOR, AttributeConstants.TSDB_AGGREGATOR_TYPE.none);
        String[] array = formal.split("\\.");
        Device device = deviceDao.findById(tenantId, UUIDConverter.fromString(array[0]));
        try {
            //查询公式对应的设备属性，并根据该属性的统计类别进行分别处理
            PropAttribute propAttribute = getProp(tenantId, array[0], array[1]);
            if (propAttribute.getStatType() == null)
                return result;
            Optional<AttributeKvEntry> changemeterAttr = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(array[0])), DataConstants.CLIENT_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
            query.put(AttributeConstants.TSDB_METRIC, UUIDConverter.fromTimeUUID(device.getTenantId().getId()) + "." + UUIDConverter.fromTimeUUID(device.getId().getId()));
            ArrayList<HashMap<String, Object>> queries = new ArrayList<>();
            LinkedHashMap<String, String> tags = new LinkedHashMap<>();
            tags.put(AttributeConstants.TSDB_PROPERTY, propAttribute.getName());
            query.put("tags", tags);
            query.put(AttributeConstants.TSDB_DOWNSAMPLE, AttributeConstants.TSDB_DOWNSAMPLE_DEFAULT);
            queries.add(query);
            String putUrl = TSDB_IP + ":" + TSDB_PORT + AttributeConstants.TSDB_API_QUERY;
            ResponseTs responseTs = doResetPost(putUrl, new RequestTs(start, end, queries)).get(0);
//            responseTs = covertTime(responseTs);
            LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
            changemeterAttr.ifPresent(Attr ->
                    changemeterMap.putAll(TimeDiff.changemeter(Attr.getValueAsString())));
            switch (propAttribute.getStatType()) {
                case AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE: {
                    return resolveData(handleResponseTs(responseTs, changemeterMap), type, AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE, readmeterTime);
                }
                case AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM: {
                    return resolveData(handleResponseTs(responseTs, changemeterMap), type, AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM, readmeterTime);
                }
                //todo：其他统计方式
            }
            return result;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 根据类型对数据进行重新差值求和(添加对统计类型的处理)
     *
     * @param data
     * @param type
     * @return
     */
    private LinkedHashMap<String, BigDecimal> resolveData(List<LinkedHashMap<Long, BigDecimal>> data, String type, String statType, String readmeterTime) {
        LinkedHashMap<String, BigDecimal> linkedHashMaps = groupDataByDay(data, statType, readmeterTime);
        switch (type) {
            case DAY: {
                return linkedHashMaps;
            }
            case MONTH: {
                return grorupDayDataByTimeType(linkedHashMaps, MONTH, statType);
            }
            case YEAR: {
                return grorupDayDataByTimeType(linkedHashMaps, YEAR, statType);
            }
            default:
                return linkedHashMaps;
        }
    }


    /**
     * 根据时间类别对每天的数据进行分类整理
     *
     * @param linkedHashMaps
     * @param type
     * @return
     */
    private LinkedHashMap<String, BigDecimal> grorupDayDataByTimeType(LinkedHashMap<String, BigDecimal> linkedHashMaps, String type, String statType) {
        LinkedHashMap<String, ArrayList<BigDecimal>> listLinkedHashMap = new LinkedHashMap<>();
        linkedHashMaps.keySet().forEach(key -> {
            String timeKey = null;
            switch (type) {
                case MONTH: {
                    timeKey = DateUtils.date2Str(DateUtils.str2Date(key, DateUtils.DATE_FORMATE_DAY), DateUtils.DATE_FORMATE_MONTH);
                    break;
                }
                case YEAR: {
                    timeKey = DateUtils.date2Str(DateUtils.str2Date(key, DateUtils.DATE_FORMATE_DAY), DateUtils.DATE_FORMATE_YEAR);
                    break;
                }
            }
            if (!listLinkedHashMap.keySet().contains(timeKey))
                listLinkedHashMap.put(timeKey, new ArrayList<BigDecimal>());
            listLinkedHashMap.get(timeKey).add(linkedHashMaps.get(key));
        });
        //差值求和
        LinkedHashMap<String, BigDecimal> linkedHashMap = new LinkedHashMap<>();
        listLinkedHashMap.keySet().forEach(map -> {
            switch (statType) {
                case AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM: {
                    linkedHashMap.put(map, listLinkedHashMap.get(map).get(listLinkedHashMap.get(map).size() - 1));
                    break;
                }
                case AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE: {
                    linkedHashMap.put(map, listLinkedHashMap.get(map).get(listLinkedHashMap.get(map).size() - 1).subtract(listLinkedHashMap.get(map).get(0)));
                    break;
                }
            }

        });
        return linkedHashMap;

    }


    /**
     * 把数据按每天进行分组并对数据进行处理
     *
     * @param data
     * @return
     */
    private LinkedHashMap<String, BigDecimal> groupDataByDay(List<LinkedHashMap<Long, BigDecimal>> data, String statType, String hour) {
        List<LinkedHashMap<String, ArrayList<BigDecimal>>> linkedHashMaps = new ArrayList<>();
        data.forEach(longBigDecimalLinkedHashMap -> {
            LinkedHashMap<String, ArrayList<BigDecimal>> map = new LinkedHashMap<>();
            longBigDecimalLinkedHashMap.keySet().forEach(key -> {
                String dayLay = TimeDiff.getDayByReadmeter(key, hour, DateUtils.DAY);
                if (!map.containsKey(dayLay))
                    map.put(dayLay, new ArrayList<BigDecimal>());
                map.get(dayLay).add(longBigDecimalLinkedHashMap.get(key));
            });
            linkedHashMaps.add(map);
        });
        //对每个天数据进行差值求和,得到每天的结果
        LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
        linkedHashMaps.forEach(linkedHashMap -> {
            linkedHashMap.keySet().forEach(key -> {
                switch (statType) {
                    case AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM: {
                        map.put(key, linkedHashMap.get(key).get(linkedHashMap.get(key).size() - 1));
                        break;
                    }
                    case AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE: {
                        if (map.keySet().contains(key))
                            map.put(key, map.get(key).add(linkedHashMap.get(key).get(linkedHashMap.get(key).size() - 1).subtract(linkedHashMap.get(key).get(0))));
                        else
                            map.put(key, linkedHashMap.get(key).get(linkedHashMap.get(key).size() - 1).subtract(linkedHashMap.get(key).get(0)));
                        break;
                    }
                }

            });
        });
        return map;
    }


    /**
     * 根据换表时间筛选数据，返回一个时间-数据的集合
     *
     * @param responseTs
     * @param changemeterMap
     * @return
     */
    private List<LinkedHashMap<Long, BigDecimal>> handleResponseTs(ResponseTs responseTs, Map<Long, Long> changemeterMap) {
        List<LinkedHashMap<Long, BigDecimal>> result = new ArrayList<>();
        if (changemeterMap.isEmpty()) {
            LinkedHashMap<Long, BigDecimal> dataMap = new LinkedHashMap<>();
            responseTs.getDps().keySet().forEach(key -> dataMap.put(Long.parseLong(key), new BigDecimal(responseTs.getDps().get(key))));
            result.add(dataMap);
        } else {
            Map<Long, BigDecimal> map = new LinkedHashMap<>();
            int count = 0;
            for (String s : responseTs.getDps().keySet()) {
                boolean flag = false;
                count++;
                for (Long j : changemeterMap.keySet()) {
                    if (TimeDiff.betweenTwoTimes(Long.parseLong(s), j, changemeterMap.get(j)))
                        flag = true;
                }
                if (!flag)
                    map.put(Long.parseLong(s), new BigDecimal(responseTs.getDps().get(s)));
                //此处添加当循环到最后一条的时候同样把map添加到返回结果集中
                if ((flag && !map.isEmpty()) || (count == responseTs.getDps().size() && !map.isEmpty())) {
                    LinkedHashMap<Long, BigDecimal> cover = new LinkedHashMap<>();
                    cover.putAll(map);
                    result.add(cover);
                    map.clear();
                }
            }
        }
        return result;
    }


    /**
     * 获取设备对应属性名的统计类型
     *
     * @param deviceId
     * @param propName
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    private PropAttribute getProp(TenantId tenantId, String deviceId, String propName) throws ExecutionException, InterruptedException {
        Optional<AttributeKvEntry> prop = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.CLIENT_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
        PropAttribute propAttribute = new PropAttribute();
        prop.ifPresent(attr -> {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(attr.getValueAsString());
                Iterator<JsonNode> elements = jsonNode.elements();
                elements.forEachRemaining(element -> {
                    if (element.get("name").asText().equals(propName)) {
                        propAttribute.setName(propName);
                        propAttribute.setStatType(element.get("statType").asText());
                    }
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        return propAttribute;
    }

    private DataValidator<Virtual> deviceValidator = new DataValidator<Virtual>() {
        @Override
        protected void validateCreate(TenantId tenantId, Virtual data) {
//            super.validateCreate(tenantId, data);
            try {
                List<Virtual> virtualList = virtualDao.findBySerialNumber(data.getSerialNumber()).get();
                for (Virtual v : virtualList) {
                    if (UUIDConverter.fromTimeUUID(v.getTenantId().getId()).equals(UUIDConverter.fromTimeUUID(data.getTenantId().getId())))
                        throw new DataValidationException("Virtual with such serialNumber already exists!");
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
        }
    };


    public static void main(String[] args) {
        List<Map<Integer, Integer>> result = new ArrayList<>();
        Map<Integer, Integer> map1 = new LinkedHashMap<>();
        for (int i = 0; i < 20; i++) {
            map1.put(i, i + 1);
        }
        Map<Integer, Integer> map2 = new LinkedHashMap<>();
        map2.put(5, 7);
        map2.put(12, 15);
        Map<Integer, Integer> map = new LinkedHashMap<>();
//        map1.keySet().forEach(tsKey -> {
//            boolean flag=false;
//            map2.keySet().forEach(key -> {
//                if (tsKey > key && tsKey < map2.get(key)) {
//                    flag=true;
//                    if (!map.isEmpty()) {
//                        Map<Integer, Integer> map3 = new LinkedHashMap<>();
//                        map3.putAll(map);
//                        result.add(map3);
//                        map.clear();
//                    }
//                } else {
//                    map.put(tsKey, map1.get(tsKey));
//                }
//            });
////            result.add(map);
//        });

        int count = 0;
        for (Integer i : map1.keySet()) {
            boolean flag = false;
            count++;
            for (int j : map2.keySet()) {
                if (i >= j && i <= map2.get(j))
                    flag = true;
            }
            if (!flag)
                map.put(i, map1.get(i));
            if ((flag && !map.isEmpty()) || count == map1.size()) {
                Map<Integer, Integer> map3 = new LinkedHashMap<>();
                map3.putAll(map);
                result.add(map3);
                map.clear();
            }
        }
        int a = result.size();
    }

    /**
     * 获取逻辑抄表数据
     *
     * @param formula
     * @param start
     * @param end
     * @param type
     * @param tenantId
     * @return
     * @throws ThingsboardException
     */
    @Override
    public Object getLogicReading(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException {
        List<String> formulas = new ArrayList<>();
        formula.forEach(f -> {
            List<String> fo = handleVirtualFormula(f);
            fo.forEach(s -> {
                if (!isMathSymbol(s))
                    formulas.add(s);
            });
        });
        List<ResponseTs> responseTsList = obtainService.getDeviceDataFromOpenTSDB(start, end, formulas, tenantId, null);
        Map<String, LinkedHashMap<String, BigDecimal>> resultMap = new LinkedHashMap<>();
        resultMap.put(DataConstants.START, new LinkedHashMap());
        resultMap.put(DataConstants.END, new LinkedHashMap());
        resultMap.put(DataConstants.CONSUMPTION, new LinkedHashMap());
        if (responseTsList != null && responseTsList.size() > 0) {
            responseTsList.forEach(responseTs -> {
                //对每一个数据检查是否有换表时间
                DeviceId deviceId = new DeviceId(UUIDConverter.fromString(responseTs.getMetric().split("\\.")[1]));
                LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
                try {
                    Optional<AttributeKvEntry> changemeterAttr = attributesDao.find(tenantId, deviceId, DataConstants.SERVER_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
                    changemeterAttr.ifPresent(Attr ->
                            changemeterMap.putAll(TimeDiff.changemeter(Attr.getValueAsString())));
                    //筛选出换表数据
                    ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(responseTs, changemeterMap);
                    String tag = responseTs.getMetric().split("\\.")[1] + "." + responseTs.getTags().get(DataConstants.ATTRIBUTE_PROP);
                    if (responseTs.getDps().size() > 0) {
                        resultMap.get(DataConstants.START).put(tag, linkedHashMaps.get(0).get(0).getData());
                        resultMap.get(DataConstants.END).put(tag, linkedHashMaps.get(linkedHashMaps.size() - 1).get(linkedHashMaps.get(linkedHashMaps.size() - 1).size() - 1).getData());
                        //计算用量
                        BigDecimal bigDecimal = new BigDecimal(0);
                        for (ArrayList<LoocalMap> maps : linkedHashMaps) {
                            bigDecimal = bigDecimal.add(maps.get(maps.size() - 1).getData().subtract(maps.get(0).getData()));
                        }
                        resultMap.get(DataConstants.CONSUMPTION).put(tag, bigDecimal);
                    } else {
                        resultMap.get(DataConstants.START).put(tag, new BigDecimal(0));
                        resultMap.get(DataConstants.END).put(tag, new BigDecimal(0));
                        resultMap.get(DataConstants.CONSUMPTION).put(tag, new BigDecimal(0));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            });
        }

        Map<String, LinkedHashMap> result = new LinkedHashMap<>();
        formula.forEach(f -> {
            List<String> noSernumber = new ArrayList<>();
            List<String> fm = handleVirtualFormula(f);
            fm.forEach(fo -> {
                noSernumber.add(fo);
            });
            result.put(f, new LinkedHashMap());
            resultMap.entrySet().forEach(time -> {
                result.get(f).put(time.getKey(), handleDataByFormula(time.getValue(), noSernumber));
            });
        });
        return result;
    }


    @Override
    public String getLastDataVirtual(String virtualId, Map<String, String> data, DeviceId deviceId) {
        try {
            Map<String, String> cover = new HashMap<>();
            data.entrySet().forEach(d -> {
                cover.put(UUIDConverter.fromTimeUUID(deviceId.getId()) + "." + d.getKey(), d.getValue());
            });
            //不确定的设备号，先从已有的Map获取，如果没有，再进行查询
            Virtual virtual = virtualDao.findByVirtualId(virtualId).get();
            List<String> formulaList = handleVirtualFormula(virtual.getFormula());
            String formula = "";
            String result = null;
            for (String fou : formulaList) {
                if (isMathSymbol(fou)) {
                    formula = formula + fou;
                } else {
                    if (cover.get(fou) != null) {
                        formula = formula + cover.get(fou);
                    } else
                        formula = formula + cover.get("0");
                }
            }
            result = new BigDecimal(String.valueOf(AviatorEvaluator.execute(formula))).toPlainString();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<Virtual> findVirtualByProjectId(String projectId) {
        return virtualDao.findVirtualByProjectId(projectId);
    }

}
