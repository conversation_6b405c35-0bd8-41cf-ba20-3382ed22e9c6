<template>
  <div class="chart-wrapper">
    <VChart :option="state.option"></VChart>
  </div>
</template>
<script lang="ts" setup>
import { graphic } from 'echarts'
import { dayjs } from 'element-plus'
import { GetFactoryFlowList } from '@/api/mapservice/dma/statistics'

const state = reactive<{
  option: any
}>({
  option: {
    tooltip: {
      trigger: 'axis',

      axisPointer: {
        lineStyle: {
          color: '#57617B'
        }
      },
      formatter(params) {
        let relVal = params[0].name
        for (let i = 0; i < params.length; i++) {
          relVal
            += '<br/>'
            + params[i].marker
            + params[i].seriesName
            + ' : '
            + params[i].value
            + '(m3/h)'
        }
        return relVal
      }
    },
    legend: {
      // icon: 'rect',
      // itemWidth: 14,
      // itemHeight: 5,
      // left: 'center',
      // itemGap: 13,
      // // data: Data.legend,
      // right: '4%',
      // textStyle: {
      //   fontSize: 12,
      //   color: '#F1F1F3'
      // }
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: 20,
      right: 20,
      top: 40,
      bottom: 20,
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#57617B'
          }
        },
        data: []
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '万m³',
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#57617B'
          }
        },
        axisLabel: {
          margin: 10,
          textStyle: {
            fontSize: 14,
            color: '#57617B'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#57617B'
          }
        }
      }
    ],
    series: []
  }
})

const refreshData = () => {
  const params = {
    start: dayjs().startOf('day').format('x'),
    end: dayjs().endOf('day').format('x'),
    queryType: 'day'
  }
  GetFactoryFlowList(params)
    .then(res => {
      const series: any[] = []
      res.data.data.tableInfo.forEach(element => {
        // x坐标处理
        if (element.columnName === '数据时间') {
          state.option.xAxis[0].data = res.data.data.tableDataList.map(
            i => i[element.columnValue]
          )
          return
        }
        const item = {
          name: element.columnName,
          unit: 'cs',
          type: 'line',
          symbol: 'none',
          smooth: true,
          symbolSize: 10,
          lineStyle: {
            width: 1
          },
          areaStyle: {
            color: new graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(137, 189, 27, 0.3)'
                },
                {
                  offset: 0.8,
                  color: 'rgba(137, 189, 27, 0)'
                }
              ],
              false
            ),
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10
          },
          itemStyle: {
            color: 'rgb(137,189,27)'
          },
          data: res.data.data.tableDataList.map(i => i[element.columnValue])
        }
        series.push(item)
      })

      state.option.series = series
    })
    .catch(() => {
      //
    })
}

onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
