<!-- 高级查询 -->
<template>
  <RightDrawerMap ref="refMap" title="高级查询" @map-loaded="onMaploaded">
    <template #right-title>
      <SchemeHeader
        :title="'高级查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template>
    <Form ref="refForm" :config="FormConfig"></Form>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic';
import { useGisStore } from '@/store';
import {
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper';
import { queryLayerClassName } from '@/api/mapservice';
import { SLMessage } from '@/utils/Message';
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import {GetFieldConfig as GetFieldConfigByGeoserver,GetFieldValueByGeoserver,QueryByPolygon} from '@/utils/geoserver/wfsUtils'

import RightDrawerMap from '../common/RightDrawerMap.vue';
import SchemeHeader from './Scheme/SchemeHeader.vue';
import SchemeManage from './Scheme/SchemeManage.vue';
import SaveScheme from './Scheme/SaveScheme.vue';
import { useScheme, useSketch } from '@/hooks/arcgis';

const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const refForm = ref<IFormIns>();
const state = reactive<{
  tabs: any[];
  curOperate: string;
  layerInfos: any[];
  layerIds: any[];
  curFieldNode?: any;
}>({
  tabs: [],
  curOperate: '',
  layerInfos: [],
  layerIds: []
});
const staticState: {
  view?: __esri.MapView;
  graphics?: __esri.Graphic;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value]);
            }
          }
        }
      ]
    },
    {
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layerid?.length) return;
            const layerid = row.layerid[0];
            if(GIS_SERVER_SWITCH){
              GetFieldConfigByGeoserver(layerid).then(res=>{
                config.data = res.data.featureTypes[0].properties;
              })
            }else{
              const layerName = state.layerInfos.find(
                (item) => item.layerid === layerid
              )?.layername;
              if (!layerName) return;
              const fields = await GetFieldConfig(layerName);
              config.data = fields.data?.result?.rows;
            }
          },
          setDataBy: 'layerid',
          displayField: GIS_SERVER_SWITCH ? 'name' : 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curFieldNode = node;
            if(GIS_SERVER_SWITCH){
              appendSQL(`"${node.name}"`);
            }else{
              appendSQL(node.name);
            }
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.curOperate === 'uniqueing'
                          ? '正在获取唯一值'
                          : '获取唯一值',
                      loading: () => state.curOperate === 'uniqueing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除',
              type: 'danger',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            },
            {
              perm: true,
              text: () =>
                state.curOperate === 'detailing' ? '正在查询' : '查询',
              disabled: () => state.curOperate === 'detailing',
              loading: () => state.curOperate === 'detailing',
              click: () => startSearch(),
              styles: {
                width: '100%'
              }
            },
            {
              perm: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,
              text: '保存方案',
              click: () => handleSaveScheme(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});
const initDraw = (type: any) => {
  if (!staticState.view) return;
  sketch.value?.create(type);
  staticState.graphicsLayer?.removeAll();
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.graphics = undefined;
};

const getLayerInfo = () => {
  if(GIS_SERVER_SWITCH){
    const field = FormConfig.group[1].fields[0] as IFormTree
    const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
    let layers = layerInfo.items.map(item => {
      return {
        label: item.name,
        value: item.name,
        // data: item
      }
    });
    field.options = layers;// [{ label: '管线类', value: -2, children: layers }]
    refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
  }else{
    state.layerIds = getSubLayerIds(staticState.view);
    queryLayerClassName(state.layerIds).then(layerInfo => {
      state.layerInfos = layerInfo.data?.result?.rows || [];
      const field = FormConfig.group[1].fields[0] as IFormTree;
      const points = state.layerInfos
        .filter((item) => item.geometrytype === 'esriGeometryPoint')
        .map((item) => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          };
        });
      const lines = state.layerInfos
        .filter((item) => item.geometrytype === 'esriGeometryPolyline')
        .map((item) => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          };
        });
      field &&
        (field.options = [
          { label: '管点类', value: -1, children: points, disabled: true },
          { label: '管线类', value: -2, children: lines, disabled: true }
        ]);
      refForm.value && (refForm.value.dataForm.layerid = []);
    })
  }
};

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};
const getUniqueValue = async () => {
  if (!state.curFieldNode) return;
  const layerid = refForm.value?.dataForm.layerid;
  if (!layerid?.length) {
    SLMessage.warning('请先选择一个图层');
    return;
  }
  state.curOperate = 'uniqueing';
  try {
    if(GIS_SERVER_SWITCH){
      GetFieldValueByGeoserver({
        layerName: layerid[0],
        fiedName:state.curFieldNode.name
      }).then(res=>{
        let data = res.data;
        // 创建一个空集合来存储唯一的MATERIAL值
        const uniqueMaterials = new Set();
        // 遍历特征数组
        data.features.forEach(feature => {
          // 添加MATERIAL值到集合中
          let val = feature.properties[state.curFieldNode.name];
          if (val === null) val = '';
          uniqueMaterials.add(val);
        });
        const extraFormItem = FormConfig.group.find(
          (item) => item.id === 'field-construct'
        )?.fields[0].extraFormItem;
        const field = extraFormItem && (extraFormItem[0] as IFormList);
        field && (field.data = uniqueMaterials);
      })
    }else{
      const res = await GetFieldUniqueValue({
        usertoken: useGisStore().gToken,
        layerid: layerid[0],
        f: 'pjson',
        field_name: state.curFieldNode.name
      });
      const extraFormItem = FormConfig.group.find(
        (item) => item.id === 'field-construct'
      )?.fields[0].extraFormItem;
      const field = extraFormItem && (extraFormItem[0] as IFormList);
      field && (field.data = res.data.result.rows);
    }
  } catch (error) {
    SLMessage.error('获取唯一值失败');
  }
  state.curOperate = '';
};

const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};
const startSearch = async () => {
  try {
    state.tabs.length = 0;
    state.curOperate = 'detailing';
    const layerIds = refForm.value?.dataForm.layerid;
    if (!layerIds?.length) {
      SLMessage.warning('请选择一个要查询的图层');
    } else {
      if(GIS_SERVER_SWITCH){
        QueryByPolygon(layerIds,staticState.graphics?.geometry?.rings[0],refForm.value?.dataForm?.sql).then(res => {
          let features = res.data.features;
          const uniqueValues = new Set(); // 使用Set来自动去重
          features.forEach(item => {
            if (item['id'].split('.')[0] !== undefined) {
              uniqueValues.add(item['id'].split('.')[0]);
            }
          });
          let tabs = Array.from(uniqueValues); // 将Set转换为数组
          tabs.forEach(tab => {
            let data = features.filter(item => item.id.split('.')[0] === tab)
            state.tabs.push({name:tab,label:`${tab}(${data.length})`,data:data})
          })
          refMap.value?.refreshDetail(state.tabs)
        })
      }else{
        state.tabs = await getLayerOids(layerIds, state.layerInfos, {
          where: refForm.value?.dataForm?.sql || '1=1',
          geometry: staticState.graphics?.geometry
        });
        refMap.value?.refreshDetail(state.tabs);
      }
    }
  } catch (error: any) {
    SLMessage.error(error.message);
  }
  state.curOperate = '';
};

const scheme = useScheme('manual');
const handleSaveScheme = () => {
  if (!refForm.value?.dataForm.layerid?.length) {
    SLMessage.warning('请选择一个要查询的图层');
    return;
  }
  scheme.openSaveDialog();
};
const handleUseScheme = async (row: any) => {
  const detail = scheme.parseScheme(row);
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || [];
    refForm.value.dataForm.sql = detail.sql;
  }
  if (detail.graphic) {
    staticState.graphics = Graphic.fromJSON(detail.graphic);
    sketch.value?.cancel();
    staticState.graphicsLayer?.removeAll();
    staticState.graphicsLayer?.add(staticState.graphics);
  }
  startSearch();
};
const handleSchemeSubmit = (params) => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      graphic: staticState.graphics,
      sql: refForm.value?.dataForm.sql
    })
  });
};
const { initSketch, destroySketch, sketch } = useSketch();
const resolveDrawEnd = (res: ISketchHandlerParameter) => {
  if (res.state === 'complete') {
    staticState.graphics = res.graphics[0];
    console.log(JSON.stringify(staticState.graphics));
  }
};
const onMaploaded = async (view) => {
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-manual',
    title: '高级查询'
  });
  initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
  setTimeout(() => {
    getLayerInfo();
  },1000)
};
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll();
  staticState.graphicsLayer?.destroy();
  destroySketch();
});
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
</style>
