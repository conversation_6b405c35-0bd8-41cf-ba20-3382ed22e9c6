package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class CircuitConfigSaveRequest extends SaveRequest<CircuitConfig> {

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    @NotNullOrEmpty
    private String type;

    // 巡检项目分类
    private String itemType;

    // 巡检配置名称
    @NotNullOrEmpty
    private String name;

    // 巡检方法
    @NotNullOrEmpty
    private String method;

    // 巡检要求
    @NotNullOrEmpty
    private String require;

    protected CircuitConfig build() {
        CircuitConfig entity = new CircuitConfig();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    protected CircuitConfig update(String id) {
        CircuitConfig entity = new CircuitConfig();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitConfig entity) {
        entity.setType(type);
        entity.setItemType(itemType);
        entity.setName(name);
        entity.setMethod(method);
        entity.setRequire(require);
    }
}