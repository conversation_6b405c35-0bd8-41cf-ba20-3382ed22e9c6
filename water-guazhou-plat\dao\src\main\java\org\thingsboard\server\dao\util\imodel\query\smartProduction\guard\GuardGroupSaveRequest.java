package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroup;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class GuardGroupSaveRequest extends ComplexSaveRequest<GuardGroup, GuardGroupPartnerSaveRequest> {
    // 地点id
    @NotNullOrEmpty
    private String placeId;

    // 班组名称
    @NotNullOrEmpty
    private String name;

    // 班组编号
    @NotNullOrEmpty
    private Integer serialNo;

    // 班组部门id
    @NotNullOrEmpty
    private String departmentId;

    // 值班长id
    @NotNullOrEmpty
    private String head;


    @Override
    protected GuardGroup build() {
        GuardGroup entity = new GuardGroup();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardGroup update(String id) {
        GuardGroup entity = new GuardGroup();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(GuardGroup entity) {
        entity.setPlaceId(placeId);
        entity.setName(name);
        entity.setSerialNo(serialNo);
        entity.setDepartmentId(departmentId);
        entity.setHead(head);
    }

    @Override
    protected StringSetter<GuardGroupPartnerSaveRequest> parentSetter() {
        return GuardGroupPartnerSaveRequest::setGroupId;
    }

}