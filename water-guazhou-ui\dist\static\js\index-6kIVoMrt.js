import{d as X,M as W,c as k,r as E,a8 as b,s as B,D,u as Y,S,y as Z,bF as _,bT as ee,bu as te,g as R,n as ae,q as V,i as T,F as ie,h as se,an as re,p as le,cU as ne,b7 as oe,bM as de}from"./index-r0dFAfgr.js";import{_ as ue}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as ce}from"./CardTable-rdWOL4_6.js";import{_ as pe}from"./CardSearch-CB_HNR-Q.js";import{I as A}from"./common-CvK_P_ao.js";import{r as me,s as fe,v as w,f as ge,k as be,l as ye,c as he,m as xe,n as Te}from"./waterInspection-DqEu1Oyl.js";import{u as Ie}from"./useDepartment-BkP08hh6.js";import{u as De}from"./useStation-DJgnSZIA.js";import{f as C}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Ue={class:"wrapper"},Pe=X({__name:"index",setup(ve){const{$messageSuccess:I,$messageError:U}=W(),{getAllStationOption:z}=De(),{getDepartmentTree:G}=Ie(),{$btnPerms:J}=W(),y=k(),F=k([]),c=k(),$=k(),N=k(),q=[{label:"项目分类",prop:"itemType"},{label:"项目名称",prop:"name"},{label:"巡检方法",prop:"method"},{label:"巡检要求",prop:"require"}],l=E({departmentTree:[],templateList:[],stationOptionList:[],searchAuditUserDepartment:{},searchExecutionUserDepartment:{},searchAuditUsers:[],searchExecutionUsers:[],resultEnum:{PENDING:"未审核",RECEIVED:"未审核",VERIFY:"未审核",APPROVED:"合格",REJECTED:"不合格"},code:"",imageUrl:"",counter:"000100"}),h=E({filters:[{label:"任务编号",field:"code",type:"input"},{label:"任务名称",field:"name",type:"input"},{label:"任务类型",field:"taskType",type:"select",options:[{label:"临时任务",value:"临时任务"},{label:"常规任务",value:"常规任务"}]},{label:"巡检部门",field:"executionUserDepartmentId",type:"select-tree",checkStrictly:!0,options:b(()=>l.departmentTree),onChange:async e=>{var a,s,i;const t=(a=h.filters)==null?void 0:a.find(r=>r.field==="executionUserId");if(e){const r=await L(e);t.options=r}else t.options=[];h.defaultParams={...h.defaultParams,...(s=y.value)==null?void 0:s.queryParams,executionUserDepartmentId:e,executionUserId:""},(i=y.value)==null||i.resetForm()}},{label:"巡检人员",field:"executionUserId",type:"select",placeholder:"请选择巡检人员"},{label:"巡检泵站",field:"stationId",type:"select",multiple:!0,options:b(()=>l.stationOptionList)},{label:"预计开始",field:"startTime",type:"date"},{label:"预计完成",field:"endTime",type:"date"},{label:"实际开始",field:"realStartTime",type:"date"},{label:"实际完成",field:"realEndTime",type:"date"},{label:"任务状态",field:"status",type:"select",options:[{label:"待接收",value:"PENDING"},{label:"处理中",value:"RECEIVED"},{label:"已审核",value:"VERIFY"}]},{xs:11,type:"select-tree",label:"审核部门",clearable:!1,checkStrictly:!0,options:b(()=>l.departmentTree),field:"auditUserDepartmentId",rules:[{required:!0,message:"请选择巡检部门"}],onChange:async e=>{var t,a;e?l.searchAuditUsers=await L(e):l.searchAuditUsers=[],h.defaultParams={...h.defaultParams,...(t=y.value)==null?void 0:t.queryParams,auditUserDepartmentId:e,auditUserId:""},(a=y.value)==null||a.resetForm()}},{xs:11,type:"select",label:"审核人员",field:"auditUserId",options:b(()=>l.searchAuditUsers),rules:[{required:!0,message:"请选择审核人员"}]},{label:"审核结果",field:"status",type:"select",options:[{label:"通过",value:"APPROVED"},{label:"拒绝",value:"REJECTED"}]}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:A.QUERY,click:()=>m()},{type:"default",perm:!0,text:"重置",svgIcon:B(oe),click:()=>{var e;h.defaultParams={},(e=y.value)==null||e.resetForm()}},{perm:J("RoleManageAdd"),text:"新增",type:"success",icon:A.ADD,click:()=>{n.title="新增",P()}}]}]}),p=E({loading:!0,defaultExpandAll:!0,indexVisible:!0,selectList:[],columns:[{label:"任务编号",prop:"code",minWidth:140},{label:"创建时间",prop:"createTime",formatter:e=>C(e.createTime,"YYYY-MM-DD"),minWidth:140},{label:"任务名称",prop:"name",minWidth:140},{label:"任务类型",prop:"taskType",minWidth:140},{label:"巡检人员",prop:"executionUserName",minWidth:140},{label:"巡检泵站",prop:"stationName",minWidth:140},{label:"预计开始",prop:"startTime",formatter:(e,t)=>C(t,"YYYY-MM-DD"),minWidth:140},{label:"预计完成",prop:"endTime",formatter:(e,t)=>C(t,"YYYY-MM-DD"),minWidth:140},{label:"实际开始",prop:"realStartTime",formatter:(e,t)=>C(t,"YYYY-MM-DD"),minWidth:140},{label:"实际完成",prop:"realEndTime",formatter:(e,t)=>C(t,"YYYY-MM-DD"),minWidth:140},{label:"任务状态",prop:"statusName",minWidth:140},{label:"审核部门",prop:"auditUserDepartmentName",minWidth:140},{label:"审核人员",prop:"auditUserName",minWidth:140},{label:"审核结果",prop:"status",formatter:(e,t)=>l.resultEnum[t],minWidth:140}],operationWidth:"280px",operationFixed:"right",operations:[{type:"primary",isTextBtn:!0,perm:!0,text:"查看",click:e=>{n.title="查看",P(e,!0)}},{type:"primary",isTextBtn:!0,perm:!0,text:"接收",disabled:e=>{var a,s,i;const t=D(((i=(s=(a=Y())==null?void 0:a.user)==null?void 0:s.id)==null?void 0:i.id)||"");return!(e.executionUserId===t&&e.status==="PENDING")},click:e=>{S("确定接收任务？","提示信息").then(()=>{me(e.id).then(()=>{m(),I("接收成功")})}).catch(()=>{})}},{type:"primary",isTextBtn:!0,perm:!0,text:"提交审核",disabled:e=>{var a,s,i;const t=D(((i=(s=(a=Y())==null?void 0:a.user)==null?void 0:s.id)==null?void 0:i.id)||"");return!(e.executionUserId===t&&e.status==="RECEiVED")},click:e=>{S("确定提交任务审核？","提示信息").then(()=>{fe(e.id,e.auditUserId).then(()=>{m(),I("提交成功")}).catch(()=>{U("提交失败")})}).catch(()=>{})}},{type:"primary",isTextBtn:!0,perm:!0,text:"审核",disabled:e=>{var a,s,i;const t=D(((i=(s=(a=Y())==null?void 0:a.user)==null?void 0:s.id)==null?void 0:i.id)||"");return!(e.auditUserId===t&&e.status==="VERIFY")},click:e=>{Z.confirm("审核是否通过","提示",{confirmButtonText:"通过",cancelButtonText:"驳回",distinguishCancelAndClose:!0,type:"warning"}).then(()=>{w(e.id,!0).then(()=>{m(),I("提交成功")}).catch(()=>{U("提交失败")})}).catch(t=>{t==="cancel"&&w(e.id,!1).then(()=>{m(),I("提交成功")}).catch(()=>{U("提交失败")})})}},{isTextBtn:!0,type:"danger",perm:!0,text:"删除",disabled:e=>{var a,s,i;const t=D(((i=(s=(a=Y())==null?void 0:a.user)==null?void 0:s.id)==null?void 0:i.id)||"");return!(e.creator===t&&e.status==="PENDING")},click:e=>Q(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{p.pagination.page=e,p.pagination.limit=t,m()}}}),j=E({title:"附件信息",labelWidth:"130px",dialogWidth:500,group:[]}),n=E({title:"新增",labelWidth:"130px",defaultValue:{taskType:"临时任务",code:l.code},group:[{fields:[{xs:16,type:"input",label:"任务编号",readonly:!0,clearable:!1,field:"code",rules:[{required:!0,message:"请输入任务名称"}]},{xs:8,type:"btn-group",field:"codeButton",btns:[{text:"获取编号",perm:!0,click:()=>{var t,a,s,i;const e=M();n.defaultValue={...n.defaultValue,...(a=(t=c.value)==null?void 0:t.refForm)==null?void 0:a.dataForm,code:e},(i=(s=c.value)==null?void 0:s.refForm)==null||i.resetForm()}}]},{xs:8,type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请输入任务名称"}]},{xs:8,type:"select",label:"任务类型",field:"taskType",readonly:!0,options:[{label:"临时任务",value:"临时任务"},{label:"常规任务",value:"常规任务"}],rules:[{required:!0,message:"请选择任务类型"}]},{xs:8,type:"select-tree",checkStrictly:!0,label:"巡检部门",options:b(()=>l.departmentTree),field:"executionUserDepartmentId",onChange:async e=>{var a,s,i,r;const t=n.group[0].fields.find(o=>o.field==="executionUserId");if(e){const o=await L(e);t.options=o}else t.options=[];n.defaultValue={...n.defaultValue,...(s=(a=c.value)==null?void 0:a.refForm)==null?void 0:s.dataForm,executionUserId:""},(r=(i=c.value)==null?void 0:i.refForm)==null||r.resetForm()},rules:[{required:!0,message:"请选择巡检部门"}]},{xs:8,type:"select",label:"巡检人员",field:"executionUserId",rules:[{required:!0,message:"请选择人员名称"}]},{xs:8,type:"date",label:"预计时间",field:"startTime",min:_().format("YYYY-MM-DD"),rules:[{required:!0,message:"请输入预计时间"}],onChange:e=>{var a,s,i,r;const t=n.group[0].fields.find(o=>o.field==="endTime");console.log(t),t.min=e,n.defaultValue={...n.defaultValue,...(s=(a=c.value)==null?void 0:a.refForm)==null?void 0:s.dataForm,endTime:""},(r=(i=c.value)==null?void 0:i.refForm)==null||r.resetForm()}},{xs:8,type:"date",label:"预计完成",field:"endTime",rules:[{required:!0,message:"请输入预计完成时间"}]},{xs:8,type:"select-tree",checkStrictly:!0,label:"审核部门",field:"auditUserDepartmentId",options:b(()=>l.departmentTree),onChange:async e=>{var a,s,i,r;const t=n.group[0].fields.find(o=>o.field==="auditUserId");e?t.options=await L(e):t.options=[],n.defaultValue={...n.defaultValue,...(s=(a=c.value)==null?void 0:a.refForm)==null?void 0:s.dataForm,auditUserId:"",auditUserDepartmentId:e},(r=(i=c.value)==null?void 0:i.refForm)==null||r.resetForm()},rules:[{required:!0,message:"请选择审核部门"}]},{xs:8,type:"select",label:"审核人员",field:"auditUserId",rules:[{required:!0,message:"请选择审核人员"}]},{xs:8,type:"select",label:"巡检泵房",field:"stationId",options:b(()=>l.stationOptionList),rules:[{required:!0,message:"请选择巡检泵房"}]},{xs:8,type:"select",label:"巡检模板",field:"templateId",clearable:!1,options:b(()=>l.templateList),onChange:e=>H(e),rules:[{required:!0,message:"请选择巡检模板"}]},{type:"table",field:"configList",config:{indexVisible:!0,height:"200px",dataList:[],columns:q,pagination:{hide:!0}}}]}]}),M=()=>{const e=new Date,t=e.getFullYear().toString()+(e.getMonth()+1).toString().padStart(2,"0")+e.getDate().toString().padStart(2,"0");l.counter||(l.counter=100);const a=l.counter.toString().padStart(6,"0");return l.counter++,t+a},P=async(e,t)=>{var o,g,x,f;n.group.map(d=>{d.fields.map(u=>{u.readonly=!!t,u.field==="taskType"||u.field==="code"?u.readonly=!0:u.field==="codeButton"&&(u.btns[0].disabled=!!e)})});const s=(g=(o=(await ge({page:1,size:999,type:"二供泵房"})).data)==null?void 0:o.data)==null?void 0:g.data;l.templateList=s==null?void 0:s.map(d=>({id:d.id,label:d.name,value:d.id}));const i=(x=n.group[0].fields)==null?void 0:x.find(d=>d.field==="configList");i.config.dataList=[];const r=JSON.parse(JSON.stringify(q));i.config.columns=r,e?(i.config.columns=r.concat([{label:"巡检结果",prop:"result"},{label:"结果备注",prop:"resultRemark"},{label:"附件",prop:"file",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",svgIcon:B(de),plain:!0,click:d=>{var u,v;(u=N.value)==null||u.openDialog(),F.value=(v=d.file)==null?void 0:v.split(","),l.imageUrl=F.value?F.value[0]:""}}]}}]),n.defaultValue={...e,startTime:_(e==null?void 0:e.startTime).format(),endTime:_(e==null?void 0:e.endTime).format(),auditUserId:e.auditUserName,executionUserId:e.executionUserName},await K(e.id)):n.defaultValue={taskType:"临时任务",code:M()},t?n.submit=void 0:n.submit=d=>{S("确定提交？","提示信息").then(()=>{d.type="二供泵房",d.id=e?e.id:null,delete d.configList,console.log(d),be(d).then(()=>{var u,v,O;(v=(u=c.value)==null?void 0:u.refForm)==null||v.resetForm(),(O=c.value)==null||O.closeDialog(),I("提交成功"),m()}).catch(()=>{U("提交失败")})}).catch(()=>{})},(f=c.value)==null||f.openDialog()},Q=e=>{S("确定删除巡检任务, 是否继续?","删除提示").then(()=>{ye(e.id).then(()=>{m(),I("删除成功")}).catch(t=>{U(t.data.message)})})},H=async e=>{var r,o,g;const s=(o=(r=(await he({page:1,size:9999,type:"二供泵房",templateId:e})).data)==null?void 0:r.data)==null?void 0:o.data,i=(g=n.group[0].fields)==null?void 0:g.find(x=>x.field==="configList");i.config.dataList=s},K=async e=>{var o,g,x;const s=(g=(o=(await xe({page:1,size:999,mainId:e,type:"二供泵房"})).data)==null?void 0:o.data)==null?void 0:g.data,i=s==null?void 0:s.map(f=>({...f,method:f.itemMethod,name:f.itemName,require:f.itemRequire})),r=(x=n.group[0].fields)==null?void 0:x.find(f=>f.field==="configList");r.config.dataList=i},m=async()=>{var s,i,r;p.loading=!0;const e=((s=y.value)==null?void 0:s.queryParams)||{createTime:[]},t={...e,type:"二供泵房",startTime:e.startTime?_(e.startTime).startOf("day").valueOf():null,endTime:e.endTime?_(e.endTime).endOf("day").valueOf():null,page:p.pagination.page||1,size:p.pagination.limit||20,stationId:e.stationId?e.stationId.join(","):""},a=await Te(t);p.pagination.total=(i=a.data)==null?void 0:i.data.total,p.dataList=(r=a.data)==null?void 0:r.data.data,p.loading=!1},L=async e=>{var i,r;const a=(r=(i=(await ee({pid:e,status:1,page:1,size:999})).data)==null?void 0:i.data)==null?void 0:r.data;return a==null?void 0:a.map(o=>({id:D(o.id.id),label:o.firstName,value:D(o.id.id)}))};return te(async()=>{l.departmentTree=await G(2),l.stationOptionList=await z("泵站"),await m()}),(e,t)=>{const a=pe,s=ce,i=ue,r=ne;return R(),ae("div",Ue,[V(a,{ref_key:"refSearch",ref:y,config:T(h)},null,8,["config"]),V(s,{ref_key:"refTable",ref:$,config:T(p),class:"card-table"},null,8,["config"]),V(i,{ref_key:"refDialogForm",ref:c,config:T(n)},null,8,["config"]),V(i,{ref_key:"refImg",ref:N,config:T(j)},{default:ie(()=>[T(l).imageUrl?(R(),se(r,{key:0,style:{width:"440px",height:"400px"},src:T(l).imageUrl,"preview-src-list":T(F),"initial-index":1,fit:"cover"},null,8,["src","preview-src-list"])):re("",!0),t[0]||(t[0]=le("div",{class:"",style:{"text-align":"center"}},"暂无信息",-1))]),_:1},8,["config"])])}}});export{Pe as default};
