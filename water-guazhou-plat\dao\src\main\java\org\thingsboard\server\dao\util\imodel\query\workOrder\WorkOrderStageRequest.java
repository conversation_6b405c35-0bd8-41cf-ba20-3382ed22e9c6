package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.*;
import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.PROCESSING;

@Getter
@Setter
public class WorkOrderStageRequest extends SaveRequest<WorkOrderDetail> {

    // 处理备注
    private String processRemark;

    // 处理详情。json格式的处理详情，用于保存处理流转中的除备注外的字段的数据
    private String processAdditionalInfo;

    // 下一步处理人
    private String nextProcessUserId;

    // 目标状态
    private String stage;

    public WorkOrderStatus stage() {
        return valueOf(stage);
    }

    @Override
    public String valid(IStarHttpRequest request) {
        try {
            if (!isNullOrEmpty(stage))
                valueOf(stage);
        } catch (IllegalArgumentException ignore) {
            return "非法的工单状态";
        }
        return null;
    }

    public WorkOrderDetail process(WorkOrder order, boolean forceStepProcessUser) {
        // region order
        WorkOrderStatus parsedStage = stage();
        WorkOrderStatus orderStage = order.stage();
        WorkOrderStatus toStage = parsedStage.getToStage();
        if (!(orderStage.equals(PROCESSING) && toStage.equals(ARRIVING))) {
            order.setStatus(toStage);
        }

        order.setUpdateTime(new Date());
        if (parsedStage.equals(SUBMIT)) {
            order.setCompleteTime(new Date());
        } else if (parsedStage.equals(REJECTED)) {
            order.setCompleteTime(null);
        }

        // 指定下一步处理人 在完成状态需要强制设置为 ⌈初始指派的用户⌋
        String nextProcessUserId = StringUtils.isNullOrEmpty(this.nextProcessUserId) ? order.getStepProcessUserId() : this.nextProcessUserId;
        order.setStepProcessUserId(nextProcessUserId);
        // endregion

        // region detail
        WorkOrderDetail detail = new WorkOrderDetail();
        detail.setMainId(order.getId());
        detail.setType(parsedStage);
        // 记录当前处理用户为处理人
        detail.setProcessUserId(currentUserUUID());
        detail.setProcessTime(new Date());
        detail.setProcessRemark(processRemark);
        detail.setProcessAdditionalInfo(processAdditionalInfo);
        // 下一步处理人为主表处理人ID
        detail.setNextProcessUserId(nextProcessUserId);
        // endregion
        return detail;
    }

    @Override
    public WorkOrderDetail build() {
        return null;
    }

    @Override
    public WorkOrderDetail update(String id) {
        return null;
    }
}
