package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄核数据副本-抄表数据
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_COPY_DATA_CORRECT_RECORDS_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeCopyDataCorrectRecords {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_COPY_DATA_CORRECT_RECORDS_COPY_ID)
    private String copyId;

    @TableField(exist = false)
    private String ym;

    @TableField(exist = false)
    private String custCode;

    @TableField(exist = false)
    private String custName;

    @TableField(exist = false)
    private String phone;

    @TableField(ModelConstants.PIPE_COPY_DATA_CORRECT_RECORDS_CORRECT_WATER)
    private BigDecimal correctWater;

    @TableField(ModelConstants.PIPE_COPY_DATA_CORRECT_RECORDS_OLD_VALUE)
    private BigDecimal oldValue;

    @TableField(ModelConstants.PIPE_COPY_DATA_CORRECT_RECORDS_NEW_VALUE)
    private BigDecimal newValue;

    @TableField(ModelConstants.TYPE)
    private String type;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.CREATOR)
    private String creator;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
