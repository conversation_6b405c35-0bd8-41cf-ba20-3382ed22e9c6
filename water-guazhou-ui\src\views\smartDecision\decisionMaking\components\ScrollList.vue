<template>
  <div class="card">
    <div class="title">
      <span>指标项</span>
      <span>年度累计值</span>
      <span>同比</span>
    </div>
    <VueScroll
      :data="props.data"
      :class-option="optionHover"
      class="warp"
    >
      <ul class="item_view">
        <li
          v-for="(item, i) in props.data"
          :key="i"
          class="title"
        >
          <span class="jcdfx-item__child jcdfx-item__desc">{{
            item.name
          }}</span>
          <span class="jcdfx-item__child jcdfx-item__value">{{
            item.value
          }}</span>
          <span class="jcdfx-item__child jcdfx-item__date">{{
            item.scale
          }}</span>
        </li>
      </ul>
    </VueScroll>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{ data: any[] }>()

const optionHover = {
  step: 0.2,
  limitMoveNum: 5
}
</script>

<style lang="scss" scoped>
.card {
  width: 100%;
  font-size: 16px;
  height: 140px;
  .warp {
    height: 112px;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
}
.over_hide {
  overflow: hidden;
}
.title {
  width: 100%;
  height: 28px;
  line-height: 28px;
  display: flex;
  font-size: 14px;
  span {
    width: 100%;
    text-align: center;
    justify-content: space-around;
  }
}
.item_view {
  margin: 0;
  list-style: none;
  padding: 0;
  li {
    line-height: 24px;
    &:nth-child(odd) {
      background-color: rgba(128, 188, 253, 0.4);
    }
  }
}
</style>
