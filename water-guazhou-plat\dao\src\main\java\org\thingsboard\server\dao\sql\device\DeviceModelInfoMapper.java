package org.thingsboard.server.dao.sql.device;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-07-24
 */
@Mapper
public interface DeviceModelInfoMapper extends BaseMapper<DeviceModelInfoEntity> {

    IPage<DeviceModelInfoEntity> getList(IPage<DeviceModelInfoEntity> page, @Param("param") DeviceModelInfoEntity param);

    DeviceModelInfoEntity getByDeviceId(@Param("deviceId") String deviceId);
}
