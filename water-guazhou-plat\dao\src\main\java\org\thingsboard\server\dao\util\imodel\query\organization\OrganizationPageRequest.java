package org.thingsboard.server.dao.util.imodel.query.organization;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.TimeableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryLike;

@Getter
@Setter
public class OrganizationPageRequest extends TimeableQueryEntity {
    // 供水单位名称
    @QueryLike
    private String name;

    // 单位类型
    private String parentId;
}
