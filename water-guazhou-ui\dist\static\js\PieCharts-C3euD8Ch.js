import{u as F}from"./useDetector-BRcb7GRN.js";import{d as U,r as w,am as z,c as k,o as N,ay as P,g as d,n as f,aB as j,aJ as q,p as L,q as A,bh as I,i as J,j as R,C as T}from"./index-r0dFAfgr.js";const W={class:"chart"},G={class:"title"},H=U({__name:"PieCharts",props:{data:{}},setup($){const r=$,s=(n=[],l="m³")=>{const u=n.map(t=>t.value).reduce((t,c)=>t+c,0);return{tooltip:{trigger:"item",formatter:t=>`${t.marker} ${t.name} ${t.value} ${l}`},legend:{type:"scroll",icon:"circle",orient:"vertical",top:"center",right:20,textStyle:{color:"#318DFF"}},series:[{type:"pie",radius:["35%","50%"],center:["30%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:0,borderColor:"#fff",borderWidth:2},label:{position:"center",color:R().isDark?"#fff":"#333",formatter:()=>`合计

${u}${l}`},labelLine:{show:!1},data:n}]}},m=w({chartOptions:[{title:"上月供水量占比",option:s()},{title:"上月售水量占比",option:s()},{title:"上月大用户水量占比",option:s()},{title:"昨日小区漏失情况统计",option:s([],"个")}]}),O=()=>{var c,v,y,_,S,g,C,x,M,b,B,D;const n=(y=(v=(c=r.data)==null?void 0:c.lastMonthSupply)==null?void 0:v.x)==null?void 0:y.map((i,p)=>{var e,a,o;return{value:(o=(a=(e=r.data)==null?void 0:e.lastMonthSupply)==null?void 0:a.y)==null?void 0:o[p],name:i}}),l=(g=(S=(_=r.data)==null?void 0:_.lastMonthSale)==null?void 0:S.x)==null?void 0:g.map((i,p)=>{var e,a,o;return{value:(o=(a=(e=r.data)==null?void 0:e.lastMonthSale)==null?void 0:a.y)==null?void 0:o[p],name:i}}),u=(M=(x=(C=r.data)==null?void 0:C.lastBigUserSupply)==null?void 0:x.x)==null?void 0:M.map((i,p)=>{var e,a,o;return{value:(o=(a=(e=r.data)==null?void 0:e.lastBigUserSupply)==null?void 0:a.y)==null?void 0:o[p],name:i}}),t=(D=(B=(b=r.data)==null?void 0:b.lastLossEvaluate)==null?void 0:B.x)==null?void 0:D.map((i,p)=>{var e,a,o;return{value:(o=(a=(e=r.data)==null?void 0:e.lastLossEvaluate)==null?void 0:a.y)==null?void 0:o[p],name:i}});m.chartOptions=[{title:"上月供水量占比",option:s(n)},{title:"上月售水量占比",option:s(l)},{title:"上月大用户水量占比",option:s(u)},{title:"昨日小区漏失情况统计",option:s(t,"个")}]};z(()=>r.data,()=>O());const h=k(),E=k(),V=F();return N(()=>{V.listenToMush(h.value,()=>{var n;(n=E.value)==null||n.map(l=>{l.resize()})})}),(n,l)=>{const u=P("VChart");return d(),f("div",{ref_key:"refDiv",ref:h,class:"pie-charts overlay-y"},[(d(!0),f(j,null,q(J(m).chartOptions,(t,c)=>(d(),f("div",{key:c,class:"chart-item"},[L("div",W,[A(u,{ref_for:!0,ref:"refChart",option:t.option},null,8,["option"])]),L("div",G,I(t.title),1)]))),128))],512)}}}),X=T(H,[["__scopeId","data-v-1c9984d7"]]);export{X as default};
