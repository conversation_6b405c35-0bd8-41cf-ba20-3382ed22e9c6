package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoConstructionApplyFlowPageRequest extends AdvancedPageableQueryEntity<SoConstructionApplyFlow, SoConstructionApplyFlowPageRequest> {
    @NotNullOrEmpty
    private String constructionApplyCode;

}
