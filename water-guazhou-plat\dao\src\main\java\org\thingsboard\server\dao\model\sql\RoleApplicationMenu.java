package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.CUSTOMER_APPLICATION_MENU_ROLE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class RoleApplicationMenu {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.CUSTOMER_APPLICATION_MENU_ROLE_MENU_ID)
    private String menuId;

    @Column(name = ModelConstants.CUSTOMER_APPLICATION_MENU_ROLE_ROLE_ID)
    private String roleId;

    @Column(name = ModelConstants.CUSTOMER_APPLICATION_MENU_ROLE_APPLICATION_ID)
    private String tenantApplicationId;

}
