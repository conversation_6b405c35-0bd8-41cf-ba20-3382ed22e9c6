<template>
  <div class="basic-info overlay-y">
    <FieldSet
      title="昨日水量信息"
      :type="'simple'"
    ></FieldSet>
    <div class="list waterinfo">
      <div
        v-for="(item, i) in state.waterInfo"
        :key="i"
        class="list-item"
      >
        <label
          class="label"
          for=""
        >{{ item.label + ':' }}</label><span class="value">{{ item.value }}</span>
      </div>
    </div>
    <FieldSet
      title="昨日小流信息"
      :type="'simple'"
    ></FieldSet>
    <div class="list flow">
      <div
        v-for="(item, i) in state.minFlowInfo"
        :key="i"
        class="list-item"
      >
        <label
          class="label"
          for=""
        >{{ item.label + ':' }}</label><span class="value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  dataDma?: Record<string, any>
}>()
const state = reactive<{
  waterInfo: { label: string; value: string }[]
  minFlowInfo: { label: string; value: string }[]
}>({
  waterInfo: [],
  minFlowInfo: []
})
const initData = () => {
  state.waterInfo = [
    {
      label: '用户类型',
      value: props.dataDma?.userType
    },
    { label: '供水量', value: `${props.dataDma?.supply || 0} m³` },
    { label: '售水量', value: `${props.dataDma?.sale || 0} m³` },
    { label: '产销差水量', value: `${props.dataDma?.nrw || 0} m³` },
    { label: '产销差', value: props.dataDma?.nrwRate },
    { label: '用户数', value: `${props.dataDma?.userNum || 0} 户` },
    { label: '大用户用水量', value: `${props.dataDma?.bigUserWater || 0} m³` },
    { label: '其它用水量', value: `${props.dataDma?.otherWater || 0} m³` }
  ]
  state.minFlowInfo = [
    {
      label: '夜间最小流量',
      value: `${props.dataDma?.minNightFlow || 0} m³/h`
    },
    { label: '夜间最小流时间', value: props.dataDma?.minNightFlowTime },
    { label: '最小水量', value: `${props.dataDma?.minValue || 0} m³` },
    { label: '最小水量时间', value: props.dataDma?.minValueTime },
    { label: '预估漏损水量', value: `${props.dataDma?.lossWater || 0} m³` },
    { label: '管道长度', value: `${props.dataDma?.pipeLength || 0} km` },
    {
      label: 'MNF/日均流量',
      value: props.dataDma?.mnfDivDayAvgFlow
    },
    {
      label: '单位管长净夜间流量',
      value: `${props.dataDma?.unitPipeNightFlow || 0} m³/h/km`
    },
    { label: '漏失等级', value: props.dataDma?.lossLevel }
  ]
}
onBeforeMount(() => {
  initData()
})
watch(
  () => props.dataDma,
  () => initData()
)
</script>
<style lang="scss" scoped>
.basic-info {
  height: 100%;
  padding: 0 20px;
  .list {
    display: flex;
    &.waterinfo {
      flex-direction: row;
      flex-wrap: wrap;
      .list-item {
        width: 50%;
      }
    }
    &.flow {
      flex-direction: column;
    }
    .list-item {
      padding: 8px 12px;

      .label {
        color: var(--el-text-color-placeholder);
        font-size: 14px;
        padding-right: 12px;
      }
      .value {
        font-size: 14px;
        color: #00bcd4;
        font-weight: bold;
      }
    }
  }
}
</style>
