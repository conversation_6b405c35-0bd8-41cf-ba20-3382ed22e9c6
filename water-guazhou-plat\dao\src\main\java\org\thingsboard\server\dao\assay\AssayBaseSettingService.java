package org.thingsboard.server.dao.assay;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.AssayBaseSettingListRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayBaseSetting;

import java.util.List;

public interface AssayBaseSettingService {
    /**
     * 分页查询数据
     *
     * @param request  请求参数
     * @param tenantId 租户ID
     * @return 数据
     */
    PageData<AssayBaseSetting> findList(AssayBaseSettingListRequest request, TenantId tenantId);

    void save(AssayBaseSetting entity, TenantId tenantId);

    void remove(List<String> ids);
}
