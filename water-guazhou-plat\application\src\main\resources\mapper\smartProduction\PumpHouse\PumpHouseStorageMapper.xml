<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpHouseStorageMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           code,
                           name,
                           nickname,
                           company_name,
                           supply_method,
                           water_box_num,
                           address,
                           install_user_name,
                           install_date,
                           collection_frequency,
                           storage_frequency,
                           remark,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_pump_house_storage -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="nickname" property="nickname"/>
        <result column="company_name" property="companyName"/>
        <result column="supply_method" property="supplyMethod"/>
        <result column="water_box_num" property="waterBoxNum"/>
        <result column="address" property="address"/>
        <result column="install_user_name" property="installUserName"/>
        <result column="install_date" property="installDate"/>
        <result column="collection_frequency" property="collectionFrequency"/>
        <result column="storage_frequency" property="storageFrequency"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_pump_house_storage
        <where>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="nickname != null and nickname != ''">
                and nickname like '%' || #{nickname} || '%'
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name like '%' || #{companyName} || '%'
            </if>
            <if test="supplyMethod != null and supplyMethod != ''">
                and supply_method like '%' || #{supplyMethod} || '%'
            </if>
            <if test="waterBoxNum != null and waterBoxNum != ''">
                and water_box_num = #{waterBoxNum}
            </if>
            <if test="installUserName != null and installUserName != ''">
                and install_user_name like '%' || #{installUserName} || '%'
            </if>
            <if test="installDateFrom != null">
                and install_date >= #{installDateFrom}
            </if>
            <if test="installDateTo != null">
                and install_date &lt;= #{installDateTo}
            </if>
            <if test="collectionFrequency != null">
                and collection_frequency = #{collectionFrequency}
            </if>
            <if test="storageFrequency != null">
                and storage_frequency = #{storageFrequency}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_pump_house_storage
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="nickname != null">
                nickname = #{nickname},
            </if>
            <if test="companyName != null">
                company_name = #{companyName},
            </if>
            <if test="supplyMethod != null">
                supply_method = #{supplyMethod},
            </if>
            <if test="waterBoxNum != null">
                water_box_num = #{waterBoxNum},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="installUserName != null">
                install_user_name = #{installUserName},
            </if>
            <if test="installDate != null">
                install_date = #{installDate},
            </if>
            <if test="collectionFrequency != null">
                collection_frequency = #{collectionFrequency},
            </if>
            <if test="storageFrequency != null">
                storage_frequency = #{storageFrequency},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO sp_pump_house_storage(id,
                                          code,
                                          name,
                                          nickname,
                                          company_name,
                                          supply_method,
                                          water_box_num,
                                          address,
                                          install_user_name,
                                          install_date,
                                          collection_frequency,
                                          storage_frequency,
                                          remark,
                                          creator,
                                          create_time,
                                          tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.code},
             #{element.name},
             #{element.nickname},
             #{element.companyName},
             #{element.supplyMethod},
             #{element.waterBoxNum},
             #{element.address},
             #{element.installUserName},
             #{element.installDate},
             #{element.collectionFrequency},
             #{element.storageFrequency},
             #{element.remark},
             #{element.creator},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update sp_pump_house_storage
        <set>
            code                 = valueTable.code,
            name                 = valueTable.name,
            nickname             = valueTable.nickname,
            company_name         = valueTable.companyName,
            supply_method        = valueTable.supplyMethod,
            water_box_num        = valueTable.waterBoxNum,
            address              = valueTable.address,
            install_user_name    = valueTable.installUserName,
            install_date         = valueTable.installDate,
            collection_frequency = valueTable.collectionFrequency,
            storage_frequency    = valueTable.storageFrequency,
            remark               = valueTable.remark,
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.code},
             #{element.name},
             #{element.nickname},
             #{element.companyName},
             #{element.supplyMethod},
             #{element.waterBoxNum},
             #{element.address},
             #{element.installUserName},
             #{element.installDate},
             #{element.collectionFrequency},
             #{element.storageFrequency},
             #{element.remark})
        </foreach>
        ) as valueTable(id, code, name, nickname, companyName, supplyMethod, waterBoxNum, address, installUserName,
                        installDate, collectionFrequency, storageFrequency, remark)
        where id = valueTable.id
    </update>

    <select id="getPumpRoomCode" resultType="java.lang.String">
        select code
        from sp_pump_house_storage
        where id = #{id}
    </select>

    <select id="getNameById" resultType="java.lang.String">
        select name
        from sp_pump_house_storage
        where id = #{id}
    </select>
</mapper>