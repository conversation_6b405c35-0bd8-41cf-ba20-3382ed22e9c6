package org.thingsboard.server.dao.model.sql.deviceManage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("m_device_type")
public class DeviceType implements Identifiable {
    @TableId(type = IdType.ASSIGN_UUID)
    @InfoViaMapper(name = "treePath", mapper = DeviceTypeMapper.class)
    private String id;

    // 序列号
    private String serialId;

    // 类别名称
    private String name;

    // 排序，升序
    private Integer orderNum;

    // 备注
    private String remark;

    // 节点级别
    private String level;

    // 父ID
    @ParseViaMapper(DeviceTypeMapper.class)
    private String parentId;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;


    // region 类别属性
    // 设备型号
    private String model;

    // 设备名称
    private String deviceName;

    // 设备标签
    private String label;

    // 计量单位
    private String unit;

    // 保养周期，单位：天
    private Integer maintenanceCycle;

    // 最小库存
    private Double minStock;

    // 设备图片，多张用逗号分隔
    private String images;

    // 设备附件，多张用逗号分隔
    private String files;

    // endregion
    
    public DeviceType() {
        
    }

    public DeviceType(String serialId, String name, Integer orderNum, String level, Date createTime) {
        this.serialId = serialId;
        this.name = name;
        this.orderNum = orderNum;
        this.level = level;
        this.createTime = createTime;
    }

    private void customizeMap(ResponseMap map) {
        if (level != null && level.equals("0"))
            map.put("treePath", "/");
    }

}
