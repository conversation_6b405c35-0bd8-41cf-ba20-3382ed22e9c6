package org.thingsboard.server.dao.production;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.VO.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 智慧生产通用接口
 */
public interface ProductionService {

    /**
     * 查询指定站点类型的供水基本信息
     * 包含：本日、昨日、本月供水量
     *
     * @param name      站点名称
     * @param type      站点类型
     * @param projectId 项目ID
     * @param tenantId  租户ID
     */
    List<StationWaterSupplyVO> getWaterSupplyInfo(String name, String type, String projectId, TenantId tenantId);

    /**
     * 查询指定站点的供水信息
     * 包含：本日供水量曲线、昨日供水量曲线、本月供水量曲线啊
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    JSONObject getWaterSupplyDetail(String stationId, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的指定属性列表的曲线数据
     *
     * @param stationId 站点ID
     * @param groupType 查询的属性分组
     * @param attrList  要查询的属性列表
     * @param queryType 数据间隔
     * @param start     开始时间
     * @param end       结束时间
     * @param tenantId  租户ID
     * @return 数据MAP
     */
    Map<String, List<JSONObject>> getStationData(String stationId, String groupType, List<String> attrList, String queryType, Date start, Date end, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定类型的站点供水量总览
     *
     * @param type      站点类型
     * @param projectId 项目ID
     * @param tenantId  租户ID
     * @return 供水量总览
     */
    StationWaterSupplyViewVO getWaterSupplyInfoView(String type, String projectId, TenantId tenantId);

    /**
     * 查询供水报表，可按日、月、年查询
     * 日：按小时返回数据
     * 月：按日返回数据
     * 年：按月返回数据
     *
     * @param stationId 站点ID
     * @param start     查询的时间范围开始时间
     * @param end       查询的时间范围结束时间
     * @param queryType 报表类型
     * @param tenantId  租户ID
     * @return 报表数据
     */
    DynamicTableVO getWaterSupplyReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询能耗报表，可按日、月、年查询
     * 日：按小时返回数据
     * 月：按日返回数据
     * 年：按月返回数据
     *
     * @param stationId 站点ID
     * @param start     查询的时间范围开始时间
     * @param end       查询的时间范围结束时间
     * @param queryType 报表类型
     * @param tenantId  租户ID
     * @return 报表数据
     */
    DynamicTableVO getEnergyMethodReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询供水报表，可按日、月、年查询
     * 日：按小时返回数据
     * 月：按日返回数据
     * 年：按月返回数据
     *
     * @param stationId 站点ID
     * @param start     查询的时间范围开始时间
     * @param end       查询的时间范围结束时间
     * @param queryType 报表类型
     * @param tenantId  租户ID
     * @return 报表数据
     */
    DynamicTableVO getWaterSupplyConsumptionReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询供水报表，可按日、月、年查询
     * 日：按小时返回数据
     * 月：按日返回数据
     * 年：按月返回数据
     *
     * @param stationType   站点类型
     * @param stationIdList 站点ID列表
     * @param start         查询的时间范围开始时间
     * @param end           查询的时间范围结束时间
     * @param queryType     报表类型
     * @param tenantId      租户ID
     * @return 报表数据
     */
    DynamicTableVO getWaterSupplyDetailReport(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId, boolean count) throws ThingsboardException;

    /**
     * 查询指定站点列表的进出水数据
     * 需计算进出水差值、差值率
     *
     * @param stationType   站点类型
     * @param stationIdList 站点ID列表
     * @param start         查询的时间范围开始时间
     * @param end           查询的时间范围结束时间
     * @param tenantId      租户ID
     * @return 报表数据
     */
    List<StationWaterOutletAndInletDataVO> getWaterOutletAndInletReport(String stationType, List<String> stationIdList, Long start, Long end, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点类型的站点列表的电耗数据
     * 包含：本期供水量、本期耗电量、本期上期吨水电耗、吨水电耗差值、变化率
     *
     * @param stationType 站点类型
     * @param start       查询的时间范围开始时间
     * @param end         查询的时间范围结束时间
     * @param queryType   分析类型。日、月、年
     * @param name        按名称模糊查询站点列表
     * @param tenantId    租户ID
     * @return 数据
     */
    List<StationWaterSupplyAndEnergyDataVO> getWaterSupplyAndEnergyData(String stationType, Long start, Long end, String queryType, String name, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的电耗数据详情
     * 包含：上期吨水电耗曲线、本期吨水电耗曲线、上期供水量曲线、本期供水量曲线、上期耗电量曲线、本期耗电量曲线
     *
     * @param stationId 站点ID
     * @param queryType 查询的数据类型
     * @param start     查询的时间范围开始时间
     * @param end       查询的时间范围结束时间
     * @param tenantId  租户ID
     * @return 数据
     */
    Object getWaterSupplyAndEnergyDataDetail(String stationId, String queryType, Long start, Long end, TenantId tenantId) throws ThingsboardException;

    /**
     * 平衡分析
     * 查询指定站点的指定时间范围内的进出厂累计流量并计算水量差、水量差百分比
     * 报表类型：
     * 日 查指定日，按小时返回数据
     * 月 查指定月，按日返回数据
     * 年 查指定年，按月返回数据
     *
     * @param start     查询时间范围的开始时间
     * @param end       查询时间范围的结束时间
     * @param queryType 报表类型
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    List<StationWaterOutletAndInletDataVO> getBalanceReport(Long start, Long end, String queryType, String stationId, TenantId tenantId) throws ThingsboardException;

    /**
     * 能耗分析
     * 查询指定站点类型的所有站点在指定时间范围内的供水量、总电流、吨水电耗
     *
     * @param stationType 站点类型
     * @param start       查询时间范围的开始时间
     * @param end         查询时间范围的结束时间
     * @param queryType   报表类型
     * @param tenantId    租户ID
     * @return 数据
     */
    List<StationWaterSupplyAndEnergyDataVO> getEnergyReport(String stationType, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的具体时间区间的能耗数据
     *
     * @param stationId 站点ID
     * @param start     查询时间范围的开始时间毫秒值
     * @param end       查询时间范围的结束时间毫秒值
     * @param queryType 查询类型
     * @param tenantId  租户ID
     * @return 数据
     */
    List<WaterSupplyVO> getEnergyDetailReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询出场流量指标数据，支持同比环比定基比
     * 包含：同比环比定基比数据、最大值最小值等统计数据
     *
     * @param stationId   　站点ID
     * @param queryType   　查询类型。日、月、年
     * @param time        　时间
     * @param compareType 　１：同比；２：环比；３：定基比（仅日分析有该选项）
     * @param tenantId    　租户ID
     * @return 数据
     */
    Object getWaterSupplyFlowReport(String stationId, String queryType, String time, String compareType, TenantId tenantId) throws Exception;

    /**
     * 查询出场压力指标数据，支持同比环比定基比
     * 包含：同比环比定基比数据、最大值最小值等统计数据
     *
     * @param stationId   站点ID
     * @param attributeId 查询属性项ID
     * @param queryType   查询类型。日、月、年
     * @param time        时间
     * @param compareType 1：同比；2：环比；3：定基比（仅日分析有该选项）
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getWaterSupplyPressureReport(String stationId, String attributeId, String queryType, String time, String compareType, TenantId tenantId) throws Exception;

    /**
     * 查询出场压力指标数据，支持同比环比定基比
     * 包含：同比环比定基比数据、最大值最小值等统计数据
     *
     * @param stationId   站点ID
     * @param attrType    要查询的水质项
     * @param queryType   查询类型。日、月、年
     * @param time        时间
     * @param compareType 1：同比；2：环比；3：定基比（仅日分析有该选项）
     * @param tenantId    租户ID
     * @return 数据
     */
    Object getWaterSupplyQualityReport(String stationId, String attrType, String queryType, String time, String compareType, TenantId tenantId) throws Exception;

    /**
     * 查询指定站点列表的进出水流量
     * 列数据为：水厂的进/出水累计流量
     * <p>
     * 数据包含：进出水数据、最大最小值以及对应时间统计、平均值、合计
     *
     * @param stationType   站点类型
     * @param stationIdList 站点ID列表
     * @param queryType     报表类型。日、月、年
     * @param time          时间点
     * @param tenantId      租户ID
     * @return 数据
     */
    DynamicTableVO getWaterPlantFlowReport(String stationType, List<String> stationIdList, String queryType, String time, TenantId tenantId) throws Exception;

    /**
     * 查询指定站点的指定属性分组的数据列表
     *
     * @param stationId 站点ID
     * @param groupType 属性分组
     * @param queryType 报表类型。日、月、年
     * @param time      时间点
     * @param tenantId  租户ID
     * @return 数据
     */
    DynamicTableVO getWaterPlantProductionReport(String stationId, String groupType, String queryType, String time, TenantId tenantId) throws Exception;

    /**
     * 查询指定类型站点的供水概要
     * 包含：运行时长、供水量、耗电量、吨水电耗
     *
     * @param stationType 站点类型
     * @param start       查询时间范围的开始时间
     * @param end         查询时间范围的结束时间
     * @param queryType   查询类型
     * @param name        站点名称模糊查询
     * @param tenantId    租户ID
     * @return 数据
     */
    List<WaterSupplyVO> getWaterSupplyOverview(String stationType, Long start, Long end, String queryType, String name, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的供水量和耗电趋势曲线数据
     *
     * @param stationId 站点ID
     * @param start     查询时间范围的开始时间
     * @param end       查询时间范围的结束时间
     * @param queryType 查询类型
     * @param tenantId  租户ID
     * @return 数据
     */
    Object getWaterSupplyOverviewTrend(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点的取/供水信息
     * 包含：今日取水、今日供水、昨日供水、本月供水
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    WaterInfoVO getWaterInfo(String stationId, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询状态查询指定类型的站点列表数据
     * 包含：站点id、站点名称、数据最后更新时间、当前状态
     *
     * @param stationType 站点类型
     * @param projectId   项目ID
     * @param name        站点名称模糊查询
     * @param status      按状态查询
     * @param tenantId    租户ID
     * @return 数据
     */
    List<StationStatusVO> getList(String stationType, String projectId, String name, String status, TenantId tenantId);

    /**
     * 产销差
     * 使用应收录入数据进行产销差计算
     *
     * @param stationId 站点ID
     * @param year      年份
     * @param tenantId  租户ID
     * @return 数据
     */
    Object productionAndSales(String stationId, String year, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点列表的供水原始数据
     *
     * @param stationType   站点类型
     * @param stationIdList 站点列表
     * @param start         开始时间
     * @param end           结束时间
     * @param queryType     查询类型
     * @param tenantId      租户ID
     * @return 数据
     */
    Object getWaterSupplyDetailOriginalData(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点列表的供水压力原始数据
     *
     * @param stationType   站点类型
     * @param stationIdList 站点列表
     * @param start         开始时间
     * @param end           结束时间
     * @param queryType     查询类型
     * @param tenantId      租户ID
     * @return 数据
     */
    Object getWaterSupplyPressureOriginalData(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询水厂月供水量
     *
     * @param stationType   站点类型
     * @param stationIdList 站点列表
     * @param tenantId      租户ID
     * @return 数据
     */
    Object waterPlantMonthTotal(String stationType, List<String> stationIdList, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询站点指定时间内的数据
     *
     * @param stationId 站点ID
     * @param attr      变量
     * @param queryType 时间间隔
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param tenantId  租户ID
     * @return 数据
     */
    Object getStationDataByAttr(String stationId, String attr, String queryType, Long startTime, Long endTime, TenantId tenantId) throws ThingsboardException;

    /**
     * 查询指定站点列表的执行属性数据
     * <p>
     * 数据包含：进出水数据、最大最小值以及对应时间统计、平均值、合计
     *
     * @param stationType   站点类型
     * @param stationIdList 站点ID列表
     * @param attr          查询的属性
     * @param queryType     报表类型。日、月、年
     * @param time          时间点
     * @param tenantId      租户ID
     * @return 数据
     */
    DynamicTableVO getPointMonitor(String stationType, List<String> stationIdList, String attr, String queryType, String time, TenantId tenantId) throws Exception;

    /**
     * 查询供水报表，可按日、月、年查询
     * 日：按小时返回数据
     * 月：按日返回数据
     * 年：按月返回数据
     *
     * @param stationType   站点类型
     * @param stationIdList 站点ID列表
     * @param start         查询的时间范围开始时间
     * @param end           查询的时间范围结束时间
     * @param queryType     报表类型
     * @param tenantId      租户ID
     * @return 报表数据
     */
    DynamicTableVO getWaterProcessDetailReport(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException;

    StationWaterSupplyVO getWaterSupplyTotal(String time, String type, TenantId tenantId);

    BigDecimal getWaterSupplyTotalAll(String time, String name, TenantId tenantId);

    List<XiaofangshuanStatusVO> getListXiaofangshuan(String type, String projectId, String name, String status, TenantId tenantId);

    JSONObject count(String type, String projectId, String name, String status, TenantId tenantId);

    /**
     * 查询各个水厂站点的取/供水信息
     * 包含：今日取水、今日供水、今日最新出水瞬时流量
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 数据
     */
    Object getWaterSupplyDataInfo(String type, TenantId tenantId);

    /**
     * 查询水源地运行概况统计数据
     * 包含：取水量、用电量、吨水电耗、报警次数等关键指标
     *
     * @param start     查询时间范围的开始时间
     * @param end       查询时间范围的结束时间
     * @param queryType 查询类型（day/month/year）
     * @param projectId 项目ID
     * @param tenantId  租户ID
     * @return 运行概况数据
     */
    Object getWaterSourceOperationOverview(Long start, Long end, String queryType, String projectId, TenantId tenantId) throws ThingsboardException;

    /**
     * 曲线运行-获取实时数据
     * 包含：今日流量、出厂压力、来厂压力、泵站效率等实时数据
     *
     * @param stationId 站点ID
     * @param tenantId  租户ID
     * @return 实时数据
     */
    Object getCurveOperationData(String stationId, TenantId tenantId) throws ThingsboardException;

    /**
     * 曲线运行-获取预测数据
     * 基于历史数据预测未来流量和压力趋势
     *
     * @param type         预测类型（actual/predicted）
     * @param pressureType 压力类型（inlet/outlet）
     * @param stationId    站点ID
     * @param timeRange    时间范围（小时）
     * @param tenantId     租户ID
     * @return 预测数据
     */
    Object getCurveOperationPrediction(String type, String pressureType, String stationId, Integer timeRange, TenantId tenantId) throws ThingsboardException;

    /**
     * 曲线运行-获取出水流量历史数据
     *
     * @param stationId   站点ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param granularity 数据粒度（hour/day/month）
     * @param tenantId    租户ID
     * @return 历史流量数据
     */
    Object getOutletFlowHistory(String stationId, Long startTime, Long endTime, String granularity, TenantId tenantId) throws ThingsboardException;

    /**
     * 曲线运行-获取压力历史数据
     *
     * @param stationId    站点ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param pressureType 压力类型（inlet/outlet/both）
     * @param granularity  数据粒度（hour/day/month）
     * @param tenantId     租户ID
     * @return 历史压力数据
     */
    Object getPressureHistory(String stationId, Long startTime, Long endTime, String pressureType, String granularity, TenantId tenantId) throws ThingsboardException;

    /**
     * 曲线运行-获取水体药物浓度数据
     *
     * @param stationId 站点ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param drugType  药物类型
     * @param tenantId  租户ID
     * @return 药物浓度数据
     */
    Object getWaterConcentrationData(String stationId, Long startTime, Long endTime, String drugType, TenantId tenantId) throws ThingsboardException;
}
