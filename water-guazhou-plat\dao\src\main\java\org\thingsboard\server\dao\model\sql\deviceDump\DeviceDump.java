package org.thingsboard.server.dao.model.sql.deviceDump;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DeviceDump {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 报废单编码
    private String code;

    // 报废单标题
    private String name;

    // 报废申请上报时间
    private Date uploadTime;

    // 仓库ID
    private String storehouseId;

    // 仓库名称
    @TableField(exist = false)
    private String storehouseName;

    // 保费申请人
    @ParseUsername(withDepartment = true)
    private String uploadUserId;

    // 经办人
    @ParseUsername(withDepartment = true)
    private String handleUserId;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

    // 是否报废
    @TableField(exist = false)
    private Boolean isDump;

}
