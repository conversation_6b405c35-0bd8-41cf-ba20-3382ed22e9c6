package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.thingsboard.server.dao.model.sql.MedicineManage;

import java.util.List;

@Controller
@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface MedicineManageFeignClient {

    @GetMapping("api/medicineManage/all")
    List<MedicineManage> all(@RequestParam("stationId") String stationId);

    @GetMapping("api/medicineManage/findByTimeAndStationId")
    List<MedicineManage> findByTimeAndStationId(@RequestParam("startTime") Long startTime, @RequestParam("endTime") Long endTime,
                                                @RequestParam("stationId") String stationId);

}
