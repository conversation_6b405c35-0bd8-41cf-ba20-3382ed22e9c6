import{_ as f}from"./ArcLayout-CHnHL9Pv.js";import{_ as w}from"./index-BJ-QPYom.js";import g from"./DivisionDetail-DtNvJTHJ.js";import{D as v}from"./DrawerBox-CLde5xC8.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as D,c as b,r as x,o as h,g as T,h as y,F as s,q as c,i as p}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as L}from"./usePartition-DkcY9fQ2.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./useDetector-BRcb7GRN.js";import"./dma-SMxrzG7b.js";import"./SideDrawer-CBntChyn.js";import"./hookupDevice-Bcbk7s68.js";const fr=D({__name:"index",setup(P){const e=b(),o=x({data:[],isFilterTree:!0,title:"分区总览",treeNodeHandleClick(t){var m;(m=e.value)==null||m.toggleDrawer("btt",!0);const i=r.List.value.find(n=>n.id===t.id);o.currentProject={data:t,partition:i},i.geom&&r.extentToPartition(a.view,JSON.parse(i.geom))}}),a={},r=L(),u=async()=>{await r.getTree(),o.data=r.Tree.value||[]},_=async t=>{a.view=t,u(),r.refreshPartitions(a.view)};return h(async()=>{var t;(t=e.value)==null||t.toggleDrawer("rtl",!0),u()}),(t,i)=>{var l,d;const m=w,n=f;return T(),y(v,{ref_key:"refDrawerBox",ref:e,"right-drawer":!0,"bottom-drawer":!0,"bottom-drawer-title":(d=(l=p(o).currentProject)==null?void 0:l.data)==null?void 0:d.label,"bottom-drawer-bar-position":"left"},{right:s(()=>[c(m,{"tree-data":p(o)},null,8,["tree-data"])]),bottom:s(()=>[c(g,{data:p(o).currentProject,partitions:p(r).List.value},null,8,["data","partitions"])]),default:s(()=>[c(n,{ref:"refArcLayout",onMapLoaded:_},null,512)]),_:1},8,["bottom-drawer-title"])}}});export{fr as default};
