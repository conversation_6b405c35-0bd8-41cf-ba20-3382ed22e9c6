package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 呼叫来源报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-01-03
 */
@Data
@NoArgsConstructor
public class PartitionTreeDTO {

    private String id;

    private String deviceId;

    private String name;

    private String type;

    private List<PartitionTreeDTO> children = new ArrayList<>();

    public PartitionTreeDTO(String deviceId, String name, String type, String id) {
        this.id = id;
        this.deviceId = deviceId;
        this.name = name;
        this.type = type;
        this.children = new ArrayList<>();
    }
}
