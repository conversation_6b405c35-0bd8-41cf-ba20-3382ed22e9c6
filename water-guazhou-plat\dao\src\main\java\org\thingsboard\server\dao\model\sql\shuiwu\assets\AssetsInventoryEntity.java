package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.List;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSETS_INVENTORY_TABLE)
public class AssetsInventoryEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSETS_INVENTORY_INVENTORY_NO)
    private String inventoryNo;

    @Column(name = ModelConstants.ASSETS_INVENTORY_INVENTORY_NAME)
    private String inventoryName;

    @Column(name = ModelConstants.ASSETS_INVENTORY_EXPECT_INVENTORY_TIME)
    private Long expectInventoryTime;

    @Column(name = ModelConstants.ASSETS_INVENTORY_INVENTORY_PERSON_IDS)
    private String inventoryPersonIds;

    @Column(name = ModelConstants.ASSETS_INVENTORY_REMARK)
    private String remark;

    @Column(name = ModelConstants.ASSETS_INVENTORY_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ASSETS_INVENTORY_UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.ASSETS_INVENTORY_TENANT_ID)
    private String tenantId;

    private transient List<AssetsInventoryCEntity> inventoryCList;

    private transient String inventoryPersonNames;

    private transient String status;

}
