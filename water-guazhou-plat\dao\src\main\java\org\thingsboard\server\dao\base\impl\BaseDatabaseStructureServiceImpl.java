package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseDatabaseStructureService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure;
import org.thingsboard.server.dao.sql.base.BaseDatabaseStructureMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseStructurePageRequest;

/**
 * 公共管理-数据库结构修复Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class BaseDatabaseStructureServiceImpl implements IBaseDatabaseStructureService {

    @Autowired
    private BaseDatabaseStructureMapper baseDatabaseStructureMapper;

    /**
     * 查询公共管理-数据库结构修复
     *
     * @param id 公共管理-数据库结构修复主键
     * @return 公共管理-数据库结构修复
     */
    @Override
    public BaseDatabaseStructure selectBaseDatabaseStructureById(String id) {
        return baseDatabaseStructureMapper.selectBaseDatabaseStructureById(id);
    }

    /**
     * 查询公共管理-数据库结构修复列表
     *
     * @param baseDatabaseStructure 公共管理-数据库结构修复
     * @return 公共管理-数据库结构修复
     */
    @Override
    public IPage<BaseDatabaseStructure> selectBaseDatabaseStructureList(BaseDatabaseStructurePageRequest baseDatabaseStructure) {
        return baseDatabaseStructureMapper.selectBaseDatabaseStructureList(baseDatabaseStructure);
    }

    /**
     * 新增公共管理-数据库结构修复
     *
     * @param baseDatabaseStructure 公共管理-数据库结构修复
     * @return 结果
     */
    @Override
    public int insertBaseDatabaseStructure(BaseDatabaseStructure baseDatabaseStructure) {
        baseDatabaseStructure.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseDatabaseStructureMapper.insertBaseDatabaseStructure(baseDatabaseStructure);
    }

    /**
     * 修改公共管理-数据库结构修复
     *
     * @param baseDatabaseStructure 公共管理-数据库结构修复
     * @return 结果
     */
    @Override
    public int updateBaseDatabaseStructure(BaseDatabaseStructure baseDatabaseStructure) {
        return baseDatabaseStructureMapper.updateBaseDatabaseStructure(baseDatabaseStructure);
    }

    /**
     * 批量删除公共管理-数据库结构修复
     *
     * @param ids 需要删除的公共管理-数据库结构修复主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseStructureByIds(List<String> ids) {
        return baseDatabaseStructureMapper.deleteBaseDatabaseStructureByIds(ids);
    }

    /**
     * 删除公共管理-数据库结构修复信息
     *
     * @param id 公共管理-数据库结构修复主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseStructureById(String id) {
        return baseDatabaseStructureMapper.deleteBaseDatabaseStructureById(id);
    }
}
