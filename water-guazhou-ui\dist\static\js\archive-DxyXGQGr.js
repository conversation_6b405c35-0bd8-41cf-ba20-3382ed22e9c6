import{d as v,c as s,s as T,r as m,x as d,o as j,g as w,n as L,q as l,i as n,F as S,b6 as Y,b7 as A}from"./index-r0dFAfgr.js";import{_ as E}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as F}from"./CardTable-rdWOL4_6.js";import{_ as N}from"./CardSearch-CB_HNR-Q.js";import{I as _}from"./common-CvK_P_ao.js";import{b as M,c as O,d as B}from"./inspectionPeriod-CRLRDctF.js";import{f as b}from"./DateFormatter-Bm9a68Ax.js";import V from"./detail-DEo1RlcF.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./projectManagement-CDcrrCQ1.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./manage-BReaEVJk.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const P={class:"wrapper"},ke=v({__name:"archive",setup(R){const f=s(),p=s(),u=s(),h=s({filters:[{label:"项目编号",field:"projectCode",type:"input"},{label:"项目名称",field:"projectName",type:"input"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:_.DOWNLOAD,click:()=>{M().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","总归档.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:T(A),click:()=>{var e;(e=f.value)==null||e.resetForm(),r()}},{perm:!0,text:"查询",icon:_.QUERY,click:()=>r()}]}]}),o=m({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"项目编号",prop:"projectCode"},{label:"项目名称",prop:"projectName"},{label:"启动时间",prop:"projectStartTime",formatter:e=>b(e.projectStartTime,"YYYY-MM-DD")},{label:"总归档时间",prop:"archiveTime",formatter:e=>b(e.archiveTime,"YYYY-MM-DD")},{label:"工作状态",prop:"status",tag:!0,tagColor:e=>e.createTime===null?"#409EFF":"#67C23A",formatter:e=>e.createTime===null?"处理中":"已完成"}],operationWidth:"300px",operations:[{disabled:e=>!e.createTime,isTextBtn:!1,text:"详情",perm:!0,click:e=>{var t;g.selected=e,(t=u.value)==null||t.openDrawer()}},{disabled:e=>e.createTime!==null,isTextBtn:!1,type:"primary",text:"添加归档",perm:!0,click:e=>{y(e)}},{disabled:e=>e.createTime===null,isTextBtn:!1,type:"success",text:"编辑归档",perm:!0,click:e=>D(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,r()}}}),i=m({title:"添加归档",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{i.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),O(e).then(a=>{var c;i.submitting=!1,a.data.code===200?(d.success(t+"成功"),(c=p.value)==null||c.closeDialog(),r()):d.warning(t+"失败")}).catch(a=>{i.submitting=!1,d.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"项目编号",field:"projectCode"},{xs:12,type:"input",label:"项目名称",field:"projectName"},{xs:12,type:"date",label:"总归档时间",field:"archiveTime",format:"x"},{type:"textarea",label:"总归档说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),x=m({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),y=e=>{var t;i.title="添加归档",i.defaultValue={...e||{}},(t=p.value)==null||t.openDialog()},D=e=>{var t;i.title="编辑归档",i.defaultValue={...e||{}},(t=p.value)==null||t.openDialog()},g=m({selected:{}}),r=async()=>{var t;const e={size:o.pagination.limit||20,page:o.pagination.page||1,...((t=f.value)==null?void 0:t.queryParams)||{}};B(e).then(a=>{o.dataList=a.data.data.data||[],o.pagination.total=a.data.data.total||0})};return j(()=>{r()}),(e,t)=>{const a=N,c=F,C=E,k=Y;return w(),L("div",P,[l(a,{ref_key:"refSearch",ref:f,config:n(h)},null,8,["config"]),l(c,{config:n(o),class:"card-table"},null,8,["config"]),l(C,{ref_key:"refForm",ref:p,config:n(i)},null,8,["config"]),l(k,{ref_key:"refDetail",ref:u,config:n(x)},{default:S(()=>[l(V,{config:n(g).selected,show:16},null,8,["config"])]),_:1},8,["config"])])}}});export{ke as default};
