package org.thingsboard.server.dao.sql.report;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.ReportRequest;
import org.thingsboard.server.dao.model.sql.report.Report;

import java.util.List;

@Mapper
public interface ReportMapper extends BaseMapper<Report> {
    IPage<Report> getList(IPage<Report> page, @Param("param") ReportRequest request);

    void changeStatus(@Param("ids") List<String> ids, @Param("status") String status);

    void delete(@Param("ids") List<String> ids);
}
