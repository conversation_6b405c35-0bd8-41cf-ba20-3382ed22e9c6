import{d as k,c as f,r as y,o as b,g as x,n as C,q as g,i as u,t as I,ar as F,a1 as w,_ as M,C as N}from"./index-r0dFAfgr.js";import{C as B}from"./index-CcDafpIP.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";import{u as T}from"./useUserLocation-YYelk0G_.js";import"./index-0NlGN6gS.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./locas-Cxm3ID_S.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";/* empty css                                                                      */import"./UserLocatePop-CScyuy8U.js";import"./URLHelper-B9aplt5w.js";const U={class:"onemap-panel-wrapper"},V=k({__name:"repair",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(h,{emit:v}){const c=T(),a=v,i=h,p=f(),e=f([{label:"0 人",value:"抢修人员数"},{label:"0 人",value:"作业人员数"}]),s=y({group:[{fields:[{type:"select-tree",defaultExpandAll:!0,field:"departmentId",checkStrictly:!0,options:[],onChange:()=>n(),extraFormItem:[{type:"input",field:"userName",appendBtns:[{perm:!0,isTextBtn:!0,text:"刷新",click:()=>n()}]}]},{type:"tree",checkStrictly:!0,options:[],props:{label:"userName"},field:"user",nodeKey:"userId",nodeClick:o=>{a("highlightMark",i.menu,o==null?void 0:o.userId)}}]}],labelPosition:"top",gutter:12}),n=async()=>{var d;const o=((d=p.value)==null?void 0:d.dataForm)||{},t=await c.getLatestQIANGXIURENYUANCoords({...o}),r=s.group[0].fields[1];r&&(r.options=t.data);const m=c.generatePops(i.view,t.data,!1,l=>{a("highlightMark",i.menu,l)});m.map(l=>{l.attributes.path=i.menu.path}),e.value[0].label=t.total+" 人",e.value[1].label="0 人",a("addMarks",{windows:m})},_=()=>{F(2).then(o=>{var r;const t=s.group[0].fields[0];t&&(t.options=w(o.data.data||[],{label:"name",value:"id",id:"id",children:"children"})),p.value&&(p.value.dataForm.departmentId=(r=t.options)==null?void 0:r[0].value),n()})};return b(()=>{_()}),(o,t)=>{const r=M;return x(),C("div",U,[g(u(B),{modelValue:u(e),"onUpdate:modelValue":t[0]||(t[0]=m=>I(e)?e.value=m:null),span:12,style:{"margin-bottom":"10px"}},null,8,["modelValue"]),g(r,{ref_key:"refForm",ref:p,config:u(s)},null,8,["config"])])}}}),Zt=N(V,[["__scopeId","data-v-370611cc"]]);export{Zt as default};
