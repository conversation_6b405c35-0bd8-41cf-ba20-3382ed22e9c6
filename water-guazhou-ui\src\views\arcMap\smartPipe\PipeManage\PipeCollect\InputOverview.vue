<!-- 入库总览 -->
<template>
  <DrawerBox
    ref="refDrawer"
    :right-drawer="true"
    :right-drawer-width="350"
  >
    <ArcLayout @map-loaded="onMaploaded">
    </ArcLayout>
  </DrawerBox>
</template>
<script lang="ts" setup>
const refDrawer = ref<IDrawerBoxIns>()
const onMaploaded = (view: __esri.MapView) => {
  console.log(view)
}
onMounted(() => {
  refDrawer.value?.toggleDrawer('rtl', true)
})
</script>
<style lang="scss" scoped></style>
