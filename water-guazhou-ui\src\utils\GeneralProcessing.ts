import { OpUnitType } from 'dayjs';
import { ElMessage, ElMessageBox, dayjs } from 'element-plus';

interface Callback {
  (dictionary: Record<string, any>): void;
}

interface DictType {
  dictLabel: string;
  dictValue: string;
}

/**
 * 通用处理函数，用于执行提交或更新操作。
 * 根据参数中是否存在id，决定是进行新增还是编辑操作。
 *
 * @param params 传递给后端的参数对象，可能包含id等关键信息。
 * @param post 用于执行新增操作的函数。
 * @param put 用于执行更新操作的函数。
 * @param ref 一个对象，用于跟踪提交状态。如果未提供，则会创建一个默认对象。
 * @param param 可选参数，包含一个key属性，用于在params中查找特定的键值。
 * @returns 返回一个Promise，解析结果为后端返回的数据。
 */
export const GeneralProcessing = (
  params: any,
  post,
  put,
  ref?: any,
  param?: { key: string }
) =>
  new Promise((resolve) => {
    // 初始化ref对象的submitting属性为true，表示提交操作正在进行。
    if (!ref) {
      ref = { submitting: true };
    }
    ref.submitting = true;

    // 检查params中是否存在id或其他指定的键值，以决定是执行更新还是新增操作。
    if (params[param?.key || 'id']) {
      // 执行更新操作。
      put(params)
        .then((res) => {
          // 更新操作完成后，将submitting设置为false。
          ref.submitting = false;
          // 如果返回码为200，表示操作成功，给出成功提示。
          if (res.code === 200 || res.data.code == 200) {
            ElMessage.success('编辑成功');
            resolve(res);
          } else {
            // 如果操作失败，给出警告提示。
            ElMessage.warning(res.data.msg);
          }
        })
        .catch((error) => {
          // 如果更新操作出现错误，将submitting设置为false，并给出警告提示。
          ref.submitting = false;
          ElMessage.warning(error);
        });
    } else {
      // 如果params中没有id，执行新增操作。
      post(params)
        .then((res) => {
          // 新增操作完成后，将submitting设置为false。
          ref.submitting = false;
          // 如果返回码为200，表示操作成功，给出成功提示。
          if (res.code === 200 || res.data.code == 200) {
            ElMessage.success('新增成功');
            resolve(res);
          } else {
            // 如果操作失败，给出警告提示。
            ElMessage.warning(res.data.msg);
          }
        })
        .catch((error) => {
          // 如果新增操作出现错误，将submitting设置为false，并给出警告提示。
          ref.submitting = false;
          ElMessage.warning(error);
        });
    }
  });

/**
 * 提交通用请求的封装。
 * @param params 任意参数，作为请求的参数。
 * @param request 发送请求的函数。
 * @param ref 包含提交状态的对象。
 * @param msg 可选，请求成功时显示的默认消息。
 * @returns 返回一个Promise对象，根据请求结果进行resolve或reject。
 */
export const CommonRequest = (
  params: any,
  request,
  ref?: { submitting?: boolean },
  msg?: string
) =>
  new Promise((resolve, reject) => {
    ref && (ref.submitting = true); // 初始化提交状态为true
    request(params)
      .then((res) => {
        ref && (ref.submitting = false); // 更新提交状态为false
        if (res.data.code === 200) {
          ElMessage.success(msg || '操作成功'); // 请求成功，显示成功消息
          resolve(res);
        } else if (res.data.status === 200) {
          ElMessage.success(msg || '操作成功'); // 请求成功，显示成功消息
          resolve(res);
        } else {
          ElMessage.error(res.data.msg); // 请求成功但业务逻辑失败，显示错误消息
          reject(res);
        }
      })
      .catch((error) => {
        // 请求失败处理
        ElMessage.warning(error);
        ref && (ref.submitting = false); // 更新提交状态为false
        reject(error);
      });
  });

/**
 * 一个通用的删除操作封装，通过弹出确认框来提示用户进行删除操作。
 * @param params 传递给删除方法的参数。
 * @param del 执行删除操作的函数。
 * @param msg 确认框中显示的消息，默认为 '确认删除该记录?'。
 * @returns 返回一个Promise对象，成功时resolve删除操作的响应结果，失败时reject错误信息。
 */
export const UniversalDelete = (
  params: any,
  del,
  msg?: string,
  title?: string
) =>
  new Promise((resolve, reject) => {
    // 弹出确认删除的对话框
    ElMessageBox.confirm(
      (msg || '确认删除该记录') + ',是否继续?',
      title || '删除提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        // 用户确认删除后，调用删除函数
        del(params)
          .then((res) => {
            if (res.code === 200 || res.data.code === 200) {
              // 删除成功，显示成功消息，并解决Promise
              ElMessage.success('删除成功');
              resolve(res);
            } else if (res.data.status === 200) {
              // 删除成功，显示成功消息，并解决Promise
              ElMessage.success('删除成功');
              resolve(res);
            } else {
              // 删除失败，显示错误消息，并拒绝Promise
              ElMessage.error(res.data.msg);
              reject(res);
            }
          })
          .catch((error) => {
            // 删除操作中出现异常，显示警告消息，并拒绝Promise
            ElMessage.warning(error);
            reject(error);
          });
      })
      .catch((error) => {
        //
      });
  });

/**
 * 弹出通用对话框，根据用户选择执行相应操作。
 *
 * @param params 传递给后台请求的参数。
 * @param post 向后台发送请求的函数。
 * @param param 对话框的配置参数，包括类型、消息、标题和成功提示。
 * @returns 返回一个Promise，根据操作结果进行解析或拒绝。
 */
export const UniversalDialog = (
  params: any,
  post,
  param: {
    type: 'success' | 'info' | 'warning' | 'error';
    msg: string;
    title?: string;
    success?: string;
  }
) =>
  new Promise((resolve, reject) => {
    // 根据配置参数显示确认对话框
    ElMessageBox.confirm(param.msg + ',是否继续?', param.title || '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: param.type || 'warning'
    })
      .then(() => {
        // 发送请求，并根据响应结果进行处理
        post(params)
          .then((res) => {
            // 如果响应成功，显示成功消息并解析Promise
            if (res.data.code === 200 || res.data.status === 200) {
              ElMessage.success(param.success || '成功');
              resolve(res);
            } else {
              // 如果响应失败，显示错误消息并拒绝Promise
              ElMessage.error(res.data.msg);
              reject(res);
            }
          })
          .catch((error) => {
            // 如果请求失败，显示警告消息并拒绝Promise
            ElMessage.warning(error);
            reject(error);
          });
      })
      .catch((error) => {
        // 如果用户取消操作，不进行任何操作，直接解析Promise
        resolve(error);
      });
  });

/**
 * 根据给定的时间参数，生成时间范围。
 * 此函数主要用于处理和格式化开始时间和结束时间的参数。
 *
 * @param params 原始参数对象，可能包含时间相关的属性。
 * @param time 参数对象中表示时间的属性名。
 * @param start 可选参数，表示开始时间的属性名，默认为'start'。
 * @param end 可选参数，表示结束时间的属性名，默认为'end'。
 * @param format 可选参数，用于格式化时间的字符串。
 * @param scope 可选参数，表示时间单位的范围，用于开始时间和结束时间的格式化。
 * @returns 返回处理后的参数对象。
 */
export const getTimeRange = (
  params: any,
  time: string,
  start?: string,
  end?: string,
  format?: string,
  scope?: [OpUnitType, OpUnitType]
) => {
  // 检查params中是否有指定的时间属性，并且属性值是一个长度大于1的数组
  if (params[time] && params[time]?.length > 1) {
    const constTime = params[time];
    const [startTime, endTime] = constTime;
    // 如果同时指定了format和scope，则根据scope对开始和结束时间进行格式化
    if (format && scope) {
      params[start || 'start'] = dayjs(startTime)
        .startOf(scope[0])
        .format(format);
      params[end || 'end'] = dayjs(endTime).endOf(scope[1]).format(format);
      // 如果只指定了format，不涉及时间单位的范围，直接对时间进行格式化
    } else if (format) {
      params[start || 'start'] = dayjs(startTime).format(format);
      params[end || 'end'] = dayjs(endTime).format(format);
      // 如果既未指定format也未指定scope，直接使用原始时间值
    } else {
      params[start || 'start'] = startTime;
      params[end || 'end'] = endTime;
    }
    // 如果当前处理的时间属性不是开始时间也不是结束时间，则删除这个属性
    if (time !== start && time !== end) delete params[time];
  }
  // 返回处理后的参数对象
  return params;
};

/**
 * 根据给定的时间参数，调整并返回参数对象中的时间范围。
 * @param params 参数对象，可能包含时间信息。
 * @param time 参数对象中表示时间的键名。
 * @param start 可选参数，表示开始时间的键名，默认为'start'。
 * @param end 可选参数，表示结束时间的键名，默认为'end'。
 * @param type 可选参数，表示时间单位，如'day'、'hour'等。
 * @param format 可选参数，表示时间的格式化字符串。
 * @returns 返回调整后的参数对象。
 */
export const getSETimeRange = (
  params: any,
  time: string,
  start?: string,
  end?: string,
  type?: any,
  format?: string
) => {
  // 检查指定的时间参数是否存在且长度大于1
  // 检查params中是否存在指定时间信息，并且是一个长度大于1的数组
  if (params[time] && params[time]?.length > 1) {
    const constTime = params[time];
    // 解构出开始时间和结束时间
    // 解构出开始和结束时间
    const [startTime, endTime] = constTime;
    // 如果提供了格式化字符串，则格式化时间
    // 根据是否提供format参数，格式化或直接分配开始和结束时间到指定键名下
    if (format) {
      params[start || 'start'] = dayjs(startTime).startOf(type).format(format);
      params[end || 'end'] = dayjs(endTime).endOf(type).format(format);
    } else {
      // 否则，直接调整时间到指定的单位
      params[start || 'start'] = dayjs(startTime).startOf(type);
      params[end || 'end'] = dayjs(endTime).endOf(type);
    }
    // 如果当前时间键名不是开始或结束时间，则删除该键
    // 如果time键名不是start或end，则删除该原始时间键
    if (time !== start && time !== end) delete params[time];
  }

  // 返回调整后的参数对象
  return params; // 返回更新后的参数对象
};

export type ExportFunction<T> = (params: any) => Promise<T>;
/**
 * 一个通用的导出函数，支持异步导出功能，并通过浏览器下载方式提供导出文件。
 * @param exportFunction 导出功能的函数，该函数应返回一个Promise，其解析值包含导出的数据。
 * @param filename 导出文件的名称，默认为'导出文件.xlsx'。
 * @param params 导出函数需要的参数，可选。
 * @returns 返回一个Promise，当文件成功下载时解析为true，否则拒绝并返回错误信息。
 */
export const UniversalExport = <T>(
  exportFunction: ExportFunction<any>,
  filename?: string,
  params?: any
): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    // 参数校验
    if (typeof exportFunction !== 'function') {
      reject(new Error('exportFunction 必须是一个函数'));
      return;
    }
    if (typeof filename !== 'string') {
      reject(new Error('文件名必须是字符串'));
      return;
    }

    // 执行导出函数，并处理结果
    exportFunction(params)
      .then((res) => {
        if (!res.data) {
          reject(new Error('导出数据为空'));
          return;
        }

        try {
          // 创建导出数据的URL
          const url = window.URL.createObjectURL(res.data);
          console.log(url);

          // 创建隐藏的下载链接
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          // 设置下载文件名
          link.setAttribute('download', filename || `导出文件.xlsx`);

          // 将下载链接添加到页面以触发下载
          document.body.appendChild(link);
          link.click();

          // 下载完成后清理资源
          link.remove();
          window.URL.revokeObjectURL(url);
          ElMessage.success('下载成功');
          resolve(true);
        } catch (error) {
          // 处理下载过程中可能出现的异常
          reject(new Error('下载文件失败'));
        }
      })
      .catch((error) => {
        // 导出函数执行失败的处理
        ElMessage.warning(error);
        reject(error);
      });
  });
};

// 数组操作函数
export class ArrayOperations {
  array: any[];
  constructor() {
    this.array = [];
  }
  // 数据初始化
  init(arr: any[]) {
    this.array = arr;
    return this;
  }
  // 数据去重
  unique(arr: any[]) {
    this.array = Array.from(new Set(arr));
    return this;
  }
  // 数据删除
  delete(key: string | string[], value?: any) {
    // 判断key是否为数组
    if (Array.isArray(key)) {
      // 遍历key数组
      key.forEach((i) => {
        if (value) {
          this.array = this.array.filter((item) => item[i] !== value);
        } else {
          this.array = this.array.filter((item) => item !== i);
        }
      });
    } else {
      if (value) {
        this.array = this.array.filter((item) => item[key] !== value);
      } else {
        this.array = this.array.filter((item) => item !== key);
      }
    }
    return this;
  }
  // 添加并去重
  addunique(key: any) {
    this.array = Array.from(new Set([...this.array, key]));
    return this;
  }
}

export function cs(val) {
  return val;
}

interface GeneralConfig<> {
  /**
   * 表格配置项，用于控制表格的显示和加载状态
   */
  table?: ITable;
  /**
   * 消息提示，用于显示请求成功后的提示信息
   */
  msg?: string;
  /**
   * 关闭自动赋值，默认为false
   */
  n_assignment?: boolean;
}

/**
 * 通用表格数据处理函数
 * @param params 请求参数
 * @param get 获取数据的函数
 * @param config 配置项
 * @returns 处理后的表格数据
 */
export const GeneralTable = <T>(
  params: any,
  get: (params: any) => Promise<any>,
  config?: GeneralConfig
) =>
  new Promise<{ data: any[]; total: number }>((resolve, reject) => {
    // 根据config中的table配置，开启表格加载状态
    config?.table && (config.table.loading = true);

    // 发起请求获取数据
    get(params)
      .then((res: any) => {
        // 判断响应是否成功
        if (res.status === 200 || res.data.code === 200 || res.code === 200) {
          // 根据config中的msg配置，显示成功消息
          config?.msg && ElMessage.success(config.msg);

          // 处理响应数据，找到总数和数据列表
          const value = FindTotal(res.data, { data: [] as T[], total: 0 });

          // 更新表格的数据列表和总数
          config?.table &&
            !config?.n_assignment &&
            (config.table.dataList = value?.data || []);
          config?.table &&
            !config?.n_assignment &&
            (config.table.pagination.total = value?.total || 0);

          // 关闭表格加载状态
          config?.table && (config.table.loading = false);

          // 解析Promise，返回处理后的表格数据
          resolve(value);
        } else {
          // 显示请求失败的警告消息
          ElMessage.warning(res.data.msg);

          // 根据config中的table配置，重置表格信息
          config?.table &&
            !config?.n_assignment &&
            (config.table.dataList = []);

          // 关闭表格加载状态
          config?.table && (config.table.loading = false);

          // 拒绝Promise，返回响应对象
          reject(res);
        }
      })
      .catch((error) => {
        // 关闭表格加载状态
        config?.table && (config.table.loading = false);
        // 显示请求错误的警告消息
        ElMessage.warning(error);

        // 拒绝Promise，返回错误对象
        reject(error);

        // 在控制台打印错误详情
        console.log(error);
      });
  });

// 递归遍历对象
// 递归遍历对象
export const FindTotal: (
  val: any,
  key: any
) => { data: any[]; total: number } = (val: any, key: any) => {
  if (val?.total) {
    return val;
  } else if (val?.data) {
    return FindTotal(val?.data, key);
  } else {
    return {
      data: val?.length ? val : [],
      total: val?.length ? val?.length : 0
    };
  }
};

/**
 * 计算表格中单元格的合并属性。
 * 该函数用于根据表格配置和数据，确定单元格是否应该合并以及合并的范围。
 * @param TableConfig 表格的配置对象，包含列配置和数据列表。
 * @param row 当前行的数据对象。
 * @param column 当前列的配置对象。
 * @param rowIndex 当前行的索引。
 * @param columnIndex 当前列的索引。
 * @returns 返回一个对象，包含单元格的rowspan和colspan属性。
 */
export const ObjectSpanMethod = (
  TableConfig,
  row,
  column,
  rowIndex,
  columnIndex
) => {
  // 当索引列不显示时，处理单元格合并
  if (!TableConfig.indexVisible) {
    // 获取当前列的属性名
    const key = TableConfig.columns[columnIndex]?.prop;
    // 如果当前单元格的值不为空，且下一行相同列的值为空，则查找后续行中第一个不为空的值
    if (
      key &&
      row[key] !== null &&
      rowIndex + 1 < TableConfig.dataList.length &&
      TableConfig.dataList[rowIndex + 1][key] === null
    ) {
      for (let i = rowIndex + 1; i < TableConfig.dataList.length; i++) {
        if (TableConfig.dataList[i][key] !== null) {
          return {
            rowspan: i - rowIndex,
            colspan: 1
          };
        }
      }
      // 如果没有找到不为空的值，则当前单元格应跨至表格底部
      return { rowspan: TableConfig.dataList.length - rowIndex, colspan: 1 };
    } else if (key && row[key] === null) {
      // 如果当前单元格的值为空，则不进行合并
      return { rowspan: 0, colspan: 0 };
    }
  }
  // 当索引列显示时，处理单元格合并，但排除索引列本身
  else if (!(TableConfig.indexVisible && columnIndex === 0)) {
    // 获取当前列的前一列的属性名，因为索引列存在时，列索引需要向前调整一位
    const key = TableConfig.columns[columnIndex - 1]?.prop;
    // 同样的逻辑，判断当前单元格是否应该合并
    if (
      key &&
      row[key] !== null &&
      rowIndex + 1 < TableConfig.dataList.length &&
      TableConfig.dataList[rowIndex + 1][key] === null
    ) {
      for (let i = rowIndex + 1; i < TableConfig.dataList.length; i++) {
        if (TableConfig.dataList[i][key] !== null) {
          return {
            rowspan: i - rowIndex,
            colspan: 1
          };
        }
      }
      return { rowspan: TableConfig.dataList.length - rowIndex, colspan: 1 };
    } else if (key && row[key] === null) {
      return { rowspan: 0, colspan: 0 };
    }
  }
};

// 定义时间格式的常量
const FORMAT_FULL_DATE_TIME = 'YYYY-MM-DD HH:mm:ss';
const FORMAT_DATE_TIME = 'YYYY-MM-DD HH:mm';
const FORMAT_DATE = 'YYYY-MM-DD';
const FORMAT_MONTH_YEAR = 'YYYY-MM';
const FORMAT_YEAR = 'YYYY';

/**
 * 格式化时间。
 * @param {string | null | undefined} time 待格式化的时间字符串。
 * @param {string} type 时间格式类型 ('1d': 日, '1n': 无时间日期, '1y': 年月, 其他: 完整日期时间)。
 * @returns 格式化后的时间字符串或空字符串。
 * @throws 当`type`参数非法时抛出错误。
 */
export const formatTime = (time: string | null | undefined, type: string) => {
  if (!time) {
    return '';
  }
  try {
    const formattedTime = dayjs(time);

    if (formattedTime.isValid()) {
      switch (type) {
        case '1h':
          return formattedTime.format(FORMAT_DATE_TIME);
        case '1d':
          return formattedTime.format(FORMAT_DATE);
        case '1n':
          return formattedTime.format(FORMAT_MONTH_YEAR);
        case '1y':
          return formattedTime.format(FORMAT_YEAR);
        default:
          return formattedTime.format(FORMAT_FULL_DATE_TIME);
      }
    } else {
      // 如果时间无效，返回原始输入
      console.warn(`Invalid time: ${time}`);
      return time;
    }
  } catch (error) {
    // 异常处理：记录错误并返回空字符串
    console.error(`Error formatting time: ${error}`);
    return '';
  }
};

export const arrayOperations = new ArrayOperations();
