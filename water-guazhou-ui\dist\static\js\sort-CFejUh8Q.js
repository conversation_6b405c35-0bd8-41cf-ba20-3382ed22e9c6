import{d as x,r as m,x as f,o as C,g as I,h as v,F as o,q as s,i as g,p as l,G as b,J as T,bU as k,bW as w,C as E}from"./index-r0dFAfgr.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{k as V,s as j}from"./index-D9ERhRP6.js";import{C as N,a as W}from"./GeneralProcessing-CQ8i9ijT.js";import"./index-C9hz-UZb.js";const B=x({__name:"sort",props:{projectId:{type:String,default:()=>""},groupId:{type:String,default:()=>""}},setup(_){const d=[{label:"海康",value:"1"},{label:"城运平台",value:"3"},{label:"天网平台",value:"2"}],n=_,i=m({title:"当前排序",defaultExpandAll:!0,indexVisible:!0,rowKey:"id",selectList:[],columns:[{label:"名称",prop:"name",minWidth:"250px"},{label:"类型",prop:"videoType",formatter:e=>{var t;return(t=d.find(a=>a.value===e.videoType))==null?void 0:t.label}}],operationWidth:70,operations:[{text:"右移",perm:!0,click:e=>{r.dataList.push({...e}),i.dataList=i.dataList.filter(t=>t.id!==e.id)}}],dataList:[],pagination:{hide:!0}}),r=m({title:"新排序",defaultExpandAll:!0,indexVisible:!0,rowKey:"id",selectList:[],columns:[{label:"名称",prop:"name",minWidth:"250px"},{label:"类型",prop:"videoType",formatter:e=>{var t;return(t=d.find(a=>a.value===e.videoType))==null?void 0:t.label}},{label:"位置",prop:"number",formItemConfig:{type:"input-number",placeholder:"新位置"}}],operationWidth:70,operations:[{text:"移到",perm:!0,click:e=>{if(!e.number){f.error("请输入位置");return}if(e.number<1||e.number>r.dataList.length+1){f.error("请输入正确的位置");return}r.dataList=r.dataList.filter(t=>t.id!==e.id),r.dataList.splice(e.number-1,0,{...e,number:null})}},{text:"左移",perm:!0,click:e=>{i.dataList.push({...e}),r.dataList=r.dataList.filter(t=>t.id!==e.id)}}],dataList:[],pagination:{hide:!0}}),h=()=>{r.dataList=i.dataList,i.dataList=[]},L=()=>{const e={groupId:n.groupId,projectId:n.projectId,videoList:r.dataList.map((t,a)=>({...t,orderNum:a}))};N(e,j,{},"重排成功").then(()=>{p(),r.dataList=[]})},p=async()=>{const e={groupId:n.groupId,projectId:n.projectId||"",name:""};W(e,V,{table:i})};return C(()=>{p()}),(e,t)=>{const a=T,u=S,c=k,y=w;return I(),v(y,{gutter:20,style:{height:"100%"}},{default:o(()=>[s(c,{span:12,style:{height:"100%"}},{default:o(()=>[s(u,{style:{height:"100%"},config:g(i)},{title:o(()=>[t[1]||(t[1]=l("div",{class:"card-table-title"},[l("div",null,"当前排序")],-1)),s(a,{type:"success",style:{"margin-right":"10px"},onClick:h},{default:o(()=>t[0]||(t[0]=[b("一键右移")])),_:1})]),_:1},8,["config"])]),_:1}),s(c,{span:12,style:{height:"100%"}},{default:o(()=>[s(u,{style:{height:"100%"},config:g(r)},{title:o(()=>[t[3]||(t[3]=l("div",{class:"card-table-title"},[l("div",null,"新排序")],-1)),s(a,{type:"success",style:{"margin-right":"10px"},onClick:L},{default:o(()=>t[2]||(t[2]=[b("保存配置")])),_:1})]),_:1},8,["config"])]),_:1})]),_:1})}}}),M=E(B,[["__scopeId","data-v-369fc4f3"]]);export{M as default};
