import { transNumberUnit } from '@/utils/GlobalHelper';

export function lineOption(
  width?: number,
  datex?: any,
  left?: number,
  right?: number
) {
  const option = {
    grid: {
      left: left || 80,
      right: right || 80,
      top: 80,
      bottom: 50
    },
    legend: {
      type: 'scroll',
      width: width || 200,
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: datex
    },

    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: '压力(Mpa)',
        axisLine: {
          show: true,
          lineStyle: {
            types: 'solid'
          }
        },
        axisLabel: {
          show: true
        },
        splitLine: {
          lineStyle: {
            type: [5, 10],
            dashOffset: 5
          }
        }
      }
    ],
    series: [{}]
  };
  return option;
}
// 饼图
export const pieOption = (title?: string, data?: any) => {
  const option = {
    color: ['red', 'blue', 'orange'],
    legend: {
      type: 'scroll',
      orient: 'vertical',
      top: 'center',
      right: 'right',
      width: 200,
      formatter: '{name}{value}',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    graphic: {
      elements: [
        {
          type: 'image',
          style: {
            image: 'https://yp.a-hh.cn/zhjk/img.jpg',
            width: 25,
            height: 30
          },
          left: 'center',
          top: 'center'
        }
      ]
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
          color: '#ffffff',
          formatter() {
            return (data || 0) + '\n' + 'm³';
          }
        },
        // emphasis: {
        //   label: {
        //     show: true,
        //     fontSize: '12',
        //     fontWeight: 'bold'
        //   }
        // },
        data: [
          { value: 0, name: '报警' },
          { value: 4, name: '正常' },
          { value: 0, name: '异常' }
        ]
      }
    ]
  };
  return option;
};
// 树状图
export const barOption = (
  title: string,
  color: string,
  dataX: any[],
  data: any[]
) => {
  const option = {
    color: [color],
    xAxis: {
      type: 'category',
      data: dataX
    },
    grid: {
      left: 50,
      right: 0
    },
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: '供水量(m³)',
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#ffffff' // '#333'
            types: 'solid'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        splitLine: {
          lineStyle: {
            type: [5, 10],
            dashOffset: 5
          }
        }
      }
    ],
    series: [
      {
        data,
        type: 'bar'
      }
    ]
  };
  return option;
};

export const gaugeOption = (data?: any[]) => {
  return {
    series: [
      {
        type: 'gauge',
        startAngle: 210,
        endAngle: -30,
        min: 0,
        max: 10,
        axisLine: {
          lineStyle: {
            width: 30,
            color: [
              [0.2, '#FFB800'],
              [0.7, '#318DFF'],
              [1, '#A431FF']
            ]
          }
        },
        pointer: {
          show: true,
          itemStyle: {
            width: 10,
            color: 'auto'
          }
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 40,
          fontSize: 20
        },
        title: {
          show: true,
          offsetCenter: [0, '-20%'],
          textStyle: {
            fontWeight: 'bolder',
            fontSize: 15
          }
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}\nMpa',
          color: 'auto',
          fontSize: 20,
          offsetCenter: [0, '30%']
        },
        data
      }
    ]
  };
};

// 饼图
export const ring = (
  data: {
    name: string;
    nameAlias?: string;
    value: string;
    valueAlias?: string;
    scale: string;
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数';
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g;
    return num.toString().replace(reg, ',');
  };
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1;
  }, 0);
  const transedTotal = transNumberUnit(total);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '') +
          params.name +
          ': ' +
          Number(params.value).toFixed(percision) +
          ' ' +
          unit
        );
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 80,
            fontSize: 12
          },
          value: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          count: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12
          }
        }
      },
      data: data.map((item) => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| ' +
                (data[i].nameAlias || name) +
                '}' +
                '{value| ' +
                (data[i].valueAlias || data[i].value) +
                ' ' +
                (unit || '') +
                '}' +
                '{value| ' +
                (data[i].scale || '') +
                '}'
              );
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|' +
          title +
          ((unit && '(' + transedTotal.unit + unit + ')') ||
            '(' + transedTotal.unit + ')') +
          '}\n{val|' +
          formatNumber(transedTotal.value.toFixed(percision)) +
          '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: '#fff'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#fff'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: false,
        label: {
          show: false,
          formatter: (params) => {
            return (
              '{icon|●}{name|' +
              params.name +
              '}{value|' +
              formatNumber(Number(params.value || '0').toFixed(percision)) +
              '}'
            );
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  };
  return option;
};
