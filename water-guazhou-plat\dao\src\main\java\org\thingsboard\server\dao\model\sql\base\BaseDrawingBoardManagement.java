package org.thingsboard.server.dao.model.sql.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-画板管理对象 base_drawing_board_management
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "画板管理", description = "平台管理-画板管理实体类")
@Data
public class BaseDrawingBoardManagement {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 工艺图名称
     */
    @ApiModelProperty(value = "工艺图名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;

    /**
     * 工艺图文件路径
     */
    @ApiModelProperty(value = "工艺图文件路径")
    private String url;
}
