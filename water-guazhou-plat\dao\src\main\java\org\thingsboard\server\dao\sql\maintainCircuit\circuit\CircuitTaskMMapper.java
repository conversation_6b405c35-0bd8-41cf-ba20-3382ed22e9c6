package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskM;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface CircuitTaskMMapper extends BaseMapper<CircuitTaskM> {

    List<CircuitTaskM> getList(@Param("code") String code, @Param("name") String name, @Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("status") String status, @Param("auditStatus") String auditStatus, @Param("deviceLabelCode") String deviceLabelCode, @Param("deviceId") String deviceId, @Param("userId") String userId, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("code") String code, @Param("name") String name, @Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("status") String status, @Param("auditStatus") String auditStatus, @Param("deviceLabelCode") String deviceLabelCode, @Param("deviceId") String deviceId, @Param("userId") String userId, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("tenantId") String tenantId);

    List statistics(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("tenantId") String tenantId);

    CircuitTaskM getById(@Param("mainId") String mainId);

    String completeStatistic(String tenantId);


    Integer getNotCompleteNum(@Param("userId") String userId, @Param("tenantId") String tenantId);

    int coutDaijieshou(@Param("userId") String userId);
}
