import{d as c,g as r,h as p,F as i,G as _,i as f,al as d,J as l,C as u}from"./index-r0dFAfgr.js";const x=c({__name:"WorkOrderLocatePop",props:{visible:{type:Boolean},config:{}},setup(n){const t=n,a=()=>{var e,o;(o=(e=t.config).extentTo)==null||o.call(e,t.config.id)};return(e,o)=>{const s=l;return r(),p(s,{icon:f(d),onClick:a},{default:i(()=>o[0]||(o[0]=[_(" 缩放至 ")])),_:1},8,["icon"])}}}),k=u(x,[["__scopeId","data-v-77ea53f5"]]);export{k as default};
