import{d as C,j as D,c as r,r as _,a8 as V,o as w,ay as R,g as i,n as F,h,i as t,p as n,q as g,_ as Y,C as B}from"./index-r0dFAfgr.js";import{饼 as v,折 as x}from"./charts-CPgX3ymz.js";import{f as S}from"./DateFormatter-Bm9a68Ax.js";import{i as T,j as L}from"./ledgerManagement-CkhtRd8m.js";const M={style:{width:"calc(100% - 20px)"}},N={class:"tcharts"},j={class:"charts"},z={class:"charts"},I=C({__name:"repairinfo",props:{id:{}},setup(y){const l=D(),p=y,s=r(new Date().toString()),c=r(v([],"维修")),d=r(x([],"维修次数")),f=_({title:"",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"divider",text:"维修信息"},{xl:8,type:"text",label:"维修次数:",field:"count"},{xl:8,type:"text",label:"最近维修:",field:"latestRepairTime"}]}]}),k=_({title:"",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"divider",text:"维修记录"},{type:"table",field:"faultReportCList",config:{indexVisible:!0,height:"350px",dataList:V(()=>u.value),columns:[{label:"任务名称",prop:"title"},{label:"任务类型",prop:"type"},{label:"任务开启时间",prop:"startTime",formatter:a=>S(a.startTime,"YYYY-MM-DD")},{label:"关联工单",prop:"workOrderName"}],pagination:{hide:!0}}}]}]}),u=r([]),b=async()=>{T(p.id).then(a=>{const e=a.data.data||{};for(const o in e)(e[o]===void 0||e[o]===null)&&(e[o]=" ");f.defaultValue={...e},c.value=v(e.gradeCount||[],"维修"),d.value=x(e.nowYearRepair||[],"维修次数"),setTimeout(()=>{s.value=new Date().toString()},1e3)}),L(p.id).then(a=>{u.value=a.data.data.data||[]})};return w(()=>{b()}),(a,e)=>{const o=Y,m=R("VChart");return i(),F("div",M,[(i(),h(o,{key:t(s),ref:"refForm",config:t(f)},null,8,["config"])),n("div",N,[n("div",j,[g(m,{ref:"refChart2",autoresize:"",theme:t(l).isDark?"dark":"light",option:t(c)},null,8,["theme","option"])]),n("div",z,[g(m,{ref:"refChart2",autoresize:"",theme:t(l).isDark?"dark":"light",option:t(d)},null,8,["theme","option"])])]),(i(),h(o,{key:t(s),ref:"refForm",config:t(k)},null,8,["config"]))])}}}),O=B(I,[["__scopeId","data-v-a5998079"]]);export{O as default};
