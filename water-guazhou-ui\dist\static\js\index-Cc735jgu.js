import{d as se,dM as _,c as X,r as ue,am as ne,o as ie,bv as le,ar as de,x as A,y as oe,C as pe,g,n as T,p as f,q as n,F as u,G as o,bo as Y,h as v,an as F,bh as O,H as ye,bb as me,I as fe,J as ge,K as he,N as Ae,c6 as Ce,O as Be,P as Ue,L as ve,br as ce}from"./index-r0dFAfgr.js";import{g as De,a as Fe,b as Q,c as Z}from"./index-CvT4_zhs.js";const Te=se({name:"DeviceAuth",components:{ChooseUserByRole:_},setup(){const s=X(null),e=X(null),t=ue({orgTreeData:[],orgTreeProps:{label:"name",children:"children"},searchOrgName:"",currentOrgId:"",queryParams:{page:1,size:10,orgId:"",deviceName:"",sortBy:""},loading:!1,deviceList:[],total:0,currentDevice:null,userBindDialogVisible:!1,userBindForm:{userIds:[],authType:0},userOptions:[],selectedUsers:[],addSelectedUsers:[],authManageDialogVisible:!1,currentDeviceUserAuthList:[],apiResponseData:null,addUserDialogVisible:!1,addUserForm:{userId:"",userName:"",authType:0}});ne(()=>t.searchOrgName,a=>{var r;(r=e.value)==null||r.filter(a)});const J=(a,r)=>a?r.name.indexOf(a)!==-1:!0,G=async()=>{var a;try{const r=await de(1);t.orgTreeData=((a=r.data)==null?void 0:a.data)||[]}catch{A.error("获取组织树失败")}},c=async()=>{var a,r,d;t.loading=!0;try{const m={...t.queryParams},l=await De(m);let y=[];(r=(a=l.data)==null?void 0:a.data)!=null&&r.data?(y=l.data.data.data||[],t.total=l.data.data.total||0):Array.isArray(l.data)?(y=l.data,t.total=l.data.length||0):(d=l.data)!=null&&d.data&&Array.isArray(l.data.data)?(y=l.data.data,t.total=l.data.data.length||0):(y=l.data||[],t.total=y.length||0),m.deviceName&&(y=y.filter(B=>B.name&&B.name.includes(m.deviceName)),t.total=y.length),t.deviceList=y.map(B=>({...B,userAuthList:[],loadingUserAuth:!1,userAuthLoaded:!1}))}catch{A.error("获取设备列表失败")}finally{t.loading=!1}},w=async a=>{if(!a.userAuthLoaded){a.loadingUserAuth=!0;try{const r=await Z(a.id);if(t.apiResponseData=r,!r.data){a.userAuthList=[];return}let d=r.data;if(!Array.isArray(d)){if(r.data&&Array.isArray(r.data.data))d=r.data.data;else if(r.data&&typeof r.data=="object"){for(const l in r.data)if(Array.isArray(r.data[l])){d=r.data[l];break}}Array.isArray(d)||(d=[])}const m=d.map(l=>{const y=typeof l.authType=="number"?l.authType:parseInt(l.authType);let B=l.userName;if(!B){const M=t.userOptions.find(W=>{const R=W.id?String(W.id):"",j=l.userId?String(l.userId):"";return R===j||R===j.replace(/-/g,"")||j===R.replace(/-/g,"")});M&&(B=M.firstName||M.email||"未命名用户")}return{...l,authType:y,userName:B||"未命名用户",fullControl:y===1,readOnly:y===2,operateBackend:y===3,paramSetting:y===4}});a.userAuthList=m,a.userAuthLoaded=!0}catch(r){a.userAuthList=[],t.apiResponseData={error:r.message}}finally{a.loadingUserAuth=!1}}},P=(a,r)=>{r.length>0&&w(a)},D=async()=>{try{const a=await Fe({});t.userOptions=a.data||[]}catch{A.error("获取用户列表失败")}},p=a=>{t.currentOrgId=a.id,t.queryParams.orgId=a.id,t.queryParams.page=1,c()},b=()=>{t.queryParams.page=1,c()},C=()=>{var a;(a=s.value)==null||a.resetFields(),t.queryParams.deviceName="",t.queryParams.sortBy="",b()},U=a=>{t.queryParams.size=a,c()},k=a=>{t.queryParams.page=a,c()},q=a=>{t.currentDevice=a,t.userBindForm={userIds:[],authType:0},t.selectedUsers=[],t.userBindDialogVisible=!0},I=a=>{t.userBindForm.userIds=a.map(r=>r.id),t.selectedUsers=a.map(r=>({id:r.id,name:r.firstName||r.email||"未命名用户"}))},E=a=>{if(a&&a.length>0){const r=a[0];t.addUserForm.userId=r.id,t.addUserForm.userName=r.firstName||r.email||"未命名用户",t.addSelectedUsers=[{id:r.id,name:r.firstName||r.email||"未命名用户"}]}},L=(a,r)=>{t[r].authType===a?t[r].authType=0:t[r].authType=a,t[r]={...t[r]}},N=(a,r)=>{a.authType===r?a.authType=0:a.authType=r,t.currentDeviceUserAuthList=[...t.currentDeviceUserAuthList]},V=async()=>{if(!t.userBindForm.userIds||t.userBindForm.userIds.length===0){A.warning("请选择用户");return}if(!t.userBindForm.authType||t.userBindForm.authType===0){A.warning("请选择权限类型");return}try{const a=t.userBindForm.userIds.map(d=>{const l=t.selectedUsers.find(y=>y.id===d)||t.userOptions.find(y=>y.id===d);return{userId:d,userName:l&&(l.name||l.firstName||l.email)||"未命名用户",authType:t.userBindForm.authType}}),r=t.currentDevice.userAuthList.map(d=>d.userId);for(const d of t.currentDevice.userAuthList)a.some(m=>m.userId===d.userId)||a.push(d);await Q({deviceId:t.currentDevice.id,deviceName:t.currentDevice.name,deviceSerial:t.currentDevice.serialId,userAuthList:a}),A.success("用户绑定成功"),t.userBindDialogVisible=!1,c()}catch{A.error("用户绑定失败")}},i=async a=>{t.currentDevice=a,t.userOptions.length===0&&await D();try{const r=await Z(a.id);let d=[];if(r.data){if(Array.isArray(r.data))d=r.data;else if(r.data.data&&Array.isArray(r.data.data))d=r.data.data;else if(typeof r.data=="object"){for(const l in r.data)if(Array.isArray(r.data[l])){d=r.data[l];break}}}const m=d.map(l=>{const y=typeof l.authType=="number"?l.authType:parseInt(l.authType)||0;let B=l.userName||"";if(!B&&l.userId){const S=t.userOptions.find(K=>{const z=String(K.id||""),$=String(l.userId||"");return z===$||z===$.replace(/-/g,"")||$===z.replace(/-/g,"")});S&&(B=S.firstName||S.email||"未命名用户")}return{userId:l.userId,userName:B||"未命名用户",authType:y}});t.currentDeviceUserAuthList=m,a.userAuthList=[...m],a.userAuthLoaded=!0}catch{t.currentDeviceUserAuthList=[],A.error("获取设备用户权限列表失败")}t.authManageDialogVisible=!0},h=()=>{t.addUserForm={userId:"",userName:"",authType:0},t.addSelectedUsers=[],t.addUserDialogVisible=!0},H=a=>t.currentDeviceUserAuthList.some(r=>{const d=r.userId?String(r.userId):"",m=a?String(a):"";return d===m||d===m.replace(/-/g,"")||m===d.replace(/-/g,"")}),x=()=>{if(!t.addUserForm.userId){A.warning("请选择用户");return}if(!t.addUserForm.authType||t.addUserForm.authType===0){A.warning("请选择权限类型");return}const r=(t.addSelectedUsers.length>0?t.addSelectedUsers[0]:null)||t.userOptions.find(d=>d.id===t.addUserForm.userId);r?(t.currentDeviceUserAuthList.push({userId:t.addUserForm.userId,userName:r.name||r.firstName||r.email||t.addUserForm.userName||"未命名用户",authType:t.addUserForm.authType}),t.addUserDialogVisible=!1):(t.currentDeviceUserAuthList.push({userId:t.addUserForm.userId,userName:t.addUserForm.userName||"未命名用户",authType:t.addUserForm.authType}),t.addUserDialogVisible=!1)},ee=a=>{t.currentDeviceUserAuthList.splice(a,1)},te=async()=>{const a=t.currentDeviceUserAuthList.filter(r=>!r.authType||r.authType===0);if(a.length>0){A.warning(`有${a.length}个用户未选择权限类型，请为所有用户选择权限类型`);return}try{await Q({deviceId:t.currentDevice.id,deviceName:t.currentDevice.name,deviceSerial:t.currentDevice.serialId,userAuthList:t.currentDeviceUserAuthList}),A.success("权限管理成功"),t.authManageDialogVisible=!1,c()}catch(r){console.error("权限管理失败",r),A.error("权限管理失败")}},re=(a,r)=>{oe.confirm("确认删除该用户权限吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const d=a.userAuthList.filter(l=>l.userId!==r.userId),m=d.filter(l=>!l.authType||l.authType===0);if(m.length>0){A.warning(`有${m.length}个用户未选择权限类型，无法提交`);return}await Q({deviceId:a.id,deviceName:a.name,deviceSerial:a.serialId,userAuthList:d}),A.success("删除成功"),c()}catch(d){console.error("删除失败",d),A.error("删除失败")}}).catch(()=>{})},ae=(a,r)=>a.userAuthList?typeof r=="string"?a.userAuthList.some(d=>d[r]===!0):typeof r=="number"?a.userAuthList.some(d=>d.authType===r):!1:!1;return ie(()=>{G(),D(),c()}),{queryForm:s,orgTree:e,...le(t),filterOrgNode:J,handleOrgNodeClick:p,handleQuery:b,resetQuery:C,handleSizeChange:U,handleCurrentChange:k,handleUserBind:q,handleUserSelect:I,handleAddUserSelect:E,handleAuthTypeChange:L,handleRowAuthTypeChange:N,submitUserBind:V,handleAuthManage:i,handleAddUserAuth:h,isUserAlreadyAdded:H,submitAddUser:x,handleRemoveUserAuth:ee,submitAuthManage:te,handleDeleteUserAuth:re,hasAuthType:ae,handleExpandChange:P}}}),be={class:"app-container"},ke={class:"device-auth-container"},Ee={class:"org-tree-container"},we={class:"search-box"},Ie={class:"org-tree-content"},Le={class:"device-list-container"},Ne={class:"auth-type-tags"},Ve={key:0},Se={key:1},ze={key:0},$e={key:1},Oe={style:{display:"flex","justify-content":"center",gap:"10px"}},Pe={class:"pagination-container"},qe={class:"auth-type-buttons"},Me={class:"dialog-footer"},Re={key:0,class:"empty-data"},je={style:{"text-align":"left","max-height":"200px",overflow:"auto",background:"#f5f5f5",padding:"10px","border-radius":"4px"}},Qe={key:0,style:{color:"#999","font-size":"12px"}},Je={class:"auth-type-buttons"},Ge={class:"auth-manage-footer"},He={class:"dialog-footer"},Ke={class:"auth-type-buttons"},We={class:"dialog-footer"};function Xe(s,e,t,J,G,c){var N,V;const w=ye,P=me,D=fe,p=ge,b=he,C=Ae,U=Ce,k=Be,q=Ue,I=_,E=ve,L=ce;return g(),T("div",be,[f("div",ke,[f("div",Ee,[e[16]||(e[16]=f("div",{class:"org-tree-title"},"水厂选择",-1)),f("div",we,[n(w,{modelValue:s.searchOrgName,"onUpdate:modelValue":e[0]||(e[0]=i=>s.searchOrgName=i),placeholder:"搜索水厂名称",clearable:"",onClear:s.filterOrgNode},null,8,["modelValue","onClear"])]),f("div",Ie,[n(P,{ref:"orgTree",data:s.orgTreeData,props:s.orgTreeProps,"node-key":"id","highlight-current":"","default-expand-all":"","filter-node-method":s.filterOrgNode,onNodeClick:s.handleOrgNodeClick},null,8,["data","props","filter-node-method","onNodeClick"])])]),f("div",Le,[n(b,{model:s.queryParams,ref:"queryForm",inline:!0,class:"search-form"},{default:u(()=>[n(D,{label:"设备名称："},{default:u(()=>[n(w,{modelValue:s.queryParams.deviceName,"onUpdate:modelValue":e[1]||(e[1]=i=>s.queryParams.deviceName=i),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),n(D,null,{default:u(()=>[n(p,{type:"primary",onClick:s.handleQuery},{default:u(()=>e[17]||(e[17]=[o("查询")])),_:1},8,["onClick"]),n(p,{onClick:s.resetQuery},{default:u(()=>e[18]||(e[18]=[o("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),Y((g(),v(k,{data:s.deviceList,border:"",onExpandChange:s.handleExpandChange},{default:u(()=>[n(C,{type:"expand"},{default:u(i=>[Y((g(),T("div",null,[n(k,{data:i.row.userAuthList||[],border:"",class:"sub-table"},{default:u(()=>[n(C,{label:"用户名称",prop:"userName",align:"center"}),n(C,{label:"权限类型",align:"center"},{default:u(h=>[f("div",Ne,[h.row.authType===1?(g(),v(U,{key:0,type:"danger",class:"ml-5"},{default:u(()=>e[19]||(e[19]=[o("完全控制")])),_:1})):F("",!0),h.row.authType===2?(g(),v(U,{key:1,type:"info",class:"ml-5"},{default:u(()=>e[20]||(e[20]=[o("只读访问")])),_:1})):F("",!0),h.row.authType===3?(g(),v(U,{key:2,type:"warning",class:"ml-5"},{default:u(()=>e[21]||(e[21]=[o("操作后台")])),_:1})):F("",!0),h.row.authType===4?(g(),v(U,{key:3,type:"success",class:"ml-5"},{default:u(()=>e[22]||(e[22]=[o("参数设置")])),_:1})):F("",!0)])]),_:1}),n(C,{label:"操作",align:"center",width:"120"},{default:u(h=>[n(p,{type:"danger",size:"mini",onClick:H=>s.handleDeleteUserAuth(i.row,h.row)},{default:u(()=>e[23]||(e[23]=[o("删除")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])),[[L,i.row.loadingUserAuth]])]),_:1}),n(C,{label:"设备名称",prop:"name",align:"center"}),n(C,{label:"设备归属",prop:"typeName",align:"center"}),n(C,{label:"绑定用户",align:"center"},{default:u(i=>[i.row.userAuthList&&i.row.userAuthList.length>0?(g(),T("span",Ve,O(i.row.userAuthList.map(h=>h.userName).join(", ")),1)):(g(),T("span",Se,"-"))]),_:1}),n(C,{label:"管理范围",align:"center"},{default:u(i=>[i.row.userAuthList&&i.row.userAuthList.length>0?(g(),T("span",ze,[s.hasAuthType(i.row,1)?(g(),v(U,{key:0,type:"danger",class:"ml-5"},{default:u(()=>e[24]||(e[24]=[o("完全控制")])),_:1})):F("",!0),s.hasAuthType(i.row,2)?(g(),v(U,{key:1,type:"info",class:"ml-5"},{default:u(()=>e[25]||(e[25]=[o("只读访问")])),_:1})):F("",!0),s.hasAuthType(i.row,3)?(g(),v(U,{key:2,type:"warning",class:"ml-5"},{default:u(()=>e[26]||(e[26]=[o("操作后台")])),_:1})):F("",!0),s.hasAuthType(i.row,4)?(g(),v(U,{key:3,type:"success",class:"ml-5"},{default:u(()=>e[27]||(e[27]=[o("参数设置")])),_:1})):F("",!0)])):(g(),T("span",$e,"-"))]),_:1}),n(C,{label:"操作",align:"center",width:"240"},{default:u(i=>[f("div",Oe,[n(p,{type:"primary",size:"mini",onClick:h=>s.handleUserBind(i.row)},{default:u(()=>e[28]||(e[28]=[o("用户绑定")])),_:2},1032,["onClick"]),n(p,{type:"success",size:"mini",onClick:h=>s.handleAuthManage(i.row)},{default:u(()=>e[29]||(e[29]=[o("权限管理")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","onExpandChange"])),[[L,s.loading]]),f("div",Pe,[n(q,{onSizeChange:s.handleSizeChange,onCurrentChange:s.handleCurrentChange,"current-page":s.queryParams.page,"page-sizes":[10,20,50,100],"page-size":s.queryParams.size,layout:"total, sizes, prev, pager, next, jumper",total:s.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])])])]),n(E,{title:"用户绑定 - "+(((N=s.currentDevice)==null?void 0:N.name)||""),modelValue:s.userBindDialogVisible,"onUpdate:modelValue":e[7]||(e[7]=i=>s.userBindDialogVisible=i),width:"600px","append-to-body":""},{footer:u(()=>[f("div",Me,[n(p,{onClick:e[6]||(e[6]=i=>s.userBindDialogVisible=!1)},{default:u(()=>e[34]||(e[34]=[o("取 消")])),_:1}),n(p,{type:"primary",onClick:s.submitUserBind},{default:u(()=>e[35]||(e[35]=[o("确 定")])),_:1},8,["onClick"])])]),default:u(()=>[n(b,{ref:"userBindForm",model:s.userBindForm,"label-width":"100px"},{default:u(()=>[n(D,{label:"选择用户",prop:"userIds"},{default:u(()=>[n(I,{users:s.selectedUsers,onCheckUsers:s.handleUserSelect,multiple:""},null,8,["users","onCheckUsers"])]),_:1}),n(D,{label:"权限类型",prop:"authType"},{default:u(()=>[f("div",qe,[n(p,{type:s.userBindForm.authType===1?"danger":"default",onClick:e[2]||(e[2]=i=>s.handleAuthTypeChange(1,"userBindForm")),size:"mini"},{default:u(()=>e[30]||(e[30]=[o("完全控制")])),_:1},8,["type"]),n(p,{type:s.userBindForm.authType===2?"info":"default",onClick:e[3]||(e[3]=i=>s.handleAuthTypeChange(2,"userBindForm")),size:"mini"},{default:u(()=>e[31]||(e[31]=[o("只读访问")])),_:1},8,["type"]),n(p,{type:s.userBindForm.authType===3?"warning":"default",onClick:e[4]||(e[4]=i=>s.handleAuthTypeChange(3,"userBindForm")),size:"mini"},{default:u(()=>e[32]||(e[32]=[o("操作后台")])),_:1},8,["type"]),n(p,{type:s.userBindForm.authType===4?"success":"default",onClick:e[5]||(e[5]=i=>s.handleAuthTypeChange(4,"userBindForm")),size:"mini"},{default:u(()=>e[33]||(e[33]=[o("参数设置")])),_:1},8,["type"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),n(E,{title:"权限管理 - "+(((V=s.currentDevice)==null?void 0:V.name)||""),modelValue:s.authManageDialogVisible,"onUpdate:modelValue":e[9]||(e[9]=i=>s.authManageDialogVisible=i),width:"800px","append-to-body":""},{footer:u(()=>[f("div",He,[n(p,{onClick:e[8]||(e[8]=i=>s.authManageDialogVisible=!1)},{default:u(()=>e[44]||(e[44]=[o("取 消")])),_:1}),n(p,{type:"primary",onClick:s.submitAuthManage},{default:u(()=>e[45]||(e[45]=[o("确 定")])),_:1},8,["onClick"])])]),default:u(()=>[s.currentDeviceUserAuthList.length===0?(g(),T("div",Re,[e[36]||(e[36]=f("p",null,"暂无用户权限数据",-1)),e[37]||(e[37]=f("p",{style:{color:"#999","font-size":"12px"}},"API返回的数据结构:",-1)),f("pre",je,O(JSON.stringify(s.apiResponseData,null,2)),1)])):(g(),v(k,{key:1,data:s.currentDeviceUserAuthList,border:""},{default:u(()=>[n(C,{label:"用户名称",align:"center"},{default:u(i=>[o(O(i.row.userName||"未命名用户")+" ",1),i.row.userName?F("",!0):(g(),T("div",Qe," 用户ID: "+O(i.row.userId),1))]),_:1}),n(C,{label:"权限类型",align:"center"},{default:u(i=>[f("div",Je,[n(p,{type:i.row.authType===1?"danger":"default",onClick:h=>s.handleRowAuthTypeChange(i.row,1),size:"mini"},{default:u(()=>e[38]||(e[38]=[o("完全控制")])),_:2},1032,["type","onClick"]),n(p,{type:i.row.authType===2?"info":"default",onClick:h=>s.handleRowAuthTypeChange(i.row,2),size:"mini"},{default:u(()=>e[39]||(e[39]=[o("只读访问")])),_:2},1032,["type","onClick"]),n(p,{type:i.row.authType===3?"warning":"default",onClick:h=>s.handleRowAuthTypeChange(i.row,3),size:"mini"},{default:u(()=>e[40]||(e[40]=[o("操作后台")])),_:2},1032,["type","onClick"]),n(p,{type:i.row.authType===4?"success":"default",onClick:h=>s.handleRowAuthTypeChange(i.row,4),size:"mini"},{default:u(()=>e[41]||(e[41]=[o("参数设置")])),_:2},1032,["type","onClick"])])]),_:1}),n(C,{label:"操作",align:"center",width:"120"},{default:u(i=>[n(p,{type:"danger",size:"mini",onClick:h=>s.handleRemoveUserAuth(i.$index)},{default:u(()=>e[42]||(e[42]=[o("移除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),f("div",Ge,[n(p,{type:"primary",onClick:s.handleAddUserAuth},{default:u(()=>e[43]||(e[43]=[o("添加用户")])),_:1},8,["onClick"])])]),_:1},8,["title","modelValue"]),n(E,{title:"添加用户",modelValue:s.addUserDialogVisible,"onUpdate:modelValue":e[15]||(e[15]=i=>s.addUserDialogVisible=i),width:"500px","append-to-body":""},{footer:u(()=>[f("div",We,[n(p,{onClick:e[14]||(e[14]=i=>s.addUserDialogVisible=!1)},{default:u(()=>e[50]||(e[50]=[o("取 消")])),_:1}),n(p,{type:"primary",onClick:s.submitAddUser},{default:u(()=>e[51]||(e[51]=[o("确 定")])),_:1},8,["onClick"])])]),default:u(()=>[n(b,{ref:"addUserForm",model:s.addUserForm,"label-width":"100px"},{default:u(()=>[n(D,{label:"选择用户",prop:"userId"},{default:u(()=>[n(I,{users:s.addSelectedUsers,onCheckUsers:s.handleAddUserSelect,multiple:!1},null,8,["users","onCheckUsers"])]),_:1}),n(D,{label:"权限类型",prop:"authType"},{default:u(()=>[f("div",Ke,[n(p,{type:s.addUserForm.authType===1?"danger":"default",onClick:e[10]||(e[10]=i=>s.handleAuthTypeChange(1,"addUserForm")),size:"mini"},{default:u(()=>e[46]||(e[46]=[o("完全控制")])),_:1},8,["type"]),n(p,{type:s.addUserForm.authType===2?"info":"default",onClick:e[11]||(e[11]=i=>s.handleAuthTypeChange(2,"addUserForm")),size:"mini"},{default:u(()=>e[47]||(e[47]=[o("只读访问")])),_:1},8,["type"]),n(p,{type:s.addUserForm.authType===3?"warning":"default",onClick:e[12]||(e[12]=i=>s.handleAuthTypeChange(3,"addUserForm")),size:"mini"},{default:u(()=>e[48]||(e[48]=[o("操作后台")])),_:1},8,["type"]),n(p,{type:s.addUserForm.authType===4?"success":"default",onClick:e[13]||(e[13]=i=>s.handleAuthTypeChange(4,"addUserForm")),size:"mini"},{default:u(()=>e[49]||(e[49]=[o("参数设置")])),_:1},8,["type"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const _e=pe(Te,[["render",Xe],["__scopeId","data-v-89614f00"]]);export{_e as default};
