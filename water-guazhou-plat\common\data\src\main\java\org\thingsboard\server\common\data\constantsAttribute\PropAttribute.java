/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
public class PropAttribute implements Comparable<PropAttribute> {


    private String isVirtual;
    //属性种类
    private String propertyCategory;
    //属性名
    private String name;
    //谐波次数
    private String harmonicOrder;
    //采样间隔
    private String samplingInterval;
    //统计类型
    private String statType;
    //采样系数
    private String sampleCoef;
    // 每小时偏差上限
    private String sampleDeviation;
    //最大值
    private String samplingMax;
    //最小值
    private String samplingMin;
    //单位系数
    private String unitCoef;
    //单位
    private String unit;
    //数据偏移量
    private String dataOffset;
    //公式
    private String formulaProperty;
    //变量类型 1:只读量;2:只写量;3:读写量;4:开关量
    private String propertyType;
    //可选的下发值，当变量类型为开关量时，当前字段有值
    private String controlOptions;
    //寄存器地址
    private String registerAddress;
    //功能码
    private String functionCode;
    //字节数
    private String byteCount;
    //0 为无符号1为1符号
    private String registerSignFlag;
    //0为普通寄存器 1为位寄存器
    private String registerType;
    //位寄存器读第几位
    private String bitPosition;
    //读取顺序
    private String byteOrder;
    //数据类型
    private String dataType;
    private String pollPeriod;
    private String readOrWrite;
    private String defaultPropCategory;
    private List<DisplayObj> display;

    private String order;
    private String allowDelete;

    private String additionalInfo;
    /**
     * 标准线值
     */
    private String standard;

    /**
     * 是否是标准MODBUS协议
     */
    private String informal;
    /**
     * 变比相关，计算方式为ct1/ct2
     */
    private String ct1;

    private String ct2;

    private String indexNumber;

    /**
     * 量程
     */
    private String range;

    /**
     * 分组
     */
    private String group;

    /**
     * 传感器ID号
     */
    private String serialNumber;

    /**
     * 监测点位
     */
    private String pointAddress;

    /**
     * 无效值, 针对特定值为无效值的情况
     */
    private String invalidValue;

    @Override
    public int compareTo(PropAttribute o) {
        if (o.getFormulaProperty() == null || o.getFormulaProperty().equalsIgnoreCase(" ") || o.getFormulaProperty().length() == 0) {
            return 1;
        } else if (this.getFormulaProperty() == null || this.getFormulaProperty().equalsIgnoreCase(" ") || this.getFormulaProperty().length() == 0) {
            return -1;
        } else if (this.getFormulaProperty().length() == o.getFormulaProperty().length()) {
            return 0;
        } else {
            return this.getFormulaProperty().length() - o.getFormulaProperty().length() > 1 ? 1 : -1;
        }
    }
}
