import{_ as h}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as x}from"./CardTable-rdWOL4_6.js";import{_ as y}from"./CardSearch-CB_HNR-Q.js";import{d as C,c as p,s as v,r as f,x as s,am as D,o as k,g as w,n as T,q as u,i as d,b7 as V}from"./index-r0dFAfgr.js";import{p as E,a as L,g as N,b as S}from"./manage-BReaEVJk.js";import{I as g}from"./common-CvK_P_ao.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const q={class:"wrapper"},R=C({__name:"typeConfiguration",setup(B){const r=p(),c=p("项目类型"),m=p({defaultParams:{radio:"项目类型"},filters:[{label:"",field:"radio",type:"radio-button",options:[{label:"项目类型",value:"项目类型"},{label:"合同分类",value:"合同分类"}],onChange:e=>{c.value=e}}],operations:[{type:"btn-group",btns:[{type:"success",perm:!0,text:"添加",icon:g.ADD,click:()=>{_()}},{type:"default",perm:!0,text:"重置",svgIcon:v(V),click:()=>{i()}},{perm:!0,text:"查询",icon:g.QUERY,click:()=>i()}]}]}),t=f({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"名称",prop:"name"},{label:"排序编号",prop:"orderNum"}],operationWidth:"100px",operations:[{isTextBtn:!1,text:"编辑",perm:!0,click:e=>{var a;n.title="编辑信息",n.defaultValue={...e},(a=r.value)==null||a.openDialog()}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{t.pagination.page=e,t.pagination.limit=a,i()}}}),n=f({title:"添加信息",labelWidth:"100px",dialogWidth:"500px",submitting:!1,submit:e=>{let a="添加";e.id&&(a="编辑"),n.submitting=!0,c.value==="项目类型"?E(e).then(o=>{var l;n.submitting=!1,o.data.code===200?(s.success(a+"成功"),(l=r.value)==null||l.closeDialog(),i()):s.warning(a+"失败")}).catch(o=>{s.warning(o)}):L(e).then(o=>{var l;n.submitting=!1,o.data.code===200?(s.success(a+"成功"),(l=r.value)==null||l.closeDialog(),i()):s.warning(a+"失败")}).catch(o=>{s.warning(o)})},defaultValue:{},group:[{fields:[{type:"input",label:"类型名称",field:"name",rules:[{required:!0,message:"请输入类型名称"}]},{type:"input-number",label:"排序",field:"orderNum",rules:[{required:!0,message:"请输入排序"}]}]}]});function _(){var e;n.title="添加信息",n.defaultValue={},(e=r.value)==null||e.openDialog()}const i=async()=>{const e={size:t.pagination.limit||20,page:t.pagination.page||1};t.dataList=[],t.pagination.total=0,c.value==="项目类型"?N(e).then(a=>{t.dataList=a.data.data.data||[],t.pagination.total=a.data.data.total||0}):S(e).then(a=>{t.dataList=a.data.data.data||[],t.pagination.total=a.data.data.total||0})};return D(c,()=>{i()}),k(()=>{i()}),(e,a)=>{const o=y,l=x,b=h;return w(),T("div",q,[u(o,{ref:"refSearch",config:d(m)},null,8,["config"]),u(l,{config:d(t),class:"card-table"},null,8,["config"]),u(b,{ref_key:"refForm",ref:r,config:d(n)},null,8,["config"])])}}});export{R as default};
