<template>
  <div class="ws_video">
    <div
      :id="'wsVideo' + config.index"
      ref="wsVideo"
      style="width: 100%; height: 100%; position: relative; background-color: #000"
    ></div>
    <div
      class="Mask"
      @dblclick="wholeFullScreen"
    >
      <div
        v-if="state.error === 2"
        class="error"
      >
        播放失败
      </div>
      <div
        v-if="state.error === 3"
        class="refresh"
      >
        加载中
      </div>
      <div class="WSPlayer_control">
        <el-tooltip
          class="box-item"
          effect="dark"
          content="刷新"
          placement="top-end"
        >
          <Icon
            class="WSPlayer_control_item"
            :size="18"
            icon="material-symbols:refresh"
            @click="updateVideo"
          >
          </Icon>
        </el-tooltip>
        <el-tooltip
          class="box-item"
          effect="dark"
          content="最大化"
          placement="top-end"
        >
          <Icon
            class="WSPlayer_control_item"
            :size="18"
            icon="gg:maximize"
            @click="wholeFullScreen"
          >
          </Icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'

const wsVideo = ref()

const emit = defineEmits(['setVolume'])

const props = defineProps<{
  config: {
    url: string
    index: number
    talkurl?: string | null
  }
}>()

const state = reactive({
  error: 3,
})

let player: any = null

// 开始播放
const realplay = () => {
  const playURL = props.config.url
  player?.JS_Play(playURL, { playURL, mode: 1 }, 0).then(
    () => {
      state.error = 1
      // console.log('realplay success')
    },
    (e) => {
      state.error = 2
      errorTip()
    },
  )
}

// 失败提示
const errorTip = () => {
  let isFirstCall = true

  return function () {
    if (isFirstCall) {
      ElMessage.error('播放失败')
      isFirstCall = false
    } else {
      console.log('播放失败')
    }
  }
}

// 刷新视频
const updateVideo = () => {
  state.error = 3
  stopPlay()
  realplay()
}

// 关闭播放
const stopPlay = () => {
  player?.JS_Stop().then(
    () => {},
    (e) => {
      console.error(e)
    },
  )
}

// 开启对讲
const talkStart = () => {
  let url = props.config.talkurl

  player?.JS_SetConnectTimeOut(0, 1000)
  player?.JS_StartTalk(url).then(
    () => {
      // console.log('talkStart success')
    },
    (e) => {
      console.error(e)
    },
  )
}

// 结束对讲
const talkStop = () => {
  player?.JS_StopTalk().then(
    () => {
      // console.log('talkStop success')
    },
    (e) => {
      console.error(e)
    },
  )
}

// 最大化
const wholeFullScreen = () => {
  player?.JS_FullScreenDisplay(true).then(
    () => {
      console.log(`wholeFullScreen success`)
    },
    (e) => {
      console.error(e)
    },
  )
}

// 初始化播放器
const refreshVideo = () => {
  const { JSPlugin } = window as any
  player = new JSPlugin({
    szId: 'wsVideo' + props.config.index,
    szBasePath: '/h5player/',
    iMaxSplit: 1,
    iCurrentSplit: 1,
    openDebug: false,
    oStyle: {
      border: '#000',
      borderSelect: '#000',
      background: '#000',
    },
  })
  player?.JS_SetWindowControlCallback({
    // windowEventSelect(iWindIndex) {
    //     // 插件选中窗口回调
    //     console.log("windowSelect callback: ", iWindIndex);
    // },
    // pluginErrorHandler(iWindIndex, iErrorCode, oError) {
    //     // 插件错误回调
    //     console.error(`window-${iWindIndex}, errorCode: ${iErrorCode}`, oError);
    // },
    // windowEventOver(iWindIndex) {
    //     // 鼠标移过回调
    //     console.log("鼠标移过回调", iWindIndex);
    // },
    // windowEventOut(iWindIndex) {
    //     // 鼠标移出回调
    //     console.log("鼠标移出回调", iWindIndex);
    // },
    // windowFullCcreenChange(bFull) {
    //     // 全屏切换回调
    //     console.log("全屏切换回调", bFull);
    // },
    // firstFrameDisplay(iWndIndex, iWidth, iHeight) {
    //     // 首帧显示回调
    //     console.log("首帧显示回调", iWndIndex, iWidth, iHeight);
    // },

    pluginErrorHandler() {
      // 插件错误回调
      // 在5秒后尝试更新视频，以处理插件加载错误的情况
      // 使用setTimeout延迟执行，避免立即重试导致的问题
      setTimeout(() => {
        // 尝试更新视频，如果失败则捕获异常并记录错误信息
        try {
          updateVideo()
        } catch (error) {
          console.log(error)
        }
      }, 5000)
    },
    performanceLack(iWndIndex) {
      // 性能不足回调
      console.log('性能不足回调', iWndIndex)
    },
  })

  realplay()
  getVolume()
}

// 获取视频音量
const getVolume = () => {
  player?.JS_GetVolume(0).then(
    (volume) => {
      // console.log('getVolume success', volume)
      emit('setVolume', true, volume)
    },
    (e) => {
      // console.error(e)
      emit('setVolume', false, 100)
    },
  )
}

// 设置音量
const setVolume = (volume) => {
  player?.JS_SetVolume(0, volume).then(
    () => {
      console.log('setVolume success')
    },
    (e) => {
      console.error(e)
    },
  )
}

// 清理对象
const clear = () => {
  player?.JS_Stop()
  player = null
}

const resizeObserver = new ResizeObserver((entries) => {
  // 处理大小变化的回调函数
  for (const entry of entries) {
    // 刷新播放器大小
    player?.JS_Resize()
  }
})

// 关闭页面触发
onBeforeUnmount(() => {
  clear()
})

onMounted(() => {
  refreshVideo()
  resizeObserver.observe(wsVideo.value)
})

watch(
  () => props.config.url,
  (newURL) => {
    if (!newURL) {
      stopPlay()
    }
    stopPlay()
    refreshVideo()
  },
)

defineExpose({
  talkStart,
  talkStop,
  setVolume,
  clear,
})
</script>

<style lang="scss" scoped>
.ws_video {
  width: 100%;
  height: 100%;
  position: relative;
}

@keyframes WSPlayer_control_Animation {
  from {
    bottom: -60px;
  }

  to {
    bottom: 0px;
  }
}

.Mask {
  z-index: 20001;

  &:hover {
    .WSPlayer_control {
      width: 100%;
      height: 30px;
      position: absolute;
      right: 0;
      bottom: 0;
      background-image: linear-gradient(rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.8));
      display: flex;
      color: #fff;
      align-items: end;
      padding: 5px 10px;
      justify-content: end;
      animation: WSPlayer_control_Animation 0.2s;

      .WSPlayer_control_item {
        display: inline;
      }
    }
  }

  width: 100%;
  height: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: auto;
  overflow: hidden;
}

.WSPlayer_control_item {
  margin-left: 10px;
  display: none;
}

.error {
  width: 64px;
  position: absolute;
  top: calc(50% - 10px);
  left: calc(50% - 32px);
  color: red;
}

.refresh {
  width: 84px;
  position: absolute;
  top: calc(50% - 10px);
  left: calc(50% - 32px);
  color: #4caf50;
}
</style>
