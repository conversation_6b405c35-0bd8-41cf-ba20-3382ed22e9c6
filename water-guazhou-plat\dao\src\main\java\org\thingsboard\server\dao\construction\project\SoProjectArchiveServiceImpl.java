package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectFiles;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectArchiveMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchivePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchiveSaveRequest;

@Service
public class SoProjectArchiveServiceImpl implements SoProjectArchiveService {
    @Autowired
    private SoProjectArchiveMapper mapper;

    @Autowired
    private SoProjectOperateRecordService recordService;

    @Override
    public IPage<SoProjectArchive> findAllConditional(SoProjectArchivePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoProjectArchive save(SoProjectArchiveSaveRequest entity) {
        entity.toUpdateModeOn(mapper.getIdByProjectCodeAndTenantId(entity.getProjectCode(), entity.tenantId()));
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            // recordService.save(SoProjectOperateRecordSaveRequest.of(entity, entity.getProjectCode(), "完成%s归档", "项目归档"));
            return mapper.save(e);
        }, mapper::updateFully);
    }

    @Override
    public boolean update(SoProjectArchive entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean canArchive(String projectCode, String tenantId) {
        return mapper.canArchive(projectCode, tenantId);
    }

    @Override
    public SoProjectFiles findFilesByProjectCode(String projectCode, String tenantId) {
        return mapper.findFilesByConstructionCode(projectCode, tenantId);
    }
}
