import{_ as M}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as I}from"./CardTable-rdWOL4_6.js";import{_ as T}from"./index-C9hz-UZb.js";import{d as V,b3 as q,c as m,M as B,r as c,s as L,x as D,S as Y,o as $,a8 as k,g as y,n as A,q as p,F as K,p as f,i as a,cy as N,bh as O,h as U,aw as W,j,J as H,_ as z,b7 as G,C as J}from"./index-r0dFAfgr.js";import{I as s}from"./common-CvK_P_ao.js";import{h as P,i as Q,j as X}from"./malfunctionRepair-CM_eL_AA.js";import{f as Z}from"./DateFormatter-Bm9a68Ax.js";const ee={class:"header-wrapper"},te={class:"content-wrapper"},ae=V({__name:"detail",setup(oe){const u=q(),_=m(),i=m(),g=m(new Date().toString()),{$btnPerms:d}=B(),l=c({id:"",title:""}),h=c({title:"",submit:e=>{n(e)},defaultValue:{},group:[{fields:[{xl:5,type:"input",label:"故障标题",field:"name"},{xl:4,type:"btn-group",btns:[{text:"查询",icon:s.QUERY,perm:!0,click:()=>{var e;(e=_.value)==null||e.Submit()}},{type:"default",text:"重置",svgIcon:L(G),perm:!0,click:()=>{g.value=new Date().toString(),n()}},{type:"success",text:"新增",icon:s.ADD,perm:!0,click:()=>x("新建")}]}]}]}),r=c({title:"新增",labelWidth:"120px",width:"500px",submit:e=>{e.mainId=l.id;let t="新增成功";e.id&&(t="修改成功"),P(e).then(()=>{var o;D.success(t),n(),(o=i.value)==null||o.closeDialog()})},defaultValue:{},group:[{fields:[{type:"textarea",label:"故障标题",field:"name",rules:[{required:!0,message:"请输入故障标题"}]},{type:"textarea",label:"故障具体描述",field:"remark",rules:[{required:!0,message:"请输入故障具体描述"}]}]}]}),b=c({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"故障标题",prop:"name"},{label:"故障具体描述",prop:"remark"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTime",formatter:e=>Z(e.createTime,"YYYY-MM-DD HH:mm:ss")}],operationWidth:"320px",operations:[{type:"primary",text:"编辑",icon:s.EDIT,perm:d("RoleManageEdit"),click:e=>v(e)},{type:"danger",text:"删除",perm:d("RoleManageDelete"),icon:s.DELETE,click:e=>C(e)},{type:"primary",text:"解决方案",icon:s.DETAIL,perm:d("RoleManageEdit"),click:e=>E(e)}],dataList:[],pagination:{hide:!0}}),x=e=>{var t;r.title=e,r.defaultValue={},(t=i.value)==null||t.openDialog()},v=e=>{var t;r.title="编辑",r.defaultValue={...e||{}},(t=i.value)==null||t.openDialog()},C=e=>{Y("确定删除指定故障信息?","删除提示").then(()=>{Q([e.id]).then(()=>{D.success("删除成功"),n()})})},E=e=>{u.push({name:"KB_dealWith",query:{id:e.id,title:e.name}})},n=async e=>{const t={...e||{}};X(l.id,t).then(o=>{b.dataList=o.data.data||[]})};return $(()=>{l.id=k(()=>u.currentRoute.value.query.id||""),l.title=k(()=>u.currentRoute.value.query.title||""),n()}),(e,t)=>{const o=H,w=z,F=T,S=I,R=M;return y(),A("div",{class:W(["wrapper",{isDark:a(j)().isDark}])},[p(F,{class:"detail_card",style:{padding:"10px"}},{default:K(()=>[f("div",ee,[p(o,{text:!0,icon:a(N),onClick:t[0]||(t[0]=le=>e.$router.back())},null,8,["icon"]),f("span",null,O(a(l).title)+" 故障信息",1)]),f("div",te,[(y(),U(w,{key:a(g),ref_key:"refForm",ref:_,config:a(h)},null,8,["config"]))])]),_:1}),p(S,{config:a(b),class:"card-table"},null,8,["config"]),p(R,{ref_key:"refForm1",ref:i,config:a(r)},null,8,["config"])],2)}}}),de=J(ae,[["__scopeId","data-v-a11b367a"]]);export{de as default};
