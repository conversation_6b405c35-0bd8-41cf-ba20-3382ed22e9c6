import{e as a,y as s,a as E,v as J}from"./Point-WxyopZva.js";import{T as d,aO as M,R as p}from"./index-r0dFAfgr.js";import{d$ as c,cW as N,v as T,aR as b,bh as g,bg as $,e0 as m,bu as x,e1 as z,bt as f,af as V}from"./MapView-DaoQedLH.js";import{a as W}from"./normalizeUtilsSync-NMksarRY.js";import{e as u}from"./mat3f64-BVJGbF0t.js";let n=class extends J{constructor(e){super(e)}get bounds(){const e=this.coords;return d(e)||d(e.extent)?null:c(e.extent)}get coords(){var o;const e=(o=M(this.element.georeference))==null?void 0:o.coords;return N(e,this.spatialReference).geometry}get normalizedCoords(){return T.fromJSON(W(this.coords))}get normalizedBounds(){const e=p(this.normalizedCoords)?this.normalizedCoords.extent:null;return p(e)?c(e):null}};a([s()],n.prototype,"spatialReference",void 0),a([s()],n.prototype,"element",void 0),a([s()],n.prototype,"bounds",null),a([s()],n.prototype,"coords",null),a([s()],n.prototype,"normalizedCoords",null),a([s()],n.prototype,"normalizedBounds",null),n=a([E("esri.layers.support.MediaElementView")],n);const t=V(),i=u(),l=u(),y=u();function H(e,o,r){return b(t,o[0],o[1],1),g(t,t,$(i,r)),t[2]===0?m(e,t[0],t[1]):m(e,t[0]/t[2],t[1]/t[2])}function I(e,o,r){return h(l,o[0],o[1],o[2],o[3],o[4],o[5],o[6],o[7]),h(y,r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7]),x(e,z(l,l),y),e[8]!==0&&(e[0]/=e[8],e[1]/=e[8],e[2]/=e[8],e[3]/=e[8],e[4]/=e[8],e[5]/=e[8],e[6]/=e[8],e[7]/=e[8],e[8]/=e[8]),e}function h(e,o,r,v,C,R,B,O,S){f(e,o,v,R,r,C,B,1,1,1),b(t,O,S,1),z(i,e);const[j,k,w]=g(t,t,$(i,i));return f(i,j,0,0,0,k,0,0,0,w),x(e,i,e)}export{H as h,I as j,n as u};
