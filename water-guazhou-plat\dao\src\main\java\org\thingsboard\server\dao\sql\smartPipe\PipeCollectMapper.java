package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PipeCollectRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollect;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-12-07
 */
@Mapper
public interface PipeCollectMapper extends BaseMapper<PipeCollect> {

    IPage<PipeCollect> getList(IPage<PipeCollect> iPage, @Param("param") PipeCollectRequest request);

    void storage(@Param("mainId") String mainId);
}