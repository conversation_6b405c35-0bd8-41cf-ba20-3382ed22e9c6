/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.device;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.dao.model.ToData;
import org.thingsboard.server.dao.model.sql.DeviceEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/6/2017.
 */
@SqlDao
public interface DeviceRepository extends CrudRepository<DeviceEntity, String> {


    @Query("SELECT d FROM DeviceEntity d WHERE d.tenantId = :tenantId " +
            "AND d.customerId = :customerId " +
            "AND LOWER(d.searchText) LIKE LOWER(CONCAT(:searchText, '%')) " +
            "AND d.id > :idOffset ORDER BY d.id")
    List<DeviceEntity> findByTenantIdAndCustomerId(@Param("tenantId") String tenantId,
                                                   @Param("customerId") String customerId,
                                                   @Param("searchText") String searchText,
                                                   @Param("idOffset") String idOffset,
                                                   Pageable pageable);

    @Query("SELECT d FROM DeviceEntity d WHERE d.tenantId = :tenantId " +
            "AND LOWER(d.searchText) LIKE LOWER(CONCAT(:textSearch, '%')) " +
            "AND d.id > :idOffset ORDER BY d.id")
    List<DeviceEntity> findByTenantId(@Param("tenantId") String tenantId,
                                      @Param("textSearch") String textSearch,
                                      @Param("idOffset") String idOffset,
                                      Pageable pageable);

    List<DeviceEntity> findByTenantIdOrderByCreateTimeAsc(String tenantId);

    @Query("SELECT d FROM DeviceEntity d WHERE d.tenantId = :tenantId " +
            "AND d.type = :type " +
            "AND LOWER(d.searchText) LIKE LOWER(CONCAT(:textSearch, '%')) " +
            "AND d.id > :idOffset ORDER BY d.id")
    List<DeviceEntity> findByTenantIdAndType(@Param("tenantId") String tenantId,
                                             @Param("type") String type,
                                             @Param("textSearch") String textSearch,
                                             @Param("idOffset") String idOffset,
                                             Pageable pageable);

    @Query("SELECT d FROM DeviceEntity d WHERE d.tenantId = :tenantId " +
            "AND d.customerId = :customerId " +
            "AND d.type = :type " +
            "AND LOWER(d.searchText) LIKE LOWER(CONCAT(:textSearch, '%')) " +
            "AND d.id > :idOffset ORDER BY d.id")
    List<DeviceEntity> findByTenantIdAndCustomerIdAndType(@Param("tenantId") String tenantId,
                                                          @Param("customerId") String customerId,
                                                          @Param("type") String type,
                                                          @Param("textSearch") String textSearch,
                                                          @Param("idOffset") String idOffset,
                                                          Pageable pageable);

    @Query("SELECT DISTINCT d.type FROM DeviceEntity d WHERE d.tenantId = :tenantId and d.isDelete = '0'")
    List<String> findTenantDeviceTypes(@Param("tenantId") String tenantId);

    DeviceEntity findByTenantIdAndNameOrderByCreateTimeAsc(String tenantId, String name);

    List<DeviceEntity> findByGateWayIdOrderByCreateTimeAsc(String gateWayId);

    List<DeviceEntity> findDevicesByTenantIdAndCustomerIdAndIdInOrderByCreateTimeAsc(String tenantId, String customerId, List<String> deviceIds);

    List<DeviceEntity> findDevicesByTenantIdAndIdInOrderByCreateTimeAsc(String tenantId, List<String> deviceIds);

    @Query("SELECT DISTINCT d.id,d.customerId,d.additionalInfo,d.name,d.type,d.tenantId,at.strValue,d.isDelete ,d.createTime,d.gateWayId,d.templateId FROM DeviceEntity d, AttributeKvEntity at " +
            "WHERE at.entityId = d.id AND at.attributeKey = :strKey  AND d.tenantId = :tenantId")
    List findAllByTenantId(@Param("tenantId") String tenantId, @Param("strKey") String strKey);

    @Query("SELECT DISTINCT d.id,d.customerId,d.additionalInfo,d.name,d.type,d.tenantId,at.strValue,d.isDelete ,d.createTime,d.gateWayId,d.templateId FROM DeviceEntity d, AttributeKvEntity at " +
            "WHERE at.entityId = d.id AND at.attributeKey in :strKeys  AND d.tenantId = :tenantId")
    List findAllByTenantId(@Param("tenantId") String tenantId, @Param("strKeys") List<String> strKeys);

//    @Query("SELECT d FROM DeviceEntity d WHERE d.additionalInfo = ?1")
    List<DeviceEntity> findByAdditionalInfo(JsonNode additionalInfo);

    @Query("SELECT d FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1 AND d.isDelete <> '1'")
    List<DeviceEntity> findDevicesByProjectId(String projectId);

    @Query("SELECT d FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1")
    List<DeviceEntity> findDevicesByProject(String projectId);

    @Query("SELECT DISTINCT d.id,d.customerId,d.additionalInfo,d.name,d.type,d.tenantId,at.strValue,d.isDelete ,d.createTime,d.gateWayId,d.templateId " +
            "FROM DeviceEntity d, AttributeKvEntity at, ProjectRelationEntity pr " +
            "WHERE at.entityId = d.id AND d.id = pr.entityId AND at.attributeKey = :strKey  AND pr.projectId = :projectId")
    List findAllByProjectId(@Param("projectId") String projectId, @Param("strKey") String strKey);

    @Query("SELECT DISTINCT d " +
            "FROM DeviceEntity d, ProjectRelationEntity pr " +
            "WHERE d.id = pr.entityId  AND pr.projectId = :projectId AND (d.toOracle is null or d.toOracle <> '1')")
    List<DeviceEntity> findAllByProjectId(@Param("projectId") String projectId);

    @Query("FROM DeviceEntity d WHERE d.templateId = ?1 and d.isDelete <> '1'")
    List<DeviceEntity> findByTemplateId(String templateId);

    @Query("SELECT DISTINCT d.id,d.customerId,d.additionalInfo,d.name,d.type,d.tenantId,at.strValue,d.isDelete ,d.createTime,d.gateWayId,at.attributeKey,d.templateId" +
            " FROM DeviceEntity d, AttributeKvEntity at" +
            " WHERE at.entityId = d.id AND at.attributeKey = ?2 AND d.templateId = ?1 and d.isDelete <> '1'")
    List findByTemplateId(String templateId, String strKey);

    @Query("SELECT d " +
            "FROM DeviceEntity d, ProjectRelationEntity pr " +
            "WHERE d.id = pr.entityId AND pr.projectId = ?1 AND d.isDelete <> '1'")
    List<DeviceEntity> findGateWayByProjectId(String projectId);


    @Query("SELECT d " +
            "FROM DeviceEntity d, ProjectRelationEntity pr " +
            "WHERE d.id = pr.entityId AND pr.projectId = ?1 AND d.isDelete <> '1' AND d.type = ?2 " +
            "ORDER BY d.createTime ASC")
    List<DeviceEntity> findDevicesByProjectIdAndType(String projectId, String type);

    @Query("SELECT DISTINCT d.id,d.customerId,d.additionalInfo,d.name,d.type,d.tenantId,at.strValue,d.isDelete ,d.createTime,d.gateWayId,at.attributeKey,d.templateId,d.additionalInfo,d.location,d.deviceTypeName" +
            " FROM DeviceEntity d, AttributeKvEntity at " +
            "WHERE at.entityId = d.id AND at.attributeKey in :strKeys  AND d.gateWayId = :gatewayId")
    List findAllByGatewayId(@Param("gatewayId") String gatewayId, @Param("strKeys") List<String> strKeys);

    @Query("SELECT count (d.id) FROM DeviceEntity d WHERE d.isDelete = '0'  AND d.gateWayId in (SELECT d1.id FROM DeviceEntity d1 WHERE d1.type = 'MODBUS')" )
    long countModbusDevice();


    @Query("SELECT count (d.id) FROM DeviceEntity d WHERE d.isDelete = '0' and d.tenantId = :tenantId AND d.gateWayId in (SELECT d1.id FROM DeviceEntity d1 WHERE d1.type = 'MQTT_MODBUS')" )
    long countModbusDeviceByTenantId(@Param("tenantId") String tenantId);

    @Query("SELECT count (d.id) FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1 AND d.gateWayId in (SELECT d1.id FROM DeviceEntity d1 WHERE d1.type = 'MQTT_MODBUS')" )
    long countModbusDeviceByProjectId(@Param("projectId") String projectId);

    @Query("SELECT count (d.id) FROM DeviceEntity d WHERE d.isDelete = '0'  AND d.gateWayId in (SELECT d1.id FROM DeviceEntity d1 WHERE d1.type = 'DTU')" )
    long countDtuDevice();

    @Query("SELECT count (d.id) FROM DeviceEntity d WHERE d.isDelete = '0' AND d.tenantId = :tenantId AND d.gateWayId in (SELECT d1.id FROM DeviceEntity d1 WHERE d1.type = 'DTU')" )
    long countDtuDeviceByTenantId(@Param("tenantId") String tenantId);

    @Query("SELECT count (d.id) FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1 AND  d.gateWayId in (SELECT d1.id FROM DeviceEntity d1 WHERE d1.type = 'DTU')" )
    long countDtuDeviceByProjectId(@Param("projectId") String projectId);

    @Query("SELECT DISTINCT d.id,d.customerId,d.additionalInfo,d.name,d.type,d.tenantId,d.isDelete ,d.createTime,d.gateWayId,d.templateId,pr.projectId FROM DeviceEntity d, ProjectRelationEntity pr " +
            "WHERE pr.entityId = d.id AND d.tenantId = ?1 AND d.isDelete <> '1'")
    List findByTenantId(@Param("tenantId") String tenantId);

    @Query("FROM DeviceEntity d WHERE d.isDelete <> '1'")
    List<DeviceEntity> findCloudDeviceList();

    List<DeviceEntity> findByTenantIdAndTypeIn(String tenantId, List<String> param);

    List<DeviceEntity> findByTenantIdAndIdInOrderByCreateTime(String tenantId, List<String> deviceIdList);

    @Query("SELECT d " +
            "FROM DeviceEntity d, ProjectRelationEntity pr " +
            "WHERE d.id = pr.entityId AND pr.projectId = ?1 AND d.isDelete <> '1' AND d.type = ?2 AND d.name like ?3 " +
            "ORDER BY d.createTime ASC")
    List<DeviceEntity> findDevicesByProjectIdAndTypeAndName(String projectId, String type, String name);

    List<DeviceEntity> findByForeignKey(String foreignKey);

    List<DeviceEntity> findByForeignKeyIn(List<String> deviceCodeList);

    @Query("SELECT d FROM DeviceEntity d WHERE d.tenantId = ?2 AND d.deviceTypeName = ?1")
    List<DeviceEntity> findByDeviceTypeName(String deviceTypeName, String tenantId);

    @Query("select d from  DeviceEntity d where d.deviceTypeName is not null and d.deviceTypeName <> '' and (d.oracleKey = '' or d.oracleKey is null)")
    List<DeviceEntity> findAllNotToOracle();

    @Query(value = "SELECT DISTINCT " +
            "mb.book_name," +
            "ci.cust_code," +
            "ci.cust_name," +
            "mi.meter_code," +
            "o.NAME," +
            "o.NAME || mb.book_name || ci.address AS address," +
            "d.device_type_name," +
            "wn.nature_name " +
            "FROM " +
            "device d " +
            "LEFT JOIN meter_info mi ON d.foreign_key = mi.meter_code " +
            "LEFT JOIN cust_info ci ON mi.cust_code = ci.cust_code " +
            "LEFT JOIN meter_books mb ON ci.book_id = mb.ID " +
            "LEFT JOIN organization o ON mb.area_code = o.ID " +
            "LEFT JOIN water_nature wn ON mi.wnid = wn.ID " +
            "WHERE " +
            "d.ID = ?1", nativeQuery = true)
    Object[][] findMeterInfo(String deviceId);

    @Query("SELECT d FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId IN ?1 AND d.isDelete <> '1'")
    List<DeviceEntity> findDevicesByProjectId(List<String> projectIdList);

    List<DeviceEntity> findAllByIdIn(Set<String> deviceIdList);
}
