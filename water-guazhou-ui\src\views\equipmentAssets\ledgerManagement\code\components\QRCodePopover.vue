<template>
  <el-popover
    v-if="true"
    trigger="click"
    placement="left"
    width="100px"
  >
    <qrcode-vue
      :value="props.row.id"
      :size="100"
      level="H"
    />
    <template #reference>
      <el-button
        class="iconfont icon-erweima1 operation-btn"
        :text="true"
        :size="'small'"
        :style="{ color: '#09a3cd', border: 'none' }"
      >
        二维码
      </el-button>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import QrcodeVue from 'qrcode.vue'

const props = defineProps<{row: any}>()

</script>

<style lang="scss" scoped>
.operation-btn {
  margin-right: 12px;
  font-size: 16px !important;
}
</style>
