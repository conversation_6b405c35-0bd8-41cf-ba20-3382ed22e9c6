import{d as a,a0 as c,c as d,o as r,Q as _,g as v,n as p,p as s,i as e,C as h}from"./index-r0dFAfgr.js";import{r as o}from"./水泵_在线-2IzRX-2K.js";const u={class:"main"},x={class:"card zutai-card"},f={class:"card-content",style:{bottom:"47%",right:"31%",width:"130px"}},m={class:"row"},w={class:"status"},g=["src"],y={class:"row"},b={class:"status"},j=["src"],B={class:"card-content",style:{bottom:"47%",left:"36%",width:"130px"}},I={class:"row"},C={class:"status"},k=["src"],A={class:"row"},L={class:"status"},M=["src"],P=a({__name:"jyj_overview",setup(S){const i=c();d({});const l=d(),n=()=>{console.log(i.projectList),i.projectList[0].id};return r(()=>{n(),l.value=setInterval(()=>{n()},3e4)}),_(()=>{clearInterval(l.value)}),(z,t)=>(v(),p("div",u,[s("div",x,[s("div",f,[t[2]||(t[2]=s("div",{class:"card-title",style:{width:"110px"}},[s("span",{style:{color:"#d8feff","text-align":"center"}},"PAC制备装置")],-1)),s("div",m,[t[0]||(t[0]=s("div",{class:"label"},"计量泵1#：",-1)),s("div",w,[s("img",{src:e(o),style:{width:"15px",height:"15px"}},null,8,g)])]),s("div",y,[t[1]||(t[1]=s("div",{class:"label"},"计量泵2#：",-1)),s("div",b,[s("img",{src:e(o),style:{width:"15px",height:"15px"}},null,8,j)])])]),s("div",B,[t[5]||(t[5]=s("div",{class:"card-title",style:{width:"110px"}},[s("span",{style:{color:"#d8feff","text-align":"center"}},"PAM制备装置")],-1)),s("div",I,[t[3]||(t[3]=s("div",{class:"label"},"计量泵1#：",-1)),s("div",C,[s("img",{src:e(o),style:{width:"15px",height:"15px"}},null,8,k)])]),s("div",A,[t[4]||(t[4]=s("div",{class:"label"},"计量泵2#：",-1)),s("div",L,[s("img",{src:e(o),style:{width:"15px",height:"15px"}},null,8,M)])])])])]))}}),N=h(P,[["__scopeId","data-v-b1c2b030"]]);export{N as default};
