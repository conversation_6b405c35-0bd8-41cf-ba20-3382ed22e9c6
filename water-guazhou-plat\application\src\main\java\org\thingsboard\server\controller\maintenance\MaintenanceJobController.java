package org.thingsboard.server.controller.maintenance;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.MaintenanceJobEntity;
import org.thingsboard.server.dao.repair.MaintenanceJobService;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("api/maintenance/job")
public class MaintenanceJobController extends BaseController {

    @Autowired
    private MaintenanceJobService maintenanceJobService;

    @GetMapping("detail/{id}")
    public MaintenanceJobEntity get(@PathVariable String id) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenanceJobService.detail(id, currentUser.getTenantId());
    }

    @GetMapping("list")
    public PageData<MaintenanceJobEntity> findList(@RequestParam int page, @RequestParam int size,
                                                   @RequestParam(required = false, defaultValue = "") String name,
                                                   @RequestParam(required = false, defaultValue = "") String deviceId) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenanceJobService.findList(page, size, name, deviceId, currentUser);
    }

    @DeleteMapping
    public void remove(@RequestBody List<String> ids) {
        maintenanceJobService.remove(ids);
    }

    @GetMapping("yearCount")
    public Object yearCount(@RequestParam String deviceId) throws ThingsboardException {
        PageData<MaintenanceJobEntity> pageResult = maintenanceJobService.findList(1, 999999, "", deviceId, getCurrentUser());
        List<MaintenanceJobEntity> data = pageResult.getData();

        if (data == null || data.isEmpty()) {
            return new ArrayList<>();
        }

        // 处理数据
        Date now = new Date();
        Calendar instance = Calendar.getInstance();
        instance.setTime(now);

        int month = instance.get(Calendar.MONTH) + 1;

        instance.set(Calendar.MONTH, 0);
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        // 数据按时间过滤
        data = data.stream().filter(job -> {
            Date executeTime = job.getExecuteTime();
            if (executeTime.getTime() < now.getTime() && executeTime.getTime() > instance.getTime().getTime()) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        if (data.isEmpty()) {
            return new ArrayList<>();
        }

        Map<Integer, List<MaintenanceJobEntity>> dataMap = new LinkedHashMap<>();
        for (int i = 1; i <= month; i++) {
            dataMap.put(i, new ArrayList<>());
        }

        // 数据分类
        for (MaintenanceJobEntity job : data) {
            Date executeTime = job.getExecuteTime();
            instance.setTime(executeTime);
            int m = instance.get(Calendar.MONTH) + 1;

            List<MaintenanceJobEntity> list = dataMap.get(m);
            list.add(job);

            dataMap.put(m, list);
        }

        List<JSONObject> result = new ArrayList<>();
        for (Map.Entry<Integer, List<MaintenanceJobEntity>> entry : dataMap.entrySet()) {
            Integer key = entry.getKey();
            List<MaintenanceJobEntity> value = entry.getValue();

            JSONObject resultObj = new JSONObject();
            resultObj.put("month", key + "月");
            resultObj.put("count", value.size());

            result.add(resultObj);
        }

        return result;
    }

}
