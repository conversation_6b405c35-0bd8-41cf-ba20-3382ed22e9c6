package org.thingsboard.server.dao.project;

import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.datavVO.PieVO;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.sql.ProjectEntity;

import java.util.List;
import java.util.Map;

public interface ProjectService {

    ProjectEntity findById(String id);

    ProjectEntity save(ProjectEntity entity);

    ProjectEntity update(ProjectEntity entity);

    ProjectEntity deleteById(String id);

    void  deleteByTenantId(TenantId tenantId);

    /**
     * 查询指定项目的子项目
     *
     * @param id
     * @return
     */
    List<ProjectTreeVO> findChildrenById(String id);

    /**
     * 查询所有的根项目
     *
     * @return
     * @param s
     * @param devices
     * @param id
     * @param authority
     */
    List<ProjectTreeVO> findRootProject(String tenantId, Boolean devices, UserId id, Authority authority);

    /**
     * 查询企业的项目树
     *
     * @param tenantId
     * @param projectList
     * @param gatewayMap
     * @param getDevice
     * @return
     */
    List<ProjectTreeVO> findProjectList(List<ProjectTreeVO> projectList, Map<String, List<Device>> gatewayMap, Boolean getDevice);

    List<ProjectTreeVO> findAll();

    List<ProjectEntity> findAllProject();

    List<ProjectEntity> findByTenantId(TenantId tenantId);

    long findAllCounts();

    long countsByTenant(String tenantId);

    List<ProjectTreeVO> findDataSourceAndDeviceProjectTree(String tenantId);

    List<ProjectTreeVO> findProjectAndGatewayTree(String tenantIdString);

    List<PieVO> projectBusinessStatistics(TenantId tenantId);


    List<ProjectEntity> findAllChild(TenantId tenantId, String projectId);
}
