<template>
  <Search
    ref="refSearch"
    :config="SearchConfig"
    class="search"
  ></Search>
  <FormTable
    :config="TableConfig"
    class="table-box"
  ></FormTable>
  <DialogForm
    ref="refDialog"
    :config="DialogConfig"
  ></DialogForm>
</template>
<script lang="ts" setup>
import {
  AddDmaPartitionLossPoint,
  DeleteDmaPartitionLossPoint,
  GetDmaPartitionLossPoint
} from '@/api/mapservice/dma/partitionResources'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'

const props = defineProps<{ partition?: NormalOption }>()
const refSearch = ref<ISearchIns>()
const refDialog = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  filters: [{ type: 'date', label: '日期', field: 'findDate' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          iconifyIcon: 'ep:search',
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          perm: true,
          iconifyIcon: 'ep:refresh',
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            // refreshData()
          }
        },
        {
          perm: true,
          iconifyIcon: 'ep:circle-plus',
          text: '新增',
          type: 'success',
          click: () => handleAou()
        }
      ]
    }
  ]
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '分区名称', prop: 'partitionName' },
    { label: '分区状态', prop: 'statusName' },
    { label: '漏点日期', prop: 'findDate' },
    { label: '漏点数量', prop: 'num' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '编辑',
      iconifyIcon: 'ep:edit',
      click: row => handleAou(row)
    },
    {
      perm: true,
      text: '删除',
      iconifyIcon: 'ep:delete',
      type: 'danger',
      click: row => handleDelete(row)
    }
  ]
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaPartitionLossPoint({
      ...query,
      partitionId: props.partition?.value,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    })
    const data = res.data.data || {}
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const handleAou = (row?: any) => {
  DialogConfig.defaultValue = {
    ...(row || {})
  }
  DialogConfig.title = row ? '编辑漏点' : '添加漏点'
  refDialog.value?.openDialog()
}
const handleDelete = (row?: any) => {
  const ids = row ? [row.id] : []
  if (!ids.length) {
    SLMessage.error('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除?', '提示信息')
    .then(async () => {
      try {
        const res = await DeleteDmaPartitionLossPoint(ids)
        if (res.data.code === 200) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      //
    })
}
const DialogConfig = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          lg: 24,
          xl: 12,
          type: 'date',
          label: '漏点日期',
          field: 'findDate',
          rules: [{ required: true, message: '请选择漏点日期' }]
        },
        {
          lg: 24,
          xl: 12,
          type: 'input-number',
          label: '漏点数量',
          field: 'num',
          rules: [{ required: true, message: '请输入漏点数量' }]
        }
      ]
    }
  ],
  submit: async params => {
    DialogConfig.submitting = true
    try {
      const res = await AddDmaPartitionLossPoint({
        ...params,
        partitionId: props.partition?.value
      })
      if (res.data.code === 200) {
        SLMessage.success('提交成功')
        refreshData()
        refDialog.value?.closeDialog()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('提交失败')
    }
    DialogConfig.submitting = false
  }
})
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.search {
  margin: 0 -20px 10px -20px;
}
.table-box {
  height: calc(100% - 40px);
}
</style>
