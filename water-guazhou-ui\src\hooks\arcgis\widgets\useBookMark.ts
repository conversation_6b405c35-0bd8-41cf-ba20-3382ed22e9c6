import Bookmarks from '@arcgis/core/widgets/Bookmarks.js'
import Expand from '@arcgis/core/widgets/Expand.js'

export const useBookMark = () => {
  let bookmarks: __esri.Bookmarks | undefined
  let bkExpand: __esri.Expand | undefined
  const init = (view: __esri.MapView, widgetPosition?: string) => {
    bookmarks = new Bookmarks({
      view,
      // allows bookmarks to be added, edited, or deleted
      editingEnabled: true,
      bookmarks: []
    })
    bkExpand = new Expand({
      view,
      content: bookmarks,
      expandTooltip: '书签'
    })
    bkExpand && view.ui?.add(bkExpand, widgetPosition || 'top-right')
    return bkExpand
  }
  const destroy = () => {
    bookmarks?.destroy()
    bkExpand?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}

export default useBookMark
