package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 水务-巡检任务主表
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_XJRW_M_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SwXjrwMEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_XJRW_M_CONTENT)
    private String content;

    @Column(name = ModelConstants.SHUIWU_XJRW_M_EXECUTE_TIME)
    private Date executeTime;

    @Column(name = ModelConstants.SHUIWU_XJRW_M_LIMIT_TIME)
    private Integer limitTime;

    @Column(name = ModelConstants.SHUIWU_XJRW_M_PERIOD_TIME)
    private Integer periodTime;

    @Column(name = ModelConstants.SHUIWU_XJRW_M_USERS)
    private String users;

    @Column(name = ModelConstants.SHUIWU_XJRW_M_STATUS)
    private String status;

    @Column(name = ModelConstants.END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.USER_ID_PROPERTY)
    private String userId;

    @Column(name = ModelConstants.START_TIME)
    private Date startTime;

    @Transient
    private List<SwXjrwCEntity> jobList;

    @Transient
    private String userNames;

    public static SwXjrwMEntity buildXJRWM(SwXjrwglMEntity xjrwglm) {
        SwXjrwMEntity xjrwm = new SwXjrwMEntity();
        xjrwm.setContent(xjrwglm.getContent());
        xjrwm.setExecuteTime(xjrwglm.getExecuteTime());
        xjrwm.setUsers(xjrwglm.getUsers());
        xjrwm.setLimitTime(xjrwglm.getLimitTime());
        xjrwm.setPeriodTime(xjrwglm.getPeriodTime());

        return xjrwm;
    }
}
