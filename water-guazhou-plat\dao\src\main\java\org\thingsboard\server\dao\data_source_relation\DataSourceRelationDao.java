package org.thingsboard.server.dao.data_source_relation;

import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.DataSourceRelationEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:03
 */
public interface DataSourceRelationDao {

    boolean mountRelation(String originateId,List<String> dataSourceId,String entityType);

    boolean mountRelation(String originateId,String dataSourceId,String type);

    DataSourceRelationEntity findByDataSourceId(String dataSourceId);



}
