"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6565],{7301:(e,t,r)=>{r.r(t),r.d(t,{registerFunctions:()=>d});var n=r(27535),o=r(77286),s=r(61363),a=r(53073),i=r(75148),l=r(9361),h=r(8667);function c(e){return e instanceof l.Z}function u(e,t,r,u){return u(e,t,(async(u,d,m)=>{if(m.length<2)throw new n.aV(e,n.rH.WrongNumberOfParameters,t);if(null===(m=(0,s.G)(m))[0]&&null===m[1])return!1;if((0,s.T)(m[0])){if(m[1]instanceof l.Z)return new a.Z({parentfeatureset:m[0],relation:r,relationGeom:m[1]});if(null===m[1])return new i.Z({parentfeatureset:m[0]});throw new n.aV(e,n.rH.InvalidParameter,t)}if(c(m[0])){if(c(m[1])){switch(r){case"esriSpatialRelEnvelopeIntersects":return(0,h.kK)((0,o.SV)(m[0]),(0,o.SV)(m[1]));case"esriSpatialRelIntersects":return(0,h.kK)(m[0],m[1]);case"esriSpatialRelContains":return(0,h.r3)(m[0],m[1]);case"esriSpatialRelOverlaps":return(0,h.Nm)(m[0],m[1]);case"esriSpatialRelWithin":return(0,h.uh)(m[0],m[1]);case"esriSpatialRelTouches":return(0,h.W4)(m[0],m[1]);case"esriSpatialRelCrosses":return(0,h.jU)(m[0],m[1])}throw new n.aV(e,n.rH.InvalidParameter,t)}if((0,s.T)(m[1]))return new a.Z({parentfeatureset:m[1],relation:r,relationGeom:m[0]});if(null===m[1])return!1;throw new n.aV(e,n.rH.InvalidParameter,t)}if(null!==m[0])throw new n.aV(e,n.rH.InvalidParameter,t);return(0,s.T)(m[1])?new i.Z({parentfeatureset:m[1]}):!(m[1]instanceof l.Z||null===m[1])&&void 0}))}function d(e){"async"===e.mode&&(e.functions.intersects=function(t,r){return u(t,r,"esriSpatialRelIntersects",e.standardFunctionAsync)},e.functions.envelopeintersects=function(t,r){return u(t,r,"esriSpatialRelEnvelopeIntersects",e.standardFunctionAsync)},e.signatures.push({name:"envelopeintersects",min:2,max:2}),e.functions.contains=function(t,r){return u(t,r,"esriSpatialRelContains",e.standardFunctionAsync)},e.functions.overlaps=function(t,r){return u(t,r,"esriSpatialRelOverlaps",e.standardFunctionAsync)},e.functions.within=function(t,r){return u(t,r,"esriSpatialRelWithin",e.standardFunctionAsync)},e.functions.touches=function(t,r){return u(t,r,"esriSpatialRelTouches",e.standardFunctionAsync)},e.functions.crosses=function(t,r){return u(t,r,"esriSpatialRelCrosses",e.standardFunctionAsync)},e.functions.relate=function(t,r){return e.standardFunctionAsync(t,r,((e,o,a)=>{if(a=(0,s.G)(a),(0,s.y)(a,3,3,t,r),c(a[0])&&c(a[1]))return(0,h.LV)(a[0],a[1],(0,s.j)(a[2]));if(a[0]instanceof l.Z&&null===a[1])return!1;if(a[1]instanceof l.Z&&null===a[0])return!1;if((0,s.T)(a[0])&&null===a[1])return new i.Z({parentfeatureset:a[0]});if((0,s.T)(a[1])&&null===a[0])return new i.Z({parentfeatureset:a[1]});if((0,s.T)(a[0])&&a[1]instanceof l.Z)return a[0].relate(a[1],(0,s.j)(a[2]));if((0,s.T)(a[1])&&a[0]instanceof l.Z)return a[1].relate(a[0],(0,s.j)(a[2]));if(null===a[0]&&null===a[1])return!1;throw new n.aV(t,n.rH.InvalidParameter,r)}))})}},5732:(e,t,r)=>{r.d(t,{c:()=>n});var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},64830:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(70586);class o{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,n.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,n.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},80903:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(50758),o=r(92604),s=r(95330),a=r(64830),i=r(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new a.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((r,n)=>{let o=!0;const a=e=>{(0,s.k_)(t.signal),o&&(o=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let o=0;o<e.length;++o){const l=e[o];(0,s.y8)(l)?this._clientPromises[o]=l.then((e=>(this._clients[o]=new i.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),a(r),this._clients[o])),(()=>(a(n),null))):(this._clients[o]=new i.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[o]=Promise.resolve(this._clients[o]),a(r))}}))}broadcast(e,t,r){const n=new Array(this._clientPromises.length);for(let o=0;o<this._clientPromises.length;++o){const s=this._clientPromises[o];n[o]=s.then((n=>n?.invoke(e,t,r)))}return n}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,s.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,r){let n;Array.isArray(r)?(o.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),n={transferList:r}):n=r;const a=(0,s.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:n,deferred:a});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return a.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,n.AL)(this._clients.map((r=>r.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let r=t.length;for(let n=0;n<this._clientPromises.length;++n)this._clientPromises[n].then((o=>{o&&(t[n]=o.openPort()),0==--r&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,r)=>{r.d(t,{bA:()=>F});var n=r(20102),o=r(80442),s=r(95330),a=r(80903),i=r(25045),l=r(40330),h=r(92604),c=r(70586),u=r(94362),d=r(99880),m=r(68773),f=(r(2587),r(17452));const g={};function p(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=g.baseUrl),t}var y=r(41213);class w{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...r)=>e[t](...r)}))}}class b{constructor(){this._dispatcher=new w,this._workerPostMessage({type:u.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,y.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,r){this._dispatcher.addEventListener(e,t,r)}removeEventListener(e,t,r){this._dispatcher.removeEventListener(e,t,r)}_workerPostMessage(e){(0,y.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,u.QM)(e);if(t&&t.type===u.Cs.OPEN){const{modulePath:e,jobId:r}=t;let n=await i.default.loadWorker(e);n||(n=await import(e));const o=i.default.connect(n);this._workerPostMessage({type:u.Cs.OPENED,jobId:r,data:o})}}}var k=r(70171),v=r(17202);const _=h.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:P}=u.Cs;let E,S;const L="Failed to create Worker. Fallback to execute module in main thread";async function Z(e){return new Promise((t=>{function r(o){const s=(0,u.QM)(o);s&&s.type===P&&(e.removeEventListener("message",r),e.removeEventListener("error",n),t(e))}function n(t){t.preventDefault(),e.removeEventListener("message",r),e.removeEventListener("error",n),_.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new b).addEventListener("message",r),e.addEventListener("error",n)}e.addEventListener("message",r),e.addEventListener("error",n)}))}function A(){let e;if(null!=m.Z.default){const t={...m.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(m.Z));e.assetsPath=(0,f.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,f.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,k.Kd)(),e.has={"esri-csp-restrictions":(0,o.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,o.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,o.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,o.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,o.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,o.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,o.Z)("esri-atomics"),"esri-shared-array-buffer":(0,o.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,o.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,o.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,o.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,o.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,f.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,f.hF)(e.workers.workerPath):e.workers.workerPath=(0,f.hF)((0,d.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=m.Z.workers.loaderConfig,r=p({baseUrl:t?.baseUrl,locale:(0,k.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),n={version:l.i8,buildDate:v.r,revision:v.$};return JSON.stringify({esriConfig:e,loaderConfig:r,kernelInfo:n})}let M=0;const{ABORT:C,INVOKE:I,OPEN:R,OPENED:D,RESPONSE:O}=u.Cs;class j{static async create(e){const t=await async function(){if(!(0,o.Z)("esri-workers")||((0,o.Z)("mozilla"),0))return Z(new b);if(!E&&!S)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${A()}'`);E=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){S=e||{}}let e;if(E)try{e=new Worker(E,{name:"esri-worker-"+M++})}catch(t){_.warn(L,S),e=new b}else _.warn(L,S),e=new b;return Z(e)}();return new j(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),h.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:r}=t,n=(0,u.jt)();return new Promise(((t,o)=>{const a={resolve:t,reject:o,abortHandle:(0,s.$F)(r,(()=>{this._outJobs.delete(n),this._post({type:C,jobId:n})}))};this._outJobs.set(n,a),this._post({type:R,jobId:n,modulePath:e})}))}_onMessage(e){const t=(0,u.QM)(e);if(t)switch(t.type){case D:this._onOpenedMessage(t);break;case O:this._onResponseMessage(t);break;case C:this._onAbortMessage(t);break;case I:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,n=t.get(r);n&&(n.controller&&n.controller.abort(),t.delete(r))}_onInvokeMessage(e){const{methodName:t,jobId:r,data:n,abortable:o}=e,a=o?new AbortController:null,i=this._inJobs,h=l.Nv[t];let c;try{if("function"!=typeof h)throw new TypeError(`${t} is not a function`);c=h.call(null,n,{signal:a?a.signal:null})}catch(e){return void this._post({type:O,jobId:r,error:(0,u.AB)(e)})}(0,s.y8)(c)?(i.set(r,{controller:a,promise:c}),c.then((e=>{i.has(r)&&(i.delete(r),this._post({type:O,jobId:r},e))}),(e=>{i.has(r)&&(i.delete(r),e||(e={message:"Error encountered at method"+t}),(0,s.D_)(e)||this._post({type:O,jobId:r,error:(0,u.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:O,jobId:r},c)}_onOpenedMessage(e){const{jobId:t,data:r}=e,n=this._outJobs.get(t);n&&(this._outJobs.delete(t),(0,c.hw)(n.abortHandle),n.resolve(r))}_onResponseMessage(e){const{jobId:t,error:r,data:o}=e,s=this._outJobs.get(t);s&&(this._outJobs.delete(t),(0,c.hw)(s.abortHandle),r?s.reject(n.Z.fromJSON(JSON.parse(r))):s.resolve(o))}_post(e,t,r){return(0,u.oi)(this.worker,e,t,r)}}let N=(0,o.Z)("esri-workers-debug")?1:(0,o.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,o.Z)("host-browser")?navigator.hardwareConcurrency-1:0;N||(N=(0,o.Z)("safari")&&(0,o.Z)("mac")?7:2);let T=0;const H=[];async function J(e,t){const r=new a.Z;return await r.open(e,t),r}async function F(e,t={}){if("string"!=typeof e)throw new n.Z("workers:undefined-module","modulePath is missing");let r=t.strategy||"distributed";if((0,o.Z)("host-webworker")&&!(0,o.Z)("esri-workers")&&(r="local"),"local"===r){let r=await i.default.loadWorker(e);r||(r=await import(e)),(0,s.k_)(t.signal);const n=t.client||r;return J([i.default.connect(r)],{...t,client:n})}if(await async function(){if(U)return U;W=new AbortController;const e=[];for(let t=0;t<N;t++){const r=j.create(t).then((e=>(H[t]=e,e)));e.push(r)}return U=Promise.all(e),U}(),(0,s.k_)(t.signal),"dedicated"===r){const r=T++%N;return J([await H[r].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const r=Math.min(t.maxNumWorkers,N);if(r<N){const n=new Array(r);for(let o=0;o<r;++o){const r=T++%N;n[o]=H[r].open(e,t)}return J(n,t)}}return J(H.map((r=>r.open(e,t))),t)}let W,U=null},2587:(e,t,r)=>{r(90344),r(18848),r(940),r(70171);var n=r(94443),o=r(3172),s=r(20102),a=r(70586);async function i(e){if((0,a.pC)(l.fetchBundleAsset))return l.fetchBundleAsset(e);const t=await(0,o.default)(e,{responseType:"text"});return JSON.parse(t.data)}const l={};var h,c=r(99880);(0,n.tz)((h={pattern:"esri/",location:c.V},new class{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let n;n="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=n,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,o){const a=t.exec(r);if(!a)throw new s.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=a[1]?`${a[1]}/`:"",h=a[2],c=(0,n.Su)(o),u=`${l}${h}.json`,d=c?`${l}${h}_${c}.json`:u;let m;try{m=await i(e(d))}catch(t){if(d===u)throw new s.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{m=await i(e(u))}catch(e){throw new s.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return m}(this.getAssetUrl,this.matcher,e,t)}}(h)))},90344:(e,t,r)=>{r.d(t,{Ze:()=>p,p6:()=>y});var n=r(35454),o=r(70171);const s={year:"numeric",month:"numeric",day:"numeric"},a={year:"numeric",month:"long",day:"numeric"},i={year:"numeric",month:"short",day:"numeric"},l={year:"numeric",month:"long",weekday:"long",day:"numeric"},h={hour:"numeric",minute:"numeric"},c={...h,second:"numeric"},u={"short-date":s,"short-date-short-time":{...s,...h},"short-date-short-time-24":{...s,...h,hour12:!1},"short-date-long-time":{...s,...c},"short-date-long-time-24":{...s,...c,hour12:!1},"short-date-le":s,"short-date-le-short-time":{...s,...h},"short-date-le-short-time-24":{...s,...h,hour12:!1},"short-date-le-long-time":{...s,...c},"short-date-le-long-time-24":{...s,...c,hour12:!1},"long-month-day-year":a,"long-month-day-year-short-time":{...a,...h},"long-month-day-year-short-time-24":{...a,...h,hour12:!1},"long-month-day-year-long-time":{...a,...c},"long-month-day-year-long-time-24":{...a,...c,hour12:!1},"day-short-month-year":i,"day-short-month-year-short-time":{...i,...h},"day-short-month-year-short-time-24":{...i,...h,hour12:!1},"day-short-month-year-long-time":{...i,...c},"day-short-month-year-long-time-24":{...i,...c,hour12:!1},"long-date":l,"long-date-short-time":{...l,...h},"long-date-short-time-24":{...l,...h,hour12:!1},"long-date-long-time":{...l,...c},"long-date-long-time-24":{...l,...c,hour12:!1},"long-month-year":{month:"long",year:"numeric"},"short-month-year":{month:"short",year:"numeric"},year:{year:"numeric"},"short-time":h,"long-time":c},d=(0,n.w)()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"}),m=(d.apiValues,d.toJSON.bind(d),d.fromJSON.bind(d),{ar:"ar-u-nu-latn-ca-gregory"});let f=new WeakMap,g=u["short-date-short-time"];function p(e){return e?u[e]:null}function y(e,t){return function(e){const t=e||g;let r=f.get(t);if(!r){const e=(0,o.Kd)(),n=m[(0,o.Kd)()]||e;r=new Intl.DateTimeFormat(n,t),f.set(t,r)}return r}(t).format(e)}(0,o.Ze)((()=>{f=new WeakMap,g=u["short-date-short-time"]}))},18848:(e,t,r)=>{r.d(t,{sh:()=>l,uf:()=>h});var n=r(70586),o=r(70171);const s={ar:"ar-u-nu-latn"};let a=new WeakMap,i={};function l(e={}){const t={};return null!=e.digitSeparator&&(t.useGrouping=e.digitSeparator),null!=e.places&&(t.minimumFractionDigits=t.maximumFractionDigits=e.places),t}function h(e,t){return-0===e&&(e=0),function(e){const t=e||i;if(!a.has(t)){const r=(0,o.Kd)(),n=s[(0,o.Kd)()]||r;a.set(t,new Intl.NumberFormat(n,e))}return(0,n.j0)(a.get(t))}(t).format(e)}(0,o.Ze)((()=>{a=new WeakMap,i={}}))},940:(e,t,r)=>{r.d(t,{n:()=>h});var n=r(92604),o=r(78286),s=r(19153),a=r(90344),i=r(18848);const l=n.Z.getLogger("esri.intl.substitute");function h(e,t,r={}){const{format:n={}}=r;return(0,s.gx)(e,(e=>function(e,t,r){let n,s;const a=e.indexOf(":");if(-1===a?n=e.trim():(n=e.slice(0,a).trim(),s=e.slice(a+1).trim()),!n)return"";const i=(0,o.hS)(n,t);if(null==i)return"";const l=(s?r?.[s]:null)??r?.[n];return l?c(i,l):s?u(i,s):d(i)}(e,t,n)))}function c(e,t){switch(t.type){case"date":return(0,a.p6)(e,t.intlOptions);case"number":return(0,i.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),d(e)}}function u(e,t){switch(t.toLowerCase()){case"dateformat":return(0,a.p6)(e);case"numberformat":return(0,i.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,a.p6)(e):/^numberformat/i.test(t)?(0,i.uf)(e):d(e)}}function d(e){switch(typeof e){case"string":return e;case"number":return(0,i.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,a.p6)(e):""}}}}]);