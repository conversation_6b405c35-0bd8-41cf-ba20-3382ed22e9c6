<template>
  <div class="g-split" ref="gSplit">
    <!-- 水平方向 -->
    <div class="horizontal" v-if="state.showHorizontal">
      <div class="left-panel position" :style="state.horizontalLeftPanel">
        <slot name="left"></slot>
      </div>
      <div
        class="horizontal-trigger-panel position"
        :style="state.horizontaltriggerPanel"
        ref="horizontalTriggerPanel"
      >
        <!-- 触发拖动的元素可以是默认的，当用户提供了，使用用户的 -->
        <slot name="trigger" v-if="$slots.trigger"></slot>
        <div class="trigger-content-default-wrap" v-else>
          <div class="trigger-content">
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
          </div>
        </div>
      </div>
      <div class="right-panel position" :style="state.horizontalRightPanel">
        <slot name="right"></slot>
      </div>
    </div>
    <!-- 垂直方向 -->
    <div class="vertical" v-if="state.showVertical">
      <div class="top-panel position" :style="state.verticalTopPanel">
        <slot name="top"></slot>
      </div>
      <div
        class="vertical-trigger-panel position"
        :style="state.verticaltriggerPanel"
        ref="verticalTriggerPanel"
      >
        <!-- 触发拖动的元素可以是默认的，当用户提供了，使用用户的 -->
        <slot name="trigger" v-if="$slots.trigger"></slot>
        <div class="trigger-content-default-wrap" v-else>
          <div class="trigger-content">
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
            <i class="trigger-bar"></i>
          </div>
        </div>
      </div>
      <div class="bottom-panel position" :style="state.verticalBottomPanel">
        <slot name="bottom"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const gSplit = ref();
const horizontalTriggerPanel = ref();
const verticalTriggerPanel = ref();

/**
 * 定义组件的props。
 *
 * @param value - 滑块的当前值，可以是数字或字符串类型，默认为0.2。
 * @param mode - 滑块的方向，可以是'horizontal'或'vertical'，默认为'horizontal'。
 * @param min - 滑块的最小值，默认为0.1。
 * @param max - 滑块的最大值，默认为0.8。
 * @returns 返回一个经过默认值设置的props对象。
 */
const props = withDefaults(
  defineProps<{
    value?: number | string;
    mode?: 'horizontal' | 'vertical';
    min?: number;
    max?: number;
  }>(),
  {
    value: 0.2,
    mode: 'horizontal',
    min: 0.1,
    max: 0.8
  }
);

const emit = defineEmits(['on-move-start', 'on-moving', 'on-move-end']);

const state = reactive({
  // 拖拽占盒子最小比例
  min: props.min,
  // 拖拽占盒子最大比例
  max: props.max,
  // 左侧偏移量，默认为初始最小值
  left: (props.value as any) * 1,
  // 上面偏移量，默认为初始最小值
  top: (props.value as any) * 1,
  // 上下面板还是左右面板，默认为上下格式
  mode: props.mode as any,
  // split面板的宽度
  gSplitWidth: 0,
  // solt面板的高度
  gSplitHeight: 0,
  // 水平拖拽部分的长条的宽度
  horizontalTriggerPanelWidht: 0,
  // 垂直拖拽部分的长条的高度
  verticalTriggerPanelHeight: 0,
  // 是否显示左右拖动面板
  showHorizontal: computed(() => {
    return props.mode === 'horizontal';
  }),
  // // 是否显示上下拖动面板
  showVertical: computed(() => {
    return props.mode === 'vertical';
  }),
  // 左侧面板偏移量
  horizontalLeftPanel: computed(() => {
    return {
      left: 0,
      right: (1 - state.left) * 100 + '%'
    };
  }),
  // 右侧面板偏移量
  horizontalRightPanel: computed(() => {
    return {
      left:
        (state.left + state.horizontalTriggerPanelWidht / state.gSplitWidth) *
          100 +
        '%',
      width:
        'calc(100% - ' +
        (state.left + state.horizontalTriggerPanelWidht / state.gSplitWidth) *
          100 +
        '%)'
    };
  }),
  // 左右面板中间trigger拖拽部分的偏移量
  horizontaltriggerPanel: computed(() => {
    return {
      left: state.left * 100 + '%'
    };
  }),
  // 上面面板偏移量
  verticalTopPanel: computed(() => {
    return {
      top: 0,
      bottom: (1 - state.top) * 100 + '%'
    };
  }),
  // 下面面板偏移量
  verticalBottomPanel: computed(() => {
    return {
      top:
        (state.top + state.verticalTriggerPanelHeight / state.gSplitHeight) *
          100 +
        '%',
      height:
        'calc(100% - ' +
        (state.top + state.verticalTriggerPanelHeight / state.gSplitHeight) *
          100 +
        '%)'
    };
  }),
  // 上下面板中间trgger拖拽部分偏移量
  verticaltriggerPanel: computed(() => {
    return {
      top: state.top * 100 + '%'
    };
  })
});

// 初始化部分dom元素的尺寸
const initDom = () => {
  state.gSplitWidth = gSplit.value.clientWidth;
  state.gSplitHeight = gSplit.value.clientHeight;
  state.mode == 'horizontal'
    ? (state.horizontalTriggerPanelWidht =
        horizontalTriggerPanel.value.clientWidth)
    : '';
  state.mode == 'vertical'
    ? (state.verticalTriggerPanelHeight =
        verticalTriggerPanel.value.clientHeight)
    : '';
};

// 挂载
const bindEvent = () => {
  // 根据mode来决定绑定那种类型的事件
  state.mode == 'horizontal' ? bindHorizontalTriggerPanelEvent() : null;
  state.mode == 'vertical' ? bindVerticalTriggerPanelEvent() : null;
};
// 禁用页面文字选中函数
const preventSelectedOnMouseMove = (e: any) => {
  e.preventDefault();
};

// 水平拖拽处理函数
const bindHorizontalTriggerPanelEvent = () => {
  resolveMouseFn('horizontal', horizontalTriggerPanel.value);
};

// 处置垂直面板拖拽函数
const bindVerticalTriggerPanelEvent = () => {
  resolveMouseFn('vertical', verticalTriggerPanel.value);
};

// 处理拖拽逻辑，水平和垂直的逻辑合在一起
const resolveMouseFn = (type = 'horizontal', element) => {
  const mousedown = (e) => {
    // 禁止页面文字的选中，避免在拖拽过成功出现文字被选中的行为
    document.addEventListener('selectstart', preventSelectedOnMouseMove);
    // 发布开始拖拽事件
    emit('on-move-start', e);
    // 获取鼠标点击的位置距离元素边缘的距离
    const pos = type == 'horizontal' ? 'left' : 'top';
    const distance =
      type == 'horizontal'
        ? e.clientX - element.offsetLeft
        : e.clientY - element.offsetTop;
    const mousemove = (e) => {
      // 发布开始拖拽中事件
      emit('on-moving', e);
      const gSplitSize =
        type == 'horizontal'
          ? gSplit.value.clientWidth
          : gSplit.value.clientHeight;
      state[pos] =
        (type == 'horizontal' ? e.clientX - distance : e.clientY - distance) /
        gSplitSize;
      // 控制范围
      if (state[pos] < state.min) {
        state[pos] = state.min;
      }
      if (state[pos] > 1 - state.min) {
        state[pos] = 1 - state.min;
      }
      return false;
    };
    const mouseup = () => {
      // 发布开始拖拽结束事件
      emit('on-move-end', e);
      // 释放按下和滑动处理函数以及禁用选中的处理函数
      document.removeEventListener('mousemove', mousemove);
      document.removeEventListener('mouseup', mouseup);
      document.removeEventListener('selectstart', preventSelectedOnMouseMove);
      return false;
    };
    document.addEventListener('mousemove', mousemove);
    document.addEventListener('mouseup', mouseup);
    return false;
  };
  element.addEventListener('mousedown', mousedown);
};

onMounted(() => {
  bindEvent();
  setTimeout(() => {
    initDom();
  }, 0);
});
</script>

<style lang="scss" scoped>
.g-split {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .position {
    position: absolute;
  }

  .horizontal {
    position: relative;
    height: 100%;

    .left-panel {
      height: 100%;
    }

    .right-panel {
      height: 100%;
    }

    .horizontal-trigger-panel {
      cursor: col-resize;
      height: 100%;

      .trigger-content-default-wrap {
        background-color: var(--el-bg-color-overlay);
        height: 100%;
        position: relative;
        width: 7px;

        .trigger-content {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);

          .trigger-bar {
            width: 7px;
            height: 1px;
            display: block;
            background: var(--el-color-info-dark-2);
            margin-top: 3px;
          }
        }
      }
    }
  }

  .vertical {
    position: relative;
    height: 100%;

    .top-panel {
      width: 100%;
    }

    .bottom-panel {
      width: 100%;
    }

    .vertical-trigger-panel {
      width: 100%;

      .trigger-content-default-wrap {
        width: 100%;
        position: relative;
        height: 7px;
        cursor: row-resize;
        background-color: var(--el-bg-color-overlay);

        .trigger-content {
          position: absolute;
          left: 50%;
          top: 0;
          transform: translateX(-50%);
          height: 100%;

          .trigger-bar {
            width: 1px;
            height: 100%;
            display: inline-block;
            background: var(--el-color-info-dark-2);
            margin-left: 3px;
            vertical-align: top;
          }
        }
      }
    }
  }
}

.position {
  overflow: hidden;
}
</style>
