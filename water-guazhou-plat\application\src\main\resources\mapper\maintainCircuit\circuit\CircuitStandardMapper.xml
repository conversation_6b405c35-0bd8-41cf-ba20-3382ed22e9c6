<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitStandardMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitStandard">

        select a.*, b.first_name as creatorName, dt.name as deviceTypeName
        from tb_device_circuit_standard a
        left join tb_user b on a.creator = b.id
        left join m_device_type dt on a.serial_id = dt.serial_id and a.tenant_id = dt.tenant_id
        where (a.method like '%' || #{keywords} || '%'
        or a.remark like '%' || #{keywords} || '%'
        or b.first_name like '%' || #{keywords} || '%'
        or dt.name like '%' || #{keywords} || '%')
        and a.tenant_id = #{tenantId}
        <if test="deviceTypeSerialIds != null and deviceTypeSerialIds.size > 0">
            and a.serial_id in
            <foreach collection="deviceTypeSerialIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        order by a.create_time desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">

        select count(*)
        from tb_device_circuit_standard a
        left join tb_user b on a.creator = b.id
        left join m_device_type dt on a.serial_id = dt.serial_id and a.tenant_id = dt.tenant_id
        where (a.method like '%' || #{keywords} || '%'
        or a.remark like '%' || #{keywords} || '%'
        or b.first_name like '%' || #{keywords} || '%'
        or dt.name like '%' || #{keywords} || '%')
        and a.tenant_id = #{tenantId}
        <if test="deviceTypeSerialIds != null and deviceTypeSerialIds.size > 0">
            and a.serial_id in
            <foreach collection="deviceTypeSerialIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>