package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.app.AppRoleService;
import org.thingsboard.server.dao.model.sql.AppRole;

import java.util.List;

@RestController
@RequestMapping("api/app/role")
public class AppRoleController {

    @Autowired
    private AppRoleService appRoleService;

    @GetMapping("{id}")
    public AppRole findById(@PathVariable String id) {
        return appRoleService.findById(id);
    }

    /**
     * 查询所有
     *
     * @return
     */
    @GetMapping("list/all")
    public List<AppRole> findAll() {
        return appRoleService.findAll();
    }

    /**
     * 按应用分类id查询
     *
     * @param appTypeId
     * @return
     */
    @GetMapping("appTypeId/{appTypeId}")
    public List<AppRole> findByAppTypeId(@PathVariable String appTypeId) {
        return appRoleService.findByAppTypeId(appTypeId);
    }

    @PostMapping
    public AppRole save(@RequestBody AppRole appRole) {
        return appRoleService.save(appRole);
    }

    @PostMapping("edit")
    public AppRole update(@RequestBody AppRole appRole) {
        return appRoleService.update(appRole);
    }

    @DeleteMapping("{id}")
    public AppRole deleteById(@PathVariable String id) {
        return appRoleService.deleteById(id);
    }

}
