import { request } from '@/plugins/axios';

/**
 * 查询养护任务列表
 * @param params
 * @returns
 */
export const GetMaintainTasks = (params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  isAssigned?: string;
  isComplete?: string;
  creator?: string;
  maintainUser?: string;
}) => {
  return request({
    url: '/api/sm/maintainTask',
    method: 'get',
    params
  });
};
/**
 * 添加养护任务
 * @param params
 * @returns
 */
export const AddMaintainTask = (params: {
  name: string;
  device: string;
  deviceName: string;
  maintainUser: string;
  beginTime: string;
  endTime: string;
  remark: string;
  items: { objectId: string }[];
}) => {
  return request({
    url: '/api/sm/maintainTask',
    method: 'post',
    data: params
  });
};

/**
 * 删除养护任务
 * @param id
 * @returns
 */
export const DeleteMaintainTasks = (ids: string[]) => {
  return request({
    url: `/api/sm/maintainTask`,
    method: 'delete',
    data: ids
  });
};
/**
 * 分派养护任务
 * @param id
 * @param params
 * @returns
 */
export const DispatchMaintainTask = (
  id?: string,
  params?: {
    maintainUser: string;
    beginTime: string;
    endTime: string;
    remark: string;
  }
) => {
  return request({
    url: `/api/sm/maintainTask/${id}/assign`,
    method: 'post',
    data: params
  });
};

/**
 * 查询养护任务设备、附件
 * @param params
 * @returns
 */
export const GetMaintainTaskItems = (
  params: IQueryPagerParams & {
    taskId?: string;
    isComplete?: boolean;
  }
) => {
  return request({
    url: '/api/sm/maintainTaskItem',
    method: 'get',
    params
  });
};
