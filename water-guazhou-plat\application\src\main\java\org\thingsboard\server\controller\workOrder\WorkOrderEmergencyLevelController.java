package org.thingsboard.server.controller.workOrder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderEmergencyLevel;
import org.thingsboard.server.dao.orderWork.WorkOrderEmergencyLevelService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

@RestController
@RequestMapping("api/workOrderEmergencyLevel")
public class WorkOrderEmergencyLevelController extends BaseController {

    @Autowired
    private WorkOrderEmergencyLevelService workOrderEmergencyLevelService;

    @GetMapping("list")
    public IstarResponse findList(@RequestParam(required = false) String status) throws ThingsboardException {
        return IstarResponse.ok(workOrderEmergencyLevelService.findList(status, getTenantId()));
    }

    @PostMapping("changeStatus")
    public IstarResponse changeStatus(@RequestBody WorkOrderEmergencyLevel param) {
        workOrderEmergencyLevelService.changeStatus(param);
        return IstarResponse.ok();
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody WorkOrderEmergencyLevel entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        workOrderEmergencyLevelService.save(entity);
        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        workOrderEmergencyLevelService.remove(ids);
        return IstarResponse.ok();
    }



}
