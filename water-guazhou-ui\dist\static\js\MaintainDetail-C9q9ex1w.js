import{d as E,r as C,a8 as F,c as J,o as M,g as m,n as f,p as e,q as w,i as r,bh as I,aB as y,aJ as T,h as k,F as S,G as U,bC as j,dc as L,aq as q,c5 as z,J as G,cU as O,dd as P,C as $}from"./index-r0dFAfgr.js";import{_ as A}from"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";import{b as H}from"./maintenance-zUn_QdHH.js";import{P as K}from"./config-C9CMv0E7.js";import Q from"./DetailTable-Dc-xAY7v.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const X={class:"detail-taskinfo"},Y={class:"detail-response"},Z={class:"detail-response-tabcontent"},tt={class:"left"},et={class:"right"},ot={class:"right-header"},it={class:"right-item"},rt={class:"box img"},st={class:"right-item video"},nt={class:"box video"},at={class:"right-item"},lt={class:"box audio"},pt={class:"right-item attac"},mt={class:"box"},dt=E({__name:"MaintainDetail",props:{row:{},view:{}},emits:["row-click"],setup(V,{emit:N}){const B=N,p=V,o=C({curResponse:"已完成",responses:[],curDeviceType:p.row.deviceName,deviceTabs:[{label:p.row.deviceName,value:p.row.deviceName}],currentRow:void 0,currentDevice:void 0,tableRows:[]}),D=C({dataList:[],columns:[{minWidth:120,label:"任务名称",prop:"name"},{minWidth:120,label:"养护设备类型",prop:"deviceName"},{minWidth:120,label:"开始时间",prop:"beginTime"},{minWidth:120,label:"结束时间",prop:"endTime"},{minWidth:120,label:"创建时间",prop:"createTime"},{minWidth:120,label:"养护人员",prop:"maintainUserName"},{minWidth:120,label:"任务状态",prop:"status",formatter:(a,t)=>{var s;return(s=K[t])==null?void 0:s.text}}],pagination:{hide:!0}}),b=F(()=>{var a,t,s,n,d,c,_,h,v,i,l,x;return{img:((s=(t=(a=o.currentRow)==null?void 0:a.img)==null?void 0:t.split(","))==null?void 0:s.filter(u=>!!u))||[],video:((c=(d=(n=o.currentRow)==null?void 0:n.video)==null?void 0:d.split(","))==null?void 0:c.filter(u=>!!u))||[],audio:((v=(h=(_=o.currentRow)==null?void 0:_.audio)==null?void 0:h.split(","))==null?void 0:v.filter(u=>!!u))||[],file:((x=(l=(i=o.currentRow)==null?void 0:i.file)==null?void 0:l.split(","))==null?void 0:x.filter(u=>!!u))||[]}}),g=()=>{H({taskId:p.row.id,isComplete:o.curResponse!=="未完成",page:1,size:9999}).then(a=>{var n,d,c;const t=((d=(n=a.data)==null?void 0:n.data)==null?void 0:d.data)||[];o.tableRows=t;const s={layername:p.row.deviceName,layerid:p.row.device,oids:t.map(_=>_.objectId)};(c=R.value)==null||c.refreshDetail(p.view,s)})},R=J(),W=async a=>{var t;o.currentRow=o.tableRows.find(s=>{var n;return s.objectId===((n=a.OBJECTID)==null?void 0:n.toString())}),o.currentDevice=a,B("row-click"),await L(500),(t=R.value)==null||t.extentTo(p.view,a.OBJECTID)};return M(()=>{g(),D.dataList=[p.row||{}]}),(a,t)=>{var v;const s=q,n=z,d=O,c=A,_=P,h=G;return m(),f("div",null,[e("div",X,[t[2]||(t[2]=e("div",{class:"detail-group-title"}," 任务详情 ",-1)),w(s,{config:r(D)},null,8,["config"])]),e("div",Y,[t[7]||(t[7]=e("p",{class:"detail-group-title"}," 设备信息 ",-1)),w(n,{modelValue:r(o).curResponse,"onUpdate:modelValue":t[0]||(t[0]=i=>r(o).curResponse=i),config:{type:"tabs",tabs:[{label:"已完成",value:"已完成"},{label:"未完成",value:"未完成"}]},onChange:g},null,8,["modelValue"]),e("div",Z,[e("div",tt,[w(n,{modelValue:r(o).curDeviceType,"onUpdate:modelValue":t[1]||(t[1]=i=>r(o).curDeviceType=i),config:{type:"tabs",tabs:r(o).deviceTabs,tabType:"simple"},onChange:g},null,8,["modelValue","config"]),w(Q,{ref_key:"refDetail",ref:R,onRowClick:W,onRefreshData:g},null,512)]),e("div",et,[e("div",ot,[e("span",null,I((v=r(o).currentDevice)==null?void 0:v.SID)+" - 详情",1)]),e("div",it,[t[3]||(t[3]=e("span",null,"图片",-1)),e("div",rt,[(m(!0),f(y,null,T(r(b).img,(i,l)=>(m(),k(d,{key:l,src:i},null,8,["src"]))),128))])]),e("div",st,[t[4]||(t[4]=e("span",null,"视频",-1)),e("div",nt,[(m(!0),f(y,null,T(r(b).video,(i,l)=>(m(),k(c,{key:l,url:i,style:{height:"180px"}},null,8,["url"]))),128))])]),e("div",at,[t[5]||(t[5]=e("span",null,"音频",-1)),e("div",lt,[(m(!0),f(y,null,T(r(b).audio,(i,l)=>(m(),k(_,{key:l,"show-url":!0,url:i,style:{height:"25px"}},null,8,["url"]))),128))])]),e("div",pt,[t[6]||(t[6]=e("span",null,"附件",-1)),e("div",mt,[(m(!0),f(y,null,T(r(b).file,(i,l)=>(m(),k(h,{key:l,text:!0,size:"small",style:{width:"100%","text-align":"left","justify-content":"flex-start","margin-left":"12px"},onClick:x=>r(j)(i)},{default:S(()=>[U(I(i),1)]),_:2},1032,["onClick"]))),128))])])])])])])}}}),Ve=$(dt,[["__scopeId","data-v-2316ae84"]]);export{Ve as default};
