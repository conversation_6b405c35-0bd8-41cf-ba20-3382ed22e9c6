import{_ as f}from"./CardTable-rdWOL4_6.js";import{d as u,r as s,a8 as _,o as b,g as x,n as g,q as e,F as y,i as n,aB as C,bz as N,C as j}from"./index-r0dFAfgr.js";import{d as T}from"./index-CCFuhOrs.js";import{c as h}from"./manage-BReaEVJk.js";import"./index-C9hz-UZb.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const z=u({__name:"xmzjsjcxx",props:{config:{}},setup(r){const t=r,c=s({defaultValue:_(()=>t.config),border:!0,direction:"horizontal",column:2,title:"项目总结算基础信息",fields:[{type:"text",label:"总结算时间:",field:"acceptTimeName"},{type:"text",label:"总结算金额:",field:"cost"},{type:"text",label:"合同总金额:",field:"contractTotalCost"},{type:"text",label:"费用总金额:",field:"expenseTotalCost"},{type:"text",label:"总结算说明:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]}),a=s({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"工程名称",prop:"name"},{label:"工程地址",prop:"address"},{label:"工程类别",prop:"typeName"},{label:"工程预算(万元)",prop:"estimate"},{label:"申请单位",prop:"fitstpartName"}],dataList:[],pagination:{hide:!0}}),i=async()=>{const o={page:-1,size:20,projectCode:t.config.projectCode};h(o).then(l=>{a.dataList=l.data.data.data||[]})};return b(()=>{i()}),(o,l)=>{const p=T,d=N,m=f;return x(),g(C,null,[e(d,{class:"card"},{default:y(()=>[e(p,{config:n(c)},null,8,["config"])]),_:1}),e(m,{title:"项目工程",config:n(a),class:"card-table"},null,8,["config"])],64)}}}),w=j(z,[["__scopeId","data-v-7558c585"]]);export{w as default};
