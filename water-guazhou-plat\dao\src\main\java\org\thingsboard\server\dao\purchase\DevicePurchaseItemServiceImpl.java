package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.deviceType.DevicePurchaseInquiryService;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseItem;
import org.thingsboard.server.dao.sql.purchase.DevicePurchaseItemMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.device.DevicePurchaseInquirySaveRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseItemSaveRequest;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DevicePurchaseItemServiceImpl implements DevicePurchaseItemService {
    @Autowired
    private DevicePurchaseItemMapper mapper;

    @Autowired
    private DevicePurchaseInquiryService service;


    @Override
    public IPage<DevicePurchaseItem> findAllConditional(DevicePurchaseItemPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public boolean update(DevicePurchaseItem entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        service.deleteByPurchaseItemId(id);
        return mapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public List<DevicePurchaseItem> saveAll(List<DevicePurchaseItemSaveRequest> entities) {
        @SuppressWarnings("Convert2MethodRef")
        Set<String> updateModeIdSet = entities.stream().map(x -> x.getId()).filter(x -> x != null).collect(Collectors.toSet());
        List<DevicePurchaseItem> savedItems = QueryUtil.saveOrUpdateBatchByRequest(entities, mapper::saveAll, mapper::saveAll);
        service.saveAll(savedItems.stream().filter(x -> !updateModeIdSet.contains(x.getId())).map(x -> {
            DevicePurchaseInquirySaveRequest item = new DevicePurchaseInquirySaveRequest();
            item.setPurchaseDetailId(x.getId());
            item.tenantId(x.getTenantId());
            return item;
        }).collect(Collectors.toList()));
        return savedItems;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        if (idList == null)
            return true;
        service.deleteBatchByPurchaseItemId(idList);
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

    @Override
    @Transactional
    public boolean deleteByMainIdOnIdNotIn(String mainId, List<String> idList) {
        service.deleteByPurchaseItemMainIdOnMainIdNotIn(mainId, idList);
        return mapper.deleteByMainIdOnIdNotIn(mainId, idList) > 0;
    }

    @Override
    @Transactional
    public boolean removeAllByMainOnIdNotIn(String mainId, List<String> idList) {
        return deleteByMainIdOnIdNotIn(mainId, idList);
    }

    @Override
    public boolean submitInquiry(String id) {
        return mapper.submitInquiry(id);
    }

}
