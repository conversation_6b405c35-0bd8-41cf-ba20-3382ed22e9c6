package org.thingsboard.server.common.data.dataSource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.common.data.VO.DefaultMapObject;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/27 13:44
 * 数据源获取数据的默认返回格式 即time-value
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataFromDataSource {
    /**
     * 数据源ID
     */
    private String dataSourceId;
    /**
     * 数据源名称
     */
    private String dataSourceName;
    /**
     * 数据集<key,value>格式，key为时间，value为对应时间点的值
     */
    private List<DefaultMapObject> data;
}
