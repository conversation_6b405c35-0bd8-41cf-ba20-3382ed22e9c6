<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalSitePackageMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        name,
        is_notice,
        at_bottom,
        active,
        display_mode,
        jump_to_url,
        link,
        create_time,
        parent_id,
        (select name from ss_portal_site_package where id = package.parent_id) parent_name,
        order_num,
        tenant_id
        <!--@sql from ss_portal_site_package package -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSitePackage">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="is_notice" property="isNotice"/>
        <result column="at_bottom" property="atBottom"/>
        <result column="active" property="active"/>
        <result column="display_mode" property="displayMode"/>
        <result column="jump_to_url" property="jumpToUrl"/>
        <result column="link" property="link"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_name" property="parentName"/>
        <result column="create_time" property="createTime"/>
        <result column="order_num" property="orderNum"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_site_package package
        <where>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="updateFully">
        update ss_portal_site_package
        set name         = #{name},
            is_notice    = #{isNotice},
            at_bottom    = #{atBottom},
            active       = #{active},
            display_mode = #{displayMode},
            jump_to_url  = #{jumpToUrl},
            link         = #{link},
            order_num    = #{orderNum}
        where id = #{id}
    </update>

    <select id="findRoots" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_site_package package
        <where>
            <if test="folderOnly != null and folderOnly">
                jump_to_url::boolean = false
            </if>
            <if test="isNotice != null">
                and is_notice = #{isNotice}
            </if>
            <if test="jumpToUrl != null">
                and jump_to_url = #{jumpToUrl}
            </if>
            <if test="active != null">
                and (active = #{active} or (parent_id is null and at_bottom::boolean = #{active}))
            </if>
            and parent_id is null
        </where>
        order by order_num
    </select>

    <select id="findChildren" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_site_package package
        where parent_id = #{id}
          and tenant_id = #{tenantId}
        <if test="jumpToUrl != null">
            and jump_to_url = #{jumpToUrl}
        </if>
        order by order_num
    </select>

    <select id="canBeDelete" resultType="boolean">
        select count(1) = 0
        from ss_portal_site_package
        where parent_id = #{id}
    </select>
    
    <select id="canSave" resultType="boolean">
        select parent_id is null
        from ss_portal_site_package
        where id = #{parentId}
    </select>
</mapper>