package org.thingsboard.server.dao.report;

import org.thingsboard.server.dao.model.sql.FlowRateReport;

import java.util.Date;
import java.util.List;

public interface FlowRateReportService {

    Object getData(Date startTime, Date endTime, String stationId);

    Object supplyByProject(Date start, Date end, String projectId, String type);

    Object supplyByStation(Date startTime, Date endTime, String stationId);

    Object findEnergyInByProject(Date startTime, Date endTime, String projectId, String type);

    Object findRuntimeList(String projectId, Date startTime, Date endTime);

    Object findRuntimeListByStation(String stationId, Date startTime, Date endTime);

    Object findRuntimeReportByStationList(List<String> stationIdList, Date startTime, Date endTime);

    void save(FlowRateReport entity);

    Object findRuntimeReportByStation(String stationId, Date startTime, Date endTime);
}
