package org.thingsboard.server.controller.gis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisFileService;
import org.thingsboard.server.dao.model.request.GisFileListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisFile;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * gis文件
 */
@RestController
@RequestMapping("api/gis/file")
public class GisFileController extends BaseController {

    @Autowired
    private GisFileService gisFileService;

    @PostMapping("save")
    public IstarResponse save(@RequestBody GisFile entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        entity.setUploadUser(getCurrentUser().getFirstName());
        gisFileService.save(entity);

        return IstarResponse.ok();
    }

    @PostMapping("batch/save")
    public IstarResponse batchSave(@RequestBody List<GisFile> entityList) throws ThingsboardException {
        for (GisFile entity : entityList) {
            entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            entity.setUploadUser(getCurrentUser().getFirstName());
            gisFileService.save(entity);
        }

        return IstarResponse.ok();
    }

    @GetMapping("list")
    public IstarResponse findList(GisFileListRequest request) throws ThingsboardException {

        return IstarResponse.ok(gisFileService.findList(request, getTenantId()));
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        gisFileService.remove(ids);
        return IstarResponse.ok();
    }

    @PostMapping("changeStatus/{status}")
    public IstarResponse changeStatus(@PathVariable String status, @RequestBody List<String> ids) {
        gisFileService.changeStatus(ids, status);
        return IstarResponse.ok();
    }

}
