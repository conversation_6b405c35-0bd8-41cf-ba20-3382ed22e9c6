import{_ as L}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as v}from"./CardTable-rdWOL4_6.js";import{_ as I}from"./CardSearch-CB_HNR-Q.js";import{d as S,M as C,c as u,s as M,r as d,x as s,a8 as _,S as U,a9 as h,o as P,g as T,n as V,q as g,i as f,b7 as N}from"./index-r0dFAfgr.js";import{I as c}from"./common-CvK_P_ao.js";import{c as q,e as R,b as w,g as B,f as F}from"./emergencyDispatch-BKfr3jS6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const W={class:"wrapper"},H=S({__name:"index",setup(z){const{$btnPerms:b}=C(),o=u(),p=u(),y=u({filters:[{label:"车辆号码",field:"numberPlate",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:c.QUERY,click:()=>l()},{type:"default",perm:!0,text:"重置",svgIcon:M(N),click:()=>{var e;(e=p.value)==null||e.resetForm(),l()}},{perm:!0,text:"新建",icon:c.ADD,type:"success",click:()=>D()}]}]}),n=d({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"车牌号",prop:"numberPlate"},{label:"车辆品牌",prop:"carBrand"},{label:"使用年限",prop:"usePeriod"},{label:"所属组织",prop:"carUserOrganizationName"},{label:"所属部门",prop:"carUserDepartmentName"},{label:"车辆负责人",prop:"carUserName"},{label:"状态",prop:"status"},{label:"SIM卡号",prop:"simNum"},{label:"服务器时间",prop:"serverTime"},{label:"定位器时间",prop:"gprsTime"},{label:"当前速度",prop:"nowSpeed",unit:"(km/h)",minWidth:"120"},{label:"方向",prop:"direction"},{label:"当日里程",prop:"mileageDay",unit:"(km)"},{label:"总里程",prop:"mileageTotal",unit:"(km)"}],operationWidth:"120px",operations:[{type:"primary",text:"编辑",icon:c.EDIT,perm:b("RoleManageEdit"),click:e=>k(e)},{type:"danger",text:"删除",perm:b("RoleManageDelete"),icon:c.DELETE,click:e=>x(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,l()}}}),r=d({title:"新增",labelWidth:"100px",dialogWidth:"500px",submitting:!1,submit:e=>{r.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),q(e).then(()=>{var a;r.submitting=!1,(a=o.value)==null||a.closeDialog(),s.success(t),l()}).catch(a=>{r.submitting=!1,s.warning(a)})},defaultValue:{},group:[{fields:[{type:"input",label:"车牌号",field:"numberPlate",rules:[{required:!0,message:"请输入车牌号"}]},{type:"input",label:"车辆品牌",field:"carBrand",rules:[{required:!0,message:"请输入车辆品牌"}]},{type:"number",label:"使用年限",field:"usePeriod"},{type:"select-tree",label:"负责人部门",field:"carUserDepartmentId",checkStrictly:!0,options:_(()=>i.department),onChange:e=>{i.getuserList(e)}},{type:"select",label:"车辆负责人",field:"carUserId",options:_(()=>i.userList)},{type:"input-number",label:"SIM卡号",field:"simNum"}]}]}),D=()=>{var e;r.title="新增",r.defaultValue={},(e=o.value)==null||e.openDialog()},k=e=>{var t;r.title="编辑",r.defaultValue={category:e.parentId,...e||{}},i.getuserList(e.carUserDepartmentId),(t=o.value)==null||t.openDialog()},x=e=>{U("确定删除该应急车辆","删除提示").then(()=>{R(e.id).then(()=>{s.success("删除成功"),l()}).catch(t=>{s.error(t)})})},i=d({department:[],userList:[],getdepartment:()=>{w(2).then(e=>{i.department=h(e.data.data||[])})},getuserList:e=>{var a;const t={size:-1,page:1,deptId:e,...((a=p.value)==null?void 0:a.queryParams)||{}};B(t).then(m=>{i.userList=h(m.data.data.data||[])})}}),l=async()=>{var t;const e={size:n.pagination.limit,page:n.pagination.page,...((t=p.value)==null?void 0:t.queryParams)||{}};F(e).then(a=>{n.dataList=a.data.data.data||[],n.pagination.total=a.data.data.total||0}).catch(a=>{s.warning("请求遇到错误")})};return P(async()=>{l(),i.getdepartment()}),(e,t)=>{const a=I,m=v,E=L;return T(),V("div",W,[g(a,{ref_key:"refSearch",ref:p,config:f(y)},null,8,["config"]),g(m,{class:"card-table",config:f(n)},null,8,["config"]),g(E,{ref_key:"refForm",ref:o,config:f(r)},null,8,["config"])])}}});export{H as default};
