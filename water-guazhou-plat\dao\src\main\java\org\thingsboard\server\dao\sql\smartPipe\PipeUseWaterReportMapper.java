package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.PipeUseWaterReportDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeUseWaterReport;

/**
 * 用水填报
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Mapper
public interface PipeUseWaterReportMapper extends BaseMapper<PipeUseWaterReport> {

    PipeUseWaterReportDTO getWaterBalance(@Param("partitionId") String partitionId, @Param("ym") String ym);

    PipeUseWaterReportDTO getSystemData(@Param("partitionId") String partitionId, @Param("ym") String ym);

    PipeUseWaterReportDTO getWaterBalanceMonth(@Param("partitionId") String partitionId, @Param("ym") String ym);
}
