import{_ as q}from"./TreeBox-DDD2iwoR.js";import{_ as R}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as H}from"./index-C9hz-UZb.js";import{d as V,c as W,r as f,l as $,bI as G,S as A,b as T,g as O,h as z,F as k,q as u,i as d,p as X,_ as E,C as J}from"./index-r0dFAfgr.js";import{_ as K}from"./CardSearch-CB_HNR-Q.js";import{_ as Q}from"./index-BJ-QPYom.js";import{D as Y,G as Z,a as M}from"./waterBalance-BluOprpN.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as ee}from"./usePartition-DkcY9fQ2.js";import{_ as te}from"./WaterBalanceDetail.vue_vue_type_script_setup_true_lang-BGGtjsTT.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./index-0NlGN6gS.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const ae={class:"form-wrapper overlay-y"},re=V({__name:"index",setup(ie){const P=W(),r=W(),x=W(),c=W(),L=W(),s=f({curFilling:"",sumField:""}),v=ee(),n=f({data:[],title:"选择分区",isFilterTree:!0,expandOnClickNode:!1,treeNodeHandleClick:t=>{n.currentProject=t,_()},autoFillOptions:()=>{v.getTree().then(()=>{n.data=v.Tree.value||[],n.currentProject=n.data[0],_()})}}),S=f({defaultParams:{type:"month",month:$().format(G)},filters:[{type:"select",field:"type",clearable:!1,width:"100px",options:[{label:"按月",value:"month"}],label:"选择方式",extraFormItem:[{width:"160px",type:"month",field:"month",disabledDate(t){return t>new Date}}]},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{_()},iconifyIcon:"ep:search"},{perm:!0,text:"填写帮助",type:"default",plain:!0,click:()=>{var t;(t=L.value)==null||t.openDialog()},iconifyIcon:"ep:info-filled"},{perm:!0,text:"保存",type:"primary",click:()=>{var t;(t=r.value)==null||t.Submit()},iconifyIcon:"ep:circle-check"}]}]}),D=f({labelPosition:"right",labelWidth:200,group:[{fieldset:{desc:"基础信息",icon:"material-symbols:sunny-outline",type:"underline"},fields:[{md:12,xl:8,type:"input",label:"填报单位",field:"uploadName"},{md:12,xl:8,type:"input",label:"填表年份",field:"ym",disabled:!0}]},{handleHidden(t,e,l){var o,a;l.hidden=((a=(o=n.currentProject)==null?void 0:o.path)==null?void 0:a.length)!==1},fieldset:{desc:"供水总量",icon:"material-symbols:sunny-outline",type:"underline"},fields:[{md:12,xl:8,type:"input-number",label:"自产供水量",field:"ownSupplyWater",suffix:"m³",onChange:()=>i("supplyTotalWater")},{md:12,xl:8,type:"input-number",label:"外购供水量",field:"buySupplyWater",suffix:"m³",onChange:()=>i("supplyTotalWater")},{md:12,xl:8,type:"input-number",label:"趸售水量",field:"batchSaleWater",suffix:"m³",onChange:()=>i("supplyTotalWater")},{md:12,xl:8,type:"input-number",label:"供水总量",field:"supplyTotalWater",suffix:"m³",readonly:!0}]},{handleHidden(t,e,l){var o,a;l.hidden=((a=(o=n.currentProject)==null?void 0:o.path)==null?void 0:a.length)!==2},fieldset:{desc:"供水总量",icon:"material-symbols:sunny-outline",type:"underline"},fields:[{md:12,xl:8,type:"input-number",label:"进水量",field:"ownSupplyWater",suffix:"m³",onChange:()=>i("supplyTotalWater")},{md:12,xl:8,type:"input-number",label:"出水量",field:"batchSaleWater",suffix:"m³",onChange:()=>i("supplyTotalWater")},{md:12,xl:8,type:"input-number",label:"供水总量",field:"supplyTotalWater",suffix:"m³",readonly:!0}]},{fieldset:{desc:"注册用户用水量",icon:"material-symbols:sunny-outline",type:"underline"},fields:[{md:12,xl:8,type:"input-number",label:"计费计量用水量",field:"feeMeteringUseWater",suffix:"m³",onChange:()=>i("useTotalWater")},{md:12,xl:8,type:"input-number",label:"计费未计量用水量",field:"feeNoMeteringUseWater",suffix:"m³",readonly:!0,appendBtns:[{styles:{width:"55px"},perm:!0,text:"填写",type:"primary",click:()=>m("计费未计量用水量","feeNoMeteringUseWater","useTotalWater")}]},{md:12,xl:8,type:"input-number",label:"免费计量用水量",field:"freeMeteringUseWater",suffix:"m³",readonly:!0,appendBtns:[{styles:{width:"55px"},perm:!0,text:"填写",type:"primary",click:()=>m("免费计量用水量","freeMeteringUseWater","useTotalWater")}]},{md:12,xl:8,type:"input-number",label:"免费未计量用水量",field:"freeNoMeteringUseWater",suffix:"m³",readonly:!0,appendBtns:[{styles:{width:"55px"},perm:!0,text:"填写",type:"primary",click:()=>m("免费未计量用水量","freeNoMeteringUseWater","useTotalWater")}]},{md:12,xl:8,type:"input-number",label:"注册用户用水量",field:"useTotalWater",suffix:"m³",readonly:!0}]},{fieldset:{desc:"漏失水量",icon:"material-symbols:sunny-outline",type:"underline"},fields:[{md:12,xl:8,type:"input-number",label:"明漏水量",field:"frontLeakTotalWater",suffix:"m³",readonly:!0,appendBtns:[{styles:{width:"55px"},perm:!0,text:"填写",type:"primary",click:()=>m("明漏水量","frontLeakTotalWater","leakTotalWater")}]},{md:12,xl:8,type:"input-number",label:"暗漏水量",field:"backendLeakTotalWater",suffix:"m³",readonly:!0,appendBtns:[{styles:{width:"55px"},perm:!0,text:"填写",type:"primary",click:()=>m("暗漏水量","backendLeakTotalWater","leakTotalWater")}]},{md:12,xl:8,type:"input-number",label:"背景漏失水量",field:"backgroundLeakWater",suffix:"m³",onChange:()=>i("leakTotalWater")},{md:12,xl:8,type:"input-number",label:"水箱、水池的售楼和溢流水量",field:"shuixiangLeakWater",suffix:"m³",onChange:()=>i("leakTotalWater")},{md:12,xl:8,type:"input-number",label:"漏失水量",field:"leakTotalWater",suffix:"m³",readonly:!0}]},{fields:[{md:12,xl:8,type:"input-number",label:"居民用户表具误差损失水量",field:"custMistakeLossWater",suffix:"m³",onChange:()=>i("mistakeLossTotalWater")},{md:12,xl:8,type:"input-number",label:"非居民用户表具误差损失水量",field:"nonCustMistakeLossWater",suffix:"m³",onChange:()=>i("mistakeLossTotalWater")},{md:12,xl:8,type:"input-number",label:"计量损失水量",field:"mistakeLossTotalWater",readonly:!0,suffix:"m³"}]},{fields:[{md:12,xl:8,type:"input-number",label:"未注册用户用水和用户拒查等",field:"noRegisterLossWater",suffix:"m³",readonly:!0,appendBtns:[{styles:{width:"55px"},perm:!0,text:"填写",type:"primary",click:()=>m("未注册用户用水和用户拒查等","noRegisterLossWater","otherLossTotalWater")}]},{md:12,xl:8,type:"input-number",label:"管理因素导致的损失水量",field:"pipeLossWater",suffix:"m³",onChange:()=>i("otherLossTotalWater")},{md:12,xl:8,type:"input-number",label:"其他漏失水量",field:"otherLossTotalWater",readonly:!0,suffix:"m³"}]},{fields:[{md:12,xl:12,type:"input-number",label:"漏损水量",field:"lossTotalWater",readonly:!0,suffix:"m³"}]},{fieldset:{desc:"系统数据",icon:"material-symbols:sunny-outline",type:"underline"},fields:[{md:12,xl:8,type:"input-number",label:"DN75（含）以上管道长度",field:"dn75PipeLength",suffix:"km"},{md:12,xl:8,type:"input-number",label:"单位供水量管长",field:"unitSupplyPipeLength",suffix:"km/m³"},{md:12,xl:8,type:"input-number",label:"年平均出厂压力",field:"yearAvgPressure",suffix:"Mpa"},{md:12,xl:8,type:"input-number",label:"居民抄表到户水量",field:"custCopiedWater",suffix:"m³"},{md:12,xl:8,type:"input-number",label:"最大冻土深度",field:"maxFrozenSoilDepth",suffix:"m"}]}],submit(t){A("确定提交？","提示信息").then(async()=>{try{const e=await Y(t);e.data.code===200?T.success("提交成功"):T.error(e.data.message)}catch{T.error("操作失败")}}).catch(()=>{})}}),m=(t,e,l)=>{var o,a;b.title=t,s.curFilling=e,s.sumField=l,b.defaultValue={...((o=r.value)==null?void 0:o.dataForm)||{}},(a=x.value)==null||a.openDialog()},p=(t,e,l)=>{l.hidden=s.curFilling!==l.id},b=f({dialogWidth:600,labelPosition:"right",labelWidth:180,title:"",group:[{handleHidden:p,id:"feeNoMeteringUseWater",fields:[{type:"input-number",suffix:"m³",field:"huanweiUseWater",label:"环卫用水"},{type:"input-number",suffix:"m³",field:"lvhuaUseWater",label:"绿化用水"},{type:"input-number",suffix:"m³",field:"pipeUseWater",label:"新建管道冲洗"},{type:"input-number",suffix:"m³",field:"sunhuaiUseWater",label:"第三方资产损坏"},{type:"input-number",suffix:"m³",field:"dingliangUseWater",label:"定量户用水"}]},{handleHidden:p,id:"freeMeteringUseWater",fields:[{type:"input-number",suffix:"m³",field:"bangongUseWater",label:"自来水企业生产/办公用水"},{type:"input-number",suffix:"m³",field:"xiaofangUseWater",label:"消防用水"},{type:"input-number",suffix:"m³",field:"jianmianUseWater",label:"政策性减免"}]},{handleHidden:p,id:"freeNoMeteringUseWater",fields:[{type:"input-number",suffix:"m³",field:"freeBangongUseWater",label:"自来水企业生产/办公用水"},{type:"input-number",suffix:"m³",field:"freeXiaofangUseWater",label:"消防用水"},{type:"input-number",suffix:"m³",field:"freeWeihuUseWater",label:"管网维护和冲洗水量"},{type:"input-number",suffix:"m³",field:"freeChongxiUseWater",label:"二次供水泵房冲洗"}]},{id:"frontLeakTotalWater",handleHidden:p,fields:[{type:"input-number",suffix:"m³",field:"frontPointLeakWater",label:"明漏点漏失水量"},{type:"input-number",suffix:"m³",field:"frontPipeLeakWater",label:"爆管漏失水量"}]},{id:"backendLeakTotalWater",handleHidden:p,fields:[{type:"input-number",suffix:"m³",field:"backendCheckedLeakWater",label:"已检出暗漏点水量"},{type:"input-number",suffix:"m³",field:"backendNoCheckedWater",label:"未检出暗漏点水量"}]},{id:"noRegisterLossWater",handleHidden:p,fields:[{type:"input-number",suffix:"m³",field:"toudaoLossWater",label:"偷盗用水量"},{type:"input-number",suffix:"m³",field:"copyMeterLossWater",label:"抄表误差水量"},{type:"input-number",suffix:"m³",field:"otherLossWater",label:"其他损失水量"}]}],submit(t){var l,o;if(!r.value||!s.curFilling||!s.sumField)return;let e=0;(l=b.group.find(a=>a.id===s.curFilling))==null||l.fields.map(a=>{r.value&&a.field&&(r.value.dataForm[a.field]=t[a.field],e+=Number(t[a.field]||0))}),r.value.dataForm[s.curFilling]=e,i(s.sumField),(o=x.value)==null||o.closeDialog()}}),i=t=>{if(!r.value)return;const e=r.value.dataForm||{};switch(t){case"supplyTotalWater":r.value.dataForm.supplyTotalWater=Number(e.ownSupplyWater||0)+Number(e.buySupplyWater||0)-Number(e.batchSaleWater||0);break;case"useTotalWater":r.value.dataForm.useTotalWater=Number(e.feeMeteringUseWater||0)+Number(e.feeNoMeteringUseWater||0)+Number(e.freeMeteringUseWater||0)+Number(e.freeNoMeteringUseWater||0);break;case"leakTotalWater":r.value.dataForm.leakTotalWater=Number(e.frontLeakTotalWater||0)+Number(e.backendLeakTotalWater||0)+Number(e.backgroundLeakWater||0)+Number(e.shuixiangLeakWater||0),i("lossTotalWater");break;case"mistakeLossTotalWater":r.value.dataForm.mistakeLossTotalWater=Number(e.custMistakeLossWater||0)+Number(e.nonCustMistakeLossWater||0),i("lossTotalWater");break;case"otherLossTotalWater":r.value.dataForm.otherLossTotalWater=Number(e.noRegisterLossWater||0)+Number(e.pipeLossWater||0),i("lossTotalWater");break;case"lossTotalWater":r.value.dataForm.lossTotalWater=Number(e.leakTotalWater||0)+Number(e.mistakeLossTotalWater||0)+Number(e.otherLossTotalWater||0);break}},B=f({title:"填写帮助",group:[],dialogWidth:"80%"}),_=async()=>{var e,l,o,a,g,y,h,F,N,C,U,w;if(!n.currentProject)return;const t=await Z({partitionId:n.currentProject.value,ym:(l=(e=c.value)==null?void 0:e.queryParams)==null?void 0:l.month});if(r.value){if(r.value.dataForm={...t.data.data||{partitionId:(o=n.currentProject)==null?void 0:o.value,ym:(g=(a=c.value)==null?void 0:a.queryParams)==null?void 0:g.month}},!t.data.data){const I=await M({partitionId:n.currentProject.value,month:(h=(y=c.value)==null?void 0:y.queryParams)==null?void 0:h.month,direction:"in"}),j=await M({partitionId:n.currentProject.value,month:(N=(F=c.value)==null?void 0:F.queryParams)==null?void 0:N.month,direction:"out"});r.value.dataForm.ownSupplyWater=((C=I.data)==null?void 0:C.data)??0,r.value.dataForm.batchSaleWater=((U=j.data)==null?void 0:U.data)??0,i("supplyTotalWater")}(w=x.value)!=null&&w.refForm&&(b.defaultValue={...t.data.data||{}})}};return(t,e)=>{const l=Q,o=K,a=E,g=H,y=R,h=q;return O(),z(h,null,{tree:k(()=>[u(l,{"tree-data":d(n)},null,8,["tree-data"])]),default:k(()=>[u(o,{ref_key:"refSearch",ref:c,config:d(S)},null,8,["config"]),u(g,{class:"card-table","content-style":{padding:"12px"}},{default:k(()=>[X("div",ae,[u(a,{ref_key:"refForm",ref:r,config:d(D)},null,8,["config"])])]),_:1}),u(y,{ref_key:"refDialog",ref:x,config:d(b)},null,8,["config"]),u(y,{ref_key:"refDetail",ref:L,config:d(B)},{default:k(()=>[u(te,{ref_key:"refDetailTable",ref:P,partition:d(n).currentProject},null,8,["partition"])]),_:1},8,["config"])]),_:1})}}}),Ut=J(re,[["__scopeId","data-v-fbddd5b0"]]);export{Ut as default};
