import{d as b,g as y,h as B,F as i,p as t,G as o,bh as s,q as p,J as N,bz as D,C as V}from"./index-r0dFAfgr.js";const $={class:"flex",style:{padding:"14px"}},E={class:"mag_left_20 text"},T={class:"flex jucon_around"},j=b({__name:"itemCard",props:{config:{}},emits:["openDetail"],setup(x,{emit:C}){const l=x,d=C;return(q,n)=>{const e=N,k=D;return y(),B(k,{"body-style":{padding:"0px"}},{default:i(()=>{var u,f,r,a,g,m;return[t("div",$,[t("div",E,[t("div",null,[n[3]||(n[3]=t("span",null,"姓名",-1)),n[4]||(n[4]=o(": ")),t("span",null,s(((u=l.config)==null?void 0:u.name)??""),1)]),t("div",null,[n[5]||(n[5]=t("span",null,"性别",-1)),n[6]||(n[6]=o(": ")),t("span",null,s(((f=l.config)==null?void 0:f.genderv)??""),1)]),t("div",null,[n[7]||(n[7]=t("span",null,"电话",-1)),n[8]||(n[8]=o(": ")),t("span",null,s(((r=l.config)==null?void 0:r.phone)??""),1)]),t("div",null,[n[9]||(n[9]=t("span",null,"部门",-1)),n[10]||(n[10]=o(": ")),t("span",null,s(((a=l.config)==null?void 0:a.department)??""),1)]),t("div",null,[n[11]||(n[11]=t("span",null,"职称",-1)),n[12]||(n[12]=o(": ")),t("span",null,s(((g=l.config)==null?void 0:g.professionalTitle)??""),1)]),t("div",null,[n[13]||(n[13]=t("span",null,"工作单位",-1)),n[14]||(n[14]=o(": ")),t("span",null,s(((m=l.config)==null?void 0:m.deptName)??""),1)])])]),t("div",T,[p(e,{text:"",onClick:n[0]||(n[0]=v=>d("edit",0,l.config))},{default:i(()=>n[15]||(n[15]=[o(" 编辑 ")])),_:1}),p(e,{text:"",onClick:n[1]||(n[1]=v=>d("openDetail",1,l.config))},{default:i(()=>n[16]||(n[16]=[o(" 详情 ")])),_:1}),p(e,{text:"",onClick:n[2]||(n[2]=v=>d("del",2,l.config))},{default:i(()=>n[17]||(n[17]=[o(" 删除 ")])),_:1})])]}),_:1})}}}),z=V(j,[["__scopeId","data-v-ae39d65d"]]);export{z as default};
