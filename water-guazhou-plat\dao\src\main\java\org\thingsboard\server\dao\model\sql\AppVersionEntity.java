package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.APP_VERSION_TABLE)
public class AppVersionEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.APP_VERSION_CODE)
    private String versionCode;

    @Column(name = ModelConstants.APP_VERSION_NAME)
    private String versionName;

    @Column(name = ModelConstants.APP_VERSION_APP_KEY)
    private String appKey;

    @Column(name = ModelConstants.APP_VERSION_TENANT_KEY)
    private String tenantKey;

    @Column(name = ModelConstants.APP_VERSION_APP_NAME)
    private String appName;

    @Column(name = ModelConstants.APP_VERSION_VERSION_CONTENT)
    private String versionContent;

    @Column(name = ModelConstants.APP_VERSION_URL)
    private String url;

    @Column(name = ModelConstants.APP_VERSION_REMARK)
    private String remark;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

}
