package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecordItem;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxMessageRecordItemSaveRequest extends SaveRequest<WxMessageRecordItem> {

    @Override
    protected WxMessageRecordItem build() {
        WxMessageRecordItem entity = new WxMessageRecordItem();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxMessageRecordItem update(String id) {
        WxMessageRecordItem entity = new WxMessageRecordItem();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxMessageRecordItem entity) {
    }
}