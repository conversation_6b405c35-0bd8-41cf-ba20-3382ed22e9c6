import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as b}from"./DPlayer-Be2AurQX.js";import{C as k,M as N,ag as F,g as d,n as u,p as r,q as g,F as C,aw as h,an as P,bh as S,aB as D,aJ as I,h as x,bb as j}from"./index-r0dFAfgr.js";import{g as q,f as z,c as m,M as f,a as p,b as T}from"./index-D9ERhRP6.js";import{g as B,a as R}from"./data-CfQCw447.js";import"./DPlayer.min-_HMH7IVX.js";const{$message:n}=N(),M={name:"RealTimeVideo",components:{DPlayer:b},data(){return{videoSources:[],treeData:[],showVideo:[],dataSourceData:[],currentProject:{},currentSize:1,currentSelect:0,videoInfoMap:{},panelOpen:!0,fullScreen:!1,hiddenText:"隐藏",defaultProps:{label:"name"},command:"",formConfig:{dialogWidth:600,title:"添加摄像头",defaultValue:{videoType:"1"},group:[{fields:[{type:"datetime",label:"开始时间",field:"start",rules:[{required:!0,message:"请选择开始时间"}]},{type:"datetime",label:"结束时间",field:"end",rules:[{required:!0,message:"请填写序列号"}]}]}],submit:async e=>{var t;try{const i=(t=(await q({...e,id:this.currentNumber})).data)==null?void 0:t.data;i?(data.videoUrl={m3u8uri:i,live:!1},this.videoInfoMap[data.serialNumber]=data,this.showVideo[currentSelect]=data,this.selcetVideoBox(this.currentSelect+1),this.$refs.refForm.closeDialog()):n("获取播放地址失败")}catch(o){SLMessage.error("获取播放地址失败"),console.dir(o)}FormConfig.submitting=!1}}}},created(){for(let e=0;e<this.currentSize;e++)this.showVideo.push({id:e,projectId:"",msg:"无信号"});F().then(e=>{if(e.data){this.treeData=e.data;const t=this.treeData.filter(s=>!s.disabled),o=this.$route.query.projectId,i=o?B(t,o):R(t[0]);this.$nextTick(()=>{this.$refs.dcTree?(this.$refs.dcTree.setChecked(i.id),this.$refs.dcTree.setCurrentKey(i.id),this.getProjectData(i)):this.getProjectData(t[0])}),this.currentProject=i,this.currentProId=i.id}else n("暂无项目 不可操作，请创建项目")}).catch(e=>{console.log(e),n("暂无项目 不可操作，请创建项目")})},methods:{getProjectData(e){e.serialNumber?(this.currentNumber=e.id,this.$refs.refForm.openDialog()):(this.currentProject=e,z(this.currentProject.id).then(t=>{if(t.data.length===0){n("该项目下无视频");return}const o=t.data.map(i=>{const s=this.videoSources.find(a=>a.deviceid===i.serialNumber);return i.status=s&&s.status,i.online=s&&s.online,i});e.children?e.children=e.children.filter(i=>i.nodeType==="Project").concat(o):this.$set(e,"children",o)}))},getVideo(e,t){this.videoInfoMap[e.serialNumber]&&(this.showVideo[t]=this.videoInfoMap[e.serialNumber],this.selcetVideoBox(t+1))},changeShowSize(e){this.currentSize=e,this.showVideo=[],this.currentSelect=0;for(let t=0;t<this.currentSize;t++)this.showVideo.push({id:t,projectId:"",msg:"无信号"})},selcetVideoBox(e){this.currentSelect=e},closeVideo(e,t){this.showVideo[e]={id:e,projectId:"",msg:t==="获取中"?"视频获取中...":"无信号"},this.hiddenText=t+" 当前："+e},async changeControl(e){if(this.command=e.action?this.command:e,!this.showVideo[this.currentSelect].serialNumber){n("请选择播放成功的窗口，进行控制");return}const t={id:this.showVideo[this.currentSelect].id,...this.command,action:e.action||0};m(t).then(o=>{o.data.code!==200&&n(o.data.msg)})},async chengeControl(e){if(!this.showVideo[this.currentSelect].serialNumber){n("请选择播放成功的窗口，进行控制");return}const t={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:e,speed:10},o=await p({param:JSON.stringify(t)}),i={parmdata:t,sign:o.data.sign};T(i).then(s=>{s.data.code==="0"&&s.data.msg==="设备不在线"&&n("设备不在线 不可控制")})},async changeZoom(e){if(console.log(e),!this.showVideo[this.currentSelect].serialNumber){n("请选择播放成功的窗口，进行控制");return}const t={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:e,speed:e===0?0:10},o=await p({param:JSON.stringify(t)}),i={parmdata:t,sign:o.data.sign};m(i).then(s=>{console.log(s)})},async changeAperture(e){if(console.log(e),!this.showVideo[this.currentSelect].serialNumber){n("请选择播放成功的窗口，进行控制");return}const t={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:e,speed:e===0?0:10},o=await p({param:JSON.stringify(t)}),i={parmdata:t,sign:o.data.sign};m(i).then(s=>{console.log(s)})},panelOpenChange(){this.panelOpen=!this.panelOpen},changeFullScreen(){const e=document.getElementById("full-box");document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen?(document.exitFullscreen&&document.exitFullscreen(),this.fullScreen=!1):(e.requestFullscreen&&e.requestFullscreen()||e.mozRequestFullScreen&&e.mozRequestFullScreen()||e.webkitRequestFullscreen&&e.webkitRequestFullscreen()||e.msRequestFullscreen&&e.msRequestFullscreen(),this.fullScreen=!0)}}},O={class:"left-video-list"},E={class:"list-box tree-list-box"},J={class:"custom-tree-node"},L={class:"c-t-label"},U={class:"c-t-name"},A={class:"right-video-show"},K={class:"top-btn-control"},Z={id:"full-box",class:"video-container"},$=["onClick"],G=["onClick"],W={key:1,class:"video-msg"};function Y(e,t,o,i,s,a){const _=j,v=b,y=w;return d(),u("div",{class:h(["real-time-video-container",{"real-time-video-full-screen":s.fullScreen}])},[r("div",O,[t[1]||(t[1]=r("p",{class:"list-title"}," 设备列表 ",-1)),r("div",E,[g(_,{ref:"dcTree",class:"data-source-s-tree",data:s.treeData,"node-key":"id","highlight-current":"","default-expand-all":"","default-checked-keys":[1],props:s.defaultProps,"expand-on-click-node":!1,onNodeClick:a.getProjectData},{default:C(({node:c,data:l})=>[r("div",J,[r("p",L,[l.serialNumber?(d(),u("i",{key:0,class:h(["iconfont icon-shexiangtou",{"shexiangtou-active":l.online}])},null,2)):P("",!0),r("span",U,S(c.label),1)])])]),_:1},8,["data","props","onNodeClick"])])]),r("div",A,[r("div",K,[r("p",{class:"full-screen",onClick:t[0]||(t[0]=(...c)=>a.changeFullScreen&&a.changeFullScreen(...c))},t[2]||(t[2]=[r("i",{class:"iconfont icon-quanping"},null,-1)]))]),r("div",Z,[(d(!0),u(D,null,I(s.showVideo,(c,l)=>(d(),u("div",{key:l,class:h(["video-box",{"item-1-size":s.currentSize===1,"item-4-size":s.currentSize===4,"item-9-size":s.currentSize===9,"current-select":s.currentSelect===l}]),onClick:V=>a.selcetVideoBox(l)},[r("i",{class:h(["el-icon-close",{"show-close":c.videoUrl}]),onClick:V=>a.closeVideo(l,"关闭")},null,10,G),c.videoUrl?(d(),x(v,{key:0,"video-info":c},null,8,["video-info"])):(d(),u("p",W,S(c.msg),1))],10,$))),128))])]),g(y,{ref:"refForm",config:s.formConfig},null,8,["config"])],2)}const ie=k(M,[["render",Y],["__scopeId","data-v-980e4b3c"]]);export{ie as default};
