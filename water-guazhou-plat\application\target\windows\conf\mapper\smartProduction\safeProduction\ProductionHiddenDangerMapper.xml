<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.safeProduction.ProductionHiddenDangerMapper">
    <select id="findList"
            resultType="org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionHiddenDander">
        SELECT
        a.*,
        b.first_name AS "creatorName",
        c.first_name AS "processUserName",d.name as "levelName"
        FROM sp_production_hidden_danger a
        LEFT JOIN tb_user b ON a.creator = b.id
        LEFT JOIN tb_user c ON a.process_user = c.id
        left join work_order_emergency_level d on a.level = d.id
        <where>
            <if test="param.code != null and param.code != ''">
                AND a.code like '%' || #{param.code} || '%'
            </if>
            <if test="param.name != null and param.name != ''">
                AND a.name like '%' || #{param.name} || '%'
            </if>
            <if test="param.status != null and param.status != ''">
                and a.status in
                <foreach collection="param.status.split(',')" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="param.tenantId != null and param.tenantId != ''">
                and a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.start != null">
                and a.create_time >= to_timestamp(#{param.start} / 1000)
            </if>
            <if test="param.end != null">
                and a.create_time &lt;= to_timestamp(#{param.end} / 1000)
            </if>
        </where>
            order by a.create_time desc
    </select>
</mapper>