package org.thingsboard.server.dao.smartService.kpi;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormManualConfig;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KpiNormManualConfigService {
    PageData getList(Boolean enabled, String month, String seatsId, Integer score, int page, int size, String tenantId);

    KpiNormManualConfig save(KpiNormManualConfig kpiNormManualConfig);

    int delete(List<String> ids);

    boolean check(KpiNormManualConfig kpiNormManualConfig);
}
