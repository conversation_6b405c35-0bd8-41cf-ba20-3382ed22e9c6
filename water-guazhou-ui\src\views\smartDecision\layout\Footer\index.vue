<template>
  <div class="footer">
    <div
      v-for="item in barItems"
      :key="item.name"
      class="footer__item"
      @click="to(item)"
    >
      <div
        class="icon"
        :class="[currentBar?.name === item.name ? 'is-active' : '']"
      ></div>
      <div
        class="title"
        :class="[currentBar?.name === item.name ? 'is-active' : '']"
      >
        <span>{{ item.text }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const emit = defineEmits(['to']);
const props = defineProps<{
  barItems: ISMART_DECISION_CONFIG_Bar[];
}>();
const currentBar = ref<ISMART_DECISION_CONFIG_Bar>(props.barItems[0]);
const to = (bar: ISMART_DECISION_CONFIG_Bar) => {
  currentBar.value = bar;
  emit('to', bar);
};
</script>
<style lang="scss" scoped>
.footer {
  width: 100%;
  height: 70px;
  background-image: url('./imgs/footer_bg.png');
  background-size: 100% 50%;
  background-position: 0 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  .footer__item {
    display: inline-flex;
    flex-direction: column;
    width: 96px;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    &:hover {
      .title {
        color: #58fef4;
      }
      .icon {
        background: url('./imgs/bar_item.png') 0 0 / 100% 100% no-repeat;
      }
    }
    .icon {
      width: 48px;
      height: 48px;
      background: url('./imgs/bar_item_1.png') 0 0 / 100% 100% no-repeat;
      &.is-active {
        background: url('./imgs/bar_item.png') 0 0 / 100% 100% no-repeat;
      }
    }
    .title {
      width: 100%;
      color: #b8d2ff;
      display: flex;
      justify-content: center;
      &.is-active {
        color: #58fef4;
      }
    }
  }
}
</style>
