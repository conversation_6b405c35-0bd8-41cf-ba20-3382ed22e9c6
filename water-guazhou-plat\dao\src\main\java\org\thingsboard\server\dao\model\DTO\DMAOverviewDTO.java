package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DMA进度统计报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-25
 */
@Data
@NoArgsConstructor
public class DMAOverviewDTO {

    private String id;

    private String name;

    private String parentName;

    private String status;

    private String statusName;

    private Integer bigUserNum;

    private Integer revenueUserNum;

    private Integer inWaterNum;

    private Integer outWaterNum;

    private BigDecimal supplyWaterArea;

    private BigDecimal mainLineLength;

    private Boolean isMachineMeter;

    private Integer collectRate;

}
