package org.thingsboard.server.dao.sql.smartManagement.district;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaPageRequest;

import java.util.List;

@Mapper
public interface CircuitDistrictAreaMapper extends BaseMapper<SMCircuitDistrictArea> {
    IPage<SMCircuitDistrictArea> findByPage(CircuitDistrictAreaPageRequest request);

    boolean update(SMCircuitDistrictArea entity);

    List<SMCircuitDistrictArea> findAllByDistrictId(String id);

    boolean isValidDistrictId(String districtId);

    String getPoints(String id);
}
