package org.thingsboard.server.dao.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.fileRegistry.FileRegistry;
import org.thingsboard.server.dao.util.imodel.query.FileRegistryPageRequest;

import java.util.List;

@Mapper
public interface FileRegistryMapper extends BaseMapper<FileRegistry> {
    IPage<FileRegistry> findByPage(FileRegistryPageRequest request);

    boolean updateFully(FileRegistry entity);

    boolean updateSelective(FileRegistry entity);

    int saveAll(List<FileRegistry> constructionFiles);

    boolean removeAll(@Param("label") String label, @Param("host") String host);
}
