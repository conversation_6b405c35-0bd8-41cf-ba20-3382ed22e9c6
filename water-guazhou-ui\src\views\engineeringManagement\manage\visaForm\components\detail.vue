<template>
  <div class="team_table">
    <FormTable :config="TableConfig"></FormTable>
    <DialogForm
      ref="refForm"
      class="dialogForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="6"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { postConstructionVisa } from '@/api/engineeringManagement/manage';
import detail from '../../../components/detail.vue';
import { formatDate } from '@/utils/DateFormatter';

const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();

const props = defineProps<{
  config: {
    items: any;
  };
}>();

const emit = defineEmits(['extendedReturn']);

const data = reactive({
  selected: {}
});

const TableConfig = reactive<ITable>({
  loading: false,
  indexVisible: true,
  dataList: computed(() => props.config.items) as any,
  columns: [
    { prop: 'code', label: '签证编号' },
    { prop: 'constructionName', label: '工程名称' },
    { prop: 'address', label: '施工地点' },
    { prop: 'constructOrganization', label: '施工单位' },
    {
      prop: 'constructTime',
      label: '施工时间',
      formatter: (row) => formatDate(row.constructTime, 'YYYY-MM-DD HH:mm:ss')
    },
    { prop: 'buildOrganization', label: '建设单位' },
    { prop: 'supervisorOrganization', label: '监理单位' },
    { prop: 'auditOrganization', label: '审计单位' },
    { prop: 'creatorName', label: '添加人' },
    { prop: 'createTimeName', label: '添加时间' }
  ],
  operationWidth: '200px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '编辑',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      isTextBtn: false,
      text: '详情',
      perm: true,
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    }
  ],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '编辑签证',
  appendToBody: true,
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionVisa(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
        emit('extendedReturn', {});
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '签证编号',
          field: 'code',
          rules: [{ required: true, message: '请输入签证编号' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'constructionCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工程名称',
          field: 'constructionName',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '施工单位',
          field: 'constructOrganization',
          rules: [{ required: true, message: '请输入施工单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '施工地点',
          field: 'address',
          rules: [{ required: true, message: '请输入施工地点' }]
        },
        {
          xs: 12,
          type: 'date',
          label: '施工时间',
          field: 'constructTime',
          format: 'x'
        },
        {
          xs: 12,
          type: 'input',
          label: '建设单位',
          field: 'buildOrganization',
          rules: [{ required: true, message: '请输入建设单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '监理单位',
          field: 'supervisorOrganization',
          rules: [{ required: true, message: '请输入监理单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '审计单位',
          field: 'auditOrganization',
          rules: [{ required: true, message: '请输入审计单位' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  appendToBody: true,
  cancel: false
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑签证';
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDialog();
};
</script>

<style lang="scss" scoped>
.team_table {
  width: 100%;
  padding: 5px 15px;
}
</style>
