package org.thingsboard.server.dao.gis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.gis.GisPipeAdditionalInfo;
import org.thingsboard.server.dao.sql.gis.GisPipeAdditionalInfoRepository;

@Slf4j
@Service
public class GisPipeAdditionalInfoServiceImpl implements GisPipeAdditionalInfoService {

    @Autowired
    private GisPipeAdditionalInfoRepository gisPipeAdditionalInfoRepository;


    @Override
    public GisPipeAdditionalInfo findOne(String layerid, String objectid, TenantId tenantId) {
        return gisPipeAdditionalInfoRepository.findByLayeridAndObjectidAndTenantId(layerid, objectid, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public void save(GisPipeAdditionalInfo entity) {
        GisPipeAdditionalInfo one = this.findOne(entity.getLayerid(), entity.getObjectid(), new TenantId(UUIDConverter.fromString(entity.getTenantId())));
        if (one != null) {
            one.setVidio(entity.getVidio());
            one.setAudio(entity.getAudio());
            one.setFiles(entity.getFiles());
            one.setImg(entity.getImg());
            gisPipeAdditionalInfoRepository.save(one);
        } else {
            gisPipeAdditionalInfoRepository.save(entity);
        }
    }
}
