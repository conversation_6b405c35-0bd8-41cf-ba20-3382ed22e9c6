package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class MessageTemplatePageRequest extends AdvancedPageableQueryEntity<MessageTemplate, MessageTemplatePageRequest> {
    // 关键字
    private String keyword;
}
