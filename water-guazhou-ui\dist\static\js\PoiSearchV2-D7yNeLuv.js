import{d as I,c as S,r as V,o as k,g as p,n as u,q as c,F as f,i as d,p as v,bh as m,aK as q,aL as G,d7 as w,C as A}from"./index-r0dFAfgr.js";/* empty css                        */import{I as E,q as N}from"./utils-D5nxoMq3.js";const x={id:"tool-search-poi",class:"esri-widget"},F={class:"poi-item"},O={class:"poi-text"},B={key:0,class:"poi-address"},D={key:1,class:"poi-address"},P=I({__name:"PoiSearchV2",emits:["change"],setup(b,{emit:g}){const _=S(),h=g,e=V({value:"",loading:!1,options:[],queryType:"7",curItem:void 0});let y;const C=(a,t)=>{clearTimeout(y),y=setTimeout(()=>{a?(e.loading=!0,N({keyWord:a,queryType:e.queryType,specifyAdminCode:e.specifyAdminCode}).then(l=>{var n,s,r;const o=l.data||{};e.returnType=o.resultType,o.resultType===1?e.options=((n=o.pois)==null?void 0:n.map(i=>({value:i.name,data:i})))||[]:o.resultType===2&&(e.options=(r=(s=o==null?void 0:o.statistics)==null?void 0:s.priorityCitys)==null?void 0:r.map(i=>({value:o.keyWord,data:i}))),t(e.options)}).finally(()=>{e.loading=!1})):(e.options=[],t(e.options))},300)},T=a=>{var t,l;if(a&&(e.curItem=a),!(e.loading||!e.curItem))if(e.returnType===2)e.value=a.data.name,(t=_.value)==null||t.focus();else{const o=(l=e.curItem.data.lonlat)==null?void 0:l.split(",");h("change",o)}};return k(()=>{var a,t,l,o;E({lon:(t=(a=window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter)==null?void 0:a[0])==null?void 0:t.toString(),lat:(o=(l=window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter)==null?void 0:l[1])==null?void 0:o.toString()}).then(n=>{var s,r,i;e.specifyAdminCode=(i=(r=(s=n.data)==null?void 0:s.result)==null?void 0:r.addressComponent)==null?void 0:i.city_code}).catch(n=>{console.log(n)})}),(a,t)=>{const l=q,o=G,n=w;return p(),u("div",x,[c(o,{modelValue:d(e).queryType,"onUpdate:modelValue":t[0]||(t[0]=s=>d(e).queryType=s),placeholder:"Select",class:"poi-selector"},{default:f(()=>[c(l,{label:"poi",value:"1"}),c(l,{label:"地名",value:"7"})]),_:1},8,["modelValue"]),c(n,{ref_key:"refAutoCom",ref:_,modelValue:d(e).value,"onUpdate:modelValue":t[1]||(t[1]=s=>d(e).value=s),"fetch-suggestions":C,"popper-class":"my-autocomplete",placeholder:"请输入",onSelect:T},{default:f(({item:s})=>[v("div",F,[v("div",O,m(s.value),1),d(e).returnType===1?(p(),u("div",B," 地址："+m(s.data.address),1)):(p(),u("div",D,"城市："+m(s.data.name),1))])]),_:1},8,["modelValue"])])}}}),K=A(P,[["__scopeId","data-v-800662ac"]]);export{K as P};
