package org.thingsboard.server.dao.sql.station;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.sql.StationEntity;

import java.util.List;

public interface StationRepository extends JpaRepository<StationEntity, String> {

    @Query("SELECT s FROM StationEntity s " +
            "WHERE s.tenantId = ?1 AND s.type IN ?2 AND s.projectId LIKE %?3% " +
            "ORDER BY s.orderNum DESC, s.createTime ASC")
    Page<StationEntity> findList(String tenantId, List<String> type, String projectId, Pageable pageable);

    @Query("SELECT s FROM StationEntity s " +
            "WHERE s.tenantId = ?1 AND s.type IN ?2 AND s.projectId IN ?3 " +
            "ORDER BY s.orderNum DESC, s.createTime ASC")
    Page<StationEntity> findList(String tenantId, List<String> type, List<String> projectId, Pageable pageable);

    List<StationEntity> findByTypeLikeAndIdIn(String stationType, List<String> stationIdList);

    List<StationEntity> findByIdInOrderByOrderNum(List<String> stationIdList);

    List<StationEntity> findByTypeInAndTenantIdOrderByOrderNum(List<String> type, String tenantId);

    List<StationEntity> findByTenantIdOrderByOrderNum(String tenantId);

    @Query("SELECT new org.thingsboard.server.dao.model.DTO.CountObjDTO(s.type, COUNT(s.id)) " +
            "FROM StationEntity s " +
            "WHERE s.tenantId = ?2 AND s.type IN ?1 " +
            "GROUP BY s.type")
    List<CountObjDTO> typeCount(List<String> typeList, String tenantId);

    List<StationEntity> findAllByNameLikeAndTypeAndTenantId(String name, String type, String tenantId);

    @Query("SELECT new org.thingsboard.server.dao.model.DTO.CountObjDTO(s.type, COUNT(s.id)) " +
            "FROM StationEntity s " +
            "WHERE s.tenantId = ?2 AND s.type IN ?1 and s.projectId = ?3 " +
            "GROUP BY s.type")
    List<CountObjDTO> typeCountProjectId(List<String> typeList, String tenantId, String projectId);
}
