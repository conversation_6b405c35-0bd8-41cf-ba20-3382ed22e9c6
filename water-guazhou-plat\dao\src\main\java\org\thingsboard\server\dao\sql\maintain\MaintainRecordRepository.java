/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.maintain;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.common.data.maintain.MaintainRecord;
import org.thingsboard.server.dao.model.sql.MaintainEntity;
import org.thingsboard.server.dao.model.sql.MaintainRecordEntity;
import org.thingsboard.server.dao.model.sql.RepairEntity;

import javax.transaction.Transactional;
import java.util.List;

public interface MaintainRecordRepository extends CrudRepository<MaintainRecordEntity, String> {

    List<MaintainRecordEntity> findByTenantId(String tenantId);

    List<MaintainRecordEntity> findByProjectId(String project);

    List<MaintainRecordEntity> findByMaintainIdAndStatus(String maintainId, String status);

    List<MaintainRecordEntity> findByMaintainId(String maintainId);

    List<MaintainRecordEntity> findByDeviceId(String deviceId);

    List<MaintainRecordEntity> findByTenantIdAndStatus(String deviceId,String status);

    List<MaintainRecordEntity> findByProjectIdAndStatus(String projectId,String status);
}
