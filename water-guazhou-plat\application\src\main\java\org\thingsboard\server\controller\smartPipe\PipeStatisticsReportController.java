package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PipeSaleWaterReportRequest;
import org.thingsboard.server.dao.smartPipe.PipeStatisticsReportService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.ArrayList;

/**
 * 智慧管网-统计报表
 */
@RestController
@RequestMapping("api/spp/statisticsReport")
public class PipeStatisticsReportController extends BaseController {


    @Autowired
    private PipeStatisticsReportService pipeStatisticsReportService;

    @GetMapping("nrw")
    public IstarResponse getNrwReport(String type, String date, String start, String end, String searchType) throws ThingsboardException {
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择查询时间");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(pipeStatisticsReportService.getNrwReport(type, date, start, end, tenantId, new ArrayList<>()));
    }

    /**
     * 产销差forApp
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("nrwForApp")
    @Cacheable(cacheNames = "nrwForApp", key = "#searchType")
    public IstarResponse getNrwForApp(String searchType) throws Exception {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        if (searchType.contains("jinzhou")) {

            JSONObject result = (JSONObject) this.getNrwReport("year", LocalDate.now().getYear() +"", "", "", "").getData();


            return IstarResponse.ok(result);
        }


        return IstarResponse.ok(pipeStatisticsReportService.getNrwForApp(tenantId));
    }

    @GetMapping("nrwExport")
    public IstarResponse getNrwReportExport(String type, String date, String start, String end, HttpServletResponse response) throws ThingsboardException {
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择查询时间");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        JSONObject nrwReport = pipeStatisticsReportService.getNrwReport(type, date, start, end, tenantId, new ArrayList<>());

        String title = "";
        switch (type) {
            case "year":
                title = date + "年";
                break;

            case "month":
                title = start + " - " + end;
                break;

            case "yearInterval":
                title = start + "年-" + end + "年";
                break;
        }
        title = title + "产销差报表";
        ExcelUtil.getNrwReport(title, nrwReport, response);
        return IstarResponse.ok();
    }

    @GetMapping("nrwDetail")
    public IstarResponse getNrwDetailReport(String partitionIds, String type, String date, String start, String end) throws ThingsboardException {
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择查询时间");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(pipeStatisticsReportService.getNrwDetailReport(partitionIds, type, date, start, end, tenantId));
    }

    /**
     * 黄平大屏产销差分析
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("nrwYearHP")
    public IstarResponse getNrwDetailHP() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(pipeStatisticsReportService.getNrwDetailHP(tenantId));
    }

    @GetMapping("nrwDetail/detail")
    public IstarResponse getNrwDetailReportDetail(String partitionId, String type, String date, String start, String end) throws ThingsboardException {
        IstarResponse result = checkDate(partitionId, type, date, start);
        if (result != null) return result;

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(pipeStatisticsReportService.getNrwDetailReportDetail(partitionId, type, date, start, end, tenantId));
    }

    @GetMapping("partitionSupplyHeader")
    public IstarResponse getPartitionSupplyHeader(String partitionId) throws ThingsboardException {
        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getPartitionSupplyHeader(partitionId, tenantId));
    }

    @GetMapping("partitionSupply")
    public IstarResponse getPartitionSupply(String partitionId, String type, String date, String start, String end) throws ThingsboardException {
        IstarResponse result = checkDate(partitionId, type, date, start);
        if (result != null) return result;

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getPartitionSupply(partitionId, type, date, start, end, tenantId));
    }

    @GetMapping("partitionInOutWater")
    public IstarResponse getPartitionInOutWater(String partitionId, String type, String date, String start, String end) throws ThingsboardException {
        IstarResponse result = checkDate(partitionId, type, date, start);
        if (result != null) return result;

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getPartitionInOutWater(partitionId, type, date, start, end, tenantId));
    }

    @GetMapping("dayFlow")
    public IstarResponse getDayFlow(String partitionIds, String day) throws ThingsboardException {
        if (StringUtils.isBlank(day)) {
            return IstarResponse.error("请选择日期");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getDayFlow(partitionIds, day, tenantId));
    }

    @GetMapping("saleWater")
    public IstarResponse getSaleWater(PipeSaleWaterReportRequest request) throws ThingsboardException {
        if (StringUtils.isBlank(request.getPartitionId())) {
            return IstarResponse.error("请选择分区");
        }
        if (StringUtils.isBlank(request.getYear())) {
            return IstarResponse.error("请选择日期");
        }
        if (Integer.valueOf(request.getYear()) > LocalDate.now().getYear()) {
            return IstarResponse.error("所选日期不能大于今年");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(pipeStatisticsReportService.getSaleWater(request));
    }

    @GetMapping("dmaOverview")
    public IstarResponse getDMAOverview(String status, String partitionName) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getDMAOverview(status, partitionName, tenantId));
    }

    @GetMapping("bigUser")
    public IstarResponse getBigUser(Integer grade, String type, String date, String start, String end) throws ThingsboardException {
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择查询时间");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getBigUser(grade, type, date, start, end, tenantId));
    }

    @GetMapping("pressurePassRate")
    public IstarResponse getPressurePassRate(String name, String date) throws ThingsboardException {
        if (StringUtils.isBlank(date)) {
            return IstarResponse.error("请选择日期");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getPressurePassRate(name, date, tenantId));
    }

    @GetMapping("lossWaterCompareToLastYear")
    public IstarResponse lossWaterCompareToLastYear(String name, String date) throws ThingsboardException {
        if (StringUtils.isBlank(date)) {
            return IstarResponse.error("请选择日期");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.lossWaterCompareToLastYear(name, date, tenantId));
    }

    @GetMapping("lossPoint")
    public IstarResponse getLossPoint(String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeStatisticsReportService.getLossPoint(name, tenantId));
    }

    @Nullable
    private IstarResponse checkDate(String partitionId, String type, String date, String start) {
        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请选择查询时间");
        }
        return null;
    }
}
