package org.thingsboard.server.dao.util.imodel.query.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

import static org.thingsboard.server.dao.util.reflection.ExceptionUtils.testNumber;

@Getter
@Setter
public class StoreSaveRequest extends SaveRequest<Store> {
    // 仓库编码
    @NotNullOrEmpty
    private String code;

    // 仓库名称
    @NotNullOrEmpty
    private String name;

    // 仓库地址
    @NotNullOrEmpty
    private String address;

    // 排序，升序
    private Integer orderNum;

    // 管理员ID
    @NotNullOrEmpty
    private String managerId;

    // 备注
    private String remark;

    // 电话号码
    @NotNullOrEmpty
    private String tel;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public String valid(IStarHttpRequest request) {
        testNumber(code, "编码只能为数值");
        return super.valid(request);
    }

    @Override
    protected Store build() {
        Store entity = new Store();
        entity.setTenantId(tenantId());
        entity.setCreator(currentUserUUID());
        commonSet(entity);
        return entity;
    }

    @Override
    protected Store update(String id) {
        Store entity = new Store();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(Store entity) {
        Date now = new Date();
        entity.setCode(code);
        entity.setName(name);
        entity.setAddress(address);
        entity.setOrderNum(orderNum);
        entity.setManagerId(managerId);
        entity.setRemark(remark);
        entity.setTel(tel);
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
    }
}