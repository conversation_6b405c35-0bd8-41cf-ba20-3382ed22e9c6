import{Y as o,e,y as s,a,v as l}from"./Point-WxyopZva.js";import{cO as p,cP as y}from"./MapView-DaoQedLH.js";import{R as n}from"./index-r0dFAfgr.js";let c=0,t=class extends o(p(y(l))){constructor(i){super(i),this.id=`${Date.now().toString(16)}-analysis-${c++}`,this.title=null}get parent(){return this._get("parent")}set parent(i){const r=this.parent;if(n(r))switch(r.type){case"line-of-sight":case"dimension":r.releaseAnalysis(this);break;case"2d":case"3d":r.analyses.includes(this)&&r.analyses.remove(this)}this._set("parent",i)}get isEditable(){return this.requiredPropertiesForEditing.every(n)}};e([s({type:String,constructOnly:!0,clonable:!1})],t.prototype,"id",void 0),e([s({type:String})],t.prototype,"title",void 0),e([s({constructOnly:!0})],t.prototype,"type",void 0),e([s({clonable:!1,value:null})],t.prototype,"parent",null),e([s({readOnly:!0})],t.prototype,"isEditable",null),e([s({readOnly:!0})],t.prototype,"requiredPropertiesForEditing",void 0),t=e([a("esri.analysis.Analysis")],t);const g=t;export{g as c};
