<template>
  <div
    v-loading="
      typeof config.loading === 'function'
        ? config.loading(row, modelValue)
        : !!config.loading
    "
    :style="
      typeof config.style === 'function'
        ? config.style(modelValue, row, config)
        : config.style
    "
    class="list-wrapper overlay"
    :class="config.className"
  >
    <el-tree
      ref="refTree"
      style="width: 100%"
      :size="config.size || size || 'default'"
      :data="config.options"
      :show-checkbox="config.showCheckbox"
      :check-strictly="config.checkStrictly"
      :check-on-click-node="config.checkOnClickNode !== false"
      :node-key="config.nodeKey || 'id'"
      default-expand-all
      :props="config.props || state.defaultProps"
      :expand-on-click-node="config.expandOnClickNode"
      :filter-node-method="filterNode"
      :highlight-current="config.highlightCurrent"
      @check="handleCheck"
      @node-click="config.nodeClick"
      @check-change="config.handleCheckChange"
      @node-contextmenu="config.nodeContextmenu"
      @current-change="handleCurrentChange"
      @node-expand="config.nodeExpand"
      @node-collapse="config.nodeCollapse"
    >
      <template
        v-if="$slots.default"
        #default="{ node, data }"
      >
        <slot
          :node="node"
          :data="data"
          :customprops="config.customProps"
        ></slot>
      </template>
    </el-tree>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { IElTree } from '@/common/types/element-plus'

const emit = defineEmits(['change', 'update:model-value'])
const props = defineProps<{
  modelValue?: string[]
  config: IFormTree
  row?: any
  size?: ISize
}>()
const refTree = ref<IElTree>()
const state = reactive<{
  defaultProps: any
}>({
  defaultProps: {
    children: 'children',
    label: 'label'
  }
})
watch(
  () => props.modelValue,
  (newVal: any) => {
    setCheckedKeys(newVal)
  }
)
const handleCurrentChange = (data: NormalOption) => {
  if (!props.config.showCheckbox) {
    emit('change', [data.value], data, [data])
    emit('update:model-value', [data.value])
  }
}
const handleCheck = (data: any, keys: any) => {
  if (props.config.showCheckbox) {
    emit('change', keys?.checkedKeys || [], data, keys?.checkedNodes || [])
    emit('update:model-value', keys?.checkedKeys || [])
  }

  // props.config.onChange && props.config.onChange(keys.checkedKeys || [])
}
const setCheckedKeys = (keys: any) => {
  props.config.showCheckbox && refTree.value?.setCheckedKeys(keys || [])
}
const filterNode = (value, data) => {
  const val: string | undefined = value?.trim()?.toLocaleLowerCase()
  if (!val) return true
  const label = data?.label?.trim()?.toLocaleLowerCase()
  if (!label) return false
  return label.indexOf(val) !== -1
}
watch(
  () => props.config.filterBy && props.row && props.row[props.config.filterBy],
  value => {
    refTree.value?.filter(value)
  }
)
onMounted(() => {
  setCheckedKeys(props.modelValue)
})
</script>
<style lang="scss" scoped>
.list-wrapper {
  width: 100%;
  padding-right: 8px;
}
:deep(.el-tree-node__content){
  height: 28px;
}
:deep(.el-tree-node__label){
  line-height: 28px;
}
</style>
