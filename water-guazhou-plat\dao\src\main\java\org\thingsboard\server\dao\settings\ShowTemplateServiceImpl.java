package org.thingsboard.server.dao.settings;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.ShowTemplate;
import org.thingsboard.server.dao.sql.settings.ShowTemplateRepository;

import java.util.List;

@Slf4j
@Service
public class ShowTemplateServiceImpl implements ShowTemplateService {

    @Autowired
    private ShowTemplateRepository showTemplateRepository;

    @Override
    public List<ShowTemplate> findList(TenantId tenantId, String type) {
        return showTemplateRepository.findByTenantIdAndType(UUIDConverter.fromTimeUUID(tenantId.getId()), type);
    }

    @Override
    public void saveOne(ShowTemplate showTemplate) {
        // 先删除再保存
        showTemplateRepository.deleteByTenantIdAndTypeAndName(showTemplate.getTenantId(), showTemplate.getType(), showTemplate.getName());

        // 保存
        showTemplateRepository.save(showTemplate);
    }

    @Override
    @Transactional
    public void saveAll(List<ShowTemplate> entityList) {
        for (ShowTemplate showTemplate : entityList) {
            saveOne(showTemplate);
        }
    }
}
