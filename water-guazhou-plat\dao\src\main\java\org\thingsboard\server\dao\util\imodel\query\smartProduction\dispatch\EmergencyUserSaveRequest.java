package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class EmergencyUserSaveRequest extends SaveRequest<EmergencyUser> {
    // 人员名称
    @NotNullOrEmpty
    private String name;

    // 联系电话
    @NotNullOrEmpty
    private String phone;

    // 所属部门
    @NotNullOrEmpty
    private String deptId;

    @Override
    protected EmergencyUser build() {
        EmergencyUser entity = new EmergencyUser();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected EmergencyUser update(String id) {
        EmergencyUser entity = new EmergencyUser();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(EmergencyUser entity) {
        entity.setName(name);
        entity.setPhone(phone);
        entity.setDeptId(deptId);
    }
}