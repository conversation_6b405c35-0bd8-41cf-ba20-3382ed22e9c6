import{d as l,g as o,n as a,aB as _,aJ as d,q as u,F as i,p as e,bh as t,dD as g,C as f}from"./index-r0dFAfgr.js";const v={class:"flex"},x={class:"span_text"},h={style:{"font-size":"20px"}},m=l({__name:"progress",props:{progressvalue:{}},setup(r){const n=r;return(y,B)=>{const c=g;return o(),a("div",v,[(o(!0),a(_,null,d(n.progressvalue,(s,p)=>(o(),a("div",{key:p,class:"progress"},[u(c,{color:s.color,type:"circle",percentage:100,status:"success"},{default:i(()=>[e("div",x,[e("span",h,t(s.value),1),e("span",null,t(s.unit),1)])]),_:2},1032,["color"]),e("span",null,t(s.name),1)]))),128))])}}}),C=f(m,[["__scopeId","data-v-d8696613"]]);export{C as default};
