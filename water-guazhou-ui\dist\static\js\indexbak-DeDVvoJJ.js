import{_ as P}from"./TreeBox-DDD2iwoR.js";import{_ as V}from"./index-C9hz-UZb.js";import{d as W,j as L,c as M,r as b,a0 as I,bB as z,o as E,g as n,h as F,F as r,q as u,p as a,n as c,aB as D,aJ as k,aw as v,i as x,cs as q,bh as _,an as C,c5 as A,C as H}from"./index-r0dFAfgr.js";import{_ as J}from"./index-BJ-QPYom.js";import{b as O,c as U}from"./headwaterMonitoring-BgK7jThW.js";import $ from"./stationDetailMonitoring-DpwJmR2X.js";import{u as G}from"./useStation-DJgnSZIA.js";import"./CardSearch-CB_HNR-Q.js";import"./Search-NSrhrIa_.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";/* empty css                         */import"./echart-D5stWtDc.js";import"./zhandian-YaGuQZe6.js";const K={class:"wrapper-content"},Q={key:0},R={class:"statistics"},X={class:"item-content"},Y={class:"item-text title"},Z={class:"total"},tt={class:"item-text count"},et={class:"tab-content"},at={class:"card-box"},st={class:"monitor-box"},ot={class:"total"},nt={class:"label"},it={key:0,class:"unit"},ct={key:1,style:{height:"calc(100% - 100px)"}},lt=W({__name:"indexbak",setup(rt){const{getStationTree:B}=G(),N=L(),h=M([]),t=b({Statistics:[{className:"orange",title:"昨日供水量",count:0,unit:"m³"},{className:"green",title:"今日供水量",count:0,unit:"m³"},{className:"blue",title:"上月供水量",count:0,unit:"m³"},{className:"purple",title:"本月供水量",count:0,unit:"m³"},{className:"red",title:"总供水量",count:0,unit:"m³"},{className:"light-blue",title:"报警率",count:0,unit:"个"}],ShuiChangDatas:[],activeName:"全部",tabsList:[],stationTree:[],treeDataType:"Project",stationId:""}),m=b({data:[],loading:!0,title:"水源站点",expandOnClickNode:!1,treeNodeHandleClick:async e=>{m.currentProject!==e&&(m.currentProject=e,t.treeDataType=e.data.type,t.treeDataType==="Project"?(I().SET_selectedProject(e),h.value=[],t.ShuiChangDatas=[],await T(e)):await z(()=>{t.stationId=e.id}))}}),g=b({type:"tabs",tabType:"simple",width:"100%",stretch:!0,tabs:[],handleTabClick:e=>{console.log("动都不动不得不对不对",e.props.name),t.ShuiChangDatas=h.value,e.props.name!=="全部"&&(t.ShuiChangDatas=t.ShuiChangDatas.filter(l=>l.title===e.props.name||l.title==="全部"))}}),T=async e=>{var y,p;await O({projectId:(e==null?void 0:e.value)||((y=I().selectedProject)==null?void 0:y.value)}).then(s=>{var o,d,i,f,w,j;t.Statistics[0].count=((o=s.data.data)==null?void 0:o.yesterdayWaterSupply)||0,t.Statistics[1].count=((d=s.data.data)==null?void 0:d.todayWaterSupply)||0,t.Statistics[2].count=((i=s.data.data)==null?void 0:i.lastMonthWaterSupply)||0,t.Statistics[3].count=((f=s.data.data)==null?void 0:f.monthWaterSupply)||0,t.Statistics[4].count=((w=s.data.data)==null?void 0:w.totalWaterSupply)||0,t.Statistics[5].count=((j=s.data.data)==null?void 0:j.alarmNum)||0}).catch(()=>{t.Statistics[0].count=0,t.Statistics[1].count=0,t.Statistics[2].count=0,t.Statistics[3].count=0,t.Statistics[4].count=0,t.Statistics[5].count=0});const S=(p=(await U({projectId:e==null?void 0:e.value})).data)==null?void 0:p.data;console.log(S),g.tabs=[{value:"全部",label:"全部"}],S.map(s=>{g.tabs.push({value:s.name,label:s.name}),h.value.push({id:s.stationId,title:s.name,monitorData:s.dataList||[]}),t.ShuiChangDatas=h.value})};return E(async()=>{const e=await B("水源地");m.data=e,m.currentProject=e[0],await T()}),(e,l)=>{const S=J,y=A,p=V,s=P;return n(),F(s,null,{tree:r(()=>[u(S,{ref:"refTree","tree-data":m},null,8,["tree-data"])]),default:r(()=>[a("div",K,[t.treeDataType==="Project"?(n(),c("div",Q,[u(p,{class:"wrapper-content",title:"实时监测",overlay:""},{title:r(()=>l[1]||(l[1]=[a("span",{class:"title title-header"},"实时监测",-1)])),default:r(()=>[a("div",R,[(n(!0),c(D,null,k(t.Statistics,(o,d)=>(n(),c("div",{key:d,class:"statistics-item"},[a("div",{class:v(["item-inner",o.className])},[u(x(q),{size:60,icon:"material-symbols:water-drop-outline"}),a("div",X,[a("div",Y,_(o.title),1),a("div",Z,[a("span",tt,_(o.count),1)])])],2)]))),128))]),u(p,{class:"card",style:{},title:" "},{title:r(()=>[u(y,{modelValue:t.activeName,"onUpdate:modelValue":l[0]||(l[0]=o=>t.activeName=o),config:g},null,8,["modelValue","config"])]),default:r(()=>[a("div",et,[a("div",at,[a("div",{class:v(["card-item",{isDark:x(N).isDark}])},[(n(!0),c(D,null,k(t.ShuiChangDatas,(o,d)=>(n(),c("div",{key:d,class:"card-content"},[u(p,{title:o.title,class:"inner-card left",overlay:""},{default:r(()=>[a("div",st,[(n(!0),c(D,null,k(o.monitorData,(i,f)=>(n(),c("div",{key:f,class:v(["monitor-item",{isDark:x(N).isDark}])},[a("span",{class:v(["count",i.status===!0?"text-green":i.status===!1?"text-red":"text-blue"])},_(i.value),3),a("span",ot,[a("span",nt,_(i.propertyName),1),i.unit?(n(),c("span",it,_(i.unit),1)):C("",!0)])],2))),128))])]),_:2},1032,["title"])]))),128))],2)])])]),_:1})]),_:1})])):C("",!0),t.treeDataType==="Station"?(n(),c("div",ct,[u($,{"station-id":t.stationId,monitor:"cs"},null,8,["station-id"])])):C("",!0)])]),_:1})}}}),Nt=H(lt,[["__scopeId","data-v-2b8e878e"]]);export{Nt as default};
