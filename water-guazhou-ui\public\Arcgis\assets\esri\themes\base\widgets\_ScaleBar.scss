@mixin scaleBar() {
  $dark_color: $font-color;
  $light_color: $background-color;

  $block_width: 25%;
  $block_height: 50%;

  $line_thickness: 2px;
  $tic_height: 1.5em;

  $offset_for_unit_label: 2ch;

  $border_style: $line_thickness solid $dark_color;

  .esri-scale-bar.esri-widget {
    background: transparent;
    box-shadow: none;
  }

  .esri-scale-bar__bar-container {
    position: relative;
    display: flex;
    align-items: flex-end;
    transition: width 250ms ease-in-out;
    font-size: $font-size--small;
  }

  .esri-scale-bar__bar-container--ruler {
    flex-direction: column;
  }

  .esri-scale-bar__bar-container--line:last-child {
    align-items: flex-start;
  }

  .esri-scale-bar__ruler {
    display: flex;
    flex-wrap: wrap;
    height: 6px;
    background-color: $light_color;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.33), 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .esri-scale-bar__ruler-block {
    height: $block_height;
    width: $block_width;
    background-color: $dark_color;
  }

  // top row
  .esri-scale-bar__ruler-block:nth-child(-n + 2) {
    margin-right: 25%;
  }

  // bottom row
  .esri-scale-bar__ruler-block:nth-child(n + 3) {
    margin-left: 25%;
  }

  .esri-scale-bar__line {
    position: relative;
    height: $tic_height;
    background-color: rgba($background-color, 0.33);
    left: 0;
    z-index: 1;
  }

  .esri-scale-bar__line--top {
    bottom: -1px;
    border-bottom: $border_style;
  }

  .esri-scale-bar__line--bottom {
    top: floor($line_thickness * 0.5) * -1;
    border-top: $border_style;
  }

  // tics
  .esri-scale-bar__line--top:before,
  .esri-scale-bar__line--top:after,
  .esri-scale-bar__line--bottom:before,
  .esri-scale-bar__line--bottom:after {
    content: "";
    display: block;
    width: $line_thickness;
    height: $tic_height;
    background-color: $dark_color;
    position: absolute;
    border-right: $border_style;
  }

  .esri-scale-bar__line--top:before {
    bottom: -$line_thickness;
    left: 0;
  }

  .esri-scale-bar__line--top:after {
    bottom: -$line_thickness;
    right: 0;
  }

  .esri-scale-bar__line--bottom:before {
    top: -$line_thickness;
    left: 0;
  }

  .esri-scale-bar__line--bottom:after {
    height: $tic_height;
    top: -$line_thickness;
    right: 0;
  }

  .esri-scale-bar__label-container--line {
    position: absolute;
    left: 0;
    z-index: 1;
  }

  .esri-scale-bar__label-container--ruler {
    display: flex;
    width: 100%;
    justify-content: space-between;
    position: relative;
    .esri-scale-bar__label {
      padding: $cap-spacing--half 0 0;
      text-shadow: 0 0 1px $light_color, 0 0 1px $light_color, 0 0 1px $light_color;
    }
  }

  .esri-scale-bar__label-container--top {
    bottom: 0;
  }

  .esri-scale-bar__label-container--bottom {
    top: floor($line_thickness * 0.5);
  }

  .esri-scale-bar__label {
    font-size: inherit;
    color: $dark_color;
    white-space: nowrap;
    padding: 0 $side-spacing--half;
    font-weight: $font-weight--bold;
  }

  [dir="rtl"] {
    .esri-scale-bar__ruler {
      margin: 0 ($offset_for_unit_label * 0.25) 0 $offset_for_unit_label;
    }

    .esri-scale-bar__label-container--line {
      left: auto;
      right: 0;
    }
  }
}

@if $include_ScaleBar == true {
  @include scaleBar();
}
