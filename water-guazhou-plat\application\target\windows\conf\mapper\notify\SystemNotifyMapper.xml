<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.notify.SystemNotifyMapper">
    <update id="readAll">
        update tb_system_notify set status = '1'
        <where>
            <if test="type != null and type != ''.toString()">
                and type = #{type}
            </if>
            <if test="to != null and to != ''.toString()">
                and (to_user = #{to} or to_user is null or to_user = '')
            </if>
            <if test="tenantId != null and tenantId != ''.toString()">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </update>

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.notify.SystemNotify">
        SELECT
        A.*
        FROM
        tb_system_notify A
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.topic != null and param.topic != ''">
                AND a.topic LIKE '%' || #{param.topic} ||'%'
            </if>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
            <if test="param.to != null and param.to != ''">
                AND (a.to_user = #{param.to} or a.to_user is null or a.to_user = '')
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.status = #{param.status}
            </if>
            <if test="param.beginTime != null">
                AND a.time <![CDATA[ >= ]]> #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.time <![CDATA[ <= ]]> #{param.endTime}
            </if>
        </where>
        ORDER BY a.time DESC
    </select>

    <select id="notifyCount" resultType="org.thingsboard.server.dao.model.DTO.CountObjDTO">
        SELECT a.type AS "key", count(*) AS "count"
        FROM tb_system_notify a
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <!--<if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>-->
            <if test="to != null and to != ''">
                AND (a.to_user = #{to} or a.to_user is null or a.to_user = '')
            </if>
            <if test="status != null and status != ''">
                AND a.status = #{status}
            </if>
        </where>
        GROUP BY a.type
    </select>

</mapper>