package org.thingsboard.server.dao.model.sql.input;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_TABLE)
@NoArgsConstructor
public class InputProductPlan {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_CAPACITY)
    private BigDecimal capacity;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_MONTH_PLAN)
    private BigDecimal monthPlan;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_MONTH_NUM)
    private BigDecimal monthNum;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_MAX_DAY)
    private BigDecimal maxDay;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_AVG_DAY)
    private BigDecimal avgDay;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_WORK_DAYS)
    private BigDecimal workDays;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_LOAD_RATE)
    private BigDecimal loadRate;

    @Column(name = ModelConstants.TB_INPUT_PRODUCT_PLAN_COMPLETE_RATE)
    private BigDecimal completeRate;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private String timeStr;

}
