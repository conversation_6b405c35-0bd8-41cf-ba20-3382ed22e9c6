import{_ as v}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{_ as M}from"./CardSearch-CB_HNR-Q.js";import"./index-0NlGN6gS.js";import{g as U}from"./index-D041o0fJ.js";import{I as T}from"./ImportButton-BXPDxx92.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as A,c as h,r as m,l as _,a8 as D,bE as S,b as a,s as F,S as k,o as P,g as B,n as L,q as c,i as d,C as N}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as q}from"./usePartition-DkcY9fQ2.js";import{s as E}from"./printUtils-C-AxhDcd.js";import{P as H,D as V,I as z,d as O,b as G,E as R}from"./userManage-E__vPxsL.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                                                                     */import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const $={class:"wrapper"},j=A({__name:"index",setup(J){const u=q(),f=h(),g=h(),y=h(),w=m({defaultParams:{itemType:"",type:"month",date:_().format()},filters:[{type:"input",label:"用户名称",field:"custName",placeholder:"请输入用户姓名"},{type:"input",label:"营收户号",field:"custCode",placeholder:"请输入营收户号"},{type:"select",field:"waterCategory",width:90,options:[],label:"用水类型",placeholder:"请选择",autoFillOptions:async e=>{const t=await U("WaterCategoryType");e.options=t||[]}},{type:"input",label:"抄表员",field:"copyMeterUser",placeholder:"请输入抄表员"},{type:"input",label:"营收所",field:"businessHall",placeholder:"请输入营收所"},{type:"select-tree",label:"所属分区",field:"partitionId",placeholder:"请选择",options:D(()=>u.Tree.value)},{type:"select",label:"是否挂接",field:"isMount",options:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}],placeholder:"请选择"},{type:"input",label:"地址",field:"address",placeholder:"请输入地址"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>p()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=y.value)==null||e.resetForm()}},{perm:!0,text:"添加",iconifyIcon:"ep:circle-plus",click:()=>b()},{perm:!0,text:"导入",type:"warning",iconifyIcon:"ep:upload",click:()=>{var e;(e=g.value)==null||e.openDialog()}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>p(!0)}]}]}),r=m({indexVisible:!0,dataList:[],columns:[{label:"用户名称",minWidth:120,prop:"custName"},{label:"营收户号",minWidth:120,prop:"custCode"},{label:"联系方式",minWidth:120,prop:"phone"},{label:"用水类型",minWidth:120,prop:"waterCategory"},{label:"册本编号",minWidth:160,prop:"meterBookCode"},{label:"册本名称",minWidth:160,prop:"meterBookName"},{label:"抄表人",minWidth:120,prop:"copyMeterUser"},{label:"地址",minWidth:120,prop:"address"},{label:"营业所",minWidth:120,prop:"businessHall"},{label:"挂接分区",minWidth:140,prop:"partitionName"},{label:"添加日期",minWidth:160,prop:"createTime",formatter:(e,t)=>t?_(t).format(S):""}],operations:[{perm:!0,text:"修改",iconifyIcon:"ep:edit",click:e=>b(e)},{perm:!0,text:"删除",type:"danger",iconifyIcon:"ep:delete",click:e=>x(e)}],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,p()}}}),n=m({dialogWidth:500,title:"营收用户信息",group:[{fields:[{type:"input",label:"营收户号",field:"custCode",rules:[{required:!0,message:"请填写营收户号"}]},{type:"select-tree",label:"所属分区",field:"partitionId",options:D(()=>u.Tree.value)},{type:"input",label:"抄表员",field:"copyMeterUser"},{type:"input",label:"营收所",field:"businessHall"}]}],submit:async e=>{var t;try{n.submitting=!0;const o={...e},i=await H(o);i.data.code===200?(a.success("操作成功"),p(),(t=f.value)==null||t.closeDialog()):a.error(i.data.message)}catch{a.error("操作失败")}n.submitting=!1}}),C=m({dialogWidth:500,title:"导入营收用户信息",group:[{fields:[{type:"btn-group",btns:[{perm:!0,text:"下载导入模板",type:"default",click:()=>V()},{perm:!0,component:F(T),iconifyIcon:"ep:upload",type:"primary",click:e=>{k("将上传数据并进行解析，确定上传？","提示信息").then(async()=>{var t;try{const o=await z(e);o.data.code===200?(a.success("导入成功"),p(),(t=g.value)==null||t.closeDialog()):a.error(o.data.message)}catch{a.error("上传失败")}})}}]}]}],cancel:!1}),b=e=>{var t;n.defaultValue={...e||{}},(t=f.value)==null||t.openDialog()},x=e=>{var o;const t=e?[e.id]:((o=r.selectList)==null?void 0:o.map(i=>i.id))||[];if(!t.length){a.warning("请先选择要删除的数据");return}k("确定删除?","提示信息").then(async()=>{try{await O(t),a.success("删除成功"),p()}catch{a.error("删除失败")}}).catch(()=>{})},p=async e=>{var t,o;r.loading=!0;try{const i=((t=y.value)==null?void 0:t.queryParams)||{},l={page:r.pagination.page||1,size:r.pagination.limit||20,...i},s=(o=(await G(l)).data)==null?void 0:o.data;if(r.dataList=(s==null?void 0:s.data)||[],r.pagination.total=(s==null?void 0:s.total)||0,e){a.warning("导出中...");const I=await R({...l,page:1,size:r.pagination.total||0});E(I.data,"营收用户列表")}}catch{}r.loading=!1};return P(async()=>{p(),u.getTree()}),(e,t)=>{const o=M,i=W,l=v;return B(),L("div",$,[c(o,{ref_key:"refSearch",ref:y,config:d(w)},null,8,["config"]),c(i,{config:d(r),class:"card-table"},null,8,["config"]),c(l,{ref_key:"refForm",ref:f,config:d(n)},null,8,["config"]),c(l,{ref_key:"refUpload",ref:g,config:d(C)},null,8,["config"])])}}}),wt=N(j,[["__scopeId","data-v-4b236705"]]);export{wt as default};
