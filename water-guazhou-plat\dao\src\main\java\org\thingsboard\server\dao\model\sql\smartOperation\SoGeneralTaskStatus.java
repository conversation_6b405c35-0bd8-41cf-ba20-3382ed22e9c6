package org.thingsboard.server.dao.model.sql.smartOperation;

import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

public enum SoGeneralTaskStatus implements NameDisplayableEnum {
    PENDING("待启用"),
    PROCESSING("处理中"),
    COMPLETED("完成"),
    ;

    private final String displayName;

    SoGeneralTaskStatus(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }
}
