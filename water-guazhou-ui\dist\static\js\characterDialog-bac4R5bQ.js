import{C as b,M as I,g as r,h as d,F as o,p as h,q as a,G as c,n as C,aJ as k,aB as v,aK as V,aL as B,I as E,K as L,J as x,L as y}from"./index-r0dFAfgr.js";import{g as D,a as F,b as U}from"./index-CaaU9niG.js";const{$message:N}=I(),R={name:"CharacterDialog",props:["tableConfig"],emits:["refreshData"],data(){return{roleId:"",roleList:[]}},computed:{visible(){return this.tableConfig.visible}},created(){D().then(t=>{console.log(t.data),this.roleList=t.data,F(this.tableConfig.userId).then(e=>{this.roleId=e.data})})},methods:{clickSave(){const t={};t.userId=this.tableConfig.userId,t.roleId=this.roleId,U(t).then(()=>{N({type:"success",message:"操作成功"}),this.$emit("refreshData"),this.tableConfig.close()})}}},S={class:"dialog-footer"};function w(t,e,m,G,s,n){const u=V,f=B,p=E,_=L,i=x,g=y;return r(),d(g,{modelValue:n.visible,"onUpdate:modelValue":e[2]||(e[2]=l=>n.visible=l),title:"角色赋予",width:"30%",class:"alarm-design","close-on-click-modal":!1},{footer:o(()=>[h("span",S,[a(i,{type:"primary",onClick:n.clickSave},{default:o(()=>e[3]||(e[3]=[c("保存")])),_:1},8,["onClick"]),a(i,{onClick:e[1]||(e[1]=l=>m.tableConfig.close())},{default:o(()=>e[4]||(e[4]=[c("取 消")])),_:1})])]),default:o(()=>[a(_,null,{default:o(()=>[a(p,{label:"选择要赋予的角色"},{default:o(()=>[a(f,{modelValue:s.roleId,"onUpdate:modelValue":e[0]||(e[0]=l=>s.roleId=l),placeholder:"请选择角色",class:"item-input-select"},{default:o(()=>[(r(!0),C(v,null,k(s.roleList,l=>(r(),d(u,{key:l.id.id,label:l.name,value:l.id.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}const T=b(R,[["render",w]]);export{T as default};
