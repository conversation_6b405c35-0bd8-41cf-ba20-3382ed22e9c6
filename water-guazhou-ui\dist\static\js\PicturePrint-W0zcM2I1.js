import{d as L,c as P,r as k,s as T,bC as R,b as D,Q as O,g as B,n as G,q as M,i as q,_ as E,bD as I}from"./index-r0dFAfgr.js";import{C as N}from"./AnimatedLinesLayer-B2VbV4jv.js";import{i as U,f as V,c as z,s as Q}from"./FeatureHelper-Da16o0mu.js";import{e as $}from"./GPHelper-fLrvVD-A.js";import"./IdentifyResult-4DxLVhTm.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import{g as j}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as d,i as H}from"./ToolHelper-BiiInOzB.js";import{e as J}from"./ViewHelper-BGCZjxXH.js";import{g as K,a as W}from"./config-DncLSA-r.js";import{v as X}from"./v4-SoommWqA.js";import{m as Y}from"./max-CCqK09y5.js";import"./widget-BcWKanF2.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./index-DeAQQ1ej.js";import"./_baseExtremum-UssVWohW.js";const br=L({__name:"PicturePrint",props:{view:{}},setup(h){const o=h,e={},m=P(),u=k({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"标题"},fields:[{type:"input",field:"title",rules:[{required:!0,message:"请输入标题"}]}]},{fieldset:{desc:"格式"},fields:[{type:"select",field:"type",options:K()}]},{fieldset:{desc:"高级设置"},fields:[{type:"radio",field:"range",label:"出图范围",options:[{label:"当前范围",value:"current"},{label:"绘制范围",value:"draw"}],onChange:t=>_(t)},{type:"input",field:"username",label:"出图人员"},{type:"input",field:"depart",label:"出图单位"},{type:"btn-group",btns:[{perm:!0,svgIcon:T(I),click:()=>b(),text:"打印"}]}]},{id:"print-result",fieldset:{desc:"打印结果"},fields:[{type:"list",data:[],style:{height:"100px"},itemStyle:t=>({textDecoration:t.url?"underline":"none",color:"#00ff00"}),formatter:(t,r)=>t&&t+(r.status==="success"?"（打印完成,点击下载！）":r.status==="printing"?"（正在打印...）":r.status==="failed"?"打印失败!":""),displayField:"title",nodeClick:t=>{t.url&&R(t.url,t.title)}}]}],defaultValue:{range:"current",type:"pdf"}}),x=()=>{var t,r,i,s,p,n;o.view&&(d("crosshair"),(t=e.drawAction)==null||t.destroy(),(r=e.drawer)==null||r.destroy(),e.graphicsLayer=j(o.view,{id:"proint-layer",title:"打印绘制图层"}),e.drawer=H(o.view),e.drawAction=(i=e.drawer)==null?void 0:i.create("rectangle"),(s=e.drawAction)==null||s.on("vertex-add",f),(p=e.drawAction)==null||p.on("cursor-update",f),(n=e.drawAction)==null||n.on("draw-complete",()=>{d("")}))},f=t=>{var p,n,c;(p=o.view)==null||p.graphics.removeAll();const r=(t==null?void 0:t.vertices)||[],i=V(r,(n=o.view)==null?void 0:n.spatialReference),s=i&&z({geometry:i,symbol:Q("polygon")});e.graphic=s,s&&((c=o.view)==null||c.graphics.add(s))},b=async()=>{var p,n,c,w,y,v;if(!o.view||await((p=m.value)==null?void 0:p.Submit())===!1)return;if(((n=m.value)==null?void 0:n.dataForm.range)==="draw"&&!e.graphic){D.warning("请先绘制打印范围");return}const r={id:X(),url:"",status:"printing",title:(c=m.value)==null?void 0:c.dataForm.title},i=(w=u.group.find(a=>a.id==="print-result"))==null?void 0:w.fields[0];i.data.unshift(r);try{const a=((y=m.value)==null?void 0:y.dataForm)||{};a.range==="draw"&&e.graphic&&await J(o.view,e.graphic.geometry.extent,!0);const l=W().find(S=>S.value==="a3-landscape"),F=Y(l==null?void 0:l.data.pageSize)||42,A=(U((v=e.graphic)==null?void 0:v.geometry.extent)||0)/(F/100),C=await $(o.view,new N({format:a.type,exportOptions:{dpi:96},outScale:a.range==="draw"?A:o.view.scale,layout:"a3-landscape",layoutOptions:{titleText:a.title,authorText:a.username,copyrightText:a.depart,scalebarUnit:"Meters",elementOverrides:{"North Arrow":{visible:!0}}},showLabels:!0}));r.status="success",r.url=C.url}catch{r.status="failed"}const s=i.data.findIndex(a=>a.id===r.id);s!==-1&&i.data.splice(s,1),i.data.unshift(r)},g=()=>{var t,r,i;e.graphicsLayer&&((t=o.view)==null||t.map.remove(e.graphicsLayer)),(r=e.drawAction)==null||r.destroy(),(i=e.drawer)==null||i.destroy(),d("")},_=t=>{var r;(r=o.view)==null||r.graphics.removeAll(),e.graphic=void 0,t==="draw"?x():g()};return O(()=>{g()}),(t,r)=>{const i=E;return B(),G("div",null,[M(i,{ref_key:"refForm",ref:m,config:q(u)},null,8,["config"])])}}});export{br as default};
