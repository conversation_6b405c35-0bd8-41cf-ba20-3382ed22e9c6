// 水质分析相关类型定义

// 处理高度选项
export interface TreatmentLevelOption {
  label: string
  value: string
}

// 社区选项
export interface CommunityOption {
  label: string
  value: string
}

// 查询表单数据
export interface QueryForm {
  pageNum: number
  pageSize: number
  treatmentLevel: string
  community: string
  dateRange: string[] | null
  month: string
}

// 水质指标数据
export interface WaterQualityData {
  id?: string
  sampleTime: string // 采样时间
  sampleLocation: string // 采样地点
  weather: string // 天气
  outletCod: number // 出水COD mg/L
  outletBod: number // BOD5 mg/L
  suspendedSolids: number // 悬浮物 mg/L
  ammoniaNitrogen: number // 氨氮 mg/L
  totalNitrogen: number // 总氮 mg/L
  totalPhosphorus: number // 总磷 mg/L
  outletPh: number // 出水PH值
  fecalColiform: number // 粪大肠菌群数 个/L
  createTime?: string
  updateTime?: string
}

// 统计数据
export interface StatisticsData {
  indicator: string // 指标名称
  avgValue: number // 平均值
  maxValue: number // 最大值
  minValue: number // 最小值
  standardValue: number // 标准值
  complianceRate: number // 达标率
}

// 对比数据
export interface CompareData {
  indicator: string // 指标名称
  currentPeriod: number // 当前期间值
  previousPeriod: number // 上一期间值
  changeRate: number // 变化率
  trend: 'up' | 'down' | 'stable' // 趋势
}

// 图表数据
export interface ChartData {
  xAxis: string[] // X轴数据
  series: {
    name: string
    data: number[]
    type: string
  }[]
}

// API响应数据结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应数据
export interface PageResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// 导出参数
export interface ExportParams extends QueryForm {
  exportType: 'excel' | 'pdf'
  fileName?: string
}
