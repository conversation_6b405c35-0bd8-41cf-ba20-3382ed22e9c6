package org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_pump_house_storage")
// 泵房台账
public class PumpHouseStorage {
    // id
    @TableId
    private String id;

    // 泵房编号
    private String code;

    // 泵房名称
    private String name;

    // 泵房简称
    private String nickname;

    // 厂家名称
    private String companyName;

    // 供水方式
    private String supplyMethod;

    // 水箱个数
    private Integer waterBoxNum;

    // 地址
    private String address;

    // 安装人名称
    private String installUserName;

    // 安装时间
    private Date installDate;

    // 采集频率（分钟）
    private Integer collectionFrequency;

    // 存储频率（分钟）
    private Integer storageFrequency;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
