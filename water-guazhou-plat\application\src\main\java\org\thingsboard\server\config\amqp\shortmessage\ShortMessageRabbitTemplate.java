package org.thingsboard.server.config.amqp.shortmessage;

import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.thingsboard.server.dao.dispatch.AmqpTemplateDeliver;

public class ShortMessageRabbitTemplate extends RabbitTemplate implements AmqpTemplateDeliver {
    public ShortMessageRabbitTemplate() {
    }

    public ShortMessageRabbitTemplate(ConnectionFactory connectionFactory) {
        super(connectionFactory);
    }

}
