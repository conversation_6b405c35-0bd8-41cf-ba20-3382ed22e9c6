package org.thingsboard.server.dao.util.imodel.query.deviceDump;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDump;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class DeviceDumpPageRequest extends AdvancedPageableQueryEntity<DeviceDump, DeviceDumpPageRequest> {
    // 报废单编码
    private String code;

    // 报废单标题
    private String name;

    // 保费申请人Id
    private String uploadUserId;

    // 保费申请部门Id
    private String uploadDepartmentId;

    // 经办人Id
    private String handleUserId;

    // 经办部门Id
    private String handleDepartmentId;

    // 创建时间
    private String createTime;

    public Date getCreateTime() {
        return toDate(createTime);
    }
}
