<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.StoreInRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->sin.id,
                           sin.code,
                           sin.title,
                           sin.batch_code,
                           sin.purchase_id,
                           sin.storehouse_id,
                           sin.acceptor,
                           sin.manager,
                           sin.contract_id,
                           sin.invoice_code,
                           sin.supplier_id,
                           sin.in_time,
                           sin.add_record,
                           sin.remark,
                           sin.creator,
                           sin.create_time,
                           sin.tenant_id<!--@sql from store_in_record sin-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.StoreInRecord">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="title" property="title"/>
        <result column="batch_code" property="batchCode"/>
        <result column="purchase_id" property="purchaseId"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="acceptor" property="acceptor"/>
        <result column="manager" property="manager"/>
        <result column="contract_id" property="contractId"/>
        <result column="invoice_code" property="invoiceCode"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="in_time" property="inTime"/>
        <result column="add_record" property="addRecord"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from store_in_record sin
        <where>
            <if test="code != null and code != ''">
                and sin.code = #{code}
            </if>
            <if test="title != null and title != ''">
                and sin.title like '%' || #{title} || '%'
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and sin.invoice_code like '%' || #{invoiceCode} || '%'
            </if>
            <if test="contractCode != null and contractCode != ''">
                and (select count(id) > 0
                     from contract
                     where id = sin.contract_id
                       and code like '%' || #{contractCode} || '%')
            </if>
            <if test="storehouseId != null and storehouseId != ''">
                and sin.storehouse_id = #{storehouseId}
            </if>
            <if test="acceptor != null and acceptor != ''">
                and acceptor = #{acceptor}
            </if>
            <if test="acceptorDepartmentId != null and acceptorDepartmentId != ''">
                and is_user_at_department(sin.acceptor, #{acceptorDepartmentId})
            </if>
            <if test="manager != null and manager != ''">
                and manager = #{manager}
            </if>
            <if test="managerDepartmentId != null and managerDepartmentId != ''">
                and is_user_at_department(sin.manager, #{managerDepartmentId})
            </if>
            <if test="fromTime != null">
                and sin.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and sin.create_time &lt;= #{toTime}
            </if>
            and sin.tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update store_in_record
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="batchCode != null">
                batch_code = #{batchCode},
            </if>
            <if test="purchaseId != null">
                purchase_id = #{purchaseId},
            </if>
            <if test="storehouseId != null">
                storehouse_id = #{storehouseId},
            </if>
            <if test="acceptor != null">
                acceptor = #{acceptor},
            </if>
            <if test="manager != null">
                manager = #{manager},
            </if>
            <if test="contractId != null">
                contract_id = #{contractId},
            </if>
            <if test="invoiceCode != null">
                invoice_code = #{invoiceCode},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="inTime != null">
                in_time = #{inTime},
            </if>
            <if test="addRecord != null">
                add_record = #{addRecord},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getCodeById" resultType="java.lang.String">
        select code
        from store_in_record
        where id = #{id}
    </select>

    <insert id="save">
        INSERT INTO store_in_record(id,
                                    code,
                                    title,
                                    batch_code,
                                    purchase_id,
                                    storehouse_id,
                                    acceptor,
                                    manager,
                                    contract_id,
                                    invoice_code,
                                    supplier_id,
                                    in_time,
                                    add_record,
                                    creator,
                                    create_time,
                                    remark,
                                    tenant_id)
        VALUES (#{id},
                generate_number_reset_different_day_with_date_prefix('store_in_record' || #{tenantId}, 'fm0000', 9999),
                #{title},
                #{batchCode},
                #{purchaseId},
                #{storehouseId},
                #{acceptor},
                #{manager},
                #{contractId},
                #{invoiceCode},
                #{supplierId},
                #{inTime},
                #{addRecord},
                #{creator},
                #{createTime},
                #{remark},
                #{tenantId})
    </insert>
</mapper>