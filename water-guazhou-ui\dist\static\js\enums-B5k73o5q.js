var i,t,o,r,l,a,u,e,c,d,f,s,p,g,C,m,T,S,P,R,B,N,h,O,L,M,A,y,E,D,F,v,x,W,U,k,H,b,w,I,G,q,J,X,Y,Z,K,Q,V,$,j,z,_,nn,tn,on,rn,ln,an,un,en;(function(n){n[n.BUTT=0]="BUTT",n[n.ROUND=1]="ROUND",n[n.SQUARE=2]="SQUARE",n[n.UNKNOWN=4]="UNKNOWN"})(i||(i={})),function(n){n[n.BEVEL=0]="BEVEL",n[n.ROUND=1]="ROUND",n[n.MITER=2]="MITER",n[n.UNKNOWN=4]="UNKNOWN"}(t||(t={})),function(n){n[n.SCREEN=0]="SCREEN",n[n.MAP=1]="MAP"}(o||(o={})),function(n){n[n.Tint=0]="Tint",n[n.Ignore=1]="Ignore",n[n.Multiply=99]="Multiply"}(r||(r={})),function(n){n.Both="Both",n.JustBegin="JustBegin",n.JustEnd="JustEnd",n.None="None"}(l||(l={})),function(n){n[n.Mosaic=0]="Mosaic",n[n.Centered=1]="Centered"}(a||(a={})),function(n){n[n.Normal=0]="Normal",n[n.Superscript=1]="Superscript",n[n.Subscript=2]="Subscript"}(u||(u={})),function(n){n[n.MSSymbol=0]="MSSymbol",n[n.Unicode=1]="Unicode"}(e||(e={})),function(n){n[n.Unspecified=0]="Unspecified",n[n.TrueType=1]="TrueType",n[n.PSOpenType=2]="PSOpenType",n[n.TTOpenType=3]="TTOpenType",n[n.Type1=4]="Type1"}(c||(c={})),function(n){n[n.Display=0]="Display",n[n.Map=1]="Map"}(d||(d={})),function(n){n.None="None",n.Loop="Loop",n.Oscillate="Oscillate"}(f||(f={})),function(n){n[n.Z=0]="Z",n[n.X=1]="X",n[n.Y=2]="Y"}(s||(s={})),function(n){n[n.XYZ=0]="XYZ",n[n.ZXY=1]="ZXY",n[n.YXZ=2]="YXZ"}(p||(p={})),function(n){n[n.Rectangle=0]="Rectangle",n[n.RoundedRectangle=1]="RoundedRectangle",n[n.Oval=2]="Oval"}(g||(g={})),function(n){n[n.None=0]="None",n[n.Alpha=1]="Alpha",n[n.Screen=2]="Screen",n[n.Multiply=3]="Multiply",n[n.Add=4]="Add"}(C||(C={})),function(n){n[n.TTB=0]="TTB",n[n.RTL=1]="RTL",n[n.BTT=2]="BTT"}(m||(m={})),function(n){n[n.None=0]="None",n[n.SignPost=1]="SignPost",n[n.FaceNearPlane=2]="FaceNearPlane"}(T||(T={})),function(n){n[n.Float=0]="Float",n[n.String=1]="String",n[n.Boolean=2]="Boolean"}(S||(S={})),function(n){n[n.Intersect=0]="Intersect",n[n.Subtract=1]="Subtract"}(P||(P={})),function(n){n.OpenEnded="OpenEnded",n.Block="Block",n.Crossed="Crossed"}(R||(R={})),function(n){n.FullGeometry="FullGeometry",n.PerpendicularFromFirstSegment="PerpendicularFromFirstSegment",n.ReversedFirstSegment="ReversedFirstSegment",n.PerpendicularToSecondSegment="PerpendicularToSecondSegment",n.SecondSegmentWithTicks="SecondSegmentWithTicks",n.DoublePerpendicular="DoublePerpendicular",n.OppositeToFirstSegment="OppositeToFirstSegment",n.TriplePerpendicular="TriplePerpendicular",n.HalfCircleFirstSegment="HalfCircleFirstSegment",n.HalfCircleSecondSegment="HalfCircleSecondSegment",n.HalfCircleExtended="HalfCircleExtended",n.OpenCircle="OpenCircle",n.CoverageEdgesWithTicks="CoverageEdgesWithTicks",n.GapExtentWithDoubleTicks="GapExtentWithDoubleTicks",n.GapExtentMidline="GapExtentMidline",n.Chevron="Chevron",n.PerpendicularWithArc="PerpendicularWithArc",n.ClosedHalfCircle="ClosedHalfCircle",n.TripleParallelExtended="TripleParallelExtended",n.ParallelWithTicks="ParallelWithTicks",n.Parallel="Parallel",n.PerpendicularToFirstSegment="PerpendicularToFirstSegment",n.ParallelOffset="ParallelOffset",n.OffsetOpposite="OffsetOpposite",n.OffsetSame="OffsetSame",n.CircleWithArc="CircleWithArc",n.DoubleJog="DoubleJog",n.PerpendicularOffset="PerpendicularOffset",n.LineExcludingLastSegment="LineExcludingLastSegment",n.MultivertexArrow="MultivertexArrow",n.CrossedArrow="CrossedArrow",n.ChevronArrow="ChevronArrow",n.ChevronArrowOffset="ChevronArrowOffset",n.PartialFirstSegment="PartialFirstSegment",n.Arch="Arch",n.CurvedParallelTicks="CurvedParallelTicks",n.Arc90Degrees="Arc90Degrees"}(B||(B={})),function(n){n.Mitered="Mitered",n.Bevelled="Bevelled",n.Rounded="Rounded",n.Square="Square",n.TrueBuffer="TrueBuffer"}(N||(N={})),function(n){n.ClosePath="ClosePath",n.ConvexHull="ConvexHull",n.RectangularBox="RectangularBox"}(h||(h={})),function(n){n.BeginningOfLine="BeginningOfLine",n.EndOfLine="EndOfLine"}(O||(O={})),function(n){n.Mitered="Mitered",n.Bevelled="Bevelled",n.Rounded="Rounded",n.Square="Square"}(L||(L={})),function(n){n.Fast="Fast",n.Accurate="Accurate"}(M||(M={})),function(n){n.BeginningOfLine="BeginningOfLine",n.EndOfLine="EndOfLine"}(A||(A={})),function(n){n.Sinus="Sinus",n.Square="Square",n.Triangle="Triangle",n.Random="Random"}(y||(y={})),function(n){n[n.None=0]="None",n[n.Default=1]="Default",n[n.Force=2]="Force"}(E||(E={})),function(n){n[n.Buffered=0]="Buffered",n[n.Left=1]="Left",n[n.Right=2]="Right",n[n.AlongLine=3]="AlongLine"}(D||(D={})),function(n){n[n.Linear=0]="Linear",n[n.Rectangular=1]="Rectangular",n[n.Circular=2]="Circular",n[n.Buffered=3]="Buffered"}(F||(F={})),function(n){n[n.Discrete=0]="Discrete",n[n.Continuous=1]="Continuous"}(v||(v={})),function(n){n[n.AcrossLine=0]="AcrossLine",n[n.AloneLine=1]="AloneLine"}(x||(x={})),function(n){n[n.Left=0]="Left",n[n.Right=1]="Right",n[n.Center=2]="Center",n[n.Justify=3]="Justify"}(W||(W={})),function(n){n[n.Base=0]="Base",n[n.MidPoint=1]="MidPoint",n[n.ThreePoint=2]="ThreePoint",n[n.FourPoint=3]="FourPoint",n[n.Underline=4]="Underline",n[n.CircularCW=5]="CircularCW",n[n.CircularCCW=6]="CircularCCW"}(U||(U={})),function(n){n.Butt="Butt",n.Round="Round",n.Square="Square"}(k||(k={})),function(n){n.NoConstraint="NoConstraint",n.HalfPattern="HalfPattern",n.HalfGap="HalfGap",n.FullPattern="FullPattern",n.FullGap="FullGap",n.Custom="Custom"}(H||(H={})),function(n){n[n.None=-1]="None",n[n.Custom=0]="Custom",n[n.Circle=1]="Circle",n[n.OpenArrow=2]="OpenArrow",n[n.ClosedArrow=3]="ClosedArrow",n[n.Diamond=4]="Diamond"}(b||(b={})),function(n){n[n.ExtraLeading=0]="ExtraLeading",n[n.Multiple=1]="Multiple",n[n.Exact=2]="Exact"}(w||(w={})),function(n){n.Bevel="Bevel",n.Round="Round",n.Miter="Miter"}(I||(I={})),function(n){n[n.Default=0]="Default",n[n.String=1]="String",n[n.Numeric=2]="Numeric"}(G||(G={})),function(n){n[n.InsidePolygon=0]="InsidePolygon",n[n.PolygonCenter=1]="PolygonCenter",n[n.RandomlyInsidePolygon=2]="RandomlyInsidePolygon"}(q||(q={})),function(n){n[n.Tint=0]="Tint",n[n.Replace=1]="Replace",n[n.Multiply=2]="Multiply"}(J||(J={})),function(n){n[n.ClipAtBoundary=0]="ClipAtBoundary",n[n.RemoveIfCenterOutsideBoundary=1]="RemoveIfCenterOutsideBoundary",n[n.DoNotTouchBoundary=2]="DoNotTouchBoundary",n[n.DoNotClip=3]="DoNotClip"}(X||(X={})),function(n){n.NoConstraint="NoConstraint",n.WithMarkers="WithMarkers",n.WithFullGap="WithFullGap",n.WithHalfGap="WithHalfGap",n.Custom="Custom"}(Y||(Y={})),function(n){n.Fixed="Fixed",n.Random="Random",n.RandomFixedQuantity="RandomFixedQuantity"}(Z||(Z={})),function(n){n.LineMiddle="LineMiddle",n.LineBeginning="LineBeginning",n.LineEnd="LineEnd",n.SegmentMidpoint="SegmentMidpoint"}(K||(K={})),function(n){n.OnPolygon="OnPolygon",n.CenterOfMass="CenterOfMass",n.BoundingBoxCenter="BoundingBoxCenter"}(Q||(Q={})),function(n){n[n.Low=0]="Low",n[n.Medium=1]="Medium",n[n.High=2]="High"}(V||(V={})),function(n){n[n.MarkerCenter=0]="MarkerCenter",n[n.MarkerBounds=1]="MarkerBounds"}($||($={})),function(n){n[n.None=0]="None",n[n.PropUniform=1]="PropUniform",n[n.PropNonuniform=2]="PropNonuniform",n[n.DifUniform=3]="DifUniform",n[n.DifNonuniform=4]="DifNonuniform"}(j||(j={})),function(n){n.Tube="Tube",n.Strip="Strip",n.Wall="Wall"}(z||(z={})),function(n){n[n.Random=0]="Random",n[n.Increasing=1]="Increasing",n[n.Decreasing=2]="Decreasing",n[n.IncreasingThenDecreasing=3]="IncreasingThenDecreasing"}(_||(_={})),function(n){n[n.Relative=0]="Relative",n[n.Absolute=1]="Absolute"}(nn||(nn={})),function(n){n[n.Normal=0]="Normal",n[n.LowerCase=1]="LowerCase",n[n.Allcaps=2]="Allcaps"}(tn||(tn={})),function(n){n[n.LTR=0]="LTR",n[n.RTL=1]="RTL"}(on||(on={})),function(n){n.Draft="Draft",n.Picture="Picture",n.Text="Text"}(rn||(rn={})),function(n){n[n.Top=0]="Top",n[n.Center=1]="Center",n[n.Baseline=2]="Baseline",n[n.Bottom=3]="Bottom"}(ln||(ln={})),function(n){n[n.Right=0]="Right",n[n.Upright=1]="Upright"}(an||(an={})),function(n){n[n.Small=0]="Small",n[n.Medium=1]="Medium",n[n.Large=2]="Large"}(un||(un={})),function(n){n[n.Calm=0]="Calm",n[n.Rippled=1]="Rippled",n[n.Slight=2]="Slight",n[n.Moderate=3]="Moderate"}(en||(en={}));export{y as A,N as B,m as C,Q as K,L as O,R as P,B as R,k as U,Y as X,Z as Y,K as Z,e as a,on as b,an as c,f as d,i as e,rn as f,o as i,H as k,u as l,T as m,t as n,l as o,c as u,I as w,E as y};
