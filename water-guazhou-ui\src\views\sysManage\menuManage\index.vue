<template>
  <TreeBox>
    <template #tree>
      <SLTree ref="refTree" :tree-data="TreeConfig" />
    </template>
    <SLCard class="card" :title="state.title">
      <template #title>
        <span class="title">{{ state.title }}</span>
        <ExportButton></ExportButton>
        <ImportButton></ImportButton>
      </template>
      <AouForm
        :menu-root-id="state.rootId"
        :sources="sources.options.value"
        :menu="state.menu"
        :menus="TreeConfig.data"
        :menu-parent-id="state.parentId"
        @success="refreshTree"
      ></AouForm>
    </SLCard>
  </TreeBox>
</template>

<script lang="ts" setup>
import { useSourceOptions } from '@/hooks/useSource';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { formatTree } from '@/utils/GlobalHelper';
import { GetMenuTree, GetMenuRootId, DeleteMenu } from '@/api/menu/source';
import AouForm from './components/AouForm.vue';
import ExportButton from './components/ExportButton.vue';
import ImportButton from './components/ImportButton.vue';
import TreeBox from '@/views/layout/treeOrDetailFrame/TreeBox.vue';

const refTree = ref<ISLTreeIns>();
const state = reactive<{
  title: string;
  rootId: string;
  parentId: string;
  menu: NormalOption | undefined;
}>({
  title: '菜单管理',
  rootId: '',
  parentId: '',
  menu: undefined
});
const TreeConfig = reactive<SLTreeConfig>({
  title: '菜单列表',
  data: [],
  add: {
    text: '添加',
    perm: true,
    click: (data?: INormalOption) => {
      if (data) TreeConfig.currentProject = data;
      if (!TreeConfig.currentProject) {
        SLMessage.warning('请先选择一个菜单');
        return;
      }
      state.title = '添加子级菜单';
      state.parentId = data?.value;
      // 添加操作置空当前菜单
      state.menu = undefined;
    }
  },
  addRoot: {
    perm: true,
    text: '添加根级菜单',
    click: async () => {
      state.title = '添加根级菜单';
      state.parentId = state.rootId;
      state.menu = undefined;
    }
  },
  edit: {
    perm: true,
    text: '编辑',
    click: (data?: INormalOption) => {
      if (data) TreeConfig.currentProject = data;
      if (!TreeConfig.currentProject) {
        SLMessage.warning('请先选择一个菜单');
        return;
      }
      state.title = '编辑菜单';
      state.parentId = '';
      state.menu = data;
    }
  },
  delete: {
    perm: true,
    // disabled: true,
    text: '删除',
    click: (row: NormalOption) => {
      SLConfirm('确定删除？', '删除提示')
        .then(async () => {
          const ids = row.id?.split(',') || [];
          if (!ids.length) {
            SLMessage.warning('请先选择要删除的数据');
            return;
          }
          const res = await DeleteMenu(ids);
          if (res.data.code === 200) {
            SLMessage.success('删除成功');
            refreshTree();
          } else {
            SLMessage.error(res.data.message || '删除失败');
          }
        })
        .catch(() => {
          //
        });
    }
  },
  loading: false,
  isFilterTree: true,
  expandNodeId: [],
  defaultProps: {
    children: 'children',
    label: 'name'
  },
  defaultExpandAll: false,
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: INormalOption) => {
    TreeConfig.currentProject = data;
    state.parentId = '';
    state.menu = data;
  }
});
const refreshTree = async () => {
  const res = await GetMenuTree();
  const data = res.data?.filter((item) => item.type !== '2') || [];
  const treeData = formatTree(
    data,
    {
      id: 'id',
      value: 'id',
      label: 'label',
      children: 'children'
    },
    undefined
  );
  TreeConfig.data = treeData;
};

const sources = useSourceOptions();
onMounted(async () => {
  refreshTree();
  GetMenuRootId().then((res) => {
    state.rootId = res.data?.id;
  });
  sources.init();
});
</script>

<style lang="scss" scoped>
.card {
  height: 100%;
}

.title {
  margin-right: auto;
}
</style>
