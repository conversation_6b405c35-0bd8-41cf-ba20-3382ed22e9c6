package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.dao.model.sql.VideoEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 呼叫来源报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-01-03
 */
@Data
@NoArgsConstructor
public class VideoSaveDTO {

    private String projectId;

    private String groupId;

    private List<VideoEntity> videoList = new ArrayList<>();

}
