import Expand from '@arcgis/core/widgets/Expand';
import Search from '@arcgis/core/widgets/Search';
import Sketch from '@arcgis/core/widgets/Sketch';
import Print from '@arcgis/core/widgets/Print';
import Swipe from '@arcgis/core/widgets/Swipe';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import TileLayer from '@arcgis/core/layers/TileLayer';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';

export const useWidgets = () => {
  /**
   * 搜索
   * @param view
   * @param widgetPosition
   */
  const initSearch = (view: __esri.MapView, widgetPosition?: string) => {
    const search = new Search({
      view
    });
    view.ui?.add(search, widgetPosition || 'top-right');
  };

  /**
   * Sketch
   * @param view
   * @param widgetPosition
   */
  const initSketch = (view: __esri.MapView, widgetPosition?: string) => {
    const layer = new GraphicsLayer();
    view.map.add(layer);
    const sketch = new Sketch({
      view,
      layer,
      creationMode: 'update'
    });
    view.ui?.add(sketch, widgetPosition || 'top-right');
  };
  const initScaleBar = (view: __esri.MapView, widgetPosition?: string) => {
    const scaleBar = new ScaleBar({
      view,
      unit: 'metric',
      style: 'ruler'
    });
    view.ui?.add(scaleBar, widgetPosition || 'bottom-left');
  };
  /**
   * 打印
   * @param view 视图
   * @param url 打印服务地址
   * @param widgetPosition toolbar放置的位置
   */
  const initPrint = (
    view: __esri.MapView,
    url?: string,
    widgetPosition?: string
  ) => {
    const print = new Print({
      view,
      printServiceUrl: url
    });
    const prExpand = new Expand({
      view,
      content: print,
      expandTooltip: '打印'
    });
    view.ui?.add(prExpand, widgetPosition || 'top-right');
  };
  /**
   * 卷帘
   * @param view
   * @param options
   * @returns
   */
  const initSwipe = (
    view: __esri.MapView,
    options: {
      leadingLayers?: any[];
      tailingLayers?: any[];
      /**
       * default horizontal
       */
      direction?: 'horizontal' | 'vertical';
      /**
       * set position of widget to 35%
       */
      position: number;
    }
  ) => {
    const swipe = new Swipe({
      ...(options || {}),
      view,
      leadingLayers: options.leadingLayers || [
        new TileLayer({
          url:
            window.SITE_CONFIG.GIS_CONFIG.gisService +
            window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
        })
      ],
      trailingLayers: options.tailingLayers || [
        new TileLayer({
          url:
            window.SITE_CONFIG.GIS_CONFIG.gisService +
            window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
        })
      ],
      direction: options.direction || 'horizontal'
    });
    view.ui?.add(swipe);
    return swipe;
  };
  const addCustomWidget = (
    view?: __esri.MapView,
    id?: string,
    widgetPosition?: string
  ) => {
    id && view?.ui.add(id, widgetPosition || 'top-right');
  };
  const removeCustomWidget = (view?: __esri.MapView, id?: string) => {
    id && view?.ui.remove(id);
  };
  return {
    initSearch,
    initSketch,
    initPrint,
    initSwipe,
    addCustomWidget,
    removeCustomWidget,
    initScaleBar
  };
};
export default useWidgets;
