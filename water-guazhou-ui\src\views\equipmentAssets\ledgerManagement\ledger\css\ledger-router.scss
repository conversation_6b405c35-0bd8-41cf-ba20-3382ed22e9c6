.content {
  background-color: #131624;
  color: #FFFFFF;
}

.info-container {
  margin-right: 20px;
}

.dialog-title {
  cursor: pointer;
}

.sl-device-card {
  width: 100%;
  height: 100%;
  box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.5);
}

.tableTitle {
  position: relative;
  margin: 0 auto;
  width: 100%;
  height: 1px;
  margin-top: 10px;
  background-color: #33374D;
  text-align: center;
  font-size: 16px;
  color: rgba(101, 101, 101, 1);
}

.midText {
  position: absolute;
  left: 50%;
  background-color: #0aad87;
  padding: 0 15px;
  transform: translateX(-50%) translateY(-50%);
}

.db-item-size {
  width: 760px;
  margin-top: 20px;
}

.select-size {
  width: 330px;
}

.item-size {
  height: 80px;
  width: 330px;
  margin-top: 20px;
}

.item-left {
  margin-left: 90px;
}

input {
  height: 48px;
}



.add-btn-left {
  border-radius: 8px;
  margin-left: 1400px;
}

.add-btn-left2 {
  border-radius: 8px;
  margin-left: 1360px;
}

.add-btn-left3 {
  border-radius: 8px;
  margin-left: 1350px;
}

.add-btn {
  color: #fff;
  line-height: 35px;
  height: 30px;
  padding: 5px 30px;
  font-size: 16px;
  background-color: #2A96D5FF;
  border: 0px;
  margin-bottom: 10px;
}

.btn-sub-style {
  margin-left: 20px;
  width: 120px;
  border-radius: 2px;
  height: 40px;
  background: linear-gradient(90deg, #2980B9 0%, #6DD5FA 100%)
}

.title-style {
  height: 52px;
  line-height: 52px;
  //background-color: #ffffff;
  margin-top: 20px;
}

.icon-style {
  font-size: 18px;
  font-weight: bolder
}

.label-style {
  font-size: 18px;
  font-weight: bolder;
  margin-left: 8px
}

.div-form-style {
  height: 810px; //这个值可能有不同
  //background-color: #FFFFFF
}

.el-button {
  &.add-btn {
    height: 25px;
    line-height: 21px;
    padding: 2px 8px;
    min-height: 25px;
  }
}

.from-margin {
  margin-left: 25px
}

.cardTable-style {
  margin-left: 50px;
  width: 1448px;
  height: 500px
}

.btn-style {
  padding-right: 20px;
  margin-top: 15px;
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: flex-end;
  align-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  text-align: left;

  .button {
    flex-basis: 140px;
  }

  //background-color: #FFFFFF
}

.btn-confirm-style,
.btn-cancel-style {
  width: 120px;
  height: 40px;
  margin-left: 20px;
  border-radius: 2px;
}

.btn-confirm-style {
  border-width: 0;
  background: linear-gradient(90deg, #2980b9 0%, #6dd5fa 100%) !important;
}

// .btn-left {
//   margin-left: 100px;
//   margin-top: 30px;
// }

.btn-cancel-style {
  border: 1px solid lightgray;
}

.table-height {
  height: 350px
}