package org.thingsboard.server.dao.base.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseBuildWebService;
import org.thingsboard.server.dao.model.sql.base.BaseBuildWeb;
import org.thingsboard.server.dao.sql.base.BaseBuildWebMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseBuildWebPageRequest;

import java.util.List;

/**
 * 平台管理-Web搭建Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class BaseBuildWebServiceImpl implements IBaseBuildWebService {

    @Autowired
    private BaseBuildWebMapper baseBuildWebMapper;

    /**
     * 查询平台管理-Web搭建
     *
     * @param id 平台管理-Web搭建主键
     * @return 平台管理-Web搭建
     */
    @Override
    public BaseBuildWeb selectBaseBuildWebById(String id) {
        return baseBuildWebMapper.selectBaseBuildWebById(id);
    }

    /**
     * 查询平台管理-Web搭建列表
     *
     * @param baseBuildWeb 平台管理-Web搭建
     * @return 平台管理-Web搭建
     */
    @Override
    public IPage<BaseBuildWeb> selectBaseBuildWebList(BaseBuildWebPageRequest baseBuildWeb) {
        return baseBuildWebMapper.selectBaseBuildWebList(baseBuildWeb);
    }

    /**
     * 新增平台管理-Web搭建
     *
     * @param baseBuildWeb 平台管理-Web搭建
     * @return 结果
     */
    @Override
    public int insertBaseBuildWeb(BaseBuildWeb baseBuildWeb) {
        return baseBuildWebMapper.insertBaseBuildWeb(baseBuildWeb);
    }

    /**
     * 修改平台管理-Web搭建
     *
     * @param baseBuildWeb 平台管理-Web搭建
     * @return 结果
     */
    @Override
    public int updateBaseBuildWeb(BaseBuildWeb baseBuildWeb) {
        return baseBuildWebMapper.updateBaseBuildWeb(baseBuildWeb);
    }

    /**
     * 批量删除平台管理-Web搭建
     *
     * @param ids 需要删除的平台管理-Web搭建主键
     * @return 结果
     */
    @Override
    public int deleteBaseBuildWebByIds(List<String> ids) {
        return baseBuildWebMapper.deleteBaseBuildWebByIds(ids);
    }

    /**
     * 删除平台管理-Web搭建信息
     *
     * @param id 平台管理-Web搭建主键
     * @return 结果
     */
    @Override
    public int deleteBaseBuildWebById(String id) {
        return baseBuildWebMapper.deleteBaseBuildWebById(id);
    }
}
