package org.thingsboard.server.dao.sql.logicalFlow;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.LogicalFlowNodeHistory;

import java.util.List;

public interface LogicalFlowNodeHistoryRepository extends JpaRepository<LogicalFlowNodeHistory, String> {
    List<LogicalFlowNodeHistory> findByHistoryIdOrderByCreatedTime(String historyId);

    void deleteByLogicalFlowId(String id);
}
