<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.supplier.SupplierMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.SupplierEntity">
        select a.*,u.first_name as creatorName, dt.name as deviceTypeName
        from tb_supplier a
            left join tb_user u on a.creator = u.id
            left join m_device_type dt on a.device_type_id = dt.id
        where
        a.name like '%'||#{name}||'%'
        and a.address like '%'||#{address}||'%'
          and a.status like '%'||#{status}||'%'
          and a.importance like '%'||#{importance}||'%'
          and a.tenant_id = #{tenantId}
        order by a.create_time desc
       offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*) from tb_supplier a
        where
            a.name like '%'||#{name}||'%'
          and a.address like '%'||#{address}||'%'
          and a.status like '%'||#{status}||'%'
          and a.importance like '%'||#{importance}||'%'
          and a.tenant_id = #{tenantId}
    </select>

    <select id="getNameById" resultType="java.lang.String" useCache="true">
        select "name"
        from tb_supplier
        where id=#{id}
    </select>
</mapper>