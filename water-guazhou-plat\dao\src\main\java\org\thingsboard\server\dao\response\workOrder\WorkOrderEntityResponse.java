package org.thingsboard.server.dao.response.workOrder;

import lombok.Data;

import java.util.List;

@Data
@Deprecated
public class WorkOrderEntityResponse {
    // 工单ID
    private String id;

    // 工单单号唯一。两部分构成：1,日期; 2,递增号码。如：202207290001
    private String serialNo;

    // 标题
    private String title;

    // 来源
    private String source;

    // 发起人
    private String organizerId;

    // 紧急程度
    private String level;

    // 工单类型
    private String type;

    // 地址
    private String address;

    // 描述/备注
    private String remark;

    // 现场视频，多个用逗号分隔（最多2）
    private String videoUrl;

    // 现场音频，多个用逗号分隔（最多2）
    private String audioUrl;

    // 现场图片，多个用逗号分隔
    private String imgUrl;

    // 其他附件，多个用逗号分隔（最多2）
    private String otherFileUrl;

    // 上报人
    private String uploadUserId;

    // 上报人电话
    private String uploadPhone;

    // 上报人户号
    private String uploadNo;

    // 上报人地址
    private String uploadAddress;

    // 是否直接派发
    private boolean isDirectDispatch;

    // 处理人
    private String processUserId;

    // 处理级别
    private String processLevel;

    // 预计完成时间
    private String estimatedFinishTime;

    // 完成时间
    private String completeTime;

    // 当前状态
    private String status;

    // 当前状态显示名
    private String statusName;

    // 当前步骤处理人
    private String stepProcessUserId;

    // 创建时间
    private String createTime;

    // 最后更新时间
    private String updateTime;

    // 项目ID
    private String projectId;

    // 租户ID
    private String tenantId;

    private List<WorkOrderDetailEntityResponse> details;
}
