package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractAmend;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoConstructionContractAmendPageRequest extends AdvancedPageableQueryEntity<SoConstructionContractAmend, SoConstructionContractAmendPageRequest> {
    // 所属合同编号
    @NotNullOrEmpty
    private String contractCode;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

}
