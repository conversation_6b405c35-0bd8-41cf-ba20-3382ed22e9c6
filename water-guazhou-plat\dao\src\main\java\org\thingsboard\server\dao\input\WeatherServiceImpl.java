package org.thingsboard.server.dao.input;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.Weather;
import org.thingsboard.server.dao.sql.input.WeatherMapper;

import java.util.List;

@Slf4j
@Service
public class WeatherServiceImpl implements WeatherService {

    @Autowired
    private WeatherMapper weatherMapper;

    @Override
    public PageData<Weather> findList(int page, int size, String time, TenantId tenantId) {
        Page<Weather> pageRequest = new Page<>(page, size);
        IPage<Weather> pageResult = weatherMapper.findList(pageRequest, time, UUIDConverter.fromTimeUUID(tenantId.getId()));

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    @Transactional
    public void save(Weather weather) {
        // 先删除
        QueryWrapper<Weather> deleteQueryWrapper = new QueryWrapper<>();
        deleteQueryWrapper.eq("date", weather.getDate()).eq("tenant_id", weather.getTenantId());
        weatherMapper.delete(deleteQueryWrapper);

        // 保存
        weatherMapper.insert(weather);
    }

    @Override
    public void remove(List<String> ids) {
        weatherMapper.deleteBatchIds(ids);
    }
}
