import{d as h,c as s,r as l,o as k,g as v,n as x,q as t,i as e,t as y,p as C,_ as V,aq as B,C as F}from"./index-r0dFAfgr.js";import{C as w}from"./index-CcDafpIP.js";const M={class:"onemap-panel-wrapper"},T={class:"table-box"},q=h({__name:"install",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(r,{emit:i}){const p=i,c=r,d=s(),o=s([{label:"0 %",value:"报装总数"},{label:"0 %",value:"完成率"}]),m=l({indexVisible:!0,dataList:[],pagination:{hide:!0},columns:[{label:"项目编号",prop:"key1",width:90},{label:"名称",prop:"key2"},{label:"流程",prop:"key3"}],handleRowClick:a=>{p("highlightMark",c.menu,a.id)}}),u=l({group:[{fields:[{type:"input",field:"layer",appendBtns:[{perm:!0,isTextBtn:!0,text:"刷新",click:()=>_()}]}]}],labelPosition:"top",gutter:12}),_=()=>{};return k(()=>{}),(a,n)=>{const f=V,g=B;return v(),x("div",M,[t(e(w),{modelValue:e(o),"onUpdate:modelValue":n[0]||(n[0]=b=>y(o)?o.value=b:null),span:12,style:{"margin-bottom":"10px"}},null,8,["modelValue"]),t(f,{ref_key:"refForm",ref:d,config:e(u)},null,8,["config"]),C("div",T,[t(g,{config:e(m)},null,8,["config"])])])}}}),D=F(q,[["__scopeId","data-v-4dd9955d"]]);export{D as default};
