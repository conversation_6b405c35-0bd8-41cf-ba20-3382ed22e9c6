package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * 核算周期配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_CUST_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipePartitionCust {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_PARTITION_ID)
    private String partitionId;

    @TableField(exist = false)
    private String partitionName;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_CUST_CODE)
    private String custCode;

    @TableField(exist = false)
    private String custName;

    @TableField(exist = false)
    private String phone;

    @TableField(exist = false)
    private String WaterCategory;

    @TableField(exist = false)
    private String meterBookCode;

    @TableField(exist = false)
    private String meterBookName;

    @TableField(exist = false)
    private String address;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_BUSINESS_HALL)
    private String businessHall;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_COPY_METER_USER)
    private String copyMeterUser;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(exist = false)
    private String createTimeStr;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
}
