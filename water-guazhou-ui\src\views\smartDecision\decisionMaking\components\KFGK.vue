<template>
  <div class="gwgk-info">
    <RoundTargetItem
      v-for="(item, i) in state.info"
      :key="i"
      :config="item"
      :class="item.className"
    />
    <img
      class="circle-image"
      src="../../imgs/compound.png"
      alt=""
    />
    <div class="prosale-ratio">
      <div class="value">
        0.00 %
      </div>
      <div class="text">
        满意率
      </div>
    </div>
  </div>
  <ScrollList :data="state.data"></ScrollList>
</template>
<script lang="ts" setup>
import RoundTargetItem from './RoundTargetItem.vue'
import ScrollList from './ScrollList.vue'

const state = reactive<{
  info: ITargetItem[]
  data: any[]
}>({
  data: [{ name: '接线率', value: '0', scale: '0.0%' }],
  info: [
    {
      value: '0',
      label: '年累计服务量(个)',
      scale: '1%',
      text: '同比',
      status: 'down',
      rows: [1, 3],
      className: 'total-server'
    },
    {
      value: '34,683',
      label: '微服务关注量(个)',
      rows: [1],
      className: 'micro-server'
    },
    {
      value: '0',
      label: '全年最高日服务量(个)',
      scale: '0%',
      text: '同比',
      status: 'up',
      rows: [1, 3],
      className: 'max-daily-server'
    },
    {
      value: '100.00%',
      label: '营商环境评价(个)',
      rows: [1],
      className: 'sale-env-assess'
    }
  ]
})
</script>
<style lang="scss" scoped>
.gwgk-info {
  position: relative;
  width: 100%;
  height: 290px;
  .total-server {
    position: absolute;
    top: 20px;
    left: 0;
  }
  .micro-server {
    position: absolute;
    top: 20px;
    right: 0;
  }
  .max-daily-server {
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .sale-env-assess {
    position: absolute;
    bottom: 20px;
    right: 0;
  }
  .circle-image {
    position: absolute;
    width: 40%;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
  .prosale-ratio {
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    .value {
      font-size: 22px;
      line-height: 30px;
    }
    .text {
      font-size: 14px;
    }
  }
}
</style>
