package org.thingsboard.server.dao.optionLog;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.base.ApiPerformance;
import org.thingsboard.server.dao.sql.optionLog.ApiPerformanceMapper;
import org.thingsboard.server.dao.util.imodel.query.base.ApiPerformancePageRequest;

/**
 * 公共平台-服务监控Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class ApiPerformanceServiceImpl implements IApiPerformanceService {

    @Autowired
    private ApiPerformanceMapper apiPerformanceMapper;

    /**
     * 查询公共平台-服务监控
     *
     * @param id 公共平台-服务监控主键
     * @return 公共平台-服务监控
     */
    @Override
    public ApiPerformance selectApiPerformanceById(String id) {
        return apiPerformanceMapper.selectApiPerformanceById(id);
    }

    /**
     * 查询公共平台-服务监控列表
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 公共平台-服务监控
     */
    @Override
    public IPage<ApiPerformance> selectApiPerformanceList(ApiPerformancePageRequest apiPerformance) {
        return apiPerformanceMapper.selectApiPerformanceList(apiPerformance);
    }

    /**
     * 新增公共平台-服务监控
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 结果
     */
    @Override
    public int insertApiPerformance(ApiPerformance apiPerformance) {
        apiPerformance.setId(UUID.randomUUID().toString().replace("-", ""));
        return apiPerformanceMapper.insertApiPerformance(apiPerformance);
    }

    /**
     * 修改公共平台-服务监控
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 结果
     */
    @Override
    public int updateApiPerformance(ApiPerformance apiPerformance) {
        return apiPerformanceMapper.updateApiPerformance(apiPerformance);
    }

    /**
     * 批量删除公共平台-服务监控
     *
     * @param ids 需要删除的公共平台-服务监控主键
     * @return 结果
     */
    @Override
    public int deleteApiPerformanceByIds(List<String> ids) {
        return apiPerformanceMapper.deleteApiPerformanceByIds(ids);
    }

    /**
     * 删除公共平台-服务监控信息
     *
     * @param id 公共平台-服务监控主键
     * @return 结果
     */
    @Override
    public int deleteApiPerformanceById(String id) {
        return apiPerformanceMapper.deleteApiPerformanceById(id);
    }
}
