package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoDeviceItemPageRequest extends AdvancedPageableQueryEntity<SoDeviceItem, SoDeviceItemPageRequest> {
    // ignore 所属作用域
    private SoGeneralSystemScope scope;

    // ignore 所属标识（id、code等），优先code
    private String identifier;

    // 设备编码
    private String serialId;

    // 设备名称
    private String name;

    // 设备型号
    private String model;

    // 设备编码
    private boolean withCode;

    public SoGeneralSystemScope getProjectScopeIdentifier() {
        return SoGeneralSystemScope.SO_PROJECT;
    }

    public SoGeneralSystemScope getConstructionScopeIdentifier() {
        return SoGeneralSystemScope.SO_CONSTRUCTION;
    }

    public SoGeneralSystemScope getContractScopeIdentifier() {
        return SoGeneralSystemScope.SO_CONSTRUCTION_CONTRACT;
    }

    public SoGeneralSystemScope getApplyScopeIdentifier() {
        return SoGeneralSystemScope.SO_CONSTRUCTION_APPLY;
    }

    public void withCode() {
        withCode = true;
    }

    public void withoutCode() {
        withCode = false;
    }
}
