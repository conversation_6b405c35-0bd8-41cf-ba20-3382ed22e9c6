import{b as K,m as at}from"./WGLContainer-Dyx9110G.js";import{glslifyDefineMap as lt,ProgramCache as ot}from"./webglDeps-BRs4ClOS.js";import{cl as X,R as S,a4 as Y,T as b,b2 as ht,$ as q}from"./index-r0dFAfgr.js";import{i as _t,ay as ut,aq as ct}from"./Point-WxyopZva.js";import{E as z,a as m}from"./Texture-BYqObwfn.js";import{bO as T,ag as Z}from"./MapView-DaoQedLH.js";import{R as C,T as H,N as A,S as nt,I as k,O as v,t as P,Y as N,V as y,M as R,D as O,P as g,G as p,L as x,F as D,A as a,E,C as W,U as ft,_ as U,X as V,n as d,W as dt,r as Q,f as Et}from"./enums-BDQrMlcz.js";import{o as J,W as gt,s as pt,M as Ft}from"./OrderIndependentTransparency-C5Ap76ew.js";import{x as w,E as I,f as $,i as tt}from"./FramebufferObject-8j9PRuxE.js";import{r as bt}from"./floatRGBA-PQQNbO39.js";import{t as et}from"./doublePrecisionUtils-B0owpBza.js";import{t as j}from"./VertexElementDescriptor-BOD-G50G.js";import{r as Tt,n as B,T as Rt}from"./webgl-debug-BJuvLAW9.js";const Bt=e=>{let t="";t+=e[0].toUpperCase();for(let s=1;s<e.length;s++){const i=e[s];i===i.toUpperCase()?(t+="_",t+=i):t+=i.toUpperCase()}return t},mt=e=>{const t={};for(const s in e)t[Bt(s)]=e[s];return lt(t)},re=(e,t,s,i)=>{const r=e+e.substring(e.lastIndexOf("/")),n=t+t.substring(t.lastIndexOf("/")),h=mt(i);return{attributes:s,shaders:{vertexShader:h+K(`${r}.vert`),fragmentShader:h+K(`${n}.frag`)}}};let st=class{constructor(){this.blend=!1,this.blendColor={r:0,g:0,b:0,a:0},this.blendFunction={srcRGB:C.ONE,dstRGB:C.ZERO,srcAlpha:C.ONE,dstAlpha:C.ZERO},this.blendEquation={mode:H.ADD,modeAlpha:H.ADD},this.colorMask={r:!0,g:!0,b:!0,a:!0},this.faceCulling=!1,this.cullFace=A.BACK,this.frontFace=nt.CCW,this.scissorTest=!1,this.scissorRect={x:0,y:0,width:0,height:0},this.depthTest=!1,this.depthFunction=k.LESS,this.clearDepth=1,this.depthWrite=!0,this.depthRange={zNear:0,zFar:1},this.viewport=null,this.stencilTest=!1,this.polygonOffsetFill=!1,this.polygonOffset=[0,0],this.stencilFunction={face:A.FRONT_AND_BACK,func:k.ALWAYS,ref:0,mask:1},this.clearStencil=0,this.stencilWriteMask=1,this.stencilOperation={face:A.FRONT_AND_BACK,fail:v.KEEP,zFail:v.KEEP,zPass:v.KEEP},this.clearColor={r:0,g:0,b:0,a:0},this.program=null,this.vertexBuffer=null,this.indexBuffer=null,this.uniformBuffer=null,this.pixelPackBuffer=null,this.pixelUnpackBuffer=null,this.copyReadBuffer=null,this.copyWriteBuffer=null,this.uniformBufferBindingPoints=new Array,this.readFramebuffer=null,this.drawFramebuffer=null,this.renderbuffer=null,this.activeTexture=0,this.textureUnitMap=new Array,this.vertexArrayObject=null}};class xt{constructor(t){this._allocations=new Map,t?Error.stackTraceLimit=1/0:(this.add=()=>{},this.remove=()=>{})}add(t){this._allocations.set(t,new Error().stack)}remove(t){this._allocations.delete(t)}get information(){let t="";if(this._allocations.size>0){t+=`${this._allocations.size} live object allocations:
`;const s=new Map;this._allocations.forEach(i=>{s.set(i,(s.get(i)??0)+1)}),s.forEach((i,r)=>{const n=r.split(`
`);n.shift(),n.shift(),t+=`${i}: ${n.shift()}
`,n.forEach(h=>t+=`   ${h}
`)})}return t}}const At={RECORD_ALLOCATIONS:!1};class Ot{constructor(){for(this._current=new Array,this._max=new Array,this._allocations=new xt(At.RECORD_ALLOCATIONS);this._current.length<P.COUNT;)this._current.push(0),this._max.push(0)}resetMax(){for(this._max.length=0;this._max.length<this._current.length;)this._max.push(0)}increment(t,s){const i=++this._current[t];this._max[t]=Math.max(i,this._max[t]),this._allocations.add(s)}decrement(t,s){--this._current[t],this._allocations.remove(s)}get max(){return this._max}get current(){return this._current}get total(){return this.current.reduce((t,s)=>t+s,0)}get resourceInformation(){let t="";if(this.total>0){t+=`Live objects:
`;for(let s=0;s<P.COUNT;++s){const i=this._current[s];i>0&&(t+=`${P[s]}: ${i}
`)}}return t+=this._allocations.information,t}}let G=class{constructor(){this._result=!1}dispose(){this._program=X(this._program)}get result(){return S(this._program)&&(this._result=this._test(this._program),this.dispose()),this._result}};class St extends G{constructor(t){super(),this._rctx=t,this._dummyProgram=null,this._rctx.type===T.WEBGL2&&Y("mac")&&Y("chrome")&&(this._program=this._prepareProgram(),this._dummyProgram=this._prepareDummyProgram())}dispose(){var t;super.dispose(),(t=this._dummyProgram)==null||t.dispose(),this._dummyProgram=null}_test(t){const s=this._rctx;s.resetState();const i=new w(s,{colorTarget:N.TEXTURE,depthStencilTarget:y.NONE},{target:R.TEXTURE_2D,wrapMode:O.CLAMP_TO_EDGE,pixelFormat:g.RGBA,dataType:p.UNSIGNED_BYTE,samplingMode:x.NEAREST,width:1,height:1}),r=I.createIndex(this._rctx,D.STATIC_DRAW,new Uint8Array([0]));s.bindFramebuffer(i),s.setViewport(0,0,1,1),s.useProgram(this._dummyProgram),s.bindBuffer(r,a.ELEMENT_ARRAY_BUFFER),s.drawElements(E.POINTS,1,W.UNSIGNED_BYTE,0),s.useProgram(t),s.bindVAO(null),s.drawArrays(E.TRIANGLES,0,258);const n=new Uint8Array(4);return i.readPixels(0,0,1,1,g.RGBA,p.UNSIGNED_BYTE,n),i.dispose(),r.dispose(),n[0]===255}_prepareProgram(){const s=`
    precision highp float;

    varying float triangleId;

    const vec3 triangleVertices[3] = vec3[3](vec3(-0.5, -0.5, 0.0), vec3(0.5, -0.5, 0.0), vec3(0.0, 0.5, 0.0));

    void main(void) {
      triangleId = floor(float(gl_VertexID)/3.0);

      vec3 position = triangleVertices[gl_VertexID % 3];
      float offset = triangleId / ${J.float(85)};
      position.z = 0.5 - offset;

      gl_Position = vec4(position, 1.0);
    }
    `,i=`
    precision highp float;

    varying float triangleId;

    void main(void) {
      gl_FragColor = triangleId == ${J.float(85)} ? vec4(0.0, 1.0, 0.0, 1.0) : vec4(1.0, 0.0, 0.0, 1.0);
    }
    `;return this._rctx.programCache.acquire(s,i,new Map([]))}_prepareDummyProgram(){return this._rctx.programCache.acquire(`
    void main(void) {
      gl_Position = vec4(0.0, 0.0, float(gl_VertexID)-2.0, 1.0);
    }`,`
    void main(void) {
      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
    }`,new Map([]))}}class Ct extends G{constructor(t){super(),this._rctx=t,this._program=it(this._rctx,!1),this._obfuscated=it(this._rctx,!0)}dispose(){super.dispose(),this._obfuscated=X(this._obfuscated)}_test(t){if(Y("force-double-precision-obfuscation"))return!0;if(b(this._obfuscated))return!1;const s=this._runProgram(t),i=this._runProgram(this._obfuscated);return s!==0&&(i===0||s/i>5)}_runProgram(t){const s=this._rctx;s.resetState();const i=new w(s,{colorTarget:N.TEXTURE,depthStencilTarget:y.NONE},{target:R.TEXTURE_2D,wrapMode:O.CLAMP_TO_EDGE,pixelFormat:g.RGBA,dataType:p.UNSIGNED_BYTE,samplingMode:x.NEAREST,width:1,height:1}),r=I.createVertex(s,D.STATIC_DRAW,new Uint16Array([0,0,1,0,0,1,1,1])),n=new $(s,new Map([["position",0]]),{geometry:[new j("position",2,W.UNSIGNED_SHORT,0,4)]},{geometry:r}),h=Z(5633261287538229e-9,2626832878767164e-9,1.4349880495278358e6),l=Z(563327146742708e-8,2.6268736381334523e6,1434963231608387e-9),o=new Float32Array(6);et(h,o,3);const _=new Float32Array(6);et(l,_,3),s.useProgram(t),t.setUniform3f("u_highA",o[0],o[2],o[4]),t.setUniform3f("u_lowA",o[1],o[3],o[5]),t.setUniform3f("u_highB",_[0],_[2],_[4]),t.setUniform3f("u_lowB",_[1],_[3],_[5]),s.bindFramebuffer(i),s.setViewport(0,0,1,1),s.bindVAO(n),s.drawArrays(E.TRIANGLE_STRIP,0,4);const u=new Uint8Array(4);i.readPixels(0,0,1,1,g.RGBA,p.UNSIGNED_BYTE,u),n.dispose(!1),r.dispose(),i.dispose();const c=(h[2]-l[2])/25,F=bt(u);return Math.abs(c-F)}}function it(e,t){const s=`

  precision highp float;

  attribute vec2 position;

  uniform vec3 u_highA;
  uniform vec3 u_lowA;
  uniform vec3 u_highB;
  uniform vec3 u_lowB;

  varying vec4 v_color;

  ${t?"#define DOUBLE_PRECISION_REQUIRES_OBFUSCATION":""}

  #ifdef DOUBLE_PRECISION_REQUIRES_OBFUSCATION

  vec3 dpPlusFrc(vec3 a, vec3 b) {
    return mix(a, a + b, vec3(notEqual(b, vec3(0))));
  }

  vec3 dpMinusFrc(vec3 a, vec3 b) {
    return mix(vec3(0), a - b, vec3(notEqual(a, b)));
  }

  vec3 dpAdd(vec3 hiA, vec3 loA, vec3 hiB, vec3 loB) {
    vec3 t1 = dpPlusFrc(hiA, hiB);
    vec3 e = dpMinusFrc(t1, hiA);
    vec3 t2 = dpMinusFrc(hiB, e) + dpMinusFrc(hiA, dpMinusFrc(t1, e)) + loA + loB;
    return t1 + t2;
  }

  #else

  vec3 dpAdd(vec3 hiA, vec3 loA, vec3 hiB, vec3 loB) {
    vec3 t1 = hiA + hiB;
    vec3 e = t1 - hiA;
    vec3 t2 = ((hiB - e) + (hiA - (t1 - e))) + loA + loB;
    return t1 + t2;
  }

  #endif

  const float MAX_RGBA_FLOAT =
    255.0 / 256.0 +
    255.0 / 256.0 / 256.0 +
    255.0 / 256.0 / 256.0 / 256.0 +
    255.0 / 256.0 / 256.0 / 256.0 / 256.0;

  const vec4 FIXED_POINT_FACTORS = vec4(1.0, 256.0, 256.0 * 256.0, 256.0 * 256.0 * 256.0);

  vec4 float2rgba(const float value) {
    // Make sure value is in the domain we can represent
    float valueInValidDomain = clamp(value, 0.0, MAX_RGBA_FLOAT);

    // Decompose value in 32bit fixed point parts represented as
    // uint8 rgba components. Decomposition uses the fractional part after multiplying
    // by a power of 256 (this removes the bits that are represented in the previous
    // component) and then converts the fractional part to 8bits.
    vec4 fixedPointU8 = floor(fract(valueInValidDomain * FIXED_POINT_FACTORS) * 256.0);

    // Convert uint8 values (from 0 to 255) to floating point representation for
    // the shader
    const float toU8AsFloat = 1.0 / 255.0;

    return fixedPointU8 * toU8AsFloat;
  }

  void main() {
    vec3 val = dpAdd(u_highA, u_lowA, -u_highB, -u_lowB);

    v_color = float2rgba(val.z / 25.0);

    gl_Position = vec4(position * 2.0 - 1.0, 0.0, 1.0);
  }
  `;return e.programCache.acquire(s,`
  precision highp float;

  varying vec4 v_color;

  void main() {
    gl_FragColor = v_color;
  }
  `,new Map([["position",0]]))}let vt=class extends G{constructor(t){var r,n,h,l,o;if(super(),this._rctx=t,!t.gl)return;if(t.type===T.WEBGL1)return void(this._result=!(!((r=t.capabilities.textureFloat)!=null&&r.textureFloat)||!((n=t.capabilities.colorBufferFloat)!=null&&n.textureFloat)));if(!((h=t.capabilities.textureFloat)!=null&&h.textureFloat&&((l=t.capabilities.colorBufferFloat)!=null&&l.textureFloat)&&((o=t.capabilities.colorBufferFloat)!=null&&o.floatBlend)))return;const s=`
    precision highp float;
    attribute vec2 a_pos;

    void main() {
      gl_Position = vec4(a_pos * 2.0 - 1.0, 0.0, 1.0);
    }
    `,i=`
     precision highp float;

     void main() {
      gl_FragColor = vec4(0.5, 0.5, 0.5, 0.5);
     }
    `;this._program=t.programCache.acquire(s,i,new Map([["a_pos",0]]))}_test(t){const s=this._rctx,i=new w(s,{colorTarget:N.TEXTURE,depthStencilTarget:y.NONE},{target:R.TEXTURE_2D,wrapMode:O.CLAMP_TO_EDGE,pixelFormat:g.RGBA,dataType:p.FLOAT,internalFormat:ft.RGBA32F,samplingMode:x.NEAREST,width:1,height:1}),r=I.createVertex(s,D.STATIC_DRAW,new Uint16Array([0,0,1,0,0,1,1,1])),n=new $(s,new Map([["a_pos",0]]),{geometry:[new j("a_pos",2,W.UNSIGNED_SHORT,0,4)]},{geometry:r});s.useProgram(t);const h=s.getBoundFramebufferObject(),{x:l,y:o,width:_,height:u}=s.getViewport();s.bindFramebuffer(i),s.setViewport(0,0,1,1),s.bindVAO(n),s.drawArrays(E.TRIANGLE_STRIP,0,4);const c=gt({blending:pt});s.setPipelineState(c),s.drawArrays(E.TRIANGLE_STRIP,0,4),Tt.init(s);const F=s.gl.getError();return s.setViewport(l,o,_,u),s.bindFramebuffer(h),n.dispose(!1),r.dispose(),i.dispose(),F!==1282||(console.warn("Device claims support for WebGL extension EXT_float_blend but does not support it. Using fall back."),!1)}};class Mt extends G{constructor(t){super(),this._rctx=t;const s=`
      precision highp float;
      attribute vec2 a_pos;
      uniform highp sampler2D u_texture;
      varying vec4 v_color;

      float getBit(in float bitset, in int bitIndex) {
        float offset = pow(2.0, float(bitIndex));
        return mod(floor(bitset / offset), 2.0);
      }

      void main() {
        vec4 value = texture2D(u_texture, vec2(0.0));
        float bit = getBit(value.x * 255.0, 1);

        v_color = bit * vec4(1.0);
        gl_Position = vec4(a_pos * 2.0 - 1.0, 0.0, 1.0);
      }
      `,i=`
      precision highp float;
      varying vec4 v_color;

      void main() {
        gl_FragColor = v_color;
      }
      `;this._program=t.programCache.acquire(s,i,new Map([["a_pos",0]]))}_test(t){const s=this._rctx,i=new w(s,{colorTarget:N.TEXTURE,depthStencilTarget:y.NONE},{target:R.TEXTURE_2D,wrapMode:O.CLAMP_TO_EDGE,pixelFormat:g.RGBA,dataType:p.UNSIGNED_BYTE,samplingMode:x.NEAREST,width:1,height:1}),r=new Uint8Array(4),n=I.createVertex(s,D.STATIC_DRAW,new Uint16Array([0,0,1,0,0,1,1,1])),h=new $(s,new Map([["a_position",0]]),{geometry:[new j("a_position",2,W.SHORT,0,4)]},{geometry:n});s.useProgram(t);const l=new z(s,{target:R.TEXTURE_2D,wrapMode:O.CLAMP_TO_EDGE,pixelFormat:g.RGBA,dataType:p.UNSIGNED_BYTE,samplingMode:x.NEAREST,width:1,height:1},new Uint8Array([2,255,0,0]));t.setUniform1i("u_texture",0),s.bindTexture(l,0);const o=s.getBoundFramebufferObject();s.bindFramebuffer(i),s.useProgram(t);const{x:_,y:u,width:c,height:F}=s.getViewport();s.setViewport(0,0,1,1),s.bindVAO(h),s.drawArrays(E.TRIANGLE_STRIP,0,4),s.setViewport(_,u,c,F),i.readPixels(0,0,1,1,g.RGBA,p.UNSIGNED_BYTE,r),h.dispose(!1),n.dispose(),i.dispose();const L=r[0]!==255||r[1]!==255||r[2]!==255||r[3]!==255;return L&&_t.getLogger("esri.views.webgl.testSamplerPrecision").warn(`A problem was detected with your graphics driver. Your driver does not appear to honor sampler precision specifiers, which may result in rendering issues due to numerical instability. We recommend ensuring that your drivers have been updated to the latest version. Applying lowp sampler workaround. [${r[0]}.${r[1]}.${r[2]}.${r[3]}]`),s.bindFramebuffer(o),L}}let Ut=class extends G{constructor(t){super(),this._rctx=t,this._image=new Image,this._image.src="data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='5' height='5' version='1.1' viewBox='0 0 5 5' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='5' height='5' fill='%23f00' fill-opacity='.5'/%3E%3C/svg%3E%0A",this._image.width=5,this._image.height=5,this._image.decode();const s=`
    precision highp float;

    attribute vec2 a_pos;
    varying vec2 v_uv;

    void main() {
      v_uv = a_pos;
      gl_Position = vec4(a_pos * 2.0 - 1.0, 0.0, 1.0);
    }
    `,i=`
    precision highp float;

    varying vec2 v_uv;
    uniform sampler2D u_texture;

    void main() {
      gl_FragColor = texture2D(u_texture, v_uv);
    }
    `;this._program=t.programCache.acquire(s,i,new Map([["a_pos",0]]))}dispose(){super.dispose(),this._image.src=""}_test(t){const s=this._rctx;if(!s.gl)return t.dispose(),!0;const i=new w(s,{colorTarget:N.TEXTURE,depthStencilTarget:y.NONE},{target:R.TEXTURE_2D,wrapMode:O.CLAMP_TO_EDGE,pixelFormat:g.RGBA,dataType:p.UNSIGNED_BYTE,samplingMode:x.NEAREST,width:1,height:1}),r=I.createVertex(s,D.STATIC_DRAW,new Uint16Array([0,0,1,0,0,1,1,1])),n=new $(s,new Map([["a_pos",0]]),at,{geometry:r}),h=new z(s,{dataType:p.UNSIGNED_BYTE,pixelFormat:g.RGBA,preMultiplyAlpha:!1,wrapMode:O.CLAMP_TO_EDGE,samplingMode:x.LINEAR},this._image);s.useProgram(t),s.bindTexture(h,0),t.setUniform1i("u_texture",0);const l=s.getBoundFramebufferObject(),{x:o,y:_,width:u,height:c}=s.getViewport();s.bindFramebuffer(i),s.setViewport(0,0,1,1),s.setClearColor(0,0,0,0),s.setBlendingEnabled(!1),s.clearSafe(U.COLOR_BUFFER_BIT),s.bindVAO(n),s.drawArrays(E.TRIANGLE_STRIP,0,4);const F=new Uint8Array(4);return i.readPixels(0,0,1,1,g.RGBA,p.UNSIGNED_BYTE,F),n.dispose(!1),r.dispose(),i.dispose(),h.dispose(),s.setViewport(o,_,u,c),s.bindFramebuffer(l),F[0]!==255}},Pt=class{constructor(t){this.rctx=t,this.floatBufferBlend=new vt(t),this.svgPremultipliesAlpha=new Ut(t),this.doublePrecisionRequiresObfuscation=new Ct(t),this.ignoresSamplerPrecision=new Mt(t),this.drawArraysRequiresIndicesTypeReset=new St(t)}dispose(){this.ignoresSamplerPrecision.dispose(),this.doublePrecisionRequiresObfuscation.dispose(),this.svgPremultipliesAlpha.dispose(),this.floatBufferBlend.dispose(),this.drawArraysRequiresIndicesTypeReset.dispose()}};function Nt(e,t){if(t.disjointTimerQuery)return null;if(B(e))return{drawBuffers:e.drawBuffers.bind(e),MAX_DRAW_BUFFERS:e.MAX_DRAW_BUFFERS,MAX_COLOR_ATTACHMENTS:e.MAX_COLOR_ATTACHMENTS};if(t.drawBuffers)return null;const s=e.getExtension("WEBGL_draw_buffers");return s?{drawBuffers:s.drawBuffersWEBGL.bind(s),MAX_DRAW_BUFFERS:s.MAX_DRAW_BUFFERS_WEBGL,MAX_COLOR_ATTACHMENTS:s.MAX_COLOR_ATTACHMENTS_WEBGL}:null}function yt(e){if(B(e))return e;const t=e.getExtension("ANGLE_instanced_arrays");return t?{drawArraysInstanced:t.drawArraysInstancedANGLE.bind(t),drawElementsInstanced:t.drawElementsInstancedANGLE.bind(t),vertexAttribDivisor:t.vertexAttribDivisorANGLE.bind(t)}:null}function Dt(e,t){if(t.compressedTextureETC)return null;const s=e.getExtension("WEBGL_compressed_texture_etc");return s?{COMPRESSED_R11_EAC:s.COMPRESSED_R11_EAC,COMPRESSED_SIGNED_R11_EAC:s.COMPRESSED_SIGNED_R11_EAC,COMPRESSED_RG11_EAC:s.COMPRESSED_RG11_EAC,COMPRESSED_SIGNED_RG11_EAC:s.COMPRESSED_SIGNED_RG11_EAC,COMPRESSED_RGB8_ETC2:s.COMPRESSED_RGB8_ETC2,COMPRESSED_SRGB8_ETC2:s.COMPRESSED_SRGB8_ETC2,COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2:s.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2:s.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,COMPRESSED_RGBA8_ETC2_EAC:s.COMPRESSED_RGBA8_ETC2_EAC,COMPRESSED_SRGB8_ALPHA8_ETC2_EAC:s.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC}:null}function wt(e,t){if(t.compressedTextureS3TC)return null;const s=e.getExtension("WEBGL_compressed_texture_s3tc");return s?{COMPRESSED_RGB_S3TC_DXT1:s.COMPRESSED_RGB_S3TC_DXT1_EXT,COMPRESSED_RGBA_S3TC_DXT1:s.COMPRESSED_RGBA_S3TC_DXT1_EXT,COMPRESSED_RGBA_S3TC_DXT3:s.COMPRESSED_RGBA_S3TC_DXT3_EXT,COMPRESSED_RGBA_S3TC_DXT5:s.COMPRESSED_RGBA_S3TC_DXT5_EXT}:null}function It(e,t){if(B(e))return{MIN:e.MIN,MAX:e.MAX};if(t.blendMinMax)return null;{const s=e.getExtension("EXT_blend_minmax");return s?{MIN:s.MIN_EXT,MAX:s.MAX_EXT}:null}}function Gt(e,t){if(t.textureFilterAnisotropic)return null;const s=e.getExtension("EXT_texture_filter_anisotropic")||e.getExtension("MOZ_EXT_texture_filter_anisotropic")||e.getExtension("WEBKIT_EXT_texture_filter_anisotropic");return s?{MAX_TEXTURE_MAX_ANISOTROPY:s.MAX_TEXTURE_MAX_ANISOTROPY_EXT,TEXTURE_MAX_ANISOTROPY:s.TEXTURE_MAX_ANISOTROPY_EXT}:null}function Lt(e,t){if(B(e))return{textureFloat:!0,textureFloatLinear:!t.textureFloatLinear&&!!e.getExtension("OES_texture_float_linear"),textureHalfFloat:!0,textureHalfFloatLinear:!0,HALF_FLOAT:e.HALF_FLOAT,R16F:e.R16F,RG16F:e.RG16F,RGBA16F:e.RGBA16F,R32F:e.R32F,RG32F:e.RG32F,RGBA32F:e.RGBA32F,R11F_G11F_B10F:e.R11F_G11F_B10F,RGB16F:e.RGB16F};if(e instanceof WebGLRenderingContext){const s=!t.textureHalfFloat&&e.getExtension("OES_texture_half_float");return{textureFloat:!t.textureFloat&&!!e.getExtension("OES_texture_float"),textureFloatLinear:!t.textureFloatLinear&&!!e.getExtension("OES_texture_float_linear"),textureHalfFloat:!!s,textureHalfFloatLinear:!t.textureHalfFloatLinear&&!!e.getExtension("OES_texture_half_float_linear"),HALF_FLOAT:s?s.HALF_FLOAT_OES:void 0}}return null}function Xt(e,t){if(B(e)){const s=!t.colorBufferHalfFloat&&e.getExtension("EXT_color_buffer_half_float")||!t.colorBufferFloat&&e.getExtension("EXT_color_buffer_float"),i=!t.colorBufferFloat&&e.getExtension("EXT_color_buffer_float"),r=!t.floatBlend&&!t.colorBufferFloat&&e.getExtension("EXT_float_blend");return s||i||r?{textureFloat:!!i,textureHalfFloat:!!s,floatBlend:!!r,R16F:e.R16F,RG16F:e.RG16F,RGBA16F:e.RGBA16F,R32F:e.R32F,RG32F:e.RG32F,RGBA32F:e.RGBA32F,R11F_G11F_B10F:e.R11F_G11F_B10F,RGB16F:e.RGB16F}:null}if(e instanceof WebGLRenderingContext){const s=!t.colorBufferHalfFloat&&e.getExtension("EXT_color_buffer_half_float"),i=!t.colorBufferFloat&&e.getExtension("WEBGL_color_buffer_float"),r=!t.floatBlend&&!t.colorBufferFloat&&e.getExtension("EXT_float_blend");return s||i||r?{textureFloat:!!i,textureHalfFloat:!!s,floatBlend:!!r,RGBA16F:s?s.RGBA16F_EXT:void 0,RGB16F:s?s.RGB16F_EXT:void 0,RGBA32F:i?i.RGBA32F_EXT:void 0}:null}return null}function M(e,t,s,i,r){if(i&&B(e))return!0;if(t[s])return!1;for(const n of r)if(e.getExtension(n))return!0;return!1}function kt(e,t){if(!B(e)||t.textureNorm16)return null;const s=e.getExtension("EXT_texture_norm16");return s?{R16:s.R16_EXT,RG16:s.RG16_EXT,RGB16:s.RGB16_EXT,RGBA16:s.RGBA16_EXT,R16_SNORM:s.R16_SNORM_EXT,RG16_SNORM:s.RG16_SNORM_EXT,RGB16_SNORM:s.RGB16_SNORM_EXT,RGBA16_SNORM:s.RGBA16_SNORM_EXT}:null}function Wt(e,t){const s=t.loseContext&&e.getExtension("WEBGL_lose_context");return s?{loseRenderingContext:()=>s.loseContext()}:null}function $t(e,t){if(B(e))return{createVertexArray:e.createVertexArray.bind(e),deleteVertexArray:e.deleteVertexArray.bind(e),bindVertexArray:e.bindVertexArray.bind(e)};if(t.vao)return null;const s=e.getExtension("OES_vertex_array_object")||e.getExtension("MOZ_OES_vertex_array_object")||e.getExtension("WEBKIT_OES_vertex_array_object");return s?{createVertexArray:s.createVertexArrayOES.bind(s),deleteVertexArray:s.deleteVertexArrayOES.bind(s),bindVertexArray:s.bindVertexArrayOES.bind(s)}:null}class Vt{constructor(t,s){this._gl=t,this._instancing=null,this._vertexArrayObject=null,this._compressedTextureETC=null,this._compressedTextureS3TC=null,this._textureFilterAnisotropic=null,this._textureFloat=null,this._colorBufferFloat=null,this._minMaxBlending=null,this._loseContext=null,this._drawBuffers=null,this._textureNorm16=null,this._depthTexture=null,this._standardDerivatives=null,this._shaderTextureLOD=null,this._fragDepth=null,this._textureFloatLinear=null,this._disabledExtensions=s.disabledExtensions||{},this._debugWebGLExtensions=s.debugWebGLExtensions||{}}get drawBuffers(){return this._drawBuffers||(this._drawBuffers=Nt(this._gl,this._disabledExtensions)),this._drawBuffers}get instancing(){return this._instancing||(this._instancing=yt(this._gl)),this._instancing}get vao(){return this._vertexArrayObject||(this._vertexArrayObject=$t(this._gl,this._disabledExtensions)),this._vertexArrayObject}get compressedTextureETC(){return this._compressedTextureETC||(this._compressedTextureETC=Dt(this._gl,this._disabledExtensions)),this._compressedTextureETC}get compressedTextureS3TC(){return this._compressedTextureS3TC||(this._compressedTextureS3TC=wt(this._gl,this._disabledExtensions)),this._compressedTextureS3TC}get textureFilterAnisotropic(){return this._textureFilterAnisotropic||(this._textureFilterAnisotropic=Gt(this._gl,this._disabledExtensions)),this._textureFilterAnisotropic}get disjointTimerQuery(){return this._disjointTimerQuery||(this._disjointTimerQuery=Rt(this._gl,this._disabledExtensions)),this._disjointTimerQuery}get textureFloat(){return this._textureFloat||(this._textureFloat=Lt(this._gl,this._disabledExtensions)),this._textureFloat}get colorBufferFloat(){return this._colorBufferFloat||(this._colorBufferFloat=Xt(this._gl,this._disabledExtensions)),this._colorBufferFloat}get blendMinMax(){return this._minMaxBlending||(this._minMaxBlending=It(this._gl,this._disabledExtensions)),this._minMaxBlending}get depthTexture(){return this._depthTexture===null&&(this._depthTexture=M(this._gl,this._disabledExtensions,"depthTexture",!0,["WEBGL_depth_texture","MOZ_WEBGL_depth_texture","WEBKIT_WEBGL_depth_texture"])),this._depthTexture}get standardDerivatives(){return this._standardDerivatives===null&&(this._standardDerivatives=M(this._gl,this._disabledExtensions,"standardDerivatives",!0,["OES_standard_derivatives"])),this._standardDerivatives}get shaderTextureLOD(){return this._shaderTextureLOD===null&&(this._shaderTextureLOD=M(this._gl,this._disabledExtensions,"shaderTextureLOD",!0,["EXT_shader_texture_lod"])),this._shaderTextureLOD}get fragDepth(){return this._fragDepth===null&&(this._fragDepth=M(this._gl,this._disabledExtensions,"fragDepth",!0,["EXT_frag_depth"])),this._fragDepth}get loseContext(){return this._loseContext||(this._loseContext=Wt(this._gl,this._debugWebGLExtensions)),this._loseContext}get textureNorm16(){return this._textureNorm16||(this._textureNorm16=kt(this._gl,this._disabledExtensions)),this._textureNorm16}get textureFloatLinear(){return this._textureFloatLinear===null&&(this._textureFloatLinear=M(this._gl,this._disabledExtensions,"textureFloatLinear",!1,["OES_texture_float_linear"])),this._textureFloatLinear}enable(t){return this[t]}}let _e=class{constructor(e,t){this.gl=e,this.instanceCounter=new Ot,this.programCache=new ot(this),this._state=new st,this._numOfDrawCalls=0,this._numOfTriangles=0,this.type=B(e)?T.WEBGL2:T.WEBGL1,this._loadExtensions(),this.configure(t)}get gl2(){return this.type===T.WEBGL1?null:this.gl}configure(e){this._capabilities=new Vt(this.gl,e),this._parameters=this._loadParameters(e);const t=this.gl.getParameter(this.gl.VIEWPORT);this._state=new st,this._state.viewport={x:t[0],y:t[1],width:t[2],height:t[3]},this._stateTracker=new Ft({setBlending:s=>{if(s){this.setBlendingEnabled(!0),this.setBlendEquationSeparate(s.opRgb,s.opAlpha),this.setBlendFunctionSeparate(s.srcRgb,s.dstRgb,s.srcAlpha,s.dstAlpha);const i=s.color;this.setBlendColor(i.r,i.g,i.b,i.a)}else this.setBlendingEnabled(!1)},setCulling:s=>{s?(this.setFaceCullingEnabled(!0),this.setCullFace(s.face),this.setFrontFace(s.mode)):this.setFaceCullingEnabled(!1)},setPolygonOffset:s=>{s?(this.setPolygonOffsetFillEnabled(!0),this.setPolygonOffset(s.factor,s.units)):this.setPolygonOffsetFillEnabled(!1)},setDepthTest:s=>{s?(this.setDepthTestEnabled(!0),this.setDepthFunction(s.func)):this.setDepthTestEnabled(!1)},setStencilTest:s=>{if(s){this.setStencilTestEnabled(!0);const i=s.function;this.setStencilFunction(i.func,i.ref,i.mask);const r=s.operation;this.setStencilOp(r.fail,r.zFail,r.zPass)}else this.setStencilTestEnabled(!1)},setDepthWrite:s=>{s?(this.setDepthWriteEnabled(!0),this.setDepthRange(s.zNear,s.zFar)):this.setDepthWriteEnabled(!1)},setColorWrite:s=>{s?this.setColorMask(s.r,s.g,s.b,s.a):this.setColorMask(!1,!1,!1,!1)},setStencilWrite:s=>{s?this.setStencilWriteMask(s.mask):this.setStencilWriteMask(0)}}),this.enforceState(),X(this._driverTest),this._driverTest=new Pt(this)}dispose(){X(this._driverTest),this.programCache.dispose(),this.bindVAO(null),this.unbindBuffer(a.ARRAY_BUFFER),this.unbindBuffer(a.ELEMENT_ARRAY_BUFFER),this.type===T.WEBGL2&&(this.unbindBuffer(a.UNIFORM_BUFFER),this._state.uniformBufferBindingPoints.length=0,this.unbindBuffer(a.PIXEL_PACK_BUFFER),this.unbindBuffer(a.PIXEL_UNPACK_BUFFER),this.unbindBuffer(a.COPY_READ_BUFFER),this.unbindBuffer(a.COPY_WRITE_BUFFER)),this._state.textureUnitMap.length=0,m()&&console.log(this.instanceCounter.resourceInformation)}get driverTest(){return this._driverTest}get contextAttributes(){return this.gl.getContextAttributes()}get parameters(){return this._parameters}setPipelineState(e){this._stateTracker.setPipeline(e)}setBlendingEnabled(e){this._state.blend!==e&&(e===!0?this.gl.enable(this.gl.BLEND):this.gl.disable(this.gl.BLEND),this._state.blend=e,this._stateTracker.invalidateBlending())}externalProgramUpdate(){var e;(e=this._state.program)==null||e.stop(),this._state.program=null}externalTextureUnitUpdate(e,t){for(let s=0;s<e.length;++s)this._state.textureUnitMap[e[s]]=null;t>=0&&(this._state.activeTexture=t)}externalVertexArrayObjectUpdate(){const e=this.capabilities.vao;e&&(e.bindVertexArray(null),this._state.vertexArrayObject=null),this._state.vertexBuffer=null,this._state.indexBuffer=null}externalVertexBufferUpdate(){this._state.vertexBuffer=null}externalIndexBufferUpdate(){this._state.indexBuffer=null}setBlendColor(e,t,s,i){e===this._state.blendColor.r&&t===this._state.blendColor.g&&s===this._state.blendColor.b&&i===this._state.blendColor.a||(this.gl.blendColor(e,t,s,i),this._state.blendColor.r=e,this._state.blendColor.g=t,this._state.blendColor.b=s,this._state.blendColor.a=i,this._stateTracker.invalidateBlending())}setBlendFunction(e,t){e===this._state.blendFunction.srcRGB&&t===this._state.blendFunction.dstRGB||(this.gl.blendFunc(e,t),this._state.blendFunction.srcRGB=e,this._state.blendFunction.srcAlpha=e,this._state.blendFunction.dstRGB=t,this._state.blendFunction.dstAlpha=t,this._stateTracker.invalidateBlending())}setBlendFunctionSeparate(e,t,s,i){this._state.blendFunction.srcRGB===e&&this._state.blendFunction.srcAlpha===s&&this._state.blendFunction.dstRGB===t&&this._state.blendFunction.dstAlpha===i||(this.gl.blendFuncSeparate(e,t,s,i),this._state.blendFunction.srcRGB=e,this._state.blendFunction.srcAlpha=s,this._state.blendFunction.dstRGB=t,this._state.blendFunction.dstAlpha=i,this._stateTracker.invalidateBlending())}setBlendEquation(e){this._state.blendEquation.mode!==e&&(this.gl.blendEquation(e),this._state.blendEquation.mode=e,this._state.blendEquation.modeAlpha=e,this._stateTracker.invalidateBlending())}setBlendEquationSeparate(e,t){this._state.blendEquation.mode===e&&this._state.blendEquation.modeAlpha===t||(this.gl.blendEquationSeparate(e,t),this._state.blendEquation.mode=e,this._state.blendEquation.modeAlpha=t,this._stateTracker.invalidateBlending())}setColorMask(e,t,s,i){this._state.colorMask.r===e&&this._state.colorMask.g===t&&this._state.colorMask.b===s&&this._state.colorMask.a===i||(this.gl.colorMask(e,t,s,i),this._state.colorMask.r=e,this._state.colorMask.g=t,this._state.colorMask.b=s,this._state.colorMask.a=i,this._stateTracker.invalidateColorWrite())}setClearColor(e,t,s,i){this._state.clearColor.r===e&&this._state.clearColor.g===t&&this._state.clearColor.b===s&&this._state.clearColor.a===i||(this.gl.clearColor(e,t,s,i),this._state.clearColor.r=e,this._state.clearColor.g=t,this._state.clearColor.b=s,this._state.clearColor.a=i)}setFaceCullingEnabled(e){this._state.faceCulling!==e&&(e===!0?this.gl.enable(this.gl.CULL_FACE):this.gl.disable(this.gl.CULL_FACE),this._state.faceCulling=e,this._stateTracker.invalidateCulling())}setPolygonOffsetFillEnabled(e){this._state.polygonOffsetFill!==e&&(e===!0?this.gl.enable(this.gl.POLYGON_OFFSET_FILL):this.gl.disable(this.gl.POLYGON_OFFSET_FILL),this._state.polygonOffsetFill=e,this._stateTracker.invalidatePolygonOffset())}setPolygonOffset(e,t){this._state.polygonOffset[0]===e&&this._state.polygonOffset[1]===t||(this._state.polygonOffset[0]=e,this._state.polygonOffset[1]=t,this.gl.polygonOffset(e,t),this._stateTracker.invalidatePolygonOffset())}setCullFace(e){this._state.cullFace!==e&&(this.gl.cullFace(e),this._state.cullFace=e,this._stateTracker.invalidateCulling())}setFrontFace(e){this._state.frontFace!==e&&(this.gl.frontFace(e),this._state.frontFace=e,this._stateTracker.invalidateCulling())}setScissorTestEnabled(e){this._state.scissorTest!==e&&(e===!0?this.gl.enable(this.gl.SCISSOR_TEST):this.gl.disable(this.gl.SCISSOR_TEST),this._state.scissorTest=e)}setScissorRect(e,t,s,i){this._state.scissorRect.x===e&&this._state.scissorRect.y===t&&this._state.scissorRect.width===s&&this._state.scissorRect.height===i||(this.gl.scissor(e,t,s,i),this._state.scissorRect.x=e,this._state.scissorRect.y=t,this._state.scissorRect.width=s,this._state.scissorRect.height=i)}setDepthTestEnabled(e){this._state.depthTest!==e&&(e===!0?this.gl.enable(this.gl.DEPTH_TEST):this.gl.disable(this.gl.DEPTH_TEST),this._state.depthTest=e,this._stateTracker.invalidateDepthTest())}setClearDepth(e){this._state.clearDepth!==e&&(this.gl.clearDepth(e),this._state.clearDepth=e)}setDepthFunction(e){this._state.depthFunction!==e&&(this.gl.depthFunc(e),this._state.depthFunction=e,this._stateTracker.invalidateDepthTest())}setDepthWriteEnabled(e){this._state.depthWrite!==e&&(this.gl.depthMask(e),this._state.depthWrite=e,this._stateTracker.invalidateDepthWrite())}setDepthRange(e,t){this._state.depthRange.zNear===e&&this._state.depthRange.zFar===t||(this.gl.depthRange(e,t),this._state.depthRange.zNear=e,this._state.depthRange.zFar=t,this._stateTracker.invalidateDepthWrite())}setStencilTestEnabled(e){this._state.stencilTest!==e&&(e===!0?this.gl.enable(this.gl.STENCIL_TEST):this.gl.disable(this.gl.STENCIL_TEST),this._state.stencilTest=e,this._stateTracker.invalidateStencilTest())}setClearStencil(e){e!==this._state.clearStencil&&(this.gl.clearStencil(e),this._state.clearStencil=e)}setStencilFunction(e,t,s){this._state.stencilFunction.func===e&&this._state.stencilFunction.ref===t&&this._state.stencilFunction.mask===s||(this.gl.stencilFunc(e,t,s),this._state.stencilFunction.face=A.FRONT_AND_BACK,this._state.stencilFunction.func=e,this._state.stencilFunction.ref=t,this._state.stencilFunction.mask=s,this._stateTracker.invalidateStencilTest())}setStencilFunctionSeparate(e,t,s,i){this._state.stencilFunction.face===e&&this._state.stencilFunction.func===t&&this._state.stencilFunction.ref===s&&this._state.stencilFunction.mask===i||(this.gl.stencilFuncSeparate(e,t,s,i),this._state.stencilFunction.face=e,this._state.stencilFunction.func=t,this._state.stencilFunction.ref=s,this._state.stencilFunction.mask=i,this._stateTracker.invalidateStencilTest())}setStencilWriteMask(e){this._state.stencilWriteMask!==e&&(this.gl.stencilMask(e),this._state.stencilWriteMask=e,this._stateTracker.invalidateStencilWrite())}setStencilOp(e,t,s){this._state.stencilOperation.face===A.FRONT_AND_BACK&&this._state.stencilOperation.fail===e&&this._state.stencilOperation.zFail===t&&this._state.stencilOperation.zPass===s||(this.gl.stencilOp(e,t,s),this._state.stencilOperation.face=A.FRONT_AND_BACK,this._state.stencilOperation.fail=e,this._state.stencilOperation.zFail=t,this._state.stencilOperation.zPass=s,this._stateTracker.invalidateStencilTest())}setStencilOpSeparate(e,t,s,i){this._state.stencilOperation.face===e&&this._state.stencilOperation.fail===t&&this._state.stencilOperation.zFail===s&&this._state.stencilOperation.zPass===i||(this.gl.stencilOpSeparate(e,t,s,i),this._state.stencilOperation.face=e,this._state.stencilOperation.fail=t,this._state.stencilOperation.zFail=s,this._state.stencilOperation.zPass=i,this._stateTracker.invalidateStencilTest())}setActiveTexture(e,t=!1){const s=this._state.activeTexture;return e>=0&&(t||e!==this._state.activeTexture)&&(this.gl.activeTexture(V+e),this._state.activeTexture=e),s}clear(e){e&&this.gl.clear(e)}clearSafe(e,t=255){e&&(e&U.COLOR_BUFFER_BIT&&this.setColorMask(!0,!0,!0,!0),e&U.DEPTH_BUFFER_BIT&&this.setDepthWriteEnabled(!0),e&U.STENCIL_BUFFER_BIT&&this.setStencilWriteMask(t),this.gl.clear(e))}drawArrays(e,t,s){if(m()&&(this._numOfDrawCalls++,this._numOfTriangles+=rt(e,s)),this.gl.drawArrays(e,t,s),m()){const i=tt(this);i&&console.error("drawArrays:",i)}}drawElements(e,t,s,i){if(m()&&(this._numOfDrawCalls++,this._numOfTriangles+=rt(e,t)),this.gl.drawElements(e,t,s,i),m()){const r=tt(this);if(r){const n=this.getBoundVAO(),h=n==null?void 0:n.indexBuffer,l=n==null?void 0:n.vertexBuffers,o={indexBuffer:h,vertexBuffers:l},_={mode:e,count:t,type:s,offset:i},u=ht(h,L=>L.size)??0,c=i+t,F=u<c?`. Buffer is too small. Attempted to draw index ${c} of ${u}`:"";console.error(`drawElements: ${r}${F}`,{args:_,vao:o})}}}logInfo(){m()&&console.log(`DrawCalls: ${this._numOfDrawCalls}, Triangles: ${this._numOfTriangles}`)}resetInfo(){m()&&(this._numOfDrawCalls=0,this._numOfTriangles=0)}get capabilities(){return this._capabilities}setViewport(e,t,s,i){s=Math.max(Math.round(s),1),i=Math.max(Math.round(i),1);const r=this._state.viewport;r.x===e&&r.y===t&&r.width===s&&r.height===i||(r.x=e,r.y=t,r.width=s,r.height=i,this.gl.viewport(e,t,s,i))}getViewport(){const e=this._state.viewport;return{x:e.x,y:e.y,width:e.width,height:e.height}}useProgram(e){var t;this._state.program!==e&&((t=this._state.program)==null||t.stop(),this._state.program=e,this.gl.useProgram((e==null?void 0:e.glName)??null))}bindTexture(e,t,s=!1){(t>=this.parameters.maxTextureImageUnits||t<0)&&console.error("Input texture unit is out of range of available units!");const i=this._state.textureUnitMap[t];return b(e)||e.glName==null?(S(i)&&(this.setActiveTexture(t,s),this.gl.bindTexture(i.descriptor.target,null)),this._state.textureUnitMap[t]=null,i):s||i!==e?(this.setActiveTexture(t,s),this.gl.bindTexture(e.descriptor.target,e.glName),e.applyChanges(),this._state.textureUnitMap[t]=e,i):(e.isDirty&&(this.setActiveTexture(t,s),e.applyChanges()),i)}unbindTexture(e){if(!b(e))for(let t=0;t<this.parameters.maxTextureImageUnits;t++)this._state.textureUnitMap[t]===e&&(this.bindTexture(null,t),this._state.textureUnitMap[t]=null)}bindFramebuffer(e,t=!1){if(t||this._state.readFramebuffer!==e||this._state.drawFramebuffer!==e){if(b(e))return this.gl.bindFramebuffer(d.FRAMEBUFFER,null),this._state.readFramebuffer=null,void(this._state.drawFramebuffer=null);e.initializeAndBind(d.FRAMEBUFFER),this._state.readFramebuffer=e,this._state.drawFramebuffer=e}}bindFramebufferSeparate(e,t,s=!1){const i=t===d.READ_FRAMEBUFFER,r=i?this._state.readFramebuffer:this._state.drawFramebuffer;(s||r!==e)&&(b(e)?this.gl.bindFramebuffer(t,null):e.initializeAndBind(t),i?this._state.readFramebuffer=q(e,null):this._state.drawFramebuffer=q(e,null))}blitFramebuffer(e,t,s=0,i=0,r=e.width,n=e.height,h=0,l=0,o=t.width,_=t.height,u=U.COLOR_BUFFER_BIT,c=x.NEAREST){this.bindFramebufferSeparate(e,d.READ_FRAMEBUFFER),this.bindFramebufferSeparate(t,d.DRAW_FRAMEBUFFER),this.gl.blitFramebuffer(s,i,r,n,h,l,o,_,u,c)}bindBuffer(e,t){if(e)switch(t??(t=e.bufferType),t){case a.ARRAY_BUFFER:this._state.vertexBuffer=f(this.gl,e,t,this._state.vertexBuffer);break;case a.ELEMENT_ARRAY_BUFFER:this._state.indexBuffer=f(this.gl,e,t,this._state.indexBuffer);break;case a.UNIFORM_BUFFER:this._state.uniformBuffer=f(this.gl,e,t,this._state.uniformBuffer);break;case a.PIXEL_PACK_BUFFER:this._state.pixelPackBuffer=f(this.gl,e,t,this._state.pixelPackBuffer);break;case a.PIXEL_UNPACK_BUFFER:this._state.pixelUnpackBuffer=f(this.gl,e,t,this._state.pixelUnpackBuffer);break;case a.COPY_READ_BUFFER:this._state.copyReadBuffer=f(this.gl,e,t,this._state.copyReadBuffer);break;case a.COPY_WRITE_BUFFER:this._state.copyWriteBuffer=f(this.gl,e,t,this._state.copyWriteBuffer)}}bindRenderbuffer(e){const t=this.gl;e||(t.bindRenderbuffer(t.RENDERBUFFER,null),this._state.renderbuffer=null),this._state.renderbuffer!==e&&(t.bindRenderbuffer(t.RENDERBUFFER,e.glName),this._state.renderbuffer=e)}_getBufferBinding(e,t){if(t>=this.parameters.maxUniformBufferBindings||t<0)return console.error("Uniform buffer binding point is out of range!"),null;const s=this._state.uniformBufferBindingPoints;let i=s[t];return b(i)&&(i={buffer:null,offset:0,size:0},s[t]=i),i}bindBufferBase(e,t,s){const i=this._getBufferBinding(e,t);b(i)||i.buffer===s&&i.offset===0&&i.size===0||(this.gl.bindBufferBase(e,t,s?s.glName:null),i.buffer=s,i.offset=0,i.size=0)}bindBufferRange(e,t,s,i,r){const n=this._getBufferBinding(e,t);if(!b(n)&&!(n.buffer===s&&n.offset===i&&n.size===r)){if(i%this._parameters.uniformBufferOffsetAlignment!=0)return void console.error("Uniform buffer binding offset is not a multiple of the context offset alignment");this.gl.bindBufferRange(e,t,s.glName,i,r),n.buffer=s,n.offset=i,n.size=r}}bindUBO(e,t,s,i){b(t)?this.bindBufferBase(a.UNIFORM_BUFFER,e,null):(m()&&(i??t.byteLength)>this._parameters.maxUniformBlockSize&&console.error("Attempting to bind more data than the maximum uniform block size"),t.initialize(),s!==void 0&&i!==void 0?this.bindBufferRange(a.UNIFORM_BUFFER,e,t.buffer,s,i):this.bindBufferBase(a.UNIFORM_BUFFER,e,t.buffer))}unbindUBO(e){for(let t=0,s=this._state.uniformBufferBindingPoints.length;t<s;t++){const i=this._state.uniformBufferBindingPoints[t];S(i)&&i.buffer===e.buffer&&this.bindBufferBase(a.UNIFORM_BUFFER,t,null)}}unbindBuffer(e){switch(e){case a.ARRAY_BUFFER:this._state.vertexBuffer=f(this.gl,null,e,this._state.vertexBuffer);break;case a.ELEMENT_ARRAY_BUFFER:this._state.indexBuffer=f(this.gl,null,e,this._state.indexBuffer);break;case a.UNIFORM_BUFFER:this._state.uniformBuffer=f(this.gl,null,e,this._state.uniformBuffer);break;case a.PIXEL_PACK_BUFFER:this._state.pixelPackBuffer=f(this.gl,null,e,this._state.pixelPackBuffer);break;case a.PIXEL_UNPACK_BUFFER:this._state.pixelUnpackBuffer=f(this.gl,null,e,this._state.pixelUnpackBuffer);break;case a.COPY_READ_BUFFER:this._state.copyReadBuffer=f(this.gl,null,e,this._state.copyReadBuffer);break;case a.COPY_WRITE_BUFFER:this._state.copyWriteBuffer=f(this.gl,null,e,this._state.copyWriteBuffer)}}bindVAO(e=null){b(e)?this._state.vertexArrayObject&&(this._state.vertexArrayObject.unbind(),this._state.vertexArrayObject=null):this._state.vertexArrayObject!==e&&(e.bind(),this._state.vertexArrayObject=e)}async clientWaitAsync(e=ct(10)){const t=this.gl,s=t.fenceSync(dt.SYNC_GPU_COMMANDS_COMPLETE,0);if(!s)throw new Error("Client wait failed, could not create sync object");let i;this.instanceCounter.increment(P.Sync,s),t.flush();do await ut(e),i=t.clientWaitSync(s,0,0);while(i===Q.TIMEOUT_EXPIRED);if(this.instanceCounter.decrement(P.Sync,s),t.deleteSync(s),i===Q.WAIT_FAILED)throw new Error("Client wait failed")}getBoundFramebufferObject(e=d.FRAMEBUFFER){return e===d.READ_FRAMEBUFFER?this._state.readFramebuffer:this._state.drawFramebuffer}getBoundVAO(){return this._state.vertexArrayObject}resetState(){this.useProgram(null),this.bindVAO(null),this.bindFramebuffer(null,!0),this.unbindBuffer(a.ARRAY_BUFFER),this.unbindBuffer(a.ELEMENT_ARRAY_BUFFER),this.type===T.WEBGL2&&(this.unbindBuffer(a.UNIFORM_BUFFER),this._state.uniformBufferBindingPoints.length=0,this.unbindBuffer(a.PIXEL_PACK_BUFFER),this.unbindBuffer(a.PIXEL_UNPACK_BUFFER),this.unbindBuffer(a.COPY_READ_BUFFER),this.unbindBuffer(a.COPY_WRITE_BUFFER));for(let e=0;e<this.parameters.maxTextureImageUnits;++e)this.bindTexture(null,e);this.setBlendingEnabled(!1),this.setBlendFunction(C.ONE,C.ZERO),this.setBlendEquation(H.ADD),this.setBlendColor(0,0,0,0),this.setFaceCullingEnabled(!1),this.setCullFace(A.BACK),this.setFrontFace(nt.CCW),this.setPolygonOffsetFillEnabled(!1),this.setPolygonOffset(0,0),this.setScissorTestEnabled(!1),this.setScissorRect(0,0,this.gl.canvas.width,this.gl.canvas.height),this.setDepthTestEnabled(!1),this.setDepthFunction(k.LESS),this.setDepthRange(0,1),this.setStencilTestEnabled(!1),this.setStencilFunction(k.ALWAYS,0,0),this.setStencilOp(v.KEEP,v.KEEP,v.KEEP),this.setClearColor(0,0,0,0),this.setClearDepth(1),this.setClearStencil(0),this.setColorMask(!0,!0,!0,!0),this.setStencilWriteMask(4294967295),this.setDepthWriteEnabled(!0),this.setViewport(0,0,this.gl.canvas.width,this.gl.canvas.height)}enforceState(){var r,n,h;const e=this.capabilities.vao;e&&e.bindVertexArray(null);const{gl:t,gl2:s}=this;for(let l=0;l<this.parameters.maxVertexAttributes;l++)t.disableVertexAttribArray(l);if(this._state.vertexBuffer?t.bindBuffer(this._state.vertexBuffer.bufferType,this._state.vertexBuffer.glName):t.bindBuffer(a.ARRAY_BUFFER,null),this._state.indexBuffer?t.bindBuffer(this._state.indexBuffer.bufferType,this._state.indexBuffer.glName):t.bindBuffer(a.ELEMENT_ARRAY_BUFFER,null),S(s)){this._state.uniformBuffer?s.bindBuffer(this._state.uniformBuffer.bufferType,this._state.uniformBuffer.glName):s.bindBuffer(a.UNIFORM_BUFFER,null);for(let l=0;l<this._parameters.maxUniformBufferBindings;l++){const o=this._state.uniformBufferBindingPoints[l];if(S(o)){const{buffer:_,offset:u,size:c}=o;_!==null?u===0&&c===0?s.bindBufferBase(a.UNIFORM_BUFFER,l,_.glName):s.bindBufferRange(a.UNIFORM_BUFFER,l,_.glName,u,c):s.bindBufferBase(a.UNIFORM_BUFFER,l,null)}}this._state.pixelPackBuffer?s.bindBuffer(this._state.pixelPackBuffer.bufferType,this._state.pixelPackBuffer.glName):s.bindBuffer(a.PIXEL_PACK_BUFFER,null),this._state.pixelUnpackBuffer?s.bindBuffer(this._state.pixelUnpackBuffer.bufferType,this._state.pixelUnpackBuffer.glName):s.bindBuffer(a.PIXEL_UNPACK_BUFFER,null),this._state.copyReadBuffer?s.bindBuffer(this._state.copyReadBuffer.bufferType,this._state.copyReadBuffer.glName):s.bindBuffer(a.COPY_READ_BUFFER,null),this._state.copyWriteBuffer?s.bindBuffer(this._state.copyWriteBuffer.bufferType,this._state.copyWriteBuffer.glName):s.bindBuffer(a.COPY_WRITE_BUFFER,null),s.bindFramebuffer(d.READ_FRAMEBUFFER,null),s.readBuffer(s.BACK),this._state.readFramebuffer&&(s.bindFramebuffer(d.READ_FRAMEBUFFER,this._state.readFramebuffer.glName),s.readBuffer(Et.COLOR_ATTACHMENT0)),s.bindFramebuffer(d.DRAW_FRAMEBUFFER,((r=this._state.drawFramebuffer)==null?void 0:r.glName)??null)}else this._state.readFramebuffer=this._state.drawFramebuffer,t.bindFramebuffer(d.FRAMEBUFFER,((n=this._state.drawFramebuffer)==null?void 0:n.glName)??null);if(e&&this._state.vertexArrayObject){const l=this._state.vertexArrayObject;this._state.vertexArrayObject&&(this._state.vertexArrayObject.unbind(),this._state.vertexArrayObject=null),this.bindVAO(l)}t.useProgram(((h=this._state.program)==null?void 0:h.glName)??null),t.blendColor(this._state.blendColor.r,this._state.blendColor.g,this._state.blendColor.b,this._state.blendColor.a),t.bindRenderbuffer(t.RENDERBUFFER,this._state.renderbuffer?this._state.renderbuffer.glName:null),this._state.blend===!0?t.enable(this.gl.BLEND):t.disable(this.gl.BLEND),t.blendEquationSeparate(this._state.blendEquation.mode,this._state.blendEquation.modeAlpha),t.blendFuncSeparate(this._state.blendFunction.srcRGB,this._state.blendFunction.dstRGB,this._state.blendFunction.srcAlpha,this._state.blendFunction.dstAlpha),t.clearColor(this._state.clearColor.r,this._state.clearColor.g,this._state.clearColor.b,this._state.clearColor.a),t.clearDepth(this._state.clearDepth),t.clearStencil(this._state.clearStencil),t.colorMask(this._state.colorMask.r,this._state.colorMask.g,this._state.colorMask.b,this._state.colorMask.a),t.cullFace(this._state.cullFace),t.depthFunc(this._state.depthFunction),t.depthRange(this._state.depthRange.zNear,this._state.depthRange.zFar),this._state.depthTest===!0?t.enable(t.DEPTH_TEST):t.disable(t.DEPTH_TEST),t.depthMask(this._state.depthWrite),t.frontFace(this._state.frontFace),t.lineWidth(1),this._state.faceCulling===!0?t.enable(t.CULL_FACE):t.disable(t.CULL_FACE),t.polygonOffset(this._state.polygonOffset[0],this._state.polygonOffset[1]),this._state.polygonOffsetFill===!0?t.enable(t.POLYGON_OFFSET_FILL):t.disable(t.POLYGON_OFFSET_FILL),t.scissor(this._state.scissorRect.x,this._state.scissorRect.y,this._state.scissorRect.width,this._state.scissorRect.height),this._state.scissorTest===!0?t.enable(t.SCISSOR_TEST):t.disable(t.SCISSOR_TEST),t.stencilFunc(this._state.stencilFunction.func,this._state.stencilFunction.ref,this._state.stencilFunction.mask),t.stencilOpSeparate(this._state.stencilOperation.face,this._state.stencilOperation.fail,this._state.stencilOperation.zFail,this._state.stencilOperation.zPass),this._state.stencilTest===!0?t.enable(t.STENCIL_TEST):t.disable(t.STENCIL_TEST),t.stencilMask(this._state.stencilWriteMask);for(let l=0;l<this.parameters.maxTextureImageUnits;l++){t.activeTexture(V+l),t.bindTexture(R.TEXTURE_2D,null),t.bindTexture(R.TEXTURE_CUBE_MAP,null),this.type===T.WEBGL2&&(t.bindTexture(R.TEXTURE_3D,null),t.bindTexture(R.TEXTURE_2D_ARRAY,null));const o=this._state.textureUnitMap[l];S(o)&&t.bindTexture(o.descriptor.target,o.glName)}t.activeTexture(V+this._state.activeTexture);const i=this._state.viewport;t.viewport(i.x,i.y,i.width,i.height),this.resetInfo()}_loadExtensions(){this.type===T.WEBGL1&&this.gl.getExtension("OES_element_index_uint"),this.gl.getExtension("KHR_parallel_shader_compile")}_loadParameters(e){const t=this.capabilities.textureFilterAnisotropic,s=e.maxAnisotropy??1/0,i=this.type===T.WEBGL2,r=this.gl,n={versionString:this.gl.getParameter(this.gl.VERSION),maxVertexTextureImageUnits:this.gl.getParameter(this.gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS),maxVertexAttributes:this.gl.getParameter(this.gl.MAX_VERTEX_ATTRIBS),maxMaxAnisotropy:t?Math.min(this.gl.getParameter(t.MAX_TEXTURE_MAX_ANISOTROPY),s):1,maxTextureImageUnits:this.gl.getParameter(this.gl.MAX_TEXTURE_IMAGE_UNITS),maxTextureSize:this.gl.getParameter(this.gl.MAX_TEXTURE_SIZE),maxUniformBufferBindings:i?r.getParameter(r.MAX_UNIFORM_BUFFER_BINDINGS):0,maxVertexUniformBlocks:i?r.getParameter(r.MAX_VERTEX_UNIFORM_BLOCKS):0,maxFragmentUniformBlocks:i?r.getParameter(r.MAX_FRAGMENT_UNIFORM_BLOCKS):0,maxUniformBlockSize:i?r.getParameter(r.MAX_UNIFORM_BLOCK_SIZE):0,uniformBufferOffsetAlignment:i?r.getParameter(r.UNIFORM_BUFFER_OFFSET_ALIGNMENT):1,maxArrayTextureLayers:i?r.getParameter(r.MAX_ARRAY_TEXTURE_LAYERS):1,maxSamples:i?r.getParameter(r.MAX_SAMPLES):1};return z.TEXTURE_UNIT_FOR_UPDATES=n.maxTextureImageUnits-1,n}};function f(e,t,s,i){return t?i!==t&&e.bindBuffer(s,t.glName):e.bindBuffer(s,null),t}function rt(e,t){switch(e){case E.POINTS:return 2*t;case E.TRIANGLES:return t/3;case E.TRIANGLE_STRIP:case E.TRIANGLE_FAN:return t-2;default:return 0}}export{_e as I,re as o};
