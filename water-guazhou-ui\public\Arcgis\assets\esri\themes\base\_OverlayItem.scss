/**
 * NOTE:
 *   Overlay items are drawn using canvas when using the takeScreenshot
 *   API. Please take care that the visual styles stay the same. Most styling
 *   should happen using inline styles, while the CSS should be concerned with
 *   positioning only.
 */
.esri-text-overlay-item {
  display: block;
  position: absolute;
  overflow: hidden;
  white-space: nowrap;

  font-size: $font-size;
  font-family: $font-family;
  line-height: $line-height;
}

.esri-text-overlay-item-anchor-bottom {
  transform: translate(-50%, -100%);
}
.esri-text-overlay-item-anchor-bottom-right {
  transform: translate(-100%, -100%);
}
.esri-text-overlay-item-anchor-bottom-left {
  transform: translate(0, -100%);
}
.esri-text-overlay-item-anchor-top {
  transform: translate(-50%, 0);
}
.esri-text-overlay-item-anchor-top-right {
  transform: translate(-100%, 0);
}
.esri-text-overlay-item-anchor-top-left {
  transform: translate(0, 0);
}
.esri-text-overlay-item-anchor-center {
  transform: translate(-50%, -50%);
}
.esri-text-overlay-item-anchor-right {
  transform: translate(-100%, -50%);
}
.esri-text-overlay-item-anchor-left {
  transform: translate(0, -50%);
}

.esri-line-overlay-item {
  display: block;
  position: absolute;
  overflow: hidden;
  white-space: nowrap;
}
