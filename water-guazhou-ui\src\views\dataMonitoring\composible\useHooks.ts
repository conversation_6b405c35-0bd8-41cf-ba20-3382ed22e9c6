import moment from 'moment';
import { reactive, ref, shallowRef } from 'vue';
import { CircleClose, Plus, Search, Download } from '@element-plus/icons-vue';
import { getDevice, getDeviceVarGroup } from '@/api/device';
import { getProjectRoot } from '@/api/project';
import {
  formatTree,
  getFormatTreeNodeDeepestChild
} from '@/utils/GlobalHelper';
import { removeSlash } from '@/utils/removeIdSlash';
import { SLMessage } from '@/utils/Message';
import { queryDeviceDataIstar } from '@/api/tsdb';

export const useDevice = () => {
  const deviceList = ref<NormalOption[]>([]);

  const getDeviceData = async (id?: string) => {
    const res = await getDevice(id);
    deviceList.value = formatTree(
      res.data?.map((item) => {
        item.rId = removeSlash(item.id.id);
        return item;
      }) || [],
      { id: 'rId', value: 'rId', label: 'name' }
    );
  };
  return {
    getDeviceData,
    deviceList
  };
};

export const useChart = (refChart: any) => {
  const chartOption = ref();
  const refreshChart = (data?: {
    data: any[];
    columns: IFormTableColumn[];
    xData: string[];
    series: any[];
    units: NormalOption[];
  }) => {
    refChart.value?.clear();
    chartOption.value = {
      grid: {
        left: 10,
        right: 70,
        bottom: '20%',
        top: 40,
        containLabel: true
      },
      dataZoom: [
        {
          show: true,
          start: 0,
          end: 100,
          dataBackground: {
            areaStyle: {
              color: 'rgba(189, 210, 225, 0.75)'
            }
          },
          left: 10,
          right: 70,
          bottom: 10
        },
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          show: true,
          yAxisIndex: 0,
          width: 30,
          showDataShadow: false,
          right: 10,
          bottom: '20%',
          top: '15%',
          filterMode: 'none'
        }
      ],
      // color: ['#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570', '#c4ccd3'],
      color: [
        '#9E87FF',
        '#73DDFF',
        '#fe9a8b',
        '#F56948',
        '#9E87FF',
        '#0090FF',
        '#36CE9E',
        '#FFC005',
        '#FF515A',
        '#8B5CFF',
        '#00CA69'
      ],
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let relVal = params[0].name;
          for (let i = 0; i < params.length; i++) {
            relVal += `<div class="circle" ><span style="background:${
              params[i].color
            }"></span>${params[i].seriesName} : ${params[i].value} ${
              data?.units.find((item) => item.label === params[i].seriesName)
                ?.value || ''
            }</div>`;
          }
          return relVal;
        },
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        // data: [],
        type: 'scroll',
        left: 10
      },
      xAxis: {
        data: data?.xData || [],
        boundaryGap: false,
        type: 'category',
        axisLine: {},
        axisLabel: {
          show: true
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#666'
          }
        },
        axisLine: {},
        min: null,
        max: null
      },
      series: data?.series || []
    };
  };
  return {
    chartOption,
    refreshChart
  };
};

export const useProject = (callBack: (data?: NormalOption) => Promise<any>) => {
  const refreshProject = async () => {
    const res = await getProjectRoot();
    TreeData.data = formatTree(res.data || []);
    callBack(getFormatTreeNodeDeepestChild(TreeData.data));
  };
  const TreeData = reactive<SLTreeConfig>({
    title: '区域列表',
    data: [],
    isFilterTree: true,
    treeNodeHandleClick: (data: NormalOption) => {
      TreeData.currentProject = data;
      callBack(data);
    }
  });
  return {
    TreeData,
    refreshProject
  };
};

export const useTable = () => {
  const TableConfig = reactive<ICardTable>({
    columns: [],
    pagination: { hide: true },
    dataList: []
  });
  const refreshTable = (data) => {
    TableConfig.dataList = data.data || [];
    TableConfig.columns = data.columns || [];
  };
  return {
    refreshTable,
    TableConfig
  };
};

export const useSearch = (config: {
  refSearch: any;
  refTable?: any;
  withInterval?: boolean;
  searchTime?: boolean;
  export?: boolean;
  exportCall?: () => void;
  refreshCall?: (data?: {
    data: any[];
    columns: IFormTableColumn[];
    xData: string[];
    series: any[];
    units: NormalOption[];
  }) => void;
}) => {
  let timer: any = null;
  const id = moment().valueOf();
  const clearTimer = () => {
    timer && clearInterval(timer);
  };
  let deviceList: NormalOption[] = [];
  const searchGroup: { key: string; props: NormalOption[] }[] = [];

  const setDeviceGroup = async (config: IFormSelect, row: any) => {
    if (!config.setOptionBy) return [];
    const val = row[config.setOptionBy];
    if (!val) return [];
    const res = await getDeviceVarGroup(val);
    const groups: NormalOption[] = [];
    for (const key in res.data) {
      groups.push({ label: key, value: key });
      const oldSearchGroup = searchGroup.find(
        (item) => item.key === config.field
      );
      if (oldSearchGroup) {
        oldSearchGroup.props = formatTree(res.data[key] || [], {
          label: 'value',
          value: 'label',
          id: 'label'
        });
      } else {
        searchGroup.push({
          key: config.field || '',
          props: formatTree(res.data[key] || [], {
            label: 'value',
            value: 'label',
            id: 'label'
          })
        });
      }
    }
    config.options = groups;
    config.field && (row[config.field] = undefined);
    // return groups
  };
  const setDeviceProps = async (config: IFormSelect, row: any) => {
    if (!config.setOptionBy) return [];
    const field = config.setOptionBy;
    const options = searchGroup.find((item) => item.key === field)?.props || [];
    config.options = options;
    config.field && (row[config.field] = undefined);
    // return options
  };

  const removeGroup = (id: number) => {
    if (!id) return;
    const index = FormConfig.group.findIndex((item) => item.id === id);
    FormConfig.group.splice(index, 1);
  };
  const addGroup = () => {
    const id = moment().valueOf();
    FormConfig.group.push({
      id,
      styles: {
        width: '100%'
      },
      fields: [
        {
          itemContainerStyle: {
            width: '180px'
          },
          type: 'select',
          label: '',
          field: `deviceId${id}`,
          options: deviceList
        },
        {
          itemContainerStyle: {
            width: '100px'
          },
          type: 'select',
          label: '',
          field: `group${id}`,
          options: [],
          setOptionBy: `deviceId${id}`,
          setOptionMethod: setDeviceGroup
        },
        {
          itemContainerStyle: {
            width: '150px'
          },
          type: 'select',
          label: '',
          field: `prop${id}`,
          multiple: true,
          options: [],
          setOptionBy: `group${id}`,
          setOptionMethod: setDeviceProps
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              isTextBtn: true,
              text: '',
              click: () => removeGroup(id),
              svgIcon: shallowRef(CircleClose)
            }
          ]
        }
      ]
    });
  };
  const refreshData = async () => {
    const queryParams = config.refSearch.value?.dataForm;
    const attributes: string[] = [];
    const selectedProps: NormalOption[] = [];
    const units: NormalOption[] = [];
    FormConfig.group.map((item) => {
      const props: string[] = queryParams[`prop${item.id}`] || [];
      const deviceId = queryParams[`deviceId${item.id}`];
      const propSlect = item.fields.find(
        (field) => field.field === `prop${item.id}`
      ) as IFormSelect;
      const propOptions =
        (propSlect &&
          propSlect.options?.filter(
            (item) => props.indexOf(item.value.toString()) !== -1
          )) ||
        [];
      selectedProps.push(...propOptions);
      props.map((prop) => {
        attributes.push(`${deviceId}.${prop}`);
      });
    });
    if (!attributes.length) return SLMessage.warning('请先选择查询项');
    const [start, end] = config.searchTime
      ? [
          moment(queryParams.date[0]).valueOf(),
          moment(queryParams.date[1]).valueOf()
        ]
      : [moment().subtract(2, 'hours').valueOf(), moment().valueOf()];
    const res = await queryDeviceDataIstar({
      start,
      end,
      type: queryParams.type,
      attributes: Array.from(new Set(attributes))
    });
    const data: any[] = [];
    for (const key in res.data) {
      const row: any = {
        date: key
      };
      for (const prop in res.data[key]) {
        row[prop] = res.data[key][prop];
      }
      data.push(row);
    }
    if (!data.length) return SLMessage.warning('无数据');
    const columns: IFormTableColumn[] = [];
    const xData: string[] = [];
    const series: any[] = [];
    if (data.length) {
      for (const key in data[0]) {
        if (key === 'date') {
          columns.push({
            iconStyle: {
              color: '#69e850'
            },
            minWidth: 150,
            icon: 'iconfont icon-shijian',
            label: '日期',
            prop: key
          });
          xData.push(...data.map((item) => item[key]));
        } else {
          const keys = key.split('.');
          const deviceName = deviceList.find(
            (item) => item.value === keys[0]
          )?.label;
          const propObj = selectedProps.find((item) => item.value === keys[1]);
          const unit = propObj?.data.unit;
          const unitText = unit ? `（${unit}）` : '';
          const propName = propObj?.label;
          columns.push({
            minWidth: 180,
            label: `${deviceName}.${propName}${unitText}`,
            prop: key
          });
          series.push({
            name: `${deviceName}.${propName}`,
            type: 'line',
            areaStyle: {
              opacity: 0.3
            },
            data: data.map((item) => item[key])
          });
          units.push({
            label: `${deviceName}.${propName}`,
            value: unit
          });
        }
      }
    }

    config.refreshCall &&
      config.refreshCall({
        data,
        columns,
        xData,
        series,
        units
      });
  };
  const initFirstLineOfFilters = (devices: NormalOption[]) => {
    deviceList = devices;
    const filter = FormConfig.group[0].fields.find(
      (item) => item.field === `deviceId${id}`
    ) as IFormSelect;
    filter && (filter.options = deviceList);
  };
  const timerangeConfig: IFormItem[] = config.searchTime
    ? [
        {
          type: 'datetimerange',
          label: '',
          field: 'date',
          clearable: false,
          itemContainerStyle: {
            width: '350px'
          }
        }
      ]
    : [];
  const exporConfig: IButton[] = config.export
    ? [
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => {
            config.exportCall && config.exportCall();
          }
        }
      ]
    : [];
  const FormConfig = reactive<IFormConfig>({
    defaultValue: {
      type: '15m',
      date: [
        moment().subtract(1, 'day').format('YYYY-MM-DD hh:mm:ss'),
        moment().format('YYYY-MM-DD hh:mm:ss')
      ]
    },
    size: 'small',
    group: [
      {
        id,
        styles: {
          width: '100%',
          display: 'flex'
        },
        fields: [
          {
            itemContainerStyle: {
              width: '180px'
            },
            type: 'select',
            label: '',
            field: `deviceId${id}`,
            options: [],
            placeholder: '请选择采集器'
          },
          {
            itemContainerStyle: {
              width: '100px'
            },
            type: 'select',
            label: '',
            field: `group${id}`,
            options: [],
            setOptionBy: `deviceId${id}`,
            setOptionMethod: setDeviceGroup,
            placeholder: '请选择设备'
          },
          {
            itemContainerStyle: {
              width: '150px'
            },
            type: 'select',
            label: '',
            field: `prop${id}`,
            options: [],
            setOptionBy: `group${id}`,
            multiple: true,
            setOptionMethod: setDeviceProps,
            placeholder: '请选择变量'
          },
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                type: 'primary',
                text: '',
                svgIcon: shallowRef(Plus),
                iconStyles: {
                  marginRight: 0
                },
                click: addGroup
              }
            ]
          },
          {
            type: 'select',
            label: '采集间隔:',
            field: 'type',
            labelWidth: 65,
            clearable: false,
            options: [
              { label: '1 m', value: '1m' },
              { label: '5 m', value: '5m' },
              { label: '10 m', value: '10m' },
              { label: '15 m', value: '15m' }
            ],
            itemContainerStyle: {
              width: '180px',
              marginLeft: 'auto'
            }
          },
          ...timerangeConfig,
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                type: 'primary',
                text: '查询',
                svgIcon: shallowRef(Search),
                click: () => {
                  refreshData();
                  if (config.withInterval) {
                    if (timer !== null) {
                      clearInterval(timer);
                    } else {
                      timer = setInterval(() => {
                        refreshData();
                      }, 30 * 1000);
                    }
                  }
                }
              },
              ...exporConfig
            ]
          }
        ]
      }
    ]
  });
  return {
    FormConfig,
    clearTimer,
    initFirstLineOfFilters
  };
};
