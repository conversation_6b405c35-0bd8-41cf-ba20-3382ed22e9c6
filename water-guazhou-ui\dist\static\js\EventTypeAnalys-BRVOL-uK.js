import{_ as w}from"./index-C9hz-UZb.js";import{d as D,c as f,r as _,s as x,l as n,o as T,Q as B,ay as S,g as q,n as z,q as s,F as O,p as i,aq as E,al as L,C as V}from"./index-r0dFAfgr.js";import{_ as P}from"./Search-NSrhrIa_.js";import{B as F,P as H}from"./echart-BoVIcYbV.js";import{G as A}from"./index-CpGhZCTT.js";import{r as G}from"./reduce-BbPixnH6.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const I={class:"wrapper"},N={class:"content-wrapper"},Q={class:"left"},R={class:"right"},U={class:"right-top"},W={class:"right-bottom"},j=D({__name:"EventTypeAnalys",setup(J){const m=f(),u=f(),h=f(),k=_({filters:[{type:"radio-button",label:"时间范围",options:[{label:"日",value:"date"},{label:"月",value:"month"},{label:"年",value:"year"}],field:"type",onChange:()=>c()},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="date"},type:"date",label:"",field:"date"},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="month"},type:"month",label:"",field:"month"},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="year"},type:"year",label:"",field:"year"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:x(L),click:()=>c()}]}],defaultParams:{type:"date",month:n().format("YYYY-MM"),year:n().format("YYYY"),date:n().format("YYYY-MM-DD")}}),l=_({indexVisible:!0,columns:[{label:"事件类型",prop:"key"},{label:"事件数量(件)",prop:"value"},{label:"比例（%）",prop:"percentage"}],dataList:[],pagination:{hide:!0,refreshData:({page:e,size:t})=>{l.pagination.page=e,l.pagination.limit=t,c()}}}),p=_({barOpion:null,pieOption:null}),c=async()=>{var b,Y,g,C;const e=((b=m.value)==null?void 0:b.queryParams)||{},t=(e==null?void 0:e.type)||"date";let a;switch(t){case"month":a=n(e[t],"YYYY-MM");break;case"year":a=n(e[t],"YYYY");break;default:a=n(e[t],"YYYY-MM-DD");break}const o=((g=(Y=(await A({fromTime:a.startOf(t==="year"?"y":t==="month"?"M":"D").valueOf(),toTime:a.endOf(t==="year"?"y":t==="month"?"M":"D").valueOf(),statisticType:!0})).data)==null?void 0:Y.data)==null?void 0:g.types)||{};p.barOpion=F(o.data||[]),p.pieOption=H(o.data||[],"事件类型分析");const d=G(o.data||[],(r,M)=>r+M.value,0);l.dataList=((C=o.data)==null?void 0:C.map(r=>(r.percentage=((r.value||0)/(d||1)*100).toFixed(2),r)))||[]},y=()=>{var e,t;(e=u.value)==null||e.resize(),(t=h.value)==null||t.resize()};return T(()=>{c(),window.addEventListener("resize",y)}),B(()=>{window.removeEventListener("resize",y)}),(e,t)=>{const a=P,v=E,o=S("VChart"),d=w;return q(),z("div",I,[s(d,{class:"card",title:" ",overlay:""},{title:O(()=>[s(a,{ref_key:"refSearch",ref:m,config:k},null,8,["config"])]),default:O(()=>[i("div",N,[i("div",Q,[s(v,{config:l},null,8,["config"])]),i("div",R,[i("div",U,[s(o,{ref_key:"refChart",ref:u,option:p.barOpion},null,8,["option"])]),i("div",W,[s(o,{ref_key:"refChart1",ref:h,option:p.pieOption},null,8,["option"])])])])]),_:1})])}}}),re=V(j,[["__scopeId","data-v-c606f052"]]);export{re as default};
