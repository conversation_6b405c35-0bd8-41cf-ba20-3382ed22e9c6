<!-- 消防栓展示 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="消防栓展示"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { IFormIns } from '@/components/type'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { SLMessage } from '@/utils/Message'
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils'
import { GetFieldConfig } from '@/utils/geoserver/wfsUtils'
import { GetFieldValueByGeoserver } from '@/utils/geoserver/wfsUtils'

// 定义图层名称常量
const LAYER_NAME = '测点'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerIds: number[]
  layerInfos: any[]
  curFieldNode?: any
  curOperate?: 'uniqueing' | 'detailing' | ''
}>({
  loading: false,
  tabs: [],
  layerIds: [],
  layerInfos: []
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '选择字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layerid?.length) return
            const layerName = LAYER_NAME // GeoServer中的图层名
            const fields = await GetFieldConfig(layerName)
            if (fields.data?.featureTypes?.[0]?.properties) {
              // 转换GeoServer字段描述为所需格式
              config.data = fields.data.featureTypes[0].properties.map(prop => ({
                name: prop.name,
                alias: prop.name, // 可以根据需要设置更友好的显示名
                type: prop.localType || prop.type
              })).filter(field => field.name !== 'geometry' && field.name !== 'geom') // 过滤掉几何字段
            }
          },
          setDataBy: 'layerid',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: node => {
            state.curFieldNode = node
            appendSQL(node.name)
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '属性过滤'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=')
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL(" ILIKE '%替换此处%'")
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>')
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<')
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>')
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('AND')
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('OR')
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: node => {
                appendSQL(`'${node}'`)
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () => (state.curOperate === 'uniqueing'
                        ? '正在获取唯一值'
                        : '获取唯一值'),
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'detailing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'id > 0'
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              disabled: () => !refForm.value?.dataForm?.layerid?.length,
              click: () => handleQuery(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    layerid: []
  }
})
const appendSQL = val => {
  if (!refForm.value) return
  if (!refForm.value?.dataForm) refForm.value.dataForm = {}
  const sql = refForm.value.dataForm.sql || ' '
  refForm.value.dataForm.sql = sql + val + ' '
}

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '')
}

const handleQuery = async () => {
  const { layerid } = refForm.value?.dataForm || {}
  if (!layerid?.length) {
    SLMessage.warning('请先选择消防栓图层')
    return
  }

  const sql = refForm.value?.dataForm.sql || '1=1'
  try {
    // 使用WFS查询获取结果
    const result = await QueryByPolygon(LAYER_NAME, null, sql)
    if (!result.data || !result.data.features) {
      SLMessage.warning('未查询到消防栓数据')
      return
    }

    // 转换查询结果为表格数据
    const features = result.data.features
    state.tabs = features.map(feature => ({
      ...feature.properties,
      layername: LAYER_NAME,
      geometry: feature.geometry
    }))

    // 刷新详情面板
    refMap.value?.refreshDetail(state.tabs)
  } catch (error) {
    console.error('查询失败:', error)
    SLMessage.error('查询失败')
  }
}

const getUniqueValue = async () => {
  if (!state.curFieldNode) return
  const layerid = refForm.value?.dataForm.layerid
  if (!layerid?.length) {
    SLMessage.warning('请先选择一个图层')
    return
  }
  state.curOperate = 'uniqueing'
  try {
    const res = await GetFieldValueByGeoserver({
      layerName: 'hydrant',
      fiedName: state.curFieldNode.name
    })
    const extraFormItem = FormConfig.group.find(
      item => item.id === 'field-construct'
    )?.fields[0].extraFormItem
    const field = extraFormItem && (extraFormItem[0] as IFormList)
    if (field && res.data?.features) {
      // 获取该字段的所有唯一值
      const uniqueValues = [...new Set(res.data.features.map(f => f.properties[state.curFieldNode.name]))]
      field.data = uniqueValues
    }
  } catch (error) {
    console.error('获取唯一值失败:', error)
    SLMessage.error('获取唯一值失败')
  }
  state.curOperate = ''
}

const getLayerInfo = async () => {
  // 直接使用GeoServer中的消防栓图层
  if(!window.GIS_SERVER_SWITCH) {
    SLMessage.warning('请启用GeoServer服务')
    return
  }
  
  // 设置图层信息
  state.layerInfos = [{
    layerid: 'hydrant',
    layername: 'hydrant', // GeoServer中的图层名
    alias: '消防栓'
  }]
  
  // 设置表单的默认图层
  refForm.value && (refForm.value.dataForm.layerid = ['hydrant'])
}
const onMaploaded = async view => {
  staticState.view = view
  await getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.view?.map.removeAll()
})
</script>
<style lang="scss" scoped></style>
