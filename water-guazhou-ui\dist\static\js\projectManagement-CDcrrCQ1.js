import{m as e}from"./index-r0dFAfgr.js";function r(t){return e({url:"/api/so/project",method:"get",params:t})}function n(){return e({url:"/api/so/project/export/excel",method:"get",responseType:"blob"})}function i(){return e({url:"/api/so/bidding/export/excel",method:"get",responseType:"blob"})}function p(t){return e({url:`/api/so/project/${t}`,method:"delete"})}function d(t){return e({url:`/api/so/deviceItem/${t}`,method:"delete"})}function u(t){return e({url:"/api/so/project",method:"post",data:t})}function c(t){return e({url:"/api/so/bidding",method:"get",params:t})}function s(t){return e({url:"/api/so/biddingCompany",method:"get",params:t})}function a(t){return e({url:"/api/so/bidding",method:"post",data:t})}function g(t){return e({url:"/api/so/projectOperateRecord",method:"get",params:t})}function l(t){return e({url:`/api/so/project/completionInfo/${t}`,method:"get"})}function m(t){return e({url:`/api/so/construction/${t}`,method:"get"})}export{i as a,c as b,s as c,g as d,n as e,p as f,r as g,d as h,u as i,m as j,l as k,a as p};
