package org.thingsboard.server.dao.shuiwu;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwglMEntity;

import java.util.List;

public interface SwXjrwglService {
    void save(SwXjrwglMEntity entity, User currentUser);

    SwXjrwglMEntity getXjrwglM(String id);

    PageData<SwXjrwglMEntity> findList(int page, int size, String content, String status, String deviceId, TenantId tenantId);

    void deleteById(String id);

    List<SwXjrwglMEntity> findAll();

    void execute(SwXjrwglMEntity xjrwgl) throws Exception;

    void changeStatus(JSONObject params);

    void deleteById(List<String> ids);
}
