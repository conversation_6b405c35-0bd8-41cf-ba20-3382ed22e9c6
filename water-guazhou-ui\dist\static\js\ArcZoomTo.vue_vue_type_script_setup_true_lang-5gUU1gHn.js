import{g as n}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{d as a,ad as s,g as c,n as l}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as d}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const G=a({__name:"ArcZoomTo",props:{layerid:{},layertitle:{}},setup(m,{expose:p}){const e=m,r=s("view");return p({gotoFeature:async(t,i)=>{const o=d(r,{id:e.layerid,title:e.layertitle});o==null||o.removeAll(),o==null||o.add(t),await n(r,t,i)}}),(t,i)=>(c(),l("div"))}});export{G as _};
