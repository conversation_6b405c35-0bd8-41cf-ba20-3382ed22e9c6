package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.RepairJobEntity;

import java.util.List;

public interface RepairJobRepository extends JpaRepository<RepairJobEntity, String> {

    @Query("SELECT DISTINCT rj FROM RepairJobEntity rj, RepairJobCEntity rjc " +
            "WHERE rj.id = rjc.mainId AND rj.tenantId = ?2 AND rjc.deviceId LIKE %?3% AND rj.name LIKE %?1%")
    Page<RepairJobEntity> findList(String name, String tenantId, String deviceId, Pageable pageable);

    List<RepairJobEntity> findByType(String type);

    @Query("SELECT DISTINCT wo.priority, count(wo.priority) FROM RepairJobEntity rj, RepairJobCEntity rjc, WorkOrderEntity wo " +
            "WHERE wo.contentId = rj.id AND rj.id = rjc.mainId AND rjc.deviceId LIKE %?1% " +
            "GROUP BY wo.priority")
    List groupByPriority(String deviceId);
}
