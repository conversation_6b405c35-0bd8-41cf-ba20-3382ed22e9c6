// import { computed, watch, onMounted, onBeforeMount } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { createVNode, render } from 'vue'
import app from '@/main'
import PopInfoTem from '../template/popWindow.vue'

export default () => {
  let map: any = null
  let AMap: any = null
  let heatmap: any = null
  const storedMarkers: any = []
  const initAMap = async (domId: string, config: any) => {
    AMap = await AMapLoader.load({
      // key: 'b9ead67a4044d978a72125506b8742db', // 申请好的Web端开发者Key，首次调用 load 时必填
      key: '312ea3a03da6d14c60ea71789d1848ae',
      version: '1.4.15', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [
        'AMap.Autocomplete',
        'AMap.PlaceSearch',
        'AMap.Scale',
        'AMap.MapType',
        'AMap.CircleEditor',
        'AMap.Geocoder',
        'AMap.DistrictSearch'
      ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      AMapUI: {
        // 是否加载 AMapUI，缺省不加载
        version: '1.1', // AMapUI 缺省 1.1
        plugins: [] // 需要加载的 AMapUI ui插件
      },
      Loca: {
        // 是否加载 Loca， 缺省不加载
        version: '1.3.2' // Loca 版本，缺省 1.3.2
      }
    })
    // 图层列表
    const mapLayers: any[] = []
    config.mapLayer.forEach(item => {
      switch (item) {
        case '标准图层':
          mapLayers.push(new AMap.TileLayer())
          break
        case '卫星图层':
          mapLayers.push(new AMap.TileLayer.Satellite())
          break
        case '路网图层':
          mapLayers.push(new AMap.TileLayer.RoadNet())
          break
        case '实时交通图层':
          mapLayers.push(new AMap.TileLayer.Traffic())
          break
        case '楼块图层':
          mapLayers.push(new AMap.Buildings())
          break
        default:
          mapLayers.push(new AMap.TileLayer())
          break
      }
    })

    map = new AMap.Map(domId, {
      zoom: 11, // 级别
      // mapStyle: config.mapStyle,
      // layers: mapLayers,
      layers: [new AMap.TileLayer.Satellite()],
      center: config.center || [116.397428, 39.90923] // 中心点坐标
    })
    // 绑定事件
    if (config.events) {
      for (const key in config.events) {
        map.on(key, () => {
          config.events[key](map)
        })
      }
    }

    if (config.search) {
      // const auto = new AMap.Autocomplete({
      //   input: config.search.input
      // })
      // const placeSearch = new AMap.PlaceSearch({
      //   pageSize: 5, // 单页显示结果条数
      //   pageIndex: 1, // 页码
      //   citylimit: false, //是否强制限制在设置的城市内搜索
      //   map: map, // 展现结果的地图实例
      //   panel: 'panel', // 结果列表将在此容器中进行展示。
      //   autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
      //   renderStyle: 'default'
      // }) //构造地点查询类
      // AMap.event.addListener(auto, 'select', e => {
      //   console.log(e)
      //   placeSearch.setCity(e.poi.adcode)
      //   placeSearch.search(e.poi.name) //关键字查询查询
      // })
      setBoundary()
    }
    return map
  }

  // 标点
  const setMarker = (
    center: (number | string)[],
    config?: any,
    click?: (mark: any) => void
  ) => {
    if (!AMap) return
    if (center.length === 2 && center[0] && center[1]) {
      const marker = new AMap.Marker({
        position: new AMap.LngLat(...center),
        ...(config || {})
      })
      if (typeof click === 'function') {
        marker.on('click', () => click(marker))
      }
      map.add(marker)
      storedMarkers.push(marker)
      return marker
    }
  }

  // 标点群组
  const setMarkers = (
    data: (number | string)[][],
    config?: any,
    click?: (marker) => void
  ) => {
    const markers: any[] = []
    data.forEach((point: (number | string)[], idx: number | string) => {
      if (config && config.contentHandle) {
        config.content = config.contentHandle(point, idx)
        console.log(config.content)

        delete config.contentHandle
      }
      const marker = new AMap.Marker({
        position: new AMap.LngLat(...point),
        ...(config || {})
      })
      if (typeof click === 'function') {
        marker.on('click', () => click(marker))
      }
      markers.push(marker)
    })

    console.log(markers)

    const overlayGroup = new AMap.OverlayGroup(markers)
    map.add(overlayGroup)
    return overlayGroup
  }

  // 划线Polyline
  const setPolyline = (data: (number | string)[][], config?: any) => {
    const path = data.map(point => new AMap.LngLat(...point))
    const polyline = new AMap.Polyline({
      path,
      ...(config || {})
    })
    map.add(polyline)
    return polyline
  }

  // Polyline群组
  const setPolylines = (data: (number | string)[][][], config?: any) => {
    const polylines: any[] = []
    data.forEach(item => {
      const path = item.map(point => new AMap.LngLat(...point))
      polylines.push(
        new AMap.Polyline({
          path,
          ...(config || {})
        })
      )
    })

    const overlayGroup = new AMap.OverlayGroup(polylines)
    map.add(overlayGroup)
    return overlayGroup
  }

  // 弹窗
  const setInfoWindow = (point: (number | string)[], config?: any) => {
    const infoWindow = new AMap.InfoWindow({
      anchor: 'top',
      ...(config || {}),
      offset: new AMap.Pixel(5, -40)
    })
    infoWindow.open(map, point)
    return infoWindow
  }

  const setInfoWindows = (data: (number | string)[][], config?: any) => {
    const infoWindows: any[] = []
    data.forEach((point: (number | string)[], idx: number | string) => {
      if (config && config.contentHandle) {
        config.content = config.contentHandle(point, idx)
        console.log(config.content)

        delete config.contentHandle
      }
      const infoWindow = new AMap.InfoWindow({
        isCustom: true,
        anchor: 'top',
        ...(config || {}),
        offset: new AMap.Pixel(5, -40)
      })
      infoWindow.open(map, point)
      infoWindows.push(infoWindow)
    })

    const overlayGroup = new AMap.OverlayGroup(infoWindows)
    return overlayGroup
  }

  const setMultiInfoWindows = (
    data: { point: number[]; content: string | ((point) => string) }[],
    config?: any
  ) => {
    const infoWindows: any[] = []
    data.forEach(item => {
      const infoWindow = new AMap.InfoWindow({
        isCustom: true,
        anchor: 'bottom-center',
        content: item.content,
        ...(config || {}),
        offset: new AMap.Pixel(5, -40)
      })
      infoWindow.open(map, item.point)
      infoWindows.push(infoWindow)
    })

    const overlayGroup = new AMap.OverlayGroup(infoWindows)
    return overlayGroup
  }
  const _template = (props: any) => {
    const container = document.createElement('div')
    const vm = createVNode(PopInfoTem, props)
    vm.props && (vm.props.onClose = clearInfoWindow)
    vm.appContext = app._context
    render(vm, container)
    return container.firstElementChild
  }

  const setCenter = (center: number[]) => {
    if (center.length === 0) {
      map.setCenter(center)
    }
  }
  const setBoundary = () => {
    if (!window.SITE_CONFIG.GIS_CONFIG.gisShowBoundary) return
    const districtSearch = new AMap.DistrictSearch({
      // 关键字对应的行政区级别，共有5种级别
      level: 'district',
      //  是否显示下级行政区级数，1表示返回下一级行政区
      // subdistrict: 0,
      // 返回行政区边界坐标点
      extensions: 'all'
    })
    const outer = [
      new AMap.LngLat(-360, 90, true),
      new AMap.LngLat(-360, -90, true),
      new AMap.LngLat(360, -90, true),
      new AMap.LngLat(360, 90, true)
    ]
    districtSearch.search('北碚', (status, result) => {
      // 查询成功时，result即为对应的行政区信息
      const holes = result.districtList[0].boundaries
      if (holes) {
        // for (let i = 0, l = holes.length; i < l; i++) {
        //   // 生成行政区划polygon
        //   const polygon = new AMap.Polygon({
        //     map, // 指定地图对象
        //     strokeWeight: 1, // 轮廓线宽度
        //     path: bounds[i], // 轮廓线的节点坐标数组
        //     fillOpacity: 0.15, // 透明度
        //     fillColor: 'transparent', // 填充颜色
        //     strokeColor: '#256edc' // 线条颜色
        //   })
        // }
        const pathArray = [outer, ...holes]
        const polygon = new AMap.Polygon({
          pathL: pathArray,
          strokeColor: '#00ffff',
          strokeWeight: 1,
          fillColor: '#353535',
          fillOpacity: 0.2
        })
        polygon.setPath(pathArray)
        map.add(polygon)
      }
    })
  }
  const setListInfoWindow = (config: {
    point: number[]
    title: string
    titleRight?: string
    valuesLabel?: string
    values: {
      label: string
      value: string
      status?: string
      unit?: string
    }[]
    extraInfos?: NormalOption[]
  }) => {
    const content = _template({ config })
    const infoWindow = new AMap.InfoWindow({
      content,
      isCustom: true,
      anchor: 'bottom-center',
      offset: new AMap.Pixel(5, -40)
    })
    infoWindow.open(map, config.point)
    return infoWindow
  }
  const clearInfoWindow = () => {
    map?.clearInfoWindow()
  }
  const getCenter = () => map.getCenter()
  /**
   * 初始化热力图
   * @param data
   */
  const initHeatMap = (data?: any[]) => {
    console.log(AMap?.Heatmap)

    map.plugin(['AMap.Heatmap'], () => {
      // 初始化heatmap对象
      heatmap = new AMap.Heatmap(map, {
        radius: 25, // 给定半径
        opacity: [0, 0.8]
        /* ,
          gradient:{
              0.5: 'blue',
              0.65: 'rgb(117,211,248)',
              0.7: 'rgb(0, 255, 0)',
              0.9: '#ffea00',
              1.0: 'red'
          }
           */
      })
      // 设置数据集：该数据为北京部分“公园”数据
      heatmap.setDataSet({
        data: data || [],
        max: 100
      })
    })
  }
  /**
   * 重新设置热力图的数据
   * @param data
   * @returns
   */
  const resetHeatMap = (data: any[]) => {
    if (!heatmap) {
      console.log('热力图实例未初始化')
      return
    }
    heatmap.setDataSet({
      data: data || [],
      max: 100
    })
  }
  /**
   * 切换热力图的可见
   * @param open 可选 ， 指定是否可以
   * @returns
   */
  const toggleHeatMap = (open?: boolean) => {
    if (!heatmap) {
      console.log('热力图实例未初始化')
      return
    }
    if (open) typeof heatmap?.show === 'function' && heatmap.show()
    else {
      heatmap.visible ? heatmap.hide() : heatmap.show()
    }
  }
  /**
   * 坐标名称转换
   */
  const getGeocoder = (key: '1' | '2', callback) => {
    const geocoder = new AMap.Geocoder({
      city: '全国'
    })
    const center = map.getCenter()
    if (key === '1') {
      geocoder.getAddress([center.lng, center.lat], (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          // result为对应的地理位置详细信息
          callback(result.regeocode.formattedAddress)
        }
      })
    }
  }

  const clear = () => {
    storedMarkers.map(marker => {
      marker.setMap(null)
      marker = null
    })
    storedMarkers.length = 0
  }
  return {
    map,
    setMarker,
    setCenter,
    getCenter,
    initAMap,
    heatmap,
    initHeatMap,
    resetHeatMap,
    toggleHeatMap,
    AMap,
    setMarkers,
    setPolyline,
    setPolylines,
    setInfoWindow,
    setInfoWindows,
    clearInfoWindow,
    setMultiInfoWindows,
    setListInfoWindow,
    clear,
    getGeocoder
  }
}
