package org.thingsboard.server.dao.fault;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.fault.FaultSolution;
import org.thingsboard.server.dao.sql.fault.FaultSolutionMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class FaultSolutionServiceImpl implements FaultSolutionService {

    @Autowired
    private FaultSolutionMapper faultSolutionMapper;

    @Override
    public List<FaultSolution> getListByMainId(String mainId, String name, String tenantId) {

        List<FaultSolution> faultSolutionList = faultSolutionMapper.getListByMainId(mainId, name, tenantId);

        return faultSolutionList;
    }

    @Override
    public FaultSolution save(FaultSolution faultSolution) {
        faultSolution.setUpdateTime(new Date());
        if (StringUtils.isBlank(faultSolution.getId())) {
            faultSolution.setCreateTime(new Date());
            faultSolutionMapper.insert(faultSolution);
        } else {
            faultSolutionMapper.updateById(faultSolution);
        }
        return faultSolution;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        faultSolutionMapper.deleteBatchIds(ids);
        return IstarResponse.ok("删除成功");
    }

}
