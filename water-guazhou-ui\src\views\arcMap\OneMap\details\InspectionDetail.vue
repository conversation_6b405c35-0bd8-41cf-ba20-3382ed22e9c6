<template>
  <div class="one-map-detail">
    <div class="detail-row">
      <FieldSet :type="'simple'" :size="'default'" class="row-title">
        人员维修工单数量统计
      </FieldSet>
      <el-tabs
        v-model="state.activeName_Ring"
        type="border-card"
        :lazy="true"
        class="tabs"
        :class="{ darkblue: appStore.isDark }"
        @tab-change="refreshRing"
      >
        <el-tab-pane
          v-for="(tab, i) in state.tabs"
          :key="i"
          :label="tab.label"
          :name="tab.value"
        >
          <div v-if="!state.option2" class="empty">暂无数据</div>
          <Charts
            v-else
            :ref="'refChart2' + tab.value"
            autoresize
            theme="dark"
            :option="state.option2"
          ></Charts>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="detail-row">
      <FieldSet :type="'simple'" :size="'default'" class="row-title">
        人员维修工单数量排行
      </FieldSet>
      <el-tabs
        v-model="state.activeName_Bar1"
        type="border-card"
        class="tabs"
        :class="{ darkblue: appStore.isDark }"
        @tab-change="refreshBar1"
      >
        <el-tab-pane
          v-for="(tab, i) in state.tabs"
          :key="i"
          :label="tab.label"
          :name="tab.value"
        >
          <div v-if="!state.option3" class="empty">暂无数据</div>
          <Charts
            v-else
            :ref="'refChart3' + tab.value"
            autoresize
            theme="dark"
            :option="state.option3"
          ></Charts>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="detail-row">
      <FieldSet :type="'simple'" :size="'default'" class="row-title">
        维修人员完成率排行
      </FieldSet>
      <el-tabs
        v-model="state.activeName_Bar2"
        type="border-card"
        class="tabs"
        :class="{ darkblue: appStore.isDark }"
        @tab-change="refreshBar2"
      >
        <el-tab-pane
          v-for="(tab, i) in state.tabs"
          :key="i"
          :label="tab.label"
          :name="tab.value"
        >
          <div v-if="!state.option4" class="empty">暂无数据</div>
          <Charts
            v-else
            :ref="'refChart4' + tab.value"
            autoresize
            theme="dark"
            :option="state.option4"
          ></Charts>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { sortBy } from 'lodash-es';
import { useAppStore } from '@/store';
import { horizontalHistogram, ring } from '../../components/components/chart';
import { Charts } from '../../components';
import { WorkOrderTrend } from '@/api/patrol';
import { GetCompleteRatio } from '@/api/workorder';
import { formatterDateTime } from '@/utils/GlobalHelper';

const appStore = useAppStore();
const { proxy }: any = getCurrentInstance();
const emit = defineEmits(['refresh', 'mounted']);

const state = reactive<{
  tabs: NormalOption[];
  activeName_Ring: string;
  activeName_Bar1: string;
  activeName_Bar2: string;
  detailLoading: boolean;
  curRow?: any;
  option2: any;
  option3: any;
  option4: any;
}>({
  activeName_Bar1: 'today',
  activeName_Bar2: 'today',
  activeName_Ring: 'today',
  tabs: [
    { label: '本日', value: 'today' },
    { label: '本月', value: 'month' },
    { label: '本年', value: 'year' }
  ],
  detailLoading: false,
  option2: ring(),
  option3: horizontalHistogram('人员维修工单数量排行'),
  option4: horizontalHistogram('维修人员完成率排行')
});
const refreshRing = async () => {
  if (!state.curRow) return;
  const fromTime =
    state.activeName_Ring === 'year'
      ? moment().startOf('y').valueOf()
      : state.activeName_Ring === 'month'
        ? moment().startOf('M').valueOf()
        : moment().startOf('D').valueOf();
  const toTime =
    state.activeName_Ring === 'year'
      ? moment().endOf('y').valueOf()
      : state.activeName_Ring === 'month'
        ? moment().endOf('M').valueOf()
        : moment().endOf('D').valueOf();
  const res = await WorkOrderTrend({
    page: 1,
    size: 0,
    fromTime,
    toTime,
    processUserId: state.curRow.userId
  });
  const data = res.data?.data?.data?.map((item) => {
    return {
      name: item.key,
      nameAlias: item.key,
      value: item.value,
      valueAlias: item.value,
      scale: (item.percentage || 0) * 100 + '%'
    };
  });
  state.option2 = ring(data, '个');
  await nextTick();
  proxy.$refs['refChart2' + state.activeName_Ring]?.[0]?.resize();
};
const refreshBar1 = async () => {
  if (!state.curRow) return;
  const fromTime =
    state.activeName_Bar1 === 'year'
      ? moment().startOf('y').format(formatterDateTime)
      : state.activeName_Bar1 === 'month'
        ? moment().startOf('M').format(formatterDateTime)
        : moment().startOf('D').format(formatterDateTime);
  const toTime =
    state.activeName_Bar1 === 'year'
      ? moment().endOf('y').format(formatterDateTime)
      : state.activeName_Bar1 === 'month'
        ? moment().endOf('M').format(formatterDateTime)
        : moment().endOf('D').format(formatterDateTime);
  const res = await WorkOrderTrend({
    page: 1,
    size: 0,
    fromTime,
    toTime,
    processUserId: state.curRow.userId
  });
  const labels: string[] = [];
  const values: number[] = [];
  const data = sortBy(res.data?.data?.data || [], 'value');
  data.map((item) => {
    labels.push(item.key);
    values.push(item.value);
  });
  if (!data.length) {
    state.option3 = undefined;
    return;
  }
  state.option3 = horizontalHistogram('人员维修工单数量排行', labels, values);
  await nextTick();
  proxy.$refs['refChart3' + state.activeName_Bar1]?.[0]?.resize();
};
const refreshBar2 = async () => {
  if (!state.curRow) return;
  const fromTime =
    state.activeName_Bar2 === 'year'
      ? moment().startOf('y').format(formatterDateTime)
      : state.activeName_Bar2 === 'month'
        ? moment().startOf('M').format(formatterDateTime)
        : moment().startOf('D').format(formatterDateTime);
  const toTime =
    state.activeName_Bar2 === 'year'
      ? moment().endOf('y').format(formatterDateTime)
      : state.activeName_Bar2 === 'month'
        ? moment().endOf('M').format(formatterDateTime)
        : moment().endOf('D').format(formatterDateTime);
  const res = await GetCompleteRatio({
    fromTime,
    toTime,
    processUserId: state.curRow.userId
  });
  const labels: string[] = [];
  const values: number[] = [];
  const data = sortBy(res.data?.data?.data?.data || [], 'value');
  data.map((item) => {
    labels.push(item.key);
    values.push(item.value);
  });
  if (!data?.length) {
    state.option4 = undefined;
    return;
  }
  state.option4 = horizontalHistogram('人员维修工单数量排行', labels, values);
  await nextTick();
  proxy.$refs['refChart4' + state.activeName_Bar2]?.[0]?.resize();
};
const refreshDetail = async (row?: any) => {
  if (!row) return;
  state.curRow = row;
  refreshRing();
  refreshBar1();
  refreshBar2();
  emit('refresh', { ...(row || {}), title: row?.userName });
};
defineExpose({
  refreshDetail
});
onMounted(() => {
  emit('mounted');
});
</script>
<style lang="scss" scoped>
.empty {
  height: 100%;
  display: grid;
  place-items: center;
}
// .dark,
// .darkblue {
//   .one-map-detail {
//     .detail-row {
//       background-color: rgb(16, 39, 60);
//     }
//   }
// }
.one-map-detail {
  .detail-row {
    background-color: var(--el-bg-color);
    height: 370px;
    align-items: center;
    padding: 8px 8px 0 8px;
    margin-bottom: 20px;
    .row-title {
      margin: 0;
    }
  }
  .el-tabs {
    :deep(.el-tabs__content) {
      height: 246px;
    }
    :deep(.el-tab-pane) {
      height: 100%;
    }
  }
  .chart-wrapper {
    height: 100%;
    width: 100%;
    overflow: hidden;
    flex-direction: row;
    .tabs {
      flex: 1;
    }
  }
  .darkblue {
    .el-tabs--border-card > .el-tabs__content {
      padding: 0;
    }
  }
}
</style>
