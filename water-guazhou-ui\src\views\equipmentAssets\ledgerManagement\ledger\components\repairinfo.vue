<template>
  <div style="width: calc(100% - 20px)">
    <Form :key="key" ref="refForm" :config="Form1"></Form>
    <div class="tcharts">
      <div class="charts">
        <VChart
          ref="refChart2"
          autoresize
          :theme="appStore.isDark ? 'dark' : 'light'"
          :option="option1"
        ></VChart>
      </div>
      <div class="charts">
        <VChart
          ref="refChart2"
          autoresize
          :theme="appStore.isDark ? 'dark' : 'light'"
          :option="option2"
        ></VChart>
      </div>
    </div>
    <Form :key="key" ref="refForm" :config="Form2"></Form>
  </div>
</template>

<script lang="ts" setup>
import { 饼图, 折线图 } from './charts';
import { formatDate } from '@/utils/DateFormatter';
import { useAppStore } from '@/store';
import {
  getRepair,
  getRepairList
} from '@/api/equipment_assets/ledgerManagement';

const appStore = useAppStore();
const props = defineProps<{ id: string }>();

const key = ref(new Date().toString());

const option1 = ref(饼图([], '维修'));
const option2 = ref(折线图([], '维修次数'));

const Form1 = reactive<IDialogFormConfig>({
  title: '',
  labelWidth: '100px',

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'divider',
          text: '维修信息'
        },
        {
          xl: 8,
          type: 'text',
          label: '维修次数:',
          field: 'count'
        },
        {
          xl: 8,
          type: 'text',
          label: '最近维修:',
          field: 'latestRepairTime'
        }
      ]
    }
  ]
});

const Form2 = reactive<IDialogFormConfig>({
  title: '',
  labelWidth: '100px',

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'divider',
          text: '维修记录'
        },
        {
          type: 'table',
          field: 'faultReportCList',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.value) as any,
            columns: [
              { label: '任务名称', prop: 'title' },
              { label: '任务类型', prop: 'type' },
              {
                label: '任务开启时间',
                prop: 'startTime',
                formatter: (row) => formatDate(row.startTime, 'YYYY-MM-DD')
              },
              { label: '关联工单', prop: 'workOrderName' }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const data = ref([]);

const refreshData = async () => {
  // 获取维修信息
  getRepair(props.id).then((res) => {
    const row = res.data.data || {};
    for (const i in row) {
      if (row[i] === undefined || row[i] === null) row[i] = ' ';
    }
    Form1.defaultValue = { ...row };
    option1.value = 饼图(row.gradeCount || [], '维修');
    option2.value = 折线图(row.nowYearRepair || [], '维修次数');
    setTimeout(() => {
      key.value = new Date().toString();
    }, 1000);
  });
  // 获取维修记录
  getRepairList(props.id).then((res) => {
    data.value = res.data.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.tcharts {
  display: flex;
  .charts {
    height: 300px;
    flex: 1;
  }
}
</style>
