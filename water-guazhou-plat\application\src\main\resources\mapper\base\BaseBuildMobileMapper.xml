<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseBuildMobileMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseBuildMobile" id="BaseBuildMobileResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="loginUrl"    column="login_url"    />
        <result property="skinUrl"    column="skin_url"    />
        <result property="style"    column="style"    />
        <result property="cloudLogin"    column="cloud_login"    />
    </resultMap>

    <sql id="selectBaseBuildMobileVo">
        select id, name, icon_url, login_url, skin_url, style, cloud_login from base_build_mobile
    </sql>

    <select id="selectBaseBuildMobileList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseBuildMobile" resultMap="BaseBuildMobileResult">
        <include refid="selectBaseBuildMobileVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="iconUrl != null  and iconUrl != ''"> and icon_url = #{iconUrl}</if>
            <if test="loginUrl != null  and loginUrl != ''"> and login_url = #{loginUrl}</if>
            <if test="skinUrl != null  and skinUrl != ''"> and skin_url = #{skinUrl}</if>
            <if test="style != null  and style != ''"> and style = #{style}</if>
            <if test="cloudLogin != null  and cloudLogin != ''"> and cloud_login = #{cloudLogin}</if>
        </where>
    </select>
    
    <select id="selectBaseBuildMobileById" parameterType="String" resultMap="BaseBuildMobileResult">
        <include refid="selectBaseBuildMobileVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseBuildMobile" parameterType="org.thingsboard.server.dao.model.sql.base.BaseBuildMobile">
        insert into base_build_mobile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="loginUrl != null">login_url,</if>
            <if test="skinUrl != null">skin_url,</if>
            <if test="style != null">style,</if>
            <if test="cloudLogin != null">cloud_login,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="loginUrl != null">#{loginUrl},</if>
            <if test="skinUrl != null">#{skinUrl},</if>
            <if test="style != null">#{style},</if>
            <if test="cloudLogin != null">#{cloudLogin},</if>
         </trim>
    </insert>

    <update id="updateBaseBuildMobile" parameterType="org.thingsboard.server.dao.model.sql.base.BaseBuildMobile">
        update base_build_mobile
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="loginUrl != null">login_url = #{loginUrl},</if>
            <if test="skinUrl != null">skin_url = #{skinUrl},</if>
            <if test="style != null">style = #{style},</if>
            <if test="cloudLogin != null">cloud_login = #{cloudLogin},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseBuildMobileById" parameterType="String">
        delete from base_build_mobile where id = #{id}
    </delete>

    <delete id="deleteBaseBuildMobileByIds" parameterType="String">
        delete from base_build_mobile where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>