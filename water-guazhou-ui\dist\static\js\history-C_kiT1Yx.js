import{_ as D}from"./CardTable-rdWOL4_6.js";import{_ as S}from"./index-C9hz-UZb.js";import{_ as F}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as L}from"./index-BJ-QPYom.js";import j from"./TreeBox-mfOmxwZJ.js";import{u as B,a as P,b as V,c as E,d as R}from"./useHooks-BeUnknpc.js";import{d as N,c as l,o as O,ay as q,g as A,h as H,F as n,q as t,i as a,p as M,j as U,dI as W,C as $}from"./index-r0dFAfgr.js";import"./index-BggOjNGp.js";import"./index-cIaXVz1R.js";const z={class:"card-wrapper"},G=N({__name:"history",setup(J){const _=l(),c=l(),i=l(),{chartOption:m,refreshChart:f}=B(c),{TableConfig:d,refreshTable:h}=P(),{FormConfig:g,initFirstLineOfFilters:C}=V({refSearch:_,refTable:i,export:!0,withInterval:!1,searchTime:!0,exportCall:()=>v(),refreshCall:e=>{f(e),h(e)}}),v=()=>{var r;const e=new W,s=(r=i.value)==null?void 0:r.getTable(),o=b(c);e.addImage(o),e.addElTable(s),e.export()},b=e=>{if(!(e!=null&&e.value))return;const s=e.value.getWidth(),o=e.value.getHeight();return{src:e.value.getDataURL({pixelRatio:window.devicePixelRatio||1}),width:s,height:o}},{deviceList:x,getDeviceData:T}=E(),{TreeData:p,refreshProject:w}=R(async e=>{p.currentProject=e,await T(e==null?void 0:e.id),C(x.value||[])});return O(async()=>{await w(),f()}),(e,s)=>{const o=L,r=F,u=S,y=q("VChart"),k=D;return A(),H(j,null,{tree:n(()=>[t(o,{"tree-data":a(p)},null,8,["tree-data"])]),default:n(()=>[t(u,{class:"card-search"},{default:n(()=>[M("div",z,[t(r,{ref_key:"refSearch",ref:_,config:a(g)},null,8,["config"])])]),_:1}),t(u,{class:"card-chart",title:"实时数据"},{default:n(()=>[t(y,{ref_key:"refChart",ref:c,theme:a(U)().isDark?"darkblue":"light",option:a(m)},null,8,["theme","option"])]),_:1}),t(k,{ref_key:"refTable",ref:i,class:"card-table",config:a(d)},null,8,["config"])]),_:1})}}}),re=$(G,[["__scopeId","data-v-2bc17f7e"]]);export{re as default};
