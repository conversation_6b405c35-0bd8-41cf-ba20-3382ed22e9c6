package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisa;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisaContainer;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaSaveRequest;

public interface SoConstructionVisaService {
    /**
     * 分页条件查询工程签证单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionVisaContainer> findAllConditional(SoConstructionVisaPageRequest request);

    /**
     * 保存工程签证单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionVisa save(SoConstructionVisaSaveRequest entity);

    /**
     * 编号是否已存在
     *
     * @param code     编号
     * @param tenantId 客户id
     * @param id       自身id（更新时不为null）
     * @return 是否已存在
     */
    boolean isCodeExists(String code, String tenantId, String id);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionVisa entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 完成
     *
     * @param constructionCode 工程编号
     * @param userId           请求发起人id
     * @param tenantId         客户id
     * @return 是否成功
     */
    boolean complete(String constructionCode, String userId, String tenantId);

    /**
     * 是否已完成
     *
     * @param id 唯一标识
     * @return 是否已完成
     */
    boolean isComplete(String id);

    /**
     * 是否已完成
     *
     * @param constructionCode 编号
     * @param tenantId         客户id
     * @return 是否已完成
     */
    boolean isComplete(String constructionCode, String tenantId);

}
