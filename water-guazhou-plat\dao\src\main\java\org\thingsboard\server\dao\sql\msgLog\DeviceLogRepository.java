package org.thingsboard.server.dao.sql.msgLog;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.DeviceLogEntity;
import org.thingsboard.server.dao.model.sql.MsgLogEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface DeviceLogRepository extends CrudRepository<DeviceLogEntity, String> {

    @Query("SELECT new DeviceLogEntity(d.id, device.name, p.name, d.status, d.updateTime, d.format, d.tenantId, d.projectId, d.deviceId) " +
            "FROM DeviceLogEntity d, DeviceEntity device, ProjectEntity p WHERE d.projectId = p.id AND d.deviceId = device.id AND d.tenantId = ?1 order by d.updateTime DESC ")
    List<DeviceLogEntity> findByTenantIdOrderByUpdateTimeDesc(@Param("tenantId") String tenantId);

    @Query("SELECT new DeviceLogEntity(d.id, device.name, p.name, d.status, d.updateTime, d.format, d.tenantId, d.projectId, d.deviceId) " +
            "FROM DeviceLogEntity d, DeviceEntity device, ProjectEntity p WHERE d.projectId = p.id AND d.deviceId = device.id AND d.projectId = ?1 order by d.updateTime DESC ")
    List<DeviceLogEntity> findByProjectId(@Param("projectId") String projectId, Sort orders);

    /**
     * 总览设备日志API，不查询DTU,NBDTU,MODBUS类型的设备
     *
     * @param projectId
     * @param start
     * @param end
     * @return
     */
    @Query("SELECT new DeviceLogEntity(d.id, device.name, p.name, d.status, d.updateTime, d.format, d.tenantId, d.projectId, d.deviceId) " +
            "FROM DeviceLogEntity d, DeviceEntity device, ProjectEntity p WHERE d.projectId = p.id AND d.deviceId = device.id AND d.projectId = ?1 AND d.updateTime > ?2 AND d.updateTime < ?3 AND device.type not in ('NBDTU', 'DTU') ORDER BY d.updateTime DESC")
    List<DeviceLogEntity> findByProjectId(String projectId, Long start, Long end);


    @Query("SELECT new DeviceLogEntity(d.id, device.name, p.name, d.status, d.updateTime, d.format, d.tenantId, d.projectId, d.deviceId) " +
            "FROM DeviceLogEntity d, DeviceEntity device, ProjectEntity p WHERE d.projectId = p.id AND d.deviceId = device.id AND d.tenantId = ?1 AND d.updateTime > ?2 AND d.updateTime < ?3 order by d.updateTime DESC ")
    List<DeviceLogEntity> findByTenantIdAndUpdateTime(String tenantId, Long start, Long end);

    void deleteByDeviceId(String deviceId);
}
