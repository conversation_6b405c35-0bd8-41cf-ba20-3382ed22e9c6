package org.thingsboard.server.dao.model.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class GisOptionLogListRequest {

    private int page;

    private int size;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginOptionTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endOptionTime;

    private String type;

    private String keyword;

    private String optionType;

    private String tenantId;

}
