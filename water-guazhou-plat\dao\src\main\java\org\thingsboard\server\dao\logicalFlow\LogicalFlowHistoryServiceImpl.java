package org.thingsboard.server.dao.logicalFlow;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.sql.logicalFlow.*;

import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class LogicalFlowHistoryServiceImpl implements LogicalFlowHistoryService {

    @Autowired
    private LogicalFlowHistoryRepository logicalFlowHistoryRepository;

    @Autowired
    private LogicalFlowNodeHistoryRepository logicalFlowNodeHistoryRepository;

    @Autowired
    private LogicalFlowAlarmNodeHistoryRepository logicalFlowAlarmNodeHistoryRepository;

    @Autowired
    private LogicalFlowRepository logicalFlowRepository;

    @Autowired
    private LogicalFlowNodeRepository logicalFlowNodeRepository;


    @Override
    public LogicalFlowHistory save(LogicalFlowHistory history) {
        return logicalFlowHistoryRepository.save(history);
    }

    @Override
    public LogicalFlowNodeHistory save(LogicalFlowNodeHistory nodeHistory) {
        return logicalFlowNodeHistoryRepository.save(nodeHistory);
    }

    @Override
    public Map<String, Object> findByLogicalFlowIdList(List<String> LogicalFlowIdList, Integer page, Integer size,
                                                       Long startTime, Long endTime, String logicalFlowName) {
        Page<LogicalFlowHistory> historyPage = logicalFlowHistoryRepository.findAll(new Specification<LogicalFlowHistory>() {
            @Override
            public Predicate toPredicate(Root<LogicalFlowHistory> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                List<Predicate> list = new ArrayList<>();

                Path<Object> path = root.get("logicalFlowId");
                CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
                for (String logicalFlowId : LogicalFlowIdList){
                    in.value(logicalFlowId);
                }

                list.add(in);

                if (StringUtils.isNotBlank(logicalFlowName)) {
                    list.add(criteriaBuilder.like(root.get("logicalFlowName").as(String.class), "%" + logicalFlowName + "%"));
                }

                if (startTime != null) {
                    list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdTime").as(Long.class), startTime));
                }

                if (endTime != null) {
                    list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdTime").as(Long.class), endTime));
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            }
        }, new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "createdTime")));

        Map<String, Object> result = new HashMap<>();
        result.put("total", historyPage.getTotalElements());
        result.put("data", historyPage.getContent());
        return result;
    }

    @Override
    public List<LogicalFlowNodeHistory> findLogicalFlowHistoryDetail(String historyId) {
        // 查询历史节点列表
        List<LogicalFlowNodeHistory> historyList = logicalFlowNodeHistoryRepository.findByHistoryIdOrderByCreatedTime(historyId);

        // 主流程节点
        List<LogicalFlowNodeHistory> mainNodeList = historyList.stream()
                .filter(logicalFlowNodeHistory -> StringUtils.isBlank(logicalFlowNodeHistory.getParentNodeId()))
                .collect(Collectors.toList());

        // 子流程节点
        List<LogicalFlowNodeHistory> childNodeList = historyList.stream()
                .filter(logicalFlowNodeHistory -> StringUtils.isNotBlank(logicalFlowNodeHistory.getParentNodeId()))
                .collect(Collectors.toList());
        Map<String, List<LogicalFlowNodeHistory>> childNodeMap = new HashMap<>();
        childNodeList.forEach(logicalFlowNodeHistory -> {
            List<LogicalFlowNodeHistory> list;
            if (childNodeMap.containsKey(logicalFlowNodeHistory.getParentNodeId())) {
                list = childNodeMap.get(logicalFlowNodeHistory.getParentNodeId());
            } else {
                list = new ArrayList<>();
            }
            list.add(logicalFlowNodeHistory);
            childNodeMap.put(logicalFlowNodeHistory.getParentNodeId(), list);
        });

        // 遍历主流程，组装结果返回
        mainNodeList.forEach(logicalFlowNodeHistory -> {
            logicalFlowNodeHistory.setChildren(childNodeMap.get(logicalFlowNodeHistory.getLogicalFlowNodeId()));
        });

        return mainNodeList;
    }

    @Override
    public Map<String, Object> findAlarmNodeHistory(Integer page, Integer size, Long startTime, Long endTime, String projectId) {
        // 查询逻辑流程
        List<LogicalFlow> logicalFlowList = logicalFlowRepository.findByTypeAndProjectIdAndAll("CLOUD", projectId);
        Map<String, LogicalFlow> logicalFlowMap = new HashMap<>();
        logicalFlowList.forEach(logicalFlow -> logicalFlowMap.put(logicalFlow.getId(), logicalFlow));

        // 查询逻辑流程节点
        List<LogicalFlowNode> logicalFlowNodeList = logicalFlowNodeRepository
                .findByTypeAndLogicalFlowIdIn(DataConstants.LogicalFlowNodeType.ALARM.name(),
                        logicalFlowList.stream().map(LogicalFlow::getId).collect(Collectors.toList()));
        Map<String, LogicalFlowNode> logicalFlowNodeMap = new HashMap<>();
        logicalFlowNodeList.forEach(logicalFlowNode -> logicalFlowNodeMap.put(logicalFlowNode.getId(), logicalFlowNode));
        if (endTime == null) {
            endTime = System.currentTimeMillis();
        }
        if (startTime == null) {
            startTime = 0L;
        }
        Page<LogicalFlowAlarmNodeHistory> historyPage = logicalFlowAlarmNodeHistoryRepository
                .findByLogicalFlowNodeIdInAndCreatedTimeIsGreaterThanEqualAndCreatedTimeIsLessThanEqualOrderByCreatedTimeDesc(
                        logicalFlowNodeList.stream().map(LogicalFlowNode::getId).collect(Collectors.toList()),
                        startTime, endTime, new PageRequest(page - 1, size));

        Map<String, Object> result = new HashMap<>();
        result.put("total", historyPage.getTotalElements());
        result.put("data", historyPage.getContent().stream().peek(history -> {
            // 设置流程名称
            if (logicalFlowMap.get(history.getLogicalFlowId()) != null) {
                LogicalFlow logicalFlow = logicalFlowMap.get(history.getLogicalFlowId());
                if (!logicalFlow.getParentId().equals("0")) {
                    history.setLogicalFlowName(logicalFlowMap.get(logicalFlow.getParentId()).getName() + "#" + logicalFlow.getName());
                } else {
                    history.setLogicalFlowName(logicalFlow.getName());
                }
            } else {
                history.setLogicalFlowName("该流程已被删除");
            }
            // 设置流程节点名称
            if (logicalFlowNodeMap.get(history.getLogicalFlowNodeId()) != null) {
                history.setLogicalFlowNodeName(logicalFlowNodeMap.get(history.getLogicalFlowNodeId()).getName());
            } else {
                history.setLogicalFlowNodeName("该节点已被删除");
            }

        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public LogicalFlowAlarmNodeHistory save(LogicalFlowAlarmNodeHistory logicalFlowAlarmNodeHistory) {
        return logicalFlowAlarmNodeHistoryRepository.save(logicalFlowAlarmNodeHistory);
    }

    @Override
    public List<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeId(String nodeId, int page, int size) {
        return logicalFlowAlarmNodeHistoryRepository.findByLogicalFlowNodeId(nodeId, new PageRequest(page, size, new Sort(Sort.Direction.DESC, "createdTime")));
    }

    @Override
    public List<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeIdAndSendFlag(String id, String sendFlag, int page, int size) {
        return logicalFlowAlarmNodeHistoryRepository.findByLogicalFlowNodeIdAndSendFlag(id, sendFlag, new PageRequest(page, size, new Sort(Sort.Direction.DESC, "createdTime")));
    }
}
