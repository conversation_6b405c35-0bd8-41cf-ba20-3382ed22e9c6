package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_circuit_plan")
public class CircuitPlan {
    // id
    @TableId
    private String id;

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 计划类别。周期性任务、固定日期任务
    private String planType;

    // 计划名称
    private String name;

    // 巡检模板ID
    private String templateId;

    // 固定的执行日期。当计划类别为：固定日期任务。该字段有值。
    private Date fixedDate;

    // 周期开始时间。当计划类别为：周期性任务。该字段有值。
    private Date startDate;

    // 周期结束时间。当计划类别为：周期性任务。该字段有值。
    private Date endDate;

    // 执行巡检人员
    @ParseUsername
    private String executionUserId;

    // 巡检成果审核人员
    @ParseUsername
    private String auditUserId;

    // 执行天数
    private Integer executionDays;

    // 间隔天数
    private Integer intervalDays;

    // 执行次数
    private Integer executionNum;

    // 要巡检的站点列表，多个用逗号分隔
    private String stationIds;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
