import{p as L,G as E,$ as T,h as G,a0 as F,a as N}from"./AnimatedLinesLayer-B2VbV4jv.js";import{s as _}from"./FeatureHelper-Da16o0mu.js";import"./pe-B8dP0-Ut.js";import{Q as l,d as P,r as $,g as f,n as q,q as A,F as D,i as B,h as x,cs as U,fJ as J,cE as Q,C as Z}from"./index-r0dFAfgr.js";import{er as j,es as z,g as K,S as O,gq as R}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const st=()=>{let t,s,o,a,d;const e={value:void 0};return{initSketch:(n,i,c)=>{var u,g,h,I,S;return e.value=new L({view:n,layer:i,polygonSymbol:_("polygon"),polylineSymbol:_("polyline"),pointSymbol:_("point"),snappingOptions:c==null?void 0:c.snappingOptions}),t=(u=e.value)==null?void 0:u.on("create",r=>{const p={graphics:r.graphic&&[r.graphic]||[],state:r.state,tool:r.tool,toolEventInfo:r.toolEventInfo,type:r.type};c!=null&&c.createCallBack&&c.createCallBack(p)}),s=(g=e.value)==null?void 0:g.on("update",r=>{const p={graphics:r.graphics,state:r.state,tool:r.tool,toolEventInfo:r.toolEventInfo,type:r.type,aborted:r.aborted};c!=null&&c.updateCallBack&&c.updateCallBack(p)}),o=(h=e.value)==null?void 0:h.on("delete",r=>{const p={graphics:r.graphics,tool:r.tool,type:r.type};c!=null&&c.delCallBack&&c.delCallBack(p)}),a=(I=e.value)==null?void 0:I.on("undo",r=>{const p={graphics:r.graphics,tool:r.tool,type:r.type};c!=null&&c.undoCallBack&&c.undoCallBack(p)}),d=(S=e.value)==null?void 0:S.on("redo",r=>{const p={graphics:r.graphics,tool:r.tool,type:r.type};c!=null&&c.redoCallBack&&c.redoCallBack(p)}),e.value},destroySketch:()=>{var n;t==null||t.remove(),s==null||s.remove(),o==null||o.remove(),a==null||a.remove(),d==null||d.remove(),(n=e.value)==null||n.destroy()},sketch:e}},ct=()=>{let t,s,o;const a=(n,i)=>{const{createTdtLayer:c}=E(),u=c({type:window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap||"vec_w"}),g=c({type:window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi||"cva_w"}),h=new j({basemap:{baseLayers:[u,g],title:"鹰眼图"}});return o=new z({container:i,map:h,constraints:{rotationEnabled:!1}}),o.ui.components=[],o.when(()=>{n.when(()=>{d(n),e(n),y(n)})}),o},d=n=>{const i=new K({geometry:n==null?void 0:n.extent,symbol:new O({color:[0,0,0,.5],outline:void 0})});o==null||o.graphics.removeAll(),o==null||o.graphics.add(i)},e=n=>{o==null||o.goTo({center:n.center,scale:n.scale*4*Math.max(n.width/o.width,n.height/o.height)})},y=n=>{n&&(t=n.watch("extent",()=>{d(n)}),s=n.watch("stationary",()=>{e(n)}))},m=()=>{t==null||t.remove(),s==null||s.remove(),o==null||o.destroy()};return l(()=>{m()}),{destroy:m,init:a}},rt=()=>{let t,s;const o=(e,y,m)=>{var n;if(e)return t=new T({view:e,basemapLegendVisible:!1,layerInfos:m||[]}),s=new G({view:e,expandTooltip:"图例",content:t}),s&&((n=e.ui)==null||n.add(s,y||"top-right")),a(e),t},a=e=>{e==null||e.map.allLayers.on("change",()=>{var y,m;t&&(t.layerInfos=((m=(y=e.map)==null?void 0:y.allLayers.map(n=>({title:n.title,layer:n})))==null?void 0:m.toArray())||[])})},d=()=>{t==null||t.destroy(),s==null||s.destroy()};return l(()=>{d()}),{init:o}},dt=()=>{let t;const s=(a,d)=>{var e;return t=new R({view:a}),(e=a.ui)==null||e.add(t,d||"top-left"),t},o=()=>{t==null||t.destroy()};return l(()=>{o()}),{init:s}},at=()=>{let t;const s=(a,d,e)=>{var y,m;return t=new F({view:a,viewpoint:{scale:e==null?void 0:e.scale,targetGeometry:(e==null?void 0:e.targetGeometry)||((y=a.extent)==null?void 0:y.clone())}}),t&&((m=a.ui)==null||m.add(t,d||"bottom-right")),t},o=()=>{t==null||t.destroy()};return l(()=>{o()}),{init:s}},mt=()=>{let t,s;const o=(d,e,y)=>{var m;return t=new N({view:d,printServiceUrl:e}),s=new G({view:d,content:t,expandTooltip:"打印"}),(m=d.ui)==null||m.add(s,y||"top-right"),s},a=()=>{t==null||t.destroy(),s==null||s.destroy()};return l(()=>{a()}),{init:o}},W=P({__name:"arcWidgetButton",props:{icon:{}},emits:["click"],setup(t,{expose:s,emit:o}){const a=o,d=$({collapsed:!0}),e=m=>{d.collapsed=m!==void 0?m:!d.collapsed},y=()=>{d.collapsed=!d.collapsed,a("click",d.collapsed)};return s({toggle:e}),(m,n)=>{const i=Q;return f(),q("div",{class:"esri-widget esri-expand esri-component esri-widget--button custom-toolbar",onClick:y},[A(i,{size:16,class:"tool-icon"},{default:D(()=>[B(d).collapsed?(f(),x(B(U),{key:0,icon:m.icon},null,8,["icon"])):(f(),x(B(J),{key:1}))]),_:1})])}}}),yt=Z(W,[["__scopeId","data-v-ef0fe076"]]);export{yt as _,mt as a,at as b,ct as c,dt as d,rt as e,st as u};
