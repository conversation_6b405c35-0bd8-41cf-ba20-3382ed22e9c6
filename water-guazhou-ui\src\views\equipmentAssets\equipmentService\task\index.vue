<!-- 保养任务 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
    <SLDrawer
      ref="refForm1"
      :config="addEquipment"
    ></SLDrawer>
    <SLDrawer
      ref="refmaintainance"
      :config="maintainanceForm"
    ></SLDrawer>
    <SLDrawer
      ref="refdevicesCare"
      :config="devicesCareForm"
    ></SLDrawer>
    <SLDrawer
      ref="refpicture"
      :config="pictureForm"
    ></SLDrawer>
    <SLDrawer
      ref="refreview"
      :config="reviewForm"
    ></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { formatDate } from '@/utils/DateFormatter'
import {
  getAreaTreeSearch
} from '@/api/equipment_assets/equipmentManage'
import {
  getMaintenanceTaskSerch,
  postMaintenancePlan,
  deleteMaintenanceTask,
  postMaintainance,
  putMaintenanceTask,
  getMaintenancePlanSerch,
  postMaintenanceTask,
  getMaintenanceTaskDetails
} from '@/api/equipment_assets/equipmentService'
import { getInspectionTeamSerch } from '@/api/equipment_assets/equipmentInspection'
import { getDeviceStorageJournalSerch } from '@/api/equipment_assets/ledgerManagement'
import { getWaterSupplyTree } from '@/api/company_org'
import { getUserList } from '@/api/user/index'
import { traverse, uniqueFunc } from '@/utils/GlobalHelper'
import { removeSlash } from '@/utils/removeIdSlash'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<ISLDrawerIns>()

const refForm1 = ref<ISLDrawerIns>()

const refmaintainance = ref<ISLDrawerIns>()

const refdevicesCare = ref<ISLDrawerIns>()

const refreview = ref<ISLDrawerIns>()

const chosen = ref([])

const refpicture = ref<ISLDrawerIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '任务编号', field: 'code', type: 'input' },
    // { label: '任务名称', field: 'planName', type: 'input' },
    { label: '保养计划', field: 'planName', type: 'select', options: computed(() => data.planList) as any },
    { label: '保养班组', field: 'teamName', type: 'input' },
    { label: '保养人员', field: 'userName', type: 'input' },
    {
      type: 'select',
      label: '审核状态',
      field: 'auditStatus',
      options: [
        { label: '全部', value: '' },
        { label: '未审核', value: '0' },
        { label: '拒绝', value: '1' },
        { label: '通过', value: '2' }
      ],
      onChange: () => refreshData()
    },
    {
      type: 'daterange',
      format: 'x',
      label: '开始时间',
      field: 'startEndTime'
    },
    {
      type: 'daterange',
      format: 'x',
      label: '结束时间',
      field: 'endEndTime'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          type: 'success',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '批量删除',
          type: 'danger',
          icon: ICONS.DELETE,
          click: () => {
            if (TableConfig.selectList?.length === 0) {
              ElMessage.warning('请选中至少一条数据')
              return
            }
            haneleDelete()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  handleSelectChange: (val: any) => {
    TableConfig.selectList = val
  },
  columns: [
    { label: '任务编号', prop: 'code' },
    { label: '任务名称', prop: 'name' },
    { label: '任务类型', prop: 'type' },
    { label: '执行班组', prop: 'teamName' },
    { label: '执行人员', prop: 'userName' },
    { label: '预计开始', prop: 'startTime', formatter: row => formatDate(row.startTime, 'YYYY-MM-DD HH:mm') },
    { label: '预计结束', prop: 'endTime', formatter: row => formatDate(row.endTime, 'YYYY-MM-DD HH:mm') },
    { label: '实际开始', prop: 'realStartTime', formatter: row => formatDate(row.realStartTime, 'YYYY-MM-DD HH:mm') },
    { label: '实际结束', prop: 'realEndTime', formatter: row => formatDate(row.realEndTime, 'YYYY-MM-DD HH:mm') },
    { label: '说明/备注', prop: 'remark' },
    { label: '添加人', prop: 'creatorName' },
    { label: '添加时间', prop: 'createTime', formatter: row => formatDate(row.createTime, 'YYYY-MM-DD HH:mm') },
    { label: '任务状态', prop: 'status', formatter: row => status[row.status * 1] },
    {
      label: '审核状态',
      prop: 'auditStatus',
      formatter: row => auditStatus[(row?.auditStatus || 0) * 1],
      tag: true,
      tagColor: row => (row.auditStatus === '0' ? '#13ce66' : '#6885bf')

    }
  ],
  operationWidth: '200px',
  operations: [
    {
      hide: row => row.status === '2' || row.status === '3',
      type: 'primary',
      isTextBtn: true,
      text: '保养',
      perm: $btnPerms('RoleManageEdit'),
      click: row => {
        maintainanceForm.title = '保养'
        data.selected = row
        const value: any = data.teamList.find((item: any) => item.value === row.teamId) || {}
        data.userList = value.maintainCircuitTeamCList.map(item => {
          return { label: item.userName, value: item.userId }
        })
        data.getMaintenanceTaskDetailsValue(row.id)
        maintainanceForm.defaultValue = { ...(row) || {} }
        refmaintainance.value?.openDrawer()
      }
    },
    {
      hide: row => !((row.status === '2' || row.status === '3') && row.auditStatus === '0'),
      type: 'primary',
      isTextBtn: true,
      text: '审核',
      perm: $btnPerms('RoleManageEdit'),
      click: row => {
        reviewForm.defaultValue = { id: row.id }
        refreview.value?.openDrawer()
      }
    },
    {
      hide: row => row.status !== '2' && row.status !== '3',
      type: 'primary',
      isTextBtn: true,
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      click: row => {
        maintainanceForm.title = '详情'
        const value: any = data.teamList.find((item: any) => item.value === row.teamId) || {}
        data.userList = value.maintainCircuitTeamCList.map(item => {
          return { label: item.userName, value: item.userId }
        })
        data.getMaintenanceTaskDetailsValue(row.id)
        maintainanceForm.defaultValue = { ...(row) || {} }
        refmaintainance.value?.openDrawer()
      }
    },
    {
      hide: row => row.status !== '0',
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      // icon: ICONS.EDIT,
      click: row => clickDetail(row)
    },
    {
      hide: row => row.status !== '0',
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      // icon: ICONS.DELETE,
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '130px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true
    let val = '添加成功'
    if (params.id) { val = '修改成功' }
    if (params.id) {
      postMaintenanceTask(params).then(() => {
        addOrUpdateConfig.submitting = false
        refreshData()
        ElMessage.success(val)
        refForm.value?.closeDrawer()
      }).catch(error => {
        addOrUpdateConfig.submitting = false
        ElMessage.warning(error)
      })
    } else {
      params.maintainPlanCList = params.maintainTaskCList
      delete params.maintainTaskCList
      params.maintainPlanCList.map(item => item.mmDeviceId = item.serialId)
      postMaintenancePlan(params).then(() => {
        addOrUpdateConfig.submitting = false
        refreshData()
        ElMessage.success(val)
        refForm.value?.closeDrawer()
      }).catch(error => {
        addOrUpdateConfig.submitting = false
        ElMessage.warning(error)
      })
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '任务名称',
          field: 'name',
          rules: [{ required: true, message: '请输入任务名称' }]
        }, {
          xl: 8,
          type: 'select',
          label: '执行班组',
          field: 'teamId',
          rules: [{ required: true, message: '请选择执行班组' }],
          options: computed(() => data.teamList) as any,
          onChange: val => {
            const value: any = data.teamList.find((item: any) => item.value === val) || {}
            data.userList = value.maintainCircuitTeamCList.map(item => {
              return { label: item.userName, value: item.userId }
            })
          }
        }, {
          xl: 8,
          type: 'select',
          label: '执行人员',
          field: 'userId',
          rules: [{ required: true, message: '请选择执行人员' }],
          options: computed(() => data.userList) as any
        }, {
          xl: 8,
          type: 'date',
          label: '预计开始',
          field: 'startTime',
          rules: [{ required: true, message: '请输入预计开始' }],
          format: 'x'
        }, {
          xl: 8,
          type: 'date',
          label: '预计结束',
          field: 'endTime',
          rules: [{ required: true, message: '请输入预计结束' }],
          format: 'x'
        }, {
          xl: 8,
          type: 'department-user',
          label: '审核人员',
          field: 'auditor',
          rules: [{ required: true, message: '请选择审核人员' }]
        }, {
          type: 'table',
          field: 'maintainTaskCList',
          config: {
            indexVisible: true,
            height: '350px',
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加设备',
                        perm: true,
                        click: () => {
                          refForm1.value?.openDrawer()
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              }, {
                label: '设备名称',
                prop: 'name'
              }, {
                label: '规格/型号',
                prop: 'model'
              }, {
                label: '所属大类',
                prop: 'topType'
              }, {
                label: '所属类别',
                prop: 'type'
              }, {
                label: '安装区域',
                prop: 'installAddressName'
              }, {
                label: '安装位置',
                prop: 'detailInstallAddressName'
              }, {
                label: '最后保养时间',
                prop: 'lastMaintainanceTime',
                formatter: row => formatDate(row.time, 'YYYY-MM-DD')
              }

            ],
            operations: [
              {
                type: 'danger',
                text: '移除',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: row => data.selectList = data.selectList.filter(item => item.id !== row.id)
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 保养
const maintainanceForm = reactive<IDrawerConfig>({
  title: '保养',
  labelWidth: '130px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '任务名称',
          field: 'name',
          rules: [{ required: true, message: '请输入任务名称' }]
        }, {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '执行班组',
          field: 'teamId',
          rules: [{ required: true, message: '请选择执行班组' }],
          options: computed(() => data.teamList) as any,
          onChange: val => {
            const value: any = data.teamList.find((item: any) => item.value === val) || {}
            data.userList = value.maintainCircuitTeamCList.map(item => {
              return { label: item.userName, value: item.userId }
            })
          }
        }, {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '执行人员',
          field: 'userId',
          rules: [{ required: true, message: '请选择执行人员' }],
          options: computed(() => data.userList) as any
        }, {
          readonly: true,
          xl: 8,
          type: 'date',
          label: '预计开始',
          field: 'startTime',
          rules: [{ required: true, message: '请输入预计开始' }],
          format: 'x'
        }, {
          readonly: true,
          xl: 8,
          type: 'date',
          label: '预计结束',
          field: 'endTime',
          rules: [{ required: true, message: '请输入预计结束' }],
          format: 'x'
        }, {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '审核部门',
          field: 'auditorDepartmentName'
        }, {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '审核人员',
          field: 'auditorName',
          rules: [{ required: true, message: '请选择审核人员' }]
        }, {
          type: 'table',
          field: 'maintainTaskCList',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              }, {
                label: '设备名称',
                prop: 'name'
              }, {
                label: '规格/型号',
                prop: 'model'
              }, {
                label: '所属大类',
                prop: 'topType'
              }, {
                label: '所属类别',
                prop: 'type'
              }, {
                label: '安装区域',
                prop: 'installAddressName'
              }, {
                label: '安装位置',
                prop: 'detailInstallAddressName'
              }, {
                label: '最后保养时间',
                prop: 'lastMaintainanceTime',
                formatter: row => formatDate(row.time, 'YYYY-MM-DD')
              }, {
                label: '保养状态',
                prop: 'status',
                formatter: row => (row.status === '0' ? '未保养' : '已保养')
              }

            ],
            operations: [
              {
                hide: row => row.status === '1',
                text: '保养',
                perm: $btnPerms('RoleManageDelete'),
                click: row => {
                  devicesCareForm.defaultValue = { id: row.id, mainId: row.mainId }
                  refdevicesCare.value?.openDrawer()
                }
              },
              {
                hide: row => row.status === '0',
                text: '详情',
                perm: $btnPerms('RoleManageDelete'),
                click: row => {
                  pictureForm.defaultValue = { ...row }
                  refpicture.value?.openDrawer()
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 设备保养
const devicesCareForm = reactive<IDrawerConfig>({
  title: '保养',
  labelWidth: '130px',
  width: 500,
  submitting: false,
  submit: (params: any) => {
    devicesCareForm.submitting = true
    postMaintainance(params).then(res => {
      if (res.data.code === 200) {
        refreshData()
        ElMessage.success('保养成功')
        devicesCareForm.submitting = false
        refdevicesCare.value?.closeDrawer()
        getMaintenanceTaskDetails(data.selected.id).then(res => {
          data.selectList = res.data.data.maintainTaskCList || []
        })
      } else {
        ElMessage.warning(res.data.message)
      }
    }).catch(error => {
      devicesCareForm.submitting = false
      ElMessage.warning(error)
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'image',
          label: '图片',
          field: 'img'
        },
        {
          type: 'input',
          label: '说明',
          field: 'remark',
          rules: [{ required: true, message: '请输入说明' }]
        },
        {
          type: 'select',
          label: '完成状态',
          field: 'status',
          options: [
            { label: '未完成', value: 0 },
            { label: '已完成', value: 1 }
          ],
          rules: [{ required: true, message: '请输入完成状态' }]
        }
      ]
    }
  ]
})

// 保养图片
const pictureForm = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '130px',
  width: 500,
  submitting: false,
  defaultValue: {},
  group: [
    {
      fields: [
        {
          readonly: true,
          type: 'image',
          label: '图片',
          field: 'img'
        },
        {
          disabled: true,
          type: 'input',
          label: '说明',
          field: 'remark'
        },
        {
          readonly: true,
          type: 'select',
          label: '完成状态',
          field: 'status',
          options: [
            { label: '未完成', value: '0' },
            { label: '已完成', value: '1' }
          ]
        }
      ]
    }
  ]
})

// 审核
const reviewForm = reactive<IDrawerConfig>({
  title: '审核',
  labelWidth: '130px',
  width: 500,
  submitting: false,
  submit: (params: any) => {
    reviewForm.submitting = true
    putMaintenanceTask(params).then(res => {
      reviewForm.submitting = false
      if (res.data.code === 500) {
        ElMessage.warning(res.data.message)
      } else {
        refreshData()
        ElMessage.success('审核成功')
        refreview.value?.closeDrawer()
      }
    }).catch(error => {
      reviewForm.submitting = false
      ElMessage.warning(error)
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '审核状态',
          field: 'auditStatus',
          options: [
            { label: '拒绝', value: 1 },
            { label: '通过', value: 2 }
          ],
          rules: [{ required: true, message: '请输入审核状态' }]
        },
        {
          type: 'textarea',
          label: '审核意见',
          field: 'auditRemark',
          rules: [{ required: true, message: '请输入审核意见' }]
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  labelWidth: '130px',
  submit: (params: any, status?: boolean) => {
    if (status) {
      delete params.drive
      data.getDevice(params)
    } else {
      data.selectList = [...data.selectList, ...chosen.value]
      data.selectList = uniqueFunc(data.selectList, ['id'])
      refForm1.value?.closeDrawer()
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '标签编码',
          field: 'deviceLabelCode'
        }, {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        }, {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        }, {
          xl: 8,
          label: '安装区域',
          field: 'areaId',
          type: 'select-tree',
          checkStrictly: true,
          options: computed(() => data.installationArea) as any
        },
        // {
        //   xl: 8,
        //   type: 'select',
        //   label: '安装位置',
        //   field: 'detailInstallAddressName'
        // },
        {
          xl: 12,
          type: 'daterange',
          label: '上次保养时间',
          field: 'lastMaintainanceTime'
        }, {
          type: 'table',
          field: 'drive',
          config: {
            indexVisible: true,
            height: '350px',
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            dataList: computed(() => data.deviceValue) as any,
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '查询',
                        type: 'success',
                        perm: true,
                        click: () => {
                          refForm1.value?.Submit(true)
                        }
                      }, {
                        text: '重置',
                        type: 'primary',
                        perm: true,
                        click: () => {
                          refForm1.value?.resetForm()
                          refForm1.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '标签编码',
                prop: 'deviceLabelCode'
              }, {
                label: '设备名称',
                prop: 'name'
              }, {
                label: '规格型号',
                prop: 'model'
              }, {
                label: '所属大类',
                prop: 'topType'
              }, {
                label: '所属类别',
                prop: 'type'
              }, {
                label: '安装区域',
                prop: 'installAddressName'
              }, {
                label: '安装位置',
                prop: 'detailInstallAddressName'
              }, {
                label: '最后保养时间',
                prop: 'lastMaintainanceTime',
                formatter: row => formatDate(row.time, 'YYYY-MM-DD')
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  data.selectList = []
  chosen.value = []
  addOrUpdateConfig.defaultValue = {}
  refForm.value?.openDrawer()
}

// 打开详情
const clickDetail = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  const value: any = data.teamList.find((item: any) => item.value === row.teamId) || {}
  data.userList = value.maintainCircuitTeamCList.map(item => {
    return { label: item.userName, value: item.userId }
  })
  data.getUserListValue(row.auditorDepartment)
  data.selectList = row.maintainTaskCList
  data.getMaintenanceTaskDetailsValue(row.id)
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDrawer()
}

const haneleDelete = (row?: { id: string }) => {
  SLConfirm('确定删除该保养任务, 是否继续?', '删除提示').then(() => {
    let ids: string[] = []
    if (row) {
      ids = [row.id]
    } else {
      ids = TableConfig?.selectList?.map(node => node.id) ?? []
    }
    deleteMaintenanceTask(ids).then(() => {
      refreshData()
      ElMessage.success('删除成功')
    })
  })
}

const data = reactive({
  // 计划
  planList: [],
  // 班组
  teamList: [],
  // 执行人员
  userList: [] as any[],
  // 设备列表
  deviceValue: [] as any[],
  // 选中的设备
  selectList: [] as any[],
  // 部门
  WaterSupplyTree: [],
  // 安装区域
  installationArea: [],
  // 部门用户
  departmentUser: [],
  // 任务详情
  taskDetails: [] as any[],
  // 选中任务
  selected: {} as any,
  // 获取设备
  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    }
    if (params.lastMaintainanceTime && params.lastMaintainanceTime.length > 1) {
      params.lastMaintainanceTimeFrom = params.lastMaintainanceTime[0]
      params.lastMaintainanceTimeTo = params.lastMaintainanceTime[1]
    }
    delete params.lastMaintainanceTime
    getDeviceStorageJournalSerch(params).then(res => {
      data.deviceValue = res.data.data.data || []
    })
  },
  // 班组
  getTeam: () => {
    const params = {
      size: 99999,
      page: 1,
      type: '保养班组',
      name: ''
    }
    getInspectionTeamSerch(params).then(res => {
      const value = res.data.data.data || []
      data.teamList = value.map(item => {
        return { label: item.name, value: item.id, maintainCircuitTeamCList: item.maintainCircuitTeamCList }
      })
    })
  },
  // 计划
  getPlan: () => {
    const params = {
      size: 99999,
      page: 1,
      startStartTime: ''
    }
    getMaintenancePlanSerch(params).then(res => {
      const value = res.data.data.data || []
      data.planList = value.map(item => {
        return { label: item.name, value: item.name }
      })
    })
  },
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.departmentUser = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  },
  // 获取安装区域
  getAreaTreeValue: () => {
    const params = {
      page: 1,
      size: 99999,
      shortName: ''
    }
    getAreaTreeSearch(params).then(res => {
      data.installationArea = traverse(res.data.data.data || [])
    })
  },
  getMaintenanceTaskDetailsValue: (id: string) => {
    getMaintenanceTaskDetails(id).then(res => {
      data.selectList = res.data.data.maintainTaskCList || []
    })
  }
})

const status = ['待接收', '已接收', '按时完成', '超时完成', '未完成']
const auditStatus = ['未审核', '拒绝', '通过']

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    startStartTime: '',
    startEndTime: '',
    endStartTime: '',
    endEndTime: '',
    ...(refSearch.value?.queryParams || {})
  }
  if (refSearch.value?.queryParams && refSearch.value?.queryParams.startEndTime && refSearch.value?.queryParams.startEndTime.length > 1) {
    params.startStartTime = refSearch.value?.queryParams.startEndTime[0]
    params.startEndTime = refSearch.value?.queryParams.startEndTime[1]
  }
  if (refSearch.value?.queryParams && refSearch.value?.queryParams.endEndTime && refSearch.value?.queryParams.endEndTime.length > 1) {
    params.endStartTime = refSearch.value?.queryParams.endEndTime[0]
    params.endEndTime = refSearch.value?.queryParams.endEndTime[1]
  }
  getMaintenanceTaskSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}
onMounted(() => {
  refreshData()
  data.getTeam()
  data.getPlan()
  data.getDevice()
  data.getWaterSupplyTreeValue()
  data.getAreaTreeValue()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
