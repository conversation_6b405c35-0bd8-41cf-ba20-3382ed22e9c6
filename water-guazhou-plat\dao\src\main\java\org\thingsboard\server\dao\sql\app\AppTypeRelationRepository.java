package org.thingsboard.server.dao.sql.app;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.AppTypeRelation;

import java.util.List;

public interface AppTypeRelationRepository extends JpaRepository<AppTypeRelation, String> {
    List<AppTypeRelation> findByAppTypeId(String appTypeId);

    @Query("SELECT a " +
            "FROM AppTypeRelation a, MenuPoolEntity mp " +
            "WHERE a.menuPoolId = mp.id AND a.appTypeId = ?1 AND mp.parentId <> '1b21dd2192ef4708080808080808080'")
    List<AppTypeRelation> findMenuByTypeIdAndMenuParentNotRoot(@Param("username") String appTypeId);

    int deleteByAppTypeId(String appTypeId);
}
