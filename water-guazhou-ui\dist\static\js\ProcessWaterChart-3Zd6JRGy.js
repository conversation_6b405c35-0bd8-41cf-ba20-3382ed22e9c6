import{d as u,c as h,am as f,o as y,g as _,n as v,p as c,c4 as x,C as g}from"./index-r0dFAfgr.js";const b={class:"chart-container"},w=u({__name:"ProcessWaterChart",props:{chartData:{type:Array,default:()=>[]}},setup(i){const o=i,s=h(null);let t=null;const p=()=>{s.value&&(t=x(s.value),n())},n=()=>{if(!t)return;const e=o.chartData,r=e.map(a=>a.stationName),l=e.map(a=>a.processWater),d=e.map(a=>a.intermediateWater),m={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["中间水量","进行中水"],top:10},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:r},yAxis:{type:"value",name:"水量(m³)"},series:[{name:"中间水量",type:"bar",data:d,itemStyle:{color:"#5470c6"},label:{show:!0,position:"top"}},{name:"进行中水",type:"bar",data:l,itemStyle:{color:"#91cc75"},label:{show:!0,position:"top"}}]};t.setOption(m)};return f(()=>o.chartData,()=>{n()},{deep:!0}),y(()=>{p(),window.addEventListener("resize",()=>{t&&t.resize()})}),(e,r)=>(_(),v("div",b,[r[0]||(r[0]=c("div",{class:"chart-title"},"进行中水与中间水量",-1)),c("div",{ref_key:"chartRef",ref:s,class:"chart"},null,512)]))}}),W=g(w,[["__scopeId","data-v-aa752f7f"]]);export{W as default};
