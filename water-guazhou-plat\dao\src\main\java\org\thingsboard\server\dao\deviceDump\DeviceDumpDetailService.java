package org.thingsboard.server.dao.deviceDump;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDumpDetail;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpDetailSaveRequest;

import java.util.List;

public interface DeviceDumpDetailService {
    /**
     * 分页条件查询设备报废单条目（实际报废的设备）
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceDumpDetail> findAllConditional(DeviceDumpDetailPageRequest request);

    /**
     * 保存所有设备报废单条目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    List<DeviceDumpDetail> saveAll(List<DeviceDumpDetailSaveRequest> entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceDumpDetail entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean removeAll(List<String> idList);

    /**
     * 通过父级id删除所有
     *
     * @param id 唯一标识
     * @return 是否成功
     */
    boolean removeAllByMainId(String id);

}
