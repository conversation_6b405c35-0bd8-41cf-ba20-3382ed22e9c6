package org.thingsboard.server.dao.sql.smartManagement.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.CircuitSettings;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.settings.CircuitSettingsPageRequest;

@Mapper
public interface CircuitSettingsMapper extends BaseMapper<CircuitSettings> {
    IPage<CircuitSettings> findByPage(CircuitSettingsPageRequest request);

    boolean update(CircuitSettings entity);

    CircuitSettings findById(String id);
}