<!-- 养护计划 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { Plus, Search } from '@element-plus/icons-vue';
import { SLConfirm } from '@/utils/Message';
import useGlobal from '@/hooks/global/useGlobal';
import useStation from '@/hooks/station/useStation';
import { formatDate } from '@/utils/DateFormatter';
import {
  getPlanList,
  delPlan,
  savePlan
} from '@/api/secondSupplyManage/stationCircuit';
import useDepartment from '@/hooks/department/useDepartment';
import { getUserList } from '@/api/user/index';
import { removeSlash } from '@/utils/removeIdSlash';

const { getAllStationOption } = useStation();
const { getDepartmentTree } = useDepartment();
const { $messageSuccess, $messageError, $messageWarning } = useGlobal();
const refForm = ref<IDialogFormIns>();
const refSearch = ref<ISearchIns>();

const state = reactive<{
  optionUsers: any;
  auditUsers: any;
  searchAuditUsers: any;
  searchOptionUsers: any;
  stationOptionList: any;
  departmentTree: any;
}>({
  optionUsers: [],
  auditUsers: [],
  searchOptionUsers: [],
  searchAuditUsers: [],
  stationOptionList: [],
  departmentTree: []
});

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      label: '关键字',
      field: 'keyword',
      type: 'input',
      placeholder: '请输入关键字'
    },
    {
      label: '维保部门',
      field: 'optionDep',
      type: 'select-tree',
      options: computed(() => state.departmentTree) as any,
      placeholder: '请选择维保部门',
      onChange: async (val) => {
        state.searchOptionUsers = await userList(val);
      }
    },
    {
      label: '维保人员',
      field: 'optionUserId',
      type: 'select',
      placeholder: '请选择维保人员',
      options: computed(() => state.searchOptionUsers) as any
    },
    {
      label: '审核部门',
      field: 'auditDep',
      type: 'select-tree',
      options: computed(() => state.departmentTree) as any,
      placeholder: '请选审核部门',
      onChange: async (val) => {
        state.searchAuditUsers = await userList(val);
      }
    },
    {
      label: '审核人员',
      field: 'auditUserId',
      type: 'select',
      placeholder: '请选择审核人',
      options: computed(() => state.searchAuditUsers) as any
    },
    {
      label: '开始时间',
      field: 'beginStartTime',
      type: 'daterange',
      format: 'YYYY-MM-DD',
      placeholder: '请选择开始时间',
      onChange: (val) => {
        const field = SearchConfig.filters?.find(
          (item) => item.field === 'endStartTime'
        ) as any;
        field.disabledDate = function (date: any) {
          const endDate = val[1];
          return date < new Date(endDate);
        };
      }
    },
    {
      label: '结束时间',
      field: 'endStartTime',
      type: 'daterange',
      format: 'YYYY-MM-DD',
      placeholder: '请选择结束时间',
      onChange: (val) => {
        const field = SearchConfig.filters?.find(
          (item) => item.field === 'beginStartTime'
        ) as any;
        field.disabledDate = function (date: any) {
          const endDate = val[0];
          return date > new Date(endDate);
        };
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          text: '新增',
          perm: true,
          svgIcon: shallowRef(Plus),
          type: 'success',
          click: () => {
            FormConfig.defaultValue = {};
            FormConfig.title = '新增';
            refForm.value?.openDialog();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'name', label: '计划名称' },
    { prop: 'type', label: '业务类型' },
    { prop: 'days', label: '消耗天数' },
    {
      prop: 'startTime',
      label: '计划开始时间',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      prop: 'endTime',
      label: '计划结束时间',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      prop: 'createTime',
      label: '添加时间',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operations: [
    {
      text: '编辑',
      isTextBtn: true,
      perm: true,
      icon: 'iconfont icon-bianji',
      click: async (row) => {
        FormConfig.title = '编辑';
        FormConfig.defaultValue = {
          ...row,
          startTime: dayjs(row.startTime).format('YYYY-MM-DD'),
          endTime: dayjs(row.endTime).format('YYYY-MM-DD'),
          stationIds: row.stationIds?.split(',') || []
        };
        state.optionUsers = await userList(row.optionDep);
        state.auditUsers = await userList(row.auditDep);
        refForm.value?.openDialog();
      }
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: true,
      type: 'danger',
      icon: 'iconfont icon-shanchu',
      click: (row) => handleDelete(row)
    }
  ],
  operationWidth: '200px',
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size;
      TableConfig.pagination.page = page;
      refreshData();
    }
  }
});
const FormConfig = reactive<IDialogFormConfig>({
  title: '新增',
  defaultValue: {},
  dialogWidth: 900,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '计划名称',
          field: 'name',
          rules: [{ required: true, message: '请填写计划名称' }]
        },
        {
          xs: 12,
          label: '业务类型',
          field: 'type',
          type: 'select',
          placeholder: '请选择业务类型',
          options: [
            { label: '维修业务', value: '维修业务' },
            { label: '保养业务', value: '保养业务' },
            { label: '清洗业务', value: '清洗业务' }
          ]
        },
        {
          xs: 12,
          type: 'number',
          label: '消耗天数',
          field: 'days',
          rules: [{ required: true, message: '请填写各泵房消耗天数' }],
          onChange: (val) => {
            const dataForm = refForm.value?.refForm?.dataForm;
            if (dataForm.startTime) {
              FormConfig.defaultValue = {
                ...dataForm,
                endTime: dayjs(dataForm.startTime)
                  .add(val, 'day')
                  .format('YYYY-MM-DD')
              };
              refForm.value?.refForm?.resetForm();
            }
          }
        },
        {
          xs: 12,
          type: 'date',
          label: '开始时间',
          field: 'startTime',
          format: 'YYYY-MM-DD',
          rules: [{ required: true, message: '请选择开始时间' }],
          disabledDate: (date: any) => {
            return new Date() > date;
          },
          onChange: (val) => {
            const dataForm = refForm.value?.refForm?.dataForm;
            if (dataForm.days > 0) {
              FormConfig.defaultValue = {
                ...dataForm,
                endTime: dayjs(val)
                  .add(dataForm.days, 'day')
                  .format('YYYY-MM-DD')
              };
              refForm.value?.refForm?.resetForm();
            }
          }
        },
        {
          xs: 12,
          type: 'date',
          label: '结束时间',
          field: 'endTime',
          format: 'YYYY-MM-DD',
          readonly: true
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '维保部门',
          field: 'optionDep',
          options: computed(() => state.departmentTree) as any,
          onChange: async (val) => {
            state.optionUsers = await userList(val);
          }
        },
        {
          xs: 12,
          type: 'select',
          label: '维保人员',
          field: 'optionUserId',
          options: computed(() => state.optionUsers) as any
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '审核部门',
          field: 'auditDep',
          options: computed(() => state.departmentTree) as any,
          onChange: async (val) => {
            state.auditUsers = await userList(val);
          }
        },
        {
          xs: 12,
          type: 'select',
          label: '审核人员',
          field: 'auditUserId',
          options: computed(() => state.auditUsers) as any
        },
        // {
        //   type: 'checkbox',
        //   field: '',
        //   label: '泵站列表',
        //   rules: [{ required: true }]
        // },
        {
          type: 'checkbox',
          field: 'stationIds',
          label: '泵站列表',
          rules: [{ required: true, message: '请选择泵站列表' }],
          noBorder: false,
          options: computed(() => state.stationOptionList) as any
        },
        {
          xs: 24,
          type: 'textarea',
          minRow: 5,
          label: '备注',
          field: 'remark',
          colStyles: {
            marginTop: '20px'
          }
        }
      ]
    }
  ],
  submit: (params: any) => {
    savePlan({
      ...params,
      stationType: '泵站',
      stationIds: params.stationIds.join(',')
    })
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('保存成功');
        } else {
          $messageError('保存失败');
        }
        refreshData();
        refForm.value?.closeDialog();
      })
      .catch((err) => {
        $messageError(err);
      });
  }
});
// 获取部门用户
const userList = async (val: any) => {
  const res = await getUserList({ pid: val });
  const value = res.data.data.data || [];
  const UserList = value.map((item) => {
    return { label: item.firstName, value: removeSlash(item.id.id) };
  });

  return UserList;
};
const refreshData = async () => {
  TableConfig.loading = true;
  const query = refSearch.value?.queryParams || {};
  const [bStart, bEnd] = query.beginStartTime || [];
  const [eStart, eEnd] = query.endStartTime || [];
  const newParams: any = {
    ...query,
    beginStartTime: bStart,
    beginEndTime: bEnd,
    endStartTime: eStart,
    endEndTime: eEnd,
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1
  };
  getPlanList(newParams)
    .then((res) => {
      TableConfig.dataList = res.data?.data?.data || [];
      TableConfig.pagination.total = res.data?.data?.total || 0;
      TableConfig.loading = false;
    })
    .catch((err) => {
      $messageError(err);
      TableConfig.loading = false;
    });
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除指定养护计划?', '删除提示').then(() => {
    delPlan([row.id])
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('删除成功');
          refreshData();
        } else {
          $messageError('删除失败');
        }
      })
      .catch((err) => {
        $messageError(err);
      });
  });
};

onMounted(async () => {
  state.stationOptionList = await getAllStationOption('泵站');
  state.departmentTree = await getDepartmentTree(2);
  refreshData();
});
</script>
<style lang="scss" scoped></style>
