"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7374],{84552:(e,t,r)=>{r.d(t,{Z:()=>h});var i=r(43697),s=r(2368),n=r(96674),a=r(35463),l=r(5600),o=(r(75215),r(67676),r(36030)),d=r(52011),u=r(78981);let p=class extends((0,s.J)(n.wq)){constructor(e){super(e),this.unit="milliseconds",this.value=0}toMilliseconds(){return(0,a.rJ)(this.value,this.unit,"milliseconds")}};(0,i._)([(0,o.J)(u.v,{nonNullable:!0})],p.prototype,"unit",void 0),(0,i._)([(0,l.Cb)({type:Number,json:{write:!0},nonNullable:!0})],p.prototype,"value",void 0),p=(0,i._)([(0,d.j)("esri.TimeInterval")],p);const h=p},3920:(e,t,r)=>{r.d(t,{p:()=>d,r:()=>u});var i=r(43697),s=r(15923),n=r(61247),a=r(5600),l=r(52011),o=r(72762);const d=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new o.t}};return(0,i._)([(0,a.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,i._)([(0,a.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,i._)([(0,l.j)("esri.core.HandleOwner")],t),t};let u=class extends(d(s.Z)){};u=(0,i._)([(0,l.j)("esri.core.HandleOwner")],u)},72762:(e,t,r)=>{r.d(t,{t:()=>p});var i=r(43697),s=r(15923),n=r(61247),a=r(70586),l=r(17445),o=r(1654),d=r(5600),u=r(52011);let p=class extends s.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,l.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,l.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:i=!1}={}){const s=++this._handleId;return this._handles.add([(0,l.on)(e,"after-changes",this._createSyncUpdatingCallback(),l.Z_),(0,l.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:i?e=>t({added:[],removed:e.toArray()}):void 0})],s),{remove:()=>this._handles.remove(s)}}addPromise(e){if((0,a.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(h)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},i){const s=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,s);const n=i(e,t,r);return this._handles.add(n,s),{remove:()=>this._handles.remove(s)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),i=(0,l.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(i,t),i}_createSyncUpdatingCallback(){return()=>{this._handles.remove(h),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,o.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(h))})),h)}}};(0,i._)([(0,d.Cb)({readOnly:!0})],p.prototype,"updating",void 0),p=(0,i._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],p);const h=-42},91040:(e,t,r)=>{r.d(t,{yZ:()=>s});var i=r(67900);function s(e,t){const r=t||e.extent,s=e.width,n=(0,i.c9)(r&&r.spatialReference);return r&&s?r.width/s*n*i.hd*96:0}},27374:(e,t,r)=>{r.r(t),r.d(t,{default:()=>q});var i=r(43697),s=r(3172),n=r(92835),a=r(20102),l=r(3920),o=r(68668),d=r(70586),u=r(16453),p=r(95330),h=r(5600),y=r(75215),c=(r(67676),r(71715)),m=r(52011),f=r(30556),g=r(31263),v=r(6570),b=r(91040),_=r(87085),w=r(54295),S=r(7944),x=r(17287),I=r(71612),C=r(17017),T=r(38009),O=r(16859),E=r(34760),U=r(72965),L=r(10343),N=r(28294),j=r(21506),P=r(15923),F=r(42033);function D(e,t){return"floorInfo"in t&&t.floorInfo?.floorField?function(e,t){if(!e?.length)return null;const r=e.filter((e=>""!==e)).map((e=>`'${e}'`));return r.push("''"),`${t} IN (${r.join(",")}) OR ${t} IS NULL`}(e,t.floorInfo.floorField):null}var Z=r(32073);const R={visible:"visibleSublayers",definitionExpression:"layerDefs",labelingInfo:"hasDynamicLayers",labelsVisible:"hasDynamicLayers",opacity:"hasDynamicLayers",minScale:"visibleSublayers",maxScale:"visibleSublayers",renderer:"hasDynamicLayers",source:"hasDynamicLayers"};let J=class extends((0,l.p)(P.Z)){constructor(e){super(e),this.floors=null,this.scale=0}destroy(){this.layer=null}get dynamicLayers(){if(!this.hasDynamicLayers)return null;const e=this.visibleSublayers.map((e=>{const t=D(this.floors,e);return e.toExportImageJSON(t)}));return e.length?JSON.stringify(e):null}get hasDynamicLayers(){return this.layer&&(0,Z.FN)(this.visibleSublayers,this.layer.serviceSublayers,this.layer.gdbVersion)}set layer(e){this._get("layer")!==e&&(this._set("layer",e),this.handles.remove("layer"),e&&this.handles.add([e.allSublayers.on("change",(()=>this.notifyChange("visibleSublayers"))),e.on("sublayer-update",(e=>this.notifyChange(R[e.propertyName])))],"layer"))}get layers(){const e=this.visibleSublayers;return e?e.length?"show:"+e.map((e=>e.id)).join(","):"show:-1":null}get layerDefs(){const e=!!this.floors?.length,t=this.visibleSublayers.filter((t=>null!=t.definitionExpression||e&&null!=t.floorInfo));return t.length?JSON.stringify(t.reduce(((e,t)=>{const r=D(this.floors,t),i=(0,F._)(r,t.definitionExpression);return(0,d.pC)(i)&&(e[t.id]=i),e}),{})):null}get version(){this.commitProperty("layers"),this.commitProperty("layerDefs"),this.commitProperty("dynamicLayers"),this.commitProperty("timeExtent");const e=this.layer;return e&&(e.commitProperty("dpi"),e.commitProperty("imageFormat"),e.commitProperty("imageTransparency"),e.commitProperty("gdbVersion")),(this._get("version")||0)+1}get visibleSublayers(){const e=[];if(!this.layer)return e;const t=this.layer.sublayers,r=t=>{const i=this.scale,s=0===i,n=0===t.minScale||i<=t.minScale,a=0===t.maxScale||i>=t.maxScale;t.visible&&(s||n&&a)&&(t.sublayers?t.sublayers.forEach(r):e.unshift(t))};t&&t.forEach(r);const i=this._get("visibleSublayers");return!i||i.length!==e.length||i.some(((t,r)=>e[r]!==t))?e:i}toJSON(){const e=this.layer;let t={dpi:e.dpi,format:e.imageFormat,transparent:e.imageTransparency,gdbVersion:e.gdbVersion||null};return this.hasDynamicLayers&&this.dynamicLayers?t.dynamicLayers=this.dynamicLayers:t={...t,layers:this.layers,layerDefs:this.layerDefs},t}};(0,i._)([(0,h.Cb)({readOnly:!0})],J.prototype,"dynamicLayers",null),(0,i._)([(0,h.Cb)()],J.prototype,"floors",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],J.prototype,"hasDynamicLayers",null),(0,i._)([(0,h.Cb)()],J.prototype,"layer",null),(0,i._)([(0,h.Cb)({readOnly:!0})],J.prototype,"layers",null),(0,i._)([(0,h.Cb)({readOnly:!0})],J.prototype,"layerDefs",null),(0,i._)([(0,h.Cb)({type:Number})],J.prototype,"scale",void 0),(0,i._)([(0,h.Cb)(j.qG)],J.prototype,"timeExtent",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],J.prototype,"version",null),(0,i._)([(0,h.Cb)({readOnly:!0})],J.prototype,"visibleSublayers",null),J=(0,i._)([(0,m.j)("esri.layers.mixins.ExportImageParameters")],J);var M=r(90082),k=r(49867),A=r(60199),H=r(80216);let V=class extends((0,I.h)((0,N.n)((0,U.M)((0,L.x)((0,S.O)((0,x.Y)((0,T.q)((0,O.I)((0,u.R)((0,E.Q)((0,w.V)((0,C.N)((0,l.p)(_.Z)))))))))))))){constructor(...e){super(...e),this.dateFieldsTimeReference=null,this.datesInUnknownTimezone=!1,this.dpi=96,this.gdbVersion=null,this.imageFormat="png24",this.imageMaxHeight=2048,this.imageMaxWidth=2048,this.imageTransparency=!0,this.isReference=null,this.labelsVisible=!1,this.operationalLayerType="ArcGISMapServiceLayer",this.preferredTimeReference=null,this.sourceJSON=null,this.sublayers=null,this.type="map-image",this.url=null}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){const t=(0,d.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Map Service"]},e).catch(p.r9).then((()=>this._fetchService(t)))),Promise.resolve(this)}readImageFormat(e,t){const r=t.supportedImageFormatTypes;return r&&r.includes("PNG32")?"png32":"png24"}writeSublayers(e,t,r,i){if(!this.loaded||!e)return;const s=e.slice().reverse().flatten((({sublayers:e})=>e&&e.toArray().reverse())).toArray();let n=!1;if(this.capabilities&&this.capabilities.operations.supportsExportMap&&this.capabilities.exportMap?.supportsDynamicLayers){const e=(0,g.M9)(i.origin);if(e===g.s3.PORTAL_ITEM){const e=this.createSublayersForOrigin("service").sublayers;n=(0,Z.QV)(s,e,g.s3.SERVICE)}else if(e>g.s3.PORTAL_ITEM){const e=this.createSublayersForOrigin("portal-item");n=(0,Z.QV)(s,e.sublayers,(0,g.M9)(e.origin))}}const a=[],l={writeSublayerStructure:n,...i};let o=n;s.forEach((e=>{const t=e.write({},l);a.push(t),o=o||"user"===e.originOf("visible")})),a.some((e=>Object.keys(e).length>1))&&(t.layers=a),o&&(t.visibleLayers=s.filter((e=>e.visible)).map((e=>e.id)))}createExportImageParameters(e,t,r,i){const s=i&&i.pixelRatio||1;e&&this.version>=10&&(e=e.clone().shiftCentralMeridian());const n=new J({layer:this,floors:i?.floors,scale:(0,b.yZ)({extent:e,width:t})*s}),a=n.toJSON();n.destroy();const l=!i||!i.rotation||this.version<10.3?{}:{rotation:-i.rotation},o=e&&e.spatialReference,d=o.wkid||JSON.stringify(o.toJSON());a.dpi*=s;const u={};if(i?.timeExtent){const{start:e,end:t}=i.timeExtent.toJSON();u.time=e&&t&&e===t?""+e:`${e??"null"},${t??"null"}`}else this.timeInfo&&!this.timeInfo.hasLiveData&&(u.time="null,null");return{bbox:e&&e.xmin+","+e.ymin+","+e.xmax+","+e.ymax,bboxSR:d,imageSR:d,size:t+","+r,...a,...l,...u}}async fetchImage(e,t,r,i){const{data:s}=await this._fetchImage("image",e,t,r,i);return s}async fetchImageBitmap(e,t,r,i){const{data:s,url:n}=await this._fetchImage("blob",e,t,r,i);return(0,M.g)(s,n)}async fetchRecomputedExtents(e={}){const t={...e,query:{returnUpdates:!0,f:"json",...this.customParameters,token:this.apiKey}},{data:r}=await(0,s.default)(this.url,t),{extent:i,fullExtent:a,timeExtent:l}=r,o=i||a;return{fullExtent:o&&v.Z.fromJSON(o),timeExtent:l&&n.Z.fromJSON({start:l[0],end:l[1]})}}loadAll(){return(0,o.G)(this,(e=>{e(this.allSublayers)}))}serviceSupportsSpatialReference(e){return(0,A.D)(this,e)}async _fetchImage(e,t,r,i,n){const l={responseType:e,signal:n?.signal??null,query:{...this.parsedUrl.query,...this.createExportImageParameters(t,r,i,n),f:"image",...this.refreshParameters,...this.customParameters,token:this.apiKey}},o=this.parsedUrl.path+"/export";if(null!=l.query?.dynamicLayers&&!this.capabilities?.exportMap?.supportsDynamicLayers)throw new a.Z("mapimagelayer:dynamiclayer-not-supported",`service ${this.url} doesn't support dynamic layers, which is required to be able to change the sublayer's order, rendering, labeling or source.`,{query:l.query});try{const{data:e}=await(0,s.default)(o,l);return{data:e,url:o}}catch(e){if((0,p.D_)(e))throw e;throw new a.Z("mapimagelayer:image-fetch-error",`Unable to load image: ${o}`,{error:e})}}async _fetchService(e){if(this.sourceJSON)return void this.read(this.sourceJSON,{origin:"service",url:this.parsedUrl});const{data:t,ssl:r}=await(0,s.default)(this.parsedUrl.path,{query:{f:"json",...this.parsedUrl.query,...this.customParameters,token:this.apiKey},signal:e});r&&(this.url=this.url.replace(/^http:/i,"https:")),this.sourceJSON=t,this.read(t,{origin:"service",url:this.parsedUrl})}};(0,i._)([(0,h.Cb)({type:H.Z})],V.prototype,"dateFieldsTimeReference",void 0),(0,i._)([(0,h.Cb)({type:Boolean})],V.prototype,"datesInUnknownTimezone",void 0),(0,i._)([(0,h.Cb)()],V.prototype,"dpi",void 0),(0,i._)([(0,h.Cb)()],V.prototype,"gdbVersion",void 0),(0,i._)([(0,h.Cb)()],V.prototype,"imageFormat",void 0),(0,i._)([(0,c.r)("imageFormat",["supportedImageFormatTypes"])],V.prototype,"readImageFormat",null),(0,i._)([(0,h.Cb)({json:{origins:{service:{read:{source:"maxImageHeight"}}}}})],V.prototype,"imageMaxHeight",void 0),(0,i._)([(0,h.Cb)({json:{origins:{service:{read:{source:"maxImageWidth"}}}}})],V.prototype,"imageMaxWidth",void 0),(0,i._)([(0,h.Cb)()],V.prototype,"imageTransparency",void 0),(0,i._)([(0,h.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],V.prototype,"isReference",void 0),(0,i._)([(0,h.Cb)({json:{read:!1,write:!1}})],V.prototype,"labelsVisible",void 0),(0,i._)([(0,h.Cb)({type:["ArcGISMapServiceLayer"]})],V.prototype,"operationalLayerType",void 0),(0,i._)([(0,h.Cb)({json:{read:!1,write:!1}})],V.prototype,"popupEnabled",void 0),(0,i._)([(0,h.Cb)({type:H.Z})],V.prototype,"preferredTimeReference",void 0),(0,i._)([(0,h.Cb)()],V.prototype,"sourceJSON",void 0),(0,i._)([(0,h.Cb)({json:{write:{ignoreOrigin:!0}}})],V.prototype,"sublayers",void 0),(0,i._)([(0,f.c)("sublayers",{layers:{type:[k.Z]},visibleLayers:{type:[y.z8]}})],V.prototype,"writeSublayers",null),(0,i._)([(0,h.Cb)({type:["show","hide","hide-children"]})],V.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)({json:{read:!1},readOnly:!0,value:"map-image"})],V.prototype,"type",void 0),(0,i._)([(0,h.Cb)(j.HQ)],V.prototype,"url",void 0),V=(0,i._)([(0,m.j)("esri.layers.MapImageLayer")],V);const q=V},17287:(e,t,r)=>{r.d(t,{Y:()=>d});var i=r(43697),s=r(92604),n=r(70586),a=r(5600),l=(r(75215),r(67676),r(52011)),o=r(66677);const d=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,o.Qc)(this.url);if((0,n.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,o.Nm)(e,s.Z.getLogger(this.declaredClass)))}};return(0,i._)([(0,a.Cb)()],t.prototype,"title",null),(0,i._)([(0,a.Cb)({type:String})],t.prototype,"url",null),t=(0,i._)([(0,l.j)("esri.layers.mixins.ArcGISService")],t),t}},28294:(e,t,r)=>{r.d(t,{n:()=>h});var i=r(43697),s=r(92835),n=r(84552),a=r(5600),l=(r(75215),r(67676),r(71715)),o=r(52011),d=r(35671),u=r(76259),p=r(78981);const h=e=>{let t=class extends e{constructor(){super(...arguments),this.timeExtent=null,this.timeOffset=null,this.useViewTime=!0}readOffset(e,t){const r=t.timeInfo.exportOptions;if(!r)return null;const i=r.timeOffset,s=p.v.fromJSON(r.timeOffsetUnits);return i&&s?new n.Z({value:i,unit:s}):null}set timeInfo(e){(0,d.UF)(e,this.fieldsIndex),this._set("timeInfo",e)}};return(0,i._)([(0,a.Cb)({type:s.Z,json:{write:!1}})],t.prototype,"timeExtent",void 0),(0,i._)([(0,a.Cb)({type:n.Z})],t.prototype,"timeOffset",void 0),(0,i._)([(0,l.r)("service","timeOffset",["timeInfo.exportOptions"])],t.prototype,"readOffset",null),(0,i._)([(0,a.Cb)({value:null,type:u.Z,json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],t.prototype,"timeInfo",null),(0,i._)([(0,a.Cb)({type:Boolean,json:{read:{source:"timeAnimation"},write:{target:"timeAnimation"},origins:{"web-scene":{read:!1,write:!1}}}})],t.prototype,"useViewTime",void 0),t=(0,i._)([(0,o.j)("esri.layers.mixins.TemporalLayer")],t),t}},76259:(e,t,r)=>{r.d(t,{Z:()=>f});var i=r(43697),s=r(92835),n=r(84552),a=r(2368),l=r(96674),o=r(70586),d=r(5600),u=(r(75215),r(67676),r(71715)),p=r(52011),h=r(30556),y=r(80216);function c(e,t){return n.Z.fromJSON({value:e,unit:t})}let m=class extends((0,a.J)(l.wq)){constructor(e){super(e),this.cumulative=!1,this.endField=null,this.fullTimeExtent=null,this.hasLiveData=!1,this.interval=null,this.startField=null,this.timeReference=null,this.trackIdField=null,this.useTime=!0}readFullTimeExtent(e,t){if(!t.timeExtent||!Array.isArray(t.timeExtent)||2!==t.timeExtent.length)return null;const r=new Date(t.timeExtent[0]),i=new Date(t.timeExtent[1]);return new s.Z({start:r,end:i})}writeFullTimeExtent(e,t){e&&(0,o.pC)(e.start)&&(0,o.pC)(e.end)?t.timeExtent=[e.start.getTime(),e.end.getTime()]:t.timeExtent=null}readInterval(e,t){return t.timeInterval&&t.timeIntervalUnits?c(t.timeInterval,t.timeIntervalUnits):t.defaultTimeInterval&&t.defaultTimeIntervalUnits?c(t.defaultTimeInterval,t.defaultTimeIntervalUnits):null}writeInterval(e,t){t.timeInterval=e?.toJSON().value??null,t.timeIntervalUnits=e?.toJSON().unit??null}};(0,i._)([(0,d.Cb)({type:Boolean,json:{name:"exportOptions.timeDataCumulative",write:!0}})],m.prototype,"cumulative",void 0),(0,i._)([(0,d.Cb)({type:String,json:{name:"endTimeField",write:{enabled:!0,allowNull:!0}}})],m.prototype,"endField",void 0),(0,i._)([(0,d.Cb)({type:s.Z,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"fullTimeExtent",void 0),(0,i._)([(0,u.r)("fullTimeExtent",["timeExtent"])],m.prototype,"readFullTimeExtent",null),(0,i._)([(0,h.c)("fullTimeExtent")],m.prototype,"writeFullTimeExtent",null),(0,i._)([(0,d.Cb)({type:Boolean,json:{write:!0}})],m.prototype,"hasLiveData",void 0),(0,i._)([(0,d.Cb)({type:n.Z,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"interval",void 0),(0,i._)([(0,u.r)("interval",["timeInterval","timeIntervalUnits","defaultTimeInterval","defaultTimeIntervalUnits"])],m.prototype,"readInterval",null),(0,i._)([(0,h.c)("interval")],m.prototype,"writeInterval",null),(0,i._)([(0,d.Cb)({type:String,json:{name:"startTimeField",write:{enabled:!0,allowNull:!0}}})],m.prototype,"startField",void 0),(0,i._)([(0,d.Cb)({type:y.Z,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"timeReference",void 0),(0,i._)([(0,d.Cb)({type:String,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"trackIdField",void 0),(0,i._)([(0,d.Cb)({type:Boolean,json:{name:"exportOptions.useTime",write:!0}})],m.prototype,"useTime",void 0),m=(0,i._)([(0,p.j)("esri.layers.support.TimeInfo")],m);const f=m},60199:(e,t,r)=>{r.d(t,{D:()=>n});var i=r(66677);const s=[];function n(e,t){if((0,i.M8)(e.url??""))return!0;const{wkid:r}=t;for(const t of s){if((e.version??0)>=t[0])return!0;if("function"==typeof t[1]&&(t[1]=t[1]()),t[1].has(r))return!1}return!0}s.push([10.91,()=>{const e=new Set([9709,9716,9741,9761,9766]);for(let t=9712;t<=9713;t++)e.add(t);for(let t=9748;t<=9749;t++)e.add(t);for(let t=20904;t<=20932;t++)e.add(t);for(let t=21004;t<=21032;t++)e.add(t);for(let t=21207;t<=21264;t++)e.add(t);for(let t=21307;t<=21364;t++)e.add(t);for(let t=102759;t<=102760;t++)e.add(t);for(let t=102901;t<=102955;t++)e.add(t);return e}]),s.push([10.9,()=>{const e=new Set([9300,9354,9364,9367,9373,9377,9387,9456,9473,9498,9678,9680,29874,103599,103872,104028]);for(let t=9356;t<=9360;t++)e.add(t);for(let t=9404;t<=9407;t++)e.add(t);for(let t=9476;t<=9482;t++)e.add(t);for(let t=9487;t<=9494;t++)e.add(t);for(let t=9697;t<=9699;t++)e.add(t);return e}]),s.push([10.81,()=>{const e=new Set([9265,9333,103598,103699]);for(let t=9248;t<=9254;t++)e.add(t);for(let t=9271;t<=9273;t++)e.add(t);for(let t=9284;t<=9285;t++)e.add(t);for(let t=21453;t<=21463;t++)e.add(t);return e}]),s.push([10.8,()=>{const e=new Set([8088,8395,8428,8433,8531,8687,8692,8694,8699,8900,9003,9006,9009,9012,9017,9191]);for(let t=8035;t<=8036;t++)e.add(t);for(let t=8455;t<=8456;t++)e.add(t);for(let t=8518;t<=8529;t++)e.add(t);for(let t=8533;t<=8536;t++)e.add(t);for(let t=8538;t<=8540;t++)e.add(t);for(let t=8677;t<=8679;t++)e.add(t);for(let t=8902;t<=8903;t++)e.add(t);for(let t=8907;t<=8910;t++)e.add(t);for(let t=8949;t<=8951;t++)e.add(t);for(let t=8972;t<=8987;t++)e.add(t);for(let t=9039;t<=9040;t++)e.add(t);for(let t=9068;t<=9069;t++)e.add(t);for(let t=9140;t<=9141;t++)e.add(t);for(let t=9148;t<=9150;t++)e.add(t);for(let t=9153;t<=9159;t++)e.add(t);for(let t=9205;t<=9218;t++)e.add(t);for(let t=9221;t<=9222;t++)e.add(t);for(let t=54098;t<=54101;t++)e.add(t);return e}]),s.push([10.71,()=>{const e=new Set([6316]);for(let t=8351;t<=8353;t++)e.add(t);for(let t=9294;t<=9297;t++)e.add(t);for(let t=22619;t<=22621;t++)e.add(t);for(let t=103586;t<=103594;t++)e.add(t);return e}]),s.push([10.7,()=>{const e=new Set([8387,8391,8427,8545,8682,8685,8818,31370,104022,104024,104975]);for(let t=8065;t<=8068;t++)e.add(t);for(let t=8082;t<=8083;t++)e.add(t);for(let t=8379;t<=8385;t++)e.add(t);for(let t=8836;t<=8840;t++)e.add(t);for(let t=8857;t<=8860;t++)e.add(t);for(let t=53035;t<=53037;t++)e.add(t);for(let t=54090;t<=54091;t++)e.add(t);for(let t=102498;t<=102499;t++)e.add(t);return e}]),s.push([10.61,()=>new Set([102497])]),s.push([10.6,()=>{const e=new Set([7803,7805,7887,8086,8232,8237,8240,8246,8249,8252,8255,9019,9391]);for(let t=7755;t<=7787;t++)e.add(t);for(let t=7791;t<=7795;t++)e.add(t);for(let t=7799;t<=7801;t++)e.add(t);for(let t=7825;t<=7831;t++)e.add(t);for(let t=7877;t<=7878;t++)e.add(t);for(let t=7882;t<=7883;t++)e.add(t);for(let t=7991;t<=7992;t++)e.add(t);for(let t=8042;t<=8043;t++)e.add(t);for(let t=8058;t<=8059;t++)e.add(t);for(let t=8311;t<=8348;t++)e.add(t);for(let t=9060;t<=9067;t++)e.add(t);for(let t=102562;t<=102568;t++)e.add(t);for(let t=102799;t<=102900;t++)e.add(t);return e}]),s.push([10.51,()=>{const e=new Set([7683,7881,7886,7899,8888,9e3]);for(let t=8013;t<=8032;t++)e.add(t);for(let t=9053;t<=9057;t++)e.add(t);for(let t=104017;t<=104018;t++)e.add(t);for(let t=104971;t<=104974;t++)e.add(t);return e}]),s.push([10.5,()=>{const e=new Set([6962,7035,7037,7039,7041,7084,7086,7133,7798,102399]);for(let t=4087;t<=4088;t++)e.add(t);for(let t=5896;t<=5899;t++)e.add(t);for(let t=7005;t<=7007;t++)e.add(t);for(let t=7057;t<=7070;t++)e.add(t);for(let t=7073;t<=7082;t++)e.add(t);for(let t=7109;t<=7128;t++)e.add(t);for(let t=7844;t<=7859;t++)e.add(t);return e}])},78981:(e,t,r)=>{r.d(t,{v:()=>i});const i=(0,r(35454).w)()({esriTimeUnitsMilliseconds:"milliseconds",esriTimeUnitsSeconds:"seconds",esriTimeUnitsMinutes:"minutes",esriTimeUnitsHours:"hours",esriTimeUnitsDays:"days",esriTimeUnitsWeeks:"weeks",esriTimeUnitsMonths:"months",esriTimeUnitsYears:"years",esriTimeUnitsDecades:"decades",esriTimeUnitsCenturies:"centuries",esriTimeUnitsUnknown:void 0})}}]);