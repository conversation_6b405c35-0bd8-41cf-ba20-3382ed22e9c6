package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionDesign implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 编号
    private String code;

    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    @TableField(exist = false)
    private String constructionName;

    // 所属工程类型id
    @TableField(exist = false)
    private String constructionTypeId;

    // 所属工程类型名称
    @TableField(exist = false)
    private String constructionTypeName;

    // 设计分类
    private String type;

    // 设计费用，万元
    private BigDecimal cost;

    // 设计管长信息
    private String pipLengthDesign;

    // 当前状态
    @TableField(exist = false)
    private SoGeneralTaskStatus status;

    // 备注
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后修改人
    @ParseUsername
    private String updateUser;

    // 最后修改时间
    private Date updateTime;

    // 客户id
    private String tenantId;

}
