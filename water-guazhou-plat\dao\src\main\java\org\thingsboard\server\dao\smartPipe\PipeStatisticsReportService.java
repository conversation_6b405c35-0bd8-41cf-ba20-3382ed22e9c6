package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.dao.model.DTO.*;
import org.thingsboard.server.dao.model.request.PipeSaleWaterReportRequest;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-25
 */
public interface PipeStatisticsReportService {

    JSONObject getNrwReport(String type, String date, String start, String end, String tenantId, List<String> partitionIdList);

    List<PipeNrwReportDetailDTO> getNrwDetailReport(String partitionIds, String type, String date, String start, String end, String tenantId);

    List<PipeNrwReportDetailDTO> getNrwDetailReportDetail(String partitionId, String type, String date, String start, String end, String tenantId);

    List<String> getPartitionSupplyHeader(String partitionId, String tenantId);

    List<JSONObject> getPartitionSupply(String partitionId, String type, String date, String start, String end, String tenantId);

    JSONObject getPartitionInOutWater(String partitionId, String type, String date, String start, String end, String tenantId);

    List<PipeDayFlowReportDTO> getDayFlow(String partitionIds, String day, String tenantId);

    JSONObject getSaleWater(PipeSaleWaterReportRequest request);

    List<DMAOverviewDTO> getDMAOverview(String status, String partitionName, String tenantId);

    JSONObject getBigUser(Integer grade, String type, String date, String start, String end, String tenantId);

    List<PressurePassRateDTO> getPressurePassRate(String name, String date, String tenantId);

    List<LossWaterCompareToLastYearDTO> lossWaterCompareToLastYear(String name, String date, String tenantId);

    List<JSONObject> getLossPoint(String name, String tenantId);

    JSONObject getNrwForApp(String tenantId);

    Object getNrwDetailHP(String tenantId);

}
