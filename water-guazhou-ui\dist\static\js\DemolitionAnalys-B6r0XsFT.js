import{d as b,c as l,r as n,b as v,Q as w,g as I,h as _,F as L,q as x,i as c,_ as D,X as q}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as C,a as S}from"./LayerHelper-Cn-iiqxI.js";import{g as B}from"./QueryHelper-ILO3qZqg.js";import{u as F}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import M from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Nr=b({__name:"DemolitionAnalys",setup(P){const p=l(),d=l(),r=n({tabs:[],curType:"",mounted:!1,layerInfos:[],layerIds:[],loading:!1}),i={queryParams:{geometry:void 0,where:"1=1"}},y=n({group:[{fieldset:{desc:"绘制拆迁范围"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>r.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>m("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>r.loading,iconifyIcon:"ep:crop",click:()=>m("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>r.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>m("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>r.loading,iconifyIcon:"ep:delete",click:()=>a()}]},{type:"btn-group",btns:[{perm:!0,text:"分析",styles:{width:"100%"},loading:()=>r.loading,click:()=>g()}]}]}],labelPosition:"top",gutter:12}),m=t=>{var o;i.view&&((o=i.sketch)==null||o.create(t),a())},a=()=>{var t,o;(t=i.graphicsLayer)==null||t.removeAll(),i.queryParams.geometry=void 0,(o=p.value)==null||o.clearDetailData()},f=async()=>{var o,e;r.layerIds=S(i.view);const t=await q(r.layerIds);r.layerInfos=((e=(o=t.data)==null?void 0:o.result)==null?void 0:e.rows)||[]},g=async()=>{var t;v.info("正在分析，请稍候...");try{r.loading=!0,r.tabs.length=0,r.tabs=await B(r.layerIds,r.layerInfos,i.queryParams),(t=p.value)==null||t.refreshDetail(r.tabs)}catch(o){console.log(o),r.loading=!1}},{initSketch:u,destroySketch:h}=F(),s=t=>{var o;t.state==="complete"&&(i.queryParams.geometry=(o=t.graphics[0])==null?void 0:o.geometry)},k=t=>{i.view=t,i.graphicsLayer=C(i.view,{id:"pipe-analys-demolition",title:"拆迁分析"}),i.sketch=u(i.view,i.graphicsLayer,{updateCallBack:s,createCallBack:s}),f()};return w(()=>{var t;(t=i.graphicsLayer)==null||t.removeAll(),h()}),(t,o)=>{const e=D;return I(),_(M,{ref_key:"refMap",ref:p,title:"拆迁分析","full-content":!0,onMapLoaded:k,onDetailRefreshed:o[0]||(o[0]=z=>c(r).loading=!1)},{default:L(()=>[x(e,{ref_key:"refForm",ref:d,config:c(y)},null,8,["config"])]),_:1},512)}}});export{Nr as default};
