package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 语音留言
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-01
 */
@TableName("tb_service_call_log_c")
@Data
public class CallLogC {

    @TableId
    private String id;

    private String mainId;

    private transient String phone;

    private transient Date callTime;

    private String status;

    private String processPersonId;

    private transient String processPersonName;

    private transient String fileUrl;

    private transient String fileName;

    private Date processTime;

}
