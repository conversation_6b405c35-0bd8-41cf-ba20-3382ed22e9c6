package org.thingsboard.server.controller.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecord;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.store.StoreOutRecordService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/StoreOutRecord")
public class StoreOutRecordController extends BaseController {
    @Autowired
    private StoreOutRecordService service;


    @GetMapping
    public IPage<StoreOutRecord> findAllConditional(StoreOutRecordPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public StoreOutRecord save(@RequestBody StoreOutRecordSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody StoreOutRecordSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(service.delete(id));
    }
}