package org.thingsboard.server.dao.origin;

import org.thingsboard.server.dao.model.sql.OriginDataEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:01
 */
public interface OriginDataService {


    void saveOriginData(List<OriginDataEntity> originDataEntities);

    OriginDataEntity saveOriginData(OriginDataEntity originDataEntity);

    List<OriginDataEntity> getOriginDataFormIdAndTime(String dataSourceId, long startTime, long endTime);

    OriginDataEntity getLastOriginDataFormId(String dataSourceId);

    List<OriginDataEntity> getOriginDataFormId(String dataSourceId);

}
