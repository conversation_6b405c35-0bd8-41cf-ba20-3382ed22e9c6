package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserPageRequest;

import java.util.Date;
import java.util.List;

@Mapper
public interface EmergencyUserMapper extends BaseMapper<EmergencyUser> {
    IPage<EmergencyUser> findByPage(EmergencyUserPageRequest request);

    boolean update(EmergencyUser entity);

    int clear(String tenantId);

    int saveAll(@Param("list") List<EmergencyUser> entity, @Param("creator") String creator, @Param("tenantId") String tenantId, @Param("createTime") Date createTime);

    List<EmergencyUser> selectFromUserEntity(String tenantId);

    List<EmergencyUser> findByDepartmentId(String departmentId);

    String getNameById(String id);
}
