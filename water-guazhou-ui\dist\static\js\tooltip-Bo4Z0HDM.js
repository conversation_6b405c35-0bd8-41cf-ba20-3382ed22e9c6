import{p as g,H as v,m as a,h as r,t as E,e as w,q as T,v as y}from"./widget-BcWKanF2.js";import{c as l,a as b,d as x,u as H,b as k,e as C,r as D,F as c}from"./openCloseComponent-aiDFLC5b.js";import{g as R}from"./guid-DO7TRjsS.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const d={container:"container",arrow:"arrow"},O=500,f="aria-describedby";function m(i){const{referenceElement:e}=i;return(typeof e=="string"?T(i,{id:e}):e)||null}class L{constructor(){this.registeredElements=new WeakMap,this.hoverTimeout=null,this.registeredElementCount=0,this.queryTooltip=e=>{const{registeredElements:t}=this,o=e.find(n=>t.has(n));return t.get(o)},this.keyDownHandler=e=>{if(e.key==="Escape"&&!e.defaultPrevented){const{activeTooltipEl:t}=this;if(t&&t.open){this.clearHoverTimeout(),this.toggleTooltip(t,!1);const o=m(t);o instanceof Element&&o.contains(e.target)&&e.preventDefault()}}},this.queryHoveredTooltip=e=>{const{activeTooltipEl:t}=this;if(t&&e.includes(t)){this.clearHoverTimeout();return}const o=this.queryTooltip(e);o?this.toggleHoveredTooltip(o,!0):t&&this.toggleHoveredTooltip(t,!1)},this.pointerMoveHandler=e=>{const t=e.composedPath();this.clearHoverTimeout(),this.hoverTimeout=window.setTimeout(()=>this.queryHoveredTooltip(t),O)},this.pointerDownHandler=e=>{if(!y(e))return;const t=this.queryTooltip(e.composedPath());this.clickedTooltip=t,t!=null&&t.closeOnClick&&(this.toggleTooltip(t,!1),this.clearHoverTimeout())},this.focusInHandler=e=>{this.queryFocusedTooltip(e,!0)},this.focusOutHandler=e=>{this.queryFocusedTooltip(e,!1)},this.toggleHoveredTooltip=(e,t)=>{t&&this.closeExistingTooltip(),this.toggleTooltip(e,t)}}registerElement(e,t){this.registeredElementCount++,this.registeredElements.set(e,t),this.registeredElementCount===1&&this.addListeners()}unregisterElement(e){this.registeredElements.delete(e)&&this.registeredElementCount--,this.registeredElementCount===0&&this.removeListeners()}addListeners(){document.addEventListener("keydown",this.keyDownHandler,{capture:!0}),document.addEventListener("pointermove",this.pointerMoveHandler,{capture:!0}),document.addEventListener("pointerdown",this.pointerDownHandler,{capture:!0}),document.addEventListener("focusin",this.focusInHandler,{capture:!0}),document.addEventListener("focusout",this.focusOutHandler,{capture:!0})}removeListeners(){document.removeEventListener("keydown",this.keyDownHandler,{capture:!0}),document.removeEventListener("pointermove",this.pointerMoveHandler,{capture:!0}),document.removeEventListener("pointerdown",this.pointerDownHandler,{capture:!0}),document.removeEventListener("focusin",this.focusInHandler,{capture:!0}),document.removeEventListener("focusout",this.focusOutHandler,{capture:!0})}clearHoverTimeout(){window.clearTimeout(this.hoverTimeout)}closeExistingTooltip(){const{activeTooltipEl:e}=this;e&&this.toggleTooltip(e,!1)}toggleFocusedTooltip(e,t){this.closeExistingTooltip(),t&&this.clearHoverTimeout(),this.toggleTooltip(e,t)}toggleTooltip(e,t){e.open=t,t&&(this.activeTooltipEl=e)}queryFocusedTooltip(e,t){const o=this.queryTooltip(e.composedPath());if(!o||o===this.clickedTooltip){this.clickedTooltip=null;return}this.toggleFocusedTooltip(o,t)}}const _='@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{--calcite-floating-ui-z-index:var(--calcite-tooltip-z-index, 901);display:block;position:absolute;z-index:var(--calcite-floating-ui-z-index)}.calcite-floating-ui-anim{position:relative;transition:var(--calcite-floating-ui-transition);transition-property:transform, visibility, opacity;opacity:0;box-shadow:0 0 16px 0 rgba(0, 0, 0, 0.16);z-index:1;border-radius:0.25rem}:host([data-placement^=bottom]) .calcite-floating-ui-anim{transform:translateY(-5px)}:host([data-placement^=top]) .calcite-floating-ui-anim{transform:translateY(5px)}:host([data-placement^=left]) .calcite-floating-ui-anim{transform:translateX(5px)}:host([data-placement^=right]) .calcite-floating-ui-anim{transform:translateX(-5px)}:host([data-placement]) .calcite-floating-ui-anim--active{opacity:1;transform:translate(0)}:host([calcite-hydrated-hidden]){visibility:hidden !important;pointer-events:none}.arrow,.arrow::before{position:absolute;inline-size:8px;block-size:8px;z-index:-1}.arrow::before{content:"";--tw-shadow:0 4px 8px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);--tw-shadow-colored:0 4px 8px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);transform:rotate(45deg);background:var(--calcite-ui-foreground-1)}:host([data-placement^=top]) .arrow{inset-block-end:-4px}:host([data-placement^=bottom]) .arrow{inset-block-start:-4px}:host([data-placement^=right]) .arrow,:host([data-placement^=left]) .arrow{direction:ltr;text-align:start}:host([data-placement^=left]) .arrow{inset-inline-end:-4px}:host([data-placement^=right]) .arrow{inset-inline-start:-4px}.container{position:relative;overflow:hidden;border-radius:0.25rem;background-color:var(--calcite-ui-foreground-1);padding-block:0.75rem;padding-inline:1rem;font-size:var(--calcite-font-size--2);line-height:1.375;font-weight:var(--calcite-font-weight-medium);color:var(--calcite-ui-text-1);max-inline-size:20rem;max-block-size:20rem;text-align:start}.calcite-floating-ui-anim{border-radius:0.25rem;border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-3);background-color:var(--calcite-ui-foreground-1)}.arrow::before{outline:1px solid var(--calcite-ui-border-3)}',p=new L,P=g(class extends v{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.calciteTooltipBeforeClose=a(this,"calciteTooltipBeforeClose",6),this.calciteTooltipClose=a(this,"calciteTooltipClose",6),this.calciteTooltipBeforeOpen=a(this,"calciteTooltipBeforeOpen",6),this.calciteTooltipOpen=a(this,"calciteTooltipOpen",6),this.guid=`calcite-tooltip-${R()}`,this.hasLoaded=!1,this.openTransitionProp="opacity",this.setTransitionEl=i=>{this.transitionEl=i,l(this)},this.setUpReferenceElement=(i=!0)=>{this.removeReferences(),this.effectiveReferenceElement=m(this.el),b(this,this.effectiveReferenceElement,this.el);const{el:e,referenceElement:t,effectiveReferenceElement:o}=this;i&&t&&!o&&console.warn(`${e.tagName}: reference-element id "${t}" was not found.`,{el:e}),this.addReferences()},this.getId=()=>this.el.id||this.guid,this.addReferences=()=>{const{effectiveReferenceElement:i}=this;if(!i)return;const e=this.getId();"setAttribute"in i&&i.setAttribute(f,e),p.registerElement(i,this.el)},this.removeReferences=()=>{const{effectiveReferenceElement:i}=this;i&&("removeAttribute"in i&&i.removeAttribute(f),p.unregisterElement(i))},this.closeOnClick=!1,this.label=void 0,this.offsetDistance=x,this.offsetSkidding=0,this.open=!1,this.overlayPositioning="absolute",this.placement="auto",this.referenceElement=void 0,this.effectiveReferenceElement=void 0}offsetDistanceOffsetHandler(){this.reposition(!0)}offsetSkiddingHandler(){this.reposition(!0)}openHandler(i){i?this.reposition(!0):H(this.el)}overlayPositioningHandler(){this.reposition(!0)}placementHandler(){this.reposition(!0)}referenceElementHandler(){this.setUpReferenceElement()}connectedCallback(){l(this),this.setUpReferenceElement(this.hasLoaded)}componentDidLoad(){this.referenceElement&&!this.effectiveReferenceElement&&this.setUpReferenceElement(),this.reposition(!0),this.hasLoaded=!0}disconnectedCallback(){this.removeReferences(),k(this,this.effectiveReferenceElement,this.el),C(this)}async reposition(i=!1){const{el:e,effectiveReferenceElement:t,placement:o,overlayPositioning:n,offsetDistance:s,offsetSkidding:h,arrowEl:u}=this;return D(this,{floatingEl:e,referenceEl:t,overlayPositioning:n,placement:o,offsetDistance:s,offsetSkidding:h,includeArrow:!0,arrowEl:u,type:"tooltip"},i)}onBeforeOpen(){this.calciteTooltipBeforeOpen.emit()}onOpen(){this.calciteTooltipOpen.emit()}onBeforeClose(){this.calciteTooltipBeforeClose.emit()}onClose(){this.calciteTooltipClose.emit()}render(){const{effectiveReferenceElement:i,label:e,open:t}=this,o=i&&t,n=!o;return r(w,{"aria-hidden":E(n),"aria-label":e,"aria-live":"polite","calcite-hydrated-hidden":n,id:this.getId(),role:"tooltip"},r("div",{class:{[c.animation]:!0,[c.animationActive]:o},ref:this.setTransitionEl},r("div",{class:d.arrow,ref:s=>this.arrowEl=s}),r("div",{class:d.container},r("slot",null))))}get el(){return this}static get watchers(){return{offsetDistance:["offsetDistanceOffsetHandler"],offsetSkidding:["offsetSkiddingHandler"],open:["openHandler"],overlayPositioning:["overlayPositioningHandler"],placement:["placementHandler"],referenceElement:["referenceElementHandler"]}}static get style(){return _}},[1,"calcite-tooltip",{closeOnClick:[516,"close-on-click"],label:[1],offsetDistance:[514,"offset-distance"],offsetSkidding:[514,"offset-skidding"],open:[516],overlayPositioning:[513,"overlay-positioning"],placement:[513],referenceElement:[1,"reference-element"],effectiveReferenceElement:[32],reposition:[64]}]);function z(){if(typeof customElements>"u")return;["calcite-tooltip"].forEach(e=>{switch(e){case"calcite-tooltip":customElements.get(e)||customElements.define(e,P);break}})}z();export{P as T,z as d};
