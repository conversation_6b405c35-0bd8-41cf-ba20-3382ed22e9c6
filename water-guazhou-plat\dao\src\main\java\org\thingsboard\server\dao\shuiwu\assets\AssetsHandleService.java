package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsHandleEntity;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
public interface AssetsHandleService {
    PageData getPage(JSONObject params);

    AssetsHandleEntity save(AssetsHandleEntity assetsHandleEntity);

    void delete(List<String> idList);

    Map reviewer(String id, String userId, String status);
}
