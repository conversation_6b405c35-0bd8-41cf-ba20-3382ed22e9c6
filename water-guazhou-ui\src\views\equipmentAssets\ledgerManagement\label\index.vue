<!-- 设备标签 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <DialogForm
      ref="InstallDialogRef"
      :config="InstallDialog"
    ></DialogForm>

    <DialogForm
      ref="UseDialogRef"
      :config="UseDialog"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { removeSlash } from '@/utils/removeIdSlash'
import { getUserList } from '@/api/user/index'
import {
  getSupplierSerch,
  getDeviceTypeTree,
  getAreaTreeSearch
} from '@/api/equipment_assets/equipmentManage'
import { getWaterSupplyTree } from '@/api/company_org'
import {
  getDeviceStorageJournalSerch,
  postDeviceSettleJournal,
  postDeviceUsageJournal,
  getDeviceUsageJournal,
  getDeviceSettleJournal,
  exportDeviceLabelExcel
} from '@/api/equipment_assets/ledgerManagement'
import {
  getConstructionProjectSerch
} from '@/api/equipment_assets/equipmentOutStock'
import { formatTree, traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const InstallDialogRef = ref<IDialogFormIns>()

const UseDialogRef = ref<IDialogFormIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '标签编码', field: 'deviceLabelCode', type: 'input' },
    { label: '设备名称', field: 'name', type: 'input' },
    { label: '设备型号', field: 'model', type: 'input' },
    { label: '安装区域', field: 'areaId', type: 'select-tree', checkStrictly: true, options: computed(() => data.installationArea) as any },
    { label: '安装位置', field: 'address', type: 'input' },
    { label: '供应商', field: 'supplierId', type: 'select-tree', checkStrictly: true, options: computed(() => data.SupplierList) as any },
    { label: '所属项目', field: 'projectId', type: 'select-tree', checkStrictly: true, options: computed(() => data.ConstructionProject) as any },
    { label: '仓库编号', field: 'storehouseCode', type: 'input' },
    { label: '仓库名称', field: 'storehouseName', type: 'input' },
    { label: '货架编号', field: 'shelfCode', type: 'input' },
    { label: '货架名称', field: 'shelfName', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          icon: ICONS.EXPORT,
          click: () => refreshData(true)
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '标签编码', prop: 'deviceLabelCode' },
    { label: '设备名称', prop: 'name' },
    { label: '设备型号', prop: 'model' },
    { label: '所属大类', prop: 'topType' },
    { label: '所属类别', prop: 'type' },
    { label: '供应商', prop: 'supplierName' },
    { label: '报废日期', prop: 'scrappedTime', formatter: row => (row.scrappedTime ? dayjs(row.scrappedTime).format('YYYY-MM-DD') : '') },
    { label: '施工项目', prop: 'projectName' },
    { label: '所在仓库', prop: 'storehouseName' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '安装信息',
      icon: ICONS.DETAIL,
      perm: $btnPerms('RoleManageEdit'),
      click: row => openInstallDialog(row)
    },
    {
      type: 'primary',
      text: '使用信息',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.DETAIL,
      click: row => openUseDialog(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const InstallDialog = reactive<IDialogFormConfig>({
  title: '安装信息',
  labelWidth: '120px',
  dialogWidth: '700px',
  submit: (params: any) => {
    let val = '添加成功'
    if (params.id) val = '修改成功'
    postDeviceSettleJournal(params).then(() => {
      refreshData()
      ElMessage.success(val)
      InstallDialogRef.value?.closeDialog()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 12,
          type: 'select-tree',
          label: '接收部门',
          field: 'installUserDepartmentId',
          checkStrictly: true,
          defaultExpandAll: true,
          rules: [{ required: true, message: '请选择接收部门' }],
          autoFillOptions: config => {
            getWaterSupplyTree(2).then(res => {
              config.options = traverse(res.data.data || [])
            })
          }
        }, {
          xl: 12,
          type: 'select',
          label: '安装人员',
          field: 'installUserId',
          options: [],
          setOptionBy: 'installUserDepartmentId',
          setOptionMethod: (config: any, row: any) => {
            if (row.installUserDepartmentId) {
              getUserList({ pid: row.installUserDepartmentId }).then(res => {
                const value = res.data.data.data || []
                config.options = value.map(item => {
                  return { label: item.firstName, value: removeSlash(item.id.id) }
                })
              })
            }
          }
        }, {
          xl: 12,
          type: 'select-tree',
          label: '所属项目',
          field: 'projectId',
          checkStrictly: true,
          options: computed(() => formatTree(data.ConstructionProject, { label: 'name', value: 'id', children: 'children', id: 'id' })) as any
        }, {
          xl: 12,
          type: 'date',
          label: '安装时间',
          field: 'installTime',
          format: 'x'
        }, {
          xl: 12,
          type: 'select-tree',
          label: '安装区域',
          field: 'installAddressId',
          rules: [{ required: true, message: '请选择安装区域' }],
          checkStrictly: true,
          options: computed(() => data.installationArea) as any
        }, {
          xl: 24,
          type: 'textarea',
          label: '安装位置',
          field: 'address',
          rules: [{ required: true, message: '请输入安装位置' }]
        },
        {
          xl: 24,
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        }
      ]
    }
  ]
})

const UseDialog = reactive<IDialogFormConfig>({
  title: '使用信息',
  labelWidth: '120px',
  dialogWidth: '500px',
  submit: (params: any) => {
    let val = '添加成功'
    if (params.id) val = '修改成功'
    postDeviceUsageJournal(params).then(() => {
      refreshData()
      ElMessage.success(val)
      UseDialogRef.value?.closeDialog()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '使用部门',
          field: 'departmentId',
          rules: [{ required: true, message: '请选择使用部门' }],
          checkStrictly: true,
          defaultExpandAll: true,
          autoFillOptions: config => {
            getWaterSupplyTree(2).then(res => {
              config.options = traverse(res.data.data || [])
            })
          }
        }, {
          type: 'select',
          label: '使用人员',
          field: 'userId',
          rules: [{ required: true, message: '请选择使用人员' }],
          options: [],
          setOptionBy: 'departmentId',
          setOptionMethod: (config: any, row: any) => {
            if (row.departmentId) {
              getUserList({ pid: row.departmentId }).then(res => {
                const value = res.data.data.data || []
                config.options = value.map(item => {
                  return { label: item.firstName, value: removeSlash(item.id.id) }
                })
              })
            }
          }
        }, {
          type: 'date',
          label: '领用时间',
          field: 'receiveTime',
          format: 'x'
        }, {
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        }
      ]
    }
  ]
})

const TreeData = reactive<SLTreeConfig>({
  title: '',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    TreeData.currentProject = data
    refreshData()
  }
})

const openInstallDialog = (row: { [x: string]: any }) => {
  InstallDialog.title = '安装信息'
  InstallDialog.defaultValue = { deviceLabelCode: row.deviceLabelCode }
  getDeviceSettleJournal({ deviceLabelCode: row.deviceLabelCode }).then(res => {
    const value = res.data.data.data || [{}]
    InstallDialog.defaultValue = { deviceLabelCode: row.deviceLabelCode, ...value[0] }
    InstallDialogRef.value?.openDialog()
  })
}

const openUseDialog = (row?: any) => {
  UseDialog.title = '使用信息'
  UseDialog.defaultValue = { deviceLabelCode: row.deviceLabelCode }
  getDeviceUsageJournal({ deviceLabelCode: row.deviceLabelCode }).then(res => {
    const value = res.data.data.data || [{}]
    UseDialog.defaultValue = { deviceLabelCode: row.deviceLabelCode, ...value[0] }
    UseDialogRef.value?.openDialog()
  })
}

const data = reactive({
  // 用户列表
  UserList: [],
  // 供应商
  SupplierList: [],
  // 施工项目
  ConstructionProject: [],
  // 安装区域
  installationArea: [],

  // 获取用户
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  },
  // 获取供应商
  getSupplierValue: () => {
    const params = { page: 1, size: 99999 }
    getSupplierSerch(params).then(res => {
      data.SupplierList = traverse(res.data.data.data || [])
    })
  },
  // 获取施工项目
  getConstructionProjectValue: () => {
    const params = { page: 1, size: 99999 }
    getConstructionProjectSerch(params).then(res => {
      data.ConstructionProject = traverse(res.data.data.data || [])
    })
  },
  // 获取安装区域
  getAreaTreeValue: () => {
    const params = {
      page: 1,
      size: 99999,
      shortName: ''
    }
    getAreaTreeSearch(params).then(res => {
      data.installationArea = traverse(res.data.data.data || [])
    })
  },

  init: () => {
    data.getSupplierValue()
    data.getConstructionProjectValue()
    data.getAreaTreeValue()
  }
})

const refreshData = async (status?: boolean) => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    deviceTypeId: TreeData.currentProject.id,
    scrapped: true,
    ...(refSearch.value?.queryParams || {})
  }
  if (status) {
    // 导出
    exportDeviceLabelExcel(params).then(res => {
      const url = window.URL.createObjectURL(res.data)
      console.log(url)
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', `设备标签.xlsx`)
      document.body.appendChild(link)
      link.click()
    })
    return
  }
  getDeviceStorageJournalSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    TreeData.currentProject = res.data.data[0]
    refreshData()
  })
}

onMounted(async () => {
  init()
  data.init()
})
</script>
