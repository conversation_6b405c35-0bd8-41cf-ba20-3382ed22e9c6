package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordSaveRequest;

public interface SoProjectOperateRecordService {
    /**
     * 分页条件查询项目大事记，项目大事记通常会记录项目或工程的重要操作及其详细信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoProjectOperateRecord> findAllConditional(SoProjectOperateRecordPageRequest request);

    /**
     * 保存项目大事记
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoProjectOperateRecord save(SoProjectOperateRecordSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoProjectOperateRecord entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 通过保存请求记录创建日志
     *
     * @param deliverEntity    保存请求模板
     * @param constructionCode 工程编号
     * @param Journal          日志模板
     * @return 保存好的日志
     */
    SoProjectOperateRecord recordCreate(SaveRequest<?> deliverEntity, String constructionCode, SoGeneralSystemJournal Journal);

    /**
     * 记录创建日志
     *
     * @param tenantId         客户id
     * @param userId           操作用户id
     * @param constructionCode 工程编号
     * @param journal          日志模板
     * @return 保存好的日志
     */
    SoProjectOperateRecord recordCreate(String tenantId, String userId, String constructionCode, SoGeneralSystemJournal journal);

    /**
     * 记录完成日志
     *
     * @param tenantId         客户id
     * @param userId           操作用户id
     * @param constructionCode 工程编号
     * @param journal          日志模板
     * @return 保存好的日志
     */
    SoProjectOperateRecord recordComplete(String tenantId, String userId, String constructionCode, SoGeneralSystemJournal journal);

}
