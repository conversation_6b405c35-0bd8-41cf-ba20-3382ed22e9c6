<!-- 地下水涵养水位 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
      @close="handleDialogClose"
    ></DialogForm>
    <DialogForm
      ref="refAnalysisForm"
      :config="AnalysisConfig"
    >
      <div class="analysis-container">
        <div class="chart-container">
          <div ref="chartRef" class="chart"></div>
        </div>
        <div class="suggestion-container">
          <h3>涵养建议</h3>
          <div class="suggestion-content">{{ state.suggestion }}</div>
        </div>
      </div>
    </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { Delete, Plus, Search, View } from '@element-plus/icons-vue'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import {
  getGroundwaterLevelList,
  saveGroundwaterLevel,
  updateGroundwaterLevel,
  deleteGroundwaterLevel,
  getGroundwaterLevelAnalysis,
  getGroundwaterRecharge
} from '@/api/waterSource/groundwater'
import { getStationList } from '@/api/station/station'
import * as echarts from 'echarts'
import { formatDate } from '@/utils/DateFormatter'

const refSearch = ref<ICardSearchIns>()
const refDialogForm = ref<IDialogFormIns>()
const refAnalysisForm = ref<IDialogFormIns>()
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const state = reactive({
  selectedIds: [] as string[],
  suggestion: '',
  dialogVisible: false, // 跟踪对话框是否打开
  analysisData: {
    dates: [] as string[],
    values: [] as number[]
  }
})

// 查询条件配置
const SearchConfig = reactive<ISearch>({
  labelWidth: '100px',
  filters: [
    { type: 'input', label: '测点名称', field: 'stationName', onChange: () => refreshData() },
    // { type: 'input', label: '测点位置', field: 'stationLocation', onChange: () => refreshData() },
    {
      type: 'date',
      label: '记录时间',
      field: 'recordTime',
      onChange: () => refreshData()
    },
    {
      type: 'select',
      label: '水位状态',
      field: 'status',
      options: [
        { label: '正常', value: 1 },
        { label: '偏低', value: 2 },
        { label: '偏高', value: 3 }
      ],
      onChange: () => refreshData()
    },
    // {
    //   type: 'select',
    //   label: '数据来源',
    //   field: 'dataSource',
    //   options: [
    //     { label: '手动录入', value: 1 },
    //     { label: '设备采集', value: 2 }
    //   ],
    //   onChange: () => refreshData()
    // },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', svgIcon: shallowRef(Search), click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', svgIcon: shallowRef(Plus), click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', svgIcon: shallowRef(Delete), click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

// 表格配置
const TableConfig = reactive<ICardTable>({
  title: '地下水水位数据列表',
  selectList: [],
  indexVisible: true,
  columns: [
    {
      label: '测点名称',
      prop: 'stationName',
      formatter: (row: any) => {
        return row.stationName || row.wellName || '-'
      }
    },
    {
      label: '测点位置',
      prop: 'stationLocation',
      formatter: (row: any) => {
        return row.stationLocation || row.wellLocation || '-'
      }
    },
    { label: '水位高度(m)', prop: 'waterLevel' },
    { label: '水位变化量(m)', prop: 'levelChange' },
    { label: '地下水补给量(m³)', prop: 'rechargeAmount' },
    { label: '降雨量(mm)', prop: 'rainfallAmount' },
    {
      label: '记录时间',
      prop: 'recordTime',
      formatter: (row: any) => {
        const time = row.recordTime
        return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : ''
      }
    },
    {
      label: '水位状态',
      prop: 'status',
      formatter: (row: any) => {
        const statusMap = {
          1: '正常',
          2: '偏低',
          3: '偏高'
        }
        return statusMap[row.status] || '-'
      }
    },
    {
      label: '数据来源',
      prop: 'dataSource',
      formatter: (row: any) => {
        const sourceMap = {
          1: '手动录入',
          2: '设备采集'
        }
        return sourceMap[row.dataSource] || '-'
      }
    },
    { label: '备注', prop: 'remark' }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [

    {
      perm: true,
      text: '分析',
      svgIcon: shallowRef(View),
      click: (row: any) => handleAnalysis(row)
    },
    {
      perm: true,
      text: '删除',
      svgIcon: shallowRef(Delete),
      click: (row: any) => handleDeleteSingle(row)
    }
  ],
  handleSelectChange: (selection: any[]) => {
    state.selectedIds = selection.map(item => item.id)
    TableConfig.selectList = selection
  }
})

// 表单配置
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '地下水水位数据',
  dialogWidth: '500px',
  group: [
    {
      fields: [
        { type: 'input', field: 'id', hidden: true },
        {
          type: 'select',
          field: 'stationId',
          label: '测点站点',
          required: true,
          options: [],
          rules: [{ required: true, message: '请选择测点站点', trigger: 'change' }],
          onChange: (value: string) => handleStationChange(value)
        },
        {
          type: 'input',
          field: 'stationName',
          label: '测点名称',
          hidden: true
        },
        {
          type: 'input',
          field: 'stationLocation',
          label: '测点位置',
          hidden: true
        },
        {
          type: 'input-number',
          field: 'waterLevel',
          label: '水位高度(m)',
          required: true,
          rules: [{ required: true, message: '请输入水位高度', trigger: 'blur' }]
        },
        {
          type: 'input-number',
          field: 'levelChange',
          label: '水位变化量(m)',
          required: true
        },
        {
          type: 'input-number',
          field: 'rechargeAmount',
          label: '地下水补给量(m³)',
          required: true
        },
        {
          type: 'input-number',
          field: 'rainfallAmount',
          label: '降雨量(mm)',
          required: true
        },
        {
          type: 'datetime',
          field: 'recordTime',
          label: '记录时间',
          required: true,
          valueFormat: 'x', // 使用时间戳格式（毫秒）
          rules: [{ required: true, message: '请选择记录时间', trigger: 'blur' }]
        },
        {
          type: 'select',
          field: 'status',
          label: '水位状态',
          options: [
            { label: '正常', value: 1 },
            { label: '偏低', value: 2 },
            { label: '偏高', value: 3 }
          ],
          required: true
        },
        {
          type: 'select',
          field: 'dataSource',
          label: '数据来源',
          options: [
            { label: '手动录入', value: 1 },
            { label: '设备采集', value: 2 }
          ],
          required: true
        },
        { type: 'textarea', field: 'remark', label: '备注' }
      ]
    }
  ],
  defaultValue: {},
  submit: async (formData: any) => {
    try {
      DialogFormConfig.submitting = true

      // 处理表单数据，确保字段名与后端 API 期望的字段名匹配
      const apiData = {
        ...formData,
        // 如果后端期望的是 wellName 而不是 stationName，进行转换
        wellName: formData.wellName || formData.stationName,
        wellLocation: formData.wellLocation || formData.stationLocation
      }

      // 处理日期字段，强制转换为时间戳
      if (formData.recordTime) {
        // 强制将日期字符串转换为时间戳
        try {
          if (typeof formData.recordTime === 'string') {
            const date = new Date(formData.recordTime)
            if (!isNaN(date.getTime())) {
              apiData.recordTime = date.getTime()
              // 日期字符串转换为时间戳成功
            } else {
              // 如果转换失败，使用当前时间
              apiData.recordTime = Date.now()
              console.warn('日期转换失败，使用当前时间')
            }
          } else if (typeof formData.recordTime === 'number') {
            // 如果已经是数字（时间戳），直接使用
            apiData.recordTime = formData.recordTime
            // 已经是时间戳格式
          } else {
            // 其他情况，使用当前时间
            apiData.recordTime = Date.now()
            console.warn('未知的日期格式，使用当前时间')
          }
        } catch (e) {
          // 如果出错，使用当前时间
          apiData.recordTime = Date.now()
          console.error('日期处理出错，使用当前时间:', e)
        }
      } else {
        // 如果没有记录时间，使用当前时间
        apiData.recordTime = Date.now()
        console.warn('没有记录时间，使用当前时间')
      }

      // 删除 measureDate 字段，只使用 recordTime
      delete apiData.measureDate

      // 如果有站点ID，确保将其传递给后端
      if (formData.stationId) {
        apiData.stationId = formData.stationId
      }

      console.log('提交数据:', apiData)

      if (formData.id) {
        // 更新
        const res = await updateGroundwaterLevel(apiData)
        console.log('更新响应:', res)
        if (res.data && res.data.code === 200) {
          SLMessage.success(res.data.message || '更新成功')
          refDialogForm.value?.closeDialog()
          // 标记对话框已关闭
          state.dialogVisible = false
          refreshData()
        } else {
          SLMessage.error(res.data?.message || res.data?.msg || '更新失败')
        }
      } else {
        // 新增
        const res = await saveGroundwaterLevel(apiData)
        // 处理新增响应
        if (res.data && res.data.code === 200) {
          SLMessage.success(res.data.message || '添加成功')
          refDialogForm.value?.closeDialog()
          // 标记对话框已关闭
          state.dialogVisible = false
          refreshData()
        } else {
          SLMessage.error(res.data?.message || res.data?.msg || '添加失败')
        }
      }
    } catch (error) {
      console.error('保存失败', error)
      SLMessage.error('保存失败')
    } finally {
      DialogFormConfig.submitting = false
    }
  }
})

// 分析配置
const AnalysisConfig = reactive<IDialogFormConfig>({
  title: '地下水涵养水位分析',
  dialogWidth: '800px',
  fullscreen: false,
  group: [],
  defaultValue: {},
  cancel: true
})

// 刷新数据
const refreshData = async () => {
  try {
    TableConfig.loading = true
    // 获取查询参数
    const queryParams = refSearch.value?.queryParams || {}

    // 处理日期参数，将日期字符串转换为时间戳
    const params = {
      ...queryParams,
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit
    }

    // 如果有记录时间参数，将其转换为时间戳或移除
    if (params.recordTime) {
      try {
        // 尝试将日期字符串转换为时间戳（毫秒数）
        const date = new Date(params.recordTime)
        if (!isNaN(date.getTime())) {
          // 使用时间戳（毫秒数）
          params.recordTime = date.getTime()
          console.log('记录时间转换为时间戳:', params.recordTime)
        } else {
          // 如果转换失败，删除该参数
          delete params.recordTime
          console.warn('日期转换失败，已移除记录时间参数')
        }
      } catch (e) {
        // 如果出错，删除该参数
        delete params.recordTime
        console.error('日期处理出错:', e)
      }
    }

    // 发送查询请求
    const res = await getGroundwaterLevelList(params)

    if (res.data && res.data.code === 200) {
      // 适配返回的数据格式
      let dataList = []
      let total = 0

      // 如果返回的是 {data: {data: [...], total: number}}
      if (res.data.data && Array.isArray(res.data.data.data)) {
        dataList = res.data.data.data
        total = res.data.data.total || 0
      }
      // 如果返回的是 {data: {records: [...], total: number}}
      else if (res.data.data && Array.isArray(res.data.data.records)) {
        dataList = res.data.data.records
        total = res.data.data.total || 0
      }
      // 如果返回的直接是数组 {data: [...]}
      else if (Array.isArray(res.data.data)) {
        dataList = res.data.data
        total = res.data.data.length
      }
      // 兑底处理
      else {
        console.warn('未识别的数据格式:', res.data)
      }

      // 处理数据，确保字段名与表格列匹配
      const processedData = dataList.map((item: any) => ({
        ...item,
        // 如果数据中没有 stationName 但有 wellName，进行转换
        stationName: item.stationName || item.wellName,
        stationLocation: item.stationLocation || item.wellLocation,
        recordTime: item.recordTime
      }))

      console.log('处理后的表格数据:', processedData)

      // 更新表格数据
      TableConfig.dataList = processedData
      TableConfig.pagination.total = total
    } else {
      TableConfig.dataList = []
      TableConfig.pagination.total = 0
      SLMessage.error(res.data?.message || res.data?.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
    SLMessage.error('获取数据失败')
    TableConfig.dataList = []
    TableConfig.pagination.total = 0
  } finally {
    TableConfig.loading = false
  }
}

// 站点列表
const stationList = ref<any[]>([])

// 加载站点列表
const loadStationList = async () => {
  try {
    // 使用站点 API 获取站点列表
    const res = await getStationList({ page: 1, size: 1000 })

    // 处理站点数据 - 适配不同的响应格式
    let stations = []

    // 检查响应结构，尝试不同的路径提取数据
    if (res && res.data) {
      // 处理标准响应格式
      if (res.data.data && Array.isArray(res.data.data)) {
        stations = res.data.data
      }
      // 其他可能的数据结构
      else if (res.data.code === 200) {
        if (res.data.data && Array.isArray(res.data.data.data)) {
          stations = res.data.data.data
        } else if (res.data.data && Array.isArray(res.data.data.records)) {
          stations = res.data.data.records
        } else if (Array.isArray(res.data.data)) {
          stations = res.data.data
        }
      } else if (Array.isArray(res.data)) {
        stations = res.data
      }
    } else if (Array.isArray(res)) {
      stations = res
    }

    if (stations && stations.length > 0) {
      // 转换为下拉选择框所需的格式
      stationList.value = stations.map((item: any) => {
        // 使用站点的 address 字段作为测点位置
        let locationStr = item.address || ''

        // 如果没有 address，则使用经纬度作为备选
        if (!locationStr && item.location) {
          if (typeof item.location === 'string' && item.location.includes(',')) {
            const [longitude, latitude] = item.location.split(',')
            locationStr = `经纬度: ${longitude},${latitude}`
          } else {
            locationStr = String(item.location)
          }
        }

        return {
          label: item.name || '未命名站点',
          value: item.id,
          // 将站点的完整数据保存下来，以便后续使用
          location: locationStr,
          address: item.address || '',
          originalLocation: item.location || '',
          originalData: item
        }
      })

      // 更新表单中的站点选项
      try {
        const stationField = DialogFormConfig.group[0].fields.find(
          (field: any) => field.field === 'stationId'
        )
        if (stationField && typeof stationField === 'object') {
          Object.assign(stationField, {
            options: stationList.value
          })
        }
      } catch (error) {
        console.error('设置站点选项失败', error)
      }

      return true
    } else {
      console.warn('未找到站点数据')
      return false
    }
  } catch (error) {
    console.error('获取站点列表失败', error)
    return false
  }
}

// 已删除从水位数据中提取站点信息的函数

// 处理站点选择变化
const handleStationChange = (stationId: string) => {
  try {
    // 根据站点ID查找对应的站点信息
    const selectedStation = stationList.value.find(item => item.value === stationId)

    if (selectedStation) {
      // 自动填充测点名称和位置
      // 直接更新表单实例的值，而不是默认值
      if (refDialogForm.value && refDialogForm.value.formRef) {
        // 如果表单实例存在，直接设置字段值
        refDialogForm.value.formRef.setFieldValue('stationName', selectedStation.label)
        refDialogForm.value.formRef.setFieldValue('stationLocation', selectedStation.address || selectedStation.location || '')
      } else {
        // 如果表单实例不存在，更新默认值
        DialogFormConfig.defaultValue = {
          ...DialogFormConfig.defaultValue,
          stationId: selectedStation.value,
          stationName: selectedStation.label,
          stationLocation: selectedStation.address || selectedStation.location || ''
        }
      }
    }
  } catch (error) {
    console.error('处理站点选择变化失败', error)
  }
}

// 新增
const handleAdd = async () => {
  DialogFormConfig.title = '新增地下水涵养水位'

  // 加载站点列表
  try {
    const success = await loadStationList()
    if (!success) {
      SLMessage.warning('站点列表加载失败，请重试')
      return
    }
  } catch (error) {
    console.error('加载站点列表失败', error)
    SLMessage.warning('站点列表加载失败，请重试')
    return
  }

  // 设置默认值，使用时间戳格式的记录时间
  DialogFormConfig.defaultValue = {
    recordTime: Date.now() // 使用当前时间的时间戳（毫秒数）
  }

  // 标记对话框将打开
  state.dialogVisible = true

  refDialogForm.value?.openDialog()
}

// 处理对话框关闭
const handleDialogClose = () => {
  // 标记对话框已关闭
  state.dialogVisible = false
  // 对话框已关闭
}

// 删除单条
const handleDeleteSingle = (row: any) => {
  SLConfirm('确定要删除该记录吗？', '删除提示')
    .then(async () => {
      try {
        await deleteGroundwaterLevel([row.id])
        SLMessage.success('删除成功')
        refreshData()
      } catch (error) {
        // 删除失败
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 批量删除
const handleDelete = () => {
  if (state.selectedIds.length === 0) {
    SLMessage.warning('请选择要删除的记录')
    return
  }

  SLConfirm(`确定要删除选中的 ${state.selectedIds.length} 条记录吗？`, '删除提示')
    .then(async () => {
      try {
        await deleteGroundwaterLevel(state.selectedIds)
        SLMessage.success('删除成功')
        refreshData()
      } catch (error) {
        // 删除失败
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 分析
const handleAnalysis = async (row: any) => {
  try {
    AnalysisConfig.title = `${row.stationName} 水位分析`
    refAnalysisForm.value?.openDialog()

    // 获取分析数据
    const analysisRes = await getGroundwaterLevelAnalysis({
      stationId: row.stationId,
      startTime: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000).getTime(),
      endTime: new Date().getTime()
    })

    console.log('分析数据返回:', analysisRes)

    if (analysisRes.data && analysisRes.data.code === 200) {
      // 适配不同的数据格式
      let data = []

      if (Array.isArray(analysisRes.data.data)) {
        data = analysisRes.data.data
      } else if (analysisRes.data.data && Array.isArray(analysisRes.data.data.data)) {
        data = analysisRes.data.data.data
      } else if (analysisRes.data.data && Array.isArray(analysisRes.data.data.records)) {
        data = analysisRes.data.data.records
      } else {
        data = []
        console.warn('未识别的分析数据格式:', analysisRes.data)
      }

      // 处理数据，确保字段名一致
      const processedData = data.map((item: any) => ({
        ...item,
        recordTime: item.recordTime,
        waterLevel: item.waterLevel || 0
      }))

      state.analysisData = {
        dates: processedData.map((item: any) => String(formatDate(item.recordTime, 'MM-DD') || '')),
        values: processedData.map((item: any) => item.waterLevel)
      }

      console.log('处理后的分析数据:', state.analysisData)

      // 渲染图表
      setTimeout(() => {
        initChart(state.analysisData.dates, state.analysisData.values)
      }, 100)

      // 获取涵养建议
      const suggestionRes = await getGroundwaterRecharge({
        areaId: row.areaId || row.id, // 使用 areaId 或者 id
        startTime: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000).getTime(),
        endTime: new Date().getTime()
      })

      console.log('涵养建议返回:', suggestionRes)

      if (suggestionRes.data && suggestionRes.data.code === 200) {
        // 适配不同的数据格式
        if (suggestionRes.data.data && suggestionRes.data.data.rechargeSuggestion) {
          state.suggestion = suggestionRes.data.data.rechargeSuggestion
        } else if (suggestionRes.data.data && typeof suggestionRes.data.data === 'string') {
          state.suggestion = suggestionRes.data.data
        } else {
          state.suggestion = '暂无建议'
          console.warn('未识别的建议数据格式:', suggestionRes.data)
        }
      } else {
        state.suggestion = '暂无建议'
      }
    } else {
      state.analysisData = { dates: [], values: [] }
      state.suggestion = '暂无建议'
      initChart([], [])
      SLMessage.error(analysisRes.data?.message || analysisRes.data?.msg || '获取分析数据失败')
    }
  } catch (error) {
    console.error('获取分析数据失败', error)
    SLMessage.error('获取分析数据失败')
    state.analysisData = { dates: [], values: [] }
    state.suggestion = '暂无建议'
    if (chartRef.value) {
      initChart([], [])
    }
  }
}

// 初始化图表
const initChart = (dates: string[], values: number[]) => {
  if (!chartRef.value) return

  if (chart) {
    chart.dispose()
  }

  chart = echarts.init(chartRef.value)

  const option = {
    title: {
      text: '地下水位变化趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br />{a}: {c} m'
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '水位(m)'
    },
    series: [
      {
        name: '水位',
        type: 'line',
        data: values,
        markPoint: {
          data: [
            { type: 'max', name: '最高值' },
            { type: 'min', name: '最低值' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ]
  }

  chart.setOption(option)

  window.addEventListener('resize', () => {
    chart?.resize()
  })
}

onMounted(async () => {
  // 首先加载站点列表，但不阻止数据列表的加载
  try {
    loadStationList().catch(error => {
      console.error('加载站点列表失败', error)
    })
  } catch (error) {
    console.error('加载站点列表失败', error)
  }

  // 加载数据列表
  try {
    await refreshData()
  } catch (error) {
    // 静默处理错误
    SLMessage.error('加载数据列表失败')
  }
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.card-table {
  height: calc(100% - 80px);
}

.analysis-container {
  display: flex;
  flex-direction: column;
  height: 500px;

  .chart-container {
    flex: 1;
    min-height: 300px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }

  .suggestion-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    h3 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: bold;
    }

    .suggestion-content {
      line-height: 1.6;
    }
  }
}
</style>
