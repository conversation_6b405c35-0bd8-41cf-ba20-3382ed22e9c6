"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[223],{57435:(e,t,r)=>{r.d(t,{Z:()=>y});var i=r(43697),s=r(46791),o=r(70586),n=(r(80442),r(20102),r(92604),r(26258),r(87538)),l=r(5600),a=(r(75215),r(67676),r(52011));let p=class extends s.Z{constructor(e){super(e),this.getCollections=null}initialize(){this.own((0,n.EH)((()=>this._refresh())))}destroy(){this.getCollections=null}_refresh(){const e=(0,o.pC)(this.getCollections)?this.getCollections():null;if((0,o.Wi)(e))return void this.removeAll();let t=0;for(const r of e)(0,o.pC)(r)&&(t=this._processCollection(t,r));this.splice(t,this.length)}_createNewInstance(e){return new s.Z(e)}_processCollection(e,t){if(!t)return e;const r=this.itemFilterFunction?this.itemFilterFunction:e=>!!e;for(const i of t)if(i){if(r(i)){const t=this.indexOf(i,e);t>=0?t!==e&&this.reorder(i,e):this.add(i,e),++e}if(this.getChildrenFunction){const t=this.getChildrenFunction(i);if(Array.isArray(t))for(const r of t)e=this._processCollection(e,r);else e=this._processCollection(e,t)}}return e}};(0,i._)([(0,l.Cb)()],p.prototype,"getCollections",void 0),(0,i._)([(0,l.Cb)()],p.prototype,"getChildrenFunction",void 0),(0,i._)([(0,l.Cb)()],p.prototype,"itemFilterFunction",void 0),p=(0,i._)([(0,a.j)("esri.core.CollectionFlattener")],p);const y=p},68668:(e,t,r)=>{r.d(t,{G:()=>l,w:()=>a});var i=r(66643),s=r(46791),o=r(83379),n=r(70586);async function l(e,t){return await e.load(),a(e,t)}async function a(e,t){const r=[],l=(...e)=>{for(const t of e)(0,n.Wi)(t)||(Array.isArray(t)?l(...t):s.Z.isCollection(t)?t.forEach((e=>l(e))):o.Z.isLoadable(t)&&r.push(t))};t(l);let a=null;if(await(0,i.UI)(r,(async e=>{const t=await(0,i.q6)(function(e){return"loadAll"in e&&"function"==typeof e.loadAll}(e)?e.loadAll():e.load());!1!==t.ok||a||(a=t)})),a)throw a.error;return e}},30223:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Xe});var i=r(43697),s=r(46791),o=r(57435),n=r(20102),l=r(22974),a=r(68668),p=r(92604),y=r(70586),d=r(16453),u=r(95330),c=r(17452),h=r(5600),b=r(75215),f=r(71715),g=r(52011),v=r(82971),_=r(87085),m=r(38171),C=r(51773),w=(r(16050),r(12501),r(28756),r(92271),r(72529),r(5499),r(84382),r(81571),r(91423),r(32400)),S=r(3172),j=r(35454),F=r(83379),x=r(609),I=(r(67676),r(36030)),O=r(6570),L=r(19238),T=r(10699),A=r(21506);let q=class extends((0,T.IG)(d.w)){constructor(e){super(e),this.title="",this.id=-1,this.modelName=null,this.isEmpty=null,this.visible=!0,this.opacity=1}readTitle(e,t){return"string"==typeof t.alias?t.alias:"string"==typeof t.name?t.name:""}readIdOnlyOnce(e){return-1!==this.id?this.id:"number"==typeof e?e:-1}};(0,i._)([(0,h.Cb)({type:String,json:{origins:{"web-scene":{write:!0},"portal-item":{write:!0}}}})],q.prototype,"title",void 0),(0,i._)([(0,f.r)("service","title",["alias","name"])],q.prototype,"readTitle",null),(0,i._)([(0,h.Cb)()],q.prototype,"layer",void 0),(0,i._)([(0,h.Cb)({type:b.z8,readOnly:!0,json:{read:!1,write:{ignoreOrigin:!0}}})],q.prototype,"id",void 0),(0,i._)([(0,f.r)("service","id")],q.prototype,"readIdOnlyOnce",null),(0,i._)([(0,h.Cb)((0,A.Lx)(String))],q.prototype,"modelName",void 0),(0,i._)([(0,h.Cb)((0,A.Lx)(Boolean))],q.prototype,"isEmpty",void 0),(0,i._)([(0,h.Cb)({type:Boolean,json:{name:"visibility",write:!0}})],q.prototype,"visible",void 0),(0,i._)([(0,h.Cb)({type:Number,json:{write:!0}})],q.prototype,"opacity",void 0),q=(0,i._)([(0,g.j)("esri.layers.buildingSublayers.BuildingSublayer")],q);const Z=q;var B=r(15506),E=r(53518),R=r(99514),k=r(35671),N=r(61064),P=r(51161),M=r(14165),U=r(32163),Q=r(86787),V=r(77397),D=r(19833);const K="esri.layers.buildingSublayers.BuildingComponentSublayer",J=p.Z.getLogger(K),G=(0,E.v)();let H=class extends(F.Z.LoadableMixin((0,x.v)(Z))){constructor(e){super(e),this.type="building-component",this.nodePages=null,this.materialDefinitions=[],this.textureSetDefinitions=[],this.geometryDefinitions=[],this.indexInfo=null,this.serviceUpdateTimeStamp=null,this.store=null,this.attributeStorageInfo=[],this.fields=[],this.associatedLayer=null,this.outFields=null,this.listMode="show",this.renderer=null,this.definitionExpression=null,this.popupEnabled=!0,this.popupTemplate=null,this.layerType="3d-object"}get parsedUrl(){return this.layer?{path:`${this.layer.parsedUrl?.path}/sublayers/${this.id}`,query:this.layer.parsedUrl?.query}:{path:""}}get fieldsIndex(){return new R.Z(this.fields)}readAssociatedLayer(e,t){const r=this.layer.associatedFeatureServiceItem,i=t.associatedLayerID;return(0,y.pC)(r)&&"number"==typeof i?new L.default({portalItem:r,layerId:i}):null}get objectIdField(){if(null!=this.fields)for(const e of this.fields)if("oid"===e.type)return e.name;return null}get displayField(){return(0,y.pC)(this.associatedLayer)?this.associatedLayer.displayField:void 0}get apiKey(){return this.layer.apiKey}get fullExtent(){return this.layer.fullExtent}get spatialReference(){return this.layer.spatialReference}get version(){return this.layer.version}get elevationInfo(){return this.layer.elevationInfo}get minScale(){return this.layer.minScale}get maxScale(){return this.layer.maxScale}get effectiveScaleRange(){return this.layer.effectiveScaleRange}get defaultPopupTemplate(){return this.createPopupTemplate()}load(e){const t=(0,y.pC)(e)?e.signal:null,r=this._fetchService(t).then((()=>{this.indexInfo=(0,N.T)(this.parsedUrl.path,this.rootNode,this.nodePages,this.apiKey,J,t)}));return this.addResolvingPromise(r),Promise.resolve(this)}createPopupTemplate(e){return(0,U.eZ)(this,e)}async _fetchService(e){const t=(await(0,S.default)(this.parsedUrl.path,{query:{f:"json",token:this.apiKey},responseType:"json",signal:e})).data;this.read(t,{origin:"service",url:this.parsedUrl})}getField(e){return this.fieldsIndex.get(e)}getFieldDomain(e,t){const r=this.getFeatureType(t?.feature)?.domains?.[e];return r&&"inherited"!==r.type?r:this.getField(e)?.domain??null}getFeatureType(e){return e&&(0,y.pC)(this.associatedLayer)?this.associatedLayer.getFeatureType(e):null}get types(){return(0,y.pC)(this.associatedLayer)?this.associatedLayer.types??[]:[]}get typeIdField(){return(0,y.pC)(this.associatedLayer)?this.associatedLayer.typeIdField:null}get geometryType(){return"3d-object"===this.layerType?"mesh":"point"}get profile(){return"3d-object"===this.layerType?"mesh-pyramids":"points"}get capabilities(){const e=(0,y.pC)(this.associatedLayer)&&this.associatedLayer.capabilities?this.associatedLayer.capabilities:B.C,{query:t,data:{supportsZ:r,supportsM:i,isVersioned:s}}=e;return{query:t,data:{supportsZ:r,supportsM:i,isVersioned:s}}}createQuery(){const e=new M.Z;return"mesh"!==this.geometryType&&(e.returnGeometry=!0,e.returnZ=!0),e.where=this.definitionExpression||"1=1",e.sqlFormat="standard",e}queryExtent(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryExtent(e||this.createQuery(),t)))}queryFeatureCount(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryFeatureCount(e||this.createQuery(),t)))}queryFeatures(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryFeatures(e||this.createQuery(),t))).then((e=>{if(e?.features)for(const t of e.features)t.layer=this.layer,t.sourceLayer=this;return e}))}queryObjectIds(e,t){return this._getAssociatedLayerForQuery().then((r=>r.queryObjectIds(e||this.createQuery(),t)))}async queryCachedAttributes(e,t){const r=(0,k.Lk)(this.fieldsIndex,await(0,D.e)(this,(0,D.V)(this)));return(0,V.xe)(this.parsedUrl.path,this.attributeStorageInfo,e,t,r)}async queryCachedFeature(e,t){const r=await this.queryCachedAttributes(e,[t]);if(!r||0===r.length)throw new n.Z("scenelayer:feature-not-in-cached-data","Feature not found in cached data");const i=new m.Z;return i.attributes=r[0],i.layer=this,i.sourceLayer=this,i}getFieldUsageInfo(e){return this.fieldsIndex.has(e)?{supportsLabelingInfo:!1,supportsRenderer:!1,supportsPopupTemplate:!1,supportsLayerQuery:!1}:{supportsLabelingInfo:!1,supportsRenderer:!0,supportsPopupTemplate:!0,supportsLayerQuery:(0,y.pC)(this.associatedLayer)}}_getAssociatedLayerForQuery(){const e=this.associatedLayer;return(0,y.pC)(e)&&e.loaded?Promise.resolve(e):this._loadAssociatedLayerForQuery()}async _loadAssociatedLayerForQuery(){if(await this.load(),(0,y.Wi)(this.associatedLayer))throw new n.Z("buildingscenelayer:query-not-available","BuildingSceneLayer component layer queries are not available without an associated feature layer",{layer:this});try{await this.associatedLayer.load()}catch(e){throw new n.Z("buildingscenelayer:query-not-available","BuildingSceneLayer associated feature layer could not be loaded",{layer:this,error:e})}return this.associatedLayer}};(0,i._)([(0,h.Cb)({readOnly:!0})],H.prototype,"parsedUrl",null),(0,i._)([(0,h.Cb)({type:P.U4,readOnly:!0})],H.prototype,"nodePages",void 0),(0,i._)([(0,h.Cb)({type:[P.QI],readOnly:!0})],H.prototype,"materialDefinitions",void 0),(0,i._)([(0,h.Cb)({type:[P.Yh],readOnly:!0})],H.prototype,"textureSetDefinitions",void 0),(0,i._)([(0,h.Cb)({type:[P.H3],readOnly:!0})],H.prototype,"geometryDefinitions",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],H.prototype,"serviceUpdateTimeStamp",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],H.prototype,"store",void 0),(0,i._)([(0,h.Cb)({type:String,readOnly:!0,json:{read:{source:"store.rootNode"}}})],H.prototype,"rootNode",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],H.prototype,"attributeStorageInfo",void 0),(0,i._)([(0,h.Cb)(G.fields)],H.prototype,"fields",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],H.prototype,"fieldsIndex",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:L.default})],H.prototype,"associatedLayer",void 0),(0,i._)([(0,f.r)("service","associatedLayer",["associatedLayerID"])],H.prototype,"readAssociatedLayer",null),(0,i._)([(0,h.Cb)(G.outFields)],H.prototype,"outFields",void 0),(0,i._)([(0,h.Cb)({type:String,readOnly:!0})],H.prototype,"objectIdField",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:String,json:{read:!1}})],H.prototype,"displayField",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:String})],H.prototype,"apiKey",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:O.Z})],H.prototype,"fullExtent",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:v.Z})],H.prototype,"spatialReference",null),(0,i._)([(0,h.Cb)({readOnly:!0})],H.prototype,"version",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:Q.Z})],H.prototype,"elevationInfo",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:Number})],H.prototype,"minScale",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:Number})],H.prototype,"maxScale",null),(0,i._)([(0,h.Cb)({readOnly:!0,type:Number})],H.prototype,"effectiveScaleRange",null),(0,i._)([(0,h.Cb)({type:["hide","show"],json:{write:!0}})],H.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)({types:w.o,json:{origins:{service:{read:{source:"drawingInfo.renderer"}}},name:"layerDefinition.drawingInfo.renderer",write:!0},value:null})],H.prototype,"renderer",void 0),(0,i._)([(0,h.Cb)({type:String,json:{origins:{service:{read:!1,write:!1}},name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],H.prototype,"definitionExpression",void 0),(0,i._)([(0,h.Cb)(A.C_)],H.prototype,"popupEnabled",void 0),(0,i._)([(0,h.Cb)({type:C.Z,json:{read:{source:"popupInfo"},write:{target:"popupInfo"}}})],H.prototype,"popupTemplate",void 0),(0,i._)([(0,h.Cb)({readOnly:!0,type:String,json:{origins:{service:{read:{source:"store.normalReferenceFrame"}}},read:!1}})],H.prototype,"normalReferenceFrame",void 0),(0,i._)([(0,h.Cb)({readOnly:!0,json:{read:!1}})],H.prototype,"defaultPopupTemplate",null),(0,i._)([(0,h.Cb)()],H.prototype,"types",null),(0,i._)([(0,h.Cb)()],H.prototype,"typeIdField",null),(0,i._)([(0,h.Cb)({json:{write:!1}}),(0,I.J)(new j.X({"3DObject":"3d-object",Point:"point"}))],H.prototype,"layerType",void 0),(0,i._)([(0,h.Cb)()],H.prototype,"geometryType",null),(0,i._)([(0,h.Cb)()],H.prototype,"profile",null),(0,i._)([(0,h.Cb)({readOnly:!0,json:{read:!1}})],H.prototype,"capabilities",null),H=(0,i._)([(0,g.j)(K)],H);const W=H;var z,X=r(20941);const Y={type:s.Z,readOnly:!0,json:{origins:{service:{read:{source:"sublayers",reader:$}}},read:!1}};function $(e,t,r){if(e&&Array.isArray(e))return new s.Z(e.map((e=>{const t=function(e){return"group"===e.layerType?ee:W}(e);if(t){const i=new t;return i.read(e,r),i}return r&&r.messages&&e&&r.messages.push(new X.Z("building-scene-layer:unsupported-sublayer-type","Building scene sublayer of type '"+(e.type||"unknown")+"' are not supported",{definition:e,context:r})),null})))}let ee=z=class extends Z{constructor(e){super(e),this.type="building-group",this.listMode="show",this.sublayers=null}loadAll(){return(0,a.w)(this,(e=>z.forEachSublayer(this.sublayers,(t=>{"building-group"!==t.type&&e(t)}))))}};var te;(0,i._)([(0,h.Cb)({type:["hide","show","hide-children"],json:{write:!0}})],ee.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)(Y)],ee.prototype,"sublayers",void 0),ee=z=(0,i._)([(0,g.j)("esri.layers.buildingSublayers.BuildingGroupSublayer")],ee),(te=ee||(ee={})).sublayersProperty=Y,te.readSublayers=$,te.forEachSublayer=function e(t,r){t.forEach((t=>{r(t),"building-group"===t.type&&e(t.sublayers,r)}))};const re=ee;var ie=r(54295),se=r(17287),oe=r(38009),ne=r(16859),le=r(72965),ae=r(20559),pe=r(96674),ye=r(41123);let de=class extends pe.wq{constructor(){super(...arguments),this.type=null}};(0,i._)([(0,h.Cb)({type:String,readOnly:!0,json:{write:!0}})],de.prototype,"type",void 0),de=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterAuthoringInfo")],de);const ue=de;var ce;let he=ce=class extends pe.wq{constructor(){super(...arguments),this.filterType=null,this.filterValues=null}clone(){return new ce({filterType:this.filterType,filterValues:(0,l.d9)(this.filterValues)})}};(0,i._)([(0,h.Cb)({type:String,json:{write:!0}})],he.prototype,"filterType",void 0),(0,i._)([(0,h.Cb)({type:[String],json:{write:!0}})],he.prototype,"filterValues",void 0),he=ce=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterAuthoringInfoType")],he);const be=he;var fe;const ge=s.Z.ofType(be);let ve=fe=class extends pe.wq{clone(){return new fe({filterTypes:(0,l.d9)(this.filterTypes)})}};(0,i._)([(0,h.Cb)({type:ge,json:{write:!0}})],ve.prototype,"filterTypes",void 0),ve=fe=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterAuthoringInfoBlock")],ve);const _e=ve;var me;const Ce=s.Z.ofType(_e);let we=me=class extends ue{constructor(){super(...arguments),this.type="checkbox"}clone(){return new me({filterBlocks:(0,l.d9)(this.filterBlocks)})}};(0,i._)([(0,h.Cb)({type:["checkbox"]})],we.prototype,"type",void 0),(0,i._)([(0,h.Cb)({type:Ce,json:{write:!0}})],we.prototype,"filterBlocks",void 0),we=me=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterAuthoringInfoCheckbox")],we);const Se=we;let je=class extends pe.wq{};(0,i._)([(0,h.Cb)({readOnly:!0,json:{read:!1}})],je.prototype,"type",void 0),je=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterMode")],je);const Fe=je;var xe;let Ie=xe=class extends Fe{constructor(){super(...arguments),this.type="solid"}clone(){return new xe}};(0,i._)([(0,h.Cb)({type:["solid"],readOnly:!0,json:{write:!0}})],Ie.prototype,"type",void 0),Ie=xe=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterModeSolid")],Ie);const Oe=Ie;var Le,Te=r(56332);let Ae=Le=class extends Fe{constructor(){super(...arguments),this.type="wire-frame",this.edges=null}clone(){return new Le({edges:(0,l.d9)(this.edges)})}};(0,i._)([(0,I.J)({wireFrame:"wire-frame"})],Ae.prototype,"type",void 0),(0,i._)([(0,h.Cb)(Te.Z)],Ae.prototype,"edges",void 0),Ae=Le=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterModeWireFrame")],Ae);const qe=Ae;var Ze;let Be=Ze=class extends Fe{constructor(){super(...arguments),this.type="x-ray"}clone(){return new Ze}};(0,i._)([(0,h.Cb)({type:["x-ray"],readOnly:!0,json:{write:!0}})],Be.prototype,"type",void 0),Be=Ze=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterModeXRay")],Be);const Ee=Be;var Re;const ke={nonNullable:!0,types:{key:"type",base:Fe,typeMap:{solid:Oe,"wire-frame":qe,"x-ray":Ee}},json:{read:e=>{switch(e&&e.type){case"solid":return Oe.fromJSON(e);case"wireFrame":return qe.fromJSON(e);case"x-ray":return Ee.fromJSON(e);default:return}},write:{enabled:!0,isRequired:!0}}};let Ne=Re=class extends pe.wq{constructor(){super(...arguments),this.filterExpression=null,this.filterMode=new Oe,this.title=""}clone(){return new Re({filterExpression:this.filterExpression,filterMode:(0,l.d9)(this.filterMode),title:this.title})}};(0,i._)([(0,h.Cb)({type:String,json:{write:{enabled:!0,isRequired:!0}}})],Ne.prototype,"filterExpression",void 0),(0,i._)([(0,h.Cb)(ke)],Ne.prototype,"filterMode",void 0),(0,i._)([(0,h.Cb)({type:String,json:{write:{enabled:!0,isRequired:!0}}})],Ne.prototype,"title",void 0),Ne=Re=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilterBlock")],Ne);const Pe=Ne;var Me;const Ue=s.Z.ofType(Pe);let Qe=Me=class extends pe.wq{constructor(){super(...arguments),this.description=null,this.filterBlocks=null,this.id=(0,ye.D)(),this.name=null}clone(){return new Me({description:this.description,filterBlocks:(0,l.d9)(this.filterBlocks),id:this.id,name:this.name,filterAuthoringInfo:(0,l.d9)(this.filterAuthoringInfo)})}};(0,i._)([(0,h.Cb)({type:String,json:{write:!0}})],Qe.prototype,"description",void 0),(0,i._)([(0,h.Cb)({type:Ue,json:{write:{enabled:!0,isRequired:!0}}})],Qe.prototype,"filterBlocks",void 0),(0,i._)([(0,h.Cb)({types:{key:"type",base:ue,typeMap:{checkbox:Se}},json:{read:e=>"checkbox"===(e&&e.type)?Se.fromJSON(e):null,write:!0}})],Qe.prototype,"filterAuthoringInfo",void 0),(0,i._)([(0,h.Cb)({type:String,constructOnly:!0,json:{write:{enabled:!0,isRequired:!0}}})],Qe.prototype,"id",void 0),(0,i._)([(0,h.Cb)({type:String,json:{write:{enabled:!0,isRequired:!0}}})],Qe.prototype,"name",void 0),Qe=Me=(0,i._)([(0,g.j)("esri.layers.support.BuildingFilter")],Qe);const Ve=Qe;let De=class extends pe.wq{constructor(){super(...arguments),this.fieldName=null,this.modelName=null,this.label=null,this.min=null,this.max=null,this.mostFrequentValues=null,this.subLayerIds=null}};(0,i._)([(0,h.Cb)({type:String})],De.prototype,"fieldName",void 0),(0,i._)([(0,h.Cb)({type:String})],De.prototype,"modelName",void 0),(0,i._)([(0,h.Cb)({type:String})],De.prototype,"label",void 0),(0,i._)([(0,h.Cb)({type:Number})],De.prototype,"min",void 0),(0,i._)([(0,h.Cb)({type:Number})],De.prototype,"max",void 0),(0,i._)([(0,h.Cb)({json:{read:e=>Array.isArray(e)&&(e.every((e=>"string"==typeof e))||e.every((e=>"number"==typeof e)))?e.slice():null}})],De.prototype,"mostFrequentValues",void 0),(0,i._)([(0,h.Cb)({type:[Number]})],De.prototype,"subLayerIds",void 0),De=(0,i._)([(0,g.j)("esri.layers.support.BuildingFieldStatistics")],De);let Ke=class extends(F.Z.LoadableMixin((0,x.v)(pe.wq))){constructor(){super(...arguments),this.url=null}get fields(){return this.loaded||"loading"===this.loadStatus?this._get("fields"):(p.Z.getLogger(this.declaredClass).error("building summary statistics are not loaded"),null)}load(e){const t=(0,y.pC)(e)?e.signal:null;return this.addResolvingPromise(this._fetchService(t)),Promise.resolve(this)}async _fetchService(e){const t=(await(0,S.default)(this.url,{query:{f:"json"},responseType:"json",signal:e})).data;this.read(t,{origin:"service"})}};(0,i._)([(0,h.Cb)({constructOnly:!0,type:String})],Ke.prototype,"url",void 0),(0,i._)([(0,h.Cb)({readOnly:!0,type:[De],json:{read:{source:"summary"}}})],Ke.prototype,"fields",null),Ke=(0,i._)([(0,g.j)("esri.layers.support.BuildingSummaryStatistics")],Ke);const Je=Ke;var Ge=r(14147);const He=s.Z.ofType(Ve),We=(0,l.d9)(re.sublayersProperty);We.json.origins["web-scene"]={type:[W],write:{enabled:!0,overridePolicy:()=>({enabled:!1})}},We.json.origins["portal-item"]={type:[W],write:{enabled:!0,overridePolicy:()=>({enabled:!1})}};let ze=class extends((0,ae.Vt)((0,se.Y)((0,oe.q)((0,ne.I)((0,le.M)((0,d.R)((0,ie.V)(_.Z)))))))){constructor(e){super(e),this.operationalLayerType="BuildingSceneLayer",this.allSublayers=new o.Z({getCollections:()=>[this.sublayers],getChildrenFunction:e=>"building-group"===e.type?e.sublayers:null}),this.sublayers=null,this._sublayerOverrides=null,this.filters=new He,this.activeFilterId=null,this.summaryStatistics=null,this.outFields=null,this.type="building-scene"}normalizeCtorArgs(e){return"string"==typeof e?{url:e}:e??{}}destroy(){this.allSublayers.destroy()}readSublayers(e,t,r){const i=re.readSublayers(e,t,r);return re.forEachSublayer(i,(e=>e.layer=this)),this._sublayerOverrides&&(this.applySublayerOverrides(i,this._sublayerOverrides),this._sublayerOverrides=null),i}applySublayerOverrides(e,{overrides:t,context:r}){re.forEachSublayer(e,(e=>e.read(t.get(e.id),r)))}readSublayerOverrides(e,t){const r=new Map;for(const i of e)null!=i&&"object"==typeof i&&"number"==typeof i.id?r.set(i.id,i):t.messages?.push(new n.Z("building-scene-layer:invalid-sublayer-override","Invalid value for sublayer override. Not an object or no id specified.",{value:i}));return{overrides:r,context:t}}writeSublayerOverrides(e,t,r){const i=[];re.forEachSublayer(this.sublayers,(e=>{const t=e.write({},r);Object.keys(t).length>1&&i.push(t)})),i.length>0&&(t.sublayers=i)}writeUnappliedOverrides(e,t){t.sublayers=[],e.overrides.forEach((e=>{t.sublayers.push((0,l.d9)(e))}))}write(e,t){return e=super.write(e,t),!t||"web-scene"!==t.origin&&"portal-item"!==t.origin||(this.sublayers?this.writeSublayerOverrides(this.sublayers,e,t):this._sublayerOverrides&&this.writeUnappliedOverrides(this._sublayerOverrides,e)),e}read(e,t){if(super.read(e,t),t&&("web-scene"===t.origin||"portal-item"===t.origin)&&null!=e&&Array.isArray(e.sublayers)){const r=this.readSublayerOverrides(e.sublayers,t);this.sublayers?this.applySublayerOverrides(this.sublayers,r):this._sublayerOverrides=r}}readSummaryStatistics(e,t){if("string"==typeof t.statisticsHRef){const e=(0,c.v_)(this.parsedUrl?.path,t.statisticsHRef);return new Je({url:e})}return null}set elevationInfo(e){this._set("elevationInfo",e),this._validateElevationInfo()}load(e){const t=(0,y.pC)(e)?e.signal:null,r=this.loadFromPortal({supportedTypes:["Scene Service"]},e).catch(u.r9).then((()=>this._fetchService(t))).then((()=>this._fetchAssociatedFeatureService(t)));return this.addResolvingPromise(r),Promise.resolve(this)}loadAll(){return(0,a.G)(this,(e=>{re.forEachSublayer(this.sublayers,(t=>{"building-group"!==t.type&&e(t)})),this.summaryStatistics&&e(this.summaryStatistics)}))}async saveAs(e,t){return this._debouncedSaveOperations(ae.xp.SAVE_AS,{...t,getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"building-scene"},e)}async save(){const e={getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:"building-scene"};return this._debouncedSaveOperations(ae.xp.SAVE,e)}validateLayer(e){if(!e.layerType||"Building"!==e.layerType)throw new n.Z("buildingscenelayer:layer-type-not-supported","BuildingSceneLayer does not support this layer type",{layerType:e.layerType})}_getTypeKeywords(){return["Building"]}_validateElevationInfo(){const e=this.elevationInfo;e&&("absolute-height"!==e.mode&&p.Z.getLogger(this.declaredClass).warn(".elevationInfo=","Building scene layers only support absolute-height elevation mode"),e.featureExpressionInfo&&"0"!==e.featureExpressionInfo.expression&&p.Z.getLogger(this.declaredClass).warn(".elevationInfo=","Building scene layers do not support featureExpressionInfo"))}async _fetchAssociatedFeatureService(e){const t=new Ge.W(this.parsedUrl,this.portalItem,this.apiKey,e);try{this.associatedFeatureServiceItem=await t.fetchPortalItem()}catch(e){p.Z.getLogger(this.declaredClass).warn("Associated feature service item could not be loaded",e)}}};(0,i._)([(0,h.Cb)({type:["BuildingSceneLayer"]})],ze.prototype,"operationalLayerType",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],ze.prototype,"allSublayers",void 0),(0,i._)([(0,h.Cb)(We)],ze.prototype,"sublayers",void 0),(0,i._)([(0,f.r)("service","sublayers")],ze.prototype,"readSublayers",null),(0,i._)([(0,h.Cb)({type:He,nonNullable:!0,json:{write:!0}})],ze.prototype,"filters",void 0),(0,i._)([(0,h.Cb)({type:String,json:{write:!0}})],ze.prototype,"activeFilterId",void 0),(0,i._)([(0,h.Cb)({readOnly:!0,type:Je})],ze.prototype,"summaryStatistics",void 0),(0,i._)([(0,f.r)("summaryStatistics",["statisticsHRef"])],ze.prototype,"readSummaryStatistics",null),(0,i._)([(0,h.Cb)({type:[String],json:{read:!1}})],ze.prototype,"outFields",void 0),(0,i._)([(0,h.Cb)(A.vg)],ze.prototype,"fullExtent",void 0),(0,i._)([(0,h.Cb)({type:["show","hide","hide-children"]})],ze.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)((0,A.Lx)(v.Z))],ze.prototype,"spatialReference",void 0),(0,i._)([(0,h.Cb)(A.PV)],ze.prototype,"elevationInfo",null),(0,i._)([(0,h.Cb)({json:{read:!1},readOnly:!0})],ze.prototype,"type",void 0),(0,i._)([(0,h.Cb)()],ze.prototype,"associatedFeatureServiceItem",void 0),ze=(0,i._)([(0,g.j)("esri.layers.BuildingSceneLayer")],ze);const Xe=ze}}]);