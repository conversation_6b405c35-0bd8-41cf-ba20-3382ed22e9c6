package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;
import org.thingsboard.server.dao.util.imodel.query.annotations.RequestCheckIgnore;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.util.*;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus.PENDING;

@Getter
@Setter
public class MessageRecordSendRequest implements Requestible, AwareCurrentUserUUID, AwareTenantUUID {
    // 接收人
    @NotNullOrEmpty
    private String[] receiveUserIdList;

    // 接收人手机号
    @NotNullOrEmpty
    private String[] receiveUserPhoneList;

    // 模板id
    @NotNullOrEmpty
    private String templateId;

    // 短信内容
    private String content;

    // 短信模板编号
    private String code;

    // 短信签名
    private String signKey;

    // 模板变量
    @Setter(AccessLevel.NONE)
    private Map<String, String> variables;

    // 发送时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    // 是否可靠发送，仅消息队列模式有效
    private Boolean reliable = false;

    // 发送人 ignore
    private String sendUserId;

    // 客户id ignore
    private String tenantId;

    @RequestCheckIgnore
    private final InfoWrapper mockList;

    public MessageRecordSendRequest() {
        mockList = new InfoWrapper();
    }

    @Override
    public String valid(IStarHttpRequest request) {
        if (receiveUserIdList.length != receiveUserPhoneList.length) {
            return "用户编号和手机号不匹配";
        }
        return null;
    }

    @Override
    public void currentUserId(String uuid) {
        sendUserId = uuid;
    }

    @Override
    public void tenantId(String uuid) {
        tenantId = uuid;
    }

    public void resolveVariables(String templateContent, String variableNames) {
        // TODO: [LFT] 可进行算法优化
        // 假设变量列表为 name,phone
        //    短信模板为 你好xx请拨打xx电话
        //    短信内容为 你好某某人请拨打123456789电话
        // 那么就能匹配出 name=某某人、电话=123456789
        String[] variableNameArr = variableNames.split(",");
        String[] contentSegmentArr = templateContent.split("xx");
        if (variableNameArr.length != contentSegmentArr.length - 1) {
            ExceptionUtils.silentThrow("模板变量数量有误，请检查数据库");
        }
        int variableCount = variableNameArr.length;
        if (variableCount == 0) {
            variables = Collections.emptyMap();
            return;
        }
        variables = new HashMap<>();
        String firstContent = contentSegmentArr[0];
        String rest = content.substring(firstContent.length());

        for (int i = 1; i < contentSegmentArr.length; i++) {
            int contentLength = rest.indexOf(contentSegmentArr[i]);
            String variableName = variableNameArr[i - 1];
            String variableValue = rest.substring(0, contentLength);

            if (i != variableNameArr.length) {
                rest = rest.substring(variableValue.length() + contentLength);
            }

            variables.put(variableName, variableValue);
        }
    }

    public class InfoWrapper extends AbstractList<MessageRecordSendRequest.MessageRecordSendRequestInfo> {
        @Override
        public MessageRecordSendRequestInfo get(int index) {
            MessageRecordSendRequestInfo info = new MessageRecordSendRequestInfo();
            info.sync(index);
            return info;
        }

        @Override
        public int size() {
            return receiveUserIdList.length;
        }

    }

    @Getter
    @Setter
    public class MessageRecordSendRequestInfo {
        private String id;

        // 接收人
        private String receiveUserId;

        // 接收人电话
        private String receiveUserPhone;

        // 模板id
        private String templateId;

        // 短信模板编号
        private String code;

        // 短信签名
        private String signKey;

        // 变量
        private String variables;

        // 发送人
        private String sendUserId;

        // 创建人
        private String creator;

        // 状态
        private MessageRecordStatus status;

        // 发送时间
        private Date sendTime;

        // 客户id
        private String tenantId;

        public MessageRecordSendRequestInfo() {
            this.templateId = MessageRecordSendRequest.this.templateId;
            this.variables = JSON.toJSONString(MessageRecordSendRequest.this.variables);
            this.sendUserId = MessageRecordSendRequest.this.sendUserId;
            this.creator = MessageRecordSendRequest.this.sendUserId;
            this.tenantId = MessageRecordSendRequest.this.tenantId;
            this.code = MessageRecordSendRequest.this.code;
            this.signKey = MessageRecordSendRequest.this.signKey;
            status = PENDING;
            this.sendTime = MessageRecordSendRequest.this.sendTime;
            // this.sendTime = this.sendTime == null ? new Date() : sendTime;
        }

        public void sync(int i) {
            this.id = IdWorker.get32UUID();
            this.receiveUserId = MessageRecordSendRequest.this.receiveUserIdList[i];
            this.receiveUserPhone = MessageRecordSendRequest.this.receiveUserPhoneList[i];
        }

        public MessageQueueMessageRecordSendRequestInfo toMessageQueueEntity() {
            return new MessageQueueMessageRecordSendRequestInfo(this);
        }

    }

}
