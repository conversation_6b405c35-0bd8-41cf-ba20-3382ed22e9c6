import{_ as S}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as k}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{d as C,M as q,c as b,r as p,s as y,S as w,o as T,g as V,n as B,q as m,i as f,al as F,ak as L}from"./index-r0dFAfgr.js";import{f as M}from"./DateFormatter-Bm9a68Ax.js";import{a as Y,b as $,c as W}from"./stationCircuit-CGrX5qR4.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const E={class:"wrapper"},J=C({__name:"index",setup(H){const{$messageSuccess:u,$messageError:s,$messageWarning:I}=q(),l=b(),d=b(),x=p({filters:[{label:"方案名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:y(F),click:()=>i()},{text:"新增",perm:!0,type:"success",svgIcon:y(L),click:()=>{var a;r.defaultValue={},r.title="新增",(a=l.value)==null||a.openDialog()}}]}]}),t=p({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"方案名称"},{prop:"createUserName",label:"创建人"},{prop:"createTime",label:"添加时间",formatter:(a,e)=>M(e,"YYYY-MM-DD HH:mm:ss")}],operations:[{text:"编辑",isTextBtn:!0,perm:!0,icon:"iconfont icon-bianji",click:a=>{var e;r.title="编辑",r.defaultValue={...a},(e=l.value)==null||e.openDialog()}},{perm:!0,text:"删除",isTextBtn:!0,type:"danger",icon:"iconfont icon-shanchu",click:a=>D(a)}],operationWidth:"200px",pagination:{refreshData:({page:a,size:e})=>{t.pagination.limit=e,t.pagination.page=a,i()}}}),r=p({title:"知识库",defaultValue:{},dialogWidth:800,group:[{fields:[{type:"input",label:"方案名称",field:"name",rules:[{required:!0,message:"请填写方案名称"}]},{type:"wangeditor",label:"方案内容",field:"content",rules:[{required:!0,message:"请填写方案内容"}]}]}],submit:a=>{Y(a).then(e=>{var o,n;((o=e.data)==null?void 0:o.code)===200?u("保存成功"):s("保存失败"),i(),(n=l.value)==null||n.closeDialog()}).catch(e=>{s(e)})}}),i=async()=>{var o;t.loading=!0;const e={...((o=d.value)==null?void 0:o.queryParams)||{},size:t.pagination.limit||20,page:t.pagination.page||1};$(e).then(n=>{var c,g,_,h;t.dataList=((g=(c=n.data)==null?void 0:c.data)==null?void 0:g.data)||[],t.pagination.total=((h=(_=n.data)==null?void 0:_.data)==null?void 0:h.total)||0,t.loading=!1}).catch(n=>{s(n),t.loading=!1})},D=a=>{w("确定删除指定养护方案?","删除提示").then(()=>{W([a.id]).then(e=>{var o;((o=e.data)==null?void 0:o.code)===200?(u("删除成功"),i()):s("删除失败")}).catch(e=>{s(e)})})};return T(()=>{i()}),(a,e)=>{const o=v,n=k,c=S;return V(),B("div",E,[m(o,{ref_key:"refSearch",ref:d,config:f(x)},null,8,["config"]),m(n,{config:f(t),class:"card-table"},null,8,["config"]),m(c,{ref_key:"refForm",ref:l,config:f(r)},null,8,["config"])])}}});export{J as default};
