<template>
  <div class="location-map">
    <div
      v-if="!hideInput"
      class="location-search-box"
    >
      <div class="location-input-box">
        <span class="location-label">经度：</span>
        <el-input-number
          v-model="state.x"
          :disabled="disabled"
          :precision="4"
          size="small"
          class="location-input"
          @change="handleInputChange"
        ></el-input-number>
        <span class="location-label margin-l-10">纬度：</span>
        <el-input-number
          v-model="state.y"
          :disabled="disabled"
          :precision="4"
          size="small"
          class="location-input"
          @change="handleInputChange"
        ></el-input-number>
      </div>
      <p
        v-if="!disabled"
        class="message-text"
      >
        提示：请拖动地图 设置位置信息
      </p>
    </div>
    <div class="get-location-box amap-container">
      <div
        id="innerAmap"
        style="height: 100%; width: 100%"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, onUnmounted, reactive, toRefs, watch } from 'vue'
import useAmap from '@/hooks/amap/useAmap'
import { getMapLocationImageUrl } from '@/utils/URLHelper'

interface markersType {
  items: number[][]
  icon?: string
}
type MapType = '标准图层' | '卫星图层' | '路网图层' | '实时交通图层' | '楼块图层'
const props = withDefaults(
  defineProps<{
    modelValue?: number[] | string
    initCenterMark?: boolean
    light?: boolean
    hideInput?: boolean
    polylines?: IPolyline[]
    resultType?: 'arrStr' | 'str'
    required?: boolean
    disabled?: boolean
    markers?: markersType[]
    maplayer?: MapType[]
    useHeatMap?: boolean
    heatMapData?: { lng: number; lat: number; count: number }[]
  }>(),
  {
    modelValue: () => [],
    initCenterMark: false,
    light: true,
    hideInput: false,
    polylines: () => [],
    resultType: 'arrStr',
    required: false,
    disabled: false,
    markers: () => [],
    maplayer: () => [],
    useHeatMap: false,
    heatMapData: () => []
  }
)

const emit = defineEmits(['update:modelValue', 'onmaploaded', 'getplacename'])
let timer: any

const defaultCenter: number[] = window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter || [106.5, 29.84]
const state = reactive<{
  x?: number
  y?: number
  centerMarker: any
  amap: any
}>({
  x: undefined,
  y: undefined,
  centerMarker: null,
  amap: null
})
const {
  setMarker,
  setCenter,
  initAMap,
  setPolylines,
  setInfoWindow,
  setInfoWindows,
  setMultiInfoWindows,
  clearInfoWindow,
  setMarkers,
  initHeatMap,
  resetHeatMap,
  toggleHeatMap,
  setListInfoWindow,
  clear,
  getGeocoder
} = useAmap()
const mapLocationImg = getMapLocationImageUrl()
const setMark = () => {
  if (state.x !== undefined && state.y !== undefined) {
    state.centerMarker = setMarker([state.x, state.y], {
      icon: mapLocationImg
    })
  } else {
    state.centerMarker = setMarker(defaultCenter, {
      icon: mapLocationImg
    })
  }
}
const positionValid = computed(() => state.x !== undefined && state.y !== undefined)
watch(
  () => props.modelValue,
  () => {
    formateModelValue()
    handleInputChange()
  }
)
const handleInputChange = () => {
  const center = handleLocation()
  setCenterMarkerPosition(center)
}
const initMap = async () => {
  const mapConfig = {
    // 地图要绑定的事件
    events: {
      mapmove: (map: any) => {
        if (!props.disabled) {
          const center = map.getCenter()
          state.x = center.lng
          state.y = center.lat
          setCenterMarkerPosition([state.x, state.y])
          // 如果disabled，则移动地图不更新表单
          updateValue()
        }

        // if (!props.disabled) {
        //   if (state.x !== center.lng && state.y !== center.lat) {
        //   }
        // }
      },
      zoomend: () => {
        props.useHeatMap && resetHeatMap(props.heatMapData)
      }
    },
    search: {
      input: 'innerAmapSearch',
      select: (center: number[]) => {
        setCenter(center)
      }
    },

    mapStyle: props.light === false ? 'amap://styles/darkblue' : '',

    mapLayer: props.maplayer ? props.maplayer : []
  }

  state.amap = await initAMap('innerAmap', mapConfig)

  initPolylines(mapConfig)
  handleInputChange()
  emit('onmaploaded')

  multiplePunctuation()
}
/**
 * 画多条线
 */
const initPolylines = (config: any) => {
  setPolylines(props.polylines, config)
}
const handleLocation = () => {
  const center = positionValid.value ? [state.x, state.y] : defaultCenter
  state.amap && state.amap.setCenter(center)
  return center
}
const setCenterMarkerPosition = (center?: (number | undefined)[]) => {
  if (!center || center[0] === undefined || center[1] === undefined) {
    center = defaultCenter
  }
  if (!state.centerMarker && props.initCenterMark) setMark()
  typeof state.centerMarker?.setPosition === 'function' && state.centerMarker.setPosition(center)
}
const updateValue = () => {
  let result = ''
  if (props.resultType === 'arrStr') {
    result = positionValid.value ? JSON.stringify([state.x, state.y]) : JSON.stringify('')
  } else {
    result = positionValid.value ? [state.x, state.y].join(',') : JSON.stringify('')
  }
  myDebounce(() => {
    getGeocoder('1', mm => {
      emit('getplacename', mm)
    })
  }, 200)

  emit('update:modelValue', result)
}

const formateModelValue = () => {
  if (props.modelValue instanceof Array && props.modelValue.length === 2) {
    state.x = parseFloat(props.modelValue[0]?.toString())
    state.y = parseFloat(props.modelValue[1]?.toString())
  } else {
    const value = props.modelValue && props.modelValue.toString()
    try {
      const lonlat = JSON.parse(value)
      if (lonlat instanceof Array) {
        lonLatToState(lonlat)
      }
    } catch (error) {
      // 传入的是以','分隔的字符串
      const lonlat = value?.trim().split(',')
      lonLatToState(lonlat)
    }
  }
}
const lonLatToState = (lonLat: any[]) => {
  if (lonLat?.length === 2) {
    const x = parseFloat(lonLat[0])
    const y = parseFloat(lonLat[1])
    state.x = isNaN(x) ? undefined : x
    state.y = isNaN(y) ? undefined : y
  }
}
// 多标点(自定义图标)
const multiplePunctuation = (click?: (mark: any) => void) => {
  console.log(props.markers)

  props.markers.forEach(item => {
    if (!item.icon) {
      item.icon = mapLocationImg
    }
    state.centerMarker = setMarkers(
      item.items ? item.items : [],
      {
        icon: item.icon
      },
      click
    )
  })
}

const myDebounce = (fn: () => void, delay: number | undefined) => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    fn()
  }, delay)
}

onBeforeMount(() => {
  formateModelValue()
})
const handleClose = e => {
  const target = e.target as HTMLElement
  if (target?.className?.includes && target.className.includes('close-wrapper')) {
    clearInfoWindow()
  }
}
// codes here
onMounted(async () => {
  initMap()
  document.addEventListener('click', handleClose)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClose)
})
defineExpose({
  ...toRefs(state),
  formateModelValue,
  handleLocation,
  initMap,
  positionValid,
  updateValue,
  setInfoWindow,
  setInfoWindows,
  clearInfoWindow,
  setMultiInfoWindows,
  setMark,
  setMarker,
  setCenter,
  initHeatMap,
  resetHeatMap,
  toggleHeatMap,
  multiplePunctuation,
  setListInfoWindow,
  clear
})
</script>

<style lang="scss" scoped>
// @import 'src/assets/css/amapSearchStyle.scss';

.message-text {
  margin: 10px 0 12px 20px;
  color: #39b01c;
  line-height: initial;
}

.get-location-box {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.amap-info-contentContainer {
  background-color: var(--el-bg-color);
}

html.dark {
  .amap-info-contentContainer.bottom-center {
    &::after {
      border: 5px solid #383e53;
    }
  }

  .amap-pop-tag {
    background-color: #383e53;
  }

  .amap-info-contentContainer {
    background-color: #383e53;

    .amap-info-content {
      background-color: #383e53;
    }
  }
}

.amap-info-contentContainer.bottom-center {
  &::after {
    content: ' ';
    width: 0;
    height: 0;
    position: absolute;
    left: 50%;
    transform: rotate(45deg) translateY(50%) translateX(-50%);
    border: 5px solid var(--el-bg-color);
    z-index: -1;
  }
}
</style>
