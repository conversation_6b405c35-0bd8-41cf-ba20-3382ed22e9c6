package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionContract implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 编号
    private String code;

    // 合同名称
    private String name;

    // 合同类型
    private String type;

    // 合同类型
    private String typeName;

    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    private String constructionName;

    // 甲方单位
    private String firstpartOrganization;

    // 甲方代表
    private String firstpartRepresentative;

    // 甲方代表联系电话
    private String firstpartPhone;

    // 乙方单位
    private String secondpartOrganization;

    // 乙方联系人
    private String secondpartRepresentative;

    // 乙方联系电话
    private String secondpartPhone;

    // 合同总金额，万元
    private BigDecimal cost;

    // 合同工期（开始时间）
    private Date workTimeBegin;

    // 合同工期（完成时间）
    private Date workTimeEnd;

    // 签署时间
    private Date signTime;
    // 详细说明
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 最后更新时间
    private Date updateTime;

    // 客户id
    private String tenantId;

}
