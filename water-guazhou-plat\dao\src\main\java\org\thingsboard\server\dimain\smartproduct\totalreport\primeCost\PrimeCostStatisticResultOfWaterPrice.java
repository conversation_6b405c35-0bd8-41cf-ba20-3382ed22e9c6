package org.thingsboard.server.dimain.smartproduct.totalreport.primeCost;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class PrimeCostStatisticResultOfWaterPrice {
    // 项目名称
    private String factory;

    // 项目期间水价
    private BigDecimal price;

    // 项目总水量
    private BigDecimal totalWaterAmount;

    // 项目总水价
    private BigDecimal totalWaterPrice;

    // 去年同期项目总水价
    private BigDecimal totalWaterPriceLastYear;

    // 费用小计
    private BigDecimal totalPrice;

    // 去年同期费用小计
    private BigDecimal totalPriceLastYear;

    // 费用小计同比增长
    private BigDecimal totalPriceCurrentRangeIncreaseCompareToLastYearTheRange;

    // 吨水成本小计
    private BigDecimal totalPrimeCostPerWater;

    // 格式化好的费用小计同比增长
    public String getTotalPriceCurrentRangeIncreaseCompareToLastYearTheRangeName() {
        if (totalPriceCurrentRangeIncreaseCompareToLastYearTheRange == null) {
            return "";
        }
        return totalPriceCurrentRangeIncreaseCompareToLastYearTheRange + "%";
    }

}
