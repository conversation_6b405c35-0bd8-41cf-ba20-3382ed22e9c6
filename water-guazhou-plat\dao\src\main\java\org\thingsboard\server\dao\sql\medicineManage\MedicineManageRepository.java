package org.thingsboard.server.dao.sql.medicineManage;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.MedicineManage;

import java.util.Date;
import java.util.List;

public interface MedicineManageRepository extends JpaRepository<MedicineManage, String> {
    Page<MedicineManage> findByStationId(String stationId, Pageable pageable);

    List<MedicineManage> findByStationId(String stationId);

    @Query("SELECT m.medicineType FROM MedicineManage m GROUP BY m.medicineType")
    List<String> groupByMedicineType();

    @Query("SELECT m.medicineType,COUNT(m.num) FROM MedicineManage m " +
            "WHERE m.tenantId = ?1 AND m.time BETWEEN ?2 AND ?3 " +
            "GROUP BY m.medicineType")
    List countByType(String tenantId, Date startTime, Date endTime);

    List<MedicineManage> findByTimeBetweenAndTenantId(Date startTime, Date endTime, String tenantId);

}
