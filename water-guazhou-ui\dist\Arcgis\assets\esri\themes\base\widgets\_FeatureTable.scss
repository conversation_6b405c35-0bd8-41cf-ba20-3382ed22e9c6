$base-scrim-z-index: 50;

@mixin esri-feature-table__prompt($contextColor) {
  animation: esri-fade-in-up 250ms ease-in-out;
  background-color: var(--calcite-ui-background);
  border: solid 3px $contextColor;
  color: $font-color;
  @include defaultBoxShadow();
  display: flex;
  flex-flow: column nowrap;
  max-height: 85%;
  padding: $cap-spacing $side-spacing;

  .esri-feature-table__prompt__header {
    color: $contextColor;
    display: flex;
    flex: 0 0 auto;
    margin-bottom: $cap-spacing;

    &__heading {
      color: $heading-color;
      margin: 0 $side-spacing--quarter;
    }
  }

  .esri-feature-table__prompt__message {
    color: inherit;
    font-weight: $font-weight;
    flex: 1 1 auto;
    overflow: auto;
    overflow-x: hidden;
    padding: 0 0 $cap-spacing 0;
    word-break: break-word;
  }

  .esri-feature-table__prompt__divider {
    border-bottom: $border;
    border-color: $border-color--subtle;
    color: inherit;
    flex: 0 0 auto;
    margin: 0 0 $cap-spacing 0;
  }

  .esri-feature-table__prompt__actions {
    display: flex;
    flex-direction: row;
  }
}

@mixin featureTable() {
  .esri-feature-table {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;

    .esri-feature-table__content {
      flex: 1 1 0;
      flex-grow: 1;
      min-height: 0;
      margin: 0;
      padding: 0;
    }

    .esri-grid--no-column-menu {
      .esri-column__menu-container {
        display: none;
      }
    }

    .esri-grid__grid {
      height: 100%;
      width: 100%;

      .esri-column__menu-container.esri-button-menu {
        height: 28px;
        width: 28px;
        position: inherit;

        .esri-button-menu__button {
          height: 26px;
          width: 16px;
        }
      }
    }

    .esri-grid,
    .esri-feature-table__menu,
    .esri-column__menu-container,
    .esri-button-menu,
    .esri-button-menu__button {
      background-color: transparent;
    }

    vaadin-grid {
      font-family: $font-family;
      font-size: $font-size;
      background-color: inherit;
      color: inherit;

      ::part(body-cell) {
        background-color: transparent;
        font-size: $font-size;
      }

      ::part(row):hover {
        background-color: var(--lumo-row-background-hover);
      }
    }
  }

  .esri-feature-table__menu {
    height: 40px;
    order: 3;
    padding: 4px;
    width: 40px;

    .esri-button-menu {
      bottom: auto;
      position: relative;
    }
  }

  .esri-feature-table__header {
    display: flex;
    flex-direction: row;
    font-weight: $font-weight;
    height: 40px;
    line-height: 40px;
    margin: 0;
    width: 100%;
  }

  .esri-feature-table__title {
    flex-grow: 1;
    order: 2;
  }

  .esri-feature-table__loader-container {
    height: 40px;
    margin: 0 8px;
    order: 1;
    width: 32px;
  }

  .esri-feature-table__loader {
    height: 40px;
    width: 32px;
    background: url(../base/images/loading-throb.gif) no-repeat center;
  }

  .esri-field-column__header-label {
    align-items: center;
    display: flex;
    flex-grow: 1;
    margin: 0 5px;
    min-height: 28px;
    overflow: auto;
  }

  .esri-field-column__header-content {
    align-items: center;
    display: flex;
    font-weight: $font-weight;
    flex-grow: 1;
    overflow: visible;
  }

  .esri-field-column__cell-content {
    font-size: 0.9em;
  }

  .esri-field-column__cell__date-input-container {
    display: flex;
    height: 100%;
    width: 100%;

    .esri-field-column__cell__date-input-wrapper {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }

    .esri-date-picker {
      flex: 1 1 0;

      .esri-date-picker__text-input.esri-input {
        width: 100%;
      }

      .esri-date-picker__calendar-toggle {
        height: 26px;
      }
    }

    .esri-time-picker {
      display: inline-flex;
      flex: 1 1 0;

      input {
        width: 100%;
      }
    }
  }

  .esri-field-column__button {
    align-items: center;
    background-color: transparent;
    border: none;
    color: $button-color;
    display: flex;
    font-size: $icon-size;
    height: auto;
    justify-content: center;
    text-align: center;
    transition: background-color 125ms ease-in-out;
    width: 26px;

    &:disabled {
      cursor: default;
      color: $interactive-font-color--disabled;
    }

    &:hover,
    &:focus {
      background-color: $background-color--hover;
      color: $interactive-font-color--hover;
      cursor: pointer;
    }
  }

  .esri-field-column__cell__input-container {
    display: flex;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    position: absolute;
  }

  .esri-field-column__cell-input {
    height: 100%;
    width: 100%;
    border: none;
    padding: 0 5px;
    flex: 1 1 0;
  }

  .esri-feature-table__prompt--info {
    @include esri-feature-table__prompt(var(--calcite-ui-info));
  }

  .esri-feature-table__prompt--warning {
    @include esri-feature-table__prompt(var(--calcite-ui-warning));
  }

  .esri-feature-table__prompt--danger {
    @include esri-feature-table__prompt(var(--calcite-ui-danger));
  }

  [dir="rtl"] {
    .esri-feature-table__menu-content {
      left: 2px;
      right: auto;
    }
  }
}

@if $include_FeatureTable == true {
  @include featureTable();
}
