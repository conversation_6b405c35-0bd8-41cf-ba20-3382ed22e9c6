import{z as a}from"./index-r0dFAfgr.js";const o=t=>a({url:"/api/spp/dma/partitionEvaluate/normalList",method:"get",params:t}),s=t=>a({url:"/api/spp/dma/partitionEvaluate/dayFlowAnalysis",method:"get",params:t}),e=t=>a({url:"/api/spp/dma/partitionEvaluate/notNormalList",method:"get",params:t}),i=t=>a({url:"/api/spp/dma/partitionEvaluate/normalListDetail",method:"get",params:t}),p=t=>a({url:"/api/spp/dma/partitionEvaluate/totalDifference",method:"get",params:t}),n=t=>a({url:"/api/spp/dma/partitionEvaluate/totalDifferenceExport",method:"get",params:t,responseType:"blob"}),l=t=>a({url:"/api/spp/dma/partition/changeStatus",method:"post",data:t}),d=t=>a({url:"/api/spp/dma/workOrder/list",method:"get",params:t}),u=t=>a({url:"/api/spp/dma/workOrder",method:"post",data:t});export{l as E,d as G,u as P,o as a,i as b,s as c,e as d,n as e,p as f};
