package org.thingsboard.server.dao.model.sql.statistic;

import org.joda.time.DateTime;
import org.thingsboard.server.dao.util.TimeUtils;

import java.util.Date;
import java.util.Iterator;

public enum StatisticTimeUnit {
    HOUR {
        @Override
        public Iterator<StatisticDateEntry> getIterator(Date from, Date to) {
            return new TimeIterator<StatisticDateEntry>(from, to) {

                @Override
                public StatisticDateEntry next() {
                    DateTime now = new DateTime(this.now);
                    setNow(now.plusHours(1).toDate());
                    return new StatisticDateEntry(now.toDate(), this.now);
                }
            };
        }

        @Override
        protected Date getDefaultFromDate() {
            return new DateTime().withMillisOfDay(0).toDate();
        }

        @Override
        protected Date getDefaultToDate() {
            return new Date();
        }
    },
    DAY {
        @Override
        public Iterator<StatisticDateEntry> getIterator(Date from, Date to) {
            Date fromDate = TimeUtils.computeDefaultIfNull(from, this::getDefaultFromDate);
            Date toDate = TimeUtils.computeDefaultIfNull(to, this::getDefaultToDate);
            return new TimeIterator<StatisticDateEntry>(from, to) {

                @Override
                public StatisticDateEntry next() {
                    DateTime now = new DateTime(this.now);
                    setNow(now.plusDays(1).toDate());
                    return new StatisticDateEntry(now.toDate(), this.now);
                }
            };
        }

        @Override
        protected Date getDefaultFromDate() {
            return new DateTime().dayOfWeek().withMinimumValue().withMillisOfDay(0).toDate();
        }

        @Override
        protected Date getDefaultToDate() {
            return new Date();
        }
    },
    MONTH {
        @Override
        public Iterator<StatisticDateEntry> getIterator(Date from, Date to) {
            Date fromDate = TimeUtils.computeDefaultIfNull(from, this::getDefaultFromDate);
            Date toDate = TimeUtils.computeDefaultIfNull(to, this::getDefaultToDate);
            return new TimeIterator<StatisticDateEntry>(from, to) {

                @Override
                public StatisticDateEntry next() {
                    DateTime now = new DateTime(this.now);
                    setNow(now.plusMonths(1).toDate());
                    return new StatisticDateEntry(now.toDate(), this.now);
                }
            };
        }

        @Override
        protected Date getDefaultFromDate() {
            return new DateTime().dayOfMonth().withMinimumValue().withMillisOfDay(0).toDate();
        }

        @Override
        protected Date getDefaultToDate() {
            return new Date();
        }
    },
    YEAR {
        @Override
        public Iterator<StatisticDateEntry> getIterator(Date from, Date to) {
            Date fromDate = TimeUtils.computeDefaultIfNull(from, this::getDefaultFromDate);
            Date toDate = TimeUtils.computeDefaultIfNull(to, this::getDefaultToDate);
            return new TimeIterator<StatisticDateEntry>(from, to) {

                @Override
                public StatisticDateEntry next() {
                    DateTime now = new DateTime(this.now);
                    setNow(now.plusYears(1).toDate());
                    return new StatisticDateEntry(now.toDate(), this.now);
                }
            };
        }

        @Override
        protected Date getDefaultFromDate() {
            return new DateTime().dayOfYear().withMinimumValue().withMillisOfDay(0).toDate();
        }

        @Override
        protected Date getDefaultToDate() {
            return new Date();
        }
    },
    ;

    public abstract Iterator<StatisticDateEntry> getIterator(Date from, Date to);

    /**
     * 默认不传入fromDate时的默认值
     *
     * @return 默认的fromDate
     */
    protected abstract Date getDefaultFromDate();

    /**
     * 默认不传入toDate时的默认值
     *
     * @return 默认的toDate
     */

    protected abstract Date getDefaultToDate();
}
