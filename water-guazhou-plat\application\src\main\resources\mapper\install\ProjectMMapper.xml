<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.install.ProjectMMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.install.ProjectM">
        select a.*, b.name as typeName, c.first_name as receiverName
        from tb_install_project_m a
            left join tb_install_process_type b on a.type = b.id
            left join tb_user c on a.receiver = c.id
        where
          a.address like '%' || #{address} || '%'
          and a.type like '%' || #{type} || '%'
          and a.status like '%' || #{status} || '%'
          <choose>
              <when test="currentStep == ''">
                  and (a.current_step like '%%' or  a.current_step is null)
              </when>
              <otherwise>
                  and a.current_step like '%' || #{currentStep} || '%'
              </otherwise>
          </choose>
          and a.tenant_id = #{tenantId}
        order by a.code desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_install_project_m a
        where
            a.address like '%' || #{address} || '%'
            and a.type like '%' || #{type} || '%'
            and a.status like '%' || #{status} || '%'
        <choose>
            <when test="currentStep == ''">
                and (a.current_step like '%%' or  a.current_step is null)
            </when>
            <otherwise>
                and a.current_step like '%' || #{currentStep} || '%'
            </otherwise>
        </choose>
          and a.tenant_id = #{tenantId}
    </select>
    <select id="getManageList" resultType="org.thingsboard.server.dao.model.sql.install.ProjectM">
        select a.*, b.name as typeName, d.first_name as receiverName
        from tb_install_project_m a
            left join tb_install_process_type b on a.type = b.id
            left join tb_install_project_c c on a.current_step_id = c.id
            left join tb_user d on a.receiver = d.id
        where
        a.code like '%' || #{code} || '%'
        and a.address like '%' || #{address} || '%'
        and a.type like '%' || #{type} || '%'
        and a.status like '%' || #{status} || '%'
        and( c.users is not null and c.users like '%' || #{userId} || '%')
        <choose>
            <when test="currentStep == ''">
                and (a.current_step like '%%' or  a.current_step is null)
            </when>
            <otherwise>
                and a.current_step like '%' || #{currentStep} || '%'
            </otherwise>
        </choose>
        and a.tenant_id = #{tenantId}
        order by a.code desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>
    <select id="getManageListCount" resultType="java.lang.Integer">
        select count(*)
        from tb_install_project_m a
        left join tb_install_process_type b on a.type = b.id
        left join tb_install_project_c c on a.current_step_id = c.id
        where
        a.code like '%' || #{code} || '%'
        and a.address like '%' || #{address} || '%'
        and a.type like '%' || #{type} || '%'
        and a.status like '%' || #{status} || '%'
        and( c.users is not null and c.users like '%' || #{userId} || '%')
        <choose>
            <when test="currentStep == ''">
                and (a.current_step like '%%' or  a.current_step is null)
            </when>
            <otherwise>
                and a.current_step like '%' || #{currentStep} || '%'
            </otherwise>
        </choose>
        and a.tenant_id = #{tenantId}
    </select>

</mapper>