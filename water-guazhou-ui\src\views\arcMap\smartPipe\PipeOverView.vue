<template>
  <DrawerBox
    ref="refDrawerBox"
    :left-drawer="true"
    :left-drawer-bar-position="'top'"
    :left-drawer-absolute="false"
    :left-drawer-width="700"
    :theme="useAppStore().isDark ? 'darkblue' : 'light'"
    :class="useAppStore().isDark ? 'darkblue' : ''"
  >
    <ArcLayout
      ref="refArcLayout"
      @map-loaded="onMapLoaded"
      @pipe-loaded="handlePipeLoaded"
      @alarm-click="closeAllPop"
    >
      <template #map-bars>
        <div v-if="state.windows?.length" class="infowindow-container">
          <ListWindow
            v-for="(pop, j) in state.windows"
            :key="j"
            :ref="'refPop' + pop.attributes?.id"
            :view="staticState.view"
            :config="pop"
            @highlight="highlightPop"
          ></ListWindow>
        </div>
      </template>
    </ArcLayout>
    <template #left>
      <div class="left-wrapper">
        <div class="left-header">
          <span>管网总览</span>
          <el-button :type="'danger'" :text="true" @click="removeSearch">
            清除查询
          </el-button>
        </div>
        <div class="overview-wrapper">
          <div class="row1">
            <div class="left">
              <FieldSet :type="'simple'"> 管网数量统计 </FieldSet>
              <div class="table-box">
                <AttrTable
                  class="attr-table"
                  :attributes="state.Attrs"
                  :link="true"
                  @row-click="(row) => handleRowClick(row)"
                ></AttrTable>
              </div>
            </div>
            <div class="right">
              <FieldSet :type="'simple'"> 按管径统计 </FieldSet>
              <div class="chart-box">
                <VChart
                  :option="state.diameterOption"
                  @click="
                    (params) => {
                      handleChartClick(
                        'diameter',
                        params.data,
                        params.data.name &&
                          ' DIAMETER=\'' + params.data.name + '\''
                      );
                    }
                  "
                ></VChart>
              </div>
            </div>
          </div>
          <div class="row2">
            <div class="left">
              <FieldSet :type="'simple'"> 管点类型统计 </FieldSet>
              <div class="chart-box">
                <VChart
                  :option="state.typeOption"
                  @click="
                    (params) => {
                      handleTypeChartClick(params.data);
                    }
                  "
                ></VChart>
              </div>
            </div>
            <div class="right">
              <FieldSet :type="'simple'"> 按材质统计 </FieldSet>
              <div class="chart-box">
                <VChart
                  :option="state.materialsOption"
                  @click="
                    (params) => {
                      handleChartClick(
                        'material',
                        params.data,
                        params.data.name &&
                          ' MATERIAL=\'' + params.data.name + '\''
                      );
                    }
                  "
                ></VChart>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { useAppStore, useGisStore } from '@/store';
import {
  EStatisticField,
  excuteIdentify,
  excuteIdentifyAllLayersByGeoserver,
  excuteQuery,
  getGeometryCenter,
  getGraphicLayer,
  getSubLayerIds,
  gotoAndHighLight,
  initIdentifyParams,
  initQueryParams,
  setMapCursor,
  setSymbol,
  staticPipe
} from '@/utils/MapHelper';

import { GetFieldUniqueValue } from '@/api/mapservice/fieldconfig';
import { usePipeLineGroup } from '@/hooks/arcgis/usePipelineGroup';
import { oneHistogram } from '../components/components/chart';
import ListWindow from '../components/popup/ListWindow.vue';
import backgroundJson from '@/plugins/echart/customed.json';
import { transNumberUnit } from '@/utils/GlobalHelper';
import { SLMessage } from '@/utils/Message';
import { groupLengthStatisticsByField } from '@/utils/geoserver/wfsUtils.js';

let wmsLayer;
const { proxy }: any = getCurrentInstance();
const refArcLayout =
  ref<
    InstanceType<
      (typeof import('@/components/arcMap/widgets/ArcLayout.vue'))['default']
    >
  >();
const refDrawerBox =
  ref<
    InstanceType<
      (typeof import('@/components/DrawerBox/DrawerBox.vue'))['default']
    >
  >();
const state = reactive<{
  Attrs: { label: string; value: string; id: string }[];
  diameterOption: any;
  materialsOption: any;
  typeOption: any;
  windows: IArcMarkerProps[];
}>({
  Attrs: [],
  diameterOption: null,
  materialsOption: null,
  typeOption: null,
  windows: []
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  // pickLayer?: __esri.GraphicsLayer
  queryFeatures: {
    typeFeatures: Record<string, __esri.Graphic[]>;
    diameterFeatures: Record<string, __esri.Graphic[]>;
    materialFeatures: Record<string, __esri.Graphic[]>;
  };
  mapClick?: any;
  identifyResults: any[];
} = {
  view: undefined,
  queryFeatures: {
    typeFeatures: {},
    diameterFeatures: {},
    materialFeatures: {}
  },
  identifyResults: []
};
const initAttrTable = async () => {
  // 查询数量
  try {
    const countRes = await staticPipe('count', {
      layerIds: useGisStore().gLayerIds
    });
    state.Attrs = countRes.map((item) => {
      return {
        value: (item.rows[0]?.[EStatisticField.OBJECTID] ?? 0) + ' 个',
        label: item.layername,
        id: item.layerid
      };
    });

    const pipeLayerIds =
      useGisStore()
        .gLayerInfos?.filter(
          (item) => item.geometrytype === 'esriGeometryPolyline'
        )
        .map((item) => item.layerid) || [];
    // 处理饼图
    const typeChartData = countRes
      .filter((item) => pipeLayerIds.indexOf(item.layerid) === -1)
      .map((item) => {
        return {
          value: item.rows[0]?.[EStatisticField.OBJECTID] ?? 0,
          name: item.layername,
          id: item.layerid
        };
      });
    refreshTypeOption(typeChartData);
    // 查询长度
    staticPipe('length', {
      layerIds: pipeLayerIds
    })
      .then((res) => {
        res.map((item) => {
          const attr = state.Attrs.find((o) => o.label === item.layername);
          attr &&
            (attr.value =
              (item.rows[0]?.[EStatisticField.ShapeLen] ?? 0) + ' 米');
        });
      })
      .catch((errer) => {
        console.log(errer);
      });
  } catch (error) {
    console.log(error);
  }
};
const refreshTypeOption = (data: any[]) => {
  const title = '总数';
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1;
  }, 0);
  const transedTotal = transNumberUnit(total);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return params.name + ': ' + Number(params.value).toFixed(0) + ' 个';
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 60,
            fontSize: 12,
            color: useAppStore().isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 120,
            fontSize: 12,
            color: '#00ff00'
          }
        }
      },
      data: data.map((item) => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| ' +
                name +
                '}' +
                '{value| ' +
                data[i].value +
                ' 个' +
                '}'
              );
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|' +
          title +
          '(' +
          transedTotal.unit +
          '个)}\n' +
          '{value|' +
          transedTotal.value.toFixed(0) +
          '}',
        top: 'center',
        left: '24%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            },
            value: {
              fontSize: 16,
              fontWeight: 'bold',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    color: backgroundJson.color,
    series: [
      {
        type: 'pie',
        radius: ['40%', '55%'],
        center: ['25%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          formatter: (params) => {
            return (
              '{icon|●}{name|' +
              params.name +
              '}{value|' +
              (params.value || '0') +
              '}'
            );
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  };
  state.typeOption = option;
};
const refreshDiameterChart = async () => {
  try {
    // GeoServer实现：按管径分组统计管长
    const layerName = '管线'; // TODO: 替换为实际GeoServer图层名
    const res = await groupLengthStatisticsByField(layerName, '管径', '管长');
    const chartData = res.map((item) => {
      return {
        name: item.name?.toString() || '未知',
        nameAlias: 'DN' + (item.name || '未知'),
        value: ((item.value || 0) / 1000).toFixed(2),
        valueAlias: ((item.value || 0) / 1000).toFixed(2)
      };
    });
    state.diameterOption = oneHistogram(
      chartData,
      '管长(m)',
      undefined,
      '长度'
    );
  } catch (error) {
    console.log(error);
  }
};
const refreshMaterialsChart = async () => {
  try {
    // GeoServer实现：按材质分组统计管长
    const layerName = '管线'; // TODO: 替换为实际GeoServer图层名
    const res = await groupLengthStatisticsByField(layerName, '管材', '管长');
    const chartData = res.map((item) => {
      return {
        name: item.name || '未知',
        value: ((item.value || 0) / 1000).toFixed(2),
        valueAlias: ((item.value || 0) / 1000).toFixed(2)
      };
    });
    state.materialsOption = oneHistogram(
      chartData,
      '管长(m)',
      [
        {
          offset: 0,
          color: '#547cff'
        },
        {
          offset: 1,
          color: '#424971'
        }
      ],
      '长度'
    );
  } catch (error) {
    console.log(error);
  }
};
const handleRowClick = async (row) => {
  if (!row) return;
  await handleTypeChartClick({
    ...row,
    value: row.value,
    name: row.label,
    id: row.id
  });
};
const getLayerColor = (layerId: number) => {
  const pipeLayerIds = useGisStore()
    .gLayerInfos?.filter((item) => item.geometrytype === 'esriGeometryPoint')
    .map((item) => item.layerid);
  const colorIndex = pipeLayerIds?.findIndex((item) => item === layerId) ?? -1;
  const itemColor = backgroundJson.color[colorIndex ?? 0];
  return itemColor;
};
const handleTypeChartClick = (data) => {
  const id = data?.id ?? undefined;
  if (!staticState.view || id === undefined) return;
  const layer = getGraphicLayer(staticState.view, {
    id: 'query-pipe-result',
    title: '管线查询结果'
  });
  if (staticState.queryFeatures.typeFeatures[id]?.length) {
    layer?.removeMany(staticState.queryFeatures.typeFeatures[id]);
    staticState.queryFeatures.typeFeatures[id] = [];
    return;
  }
  excuteQuery(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
      '/' +
      id,
    initQueryParams({
      returnGeometry: true,
      where: '1=1'
    })
  )
    .then((res) => {
      const itemColor = getLayerColor(id);
      res.features.map(
        (item) =>
          (item.symbol = setSymbol(item.geometry.type, { color: itemColor }))
      );
      layer?.addMany(res.features);
      staticState.queryFeatures.typeFeatures[id] = res.features;
      SLMessage.success('查询成功！');
    })
    .catch(() => {
      SLMessage.error('查询失败');
    });
};
const removeSearch = () => {
  const layer = getGraphicLayer(staticState.view, {
    id: 'query-pipe-result',
    title: '管线查询结果'
  });
  layer?.removeAll();
  staticState.queryFeatures.diameterFeatures = {};
  staticState.queryFeatures.materialFeatures = {};
  staticState.queryFeatures.typeFeatures = {};
};
const handleChartClick = async (
  from: 'diameter' | 'material',
  data: any,
  sql?: string,
  layerId?: number
) => {
  const name = data.name;
  if (!staticState.view || !name) return;
  layerId =
    layerId === undefined
      ? useGisStore().gLayerInfos?.find(
          (item) => item.geometrytype === 'esriGeometryPolyline'
        )?.layerid
      : layerId;
  if (layerId === undefined) return;
  const layer = getGraphicLayer(staticState.view, {
    id: 'query-pipe-result',
    title: '管线查询结果'
  });
  if (from === 'diameter') {
    if (staticState.queryFeatures.diameterFeatures[name]?.length) {
      layer?.removeMany(staticState.queryFeatures.diameterFeatures[name]);
      staticState.queryFeatures.diameterFeatures[name] = [];
      return;
    }
  } else if (from === 'material') {
    if (staticState.queryFeatures.materialFeatures[name]?.length) {
      layer?.removeMany(staticState.queryFeatures.materialFeatures[name]);
      staticState.queryFeatures.materialFeatures[name] = [];
      return;
    }
  }
  excuteQuery(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
      '/' +
      layerId,
    initQueryParams({
      returnGeometry: true,
      where: sql || ''
    })
  )
    .then((res) => {
      const layer = getGraphicLayer(staticState.view, {
        id: 'query-pipe-result',
        title: '管线查询结果'
      });
      const itemColor = getLayerColor(layerId ?? -1);
      res.features.map(
        (item) =>
          (item.symbol = setSymbol(item.geometry.type, { color: itemColor }))
      );
      layer?.addMany(res.features);
      if (from === 'diameter') {
        staticState.queryFeatures.diameterFeatures[name] = res.features;
      } else if (from === 'material') {
        staticState.queryFeatures.materialFeatures[name] = res.features;
      }
      SLMessage.success('查询成功！');
    })
    .catch(() => {
      SLMessage.error('查询失败');
    });
};
const refreshData = async () => {
  refreshDiameterChart();
  refreshMaterialsChart();
  initAttrTable();
};

const pickPipe = () => {
  if (!staticState.view) return;
  setMapCursor('crosshair');
  // staticState.pickLayer = getGraphicLayer(staticState.view, {
  //   id: 'search-pick',
  //   title: '查询结果'
  // })
  staticState.mapClick = staticState.view?.on('click', async (e) => {
    await doIdentify(e);
  });
};

const doIdentify = async (e: any) => {
  if (!staticState.view) return;
  try {
    let res;
    state.windows = [];
    const pipelayerIds = getSubLayerIds(staticState.view, true);
    const queryParams = initIdentifyParams({
      layerIds: pipelayerIds,
      geometry: e.mapPoint,
      mapExtent: staticState.view.extent
    });
    if(GIS_SERVER_SWITCH){
      res = await excuteIdentifyAllLayersByGeoserver(staticState.view,wmsLayer,e);
      if (!res) {
        SLMessage.warning('没有相关数据');
        return;
      }
      res = res.data;
      state.windows.push({
        visible: true,
        // x: res.features[0].geometry.coordinates[0][0][0],
        // y: res.features[0].geometry.coordinates[0][0][1],
        longitude: res.features[0].geometry.coordinates[0][0][0],
        latitude: res.features[0].geometry.coordinates[0][0][1],
        title: res.features[0].id,
        attributes: {
          row: res.features[0].properties,
          id: res.features[0].properties.OBJECTID
        }
      });
      openPop(res.features[0].properties.OBJECTID);
    }else{
      res = await excuteIdentify(
        window.SITE_CONFIG.GIS_CONFIG.gisService +
          window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
        queryParams
      );
      console.log('arcgis_result:',res);
  
      // staticState.pickLayer?.removeAll()
      staticState.identifyResults = res.results || [];
      staticState.identifyResults.length > 50 &&
        (staticState.identifyResults.length = 50);
      const tableData: any[] = [];
      const features: __esri.Graphic[] = [];
      staticState.identifyResults.map((item) => {
        tableData.push({
          layerName: item.layerName,
          layerId: item.layerId,
          ...item.feature.attributes
        });
        item.feature.symbol = setSymbol(item.feature.geometry.type);
        features.push(item.feature);
        const center = getGeometryCenter(item.feature.geometry) || [];
        state.windows.push({
          visible: false,
          x: center[0],
          y: center[1],
          title: item.layerName + '(' + item.feature.attributes['新编号'] + ')',
          attributes: {
            row: item.feature.attributes,
            id: item.feature.attributes.OBJECTID
          }
        });
      });
      await gotoAndHighLight(
        staticState.view,
        staticState.identifyResults[0].feature
      );
      const id = staticState.identifyResults[0]?.feature.attributes.OBJECTID;
      openPop(id);
    }
  } catch (error) {
    console.error(error);
    staticState.view?.graphics.removeAll();
  }
};
const highlightPop = (pop: IArcMarkerProps) => {
  pop.highLight = true;
};

const openPop = (id?: string) => {
  closeAllPop();
  refArcLayout.value?.toggleAlarmPop(false);
  if (id === undefined) return;
  const pop = proxy.$refs['refPop' + id];
  if (!pop?.[0]) return;
  pop[0]?.toggle(true);
  pop[0]?.setPosition(staticState.view);
};
const closeAllPop = () => {
  state.windows?.map((item) => {
    const id = item.attributes?.id;
    if (!id) return;
    const pop = proxy.$refs['refPop' + id];
    pop?.length && pop[0]?.toggle(false);
  });
};
const group = usePipeLineGroup('管线材质分组');
const group1 = usePipeLineGroup('管线口径分组');
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view;

  pickPipe();
};
const handlePipeLoaded = async (layer) => {
  wmsLayer = layer;
  const layerId = useGisStore().gLayerInfos?.find(
    (item) => item.geometrytype === 'esriGeometryPolyline'
  )?.layerid;
  GetFieldUniqueValue({
    layerid: layerId,
    field_name: 'MATERIAL'
  })
    .then((res) => {
      group.init(staticState.view);
      const material = res.data?.result?.rows || [];
      material.map((item, i) => {
        const sql = group.genSql('MATERIAL', item);
        const color = group.genColor(i);
        group.addSubLayer(staticState.view, layerId, item, sql, color);
      });
    })
    .catch((error) => {
      console.log(error);
    });
    GetFieldUniqueValue({
      layerid: layerId,
      field_name: 'DIAMETER'
    })
    .then((res) => {
      group1.init(staticState.view);
      const material = res.data?.result?.rows || [];
      material.map((item, i) => {
        const sql = group1.genSql('DIAMETER', item);
        const color = group1.genColor(i);
        group1.addSubLayer(staticState.view, layerId, 'DN' + item, sql, color);
      });
    })
    .catch((error) => {
      console.log(error);
    });
  refreshData();
};
onMounted(async () => {
  refDrawerBox.value?.toggleDrawer('ltr', true);
});
onBeforeUnmount(() => {
  staticState.mapClick?.remove();
});
</script>
<style lang="scss" scoped>
.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;

  --ss-color: rgb(125, 0, 255);
  --ss-color: rgb(125, 125, 255);
  --ss-color: rgb(125, 255, 0);
  --ss-color: rgb(125, 255, 125);
  --ss-color: rgb(125, 255, 255);
  --ss-color: rgb(255, 0, 125);
  --ss-color: rgb(255, 0, 255);
  --ss-color: rgb(255, 125, 0);
  --ss-color: rgb(255, 125, 125);
  --ss-color: rgb(255, 125, 255);
  --ss-color: rgb(125, 0, 0);
  --ss-color: rgb(125, 0, 125);
  --ss-color: rgb(125, 125, 0);
  :deep(.esri-ui-top-left) {
    flex-flow: row;
  }

  :deep(.esri-ui-bottom-right) {
    flex-flow: column;
  }
}

.custom-menubar,
.custom-horizontal-menu,
.custom-submenubar {
  width: auto;
  height: auto;
  border-radius: 4px;
  background-color: transparent;
  margin: 0;
  margin-right: 12px;
}

:deep(.esri-ui-bottom-right) {
  &.esri-widget--button,
  .esri-widget--button {
    border-top: solid 1px rgba(173, 173, 173, 0.3);
  }
}
.left-wrapper {
  height: 100%;
  width: 100%;
  .left-header {
    background-color: var(--el-fill-color-light);
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
  }
}
.overview-wrapper {
  height: calc(100% - 40px);
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  padding: 15px;
  .left,
  .right {
    width: 50%;
  }
  .row1,
  .row2 {
    width: 100%;
    display: flex;
  }
  .table-box {
    height: 300px;
    width: 250px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .right {
    padding: 0 8px;
    flex: 1;
  }
  .chart-box {
    height: 300px;
    width: 100%;
  }
}
</style>
