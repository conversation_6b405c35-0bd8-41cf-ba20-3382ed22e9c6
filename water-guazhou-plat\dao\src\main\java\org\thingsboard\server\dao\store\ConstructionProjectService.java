package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.ConstructionProject;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectSaveRequest;

public interface ConstructionProjectService {
    /**
     * 分页条件查询施工项目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<ConstructionProject> findAllConditional(ConstructionProjectPageRequest request);

    /**
     * 保存施工项目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    ConstructionProject save(ConstructionProjectSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(ConstructionProject entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
