"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3594],{5732:(t,e,i)=>{i.d(e,{c:()=>s});var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},69801:(t,e,i)=>{i.d(e,{WJ:()=>h,Xq:()=>a});var s,n,r=i(70586),o=i(44553);(n=s||(s={}))[n.ALL=0]="ALL",n[n.SOME=1]="SOME";class a{constructor(t,e,i){this._namespace=t,this._storage=e,this._removeFunc=!1,this._hit=0,this._miss=0,this._storage.register(this),this._namespace+=":",i&&(this._storage.registerRemoveFunc(this._namespace,i),this._removeFunc=!0)}destroy(){this._storage.clear(this._namespace),this._removeFunc&&this._storage.deregisterRemoveFunc(this._namespace),this._storage.deregister(this),this._storage=null}get namespace(){return this._namespace.slice(0,-1)}get hitRate(){return this._hit/(this._hit+this._miss)}get size(){return this._storage.size}get maxSize(){return this._storage.maxSize}resetHitRate(){this._hit=this._miss=0}put(t,e,i,s=0){this._storage.put(this._namespace+t,e,i,s)}get(t){const e=this._storage.get(this._namespace+t);return void 0===e?++this._miss:++this._hit,e}pop(t){const e=this._storage.pop(this._namespace+t);return void 0===e?++this._miss:++this._hit,e}updateSize(t,e,i){this._storage.updateSize(this._namespace+t,e,i)}clear(){this._storage.clear(this._namespace)}clearAll(){this._storage.clearAll()}getStats(){return this._storage.getStats()}resetStats(){this._storage.resetStats()}}class h{constructor(t=10485760){this._maxSize=t,this._db=new Map,this._size=0,this._hit=0,this._miss=0,this._removeFuncs=new o.Z,this._users=new o.Z}destroy(){this.clearAll(),this._removeFuncs.clear(),this._users.clear(),this._db=null}register(t){this._users.push(t)}deregister(t){this._users.removeUnordered(t)}registerRemoveFunc(t,e){this._removeFuncs.push([t,e])}deregisterRemoveFunc(t){this._removeFuncs.filterInPlace((e=>e[0]!==t))}get size(){return this._size}get maxSize(){return this._maxSize}set maxSize(t){this._maxSize=Math.max(t,0),this._checkSizeLimit()}put(t,e,i,n){const r=this._db.get(t);if(r&&(this._size-=r.size,this._db.delete(t),r.entry!==e&&this._notifyRemove(t,r.entry,s.ALL)),i>this._maxSize)return void this._notifyRemove(t,e,s.ALL);if(void 0===e)return void console.warn("Refusing to cache undefined entry ");if(!i||i<0)return void console.warn("Refusing to cache entry with invalid size "+i);const o=1+Math.max(n,-3)- -3;this._db.set(t,{entry:e,size:i,lifetime:o,lives:o}),this._size+=i,this._checkSizeLimit()}updateSize(t,e,i){const n=this._db.get(t);if(n&&n.entry===e){for(this._size-=n.size;i>this._maxSize;){const n=this._notifyRemove(t,e,s.SOME);if(!((0,r.pC)(n)&&n>0))return void this._db.delete(t);i=n}n.size=i,this._size+=i,this._checkSizeLimit()}}pop(t){const e=this._db.get(t);if(e)return this._size-=e.size,this._db.delete(t),++this._hit,e.entry;++this._miss}get(t){const e=this._db.get(t);if(void 0!==e)return this._db.delete(t),e.lives=e.lifetime,this._db.set(t,e),++this._hit,e.entry;++this._miss}getStats(){const t={Size:Math.round(this._size/1048576)+"/"+Math.round(this._maxSize/1048576)+"MB","Hit rate":Math.round(100*this._getHitRate())+"%",Entries:this._db.size.toString()},e={},i=new Array;this._db.forEach(((t,s)=>{const n=t.lifetime;i[n]=(i[n]||0)+t.size,this._users.forAll((i=>{const n=i.namespace;if(s.startsWith(n)){const i=e[n]||0;e[n]=i+t.size}}))}));const s={};this._users.forAll((t=>{const i=t.namespace;if(!isNaN(t.hitRate)&&t.hitRate>0){const n=e[i]||0;e[i]=n,s[i]=Math.round(100*t.hitRate)+"%"}else s[i]="0%"}));const n=Object.keys(e);n.sort(((t,i)=>e[i]-e[t])),n.forEach((i=>t[i]=Math.round(e[i]/2**20)+"MB / "+s[i]));for(let e=i.length-1;e>=0;--e){const s=i[e];s&&(t["Priority "+(e+-3-1)]=Math.round(s/this.size*100)+"%")}return t}resetStats(){this._hit=this._miss=0,this._users.forAll((t=>t.resetHitRate()))}clear(t){this._db.forEach(((e,i)=>{i.startsWith(t)&&(this._size-=e.size,this._db.delete(i),this._notifyRemove(i,e.entry,s.ALL))}))}clearAll(){this._db.forEach(((t,e)=>this._notifyRemove(e,t.entry,s.ALL))),this._size=0,this._db.clear()}_getHitRate(){return this._hit/(this._hit+this._miss)}_notifyRemove(t,e,i){let s;return this._removeFuncs.some((n=>{if(t.startsWith(n[0])){const t=n[1](e,i);return"number"==typeof t&&(s=t),!0}return!1})),s}_checkSizeLimit(){if(!(this._size<=this._maxSize))for(const[t,e]of this._db){if(this._db.delete(t),e.lives<=1){this._size-=e.size;const i=this._notifyRemove(t,e.entry,s.SOME);(0,r.pC)(i)&&i>0&&(this._size+=i,e.lives=e.lifetime,e.size=i,this._db.set(t,e))}else--e.lives,this._db.set(t,e);if(this._size<=.9*this.maxSize)return}}}},24133:(t,e,i)=>{i.d(e,{Q:()=>a});var s=i(67676),n=i(70586),r=i(44553),o=i(88764);class a{constructor(t=9,e){this._compareMinX=u,this._compareMinY=d,this._toBBox=t=>t,this._maxEntries=Math.max(4,t||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),e&&("function"==typeof e?this._toBBox=e:this._initFormat(e)),this.clear()}destroy(){this.clear(),b.prune(),v.prune(),B.prune(),I.prune()}all(t){this._all(this._data,t)}search(t,e){let i=this._data;const s=this._toBBox;if(y(t,i))for(b.clear();i;){for(let n=0,r=i.children.length;n<r;n++){const r=i.children[n],o=i.leaf?s(r):r;y(t,o)&&(i.leaf?e(r):p(t,o)?this._all(r,e):b.push(r))}i=b.pop()}}collides(t){let e=this._data;const i=this._toBBox;if(!y(t,e))return!1;for(b.clear();e;){for(let s=0,n=e.children.length;s<n;s++){const n=e.children[s],r=e.leaf?i(n):n;if(y(t,r)){if(e.leaf||p(t,r))return!0;b.push(n)}}e=b.pop()}return!1}load(t){if(!t.length)return this;if(t.length<this._minEntries){for(let e=0,i=t.length;e<i;e++)this.insert(t[e]);return this}let e=this._build(t.slice(0,t.length),0,t.length-1,0);if(this._data.children.length)if(this._data.height===e.height)this._splitRoot(this._data,e);else{if(this._data.height<e.height){const t=this._data;this._data=e,e=t}this._insert(e,this._data.height-e.height-1,!0)}else this._data=e;return this}insert(t){return t&&this._insert(t,this._data.height-1),this}clear(){return this._data=new w([]),this}remove(t){if(!t)return this;let e,i=this._data,r=null,o=0,a=!1;const h=this._toBBox(t);for(B.clear(),I.clear();i||B.length>0;){if(i||(i=(0,n.j0)(B.pop()),r=B.data[B.length-1],o=I.pop()??0,a=!0),i.leaf&&(e=(0,s.cq)(i.children,t,i.children.length,i.indexHint),-1!==e))return i.children.splice(e,1),B.push(i),this._condense(B),this;a||i.leaf||!p(i,h)?r?(o++,i=r.children[o],a=!1):i=null:(B.push(i),I.push(o),o=0,r=i,i=i.children[0])}return this}toJSON(){return this._data}fromJSON(t){return this._data=t,this}_all(t,e){let i=t;for(v.clear();i;){if(!0===i.leaf)for(const t of i.children)e(t);else v.pushArray(i.children);i=v.pop()??null}}_build(t,e,i,s){const n=i-e+1;let r=this._maxEntries;if(n<=r){const s=new w(t.slice(e,i+1));return h(s,this._toBBox),s}s||(s=Math.ceil(Math.log(n)/Math.log(r)),r=Math.ceil(n/r**(s-1)));const o=new X([]);o.height=s;const a=Math.ceil(n/r),l=a*Math.ceil(Math.sqrt(r));x(t,e,i,l,this._compareMinX);for(let n=e;n<=i;n+=l){const e=Math.min(n+l-1,i);x(t,n,e,a,this._compareMinY);for(let i=n;i<=e;i+=a){const n=Math.min(i+a-1,e);o.children.push(this._build(t,i,n,s-1))}}return h(o,this._toBBox),o}_chooseSubtree(t,e,i,s){for(;s.push(e),!0!==e.leaf&&s.length-1!==i;){let i,s=1/0,n=1/0;for(let r=0,o=e.children.length;r<o;r++){const o=e.children[r],a=f(o),h=_(t,o)-a;h<n?(n=h,s=a<s?a:s,i=o):h===n&&a<s&&(s=a,i=o)}e=i||e.children[0]}return e}_insert(t,e,i){const s=this._toBBox,n=i?t:s(t);B.clear();const r=this._chooseSubtree(n,this._data,e,B);for(r.children.push(t),c(r,n);e>=0&&B.data[e].children.length>this._maxEntries;)this._split(B,e),e--;this._adjustParentBBoxes(n,B,e)}_split(t,e){const i=t.data[e],s=i.children.length,n=this._minEntries;this._chooseSplitAxis(i,n,s);const r=this._chooseSplitIndex(i,n,s);if(!r)return void console.log("  Error: assertion failed at PooledRBush._split: no valid split index");const o=i.children.splice(r,i.children.length-r),a=i.leaf?new w(o):new X(o);a.height=i.height,h(i,this._toBBox),h(a,this._toBBox),e?t.data[e-1].children.push(a):this._splitRoot(i,a)}_splitRoot(t,e){this._data=new X([t,e]),this._data.height=t.height+1,h(this._data,this._toBBox)}_chooseSplitIndex(t,e,i){let s,n,r;s=n=1/0;for(let o=e;o<=i-e;o++){const e=l(t,0,o,this._toBBox),a=l(t,o,i,this._toBBox),h=g(e,a),c=f(e)+f(a);h<s?(s=h,r=o,n=c<n?c:n):h===s&&c<n&&(n=c,r=o)}return r}_chooseSplitAxis(t,e,i){const s=t.leaf?this._compareMinX:u,n=t.leaf?this._compareMinY:d;this._allDistMargin(t,e,i,s)<this._allDistMargin(t,e,i,n)&&t.children.sort(s)}_allDistMargin(t,e,i,s){t.children.sort(s);const n=this._toBBox,r=l(t,0,e,n),o=l(t,i-e,i,n);let a=m(r)+m(o);for(let s=e;s<i-e;s++){const e=t.children[s];c(r,t.leaf?n(e):e),a+=m(r)}for(let s=i-e-1;s>=e;s--){const e=t.children[s];c(o,t.leaf?n(e):e),a+=m(o)}return a}_adjustParentBBoxes(t,e,i){for(let s=i;s>=0;s--)c(e.data[s],t)}_condense(t){for(let e=t.length-1;e>=0;e--){const i=t.data[e];if(0===i.children.length)if(e>0){const n=t.data[e-1],r=n.children;r.splice((0,s.cq)(r,i,r.length,n.indexHint),1)}else this.clear();else h(i,this._toBBox)}}_initFormat(t){const e=["return a"," - b",";"];this._compareMinX=new Function("a","b",e.join(t[0])),this._compareMinY=new Function("a","b",e.join(t[1])),this._toBBox=new Function("a","return {minX: a"+t[0]+", minY: a"+t[1]+", maxX: a"+t[2]+", maxY: a"+t[3]+"};")}}function h(t,e){l(t,0,t.children.length,e,t)}function l(t,e,i,s,n){n||(n=new w([])),n.minX=1/0,n.minY=1/0,n.maxX=-1/0,n.maxY=-1/0;for(let r,o=e;o<i;o++)r=t.children[o],c(n,t.leaf?s(r):r);return n}function c(t,e){t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY)}function u(t,e){return t.minX-e.minX}function d(t,e){return t.minY-e.minY}function f(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function m(t){return t.maxX-t.minX+(t.maxY-t.minY)}function _(t,e){return(Math.max(e.maxX,t.maxX)-Math.min(e.minX,t.minX))*(Math.max(e.maxY,t.maxY)-Math.min(e.minY,t.minY))}function g(t,e){const i=Math.max(t.minX,e.minX),s=Math.max(t.minY,e.minY),n=Math.min(t.maxX,e.maxX),r=Math.min(t.maxY,e.maxY);return Math.max(0,n-i)*Math.max(0,r-s)}function p(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function y(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function x(t,e,i,s,r){const a=[e,i];for(;a.length;){const e=(0,n.j0)(a.pop()),i=(0,n.j0)(a.pop());if(e-i<=s)continue;const h=i+Math.ceil((e-i)/s/2)*s;(0,o.q)(t,h,i,e,r),a.push(i,h,h,e)}}const b=new r.Z,v=new r.Z,B=new r.Z,I=new r.Z({deallocator:void 0});class S{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}}class M extends S{constructor(){super(...arguments),this.height=1,this.indexHint=new s.SO}}class w extends M{constructor(t){super(),this.children=t,this.leaf=!0}}class X extends M{constructor(t){super(),this.children=t,this.leaf=!1}}},17445:(t,e,i)=>{i.d(e,{N1:()=>d,YP:()=>h,Z_:()=>_,gx:()=>l,nn:()=>g,on:()=>u,tX:()=>p});var s=i(91460),n=i(50758),r=i(70586),o=i(95330),a=i(26258);function h(t,e,i={}){return c(t,e,i,f)}function l(t,e,i={}){return c(t,e,i,m)}function c(t,e,i={},s){let n=null;const o=i.once?(t,i)=>{s(t)&&((0,r.hw)(n),e(t,i))}:(t,i)=>{s(t)&&e(t,i)};if(n=(0,a.aQ)(t,o,i.sync,i.equals),i.initial){const e=t();o(e,e)}return n}function u(t,e,i,o={}){let a=null,l=null,c=null;function u(){a&&l&&(l.remove(),o.onListenerRemove?.(a),a=null,l=null)}function d(t){o.once&&o.once&&(0,r.hw)(c),i(t)}const f=h(t,((t,i)=>{u(),(0,s.vT)(t)&&(a=t,l=(0,s.on)(t,e,d),o.onListenerAdd?.(t))}),{sync:o.sync,initial:!0});return c=(0,n.kB)((()=>{f.remove(),u()})),c}function d(t,e){return function(t,e,i){if((0,o.Hc)(i))return Promise.reject((0,o.zE)());const s=t();if(e?.(s))return Promise.resolve(s);let a=null;function h(){a=(0,r.hw)(a)}return new Promise(((s,r)=>{a=(0,n.AL)([(0,o.fu)(i,(()=>{h(),r((0,o.zE)())})),c(t,(t=>{h(),s(t)}),{sync:!1,once:!0},e??f)])}))}(t,m,e)}function f(t){return!0}function m(t){return!!t}i(87538);const _={sync:!0},g={initial:!0},p={sync:!0,initial:!0}},16306:(t,e,i)=>{i.d(e,{aX:()=>I});var s=i(68773),n=i(20102),r=i(92604),o=i(70586),a=i(38913),h=i(58901),l=i(73913),c=i(8744),u=i(40488),d=(i(66577),i(3172)),f=i(33955),m=i(11282),_=i(17452);async function g(t,e,i){const s="string"==typeof t?(0,_.mN)(t):t,n=e[0].spatialReference,r=(0,f.Ji)(e[0]),o={...i,query:{...s.query,f:"json",sr:n.wkid?n.wkid:JSON.stringify(n),geometries:JSON.stringify((h=e,{geometryType:(0,f.Ji)(h[0]),geometries:h.map((t=>t.toJSON()))}))}},{data:a}=await(0,d.default)(s.path+"/simplify",o);var h;return function(t,e,i){const s=(0,f.q9)(e);return t.map((t=>{const e=s.fromJSON(t);return e.spatialReference=i,e}))}(a.geometries,r,n)}const p=r.Z.getLogger("esri.geometry.support.normalizeUtils");function y(t){return"polygon"===t[0].type}function x(t){return"polyline"===t[0].type}function b(t,e,i){if(e){const e=function(t,e){if(!(t instanceof h.Z||t instanceof a.Z)){const t="straightLineDensify: the input geometry is neither polyline nor polygon";throw p.error(t),new n.Z(t)}const i=(0,l.x3)(t),s=[];for(const t of i){const i=[];s.push(i),i.push([t[0][0],t[0][1]]);for(let s=0;s<t.length-1;s++){const n=t[s][0],r=t[s][1],o=t[s+1][0],a=t[s+1][1],h=Math.sqrt((o-n)*(o-n)+(a-r)*(a-r)),l=(a-r)/h,c=(o-n)/h,u=h/e;if(u>1){for(let t=1;t<=u-1;t++){const s=t*e,o=c*s+n,a=l*s+r;i.push([o,a])}const t=(h+Math.floor(u-1)*e)/2,s=c*t+n,o=l*t+r;i.push([s,o])}i.push([o,a])}}return function(t){return"polygon"===t.type}(t)?new a.Z({rings:s,spatialReference:t.spatialReference}):new h.Z({paths:s,spatialReference:t.spatialReference})}(t,1e6);t=(0,u.Sx)(e,!0)}return i&&(t=(0,l.Sy)(t,i)),t}function v(t,e,i){if(Array.isArray(t)){const s=t[0];if(s>e){const i=(0,l.XZ)(s,e);t[0]=s+i*(-2*e)}else if(s<i){const e=(0,l.XZ)(s,i);t[0]=s+e*(-2*i)}}else{const s=t.x;if(s>e){const i=(0,l.XZ)(s,e);t=t.clone().offset(i*(-2*e),0)}else if(s<i){const e=(0,l.XZ)(s,i);t=t.clone().offset(e*(-2*i),0)}}return t}function B(t,e){let i=-1;for(let s=0;s<e.cutIndexes.length;s++){const n=e.cutIndexes[s],r=e.geometries[s],o=(0,l.x3)(r);for(let t=0;t<o.length;t++){const e=o[t];e.some((i=>{if(i[0]<180)return!0;{let i=0;for(let t=0;t<e.length;t++){const s=e[t][0];i=s>i?s:i}i=Number(i.toFixed(9));const s=-360*(0,l.XZ)(i,180);for(let i=0;i<e.length;i++){const e=r.getPoint(t,i);r.setPoint(t,i,e.clone().offset(s,0))}return!0}}))}if(n===i){if(y(t))for(const e of(0,l.x3)(r))t[n]=t[n].addRing(e);else if(x(t))for(const e of(0,l.x3)(r))t[n]=t[n].addPath(e)}else i=n,t[n]=r}return t}async function I(t,e,i){if(!Array.isArray(t))return I([t],e);e&&"string"!=typeof e&&p.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const n="string"==typeof e?e:e?.url??s.Z.geometryServiceUrl;let r,_,y,x,S,M,w,X,z=0;const Y=[],Z=[];for(const e of t)if((0,o.Wi)(e))Z.push(e);else if(r||(r=e.spatialReference,_=(0,c.C5)(r),y=r.isWebMercator,M=y?102100:4326,x=l.UZ[M].maxX,S=l.UZ[M].minX,w=l.UZ[M].plus180Line,X=l.UZ[M].minus180Line),_)if("mesh"===e.type)Z.push(e);else if("point"===e.type)Z.push(v(e.clone(),x,S));else if("multipoint"===e.type){const t=e.clone();t.points=t.points.map((t=>v(t,x,S))),Z.push(t)}else if("extent"===e.type){const t=e.clone()._normalize(!1,!1,_);Z.push(t.rings?new a.Z(t):t)}else if(e.extent){const t=e.extent,i=(0,l.XZ)(t.xmin,S)*(2*x);let s=0===i?e.clone():(0,l.Sy)(e.clone(),i);t.offset(i,0),t.intersects(w)&&t.xmax!==x?(z=t.xmax>z?t.xmax:z,s=b(s,y),Y.push(s),Z.push("cut")):t.intersects(X)&&t.xmin!==S?(z=t.xmax*(2*x)>z?t.xmax*(2*x):z,s=b(s,y,360),Y.push(s),Z.push("cut")):Z.push(s)}else Z.push(e.clone());else Z.push(e);let R=(0,l.XZ)(z,x),F=-90;const L=R,A=new h.Z;for(;R>0;){const t=360*R-180;A.addPath([[t,F],[t,-1*F]]),F*=-1,R--}if(Y.length>0&&L>0){const e=B(Y,await async function(t,e,i,s){const n=(0,m.en)(t),r=e[0].spatialReference,o={...s,query:{...n.query,f:"json",sr:JSON.stringify(r),target:JSON.stringify({geometryType:(0,f.Ji)(e[0]),geometries:e}),cutter:JSON.stringify(i)}},a=await(0,d.default)(n.path+"/cut",o),{cutIndexes:h,geometries:l=[]}=a.data;return{cutIndexes:h,geometries:l.map((t=>{const e=(0,f.im)(t);return e.spatialReference=r,e}))}}(n,Y,A,i)),s=[],r=[];for(let i=0;i<Z.length;i++){const n=Z[i];if("cut"!==n)r.push(n);else{const n=e.shift(),a=t[i];(0,o.pC)(a)&&"polygon"===a.type&&a.rings&&a.rings.length>1&&n.rings.length>=a.rings.length?(s.push(n),r.push("simplify")):r.push(y?(0,u.$)(n):n)}}if(!s.length)return r;const a=await g(n,s,i),h=[];for(let t=0;t<r.length;t++){const e=r[t];"simplify"!==e?h.push(e):h.push(y?(0,u.$)(a.shift()):a.shift())}return h}const T=[];for(let t=0;t<Z.length;t++){const e=Z[t];if("cut"!==e)T.push(e);else{const t=Y.shift();T.push(!0===y?(0,u.$)(t):t)}}return T}},73913:(t,e,i)=>{i.d(e,{Sy:()=>h,UZ:()=>o,XZ:()=>a,x3:()=>l});var s=i(58901),n=i(82971),r=i(33955);const o={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new s.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:n.Z.WebMercator}),minus180Line:new s.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:n.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new s.Z({paths:[[[180,-180],[180,180]]],spatialReference:n.Z.WGS84}),minus180Line:new s.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:n.Z.WGS84})}};function a(t,e){return Math.ceil((t-e)/(2*e))}function h(t,e){const i=l(t);for(const t of i)for(const i of t)i[0]+=e;return t}function l(t){return(0,r.oU)(t)?t.rings:t.paths}},37549:(t,e,i)=>{i.d(e,{H:()=>a});var s=i(80442),n=i(24133),r=i(24470);const o={minX:0,minY:0,maxX:0,maxY:0};class a{constructor(){this._indexInvalid=!1,this._boundsToLoad=[],this._boundsById=new Map,this._idByBounds=new Map,this._index=new n.Q(9,(0,s.Z)("esri-csp-restrictions")?t=>({minX:t[0],minY:t[1],maxX:t[2],maxY:t[3]}):["[0]","[1]","[2]","[3]"]),this._loadIndex=()=>{if(this._indexInvalid){const t=new Array(this._idByBounds.size);let e=0;this._idByBounds.forEach(((i,s)=>{t[e++]=s})),this._indexInvalid=!1,this._index.clear(),this._index.load(t)}else this._boundsToLoad.length&&(this._index.load(Array.from(new Set(this._boundsToLoad.filter((t=>this._idByBounds.has(t)))))),this._boundsToLoad.length=0)}}get fullBounds(){if(!this._boundsById.size)return null;const t=(0,r.cS)();for(const e of this._boundsById.values())e&&(t[0]=Math.min(e[0],t[0]),t[1]=Math.min(e[1],t[1]),t[2]=Math.max(e[2],t[2]),t[3]=Math.max(e[3],t[3]));return t}get valid(){return!this._indexInvalid}clear(){this._indexInvalid=!1,this._boundsToLoad.length=0,this._boundsById.clear(),this._idByBounds.clear(),this._index.clear()}delete(t){const e=this._boundsById.get(t);this._boundsById.delete(t),e&&(this._idByBounds.delete(e),this._indexInvalid||this._index.remove(e))}forEachInBounds(t,e){this._loadIndex(),function(t,e,i){(function(t){o.minX=t[0],o.minY=t[1],o.maxX=t[2],o.maxY=t[3]})(e),t.search(o,i)}(this._index,t,(t=>e(this._idByBounds.get(t))))}get(t){return this._boundsById.get(t)}has(t){return this._boundsById.has(t)}invalidateIndex(){this._indexInvalid||(this._indexInvalid=!0,this._boundsToLoad.length=0)}set(t,e){if(!this._indexInvalid){const e=this._boundsById.get(t);e&&(this._index.remove(e),this._idByBounds.delete(e))}this._boundsById.set(t,e),e&&(this._idByBounds.set(e,t),this._indexInvalid||(this._boundsToLoad.push(e),this._boundsToLoad.length>5e4&&this._loadIndex()))}}},57191:(t,e,i)=>{i.d(e,{Z:()=>p});var s=i(20102),n=i(32448),r=i(92604),o=i(70586),a=i(60437),h=i(24470),l=i(98732),c=i(37549),u=i(29730),d=i(70272),f=i(5428);const m={getObjectId:t=>t.objectId,getAttributes:t=>t.attributes,getAttribute:(t,e)=>t.attributes[e],cloneWithGeometry:(t,e)=>new d.u_(e,t.attributes,null,t.objectId),getGeometry:t=>t.geometry,getCentroid:(t,e)=>((0,o.Wi)(t.centroid)&&(t.centroid=(0,u.Y)(new f.Z,t.geometry,e.hasZ,e.hasM)),t.centroid)};var _=i(11490);const g=(0,a.Ue)();class p{constructor(t){this.geometryInfo=t,this._boundsStore=new c.H,this._featuresById=new Map,this._markedIds=new Set,this.events=new n.Z,this.featureAdapter=m}get geometryType(){return this.geometryInfo.geometryType}get hasM(){return this.geometryInfo.hasM}get hasZ(){return this.geometryInfo.hasZ}get numFeatures(){return this._featuresById.size}get fullBounds(){return this._boundsStore.fullBounds}get storeStatistics(){let t=0;return this._featuresById.forEach((e=>{(0,o.pC)(e.geometry)&&e.geometry.coords&&(t+=e.geometry.coords.length)})),{featureCount:this._featuresById.size,vertexCount:t/(this.hasZ?this.hasM?4:3:this.hasM?3:2)}}getFullExtent(t){if((0,o.Wi)(this.fullBounds))return null;const[e,i,s,n]=this.fullBounds;return{xmin:e,ymin:i,xmax:s,ymax:n,spatialReference:(0,_.S2)(t)}}add(t){this._add(t),this._emitChanged()}addMany(t){for(const e of t)this._add(e);this._emitChanged()}clear(){this._featuresById.clear(),this._boundsStore.clear(),this._emitChanged()}removeById(t){const e=this._featuresById.get(t);return e?(this._remove(e),this._emitChanged(),e):null}removeManyById(t){this._boundsStore.invalidateIndex();for(const e of t){const t=this._featuresById.get(e);t&&this._remove(t)}this._emitChanged()}forEachBounds(t,e){for(const i of t){const t=this._boundsStore.get(i.objectId);t&&e((0,a.JR)(g,t))}}getFeature(t){return this._featuresById.get(t)}has(t){return this._featuresById.has(t)}forEach(t){this._featuresById.forEach((e=>t(e)))}forEachInBounds(t,e){this._boundsStore.forEachInBounds(t,(t=>{e(this._featuresById.get(t))}))}startMarkingUsedFeatures(){this._boundsStore.invalidateIndex(),this._markedIds.clear()}sweep(){let t=!1;this._featuresById.forEach(((e,i)=>{this._markedIds.has(i)||(t=!0,this._remove(e))})),this._markedIds.clear(),t&&this._emitChanged()}_emitChanged(){this.events.emit("changed",void 0)}_add(t){if(!t)return;const e=t.objectId;if(null==e)return void r.Z.getLogger("esri.layers.graphics.data.FeatureStore").error(new s.Z("featurestore:invalid-feature","feature id is missing",{feature:t}));const i=this._featuresById.get(e);let n;if(this._markedIds.add(e),i?(t.displayId=i.displayId,n=this._boundsStore.get(e),this._boundsStore.delete(e)):(0,o.pC)(this.onFeatureAdd)&&this.onFeatureAdd(t),(0,o.Wi)(t.geometry)||!t.geometry.coords||!t.geometry.coords.length)return this._boundsStore.set(e,null),void this._featuresById.set(e,t);n=(0,l.$)((0,o.pC)(n)?n:(0,h.Ue)(),t.geometry,this.geometryInfo.hasZ,this.geometryInfo.hasM),(0,o.pC)(n)&&this._boundsStore.set(e,n),this._featuresById.set(e,t)}_remove(t){(0,o.pC)(this.onFeatureRemove)&&this.onFeatureRemove(t);const e=t.objectId;return this._markedIds.delete(e),this._boundsStore.delete(e),this._featuresById.delete(e),t}}},23095:(t,e,i)=>{i.d(e,{O0:()=>d,av:()=>h,b:()=>g,d1:()=>c,og:()=>_});var s=i(70586),n=i(8744),r=i(35671);class o{constructor(){this.code=null,this.description=null}}class a{constructor(t){this.error=new o,this.globalId=null,this.objectId=null,this.success=!1,this.uniqueId=null,this.error.description=t}}function h(t){return new a(t)}class l{constructor(t){this.globalId=null,this.success=!0,this.objectId=this.uniqueId=t}}function c(t){return new l(t)}const u=new Set;function d(t,e,i,s=!1,n){u.clear();for(const o in i){const a=t.get(o);if(!a)continue;const l=i[o],c=f(a,l);if(c!==l&&n&&n.push({name:"invalid-value-type",message:"attribute value was converted to match the field type",details:{field:a,originalValue:l,sanitizedValue:c}}),u.add(a.name),a&&(s||a.editable)){const t=(0,r.Qc)(a,c);if(t)return h((0,r.vP)(t,a,c));e[a.name]=c}}for(const e of t?.requiredFields??[])if(!u.has(e.name))return h(`missing required field "${e.name}"`);return null}function f(t,e){let i=e;return"string"==typeof e&&(0,r.H7)(t)?i=parseFloat(e):null!=e&&(0,r.qN)(t)&&"string"!=typeof e&&(i=String(e)),(0,r.Pz)(i)}let m;function _(t,e){if(!t||!(0,n.JY)(e))return t;if("rings"in t||"paths"in t){if((0,s.Wi)(m))throw new TypeError("geometry engine not loaded");return m.simplify(e,t)}return t}async function g(t,e){!(0,n.JY)(t)||"esriGeometryPolygon"!==e&&"esriGeometryPolyline"!==e||await async function(){return(0,s.Wi)(m)&&(m=await Promise.all([i.e(5837),i.e(247)]).then(i.bind(i,30247))),m}()}},86719:(t,e,i)=>{i.d(e,{v:()=>s});const s=new(i(35454).X)({esriFieldTypeSmallInteger:"small-integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"single",esriFieldTypeDouble:"double",esriFieldTypeLong:"long",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"oid",esriFieldTypeGeometry:"geometry",esriFieldTypeBlob:"blob",esriFieldTypeRaster:"raster",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"global-id",esriFieldTypeXML:"xml"})},11282:(t,e,i)=>{i.d(e,{cv:()=>a,en:()=>o,lA:()=>r}),i(68773),i(40330);var s=i(22974),n=i(17452);function r(t,e){return e?{...e,query:{...t??{},...e.query}}:{query:t}}function o(t){return"string"==typeof t?(0,n.mN)(t):(0,s.d9)(t)}function a(t,e,i){const s={};for(const n in t){if("declaredClass"===n)continue;const r=t[n];if(null!=r&&"function"!=typeof r)if(Array.isArray(r)){s[n]=[];for(let t=0;t<r.length;t++)s[n][t]=a(r[t])}else if("object"==typeof r)if(r.toJSON){const t=r.toJSON(i&&i[n]);s[n]=e?t:JSON.stringify(t)}else s[n]=e?r:JSON.stringify(r);else s[n]=r}return s}i(71058)}}]);