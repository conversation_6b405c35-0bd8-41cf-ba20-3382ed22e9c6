<template>
  <div class="app-setting">
    <el-form
      ref="eForm"
      :model="formData"
      label-width="100px"
    >
      <el-form-item
        label="App上传"
        prop="logoUrl"
      >
        <el-upload
          ref="refUpload"
          class="upload-demo"
          :action="useAppStore().actionUrl + 'file/api/upload/file'"
          :limit="1"
          :headers="{
            'X-Authorization': 'Bearer ' + useUserStore().token
          }"
          :auto-upload="false"
          :on-exceed="handleExceed"
          :before-upload="handleBeforeUpload"
          :on-success="handleSuccess"
          :on-error="handleError"
          @remove="handleRemove"
        >
          <template #trigger>
            <el-button
              type="primary"
              :loading="loading"
            >
              选择上传文件
            </el-button>
          </template>
          <div
            class="el-upload el-upload--text"
            style="margin-left: 8px"
          >
            <el-button
              class="ml-3"
              type="success"
              :disabled="loading"
              @click="submitUpload"
            >
              点击上传
            </el-button>
          </div>

          <template #tip>
            <div class="el-upload__tip">
              点击按钮选择要上传的安装包文件
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item
        label="版本号"
        prop="versionCode"
      >
        <el-input v-model="formData.versionCode"></el-input>
      </el-form-item>
      <el-form-item
        label="版本名称"
        prop="versionName"
      >
        <el-input v-model="formData.versionName"></el-input>
      </el-form-item>
      <el-form-item
        label="APP名称"
        prop="appName"
      >
        <el-input v-model="formData.appName"></el-input>
      </el-form-item>

      <el-form-item
        label="APP备注信息"
        prop="remark"
      >
        <el-input v-model="formData.remark"></el-input>
      </el-form-item>
      <el-form-item
        label="更新内容明细"
        prop="versionContent"
      >
        <el-input
          v-model="formData.versionContent"
          type="textarea"
        ></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button
          type="primary"
          :disabled="loading"
          @click="saveApp"
        >
          保存
        </el-button>
        <el-button
          type="default"
          @click="historyVersions"
        >
          历史版本
        </el-button>
      </el-form-item>
    </el-form>

    <DialogForm
      ref="refDialog"
      :config="FormConfig"
    >
      <FormTable :config="TableConfig"></FormTable>
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import type { UploadInstance, UploadFile, UploadFiles, UploadRawFile, UploadProps } from 'element-plus'
import { genFileId } from 'element-plus'
import { useAppStore, useUserStore } from '@/store'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { DeleteAppVersion, GetAppVersionHistory, PostAppVersion } from '@/api/login'
import { formatDate } from '@/utils/DateFormatter'
import { downloadFile } from '@/utils/fileHelper'
import { getUrlPramByName } from '@/utils/GlobalHelper'

const refDialog = ref<IDialogFormIns>()
const refUpload = ref<UploadInstance>()
const loading = ref<boolean>(false)
const formData = reactive<Record<string, any>>({
  appKey: window.SITE_CONFIG.LOGIN_CONFIG.APPKEY
})
const handleExceed: UploadProps['onExceed'] = files => {
  refUpload.value?.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  refUpload.value?.handleStart(file)
}
const handleBeforeUpload = () => {
  loading.value = true
}
const handleError = e => {
  console.log(e)
  loading.value = false
}
const submitUpload = () => {
  refUpload.value?.submit()
}
// 上传成功后处理
const handleSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  console.log(response, uploadFile, uploadFiles)
  formData.url = response + '?name=' + uploadFile.name
  loading.value = false
}
const handleRemove = () => {
  formData.url = ''
}
const eForm = ref()
// 企业配置
const saveApp = () => {
  if (!formData.appKey) {
    SLMessage.error('保存失败，请联系管理员')
    console.log('appKey不存在')
    return
  }
  if (!formData.url) {
    SLMessage.error('请先选择文件并上传')
    return
  }
  eForm.value.validate(async valid => {
    if (valid) {
      try {
        const res = await PostAppVersion(formData)
        if (res.data.code === 200) {
          SLMessage.success('保存成功')
        } else {
          SLMessage.error(res.data.message || '保存失败')
        }
      } catch (error) {
        SLMessage.error('保存失败')
      }
    } else {
      SLMessage.warning('输入内容有误，请检查后再试')
      return false
    }
  })
}
const FormConfig = reactive<IDialogFormConfig>({
  group: [],
  title: 'App历史版本',
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {}
})
const historyVersions = () => {
  refDialog.value?.openDialog()
  refreshData()
}
const TableConfig = reactive<ITable>({
  columns: [
    { minWidth: 120, label: '软件名', prop: 'appName' },
    {
      minWidth: 220,
      label: '下载路径',
      isUrl: true,
      download: true,
      prop: 'url',
      handleClick(row, config) {
        const name = getUrlPramByName(row.url, 'name')?.value ?? 'App'
        config.download && downloadFile(row.url, name)
      }
    },
    { minWidth: 120, label: '版本号', prop: 'versionCode' },
    { minWidth: 120, label: '版本信息', prop: 'versionContent' },
    { minWidth: 120, label: '版本名称', prop: 'versionName' },
    {
      minWidth: 160,
      label: '上传时间',
      prop: 'createTime',
      formatter(row, value) {
        return formatDate(value)
      }
    }
  ],
  dataList: [],
  operations: [{ perm: true, text: '删除', type: 'danger', click: row => removeVersion(row) }],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const refreshData = async () => {
  try {
    const res = await GetAppVersionHistory({
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    })
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data.total || 0
  } catch (error) {
    //
  }
}
const removeVersion = (row: any) => {
  const id = row?.id
  if (!id) {
    SLMessage.error('请先选择一个要删除的版本')
    return
  }
  SLConfirm('确定删除？', '删除提示')
    .then(() => {
      try {
        DeleteAppVersion([id])
          .then(res => {
            if (res.data?.code === 200) {
              SLMessage.success('删除成功')
              refreshData()
            } else {
              SLMessage.error('删除失败')
            }
          })
          .catch(error => {
            SLMessage.error(error.message ?? '删除失败')
          })
      } catch (error) {
        //
      }
    })
    .catch(() => {
      //
    })
}
</script>
<style lang="scss" scoped>
.upload-demo {
  width: 100%;
}
</style>
