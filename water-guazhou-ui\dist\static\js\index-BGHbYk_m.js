import{_ as S}from"./TreeBox-DDD2iwoR.js";import{_ as E}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as P}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{_ as N}from"./index-BJ-QPYom.js";import{d as A,M as F,c as f,s as L,r as _,x as n,a8 as M,a9 as y,S as R,o as V,g as q,h as B,F as b,q as p,i as d,b7 as O}from"./index-r0dFAfgr.js";import{I as u}from"./common-CvK_P_ao.js";import{p as W,a as $,b as w,d as U,c as G}from"./equipmentManage-DuoY00aj.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const re=A({__name:"index",setup(H){const{$btnPerms:g}=F(),l=f(),m=f(),h=f({filters:[{label:"属性编码",field:"code",type:"input"},{label:"属性名称",field:"name",type:"input"},{label:"所属类别",field:"typeName",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:u.QUERY,click:()=>r()},{type:"default",perm:!0,text:"重置",svgIcon:L(O),click:()=>{var e;(e=m.value)==null||e.resetForm(),r()}},{perm:!0,text:"新建",icon:u.ADD,type:"success",click:()=>D("新建")}]}]}),o=_({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"属性编码",prop:"code"},{label:"属性名称",prop:"name"},{label:"所属类别",prop:"mainTypeName"},{label:"备注",prop:"remark"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime"}],operationWidth:"200px",operations:[{type:"primary",text:"编辑",icon:u.EDIT,perm:g("RoleManageEdit"),click:e=>x(e)},{type:"danger",text:"删除",perm:g("RoleManageDelete"),icon:u.DELETE,click:e=>k(e)}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,r()}}}),a=_({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{a.currentProject=e,r()}}),i=_({title:"新增",dialogWidth:"500px",labelWidth:"100px",submit:e=>{e.id?W(e.id,e).then(()=>{var t;n.success("修改成功"),(t=l.value)==null||t.closeDialog(),r()}).catch(t=>{n.warning(t)}):$(e).then(()=>{var t;n.success("新增成功"),(t=l.value)==null||t.closeDialog(),r()}).catch(t=>{n.warning(t)})},defaultValue:{},group:[{fields:[{type:"select-tree",label:"所属类型",field:"serialId",checkStrictly:!0,options:M(()=>y(a.data,"children",{label:"name",value:"serialId"})),readonly:!0},{type:"input-number",label:"属性编码",field:"code",rules:[{required:!0,message:"请输入属性编码"}]},{type:"input",label:"属性名称",field:"name",rules:[{required:!0,message:"请输入属性名称"}]},{type:"textarea",label:"备注/说明",field:"remark"}]}]}),r=async()=>{var t;const e={serialId:a.currentProject.serialId||"",...(t=m.value)==null?void 0:t.queryParams};w(e).then(c=>{o.dataList=c.data.data.data||[],o.dataList.map(s=>(s.category=a.currentProject.name,s)),o.pagination.total=c.data.data.total||0})},D=e=>{var t;if(i.title=e,!a.currentProject.serialId){n.warning("请选中左侧类别树");return}if(a.currentProject.level!=="3"){n.warning("请选择末级，分组无法添加");return}i.defaultValue={serialId:a.currentProject.serialId},(t=l.value)==null||t.openDialog()},x=e=>{var t;i.title="编辑",i.defaultValue={...e||{}},(t=l.value)==null||t.openDialog()},k=e=>{R("确定删除指定类别属性?","删除提示").then(()=>{U((e==null?void 0:e.id)||"").then(()=>{n.success("删除成功"),r()})})};function v(){G().then(e=>{var t;a.data=y(((t=e==null?void 0:e.data)==null?void 0:t.data)||[]),r()})}return V(async()=>{v()}),(e,t)=>{const c=N,s=j,T=P,C=E,I=S;return q(),B(I,null,{tree:b(()=>[p(c,{"tree-data":d(a)},null,8,["tree-data"])]),default:b(()=>[p(s,{ref_key:"refSearch",ref:m,config:d(h)},null,8,["config"]),p(T,{config:d(o),class:"card-table"},null,8,["config"]),p(C,{ref_key:"refForm",ref:l,config:d(i)},null,8,["config"])]),_:1})}}});export{re as default};
