const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/geometryEngineJSON-DX0oHAsv.js","static/js/geometryEngineBase-BhsKaODW.js","static/js/json-Wa8cmqdu.js"])))=>i.map(i=>d[i]);
import{T as d,a3 as p}from"./index-r0dFAfgr.js";import{I as m}from"./Point-WxyopZva.js";import{ci as g,cj as y,am as w,ck as I,cl as _}from"./MapView-DaoQedLH.js";class b{constructor(){this.code=null,this.description=null}}class v{constructor(t){this.error=new b,this.globalId=null,this.objectId=null,this.success=!1,this.uniqueId=null,this.error.description=t}}function f(e){return new v(e)}class q{constructor(t){this.globalId=null,this.success=!0,this.objectId=this.uniqueId=t}}function F(e){return new q(e)}const l=new Set;function G(e,t,i,h=!1,u){l.clear();for(const r in i){const n=e.get(r);if(!n)continue;const a=i[r],s=P(n,a);if(s!==a&&u&&u.push({name:"invalid-value-type",message:"attribute value was converted to match the field type",details:{field:n,originalValue:a,sanitizedValue:s}}),l.add(n.name),n&&(h||n.editable)){const c=g(n,s);if(c)return f(y(c,n,s));t[n.name]=s}}for(const r of(e==null?void 0:e.requiredFields)??[])if(!l.has(r.name))return f(`missing required field "${r.name}"`);return null}function P(e,t){let i=t;return typeof t=="string"&&w(e)?i=parseFloat(t):t!=null&&I(e)&&typeof t!="string"&&(i=String(t)),_(i)}let o;function S(e,t){if(!e||!m(t))return e;if("rings"in e||"paths"in e){if(d(o))throw new TypeError("geometry engine not loaded");return o.simplify(t,e)}return e}async function j(){return d(o)&&(o=await p(()=>import("./geometryEngineJSON-DX0oHAsv.js").then(e=>e.g),__vite__mapDeps([0,1,2]))),o}async function k(e,t){!m(e)||t!=="esriGeometryPolygon"&&t!=="esriGeometryPolyline"||await j()}export{f as a,F as f,S as g,G as m,k as w};
