import{_ as T}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as x}from"./CardTable-rdWOL4_6.js";import{_ as C}from"./CardSearch-CB_HNR-Q.js";import{z as c,C as E,c as y,r as d,bF as v,b as i,S as L,o as B,g as R,n as w,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function A(l){return c({url:"/api/base/database/structure/list",method:"get",params:l})}function k(l){return c({url:"/api/base/database/structure/getDetail",method:"get",params:{id:l}})}function P(l){return c({url:"/api/base/database/structure/add",method:"post",data:l})}function q(l){return c({url:"/api/base/database/structure/edit",method:"post",data:l})}function I(l){return c({url:"/api/base/database/structure/deleteIds",method:"delete",data:l})}const j={class:"wrapper"},F={__name:"databaseStructure",setup(l){const b=y(),u=y(),_=d({labelWidth:"100px",filters:[{type:"input",label:"对象名称",field:"objectName",placeholder:"请输入操作对象名称",onChange:()=>n()},{type:"select",label:"执行状态",field:"status",options:[{label:"全部",value:""},{label:"成功",value:"SUCCESS"},{label:"失败",value:"FAILED"},{label:"执行中",value:"PROCESSING"}],onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"新增",click:()=>f()},{perm:!0,type:"danger",text:"批量删除",click:()=>h()}]}],defaultParams:{}}),o=d({columns:[{label:"主键ID",prop:"id"},{label:"版本号",prop:"version"},{label:"变更类型",prop:"changeType",formatter:e=>({CREATE:"新建",ALTER:"修改",DROP:"删除"})[e.changeType]||e.changeType},{label:"操作对象名",prop:"objectName"},{label:"对象类型",prop:"objectType",formatter:e=>({TABLE:"数据表",VIEW:"视图",PROCEDURE:"存储过程"})[e.objectType]||e.objectType},{label:"变更描述",prop:"changeDescription"},{label:"执行状态",prop:"status",tagType:e=>({SUCCESS:"success",FAILED:"danger",PROCESSING:"warning"})[e.status]},{label:"执行时间",prop:"executedAt",formatter:e=>v(e.executedAt).format("YYYY-MM-DD HH:mm:ss")},{label:"执行者",prop:"executedBy"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>S(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>f(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>h(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{o.pagination.page=e,n()},handleSize:e=>{o.pagination.limit=e,n()}},handleSelectChange:e=>{o.selectList=e||[]}}),a=d({title:"SQL变更管理",group:[{fields:[{type:"select",label:"变更类型",field:"changeType",options:[{label:"新建",value:"CREATE"},{label:"修改",value:"ALTER"},{label:"删除",value:"DROP"}],rules:[{required:!0,message:"请选择变更类型"}]},{type:"select",label:"对象类型",field:"objectType",options:[{label:"数据表",value:"TABLE"},{label:"视图",value:"VIEW"},{label:"存储过程",value:"PROCEDURE"}],rules:[{required:!0,message:"请选择对象类型"}]},{type:"input",label:"操作对象名",field:"objectName",rules:[{required:!0,message:"请输入操作对象名称"}]},{type:"textarea",label:"变更描述",field:"changeDescription",rows:3,rules:[{required:!0,message:"请输入变更描述"}]},{type:"textarea",label:"SQL语句",field:"sqlStatement",rows:6,rules:[{required:!0,message:"请输入SQL语句"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:800,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"提交",submit:async e=>{var t;try{e.id?(await q(e),i.success("修改成功")):(await P(e),i.success("新增成功")),(t=u.value)==null||t.closeDialog(),n()}catch{i.error("操作失败")}}}),m=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="提交"},f=e=>{var t;m(),a.title=e?"编辑变更记录":"新增变更记录",a.defaultValue={...e||{}},(t=u.value)==null||t.openDialog()},S=async e=>{var t,r;try{const s=await k(e.id),p=((t=s.data)==null?void 0:t.data)||s;m(),a.title="变更记录详情",a.defaultValue={...p},a.group[0].fields.forEach(D=>{D.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=u.value)==null||r.openDialog()}catch{i.error("获取详情失败")}},h=e=>{L("确定删除？","删除提示").then(async()=>{var r;const t=e?[e.id]:((r=o.selectList)==null?void 0:r.map(s=>s.id))||[];if(!t.length){i.warning("请选择要删除的数据");return}await I(t),i.success("删除成功"),n()}).catch(()=>{})},n=async()=>{var e,t;try{const r=await A({page:o.pagination.page,size:o.pagination.limit,...((e=b.value)==null?void 0:e.queryParams)||{}}),s=((t=r.data)==null?void 0:t.data)||r;o.dataList=s.records||s,o.pagination.total=s.total||s.length||0}catch{i.error("数据加载失败")}};return B(()=>{n()}),(e,t)=>{const r=C,s=x,p=T;return R(),w("div",j,[g(r,{ref_key:"refSearch",ref:b,config:_},null,8,["config"]),g(s,{class:"card-table",config:o},null,8,["config"]),g(p,{ref_key:"refDialogForm",ref:u,config:a},null,8,["config"])])}}},Y=E(F,[["__scopeId","data-v-3f63709c"]]);export{Y as default};
