package com.istar_zuul.istar_zuul.filter;

import com.netflix.zuul.ZuulFilter;
import com.netflix.zuul.context.RequestContext;
import com.netflix.zuul.exception.ZuulException;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.netflix.zuul.filters.support.FilterConstants;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class LoginFilter extends ZuulFilter {

    private static final Logger LOG = LoggerFactory.getLogger(LoginFilter.class);

    public static final String DEVICE_API_ENTRY_POINT = "/api/v1";
    public static final String FORM_BASED_LOGIN_ENTRY_POINT = "/api/auth/login";
    public static final String PUBLIC_LOGIN_ENTRY_POINT = "/api/auth/login/public";
    public static final String TOKEN_REFRESH_ENTRY_POINT = "/api/auth/token";
    public static final String NO_AUTH = "/api/noauth/";
    public static final String NO_AUTH_BACKGROUND = "/api/admin/noauth/settings/background";
    public static final String ENERGY = "/api/energy";
    public static final String ALARM = "/api/noAuthAlarm";
    public static final String GET_ALL_DEVICE_BY_TENANT_ID = "/api/getAllDeviceByTenantId";
    public static final String GET_ALL_DEVICE = "/api/getAllDevice";
    public static final String GET_ALL_DEVICE_BY_TYPE = "/api/getAllDevice/";
    public static final String DATAMAINTENANCE = "/api/data/maintenance";
    public static final String DICT_DEVICE_SELECT = "/api/dict/device/select";
    public static final String NONGYE_GET_IMAGE = "/api/nongye/dataInfo/getImgByName";
    public static final String SUYUAN_GET_IMAGE = "/suyuan/api/suyuan/file/getImgByName";
    public static final String GET_IMAGE = "/api/file/getImgByName";
    public static final String VIEW_FILE = "/api/file/viewFile";
    public static final String API_DICT_MEDIA = "/api/dict/media/live";
    public static final String GET_SUYUAN_INFO_START_WITH = "/suyuan/api/suyuan/info";


    public static final String DEVICE_TEMPLATE_GROUP_NOAUTH = "/api/deviceTemplate/noauth/protocol/group";

    public static final String DEVICE_DATA_NOAUTH = "/istar/api/device/dataNoAuth";

    public static final String WATER_METER_DATA = "/api/revenue/waterMeter/waterMeterData";

    public static final String NONGYE_VIDEO = "/api/video/findByProjectAndType";

    public static final String VIDEO_URL = "/api/video/getPreviewUrl";

    public static final String AEP_SIGN = "/api/aep/sign";
    
    public static final String WECHAT_NOTIFY = "/api/wechat/notify";

    public static final String WEB_SOCKET = "/api/ws";

    public static final String PORTAL_COMMENT_URL = "/api/SsPortalComment/public";

    public static final String PORTAL_PUBLIC_ACCESS_URL = "/api/SsPortal";

    public static final String GET_APP_VERSION = "/api/app/version/checkVersion";

    private static final List<String> noauthUrl = Arrays.asList(
            DEVICE_API_ENTRY_POINT, NO_AUTH,GET_IMAGE,DICT_DEVICE_SELECT,
            FORM_BASED_LOGIN_ENTRY_POINT,VIDEO_URL,WEB_SOCKET,
            PUBLIC_LOGIN_ENTRY_POINT, DEVICE_DATA_NOAUTH,WATER_METER_DATA,
            TOKEN_REFRESH_ENTRY_POINT, DEVICE_TEMPLATE_GROUP_NOAUTH,
            ENERGY, ALARM, DATAMAINTENANCE, AEP_SIGN, VIEW_FILE,API_DICT_MEDIA,
            SUYUAN_GET_IMAGE, GET_ALL_DEVICE, GET_ALL_DEVICE_BY_TYPE,
            GET_SUYUAN_INFO_START_WITH, WECHAT_NOTIFY,
            NONGYE_GET_IMAGE, NO_AUTH_BACKGROUND, NONGYE_VIDEO, GET_ALL_DEVICE_BY_TENANT_ID,
            PORTAL_COMMENT_URL,PORTAL_PUBLIC_ACCESS_URL,GET_APP_VERSION
    );

    @Value("${tokenSignKey}")
    private String tokenSignKey;

//    @Value("${zuul.prefix}")
//    private String zuulPrefix;

    @Override
    public String filterType() {
        //四种类型：pre、routing、post、error
        return FilterConstants.PRE_TYPE;
    }

    @Override
    public int filterOrder() {
        return 0;
    }

    @Override
    public boolean shouldFilter() {
        return true;
    }


    @Override
    public Object run() throws ZuulException {
        //上下文对象
        RequestContext requestContext = RequestContext.getCurrentContext();
        //请求对象
        HttpServletRequest request = requestContext.getRequest();

        String requestURI = request.getRequestURI();
        for (String s : noauthUrl) {
            if (requestURI.startsWith(s)) {
                return null;
            }
            if (requestURI.contains(s)) {
                return null;
            }
//            if (requestURI.startsWith("/" + zuulPrefix + s)) {
//                return null;
//            }

        }

        //查询身份令牌
        String access_token = getJwtFromHeader(request);
        if (access_token == null) {
            //拒绝访问
            accessDenied();
            return null;
        }
        //校验token
        try {
            Jws<Claims> claimsJws = Jwts.parser().setSigningKey(tokenSignKey).parseClaimsJws(access_token.substring(7));
            Claims claims = claimsJws.getBody();
            Integer exp = (Integer) claims.get("exp");
            if (Long.valueOf(exp) * 1000 < System.currentTimeMillis()) {
                accessDenied();
                return null;
            }
        } catch (Exception e) {
            accessDenied();
            return null;
        }

        return null;
    }

    /**
     * 拒绝访问
     */
    private void accessDenied() {
        //上下文对象
        RequestContext requestContext = RequestContext.getCurrentContext();
        requestContext.setSendZuulResponse(false);      //拒绝访问
        //设置状态码
        requestContext.setResponseStatusCode(401);
        requestContext.getResponse().setContentType("application/json;charset=utf-8");
    }

    public String getJwtFromHeader(HttpServletRequest request) {
        String authorization = request.getHeader("X-Authorization");
        if (authorization == null || "".equals(authorization)) {
            //拒绝访问
            return null;
        }
        if (!authorization.startsWith("Bearer ") && !authorization.startsWith("bearer ")) {
            //拒绝访问
            return null;
        }
        return authorization;
    }


}