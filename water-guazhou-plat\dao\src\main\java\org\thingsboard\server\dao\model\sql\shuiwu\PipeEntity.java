package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_PIPE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_PIPE_CODE)
    private String code;

    @Column(name = ModelConstants.SHUIWU_PIPE_NAME)
    private String name;

    @Column(name = ModelConstants.SHUIWU_PIPE_TYPE)
    private String type;

    @Column(name = ModelConstants.SHUIWU_PIPE_START_POINT)
    private String startPointId;

    @Column(name = ModelConstants.SHUIWU_PIPE_END_POINT)
    private String endPointId;

    @Column(name = ModelConstants.SHUIWU_PIPE_MATERIAL)
    private String material;

    @Column(name = ModelConstants.SHUIWU_PIPE_CALIBER)
    private BigDecimal caliber;

    @Column(name = ModelConstants.SHUIWU_PIPE_BUILD_TIME)
    private Date buildTime;

    @Column(name = ModelConstants.SHUIWU_PIPE_START_USE_TIME)
    private Date startUseTime;

    @Column(name = ModelConstants.SHUIWU_PIPE_DISCARD_TIME)
    private Date discardTime;

    @Column(name = ModelConstants.SHUIWU_PIPE_BURIED_METHOD)
    private Date buriedMethod;

    @Column(name = ModelConstants.SHUIWU_PIPE_ADDRESS)
    private String address;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.REMARK)
    private String remark;

    @Column(name = ModelConstants.IS_DEL)
    private String isDel;

    @Column(name = ModelConstants.DEVICE_ID_PROPERTY)
    private String deviceId;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private PipePointEntity startPoint;

    @Transient
    private PipePointEntity endPoint;

    @Transient
    private String startPointLocation;

    @Transient
    private String endPointLocation;

    @Transient
    private String value;

}
