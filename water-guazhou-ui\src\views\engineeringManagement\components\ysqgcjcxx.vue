<!-- 工程管理-详情-验收期工程基础信息 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '验收期工程基础信息',
  fields: [
    {
      type: 'text',
      label: '开始时间:',
      field: 'beginTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    {
      type: 'text',
      label: '完成时间:',
      field: 'endTimeName',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    { type: 'text', label: '申请单位:', field: 'applicantOrganization' },
    { type: 'text', label: '申请人:', field: 'applicant' },
    { type: 'text', label: '施工单位:', field: 'constructOrganization' },
    { type: 'text', label: '监理单位:', field: 'supervisorOrganization' },
    { type: 'text', label: '审计单位:', field: 'auditOrganization' },
    { type: 'text', label: '设计单位:', field: 'designOrganization' },
    { type: 'text', label: '申请电话:', field: 'applicantPhone' },
    { type: 'text', label: '验收说明:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
