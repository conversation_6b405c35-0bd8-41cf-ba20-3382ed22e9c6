package org.thingsboard.server.dao.deviceType;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseInquiry;
import org.thingsboard.server.dao.util.imodel.query.device.DevicePurchaseInquiryPageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DevicePurchaseInquirySaveRequest;
import org.thingsboard.server.dao.util.imodel.response.device.DevicePurchaseInquiryItemResponse;

import java.util.List;

public interface DevicePurchaseInquiryService {
    /**
     * 分页条件查询设备询价信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DevicePurchaseInquiryItemResponse> findAllConditional(DevicePurchaseInquiryPageRequest request);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    DevicePurchaseInquiry update(DevicePurchaseInquiry entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param collect 实体列表
     * @return 保存好的数据
     */
    List<DevicePurchaseInquiry> saveAll(List<DevicePurchaseInquirySaveRequest> collect);

    /**
     * 通过采购单id删除
     *
     * @param id     采购单id
     * @param idList 采购单条目的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean deleteByPurchaseItemMainIdOnMainIdNotIn(String id, List<String> idList);

    /**
     * 批量通过采购单子表id删除
     *
     * @param idList 采购单子表id列表
     * @return 是否成功
     */
    boolean deleteBatchByPurchaseItemId(List<String> idList);

    /**
     * 通过采购单子表id删除
     *
     * @param id 采购单子表id删除
     * @return 是否成功
     */
    boolean deleteByPurchaseItemId(String id);

    /**
     * 更新询价信息
     *
     * @param entity 巡检信息
     * @return 保存号的询价信息
     */
    DevicePurchaseInquiry inquiry(DevicePurchaseInquiry entity);

}
