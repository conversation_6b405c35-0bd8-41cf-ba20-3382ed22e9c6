"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[540],{30540:(t,e,o)=>{o.r(e),o.d(e,{executeAttachmentQuery:()=>p,fetchAttachments:()=>c,processAttachmentQueryResult:()=>d});var n=o(40330),r=o(3172),i=o(17452),a=o(76497),s=o(98326);function l(t){const e=t.toJSON();return e.attachmentTypes&&(e.attachmentTypes=e.attachmentTypes.join(",")),e.keywords&&(e.keywords=e.keywords.join(",")),e.globalIds&&(e.globalIds=e.globalIds.join(",")),e.objectIds&&(e.objectIds=e.objectIds.join(",")),e.size&&(e.size=e.size.join(",")),e}function d(t,e){const o={};for(const r of e){const{parentObjectId:e,parentGlobalId:a,attachmentInfos:l}=r;for(const r of l){const{id:l}=r,d=(0,i.qg)((0,n.Dp)(`${t.path}/${e}/attachments/${l}`)),p=s.Z.fromJSON(r);p.set({url:d,parentObjectId:e,parentGlobalId:a}),o[e]?o[e].push(p):o[e]=[p]}}return o}function p(t,e,o){let n={query:(0,a.A)({...t.query,f:"json",...l(e)})};return o&&(n={...o,...n,query:{...o.query,...n.query}}),(0,r.default)(t.path+"/queryAttachments",n).then((t=>t.data.attachmentGroups))}async function c(t,e,o){const{objectIds:n}=e,i=[];for(const e of n)i.push((0,r.default)(t.path+"/"+e+"/attachments",o));return Promise.all(i).then((t=>n.map(((e,o)=>({parentObjectId:e,attachmentInfos:t[o].data.attachmentInfos})))))}},98326:(t,e,o)=>{o.d(e,{Z:()=>c});var n,r=o(43697),i=o(96674),a=o(5600),s=o(75215),l=(o(67676),o(52011));const d={1:{id:1,rotation:0,mirrored:!1},2:{id:2,rotation:0,mirrored:!0},3:{id:3,rotation:180,mirrored:!1},4:{id:4,rotation:180,mirrored:!0},5:{id:5,rotation:-90,mirrored:!0},6:{id:6,rotation:90,mirrored:!1},7:{id:7,rotation:90,mirrored:!0},8:{id:8,rotation:-90,mirrored:!1}};let p=n=class extends i.wq{constructor(t){super(t),this.contentType=null,this.exifInfo=null,this.id=null,this.globalId=null,this.keywords=null,this.name=null,this.parentGlobalId=null,this.parentObjectId=null,this.size=null,this.url=null}get orientationInfo(){const{exifInfo:t}=this,e=function(t){const{exifInfo:e,exifName:o,tagName:n}=t;if(!e||!o||!n)return null;const r=e.find((t=>t.name===o));return r?function(t){const{tagName:e,tags:o}=t;if(!o||!e)return null;const n=o.find((t=>t.name===e));return n&&n.value||null}({tagName:n,tags:r.tags}):null}({exifName:"Exif IFD0",tagName:"Orientation",exifInfo:t});return d[e]||null}clone(){return new n({contentType:this.contentType,exifInfo:this.exifInfo,id:this.id,globalId:this.globalId,keywords:this.keywords,name:this.name,parentGlobalId:this.parentGlobalId,parentObjectId:this.parentObjectId,size:this.size,url:this.url})}};(0,r._)([(0,a.Cb)({type:String})],p.prototype,"contentType",void 0),(0,r._)([(0,a.Cb)()],p.prototype,"exifInfo",void 0),(0,r._)([(0,a.Cb)({readOnly:!0})],p.prototype,"orientationInfo",null),(0,r._)([(0,a.Cb)({type:s.z8})],p.prototype,"id",void 0),(0,r._)([(0,a.Cb)({type:String})],p.prototype,"globalId",void 0),(0,r._)([(0,a.Cb)({type:String})],p.prototype,"keywords",void 0),(0,r._)([(0,a.Cb)({type:String})],p.prototype,"name",void 0),(0,r._)([(0,a.Cb)({json:{read:!1}})],p.prototype,"parentGlobalId",void 0),(0,r._)([(0,a.Cb)({json:{read:!1}})],p.prototype,"parentObjectId",void 0),(0,r._)([(0,a.Cb)({type:s.z8})],p.prototype,"size",void 0),(0,r._)([(0,a.Cb)({json:{read:!1}})],p.prototype,"url",void 0),p=n=(0,r._)([(0,l.j)("esri.layers.support.AttachmentInfo")],p);const c=p}}]);