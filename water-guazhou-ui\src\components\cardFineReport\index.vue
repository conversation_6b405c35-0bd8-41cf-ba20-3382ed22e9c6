<template>
  <!-- 帆软报表  iframe组建 -->
  <el-card class="chart-wrapper">
    <!-- 顶部栏 -->
    <header
      v-if="config.header"
      class="header"
    >
      <!-- 左侧 标题 -->
      <h1 class="title">
        {{ config.header.title }}
      </h1>
      <!-- 中间 选择栏 -->
      <div
        v-if="config.header.switch"
        class="switchBox"
      >
        <span
          v-for="item in config.header.switch.options"
          :key="item.name"
          :class="{ active: config.header.switch.curVal === item.val }"
          @click="config.header.switch.handle(item)"
        >{{ item.name }}</span>
      </div>
      <!-- 右侧 操作 -->
      <div>
        <template v-for="operation in config.header.operations">
          <el-button
            v-if="operation && operation.perm"
            :key="operation.text"
            type="primary"
            :icon="operation.icon"
            style="height: 40px"
            @click="operation.handle"
          >
            {{ operation.text }}
          </el-button>
        </template>
      </div>
    </header>
    <div
      class="chart-container"
      style="overflow: hidden"
    >
      <iframe
        ref="iframe"
        class="fineReportBox"
        frameborder="0"
        scrolling="auto"
        :src="url"
      ></iframe>
    </div>
  </el-card>
</template>

<script lang="ts">
import { computed, defineComponent, PropType } from 'vue'

export default defineComponent({
  props: {
    config: {
      type: Object as PropType<CFRConfig>,
      default() {
        return {
          url: '',
          queryParams: {}
        }
      }
    }
  },
  setup(props: any) {
    const url = computed(() => {
      let url = props.config.url
      for (const key in props.config.queryParams) {
        url += `&${key}=${props.config.queryParams[key]}`
      }
      return url
    })

    const exportFile = (fileType: any) => {
      const exportUrl = url.value + `&format=${fileType}`
      const a = document.createElement('a')
      a.href = exportUrl
      a.click()
    }
    return {
      url,
      exportFile
    }
  }
})
</script>
<style lang="scss">
.el-card__body {
  height: 100%;
}

.chart-wrapper {
  flex: auto;
  // flex: 1;
  margin-top: 0;
  :deep(.el-card__body) {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-top: 0 !important;
    padding-left: 0;
    padding-right: 0;
  }
  .el-card__body {
    padding: 0 16px 16px 16px !important;
    display: flex;
    flex-direction: column;
  }
  .chart-container {
    flex: 1;
    overflow: hidden;
    // background: #212434;
    .fineReportBox {
      // background: #212434;
      width: calc(100% + 18px);
      height: 100%;
    }
  }
  .header {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 18px;
      color: #333;
      font-weight: 400;
    }
    .switchBox {
      // padding: 0 10px;
      display: flex;
      width: 240px;
      height: 40px;
      border-radius: 4px;
      border: 1px solid #404354;
      span {
        // border-right: 1px solid #404354;
        display: block;
        flex: 1;
        color: #fff;
        text-align: center;
        line-height: 40px;
        &.active {
          background-color: #3e8ef7;
        }
        &:hover {
          cursor: pointer;
        }
        &:last-of-type {
          border-right: none;
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
        &:first-of-type {
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }
      }
    }
  }
}
</style>
