package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBasePushSchemeConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePushSchemeConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-推送方案配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "平台管理-推送方案配置")
@RestController
@RequestMapping("api/base/push/scheme/configuration")
public class BasePushSchemeConfigurationController extends BaseController {

    @Autowired
    private IBasePushSchemeConfigurationService basePushSchemeConfigurationService;

    /**
     * 查询平台管理-推送方案配置列表
     */
    @MonitorPerformance(description = "平台管理-查询推送方案配置列表接口")
    @ApiOperation(value = "查询推送方案配置列表")
    @GetMapping("/list")
    public IstarResponse list(BasePushSchemeConfigurationPageRequest basePushSchemeConfiguration) {
        return IstarResponse.ok(basePushSchemeConfigurationService.selectBasePushSchemeConfigurationList(basePushSchemeConfiguration));
    }

    /**
     * 获取平台管理-推送方案配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询推送方案配置详情接口")
    @ApiOperation(value = "查询推送方案配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(basePushSchemeConfigurationService.selectBasePushSchemeConfigurationById(id));
    }

    /**
     * 新增平台管理-推送方案配置
     */
    @MonitorPerformance(description = "平台管理-新增推送方案配置接口")
    @ApiOperation(value = "新增推送方案配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BasePushSchemeConfiguration basePushSchemeConfiguration) {
        return IstarResponse.ok(basePushSchemeConfigurationService.insertBasePushSchemeConfiguration(basePushSchemeConfiguration));
    }

    /**
     * 修改平台管理-推送方案配置
     */
    @MonitorPerformance(description = "平台管理-修改推送方案配置接口")
    @ApiOperation(value = "修改推送方案配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BasePushSchemeConfiguration basePushSchemeConfiguration) {
        return IstarResponse.ok(basePushSchemeConfigurationService.updateBasePushSchemeConfiguration(basePushSchemeConfiguration));
    }

    /**
     * 删除平台管理-推送方案配置
     */
    @MonitorPerformance(description = "平台管理-删除推送方案配置接口")
    @ApiOperation(value = "删除推送方案配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody  List<String> ids) {
        return IstarResponse.ok(basePushSchemeConfigurationService.deleteBasePushSchemeConfigurationByIds(ids));
    }
}
