<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseVectorConfigurationMapper">

    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration"
               id="BaseVectorConfigurationResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="source" column="source"/>
        <result property="refreshInterval" column="refresh_interval"/>
        <result property="modelUrl" column="model_url"/>
        <result property="scale" column="scale"/>
        <result property="rotation" column="rotation"/>
        <result property="lodStrategy" column="lod_strategy"/>
        <result property="name" column="name"/>
    </resultMap>

    <sql id="selectBaseVectorConfigurationVo">
        select id, name, type, source, refresh_interval, model_url, scale, rotation, lod_strategy from
        base_vector_configuration
    </sql>

    <select id="selectBaseVectorConfigurationList"
            parameterType="org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration"
            resultMap="BaseVectorConfigurationResult">
        <include refid="selectBaseVectorConfigurationVo"/>
        <where>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="source != null  and source != ''">and source = #{source}</if>
            <if test="refreshInterval != null  and refreshInterval != ''">and refresh_interval = #{refreshInterval}</if>
            <if test="modelUrl != null  and modelUrl != ''">and model_url = #{modelUrl}</if>
            <if test="scale != null  and scale != ''">and scale = #{scale}</if>
            <if test="rotation != null  and rotation != ''">and rotation = #{rotation}</if>
            <if test="lodStrategy != null  and lodStrategy != ''">and lod_strategy = #{lodStrategy}</if>
        </where>
    </select>

    <select id="selectBaseVectorConfigurationById" parameterType="String" resultMap="BaseVectorConfigurationResult">
        <include refid="selectBaseVectorConfigurationVo"/>
        where id = #{id}
    </select>

    <select id="selectAllBaseVectorConfiguration"
            resultType="org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration">
        <include refid="selectBaseVectorConfigurationVo"/>
    </select>

    <insert id="insertBaseVectorConfiguration"
            parameterType="org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration">
        insert into base_vector_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="source != null">source,</if>
            <if test="refreshInterval != null">refresh_interval,</if>
            <if test="modelUrl != null">model_url,</if>
            <if test="scale != null">scale,</if>
            <if test="rotation != null">rotation,</if>
            <if test="lodStrategy != null">lod_strategy,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="source != null">#{source},</if>
            <if test="refreshInterval != null">#{refreshInterval},</if>
            <if test="modelUrl != null">#{modelUrl},</if>
            <if test="scale != null">#{scale},</if>
            <if test="rotation != null">#{rotation},</if>
            <if test="lodStrategy != null">#{lodStrategy},</if>
        </trim>
    </insert>

    <update id="updateBaseVectorConfiguration"
            parameterType="org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration">
        update base_vector_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="source != null">source = #{source},</if>
            <if test="refreshInterval != null">refresh_interval = #{refreshInterval},</if>
            <if test="modelUrl != null">model_url = #{modelUrl},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="rotation != null">rotation = #{rotation},</if>
            <if test="lodStrategy != null">lod_strategy = #{lodStrategy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseVectorConfigurationById" parameterType="String">
        delete from base_vector_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseVectorConfigurationByIds" parameterType="String">
        delete from base_vector_configuration where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>