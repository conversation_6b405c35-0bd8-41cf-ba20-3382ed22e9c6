package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyPlan;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyPlanPageRequest;

@Mapper
public interface EmergencyPlanMapper extends BaseMapper<EmergencyPlan> {
    IPage<EmergencyPlan> findByPage(EmergencyPlanPageRequest request);

    boolean update(EmergencyPlan entity);

}
