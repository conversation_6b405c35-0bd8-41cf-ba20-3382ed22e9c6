package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoBiddingMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanySaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingSaveRequest;

import java.util.List;

@Service
public class SoBiddingServiceImpl implements SoBiddingService {
    @Autowired
    private SoBiddingMapper mapper;

    @Autowired
    private SoBiddingCompanyService biddingCompanyService;


    @Override
    public IPage<SoBidding> findAllConditional(SoBiddingPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public SoBidding save(SoBiddingSaveRequest entity) {
        entity.toUpdateModeOn(mapper.getIdByProjectCodeAndTenantId(entity.getProjectCode(), entity.tenantId()));
        SoBidding bidding = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);

        List<SoBiddingCompanySaveRequest> items = entity.getItems(bidding.getId());
        biddingCompanyService.removeAllByBiddingId(bidding.getId());
        biddingCompanyService.saveAll(items);
        return bidding;
    }

    @Override
    public boolean update(SoBidding entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
