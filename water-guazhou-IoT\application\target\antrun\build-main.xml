<?xml version="1.0" encoding="UTF-8" ?>
<project name="maven-antrun-" default="main"  >
<target name="main">
  <echo message="===== DEBUG PATH INFO ====="/>
  <echo message="project.basedir: D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\application"/>
  <echo message="project.parent.basedir: ${project.parent.basedir}"/>
  <echo message="user.dir: D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT"/>
  <echo message="basedir: D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\application"/>
  <echo message="Listing parent directory:"/>
  <exec failonerror="false" executable="ls">
    <arg value="-la"/>
    <arg value="${project.parent.basedir}"/>
  </exec>
  <echo message="Checking if gradle-2.13 exists:"/>
  <exec failonerror="false" executable="ls">
    <arg value="-la"/>
    <arg value="${project.parent.basedir}/gradle-2.13"/>
  </exec>
</target>
</project>