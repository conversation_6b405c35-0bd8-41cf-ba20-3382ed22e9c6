package org.thingsboard.server.dao.organization;

import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentSaveRequest;

import java.util.List;

public interface DepartmentService {
    /**
     * 分页条件查询部门
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    List<Department> findAllConditional(DepartmentPageRequest request);

    /**
     * 保存部门
     *
     * @param request 实体信息
     * @return 保存好的实体
     */
    Department save(DepartmentSaveRequest request);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(Department entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 树形查询，结构为 单位->部门
     *
     * @param id       根节点id
     * @param depth    深度
     * @param tenantId 客户id
     * @return 部门树
     */
    List<Department> findAllStructure(String id, Integer depth, String tenantId);

    /**
     * 是否允许删除
     *
     * @param id 唯一标识
     * @return 是否允许删除
     */
    boolean canBeDelete(String id);

    /**
     * 是否允许添加
     *
     * @param parentId 父级唯一标识
     * @return 是否允许删除
     */
    boolean canBeAdd(String parentId);

    List<Department> findByTenantId(String tenantId);

    Department findById(String id);
}
