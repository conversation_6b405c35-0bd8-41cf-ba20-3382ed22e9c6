<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionApplyMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        apply.id,
        apply.code,
        construction.code as construction_code,
        construction.name as construction_name,
        apply.contract_code,
        <!--@formatter:off-->
        (select name from so_construction_contract contract
          where construction_code = apply.construction_code
            and code = contract_code and tenant_id = apply.tenant_id)    as contract_name,
        (select type from so_construction_contract contract
         where construction_code = apply.construction_code
            and code = contract_code and tenant_id = apply.tenant_id)    as contract_type,
        (select name from so_general_type where id = (select type from so_construction_contract contract
         where construction_code = apply.construction_code
           and code = contract_code and tenant_id = apply.tenant_id))    as contract_type_name,
        <!--@formatter:on-->
        apply.begin_time,
        apply.end_time,
        apply.principal,
        apply.phone,
        apply.construct_class,
        apply.status,
        apply.remark,
        apply.creator,
        apply.create_time,
        info.status apply_status,
        construction.tenant_id<!--@sql from so_construction_apply apply, so_construction construction, so_construction_task_info info -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApply">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="contract_code" property="contractCode"/>
        <result column="contract_name" property="contractName"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_type_name" property="contractTypeName"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="principal" property="principal"/>
        <result column="phone" property="phone"/>
        <result column="construct_class" property="constructClass"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="SoConstructionExpenseContainerResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyContainer">
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="apply_status" property="status"/>
        <collection property="items" resultMap="BaseResultMap"/>
    </resultMap>

    <select id="findByPage" resultMap="SoConstructionExpenseContainerResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_APPLY"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_apply apply
                           on apply.construction_code = construction.code and
                              apply.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id
                               and info.scope = #{scope}
        where construction.code in (
        <include refid="pageInfoSubQuery"/>
        <if test="size > 0">
            offset #{offset} limit #{size}
        </if>
        )
          and construction.tenant_id = #{tenantId}
        order by construction.create_time desc
    </select>

    <select id="countByPage" resultType="long">
        select count(1) from (<include refid="pageInfoSubQuery"/>) a
    </select>

    <sql id="pageInfoSubQuery">
        select distinct construction.code
        from so_construction construction
                 left join so_construction_apply apply
                           on apply.construction_code = construction.code and
                              apply.tenant_id = construction.tenant_id
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
    </sql>

    <update id="update">
        update so_construction_apply
        <set>
            <!--            <if test="code != null">-->
            <!--                code = #{code},-->
            <!--            </if>-->
            <!--            <if test="constructionCode != null">-->
            <!--                construction_code = #{constructionCode},-->
            <!--            </if>-->
            <if test="contractCode != null">
                contract_code = #{contractCode},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="principal != null">
                principal = #{principal},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="constructClass != null">
                construct_class = #{constructClass},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_apply
        set contract_code   = #{contractCode},
            begin_time      = #{beginTime},
            end_time        = #{endTime},
            principal       = #{principal},
            phone           = #{phone},
            construct_class = #{constructClass},
            remark          = #{remark}
        <!--        code              = #{code},-->
        <!--            construction_code = #{constructionCode},-->
        where id = #{id}
    </update>

    <select id="isCodeExists" resultType="boolean">
        select count(1)
        from so_construction_apply outside
        where outside.code = #{code}
          and tenant_id = #{tenantId}
        <if test="id != null and id != ''">
            <!--需要存在，下方不存在是会返回null，null意味着false(不重复)，会出现假是自己(假不重复)-->
            and (select count(1) > 0 from so_construction_apply where id = #{id})
            <!--两个code相等则意味着是自己，不是自己则重复，是自己则放行(false-是自己所以返回未重复，true-不是自己所以返回重复)-->
            and (select not outside.code = code from so_construction_apply where id = #{id})
        </if>
    </select>

    <update id="markAsComplete">
        <bind name="completeStatus"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        <bind name="processingStatus"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@PROCESSING"/>
        update so_construction_apply
        set status = #{completeStatus}
        where id = #{id}
          and status = #{processingStatus}
    </update>


    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_apply
        where id = #{id}
    </select>

    <select id="canMarkGlobalAsComplete" resultType="boolean">
        <bind name="processStatus"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@PROCESSING"/>
        select count(1) = 0
        from so_construction_apply
        where so_construction_apply.status = #{processStatus}
          and construction_code = (select construction_code from so_construction_apply where id = #{id})
          and tenant_id = (select tenant_id from so_construction_apply where id = #{id})
    </select>
</mapper>