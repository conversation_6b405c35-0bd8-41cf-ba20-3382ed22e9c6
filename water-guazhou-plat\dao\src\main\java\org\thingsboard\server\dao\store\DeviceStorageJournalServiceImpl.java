package org.thingsboard.server.dao.store;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.store.*;
import org.thingsboard.server.dao.sql.department.DeviceStorageJournalMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceStorageJournalCheckOutRequest;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceStorageJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.RestDeviceInfoPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.RestDeviceStorageJournalPageRequest;

import java.util.Date;
import java.util.List;

@Service
public class DeviceStorageJournalServiceImpl implements DeviceStorageJournalService {
    @Autowired
    private DeviceStorageJournalMapper mapper;

    @Override
    public IPage<DeviceStorageJournalResponse> findAllConditional(DeviceStorageJournalPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public DeviceStorageJournal save(DeviceStorageJournal entity) {
        return null;
    }

    @Override
    public boolean update(DeviceStorageJournal entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<DeviceStorageJournal> saveAll(List<DeviceStorageJournal> journals) {
        return QueryUtil.saveOrUpdateBatch(journals, mapper::saveAll, mapper::updateAll);
    }

    @Override
    public boolean checkout(String id, String storeOutItemId) {
        return mapper.checkout(id, storeOutItemId);
    }

    @Override
    public boolean checkinViaStoreOut(String id) {
        if (id == null) {
            return true;
        }
        return mapper.checkinViaStoreOutItemId(id);
    }

    @Override
    public boolean checkinAllViaStoreOut(List<String> remove) {
        if (remove == null || remove.isEmpty()) {
            return true;
        }
        return mapper.checkinAllViaStoreOutItemId(remove) > 0;
    }

    @Override
    public IPage<MainRestDeviceStorageJournal> findRestConditional(RestDeviceStorageJournalPageRequest request) {
        return QueryUtil.pagify(request,
                (page, size) -> mapper.findRestByPage(page, size, request),
                () -> mapper.countRestCountByPage(request));
    }

    @Override
    public IPage<RestDeviceStorageJournal> findRestWithoutSplitConditional(RestDeviceStorageJournalPageRequest request) {
        return mapper.findRestWithoutSplitByPage(request);
    }

    @Override
    public IPage<RestDeviceStorageInfo> findRestDeviceInfoConditional(RestDeviceInfoPageRequest request) {
        return mapper.findRestDeviceInfoConditional(request);
    }

    @Override
    public DetailDeviceStorageInfo detail(String id) {
        return mapper.detail(id);
    }

    @Override
    public boolean updateScrappedTime(String deviceLabelCode, Date receiveTime, String tenantId) {
        return mapper.updateScrappedTime(deviceLabelCode, receiveTime, tenantId);
    }

    @Override
    public boolean canOperate(String storeOutId, String currentUserId) {
        return mapper.canOperate(storeOutId, currentUserId);
    }

    @Override
    public void collect(JSONObject params) {
        String id = params.getString("id");
        String isCollect = params.getString("isCollect");
        DeviceStorageJournal deviceStorageJournal = new DeviceStorageJournal();
        deviceStorageJournal.setId(id);
        deviceStorageJournal.setIsCollect(isCollect);
        mapper.updateById(deviceStorageJournal);
    }

    @Override
    public boolean checkoutAll(DeviceStorageJournalCheckOutRequest first) {
        for (String id : first.getCheckouts()) {
            mapper.checkoutViaStoreOut(id, first.getStoreOutId());
        }
        return true;
    }

}
