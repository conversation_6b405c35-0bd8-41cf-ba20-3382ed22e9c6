import{d as L,dM as $,M as G,r as R,c as j,a8 as I,o as O,bB as T,am as J,bu as W,j as K,u as q,bx as N,bv as H,m as X,C as Y,g as o,h as r,F as p,q as F,G as V,an as u,n as m,aJ as M,aB as v,bh as k,p as h,av as Q,H as Z,e4 as x,aK as ee,aL as ae,dz as le,dA as oe,c2 as se,f3 as de,eL as ne,bK as te,J as re,I as ue,K as ie,L as ce}from"./index-r0dFAfgr.js";import{u as pe}from"./index-t9hHXNDZ.js";import{a as me}from"./URLHelper-B9aplt5w.js";import"./index-BI1vGJja.js";const fe=L({name:"AddUpdateDialog",components:{ChooseUserByRole:$},props:{config:{type:Object,default(){return{}}},dialogWidth:{type:String,default:"460px"}},emits:["refreshData"],setup(a,d){const{$messageSuccess:C,$messageError:g,$format:P}=G(),n=R({imgActionUrl:"",fileActionUrl:"",uploadFileName:"",headers:{},zoom:11,center:[116.4,39.91],dataForm:{id:null}}),y=j(),U=I(()=>{const s={};return(a.config.columns||[]).forEach(i=>{s[i.key]=i.rules}),s}),E=()=>{var s;(s=y.value)==null||s.validate(async i=>{if(!i)return!1;let c="新增成功",f=a.config.addUrl;if(n.dataForm.id){if(!a.config.submit&&!a.config.editUrl){console.log("没有配置请求路径或方法"),a.config.close();return}c="修改成功",f=a.config.editUrl}else if(!a.config.submit&&!a.config.addUrl){a.config.close();return}const b={};let t=N({...n.dataForm});for(const e of a.config.columns)e.type==="location"&&e.replaceKey&&(t[e.replaceKey]=[...t.location],delete t.location),e.type==="date"&&(t[e.key]=P(t[e.key],"Y-M-D")),e.aInfo&&(e.replaceKey?(b[e.replaceKey]=t[e.replaceKey],delete t[e.replaceKey]):(b[e.key]=t[e.key],delete t[e.key]));if(t={...t,...a.config.externalParams||{}},Object.values(b).length>0&&(t.additionalInfo=b),a.config.setSubmitParams&&(t=a.config.setSubmitParams(t)),a.config.submit){const e=await a.config.submit(t);console.log(e),e.status===200?(C(c),a.config.close(),d.emit("refreshData")):g("请求失败")}else try{(await X({url:f,method:"post",data:t})).status===200?(C(c),a.config.close(),d.emit("refreshData")):g("请求失败")}catch(e){g(e.message||e.data.message)}})},B=(s,i,c)=>{console.log(s,"handleUploadSuccess"),n.dataForm[c]=s},A=s=>{const i=s.type==="image/jpeg"||s.type==="image/png",c=s.size/1024/1024<2;return i||g("上传图片只能是 JPG/PNG 格式!"),c||g("上传图片大小不能超过 2MB!"),i&&c},w=s=>(n.uploadFileName=s.name,!0);O(async()=>{if(T(()=>{var s;(s=y.value)==null||s.clearValidate()}),a.config.columns.some(s=>s.type==="location")){const s={events:{mapmove:f=>{const b=f.getCenter();n.dataForm.location=[b.lng,b.lat]}},search:{input:"innerAmapSearch",select:f=>{n.dataForm.location=f}}},{setMarker:i}=await pe("innerAmap",s),c=i(n.dataForm.location,{icon:me()});J(()=>n.dataForm.location,()=>{c.setPosition(n.dataForm.location)})}});const _=(s,i)=>{var c;n.dataForm[i]=s.map(f=>f.id).join(","),(c=y.value)==null||c.validate(async f=>{if(!f)return!1})};W(()=>{n.imgActionUrl=K().actionUrl+"/file/api/upload/image",n.fileActionUrl=K().actionUrl+"/file/api/upload/file",n.headers["X-Authorization"]="Bearer "+q().token,D()});const D=()=>{n.dataForm=N(a.config.defaultValue),n.dataForm.location&&(n.dataForm.location=[n.dataForm.location[0]-1,n.dataForm.location[1]-0]),a.config.open&&a.config.open()},S=I(()=>a.config.visible);return{...H(n),rules:U,submit:E,handleUploadSuccess:B,beforeAvatarUpload:A,beforeFileUpload:w,checkUsers:_,visible:S,ruleForm:y}}}),ye={key:0,class:"inputUnit",style:{"margin-right":"10px"}},be={key:0,class:"inputUnit",style:{"margin-right":"10px"}},ge={key:0,class:"inputUnit",style:{"margin-right":"10px"}},Fe=["src"],ke={key:1,class:"el-icon-plus avatar-uploader-icon"},he={key:11,class:"fileUpload"},Ue={key:0,class:"fileBox"},Ve=["onClick"],ve={key:13};function Ce(a,d,C,g,P,n){const y=Z,U=x,E=ee,B=ae,A=le,w=oe,_=se,D=de,S=ne,s=te,i=re,c=$,f=ue,b=ie,t=ce;return o(),r(t,{modelValue:a.visible,"onUpdate:modelValue":d[2]||(d[2]=e=>a.visible=e),width:a.dialogWidth||"460px",title:a.config.title||"","lock-scroll":!1,"close-on-click-modal":!1,"close-on-press-escape":!0,onClose:a.config.close},{footer:p(()=>[F(i,{size:"small",onClick:a.config.close},{default:p(()=>d[8]||(d[8]=[V(" 取 消 ")])),_:1},8,["onClick"]),a.config.disableSubmit?u("",!0):(o(),r(i,{key:0,size:"small",type:"primary",onClick:a.submit},{default:p(()=>d[9]||(d[9]=[V(" 保 存 ")])),_:1},8,["onClick"]))]),default:p(()=>[F(b,{ref:"ruleForm",rules:a.rules,"label-width":a.config.labelWidth||"120px","label-position":"top",model:a.dataForm,class:"dialogform addOrUpdateDialog"},{default:p(()=>[(o(!0),m(v,null,M(a.config.columns,e=>(o(),m(v,null,[e.type!=="none"?(o(),r(f,{key:e.key,label:e.label,prop:e.key},{default:p(()=>[e.type==="input"?(o(),r(y,{key:0,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,placeholder:"请输入"+e.label},{suffix:p(()=>[e.unit?(o(),m("i",ye,k(e.unit),1)):u("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])):u("",!0),e.type==="password"?(o(),r(y,{key:1,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,type:"password",disabled:e.disabled,placeholder:"请输入"+e.label},null,8,["modelValue","onUpdate:modelValue","disabled","placeholder"])):u("",!0),e.type==="input-number"?(o(),r(U,{key:2,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,placeholder:"请输入"},{suffix:p(()=>[e.unit?(o(),m("i",be,k(e.unit),1)):u("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])):e.type==="number"?(o(),r(y,{key:3,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,placeholder:e.label,onkeyup:"value=value.replace(/[^\\d]/g,'')",style:{width:"100%"}},{suffix:p(()=>[e.unit?(o(),m("i",ge,k(e.unit),1)):u("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])):e.type==="textarea"?(o(),r(y,{key:4,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,type:"textarea",resize:"none",rows:e.rows,placeholder:"请输入"+e.label},null,8,["modelValue","onUpdate:modelValue","disabled","rows","placeholder"])):e.type==="select"?(o(),r(B,{key:5,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,multiple:e.multiple,"multiple-limit":e.multipleLimit,"default-first-option":e.defaultFirst,"collapse-tags":"","allow-create":e.allowCreate,placeholder:"请选择"+e.label,style:{width:"100%"},filterable:e.search,onChange:e.handleChange},{default:p(()=>[(o(!0),m(v,null,M(e.options,l=>(o(),r(E,{key:l.value,value:l.value,label:l.label},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","multiple","multiple-limit","default-first-option","allow-create","placeholder","filterable","onChange"])):u("",!0),e.type==="radio"?(o(),r(w,{key:6,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,onChange:e.handleChange},{default:p(()=>[(o(!0),m(v,null,M(e.options,l=>(o(),r(A,{key:l.value,style:{"margin-bottom":"0"},label:l.value},{default:p(()=>[V(k(l.label),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])):u("",!0),e.type==="date"?(o(),r(_,{key:7,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,type:e.dateType,class:"date-picker-input","range-separator":"至","start-placeholder":"开始日期",placeholder:"请选择","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","disabled","type"])):e.type==="time"?(o(),r(D,{key:8,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,"is-range":e.range,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:e.format,"value-format":e.valFormat,placeholder:"请选择",style:{width:"100%"},onChange:e.handleChange},null,8,["modelValue","onUpdate:modelValue","disabled","is-range","format","value-format","onChange"])):u("",!0),e.type==="cascader"?(o(),r(S,{key:9,modelValue:a.dataForm[e.key],"onUpdate:modelValue":l=>a.dataForm[e.key]=l,disabled:e.disabled,options:e.options,clearable:"","collapse-tags":"",filterable:"",style:{width:"100%"},props:e.props,onChange:e.handleChange},null,8,["modelValue","onUpdate:modelValue","disabled","options","props","onChange"])):u("",!0),e.type==="image"?(o(),r(s,{key:10,class:"avatar-uploader",disabled:e.disabled,action:e.imgActionUrl||a.imgActionUrl,headers:e.headers||a.headers,"show-file-list":!1,"on-success":(l,z)=>a.handleUploadSuccess(l,z,e.key),"before-upload":a.beforeAvatarUpload},{default:p(()=>[a.dataForm[e.key]?(o(),m("img",{key:0,src:a.dataForm[e.key],class:"avatar"},null,8,Fe)):(o(),m("i",ke))]),_:2},1032,["disabled","action","headers","on-success","before-upload"])):u("",!0),e.type==="file"?(o(),m("div",he,[F(s,{class:"upload-demo",disabled:e.disabled,action:e.fileActionUrl||a.fileActionUrl,headers:e.headers||a.headers,"show-file-list":!1,"on-success":(l,z)=>a.handleUploadSuccess(l,z,e.key),"before-upload":a.beforeFileUpload},{default:p(()=>[F(i,{size:"small",type:"primary"},{default:p(()=>d[3]||(d[3]=[V(" 点击上传 ")])),_:1})]),_:2},1032,["disabled","action","headers","on-success","before-upload"]),a.dataForm[e.key]?(o(),m("div",Ue,[V(k(a.uploadFileName)+" ",1),h("span",{onClick:l=>a.dataForm[e.key]=""},"×",8,Ve)])):u("",!0)])):u("",!0),e.type==="userByRole"?(o(),r(c,{key:12,width:"100%",height:"48px",onCheckUsers:l=>a.checkUsers(l,e.key)},null,8,["onCheckUsers"])):u("",!0),e.type==="location"&&a.dataForm.location?(o(),m("div",ve,[d[4]||(d[4]=h("span",{class:"location-label"},"经度：",-1)),F(U,{modelValue:a.dataForm.location[0],"onUpdate:modelValue":d[0]||(d[0]=l=>a.dataForm.location[0]=l),disabled:e.disabled,precision:4,size:"small",class:"location-input"},null,8,["modelValue","disabled"]),d[5]||(d[5]=h("span",{class:"location-label margin-l-10"},"纬度：",-1)),F(U,{modelValue:a.dataForm.location[1],"onUpdate:modelValue":d[1]||(d[1]=l=>a.dataForm.location[1]=l),disabled:e.disabled,precision:4,size:"small",class:"location-input"},null,8,["modelValue","disabled"]),d[6]||(d[6]=h("p",{class:"message-text"}," 提示：请拖动地图 设置设备地图定位 ",-1)),d[7]||(d[7]=h("div",{class:"get-location-box amap-container"},[h("div",{id:"innerAmap",style:{height:"100%",width:"100%"}})],-1))])):u("",!0),e.message?(o(),m("p",{key:14,style:Q(e.messageStyle)},k(e.message),5)):u("",!0)]),_:2},1032,["label","prop"])):u("",!0)],64))),256))]),_:1},8,["rules","label-width","model"])]),_:1},8,["modelValue","width","title","onClose"])}const _e=Y(fe,[["render",Ce],["__scopeId","data-v-23ddd52c"]]);export{_e as default};
