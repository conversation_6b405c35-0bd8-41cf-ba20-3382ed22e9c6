package org.thingsboard.server.dao.construction.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeConstants;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.sql.smartOperation.construction.device.SoDeviceTypeMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypeSaveRequest;

import java.util.List;

@Service
public class SoDeviceTypeServiceImpl implements SoDeviceTypeService {
    @Autowired
    private SoDeviceTypeMapper mapper;

    @Override
    public IPage<SoDeviceType> findAllConditional(SoDeviceTypePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoDeviceType save(SoDeviceTypeSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);
    }

    @Override
    public boolean update(SoDeviceType entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public String getParentId(String id) {
        return mapper.getParentId(id);
    }

    @Override
    public boolean canBeDelete(String id, String tenantId) {
        return mapper.canBeDelete(id, tenantId);
    }

    @Override
    public List<SoDeviceType> findAllStructure(String tenantId) {
        SoDeviceType fakeRoot = DeviceTypeConstants.SO_TOP_ROOT;
        return QueryUtil.buildTreeWithFakeRoot(mapper.findRoots(tenantId), fakeRoot, mapper::findChildren);
    }

    @Override
    public Integer getDepth(String id) {
        if (id == null)
            return 0;

        return mapper.getDepth(id);
    }

    @Override
    public String getSerialId(String id) {
        return mapper.getSerialId(id);
    }

    @Override
    public boolean isSerialIdExists(String serialId, String id, String tenantId) {
        return mapper.isSerialIdExists(serialId, id, tenantId);
    }


    @Override
    public boolean existsBySerialId(String serialId, String tenantId) {
        return mapper.existsBySerialId(serialId, tenantId);
    }

    @Override
    public int getDepthBySerialId(String serialId, String tenantId) {
        return mapper.getDepthBySerialId(serialId, tenantId);
    }

    @Override
    public String getIdBySerialId(String serialId, String tenantId) {
        if (serialId == null) {
            return null;
        }
        return mapper.getIdBySerialId(serialId, tenantId);
    }
}
