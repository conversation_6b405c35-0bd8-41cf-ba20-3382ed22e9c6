/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.inputKv;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.inputKv.InputKv;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.inputKv.InputKvDao;
import org.thingsboard.server.dao.model.sql.InputKvEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
public class jpaInputKvDao extends JpaAbstractSearchTextDao<InputKvEntity, InputKv> implements InputKvDao {

    @Autowired
    private InputKvRepository inputKvRepository;

    @Override
    public List<InputKv> getInputKvByEntityId(String entityId) {
        return DaoUtil.convertDataList(inputKvRepository.findByEntityId(entityId));
    }

    @Override
    public List<InputKv> getInputKvByEntityIdAndTime(String entityId, long start, long end) {
        return DaoUtil.convertDataList(inputKvRepository.getInputKvByEntityIdAndTime(entityId,start,end));
    }

    @Override
    public void deleteInputKvByTime(String entityId,long startTime, long endTime) {
        inputKvRepository.deleteByTs(entityId,startTime,endTime);
    }

    @Override
    protected Class<InputKvEntity> getEntityClass() {
        return InputKvEntity.class;
    }

    @Override
    protected CrudRepository<InputKvEntity, String> getCrudRepository() {
        return inputKvRepository;
    }
}
