import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as k,c as _,r as d,b as i,S as q,o as v,g as L,n as B,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function V(l){return u({url:"/api/base/push/template/configuration/list",method:"get",params:l})}function F(l){return u({url:"/api/base/push/template/configuration/getDetail",method:"get",params:{id:l}})}function y(l){return u({url:"/api/base/push/template/configuration/add",method:"post",data:l})}function C(l){return u({url:"/api/base/push/template/configuration/edit",method:"post",data:l})}function z(l){return u({url:"/api/base/push/template/configuration/deleteIds",method:"delete",data:l})}const E={class:"wrapper"},I={__name:"basePushTemplateConfiguration",setup(l){const m=_(),c=_(),T=d({labelWidth:"100px",filters:[{type:"input",label:"模板名称",field:"name",placeholder:"请输入模板名称",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"新增",click:()=>h()},{perm:!0,type:"danger",text:"批量删除",click:()=>b()}]}],defaultParams:{}}),o=d({columns:[{label:"模板名称",prop:"name"},{label:"模板类型",prop:"type"},{label:"消息标题",prop:"title"},{label:"消息内容",prop:"content"},{label:"状态",prop:"status"},{label:"创建时间",prop:"createTime"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>x(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>h(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>b(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{o.pagination.page=e,n()},handleSize:e=>{o.pagination.limit=e,n()}},handleSelectChange:e=>{o.selectList=e||[]}}),a=d({title:"新增推送模板配置",group:[{fields:[{type:"input",label:"模板名称",field:"name",rules:[{required:!0,message:"请输入模板名称"}]},{type:"input",label:"模板类型",field:"type",rules:[{required:!0,message:"请输入模板类型"}]},{type:"input",label:"消息标题",field:"title",rules:[{required:!0,message:"请输入消息标题"}]},{type:"input",label:"消息内容",field:"content",rules:[{required:!0,message:"请输入消息内容"}]},{type:"input",label:"状态",field:"status",rules:[{required:!0,message:"请输入状态"}]},{type:"date",label:"创建时间",field:"createTime",rules:[{required:!0,message:"请选择创建时间"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await C(e),i.success("修改成功")):(await y(e),i.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{i.error("操作失败")}}}),f=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await C(e),i.success("修改成功")):(await y(e),i.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{i.error("操作失败")}}},x=async e=>{var t,r;try{const s=await F(e.id),p=((t=s.data)==null?void 0:t.data)||s;f(),a.title="推送模板配置详情",a.defaultValue={...p},a.group[0].fields.forEach(D=>{D.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=c.value)==null||r.openDialog()}catch{i.error("获取详情失败")}},h=e=>{var t;f(),e?(a.title="编辑推送模板配置",a.defaultValue={...e}):(a.title="新增推送模板配置",a.defaultValue={}),(t=c.value)==null||t.openDialog()},b=async e=>{try{const t=e?[e.id]:o.selectList.map(r=>r.id);if(!t.length){i.warning("请选择要删除的数据");return}await q("确定要删除选中的数据吗？"),await z(t),i.success("删除成功"),n()}catch(t){t!=="cancel"&&i.error("删除失败")}},n=async()=>{var e,t;try{const r=await V({page:o.pagination.page,size:o.pagination.limit,...((e=m.value)==null?void 0:e.queryParams)||{}}),s=((t=r.data)==null?void 0:t.data)||r;o.dataList=s.records||s,o.pagination.total=s.total||s.length||0}catch{i.error("数据加载失败")}};return v(()=>{n()}),(e,t)=>{const r=P,s=S,p=w;return L(),B("div",E,[g(r,{ref_key:"refSearch",ref:m,config:T},null,8,["config"]),g(s,{class:"card-table",config:o},null,8,["config"]),g(p,{ref_key:"refDialogForm",ref:c,config:a},null,8,["config"])])}}},G=k(I,[["__scopeId","data-v-993ea54d"]]);export{G as default};
