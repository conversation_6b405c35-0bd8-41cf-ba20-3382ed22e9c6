package org.thingsboard.server.dao.stationData.customization;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.AssayRecordFeignClient;
import org.thingsboard.server.dao.client.DeviceFeignClient;
import org.thingsboard.server.dao.client.MedicineManageFeignClient;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.stationData.StationDataService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PipeNetworkDataService {

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Autowired
    private MedicineManageFeignClient medicineManageFeignClient;

    @Autowired
    private AssayRecordFeignClient assayRecordFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;


    /**
     * 压力分布
     */
    public Object pressureDistribution(String projectId, String type, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "压力监测站,测流压站", projectId);
        List<StationEntity> stationList = stationPageData.getData();

        // 未查询到站点
        if (stationList ==null || stationList.isEmpty()) {
            return null;
        }

        // 查询数据
        Map<String, StationEntity> keyMap = new HashMap<>();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            // 压力数据key
            if (stationAttrList != null) {
                for (StationAttrEntity stationAttr : stationAttrList) {
                    if ("pressure".equals(stationAttr.getAttr())) {// 压力
                        keyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), station);
                    }
                }
            }
        }

        // 查询压力数据
        List<String> attrs = new ArrayList<>(keyMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> pressureDataMap
                = obtainDataService.getDeviceData(attrs, start, end, type, null, tenantId);

        // 收集数据
        List<JSONObject> resultList = new ArrayList<>();

        Map<String, Map<String, JSONObject>> collectDataMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : pressureDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                String attr = dataEntry.getKey();
                BigDecimal value = dataEntry.getValue();

                Map<String, JSONObject> map = new LinkedHashMap<>();
                if (collectDataMap.containsKey(attr)) {
                    map = collectDataMap.get(attr);
                }

                JSONObject obj = new JSONObject();
                obj.put("value", value);

                map.put(key, obj);

                collectDataMap.put(attr, map);
            }


        }

        Set<String> set = pressureDataMap.keySet();
        for (String key : set) {
            JSONObject resultObj = new JSONObject();
            resultObj.put("date", key);

            List<JSONObject> dataList = new ArrayList<>();
            for (String attr : attrs) {
                Map<String, JSONObject> map = collectDataMap.get(attr);
                StationEntity station = keyMap.get(attr);

                JSONObject value = map.get(key);
                value.put("location", station.getLocation());

                dataList.add(value);
            }
            resultObj.put("dataList", dataList);

            resultList.add(resultObj);
        }

        return resultList;
    }

    /**
     * 管网采样
     */
    public Object sampling(String stationId, Long start, Long end, TenantId tenantId) {
        JSONObject result = new JSONObject();
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        result.put("stationName", station.getName());
        // 查询
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(stationId);
        if (stationAttrList == null || stationAttrList.isEmpty()) {
            return null;
        }

        List<String> attrs = new ArrayList<>();
        for (StationAttrEntity stationAttr : stationAttrList) {
            attrs.add(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr());
        }
        Map<String, StationAttrEntity> stationAttrMap = stationAttrList
                .stream()
                .collect(Collectors
                        .toMap(stationAttr -> UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(),
                                stationAttr -> stationAttr));


        // 查询数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceOriginalData(attrs, start, end, null, tenantId);

        if (stationDataMap == null || stationAttrMap.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, List<BigDecimal>> attrDataList = attrs.stream().collect(Collectors.toMap(attr -> attr, attr -> new ArrayList<>()));

        // 数据进行分组
        // 对分组后的数据进行统计
        int bad = 0;
        int ok = 0;
        int total = 0;
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null && !dataMap.isEmpty()) {
                boolean isOK = false;
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    String key = dataEntry.getKey();
                    StationAttrEntity stationAttr = stationAttrMap.get(key);
                    if (stationAttr == null) {
                        continue;
                    }
                    // 达标范围
                    String range = stationAttr.getRange();
                    BigDecimal data = dataEntry.getValue();
                    if (StringUtils.isBlank(range)) {
                        continue;
                    }
                    String[] rangeArray = range.split(",");

                    if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                        if (data.doubleValue() > new BigDecimal(rangeArray[0]).doubleValue() && data.doubleValue() < new BigDecimal(rangeArray[1]).doubleValue()) {
                            // 达标
                            isOK = true;
                        }
                    }

                    if (StringUtils.isBlank(rangeArray[0]) && StringUtils.isNotBlank(rangeArray[1])) {
                        if (data.doubleValue() < new BigDecimal(rangeArray[1]).doubleValue()) {
                            // 达标
                            isOK = true;
                        }
                    }

                    if (StringUtils.isNotBlank(rangeArray[0]) && StringUtils.isBlank(rangeArray[1])) {
                        if (data.doubleValue() > new BigDecimal(rangeArray[0]).doubleValue()) {
                            // 达标
                            isOK = true;
                        }
                    }

                    if (!isOK) {
                        break;
                    }
                }

                if (isOK) {
                    ok++;
                } else {
                    bad++;
                }
                total++;
            }
        }

        result.put("bad", bad);
        result.put("ok", ok);
        result.put("total", total);
        result.put("okRate", BigDecimal.valueOf(ok).divide(BigDecimal.valueOf(total), 2, BigDecimal.ROUND_DOWN).multiply(new BigDecimal("100")));

        return result;
    }

}
