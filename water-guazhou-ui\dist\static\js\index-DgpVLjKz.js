import{d as F,M as R,c as d,s as W,x as c,r as f,a8 as o,bQ as U,S as z,ar as B,a9 as V,bT as j,D as O,o as Q,g as $,n as G,q as g,i as b,b6 as J,b7 as H}from"./index-r0dFAfgr.js";import{_ as K}from"./CardTable-rdWOL4_6.js";import{_ as X}from"./CardSearch-CB_HNR-Q.js";import{I as x}from"./common-CvK_P_ao.js";import{s as Z}from"./equipmentManage-DuoY00aj.js";import{a as ee,b as ae,c as te,e as le}from"./equipmentService-DanrK8F-.js";import{g as re}from"./equipmentInspection-Ci5RL-WV.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";import{h as ie}from"./ledgerManagement-CkhtRd8m.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ne={class:"wrapper"},he=F({__name:"index",setup(se){const{$btnPerms:m}=R(),i=d(),h=d(),D=d(),u=d(),q=d([]),Y=d({filters:[{label:"计划名称",field:"planName",type:"input"},{label:"班组名称",field:"teamName",type:"input"},{label:"保养人员名称",field:"userName",type:"input",labelWidth:"100px"},{label:"开始时间",field:"startEndTime",type:"daterange",format:"x"},{label:"结束时间",field:"endEndTime",type:"daterange",format:"x"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:x.QUERY,click:()=>p()},{type:"default",perm:!0,text:"重置",svgIcon:W(H),click:()=>{var e;(e=i.value)==null||e.resetForm(),p()}},{perm:m("RoleManageAdd"),text:"新增",type:"success",icon:x.ADD,click:()=>w()},{perm:m("RoleManageAdd"),text:"批量删除",type:"danger",icon:x.DELETE,click:()=>{var e;if(((e=n.selectList)==null?void 0:e.length)===0){c.warning("请选中至少一条数据");return}_()}}]}]}),n=f({defaultExpandAll:!0,indexVisible:!0,selectList:[],handleSelectChange:e=>{n.selectList=e},columns:[{label:"计划名称",prop:"name"},{label:"保养班组",prop:"teamName"},{label:"保养人员",prop:"userName"},{label:"开始时间",prop:"startTime",formatter:e=>L(e.startTime,"YYYY-MM-DD")},{label:"结束时间",prop:"endTime",formatter:e=>L(e.endTime,"YYYY-MM-DD")},{label:"消耗天数",prop:"executionDays"},{label:"间隔天数",prop:"intervalDays"},{label:"执行次数",prop:"executionNum"},{label:"备注",prop:"remark"},{label:"审核人姓名",prop:"reviewerName"},{label:"审核部门",prop:"reviewerDepartment"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTime",formatter:e=>L(e.createTime,"YYYY-MM-DD")}],operations:[{type:"primary",isTextBtn:!0,color:"#4195f0",text:"详情",perm:m("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>I(e)},{isTextBtn:!0,type:"danger",text:"删除",icon:"iconfont icon-shanchu",perm:m("RoleManageDelete"),click:e=>_(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{n.pagination.page=e,n.pagination.limit=a,p()}}}),v=f({title:"新增",labelWidth:"130px",submit:e=>{if(!e.maintainPlanCList||!e.maintainPlanCList.length){c.warning("请添加设备");return}e.maintainPlanCList.map(a=>a.mmDeviceId=a.serialId),ee(e).then(()=>{var a;p(),c.success("添加成功"),(a=h.value)==null||a.closeDrawer()})},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"计划名称",field:"name",rules:[{required:!0,message:"请输入计划名称"}]},{xl:8,type:"select",label:"班组",field:"teamId",rules:[{required:!0,message:"请选择班组"}],options:o(()=>t.teamList),onChange:e=>{const a=t.teamList.find(l=>l.value===e)||{};t.userList=a.maintainCircuitTeamCList.map(l=>({label:l.userName,value:l.userId}))}},{xl:8,type:"select",label:"人员名称",field:"userId",rules:[{required:!0,message:"请选择人员名称"}],options:o(()=>t.userList)},{xl:8,type:"date",label:"计划开始时间",field:"startTime",rules:[{required:!0,message:"请输入计划开始时间"}],format:"YYYY-MM-DD"},{xl:8,type:"number",label:"执行天数",field:"executionDays",min:0,rules:[{required:!0,message:"请输入执行天数"}]},{xl:8,type:"number",label:"间隔时间",field:"intervalDays",min:0,rules:[{required:!0,message:"请输入间隔时间"}]},{xl:8,type:"number",label:"执行次数",field:"executionNum",min:0,rules:[{required:!0,message:"请输入执行次数"}]},{xl:8,type:"department-user",label:"审核人员",field:"reviewer",rules:[{required:!0,message:"请选择审核人员"}]},{xl:16,type:"textarea",label:"备注",field:"remark"},{type:"table",field:"maintainPlanCList",config:{indexVisible:!0,height:"350px",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加设备",perm:!0,click:()=>{var e;(e=u.value)==null||e.openDrawer()}}]}]}],dataList:o(()=>t.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备款式",prop:"model"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"actualAddress"}],operations:[{type:"danger",text:"移除",icon:x.DELETE,perm:m("RoleManageDelete"),click:e=>t.selectList=t.selectList.filter(a=>a.id!==e.id)}],pagination:{hide:!0}}}]}]}),T=f({title:"详情",labelWidth:"130px",defaultValue:{},group:[{fields:[{disabled:!0,xl:8,type:"input",label:"计划名称",field:"name",rules:[{required:!0,message:"请输入计划名称"}]},{readonly:!0,xl:8,type:"select",label:"班组",field:"teamId",rules:[{required:!0,message:"请选择班组"}],options:o(()=>t.teamList)},{readonly:!0,xl:8,type:"select",label:"人员名称",field:"userName",rules:[{required:!0,message:"请选择人员名称"}],options:o(()=>t.userList)},{readonly:!0,xl:8,type:"date",label:"计划开始时间",field:"startTime",rules:[{required:!0,message:"请输入计划开始时间"}],format:"x"},{readonly:!0,xl:8,type:"input-number",label:"执行天数",field:"executionDays",rules:[{required:!0,message:"请输入执行天数"}]},{readonly:!0,xl:8,type:"input-number",label:"间隔时间",field:"intervalDays",rules:[{required:!0,message:"请输入间隔时间"}]},{readonly:!0,xl:8,type:"input-number",label:"执行次数",field:"executionNum",rules:[{required:!0,message:"请输入执行次数"}]},{readonly:!0,xl:8,type:"select-tree",label:"审核部门",field:"reviewerDepartment",checkStrictly:!0,options:o(()=>t.WaterSupplyTree),rules:[{required:!0,message:"请选择审核部门"}]},{disabled:!0,xl:8,type:"input",label:"审核人员",field:"reviewerName",rules:[{required:!0,message:"请选择审核人员"}]},{disabled:!0,xl:16,type:"textarea",label:"备注",field:"remark"},{type:"table",field:"maintainPlanCList",config:{indexVisible:!0,height:"350px",dataList:o(()=>t.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备款式",prop:"model"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"actualAddress"}],pagination:{hide:!0}}}]}]}),A=f({title:"设备选择",labelWidth:"130px",submit:(e,a)=>{var l;if(a){delete e.drive,t.getDevice(e);return}t.selectList=[...t.selectList,...q.value],t.selectList=U(t.selectList,"id"),(l=u.value)==null||l.closeDrawer()},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"标签编码",field:"deviceLabelCode"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{xl:8,label:"安装区域",field:"areaId",type:"select-tree",checkStrictly:!0,options:o(()=>t.installationArea)},{xl:12,type:"daterange",label:"上次保养时间",field:"lastMaintainanceTime"},{type:"table",field:"drive",config:{indexVisible:!0,height:"350px",selectList:[],handleSelectChange:e=>{q.value=e},dataList:o(()=>t.deviceValue),titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"查询",type:"success",perm:!0,click:()=>{var e;(e=u.value)==null||e.Submit(!0)}},{text:"重置",type:"primary",perm:!0,click:()=>{var e,a;(e=u.value)==null||e.resetForm(),(a=u.value)==null||a.Submit(!0)}}]}]}],columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"规格型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"actualAddress"},{label:"最后保养时间",prop:"lastMaintainanceTime"}],pagination:{hide:!0}}}]}]}),w=()=>{var e;v.title="新增",v.defaultValue={},t.selectList=[],(e=h.value)==null||e.openDrawer()},I=e=>{T.title="详情",ae(e.id).then(a=>{var r;const l=a.data.data||{};T.defaultValue={...l||{}},t.selectList=l.maintainPlanCList||[],(r=D.value)==null||r.openDrawer()}).catch(a=>{c.warning(a)})},_=e=>{z("确定删除该保养计划, 是否继续?","删除提示").then(()=>{var l;let a=[];e?a=[e.id]:a=((l=n==null?void 0:n.selectList)==null?void 0:l.map(r=>r.id))??[],te(a).then(()=>{p(),c.success("删除成功")})})},t=f({teamList:[],userList:[],WaterSupplyTree:[],departmentUser:[],deviceValue:[],installationArea:[],selectList:[],getTeam:()=>{re({size:99999,page:1,type:"保养班组",name:""}).then(a=>{const l=a.data.data.data||[];t.teamList=l.map(r=>({label:r.name,value:r.id,maintainCircuitTeamCList:r.maintainCircuitTeamCList}))})},getWaterSupplyTreeValue:()=>{B(2).then(a=>{t.WaterSupplyTree=V(a.data.data||[])})},getUserListValue:e=>{j({pid:e}).then(a=>{const l=a.data.data.data||[];t.departmentUser=l.map(r=>({label:r.firstName,value:O(r.id.id)}))})},getDevice:e=>{const a={size:99999,page:1,...e};a.lastMaintainanceTime&&a.lastMaintainanceTime.length>1&&(a.lastMaintainanceTimeFrom=a.lastMaintainanceTime[0],a.lastMaintainanceTimeTo=a.lastMaintainanceTime[1]),delete a.lastMaintainanceTime,ie(a).then(l=>{const r=l.data.data.data||[];let s=[];r.forEach(y=>{s=[...s,...y.restDeviceInfos]}),t.deviceValue=s})},getAreaTreeValue:()=>{Z({page:1,size:99999,shortName:""}).then(a=>{t.installationArea=V(a.data.data.data||[])})}}),p=async()=>{var a,l,r,s,y,S,C,M,E,k,P;const e={size:n.pagination.limit,page:n.pagination.page,startStartTime:"",startEndTime:[],endStartTime:"",endEndTime:[],...((a=i.value)==null?void 0:a.queryParams)||{}};(l=i.value)!=null&&l.queryParams&&((r=i.value)!=null&&r.queryParams.startEndTime)&&((s=i.value)==null?void 0:s.queryParams.startEndTime.length)>1&&(e.startStartTime=(y=i.value)==null?void 0:y.queryParams.startEndTime[0],e.startEndTime=(S=i.value)==null?void 0:S.queryParams.startEndTime[1]),(C=i.value)!=null&&C.queryParams&&((M=i.value)!=null&&M.queryParams.endEndTime)&&((E=i.value)==null?void 0:E.queryParams.endEndTime.length)>1&&(e.endStartTime=(k=i.value)==null?void 0:k.queryParams.endEndTime[0],e.endEndTime=(P=i.value)==null?void 0:P.queryParams.endEndTime[1]),le(e).then(N=>{n.dataList=N.data.data.data||[],n.pagination.total=N.data.data.total||0})};return Q(()=>{p(),t.getTeam(),t.getWaterSupplyTreeValue(),t.getDevice(),t.getAreaTreeValue()}),(e,a)=>{const l=X,r=K,s=J;return $(),G("div",ne,[g(l,{ref_key:"refSearch",ref:i,config:b(Y)},null,8,["config"]),g(r,{config:b(n),class:"card-table"},null,8,["config"]),g(s,{ref_key:"refForm",ref:h,config:b(v)},null,8,["config"]),g(s,{ref_key:"detailForm",ref:D,config:b(T)},null,8,["config"]),g(s,{ref_key:"refForm1",ref:u,config:b(A)},null,8,["config"])])}}});export{he as default};
