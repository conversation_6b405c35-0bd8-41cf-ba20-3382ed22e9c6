<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="telport"
      @refreshed="() => (state.curOperate = 'viewingDetail')"
      @refreshing="() => (state.curOperate = 'detailing')"
      @close="() => (state.curOperate = 'analysed')"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import Extent from '@arcgis/core/geometry/Extent.js'
import {
  createPictureMarker,
  excuteFind,
  initFindParams,
  excuteIdentify,
  excuteQuery,
  excuteQueryForIds,
  getGraphicLayer,
  getNeerestPoint,
  getPipeMapLayerMinIndex,
  getSubLayerIds,
  initIdentifyParams,
  initQueryParams,
  setSymbol,
  submitShutValvesGPJob,
  submitExtendShutValvesGPJob
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import { GetFieldConfig } from '@/api/mapservice/fieldconfig'
import PipeDetail from '../common/PipeDetail.vue'
import { useGisStore } from '@/store'
import { getShutValveAnalysJobLayers } from '@/api/mapservice/shutvalveanalys'
import burstPng from '../../../../assets/images/pipe/poi_burst.png'

const refForm = ref<IFormIns>()
const refDetail = ref<InstanceType<typeof PipeDetail>>()
const props = defineProps<{
  view?: __esri.MapView
  telport?: string
}>()
const state = reactive<{
  tabs: IPipeDetailTab[]
  curOperate:
    | 'picking'
    | 'picked'
    | 'analysing'
    | 'analysed'
    | 'extendAnalysing'
    | 'extendAnalysed'
    | 'detailing'
    | 'viewingDetail'
    | 'userDetailing'
    | 'viewingUser'
    | ''
}>({
  tabs: [],
  curOperate: ''
})
const staticState: {
  identifyResult?: any
  markLayer?: __esri.GraphicsLayer
  mapClick: any
  queryUrl: string
  queryParams: any
  analysUrl: string
  extendAnalysUrl: string
  shutPoint?: any
  jobid?: string
  resultLayer?: __esri.MapImageLayer
  valveFlag: boolean
  valveCode?: any
  resultSummary?: any
  deviceids: any[]
  devicelayerIndex: number
  devicelayers: string[]
  mustShutVolveFeatures: __esri.Graphic[]
  mustShutValveLayer?: __esri.GraphicsLayer
  extentMustShutValveLayer?: __esri.GraphicsLayer
  extendResultLayer?: __esri.MapImageLayer
} = {
  identifyResult: undefined,
  markLayer: undefined,
  mapClick: undefined,
  queryUrl: window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
  queryParams: initIdentifyParams(),
  analysUrl: window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisShutValveAnalysGPService,
  extendAnalysUrl: window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisExtendShutValveAnalysGPService,
  jobid: '',
  valveFlag: false,
  deviceids: [],
  devicelayerIndex: 0,
  devicelayers: ['阀门', '计量装置'],
  mustShutVolveFeatures: []
}

const TableConfig_Valve = reactive<ITable>({
  columns: [
    { label: '阀门类型', prop: 'layerName' },
    { label: '阀门编号', prop: 'value' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
})
const TableConfig_EffectRange = reactive<IAttrTable>({
  data: [],
  columns: []
})
const TableConfig_MustShut = reactive<ITable>({
  handleSelectChange: rows => {
    if (!props.view) return
    TableConfig_MustShut.selectList = rows || []
    const sids = rows.map(item => item.SID)
    const features = staticState.mustShutVolveFeatures
      .filter(item => sids.indexOf(item.attributes.SID) !== -1)
      .map(item => {
        item.symbol = setSymbol('point', {
          color: [0, 255, 255],
          outlineColor: [255, 0, 255],
          outlineWidth: 2
        })
        return item
      })
    staticState.mustShutValveLayer = getGraphicLayer(props.view, {
      id: 'extentMustShutValveLayer',
      title: '二次关阀'
    })
    staticState.mustShutValveLayer?.removeAll()
    staticState.mustShutValveLayer?.addMany(features)
  },
  dataList: [],
  columns: [
    { label: '编号', prop: 'SID' },
    { label: '阀门级别', prop: 'VALVECLASS' }
  ],
  pagination: {
    hide: true
  }
})
const TableConfig_User = reactive<ITable>({
  columns: [
    { prop: 'yhbh', label: '用户编号' },
    { prop: 'yhxm', label: '用户姓名' },
    { prop: 'yhdz', label: '用户地址' },
    { prop: 'lxdh', label: '联系电话' },
    { prop: 'dxdh', label: '短信电话' },
    { prop: 'ysxz', label: '用水性质' },
    { prop: 'vnum', label: '阀门编号' },
    { prop: 'sbbh', label: '水表编号' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
})
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '选取阀门'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              type: 'warning',
              text: () => (state.curOperate === 'picking'
                ? '正在选取阀门'
                : '点击选取阀门'),
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing'
                || state.curOperate === 'extendAnalysing'
                || state.curOperate === 'userDetailing',
              loading: () => state.curOperate === 'picking',
              click: () => pickValve()
            }
          ]
        },
        {
          type: 'table',
          style: {
            height: '80px'
          },
          config: TableConfig_Valve
        }
      ]
    },
    {
      fieldset: {
        desc: '执行分析'
      },
      fields: [
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: 0
          },
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              loading: () => state.curOperate === 'analysing',
              text: () => (state.curOperate === 'analysing' ? '正在分析' : '开始分析'),
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing'
                || state.curOperate === 'extendAnalysing'
                || state.curOperate === 'userDetailing'
                || !TableConfig_Valve.dataList.length,
              click: () => startAnalys()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析结果'
      },
      fields: [
        {
          label: '影响范围概览',
          type: 'checkbox',
          field: 'showInMap',
          options: [{ label: '地图显示', value: 'show' }],
          onChange: val => {
            staticState.resultLayer
              && (staticState.resultLayer.visible = !!val.length)
            staticState.extendResultLayer
              && (staticState.extendResultLayer.visible = !!val.length)
          }
        },
        {
          type: 'attr-table',
          style: {
            minHeight: '50px'
          },
          config: TableConfig_EffectRange
        },
        {
          type: 'table',
          label: '必关阀',
          style: {
            height: '250px'
          },
          config: TableConfig_MustShut
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '5px',
            marginTop: '15px'
          },
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              loading: () => state.curOperate === 'extendAnalysing',
              text: () => (state.curOperate === 'extendAnalysing'
                ? '正在分析'
                : '二次关阀分析'),
              disabled: () => !TableConfig_MustShut.selectList?.length,
              click: () => handleExtendAnalys()
            }
          ]
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '5px'
          },
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing'
                || state.curOperate === 'extendAnalysing'
                || state.curOperate === 'userDetailing'
                || !TableConfig_Valve.dataList.length,
              loading: () => state.curOperate === 'detailing',
              text: () => (state.curOperate === 'detailing' ? '正在查询' : '查看详细结果'),
              click: () => handleDetail()
            }
          ]
        },
        // {
        //   type: 'btn-group',
        //   itemContainerStyle: {
        //     marginBottom: '0'
        //   },
        //   btns: [
        //     {
        //       perm: true,
        //       styles: {
        //         width: '100%'
        //       },
        //       text: () => (state.curOperate === 'userDetailing'
        //         ? '正在查询'
        //         : '查看受影响用户'),
        //       loading: () => state.curOperate === 'userDetailing',
        //       disabled: () => state.curOperate === 'analysing'
        //         || state.curOperate === 'detailing'
        //         || state.curOperate === 'extendAnalysing'
        //         || state.curOperate === 'userDetailing'
        //         || !TableConfig_Valve.dataList.length,
        //       click: () => viewEffectedUser()
        //     }
        //   ]
        // },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              type: 'danger',
              text: '清除所有',
              disabled: () => state.curOperate === 'analysing'
                || state.curOperate === 'detailing'
                || state.curOperate === 'extendAnalysing'
                || state.curOperate === 'userDetailing',
              click: () => clear()
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    showInMap: ['show']
  }
})
const pickValve = () => {
  if (!props.view) return
  setMapCursor('crosshair')
  state.curOperate = 'picking'
  staticState.markLayer = getGraphicLayer(props.view, {
    id: 'shutvalve-analys',
    title: '关阀分析标注'
  })
  staticState.mapClick = props.view.on('click', e => {
    staticState.markLayer?.removeAll()
    startQuery(e)
  })
}

const startQuery = async (e: any) => {
  if (!props.view) return
  try {
    staticState.queryParams.layerIds = getSubLayerIds(
      props.view,
      undefined,
      undefined,
      '阀门'
    )
    staticState.queryParams.geometry = e.mapPoint
    staticState.queryParams.mapExtent = props.view.extent
    const res = await excuteIdentify(
      staticState.queryUrl,
      staticState.queryParams
    )
    props.view?.graphics.removeAll()
    const result = res.results?.filter(
      item => item.feature?.geometry?.type === 'point'
    )
    if (!result.length) {
      SLMessage.warning('没有查询到阀门')
      state.curOperate = 'picked'
      return
    }
    staticState.identifyResult = result && result[0]
    staticState.shutPoint = result && getNeerestPoint(result[0]?.feature?.geometry, e.mapPoint)
    const picturemark = staticState.shutPoint
      && createPictureMarker(staticState.shutPoint.x, staticState.shutPoint.y, {
        picUrl: burstPng,
        spatialReference: props.view?.spatialReference,
        yOffset: 8
      })
    const feature = result[0].feature

    feature && (feature.symbol = setSymbol(feature.geometry.type) as any)
    result.length && staticState.markLayer?.add(feature)
    staticState.markLayer?.add(picturemark)
    const formated = result.map(item => {
      return {
        layerName: item.layerName,
        layerId: item.layerId,
        value: item.feature.attributes?.OBJECTID,
        attributes: item.feature?.attributes
      }
    }) || []
    TableConfig_Valve.dataList = (formated.length && [formated[0]]) || []
    const extent = feature?.geometry.extent
    if (extent) {
      const width = extent.xmax - extent.xmin
      const height = extent.ymax - extent.ymin
      const xmin = extent.xmin - width / 2
      const xmax = extent.xmax + width / 2
      const ymin = extent.ymin - height / 2
      const ymax = extent.ymax + height / 2
      props.view?.goTo(
        new Extent({
          xmin,
          ymin,
          xmax,
          ymax,
          spatialReference: props.view.spatialReference
        })
      )
      setMapCursor('')
      staticState.mapClick?.remove && staticState.mapClick?.remove()
    }
  } catch (error) {
    props.view?.graphics.removeAll()
    // SLMessage.error('未查询到阀门信息，请重新尝试')
    state.curOperate = ''
    return
  }
  state.curOperate = 'picked'
}
const showAnalyzsSummary = () => {
  const summary = staticState.resultSummary
  if (summary?.layersummary?.length) {
    const summaryTData: any = {}
    const columns: IAttrTableRow[][] = []
    summary.layersummary.forEach(value => {
      summaryTData[value.layerdbname] = value.geometrytype === 'esriGeometryPoint'
        ? value.count + '个'
        : value.length + '米'
      columns.push([{ label: value.layername, prop: value.layerdbname }])
    })
    TableConfig_EffectRange.data = summaryTData
    TableConfig_EffectRange.columns = columns
  }
  let xmin: number = summary.xmin || props.view?.extent?.xmin
  let xmax: number = summary.xmax || props.view?.extent?.xmax
  let ymin: number = summary.ymin || props.view?.extent?.ymin
  let ymax: number = summary.ymax || props.view?.extent?.ymax
  const width = xmax - xmin
  const height = ymax - ymin
  xmin -= width / 2
  xmax += width / 2
  ymin -= height / 2
  ymax += height / 2
  props.view?.goTo(
    new Extent({
      xmin,
      ymin,
      xmax,
      ymax,
      spatialReference: props.view?.spatialReference
    })
  )
}
const startAnalys = async (isExtend?: boolean) => {
  state.curOperate = isExtend ? 'extendAnalysing' : 'analysing'
  try {
    staticState.resultLayer && props.view?.map.remove(staticState.resultLayer)
    staticState.extendResultLayer
      && props.view?.map.remove(staticState.extendResultLayer)
    const res = await queryLayerClassName(TableConfig_Valve.dataList[0].layerId)
    const datas = res.data?.result?.rows
    const layerdbName = datas?.length && datas[0].layerdbname
    const valveOid = staticState.identifyResult?.feature.attributes['OBJECTID']
    const jobinfo = await submitShutValvesGPJob({
      valvefeatureclassname: layerdbName,
      valveobjectid: valveOid,
      bysource: true,
      usertoken: useGisStore().gToken
    })
    await jobinfo.waitForJobCompletion()
    if (jobinfo.jobStatus === 'job-succeeded') {
      staticState.jobid = jobinfo.jobId

      const layer = await jobinfo.fetchResultMapImageLayer(jobinfo.jobId)
      staticState.resultLayer = layer
      staticState.resultLayer.title = '关阀分析结果'
      const pipeMapIndex = getPipeMapLayerMinIndex(props.view)
      props.view?.map.add(staticState.resultLayer, pipeMapIndex)

      const res = await jobinfo.fetchResultData('summary')

      const value: any = res.value
      if (value?.code !== 10000) {
        SLMessage.error(value.error)
      } else {
        staticState.resultSummary = value?.result?.summary
        showAnalyzsSummary()
      }
      // 查询必关阀
      state.tabs = staticState.resultSummary.layersummary.map(item => {
        return {
          label: item.layername,
          name: item.layername,
          data: []
        }
      })
      await setTabOids(state.tabs, 0)
      await getShutValves()
    } else if (jobinfo.jobStatus === 'job-cancelled') {
      SLMessage.info('已取消分析')
    } else if (jobinfo.jobStatus === 'job-cancelling') {
      SLMessage.info('任务正在取消')
    } else if (jobinfo.jobStatus === 'job-failed') {
      SLMessage.info('分析失败，请联系管理员')
    }
  } catch (error) {
    SLMessage.info('分析失败，请联系管理员')
    state.curOperate = 'picked'
    return
  }
  state.curOperate = 'analysed'
}
const setTabOids = async (tabs, index) => {
  if (index < tabs.length) {
    const tab = tabs[index]
    tab.data = await getTempOids(tab.name, 0)
    index < tabs.length - 1 && (await setTabOids(tabs, ++index))
  }
}
const getTempOids = async (tab: string, layerIndex: number) => {
  try {
    let alloids = await excuteQueryForIds(
      ((state.curOperate === 'extendAnalysing'
        ? staticState.extendResultLayer?.url
        : staticState.resultLayer?.url) || '')
        + '/'
        + layerIndex,
      initQueryParams({
        where: "layername='" + tab + "'",
        orderByFields: ['OBJECTID asc'],
        returnGeometry: false
      })
    )
    if (alloids === null) {
      alloids = await getTempOids(tab, ++layerIndex)
    }
    return alloids
  } catch (error) {
    return []
  }
}
const getShutValves = async () => {
  if (!props.view) return
  try {
    TableConfig_MustShut.loading = true
    const gpBaseUrl = parseUrl2Mapserver(
      (state.curOperate === 'extendAnalysing' && staticState.extendAnalysUrl)
        || staticState.analysUrl
    )
    const resultMaptUrl = gpBaseUrl + staticState.jobid
    const layerres = await getShutValveAnalysJobLayers(staticState.jobid)
    const valveLayers = layerres.data?.layers
      ?.filter(item => item.geometryType === 'esriGeometryPoint')
      .map(item => item.id) || []
    const res = await excuteFind(
      resultMaptUrl,
      initFindParams({
        layerIds: valveLayers || [0, 1],
        searchFields: ['mustshut'],
        returnGeometry: true,
        searchText: '1',
        contains: false
      })
    )
    const tabs: any[] = []
    const tableData: any[] = []
    res.results?.map(item => {
      if (!item.feature?.attributes) return
      const attributes = item.feature.attributes
      const tab = tabs.find(tab => tab.name === attributes.layername)
      if (tab) tab.data.push(attributes?.OBJECTID)
      else {
        const layername = attributes.layername
        tabs.push({
          label: layername,
          name: layername,
          id: item.layerId,
          data: [attributes.OBJECTID]
        })
      }
    })
    const tab = tabs.find(item => item.name === '阀门')
    if (!tab) {
      TableConfig_MustShut.dataList = []
      TableConfig_MustShut.loading = false
      return
    }
    const layerIndex = await getSubLayerIds(
      props.view,
      undefined,
      undefined,
      '阀门'
    )
    const layerId = tab?.id
    const outFields = await GetFieldConfig('阀门')
    const valves = await excuteQuery(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layerIndex[0],
      initQueryParams({
        objectIds: tab?.data,
        outFields:
          outFields.data?.result?.rows
            ?.filter(item => item.visible)
            .map(item => item.name) || [],
        returnGeometry: true
      })
    )
    valves.features?.map(item => {
      tableData.push({
        ...(item.attributes || {}),
        layerId
      })
    })
    staticState.mustShutVolveFeatures = valves.features
    TableConfig_MustShut.dataList = tableData
  } catch (error) {
    SLMessage.error('必关阀查询失败')
  }
  TableConfig_MustShut.loading = false
}

// const updateSelectedShutValve = async () => {
//   if (!props.view) return
//   // 定义图层
//   staticState.mustShutValveLayer = getGraphicLayer(props.view, {
//     id: 'burstAnalysisShutvalve',
//     title: '必关阀'
//   })
//   staticState.mustShutValveLayer?.removeAll()
//   if (!TableConfig_MustShut.selectList?.length) return
//   const layerIndex = TableConfig_MustShut.selectList[0]?.layerindex
//   const oids = TableConfig_MustShut.selectList.map(item => {
//     return item.OBJECTID
//   })
//   if (layerIndex === undefined) return

//   const url = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
//     + '/'
//     + layerIndex
//   const data = await excuteQuery(
//     url,
//     initQueryParams({
//       outFields: ['OBJECTID'],
//       objectIds: oids,
//       returnGeometry: true
//     })
//   )
//   if (!data.features?.length) return

//   for (let i = 0; i < data.features.length; i++) {
//     const feature = createGraphic({
//       geometry: data.features[i].geometry,
//       symbol: setSymbol('point')
//     })
//     staticState.mustShutValveLayer?.add(feature)
//   }
// }
const parseUrl2Mapserver = (url?: string) => {
  url = url || window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisBurstGPService
  const reg = /GPServer.+/
  return url.replace(reg, 'MapServer/jobs/')
}
const handleDetail = () => {
  state.curOperate = 'detailing'
  refDetail.value?.openDialog()
}
// const viewEffectedUser = () => {
//   state.curOperate = 'userDetailing'
// }
const handleExtendAnalys = async () => {
  state.curOperate = 'extendAnalysing'
  try {
    staticState.resultLayer && props.view?.map.remove(staticState.resultLayer)
    const extendMustShutValves = TableConfig_MustShut.selectList?.map(item => item.OBJECTID) || []
    await submitExtendAnalyzeTask('Valve', extendMustShutValves) // this.mustValve);
    state.curOperate = 'extendAnalysed'
  } catch (error) {
    state.curOperate = 'analysed'
  }
}
/**
 * (方法说明)必关阀二次分析任务
 * @method (方法名)
 * @for (所属类名)
 * @param {(参数类型)} (参数名) (参数说明)
 * @return {(返回值类型)} (返回值说明)
 */
const submitExtendAnalyzeTask = async (layerdbname, oids: any[]) => {
  const jobinfo = await submitExtendShutValvesGPJob({
    bysource: true,
    usertoken: useGisStore().gToken,
    valves: layerdbname + ':' + oids.join(',')
  })
  await jobinfo.waitForJobCompletion()
  if (jobinfo.jobStatus === 'job-succeeded') {
    staticState.jobid = jobinfo.jobId

    const layer = await jobinfo.fetchResultMapImageLayer(jobinfo.jobId)
    staticState.extendResultLayer = layer
    staticState.extendResultLayer.title = '二次关阀分析结果'
    const pipeMapIndex = getPipeMapLayerMinIndex(props.view)
    props.view?.map.add(staticState.extendResultLayer, pipeMapIndex)

    const res = await jobinfo.fetchResultData('summary')

    const value: any = res.value
    if (value?.code !== 10000) {
      SLMessage.error(value.error)
    } else {
      staticState.resultSummary = value?.result?.summary
      showAnalyzsSummary()
    }
    // const layers = await getLayers()
    // state.tabs = []
    // await getTab(layers, 0)
    // // 显示必关阀
    // await getShutValves()
    state.tabs = staticState.resultSummary.layersummary.map(item => {
      return {
        label: item.layername,
        name: item.layername,
        data: []
      }
    })
    await setTabOids(state.tabs, 0)
    await getShutValves()
  } else if (jobinfo.jobStatus === 'job-cancelled') {
    SLMessage.info('已取消分析')
  } else if (jobinfo.jobStatus === 'job-cancelling') {
    SLMessage.info('任务正在取消')
  } else if (jobinfo.jobStatus === 'job-failed') {
    SLMessage.info('分析失败，请联系管理员')
    state.curOperate = 'analysed'
  }
}

const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view)
}
const clear = () => {
  destroy()
  TableConfig_EffectRange.data = []
  TableConfig_MustShut.dataList = []
  TableConfig_Valve.dataList = []
  TableConfig_User.dataList = []
}
const setMapCursor = (type: string) => {
  const mapDiv = document.getElementById('viewDiv')
  mapDiv && (mapDiv.style.cursor = type)
}
const destroy = () => {
  setMapCursor('')
  staticState.resultLayer && props.view?.map.remove(staticState.resultLayer)
  staticState.markLayer && props.view?.map.remove(staticState.markLayer)
  staticState.mustShutValveLayer
    && props.view?.map.remove(staticState.mustShutValveLayer)
  staticState.extentMustShutValveLayer
    && props.view?.map.remove(staticState.extentMustShutValveLayer)
  staticState.mapClick?.remove && staticState.mapClick.remove()
}
onBeforeUnmount(() => {
  destroy()
  staticState.mapClick?.remove && staticState.mapClick?.remove()
  staticState.markLayer && props.view?.map.remove(staticState.markLayer)
})
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
