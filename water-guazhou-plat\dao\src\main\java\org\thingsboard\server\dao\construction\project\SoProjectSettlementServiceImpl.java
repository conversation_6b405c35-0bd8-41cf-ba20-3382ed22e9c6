package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectSettlement;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectSettlementMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementSaveRequest;

@Service
public class SoProjectSettlementServiceImpl implements SoProjectSettlementService {
    @Autowired
    private SoProjectSettlementMapper mapper;

    @Autowired
    private SoProjectOperateRecordService recordService;

    @Override
    public IPage<SoProjectSettlement> findAllConditional(SoProjectSettlementPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoProjectSettlement save(SoProjectSettlementSaveRequest entity) {
        entity.toUpdateModeOn(mapper.getIdByProjectCodeAndTenantId(entity.getProjectCode(), entity.tenantId()));
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            // recordService.save(SoProjectOperateRecordSaveRequest.of(entity, entity.getProjectCode(), "添加%s结算", "项目结算"));
            return mapper.save(e);
        }, mapper::updateFully);
    }

    @Override
    public boolean update(SoProjectSettlement entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
