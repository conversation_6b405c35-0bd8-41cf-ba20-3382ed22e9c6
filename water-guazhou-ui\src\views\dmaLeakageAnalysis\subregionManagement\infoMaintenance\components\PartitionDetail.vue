<template>
  <div class="partition-detail">
    <div class="form-box">
      <div class="form-scroll overlay-y">
        <Form
          ref="refForm"
          :config="FormConfig"
        ></Form>
      </div>
      <div class="form-footer">
        <el-button
          type="primary"
          @click="refForm?.Submit()"
        >
          保存
        </el-button>
        <el-button
          type="success"
          @click="viewMoreInfo"
        >
          分区资料
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <Tabs
        v-model="curTab"
        :config="TabConfig"
        @change="TabConfig.onChange"
      >
        <template #content>
          <FormTable :config="TableConfig"></FormTable>
        </template>
      </Tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  CopyMeterOptions,
  DMADirection,
  DMAStatusOptions,
  DMATypeOptions,
  GetPartitionDetail,
  IsMachineMeterOptions,
  CollectRateOptions,
  UserTypeOptions,
  ApartmentTypeOptions,
  TimeOptions,
  PostPartition
} from '@/api/mapservice/dma'
import { IFormIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { SLConfirm, SLMessage } from '@/utils/Message'

const emit = defineEmits(['edit', 'draw', 'success', 'more'])
const props = defineProps<{
  treeData?: NormalOption[]
  currentTreeNode?: NormalOption
}>()
const refForm = ref<IFormIns>()
const curTab = ref<string>('1')
const TabConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'border-card',
  fullHeight: true,
  tabs: [
    { label: '流量计', value: '1' },
    { label: '压力计', value: '2' },
    { label: '大用户', value: '3' }
  ],
  onChange: () => {
    refreshDevice()
  }
})
const handleHidden = (params, query, config) => {
  config.hidden = params.type !== '2'
}
const TableConfig = reactive<ITable>({
  dataList: [],
  indexVisible: true,
  columns: [
    { label: '名称', prop: 'name' },
    {
      label: '水流方向',
      prop: 'direction',
      tag: true,
      tagColor(row, val) {
        return val === '1' ? '#318DFF' : '#f56c6c'
      },
      formatter: (row, val) => DMADirection[val] || '- -'
    }
    // { label: '设备编号', prop: 'deviceId' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshDevice()
    }
  }
})
const partitions = usePartition()
const refreshDevice = async () => {
  if (!props.currentTreeNode) {
    TableConfig.dataList = []
    TableConfig.pagination.total = 0
    return
  }
  TableConfig.loading = true
  try {
    const type = curTab.value
    const device = await partitions.getDevices({
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      partitionId: props.currentTreeNode?.value,
      type
    })
    TableConfig.dataList = device.data || []
    TableConfig.pagination.total = device.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const FormConfig = reactive<IFormConfig>({
  labelWidth: 120,
  labelPosition: 'right',
  group: [
    {
      fields: [
        { lg: 12, xl: 12, type: 'input', label: '分区名称', field: 'name' },
        {
          lg: 12,
          xl: 12,
          type: 'select',
          label: '分区类型',
          field: 'type',
          clearable: false,
          options: DMATypeOptions
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'select',
          label: '分区状态',
          field: 'status',
          clearable: false,
          options: DMAStatusOptions
        },
        {
          lg: 12,
          xl: 12,
          type: 'input-number',
          label: '分区排序',
          field: 'orderNum'
        },
        {
          lg: 12,
          xl: 12,
          type: 'select',
          label: '分区抄表类型',
          field: 'copyMeterType',
          clearable: false,
          options: CopyMeterOptions
        },
        {
          lg: 12,
          xl: 12,
          type: 'select',
          label: '是否有机械表',
          field: 'isMachineMeter',
          clearable: false,
          options: IsMachineMeterOptions
        },
        {
          lg: 12,
          xl: 12,
          type: 'select',
          label: '采集频率',
          field: 'collectRate',
          options: CollectRateOptions,
          rules: [{ required: true, message: '请选择采集频率' }]
        },

        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'select',
          label: '小区类型',
          field: 'villageType',
          options: ApartmentTypeOptions
        },
        {
          lg: 12,
          xl: 12,
          type: 'select-tree',
          label: '所属分区',
          field: 'pid',
          checkStrictly: true,
          options: computed(() => props.treeData || []) as any,
          rules: [
            {
              validator(rule, value, callback) {
                if (value === refForm.value?.dataForm.id) {
                  callback(new Error('所属分区不能是自身ID'))
                } else {
                  callback()
                }
              }
            }
          ]
        },
        {
          lg: 12,
          xl: 12,
          type: 'select',
          label: '用户类型',
          field: 'userType',
          options: UserTypeOptions
        },
        {
          handleHidden(params, query, config) {
            config.hidden = params.type !== '1'
          },
          type: 'input',
          label: '分区范围',
          field: 'range'
        },

        {
          handleHidden,
          type: 'input',
          label: '小区地址',
          field: 'range'
        },
        {
          lg: 12,
          xl: 12,
          type: 'color-picker',
          label: '边框颜色',
          field: 'borderColor'
        },
        {
          lg: 12,
          xl: 12,
          type: 'color-picker',
          label: '区域颜色',
          field: 'rangeColor'
        },
        {
          type: 'btn-group',
          label: '分区绘制',
          btns: [
            { perm: true, text: '编辑', plain: true, click: () => editArea() },
            {
              perm: true,
              text: '重绘',
              plain: true,
              type: 'danger',
              click: () => redrawArea()
            }
          ]
        },
        {
          lg: 12,
          xl: 12,
          type: 'input-number',
          label: '供水面积',
          field: 'supplyWaterArea',
          suffix: 'k㎡',
          appendBtns: [{ perm: true, text: '计算', click: () => calcArea() }]
        },
        {
          lg: 12,
          xl: 12,
          type: 'input-number',
          label: '主管线长度',
          field: 'mainLineLength',
          suffix: 'km',
          appendBtns: [{ perm: true, text: '计算', click: () => calcLength() }]
        },

        {
          lg: 12,
          xl: 12,
          type: 'input',
          label: '抄表员',
          field: 'copyMeterUser'
        },
        {
          lg: 12,
          xl: 12,
          type: 'input',
          label: '负责人',
          field: 'director'
        },
        {
          lg: 12,
          xl: 12,
          type: 'input',
          disabled: true,
          label: '大用户数',
          suffix: '户',
          field: 'bigUserNum'
        },
        {
          lg: 12,
          xl: 12,
          type: 'input',
          label: '营收用户数',
          field: 'revenueUserNum',
          suffix: '户',
          disabled: true
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          label: '抄表周期',
          field: 'copyMeterCycle'
        },

        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          label: '入口数量',
          suffix: '个',
          field: 'inletNum'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          label: '小区入管口径',
          suffix: 'mm',
          field: 'inPipeCaliber'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input',
          label: '小区物业',
          field: 'propertyName',
          placeholder: '请输入物业公司'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'range',
          rangeType: 'select',
          label: '最小流量区间',
          options: TimeOptions,
          field: 'minFlowStartHour'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          label: '合法用水当量',
          field: 'legalUseWater'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          suffix: 'MPa',
          label: '平均压力',
          field: 'avgPressure'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'select',
          label: '启用产销差',
          field: 'enableNrw',
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ]
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          suffix: 'MPa',
          label: '最小合格压力值',
          field: 'minPassPressure'
        },
        {
          lg: 12,
          xl: 12,
          handleHidden,
          type: 'input-number',
          label: '最大合格压力值',
          suffix: 'MPa',
          field: 'maxPassPressure'
        },
        {
          handleHidden,
          type: 'textarea',
          label: '零压测试详情',
          field: 'zeroPressureTestDetail'
        },
        { type: 'textarea', label: '备注', field: 'remark' }
      ]
    }
  ],
  defaultValue: {
    ...(props.currentTreeNode || {})
  },
  submit: params => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        try {
          FormConfig.submitting = true
          const minFlowStartHour = params.minFlowStartHour?.[0]
          const minFlowEndHour = params.minFlowStartHour?.[1]
          const submitParams = {
            ...params,
            minFlowStartHour,
            minFlowEndHour
          }
          const res = await PostPartition(submitParams)
          if (res.data) {
            SLMessage.success('操作成功')
            emit('success')
          }
        } catch (error) {
          console.log(error)
          SLMessage.error('操作失败')
        }
        FormConfig.submitting = false
      })
      .catch(() => {
        //
      })
  }
})
const redrawArea = () => {
  emit('draw')
}
const editArea = () => {
  emit('edit', refForm.value?.dataForm.id)
}
const calcArea = () => {
  //
}
const calcLength = () => {
  //
}
const viewMoreInfo = () => {
  emit('more')
}
const refreshForm = async () => {
  const res = await GetPartitionDetail(props.currentTreeNode?.value)
  const data = res.data || {}
  data.minFlowStartHour = [data.minFlowStartHour, data.minFlowEndHour]
  refForm.value
    && (refForm.value.dataForm = {
      ...data
    })
}
const refreshData = async () => {
  refreshDevice()
  refreshForm()
}
watch(
  () => props.currentTreeNode?.value,
  () => {
    refreshData()
  }
)
defineExpose({
  refForm
})
</script>
<style lang="scss" scoped>
.partition-detail {
  height: 100%;
  width: 100%;
  display: flex;
  .form-box {
    width: 50%;
    .form-scroll {
      height: calc(100% - 40px);
      width: 100%;
      padding: 12px 15px 0 0;
    }
    .form-footer {
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .table-box {
    width: 50%;
    height: 100%;
  }
}
</style>
