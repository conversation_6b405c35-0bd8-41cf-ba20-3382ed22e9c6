package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionVisa implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 编号
    private String code;

    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    @TableField(exist = false)
    private String constructionName;

    // 预算人
    private String budgeter;

    // 施工单位
    private String constructOrganization;

    // 施工地点
    private String address;

    // 施工时间
    private Date constructTime;

    // 建设单位
    private String buildOrganization;

    // 监理单位
    private String supervisorOrganization;

    // 审计单位
    private String auditOrganization;

    // 原因
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
