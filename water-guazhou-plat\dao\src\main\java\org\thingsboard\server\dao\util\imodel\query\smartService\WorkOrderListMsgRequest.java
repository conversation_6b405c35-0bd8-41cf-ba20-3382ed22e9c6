package org.thingsboard.server.dao.util.imodel.query.smartService;

import lombok.Data;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderPageRequest;

@Data
public class WorkOrderListMsgRequest extends WorkOrderPageRequest {

    /**
     * 满意度
     * 1满意  2不满意 3问题未处理  4. 未评价 不回复的为满意
     */
    public String evaluate = "";


    /**
     * 发送状态
     * 1发送成功 2发送失败 3未发送
     */
    public String sendStatus = "";
}
