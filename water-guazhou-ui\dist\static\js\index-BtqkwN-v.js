import{d as Y,M,c as m,a8 as s,s as R,r as f,x as V,bQ as U,a9 as x,bT as z,D as F,o as O,g as G,n as W,q as g,i as h,b6 as B,b7 as A}from"./index-r0dFAfgr.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as Q}from"./CardSearch-CB_HNR-Q.js";import{I as D}from"./common-CvK_P_ao.js";import{p as $,c as N,g as J,b as K,a as X,d as Z}from"./equipmentOutStock-BiNkB8x8.js";import{h as ee}from"./ledgerManagement-CkhtRd8m.js";import{b as te,g as ae}from"./equipmentPurchase-KOqzaoYr.js";import{r as le,c as re}from"./equipmentManage-DuoY00aj.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";import{e as ie,f as P}from"./equipmentAssetsData-B4Olvyjd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./xlsx-rVJkW9yq.js";const oe={class:"wrapper"},De=Y({__name:"index",setup(se){const{$btnPerms:S}=M(),n=m(),b=m(),_=m(),u=m(),y=m([]),k=m({filters:[{label:"出库单编码",field:"code",type:"input",labelWidth:"90px"},{label:"出库单标题",field:"title",type:"input",labelWidth:"90px"},{label:"目标仓库",field:"storehouseId",type:"select",options:s(()=>l.storeList)},{label:"出库状态",field:"isOut",type:"select",options:ie},{label:"出库类型",field:"type",type:"select",options:P},{type:"select",label:"关联项目",field:"constructionProjectId",options:s(()=>l.ConstructionProject),onChange:()=>d(),formatter:(e,a,t)=>{var r,i;return((i=(r=t==null?void 0:t.options)==null?void 0:r.find(o=>o.value===e))==null?void 0:i.label)||e}},{type:"department-user",label:"领用人",field:"receiveUserId",formatter:(e,a,t)=>{var r,i;return((i=(r=t==null?void 0:t.options)==null?void 0:r.find(o=>o.value===e))==null?void 0:i.label)||e}},{type:"department-user",label:"经办人",field:"manager",formatter:(e,a,t)=>{var r,i;return((i=(r=t==null?void 0:t.options)==null?void 0:r.find(o=>o.value===e))==null?void 0:i.label)||e}},{type:"department-user",label:"出库人",field:"creator",formatter:(e,a,t)=>{var r,i;return((i=(r=t==null?void 0:t.options)==null?void 0:r.find(o=>o.value===e))==null?void 0:i.label)||e}},{label:"出库时间",field:"outtime",type:"daterange"},{label:"创建时间",field:"createtime",type:"daterange"},{type:"switch",label:"是否报账",field:"reimbursement"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:D.QUERY,click:()=>d()},{type:"default",perm:!0,text:"重置",svgIcon:R(A),click:()=>{var e;(e=n.value)==null||e.resetForm(),d()}},{type:"success",perm:!0,text:"新增",icon:D.ADD,click:()=>q()}]}]}),p=f({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"出库单编码",prop:"code"},{label:"出库单标题",prop:"title"},{label:"仓库名称",prop:"storehouseName"},{label:"出库时间",prop:"outTime",formatter:e=>L(e.outTime,"YYYY-MM-DD")||""},{label:"领用部门",prop:"managerDepartmentName"},{label:"领用人",prop:"receiveUserName"},{label:"经办人",prop:"managerName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>L(e.createTime,"YYYY-MM-DD")},{label:"项目名称",prop:"constructionProjectName"},{label:"出库状态",prop:"isOut",formatter:e=>e.isOut?"已出库":"未出库"},{label:"出库类型",prop:"type",formatter:e=>{var a;return(a=P.find(t=>t.value===e.type))==null?void 0:a.label}},{label:"是否报账",prop:"reimbursement",formItemConfig:{readonly:!0,type:"switch"}}],operationWidth:"160px",operations:[{disabled:e=>e.isOut,type:"success",text:"编辑",perm:S("RoleManageEdit"),icon:D.EDIT,click:e=>j(e)},{type:"primary",color:"#4195f0",text:"详情",perm:S("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>E(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{p.pagination.page=e,p.pagination.limit=a,d()}}}),c=f({title:"添加",labelWidth:"100px",width:"1100px",submit:(e,a)=>{var t;if(a)l.selectValue=e,l.getDevice(),(t=u.value)==null||t.openDrawer();else{if(e.items)for(const i in e.items){const o=e.items[i];if(o.count<o.num){V.warning("出库数量不应多余当前数量");return}if(o.num===0){V.warning("出库数量不能为0");return}}let r="添加成功";e.id&&(r="修改成功"),e.items&&e.items.map(i=>(i.deviceLabelId=i.id,i)),$(e).then(()=>{var i;d(),V.success(r),(i=b.value)==null||i.closeDrawer()})}},defaultValue:{},group:[{fields:[{disabled:!0,xl:8,type:"input",label:"出库单编码",field:"code",rules:[{required:!0,message:"请输入出库单编码"}]},{xl:8,type:"input",label:"出库单标题",field:"title",rules:[{required:!0,message:"请输入出库单标题"}]},{xl:8,type:"switch",label:"是否报账",field:"reimbursement"},{xl:8,type:"select",label:"目标仓库",field:"storehouseId",rules:[{required:!0,message:"请选择目标仓库"}],options:s(()=>l.storeList),onChange:e=>{l.getGoodsShelfValue(e)}},{xl:8,type:"department-user",label:"领用人",field:"receiveUserId",rules:[{required:!0,message:"请选择领用人"}]},{xl:8,type:"department-user",label:"经办人",field:"manager",rules:[{required:!0,message:"请选择经办人"}]},{xl:8,type:"select",label:"出库类型",field:"type",rules:[{required:!0,message:"请选择出库类型"}],options:[{label:"领用",value:"0"},{label:"安装",value:"1"},{label:"售出",value:"2"}]},{xl:8,type:"select",label:"关联项目",field:"constructionProjectId",options:s(()=>l.ConstructionProject)},{xl:8,type:"switch",label:"是否补录",field:"addRecord"},{xl:18,type:"textarea",label:"备注",field:"remark"},{type:"table",field:"items",config:{titleRight:[{style:{justifyContent:"flex-end",marginBottom:"10px"},items:[{type:"btn-group",btns:[{text:"增加设备",perm:!0,click:()=>{var e;(e=b.value)==null||e.Submit(!0)}}]}]}],indexVisible:!0,height:"350px",dataList:s(()=>l.selectList),columns:[{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"仓库",prop:"storehouseName"},{label:"货架",prop:"shelvesName"},{label:"当前数量",prop:"count"},{label:"出库数量",prop:"num",width:"140px",formItemConfig:{type:"number",min:0}},{label:"计算单位",prop:"unit"}],operationWidth:80,operations:[{text:"移除",type:"danger",icon:D.DELETE,perm:S("RoleManageDelete"),click:e=>{l.selectList=l.selectList.filter(a=>a.location!==e.location)}}],pagination:{hide:!0}}}]}]}),w=f({title:"设备选择",labelWidth:"130px",submit:(e,a)=>{var t;a?(delete e.device,l.getDevice(e)):(l.selectList=[...l.selectList,...y.value],l.selectList=U(l.selectList,["shelvesId","serialId"]),l.selectList.map((r,i)=>(r.location=i,r)),(t=u.value)==null||t.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"规格/型号",field:"model"},{xl:8,type:"select-tree",label:"货架",field:"shelvesId",checkStrictly:!0,options:s(()=>x(l.GoodsShelf))},{xl:8,type:"select-tree",label:"设备类别",field:"deviceTypeId",checkStrictly:!0,options:s(()=>x(l.DeviceType))},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:s(()=>l.deviceValue),selectList:[],handleSelectChange:e=>{y.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>{var e;(e=u.value)==null||e.Submit(!0)}},{text:"重置",perm:!0,click:()=>{var e,a;(e=u.value)==null||e.resetForm(),(a=u.value)==null||a.Submit(!0)}}]}]}],columns:[{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"仓库",prop:"storehouseName"},{label:"货架",prop:"shelvesName"},{label:"当前数量",prop:"count"},{label:"单位",prop:"unit"}],pagination:{hide:!0}}}]}]}),T=f({title:"详情",labelWidth:"100px",defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"出库单编码",field:"code",disabled:!0},{xl:8,type:"input",label:"出库单标题",field:"title",disabled:!0},{xl:8,type:"input",label:"仓库名称",field:"storehouseName",disabled:!0},{xl:8,type:"input",label:"出库时间",field:"outTime",disabled:!0},{xl:8,type:"input",label:"领用部门",field:"receiveUserDepartmentName",disabled:!0},{xl:8,type:"input",label:"领用人",field:"receiveUserName",disabled:!0},{xl:8,type:"input",label:"经办部门",field:"managerDepartmentName",disabled:!0},{xl:8,type:"input",label:"经办人",field:"managerName",disabled:!0},{xl:8,type:"input",label:"关联项目",field:"constructionProjectName",disabled:!0},{xl:8,type:"select",label:"出库类型",field:"type",rules:[{required:!0,message:"请选择出库类型"}],options:[{label:"领用",value:"0"},{label:"安装",value:"1"},{label:"售出",value:"2"}],readonly:!0},{xl:8,type:"switch",label:"是否报账",field:"reimbursement",readonly:!0},{xl:8,type:"input",label:"创建人",field:"creatorName",disabled:!0},{xl:8,type:"input",label:"创建时间",field:"createTime",disabled:!0},{xl:16,type:"textarea",label:"备注",field:"remark",disabled:!0},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:s(()=>l.selectList),columns:[{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"出库数量",prop:"num"},{label:"单位",prop:"unit"}],pagination:{hide:!0}}}]}]}),q=()=>{var e;c.title="新建出库单",l.selectList=[],y.value=[],c.defaultValue={code:"CK"+L(new Date,"YYYYMMDDHHmmss")},(e=b.value)==null||e.openDrawer()},E=e=>{var r;const a={...e};for(const i in a)(a[i]===void 0||a[i]===null)&&(a[i]=" ");c.title="出库单详情",T.defaultValue={...a||{}},(r=_.value)==null||r.openDrawer();const t={page:1,size:99999,mainId:a.id};N(t).then(i=>{l.selectList=i.data.data.data||[]})},j=e=>{var t;c.title="编辑出库单",l.selectList=[],y.value=[],l.selectValue=e,l.getGoodsShelfValue(e.storehouseId),c.defaultValue={...e||{}},(t=b.value)==null||t.openDrawer();const a={page:1,size:99999,mainId:e.id};N(a).then(r=>{const i=x(r.data.data.data||[],"children",{storehouseName:"storeName"});l.selectList=i.map((o,v)=>(o.location=v,o))})},l=f({UserList:[],DevicePurchase:[],storeList:[],selectValue:{},deviceValue:[],total:0,selectList:[],SupplierList:[],contract:[],GoodsShelf:[],ConstructionProject:[],DeviceType:[],getDevice:e=>{const a={size:99999,page:1,storeId:l.selectValue.storehouseId||"",...e};ee(a).then(t=>{l.deviceValue=t.data.data.data||[]})},getUserListValue:e=>{z({pid:e}).then(a=>{const t=a.data.data.data||[];l.UserList=t.map(r=>({label:r.firstName,value:F(r.id.id)}))})},getDevicePurchaseValue:()=>{te({page:1,size:99999}).then(a=>{const t=a.data.data.data||[];l.DevicePurchase=t.map(r=>({label:r.title,value:r.id}))})},getstoreSerchValue:()=>{J({page:1,size:99999}).then(a=>{const t=a.data.data.data||[];l.storeList=t.map(r=>({label:r.name,value:r.id}))})},getSupplierValue:()=>{le({page:1,size:99999}).then(a=>{const t=a.data.data.data||[];l.SupplierList=t.map(r=>({label:r.name,value:r.id}))})},getGoodsShelfValue:e=>{K({page:1,size:99999,id:e}).then(t=>{l.GoodsShelf=t.data.data.data[0].children||[]})},getDeviceapiContractDetailValue:()=>{ae({page:1,size:99999}).then(a=>{const t=a.data.data.data||[];l.contract=t.map(r=>({label:r.title,value:r.id}))})},getConstructionProjectValue:()=>{X({page:1,size:99999}).then(a=>{l.ConstructionProject=x(a.data.data.data||[])})},getDeviceTypeTreeValue:()=>{re().then(e=>{l.DeviceType=e.data.data||[]})},init:()=>{l.getDevicePurchaseValue(),l.getstoreSerchValue(),l.getSupplierValue(),l.getDeviceapiContractDetailValue(),l.getConstructionProjectValue(),l.getDeviceTypeTreeValue()}}),d=async()=>{var a,t,r,i,o,v,C;const e={size:p.pagination.limit,page:p.pagination.page,...((a=n.value)==null?void 0:a.queryParams)||{}};e.createtime&&((t=e.createtime)==null?void 0:t.length)>1&&(e.fromTime=((r=n.value)==null?void 0:r.queryParams).createtime[0]||"",e.toTime=((i=n.value)==null?void 0:i.queryParams).createtime[1]||""),e.outtime&&((o=e.outtime)==null?void 0:o.length)>1&&(e.outTimeFrom=((v=n.value)==null?void 0:v.queryParams).outtime[0]||"",e.outTimeTo=((C=n.value)==null?void 0:C.queryParams).outtime[1]||""),delete e.createtime,delete e.outtime,Z(e).then(I=>{p.dataList=I.data.data.data||[],p.pagination.total=I.data.data.total||0})};return O(()=>{d(),l.init()}),(e,a)=>{const t=Q,r=H,i=B;return G(),W("div",oe,[g(t,{ref_key:"refSearch",ref:n,config:h(k)},null,8,["config"]),g(r,{config:h(p),class:"card-table"},null,8,["config"]),g(i,{ref_key:"refForm",ref:b,config:h(c)},null,8,["config"]),g(i,{ref_key:"refFormEquipment",ref:u,config:h(w)},null,8,["config"]),g(i,{ref_key:"detailForm",ref:_,config:h(T)},null,8,["config"])])}}});export{De as default};
