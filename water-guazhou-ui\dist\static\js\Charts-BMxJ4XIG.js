import{d as l,c as n,a6 as f,o as _,Q as u,ay as v,g as m,n as d,q as C,C as h}from"./index-r0dFAfgr.js";const k=l({__name:"Charts",props:{option:{}},setup(a,{expose:c}){const i=a,e=n(),t=n(),r=f();_(()=>{e.value&&r.listenTo(e.value,s)}),u(()=>{e.value&&r.removeAllListeners(e.value)});function s(){var o;(o=t.value)==null||o.resize()}return c({resize:s}),(o,x)=>{const p=v("VChart");return m(),d("div",{ref_key:"refDiv",ref:e,class:"chartview"},[C(p,{ref_key:"refChart",ref:t,option:i.option},null,8,["option"])],512)}}}),y=h(k,[["__scopeId","data-v-0968d893"]]);export{y as default};
