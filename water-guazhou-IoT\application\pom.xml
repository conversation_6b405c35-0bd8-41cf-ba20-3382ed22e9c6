 <!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.thingsboard</groupId>
        <version>2.3.0</version>
        <artifactId>thingsboard</artifactId>
    </parent>
    <artifactId>application</artifactId>
    <packaging>jar</packaging>

    <name>ThingsBoard Server Application</name>
    <url>https://thingsboard.io</url>
    <description>Open-source IoT Platform - Device management, data collection, processing and visualization
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <main.dir>${basedir}/..</main.dir>
        <pkg.name>thingsboard</pkg.name>
        <pkg.unixLogFolder>/var/log/${pkg.name}</pkg.unixLogFolder>
        <pkg.installFolder>/usr/share/${pkg.name}</pkg.installFolder>
        <pkg.win.dist>${project.build.directory}/windows</pkg.win.dist>
    </properties>

    <dependencies>
        <dependency>
            <groupId>de.ruedigermoeller</groupId>
            <artifactId>fst</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport-native-epoll</artifactId>
            <version>${netty.version}</version>
            <!-- Explicitly bring in the linux classifier, test may fail on 32-bit linux -->
            <classifier>linux-x86_64</classifier>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.thingsboard.common.transport</groupId>-->
<!--            <artifactId>transport-api</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.thingsboard.common.transport</groupId>-->
<!--            <artifactId>mqtt</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.thingsboard.common.transport</groupId>-->
<!--            <artifactId>http</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.thingsboard.common.transport</groupId>-->
<!--            <artifactId>coap</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.thingsboard</groupId>
            <artifactId>dao</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thingsboard.common</groupId>
            <artifactId>queue</artifactId>
        </dependency>
     <dependency>
            <groupId>org.thingsboard</groupId>
            <artifactId>dao</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.takari.junit</groupId>
            <artifactId>takari-cpsuite</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.cassandraunit</groupId>
            <artifactId>cassandra-unit</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.thingsboard</groupId>-->
<!--            <artifactId>ui</artifactId>-->
<!--            <version>${project.version}</version>-->
<!--            <scope>runtime</scope>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-actor_${scala.version}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-slf4j_${scala.version}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
        </dependency>
        <!-- 公式计算 see also https://github.com/killme2008/aviator-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

        <!--短信 -->
        <dependency>
            <groupId>com.yunpian.sdk</groupId>
            <artifactId>yunpian-java-sdk</artifactId>
        </dependency>
        <!--influxDB -->
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>influxdb-client-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>influxdb-client-flux</artifactId>
        </dependency>


        <!-- cloud -->
        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-eureka-server -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-eureka</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.winsw</groupId>
            <artifactId>winsw</artifactId>
            <classifier>bin</classifier>
            <type>exe</type>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.thingsboard</groupId>
            <artifactId>tools</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path-assert</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.dbunit</groupId>
            <artifactId>dbunit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.springtestdbunit</groupId>
            <artifactId>spring-test-dbunit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hsqldb</groupId>
            <artifactId>hsqldb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.javadelight</groupId>
            <artifactId>delight-nashorn-sandbox</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox.ui</groupId>
            <artifactId>springfox-swagger-ui-rfc6570</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.0</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.12.2</version>
        </dependency>

        <!-- freemarker jar -->
        <dependency>
           <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.23</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${pkg.name}-${project.version}</finalName>
        <resources>
            <resource>
                <directory>${project.basedir}/src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surfire.version}</version>
                <configuration>
                    <systemPropertyVariables>
                        <spring.config.name>thingsboard</spring.config.name>
                    </systemPropertyVariables>
                    <includes>
                        <include>**/*TestSuite.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <excludes>
                                        <exclude>logback.xml</exclude>
                                    </excludes>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-service-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/conf</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/unix.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-win-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.win.dist}/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <excludes>
                                        <exclude>logback.xml</exclude>
                                    </excludes>
                                    <filtering>false</filtering>
                                </resource>
                                <resource>
                                    <directory>src/main/conf</directory>
                                    <excludes>
                                        <exclude>thingsboard.conf</exclude>
                                    </excludes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/windows.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-control</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/control</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/control</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/unix.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-install</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/bin/install</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/install</directory>
                                    <includes>
                                        <include>**/*.sh</include>
                                        <include>**/*.xml</include>
                                    </includes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/unix.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-windows-control</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.win.dist}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/windows</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/windows.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-windows-install</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${pkg.win.dist}/install</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts/install</directory>
                                    <includes>
                                        <include>logback.xml</include>
                                    </includes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                            <filters>
                                <filter>src/main/filters/windows.properties</filter>
                            </filters>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-data</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/data</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/data</directory>
                                </resource>
                                <resource>
                                    <directory>../dao/src/main/resources</directory>
                                    <includes>
                                        <include>**/*.cql</include>
                                        <include>**/*.sql</include>
                                    </includes>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-winsw-service</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.sun.winsw</groupId>
                                    <artifactId>winsw</artifactId>
                                    <classifier>bin</classifier>
                                    <type>exe</type>
                                    <destFileName>service.exe</destFileName>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>${pkg.win.dist}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/logback.xml</exclude>
                    </excludes>
                    <archive>
                        <manifestEntries>
                            <Implementation-Title>ThingsBoard</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>org.thingsboard.server.ThingsboardServerApplication</mainClass>
                    <classifier>boot</classifier>
                    <layout>ZIP</layout>
                    <executable>true</executable>
                    <excludeDevtools>true</excludeDevtools>
                    <embeddedLaunchScriptProperties>
                        <confFolder>${pkg.installFolder}/conf</confFolder>
                        <logFolder>${pkg.unixLogFolder}</logFolder>
                        <logFilename>${pkg.name}.out</logFilename>
                        <initInfoProvides>${pkg.name}</initInfoProvides>
                    </embeddedLaunchScriptProperties>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>debug-paths</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <echo message="===== DEBUG PATH INFO =====" />
                                <echo message="project.basedir: ${project.basedir}" />
                                <echo message="project.parent.basedir: ${project.parent.basedir}" />
                                <echo message="user.dir: ${user.dir}" />
                                <echo message="basedir: ${basedir}" />
                                <echo message="Listing parent directory:" />
                                <exec executable="ls" failonerror="false">
                                    <arg value="-la" />
                                    <arg value="${project.parent.basedir}" />
                                </exec>
                                <echo message="Checking if gradle-2.13 exists:" />
                                <exec executable="ls" failonerror="false">
                                    <arg value="-la" />
                                    <arg value="${project.parent.basedir}/gradle-2.13" />
                                </exec>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.fortasoft</groupId>
                <artifactId>gradle-maven-plugin</artifactId>
                <configuration>
                    <gradleVersion>2.13</gradleVersion>
                    <gradleInstallationDir>${project.parent.basedir}/gradle-2.13</gradleInstallationDir>
                    <tasks>
                        <task>build</task>
                        <task>buildDeb</task>
                        <task>buildRpm</task>
                    </tasks>
                    <args>
                        <arg>-PprojectBuildDir=${project.build.directory}</arg>
                        <arg>-PprojectVersion=${project.version}</arg>
                        <arg>-PmainJar=${project.build.directory}/${project.build.finalName}-boot.${project.packaging}
                        </arg>
                        <arg>-PpkgName=${pkg.name}</arg>
                        <arg>-PpkgInstallFolder=${pkg.installFolder}</arg>
                        <arg>-PpkgLogFolder=${pkg.unixLogFolder}</arg>
                    </args>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>invoke</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>${pkg.name}</finalName>
                    <descriptors>
                        <descriptor>src/main/assembly/windows.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <file>${project.build.directory}/${pkg.name}.deb</file>
                    <artifactId>${project.artifactId}</artifactId>
                    <groupId>${project.groupId}</groupId>
                    <version>${project.version}</version>
                    <classifier>deb</classifier>
                    <packaging>deb</packaging>
                </configuration>
                <executions>
                    <execution>
                        <id>install-deb</id>
                        <phase>package</phase>
                        <goals>
                            <goal>install-file</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>jenkins</id>
            <name>Jenkins Repository</name>
            <url>http://repo.jenkins-ci.org/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
