/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.telemetryAttribute;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;

@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class RequestTs {
    private Long start;
    private ArrayList<HashMap<String, Object>> queries;
    private Long end;
    private String delete ;
    private String resolveNames ;


    public RequestTs(Long start, Long end, ArrayList<HashMap<String, Object>> queries) {
        this.start = start;
        this.end = end;
        this.queries = queries;
    }

    public RequestTs(Long start, Long end, ArrayList<HashMap<String, Object>> queries, String delete) {
        this.start = start;
        this.end = end;
        this.queries = queries;
        this.delete = delete;
    }

    public RequestTs(ArrayList<HashMap<String, Object>> queries, String resolveNames) {
        this.queries = queries;
        this.resolveNames = resolveNames;

    }
}
