package org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SMMaintainTaskItemSaveRequest extends SaveRequest<SMMaintainTaskItem> {
    // 任务id
    private String taskId;

    // 设备在gis服务中的标识
    @NotNullOrEmpty
    private String objectId;

    // 图片
    private String img;

    // 音频
    private String audio;

    // 视频
    private String video;

    // 文件
    private String file;

    // 备注
    private String remark;

    @Override
    protected SMMaintainTaskItem build() {
        SMMaintainTaskItem entity = new SMMaintainTaskItem();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SMMaintainTaskItem update(String id) {
        disallowUpdate();
        SMMaintainTaskItem entity = new SMMaintainTaskItem();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SMMaintainTaskItem entity) {
        entity.setTaskId(taskId);
        entity.setObjectId(objectId);
        entity.setImg(img);
        entity.setAudio(audio);
        entity.setVideo(video);
        entity.setFile(file);
        entity.setRemark(remark);
    }
}