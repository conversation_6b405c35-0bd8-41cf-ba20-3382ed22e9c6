package org.thingsboard.server.service.task.job;

import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.service.statistics.StatisticsProcessService;
import org.thingsboard.server.service.utils.SpringContextUtils;

/**
 * <AUTHOR>
 * @date 2020/4/15 11:04
 */
@DisallowConcurrentExecution
@Slf4j
public class StatisticsJob implements Job {


    private StatisticsProcessService statisticsService = (StatisticsProcessService) SpringContextUtils.getBean("statisticsProcessService");

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        DataSourceEntity dataSourceEntity = (DataSourceEntity) jobExecutionContext.getJobDetail().getJobDataMap().get(DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_VALUE);
        String originatorId = (String) jobExecutionContext.getJobDetail().getJobDataMap().get("originatorId");
        String originatorType = (String) jobExecutionContext.getJobDetail().getJobDataMap().get("originatorType");
        boolean isOrigin = (boolean) jobExecutionContext.getJobDetail().getJobDataMap().get("isOrigin");
        switch (DataSourceType.valueOf(dataSourceEntity.getType())) {
            case PREPARATION_SOURCE: {
                switch (originatorType) {
                    case DataConstants.ROOT: {
                        try {
                            statisticsService.statisticsRootPreparation(dataSourceEntity,isOrigin);
                        }catch (Exception e){
                            log.info("处理数据统计出错");
                        }
                        break;
                    }
                    case DataConstants.TENANT:{
                        try {
                            statisticsService.statisticsTenantPreparation(dataSourceEntity,originatorId,isOrigin);
                        }catch (Exception e){
                            log.info("处理数据统计出错");
                        }
                        break;
                    }
                    case DataConstants.PROJECT:{
                        try {
                            statisticsService.statisticsProjectPreparation(dataSourceEntity,originatorId,isOrigin);
                        }catch (Exception e){
                            log.info("处理数据统计出错");
                        }
                        break;
                    }
                    default: {
                        break;
                    }
                }
                break;
            }
            case STATISTICS_SOURCE: {
                try {
                    statisticsService.processStatistics(dataSourceEntity,isOrigin);
                }catch (Exception e){
                    log.info("处理数据统计出错");
                }
                break;
            }
            default:
                break;
        }
    }


}
