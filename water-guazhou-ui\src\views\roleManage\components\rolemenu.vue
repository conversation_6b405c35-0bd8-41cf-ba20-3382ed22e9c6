<template>
  <el-dialog
    v-model="state.visible"
    title="授权角色的菜单"
    width="60%"
    :close-on-click-modal="false"
    @close="props.tableConfig?.close"
  >
    <el-card>
      <div class="app-auth-title">
        应用授权
      </div>
      <el-checkbox-group v-model="state.selectedApps">
        <el-checkbox
          v-for="(item, i) in state.apps"
          :key="i"
          :label="item.id"
          @change="handleAppChange(item)"
        >
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-card>
    <el-tabs
      v-model="state.app"
      tab-position="left"
      style="height: 500px; margin-top: 20px"
      @tab-click="handleTabClick"
    >
      <el-tab-pane
        v-for="(item, i) in state.selectedAppItems"
        :key="i"
        :label="item.name"
        :name="item.id"
      >
        <div
          v-loading="state.loading"
          class="content-box"
        >
          <el-scrollbar
            v-if="state.data?.length > 0"
            height="500px"
          >
            <el-tree
              :ref="
                el=>{
                  menuTree[item.id]=el
                }
              "
              :default-checked-keys="userChange[state.app]"
              :data="state.data"
              class="menu-tree"
              show-checkbox
              node-key="id"
              :default-expand-all="true"
              @check="change"
            ></el-tree>
          </el-scrollbar>
          <el-empty
            v-else
            :image-size="150"
            description="该应用暂不支持菜单授权"
          ></el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button
          size="small"
          type="primary"
          @click="clickSaveMenuToRole"
        >保存</el-button>
        <el-button
          size="small"
          @click="clearMenuTree"
        >清空</el-button>
        <el-button
          size="small"
          @click="props.tableConfig.close"
        >取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import Cookies from 'js-cookie'
import { onMounted, reactive, watch } from 'vue'
import {
  GetRoleMenus,
  saveRoleMenus
} from '@/api/menu'
import { removeSlash } from '@/utils/removeIdSlash'
import {
  getapplications,
  getRoleTenantApplicationList,
  assignTenantApplicationToRole
} from '@/api/portal'
import { GetApplicationAsyncRoutes } from '@/api/menu/source'
import { SLMessage } from '@/utils/Message'

const props = defineProps<{
  tableConfig: any
}>()
const menuTree = ref<any>([])
const state = reactive<{
  userdata: any[]
  data: any[]
  selectNodes: any[]
  enterpriseoptions: any[]
  enterprise: string
  appoptions: any[]
  app: string
  apps: any[]
  selectedApps: any[]
  selectedAppItems: any[]
  visible: boolean
  loading: boolean
}>({
  userdata: [],
  data: [],
  selectNodes: [],
  enterpriseoptions: [],
  enterprise: '',
  appoptions: [],
  app: '',
  apps: [],
  selectedApps: [],
  selectedAppItems: [],
  visible: true,
  loading: false
})

// 用户选中的菜单
const userChange = ref({})

watch(
  () => props.tableConfig.visible,
  (newVal: any) => {
    state.visible = newVal
  }
)

onMounted(() => {
  state.appoptions = []
  state.app = ''
  state.apps = []
  state.selectedApps = []
  state.selectedAppItems = []
  getapplication().then(() => {
    getCureemtApps()
  })
})
const handleTabClick = async (tab, event) => {
  console.log(tab, event)
  userChange.value[state.app] = menuTree.value[state.app]?.getCheckedKeys()
  state.app = tab.props.name || ''
  await nextTick()
  getApplicationTree(state.app)
}
const handleAppChange = async item => {
  // const tenantId = removeSlash(Cookies.get('tenantId') || '')
  const roleId = removeSlash(props.tableConfig.roleId)
  await assignTenantApplicationToRole({ roleId, tenantApplicationId: item.id })
  await getCureemtApps()
}
const getCureemtApps = async () => {
  // const tenantId = removeSlash(Cookies.get('tenantId') || '')
  const roleId = removeSlash(props.tableConfig.roleId)

  const res = await getRoleTenantApplicationList(roleId)
  state.selectedApps = res.data
  state.selectedAppItems = state.apps.filter(item => {
    return state.selectedApps.indexOf(item.id) !== -1
  })
  userChange.value = {}
  state.selectedAppItems.forEach(item => {
    if (!(item in userChange.value)) {
      userChange.value[item] = []
    }
  })
  // state.selectedAppItems = res.data?.map(item => state.apps.find(obj => obj.id === item)) || []

  if (state.selectedAppItems.length > 0) {
    state.app = state.selectedAppItems[0]?.id || ''
    getApplicationTree(state.app)
  }
}
const change = () => {
  // node
  // console.log(this.$refs['menuTree' + this.app].getCheckedKeys())
}
// const _getTreeByRoleId = () => {
//   getTreeByRoleId(props.tableConfig.roleId).then(res => {
//     state.selectNodes = res.data
//     menuTree.value?.setCheckedKeys(state.selectNodes)
//   })
// }
const clickSaveMenuToRole = () => {
  // props.tableConfig.close()
  // const params = {
  //   roleId: props.tableConfig.roleId,
  //   menuIds: menuTree.value[state.app].getCheckedKeys(),
  //   tenantApplicationId: state.app
  // }
  userChange.value[state.app] = menuTree.value[state.app].getCheckedKeys()
  saveRoleMenus({
    roleId: removeSlash(props.tableConfig.roleId),
    menus: [...userChange.value[state.app]],
    tenantApplicationId: state.app
  })
    .then(() => {
      SLMessage.success('操作成功')
    })
    .catch(() => {
      SLMessage.error('保存失败')
    })
}

// 获取应用列表
const getapplication = async () => {
  const tenantId = removeSlash(Cookies.get('tenantId') || '')
  const res = await getapplications(tenantId, 'ALL')
  if (res.status === 200) {
    res.data.forEach(element => {
      state.appoptions.push({ label: element.name, value: element.id })
      state.apps.push(element)
    })
  }
}

// 获取当前应用的菜单
const getApplicationTree = async appId => {
  if (appId) state.app = appId
  await nextTick()
  state.loading = true
  GetApplicationAsyncRoutes(state.app)
    .then(res => {
      console.log(res)
      state.data = traverse(res.data, 'children', { label: 'meta.title' })
      // 获取当前应用拥有的菜单
      getuserTree()
    })
    .finally(() => {
      state.loading = false
    })
}

// 获取用户当前的路由
const getuserTree = () => {
  GetRoleMenus({
    roleId: removeSlash(props.tableConfig.roleId),
    tenantApplicationId: state.app
  }).then(res => {
    if (!state.app) return
    userChange.value[state.app] = res.data.data
  })
}
// 清空选择的节点
const clearMenuTree = () => {
  menuTree.value[state.app]?.setCheckedKeys([])
}

// 树结构转换
function traverse(
  val,
  children: 'children' | string = 'children',
  keys: { label: 'name'; value: 'id' } | any = { label: 'name', value: 'id' }
) {
  val.map(obj => {
    if (obj) {
      for (const i in keys) {
        obj[i] = obj.meta.title
      }
      if (obj[children] && obj[children].length) {
        traverse(obj[children], children, keys)
      }
    }
    return obj
  })
  return val
}
</script>

<style lang="scss">
.alarm-design {
  // .el-tree-node__content {
  //   &:hover {
  //     background: #373b4e 100% !important;
  //   }
  // }
  // .el-tree-node.is-current {
  //   > .el-tree-node__content {
  //     background: #373b4e 100% !important;
  //   }

  //   .el-tree-node__content {
  //     &:hover {
  //       background: #373b4e 100% !important;
  //     }
  //   }

  //   .el-tree-node__children {
  //     .el-tree-node__content {
  //       &:hover {
  //         background: #373b4e 100% !important;
  //       }
  //     }
  //   }
  // }
}
</style>
<style lang="scss" scoped>
:deep(.el-tab-pane) {
  height: 100%;
}
.content-box {
  height: 100%;
}
.menu-tree {
  margin-top: 20px;
  width: calc(100% - 20px);
}
:deep(.el-tabs__nav-wrap) {
  &.is-left {
    padding: 0;
  }
  .el-tabs__nav-scroll {
    .el-tabs__nav {
      .el-tabs__item {
        text-align: right;
      }
    }
  }
}
.app-auth-title {
  color: #409eff;
  margin: 5px 0;
}
:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: center;
  .el-checkbox {
    flex-basis: 220px;
  }
}
// .el-tabs {
//   background-color: #222536 !important;
// }
:deep(.el-tabs__content) {
  width: calc(100% - 250px) !important;
  height: 100% !important;
}

// :deep(.el-card__body) {
//   background: #383d51 !important;
// }
</style>
