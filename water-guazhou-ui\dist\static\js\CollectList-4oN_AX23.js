import{_ as G}from"./ArcLayout-CHnHL9Pv.js";import{_ as O}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as J}from"./CardSearch-CB_HNR-Q.js";import{d as K,u as Q,c as n,r as d,bE as X,D as M,b as s,S as D,o as Y,g as f,h as q,F as Z,p as g,bo as tt,bR as et,i,q as m,aw as rt,n as A,bh as N,an as C,cs as ot,C as at}from"./index-r0dFAfgr.js";import{a as it,F as st,E as x,C as lt}from"./CollectDetail-CZJXrerj.js";import{D as pt,G as nt,P as ct,a as mt}from"./usePipeCollect-DNAtT5mx.js";import{f as ut}from"./DateFormatter-Bm9a68Ax.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./detail-CU6-qhMl.js";import"./index-CpGhZCTT.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const dt={class:"collect-pages"},ft={class:"page-table"},gt={key:0,class:"page-detail"},_t={class:"detail-header"},bt={key:0},ht=K({__name:"CollectList",setup(yt){const w=Q(),u=n(),_=n(),T=d({filters:[{type:"radio-button",label:"状态",field:"status",options:[{label:"全部",value:"all"},...it]},{type:"input",label:"任务名称",field:"name"},{type:"input",label:"任务编号",field:"code"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"搜索",click:()=>l()},{perm:!0,text:"重置",click:()=>V()},{perm:!0,text:"新建任务",type:"success",click:()=>E()}]}],defaultParams:{status:"all"}}),o=d({columns:[{prop:"name",label:"任务名称"},{prop:"code",label:"任务编号"},{prop:"type",label:"采集类型"},{prop:"creatorName",label:"创建人"},{prop:"createTime",label:"创建时间",formatter(t,e){return ut(e,X)}},{prop:"processUserName",label:"接收人"},{prop:"status",label:"状态",formatter(t,e){return st(e)??""}},{prop:"remark",label:"任务备描述"}],dataList:[],operationWidth:180,operations:[{perm:!0,text:"详情",click:t=>I(t)},{perm:t=>!t.processUser||!t.status,text:"派发",click:t=>k(t)},{perm:t=>{var e,r;return t.processUser===M((r=(e=w.user)==null?void 0:e.id)==null?void 0:r.id)&&[x.待接收,x.处理中,x.审核退回].includes(t.status)},text:"转发",click:t=>k(t)},{perm:t=>{var e,r;return t.creator===M((r=(e=w.user)==null?void 0:e.id)==null?void 0:r.id)},text:"删除",type:"danger",click:t=>B(t)}],pagination:{refreshData:({page:t,size:e})=>{o.pagination.page=t||1,o.pagination.limit=e||20,l()}}}),V=()=>{var t;(t=_.value)==null||t.resetForm()},W=t=>{var e,r;o.currentRow.status=t,c.value="table",(r=(e=u.value)==null?void 0:e.refPanel)==null||r.toggleMaxMin("normal"),l()},B=async t=>{var r;const e=t?[t.id]:((r=o.selectList)==null?void 0:r.map(a=>a.id))||[];if(!e.length){s.warning("请选择要删除的数据");return}D("确定删除？","提示信息").then(async()=>{try{await pt(e),s.success("删除成功"),l(),l()}catch(a){s.error("删除失败"),console.log(a)}}).catch(()=>{})},l=async()=>{var t,e,r;try{const a={...((t=_.value)==null?void 0:t.queryParams)||{},page:o.pagination.page??1,size:o.pagination.limit??20},p=await nt({...a,status:a.status==="all"?void 0:a.status});o.dataList=((e=p.data.data)==null?void 0:e.data)||[],o.pagination.total=((r=p.data.data)==null?void 0:r.total)||0}catch(a){console.log(a)}},b=n(),h=d({title:"创建项目",dialogWidth:550,group:[{fields:[{label:"任务名称",field:"name",type:"input",rules:[{required:!0,message:"请输入任务名称"}]},{label:"采集类型",field:"type",type:"radio",options:[{label:"点",value:"点"},{label:"线",value:"线"}]},{label:"备注",field:"remark",type:"textarea"}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{type:"点"},submit:t=>{D("确定提交？","提示信息").then(async()=>{var e,r;h.submitting=!0;try{(await ct({...t,processUser:(e=t.processUser)==null?void 0:e.join(",")})).data.code===200?(s.success("提交成功"),(r=b.value)==null||r.closeDialog(),l()):s.error("提交失败")}catch(a){console.log(a),s.error("提交失败")}h.submitting=!1}).catch(()=>{})}}),y=n(),v=d({dialogWidth:550,title:"采集任务指派",group:[{fields:[{type:"user-select",label:"指派到",field:"processUser",rules:[{required:!0,message:"请选择处理人",trigger:"change"}],departField:"processUserDepartmentId"},{type:"user-select",label:"审核人",rules:[{trigger:"change",required:!0,message:"请选择审核人"}],field:"reviewUser",departField:"processUserDepartmentId"}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{},submit:t=>{D("确定派发此任务？","提示信息").then(async()=>{var e,r,a;v.submitting=!0;try{(await mt({...t,id:o.currentRow.id,processUser:(e=t.processUser)==null?void 0:e.join(","),reviewUser:(r=t.reviewUser)==null?void 0:r.join(",")})).data.code===200?(s.success("派发成功"),l(),(a=y.value)==null||a.closeDialog()):s.error("派发失败")}catch(p){console.log(p),s.error("派发失败")}v.submitting=!1}).catch(()=>{})}}),k=t=>{var e;o.currentRow=t,(e=y.value)==null||e.openDialog()},E=()=>{var t;(t=b.value)==null||t.openDialog()},c=n("table"),I=t=>{o.currentRow=t,c.value="detail"},j=()=>{c.value="table"},P=n("normal"),$=t=>{P.value=t};return Y(()=>{var t,e;l(),(e=(t=u.value)==null?void 0:t.refPanel)==null||e.toggleMaxMin("max")}),(t,e)=>{const r=J,a=H,p=O,z=G;return f(),q(z,{ref_key:"refArcLayout",ref:u,"panel-default-visible":!0,"panel-max-min":!0,"hide-panel-close":!0,"panel-dragable":!1,"panel-default-maxmin":"max","panel-maxmin-changed":$,"panel-title":"采集任务列表"},{"detail-default":Z(()=>{var S,U,L;return[g("div",dt,[tt(g("div",ft,[m(r,{ref_key:"refSearch",ref:_,config:i(T)},null,8,["config"]),m(a,{config:i(o),class:rt(["card-table",i(P)])},null,8,["config","class"]),m(p,{ref_key:"refDialog",ref:b,config:i(h)},null,8,["config"])],512),[[et,i(c)==="table"]]),i(c)==="detail"?(f(),A("div",gt,[g("div",_t,[g("span",null,N((S=i(o).currentRow)==null?void 0:S.name),1),(U=i(o).currentRow)!=null&&U.code?(f(),A("span",bt,N("("+((L=i(o).currentRow)==null?void 0:L.code)+")"),1)):C("",!0),m(i(ot),{style:{"margin-left":"auto",cursor:"pointer"},icon:"ep:close",onClick:j})]),i(o).currentRow?(f(),q(lt,{key:0,row:i(o).currentRow,class:"detail-table",onAudit:W,onRowClick:e[0]||(e[0]=vt=>{var R,F;return(F=(R=i(u))==null?void 0:R.refPanel)==null?void 0:F.toggleMaxMin("normal")})},null,8,["row"])):C("",!0)])):C("",!0)]),m(p,{ref_key:"refDispatch",ref:y,config:i(v)},null,8,["config"])]}),_:1},512)}}}),hr=at(ht,[["__scopeId","data-v-b974fc6c"]]);export{hr as default};
