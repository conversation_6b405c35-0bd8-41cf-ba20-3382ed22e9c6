import{d as p,c as u,g as m,h as c,F as s,p as o,q as _,G as d,y as f,J as g,L as v,C as h}from"./index-r0dFAfgr.js";const y={class:"dialog-footer"},C=p({__name:"dispatchCommand",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(V){const t=u(!0),l=a=>{f.confirm("Are you sure to close this dialog?").then(()=>{a()}).catch(()=>{})};return(a,e)=>{const n=g,i=v;return m(),c(i,{modelValue:t.value,"onUpdate:modelValue":e[1]||(e[1]=r=>t.value=r),title:"调度指挥",width:"70%","before-close":l},{footer:s(()=>[o("span",y,[_(n,{type:"primary",onClick:e[0]||(e[0]=r=>t.value=!1)},{default:s(()=>e[2]||(e[2]=[d("关闭")])),_:1})])]),default:s(()=>[e[3]||(e[3]=o("video",{controls:"",width:"420",height:"280"},[o("source",{src:"",type:"video/webm"}),o("source",{src:"",type:"video/mp4"}),d(" Sorry, your browser doesn't support embedded videos. ")],-1))]),_:1},8,["modelValue"])}}}),w=h(C,[["__scopeId","data-v-6e62a4a1"]]);export{w as default};
