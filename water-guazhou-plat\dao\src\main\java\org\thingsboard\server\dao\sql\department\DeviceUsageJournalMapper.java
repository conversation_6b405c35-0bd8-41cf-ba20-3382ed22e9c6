package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.DeviceUsageJournal;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceUsageJournalPageRequest;

@Mapper
public interface DeviceUsageJournalMapper extends BaseMapper<DeviceUsageJournal> {
    IPage<DeviceUsageJournal> findByPage(DeviceUsageJournalPageRequest request);

    boolean update(DeviceUsageJournal entity);

}
