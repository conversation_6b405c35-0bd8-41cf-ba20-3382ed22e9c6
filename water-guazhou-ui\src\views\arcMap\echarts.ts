import { useAppStore } from '@/store'

export const gaugeOption = (
  value: number,
  options: {
    max?: number
    title?: string
  }
) => {
  const option = {
    title: {
      text: options.title,
      textStyle: {
        fontSize: 14,
        color: useAppStore().isDark ? '#ddd' : '#7C8295'
      },
      left: 'center',
      top: 'bottom'
    },
    series: [
      {
        type: 'gauge',
        axisLine: {
          lineStyle: {
            width: 20,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: 'inherit'
          },
          length: '40%'
        },
        axisTick: {
          distance: 15,
          length: 4,
          lineStyle: {
            color: useAppStore().isDark ? '#ddd' : '#7C8295',
            width: 1
          }
        },
        splitLine: {
          distance: 15,
          length: 8,
          lineStyle: {
            color: useAppStore().isDark ? '#ddd' : '#7C8295',
            width: 2
          }
        },
        axisLabel: {
          color: 'inherit',
          distance: 30,
          fontSize: 10
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
          color: 'inherit',
          fontSize: 20,
          offsetCenter: [0, '90%']
        },
        max: options.max || 100,
        min: 0,
        data: [
          {
            value: value || 0
          }
        ]
      }
    ]
  }
  return option
}
