package org.thingsboard.server.dao.util.imodel.query.purchase;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseItem;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class DevicePurchaseItemPageRequest extends AdvancedPageableQueryEntity<DevicePurchaseItem, DevicePurchaseItemPageRequest> {
    // 采购单ID
    private String mainId;

    // 设备编码
    private String serialId;

    // 设备名称
    private String deviceName;

    // 设备型号
    private String deviceModel;

    // 采购单编号
    private String purchaseCode;

    // 采购单标题
    private String purchaseTitle;

    // 采购人
    private String userDepartmentId;

    // 采购人
    private String userId;
}
