import { request } from '@/plugins/axios';

/**
 * 删除分区
 * @param ids
 * @returns
 */
export const DeletePartition = (ids: string[]) =>
  request({
    url: '/api/dma/partition',
    method: 'delete',
    data: ids
  });
/**
 * 查询分区明细
 * @param id
 * @returns
 */
export const GetPartitionDetail = (id: string) =>
  request({
    url: `/api/dma/partition/${id}`,
    method: 'get'
  });

/**
 * 分区树
 * @param params
 * @returns
 */
export const GetPartitionTree = () =>
  request({
    url: '/api/dma/partition/list',
    method: 'get'
  });

/**
 * 查询指定分区的父级所有分区
 * @param id
 * @returns
 */
export const GetParentPartition = (id: string) => {
  return request({
    url: `/api/dma/partition/parent/${id}`,
    method: 'get'
  });
};

/**
 * 获取指定区域的子级分区
 * @param pId 分区id
 * @returns
 */
export const GetDMAChildPartitions = (pId: string) => {
  return request({
    url: `/api/dma/partition/child/${pId}`,
    method: 'get'
  });
};
/**
 * 获取DMA的总览数据
 * @param partitionId
 * @returns
 */
export const GetDMADashboard = (partitionId: string) => {
  return request({
    url: '/api/dma/analysis/dashboard?partitionId=' + partitionId,
    method: 'get'
  });
};
