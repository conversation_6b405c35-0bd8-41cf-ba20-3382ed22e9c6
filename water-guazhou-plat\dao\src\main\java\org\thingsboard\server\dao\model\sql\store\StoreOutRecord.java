package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.ConstructionProjectMapper;
import org.thingsboard.server.dao.sql.department.StoreMapper;
import org.thingsboard.server.dao.sql.department.StoreOutRecordMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class StoreOutRecord {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    @InfoViaMapper(name = "isOut", mapper = StoreOutRecordMapper.class)
    private String id;

    // 出库单编号
    private String code;

    // 出库单标题
    private String title;

    // 是否报账
    private Boolean reimbursement;

    // 仓库ID
    @ParseViaMapper(StoreMapper.class)
    private String storehouseId;

    // 领用人ID
    @ParseUsername(withDepartment = true)
    private String receiveUserId;

    // 经办人
    @ParseUsername(withDepartment = true)
    private String manager;

    // 出库类型
    private String type;

    // 施工项目ID
    @ParseViaMapper(ConstructionProjectMapper.class)
    private String constructionProjectId;

    // 是否为补录
    private Boolean addRecord;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 出库时间
    private Date outTime;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
