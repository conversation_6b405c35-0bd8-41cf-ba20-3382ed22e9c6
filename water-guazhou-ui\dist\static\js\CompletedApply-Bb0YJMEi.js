import{d as O,c as m,r as l,s as I,bF as R,S as L,b as h,D as T,u as k,o as B,g as P,n as N,q as c,i as p,F as V,a9 as w,b6 as x,al as M,b7 as G}from"./index-r0dFAfgr.js";import{_ as U}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as Y}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{V as H,c as q,a as J}from"./index-CpGhZCTT.js";import K from"./detail-CU6-qhMl.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const $={class:"wrapper"},fe=O({__name:"CompletedApply",setup(j){const d=m(),u=m(),b=m(),f=l({WorkOrderEmergencyLevelList:[]});function W(){J("1").then(e=>{f.WorkOrderEmergencyLevelList=w(e.data.data||[],"children",{label:"name",value:"id"})})}const E=l({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),v=m(""),C=l({filters:[{type:"radio-button",label:"类别",labelWidth:40,field:"stageBetween",options:[{label:"待审核",value:"SUBMIT"},{label:"已审核",value:"APPROVED"}],onChange:e=>{var o;const t=e==="SUBMIT"?["详情","审核","复审"]:["详情"],r=e==="SUBMIT"?140:100;a.operationWidth=r,(o=a.operations)==null||o.map(s=>{const i=s.text;s.perm=t.indexOf(i)!==-1}),n()}},{type:"input",label:"标题",field:"title",onChange:()=>n()},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:I(M),click:()=>n()},{perm:!0,text:"重置",type:"default",svgIcon:I(G),click:()=>{var e;(e=d.value)==null||e.resetForm(),n()}}]}],defaultParams:{stageBetween:"SUBMIT"},handleSearch:()=>n()}),a=l({columns:[{minWidth:180,prop:"serialNo",label:"工单编号"},{minWidth:120,prop:"level",label:"紧急程度",tag:!0,tagColor:e=>{var t;return((t=f.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=f.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.label}},{minWidth:120,prop:"type",label:"类型"},{minWidth:120,prop:"title",label:"标题"},{minWidth:200,prop:"address",label:"地址"},{minWidth:160,prop:"createTime",label:"分派时间",formatter:e=>R(e.createTime).format("YYYY-MM-DD HH:mm:ss")},{minWidth:160,prop:"completeTime",label:"完成时间",formatter:e=>R(e.completeTime).format("YYYY-MM-DD HH:mm:ss")},{minWidth:120,prop:"processUserName",label:"处理人"},{minWidth:120,prop:"status",label:"状态",formatter:e=>{switch(e.status){case"PENDING":case"ASSIGN":return"待处理";case"RESOLVING":case"ARRIVING":case"PROCESSING":case"SUBMIT":case"REVIEW":case"CHARGEBACK_REVIEW":case"HANDOVER_REVIEW":case"REASSIGN":case"COLLABORATION":return"处理中";case"APPROVED":case"CHARGEBACK":case"TERMINATED":return"已结束"}}}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{a.pagination.limit=t,a.pagination.page=e,n()}},operations:[{perm:!0,isTextBtn:!0,text:"详情",click:e=>{var t;v.value=e.id||"",E.title=e.serialNo,(t=b.value)==null||t.openDrawer()}},{perm:!0,text:"审核",isTextBtn:!0,click:e=>A(e)}]}),g=l({dialogWidth:500,title:"完工审核",labelPosition:"top",group:[{fields:[{type:"radio",label:"审核结果：",field:"stage",options:[{label:"通过",value:"APPROVED"},{label:"退回",value:"REJECTED"}],rules:[{required:!0,message:"请选择结果"}]},{type:"textarea",label:"备注：",field:"processRemark"}]}],defaultValue:{},submit:e=>{L("确定提交？","提示信息").then(async()=>{var t,r,o,s;g.submitting=!0;try{const i=await H((t=a.currentRow)==null?void 0:t.id,{...e,processAdditionalInfo:JSON.stringify(e)});((r=i.data)==null?void 0:r.code)===200?(h.success("操作成功"),(o=u.value)==null||o.closeDialog(),n()):h.error(((s=i.data)==null?void 0:s.err)||"操作失败")}catch{h.error("系统错误")}g.submitting=!1}).catch(()=>{})}}),A=e=>{var t;a.currentRow=e,(t=u.value)==null||t.openDialog()},n=async()=>{var e,t,r,o,s,i,y;a.loading=!0;try{const _=((e=d.value)==null?void 0:e.queryParams)||{},D={page:a.pagination.page||1,size:a.pagination.limit||20,..._,stageBetween:_.stageBetween==="APPROVED"?"APPROVED":"SUBMIT,REVIEW",stepProcessUserId:T(((r=(t=k().user)==null?void 0:t.id)==null?void 0:r.id)||"")};console.log();const S=await q(D);a.dataList=((s=(o=S.data)==null?void 0:o.data)==null?void 0:s.data)||[],a.pagination.total=((y=(i=S.data)==null?void 0:i.data)==null?void 0:y.total)||0}catch{}a.loading=!1};return B(()=>{W(),n()}),(e,t)=>{const r=F,o=Y,s=U,i=x;return P(),N("div",$,[c(r,{ref_key:"refSearch",ref:d,config:p(C)},null,8,["config"]),c(o,{class:"card-table",config:p(a)},null,8,["config"]),c(s,{ref_key:"refFormAudit",ref:u,config:p(g)},null,8,["config"]),c(i,{ref_key:"refdetail",ref:b,config:p(E)},{default:V(()=>[c(K,{id:p(v)},null,8,["id"])]),_:1},8,["config"])])}}});export{fe as default};
