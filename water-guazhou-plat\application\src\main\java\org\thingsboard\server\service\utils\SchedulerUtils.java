package org.thingsboard.server.service.utils;

import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.schedule.ScheduleJob;
import org.thingsboard.server.service.task.job.DeviceOfflineJob;
import org.thingsboard.server.service.task.job.RestApiJob;
import org.thingsboard.server.service.task.job.StatisticsJob;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/11 15:13
 */
public class SchedulerUtils {

    private static SchedulerFactory schedulerFactory = new StdSchedulerFactory();


    private SchedulerUtils() {
    }

    public static SchedulerFactory getSchedulerFactory() {
        if (schedulerFactory == null) {
            return new StdSchedulerFactory();
        } else {
            return schedulerFactory;
        }
    }


    /**
     * 转换定时任务时间表达式
     *
     * @param frequency 统计间隔
     * @return 时间表达式
     */
    public static String getSchedulerCron(String frequency, boolean isOrigin) {
        String defaultCron = "0 0/1 * * * ?";
        if (frequency == null || isOrigin) {
            return defaultCron;
        }
        if (frequency.equalsIgnoreCase(DateUtils.MINUTE)) {
            defaultCron = "0 0/1 * * * ?";
        } else if (frequency.endsWith(DateUtils.MINUTE_SPC)) {
            if (Integer.parseInt(frequency.split(DateUtils.MINUTE_SPC)[0]) > 59) {
                defaultCron = "0 2 * * * ?";
            } else {
                defaultCron = "0 " + "2/" + frequency.split(DateUtils.MINUTE_SPC)[0] + " * * * ?";
            }
        } else if (frequency.endsWith(DateUtils.HOUR_SPC)) {
            defaultCron = "0 2 * * * ?";
        } else if (frequency.endsWith(DateUtils.DAY_SPC)) {
            defaultCron = "0 2 0 * * ?";
        }
        return defaultCron;
    }


    /**
     * 根据数据间隔获取数据类型
     *
     * @param frequency 统计间隔
     * @return 时间表达式
     */
    public static String getFrequencyType(String frequency) {
        String defaultType = "m";
        if (frequency == null) {
            return defaultType;
        }
        if (frequency.endsWith(DateUtils.MINUTE_SPC)) {
            defaultType = DateUtils.MINUTE_SPC;
        } else if (frequency.endsWith(DateUtils.HOUR_SPC)) {
            defaultType = DateUtils.HOUR;
        } else if (frequency.endsWith(DateUtils.DAY_SPC)) {
            defaultType = DateUtils.DAY;
        }
        return defaultType;
    }


    /**
     * 执行定时任务
     */
    public static void startScheduler(List<DataSourceEntity> rootSource, Scheduler scheduler, String originatorId, String originatorType, boolean isOrigin) throws SchedulerException {
        rootSource.forEach(dataSourceEntity -> {
            String sourceName = dataSourceEntity.getId();
            if (isOrigin) {
                sourceName = sourceName + DataConstants.ORGIN;
            }
            ScheduleJob scheduleJob = ScheduleJob.builder().name(sourceName)
                    .cronExpression(getSchedulerCron(dataSourceEntity.getFrequency(), isOrigin))
                    .group(originatorId).build();
            JobDetail jobDetail = JobBuilder.newJob(StatisticsJob.class).withIdentity(scheduleJob.getName(), scheduleJob.getGroup()).build();
            jobDetail.getJobDataMap().put(DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_VALUE, dataSourceEntity);
            jobDetail.getJobDataMap().put("originatorId", originatorId);
            jobDetail.getJobDataMap().put("originatorType", originatorType);
            jobDetail.getJobDataMap().put("isOrigin", isOrigin);
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());
            // 按新的cronExpression表达式构建一个新的trigger
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(scheduleJob.getName(), scheduleJob.getGroup()).withSchedule(scheduleBuilder).build();
            try {
                scheduler.scheduleJob(jobDetail, trigger);
            } catch (SchedulerException e) {
                e.printStackTrace();
            }
        });
        scheduler.start();
    }


    /**
     * 执行restApi数据源
     */
    public static void processRestApi(RestApiEntity restApiEntity, Scheduler scheduler) throws SchedulerException {
        ScheduleJob scheduleJob = ScheduleJob.builder().name(restApiEntity.getName())
                .cronExpression(getSchedulerCron(null, true))
                .group(restApiEntity.getId()).build();
        JobDetail jobDetail = JobBuilder.newJob(RestApiJob.class).withIdentity(scheduleJob.getName(), scheduleJob.getGroup()).build();
        jobDetail.getJobDataMap().put(DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_VALUE, restApiEntity);
        jobDetail.getJobDataMap().put("originatorId", restApiEntity.getId());
        jobDetail.getJobDataMap().put("originatorType", DataSourceType.RESTAPI_SOURCE);
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());
        // 按新的cronExpression表达式构建一个新的trigger
        CronTrigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(scheduleJob.getName(), scheduleJob.getGroup()).withSchedule(scheduleBuilder).build();
        try {
            scheduler.scheduleJob(jobDetail, trigger);
//                JobKey key = new JobKey(scheduleJob.getName(), scheduleJob.getGroup());
//                scheduler.resumeJob(key);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
        scheduler.start();
    }


    /**
     * 设备掉线定时任务
     */
    public static void startScheduler(List<Device> devices, Scheduler scheduler) throws SchedulerException {
        devices.forEach(device -> {
            ScheduleJob scheduleJob = ScheduleJob.builder().name(UUIDConverter.fromTimeUUID(device.getUuidId()))
                    .cronExpression(getSchedulerCron(device.getOfflineInterval(), false))
                    .group(UUIDConverter.fromTimeUUID(device.getTenantId().getId())).build();
            JobDetail jobDetail = JobBuilder.newJob(DeviceOfflineJob.class).withIdentity(scheduleJob.getName(), scheduleJob.getGroup()).build();
            jobDetail.getJobDataMap().put(DataConstants.STATISTICS_DATA_SOURCE_TEMPLATE_VALUE, device);
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());
            // 按新的cronExpression表达式构建一个新的trigger
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(scheduleJob.getName(), scheduleJob.getGroup()).withSchedule(scheduleBuilder).build();
            try {
                scheduler.scheduleJob(jobDetail, trigger);
            } catch (SchedulerException e) {
                e.printStackTrace();
            }
        });
        // TODO 暂停离线告警
//        scheduler.start();
    }


}
