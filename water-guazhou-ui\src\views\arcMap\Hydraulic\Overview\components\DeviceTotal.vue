<template>
  <el-row :gutter="20">
    <el-col
      v-for="(item, i) in props.deviceTotal"
      :key="i"
      :span="item.span"
      style="margin: 10px 0"
    >
      <div
        class="total-item"
        :style="{ background: item.bgColor, color: item.color || '#fff' }"
      >
        <p>
          <span>{{ item.value }}</span> <span class="unit">{{ item.unit }}</span>
        </p>
        <p>
          <span>{{ item.label }}</span>
        </p>
      </div>
    </el-col>
  </el-row>
</template>
<script lang="ts" setup>
const props = defineProps<{
  deviceTotal: { label: string; value: any; unit: string; span: number; bgColor: string; color?: string }[]
}>()
</script>
<style lang="scss" scoped>
.total-item {
  height: 170px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  p {
    display: flex;
    align-items: baseline;
    flex-direction: row;
    .unit {
      margin-left: 8px;
    }
  }
}
</style>
