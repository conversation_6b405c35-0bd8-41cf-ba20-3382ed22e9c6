import{R as A,T as N,aW as fo,cl as We,a4 as yt,$ as ae,a3 as Tr,b1 as ea,hK as Gn,aO as Wn,eS as Bn,aR as mo}from"./index-r0dFAfgr.js";import{jk as wt,ex as jt,ez as ta,a_ as ie,aS as Tt,ag as Hr,aR as H,jl as kn,aQ as te,aX as ir,aO as k,af as $,A as cr,lS as ra,lQ as qn,h$ as Xn,d0 as vr,gO as Es,gl as ke,aW as ne,a3 as Zn,aa as Yn,a9 as qi,a0 as ti,lT as Jn,eM as Qn,aY as dr,aZ as go,i9 as Or,lU as Kn,lV as ia,jE as sa,ab as ct,jI as aa,lW as el,dD as tl,ey as sr,aP as Z,gI as Nt,bm as Ot,aT as nt,ac as rt,ae as de,aU as rl,lX as vo,kP as Le,la as Ds,a1 as Gr,b1 as zt,lE as Fi,bd as Si,aA as K,hr as oa,ji as il,aV as sl,e0 as Vt,fZ as Rt,f_ as tr,gN as Dr,dB as na,fY as rr,fV as ye,lY as al,gV as la,aN as ol,lZ as nl,bO as zi,l_ as ll,l$ as cl,jv as dl,c9 as hl,li as xs,m0 as ul,kK as pl,ln as fl,lo as ml,iP as gl,kR as vl,lD as _l,hI as yl,g_ as wl,l6 as Sl,l0 as xl,lA as Tl,m1 as Ol,m2 as Cl,m3 as bl,be as Al,bE as Rl,r as Pl,m4 as El,kY as Dl,bf as ca,bh as $l,lJ as Il,m5 as Ml}from"./MapView-DaoQedLH.js";import{r as le,n as ee,t as da}from"./vec3f32-nZdmKIgz.js";import{l as Ts,p as dt,j as gt,V as ce,Y as xi,_ as Ll,x as Nl}from"./plane-BhzlJB-C.js";import{R as Fl,C as zl,d as mt,f as jl,h as Vl,a as Os,p as Ul}from"./sphere-NgXH-gLx.js";import{aU as _o,an as Cr,aM as ha,aV as ua,aW as pa,h as T,t as yo,r as x,ac as br,b as B,$ as ar,aP as fi,aQ as zr,aR as wo,aD as $s,T as Is,z as Ti,B as So,D as Hl,W as Ee,al as Gl,Q as Yr,J as Wl,p as xo,q as Ms,v as Jr,f as _r,aq as St,aE as To,aF as Bl,X as Ls,u as vt,P as ji,a as Ve,a6 as Ar,a7 as Rr,a9 as Oi,aa as Ci,ab as Ns,ah as W,a5 as Pr,aG as fa,aH as kl,aI as ql,aJ as Xl,aK as Oo,ad as Vi,a4 as xt,aw as Ui,aL as Zl,am as Ne,ak as j,ao as Fs,j as Ut,aX as Xi,aY as Yl,aZ as Jl,ag as ma,a1 as Ql,af as Kl,a_ as ec,A as tc,N as Zi,K as Co,L as bo,a$ as rc,g as zs,U as ic,k as Hi,M as sc,I as ac,G as Ao,Z as oc,a8 as Qr,ai as js,ax as Vs,ar as nc,b0 as lc,b1 as Ro,b3 as cc,b4 as dc,b5 as hc,av as uc,b6 as Po,b7 as pc,d as fc,aA as Eo,b8 as mc,s as gc,b9 as vc,a3 as ga,ba as _c,bb as yc,bc as va,ae as wc,aj as Sc,bd as xc,az as Tc,aB as Oc,aT as Cc,aC as _a,be as ya,bf as bc,V as Do,w as Ac,ay as Rc,b2 as Pc,bg as Ec,bh as Dc,bi as $c}from"./VertexColor.glsl-CUs3Tjt8.js";import{n as Us,f as bi,l as $o,a as wa}from"./triangulationUtils-Da3LiW_b.js";import{o as Ic}from"./Indices-iFKW8TWb.js";import{s as Te,l as Io,a as Mc,c as kt,f as ri,h as Lc}from"./Util-sSNWzwlq.js";import{O as h}from"./VertexAttribute-BAIQI41G.js";import{J as it,M as Mo,e as v,y as L,v as Hs,i as Kr,a as Gi,aq as Sa,ae as Gs,aI as Nc,bx as Fc,aZ as ot,by as Yi,$ as zc,S as jc,aY as Vc}from"./Point-WxyopZva.js";import{e as Y,o as Lo}from"./mat4f64-BCm7QTSd.js";import{aM as se,aN as mi,aO as Uc,ap as Hc,ar as Gc}from"./AnimatedLinesLayer-B2VbV4jv.js";import{e as Ws}from"./mat3f64-BVJGbF0t.js";import{x as Wc,i as Bc,u as kc}from"./BufferView-BcX1hwIm.js";import{T as bt}from"./InterleavedLayout-EYSqXknm.js";import{a as re,o as m,n as qc,W as et,c as jr,A as Wi,l as Bs,b as yr,E as No,_ as lt,f as Xc,d as Fo,S as zo,g as jo,e as Zc,h as Yc}from"./OrderIndependentTransparency-C5Ap76ew.js";import{Y as Vo,V as Uo,M as Vr,P as wr,G as Wr,D as Sr,L as Ai,E as Ht,_ as Ri,F as Ho,A as Jc,C as Qc,R as tt,I as Kc}from"./enums-BDQrMlcz.js";import{t as xa,i as Ta,N as ed,n as ks}from"./basicInterfaces-Dc_Mm1a-.js";import{M as qs}from"./dehydratedFeatures-CEuswj7y.js";import{c as Go,l as Ji,w as Oa,a as td}from"./widget-BcWKanF2.js";import{x as Wo,u as rd,E as Bo,n as id}from"./FramebufferObject-8j9PRuxE.js";import{t as Xs}from"./NestedMap-DgiGbX8E.js";import{V as sd,H as ad,u as od,s as nd,U as _t}from"./Octree-s2cwrV3a.js";import{v as Bi,M as ko,b as hr,k as qo}from"./lineSegment-DQ0q5UHF.js";import{E as or}from"./Texture-BYqObwfn.js";import"./boundedPlane-DeyjpfhM.js";import{o as ld}from"./glUtil-D4FNL8tc.js";import{o as Xo}from"./floatRGBA-PQQNbO39.js";var Ur,Pi,Cs;(function(t){t[t.RasterImage=0]="RasterImage",t[t.Features=1]="Features"})(Ur||(Ur={})),function(t){t[t.MapLayer=0]="MapLayer",t[t.ViewLayer=1]="ViewLayer",t[t.Outline=2]="Outline",t[t.SnappingHint=3]="SnappingHint"}(Pi||(Pi={})),function(t){t[t.WithRasterImage=0]="WithRasterImage",t[t.WithoutRasterImage=1]="WithoutRasterImage"}(Cs||(Cs={}));var Ei,Ke;(function(t){t[t.ADD=0]="ADD",t[t.UPDATE=1]="UPDATE",t[t.REMOVE=2]="REMOVE"})(Ei||(Ei={})),function(t){t[t.NONE=0]="NONE",t[t.VISIBILITY=1]="VISIBILITY",t[t.GEOMETRY=2]="GEOMETRY",t[t.TRANSFORMATION=4]="TRANSFORMATION",t[t.HIGHLIGHT=8]="HIGHLIGHT",t[t.OCCLUDEE=16]="OCCLUDEE"}(Ke||(Ke={}));var Br;(function(t){t[t.ASYNC=0]="ASYNC",t[t.SYNC=1]="SYNC"})(Br||(Br={}));let cd=class extends _o{get geometries(){return this._geometries}get transformation(){return this._transformation}set transformation(e){wt(this._transformation,e),this._invalidateBoundingVolume(),this._emit("objectTransformation",this)}constructor(e={}){super(),this.type=Cr.Object,this._geometries=new Array,this._transformation=Y(),this._bvObjectSpace=new Ca,this._bvWorldSpace=new Ca,this._bvDirty=!0,this._hasVolatileTransformation=!1,this._visible=!0,this.castShadow=e.castShadow==null||e.castShadow,this.metadata=e.metadata,this.metadata&&this.metadata.isElevationSource&&(this.metadata.lastValidElevationBB=new Zo);const r=e.geometries;A(r)&&(this._geometries=Array.from(r))}dispose(){this._geometries.length=0}get parentLayer(){return this._parentLayer}set parentLayer(e){Te(this._parentLayer==null||e==null,"Object3D can only be added to a single Layer"),this._parentLayer=e}addGeometry(e){e.visible=this._visible,this._geometries.push(e),this._hasVolatileTransformation=this._hasVolatileTransformation||e.hasVolatileTransformation,this._emit("objectGeometryAdded",{object:this,geometry:e}),this._invalidateBoundingVolume()}removeGeometry(e){const r=this._geometries.splice(e,1)[0];r&&(this._emit("objectGeometryRemoved",{object:this,geometry:r}),this._invalidateBoundingVolume())}removeAllGeometries(){for(;this._geometries.length>0;)this.removeGeometry(0)}geometryVertexAttrsUpdated(e){this._emit("objectGeometryUpdated",{object:this,geometry:e}),this._invalidateBoundingVolume()}get visible(){return this._visible}set visible(e){if(this._visible!==e){this._visible=e;for(const r of this._geometries)r.visible=this._visible;this._emit("visibilityChanged",this)}}maskOccludee(){const e=new ha(xa.MaskOccludee);for(const r of this._geometries)r.occludees=ua(r.occludees,e);return this._emit("occlusionChanged",this),e}removeOcclude(e){for(const r of this._geometries)r.occludees=pa(r.occludees,e);this._emit("occlusionChanged",this)}highlight(){const e=new ha(xa.Highlight);for(const r of this._geometries)r.highlights=ua(r.highlights,e);return this._emit("highlightChanged",this),e}removeHighlight(e){for(const r of this._geometries)r.highlights=pa(r.highlights,e);this._emit("highlightChanged",this)}getCombinedStaticTransformation(e,r){return jt(r,this.transformation,e.transformation)}_getCombinedShaderTransformation(e){return jt(Y(),this.transformation,e.shaderTransformation)}hasVolativeTransformation(){return this._hasVolatileTransformation}get boundingVolumeWorldSpace(){return this._validateBoundingVolume(),this._bvWorldSpace}get boundingVolumeObjectSpace(){return this._validateBoundingVolume(),this._bvObjectSpace}_validateBoundingVolume(){if(!this._bvDirty&&!this._hasVolatileTransformation)return;this._bvObjectSpace.init(),this._bvWorldSpace.init();for(const s of this._geometries){const a=s.boundingInfo;A(a)&&(ba(a,this._bvObjectSpace,s.shaderTransformation),ba(a,this._bvWorldSpace,this._getCombinedShaderTransformation(s)))}ta(this._bvObjectSpace.bounds,this._bvObjectSpace.min,this._bvObjectSpace.max,.5),ta(this._bvWorldSpace.bounds,this._bvWorldSpace.min,this._bvWorldSpace.max,.5);const e=$(),r=$(),i=Ts(this.transformation);for(const s of this._geometries){const a=s.boundingInfo;if(N(a))continue;const o=s.shaderTransformation,n=Ts(o);ie(e,a.center,o);const c=Tt(e,this._bvObjectSpace.bounds),l=a.radius*n;this._bvObjectSpace.bounds[3]=Math.max(this._bvObjectSpace.bounds[3],c+l),ie(r,e,this.transformation);const d=Tt(r,this._bvWorldSpace.bounds),p=l*i;this._bvWorldSpace.bounds[3]=Math.max(this._bvWorldSpace.bounds[3],d+p)}this._bvDirty=!1}_invalidateBoundingVolume(){this._bvDirty=!0,A(this._parentLayer)&&this._parentLayer.notifyObjectBBChanged(this,this._bvWorldSpace.bounds)}_emit(e,r){A(this._parentLayer)&&this._parentLayer.events.emit(e,r)}get test(){const e=this;return{hasGeometry:r=>e._geometries.includes(r),getGeometryIndex:r=>e._geometries.indexOf(r)}}},Zo=class{constructor(){this.min=Hr(Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),this.max=Hr(-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE)}isEmpty(){return this.max[0]<this.min[0]&&this.max[1]<this.min[1]&&this.max[2]<this.min[2]}},Ca=class extends Zo{constructor(){super(...arguments),this.bounds=Fl()}init(){H(this.min,Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),H(this.max,-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE),zl(this.bounds)}};function ba(t,e,r){const i=t.bbMin,s=t.bbMax;if(kn(r)){const a=H(dd,r[12],r[13],r[14]);te(De,i,a),te(Ge,s,a);for(let o=0;o<3;++o)e.min[o]=Math.min(e.min[o],De[o]),e.max[o]=Math.max(e.max[o],Ge[o])}else if(ie(De,i,r),ir(i,s))for(let a=0;a<3;++a)e.min[a]=Math.min(e.min[a],De[a]),e.max[a]=Math.max(e.max[a],De[a]);else{ie(Ge,s,r);for(let a=0;a<3;++a)e.min[a]=Math.min(e.min[a],De[a],Ge[a]),e.max[a]=Math.max(e.max[a],De[a],Ge[a]);for(let a=0;a<3;++a){k(De,i),k(Ge,s),De[a]=s[a],Ge[a]=i[a],ie(De,De,r),ie(Ge,Ge,r);for(let o=0;o<3;++o)e.min[o]=Math.min(e.min[o],De[o],Ge[o]),e.max[o]=Math.max(e.max[o],De[o],Ge[o])}}}const dd=$(),De=$(),Ge=$(),hd=["layerObjectAdded","layerObjectRemoved","layerObjectsAdded","layerObjectsRemoved","shaderTransformationChanged","objectTransformation","visibilityChanged","occlusionChanged","highlightChanged","objectGeometryAdded","objectGeometryRemoved","objectGeometryUpdated"];let ud=class extends _o{get objects(){return this._objects}constructor(e,r=""){super(),this.apiLayerUid=r,this.type=Cr.Layer,this.events=new Go,this.sliceable=!1,this._objects=new it,this._objectsAdded=new it,this._stageHandles=new Mo,this.apiLayerUid=r,this.visible=(e==null?void 0:e.visible)??!0,this.pickable=(e==null?void 0:e.pickable)??!0,this.updatePolicy=(e==null?void 0:e.updatePolicy)??Br.ASYNC,this._disableOctree=(e==null?void 0:e.disableOctree)??!1}destroy(){this.detachStage(),this._stage=null}attachStage(e){this.detachStage(),this._stage=e;for(const r of hd)this._stageHandles.add(this.events.on(r,i=>e.handleEvent(r,i)))}detachStage(){this._stageHandles.removeAll(),this.invalidateSpatialQueryAccelerator()}add(e){this._objects.push(e),e.parentLayer=this,this.events.emit("layerObjectAdded",{layer:this,object:e}),A(this._octree)&&this._objectsAdded.push(e)}remove(e){this._objects.removeUnordered(e)&&(e.parentLayer=null,this.events.emit("layerObjectRemoved",{layer:this,object:e}),A(this._octree)&&(this._objectsAdded.removeUnordered(e)||this._octree.remove([e])))}addMany(e){this._objects.pushArray(e);for(const r of e)r.parentLayer=this;this.events.emit("layerObjectsAdded",{layer:this,objects:e}),A(this._octree)&&this._objectsAdded.pushArray(e)}removeMany(e){const r=new Array;if(this._objects.removeUnorderedMany(e,e.length,r),r.length!==0){for(const i of r)i.parentLayer=null;if(this.events.emit("layerObjectsRemoved",{layer:this,objects:r}),A(this._octree)){for(let i=0;i<r.length;)this._objectsAdded.removeUnordered(r[i])?(r[i]=r[r.length-1],r.length-=1):++i;this._octree.remove(r)}}}sync(){A(this._stage)&&this.updatePolicy!==Br.SYNC&&this._stage.syncLayer(this.id)}notifyObjectBBChanged(e,r){A(this._octree)&&!this._objectsAdded.includes(e)&&this._octree.update(e,r)}getSpatialQueryAccelerator(){return N(this._octree)&&this._objects.length>50&&!this._disableOctree?(this._octree=new sd(e=>e.boundingVolumeWorldSpace.bounds),this._octree.add(this._objects.data,this._objects.length)):A(this._octree)&&this._objectsAdded.length>0&&(this._octree.add(this._objectsAdded.data,this._objectsAdded.length),this._objectsAdded.clear()),this._octree}shaderTransformationChanged(){this.invalidateSpatialQueryAccelerator(),this.events.emit("shaderTransformationChanged",this)}invalidateSpatialQueryAccelerator(){this._octree=fo(this._octree),this._objectsAdded.clear()}},pd=class{constructor(e,r){this.vec3=e,this.id=r}};function gi(t,e,r,i){return new pd(Hr(t,e,r),i)}var nr;(function(t){t[t.None=0]="None",t[t.ColorAndWater=1]="ColorAndWater",t[t.Highlight=2]="Highlight",t[t.Occluded=3]="Occluded",t[t.ObjectAndLayerIdColor=4]="ObjectAndLayerIdColor"})(nr||(nr={}));let Aa=class{get extent(){return this._extent}constructor(e,r){this.index=e,this.renderTargets=r,this._extent=cr(),this.resolution=0,this.renderLocalOrigin=gi(0,0,0,"O"),this.pixelRatio=1,this.mapUnitsPerPixel=1,this.canvasGeometries=new fd,this.hasDrapedFeatureSource=!1,this.hasDrapedRasterSource=!1,this.hasTargetWithoutRasterImage=!1,this.index=e,this.validTargets=new Array(r.renderTargets.length).fill(!1)}getValidTexture(e){return this.validTargets[e]?this.renderTargets.getTarget(e).getTexture():null}get _needsColorWithoutRasterImage(){return this.hasDrapedRasterSource&&this.hasDrapedFeatureSource&&this.hasTargetWithoutRasterImage}getColorTexture(e){const r=e===nr.ColorAndWater?this.renderTargets.getTarget(se.Color):e===nr.Highlight?this.renderTargets.getTarget(se.Highlight):e===nr.ObjectAndLayerIdColor?this.renderTargets.getTarget(se.ObjectAndLayerIdColor):this.renderTargets.getTarget(se.Occluded);return r?r.getTexture():null}getColorTextureNoRasterImage(){return this._needsColorWithoutRasterImage?this.getValidTexture(se.ColorNoRasterImage):this.hasDrapedFeatureSource?this.getValidTexture(se.Color):null}getNormalTexture(e){const r=e===nr.ColorAndWater?this.renderTargets.getTarget(se.Water):null;return r?r.getTexture():null}draw(e,r){const i=this.computeRenderTargetValidityBitfield();for(const s of this.renderTargets.renderTargets)s.type!==se.ColorNoRasterImage||this._needsColorWithoutRasterImage?this.validTargets[s.type]=e.drawTarget(this,s,r):this.validTargets[s.type]=!1;return i^this.computeRenderTargetValidityBitfield()?Ta.CHANGED:Ta.UNCHANGED}computeRenderTargetValidityBitfield(){const e=this.validTargets;return+e[se.Color]|+e[se.ColorNoRasterImage]<<1|+e[se.Highlight]<<2|+e[se.Water]<<3|+e[se.Occluded]<<4}setupGeometryViewsCyclical(e){this.setupGeometryViewsDirect();const r=.001*e.range;if(this._extent[0]-r<=e.min){const i=this.canvasGeometries.extents[this.canvasGeometries.numViews++];ra(this._extent,e.range,0,i)}if(this._extent[2]+r>=e.max){const i=this.canvasGeometries.extents[this.canvasGeometries.numViews++];ra(this._extent,-e.range,0,i)}}setupGeometryViewsDirect(){this.canvasGeometries.numViews=1,qn(this.canvasGeometries.extents[0],this._extent)}hasSomeSizedView(){for(let e=0;e<this.canvasGeometries.numViews;e++){const r=this.canvasGeometries.extents[e];if(r[0]!==r[2]&&r[1]!==r[3])return!0}return!1}applyViewport(e){e.setViewport(this.index===mi.INNER?0:this.resolution,0,this.resolution,this.resolution)}},fd=class{constructor(){this.extents=[cr(),cr(),cr()],this.numViews=0}},Yo=class{constructor(e,r){this._size=Xn(),this._fbo=null,this._fbo=new Wo(e,{colorTarget:Vo.TEXTURE,depthStencilTarget:Uo.NONE},{target:Vr.TEXTURE_2D,pixelFormat:wr.RGBA,dataType:Wr.UNSIGNED_BYTE,wrapMode:Sr.CLAMP_TO_EDGE,samplingMode:Ai.LINEAR_MIPMAP_LINEAR,hasMipmap:r,maxAnisotropy:8,width:0,height:0})}dispose(){this._fbo=We(this._fbo)}getTexture(){return this._fbo?this._fbo.colorTexture:null}isValid(){return this._fbo!==null}resize(e,r){this._size[0]=e,this._size[1]=r,this._fbo.resize(this._size[0],this._size[1])}bind(e){e.bindFramebuffer(this._fbo)}generateMipMap(){const e=this._fbo.colorTexture;e.descriptor.hasMipmap&&e.generateMipmap()}disposeRenderTargetMemory(){var e;(e=this._fbo)==null||e.resize(0,0)}get gpuMemoryUsage(){var e;return((e=this._fbo)==null?void 0:e.gpuMemoryUsage)??0}},qt=class{constructor(e,r,i,s=!0){this.output=r,this.type=i,this.valid=!1,this.lastUsed=1/0,this.fbo=new Yo(e,s)}},md=class{constructor(e){this.renderTargets=[new qt(e,T.Color,se.Color),new qt(e,T.Color,se.ColorNoRasterImage),new qt(e,T.Highlight,se.Highlight,!1),new qt(e,T.Normal,se.Water),new qt(e,T.Color,se.Occluded)],yt("enable-feature:objectAndLayerId-rendering")&&this.renderTargets.push(new qt(e,T.ObjectAndLayerIdColor,se.ObjectAndLayerIdColor))}getTarget(e){return this.renderTargets[e].fbo}dispose(){for(const e of this.renderTargets)e.fbo.dispose()}disposeRenderTargetMemory(){for(const e of this.renderTargets)e.fbo.disposeRenderTargetMemory()}validateUsageForTarget(e,r,i){if(e)r.lastUsed=i;else if(i-r.lastUsed>gd)r.fbo.disposeRenderTargetMemory(),r.lastUsed=1/0;else if(r.lastUsed<1/0)return!0;return!1}get gpuMemoryUsage(){return this.renderTargets.reduce((e,r)=>e+r.fbo.gpuMemoryUsage,0)}};const gd=1e3;let vd=class{constructor(e){this._context=e,this._perConstructorInstances=new Xs,this._frameCounter=0,this._keepAliveFrameCount=Ra}get viewingMode(){return this._context.viewingMode}get constructionContext(){return this._context}destroy(){this._perConstructorInstances.forEach(e=>e.forEach(r=>r.technique.destroy())),this._perConstructorInstances.clear()}acquire(e,r=yd){const i=r.key;let s=this._perConstructorInstances.get(e,i);if(N(s)){const a=new e(this._context,r,()=>this.release(a));s=new _d(a),this._perConstructorInstances.set(e,i,s)}return++s.refCount,s.technique}releaseAndAcquire(e,r,i){if(A(i)){if(r.key===i.key)return i;this.release(i)}return this.acquire(e,r)}release(e){if(N(e)||this._perConstructorInstances.empty)return;const r=this._perConstructorInstances.get(e.constructor,e.key);N(r)||(--r.refCount,r.refCount===0&&(r.refZeroFrame=this._frameCounter))}frameUpdate(){this._frameCounter++,this._keepAliveFrameCount!==Ra&&this._perConstructorInstances.forEach((e,r)=>{e.forEach((i,s)=>{i.refCount===0&&i.refZeroFrame+this._keepAliveFrameCount<this._frameCounter&&(i.technique.destroy(),this._perConstructorInstances.delete(r,s))})})}async reloadAll(){const e=new Array;this._perConstructorInstances.forEach((r,i)=>{const s=async(a,o)=>{const n=o.shader;n&&(await n.reload(),a.forEach(c=>c.technique.reload(this._context)))};e.push(s(r,i))}),await Promise.all(e)}},_d=class{constructor(e){this.technique=e,this.refCount=0,this.refZeroFrame=0}};const Ra=-1,yd=new yo;function wd(t,e,r){return 2*Math.atan(Math.sqrt(e*e+r*r)*Math.tan(.5*t)/e)}function Sd(t,e,r){return 2*Math.atan(Math.sqrt(e*e+r*r)*Math.tan(.5*t)/r)}function xd(t,e,r){return 2*Math.atan(e*Math.tan(.5*t)/Math.sqrt(e*e+r*r))}function Td(t,e,r){return 2*Math.atan(r*Math.tan(.5*t)/Math.sqrt(e*e+r*r))}var bs;let U=bs=class extends Hs{constructor(t={}){super(t),this._center=$(),this._up=$(),this._viewUp=$(),this._viewForward=$(),this._viewRight=$(),this._ray=mt(),this._viewport=vr(0,0,1,1),this._padding=vr(0,0,0,0),this._fov=55/180*Math.PI,this._nearFar=Es(1,1e3),this._viewDirty=!0,this._viewMatrix=Y(),this._viewProjectionDirty=!0,this._viewProjectionMatrix=Y(),this._viewInverseTransposeMatrixDirty=!0,this._viewInverseTransposeMatrix=Y(),this._frustumDirty=!0,this._frustum=ad(),this._fullViewport=ke(),this._pixelRatio=1,this.relativeElevation=0}get pixelRatio(){return this._pixelRatio}set pixelRatio(t){this._pixelRatio=t>0?t:1}get eye(){return this._ray.origin}set eye(t){this._compareAndSetView(t,this._ray.origin)}get center(){return this._center}set center(t){this._compareAndSetView(t,this._center,"_center")}get ray(){return ne(this._ray.direction,this.center,this.eye),this._ray}get up(){return this._up}set up(t){this._compareAndSetView(t,this._up,"_up")}get viewMatrix(){return this._ensureViewClean(),this._viewMatrix}set viewMatrix(t){wt(this._viewMatrix,t),this._viewDirty=!1,this._viewInverseTransposeMatrixDirty=!0,this._viewProjectionDirty=!0,this._frustumDirty=!0}get viewForward(){return this._ensureViewClean(),this._viewForward}get viewUp(){return this._ensureViewClean(),this._viewUp}get viewRight(){return this._ensureViewClean(),this._viewRight}get nearFar(){return this._nearFar}get near(){return this._nearFar[0]}set near(t){this._nearFar[0]!==t&&(this._nearFar[0]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_nearFar"))}get far(){return this._nearFar[1]}set far(t){this._nearFar[1]!==t&&(this._nearFar[1]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_nearFar"))}get viewport(){return this._viewport}set viewport(t){this.x=t[0],this.y=t[1],this.width=t[2],this.height=t[3]}get screenViewport(){if(this.pixelRatio===1)return this._viewport;const t=Zn(ke(),this._viewport,1/this.pixelRatio),e=this._get("screenViewport");return e&&Yn(t,e)?e:t}get x(){return this._viewport[0]}set x(t){t+=this._padding[G.LEFT],this._viewport[0]!==t&&(this._viewport[0]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_viewport"))}get y(){return this._viewport[1]}set y(t){t+=this._padding[G.BOTTOM],this._viewport[1]!==t&&(this._viewport[1]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_viewport"))}get width(){return this._viewport[2]}set width(t){this._viewport[2]!==t&&(this._viewport[2]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_viewport"))}get height(){return this._viewport[3]}set height(t){this._viewport[3]!==t&&(this._viewport[3]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_viewport"))}get fullWidth(){return this._viewport[2]+this._padding[G.RIGHT]+this._padding[G.LEFT]}set fullWidth(t){this.width=t-(this._padding[G.RIGHT]+this._padding[G.LEFT])}get fullHeight(){return this._viewport[3]+this._padding[G.TOP]+this._padding[G.BOTTOM]}set fullHeight(t){this.height=t-(this._padding[G.TOP]+this._padding[G.BOTTOM])}get fullViewport(){return this._fullViewport[0]=this._viewport[0]-this._padding[G.LEFT],this._fullViewport[1]=this._viewport[1]-this._padding[G.BOTTOM],this._fullViewport[2]=this.fullWidth,this._fullViewport[3]=this.fullHeight,this._fullViewport}get _aspect(){return this.width/this.height}get padding(){return this._padding}set padding(t){qi(this._padding,t)||(this._viewport[0]+=t[G.LEFT]-this._padding[G.LEFT],this._viewport[1]+=t[G.BOTTOM]-this._padding[G.BOTTOM],this._viewport[2]-=t[G.RIGHT]+t[G.LEFT]-(this._padding[G.RIGHT]+this._padding[G.LEFT]),this._viewport[3]-=t[G.TOP]+t[G.BOTTOM]-(this._padding[G.TOP]+this._padding[G.BOTTOM]),ti(this._padding,t),this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange("_padding"),this.notifyChange("_viewport"))}get viewProjectionMatrix(){return this._viewProjectionDirty&&(jt(this._viewProjectionMatrix,this.projectionMatrix,this.viewMatrix),this._viewProjectionDirty=!1),this._viewProjectionMatrix}get projectionMatrix(){const t=this.width,e=this.height,r=this.near*Math.tan(this.fovY/2),i=r*this._aspect,s=Jn(Y(),-i*(1+2*this._padding[G.LEFT]/t),i*(1+2*this._padding[G.RIGHT]/t),-r*(1+2*this._padding[G.BOTTOM]/e),r*(1+2*this._padding[G.TOP]/e),this.near,this.far),a=this._get("projectionMatrix");return a&&Qn(a,s)?a:s}get inverseProjectionMatrix(){return dr(Y(),this.projectionMatrix)||this._get("inverseProjectionMatrix")||Y()}get fov(){return this._fov}set fov(t){this._fov=t,this._viewProjectionDirty=!0,this._frustumDirty=!0}get fovX(){return xd(this._fov,this.width,this.height)}set fovX(t){this._fov=wd(t,this.width,this.height),this._viewProjectionDirty=!0,this._frustumDirty=!0}get fovY(){return Td(this._fov,this.width,this.height)}set fovY(t){this._fov=Sd(t,this.width,this.height),this._viewProjectionDirty=!0,this._frustumDirty=!0}get distance(){return Tt(this.center,this.eye)}get frustum(){return this._recomputeFrustum(),this._frustum}get viewInverseTransposeMatrix(){return(this._viewInverseTransposeMatrixDirty||this._viewDirty)&&(dr(this._viewInverseTransposeMatrix,this.viewMatrix),go(this._viewInverseTransposeMatrix,this._viewInverseTransposeMatrix),this._viewInverseTransposeMatrixDirty=!1),this._viewInverseTransposeMatrix}depthNDCToWorld(t){const e=2*t-1;return 2*this.near*this.far/(this.far+this.near-e*(this.far-this.near))}get perRenderPixelRatio(){return Math.tan(this.fovX/2)/(this.width/2)}get perScreenPixelRatio(){return this.perRenderPixelRatio*this.pixelRatio}get aboveGround(){return this.relativeElevation!=null&&this.relativeElevation>=0}copyFrom(t){k(this._ray.origin,t.eye),this.center=t.center,this.up=t.up,ti(this._viewport,t.viewport),this.notifyChange("_viewport"),ti(this._padding,t.padding),this.notifyChange("_padding"),Or(this._nearFar,t.nearFar),this.notifyChange("_nearFar"),this._fov=t.fov,this.relativeElevation=t.relativeElevation;const e=t;return this._viewDirty=e._viewDirty,this._viewDirty||(wt(this._viewMatrix,t.viewMatrix),k(this._viewRight,t.viewRight),k(this._viewUp,t.viewUp),k(this._viewForward,t.viewForward)),this._viewProjectionDirty=!0,this._frustumDirty=e._frustumDirty,this._frustumDirty||(od(this._frustum,t.frustum),this._frustumDirty=!1),e._viewInverseTransposeMatrixDirty?this._viewInverseTransposeMatrixDirty=!0:(wt(this._viewInverseTransposeMatrix,t.viewInverseTransposeMatrix),this._viewInverseTransposeMatrixDirty=!1),ti(this._fullViewport,t.fullViewport),this.pixelRatio=t.pixelRatio,this}copyViewFrom(t){this.eye=t.eye,this.center=t.center,this.up=t.up}clone(){return new bs().copyFrom(this)}equals(t){return ir(this.eye,t.eye)&&ir(this.center,t.center)&&ir(this.up,t.up)&&qi(this._viewport,t.viewport)&&qi(this._padding,t.padding)&&Kn(this.nearFar,t.nearFar)&&this._fov===t.fov&&this.pixelRatio===t.pixelRatio&&this.relativeElevation===t.relativeElevation}almostEquals(t){if(Math.abs(t.fov-this._fov)>=.001||ia(t.padding,this._padding)>=.5||ia(this.screenViewport,t.screenViewport)>=.5)return!1;sa(ge,t.eye,t.center),sa(Qi,this.eye,this.center);const e=ct(ge,Qi),r=aa(ge),i=aa(Qi),s=5e-4;return e*e>=(1-1e-10)*r*i&&el(t.eye,this.eye)<Math.max(r,i)*s*s}computeRenderPixelSizeAt(t){return this.computeRenderPixelSizeAtDist(this._viewDirectionDistance(t))}computeRenderPixelSizeAtDist(t){return t*this.perRenderPixelRatio}computeScreenPixelSizeAt(t){return this.computeScreenPixelSizeAtDist(this._viewDirectionDistance(t))}_viewDirectionDistance(t){return Math.abs(jl(this.viewForward,ne(ge,t,this.eye)))}computeScreenPixelSizeAtDist(t){return t*this.perScreenPixelRatio}computeDistanceFromRadius(t,e){return t/Math.tan(Math.min(this.fovX,this.fovY)/(2*(e||1)))}getScreenCenter(t=tl()){return t[0]=(this.padding[G.LEFT]+this.width/2)/this.pixelRatio,t[1]=(this.padding[G.TOP]+this.height/2)/this.pixelRatio,t}getRenderCenter(t,e=.5,r=.5){return t[0]=this.padding[G.LEFT]+this.width*e,t[1]=this.padding[G.BOTTOM]+this.height*r,t[2]=.5,t}setGLViewport(t){const e=this.viewport,r=this.padding;t.setViewport(e[0]-r[3],e[1]-r[2],e[2]+r[1]+r[3],e[3]+r[0]+r[2])}applyProjection(t,e){t!==M&&k(M,t),M[3]=1,sr(M,M,this.projectionMatrix);const r=Math.abs(M[3]);Z(M,M,1/r);const i=this.fullViewport;e[0]=Nt(0,i[0]+i[2],.5+.5*M[0]),e[1]=Nt(0,i[1]+i[3],.5+.5*M[1]),e[2]=.5*(M[2]+1),e[3]=r}unapplyProjection(t,e){const r=this.fullViewport;M[0]=(t[0]/(r[0]+r[2])*2-1)*t[3],M[1]=(t[1]/(r[1]+r[3])*2-1)*t[3],M[2]=(2*t[2]-1)*t[3],M[3]=t[3],this.inverseProjectionMatrix!=null&&(sr(M,M,this.inverseProjectionMatrix),e[0]=M[0],e[1]=M[1],e[2]=M[2])}projectToScreen(t,e){return this.projectToRenderScreen(t,Ki),this.renderToScreen(Ki,e),e}projectToRenderScreen(t,e){if(M[0]=t[0],M[1]=t[1],M[2]=t[2],M[3]=1,sr(M,M,this.viewProjectionMatrix),M[3]===0)return null;Z(M,M,1/Math.abs(M[3]));const r=this.fullViewport;return"x"in e?(e.x=Nt(0,r[0]+r[2],.5+.5*M[0]),e.y=Nt(0,r[1]+r[3],.5+.5*M[1])):(e[0]=Nt(0,r[0]+r[2],.5+.5*M[0]),e[1]=Nt(0,r[1]+r[3],.5+.5*M[1]),e.length>2&&(e[2]=.5*(M[2]+1))),e}unprojectFromScreen(t,e){return this.unprojectFromRenderScreen(this.screenToRender(t,Ki),e)}unprojectFromRenderScreen(t,e){if(jt(ii,this.projectionMatrix,this.viewMatrix),!dr(ii,ii))return null;const r=this.fullViewport;return M[0]=2*(t[0]-r[0])/r[2]-1,M[1]=2*(t[1]-r[1])/r[3]-1,M[2]=2*t[2]-1,M[3]=1,sr(M,M,ii),M[3]===0?null:(e[0]=M[0]/M[3],e[1]=M[1]/M[3],e[2]=M[2]/M[3],e)}constrainWindowSize(t,e,r,i){const s=t*this.pixelRatio,a=e*this.pixelRatio,o=Math.max(s-r/2,0),n=Math.max(this.fullHeight-a-i/2,0),c=-Math.min(s-r/2,0),l=-Math.min(this.fullHeight-a-i/2,0);return[o,n,r-c- -Math.min(this.fullWidth-s-r/2,0),i-l- -Math.min(a-i/2,0)]}computeUp(t){t===Ot.Global?this._computeUpGlobal():this._computeUpLocal()}screenToRender(t,e){const r=t[0]*this.pixelRatio,i=this.fullHeight-t[1]*this.pixelRatio;return e[0]=r,e[1]=i,e}renderToScreen(t,e){const r=t[0]/this.pixelRatio,i=(this.fullHeight-t[1])/this.pixelRatio;e[0]=r,e[1]=i}_computeUpGlobal(){ne(ge,this.center,this.eye);const t=nt(this.center);t<1?(H(this._up,0,0,1),this._markViewDirty(),this.notifyChange("_up")):Math.abs(ct(ge,this.center))>.9999*nt(ge)*t||(rt(this._up,ge,this.center),rt(this._up,this._up,ge),de(this._up,this._up),this.notifyChange("_up"),this._markViewDirty())}_computeUpLocal(){rl(ge,this.eye,this.center),Math.abs(ge[2])<=.9999&&(Z(ge,ge,ge[2]),H(this._up,-ge[0],-ge[1],1-ge[2]),de(this._up,this._up),this.notifyChange("_up"),this._markViewDirty())}_compareAndSetView(t,e,r=""){typeof t[0]=="number"&&isFinite(t[0])&&typeof t[1]=="number"&&isFinite(t[1])&&typeof t[2]=="number"&&isFinite(t[2])?ir(t,e)||(k(e,t),this._markViewDirty(),r.length&&this.notifyChange(r)):Kr.getLogger("esri.views.3d.webgl-engine.lib.Camera").warn("Camera vector contains invalid number, ignoring value")}_markViewDirty(){this._viewDirty=!0,this._frustumDirty=!0,this._viewProjectionDirty=!0}_recomputeFrustum(){this._frustumDirty&&(nd(this.viewMatrix,this.projectionMatrix,this._frustum),this._frustumDirty=!1)}_ensureViewClean(){this._viewDirty&&(vo(this._viewMatrix,this.eye,this.center,this.up),H(this._viewForward,-this._viewMatrix[2],-this._viewMatrix[6],-this._viewMatrix[10]),H(this._viewUp,this._viewMatrix[1],this._viewMatrix[5],this._viewMatrix[9]),H(this._viewRight,this._viewMatrix[0],this._viewMatrix[4],this._viewMatrix[8]),this._viewDirty=!1,this._viewInverseTransposeMatrixDirty=!0)}};v([L()],U.prototype,"_center",void 0),v([L()],U.prototype,"_up",void 0),v([L()],U.prototype,"_viewport",void 0),v([L()],U.prototype,"_padding",void 0),v([L()],U.prototype,"_fov",void 0),v([L()],U.prototype,"_nearFar",void 0),v([L()],U.prototype,"_pixelRatio",void 0),v([L()],U.prototype,"pixelRatio",null),v([L()],U.prototype,"eye",null),v([L()],U.prototype,"center",null),v([L()],U.prototype,"up",null),v([L({readOnly:!0})],U.prototype,"nearFar",null),v([L()],U.prototype,"near",null),v([L()],U.prototype,"far",null),v([L()],U.prototype,"viewport",null),v([L({readOnly:!0})],U.prototype,"screenViewport",null),v([L()],U.prototype,"x",null),v([L()],U.prototype,"y",null),v([L()],U.prototype,"width",null),v([L()],U.prototype,"height",null),v([L()],U.prototype,"fullWidth",null),v([L()],U.prototype,"fullHeight",null),v([L({readOnly:!0})],U.prototype,"_aspect",null),v([L()],U.prototype,"padding",null),v([L({readOnly:!0})],U.prototype,"projectionMatrix",null),v([L({readOnly:!0})],U.prototype,"inverseProjectionMatrix",null),v([L()],U.prototype,"fov",null),v([L()],U.prototype,"fovX",null),v([L()],U.prototype,"fovY",null),U=bs=v([Gi("esri.views.3d.webgl-engine.lib.Camera")],U);const M=ke(),ii=Y(),ge=$(),Qi=$(),Ki=Le();var G;(function(t){t[t.TOP=0]="TOP",t[t.RIGHT=1]="RIGHT",t[t.BOTTOM=2]="BOTTOM",t[t.LEFT=3]="LEFT"})(G||(G={}));let Od=class{constructor(e,r,i,s){this._textureRepository=e,this._techniqueRepository=r,this.materialChanged=i,this.requestRender=s,this._id2glMaterialRef=new Xs}dispose(){this._textureRepository.destroy()}acquire(e,r,i){if(this._ownMaterial(e),!e.requiresSlot(r,i))return null;let s=this._id2glMaterialRef.get(i,e.id);if(N(s)){const a=e.createGLMaterial({material:e,techniqueRep:this._techniqueRepository,textureRep:this._textureRepository,output:i});s=new Cd(a),this._id2glMaterialRef.set(i,e.id,s)}return s.ref(),s.glMaterial}release(e,r){const i=this._id2glMaterialRef.get(r,e.id);A(i)&&(i.unref(),i.referenced||(We(i.glMaterial),this._id2glMaterialRef.delete(r,e.id)))}_ownMaterial(e){A(e.repository)&&e.repository!==this&&Kr.getLogger("esri.views.3d.webgl-engine.lib.GLMaterialRepository").error("Material is already owned by a different material repository"),e.repository=this}},Cd=class{constructor(e){this.glMaterial=e,this._refCnt=0}ref(){++this._refCnt}unref(){--this._refCnt,Te(this._refCnt>=0)}get referenced(){return this._refCnt>0}};const bd={rootOrigin:null};let Ad=class{constructor(){this.enabled=!0,this._time=Sa(0)}get time(){return this._time}advance({deltaTime:e,fixedTime:r}){return A(r)?this._time!==r&&(this._time=r,!0):(this._time=Sa(this._time+e),e!==0)}};var xr,kr;(function(t){t[t.Draped=0]="Draped",t[t.Screen=1]="Screen",t[t.World=2]="World",t[t.COUNT=3]="COUNT"})(xr||(xr={})),function(t){t[t.Center=0]="Center",t[t.Tip=1]="Tip",t[t.COUNT=2]="COUNT"}(kr||(kr={}));let fe=class extends br{constructor(){super(...arguments),this.output=T.Color,this.transparencyPassType=re.NONE,this.occluder=!1,this.hasSlicePlane=!1,this.writeDepth=!1,this.space=xr.Screen,this.hideOnShortSegments=!1,this.hasCap=!1,this.anchor=kr.Center,this.hasTip=!1,this.vvSize=!1,this.vvColor=!1,this.vvOpacity=!1,this.hasOccludees=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1}};v([x({count:T.COUNT})],fe.prototype,"output",void 0),v([x({count:re.COUNT})],fe.prototype,"transparencyPassType",void 0),v([x()],fe.prototype,"occluder",void 0),v([x()],fe.prototype,"hasSlicePlane",void 0),v([x()],fe.prototype,"writeDepth",void 0),v([x({count:xr.COUNT})],fe.prototype,"space",void 0),v([x()],fe.prototype,"hideOnShortSegments",void 0),v([x()],fe.prototype,"hasCap",void 0),v([x({count:kr.COUNT})],fe.prototype,"anchor",void 0),v([x()],fe.prototype,"hasTip",void 0),v([x()],fe.prototype,"vvSize",void 0),v([x()],fe.prototype,"vvColor",void 0),v([x()],fe.prototype,"vvOpacity",void 0),v([x()],fe.prototype,"hasOccludees",void 0),v([x()],fe.prototype,"hasMultipassTerrain",void 0),v([x()],fe.prototype,"cullAboveGround",void 0),v([x({constValue:!0})],fe.prototype,"hasVvInstancing",void 0),v([x({constValue:!0})],fe.prototype,"hasSliceTranslatedView",void 0);const Pa=8;function Rd(t,e){const r=t.vertex;r.uniforms.add(new B("intrinsicWidth",i=>i.width)),e.vvSize?(t.attributes.add(h.SIZEFEATUREATTRIBUTE,"float"),r.uniforms.add(new ar("vvSizeMinSize",i=>i.vvSizeMinSize)),r.uniforms.add(new ar("vvSizeMaxSize",i=>i.vvSizeMaxSize)),r.uniforms.add(new ar("vvSizeOffset",i=>i.vvSizeOffset)),r.uniforms.add(new ar("vvSizeFactor",i=>i.vvSizeFactor)),r.code.add(m`float getSize() {
return intrinsicWidth * clamp(vvSizeOffset + sizeFeatureAttribute * vvSizeFactor, vvSizeMinSize, vvSizeMaxSize).x;
}`)):(t.attributes.add(h.SIZE,"float"),r.code.add(m`float getSize(){
return intrinsicWidth * size;
}`)),e.vvOpacity?(t.attributes.add(h.OPACITYFEATUREATTRIBUTE,"float"),r.constants.add("vvOpacityNumber","int",8),r.uniforms.add([new fi("vvOpacityValues",i=>i.vvOpacityValues,Pa),new fi("vvOpacityOpacities",i=>i.vvOpacityOpacities,Pa)]),r.code.add(m`float interpolateOpacity( float value ){
if (value <= vvOpacityValues[0]) {
return vvOpacityOpacities[0];
}
for (int i = 1; i < vvOpacityNumber; ++i) {
if (vvOpacityValues[i] >= value) {
float f = (value - vvOpacityValues[i-1]) / (vvOpacityValues[i] - vvOpacityValues[i-1]);
return mix(vvOpacityOpacities[i-1], vvOpacityOpacities[i], f);
}
}
return vvOpacityOpacities[vvOpacityNumber - 1];
}
vec4 applyOpacity( vec4 color ){
return vec4(color.xyz, interpolateOpacity(opacityFeatureAttribute));
}`)):r.code.add(m`vec4 applyOpacity( vec4 color ){
return color;
}`),e.vvColor?(t.attributes.add(h.COLORFEATUREATTRIBUTE,"float"),r.constants.add("vvColorNumber","int",zr),r.uniforms.add(new fi("vvColorValues",i=>i.vvColorValues,zr)),r.uniforms.add(new wo("vvColorColors",i=>i.vvColorColors,zr)),r.code.add(m`vec4 interpolateColor( float value ) {
if (value <= vvColorValues[0]) {
return vvColorColors[0];
}
for (int i = 1; i < vvColorNumber; ++i) {
if (vvColorValues[i] >= value) {
float f = (value - vvColorValues[i-1]) / (vvColorValues[i] - vvColorValues[i-1]);
return mix(vvColorColors[i-1], vvColorColors[i], f);
}
}
return vvColorColors[vvColorNumber - 1];
}
vec4 getColor(){
return applyOpacity(interpolateColor(colorFeatureAttribute));
}`)):(t.attributes.add(h.COLOR,"vec4"),r.code.add(m`vec4 getColor(){
return applyOpacity(color);
}`))}let Pd=class{constructor(e){this._rctx=e,this._cache=new Map}destroy(){this._cache.forEach(e=>We(e.stippleTexture)),this._cache.clear()}_acquire(e){if(N(e))return null;const r=this._patternId(e),i=this._cache.get(r);if(i)return i.refCount++,i;const{encodedData:s,paddedPixels:a}=Dd(e),o=new Ed(new or(this._rctx,{width:a,height:1,internalFormat:wr.RGBA,pixelFormat:wr.RGBA,dataType:Wr.UNSIGNED_BYTE,wrapMode:Sr.CLAMP_TO_EDGE},s));return this._cache.set(r,o),o}release(e){if(N(e))return;const r=this._patternId(e),i=this._cache.get(r);i&&(i.refCount--,i.refCount===0&&(A(i.stippleTexture)&&i.stippleTexture.dispose(),this._cache.delete(r)))}swap(e,r){const i=this._acquire(r);return this.release(e),i}_patternId(e){return`${e.pattern.join(",")}-r${e.pixelRatio}`}},Ed=class extends qc{constructor(e){super(),this.stippleTexture=e,this.refCount=1}};function Dd(t){const e=Zs(t),r=1/t.pixelRatio,i=Jo(t),s=Qo(t),a=(Math.floor(.5*(s-1))+.5)*r,o=[];let n=1;for(const f of e){for(let g=0;g<f;g++){const _=n*(Math.min(g,f-1-g)+.5)*r/a*.5+.5;o.push(_)}n=-n}const c=Math.round(e[0]/2),l=[...o.slice(c),...o.slice(0,c)],d=i+Ko,p=new Uint8Array(4*d);let u=4;for(const f of l)Xo(f,p,u),u+=4;return p.copyWithin(0,u-4,u),p.copyWithin(u,4,8),{encodedData:p,paddedPixels:d}}function Zs(t){return t.pattern.map(e=>Math.round(e*t.pixelRatio))}function Jo(t){if(N(t))return 1;const e=Zs(t);return Math.floor(e.reduce((r,i)=>r+i))}function Qo(t){return Zs(t).reduce((e,r)=>Math.max(e,r))}const Ko=2;function $d(t){return N(t)?Ds:t.length===4?t:Gr(Id,t[0],t[1],t[2],1)}const Id=ke();function en(t,e){t.constants.add("stippleAlphaColorDiscard","float",.001),t.constants.add("stippleAlphaHighlightDiscard","float",.5),e.stippleEnabled?Md(t,e):Ld(t)}function Md(t,e){const r=!(e.draped&&e.stipplePreferContinuous),{vertex:i,fragment:s}=t;s.include($s),e.draped||(Is(i,e),i.uniforms.add(new B("worldToScreenPerDistanceRatio",(o,n)=>1/n.camera.perScreenPixelRatio)),i.code.add(m`float computeWorldToScreenRatio(vec3 segmentCenter) {
float segmentDistanceToCamera = length(segmentCenter - cameraPosition);
return worldToScreenPerDistanceRatio / segmentDistanceToCamera;
}`)),t.varyings.add("vStippleDistance","float"),e.stippleRequiresClamp&&t.varyings.add("vStippleDistanceLimits","vec2"),e.stippleRequiresStretchMeasure&&t.varyings.add("vStipplePatternStretch","float"),i.code.add(m`
    float discretizeWorldToScreenRatio(float worldToScreenRatio) {
      float step = ${Fd};

      float discreteWorldToScreenRatio = log(worldToScreenRatio);
      discreteWorldToScreenRatio = ceil(discreteWorldToScreenRatio / step) * step;
      discreteWorldToScreenRatio = exp(discreteWorldToScreenRatio);
      return discreteWorldToScreenRatio;
    }
  `),i.code.add(m`vec2 computeStippleDistanceLimits(float startPseudoScreen, float segmentLengthPseudoScreen, float segmentLengthScreen, float patternLength) {`),i.code.add(m`
    if (segmentLengthPseudoScreen >= ${r?"patternLength":"1e4"}) {
  `),i.uniforms.add(new B("pixelRatio",(o,n)=>n.camera.pixelRatio)),i.code.add(m`
        // Round the screen length to get an integer number of pattern repetitions (minimum 1).
        float repetitions = segmentLengthScreen / (patternLength * pixelRatio);
        float flooredRepetitions = max(1.0, floor(repetitions + 0.5));
        float segmentLengthScreenRounded = flooredRepetitions * patternLength;

        ${e.stippleRequiresStretchMeasure?m`
              float stretch = repetitions / flooredRepetitions;

              // We need to impose a lower bound on the stretch factor to prevent the dots from merging together when there is only 1 repetition.
              // 0.75 is the lowest possible stretch value for flooredRepetitions > 1, so it makes sense as lower bound.
              vStipplePatternStretch = max(0.75, stretch);`:""}

        return vec2(0.0, segmentLengthScreenRounded);
      }
      return vec2(startPseudoScreen, startPseudoScreen + segmentLengthPseudoScreen);
    }
  `),s.constants.add("stippleTexturePadding","float",Ko);const a=e.hasWebGL2Context?Ti.None:Ti.Size;s.uniforms.add(So("stipplePatternTexture",o=>o.stippleTexture,a)),s.uniforms.add([new B("stipplePatternSDFNormalizer",o=>Nd(o.stipplePattern)),new B("stipplePatternPixelSizeInv",o=>1/Ys(o))]),s.code.add(m`
    float padStippleTexture(float u) {
      float paddedTextureSize = ${Hl(e,"stipplePatternTexture")}.x;
      float unpaddedTextureSize = paddedTextureSize - stippleTexturePadding;

      return (u * unpaddedTextureSize + stippleTexturePadding * 0.5) / paddedTextureSize;
    }
  `),s.code.add(m`
    float getStippleSDF(out bool isClamped) {
      ${e.stippleRequiresClamp?m`
          float stippleDistanceClamped = clamp(vStippleDistance, vStippleDistanceLimits.x, vStippleDistanceLimits.y);
          vec2 aaCorrectedLimits = vStippleDistanceLimits + vec2(1.0, -1.0) / gl_FragCoord.w;
          isClamped = vStippleDistance < aaCorrectedLimits.x || vStippleDistance > aaCorrectedLimits.y;`:m`
          float stippleDistanceClamped = vStippleDistance;
          isClamped = false;`}

      float u = stippleDistanceClamped * gl_FragCoord.w * stipplePatternPixelSizeInv;
      ${e.stippleScaleWithLineWidth?m`u *= vLineSizeInv;`:""}
      u = padStippleTexture(fract(u));

      float encodedSDF = rgba2float(texture2D(stipplePatternTexture, vec2(u, 0.5)));
      float sdf = (encodedSDF * 2.0 - 1.0) * stipplePatternSDFNormalizer;

      ${e.stippleRequiresStretchMeasure?m`return (sdf - 0.5) * vStipplePatternStretch + 0.5;`:m`return sdf;`}
    }

    float getStippleSDF() {
      bool ignored;
      return getStippleSDF(ignored);
    }

    float getStippleAlpha() {
      bool isClamped;
      float stippleSDF = getStippleSDF(isClamped);

      float antiAliasedResult = ${e.stippleScaleWithLineWidth?m`clamp(stippleSDF * vLineWidth + 0.5, 0.0, 1.0);`:m`clamp(stippleSDF + 0.5, 0.0, 1.0);`}

      return isClamped ? floor(antiAliasedResult + 0.5) : antiAliasedResult;
    }
  `),e.stippleOffColorEnabled?(s.uniforms.add(new Ee("stippleOffColor",o=>$d(o.stippleOffColor))),s.code.add(m`#define discardByStippleAlpha(stippleAlpha, threshold) {}
#define blendStipple(color, stippleAlpha) mix(color, stippleOffColor, stippleAlpha)`)):s.code.add(m`#define discardByStippleAlpha(stippleAlpha, threshold) if (stippleAlpha < threshold) { discard; }
#define blendStipple(color, stippleAlpha) vec4(color.rgb, color.a * stippleAlpha)`)}function Ld(t){t.fragment.code.add(m`float getStippleAlpha() { return 1.0; }
#define discardByStippleAlpha(_stippleAlpha_, _threshold_) {}
#define blendStipple(color, _stippleAlpha_) color`)}function Nd(t){return A(t)?(Math.floor(.5*(Qo(t)-1))+.5)/t.pixelRatio:1}function Ys(t){const e=t.stipplePattern;return A(e)?Jo(t.stipplePattern)/e.pixelRatio:1}const Fd=m.float(.4),Js=128,tn=.5;function zd(t,e=Js,r=e*tn,i=0){const s=jd(t,e,r,i);return new Gl(s,{mipmap:!1,wrap:{s:Sr.CLAMP_TO_EDGE,t:Sr.CLAMP_TO_EDGE},width:e,height:e,components:4,noUnpackFlip:!0})}function jd(t,e=Js,r=e*tn,i=0){switch(t){case"circle":default:return Vd(e,r);case"square":return Ud(e,r);case"cross":return Gd(e,r,i);case"x":return Wd(e,r,i);case"kite":return Hd(e,r);case"triangle":return Bd(e,r);case"arrow":return kd(e,r)}}function Vd(t,e){const r=t/2-.5;return ei(t,an(r,r,e/2))}function Ud(t,e){return rn(t,e,!1)}function Hd(t,e){return rn(t,e,!0)}function Gd(t,e,r=0){return sn(t,e,!1,r)}function Wd(t,e,r=0){return sn(t,e,!0,r)}function Bd(t,e){return ei(t,on(t/2,e,e/2))}function kd(t,e){const r=e,i=e/2,s=t/2,a=.8*r,o=an(s,(t-e)/2-a,Math.sqrt(a*a+i*i)),n=on(s,r,i);return ei(t,(c,l)=>Math.max(n(c,l),-o(c,l)))}function rn(t,e,r){return r&&(e/=Math.SQRT2),ei(t,(i,s)=>{let a=i-.5*t+.25,o=.5*t-s-.75;if(r){const n=(a+o)/Math.SQRT2;o=(o-a)/Math.SQRT2,a=n}return Math.max(Math.abs(a),Math.abs(o))-.5*e})}function sn(t,e,r,i=0){e-=i,r&&(e*=Math.SQRT2);const s=.5*e;return ei(t,(a,o)=>{let n,c=a-.5*t,l=.5*t-o-1;if(r){const d=(c+l)/Math.SQRT2;l=(l-c)/Math.SQRT2,c=d}return c=Math.abs(c),l=Math.abs(l),n=c>l?c>s?Math.sqrt((c-s)*(c-s)+l*l):l:l>s?Math.sqrt(c*c+(l-s)*(l-s)):c,n-=i/2,n})}function an(t,e,r){return(i,s)=>{const a=i-t,o=s-e;return Math.sqrt(a*a+o*o)-r}}function on(t,e,r){const i=Math.sqrt(e*e+r*r);return(s,a)=>{const o=Math.abs(s-t)-r,n=a-t+e/2+.75,c=(e*o+r*n)/i,l=-n;return Math.max(c,l)}}function ei(t,e){const r=new Uint8Array(4*t*t);for(let i=0;i<t;i++)for(let s=0;s<t;s++){const a=s+t*i;let o=e(s,i);o=o/t+.5,Xo(o,r,4*a)}return r}const Qs=64,nn=Qs/2,ln=nn/5,qd=Qs/ln,jf=.25;function Vf(t,e){return t.fromData(`${e}-marker`,()=>zd(e,Qs,nn,ln))}function Xd(t,e){const r=t.vertex;t.constants.add("markerSizePerLineWidth","float",qd),r.uniforms.add(new B("pixelRatio",(i,s)=>s.camera.pixelRatio)),N(r.uniforms.get("markerScale"))&&r.constants.add("markerScale","float",1),r.code.add(m`float getLineWidth() {
return max(getSize(), 1.0) * pixelRatio;
}
float getScreenMarkerSize() {
return markerSizePerLineWidth * markerScale * getLineWidth();
}`),e.space===xr.World&&(r.constants.add("maxSegmentLengthFraction","float",.45),r.uniforms.add(new B("perRenderPixelRatio",(i,s)=>s.camera.perRenderPixelRatio)),r.code.add(m`float getWorldMarkerSize(vec4 pos) {
float distanceToCamera = length(pos.xyz);
float screenToWorldRatio = perRenderPixelRatio * distanceToCamera * 0.5;
return getScreenMarkerSize() * screenToWorldRatio;
}
bool areWorldMarkersHidden(vec4 pos, vec4 other) {
vec3 midPoint = mix(pos.xyz, other.xyz, 0.5);
float distanceToCamera = length(midPoint);
float screenToWorldRatio = perRenderPixelRatio * distanceToCamera * 0.5;
float worldMarkerSize = getScreenMarkerSize() * screenToWorldRatio;
float segmentLen = length(pos.xyz - other.xyz);
return worldMarkerSize > maxSegmentLengthFraction * segmentLen;
}`))}var Gt;(function(t){t[t.BUTT=0]="BUTT",t[t.SQUARE=1]="SQUARE",t[t.ROUND=2]="ROUND",t[t.COUNT=3]="COUNT"})(Gt||(Gt={}));let q=class extends br{constructor(){super(...arguments),this.output=T.Color,this.capType=Gt.BUTT,this.transparencyPassType=re.NONE,this.occluder=!1,this.hasSlicePlane=!1,this.hasPolygonOffset=!1,this.writeDepth=!1,this.draped=!1,this.stippleEnabled=!1,this.stippleOffColorEnabled=!1,this.stippleScaleWithLineWidth=!1,this.stipplePreferContinuous=!0,this.roundJoins=!1,this.applyMarkerOffset=!1,this.vvSize=!1,this.vvColor=!1,this.vvOpacity=!1,this.falloffEnabled=!1,this.innerColorEnabled=!1,this.hasOccludees=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.wireframe=!1,this.objectAndLayerIdColorInstanced=!1}};v([x({count:T.COUNT})],q.prototype,"output",void 0),v([x({count:Gt.COUNT})],q.prototype,"capType",void 0),v([x({count:re.COUNT})],q.prototype,"transparencyPassType",void 0),v([x()],q.prototype,"occluder",void 0),v([x()],q.prototype,"hasSlicePlane",void 0),v([x()],q.prototype,"hasPolygonOffset",void 0),v([x()],q.prototype,"writeDepth",void 0),v([x()],q.prototype,"draped",void 0),v([x()],q.prototype,"stippleEnabled",void 0),v([x()],q.prototype,"stippleOffColorEnabled",void 0),v([x()],q.prototype,"stippleScaleWithLineWidth",void 0),v([x()],q.prototype,"stipplePreferContinuous",void 0),v([x()],q.prototype,"roundJoins",void 0),v([x()],q.prototype,"applyMarkerOffset",void 0),v([x()],q.prototype,"vvSize",void 0),v([x()],q.prototype,"vvColor",void 0),v([x()],q.prototype,"vvOpacity",void 0),v([x()],q.prototype,"falloffEnabled",void 0),v([x()],q.prototype,"innerColorEnabled",void 0),v([x()],q.prototype,"hasOccludees",void 0),v([x()],q.prototype,"hasMultipassTerrain",void 0),v([x()],q.prototype,"cullAboveGround",void 0),v([x()],q.prototype,"wireframe",void 0),v([x({constValue:!0})],q.prototype,"stippleRequiresClamp",void 0),v([x({constValue:!0})],q.prototype,"stippleRequiresStretchMeasure",void 0),v([x({constValue:!0})],q.prototype,"hasVvInstancing",void 0),v([x({constValue:!0})],q.prototype,"hasSliceTranslatedView",void 0),v([x()],q.prototype,"objectAndLayerIdColorInstanced",void 0);const qr=1;function cn(t){const e=new Yr,{vertex:r,fragment:i}=e,s=t.hasMultipassTerrain&&(t.output===T.Color||t.output===T.Alpha);e.include(Wl),e.include(Rd,t),e.include(en,t);const a=t.applyMarkerOffset&&!t.draped;a&&(r.uniforms.add(new B("markerScale",f=>f.markerScale)),e.include(Xd,{space:xr.World})),t.output===T.Depth&&e.include(xo,t),e.include(Ms,t),Jr(r,t),r.uniforms.add([new _r("inverseProjectionMatrix",(f,g)=>g.camera.inverseProjectionMatrix),new St("nearFar",(f,g)=>g.camera.nearFar),new B("miterLimit",f=>f.join!=="miter"?0:f.miterLimit),new Ee("viewport",(f,g)=>g.camera.fullViewport)]),r.constants.add("LARGE_HALF_FLOAT","float",65500),e.attributes.add(h.POSITION,"vec3"),e.attributes.add(h.SUBDIVISIONFACTOR,"float"),e.attributes.add(h.UV0,"vec2"),e.attributes.add(h.AUXPOS1,"vec3"),e.attributes.add(h.AUXPOS2,"vec3"),e.varyings.add("vColor","vec4"),e.varyings.add("vpos","vec3"),To(e),s&&e.varyings.add("depth","float");const o=t.capType===Gt.ROUND,n=t.stippleEnabled&&t.stippleScaleWithLineWidth||o;n&&e.varyings.add("vLineWidth","float");const c=t.stippleEnabled&&t.stippleScaleWithLineWidth;c&&e.varyings.add("vLineSizeInv","float");const l=t.innerColorEnabled||o;l&&e.varyings.add("vLineDistance","float");const d=t.stippleEnabled&&o,p=t.falloffEnabled||d;p&&e.varyings.add("vLineDistanceNorm","float"),o&&(e.varyings.add("vSegmentSDF","float"),e.varyings.add("vReverseSegmentSDF","float")),r.code.add(m`#define PERPENDICULAR(v) vec2(v.y, -v.x);
float interp(float ncp, vec4 a, vec4 b) {
return (-ncp - a.z) / (b.z - a.z);
}
vec2 rotate(vec2 v, float a) {
float s = sin(a);
float c = cos(a);
mat2 m = mat2(c, -s, s, c);
return m * v;
}`),r.code.add(m`vec4 projectAndScale(vec4 pos) {
vec4 posNdc = proj * pos;
posNdc.xy *= viewport.zw / posNdc.w;
return posNdc;
}`),Bl(e),r.code.add(m`
    void clipAndTransform(inout vec4 pos, inout vec4 prev, inout vec4 next, in bool isStartVertex) {
      float vnp = nearFar[0] * 0.99;

      if(pos.z > -nearFar[0]) {
        //current pos behind ncp --> we need to clip
        if (!isStartVertex) {
          if(prev.z < -nearFar[0]) {
            //previous in front of ncp
            pos = mix(prev, pos, interp(vnp, prev, pos));
            next = pos;
          } else {
            pos = vec4(0.0, 0.0, 0.0, 1.0);
          }
        } else {
          if(next.z < -nearFar[0]) {
            //next in front of ncp
            pos = mix(pos, next, interp(vnp, pos, next));
            prev = pos;
          } else {
            pos = vec4(0.0, 0.0, 0.0, 1.0);
          }
        }
      } else {
        //current position visible
        if (prev.z > -nearFar[0]) {
          //previous behind ncp
          prev = mix(pos, prev, interp(vnp, pos, prev));
        }
        if (next.z > -nearFar[0]) {
          //next behind ncp
          next = mix(next, pos, interp(vnp, next, pos));
        }
      }

      ${s?"depth = pos.z;":""}
      linearDepth = calculateLinearDepth(nearFar,pos.z);

      pos = projectAndScale(pos);
      next = projectAndScale(next);
      prev = projectAndScale(prev);
    }
  `),r.uniforms.add(new B("pixelRatio",(f,g)=>g.camera.pixelRatio)),r.code.add(m`
  void main(void) {
    // unpack values from uv0.y
    bool isStartVertex = abs(abs(uv0.y)-3.0) == 1.0;

    float coverage = 1.0;

    // Check for special value of uv0.y which is used by the Renderer when graphics
    // are removed before the VBO is recompacted. If this is the case, then we just
    // project outside of clip space.
    if (uv0.y == 0.0) {
      // Project out of clip space
      gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
    }
    else {
      bool isJoin = abs(uv0.y) < 3.0;

      float lineSize = getSize();
      float lineWidth = lineSize * pixelRatio;

      ${n?m`vLineWidth = lineWidth;`:""}
      ${c?m`vLineSizeInv = 1.0 / lineSize;`:""}

      // convert sub-pixel coverage to alpha
      if (lineWidth < 1.0) {
        coverage = lineWidth;
        lineWidth = 1.0;
      }else{
        // Ribbon lines cannot properly render non-integer sizes. Round width to integer size if
        // larger than one for better quality. Note that we do render < 1 pixels more or less correctly
        // so we only really care to round anything larger than 1.
        lineWidth = floor(lineWidth + 0.5);
      }

      vec4 pos  = view * vec4(position.xyz, 1.0);
      vec4 prev = view * vec4(auxpos1.xyz, 1.0);
      vec4 next = view * vec4(auxpos2.xyz, 1.0);
  `),a&&r.code.add(m`vec4 other = isStartVertex ? next : prev;
bool markersHidden = areWorldMarkersHidden(pos, other);
if(!isJoin && !markersHidden) {
pos.xyz += normalize(other.xyz - pos.xyz) * getWorldMarkerSize(pos) * 0.5;
}`),r.code.add(m`clipAndTransform(pos, prev, next, isStartVertex);
vec2 left = (pos.xy - prev.xy);
vec2 right = (next.xy - pos.xy);
float leftLen = length(left);
float rightLen = length(right);`),(t.stippleEnabled||o)&&r.code.add(m`
      float isEndVertex = float(!isStartVertex);
      vec2 segmentOrigin = mix(pos.xy, prev.xy, isEndVertex);
      vec2 segment = mix(right, left, isEndVertex);
      ${o?m`vec2 segmentEnd = mix(next.xy, pos.xy, isEndVertex);`:""}
    `),r.code.add(m`left = (leftLen > 0.001) ? left/leftLen : vec2(0.0, 0.0);
right = (rightLen > 0.001) ? right/rightLen : vec2(0.0, 0.0);
vec2 capDisplacementDir = vec2(0, 0);
vec2 joinDisplacementDir = vec2(0, 0);
float displacementLen = lineWidth;
if (isJoin) {
bool isOutside = (left.x * right.y - left.y * right.x) * uv0.y > 0.0;
joinDisplacementDir = normalize(left + right);
joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);
if (leftLen > 0.001 && rightLen > 0.001) {
float nDotSeg = dot(joinDisplacementDir, left);
displacementLen /= length(nDotSeg * left - joinDisplacementDir);
if (!isOutside) {
displacementLen = min(displacementLen, min(leftLen, rightLen)/abs(nDotSeg));
}
}
if (isOutside && (displacementLen > miterLimit * lineWidth)) {`),t.roundJoins?r.code.add(m`
        vec2 startDir = leftLen < 0.001 ? right : left;
        startDir = PERPENDICULAR(startDir);

        vec2 endDir = rightLen < 0.001 ? left : right;
        endDir = PERPENDICULAR(endDir);

        float factor = ${t.stippleEnabled?m`min(1.0, subdivisionFactor * ${m.float((qr+2)/(qr+1))})`:m`subdivisionFactor`};

        float rotationAngle = acos(clamp(dot(startDir, endDir), -1.0, 1.0));
        joinDisplacementDir = rotate(startDir, -sign(uv0.y) * factor * rotationAngle);
      `):r.code.add(m`if (leftLen < 0.001) {
joinDisplacementDir = right;
}
else if (rightLen < 0.001) {
joinDisplacementDir = left;
}
else {
joinDisplacementDir = (isStartVertex || subdivisionFactor > 0.0) ? right : left;
}
joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);`);const u=t.capType!==Gt.BUTT;return r.code.add(m`
        displacementLen = lineWidth;
      }
    } else {
      // CAP handling ---------------------------------------------------
      joinDisplacementDir = isStartVertex ? right : left;
      joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);

      ${u?m`capDisplacementDir = isStartVertex ? -right : left;`:""}
    }
  `),r.code.add(m`
    // Displacement (in pixels) caused by join/or cap
    vec2 dpos = joinDisplacementDir * sign(uv0.y) * displacementLen + capDisplacementDir * displacementLen;

    ${p||l?m`float lineDistNorm = sign(uv0.y) * pos.w;`:""}

    ${l?m`vLineDistance = lineWidth * lineDistNorm;`:""}
    ${p?m`vLineDistanceNorm = lineDistNorm;`:""}

    pos.xy += dpos;
  `),o&&r.code.add(m`vec2 segmentDir = normalize(segment);
vSegmentSDF = (isJoin && isStartVertex) ? LARGE_HALF_FLOAT : (dot(pos.xy - segmentOrigin, segmentDir) * pos.w) ;
vReverseSegmentSDF = (isJoin && !isStartVertex) ? LARGE_HALF_FLOAT : (dot(pos.xy - segmentEnd, -segmentDir) * pos.w);`),t.stippleEnabled&&(t.draped?r.uniforms.add(new B("worldToScreenRatio",(f,g)=>1/g.screenToPCSRatio)):r.code.add(m`vec3 segmentCenter = mix((auxpos2 + position) * 0.5, (position + auxpos1) * 0.5, isEndVertex);
float worldToScreenRatio = computeWorldToScreenRatio(segmentCenter);`),r.code.add(m`float segmentLengthScreenDouble = length(segment);
float segmentLengthScreen = segmentLengthScreenDouble * 0.5;
float discreteWorldToScreenRatio = discretizeWorldToScreenRatio(worldToScreenRatio);
float segmentLengthRender = length(mix(auxpos2 - position, position - auxpos1, isEndVertex));
vStipplePatternStretch = worldToScreenRatio / discreteWorldToScreenRatio;`),t.draped?r.code.add(m`float segmentLengthPseudoScreen = segmentLengthScreen / pixelRatio * discreteWorldToScreenRatio / worldToScreenRatio;
float startPseudoScreen = uv0.x * discreteWorldToScreenRatio - mix(0.0, segmentLengthPseudoScreen, isEndVertex);`):r.code.add(m`float startPseudoScreen = mix(uv0.x, uv0.x - segmentLengthRender, isEndVertex) * discreteWorldToScreenRatio;
float segmentLengthPseudoScreen = segmentLengthRender * discreteWorldToScreenRatio;`),r.uniforms.add(new B("stipplePatternPixelSize",f=>Ys(f))),r.code.add(m`
      float patternLength = ${t.stippleScaleWithLineWidth?"lineSize * ":""} stipplePatternPixelSize;

      // Compute the coordinates at both start and end of the line segment, because we need both to clamp to in the fragment shader
      vStippleDistanceLimits = computeStippleDistanceLimits(startPseudoScreen, segmentLengthPseudoScreen, segmentLengthScreen, patternLength);

      vStippleDistance = mix(vStippleDistanceLimits.x, vStippleDistanceLimits.y, isEndVertex);

      // Adjust the coordinate to the displaced position (the pattern is shortened/overextended on the in/outside of joins)
      if (segmentLengthScreenDouble >= 0.001) {
        // Project the actual vertex position onto the line segment. Note that the resulting factor is within [0..1] at the
        // original vertex positions, and slightly outside of that range at the displaced positions
        vec2 stippleDisplacement = pos.xy - segmentOrigin;
        float stippleDisplacementFactor = dot(segment, stippleDisplacement) / (segmentLengthScreenDouble * segmentLengthScreenDouble);

        // Apply this offset to the actual vertex coordinate (can be screen or pseudo-screen space)
        vStippleDistance += (stippleDisplacementFactor - isEndVertex) * (vStippleDistanceLimits.y - vStippleDistanceLimits.x);
      }

      // Cancel out perspective correct interpolation because we want this length the really represent the screen distance
      vStippleDistanceLimits *= pos.w;
      vStippleDistance *= pos.w;

      // Disable stipple distance limits on caps
      vStippleDistanceLimits = isJoin ?
                                 vStippleDistanceLimits :
                                 isStartVertex ?
                                  vec2(-1e038, vStippleDistanceLimits.y) :
                                  vec2(vStippleDistanceLimits.x, 1e038);
    `)),r.code.add(m`
      // Convert back into NDC
      pos.xy = (pos.xy / viewport.zw) * pos.w;

      vColor = getColor();
      vColor.a *= coverage;

      ${t.wireframe&&!t.draped?"pos.z -= 0.001 * pos.w;":""}

      // transform final position to camera space for slicing
      vpos = (inverseProjectionMatrix * pos).xyz;
      gl_Position = pos;
      forwardObjectAndLayerIdColor();
    }
  }
  `),s&&e.include(Ls,t),e.include(vt,t),i.include(ji),i.code.add(m`
  void main() {
    discardBySlice(vpos);
    ${s?"terrainDepthTest(gl_FragCoord, depth);":""}
  `),t.wireframe?i.code.add(m`vec4 finalColor = vec4(1.0, 0.0, 1.0, 1.0);`):(o&&i.code.add(m`
      float sdf = min(vSegmentSDF, vReverseSegmentSDF);
      vec2 fragmentPosition = vec2(
        min(sdf, 0.0),
        vLineDistance
      ) * gl_FragCoord.w;

      float fragmentRadius = length(fragmentPosition);
      float fragmentCapSDF = (fragmentRadius - vLineWidth) * 0.5; // Divide by 2 to transform from double pixel scale
      float capCoverage = clamp(0.5 - fragmentCapSDF, 0.0, 1.0);

      if (capCoverage < ${m.float(Ve)}) {
        discard;
      }
    `),d?i.code.add(m`
      vec2 stipplePosition = vec2(
        min(getStippleSDF() * 2.0 - 1.0, 0.0),
        vLineDistanceNorm * gl_FragCoord.w
      );
      float stippleRadius = length(stipplePosition * vLineWidth);
      float stippleCapSDF = (stippleRadius - vLineWidth) * 0.5; // Divide by 2 to transform from double pixel scale
      float stippleCoverage = clamp(0.5 - stippleCapSDF, 0.0, 1.0);
      float stippleAlpha = step(${m.float(Ve)}, stippleCoverage);
      `):i.code.add(m`float stippleAlpha = getStippleAlpha();`),i.uniforms.add(new Ee("intrinsicColor",f=>f.color)),t.output!==T.ObjectAndLayerIdColor&&i.code.add(m`discardByStippleAlpha(stippleAlpha, stippleAlphaColorDiscard);`),i.code.add(m`vec4 color = intrinsicColor * vColor;`),t.innerColorEnabled&&(i.uniforms.add(new Ee("innerColor",f=>ae(f.innerColor,f.color))),i.uniforms.add(new B("innerWidth",(f,g)=>f.innerWidth*g.camera.pixelRatio)),i.code.add(m`float distToInner = abs(vLineDistance * gl_FragCoord.w) - innerWidth;
float innerAA = clamp(0.5 - distToInner, 0.0, 1.0);
float innerAlpha = innerColor.a + color.a * (1.0 - innerColor.a);
color = mix(color, vec4(innerColor.rgb, innerAlpha), innerAA);`)),i.code.add(m`vec4 finalColor = blendStipple(color, stippleAlpha);`),t.falloffEnabled&&(i.uniforms.add(new B("falloff",f=>f.falloff)),i.code.add(m`finalColor.a *= pow(max(0.0, 1.0 - abs(vLineDistanceNorm * gl_FragCoord.w)), falloff);`))),i.code.add(m`
    ${t.output===T.ObjectAndLayerIdColor?m`finalColor.a = 1.0;`:""}

    if (finalColor.a < ${m.float(Ve)}) {
      discard;
    }

    ${t.output===T.Alpha?m`gl_FragColor = vec4(finalColor.a);`:""}
    ${t.output===T.Color?m`gl_FragColor = highlightSlice(finalColor, vpos);`:""}
    ${t.output===T.Color&&t.transparencyPassType===re.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}
    ${t.output===T.Highlight?m`gl_FragColor = vec4(1.0);`:""}
    ${t.output===T.Depth?m`outputDepth(linearDepth);`:""}
    ${t.output===T.ObjectAndLayerIdColor?m`outputObjectAndLayerIdColor();`:""}
  }
  `),e}const Zd=Object.freeze(Object.defineProperty({__proto__:null,RIBBONLINE_NUM_ROUND_JOIN_SUBDIVISIONS:qr,build:cn},Symbol.toStringTag,{value:"Module"})),dn=new Map([[h.POSITION,0],[h.SUBDIVISIONFACTOR,1],[h.UV0,2],[h.AUXPOS1,3],[h.AUXPOS2,4],[h.COLOR,5],[h.COLORFEATUREATTRIBUTE,5],[h.SIZE,6],[h.SIZEFEATUREATTRIBUTE,6],[h.OPACITYFEATUREATTRIBUTE,7],[h.OBJECTANDLAYERIDCOLOR,8]]);let hn=class un extends Ar{initializeProgram(e){return new Rr(e.rctx,un.shader.get().build(this.configuration),dn)}_makePipelineState(e,r){const i=this.configuration,s=e===re.NONE,a=e===re.FrontFace;return et({blending:i.output===T.Color||i.output===T.Alpha?s?jr:Wi(e):null,depthTest:{func:Bs(e)},depthWrite:s?i.writeDepth?yr:null:No(e),colorWrite:lt,stencilWrite:i.hasOccludees?Oi:null,stencilTest:i.hasOccludees?r?Ci:Ns:null,polygonOffset:s||a?i.hasPolygonOffset?Ea:null:Xc})}initializePipeline(){const e=this.configuration;if(e.occluder){const r=e.hasPolygonOffset?Ea:null;this._occluderPipelineTransparent=et({blending:jr,polygonOffset:r,depthTest:fa,depthWrite:null,colorWrite:lt,stencilWrite:null,stencilTest:kl}),this._occluderPipelineOpaque=et({blending:jr,polygonOffset:r,depthTest:fa,depthWrite:null,colorWrite:lt,stencilWrite:ql,stencilTest:Xl}),this._occluderPipelineMaskWrite=et({blending:null,polygonOffset:r,depthTest:Oo,depthWrite:null,colorWrite:null,stencilWrite:Oi,stencilTest:Ci})}return this._occludeePipelineState=this._makePipelineState(this.configuration.transparencyPassType,!0),this._makePipelineState(this.configuration.transparencyPassType,!1)}get primitiveType(){return this.configuration.wireframe?Ht.LINES:Ht.TRIANGLE_STRIP}getPipelineState(e,r){return r?this._occludeePipelineState:this.configuration.occluder?e===W.TRANSPARENT_OCCLUDER_MATERIAL?this._occluderPipelineTransparent:e===W.OCCLUDER_MATERIAL?this._occluderPipelineOpaque:this._occluderPipelineMaskWrite:super.getPipelineState(e,r)}};hn.shader=new Pr(Zd,()=>Tr(()=>Promise.resolve().then(()=>jp),void 0));const Ea={factor:0,units:-4};var Pe;(function(t){t[t.LEFT_JOIN_START=-2]="LEFT_JOIN_START",t[t.LEFT_JOIN_END=-1]="LEFT_JOIN_END",t[t.LEFT_CAP_START=-4]="LEFT_CAP_START",t[t.LEFT_CAP_END=-5]="LEFT_CAP_END",t[t.RIGHT_JOIN_START=2]="RIGHT_JOIN_START",t[t.RIGHT_JOIN_END=1]="RIGHT_JOIN_END",t[t.RIGHT_CAP_START=4]="RIGHT_CAP_START",t[t.RIGHT_CAP_END=5]="RIGHT_CAP_END"})(Pe||(Pe={}));let Yd=class extends Vi{constructor(e){super(e,new Qd),this._configuration=new q,this._vertexAttributeLocations=dn,this._layout=this.createLayout()}isClosed(e,r){return fn(this.parameters,e,r)}getConfiguration(e,r){this._configuration.output=e,this._configuration.draped=r.slot===W.DRAPED_MATERIAL;const i=A(this.parameters.stipplePattern)&&e!==T.Highlight;return this._configuration.stippleEnabled=i,this._configuration.stippleOffColorEnabled=i&&A(this.parameters.stippleOffColor),this._configuration.stippleScaleWithLineWidth=i&&this.parameters.stippleScaleWithLineWidth,this._configuration.stipplePreferContinuous=i&&this.parameters.stipplePreferContinuous,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.roundJoins=this.parameters.join==="round",this._configuration.capType=this.parameters.cap,this._configuration.applyMarkerOffset=!!A(this.parameters.markerParameters)&&eh(this.parameters.markerParameters),this._configuration.hasPolygonOffset=this.parameters.hasPolygonOffset,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.vvColor=this.parameters.vvColorEnabled,this._configuration.vvOpacity=this.parameters.vvOpacityEnabled,this._configuration.vvSize=this.parameters.vvSizeEnabled,this._configuration.innerColorEnabled=this.parameters.innerWidth>0&&A(this.parameters.innerColor),this._configuration.falloffEnabled=this.parameters.falloff>0,this._configuration.occluder=this.parameters.renderOccluded===xt.OccludeAndTransparentStencil,this._configuration.transparencyPassType=r.transparencyPassType,this._configuration.hasMultipassTerrain=r.multipassTerrain.enabled,this._configuration.cullAboveGround=r.multipassTerrain.cullAboveGround,this._configuration.wireframe=this.parameters.wireframe,this._configuration}intersectDraped(e,r,i,s,a,o){if(!i.options.selectionMode)return;const n=e.vertexAttributes.get(h.POSITION).data,c=e.vertexAttributes.get(h.SIZE);let l=this.parameters.width;if(this.parameters.vvSizeEnabled){const _=e.vertexAttributes.get(h.SIZEFEATUREATTRIBUTE).data[0];l*=zt(this.parameters.vvSizeOffset[0]+_*this.parameters.vvSizeFactor[0],this.parameters.vvSizeMinSize[0],this.parameters.vvSizeMaxSize[0])}else c&&(l*=c.data[0]);const d=s[0],p=s[1],u=(l/2+4)*e.screenToWorldRatio;let f=Number.MAX_VALUE,g=0;for(let _=0;_<n.length-5;_+=3){const w=n[_],O=n[_+1],S=d-w,y=p-O,E=n[_+3]-w,C=n[_+4]-O,b=zt((E*S+C*y)/(E*E+C*C),0,1),F=E*b-S,R=C*b-y,V=F*F+R*R;V<f&&(f=V,g=_/3)}f<u*u&&a(o.dist,o.normal,g,!1)}intersect(e,r,i,s,a,o){if(!i.options.selectionMode||!e.visible)return;if(!Io(r))return void Kr.getLogger("esri.views.3d.webgl-engine.materials.RibbonLineMaterial").error("intersection assumes a translation-only matrix");const n=e.vertexAttributes,c=n.get(h.POSITION).data;let l=this.parameters.width;if(this.parameters.vvSizeEnabled){const S=n.get(h.SIZEFEATUREATTRIBUTE).data[0];l*=zt(this.parameters.vvSizeOffset[0]+S*this.parameters.vvSizeFactor[0],this.parameters.vvSizeMinSize[0],this.parameters.vvSizeMaxSize[0])}else n.has(h.SIZE)&&(l*=n.get(h.SIZE).data[0]);const d=i.camera,p=th;Or(p,i.point);const u=l*d.pixelRatio/2+4*d.pixelRatio;H($r[0],p[0]-u,p[1]+u,0),H($r[1],p[0]+u,p[1]+u,0),H($r[2],p[0]+u,p[1]-u,0),H($r[3],p[0]-u,p[1]-u,0);for(let S=0;S<4;S++)if(!d.unprojectFromRenderScreen($r[S],st[S]))return;gt(d.eye,st[0],st[1],ts),gt(d.eye,st[1],st[2],rs),gt(d.eye,st[2],st[3],is),gt(d.eye,st[3],st[0],ss);let f=Number.MAX_VALUE,g=0;const _=pn(this.parameters,n,e.indices)?c.length-2:c.length-5;for(let S=0;S<_;S+=3){Oe[0]=c[S]+r[12],Oe[1]=c[S+1]+r[13],Oe[2]=c[S+2]+r[14];const y=(S+3)%c.length;if(Ce[0]=c[y]+r[12],Ce[1]=c[y+1]+r[13],Ce[2]=c[y+2]+r[14],ce(ts,Oe)<0&&ce(ts,Ce)<0||ce(rs,Oe)<0&&ce(rs,Ce)<0||ce(is,Oe)<0&&ce(is,Ce)<0||ce(ss,Oe)<0&&ce(ss,Ce)<0)continue;if(d.projectToRenderScreen(Oe,Et),d.projectToRenderScreen(Ce,Dt),Et[2]<0&&Dt[2]>0){ne(Ze,Oe,Ce);const C=d.frustum,b=-ce(C[_t.NEAR],Oe)/ct(Ze,xi(C[_t.NEAR]));Z(Ze,Ze,b),te(Oe,Oe,Ze),d.projectToRenderScreen(Oe,Et)}else if(Et[2]>0&&Dt[2]<0){ne(Ze,Ce,Oe);const C=d.frustum,b=-ce(C[_t.NEAR],Ce)/ct(Ze,xi(C[_t.NEAR]));Z(Ze,Ze,b),te(Ce,Ce,Ze),d.projectToRenderScreen(Ce,Dt)}else if(Et[2]<0&&Dt[2]<0)continue;Et[2]=0,Dt[2]=0;const E=ko(hr(Et,Dt,Ia),p);E<f&&(f=E,k(Da,Oe),k($a,Ce),g=S/3)}const w=i.rayBegin,O=i.rayEnd;if(f<u*u){let S=Number.MAX_VALUE;if(qo(hr(Da,$a,Ia),hr(w,O,rh),Pt)){ne(Pt,Pt,w);const y=nt(Pt);Z(Pt,Pt,1/y),S=y/Tt(w,O)}o(S,Pt,g,!1)}}createLayout(){const e=bt().vec3f(h.POSITION).f32(h.SUBDIVISIONFACTOR).vec2f(h.UV0).vec3f(h.AUXPOS1).vec3f(h.AUXPOS2);return this.parameters.vvSizeEnabled?e.f32(h.SIZEFEATUREATTRIBUTE):e.f32(h.SIZE),this.parameters.vvColorEnabled?e.f32(h.COLORFEATUREATTRIBUTE):e.vec4f(h.COLOR),this.parameters.vvOpacityEnabled&&e.f32(h.OPACITYFEATUREATTRIBUTE),yt("enable-feature:objectAndLayerId-rendering")&&e.vec4u8(h.OBJECTANDLAYERIDCOLOR),e}createBufferWriter(){return new Kd(this._layout,this.parameters)}requiresSlot(e,r){return r===T.Color||r===T.Alpha||r===T.Highlight||r===T.Depth||r===T.ObjectAndLayerIdColor?e===W.DRAPED_MATERIAL?!0:this.parameters.renderOccluded===xt.OccludeAndTransparentStencil?e===W.OPAQUE_MATERIAL||e===W.OCCLUDER_MATERIAL||e===W.TRANSPARENT_OCCLUDER_MATERIAL:r===T.Color||r===T.Alpha?e===(this.parameters.writeDepth?W.TRANSPARENT_MATERIAL:W.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL):e===W.OPAQUE_MATERIAL:!1}createGLMaterial(e){return new Jd(e)}validateParameters(e){e.join!=="miter"&&(e.miterLimit=0),A(e.markerParameters)&&(e.markerScale=e.markerParameters.width/e.width)}},Jd=class extends Ui{constructor(){super(...arguments),this._stipplePattern=null}dispose(){super.dispose(),this._stippleTextureRepository.release(this._stipplePattern),this._stipplePattern=null}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){this._output!==T.Color&&this._output!==T.Alpha||this._updateOccludeeState(e);const r=this._material.parameters.stipplePattern;return this._stipplePattern!==r&&(this._material.setParameters(this._stippleTextureRepository.swap(this._stipplePattern,r)),this._stipplePattern=r),this.ensureTechnique(hn,e)}},Qd=class extends Zl{constructor(){super(...arguments),this.width=0,this.color=Fi,this.join="miter",this.cap=Gt.BUTT,this.miterLimit=5,this.writeDepth=!0,this.hasPolygonOffset=!1,this.stippleTexture=null,this.stippleScaleWithLineWidth=!1,this.stipplePreferContinuous=!0,this.markerParameters=null,this.markerScale=1,this.hasSlicePlane=!1,this.vvFastUpdate=!1,this.isClosed=!1,this.falloff=0,this.innerWidth=0,this.hasOccludees=!1,this.wireframe=!1}},Kd=class{constructor(e,r){this._parameters=r,this.numJoinSubdivisions=0,this.vertexBufferLayout=e;const i=r.stipplePattern?1:0;switch(this._parameters.join){case"miter":case"bevel":this.numJoinSubdivisions=i;break;case"round":this.numJoinSubdivisions=qr+i}}_isClosed(e){return pn(this._parameters,e.vertexAttributes,e.indices)}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){const i=e.indices.get(h.POSITION).length/2+1,s=this._isClosed(e);let a=s?2:2*2;return a+=((s?i:i-1)-(s?0:1))*(2*this.numJoinSubdivisions+4),a+=2,this._parameters.wireframe&&(a=2+4*(a-2)),a}write(e,r,i,s,a){var Bt;const o=ih,n=sh,c=ah,l=i.vertexAttributes.get(h.POSITION).data,d=i.indices&&i.indices.get(h.POSITION),p=(Bt=i.vertexAttributes.get(h.DISTANCETOSTART))==null?void 0:Bt.data;d&&d.length!==2*(l.length/3-1)&&console.warn("RibbonLineMaterial does not support indices");let u=1,f=0;this._parameters.vvSizeEnabled?f=i.vertexAttributes.get(h.SIZEFEATUREATTRIBUTE).data[0]:i.vertexAttributes.has(h.SIZE)&&(u=i.vertexAttributes.get(h.SIZE).data[0]);let g=[1,1,1,1],_=0;this._parameters.vvColorEnabled?_=i.vertexAttributes.get(h.COLORFEATUREATTRIBUTE).data[0]:i.vertexAttributes.has(h.COLOR)&&(g=i.vertexAttributes.get(h.COLOR).data);const w=yt("enable-feature:objectAndLayerId-rendering")?i.objectAndLayerIdColor:null;let O=0;this._parameters.vvOpacityEnabled&&(O=i.vertexAttributes.get(h.OPACITYFEATUREATTRIBUTE).data[0]);const S=l.length/3,y=new Float32Array(s.buffer),E=yt("enable-feature:objectAndLayerId-rendering")?new Uint8Array(s.buffer):null,C=this.vertexBufferLayout.stride/4;let b=a*C;const F=b;let R=0;const V=p?(J,Fe,He)=>R=p[He]:(J,Fe,He)=>R+=Tt(J,Fe),pe=yt("enable-feature:objectAndLayerId-rendering"),D=(J,Fe,He,I,At,Xe,Er)=>{if(y[b++]=Fe[0],y[b++]=Fe[1],y[b++]=Fe[2],y[b++]=I,y[b++]=Er,y[b++]=At,y[b++]=J[0],y[b++]=J[1],y[b++]=J[2],y[b++]=He[0],y[b++]=He[1],y[b++]=He[2],this._parameters.vvSizeEnabled?y[b++]=f:y[b++]=u,this._parameters.vvColorEnabled)y[b++]=_;else{const ht=Math.min(4*Xe,g.length-4);y[b++]=g[ht],y[b++]=g[ht+1],y[b++]=g[ht+2],y[b++]=g[ht+3]}this._parameters.vvOpacityEnabled&&(y[b++]=O),pe&&(A(w)&&(E[4*b]=w[0],E[4*b+1]=w[1],E[4*b+2]=w[2],E[4*b+3]=w[3]),b++)};b+=C,H(n,l[0],l[1],l[2]),e&&ie(n,n,e);const X=this._isClosed(i);if(X){const J=l.length-3;H(o,l[J],l[J+1],l[J+2]),e&&ie(o,o,e)}else H(c,l[3],l[4],l[5]),e&&ie(c,c,e),D(n,n,c,1,Pe.LEFT_CAP_START,0,0),D(n,n,c,1,Pe.RIGHT_CAP_START,0,0),k(o,n),k(n,c);const me=X?0:1,qe=X?S:S-1;for(let J=me;J<qe;J++){const Fe=(J+1)%S*3;H(c,l[Fe],l[Fe+1],l[Fe+2]),e&&ie(c,c,e),V(o,n,J),D(o,n,c,0,Pe.LEFT_JOIN_END,J,R),D(o,n,c,0,Pe.RIGHT_JOIN_END,J,R);const He=this.numJoinSubdivisions;for(let I=0;I<He;++I){const At=(I+1)/(He+1);D(o,n,c,At,Pe.LEFT_JOIN_END,J,R),D(o,n,c,At,Pe.RIGHT_JOIN_END,J,R)}D(o,n,c,1,Pe.LEFT_JOIN_START,J,R),D(o,n,c,1,Pe.RIGHT_JOIN_START,J,R),k(o,n),k(n,c)}X?(H(c,l[3],l[4],l[5]),e&&ie(c,c,e),R=V(o,n,qe),D(o,n,c,0,Pe.LEFT_JOIN_END,me,R),D(o,n,c,0,Pe.RIGHT_JOIN_END,me,R)):(R=V(o,n,qe),D(o,n,n,0,Pe.LEFT_CAP_END,qe,R),D(o,n,n,0,Pe.RIGHT_CAP_END,qe,R)),es(y,F+C,y,F,C),b=es(y,b-C,y,b,C),this._parameters.wireframe&&this._addWireframeVertices(s,F,b,C)}_addWireframeVertices(e,r,i,s){const a=new Float32Array(e.buffer,i*Float32Array.BYTES_PER_ELEMENT),o=new Float32Array(e.buffer,r*Float32Array.BYTES_PER_ELEMENT,i-r);let n=0;const c=l=>n=es(o,l,a,n,s);for(let l=0;l<o.length-1;l+=2*s)c(l),c(l+2*s),c(l+1*s),c(l+2*s),c(l+1*s),c(l+3*s)}};function es(t,e,r,i,s){for(let a=0;a<s;a++)r[i++]=t[e++];return i}function pn(t,e,r){return fn(t,e.get(h.POSITION).data,r?r.get(h.POSITION):null)}function fn(t,e,r){return!!t.isClosed&&(r?r.length>2:e.length>6)}function eh(t){return t.anchor===kr.Tip&&t.hideOnShortSegments&&t.placement==="begin-end"&&t.worldSpace}const Oe=$(),Ce=$(),Ze=$(),Pt=$(),th=$(),Et=Le(),Dt=Le(),Da=$(),$a=$(),Ia=Bi(),rh=Bi(),ih=$(),sh=$(),ah=$(),$r=[Le(),Le(),Le(),Le()],st=[$(),$(),$(),$()],ts=dt(),rs=dt(),is=dt(),ss=dt();let oh=class{constructor(e){this._originSR=e,this._origins=new Map,this._objects=new Map,this._gridSize=5e5,this._rootOriginId="root/"+Gs()}getOrigin(e){const r=this._origins.get(this._rootOriginId);if(r==null){const d=bd.rootOrigin;if(A(d))return this._origins.set(this._rootOriginId,gi(d[0],d[1],d[2],this._rootOriginId)),this.getOrigin(e);const p=gi(e[0]+Math.random()-.5,e[1]+Math.random()-.5,e[2]+Math.random()-.5,this._rootOriginId);return this._origins.set(this._rootOriginId,p),p}const i=this._gridSize,s=Math.round(e[0]/i),a=Math.round(e[1]/i),o=Math.round(e[2]/i),n=`${s}/${a}/${o}`;let c=this._origins.get(n);const l=.5*i;if(ne(he,e,r.vec3),he[0]=Math.abs(he[0]),he[1]=Math.abs(he[1]),he[2]=Math.abs(he[2]),he[0]<l&&he[1]<l&&he[2]<l){if(c){const d=Math.max(...he);if(ne(he,e,c.vec3),he[0]=Math.abs(he[0]),he[1]=Math.abs(he[1]),he[2]=Math.abs(he[2]),Math.max(...he)<d)return c}return r}return c||(c=gi(s*i,a*i,o*i,n),this._origins.set(n,c)),c}_drawOriginBox(e,r=vr(1,1,0,1)){const i=window.view,s=i._stage,a=r.toString();if(!this._objects.has(a)){this._material=new Yd({width:2,color:r}),s.add(this._material);const f=new ud({pickable:!1}),g=new cd({castShadow:!1});s.add(g),f.add(g),s.add(f),this._objects.set(a,g)}const o=this._objects.get(a),n=[0,1,5,4,0,2,1,7,6,2,0,1,3,7,5,4,6,2,0],c=n.length,l=new Array(3*c),d=new Array,p=.5*this._gridSize;for(let f=0;f<c;f++)l[3*f+0]=e[0]+(1&n[f]?p:-p),l[3*f+1]=e[1]+(2&n[f]?p:-p),l[3*f+2]=e[2]+(4&n[f]?p:-p),f>0&&d.push(f-1,f);Si(l,this._originSR,0,l,i.renderSpatialReference,0,c);const u=new Ne(this._material,[[h.POSITION,new j(l,3,!0)]],[[h.POSITION,d]],null,Cr.Line);s.add(u),o.addGeometry(u)}get test(){const e=this;return{set gridSize(r){e._gridSize=r}}}};const he=$();var Xr,Di;(function(t){t[t.RENDERING=0]="RENDERING",t[t.FINISHED_RENDERING=1]="FINISHED_RENDERING",t[t.FADING_TEXTURE_CHANNELS=2]="FADING_TEXTURE_CHANNELS",t[t.SWITCH_CHANNELS=3]="SWITCH_CHANNELS",t[t.FINISHED=4]="FINISHED"})(Xr||(Xr={})),function(t){t[t.RG=0]="RG",t[t.BA=1]="BA"}(Di||(Di={}));let nh=class{constructor(){this.readChannels=Di.RG,this.renderingStage=Xr.FINISHED,this.startTime=0,this.startTimeHeightFade=0,this.cameraPositionLastFrame=$(),this.isCameraPositionFinal=!0,this.parallax=new Ma,this.parallaxNew=new Ma,this.crossFade={enabled:!1,factor:1,distanceThresholdFactor:.3},this.fadeInOut={stage:ur.FINISHED,factor:1,distanceThresholdFactor:.6},this.fadeIn={stage:$i.FINISHED,factor:1,distanceThresholdFactor:2},this.fadeInOutHeight={stage:Ii.FINISHED,factor:-1}}get isFading(){return this.fadeInOut.stage===ur.FADE_OUT||this.fadeInOut.stage===ur.FADE_IN||this.fadeIn.stage===$i.FADE_IN||this.fadeInOutHeight.stage!==Ii.FINISHED||this.renderingStage===Xr.FADING_TEXTURE_CHANNELS}};var $i,ur,Ii;(function(t){t[t.FINISHED=0]="FINISHED",t[t.CHANGE_ANCHOR=1]="CHANGE_ANCHOR",t[t.FADE_IN=2]="FADE_IN"})($i||($i={})),function(t){t[t.FINISHED=0]="FINISHED",t[t.FADE_OUT=1]="FADE_OUT",t[t.SWITCH=2]="SWITCH",t[t.FADE_IN=3]="FADE_IN"}(ur||(ur={})),function(t){t[t.FINISHED=0]="FINISHED",t[t.HEIGHT_FADE=1]="HEIGHT_FADE"}(Ii||(Ii={}));let Ma=class{constructor(){this.anchorPointClouds=$(),this.cloudsHeight=1e5,this.radiusCurvatureCorrectionFactor=0,this.transform=Y()}};function lh(t){t.include(Fs),t.uniforms.add([new Ut("geometryDepthTexture",(e,r)=>r.multipassGeometry.linearDepthTexture),new St("nearFar",(e,r)=>r.camera.nearFar)]),t.code.add(m`bool geometryDepthTest(vec2 pos, float elementDepth) {
float geometryDepth = linearDepthFromTexture(geometryDepthTexture, pos, nearFar);
return (elementDepth < (geometryDepth - 1.0));
}`)}let ch=class{constructor(){this.enabled=!1}};function dh(t,e){const r=t.fragment;r.include(Fs),r.uniforms.add(new St("nearFar",(i,s)=>s.camera.nearFar)),r.uniforms.add(new Ut("depthMap",(i,s)=>s.linearDepthTexture)),r.uniforms.add(new _r("proj",(i,s)=>s.ssr.camera.projectionMatrix)),r.uniforms.add(new B("invResolutionHeight",(i,s)=>1/s.ssr.camera.height)),r.uniforms.add(new _r("reprojectionMatrix",(i,s)=>s.ssr.reprojectionMatrix)),r.code.add(m`
  vec2 reprojectionCoordinate(vec3 projectionCoordinate)
  {
    vec4 zw = proj * vec4(0.0, 0.0, -projectionCoordinate.z, 1.0);
    vec4 reprojectedCoord = reprojectionMatrix * vec4(zw.w * (projectionCoordinate.xy * 2.0 - 1.0), zw.z, zw.w);
    reprojectedCoord.xy /= reprojectedCoord.w;
    return reprojectedCoord.xy * 0.5 + 0.5;
  }

  const int maxSteps = ${e.highStepCount?"150":"75"};

  vec4 applyProjectionMat(mat4 projectionMat, vec3 x)
  {
    vec4 projectedCoord =  projectionMat * vec4(x, 1.0);
    projectedCoord.xy /= projectedCoord.w;
    projectedCoord.xy = projectedCoord.xy*0.5 + 0.5;
    return projectedCoord;
  }

  vec3 screenSpaceIntersection(vec3 dir, vec3 startPosition, vec3 viewDir, vec3 normal)
  {
    vec3 viewPos = startPosition;
    vec3 viewPosEnd = startPosition;

    // Project the start position to the screen
    vec4 projectedCoordStart = applyProjectionMat(proj, viewPos);
    vec3  Q0 = viewPos / projectedCoordStart.w; // homogeneous camera space
    float k0 = 1.0/ projectedCoordStart.w;

    // advance the position in the direction of the reflection
    viewPos += dir;

    vec4 projectedCoordVanishingPoint = applyProjectionMat(proj, dir);

    // Project the advanced position to the screen
    vec4 projectedCoordEnd = applyProjectionMat(proj, viewPos);
    vec3  Q1 = viewPos / projectedCoordEnd.w; // homogeneous camera space
    float k1 = 1.0/ projectedCoordEnd.w;

    // calculate the reflection direction in the screen space
    vec2 projectedCoordDir = (projectedCoordEnd.xy - projectedCoordStart.xy);
    vec2 projectedCoordDistVanishingPoint = (projectedCoordVanishingPoint.xy - projectedCoordStart.xy);

    float yMod = min(abs(projectedCoordDistVanishingPoint.y), 1.0);

    float projectedCoordDirLength = length(projectedCoordDir);
    float maxSt = float(maxSteps);

    // normalize the projection direction depending on maximum steps
    // this determines how blocky the reflection looks
    vec2 dP = yMod * (projectedCoordDir)/(maxSt * projectedCoordDirLength);

    // Normalize the homogeneous camera space coordinates
    vec3  dQ = yMod * (Q1 - Q0)/(maxSt * projectedCoordDirLength);
    float dk = yMod * (k1 - k0)/(maxSt * projectedCoordDirLength);

    // initialize the variables for ray marching
    vec2 P = projectedCoordStart.xy;
    vec3 Q = Q0;
    float k = k0;
    float rayStartZ = -startPosition.z; // estimated ray start depth value
    float rayEndZ = -startPosition.z;   // estimated ray end depth value
    float prevEstimateZ = -startPosition.z;
    float rayDiffZ = 0.0;
    float dDepth;
    float depth;
    float rayDiffZOld = 0.0;

    // early outs
    if (dot(normal, dir) < 0.0 || dot(-viewDir, normal) < 0.0)
      return vec3(P, 0.0);

    for(int i = 0; i < maxSteps-1; i++)
    {
      depth = -linearDepthFromTexture(depthMap, P, nearFar); // get linear depth from the depth buffer

      // estimate depth of the marching ray
      rayStartZ = prevEstimateZ;
      dDepth = -rayStartZ - depth;
      rayEndZ = (dQ.z * 0.5 + Q.z)/ ((dk * 0.5 + k));
      rayDiffZ = rayEndZ- rayStartZ;
      prevEstimateZ = rayEndZ;

      if(-rayEndZ > nearFar[1] || -rayEndZ < nearFar[0] || P.y < 0.0  || P.y > 1.0 )
      {
        return vec3(P, 0.);
      }

      // If we detect a hit - return the intersection point, two conditions:
      //  - dDepth > 0.0 - sampled point depth is in front of estimated depth
      //  - if difference between dDepth and rayDiffZOld is not too large
      //  - if difference between dDepth and 0.025/abs(k) is not too large
      //  - if the sampled depth is not behind far plane or in front of near plane

      if((dDepth) < 0.025/abs(k) + abs(rayDiffZ) && dDepth > 0.0 && depth > nearFar[0] && depth < nearFar[1] && abs(P.y - projectedCoordStart.y) > invResolutionHeight)
      {
        return vec3(P, depth);
      }

      // continue with ray marching
      P += dP;
      Q.z += dQ.z;
      k += dk;
      rayDiffZOld = rayDiffZ;
    }
    return vec3(P, 0.0);
  }
  `)}let hh=class{constructor(){this.enabled=!1,this.fadeFactor=1,this.reprojectionMatrix=Y()}},uh=class{constructor(e,r,i){this.shadowMap=e,this.ssaoHelper=r,this.slicePlane=i,this.slot=W.OPAQUE_MATERIAL,this.hasOccludees=!1,this.enableFillLights=!0,this.transparencyPassType=re.NONE,this._camera=new U,this._inverseViewport=K(),this.oldLighting=new Xi,this.newLighting=new Xi,this._fadedLighting=new Xi,this._lighting=this.newLighting,this.ssr=new hh,this.multipassTerrain=new Yl,this.multipassGeometry=new ch,this.overlays=[],this.cloudsFade=new nh}get camera(){return this._camera}set camera(e){this._camera=this.ssr.camera=e,this._inverseViewport[0]=1/e.fullViewport[2],this._inverseViewport[1]=1/e.fullViewport[3]}get inverseViewport(){return this._inverseViewport}get lighting(){return this._lighting}get weatherFading(){return this._lighting===this._fadedLighting}fadeLighting(e){const{oldLighting:r,newLighting:i}=this;e>=1?this._lighting=i:(this._fadedLighting.lerpLighting(r,i,e),this._lighting=this._fadedLighting)}},ph=class{constructor(e,r,i,s=null){this.rctx=e,this.sliceHelper=s,this.lastFrameCamera=new U,this.output=T.Color,this.renderOccludedMask=La,this.bindParameters=new uh(r,i,A(s)?s.plane:null)}resetRenderOccludedMask(){this.renderOccludedMask=La}};const La=xt.Occlude|xt.OccludeAndTransparent|xt.OccludeAndTransparentStencil;let Fr=class extends U{constructor(){super(...arguments),this._projectionMatrix=Y()}get projectionMatrix(){return this._projectionMatrix}};v([L()],Fr.prototype,"_projectionMatrix",void 0),v([L({readOnly:!0})],Fr.prototype,"projectionMatrix",null),Fr=v([Gi("esri.views.3d.webgl-engine.lib.CascadeCamera")],Fr);var Na;(function(t){t[t.Highlight=0]="Highlight",t[t.Default=1]="Default"})(Na||(Na={}));let si=class{constructor(){this.camera=new Fr,this.lightMat=Y()}},fh=class{get depthTexture(){return this._depthTexture}get textureSize(){return this._textureSize}get numCascades(){return this._numCascades}get cascadeDistances(){return Gr(this._usedCascadeDistances,this._cascadeDistances[0],this._numCascades>1?this._cascadeDistances[1]:1/0,this._numCascades>2?this._cascadeDistances[2]:1/0,this._numCascades>3?this._cascadeDistances[3]:1/0)}constructor(e,r){this._rctx=e,this._viewingMode=r,this._enabled=!1,this._snapshots=new Array,this._textureSize=0,this._numCascades=1,this._maxNumCascades=4,this._projectionView=Y(),this._projectionViewInverse=Y(),this._modelViewLight=Y(),this._splitSchemeLambda=0,this._cascadeDistances=[0,0,0,0,0],this._usedCascadeDistances=ke(),this._cascades=[new si,new si,new si,new si],this._lastOrigin=null,this._maxTextureSize=Math.min(yt("esri-mobile")?2048:8192,this._rctx.parameters.maxTextureSize)}dispose(){this.enabled=!1,this.disposeOffscreenBuffers()}disposeOffscreenBuffers(){this._discardDepthTexture(),this._discardAllSnapshots()}set maxCascades(e){this._maxNumCascades=zt(Math.floor(e),1,4)}get maxCascades(){return this._maxNumCascades}set enabled(e){this._enabled=e,e||(this._discardDepthTexture(),this._discardAllSnapshots())}get enabled(){return this._enabled}get ready(){return this._enabled&&A(this._depthTexture)}getSnapshot(e){return this.enabled?this._snapshots[e]:null}get cascades(){for(let e=0;e<this._numCascades;++e)os[e]=this._cascades[e];return os.length=this._numCascades,os}start(e,r,i){Te(this.enabled),this._textureSize=this._computeTextureSize(e.fullWidth,e.fullHeight),this._ensureDepthTexture();const{near:s,far:a}=this._clampNearFar(i);this._computeCascadeDistances(a,s),this._setupMatrices(e,r);const{viewMatrix:o,projectionMatrix:n}=e;for(let c=0;c<this._numCascades;++c)this._constructCascade(c,n,o,r);this._lastOrigin=null,this.clear()}finish(e){Te(this.enabled),this._rctx.bindFramebuffer(e)}getShadowMapMatrices(e){if(!this._lastOrigin||!ir(e,this._lastOrigin)){this._lastOrigin=this._lastOrigin||$(),k(this._lastOrigin,e);for(let r=0;r<this._numCascades;++r){oa(Ha,this._cascades[r].lightMat,e);for(let i=0;i<16;++i)Ga[16*r+i]=Ha[i]}}return Ga}takeCascadeSnapshotTo(e,r){Te(this.enabled);const i=this._ensureSnapshot(r);this._bindFbo();const s=this._rctx,a=s.bindTexture(i,or.TEXTURE_UNIT_FOR_UPDATES);s.gl.copyTexSubImage2D(Vr.TEXTURE_2D,0,e.camera.viewport[0],e.camera.viewport[1],e.camera.viewport[0],e.camera.viewport[1],e.camera.viewport[2],e.camera.viewport[3]),s.bindTexture(a,or.TEXTURE_UNIT_FOR_UPDATES)}clear(){const e=this._rctx;this._bindFbo(),e.setClearColor(1,1,1,1),e.clearSafe(Ri.COLOR_BUFFER_BIT|Ri.DEPTH_BUFFER_BIT)}_computeTextureSize(e,r){const i=.5*Math.log(e*e+r*r)*Math.LOG2E,s=.35,a=2**Math.round(i+s);return Math.min(this._maxTextureSize,2*a)}_ensureDepthTexture(){if(A(this._depthTexture)&&this._depthTexture.descriptor.width===this._textureSize)return;this._discardDepthTexture();const e={target:Vr.TEXTURE_2D,pixelFormat:wr.RGBA,dataType:Wr.UNSIGNED_BYTE,wrapMode:Sr.CLAMP_TO_EDGE,samplingMode:Ai.NEAREST,flipped:!0,width:this._textureSize,height:this._textureSize};this._depthTexture=new or(this._rctx,e),this._fbo=new Wo(this._rctx,{colorTarget:Vo.TEXTURE,depthStencilTarget:Uo.DEPTH_RENDER_BUFFER,width:this._textureSize,height:this._textureSize},this._depthTexture)}_ensureSnapshot(e){let r=this._snapshots[e];if(A(r)&&r.descriptor.width===this._textureSize)return r;this._discardSnapshot(e);const i={target:Vr.TEXTURE_2D,pixelFormat:wr.RGBA,dataType:Wr.UNSIGNED_BYTE,wrapMode:Sr.CLAMP_TO_EDGE,samplingMode:Ai.NEAREST,flipped:!0,width:this._textureSize,height:this._textureSize};return r=new or(this._rctx,i),this._snapshots[e]=r,r}_discardDepthTexture(){this._fbo=We(this._fbo),this._depthTexture=We(this._depthTexture)}_discardSnapshot(e){this._snapshots[e]=We(this._snapshots[e])}_discardAllSnapshots(){for(let e=0;e<this._snapshots.length;++e)this._discardSnapshot(e);this._snapshots.length=0}_bindFbo(){const e=this._rctx;e.unbindTexture(this._depthTexture),e.bindFramebuffer(this._fbo)}_constructCascade(e,r,i,s){const a=this._cascades[e],o=-this._cascadeDistances[e],n=-this._cascadeDistances[e+1],c=(r[10]*o+r[14])/Math.abs(r[11]*o+r[15]),l=(r[10]*n+r[14])/Math.abs(r[11]*n+r[15]);Te(c<l);for(let f=0;f<8;++f){Gr(Fa,f%4==0||f%4==3?-1:1,f%4==0||f%4==1?-1:1,f<4?c:l,1);const g=je[f];sr(g,Fa,this._projectionViewInverse),g[0]/=g[3],g[1]/=g[3],g[2]/=g[3]}il(as,je[0]),a.camera.viewMatrix=oa(mh,this._modelViewLight,as);for(let f=0;f<8;++f)ie(je[f],je[f],a.camera.viewMatrix);let d=je[0][2],p=je[0][2];for(let f=1;f<8;++f)d=Math.min(d,je[f][2]),p=Math.max(p,je[f][2]);d-=200,p+=200,a.camera.near=-p,a.camera.far=-d,yh(i,s,d,p,a.camera),jt(a.lightMat,a.camera.projectionMatrix,a.camera.viewMatrix);const u=this._textureSize/2;a.camera.viewport=[e%2==0?0:u,Math.floor(e/2)===0?0:u,u,u]}_setupMatrices(e,r){jt(this._projectionView,e.projectionMatrix,e.viewMatrix),dr(this._projectionViewInverse,this._projectionView);const i=this._viewingMode===Ot.Global?e.eye:H(as,0,0,1);vo(this._modelViewLight,[0,0,0],[-r[0],-r[1],-r[2]],i)}_clampNearFar(e){let{near:r,far:i}=e;return r<2&&(r=2),i<2&&(i=2),r>=i&&(r=2,i=4),{near:r,far:i}}_computeCascadeDistances(e,r){this._numCascades=Math.min(1+Math.floor(Mc(e/r,4)),this._maxNumCascades);const i=(e-r)/this._numCascades,s=(e/r)**(1/this._numCascades);let a=r,o=r;for(let n=0;n<this._numCascades+1;++n)this._cascadeDistances[n]=Nt(a,o,this._splitSchemeLambda),a*=s,o+=i}get gpuMemoryUsage(){var e;return this._snapshots.reduce((r,i)=>r+rd(i),((e=this._fbo)==null?void 0:e.gpuMemoryUsage)??0)}get test(){const e=this;return{maxNumCascades:this._maxNumCascades,cascades:this._cascades,textureSize:this._textureSize,set splitSchemeLambda(r){e._splitSchemeLambda=r},get splitSchemeLambda(){return e._splitSchemeLambda}}}};const mh=Y(),Fa=ke(),je=[];for(let t=0;t<8;++t)je.push(ke());const za=K(),ja=K(),gh=K(),Va=K(),Ua=K(),as=$(),os=[],Ha=Y(),Ga=new Float32Array(64),$e=K(),Xt=K(),$t=[K(),K(),K(),K()],oe=K(),ns=K(),ut=K(),Ir=K(),Zt=K(),Yt=K(),ai=K();function vh(t,e,r,i,s,a,o,n){Vt($e,0,0);for(let C=0;C<4;++C)Rt($e,$e,t[C]);tr($e,$e,.25),Vt(Xt,0,0);for(let C=4;C<8;++C)Rt(Xt,Xt,t[C]);tr(Xt,Xt,.25),Dr($t[0],t[4],t[5],.5),Dr($t[1],t[5],t[6],.5),Dr($t[2],t[6],t[7],.5),Dr($t[3],t[7],t[4],.5);let c=0,l=na($t[0],$e);for(let C=1;C<4;++C){const b=na($t[C],$e);b<l&&(l=b,c=C)}rr(oe,$t[c],t[c+4]);const d=oe[0];let p,u;oe[0]=-oe[1],oe[1]=d,rr(ns,Xt,$e),ye(ns,oe)<0&&al(oe,oe),Dr(oe,oe,ns,r),la(oe,oe),p=u=ye(rr(ut,t[0],$e),oe);for(let C=1;C<8;++C){const b=ye(rr(ut,t[C],$e),oe);b<p?p=b:b>u&&(u=b)}Or(i,$e),tr(ut,oe,p-e),Rt(i,i,ut);let f=-1,g=1,_=0,w=0;for(let C=0;C<8;++C){rr(Ir,t[C],i),la(Ir,Ir);const b=oe[0]*Ir[1]-oe[1]*Ir[0];b>0?b>f&&(f=b,_=C):b<g&&(g=b,w=C)}kt(f>0,"leftArea"),kt(g<0,"rightArea"),tr(Zt,oe,p),Rt(Zt,Zt,$e),tr(Yt,oe,u),Rt(Yt,Yt,$e),ai[0]=-oe[1],ai[1]=oe[0];const O=ri(i,t[w],Yt,Rt(ut,Yt,ai),1,s),S=ri(i,t[_],Yt,ut,1,a),y=ri(i,t[_],Zt,Rt(ut,Zt,ai),1,o),E=ri(i,t[w],Zt,ut,1,n);kt(O,"rayRay"),kt(S,"rayRay"),kt(y,"rayRay"),kt(E,"rayRay")}function z(t,e){return 3*e+t}const Wa=K();function Ae(t,e){return Vt(Wa,t[e],t[e+3]),Wa}const be=K(),P=Ws();function _h(t,e,r,i,s){rr(be,r,i),tr(be,be,.5),P[0]=be[0],P[1]=be[1],P[2]=0,P[3]=be[1],P[4]=-be[0],P[5]=0,P[6]=be[0]*be[0]+be[1]*be[1],P[7]=be[0]*be[1]-be[1]*be[0],P[8]=1,P[z(0,2)]=-ye(Ae(P,0),t),P[z(1,2)]=-ye(Ae(P,1),t);let a=ye(Ae(P,0),r)+P[z(0,2)],o=ye(Ae(P,1),r)+P[z(1,2)],n=ye(Ae(P,0),i)+P[z(0,2)],c=ye(Ae(P,1),i)+P[z(1,2)];a=-(a+n)/(o+c),P[z(0,0)]+=P[z(1,0)]*a,P[z(0,1)]+=P[z(1,1)]*a,P[z(0,2)]+=P[z(1,2)]*a,a=1/(ye(Ae(P,0),r)+P[z(0,2)]),o=1/(ye(Ae(P,1),r)+P[z(1,2)]),P[z(0,0)]*=a,P[z(0,1)]*=a,P[z(0,2)]*=a,P[z(1,0)]*=o,P[z(1,1)]*=o,P[z(1,2)]*=o,P[z(2,0)]=P[z(1,0)],P[z(2,1)]=P[z(1,1)],P[z(2,2)]=P[z(1,2)],P[z(1,2)]+=1,a=ye(Ae(P,1),e)+P[z(1,2)],o=ye(Ae(P,2),e)+P[z(2,2)],n=ye(Ae(P,1),r)+P[z(1,2)],c=ye(Ae(P,2),r)+P[z(2,2)],a=-.5*(a/o+n/c),P[z(1,0)]+=P[z(2,0)]*a,P[z(1,1)]+=P[z(2,1)]*a,P[z(1,2)]+=P[z(2,2)]*a,a=ye(Ae(P,1),e)+P[z(1,2)],o=ye(Ae(P,2),e)+P[z(2,2)],n=-o/a,P[z(1,0)]*=n,P[z(1,1)]*=n,P[z(1,2)]*=n,s[0]=P[0],s[1]=P[1],s[2]=0,s[3]=P[2],s[4]=P[3],s[5]=P[4],s[6]=0,s[7]=P[5],s[8]=0,s[9]=0,s[10]=1,s[11]=0,s[12]=P[6],s[13]=P[7],s[14]=0,s[15]=P[8]}function yh(t,e,r,i,s){const a=1/je[0][3],o=1/je[4][3];Te(a<o);let n=a+Math.sqrt(a*o);const c=Math.sin(sl(t[2]*e[0]+t[6]*e[1]+t[10]*e[2]));n/=c,vh(je,n,c,za,ja,gh,Va,Ua),_h(za,ja,Va,Ua,s.projectionMatrix),s.projectionMatrix[10]=2/(r-i),s.projectionMatrix[14]=-(r+i)/(r-i)}var Ct,Wt;(function(t){t[t.OBJECT=0]="OBJECT",t[t.HUD=1]="HUD",t[t.TERRAIN=2]="TERRAIN",t[t.OVERLAY=3]="OVERLAY",t[t.I3S=4]="I3S",t[t.PCL=5]="PCL",t[t.LOD=6]="LOD",t[t.VOXEL=7]="VOXEL"})(Ct||(Ct={}));let wh=class{constructor(){this.verticalOffset=0,this.selectionMode=!1,this.hud=!0,this.selectOpaqueTerrainOnly=!0,this.invisibleTerrain=!1,this.backfacesTerrain=!0,this.isFiltered=!1,this.filteredLayerUids=[],this.store=Wt.ALL}};(function(t){t[t.MIN=0]="MIN",t[t.MINMAX=1]="MINMAX",t[t.ALL=2]="ALL"})(Wt||(Wt={}));let Sh=class{constructor(e,r,i){this.object=e,this.geometryId=r,this.triangleNr=i}},xh=class extends Sh{constructor(e,r,i,s){super(e,r,i),this.center=A(s)?ol(s):null}},Th=class{constructor(e){this.layerUid=e}},Oh=class extends Th{constructor(e,r){super(e),this.graphicUid=r}};function Ch(t){return A(t)&&A(t.dist)}let bh=class extends Oh{constructor(e,r,i){super(e,r),this.triangleNr=i}},Ah=class{constructor(){this.adds=new it,this.removes=new it,this.updates=new it({allocator:e=>e||new Rh,deallocator:e=>(e.renderGeometry=null,e)})}clear(){this.adds.clear(),this.removes.clear(),this.updates.clear()}prune(){this.adds.prune(),this.removes.prune(),this.updates.prune()}get empty(){return this.adds.length===0&&this.removes.length===0&&this.updates.length===0}},Rh=class{},Ph=class{constructor(){this.adds=new Array,this.removes=new Array,this.updates=new Array}};const Ba=1e-5;let Eh=class{constructor(e){this.options=new wh,this._results=new Dh,this.transform=new Jl,this.tolerance=Ba,this.verticalOffset=null,this._ray=mt(),this._rayEnd=$(),this._rayBeginTransformed=$(),this._rayEndTransformed=$(),this.viewingMode=e??Ot.Global}get results(){return this._results}get ray(){return this._ray}get rayBegin(){return this._ray.origin}get rayEnd(){return this._rayEnd}reset(e,r,i){this.resetWithRay(Vl(e,r,this._ray),i)}resetWithRay(e,r){this.camera=r,e!==this._ray&&Os(e,this._ray),this.options.verticalOffset!==0?this.viewingMode===Ot.Local?this._ray.origin[2]-=this.options.verticalOffset:this.verticalOffset=this.options.verticalOffset:this.verticalOffset=null,te(this._rayEnd,this._ray.origin,this._ray.direction),this._results.init(this._ray)}intersect(e=null,r,i,s,a){this.point=r,this.filterPredicate=s,this.tolerance=i??Ba;const o=ma(this.verticalOffset);if(A(e)&&e.length>0){const n=a?c=>{a(c)&&this.intersectObject(c)}:c=>{this.intersectObject(c)};for(const c of e){const l=c.getSpatialQueryAccelerator&&c.getSpatialQueryAccelerator();A(l)?(A(o)?l.forEachAlongRayWithVerticalOffset(this._ray.origin,this._ray.direction,n,o):l.forEachAlongRay(this._ray.origin,this._ray.direction,n),this.options.selectionMode&&this.options.hud&&l.forEachDegenerateObject(n)):c.objects.forAll(d=>n(d))}}this.sortResults()}intersectObject(e){const r=e.geometries;if(!r)return;const i=e.transformation,s=ma(this.verticalOffset);for(const a of r){if(!a.visible)continue;const{material:o,id:n}=a;this.transform.setAndInvalidateLazyTransforms(i,a.shaderTransformation),ie(this._rayBeginTransformed,this.rayBegin,this.transform.inverse),ie(this._rayEndTransformed,this.rayEnd,this.transform.inverse);const c=this.transform.transform;A(s)&&(s.objectTransform=this.transform),o.intersect(a,this.transform.transform,this,this._rayBeginTransformed,this._rayEndTransformed,(l,d,p,u,f,g)=>{if(l>=0){if(A(this.filterPredicate)&&!this.filterPredicate(this._ray.origin,this._rayEnd,l))return;const _=u?this._results.hud:this._results,w=u?O=>{const S=new xh(e,n,p,g);O.set(Ct.HUD,S,l,d,Lo,f)}:O=>O.set(Ct.OBJECT,{object:e,geometryId:n,triangleNr:p},l,d,c,f);if((_.min.drapedLayerOrder==null||f>=_.min.drapedLayerOrder)&&(_.min.dist==null||l<_.min.dist)&&w(_.min),this.options.store!==Wt.MIN&&(_.max.drapedLayerOrder==null||f<_.max.drapedLayerOrder)&&(_.max.dist==null||l>_.max.dist)&&w(_.max),this.options.store===Wt.ALL)if(u){const O=new As(this._ray);w(O),this._results.hud.all.push(O)}else{const O=new pr(this._ray);w(O),this._results.all.push(O)}}})}}sortResults(e=this._results.all){e.sort((r,i)=>r.dist!==i.dist?ae(r.dist,0)-ae(i.dist,0):r.drapedLayerOrder!==i.drapedLayerOrder?ae(r.drapedLayerOrder,Number.MAX_VALUE)-ae(i.drapedLayerOrder,Number.MAX_VALUE):ae(i.drapedLayerGraphicOrder,Number.MIN_VALUE)-ae(r.drapedLayerGraphicOrder,Number.MIN_VALUE))}};function um(t){return new Eh(t)}let Dh=class{constructor(){this.min=new pr(mt()),this.max=new pr(mt()),this.hud={min:new As(mt()),max:new As(mt()),all:new Array},this.ground=new pr(mt()),this.all=[]}init(e){this.min.init(e),this.max.init(e),this.ground.init(e),this.all.length=0,this.hud.min.init(e),this.hud.max.init(e),this.hud.all.length=0}},pr=class{get ray(){return this._ray}get distanceInRenderSpace(){return A(this.dist)?(Z(oi,this.ray.direction,this.dist),nt(oi)):null}getIntersectionPoint(e){return!!Ch(this)&&(Z(oi,this.ray.direction,this.dist),te(e,this.ray.origin,oi),!0)}getTransformedNormal(e){return k(Mr,this.normal),Mr[3]=0,sr(Mr,Mr,this.transformation),k(e,Mr),de(e,e)}constructor(e){this.intersector=Ct.OBJECT,this.normal=$(),this.transformation=Y(),this._ray=mt(),this.init(e)}init(e){this.dist=null,this.target=null,this.drapedLayerOrder=null,this.drapedLayerGraphicOrder=null,this.intersector=Ct.OBJECT,Os(e,this._ray)}set(e,r,i,s,a,o,n){this.intersector=e,this.dist=i,k(this.normal,ae(s,nl)),wt(this.transformation,ae(a,Lo)),this.target=r,this.drapedLayerOrder=o,this.drapedLayerGraphicOrder=n}copy(e){Os(e.ray,this._ray),this.intersector=e.intersector,this.dist=e.dist,this.target=e.target,this.drapedLayerOrder=e.drapedLayerOrder,this.drapedLayerGraphicOrder=e.drapedLayerGraphicOrder,k(this.normal,e.normal),wt(this.transformation,e.transformation)}},As=class extends pr{constructor(){super(...arguments),this.intersector=Ct.HUD}};function $h(t){return new pr(t)}const oi=$(),Mr=ke();function Ih(t){const e=new Map,r=i=>{let s=e.get(i);return s||(s=new Ph,e.set(i,s)),s};return t.removes.forAll(i=>{ls(i)&&r(i.material).removes.push(i)}),t.adds.forAll(i=>{ls(i)&&r(i.material).adds.push(i)}),t.updates.forAll(i=>{ls(i.renderGeometry)&&r(i.renderGeometry.material).updates.push(i)}),e}function ls(t){return t.geometry.indexCount>=1}let Mh=class{constructor(e){this._rctx=e,this._indexBuffer=this._createIndexbuffer(),this._program=this._createProgram()}_createProgram(){return this._rctx.programCache.acquire(`
    void main(void) {
      gl_Position = vec4(0.0, 0.0, float(gl_VertexID)-2.0, 1.0);
    }`,`
    void main(void) {
      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
    }`,new Map([]))}_createIndexbuffer(){return Bo.createIndex(this._rctx,Ho.STATIC_DRAW,new Uint32Array([0]))}resetIndicesType(){this._program.compiled&&this._indexBuffer&&(this._rctx.bindVAO(null),this._rctx.useProgram(this._program),this._rctx.bindBuffer(this._indexBuffer,Jc.ELEMENT_ARRAY_BUFFER),this._rctx.drawElements(Ht.POINTS,1,Qc.UNSIGNED_INT,0))}dispose(){this._program.dispose(),this._indexBuffer.dispose()}},Lh=class{constructor(e,r){this._material=e,this._repository=r,this._map=new Map}destroy(){this._map.forEach((e,r)=>{A(e)&&this._repository.release(this._material,r)})}load(e,r,i){if(!this._material.requiresSlot(r,i))return null;this._map.has(i)||this._map.set(i,this._repository.acquire(this._material,r,i));const s=this._map.get(i);if(A(s)){if(s.ensureResources(e)===ed.LOADED)return s;this._repository.requestRender()}return null}},Nh=class extends Ql{constructor(e=$()){super(),this.origin=e,this.slicePlaneLocalOrigin=this.origin}};const Fh=bt().vec3f(h.POSITION),zh=bt().vec3f(h.POSITION).vec2f(h.UV0),mn=bt().vec3f(h.POSITION).vec4u8(h.COLOR);bt().vec3f(h.POSITION).vec4u8(h.OBJECTANDLAYERIDCOLOR);bt().vec3f(h.POSITION).vec2f(h.UV0).vec4u8(h.OBJECTANDLAYERIDCOLOR);const jh=bt().vec3f(h.POSITION).vec4u8(h.COLOR).vec4u8(h.OBJECTANDLAYERIDCOLOR);let gn=class extends Vi{intersect(e,r,i,s,a,o){return Kl(e,i,s,a,void 0,o)}};function Vh(t){t.fragment.code.add(m`float normals2FoamIntensity(vec3 n, float waveStrength){
float normalizationFactor =  max(0.015, waveStrength);
return max((n.x + n.y)*0.3303545/normalizationFactor + 0.3303545, 0.0);
}`)}function Uh(t){t.fragment.code.add(m`vec3 foamIntensity2FoamColor(float foamIntensityExternal, float foamPixelIntensity, vec3 skyZenitColor, float dayMod){
return foamIntensityExternal * (0.075 * skyZenitColor * pow(foamPixelIntensity, 4.) +  50.* pow(foamPixelIntensity, 23.0)) * dayMod;
}`)}function ka(t){t.fragment.uniforms.add(new Ut("texWaveNormal",e=>e.waveNormal)),t.fragment.uniforms.add(new Ut("texWavePerturbation",e=>e.wavePertubation)),t.fragment.uniforms.add([new Ee("waveParams",e=>Gr(Hh,e.waveStrength,e.waveTextureRepeat,e.flowStrength,e.flowOffset)),new St("waveDirection",e=>Vt(Gh,e.waveDirection[0]*e.waveVelocity,e.waveDirection[1]*e.waveVelocity))]),t.include(Vh),t.fragment.code.add(m`const vec2  FLOW_JUMP = vec2(6.0/25.0, 5.0/24.0);
vec2 textureDenormalized2D(sampler2D _tex, vec2 _uv) {
return 2.0 * texture2D(_tex, _uv).rg - 1.0;
}
float sampleNoiseTexture(vec2 _uv) {
return texture2D(texWavePerturbation, _uv).b;
}
vec3 textureDenormalized3D(sampler2D _tex, vec2 _uv) {
return 2.0 * texture2D(_tex, _uv).rgb - 1.0;
}
float computeProgress(vec2 uv, float time) {
return fract(time);
}
float computeWeight(vec2 uv, float time) {
float progress = computeProgress(uv, time);
return 1.0 - abs(1.0 - 2.0 * progress);
}
vec3 computeUVPerturbedWeigth(sampler2D texFlow, vec2 uv, float time, float phaseOffset) {
float flowStrength = waveParams[2];
float flowOffset = waveParams[3];
vec2 flowVector = textureDenormalized2D(texFlow, uv) * flowStrength;
float progress = computeProgress(uv, time + phaseOffset);
float weight = computeWeight(uv, time + phaseOffset);
vec2 result = uv;
result -= flowVector * (progress + flowOffset);
result += phaseOffset;
result += (time - progress) * FLOW_JUMP;
return vec3(result, weight);
}
const float TIME_NOISE_TEXTURE_REPEAT = 0.3737;
const float TIME_NOISE_STRENGTH = 7.77;
vec3 getWaveLayer(sampler2D _texNormal, sampler2D _dudv, vec2 _uv, vec2 _waveDir, float time) {
float waveStrength = waveParams[0];
vec2 waveMovement = time * -_waveDir;
float timeNoise = sampleNoiseTexture(_uv * TIME_NOISE_TEXTURE_REPEAT) * TIME_NOISE_STRENGTH;
vec3 uv_A = computeUVPerturbedWeigth(_dudv, _uv + waveMovement, time + timeNoise, 0.0);
vec3 uv_B = computeUVPerturbedWeigth(_dudv, _uv + waveMovement, time + timeNoise, 0.5);
vec3 normal_A = textureDenormalized3D(_texNormal, uv_A.xy) * uv_A.z;
vec3 normal_B = textureDenormalized3D(_texNormal, uv_B.xy) * uv_B.z;
vec3 mixNormal = normalize(normal_A + normal_B);
mixNormal.xy *= waveStrength;
mixNormal.z = sqrt(1.0 - dot(mixNormal.xy, mixNormal.xy));
return mixNormal;
}
vec4 getSurfaceNormalAndFoam(vec2 _uv, float _time) {
float waveTextureRepeat = waveParams[1];
vec3 normal = getWaveLayer(texWaveNormal, texWavePerturbation, _uv * waveTextureRepeat, waveDirection, _time);
float foam  = normals2FoamIntensity(normal, waveParams[0]);
return vec4(normal, foam);
}`)}const Hh=ke(),Gh=K();function qa(t,e){e.spherical?t.vertex.code.add(m`vec3 getLocalUp(in vec3 pos, in vec3 origin) {
return normalize(pos + origin);
}`):t.vertex.code.add(m`vec3 getLocalUp(in vec3 pos, in vec3 origin) {
return vec3(0.0, 0.0, 1.0);
}`),e.spherical?t.vertex.code.add(m`mat3 getTBNMatrix(in vec3 n) {
vec3 t = normalize(cross(vec3(0.0, 0.0, 1.0), n));
vec3 b = normalize(cross(n, t));
return mat3(t, b, n);
}`):t.vertex.code.add(m`mat3 getTBNMatrix(in vec3 n) {
vec3 t = vec3(1.0, 0.0, 0.0);
vec3 b = normalize(cross(n, t));
return mat3(t, b, n);
}`)}function Wh(t){t.fragment.code.add(m`const float GAMMA = 2.2;
const float INV_GAMMA = 0.4545454545;
vec4 delinearizeGamma(vec4 color) {
return vec4(pow(color.rgb, vec3(INV_GAMMA)), color.w);
}
vec3 linearizeGamma(vec3 color) {
return pow(color, vec3(GAMMA));
}`)}let Bh=class extends ec{constructor(e,r){super(e,"samplerCube",tc.Pass,(i,s,a)=>i.bindTexture(e,r(s,a)))}};function kh(t){const e=t.fragment;e.uniforms.add([new _r("rotationMatrixClouds",(r,i)=>i.cloudsFade.parallax.transform),new _r("rotationMatrixCloudsCrossFade",(r,i)=>i.cloudsFade.parallaxNew.transform),new ar("anchorPosition",(r,i)=>i.cloudsFade.parallax.anchorPointClouds),new ar("anchorPositionCrossFade",(r,i)=>i.cloudsFade.parallaxNew.anchorPointClouds),new B("cloudsHeight",(r,i)=>i.cloudsFade.parallax.cloudsHeight),new B("radiusCurvatureCorrectionFactor",(r,i)=>i.cloudsFade.parallax.radiusCurvatureCorrectionFactor),new B("totalFadeInOut",(r,i)=>i.cloudsFade.fadeInOut.stage===ur.FINISHED?i.cloudsFade.fadeInOutHeight.factor+1-i.cloudsFade.fadeIn.factor:i.cloudsFade.fadeInOutHeight.factor+1-i.cloudsFade.fadeInOut.factor),new B("crossFadeAnchorFactor",(r,i)=>zt(i.cloudsFade.crossFade.factor,0,1)),new Bh("cubeMap",(r,i)=>A(i.cloudsFade.data)&&A(i.cloudsFade.data.cubeMap)?i.cloudsFade.data.cubeMap.colorTexture:null),new Zi("crossFade",(r,i)=>i.cloudsFade.crossFade.enabled),new Zi("readChannelsRG",(r,i)=>i.cloudsFade.readChannels===Di.RG),new Zi("fadeTextureChannels",(r,i)=>i.cloudsFade.renderingStage===Xr.FADING_TEXTURE_CHANNELS)]),e.constants.add("planetRadius","float",Nc.radius),e.code.add(m`vec3 intersectWithCloudLayer(vec3 dir, vec3 cameraPosition, vec3 spherePos)
{
float radiusClouds = planetRadius + cloudsHeight;
float B = 2.0 * dot(cameraPosition, dir);
float C = dot(cameraPosition, cameraPosition) - radiusClouds * radiusClouds;
float det = B * B - 4.0 * C;
float pointIntDist = max(0.0, 0.5 *(-B + sqrt(det)));
vec3 intersectionPont = cameraPosition + dir * pointIntDist;
intersectionPont =  intersectionPont - spherePos;
return intersectionPont;
}`),e.code.add(m`vec3 correctForPlanetCurvature(vec3 dir)
{
dir.z = dir.z*(1.-radiusCurvatureCorrectionFactor) + radiusCurvatureCorrectionFactor;
return dir;
}`),e.code.add(m`vec3 rotateDirectionToAnchorPoint(mat4 rotMat, vec3 inVec)
{
return (rotMat * vec4(inVec, 0.0)).xyz;
}`),Co(e),bo(e),e.code.add(m`const float SUNSET_TRANSITION_FACTOR = 0.3;
const vec3 RIM_COLOR = vec3(0.28, 0.175, 0.035);
const float RIM_SCATTERING_FACTOR = 140.0;
const float BACKLIGHT_FACTOR = 0.2;
const float BACKLIGHT_SCATTERING_FACTOR = 10.0;
const float BACKLIGHT_TRANSITION_FACTOR = 0.3;
vec3 calculateCloudColor(vec3 cameraPosition, vec3 worldSpaceRay, vec4 clouds)
{
float upDotLight = dot(normalize(cameraPosition), normalize(mainLightDirection));
float dirDotLight = max(dot(normalize(-worldSpaceRay), normalize(mainLightDirection)), 0.0);
float sunsetTransition = clamp(pow(max(upDotLight, 0.0), SUNSET_TRANSITION_FACTOR), 0.0, 1.0);
vec3 ambientLight = calculateAmbientIrradiance(normalize(cameraPosition),  0.0);
vec3 mainLight = evaluateMainLighting(normalize(cameraPosition),  0.0);
vec3 combinedLight = clamp((mainLightIntensity + ambientLight )/PI, vec3(0.0), vec3(1.0));
vec3 baseCloudColor = pow(combinedLight * pow(clouds.xyz, vec3(GAMMA)), vec3(INV_GAMMA));
float scatteringMod = max(clouds.a < 0.5 ? clouds.a / 0.5 : - clouds.a / 0.5 + 2.0, 0.0);
float rimLightIntensity = 0.5 + 0.5 *pow(max(upDotLight, 0.0), 0.35);
vec3 directSunScattering = RIM_COLOR * rimLightIntensity * (pow(dirDotLight, RIM_SCATTERING_FACTOR)) * scatteringMod;
float additionalLight = BACKLIGHT_FACTOR * pow(dirDotLight, BACKLIGHT_SCATTERING_FACTOR) * (1. - pow(sunsetTransition, BACKLIGHT_TRANSITION_FACTOR)) ;
return vec3(baseCloudColor * (1. + additionalLight) + directSunScattering);
}`),e.code.add(m`vec4 getCloudData(vec3 rayDir, bool readOtherChannel)
{
vec4 cloudData = textureCube(cubeMap, rayDir);
float mu = dot(rayDir, vec3(0, 0, 1));
bool readChannels = readChannelsRG ^^ readOtherChannel;
if (readChannels) {
cloudData = vec4(vec3(cloudData.r), cloudData.g);
} else {
cloudData = vec4(vec3(cloudData.b), cloudData.a);
}
if (length(cloudData) == 0.0) {
return vec4(cloudData.rgb, 1.0);
}
return cloudData;
}`),e.code.add(m`vec4 renderCloudsNoFade(vec3 worldRay, vec3 cameraPosition)
{
vec3 intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPosition);
vec3 worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixClouds, normalize(intersectionPoint));
vec3 worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);
vec4 cloudData = getCloudData(worldRayRotatedCorrected, false);
float totalTransmittance = clamp(cloudData.a * (1.0 - totalFadeInOut) + totalFadeInOut, 0.0 , 1.0);
if (length(cloudData.rgb) == 0.0) {
totalTransmittance = 1.0;
}
return vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), totalTransmittance);
}`),e.code.add(m`vec4 renderCloudsCrossFade(vec3 worldRay, vec3 cameraPosition)
{
vec3 intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPosition);
vec3 worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixClouds, normalize(intersectionPoint));
vec3 worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);
vec4 cloudData = getCloudData(worldRayRotatedCorrected, false);
vec4 cloudColor = vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), cloudData.a);
intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPositionCrossFade);
worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixCloudsCrossFade, normalize(intersectionPoint));
worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);
cloudData = getCloudData(worldRayRotatedCorrected, fadeTextureChannels);
vec4 cloudColorCrossFade = vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), cloudData.a);
cloudColor = mix(cloudColor, cloudColorCrossFade, crossFadeAnchorFactor);
float totalTransmittance = clamp(cloudColor.a * (1.0 - totalFadeInOut) + totalFadeInOut, 0.0 , 1.0);
if (length(cloudColor.rgb) == 0.0) {
totalTransmittance = 1.0;
}
return vec4(cloudColor.rgb, totalTransmittance);
}`),e.code.add(m`vec4 renderClouds(vec3 worldRay, vec3 cameraPosition)
{
return crossFade ? renderCloudsCrossFade(worldRay, cameraPosition) : renderCloudsNoFade(worldRay, cameraPosition);
}`)}function qh(t,e){t.include(rc,e),t.include(Wh),t.include(Uh),e.hasCloudsReflections&&t.include(kh,e),e.hasScreenSpaceReflections&&t.include(dh,e);const r=t.fragment;r.constants.add("fresnelSky","vec3",[.02,1,15]).add("fresnelMaterial","vec2",[.02,.1]).add("roughness","float",.015).add("foamIntensityExternal","float",1.7).add("ssrIntensity","float",.65).add("ssrHeightFadeStart","float",3e5).add("ssrHeightFadeEnd","float",5e5).add("waterDiffusion","float",.92).add("waterSeaColorMod","float",.8).add("correctionViewingPowerFactor","float",.4).add("skyZenitColor","vec3",[.52,.68,.9]).add("skyColor","vec3",[.67,.79,.9]).add("cloudFresnelModifier","vec2",[1.2,.01]),r.code.add(m`PBRShadingWater shadingInfo;
vec3 getSkyGradientColor(in float cosTheta, in vec3 horizon, in vec3 zenit) {
float exponent = pow((1.0 - cosTheta), fresnelSky[2]);
return mix(zenit, horizon, exponent);
}`),r.uniforms.add([new B("lightingSpecularStrength",(i,s)=>s.lighting.mainLight.specularStrength),new B("lightingEnvironmentStrength",(i,s)=>s.lighting.mainLight.environmentStrength)]),r.code.add(m`vec3 getSeaColor(in vec3 n, in vec3 v, in vec3 l, vec3 color, in vec3 lightIntensity, in vec3 localUp, in float shadow, float foamIntensity, vec3 viewPosition, vec3 position) {
float reflectionHit = 0.0;
float reflectionHitDiffused = 0.0;
vec3 seaWaterColor = linearizeGamma(color);
vec3 h = normalize(l + v);
shadingInfo.NdotL = clamp(dot(n, l), 0.0, 1.0);
shadingInfo.NdotV = clamp(dot(n, v), 0.001, 1.0);
shadingInfo.VdotN = clamp(dot(v, n), 0.001, 1.0);
shadingInfo.NdotH = clamp(dot(n, h), 0.0, 1.0);
shadingInfo.VdotH = clamp(dot(v, h), 0.0, 1.0);
shadingInfo.LdotH = clamp(dot(l, h), 0.0, 1.0);
float upDotV = max(dot(localUp,v), 0.0);
vec3 skyHorizon = linearizeGamma(skyColor);
vec3 skyZenit = linearizeGamma(skyZenitColor);
vec3 skyColor = getSkyGradientColor(upDotV, skyHorizon, skyZenit );
float upDotL = max(dot(localUp,l),0.0);
float daytimeMod = 0.1 + upDotL * 0.9;
skyColor *= daytimeMod;
float shadowModifier = clamp(shadow, 0.8, 1.0);
vec3 fresnelModifier = fresnelReflection(shadingInfo.VdotN, vec3(fresnelSky[0]), fresnelSky[1]);
vec3 reflSky = lightingEnvironmentStrength * fresnelModifier * skyColor * shadowModifier;
vec3 reflSea = seaWaterColor * mix(skyColor, upDotL * lightIntensity * LIGHT_NORMALIZATION, 2.0 / 3.0) * shadowModifier;
vec3 specular = vec3(0.0);
if(upDotV > 0.0 && upDotL > 0.0) {
vec3 specularSun = brdfSpecularWater(shadingInfo, roughness, vec3(fresnelMaterial[0]), fresnelMaterial[1]);
vec3 incidentLight = lightIntensity * LIGHT_NORMALIZATION * shadow;
specular = lightingSpecularStrength * shadingInfo.NdotL * incidentLight * specularSun;
}
vec3 foam = vec3(0.0);
if(upDotV > 0.0) {
foam = foamIntensity2FoamColor(foamIntensityExternal, foamIntensity, skyZenitColor, daytimeMod);
}
float correctionViewingFactor = pow(max(dot(v, localUp), 0.0), correctionViewingPowerFactor);
vec3 normalCorrectedClouds = mix(localUp, n, correctionViewingFactor);
vec3 reflectedWorld = normalize(reflect(-v, normalCorrectedClouds));`),e.hasCloudsReflections&&r.code.add(m`vec4 cloudsColor = renderClouds(reflectedWorld, position);
cloudsColor.a = 1.0 - cloudsColor.a;
cloudsColor = pow(cloudsColor, vec4(GAMMA));
cloudsColor *= clamp(fresnelModifier.y*cloudFresnelModifier[0] - cloudFresnelModifier[1], 0.0, 1.0) * clamp((1.0 - totalFadeInOut), 0.0, 1.0);`),e.hasScreenSpaceReflections?(r.uniforms.add([new _r("view",(i,s)=>s.ssr.camera.viewMatrix),new Ut("lastFrameColorTexture",(i,s)=>s.ssr.lastFrameColorTexture),new B("fadeFactor",(i,s)=>s.ssr.fadeFactor)]),r.code.add(m`vec3 viewDir = normalize(viewPosition);
vec4 viewNormalVectorCoordinate = view *vec4(n, 0.0);
vec3 viewNormal = normalize(viewNormalVectorCoordinate.xyz);
vec4 viewUp = view * vec4(localUp, 0.0);
vec3 viewNormalCorrectedSSR = mix(viewUp.xyz, viewNormal, correctionViewingFactor);
vec3 reflected = normalize(reflect(viewDir, viewNormalCorrectedSSR));
vec3 hitCoordinate = screenSpaceIntersection(reflected, viewPosition, viewDir, viewUp.xyz);
vec3 reflectedColor = vec3(0.0);
if (hitCoordinate.z > 0.0)
{
vec2 reprojectedCoordinate = reprojectionCoordinate(hitCoordinate);
vec2 dCoords = smoothstep(0.3, 0.6, abs(vec2(0.5, 0.5) - hitCoordinate.xy));
float heightMod = smoothstep(ssrHeightFadeEnd, ssrHeightFadeStart, -viewPosition.z);
reflectionHit = clamp(1.0 - (1.3 * dCoords.y), 0.0, 1.0) * heightMod * fadeFactor;
reflectionHitDiffused = waterDiffusion * reflectionHit;
reflectedColor = linearizeGamma(texture2D(lastFrameColorTexture, reprojectedCoordinate).xyz) *
reflectionHitDiffused * fresnelModifier.y * ssrIntensity;
}
float seaColorMod =  mix(waterSeaColorMod, waterSeaColorMod * 0.5, reflectionHitDiffused);
vec3 waterRenderedColor = tonemapACES((1.0 - reflectionHitDiffused) * reflSky + reflectedColor +
reflSea * seaColorMod + specular + foam);`)):r.code.add(m`vec3 waterRenderedColor = tonemapACES(reflSky + reflSea * waterSeaColorMod + specular + foam);`),e.hasCloudsReflections?e.hasScreenSpaceReflections?r.code.add(m`return waterRenderedColor * (1.0 - (1.0 - reflectionHit) * cloudsColor.a) + (1.0 - reflectionHit) * cloudsColor.xyz;
}`):r.code.add(m`return waterRenderedColor * (1.0 - cloudsColor.a) + cloudsColor.xyz;
}`):r.code.add(m`return waterRenderedColor;
}`)}function vn(t){const e=new Yr,{vertex:r,fragment:i}=e;Jr(r,t),e.include(zs,t),e.attributes.add(h.POSITION,"vec3"),e.attributes.add(h.UV0,"vec2");const s=new Ee("waterColor",a=>a.color);if(t.output===T.Color&&t.isDraped)return e.varyings.add("vpos","vec3"),r.uniforms.add(s),r.code.add(m`
        void main(void) {
          if (waterColor.a < ${m.float(Ve)}) {
            // Discard this vertex
            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
            return;
          }

          vpos = position;
          gl_Position = transformPosition(proj, view, vpos);
        }
    `),i.uniforms.add(s),i.code.add(m`void main() {
gl_FragColor = waterColor;
}`),e;switch(t.output!==T.Color&&t.output!==T.Alpha||(e.include(qa,t),e.include(ic,t),e.varyings.add("vuv","vec2"),e.varyings.add("vpos","vec3"),e.varyings.add("vnormal","vec3"),e.varyings.add("vtbnMatrix","mat3"),t.hasMultipassTerrain&&e.varyings.add("depth","float"),r.uniforms.add(s),r.code.add(m`
      void main(void) {
        if (waterColor.a < ${m.float(Ve)}) {
          // Discard this vertex
          gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
          return;
        }

        vuv = uv0;
        vpos = position;

        vnormal = getLocalUp(vpos, localOrigin);
        vtbnMatrix = getTBNMatrix(vnormal);

        ${t.hasMultipassTerrain?"depth = (view * vec4(vpos, 1.0)).z;":""}

        gl_Position = transformPosition(proj, view, vpos);
        ${t.output===T.Color?"forwardLinearDepth();":""}
      }
    `)),e.include(Ls,t),t.output){case T.Alpha:e.include(vt,t),i.uniforms.add(s),i.code.add(m`
        void main() {
          discardBySlice(vpos);
          ${t.hasMultipassTerrain?"terrainDepthTest(gl_FragCoord, depth);":""}

          gl_FragColor = vec4(waterColor.a);
        }
      `);break;case T.Color:e.include(sc,t),e.include(ac,{pbrMode:Ao.Disabled,lightingSphericalHarmonicsOrder:2}),e.include(ka),e.include(vt,t),e.include(oc,t),e.include(qh,t),i.uniforms.add([s,new B("timeElapsed",a=>a.timeElapsed),r.uniforms.get("view"),r.uniforms.get("localOrigin")]),Is(i,t),i.include(ji),Co(i),bo(i),i.code.add(m`
      void main() {
        discardBySlice(vpos);
        ${t.hasMultipassTerrain?"terrainDepthTest(gl_FragCoord, depth);":""}
        vec3 localUp = vnormal;
        // the created normal is in tangent space
        vec4 tangentNormalFoam = getSurfaceNormalAndFoam(vuv, timeElapsed);

        // we rotate the normal according to the tangent-bitangent-normal-Matrix
        vec3 n = normalize(vtbnMatrix * tangentNormalFoam.xyz);
        vec3 v = -normalize(vpos - cameraPosition);
        float shadow = ${t.receiveShadows?m`1.0 - readShadowMap(vpos, linearDepth)`:"1.0"};
        vec4 vPosView = view * vec4(vpos, 1.0);
        vec4 final = vec4(getSeaColor(n, v, mainLightDirection, waterColor.rgb, mainLightIntensity, localUp, shadow, tangentNormalFoam.w, vPosView.xyz, vpos + localOrigin), waterColor.w);

        // gamma correction
        gl_FragColor = delinearizeGamma(final);
        gl_FragColor = highlightSlice(gl_FragColor, vpos);
        ${t.transparencyPassType===re.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}
      }
    `);break;case T.Normal:e.include(qa,t),e.include(ka,t),e.include(vt,t),e.varyings.add("vpos","vec3"),e.varyings.add("vuv","vec2"),r.uniforms.add(s),r.code.add(m`
        void main(void) {
          if (waterColor.a < ${m.float(Ve)}) {
            // Discard this vertex
            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
            return;
          }

          vuv = uv0;
          vpos = position;

          gl_Position = transformPosition(proj, view, vpos);
        }
    `),i.uniforms.add(new B("timeElapsed",a=>a.timeElapsed)),i.code.add(m`void main() {
discardBySlice(vpos);
vec4 tangentNormalFoam = getSurfaceNormalAndFoam(vuv, timeElapsed);
tangentNormalFoam.xyz = normalize(tangentNormalFoam.xyz);
gl_FragColor = vec4((tangentNormalFoam.xyz + vec3(1.0)) * 0.5, tangentNormalFoam.w);
}`);break;case T.Highlight:e.include(Hi,t),e.varyings.add("vpos","vec3"),r.uniforms.add(s),r.code.add(m`
      void main(void) {
        if (waterColor.a < ${m.float(Ve)}) {
          // Discard this vertex
          gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
          return;
        }

        vpos = position;
        gl_Position = transformPosition(proj, view, vpos);
      }
    `),e.include(vt,t),i.code.add(m`void main() {
discardBySlice(vpos);
outputHighlight();
}`)}return e}const Xh=Object.freeze(Object.defineProperty({__proto__:null,build:vn},Symbol.toStringTag,{value:"Module"}));let _n=class yn extends Ar{initializeConfiguration(e,r){r.hasWebGL2Context=e.rctx.type===zi.WEBGL2,r.spherical=e.viewingMode===Ot.Global,r.doublePrecisionRequiresObfuscation=e.rctx.driverTest.doublePrecisionRequiresObfuscation.result}initializeProgram(e){return new Rr(e.rctx,yn.shader.get().build(this.configuration),Qr)}_setPipelineState(e){const r=this.configuration,i=e===re.NONE,s=e===re.FrontFace;return et({blending:r.output!==T.Normal&&r.output!==T.Highlight&&r.transparent?i?jr:Wi(e):null,depthTest:{func:Bs(e)},depthWrite:i?r.writeDepth?yr:null:No(e),colorWrite:lt,polygonOffset:i||s?null:Fo(r.enableOffset)})}initializePipeline(){return this._setPipelineState(this.configuration.transparencyPassType)}};_n.shader=new Pr(Xh,()=>Tr(()=>Promise.resolve().then(()=>Vp),void 0));let ue=class extends br{constructor(){super(...arguments),this.output=T.Color,this.transparencyPassType=re.NONE,this.spherical=!1,this.receiveShadows=!1,this.hasSlicePlane=!1,this.transparent=!1,this.enableOffset=!0,this.writeDepth=!1,this.hasScreenSpaceReflections=!1,this.doublePrecisionRequiresObfuscation=!1,this.hasCloudsReflections=!1,this.isDraped=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1}};v([x({count:T.COUNT})],ue.prototype,"output",void 0),v([x({count:re.COUNT})],ue.prototype,"transparencyPassType",void 0),v([x()],ue.prototype,"spherical",void 0),v([x()],ue.prototype,"receiveShadows",void 0),v([x()],ue.prototype,"hasSlicePlane",void 0),v([x()],ue.prototype,"transparent",void 0),v([x()],ue.prototype,"enableOffset",void 0),v([x()],ue.prototype,"writeDepth",void 0),v([x()],ue.prototype,"hasScreenSpaceReflections",void 0),v([x()],ue.prototype,"doublePrecisionRequiresObfuscation",void 0),v([x()],ue.prototype,"hasCloudsReflections",void 0),v([x()],ue.prototype,"isDraped",void 0),v([x()],ue.prototype,"hasMultipassTerrain",void 0),v([x()],ue.prototype,"cullAboveGround",void 0),v([x({constValue:Ao.Water})],ue.prototype,"pbrMode",void 0),v([x({constValue:!0})],ue.prototype,"useCustomDTRExponentForWater",void 0),v([x({constValue:!0})],ue.prototype,"highStepCount",void 0),v([x({constValue:!1})],ue.prototype,"useFillLights",void 0);let Zh=class extends Ui{_updateShadowState(e){e.shadowMap.enabled!==this._material.parameters.receiveShadows&&this._material.setParameters({receiveShadows:e.shadowMap.enabled})}_updateSSRState(e){e.ssr.enabled!==this._material.parameters.hasScreenSpaceReflections&&this._material.setParameters({hasScreenSpaceReflections:e.ssr.enabled})}_updateCloudsReflectionState(e){const r=A(e.cloudsFade.data);r!==this._material.parameters.hasCloudsReflections&&this._material.setParameters({hasCloudsReflections:r})}ensureResources(e){return this._techniqueRepository.constructionContext.waterTextureRepository.ensureResources(e)}beginSlot(e){return this._output===T.Color&&(this._updateShadowState(e),this._updateSSRState(e),this._updateCloudsReflectionState(e)),this._material.setParameters(this._techniqueRepository.constructionContext.waterTextureRepository.passParameters),this.ensureTechnique(_n,e)}},Yh=class extends gn{constructor(e){super(e,new Jh),this._configuration=new ue,this.animation=new Ad}getConfiguration(e,r){return this._configuration.output=e,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.receiveShadows=this.parameters.receiveShadows,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.hasScreenSpaceReflections=this.parameters.hasScreenSpaceReflections,this._configuration.hasCloudsReflections=this.parameters.hasCloudsReflections,this._configuration.isDraped=this.parameters.isDraped,this._configuration.transparencyPassType=r.transparencyPassType,this._configuration.enableOffset=r.camera.relativeElevation<zo,this._configuration.hasMultipassTerrain=r.multipassTerrain.enabled,this._configuration.cullAboveGround=r.multipassTerrain.cullAboveGround,this._configuration}update(e){const r=Math.min(e.camera.relativeElevation,e.camera.distance);this.animation.enabled=Math.sqrt(this.parameters.waveTextureRepeat/this.parameters.waveStrength)*r<Qh;const i=this.animation.advance(e);return this.setParameters({timeElapsed:Fc(this.animation.time)*this.parameters.animationSpeed},!1),this.animation.enabled&&i}requiresSlot(e,r){switch(r){case T.Normal:return e===W.DRAPED_WATER;case T.Color:if(this.parameters.isDraped)return e===W.DRAPED_MATERIAL;break;case T.Alpha:break;case T.Highlight:return e===W.OPAQUE_MATERIAL||e===W.DRAPED_MATERIAL;default:return!1}let i=W.OPAQUE_MATERIAL;return this.parameters.transparent&&(i=this.parameters.writeDepth?W.TRANSPARENT_MATERIAL:W.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL),e===i}createGLMaterial(e){return new Zh(e)}createBufferWriter(){return new js(zh)}},Jh=class extends Vs{constructor(){super(...arguments),this.waveStrength=.06,this.waveTextureRepeat=32,this.waveDirection=Es(1,0),this.waveVelocity=.05,this.flowStrength=.015,this.flowOffset=-.5,this.animationSpeed=.35,this.timeElapsed=0,this.color=vr(0,0,0,0),this.transparent=!0,this.writeDepth=!0,this.hasSlicePlane=!1,this.isDraped=!1,this.receiveShadows=!0,this.hasScreenSpaceReflections=!1,this.hasCloudsReflections=!1}};const Qh=35e3;let Ks=class{constructor(e=0,r=0){this.from=e,this.to=r}get numElements(){return this.to-this.from}};function Xa(t){const e=new Map;t.forAll(i=>e.set(i.from,i));let r=!0;for(;r;)r=!1,t.forEach(i=>{const s=e.get(i.to);s&&(i.to=s.to,e.delete(s.from),t.removeUnordered(s),r=!0)})}let Za=class extends Ks{constructor(e,r,i){super(r,i),this.geometry=e}get isVisible(){return this.geometry.visible}get hasHighlights(){return A(this.geometry.highlights)&&this.isVisible}get hasOccludees(){return A(this.geometry.occludees)}},Kh=class{constructor(){this.first=0,this.count=0}},eu=class{constructor(){this._numElements=0,this._instances=new Map,this.holes=new it({allocator:e=>e||new Ks,deallocator:null}),this.hasHiddenInstances=!1,this.hasHighlights=!1,this.hasOccludees=!1,this.drawCommandsDirty=!0,this.drawCommandsDefault=ni(),this.drawCommandsHighlight=ni(),this.drawCommandsOccludees=ni(),this.drawCommandsShadowHighlightRest=ni()}get numElements(){return this._numElements}get instances(){return this._instances}addInstance(e,r){this.deleteInstance(e),this._instances.set(e,r),this._numElements+=r.numElements}deleteInstance(e){const r=this._instances.get(e);r&&(this._numElements-=r.numElements,this._instances.delete(e))}updateInstance(e,r,i){const s=this._instances.get(e);s&&(this._numElements-=s.numElements,s.from=r,s.to=i,this._numElements+=s.numElements)}updateDrawState(e){e.isVisible?(e.hasHighlights&&(this.hasHighlights=!0),e.hasOccludees&&(this.hasOccludees=!0)):this.hasHiddenInstances=!0}updateDrawCommands(e){if(this.drawCommandsDefault.clear(),this.drawCommandsHighlight.clear(),this.drawCommandsOccludees.clear(),this.drawCommandsShadowHighlightRest.clear(),this.drawCommandsDirty=!1,this._instances.size===0)return;if(!this.needsMultipleCommands()){const i=this.drawCommandsDefault.pushNew(),s=this.holes.front();return A(this.vao)&&this.holes.length===1&&s.to===Math.floor(this.vao.size/e)?(i.first=0,void(i.count=s.from)):(i.first=1/0,i.count=0,this._instances.forEach(a=>{i.first=Math.min(i.first,a.from),i.count=Math.max(i.count,a.to)}),void(i.count-=i.first))}const r=Array.from(this._instances.values()).sort((i,s)=>i.from===s.from?i.to-s.to:i.from-s.from);for(const i of r)i.isVisible&&(Ya(i.hasOccludees?this.drawCommandsOccludees:this.drawCommandsDefault,i),Ya(i.hasHighlights?this.drawCommandsHighlight:this.drawCommandsShadowHighlightRest,i))}needsMultipleCommands(){return this.hasOccludees||this.hasHighlights||this.hasHiddenInstances}};function tu(t){return A(t.vao)}function ni(){return new it({allocator:t=>t||new Kh,deallocator:t=>t})}function Ya(t,e){const r=t.back();if(r==null){const i=t.pushNew();return i.first=e.from,void(i.count=e.numElements)}if(ru(r,e)){const i=e.from-r.first+e.numElements;r.count=i}else{const i=t.pushNew();i.first=e.from,i.count=e.numElements}}function ru(t,e){return t.first+t.count>=e.from}let iu=class{constructor(e){this.origin=e,this.buffers=new Array}dispose(){this.buffers.forEach(e=>e.vao.dispose()),this.buffers.length=0}findBuffer(e){return this.buffers.find(r=>r.instances.has(e))}};const su=cl+1;let au=class{constructor(e,r,i){this._rctx=e,this._locations=r,this._layout=i,this._cache=e.newCache(`VaoCache ${Gs()}`,ou)}dispose(){this._cache.destroy()}newVao(e){const r=e.toString(),i=this._cache.pop(r);if(A(i)){const a=i.pop();return i.length>0&&this._cache.put(r,i,e*i.length,su),a}const s=new nc(this._rctx,this._locations,{geometry:this._layout},{geometry:Bo.createVertex(this._rctx,Ho.STATIC_DRAW)});return s.vertexBuffers.geometry.setSize(e),s}deleteVao(e){if(N(e))return null;const r=e.size,i=r.toString(),s=this._cache.pop(i);return A(s)?(s.push(e),this._cache.put(i,s,r*s.length,-1)):this._cache.put(i,[e],r,-1),null}};function ou(t,e){if(e===ll.ALL)return void t.forEach(s=>s.dispose());const r=t.pop(),i=t.length*r.size;return r.dispose(),i}let nu=class{constructor(e,r,i){this._rctx=e,this._materialRepository=r,this.material=i,this._dataByOrigin=new Map,this._appleAmdDriverHelper=null,this._hasHighlights=!1,this._hasOccludees=!1,this._glMaterials=new Lh(this.material,this._materialRepository),this._bufferWriter=i.createBufferWriter(),this._vaoCache=new au(e,i.vertexAttributeLocations,ld(this._bufferWriter.vertexBufferLayout)),this._rctx.driverTest.drawArraysRequiresIndicesTypeReset.result&&(this._appleAmdDriverHelper=new Mh(this._rctx))}dispose(){var e;this._glMaterials.destroy(),this._dataByOrigin.forEach(r=>r.dispose()),this._dataByOrigin.clear(),this._vaoCache.dispose(),(e=this._appleAmdDriverHelper)==null||e.dispose()}get isEmpty(){return this._dataByOrigin.size===0}get hasHighlights(){return this._hasHighlights}get hasOccludees(){return this._hasOccludees}get hasWater(){return!this.isEmpty&&this.material instanceof Yh}get rendersOccluded(){return!this.isEmpty&&this.material.renderOccluded!==xt.Occlude}get numGeometries(){let e=0;return this._dataByOrigin.forEach(r=>e+=r.buffers.reduce((i,s)=>i+s.instances.size,0)),e}forEachGeometry(e){this._dataByOrigin.forEach(r=>r.buffers.forEach(i=>i.instances.forEach(s=>e(s.geometry))))}modify(e){this._updateGeometries(e.updates),this._addAndRemoveGeometries(e.adds,e.removes),this._updateDrawCommands()}_updateGeometries(e){var s;const r=this._bufferWriter,i=r.vertexBufferLayout.stride/4;for(const a of e){const o=a.renderGeometry,n=(s=this._dataByOrigin.get(o.localOrigin.id))==null?void 0:s.findBuffer(o.id);if(N(n))return;const c=n.instances.get(o.id);if(a.updateType&(Ke.GEOMETRY|Ke.TRANSFORMATION)){const l=hi(r.elementCount(c.geometry.geometry)*i),d=r.vertexBufferLayout.createView(l.buffer);this._writeGeometry(o,d,0),n.vao.vertexBuffers.geometry.setSubData(l,c.from*i,0,c.numElements*i)}a.updateType&(Ke.HIGHLIGHT|Ke.OCCLUDEE|Ke.VISIBILITY)&&(n.drawCommandsDirty=!0)}}_computeDeltas(e,r){var s;const i=new Xs;for(const a of e){const o=a.localOrigin;if(N(o))continue;let n=i.get(o.id,null);N(n)&&(n=new Ja(o.vec3),i.set(o.id,null,n)),n.changes.push(a)}for(const a of r){const o=a.localOrigin;if(N(o))continue;const n=(s=this._dataByOrigin.get(o.id))==null?void 0:s.findBuffer(a.id);if(N(n))continue;let c=i.get(o.id,n);N(c)&&(c=new Ja(o.vec3),i.set(o.id,n,c)),c.changes.push(a)}return i}_addAndRemoveGeometries(e,r){const{_bufferWriter:i,_dataByOrigin:s}=this,a=i.vertexBufferLayout.stride/4,o=this._computeDeltas(e,r);o.forEach((n,c)=>{const l=n.get(null),d=A(l)?l.changes:[];o.delete(c,null);let p=s.get(c);if(n.forEach((u,f)=>{if(o.delete(c,f),N(f))return void Te(!1,"No VAO for removed geometries");if(f.instances.size===u.changes.length)return this._vaoCache.deleteVao(f.vao),ea(p.buffers,f),void(p.buffers.length===0&&d.length===0&&s.delete(c));const g=f.numElements,_=f.vao.size/4,w=d.reduce((E,C)=>E+i.elementCount(C.geometry),0),O=u.changes.reduce((E,C)=>E+i.elementCount(C.geometry),0),S=Math.min((g+w-O)*a,di),y=S>_;S>Mi&&S<_/2?(u.changes.forEach(({id:E})=>f.deleteInstance(E)),f.instances.forEach(({geometry:E})=>d.push(E)),this._vaoCache.deleteVao(f.vao),ea(p.buffers,f)):y?this._applyAndRebuild(f,d,u):this._applyRemoves(f,u)}),d.length>0)for(N(p)&&(p=new iu(l.origin),s.set(c,p)),p.buffers.forEach(u=>this._applyAdds(u,d));d.length>0;)p.buffers.push(this._applyAndRebuild(new eu,d,null))})}_updateDrawCommands(){this._hasHighlights=!1,this._hasOccludees=!1,this._dataByOrigin.forEach(e=>{e.buffers.forEach(r=>{r.drawCommandsDirty&&(r.hasHiddenInstances=!1,r.hasHighlights=!1,r.hasOccludees=!1,ot(r.instances,i=>(r.updateDrawState(i),r.hasHiddenInstances&&r.hasHighlights&&r.hasOccludees)),r.updateDrawCommands(this._bufferWriter.vertexBufferLayout.stride)),this._hasHighlights=this._hasHighlights||r.hasHighlights,this._hasOccludees=this._hasOccludees||r.hasOccludees})})}_applyAndRebuild(e,r,i){if(A(i))for(const g of i.changes)e.deleteInstance(g.id);const s=this._bufferWriter,a=s.vertexBufferLayout.stride,o=a/4,n=Math.floor(di/o);let c=e.numElements;for(;r.length>0;){const g=r.pop(),_=s.elementCount(g.geometry);if(c+_>n&&c>0){r.push(g);break}c+=_;const w=new Za(g,0,0);Te(e.instances.get(g.id)==null),e.addInstance(g.id,w)}const l=c*o,d=hi(l),p=s.vertexBufferLayout.createView(d.buffer);let u=0;e.hasHiddenInstances=!1,e.hasHighlights=!1,e.hasOccludees=!1,e.instances.forEach((g,_)=>{this._writeGeometry(g.geometry,p,u);const w=u;u+=s.elementCount(g.geometry.geometry),e.updateInstance(_,w,u),e.updateDrawState(g)}),this._vaoCache.deleteVao(e.vao),e.vao=this._vaoCache.newVao(Ka(l)),e.vao.vertexBuffers.geometry.setSubData(d,0,0,u*o),e.holes.clear();const f=e.holes.pushNew();return f.from=u,f.to=Math.floor(e.vao.size/a),e.updateDrawCommands(a),e}_applyRemoves(e,r){if(r.changes.length===0)return;for(const n of r.changes){const c=n.id,l=e.instances.get(c);if(!l)continue;e.deleteInstance(c);const d=Ye.back();if(d){if(d.to===l.from){d.to=l.to;continue}if(d.from===l.to){d.from=l.from;continue}}const p=Ye.pushNew();p.from=l.from,p.to=l.to}Xa(Ye);const i=this._bufferWriter.vertexBufferLayout.stride/4,s=Ye.reduce((n,c)=>Math.max(n,c.numElements),0)*i,a=hi(s);a.fill(0,0,s);const o=e.vao.vertexBuffers.geometry;Ye.forAll(n=>o.setSubData(a,n.from*i,0,n.numElements*i)),e.holes.pushArray(Ye.data,Ye.length),Ye.forAll((n,c)=>Ye.data[c]=null),Ye.clear(),e.drawCommandsDirty=!0}_applyAdds(e,r){if(r.length===0)return;if(!tu(e))return void this._applyAndRebuild(e,r,null);const i=this._bufferWriter,s=i.vertexBufferLayout.stride/4,a=e.numElements,o=r.reduce((O,S)=>O+i.elementCount(S.geometry),0),n=Math.min((a+o)*s,di),c=4*n;if(e.vao.size<Ka(di-Mi)&&c>e.vao.size)return void this._applyAndRebuild(e,r,null);Xa(e.holes);const l=new Array;for(const O of r){const S=i.elementCount(O.geometry),y=lu(e.holes,S);l.push(y)}const d=e.vao.vertexBuffers.geometry;let p=0,u=0,f=0;const g=hi(n),_=i.vertexBufferLayout.createView(g.buffer);r.forEach((O,S)=>{const y=l[S];if(N(y))return;if(f!==y){const b=f-u;b>0&&d.setSubData(g,u*s,0,b*s),u=y,p=0}const E=i.elementCount(O.geometry);this._writeGeometry(O,_,p),p+=E,f=y+E;const C=new Za(O,y,y+E);Te(e.instances.get(O.id)==null),e.addInstance(O.id,C),e.drawCommandsDirty=!0});const w=f-u;w>0&&d.setSubData(g,u*s,0,w*s),Gn(r,(O,S)=>N(l[S]))}_writeGeometry(e,r,i){const s=e.localOrigin.vec3;Lc(Qa,-s[0],-s[1],-s[2]);const a=jt(cu,Qa,e.transformation);dr(li,a),go(li,li),this._bufferWriter.write(a,li,e.geometry,r,i)}updateAnimation(e){return this.material.update(e)}requiresSlot(e,r){return this.material.requiresSlot(e,r)}render(e,r){var l;if(!this.requiresSlot(r.slot,e))return!1;const i=e===T.Highlight||e===T.ShadowHighlight;if(i&&!this._hasHighlights)return!1;const s=e===T.ShadowExcludeHighlight,a=!(i||s),o=this._rctx;let n;const c=()=>{if(A(n))return n;const d=this._glMaterials.load(o,r.slot,e);return N(d)?null:(n=d.beginSlot(r),N(n)?null:(o.bindTechnique(n,this.material.parameters,r),n))};(l=this._appleAmdDriverHelper)==null||l.resetIndicesType();for(const d of this._dataByOrigin.values())for(const p of d.buffers){if(i&&!p.hasHighlights)continue;const u=(i?p.drawCommandsHighlight:s&&p.needsMultipleCommands()?p.drawCommandsShadowHighlightRest:p.drawCommandsDefault)||null,f=a&&p.drawCommandsOccludees||null;if(u!=null&&u.length||f!=null&&f.length){const g=c();if(N(g))return!1;g.program.bindDraw(new Nh(d.origin),r,this.material.parameters),g.ensureAttributeLocations(p.vao),o.bindVAO(p.vao),u!=null&&u.length&&(g.bindPipelineState(o,r.slot,!1),u.forAll(_=>o.drawArrays(g.primitiveType,_.first,_.count))),f!=null&&f.length&&(g.bindPipelineState(o,r.slot,!0),f.forAll(_=>o.drawArrays(g.primitiveType,_.first,_.count)))}}return A(n)}get test(){return{material:this.material,glMaterials:this._glMaterials,dataByOrigin:this._dataByOrigin}}},Ja=class{constructor(e){this.origin=e,this.changes=new Array}};function lu(t,e){let r;if(!t.some(s=>!(s.numElements<e)&&(r=s,!0)))return null;const i=r.from;return r.from+=e,r.from>=r.to&&t.removeUnordered(r),i}const Qa=Y(),cu=Y(),li=Y(),Ye=new it({allocator:t=>t||new Ks,deallocator:null}),Mi=65536,ci=4*Mi,wn=16777216,di=wn/4;let cs=new Float32Array(Mi);function hi(t){return cs.length<t&&(cs=new Float32Array(t)),cs}function Ka(t){const e=4*t;return e<ci?ci:Math.max(Math.min(Math.ceil(1.5*e/ci)*ci,wn),e)}let ze=class extends Hs{constructor(e){super(e),this._pending=new du,this._changes=new Ah,this._materialRenderers=new Map,this._sortedMaterialRenderers=new it,this._geometries=new Map,this._hasHighlights=!1,this._hasWater=!1}destroy(){this._changes.prune(),this._materialRenderers.forEach(e=>e.dispose()),this._materialRenderers.clear(),this._sortedMaterialRenderers.clear(),this._geometries.clear()}get updating(){return!this._pending.empty||this._changes.updates.length>0}get rctx(){return this.rendererContext.rctx}get _materialRepository(){return this.rendererContext.materialRepository}get _localOriginFactory(){return this.rendererContext.localOriginFactory}get hasHighlights(){return this._hasHighlights}get hasWater(){return this._hasWater}get rendersOccluded(){return ot(this._materialRenderers,e=>e.rendersOccluded)}get isEmpty(){return!this.updating&&this._materialRenderers.size===0&&this._geometries.size===0}commitChanges(){if(!this.updating)return!1;this._processAddsRemoves();const e=Ih(this._changes);let r=!1,i=!1,s=!1;return e.forEach((a,o)=>{let n=this._materialRenderers.get(o);if(!n&&a.adds.length>0&&(n=new nu(this.rctx,this._materialRepository,o),this._materialRenderers.set(o,n),r=!0,i=!0,s=!0),!n)return;const c=i||n.hasHighlights,l=s||n.hasWater;n.modify(a),i=i||c!==n.hasHighlights,s=s||l!==n.hasWater,n.isEmpty&&(this._materialRenderers.delete(o),n.dispose(),r=!0)}),this._changes.clear(),r&&this._updateSortedMaterialRenderers(),i&&(this._hasHighlights=ot(this._materialRenderers,a=>a.hasHighlights)),s&&(this._hasWater=ot(this._materialRenderers,a=>a.hasWater)),this.notifyChange("updating"),!0}addGeometries(e,r){if(e.length===0)return;const i=this._validateRenderGeometries(e);for(const a of i)this._geometries.set(a.id,a);const s=this._pending.empty;for(const a of i)this._pending.adds.add(a);s&&this.notifyChange("updating"),r===Ei.UPDATE&&this._notifyGraphicGeometryChanged(e)}removeGeometries(e,r){const i=this._pending.empty,s=this._pending.adds;for(const a of e)s.has(a)?(this._pending.removed.add(a),s.delete(a)):this._pending.removed.has(a)||this._pending.removes.add(a),this._geometries.delete(Wn(a.id));i&&!this._pending.empty&&this.notifyChange("updating"),r===Ei.UPDATE&&this._notifyGraphicGeometryChanged(e)}modifyGeometries(e,r){const i=this._changes.updates.length===0;for(const s of e){const a=this._changes.updates.pushNew();a.renderGeometry=this._validateRenderGeometry(s),a.updateType=r}switch(i&&this._changes.updates.length>0&&this.notifyChange("updating"),r){case Ke.TRANSFORMATION:case Ke.GEOMETRY:return this._notifyGraphicGeometryChanged(e);case Ke.VISIBILITY:return this._notifyGraphicVisibilityChanged(e)}}updateAnimation(e){let r=!1;return this._sortedMaterialRenderers.forAll(i=>r=i.updateAnimation(e)||r),r}render(e){this._sortedMaterialRenderers.forAll(r=>{r.material.shouldRender(e)&&r.render(e.output,e.bindParameters)})}intersect(e,r,i,s,a){return this._geometries.forEach(o=>{if(s&&!s(o))return;this._intersectRenderGeometry(o,i,r,0,e,a);const n=this.rendererContext.longitudeCyclical;n&&(o.boundingSphere[0]-o.boundingSphere[3]<n.min&&this._intersectRenderGeometry(o,i,r,n.range,e,a),o.boundingSphere[0]+o.boundingSphere[3]>n.max&&this._intersectRenderGeometry(o,i,r,-n.range,e,a)),a++}),a}_updateSortedMaterialRenderers(){this._sortedMaterialRenderers.clear();let e=0;this._materialRenderers.forEach((r,i)=>{i.insertOrder=e++,this._sortedMaterialRenderers.push(r)}),this._sortedMaterialRenderers.sort((r,i)=>{const s=i.material.renderPriority-r.material.renderPriority;return s!==0?s:r.material.insertOrder-i.material.insertOrder})}_processAddsRemoves(){this._changes.adds.clear(),this._changes.removes.clear(),this._changes.adds.pushArray(Array.from(this._pending.adds)),this._changes.removes.pushArray(Array.from(this._pending.removes));for(let e=0;e<this._changes.updates.length;){const r=this._changes.updates.data[e];this._pending.has(r.renderGeometry)?this._changes.updates.removeUnorderedIndex(e):e++}this._pending.clear()}_intersectRenderGeometry(e,r,i,s,a,o){if(!e.visible)return;let n=0;s+=e.transformation[12],n=e.transformation[13],ds[0]=i[0]-s,ds[1]=i[1]-n,e.screenToWorldRatio=this.rendererContext.screenToWorldRatio,e.material.intersectDraped(e,null,a,ds,(c,l,d)=>{hu(r,d,e.material.renderPriority,o,a,e.layerUid,e.graphicUid)},r)}_notifyGraphicGeometryChanged(e){if(N(this.drapeSource.notifyGraphicGeometryChanged))return;let r;for(const i of e){const s=i.graphicUid;A(s)&&s!==r&&(this.drapeSource.notifyGraphicGeometryChanged(s),r=s)}}_notifyGraphicVisibilityChanged(e){if(N(this.drapeSource.notifyGraphicVisibilityChanged))return;let r;for(const i of e){const s=i.graphicUid;A(s)&&s!==r&&(this.drapeSource.notifyGraphicVisibilityChanged(s),r=s)}}_validateRenderGeometries(e){for(const r of e)this._validateRenderGeometry(r);return e}_validateRenderGeometry(e){return N(e.localOrigin)&&(e.localOrigin=this._localOriginFactory.getOrigin(e.boundingSphere)),e}get test(){return{sortedMaterialRenderers:this._sortedMaterialRenderers}}};v([L()],ze.prototype,"drapeSource",void 0),v([L()],ze.prototype,"updating",null),v([L()],ze.prototype,"rctx",null),v([L()],ze.prototype,"rendererContext",void 0),v([L()],ze.prototype,"_materialRepository",null),v([L()],ze.prototype,"_localOriginFactory",null),v([L({readOnly:!0})],ze.prototype,"isEmpty",null),v([L()],ze.prototype,"_materialRenderers",void 0),v([L()],ze.prototype,"_geometries",void 0),ze=v([Gi("esri.views.3d.webgl-engine.lib.SortedRenderGeometryRenderer")],ze);let du=class{constructor(){this.adds=new Set,this.removes=new Set,this.removed=new Set}get empty(){return this.adds.size===0&&this.removes.size===0&&this.removed.size===0}has(e){return this.adds.has(e)||this.removes.has(e)||this.removed.has(e)}clear(){this.adds.clear(),this.removes.clear(),this.removed.clear()}};function hu(t,e,r,i,s,a,o){const n=new bh(a,o,e),c=l=>{l.set(Ct.OVERLAY,n,t.dist,t.normal,t.transformation,r,i)};if((s.results.min.drapedLayerOrder==null||r>=s.results.min.drapedLayerOrder)&&(s.results.min.dist==null||s.results.ground.dist<=s.results.min.dist)&&c(s.results.min),s.options.store!==Wt.MIN&&(s.results.max.drapedLayerOrder==null||r<s.results.max.drapedLayerOrder)&&(s.results.max.dist==null||s.results.ground.dist>s.results.max.dist)&&c(s.results.max),s.options.store===Wt.ALL){const l=$h(s.ray);c(l),s.results.all.push(l)}}const ds=K();let Sn=class xn extends Ar{initializeProgram(e){return new Rr(e.rctx,xn.shader.get().build(),Qr)}initializePipeline(){return this.configuration.hasAlpha?et({blending:jo(tt.SRC_ALPHA,tt.ONE,tt.ONE_MINUS_SRC_ALPHA,tt.ONE_MINUS_SRC_ALPHA),colorWrite:lt}):et({colorWrite:lt})}};Sn.shader=new Pr(lc,()=>Tr(()=>Promise.resolve().then(()=>Up),void 0));let Tn=class extends yo{constructor(){super(...arguments),this.hasAlpha=!1}};v([x()],Tn.prototype,"hasAlpha",void 0);let Je=class extends Hs{get _bindParameters(){return this._renderContext.bindParameters}get rctx(){return this._rctx}get materialRepository(){return this._materialRepository}get screenToWorldRatio(){return this._screenToWorldRatio}get localOriginFactory(){return this._localOriginFactory}constructor(e){super(e),this._overlays=null,this._overlayRenderTarget=null,this._hasHighlights=!1,this._rendersOccluded=!1,this._hasWater=!1,this._handles=new Mo,this._renderers=new Map,this._sortedDrapeSourceRenderersDirty=!1,this._sortedRenderers=new it,this._passParameters=new Ro,this._rctx=null,this._materialRepository=null,this._screenToWorldRatio=1,this._localOriginFactory=null,this._camera=new U,this.worldToPCSRatio=1,this.events=new Go,this.longitudeCyclical=null}initialize(){const e=this.view._stage.renderView;this._rctx=e.renderingContext;const r=e.waterTextureRepository;this._stippleTextureRepository=new Pd(e.renderingContext),this._shaderTechniqueRepository=new vd({rctx:this._rctx,viewingMode:Ot.Local,stippleTextureRepository:this._stippleTextureRepository,waterTextureRepository:r}),this._renderContext=new ph(this._rctx,new fh(this._rctx,this.view.state.viewingMode),new cc(this.view,this._shaderTechniqueRepository,this._rctx,()=>{})),this._handles.add([Ji(()=>r.updating,()=>this.events.emit("content-changed"),Oa),Ji(()=>this.spatialReference,i=>this._localOriginFactory=new oh(i),Oa),td(()=>this.view.allLayerViews,"after-changes",()=>this._sortedDrapeSourceRenderersDirty=!0)]),this._materialRepository=new Od(e.textureRepository,this._shaderTechniqueRepository,i=>{(i.renderOccluded&eo)>0!==this._rendersOccluded&&this._updateRendersOccluded(),this.events.emit("content-changed"),this.notifyChange("updating"),this.notifyChange("isEmpty")},()=>this.events.emit("content-changed")),this._bindParameters.slot=W.DRAPED_MATERIAL,this._bindParameters.highlightDepthTexture=dc(this._rctx),this._camera.near=1,this._camera.far=1e4,this._camera.relativeElevation=null,this._bindParameters.camera=this._camera,this._bindParameters.transparencyPassType=re.NONE,this._bindParameters.newLighting.noonFactor=0,this._bindParameters.newLighting.globalFactor=0,this._bindParameters.newLighting.set([new hc(Hr(1,1,1))]),this._handles.add(this.view.resourceController.scheduler.registerTask(dl.STAGE,this))}destroy(){this._handles.destroy(),this._renderers.forEach(e=>e.destroy()),this._renderers.clear(),this._debugTextureTechnique=Bn(this._debugTextureTechnique),this._passParameters.texture=We(this._passParameters.texture),this._bindParameters.highlightDepthTexture=We(this._bindParameters.highlightDepthTexture),this._shaderTechniqueRepository=fo(this._shaderTechniqueRepository),this._temporaryFBO=We(this._temporaryFBO),this._quadVAO=We(this._quadVAO),this.disposeOverlays()}get updating(){return this._sortedDrapeSourceRenderersDirty||ot(this._renderers,e=>e.updating)}get hasOverlays(){return A(this._overlays)&&A(this._overlayRenderTarget)}get gpuMemoryUsage(){return A(this._overlayRenderTarget)?this._overlayRenderTarget.gpuMemoryUsage:0}createGeometryDrapeSourceRenderer(e){return this.createDrapeSourceRenderer(e,ze)}createDrapeSourceRenderer(e,r,i){const s=this._renderers.get(e);A(s)&&s.destroy();const a=new r({...i,rendererContext:this,drapeSource:e});return this._renderers.set(e,a),this._sortedDrapeSourceRenderersDirty=!0,"fullOpacity"in e&&this._handles.add(Ji(()=>e.fullOpacity,()=>this.events.emit("content-changed")),e),a}removeDrapeSourceRenderer(e){if(N(e))return;const r=this._renderers.get(e);N(r)||(this._sortedDrapeSourceRenderersDirty=!0,this._renderers.delete(e),this._handles.remove(e),r.destroy())}collectUnusedRenderTargetMemory(e){let r=!1;if(A(this._overlayRenderTarget))for(const i of this._overlayRenderTarget.renderTargets){const[s,a]=this.overlays,o=s.validTargets[i.type]||!a.validTargets[i.type];r=this._overlayRenderTarget.validateUsageForTarget(o,i,e)||r}return r}get overlays(){return ae(this._overlays,[])}ensureDrapeTargets(e){A(this._overlays)&&this._overlays.forEach(r=>r.hasTargetWithoutRasterImage=Yi(e,i=>i.drapeTargetType===Cs.WithoutRasterImage))}ensureDrapeSources(e){A(this._overlays)&&this._overlays.forEach(r=>{r.hasDrapedFeatureSource=Yi(e,i=>i.drapeSourceType===Ur.Features),r.hasDrapedRasterSource=Yi(e,i=>i.drapeSourceType===Ur.RasterImage)})}ensureOverlays(e,r){N(this._overlays)&&(this._overlayRenderTarget=new md(this._rctx),this._overlays=[new Aa(mi.INNER,this._overlayRenderTarget),new Aa(mi.OUTER,this._overlayRenderTarget)]),this.ensureDrapeTargets(e),this.ensureDrapeSources(r)}disposeOverlays(){this._overlays=null,this._overlayRenderTarget=We(this._overlayRenderTarget),this.events.emit("textures-disposed")}get running(){return this.updating}runTask(e){this._processDrapeSources(e,()=>!0)}_processDrapeSources(e,r){let i=!1;for(const[s,a]of this._renderers){if(e.done)break;(s.destroyed||r(s))&&a.commitChanges()&&(i=!0,e.madeProgress())}this._sortedDrapeSourceRenderersDirty&&(this._sortedDrapeSourceRenderersDirty=!1,i=!0,this._updateSortedDrapeSourceRenderers()),i&&(A(this._overlays)&&this._renderers.size===0&&this.disposeOverlays(),this.notifyChange("updating"),this.notifyChange("isEmpty"),this.events.emit("content-changed"),this._updateHasHighlights(),this._updateRendersOccluded(),this._updateHasWater())}processSyncDrapeSources(){this._processDrapeSources(hl,e=>e.updatePolicy===Br.SYNC)}get isEmpty(){return!xs.OVERLAY_DRAW_DEBUG_TEXTURE&&!ot(this._renderers,e=>!e.isEmpty)}get hasHighlights(){return this._hasHighlights}get hasWater(){return this._hasWater}get rendersOccluded(){return this._rendersOccluded}updateAnimation(e){let r=!1;return this._renderers.forEach(i=>r=i.updateAnimation(e)||r),r}updateDrapeSourceOrder(){this._sortedDrapeSourceRenderersDirty=!0}drawTarget(e,r,i){const s=e.canvasGeometries;if(s.numViews===0)return!1;this._screenToWorldRatio=i*e.mapUnitsPerPixel;const a=r.output;if(this.isEmpty||a===T.Highlight&&!this.hasHighlights||a===T.Normal&&!this.hasWater||!e.hasSomeSizedView())return!1;const o=r.fbo;if(!o.isValid())return!1;const n=2*e.resolution,c=e.resolution;o.resize(n,c);const l=this._rctx;if(this._camera.pixelRatio=e.pixelRatio*i,this._renderContext.output=a,this._bindParameters.screenToWorldRatio=this._screenToWorldRatio,this._bindParameters.screenToPCSRatio=this._screenToWorldRatio*this.worldToPCSRatio,this._bindParameters.slot=a===T.Normal?W.DRAPED_WATER:W.DRAPED_MATERIAL,e.applyViewport(this._rctx),o.bind(l),e.index===mi.INNER&&(l.setClearColor(0,0,0,0),l.clearSafe(Ri.COLOR_BUFFER_BIT)),r.type===se.Occluded&&(this._renderContext.renderOccludedMask=eo),xs.OVERLAY_DRAW_DEBUG_TEXTURE&&r.type!==se.Occluded)for(let d=0;d<s.numViews;d++)this._setViewParameters(s.extents[d],e),this._drawDebugTexture(e.resolution,pu[e.index]);return this._renderers.size>0&&this._sortedRenderers.forAll(({drapeSource:d,renderer:p})=>{if(r.type===se.ColorNoRasterImage&&d.drapeSourceType===Ur.RasterImage)return;const{fullOpacity:u}=d,f=A(u)&&u<1&&a===T.Color;f&&(this.bindTemporaryFramebuffer(this._rctx,n,c),l.clearSafe(Ri.COLOR_BUFFER_BIT));for(let g=0;g<s.numViews;g++)this._setViewParameters(s.extents[g],e),p.render(this._renderContext);f&&A(this._temporaryFBO)&&(o.bind(l),this.view._stage.renderView.compositingHelper.compositeOverlay(this._renderContext.bindParameters,this._temporaryFBO.getTexture(),u,e.index))}),l.bindFramebuffer(null),o.generateMipMap(),this._renderContext.resetRenderOccludedMask(),!0}bindTemporaryFramebuffer(e,r,i){N(this._temporaryFBO)&&(this._temporaryFBO=new Yo(e,!1)),this._temporaryFBO.resize(r,i),this._temporaryFBO.bind(e)}async reloadShaders(){await this._shaderTechniqueRepository.reloadAll()}notifyContentChanged(){this.events.emit("content-changed")}intersect(e,r,i,s){var o;let a=0;for(const n of this._renderers.values())a=((o=n.intersect)==null?void 0:o.call(n,e,r,i,s,a))??a}_updateSortedDrapeSourceRenderers(){if(this._sortedRenderers.clear(),this._renderers.size===0)return;const e=this.view.map.allLayers;this._renderers.forEach((r,i)=>{const s=e.indexOf(i.layer),a=s>=0,o=this._renderers.size*(i.renderGroup??(a?Pi.MapLayer:Pi.ViewLayer))+(a?s:0);this._sortedRenderers.push(new uu(i,r,o))}),this._sortedRenderers.sort((r,i)=>r.index-i.index)}_setViewParameters(e,r){const i=this._camera;i.viewport=[0,0,r.resolution,r.resolution],ul(i.projectionMatrix,0,e[2]-e[0],0,e[3]-e[1],i.near,i.far),pl(i.viewMatrix,[-e[0],-e[1],0])}_updateHasWater(){const e=ot(this._renderers,r=>r.hasWater);e!==this._hasWater&&(this._hasWater=e,this.events.emit("has-water",e))}_updateHasHighlights(){const e=ot(this._renderers,r=>r.hasHighlights);e!==this._hasHighlights&&(this._hasHighlights=e,this.events.emit("has-highlights",e))}_updateRendersOccluded(){const e=ot(this._renderers,r=>r.rendersOccluded);e!==this._rendersOccluded&&(this._rendersOccluded=e,this.events.emit("renders-occluded",e))}_drawDebugTexture(e,r){this._ensureDebugPatternResources(e,e,r);const i=this._rctx;i.bindTechnique(this._debugTextureTechnique,this._passParameters,null),i.bindVAO(this._quadVAO),i.drawArrays(Ht.TRIANGLE_STRIP,0,id(this._quadVAO,"geometry"))}_ensureDebugPatternResources(e,r,i){if(H(this._passParameters.color,i[0],i[1],i[2]),this._passParameters.texture)return;const s=new Uint8Array(e*r*4);let a=0;for(let n=0;n<r;n++)for(let c=0;c<e;c++){const l=Math.floor(c/10),d=Math.floor(n/10);l<2||d<2||10*l>e-20||10*d>r-20?(s[a++]=255,s[a++]=255,s[a++]=255,s[a++]=255):(s[a++]=255,s[a++]=255,s[a++]=255,s[a++]=1&l&&1&d?1&c^1&n?0:255:1&l^1&d?0:128)}this._passParameters.texture=new or(this._rctx,{target:Vr.TEXTURE_2D,pixelFormat:wr.RGBA,dataType:Wr.UNSIGNED_BYTE,samplingMode:Ai.NEAREST,width:e,height:r},s);const o=new Tn;o.hasAlpha=!0,this._debugTextureTechnique=this._shaderTechniqueRepository.acquire(Sn,o),this._quadVAO=uc(this._rctx)}get test(){return{drapedRenderers:Array.from(this._renderers.values()),getDrapeSourceRenderer:e=>this._renderers.get(e)}}};v([L()],Je.prototype,"_sortedDrapeSourceRenderersDirty",void 0),v([L({autoDestroy:!0})],Je.prototype,"_shaderTechniqueRepository",void 0),v([L({autoDestroy:!0})],Je.prototype,"_stippleTextureRepository",void 0),v([L({constructOnly:!0})],Je.prototype,"view",void 0),v([L()],Je.prototype,"worldToPCSRatio",void 0),v([L()],Je.prototype,"spatialReference",void 0),v([L({type:Boolean,readOnly:!0})],Je.prototype,"updating",null),v([L()],Je.prototype,"isEmpty",null),Je=v([Gi("esri.views.3d.terrain.OverlayRenderer")],Je);let uu=class{constructor(e,r,i){this.drapeSource=e,this.renderer=r,this.index=i}};const pu=[[1,.5,.5],[.5,.5,1]],fu=-2,eo=xt.OccludeAndTransparent;var Rs;(function(t){function e(o,n){const c=o[n],l=o[n+1],d=o[n+2];return Math.sqrt(c*c+l*l+d*d)}function r(o,n){const c=o[n],l=o[n+1],d=o[n+2],p=1/Math.sqrt(c*c+l*l+d*d);o[n]*=p,o[n+1]*=p,o[n+2]*=p}function i(o,n,c){o[n]*=c,o[n+1]*=c,o[n+2]*=c}function s(o,n,c,l,d,p=n){(d=d||o)[p]=o[n]+c[l],d[p+1]=o[n+1]+c[l+1],d[p+2]=o[n+2]+c[l+2]}function a(o,n,c,l,d,p=n){(d=d||o)[p]=o[n]-c[l],d[p+1]=o[n+1]-c[l+1],d[p+2]=o[n+2]-c[l+2]}t.length=e,t.normalize=r,t.scale=i,t.add=s,t.subtract=a})(Rs||(Rs={}));function Ue(t,e=!1){return t<=mo?e?new Array(t).fill(0):new Array(t):new Float32Array(t)}function to(t){return length<=mo?Array.from(t):new Float32Array(t)}function zm(t,e,r){return Array.isArray(t)?t.slice(e,e+r):t.subarray(e,e+r)}const Jt=Rs,hs=[[-.5,-.5,.5],[.5,-.5,.5],[.5,.5,.5],[-.5,.5,.5],[-.5,-.5,-.5],[.5,-.5,-.5],[.5,.5,-.5],[-.5,.5,-.5]],mu=[0,0,1,-1,0,0,1,0,0,0,-1,0,0,1,0,0,0,-1],gu=[0,0,1,0,1,1,0,1],vu=[0,1,2,2,3,0,4,0,3,3,7,4,1,5,6,6,2,1,1,0,4,4,5,1,3,2,6,6,7,3,5,4,7,7,6,5],On=new Array(36);for(let t=0;t<6;t++)for(let e=0;e<6;e++)On[6*t+e]=t;const Ft=new Array(36);for(let t=0;t<6;t++)Ft[6*t+0]=0,Ft[6*t+1]=1,Ft[6*t+2]=2,Ft[6*t+3]=2,Ft[6*t+4]=3,Ft[6*t+5]=0;function jm(t,e){Array.isArray(e)||(e=[e,e,e]);const r=new Array(24);for(let i=0;i<8;i++)r[3*i]=hs[i][0]*e[0],r[3*i+1]=hs[i][1]*e[1],r[3*i+2]=hs[i][2]*e[2];return new Ne(t,[[h.POSITION,new j(r,3,!0)],[h.NORMAL,new j(mu,3)],[h.UV0,new j(gu,2)]],[[h.POSITION,vu],[h.NORMAL,On],[h.UV0,Ft]])}const us=[[-.5,0,-.5],[.5,0,-.5],[.5,0,.5],[-.5,0,.5],[0,-.5,0],[0,.5,0]],_u=[0,1,-1,1,1,0,0,1,1,-1,1,0,0,-1,-1,1,-1,0,0,-1,1,-1,-1,0],yu=[5,1,0,5,2,1,5,3,2,5,0,3,4,0,1,4,1,2,4,2,3,4,3,0],wu=[0,0,0,1,1,1,2,2,2,3,3,3,4,4,4,5,5,5,6,6,6,7,7,7];function Vm(t,e){Array.isArray(e)||(e=[e,e,e]);const r=new Array(18);for(let i=0;i<6;i++)r[3*i]=us[i][0]*e[0],r[3*i+1]=us[i][1]*e[1],r[3*i+2]=us[i][2]*e[2];return new Ne(t,[[h.POSITION,new j(r,3,!0)],[h.NORMAL,new j(_u,3)]],[[h.POSITION,yu],[h.NORMAL,wu]])}const vi=le(-.5,0,-.5),_i=le(.5,0,-.5),yi=le(0,0,.5),wi=le(0,.5,0),Qt=ee(),Kt=ee(),fr=ee(),mr=ee(),gr=ee();ne(Qt,vi,wi),ne(Kt,vi,_i),rt(fr,Qt,Kt),de(fr,fr),ne(Qt,_i,wi),ne(Kt,_i,yi),rt(mr,Qt,Kt),de(mr,mr),ne(Qt,yi,wi),ne(Kt,yi,vi),rt(gr,Qt,Kt),de(gr,gr);const ps=[vi,_i,yi,wi],Su=[0,-1,0,fr[0],fr[1],fr[2],mr[0],mr[1],mr[2],gr[0],gr[1],gr[2]],xu=[0,1,2,3,1,0,3,2,1,3,0,2],Tu=[0,0,0,1,1,1,2,2,2,3,3,3];function Um(t,e){Array.isArray(e)||(e=[e,e,e]);const r=new Array(12);for(let i=0;i<4;i++)r[3*i]=ps[i][0]*e[0],r[3*i+1]=ps[i][1]*e[1],r[3*i+2]=ps[i][2]*e[2];return new Ne(t,[[h.POSITION,new j(r,3,!0)],[h.NORMAL,new j(Su,3)]],[[h.POSITION,xu],[h.NORMAL,Tu]])}function Hm(t,e,r,i,s={uv:!0}){const a=-Math.PI,o=2*Math.PI,n=-Math.PI/2,c=Math.PI,l=Math.max(3,Math.floor(r)),d=Math.max(2,Math.floor(i)),p=(l+1)*(d+1),u=Ue(3*p),f=Ue(3*p),g=Ue(2*p),_=[];let w=0;for(let E=0;E<=d;E++){const C=[],b=E/d,F=n+b*c,R=Math.cos(F);for(let V=0;V<=l;V++){const pe=V/l,D=a+pe*o,X=Math.cos(D)*R,me=Math.sin(F),qe=-Math.sin(D)*R;u[3*w]=X*e,u[3*w+1]=me*e,u[3*w+2]=qe*e,f[3*w]=X,f[3*w+1]=me,f[3*w+2]=qe,g[2*w]=pe,g[2*w+1]=b,C.push(w),++w}_.push(C)}const O=new Array;for(let E=0;E<d;E++)for(let C=0;C<l;C++){const b=_[E][C],F=_[E][C+1],R=_[E+1][C+1],V=_[E+1][C];E===0?(O.push(b),O.push(R),O.push(V)):E===d-1?(O.push(b),O.push(F),O.push(R)):(O.push(b),O.push(F),O.push(R),O.push(R),O.push(V),O.push(b))}const S=[[h.POSITION,O],[h.NORMAL,O]],y=[[h.POSITION,new j(u,3,!0)],[h.NORMAL,new j(f,3,!0)]];return s.uv&&(y.push([h.UV0,new j(g,2,!0)]),S.push([h.UV0,O])),s.offset&&(S[0][0]=h.OFFSET,y[0][0]=h.OFFSET,S.push([h.POSITION,new Array(O.length).fill(0)]),y.push([h.POSITION,new j(Float64Array.from(s.offset),3,!0)])),new Ne(t,y,S)}function Gm(t,e,r,i){const{vertexAttributes:s,indices:a}=Ou(e,r);return new Ne(t,s,a)}function Ou(t,e,r){let i,s;i=[0,-1,0,1,0,0,0,0,1,-1,0,0,0,0,-1,0,1,0],s=[0,1,2,0,2,3,0,3,4,0,4,1,1,5,2,2,5,3,3,5,4,4,5,1];for(let l=0;l<i.length;l+=3)Jt.scale(i,l,t/Jt.length(i,l));let a={};function o(l,d){l>d&&([l,d]=[d,l]);const p=l.toString()+"."+d.toString();if(a[p])return a[p];let u=i.length;return i.length+=3,Jt.add(i,3*l,i,3*d,i,u),Jt.scale(i,u,t/Jt.length(i,u)),u/=3,a[p]=u,u}for(let l=0;l<e;l++){const d=s.length,p=new Array(4*d);for(let u=0;u<d;u+=3){const f=s[u],g=s[u+1],_=s[u+2],w=o(f,g),O=o(g,_),S=o(_,f),y=4*u;p[y]=f,p[y+1]=w,p[y+2]=S,p[y+3]=g,p[y+4]=O,p[y+5]=w,p[y+6]=_,p[y+7]=S,p[y+8]=O,p[y+9]=w,p[y+10]=O,p[y+11]=S}s=p,a={}}const n=to(i);for(let l=0;l<n.length;l+=3)Jt.normalize(n,l);const c=[[h.POSITION,s],[h.NORMAL,s]];return{vertexAttributes:[[h.POSITION,new j(to(i),3,!0)],[h.NORMAL,new j(n,3,!0)]],indices:c}}function Wm(t,e,r,i,s,a,o,n,c=null){const l=r?[r[0],r[1],r[2]]:[0,0,0],d=e?[e[0],e[1],e[2]]:[0,0,1];o=o||[0,0];const p=i?[255*i[0],255*i[1],255*i[2],i.length>3?255*i[3]:255]:[255,255,255,255],u=A(s)&&s.length===2?s:[1,1],f=[[h.POSITION,new j(l,3,!0)],[h.NORMAL,new j(d,3,!0)],[h.UV0,new j(o,o.length)],[h.COLOR,new j(p,4,!0)],[h.SIZE,new j(u,2)]];if(a!=null){const g=[a[0],a[1],a[2],a[3]];f.push([h.AUXPOS1,new j(g,4)])}if(n!=null){const g=[n[0],n[1],n[2],n[3]];f.push([h.AUXPOS2,new j(g,4)])}return new Ne(t,f,null,null,Cr.Point,c)}function Bm(t,e,r,i,s,a=!0,o=!0){let n=0;const c=r,l=e;let d=le(0,n,0),p=le(0,n+l,0),u=le(0,-1,0),f=le(0,1,0);s&&(n=l,p=le(0,0,0),d=le(0,n,0),u=le(0,1,0),f=le(0,-1,0));const g=[p,d],_=[u,f],w=i+2,O=Math.sqrt(l*l+c*c);if(s)for(let R=i-1;R>=0;R--){const V=R*(2*Math.PI/i),pe=le(Math.cos(V)*c,n,Math.sin(V)*c);g.push(pe);const D=le(l*Math.cos(V)/O,-c/O,l*Math.sin(V)/O);_.push(D)}else for(let R=0;R<i;R++){const V=R*(2*Math.PI/i),pe=le(Math.cos(V)*c,n,Math.sin(V)*c);g.push(pe);const D=le(l*Math.cos(V)/O,c/O,l*Math.sin(V)/O);_.push(D)}const S=new Array,y=new Array;if(a){for(let R=3;R<g.length;R++)S.push(1),S.push(R-1),S.push(R),y.push(0),y.push(0),y.push(0);S.push(g.length-1),S.push(2),S.push(1),y.push(0),y.push(0),y.push(0)}if(o){for(let R=3;R<g.length;R++)S.push(R),S.push(R-1),S.push(0),y.push(R),y.push(R-1),y.push(1);S.push(0),S.push(2),S.push(g.length-1),y.push(1),y.push(2),y.push(_.length-1)}const E=Ue(3*w);for(let R=0;R<w;R++)E[3*R]=g[R][0],E[3*R+1]=g[R][1],E[3*R+2]=g[R][2];const C=Ue(3*w);for(let R=0;R<w;R++)C[3*R]=_[R][0],C[3*R+1]=_[R][1],C[3*R+2]=_[R][2];const b=[[h.POSITION,S],[h.NORMAL,y]],F=[[h.POSITION,new j(E,3,!0)],[h.NORMAL,new j(C,3,!0)]];return new Ne(t,F,b)}function km(t,e,r,i,s,a,o){const n=s?da(s):le(1,0,0),c=a?da(a):le(0,0,0);o=o??!0;const l=ee();de(l,n);const d=ee();Z(d,l,Math.abs(e));const p=ee();Z(p,d,-.5),te(p,p,c);const u=le(0,1,0);Math.abs(1-ct(l,u))<.2&&H(u,0,0,1);const f=ee();rt(f,l,u),de(f,f),rt(u,f,l);const g=2*i+(o?2:0),_=i+(o?2:0),w=Ue(3*g),O=Ue(3*_),S=Ue(2*g),y=new Array(3*i*(o?4:2)),E=new Array(3*i*(o?4:2));o&&(w[3*(g-2)+0]=p[0],w[3*(g-2)+1]=p[1],w[3*(g-2)+2]=p[2],S[2*(g-2)]=0,S[2*(g-2)+1]=0,w[3*(g-1)+0]=w[3*(g-2)+0]+d[0],w[3*(g-1)+1]=w[3*(g-2)+1]+d[1],w[3*(g-1)+2]=w[3*(g-2)+2]+d[2],S[2*(g-1)]=1,S[2*(g-1)+1]=1,O[3*(_-2)+0]=-l[0],O[3*(_-2)+1]=-l[1],O[3*(_-2)+2]=-l[2],O[3*(_-1)+0]=l[0],O[3*(_-1)+1]=l[1],O[3*(_-1)+2]=l[2]);const C=(D,X,me)=>{y[D]=X,E[D]=me};let b=0;const F=ee(),R=ee();for(let D=0;D<i;D++){const X=D*(2*Math.PI/i);Z(F,u,Math.sin(X)),Z(R,f,Math.cos(X)),te(F,F,R),O[3*D+0]=F[0],O[3*D+1]=F[1],O[3*D+2]=F[2],Z(F,F,r),te(F,F,p),w[3*D+0]=F[0],w[3*D+1]=F[1],w[3*D+2]=F[2],S[2*D+0]=D/i,S[2*D+1]=0,w[3*(D+i)+0]=w[3*D+0]+d[0],w[3*(D+i)+1]=w[3*D+1]+d[1],w[3*(D+i)+2]=w[3*D+2]+d[2],S[2*(D+i)+0]=D/i,S[2*D+1]=1;const me=(D+1)%i;C(b++,D,D),C(b++,D+i,D),C(b++,me,me),C(b++,me,me),C(b++,D+i,D),C(b++,me+i,me)}if(o){for(let D=0;D<i;D++){const X=(D+1)%i;C(b++,g-2,_-2),C(b++,D,_-2),C(b++,X,_-2)}for(let D=0;D<i;D++){const X=(D+1)%i;C(b++,D+i,_-1),C(b++,g-1,_-1),C(b++,X+i,_-1)}}const V=[[h.POSITION,y],[h.NORMAL,E],[h.UV0,y]],pe=[[h.POSITION,new j(w,3,!0)],[h.NORMAL,new j(O,3,!0)],[h.UV0,new j(S,2,!0)]];return new Ne(t,pe,V)}function qm(t,e,r,i,s,a){i=i||10,s=s==null||s,Te(e.length>1);const o=[[0,0,0]],n=[],c=[];for(let l=0;l<i;l++){n.push([0,-l-1,-(l+1)%i-1]);const d=l/i*2*Math.PI;c.push([Math.cos(d)*r,Math.sin(d)*r])}return Cu(t,c,e,o,n,s,a)}function Cu(t,e,r,i,s,a,o=le(0,0,0)){const n=e.length,c=Ue(r.length*n*3+(6*i.length||0)),l=Ue(r.length*n*3+(i?6:0)),d=new Array,p=new Array;let u=0,f=0;const g=ee(),_=ee(),w=ee(),O=ee(),S=ee(),y=ee(),E=ee(),C=$(),b=ee(),F=ee(),R=ee(),V=ee(),pe=ee(),D=dt();H(b,0,1,0),ne(_,r[1],r[0]),de(_,_),a?(te(C,r[0],o),de(w,C)):H(w,0,0,1),ro(_,w,b,b,S,w,io),k(O,w),k(V,S);for(let I=0;I<i.length;I++)Z(y,S,i[I][0]),Z(C,w,i[I][2]),te(y,y,C),te(y,y,r[0]),c[u++]=y[0],c[u++]=y[1],c[u++]=y[2];l[f++]=-_[0],l[f++]=-_[1],l[f++]=-_[2];for(let I=0;I<s.length;I++)d.push(s[I][0]>0?s[I][0]:-s[I][0]-1+i.length),d.push(s[I][1]>0?s[I][1]:-s[I][1]-1+i.length),d.push(s[I][2]>0?s[I][2]:-s[I][2]-1+i.length),p.push(0),p.push(0),p.push(0);let X=i.length;const me=i.length-1;for(let I=0;I<r.length;I++){let At=!1;I>0&&(k(g,_),I<r.length-1?(ne(_,r[I+1],r[I]),de(_,_)):At=!0,te(F,g,_),de(F,F),te(R,r[I-1],O),Ll(r[I],F,D),Nl(D,Ul(R,g),C)?(ne(C,C,r[I]),de(w,C),rt(S,F,w),de(S,S)):ro(F,O,V,b,S,w,io),k(O,w),k(V,S)),a&&(te(C,r[I],o),de(pe,C));for(let Xe=0;Xe<n;Xe++)if(Z(y,S,e[Xe][0]),Z(C,w,e[Xe][1]),te(y,y,C),de(E,y),l[f++]=E[0],l[f++]=E[1],l[f++]=E[2],te(y,y,r[I]),c[u++]=y[0],c[u++]=y[1],c[u++]=y[2],!At){const Er=(Xe+1)%n;d.push(X+Xe),d.push(X+n+Xe),d.push(X+Er),d.push(X+Er),d.push(X+n+Xe),d.push(X+n+Er);for(let ht=0;ht<6;ht++){const Hn=d.length-6;p.push(d[Hn+ht]-me)}}X+=n}const qe=r[r.length-1];for(let I=0;I<i.length;I++)Z(y,S,i[I][0]),Z(C,w,i[I][1]),te(y,y,C),te(y,y,qe),c[u++]=y[0],c[u++]=y[1],c[u++]=y[2];const Bt=f/3;l[f++]=_[0],l[f++]=_[1],l[f++]=_[2];const J=X-n;for(let I=0;I<s.length;I++)d.push(s[I][0]>=0?X+s[I][0]:-s[I][0]-1+J),d.push(s[I][2]>=0?X+s[I][2]:-s[I][2]-1+J),d.push(s[I][1]>=0?X+s[I][1]:-s[I][1]-1+J),p.push(Bt),p.push(Bt),p.push(Bt);const Fe=[[h.POSITION,d],[h.NORMAL,p]],He=[[h.POSITION,new j(c,3,!0)],[h.NORMAL,new j(l,3,!0)]];return new Ne(t,He,Fe)}function Xm(t,e,r,i){Te(e.length>1,"createPolylineGeometry(): polyline needs at least 2 points"),Te(e[0].length===3,"createPolylineGeometry(): malformed vertex"),Te(r==null||r.length===e.length,"createPolylineGeometry: need same number of points and normals"),Te(r==null||r[0].length===3,"createPolylineGeometry(): malformed normal");const s=Us(3*e.length),a=new Array(2*(e.length-1));let o=0,n=0;for(let d=0;d<e.length;d++){for(let p=0;p<3;p++)s[o++]=e[d][p];d>0&&(a[n++]=d-1,a[n++]=d)}const c=[],l=[];if(c.push([h.POSITION,a]),l.push([h.POSITION,new j(s,3,!0)]),r){const d=Ue(3*r.length);let p=0;for(let u=0;u<e.length;u++)for(let f=0;f<3;f++)d[p++]=r[u][f];c.push([h.NORMAL,a]),l.push([h.NORMAL,new j(d,3,!0)])}return i&&(l.push([h.COLOR,new j(i,4)]),c.push([h.COLOR,Ic(i.length/4)])),new Ne(t,l,c,null,Cr.Line)}function Zm(t,e,r,i,s,a=0){const o=new Array(18),n=[[-r,a,s/2],[i,a,s/2],[0,e+a,s/2],[-r,a,-s/2],[i,a,-s/2],[0,e+a,-s/2]],c=[0,1,2,3,0,2,2,5,3,1,4,5,5,2,1,1,0,3,3,4,1,4,3,5];for(let l=0;l<6;l++)o[3*l]=n[l][0],o[3*l+1]=n[l][1],o[3*l+2]=n[l][2];return new Ne(t,[[h.POSITION,new j(o,3,!0)]],[[h.POSITION,c]])}function Ym(t,e){const r=t.getMutableAttribute(h.POSITION).data;for(let i=0;i<r.length;i+=3){const s=r[i],a=r[i+1],o=r[i+2];H(er,s,a,o),ie(er,er,e),r[i]=er[0],r[i+1]=er[1],r[i+2]=er[2]}}function Jm(t,e=t){const r=t.vertexAttributes,i=r.get(h.POSITION).data,s=r.get(h.NORMAL).data;if(s){const a=e.getMutableAttribute(h.NORMAL).data;for(let o=0;o<s.length;o+=3){const n=s[o+1];a[o+1]=-s[o+2],a[o+2]=n}}if(i){const a=e.getMutableAttribute(h.POSITION).data;for(let o=0;o<i.length;o+=3){const n=i[o+1];a[o+1]=-i[o+2],a[o+2]=n}}}function fs(t,e,r,i,s){return!(Math.abs(ct(e,t))>s)&&(rt(r,t,e),de(r,r),rt(i,r,t),de(i,i),!0)}function ro(t,e,r,i,s,a,o){return fs(t,e,s,a,o)||fs(t,r,s,a,o)||fs(t,i,s,a,o)}const io=.99619469809,er=ee();let Qm=class{constructor(e,r={}){this.geometry=e,this.boundingSphere=ke(),this.screenToWorldRatio=1,this._transformation=Y(),this._shaderTransformationDirty=!0,this.id=Gs(),this.layerUid=r.layerUid,this.graphicUid=r.graphicUid,this.boundingInfo=r.boundingInfo,this.shaderTransformer=r.shaderTransformer,this.castShadow=!!r.castShadow&&r.castShadow}get transformation(){return this._transformation}updateTransformation(e){e(this._transformation),this._shaderTransformationDirty=!0,this.computeBoundingSphere(this._transformation,this.boundingSphere)}shaderTransformationChanged(){this._shaderTransformationDirty=!0}computeBoundingSphere(e,r,i=Ts(e)){N(this.boundingInfo)||(ie(r,this.boundingInfo.center,e),r[3]=this.boundingInfo.radius*i)}get hasShaderTransformation(){return A(this.shaderTransformer)}get material(){return this.geometry.material}get type(){return this.geometry.type}get shaderTransformation(){return N(this.shaderTransformer)?this.transformation:(this._shaderTransformationDirty&&(this._shaderTransformation||(this._shaderTransformation=Y()),wt(this._shaderTransformation,this.shaderTransformer(this.transformation)),this._shaderTransformationDirty=!1),this._shaderTransformation)}get indices(){return this.geometry.indices}get vertexAttributes(){return this.geometry.vertexAttributes}get highlights(){return this.geometry.highlights}get occludees(){return this.geometry.occludees}get visible(){return this.geometry.visible}set visible(e){this.geometry.visible=e}};function Cn(t){return t.type==="point"}function eg(t,e){if(t.type==="point")return pt(t,e,!1);if(Uc(t))switch(t.type){case"extent":return pt(t.center,e,!1);case"polygon":return pt(t.centroid,e,!1);case"polyline":return pt(so(t),e,!0);case"mesh":return pt(t.origin,e,!1)}else switch(t.type){case"extent":return pt(bu(t),e,!0);case"polygon":return pt(Au(t),e,!0);case"polyline":return pt(so(t),e,!0)}}function so(t){const e=t.paths[0];if(!e||e.length===0)return null;const r=fl(e,ml(e)/2);return qs(r[0],r[1],r[2],t.spatialReference)}function bu(t){return qs(.5*(t.xmax+t.xmin),.5*(t.ymax+t.ymin),t.zmin!=null&&t.zmax!=null&&isFinite(t.zmin)&&isFinite(t.zmax)?.5*(t.zmax+t.zmin):void 0,t.spatialReference)}function Au(t){const e=t.rings[0];if(!e||e.length===0)return null;const r=gl(t.rings,!!t.hasZ);return qs(r[0],r[1],r[2],t.spatialReference)}function pt(t,e,r){const i=r?t:Hc(t);return e&&t?vl(t,i,e)?i:null:i}function tg(t,e,r,i=0){if(t){e||(e=cr());const s=t;let a=.5*s.width*(r-1),o=.5*s.height*(r-1);return s.width<1e-7*s.height?a+=o/20:s.height<1e-7*s.width&&(o+=a/20),Gr(e,s.xmin-a-i,s.ymin-o-i,s.xmax+a+i,s.ymax+o+i),e}return null}function Ru(t,e){for(let r=0;r<t.geometries.length;++r){const i=t.geometries[r].getMutableAttribute(h.AUXPOS1);i&&i.data[3]!==e&&(i.data[3]=e,t.geometryVertexAttrsUpdated(t.geometries[r]))}}function rg(t,e){const r=_l(Fi);return A(t)&&(r[0]=t[0],r[1]=t[1],r[2]=t[2]),A(e)?r[3]=e:A(t)&&t.length>3&&(r[3]=t[3]),r}function ig(t=wl,e,r,i=1){const s=new Array(3);if(N(e)||N(r))s[0]=1,s[1]=1,s[2]=1;else{let a,o=0;for(let n=2;n>=0;n--){const c=t[n];let l;const d=c!=null,p=n===0&&!a&&!d,u=r[n];c==="symbol-value"||p?l=u!==0?e[n]/u:1:d&&c!=="proportional"&&isFinite(c)&&(l=u!==0?c/u:1),l!=null&&(s[n]=l,a=l,o=Math.max(o,Math.abs(l)))}for(let n=2;n>=0;n--)s[n]==null?s[n]=a:s[n]===0&&(s[n]=.001*o)}for(let a=2;a>=0;a--)s[a]/=i;return yl(s)}function Pu(t){return t.isPrimitive!=null}function sg(t){return bn(Pu(t)?[t.width,t.depth,t.height]:t)?null:"Symbol sizes may not be negative values"}function bn(t){if(Array.isArray(t)){for(const e of t)if(!bn(e))return!1;return!0}return t==null||t>=0}function ag(t,e,r,i=Y()){const s=t||0,a=e||0,o=r||0;return s!==0&&Sl(i,i,-s/180*Math.PI),a!==0&&xl(i,i,a/180*Math.PI),o!==0&&Tl(i,i,o/180*Math.PI),i}function og(t,e,r){if(r.minDemResolution!=null)return r.minDemResolution;const i=zc(e),s=Ol(t)*i,a=Cl(t)*i,o=bl(t)*(e.isGeographic?1:i);return s===0&&a===0&&o===0?r.minDemResolutionForPoints:.01*Math.max(s,a,o)}let ng=class{constructor(e,r=null,i=0){this.array=e,this.spatialReference=r,this.offset=i}};function An(t){return"array"in t}function ui(t,e,r="ground"){if(Cn(e))return t.getElevation(e.x,e.y,e.z||0,e.spatialReference,r);if(An(e)){let i=e.offset;return t.getElevation(e.array[i++],e.array[i++],e.array[i]||0,ae(e.spatialReference,t.spatialReference),r)}return t.getElevation(e[0],e[1],e[2]||0,t.spatialReference,r)}function Eu(t,e,r,i,s,a,o,n,c,l,d){const p=Fu[d.mode];let u,f,g=0;if(Si(t,e,r,i,c.spatialReference,s,n))return p.requiresAlignment(d)?(g=p.applyElevationAlignmentBuffer(i,s,a,o,n,c,l,d),u=a,f=o):(u=i,f=s),Si(u,c.spatialReference,f,a,l.spatialReference,o,n)?g:void 0}function Rn(t,e,r,i,s){const a=(Cn(t)?t.z:An(t)?t.array[t.offset+2]:t[2])||0;switch(r.mode){case"on-the-ground":{const o=ae(ui(e,t,"ground"),0);return s.verticalDistanceToGround=0,s.sampledElevation=o,void(s.z=o)}case"relative-to-ground":{const o=ae(ui(e,t,"ground"),0),n=r.geometryZWithOffset(a,i);return s.verticalDistanceToGround=n,s.sampledElevation=o,void(s.z=n+o)}case"relative-to-scene":{const o=ae(ui(e,t,"scene"),0),n=r.geometryZWithOffset(a,i);return s.verticalDistanceToGround=n,s.sampledElevation=o,void(s.z=n+o)}case"absolute-height":{const o=r.geometryZWithOffset(a,i),n=ae(ui(e,t,"ground"),0);return s.verticalDistanceToGround=o-n,s.sampledElevation=n,void(s.z=o)}default:return void(s.z=0)}}function cg(t,e,r,i){return Rn(t,e,r,i,lr),lr.z}function dg(t,e,r){return e==null||r==null?t.definedChanged:e==="on-the-ground"&&r==="on-the-ground"?t.staysOnTheGround:e===r||e!=="on-the-ground"&&r!=="on-the-ground"?Ps.UPDATE:t.onTheGroundChanged}function hg(t){return t==="relative-to-ground"||t==="relative-to-scene"}function ug(t){return t!=="absolute-height"}function pg(t,e,r,i,s){Rn(e,r,s,i,lr),Ru(t,lr.verticalDistanceToGround);const a=lr.sampledElevation,o=wt(zu,t.transformation);return pi[0]=e.x,pi[1]=e.y,pi[2]=lr.z,Al(e.spatialReference,pi,o,i.spatialReference)?t.transformation=o:console.warn("Could not locate symbol object properly, it might be misplaced"),a}function Du(t,e,r,i,s,a){let o=0;const n=a.spatialReference;e*=3,i*=3;for(let c=0;c<s;++c){const l=t[e+0],d=t[e+1],p=t[e+2],u=ae(a.getElevation(l,d,p,n,"ground"),0);o+=u,r[i+0]=l,r[i+1]=d,r[i+2]=u,e+=3,i+=3}return o/s}function $u(t,e,r,i,s,a,o,n){let c=0;const l=n.calculateOffsetRenderUnits(o),d=n.featureExpressionInfoContext,p=a.spatialReference;e*=3,i*=3;for(let u=0;u<s;++u){const f=t[e+0],g=t[e+1],_=t[e+2],w=ae(a.getElevation(f,g,_,p,"ground"),0);c+=w,r[i+0]=f,r[i+1]=g,r[i+2]=d==null?_+w+l:w+l,e+=3,i+=3}return c/s}function Iu(t,e,r,i,s,a,o,n){let c=0;const l=n.calculateOffsetRenderUnits(o),d=n.featureExpressionInfoContext,p=a.spatialReference;e*=3,i*=3;for(let u=0;u<s;++u){const f=t[e+0],g=t[e+1],_=t[e+2],w=ae(a.getElevation(f,g,_,p,"scene"),0);c+=w,r[i+0]=f,r[i+1]=g,r[i+2]=d==null?_+w+l:w+l,e+=3,i+=3}return c/s}function Mu(t){const e=t.meterUnitOffset,r=t.featureExpressionInfoContext;return e!==0||r!=null}function Lu(t,e,r,i,s,a,o,n){const c=n.calculateOffsetRenderUnits(o),l=n.featureExpressionInfoContext;e*=3,i*=3;for(let d=0;d<s;++d){const p=t[e+0],u=t[e+1],f=t[e+2];r[i+0]=p,r[i+1]=u,r[i+2]=l==null?f+c:c,e+=3,i+=3}return 0}let Nu=class{constructor(){this.verticalDistanceToGround=0,this.sampledElevation=0,this.z=0}};var Ps;(function(t){t[t.NONE=0]="NONE",t[t.UPDATE=1]="UPDATE",t[t.RECREATE=2]="RECREATE"})(Ps||(Ps={}));const Fu={"absolute-height":{applyElevationAlignmentBuffer:Lu,requiresAlignment:Mu},"on-the-ground":{applyElevationAlignmentBuffer:Du,requiresAlignment:()=>!0},"relative-to-ground":{applyElevationAlignmentBuffer:$u,requiresAlignment:()=>!0},"relative-to-scene":{applyElevationAlignmentBuffer:Iu,requiresAlignment:()=>!0}},zu=Y(),lr=new Nu,pi=$(),ju=Kr.getLogger("esri.views.3d.layers.graphics.featureExpressionInfoUtils");function Vu(t){return{cachedResult:t.cachedResult,arcade:t.arcade?{func:t.arcade.func,context:t.arcade.modules.arcadeUtils.createExecContext(null,{sr:t.arcade.context.spatialReference}),modules:t.arcade.modules}:null}}async function mg(t,e,r,i){const s=t&&t.expression;if(typeof s!="string")return null;const a=Wu(s);if(a!=null)return{cachedResult:a};const o=await Rl();jc(r);const n=o.arcadeUtils,c=n.createSyntaxTree(s);return n.dependsOnView(c)?(i!=null&&i.error("Expressions containing '$view' are not supported on ElevationInfo"),{cachedResult:0}):{arcade:{func:n.createFunction(c),context:n.createExecContext(null,{sr:e}),modules:o}}}function Uu(t,e,r){return t.arcadeUtils.createFeature(e.attributes,e.geometry,r)}function Hu(t,e){if(t!=null&&!Pn(t)){if(!e||!t.arcade)return void ju.errorOncePerTick("Arcade support required but not provided");const r=e;r._geometry&&(r._geometry=Gc(r._geometry)),t.arcade.modules.arcadeUtils.updateExecContext(t.arcade.context,e)}}function Gu(t){if(t!=null){if(Pn(t))return t.cachedResult;const e=t.arcade;let r=e==null?void 0:e.modules.arcadeUtils.executeFunction(e.func,e.context);return typeof r!="number"&&(t.cachedResult=0,r=0),r}return 0}function gg(t,e=!1){let r=t&&t.featureExpressionInfo;const i=r&&r.expression;return e||i==="0"||(r=null),r??null}const vg={cachedResult:0};function Pn(t){return t.cachedResult!=null}function Wu(t){return t==="0"?0:null}let _g=class En{constructor(){this._meterUnitOffset=0,this._renderUnitOffset=0,this._unit="meters",this._metersPerElevationInfoUnit=1,this._featureExpressionInfoContext=null,this.centerPointInElevationSR=null,this.mode=null}get featureExpressionInfoContext(){return this._featureExpressionInfoContext}get meterUnitOffset(){return this._meterUnitOffset}get unit(){return this._unit}set unit(e){this._unit=e,this._metersPerElevationInfoUnit=Pl(e)}get requiresSampledElevationInfo(){return this.mode!=="absolute-height"}reset(){this.mode=null,this._meterUnitOffset=0,this._renderUnitOffset=0,this._featureExpressionInfoContext=null,this.unit="meters"}set offsetMeters(e){this._meterUnitOffset=e,this._renderUnitOffset=0}set offsetElevationInfoUnits(e){this._meterUnitOffset=e*this._metersPerElevationInfoUnit,this._renderUnitOffset=0}addOffsetRenderUnits(e){this._renderUnitOffset+=e}geometryZWithOffset(e,r){const i=this.calculateOffsetRenderUnits(r);return this.featureExpressionInfoContext!=null?i:e+i}calculateOffsetRenderUnits(e){let r=this._meterUnitOffset;const i=this.featureExpressionInfoContext;return i!=null&&(r+=Gu(i)*this._metersPerElevationInfoUnit),r/e.unitInMeters+this._renderUnitOffset}setFromElevationInfo(e){this.mode=e.mode,this.unit=El(e.unit)?e.unit:"meters",this.offsetElevationInfoUnits=ae(e.offset,0)}updateFeatureExpressionInfoContext(e,r,i){if(N(e))return void(this._featureExpressionInfoContext=null);const s=e&&e.arcade;s&&A(r)&&A(i)?(this._featureExpressionInfoContext=Vu(e),Hu(this._featureExpressionInfoContext,Uu(s.modules,r,i))):this._featureExpressionInfoContext=e}static fromElevationInfo(e){const r=new En;return A(e)&&r.setFromElevationInfo(e),r}};function Bu(t){return t instanceof Float32Array&&t.length>=16}function ku(t){return Array.isArray(t)&&t.length>=16}function qu(t){return Bu(t)||ku(t)}var Li;(function(t){t[t.Occluded=0]="Occluded",t[t.NotOccluded=1]="NotOccluded",t[t.Both=2]="Both",t[t.COUNT=3]="COUNT"})(Li||(Li={}));var Be;function Xu(t,e){t.include(Po),t.attributes.add(h.POSITION,"vec3"),t.attributes.add(h.NORMAL,"vec3"),t.attributes.add(h.AUXPOS1,"vec4");const r=t.vertex;Jr(r,e),Is(r,e),r.uniforms.add([new Ee("viewport",(i,s)=>s.camera.fullViewport),new B("polygonOffset",i=>i.shaderPolygonOffset),new B("cameraGroundRelative",(i,s)=>s.camera.aboveGround?1:-1),new B("renderTransparentlyOccludedHUD",(i,s)=>s.renderTransparentlyOccludedHUD===Li.Occluded?1:s.renderTransparentlyOccludedHUD===Li.NotOccluded?0:.75),new Ut("hudVisibilityTexture",(i,s)=>s.hudVisibilityTexture)]),e.hasVerticalOffset&&pc(r),r.constants.add("smallOffsetAngle","float",.984807753012208),r.code.add(m`struct ProjectHUDAux {
vec3 posModel;
vec3 posView;
vec3 vnormal;
float distanceToCamera;
float absCosAngle;
};`),r.code.add(m`float applyHUDViewDependentPolygonOffset(float pointGroundDistance, float absCosAngle, inout vec3 posView) {
float pointGroundSign = sign(pointGroundDistance);
if (pointGroundSign == 0.0) {
pointGroundSign = cameraGroundRelative;
}
float groundRelative = cameraGroundRelative * pointGroundSign;
if (polygonOffset > .0) {
float cosAlpha = clamp(absCosAngle, 0.01, 1.0);
float tanAlpha = sqrt(1.0 - cosAlpha * cosAlpha) / cosAlpha;
float factor = (1.0 - tanAlpha / viewport[2]);
if (groundRelative > 0.0) {
posView *= factor;
}
else {
posView /= factor;
}
}
return groundRelative;
}`),e.isDraped&&!e.hasVerticalOffset||fc(r),e.isDraped||(r.uniforms.add(new B("perDistancePixelRatio",(i,s)=>Math.tan(s.camera.fovY/2)/(s.camera.fullViewport[2]/2))),r.code.add(m`void applyHUDVerticalGroundOffset(vec3 normalModel, inout vec3 posModel, inout vec3 posView) {
float distanceToCamera = length(posView);
float pixelOffset = distanceToCamera * perDistancePixelRatio * 0.5;
vec3 modelOffset = normalModel * cameraGroundRelative * pixelOffset;
vec3 viewOffset = (viewNormal * vec4(modelOffset, 1.0)).xyz;
posModel += modelOffset;
posView += viewOffset;
}`)),e.screenCenterOffsetUnitsEnabled===Be.Screen&&r.uniforms.add(new B("pixelRatio",(i,s)=>s.camera.pixelRatio)),e.hasScreenSizePerspective&&Eo(r),r.code.add(m`
    vec4 projectPositionHUD(out ProjectHUDAux aux) {
      // centerOffset is in view space and is used to implement world size offsetting
      // of labels with respect to objects. It also pulls the label towards the viewer
      // so that the label is visible in front of the object.
      vec3 centerOffset = auxpos1.xyz;

      // The pointGroundDistance is the distance of the geometry to the ground and is
      // negative if the point is below the ground, or positive if the point is above
      // ground.
      float pointGroundDistance = auxpos1.w;

      aux.posModel = position;
      aux.posView = (view * vec4(aux.posModel, 1.0)).xyz;
      aux.vnormal = normal;
      ${e.isDraped?"":"applyHUDVerticalGroundOffset(aux.vnormal, aux.posModel, aux.posView);"}

      // Screen sized offset in world space, used for example for line callouts
      // Note: keep this implementation in sync with the CPU implementation, see
      //   - MaterialUtil.verticalOffsetAtDistance
      //   - HUDMaterial.applyVerticalOffsetTransformation

      aux.distanceToCamera = length(aux.posView);

      vec3 viewDirObjSpace = normalize(cameraPosition - aux.posModel);
      float cosAngle = dot(aux.vnormal, viewDirObjSpace);

      aux.absCosAngle = abs(cosAngle);

      ${e.hasScreenSizePerspective&&(e.hasVerticalOffset||e.screenCenterOffsetUnitsEnabled===Be.Screen)?"vec4 perspectiveFactor = screenSizePerspectiveScaleFactor(aux.absCosAngle, aux.distanceToCamera, screenSizePerspectiveAlignment);":""}

      ${e.hasVerticalOffset?e.hasScreenSizePerspective?"float verticalOffsetScreenHeight = applyScreenSizePerspectiveScaleFactorFloat(verticalOffset.x, perspectiveFactor);":"float verticalOffsetScreenHeight = verticalOffset.x;":""}

      ${e.hasVerticalOffset?m`
            float worldOffset = clamp(verticalOffsetScreenHeight * verticalOffset.y * aux.distanceToCamera, verticalOffset.z, verticalOffset.w);
            vec3 modelOffset = aux.vnormal * worldOffset;
            aux.posModel += modelOffset;
            vec3 viewOffset = (viewNormal * vec4(modelOffset, 1.0)).xyz;
            aux.posView += viewOffset;
            // Since we elevate the object, we need to take that into account
            // in the distance to ground
            pointGroundDistance += worldOffset;`:""}

      float groundRelative = applyHUDViewDependentPolygonOffset(pointGroundDistance, aux.absCosAngle, aux.posView);

      ${e.screenCenterOffsetUnitsEnabled!==Be.Screen?m`
            // Apply x/y in view space, but z in screen space (i.e. along posView direction)
            aux.posView += vec3(centerOffset.x, centerOffset.y, 0.0);

            // Same material all have same z != 0.0 condition so should not lead to
            // branch fragmentation and will save a normalization if it's not needed
            if (centerOffset.z != 0.0) {
              aux.posView -= normalize(aux.posView) * centerOffset.z;
            }
          `:""}

      vec4 posProj = proj * vec4(aux.posView, 1.0);

      ${e.screenCenterOffsetUnitsEnabled===Be.Screen?e.hasScreenSizePerspective?"float centerOffsetY = applyScreenSizePerspectiveScaleFactorFloat(centerOffset.y, perspectiveFactor);":"float centerOffsetY = centerOffset.y;":""}

      ${e.screenCenterOffsetUnitsEnabled===Be.Screen?"posProj.xy += vec2(centerOffset.x, centerOffsetY) * pixelRatio * 2.0 / viewport.zw * posProj.w;":""}

      // constant part of polygon offset emulation
      posProj.z -= groundRelative * polygonOffset * posProj.w;
      return posProj;
    }
  `),r.code.add(m`bool testVisibilityHUD(vec4 posProj) {
vec4 posProjCenter = alignToPixelCenter(posProj, viewport.zw);
vec4 occlusionPixel = texture2D(hudVisibilityTexture, .5 + .5 * posProjCenter.xy / posProjCenter.w);
if (renderTransparentlyOccludedHUD > 0.5) {
return occlusionPixel.r * occlusionPixel.g > 0.0 && occlusionPixel.g * renderTransparentlyOccludedHUD < 1.0;
}
return occlusionPixel.r * occlusionPixel.g > 0.0 && occlusionPixel.g == 1.0;
}`)}(function(t){t[t.World=0]="World",t[t.Screen=1]="Screen",t[t.COUNT=2]="COUNT"})(Be||(Be={}));class Zu{constructor(){this.factor=new ao,this.factorAlignment=new ao}}let ao=class{constructor(){this.scale=0,this.factor=0,this.minPixelSize=0,this.paddingPixels=0}};function Yu(t){const e=m`vec4 alignToPixelCenter(vec4 clipCoord, vec2 widthHeight) {
vec2 xy = vec2(0.500123) + 0.5 * clipCoord.xy / clipCoord.w;
vec2 pixelSz = vec2(1.0) / widthHeight;
vec2 ij = (floor(xy * widthHeight) + vec2(0.5)) * pixelSz;
vec2 result = (ij * 2.0 - vec2(1.0)) * clipCoord.w;
return vec4(result, clipCoord.zw);
}`,r=m`vec4 alignToPixelOrigin(vec4 clipCoord, vec2 widthHeight) {
vec2 xy = vec2(0.5) + 0.5 * clipCoord.xy / clipCoord.w;
vec2 pixelSz = vec2(1.0) / widthHeight;
vec2 ij = floor((xy + 0.5 * pixelSz) * widthHeight) * pixelSz;
vec2 result = (ij * 2.0 - vec2(1.0)) * clipCoord.w;
return vec4(result, clipCoord.zw);
}`;t.vertex.code.add(e),t.vertex.code.add(r),t.fragment.code.add(e),t.fragment.code.add(r)}function Ju(t,e){const{vertex:r,fragment:i}=t;e.hasMultipassGeometry&&r.include(lh),e.hasMultipassTerrain&&t.varyings.add("depth","float"),r.code.add(m`
  void main(void) {
    vec4 posProjCenter;
    if (dot(position, position) > 0.0) {
      // Render single point to center of the pixel to avoid subpixel
      // filtering to affect the marker color
      ProjectHUDAux projectAux;
      vec4 posProj = projectPositionHUD(projectAux);
      posProjCenter = alignToPixelCenter(posProj, viewport.zw);

      ${e.hasMultipassGeometry?m`
        // Don't draw vertices behind geometry
        if(geometryDepthTest(.5 + .5 * posProjCenter.xy / posProjCenter.w, projectAux.posView.z)){
          posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);
        }`:""}

      ${e.hasMultipassTerrain?"depth = projectAux.posView.z;":""}
      vec3 vpos = projectAux.posModel;
      if (rejectBySlice(vpos)) {
        // Project out of clip space
        posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);
      }

    } else {
      // Project out of clip space
      posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);
    }

    gl_Position = posProjCenter;
    gl_PointSize = 1.0;
  }
  `),e.hasMultipassTerrain&&i.include(Fs),e.hasMultipassTerrain&&i.uniforms.add([...So("terrainDepthTexture",(s,a)=>a.multipassTerrain.linearDepthTexture,e.hasWebGL2Context?Ti.None:Ti.InvSize),new St("nearFar",(s,a)=>a.camera.nearFar)]),i.include($s),i.code.add(m`
  void main() {
    gl_FragColor = vec4(1, 1, 1, 1);
    ${e.hasMultipassTerrain?m`
          vec2 uv = gl_FragCoord.xy;

          // Read the rgba data from the texture linear depth
          vec4 terrainDepthData = ${mc(e,"terrainDepthTexture","uv")};

          float terrainDepth = linearDepthFromFloat(rgba2float(terrainDepthData), nearFar);

          // If HUD vertex is behind terrain and the terrain depth is not the initialize value (e.g. we are not looking at the sky)
          // Mark the HUD vertex as occluded by transparent terrain
          if(depth < terrainDepth && terrainDepthData != vec4(0,0,0,1)){
            gl_FragColor.g = 0.5;
          }`:""}
  }
  `)}function Dn(t){const e=new Yr,r=t.signedDistanceFieldEnabled;if(e.include(Yu),e.include(Xu,t),e.include(vt,t),t.occlusionPass)return e.include(Ju,t),e;const{vertex:i,fragment:s}=e;e.include(Po),s.include($s),s.include(ji),e.include(gc,t),e.include(Ms,t),e.varyings.add("vcolor","vec4"),e.varyings.add("vtc","vec2"),e.varyings.add("vsize","vec2"),t.binaryHighlightOcclusionEnabled&&e.varyings.add("voccluded","float"),i.uniforms.add([new Ee("viewport",(l,d)=>d.camera.fullViewport),new St("screenOffset",(l,d)=>Vt($n,2*l.screenOffset[0]*d.camera.pixelRatio,2*l.screenOffset[1]*d.camera.pixelRatio)),new St("anchorPosition",l=>Zr(l)),new Ee("materialColor",l=>l.color),new B("pixelRatio",(l,d)=>d.camera.pixelRatio)]),r&&(i.uniforms.add(new Ee("outlineColor",l=>l.outlineColor)),s.uniforms.add([new Ee("outlineColor",l=>oo(l)?l.outlineColor:Ds),new B("outlineSize",l=>oo(l)?l.outlineSize:0)])),t.hasScreenSizePerspective&&(vc(i),Eo(i)),(t.debugDrawLabelBorder||t.binaryHighlightOcclusionEnabled)&&e.varyings.add("debugBorderCoords","vec4"),e.attributes.add(h.UV0,"vec2"),e.attributes.add(h.COLOR,"vec4"),e.attributes.add(h.SIZE,"vec2"),e.attributes.add(h.AUXPOS2,"vec4"),i.code.add(m`
    void main(void) {
      ProjectHUDAux projectAux;
      vec4 posProj = projectPositionHUD(projectAux);
      forwardObjectAndLayerIdColor();

      if (rejectBySlice(projectAux.posModel)) {
        // Project outside of clip plane
        gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
        return;
      }
      vec2 inputSize;
      ${t.hasScreenSizePerspective?m`
      inputSize = screenSizePerspectiveScaleVec2(size, projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspective);
      vec2 screenOffsetScaled = screenSizePerspectiveScaleVec2(screenOffset, projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspectiveAlignment);
         `:m`
      inputSize = size;
      vec2 screenOffsetScaled = screenOffset;`}

      ${t.vvSize?"inputSize *= vvScale(auxpos2).xx;":""}

      vec2 combinedSize = inputSize * pixelRatio;
      vec4 quadOffset = vec4(0.0);

      ${t.occlusionTestEnabled||t.binaryHighlightOcclusionEnabled?"bool visible = testVisibilityHUD(posProj);":""}

      ${t.binaryHighlightOcclusionEnabled?"voccluded = visible ? 0.0 : 1.0;":""}
    `);const a=m`vec2 uv01 = floor(uv0);
vec2 uv = uv0 - uv01;
quadOffset.xy = ((uv01 - anchorPosition) * 2.0 * combinedSize + screenOffsetScaled) / viewport.zw * posProj.w;`,o=t.pixelSnappingEnabled?r?m`posProj = alignToPixelOrigin(posProj, viewport.zw) + quadOffset;`:m`posProj += quadOffset;
if (inputSize.x == size.x) {
posProj = alignToPixelOrigin(posProj, viewport.zw);
}`:m`posProj += quadOffset;`;t.vvColor&&i.uniforms.add([new wo("vvColorColors",l=>l.vvColorColors,zr),new fi("vvColorValues",l=>l.vvColorValues,zr)]),i.uniforms.add(new St("textureCoordinateScaleFactor",l=>A(l.texture)&&A(l.texture.descriptor.textureCoordinateScaleFactor)?l.texture.descriptor.textureCoordinateScaleFactor:Dl)),i.code.add(m`
    ${t.occlusionTestEnabled?"if (visible) {":""}
    ${a}
    ${t.vvColor?"vcolor = vvGetColor(auxpos2, vvColorValues, vvColorColors) * materialColor;":"vcolor = color / 255.0 * materialColor;"}

    ${t.output===T.ObjectAndLayerIdColor?m`vcolor.a = 1.0;`:""}

    bool alphaDiscard = vcolor.a < ${m.float(Ve)};
    ${r?`alphaDiscard = alphaDiscard && outlineColor.a < ${m.float(Ve)};`:""}
    if (alphaDiscard) {
      // "early discard" if both symbol color (= fill) and outline color (if applicable) are transparent
      gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
      return;
    } else {
      ${o}
      gl_Position = posProj;
    }

    vtc = uv * textureCoordinateScaleFactor;

    ${t.debugDrawLabelBorder?"debugBorderCoords = vec4(uv01, 1.5 / combinedSize);":""}
    vsize = inputSize;
    ${t.occlusionTestEnabled?m`} else { vtc = vec2(0.0);
      ${t.debugDrawLabelBorder?"debugBorderCoords = vec4(0.5, 0.5, 1.5 / combinedSize);}":"}"}`:""}
  }
  `),s.uniforms.add(new Ut("tex",l=>l.texture));const n=t.debugDrawLabelBorder?m`(isBorder > 0.0 ? 0.0 : ${m.float(ga)})`:m.float(ga),c=m`
    ${t.debugDrawLabelBorder?m`
      float isBorder = float(any(lessThan(debugBorderCoords.xy, debugBorderCoords.zw)) || any(greaterThan(debugBorderCoords.xy, 1.0 - debugBorderCoords.zw)));`:""}

    ${r?m`
      vec4 fillPixelColor = vcolor;

      // Attempt to sample texel centers to avoid that thin cross outlines
      // disappear with large symbol sizes.
      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/7058#issuecomment-603041
      const float txSize = ${m.float(Js)};
      const float texelSize = 1.0 / txSize;
      // Calculate how much we have to add/subtract to/from each texel to reach the size of an onscreen pixel
      vec2 scaleFactor = (vsize - txSize) * texelSize;
      vec2 samplePos = vtc + (vec2(1.0, -1.0) * texelSize) * scaleFactor;

      // Get distance and map it into [-0.5, 0.5]
      float d = rgba2float(texture2D(tex, samplePos)) - 0.5;

      // Distance in output units (i.e. pixels)
      float dist = d * vsize.x;

      // Create smooth transition from the icon into its outline
      float fillAlphaFactor = clamp(0.5 - dist, 0.0, 1.0);
      fillPixelColor.a *= fillAlphaFactor;

      if (outlineSize > 0.25) {
        vec4 outlinePixelColor = outlineColor;
        float clampedOutlineSize = min(outlineSize, 0.5*vsize.x);

        // Create smooth transition around outline
        float outlineAlphaFactor = clamp(0.5 - (abs(dist) - 0.5*clampedOutlineSize), 0.0, 1.0);
        outlinePixelColor.a *= outlineAlphaFactor;

        if (
          outlineAlphaFactor + fillAlphaFactor < ${n} ||
          fillPixelColor.a + outlinePixelColor.a < ${m.float(Ve)}
        ) {
          discard;
        }

        // perform un-premultiplied over operator (see https://en.wikipedia.org/wiki/Alpha_compositing#Description)
        float compositeAlpha = outlinePixelColor.a + fillPixelColor.a * (1.0 - outlinePixelColor.a);
        vec3 compositeColor = vec3(outlinePixelColor) * outlinePixelColor.a +
          vec3(fillPixelColor) * fillPixelColor.a * (1.0 - outlinePixelColor.a);

        gl_FragColor = vec4(compositeColor, compositeAlpha);
      } else {
        if (fillAlphaFactor < ${n}) {
          discard;
        }

        gl_FragColor = premultiplyAlpha(fillPixelColor);
      }

      // visualize SDF:
      // gl_FragColor = vec4(clamp(-dist/vsize.x*2.0, 0.0, 1.0), clamp(dist/vsize.x*2.0, 0.0, 1.0), 0.0, 1.0);
      `:m`
          vec4 texColor = texture2D(tex, vtc, -0.5);
          if (texColor.a < ${n}) {
            discard;
          }
          gl_FragColor = texColor * premultiplyAlpha(vcolor);
          `}

    // Draw debug border with transparency, so that original texels along border are still partially visible
    ${t.debugDrawLabelBorder?m`gl_FragColor = mix(gl_FragColor, vec4(1.0, 0.0, 1.0, 1.0), isBorder * 0.5);`:""}
  `;return t.output===T.Alpha&&s.code.add(m`
      void main() {
        ${c}
        gl_FragColor = vec4(gl_FragColor.a);
      }
      `),t.output===T.ObjectAndLayerIdColor&&s.code.add(m`
      void main() {
        ${c}
        outputObjectAndLayerIdColor();
      }
      `),t.output===T.Color&&s.code.add(m`
    void main() {
      ${c}
      ${t.transparencyPassType===re.FrontFace?"gl_FragColor.rgb /= gl_FragColor.a;":""}
    }
    `),t.output===T.Highlight&&(e.include(Hi,t),s.code.add(m`
    void main() {
      ${c}
      ${t.binaryHighlightOcclusionEnabled?m`
          if (voccluded == 1.0) {
            gl_FragColor = vec4(1.0, 1.0, 0.0, 1.0);
          } else {
            gl_FragColor = vec4(1.0, 0.0, 1.0, 1.0);
          }`:"outputHighlight();"}
    }
    `)),e}function oo(t){return t.outlineColor[3]>0&&t.outlineSize>0}function Zr(t,e=$n){return t.textureIsSignedDistanceField?Qu(t.anchorPosition,t.distanceFieldBoundingBox,e):Or(e,t.anchorPosition),e}function Qu(t,e,r){A(e)?Vt(r,t[0]*(e[2]-e[0])+e[0],t[1]*(e[3]-e[1])+e[1]):Vt(r,0,0)}const $n=K(),Ku=Object.freeze(Object.defineProperty({__proto__:null,build:Dn,calculateAnchorPosForRendering:Zr},Symbol.toStringTag,{value:"Module"}));let In=class Mn extends Ar{initializeConfiguration(e,r){r.hasWebGL2Context=e.rctx.type===zi.WEBGL2,r.spherical=e.viewingMode===Ot.Global}initializeProgram(e){return new Rr(e.rctx,Mn.shader.get().build(this.configuration),Qr)}_setPipelineState(e){const r=this.configuration,i=e===re.NONE,s=e===re.FrontFace,a=this.configuration.hasPolygonOffset?ep:null,o=(i||s)&&r.output!==T.Highlight&&(r.depthEnabled||r.occlusionPass)?yr:null;return et({blending:r.output===T.Color||r.output===T.Alpha||r.output===T.Highlight?i?tp:Wi(e):null,depthTest:{func:Kc.LEQUAL},depthWrite:o,colorWrite:lt,polygonOffset:a})}initializePipeline(){return this._setPipelineState(this.configuration.transparencyPassType)}get primitiveType(){return this.configuration.occlusionPass?Ht.POINTS:Ht.TRIANGLES}};In.shader=new Pr(Ku,()=>Tr(()=>Promise.resolve().then(()=>Hp),void 0));const ep={factor:0,units:-4},tp=Zc(tt.ONE,tt.ONE_MINUS_SRC_ALPHA);let Q=class extends br{constructor(){super(...arguments),this.output=T.Color,this.screenCenterOffsetUnitsEnabled=Be.World,this.transparencyPassType=re.NONE,this.spherical=!1,this.occlusionTestEnabled=!0,this.signedDistanceFieldEnabled=!1,this.vvSize=!1,this.vvColor=!1,this.hasVerticalOffset=!1,this.hasScreenSizePerspective=!1,this.debugDrawLabelBorder=!1,this.binaryHighlightOcclusionEnabled=!0,this.hasSlicePlane=!1,this.hasPolygonOffset=!1,this.depthEnabled=!0,this.pixelSnappingEnabled=!0,this.isDraped=!1,this.hasMultipassGeometry=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.occlusionPass=!1,this.objectAndLayerIdColorInstanced=!1}};v([x({count:T.COUNT})],Q.prototype,"output",void 0),v([x({count:Be.COUNT})],Q.prototype,"screenCenterOffsetUnitsEnabled",void 0),v([x({count:re.COUNT})],Q.prototype,"transparencyPassType",void 0),v([x()],Q.prototype,"spherical",void 0),v([x()],Q.prototype,"occlusionTestEnabled",void 0),v([x()],Q.prototype,"signedDistanceFieldEnabled",void 0),v([x()],Q.prototype,"vvSize",void 0),v([x()],Q.prototype,"vvColor",void 0),v([x()],Q.prototype,"hasVerticalOffset",void 0),v([x()],Q.prototype,"hasScreenSizePerspective",void 0),v([x()],Q.prototype,"debugDrawLabelBorder",void 0),v([x()],Q.prototype,"binaryHighlightOcclusionEnabled",void 0),v([x()],Q.prototype,"hasSlicePlane",void 0),v([x()],Q.prototype,"hasPolygonOffset",void 0),v([x()],Q.prototype,"depthEnabled",void 0),v([x()],Q.prototype,"pixelSnappingEnabled",void 0),v([x()],Q.prototype,"isDraped",void 0),v([x()],Q.prototype,"hasMultipassGeometry",void 0),v([x()],Q.prototype,"hasMultipassTerrain",void 0),v([x()],Q.prototype,"cullAboveGround",void 0),v([x()],Q.prototype,"occlusionPass",void 0),v([x()],Q.prototype,"objectAndLayerIdColorInstanced",void 0),v([x({constValue:!0})],Q.prototype,"hasSliceInVertexProgram",void 0),v([x({constValue:!1})],Q.prototype,"hasVvInstancing",void 0);let Sg=class extends Vi{constructor(e){super(e,new hp),this._configuration=new Q}getConfiguration(e,r){return this._configuration.output=e,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasVerticalOffset=!!this.parameters.verticalOffset,this._configuration.hasScreenSizePerspective=!!this.parameters.screenSizePerspective,this._configuration.screenCenterOffsetUnitsEnabled=this.parameters.centerOffsetUnits==="screen"?Be.Screen:Be.World,this._configuration.hasPolygonOffset=this.parameters.polygonOffset,this._configuration.isDraped=this.parameters.isDraped,this._configuration.occlusionTestEnabled=this.parameters.occlusionTest,this._configuration.pixelSnappingEnabled=this.parameters.pixelSnappingEnabled,this._configuration.signedDistanceFieldEnabled=this.parameters.textureIsSignedDistanceField,this._configuration.vvSize=!!this.parameters.vvSizeEnabled,this._configuration.vvColor=!!this.parameters.vvColorEnabled,this._configuration.occlusionPass=r.slot===W.OCCLUSION_PIXELS&&this.parameters.occlusionTest&&(e===T.Color||e===T.Alpha),e===T.Color&&(this._configuration.debugDrawLabelBorder=!!xs.LABELS_SHOW_BORDER),e===T.Highlight&&(this._configuration.binaryHighlightOcclusionEnabled=this.parameters.binaryHighlightOcclusion),this._configuration.depthEnabled=this.parameters.depthEnabled,this._configuration.transparencyPassType=r.transparencyPassType,this._configuration.hasMultipassGeometry=r.multipassGeometry.enabled,this._configuration.hasMultipassTerrain=r.multipassTerrain.enabled,this._configuration.cullAboveGround=r.multipassTerrain.cullAboveGround,this._configuration}intersect(e,r,i,s,a,o){if(!i.options.selectionMode||!i.options.hud||!e.visible)return;const n=this.parameters;let c=1,l=1;if(ca(gs,r),A(e.shaderTransformer)){const S=e.shaderTransformer(co);c=S[0],l=S[5],sp(gs)}const d=e.vertexAttributes.get(h.POSITION),p=e.vertexAttributes.get(h.SIZE),u=e.vertexAttributes.get(h.NORMAL),f=e.vertexAttributes.get(h.AUXPOS1);Te(d.size>=3);const g=i.point,_=i.camera,w=Zr(n);c*=_.pixelRatio,l*=_.pixelRatio;const O=this.parameters.centerOffsetUnits==="screen";for(let S=0;S<d.data.length/d.size;S++){const y=S*d.size;H(ve,d.data[y],d.data[y+1],d.data[y+2]),ie(ve,ve,r);const E=S*p.size;ft[0]=p.data[E]*c,ft[1]=p.data[E+1]*l,ie(ve,ve,_.viewMatrix);const C=S*f.size;if(H(Ie,f.data[C+0],f.data[C+1],f.data[C+2]),!O&&(ve[0]+=Ie[0],ve[1]+=Ie[1],Ie[2]!==0)){const F=Ie[2];de(Ie,ve),ne(ve,ve,Z(Ie,Ie,F))}const b=S*u.size;if(H(Lr,u.data[b],u.data[b+1],u.data[b+2]),this._normalAndViewAngle(Lr,gs,_,vs),this._applyVerticalOffsetTransformationView(ve,vs,_,ms),_.applyProjection(ve,we),we[0]>-1){we[0]=Math.floor(we[0]),we[1]=Math.floor(we[1]),O&&(Ie[0]||Ie[1])&&(we[0]+=Ie[0],Ie[1]!==0&&(we[1]+=_c(Ie[1],ms.factorAlignment)),_.unapplyProjection(we,ve)),we[0]+=this.parameters.screenOffset[0],we[1]+=this.parameters.screenOffset[1],yc(ft,ms.factor,ft);const F=lp*_.pixelRatio;let R=0;if(n.textureIsSignedDistanceField&&(R=n.outlineSize*_.pixelRatio/2),g&&no(g,we[0],we[1],ft,F,R,n,w)){const V=i.ray;if(ie(lo,ve,dr(np,_.viewMatrix)),we[0]=g[0],we[1]=g[1],_.unprojectFromRenderScreen(we,ve)){const pe=$();k(pe,V.direction);const D=1/nt(pe);Z(pe,pe,D),o(Tt(V.origin,ve)*D,pe,-1,!0,1,lo)}}}}}intersectDraped(e,r,i,s,a,o){const n=e.vertexAttributes.get(h.POSITION),c=e.vertexAttributes.get(h.SIZE),l=this.parameters,d=Zr(l);let p=1,u=1;if(A(e.shaderTransformer)){const g=e.shaderTransformer(co);p=g[0],u=g[5]}p*=e.screenToWorldRatio,u*=e.screenToWorldRatio;const f=cp*e.screenToWorldRatio;for(let g=0;g<n.data.length/n.size;g++){const _=g*n.size,w=n.data[_],O=n.data[_+1],S=g*c.size;ft[0]=c.data[S]*p,ft[1]=c.data[S+1]*u;let y=0;l.textureIsSignedDistanceField&&(y=l.outlineSize*e.screenToWorldRatio/2),no(s,w,O,ft,f,y,l,d)&&a(o.dist,o.normal,-1,!1)}}createBufferWriter(){return new pp(this)}_normalAndViewAngle(e,r,i,s){return qu(r)&&(r=ca(op,r)),$l(s.normal,e,r),ie(s.normal,s.normal,i.viewInverseTransposeMatrix),s.cosAngle=ct(Ln,dp),s}_updateScaleInfo(e,r,i){const s=this.parameters;A(s.screenSizePerspective)?va(i,r,s.screenSizePerspective,e.factor):(e.factor.scale=1,e.factor.factor=0,e.factor.minPixelSize=0,e.factor.paddingPixels=0),A(s.screenSizePerspectiveAlignment)?va(i,r,s.screenSizePerspectiveAlignment,e.factorAlignment):(e.factorAlignment.factor=e.factor.factor,e.factorAlignment.scale=e.factor.scale,e.factorAlignment.minPixelSize=e.factor.minPixelSize,e.factorAlignment.paddingPixels=e.factor.paddingPixels)}applyShaderOffsetsView(e,r,i,s,a,o,n){const c=this._normalAndViewAngle(r,i,a,vs);return this._applyVerticalGroundOffsetView(e,c,a,n),this._applyVerticalOffsetTransformationView(n,c,a,o),this._applyPolygonOffsetView(n,c,s[3],a,n),this._applyCenterOffsetView(n,s,n),n}applyShaderOffsetsNDC(e,r,i,s,a){return this._applyCenterOffsetNDC(e,r,i,s),A(a)&&k(a,s),this._applyPolygonOffsetNDC(s,r,i,s),s}_applyPolygonOffsetView(e,r,i,s,a){const o=s.aboveGround?1:-1;let n=Math.sign(i);n===0&&(n=o);const c=o*n;if(this.parameters.shaderPolygonOffset<=0)return k(a,e);const l=zt(Math.abs(r.cosAngle),.01,1),d=1-Math.sqrt(1-l*l)/l/s.viewport[2];return Z(a,e,c>0?d:1/d),a}_applyVerticalGroundOffsetView(e,r,i,s){const a=nt(e),o=i.aboveGround?1:-1,n=.5*i.computeRenderPixelSizeAtDist(a),c=Z(ve,r.normal,o*n);return te(s,e,c),s}_applyVerticalOffsetTransformationView(e,r,i,s){const a=this.parameters;if(!a.verticalOffset||!a.verticalOffset.screenLength){if(a.screenSizePerspective||a.screenSizePerspectiveAlignment){const l=nt(e);this._updateScaleInfo(s,l,r.cosAngle)}else s.factor.scale=1,s.factorAlignment.scale=1;return e}const o=nt(e),n=ae(a.screenSizePerspectiveAlignment,a.screenSizePerspective),c=wc(i,o,a.verticalOffset,r.cosAngle,n);return this._updateScaleInfo(s,o,r.cosAngle),Z(r.normal,r.normal,c),te(e,e,r.normal)}_applyCenterOffsetView(e,r,i){const s=this.parameters.centerOffsetUnits!=="screen";return i!==e&&k(i,e),s&&(i[0]+=r[0],i[1]+=r[1],r[2]&&(de(Lr,i),te(i,i,Z(Lr,Lr,r[2])))),i}_applyCenterOffsetNDC(e,r,i,s){const a=this.parameters.centerOffsetUnits!=="screen";return s!==e&&k(s,e),a||(s[0]+=r[0]/i.fullWidth*2,s[1]+=r[1]/i.fullHeight*2),s}_applyPolygonOffsetNDC(e,r,i,s){const a=this.parameters.shaderPolygonOffset;if(e!==s&&k(s,e),a){const o=i.aboveGround?1:-1,n=o*Math.sign(r[3]);s[2]-=(n||o)*a}return s}requiresSlot(e,r){if(r===T.Color||r===T.Alpha||r===T.Highlight||r===T.ObjectAndLayerIdColor){if(e===W.DRAPED_MATERIAL)return!0;const{drawInSecondSlot:i,occlusionTest:s}=this.parameters;return e===(i?W.LABEL_MATERIAL:W.HUD_MATERIAL)||s&&e===W.OCCLUSION_PIXELS}return!1}createGLMaterial(e){return new rp(e)}calculateRelativeScreenBounds(e,r,i=cr()){return ip(this.parameters,e,r,i),i[2]=i[0]+e[0],i[3]=i[1]+e[1],i}},rp=class extends Sc{constructor(e){super({...e,...e.material.parameters})}selectProgram(e){return this.ensureTechnique(In,e)}beginSlot(e){return this.updateTexture(this._material.parameters.textureId),this._material.setParameters(this.textureBindParameters),this.selectProgram(e)}};function ip(t,e,r,i=ap){return Or(i,t.anchorPosition),i[0]*=-e[0],i[1]*=-e[1],i[0]+=t.screenOffset[0]*r,i[1]+=t.screenOffset[1]*r,i}function sp(t){const e=t[0],r=t[1],i=t[2],s=t[3],a=t[4],o=t[5],n=t[6],c=t[7],l=t[8],d=1/Math.sqrt(e*e+r*r+i*i),p=1/Math.sqrt(s*s+a*a+o*o),u=1/Math.sqrt(n*n+c*c+l*l);return t[0]=e*d,t[1]=r*d,t[2]=i*d,t[3]=s*p,t[4]=a*p,t[5]=o*p,t[6]=n*u,t[7]=c*u,t[8]=l*u,t}function no(t,e,r,i,s,a,o,n){let c=e-s-(n[0]>0?i[0]*n[0]:0),l=c+i[0]+2*s,d=r-s-(n[1]>0?i[1]*n[1]:0),p=d+i[1]+2*s;const u=o.distanceFieldBoundingBox;return o.textureIsSignedDistanceField&&A(u)&&(c+=i[0]*u[0],d+=i[1]*u[1],l-=i[0]*(1-u[2]),p-=i[1]*(1-u[3]),c-=a,l+=a,d-=a,p+=a),t[0]>c&&t[0]<l&&t[1]>d&&t[1]<p}const ms=new Zu,ap=K(),ve=$(),Lr=$(),we=ke(),Ln=$(),lo=$(),gs=Ws(),op=Ws(),np=Y(),Ie=$(),vs={normal:Ln,cosAngle:0},co=Y(),lp=1,cp=2,ft=[0,0],dp=Hr(0,0,1);class hp extends xc{constructor(){super(...arguments),this.renderOccluded=xt.Occlude,this.color=vr(1,1,1,1),this.texCoordScale=[1,1],this.polygonOffset=!1,this.anchorPosition=Es(.5,.5),this.screenOffset=[0,0],this.shaderPolygonOffset=1e-5,this.textureIsSignedDistanceField=!1,this.outlineColor=vr(1,1,1,1),this.outlineSize=0,this.vvSizeEnabled=!1,this.vvSizeMinSize=[1,1,1],this.vvSizeMaxSize=[100,100,100],this.vvSizeOffset=[0,0,0],this.vvSizeFactor=[1,1,1],this.vvColorEnabled=!1,this.vvColorValues=[0,0,0,0,0,0,0,0],this.vvColorColors=[1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0],this.hasSlicePlane=!1,this.pixelSnappingEnabled=!0,this.occlusionTest=!0,this.binaryHighlightOcclusion=!0,this.centerOffsetUnits="world",this.drawInSecondSlot=!1,this.depthEnabled=!0,this.isDraped=!1}}const Nn=bt().vec3f(h.POSITION).vec3f(h.NORMAL).vec2f(h.UV0).vec4u8(h.COLOR).vec2f(h.SIZE).vec4f(h.AUXPOS1).vec4f(h.AUXPOS2),up=Nn.clone().vec4u8(h.OBJECTANDLAYERIDCOLOR);class pp{constructor(e){this._material=e,this.vertexBufferLayout=yt("enable-feature:objectAndLayerId-rendering")?up:Nn}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){return 6*e.indices.get(h.POSITION).length}write(e,r,i,s,a){Tc(i.indices.get(h.POSITION),i.vertexAttributes.get(h.POSITION).data,e,s.position,a,6),Oc(i.indices.get(h.NORMAL),i.vertexAttributes.get(h.NORMAL).data,r,s.normal,a,6);const o=i.vertexAttributes.get(h.UV0).data;let n,c,l,d;if(o==null||o.length<4){const O=this._material.parameters;n=0,c=0,l=O.texCoordScale[0],d=O.texCoordScale[1]}else n=o[0],c=o[1],l=o[2],d=o[3];l=Math.min(1.99999,l+1),d=Math.min(1.99999,d+1);let p=i.indices.get(h.POSITION).length,u=a;const f=s.uv0;for(let O=0;O<p;++O)f.set(u,0,n),f.set(u,1,c),u+=1,f.set(u,0,l),f.set(u,1,c),u+=1,f.set(u,0,l),f.set(u,1,d),u+=1,f.set(u,0,l),f.set(u,1,d),u+=1,f.set(u,0,n),f.set(u,1,d),u+=1,f.set(u,0,n),f.set(u,1,c),u+=1;Cc(i.indices.get(h.COLOR),i.vertexAttributes.get(h.COLOR).data,4,s.color,a,6);const g=i.indices.get(h.SIZE),_=i.vertexAttributes.get(h.SIZE).data;p=g.length;const w=s.size;u=a;for(let O=0;O<p;++O){const S=_[2*g[O]],y=_[2*g[O]+1];for(let E=0;E<6;++E)w.set(u,0,S),w.set(u,1,y),u+=1}if(i.indices.get(h.AUXPOS1)&&i.vertexAttributes.get(h.AUXPOS1)?_a(i.indices.get(h.AUXPOS1),i.vertexAttributes.get(h.AUXPOS1).data,s.auxpos1,a,6):ya(s.auxpos1,a,6*p),i.indices.get(h.AUXPOS2)&&i.vertexAttributes.get(h.AUXPOS2)?_a(i.indices.get(h.AUXPOS2),i.vertexAttributes.get(h.AUXPOS2).data,s.auxpos2,a,6):ya(s.auxpos2,a,6*p),A(i.objectAndLayerIdColor)&&i.indices.get(h.POSITION)){const O=i.indices.get(h.POSITION).length,S=s.getField(h.OBJECTANDLAYERIDCOLOR,Wc);bc(i.objectAndLayerIdColor,S,O,a,6)}}}function Fn(t){const e=new Yr,{vertex:r,fragment:i}=e,s=t.output===T.Depth,a=t.hasMultipassTerrain&&(t.output===T.Color||t.output===T.Alpha);return Jr(r,t),e.include(zs,t),e.include(Do,t),e.include(Ms,t),e.attributes.add(h.POSITION,"vec3"),e.varyings.add("vpos","vec3"),a&&e.varyings.add("depth","float"),s&&(e.include(xo,t),Ac(e),To(e)),r.code.add(m`
    void main(void) {
      vpos = position;
      forwardNormalizedVertexColor();
      forwardObjectAndLayerIdColor();
      ${a?"depth = (view * vec4(vpos, 1.0)).z;":""}
      gl_Position = ${s?m`transformPositionWithDepth(proj, view, vpos, nearFar, linearDepth);`:m`transformPosition(proj, view, vpos);`}
    }
  `),e.include(vt,t),a&&e.include(Ls,t),i.include(ji),i.uniforms.add(new Ee("eColor",o=>o.color)),t.output===T.Highlight&&e.include(Hi,t),i.code.add(m`
  void main() {
    discardBySlice(vpos);
    ${a?"terrainDepthTest(gl_FragCoord, depth);":""}
    vec4 fColor = ${t.hasVertexColors?"vColor * eColor;":"eColor;"}

    ${t.output===T.ObjectAndLayerIdColor?m`fColor.a = 1.0;`:""}

    if (fColor.a < ${m.float(Ve)}) {
      discard;
    }

    ${t.output===T.Alpha?m`gl_FragColor = vec4(fColor.a);`:""}

    ${t.output===T.Color?m`gl_FragColor = highlightSlice(fColor, vpos); ${t.transparencyPassType===re.Color?"gl_FragColor = premultiplyAlpha(gl_FragColor);":""}`:""}
    ${t.output===T.Highlight?m`outputHighlight();`:""};
    ${t.output===T.Depth?m`outputDepth(linearDepth);`:""};
    ${t.output===T.ObjectAndLayerIdColor?m`outputObjectAndLayerIdColor();`:""}
  }
  `),e}const fp=Object.freeze(Object.defineProperty({__proto__:null,build:Fn},Symbol.toStringTag,{value:"Module"}));let zn=class jn extends Ar{initializeConfiguration(e,r){r.hasWebGL2Context=e.rctx.type===zi.WEBGL2}initializeProgram(e){return new Rr(e.rctx,jn.shader.get().build(this.configuration),Qr)}_createPipeline(e,r){const i=this.configuration,s=e===re.NONE,a=e===re.FrontFace;return et({blending:i.output!==T.Color&&i.output!==T.Alpha||!i.transparent?null:s?jr:Wi(e),culling:Yc(i.cullFace),depthTest:{func:Bs(e)},depthWrite:(s||a)&&i.writeDepth?yr:null,colorWrite:lt,stencilWrite:i.hasOccludees?Oi:null,stencilTest:i.hasOccludees?r?Ci:Ns:null,polygonOffset:s||a?i.polygonOffset?mp:null:Fo(i.enableOffset)})}initializePipeline(){return this._occludeePipelineState=this._createPipeline(this.configuration.transparencyPassType,!0),this._createPipeline(this.configuration.transparencyPassType,!1)}getPipelineState(e,r){return r?this._occludeePipelineState:super.getPipelineState(e,r)}};zn.shader=new Pr(fp,()=>Tr(()=>Promise.resolve().then(()=>Gp),void 0));const mp={factor:1,units:1};let Re=class extends br{constructor(){super(...arguments),this.output=T.Color,this.cullFace=ks.None,this.hasSlicePlane=!1,this.hasVertexColors=!1,this.transparent=!1,this.polygonOffset=!1,this.enableOffset=!0,this.writeDepth=!0,this.hasOccludees=!1,this.transparencyPassType=re.NONE,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.objectAndLayerIdColorInstanced=!1}};v([x({count:T.COUNT})],Re.prototype,"output",void 0),v([x({count:ks.COUNT})],Re.prototype,"cullFace",void 0),v([x()],Re.prototype,"hasSlicePlane",void 0),v([x()],Re.prototype,"hasVertexColors",void 0),v([x()],Re.prototype,"transparent",void 0),v([x()],Re.prototype,"polygonOffset",void 0),v([x()],Re.prototype,"enableOffset",void 0),v([x()],Re.prototype,"writeDepth",void 0),v([x()],Re.prototype,"hasOccludees",void 0),v([x({count:re.COUNT})],Re.prototype,"transparencyPassType",void 0),v([x()],Re.prototype,"hasMultipassTerrain",void 0),v([x()],Re.prototype,"cullAboveGround",void 0),v([x()],Re.prototype,"objectAndLayerIdColorInstanced",void 0);let Cg=class extends gn{constructor(e){super(e,new vp),this.supportsEdges=!0,this._configuration=new Re}getConfiguration(e,r){return this._configuration.output=e,this._configuration.cullFace=this.parameters.cullFace,this._configuration.hasVertexColors=this.parameters.hasVertexColors,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.polygonOffset=this.parameters.polygonOffset,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.transparencyPassType=r.transparencyPassType,this._configuration.enableOffset=r.camera.relativeElevation<zo,this._configuration.hasMultipassTerrain=r.multipassTerrain.enabled,this._configuration.cullAboveGround=r.multipassTerrain.cullAboveGround,this._configuration}requiresSlot(e,r){return r===T.Color||r===T.Alpha||r===T.Highlight||r===T.Depth&&this.parameters.writeLinearDepth||r===T.ObjectAndLayerIdColor?e===W.DRAPED_MATERIAL?!0:r===T.Highlight?e===W.OPAQUE_MATERIAL:e===(this.parameters.transparent?this.parameters.writeDepth?W.TRANSPARENT_MATERIAL:W.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL:W.OPAQUE_MATERIAL):!1}createGLMaterial(e){return new gp(e)}createBufferWriter(){return new js(yt("enable-feature:objectAndLayerId-rendering")?jh:mn)}};class gp extends Ui{_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){return this._output!==T.Color&&this._output!==T.Alpha||this._updateOccludeeState(e),this.ensureTechnique(zn,e)}}let vp=class extends Vs{constructor(){super(...arguments),this.color=Ds,this.transparent=!1,this.writeDepth=!0,this.writeLinearDepth=!1,this.hasVertexColors=!1,this.polygonOffset=!1,this.hasSlicePlane=!1,this.cullFace=ks.None,this.hasOccludees=!1}};const _e={dash:[4,3],dot:[1,3],"long-dash":[8,3],"short-dash":[4,1],"short-dot":[1,1]},_p={dash:_e.dash,"dash-dot":[..._e.dash,..._e.dot],dot:_e.dot,"long-dash":_e["long-dash"],"long-dash-dot":[..._e["long-dash"],..._e.dot],"long-dash-dot-dot":[..._e["long-dash"],..._e.dot,..._e.dot],none:null,"short-dash":_e["short-dash"],"short-dash-dot":[..._e["short-dash"],..._e["short-dot"]],"short-dash-dot-dot":[..._e["short-dash"],..._e["short-dot"],..._e["short-dot"]],"short-dot":_e["short-dot"],solid:null},yp=8;function wp(t,e=2){return N(t)?t:{pattern:t.slice(),pixelRatio:e}}function Rg(t,e=2){return{pattern:[t,t],pixelRatio:e}}function Pg(t){return A(t)&&t.type==="style"?Sp(t.style):null}function Sp(t){return A(t)?wp(_p[t],yp):null}function Eg(t,e,r=null){const i=[],s=[],a=e.mapPositions;xp(e,s,i);const o=s[0][1].data,n=i[0][1].length,c=new Array(n).fill(0);return Tp(e,s,i,c),bp(e,s,i,c),Op(e,s,i,c),Cp(e,s,i,c),Ap(e,s,i,c),Rp(e,s,i,c),Pp(e,s,i,o),new Ne(t,s,i,a,Cr.Line,r)}function xp(t,e,r){const{attributeData:{position:i},removeDuplicateStartEnd:s}=t,a=Ep(i)&&s,o=i.length/3-(a?1:0),n=new Array(2*(o-1)),c=a?i.slice(0,i.length-3):i;let l=0;for(let d=0;d<o-1;d++)n[l++]=d,n[l++]=d+1;e.push([h.POSITION,new j(c,3,a)]),r.push([h.POSITION,n])}function Tp(t,e,r,i){if(A(t.attributeData.colorFeature))return;const s=t.attributeData.color;e.push([h.COLOR,new j(ae(s,Fi),4)]),r.push([h.COLOR,i])}function Op(t,e,r,i){if(!A(t.attributeData.normal))return;const s=t.attributeData.normal;e.push([h.NORMAL,new j(s,3)]),r.push([h.NORMAL,i])}function Cp(t,e,r,i){const s=t.attributeData.colorFeature;N(s)||(e.push([h.COLORFEATUREATTRIBUTE,new j([s],1,!0)]),r.push([h.COLOR,i]))}function bp(t,e,r,i){if(A(t.attributeData.sizeFeature))return;const s=t.attributeData.size;e.push([h.SIZE,new j([ae(s,1)],1,!0)]),r.push([h.SIZE,i])}function Ap(t,e,r,i){const s=t.attributeData.sizeFeature;N(s)||(e.push([h.SIZEFEATUREATTRIBUTE,new j([s],1,!0)]),r.push([h.SIZEFEATUREATTRIBUTE,i]))}function Rp(t,e,r,i){const s=t.attributeData.opacityFeature;N(s)||(e.push([h.OPACITYFEATUREATTRIBUTE,new j([s],1,!0)]),r.push([h.OPACITYFEATUREATTRIBUTE,i]))}function Pp(t,e,r,i){if(N(t.overlayInfo)||t.overlayInfo.renderCoordsHelper.viewingMode!==Ot.Global||!t.overlayInfo.spatialReference.isGeographic)return;const s=Us(i.length),a=Vc(t.overlayInfo.spatialReference);for(let u=0;u<s.length;u+=3)Il(i,u,s,u,a);const o=i.length/3,n=Ue(o+1);let c=Dp,l=$p,d=0,p=0;H(c,s[p++],s[p++],s[p++]),n[0]=0;for(let u=1;u<o+1;++u)u===o&&(p=0),H(l,s[p++],s[p++],s[p++]),d+=Ml(c,l),n[u]=d,[c,l]=[l,c];e.push([h.DISTANCETOSTART,new j(n,1,!0)]),r.push([h.DISTANCETOSTART,r[0][1]])}function Ep(t){const e=t.length;return t[0]===t[e-3]&&t[1]===t[e-2]&&t[2]===t[e-1]}const Dp=$(),$p=$();function Dg(t,e,r,i){const s=t.type==="polygon"?bi.CCW_IS_HOLE:bi.NONE,a=t.type==="polygon"?t.rings:t.paths,{position:o,outlines:n}=$o(a,!!t.hasZ,s),c=Us(o.length),l=Eu(o,t.spatialReference,0,c,0,o,0,o.length/3,e,r,i),d=l!=null;return{lines:d?Vn(n,o,c):[],projectionSuccess:d,sampledElevation:l}}function $g(t,e){const r=t.type==="polygon"?bi.CCW_IS_HOLE:bi.NONE,i=t.type==="polygon"?t.rings:t.paths,{position:s,outlines:a}=$o(i,!1,r),o=Si(s,t.spatialReference,0,s,e,0,s.length/3);for(let n=2;n<s.length;n+=3)s[n]=fu;return{lines:o?Vn(a,s):[],projectionSuccess:o}}function Vn(t,e,r=null){const i=new Array;for(const{index:s,count:a}of t){if(a<=1)continue;const o=3*s,n=3*a;i.push({position:wa(e,3*s,3*a),mapPositions:A(r)?wa(r,o,n):void 0})}return i}function Un(t){const e=new Yr,{vertex:r,fragment:i}=e;return e.include(zs,t),e.include(Do,t),e.include(en,t),Jr(r,t),t.stippleEnabled&&(e.attributes.add(h.UV0,"vec2"),e.attributes.add(h.AUXPOS1,"vec3"),r.uniforms.add(new Ee("viewport",(s,a)=>a.camera.fullViewport))),e.attributes.add(h.POSITION,"vec3"),e.varyings.add("vpos","vec3"),r.code.add(m`void main(void) {
vpos = position;
forwardNormalizedVertexColor();
gl_Position = transformPosition(proj, view, vpos);`),t.stippleEnabled&&(r.code.add(m`vec4 vpos2 = transformPosition(proj, view, auxpos1);
vec2 ndcToPixel = viewport.zw * 0.5;
float lineSegmentPixelSize = length((vpos2.xy / vpos2.w - gl_Position.xy / gl_Position.w) * ndcToPixel);`),t.draped?r.uniforms.add(new B("worldToScreenRatio",(s,a)=>1/a.screenToPCSRatio)):r.code.add(m`vec3 segmentCenter = (position + auxpos1) * 0.5;
float worldToScreenRatio = computeWorldToScreenRatio(segmentCenter);`),r.code.add(m`float discreteWorldToScreenRatio = discretizeWorldToScreenRatio(worldToScreenRatio);`),t.draped?r.code.add(m`float startPseudoScreen = uv0.y * discreteWorldToScreenRatio - mix(0.0, lineSegmentPixelSize, uv0.x);
float segmentLengthPseudoScreen = lineSegmentPixelSize;`):r.code.add(m`float segmentLengthRender = length(position - auxpos1);
float startPseudoScreen = mix(uv0.y, uv0.y - segmentLengthRender, uv0.x) * discreteWorldToScreenRatio;
float segmentLengthPseudoScreen = segmentLengthRender * discreteWorldToScreenRatio;`),r.uniforms.add(new B("stipplePatternPixelSize",s=>Ys(s))),r.code.add(m`vec2 stippleDistanceLimits = computeStippleDistanceLimits(startPseudoScreen, segmentLengthPseudoScreen, lineSegmentPixelSize, stipplePatternPixelSize);
vStippleDistance = mix(stippleDistanceLimits.x, stippleDistanceLimits.y, uv0.x);
vStippleDistance *= gl_Position.w;`)),r.code.add(m`}`),t.output===T.Highlight&&e.include(Hi,t),e.include(vt,t),i.uniforms.add(new B("alphaCoverage",(s,a)=>Math.min(1,s.width*a.camera.pixelRatio))),t.hasVertexColors||i.uniforms.add(new Ee("constantColor",s=>s.color)),i.code.add(m`
  void main() {
    discardBySlice(vpos);

    vec4 color = ${t.hasVertexColors?"vColor":"constantColor"};

    float stippleAlpha = getStippleAlpha();
    discardByStippleAlpha(stippleAlpha, stippleAlphaColorDiscard);

    vec4 finalColor = blendStipple(vec4(color.rgb, color.a * alphaCoverage), stippleAlpha);

    ${t.output===T.ObjectAndLayerIdColor?m`finalColor.a = 1.0;`:""}

    if (finalColor.a < ${m.float(Ve)}) {
      discard;
    }

    ${t.output===T.Color?m`gl_FragColor = highlightSlice(finalColor, vpos);`:""}
    ${t.output===T.Highlight?m`outputHighlight();`:""}
  }
  `),e}const Ip=Object.freeze(Object.defineProperty({__proto__:null,build:Un},Symbol.toStringTag,{value:"Module"}));class ki extends Ar{get _stippleEnabled(){return this.configuration.stippleEnabled&&this.configuration.output!==T.Highlight}initializeConfiguration(e,r){r.hasWebGL2Context=e.rctx.type===zi.WEBGL2}initializeProgram(e){return new Rr(e.rctx,ki.shader.get().build(this.configuration),Qr)}initializePipeline(){const e=this.configuration,r=jo(tt.SRC_ALPHA,tt.ONE,tt.ONE_MINUS_SRC_ALPHA,tt.ONE_MINUS_SRC_ALPHA),i=(s,a=null,o=null)=>et({blending:a,depthTest:Oo,depthWrite:o,colorWrite:lt,stencilWrite:e.hasOccludees?Oi:null,stencilTest:e.hasOccludees?s?Ci:Ns:null});return e.output===T.Color?(this._occludeePipelineState=i(!0,e.transparent||this._stippleEnabled?r:null,yr),i(!1,e.transparent||this._stippleEnabled?r:null,yr)):i(!1)}get primitiveType(){return Ht.LINES}getPipelineState(e,r){return r?this._occludeePipelineState:super.getPipelineState(e,r)}}ki.shader=new Pr(Ip,()=>Tr(()=>Promise.resolve().then(()=>Wp),void 0));class Me extends br{constructor(){super(...arguments),this.output=T.Color,this.hasSlicePlane=!1,this.hasVertexColors=!1,this.transparent=!1,this.draped=!1,this.stippleEnabled=!1,this.stippleOffColorEnabled=!1,this.stipplePreferContinuous=!0,this.hasOccludees=!1}}v([x({count:T.COUNT})],Me.prototype,"output",void 0),v([x()],Me.prototype,"hasSlicePlane",void 0),v([x()],Me.prototype,"hasVertexColors",void 0),v([x()],Me.prototype,"transparent",void 0),v([x()],Me.prototype,"draped",void 0),v([x()],Me.prototype,"stippleEnabled",void 0),v([x()],Me.prototype,"stippleOffColorEnabled",void 0),v([x()],Me.prototype,"stipplePreferContinuous",void 0),v([x()],Me.prototype,"hasOccludees",void 0),v([x({constValue:!1})],Me.prototype,"stippleRequiresClamp",void 0),v([x({constValue:!1})],Me.prototype,"stippleScaleWithLineWidth",void 0),v([x({constValue:!1})],Me.prototype,"stippleRequiresStretchMeasure",void 0);var Ni;(function(t){t[t.START=0]="START",t[t.END=1]="END"})(Ni||(Ni={}));class Ig extends Vi{constructor(e){super(e,new Np),this._configuration=new Me}getConfiguration(e,r){this._configuration.output=e,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasVertexColors=this.parameters.hasVertexColors,this._configuration.transparent=this.parameters.color[3]<1||this.parameters.width<1,this._configuration.draped=r.slot===W.DRAPED_MATERIAL;const i=A(this.parameters.stipplePattern);return this._configuration.stippleEnabled=i,this._configuration.stippleOffColorEnabled=i&&A(this.parameters.stippleOffColor),this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.stipplePreferContinuous=this.parameters.stipplePreferContinuous,this._configuration}intersect(e,r,i,s,a,o){if(!i.options.selectionMode||!e.visible)return;if(!Io(r))return void Kr.getLogger("esri.views.3d.webgl-engine.materials.NativeLineMaterial").error("intersection assumes a translation-only matrix");const n=e.vertexAttributes.get(h.POSITION).data,c=i.camera,l=zp;Or(l,i.point);const d=2;H(Nr[0],l[0]-d,l[1]+d,0),H(Nr[1],l[0]+d,l[1]+d,0),H(Nr[2],l[0]+d,l[1]-d,0),H(Nr[3],l[0]-d,l[1]-d,0);for(let _=0;_<4;_++)if(!c.unprojectFromRenderScreen(Nr[_],at[_]))return;gt(c.eye,at[0],at[1],_s),gt(c.eye,at[1],at[2],ys),gt(c.eye,at[2],at[3],ws),gt(c.eye,at[3],at[0],Ss);let p=Number.MAX_VALUE,u=0;for(let _=0;_<n.length-5;_+=3){if(Se[0]=n[_]+r[12],Se[1]=n[_+1]+r[13],Se[2]=n[_+2]+r[14],xe[0]=n[_+3]+r[12],xe[1]=n[_+4]+r[13],xe[2]=n[_+5]+r[14],ce(_s,Se)<0&&ce(_s,xe)<0||ce(ys,Se)<0&&ce(ys,xe)<0||ce(ws,Se)<0&&ce(ws,xe)<0||ce(Ss,Se)<0&&ce(Ss,xe)<0)continue;if(c.projectToRenderScreen(Se,Mt),c.projectToRenderScreen(xe,Lt),Mt[2]<0&&Lt[2]>0){ne(Qe,Se,xe);const O=c.frustum,S=-ce(O[_t.NEAR],Se)/ct(Qe,xi(O[_t.NEAR]));Z(Qe,Qe,S),te(Se,Se,Qe),c.projectToRenderScreen(Se,Mt)}else if(Mt[2]>0&&Lt[2]<0){ne(Qe,xe,Se);const O=c.frustum,S=-ce(O[_t.NEAR],xe)/ct(Qe,xi(O[_t.NEAR]));Z(Qe,Qe,S),te(xe,xe,Qe),c.projectToRenderScreen(xe,Lt)}else if(Mt[2]<0&&Lt[2]<0)continue;Mt[2]=0,Lt[2]=0;const w=ko(hr(Mt,Lt,po),l);w<p&&(p=w,k(ho,Se),k(uo,xe),u=_/3)}const f=i.rayBegin,g=i.rayEnd;if(p<d*d){let _=Number.MAX_VALUE;if(qo(hr(ho,uo,po),hr(f,g,Fp),It)){ne(It,It,f);const w=nt(It);Z(It,It,1/w),_=w/Tt(f,g)}o(_,It,u,!1)}}intersectDraped(e,r,i,s,a,o){if(!i.options.selectionMode)return;const n=e.vertexAttributes.get(h.POSITION).data,c=e.vertexAttributes.get(h.SIZE),l=c?c.data[0]:0,d=s[0],p=s[1],u=((l+1)/2+4)*e.screenToWorldRatio;let f=Number.MAX_VALUE,g=0;for(let _=0;_<n.length-5;_+=3){const w=n[_],O=n[_+1],S=d-w,y=p-O,E=n[_+3]-w,C=n[_+4]-O,b=zt((E*S+C*y)/(E*E+C*C),0,1),F=E*b-S,R=C*b-y,V=F*F+R*R;V<f&&(f=V,g=_/3)}f<u*u&&a(o.dist,o.normal,g,!1)}requiresSlot(e,r){return!(r!==T.Color&&r!==T.Highlight&&r!==T.ObjectAndLayerIdColor||e!==W.OPAQUE_MATERIAL&&e!==W.DRAPED_MATERIAL)}createGLMaterial(e){return new Mp(e)}createBufferWriter(){const e=this.parameters.hasVertexColors?mn:Fh;return N(this.parameters.stipplePattern)?new js(e):new Lp(e.clone().vec3f(h.AUXPOS1).vec2f(h.UV0))}}class Mp extends Ui{constructor(){super(...arguments),this._stipplePattern=null}dispose(){super.dispose(),this._stippleTextureRepository.release(this._stipplePattern),this._stipplePattern=null}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){this._output===T.Color&&this._updateOccludeeState(e);const r=this._material.parameters.stipplePattern;return this._stipplePattern!==r&&(this._material.setParameters(this._stippleTextureRepository.swap(this._stipplePattern,r)),this._stipplePattern=r),this.ensureTechnique(ki,e)}}class Lp{constructor(e){this.vertexBufferLayout=e}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){return e.indices.get(h.POSITION).length}write(e,r,i,s,a){Rc(i,this.vertexBufferLayout,e,r,s,a),this._writeAuxpos1(e,i,s,a),this._writeUV0(e,i,s,a)}_writeAuxpos1(e,r,i,s){const a=i.getField(h.AUXPOS1,Bc),o=r.indices.get(h.POSITION),n=r.vertexAttributes.get(h.POSITION).data,c=e,l=a.typedBufferStride,d=a.typedBuffer;s*=l;for(let p=0;p<o.length-1;p+=2)for(const u of[1,0]){const f=3*o[p+u],g=n[f],_=n[f+1],w=n[f+2],O=c[0]*g+c[4]*_+c[8]*w+c[12],S=c[1]*g+c[5]*_+c[9]*w+c[13],y=c[2]*g+c[6]*_+c[10]*w+c[14];d[s]=O,d[s+1]=S,d[s+2]=y,s+=l}}_writeUV0(e,r,i,s){var y;const a=i.getField(h.UV0,kc),o=r.indices.get(h.POSITION),n=r.vertexAttributes.get(h.POSITION).data,c=(y=r.vertexAttributes.get(h.DISTANCETOSTART))==null?void 0:y.data,l=a.typedBufferStride,d=a.typedBuffer;let p=0;d[s*=l]=Ni.START,d[s+1]=p,s+=l;const u=3*o[0],f=H(Se,n[u],n[u+1],n[u+2]);e&&ie(f,f,e);const g=xe,_=o.length-1;let w=1;const O=c?(E,C,b)=>p=c[b]:(E,C,b)=>p+=Tt(E,C);for(let E=1;E<_;E+=2){const C=3*o[E];H(g,n[C],n[C+1],n[C+2]),e&&ie(g,g,e),O(f,g,w++);for(let b=0;b<2;++b)d[s]=1-b,d[s+1]=p,s+=l;k(f,g)}const S=3*o[_];H(g,n[S],n[S+1],n[S+2]),e&&ie(g,g,e),O(f,g,w),d[s]=Ni.END,d[s+1]=p}}class Np extends Vs{constructor(){super(...arguments),this.color=Fi,this.hasVertexColors=!1,this.hasSlicePlane=!1,this.width=1,this.stipplePreferContinuous=!0,this.hasOccludees=!1,this.stippleTexture=null}}const Se=$(),xe=$(),Qe=$(),It=$(),Mt=Le(),Lt=Le(),ho=$(),uo=$(),po=Bi(),Fp=Bi(),zp=$(),Nr=[Le(),Le(),Le(),Le()],at=[$(),$(),$(),$()],_s=dt(),ys=dt(),ws=dt(),Ss=dt(),jp=Object.freeze(Object.defineProperty({__proto__:null,RIBBONLINE_NUM_ROUND_JOIN_SUBDIVISIONS:qr,build:cn},Symbol.toStringTag,{value:"Module"})),Vp=Object.freeze(Object.defineProperty({__proto__:null,build:vn},Symbol.toStringTag,{value:"Module"})),Mg=Object.freeze(Object.defineProperty({__proto__:null,build:Ec},Symbol.toStringTag,{value:"Module"})),Lg=Object.freeze(Object.defineProperty({__proto__:null,build:Dc,getRadius:$c},Symbol.toStringTag,{value:"Module"})),Up=Object.freeze(Object.defineProperty({__proto__:null,TextureOnlyPassParameters:Ro,build:Pc},Symbol.toStringTag,{value:"Module"})),Hp=Object.freeze(Object.defineProperty({__proto__:null,build:Dn,calculateAnchorPosForRendering:Zr},Symbol.toStringTag,{value:"Module"})),Gp=Object.freeze(Object.defineProperty({__proto__:null,build:Fn},Symbol.toStringTag,{value:"Module"})),Wp=Object.freeze(Object.defineProperty({__proto__:null,build:Un},Symbol.toStringTag,{value:"Module"}));export{Sg as $,eg as A,km as B,Br as C,qm as D,Ei as E,Bm as F,Cu as G,gg as H,mg as I,Rn as J,ng as K,Hu as L,Jm as M,Vm as N,Ym as O,Um as P,jm as Q,Gm as R,vg as S,Nu as T,Ru as U,rg as V,Ps as W,og as X,pg as Y,Yu as Z,Xu as _,Pi as a,lh as a0,Be as a1,dg as a2,hg as a3,Uu as a4,sg as a5,ug as a6,Ke as a7,Js as a8,Gt as a9,tg as aA,Mg as aB,Lg as aC,xr as aa,Rd as ab,Xd as ac,kr as ad,Qs as ae,nn as af,jf as ag,fe as ah,Vf as ai,Pg as aj,Ue as ak,Oh as al,Lh as am,Ct as an,Wt as ao,$h as ap,ig as aq,ag as ar,ro as as,qa as at,bn as au,zm as av,Jh as aw,Yh as ax,um as ay,oh as az,_g as b,fu as c,cg as d,Ur as e,Xm as f,Wm as g,Qm as h,tn as i,Cg as j,$g as k,ud as l,Eg as m,Eu as n,zd as o,Dg as p,Hm as q,Rg as r,U as s,ui as t,gn as u,zh as v,Ig as w,cd as x,Zm as y,Yd as z};
