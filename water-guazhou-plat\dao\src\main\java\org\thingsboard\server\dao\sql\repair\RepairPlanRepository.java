package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.RepairPlanEntity;

import java.util.List;

public interface RepairPlanRepository extends JpaRepository<RepairPlanEntity, String> {

    @Query("SELECT rp FROM RepairPlanEntity rp " +
            "WHERE rp.tenantId = ?2 AND rp.name LIKE %?1% AND rp.status IN ?3")
    Page<RepairPlanEntity> findList(String name, String tenantId, List<String> statusList, Pageable pageable);

    List<RepairPlanEntity> findByType(String type);
}
