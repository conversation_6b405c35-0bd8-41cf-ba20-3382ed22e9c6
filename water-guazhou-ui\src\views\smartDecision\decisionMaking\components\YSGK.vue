<template>
  <div class="scgk-info">
    <TargetItem
      v-for="(item, i) in state.info"
      :key="i"
      :config="item"
      :class="item.className"
    />
    <el-image
      :fit="'contain'"
      class="ellipse-image"
      :src="ellipseBg"
      alt=""
    />
    <div class="annual-water-sale">
      <div class="count">
        318.72
      </div>
      <div class="text">
        年度累计售水量 (万m3)
      </div>
    </div>
  </div>

  <ScrollList :data="state.data"></ScrollList>
</template>
<script lang="ts" setup>
import ScrollList from './ScrollList.vue'
import TargetItem from './TargetItem.vue'
import ellipseBg from '../imgs/decisionMaking_center_left.png'

const state = reactive<{
  info: ITargetItem[]
  data: any[]
}>({
  data: [
    { name: '水费回收率(%)', value: '193.1', scale: '-52.5%' },
    { name: '综合水价（元', value: '9.28', scale: '-13%' },
    { name: '抢修次数', value: '2.04', scale: '-0.8%' }
  ],
  info: [
    {
      label: '年度目标售水量',
      value: '2,000.00',
      unit: '万m3',
      text: '完成率',
      scale: '16.2%',
      className: 'annual-water-target-sale',
      rows: [1, 2, 3]
    },
    {
      label: '全年日均售水量',
      value: '3.14',
      unit: '万m3',
      text: '同比',
      status: 'down',
      scale: '3.4%',
      className: 'dayly-max-water-sale',
      rows: [1, 2, 3]
    }
  ]
})
</script>
<style lang="scss" scoped>
.scgk-info {
  position: relative;
  width: 100%;
  height: 290px;
  .annual-water-target-sale {
    position: absolute;
    top: 60px;
    left: 20px;
  }
  .dayly-max-water-sale {
    position: absolute;
    top: 60px;
    right: 20px;
  }
  .ellipse-image {
    position: absolute;
    top: 120px;
    left: 50%;
    width: 90%;
    transform: translateX(-52%);
  }
  .annual-water-sale {
    position: absolute;
    top: 190px;
    left: 50%;
    transform: translateX(-50%);
    .count {
      font-size: 24px;
      text-align: center;
    }
    .text {
      font-size: 14px;
      text-align: center;
    }
  }
}
</style>
