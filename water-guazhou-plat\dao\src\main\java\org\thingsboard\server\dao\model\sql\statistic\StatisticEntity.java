package org.thingsboard.server.dao.model.sql.statistic;

import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;

import static org.thingsboard.server.dao.util.TimeUtils.formatDateTime;

@Deprecated
public class StatisticEntity implements Responsible {
    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        return process(returnHelper);
    }

    public Object process(ReturnHelper helper) {
        HashMap<String, Object> result = new ResponseMap();
        for (Field field : this.getClass().getDeclaredFields()) {
            Object value = ReflectionUtils.getValue(field, this);
            // 忽略空值
            if (value == null) {
                continue;
            }

            if (value instanceof StatisticItem) {
                // 处理统计数据
                if (((StatisticItem) value).isEmpty()) {
                    continue;
                }

                if (value instanceof ScopeStatisticLongWrapper) {
                    result.put(field.getName(), processScopeStatisticLongWrapper(helper, (ScopeStatisticLongWrapper) value));
                } else if (value instanceof TimedStatisticLongWrapperSplitter) {
                    result.put(field.getName(), processTimedStatisticLongWrapperSplitter(helper, (TimedStatisticLongWrapperSplitter) value));
                } else if (value instanceof MultiTimedStatisticLongWrapper) {
                    result.put(field.getName(), processMultiTimedStatisticLongWrapper(helper, (MultiTimedStatisticLongWrapper) value));
                }
            } else {
                // 处理普通数据
                result.put(field.getName(), helper.process(value, null));
            }

        }
        return result;
    }

    private Object processScopeStatisticLongWrapper(ReturnHelper helper, ScopeStatisticLongWrapper wrapper) {
        return processStatisticList(helper, wrapper.getData());
    }

    private Object processTimedStatisticLongWrapperSplitter(ReturnHelper helper, TimedStatisticLongWrapperSplitter splitter) {
        List<Object> result = new ArrayList<>();
        for (Map.Entry<String, List<TimedStatisticLongWrapper>> entry : splitter.getRoutes().entrySet()) {
            Object processedObj = processStatisticList(helper, entry.getValue());
            // 无需处理
            if (processedObj instanceof List<?>) {
                Map<String, Object> map = new HashMap<>();
                map.put("data", processedObj);
                return map;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) processedObj;
            map.put("key", entry.getKey());
            result.add(map);
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    private Map<String, Object> processMultiTimedStatisticLongWrapper(ReturnHelper helper, MultiTimedStatisticLongWrapper wrapper) {
        Map<String, Object> map = new HashMap<>();
        List<StatisticLong> statisticLongs = wrapper.getStatisticLongs();
        Object processedValue = processStatisticList(helper, statisticLongs);
        if (processedValue == statisticLongs) {
            map.put("from", formatDateTime(wrapper.getFrom()));
            map.put("to", formatDateTime(wrapper.getTo()));
            map.put("total", 0);
            map.put("data", statisticLongs);
            return map;
        }

        map = (Map<String, Object>) processedValue;
        map.put("from", formatDateTime(wrapper.getFrom()));
        map.put("to", formatDateTime(wrapper.getTo()));
        return map;
    }

    @SuppressWarnings("unchecked")
    private Object processStatisticList(ReturnHelper helper, List<?> list) {
        Map<String, Object> map = new HashMap<>();
        Iterator<?> iterator = list.iterator();
        Object next = iterator.hasNext() ? iterator.next() : null;
        if (next == null) {
            return list;
        }

        if (next instanceof MultiTimedStatisticLongWrapper) {
            List<Object> processedList = new ArrayList<>();
            while (next != null) {
                processedList.add(processMultiTimedStatisticLongWrapper(helper, (MultiTimedStatisticLongWrapper) next));
                next = iterator.hasNext() ? iterator.next() : null;
            }
            return processedList;
        } else if (next instanceof Statisticable) {
            // 处理StatisticLong
            resolveStatisticable(helper, map, next, iterator, list);
        } else if (next instanceof List) {
            List<Object> processedList = new ArrayList<>();
            while (next != null) {
                processedList.add(processStatisticList(helper, (List<Object>) next));
                next = iterator.hasNext() ? iterator.next() : null;
            }
            map.put("data", processedList);
        } else {
            return list;
        }

        return map;
    }

    private void resolveStatisticable(ReturnHelper helper, Map<String, Object> map, Object next, Iterator<?> iterator, List<?> list) {
        // region 计算Total
        long total = 0L;
        while (next != null) {
            total += ((Statisticable) next).getValue();
            next = iterator.hasNext() ? iterator.next() : null;
        }
        // endregion
        List<Object> data = new ArrayList<>();
        for (Object item : list) {
            data.add(helper.process(item, total));
        }
        map.put("total", total);
        map.put("data", data);
    }
}
