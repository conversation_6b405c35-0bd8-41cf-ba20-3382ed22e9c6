package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 分机状态
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-12-14
 */
@TableName("tb_service_call_extension_status")
@Data
public class CallExtensionStatus {

    @TableId
    private String id;

    private String extension;

    private String userId;

    private Date startTime;

    private Date endTime;

    private String status;

    private String remark;

    private transient Integer duration;

    private transient String seatsName;

}
