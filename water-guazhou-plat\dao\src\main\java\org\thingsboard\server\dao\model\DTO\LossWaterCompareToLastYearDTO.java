package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 漏失水量同比报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-25
 */
@Data
@NoArgsConstructor
public class LossWaterCompareToLastYearDTO {

    private String id;

    private String name;

    private String status;

    private String statusName;

    private String month;

    private BigDecimal lossWater = BigDecimal.ZERO;

    private BigDecimal changeRate = BigDecimal.ZERO;


}
