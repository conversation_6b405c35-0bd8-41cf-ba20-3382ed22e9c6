package org.thingsboard.server.dao.util.imodel.query.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段说明
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-03-01
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface FieldRemark {
    String value();
}
