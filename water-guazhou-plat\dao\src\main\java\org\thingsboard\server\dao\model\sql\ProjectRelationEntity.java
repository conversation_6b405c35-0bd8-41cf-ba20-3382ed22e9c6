package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.PROJECT_RELATION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectRelationEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.PROJECT_RELATION_ENTITY_ID)
    private String entityId;

    @Column(name = ModelConstants.PROJECT_RELATION_ENTITY_TYPE)
    private String entityType;

    @Column(name = ModelConstants.PROJECT_RELATION_CREATE_TIME)
    private Long createTime;

    public ProjectRelationEntity(String projectId, String entityId, String entityType, Long createTime) {
        this.projectId = projectId;
        this.entityId = entityId;
        this.entityType = entityType;
        this.createTime = createTime;
    }
}
