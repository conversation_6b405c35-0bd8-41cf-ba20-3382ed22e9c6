package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstruction {
    // id
    private String id;

    // 编号
    private String code;

    // 所属项目编号
    private String projectCode;

    // 所属项目名称
    @TableField(exist = false)
    private String projectName;

    // 工程名称
    private String name;

    // 工程地址
    private String address;

    // 工程类型
    private String typeId;

    // 工程类型
    @TableField(exist = false)
    private String typeName;

    // 创建时间
    private Date createTime;

    // 甲方代表
    private String firstpartName;

    // 联系电话
    private String firstpartPhone;

    // 详细地址
    private String detailAddress;

    // 工程概况
    private String remark;

    // 工程预算，万元
    private BigDecimal estimate;

    // 附件信息
    private String attachments;

    // 创建者
    @ParseUsername
    private String creator;

    // 最后更新时间
    private Date updateTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 客户id
    private String tenantId;

    // 是否可以被删除
    private Boolean canBeDelete;

}
