import{d as y,c as i,r as d,a8 as v,o as b,ay as k,g as s,n as V,h as f,i as a,p as u,q as C,j as D,_ as M,C as w}from"./index-r0dFAfgr.js";import{折 as m}from"./charts-CPgX3ymz.js";import{k as F,l as B}from"./ledgerManagement-CkhtRd8m.js";const L={style:{width:"calc(100% - 20px)"}},N={class:"tcharts"},S={class:"charts"},T=y({__name:"maintenanceinfo",props:{id:{}},setup(_){const l=_,n=i(new Date().toString()),r=i(m([],"保养次数")),c=d({title:"",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"divider",text:"保养信息"},{xl:8,type:"text",label:"保养次数:",field:"count"},{xl:8,type:"text",label:"最近保养:",field:"latestMaintainTime"}]}]}),h=d({title:"",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"divider",text:"保养计划"},{type:"table",field:"faultReportCList",config:{indexVisible:!0,height:"350px",dataList:v(()=>p.value),columns:[{label:"计划名称",prop:"name"},{label:"限制时间",prop:"limitDays"},{label:"循环周期",prop:"cycleDays"},{label:"下一次保养时间",prop:"nextTime"},{label:"任务人",prop:"userName"}],pagination:{hide:!0}}}]}]}),p=i([]),g=async()=>{F(l.id).then(o=>{const e=o.data.data||{};for(const t in e)(e[t]===void 0||e[t]===null)&&(e[t]=" ");c.defaultValue={...e},r.value=m(e.nowYearMaintain||[],"保养次数"),setTimeout(()=>{n.value=new Date().toString()},1e3)}),B(l.id).then(o=>{p.value=o.data.data.data||[]})};return b(()=>{g()}),(o,e)=>{const t=M,x=k("VChart");return s(),V("div",L,[(s(),f(t,{key:a(n),ref:"refForm",config:a(c)},null,8,["config"])),u("div",N,[u("div",S,[C(x,{ref:"refChart2",autoresize:"",theme:a(D)().isDark?"dark":"light",option:a(r)},null,8,["theme","option"])])]),(s(),f(t,{key:a(n),ref:"refForm",config:a(h)},null,8,["config"]))])}}}),q=w(T,[["__scopeId","data-v-8ffe2b51"]]);export{q as default};
