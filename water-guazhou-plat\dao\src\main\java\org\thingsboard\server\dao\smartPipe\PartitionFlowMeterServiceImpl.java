package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionFlowMeter;
import org.thingsboard.server.dao.sql.smartPipe.PartitionFlowMeterMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionFlowMeterServiceImpl implements PartitionFlowMeterService {

    @Autowired
    private PartitionFlowMeterMapper partitionFlowMeterMapper;

    @Override
    public PartitionFlowMeter save(PartitionFlowMeter partitionFlowMeter) {
        if (StringUtils.isBlank(partitionFlowMeter.getId())) {
            partitionFlowMeter.setCreateTime(new Date());
            partitionFlowMeterMapper.insert(partitionFlowMeter);
        } else {
            partitionFlowMeterMapper.updateById(partitionFlowMeter);
        }
        return partitionFlowMeter;
    }


    @Override
    public PageData<PartitionFlowMeter> getList(PartitionMountRequest request) {
        IPage<PartitionFlowMeter> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionFlowMeter> result = partitionFlowMeterMapper.getList(page, request);
        result.getRecords().stream().forEach(a -> {
            try {
                a.setTypeName(DataConstants.PARTITION_FLOW_METER_TYPE.getByValue(a.getType()).getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionFlowMeterMapper.deleteBatchIds(ids);
    }

}
