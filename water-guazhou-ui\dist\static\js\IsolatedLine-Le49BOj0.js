import{d as M,c as C,r as L,b as c,Q as N,g as A,h as B,F as E,q as R,i as S,_ as T,X as q}from"./index-r0dFAfgr.js";import{w as z}from"./Point-WxyopZva.js";import{d as U,s as $,h as Q}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import{i as V,e as j}from"./IdentifyHelper-RJWmLn49.js";import{g as J,a as K}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as F,i as X}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{g as H}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import W from"./RightDrawerMap-D5PhmGFO.js";import{C as Y}from"./pipeCheck-BaGB4XFi.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const pe=M({__name:"IsolatedLine",setup(Z){const d=C(),h=C(),i=L({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1,flagOids:[]}),t={identifyResults:[]},v=L({group:[{fieldset:{desc:"选择水源点"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"选择水源地",disabled:()=>i.loading,iconifyIcon:"gis:multipoint",click:()=>I()},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>i.loading,iconifyIcon:"ep:delete",click:()=>x()}]}]},{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,label:"选择图层",showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(r,e)=>{e&&d.value&&(d.value.dataForm.layerid=[r.value])}},{type:"btn-group",btns:[{perm:!0,text:()=>i.loading?"正在检查，过程稍长，请耐心等待！":"检查",styles:{width:"100%"},loading:()=>i.loading,click:()=>G()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),I=()=>{var r;t.view&&(F("crosshair"),t.draw=X(t.view),t.drawAction=t.draw.create("point"),(r=t.drawAction)==null||r.on("draw-complete",async e=>{k(e),I()}))},k=async r=>{var a,p,s,m,o,n,f,u;if(!((a=r.coordinates)!=null&&a.length)||!t.view)return;const e=((p=d.value)==null?void 0:p.dataForm.layerid)||[];if(!e.length===void 0){c.warning("请选择一个图层");return}try{const y=new z({x:r.coordinates[0],y:r.coordinates[1],spatialReference:(s=t.view)==null?void 0:s.spatialReference}),g=V();g.layerIds=e,g.geometry=y,g.mapExtent=(m=t.view)==null?void 0:m.extent;const b=await j(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,g),l=b.results&&b.results[0];if(!l){c.warning("没有查询到管线");return}const w=U((o=l==null?void 0:l.feature)==null?void 0:o.geometry,y);if(!w)return;const _=l.feature;_.symbol=$("polyline"),(n=t.graphicsLayer)==null||n.add(_),i.flagOids.push(l==null?void 0:l.feature.attributes.OBJECTID);const D=Q(w.x,w.y,{picUrl:H("水源地.png"),picSize:[12,15],yOffset:7,spatialReference:(f=t.view)==null?void 0:f.spatialReference});(u=t.graphicsLayer)==null||u.add(D)}catch{c.error("检查失败")}},x=()=>{var r;(r=t.graphicsLayer)==null||r.removeAll(),i.flagOids.length=0},O=async()=>{var p,s,m;i.layerIds=K(t.view);const r=await q(i.layerIds);i.layerInfos=((s=(p=r.data)==null?void 0:p.result)==null?void 0:s.rows)||[];const e=(m=v.group.find(o=>o.id==="layer"))==null?void 0:m.fields[0],a=i.layerInfos.filter(o=>o.geometrytype==="esriGeometryPolyline").map(o=>({label:o.layername,value:o.layerid,data:o}));e&&(e.options=[{label:"管线类",value:-2,children:a,disabled:!0}]),a.length===1&&d.value&&(d.value.dataForm.layerid=a.map(o=>o.value))},G=async()=>{var e,a,p,s,m,o;(e=t.drawAction)==null||e.destroy(),F(""),c.info("正在检查，请稍候...");const r=(a=d.value)==null?void 0:a.dataForm.layerid[0];if(r===void 0){c.warning("请先选择一个图层");return}if(!i.flagOids.length){c.warning("请先选择水源点");return}i.loading=!0;try{const n=(p=i.layerInfos.find(y=>y.layerid===r))==null?void 0:p.layername,f=await Y({flagOIDs:i.flagOids.join(","),layer:n});(s=f.data.result)!=null&&s.length||c.success("没有相关数据");const u=[{label:n,name:n,data:((m=f.data)==null?void 0:m.result)||[],layerid:r}];await((o=h.value)==null?void 0:o.refreshDetail(u))}catch(n){console.log(n),i.loading=!1,c.error("检查失败")}},P=r=>{t.view=r,O(),t.graphicsLayer=J(t.view,{id:"isolated-line",title:"孤立组件"})};return N(()=>{var r,e,a;(r=t.graphicsLayer)==null||r.removeAll(),(e=t.drawAction)==null||e.destroy(),(a=t.draw)==null||a.destroy()}),(r,e)=>{const a=T;return A(),B(W,{ref_key:"refMap",ref:h,title:"孤立组件检查",onMapLoaded:P,onDetailRefreshed:e[0]||(e[0]=p=>S(i).loading=!1)},{default:E(()=>[R(a,{ref_key:"refForm",ref:d,config:S(v)},null,8,["config"])]),_:1},512)}}});export{pe as default};
