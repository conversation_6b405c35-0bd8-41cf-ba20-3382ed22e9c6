<!-- 入库单 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
    <SLDrawer ref="detailForm" :config="detailConfig"></SLDrawer>
    <!-- 设备选中 -->
    <SLDrawer ref="refFormEquipment" :config="addEquipment"></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import {
  getStoreInRecordSerch,
  postStoreInRecord,
  getstoreSerch,
  getGoodsShelfSerch,
  getStoreInRecordDetailSerch
} from '@/api/equipment_assets/equipmentOutStock';
import {
  getDevicePurchaseSearch,
  getDeviceapiContract,
  getDevicePurchaseItem
} from '@/api/equipment_assets/equipmentPurchase';
import {
  getSupplierSerch,
  getDeviceListSearch
} from '@/api/equipment_assets/equipmentManage';
import { traverse, uniqueFunc } from '@/utils/GlobalHelper';
import { formatDate } from '@/utils/DateFormatter';

const { $btnPerms } = useGlobal();

const refSearch = ref<ICardSearchIns>();

const refForm = ref<ISLDrawerIns>();

const detailForm = ref<ISLDrawerIns>();

const refFormEquipment = ref<ISLDrawerIns>();

const chosen = ref([]);

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '入库单编码', field: 'code', type: 'input', labelWidth: '90px' },
    { label: '入库单标题', field: 'title', type: 'input', labelWidth: '90px' },
    { label: '发票编号', field: 'invoiceCode', type: 'input' },
    {
      label: '仓库名称',
      field: 'storehouseId',
      type: 'select',
      labelWidth: '90px',
      options: computed(() => data.storeList) as any
    },
    {
      type: 'department-user',
      label: '验收人',
      field: 'acceptor'
    },
    {
      type: 'department-user',
      label: '经办人',
      field: 'manager'
    },
    { label: '入库时间', field: 'time', type: 'daterange', labelWidth: '120px' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          type: 'success',
          perm: true,
          text: '新增',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '单据号', prop: 'code' },
    { label: '标题', prop: 'title' },
    { label: '批次号', prop: 'batchCode' },
    { label: '发票号', prop: 'invoiceCode' },
    { label: '目标仓库', prop: 'storehouseName' },
    {
      label: '入库时间',
      prop: 'inTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD')
    },
    { label: '验收人', prop: 'acceptorName' },
    { label: '经办人', prop: 'managerName' },
    { label: '创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD')
    },
    { label: '供应商', prop: 'supplierName' }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: (row) => openDetails(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '添加',
  labelWidth: '100px',
  width: '1300px',
  submit: (params: any, status?: boolean) => {
    if (status) {
      data.getDevice();
      refFormEquipment.value?.openDrawer();
      return;
    }
    if (
      !params.items.some(
        (item) => item.shelvesId && item.num && item.price && item.taxRete
      )
    ) {
      ElMessage.warning('设备信息未填写完整');
      return;
    }
    postStoreInRecord(params)
      .then(() => {
        refreshData();
        ElMessage.success('添加成功');
        refForm.value?.closeDrawer();
      })
      .catch((error) => {
        ElMessage.warning(error);
      });
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          readonly: true,
          xl: 8,
          type: 'input-number',
          label: '入库单编号',
          field: 'code',
          rules: [{ required: true, message: '请输入入库单编号' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '入库单标题',
          field: 'title',
          rules: [{ required: true, message: '请输入入库单标题' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '批次号',
          field: 'batchCode'
        },
        {
          xl: 8,
          type: 'select',
          label: '相关采购单',
          field: 'purchaseId',
          options: computed(() => data.DevicePurchase) as any,
          onChange: (row) => data.getdetail(row)
        },
        {
          xl: 8,
          type: 'select',
          label: '目标仓库',
          field: 'storehouseId',
          rules: [{ required: true, message: '请选择目标仓库' }],
          options: computed(() => data.storeList) as any,
          onChange: (row) => data.getGoodsShelfValue(row)
        },
        {
          xl: 8,
          type: 'department-user',
          label: '验收人',
          field: 'acceptor'
        },
        {
          xl: 8,
          type: 'department-user',
          label: '经办人',
          field: 'manager',
          rules: [{ required: true, message: '请选择经办人' }]
        },
        {
          xl: 8,
          type: 'select',
          label: '所属合同',
          field: 'contractId',
          options: computed(() => data.contract) as any
        },
        {
          xl: 8,
          type: 'input-number',
          label: '发票编号',
          field: 'invoiceCode'
        },
        {
          xl: 8,
          type: 'select',
          label: '供应商',
          field: 'supplierId',
          rules: [{ required: true, message: '请选择供应商' }],
          options: computed(() => data.SupplierList) as any
        },
        {
          xl: 8,
          type: 'date',
          label: '入库时间',
          field: 'inTime',
          rules: [{ required: true, message: '请输入入库时间' }],
          format: 'YYYY-MM-DD HH:mm:ss'
        },
        {
          xl: 8,
          type: 'switch',
          label: '是否补录',
          field: 'addRecord'
        },
        {
          xl: 18,
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        },
        {
          type: 'table',
          field: 'items',
          config: {
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end',
                  marginBottom: '10px'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '增加设备',
                        perm: true,
                        click: () => {
                          refForm.value?.Submit(true);
                        }
                      },
                      {
                        text: '导入',
                        perm: true,
                        click: () => {
                          //   refForm.value?.openDrawer()
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '顶级类别',
                prop: 'topType'
              },
              {
                label: '货架',
                prop: 'shelvesId',
                formItemConfig: {
                  type: 'select-tree',
                  checkStrictly: true,
                  options: computed(() => data.GoodsShelf) as any
                }
              },
              {
                label: '数量',
                prop: 'num',
                formItemConfig: {
                  type: 'number',
                  min: 0
                }
              },
              {
                label: '单价(元)',
                prop: 'price',
                formItemConfig: {
                  type: 'number',
                  min: 0
                }
              },
              {
                label: '税率',
                prop: 'taxRete',
                formItemConfig: {
                  type: 'number',
                  min: 0
                }
              }
            ],
            operationWidth: 80,
            operations: [
              {
                text: '移除',
                type: 'danger',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: (row) => {
                  data.selectList = data.selectList.filter(
                    (item) => item.id !== row.id
                  );
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          readonly: true,
          xl: 8,
          type: 'input-number',
          label: '入库单编号',
          field: 'code',
          rules: [{ required: true, message: '请输入入库单编号' }]
        },
        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '入库单标题',
          field: 'title',
          rules: [{ required: true, message: '请输入入库单标题' }]
        },
        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '批次号',
          field: 'batchCode'
        },
        {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '相关采购单',
          field: 'purchaseId',
          options: computed(() => data.DevicePurchase) as any,
          onChange: (row) => data.getdetail(row)
        },
        {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '目标仓库',
          field: 'storehouseId',
          rules: [{ required: true, message: '请选择目标仓库' }],
          options: computed(() => data.storeList) as any,
          onChange: (row) => data.getGoodsShelfValue(row)
        },

        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '验收人',
          field: 'acceptorName'
        },

        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '经办人',
          field: 'managerName',
          rules: [{ required: true, message: '请选择经办人' }]
        },
        {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '所属合同',
          field: 'contractId',
          options: computed(() => data.contract) as any
        },
        {
          readonly: true,
          xl: 8,
          type: 'input-number',
          label: '发票编号',
          field: 'invoiceCode'
        },
        {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '供应商',
          field: 'supplierId',
          rules: [{ required: true, message: '请选择供应商' }],
          options: computed(() => data.SupplierList) as any
        },
        {
          readonly: true,
          xl: 8,
          type: 'date',
          label: '入库时间',
          field: 'inTime',
          rules: [{ required: true, message: '请输入入库时间' }]
        },
        {
          readonly: true,
          xl: 8,
          type: 'switch',
          label: '是否补录',
          field: 'addRecord'
        },
        {
          disabled: true,
          xl: 18,
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        },
        {
          type: 'table',
          field: 'items',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '规格/型号',
                prop: 'model'
              },
              {
                label: '顶级类别',
                prop: 'topType'
              },
              {
                label: '货架',
                prop: 'shelvesName'
              },
              {
                label: '数量',
                prop: 'num'
              },
              {
                label: '单价(元)',
                prop: 'price'
              },
              {
                label: '税率',
                prop: 'taxRete'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});
// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  submit: (params: any, status?: boolean) => {
    // 搜索处理
    if (status) {
      delete params.device;
      data.getDevice(params);
    } else {
      data.selectList = [...data.selectList, ...chosen.value];
      data.selectList = uniqueFunc(data.selectList, 'id');
      refFormEquipment.value?.closeDrawer();
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '设备编码',
          field: 'serialId'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: (val) => {
              chosen.value = val;
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '搜索',
                        perm: true,
                        click: () => {
                          refFormEquipment.value?.Submit(true);
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '设备编码',
                prop: 'serialId'
              },
              {
                label: '设备名称',
                prop: 'name'
              },
              {
                label: '设备款式',
                prop: 'model'
              },
              {
                label: '可用年限',
                prop: 'useYear'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const clickCreatedRole = () => {
  addOrUpdateConfig.defaultValue = {
    code: 'RK' + formatDate(new Date(), 'YYYYMMDDHHmmss')
  };
  chosen.value = [];
  data.selectList = [];
  refForm.value?.openDrawer();
};

const openDetails = (row: { [x: string]: any }) => {
  for (const i in row) {
    if (row[i] === undefined || row[i] === null) {
      row[i] = ' ';
    }
  }
  detailConfig.defaultValue = { ...(row || {}) };
  getStoreInRecordDetailSerch({ page: 1, size: 99999, mainId: row.id }).then(
    (res) => {
      data.selectList = res.data.data.data || [];
    }
  );
  detailForm.value?.openDrawer();
};

const data = reactive({
  // 采购单
  DevicePurchase: [],
  // 仓库
  storeList: [],
  // 设备列表
  deviceValue: [],
  // 选中的设备
  selectList: [] as any[],
  // 供应商
  SupplierList: [],
  // 合同
  contract: [],
  // 货架
  GoodsShelf: [],
  // 获取设备
  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    };
    getDeviceListSearch(params).then((res) => {
      data.deviceValue = res.data.data.data || [];
    });
  },
  // 获取采购单
  getDevicePurchaseValue: () => {
    const params = { page: 1, size: 99999 };
    getDevicePurchaseSearch(params).then((res) => {
      data.DevicePurchase = traverse(res.data.data.data || [], 'children', {
        label: 'title',
        value: 'id'
      });
    });
  },
  // 获取仓库
  getstoreSerchValue: () => {
    const params = { page: 1, size: 99999 };
    getstoreSerch(params).then((res) => {
      data.storeList = traverse(res.data.data.data || []);
    });
  },
  // 获取供应商
  getSupplierValue: () => {
    const params = { page: 1, size: 99999 };
    getSupplierSerch(params).then((res) => {
      data.SupplierList = traverse(res.data.data.data || []);
    });
  },
  // 获取货架
  getGoodsShelfValue: (row) => {
    const params = { page: 1, size: 99999, id: row };
    getGoodsShelfSerch(params).then((res) => {
      data.GoodsShelf = traverse(
        res.data.data.data[0].children || [],
        'children',
        { label: 'name', value: 'id' }
      );
    });
  },
  // 获取合同
  getDeviceapiContractDetailValue: () => {
    const params = { page: 1, size: 99999 };
    getDeviceapiContract(params).then((res) => {
      data.contract = traverse(res.data.data.data || [], 'children', {
        label: 'title',
        value: 'id'
      });
    });
  },
  // 根据采购单获取设备
  getdetail: (mainId) => {
    // 代表取消选中
    if (!mainId) {
      data.selectList = [];
      return;
    }
    const params = { page: 1, size: 99999, mainId };
    getDevicePurchaseItem(params).then((res) => {
      data.selectList = res.data.data.data || [];
    });
  },

  init: () => {
    data.getDevicePurchaseValue();
    data.getstoreSerchValue();
    data.getSupplierValue();
    data.getDeviceapiContractDetailValue();
  }
});

const refreshData = async () => {
  const params: {
    size: number | undefined;
    page: number | undefined;
    fromTime?: string;
    toTime?: string;
    time?: string;
  } = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.time && params.time?.length > 1) {
    params.fromTime = (refSearch.value?.queryParams as any).time[0] || '';
    params.toTime = (refSearch.value?.queryParams as any).time[1] || '';
  }
  delete params.time;
  getStoreInRecordSerch(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.init();
});
</script>
