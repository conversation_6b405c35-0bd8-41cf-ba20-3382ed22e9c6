package org.thingsboard.server.dao.util.imodel.query.smartManagement.district;

import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CircuitDistrictAreaPageRequest extends AdvancedPageableQueryEntity<SMCircuitDistrictArea, CircuitDistrictAreaPageRequest> {
    // 所属片区Id
    private String districtId;
}
