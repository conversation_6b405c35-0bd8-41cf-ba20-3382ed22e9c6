package org.thingsboard.server.dao.sql.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.ProjectEntity;

@Mapper
public interface ProjectMapper extends BaseMapper<ProjectEntity> {
    @Select("select name from project where id = #{id}")
    String getNameById(String id);
}
