<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionPipeMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPipe">
        select a.*
        from tb_pipe_partition_pipe a
        <where>
            <if test="param.partitionId != null and param.partitionId != ''">
                and a.partition_id = #{param.partitionId}
            </if>
        </where>
        order by a.create_time desc

    </select>
</mapper>