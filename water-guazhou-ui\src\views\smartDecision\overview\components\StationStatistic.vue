<template>
  <div class="stationstatistic">
    <div v-for="(item, i) in state.statisticResult" :key="i" class="statistic-item">
      <div class="text">
        <span class="name">{{ item.name }}</span>
        <span v-if="item.unit" class="unit">({{ item.unit }})</span>
      </div>
      <div class="count">
        {{ item.value }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { AutoLogin } from '@/api/mapservice';
import { QueryPipeDataService } from '@/api/mapservice/pipe';
import { GetStationTypeCount } from '@/api/shuiwureports/zhandian';
import { useGisStore } from '@/store';
import { toCommaNumber } from '@/utils/GlobalHelper';
import { EStatisticField, staticPipe } from '@/utils/MapHelper';
import { initStationType } from '@/views/basicDataManage/stationManage/data';

const props = defineProps<{
  data?: { value: string; name: string; unit: string }[];
  projectId?: string;
}>();
const state = reactive<{
  statisticResult: { value: string; name: string; unit: string }[];
}>({
  statisticResult: props.data || [
    { name: '管线长度', unit: '千米', value: toCommaNumber(0) },
    { name: '设备总数', unit: '个', value: toCommaNumber(0) },
    { name: '流量监测点', unit: '个', value: toCommaNumber(0) },
    { name: '压力监测点', unit: '个', value: toCommaNumber(0) },
    { name: '水质监测点', unit: '个', value: toCommaNumber(0) },
    { name: '泵房', unit: '个', value: toCommaNumber(0) }
  ]
});
const refreshData = async () => {
  state.statisticResult.length = 0;
  refreshPipeLength();
  refreshStationCount();
};
const refreshStationCount = async () => {
  try {
    const types = initStationType()
      .map((item) => item.label)
      ?.join(',');
    const res = await GetStationTypeCount({
      types,
      projectId: props.projectId
    });
    const stationArr = res.data?.data;
    const newArr =
      stationArr
        ?.filter((item) => !!item.count)
        .map((item) => {
          return {
            name: item.key,
            value: toCommaNumber(item.count),
            unit: '个'
          };
        }) || [];
    const total = stationArr.reduce((prev, cur) => {
      return cur.count + prev;
    }, 0);
    state.statisticResult.push({
      name: '设备总数',
      value: toCommaNumber(total),
      unit: '个'
    });
    state.statisticResult.push(...newArr);
  } catch (error) {
    //
  }
};
const refreshPipeLength = async () => {
  try {
    const layerInfos = await QueryPipeDataService();
    const lengthResult = await staticPipe('length', {
      layerIds: layerInfos.data.layers?.filter((item) => item.name === '管网管线')?.map((item) => item.id)
    });
    const pipeLength = lengthResult.reduce((prev, item) => {
      return item.rows[0][EStatisticField.ShapeLen] + prev;
    }, 0);
    state.statisticResult.unshift({
      name: '管网管线',
      value: toCommaNumber((pipeLength / 1000).toFixed(2)),
      unit: '千米'
    });
  } catch (error) {
    //
  }
};
const gisStore = useGisStore()
onMounted(async () => {
  if (!gisStore.gToken) {
    const res = await AutoLogin();
    if (res.data?.code === 10000) {
      gisStore.SET_gToken(res.data?.result?.token);
      gisStore.SET_gUserInfo(res.data?.result);
    }
  }
  refreshData();
});
</script>
<style lang="scss" scoped>
.stationstatistic {
  font-size: 12px;
  .statistic-item {
    height: 60px;
    .text {
      position: relative;
      padding-left: 20px;
      &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 12px;
        background: linear-gradient(#37dbff, #373fff) no-repeat;
        border-radius: 6px;
        left: 0;
        top: 0;
      }
    }

    &:nth-child(1) {
      .count {
        color: rgba(255, 210, 50, 1);
        font-size: 26px;
        font-family: font-lcd;
        margin-right: 8px;
        @supports (-webkit-background-clip: text) or (background-clip: text) {
          background: linear-gradient(to right, rgba(255, 210, 50, 1), rgba(255, 247, 50, 1));
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
      }
    }
    &:nth-child(2) {
      .count {
        color: #32c1ff;
        font-size: 26px;
        font-family: font-lcd;
        margin-right: 8px;
        font-size: 32px;
        @supports (-webkit-background-clip: text) or (background-clip: text) {
          background: linear-gradient(to right, #32c1ff, #32fff3);

          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
      }
    }
    .count {
      color: rgba(255, 255, 255, 1);
      font-size: 26px;
      font-family: font-lcd;
      margin-right: 8px;
      font-size: 32px;
      @supports (-webkit-background-clip: text) or (background-clip: text) {
        background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }
    }
  }
}
</style>
