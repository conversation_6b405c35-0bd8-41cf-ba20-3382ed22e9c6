package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalActivity;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalActivitySaveRequest extends SaveRequest<SsPortalActivity> {
    // 标题
    @NotNullOrEmpty
    private String title;

    // 封面
    private String image;

    // 封面
    private String content;

    // 链接
    private String link;


    @Override
    protected SsPortalActivity build() {
        SsPortalActivity entity = new SsPortalActivity();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalActivity update(String id) {
        SsPortalActivity entity = new SsPortalActivity();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalActivity entity) {
        entity.setTitle(title);
        entity.setImage(image);
        entity.setContent(content);
        entity.setLink(link);
        entity.setActive(false);
    }

}