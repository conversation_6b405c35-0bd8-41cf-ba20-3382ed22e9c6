/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.maintain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.MaintainId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.maintain.MaintainRecord;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.dao.Dao;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.maintain.MaintainRecordDao;
import org.thingsboard.server.dao.model.sql.MaintainEntity;
import org.thingsboard.server.dao.model.sql.MaintainRecordEntity;
import org.thingsboard.server.dao.model.sql.RepairEntity;
import org.thingsboard.server.dao.repair.RepairDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.sql.repair.RepairRepository;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
@Slf4j
public class JpaMaintainRecordDao extends JpaAbstractDao<MaintainRecordEntity, MaintainRecord> implements MaintainRecordDao {

    @Autowired
    private MaintainRecordRepository maintainRecordRepository;


    @Override
    protected Class<MaintainRecordEntity> getEntityClass() {
        return MaintainRecordEntity.class;
    }

    @Override
    protected CrudRepository<MaintainRecordEntity, String> getCrudRepository() {
        return maintainRecordRepository;
    }


    @Override
    public List<MaintainRecord> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(maintainRecordRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public List<MaintainRecord> findByStatus(MaintainId maintainId, String status) {
        return DaoUtil.convertDataList(maintainRecordRepository.findByMaintainIdAndStatus(UUIDConverter.fromTimeUUID(maintainId.getId()), status));
    }

    @Override
    public List<MaintainRecord> findByMaintainId(MaintainId maintainId) {
        return DaoUtil.convertDataList(maintainRecordRepository.findByMaintainId(UUIDConverter.fromTimeUUID(maintainId.getId())));
    }

    @Override
    public List<MaintainRecord> findByDeviceId(DeviceId deviceId) {
        return DaoUtil.convertDataList(maintainRecordRepository.findByDeviceId(UUIDConverter.fromTimeUUID(deviceId.getId())));
    }

    @Override
    public List<MaintainRecord> findByTenantIdAndStatus(TenantId tenantId, String status) {
        return DaoUtil.convertDataList(maintainRecordRepository.findByTenantIdAndStatus(UUIDConverter.fromTimeUUID(tenantId.getId()),status));
    }
}
