package org.thingsboard.server.dao.alarmV2;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.DTO.AlarmRuleSmartSaveDTO;
import org.thingsboard.server.dao.model.DTO.AlarmRuleStationParamDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleSmartRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleUser;
import org.thingsboard.server.dao.sql.alarmV2.AlarmRuleSmartMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmRuleSmartServiceImpl implements AlarmRuleSmartService {

    @Autowired
    private AlarmRuleSmartMapper alarmRuleSmartMapper;

    @Autowired
    private AlarmRuleUserService alarmRuleUserService;

    @Override
    public IstarResponse save(AlarmRuleSmartSaveDTO entity, String tenantId) {
        if (entity.getAlarmRuleSmart() == null) {
            return IstarResponse.error("参数错误");
        }
        if (entity.getStationParamList().size() == 0) {
            return IstarResponse.error("请选择站点");
        }
        List<AlarmRuleSmart> saveList = new ArrayList<>();
        if (entity.getAlarmRuleSmart().getEnabled() == null) {
            entity.getAlarmRuleSmart().setEnabled("1");
        }
        AlarmRuleSmart alarmRuleSmart;
        for (AlarmRuleStationParamDTO alarmRuleStationParamDTO : entity.getStationParamList()) {
            if (StringUtils.isBlank(entity.getAlarmRuleSmart().getAttr())) {
                entity.getAlarmRuleSmart().setAttr(alarmRuleStationParamDTO.getAttr());
            }
            alarmRuleSmart = new AlarmRuleSmart();
            BeanUtils.copyProperties(entity.getAlarmRuleSmart(), alarmRuleSmart);
            alarmRuleSmart.setStationId(alarmRuleStationParamDTO.getStationId());
            alarmRuleSmart.setDeviceId(alarmRuleStationParamDTO.getDeviceId());
            alarmRuleSmart.setAttr(alarmRuleStationParamDTO.getAttr());
            alarmRuleSmart.setDataNum(alarmRuleStationParamDTO.getDataNum());
            alarmRuleSmart.setMaxValue(alarmRuleStationParamDTO.getMaxValue());
            alarmRuleSmart.setMinValue(alarmRuleStationParamDTO.getMinValue());
            alarmRuleSmart.setUpLimitValue(alarmRuleStationParamDTO.getUpLimitValue());
            alarmRuleSmart.setDownLimitValue(alarmRuleStationParamDTO.getDownLimitValue());
            alarmRuleSmart.setType(alarmRuleStationParamDTO.getType());
            alarmRuleSmart.setCreateTime(new Date());
            saveList.add(alarmRuleSmart);
        }
        List<String> StationIdList = new ArrayList<>();
        List<String> deviceIdList = new ArrayList<>();
        for (AlarmRuleSmart deleteSmart : saveList) {
            StationIdList.add(deleteSmart.getStationId());
            deviceIdList.add(deleteSmart.getDeviceId());
        }

        // 先删除
        QueryWrapper<AlarmRuleSmart> deleteWrapper = new QueryWrapper();
        deleteWrapper.in("station_id", StationIdList);
        deleteWrapper.in("device_id", deviceIdList);
        deleteWrapper.eq("attr", entity.getAlarmRuleSmart().getAttr());
        List<AlarmRuleSmart> list = alarmRuleSmartMapper.selectList(deleteWrapper);
        List<String> idList = list.stream().map(AlarmRuleSmart::getId).collect(Collectors.toList());
        if (idList.size() > 0) {
            alarmRuleUserService.deleteByRuleIdList(idList);
            alarmRuleSmartMapper.delete(deleteWrapper);
        }

        // 保存
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (AlarmRuleSmart ruleSmart : saveList) {
            executorService.execute(() -> {
                ruleSmart.setId(null);
                alarmRuleSmartMapper.insert(ruleSmart);
                alarmRuleUserService.save(ruleSmart.getId(), JSONObject.parseArray(JSONObject.toJSONString(entity.getMsgList()), AlarmRuleUser.class), JSONObject.parseArray(JSONObject.toJSONString(entity.getAppList()), AlarmRuleUser.class));
            });
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }

        return IstarResponse.ok(entity);
    }

    @Override
    public IstarResponse findList(AlarmRuleSmartRequest request, String tenantId) {
        List<String> stationIdList = request.getStationIdList();
        if (request.getStationIdList() == null || request.getStationIdList().size() == 0) {
            return IstarResponse.error("站点列表不能为空");
        }
        String attr = request.getAttr();
        if (attr == null || attr.equals("")) {
            return IstarResponse.error("属性不能为空");
        }
        List<AlarmRuleSmart> list = alarmRuleSmartMapper.getList(stationIdList, Collections.singletonList(attr));
        if (list == null || list.size() == 0) {
            return IstarResponse.ok(new AlarmRuleSmartSaveDTO());
        }
        AlarmRuleSmartSaveDTO alarmRuleSmartSaveDTO = new AlarmRuleSmartSaveDTO();
        alarmRuleSmartSaveDTO.setAlarmRuleSmart(list.get(0));
        // 参数列表
        AlarmRuleStationParamDTO alarmRuleStationParamDTO;
        for (AlarmRuleSmart alarmRuleSmart : list) {
            alarmRuleStationParamDTO = new AlarmRuleStationParamDTO();
            alarmRuleStationParamDTO.setAttr(alarmRuleSmart.getAttr());
            alarmRuleStationParamDTO.setDataNum(alarmRuleSmart.getDataNum());
            alarmRuleStationParamDTO.setDeviceId(alarmRuleSmart.getDeviceId());
            alarmRuleStationParamDTO.setDeviceName(alarmRuleSmart.getDeviceName());
            alarmRuleStationParamDTO.setStationId(alarmRuleSmart.getStationId());
            alarmRuleStationParamDTO.setStationName(alarmRuleSmart.getStationName());
            alarmRuleStationParamDTO.setType(alarmRuleSmart.getType());
            alarmRuleStationParamDTO.setMaxValue(alarmRuleSmart.getMaxValue());
            alarmRuleStationParamDTO.setMinValue(alarmRuleSmart.getMinValue());
            alarmRuleStationParamDTO.setUpLimitValue(alarmRuleSmart.getUpLimitValue());
            alarmRuleStationParamDTO.setDownLimitValue(alarmRuleSmart.getDownLimitValue());

            alarmRuleSmartSaveDTO.getStationParamList().add(alarmRuleStationParamDTO);
        }

        // 用户列表
        List<AlarmRuleUser> alarmRuleUsers = alarmRuleUserService.findByRuleIdList(Collections.singletonList(list.get(0).getId()));
        for (AlarmRuleUser alarmRuleUser : alarmRuleUsers) {
            if ("1".equals(alarmRuleUser.getType())) {
                alarmRuleSmartSaveDTO.getMsgList().add(alarmRuleUser);
                continue;
            }
            alarmRuleSmartSaveDTO.getAppList().add(alarmRuleUser);
        }
        return IstarResponse.ok(alarmRuleSmartSaveDTO);
    }

    @Override
    public IstarResponse findListV2(AlarmRuleSmartRequest request, String tenantId) {
        List<String> stationIdList = request.getStationIdList();
        if (request.getStationIdList() == null || request.getStationIdList().size() == 0) {
            return IstarResponse.error("站点列表不能为空");
        }
        String attr = request.getAttr();
        if (attr == null || attr.equals("")) {
            return IstarResponse.error("属性不能为空");
        }
        List<AlarmRuleSmart> list = alarmRuleSmartMapper.getList(stationIdList, Collections.singletonList(attr));
        if (list == null || list.size() == 0) {
            return IstarResponse.ok(new ArrayList<>());
        }

        // 用户列表
        List<String> ruleIdList = list.stream().map(AlarmRuleSmart::getId).collect(Collectors.toList());
        List<AlarmRuleUser> alarmRuleUsers = alarmRuleUserService.findByRuleIdList(ruleIdList);
        Map<String, List<AlarmRuleUser>> alarmRuleUserMap = alarmRuleUsers.stream().collect(Collectors.groupingBy(AlarmRuleUser::getMainId));

        for (AlarmRuleSmart alarmRuleSmart : list) {
            if (alarmRuleUserMap.containsKey(alarmRuleSmart.getId())) {
                List<AlarmRuleUser> alarmRuleUsers1 = alarmRuleUserMap.get(alarmRuleSmart.getId());
                for (AlarmRuleUser alarmRuleUser : alarmRuleUsers1) {
                    if ("1".equals(alarmRuleUser.getType())) {
                        alarmRuleSmart.getMsgList().add(alarmRuleUser);
                        continue;
                    }
                    alarmRuleSmart.getAppList().add(alarmRuleUser);
                }
            }
        }

        return IstarResponse.ok(list);
    }

    @Override
    public List<AlarmRuleSmart> findByStationAttrList(List<String> stationIdList, List<String> attrList) {
        return alarmRuleSmartMapper.getList(stationIdList, attrList);
    }

    @Override
    public IstarResponse delete(List<String> idList) {
        alarmRuleSmartMapper.deleteBatchIds(idList);
        alarmRuleUserService.deleteByRuleIdList(idList);

        return IstarResponse.ok();
    }
}
