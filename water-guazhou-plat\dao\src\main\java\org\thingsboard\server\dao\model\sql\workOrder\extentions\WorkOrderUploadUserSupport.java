package org.thingsboard.server.dao.model.sql.workOrder.extentions;

import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.model.JdbcHelper;

public interface WorkOrderUploadUserSupport {
    default void customizeMap(ResponseMap map, JdbcHelper jdbc) {
        boolean isOutside = true;
        String username = null;
        try {
            UUIDConverter.fromString(getUploadUserId());
            username = jdbc.resolveUsername(getUploadUserId());
            isOutside = username == null;
        } catch (Exception ignore) {
        }
        map.put("isOutside", isOutside);
        map.put("uploadUserName", isOutside ? getUploadUserId() : username);
    }

    String getUploadUserId();

}
