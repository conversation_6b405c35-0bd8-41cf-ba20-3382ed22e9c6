<!-- 企业详情 -->
<template>
  <DrawerBox
    ref="refDrawer"
    :right-drawer-absolute="true"
    :right-drawer-width="600"
    :right-drawer="true"
    :right-drawer-title="state.drawerTitle"
    :right-drawer-bar-hide="true"
    :right-drawer-modal="true"
  >
    <SLCard class="card-box">
      <div
        class="apps"
        :class="{ isDark: useAppStore().isDark }"
      >
        <div class="manage-title">
          <span>应用管理</span>
        </div>
        <div class="manage">
          <el-scrollbar>
            <div class="cs">
              <div class="cards">
                <el-row :gutter="30">
                  <el-col
                    :xs="24"
                    :sm="12"
                    :md="8"
                    :lg="8"
                    :xl="6"
                  >
                    <!-- 添加应用 -->
                    <div
                      class="cardinput"
                      style="cursor: pointer"
                      @click="() => handleAdd()"
                    >
                      <div class="icon">
                        <img src="./img/input.png" />
                      </div>
                      <div class="text">
                        添加应用
                      </div>
                    </div>
                  </el-col>
                  <el-col
                    v-for="item in state.apps"
                    :key="item.id"
                    :xs="24"
                    :sm="12"
                    :md="8"
                    :lg="8"
                    :xl="6"
                  >
                    <div class="card">
                      <div class="title">
                        <span class="title-text">{{ item.name }}</span>
                        <div class="title-buttom">
                          <el-dropdown trigger="click">
                            <span class="el-dropdown-link">
                              <!-- <img src="./img/icon.png" /> -->
                              <Icon icon="ep:more"></Icon>
                            </span>
                            <template #dropdown>
                              <el-dropdown-menu>
                                <el-dropdown-item @click="() => handleAdd(item)">
                                  编辑
                                </el-dropdown-item>
                                <el-dropdown-item @click="() => handleDelete(item)">
                                  删除
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </template>
                          </el-dropdown>
                        </div>
                      </div>
                      <div class="introduce">
                        <span>{{ item.remark }}</span>
                      </div>
                      <div class="application-img">
                        <span>应用图片：</span>
                        <div class="icon">
                          <img :src="item.img" />
                        </div>
                      </div>
                      <div class="application-icon">
                        <span>应用图标：</span>
                        <Icon
                          style="width: 25px; height: 25px"
                          :icon="item.icon"
                        ></Icon>
                      </div>
                      <div class="type">
                        <span>应用类型：{{ getapplicationType(item.type) }}</span>
                      </div>
                      <div class="update-time">
                        <span>创建时间：{{ formatDate(item.createTime) }}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </SLCard>
    <template #right>
      <div class="drawer-content">
        <div class="content overlay-y">
          <Form
            ref="refForm"
            :config="FormConfig"
          ></Form>
        </div>
        <div class="footer">
          <el-button
            type="default"
            @click="handleCancel"
          >
            取 消
          </el-button>
          <el-button
            type="primary"
            :loading="FormConfig.submitting"
            @click="handleSubmit"
          >
            确 定
          </el-button>
        </div>
      </div>
    </template>
  </DrawerBox>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'
import { getapplications, deleteapplications, getChecklist, addeditapp } from '@/api/portal'
import { removeSlash } from '@/utils/removeIdSlash'
import { useAppStore, useUserStore } from '@/store'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { formatTree } from '@/utils/GlobalHelper'
import { GetMenuTree } from '@/api/menu/source'
import { formatDate } from '@/utils/DateFormatter'

const refDrawer = ref<InstanceType<typeof DrawerBox>>()
const refForm = ref<IFormIns>()
const state = reactive<{
  apps: any[]
  menus: NormalOption[]
  drawerTitle: string
}>({
  apps: [],
  menus: [],
  drawerTitle: '添加'
})
const initFormGroup = (type: '1' | '2' | '3'): IFormFieldGroup[] => {
  const typerows: IFormItem[] = type === '2'
    ? [{ type: 'input', label: '应用链接', field: 'applicationUrl' }]
    : type === '3'
      ? [
        {
          type: 'select-tree',
          label: '应用菜单',
          defaultExpandAll: true,
          // style: {
          //   height: '500px'
          // },
          field: 'menuIdList',
          showCheckbox: false,
          // checkStrictly: true,
          options: state.menus
        }
      ]
      : [
        {
          type: 'tree',
          label: '应用菜单',
          // style: {
          //   height: '500px'
          // },
          field: 'menuIdList',
          showCheckbox: true,
          // checkStrictly: true,
          options: state.menus
        }
      ]
  return [
    {
      fields: [
        {
          type: 'input',
          label: '应用名称',
          field: 'name',
          rules: [{ required: true, message: '请输入应用名称' }]
        },
        {
          type: 'radio-button',
          label: '应用来源',
          field: 'resourceType',
          options: [
            { label: 'APP', value: 'APP' },
            { label: 'PC', value: 'PC' }
          ]
        },
        { type: 'textarea', label: '应用介绍', field: 'remark' },
        { type: 'input-number', label: '应用序号', field: 'orderNum' },
        { type: 'avatar', label: '应用图片', field: 'img' },
        { type: 'input', label: '应用图标', field: 'icon' },
        {
          type: 'radio-button',
          label: '应用类型',
          field: 'type',
          options: [
            { label: '基础应用', value: '1' },
            { label: '直接跳转', value: '3' },
            { label: '新标签打开', value: '2' }
          ],
          onChange: (type: any) => {
            // handleAppTypeChange(val)
            const formData = refForm.value?.dataForm
            if (!formData) return
            formData.menuIdList = type === '1' ? menusIds : type === '3' ? menusIds.findLast(() => true) : []
            FormConfig.group = initFormGroup(type)
          }
        },
        ...typerows
      ]
    }
  ]
}
const FormConfig = reactive<IFormConfig>({
  group: initFormGroup('1'),
  submit: async (params: any) => {
    if (!useUserStore().user?.tenantId?.id) {
      SLMessage.error('授权失败')
      return
    }
    console.log(params)
    FormConfig.submitting = true
    try {
      const result = {
        ...(params || {}),
        menuIdList: params.menuIdList.length
          ? typeof params.menuIdList === 'string'
            ? [params.menuIdList]
            : params.menuIdList
          : [],
        tenantId: params.tenantId || removeSlash(useUserStore().user?.tenantId?.id)
      }
      const res = await addeditapp(result)
      console.log(res)
      refDrawer.value?.toggleDrawer('rtl', false)
      // refForm.value?.closeDialog()
      SLMessage.success('操作成功')
      FormConfig.submitting = false
      getapplication()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  },
  defaultValue: {
    resourceType: 'PC'
  }
})
const handleCancel = () => {
  refDrawer.value?.toggleDrawer('rtl', false)
}
const handleSubmit = () => {
  refForm.value?.Submit()
}
let menusIds: any[] = []
/**
 * 处理当点击应用类型时的应用菜单默认选中值，因为基础应用是多选，直接跳转是单选，共用的同一字段，所以需要特殊处理一下
 * @param type 应用类型
 */
// const handleAppTypeChange = (type: '1'|'2'|'3') => {
//   const formData = refForm.value?.dataForm
//   if (!formData) return
//   formData.menuIdList = type === '1' ? menusIds : type === '2' ? menusIds.findLast(() => true) : []
//   FormConfig.group = initFormGroup(type)
// }
const handleAdd = async (row?: any) => {
  console.log(row)
  menusIds = []
  try {
    if (row) {
      const res = await getChecklist(row.id)
      menusIds = res.data || []
    }
  } catch (error) {
    console.log('查询选中列表失败')
  }

  state.drawerTitle = row ? '编辑' : '添加'
  const type = row?.type || '1'
  FormConfig.defaultValue = {
    type,
    resourceType: 'PC',
    ...(row || {}),
    menuIdList: type === '1' ? menusIds : type === '3' ? menusIds.findLast(() => true) : []
  }

  // const formData = refForm.value?.dataForm
  // if (!formData) return
  // formData.menuIdList = type === '1' ? menusIds : type === '2' ? menusIds.findLast(() => true) : []
  FormConfig.group = initFormGroup(type)
  // handleAppTypeChange(type)
  // FormConfig.group = initFormGroup(type)
  refDrawer.value?.toggleDrawer('rtl', true)
  // refForm.value?.openDialog()
  refForm.value?.resetForm()
}
const handleDelete = (row?: any) => {
  if (!row?.id) return
  SLConfirm('确认要删除该应用？', '删除提示')
    .then(async () => {
      try {
        const res = await deleteapplications(row.id.split(','))
        console.log(res)

        SLMessage.success('操作成功')
        getapplication()
      } catch (error) {
        SLMessage.error('操作失败')
      }
    })
    .catch(() => {
      //
    })
}

const getapplication = async () => {
  const tenantId = useUserStore().user?.tenantId?.id
  if (!tenantId) return
  try {
    const tenantid = removeSlash(tenantId)
    const res = await getapplications(tenantid, 'ALL')
    state.apps = res.data || []
  } catch (error) {
    console.log('查询应用列表失败')
  }
}

// 返回应用类型
const getapplicationType = key => {
  switch (key) {
    case '1':
      return '基本类型'
    case '2':
      return '新标签打开'
    case '3':
      return '直接跳转'
    default:
      break
  }
}

onMounted(async () => {
  getapplication()
  try {
    const res = await GetMenuTree()
    state.menus = formatTree(res.data || [], {
      id: 'id',
      value: 'id',
      label: 'label',
      children: 'children'
    })
  } catch (error) {
    console.log('查询菜单树失败')
  }
})
</script>

<style lang="scss" scoped>
.card-box {
  height: 100%;
}

.apps {
  padding: 15px;
  height: calc(100% - 30px);
  position: relative;

  &.isDark {
    .manage-title {
      background: linear-gradient(180deg, #2d5181 0%, #28305b 100%);
    }

    .manage {
      .cs {
        .cards {

          .cardinput,
          .card {
            box-shadow: none;
            background-color: #2e395a;
          }
        }
      }
    }
  }

  .manage-title {
    position: absolute;
    z-index: 100;
    left: 50%;
    top: 35px;
    transform: translate(-50%, -50%);
    width: 160px;
    height: 40px;
    background: linear-gradient(180deg, #e9f2ff 0%, #9ec7f1 100%);
    border-radius: 6px;
    border: 1px solid #3f9cec;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
    }
  }

  .manage ::-webkit-scrollbar {
    display: none;
  }

  .manage {
    width: 100%;
    height: 100%;
    margin-top: 20px;
    overflow: hidden;
    border: 1px solid #1e72ee;
    // z-index: -50;

    .cs {
      width: 100%;
      padding: 40px 20px 0;
      overflow: auto;

      .cards {
        .cardinput {
          width: 100%;
          height: 300px;
          // background: #e1e1e1;
          box-shadow: 0 0 1px 1px #e1e1e1;
          border-radius: 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          padding: 60px 1px;

          &:hover {
            box-shadow: 0 0 10px 0 rgba(69, 178, 255, 0.3);
            border: 1px solid #45b2ff;
          }

          .icon {
            width: 44px;
            height: 44px;
          }

          .text {
            width: 64px;
            height: 22px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #43acf7;
            line-height: 22px;
          }
        }

        .card {
          flex-grow: 1;
          width: 100%;
          height: 300px;
          // background: #e1e1e1;
          box-shadow: 0 0 1px 1px #e1e1e1;
          border-radius: 4px;
          margin-bottom: 30px;
          padding: 16px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-start;

          &:hover {
            box-shadow: 0 0 10px 0 rgba(69, 178, 255, 0.3);
            border: 1px solid #45b2ff;
            padding: 15px;
          }

          &:hover .title .title-buttom {
            display: initial;
            cursor: pointer;
          }

          .title {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-text {
              font-size: 16px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #43acf7;
              line-height: 22px;
              display: inline-block;

              .css {
                color: #ffffff;
              }
            }

            .title-buttom {
              display: none;
            }
          }

          .introduce {
            width: 100%;
            height: 80px;
            overflow: hidden;
            text-overflow: ellipsis;

            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #c1c3d9;
              line-height: 20px;
            }
          }

          .application-img,
          .application-icon {
            display: flex;

            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #c1c3d9;
              line-height: 20px;
            }

            .icon {
              width: 72px;
              height: 72px;
              box-shadow: 0 0 1px rgba(125, 125, 125, 1);
              padding: 7px;

              img {
                width: 100%;
              }
            }
          }

          .type {
            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #c1c3d9;
              line-height: 20px;
            }
          }

          .update-time {
            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #c1c3d9;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
}

.drawer-content {
  height: 100%;
  padding-top: 12px;

  .content {
    height: calc(100% - 60px);
    padding-right: 15px;
  }

  .footer {
    display: flex;
    height: 60px;
    padding: 8px;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
