const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/GroupLayer-DhhN3FyN.js","static/js/Point-WxyopZva.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/lazyLayerLoader-DbM9sT1W.js"])))=>i.map(i=>d[i]);
import{a3 as O,T as S,R as m}from"./index-r0dFAfgr.js";import{s as T}from"./Point-WxyopZva.js";import{q as L,L as P}from"./pe-B8dP0-Ut.js";import{aJ as _,bw as g,bx as w}from"./MapView-DaoQedLH.js";import{t as d,r as C}from"./fetchService-B3xiPs3_.js";import{a as F}from"./lazyLayerLoader-DbM9sT1W.js";import"./widget-BcWKanF2.js";const J={FeatureLayer:!0,SceneLayer:!0};async function G(r){var t;const l=(t=r.properties)==null?void 0:t.customParameters,e=await x(r.url,l),a={...r.properties,url:r.url};if(!e.sublayerIds)return e.layerOrTableId!=null&&(a.layerId=e.layerOrTableId,a.sourceJSON=e.sourceJSON),new e.Constructor(a);const s=new(await O(async()=>{const{default:o}=await import("./GroupLayer-DhhN3FyN.js");return{default:o}},__vite__mapDeps([0,1,2,3,4,5,6,7]))).default({title:e.parsedUrl.title});return N(s,e,a),s}function v(r,l){return r?r.find(e=>e.id===l):null}function N(r,l,e){function a(s,t){const o={...e,layerId:s,sublayerTitleMode:"service-name"};return m(t)&&(o.sourceJSON=t),new l.Constructor(o)}l.sublayerIds.forEach(s=>{const t=a(s,v(l.sublayerInfos,s));r.add(t)}),l.tableIds.forEach(s=>{const t=a(s,v(l.tableInfos,s));r.tables.add(t)})}async function x(r,l){var f,p,I,b;let e=_(r);if(S(e)&&(e=await U(r,l)),S(e))throw new T("arcgis-layers:url-mismatch","The url '${url}' is not a valid arcgis resource",{url:r});const{serverType:a,sublayer:s}=e;let t;const o={FeatureServer:"FeatureLayer",StreamServer:"StreamLayer",VectorTileServer:"VectorTileLayer"};switch(a){case"MapServer":s!=null?t="FeatureLayer":t=await M(r,l)?"TileLayer":"MapImageLayer";break;case"ImageServer":{const n=await d(r,{customParameters:l}),{tileInfo:c,cacheType:y}=n;t=c?((f=c==null?void 0:c.format)==null?void 0:f.toUpperCase())!=="LERC"||y&&y.toLowerCase()!=="elevation"?"ImageryTileLayer":"ElevationLayer":"ImageryLayer";break}case"SceneServer":{const n=await d(e.url.path,{customParameters:l});if(t="SceneLayer",n){const c=n==null?void 0:n.layers;if((n==null?void 0:n.layerType)==="Voxel")t="VoxelLayer";else if(c!=null&&c.length){const y=(p=c[0])==null?void 0:p.layerType;y!=null&&w[y]!=null&&(t=w[y])}}break}default:t=o[a]}const u=a==="FeatureServer",i={parsedUrl:e,Constructor:null,layerOrTableId:u?s:void 0,sublayerIds:null,tableIds:null};if(J[t]&&s==null){const n=await V(r,a,l);u&&(i.sublayerInfos=n.layerInfos,i.tableInfos=n.tableInfos),n.layerIds.length+n.tableIds.length!==1?(i.sublayerIds=n.layerIds,i.tableIds=n.tableIds):u&&(i.layerOrTableId=n.layerIds[0]??n.tableIds[0],i.sourceJSON=((I=n.layerInfos)==null?void 0:I[0])??((b=n.tableInfos)==null?void 0:b[0]))}return i.Constructor=await E(t),i}async function U(r,l){var u;const e=await d(r,{customParameters:l});let a=null,s=null;const t=e.type;if(t==="Feature Layer"||t==="Table"?(a="FeatureServer",s=e.id??null):t==="indexedVector"?a="VectorTileServer":e.hasOwnProperty("mapName")?a="MapServer":e.hasOwnProperty("bandCount")&&e.hasOwnProperty("pixelSizeX")?a="ImageServer":e.hasOwnProperty("maxRecordCount")&&e.hasOwnProperty("allowGeometryUpdates")?a="FeatureServer":e.hasOwnProperty("streamUrls")?a="StreamServer":h(e)?(a="SceneServer",s=e.id):e.hasOwnProperty("layers")&&h((u=e.layers)==null?void 0:u[0])&&(a="SceneServer"),!a)return null;const o=s!=null?g(r):null;return{title:m(o)&&e.name||L(r),serverType:a,sublayer:s,url:{path:m(o)?o.serviceUrl:P(r).path}}}function h(r){return r!=null&&r.hasOwnProperty("store")&&r.hasOwnProperty("id")&&typeof r.id=="number"}async function V(r,l,e){let a,s=!1;if(l==="FeatureServer"){const u=await C(r,{customParameters:e});s=!!u.layersJSON,a=u.layersJSON||u.serviceJSON}else a=await d(r,{customParameters:e});const t=a==null?void 0:a.layers,o=a==null?void 0:a.tables;return{layerIds:(t==null?void 0:t.map(u=>u.id).reverse())||[],tableIds:(o==null?void 0:o.map(u=>u.id).reverse())||[],layerInfos:s?t:[],tableInfos:s?o:[]}}async function E(r){return(0,F[r])()}async function M(r,l){return(await d(r,{customParameters:l})).tileInfo}export{G as fromUrl};
