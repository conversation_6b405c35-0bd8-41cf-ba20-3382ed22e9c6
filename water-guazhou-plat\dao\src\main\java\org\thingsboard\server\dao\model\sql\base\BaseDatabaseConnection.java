package org.thingsboard.server.dao.model.sql.base;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-数据库连接对象 base_database_connection
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel(value = "数据库连接对象", description = "平台管理-数据库连接对象实体类")
@Data
public class BaseDatabaseConnection {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    private String name;

    /**
     * 方案描述
     */
    @ApiModelProperty(value = "方案描述")
    private String description;

    /**
     * 数据库初始化SQL脚本
     */
    @ApiModelProperty(value = "数据库初始化SQL脚本")
    private String initScriptDb;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型")
    private String dbType;

    /**
     * 数据库服务器地址
     */
    @ApiModelProperty(value = "数据库服务器地址")
    private String dbHost;

    /**
     * 数据库端口号
     */
    @ApiModelProperty(value = "数据库端口号")
    private String dbPort;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbName;

    /**
     * 数据库用户名
     */
    @ApiModelProperty(value = "数据库用户名")
    private String dbUser;

    /**
     * 数据库密码
     */
    @ApiModelProperty(value = "数据库密码")
    private String dbPwd;
}
