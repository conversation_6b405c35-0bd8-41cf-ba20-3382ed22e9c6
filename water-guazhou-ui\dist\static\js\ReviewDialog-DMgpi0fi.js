import{d as M,c as b,r as G,a8 as H,am as C,o as J,g as d,h as c,F as a,p as s,q as o,G as p,bh as f,an as D,n as K,aJ as z,aB as W,b as i,D as P,bU as Q,bW as X,dz as Y,dA as Z,I as $,aK as ee,aL as te,H as ae,K as se,J as oe,L as le,C as ne}from"./index-r0dFAfgr.js";import{a as re,A as ie,R as de}from"./eventOverview-CiZ_uCa9.js";import{u as pe}from"./useUser-Blb5V02j.js";const ue={class:"review-content"},ve={class:"event-info-section"},me={class:"event-info-content"},ce={class:"info-item"},fe={class:"info-value"},_e={class:"info-item"},ye={class:"info-value"},we={class:"info-item"},ge={class:"info-value"},Te={class:"info-item"},be={class:"info-value"},De={class:"info-item"},Ve={class:"info-value"},Ue={class:"review-section"},Re={class:"dialog-footer"},he=M({__name:"ReviewDialog",props:{modelValue:{type:Boolean},eventData:{}},emits:["update:modelValue","submit"],setup(E,{emit:k}){const l=E,V=k,u=b(!1),w=b(!1),t=G({reviewType:"",handleType:"",rejectReason:"",assignUser:""}),{getUserOptions:j}=pe(),U=b([]),v=H(()=>l.eventData?{id:l.eventData.id||"",title:l.eventData.title||"",type:l.eventData.type||"",typeName:l.eventData.typeName||"",typeCategory:l.eventData.type?[l.eventData.type]:[],address:l.eventData.address||"",remark:l.eventData.remark||"",coordinate:l.eventData.coordinate||"",coordinateName:l.eventData.coordinateName||"",createTime:l.eventData.createTime||"",status:l.eventData.status||"",rejectReason:l.eventData.rejectReason||""}:{typeCategory:[],title:"",type:"",createTime:"",address:"",remark:""});C(()=>l.modelValue,r=>{u.value=r,r&&x()}),C(u,r=>{V("update:modelValue",r)});const x=()=>{t.reviewType="",t.handleType="",t.rejectReason="",t.assignUser=""},N=()=>{t.handleType="",t.rejectReason="",t.assignUser=""},O=()=>{t.assignUser=""},g=()=>{u.value=!1},B=async()=>{if(!t.reviewType){i.warning("请选择审核意见");return}if(t.reviewType==="approve"&&!t.handleType){i.warning("请选择是否办结");return}if(t.reviewType==="approve"&&t.handleType==="transfer"&&!t.assignUser){i.warning("请选择处理人员");return}if(t.reviewType==="reject"&&!t.rejectReason.trim()){i.warning("请输入驳回原因");return}try{w.value=!0,t.reviewType==="approve"?t.handleType==="direct"?(await re(l.eventData.id),i.success("审核通过，事件已办结")):(await ie(l.eventData.id,{assignUserId:t.assignUser}),i.success("审核通过，已转工单")):(await de(l.eventData.id,{reason:t.rejectReason}),i.success("事件已驳回")),g(),V("submit")}catch(r){console.error("审核失败:",r),i.error("审核失败")}finally{w.value=!1}},F=async()=>{try{const r=await j(!1,{authority:"CUSTOMER_USER"});U.value=r.map(e=>({...e,value:P(e.value)}))}catch{}};return J(()=>{F()}),(r,e)=>{const m=Q,T=X,_=Y,R=Z,y=$,S=ee,q=te,I=ae,L=se,h=oe,A=le;return d(),c(A,{modelValue:u.value,"onUpdate:modelValue":e[4]||(e[4]=n=>u.value=n),title:"事件审核",width:"1000px","close-on-click-modal":!1,onClose:g},{footer:a(()=>[s("div",Re,[o(h,{onClick:g},{default:a(()=>e[16]||(e[16]=[p("取消")])),_:1}),o(h,{type:"primary",onClick:B,loading:w.value},{default:a(()=>e[17]||(e[17]=[p(" 提交 ")])),_:1},8,["loading"])])]),default:a(()=>[s("div",ue,[s("div",ve,[e[10]||(e[10]=s("h4",{style:{"margin-bottom":"16px",color:"#303133","font-weight":"600"}},"事件信息",-1)),s("div",me,[o(T,{gutter:20},{default:a(()=>[o(m,{span:12},{default:a(()=>[s("div",ce,[e[5]||(e[5]=s("span",{class:"info-label"},"事件类型：",-1)),s("span",fe,f(v.value.typeName||"-"),1)])]),_:1}),o(m,{span:12},{default:a(()=>[s("div",_e,[e[6]||(e[6]=s("span",{class:"info-label"},"事件名称：",-1)),s("span",ye,f(v.value.title||"-"),1)])]),_:1})]),_:1}),o(T,{gutter:20},{default:a(()=>[o(m,{span:12},{default:a(()=>[s("div",we,[e[7]||(e[7]=s("span",{class:"info-label"},"创建时间：",-1)),s("span",ge,f(v.value.createTime||"-"),1)])]),_:1}),o(m,{span:12},{default:a(()=>[s("div",Te,[e[8]||(e[8]=s("span",{class:"info-label"},"事件地址：",-1)),s("span",be,f(v.value.address||"-"),1)])]),_:1})]),_:1}),o(T,{gutter:20},{default:a(()=>[o(m,{span:24},{default:a(()=>[s("div",De,[e[9]||(e[9]=s("span",{class:"info-label"},"事件描述：",-1)),s("span",Ve,f(v.value.remark||"-"),1)])]),_:1})]),_:1})])]),s("div",Ue,[e[15]||(e[15]=s("h4",{style:{"margin-bottom":"16px",color:"#303133","font-weight":"600"}},"审核意见",-1)),o(L,{model:t,"label-width":"100px",class:"review-form"},{default:a(()=>[o(y,{label:"审核意见",required:""},{default:a(()=>[o(R,{modelValue:t.reviewType,"onUpdate:modelValue":e[0]||(e[0]=n=>t.reviewType=n),onChange:N},{default:a(()=>[o(_,{value:"approve"},{default:a(()=>e[11]||(e[11]=[p("审核通过")])),_:1}),o(_,{value:"reject"},{default:a(()=>e[12]||(e[12]=[p("驳回")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t.reviewType==="approve"?(d(),c(y,{key:0,label:"是否办结",required:""},{default:a(()=>[o(R,{modelValue:t.handleType,"onUpdate:modelValue":e[1]||(e[1]=n=>t.handleType=n),onChange:O},{default:a(()=>[o(_,{value:"direct"},{default:a(()=>e[13]||(e[13]=[p("直接办结")])),_:1}),o(_,{value:"transfer"},{default:a(()=>e[14]||(e[14]=[p("转工单")])),_:1})]),_:1},8,["modelValue"])]),_:1})):D("",!0),t.reviewType==="approve"&&t.handleType==="transfer"?(d(),c(y,{key:1,label:"处理人员",required:""},{default:a(()=>[o(q,{modelValue:t.assignUser,"onUpdate:modelValue":e[2]||(e[2]=n=>t.assignUser=n),placeholder:"请选择处理人员",style:{width:"240px"},filterable:"",clearable:""},{default:a(()=>[(d(!0),K(W,null,z(U.value,n=>(d(),c(S,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):D("",!0),t.reviewType==="reject"?(d(),c(y,{key:2,label:"驳回原因",required:""},{default:a(()=>[o(I,{modelValue:t.rejectReason,"onUpdate:modelValue":e[3]||(e[3]=n=>t.rejectReason=n),type:"textarea",placeholder:"请输入驳回原因",rows:3,style:{width:"400px"},maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})):D("",!0)]),_:1},8,["model"])])])]),_:1},8,["modelValue"])}}}),je=ne(he,[["__scopeId","data-v-fe7c6481"]]);export{je as default};
