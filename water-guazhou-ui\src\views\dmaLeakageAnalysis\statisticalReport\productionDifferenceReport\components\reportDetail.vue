<!-- 产销差报表详情 -->
<template>
  <TreeBox v-loading="!!TreeData.loading">
    <template #tree>
      <SLTree ref="refTree" :tree-data="TreeData"></SLTree>
    </template>
    <Search ref="refSearch" :config="SearchConfig" class="search" />
    <CardTable ref="refTable" class="card-table" :config="TableConfig" />
    <DialogForm ref="refDialog" :config="DialogConfig">
      <Search
        ref="refSearch_Detail"
        :config="DialogSearchConfig"
        class="search"
      />
      <FormTable
        ref="refTable_Detail"
        :config="DialogTableConfig"
        class="form-table"
      ></FormTable>
    </DialogForm>
  </TreeBox>
</template>
<script lang="ts" setup>
import {
  GetDmaPSReportDetail,
  GetDmaPSReportDetailDetail
} from '@/api/mapservice/dma';
import { formatterMonth, formatterYear } from '@/utils/GlobalHelper';
import { printJSON } from '@/utils/printUtils';

const props = defineProps<{ partitions: any[]; tree: NormalOption[] }>();
const refSearch = ref<ISearchIns>();
const refSearch_Detail = ref<ISearchIns>();
const refTable_Detail = ref<IFormTableIns>();
const refDialog = ref<IDialogFormIns>();
const refTable = ref<ICardTableIns>();
// 获取左边树
const TreeData = reactive<SLTreeConfig>({
  data: props.tree || [],
  title: '区域划分',
  showCheckbox: true,
  checkedNodes: [],
  checkedKeys: [],
  handleCheck: (
    ids: string[],
    data: {
      checkedKeys?: string[] | undefined;
      checkedNodes?: Omit<NormalOption, 'children'>[] | undefined;
    }
  ) => {
    console.log(data.checkedNodes, data.checkedKeys);
    TreeData.checkedKeys = data.checkedKeys || [];
    TreeData.checkedNodes = data.checkedNodes || [];
    refreshData();
  }
});
const handleHidden = (params, query, config) => {
  config.hidden = params.type !== config.field;
};
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'year',
    year: moment().format(formatterYear),
    month: [moment().format(formatterMonth), moment().format(formatterMonth)]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按年月', value: 'month' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden,
      type: 'monthrange',
      label: '',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm();
          }
        },
        {
          perm: true,
          text: '打印',
          type: 'default',
          plain: true,
          iconifyIcon: 'ep:printer',
          click: () => {
            printJSON({
              title: `差销差报表详情`,
              data: TableConfig.dataList,
              titleList: TableConfig.columns
            });
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable();
          }
        }
      ]
    }
  ]
});

// 初始化列表配置数据
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'partitionName', label: '分区名称', minWidth: 120 },
    {
      prop: 'correctNrwRate',
      label: '校准产销差',
      unit: '(%)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'supplyTotal',
      label: '供水总量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'correctUseWater',
      label: '校准用水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'userNum',
      label: '挂接用户数',
      unit: '(户)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'inWater',
      label: '进水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'outWater',
      label: '出水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'noIncomeWater',
      label: '无收益水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    }
  ],
  operations: [
    {
      perm: true,
      type: 'default',
      isTextBtn: false,
      plain: true,
      text: '详情',
      iconifyIcon: 'ep:more-filled',
      click: (row: any) => {
        TableConfig.currentRow = row;
        refDialog.value?.openDialog();
        refreshDetail();
      }
    }
  ],
  operationWidth: '150px',
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

// 对话框配置
const DialogConfig = reactive<IDialogFormConfig>({
  dialogWidth: '70%',
  title: '详情',
  group: []
});
const DialogSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'year',
    year: moment().format(formatterYear),
    month: [moment().format(formatterMonth), moment().format(formatterMonth)]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按年月', value: 'month' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden,
      type: 'monthrange',
      label: '',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshDetail()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch_Detail.value?.resetForm();
          }
        },
        {
          perm: true,
          text: '打印',
          type: 'default',
          plain: true,
          iconifyIcon: 'ep:printer',
          click: () => {
            printJSON({
              title: `差销差报表详情`,
              data: DialogTableConfig.dataList,
              titleList: DialogTableConfig.columns
            });
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable_Detail.value?.exportTable();
          }
        }
      ]
    }
  ]
});
// 详情弹框列表
const DialogTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'partitionId', label: '日期' },
    {
      prop: 'partitionName',
      label: '区域名称',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'correctNrwRate',
      label: '参考产销差',
      unit: '(%)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'supplyTotal',
      label: '供水总量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'correctUseWater',
      label: '参考用户抄见量',
      unit: '(m³)',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'userNum',
      label: '挂接用户数',
      unit: '(户)',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'inWater',
      label: '进水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'outWater',
      label: '出水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'noIncomeWater',
      label: '无收益水量',
      unit: '(m³)',
      align: 'center',
      minWidth: 150
    }
  ],
  pagination: {
    hide: true
  }
});

const refreshData = async () => {
  try {
    TableConfig.loading = true;
    const query = refSearch.value?.queryParams || {};
    const res = await GetDmaPSReportDetail({
      type: query.type,
      date: query.type === 'year' ? query.year : undefined,
      start: query.type === 'month' ? query.month?.[0] : undefined,
      end: query.type === 'month' ? query.month?.[1] : undefined,
      partitionIds: TreeData.checkedKeys?.join(',')
    });
    TableConfig.dataList = res.data.data || [];
  } catch (error) {
    //
  }
  TableConfig.loading = false;
};
const refreshDetail = async () => {
  if (!TableConfig.currentRow) return;
  try {
    DialogTableConfig.loading = true;
    const query =
      refSearch_Detail.value?.queryParams ||
      DialogSearchConfig.defaultParams ||
      {};
    const res = await GetDmaPSReportDetailDetail({
      type: query.type,
      date: query.type === 'year' ? query.year : undefined,
      partitionId: TableConfig.currentRow.partitionId,
      start: query.type === 'month' ? query.month?.[0] : undefined,
      end: query.type === 'month' ? query.month?.[1] : undefined
    });
    DialogTableConfig.dataList = res.data.data || [];
  } catch (error) {
    //
  }
  DialogTableConfig.loading = false;
};
watch(
  () => props.tree,
  () => {
    TreeData.data = props.tree || [];
  }
);
watch(
  () => props.partitions,
  () => {
    refreshData();
  }
);
onMounted(async () => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100vh - 260px);
}
.search {
  margin-bottom: 10px;
}
.form-table {
  height: 500px;
}
</style>
