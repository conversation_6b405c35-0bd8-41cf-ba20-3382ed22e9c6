<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceType.ContractMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->c.id,
                           c.purchase_id,
                           c.code,
                           c.title,
                           c."file",
                           c.creator,
                           c.create_time,
                           c.tenant_id
        <!--@sql from contract c -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.purchase.Contract">
        <result column="id" property="id"/>
        <result column="purchase_id" property="purchaseId"/>
        <result column="code" property="code"/>
        <result column="title" property="title"/>
        <result column="file" property="file"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contract c
        <where>
            <if test="code != null">
                and c.code = #{code}
            </if>
            <if test="title != null">
                and c.title like '%' || #{title} || '%'
            </if>
            <if test="fromTime != null">
                and c.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and c.create_time &lt;= #{toTime}
            </if>
            <if test="tenantId != null">
                and c.tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update contract
        <set>
            <if test="purchaseId != null">
                purchase_id = #{purchaseId},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getChildrenId" resultType="java.lang.String">
        select id
        from contract_detail
        where main_id = #{id}
    </select>
</mapper>