import{T as o}from"./index-r0dFAfgr.js";import{e as r}from"./centroid-UTistape.js";import{an as i,ao as s}from"./MapView-DaoQedLH.js";const b={getObjectId:t=>t.objectId,getAttributes:t=>t.attributes,getAttribute:(t,e)=>t.attributes[e],cloneWithGeometry:(t,e)=>new i(e,t.attributes,null,t.objectId),getGeometry:t=>t.geometry,getCentroid:(t,e)=>(o(t.centroid)&&(t.centroid=r(new s,t.geometry,e.hasZ,e.hasM)),t.centroid)};export{b as i};
