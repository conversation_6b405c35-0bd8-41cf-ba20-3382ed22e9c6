package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectSettlement;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectSettlementSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.project.SoProjectSettlementService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/projectSettlement")
public class SoProjectSettlementController extends BaseController {
    @Autowired
    private SoProjectSettlementService service;

    @GetMapping
    public IPage<SoProjectSettlement> findAllConditional(SoProjectSettlementPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoProjectSettlementPageRequest request) {
        return ExcelFileInfo.of("项目总结算列表", findAllConditional(request).getRecords())
                .nextTitle("projectCode", "项目编号")
                .nextTitle("projectName", "项目名称")
                .nextTitle("projectStartTimeName", "启动时间")
                .nextTitle("cost", "总结算金额")
                .nextTitle("acceptTimeName", "总结算时间");
    }

    @PostMapping
    public SoProjectSettlement save(@RequestBody SoProjectSettlementSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoProjectSettlementSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}