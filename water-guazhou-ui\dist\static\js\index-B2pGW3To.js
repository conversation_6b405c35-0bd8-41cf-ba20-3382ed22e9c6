import{_ as T}from"./ArcLayout-CHnHL9Pv.js";import{_ as C}from"./ArcZoomTo.vue_vue_type_script_setup_true_lang-5gUU1gHn.js";import{d as F,W as I,c,r as v,a8 as P,bH as A,bS as G,b as p,S as w,o as D,g as M,h as q,F as x,q as u,i as f,aq as O,C as $}from"./index-r0dFAfgr.js";import{_ as J}from"./Search-NSrhrIa_.js";import{a as N,G as Z}from"./engineeringDocuments-DYprVB7x.js";import{s as B}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{g as E}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{E as S}from"./config-B_00vVdd.js";import{f as U}from"./DateFormatter-Bm9a68Ax.js";import{c as W}from"./geoserverUtils-wjOSMa7E.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";const z=F({__name:"index",setup(H){const d=I(),g=c(),l=c(),s=c(),L=v({filters:[{type:"radio-button",label:"类型",field:"fileType",options:[{label:"全部",value:""},{label:"图片",value:"image"},{label:"视频",value:"video"},{label:"音频",value:"audio"},{label:"文档",value:"file"}]},{type:"select-tree",label:"设备类型",field:"deviceType",options:P(()=>{var e,t;return[{label:"管点类",value:-1,children:(e=d.gLayerOption_Point)==null?void 0:e.map(o=>({...o,value:o.label})),disabled:!0},{label:"管线类",value:-2,children:(t=d.gLayerOption_Line)==null?void 0:t.map(o=>({...o,value:o.label})),disabled:!0}]})},{type:"input",label:"设备编号",field:"deviceCode"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>n()},{perm:!0,text:"重置",iconifyIcon:"ep:refresh",type:"default",click:()=>{var e;return(e=s.value)==null?void 0:e.resetForm()}},{perm:!0,text:"批量删除",iconifyIcon:"ep:delete",type:"danger",click:()=>_()}]}],defaultParams:{}}),i=v({columns:[{label:"文件名称",prop:"name"},{label:"文件类型",prop:"fileType"},{label:"设备类型",prop:"deviceType"},{label:"设备编号",prop:"deviceCode"},{label:"上传时间",prop:"uploadTime",formatter(e,t){return U(t,A)}},{label:"上传人",prop:"uploadUser"},{label:"备注",prop:"remark"},{label:"归档状态",prop:"status",formatter(e,t){return t==="1"?"正常":t==="2"?"已删除":""}}],dataList:[],operationWidth:200,operations:[{perm:!0,text:"定位",iconifyIcon:"ep:location",click:e=>k(e)},{perm:!0,text:"下载",iconifyIcon:"ep:download",click:e=>G(e.file,e.name)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>_(e)}],handleSelectChange(e){i.selectList=e},pagination:{refreshData:({page:e,size:t})=>{i.pagination.page=e||1,i.pagination.limit=t||20,n()}}}),k=e=>{var t,o,r;try{if(e.geo)try{const a=JSON.parse(e.geo);if(!a||!a.type){p.warning("几何信息格式不正确");return}const m=W(a);if(console.log("转换后的几何对象:",m),m){const b=new E({geometry:m,symbol:B(m.type)});(o=(t=l.value)==null?void 0:t.refPanel)==null||o.toggleMaxMin("normal"),(r=g.value)==null||r.gotoFeature(b);return}else p.warning("无法解析几何信息")}catch(a){console.error("解析几何信息失败:",a)}}catch(a){console.error("定位失败:",a),p.error("定位失败")}},_=e=>{var o;const t=e?[e.id]:((o=i.selectList)==null?void 0:o.map(r=>r.id))||[];if(!t.length){p.error("请先选择要删除的数据");return}w("确定删除?","提示信息").then(()=>{N(t,S.已删除).then(r=>{r.data.code===200?(p.success("删除成功"),n()):(p.error("删除失败"),console.log(r.data.message))}).catch(r=>{console.log(r),p.error("删除失败")})}).catch(()=>{})},n=()=>{var t;const e=((t=s.value)==null?void 0:t.queryParams)||{};Z({...e,page:i.pagination.page||1,size:i.pagination.limit||20,status:S.正常}).then(o=>{i.dataList=o.data.data.data||[],i.pagination.total=o.data.data.total||0}).catch(o=>{console.log(o)})};return D(()=>{n()}),(e,t)=>{const o=J,r=O,a=C,m=T;return M(),q(m,{ref_key:"refArcLayout",ref:l,"panel-title":"多媒体管理","hide-panel-close":!0,"panel-max-min":!0,"panel-default-maxmin":"normal","panel-default-visible":!0,onMounted:t[0]||(t[0]=b=>{var y,h;return(h=(y=f(l))==null?void 0:y.refPanel)==null?void 0:h.Toggle(!0)})},{"detail-default":x(()=>[u(o,{ref_key:"refSearch",ref:s,style:{"margin-bottom":"10px"},config:f(L)},null,8,["config"]),u(r,{class:"table-box",config:f(i)},null,8,["config"])]),default:x(()=>[u(a,{ref_key:"refArcZoomTo",ref:g,layerid:"media-manage",layertitle:"多媒体定位点"},null,512)]),_:1},512)}}}),wt=$(z,[["__scopeId","data-v-bb3198a0"]]);export{wt as default};
