package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PipeCollectRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollect;
import org.thingsboard.server.dao.smartPipe.PipeCollectService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

/**
 * 智慧管网-管网采集
 */
@IStarController
@RequestMapping("api/spp/collect")
public class PipeCollectController extends BaseController {

    @Autowired
    private PipeCollectService pipeCollectService;

    @PostMapping
    public IstarResponse save(@RequestBody PipeCollect pipeCollect) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        pipeCollect.setTenantId(tenantId);
        pipeCollect.setCreator(userId);
        return IstarResponse.ok(pipeCollectService.save(pipeCollect));
    }

    @PostMapping("assign")
    public IstarResponse assign(@RequestBody JSONObject params) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        params.put("userId", userId);
        return pipeCollectService.assign(params);
    }

    @PostMapping("receive/{id}")
    public IstarResponse receive(@PathVariable String id) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return pipeCollectService.receive(id, userId);
    }

    @PostMapping("submit")
    public IstarResponse submit(@RequestBody JSONObject params) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        params.put("userId", userId);
        return pipeCollectService.submit(params);
    }

    @PostMapping("review")
    public IstarResponse review(@RequestBody JSONObject params) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        params.put("userId", userId);
        return pipeCollectService.review(params);
    }

    @PostMapping("storage/{id}")
    public IstarResponse storage(@PathVariable String id) throws ThingsboardException {
        return pipeCollectService.storage(id);
    }


    @GetMapping("list")
    public IstarResponse getList(PipeCollectRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(pipeCollectService.getList(request));
    }


    @GetMapping("layerIdList")
    public IstarResponse getLayerIdList(String mainId) {
        return IstarResponse.ok(pipeCollectService.getLayerIdList(mainId));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        pipeCollectService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
