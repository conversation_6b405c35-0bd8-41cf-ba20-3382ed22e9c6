package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionCust;

import java.util.List;

/**
 * 小流量指标设置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-10
 */
@Mapper
public interface PipePartitionCustMapper extends BaseMapper<PipePartitionCust> {

    IPage<PipePartitionCust> getList(IPage<PipePartitionCust> custPage, @Param("param") PartitionCustRequest partitionCustRequest);

    void batchSave(List<PipePartitionCust> pipePartitionCustList);

    void batchRemove(@Param("idList") List<String> idList);

    void batchUpdatePartitionId(@Param("pipePartitionCustList") List<PipePartitionCust> pipePartitionCustList);

}
