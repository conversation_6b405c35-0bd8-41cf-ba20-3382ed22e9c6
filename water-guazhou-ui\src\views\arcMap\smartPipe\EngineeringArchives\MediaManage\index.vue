<template>
  <ArcLayout
    ref="refArcLayout"
    :panel-title="'多媒体管理'"
    :hide-panel-close="true"
    :panel-max-min="true"
    :panel-default-maxmin="'normal'"
    :panel-default-visible="true"
    @mounted="refArcLayout?.refPanel?.Toggle(true)"
  >
    <template #detail-default>
      <Search
        ref="refSearch"
        style="margin-bottom: 10px"
        :config="SearchConfig"
      ></Search>
      <FormTable class="table-box" :config="TableConfig"></FormTable>
    </template>
    <ArcZoomTo
      ref="refArcZoomTo"
      :layerid="'media-manage'"
      :layertitle="'多媒体定位点'"
    ></ArcZoomTo>
  </ArcLayout>
</template>
<script lang="ts" setup>
import {
  GetGisFileList,
  PostGisFileStatus
} from '@/api/mapservice/engineeringDocuments';
import { useGisStore } from '@/store';
import { excuteQuery, initQueryParams, setSymbol } from '@/utils/MapHelper';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { downloadUrl } from '@/utils/fileHelper';
import { EPipeFileStatus } from '../config';
import { formatDate } from '@/utils/DateFormatter';
import { formatterDate } from '@/utils/GlobalHelper';

import Graphic from '@arcgis/core/Graphic';
import { convertGeoJSONToArcGIS } from '@/utils/geoserver/geoserverUtils.js';

const gisStore = useGisStore();
const refArcZoomTo = ref<IArcZoomToIns>();
const refArcLayout = ref<IArcLayoutIns>();
const refSearch = ref<ISearchIns>();
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '类型',
      field: 'fileType',
      options: [
        { label: '全部', value: '' },
        { label: '图片', value: 'image' },
        { label: '视频', value: 'video' },
        { label: '音频', value: 'audio' },
        { label: '文档', value: 'file' }
      ]
    },
    {
      type: 'select-tree',
      label: '设备类型',
      field: 'deviceType',
      options: computed(() => {
        return [
          {
            label: '管点类',
            value: -1,
            children: gisStore.gLayerOption_Point?.map((item) => ({
              ...item,
              value: item.label
            })),
            disabled: true
          },
          {
            label: '管线类',
            value: -2,
            children: gisStore.gLayerOption_Line?.map((item) => ({
              ...item,
              value: item.label
            })),
            disabled: true
          }
        ];
      }) as any
    },
    {
      type: 'input',
      label: '设备编号',
      field: 'deviceCode'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          type: 'default',
          click: () => refSearch.value?.resetForm()
        },
        {
          perm: true,
          text: '批量删除',
          iconifyIcon: 'ep:delete',
          type: 'danger',
          click: () => handleDelete()
        }
      ]
    }
  ],
  defaultParams: {}
});
const TableConfig = reactive<ITable>({
  columns: [
    { label: '文件名称', prop: 'name' },
    { label: '文件类型', prop: 'fileType' },
    { label: '设备类型', prop: 'deviceType' },
    { label: '设备编号', prop: 'deviceCode' },
    {
      label: '上传时间',
      prop: 'uploadTime',
      formatter(row, value) {
        return formatDate(value, formatterDate);
      }
    },
    { label: '上传人', prop: 'uploadUser' },
    { label: '备注', prop: 'remark' },
    {
      label: '归档状态',
      prop: 'status',
      formatter(row, value) {
        return value === '1' ? '正常' : value === '2' ? '已删除' : '';
      }
    }
  ],
  dataList: [],
  operationWidth: 200,
  operations: [
    {
      perm: true,
      text: '定位',
      iconifyIcon: 'ep:location',
      click: (row) => gotoFeature(row)
    },
    {
      perm: true,
      text: '下载',
      iconifyIcon: 'ep:download',
      click: (row) => downloadUrl(row.file, row.name)
    },
    {
      perm: true,
      text: '删除',
      iconifyIcon: 'ep:delete',
      type: 'danger',
      click: (row) => handleDelete(row)
    }
  ],
  handleSelectChange(val) {
    TableConfig.selectList = val;
  },
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1;
      TableConfig.pagination.limit = size || 20;
      refreshData();
    }
  }
});

const gotoFeature = (row: any) => {
  try {
    // 如果有geo字符串，优先使用geo字符串定位
    if (row.geo) {
      try {
        // 解析geo字符串
        const geoJson = JSON.parse(row.geo);
        if (!geoJson || !geoJson.type) {
          SLMessage.warning('几何信息格式不正确');
          return;
        }

        // 使用封装方法创建几何对象
        const geometry = convertGeoJSONToArcGIS(geoJson);
        console.log('转换后的几何对象:', geometry);
        
        if (geometry) {
          const feature = new Graphic({
            geometry,
            symbol: setSymbol(geometry.type)
          });
          // 定位到要素
          refArcLayout.value?.refPanel?.toggleMaxMin('normal');
          refArcZoomTo.value?.gotoFeature(feature);
          return;
        } else {
          SLMessage.warning('无法解析几何信息');
        }
      } catch (error) {
        console.error('解析几何信息失败:', error);
      }
    }
  } catch (error) {
    console.error('定位失败:', error);
    SLMessage.error('定位失败');
  }
};
const handleDelete = (row?: any) => {
  const ids = row
    ? [row.id]
    : TableConfig.selectList?.map((item) => item.id) || [];
  if (!ids.length) {
    SLMessage.error('请先选择要删除的数据');
    return;
  }
  SLConfirm('确定删除?', '提示信息')
    .then(() => {
      PostGisFileStatus(ids, EPipeFileStatus.已删除)
        .then((res) => {
          if (res.data.code === 200) {
            SLMessage.success('删除成功');
            refreshData();
          } else {
            SLMessage.error('删除失败');
            console.log(res.data.message);
          }
        })
        .catch((e) => {
          console.log(e);
          SLMessage.error('删除失败');
        });
    })
    .catch(() => {
      //
    });
};
const refreshData = () => {
  const queryParams = refSearch.value?.queryParams || {};
  GetGisFileList({
    ...queryParams,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    status: EPipeFileStatus.正常
  })
    .then((res) => {
      TableConfig.dataList = res.data.data.data || [];
      TableConfig.pagination.total = res.data.data.total || 0;
    })
    .catch((e) => {
      console.log(e);
    });
};

onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 50px);
}
</style>
