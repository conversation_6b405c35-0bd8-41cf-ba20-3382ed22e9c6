package org.thingsboard.server.controller.production;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.DTO.ExceptionByChangeRatioDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByFlowDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByReverseDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByZeroDTO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.pipeNetwork.PipeNetworkService;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.StationDataUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("api/flowMonitoringStation")
public class FlowMonitoringStationController extends BaseController {

    @Autowired
    private ProductionService productionService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private PipeNetworkService pipeNetworkService;

    @GetMapping("getList")
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String projectId,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam String status) throws ThingsboardException {
        String type = DataConstants.StationType.FLOW_MONITORING.getValue() + "," + DataConstants.StationType.FLOW_FLOW_MONITORING.getValue();
        return IstarResponse.ok(productionService.getList(type, projectId, name, status, getTenantId()));
    }

    /**
     * 查询指定站点的流量监测详情数据
     */
    @GetMapping("gis/getDataDetail")
    public IstarResponse getDataDetail(@RequestParam String stationId) throws ThingsboardException {
        JSONObject waterSupplyDetail = new JSONObject();

        // 压力曲线、瞬时流量曲线
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.PRESSURE.getValue());
        attrList.add(DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, "", attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        // 最后瞬时流量、最后压力
        List<DeviceFullData> dataList = stationDataService.getStationDataDetail(stationId, "", true, getTenantId());
        BigDecimal currentPressure = null;
        BigDecimal currentInstantaneousFlow = null;
        if (dataList != null && dataList.size() > 0) {
            for (DeviceFullData deviceFullData : dataList) {
                if (deviceFullData.getProperty().equals(DataConstants.DeviceAttrType.PRESSURE.getValue())) {
                    currentPressure = new BigDecimal(deviceFullData.getValue());
                }
                if (deviceFullData.getProperty().equals(DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue())) {
                    currentInstantaneousFlow = new BigDecimal(deviceFullData.getValue());
                }
            }
        }
        waterSupplyDetail.put("currentPressure", currentPressure);
        waterSupplyDetail.put("currentInstantaneousFlow", currentInstantaneousFlow);

        return IstarResponse.ok(waterSupplyDetail);
    }

    @GetMapping("flowData")
    public IstarResponse flowData(@RequestParam String stationId, @RequestParam String queryType, @RequestParam String date) {
        try {
            // 查询站点
            StationEntity station = stationFeignClient.get(stationId);
            if (station == null) {
                throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            // 压力曲线、瞬时流量曲线
            List<String> attrList = new ArrayList<>();
            attrList.add(DataConstants.DeviceAttrType.TOTAL_FLOW.getValue());

            Map<String, Date> timeRange = StationDataUtil.getTimeRange(date, queryType);
            // 本期开始时间
            Date startTime = timeRange.get("start");
            // 本期结束时间
            Date endTime = timeRange.get("end");

            switch (queryType) {
                case "day":
                    queryType = DateUtils.HOUR;
                    break;
                case "month":
                    queryType = DateUtils.DAY;
                    break;
                case "year":
                    queryType = DateUtils.MONTH;
                    break;
                default:
                    throw new ThingsboardException("非法的查询类型!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }

            Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                    stationId, "", attrList, queryType, startTime, endTime, getTenantId());

            return IstarResponse.ok(stationDataMap.get(DataConstants.DeviceAttrType.TOTAL_FLOW.getValue()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定站点的水量报表
     */
    @GetMapping("getFlowReport")
    public IstarResponse getFlowReport(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(pipeNetworkService.getFlowReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定站点的起止读数
     */
    @GetMapping("getReadingsBetween")
    public IstarResponse getReadingsBetween(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String stationIds) {
        try {
            return IstarResponse.ok(pipeNetworkService.getReadingsBetween(stationIds, start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }


    /**
     * 查询多个站点的水量报表
     */
    @GetMapping("getFlowDetailReport")
    public IstarResponse getFlowDetailReport(@RequestParam Long start, @RequestParam Long end,
                                                    @RequestParam String stationIdList, @RequestParam String queryType) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            return IstarResponse.ok(pipeNetworkService.getFlowDetailReport(DataConstants.StationType.FLOW_MONITORING.getValue(), Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 流量分析-配表分析
     */
    @GetMapping("getMeterConfigChart")
    public IstarResponse getMeterConfigChart(@RequestParam Long start, @RequestParam Long end,
                                             @RequestParam String stationId) {
        try {
            return IstarResponse.ok(pipeNetworkService.getMeterConfigChart(DataConstants.StationType.FLOW_MONITORING.getValue(), stationId, start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 流量分析-峰谷分析
     */
    @GetMapping("getPeak")
    public IstarResponse getPeak(@RequestParam Long start, @RequestParam Long end,
                                 @RequestParam String stationId) {
        try {
            return IstarResponse.ok(pipeNetworkService.getFlowPeak(DataConstants.StationType.FLOW_MONITORING.getValue(), stationId, start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 流量分析-时段对比
     */
    @GetMapping("getPeriod")
    public IstarResponse getPeriod(@RequestParam Long start, @RequestParam Long end,
                                   @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(pipeNetworkService.getFlowPeriod(DataConstants.StationType.FLOW_MONITORING.getValue(), stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 流量分析-同比曲线
     */
    @GetMapping("getRatio")
    public IstarResponse getRatio(@RequestParam Long start, @RequestParam Long end,
                                  @RequestParam String stationId, @RequestParam String type, @RequestParam(required = false) String attr) {
        try {
            return IstarResponse.ok(pipeNetworkService.getFlowRatio(DataConstants.StationType.FLOW_MONITORING.getValue(), stationId, start, end, type, getTenantId(), attr));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 异常分析-用水突变
     */
    @GetMapping("exception/changeRatio")
    public IstarResponse exceptionChangeRatio(@RequestParam int page, @RequestParam int size,
                                              @RequestParam String type, @RequestParam String queryType,
                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                              @RequestParam(required = false) String attr,
                                              @RequestParam(required = false) Integer range, @RequestParam String stationId) throws ThingsboardException {
        // 查询用水突变
        List<ExceptionByChangeRatioDTO> data = pipeNetworkService.getExceptionChangeRatio(stationId, type, queryType, startTime, endTime, range, attr, getTenantId());

        return IstarResponse.ok(PageData.page(data, page, size));
    }

    /**
     * 异常分析-流量异常
     */

    /**
     * 异常分析-反向流量
     */
    @GetMapping("exception/reverse")
    public IstarResponse exceptionReverse(@RequestParam int page, @RequestParam int size,
                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                          @RequestParam(required = false) Double range, @RequestParam String stationId) throws ThingsboardException {
        List<ExceptionByReverseDTO> data = pipeNetworkService.getExceptionReverseData(stationId, startTime, endTime, range, getTenantId());

        return IstarResponse.ok(PageData.page(data, page, size));
    }


    /**
     * 异常分析-零流量
     */
    @GetMapping("exception/zero")
    public IstarResponse exceptionZero(@RequestParam int page, @RequestParam int size,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                       @RequestParam(required = false) String attr,
                                       @RequestParam(required = false) Integer range, @RequestParam String stationId) throws ThingsboardException {
        List<ExceptionByZeroDTO> data = pipeNetworkService.getExceptionZeroData(stationId, startTime, endTime, range, attr, getTenantId());

        return IstarResponse.ok(PageData.page(data, page, size));
    }


    /**
     * 异常分析-流量异常
     */
    @GetMapping("exception/flow")
    public IstarResponse exceptionFlow(@RequestParam int page, @RequestParam int size,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                       @RequestParam(required = false) Integer divideHour,@RequestParam(required = false) Integer overloadHour, @RequestParam String stationId) throws ThingsboardException {
        List<ExceptionByFlowDTO> data = pipeNetworkService.getExceptionFlowData(stationId, startTime, endTime, divideHour, overloadHour, getTenantId());

        return IstarResponse.ok(PageData.page(data, page, size));
    }

    /**
     * 异常分析-用水突变
     */
    @GetMapping("exception/changeRatio/export")
    public IstarResponse exceptionChangeRatioExport(@RequestParam int page, @RequestParam int size,
                                                    @RequestParam String type, @RequestParam String queryType,
                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                                    @RequestParam(required = false) String attr,
                                                    @RequestParam(required = false) Integer range, @RequestParam String stationId,
                                                    HttpServletResponse response) throws ThingsboardException {
        // 查询用水突变
        List<ExceptionByChangeRatioDTO> data = pipeNetworkService.getExceptionChangeRatio(stationId, type, queryType, startTime, endTime, range, attr, getTenantId());
        // 数据列表
        Map headMap = new LinkedHashMap();
        headMap.put("stationName", "站点名称");
        headMap.put("value", "正瞬时流量(m³/h)");
        headMap.put("compareValue", "参照流量(m³/h)");
        headMap.put("ratio", "突变百分比(%)");
        headMap.put("valueDate", "分析日期");
        headMap.put("compareValueDate", "参照日期");

        // 水质报表
        String title = "异常分析-用水突变";

        JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
        ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

        return IstarResponse.ok();
    }

    /**
     * 异常分析-流量异常
     */

    /**
     * 异常分析-反向流量
     */
    @GetMapping("exception/reverse/export")
    public IstarResponse exceptionReverseExport(@RequestParam int page, @RequestParam int size,
                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                                @RequestParam(required = false) Double range, @RequestParam String stationId,
                                                HttpServletResponse response) throws ThingsboardException {
        List<ExceptionByReverseDTO> data = pipeNetworkService.getExceptionReverseData(stationId, startTime, endTime, range, getTenantId());
        // 数据列表
        Map headMap = new LinkedHashMap();
        headMap.put("stationName", "站点名称");
        headMap.put("value", "反向流量");
        headMap.put("valueDate", "读取日期");

        // 水质报表
        String title = "异常分析-反向流量";

        JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
        ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

        return IstarResponse.ok();
    }


    /**
     * 异常分析-零流量
     */
    @GetMapping("exception/zero/export")
    public IstarResponse exceptionZeroExport(@RequestParam int page, @RequestParam int size,
                                             @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                             @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                             @RequestParam(required = false) String attr,
                                             @RequestParam(required = false) Integer range, @RequestParam String stationId,
                                             HttpServletResponse response) throws ThingsboardException {
        List<ExceptionByZeroDTO> data = pipeNetworkService.getExceptionZeroData(stationId, startTime, endTime, range, attr, getTenantId());
        // 数据列表
        Map headMap = new LinkedHashMap();
        headMap.put("stationName", "站点名称");
        headMap.put("value", "异常总分钟数");
        headMap.put("valueDate", "读取日期");

        // 水质报表
        String title = "异常分析-零流量";

        JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
        ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

        return IstarResponse.ok();
    }

    /**
     * 监测日报
     */
    @GetMapping("dayReport")
    public IstarResponse dayReport(@RequestParam int page, @RequestParam int size,
                                   @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date dayTime,
                                   @RequestParam(required = false) String stationName) throws ThingsboardException {
        List<JSONObject> dataList = pipeNetworkService.getDayReport(stationName, dayTime, getTenantId());
        return IstarResponse.ok(PageData.page(dataList, page, size));
    }

    /**
     * 监测日报
     */
    @GetMapping("dayReport/export")
    public IstarResponse dayReportExport(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date dayTime,
                                         @RequestParam(required = false) String stationName, HttpServletResponse response) throws ThingsboardException {
        List<JSONObject> data = pipeNetworkService.getDayReport(stationName, dayTime, getTenantId());

        // 数据列表
        Map headMap = new LinkedHashMap();
        headMap.put("stationName", "站点名称");
        headMap.put("yesterdayData", "前一天读数");
        headMap.put("todayData", "当天读数");
        headMap.put("useData", "当天用量");
        headMap.put("max", "上限");
        headMap.put("min", "下限");
        headMap.put("time", "读取时间");

        // 水质报表
        String title = "监测日报";

        JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
        ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

        return IstarResponse.ok();
    }
}
