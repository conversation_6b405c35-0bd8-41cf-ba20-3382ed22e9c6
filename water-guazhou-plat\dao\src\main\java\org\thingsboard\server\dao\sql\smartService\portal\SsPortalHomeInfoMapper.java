package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalHomeInfo;

@Mapper
public interface SsPortalHomeInfoMapper extends BaseMapper<SsPortalHomeInfo> {
    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalHomeInfo entity);

    int save(SsPortalHomeInfo entity);

    boolean updateFully(SsPortalHomeInfo entity);

    SsPortalHomeInfo getByTenantId(String tenantId);



}
