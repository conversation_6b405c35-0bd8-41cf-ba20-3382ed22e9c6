package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.MsgConfigEntity;
import org.thingsboard.server.dao.msg.MsgConfigService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

@RestController
@RequestMapping("/api/msg/config")
public class MsgConfigController extends BaseController {

    @Autowired
    private MsgConfigService msgConfigService;

    @PostMapping(value = "save")
    public IstarResponse save(@RequestBody MsgConfigEntity msgConfigEntity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        msgConfigEntity.setTenantId(tenantId);
        return IstarResponse.ok(msgConfigService.save(msgConfigEntity));
    }

    @GetMapping("list")
    public IstarResponse getList(int page, int size, @RequestParam(required = false, defaultValue = "") String signName) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(msgConfigService.getList(page, size, signName, tenantId));
    }


    @DeleteMapping("delete")
    public IstarResponse deleteAll(@RequestBody List<String> ids) {
        boolean delete = msgConfigService.delete(ids);
        if (!delete) {
            return IstarResponse.error("部分签名删除失败，请先删除签名下的模板");
        }
        return IstarResponse.ok();
    }

}
