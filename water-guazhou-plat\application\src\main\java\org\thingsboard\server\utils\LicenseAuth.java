package org.thingsboard.server.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.thingsboard.server.common.data.DataConstants;

import java.io.*;
import java.net.NetworkInterface;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by paul on 2018/7/12.
 */
@Slf4j
public class LicenseAuth {
    private static final int SPLITLENGTH = 4;

    private static final String salt = "AA";

    public static String getMachineCode() throws Exception {

        String machineCode = DataConstants.machineCodeMap.get("code");
        if (StringUtils.isNotBlank(machineCode)) {
            return machineCode;
        }

        Set<String> result = new HashSet<>();
        String mac = getMac();
        result.add(mac);
        Properties props = System.getProperties();
        String javaVersion = props.getProperty("java.version");
        result.add(javaVersion);
        String javaVMVersion = props.getProperty("java.vm.version");
        result.add(javaVMVersion);
        String osVersion = props.getProperty("os.version");
        result.add(osVersion);
        String code = Encrypt.GetMD5Code(result.toString());
        String resultCode = getSplitString(code, "-", 4);
        DataConstants.machineCodeMap.put("code", resultCode);
        return resultCode;

    }

    private static HashMap<String, String> genDataFromArrayByte(byte[] b) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(b)));
        HashMap<String, String> data = new HashMap<String, String>();
        String str = null;
        while ((str = br.readLine()) != null) {
            if (StringUtils.isNotEmpty(str)) {
                str = str.trim();
                int pos = str.indexOf("=");
                if (pos <= 0) continue;
                if (str.length() > pos + 1) {
                    data.put(str.substring(0, pos).trim().toUpperCase(), str.substring(pos + 1).trim());
                } else {
                    data.put(str.substring(0, pos).trim().toUpperCase(), "");
                }
            }
        }
        return data;
    }

    private static String getSplitString(String str) {
        return getSplitString(str, "-", SPLITLENGTH);
    }

    private static String getSplitString(String str, String split, int length) {
        int len = str.length();
        StringBuilder temp = new StringBuilder();
        for (int i = 0; i < len; i++) {
            if (i % length == 0 && i > 0) {
                temp.append(split);
            }
            temp.append(str.charAt(i));
        }
        String[] attrs = temp.toString().split(split);
        StringBuilder finalMachineCode = new StringBuilder();
        for (String attr : attrs) {
            if (attr.length() == length) {
                finalMachineCode.append(attr).append(split);
            }
        }
        String result = finalMachineCode.toString().substring(0,
                finalMachineCode.toString().length() - 1);
        return result;
    }

    private static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    public static String getMac() {
        try {
            Enumeration<NetworkInterface> el = NetworkInterface
                    .getNetworkInterfaces();
            while (el.hasMoreElements()) {
                byte[] mac = el.nextElement().getHardwareAddress();
                if (mac == null)
                    continue;
                String hexStr = bytesToHexString(mac);
                return getSplitString(hexStr, "-", 2).toUpperCase();
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return null;
    }

    public static boolean authLicense(String licensePath, String publicPath) throws Exception {
        boolean isAuth = false;
        File licenseFile = new File(licensePath);
        if (licenseFile.exists()) {
            if (licenseFile.isFile()) {
                // 解析文件获取证书信息
                String licenseContent = FileUtil.readFileToString(licenseFile);
                String licenseDescription = Encrypt.DecriptWithRSA_Pub(licenseContent, publicPath);


                HashMap<String, String> props = genDataFromArrayByte(licenseDescription.getBytes());
                String licenseId = props.get("LICENSEID");// 证书ID
                String licenseName = props.get("LICENSENAME");// 证书名称
                String licenseType = props.get("LICENSETYPE");// 授权类型
                String licenseLimit = props.get("EXPIREDAY");// 授权有效期
                String machineCode = props.get("MACHINECODE");// 机器码
                String licenseSign = props.get("LICENSESIGN");// 签名

                //验证签名
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("LICENSEID=").append(licenseId).append(salt);
                stringBuilder.append("LICENSENAME=").append(licenseName).append(salt);
                stringBuilder.append("LICENSETYPE=").append(licenseType).append(salt);
                stringBuilder.append("EXPIREDAY=").append(licenseLimit).append(salt);
                stringBuilder.append("MACHINECODE=").append(machineCode).append(salt);
                stringBuilder.append(salt);

                String sign = stringBuilder.toString();
                if (licenseSign.equals(Encrypt.GetMD5Code(sign))) {
                    if (getMachineCode().equals(machineCode)) {// 验证机器码
                        // 是否为无期限License
                        if (licenseType.equalsIgnoreCase("y")) {
                            isAuth = true;
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            Date bt = new Date();
                            Date et = sdf.parse(licenseLimit);
                            //验证时间
                            if (bt.compareTo(et) <= 0) {
                                isAuth = true;
                                log.info("License证书验证通过!");
                            } else {
                                log.warn("License证书已过期");
                            }
                        }
                    } else {
                        log.error("非法机器码!");
                    }
                } else {
                    log.error("非法签名!");
                }
            }
        } else {
            System.out.println("未上传证书");
        }
        return isAuth;
    }
}
