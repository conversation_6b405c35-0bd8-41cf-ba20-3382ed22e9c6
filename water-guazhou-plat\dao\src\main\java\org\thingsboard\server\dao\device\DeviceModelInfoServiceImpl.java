package org.thingsboard.server.dao.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity;
import org.thingsboard.server.dao.sql.device.DeviceModelInfoMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-17
 */
@Slf4j
@Service
@Transactional
public class DeviceModelInfoServiceImpl implements DeviceModelInfoService {

    @Autowired
    private DeviceModelInfoMapper deviceModelInfoMapper;



    @Override
    public PageData<DeviceModelInfoEntity> getList(DeviceModelInfoEntity deviceModelInfoEntity, int page, int size) {
        Page<DeviceModelInfoEntity> page1 = new Page<>(page, size);
        IPage<DeviceModelInfoEntity> page2 = deviceModelInfoMapper.getList(page1, deviceModelInfoEntity);
        return new PageData<>(page2.getTotal(), page2.getRecords());
    }

    @Override
    public DeviceModelInfoEntity save(DeviceModelInfoEntity ganInstall) {
        if (StringUtils.isBlank(ganInstall.getId())) {
            ganInstall.setCreateTime(new Date());
            deviceModelInfoMapper.insert(ganInstall);
        } else {
            // 重新申请
            deviceModelInfoMapper.updateById(ganInstall);
        }
        return ganInstall;
    }


    @Override
    public IstarResponse delete(List<String> ids) {
        deviceModelInfoMapper.deleteBatchIds(ids);
        return IstarResponse.ok();
    }

    @Override
    public DeviceModelInfoEntity getByDeviceId(String deviceId) {
        return deviceModelInfoMapper.getByDeviceId(deviceId);
    }
}
