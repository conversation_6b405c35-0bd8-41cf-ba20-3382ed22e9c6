package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesign;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignPageRequest;

@Mapper
public interface SoConstructionDesignMapper extends BaseMapper<SoConstructionDesign> {
    IPage<SoConstructionDesign> findByPage(SoConstructionDesignPageRequest request);

    boolean update(SoConstructionDesign entity);

    boolean updateFully(SoConstructionDesign entity);

    String getConstructionCodeById(String id);

}
