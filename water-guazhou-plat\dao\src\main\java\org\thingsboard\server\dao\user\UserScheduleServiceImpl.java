package org.thingsboard.server.dao.user;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.dao.model.request.UserScheduleListRequest;
import org.thingsboard.server.dao.model.sql.UserSchedule;
import org.thingsboard.server.dao.sql.user.UserScheduleRepository;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class UserScheduleServiceImpl implements UserScheduleService {

    @Autowired
    private UserScheduleRepository userScheduleRepository;

    @Override
    public List<UserSchedule> findList(UserScheduleListRequest request, User currentUser) {
        Date beginTime = request.getBeginTime();
        Date endTime = request.getEndTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (beginTime == null) {
            try {
                beginTime = dateFormat.parse(dateFormat.format(new Date()));
            } catch (ParseException ignored) {
            }
        }
        if (endTime == null) {
            endTime = new Date();
        } else {
            endTime = new Date(endTime.getTime() + (24 * 3600 * 1000));
        }
        return userScheduleRepository.findList(beginTime, endTime,
                UUIDConverter.fromTimeUUID(currentUser.getUuidId()), UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
    }

    @Override
    public void save(UserSchedule entity) {
        userScheduleRepository.save(entity);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            userScheduleRepository.delete(id);
        }
    }
}
