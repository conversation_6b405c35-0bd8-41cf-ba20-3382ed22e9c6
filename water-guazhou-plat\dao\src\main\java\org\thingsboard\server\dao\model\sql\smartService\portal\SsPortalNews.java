package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_news")
public class SsPortalNews {
    // id
    private String id;

    // 标题
    private String title;

    // 摘要
    private String summary;

    // 封面
    private String cover;

    // 内容
    private String content;

    // 是否发布
    private Boolean active;

    // 是否推荐新闻
    private Boolean isRecommend;

    // 是否热点新闻
    private Boolean isHot;

    // 所属目录id
    private String packageId;

    // 所属目录名称
    private String packageName;

    // 附件
    private String attachment;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
