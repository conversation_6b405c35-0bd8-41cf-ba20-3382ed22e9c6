package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.construction.SoConstructionContractService;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoSimpleConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypeSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.Collections;
import java.util.List;

@IStarController2
@RequestMapping("/api/so/constructionContract")
public class SoConstructionContractController extends BaseController {
    @Autowired
    private SoConstructionContractService service;


    @GetMapping
    public IPage<SoConstructionContractContainer> findAllConditional(SoConstructionContractPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/simple")
    public IPage<SoSimpleConstructionContract> findSimple(SoConstructionContractPageRequest request) {
        return service.findSimple(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionContractPageRequest request) throws ThingsboardException {
        if (request.getConstructionCode() == null) {
            ExceptionUtils.silentThrow("所属工程编号未传入");
        }
        List<SoConstructionContractContainer> records = findAllConditional(request).getRecords();
        List<SoConstructionContract> items = null;
        if (records.size() > 0) {
            items = records.get(0).getItems();
        }
        return ExcelFileInfo.of(
                        String.format("工程%s-签证信息", request.getConstructionCode()), items
                ).
                nextTitle("code", "合同编号").
                nextTitle("name", "合同名称").
                nextTitle("type", "合同类型").
                nextTitle("firstpartOrganization", "甲方单位").
                nextTitle("secondpartOrganization", "乙方单位").
                nextTitle("cost", "合同总金额（万元）").
                nextTitle("constructTimeStr", "施工日期").
                nextTitle("signTimeName", "签署时间").
                nextTitle("workTimeBegin", "合同工期（开始时间）").
                nextTitle("workTimeEnd", "合同工期（完成时间）").
                nextTitle("creatorName", "创建人").
                nextTitle("createTime", "创建时间");
    }

    @GetMapping("/export/global/excel")
    public ExcelFileInfo exportGlobalExcel(SoConstructionContractPageRequest request) {
        return ExcelFileInfo.of("工程合同管理列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称")
                .nextTitle("constructionTypeName", "工程类别")
                .nextTitle("firstpartOrganization", "业主单位")
                // .nextTitle("secondpartOrganization", "施工单位")
                .nextTitle("secondpartOrganization", "设计单位")
                // .nextTitle("expectEndTimeStr", "监理单位")
                .nextTitle("contractTotalCost", "合同总金额(万元)");
    }

    @PostMapping
    public SoConstructionContract save(@RequestBody SoConstructionContractSaveRequest req) throws ThingsboardException {
        req.preCheckUpdate(() -> !service.isComplete(req.getConstructionCode(), req.tenantId()), "已完成不可更改");
        return service.save(req);
    }

    @PostMapping("/{constructionCode}/complete")
    public boolean complete(@PathVariable String constructionCode) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.complete(constructionCode, userId, tenantId);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionContractSaveRequest req, @PathVariable String id) {
        if (StringUtils.isNotEmpty(req.getCode()) && service.isCodeExists(req.getCode(), req.tenantId(), req.getId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (service.isComplete(id)) {
            ExceptionUtils.silentThrow("已完成，不可删除");
        }
        return service.delete(id);
    }

    // region 类型配置
    @GetMapping("/type")
    public IPage<SoGeneralType> getTypes(SoGeneralTypePageRequest request) {
        return service.getTypes(request);
    }

    @PostMapping("/type")
    public SoGeneralType saveType(@RequestBody SoGeneralTypeSaveRequest request) {
        return service.saveType(request);
    }
    // endregion

    // endregion

    // region 设备项管理
    @GetMapping("/{code}/device")
    public IPage<SoDeviceItem> getDevices(@PathVariable String code, SoDeviceItemPageRequest request) {
        request.setScope(SoGeneralSystemScope.SO_CONSTRUCTION_CONTRACT);
        request.setIdentifier(code);
        request.withoutCode();
        return service.getDevices(request);
    }

    @PostMapping("/{code}/device")
    public List<SoDeviceItem> saveDevice(@PathVariable String code, @RequestBody List<SoDeviceItemSaveRequest> request) {
        if (request.size() == 0) {
            return Collections.emptyList();
        }
        if (!service.isCompleteByContractCode(code, request.get(0).tenantId())) {
            ExceptionUtils.silentThrow("已完成不可更改");
        }
        for (SoDeviceItemSaveRequest item : request) {
            item.setIdentifier(code);
        }
        return service.saveDevice(request);
    }
    // endregion

}