import{d as f,g as o,n,p as a,bh as t,an as c,h as p,aw as d,i as _,cs as g,C as m}from"./index-r0dFAfgr.js";const h={class:"item"},w={key:0,class:"row"},v={class:"title"},k={key:1,class:"row"},y={class:"value blue"},b={class:"unit"},B={key:2,class:"row"},C={class:"text"},I=f({__name:"TargetItem",props:{config:{}},setup(u){const e=u;return(s,O)=>{var i,r,l;return o(),n("div",h,[((i=s.config.rows)==null?void 0:i.indexOf(1))!==-1?(o(),n("div",w,[a("span",v,t(e.config.label),1)])):c("",!0),((r=s.config.rows)==null?void 0:r.indexOf(2))!==-1?(o(),n("div",k,[a("span",y,t(e.config.value),1),a("span",b,t(e.config.unit),1)])):c("",!0),((l=s.config.rows)==null?void 0:l.indexOf(3))!==-1?(o(),n("div",B,[a("span",C,t(e.config.text),1),s.config.status?(o(),p(_(g),{key:0,icon:s.config.status==="up"?"material-symbols:arrow-drop-up":"material-symbols:arrow-drop-down",class:d(s.config.status)},null,8,["icon","class"])):c("",!0),a("span",{class:d(["value blue",s.config.status])},t(e.config.scale),3)])):c("",!0)])}}}),T=m(I,[["__scopeId","data-v-8609e48e"]]);export{T as default};
