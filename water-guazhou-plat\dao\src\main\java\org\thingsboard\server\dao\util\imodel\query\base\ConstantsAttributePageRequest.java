package org.thingsboard.server.dao.util.imodel.query.base;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.ConstantsAttributeList;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

@Data
public class ConstantsAttributePageRequest extends PageableQueryEntity<ConstantsAttributeList> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 键
     */
    @ApiModelProperty(value = "键")
    private String key;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String value;

}
