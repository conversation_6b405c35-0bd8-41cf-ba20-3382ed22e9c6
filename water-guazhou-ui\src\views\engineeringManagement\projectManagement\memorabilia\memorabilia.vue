<!-- 工程管理-项目管理-大事记 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ICONS } from '@/common/constans/common';
import { getProjectOperateRecord } from '@/api/engineeringManagement/projectManagement';

const refSearch = ref<ICardSearchIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '项目编号', field: 'code', type: 'input' },
    { label: '项目名称', field: 'name', type: 'input' },
    { label: '记录时间', field: 'time', type: 'daterange' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '项目编号', prop: 'code' },
    { label: '项目名称', prop: 'name' },
    { label: '项目类别', prop: 'typeName' },
    { label: '项目大事记', prop: 'detail' },
    { label: '大事记备注', prop: 'remark' },
    { label: '记录人', prop: 'creatorName' },
    { label: '记录时间', prop: 'createTimeName' }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.time) {
    params.fromTime = params.time[0];
    params.toTime = params.time[1];
    delete params.time;
  }
  getProjectOperateRecord(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

function init() {
  refreshData();
}

onMounted(() => {
  init();
});
</script>
