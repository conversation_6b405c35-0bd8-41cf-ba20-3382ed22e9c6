package org.thingsboard.server.dao.auditRecord.BO;

import org.thingsboard.server.dao.model.sql.AuditRecordEntity;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-01-19
 */
public class AuditRecordBO extends AuditRecordEntity {
    private int page;
    private int limit;

    private String startTime;

    private String endTime;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }
}
