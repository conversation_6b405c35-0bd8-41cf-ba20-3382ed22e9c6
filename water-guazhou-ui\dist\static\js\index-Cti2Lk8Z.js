import{d as X,c,s as C,r as Z,y as ee,x as f,o as le,g as U,n as R,q as a,i as o,p as s,F as i,G as _,bh as T,an as E,t as S,h as F,J as te,L as ae,H as oe,I as ne,aK as ie,aL as de,K as se,b7 as ue,bL as re,bM as pe,bq as me,C as fe}from"./index-r0dFAfgr.js";import{_ as ce}from"./CardTable-rdWOL4_6.js";import{_ as ve}from"./CardSearch-CB_HNR-Q.js";import{I as ge}from"./common-CvK_P_ao.js";import{l as be,m as ye,n as q}from"./index-D9ERhRP6.js";import{u as D}from"./useAmap-D6DJ1T90.js";import{a as A}from"./URLHelper-B9aplt5w.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-BI1vGJja.js";const Ve={class:"app-container"},_e={class:"action-bar"},Te={key:0,class:"video-detail"},xe={class:"detail-item"},ke={class:"value"},Ne={class:"detail-item"},Ie={class:"value"},Ce={class:"detail-item"},Ue={class:"value"},Me={class:"detail-item"},Ee={class:"value"},Se={class:"detail-item"},De={class:"value"},Ae={class:"detail-item"},Be={class:"value"},Le={class:"dialog-footer"},$e={class:"dialog-footer"},we={class:"dialog-footer"},Re=X({__name:"index",setup(Fe){const x=c(),k=c(!1),g=c(!1),b=c(!1),l=c(null),B=c(null),L=c(null);let M=null,N=null;const P=async()=>{if(!B.value)return;const{initAMap:t,setMarker:e}=D();await t("map-container",{center:[116.397428,39.90923],events:{click:m=>{const u=m.lnglat;l.value.longitude=u.lng,l.value.latitude=u.lat,M?M.setPosition([u.lng,u.lat]):M=e([u.lng,u.lat],{icon:A()})}}})},h=async()=>{if(!L.value)return;const{initAMap:t,setMarker:e}=D();if(await t("edit-map-container",{center:l.value.longitude&&l.value.latitude?[l.value.longitude,l.value.latitude]:[116.397428,39.90923],events:{click:m=>{const u=m.lnglat;l.value.longitude=u.lng,l.value.latitude=u.lat,N?N.setPosition([u.lng,u.lat]):N=e([u.lng,u.lat],{icon:A()})}}}),l.value.longitude&&l.value.latitude){const{setMarker:m}=D();N=m([l.value.longitude,l.value.latitude],{icon:A()})}},O=c({filters:[{label:"设备名称",field:"name",type:"input"},{label:"设备编号",field:"serialNumber",type:"input"},{label:"地址",field:"location",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:ge.QUERY,click:()=>v()},{type:"default",perm:!0,text:"重置",svgIcon:C(ue),click:()=>{var t;(t=x.value)==null||t.resetForm(),v()}}]}]}),r=Z({title:"视频监控",defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"设备名称",prop:"name"},{label:"设备编号",prop:"serialNumber"},{label:"视频类型",prop:"videoType",formatter:t=>({1:"海康",2:"大华",3:"其他"})[t.videoType]||t.videoType},{label:"更新时间",prop:"updateTime",formatter:t=>t.updateTime?new Date(t.updateTime).toLocaleString():"-"},{label:"经纬度",prop:"coordinate",formatter:t=>t.longitude&&t.latitude?`${t.longitude}, ${t.latitude}`:t.longitude?`${t.longitude}, -`:t.latitude?`-, ${t.latitude}`:"-"},{label:"地址",prop:"location",formatter:t=>t.location||"-"}],dataList:[],loading:!1,pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{r.pagination.page=t,r.pagination.limit=e,v()}},operations:[{perm:!0,text:"查看",isTextBtn:!0,svgIcon:C(re),click:t=>K(t)},{perm:!0,text:"编辑",isTextBtn:!0,svgIcon:C(pe),click:t=>z(t)},{perm:!0,text:"删除",isTextBtn:!0,svgIcon:C(me),click:t=>W(t)}]}),j=()=>{l.value={name:"",serialNumber:"",videoType:"1",longitude:"",latitude:"",location:"",url:""},b.value=!0,setTimeout(()=>{P()},300)},J=async()=>{if(l.value){if(!l.value.name){f.warning("请输入设备名称");return}if(!l.value.serialNumber){f.warning("请输入设备编号");return}try{const t={name:l.value.name,serialNumber:l.value.serialNumber,videoType:l.value.videoType,longitude:l.value.longitude,latitude:l.value.latitude,location:l.value.location,url:l.value.url||"",projectId:"",tenantId:"",groupId:""};console.log("新增视频数据:",t),await q(t),f.success("新增成功"),b.value=!1,v()}catch(t){console.error("新增失败:",t),f.error("新增失败，请重试")}}},K=t=>{l.value=t,k.value=!0},z=t=>{l.value=JSON.parse(JSON.stringify(t)),g.value=!0,console.log("编辑视频详情:",t),setTimeout(()=>{h()},300)},G=async()=>{if(l.value)try{const t={id:l.value.id,name:l.value.name,serialNumber:l.value.serialNumber,videoType:l.value.videoType,longitude:l.value.longitude,latitude:l.value.latitude,location:l.value.location,url:l.value.url||"",projectId:l.value.projectId||"",tenantId:l.value.tenantId||"",groupId:l.value.groupId||""};console.log("保存视频数据:",t),await q(t),f.success("保存成功"),g.value=!1,v()}catch(t){console.error("保存失败:",t),f.error("保存失败，请重试")}},H=t=>({1:"海康",2:"大华",3:"其他"})[t]||t,Q=t=>t.longitude&&t.latitude?`${t.longitude}, ${t.latitude}`:t.longitude?`${t.longitude}, -`:t.latitude?`-, ${t.latitude}`:"-",Y=t=>t?new Date(t).toLocaleString():"-",W=t=>{ee.confirm("确认删除该视频监控设备吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await be(t.id),f.success("删除成功"),v()}catch{f.error("删除失败")}}).catch(()=>{})},v=async()=>{var t,e,m,u,I,y;r.loading=!0;try{const p={page:r.pagination.page||1,size:r.pagination.limit||20,name:((e=(t=x.value)==null?void 0:t.queryParams)==null?void 0:e.name)||"",serialNumber:((u=(m=x.value)==null?void 0:m.queryParams)==null?void 0:u.serialNumber)||"",location:((y=(I=x.value)==null?void 0:I.queryParams)==null?void 0:y.location)||""},d=await ye(p);console.log("视频监控数据 - 完整响应:",d),console.log("视频监控数据 - data:",d.data),d.data&&Array.isArray(d.data)?(r.dataList=d.data,r.pagination.total=d.data.length,console.log("数据已赋值 - 新的TableConfig:",r)):d.data&&Array.isArray(d.data.content)?(r.dataList=d.data.content,r.pagination.total=d.data.totalElements||0,console.log("数据已赋值(分页格式) - 新的TableConfig:",r)):(console.error("数据结构不符合预期:",d.data),r.dataList=[],r.pagination.total=0)}catch{f.error("获取数据失败")}r.loading=!1};return le(async()=>{await v()}),(t,e)=>{const m=ve,u=te,I=ce,y=ae,p=oe,d=ne,V=ie,$=de,w=se;return U(),R("div",Ve,[a(m,{ref_key:"refSearch",ref:x,config:o(O)},null,8,["config"]),s("div",_e,[a(u,{type:"primary",onClick:j},{default:i(()=>e[18]||(e[18]=[_("新增")])),_:1})]),a(I,{config:o(r),class:"card-table"},null,8,["config"]),a(y,{title:"查看视频监控",modelValue:o(k),"onUpdate:modelValue":e[1]||(e[1]=n=>S(k)?k.value=n:null),width:"700px","destroy-on-close":""},{footer:i(()=>[s("span",Le,[a(u,{onClick:e[0]||(e[0]=n=>k.value=!1)},{default:i(()=>e[25]||(e[25]=[_("关闭")])),_:1})])]),default:i(()=>[o(l)?(U(),R("div",Te,[s("div",xe,[e[19]||(e[19]=s("span",{class:"label"},"设备名称：",-1)),s("span",ke,T(o(l).name),1)]),s("div",Ne,[e[20]||(e[20]=s("span",{class:"label"},"设备编号：",-1)),s("span",Ie,T(o(l).serialNumber),1)]),s("div",Ce,[e[21]||(e[21]=s("span",{class:"label"},"视频类型：",-1)),s("span",Ue,T(H(o(l).videoType)),1)]),s("div",Me,[e[22]||(e[22]=s("span",{class:"label"},"经纬度：",-1)),s("span",Ee,T(Q(o(l))),1)]),s("div",Se,[e[23]||(e[23]=s("span",{class:"label"},"地址：",-1)),s("span",De,T(o(l).location||"-"),1)]),s("div",Ae,[e[24]||(e[24]=s("span",{class:"label"},"更新时间：",-1)),s("span",Be,T(Y(o(l).updateTime)),1)])])):E("",!0)]),_:1},8,["modelValue"]),a(y,{title:"编辑视频监控",modelValue:o(g),"onUpdate:modelValue":e[9]||(e[9]=n=>S(g)?g.value=n:null),width:"700px","destroy-on-close":""},{footer:i(()=>[s("span",$e,[a(u,{onClick:e[8]||(e[8]=n=>g.value=!1)},{default:i(()=>e[27]||(e[27]=[_("取消")])),_:1}),a(u,{type:"primary",onClick:G},{default:i(()=>e[28]||(e[28]=[_("确定")])),_:1})])]),default:i(()=>[o(l)?(U(),F(w,{key:0,model:o(l),"label-width":"100px"},{default:i(()=>[a(d,{label:"设备名称"},{default:i(()=>[a(p,{modelValue:o(l).name,"onUpdate:modelValue":e[2]||(e[2]=n=>o(l).name=n),placeholder:"请输入设备名称"},null,8,["modelValue"])]),_:1}),a(d,{label:"设备编号"},{default:i(()=>[a(p,{modelValue:o(l).serialNumber,"onUpdate:modelValue":e[3]||(e[3]=n=>o(l).serialNumber=n),placeholder:"请输入设备编号"},null,8,["modelValue"])]),_:1}),a(d,{label:"视频类型"},{default:i(()=>[a($,{modelValue:o(l).videoType,"onUpdate:modelValue":e[4]||(e[4]=n=>o(l).videoType=n),placeholder:"请选择视频类型",style:{width:"100%"}},{default:i(()=>[a(V,{label:"海康",value:"1"}),a(V,{label:"大华",value:"2"}),a(V,{label:"其他",value:"3"})]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"经度"},{default:i(()=>[a(p,{modelValue:o(l).longitude,"onUpdate:modelValue":e[5]||(e[5]=n=>o(l).longitude=n),placeholder:"请输入经度"},null,8,["modelValue"])]),_:1}),a(d,{label:"纬度"},{default:i(()=>[a(p,{modelValue:o(l).latitude,"onUpdate:modelValue":e[6]||(e[6]=n=>o(l).latitude=n),placeholder:"请输入纬度"},null,8,["modelValue"])]),_:1}),a(d,{label:"地址"},{default:i(()=>[a(p,{modelValue:o(l).location,"onUpdate:modelValue":e[7]||(e[7]=n=>o(l).location=n),placeholder:"请输入地址"},null,8,["modelValue"])]),_:1}),a(d,{label:"地图选点"},{default:i(()=>[s("div",{id:"edit-map-container",ref_key:"editMapRef",ref:L,style:{width:"100%",height:"300px",border:"1px solid #ddd"}},null,512),e[26]||(e[26]=s("div",{class:"map-tip"},"点击地图可获取经纬度信息",-1))]),_:1})]),_:1},8,["model"])):E("",!0)]),_:1},8,["modelValue"]),a(y,{title:"新增视频监控",modelValue:o(b),"onUpdate:modelValue":e[17]||(e[17]=n=>S(b)?b.value=n:null),width:"700px","destroy-on-close":""},{footer:i(()=>[s("span",we,[a(u,{onClick:e[16]||(e[16]=n=>b.value=!1)},{default:i(()=>e[30]||(e[30]=[_("取消")])),_:1}),a(u,{type:"primary",onClick:J},{default:i(()=>e[31]||(e[31]=[_("确定")])),_:1})])]),default:i(()=>[o(l)?(U(),F(w,{key:0,model:o(l),"label-width":"100px"},{default:i(()=>[a(d,{label:"设备名称",prop:"name"},{default:i(()=>[a(p,{modelValue:o(l).name,"onUpdate:modelValue":e[10]||(e[10]=n=>o(l).name=n),placeholder:"请输入设备名称"},null,8,["modelValue"])]),_:1}),a(d,{label:"设备编号",prop:"serialNumber"},{default:i(()=>[a(p,{modelValue:o(l).serialNumber,"onUpdate:modelValue":e[11]||(e[11]=n=>o(l).serialNumber=n),placeholder:"请输入设备编号"},null,8,["modelValue"])]),_:1}),a(d,{label:"视频类型",prop:"videoType"},{default:i(()=>[a($,{modelValue:o(l).videoType,"onUpdate:modelValue":e[12]||(e[12]=n=>o(l).videoType=n),placeholder:"请选择视频类型",style:{width:"100%"}},{default:i(()=>[a(V,{label:"海康",value:"1"}),a(V,{label:"大华",value:"2"}),a(V,{label:"其他",value:"3"})]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"经度",prop:"longitude"},{default:i(()=>[a(p,{modelValue:o(l).longitude,"onUpdate:modelValue":e[13]||(e[13]=n=>o(l).longitude=n),placeholder:"请输入经度"},null,8,["modelValue"])]),_:1}),a(d,{label:"纬度",prop:"latitude"},{default:i(()=>[a(p,{modelValue:o(l).latitude,"onUpdate:modelValue":e[14]||(e[14]=n=>o(l).latitude=n),placeholder:"请输入纬度"},null,8,["modelValue"])]),_:1}),a(d,{label:"地址",prop:"location"},{default:i(()=>[a(p,{modelValue:o(l).location,"onUpdate:modelValue":e[15]||(e[15]=n=>o(l).location=n),placeholder:"请输入地址"},null,8,["modelValue"])]),_:1}),a(d,{label:"地图选点"},{default:i(()=>[s("div",{id:"map-container",ref_key:"mapRef",ref:B,style:{width:"100%",height:"300px",border:"1px solid #ddd"}},null,512),e[29]||(e[29]=s("div",{class:"map-tip"},"点击地图可获取经纬度信息",-1))]),_:1})]),_:1},8,["model"])):E("",!0)]),_:1},8,["modelValue"])])}}}),Qe=fe(Re,[["__scopeId","data-v-c463eb20"]]);export{Qe as default};
