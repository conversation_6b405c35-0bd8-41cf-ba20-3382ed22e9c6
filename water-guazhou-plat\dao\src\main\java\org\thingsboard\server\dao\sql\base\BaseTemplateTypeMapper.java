package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateType;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateTypePageRequest;

import java.util.List;

/**
 * 平台管理-模型类型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface BaseTemplateTypeMapper {
    /**
     * 查询平台管理-模型类型
     *
     * @param id 平台管理-模型类型主键
     * @return 平台管理-模型类型
     */
    public BaseTemplateType selectBaseTemplateTypeById(String id);

    /**
     * 查询平台管理-模型类型列表
     *
     * @param baseTemplateType 平台管理-模型类型
     * @return 平台管理-模型类型集合
     */
    public IPage<BaseTemplateType> selectBaseTemplateTypeList(BaseTemplateTypePageRequest baseTemplateType);

    /**
     * 新增平台管理-模型类型
     *
     * @param baseTemplateType 平台管理-模型类型
     * @return 结果
     */
    public int insertBaseTemplateType(BaseTemplateType baseTemplateType);

    /**
     * 修改平台管理-模型类型
     *
     * @param baseTemplateType 平台管理-模型类型
     * @return 结果
     */
    public int updateBaseTemplateType(BaseTemplateType baseTemplateType);

    /**
     * 删除平台管理-模型类型
     *
     * @param id 平台管理-模型类型主键
     * @return 结果
     */
    public int deleteBaseTemplateTypeById(String id);

    /**
     * 批量删除平台管理-模型类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseTemplateTypeByIds(@Param("array") List<String> ids);
}
