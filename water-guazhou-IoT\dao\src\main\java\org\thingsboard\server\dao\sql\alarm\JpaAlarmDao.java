/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.alarm;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.EntityType;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.relation.EntityRelation;
import org.thingsboard.server.common.data.relation.RelationTypeGroup;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.alarm.AlarmDao;
import org.thingsboard.server.dao.alarm.BaseAlarmService;
import org.thingsboard.server.dao.model.sql.AlarmEntity;
import org.thingsboard.server.dao.relation.RelationDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Created by Valerii Sosliuk on 5/19/2017.
 */
@Slf4j
@Component
@SqlDao
public class JpaAlarmDao extends JpaAbstractDao<AlarmEntity, Alarm> implements AlarmDao {

    @Autowired
    private AlarmRepository alarmRepository;

    @Autowired
    private RelationDao relationDao;

    @PersistenceContext
    EntityManager entityManager;

    @Override
    protected Class<AlarmEntity> getEntityClass() {
        return AlarmEntity.class;
    }

    @Override
    protected CrudRepository<AlarmEntity, String> getCrudRepository() {
        return alarmRepository;
    }

    @Override
    public Boolean deleteAlarm(TenantId tenantId, Alarm alarm) {
        return removeById(alarm.getUuidId());
    }

    @Override
    public ListenableFuture<Alarm> findLatestByOriginatorAndType(TenantId tenantId, EntityId originator, String type) {
        return service.submit(() -> {
            List<AlarmEntity> latest = alarmRepository.findLatestByOriginatorAndType(
                    UUIDConverter.fromTimeUUID(tenantId.getId()),
                    UUIDConverter.fromTimeUUID(originator.getId()),
                    originator.getEntityType(),
                    type,
                    new PageRequest(0, 1));
            return latest.isEmpty() ? null : DaoUtil.getData(latest.get(0));
        });
    }

    @Override
    public ListenableFuture<Alarm> findByIdAsync(UUID key) {
        return service.submit(() -> findById(key));
    }

    @Override
    public ListenableFuture<List<AlarmInfo>> findAlarms(TenantId tenantId, AlarmQuery query) {
        log.trace("Try to find alarms by entity [{}], status [{}] and pageLink [{}]", query.getAffectedEntityId(), query.getStatus(), query.getPageLink());
        EntityId affectedEntity = query.getAffectedEntityId();
        String searchStatusName;
        if (query.getSearchStatus() == null && query.getStatus() == null) {
            searchStatusName = AlarmSearchStatus.ANY.name();
        } else if (query.getSearchStatus() != null) {
            searchStatusName = query.getSearchStatus().name();
        } else {
            searchStatusName = query.getStatus().name();
        }
        String relationType = BaseAlarmService.ALARM_RELATION_PREFIX + searchStatusName;
        ListenableFuture<List<EntityRelation>> relations = relationDao.findRelations(tenantId, affectedEntity, relationType, RelationTypeGroup.ALARM, EntityType.ALARM, query.getPageLink());
        return Futures.transformAsync(relations, input -> {
            List<ListenableFuture<AlarmInfo>> alarmFutures = new ArrayList<>(input.size());
            for (EntityRelation relation : input) {
                alarmFutures.add(Futures.transform(
                        findByIdAsync(tenantId, relation.getTo().getId()),
                        AlarmInfo::new));
            }
            return Futures.successfulAsList(alarmFutures);
        });
    }

    @Override
    public ListenableFuture<List<Alarm>> findUnClearByJsonId(AlarmJsonId id) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByAlarmJsonId(UUIDConverter.fromTimeUUID(id.getId()))).parallelStream()
                .filter(a -> a.getStatus() != AlarmStatus.CLEARED_ACK && a.getStatus() != AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public List<Alarm> findClearAlarmByTenantAndLevel(TenantId tenantId, List<DeviceId> deviceId, String type, String level, String status, long start, long end) {

        //String sql = "";
        Query query = null;
        StringBuilder sql = new StringBuilder("select * from  alarm a where a.tenant_id = :tenantId");
        if (deviceId != null && deviceId.size() > 0) {
            sql.append(" and a.originator_id in ( ");
            deviceId.forEach(device -> {
                sql.append("'" + UUIDConverter.fromTimeUUID(device.getId()) + "' ,");
            });
            sql.deleteCharAt(sql.lastIndexOf(","));
            sql.append(" )");
        }
        if (type != null && "" != type) {
            sql.append(" and a.type = :type ");
        }
        if (status != null && "" != status) {
            sql.append(" and a.status = :status");
        }
        if (level != null && "" != level) {
            sql.append(" and a.level = :level");
        }
        sql.append(" and a.start_ts >= :start and a.start_ts <= :end ORDER BY a.start_ts DESC");
        query = entityManager.createNativeQuery(sql.toString(), AlarmEntity.class);
        query.setParameter("tenantId", UUIDConverter.fromTimeUUID(tenantId.getId()));
//        if (deviceId != null) {
//            query.setParameter("deviceId", UUIDConverter.fromTimeUUID(deviceId.getId()));
//        }
        if (type != null && "" != type) {
            query.setParameter("type", type);
        }
        if (status != null && "" != status) {
            query.setParameter("status", status);
        }
        if (level != null && "" != level) {
            query.setParameter("level", level);
        }
        query.setParameter("start", start);
        query.setParameter("end", end);
        log.debug("查询语句：" + sql.toString());
        List<AlarmEntity> list = query.getResultList();
        entityManager.clear();
        return DaoUtil.convertDataList(list);
    }


    @Override
    public ListenableFuture<List<Alarm>> findHistoryAlarm(TenantId tenantId, long start, long end) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByTenantIdAndStartTsBetween(UUIDConverter.fromTimeUUID(tenantId.getId()), start, end)).parallelStream()
                .filter(a -> a.getStatus() == AlarmStatus.CLEARED_ACK || a.getStatus() == AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findRealTimeAlarm(TenantId tenantId, long start, long end) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByTenantIdAndStartTsBetween(UUIDConverter.fromTimeUUID(tenantId.getId()), start, end)).parallelStream()
                .filter(a -> a.getStatus() != AlarmStatus.CLEARED_ACK && a.getStatus() != AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findHistoryByDeviceId(DeviceId deviceId, long start, long end) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByOriginatorIdAndStartTsAndEndTs(UUIDConverter.fromTimeUUID(deviceId.getId()), start, end)).parallelStream()
                .filter(a -> a.getStatus() == AlarmStatus.CLEARED_ACK || a.getStatus() == AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findOnlineByTypeAndDevice(DeviceId deviceId, String type) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByOriginatorIdAndType(UUIDConverter.fromTimeUUID(deviceId.getId()), type)).parallelStream()
                .filter(a -> a.getStatus() != AlarmStatus.CLEARED_ACK && a.getStatus() != AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findOnlineByLevelAndDevice(DeviceId deviceId, String level) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByOriginatorIdAndLevel(UUIDConverter.fromTimeUUID(deviceId.getId()), level)).parallelStream()
                .filter(a -> a.getStatus() != AlarmStatus.CLEARED_ACK && a.getStatus() != AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findHistoryAlarmByDevice(DeviceId deviceId, String alarmType) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByOriginatorIdAndType(UUIDConverter.fromTimeUUID(deviceId.getId()), alarmType)).parallelStream()
                .filter(a -> a.getStatus() == AlarmStatus.CLEARED_ACK || a.getStatus() == AlarmStatus.CLEAR_FORCED).collect(Collectors.toList()));
    }

    @Override
    public void deleteAlarmByDevice(DeviceId deviceId) {
        alarmRepository.deleteAllByOriginatorId(UUIDConverter.fromTimeUUID(deviceId.getId()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findOnlineAlarmByJsonId(AlarmJsonId id) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByAlarmJsonId(UUIDConverter.fromTimeUUID(id.getId()))).parallelStream()
                .filter(a -> a.getStatus() != AlarmStatus.CLEARED_ACK && a.getStatus() != AlarmStatus.CLEAR_FORCED && a.getStatus() != AlarmStatus.RESTORE_ACK).collect(Collectors.toList()));
    }

    @Override
    public ListenableFuture<List<Alarm>> findAlarmByJsonId(AlarmJsonId id) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByAlarmJsonId(UUIDConverter.fromTimeUUID(id.getId()))));
    }

    @Override
    public ListenableFuture<List<Alarm>> findNotOfflineAlarmByDevice(DeviceId deviceId) {
        return service.submit(() -> DaoUtil.convertDataList(alarmRepository.findByOriginatorIdAndAlarmJsonIdIsNotNullOrderByStartTs(UUIDConverter.fromTimeUUID(deviceId.getId()))));
    }

    @Override
    public List<String> findAlarmDeviceIdList(TenantId tenantId) {
        return alarmRepository.findAlarmDeviceIdList(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

}
