package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsInventoryCEntity;

import java.util.List;

/**
 * 设备盘点子表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsInventoryCRepository extends CrudRepository<AssetsInventoryCEntity, String> {
    List<AssetsInventoryCEntity> findAllByPidOrderByCreateTimeDesc(String pid);
}
