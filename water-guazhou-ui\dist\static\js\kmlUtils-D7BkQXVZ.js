import{a5 as c,aS as I}from"./index-r0dFAfgr.js";import{r as G,d as k,U as w}from"./pe-B8dP0-Ut.js";import{bQ as h,bR as S,bS as m,bT as d,bU as x,bH as E,bJ as v,bI as O}from"./MapView-DaoQedLH.js";import{b as F}from"./Point-WxyopZva.js";const M={esriGeometryPoint:"points",esriGeometryPolyline:"polylines",esriGeometryPolygon:"polygons"};function D(r){const s=r.folders||[],t=s.slice(),e=new Map,n=new Map,f=new Map,l=new Map,p=new Map,u={esriGeometryPoint:n,esriGeometryPolyline:f,esriGeometryPolygon:l};(r.featureCollection&&r.featureCollection.layers||[]).forEach(o=>{const a=c(o);a.featureSet.features=[];const i=o.featureSet.geometryType;e.set(i,a);const g=o.layerDefinition.objectIdField;i==="esriGeometryPoint"?b(n,g,o.featureSet.features):i==="esriGeometryPolyline"?b(f,g,o.featureSet.features):i==="esriGeometryPolygon"&&b(l,g,o.featureSet.features)}),r.groundOverlays&&r.groundOverlays.forEach(o=>{p.set(o.id,o)}),s.forEach(o=>{o.networkLinkIds.forEach(a=>{const i=J(a,o.id,r.networkLinks);i&&t.push(i)})}),t.forEach(o=>{if(o.featureInfos){o.points=c(e.get("esriGeometryPoint")),o.polylines=c(e.get("esriGeometryPolyline")),o.polygons=c(e.get("esriGeometryPolygon")),o.mapImages=[];for(const a of o.featureInfos)switch(a.type){case"esriGeometryPoint":case"esriGeometryPolyline":case"esriGeometryPolygon":{const i=u[a.type].get(a.id);i&&o[M[a.type]].featureSet.features.push(i);break}case"GroundOverlay":{const i=p.get(a.id);i&&o.mapImages.push(i);break}}o.fullExtent=P([o])}});const y=P(t);return{folders:s,sublayers:t,extent:y}}function L(r,s,t,e){const n=G&&G.findCredential(r);r=k(r,{token:n&&n.token});const f=I.kmlServiceUrl;return w(f,{query:{url:r,model:"simple",folders:"",refresh:t!==0||void 0,outSR:JSON.stringify(s)},responseType:"json",signal:e})}function N(r,s,t=null,e=[]){const n=[],f={},l=s.sublayers,p=s.folders.map(u=>u.id);return l.forEach(u=>{var o;const y=new r;if(t?y.read(u,t):y.read(u),e.length&&p.includes(y.id)&&(y.visible=e.includes(y.id)),f[u.id]=y,u.parentFolderId!=null&&u.parentFolderId!==-1){const a=f[u.parentFolderId];a.sublayers||(a.sublayers=[]),(o=a.sublayers)==null||o.unshift(y)}else n.unshift(y)}),n}function b(r,s,t){t.forEach(e=>{r.set(e.attributes[s],e)})}function $(r,s){let t;return s.some(e=>e.id===r&&(t=e,!0)),t}function J(r,s,t){const e=$(r,t);return e&&(e.parentFolderId=s,e.networkLink=e),e}async function R(r){const s=E.fromJSON(r.featureSet).features,t=r.layerDefinition,e=v(t.drawingInfo.renderer),n=O.fromJSON(r.popupInfo),f=[];for(const l of s){const p=await e.getSymbolAsync(l);l.symbol=p,l.popupTemplate=n,l.visible=!0,f.push(l)}return f}function P(r){const s=h(S),t=h(S);for(const e of r){if(e.polygons&&e.polygons.featureSet&&e.polygons.featureSet.features)for(const n of e.polygons.featureSet.features)m(s,n.geometry),d(t,s);if(e.polylines&&e.polylines.featureSet&&e.polylines.featureSet.features)for(const n of e.polylines.featureSet.features)m(s,n.geometry),d(t,s);if(e.points&&e.points.featureSet&&e.points.featureSet.features)for(const n of e.points.featureSet.features)m(s,n.geometry),d(t,s);if(e.mapImages)for(const n of e.mapImages)m(s,n.extent),d(t,s)}return x(t,S)?void 0:{xmin:t[0],ymin:t[1],zmin:t[2],xmax:t[3],ymax:t[4],zmax:t[5],spatialReference:F.WGS84}}export{N as S,R as b,D as d,L as g,P as j};
