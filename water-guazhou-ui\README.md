# istart-frame

## 环境

- nodejs 16+
- 内存 16G+

## Project setup

```cmd
yarn install
```

### Compiles and hot-reloads for development

```cmd
yarn serve
```

### Compiles and minifies for production

```cmd
yarn build
```

### Lints and fixes files

```cmd
yarn lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

### Formatting code

```cmd
ESLint prettier  (.eslintrc.js/.prettierrc)
```

### @arcgis/core": "^4.24.7
