import request from '@/plugins/axios'
import { useAppStore } from '@/store'

/**
 * 通用文件上传方法
 * @param file 要上传的文件
 * @param type 文件类型，可选值：'file' | 'image'，默认为 'file'
 * @returns Promise<string> 返回文件地址
 */
export async function uploadFile(file: File, type: 'file' | 'image' = 'file'): Promise<string> {
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const baseUrl = useAppStore().actionUrl
    if (!baseUrl) {
      throw new Error('未配置上传服务器地址')
    }
    
    const url = type === 'file' 
      ? baseUrl + 'file/api/upload/file'
      : baseUrl + 'file/api/upload/image'
    
    const res = await request({
      url,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    if (!res.data) {
      throw new Error('文件上传失败：未获取到文件地址')
    }
    
    return res.data
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
} 