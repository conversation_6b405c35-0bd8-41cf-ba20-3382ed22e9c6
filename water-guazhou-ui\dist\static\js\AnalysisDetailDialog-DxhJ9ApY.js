import{d as M,c as G,a8 as H,am as C,bB as O,c4 as U,g,h as k,F as t,p as s,q as l,G as p,bh as n,aw as E,an as T,n as W,x as q,bU as j,bW as K,c6 as Q,bz as X,dD as Z,eJ as x,J as aa,L as sa,C as ea}from"./index-r0dFAfgr.js";/* empty css                 */import{f as ta}from"./DateFormatter-Bm9a68Ax.js";import{d as la}from"./processNumber-Clv_jqeh.js";const na={class:"analysis-detail"},ia={class:"info-item"},oa={class:"info-item"},ra={class:"info-item"},da={class:"info-item"},ua={class:"info-item"},ca={class:"info-item"},pa={class:"result-content"},fa={class:"result-content"},va={class:"result-value"},ma={class:"result-content"},ya={class:"result-value"},ga={class:"result-content"},Da={class:"result-value"},_a={class:"data-grid"},ba={class:"data-item"},ha={class:"data-item"},wa={class:"data-item"},Ca={class:"data-grid"},ka={class:"data-item"},Ea={class:"data-item"},Ta={class:"data-item"},La={class:"card-header"},Aa={class:"suggestion-content"},Va={class:"suggestion-text"},Sa={key:0,class:"risk-description"},Na={class:"chart-container"},Ba={class:"dialog-footer"},Ra=M({__name:"AnalysisDetailDialog",props:{visible:{type:Boolean,default:!1},analysisData:{default:()=>({})}},emits:["update:visible"],setup(L,{emit:A}){const f=a=>ta(a,"YYYY-MM-DD HH:mm:ss"),d=(a,e=2)=>a==null||a===""?"--":la(a),v=L,D=A,m=G();let u=null;const y=H({get:()=>v.visible,set:a=>D("update:visible",a)});C(y,a=>{a&&O(()=>{V()})}),C(()=>v.analysisData,a=>{a&&u&&_(a)},{deep:!0});const V=()=>{m.value&&(u=U(m.value),_(v.analysisData))},_=a=>{if(!(!u||!a.analysisDetails))try{const i=JSON.parse(a.analysisDetails).levelTrend||[],c={title:{text:"水位变化趋势",left:"center"},tooltip:{trigger:"axis",formatter:o=>{const r=o[0];return`
            时间: ${new Date(r.axisValue).toLocaleString()}<br/>
            原水液位: ${r.value[1]}m<br/>
            地下水位: ${r.value[2]}m<br/>
            液位变化: ${r.value[3]}m
          `}},legend:{data:["原水液位","地下水位","液位变化"],top:30},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"time",boundaryGap:!1},yAxis:[{type:"value",name:"水位(m)",position:"left"},{type:"value",name:"变化量(m)",position:"right"}],series:[{name:"原水液位",type:"line",data:i.map(o=>[o.time,o.rawWaterLevel]),smooth:!0,lineStyle:{color:"#409EFF"}},{name:"地下水位",type:"line",data:i.map(o=>[o.time,o.groundwaterLevel]),smooth:!0,lineStyle:{color:"#67C23A"}},{name:"液位变化",type:"line",yAxisIndex:1,data:i.map(o=>[o.time,o.levelChange]),smooth:!0,lineStyle:{color:"#E6A23C"}}]};u.setOption(c)}catch(e){console.error("解析分析详情数据失败:",e)}},S=a=>{switch(a){case 1:return"warning";case 2:return"success";case 3:return"danger";default:return"info"}},N=a=>{switch(a){case 1:return"分析中";case 2:return"已完成";case 3:return"分析失败";default:return"未知"}},b=a=>a?a>0?"level-increase":a<0?"level-decrease":"":"",B=a=>a?a>=80?"#67c23a":a>=60?"#e6a23c":a>=40?"#f56c6c":"#909399":"#e6e6e6",R=a=>{switch(a){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},P=a=>{switch(a){case 1:return"低风险";case 2:return"中风险";case 3:return"高风险";default:return"未知"}},Y=a=>{switch(a){case 1:return"success";case 2:return"warning";case 3:return"error";default:return"info"}},$=()=>{q.info("导出功能开发中...")},h=()=>{u&&(u.dispose(),u=null),D("update:visible",!1)};return(a,e)=>{const i=j,c=K,o=Q,r=X,z=Z,F=x,w=aa,I=sa;return g(),k(I,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=J=>y.value=J),title:"涵养水位分析详情",width:"1200px","close-on-click-modal":!1,onClose:h},{footer:t(()=>[s("div",Ba,[l(w,{onClick:h},{default:t(()=>e[22]||(e[22]=[p("关闭")])),_:1}),l(w,{type:"primary",onClick:$},{default:t(()=>e[23]||(e[23]=[p("导出报告")])),_:1})])]),default:t(()=>[s("div",na,[l(r,{class:"info-card",shadow:"never"},{header:t(()=>e[1]||(e[1]=[s("div",{class:"card-header"},[s("span",null,"基础信息")],-1)])),default:t(()=>[l(c,{gutter:20},{default:t(()=>[l(i,{span:8},{default:t(()=>[s("div",ia,[e[2]||(e[2]=s("label",null,"测点名称：",-1)),s("span",null,n(a.analysisData.stationName),1)])]),_:1}),l(i,{span:8},{default:t(()=>[s("div",oa,[e[3]||(e[3]=s("label",null,"分析周期：",-1)),s("span",null,n(f(a.analysisData.startTime))+" 至 "+n(f(a.analysisData.endTime)),1)])]),_:1}),l(i,{span:8},{default:t(()=>[s("div",ra,[e[4]||(e[4]=s("label",null,"分析时间：",-1)),s("span",null,n(f(a.analysisData.createTime)),1)])]),_:1})]),_:1}),l(c,{gutter:20},{default:t(()=>[l(i,{span:8},{default:t(()=>[s("div",da,[e[5]||(e[5]=s("label",null,"分析人：",-1)),s("span",null,n(a.analysisData.creatorName),1)])]),_:1}),l(i,{span:8},{default:t(()=>[s("div",ua,[e[6]||(e[6]=s("label",null,"算法版本：",-1)),s("span",null,n(a.analysisData.algorithmVersion),1)])]),_:1}),l(i,{span:8},{default:t(()=>[s("div",ca,[e[7]||(e[7]=s("label",null,"分析状态：",-1)),l(o,{type:S(a.analysisData.status)},{default:t(()=>[p(n(N(a.analysisData.status)),1)]),_:1},8,["type"])])]),_:1})]),_:1})]),_:1}),l(c,{gutter:20,style:{"margin-bottom":"20px"}},{default:t(()=>[l(i,{span:6},{default:t(()=>[l(r,{class:"result-card",shadow:"never"},{default:t(()=>[s("div",pa,[s("div",{class:E(["result-value",b(a.analysisData.levelChange)])},n(d(a.analysisData.levelChange))+"m ",3),e[8]||(e[8]=s("div",{class:"result-label"},"水位变化",-1))])]),_:1})]),_:1}),l(i,{span:6},{default:t(()=>[l(r,{class:"result-card",shadow:"never"},{default:t(()=>[s("div",fa,[s("div",va,n(d(a.analysisData.conservationCoefficient)),1),e[9]||(e[9]=s("div",{class:"result-label"},"涵养系数",-1))])]),_:1})]),_:1}),l(i,{span:6},{default:t(()=>[l(r,{class:"result-card",shadow:"never"},{default:t(()=>[s("div",ma,[s("div",ya,n(d(a.analysisData.conservationPotential))+"%",1),e[10]||(e[10]=s("div",{class:"result-label"},"涵养潜力",-1)),l(z,{percentage:a.analysisData.conservationPotential,color:B(a.analysisData.conservationPotential),"show-text":!1,style:{"margin-top":"8px"}},null,8,["percentage","color"])])]),_:1})]),_:1}),l(i,{span:6},{default:t(()=>[l(r,{class:"result-card",shadow:"never"},{default:t(()=>[s("div",ga,[s("div",Da,[l(o,{type:R(a.analysisData.riskLevel),size:"large"},{default:t(()=>[p(n(P(a.analysisData.riskLevel)),1)]),_:1},8,["type"])]),e[11]||(e[11]=s("div",{class:"result-label"},"风险等级",-1))])]),_:1})]),_:1})]),_:1}),l(c,{gutter:20,style:{"margin-bottom":"20px"}},{default:t(()=>[l(i,{span:12},{default:t(()=>[l(r,{class:"data-card",shadow:"never"},{header:t(()=>e[12]||(e[12]=[s("div",{class:"card-header"},[s("span",null,"水位数据")],-1)])),default:t(()=>[s("div",_a,[s("div",ba,[e[13]||(e[13]=s("label",null,"期初水位：",-1)),s("span",null,n(d(a.analysisData.initialLevel))+" 米",1)]),s("div",ha,[e[14]||(e[14]=s("label",null,"期末水位：",-1)),s("span",null,n(d(a.analysisData.finalLevel))+" 米",1)]),s("div",wa,[e[15]||(e[15]=s("label",null,"水位变化：",-1)),s("span",{class:E(b(a.analysisData.levelChange))},n(d(a.analysisData.levelChange))+" 米 ",3)])])]),_:1})]),_:1}),l(i,{span:12},{default:t(()=>[l(r,{class:"data-card",shadow:"never"},{header:t(()=>e[16]||(e[16]=[s("div",{class:"card-header"},[s("span",null,"环境因素")],-1)])),default:t(()=>[s("div",Ca,[s("div",ka,[e[17]||(e[17]=s("label",null,"平均降雨量：",-1)),s("span",null,n(d(a.analysisData.avgRainfall))+" 毫米",1)]),s("div",Ea,[e[18]||(e[18]=s("label",null,"平均蒸发量：",-1)),s("span",null,n(d(a.analysisData.avgEvaporation))+" 毫米",1)]),s("div",Ta,[e[19]||(e[19]=s("label",null,"总开采量：",-1)),s("span",null,n(d(a.analysisData.totalExtraction))+" 立方米",1)])])]),_:1})]),_:1})]),_:1}),l(r,{class:"suggestion-card",shadow:"never"},{header:t(()=>[s("div",La,[e[20]||(e[20]=s("span",null,"涵养建议",-1)),a.analysisData.suggestedConservationAmount?(g(),k(o,{key:0,type:"primary"},{default:t(()=>[p(" 建议涵养量："+n(d(a.analysisData.suggestedConservationAmount))+" 立方米 ",1)]),_:1})):T("",!0)])]),default:t(()=>[s("div",Aa,[s("div",Va,n(a.analysisData.conservationSuggestion||"暂无建议"),1),a.analysisData.riskDescription?(g(),W("div",Sa,[l(F,{title:a.analysisData.riskDescription,type:Y(a.analysisData.riskLevel),closable:!1,"show-icon":""},null,8,["title","type"])])):T("",!0)])]),_:1}),l(r,{class:"chart-card",shadow:"never"},{header:t(()=>e[21]||(e[21]=[s("div",{class:"card-header"},[s("span",null,"水位变化趋势")],-1)])),default:t(()=>[s("div",Na,[s("div",{ref_key:"chartRef",ref:m,style:{width:"100%",height:"400px"}},null,512)])]),_:1})])]),_:1},8,["modelValue"])}}}),Fa=ea(Ra,[["__scopeId","data-v-29c1a590"]]);export{Fa as default};
