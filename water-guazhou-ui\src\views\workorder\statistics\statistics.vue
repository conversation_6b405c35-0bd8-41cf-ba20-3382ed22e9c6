<!-- 统一工单-统计分析-工单统计 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <SLCard class="card-table">
      <div class="card-content">
        <div class="top">
          <div class="table-box">
            <FormTable :config="TableConfig"></FormTable>
          </div>
          <div
            ref="agriEcoDev"
            class="echart-box"
          >
            <VChart
              ref="refChartBar"
              :option="state.barOption"
            ></VChart>
          </div>
        </div>
        <div class="bottom">
          <div
            ref="agriEcoDev"
            class="chart-box"
          >
            <VChart
              ref="refChartLine"
              :option="state.lineOption"
            ></VChart>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import moment from 'moment'
import elementResizeDetectorMaker from 'element-resize-detector'
import { zhuzhuangtu, zhexiantu } from './echart'
import { GetWorkOrderCountStatistic } from '@/api/workorder'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
// import { getUserEventCount } from '../config'
const erd = elementResizeDetectorMaker()
const refChartLine = ref<IECharts>()
const refChartBar = ref<IECharts>()
const agriEcoDev = ref<any>()
const state = reactive<{
  barOption: any
  lineOption: any
}>({
  barOption: null,
  lineOption: null
})

watch(() => useAppStore().isDark, () => {
  state.lineOption.backgroundColor = useAppStore().isDark ? '#131624' : '#F4F7FA'
  state.barOption.backgroundColor = useAppStore().isDark ? '#131624' : '#F4F7FA'
})

const refSearch = ref<ISearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '发起时间',
      options: [
        { label: '日', value: 'date' },
        { label: '月', value: 'month' },
        { label: '年', value: 'year' }
      ],
      field: 'type',
      onChange: () => refreshData()
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'date'
      },
      type: 'date',
      label: '',
      field: 'date'
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'month'
      },
      type: 'month',
      label: '',
      field: 'month'
    },
    {
      clearable: false,
      handleHidden: (params, query, config) => {
        config.hidden = params.type !== 'year'
      },
      type: 'year',
      label: '',
      field: 'year'
    },
    {
      type: 'btn-group',
      btns: [{ perm: true, text: '统计', click: () => refreshData() }]
    }
  ],
  defaultParams: {
    type: 'date',
    month: moment().format('YYYY-MM'),
    year: moment().format('YYYY'),
    date: moment().format('YYYY-MM-DD')
  }
})
const TableConfig = reactive<ITable>({
  border: false,
  columns: [
    { label: '发起人员名称', prop: 'key' },
    { label: '发起事件数量', prop: 'value' }
  ],
  indexVisible: true,
  pagination: {
    hide: true
  },
  dataList: []
})
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}

  refChartLine.value?.clear()
  const type = query?.type || 'date'
  let date: moment.Moment
  switch (type) {
    case 'month':
      date = moment(query[type], 'YYYY-MM')
      break
    case 'year':
      date = moment(query[type], 'YYYY')
      break
    default:
      date = moment(query[type], 'YYYY-MM-DD')
      break
  }
  GetWorkOrderCountStatistic({
    fromTime: date
      .startOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    toTime: date
      .endOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    statisticOrganizer: true,
    statisticType: true
  })
    .then(res => {
      TableConfig.dataList = res.data?.data?.organizers?.data || []
      erd.listenTo(agriEcoDev.value, () => {
        state.barOption = zhuzhuangtu(res.data?.data?.types?.data || [])
        refChartBar.value?.resize()
      })
    })
    .catch(e => {
      console.dir(e)

      TableConfig.dataList = []
      state.barOption = zhuzhuangtu([])
    })
  GetWorkOrderCountStatistic({
    timeUnit: type === 'year' ? 'MONTH' : type === 'month' ? 'DAY' : 'HOUR',
    fromTime: date
      .startOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    toTime: date
      .endOf(type === 'year' ? 'y' : type === 'month' ? 'M' : 'D')
      .valueOf(),
    statisticOrganizer: true,
    statisticType: true
  })
    .then(res => {
      erd.listenTo(agriEcoDev.value, () => {
        state.lineOption = zhexiantu(res.data?.data?.types || [], type)
        refChartLine.value?.resize()
      })
    })
    .catch(() => {
      state.lineOption = zhexiantu([])
    })
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.card {
  height: 100%;
  overflow: auto;
}

.card-content {
  height: 100%;

  .top {
    display: flex;
    height: calc(50% - 10px);
  }

  .table-box {
    height: calc(100% - 10px);
    width: 100%;
  }

  .echart-box {
    height: calc(100% - 10px);
    width: 100%;
  }

  .bottom {
    height: calc(55% - 100px);
    margin: 20px;

    .chart-box {
      height: 100%;
    }
  }
}
</style>
