import { request } from '@/plugins/axios'

/**
 * 查询阀门列表
 * @param params
 * @returns
 */
export const GetDmaPartitionValve = (
  params: IQueryPagerParams & {
    partitionId?: string
    /**
     * 1: 边界阀门，2：分支管线阀门
     */
    type?: string
    code?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/valve/list',
    method: 'get',
    params
  })
}
/**
 * 添加阀门
 * @param params
 * @returns
 */
export const AddDmaPartitionValve = (params: {
  partitionId: string
  maintainDate: string
  remark: string
  img: string
}) => {
  return request({
    url: '/api/spp/dma/partition/valve',
    method: 'post',
    data: params
  })
}
/**
 * 删除阀门
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionValve = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/valve',
    method: 'delete',
    data: ids
  })
}
