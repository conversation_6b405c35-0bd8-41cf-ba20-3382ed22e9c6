import{_ as c}from"./index-C9hz-UZb.js";import{_ as t}from"./Search-NSrhrIa_.js";import{d as _,c as p,a8 as m,h as f,F as u,g as l,q as i,C as g}from"./index-r0dFAfgr.js";const d=_({__name:"CardSearch",props:{config:{}},setup(h,{expose:r}){const o=p(),a=()=>{var e;(e=o.value)==null||e.toggleMore()},s=m(()=>{var e;return(e=o.value)==null?void 0:e.queryParams});return r({queryParams:s,toggleMore:a,resetForm:()=>{var e;(e=o.value)==null||e.resetForm()}}),(e,C)=>{const n=c;return l(),f(n,{class:"card-search-box"},{default:u(()=>[i(t,{ref_key:"refSearch",ref:o,config:e.config},null,8,["config"])]),_:1})}}}),k=g(d,[["__scopeId","data-v-eaf6d082"]]);export{k as _};
