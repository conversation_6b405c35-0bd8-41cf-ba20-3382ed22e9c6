package org.thingsboard.server.dao.base.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement;
import org.thingsboard.server.dao.base.IBaseSchemeManagementService;
import org.thingsboard.server.dao.sql.base.BaseSchemeManagementMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseSchemeManagementPageRequest;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 平台管理-方案管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class BaseSchemeManagementServiceImpl implements IBaseSchemeManagementService {

    @Autowired
    private BaseSchemeManagementMapper baseSchemeManagementMapper;

    /**
     * 查询平台管理-方案管理
     *
     * @param id 平台管理-方案管理主键
     * @return 平台管理-方案管理
     */
    @Override
    public BaseSchemeManagement selectBaseSchemeManagementById(String id) {
        return baseSchemeManagementMapper.selectBaseSchemeManagementById(id);
    }

    /**
     * 查询平台管理-方案管理列表
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 平台管理-方案管理
     */
    @Override
    public IPage<BaseSchemeManagement> selectBaseSchemeManagementList(BaseSchemeManagementPageRequest baseSchemeManagement) {
        return baseSchemeManagementMapper.selectBaseSchemeManagementList(baseSchemeManagement);
    }

    /**
     * 新增平台管理-方案管理
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 结果
     */
    @Override
    public int insertBaseSchemeManagement(BaseSchemeManagement baseSchemeManagement) {
        baseSchemeManagement.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseSchemeManagementMapper.insertBaseSchemeManagement(baseSchemeManagement);
    }

    /**
     * 修改平台管理-方案管理
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 结果
     */
    @Override
    public int updateBaseSchemeManagement(BaseSchemeManagement baseSchemeManagement) {
        return baseSchemeManagementMapper.updateBaseSchemeManagement(baseSchemeManagement);
    }

    /**
     * 批量删除平台管理-方案管理
     *
     * @param ids 需要删除的平台管理-方案管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseSchemeManagementByIds(List<String> ids) {
        return baseSchemeManagementMapper.deleteBaseSchemeManagementByIds(ids);
    }

    /**
     * 删除平台管理-方案管理信息
     *
     * @param id 平台管理-方案管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseSchemeManagementById(String id) {
        return baseSchemeManagementMapper.deleteBaseSchemeManagementById(id);
    }

    /**
     * 获取所有方案
     * @return
     */
    @Override
    public List<BaseSchemeManagement> getAllBaseSchemeManagement() {
        return baseSchemeManagementMapper.getAllBaseSchemeManagement();
    }
}
