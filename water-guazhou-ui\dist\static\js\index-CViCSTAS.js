import{_ as z}from"./TreeBox-DDD2iwoR.js";import{_ as K}from"./index-C9hz-UZb.js";import{d as J,r as g,c as C,l as b,bI as I,bJ as Y,b as U,j as W,o as Z,ay as $,g as T,h as E,F as y,q as d,i as n,cs as B,p as Q,n as X,an as F,dF as ee,dA as te,aq as ae,C as re}from"./index-r0dFAfgr.js";import{_ as oe}from"./CardSearch-CB_HNR-Q.js";import{_ as ie}from"./index-BJ-QPYom.js";import{u as ne}from"./useDetector-BRcb7GRN.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as le}from"./usePartition-DkcY9fQ2.js";import"./index-0NlGN6gS.js";import{E as se}from"./index-Bo22WWST.js";import{E as pe,g as me}from"./statistics-CeyexT_5.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const de=J({__name:"index",setup(ce){const c=g({chartOption:null,activeName:"list"}),k=C(),_=C(),H=C(),D=g([]),h=g({data:[],title:"选择设备",showCheckbox:!0,handleCheck(e,t){h.checkedKeys=t.checkedKeys||[],h.checkedNodes=t.checkedNodes||[],G()}}),A=g({defaultParams:{type:"daterange",queryType:"15m",daterange:[b().subtract(1,"d").format("YYYY-MM-DD"),b().format("YYYY-MM-DD")],month:b().format(I),year:b().format(Y)},filters:[{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"daterange"}],label:"选择方式",onChange:e=>{var t;(t=_.value)!=null&&t.queryParams&&(_.value.queryParams.queryType=e==="year"?"1nc":e==="month"?"1d":"15min")}},{hidden:!0,handleHidden:(e,t,a)=>a.hidden=e.type!=="year",type:"year",label:"选择年份",field:"year",clearable:!1,format:Y,disabledDate(e){return new Date<e}},{handleHidden(e,t,a){a.hidden=e.type!=="year"},type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1月",value:"1nc"}],itemContainerStyle:{width:"180px"}},{hidden:!0,handleHidden:(e,t,a)=>a.hidden=e.type!=="month",type:"month",label:"选择月份",field:"month",clearable:!1,format:I,disabledDate(e){return new Date<e}},{handleHidden(e,t,a){a.hidden=e.type!=="month"},type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1天",value:"1d"}],itemContainerStyle:{width:"180px"}},{hidden:!0,handleHidden:(e,t,a)=>a.hidden=e.type!=="daterange",type:"daterange",label:"选择日期",field:"daterange",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden(e,t,a){a.hidden=e.type!=="daterange"},type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1分钟",value:"1m"},{label:"5分钟",value:"5m"},{label:"15分钟",value:"15m"},{label:"30分钟",value:"30m"},{label:"1小时",value:"1h"}],itemContainerStyle:{width:"180px"}},{type:"btn-group",btns:[{perm:!0,text:"查询",disabled:()=>!!r.loading,click:()=>{q()},iconifyIcon:"ep:search"},{type:"default",perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>{var e;(e=_.value)==null||e.resetForm()}},{perm:!0,text:"导出",type:"warning",disabled:()=>!!r.loading,hide:()=>c.activeName!=="list",click:()=>{q(!0)},iconifyIcon:"ep:download"}]}]}),S=()=>{var t;return((t=h.checkedNodes)==null?void 0:t.filter(a=>a.data.type==="3"))||[]},P=()=>S().map(a=>{var p;return{minWidth:120,label:a.label,prop:(p=a.data)==null?void 0:p.deviceId,unit:"(MPa)"}})||[],G=()=>{const e=P();r.columns=[{minWidth:160,label:"时间",prop:"time"},...e,{label:"最小站点",minWidth:120,prop:"minPoint"},{label:"最小值(MPa)",minWidth:120,prop:"minValue"},{label:"最大站点",minWidth:120,prop:"maxPoint"},{label:"最大值(MPa)",minWidth:120,prop:"maxValue"},{label:"平均值(MPa)",minWidth:120,prop:"avgValue"},{label:"合计(MPa)",minWidth:120,prop:"sumValue"}]},r=g({loading:!1,dataList:[],columns:[],operations:[],pagination:{page:1,limit:200,pageSize:[100,200,300,500],total:0,refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,r.dataList=D==null?void 0:D.slice((e-1)*t,e*t)}}}),q=async e=>{var t,a,p,l,u,o,f,s;if(!((t=h.checkedNodes)!=null&&t.length)){U.warning("请先选择设备");return}r.loading=!0;try{const i=((a=_.value)==null?void 0:a.queryParams)||{},v={page:r.pagination.page,size:r.pagination.limit,deviceIdList:S().map(x=>{var m;return(m=x.data)==null?void 0:m.deviceId}),type:i.type==="daterange"?"time":i.type,interval:i.queryType,start:((p=i.daterange)==null?void 0:p[0])&&b((l=i.daterange)==null?void 0:l[0]).startOf("D").valueOf(),end:((u=i.daterange)==null?void 0:u[1])&&b((o=i.daterange)==null?void 0:o[1]).endOf("D").valueOf(),month:i.month,year:i.year,queryType:"pressure"};if(e){const x=await pe(v);se(x.data,"压力分析报表")}else{const m=(f=(await me(v)).data)==null?void 0:f.data;r.dataList=((s=m==null?void 0:m.data)==null?void 0:s.map(N=>{var V;const L={...N};return(V=N.data)==null||V.map(O=>{L[O.id]=O.value}),L}))||[],r.pagination.total=(m==null?void 0:m.total)||0,R(P())}}catch{}r.loading=!1},R=e=>{var u;if(c.activeName!=="echarts")return;const t={grid:{left:50,right:50,top:50,bottom:80},legend:{type:"scroll",textStyle:{color:"#666",fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:["--"]},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100}],yAxis:[{position:"left",type:"value",name:"出口压力(Mpa)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:W().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}],series:[]},a=r.dataList.filter(o=>!!o.time),p=a.map(o=>o.time);t.xAxis.data=p;const l={};a.map(o=>{var f;(f=o.data)==null||f.map(s=>{l[s.id]?l[s.id].push(s.value):l[s.id]=[s.value]})}),t.series=e.map(o=>({name:o.label,smooth:!0,data:l[o.prop]||[],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}})),(u=k.value)==null||u.clear(),c.chartOption=t},j=ne(),M=le(),w=C();return Z(async()=>{await M.getDeviceTree({type:"flow"}),h.data=M.DeviceTree.value,j.listenToMush(w.value,()=>{var e;(e=k.value)==null||e.resize()})}),(e,t)=>{const a=ie,p=oe,l=ee,u=te,o=$("VChart"),f=ae,s=K,i=z;return T(),E(i,null,{tree:y(()=>[d(a,{ref:"refTree","tree-data":n(h)},null,8,["tree-data"])]),default:y(()=>[d(p,{ref_key:"refSearch",ref:_,config:n(A)},null,8,["config"]),d(s,{class:"card-table",title:" "},{right:y(()=>[d(u,{modelValue:n(c).activeName,"onUpdate:modelValue":t[0]||(t[0]=v=>n(c).activeName=v),onChange:t[1]||(t[1]=()=>q())},{default:y(()=>[d(l,{label:"echarts"},{default:y(()=>[d(n(B),{icon:"clarity:line-chart-line"})]),_:1}),d(l,{label:"list"},{default:y(()=>[d(n(B),{icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:y(()=>[Q("div",{ref_key:"refDiv",ref:w,class:"content"},[n(c).activeName==="echarts"?(T(),X("div",{key:0,ref_key:"refDiv",ref:w,class:"chart-box"},[d(o,{ref_key:"refChart",ref:k,theme:n(W)().isDark?"dark":"light",option:n(c).chartOption},null,8,["theme","option"])],512)):F("",!0),n(c).activeName==="list"?(T(),E(f,{key:1,ref_key:"refCardTable",ref:H,class:"card-table",config:n(r)},null,8,["config"])):F("",!0)],512)]),_:1})]),_:1})}}}),Wt=re(de,[["__scopeId","data-v-a9d0e5c8"]]);export{Wt as default};
