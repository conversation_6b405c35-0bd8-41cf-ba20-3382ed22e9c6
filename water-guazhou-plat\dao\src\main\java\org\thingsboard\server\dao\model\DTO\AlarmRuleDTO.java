package org.thingsboard.server.dao.model.DTO;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleUser;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 报警Version2 报警规则
 */
@Data
public class AlarmRuleDTO implements Serializable {

    @Id
    private String id;

    private String type;

    private String title;

    private String stationId;

    private String stationAttrId;

    private String ruleType;

    private String ruleParam;

    private String status;

    private String alarmLevel;

    private String alarmType;

    private String processMethod;

    private String remoteStationAttrId;

    private String remoteVideoId;

    private Integer reAlarmValue;

    private String reAlarmType;

    private String reAlarmUnit;

    private String sendWay;

    private Date createTime;

    private String tenantId;

    private String stationAttrName;

    private String stationName;

    private JSONObject ruleParamObj;

    private List<AlarmRuleUser> msgList = new ArrayList<>();

    private List<AlarmRuleUser> appList = new ArrayList<>();

}
