import { request } from '@/plugins/axios'

/**
 * 查询消防栓列表
 * @param params
 * @returns
 */
export const GetDmaPartitionFireHydrant = (
  params: IQueryPagerParams & {
    partitionId?: string
    code?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/fireHydrant/list',
    method: 'get',
    params
  })
}
/**
 * 添加消防栓
 * @param params
 * @returns
 */
export const AddDmaPartitionFireHydrant = (params: {
  partitionId: string
  code: string
  address: string
  type: string
  runStatus: string
  remark: string
  img: string
}) => {
  return request({
    url: '/api/spp/dma/partition/fireHydrant',
    method: 'post',
    data: params
  })
}
/**
 * 删除消防栓
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionFireHydrant = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/fireHydrant',
    method: 'delete',
    data: ids
  })
}
