<!-- 小流分析 -->
<template>
  <div class="view">
    <Search
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      ref="refTable"
      class="card-table"
      :config="cardTableConfig"
    />
  </div>
</template>
<script lang="ts" setup>
import { Refresh, Search as SearchIcon, Download, Filter } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { filterTime } from './data/data'

const refSearch = ref<ISearchIns>()
const refTable = ref<ICardTableIns>()
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    filterStart: [0, 23],
    date: [dayjs().format(), dayjs().format()]
  },
  filters: [
    {
      type: 'select',
      label: '统计类型',
      field: 'va1',
      width: '140px',
      options: [
        { label: '净累计', value: '净累计' }
      ]
    },
    { type: 'daterange', label: '日期', field: 'date' },
    {
      label: '时间',
      type: 'range',
      rangeType: 'select',
      field: 'filterStart',
      options: JSON.parse(JSON.stringify(filterTime)),
      startPlaceHolder: '0时',
      endPlaceHolder: '23时',
      startOptionDisabled: (option, end) => {
        return end && Number(end) < option.value
      },
      endOptionDisabled: (option, start) => {
        return start && option.value <= Number(start)
      }
    },
    { type: 'input-number', label: '最小流量', field: 'v2' }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', svgIcon: shallowRef(SearchIcon), click: () => refreshData() },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})

// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'differenceRate', label: '日期', formatter: (row: any, value: any) => { return dayjs(value).format('YYYY-MM-DD') } },
    { prop: 'name', label: '起始读数' },
    { prop: 'outletTotalFlow', label: '结束读数', unit: '(m³/h)' },
    { prop: 'inletTotalFlow', label: '用水量', unit: '(m³)', sortable: true },
    { prop: 'differenceTotalFlow', label: '最大瞬时流量', sortable: true },
    { prop: 'differenceTotalFlow', label: '最小瞬时流量', sortable: true },
    { prop: 'differenceRate', label: '统计' }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

const refreshData = async () => {
  //
}
</script>
<style lang="scss" scoped>
.vuew{
  height: 100%;
}
.card-table {
  margin-top: 10px;
  height: calc(100% - 64px);
}
</style>
