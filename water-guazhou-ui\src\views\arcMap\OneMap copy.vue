<template>
  <RightDrawerMap
    ref="refMap"
    :title="state.title"
    :hide-coords="true"
    :hide-right-drawer="true"
    :right-drawer-width="state.rightWidth"
    :right-drawer-absolute="true"
    :pops="state.pops"
    @map-loaded="onMapLoaded"
    @pop-toggle="(pop, flag) => (pop.visible = flag)"
  >
    <template #map-bars>
      <div class="custom-menubar">
        <VerticalBar
          :collapse-on-click="true"
          :menus="state.menus"
          @change="handleVerticalBarChange"
        />
      </div>
      <div class="custom-submenubar">
        <HorizontalBar
          :menu="state.hMenu"
          @click="handleHorizontalBarClick"
        />
      </div>
    </template>
    <template #detail-header>
      <span>{{ state.detailTitle }}</span>
    </template>
    <template #detail-default>
      <template
        v-for="item in state.menus"
        :key="item.path"
      >
        <template
          v-for="cItem in item.children"
          :key="cItem.path"
        >
          <component
            :is="cItem.detailComponent"
            v-if="state.detailType === cItem.path && cItem.detailComponent"
            :ref="'refDetail' + cItem.path"
            @refresh="row => (state.detailTitle = row.title)"
          ></component>
        </template>
      </template>
    </template>
  </RightDrawerMap>

  <SLDrawer
    ref="refDrawer"
    :config="DrawerConfig"
  >
    <div class="one-map-bg">
      <div class="one-map-page">
        <template
          v-for="item in state.menus"
          :key="item.path"
        >
          <template
            v-for="cItem in item.children"
            :key="cItem.path"
          >
            <template v-if="cItem.path === state.curPath && cItem.component">
              <component
                :is="cItem.component"
                :menu="cItem"
                :view="staticState.view"
                @highlight-mark="highlightMark"
                @add-marks="addMarks"
              ></component>
            </template>
          </template>
        </template>
      </div>
      <div class="one-map-detail">
        <template
          v-for="item in state.menus"
          :key="item.path"
        >
          <template
            v-for="cItem in item.children"
            :key="cItem.path"
          >
            <component
              :is="cItem.detailComponent"
              v-if="state.detailType === cItem.path && cItem.detailComponent"
              :ref="'refDetail' + cItem.path"
              @refresh="row => (state.detailTitle = row.title)"
            ></component>
          </template>
        </template>
      </div>
    </div>
  </SLDrawer>
</template>
<script lang="ts" setup>
import {
  createPictureMarker,
  getGraphicLayer,
  gotoAndHighLight
} from '@/utils/MapHelper'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import { useHighLight } from '@/hooks/arcgis'
import { delay } from '@/utils/GlobalHelper'
import VerticalBar from './components/menus/VerticalBar.vue'
import HorizontalBar from './components/menus/HorizontalBar.vue'
import { getOneMapMenus } from './config'
import SLDrawer from '@/components/SLDrawer/index.vue'
import { getMapLocationImageUrl } from '@/utils/URLHelper'

const { proxy }: any = getCurrentInstance()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refDrawer = ref<InstanceType<typeof SLDrawer>>()
const highLight = useHighLight()
const state = reactive<{
  title: string
  detailTitle: string
  menus: IMenuItem[]
  hMenu?: IMenuItem
  curPath: string
  detailType: string
  pageConfig: { path: string; layerIds: string[] }[]
  pops: IArcPopConfig[]
  rightWidth: number
}>({
  title: '',
  curPath: '',
  detailTitle: '',
  pageConfig: [],
  menus: [],
  pops: [],
  detailType: '',
  rightWidth: 460
})
const staticState: {
  view?: __esri.MapView
  layers: __esri.GraphicsLayer[]
  highlightGraphic?: __esri.Graphic
} = {
  layers: []
}
const DrawerConfig = reactive<IDrawerConfig>({
  width: 460,
  group: [],
  title: '',
  escapeClose: false,
  modal: false,
  showClose: false,
  withHeader: false,
  modalClass: 'one-map-drawer',
  cancel: false
})
const handleVerticalBarChange = (menu: IMenuItem) => {
  /** 多次点击同一个菜单直接返回 */
  if (state.hMenu?.path === menu.path) return
  if (menu.path === state.hMenu?.path) return
  staticState.view?.map.removeMany(staticState.layers)
  highLight.destroy()
  staticState.layers.length = 0
  state.pops.length = 0
  refMap.value?.toggleCustomDetail(false)
  state.hMenu = menu
  menu.children?.length && handleHorizontalBarClick(menu.children[0], true)
}
const handleHorizontalBarClick = (menu: IMenuItem, isChecked: boolean) => {
  state.curPath = menu.path
  state.title = menu.meta.title
  let layer = staticState.layers.find(item => item.id === menu.path)
  if (!layer) {
    layer = getGraphicLayer(staticState.view, {
      id: menu.path,
      title: menu.meta.title
    })
    layer && staticState.layers.push(layer)
    bindHighlightMark()
  }
  layer && (layer.visible = isChecked)
  DrawerConfig.width = 460
}

const hightCallback = async (graphic?: __esri.Graphic) => {
  staticState.highlightGraphic = graphic
  const id = graphic?.attributes.id || ''
  state.detailType = graphic?.attributes?.path || ''
  await nextTick()
  if (!state.detailType) return
  refMap.value?.toggleCustomDetail(true)
  refMap.value?.openPop(id)
  // 触发具体详情页面的刷新方法
  // 相同id表示是同一个要素，不多次进行数据刷新
  // if (staticState.highlightGraphic?.attributes.id === graphic?.attributes.id) return
  refreshDetail()
}
const refreshPipeDetail = async (tab: string, sql?: string) => {
  DrawerConfig.width = 1460
  const opend = refMap.value?.isCustomOpened()
  if (!opend) {
    refMap.value?.toggleCustomDetail(true)
    await delay(600)
  }
  const refDetail = proxy.$refs['refDetail' + state.detailType]
  refDetail?.[0].refreshDetail(staticState.view, tab, {
    where: sql
  })
}
const refreshDetail = async () => {
  DrawerConfig.width = 1460
  await nextTick()
  const refDetail = proxy.$refs['refDetail' + state.detailType]
  refDetail?.length
    && refDetail[0].refreshDetail(staticState.highlightGraphic?.attributes?.row)
}
const bindHighlightMark = () => {
  highLight.bindHoverHighLight(
    staticState.view,
    staticState.layers,
    hightCallback
  )
}
const highlightMark = async (
  menu: IMenuItem,
  id: string,
  customTime?: string
) => {
  // 如果是管网服务来的由显示数据table
  if (menu.path.startsWith('sbzc')) {
    state.detailTitle = (menu.meta.title || '') + (customTime ? '(' + customTime + ')' : '')
    state.detailType = menu.path || ''
    refreshPipeDetail(menu.meta.title, id)
  } else {
    const layer = staticState.layers.find(item => item.id === menu.path)
    const graphic = layer?.graphics.find(item => item.attributes?.id === id)
    if (!layer || !graphic || !staticState.view) return
    await gotoAndHighLight(staticState.view, graphic, { avoidHighlight: true })
    highLight.highlight(staticState.view, graphic, hightCallback)
  }
}
/**
 * 增加气泡
 * @param menu
 * @param data
 */
const addMarks = (options?: { windows: IArcPopConfig[] }) => {
  const path = state.curPath
  if (!path) return
  state.pops = state.pops.filter(
    item => item.attributes.customConfig?.path !== path
  )
  const data = options?.windows || []
  state.pops.push(...data)
  const layer = getGraphicLayer(staticState.view, {
    id: path
  })
  if (!layer) return
  layer.removeAll()
  const graphics = data
    .filter(item => item.symbolConfig)
    .map(item => {
      const graphic = createPictureMarker(item.x, item.y, {
        picUrl: item.symbolConfig?.url || getMapLocationImageUrl(),
        spatialReference: staticState.view?.spatialReference,
        attributes: item.attributes,
        picSize: [
          item.symbolConfig?.width || 20,
          item.symbolConfig?.height || 25
        ],
        xOffset: item.symbolConfig?.xoffset || 0,
        yOffset: item.symbolConfig?.yoffset || 13
      })
      return graphic
    })
  layer.addMany(graphics)
}
const onMapLoaded = (view?: __esri.MapView) => {
  staticState.view = view
  state.menus = getOneMapMenus()?.children || []
  state.hMenu = state.menus[0]
  const hChildren = state.hMenu.children || []
  handleHorizontalBarClick(hChildren[0], true)
}
onMounted(() => {
  refDrawer.value?.openDrawer()
})
</script>
<style lang="scss" scoped>
.custom-menubar {
  position: absolute;
  width: 120px;
  top: 15px;
  left: 15px;
}
.custom-submenubar {
  position: absolute;
  left: 150px;
  top: 15px;
}
.one-map-bg {
  width: 100%;
  display: inline-block;
  .one-map-page,
  .one-map-detail {
    vertical-align: bottom;
  }
  .one-map-page {
    width: calc(460px - 2 * var(--el-drawer-padding-primary));
    height: 100%;
  }
}
.dark,
.darkblue {
  .one-map-detail {
    background-color: transparent;
  }
}
.one-map-detail {
  width: 1000px;
  background-color: #f4f7fa;
}
</style>
