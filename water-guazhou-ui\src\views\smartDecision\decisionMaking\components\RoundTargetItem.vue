<template>
  <div class="item">
    <div class="ball">
      <span :class="config.status">{{ config.value }}</span>
    </div>
    <TargetItem :config="config"></TargetItem>
  </div>
</template>

<script lang="ts" setup>
import TargetItem from './TargetItem.vue'

defineProps<{ config: ITargetItem }>()
</script>

<style lang="scss" scoped>
.item {
  width: 148px;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 24px;
  .ball {
    background-image: url('../../imgs/small_circle.png');
    background-size: 100%;
    background-position: top;
    background-repeat: no-repeat;
    width: 80px;
    height: 80px;
    text-align: center;
    line-height: 80px;
    font-size: 22px;
  }
}
</style>
