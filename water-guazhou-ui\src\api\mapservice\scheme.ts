import { request } from '@/plugins/axios'

/**
 * 查询gis方案列表
 * @param params
 * @returns
 */
export const GetGisSchemeList = (
  params: Partial<{
    name: string
    type: string
    page: number
    size: number
  }>
) => {
  return request({
    url: '/api/gis/plan/list',
    method: 'get',
    params
  })
}
/**
 * 保存方案数据
 * @param params
 * @returns
 */
export const PostGisScheme = (params: any) => {
  return request({
    url: '/api/gis/plan',
    method: 'post',
    data: params
  })
}

/**
 * 删除方案列表
 * @param ids
 * @returns
 */
export const DeleteGisScheme = (ids: string[]) => {
  return request({
    url: '/api/gis/plan/remove',
    method: 'delete',
    data: ids
  })
}
