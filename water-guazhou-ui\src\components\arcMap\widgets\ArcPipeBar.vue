<!-- 管线统计柱状图 -->
<template>
  <div
    ref="refContainer"
    :style="computedStyle"
  >
    <VChart
      ref="refChart"
      :option="option"
    ></VChart>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts'
import { IECharts } from '@/plugins/echart'
import { useGisStore } from '@/store'
import { EStatisticField, EStatisticType, statistic } from '@/utils/MapHelper'
import { zhuzhuangtu } from '@/views/workorder/statistics/echart'

const gisStore = useGisStore()
const props = defineProps<{
  layerIds: any[]
  group_fields: string[]
  statistic_field: EStatisticField
  /** 1:数量; 2：长度 */
  statistic_type: EStatisticType
  prefix?: string
  height?: number
  width?: number
}>()
const computedStyle = computed(() => {
  return {
    height: props.height ? props.height + 'px' : '100%',
    width: props.width ? props.width + 'px' : '100%'
  }
})
const option = ref<any>()
watch(
  () => gisStore.gLayerInfos,
  () => {
    refreshChart()
  }
)
const refreshChart = async () => {
  let res: any[] = []
  if (gisStore.gLayerInfos?.length) {
    res = await statistic({
      layerIds: props.layerIds,
      statistic_field: props.statistic_field,
      statistic_type: props.statistic_type,
      group_fields: props.group_fields
    })
  }
  const barData = res.map(item => {
    return {
      value: item.value,
      key: (props.prefix || '') + (item.label || '--')
    }
  }) || []
  option.value = zhuzhuangtu(barData, props.statistic_type === '1' ? '个' : 'm')
}
const detector = useDetector()
const refChart = ref<IECharts>()
const refContainer = ref<HTMLDivElement>()
onMounted(() => {
  refreshChart()
  detector.listenTo(refContainer.value, refChart)
})
</script>
<style lang="scss" scoped></style>
