package org.thingsboard.server.dao.smartService.portal;

import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalCompanyIntroduce;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCompanyIntroduceSaveRequest;

public interface SsPortalCompanyIntroduceService {
    SsPortalCompanyIntroduce get(String tenantId);

    SsPortalCompanyIntroduce save(SsPortalCompanyIntroduceSaveRequest entity);

    boolean update(SsPortalCompanyIntroduce entity);

    boolean delete(String id);

}
