package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_process_type")
@Data
public class ProcessType {
    @TableId
    private String id;

    private String code;

    private String name;

    private String type;

    private String remark;

    private Boolean status;

    private String tenantId;

    private transient List<ProcessContractTemplate> contractTemplateList;

}
