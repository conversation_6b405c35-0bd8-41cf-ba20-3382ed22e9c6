/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.RepairId;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.REPAIR_NAME)
public class RepairEntity extends BaseSqlEntity<Repair> {

    @Column(name = ModelConstants.REPAIR_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.REPAIR_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.REPAIR_REPAIR_START_TIME)
    private Long repairStartTime;

    @Column(name = ModelConstants.REPAIR_REPAIR_END_TIME)
    private Long repairEndTime;

    @Column(name = ModelConstants.REPAIR_STATUS)
    private String status;

    @Column(name = ModelConstants.REPAIR_REMARK)
    private String remark;

    @Column(name = ModelConstants.REPAIR_TENANT_ID)
    private String tenantId;

    public RepairEntity() {
    }

    public RepairEntity(Repair repair) {
        if (repair.getId() != null) {
            this.id = toString(repair.getId().getId());
        }
        if (repair.getDeviceId() != null) {
            this.deviceId = toString(repair.getDeviceId().getId());
        }
        if (repair.getTenantId() != null) {
            this.tenantId = toString(repair.getTenantId().getId());
        }
        this.createTime = repair.getCreatedTime();
        this.remark = repair.getRemark();
        this.repairEndTime = repair.getRepairEndTime();
        this.repairStartTime = repair.getRepairStartTime();
        this.status = repair.getStatus();
    }

    @Override
    public Repair toData() {
        Repair repair = new Repair(new RepairId(getId()));
        repair.setDeviceId(new DeviceId(toUUID(deviceId)));
        repair.setCreatedTime(createTime);
        repair.setRemark(remark);
        repair.setRepairEndTime(repairEndTime);
        repair.setRepairStartTime(repairStartTime);
        repair.setStatus(status);
        repair.setTenantId(new TenantId(toUUID(tenantId)));

        return repair;
    }
}
