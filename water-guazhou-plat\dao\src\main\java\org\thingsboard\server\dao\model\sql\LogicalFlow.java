package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

@Data
@Entity
@EqualsAndHashCode
@Table(name = ModelConstants.TABLE_LOGICAL_FLOW)
public class  LogicalFlow {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.NAME)
    private String name;

    /**
     * 逻辑流程类型
     * 云端触发: CLOUD
     * 普通触发: LOCAL
     */
    @Column(name = ModelConstants.LOGICAL_FLOW_TYPE)
    private String type;

    @Column(name = ModelConstants.LOGICAL_FLOW_REMARK)
    private String remark;

    @Column(name = ModelConstants.LOGICAL_FLOW_CREATER)
    private String creater;

    @Column(name = ModelConstants.CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.LOGICAL_FLOW_PARENT_ID)
    private String parentId;

    @Column(name = ModelConstants.GATEWAY_ID_PROPERTY)
    private String gatewayId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.LOGICAL_FLOW_STATUS)
    private String status;

    @Column(name = ModelConstants.LOGICAL_FLOW_STOP_FORCE)
    private String stopForce;

}
