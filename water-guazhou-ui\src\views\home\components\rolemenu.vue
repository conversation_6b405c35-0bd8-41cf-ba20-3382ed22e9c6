<template>
  <el-dialog
    v-model="state.visible"
    title="常用功能"
    width="60%"
    :close-on-click-modal="false"
    @close="props.tableConfig?.close"
  >
    <el-tabs
      v-model="state.app"
      tab-position="left"
      style="height: 500px; margin-top: 20px"
      @tab-click="handleTabClick"
    >
      <el-tab-pane
        v-for="(item, i) in useBusinessStore().curNavs"
        :key="i"
        :label="item.name"
        :name="item.id"
      >
        <div v-loading="state.loading" class="content-box">
          <el-scrollbar v-if="state.data?.length > 0" height="500px">
            <el-tree
              :ref="
                (el) => {
                  menuTree[item.id] = el;
                }
              "
              :default-checked-keys="userChange[state.app]"
              :data="state.data"
              class="menu-tree"
              show-checkbox
              node-key="id"
              :default-expand-all="true"
            ></el-tree>
          </el-scrollbar>
          <el-empty v-else :image-size="150" description="暂无"></el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" type="primary" @click="clickSaveMenuToRole"
          >保存</el-button
        >
        <el-button size="small" @click="clearMenuTree">清空</el-button>
        <el-button size="small" @click="props.tableConfig.close"
          >取 消</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useBusinessStore, useUserStore } from '@/store';
import { saveUser } from '@/api/user';
import { GetApplicationAsyncRoutes } from '@/api/menu/source';

const userStore = useUserStore();
const emit = defineEmits(['refresh']);
const props = defineProps<{
  tableConfig: any;
}>();
const menuTree = ref<any>([]);
const state = reactive<{
  userdata: any[];
  data: any[];
  selectNodes: any[];
  enterpriseoptions: any[];
  enterprise: string;
  appoptions: any[];
  app: string;
  apps: any[];
  selectedApps: any[];
  selectedAppItems: any[];
  visible: boolean;
  loading: boolean;
  userInfo: any;
}>({
  userdata: [],
  data: [],
  selectNodes: [],
  enterpriseoptions: [],
  enterprise: '',
  appoptions: [],
  app: '',
  apps: [],
  selectedApps: [],
  selectedAppItems: [],
  visible: true,
  loading: false,
  userInfo: {
    ...(userStore.user || {}),
    additionalInfo: { ...(userStore.user?.additionalInfo || {}) }
  }
});

// 用户选中的菜单
const userChange = ref({});

const shortcutMenu = ref({});

onMounted(() => {
  state.appoptions = [];
  state.app = useBusinessStore().curNavs[0].id as any;
  state.apps = [];
  state.selectedApps = [];
  state.selectedAppItems = [];
  getApplicationTree(state.app);
});

const handleTabClick = async (tab, event) => {
  userChange.value[state.app] = menuTree.value[state.app].getCheckedKeys();
  state.app = tab.props.name || '';
  getApplicationTree(state.app);
};

// 保存
const clickSaveMenuToRole = async () => {
  userChange.value[state.app] = menuTree.value[state.app].getCheckedKeys();
  shortcutMenu.value[state.app] = [...userChange.value[state.app]];
  shortcutMenu.value[state.app].forEach((item, index) => {
    traverseObject(state.data, item, index);
  });
  state.userInfo.additionalInfo.menu = shortcutMenu.value;
  try {
    await saveUser(state.userInfo);
    ElMessage.success('保存成功');
    emit('refresh');
  } catch (error) {
    ElMessage.warning('保存失败');
  }
};

// 获取当前应用的菜单
const getApplicationTree = async (appId) => {
  if (appId) state.app = appId;
  state.loading = true;
  GetApplicationAsyncRoutes(state.app)
    .then((res) => {
      state.data = traverse(res.data, 'children', { label: 'meta.title' });
      // 获取当前应用拥有的菜单
      getuserTree();
    })
    .finally(() => {
      state.loading = false;
    });
};

// 获取用户当前的路由
const getuserTree = () => {
  //   userChange.value[state.app]
};
// 清空选择的节点
const clearMenuTree = () => {
  menuTree.value[state.app]?.setCheckedKeys([]);
};

// 树结构转换
function traverse(
  val,
  children: 'children' | string = 'children',
  keys: { label: 'name'; value: 'id' } | any = { label: 'name' }
) {
  val.map((obj) => {
    if (obj) {
      for (const i in keys) {
        obj[i] = obj.meta.title;
      }
      obj['value'] = { name: obj.meta.title, value: obj.id };
      if (obj[children] && obj[children].length) {
        traverse(obj[children], children, keys);
      }
    }
    return obj;
  });
  return val;
}

function traverseObject(val, key, index) {
  val.forEach((obj) => {
    if (obj) {
      if (key === obj.id) {
        shortcutMenu.value[state.app][index] = {
          label: obj.meta.title,
          value: obj.name,
          component: obj.component,
          id: obj.id,
          children: !!obj.children
        };
      }
      if (obj.children && obj.children.length) {
        traverseObject(obj.children, key, index);
      }
    }
    return obj;
  });
  return val;
}

onMounted(() => {
  const value = (userStore.user as any).additionalInfo?.menu || [];
  for (const i in value) {
    userChange.value[i] = [];
    value[i].forEach((item) => {
      userChange.value[i].push(item.id);
    });
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-tab-pane) {
  height: 100%;
}
.content-box {
  height: 100%;
}
.menu-tree {
  margin-top: 20px;
  width: calc(100% - 20px);
}
:deep(.el-tabs__nav-wrap) {
  &.is-left {
    padding: 0;
  }
  .el-tabs__nav-scroll {
    .el-tabs__nav {
      .el-tabs__item {
        text-align: right;
      }
    }
  }
}
.app-auth-title {
  color: #409eff;
  margin: 5px 0;
}
:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: center;
  .el-checkbox {
    flex-basis: 220px;
  }
}
// .el-tabs {
//   background-color: #222536 !important;
// }
:deep(.el-tabs__content) {
  width: calc(100% - 250px) !important;
  height: 100% !important;
}

// :deep(.el-card__body) {
//   background: #383d51 !important;
// }
</style>
