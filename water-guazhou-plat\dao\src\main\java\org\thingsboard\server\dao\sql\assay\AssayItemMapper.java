package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.AssayItemListRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayItem;

@Mapper
public interface AssayItemMapper extends BaseMapper<AssayItem> {
    IPage<AssayItem> findList(IPage<AssayItem> pageRequest, @Param("param") AssayItemListRequest request);
}
