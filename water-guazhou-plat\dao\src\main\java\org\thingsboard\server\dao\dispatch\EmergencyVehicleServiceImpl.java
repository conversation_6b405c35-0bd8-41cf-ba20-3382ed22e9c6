package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyVehicle;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyVehicleMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehiclePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehicleSaveRequest;

@Service
public class EmergencyVehicleServiceImpl implements EmergencyVehicleService {
    @Autowired
    private EmergencyVehicleMapper mapper;

    @Override
    public IPage<EmergencyVehicle> findAllConditional(EmergencyVehiclePageRequest request) {
        IPage<EmergencyVehicle> vehicles = mapper.findByPage(request);
        return vehicles;
    }

    @Override
    public EmergencyVehicle save(EmergencyVehicleSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(EmergencyVehicle entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
