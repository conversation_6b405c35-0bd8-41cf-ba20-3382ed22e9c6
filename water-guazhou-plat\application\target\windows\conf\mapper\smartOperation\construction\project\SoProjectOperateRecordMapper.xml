<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectOperateRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        record.id,
        record.code,
        case
            when record.code like 'Q%' then
                (select name from so_project where code = record.code and tenant_id = record.tenant_id)
            when record.code like 'S%' then
                (select name from so_construction where code = record.code and tenant_id = record.tenant_id)
            end "name",
        case
            when record.code like 'Q%' then
                (select type_id from so_project where code = record.code and tenant_id = record.tenant_id)
            when record.code like 'S%' then
                (select type_id from so_construction where code = record.code and tenant_id = record.tenant_id)
            end type_id,
        case
            when record.code like 'Q%' then
                (select name
                 from so_general_type type
                 where id = (select type_id
                             from so_project
                             where code = record.code
                               and tenant_id = record.tenant_id))
            when record.code like 'S%' then
                (select name
                 from so_general_type type
                 where id = (select type_id
                             from so_construction
                             where code = record.code
                               and tenant_id = record.tenant_id))
            end type_name,
        record.detail,
        record.creator,
        record.create_time,
        record.remark,
        record.tenant_id
        <!--@sql from so_project_operate_record record -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type_id" property="type"/>
        <result column="type_name" property="typeName"/>
        <result column="detail" property="detail"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_project_operate_record record
        <where>
            <if test="code != null and code != ''">
                and record.code like '%' || #{code} || '%'
            </if>
            <if test="name != null and name != ''">
                <!--@formatter:off-->
                and case
                when record.code like 'Q%' then
(select count(1) > 0 from so_project where so_project.code = record.code and tenant_id = record.tenant_id and name like '%' || #{name} || '%')
                when record.code like 'S%' then
(select count(1) > 0 from so_construction where so_construction.code = record.code and tenant_id = record.tenant_id and name like '%' || #{name} || '%')
                end
                <!--@formatter:on-->
            </if>
            <if test="fromTime != null">
                and record.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and record.create_time &lt;= #{toTime}
            </if>
            and record.tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>
</mapper>