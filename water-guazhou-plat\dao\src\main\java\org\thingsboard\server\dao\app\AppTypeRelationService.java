package org.thingsboard.server.dao.app;

import org.thingsboard.server.dao.model.sql.AppTypeRelation;

import java.util.List;

public interface AppTypeRelationService {
    AppTypeRelation findById(String id);

    boolean assignMenuToAppType(String appTypeId, List<String> menuPoolId);

    List<AppTypeRelation> findByAppTypeId(String appTypeId);

    List<String> findMenuByAppTypeId(String appTypeId);
}
