<!-- 用水量修正记录 -->
<template>
  <DialogForm
    ref="refRecord"
    :config="RecordConfig"
  ></DialogForm>
</template>
<script lang="ts" setup>
import { GetDMAReadMeterDataRecords, ExportDMAReadMeterDataRecords } from '@/api/mapservice/dma'
import { IDialogFormIns } from '@/components/type'
import { saveAs } from '@/utils/printUtils'

const refRecord = ref<IDialogFormIns>()
const TableConfig_Records = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '抄表记号', prop: 'custCode' },
    { minWidth: 120, label: '用户名称', prop: 'custName' },
    { minWidth: 120, label: '联系方式', prop: 'phone' },
    { minWidth: 120, label: '抄表年月', prop: 'ym' },
    { minWidth: 120, label: '修正水量', prop: 'correctWater' }
  ],
  height: 400,
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Records.pagination.page = page
      TableConfig_Records.pagination.limit = size
      refreshRecords()
    }
  }
})
const refreshRecords = async (isExport?: boolean) => {
  try {
    const params = {
      page: TableConfig_Records.pagination.page || 1,
      size: TableConfig_Records.pagination.limit || 20,
      custName: refRecord.value?.refForm?.dataForm?.name
    }
    if (isExport) {
      const res = await ExportDMAReadMeterDataRecords(params)
      saveAs(res.data, '用水量修正记录')
    } else {
      TableConfig_Records.loading = true
      const res = await GetDMAReadMeterDataRecords(params)
      const data = res.data?.data
      TableConfig_Records.dataList = data?.data || []
      TableConfig_Records.pagination.total = data?.total || 0
    }
  } catch (error) {
    //
  }
  TableConfig_Records.loading = false
}
const RecordConfig = reactive<IDialogFormConfig>({
  title: '修正记录',
  labelWidth: 70,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '用户名称',
          field: 'name',
          inputStyle: {
            width: '200px'
          },
          extraFormItem: [
            {
              type: 'btn-group',
              btns: [
                {
                  perm: true,
                  text: '查询',
                  styles: {
                    marginLeft: '20px'
                  },
                  iconifyIcon: 'ep:search',
                  click: () => refreshRecords()
                },
                {
                  perm: true,
                  text: '导出',
                  type: 'success',
                  iconifyIcon: 'ep:download',
                  click: () => refreshRecords(true)
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fields: [{ type: 'table', config: TableConfig_Records }]
    }
  ]
})
onMounted(() => {
  refreshRecords()
})
defineExpose({
  refRecord
})
</script>
<style lang="scss" scoped></style>
