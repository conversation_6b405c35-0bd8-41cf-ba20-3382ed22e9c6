package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskReportRequest;
import org.thingsboard.server.dao.util.imodel.query.Requestible;

@Getter
@Setter
public class CircuitTaskReportCompleteRequest implements Requestible {
    // 报告Id
    private String reportId;

    // 图片
    private String image;

    // 视频
    private String video;

    // 音频
    private String audio;

    // 附件
    private String file;

    public static CircuitTaskReportCompleteRequest fromSMCircuitTaskReportRequest(SMCircuitTaskReportRequest req, String reportId) {
        CircuitTaskReportCompleteRequest result = new CircuitTaskReportCompleteRequest();
        result.setReportId(reportId);
        result.setImage(req.getImgUrl());
        result.setVideo(req.getVideoUrl());
        result.setAudio(req.getAudioUrl());
        result.setFile(req.getOtherFileUrl());
        return result;
    }

}