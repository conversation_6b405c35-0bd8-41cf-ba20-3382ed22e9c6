package org.thingsboard.server.dao.sql.auditRecord;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.thingsboard.server.dao.model.sql.AuditRecordEntity;
import org.thingsboard.server.dao.util.SqlDao;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-01-19
 */
@SqlDao
public interface AuditRecordRepository extends JpaRepository<AuditRecordEntity, String>, JpaSpecificationExecutor<AuditRecordEntity> {

    /* @Query("SELECT a FROM AuditRecordEntity a where" +
            " if(:record.username is not null and :record.username !='', a.username like concat('%', concat(:record.username, '%')))" +
            " and if(:record.target is not null and :record.target !='', a.target like concat('%', concat(:record.target, '%')))" +
            " and if(:record.result is not null and :record.result !='', a.result like concat('%', concat(:record.result, '%')))" +
            " and if(:record.event_analysis is not null and :record.event_analysis !='', a.event_analysis like concat('%', concat(:record.event_analysis, '%')))" +
            " if(:record.limit is not null and :record.page is not null, limit :record.limit offset (:record.page-1) * :record.limit)" +
            " order by created_time desc")
    Page<AuditRecordEntity> getListByCondition(@Param("record") AuditRecordBO recordBO);*/

}
