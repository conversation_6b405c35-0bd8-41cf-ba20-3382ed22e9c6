package org.thingsboard.server.dao.model.sql.smartService.system;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 紧急程度
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-09
 */
@TableName("tb_service_system_urgency")
@Data
public class SystemUrgency {
    
    @TableId
    private String id;

    private String name;

    private String color;

    private Date createTime;

    private Date updateTime;

    private String isDel;

    private String tenantId;

}
