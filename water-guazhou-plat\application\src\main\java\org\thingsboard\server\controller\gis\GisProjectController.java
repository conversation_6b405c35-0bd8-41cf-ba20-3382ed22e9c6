package org.thingsboard.server.controller.gis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisProjectService;
import org.thingsboard.server.dao.model.request.GisProjectListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisProject;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * GIS项目
 */
@RestController
@RequestMapping("api/gis/project")
public class GisProjectController extends BaseController {

    @Autowired
    private GisProjectService gisProjectService;

    @PostMapping("save")
    public IstarResponse save(@RequestBody GisProject entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        gisProjectService.save(entity);

        return IstarResponse.ok();
    }

    @GetMapping("list")
    public IstarResponse findList(GisProjectListRequest request) throws ThingsboardException {

        return IstarResponse.ok(gisProjectService.findList(request, getTenantId()));
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        gisProjectService.remove(ids);
        return IstarResponse.ok();
    }

}
