import{d as X,r as F,c,u as Z,o as ee,g as C,n as h,q as t,F as o,p as y,G as d,bo as te,h as ae,bh as le,i as oe,an as re,y as L,x as u,J as ne,H as se,I as ie,c2 as de,K as ue,N as pe,O as me,P as ge,bz as ce,bK as fe,L as ve,br as ye,C as be}from"./index-r0dFAfgr.js";import{f as _e}from"./DateFormatter-Bm9a68Ax.js";import{g as Ve,d as E,u as De,s as Ue}from"./assay-DCQODP3D.js";import{u as Ce}from"./fileUpload-CoTDqaci.js";const we={class:"water-quality-test"},ke={class:"card-header"},Ne={class:"header-buttons"},xe={class:"pagination"},Fe={class:"el-upload__tip"},he={key:0,style:{color:"#67C23A"}},Le={class:"dialog-footer"},Ee=X({__name:"index",setup(Se){const n=F({pageNum:1,pageSize:10,samplingLocation:"",reportName:"",testingUnit:"",testDate:void 0,type:0}),b=c([]),V=c(0),D=c(!1),p=c([]),S=a=>{p.value=a.map(e=>e.id)},r=F({id:"",samplingLocation:"",reportName:"",testingUnit:"",testResults:"",testDate:void 0,reportFile:"",remark:"",type:0}),z={samplingLocation:[{required:!0,message:"请输入采样地点",trigger:"blur"}],reportName:[{required:!0,message:"请输入报告名称",trigger:"blur"}],testingUnit:[{required:!0,message:"请输入检测单位",trigger:"blur"}],testResults:[{required:!0,message:"请输入检测结果",trigger:"blur"}],testDate:[{required:!0,message:"请选择检测月份",trigger:"change"}]},v=c(!1),U=c(""),_=c();c({"X-Authorization":"Bearer "+Z().token});const f=async()=>{D.value=!0;try{const a=await Ve(n);console.log("API返回数据:",a),a.data&&a.data.data&&a.data.data.data?(b.value=Array.isArray(a.data.data.data)?a.data.data.data:[],V.value=a.data.data.total||0,console.log("表格数据:",b.value)):(console.warn("返回数据格式不正确:",a),b.value=[],V.value=0)}catch(a){console.error("获取数据失败:",a),b.value=[],V.value=0}finally{D.value=!1}},w=()=>{n.pageNum=1,p.value=[],f()},q=()=>{n.samplingLocation="",n.reportName="",n.testingUnit="",n.testDate=void 0,p.value=[],w()},R=()=>{U.value="新增化验记录",v.value=!0},B=a=>{U.value="编辑化验记录",Object.assign(r,{...a,testDate:a.testDate?Number(a.testDate):void 0}),v.value=!0},T=a=>{L.confirm("确认删除该记录吗？","提示",{type:"warning"}).then(async()=>{try{await E([a.id]),u.success("删除成功"),f()}catch(e){console.error(e),u.error("删除失败")}})},A=()=>{if(p.value.length===0){u.warning("请选择要删除的记录");return}L.confirm(`确认删除选中的 ${p.value.length} 条记录吗？`,"提示",{type:"warning"}).then(async()=>{try{await E(p.value),u.success("批量删除成功"),p.value=[],f()}catch(a){console.error(a),u.error("批量删除失败")}})},I=a=>{window.open(a.reportFile)},P=a=>!0,Q=async a=>{const{file:e}=a;try{const s=await Ce(e,"file");r.reportFile=s,u.success("文件上传成功")}catch(s){console.error("文件上传失败:",s),u.error("文件上传失败")}},M=async()=>{_.value&&await _.value.validate(async a=>{if(a)try{const e={...r,testDate:r.testDate?String(r.testDate):String(Date.now()),reportFile:typeof r.reportFile=="string"?r.reportFile:"",type:0};if(!e.reportFile&&!r.id){u.warning("请先上传化验报告文件");return}r.id?(await De(e),u.success("修改成功")):(await Ue(e),u.success("新增成功")),v.value=!1,f()}catch(e){console.error("保存失败:",e),u.error("保存失败")}})},$=()=>{_.value&&_.value.resetFields(),Object.assign(r,{id:"",samplingLocation:"",reportName:"",testingUnit:"",testResults:"",testDate:void 0,reportFile:"",remark:"",type:0});const a=document.querySelector(".upload-demo .el-upload__input");a&&(a.value="")},W=a=>{n.pageSize=a,p.value=[],f()},Y=a=>{n.pageNum=a,p.value=[],f()};return ee(()=>{f()}),(a,e)=>{const s=ne,m=se,i=ie,k=de,N=ue,g=pe,j=me,O=ge,K=ce,G=fe,H=ve,J=ye;return C(),h("div",we,[t(K,{class:"box-card"},{header:o(()=>[y("div",ke,[e[16]||(e[16]=y("span",null,"化验记录",-1)),y("div",Ne,[t(s,{type:"primary",onClick:R},{default:o(()=>e[14]||(e[14]=[d("新增记录")])),_:1}),t(s,{type:"danger",disabled:p.value.length===0,onClick:A},{default:o(()=>e[15]||(e[15]=[d("批量删除")])),_:1},8,["disabled"])])])]),default:o(()=>[t(N,{model:n,ref:"queryForm",inline:!0,class:"search-form"},{default:o(()=>[t(i,{label:"采样地点",prop:"samplingLocation"},{default:o(()=>[t(m,{modelValue:n.samplingLocation,"onUpdate:modelValue":e[0]||(e[0]=l=>n.samplingLocation=l),placeholder:"请输入采样地点",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"报告名称",prop:"reportName"},{default:o(()=>[t(m,{modelValue:n.reportName,"onUpdate:modelValue":e[1]||(e[1]=l=>n.reportName=l),placeholder:"请输入报告名称",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"检测单位",prop:"testingUnit"},{default:o(()=>[t(m,{modelValue:n.testingUnit,"onUpdate:modelValue":e[2]||(e[2]=l=>n.testingUnit=l),placeholder:"请输入检测单位",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"检测月份",prop:"testDate"},{default:o(()=>[t(k,{modelValue:n.testDate,"onUpdate:modelValue":e[3]||(e[3]=l=>n.testDate=l),type:"month",placeholder:"选择月份","value-format":"x"},null,8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(s,{type:"primary",onClick:w},{default:o(()=>e[17]||(e[17]=[d("搜索")])),_:1}),t(s,{onClick:q},{default:o(()=>e[18]||(e[18]=[d("重置")])),_:1})]),_:1})]),_:1},8,["model"]),te((C(),ae(j,{data:b.value,style:{width:"100%"},onSelectionChange:S},{default:o(()=>[t(g,{type:"selection",width:"55"}),t(g,{prop:"samplingLocation",label:"采样地点","min-width":"120"}),t(g,{prop:"reportName",label:"报告名称","min-width":"120"}),t(g,{prop:"testingUnit",label:"检测单位","min-width":"120"}),t(g,{prop:"testResults",label:"检测结果","min-width":"120"}),t(g,{prop:"testDate",label:"检测月份","min-width":"120"},{default:o(({row:l})=>[d(le(l.testDate?oe(_e)(Number(l.testDate),"YYYY-MM"):""),1)]),_:1}),t(g,{prop:"reportFile",label:"报告附件","min-width":"120"},{default:o(({row:l})=>[t(s,{type:"text",onClick:x=>I(l)},{default:o(()=>e[19]||(e[19]=[d("下载")])),_:2},1032,["onClick"])]),_:1}),t(g,{label:"操作",width:"150"},{default:o(({row:l})=>[t(s,{type:"text",onClick:x=>B(l)},{default:o(()=>e[20]||(e[20]=[d("编辑")])),_:2},1032,["onClick"]),t(s,{type:"text",onClick:x=>T(l)},{default:o(()=>e[21]||(e[21]=[d("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,D.value]]),y("div",xe,[t(O,{"current-page":n.pageNum,"onUpdate:currentPage":e[4]||(e[4]=l=>n.pageNum=l),"page-size":n.pageSize,"onUpdate:pageSize":e[5]||(e[5]=l=>n.pageSize=l),total:V.value,onSizeChange:W,onCurrentChange:Y,layout:"total, sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])]),_:1}),t(H,{title:U.value,modelValue:v.value,"onUpdate:modelValue":e[13]||(e[13]=l=>v.value=l),width:"500px",onClose:$},{footer:o(()=>[y("span",Le,[t(s,{onClick:e[12]||(e[12]=l=>v.value=!1)},{default:o(()=>e[24]||(e[24]=[d("取消")])),_:1}),t(s,{type:"primary",onClick:M},{default:o(()=>e[25]||(e[25]=[d("确定")])),_:1})])]),default:o(()=>[t(N,{ref_key:"formRef",ref:_,model:r,rules:z,"label-width":"100px"},{default:o(()=>[t(i,{label:"采样地点",prop:"samplingLocation"},{default:o(()=>[t(m,{modelValue:r.samplingLocation,"onUpdate:modelValue":e[6]||(e[6]=l=>r.samplingLocation=l),placeholder:"请输入采样地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"报告名称",prop:"reportName"},{default:o(()=>[t(m,{modelValue:r.reportName,"onUpdate:modelValue":e[7]||(e[7]=l=>r.reportName=l),placeholder:"请输入报告名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测单位",prop:"testingUnit"},{default:o(()=>[t(m,{modelValue:r.testingUnit,"onUpdate:modelValue":e[8]||(e[8]=l=>r.testingUnit=l),placeholder:"请输入检测单位"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测结果",prop:"testResults"},{default:o(()=>[t(m,{modelValue:r.testResults,"onUpdate:modelValue":e[9]||(e[9]=l=>r.testResults=l),placeholder:"请输入检测结果"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测月份",prop:"testDate"},{default:o(()=>[t(k,{modelValue:r.testDate,"onUpdate:modelValue":e[10]||(e[10]=l=>r.testDate=l),type:"month",placeholder:"选择月份","value-format":"x"},null,8,["modelValue"])]),_:1}),t(i,{label:"化验报告",prop:"reportFile"},{default:o(()=>[t(G,{class:"upload-demo","http-request":Q,"before-upload":P,limit:1},{tip:o(()=>[y("div",Fe,[e[23]||(e[23]=d(" 请上传化验报告文件 ")),r.reportFile?(C(),h("span",he," (已上传) ")):re("",!0)])]),default:o(()=>[t(s,{type:"primary"},{default:o(()=>e[22]||(e[22]=[d("点击上传")])),_:1})]),_:1})]),_:1}),t(i,{label:"备注",prop:"remark"},{default:o(()=>[t(m,{modelValue:r.remark,"onUpdate:modelValue":e[11]||(e[11]=l=>r.remark=l),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}}),Te=be(Ee,[["__scopeId","data-v-99e3e365"]]);export{Te as default};
