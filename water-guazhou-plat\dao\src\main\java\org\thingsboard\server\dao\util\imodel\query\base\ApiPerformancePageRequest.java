package org.thingsboard.server.dao.util.imodel.query.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.ApiPerformance;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

import java.util.Date;

/**
 * 公共平台-服务监控对象 api_performance
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@ApiModel(value = "服务监控", description = "平台管理-服务监控实体类")
@Data
public class ApiPerformancePageRequest extends PageableQueryEntity<ApiPerformance> {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 方法名称
     */
    @ApiModelProperty(value = "方法名称")
    private String methodName;

    /**
     * 类名
     */
    @ApiModelProperty(value = "类名")
    private String className;

    /**
     * 执行时长
     */
    @ApiModelProperty(value = "执行时长")
    private String executionTime;

    /**
     * 调用时间
     */
    @ApiModelProperty(value = "调用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;

    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private String success;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    private String description;
}
