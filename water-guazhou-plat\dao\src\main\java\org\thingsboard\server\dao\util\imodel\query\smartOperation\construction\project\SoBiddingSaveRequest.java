package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoBiddingSaveRequest extends ComplexSaveRequest<SoBidding, SoBiddingCompanySaveRequest> {
    // 所属项目编号
    @NotNullOrEmpty
    private String projectCode;

    // 代理招标公司
    private String proxyBiddingCompany;

    // 中标公司id
    private String preferCompanyId;

    // 附件
    private String attachments;

    @Override
    protected SoBidding build() {
        SoBidding entity = new SoBidding();
        entity.setProjectCode(projectCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoBidding update(String id) {
        SoBidding entity = new SoBidding();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoBidding entity) {
        entity.setProxyBiddingCompany(proxyBiddingCompany);
        entity.setPreferCompanyId(preferCompanyId);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }

    @Override
    protected StringSetter<SoBiddingCompanySaveRequest> parentSetter() {
        return SoBiddingCompanySaveRequest::setBiddingId;
    }
}