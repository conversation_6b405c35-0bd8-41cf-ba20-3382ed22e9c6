package org.thingsboard.server.dao.util.imodel.query.workOrder;

import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;

import java.util.Set;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage.*;

public interface WorkOrderStagibleRequest {

    default Set<WorkOrderStatus> getOnProcessStage() {
        return PROCESSED.getStatusBetween();
    }

    default Set<WorkOrderStatus> getPendingStage() {
        return WAITED.getStatusBetween();
    }

    default Set<WorkOrderStatus> getReceivedStage() {
        return RECEIVED.getStatusBetween();
    }

    default Set<WorkOrderStatus> getCompleteStage() {
        return COMPLETED.getStatusBetween();
    }
}
