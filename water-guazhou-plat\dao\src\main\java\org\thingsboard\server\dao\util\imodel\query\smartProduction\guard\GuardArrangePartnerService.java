package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner;

import java.util.List;

public interface GuardArrangePartnerService {
    IPage<GuardArrangePartner> findAllConditional(GuardArrangePartnerPageRequest request);

    GuardArrangePartner save(GuardArrangePartnerSaveRequest entity);

    List<GuardArrangePartner> replaceAll(String arrangeId, List<GuardArrangePartnerSaveRequest> partners);

    /**
     * 调班
     *
     * @param req 调班请求
     * @return 是否成功
     */
    boolean switchArrangement(GuardArrangeSwitchRequest req);


    /**
     * 是否成功
     *
     * @return 是否成功
     */
    List<GuardArrangePartner> autoCompletePendingArrange();

    /**
     * 移除指定排班下的成员
     * @param removedArrangeInfoIdList 排班id
     * @return 是否成功
     */
    boolean removeOnArrangeIdIn(List<String> removedArrangeInfoIdList);

}
