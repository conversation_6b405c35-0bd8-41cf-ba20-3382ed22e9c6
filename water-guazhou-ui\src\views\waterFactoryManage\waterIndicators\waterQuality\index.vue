<!-- 出水水质 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <div class="main">
      <div class="left">
        <SLCard class="left-card" title=" ">
          <!-- <template #title>
            <div class="card-title">
              <span class="title"> 新一水厂数据详情</span>
            </div>
          </template> -->
          <template #right>
            <InlineForm
              ref="refForm"
              class="left"
              :config="formConfig"
            ></InlineForm>
          </template>
          <div class="table1">
            <CardTable
              ref="refCardTable"
              class="card-table"
              :config="cardTableConfig1"
            />
          </div>
          <div class="table2">
            <CardTable
              ref="refCardTable2"
              class="card-table"
              :config="cardTableConfig2"
            />
          </div>
        </SLCard>
      </div>
      <div class="right">
        <!-- 详情列表 -->
        <SLCard
          :title="
            state.compareType === 1
              ? ' 同比'
              : state.compareType === 2
                ? ' 环比'
                : ' 定基比' + '分析'
          "
          class="card-chart"
        >
          <div ref="zxDiv" v-loading="loading" class="chart-box">
            <!-- 折线图 -->
            <VChart
              ref="refChart1"
              :theme="
                useAppStore().isDark ? 'blackBackground' : 'whiteBackground'
              "
              :option="state.chartOption1"
            ></VChart>
          </div>
        </SLCard>
        <!-- 详情折线图 -->
        <SLCard title="阈值分析" class="card-chart">
          <div ref="zxDiv" v-loading="loading" class="chart-box">
            <!-- 折线图 -->
            <VChart
              ref="refChart2"
              :theme="
                useAppStore().isDark ? 'blackBackground' : 'whiteBackground'
              "
              :option="state.chartOption2"
            ></VChart>
          </div>
        </SLCard>
        <!-- 详情折线图 -->
        <SLCard title="系数分析" class="card-chart">
          <div ref="zxDiv" v-loading="loading" class="chart-box">
            <!-- 折线图 -->
            <VChart
              ref="refChart3"
              :theme="
                useAppStore().isDark ? 'blackBackground' : 'whiteBackground'
              "
              :option="state.chartOption3"
            ></VChart>
          </div>
        </SLCard>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector';
import { Refresh } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { lineOption } from '../echartsData/echart';
import { IECharts } from '@/plugins/echart';
import useStation from '@/hooks/station/useStation';
import {
  getWaterSupplyQualityReport,
  exportWaterSupplyQualityReport
} from '@/api/waterFactoryManage/waterIndicators';
import { useAppStore } from '@/store';

const { getAllStationOption } = useStation();

const erd = elementResizeDetectorMaker();
const state = reactive<{
  compareType: number;
  chartOption1: any;
  chartOption2: any;
  chartOption3: any;
  storedStationName: string;
  storedStationList: any[];
  attrTypeList: NormalOption[];
}>({
  compareType: 2,
  chartOption1: null,
  chartOption2: null,
  chartOption3: null,
  storedStationName: '',
  storedStationList: [],
  attrTypeList: [
    { label: '浊度', value: 'turbidity', unit: '(NTU)' },
    { label: '余氯', value: 'remainder', unit: '(mg/L)' },
    { label: 'PH', value: 'ph', unit: '(pH)' },
    { label: 'BOD5', value: 'BOD5', unit: '(mg/L)' },
    { label: 'COD', value: 'COD', unit: '(mg/L)' },
    { label: '总磷', value: 'CSK_TP', unit: '(mg/L)' },
    { label: '总氮', value: 'CSK_TN', unit: '(mg/L)' },
    { label: '溶解氧', value: 'DO', unit: '(mg/L)' },
    { label: '氨氮', value: 'CSK_NH3_N', unit: '(mg/L)' }
  ]
});
watch(
  () => useAppStore().isDark,
  () => {
    state.chartOption1.backgroundColor = useAppStore().isDark
      ? '#131624'
      : '#F4F7FA';
    state.chartOption2.backgroundColor = useAppStore().isDark
      ? '#131624'
      : '#F4F7FA';
    state.chartOption3.backgroundColor = useAppStore().isDark
      ? '#131624'
      : '#F4F7FA';
  }
);
const loading = ref<boolean>(false);
const refSearch = ref<ICardSearchIns>();
const refCardTable = ref<ICardTableIns>();
const refCardTable2 = ref<ICardTableIns>();
const refChart1 = ref<IECharts>();
const refChart2 = ref<IECharts>();
const refChart3 = ref<IECharts>();
const zxDiv = ref<any>();
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    attrType: 'turbidity',
    time: dayjs().format('YYYY-MM-DD'),
    queryType: '15m'
  },
  filters: [
    {
      type: 'select',
      label: '水厂',
      field: 'stationId',
      options: [],
      clearable: false,
      onChange: (val: any) => {
        state.storedStationName = state.storedStationList.find(
          (item) => item?.id === val
        ).label;
      }
    },
    {
      type: 'select',
      field: 'attrType',
      options: state.attrTypeList,
      clearable: false,
      label: '水质项'
    },
    { type: 'date', label: '日期', field: 'time', clearable: false },
    {
      type: 'select',
      width: '100px',
      field: 'queryType',
      clearable: false,
      options: [
        { label: '1 m', value: '1m' },
        { label: '5 m', value: '5m' },
        { label: '10 m', value: '10m' },
        { label: '15 m', value: '15m' },
        { label: '小时', value: '60m' }
      ],
      label: '时间间隔'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
          }
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          icon: 'iconfont icon-xiazai',
          click: () => _exportWaterQuality()
        }
      ]
    }
  ]
});

// 列表筛选按钮
const formConfig = ref<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'compareType',
          options: [
            { label: '环比', value: 2 },
            { label: '同比', value: 1 },
            { label: '定基比', value: 3 }
          ],
          label: '',
          clearable: false,
          onChange: (val: any) => {
            state.compareType = val;
            refreshData();
          }
        }
      ]
    }
  ],
  defaultValue: {
    compareType: 2
  }
});

// 初始化水厂列表配置数据
const cardTableConfig1 = reactive<ITable>({
  loading: true,
  dataList: [],
  highlightCurrentRow: true,
  columns: [
    { prop: 'name1', label: '时间' },
    { prop: 'name2', label: '差值率', unit: '(%)' },
    { prop: 'name3', label: '变化系数' }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
});

// 底部列表配置
const cardTableConfig2 = reactive<ITable>({
  loading: true,
  dataList: [],
  highlightCurrentRow: true,
  columns: [
    { prop: 'ts', label: '时间' },
    { prop: 'max', label: '最大值' },
    { prop: 'maxTs', label: '最大值发生时间' },
    { prop: 'min', label: '最小值' },
    { prop: 'minTs', label: '最小值发生时间' },
    { prop: 'avg', label: '平均值' }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
});

// 刷新列表 模拟数据
const refreshData = async () => {
  loading.value = true;
  cardTableConfig1.loading = true;
  cardTableConfig2.loading = true;
  const queryParams = (refSearch.value?.queryParams as any) || {};
  const params: any = {
    stationId: queryParams.stationId,
    attrType: queryParams.attrType,
    queryType: queryParams.queryType,
    time: dayjs(queryParams.time).format('YYYY-MM-DD'),
    compareType: state.compareType
  };

  const supplyQualityReport = await getWaterSupplyQualityReport(params);
  const data = supplyQualityReport.data?.data;

  cardTableConfig1.columns = data?.baseTable.tableInfo.map((item) => {
    return {
      prop: item.columnValue,
      label: item.columnName,
      unit: item.unit ? '(' + item.unit + ')' : ''
    };
  });
  // 顶部列表数据
  cardTableConfig1.dataList = data?.baseTable.tableDataList;
  cardTableConfig1.loading = false;
  // 总结列表数据
  cardTableConfig2.dataList = data?.countTable;
  cardTableConfig2.loading = false;
  // 加载图表
  refuseChart(data?.baseTable, params);
};

// 加载图表
const refuseChart = (data, queryParams) => {
  const attrType: any = state.attrTypeList.find(
    (item) => queryParams?.attrType === item.value
  );
  clearEcharts();
  const serie = {
    name: '',
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: useAppStore().isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: useAppStore().isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    }
  };
  const dataX = data?.tableDataList.map((table) => table.ts);

  // 同比、环比、顶级比图表
  const echartOption = lineOption();
  echartOption.series = [];
  echartOption.yAxis[0].name = attrType?.label.concat(attrType?.unit);
  echartOption.xAxis.data = dataX;
  data?.tableInfo.filter((table) => {
    if (!['differenceRate', 'ts', 'changeRate'].includes(table.columnValue)) {
      const newSerie = JSON.parse(JSON.stringify(serie));
      newSerie.name = table.columnName;
      newSerie.data = data?.tableDataList.map(
        (data) => data[table.columnValue]
      );
      echartOption.series.push(newSerie);
    }
  });
  // 阈值分析图表
  const yuOption = lineOption();
  yuOption.series = [];
  yuOption.yAxis[0].name = attrType?.label.concat(attrType?.unit);
  yuOption.xAxis.data = dataX;
  const yuSerie = JSON.parse(JSON.stringify(serie));
  yuSerie.data = data?.tableDataList.map((data) => data[queryParams.time]);
  yuOption.series.push(yuSerie);
  // 系数分析图表
  const xiOption = lineOption();
  xiOption.series = [];
  xiOption.yAxis[0].name = '系数(%)';
  xiOption.xAxis.data = dataX;
  const xiSerie = JSON.parse(JSON.stringify(serie));
  xiSerie.data = data?.tableDataList.map((data) => data.changeRate);
  xiOption.series.push(xiSerie);
  nextTick(() => {
    if (zxDiv.value) {
      erd.listenTo(zxDiv.value, () => {
        state.chartOption1 = echartOption;
        state.chartOption2 = yuOption;
        state.chartOption3 = xiOption;
        resizeChart();
      });
    }
  });
};

const clearEcharts = () => {
  refChart1.value?.clear();
  refChart2.value?.clear();
  refChart3.value?.clear();
};
const resizeChart = () => {
  refChart1.value?.resize();
  refChart2.value?.resize();
  refChart3.value?.resize();
  loading.value = false;
};
// 导出水量报告
const _exportWaterQuality = () => {
  const queryParams = (refSearch.value?.queryParams as any) || {};
  const params: any = {
    stationId: queryParams.stationId,
    attrType: queryParams.attrType,
    queryType: queryParams.queryType,
    time: dayjs(queryParams.time).format('YYYY-MM-DD'),
    compareType: state.compareType
  };

  exportWaterSupplyQualityReport(params).then((res) => {
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `出水水质报表.xlsx`);
    document.body.appendChild(link);
    link.click();
  });
};

onMounted(async () => {
  const storedStations: any = await getAllStationOption('水厂');
  state.storedStationList = storedStations;
  console.log('state.storedStations', storedStations);
  // storedStations = storedStations.map((item: any) => {
  //   return { label: item.name, value: item.id }
  // })
  const filter = cardSearchConfig.filters?.find(
    (item) => item?.field === 'stationId'
  ) as IFormSelect;
  filter.options = storedStations;
  cardSearchConfig.defaultParams = {
    ...cardSearchConfig.defaultParams,
    stationId: storedStations[0]?.id
  };
  state.storedStationName = storedStations[0]?.label;
  refSearch.value?.resetForm();
  await refreshData();
});
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  justify-content: space-between;
  height: 100%;

  .left {
    width: 55%;
    height: 100%;
    min-width: 46%;

    .left-card {
      width: 99%;
      height: calc(100% - 100px);
      padding: 50px 0 8px 0;

      .table1 {
        height: calc(70% - 15px);
        margin-bottom: 15px;
      }

      .table2 {
        height: 30%;
      }

      .card-table {
        height: 100%;
      }
    }

    :deep(.sl-card-title) {
      justify-content: space-between;
      padding-top: 8px;
    }
  }

  .right {
    height: calc(100% - 100px);
    width: calc(54% - 15px);
  }
}

.card-title {
  font-size: 16px;
}

.card-chart {
  width: 100%;
  height: calc(33.33% - 15px);
  margin-bottom: 15px;

  &:last-child {
    height: 33.33%;
    margin-bottom: 0;
  }
}

.chart-box {
  width: 100%;
  height: 100%;
}
</style>
