package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwglMEntity;

import java.util.List;

public interface SwXjrwglMRepository extends JpaRepository<SwXjrwglMEntity, String> {

    @Query("SELECT DISTINCT m FROM SwXjrwglMEntity m, SwXjrwglCEntity c " +
            "WHERE m.id = c.mainId AND m.tenantId = ?3 AND m.content LIKE %?1% AND m.status IN ?2 AND m.isDel = '0' AND c.deviceId LIKE %?4%")
    Page<SwXjrwglMEntity> findList(String content, List<String> statusList, String tenantId, String deviceId, Pageable pageable);

    @Query("SELECT m FROM SwXjrwglMEntity m " +
            "WHERE m.isDel = ?1 AND m.status = '1'")
    List<SwXjrwglMEntity> findAllByIsDel(String isDel);
}
