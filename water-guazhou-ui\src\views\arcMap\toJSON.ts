export const toJson = (mapView: __esri.MapView, basemapType) => {
  const result: any = {
    mapOptions: {
      extent: mapView.extent.toJSON(),
      spatialReference: mapView.spatialReference.toJSON(),
      scale: mapView.scale
    },
    operationalLayers: [],
    exportOptions: {
      // "outputSize": [
      //     800,
      //     1100
      // ],
      // "dpi": 96
    },
    layoutOptions: {
      titleText: 'My Print',
      authorText: 'Sam',
      copyrightText: 'My Company',

      customTextElements: [],
      //     {
      //         company: "1212"
      //     },
      //      {
      //         leftbottom:"000-000"
      //     }
      // ],
      scaleBarOptions: {
        metricUnit: 'esriMeters ',
        metricLabel: '米',
        nonMetricUnit: 'esriMiles',
        nonMetricLabel: 'mi'
      }
      // "legendOptions": {
      //     "operationalLayers": [
      //         {
      //             "id": "layer0",
      //             "subLayerIds": [
      //                 0
      //             ]
      //         },
      //         {
      //             "id": "layer1",
      //             "subLayerIds": [
      //                 0
      //             ]
      //         },
      //         {
      //             "id": "layer3",
      //             "subLayerIds": [
      //                 0,
      //                 15,
      //                 14,
      //                 13,
      //                 12,
      //                 11,
      //                 10,
      //                 9,
      //                 8,
      //                 7,
      //                 6,
      //                 5,
      //                 4,
      //                 3,
      //                 2,
      //                 1
      //             ]
      //         }
      //     ]
      // }
    }
  }

  // var layers = mapView.getLayersVisibleAtScale(mapView.getScale());
  // layers.forEach(function (layer) {
  //     console.log(layer.id);

  //     var layerInfo = {
  //         "id": layer.id,
  //         "title": layer.name,
  //         "opacity": layer.opacity,
  //         "minScale": layer.minScale,
  //         "maxScale": layer.maxScale,
  //         "url": layer.url,
  //         "token": layer.token,

  //         "layers": []

  //     }
  //     //.subLayerIds
  //     if (layer.layerInfos && layer.layerInfos[0]) {
  //         var parentLayerId = -1;
  //         layerInfo.layers = [];

  //         layer.layerInfos.forEach(function (sublayer, i) {
  //             var currentSublayer = {
  //                 "id": sublayer.id,
  //                 "minScale": sublayer.minScale,
  //                 "maxScale": sublayer.maxScale,
  //                 "parentLayerId": parentLayerId,
  //                 "layerDefinition": {
  //                     "source": {
  //                         "mapLayerId": sublayer.id,
  //                         "type": "mapLayer"
  //                     }
  //                 },
  //                 "subLayerIds": null,
  //                 "name": sublayer.name,
  //                 "defaultVisibility": sublayer.visible
  //             }
  //             if (sublayer.subLayerIds) {
  //                 currentSublayer.subLayerIds = [];
  //                 sublayer.subLayerIds.forEach(function (sub) {

  //                     currentSublayer.subLayerIds.push(sub);
  //                 });

  //             }
  //             parentLayerId = 0;
  //             layerInfo.layers.push(currentSublayer);

  //         });
  //     }

  //     result.operationalLayers.push(layerInfo);

  // });

  mapView.map.layers.forEach((layer: any) => {
    let isBeijing = false
    if (layer.url && /BEIJING/.test(layer.url)) {
      isBeijing = true
    }
    if (basemapType === '0') {
      const layerInfo: any = {
        id: layer.id,
        title: layer.name,
        opacity: layer.opacity,
        minScale: layer.minScale,
        maxScale: layer.maxScale,
        url: layer.url,
        token: layer.token,

        layers: []
      }
      // .subLayerIds
      if (layer.layerInfos && layer.layerInfos[0]) {
        let parentLayerId = -1
        layerInfo.layers = []

        // if (layer.visible) {
        layer.layerInfos.forEach(sublayer => {
          const currentSublayer: any = {
            id: sublayer.id,
            minScale: sublayer.minScale,
            maxScale: sublayer.maxScale,
            parentLayerId,
            layerDefinition: {
              source: {
                mapLayerId: sublayer.id,
                type: 'mapLayer'
              }
            },
            subLayerIds: null,
            name: sublayer.name,
            defaultVisibility: sublayer.visible
          }
          if (sublayer.subLayerIds) {
            currentSublayer.subLayerIds = []
            sublayer.subLayerIds.forEach(sub => {
              currentSublayer.subLayerIds.push(sub)
            })
          }
          parentLayerId = 0
          layerInfo.layers.push(currentSublayer)
        })
        // }
      }
      result.operationalLayers.push(layerInfo)
    } else if (layer.visible && !isBeijing) {
      const layerInfo: any = {
        id: layer.id,
        title: layer.name,
        opacity: layer.opacity,
        minScale: layer.minScale,
        maxScale: layer.maxScale,
        url: layer.url,
        token: layer.token,

        layers: []
      }
      // .subLayerIds
      if (layer.layerInfos && layer.layerInfos[0]) {
        let parentLayerId = -1
        layerInfo.layers = []

        // if (layer.visible) {
        layer.layerInfos.forEach(sublayer => {
          const currentSublayer: any = {
            id: sublayer.id,
            minScale: sublayer.minScale,
            maxScale: sublayer.maxScale,
            parentLayerId,
            layerDefinition: {
              source: {
                mapLayerId: sublayer.id,
                type: 'mapLayer'
              }
            },
            subLayerIds: null,
            name: sublayer.name,
            defaultVisibility: sublayer.visible
          }
          if (sublayer.subLayerIds) {
            currentSublayer.subLayerIds = []
            sublayer.subLayerIds.forEach(sub => {
              currentSublayer.subLayerIds.push(sub)
            })
          }
          parentLayerId = 0
          layerInfo.layers.push(currentSublayer)
        })
        // }
      }
      result.operationalLayers.push(layerInfo)
    }
  })

  // mapView.allLayerViews.forEach(function (items) {
  //     var layer = items.layer;

  //     var layerInfo = {
  //         "id": layer.id,
  //         "title": layer.title,
  //         "opacity": layer.opacity,
  //         "minScale": layer.minScale,
  //         "maxScale": layer.maxScale,
  //         "url": layer.url,
  //         "token": layer.token,

  //         "layers": []

  //     }

  //     if (layer.allSublayers) {
  //         var parentLayerId = -1;
  //         layerInfo.layers = [];
  //         layer.allSublayers.forEach(function (sublayer) {
  //             var currentSublayer = {
  //                 "id": sublayer.id,
  //                 "minScale": sublayer.minScale,
  //                 "maxScale": sublayer.maxScale,
  //                 "parentLayerId": parentLayerId,
  //                 "layerDefinition": {
  //                     "source": {
  //                         "mapLayerId": sublayer.id,
  //                         "type": "mapLayer"
  //                     }
  //                 },
  //                 "subLayerIds": null,
  //                 "name": sublayer.title,
  //                 "defaultVisibility": sublayer.visible
  //             }

  //             if (sublayer.sublayers) {
  //                 currentSublayer.subLayerIds = [];
  //                 sublayer.sublayers.forEach(function (sub) {
  //                     currentSublayer.subLayerIds.push(sub.id);
  //                 });
  //             }

  //             parentLayerId = 0;
  //             layerInfo.layers.push(currentSublayer);
  //         });
  //     }

  //     result.operationalLayers.push(layerInfo);

  // });

  //    console.log(JSON.stringify(result));
  return result
}
