import request from '@/plugins/axios';

/**
 * 分页查询水厂压力信息
 * @param params 查询参数
 */
export function getWaterPlantPressureList(params: any) {
  // 创建一个新的参数对象，只包含必要的字段
  const queryParams: any = {
    page: params.page || 1,
    size: params.size || 10
  };

  // 只添加非空的参数
  if (params.waterPlantName) queryParams.waterPlantName = params.waterPlantName;
  if (params.startTime) queryParams.startTime = params.startTime;
  if (params.endTime) queryParams.endTime = params.endTime;
  if (params.dataSource) queryParams.dataSource = params.dataSource;

  return request({
    url: '/api/waterPlant/pressure',
    method: 'get',
    params: queryParams
  });
}

/**
 * 新增或更新水厂压力信息
 * @param data 水厂压力信息
 */
export function saveWaterPlantPressure(data: any) {
  return request({
    url: '/api/waterPlant/pressure',
    method: 'post',
    data
  });
}

/**
 * 批量导入水厂压力信息
 * @param data 水厂压力信息列表
 */
export function importWaterPlantPressure(data: any) {
  return request({
    url: '/api/waterPlant/pressure/import',
    method: 'post',
    data
  });
}

/**
 * 导出水厂压力信息
 * @param params 查询参数
 */
export function exportWaterPlantPressure(params: any) {
  // 创建一个新的参数对象，只包含必要的字段
  const queryParams: any = {
    page: params.page || 1,
    size: params.size || -1 // 导出所有数据
  };

  // 只添加非空的参数
  if (params.waterPlantName) queryParams.waterPlantName = params.waterPlantName;
  if (params.startTime) queryParams.startTime = params.startTime;
  if (params.endTime) queryParams.endTime = params.endTime;
  if (params.dataSource) queryParams.dataSource = params.dataSource;

  return request({
    url: '/api/waterPlant/pressure/export',
    method: 'get',
    params: queryParams,
    responseType: 'blob'
  });
}

/**
 * 删除水厂压力信息
 * @param id 水厂压力信息ID
 */
export function deleteWaterPlantPressure(id: string) {
  return request({
    url: `/api/waterPlant/pressure/${id}`,
    method: 'delete'
  });
}

/**
 * 获取水厂列表
 * @param params 查询参数
 */
export function getWaterPlantList(params: any) {
  return request({
    url: '/api/station/list',
    method: 'get',
    params: {
      ...params,
      type: '水厂' // 指定站点类型为水厂
    }
  });
}
