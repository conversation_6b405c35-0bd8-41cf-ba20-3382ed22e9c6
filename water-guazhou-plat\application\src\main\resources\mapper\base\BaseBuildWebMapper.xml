<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseBuildWebMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseBuildWeb" id="BaseBuildWebResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="loginUrl"    column="login_url"    />
        <result property="loginTemplate"    column="login_template"    />
        <result property="style"    column="style"    />
        <result property="messageType"    column="message_type"    />
    </resultMap>

    <sql id="selectBaseBuildWebVo">
        select id, title, icon_url, login_url, login_template, style, message_type from base_build_web
    </sql>

    <select id="selectBaseBuildWebList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseBuildWeb" resultMap="BaseBuildWebResult">
        <include refid="selectBaseBuildWebVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="iconUrl != null  and iconUrl != ''"> and icon_url = #{iconUrl}</if>
            <if test="loginUrl != null  and loginUrl != ''"> and login_url = #{loginUrl}</if>
            <if test="loginTemplate != null  and loginTemplate != ''"> and login_template = #{loginTemplate}</if>
            <if test="style != null  and style != ''"> and style = #{style}</if>
            <if test="messageType != null  and messageType != ''"> and message_type = #{messageType}</if>
        </where>
    </select>
    
    <select id="selectBaseBuildWebById" parameterType="String" resultMap="BaseBuildWebResult">
        <include refid="selectBaseBuildWebVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseBuildWeb" parameterType="org.thingsboard.server.dao.model.sql.base.BaseBuildWeb">
        insert into base_build_web
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="loginUrl != null">login_url,</if>
            <if test="loginTemplate != null">login_template,</if>
            <if test="style != null">style,</if>
            <if test="messageType != null">message_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="loginUrl != null">#{loginUrl},</if>
            <if test="loginTemplate != null">#{loginTemplate},</if>
            <if test="style != null">#{style},</if>
            <if test="messageType != null">#{messageType},</if>
         </trim>
    </insert>

    <update id="updateBaseBuildWeb" parameterType="org.thingsboard.server.dao.model.sql.base.BaseBuildWeb">
        update base_build_web
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="loginUrl != null">login_url = #{loginUrl},</if>
            <if test="loginTemplate != null">login_template = #{loginTemplate},</if>
            <if test="style != null">style = #{style},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseBuildWebById" parameterType="String">
        delete from base_build_web where id = #{id}
    </delete>

    <delete id="deleteBaseBuildWebByIds" parameterType="String">
        delete from base_build_web where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>