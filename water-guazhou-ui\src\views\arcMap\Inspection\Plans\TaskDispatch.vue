<!-- 任务制定 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="任务制定"
    :detail-max-min="true"
    :hide-right-drawer="true"
    :hide-detail-close="true"
    @map-loaded="onMaploaded"
  >
    <template #detail-header>
      <span>任务制定</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <Search
          ref="refSearch"
          :config="SearchConfig"
          style="margin-bottom: 8px;"
        ></Search>
        <div class="detail-table">
          <FormTable :config="TableConfig"></FormTable>
        </div>
      </div>
    </template>
  </RightDrawerMap>
  <DialogForm
    ref="refDialogForm"
    :config="FormConfigAdd"
  ></DialogForm>
</template>
<script lang="ts" setup>
import {
  Delete,
  // Plus,
  Promotion,
  Refresh,
  Search as SearchIcon
} from '@element-plus/icons-vue'
import { queryLayerClassName } from '@/api/mapservice'
// import { IFormIns } from '@/components/type'
import { getSubLayerIds } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import {
  DeletePatrolPlans,
  GetPatrolPlanList,
  PlanPatrolTask
} from '@/api/patrol'
import { formatterDateTime } from '@/utils/GlobalHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref<ISearchIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refDialogForm = ref<IDialogFormIns>()
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerIds: number[]
  layerInfos: any[]
  planPeriod: NormalOption[]
}>({
  tabs: [],
  loading: false,
  layerIds: [],
  layerInfos: [],
  planPeriod: []
})
const SearchConfig = reactive<ISearch>({
  scrollBarGradientColor: '#fafafa',
  filters: [
    {
      type: 'datetimerange',
      field: 'date',
      label: '创建时间'
    },
    // {
    //   type: 'select',
    //   field: 'creator',
    //   label: '创建人'
    // },
    {
      type: 'input',
      field: 'keyword',
      clearable: false,
      label: '快速查找'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        // {
        //   perm: false,
        //   type: 'success',
        //   text: '新增',
        //   svgIcon: shallowRef(Plus),
        //   click: () => {}
        // },
        {
          perm: true,
          type: 'danger',
          text: '批量删除',
          disabled: (): boolean => !TableConfig.selectList?.length,
          svgIcon: shallowRef(Delete),
          click: () => handleDelete()
        }
      ]
    }
  ],
  defaultParams: {
    date: [
      moment().subtract(6, 'M').format(formatterDateTime),
      moment().format(formatterDateTime)
    ]
  }
})
const TableConfig = reactive<ITable>({
  dataList: [],
  handleSelectChange: (rows): any[] => (TableConfig.selectList = rows || []),
  columns: [
    {
      minWidth: 120,
      align: 'center',
      label: '是否常规计划',
      prop: 'isNormalPlan',
      tag: true,
      tagColor: (row): string => (row.isNormalPlan ? '#409eff' : '#0cbb4a'),
      formatter: (row): string => (row.isNormalPlan ? '常规' : '临时')
    },
    {
      minWidth: 120,
      align: 'center',
      label: '是否需要反馈',
      prop: 'isNeedFeedback',
      tag: true,
      tagColor: (row): string => (row.isNeedFeedback === false ? '#0cbb4a' : '#409eff'),
      formatter: (row): string => (row.isNeedFeedback === false ? '仅到位' : '需要')
    },
    { minWidth: 120, label: '计划名称', prop: 'name' },
    // { minWidth: 120, label: '计划类型', prop: 'key4' },
    { minWidth: 120, label: '区域/路线名称', prop: 'districtAreaName' },
    {
      minWidth: 120,
      label: '计划周期',
      prop: 'planCircleName'
    },
    { minWidth: 120, label: '巡检方式', prop: 'moveType' },
    { minWidth: 120, label: '创建人', prop: 'creatorName' },
    { minWidth: 120, label: '创建时间', prop: 'createTime' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  },
  operationWidth: 140,
  operations: [
    {
      perm: true,
      text: '任务制定',
      svgIcon: shallowRef(Promotion),
      click: row => {
        TableConfig.currentRow = row
        createTask()
      }
    },
    // {
    //   perm: true,
    //   type: 'success',
    //   text: '编辑',
    //   svgIcon: shallowRef(Edit),
    //   click: row => {
    //     TableConfig.currentRow = row
    //     handleEdit()
    //   }
    // },
    {
      perm: true,
      type: 'danger',
      text: '删除',
      svgIcon: shallowRef(Delete),
      click: row => handleDelete(row)
    }
  ]
})
const handleDelete = (row?: any) => {
  const ids = row
    ? [row.id]
    : TableConfig.selectList?.map(item => item.id) || []
  if (!ids.length) {
    SLMessage.warning('请先选择要删除的数据')
    return
  }
  SLConfirm('确定删除?', '提示信息')
    .then(() => {
      DeletePatrolPlans(ids)
        .then(res => {
          if (res.data.code === 200) {
            SLMessage.success(res.data.message)
            refreshData()
          } else {
            SLMessage.error(res.data.message)
          }
        })
        .catch(() => {
          SLMessage.error('系统错误')
        })
    })
    .catch(() => {
      //
    })
}
const createTask = async () => {
  const row = TableConfig.currentRow
  FormConfigAdd.defaultValue = {
    planName: row?.name,
    planId: row?.id
  }
  refDialogForm.value?.openDialog()
  await nextTick()
  refDialogForm.value?.resetForm()
}
const refreshData = () => {
  const { keyword, date } = refSearch.value?.queryParams || {}
  const [fromTime, toTime] = date || []
  GetPatrolPlanList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    keyword,
    fromTime,
    toTime
  }).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}
const FormConfigAdd = reactive<IDialogFormConfig>({
  title: '任务制定',
  dialogWidth: 500,
  labelPosition: 'right',
  group: [
    {
      fields: [
        { type: 'input', label: '计划名称', readonly: true, field: 'planName' },
        {
          type: 'input',
          label: '任务名称',
          field: 'name',
          rules: [{ required: true, message: '请输入任务名称' }]
        },
        {
          type: 'user-select',
          label: '接收人员',
          field: 'receiveUserId',
          rules: [{ required: true, message: '请选择接收人员' }]
        },
        {
          type: 'datetimerange',
          label: '起止时间',
          field: 'beginTime',
          rules: [{ required: true, message: '请选择起止时间' }]
        },
        {
          type: 'user-select',
          label: '共同完成人',
          multiple: true,
          field: 'collaborateUserId'
        },
        {
          type: 'input-number',
          label: '到位距离',
          append: '米',
          field: 'presentDistance'
        },
        {
          type: 'textarea',
          label: '任务描述',
          field: 'remark'
        }
      ]
    }
  ],
  submit: (params: any) => {
    const submitParams = {
      ...params,
      beginTime: params.beginTime && params.beginTime[0],
      endTime: params.beginTime && params.beginTime[1],
      planId: TableConfig.currentRow.id,
      collaborateUserId: params.collaborateUserId?.join(','),
      receiveUserId: params.receiveUserId?.join(',')
    }
    PlanPatrolTask(submitParams)
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refDialogForm.value?.closeDialog()
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch(() => {
        SLMessage.error('系统错误')
      })
  },
  defaultValue: {
    presentDistance: 0
  }
})
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}
const onMaploaded = async view => {
  staticState.view = view
  refMap.value?.toggleCustomDetail(true)
  refMap.value?.toggleCustomDetailMaxmin('max')
  await getLayerInfo()
}
onMounted(async () => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.detail-wrapper {
  height: 100%;
  .detail-table {
    height: calc(100% - 50px);
  }
}
</style>
