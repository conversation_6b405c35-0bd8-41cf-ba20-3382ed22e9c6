/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.obtain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.telemetryAttribute.RequestTs;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.utils.AttributeConstants;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import static org.thingsboard.server.service.rest.RestUtil.doResetPost;

@Service
@Slf4j
public class ObtainServiceImpl implements BaseObtainDataService {
    @Value("${tsdb.HITSDB_IP}")
    private String TSDB_IP;

    @Value("${tsdb.HITSDB_PORT}")
    private String TSDB_PORT;


    /**
     * 获取当前时间段内的第一个数据
     *
     * @param start
     * @param end
     * @param formula
     * @return
     */
    @Override
    public List<ResponseTs> getFirstDataFromOpenTSDB(long start, long end, String formula, TenantId
            tenantId, String timeLimit) throws ThingsboardException {
        ArrayList<HashMap<String, Object>> queries = new ArrayList<>();
        try {
            String[] array = formula.split("\\.");
            LinkedHashMap<String, Object> query = new LinkedHashMap<>();
            query.put(AttributeConstants.TSDB_AGGREGATOR, AttributeConstants.TSDB_AGGREGATOR_TYPE.none);
            query.put(AttributeConstants.TSDB_METRIC, UUIDConverter.fromTimeUUID(tenantId.getId()) + "." + array[0]);
            LinkedHashMap<String, String> tags = new LinkedHashMap<>();
            tags.put(AttributeConstants.TSDB_PROPERTY, array[1]);
            query.put("tags", tags);
            query.put(AttributeConstants.TSDB_DOWNSAMPLE, "1h-first");
            queries.add(query);
            if (queries.isEmpty()) {
                return null;
            }
            String putUrl = TSDB_IP + ":" + TSDB_PORT + AttributeConstants.TSDB_API_QUERY;
            return doResetPost(putUrl, new RequestTs(start, end, queries));
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DATA_FALIED + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


}
