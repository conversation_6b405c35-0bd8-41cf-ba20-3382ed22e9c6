<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.thingsboard</groupId>
    <artifactId>thingsboard</artifactId>
    <version>2.3.0</version>
    <packaging>pom</packaging>

    <name>Thingsboard</name>
    <url>https://thingsboard.io</url>
    <inceptionYear>2016</inceptionYear>

    <properties>
        <main.dir>${basedir}</main.dir>
        <spring-boot.version>1.4.3.RELEASE</spring-boot.version>
        <spring.version>4.3.4.RELEASE</spring.version>
        <spring-security.version>4.2.0.RELEASE</spring-security.version>
        <spring-data-redis.version>1.7.6.RELEASE</spring-data-redis.version>
        <jedis.version>2.9.0</jedis.version>
        <jjwt.version>0.7.0</jjwt.version>
        <json-path.version>2.2.0</json-path.version>
        <junit.version>4.12</junit.version>
        <slf4j.version>1.7.7</slf4j.version>
        <logback.version>1.2.3</logback.version>
        <mockito.version>1.9.5</mockito.version>
        <rat.version>0.10</rat.version>
        <cassandra.version>3.5.0</cassandra.version>
        <cassandra-unit.version>3.3.0.2</cassandra-unit.version>
        <takari-cpsuite.version>1.2.7</takari-cpsuite.version>
        <guava.version>21.0</guava.version>
        <caffeine.version>2.6.1</caffeine.version>
        <commons-lang3.version>3.4</commons-lang3.version>
        <commons-validator.version>1.5.0</commons-validator.version>
        <commons-io.version>2.5</commons-io.version>
        <commons-csv.version>1.4</commons-csv.version>
        <jackson.version>2.10.5.1</jackson.version>
        <jackson-core.version>2.10.5</jackson-core.version>
        <json-schema-validator.version>2.2.6</json-schema-validator.version>
        <scala.version>2.11</scala.version>
        <akka.version>2.4.2</akka.version>
        <californium.version>1.0.2</californium.version>
        <gson.version>2.6.2</gson.version>
        <velocity.version>1.7</velocity.version>
        <velocity-tools.version>2.0</velocity-tools.version>
        <mail.version>1.4.3</mail.version>
        <curator.version>4.0.1</curator.version>
        <protobuf.version>3.0.2</protobuf.version>
        <grpc.version>1.12.0</grpc.version>
        <lombok.version>1.18.10</lombok.version>
        <paho.client.version>1.1.0</paho.client.version>
        <netty.version>4.1.22.Final</netty.version>
        <os-maven-plugin.version>1.5.0</os-maven-plugin.version>
        <rabbitmq.version>4.8.0</rabbitmq.version>
        <surfire.version>2.19.1</surfire.version>
        <jar-plugin.version>3.0.2</jar-plugin.version>
        <springfox-swagger.version>2.6.1</springfox-swagger.version>
        <springfox-swagger-ui-rfc6570.version>1.0.0</springfox-swagger-ui-rfc6570.version>
        <bouncycastle.version>1.56</bouncycastle.version>
        <winsw.version>2.0.1</winsw.version>
        <hsqldb.version>2.4.0</hsqldb.version>
        <dbunit.version>2.5.3</dbunit.version>
        <spring-test-dbunit.version>1.2.1</spring-test-dbunit.version>
        <postgresql.driver.version>9.4.1211</postgresql.driver.version>
        <sonar.exclusions>org/thingsboard/server/gen/**/*,
            org/thingsboard/server/extensions/core/plugin/telemetry/gen/**/*
        </sonar.exclusions>
        <elasticsearch.version>5.0.2</elasticsearch.version>
        <delight-nashorn-sandbox.version>0.1.14</delight-nashorn-sandbox.version>
        <kafka.version>2.0.0</kafka.version>
        <bucket4j.version>4.1.1</bucket4j.version>
        <fst.version>2.57</fst.version>
        <yunpian.version>1.2.7</yunpian.version>
        <aviator.version>3.2.0</aviator.version>
        <druid.version>1.1.10</druid.version>
        <logger.version>0.0.6</logger.version>
        <quartz.version>2.3.2</quartz.version>
        <mybatis.plus.version>3.4.0</mybatis.plus.version>
    </properties>

    <modules>
        <module>netty-mqtt</module>
        <module>common</module>
        <module>rule-engine</module>
        <module>dao</module>
        <module>transport</module>
        <!--<module>ui</module>-->
        <module>tools</module>
        <module>application</module>
        <module>msa</module>
    </modules>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.5.0.Final</version>
            </extension>
        </extensions>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.5.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <compilerArguments>
                            <verbose />
                            <bootclasspath>${java.home}/lib/rt.jar;${java.home}/lib/jce.jar</bootclasspath>
                        </compilerArguments>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.fortasoft</groupId>
                    <artifactId>gradle-maven-plugin</artifactId>
                    <version>1.0.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>copy-protoc</id>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <artifactItems>
                                    <artifactItem>
                                        <groupId>com.google.protobuf</groupId>
                                        <artifactId>protoc</artifactId>
                                        <version>${protobuf.version}</version>
                                        <classifier>${os.detected.classifier}</classifier>
                                        <type>exe</type>
                                        <overWrite>true</overWrite>
                                        <outputDirectory>${project.build.directory}</outputDirectory>
                                    </artifactItem>
                                </artifactItems>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>0.5.0</version>
                    <configuration>
                        <!--
                          The version of protoc must match protobuf-java. If you don't depend on
                          protobuf-java directly, you will be transitively depending on the
                          protobuf-java version that grpc depends on.
                        -->
                        <protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}
                        </protocArtifact>
                        <pluginId>grpc-java</pluginId>
                        <pluginArtifact>io.grpc:protoc-gen-grpc-java:1.0.0:exe:${os.detected.classifier}
                        </pluginArtifact>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>compile</goal>
                                <goal>compile-custom</goal>
                                <goal>test-compile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.12</version>
                    <executions>
                        <execution>
                            <id>add-source</id>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>add-source</goal>
                            </goals>
                            <configuration>
                                <sources>
                                    <source>${basedir}/target/generated-sources</source>
                                </sources>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.apache.maven.plugins
                                        </groupId>
                                        <artifactId>
                                            maven-antrun-plugin
                                        </artifactId>
                                        <versionRange>
                                            [1.3,)
                                        </versionRange>
                                        <goals>
                                            <goal>run</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore></ignore>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
<!--                <plugin>-->
<!--                    <groupId>com.mycila</groupId>-->
<!--                    <artifactId>license-maven-plugin</artifactId>-->
<!--                    <version>3.0</version>-->
<!--                    <configuration>-->
<!--                        <header>${main.dir}/license-header-template.txt</header>-->
<!--                        <properties>-->
<!--                            <owner>The Thingsboard Authors</owner>-->
<!--                        </properties>-->
<!--                        <excludes>-->
<!--                            <exclude>**/.env</exclude>-->
<!--                            <exclude>**/.eslintrc</exclude>-->
<!--                            <exclude>**/.babelrc</exclude>-->
<!--                            <exclude>**/.jshintrc</exclude>-->
<!--                            <exclude>**/.gradle/**</exclude>-->
<!--                            <exclude>**/nightwatch</exclude>-->
<!--                            <exclude>**/README</exclude>-->
<!--                            <exclude>**/LICENSE</exclude>-->
<!--                            <exclude>**/banner.txt</exclude>-->
<!--                            <exclude>node_modules/**</exclude>-->
<!--                            <exclude>**/*.properties</exclude>-->
<!--                            <exclude>src/test/resources/**</exclude>-->
<!--                            <exclude>src/vendor/**</exclude>-->
<!--                            <exclude>src/font/**</exclude>-->
<!--                            <exclude>src/sh/**</exclude>-->
<!--                            <exclude>src/main/scripts/control/**</exclude>-->
<!--                            <exclude>src/main/scripts/windows/**</exclude>-->
<!--                            <exclude>src/main/resources/public/static/rulenode/**</exclude>-->
<!--                            <exclude>**/*.proto.js</exclude>-->
<!--                            <exclude>docker/haproxy/**</exclude>-->
<!--                            <exclude>docker/tb-node/**</exclude>-->
<!--                        </excludes>-->
<!--                        <mapping>-->
<!--                            <proto>JAVADOC_STYLE</proto>-->
<!--                            <cql>DOUBLEDASHES_STYLE</cql>-->
<!--                            <scss>JAVADOC_STYLE</scss>-->
<!--                            <jsx>SLASHSTAR_STYLE</jsx>-->
<!--                            <conf>SCRIPT_STYLE</conf>-->
<!--                            <gradle>JAVADOC_STYLE</gradle>-->
<!--                        </mapping>-->
<!--                    </configuration>-->
<!--                    <executions>-->
<!--                        <execution>-->
<!--                            <goals>-->
<!--                                <goal>check</goal>-->
<!--                            </goals>-->
<!--                        </execution>-->
<!--                    </executions>-->
<!--                </plugin>-->
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>


    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.thingsboard</groupId>
                <artifactId>netty-mqtt</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common</groupId>
                <artifactId>data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.rule-engine</groupId>
                <artifactId>rule-engine-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.rule-engine</groupId>
                <artifactId>rule-engine-components</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common</groupId>
                <artifactId>message</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common</groupId>
                <artifactId>transport</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common.transport</groupId>
                <artifactId>transport-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common.transport</groupId>
                <artifactId>mqtt</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common.transport</groupId>
                <artifactId>http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common.transport</groupId>
                <artifactId>coap</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard</groupId>
                <artifactId>dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard.common</groupId>
                <artifactId>queue</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thingsboard</groupId>
                <artifactId>tools</artifactId>
                <version>${project.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.thingsboard</groupId>
                <artifactId>dao</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-test</artifactId>
                <version>${spring-security.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.springtestdbunit</groupId>
                <artifactId>spring-test-dbunit</artifactId>
                <version>${spring-test-dbunit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-tools</artifactId>
                <version>${velocity-tools.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>dom4j</groupId>
                        <artifactId>dom4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>antlr</groupId>
                        <artifactId>antlr</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rabbitmq.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${mail.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <scope>test</scope>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path-assert</artifactId>
                <version>${json-path.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-mqtt</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datastax.cassandra</groupId>
                <artifactId>cassandra-driver-core</artifactId>
                <version>${cassandra.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datastax.cassandra</groupId>
                <artifactId>cassandra-driver-mapping</artifactId>
                <version>${cassandra.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datastax.cassandra</groupId>
                <artifactId>cassandra-driver-extras</artifactId>
                <version>${cassandra.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>${commons-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${commons-csv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.fge</groupId>
                <artifactId>json-schema-validator</artifactId>
                <version>${json-schema-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-actor_${scala.version}</artifactId>
                <version>${akka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-slf4j_${scala.version}</artifactId>
                <version>${akka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.californium</groupId>
                <artifactId>californium-core</artifactId>
                <version>${californium.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.takari.junit</groupId>
                <artifactId>takari-cpsuite</artifactId>
                <version>${takari-cpsuite.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.cassandraunit</groupId>
                <artifactId>cassandra-unit</artifactId>
                <version>${cassandra-unit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.dbunit</groupId>
                <artifactId>dbunit</artifactId>
                <version>${dbunit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${paho.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox-swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hsqldb</groupId>
                <artifactId>hsqldb</artifactId>
                <version>${hsqldb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring-data-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.winsw</groupId>
                <artifactId>winsw</artifactId>
                <version>${winsw.version}</version>
                <classifier>bin</classifier>
                <type>exe</type>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>rest</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javadelight</groupId>
                <artifactId>delight-nashorn-sandbox</artifactId>
                <version>${delight-nashorn-sandbox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.vladimir-bukhtoyarov</groupId>
                <artifactId>bucket4j-core</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            <dependency>
                <groupId>de.ruedigermoeller</groupId>
                <artifactId>fst</artifactId>
                <version>${fst.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox.ui</groupId>
                <artifactId>springfox-swagger-ui-rfc6570</artifactId>
                <version>${springfox-swagger-ui-rfc6570.version}</version>
            </dependency>

            <!-- 公式计算 see also https://github.com/killme2008/aviator-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <!--短信 -->
            <dependency>
                <groupId>com.yunpian.sdk</groupId>
                <artifactId>yunpian-java-sdk</artifactId>
                <version>${yunpian.version}</version>
            </dependency>

            <!-- 调用打印-->
            <dependency>
                <groupId>me.vcoder</groupId>
                <artifactId>http-logger</artifactId>
                <version>${logger.version}</version>
            </dependency>

            <!-- druid连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.10</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.70</version>
            </dependency>

            <!-- 定时任务调度 -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/org.quartz-scheduler/quartz-jobs -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz-jobs</artifactId>
                <version>${quartz.version}</version>
            </dependency>


            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-eureka</artifactId>
                <version>1.3.6.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>2.4.0</version>
            </dependency>

            <!--influxDB -->
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-flux</artifactId>
                <version>1.10.0</version>
            </dependency>

            <!-- Sevlet-->
            <!--<dependency>-->
            <!--<groupId>javax.servlet</groupId>-->
            <!--<artifactId>servlet-api</artifactId>-->
            <!--<version>${servlet-api.version}</version>-->
            <!--</dependency>-->
            <!-- 配置加密 -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>1.16</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>typesafe</id>
            <name>Typesafe Repository</name>
            <url>http://repo.typesafe.com/typesafe/releases/</url>
        </repository>
        <repository>
            <id>sonatype</id>
            <url>https://oss.sonatype.org/content/groups/public</url>
        </repository>
        <repository>
            <id>getui-nexus</id>
            <url>http://mvn.gt.igexin.com/nexus/content/repositories/releases/</url>
        </repository>
    </repositories>

</project>
