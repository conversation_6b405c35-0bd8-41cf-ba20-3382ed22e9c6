package org.thingsboard.server.dao.model.sql.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 票据
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.REPORT_TYPE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ReportType {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.REPORT_TYPE_PID)
    private String pid;

    @TableField(exist = false)
    private String parentName;

    @TableField(ModelConstants.REPORT_TYPE_NAME)
    private String name;

    @TableField(ModelConstants.REPORT_TYPE_ORDER_NUM)
    private Integer orderNum;

    @TableField(ModelConstants.REMARK)
    private String remark;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    private transient List<ReportType> children = new ArrayList<>();

}
