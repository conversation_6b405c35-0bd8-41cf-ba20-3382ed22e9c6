import{_ as I}from"./TreeBox-DDD2iwoR.js";import{_ as F}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as A}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{_ as q}from"./index-BJ-QPYom.js";import"./index-0NlGN6gS.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as L,c as C,r as y,l as a,bI as d,bH as m,S as w,b as x,o as E,g as H,h as V,F as R,q as c,i as f,C as B}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as $}from"./usePartition-DkcY9fQ2.js";import{f as W}from"./DateFormatter-Bm9a68Ax.js";import{E as G,G as z,_ as J}from"./RecordList.vue_vue_type_script_setup_true_lang-BDqxCe-t.js";import{s as K}from"./printUtils-C-AxhDcd.js";import{C as Q}from"./useWaterCorrect-CpCzjcCp.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const U=L({__name:"index",setup(X){const g=C(),v=C(),i=y({data:[],loading:!0,title:"选择分区",expandOnClickNode:!1,defaultExpandAll:!0,treeNodeHandleClick:t=>{i.currentProject!==t&&(i.currentProject=t,l())}}),D=$(),T=async()=>{var t;await D.getTree(),i.data=D.Tree.value,i.currentProject=(t=D.Tree.value)==null?void 0:t[0],l()},P=y({defaultParams:{type:"month",month:[a().subtract(1,"M").startOf("M").format(d),a().endOf("M").format(d)],date:[a().subtract(1,"M").startOf("D").format(m),a().endOf("D").format(m)]},filters:[{type:"input",label:"用户名称",field:"custName",placeholder:"请输入用户姓名"},{type:"input",label:"营收户号",field:"custCode",placeholder:"请输入营收户号"},{type:"select",field:"type",width:90,clearable:!1,options:[{label:"按年月",value:"month"},{label:"按年月日",value:"date"}],label:"选择方式",placeholder:"请选择"},{handleHidden:(t,e,r)=>{r.hidden=t.type==="date"},type:"monthrange",format:d,field:"month",clearable:!1},{handleHidden:(t,e,r)=>{r.hidden=t.type==="month"},type:"daterange",field:"date",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>l()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var t;(t=v.value)==null||t.resetForm()}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>{l(!0)}},{perm:!0,text:"修正记录",type:"default",iconifyIcon:"mdi:eye",click:()=>S()}]}]}),o=y({indexVisible:!0,dataList:[],columns:[{label:"分区名称",minWidth:120,prop:"partitionName"},{label:"用户名称",minWidth:120,prop:"custName"},{label:"营收户号",minWidth:120,prop:"custCode"},{label:"联系方式",minWidth:120,prop:"phone"},{label:"抄表年月",minWidth:120,prop:"ym"},{label:"修正水量",minWidth:120,prop:"correctWater"},{label:"抄表水量",minWidth:120,prop:"totalWater"},{label:"本次抄表日期",minWidth:120,prop:"thisReadDate",formatter:(t,e)=>W(e,m)},{label:"上次抄表时间",minWidth:120,prop:"lastReadDate",formatter:(t,e)=>W(e,m)}],operations:[{perm:!0,text:"修改",iconifyIcon:"ep:edit",click:t=>N(t)}],pagination:{refreshData:({page:t,size:e})=>{o.pagination.page=t,o.pagination.limit=e,l()}}}),u=y({dialogWidth:450,title:"编辑",defaultValue:{},group:[{fields:[{type:"input",label:"营收户号",disabled:!0,field:"custCode",placeholder:" "},{type:"input",label:"用户姓名",field:"custName",disabled:!0,placeholder:" "},{type:"input",label:"联系方式",field:"phone",disabled:!0,placeholder:" "},{type:"datetime",label:"抄表日期",field:"thisReadDate",readonly:!0},{type:"number",label:"水量",field:"totalWater"}]}],submit:t=>{w("确定提交？","提示信息").then(async()=>{var e;try{u.submitting=!0;const r={id:t.id,correctWater:t.totalWater},n=await Q(r);n.data.code===200?(x.success("操作成功"),l(),(e=g.value)==null||e.closeDialog()):x.error(n.data.message)}catch{x.error("操作失败")}u.submitting=!1}).catch(()=>{})}}),N=t=>{var e;u.defaultValue={...t||{},thisReadDate:W(t.thisReadDate,m),id:t==null?void 0:t.id},(e=g.value)==null||e.openDialog()},M=C(),S=()=>{var t,e;(e=(t=M.value)==null?void 0:t.refRecord)==null||e.openDialog()},l=async t=>{var r,n,h,_,b,s;const e=((r=v.value)==null?void 0:r.queryParams)||{};try{o.loading=!0;const p={page:o.pagination.page||1,size:o.pagination.limit||20,custCode:e.custCode,custName:e.custName,start:void 0,end:void 0,partitionId:(n=i.currentProject)==null?void 0:n.value};if(e.type==="month"?(p.start=((h=e.month)==null?void 0:h[0])&&a(e.month[0],d).startOf("M").valueOf()||void 0,p.end=((_=e.month)==null?void 0:_[1])&&a(e.month[1],d).endOf("M").valueOf()||void 0):(p.start=((b=e.date)==null?void 0:b[0])&&a(e.date[0],m).startOf("D").valueOf()||void 0,p.end=((s=e.date)==null?void 0:s[1])&&a(e.date[1],m).endOf("D").valueOf()||void 0),t){const k=await G(p);K(k.data,"用水量")}else{const O=(await z(p)).data.data;o.dataList=O.data||[],o.pagination.total=O.total||0}}catch{}o.loading=!1};return E(async()=>{T()}),(t,e)=>{const r=q,n=j,h=A,_=F,b=I;return H(),V(b,null,{tree:R(()=>[c(r,{ref:"refTree","tree-data":f(i)},null,8,["tree-data"])]),default:R(()=>{var s;return[c(n,{ref_key:"refSearch",ref:v,config:f(P)},null,8,["config"]),c(h,{config:f(o),class:"card-table"},null,8,["config"]),c(_,{ref_key:"refForm",ref:g,config:f(u)},null,8,["config"]),c(J,{ref_key:"refRecord",ref:M,"partition-id":(s=f(i).currentProject)==null?void 0:s.value},null,8,["partition-id"])]}),_:1})}}}),Me=B(U,[["__scopeId","data-v-b7553785"]]);export{Me as default};
