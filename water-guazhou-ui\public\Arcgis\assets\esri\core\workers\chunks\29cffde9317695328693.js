"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7483],{92835:(e,t,i)=>{i.d(t,{Z:()=>g});var s,r=i(43697),n=i(96674),l=i(70586),a=i(35463),o=i(5600),h=(i(75215),i(67676),i(71715)),c=i(52011),u=i(30556);let d=s=class extends n.wq{static get allTime(){return p}static get empty(){return f}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(s.allTime)}get isEmpty(){return this.equals(s.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new s({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,l.pC)(this.start)?this.start.getTime():this.start,i=(0,l.pC)(this.end)?this.end.getTime():this.end,s=(0,l.pC)(e.start)?e.start.getTime():e.start,r=(0,l.pC)(e.end)?e.end.getTime():e.end;return t===s&&i===r}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,l.yw)(this.start,(t=>(0,a.JE)(t,e))),i=(0,l.yw)(this.end,(t=>{const i=(0,a.JE)(t,e);return t.getTime()===i.getTime()?i:(0,a.Nm)(i,1,e)}));return new s({start:t,end:i})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return s.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,l.R2)(this.start,-1/0,(e=>e.getTime())),i=(0,l.R2)(this.end,1/0,(e=>e.getTime())),r=(0,l.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,l.R2)(e.end,1/0,(e=>e.getTime()));let a,o;if(r>=t&&r<=i?a=r:t>=r&&t<=n&&(a=t),i>=r&&i<=n?o=i:n>=t&&n<=i&&(o=n),null!=a&&null!=o&&!isNaN(a)&&!isNaN(o)){const e=new s;return e.start=a===-1/0?null:new Date(a),e.end=o===1/0?null:new Date(o),e}return s.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const i=new s,{start:r,end:n}=this;return(0,l.pC)(r)&&(i.start=(0,a.Nm)(r,e,t)),(0,l.pC)(n)&&(i.end=(0,a.Nm)(n,e,t)),i}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return p.clone();const t=(0,l.pC)(this.start)&&(0,l.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,i=(0,l.pC)(this.end)&&(0,l.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new s({start:t,end:i})}};(0,r._)([(0,o.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"end",void 0),(0,r._)([(0,h.r)("end")],d.prototype,"readEnd",null),(0,r._)([(0,u.c)("end")],d.prototype,"writeEnd",null),(0,r._)([(0,o.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isAllTime",null),(0,r._)([(0,o.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isEmpty",null),(0,r._)([(0,o.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"start",void 0),(0,r._)([(0,h.r)("start")],d.prototype,"readStart",null),(0,r._)([(0,u.c)("start")],d.prototype,"writeStart",null),d=s=(0,r._)([(0,c.j)("esri.TimeExtent")],d);const p=new d,f=new d({start:void 0,end:void 0}),g=d},5732:(e,t,i)=>{i.d(t,{c:()=>s});var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},46791:(e,t,i)=>{i.d(t,{Z:()=>L});var s,r=i(43697),n=i(3894),l=i(32448),a=i(22974),o=i(70586),h=i(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(s||(s={}));var c,u=i(1654),d=i(5600),p=i(75215),f=i(52421),g=i(52011),m=i(58971),y=i(10661);const _=new h.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),b=()=>{};function v(e){return e?e instanceof O?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function w(e){if(e&&e.length)return e[0]}function C(e,t,i,s){const r=Math.min(e.length-i,t.length-s);let n=0;for(;n<r&&e[i+n]===t[s+n];)n++;return n}function E(e,t,i,s){t&&t.forEach(((t,r,n)=>{e.push(t),E(e,i.call(s,t,r,n),i,s)}))}const A=new Set,M=new Set,D=new Set,I=new Map;let T=0,O=c=class extends l.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof c}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new y.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:T++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof c?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,m.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(s.ADD)||(this._splice(0,this.length,v(e)),this._emitAfterChanges(s.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,i={removed:!1,callback:t};return e.push(i),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=b,i.removed=!0,e.splice(e.indexOf(i),1)}}}return this._emitter.on(e,t)}once(e,t){const i=this.on(e,t);return{remove(){i.remove()}}}add(e,t){if((0,m.it)(this._observable),this._emitBeforeChanges(s.ADD))return this;const i=this.getNextIndex(t??null);return this._splice(i,0,[e]),this._emitAfterChanges(s.ADD),this}addMany(e,t=this._items.length){if((0,m.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(s.ADD))return this;const i=this.getNextIndex(t);return this._splice(i,0,v(e)),this._emitAfterChanges(s.ADD),this}at(e){if((0,m.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(s.REMOVE),e}clone(){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.map(a.d9)})}concat(...e){(0,m.it)(this._observable);const t=e.map(v);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const i=(0,o.j0)(this._splice(0,this.length)),r=i.length;for(let s=0;s<r;s++)e.call(t,i[s],s,i);this._emitAfterChanges(s.REMOVE)}every(e,t){return(0,m.it)(this._observable),this._items.every(e,t)}filter(e,t){let i;return(0,m.it)(this._observable),i=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:i})}find(e,t){return(0,m.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,m.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,m.it)(this._observable);const i=[];return E(i,this,e,t),new c(i)}forEach(e,t){return(0,m.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,m.it)(this._observable),this._items[e]}getNextIndex(e){(0,m.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,m.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,m.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,m.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,m.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,m.it)(this._observable);const i=this._items.map(e,t);return new c({items:i})}reorder(e,t=this.length-1){(0,m.it)(this._observable);const i=this.indexOf(e);if(-1!==i){if(t<0?t=0:t>=this.length&&(t=this.length-1),i!==t){if(this._emitBeforeChanges(s.MOVE))return e;this._splice(i,1),this._splice(t,0,[e]),this._emitAfterChanges(s.MOVE)}return e}}pop(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=w(this._splice(this.length-1,1));return this._emitAfterChanges(s.REMOVE),e}push(...e){return(0,m.it)(this._observable),this._emitBeforeChanges(s.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(s.ADD)),this.length}reduce(e,t){(0,m.it)(this._observable);const i=this._items;return 2===arguments.length?i.reduce(e,t):i.reduce(e)}reduceRight(e,t){(0,m.it)(this._observable);const i=this._items;return 2===arguments.length?i.reduceRight(e,t):i.reduceRight(e)}remove(e){return(0,m.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,m.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(s.REMOVE))return;const t=w(this._splice(e,1));return this._emitAfterChanges(s.REMOVE),t}removeMany(e){if((0,m.it)(this._observable),!e||!e.length||this._emitBeforeChanges(s.REMOVE))return[];const t=e instanceof c?e.toArray():e,i=this._items,r=[],n=t.length;for(let e=0;e<n;e++){const s=t[e],n=i.indexOf(s);if(n>-1){const s=1+C(t,i,e+1,n+1),l=this._splice(n,s);l&&l.length>0&&r.push.apply(r,l),e+=s-1}}return this._emitAfterChanges(s.REMOVE),r}reverse(){if((0,m.it)(this._observable),this._emitBeforeChanges(s.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(s.MOVE),this}shift(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=w(this._splice(0,1));return this._emitAfterChanges(s.REMOVE),e}slice(e=0,t=this.length){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,m.it)(this._observable),this._items.some(e,t)}sort(e){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(s.MOVE))return this;const t=(0,o.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(s.MOVE),this}splice(e,t,...i){(0,m.it)(this._observable);const r=(t?s.REMOVE:0)|(i.length?s.ADD:0);if(this._emitBeforeChanges(r))return[];const n=this._splice(e,t,i)||[];return this._emitAfterChanges(r),n}toArray(){return(0,m.it)(this._observable),this._items.slice()}toJSON(){return(0,m.it)(this._observable),this.toArray()}toLocaleString(){return(0,m.it)(this._observable),this._items.toLocaleString()}toString(){return(0,m.it)(this._observable),this._items.toString()}unshift(...e){return(0,m.it)(this._observable),!e.length||this._emitBeforeChanges(s.ADD)||(this._splice(0,0,e),this._emitAfterChanges(s.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,i){const s=this._items,r=this.itemType;let n,l;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,u.Os)((()=>this._dispatchChange()))),t){if(l=s.splice(e,t),this.hasEventListener("before-remove")){const t=_.acquire();t.target=this,t.cancellable=!0;for(let i=0,r=l.length;i<r;i++)n=l[i],t.reset(n),this.emit("before-remove",t),t.defaultPrevented&&(l.splice(i,1),s.splice(e,0,n),e+=1,i-=1,r-=1);_.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=_.acquire();e.target=this,e.cancellable=!1;const t=l.length;for(let i=0;i<t;i++)e.reset(l[i]),this.emit("after-remove",e);_.release(e)}}if(i&&i.length){if(r){const e=[];for(const t of i){const i=r.ensureType(t);null==i&&null!=t||e.push(i)}i=e}const t=this.hasEventListener("before-add"),n=this.hasEventListener("after-add"),l=e===this.length;if(t||n){const r=_.acquire();r.target=this,r.cancellable=!0;const a=_.acquire();a.target=this,a.cancellable=!1;for(const o of i)t?(r.reset(o),this.emit("before-add",r),r.defaultPrevented||(l?s.push(o):s.splice(e++,0,o),this._set("length",s.length),n&&(a.reset(o),this.emit("after-add",a)))):(l?s.push(o):s.splice(e++,0,o),this._set("length",s.length),a.reset(o),this.emit("after-add",a));_.release(a),_.release(r)}else{if(l)for(const e of i)s.push(e);else s.splice(e,0,...i);this._set("length",s.length)}}return(i&&i.length||l&&l.length)&&this._notifyChangeEvent(i,l),l}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const i=_.acquire();i.target=this,i.cancellable=!0,i.type=e,this.emit("before-changes",i),t=i.defaultPrevented,_.release(i)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=_.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),_.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;A.clear(),M.clear(),D.clear();for(const{added:t,removed:i}of e){if(t)if(0===D.size&&0===M.size)for(const e of t)A.add(e);else for(const e of t)M.has(e)?(D.add(e),M.delete(e)):D.has(e)||A.add(e);if(i)if(0===D.size&&0===A.size)for(const e of i)M.add(e);else for(const e of i)A.has(e)?A.delete(e):(D.delete(e),M.add(e))}const i=n.Z.acquire();A.forEach((e=>{i.push(e)}));const s=n.Z.acquire();M.forEach((e=>{s.push(e)}));const r=this._items,l=t.items,a=n.Z.acquire();if(D.forEach((e=>{l.indexOf(e)!==r.indexOf(e)&&a.push(e)})),t.listeners&&(i.length||s.length||a.length)){const e={target:this,added:i,removed:s,moved:a},r=t.listeners.length;for(let i=0;i<r;i++){const s=t.listeners[i];s.removed||s.callback.call(this,e)}}n.Z.release(i),n.Z.release(s),n.Z.release(a)}A.clear(),M.clear(),D.clear()}};O.ofType=e=>{if(!e)return c;if(I.has(e))return I.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const i in e.typeMap){const s=e.typeMap[i].prototype.declaredClass;t?t+=` | ${s}`:t=s}let i=class extends c{};return(0,r._)([(0,f.c)({Type:e,ensureType:"function"==typeof e?(0,p.se)(e):(0,p.N7)(e)})],i.prototype,"itemType",void 0),i=(0,r._)([(0,g.j)(`esri.core.Collection<${t}>`)],i),I.set(e,i),i},(0,r._)([(0,d.Cb)()],O.prototype,"length",void 0),(0,r._)([(0,d.Cb)()],O.prototype,"items",null),O=c=(0,r._)([(0,g.j)("esri.core.Collection")],O);const L=O},57435:(e,t,i)=>{i.d(t,{Z:()=>c});var s=i(43697),r=i(46791),n=i(70586),l=(i(80442),i(20102),i(92604),i(26258),i(87538)),a=i(5600),o=(i(75215),i(67676),i(52011));let h=class extends r.Z{constructor(e){super(e),this.getCollections=null}initialize(){this.own((0,l.EH)((()=>this._refresh())))}destroy(){this.getCollections=null}_refresh(){const e=(0,n.pC)(this.getCollections)?this.getCollections():null;if((0,n.Wi)(e))return void this.removeAll();let t=0;for(const i of e)(0,n.pC)(i)&&(t=this._processCollection(t,i));this.splice(t,this.length)}_createNewInstance(e){return new r.Z(e)}_processCollection(e,t){if(!t)return e;const i=this.itemFilterFunction?this.itemFilterFunction:e=>!!e;for(const s of t)if(s){if(i(s)){const t=this.indexOf(s,e);t>=0?t!==e&&this.reorder(s,e):this.add(s,e),++e}if(this.getChildrenFunction){const t=this.getChildrenFunction(s);if(Array.isArray(t))for(const i of t)e=this._processCollection(e,i);else e=this._processCollection(e,t)}}return e}};(0,s._)([(0,a.Cb)()],h.prototype,"getCollections",void 0),(0,s._)([(0,a.Cb)()],h.prototype,"getChildrenFunction",void 0),(0,s._)([(0,a.Cb)()],h.prototype,"itemFilterFunction",void 0),h=(0,s._)([(0,o.j)("esri.core.CollectionFlattener")],h);const c=h},52421:(e,t,i)=>{function s(e){return(t,i)=>{t[i]=e}}i.d(t,{c:()=>s})},70921:(e,t,i)=>{i.d(t,{R:()=>n,Z:()=>r});var s=i(46791);function r(e,t,i=s.Z){return t||(t=new i),t===e||(t.removeAll(),(r=e)&&(Array.isArray(r)||"items"in r&&Array.isArray(r.items))?t.addMany(e):e&&t.add(e)),t;var r}function n(e){return e}},68668:(e,t,i)=>{i.d(t,{G:()=>a,w:()=>o});var s=i(66643),r=i(46791),n=i(83379),l=i(70586);async function a(e,t){return await e.load(),o(e,t)}async function o(e,t){const i=[],a=(...e)=>{for(const t of e)(0,l.Wi)(t)||(Array.isArray(t)?a(...t):r.Z.isCollection(t)?t.forEach((e=>a(e))):n.Z.isLoadable(t)&&i.push(t))};t(a);let o=null;if(await(0,s.UI)(i,(async e=>{const t=await(0,s.q6)(function(e){return"loadAll"in e&&"function"==typeof e.loadAll}(e)?e.loadAll():e.load());!1!==t.ok||o||(o=t)})),o)throw o.error;return e}},17445:(e,t,i)=>{i.d(t,{N1:()=>d,YP:()=>o,Z_:()=>g,gx:()=>h,nn:()=>m,on:()=>u,tX:()=>y});var s=i(91460),r=i(50758),n=i(70586),l=i(95330),a=i(26258);function o(e,t,i={}){return c(e,t,i,p)}function h(e,t,i={}){return c(e,t,i,f)}function c(e,t,i={},s){let r=null;const l=i.once?(e,i)=>{s(e)&&((0,n.hw)(r),t(e,i))}:(e,i)=>{s(e)&&t(e,i)};if(r=(0,a.aQ)(e,l,i.sync,i.equals),i.initial){const t=e();l(t,t)}return r}function u(e,t,i,l={}){let a=null,h=null,c=null;function u(){a&&h&&(h.remove(),l.onListenerRemove?.(a),a=null,h=null)}function d(e){l.once&&l.once&&(0,n.hw)(c),i(e)}const p=o(e,((e,i)=>{u(),(0,s.vT)(e)&&(a=e,h=(0,s.on)(e,t,d),l.onListenerAdd?.(e))}),{sync:l.sync,initial:!0});return c=(0,r.kB)((()=>{p.remove(),u()})),c}function d(e,t){return function(e,t,i){if((0,l.Hc)(i))return Promise.reject((0,l.zE)());const s=e();if(t?.(s))return Promise.resolve(s);let a=null;function o(){a=(0,n.hw)(a)}return new Promise(((s,n)=>{a=(0,r.AL)([(0,l.fu)(i,(()=>{o(),n((0,l.zE)())})),c(e,(e=>{o(),s(e)}),{sync:!1,once:!0},t??p)])}))}(e,f,t)}function p(e){return!0}function f(e){return!!e}i(87538);const g={sync:!0},m={initial:!0},y={sync:!0,initial:!0}},35463:(e,t,i)=>{i.d(t,{JE:()=>l,Nm:()=>n,rJ:()=>a}),i(80442);const s={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},r={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,i){const s=new Date(e.getTime());if(t&&i){const e=r[i],{getter:n,setter:l,multiplier:a}=e;if("months"===i){const e=function(e,t){const i=new Date(e,t+1,1);return i.setDate(0),i.getDate()}(s.getFullYear(),s.getMonth()+t);s.getDate()>e&&s.setDate(e)}s[l](s[n]()+t*a)}return s}function l(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function a(e,t,i){return 0===e?0:e*s[t]/s[i]}},89348:(e,t,i)=>{i.r(t),i.d(t,{default:()=>O});var s=i(43697),r=i(57435),n=i(68668),l=i(70586),a=i(16453),o=i(17445),h=i(5600),c=(i(75215),i(67676),i(1153)),u=i(52011),d=i(30556),p=i(87085),f=i(71612),g=i(38009),m=i(16859),y=i(72965),_=i(87344),b=i(46791),v=i(70921),w=i(92604),C=i(95330);function E(e,t,i){let s,r;if(e)for(let n=0,l=e.length;n<l;n++){if(s=e.getItemAt(n),s[t]===i)return s;if("group"===s?.type&&(r=E(s.layers,t,i),r))return r}}const A=e=>{let t=class extends e{constructor(...e){super(...e),this.layers=new b.Z;const t=e=>{e.parent=this,this.layerAdded(e),"elevation"!==e.type&&"base-elevation"!==e.type||w.Z.getLogger(this.declaredClass).error(`Layer 'title:${e.title}, id:${e.id}' of type '${e.type}' is not supported as an operational layer and will therefore be ignored.`)},i=e=>{e.parent=null,this.layerRemoved(e)};this.layers.on("before-add",(e=>(e=>{e.parent&&"remove"in e.parent&&e.parent.remove(e)})(e.item))),this.layers.on("after-add",(e=>t(e.item))),this.layers.on("after-remove",(e=>i(e.item)))}destroy(){const e=this.layers.removeAll();for(const t of e)this.layerRemoved(t),t.destroy();this.layers.destroy()}set layers(e){this._set("layers",(0,v.Z)(e,this._get("layers")))}add(e,t){const i=this.layers;if(t=i.getNextIndex(t),e instanceof p.Z){const s=e;s.parent===this?this.reorder(s,t):i.add(s,t)}else(0,C.y8)(e)?e.then((e=>{this.destroyed||this.add(e,t)})):w.Z.getLogger(this.declaredClass).error("#add()","The item being added is not a Layer or a Promise that resolves to a Layer.")}addMany(e,t){const i=this.layers;let s=i.getNextIndex(t);e.slice().forEach((e=>{e.parent!==this?(i.add(e,s),s+=1):this.reorder(e,s)}))}findLayerById(e){return E(this.layers,"id",e)}findLayerByUid(e){return E(this.layers,"uid",e)}remove(e){return this.layers.remove(e)}removeMany(e){return this.layers.removeMany(e)}removeAll(){return this.layers.removeAll()}reorder(e,t){return this.layers.reorder(e,t)}layerAdded(e){}layerRemoved(e){}};return(0,s._)([(0,h.Cb)()],t.prototype,"layers",null),t=(0,s._)([(0,u.j)("esri.support.LayersMixin")],t),t};function M(e,t,i){if(e)for(let s=0,r=e.length;s<r;s++){const r=e.getItemAt(s);if(r[t]===i)return r;if("group"===r?.type){const e=M(r.tables,t,i);if(e)return e}}}const D=e=>{let t=class extends e{constructor(...e){super(...e),this.tables=new b.Z,this.tables.on("after-add",(e=>{const t=e.item;t.parent&&t.parent!==this&&"tables"in t.parent&&t.parent.tables.remove(t),t.parent=this,"feature"!==t.type&&w.Z.getLogger(this.declaredClass).error(`Layer 'title:${t.title}, id:${t.id}' of type '${t.type}' is not supported as a table and will therefore be ignored.`)})),this.tables.on("after-remove",(e=>{e.item.parent=null}))}destroy(){const e=this.tables.removeAll();for(const t of e)t.destroy();this.tables.destroy()}set tables(e){this._set("tables",(0,v.Z)(e,this._get("tables")))}findTableById(e){return M(this.tables,"id",e)}findTableByUid(e){return M(this.tables,"uid",e)}};return(0,s._)([(0,h.Cb)()],t.prototype,"tables",null),t=(0,s._)([(0,u.j)("esri.support.TablesMixin")],t),t};var I=i(15650);let T=class extends((0,f.h)((0,y.M)((0,g.q)((0,m.I)(D(A((0,a.R)(p.Z)))))))){constructor(e){var t;super(e),this._visibilityHandles={},this.allLayers=new r.Z({getCollections:()=>[this.layers],getChildrenFunction:e=>"layers"in e?e.layers:null}),this.allTables=(t=this,new r.Z({getCollections:()=>[t.tables,t.layers],getChildrenFunction:e=>{const t=[];return"tables"in e&&t.push(e.tables),"layers"in e&&t.push(e.layers),t},itemFilterFunction:e=>{const t=e.parent;return!!t&&"tables"in t&&t.tables.includes(e)}})),this.fullExtent=void 0,this.operationalLayerType="GroupLayer",this.spatialReference=void 0,this.type="group"}initialize(){this._enforceVisibility(this.visibilityMode,this.visible),this.addHandles((0,o.YP)((()=>this.visible),this._onVisibilityChange.bind(this),o.Z_))}_writeLayers(e,t,i,s){const r=[];if(!e)return r;e.forEach((e=>{const t=(0,I.Nw)(e,s.webmap?s.webmap.getLayerJSONFromResourceInfo(e):null,s);(0,l.pC)(t)&&t.layerType&&r.push(t)})),t.layers=r}set portalItem(e){this._set("portalItem",e)}set visibilityMode(e){const t=this._get("visibilityMode")!==e;this._set("visibilityMode",e),t&&this._enforceVisibility(e,this.visible)}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Feature Service","Feature Collection","Scene Service"],layerModuleTypeMap:_.T},e)),Promise.resolve(this)}async loadAll(){return(0,n.G)(this,(e=>{e(this.layers,this.tables)}))}layerAdded(e){e.visible&&"exclusive"===this.visibilityMode?this._turnOffOtherLayers(e):"inherited"===this.visibilityMode&&(e.visible=this.visible),this._visibilityHandles[e.uid]=(0,o.YP)((()=>e.visible),(t=>this._onChildVisibilityChange(e,t)),o.Z_)}layerRemoved(e){const t=this._visibilityHandles[e.uid];t&&(t.remove(),delete this._visibilityHandles[e.uid]),this._enforceVisibility(this.visibilityMode,this.visible)}_turnOffOtherLayers(e){this.layers.forEach((t=>{t!==e&&(t.visible=!1)}))}_enforceVisibility(e,t){if(!(0,c.vw)(this).initialized)return;const i=this.layers;let s=i.find((e=>e.visible));switch(e){case"exclusive":i.length&&!s&&(s=i.getItemAt(0),s.visible=!0),this._turnOffOtherLayers(s);break;case"inherited":i.forEach((e=>{e.visible=t}))}}_onVisibilityChange(e){"inherited"===this.visibilityMode&&this.layers.forEach((t=>{t.visible=e}))}_onChildVisibilityChange(e,t){switch(this.visibilityMode){case"exclusive":t?this._turnOffOtherLayers(e):this._isAnyLayerVisible()||(e.visible=!0);break;case"inherited":e.visible=this.visible}}_isAnyLayerVisible(){return this.layers.some((e=>e.visible))}};(0,s._)([(0,h.Cb)({readOnly:!0,dependsOn:[]})],T.prototype,"allLayers",void 0),(0,s._)([(0,h.Cb)({readOnly:!0})],T.prototype,"allTables",void 0),(0,s._)([(0,h.Cb)()],T.prototype,"fullExtent",void 0),(0,s._)([(0,h.Cb)({json:{read:!0,write:!0}})],T.prototype,"blendMode",void 0),(0,s._)([(0,h.Cb)({json:{read:!1,write:{ignoreOrigin:!0}}})],T.prototype,"layers",void 0),(0,s._)([(0,d.c)("layers")],T.prototype,"_writeLayers",null),(0,s._)([(0,h.Cb)({type:["GroupLayer"]})],T.prototype,"operationalLayerType",void 0),(0,s._)([(0,h.Cb)({json:{origins:{"web-document":{read:!1,write:!1}}}})],T.prototype,"portalItem",null),(0,s._)([(0,h.Cb)()],T.prototype,"spatialReference",void 0),(0,s._)([(0,h.Cb)({json:{read:!1},readOnly:!0,value:"group"})],T.prototype,"type",void 0),(0,s._)([(0,h.Cb)({type:["independent","inherited","exclusive"],value:"independent",json:{write:!0,origins:{"web-map":{read:!1,write:!1}}}})],T.prototype,"visibilityMode",null),T=(0,s._)([(0,u.j)("esri.layers.GroupLayer")],T);const O=T},16859:(e,t,i)=>{i.d(t,{I:()=>C});var s=i(43697),r=i(68773),n=i(40330),l=i(3172),a=i(66643),o=i(20102),h=i(92604),c=i(70586),u=i(95330),d=i(17452),p=i(5600),f=(i(75215),i(67676),i(71715)),g=i(52011),m=i(30556),y=i(84230),_=i(65587),b=i(15235),v=i(86082),w=i(14661);const C=e=>{let t=class extends e{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,c.SC)(this.portalItem)}set portalItem(e){e!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",e))}readPortalItem(e,t,i){if(t.itemId)return new b.default({id:t.itemId,portal:i&&i.portal})}writePortalItem(e,t){e&&e.id&&(t.itemId=e.id)}async loadFromPortal(e,t){if(this.portalItem&&this.portalItem.id)try{const s=await i.e(8062).then(i.bind(i,18062));return(0,u.k_)(t),await s.load({instance:this,supportedTypes:e.supportedTypes,validateItem:e.validateItem,supportsData:e.supportsData,layerModuleTypeMap:e.layerModuleTypeMap},t)}catch(e){throw(0,u.D_)(e)||h.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${e}`),e}}async finishLoadEditablePortalLayer(e){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(e).catch((e=>((0,u.r9)(e),!0))))}async _setUserPrivileges(e,t){if(!r.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(t);if(this.url)try{const{features:{edit:i,fullEdit:s},content:{updateItem:r}}=await this._fetchUserPrivileges(e,t);this._set("userHasEditingPrivileges",i),this._set("userHasFullEditingPrivileges",s),this._set("userHasUpdateItemPrivileges",r)}catch(e){(0,u.r9)(e)}}async _fetchUserPrivileges(e,t){let i=this.portalItem;if(!e||!i||!i.loaded||i.sourceUrl)return this._fetchFallbackUserPrivileges(t);const s=e===i.id;if(s&&i.portal.user)return(0,w.Ss)(i);let r,l;if(s)r=i.portal.url;else try{r=await(0,y.oP)(this.url,t)}catch(e){(0,u.r9)(e)}if(!r||!(0,d.Zo)(r,i.portal.url))return this._fetchFallbackUserPrivileges(t);try{const e=(0,c.pC)(t)?t.signal:null;l=await(n.id?.getCredential(`${r}/sharing`,{prompt:!1,signal:e}))}catch(e){(0,u.r9)(e)}if(!l)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(s?await i.reload():(i=new b.default({id:e,portal:{url:r}}),await i.load(t)),i.portal.user)return(0,w.Ss)(i)}catch(e){(0,u.r9)(e)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(e){let t=!0;try{t=await this._fetchUserHasEditingPrivileges(e)}catch(e){(0,u.r9)(e)}return{features:{edit:t,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(e){const t=this.url?n.id?.findCredential(this.url):null;if(!t)return!0;const i=E.credential===t?E.user:await this._fetchEditingUser(e);return E.credential=t,E.user=i,(0,c.Wi)(i)||null==i.privileges||i.privileges.includes("features:user:edit")}async _fetchEditingUser(e){const t=this.portalItem?.portal?.user;if(t)return t;const i=n.id.findServerInfo(this.url??"");if(!i?.owningSystemUrl)return null;const s=`${i.owningSystemUrl}/sharing/rest`,r=_.Z.getDefault();if(r&&r.loaded&&(0,d.Fv)(r.restUrl)===(0,d.Fv)(s))return r.user;const o=`${s}/community/self`,h=(0,c.pC)(e)?e.signal:null,u=await(0,a.q6)((0,l.default)(o,{authMode:"no-prompt",query:{f:"json"},signal:h}));return u.ok?v.default.fromJSON(u.value.data):null}read(e,t){t&&(t.layer=this),super.read(e,t)}write(e,t){const i=t&&t.portal,s=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||_.Z.getDefault());return i&&s&&!(0,d.tm)(s.restUrl,i.restUrl)?(t.messages&&t.messages.push(new o.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(e,{...t,layer:this})}};return(0,s._)([(0,p.Cb)({type:b.default})],t.prototype,"portalItem",null),(0,s._)([(0,f.r)("web-document","portalItem",["itemId"])],t.prototype,"readPortalItem",null),(0,s._)([(0,m.c)("web-document","portalItem",{itemId:{type:String}})],t.prototype,"writePortalItem",null),(0,s._)([(0,p.Cb)({clonable:!1})],t.prototype,"resourceReferences",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasEditingPrivileges",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasFullEditingPrivileges",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasUpdateItemPrivileges",void 0),t=(0,s._)([(0,g.j)("esri.layers.mixins.PortalLayer")],t),t},E={credential:null,user:null}},15650:(e,t,i)=>{i.d(t,{Nw:()=>d});var s=i(20102),r=i(22974),n=i(70586),l=i(78286),a=i(827),o=i(84230);const h=new Set(["bing-maps","imagery","imagery-tile","map-image","open-street-map","tile","unknown","unsupported","vector-tile","web-tile","wms","wmts"]),c=new Set(["csv","feature","geo-rss","geojson","group","imagery","imagery-tile","kml","map-image","map-notes","media","ogc-feature","route","subtype-group","tile","unknown","unsupported","vector-tile","web-tile","wfs","wms","wmts"]);function u(e,t){"maxScale"in e&&(t.maxScale=(0,a.k)(e.maxScale)??void 0),"minScale"in e&&(t.minScale=(0,a.k)(e.minScale)??void 0)}function d(e,t,i){if(!("write"in e)||!e.write)return i&&i.messages&&i.messages.push(new s.Z("layer:unsupported",`Layers (${e.title}, ${e.id}) of type '${e.declaredClass}' cannot be persisted`,{layer:e})),null;if(function(e,t){if(t.restrictedWebMapWriting){const i=function(e){return"basemap"===e.layerContainerType?h:"operational-layers"===e.layerContainerType?c:null}(t);return!(0,n.pC)(i)||i.has(e.type)&&!(0,o.rQ)(e)}return!0}(e,i)){const t={};return e.write(t,i)?t:null}return(0,n.pC)(t)&&function(e,t){if(function(e,t){if(t)if((0,o.rQ)(e)){const i=(0,l.hS)("featureCollection.layers",t),s=i&&i[0]&&i[0].layerDefinition;s&&u(e,s)}else"stream"===e.type?u(e,t.layerDefinition=t.layerDefinition||{}):"group"!==e.type&&u(e,t)}(e,t),t&&("blendMode"in e&&(t.blendMode=e.blendMode,"normal"===t.blendMode&&delete t.blendMode),t.opacity=(0,a.k)(e.opacity)??void 0,t.title=e.title||"Layer",t.visibility=e.visible,"legendEnabled"in e&&"wmts"!==e.type))if((0,o.rQ)(e)){const i=t.featureCollection;i&&(i.showLegend=e.legendEnabled)}else t.showLegend=e.legendEnabled}(e,t=(0,r.d9)(t)),t}}}]);