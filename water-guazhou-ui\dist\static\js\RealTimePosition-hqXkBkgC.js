import{d as N,c as x,r as g,s as h,g as b,h as L,F as d,p as l,q as f,G as k,i,bh as F,b7 as M,n as q,aJ as E,aB as U,bk as A,J as D,_ as H,de as J,al as $,ar as K,a1 as O,df as T,C as W}from"./index-r0dFAfgr.js";import{g as B}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as j}from"./ViewHelper-BGCZjxXH.js";import Q from"./RightDrawerMap-D5PhmGFO.js";import X from"./VerticalBar-D5SkKIju.js";import"./TileLayer-B5vQ99gG.js";import{u as Y}from"./useUserLocation-YYelk0G_.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{G as Z}from"./setting-D9qCoDRn.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";/* empty css                         */import"./locas-Cxm3ID_S.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./UserLocatePop-CScyuy8U.js";const tt={class:"arc-bar refresh-bar"},ot={class:"danger"},et={class:"arc-bar status-bar"},rt={class:"arc-bar rolelist-bar"},it={class:"tree-box overlay-y"},st=N({__name:"RealTimePosition",setup(pt){const u=Y(o=>w(o)),c=x(),_=x(),n={},s=g({pops:[],loading:!1,menus:[],status:[{label:"全部",value:"",type:"primary"},{label:"在线",value:"2",type:"success"},{label:"离线",value:"1",type:"warning"},{label:"未使用",value:"0",type:"info"}],curStatus:"",curMenu:""}),e=g({autoRefresh:!1,refreshLasts:20,isRefreshing:!1}),C=g({type:"tree",field:"person",props:{label:"userName"},nodeKey:"userId",options:[],nodeClick:o=>{const t=u.getGraphic(o.userId);V(t)}}),I=g({group:[{fields:[{type:"select-tree",field:"pid",checkStrictly:!0,defaultExpandAll:!0,options:[],onChange:()=>a(),extraFormItem:[{type:"input",field:"name",prefixIcon:h($),appendBtns:[{perm:!0,svgIcon:h(M),click:()=>a()}]}]}]}],labelPosition:"top",defaultValue:{}}),a=async()=>{var o;if(!e.isRefreshing){e.isRefreshing=!0;try{const t=((o=c.value)==null?void 0:o.dataForm)||{},p=await u._getLatestUserCoords({departmentId:t.pid,userName:t.name,status:s.curStatus,userTypeId:s.curMenu==="qb"?"":s.curMenu});C.options=p.data,s.pops=u.generatePops(n.view,p.data,!0,m=>{const v=u.getGraphic(m);B(n.view,v,{zoom:15,avoidHighlight:!0})})}catch{}e.refreshLasts=20,e.isRefreshing=!1}},V=o=>{B(n.view,o,{zoom:15,avoidHighlight:!0}),u.highlight.highlight(n.view,o,w)},R=o=>{o&&a(),e.timer&&clearInterval(e.timer),e.autoRefresh?e.timer=setInterval(()=>{e.refreshLasts===0?a():e.refreshLasts-=1},1e3):e.refreshLasts=20},P=()=>{K(2).then(o=>{var p;const t=I.group[0].fields[0];t.options=O(o.data.data||[]),c.value&&(c.value.dataForm.pid=(p=t.options)==null?void 0:p[0].value),a()})},S=()=>{Z({page:1,size:999}).then(o=>{var t,p;s.menus=(p=(t=o.data)==null?void 0:t.data)==null?void 0:p.data.map(m=>({path:m.type,meta:{title:m.name,svgIcon:h(T)}})),s.menus.unshift({path:"qb",meta:{title:"全部",svgIcon:h(T)}})})},w=o=>{var t;o!=null&&o.attributes.userId&&((t=_.value)==null||t.openPop(o==null?void 0:o.attributes.userId))},z=async o=>{n.view=o,S(),P(),j(n.view,async()=>{var t;(t=_.value)==null||t.closeAllPop()})};return(o,t)=>{const p=A,m=D,v=H,G=J;return b(),L(Q,{ref_key:"refMap",ref:_,title:"实时位置","hide-coords":!0,"hide-search":!0,"hide-layer-list":!0,pops:i(s).pops,onMapLoaded:z,onPopToggle:t[4]||(t[4]=(r,y)=>r.visible=y)},{"map-bars":d(()=>[l("div",tt,[f(p,{modelValue:i(e).autoRefresh,"onUpdate:modelValue":t[0]||(t[0]=r=>i(e).autoRefresh=r),onChange:t[1]||(t[1]=()=>R())},{default:d(()=>t[5]||(t[5]=[k(" 自动刷新 ")])),_:1},8,["modelValue"]),l("span",ot,F(i(e).refreshLasts),1),t[7]||(t[7]=l("span",{class:"text"},"s后刷新",-1)),f(m,{size:"small",icon:i(M),type:"primary",loading:i(e).isRefreshing,onClick:t[2]||(t[2]=r=>R(!0))},{default:d(()=>t[6]||(t[6]=[k(" 刷新 ")])),_:1},8,["icon","loading"])]),l("div",et,[(b(!0),q(U,null,E(i(s).status,(r,y)=>(b(),L(m,{key:y,type:r.type,onClick:()=>{i(s).curStatus=r.value,a()}},{default:d(()=>[k(F(r.label),1)]),_:2},1032,["type","onClick"]))),128))]),l("div",rt,[f(X,{menus:i(s).menus,onChange:t[3]||(t[3]=r=>{i(s).curMenu=r==null?void 0:r.path,a()})},null,8,["menus"])])]),default:d(()=>[f(v,{ref_key:"refForm",ref:c,config:i(I)},null,8,["config"]),l("div",it,[f(G,{config:i(C)},null,8,["config"])])]),_:1},8,["pops"])}}}),ve=W(st,[["__scopeId","data-v-7c02589c"]]);export{ve as default};
