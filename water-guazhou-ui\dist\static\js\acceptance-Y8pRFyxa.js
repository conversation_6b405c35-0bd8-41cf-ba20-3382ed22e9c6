import{d as E,M,c as u,a8 as S,s as v,r as f,x as s,a9 as y,o as Y,g as N,n as j,q as r,i as p,F as R,b6 as z,b7 as A}from"./index-r0dFAfgr.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as B}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{I as h}from"./common-CvK_P_ao.js";import{o as q,q as I,r as V,g as w,s as U}from"./manage-BReaEVJk.js";import{g as W}from"./projectManagement-CDcrrCQ1.js";import{S as _}from"./data-Dv9-Tstw.js";import $ from"./detail-DEo1RlcF.js";import{f as b}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const G={class:"wrapper"},Ye=E({__name:"acceptance",setup(H){const{$btnPerms:c}=M(),g=u(),d=u(),x=u(),T=u({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:S(()=>i.projectType)},{label:"启动时间",field:"time",type:"daterange",format:"x"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:h.DOWNLOAD,click:()=>{q().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","验收管理.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:v(A),click:()=>{var e;(e=g.value)==null||e.resetForm(),l()}},{perm:!0,text:"查询",icon:h.QUERY,click:()=>l()}]}]}),n=f({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"所属项目",prop:"projectName"},{label:"开始时间",prop:"beginTime",formatter:e=>b(e.beginTime,"YYYY-MM-DD")},{label:"完成时间",prop:"endTime",formatter:e=>b(e.endTime,"YYYY-MM-DD")},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>b(e.createTime,"YYYY-MM-DD HH:mm:ss")},{label:"工作状态",prop:"status",tag:!0,tagColor:e=>{var t;return((t=_.find(a=>a.value===e.status))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=_.find(a=>a.value===e.status))==null?void 0:t.label}}],operationWidth:"360px",operations:[{disabled:e=>!e.status,isTextBtn:!1,text:"详情",perm:c("RoleManageEdit"),click:e=>{var t;i.selected=e,(t=x.value)==null||t.openDrawer()}},{disabled:e=>e.status!==null,isTextBtn:!1,type:"primary",text:"发起工程验收",perm:c("RoleManageEdit"),click:e=>{C(e)}},{disabled:e=>e.status==="COMPLETED"||e.status===null,isTextBtn:!1,type:"success",text:"编辑工程验收",perm:c("RoleManageEdit"),click:e=>L(e)},{disabled:e=>e.status!=="PROCESSING",isTextBtn:!1,text:"完成",perm:c("RoleManageEdit"),click:e=>{I(e.constructionCode).then(t=>{t.data.code===200?s.success("已完成"):s.warning("完成失败"),l()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,l()}}}),o=f({title:"添加工程预算信息",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{let t="新增";if(e.id&&(t="修改"),e.beginTime>e.endTime){s.warning("时间范围异常");return}o.submitting=!0,e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),V(e).then(a=>{var m;o.submitting=!1,a.data.code===200?(s.success(t+"成功"),(m=d.value)==null||m.closeDialog(),l()):s.warning(t+"失败")}).catch(a=>{o.submitting=!1,s.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"date",label:"开始时间",field:"beginTime",format:"x"},{xs:12,type:"date",label:"完成时间",field:"endTime",format:"x"},{xs:12,type:"input",label:"申请单位",field:"applicantOrganization",rules:[{required:!0,message:"请输入申请单位"}]},{xs:12,type:"input",label:"申请人",field:"applicant",rules:[{required:!0,message:"请输入申请人"}]},{xs:12,type:"input",label:"申请电话",field:"applicantPhone"},{xs:12,type:"input",label:"施工单位",field:"constructOrganization"},{xs:12,type:"input",label:"监理单位",field:"supervisorOrganization"},{xs:12,type:"input",label:"审计单位",field:"auditOrganization"},{xs:12,type:"input",label:"设计单位",field:"designOrganization"},{type:"textarea",label:"验收说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),D=f({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),C=e=>{var t;o.title="发起工程验收",i.DesignTubeLength=[],o.defaultValue={...e||{}},(t=d.value)==null||t.openDialog()},L=e=>{var t;o.title="编辑工程验收",i.DesignTubeLength=[],o.defaultValue={...e||{}},i.DesignTubeLength=(e==null?void 0:e.pipLengthDesign)&&JSON.parse(e==null?void 0:e.pipLengthDesign)||[],(t=d.value)==null||t.openDialog()},i=f({projectList:[],projectType:[],selected:{},DesignTubeLength:[],getOptions:()=>{W({page:1,size:-1}).then(e=>{i.projectList=y(e.data.data.data||[],"children",{label:"name",value:"code"})}),w({page:1,size:-1}).then(e=>{i.projectType=y(e.data.data.data||[],"children")})}}),l=async()=>{var t;const e={size:n.pagination.limit||20,page:n.pagination.page||1,...((t=g.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.projectStartTimeFrom=e.time[0],e.projectStartTimeTo=e.time[1],delete e.time),U(e).then(a=>{n.dataList=a.data.data.data||[],n.pagination.total=a.data.data.total||0})};return Y(()=>{l(),i.getOptions()}),(e,t)=>{const a=F,m=B,k=P,O=z;return N(),j("div",G,[r(a,{ref_key:"refSearch",ref:g,config:p(T)},null,8,["config"]),r(m,{config:p(n),class:"card-table"},null,8,["config"]),r(k,{ref_key:"refForm",ref:d,config:p(o)},null,8,["config"]),r(O,{ref_key:"refDetail",ref:x,config:p(D)},{default:R(()=>[r($,{config:p(i).selected,show:11},null,8,["config"])]),_:1},8,["config"])])}}});export{Ye as default};
