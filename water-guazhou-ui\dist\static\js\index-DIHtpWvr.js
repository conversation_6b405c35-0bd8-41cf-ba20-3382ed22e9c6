import{_ as d}from"./CardTable-rdWOL4_6.js";import{_ as u}from"./CardSearch-CB_HNR-Q.js";import{d as f,c as _,r as i,s,bF as h,o as b,g,n as q,q as l,i as p,al as y,b7 as W}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const x={class:"wrapper"},Y=f({__name:"index",setup(k){const t=_(),c=i({filters:[{type:"input",label:"操作人",field:"name"},{type:"daterange",label:"时间查询",field:"date"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:s(y),click:()=>o()},{perm:!0,text:"重置",type:"default",svgIcon:s(W),click:()=>{var e;(e=t.value)==null||e.resetForm()}}]}]}),n=i({indexVisible:!0,columns:[{label:"操作模块",minWidth:120,prop:"name1"},{label:"业务类型",minWidth:120,prop:"name"},{label:"操作人员",minWidth:120,prop:"method"},{label:"请求参数",minWidth:120,prop:"require"},{label:"返回参数",minWidth:120,prop:"require"},{label:"错误信息",minWidth:120,prop:"require"},{label:"操作地址",minWidth:120,prop:"require"},{label:"操作地址",minWidth:120,prop:"require"},{label:"操作状态",minWidth:120,prop:"require"},{label:"操作时间",minWidth:120,prop:"require",formatter:(e,r)=>r?h(r).format("YYYY-MM-DD HH:mm:ss"):""}],dataList:[],pagination:{refreshData:({page:e,size:r})=>{n.pagination.page=e,n.pagination.limit=r,o()}}}),o=async()=>{var a;const r={...((a=t.value)==null?void 0:a.queryParams)||{}};console.log(r)};return b(async()=>{await o()}),(e,r)=>{const a=u,m=d;return g(),q("div",x,[l(a,{ref_key:"refSearch",ref:t,config:p(c)},null,8,["config"]),l(m,{config:p(n),class:"card-table"},null,8,["config"])])}}});export{Y as default};
