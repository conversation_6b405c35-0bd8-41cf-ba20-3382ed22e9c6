package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionCompositeProject;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.construction.SoConstructionService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/so/construction")
public class SoConstructionController extends BaseController {
    @Autowired
    private SoConstructionService service;


    @GetMapping("/codeGen")
    public String code() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.generateCode(tenantId);
    }

    @GetMapping("/completionInfo/{constructionCode}")
    public List<ConstructionWorkflow> completionInfo(@PathVariable String constructionCode) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.completionInfo(constructionCode, tenantId);
    }

    @GetMapping
    public IPage<SoConstruction> findAllConditional(SoConstructionPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/{constructionCode}")
    public SoConstructionCompositeProject findByConstructionCode(@PathVariable String constructionCode) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.getCompositeByConstructionCode(constructionCode, tenantId);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionPageRequest request) {
        return ExcelFileInfo.of("工程受理列表", findAllConditional(request).getRecords())
                .nextTitle("code", "工程编号")
                .nextTitle("name", "工程名称")
                .nextTitle("projectCode", "所属项目")
                .nextTitle("address", "工程地址")
                .nextTitle("typeName", "工程类别")
                .nextTitle("estimate", "工程预算(万元)")
                .nextTitle("fitstpartName", "甲方代表")
                .nextTitle("creatorName", "创建人")
                .nextTitle("createTimeName", "创建时间");
    }

    @PostMapping
    public SoConstruction save(@RequestBody SoConstructionSaveRequest req) {
        if (service.isCodeExists(req.getCode(), req.tenantId(), req.getId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionSaveRequest req, @PathVariable String id) {
        if (StringUtils.isNotEmpty(req.getCode()) && service.isCodeExists(req.getCode(), req.tenantId(), req.getId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (!service.canBeDelete(id)) {
            ExceptionUtils.silentThrow("工程已有关联项，无法删除。");
        }
        return service.delete(id);
    }

    //region 设备项管理
    @GetMapping("/{code}/device")
    public IPage<SoDeviceItem> getDevices(@PathVariable String code, SoDeviceItemPageRequest request) {
        request.setScope(SoGeneralSystemScope.SO_CONSTRUCTION);
        request.setIdentifier(code);
        request.withoutCode();
        return service.getDevices(request);
    }

    @PostMapping("/{code}/device")
    public List<SoDeviceItem> saveDevice(@PathVariable String code, @RequestBody List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest item : request) {
            item.setIdentifier(code);
        }
        return service.saveDevice(request);
    }
    //endregion

}