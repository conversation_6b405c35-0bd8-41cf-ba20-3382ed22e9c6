package org.thingsboard.server.dao.smartService.call;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.MsgSend;
import org.thingsboard.server.dao.sql.smartService.call.MsgSendMapper;
import org.thingsboard.server.dao.util.SmsUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class MsgSendServiceImpl implements MsgSendService {
    @Autowired
    private MsgSendMapper msgSendMapper;

    @Autowired
    private SmsUtil smsUtil;

    @Override
    public PageData getList(String receivePhone, String keywords, String status, Long start, Long end, int page, int size, String tenantId) {

        List<MsgSend> blacklists = msgSendMapper.getList(receivePhone, keywords, status, start, end, page, size, tenantId);

        int total = msgSendMapper.getListCount(receivePhone, keywords, status, start, end, tenantId);

        return new PageData(total, blacklists);

    }

    @Override
    public MsgSend save(MsgSend msgSend) {
        msgSend.setCreateTime(new Date());
        msgSend.setUpdateTime(new Date());
        if (StringUtils.isBlank(msgSend.getId())) {
            msgSend.setCreateTime(new Date());
            msgSendMapper.insert(msgSend);
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = smsUtil.sendSms(msgSend.getReceivePhone(), msgSend.getContent());
        } catch (Exception e) {
            // 发送失败
            msgSend.setRemark(e.getMessage());
            msgSend.setStatus("2");
            e.printStackTrace();
            msgSendMapper.updateById(msgSend);
            return msgSend;
        }

        if (jsonObject.toJSONString().contains("OK")) {
            msgSend.setStatus("1");
        } else {
            msgSend.setStatus("2");
        }
        msgSend.setRemark(jsonObject.getJSONObject("body").toJSONString());
        msgSendMapper.updateById(msgSend);
        return msgSend;

    }

    @Override
    public int delete(List<String> ids) {
        return msgSendMapper.deleteBatchIds(ids);
    }

    @Override
    public IstarResponse resend(String id) {
        MsgSend msgSend = msgSendMapper.selectById(id);
        if (msgSend == null || !"2".equals(msgSend.getStatus())) {
            return IstarResponse.error("该短信不允许重新发送");
        }

        this.save(msgSend);
        return IstarResponse.ok("重发成功");
    }
}
