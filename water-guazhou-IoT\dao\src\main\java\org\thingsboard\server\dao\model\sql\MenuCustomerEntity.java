/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.menu.MenuCustomer;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MENU_CUSTOMER_NAME)
public class MenuCustomerEntity extends BaseSqlEntity<MenuCustomer> {

    @Column(name = ModelConstants.MENU_CUSTOMER_PARENT_ID)
    private String parentId;

    @Column(name = ModelConstants.MENU_CUSTOMER_NAME_PROPERTY)
    private String name;

    @Column(name = ModelConstants.MENU_CUSTOMER_PERMS)
    private String perms;

    @Column(name = ModelConstants.MENU_CUSTOMER_ICON)
    private String icon;

    @Column(name = ModelConstants.MENU_CUSTOMER_ORDER_NUM)
    private Integer orderNum;

    @Column(name = ModelConstants.MENU_CUSTOMER_DEFAULT_NAME)
    private String defaultName;

    @Column(name = ModelConstants.MENU_CUSTOMER_URL)
    private String url;

    @Column(name = ModelConstants.MENU_CUSTOMER_STATUS)
    private Integer status;

    @Column(name = ModelConstants.MENU_CUSTOMER_FLAG_DELETE)
    private Integer flagDelete;

    @Column(name = ModelConstants.MENU_CUSTOMER_MENT_TENANT_ID)
    private String menuTenantId;

    @Column(name = ModelConstants.MENU_CUSTOMER_ADDITIONAL_INFO)
    private String additionalInfo;


    public MenuCustomerEntity() {
        super();
    }

    public MenuCustomerEntity(MenuCustomer menuCustomer) {
        if (menuCustomer.getId() != null) {
            this.setId(menuCustomer.getId().getId());
        }
        if (menuCustomer.getParentId() != null) {
            this.parentId = toString(menuCustomer.getParentId().getId());
        }
        if (menuCustomer.getMenuTenantId() != null) {
            this.menuTenantId = toString(menuCustomer.getMenuTenantId().getId());
        }
        this.name = menuCustomer.getName();
        this.perms = menuCustomer.getPerms();
        this.icon = menuCustomer.getIcon();
        this.orderNum = menuCustomer.getOrderNum();
        this.defaultName = menuCustomer.getDefaultName();
        this.url = menuCustomer.getUrl();
        this.status = menuCustomer.getStatus();
        this.flagDelete = menuCustomer.getFlagDelete();
        this.additionalInfo = menuCustomer.getAdditionalInfo();
    }



    @Override
    public MenuCustomer toData() {
        MenuCustomer menuCustomer = new MenuCustomer(new MenuCustomerId(getId()));
        menuCustomer.setParentId(new MenuCustomerId(toUUID(parentId)));
        menuCustomer.setName(name);
        menuCustomer.setPerms(perms);
        menuCustomer.setIcon(icon);
        menuCustomer.setOrderNum(orderNum);
        menuCustomer.setDefaultName(defaultName);
        menuCustomer.setUrl(url);
        menuCustomer.setStatus(status);
        menuCustomer.setFlagDelete(flagDelete);
        menuCustomer.setMenuTenantId(new MenuTenantId(toUUID(menuTenantId)));
        menuCustomer.setAdditionalInfo(additionalInfo);

        return menuCustomer;
    }
}
