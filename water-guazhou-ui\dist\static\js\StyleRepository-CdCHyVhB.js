import{a as T,f as $,l as X}from"./StyleDefinition-Bnnz5uyC.js";import{L as W}from"./enums-BRzLM11V.js";import{T as Qt}from"./index-r0dFAfgr.js";import{C as g}from"./enums-BDQrMlcz.js";import{t as A}from"./VertexElementDescriptor-BOD-G50G.js";import{l as b,hg as Jt,hh as te,hi as ee,hj as ge}from"./MapView-DaoQedLH.js";import{h as P,t as vt}from"./GeometryUtils-B7ExOJII.js";import{X as _e}from"./definitions-826PWLuy.js";let de=class{constructor(t){this._array=[],t<=0&&console.error("strideInBytes must be positive!"),this._stride=t}get array(){return this._array}get index(){return 4*this._array.length/this._stride}get itemSize(){return this._stride}get sizeInBytes(){return 4*this._array.length}reset(){this.array.length=0}toBuffer(){return new Uint32Array(this._array).buffer}static i1616to32(t,e){return 65535&t|e<<16}static i8888to32(t,e,r,s){return 255&t|(255&e)<<8|(255&r)<<16|s<<24}static i8816to32(t,e,r){return 255&t|(255&e)<<8|r<<16}};var c,K;(function(a){a[a.R8_SIGNED=0]="R8_SIGNED",a[a.R8_UNSIGNED=1]="R8_UNSIGNED",a[a.R16_SIGNED=2]="R16_SIGNED",a[a.R16_UNSIGNED=3]="R16_UNSIGNED",a[a.R8G8_SIGNED=4]="R8G8_SIGNED",a[a.R8G8_UNSIGNED=5]="R8G8_UNSIGNED",a[a.R16G16_SIGNED=6]="R16G16_SIGNED",a[a.R16G16_UNSIGNED=7]="R16G16_UNSIGNED",a[a.R8G8B8A8_SIGNED=8]="R8G8B8A8_SIGNED",a[a.R8G8B8A8_UNSIGNED=9]="R8G8B8A8_UNSIGNED",a[a.R8G8B8A8_COLOR=10]="R8G8B8A8_COLOR",a[a.R16G16B16A16_DASHARRAY=11]="R16G16B16A16_DASHARRAY",a[a.R16G16B16A16_PATTERN=12]="R16G16B16A16_PATTERN"})(c||(c={})),function(a){a[a.UNIFORM=0]="UNIFORM",a[a.DATA_DRIVEN=1]="DATA_DRIVEN",a[a.INTERPOLATED_DATA_DRIVEN=2]="INTERPOLATED_DATA_DRIVEN",a[a.UNUSED=3]="UNUSED"}(K||(K={}));let H=class Dt{constructor(t){this._locations=new Map,this._key=t}get key(){return this._key}get type(){return 7&this._key}defines(){return[]}getStride(){return this._layoutInfo||this._buildAttributesInfo(),this._stride}getAttributeLocations(){return this._locations.size===0&&this._buildAttributesInfo(),this._locations}getLayoutInfo(){return this._layoutInfo||this._buildAttributesInfo(),this._layoutInfo}getEncodingInfos(){return this._propertyEncodingInfo||this._buildAttributesInfo(),this._propertyEncodingInfo}getUniforms(){return this._uniforms||this._buildAttributesInfo(),this._uniforms}getShaderHeader(){return this._shaderHeader||this._buildAttributesInfo(),this._shaderHeader}getShaderMain(){return this._shaderMain||this._buildAttributesInfo(),this._shaderMain}setDataUniforms(t,e,r,s,i){const o=this.getUniforms();for(const n of o){const{name:l,type:u,getValue:p}=n,h=p(r,e,s,i);if(h!==null)switch(u){case"float":t.setUniform1f(l,h);break;case"vec2":t.setUniform2fv(l,h);break;case"vec4":t.setUniform4fv(l,h)}}}encodeAttributes(t,e,r,s){var p;const i=this.attributesInfo(),o=this.getEncodingInfos(),n=[];let l=0,u=0;for(const h of Object.keys(o)){const f=o[h],{type:_,precisionFactor:d,isLayout:O}=i[h],R=O?r.getLayoutProperty(h):r.getPaintProperty(h),M=(p=R.interpolator)==null?void 0:p.getInterpolationRange(e);let I=0;for(const x of f){const{offset:E,bufferElementsToAdd:G}=x;if(G>0){for(let D=0;D<G;D++)n.push(0);l+=u,u=x.bufferElementsToAdd}const v=s??R.getValue(M?M[I]:e,t);switch(_){case c.R8_SIGNED:case c.R8_UNSIGNED:n[l]|=this._encodeByte(v*(d||1),8*E);break;case c.R16_SIGNED:case c.R16_UNSIGNED:n[l]|=this._encodeShort(v*(d||1),8*E);break;case c.R8G8_SIGNED:case c.R8G8_UNSIGNED:n[l]|=this._encodeByte(v*(d||1),8*E),n[l]|=this._encodeByte(v*(d||1),8*E+8);break;case c.R16G16_SIGNED:case c.R16G16_UNSIGNED:n[l]|=this._encodeShort(v*(d||1),8*E),n[l]|=this._encodeShort(v*(d||1),8*E+16);break;case c.R8G8B8A8_SIGNED:case c.R8G8B8A8_UNSIGNED:n[l]|=this._encodeByte(v*(d||1),8*E),n[l]|=this._encodeByte(v*(d||1),8*E+8),n[l]|=this._encodeByte(v*(d||1),8*E+16),n[l]|=this._encodeByte(v*(d||1),8*E+24);break;case c.R8G8B8A8_COLOR:n[l]=this._encodeColor(v);break;case c.R16G16B16A16_DASHARRAY:case c.R16G16B16A16_PATTERN:this._encodePattern(l,n,v);break;default:throw new Error("Unsupported encoding type")}I++}}return n}getAtributeState(t){let e=0;const r=3+2*t;return e|=this._bit(r),e|=this._bit(r+1)<<1,e}_buildAttributesInfo(){const t=[],e={},r={};let s=-1;const i=this.attributesInfo(),o=this.attributes();let n=-1;for(const h of o){n++;const f=this.getAtributeState(n);if(f===K.UNIFORM||f===K.UNUSED)continue;const _=i[h],d=[];e[h]=d;const O=_.type;for(let R=0;R<f;R++){const{dataType:M,bytesPerElement:I,count:x,normalized:E}=Dt._encodingInfo[O],G=I*x,v=`${M}-${E===!0}`;let D=r[v],k=0;if(!D||D.count+x>4)s++,D={dataIndex:s,count:0,offset:0},x!==4&&(r[v]=D),t.push({location:-1,name:"a_data_"+s,count:x,type:M,normalized:E}),k=Math.ceil(Math.max(G/4,1));else{const z=t[D.dataIndex];z.count+=x,k=Math.ceil(Math.max(z.count*I/4,1))-Math.ceil(Math.max(D.offset/4,1))}d.push({dataIndex:D.dataIndex,offset:D.offset,bufferElementsToAdd:k}),D.offset+=G,D.count+=x}}for(const h of t)switch(h.type){case g.BYTE:case g.UNSIGNED_BYTE:h.count=4;break;case g.SHORT:case g.UNSIGNED_SHORT:h.count+=h.count%2}this._buildVertexBufferLayout(t);let l=0;const u=this._layoutInfo.geometry;for(const h of u)this._locations.set(h.name,l++);const p=this._layoutInfo.opacity;if(p)for(const h of p)this._locations.set(h.name,l++);this._buildShaderInfo(t,e),this._propertyEncodingInfo=e}_buildVertexBufferLayout(t){const e={},r=this.geometryInfo();let s=r[0].stride;if(t.length===0)e.geometry=r;else{const i=[];let o=s;for(const n of t)s+=re(n.type)*n.count;for(const n of r)i.push(new A(n.name,n.count,n.type,n.offset,s,n.normalized));for(const n of t)i.push(new A(n.name,n.count,n.type,o,s,n.normalized)),o+=re(n.type)*n.count;e.geometry=i}this.opacityInfo()&&(e.opacity=this.opacityInfo()),this._layoutInfo=e,this._stride=s}_buildShaderInfo(t,e){let r=`
`,s=`
`;const i=[];for(const u of t)r+=`attribute ${this._getType(u.count)} ${u.name};
`;const o=this.attributes(),n=this.attributesInfo();let l=-1;for(const u of o){l++;const{name:p,type:h,precisionFactor:f,isLayout:_}=n[u],d=f&&f!==1?" * "+1/f:"",{bytesPerElement:O,count:R}=Dt._encodingInfo[h],M=I=>`a_data_${I.dataIndex}${me(R,I.offset,O)}`;switch(this.getAtributeState(l)){case K.UNIFORM:{const I=this._getType(R),x=`u_${p}`;i.push({name:x,type:I,getValue:(E,G,v,D)=>{const k=_?E.getLayoutValue(u,G):E.getPaintValue(u,G);if(h===c.R16G16B16A16_DASHARRAY){const z=E.getDashKey(k,E.getLayoutValue("line-cap",G)),it=D.getMosaicItemPosition(z,!1);if(Qt(it))return null;const{tl:at,br:Xt}=it;return[at[0],Xt[1],Xt[0],at[1]]}if(h===c.R16G16B16A16_PATTERN){const z=D.getMosaicItemPosition(k,!u.includes("line-"));if(Qt(z))return null;const{tl:it,br:at}=z;return[it[0],at[1],at[0],it[1]]}if(h===c.R8G8B8A8_COLOR){const z=k[3];return[z*k[0],z*k[1],z*k[2],z]}return k}}),r+=`uniform ${I} ${x};
`,s+=`${I} ${p} = ${x};
`}break;case K.DATA_DRIVEN:{const I=M(e[u][0]);s+=`${this._getType(R)} ${p} = ${I}${d};
`}break;case K.INTERPOLATED_DATA_DRIVEN:{const I=`u_t_${p}`;i.push({name:I,type:"float",getValue:(G,v,D,k)=>(_?G.getLayoutProperty(u):G.getPaintProperty(u)).interpolator.interpolationUniformValue(D,v)}),r+=`uniform float ${I};
`;const x=M(e[u][0]),E=M(e[u][1]);s+=`${this._getType(R)} ${p} = mix(${x}${d}, ${E}${d}, ${I});
`}}}this._shaderHeader=r,this._shaderMain=s,this._uniforms=i}_bit(t){return(this._key&1<<t)>>t}_getType(t){switch(t){case 1:return"float";case 2:return"vec2";case 3:return"vec3";case 4:return"vec4"}throw new Error("Invalid count")}_encodeColor(t){const e=255*t[3];return de.i8888to32(t[0]*e,t[1]*e,t[2]*e,e)}_encodePattern(t,e,r){if(!r||!r.rect)return;const s=2,i=r.rect,o=r.width,n=r.height;e[t]=this._encodeShort(i.x+s,0),e[t]|=this._encodeShort(i.y+s+n,16),e[t+1]=this._encodeShort(i.x+s+o,0),e[t+1]|=this._encodeShort(i.y+s,16)}_encodeByte(t,e){return(255&t)<<e}_encodeShort(t,e){return(65535&t)<<e}};H._encodingInfo={[c.R8_SIGNED]:{dataType:g.BYTE,bytesPerElement:1,count:1,normalized:!1},[c.R8_UNSIGNED]:{dataType:g.UNSIGNED_BYTE,bytesPerElement:1,count:1,normalized:!1},[c.R16_SIGNED]:{dataType:g.SHORT,bytesPerElement:2,count:1,normalized:!1},[c.R16_UNSIGNED]:{dataType:g.UNSIGNED_SHORT,bytesPerElement:2,count:1,normalized:!1},[c.R8G8_SIGNED]:{dataType:g.BYTE,bytesPerElement:1,count:2,normalized:!1},[c.R8G8_UNSIGNED]:{dataType:g.UNSIGNED_BYTE,bytesPerElement:1,count:2,normalized:!1},[c.R16G16_SIGNED]:{dataType:g.SHORT,bytesPerElement:2,count:2,normalized:!1},[c.R16G16_UNSIGNED]:{dataType:g.UNSIGNED_SHORT,bytesPerElement:2,count:2,normalized:!1},[c.R8G8B8A8_SIGNED]:{dataType:g.BYTE,bytesPerElement:1,count:4,normalized:!1},[c.R8G8B8A8_UNSIGNED]:{dataType:g.UNSIGNED_BYTE,bytesPerElement:1,count:4,normalized:!1},[c.R8G8B8A8_COLOR]:{dataType:g.UNSIGNED_BYTE,bytesPerElement:1,count:4,normalized:!0},[c.R16G16B16A16_DASHARRAY]:{dataType:g.UNSIGNED_SHORT,bytesPerElement:2,count:4,normalized:!1},[c.R16G16B16A16_PATTERN]:{dataType:g.UNSIGNED_SHORT,bytesPerElement:2,count:4,normalized:!1}};const re=a=>{switch(a){case g.FLOAT:case g.INT:case g.UNSIGNED_INT:return 4;case g.SHORT:case g.UNSIGNED_SHORT:return 2;case g.BYTE:case g.UNSIGNED_BYTE:return 1}},me=(a,t,e)=>{const r=t/e;if(a===1)switch(r){case 0:return".x";case 1:return".y";case 2:return".z";case 3:return".w"}else if(a===2)switch(r){case 0:return".xy";case 1:return".yz";case 2:return".zw"}else if(a===3)switch(r){case 0:return".xyz";case 1:return".yzw"}return""};let Q=class pt extends H{constructor(t){super(t)}geometryInfo(){return pt.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return pt.ATTRIBUTES}attributesInfo(){return pt.ATTRIBUTES_INFO}};Q.ATTRIBUTES=[],Q.GEOMETRY_LAYOUT=[new A("a_pos",2,g.BYTE,0,2)],Q.ATTRIBUTES_INFO={};let J=class yt extends H{constructor(t){super(t)}geometryInfo(){return yt.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return yt.ATTRIBUTES}attributesInfo(){return yt.ATTRIBUTES_INFO}};J.ATTRIBUTES=["circle-radius","circle-color","circle-opacity","circle-stroke-width","circle-stroke-color","circle-stroke-opacity","circle-blur"],J.GEOMETRY_LAYOUT=[new A("a_pos",2,g.SHORT,0,4)],J.ATTRIBUTES_INFO={"circle-radius":{name:"radius",type:c.R8_UNSIGNED},"circle-color":{name:"color",type:c.R8G8B8A8_COLOR},"circle-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100},"circle-stroke-width":{name:"stroke_width",type:c.R8_UNSIGNED,precisionFactor:4},"circle-stroke-color":{name:"stroke_color",type:c.R8G8B8A8_COLOR},"circle-stroke-opacity":{name:"stroke_opacity",type:c.R8_UNSIGNED,precisionFactor:100},"circle-blur":{name:"blur",type:c.R8_UNSIGNED,precisionFactor:32}};let tt=class ft extends H{constructor(t){super(t)}geometryInfo(){return ft.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return ft.ATTRIBUTES}attributesInfo(){return ft.ATTRIBUTES_INFO}};tt.ATTRIBUTES=["fill-color","fill-opacity","fill-pattern"],tt.GEOMETRY_LAYOUT=[new A("a_pos",2,g.SHORT,0,4)],tt.ATTRIBUTES_INFO={"fill-color":{name:"color",type:c.R8G8B8A8_COLOR},"fill-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100},"fill-pattern":{name:"tlbr",type:c.R16G16B16A16_PATTERN,isOptional:!0}};let V=class Z extends H{constructor(t,e){super(t),this._usefillColor=e}geometryInfo(){return Z.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return this._usefillColor?Z.ATTRIBUTES_FILL:Z.ATTRIBUTES_OUTLINE}attributesInfo(){return this._usefillColor?Z.ATTRIBUTES_INFO_FILL:Z.ATTRIBUTES_INFO_OUTLINE}};V.ATTRIBUTES_OUTLINE=["fill-outline-color","fill-opacity"],V.ATTRIBUTES_FILL=["fill-color","fill-opacity"],V.GEOMETRY_LAYOUT=[new A("a_pos",2,g.SHORT,0,8),new A("a_offset",2,g.BYTE,4,8),new A("a_xnormal",2,g.BYTE,6,8)],V.ATTRIBUTES_INFO_OUTLINE={"fill-outline-color":{name:"color",type:c.R8G8B8A8_COLOR},"fill-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100}},V.ATTRIBUTES_INFO_FILL={"fill-color":{name:"color",type:c.R8G8B8A8_COLOR},"fill-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100}};let et=class gt extends H{constructor(t){super(t)}geometryInfo(){return gt.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return gt.ATTRIBUTES}attributesInfo(){return gt.ATTRIBUTES_INFO}};et.ATTRIBUTES=["line-blur","line-color","line-gap-width","line-offset","line-opacity","line-width","line-pattern","line-dasharray"],et.GEOMETRY_LAYOUT=[new A("a_pos",2,g.SHORT,0,16),new A("a_extrude_offset",4,g.BYTE,4,16),new A("a_dir_normal",4,g.BYTE,8,16),new A("a_accumulatedDistance",2,g.UNSIGNED_SHORT,12,16)],et.ATTRIBUTES_INFO={"line-width":{name:"width",type:c.R8_UNSIGNED,precisionFactor:2},"line-gap-width":{name:"gap_width",type:c.R8_UNSIGNED,precisionFactor:2},"line-offset":{name:"offset",type:c.R8_SIGNED,precisionFactor:2},"line-color":{name:"color",type:c.R8G8B8A8_COLOR},"line-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100},"line-blur":{name:"blur",type:c.R8_UNSIGNED,precisionFactor:4},"line-pattern":{name:"tlbr",type:c.R16G16B16A16_PATTERN,isOptional:!0},"line-dasharray":{name:"tlbr",type:c.R16G16B16A16_DASHARRAY,isOptional:!0}};const le=[new A("a_pos",2,g.SHORT,0,16),new A("a_vertexOffset",2,g.SHORT,4,16),new A("a_texAngleRange",4,g.UNSIGNED_BYTE,8,16),new A("a_levelInfo",4,g.UNSIGNED_BYTE,12,16)],ue=[new A("a_opacityInfo",1,g.UNSIGNED_BYTE,0,1)];let ot=class Rt extends H{constructor(t){super(t)}geometryInfo(){return le}opacityInfo(){return ue}attributes(){return Rt.ATTRIBUTES}attributesInfo(){return Rt.ATTRIBUTES_INFO}};ot.ATTRIBUTES=["icon-color","icon-opacity","icon-halo-blur","icon-halo-color","icon-halo-width","icon-size"],ot.ATTRIBUTES_INFO={"icon-color":{name:"color",type:c.R8G8B8A8_COLOR},"icon-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100},"icon-halo-color":{name:"halo_color",type:c.R8G8B8A8_COLOR},"icon-halo-width":{name:"halo_width",type:c.R8_UNSIGNED,precisionFactor:4},"icon-halo-blur":{name:"halo_blur",type:c.R8_UNSIGNED,precisionFactor:4},"icon-size":{name:"size",type:c.R8_UNSIGNED,precisionFactor:32,isLayout:!0}};let lt=class At extends H{constructor(t){super(t)}geometryInfo(){return le}opacityInfo(){return ue}attributes(){return At.ATTRIBUTES}attributesInfo(){return At.ATTRIBUTES_INFO}};lt.ATTRIBUTES=["text-color","text-opacity","text-halo-blur","text-halo-color","text-halo-width","text-size"],lt.ATTRIBUTES_INFO={"text-color":{name:"color",type:c.R8G8B8A8_COLOR},"text-opacity":{name:"opacity",type:c.R8_UNSIGNED,precisionFactor:100},"text-halo-color":{name:"halo_color",type:c.R8G8B8A8_COLOR},"text-halo-width":{name:"halo_width",type:c.R8_UNSIGNED,precisionFactor:4},"text-halo-blur":{name:"halo_blur",type:c.R8_UNSIGNED,precisionFactor:4},"text-size":{name:"size",type:c.R8_UNSIGNED,isLayout:!0}};const ce={kind:"null"},w={kind:"number"},B={kind:"string"},N={kind:"boolean"},Y={kind:"color"},Tt={kind:"object"},C={kind:"value"};function ct(a,t){return{kind:"array",itemType:a,n:t}}const we=[ce,w,B,N,Y,Tt,ct(C)];function rt(a){if(a.kind==="array"){const t=rt(a.itemType);return typeof a.n=="number"?`array<${t}, ${a.n}>`:a.itemType.kind==="value"?"array":`array<${t}>`}return a.kind}function bt(a){if(a===null)return ce;if(typeof a=="string")return B;if(typeof a=="boolean")return N;if(typeof a=="number")return w;if(a instanceof b)return Y;if(Array.isArray(a)){let t;for(const e of a){const r=bt(e);if(t){if(t!==r){t=C;break}}else t=r}return ct(t||C,a.length)}return typeof a=="object"?Tt:C}function Nt(a,t){if(t.kind==="array")return a.kind==="array"&&(a.n===0&&a.itemType.kind==="value"||Nt(a.itemType,t.itemType))&&(typeof t.n!="number"||t.n===a.n);if(t.kind==="value"){for(const e of we)if(Nt(a,e))return!0}return t.kind===a.kind}function xt(a){if(a===null)return"";const t=typeof a;return t==="string"?a:t==="number"||t==="boolean"?String(a):a instanceof b?a.toString():JSON.stringify(a)}let Ee=class{constructor(t){this._parent=t,this._vars={}}add(t,e){this._vars[t]=e}get(t){return this._vars[t]?this._vars[t]:this._parent?this._parent.get(t):null}};class St{constructor(){this.type=C}static parse(t){if(t.length>1)throw new Error('"id" does not expect arguments');return new St}evaluate(t,e){return t==null?void 0:t.id}}class Lt{constructor(){this.type=B}static parse(t){if(t.length>1)throw new Error('"geometry-type" does not expect arguments');return new Lt}evaluate(t,e){if(!t)return null;switch(t.type){case vt.Point:return"Point";case vt.LineString:return"LineString";case vt.Polygon:return"Polygon";default:return null}}}let Te=class he{constructor(){this.type=Tt}static parse(t){if(t.length>1)throw new Error('"properties" does not expect arguments');return new he}evaluate(t,e){return t==null?void 0:t.values}};class It{constructor(){this.type=w}static parse(t){if(t.length>1)throw new Error('"zoom" does not expect arguments');return new It}evaluate(t,e){return e}}class U{constructor(t,e,r){this._lhs=t,this._rhs=e,this._compare=r,this.type=N}static parse(t,e,r){if(t.length!==3&&t.length!==4)throw new Error(`"${t[0]}" expects 2 or 3 arguments`);if(t.length===4)throw new Error(`"${t[0]}" collator not supported`);return new U(y(t[1],e),y(t[2],e),r)}evaluate(t,e){return this._compare(this._lhs.evaluate(t,e),this._rhs.evaluate(t,e))}}class be extends U{static parse(t,e){return U.parse(t,e,(r,s)=>r===s)}}class Ie extends U{static parse(t,e){return U.parse(t,e,(r,s)=>r!==s)}}class ve extends U{static parse(t,e){return U.parse(t,e,(r,s)=>r<s)}}class De extends U{static parse(t,e){return U.parse(t,e,(r,s)=>r<=s)}}class Re extends U{static parse(t,e){return U.parse(t,e,(r,s)=>r>s)}}class Ae extends U{static parse(t,e){return U.parse(t,e,(r,s)=>r>=s)}}class Ut{constructor(t){this._arg=t,this.type=N}static parse(t,e){if(t.length!==2)throw new Error('"!" expects 1 argument');return new Ut(y(t[1],e))}evaluate(t,e){return!this._arg.evaluate(t,e)}}class Bt{constructor(t){this._args=t,this.type=N}static parse(t,e){const r=[];for(let s=1;s<t.length;s++)r.push(y(t[s],e));return new Bt(r)}evaluate(t,e){for(const r of this._args)if(!r.evaluate(t,e))return!1;return!0}}class Ot{constructor(t){this._args=t,this.type=N}static parse(t,e){const r=[];for(let s=1;s<t.length;s++)r.push(y(t[s],e));return new Ot(r)}evaluate(t,e){for(const r of this._args)if(r.evaluate(t,e))return!0;return!1}}class Gt{constructor(t){this._args=t,this.type=N}static parse(t,e){const r=[];for(let s=1;s<t.length;s++)r.push(y(t[s],e));return new Gt(r)}evaluate(t,e){for(const r of this._args)if(r.evaluate(t,e))return!1;return!0}}class kt{constructor(t,e,r){this.type=t,this._args=e,this._fallback=r}static parse(t,e,r){if(t.length<4)throw new Error('"case" expects at least 3 arguments');if(t.length%2==1)throw new Error('"case" expects an odd number of arguments');let s;const i=[];for(let n=1;n<t.length-1;n+=2){const l=y(t[n],e),u=y(t[n+1],e,r);s||(s=u.type),i.push({condition:l,output:u})}const o=y(t[t.length-1],e,r);return s||(s=o.type),new kt(s,i,o)}evaluate(t,e){for(const r of this._args)if(r.condition.evaluate(t,e))return r.output.evaluate(t,e);return this._fallback.evaluate(t,e)}}let Ne=class pe{constructor(t,e){this.type=t,this._args=e}static parse(t,e){if(t.length<2)throw new Error('"coalesce" expects at least 1 argument');let r;const s=[];for(let i=1;i<t.length;i++){const o=y(t[i],e);r||(r=o.type),s.push(o)}return new pe(r,s)}evaluate(t,e){for(const r of this._args){const s=r.evaluate(t,e);if(s!==null)return s}return null}};class zt{constructor(t,e,r,s,i){this.type=t,this._input=e,this._labels=r,this._outputs=s,this._fallback=i}static parse(t,e){if(t.length<3)throw new Error('"match" expects at least 3 arguments');if(t.length%2==0)throw new Error('"case" expects an even number of arguments');let r;const s=y(t[1],e),i=[],o={};let n;for(let l=2;l<t.length-1;l+=2){let u=t[l];Array.isArray(u)||(u=[u]);for(const h of u){const f=typeof h;if(f!=="string"&&f!=="number")throw new Error('"match" requires string or number literal as labels');if(n){if(f!==n)throw new Error('"match" requires labels to have the same type')}else n=f;o[h]=i.length}const p=y(t[l+1],e);r||(r=p.type),i.push(p)}return new zt(r,s,o,i,y(t[t.length-1],e))}evaluate(t,e){const r=this._input.evaluate(t,e);return(this._outputs[this._labels[r]]||this._fallback).evaluate(t,e)}}let _t=class j{constructor(t,e,r,s,i){this._operator=t,this.type=e,this.interpolation=r,this.input=s,this._stops=i}static parse(t,e,r){const s=t[0];if(t.length<5)throw new Error(`"${s}" expects at least 4 arguments`);const i=t[1];if(!Array.isArray(i)||i.length===0)throw new Error(`"${i}" is not a valid interpolation`);switch(i[0]){case"linear":if(i.length!==1)throw new Error("Linear interpolation cannot have parameters");break;case"exponential":if(i.length!==2||typeof i[1]!="number")throw new Error("Exponential interpolation requires one numeric argument");break;case"cubic-bezier":if(i.length!==5)throw new Error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1");for(let u=1;u<5;u++){const p=i[u];if(typeof p!="number"||p<0||p>1)throw new Error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1")}break;default:throw new Error(`"${t[0]}" unknown interpolation type "${i[0]}"`)}if(t.length%2!=1)throw new Error(`"${s}" expects an even number of arguments`);const o=y(t[2],e,w);let n;s==="interpolate-hcl"||s==="interpolate-lab"?n=Y:r&&r.kind!=="value"&&(n=r);const l=[];for(let u=3;u<t.length;u+=2){const p=t[u];if(typeof p!="number")throw new Error(`"${s}" requires stop inputs as literal numbers`);if(l.length&&l[l.length-1][0]>=p)throw new Error(`"${s}" requires strictly ascending stop inputs`);const h=y(t[u+1],e,n);n||(n=h.type),l.push([p,h])}if(n&&n!==Y&&n!==w&&(n.kind!=="array"||n.itemType!==w))throw new Error(`"${s}" cannot interpolate type ${rt(n)}`);return new j(s,n,i,o,l)}evaluate(t,e){const r=this._stops;if(r.length===1)return r[0][1].evaluate(t,e);const s=this.input.evaluate(t,e);if(s<=r[0][0])return r[0][1].evaluate(t,e);if(s>=r[r.length-1][0])return r[r.length-1][1].evaluate(t,e);let i=0;for(;++i<r.length&&!(s<r[i][0]););const o=r[i-1][0],n=r[i][0],l=j.interpolationRatio(this.interpolation,s,o,n),u=r[i-1][1].evaluate(t,e),p=r[i][1].evaluate(t,e);if(this._operator==="interpolate"){if(this.type.kind==="array"&&Array.isArray(u)&&Array.isArray(p))return u.map((h,f)=>P(h,p[f],l));if(this.type.kind==="color"&&u instanceof b&&p instanceof b){const h=new b(u),f=new b(p);return new b([P(h.r,f.r,l),P(h.g,f.g,l),P(h.b,f.b,l),P(h.a,f.a,l)])}if(this.type.kind==="number"&&typeof u=="number"&&typeof p=="number")return P(u,p,l);throw new Error(`"${this._operator}" cannot interpolate type ${rt(this.type)}`)}if(this._operator==="interpolate-hcl"){const h=Jt(u),f=Jt(p),_=f.h-h.h,d=te({h:h.h+l*(_>180||_<-180?_-360*Math.round(_/360):_),c:P(h.c,f.c,l),l:P(h.l,f.l,l)});return new b({a:P(u.a,p.a,l),...d})}if(this._operator==="interpolate-lab"){const h=ee(u),f=ee(p),_=te({l:P(h.l,f.l,l),a:P(h.a,f.a,l),b:P(h.b,f.b,l)});return new b({a:P(u.a,p.a,l),..._})}throw new Error(`Unexpected operator "${this._operator}"`)}interpolationUniformValue(t,e){const r=this._stops;if(r.length===1||t>=r[r.length-1][0])return 0;let s=0;for(;++s<r.length&&!(t<r[s][0]););const i=r[s-1][0],o=r[s][0];return j.interpolationRatio(this.interpolation,e,i,o)}getInterpolationRange(t){const e=this._stops;if(e.length===1){const i=e[0][0];return[i,i]}const r=e[e.length-1][0];if(t>=r)return[r,r];let s=0;for(;++s<e.length&&!(t<e[s][0]););return[e[s-1][0],e[s][0]]}static interpolationRatio(t,e,r,s){let i=0;return t[0]==="linear"?i=j._exponentialInterpolationRatio(e,1,r,s):t[0]==="exponential"?i=j._exponentialInterpolationRatio(e,t[1],r,s):t[0]==="cubic-bezier"&&(i=ge(t[1],t[2],t[3],t[4])(j._exponentialInterpolationRatio(e,1,r,s),1e-5)),i<0?i=0:i>1&&(i=1),i}static _exponentialInterpolationRatio(t,e,r,s){const i=s-r;if(i===0)return 0;const o=t-r;return e===1?o/i:(e**o-1)/(e**i-1)}};class Ft{constructor(t,e,r){this.type=t,this._input=e,this._stops=r}static parse(t,e){if(t.length<5)throw new Error('"step" expects at least 4 arguments');if(t.length%2!=1)throw new Error('"step" expects an even number of arguments');const r=y(t[1],e,w);let s;const i=[];i.push([-1/0,y(t[2],e)]);for(let o=3;o<t.length;o+=2){const n=t[o];if(typeof n!="number")throw new Error('"step" requires stop inputs as literal numbers');if(i.length&&i[i.length-1][0]>=n)throw new Error('"step" requires strictly ascending stop inputs');const l=y(t[o+1],e);s||(s=l.type),i.push([n,l])}return new Ft(s,r,i)}evaluate(t,e){const r=this._stops;if(r.length===1)return r[0][1].evaluate(t,e);const s=this._input.evaluate(t,e);let i=0;for(;++i<r.length&&!(s<r[i][0]););return this._stops[i-1][1].evaluate(t,e)}}class $t{constructor(t,e){this.type=t,this._output=e}static parse(t,e,r){if(t.length<4)throw new Error('"let" expects at least 3 arguments');if(t.length%2==1)throw new Error('"let" expects an odd number of arguments');const s=new Ee(e);for(let o=1;o<t.length-1;o+=2){const n=t[o];if(typeof n!="string")throw new Error(`"let" requires a string to define variable names - found ${n}`);s.add(n,y(t[o+1],e))}const i=y(t[t.length-1],s,r);return new $t(i.type,i)}evaluate(t,e){return this._output.evaluate(t,e)}}let xe=class ye{constructor(t,e){this.type=t,this.output=e}static parse(t,e,r){if(t.length!==2||typeof t[1]!="string")throw new Error('"var" requires just one literal string argument');const s=e.get(t[1]);if(!s)throw new Error(`${t[1]} must be defined before being used in a "var" expression`);return new ye(r||C,s)}evaluate(t,e){return this.output.evaluate(t,e)}};class Vt{constructor(t,e,r){this.type=t,this._index=e,this._array=r}static parse(t,e){if(t.length!==3)throw new Error('"at" expects 2 arguments');const r=y(t[1],e,w),s=y(t[2],e);return new Vt(s.type.itemType,r,s)}evaluate(t,e){const r=this._index.evaluate(t,e),s=this._array.evaluate(t,e);if(r<0||r>=s.length)throw new Error('"at" index out of bounds');if(r!==Math.floor(r))throw new Error('"at" index must be an integer');return s[r]}}class dt{constructor(t,e){this._key=t,this._obj=e,this.type=C}static parse(t,e){let r,s;switch(t.length){case 2:return r=y(t[1],e),new dt(r);case 3:return r=y(t[1],e),s=y(t[2],e),new dt(r,s);default:throw new Error('"get" expects 1 or 2 arguments')}}evaluate(t,e){const r=this._key.evaluate(t,e);return this._obj?this._obj.evaluate(t,e)[r]:t==null?void 0:t.values[r]}}class mt{constructor(t,e){this._key=t,this._obj=e,this.type=N}static parse(t,e){let r,s;switch(t.length){case 2:return r=y(t[1],e),new mt(r);case 3:return r=y(t[1],e),s=y(t[2],e),new mt(r,s);default:throw new Error('"has" expects 1 or 2 arguments')}}evaluate(t,e){const r=this._key.evaluate(t,e);return this._obj?r in this._obj.evaluate(t,e):!!(t!=null&&t.values[r])}}class Mt{constructor(t,e){this._key=t,this._vals=e,this.type=N}static parse(t,e){if(t.length!==3)throw new Error('"in" expects 2 arguments');return new Mt(y(t[1],e),y(t[2],e))}evaluate(t,e){const r=this._key.evaluate(t,e);return this._vals.evaluate(t,e).includes(r)}}let Pe=class Pt{constructor(t,e,r){this._item=t,this._array=e,this._from=r,this.type=w}static parse(t,e){if(t.length<3||t.length>4)throw new Error('"index-of" expects 3 or 4 arguments');const r=y(t[1],e),s=y(t[2],e);if(t.length===4){const i=y(t[3],e,w);return new Pt(r,s,i)}return new Pt(r,s)}evaluate(t,e){const r=this._item.evaluate(t,e),s=this._array.evaluate(t,e);if(this._from){const i=this._from.evaluate(t,e);if(i!==Math.floor(i))throw new Error('"index-of" index must be an integer');return s.indexOf(r,i)}return s.indexOf(r)}};class Ct{constructor(t){this._arg=t,this.type=w}static parse(t,e){if(t.length!==2)throw new Error('"length" expects 2 arguments');const r=y(t[1],e);return new Ct(r)}evaluate(t,e){const r=this._arg.evaluate(t,e);if(typeof r=="string"||Array.isArray(r))return r.length;throw new Error('"length" expects string or array')}}class wt{constructor(t,e,r,s){this.type=t,this._array=e,this._from=r,this._to=s}static parse(t,e){if(t.length<3||t.length>4)throw new Error('"slice" expects 2 or 3 arguments');const r=y(t[1],e),s=y(t[2],e,w);if(s.type!==w)throw new Error('"slice" index must return a number');if(t.length===4){const i=y(t[3],e,w);if(i.type!==w)throw new Error('"slice" index must return a number');return new wt(r.type,r,s,i)}return new wt(r.type,r,s)}evaluate(t,e){const r=this._array.evaluate(t,e);if(!Array.isArray(r)&&typeof r!="string")throw new Error('"slice" input must be an array or a string');const s=this._from.evaluate(t,e);if(s<0||s>=r.length)throw new Error('"slice" index out of bounds');if(s!==Math.floor(s))throw new Error('"slice" index must be an integer');if(this._to){const i=this._to.evaluate(t,e);if(i<0||i>=r.length)throw new Error('"slice" index out of bounds');if(i!==Math.floor(i))throw new Error('"slice" index must be an integer');return r.slice(s,i)}return r.slice(s)}}class Yt{constructor(){this.type=N}static parse(t){if(t.length!==1)throw new Error('"has-id" expects no arguments');return new Yt}evaluate(t,e){return t&&t.id!==void 0}}class S{constructor(t,e){this._args=t,this._calculate=e,this.type=w}static parse(t,e,r){const s=t.slice(1).map(i=>y(i,e));return new S(s,r)}evaluate(t,e){let r;return this._args&&(r=this._args.map(s=>s.evaluate(t,e))),this._calculate(r)}}class Se extends S{static parse(t,e){switch(t.length){case 2:return S.parse(t,e,r=>-r[0]);case 3:return S.parse(t,e,r=>r[0]-r[1]);default:throw new Error('"-" expects 1 or 2 arguments')}}}class Le extends S{static parse(t,e){return S.parse(t,e,r=>{let s=1;for(const i of r)s*=i;return s})}}class Ue extends S{static parse(t,e){if(t.length===3)return S.parse(t,e,r=>r[0]/r[1]);throw new Error('"/" expects 2 arguments')}}class Be extends S{static parse(t,e){if(t.length===3)return S.parse(t,e,r=>r[0]%r[1]);throw new Error('"%" expects 2 arguments')}}class Oe extends S{static parse(t,e){if(t.length===3)return S.parse(t,e,r=>r[0]**r[1]);throw new Error('"^" expects 1 or 2 arguments')}}class Ge extends S{static parse(t,e){return S.parse(t,e,r=>{let s=0;for(const i of r)s+=i;return s})}}class m{constructor(t,e){this._args=t,this._calculate=e,this.type=w}static parse(t,e){const r=t.slice(1).map(s=>y(s,e));return new m(r,m.ops[t[0]])}evaluate(t,e){let r;return this._args&&(r=this._args.map(s=>s.evaluate(t,e))),this._calculate(r)}}m.ops={abs:a=>Math.abs(a[0]),acos:a=>Math.acos(a[0]),asin:a=>Math.asin(a[0]),atan:a=>Math.atan(a[0]),ceil:a=>Math.ceil(a[0]),cos:a=>Math.cos(a[0]),e:()=>Math.E,floor:a=>Math.floor(a[0]),ln:a=>Math.log(a[0]),ln2:()=>Math.LN2,log10:a=>Math.log(a[0])/Math.LN10,log2:a=>Math.log(a[0])/Math.LN2,max:a=>Math.max(...a),min:a=>Math.min(...a),pi:()=>Math.PI,round:a=>Math.round(a[0]),sin:a=>Math.sin(a[0]),sqrt:a=>Math.sqrt(a[0]),tan:a=>Math.tan(a[0])};class Ht{constructor(t){this._args=t,this.type=B}static parse(t,e){return new Ht(t.slice(1).map(r=>y(r,e)))}evaluate(t,e){return this._args.map(r=>r.evaluate(t,e)).join("")}}class st{constructor(t,e){this._arg=t,this._calculate=e,this.type=B}static parse(t,e){if(t.length!==2)throw new Error(`${t[0]} expects 1 argument`);const r=y(t[1],e);return new st(r,st.ops[t[0]])}evaluate(t,e){return this._calculate(this._arg.evaluate(t,e))}}st.ops={downcase:a=>a.toLowerCase(),upcase:a=>a.toUpperCase()};class jt{constructor(t){this._args=t,this.type=Y}static parse(t,e){if(t.length!==4)throw new Error('"rgb" expects 3 arguments');const r=t.slice(1).map(s=>y(s,e));return new jt(r)}evaluate(t,e){const r=this._validate(this._args[0].evaluate(t,e)),s=this._validate(this._args[1].evaluate(t,e)),i=this._validate(this._args[2].evaluate(t,e));return new b({r,g:s,b:i})}_validate(t){if(typeof t!="number"||t<0||t>255)throw new Error(`${t}: invalid color component`);return Math.round(t)}}class Kt{constructor(t){this._args=t,this.type=Y}static parse(t,e){if(t.length!==5)throw new Error('"rgba" expects 4 arguments');const r=t.slice(1).map(s=>y(s,e));return new Kt(r)}evaluate(t,e){const r=this._validate(this._args[0].evaluate(t,e)),s=this._validate(this._args[1].evaluate(t,e)),i=this._validate(this._args[2].evaluate(t,e)),o=this._validateAlpha(this._args[3].evaluate(t,e));return new b({r,g:s,b:i,a:o})}_validate(t){if(typeof t!="number"||t<0||t>255)throw new Error(`${t}: invalid color component`);return Math.round(t)}_validateAlpha(t){if(typeof t!="number"||t<0||t>1)throw new Error(`${t}: invalid alpha color component`);return t}}class qt{constructor(t){this._color=t,this.type=ct(w,4)}static parse(t,e){if(t.length!==2)throw new Error('"to-rgba" expects 1 argument');const r=y(t[1],e);return new qt(r)}evaluate(t,e){return new b(this._color.evaluate(t,e)).toRgba()}}class q{constructor(t,e){this.type=t,this._args=e}static parse(t,e){const r=t[0];if(t.length<2)throw new Error(`${r} expects at least one argument`);let s,i=1;if(r==="array"){if(t.length>2){switch(t[1]){case"string":s=B;break;case"number":s=w;break;case"boolean":s=N;break;default:throw new Error('"array" type argument must be string, number or boolean')}i++}else s=C;let n;if(t.length>3){if(n=t[2],n!==null&&(typeof n!="number"||n<0||n!==Math.floor(n)))throw new Error('"array" length argument must be a positive integer literal');i++}s=ct(s,n)}else switch(r){case"string":s=B;break;case"number":s=w;break;case"boolean":s=N;break;case"object":s=Tt}const o=[];for(;i<t.length;i++){const n=y(t[i],e);o.push(n)}return new q(s,o)}evaluate(t,e){let r;for(const s of this._args){const i=s.evaluate(t,e);if(r=bt(i),Nt(r,this.type))return i}throw new Error(`Expected ${rt(this.type)} but got ${rt(r)}`)}}class F{constructor(t,e){this.type=t,this._args=e}static parse(t,e){const r=t[0],s=F.types[r];if(s===N||s===B){if(t.length!==2)throw new Error(`${r} expects one argument`)}else if(t.length<2)throw new Error(`${r} expects at least one argument`);const i=[];for(let o=1;o<t.length;o++){const n=y(t[o],e);i.push(n)}return new F(s,i)}evaluate(t,e){if(this.type===N)return!!this._args[0].evaluate(t,e);if(this.type===B)return xt(this._args[0].evaluate(t,e));if(this.type===w){for(const r of this._args){const s=Number(r.evaluate(t,e));if(!isNaN(s))return s}return null}if(this.type===Y){for(const r of this._args)try{const s=F.toColor(r.evaluate(t,e));if(s instanceof b)return s}catch{}return null}}static toBoolean(t){return!!t}static toString(t){return xt(t)}static toNumber(t){const e=Number(t);if(isNaN(e))throw new Error(`"${t}" is not a number`);return e}static toColor(t){if(t instanceof b)return t;if(typeof t=="string"){const e=b.fromString(t);if(e)return e;throw new Error(`"${t}" is not a color`)}if(Array.isArray(t))return b.fromArray(t);throw new Error(`"${t}" is not a color`)}}F.types={"to-boolean":N,"to-color":Y,"to-number":w,"to-string":B};class Wt{constructor(t){this._val=t,this.type=bt(t)}static parse(t){if(t.length!==2)throw new Error('"literal" expects 1 argument');return new Wt(t[1])}evaluate(t,e){return this._val}}class Zt{constructor(t){this._arg=t,this.type=B}static parse(t,e){if(t.length!==2)throw new Error('"typeof" expects 1 argument');return new Zt(y(t[1],e))}evaluate(t,e){return rt(bt(this._arg.evaluate(t,e)))}}function y(a,t,e){const r=typeof a;if(r==="string"||r==="boolean"||r==="number"||a===null){if(e)switch(e.kind){case"string":r!=="string"&&(a=F.toString(a));break;case"number":r!=="number"&&(a=F.toNumber(a));break;case"color":a=F.toColor(a)}a=["literal",a]}if(!Array.isArray(a)||a.length===0)throw new Error("Expression must be a non empty array");const s=a[0];if(typeof s!="string")throw new Error("First element of expression must be a string");const i=fe[s];if(i===void 0)throw new Error(`Invalid expression operator "${s}"`);if(!i)throw new Error(`Unimplemented expression operator "${s}"`);return i.parse(a,t,e)}const fe={array:q,boolean:q,collator:null,format:null,image:null,literal:Wt,number:q,"number-format":null,object:q,string:q,"to-boolean":F,"to-color":F,"to-number":F,"to-string":F,typeof:Zt,accumulated:null,"feature-state":null,"geometry-type":Lt,id:St,"line-progress":null,properties:Te,at:Vt,get:dt,has:mt,in:Mt,"index-of":Pe,length:Ct,slice:wt,"!":Ut,"!=":Ie,"<":ve,"<=":De,"==":be,">":Re,">=":Ae,all:Bt,any:Ot,case:kt,coalesce:Ne,match:zt,within:null,interpolate:_t,"interpolate-hcl":_t,"interpolate-lab":_t,step:Ft,let:$t,var:xe,concat:Ht,downcase:st,"is-supported-script":null,"resolved-locale":null,upcase:st,rgb:jt,rgba:Kt,"to-rgba":qt,"-":Se,"*":Le,"/":Ue,"%":Be,"^":Oe,"+":Ge,abs:m,acos:m,asin:m,atan:m,ceil:m,cos:m,e:m,floor:m,ln:m,ln2:m,log10:m,log2:m,max:m,min:m,pi:m,round:m,sin:m,sqrt:m,tan:m,zoom:It,"heatmap-density":null,"has-id":Yt,none:Gt};class L{constructor(t){this._expression=t}filter(t,e){if(!this._expression)return!0;try{return this._expression.evaluate(t,e)}catch(r){return console.log(r.message),!0}}static createFilter(t){if(!t)return null;this.isLegacyFilter(t)&&(t=this.convertLegacyFilter(t));try{const e=y(t,null,N);return new L(e)}catch(e){return console.log(e.message),null}}static isLegacyFilter(t){if(!Array.isArray(t)||t.length===0)return!0;switch(t[0]){case"==":case"!=":case">":case"<":case">=":case"<=":return t.length===3&&typeof t[1]=="string"&&!Array.isArray(t[2]);case"in":return t.length>=3&&typeof t[1]=="string"&&!Array.isArray(t[2]);case"!in":case"none":case"!has":return!0;case"any":case"all":for(let e=1;e<t.length;e++)if(this.isLegacyFilter(t[e]))return!0;return!1;case"has":return t.length===2&&(t[1]==="$id"||t[1]==="$type");default:return!1}}static convertLegacyFilter(t){if(!Array.isArray(t)||t.length===0)return!0;const e=t[0];if(t.length===1)return e!=="any";switch(e){case"==":return L.convertComparison("==",t[1],t[2]);case"!=":return L.negate(L.convertComparison("==",t[1],t[2]));case">":case"<":case">=":case"<=":return L.convertComparison(e,t[1],t[2]);case"in":return L.convertIn(t[1],t.slice(2));case"!in":return L.negate(L.convertIn(t[1],t.slice(2)));case"any":case"all":case"none":return L.convertCombining(e,t.slice(1));case"has":return L.convertHas(t[1]);case"!has":return L.negate(L.convertHas(t[1]));default:throw new Error("Unexpected legacy filter.")}}static convertComparison(t,e,r){switch(e){case"$type":return[t,["geometry-type"],r];case"$id":return[t,["id"],r];default:return[t,["get",e],r]}}static convertIn(t,e){switch(t){case"$type":return["in",["geometry-type"],["literal",e]];case"$id":return["in",["id"],["literal",e]];default:return["in",["get",t],["literal",e]]}}static convertHas(t){switch(t){case"$type":return!0;case"$id":return["has-id"];default:return["has",t]}}static convertCombining(t,e){return[t].concat(e.map(this.convertLegacyFilter))}static negate(t){return["!",t]}}class Et{constructor(t,e){let r;switch(this.isDataDriven=!1,this.interpolator=null,t.type){case"number":case"color":r=!0;break;case"array":r=t.value==="number";break;default:r=!1}if(e==null&&(e=t.default),Array.isArray(e)&&e.length>0&&fe[e[0]]){const i={number:w,color:Y,string:B,boolean:N,enum:B};try{const o=t.type==="array"?ct(i[t.value]||C,t.length):i[t.type],n=y(e,null,o);this.getValue=this._buildExpression(n,t),this.isDataDriven=!0,n instanceof _t&&n.input instanceof It&&(this.interpolator=n)}catch(o){console.log(o.message),this.getValue=this._buildSimple(t.default)}return}r&&e.type==="interval"&&(r=!1);const s=e&&e.stops&&e.stops.length>0;if(s)for(const i of e.stops)i[1]=this._validate(i[1],t);if(this.isDataDriven=!!e&&!!e.property,this.isDataDriven)if(e.default!==void 0&&(e.default=this._validate(e.default,t)),s)switch(e.type){case"identity":this.getValue=this._buildIdentity(e,t);break;case"categorical":this.getValue=this._buildCategorical(e,t);break;default:this.getValue=r?this._buildInterpolate(e,t):this._buildInterval(e,t)}else this.getValue=this._buildIdentity(e,t);else s?this.getValue=r?this._buildZoomInterpolate(e):this._buildZoomInterval(e):(e=this._validate(e,t),this.getValue=this._buildSimple(e))}_validate(t,e){if(e.type==="number"){if(t<e.minimum)return e.minimum;if(t>e.maximum)return e.maximum}else e.type==="color"?t=Et._parseColor(t):e.type==="enum"?typeof t=="string"&&(t=e.values.indexOf(t)):e.type==="array"&&e.value==="enum"?t=t.map(r=>typeof r=="string"?e.values.indexOf(r):r):e.type==="string"&&(t=xt(t));return t}_buildSimple(t){return()=>t}_buildExpression(t,e){return(r,s)=>{try{const i=t.evaluate(s,r);return i===void 0?e.default:this._validate(i,e)}catch(i){return console.log(i.message),e.default}}}_buildIdentity(t,e){return(r,s)=>{let i;return s&&(i=s.values[t.property]),i!==void 0&&(i=this._validate(i,e)),i??(t.default!==void 0?t.default:e.default)}}_buildCategorical(t,e){return(r,s)=>{let i;return s&&(i=s.values[t.property]),i=this._categorical(i,t.stops),i!==void 0?i:t.default!==void 0?t.default:e.default}}_buildInterval(t,e){return(r,s)=>{let i;return s&&(i=s.values[t.property]),typeof i=="number"?this._interval(i,t.stops):t.default!==void 0?t.default:e.default}}_buildInterpolate(t,e){return(r,s)=>{let i;return s&&(i=s.values[t.property]),typeof i=="number"?this._interpolate(i,t.stops,t.base||1):t.default!==void 0?t.default:e.default}}_buildZoomInterpolate(t){return e=>this._interpolate(e,t.stops,t.base||1)}_buildZoomInterval(t){return e=>this._interval(e,t.stops)}_categorical(t,e){const r=e.length;for(let s=0;s<r;s++)if(e[s][0]===t)return e[s][1]}_interval(t,e){const r=e.length;let s=0;for(let i=0;i<r&&e[i][0]<=t;i++)s=i;return e[s][1]}_interpolate(t,e,r){let s,i;const o=e.length;for(let n=0;n<o;n++){const l=e[n];if(!(l[0]<=t)){i=l;break}s=l}if(s&&i){const n=i[0]-s[0],l=t-s[0],u=r===1?l/n:(r**l-1)/(r**n-1);if(Array.isArray(s[1])){const p=s[1],h=i[1],f=[];for(let _=0;_<p.length;_++)f.push(P(p[_],h[_],u));return f}return P(s[1],i[1],u)}return s?s[1]:i?i[1]:void 0}static _isEmpty(t){for(const e in t)if(t.hasOwnProperty(e))return!1;return!0}static _parseColor(t){return Array.isArray(t)?t:(typeof t=="string"&&(t=new b(t)),t instanceof b&&!this._isEmpty(t)?b.toUnitRGBA(t):void 0)}}var ut;(function(a){a[a.BUTT=0]="BUTT",a[a.ROUND=1]="ROUND",a[a.SQUARE=2]="SQUARE",a[a.UNKNOWN=4]="UNKNOWN"})(ut||(ut={}));class ht{constructor(t,e,r,s){switch(this.type=t,this.typeName=e.type,this.id=e.id,this.source=e.source,this.sourceLayer=e["source-layer"],this.minzoom=e.minzoom,this.maxzoom=e.maxzoom,this.filter=e.filter,this.layout=e.layout,this.paint=e.paint,this.z=r,this.uid=s,t){case T.BACKGROUND:this._layoutDefinition=$.backgroundLayoutDefinition,this._paintDefinition=$.backgroundPaintDefinition;break;case T.FILL:this._layoutDefinition=$.fillLayoutDefinition,this._paintDefinition=$.fillPaintDefinition;break;case T.LINE:this._layoutDefinition=$.lineLayoutDefinition,this._paintDefinition=$.linePaintDefinition;break;case T.SYMBOL:this._layoutDefinition=$.symbolLayoutDefinition,this._paintDefinition=$.symbolPaintDefinition;break;case T.CIRCLE:this._layoutDefinition=$.circleLayoutDefinition,this._paintDefinition=$.circlePaintDefinition}this._layoutProperties=this._parseLayout(this.layout),this._paintProperties=this._parsePaint(this.paint)}getFeatureFilter(){return this._featureFilter!==void 0?this._featureFilter:this._featureFilter=L.createFilter(this.filter)}getLayoutProperty(t){return this._layoutProperties[t]}getPaintProperty(t){return this._paintProperties[t]}getLayoutValue(t,e,r){let s;const i=this._layoutProperties[t];return i&&(s=i.getValue(e,r)),s===void 0&&(s=this._layoutDefinition[t].default),s}getPaintValue(t,e,r){let s;const i=this._paintProperties[t];return i&&(s=i.getValue(e,r)),s===void 0&&(s=this._paintDefinition[t].default),s}isPainterDataDriven(){const t=this._paintProperties;if(t){for(const e in t)if(t[e].isDataDriven)return!0}return!1}_parseLayout(t){const e={};for(const r in t){const s=this._layoutDefinition[r];s&&(e[r]=new Et(s,t[r]))}return e}_parsePaint(t){const e={};for(const r in t){const s=this._paintDefinition[r];s&&(e[r]=new Et(s,t[r]))}return e}computeAttributesKey(t,e,r,s){let i=0,o=0;for(const n of e){let l=3;if(!n||n!==s){const u=r[n],{isLayout:p,isOptional:h}=u,f=p?this.getLayoutProperty(n):this.getPaintProperty(n);l=f!=null&&f.interpolator?2:f!=null&&f.isDataDriven?1:h&&!f?3:0}o|=l<<i,i+=2}return o<<3|t}}class se extends ht{constructor(t,e,r,s){super(t,e,r,s),this.backgroundMaterial=new Q(this.computeAttributesKey(W.BACKGROUND,Q.ATTRIBUTES,Q.ATTRIBUTES_INFO))}}class ie extends ht{constructor(t,e,r,s){super(t,e,r,s);const i=this.getPaintProperty("fill-color"),o=this.getPaintProperty("fill-opacity"),n=this.getPaintProperty("fill-pattern");this.hasDataDrivenColor=i==null?void 0:i.isDataDriven,this.hasDataDrivenOpacity=o==null?void 0:o.isDataDriven,this.hasDataDrivenFill=this.hasDataDrivenColor||this.hasDataDrivenOpacity||(n==null?void 0:n.isDataDriven);const l=this.getPaintProperty("fill-outline-color");this.outlineUsesFillColor=!l,this.hasDataDrivenOutlineColor=l==null?void 0:l.isDataDriven,this.hasDataDrivenOutline=l?l.isDataDriven:!!i&&i.isDataDriven,this.hasDataDrivenOutline=(l?this.hasDataDrivenOutlineColor:this.hasDataDrivenColor)||this.hasDataDrivenOpacity,this.fillMaterial=new tt(this.computeAttributesKey(W.FILL,tt.ATTRIBUTES,tt.ATTRIBUTES_INFO)),this.outlineMaterial=new V(this.computeAttributesKey(W.OUTLINE,this.outlineUsesFillColor?V.ATTRIBUTES_FILL:V.ATTRIBUTES_OUTLINE,this.outlineUsesFillColor?V.ATTRIBUTES_INFO_FILL:V.ATTRIBUTES_INFO_OUTLINE),this.outlineUsesFillColor)}}class ae extends ht{constructor(t,e,r,s){var o,n,l,u,p,h,f,_,d;super(t,e,r,s);const i=this.getPaintProperty("line-pattern");if(this.lineMaterial=new et(this.computeAttributesKey(W.LINE,et.ATTRIBUTES,et.ATTRIBUTES_INFO,i?"line-dasharray":"")),this.hasDataDrivenLine=((o=this.getPaintProperty("line-blur"))==null?void 0:o.isDataDriven)||((n=this.getPaintProperty("line-color"))==null?void 0:n.isDataDriven)||((l=this.getPaintProperty("line-gap-width"))==null?void 0:l.isDataDriven)||((u=this.getPaintProperty("line-offset"))==null?void 0:u.isDataDriven)||((p=this.getPaintProperty("line-opacity"))==null?void 0:p.isDataDriven)||((h=this.getPaintProperty("line-pattern"))==null?void 0:h.isDataDriven)||((f=this.getPaintProperty("line-dasharray"))==null?void 0:f.isDataDriven)||((_=this.getLayoutProperty("line-cap"))==null?void 0:_.isDataDriven)||((d=this.getPaintProperty("line-width"))==null?void 0:d.isDataDriven),this.canUseThinTessellation=!1,!this.hasDataDrivenLine){const O=this.getPaintProperty("line-width");if(!O||typeof O=="number"&&.5*O<_e){const R=this.getPaintProperty("line-offset");(!R||typeof R=="number"&&R===0)&&(this.canUseThinTessellation=!0)}}}getDashKey(t,e){let r;switch(e){case ut.BUTT:r="Butt";break;case ut.ROUND:r="Round";break;case ut.SQUARE:r="Square";break;default:r="Butt"}return`dasharray-[${t.toString()}]-${r}`}}class ne extends ht{constructor(t,e,r,s){var i,o,n,l,u,p,h,f,_,d,O,R;super(t,e,r,s),this.iconMaterial=new ot(this.computeAttributesKey(W.ICON,ot.ATTRIBUTES,ot.ATTRIBUTES_INFO)),this.textMaterial=new lt(this.computeAttributesKey(W.TEXT,lt.ATTRIBUTES,lt.ATTRIBUTES_INFO)),this.hasDataDrivenIcon=((i=this.getPaintProperty("icon-color"))==null?void 0:i.isDataDriven)||((o=this.getPaintProperty("icon-halo-blur"))==null?void 0:o.isDataDriven)||((n=this.getPaintProperty("icon-halo-color"))==null?void 0:n.isDataDriven)||((l=this.getPaintProperty("icon-halo-width"))==null?void 0:l.isDataDriven)||((u=this.getPaintProperty("icon-opacity"))==null?void 0:u.isDataDriven)||((p=this.getLayoutProperty("icon-size"))==null?void 0:p.isDataDriven),this.hasDataDrivenText=((h=this.getPaintProperty("text-color"))==null?void 0:h.isDataDriven)||((f=this.getPaintProperty("text-halo-blur"))==null?void 0:f.isDataDriven)||((_=this.getPaintProperty("text-halo-color"))==null?void 0:_.isDataDriven)||((d=this.getPaintProperty("text-halo-width"))==null?void 0:d.isDataDriven)||((O=this.getPaintProperty("text-opacity"))==null?void 0:O.isDataDriven)||((R=this.getLayoutProperty("text-size"))==null?void 0:R.isDataDriven)}}class oe extends ht{constructor(t,e,r,s){super(t,e,r,s),this.circleMaterial=new J(this.computeAttributesKey(W.CIRCLE,J.ATTRIBUTES,J.ATTRIBUTES_INFO))}}class Ke{constructor(t,e,r){let s;this.allowOverlap=t.getLayoutValue("icon-allow-overlap",e),this.ignorePlacement=t.getLayoutValue("icon-ignore-placement",e),this.keepUpright=t.getLayoutValue("icon-keep-upright",e),this.optional=t.getLayoutValue("icon-optional",e),this.rotationAlignment=t.getLayoutValue("icon-rotation-alignment",e),this.rotationAlignment===X.AUTO&&(this.rotationAlignment=r?X.MAP:X.VIEWPORT),s=t.getLayoutProperty("icon-anchor"),s!=null&&s.isDataDriven?this._anchorProp=s:this.anchor=t.getLayoutValue("icon-anchor",e),s=t.getLayoutProperty("icon-offset"),s!=null&&s.isDataDriven?this._offsetProp=s:this.offset=t.getLayoutValue("icon-offset",e),s=t.getLayoutProperty("icon-padding"),s!=null&&s.isDataDriven?this._paddingProp=s:this.padding=t.getLayoutValue("icon-padding",e),s=t.getLayoutProperty("icon-rotate"),s!=null&&s.isDataDriven?this._rotateProp=s:this.rotate=t.getLayoutValue("icon-rotate",e),s=t.getLayoutProperty("icon-size"),s!=null&&s.isDataDriven?this._sizeProp=s:this.size=t.getLayoutValue("icon-size",e)}update(t,e){this._anchorProp&&(this.anchor=this._anchorProp.getValue(t,e)),this._offsetProp&&(this.offset=this._offsetProp.getValue(t,e)),this._paddingProp&&(this.padding=this._paddingProp.getValue(t,e)),this._rotateProp&&(this.rotate=this._rotateProp.getValue(t,e)),this._sizeProp&&(this.size=this._sizeProp.getValue(t,e))}}class qe{constructor(t,e,r){let s;this.allowOverlap=t.getLayoutValue("text-allow-overlap",e),this.ignorePlacement=t.getLayoutValue("text-ignore-placement",e),this.keepUpright=t.getLayoutValue("text-keep-upright",e),this.optional=t.getLayoutValue("text-optional",e),this.rotationAlignment=t.getLayoutValue("text-rotation-alignment",e),this.rotationAlignment===X.AUTO&&(this.rotationAlignment=r?X.MAP:X.VIEWPORT),s=t.getLayoutProperty("text-anchor"),s!=null&&s.isDataDriven?this._anchorProp=s:this.anchor=t.getLayoutValue("text-anchor",e),s=t.getLayoutProperty("text-justify"),s!=null&&s.isDataDriven?this._justifyProp=s:this.justify=t.getLayoutValue("text-justify",e),s=t.getLayoutProperty("text-letter-spacing"),s!=null&&s.isDataDriven?this._letterSpacingProp=s:this.letterSpacing=t.getLayoutValue("text-letter-spacing",e),s=t.getLayoutProperty("text-line-height"),s!=null&&s.isDataDriven?this._lineHeightProp=s:this.lineHeight=t.getLayoutValue("text-line-height",e),s=t.getLayoutProperty("text-max-angle"),s!=null&&s.isDataDriven?this._maxAngleProp=s:this.maxAngle=t.getLayoutValue("text-max-angle",e),s=t.getLayoutProperty("text-max-width"),s!=null&&s.isDataDriven?this._maxWidthProp=s:this.maxWidth=t.getLayoutValue("text-max-width",e),s=t.getLayoutProperty("text-offset"),s!=null&&s.isDataDriven?this._offsetProp=s:this.offset=t.getLayoutValue("text-offset",e),s=t.getLayoutProperty("text-padding"),s!=null&&s.isDataDriven?this._paddingProp=s:this.padding=t.getLayoutValue("text-padding",e),s=t.getLayoutProperty("text-rotate"),s!=null&&s.isDataDriven?this._rotateProp=s:this.rotate=t.getLayoutValue("text-rotate",e),s=t.getLayoutProperty("text-size"),s!=null&&s.isDataDriven?this._sizeProp=s:this.size=t.getLayoutValue("text-size",e),s=t.getLayoutProperty("text-writing-mode"),s!=null&&s.isDataDriven?this._writingModeProp=s:this.writingMode=t.getLayoutValue("text-writing-mode",e)}update(t,e){this._anchorProp&&(this.anchor=this._anchorProp.getValue(t,e)),this._justifyProp&&(this.justify=this._justifyProp.getValue(t,e)),this._letterSpacingProp&&(this.letterSpacing=this._letterSpacingProp.getValue(t,e)),this._lineHeightProp&&(this.lineHeight=this._lineHeightProp.getValue(t,e)),this._maxAngleProp&&(this.maxAngle=this._maxAngleProp.getValue(t,e)),this._maxWidthProp&&(this.maxWidth=this._maxWidthProp.getValue(t,e)),this._offsetProp&&(this.offset=this._offsetProp.getValue(t,e)),this._paddingProp&&(this.padding=this._paddingProp.getValue(t,e)),this._rotateProp&&(this.rotate=this._rotateProp.getValue(t,e)),this._sizeProp&&(this.size=this._sizeProp.getValue(t,e)),this._writingModeProp&&(this.writingMode=this._writingModeProp.getValue(t,e))}}class nt{constructor(t){if(this._style=t,this.backgroundBucketIds=[],this._uidToLayer=new Map,this._layerByName={},this._runningId=0,t.layers||(t.layers=[]),this.version=parseFloat(t.version),this.layers=t.layers.map((e,r,s)=>this._create(e,r,s)).filter(e=>!!e),this.layers){let e;for(let r=0;r<this.layers.length;r++)e=this.layers[r],this._layerByName[e.id]=e,this._uidToLayer.set(e.uid,e),e.type===T.BACKGROUND&&this.backgroundBucketIds.push(e.id)}this._identifyRefLayers()}isPainterDataDriven(t){const e=this._layerByName[t];return!!e&&e.isPainterDataDriven()}getStyleLayerId(t){return t>=this.layers.length?null:this.layers[t].id}getStyleLayerByUID(t){return this._uidToLayer.get(t)??null}getStyleLayerIndex(t){const e=this._layerByName[t];return e?this.layers.indexOf(e):-1}setStyleLayer(t,e){if(!t||!t.id)return;const r=this._style;e!=null&&e>=this.layers.length&&(e=this.layers.length-1);let s,i=!0;const o=this._layerByName[t.id];if(o){const n=this.layers.indexOf(o);e||(e=n),e===n?(i=!1,s=nt._recreateLayer(t,o),this.layers[e]=s,r.layers[e]=t):(this.layers.splice(n,1),r.layers.splice(n,1),s=this._create(t,e,this.layers),this.layers.splice(e,0,s),r.layers.splice(e,0,t))}else s=this._create(t,e,this.layers),!e||e>=this.layers.length?(this.layers.push(s),r.layers.push(t)):(this.layers.splice(e,0,s),r.layers.splice(e,0,t));this._layerByName[t.id]=s,this._uidToLayer.set(s.uid,s),i&&this._recomputeZValues(),this._identifyRefLayers()}getStyleLayer(t){const e=this._layerByName[t];return e?{type:e.typeName,id:e.id,source:e.source,"source-layer":e.sourceLayer,minzoom:e.minzoom,maxzoom:e.maxzoom,filter:e.filter,layout:e.layout,paint:e.paint}:null}deleteStyleLayer(t){const e=this._layerByName[t];if(e){delete this._layerByName[t],this._uidToLayer.delete(e.uid);const r=this.layers.indexOf(e);this.layers.splice(r,1),this._style.layers.splice(r,1),this._recomputeZValues(),this._identifyRefLayers()}}getLayerById(t){return this._layerByName[t]}getLayoutProperties(t){const e=this._layerByName[t];return e?e.layout:null}getPaintProperties(t){const e=this._layerByName[t];return e?e.paint:null}setPaintProperties(t,e){const r=this._layerByName[t];if(!r)return;const s={type:r.typeName,id:r.id,source:r.source,"source-layer":r.sourceLayer,minzoom:r.minzoom,maxzoom:r.maxzoom,filter:r.filter,layout:r.layout,paint:e},i=nt._recreateLayer(s,r),o=this.layers.indexOf(r);this.layers[o]=i,this._style.layers[o].paint=e,this._layerByName[r.id]=i,this._uidToLayer.set(r.uid,i)}setLayoutProperties(t,e){const r=this._layerByName[t];if(!r)return;const s={type:r.typeName,id:r.id,source:r.source,"source-layer":r.sourceLayer,minzoom:r.minzoom,maxzoom:r.maxzoom,filter:r.filter,layout:e,paint:r.paint},i=nt._recreateLayer(s,r),o=this.layers.indexOf(r);this.layers[o]=i,this._style.layers[o].layout=e,this._layerByName[r.id]=i,this._uidToLayer.set(r.uid,i)}setStyleLayerVisibility(t,e){const r=this._layerByName[t];if(!r)return;const s=r.layout||{};s.visibility=e;const i={type:r.typeName,id:r.id,source:r.source,"source-layer":r.sourceLayer,minzoom:r.minzoom,maxzoom:r.maxzoom,filter:r.filter,layout:s,paint:r.paint},o=nt._recreateLayer(i,r),n=this.layers.indexOf(r);this.layers[n]=o,this._style.layers[n].layout=s,this._layerByName[r.id]=o,this._uidToLayer.set(r.uid,o)}getStyleLayerVisibility(t){var r;const e=this._layerByName[t];return e?((r=e.layout)==null?void 0:r.visibility)??"visible":"none"}_recomputeZValues(){const t=this.layers,e=1/(t.length+1);for(let r=0;r<t.length;r++)t[r].z=1-(1+r)*e}_identifyRefLayers(){const t=[],e=[];let r=0;for(const s of this.layers){const i=s.layout;if(s.type===T.FILL){const o=s;let n=s.source+"|"+s.sourceLayer;n+="|"+((i==null?void 0:i.visibility)??""),n+="|"+s.minzoom,n+="|"+s.maxzoom,n+="|"+JSON.stringify(s.filter),(o.hasDataDrivenFill||o.hasDataDrivenOutline)&&(n+="|"+r),t.push({key:n,layer:s})}else if(s.type===T.LINE){const o=s,n=s.paint,l=n!=null&&(n["line-pattern"]!=null||n["line-dasharray"]!=null);let u=s.source+"|"+s.sourceLayer;u+="|"+((i==null?void 0:i.visibility)??""),u+="|"+s.minzoom,u+="|"+s.maxzoom,u+="|"+JSON.stringify(s.filter),u+="|"+(i!==void 0?i["line-cap"]:""),u+="|"+(i!==void 0?i["line-join"]:""),(o.hasDataDrivenLine||l)&&(u+="|"+r),e.push({key:u,layer:s})}++r}this._assignRefLayers(t),this._assignRefLayers(e)}_assignRefLayers(t){let e,r;t.sort((i,o)=>i.key<o.key?-1:i.key>o.key?1:0);const s=t.length;for(let i=0;i<s;i++){const o=t[i];if(o.key===e)o.layer.refLayerId=r;else if(e=o.key,r=o.layer.id,o.layer.type===T.FILL){if(!o.layer.getPaintProperty("fill-outline-color"))for(let n=i+1;n<s;n++){const l=t[n];if(l.key!==e)break;if(l.layer.getPaintProperty("fill-outline-color")){t[i]=l,t[n]=o,r=l.layer.id;break}}}else if(o.layer.type===T.LINE){let n=o.layer;for(let l=i+1;l<s;l++){const u=t[l];if(u.key!==e)break;const p=u.layer;(n.canUseThinTessellation&&!p.canUseThinTessellation||!n.canUseThinTessellation&&(p.getPaintProperty("line-pattern")||p.getPaintProperty("line-dasharray")))&&(n=p,t[i]=u,t[l]=o,r=u.layer.id)}}}}_create(t,e,r){const s=1-(1+e)*(1/(r.length+1)),i=this._runningId++;switch(t.type){case"background":return new se(T.BACKGROUND,t,s,i);case"fill":return new ie(T.FILL,t,s,i);case"line":return new ae(T.LINE,t,s,i);case"symbol":return new ne(T.SYMBOL,t,s,i);case"raster":return console.warn(`Unsupported vector tile raster layer ${t.id}`),null;case"circle":return new oe(T.CIRCLE,t,s,i)}return null}static _recreateLayer(t,e){switch(t.type){case"background":return new se(T.BACKGROUND,t,e.z,e.uid);case"fill":return new ie(T.FILL,t,e.z,e.uid);case"line":return new ae(T.LINE,t,e.z,e.uid);case"symbol":return new ne(T.SYMBOL,t,e.z,e.uid);case"raster":return console.warn(`Unsupported vector tile raster layer ${t.id}`),null;case"circle":return new oe(T.CIRCLE,t,e.z,e.uid)}return null}}export{Ke as T,nt as l,qe as m,de as t};
