"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1482],{48853:(e,a,t)=>{t.d(a,{Qn:()=>m,iG:()=>l});var i,n=t(27535),r=t(17126);class s{}s.instance=new r.vF("Etc/UTC"),(i||(i={})).TimeZoneNotRecognised="TimeZoneNotRecognised";const o={[i.TimeZoneNotRecognised]:"Timezone identifier has not been recognised."};class d extends Error{constructor(e,a){super((0,n.Hy)(o[e],a)),this.declaredRootClass="esri.arcade.arcadedate.dateerror",Error.captureStackTrace&&Error.captureStackTrace(this,d)}}class l{constructor(e){this._date=e,this.declaredRootClass="esri.arcade.arcadedate"}static fromParts(e=0,a=1,t=1,i=0,n=0,s=0,o=0,d){if(isNaN(e)||isNaN(a)||isNaN(t)||isNaN(i)||isNaN(n)||isNaN(s)||isNaN(o))return null;let c=0;const u=r.ou.local(e,a).daysInMonth;t<1&&(c=t-1,t=1),t>u&&(c=t-u,t=u);let T=0;a>12?(T=a-12,a=12):a<1&&(T=a-1,a=1);let h=0;n>59?(h=n-59,n=59):n<0&&(h=n,n=0);let f=0;s>59?(f=s-59,s=59):s<0&&(f=s,s=0);let S=0;o>999?(S=o-999,o=999):o<0&&(S=o,o=0);let p=r.ou.fromObject({day:t,year:e,month:a,hour:i,minute:n,second:s,millisecond:o},{zone:m(d)});return 0!==T&&(p=p.plus({months:T})),0!==c&&(p=p.plus({days:c})),0!==h&&(p=p.plus({minutes:h})),0!==f&&(p=p.plus({seconds:f})),0!==S&&(p=p.plus({milliseconds:S})),new l(p)}static get systemTimeZoneCanonicalName(){return Intl.DateTimeFormat().resolvedOptions().timeZone??"system"}static arcadeDateAndZoneToArcadeDate(e,a){const t=m(a);return e.isUnknownTimeZone||t===s.instance?l.fromParts(e.year,e.monthJS+1,e.day,e.hour,e.minute,e.second,e.millisecond,t):new l(e._date.setZone(a))}static dateJSToArcadeDate(e){return new l(r.ou.fromJSDate(e,{zone:"system"}))}static dateJSAndZoneToArcadeDate(e,a="system"){return new l(r.ou.fromJSDate(e,{zone:a}))}static unknownEpochToArcadeDate(e){return new l(r.ou.fromMillis(e,{zone:s.instance}))}static unknownDateJSToArcadeDate(e){return new l(r.ou.fromMillis(e.getTime(),{zone:s.instance}))}static epochToArcadeDate(e,a="system"){return new l(r.ou.fromMillis(e,{zone:a}))}static dateTimeToArcadeDate(e){return new l(e)}changeTimeZone(e){const a=m(e);return l.dateTimeToArcadeDate(this._date.setZone(a))}static dateTimeAndZoneToArcadeDate(e,a){const t=m(a);return e.zone===s.instance||t===s.instance?l.fromParts(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond,t):new l(e.setZone(t))}static nowToArcadeDate(e){return new l(r.ou.fromJSDate(new Date,{zone:e}))}static nowUTCToArcadeDate(){return new l(r.ou.utc())}get isSystem(){return"system"===this.timeZone||this.timeZone===l.systemTimeZoneCanonicalName}equals(e){return this.isSystem&&e.isSystem?this.toNumber()===e.toNumber():this.isUnknownTimeZone===e.isUnknownTimeZone&&this._date.equals(e._date)}get isUnknownTimeZone(){return this._date.zone===s.instance}get isValid(){return this._date.isValid}get hour(){return this._date.hour}get second(){return this._date.second}get day(){return this._date.day}get dayOfWeekISO(){return this._date.weekday}get dayOfWeekJS(){let e=this._date.weekday;return e>6&&(e=0),e}get millisecond(){return this._date.millisecond}get monthISO(){return this._date.month}get weekISO(){return this._date.weekNumber}get yearISO(){return this._date.weekYear}get monthJS(){return this._date.month-1}get year(){return this._date.year}get minute(){return this._date.minute}get zone(){return this._date.zone}get timeZoneOffset(){return this.isUnknownTimeZone?0:this._date.offset}get timeZone(){if(this.isUnknownTimeZone)return"unknown";if("system"===this._date.zone.type)return"system";const e=this.zone;return"fixed"===e.type?0===e.fixed?"utc":e.formatOffset(0,"short"):e.name}stringify(){return JSON.stringify(this.toJSDate())}plus(e){return new l(this._date.plus(e))}diff(e,a="milliseconds"){return this._date.diff(e._date,a)[a]}toISOString(e){return e?this._date.toISO({suppressMilliseconds:!0,includeOffset:!this.isUnknownTimeZone}):this._date.toISO({includeOffset:!this.isUnknownTimeZone})}toFormat(e,a){return this._date.toFormat(e,a)}toJSDate(){return this._date.toJSDate()}toSQLString(){return"timestamp '"+this._date.toFormat("yyyy-LL-dd HH:mm:ss")+"'"}toDateTime(){return this._date}toNumber(){return this._date.toMillis()}getTime(){return this._date.toMillis()}toUTC(){return new l(this._date.toUTC())}toLocal(){return new l(this._date.toLocal())}toString(){return this.toISOString(!0)}}function m(e){if(e instanceof r.ld)return e;if("system"===e.toLowerCase())return"system";if("utc"===e.toLowerCase())return"utc";if("unknown"===e.toLowerCase())return s.instance;if(/^[\+\-]?[0-9]{1,2}([:][0-9]{2})?$/.test(e)){const a=r.Qf.parseSpecifier("UTC"+(e.startsWith("+")||e.startsWith("-")?"":"+")+e);if(a)return a}const a=r.vF.create(e);if(!a.isValid)throw new d(i.TimeZoneNotRecognised);return a}},85839:(e,a,t)=>{t.d(a,{nu:()=>r}),t(48853);var i=t(99514),n=(t(80216),t(17126),t(42799));class r{constructor(){this.dateTimeReferenceMetaData=null,this._fieldTimeZoneIndex={},this._fieldIndex=null,this._ianaPreferred=null,this._ianaTimeInfo=null,this._ianaEditFields=null,this._ianaLayerDateFields=null}static create(e,a){const t=new r;return t.dateTimeReferenceMetaData=a,t._fieldIndex=e instanceof i.Z?e:new i.Z(e),t}static createFromLayer(e){if(!e)return null;if(!e.fieldsIndex)return!e.declaredClass&&e.fields?r.create(e.fields,e):null;const a=new r;return a._fieldIndex=e.fieldsIndex,a.dateTimeReferenceMetaData={timeInfo:e?.timeInfo?.toJSON()??null,editFieldsInfo:e?.editFieldsInfo?.toJSON()??null,dateFieldsTimeReference:e?.dateFieldsTimeReference?.toJSON()??null,preferredTimeReference:e?.preferredTimeReference?.toJSON()??null,datesInUnknownTimezone:!0===e?.datesInUnknownTimezone},a}fieldTimeZone(e){const a=this._fieldIndex?.get(e);if(!a)return null;if("date"!==a.type&&"esriFieldTypeDate"!==a.type)return null;const t=this._fieldTimeZoneIndex[a.name];if(void 0!==t)return t;const i=[{field:this.dateTimeReferenceMetaData?.editFieldsInfo?.creationDateField,timeReference:this.dateTimeReferenceMetaData?.editFieldsInfo?.dateFieldsTimeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone},{field:this.dateTimeReferenceMetaData?.editFieldsInfo?.editDateField,timeReference:this.dateTimeReferenceMetaData?.editFieldsInfo?.dateFieldsTimeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone},{field:this.dateTimeReferenceMetaData?.timeInfo?.startTimeField,timeReference:this.dateTimeReferenceMetaData?.timeInfo?.timeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone},{field:this.dateTimeReferenceMetaData?.timeInfo?.endTimeField,timeReference:this.dateTimeReferenceMetaData?.timeInfo?.timeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone}];for(const e of i)if(e.field===a.name){const t=this.convertToIANA(e.timeReference,e.isunknown);return this._fieldTimeZoneIndex[a.name]=t,t}const n=this.convertToIANA(this.dateTimeReferenceMetaData?.dateFieldsTimeReference,this.dateTimeReferenceMetaData?.datesInUnknownTimezone);return this._fieldTimeZoneIndex[a.name]=n,n}convertToIANA(e,a){return a?"unknown":function(e){return e?.timeZoneIANA?e?.timeZoneIANA:e?.timeZone?(0,n.G)(e,""):""}(e)}get layerPreferredTimeZone(){if(null!==this._ianaPreferred)return this._ianaPreferred;this._ianaPreferred="";const e=this.dateTimeReferenceMetaData?.preferredTimeReference;return this._ianaPreferred=this.convertToIANA(e,!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone),this._ianaPreferred}get layerTimeInfoTimeZone(){if(null!==this._ianaTimeInfo)return this._ianaTimeInfo;this._ianaTimeInfo="";const e=this.dateTimeReferenceMetaData?.timeInfo?.timeReference;return this._ianaTimeInfo=this.convertToIANA(e,!1),this._ianaTimeInfo}get layerEditFieldsTimeZone(){if(null!==this._ianaEditFields)return this._ianaEditFields;this._ianaEditFields="";const e=this.dateTimeReferenceMetaData?.editFieldsInfo?.dateFieldsTimeReference;return this._ianaEditFields=this.convertToIANA(e,this.dateTimeReferenceMetaData?.datesInUnknownTimezone),this._ianaEditFields}get layerDateFieldsTimeZone(){if(null!==this._ianaLayerDateFields)return this._ianaLayerDateFields;this._ianaLayerDateFields="";const e=this.dateTimeReferenceMetaData?.dateFieldsTimeReference;return this._ianaLayerDateFields=this.convertToIANA(e,!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone),this._ianaLayerDateFields}}},2368:(e,a,t)=>{t.d(a,{J:()=>m,j:()=>c});var i=t(43697),n=t(15923),r=(t(80442),t(22974)),s=(t(92604),t(70586)),o=t(31263),d=t(1153),l=t(52011);const m=e=>{let a=class extends e{clone(){const e=(0,s.s3)((0,d.vw)(this),"unable to clone instance of non-accessor class"),a=e.metadatas,t=e.store,i={},n=new Map;for(const e in a){const s=a[e],d=t?.originOf(e),l=s.clonable;if(s.readOnly||!1===l||d!==o.s3.USER&&d!==o.s3.DEFAULTS&&d!==o.s3.WEB_MAP&&d!==o.s3.WEB_SCENE)continue;const m=this[e];let c=null;c="function"==typeof l?l(m):"reference"===l?m:(0,r.Vo)(m),null!=m&&null==c||(d===o.s3.DEFAULTS?n.set(e,c):i[e]=c)}const l=new(0,Object.getPrototypeOf(this).constructor)(i);if(n.size){const e=(0,d.vw)(l)?.store;if(e)for(const[a,t]of n)e.set(a,t,o.s3.DEFAULTS)}return l}};return a=(0,i._)([(0,l.j)("esri.core.Clonable")],a),a};let c=class extends(m(n.Z)){};c=(0,i._)([(0,l.j)("esri.core.Clonable")],c)},1231:(e,a,t)=>{t.d(a,{Z:()=>S});var i,n=t(43697),r=t(35454),s=t(96674),o=t(5600),d=t(75215),l=(t(67676),t(36030)),m=t(71715),c=t(52011),u=t(72729),T=t(86719);const h=new r.X({binary:"binary",coordinate:"coordinate",countOrAmount:"count-or-amount",dateAndTime:"date-and-time",description:"description",locationOrPlaceName:"location-or-place-name",measurement:"measurement",nameOrTitle:"name-or-title",none:"none",orderedOrRanked:"ordered-or-ranked",percentageOrRatio:"percentage-or-ratio",typeOrCategory:"type-or-category",uniqueIdentifier:"unique-identifier"});let f=i=class extends s.wq{constructor(e){super(e),this.alias=null,this.defaultValue=void 0,this.description=null,this.domain=null,this.editable=!0,this.length=-1,this.name=null,this.nullable=!0,this.type=null,this.valueType=null,this.visible=!0}readDescription(e,{description:a}){let t=null;try{t=a?JSON.parse(a):null}catch(e){}return t?.value??null}readValueType(e,{description:a}){let t=null;try{t=a?JSON.parse(a):null}catch(e){}return t?h.fromJSON(t.fieldValueType):null}clone(){return new i({alias:this.alias,defaultValue:this.defaultValue,description:this.description,domain:this.domain&&this.domain.clone()||null,editable:this.editable,length:this.length,name:this.name,nullable:this.nullable,type:this.type,valueType:this.valueType,visible:this.visible})}};(0,n._)([(0,o.Cb)({type:String,json:{write:!0}})],f.prototype,"alias",void 0),(0,n._)([(0,o.Cb)({type:[String,Number],json:{write:{allowNull:!0}}})],f.prototype,"defaultValue",void 0),(0,n._)([(0,o.Cb)()],f.prototype,"description",void 0),(0,n._)([(0,m.r)("description")],f.prototype,"readDescription",null),(0,n._)([(0,o.Cb)({types:u.V5,json:{read:{reader:u.im},write:!0}})],f.prototype,"domain",void 0),(0,n._)([(0,o.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"editable",void 0),(0,n._)([(0,o.Cb)({type:d.z8,json:{write:!0}})],f.prototype,"length",void 0),(0,n._)([(0,o.Cb)({type:String,json:{write:!0}})],f.prototype,"name",void 0),(0,n._)([(0,o.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"nullable",void 0),(0,n._)([(0,l.J)(T.v)],f.prototype,"type",void 0),(0,n._)([(0,o.Cb)()],f.prototype,"valueType",void 0),(0,n._)([(0,m.r)("valueType",["description"])],f.prototype,"readValueType",null),(0,n._)([(0,o.Cb)({type:Boolean,json:{read:!1}})],f.prototype,"visible",void 0),f=i=(0,n._)([(0,c.j)("esri.layers.support.Field")],f);const S=f},72729:(e,a,t)=>{t.d(a,{im:()=>k,V5:()=>C}),t(80442);var i,n=t(43697),r=t(22974),s=t(5600),o=(t(75215),t(36030)),d=t(52011),l=t(96674);t(67676);let m=i=class extends l.wq{constructor(e){super(e),this.name=null,this.code=null}clone(){return new i({name:this.name,code:this.code})}};(0,n._)([(0,s.Cb)({type:String,json:{write:!0}})],m.prototype,"name",void 0),(0,n._)([(0,s.Cb)({type:[String,Number],json:{write:!0}})],m.prototype,"code",void 0),m=i=(0,n._)([(0,d.j)("esri.layers.support.CodedValue")],m);const c=new(t(35454).X)({inherited:"inherited",codedValue:"coded-value",range:"range"});let u=class extends l.wq{constructor(e){super(e),this.name=null,this.type=null}};(0,n._)([(0,s.Cb)({type:String,json:{write:!0}})],u.prototype,"name",void 0),(0,n._)([(0,o.J)(c)],u.prototype,"type",void 0),u=(0,n._)([(0,d.j)("esri.layers.support.Domain")],u);const T=u;var h;let f=h=class extends T{constructor(e){super(e),this.codedValues=null,this.type="coded-value"}getName(e){let a=null;if(this.codedValues){const t=String(e);this.codedValues.some((e=>(String(e.code)===t&&(a=e.name),!!a)))}return a}clone(){return new h({codedValues:(0,r.d9)(this.codedValues),name:this.name})}};(0,n._)([(0,s.Cb)({type:[m],json:{write:!0}})],f.prototype,"codedValues",void 0),(0,n._)([(0,o.J)({codedValue:"coded-value"})],f.prototype,"type",void 0),f=h=(0,n._)([(0,d.j)("esri.layers.support.CodedValueDomain")],f);const S=f;var p;t(92604),t(20102);let A=p=class extends T{constructor(e){super(e),this.type="inherited"}clone(){return new p}};(0,n._)([(0,o.J)({inherited:"inherited"})],A.prototype,"type",void 0),A=p=(0,n._)([(0,d.j)("esri.layers.support.InheritedDomain")],A);const y=A;var g;let _=g=class extends T{constructor(e){super(e),this.maxValue=null,this.minValue=null,this.type="range"}clone(){return new g({maxValue:this.maxValue,minValue:this.minValue,name:this.name})}};(0,n._)([(0,s.Cb)({type:Number,json:{type:[Number],read:{source:"range",reader:(e,a)=>a.range&&a.range[1]},write:{enabled:!1,overridePolicy(){return{enabled:null!=this.maxValue&&null==this.minValue}},target:"range",writer(e,a,t){a[t]=[this.minValue||0,e]}}}})],_.prototype,"maxValue",void 0),(0,n._)([(0,s.Cb)({type:Number,json:{type:[Number],read:{source:"range",reader:(e,a)=>a.range&&a.range[0]},write:{target:"range",writer(e,a,t){a[t]=[e,this.maxValue||0]}}}})],_.prototype,"minValue",void 0),(0,n._)([(0,o.J)({range:"range"})],_.prototype,"type",void 0),_=g=(0,n._)([(0,d.j)("esri.layers.support.RangeDomain")],_);const w=_,C={key:"type",base:T,typeMap:{range:_,"coded-value":S,inherited:y}};function k(e){if(!e||!e.type)return null;switch(e.type){case"range":return w.fromJSON(e);case"codedValue":return S.fromJSON(e);case"inherited":return y.fromJSON(e)}return null}},86719:(e,a,t)=>{t.d(a,{v:()=>i});const i=new(t(35454).X)({esriFieldTypeSmallInteger:"small-integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"single",esriFieldTypeDouble:"double",esriFieldTypeLong:"long",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"oid",esriFieldTypeGeometry:"geometry",esriFieldTypeBlob:"blob",esriFieldTypeRaster:"raster",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"global-id",esriFieldTypeXML:"xml"})},80216:(e,a,t)=>{t.d(a,{Z:()=>T});var i=t(43697),n=t(2368),r=t(96674),s=t(70586),o=t(5600),d=(t(75215),t(67676),t(71715)),l=t(52011),m=t(30556),c=t(42799);let u=class extends((0,n.J)(r.wq)){constructor(e){super(e),this.legacy=null,this.timeZone="system"}readLegacy(e,a){const{timeZone:t,respectsDaylightSaving:i}=a;return{timeZone:t,respectsDaylightSaving:i}}readTimeZone(e,a){return"timeZoneIANA"in a?a.timeZoneIANA:(0,c.G)(a)}writeTimeZone(e,a){a.timeZoneIANA=e}equals(e){return!((0,s.Wi)(e)||(0,s.Wi)(this.timeZone)||(0,s.Wi)(e.timeZone))&&this.timeZone===e.timeZone}};(0,i._)([(0,o.Cb)()],u.prototype,"legacy",void 0),(0,i._)([(0,d.r)("legacy",["timeZone"])],u.prototype,"readLegacy",null),(0,i._)([(0,o.Cb)({type:String,nonNullable:!0})],u.prototype,"timeZone",void 0),(0,i._)([(0,d.r)("timeZone",["timeZone","timeZoneIANA","respectsDaylightSaving"])],u.prototype,"readTimeZone",null),(0,i._)([(0,m.c)("timeZone")],u.prototype,"writeTimeZone",null),u=(0,i._)([(0,l.j)("esri.time.TimeReference")],u);const T=u},42799:(e,a,t)=>{t.d(a,{G:()=>r});const i=new Map([["AUS Central Standard Time","Australia/Darwin"],["AUS Eastern Standard Time","Australia/Sydney"],["Afghanistan Standard Time","Asia/Kabul"],["Alaskan Standard Time","America/Anchorage"],["Aleutian Standard Time","America/Adak"],["Altai Standard Time","Asia/Barnaul"],["Arab Standard Time","Asia/Riyadh"],["Arabian Standard Time","Asia/Dubai"],["Arabic Standard Time","Asia/Baghdad"],["Argentina Standard Time","America/Buenos_Aires"],["Astrakhan Standard Time","Europe/Astrakhan"],["Atlantic Standard Time","America/Halifax"],["Aus Central W. Standard Time","Australia/Eucla"],["Azerbaijan Standard Time","Asia/Baku"],["Azores Standard Time","Atlantic/Azores"],["Bahia Standard Time","America/Bahia"],["Bangladesh Standard Time","Asia/Dhaka"],["Belarus Standard Time","Europe/Minsk"],["Bougainville Standard Time","Pacific/Bougainville"],["Canada Central Standard Time","America/Regina"],["Cape Verde Standard Time","Atlantic/Cape_Verde"],["Caucasus Standard Time","Asia/Yerevan"],["Cen. Australia Standard Time","Australia/Adelaide"],["Central America Standard Time","America/Guatemala"],["Central Asia Standard Time","Asia/Almaty"],["Central Brazilian Standard Time","America/Cuiaba"],["Central Europe Standard Time","Europe/Budapest"],["Central European Standard Time","Europe/Warsaw"],["Central Pacific Standard Time","Pacific/Guadalcanal"],["Central Standard Time","America/Chicago"],["Central Standard Time (Mexico)","America/Mexico_City"],["Chatham Islands Standard Time","Pacific/Chatham"],["China Standard Time","Asia/Shanghai"],["Cuba Standard Time","America/Havana"],["Dateline Standard Time","Etc/GMT+12"],["E. Africa Standard Time","Africa/Nairobi"],["E. Australia Standard Time","Australia/Brisbane"],["E. Europe Standard Time","Europe/Chisinau"],["E. South America Standard Time","America/Sao_Paulo"],["Easter Island Standard Time","Pacific/Easter"],["Eastern Standard Time","America/New_York"],["Eastern Standard Time (Mexico)","America/Cancun"],["Egypt Standard Time","Africa/Cairo"],["Ekaterinburg Standard Time","Asia/Yekaterinburg"],["FLE Standard Time","Europe/Kiev"],["Fiji Standard Time","Pacific/Fiji"],["GMT Standard Time","Europe/London"],["GTB Standard Time","Europe/Bucharest"],["Georgian Standard Time","Asia/Tbilisi"],["Greenland Standard Time","America/Godthab"],["Greenwich Standard Time","Atlantic/Reykjavik"],["Haiti Standard Time","America/Port-au-Prince"],["Hawaiian Standard Time","Pacific/Honolulu"],["India Standard Time","Asia/Calcutta"],["Iran Standard Time","Asia/Tehran"],["Israel Standard Time","Asia/Jerusalem"],["Jordan Standard Time","Asia/Amman"],["Kaliningrad Standard Time","Europe/Kaliningrad"],["Korea Standard Time","Asia/Seoul"],["Libya Standard Time","Africa/Tripoli"],["Line Islands Standard Time","Pacific/Kiritimati"],["Lord Howe Standard Time","Australia/Lord_Howe"],["Magadan Standard Time","Asia/Magadan"],["Magallanes Standard Time","America/Punta_Arenas"],["Marquesas Standard Time","Pacific/Marquesas"],["Mauritius Standard Time","Indian/Mauritius"],["Middle East Standard Time","Asia/Beirut"],["Montevideo Standard Time","America/Montevideo"],["Morocco Standard Time","Africa/Casablanca"],["Mountain Standard Time","America/Denver"],["Mountain Standard Time (Mexico)","America/Chihuahua"],["Myanmar Standard Time","Asia/Rangoon"],["N. Central Asia Standard Time","Asia/Novosibirsk"],["Namibia Standard Time","Africa/Windhoek"],["Nepal Standard Time","Asia/Katmandu"],["New Zealand Standard Time","Pacific/Auckland"],["Newfoundland Standard Time","America/St_Johns"],["Norfolk Standard Time","Pacific/Norfolk"],["North Asia East Standard Time","Asia/Irkutsk"],["North Asia Standard Time","Asia/Krasnoyarsk"],["North Korea Standard Time","Asia/Pyongyang"],["Omsk Standard Time","Asia/Omsk"],["Pacific SA Standard Time","America/Santiago"],["Pacific Standard Time","America/Los_Angeles"],["Pacific Standard Time (Mexico)","America/Tijuana"],["Pakistan Standard Time","Asia/Karachi"],["Paraguay Standard Time","America/Asuncion"],["Qyzylorda Standard Time","Asia/Qyzylorda"],["Romance Standard Time","Europe/Paris"],["Russia Time Zone 10","Asia/Srednekolymsk"],["Russia Time Zone 11","Asia/Kamchatka"],["Russia Time Zone 3","Europe/Samara"],["Russian Standard Time","Europe/Moscow"],["SA Eastern Standard Time","America/Cayenne"],["SA Pacific Standard Time","America/Bogota"],["SA Western Standard Time","America/La_Paz"],["SE Asia Standard Time","Asia/Bangkok"],["Saint Pierre Standard Time","America/Miquelon"],["Sakhalin Standard Time","Asia/Sakhalin"],["Samoa Standard Time","Pacific/Apia"],["Sao Tome Standard Time","Africa/Sao_Tome"],["Saratov Standard Time","Europe/Saratov"],["Singapore Standard Time","Asia/Singapore"],["South Africa Standard Time","Africa/Johannesburg"],["South Sudan Standard Time","Africa/Juba"],["Sri Lanka Standard Time","Asia/Colombo"],["Sudan Standard Time","Africa/Khartoum"],["Syria Standard Time","Asia/Damascus"],["Taipei Standard Time","Asia/Taipei"],["Tasmania Standard Time","Australia/Hobart"],["Tocantins Standard Time","America/Araguaina"],["Tokyo Standard Time","Asia/Tokyo"],["Tomsk Standard Time","Asia/Tomsk"],["Tonga Standard Time","Pacific/Tongatapu"],["Transbaikal Standard Time","Asia/Chita"],["Turkey Standard Time","Europe/Istanbul"],["Turks And Caicos Standard Time","America/Grand_Turk"],["US Eastern Standard Time","America/Indianapolis"],["US Mountain Standard Time","America/Phoenix"],["UTC","Etc/GMT"],["UTC+01","Etc/GMT-1"],["UTC+02","Etc/GMT-2"],["UTC+03","Etc/GMT-3"],["UTC+04","Etc/GMT-4"],["UTC+05","Etc/GMT-5"],["UTC+06","Etc/GMT-6"],["UTC+07","Etc/GMT-7"],["UTC+08","Etc/GMT-8"],["UTC+09","Etc/GMT-9"],["UTC+10","Etc/GMT-10"],["UTC+11","Etc/GMT-11"],["UTC+12","Etc/GMT-12"],["UTC+13","Etc/GMT-13"],["UTC+14","Etc/GMT-14"],["UTC-01","Etc/GMT+1"],["UTC-02","Etc/GMT+2"],["UTC-03","Etc/GMT+3"],["UTC-04","Etc/GMT+4"],["UTC-05","Etc/GMT+5"],["UTC-06","Etc/GMT+6"],["UTC-07","Etc/GMT+7"],["UTC-08","Etc/GMT+8"],["UTC-09","Etc/GMT+9"],["UTC-10","Etc/GMT+10"],["UTC-11","Etc/GMT+11"],["UTC-12","Etc/GMT+12"],["Ulaanbaatar Standard Time","Asia/Ulaanbaatar"],["Venezuela Standard Time","America/Caracas"],["Vladivostok Standard Time","Asia/Vladivostok"],["Volgograd Standard Time","Europe/Volgograd"],["W. Australia Standard Time","Australia/Perth"],["W. Central Africa Standard Time","Africa/Lagos"],["W. Europe Standard Time","Europe/Berlin"],["W. Mongolia Standard Time","Asia/Hovd"],["West Asia Standard Time","Asia/Tashkent"],["West Bank Standard Time","Asia/Hebron"],["West Pacific Standard Time","Pacific/Port_Moresby"],["Yakutsk Standard Time","Asia/Yakutsk"],["Yukon Standard Time","America/Whitehorse"]]);var n=t(17126);function r(e,a="system"){if(!e||!i.has(e.timeZone))return a;const t=i.get(e.timeZone);return e.timeZone.startsWith("UTC")||e.respectsDaylightSaving?t:function(e){const a=n.ou.local().setZone(e),t=Math.min(a.set({month:1,day:1}).offset,a.set({month:5}).offset);return 0===t?"Etc/UTC":`Etc/GMT${n.Qf.instance(-t).formatOffset(0,"narrow")}`}(t)}}}]);