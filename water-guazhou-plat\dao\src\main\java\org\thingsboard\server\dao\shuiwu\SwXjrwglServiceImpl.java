package org.thingsboard.server.dao.shuiwu;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.*;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.shuiwu.SwXjrwglCRepository;
import org.thingsboard.server.dao.sql.shuiwu.SwXjrwglMRepository;
import org.thingsboard.server.dao.user.UserService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class SwXjrwglServiceImpl implements SwXjrwglService {

    @Autowired
    private SwXjrwglMRepository xjrwglMRepository;
    @Autowired
    private SwXjrwglCRepository xjrwglCRepository;
    @Autowired
    private SwXjrwService xjrwService;
    @Autowired
    private CriterionService criterionService;
    @Autowired
    private UserService userService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private PipeService pipeService;
    @Autowired
    private AssetsAccountService assetsAccountService;

    @Override
    public void save(SwXjrwglMEntity entity, User currentUser) {
        List<SwXjrwglCEntity> jobList = entity.getJobList();
        // 保存主表数据
        if (StringUtils.isBlank(entity.getId())) {
            entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
            entity.setCreateTime(new Date());
            entity.setIsDel("0");// 未删除
            entity.setCreator(currentUser.getFirstName());
        } else {
            // 删除子表记录, 重新新增
            xjrwglCRepository.deleteByMainId(entity.getId());
        }

        entity = xjrwglMRepository.save(entity);

        // 保存子表记录
        for (SwXjrwglCEntity cEntity : jobList) {
            cEntity.setMainId(entity.getId());
            cEntity.setCreateTime(entity.getCreateTime());
            cEntity.setTenantId(entity.getTenantId());
        }
        xjrwglCRepository.save(jobList);

        try {
            entity.setCreate(true);
            this.execute(entity);
        } catch (Exception e) {
            log.error("生成巡检任务失败! 巡检计划ID = {}", entity.getId());
        }
    }

    @Override
    public SwXjrwglMEntity getXjrwglM(String id) {
        // 查询主表
        SwXjrwglMEntity entity = xjrwglMRepository.findOne(id);
        TenantId tenantId = new TenantId(UUIDConverter.fromString(entity.getTenantId()));

        // 管道列表
        List<PipeEntity> pipeList = pipeService.findAll(tenantId);
        Map<String, PipeEntity> pipeMap = new HashMap<>();
        pipeList.forEach(pipe -> pipeMap.put(pipe.getId(), pipe));

        // 标准
        List<CriterionEntity> criterionList = criterionService.findAll(UUIDConverter.fromTimeUUID(tenantId.getId()));
        Map<String, CriterionEntity> criterionMap = new HashMap<>();
        criterionList.forEach(criterion -> {
            criterionMap.put(criterion.getId(), criterion);
        });

        // 设备列表
        List<AssetsAccountEntity> deviceList = assetsAccountService.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        Map<String, AssetsAccountEntity> deviceMap = new HashMap<>();// 水表
        deviceList.forEach(device -> {
            deviceMap.put(device.getId(), device);
        });

        // 项目列表
        List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
        Map<String, ProjectEntity> projectMap = new HashMap<>();
        projectList.forEach(project -> projectMap.put(project.getId(), project));

        List<User> userList = userService.findUserByTenant(tenantId);
        Map<String, User> userMap = new HashMap<>();
        if (userList != null) {
            userList.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));
        }
        String users = entity.getUsers();
        StringBuilder usernames = new StringBuilder();
        if (StringUtils.isNotBlank(users)) {
            String[] usersArray = users.split(",");
            for (int i = 0; i < usersArray.length; i++) {
                User user = userMap.get(usersArray[i]);
                if (user == null) {
                    continue;
                }
                if (i < usersArray.length - 1) {
                    usernames.append(user.getFirstName()).append(",");
                } else {
                    usernames.append(user.getFirstName());
                }
            }
        }
        entity.setUserNames(usernames.toString());

        // 查询子表
        List<SwXjrwglCEntity> list = xjrwglCRepository.findByMainIdOrderByOrderNum(id);
        for (SwXjrwglCEntity child : list) {
            // 设备名
            String deviceType = child.getDeviceType();
            if ("1".equals(deviceType)) {// 管道设备
                PipeEntity pipe = pipeMap.get(child.getDeviceId());
                if (pipe != null) {
                    child.setDeviceName(pipe.getName());
                }
            } else {// 物联网水表
                AssetsAccountEntity device = deviceMap.get(child.getDeviceId());
                if (device != null) {
                    child.setDeviceName(device.getDeviceName());
                }
            }
            String criterionId = child.getCriterionId();
            CriterionEntity criterion = criterionMap.get(criterionId);
            if (criterion != null) {
                child.setCriterionName(criterion.getName());
            }
            // 所在项目
            ProjectEntity project = projectMap.get(child.getProjectId());
            if (project != null) {
                child.setProjectName(project.getName());
            }
        }

        entity.setJobList(list);

        return entity;
    }

    @Override
    public PageData<SwXjrwglMEntity> findList(int page, int size, String content, String status, String deviceId, TenantId tenantId) {
        // 分页
        PageRequest pageable = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "createTime"));

        // 状态, 启用/停用
        List<String> statusList = new ArrayList<>();
        if (StringUtils.isNotBlank(status)) {
            statusList.addAll(Arrays.asList(status.split(",")));
        } else {
            statusList.add("1");// 启用
            statusList.add("2");// 停用
        }

        Page<SwXjrwglMEntity> pageResult = xjrwglMRepository.findList(content, statusList, UUIDConverter.fromTimeUUID(tenantId.getId()), deviceId, pageable);

        List<SwXjrwglMEntity> list = pageResult.getContent();

        List<User> userList = userService.findUserByTenant(tenantId);
        Map<String, User> userMap = new HashMap<>();
        if (userList != null) {
            userList.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));
        }

        for (SwXjrwglMEntity xjrwgl : list) {
            String users = xjrwgl.getUsers();
            StringBuilder usernames = new StringBuilder();
            if (StringUtils.isNotBlank(users)) {
                String[] usersArray = users.split(",");
                for (int i = 0; i < usersArray.length; i++) {
                    User user = userMap.get(usersArray[i]);
                    if (user == null) {
                        continue;
                    }
                    if (i < usersArray.length - 1) {
                        usernames.append(user.getFirstName()).append(",");
                    } else {
                        usernames.append(user.getFirstName());
                    }
                }
            }
            xjrwgl.setUserNames(usernames.toString());
        }


        return new PageData<>(pageResult.getTotalElements(), list);
    }

    @Override
    public void deleteById(String id) {
        // 查询
        SwXjrwglMEntity entity = xjrwglMRepository.findOne(id);
        if (entity == null) {
            return;
        }
        entity.setIsDel("1");
        xjrwglMRepository.save(entity);
    }

    @Override
    public List<SwXjrwglMEntity> findAll() {
        return xjrwglMRepository.findAllByIsDel("0");
    }

    /**
     * 根据巡检计划生成巡检任务
     *
     * @param xjrwgl 巡检任务计划
     */
    @Override
    public void execute(SwXjrwglMEntity xjrwgl) throws Exception {
        // 周期, 单位: 天
        Integer periodTime = xjrwgl.getPeriodTime();

        Date executeTime = xjrwgl.getExecuteTime();
        if (executeTime == null) {
            return;
        }
        Date nextExecuteTime = null;
        if (xjrwgl.isCreate()) {// 新建的下一次任务时间为执行时间
            nextExecuteTime = executeTime;
        } else {
            if (periodTime == null || periodTime == 0) {
                nextExecuteTime = executeTime;
            } else {
                nextExecuteTime = new Date(executeTime.getTime() + (periodTime * 24 * 60 * 60 * 1000));
            }
        }

        boolean result = checkTimeIsToday(nextExecuteTime);

        if (!result) {// 不在今天
            return;
        }

        // 查询巡检设置子表内容
        List<SwXjrwglCEntity> xjrwcList = this.xjrwglCRepository.findByMainIdOrderByOrderNum(xjrwgl.getId());

        if (periodTime == null || periodTime == 0) {
            // 一次性任务, 删除巡检任务设置
            xjrwgl.setIsDel("1");

        } else {// 周期任务, 修改执行时间
            xjrwgl.setExecuteTime(nextExecuteTime);
        }

        // 生成巡检任务
        SwXjrwMEntity xjrw = saveXJRW(xjrwgl, xjrwcList);

    }

    @Override
    public void changeStatus(JSONObject params) {
        String id = params.getString("id");
        String status = params.getString("status");
        if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(status)) {
            SwXjrwglMEntity entity = xjrwglMRepository.findOne(id);
            if (entity != null && !entity.getStatus().equals(status)) {
                entity.setStatus(status);
                xjrwglMRepository.save(entity);
            }
        }
    }

    @Override
    public void deleteById(List<String> ids) {
        for (String id : ids) {
            this.deleteById(id);
        }
    }

    /**
     * 保存巡检任务
     *
     *  @param xjrwgl      巡检任务管理
     * @param xjrwglcList 巡检任务子表数据
     */
    private SwXjrwMEntity saveXJRW(SwXjrwglMEntity xjrwgl, List<SwXjrwglCEntity> xjrwglcList) {
        // 巡检任务主表
        SwXjrwMEntity xjrwmEntity = SwXjrwMEntity.buildXJRWM(xjrwgl);
        // 新建
        xjrwmEntity.setStatus("0");
        xjrwmEntity.setCreateTime(new Date());
        xjrwmEntity.setTenantId(xjrwgl.getTenantId());

        xjrwService.saveXJRWM(xjrwmEntity);
        // 巡检任务子表
        if (xjrwglcList != null && !xjrwglcList.isEmpty()) {
            // 查询巡检标准库
            List<CriterionEntity> criterionEntityList = criterionService.findAll(xjrwgl.getTenantId());
            Map<String, CriterionEntity> criterionMap = new HashMap<>();
            criterionEntityList.forEach(entity -> criterionMap.put(entity.getId(), entity));

            List<SwXjrwCEntity> xjrwcEntityList = new ArrayList<>();
            for (SwXjrwglCEntity xjrwglc : xjrwglcList) {
                SwXjrwCEntity xjrwcEntity = SwXjrwCEntity.buildXJRWC(xjrwglc);
                xjrwcEntity.setMainId(xjrwmEntity.getId());

                // 设置标准库详情
                CriterionEntity criterionEntity = criterionMap.get(xjrwglc.getCriterionId());
                if (criterionEntity != null) {
                    xjrwcEntity.setCriterionName(criterionEntity.getName());
                    xjrwcEntity.setCriterionDetail(criterionEntity.getDetail());
                }

                xjrwcEntity.setStatus("0");

                xjrwcEntityList.add(xjrwcEntity);
            }
            xjrwService.save(xjrwcEntityList);
        }

        return xjrwmEntity;

    }

    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }
}
