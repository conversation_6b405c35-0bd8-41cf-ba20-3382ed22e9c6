import{d as _,c as f,r as y,b as n,Q as I,g as k,h as C,F,q as L,i as u,_ as x,X as M}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as P}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import S from"./RightDrawerMap-D5PhmGFO.js";import{a as q}from"./pipeCheck-BaGB4XFi.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Qt=_({__name:"IsolatedPoint",setup(B){const a=f(),d=f(),o=y({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1}),l={queryParams:{geometry:void 0,where:"1=1"}},c=y({group:[{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,label:"选择图层",showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(r,t)=>{t&&a.value&&(a.value.dataForm.layerid=[r.value])}},{type:"btn-group",btns:[{perm:!0,text:()=>o.loading?"正在检查，过程稍长，请耐心等待！":"检查",styles:{width:"100%"},loading:()=>o.loading,click:()=>h()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),g=async()=>{var p,s,m;o.layerIds=P(l.view);const r=await M(o.layerIds);o.layerInfos=((s=(p=r.data)==null?void 0:p.result)==null?void 0:s.rows)||[];const t=(m=c.group.find(i=>i.id==="layer"))==null?void 0:m.fields[0],e=o.layerInfos.filter(i=>i.geometrytype==="esriGeometryPoint").map(i=>({label:i.layername,value:i.layerid,data:i}));t&&(t.options=[{label:"管点类",value:-1,children:e,disabled:!0}]),e.length===1&&a.value&&(a.value.dataForm.layerid=e.map(i=>i.value))},h=async()=>{var t,e,p,s;n.info("正在检查，请稍候...");const r=(t=a.value)==null?void 0:t.dataForm.layerid[0];if(r===void 0){n.warning("请先选择一个图层");return}o.loading=!0;try{o.loading=!0,o.tabs.length=0;const m=(e=o.layerInfos.find(w=>w.layerid===r))==null?void 0:e.layername,i=await q({layer:m});(p=i.data.result)!=null&&p.length||n.success("没有相关数据");const b=[{label:m,name:m,data:i.data.result,layerid:r}];await((s=d.value)==null?void 0:s.refreshDetail(b))}catch(m){console.log(m),o.loading=!1,n.error("检查失败")}},v=r=>{l.view=r,g()};return I(()=>{var r,t,e;(r=l.graphicsLayer)==null||r.removeAll(),(t=l.drawAction)==null||t.destroy(),(e=l.drawer)==null||e.destroy()}),(r,t)=>{const e=x;return k(),C(S,{ref_key:"refMap",ref:d,title:"孤立点检查",onMapLoaded:v,onDetailRefreshed:t[0]||(t[0]=p=>u(o).loading=!1)},{default:F(()=>[L(e,{ref_key:"refForm",ref:a,config:u(c)},null,8,["config"])]),_:1},512)}}});export{Qt as default};
