package org.thingsboard.server.dao.util.imodel.response.cache;

import org.apache.ibatis.cache.decorators.LruCache;
import org.apache.ibatis.cache.decorators.WeakCache;
import org.apache.ibatis.cache.impl.PerpetualCache;

import java.util.EnumMap;
import java.util.Map;
import java.util.function.BiConsumer;

public class CacheGroup {
    private final Map<CacheType, Cache<?, ?>> cacheMap = new EnumMap<>(CacheType.class);

    public <TKey, TVal> Cache<TKey, TVal> get(CacheType type) {
        return get(type, null);
    }

    @SuppressWarnings("unchecked")
    public <TKey, TVal> Cache<TKey, TVal> get(CacheType type, BiConsumer<TKey, TVal> removeDetector) {
        return (Cache<TKey, TVal>) cacheMap.computeIfAbsent(type, cacheType -> {
            PerpetualCache cache = new PerpetualCache(type.name());
            MybatisOnRemoveDetector removeDetectCache = new MybatisOnRemoveDetector(cache, removeDetector);
            LruCache lruCache = new LruCache(removeDetectCache);
            lruCache.setSize(type.getSize());
            WeakCache weakCache = new WeakCache(lruCache);
            return new MybatisCacheAdapter<>(weakCache);
        });
    }
}
