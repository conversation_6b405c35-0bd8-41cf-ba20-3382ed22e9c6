<!-- 库存信息 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        :tree-data="TreeData"
      />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { getDeviceStorageJournalEq } from '@/api/equipment_assets/ledgerManagement'
import { getDeviceTypeTree } from '@/api/equipment_assets/equipmentManage'
import {
  getstoreSerch,
  getGoodsShelfSerch
} from '@/api/equipment_assets/equipmentOutStock'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refForm = ref<ISLDrawerIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备编码', field: 'serialId', type: 'input' },
    { label: '设备名称', field: 'name', type: 'input' },
    { label: '设备型号', field: 'model', type: 'input' },
    { label: '仓库名称',
      field: 'storeId',
      type: 'select-tree',
      checkStrictly: true,
      autoFillOptions: config => {
        getstoreSerch({ page: 1, size: 99999 }).then(res => {
          config.options = traverse(res.data.data.data || [])
        })
      },
      onChange: row => data.getGoodsShelfValue(row) },
    { label: '货架', field: 'shelvesId', type: 'select-tree', checkStrictly: true, options: computed(() => data.GoodsShelf)as any }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ] })

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '设备编码', prop: 'serialId' },
    { label: '设备名称', prop: 'name' },
    { label: '设备型号', prop: 'model' },
    { label: '所属大类', prop: 'topType' },
    { label: '所属类别', prop: 'type' },
    { label: '仓库', prop: 'storehouseName' },
    { label: '货架', prop: 'shelvesName' },
    // { label: '入库批次', prop: 'remark' },
    { label: '当前在库量', prop: 'count' }
    // { label: '最小在库量', prop: 'remark' }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      text: '查看标签',
      icon: ICONS.DETAIL,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'drive',
          config: {
            height: '600px',
            indexVisible: true,
            dataList: computed(() => data.deviceList) as any,
            columns: [{
              label: '标签编码',
              prop: 'deviceLabelCode'
            },
            {
              label: '入库单编号',
              prop: 'storeInCode'
            },
            {
              label: '供应商',
              prop: 'supplierName'
            },
            {
              label: '仓库编码',
              prop: 'storehouseCode'
            },
            {
              label: '仓库名称',
              prop: 'storehouseName'
            },
            {
              label: '货架编码',
              prop: 'shelvesCode'
            },
            {
              label: '货架名称',
              prop: 'shelvesName'
            }],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})
const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    TreeData.currentProject = data
    refreshData()
  }
})

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '详情'
  data.deviceList = row.restDeviceInfos
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDrawer()
}

const data = reactive({
  // 设备列表
  deviceList: [],
  // 货架
  GoodsShelf: [],

  // 获取货架
  getGoodsShelfValue: row => {
    const params = { page: 1, size: 99999, id: row }
    getGoodsShelfSerch(params).then(res => {
      data.GoodsShelf = traverse(res.data.data.data || [])
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    inStoreOnly: true,
    deviceTypeId: TreeData.currentProject.id,
    ...(refSearch.value?.queryParams || {})
  }

  getDeviceStorageJournalEq(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    TreeData.currentProject = res.data.data[0]
    refreshData()
  })
}

onMounted(async () => {
  init()
})

</script>
