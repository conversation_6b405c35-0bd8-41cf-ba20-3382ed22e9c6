<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getPipeNetworkConfigList, 
  addPipeNetworkConfig, 
  editPipeNetworkConfig, 
  deletePipeNetworkConfig,
  getPipeNetworkConfigDetail 
} from '@/api/platformManagement/pipeNetworkConfig'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '管网ID', 
      field: 'pipeId', 
      placeholder: '请输入管网ID',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '管网ID', prop: 'pipeId' },
    { label: '运行规则', prop: 'rule' },
    { label: '报警阈值', prop: 'alarmThreshold' },
    { label: '备注', prop: 'remark' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增管网配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '管网ID',
          field: 'pipeId',
          rules: [{ required: true, message: '请输入管网ID' }]
        },
        {
          type: 'input',
          label: '运行规则',
          field: 'rule',
          rules: [{ required: true, message: '请输入运行规则' }]
        },
        {
          type: 'input',
          label: '报警阈值',
          field: 'alarmThreshold',
          rules: [{ required: true, message: '请输入报警阈值' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark',
          placeholder: '请输入备注信息'
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editPipeNetworkConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addPipeNetworkConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  // 重置所有表单字段为可编辑状态
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  // 恢复默认按钮配置
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  // 恢复提交函数
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editPipeNetworkConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addPipeNetworkConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
  
  // 清除自定义按钮配置
  DialogFormConfig.footerBtns = undefined
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  DialogFormConfig.title = row ? '编辑管网配置' : '新增管网配置'
  DialogFormConfig.defaultValue = { ...(row || {}) }
  refDialogForm.value?.openDialog()
}

// 查看详情
const handleDetail = async (row) => {
  // 模拟详情数据
  const mockDetailData = {
    id: row.id || '1',
    pipeId: row.pipeId || 'PIPE001',
    rule: row.rule || '正常运行规则',
    alarmThreshold: row.alarmThreshold || '80%',
    remark: row.remark || '这是管网配置的详情数据'
  }

  try {
    console.log('获取详情，行数据:', row)
    
    const res = await getPipeNetworkConfigDetail(row.id)
    console.log('详情API响应:', res)
    
    let detailData = null
    
    // 处理不同的数据结构
    if (res.data) {
      if (res.data.data) {
        detailData = res.data.data
      } else {
        detailData = res.data
      }
    } else if (res) {
      detailData = res
    }
    
    console.log('解析后的详情数据:', detailData)
    
    // 如果没有获取到详情数据，使用模拟数据
    if (!detailData) {
      console.log('使用模拟详情数据')
      detailData = mockDetailData
    }
    
    // 先重置配置
    resetDialogConfig()
    
    // 设置详情模式
    DialogFormConfig.title = '管网配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    
    console.log('设置的详情数据:', DialogFormConfig.defaultValue)
    
    // 设置为只读模式 - 对不同类型字段采用不同的禁用方式
    DialogFormConfig.group[0].fields.forEach(field => {
      if (field.type === 'select') {
        field.readonly = true
        field.disabled = true
      } else {
        field.disabled = true
      }
    })
    
    // 隐藏提交按钮，只显示关闭按钮 - 尝试多种方法
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancel = true
    DialogFormConfig.cancelText = '关闭' 
    DialogFormConfig.submitText = undefined // 设置为undefined
    DialogFormConfig.submit = undefined // 设置为undefined
    DialogFormConfig.submitting = false
    DialogFormConfig.footerBtns = [
      {
        text: '关闭',
        type: 'default',
        click: () => {
          refDialogForm.value?.closeDialog()
        }
      }
    ]
    
    console.log('详情模式DialogFormConfig配置:', {
      showSubmit: DialogFormConfig.showSubmit,
      showCancel: DialogFormConfig.showCancel,
      cancel: DialogFormConfig.cancel,
      cancelText: DialogFormConfig.cancelText,
      submitText: DialogFormConfig.submitText,
      submit: DialogFormConfig.submit,
      footerBtns: DialogFormConfig.footerBtns
    })
    
    refDialogForm.value?.openDialog()
    
  } catch (error) {
    console.error('获取详情失败:', error)
    console.log('API调用失败，使用模拟详情数据')
    
    // 先重置配置
    resetDialogConfig()
    
    // 设置详情模式
    DialogFormConfig.title = '管网配置详情'
    DialogFormConfig.defaultValue = { ...mockDetailData }
    
    // 设置为只读模式
    DialogFormConfig.group[0].fields.forEach(field => {
      if (field.type === 'select') {
        field.readonly = true
        field.disabled = true
      } else {
        field.disabled = true
      }
    })
    
    // 隐藏提交按钮，只显示关闭按钮 - 尝试多种方法
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancel = true
    DialogFormConfig.cancelText = '关闭' 
    DialogFormConfig.submitText = undefined // 设置为undefined
    DialogFormConfig.submit = undefined // 设置为undefined
    DialogFormConfig.submitting = false
    DialogFormConfig.footerBtns = [
      {
        text: '关闭',
        type: 'default',
        click: () => {
          refDialogForm.value?.closeDialog()
        }
      }
    ]
    
    console.log('详情模式DialogFormConfig配置:', {
      showSubmit: DialogFormConfig.showSubmit,
      showCancel: DialogFormConfig.showCancel,
      cancel: DialogFormConfig.cancel,
      cancelText: DialogFormConfig.cancelText,
      submitText: DialogFormConfig.submitText,
      submit: DialogFormConfig.submit,
      footerBtns: DialogFormConfig.footerBtns
    })
    
    refDialogForm.value?.openDialog()
    
    SLMessage.error('API调用失败，当前显示模拟数据')
  }
}

// 删除
const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      try {
        const ids = row
          ? [row.id]
          : TableConfig.selectList?.map(item => item.id) || []

        if (!ids.length) {
          SLMessage.warning('请选择要删除的数据')
          return
        }

        const res = await deletePipeNetworkConfig(ids)
        if (res.data) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error('删除失败')
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 刷新数据
const refreshData = async () => {
  // 模拟数据 - 用于测试显示
  const mockData = [
    {
      id: '1',
      pipeId: 'PIPE001',
      rule: '正常运行规则',
      alarmThreshold: '80%',
      remark: '主要供水管网配置'
    },
    {
      id: '2', 
      pipeId: 'PIPE002',
      rule: '备用运行规则',
      alarmThreshold: '75%',
      remark: '备用供水管网配置'
    }
  ]

  try {
    const query = refSearch.value?.queryParams
    console.log('请求参数:', {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(query || {})
    })
    
    const res = await getPipeNetworkConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(query || {})
    })
    
    console.log('API响应数据:', res)
    
    // 处理不同的数据结构
    if (res.data) {
      // 情况1: res.data.records 和 res.data.total
      if (res.data.records) {
        TableConfig.dataList = res.data.records || []
        TableConfig.pagination.total = res.data.total || 0
      }
      // 情况2: res.data.data.records 和 res.data.data.total  
      else if (res.data.data && res.data.data.records) {
        TableConfig.dataList = res.data.data.records || []
        TableConfig.pagination.total = res.data.data.total || 0
      }
      // 情况3: res.data 直接是数组
      else if (Array.isArray(res.data)) {
        TableConfig.dataList = res.data
        TableConfig.pagination.total = res.data.length
      }
      // 情况4: res.data.data 是数组
      else if (Array.isArray(res.data.data)) {
        TableConfig.dataList = res.data.data
        TableConfig.pagination.total = res.data.data.length
      }
      else {
        console.warn('未知的数据结构:', res.data)
        TableConfig.dataList = []
        TableConfig.pagination.total = 0
      }
    }
    // 情况5: 直接是数组格式
    else if (Array.isArray(res)) {
      TableConfig.dataList = res
      TableConfig.pagination.total = res.length
    }
    else {
      console.warn('无法解析的响应格式:', res)
      TableConfig.dataList = []
      TableConfig.pagination.total = 0
    }
    
    console.log('解析后的数据:', TableConfig.dataList)
    console.log('总数:', TableConfig.pagination.total)
    
    // 如果没有数据，使用模拟数据进行测试
    if (TableConfig.dataList.length === 0) {
      console.log('使用模拟数据进行测试')
      TableConfig.dataList = mockData
      TableConfig.pagination.total = mockData.length
    }
    
  } catch (error) {
    console.error('获取数据失败:', error)
    console.log('API调用失败，使用模拟数据')
    
    // API调用失败时使用模拟数据
    TableConfig.dataList = mockData
    TableConfig.pagination.total = mockData.length
    
    SLMessage.error('API调用失败，当前显示模拟数据')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style> 