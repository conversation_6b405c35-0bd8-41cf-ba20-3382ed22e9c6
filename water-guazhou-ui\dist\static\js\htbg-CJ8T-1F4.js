import{_ as b}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as C}from"./CardTable-rdWOL4_6.js";import{i as _,j as h,k as x}from"./manage-BReaEVJk.js";import{d as D,c as y,r as d,dP as k,bF as L,x as c,S as v,o as B,g as F,n as N,q as p,i as f,aB as T,C as V}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const w=D({__name:"htbg",props:{id:{},config:{}},setup(u){const s=y(),n=u,l=d({defaultExpandAll:!0,indexVisible:!0,title:"合同变更",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{var e;o.title="添加合同变更",o.defaultValue={code:`${n.config.code||""}-${k()}`,contractCode:n.config.code||"",constructionCode:n.config.constructionCode,type:"提高合理化建议"},(e=s.value)==null||e.openDialog()}}]}]}],columns:[{label:"合同编号",prop:"contractCode"},{label:"变更日期",prop:"amendDate",formatter:e=>L(e.amendDate).format("YYYY-MM-DD")},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTimeName"},{label:"变更原因",prop:"remark"},{label:"附件",prop:"attachments",download:!0}],operationWidth:"160px",operations:[{isTextBtn:!1,type:"success",text:"编辑合同",perm:!0,click:e=>m(e)},{isTextBtn:!1,type:"danger",text:"删除",perm:!0,click:e=>g(e)}],dataList:[],pagination:{hide:!0}}),o=d({title:"添加合同变更",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),_(e).then(a=>{var i;o.submitting=!1,a.data.code===200?(c.success(t+"成功"),(i=s.value)==null||i.closeDialog(),r()):c.warning(t+"失败")}).catch(a=>{o.submitting=!1,c.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"date",label:"变更日期",field:"amendDate",format:"x"},{type:"textarea",label:"变更原因",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),m=e=>{var t;o.title="编辑合同变更",o.defaultValue={...e||{},contractCode:n.config.code,constructionCode:n.config.constructionCode},(t=s.value)==null||t.openDialog()},g=e=>{v("确定删除？","提示信息").then(()=>{h(e.id).then(t=>{var a,i;((a=t.data)==null?void 0:a.code)===200?(c.success("删除成功"),r()):c.warning((i=t.data)==null?void 0:i.message)})}).catch(()=>{})},r=async()=>{x({page:1,size:-1,contractCode:n.config.code,constructionCode:n.config.constructionCode}).then(e=>{l.dataList=e.data.data.data||[]})};return B(()=>{r()}),(e,t)=>{const a=C,i=b;return F(),N(T,null,[p(a,{config:f(l),class:"card-table"},null,8,["config"]),p(i,{ref_key:"refForm",ref:s,config:f(o)},null,8,["config"])],64)}}}),S=V(w,[["__scopeId","data-v-54f327d8"]]);export{S as default};
