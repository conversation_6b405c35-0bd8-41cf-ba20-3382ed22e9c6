package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDrawingBoardManagementPageRequest;

import java.util.List;

/**
 * 平台管理-画板管理Service接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IBaseDrawingBoardManagementService {
    /**
     * 查询平台管理-画板管理
     *
     * @param id 平台管理-画板管理主键
     * @return 平台管理-画板管理
     */
    public BaseDrawingBoardManagement selectBaseDrawingBoardManagementById(String id);

    /**
     * 查询平台管理-画板管理列表
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 平台管理-画板管理集合
     */
    public IPage<BaseDrawingBoardManagement> selectBaseDrawingBoardManagementList(BaseDrawingBoardManagementPageRequest baseDrawingBoardManagement);

    /**
     * 新增平台管理-画板管理
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 结果
     */
    public int insertBaseDrawingBoardManagement(BaseDrawingBoardManagement baseDrawingBoardManagement);

    /**
     * 修改平台管理-画板管理
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 结果
     */
    public int updateBaseDrawingBoardManagement(BaseDrawingBoardManagement baseDrawingBoardManagement);

    /**
     * 批量删除平台管理-画板管理
     *
     * @param ids 需要删除的平台管理-画板管理主键集合
     * @return 结果
     */
    public int deleteBaseDrawingBoardManagementByIds(List<String> ids);

    /**
     * 删除平台管理-画板管理信息
     *
     * @param id 平台管理-画板管理主键
     * @return 结果
     */
    public int deleteBaseDrawingBoardManagementById(String id);
}
