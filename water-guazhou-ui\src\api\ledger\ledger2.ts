/* 台账接口 */
import request from '@/plugins/axios';

// 台账保存
export function saveData(params) {
  return request({
    url: '/api/assets/account',
    method: 'post',
    data: params
  });
}

export const getDeviceTreeData = (projectId: string) =>
  request({
    url: `/assets/account/getListTree?projectId=${projectId}`,
    method: 'get'
  });

// 获取设备类型
export const getDeviceType = () =>
  request({
    url: `/api/assets/account/getDeviceTypeList`,
    method: 'get'
  });

// 根据设备类型获取设备
export const getListByDeviceType = (deviceType?: string) =>
  request({
    url: `/api/assets/account/getListByDeviceType?deviceType=${deviceType}`,
    method: 'get'
  });
