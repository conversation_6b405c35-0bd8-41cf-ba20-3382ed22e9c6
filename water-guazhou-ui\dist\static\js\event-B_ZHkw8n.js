import{d as l,c as i,r as p,g as c,n as d,q as s,i as o,t as m,_ as u,C as _}from"./index-r0dFAfgr.js";import{C as f}from"./index-CcDafpIP.js";import{r as v}from"./chart-wy3NEK2T.js";const g={class:"onemap-panel-wrapper"},h=l({__name:"event",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(C){const e=i([{label:"0 个",value:"隐藏点总数"}]),t=p({labelPosition:"top",group:[{fieldset:{type:"underline",desc:"隐患事件类型占比"},fields:[{type:"vchart",option:v(),style:{width:"100%",height:"150px"}}]}]});return(k,a)=>{const n=u;return c(),d("div",g,[s(o(f),{modelValue:o(e),"onUpdate:modelValue":a[0]||(a[0]=r=>m(e)?e.value=r:null),span:24},null,8,["modelValue"]),s(n,{config:o(t)},null,8,["config"])])}}}),w=_(h,[["__scopeId","data-v-014b4023"]]);export{w as default};
