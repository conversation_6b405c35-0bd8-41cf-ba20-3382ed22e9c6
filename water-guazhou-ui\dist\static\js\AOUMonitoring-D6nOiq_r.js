import{z as i,d as k,c as f,r as p,b as o,S as A,o as C,g as F,n as I,p as g,q as l,i as d,aq as N,C as q}from"./index-r0dFAfgr.js";import{_ as O}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as L}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";const Z=r=>i({url:"/api/spp/waterFactory/monitorPoint/report",method:"get",params:r}),ee=()=>i({url:"/api/spp/waterFactory/monitorPoint/reportHeader",method:"get"}),R=r=>i({url:"/api/spp/waterFactory/monitorPoint",method:"delete",data:r}),G=r=>i({url:"/api/spp/waterFactory/monitorPoint/changeOrderNum",method:"post",data:r}),T=r=>i({url:`/api/spp/waterFactory/monitorPoint/changeDirection/${r}`,method:"post"}),W=r=>i({url:"/api/spp/waterFactory/monitorPoint/list",method:"get",params:r}),z=r=>i({url:"/api/spp/waterFactory/monitorPoint",method:"post",data:r}),B=r=>i({url:"/api/device/getWaterFactoryDevice",method:"get",params:r}),E={4:"供水",3:"进水"},U={class:"aou-wrapper overlay-y"},$={class:"search-box"},j={class:"table-box"},H={class:"table-box"},V=k({__name:"AOUMonitoring",setup(r){const h=f(),v=p({filters:[{type:"input",label:"名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>s()}]}]}),_=f(),D=p({labelPosition:"right",labelWidth:90,group:[{fields:[{type:"input",label:"水厂名称",clearable:!1,field:"factoryName",rules:[{required:!0,message:"请输入水厂名称"}]},{type:"select",label:"供水类型",field:"direction",options:[{label:"供水",value:"4"},{label:"进水",value:"3"}],rules:[{required:!0,message:"请选择供水类型"}]},{type:"input-number",label:"排序编号",field:"orderNum"}]}],submit:e=>{if(!a.currentRow){o.error("请先选择要添加的设备");return}z({...e,deviceId:a.currentRow.id}).then(t=>{t.data.code===200?(o.success("添加成功"),c(),s()):o.error("添加失败")}).catch(()=>{o.error("添加失败")})}}),a=p({height:250,pagination:{refreshData({page:e,size:t}){a.pagination.page=e,a.pagination.limit=t,s()}},operationWidth:60,operations:[{perm:!0,text:"添加",iconifyIcon:"ep:circle-plus",type:"success",click:e=>M(e)}],dataList:[],columns:[{label:"编号",prop:"id"},{label:"名称",prop:"name"},{label:"设备类型",prop:"deviceTypeName"}]}),s=()=>{var t;const e=((t=h.value)==null?void 0:t.queryParams)||{};B({page:a.pagination.page||1,size:a.pagination.limit||20,name:e.name}).then(n=>{const m=n.data.data||{};a.dataList=m.data||[],a.pagination.total=m.total||0})},y=f(),P=p({filters:[{type:"input",label:"水厂名称",field:"name"},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>c()}]}]}),u=p({height:250,pagination:{hide:!0,refreshData({page:e,size:t}){u.pagination.page=e,u.pagination.limit=t,c()}},dataList:[],columns:[{label:"水厂名称",prop:"factoryName"},{label:"编号",prop:"deviceId"},{label:"排序",prop:"orderNum",formItemConfig:{type:"input-number",onChange:(e,t)=>w(e,t)}},{label:"流量计名称",prop:"deviceName"},{label:"供水方向",prop:"direction",formatter(e,t){return E[t]}}],operationWidth:150,operations:[{perm:!0,text:"变更方向",iconifyIcon:"ep:edit-pen",click:e=>S(e)},{perm:!0,text:"移出",type:"danger",iconifyIcon:"ep:delete",click:e=>x(e)}]}),c=async()=>{var t,n;const e=await W({name:(n=(t=y.value)==null?void 0:t.queryParams)==null?void 0:n.name});u.dataList=e.data.data},S=async e=>{try{const t=await T(e.id);t.data.code===200?(o.success("修改成功"),c()):o.error(t.data.message)}catch{o.error("修改失败")}},w=async(e,t)=>{try{(await G({id:t.id,orderNum:e})).data.code===200&&(o.success("修改成功"),c())}catch{o.error("操作失败")}},x=async e=>{const t=e?[e.id]:[];if(!t.length){o.warning("请先选择要移除的监测点");return}A("确定移出?","提示信息").then(async()=>{try{await R(t),c(),s(),o.success("移出成功")}catch{o.error("移出失败")}}).catch(()=>{})},M=async e=>{var t;e&&(a.currentRow=e,(t=_.value)==null||t.Submit())};return C(()=>{s(),c()}),(e,t)=>{const n=L,m=O,b=N;return F(),I("div",U,[g("div",$,[l(n,{ref_key:"refSearch",ref:h,config:d(v)},null,8,["config"])]),g("div",j,[l(m,{ref_key:"refSearch_Device",ref:_,config:d(D),style:{padding:"10px 0"}},null,8,["config"]),l(b,{config:d(a)},null,8,["config"])]),g("div",H,[l(n,{ref_key:"refSearch_Attr",ref:y,config:d(P),style:{padding:"10px 0"}},null,8,["config"]),l(b,{config:d(u)},null,8,["config"])])])}}}),J=q(V,[["__scopeId","data-v-2d19cda6"]]),te=Object.freeze(Object.defineProperty({__proto__:null,default:J},Symbol.toStringTag,{value:"Module"}));export{J as A,ee as G,Z as a,te as b};
