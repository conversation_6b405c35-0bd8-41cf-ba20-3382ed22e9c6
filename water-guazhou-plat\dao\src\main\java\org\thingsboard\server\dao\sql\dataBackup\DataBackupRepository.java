package org.thingsboard.server.dao.sql.dataBackup;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.DataBackup;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;

import java.util.List;

public interface DataBackupRepository extends CrudRepository<DataBackup, String> {

    @Query("FROM DataBackup d WHERE d.createTime > ?1 AND d.createTime < ?2 ORDER BY d.createTime DESC")
    List<DataBackup> findByStartAndEnd(long start, long end);

}
