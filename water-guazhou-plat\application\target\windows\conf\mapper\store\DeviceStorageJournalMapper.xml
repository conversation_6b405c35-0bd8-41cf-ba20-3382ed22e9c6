<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.DeviceStorageJournalMapper">
    <!--suppress SqlShadowingAlias -->
    <sql id="Base_Column_List">
        <!--@formatter:off-->
        <!--@sql select -->s.id,s.is_collect,
                           s.serial_id,
                           s.device_label_code,
                           s.supplier_id,
                           (select name from tb_supplier where id = s.supplier_id)             as supplier_name,
        <!--    if(scrapped_time is null,-->
        <!--        &lt;!&ndash;动态计算预期报废时间&ndash;&gt;-->
        <!--        (select-->
        <!--            (select usage.receive_time from device_usage_journal usage where usage.device_label_code = s.device_label_code)::timestamp-->
        <!--            + (i.use_year || ' year')::interval-->
        <!--        from m_device i where i.serial_id = s.serial_id and i.tenant_id = s.tenant_id),-->

        <!--        &lt;!&ndash;已经报废&ndash;&gt;-->
        <!--        scrapped_time-->
        <!--    ) scrapped_time,-->
storage_journal_get_scrapped_time(scrapped_time, s.serial_id, s.device_label_code, s.tenant_id) as scrapped_time,
                           s.storehouse_id,
                           (select name from store where id = s.storehouse_id)            as storehouse_name,
                           s.shelves_id,
                           (select name from goods_shelf where id = s.shelves_id)         as shelves_name,
                           s.out_time,
                           s.last_maintainance_time,
                           s.last_inspection_time,
                           s.store_out_item_id,
                           s.tenant_id,
                           d.model,
                           d.name,
                           d.type_id,
                           (select name from m_device_type where id = d.type_id)         as type,
                           device_get_root_type_id_by_type_id(d.type_id)                 as top_type_id,
(select name from m_device_type where id = device_get_root_type_id_by_type_id(d.type_id)) as top_type,
                           device_storage_journal_get_area_id(s.device_label_code, s.tenant_id)       as install_address_id,
                           device_storage_journal_get_area_name(s.device_label_code, s.tenant_id)     as address,
                           device_storage_journal_get_address(s.device_label_code, s.tenant_id)       as detailAddress,
                           device_storage_journal_get_project_id(s.device_label_code, s.tenant_id)    as project_id,
            (select name from construction_project where id =
                            device_storage_journal_get_project_id(s.device_label_code, s.tenant_id))  as project_name,
                           device_storage_journal_get_department_id(s.device_label_code, s.tenant_id) as department_id,
            (select name from tb_department where id =
                            device_storage_journal_get_department_id(s.device_label_code, s.tenant_id)) as department_name
        <!--@sql from device_storage_journal s, m_device d-->
        <!--@formatter:on-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.DeviceStorageJournalResponse">
        <result column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="device_label_code" property="deviceLabelCode"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="scrapped_time" property="scrappedTime"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="storehouse_name" property="storehouseName"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="store_out_item_id" property="storeOutItemId"/>
        <result column="out_time" property="outTime"/>
        <result column="last_maintainance_time" property="lastMaintainanceTime"/>
        <result column="last_inspection_time" property="lastInspectionTime"/>
        <result column="tenant_id" property="tenantId"/>

        <result column="model" property="model"/>
        <result column="is_collect" property="isCollect"/>
        <result column="name" property="name"/>
        <result column="type_id" property="typeId"/>
        <result column="type" property="type"/>
        <result column="top_type_id" property="topTypeId"/>
        <result column="top_type" property="topType"/>
        <result column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="install_address_id" property="installAddressId"/>
        <result column="address" property="installAddressName"/>
        <result column="detailAddress" property="detailInstallAddressName"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_storage_journal s
                 left join
             m_device d on s.serial_id = d.serial_id and s.tenant_id = d.tenant_id
        <where>
            <if test="serialId != null and serialId != ''">
                and s.serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="name != null and name != ''">
                and d.name like '%' || #{name} || '%'
            </if>
            <if test="deviceTypeId != null and deviceTypeId != ''">
                and device_is_device_type_by_device_serial_id(s.serial_id, #{deviceTypeId}, #{tenantId})
            </if>
            <if test="deviceLabelCode != null and deviceLabelCode != ''">
                and device_label_code like '%' || #{deviceLabelCode} || '%'
            </if>
            <if test="supplierId != null and supplierId != ''">
                and supplier_id = #{supplierId}
            </if>
            <if test="scrappedTime != null">
                and onday(scrapped_time, #{scrappedTime})
            </if>
            <if test="storehouseId != null and storehouseId != ''">
                and storehouse_id = #{storehouseId}
            </if>
            <if test="shelvesId != null and shelvesId != ''">
                and shelves_id = #{shelvesId}
            </if>
            <if test="areaId != null and areaId != ''">
                and device_storage_journal_at_area(device_label_code, #{areaId}, s.tenant_id)
            </if>
            <if test="address != null and address != ''">
                <!--@formatter:off-->
                and (select settle.address like '%' || #{address} || '%' from device_settle_journal settle
                where device_label_code = s.device_label_code)
                <!--@formatter:on-->
            </if>
            <if test="projectId != null and projectId != ''">
                and device_storage_journal_is_project(device_label_code, #{projectId}, s.tenant_id)
            </if>
            <if test="storehouseCode != null and storehouseCode != ''">
                and store_house_is_code_like(storehouse_id, #{storehouseCode})
            </if>
            <if test="storehouseName != null and storehouseName != ''">
                and store_house_is_name_like(storehouse_id, #{storehouseName})
            </if>
            <if test="shelfCode != null and shelfCode != ''">
                <!--@formatter:off-->
and (select goods_shelf.code like '%' || #{shelfCode} || '%' from goods_shelf where id = shelves_id)
                <!--@formatter:on-->
            </if>
            <if test="shelfName != null and shelfName != ''">
                <!--@formatter:off-->
and (select goods_shelf.name like '%' || #{shelfName} || '%' from goods_shelf where id = shelves_id)
                <!--@formatter:on-->
            </if>
            <if test="isOut != null">
                <choose>
                    <when test="isOut">
                        and store_out_item_id is not null
                    </when>
                    <otherwise>
                        and store_out_item_id is null
                    </otherwise>
                </choose>
            </if>
            <if test="model != null and model != ''">
                and d.model like '%' || #{model} || '%'
            </if>
            <if test="lastMaintainanceTime != null">
                and onday(last_maintainance_time, #{lastMaintainanceTime})
            </if>
            <if test="lastMaintainanceTimeFrom != null">
                and last_maintainance_time >= #{lastMaintainanceTimeFrom}
            </if>
            <if test="lastMaintainanceTimeTo != null">
                and last_maintainance_time &lt;= #{lastMaintainanceTimeTo}
            </if>
            <if test="lastInspectionTime != null">
                and onday(last_inspection_time, #{lastInspectionTime})
            </if>
            <if test="storeOutId != null and storeOutId != ''">
                and store_out_item_id in
                    (select id from store_out_record_detail detail where detail.main_id = #{storeOutId})
            </if>
            <if test="storeOutItemId != null and storeOutItemId != ''">
                and store_out_item_id in (<foreach collection="storeOutItemId.split(',')" item="element">
                #{element}
            </foreach>)
            </if>
            <if test="scrapped == null or scrapped == false">
                and s.scrapped_time is null
            </if>
            <if test="isCollect != null and isCollect != ''">
                and s.is_collect is not null and s.is_collect = #{isCollect}
            </if>
            and s.tenant_id = #{tenantId}
        </where>
        order by device_label_code desc
    </select>


    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_storage_journal s,
             m_device d
        where s.serial_id = d.serial_id
          and s.id = #{id}
    </select>

    <update id="update">
        update device_storage_journal
        <set>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="deviceLabelCode != null">
                device_label_code = #{deviceLabelCode},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <!--            <if test="scrappedTime != null">-->
            <!--                scrapped_time = #{scrappedTime},-->
            <!--            </if>-->
            <if test="storehouseId != null">
                storehouse_id = #{storehouseId},
            </if>
            <if test="shelvesId != null">
                shelves_id = #{shelvesId},
            </if>
            <if test="lastMaintainanceTime != null">
                last_maintainance_time = #{lastMaintainanceTime},
            </if>
            <if test="lastInspectionTime != null">
                last_inspection_time = #{lastInspectionTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO device_storage_journal(id,
                                           serial_id,
                                           device_label_code,
                                           store_in_code,
                                           supplier_id,
                                           storehouse_id,
                                           shelves_id,
                                           last_maintainance_time,
                                           last_inspection_time,
                                           tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.serialId},
             generate_number_reset_different_day_with_date_prefix('device_storage_journal' || #{element.tenantId},
                                                                  'fm000000', 999999),
             #{element.storeInCode},
             #{element.supplierId},
             #{element.storehouseId},
             #{element.shelvesId},
             #{element.lastMaintainanceTime},
             #{element.lastInspectionTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="checkout">
        update device_storage_journal sj
        set store_out_item_id = #{storeOutItemId}
        where id = #{id}
    </update>

    <update id="checkoutAll">
        update device_storage_journal sj
        set store_out_item_id = #{storeOutItemId}
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element})
        </foreach>
        ) as valueTable(id)
        where sj.id = valueTable.id
          and sj.tenant_id = #{tenantId}
    </update>

    <update id="checkinViaStoreOutItemId">
        update
            device_storage_journal
        set store_out_item_id = null,
            out_time          = null
        where store_out_item_id = #{storeOutItemId}
    </update>

    <update id="checkinAllViaStoreOutItemId">
        update device_storage_journal
        set store_out_item_id = null,
            out_time          = null
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element})
        </foreach>
        ) as valueTable(id)
        where device_storage_journal.store_out_item_id = valueTable.id
    </update>

    <update id="updateAll">
        update device_storage_journal
        <set>
            serial_id              = valueTable.serialId,
            device_label_code      = valueTable.deviceLabelCode,
            supplier_id            = valueTable.supplierId,
            storehouse_id          = valueTable.storehouseId,
            shelves_id             = valueTable.shelvesId,
            last_maintainance_time = valueTable.lastMaintainanceTime,
            last_inspection_time   = valueTable.lastInspectionTime,
            store_in_code          = valueTable.storeInCode
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.serialId},
             #{element.deviceLabelCode},
             #{element.supplierId},
             #{element.storehouseId},
             #{element.shelvesId},
             #{element.lastMaintainanceTime},
             #{element.lastInspectionTime},
             #{element.storeInCode})
        </foreach>
        ) as valueTable(id, serialId, deviceLabelCode, supplierId, storehouseId, shelvesId,
                        lastMaintainanceTime, lastInspectionTime, storeInCode)
        where id = valueTable.id
    </update>
    <resultMap id="MainRestDeviceStorageJournalResultMap"
               type="org.thingsboard.server.dao.model.sql.store.MainRestDeviceStorageJournal">
        <result column="serial_id" property="serialId"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="storehouse_code" property="storehouseCode"/>
        <result column="storehouse_name" property="storehouseName"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="shelves_code" property="shelvesCode"/>
        <result column="shelves_name" property="shelvesName"/>
        <result column="count" property="count"/>
        <collection property="restDeviceInfos"
                    resultMap="RestDeviceStorageJournalResultMap"/>
    </resultMap>

    <resultMap id="RestDeviceStorageJournalResultMap"
               type="org.thingsboard.server.dao.model.sql.store.RestDeviceStorageJournal">
        <result column="id" property="id"/>
        <result column="device_label_code" property="deviceLabelCode"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="storehouse_code" property="storehouseCode"/>
        <result column="storehouse_name" property="storehouseName"/>
        <result column="store_in_code" property="storeInCode"/>
        <result column="store_out_item_id" property="storeOutItemId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="install_address_id" property="installAddressId"/>
        <result column="address" property="installAddressName"/>
        <result column="actual_address" property="actualAddress"/>
        <result column="last_maintainance_time" property="lastMaintainanceTime"/>
        <result column="last_inspection_time" property="lastInspectionTime"/>
        <result column="tenant_id" property="tenantId"/>
        <!--        <association property="deviceInfoResponse"-->
        <!--                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"-->
        <!--                     column="{labelCode=device_label_code,tenantId=tenant_id}"-->
        <!--                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoByDeviceLabelCode"/>-->
        <association property="deviceInfoResponse" resultMap="DeviceInfoResponseResultMap"/>
    </resultMap>
    <resultMap id="DeviceInfoResponseResultMap" type="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse">
        <result column="serial_id" property="serialId"/>
        <result column="model" property="model"/>
        <result column="name" property="name"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="shelves_code" property="shelvesCode"/>
        <result column="shelves_name" property="shelvesName"/>
        <result column="type_id" property="typeId"/>
        <result column="root_type" property="topType"/>
        <result column="type" property="type"/>
        <result column="unit" property="unit"/>
    </resultMap>

    <sql id="RestStorageSelectorColumn">
        <!--@sql select -->id,
                           tenant_id,
                           last_maintainance_time,
                           last_inspection_time,
                           store_in_code,
                           supplier_id,
                           storehouse_id,
                           shelves_id,
                           device_label_code,
                           store_out_item_id,
                           device_storage_journal_get_area_id(device_label_code, tenant_id)   as install_address_id,
                           device_storage_journal_get_area_name(device_label_code, tenant_id) as address,
                           serial_id<!--@sql from device_storage_journal   -->
    </sql>

    <sql id="RestStorageSelectorColumnNameDriving">
        <!--@sql select -->s.id,
                           s.tenant_id,
                           s.store_in_code,
                           s.last_maintainance_time,
                           s.last_inspection_time,
                           s.supplier_id,
                           s.storehouse_id,
                           s.shelves_id,
                           s.device_label_code,
                           s.store_out_item_id,
                           device_storage_journal_get_area_id(device_label_code, tenant_id)   as install_address_id,
                           device_storage_journal_get_area_name(device_label_code, tenant_id) as address,
                           s.serial_id<!--@sql from device_storage_journal s  -->
    </sql>

    <sql id="RestSelectorColumn">
        <!--@sql select -->s.id,
                           s.tenant_id,
                           s.store_in_code,
                           s.last_maintainance_time,
                           s.last_inspection_time,
                           s.supplier_id,
                           (select name from tb_supplier where id = s.supplier_id)                as supplier_name,
                           s.storehouse_id,
                           s.shelves_id,
                           s.device_label_code,
                           device_rest_storage_count_by_serial_id_and_shelves_id(d.serial_id, s.shelves_id,
                                                                                 #{req.inStoreOnly},
                                                                                 s.tenant_id)     as count,
                           s.storehouse_id,
                           (select code from store where id = s.storehouse_id)                    as storehouse_code,
                           (select name from store where id = s.storehouse_id)                    as storehouse_name,
                           s.store_out_item_id,
                           device_storage_journal_get_area_id(s.device_label_code, s.tenant_id)   as install_address_id,
                           device_storage_journal_get_area_name(s.device_label_code, s.tenant_id) as address,
                           device_storage_journal_get_address(device_label_code, s.tenant_id)     as actual_address,
                           d.serial_id,
                           d.name,
                           d.model,
                           d.type_id,
                           device_get_root_type_name_by_type_id(d.type_id)                        as root_type,
                           (select indt.name from m_device_type indt where indt.id = d.type_id)   as type,
                           d.unit,
        <!--@formatter:off-->
        <!--shelves_id-->
(select shelves_id from device_storage_journal
        where device_label_code = s.device_label_code and tenant_id = s.tenant_id) as shelves_id,
(select code from goods_shelf where id =
    (select shelves_id from device_storage_journal where device_label_code = s.device_label_code and tenant_id = s.tenant_id)) as shelves_code,
(select name from goods_shelf where id =
    (select shelves_id from device_storage_journal where device_label_code = s.device_label_code and tenant_id = s.tenant_id)) as shelves_name
        <!--@formatter:on-->
        <!--@sql from device_storage_journal s, m_device d, goods_shelf shelf-->
    </sql>

    <sql id="RestStorageSelector">
        from device_storage_journal
        <where>
            <if test="req.serialId != null and req.serialId != ''">
                and serial_id like '%' || #{req.serialId} || '%'
            </if>
            <if test="req.deviceLabelCode != null and req.deviceLabelCode != ''">
                and device_label_code like '%' || #{req.deviceLabelCode} || '%'
            </if>
            <if test="req.storeId != null and req.storeId != ''">
                and storehouse_id = #{req.storeId}
            </if>
            <if test="req.shelvesId != null and req.shelvesId != ''">
                and shelves_id = #{req.shelvesId}
            </if>
            <if test="req.areaId != null and req.areaId != ''">
                and device_storage_journal_at_area(device_label_code, #{req.areaId}, tenant_id)
            </if>
            <if test="req.address != null and req.address != ''">
                <!--@formatter:off-->
                and (select settle.address like '%' || #{req.address} || '%' from device_settle_journal settle
                where device_label_code = device_storage_journal.device_label_code)
                <!--@formatter:on-->
            </if>
            <if test="req.inStoreOnly != null">
                <choose>
                    <when test="req.inStoreOnly">
                        and store_out_item_id is null
                    </when>
                    <otherwise>
                        and store_out_item_id is not null
                    </otherwise>
                </choose>
            </if>
            <if test="req.lastMaintainanceTimeFrom != null">
                and last_maintainance_time >= #{req.lastMaintainanceTimeFrom}
            </if>
            <if test="req.lastMaintainanceTimeTo != null">
                and last_maintainance_time &lt;= #{req.lastMaintainanceTimeTo}
            </if>
            and scrapped_time is null
            and tenant_id = #{req.tenantId}
        </where>
    </sql>
    <sql id="RestDeviceCombineSelector">
        <!--@sql select from m_device_type s-->
        left join m_device d
                  on d.serial_id = s.serial_id
        <where>
            <if test="req.name != null and req.name != ''">
                and d.name like '%' || #{req.name} || '%'
            </if>
            <if test="req.model != null and req.model != ''">
                and d.model = #{req.model}
            </if>
            <if test="req.deviceTypeId != null and req.deviceTypeId != ''">
                and device_type_is_type(d.type_id, #{req.deviceTypeId})
            </if>
            and d.tenant_id = #{req.tenantId}
        </where>
    </sql>
    <select id="findRestByPage" resultMap="MainRestDeviceStorageJournalResultMap">
        select
        <include refid="RestSelectorColumn"/>
        from
        (
        <include refid="RestSubQueryTable"/>
        ) s
        <include refid="RestDeviceCombineSelector"/>
        order by s.device_label_code desc
    </select>

    <sql id="RestSubQueryTable">
        select
        <include refid="RestStorageSelectorColumn"/>
        <include refid="RestStorageSelector"/>
        and (shelves_id, serial_id) in (
        select shelves_id, serial_id
        <include refid="RestStorageSelector"/>
        group by shelves_id, serial_id
        <if test="size > 0">
            offset #{page} limit #{size}
        </if>
        )
    </sql>

    <select id="countRestCountByPage" resultType="long">
        select count(1) from (select serial_id
        <include refid="RestStorageSelector"/>
        group by shelves_id, serial_id) s
        <include refid="RestDeviceCombineSelector"/>
    </select>

    <select id="restCount" resultType="int">
        select out.num - (select count(id) from device_storage_journal sj where sj.store_out_item_id = out.id)
        from store_out_record_detail out
        where out.tenant_id = #{tenantId}
          and out.id = #{storeOutItemId}
    </select>

    <select id="checkoutViaStoreOut" resultType="boolean">
        select device_storage_checkout_by_store_out_id(#{id}, #{storeOutId})
    </select>

    <select id="findRestWithoutSplitByPage"
            resultMap="RestDeviceStorageJournalResultMap">
        select
        <include refid="RestSelectorColumn"/>
        from (select
        <include refid="RestStorageSelectorColumn"/>
        <include refid="RestStorageSelector"/>) s
        <include refid="RestDeviceCombineSelector"/>
        order by s.device_label_code desc
    </select>

    <resultMap id="RestDeviceStorageInfoResultMap"
               type="org.thingsboard.server.dao.model.sql.store.RestDeviceStorageInfo">
        <id column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="type_id" property="typeId"/>
        <result column="type" property="type"/>
        <result column="top_type_id" property="topTypeId"/>
        <result column="top_type" property="topType"/>
        <result column="model" property="model"/>
        <result column="name" property="name"/>
        <result column="count" property="count"/>
        <result column="unit" property="unit"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findRestDeviceInfoConditional"
            resultMap="RestDeviceStorageInfoResultMap">
        select id,
               serial_id,
               type_id,
               device_get_type_name_by_type_id(type_id)                     as type,
               device_get_root_type_id_by_type_id(type_id)                  as top_type_id,
               device_get_root_type_name_by_type_id(type_id)                as top_type,
               model,
               name,
               device_rest_storage_count_by_serial_id(serial_id, tenant_id) as count,
               unit,
               tenant_id
        from m_device
        <where>
            <if test="serialId != null and serialId != ''">
                and serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="model != null and model != ''">
                and model like '%' || #{model} || '%'
            </if>
            <if test="deviceTypeId != null and deviceTypeId != ''">
                and device_type_is_type(type_id, #{deviceTypeId})
            </if>
        </where>
    </select>


    <sql id="Detail_Column_List">
        <!--@sql select -->
        storage.id,
        device.serial_id,
        device.name                                                             device_name,
        device.unit                                                             device_unit,
        device.type_id                                                          device_type_id,
        device_get_type_name_by_type_id(device.type_id)                         device_type,
        device.model                                                            device_model,
        device.images                                                           device_images,
        device.files                                                            device_files,
        storage.supplier_id,
        storage.device_label_code,
        device_storage_journal_get_supplier_name(storage.supplier_id)           supplier_name,
        device_purchase_get_intention_price_by_store_in_code(storage.store_in_code, device.serial_id,
                                                             storage.tenant_id) price,
        sin.create_time,
        device.use_year                                                         device_use_year,
        device.auto_field,
        storage.out_time,
        storage.scrapped_time,
        storage.scrapped,
        device.remark,
        storage.tenant_id
        <!--@sql from device_storage_journal storage, m_device device, store_in_record sin-->
    </sql>
    <resultMap id="DetailDeviceStorageInfoResultMap"
               type="org.thingsboard.server.dao.model.sql.store.DetailDeviceStorageInfo">
        <id column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_label_code" property="deviceLabelCode"/>
        <result column="device_unit" property="deviceUnit"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_type_id" property="deviceTypeId"/>
        <result column="device_model" property="deviceModel"/>
        <result column="device_images" property="deviceImages"/>
        <result column="device_files" property="deviceFiles"/>
        <result column="auto_field" property="autoField"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="price" property="price"/>
        <result column="create_time" property="createTime"/>
        <result column="device_use_year" property="deviceUseYear"/>
        <result column="out_time" property="outTime"/>
        <result column="scrapped_time" property="scrappedTime"/>
        <result column="scrapped" property="scrapped"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="detail" resultMap="DetailDeviceStorageInfoResultMap">
        select
        <include refid="Detail_Column_List"/>
        from device_storage_journal storage,
             m_device device,
             store_in_record sin
        where storage.serial_id = device.serial_id
          and storage.tenant_id = device.tenant_id
          and storage.store_in_code = sin.code
          and storage.tenant_id = sin.tenant_id
          and storage.id = #{id}
    </select>

    <update id="updateScrappedTime">
        update device_storage_journal storage
        set scrapped_time = (select (select usage.receive_time
                                     from device_usage_journal usage
                                     where usage.device_label_code = storage.device_label_code)::timestamp
                                        + (i.use_year || ' year')::interval
                             from m_device i
                             where i.serial_id = storage.serial_id
                               and i.tenant_id = storage.tenant_id)
        where scrapped_time is null
          and device_label_code = #{deviceLabelCode}
          and tenant_id = #{tenantId}
    </update>

    <insert id="save">
        INSERT INTO device_storage_journal(id,
                                           serial_id,
                                           device_label_code,
                                           store_in_code,
                                           supplier_id,
                                           storehouse_id,
                                           shelves_id,
                                           last_maintainance_time,
                                           last_inspection_time,
                                           tenant_id)
        VALUES (#{id},
                #{serialId},
                generate_number_reset_different_day_with_date_prefix('device_storage_journal' || #{tenantId},
                                                                     'fm000000', 999999),
                #{storeInCode},
                #{supplierId},
                #{storehouseId},
                #{shelvesId},
                #{lastMaintainanceTime},
                #{lastInspectionTime},
                #{tenantId})
    </insert>

    <select id="canOperate" resultType="boolean">
        select manager = #{currentUserId}
        from store_out_record
        where id = #{storeOutId}
    </select>
</mapper>