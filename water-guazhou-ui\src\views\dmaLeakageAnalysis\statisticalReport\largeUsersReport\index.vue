<!-- 大用户报表 -->
<template>
  <SLCard
    class="wrapper-content"
    title=""
    overlay
  >
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <FormTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></FormTable>
  </SLCard>
</template>
<script lang="ts" setup>
import { GetDmaBigUserReport } from '@/api/mapservice/dma'
import { IFormTableIns, ISearchIns } from '@/components/type'
import {
  formatterYear,
  formatterMonth,
  formatterDate
} from '@/utils/GlobalHelper'

const refTable = ref<IFormTableIns>()
const refSearch = ref<ISearchIns>()
const handleHidden = (params, query, config) => (config.hidden = params.type !== config.field)
// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    grade: '1',
    type: 'month',
    year: moment().format(formatterYear),
    month: moment().format(formatterMonth),
    day: [moment().format(formatterDate), moment().format(formatterDate)]
  },
  filters: [
    {
      type: 'select',
      field: 'grade',
      label: '分区等级',
      clearable: false,
      options: [
        { label: '一级分区', value: '1' },
        { label: '二级分区', value: '2' }
      ]
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'day' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'month',
      label: '',
      field: 'month',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'daterange',
      label: '',
      field: 'day',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'success',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})

const initTableColumns = (
  subColumns: IFormTableColumn[] = []
): IFormTableColumn[] => {
  return [
    {
      prop: 'title',
      label: '2022-10月大用户用水量报表',
      align: 'center',
      subColumns: [
        { prop: 'date', label: '日期', minWidth: 120 },
        ...subColumns,
        { prop: 'totalSupplyWater', label: '供水量', minWidth: 120 },
        { prop: 'totalUseWater', label: '大用户用水量', minWidth: 120 },
        { prop: 'rate', label: '占比(%)', minWidth: 120 }
      ]
    }
  ]
}
// 列表
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: initTableColumns(),
  pagination: {
    hide: true
  }
})
// 数据获取
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaBigUserReport({
      type: query.type,
      date:
        query.type === 'year'
          ? query.year
          : query.type === 'month'
            ? query.month
            : undefined,
      start: query.day?.[0] ?? undefined,
      end: query.day?.[1] ?? undefined,
      grade: query.grade
    })
    const data = res.data.data || {}
    TableConfig.dataList = data.dataList || []

    TableConfig.columns = initTableColumns(
      data.header?.map(item => {
        return {
          label: item.label,
          prop: item.value,
          minWidth: 160
        }
      })
    )
  } catch (error) {
    //
  }
  TableConfig.loading = false
}

onMounted(async () => {
  await refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}

.card-table {
  height: calc(100% - 100px);
  width: 100%;
}
</style>
