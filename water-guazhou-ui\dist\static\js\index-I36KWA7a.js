import{_ as x}from"./index-BJ-QPYom.js";import h from"./card-BMsSIo6U.js";import{d as k,r as i,S as _,c7 as v,b as s,c8 as I,Q as P,o as L,c9 as T,g as c,h as f,F as y,q as b,i as n,an as N}from"./index-r0dFAfgr.js";import q from"./index-DI5uYxj7.js";import D from"./TreeBox-mfOmxwZJ.js";import"./index-C9hz-UZb.js";import"./index-t9hHXNDZ.js";import"./index-BI1vGJja.js";import"./add-CFWDlVLF.js";import"./index-CaaU9niG.js";import"./URLHelper-B9aplt5w.js";const $=k({__name:"index",setup(V){const u=i({totalLoading:!1}),o=i({title:"区域划分",data:[],loading:!1,isFilterTree:!0,currentProject:{},addRoot:{text:"新建项目",perm:!0,click:e=>g(e)},edit:{text:"编辑",perm:!0,click:e=>{g(e,"edit")}},delete:{text:"删除",perm:!0,click:e=>{_("确定删除？","删除提示").then(()=>{v(e==null?void 0:e.id.id).then(()=>{s.success("操作成功"),d()}).catch(()=>s.error("删除失败"))}).catch(()=>{})}},expandNodeId:[],defaultProps:{children:"children",label:"name"},treeNodeHandleClick:e=>{p.value=e,o.currentProject=e}}),g=(e,r)=>{const t=e==null?void 0:e.data;if(a.externalParams={},r==="edit"){if(t!=null&&t.additionalInfo){let l=null;try{l=JSON.parse(t.additionalInfo)}catch{l=t.additionalInfo}a.defaultValue={...t,latd:t.latd*1,lgtd:t.lgtd*1,...l}}a.title="编辑应用"}else a.title="新建应用",a.defaultValue={location:[116.4,39.91]},t&&(a.defaultValue={location:[116.4,39.91]},a.externalParams={parentId:t.id});a.visible=!0},m=i({validatePhone:(e,r,t)=>{r===""||r===null||/^1\d{10}$/.test(r)?t():t(new Error("请输入正确手机号"))},postcode:(e,r,t)=>{r===""||r===null||/^[1-9][0-9]{5}$/.test(r)?t():t(new Error("请输入正确邮政编码"))}}),a=i({visible:!1,title:"新建应用",close:()=>{a.visible=!1},addUrl:"api/tenant",editUrl:"api/tenant",defaultValue:{},externalParams:{},columns:[{type:"input",label:"企业名称",key:"title",rules:[{required:!0,message:"请填写企业名称"}]},{type:"input",label:"邮箱",key:"email",rules:[{type:"email",message:"请输入正确邮箱地址",trigger:"blur"}]},{type:"select",label:"应用",key:"appTypeId",options:[],rules:[{required:!0,message:"请选择应用",trigger:"change"}]},{type:"input",label:"联系手机",key:"phone",rules:[{validator:m.validatePhone,trigger:"blur"}]},{type:"input",label:"国家",key:"country",rules:[{max:20,message:"输入不可超过20位",trigger:"blur"}]},{type:"input",label:"省",key:"state",rules:[{max:20,message:"输入不可超过20位",trigger:"blur"}]},{type:"input",label:"城市",key:"city",rules:[{max:20,message:"输入不可超过20位",trigger:"blur"}]},{type:"input",label:"地址",key:"address",rules:[{required:!0,message:"请输入企业地址",trigger:"blur"},{max:40,message:"输入不可超过40位",trigger:"blur"}]},{type:"input-number",label:"经度",key:"lgtd",rules:[{required:!0,message:"请输入企业经度",trigger:"blur"}]},{type:"input-number",label:"纬度",key:"latd",rules:[{required:!0,message:"请输入企业纬度",trigger:"blur"}]},{type:"input",label:"邮编",key:"zip",rules:[{validator:m.postcode,trigger:"blur"},{max:20,message:"名称不超过20位",trigger:"blur"}]},{type:"input",label:"地区",key:"region",rules:[{max:40,message:"输入不可超过40位",trigger:"blur"}]},{type:"input",label:"平台名称",key:"platformName",aInfo:!0,rules:[{max:40,message:"输入不可超过40位",trigger:"blur"}]},{type:"textarea",label:"企业简介",key:"companyProfiles",aInfo:!0,rules:[{max:200,message:"输入不可超过200位",trigger:"blur"}]},{type:"input",label:"App标题",key:"apptitle",aInfo:!0,rules:[{max:40,message:"输入不可超过200位",trigger:"blur"}]}]}),d=()=>{I().then(e=>{if(console.log(e),e.data){o.data=e.data.data;const r=o.data;p.value=r[0],u.totalLoading=!1}else s.info("暂无企业 不可操作，请创建企业"),u.totalLoading=!1}).catch(e=>{console.log(e),s.info("暂无企业 不可操作，请创建企业"),u.totalLoading=!1})};P(()=>{console.log("dataMonitoring is Unmounted")});const p=i({value:{}});return L(()=>{d(),T().then(e=>{console.log(e.data),a.columns[2].options=e.data.map(r=>({label:r.appName,value:r.id}))})}),(e,r)=>{const t=x;return c(),f(D,null,{tree:y(()=>[b(t,{"tree-data":n(o)},null,8,["tree-data"])]),default:y(()=>[b(h,{config:n(p)},null,8,["config"]),n(a).visible?(c(),f(q,{key:0,"dialog-width":"560px",config:n(a),onRefreshData:d},null,8,["config"])):N("",!0)]),_:1})}}});export{$ as default};
