import{d as L,r as _,o as V,ay as A,g as D,n as F,q as m,p as v,i as b,s as w,bt as P,aq as R,C as B}from"./index-r0dFAfgr.js";import{w as O}from"./Point-WxyopZva.js";import{P as y}from"./index-CcDafpIP.js";import{r as C}from"./chart-wy3NEK2T.js";import{g as q}from"./URLHelper-B9aplt5w.js";import{c as N}from"./useStation-DJgnSZIA.js";import"./zhandian-YaGuQZe6.js";const T={class:"onemap-panel-wrapper"},U={class:"chart-box"},$={class:"table-box"},j=L({__name:"waterPoolMonitoring",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(x,{emit:I}){const d=I,c=x,u=_({chartOption:C()}),n=_({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:a})=>{n.pagination.page=t,n.pagination.limit=a},layout:"total,sizes, jumper"},handleRowClick:t=>S(t),columns:[{width:200,label:"名称",prop:"name",sortable:!0},{width:80,label:"液位",prop:"level",formatter(t,a){return(a??"--")+" "+(t.level_unit??"")}},{width:160,label:"更新时间",prop:"time",sortable:!0}]}),k=N(),M=async()=>{var t,a;n.loading=!0;try{const e=await k.getLatestData({type:"水池监测站"});n.dataList=e||[];const l=[];e==null||e.map(o=>{var g,f;const i=(g=o.location)==null?void 0:g.split(",");if((i==null?void 0:i.length)===2){const h=new O({longitude:i[0],latitude:i[1],spatialReference:(f=c.view)==null?void 0:f.spatialReference});l.push({visible:!1,x:h.x,y:h.y,offsetY:-40,id:o.stationId,title:o.name,customComponent:w(y),customConfig:{info:{type:"attrs",imageUrl:o.imgs,stationId:o.stationId}},attributes:{path:c.menu.path,id:o.stationId,row:o},symbolConfig:{url:q("水池监测站.png")}})}});const s=((t=e.filter(o=>o.level_status!=="offline"))==null?void 0:t.length)??0,p=((a=e.filter(o=>o.level_status==="offline"))==null?void 0:a.length)??0,r=e.length;u.chartOption=C([{scale:r===0?"0":(s/r*100).toFixed(2),name:"online",nameAlias:"运行",value:s.toString(),valueAlias:s.toString()},{scale:r===0?"0":(p/r*100).toFixed(2),name:"offline",nameAlias:"离线",value:p.toString(),valueAlias:p.toString()}],"个","",0),d("addMarks",{windows:l,customWinComp:w(y)})}catch(e){console.dir(e)}n.loading=!1},S=async t=>{d("highlightMark",c.menu,t==null?void 0:t.stationId)};return V(()=>{M()}),(t,a)=>{const e=P,l=A("VChart"),s=R;return D(),F("div",T,[m(e,{title:"监测状态统计",type:"underline",style:{"margin-top":"0"}}),v("div",U,[m(l,{option:b(u).chartOption},null,8,["option"])]),v("div",$,[m(s,{config:b(n)},null,8,["config"])])])}}}),K=B(j,[["__scopeId","data-v-ab20e15e"]]);export{K as default};
