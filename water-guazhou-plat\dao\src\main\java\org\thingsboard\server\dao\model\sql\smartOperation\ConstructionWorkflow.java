package org.thingsboard.server.dao.model.sql.smartOperation;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class ConstructionWorkflow {
    // 业务阶段
    private SoGeneralSystemScope scope;

    // 所属工程code
    private String code;

    // 所属工程名称
    private String name;

    // 开始时间
    private Date startTime;

    // 结束时间
    private Date completeTime;

    // 处理人
    @ParseViaMapper(value = UserMapper.class, method = "getNameByMultiId")
    private String processUser;

    // 完成状态
    private SoGeneralTaskStatus status;

}
