package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecord;
import org.thingsboard.server.dao.sql.department.StoreOutRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordSaveRequest;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StoreOutRecordServiceImpl implements StoreOutRecordService {
    @Autowired
    private StoreOutRecordMapper mapper;

    @Autowired
    private StoreOutRecordDetailService service;

    @Override
    public IPage<StoreOutRecord> findAllConditional(StoreOutRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public StoreOutRecord save(StoreOutRecordSaveRequest entity) {
        service.checkInStorages(entity.getId(), entity.getItemIdList());
        StoreOutRecord result = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
        List<StoreOutRecordDetailSaveRequest> items = entity.getItems(result.getId());
        if (items != null) {
            // noinspection Convert2MethodRef
            service.removeAllByMainOnIdNotIn(result.getId(),
                    items.stream().map(x -> x.getId()).filter(x -> x != null).collect(Collectors.toList()));
            service.saveAll(items);
        }
        return result;
    }

    @Override
    public boolean update(StoreOutRecord attr) {
        return mapper.update(attr);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        service.deleteAll(mapper.childrenId(id));
        return mapper.deleteById(id) > 0;
    }

}
