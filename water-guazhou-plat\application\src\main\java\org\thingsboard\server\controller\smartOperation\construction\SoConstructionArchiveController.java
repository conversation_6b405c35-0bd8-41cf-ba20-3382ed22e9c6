package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchivePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionArchiveSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.SoConstructionArchiveService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionArchive")
public class SoConstructionArchiveController extends BaseController {
    @Autowired
    private SoConstructionArchiveService service;


    @GetMapping
    public IPage<SoConstructionArchive> findAllConditional(SoConstructionArchivePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionArchivePageRequest request) {
        return ExcelFileInfo.of("工程归档管理列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称")
                .nextTitle("constructionTypeName", "工程类别")
                .nextTitle("creatorName", "创建人")
                .nextTitle("createTimeName", "创建时间");
    }

    @PostMapping
    public SoConstructionArchive save(@RequestBody SoConstructionArchiveSaveRequest req) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        if (!service.isComplete(req.getConstructionCode(), tenantId)) {
            throw new ThingsboardException("不满足归档条件", ThingsboardErrorCode.GENERAL);
        }
        
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionArchiveSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}