import{_ as A}from"./index-C9hz-UZb.js";import{d as J,M as G,a6 as U,c as _,r as h,bF as b,a8 as v,s as q,bu as W,ah as $,ay as H,g as X,n as K,q as l,F as d,i as n,cs as I,bo as V,bR as O,p as B,j as Q,bB as Z,c5 as ee,dF as ae,dA as te,aq as se,al as oe,aj as ne,C as re}from"./index-r0dFAfgr.js";import{_ as ie}from"./CardSearch-CB_HNR-Q.js";import{u as le}from"./useStation-DJgnSZIA.js";import{f as ce}from"./formartColumn-D5r7JJ2G.js";import{c as me}from"./waterQualityMonitoring-BYvdS1Mm.js";import{a as de}from"./data-BotOAhUJ.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function pe(D,Y,C){return{name:"",grid:{left:80,right:80,top:80,bottom:50},legend:{type:"scroll",width:600,top:10,textStyle:{fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:D},yAxis:[{position:"left",type:"value",name:"",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{}},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{}},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{}]}}const ue={class:"wrapper"},fe={class:"chart-box"},ye=J({__name:"waterLine",setup(D){const{$messageWarning:Y}=G(),C=U(),x=_(),L=_(),c=_(),w=_();let M=h([]);const{getStationTree:E,getStationTreeByDisabledType:F}=le(),e=h({activeName:"day",activePattern:"echarts",stationId:"",chartOption:null,data:[],stationTree:[],chartName:""}),j=h({type:"tabs",tabType:"simple",width:"100%",field:"type",tabs:[{label:"日水质",value:"day"},{label:"月水质",value:"month"},{label:"年水质",value:"year"}],handleTabClick:r=>{var t;console.log("选择",r.props.name),e.activeName=r.props.name,(((t=c.value)==null?void 0:t.queryParams)||{}).stationIds&&P()}}),N=h({defaultParams:{day:b().format("YYYY-MM-DD"),month:b().format("YYYY-MM"),year:b().format("YYYY"),daterange:[b().format("YYYY-MM-DD"),b().format("YYYY-MM-DD")],attr:"remainder"},filters:[{type:"select-tree",label:"监测点:",defaultExpandAll:!0,field:"stationIds",clearable:!1,multiple:!0,width:"200px",showCheckbox:!0,options:v(()=>e.stationTree),onChange:()=>{P()}},{type:"select",label:"统计类型",field:"attr",width:"140px",options:[{label:"余氯",value:"remainder"},{label:"浊度",value:"turbidity"},{label:"PH",value:"ph"},{label:"溶氧",value:"oxygen"},{label:"电导率",value:"conductance"}]},{type:"date",label:"日期",field:"day",format:"YYYY-MM-DD",clearable:!1,hidden:v(()=>e.activeName==="year"||e.activeName==="month"||e.activeName==="daterange"),width:300},{type:"month",label:"日期",field:"month",format:"YYYY-MM",clearable:!1,hidden:v(()=>e.activeName==="year"||e.activeName==="day"||e.activeName==="daterange"),width:300},{type:"year",label:"日期",field:"year",format:"YYYY",clearable:!1,hidden:v(()=>e.activeName==="day"||e.activeName==="month"||e.activeName==="daterange"),width:300},{type:"daterange",label:"日期",format:"YYYY-MM",field:"daterange",clearable:!1,hidden:v(()=>e.activeName==="year"||e.activeName==="month"||e.activeName==="day"),width:300}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:q(oe),click:()=>{var a;(((a=c.value)==null?void 0:a.queryParams)||{}).stationIds?P():Y("请选择监测点")}},{perm:!0,text:"导出",type:"warning",svgIcon:q(ne),hide:()=>e.activePattern!=="list",click:()=>{var a,t;(((a=c.value)==null?void 0:a.queryParams)||{}).stationIds?(t=L.value)==null||t.exportTable():Y("请选择监测点")}}]}]}),g=h({loading:!1,dataList:[],columns:[],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),z=()=>{var m,y,p,T;const a=e.data.tableDataList.filter(o=>!/[\u4E00-\u9FA5]/g.test(o.ts)).map(o=>o.ts);console.log(a);const t=pe(a);t.series=[];const u={name:e.chartName,smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}},s=JSON.parse(JSON.stringify(u));s.data=(m=e.data)==null?void 0:m.tableDataList.map(o=>o.value),t.series.push(s),(y=e.data)==null||y.tableInfo.map(o=>{var k;if(o.columnValue!=="ts"){const S=JSON.parse(JSON.stringify(u));S.name=o.columnName,S.data=(k=e.data)==null?void 0:k.tableDataList.map(R=>R[o.columnValue]),t.series.push(S)}});const f=((p=c.value)==null?void 0:p.queryParams)||{},i=de.find(o=>o.value===f.attr);t.yAxis[0].name=(i==null?void 0:i.label)+"（"+(i==null?void 0:i.unit)+"）",(T=x.value)==null||T.clear(),Z(()=>{w.value&&C.listenTo(w.value,()=>{var o;e.chartOption=t,(o=x.value)==null||o.resize()})})},P=async()=>{var i,m;const r=((i=c.value)==null?void 0:i.queryParams)||{},a=r[e.activeName],t={stationIds:r.stationIds.join(","),queryType:e.activeName,time:a,attr:r.attr,stationType:"水质监测站"},s=(m=(await me(t)).data)==null?void 0:m.data,f=ce(s==null?void 0:s.tableInfo);M=s==null?void 0:s.tableDataList,g.columns=f,g.dataList=M,g.pagination.total=s==null?void 0:s.tableDataList.length,e.data=s,z()};return W(async()=>{var t;const r=["水质监测站"].join(","),a=await E(r);e.stationTree=a,await F(a,["Project"],!1,"Station"),$(a),N.defaultParams={...N.defaultParams},(t=c.value)==null||t.resetForm()}),(r,a)=>{const t=ee,u=ie,s=ae,f=te,i=se,m=H("VChart"),y=A;return X(),K("div",ue,[l(y,{class:"card",title:" "},{title:d(()=>[l(t,{modelValue:n(e).activeName,"onUpdate:modelValue":a[0]||(a[0]=p=>n(e).activeName=p),config:n(j)},null,8,["modelValue","config"])]),default:d(()=>[l(u,{ref_key:"refSearch",ref:c,config:n(N)},null,8,["config"]),l(y,{class:"SCard",title:n(e).activePattern==="list"?"水质数据列表":"水质曲线"},{right:d(()=>[l(f,{modelValue:n(e).activePattern,"onUpdate:modelValue":a[1]||(a[1]=p=>n(e).activePattern=p)},{default:d(()=>[l(s,{label:"echarts"},{default:d(()=>[l(n(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),l(s,{label:"list"},{default:d(()=>[l(n(I),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:d(()=>[V(B("div",fe,[l(i,{ref_key:"refCard",ref:L,config:n(g)},null,8,["config"])],512),[[O,n(e).activePattern==="list"]]),V(B("div",{ref_key:"agriEcoDev",ref:w,class:"chart-box"},[l(m,{ref_key:"refChart",ref:x,theme:n(Q)().isDark?"dark":"light",option:n(e).chartOption},null,8,["theme","option"])],512),[[O,n(e).activePattern==="echarts"]])]),_:1},8,["title"])]),_:1})])}}}),Pe=re(ye,[["__scopeId","data-v-9d5a08bd"]]);export{Pe as default};
