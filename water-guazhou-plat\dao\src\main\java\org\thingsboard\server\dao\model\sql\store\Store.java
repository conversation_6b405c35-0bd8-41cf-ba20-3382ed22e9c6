package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class Store implements Identifiable {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 仓库编码
    private String code;

    // 仓库名称
    private String name;

    // 仓库地址
    private String address;

    // 排序，升序
    private Integer orderNum;

    // 管理员ID
    @ParseUsername(withDepartment = true)
    private String managerId;

    // 备注
    private String remark;

    // 电话号码
    private String tel;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

}
