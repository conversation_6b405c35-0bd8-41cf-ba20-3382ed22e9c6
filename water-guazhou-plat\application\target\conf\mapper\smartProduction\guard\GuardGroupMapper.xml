<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardGroupMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        place_id,
        (select address from guard_place where id = #{placeId}) place_name,
        name,
        serial_no,
        department_id,
        department_get_name(department_id)                      department_name,
        head,
        creator,
        create_time,
        tenant_id,
        (select string_agg(first_name, '、')
         from guard_group_partner
                  join tb_user on guard_group_partner.user_id = tb_user.id
         where group_id = guard_group.id)                       partner_names
        <!--@sql from guard_group -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroup">
        <result column="id" property="id"/>
        <result column="place_id" property="placeId"/>
        <result column="place_name" property="placeName"/>
        <result column="name" property="name"/>
        <result column="serial_no" property="serialNo"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="head" property="head"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="partner_names" property="partnerNames"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_group
        <where>
            <if test="placeId != null and placeId != ''">
                and place_id = #{placeId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="updateFully">
        update guard_group
        set place_id      = #{placeId},
            name          = #{name},
            serial_no     = #{serialNo},
            department_id = #{departmentId},
            head          = #{head}
        where id = #{id}
    </update>
</mapper>