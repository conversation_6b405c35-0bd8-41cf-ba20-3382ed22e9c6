<!-- gis工单流程 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards v-model="cardsvalue" :span="12"></Cards>
    <Form ref="refForm" :config="FormConfig"> </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js';
import { Cards } from '../../components';
import { GetCompleteRatio, GetWorkOrderPage } from '@/api/workorder';
import { formatWorkOrderStatus } from '../../components/components/config';
import { ring } from '../../components/components/chart';
import { useAppStore } from '@/store';
// import { createGraphic } from '@/utils/MapHelper'
import WorkOrderLocatePop from '@/views/arcMap/components/WorkOrderLocatePop.vue';
import mapiconpPng from '../../../../assets/images/map-icon-p.png';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();

const refForm = ref<IFormIns>();

const cardsvalue = ref([
  { label: '0 条', value: '全年工单总数' },
  { label: '0.00 %', value: '全年完成率' },
  { label: '0 条', value: '当月工单总数' },
  { label: '0.00 %', value: '当月完成率' }
]);

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    layout: 'total,sizes,  jumper',
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1;
      TableConfig.pagination.limit = size || 20;
      refreshData();
    }
  },
  columns: [
    {
      label: '工单编号',
      prop: 'serialNo',
      minWidth: 120
    },
    {
      label: '处理人',
      prop: 'processUserName',
      minWidth: 150
    },
    {
      label: '状态',
      prop: 'status',
      formatter: (row, val) => formatWorkOrderStatus(val),
      minWidth: 120
    }
  ],
  handleRowClick: (row) => {
    TableConfig.currentRow = row;
    emit('highlightMark', props.menu, row?.id);
  }
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '工单类型占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'serialNo',
          placeholder: '单号',
          append: '刷新',
          onChange: () => refreshTable()
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});
const generatePops = (
  view?: __esri.MapView,
  data?: {
    id: string;
    title: string;
    coordinate?: string;
  }[],
  extentToMark?: (id: string) => void
) => {
  const pops: IArcPopConfig[] = [];
  data?.map((item) => {
    const coords = item.coordinate?.split(',');
    const lon = parseFloat(coords?.[1] || '0');
    const lat = parseFloat(coords?.[0] || '0');
    if (isNaN(lon) || isNaN(lat)) return;
    const pointGeometry = new Point({
      longitude: lon,
      latitude: lat,
      spatialReference: view?.spatialReference
    });
    const pop: IArcPopConfig = {
      id: item.id,
      visible: false,
      title: item.title,
      x: pointGeometry.x,
      y: pointGeometry.y,
      offsetY: -40,
      attributes: {
        row: item,
        path: props.menu.path,
        id: item.id
      },
      customComponent: shallowRef(WorkOrderLocatePop),
      customConfig: {
        ...item,
        extentTo: extentToMark
      },
      symbolConfig: {
        url: mapiconpPng,
        width: 20,
        height: 20,
        yoffset: 12
      }
    };
    pops.push(pop);
  }) || [];
  return pops;
};
const refreshTable = () => {
  GetWorkOrderPage({
    serialNo: refForm.value?.dataForm.serialNo,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20
  }).then((res) => {
    const data = res.data?.data;
    TableConfig.dataList = data?.data || [];
    TableConfig.pagination.total = data?.total || 0;
    const pops = generatePops(props.view, data?.data || [], (id: string) => {
      emit('highlightMark', props.menu, id, undefined, 19);
    });
    emit('addMarks', {
      windows: pops
    });
  });
};
const refreshData = () => {
  refreshTable();
  GetCompleteRatio().then((res) => {
    const data = res.data?.data || {};
    cardsvalue.value[0].label = (data.totalYearly || 0) + ' 条';
    cardsvalue.value[1].label =
      (data.percentYearly?.toFixed(2) || '0.00') + ' %';
    cardsvalue.value[2].label = (data.totalMonthly || 0) + ' 条';
    cardsvalue.value[3].label =
      (data.percentMonthly?.toFixed(2) || '0.00') + ' %';
    const field = FormConfig.group[0].fields[0] as IFormVChart;

    const chartData = data.data?.data?.map((item) => {
      return {
        name: item.key,
        nameAlias: item.key,
        value: item.value,
        valueAlias: item.value,
        scale: ((item.percentage || 0) * 100).toFixed(2) + '%'
      };
    });
    field.option = ring(chartData, '条', undefined, 2);
  });
};
onMounted(() => {
  refreshData();
});
watch(
  () => useAppStore().isDark,
  () => refreshData()
);
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 395px);
}
</style>
