<template>
  <div class="vertical-menu">
    <el-collapse
      v-model="state.collapse"
      accordion
      class="collapse-wrapper"
    >
      <el-collapse-item name="1">
        <template #title>
          <el-icon>
            <i
              v-if="state.current?.meta.icon"
              :class="state.current?.meta.icon"
            ></i>
            <component
              :is="state.current?.meta.svgIcon"
              v-else-if="state.current?.meta.svgIcon"
            ></component>
          </el-icon>
          <span class="current-title">
            {{ state.current?.meta.title }}
          </span>
          <!-- <el-icon class="header-icon">
          <info-filled />
        </el-icon> -->
        </template>
        <div
          v-for="(menu, i) in menus"
          :key="i"
          class="menu-item"
          :class="[state.current?.path === menu.path ? 'active' : '']"
          @click="() => handleClick(menu)"
        >
          <el-icon>
            <i
              v-if="menu.meta.icon"
              :class="menu.meta.icon"
            ></i>
            <component
              :is="menu.meta.svgIcon"
              v-else-if="menu.meta.svgIcon"
            ></component>
          </el-icon>
          <span>{{ menu.meta.title }}</span>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  collapseOnClick?: boolean
  menus: IMenuItem[]
}>()
const state = reactive<{
  collapse: string
  current?: IMenuItem
}>({
  collapse: '',
  // menus: ['实时设备', '设备资产', '人员设备', '业务流程', '数据场景', '应急调度'],
  current: undefined
})
const emit = defineEmits(['change'])
const handleClick = menu => {
  state.current = menu
  props.collapseOnClick && (state.collapse = '')
  emit('change', menu)
}
watch(
  () => props.menus,
  () => {
    state.current = props.menus[0]
  }
)
</script>
<style lang="scss" scoped>
.dark,
.darkblue {
  .vertical-menu {
    background-color: var(--el-bg-color);
  }

  .collapse-wrapper {
    background-color: var(--el-bg-color);
  }
  .menu-item {
    &:hover,
    &.active {
      background-color: var(--el-bg-color-page);
    }
  }
  :deep(.el-collapse-item__header) {
    background-color: var(--el-bg-color);
    &.is-active {
      background-color: var(--el-bg-color-page);
    }
  }
}
.vertical-menu {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--el-text-color-regular);
  border-radius: 4px;
}
.collapse-wrapper {
  border-radius: 4px;
  // background-color: rgba(21, 45, 68, 0.9);
  background-color: transparent;
  --el-collapse-header-height: 40px;
}
:deep(.el-collapse-item__header) {
  border-radius: 4px;
  padding-left: 15px;
}
:deep(.el-collapse-item__wrap) {
  background-color: transparent;
  border-bottom: none;
}
.current-title {
  padding: 0;
  overflow: hidden;
  white-space: nowrap;
}
.menu-item {
  display: flex;
  user-select: none;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
}
.menu-item {
  &:hover {
    background-color: #f4f7fa;
  }
  &.active {
    background-color: #e7ebf4;
  }
}
.el-icon + span {
  margin-left: 8px;
}
</style>
