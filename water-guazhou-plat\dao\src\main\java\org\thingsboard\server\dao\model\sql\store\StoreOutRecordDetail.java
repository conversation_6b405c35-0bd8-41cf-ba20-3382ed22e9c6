package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class StoreOutRecordDetail {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 设备序列号
    private String serialId;

    // 出库单主表ID
    private String mainId;

    // 货架Id
    private String shelvesId;

    // 出库时间
    private Date outTime;

    // 目标出货数量
    private Integer num;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
