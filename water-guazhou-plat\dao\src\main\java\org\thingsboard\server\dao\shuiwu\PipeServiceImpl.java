package org.thingsboard.server.dao.shuiwu;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.PipeEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.PipePointEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.shuiwu.PipePointRepository;
import org.thingsboard.server.dao.sql.shuiwu.PipeRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PipeServiceImpl implements PipeService {

    @Autowired
    private PipeRepository pipeRepository;

    @Autowired
    private PipePointRepository pipePointRepository;

    @Autowired
    private AssetsAccountService assetsAccountService;

    @Override
    public PipeEntity findById(String id) {
        PipeEntity entity = pipeRepository.findOne(id);

        // 管道节点ID
        String startPointId = entity.getStartPointId();
        String endPointId = entity.getEndPointId();

        if (StringUtils.isNotBlank(startPointId)) {
            PipePointEntity point = pipePointRepository.findOne(startPointId);
            if (point != null) {
                entity.setStartPoint(point);
            }
        }

        if (StringUtils.isNotBlank(endPointId)) {
            PipePointEntity point = pipePointRepository.findOne(endPointId);
            if (point != null) {
                entity.setEndPoint(point);
            }
        }

        return entity;
    }

    @Override
    public PageData<PipeEntity> findList(int page, int size, String keyword, TenantId tenantId) {
        // 分页
        PageRequest pageable = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "createTime"));

        List<PipePointEntity> pipePointList = pipePointRepository.findAll();
        Map<String, PipePointEntity> pipePointMap = new HashMap<>();
        pipePointList.forEach(pipePointEntity -> pipePointMap.put(pipePointEntity.getId(), pipePointEntity));

        Page<PipeEntity> pageResult = pipeRepository.findList(keyword, UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);

        List<PipeEntity> list = pageResult.getContent();
        for (PipeEntity pipe : list) {
            // 设置起点终点
            PipePointEntity startPoint = pipePointMap.get(pipe.getStartPointId());
            if (startPoint != null) {
                pipe.setStartPointLocation(startPoint.getLocation());
            }
            PipePointEntity endPoint = pipePointMap.get(pipe.getEndPointId());
            if (endPoint != null) {
                pipe.setEndPointLocation(endPoint.getLocation());
            }
        }

        return new PageData<>(pageResult.getTotalElements(), list);
    }

    @Override
    public void save(PipeEntity entity) {
        String id = entity.getId();
        PipeEntity save = pipeRepository.save(entity);

        // 同步设备到资产
        try {
            AssetsAccountEntity assetsAccountEntity = new AssetsAccountEntity();
            if (id != null) {
                assetsAccountEntity = assetsAccountService.findByDeviceId(id);
            } else {
                assetsAccountEntity.setDeviceId(save.getId());
            }
            assetsAccountEntity.setDeviceName(entity.getName());
            assetsAccountEntity.setDeviceType("管道");
            assetsAccountService.save(assetsAccountEntity);
        } catch (Exception e) {
            log.error("同步管道到设备资产错误, e = ", e);
        }
    }

    @Override
    public void deleteById(String id) {
        // 查询
        PipeEntity entity = pipeRepository.findOne(id);
        if (entity == null) {
            return;
        }
        entity.setIsDel("1");

        pipeRepository.save(entity);
    }

    @Override
    public PipePointEntity findPipePointById(String pipePointId) {
        return pipePointRepository.findOne(pipePointId);
    }

    @Override
    public PageData<PipePointEntity> findPipePointList(int page, int size, String keyword, TenantId tenantId) {
        // 分页
        PageRequest pageable = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "createTime"));

        Page<PipePointEntity> pageResult = pipePointRepository.finList(keyword, UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public void savePipePoint(PipePointEntity entity) {
        pipePointRepository.save(entity);
    }

    @Override
    public void removePipePoints(List<String> ids) {
        for (String id : ids) {
            pipePointRepository.delete(id);
        }
    }

    @Override
    public void deleteById(List<String> ids) {
        for (String id : ids) {
            this.deleteById(id);
        }
    }

    @Override
    public List<PipeEntity> findAll(TenantId tenantId) {
        return pipeRepository.findAllByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }
}
