package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionTaskInfo;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoSaveRequest;
import org.thingsboard.server.dao.construction.SoConstructionTaskInfoService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionTaskInfo")
public class SoConstructionTaskInfoController extends BaseController {
    @Autowired
    private SoConstructionTaskInfoService service;


    // @GetMapping
    public IPage<SoConstructionTaskInfo> findAllConditional(SoConstructionTaskInfoPageRequest request) {
        return service.findAllConditional(request);
    }

    // @PostMapping
    public SoConstructionTaskInfo save(@RequestBody SoConstructionTaskInfoSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionTaskInfoSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}