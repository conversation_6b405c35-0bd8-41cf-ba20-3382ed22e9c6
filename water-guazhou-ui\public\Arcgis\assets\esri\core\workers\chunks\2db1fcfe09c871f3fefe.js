"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4166],{16453:(e,t,r)=>{r.d(t,{R:()=>m,w:()=>w});var s=r(43697),i=r(15923),o=r(70586),a=r(41103),n=r(22974),l=r(31263);class u{constructor(){this._propertyOriginMap=new Map,this._originStores=new Array(l.kk),this._values=new Map,this.multipleOriginsSupported=!0}clone(e){const t=new u,r=this._originStores[l.s3.DEFAULTS];r&&r.forEach(((e,r)=>{t.set(r,(0,n.d9)(e),l.s3.DEFAULTS)}));for(let r=l.s3.SERVICE;r<l.kk;r++){const s=this._originStores[r];s&&s.forEach(((s,i)=>{e&&e.has(i)||t.set(i,(0,n.d9)(s),r)}))}return t}get(e,t){const r=void 0===t?this._values:this._originStores[t];return r?r.get(e):void 0}keys(e){const t=null==e?this._values:this._originStores[e];return t?[...t.keys()]:[]}set(e,t,r=l.s3.USER){let s=this._originStores[r];if(s||(s=new Map,this._originStores[r]=s),s.set(e,t),!this._values.has(e)||(0,o.j0)(this._propertyOriginMap.get(e))<=r){const s=this._values.get(e);return this._values.set(e,t),this._propertyOriginMap.set(e,r),s!==t}return!1}delete(e,t=l.s3.USER){const r=this._originStores[t];if(!r)return;const s=r.get(e);if(r.delete(e),this._values.has(e)&&this._propertyOriginMap.get(e)===t){this._values.delete(e);for(let r=t-1;r>=0;r--){const t=this._originStores[r];if(t&&t.has(e)){this._values.set(e,t.get(e)),this._propertyOriginMap.set(e,r);break}}}return s}has(e,t){const r=void 0===t?this._values:this._originStores[t];return!!r&&r.has(e)}revert(e,t){for(;t>0&&!this.has(e,t);)--t;const r=this._originStores[t],s=r&&r.get(e),i=this._values.get(e);return this._values.set(e,s),this._propertyOriginMap.set(e,t),i!==s}originOf(e){return this._propertyOriginMap.get(e)||l.s3.DEFAULTS}forEach(e){this._values.forEach(e)}}var p=r(50549),h=r(1153),d=r(52011);const c=e=>{let t=class extends e{constructor(...e){super(...e);const t=(0,o.j0)((0,h.vw)(this)),r=t.store,s=new u;t.store=s,(0,a.M)(t,r,s)}read(e,t){(0,p.i)(this,e,t)}getAtOrigin(e,t){const r=g(this),s=(0,l.M9)(t);if("string"==typeof e)return r.get(e,s);const i={};return e.forEach((e=>{i[e]=r.get(e,s)})),i}originOf(e){return(0,l.x3)(this.originIdOf(e))}originIdOf(e){return g(this).originOf(e)}revert(e,t){const r=g(this),s=(0,l.M9)(t),i=(0,h.vw)(this);let o;o="string"==typeof e?"*"===e?r.keys(s):[e]:e,o.forEach((e=>{i.invalidate(e),r.revert(e,s),i.commit(e)}))}};return t=(0,s._)([(0,d.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],t),t};function g(e){return(0,h.vw)(e).store}let f=class extends(c(i.Z)){};f=(0,s._)([(0,d.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],f);var y=r(76169);const v=e=>{let t=class extends e{constructor(...e){super(...e)}clear(e,t="user"){return _(this).delete(e,(0,l.M9)(t))}write(e={},t){return(0,y.c)(this,e=e||{},t),e}setAtOrigin(e,t,r){(0,h.vw)(this).setAtOrigin(e,t,(0,l.M9)(r))}removeOrigin(e){const t=_(this),r=(0,l.M9)(e),s=t.keys(r);for(const e of s)t.originOf(e)===r&&t.set(e,t.get(e,r),l.s3.USER)}updateOrigin(e,t){const r=_(this),s=(0,l.M9)(t),i=this.get(e);for(let t=s+1;t<l.kk;++t)r.delete(e,t);r.set(e,i,s)}toJSON(e){return this.write({},e)}};return t=(0,s._)([(0,d.j)("esri.core.WriteableMultiOriginJSONSupport")],t),t.prototype.toJSON.isDefaultToJSON=!0,t};function _(e){return(0,h.vw)(e).store}const m=e=>{let t=class extends(v(c(e))){constructor(...e){super(...e)}};return t=(0,s._)([(0,d.j)("esri.core.MultiOriginJSONSupport")],t),t};let w=class extends(m(i.Z)){};w=(0,s._)([(0,d.j)("esri.core.MultiOriginJSONSupport")],w)},44166:(e,t,r)=>{r.r(t),r.d(t,{default:()=>d});var s=r(43697),i=r(20102),o=r(16453),a=r(1654),n=r(5600),l=(r(75215),r(67676),r(52011)),u=r(87085),p=r(16859);let h=class extends((0,p.I)((0,o.R)(u.Z))){constructor(e){super(e),this.resourceInfo=null,this.type="unknown"}initialize(){this.addResolvingPromise(new Promise(((e,t)=>{(0,a.Os)((()=>{const e=this.resourceInfo&&(this.resourceInfo.layerType||this.resourceInfo.type);let r="Unknown layer type";e&&(r+=" "+e),t(new i.Z("layer:unknown-layer-type",r,{layerType:e}))}))})))}read(e,t){super.read({resourceInfo:e},t)}write(){return null}};(0,s._)([(0,n.Cb)({readOnly:!0})],h.prototype,"resourceInfo",void 0),(0,s._)([(0,n.Cb)({type:["show","hide"]})],h.prototype,"listMode",void 0),(0,s._)([(0,n.Cb)({json:{read:!1},readOnly:!0,value:"unknown"})],h.prototype,"type",void 0),h=(0,s._)([(0,l.j)("esri.layers.UnknownLayer")],h);const d=h},16859:(e,t,r)=>{r.d(t,{I:()=>O});var s=r(43697),i=r(68773),o=r(40330),a=r(3172),n=r(66643),l=r(20102),u=r(92604),p=r(70586),h=r(95330),d=r(17452),c=r(5600),g=(r(75215),r(67676),r(71715)),f=r(52011),y=r(30556),v=r(84230),_=r(65587),m=r(15235),w=r(86082),I=r(14661);const O=e=>{let t=class extends e{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,p.SC)(this.portalItem)}set portalItem(e){e!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",e))}readPortalItem(e,t,r){if(t.itemId)return new m.default({id:t.itemId,portal:r&&r.portal})}writePortalItem(e,t){e&&e.id&&(t.itemId=e.id)}async loadFromPortal(e,t){if(this.portalItem&&this.portalItem.id)try{const s=await r.e(8062).then(r.bind(r,18062));return(0,h.k_)(t),await s.load({instance:this,supportedTypes:e.supportedTypes,validateItem:e.validateItem,supportsData:e.supportsData,layerModuleTypeMap:e.layerModuleTypeMap},t)}catch(e){throw(0,h.D_)(e)||u.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${e}`),e}}async finishLoadEditablePortalLayer(e){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(e).catch((e=>((0,h.r9)(e),!0))))}async _setUserPrivileges(e,t){if(!i.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(t);if(this.url)try{const{features:{edit:r,fullEdit:s},content:{updateItem:i}}=await this._fetchUserPrivileges(e,t);this._set("userHasEditingPrivileges",r),this._set("userHasFullEditingPrivileges",s),this._set("userHasUpdateItemPrivileges",i)}catch(e){(0,h.r9)(e)}}async _fetchUserPrivileges(e,t){let r=this.portalItem;if(!e||!r||!r.loaded||r.sourceUrl)return this._fetchFallbackUserPrivileges(t);const s=e===r.id;if(s&&r.portal.user)return(0,I.Ss)(r);let i,a;if(s)i=r.portal.url;else try{i=await(0,v.oP)(this.url,t)}catch(e){(0,h.r9)(e)}if(!i||!(0,d.Zo)(i,r.portal.url))return this._fetchFallbackUserPrivileges(t);try{const e=(0,p.pC)(t)?t.signal:null;a=await(o.id?.getCredential(`${i}/sharing`,{prompt:!1,signal:e}))}catch(e){(0,h.r9)(e)}if(!a)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(s?await r.reload():(r=new m.default({id:e,portal:{url:i}}),await r.load(t)),r.portal.user)return(0,I.Ss)(r)}catch(e){(0,h.r9)(e)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(e){let t=!0;try{t=await this._fetchUserHasEditingPrivileges(e)}catch(e){(0,h.r9)(e)}return{features:{edit:t,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(e){const t=this.url?o.id?.findCredential(this.url):null;if(!t)return!0;const r=S.credential===t?S.user:await this._fetchEditingUser(e);return S.credential=t,S.user=r,(0,p.Wi)(r)||null==r.privileges||r.privileges.includes("features:user:edit")}async _fetchEditingUser(e){const t=this.portalItem?.portal?.user;if(t)return t;const r=o.id.findServerInfo(this.url??"");if(!r?.owningSystemUrl)return null;const s=`${r.owningSystemUrl}/sharing/rest`,i=_.Z.getDefault();if(i&&i.loaded&&(0,d.Fv)(i.restUrl)===(0,d.Fv)(s))return i.user;const l=`${s}/community/self`,u=(0,p.pC)(e)?e.signal:null,h=await(0,n.q6)((0,a.default)(l,{authMode:"no-prompt",query:{f:"json"},signal:u}));return h.ok?w.default.fromJSON(h.value.data):null}read(e,t){t&&(t.layer=this),super.read(e,t)}write(e,t){const r=t&&t.portal,s=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||_.Z.getDefault());return r&&s&&!(0,d.tm)(s.restUrl,r.restUrl)?(t.messages&&t.messages.push(new l.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(e,{...t,layer:this})}};return(0,s._)([(0,c.Cb)({type:m.default})],t.prototype,"portalItem",null),(0,s._)([(0,g.r)("web-document","portalItem",["itemId"])],t.prototype,"readPortalItem",null),(0,s._)([(0,y.c)("web-document","portalItem",{itemId:{type:String}})],t.prototype,"writePortalItem",null),(0,s._)([(0,c.Cb)({clonable:!1})],t.prototype,"resourceReferences",void 0),(0,s._)([(0,c.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasEditingPrivileges",void 0),(0,s._)([(0,c.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasFullEditingPrivileges",void 0),(0,s._)([(0,c.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasUpdateItemPrivileges",void 0),t=(0,s._)([(0,f.j)("esri.layers.mixins.PortalLayer")],t),t},S={credential:null,user:null}}}]);