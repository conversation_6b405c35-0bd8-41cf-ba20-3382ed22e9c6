import{d as l,g as n,n as s,q as e,F as _,N as c,O as p,C as i}from"./index-r0dFAfgr.js";const m={class:"team_table"},r=l({__name:"teamTable",props:{config:{}},setup(d){return(t,b)=>{const a=c,o=p;return n(),s("div",m,[e(o,{data:t.config.maintainCircuitTeamCList,style:{width:"100%"}},{default:_(()=>[e(a,{type:"index",label:"序号",width:"60"}),e(a,{prop:"email",label:"成员账户"}),e(a,{prop:"userName",label:"成员名称"})]),_:1},8,["data"])])}}}),f=i(r,[["__scopeId","data-v-7358b21b"]]);export{f as default};
