import{T as y}from"./index-r0dFAfgr.js";import{dN as f,cx as h}from"./MapView-DaoQedLH.js";import{t as z}from"./color-DAS1c3my.js";import{c as g}from"./definitions-826PWLuy.js";import{L as l}from"./enums-L38xj_2E.js";import{l as S}from"./visualVariablesUtils-7_6yXvXo.js";const $=8388607,v=8388608,x=254,E=255,L=0,m=1,d=e=>(e&v)>>>23,T=e=>e&$,R=e=>d(e)===m?x:E;function U(e){return d(e)===m}function Z(e,t){return((t?v:0)|e)>>>0}function k(e,t){if(!e||!t)return e;switch(t){case"radius":case"distance":return 2*e;case"diameter":case"width":return e;case"area":return Math.sqrt(e)}return e}function V(e){return{value:e.value,size:f(e.size)}}function a(e){return(e??[]).map(t=>V(t))}function u(e){if(typeof e=="string"||typeof e=="number")return f(e);const t=e;return{type:"size",expression:t.expression,stops:a(t.stops)}}const c=e=>{const t=[],n=[],s=a(e),o=s.length;for(let i=0;i<6;i++){const r=s[Math.min(i,o-1)];t.push(r.value),n.push(r.size==null?g:h(r.size))}return{values:new Float32Array(t),sizes:new Float32Array(n)}};function N(e){const t=e&&e.length>0?{}:null,n=t?{}:null;if(!t||!n)return{vvFields:t,vvRanges:n};for(const s of e)if(s.field&&(t[s.type]=s.field),s.type==="size"){n.size||(n.size={});const o=s;switch(S(o)){case l.SIZE_MINMAX_VALUE:n.size.minMaxValue={minDataValue:o.minDataValue,maxDataValue:o.maxDataValue,minSize:u(o.minSize),maxSize:u(o.maxSize)};break;case l.SIZE_SCALE_STOPS:n.size.scaleStops={stops:a(o.stops)};break;case l.SIZE_FIELD_STOPS:if(o.levels){const i={};for(const r in o.levels)i[r]=c(o.levels[r]);n.size.fieldStops={type:"level-dependent",levels:i}}else n.size.fieldStops={type:"static",...c(o.stops)};break;case l.SIZE_UNIT_VALUE:n.size.unitValue={unit:o.valueUnit,valueRepresentation:o.valueRepresentation??void 0}}}else if(s.type==="color")n.color=I(s);else if(s.type==="opacity")n.opacity=_(s);else if(s.type==="rotation"){const o=s;n.rotation={type:o.rotationType}}return{vvFields:t,vvRanges:n}}function _(e){const t={values:[0,0,0,0,0,0,0,0],opacities:[0,0,0,0,0,0,0,0]};if(typeof e.field=="string"){if(!e.stops)return null;{if(e.stops.length>8)return null;const n=e.stops;for(let s=0;s<8;++s){const o=n[Math.min(s,n.length-1)];t.values[s]=o.value,t.opacities[s]=o.opacity}}}else{if(!(e.stops&&e.stops.length>=0))return null;{const n=e.stops&&e.stops.length>=0?e.stops[0].opacity:0;for(let s=0;s<8;s++)t.values[s]=1/0,t.opacities[s]=n}}return t}function p(e,t,n){e[4*t+0]=n.r/255,e[4*t+1]=n.g/255,e[4*t+2]=n.b/255,e[4*t+3]=n.a}function I(e){if(y(e)||e.normalizationField)return null;const t={field:null,values:[0,0,0,0,0,0,0,0],colors:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]};if(typeof e.field=="string"){if(!e.stops)return null;{if(e.stops.length>8)return null;t.field=e.field;const n=e.stops;for(let s=0;s<8;++s){const o=n[Math.min(s,n.length-1)];t.values[s]=o.value,p(t.colors,s,o.color)}}}else{if(!(e.stops&&e.stops.length>=0))return null;{const n=e.stops&&e.stops.length>=0&&e.stops[0].color;for(let s=0;s<8;s++)t.values[s]=1/0,p(t.colors,s,n)}}for(let n=0;n<32;n+=4)z(t.colors,n,!0);return t}export{N as a,m as c,d as e,T as f,R as i,$ as n,U as p,k as r,Z as s,L as u};
