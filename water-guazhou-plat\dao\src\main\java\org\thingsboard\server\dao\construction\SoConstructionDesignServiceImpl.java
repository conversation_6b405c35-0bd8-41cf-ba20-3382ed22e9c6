package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesign;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionDesignMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignSaveRequest;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_DESIGN_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_DESIGN;

@Service
public class SoConstructionDesignServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionDesign> implements SoConstructionDesignService {
    @Autowired
    private SoConstructionDesignMapper mapper;

    @Override
    public IPage<SoConstructionDesign> findAllConditional(SoConstructionDesignPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionDesign save(SoConstructionDesignSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> commonSave(entity, e), mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionDesign entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean complete(String constructionCode, String userId, String tenantId) {
        boolean success = taskInfoService.markAsComplete(constructionCode, tenantId, getCurrentScope());
        if (success) {
            recordService.recordComplete(tenantId, userId, constructionCode, getCurrentJournalType());
        }
        return success;
    }

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_DESIGN;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_DESIGN_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionDesign> getDirectMapper() {
        return mapper;
    }

}
