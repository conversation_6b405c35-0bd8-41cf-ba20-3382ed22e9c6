package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_gan_repair")
@Data
public class GanRepair {
    @TableId
    private String id;

    private String name;

    private String code;

    private String idCard;

    private String phone;

    private String address;

    private String status;

    @TableField(exist = false)
    private String auditor;

    @TableField(exist = false)
    private String auditorRemark;

    @TableField(exist = false)
    private String auditTime;

    private String img;

    private String remark;

    private Integer currentStepNo;

    private Date createTime;

    private String userId;

    private String evaluateGrade;

    private String evaluateContent;

    private String source;

    @TableField(exist = false)
    private Date completeTime;

    @TableField(exist = false)
    private String processTime;

    @TableField(exist = false)
    private String processName;

    @TableField(exist = false)
    private Integer stepNo;

    private String isSendMsg;

    private String meterCode;

}
