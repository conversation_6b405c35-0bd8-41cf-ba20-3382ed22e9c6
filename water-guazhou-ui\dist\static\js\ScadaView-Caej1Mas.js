import{u as o}from"./useStation-DJgnSZIA.js";import{d as r,c as n,o as c,g as i,n as _,p as d,i as l,C as u}from"./index-r0dFAfgr.js";import"./zhandian-YaGuQZe6.js";const f={class:"item_center_big_bg"},m=["src"],p=r({__name:"ScadaView",setup(h){const t=o(),a=n(""),s=async()=>{var e;await t.getStationOption("水厂",void 0,!0),a.value=(e=t.StationList.value[0])==null?void 0:e.data.scadaUrl};return c(()=>{s()}),(e,g)=>(i(),_("div",f,[d("iframe",{ref:"iframe",frameborder:"0",scrolling:"auto",src:l(a),width:"100%",height:"100%"},null,8,m)]))}}),w=u(p,[["__scopeId","data-v-b9783334"]]);export{w as default};
