package org.thingsboard.server.dao.sql.groundwater;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 地下水水位 Mapper
 */
@Mapper
public interface GroundwaterLevelMapper extends BaseMapper<GroundwaterLevel> {

    /**
     * 分页查询水位数据
     * @param page 分页参数
     * @param stationId 测点ID
     * @param tenantId 租户ID
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页数据
     */
    List<GroundwaterLevel> findList(Page<GroundwaterLevel> page,
                                   @Param("stationId") String stationId,
                                   @Param("stationName") String stationName,
                                   @Param("tenantId") String tenantId,
                                   @Param("status") Integer status,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime);

    /**
     * 查询水位数据总数
     * @param stationId 测点ID
     * @param tenantId 租户ID
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 总数
     */
    Integer findListCount(@Param("stationId") String stationId,
                          @Param("stationName") String stationName,
                         @Param("tenantId") String tenantId,
                         @Param("status") Integer status,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);

    /**
     * 获取指定时间范围内的水位数据
     * @param stationId 测点ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 水位数据列表
     */
    List<GroundwaterLevel> findByStationIdAndTimeBetween(@Param("stationId") String stationId,
                                                        @Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime);

    /**
     * 根据ID查询水位数据，并关联测点表获取测点名称
     * @param id 水位ID
     * @return 水位数据对象
     */
    GroundwaterLevel findDetailById(@Param("id") String id);

    /**
     * 使用Map参数查询水位数据
     * @param page 分页参数
     * @param params 查询参数
     * @return 水位数据列表
     */
    IPage<GroundwaterLevel> getList(Page<GroundwaterLevel> page, @Param("params") Map<String, Object> params);
}