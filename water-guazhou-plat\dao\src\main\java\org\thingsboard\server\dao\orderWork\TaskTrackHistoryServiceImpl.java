package org.thingsboard.server.dao.orderWork;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.TaskTrackHistoryEntity;
import org.thingsboard.server.dao.sql.workOrder.TaskTrackHistoryRepository;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@Deprecated
public class TaskTrackHistoryServiceImpl implements TaskTrackHistoryService {

    @Autowired
    private TaskTrackHistoryRepository taskTrackHistoryRepository;

    @Override
    public TaskTrackHistoryEntity save(TaskTrackHistoryEntity entity) {
        entity.setTs(new Date());
        return taskTrackHistoryRepository.save(entity);
    }

    @Override
    public List<TaskTrackHistoryEntity> findByContentId(String contentId) {
        return taskTrackHistoryRepository.findByContentIdOrderByTs(contentId);
    }
}
