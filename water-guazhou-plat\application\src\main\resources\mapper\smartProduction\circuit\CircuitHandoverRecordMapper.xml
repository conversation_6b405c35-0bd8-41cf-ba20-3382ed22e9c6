<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitHandoverRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           duty_location,
                           om_remark,
                           pump_room_status,
                           record,
                           record_time,
                           shift_time,
                           handover_person,
                           takeover_person,
                           handover_remark,
                           tenant_id<!--@sql from sp_circuit_handover_record -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord">
        <result column="id" property="id"/>
        <result column="duty_location" property="dutyLocation"/>
        <result column="om_remark" property="omRemark"/>
        <result column="pump_room_status" property="pumpRoomStatus"/>
        <result column="record" property="record"/>
        <result column="record_time" property="recordTime"/>
        <result column="shift_time" property="shiftTime"/>
        <result column="handover_person" property="handoverPerson"/>
        <result column="takeover_person" property="takeoverPerson"/>
        <result column="handover_remark" property="handoverRemark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_circuit_handover_record
        <where>
            <if test="keyWord != null">
                (duty_location like '%' || #{keyWord} || '%'
                   or om_remark like '%' || #{keyWord} || '%'
                   or record like '%' || #{keyWord} || '%'
                   or handover_person like '%' || #{keyWord} || '%'
                   or takeover_person like '%' || #{keyWord} || '%'
                   or handover_remark like '%' || #{keyWord} || '%')
            </if>
            <if test="pumpRoomStatus != null">
                and pump_room_status = #{pumpRoomStatus}
            </if>
            <if test="shiftTimeFrom != null">
                and shift_time >= #{shiftTimeFrom}
            </if>
            <if test="shiftTimeTo != null">
                and shift_time &lt;= #{shiftTimeTo}
            </if>
            <if test="handoverPerson != null">
                and handover_person like '%' || #{handoverPerson} || '%'
            </if>
            <if test="takeoverPerson != null">
                and takeover_person like '%' || #{takeoverPerson} || '%'
            </if>
            <if test="fromTime != null">
                and record_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and record_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by record_time desc
    </select>

    <update id="update">
        update sp_circuit_handover_record
        <set>
            <if test="dutyLocation != null">
                duty_location = #{dutyLocation},
            </if>
            <if test="omRemark != null">
                om_remark = #{omRemark},
            </if>
            <if test="pumpRoomStatus != null">
                pump_room_status = #{pumpRoomStatus},
            </if>
            <if test="record != null">
                record = #{record},
            </if>
            <if test="shiftTime != null">
                shift_time = #{shiftTime},
            </if>
            <if test="handoverPerson != null">
                handover_person = #{handoverPerson},
            </if>
            <if test="takeoverPerson != null">
                takeover_person = #{takeoverPerson},
            </if>
            <if test="handoverRemark != null">
                handover_remark = #{handoverRemark},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>