import{d as E,c as g,r as d,a8 as b,x as r,S as F,a9 as B,o as M,g as W,n as N,q as u,i as m,F as q,aq as O,b6 as z,C as S}from"./index-r0dFAfgr.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{w as R,b as j,x as A}from"./manage-BReaEVJk.js";import{k as $,l as H}from"./device-DWHb0XjG.js";import{h as U}from"./projectManagement-CDcrrCQ1.js";import G from"./detail-DEo1RlcF.js";import{f as x}from"./DateFormatter-Bm9a68Ax.js";import{I as J}from"./common-CvK_P_ao.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./CardTable-rdWOL4_6.js";import"./index-C9hz-UZb.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const K={class:"team_table"},Q=E({__name:"detail",props:{config:{}},emits:["extendedReturn"],setup(_,{emit:T}){const v=g(),y=g(),h=g(),f=g(),k=_,V=T,w=d({loading:!1,indexVisible:!0,dataList:b(()=>k.config.items),columns:[{prop:"code",label:"合同编号"},{prop:"name",label:"合同名称"},{prop:"typeName",label:"合同类型"},{prop:"firstpartOrganization",label:"甲方单位"},{prop:"secondpartOrganization",label:"乙方单位"},{prop:"cost",label:"合同总金额(万元)"},{prop:"signTime",label:"签署时间",formatter:e=>x(e.signTime,"YYYY-MM-DD")},{prop:"workTimeBegin",label:"合同工期(开始时间)",minWidth:"100px",formatter:e=>x(e.workTimeBegin,"YYYY-MM-DD")},{prop:"workTimeEnd",label:"合同工期(完成时间)",minWidth:"100px",formatter:e=>x(e.workTimeEnd,"YYYY-MM-DD")},{prop:"creatorName",label:"创建人"},{prop:"createTime",label:"创建时间",formatter:e=>x(e.createTime,"YYYY-MM-DD HH:mm:ss")}],operationWidth:"260px",operations:[{text:"详情",perm:!0,click:e=>{var t;o.selected=e,(t=y.value)==null||t.openDrawer()}},{text:"查看设备",perm:!0,click:e=>{var t;c.defaultValue={code:e.code,constructionCode:e.constructionCode},o.getProjectDeviceValue(),(t=h.value)==null||t.openDialog()}},{text:"编辑",perm:!0,click:e=>L(e)},{text:"添加设备",perm:!0,click:e=>{var i;c.defaultValue={code:e.code,constructionCode:e.constructionCode};const t=p.group[0].fields.find(a=>a.type==="table");t.config.selectList=[],p.defaultValue={code:e.code},o.geDeviceListValue(),(i=f.value)==null||i.openDialog()}}],pagination:{hide:!0}}),s=d({title:"添加工程合同",appendToBody:!0,labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{s.submitting=!0;let t="新增";e.id&&(t="修改"),e.time&&(e.workTimeBegin=e.time[0],e.workTimeEnd=e.time[1],delete e.time),R(e).then(i=>{var a;s.submitting=!1,i.data.code===200?(r.success(t+"成功"),(a=v.value)==null||a.closeDialog()):r.warning(t+"失败"),V("extendedReturn",{})}).catch(i=>{s.submitting=!1,r.warning(i)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"合同编号",field:"code",rules:[{required:!0,message:"请输入合同编号"}],disabled:!0},{xs:12,type:"select",label:"合同分类",field:"type",options:b(()=>o.ConstructionContractType)},{xs:12,type:"input",label:"合同名称",field:"name",rules:[{required:!0,message:"请输入合同名称"}]},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"甲方单位",field:"firstpartOrganization",rules:[{required:!0,message:"请输入甲方单位"}]},{xs:12,type:"input",label:"乙方单位",field:"secondpartOrganization",rules:[{required:!0,message:"请输入乙方单位"}]},{xs:12,type:"input",label:"甲方代表",field:"firstpartRepresentative",rules:[{required:!0,message:"请输入甲方代表"}]},{xs:12,type:"input",label:"乙方联系人",field:"secondpartRepresentative"},{xs:12,type:"input",label:"甲方代表联系电话",field:"firstpartPhone"},{xs:12,type:"input",label:"乙方联系电话",field:"secondpartPhone"},{xs:12,type:"number",label:"合同金额",field:"cost",rules:[{required:!0,message:"请输入合同金额"}]},{xs:12,type:"daterange",label:"合同工期",field:"time",format:"x"},{xs:12,type:"date",label:"签署时间",field:"signTime",format:"x"},{type:"textarea",label:"详细说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),c=d({title:"查看设备",labelWidth:"130px",appendToBody:!0,dialogWidth:"1000px",defaultValue:{},group:[{fields:[{type:"table",field:"deviceTable",config:{indexVisible:!0,height:"350px",dataList:b(()=>o.deviceInformation),columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"amount"}],operations:[{perm:!0,text:"删除",icon:J.DELETE,click:e=>{F("确定删除该设备","删除提示").then(()=>{U(e.id).then(()=>{r.success("删除成功"),o.getProjectDeviceValue()}).catch(t=>{r.warning(t)})})}}],pagination:{hide:!0}}}]}]}),p=d({title:"添加设备",labelWidth:"80px",appendToBody:!0,dialogWidth:"1000px",defaultValue:{},submitting:!1,submit:(e,t)=>{var i,a;if(t)o.geDeviceListValue(e);else{let n=!1;const D=p.group[0].fields.find(l=>l.type==="table");((i=D.config.selectList)==null?void 0:i.length)===0&&(r.warning("请选中设备"),n=!0);const Y=(a=D.config.selectList)==null?void 0:a.map(l=>((l.number===0||!l.number)&&(r.warning("数量最少为1台"),n=!0),l.number>l.rest&&(r.warning("申请数量超过剩余数量"),n=!0),{serialId:l.serialId,amount:l.number||0}));if(n)return;p.submitting=!0,$(e.code,Y).then(l=>{var C;p.submitting=!1,l.data.code===200?(r.success("添加成功"),(C=f.value)==null||C.closeDialog()):r.warning("添加失败")}).catch(l=>{p.submitting=!1,r.warning(l)})}},group:[{fields:[{xs:6,type:"input",field:"serialId",label:"设备编码"},{xs:6,type:"input",field:"name",label:"设备名称"},{xs:6,type:"input",field:"model",label:"设备型号"},{xs:6,type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>{var e;(e=f.value)==null||e.Submit(!0)}},{text:"重置",perm:!0,type:"default",click:()=>{var e;(e=f.value)==null||e.resetForm(),o.geDeviceListValue()}}]},{type:"table",config:{indexVisible:!0,height:"350px",dataList:b(()=>o.deviceInformation),handleSelectChange:e=>{const t=p.group[0].fields.find(i=>i.type==="table");t.config.selectList=e},selectList:[],columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"rest"},{label:"申请数量",prop:"number",minWidth:"120px",formItemConfig:{type:"number",field:"number",min:0}}],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{const i=p.group[0].fields.find(a=>a.type==="table");i.config.pagination.page=e,i.config.pagination.limit=t,o.geDeviceListValue()}}}}]}]}),I=d({title:"详情",group:[],width:"80%",modalClass:"lightColor",appendToBody:!0,cancel:!1}),L=e=>{var t;s.title="编辑签证",s.defaultValue={...e||{},time:[e.workTimeBegin,e.workTimeEnd]},(t=v.value)==null||t.openDialog()},o=d({ConstructionContractType:[],deviceInformation:[],selected:{},getOptions:()=>{j({page:1,size:-1}).then(e=>{o.ConstructionContractType=B(e.data.data.data||[],"children")})},geDeviceListValue:e=>{var a;o.deviceInformation=[];const t=p.group[0].fields.find(n=>n.type==="table"),i={page:t.config.pagination.page||1,size:t.config.pagination.limit||20,...e};A((a=c.defaultValue)==null?void 0:a.constructionCode,i).then(n=>{o.deviceInformation=n.data.data.data||[],t.config.pagination.total=n.data.data.total||0})},getProjectDeviceValue:()=>{var i;o.deviceInformation=[];const e=c.group[0].fields.find(a=>a.type==="table"),t={page:e.config.pagination.page||1,size:e.config.pagination.limit||20};H((i=c.defaultValue)==null?void 0:i.code,t).then(a=>{o.deviceInformation=a.data.data.data||[],e.config.pagination.total=a.data.data.total||0})}});return M(()=>{o.getOptions(),o.geDeviceListValue()}),(e,t)=>{const i=O,a=P,n=z;return W(),N("div",K,[u(i,{config:m(w)},null,8,["config"]),u(a,{ref_key:"refForm",ref:v,class:"dialogForm",config:m(s)},null,8,["config"]),u(a,{ref_key:"refDeviceForm",ref:h,config:m(c)},null,8,["config"]),u(a,{ref_key:"refAddDeviceForm",ref:f,config:m(p)},null,8,["config"]),u(n,{ref_key:"refDetail",ref:y,config:m(I)},{default:q(()=>[u(G,{config:m(o).selected,show:7},null,8,["config"])]),_:1},8,["config"])])}}}),Fe=S(Q,[["__scopeId","data-v-2aaaf73d"]]);export{Fe as default};
