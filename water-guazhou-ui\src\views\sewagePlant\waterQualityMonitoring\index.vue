<!-- 污水厂水质监控 -->
<template>
  <div class="water-quality-monitoring">
    <!-- 查询条件 -->
    <CardSearch
      ref="refCardSearch"
      :config="searchConfig"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧指标卡片 -->
      <div class="left-panel">
        <!-- 本日水质均值 -->
        <SLCard title="本日水质均值" class="indicator-card">
          <div class="indicator-list">
            <div 
              v-for="indicator in todayIndicators" 
              :key="indicator.code"
              class="indicator-item"
            >
              <div class="indicator-name">{{ indicator.name }}</div>
              <div class="indicator-bar">
                <div class="bar-container">
                  <div 
                    class="bar-fill" 
                    :style="{ 
                      width: indicator.percentage + '%',
                      backgroundColor: getIndicatorColor(indicator.level)
                    }"
                  ></div>
                  <div class="bar-labels">
                    <span class="current-value">{{ indicator.currentValue }}{{ indicator.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SLCard>

        <!-- 出水COD均值 -->
        <SLCard title="出水COD均值" class="cod-card">
          <div class="cod-content">
            <div class="cod-value">
              <span class="value">{{ codData.value }}</span>
              <span class="unit">{{ codData.unit }}</span>
            </div>
            <div class="cod-status">
              <span class="status-text">较昨日{{ codData.trend > 0 ? '+' : '' }}{{ codData.trend }}{{ codData.unit }}</span>
              <el-icon class="trend-icon" :class="codData.trend > 0 ? 'trend-up' : 'trend-down'">
                <ArrowUp v-if="codData.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
            </div>
            <div class="cod-standard">
              <div class="standard-text">国标限值：</div>
              <div class="standard-value">{{ codData.standard }}</div>
            </div>
          </div>
        </SLCard>

        <!-- 出水PH均值 -->
        <SLCard title="出水PH均值" class="ph-card">
          <div class="ph-content">
            <div class="ph-value">
              <span class="value">{{ phData.value }}</span>
            </div>
            <div class="ph-status">
              <span class="status-text">较昨日{{ phData.trend > 0 ? '+' : '' }}{{ phData.trend }}</span>
              <el-icon class="trend-icon" :class="phData.trend > 0 ? 'trend-up' : 'trend-down'">
                <ArrowUp v-if="phData.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
            </div>
            <div class="ph-standard">
              <div class="standard-text">国标限值：</div>
              <div class="standard-value">{{ phData.standard }}</div>
            </div>
          </div>
        </SLCard>
      </div>

      <!-- 右侧数据表格 -->
      <div class="right-panel">
        <SLCard title="" class="table-card">
          <div class="table-container">
            <el-table
              :data="tableData"
              :loading="loading"
              style="width: 100%"
              border
              stripe
            >
              <el-table-column
                prop="level"
                label="水质等级"
                width="100"
                align="center"
              >
                <template #default="{ row }">
                  <el-tag :class="getQualityLevelClass(row.level)">
                    {{ row.level }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="plantName" label="污水厂名称" min-width="120" />
              <el-table-column prop="stationName" label="采样点位" min-width="100" />
              <el-table-column prop="sampleTime" label="采样时间" min-width="150" />
              <el-table-column prop="waterTemp" label="水温°C" width="80" align="center" />
              <el-table-column prop="cod" label="出水COD mg/L" width="120" align="center" />
              <el-table-column prop="ph" label="出水PH值" width="100" align="center" />
              <el-table-column prop="inflow" label="进水流量 m³/h" width="120" align="center" />
              <el-table-column prop="outflow" label="出水流量 m³/h" width="120" align="center" />
              <el-table-column label="检测报告" width="80" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleViewReport(row)"
                  >
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>


          </div>
        </SLCard>
      </div>
    </div>

    <!-- 水质监控详情弹窗 -->
    <WaterQualityMonitorDialog
      v-model="showReportDialog"
      :data="currentReportData"
      @close="handleCloseReport"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, shallowRef } from 'vue'
import { ArrowUp, ArrowDown, Search, Refresh } from '@element-plus/icons-vue'
import WaterQualityMonitorDialog from '@/components/WaterQualityMonitorDialog/index.vue'
import { GetWaterPlantSupply } from '@/api/mapservice/onemap'
import { useBusinessStore } from '@/store'


// Store
const businessStore = useBusinessStore()

// 响应式数据
const loading = ref(false)
const showReportDialog = ref(false)
const currentReportData = ref<any>(null)
const refCardSearch = ref<any>()

// 搜索配置
const searchConfig = reactive<ISearch>({
  defaultParams: {
    plantId: '',
    stationId: '',
    dateRange: [
      new Date().toISOString().split('T')[0] + ' 00:00:00',
      new Date().toISOString().split('T')[0] + ' 23:59:59'
    ]
  },
  filters: [
    {
      type: 'select',
      label: '污水厂',
      field: 'plantId',
      placeholder: '全部',
      options: computed(() => [
        { label: '全部', value: '' },
        ...plantList.value.map(item => ({ label: item.name, value: item.id }))
      ]) as any,
      onChange: () => {
        // 污水厂变化时重新加载采样点位
        loadStationList()
      }
    },
    {
      type: 'select',
      label: '采样点位',
      field: 'stationId',
      placeholder: '全部',
      options: computed(() => [
        { label: '全部', value: '' },
        ...stationList.value.map(item => ({ label: item.name, value: item.id }))
      ]) as any
    },
    {
      type: 'datetimerange',
      label: '监测时间',
      field: 'dateRange'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => handleSearch()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => handleReset()
        }
      ]
    }
  ]
})

// 污水厂列表
const plantList = ref<any[]>([])

// 采样点位列表
const stationList = ref<any[]>([
  { id: '1', name: '进水口' },
  { id: '2', name: '出水口' },
  { id: '3', name: '中间处理池' }
])

// 本日水质均值指标
const todayIndicators = ref([
  {
    code: 'cod',
    name: 'COD',
    currentValue: '25.8',
    unit: 'mg/L',
    percentage: 52,
    level: 'normal'
  },
  {
    code: 'bod',
    name: 'BOD',
    currentValue: '12.5',
    unit: 'mg/L',
    percentage: 42,
    level: 'good'
  },
  {
    code: 'ss',
    name: '悬浮物',
    currentValue: '18.2',
    unit: 'mg/L',
    percentage: 61,
    level: 'normal'
  },
  {
    code: 'nh3n',
    name: '氨氮',
    currentValue: '2.1',
    unit: 'mg/L',
    percentage: 35,
    level: 'good'
  },
  {
    code: 'tn',
    name: '总氮',
    currentValue: '8.5',
    unit: 'mg/L',
    percentage: 57,
    level: 'normal'
  },
  {
    code: 'tp',
    name: '总磷',
    currentValue: '0.8',
    unit: 'mg/L',
    percentage: 80,
    level: 'warning'
  },
  {
    code: 'ph',
    name: 'pH值',
    currentValue: '7.2',
    unit: '',
    percentage: 45,
    level: 'good'
  },
  {
    code: 'fecal',
    name: '粪大肠菌群',
    currentValue: '2500',
    unit: '个/L',
    percentage: 25,
    level: 'good'
  }
])

// COD数据
const codData = ref({
  value: '45.2',
  unit: 'mg/L',
  trend: 3.8,
  standard: 'GB 18918-2020 一级A标准：50mg/L'
})

// PH数据
const phData = ref({
  value: '9',
  trend: 1,
  standard: 'GB 8978-1996 pH控制：6~9'
})

// 表格数据
const tableData = ref<any[]>([])



// 方法
const getIndicatorColor = (level: string) => {
  const colors = {
    good: '#52c41a',
    normal: '#faad14', 
    warning: '#ff4d4f',
    danger: '#a8071a'
  }
  return colors[level] || '#d9d9d9'
}

const getQualityLevelClass = (level: string) => {
  const classes = {
    'I类': 'quality-level-1',
    'II类': 'quality-level-2', 
    'III类': 'quality-level-3',
    'IV类': 'quality-level-4',
    'V类': 'quality-level-5'
  }
  return classes[level] || 'quality-level-unknown'
}

const handleSearch = () => {
  const queryParams = refCardSearch.value?.queryParams || {}
  console.log('执行查询:', queryParams)
  loadTableData()
}

const handleReset = () => {
  refCardSearch.value?.resetForm()
  loadTableData()
}

const handleViewReport = (row: any) => {
  console.log('查看检测报告:', row)

  // 按照WaterQualityMonitorDialog组件期望的数据结构组织数据
  currentReportData.value = {
    stationName: row.stationName,
    sampleTime: row.sampleTime,
    waterQualityLevel: row.level,
    indicators: {
      // 确保所有数值都是number类型
      outflow: Number(row.outflow) || (75 + Math.random() * 35),
      inflow: Number(row.inflow) || (80 + Math.random() * 40),
      cod: Number(row.cod) || (15 + Math.random() * 20),
      ph: Number(row.ph) || (6.5 + Math.random() * 2),
      bod5: 5 + Math.random() * 15,
      ammoniaNitrogen: 0.2 + Math.random() * 1.5,
      totalNitrogen: 0.3 + Math.random() * 1.5,
      totalPhosphorus: 0.05 + Math.random() * 0.3,
      fecalColiform: 500 + Math.random() * 15000
    }
  }
  showReportDialog.value = true
}

const handleCloseReport = () => {
  showReportDialog.value = false
  currentReportData.value = null
}

// 加载污水厂列表
const loadPlantList = async () => {
  try {
    const response = await GetWaterPlantSupply({
      projectId: businessStore.selectedProject?.value
    })

    const dataList = response.data?.data || response.data || []

    if (Array.isArray(dataList) && dataList.length > 0) {
      // 过滤出污水厂
      const sewagePlants = dataList.filter((item: any) => {
        return item.stationType === '污水处理厂' ||
               item.type === '污水处理厂' ||
               (item.name && item.name.includes('污水'))
      })

      plantList.value = sewagePlants.map((item: any) => ({
        id: item.stationId || item.id,
        name: item.name || item.stationName
      }))
    }
  } catch (error) {
    console.error('加载污水厂列表失败:', error)
    // 使用默认数据
    plantList.value = [
      { id: '1', name: '污水处理厂A' },
      { id: '2', name: '污水处理厂B' },
      { id: '3', name: '污水处理厂C' }
    ]
  }
}

const loadStationList = () => {
  // 根据选择的污水厂加载对应的采样点位
  // 这里可以调用API获取真实数据
}

const loadTableData = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    const plantNames = plantList.value.length > 0
      ? plantList.value.map(p => p.name)
      : ['污水处理厂A', '污水处理厂B', '污水处理厂C']
    const stationNames = ['进水口', '出水口', '中间处理池']
    const levels = ['II类', 'III类', 'IV类']

    // 为每个污水厂生成最新的一条数据
    const latestData: any[] = []

    plantNames.forEach((plantName, index) => {
      const stationName = stationNames[Math.floor(Math.random() * stationNames.length)]
      const level = levels[Math.floor(Math.random() * levels.length)]

      // 生成最新时间（最近1小时内）
      const now = new Date()
      const randomMinutes = Math.floor(Math.random() * 60) // 最近1小时内
      const sampleTime = new Date(now.getTime() - randomMinutes * 60 * 1000)

      latestData.push({
        id: `station-${index + 1}`,
        level: level,
        plantName: plantName,
        stationName: stationName,
        sampleTime: sampleTime.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }).replace(/\//g, '-'),
        waterTemp: Math.floor(Math.random() * 10) + 15, // 15-25度
        cod: Math.floor(Math.random() * 50) + 20, // 20-70
        ph: Number((Math.random() * 2 + 6.5).toFixed(1)), // 6.5-8.5，确保是数字类型
        inflow: Math.floor(Math.random() * 50) + 100, // 100-150
        outflow: Math.floor(Math.random() * 40) + 95 // 95-135
      })
    })

    // 按时间倒序排列，最新的在前面
    latestData.sort((a, b) => new Date(b.sampleTime).getTime() - new Date(a.sampleTime).getTime())

    tableData.value = latestData
    loading.value = false
  }, 500)
}

// 生命周期
onMounted(async () => {
  await loadPlantList()
  loadTableData()
})
</script>

<style lang="scss" scoped>
.water-quality-monitoring {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .main-content {
    display: flex;
    gap: 16px;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    padding: 0 16px 16px 16px;

    .left-panel {
      width: 280px;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .indicator-card {
        flex: 0 0 auto;
        max-height: 500px;

        :deep(.el-card__body) {
          padding: 12px;
        }

        .indicator-list {
          .indicator-item {
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            .indicator-name {
              font-size: 11px;
              color: #666;
              margin-bottom: 6px;
            }

            .indicator-bar {
              .bar-container {
                position: relative;
                height: 16px;
                background-color: #f0f0f0;
                border-radius: 8px;
                overflow: hidden;

                .bar-fill {
                  height: 100%;
                  border-radius: 8px;
                  transition: width 0.3s ease;
                }

                .bar-labels {
                  position: absolute;
                  right: 6px;
                  top: 50%;
                  transform: translateY(-50%);

                  .current-value {
                    font-size: 10px;
                    color: #333;
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }
      }

      .cod-card, .ph-card {
        flex: 0 0 auto;
        height: 140px;

        :deep(.el-card__body) {
          padding: 12px;
        }

        .cod-content, .ph-content {
          text-align: center;

          .cod-value, .ph-value {
            margin-bottom: 8px;

            .value {
              font-size: 28px;
              font-weight: bold;
              color: #333;
            }

            .unit {
              font-size: 14px;
              color: #666;
              margin-left: 4px;
            }
          }

          .cod-status, .ph-status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;

            .status-text {
              font-size: 12px;
              margin-right: 4px;
              color: rgb(179, 76, 76);
            }

            .trend-icon {
              font-size: 14px;

              &.trend-up {
                color: #f56c6c;
              }

              &.trend-down {
                color: #67c23a;
              }
            }
          }

          .cod-standard, .ph-standard {
            .standard-text {
              font-size: 10px;
              color: #666;
              margin-bottom: 2px;
            }

            .standard-value {
              font-size: 10px;
              color: #333;
              line-height: 1.2;
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      overflow: hidden;

      .table-card {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;

        :deep(.el-card__header) {
          padding: 0;
          border-bottom: none;
        }

        :deep(.el-card__body) {
          flex: 1;
          padding: 16px;
          display: flex;
          flex-direction: column;
          min-height: 0;
          overflow: hidden;
        }

        .table-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          min-height: 0;

          .el-table {
            flex: 1;
            min-height: 0;
          }
        }
      }
    }
  }
}

// 水质等级颜色
.quality-level-1 {
  color: #67c23a;
  font-weight: bold;
}

.quality-level-2 {
  color: #409eff;
  font-weight: bold;
}

.quality-level-3 {
  color: #e6a23c;
  font-weight: bold;
}

.quality-level-4 {
  color: #f56c6c;
  font-weight: bold;
}

.quality-level-5 {
  color: #909399;
  font-weight: bold;
}

.quality-level-unknown {
  color: var(--el-text-color-placeholder);
}

// 响应式适配
@media (max-width: 1200px) {
  .water-quality-monitoring {
    .main-content {
      flex-direction: column;

      .left-panel {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
        flex-shrink: 0;

        .indicator-card, .cod-card, .ph-card {
          min-width: 250px;
        }
      }

      .right-panel {
        flex: 1;
        min-height: 400px;
      }
    }
  }
}

@media (max-width: 768px) {
  .water-quality-monitoring {
    padding: 8px;
    height: 100vh;

    .search-card {
      .search-form {
        .el-form {
          .el-form-item {
            display: block;
            margin-bottom: 16px;
            margin-right: 0;
          }
        }
      }
    }

    .main-content {
      .left-panel {
        flex-direction: column;

        .indicator-card, .cod-card, .ph-card {
          min-width: auto;
        }
      }

      .right-panel {
        flex: 1;
        min-height: 300px;
      }
    }
  }
}
</style>
