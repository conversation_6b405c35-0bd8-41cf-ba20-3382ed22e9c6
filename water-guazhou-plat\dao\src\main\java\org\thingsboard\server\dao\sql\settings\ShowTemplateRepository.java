package org.thingsboard.server.dao.sql.settings;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.ShowTemplate;

import java.util.List;

public interface ShowTemplateRepository extends JpaRepository<ShowTemplate, String> {
    List<ShowTemplate> findByTenantIdAndType(String tenantId, String type);

    void deleteByTenantIdAndTypeAndName(String tenantId, String type, String name);
}
