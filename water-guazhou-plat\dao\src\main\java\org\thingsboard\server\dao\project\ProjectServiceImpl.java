package org.thingsboard.server.dao.project;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.datavVO.PieVO;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.dataSource.DataSourceService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.sql.project.ProjectRepository;
import org.thingsboard.server.dao.util.OracleUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.DEVICE_CACHE;
import static org.thingsboard.server.common.data.CacheConstants.DEVICE_CREDENTIALS_CACHE;

@Slf4j
@Service
@Transactional
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private DataSourceService dataSourceService;
    @Autowired
    private OracleUtil oracleUtil;


    @Override
    public ProjectEntity findById(String id) {
        ProjectEntity entity = projectRepository.findOne(id);
        if (entity != null) {
            return entity;
        }
        return null;
    }

    @Override
    public ProjectEntity save(ProjectEntity entity) {
        // 是否为根节点
        if (StringUtils.isBlank(entity.getParentId())) {
            entity.setParentId(DataConstants.ROOT_PROJECT_PARENT_ID);
        }
        // 保存数据
        entity.setCreateTime(System.currentTimeMillis());
        projectRepository.save(entity);
        return entity;
    }

    @Override
    public ProjectEntity update(ProjectEntity entity) {
        // 查询是否存在该项目
        ProjectEntity _entity = projectRepository.findOne(entity.getId());
        if (_entity != null) {
            _entity.setName(entity.getName());
            _entity.setParentId(entity.getParentId());
            _entity.setAdditionalInfo(entity.getAdditionalInfo());
            _entity.setType(entity.getType());

            projectRepository.save(_entity);
        }
        return _entity;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(cacheNames = DEVICE_CACHE, allEntries = true),
            @CacheEvict(cacheNames = DEVICE_CREDENTIALS_CACHE, allEntries = true)
    })
    public ProjectEntity deleteById(String id) {
        // 删除项目
        ProjectEntity _entity = projectRepository.findOne(id);
        if (_entity != null) {
            projectRepository.delete(id);
            // TODO 删除项目与其他实体的关系
            //1 删除项目下的主机、从机
            deviceService.deleteByProjectId(id);
            //2 删除关联此项目的用户关系
            projectRelationService.removeProjectRelation(id);
        }
        return _entity;
    }

    @Override
    public void deleteByTenantId(TenantId tenantId) {
        projectRepository.deleteByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<ProjectTreeVO> findChildrenById(String id) {
        ProjectEntity rootProject = projectRepository.findOne(id);
        List<ProjectEntity> children = projectRepository.findByParentIdOrderByCreateTime(rootProject.getId());

        return getProjectTreeVOByProjectEntity(children);
    }

    @Override
    public List<ProjectTreeVO> findRootProject(String tenantId, Boolean getDevice, UserId id, Authority authority) {
        try {
            // 获取所有项目信息
            List<ProjectTreeVO> projectListAll = findByTenantId(tenantId);
            if (Authority.CUSTOMER_USER.equals(authority) && projectListAll != null) {
                Map<String, List<ProjectTreeVO>> projectMap = new HashMap<>();
                projectListAll.forEach(project -> {
                    List<ProjectTreeVO> list = new ArrayList<>();
                    if (projectMap.containsKey(project.getParentId())) {
                        list = projectMap.get(project.getParentId());
                    }
                    list.add(project);

                    projectMap.put(project.getParentId(), list);
                });

                // 筛选用户的项目
                List<ProjectTreeVO> customerProjectList = projectRelationService.findProjectTreeVOByEntityTypeAndEntityId(
                        "USER", id.getId().toString());
                /*if (customerProjectList.size() == 1 && !customerProjectList.get(0).getParentId().equals("0")) {
                    return customerProjectList;
                }*/
                customerProjectList = setParentProjectToList(customerProjectList, projectMap);

                // 去重
                Set<ProjectTreeVO> list = new HashSet<>(customerProjectList);
                customerProjectList = new ArrayList<>(list);

                Map<String, ProjectTreeVO> customerProjectMap = new HashMap<>();
                for (ProjectTreeVO projectTreeVO : customerProjectList) {
                    customerProjectMap.put(projectTreeVO.getId(), projectTreeVO);
                }

                projectListAll = projectListAll.stream()
                        .filter(projectTreeVO -> customerProjectMap.containsKey(projectTreeVO.getId()))
                        .collect(Collectors.toList());

            }
            // 获取所有设备信息
            Map<String, List<Device>> gatewayMap = new HashMap<>();
            deviceService.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))
                    .forEach(device -> {
                        if (device.getAdditionalInfo() != null) {
                            JSONObject obj = JSON.parseObject(device.getAdditionalInfo().asText());
                            if (obj != null && obj.containsKey("gateway") && obj.getBoolean("gateway")) {
                                List<Device> list = null;
                                if (gatewayMap.containsKey(device.getProjectId())) {
                                    list = gatewayMap.get(device.getProjectId());
                                } else {
                                    list = new ArrayList<>();
                                }
                                list.add(device);
                                gatewayMap.put(device.getProjectId(), list);
                            }
                        }
                    });
            return findProjectList(projectListAll, gatewayMap, getDevice);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private List<ProjectTreeVO> setParentProjectToList(List<ProjectTreeVO> customerProjectList, Map<String, List<ProjectTreeVO>> projectMap) {
        List<ProjectTreeVO> list = new ArrayList<>();
        Map<String, ProjectTreeVO> existParentMap = new HashMap<>();
        for (ProjectTreeVO projectTreeVO : customerProjectList) {
            addParentProjectToList(existParentMap, projectTreeVO, projectMap, list);

            addChildProjectToList(customerProjectList, projectTreeVO, projectMap, list);
        }
        list.addAll(customerProjectList);
        return list;
    }

    private void addChildProjectToList(List<ProjectTreeVO> customerProjectList, ProjectTreeVO projectTreeVO, Map<String, List<ProjectTreeVO>> projectMap, List<ProjectTreeVO> list) {
        List<ProjectTreeVO> childProject = projectMap.get(projectTreeVO.getId());
        if (childProject == null || childProject.size() < 1) {
            return;
        }
        list.addAll(childProject);

        for (ProjectTreeVO treeVO : childProject) {
            addChildProjectToList(customerProjectList, treeVO, projectMap, list);
        }
    }

    private void addParentProjectToList(Map<String, ProjectTreeVO> existParentMap, ProjectTreeVO projectTreeVO, Map<String, List<ProjectTreeVO>> projectMap, List<ProjectTreeVO> list) {
        if ("0".equals(projectTreeVO.getParentId())) {
            return;
        }
        ProjectEntity parentProject = null;
        ProjectTreeVO treeVO = null;
        if (existParentMap.containsKey(projectTreeVO.getParentId())) {
            treeVO = existParentMap.get(projectTreeVO.getParentId());
        } else {
            parentProject = projectRepository.findOne(projectTreeVO.getParentId());
            treeVO = projectEntityToProjectTreeVO(parentProject);
            existParentMap.put(treeVO.getId(), treeVO);
        }
        if (parentProject == null) {
            return;
        }

        list.add(treeVO);

        if ("0".equals(treeVO.getParentId())) {
            return;
        }
        addParentProjectToList(existParentMap, treeVO, projectMap, list);

    }


    public List<ProjectTreeVO> findByTenantId(String tenantId) {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.ASC, "createTime"));
        return projectEntityListToProjectTreeVOList(projectRepository.findByTenantId(tenantId, orders));
    }

    @Override
    public List<ProjectTreeVO> findProjectList(List<ProjectTreeVO> projectList, Map<String, List<Device>> gatewayMap, Boolean getDevice) {
        // 项目分类
        if (projectList == null || projectList.isEmpty()) {
            return null;
        }
        Map<String, List<ProjectTreeVO>> projectListMap = new HashMap<>();
        projectList.forEach(project -> {
            List<ProjectTreeVO> projectEntityList = null;
            if (!projectListMap.containsKey(project.getParentId())) {
                projectEntityList = new ArrayList<>();
            } else {
                projectEntityList = projectListMap.get(project.getParentId());
            }
            projectEntityList.add(project);
            projectListMap.put(project.getParentId(), projectEntityList);
        });

        if (projectListMap.isEmpty()) {
            return null;
        }
        List<ProjectTreeVO> root = projectListMap.get("0");
        root.forEach(rootNode -> {
            getProjectByParentId(rootNode, projectListMap, gatewayMap, getDevice);
        });

        return root;
    }

    @Override
    public List<ProjectTreeVO> findAll() {
        List<ProjectTreeVO> result = new ArrayList<>();
        projectRepository.findAll().forEach(projectEntity -> result.add(projectEntityToProjectTreeVO(projectEntity)));
        return result;
    }

    @Override
    public List<ProjectEntity> findAllProject() {
        return projectRepository.findAll();
    }

    @Override
    public List<ProjectEntity> findByTenantId(TenantId tenantId) {
        return projectRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public long findAllCounts() {
        return projectRepository.count();
    }

    @Override
    public long countsByTenant(String tenantId) {
        return projectRepository.countByTenantId(tenantId);
    }

    @Override
    public List<ProjectTreeVO> findDataSourceAndDeviceProjectTree(String tenantId) {
        try {
            // 获取所有项目信息
            List<ProjectTreeVO> projectListAll = findByTenantId(tenantId).stream().peek(projectTreeVO -> projectTreeVO.setNodeId(projectTreeVO.getId())).collect(Collectors.toList());
            // 获取所有设备信息
            Map<String, List<Device>> gatewayMap = new HashMap<>();
            Map<DeviceId, List<Device>> deviceMap = new HashMap<>();
            List<Device> list = deviceService.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId)));
            list = list.stream().filter(device -> {
                if (device.getGateWayId() != null) {
                    device.setNodeId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                    device.setNodeType("Device");
                    List<Device> l = null;
                    if (deviceMap.get(device.getGateWayId()) != null && !deviceMap.get(device.getGateWayId()).isEmpty()) {
                        l = deviceMap.get(device.getGateWayId());
                    } else {
                        l = new ArrayList<>();
                    }
                    l.add(device);
                    deviceMap.put(device.getGateWayId(), l);
                } else {
                    device.setNodeId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                    device.setNodeType("MainDevice");
                }
                if (device.getAdditionalInfo() != null) {
                    JSONObject additionalInfo = JSON.parseObject(device.getAdditionalInfo().asText());
                    if (additionalInfo != null && additionalInfo.containsKey("gateway") && additionalInfo.getBoolean("gateway")) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            list.forEach(device -> {
                device.setChildren(deviceMap.get(device.getId()));
                if (device.getAdditionalInfo() != null) {
                    JSONObject obj = JSON.parseObject(device.getAdditionalInfo().asText());
                    if (obj != null && obj.containsKey("gateway") && obj.getBoolean("gateway")) {
                        List<Device> l = null;
                        if (gatewayMap.containsKey(device.getProjectId())) {
                            l = gatewayMap.get(device.getProjectId());
                        } else {
                            l = new ArrayList<>();
                        }
                        l.add(device);
                        gatewayMap.put(device.getProjectId(), l);
                    }
                }
            });
            // 获取API数据源
            Map<String, List<RestApiEntity>> dataSourceMap = new HashMap<>();
            dataSourceService.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))
                    .forEach(dataSource -> {
                        List<RestApiEntity> l = null;
                        if (dataSourceMap.containsKey(dataSource.getProjectId())) {
                            l = dataSourceMap.get(dataSource.getProjectId());
                        } else {
                            l = new ArrayList<>();
                        }
                        l.add(dataSource);
                        dataSourceMap.put(dataSource.getProjectId(), l);
                    });
            return findProjectList(projectListAll, gatewayMap, true, dataSourceMap);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProjectTreeVO> findProjectAndGatewayTree(String tenantId) {
        try {
            // 获取所有项目信息
            List<ProjectTreeVO> projectListAll = findByTenantId(tenantId).stream().peek(projectTreeVO -> projectTreeVO.setNodeId(projectTreeVO.getId())).collect(Collectors.toList());
            // 获取所有设备信息
            Map<String, List<Device>> gatewayMap = new HashMap<>();
            Map<DeviceId, List<Device>> deviceMap = new HashMap<>();
            List<Device> list = deviceService.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId))).stream()
                    .filter(device -> (device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT))
                            && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());

            list.forEach(device -> {
                device.setNodeId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                device.setNodeType("MainDevice");
                if (device.getAdditionalInfo() != null) {
                    JSONObject obj = JSON.parseObject(device.getAdditionalInfo().asText());
                    if (obj != null && obj.containsKey("gateway") && obj.getBoolean("gateway")) {
                        List<Device> l = null;
                        if (gatewayMap.containsKey(device.getProjectId())) {
                            l = gatewayMap.get(device.getProjectId());
                        } else {
                            l = new ArrayList<>();
                        }
                        l.add(device);
                        gatewayMap.put(device.getProjectId(), l);
                    }
                }
            });
            Map<String, List<RestApiEntity>> dataSourceMap = new HashMap<>();

            return findProjectList(projectListAll, gatewayMap, true, dataSourceMap);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @Override
    public List<PieVO> projectBusinessStatistics(TenantId tenantId) {
        List<PieVO> result = new ArrayList<>();
        Map<String, Long> countMap = new HashMap<>();

        List<ProjectEntity> projectEntityList = projectRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        for (ProjectEntity projectEntity : projectEntityList) {
            JSONObject projectObj = JSON.parseObject(projectEntity.getAdditionalInfo());
            if (!projectObj.containsKey("businessType")) {
                continue;
            }
            String businessType = projectObj.getString("businessType");
            if (StringUtils.isBlank(businessType)) {
                continue;
            }
            Long aLong = 1L;
            if (countMap.containsKey(businessType)) {
                aLong = countMap.get(businessType);
            }
            aLong = aLong + 1;
            countMap.put(businessType, aLong);
        }

        if (!countMap.isEmpty()) {
            for (Map.Entry<String, Long> countEntry : countMap.entrySet()) {
                PieVO pieVO = new PieVO();
                pieVO.setName(countEntry.getKey());
                pieVO.setValue(BigDecimal.valueOf(countEntry.getValue()));

                result.add(pieVO);
            }
        }

        return result;
    }

    /**
     * 查询指定项目ID下的所有子项目（返回结果中包含自己）
     */
    @Override
    public List<ProjectEntity> findAllChild(TenantId tenantId, String projectId) {

        ProjectEntity project = projectRepository.findOne(projectId);
        if (project == null) {
            return new ArrayList<>();
        }

        List<ProjectEntity> projectList = projectRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<ProjectEntity> child = new ArrayList<>();
        getChild(projectId, projectList, child);

        child.add(project);
        return child;
    }

    private void getChild(String projectId, List<ProjectEntity> list, List<ProjectEntity> childResultList) {
        list.stream()
                .filter(entity -> entity.getParentId().equals(projectId))
                .collect(Collectors.toList())
                .forEach(entity -> {
                    childResultList.add(entity);
                    getChild(entity.getId(), list, childResultList);
                });
    }


    public List<ProjectTreeVO> findProjectList(List<ProjectTreeVO> projectList, Map<String, List<Device>> gatewayMap,
                                               Boolean getDevice, Map<String, List<RestApiEntity>> dataSourceMap) {
        // 项目分类
        if (projectList == null || projectList.isEmpty()) {
            return null;
        }
        Map<String, List<ProjectTreeVO>> projectListMap = new HashMap<>();
        projectList.forEach(project -> {
            List<ProjectTreeVO> projectEntityList = null;
            if (!projectListMap.containsKey(project.getParentId())) {
                projectEntityList = new ArrayList<>();
            } else {
                projectEntityList = projectListMap.get(project.getParentId());
            }
            projectEntityList.add(project);
            projectListMap.put(project.getParentId(), projectEntityList);
        });

        if (projectListMap.isEmpty()) {
            return null;
        }
        List<ProjectTreeVO> root = projectListMap.get("0");
        root.forEach(rootNode -> {
            getProjectByParentId(rootNode, projectListMap, gatewayMap, getDevice, dataSourceMap);
        });

        return root;
    }

    /**
     * 设置子项目，主机，数据源
     *
     * @param parentNode
     * @param projectListMap
     * @param gatewayMap
     * @param getDevice
     */
    private void getProjectByParentId(ProjectTreeVO parentNode, Map<String, List<ProjectTreeVO>> projectListMap,
                                      Map<String, List<Device>> gatewayMap, Boolean getDevice,
                                      Map<String, List<RestApiEntity>> dataSourceMap) {
        if (parentNode != null) {
            List list = new ArrayList();
            List<ProjectTreeVO> projectTreeVOS = projectListMap.get(parentNode.getId());
            if (projectTreeVOS != null) {
                list.addAll(projectTreeVOS);
            }
            if (getDevice && gatewayMap != null && gatewayMap.get(parentNode.getId()) != null) {
                list.addAll(gatewayMap.get(parentNode.getId()));
            }
            if (dataSourceMap != null && dataSourceMap.get(parentNode.getId()) != null) {
                list.addAll(dataSourceMap.get(parentNode.getId()));
            }
            parentNode.setChildren(list);
            if (projectTreeVOS != null) {
                projectTreeVOS.forEach(children -> {
                    getProjectByParentId(children, projectListMap, gatewayMap, getDevice, dataSourceMap);
                });
            }
        }
    }

    /**
     * 设置子项目
     *
     * @param parentNode
     * @param projectListMap
     * @param gatewayMap
     * @param getDevice
     */
    private void getProjectByParentId(ProjectTreeVO parentNode, Map<String, List<ProjectTreeVO>> projectListMap, Map<String, List<Device>> gatewayMap, Boolean getDevice) {
        if (parentNode != null) {
            List list = new ArrayList();
            List<ProjectTreeVO> projectTreeVOS = projectListMap.get(parentNode.getId());
            if (projectTreeVOS != null) {
                list.addAll(projectTreeVOS);
            }
            if (getDevice && gatewayMap != null && gatewayMap.get(parentNode.getId()) != null) {
                list.addAll(gatewayMap.get(parentNode.getId()));
            }
            parentNode.setChildren(list);
            if (projectTreeVOS != null) {
                projectTreeVOS.forEach(children -> {
                    getProjectByParentId(children, projectListMap, gatewayMap, getDevice);
                });
            }
        }
    }

    private List<ProjectTreeVO> getProjectTreeVOByProjectEntity(List<ProjectEntity> byParentId) {
        return byParentId.stream().map(project -> {
            ProjectTreeVO treeVO = new ProjectTreeVO();
            treeVO.setId(project.getId());
            treeVO.setName(project.getName());
            treeVO.setParentId(project.getParentId());
            treeVO.setAdditionalInfo(project.getAdditionalInfo());
            treeVO.setCreateTime(project.getCreateTime());
            int count = projectRepository.countByParentId(project.getId());
            treeVO.setLeaf(count <= 0);

            return treeVO;
        }).collect(Collectors.toList());
    }

    private List<ProjectTreeVO> projectEntityListToProjectTreeVOList(List<ProjectEntity> projectList) {
        return projectList.stream().map(this::projectEntityToProjectTreeVO).collect(Collectors.toList());
    }

    /**
     * projectEntity转换projectTreeVO
     *
     * @param project
     * @return
     */
    private ProjectTreeVO projectEntityToProjectTreeVO(ProjectEntity project) {
        ProjectTreeVO treeVO = new ProjectTreeVO();
        treeVO.setId(project.getId());
        treeVO.setName(project.getName());
        treeVO.setParentId(project.getParentId());
        treeVO.setAdditionalInfo(project.getAdditionalInfo());
        treeVO.setCreateTime(project.getCreateTime());
        treeVO.setTenantId(project.getTenantId());
        treeVO.setType(project.getType());
        int count = projectRepository.countByParentId(project.getId());
        treeVO.setLeaf(count <= 0);

        return treeVO;
    }
}
