<!-- 日常方案 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable class="card-table" :config="TableConfig" />
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <el-descriptions title=" " :column="3" :border="true">
        <template v-for="(item, index) in fields" :key="index">
          <el-descriptions-item :label="item.label + ' :'">
            {{ detailValue[item.field] || '' }}
            {{ item.unit ? '(' + item.unit + ')' : '' }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import {
  getDispatchMethodList,
  postDispatchMethod,
  deleteDispatchMethod,
  postDispatchMethodStatus
} from '@/api/productionScheduling/schedulingPlan';
import { programType, dateLabel, weatherType, fields } from './data';
import { GetStationTree } from '@/api/shuiwureports/zhandian';
import { SLConfirm } from '@/utils/Message';
import { traverse } from '@/utils/GlobalHelper';

const { $btnPerms } = useGlobal();

const refForm = ref<ISLDrawerIns>();
const refDetail = ref<ISLDrawerIns>();
const refSearch = ref<ICardSearchIns>();
const detailValue = ref<any>({});

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '方案名称', field: 'name', type: 'input' },
    { label: '方案类型', field: 'type', type: 'select', options: programType },
    {
      label: '日期标签',
      field: 'dateLabel',
      type: 'select',
      options: dateLabel
    },
    { label: '方案时间', field: 'time', type: 'daterange', format: 'x' },
    {
      label: '天气类型',
      field: 'weatherType',
      type: 'select',
      options: weatherType
    },
    {
      type: 'input',
      label: '供水量',
      field: 'waterSupplyFrom'
    },
    {
      type: 'input',
      label: '-',
      field: 'waterSupplyTo',
      labelWidth: '2px'
    },
    {
      type: 'input',
      label: '耗电量',
      field: 'powerConsumptionFrom'
    },
    {
      type: 'input',
      label: '-',
      field: 'powerConsumptionTo',
      labelWidth: '2px'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '方案编号', prop: 'code', minWidth: 120 },
    { label: '方案名称', prop: 'name', minWidth: 120 },
    { label: '方案时间', prop: 'time', minWidth: 140 },
    { label: '日期标签', prop: 'dateLabel', minWidth: 120 },
    { label: '水厂', prop: 'stationName', minWidth: 120 },
    { label: '天气', prop: 'weatherType', minWidth: 120 },
    { label: '最高温度', prop: 'maxTemperature', unit: '(°C)', minWidth: 120 },
    { label: '最低温度', prop: 'minTemperature', unit: '(°C)', minWidth: 120 },
    { label: '降水量', prop: 'rainfall', unit: '(mm)', minWidth: 120 },
    { label: '相对湿度', prop: 'relativeHumidity', unit: '(%)', minWidth: 120 },
    { label: '供水量', prop: 'waterSupply', unit: '(m³)', minWidth: 120 },
    { label: '耗电量', prop: 'powerConsumption', unit: '(Kwh)', minWidth: 120 },
    { label: '清水池液位', prop: 'waterLevel', unit: '(m)', minWidth: 140 },
    { label: '方案描述', prop: 'remark', minWidth: 120 },
    { label: '编辑人', prop: 'editUserName', minWidth: 120 },
    { label: '编辑时间', prop: 'createTime', minWidth: 140 },
    {
      label: '是否启用',
      prop: 'isEnabled',
      minWidth: 120,
      formItemConfig: {
        type: 'switch',
        onChange: (val, row) => {
          if (row.isEnabledCopy !== row.isEnabled) {
            let text = '停用成功';
            val && (text = '启用成功');
            postDispatchMethodStatus(row.id, val)
              .then(() => {
                row.isEnabledCopy = row.isEnabled;
                ElMessage.success(text);
              })
              .catch((error) => {
                ElMessage.warning(error);
              });
          }
        }
      }
    }
  ],
  operationWidth: '220px',
  operations: [
    {
      type: 'primary',
      text: '详情',
      icon: ICONS.DETAIL,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => openDetail(row)
    },
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      icon: ICONS.DELETE,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '100px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增成功';
    if (params.id) text = '修改成功';
    postDispatchMethod(params)
      .then(() => {
        addOrUpdateConfig.submitting = false;
        refForm.value?.closeDrawer();
        ElMessage.success(text);
        refreshData();
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        { text: '基础数据信息', type: 'divider' },
        {
          xl: 8,
          type: 'input',
          label: '方案名称',
          field: 'name',
          rules: [{ required: true, message: '请输入方案名称' }]
        },
        {
          xl: 8,
          type: 'select',
          label: '方案类型',
          field: 'type',
          options: programType,
          rules: [{ required: true, message: '请输入方案类型' }]
        },
        {
          xl: 8,
          type: 'select',
          label: '日期标签',
          field: 'dateLabel',
          options: dateLabel,
          rules: [{ required: true, message: '请输入日期标签' }]
        },
        {
          xl: 8,
          type: 'select-tree',
          label: '水厂',
          options: computed(() => data.waterPlant) as any,
          field: 'stationId'
        },
        {
          xl: 8,
          type: 'date',
          label: '方案日期',
          field: 'time',
          rules: [{ required: true, message: '请输入方案日期' }],
          format: 'x'
        },
        {
          xl: 16,
          type: 'textarea',
          label: '方案描述',
          field: 'remark',
          rules: [{ required: true, message: '请输入方案描述' }]
        },
        {
          text: '系统监测数据',
          type: 'divider'
        },
        {
          xl: 8,
          type: 'select',
          label: '天气类型',
          options: weatherType,
          field: 'weatherType'
        },
        {
          xl: 8,
          type: 'input',
          label: '最高温度',
          field: 'maxTemperature',
          append: '°C'
        },
        {
          xl: 8,
          type: 'input',
          label: '最低温度',
          field: 'minTemperature',
          append: '°C'
        },
        {
          xl: 8,
          type: 'input-number',
          label: '降雨量',
          field: 'rainfall',
          append: 'mm'
        },
        {
          xl: 8,
          type: 'input-number',
          label: '相对湿度',
          field: 'relativeHumidity',
          append: '%'
        },
        {
          xl: 8,
          type: 'input-number',
          label: '供水量',
          field: 'waterSupply',
          append: 'm³'
        },
        {
          xl: 8,
          type: 'input-number',
          label: '耗电量',
          field: 'powerConsumption',
          append: 'Kwh'
        },
        {
          xl: 8,
          type: 'input-number',
          label: '清水池液位',
          field: 'waterLevel',
          append: 'm'
        }
      ]
    }
  ]
});

const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',
  group: []
});

function openDetail(row?: any) {
  detailConfig.title = row.code || '';
  refDetail.value?.openDrawer();
  detailValue.value = row;
}

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增';
  addOrUpdateConfig.defaultValue = {};
  refForm.value?.openDrawer();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑';
  addOrUpdateConfig.defaultValue = { category: row.parentId, ...(row || {}) };
  refForm.value?.openDrawer();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除该预案', '删除提示').then(() => {
    deleteDispatchMethod(row.id)
      .then(() => {
        ElMessage.success('删除成功');
        refreshData();
      })
      .catch((error) => {
        ElMessage.error(error.toString());
      });
  });
};

const data = reactive({
  // 水厂
  waterPlant: [],
  getwaterPlant: () => {
    GetStationTree('水厂').then((res) => {
      data.waterPlant = traverse(res.data.data || []);
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.time && params.time?.length > 1) {
    params.timeFrom = (refSearch.value?.queryParams as any).time[0] || '';
    params.timeTo = (refSearch.value?.queryParams as any).time[1] || '';
  }
  delete params.time;
  getDispatchMethodList(params).then((res) => {
    const value = res.data.data.data || [];
    value.map((item) => (item.isEnabledCopy = item.isEnabled));
    TableConfig.dataList = value;
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(async () => {
  refreshData();
  data.getwaterPlant();
});
</script>
