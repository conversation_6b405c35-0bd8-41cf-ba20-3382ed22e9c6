import{Z as d,cQ as i,ae as n}from"./index-r0dFAfgr.js";const p=2302600009,g="4jqDiL2nzeFRfObhDdY7",u={baseURL:"http://localhost:8081",timeout:1e3*180,method:"post"},c=d.create(u);c.interceptors.request.use(e=>(e.headers["Content-Type"]="application/json",e),e=>(console.log(e),Promise.reject(e)));const t=d.create(u);t.interceptors.request.use(e=>(i()&&(e.headers["X-Authorization"]=`bearer ${i()}`),n()&&(e.headers.currentTenant=n()),e),e=>(console.log(e),Promise.reject(e)));t.interceptors.response.use(e=>{var r;const o=e.status;return o!==200&&o!==201&&o!==204?Promise.reject(new Error(((r=e.data)==null?void 0:r.message)||"错误")):e},e=>{var r,a,s;const o=((a=(r=e.response)==null?void 0:r.data)==null?void 0:a.message)||((s=e.response)==null?void 0:s.message)||e.message;return Promise.reject(new Error(o))});function m(e){return t({url:"/api/video/save",method:"post",data:e})}function h(e,o){return t({url:`/api/video/findByProject/${e}`,method:"get",params:o})}function v(e){return t({url:`/api/video/delete/${e}`,method:"delete"})}function f(e){return t({url:"/api/aep/sign",method:"post",data:e})}function P(e){return c({url:`/api/dict/device/ctrl/ptz?appkey=${p}`,method:"post",data:e,headers:{"Content-Type":"application/JSON"}})}function y(e){return t({url:`/api/video/getPreviewUrlById/${e}`,method:"get"})}function j(e){return t({url:"/api/video/controlling",method:"post",data:e})}function C(e){return t({url:"/api/video/getPlayback",method:"get",params:e})}function $(e){return t({url:"/api/rtsp/hlv",method:"get",params:e})}function B(e,o){return t({url:`/api/video/findByProject/${e}`,method:"get",params:o})}const T=(e,o)=>t({url:`/api/video/findByProject/${e}`,method:"get",params:o}),V=e=>t({url:"/api/video/batchSave",method:"post",data:e}),b=e=>t({url:`/api/video/group/tree/${e.projectId}`,method:"get"}),E=e=>t({url:"/api/video/group/save",method:"post",data:e}),L=e=>t({url:`/api/video/group/delete/${e}`,method:"delete"}),k=()=>t({url:"/api/video/tree",method:"get"}),G=e=>t({url:"/api/video/findList",method:"get",params:e}),S=e=>t({url:"/api/video/findByPage",method:"get",params:e});export{g as M,T as S,f as a,P as b,j as c,y as d,$ as e,B as f,C as g,L as h,b as i,h as j,G as k,v as l,S as m,m as n,k as o,E as p,V as s};
