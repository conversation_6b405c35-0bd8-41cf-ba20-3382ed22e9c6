<!-- 管道流量 -->
<template>
  <RightDrawerMap :title="'管道流量'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['流量分级(m³/h)', '图层控制', '定位']"
      :legends="[
        { label: '0~20m³/h', value: 3145, checked: true },
        { label: '20~25m³/h', value: 211, checked: true },
        { label: '25~30m³/h', value: 184, checked: true },
        { label: '30~35m³/h', value: 41, checked: true },
        { label: '35~40m³/h', value: 22, checked: true },
        { label: '40~45m³/h', value: 1, checked: true },
        { label: '>45m³/h', value: 0, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
import SliderBar from '../components/SliderBar.vue'
</script>
<style lang="scss" scoped></style>
