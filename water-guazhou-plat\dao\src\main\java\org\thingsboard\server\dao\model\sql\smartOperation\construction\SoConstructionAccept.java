package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionAccept implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    @TableField(exist = false)
    private String constructionName;

    // 所属工程类型id
    @TableField(exist = false)
    private String constructionTypeId;

    // 所属工程类型名称
    @TableField(exist = false)
    private String constructionTypeName;

    // 所属项目编号
    @TableField(exist = false)
    private String projectCode;

    // 所属项目名称
    @TableField(exist = false)
    private String projectName;

    // 开始时间
    private Date beginTime;

    // 完成时间
    private Date endTime;

    // 申请单位
    private String applicantOrganization;

    // 申请人
    private String applicant;

    // 申请人电话
    private String applicantPhone;

    // 施工单位
    private String constructOrganization;

    // 监理单位
    private String supervisorOrganization;

    // 审计单位
    private String auditOrganization;

    // 设计单位
    private String designOrganization;

    // 当前状态
    private SoGeneralTaskStatus status;

    // 验收说明
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 最后更新时间
    private Date updateTime;

    // 客户id
    private String tenantId;

}
