<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardGroupPartnerMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        group_id,
        user_id,
        (select customer_role.name
         from customer_user_role
                  join customer_role
                       on customer_user_role.role_id = customer_role.id
         where user_id = guard_group_partner.user_id) role_name
        <!--@sql from guard_group_partner -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroupPartner">
        <result column="group_id" property="groupId"/>
        <result column="user_id" property="userId"/>
        <result column="role_name" property="roleName"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_group_partner
        <where>
            <if test="groupId != null and groupId != ''">
                and group_id = #{groupId}
            </if>
        </where>
    </select>

    <delete id="deleteAllByGroupId">
        delete from guard_group_partner
        where group_id = #{groupId}
    </delete>

    <insert id="saveAll">
        INSERT INTO guard_group_partner(id,
                                        group_id,
                                        user_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.groupId},
             #{element.userId})
        </foreach>
    </insert>
</mapper>