import{e as a,y as t,a as o,j as h,i as l}from"./Point-WxyopZva.js";import{a as d}from"./widget-BcWKanF2.js";import"./index-r0dFAfgr.js";const f=s=>{let e=class extends s{initialize(){this.handles.add(d(()=>this.layer,"refresh",i=>{this.doRefresh(i.dataChanged).catch(r=>{h(r)||l.getLogger(this.declaredClass).error(r)})}),"RefreshableLayerView")}};return a([t()],e.prototype,"layer",void 0),e=a([o("esri.layers.mixins.RefreshableLayerView")],e),e};export{f as i};
