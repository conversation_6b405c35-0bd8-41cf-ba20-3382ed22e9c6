package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_TABLE)
public class AssetsAccountComponentEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_PID)
    private String pid;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_COMPONENT_ID)
    private String componentId;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_NUM)
    private Integer num;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_COMPONENT_TENANT_ID)
    private String tenantId;

    private transient String componentNo;

    private transient String name;

    private transient String type;

    private transient String specification;

    private transient String unit;

}
