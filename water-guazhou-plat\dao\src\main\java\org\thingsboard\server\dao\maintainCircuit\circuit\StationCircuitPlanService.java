package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationCircuitPlanListRequest;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitPlan;

import java.util.List;

public interface StationCircuitPlanService {

    void save(StationCircuitPlan entity);

    /**
     * 分页查询站点养护计划
     *
     * @param request 请求参数
     * @return 数据
     */
    PageData<StationCircuitPlan> findList(StationCircuitPlanListRequest request);

    void remove(List<String> ids);
}
