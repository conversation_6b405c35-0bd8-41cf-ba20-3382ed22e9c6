import { ref, onBeforeMount, computed } from 'vue'
import useGlobal from '@/hooks/global/useGlobal'

export default (props, ctx, _getDetail) => {
  const dataForm = ref<any>({ id: null, jobList: [], users: [] })
  const curJob = ref<string>('')
  const { $format, $messageError } = useGlobal()

  const visible = computed(() => props.config.visible)

  const getDetail = async () => {
    try {
      const res = await _getDetail(dataForm.value.id)
      console.log(res.data, '_getDetail_getDetail')

      const detail = res.data
      detail.jobList.forEach(item => {
        item.criterionDetail = JSON.parse(item.criterionDetail)
      })

      dataForm.value = detail
      if (dataForm.value.jobList[0]) {
        curJob.value = dataForm.value.jobList[0].id
      }
      if (dataForm.value.users) {
        dataForm.value.users = dataForm.value.users.split(',')
      } else {
        dataForm.value.users = []
      }
    } catch (err:any) {
      $messageError(err.data.message)
    }
  }

  const getTime = (val: string | Date, format?: string) => $format(val, format)

  onBeforeMount(() => {
    dataForm.value.id = props.config.currentId
    if (dataForm.value.id) getDetail()
  })

  const jobStatus = ref<string[]>(['未完成', '已完成', '处理中', '记录故障'])

  return {
    dataForm,
    curJob,
    jobStatus,
    getTime,
    visible
  }
}
