package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class MessageRecordSaveRequest extends SaveRequest<MessageRecord> {
    // 接收人
    private String receiveUserId;

    // 消息内容
    private String content;

    // 接收人手机号
    private String receivePhone;

    // 发送人
    private String sendUserId;

    // 发送时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    // 是否成功
    private MessageRecordStatus status;

    public MessageRecord build() {
        MessageRecord entity = new MessageRecord();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public MessageRecord update(String id) {
        disallowUpdate();
        MessageRecord entity = new MessageRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(MessageRecord entity) {
        entity.setReceiveUserId(receiveUserId);
        entity.setContent(content);
        entity.setReceivePhone(receivePhone);
        entity.setSendUserId(sendUserId);
        entity.setSendTime(sendTime);
        entity.setStatus(status);
    }
}