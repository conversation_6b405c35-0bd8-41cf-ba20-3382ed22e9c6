package org.thingsboard.server.dao.util.imodel.query.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceUsageJournal;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class DeviceUsageJournalSaveRequest extends SaveRequest<DeviceUsageJournal> {
    // 使用部门
    private String departmentId;

    // 设备标签
    private String deviceLabelCode;

    // 使用人员
    private String userId;

    // 领用时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveTime;

    // 备注
    private String remark;

    @Override
    protected DeviceUsageJournal build() {
        DeviceUsageJournal entity = new DeviceUsageJournal();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected DeviceUsageJournal update(String id) {
        DeviceUsageJournal entity = new DeviceUsageJournal();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(DeviceUsageJournal entity) {
        entity.setDepartmentId(departmentId);
        entity.setUserId(userId);
        entity.setDeviceLabelCode(deviceLabelCode);
        entity.setReceiveTime(receiveTime);
        entity.setRemark(remark);
    }
}