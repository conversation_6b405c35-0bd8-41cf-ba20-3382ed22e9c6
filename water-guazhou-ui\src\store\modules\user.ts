import Cookies from 'js-cookie';
import { cloneDeep } from 'lodash-es';
import storage from '@/utils/storage';
import { getTenantInfo, setCurTenant } from '@/api/tenant';
import { getInfo, login } from '@/api/login';
import { SLMessage } from '@/utils/Message';
import { removeSlash } from '@/utils/removeIdSlash';
import { getCurUserBtnPerms } from '@/api/permission';
import router from '@/router';
import { refreshAllRoutes } from '@/utils/RouterHelper';
import { useBusinessStore } from '..';
import { useAppStore } from '..';
import { useTagsStore } from '..';
import { usePermissionStore } from '..';

export const useUserStore = defineStore('userStore', () => {
  const initialState: Store_User_State = {
    user: undefined,
    appname: '',
    status: '',
    token: Cookies.get('Admin-Token'),
    name: '',
    avatar: '',
    roles: [],
    id: '',
    tenantId: '',
    departmentId: '',
    sysTenantId: '',
    email: '',
    firstName: '',
    lastName: '',
    tenantList: [],
    scadaToken: Cookies.get('scadaToken') || '',
    playedAlarmList: [],
    tenantInfo: undefined
  };
  const state = reactive<Store_User_State>(initialState);
  const actions = {
    SET_user: (user) => {
      state.user = user;
      storage.set('uinfo', user);
    },
    SET_APPNAME: (name) => {
      state.name = name;
    },
    SET_TOKEN: (token) => {
      state.token = token;
      Cookies.set('Admin-Token', token);
    },
    SET_NAME: (name) => {
      state.name = name;
    },
    SET_STATUS: (status) => {
      state.status = status;
    },
    SET_AVATAR: (avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (roles) => {
      state.roles = roles;
    },
    SET_ID: (id) => {
      state.id = id;
    },
    SET_EMAIL: (email) => {
      state.email = email;
    },
    SET_FIRSTNAME: (firstName) => {
      state.firstName = firstName;
    },
    SET_LASTNAME: (lastName) => {
      state.lastName = lastName;
    },
    SET_TENANTID: (tenantId) => {
      state.tenantId = tenantId;
    },
    SET_DEPARTMENTID: (departmentId) => {
      state.departmentId = departmentId;
    },
    SET_SYS_TENANTID: (tenantId) => {
      if (tenantId.sys) {
        state.sysTenantId = state.tenantId;
        state.tenantId = tenantId.id;
      } else {
        state.tenantId = tenantId.sysTenantId;
        state.sysTenantId = '';
      }
    },
    SET_SCADA_TOKEN: (scadaToken) => {
      Cookies.set('scadaToken', scadaToken);
      state.scadaToken = scadaToken;
    },
    SET_TENANT_LIST: (tenantList) => {
      state.tenantList = tenantList;
      if (tenantList[0]) {
        setCurTenant(tenantList[0].id);
      }
    },
    ADD_PLAYED_ALARM: (id) => {
      state.playedAlarmList?.push(id);
    },
    /**
     * 初始化企业信息
     */
    async InitTenantInfo() {
      try {
        const enterpriseInfo = await getTenantInfo(
          state.user?.tenantId?.id || ''
        );
        const additionalInfo = enterpriseInfo.data?.additionalInfo;
        state.tenantInfo = {
          ...(enterpriseInfo.data || {}),
          additionalInfo:
            typeof additionalInfo === 'string'
              ? JSON.parse(additionalInfo)
              : additionalInfo || {}
        };
      } catch (error) {
        console.log('get tenant fail');
      }
    },
    // 登录
    async Login(userInfo) {
      const businessStore = useBusinessStore();
      try {
        const params = {
          username: userInfo.username,
          password: userInfo.password,
          verifyCode: userInfo.verifyCode,
          requestId: userInfo.requestId
          // username: username,
          // password: userInfo.password
        };
        const res = await login(params);
        actions.SET_TOKEN(res.data.token);
        // parse user id from token
        const tokenBody = JSON.parse(
          atob(
            res.data.token
              .match(/\.(.*?)\./)[1]
              .replace(/-/g, '+')
              .replace(/_/g, '/')
          )
        );
        // FIXME: SET_ID is not working
        actions.SET_ID(tokenBody.userId);
        Cookies.set('userId', tokenBody.userId);
        await actions.GetInfo();
        await actions.InitTenantInfo();
        // const businessStore = useBusinessStore()
        // 设置全局项目树
        await businessStore.INIT_projectInfo(true);
        await refreshAllRoutes();
      } catch (error: any) {
        SLMessage.error(error || '登录失败');
      }
    },

    // 获取用户信息
    async GetInfo() {
      try {
        const userId = Cookies.get('userId');
        const res = await getInfo(userId);
        const data = res.data;
        const authority = [data.authority];
        actions.SET_ROLES(authority || []);
        actions.SET_NAME(data.name);
        actions.SET_AVATAR(data.avatar);
        actions.SET_TENANTID(data.tenantId.id);
        actions.SET_DEPARTMENTID(data.departmentId);
        actions.SET_ID(data.id.id);
        actions.SET_EMAIL(data.email);
        actions.SET_FIRSTNAME(data.firstName);
        actions.SET_LASTNAME(data.lastName);
        Cookies.set('userId', data.id.id);
        Cookies.set('tenantId', data.tenantId.id);
        Cookies.set('departmentId', data.departmentId);
        Cookies.set('serialNo', data.serialNo);
        const additionalInfo =
          typeof data.additionalInfo === 'string'
            ? JSON.parse(data.additionalInfo)
            : data.additionalInfo || {};
        const isDark = additionalInfo.isDark ?? true;
        const menuType = additionalInfo.menuType ?? 'top';
        const showTags = additionalInfo.showTags ?? true;
        // 处理主题相关
        const appStore = useAppStore();
        const tagsStore = useTagsStore();
        appStore.SET_isDark(isDark);
        appStore.TOGGLE_menuType(menuType);
        tagsStore.TOGGLE_showTags(showTags);
        data.additionalInfo = {
          ...additionalInfo,
          isDark,
          menuType,
          showTags
        };
        actions.SET_user(data);

        // 处理地图相关
        console.log('=== 用户配置调试信息 ===');
        console.log('用户additionalInfo:', additionalInfo);
        console.log('全局配置 gisDefaultBaseMap:', window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap);
        console.log('用户配置 gisDefaultBaseMap:', additionalInfo.gisDefaultBaseMap);
        
        window.SITE_CONFIG = {
          ...window.SITE_CONFIG,
          GIS_CONFIG: {
            ...window.SITE_CONFIG.GIS_CONFIG,
            gisDefaultBaseMap: additionalInfo.gisDefaultBaseMap ?? window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap ?? 'vec_w',
            gisDefaultPoi: additionalInfo.gisDefaultPoi ?? window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi ?? 'cva_w',
            gisDefaultBaseMapFilterColor:
              additionalInfo.gisDefaultBaseMapFilterColor === undefined
                ? window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMapFilterColor
                : additionalInfo.gisDefaultBaseMapFilterColor
          }
        };
        
        console.log('最终配置 gisDefaultBaseMap:', window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap);
        console.log('=== 用户配置调试信息结束 ===');
      } catch (error) {
        console.log('用户验证信息失败，请重新登录');
        router.push({ path: '/login' });
      }
    },
    async GetBtnPerms() {
      const permissionStore = usePermissionStore();
      // 处理按钮权限
      const btnPermRes = await getCurUserBtnPerms();
      permissionStore.SetCurUserBtnPerms(btnPermRes.data || []);
    },
    // 跳转替换信息
    ToggleRoles: (role) => {
      actions.SET_ROLES(role);
    },
    ToggleTenantId: (tenantId) => {
      actions.SET_SYS_TENANTID(tenantId);
    },
    ToggleToken: (token) => {
      actions.SET_TOKEN(token);
    },
    ToggleScadaToken: (token) => {
      actions.SET_SCADA_TOKEN(token);
    },

    // 登出
    LogOut: () => {
      const appStore = useAppStore();
      appStore.REAL_TIME_MONITOR([]);
      appStore.HistoryTimeMonitor({});
      appStore.REMOVE_appInfos();
      const tagsStore = useTagsStore();
      actions.SET_TOKEN('');
      actions.SET_ROLES([]);
      tagsStore.REMOVEALL_cachedRouters();
      const pSSign = {
        returnSys: true
      };
      // const userStore = useUserStore()
      // store.user.SetUserProject({ pData: null, isSet: false })
      appStore.ToggleSysSign(pSSign);
      appStore.TOGGLE_menuShow(false);
      const businessStore = useBusinessStore();
      businessStore.SET_BUSINESS_NAVSELECTEDRANGE(undefined);
      businessStore.SET_selectedProject(undefined);
      businessStore.SET_projectList([]);
      // localStorage.clear()
      localStorage.clear();
      localStorage.removeItem('uinfo');
      localStorage.removeItem('navSelectedRange');
      localStorage.removeItem('curNavs');
      localStorage.removeItem('gToken');
      localStorage.removeItem('ysinfo');
      localStorage.removeItem('gUserInfo');
      router.push({ path: '/login' }).finally(() => {
        // location.reload() // 为了重新实例化vue-router对象 避免bug
      });
    },

    // 设置企业列表
    getTenantList(tenantList) {
      const dataList = cloneDeep(tenantList);
      console.log(dataList);
      if (tenantList && tenantList.length) {
        dataList.forEach((tenant) => (tenant.id = removeSlash(tenant.id.id)));
      }
      actions.SET_TENANT_LIST(dataList);
    },

    addPlayedAlarm(id) {
      actions.ADD_PLAYED_ALARM(id);
    }
  };
  return { ...toRefs(state), ...actions };
});
