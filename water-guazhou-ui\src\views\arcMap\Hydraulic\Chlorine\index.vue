<!-- 余氯分析 -->
<template>
  <RightDrawerMap :title="'余氯分析'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['浓度(mg/L)', '图层控制', '定位']"
      :legends="[
        { label: '0~0.05mg/L', value: 4521, checked: true },
        { label: '0.05~0.1mg/L', value: 1121, checked: true },
        { label: '0.1~0.3mg/L', value: 231, checked: true },
        { label: '0.3~0.4mg/L', value: 421, checked: true },
        { label: '0.4~0.4mg/L', value: 0, checked: true },
        { label: '>0.5mg/L', value: 0, checked: true }
      ]"
      :unit="'h'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
import SliderBar from '../components/SliderBar.vue'
</script>
<style lang="scss" scoped></style>
