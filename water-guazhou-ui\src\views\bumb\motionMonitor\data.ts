import * as echarts from 'echarts'

export const initTopLeftChart = dataArr => {
  const value: any = 100 // 阈值
  let colorSet: any = {}
  if (dataArr > value) {
    colorSet = {
      colorIn: 'rgba(250,55,119,1)',
      colorOut: 'rgba(250,55,119,0.6)'
    }
  } else {
    colorSet = {
      colorIn: 'rgba(0,255,255,1)',
      colorOut: 'rgba(0,255,255,0.6)'
    }
  }
  const option = {
    grid: {
      top: 0, // 等价于 y: '16%'
      left: 0,
      right: 0,
      bottom: 40
    },
    tooltip: {
      formatter: '{b} : {c} Mpa'
    },
    color: '#fff',
    series: [
      {
        type: 'gauge',
        axisLabel: {
          show: false
        },
        center: ['50%', '50%'],
        radius: '80%',
        splitNumber: 10,
        axisTick: {
          show: false
        },
        color: '#464646',
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: [
              [dataArr / 100, colorSet.colorIn],
              [1, '#111F42']
              // [1, "yellow"]
            ],
            width: 5
          }
        },
        detail: {
          // formatter: function (value) {
          //   if (value !== 0) {
          //     const num = Math.round(value)
          //     return num.toFixed(0)
          //   } else {
          //     return '0'
          //   }
          // },
          textStyle: {
            // padding: [0, 0, 0, 0],
            fontSize: 25,
            fontWeight: 700,
            color: '#fff'
          },
          offsetCenter: [0, 0]
        },
        title: {
          textStyle: {
            fontSize: 10,
            color: '#fff'
          },
          offsetCenter: [0, '50%']
        },
        data: [
          {
            name: 'Mpa',
            value: dataArr,
            color: '#fff'
          }
        ],
        pointer: {
          show: false,
          showAbove: false,
          length: 0,
          width: 0
        },
        animationDuration: 4000
      },
      {
        type: 'gauge',
        center: ['50%', '50%'],
        radius: '80%',
        splitNumber: 10,
        axisLabel: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        itemStyle: {
          color: '#fff',
          shadowBlur: 10,
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        detail: {
          show: false
        },
        data: [
          {
            value: dataArr,
            name: '出水压力',
            color: '#fff'
          }
        ],
        title: {
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          offsetCenter: [0, '115%']
        },
        pointer: {
          show: false,
          showAbove: false,
          length: 0,
          width: 0
        },
        axisLine: {
          // 仪表盘轴线(轮廓线)相关配置。
          show: true, // 是否显示仪表盘轴线(轮廓线),默认 true。
          lineStyle: {
            // 仪表盘轴线样式。
            color: [[1, colorSet.colorOut]], // 仪表盘的轴线可以被分成不同颜色的多段。每段的  结束位置(范围是[0,1]) 和  颜色  可以通过一个数组来表示。默认取值：[[0.2, '#91c7ae'], [0.8, '#63869e'], [1, '#c23531']]
            opacity: 1, // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。
            width: 1, // 轴线宽度,默认 30。
            shadowBlur: 0, // (发光效果)图形阴影的模糊大小。该属性配合 shadowColor,shadowOffsetX, shadowOffsetY 一起设置图形的阴影效果。
            shadowColor: '#fff' // 阴影颜色。支持的格式同color。
          }
        }
      }
    ]
  }
  return option
}
export const initTopRightChart = value => {
  const option = {
    title: {
      text: '水位',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWight: 400
      },
      bottom: 0,
      left: 'center'
    },
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    series: [
      {
        name: '水位',
        type: 'liquidFill',
        data: [
          {
            value: 0.6,
            direction: 'left',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(125,205,239,0.80)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(13,115,185,0.80)' // 100% 处的颜色
                  }
                ]
              }
            }
          },
          {
            value: 0.5,
            direction: 'left',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(125,205,239,0.80)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(13,115,185,0.80)' // 100% 处的颜色
                  }
                ]
              }
            }
          },
          {
            value: 0.4,
            direction: 'left',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(125,205,239,0.80)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(13,115,185,0.80)' // 100% 处的颜色
                  }
                ]
              }
            }
          }
        ],
        radius: '80%',
        outline: {
          show: false
        },
        backgroundStyle: {
          borderColor: 'rgba(97,182,223,1)',
          borderWidth: 1,
          color: 'transparent'
        },
        label: {
          position: ['50%', '50%'],
          formatter() {
            return `${value} m`
          },
          fontWight: 700,
          fontSize: 25,
          color: '#fff'
        },
        center: ['50%', '50%']
      }
    ]
  }

  return option
}
export const initBottomChart = (xData, Data, Data1) => {
  const option = {
    textStyle: {
      color: '#fff'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: true,
      right: 10,
      top: 10,
      textStyle: {
        color: '#fff'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData || [],
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '供水量',
      nameLoacation: 'top',
      splitLine: {
        lineStyle: {
          color: '#fff',
          opacity: 0.2
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },
      boundaryGap: [0, '100%']
    },
    grid: {
      top: 40,
      left: 50,
      right: 20,
      bottom: 30
    },
    series: [
      {
        name: '昨日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#D7A540'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(215,165,64,1)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(215,165,64,0.1)' // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
          // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     {
          //         offset: 0,
          //         color: "#D7A540",
          //     },
          //     {
          //         offset: 1,
          //         color: "#D7A540",
          //     },
          // ]),
        },
        data: Data || []
      },
      {
        name: '今日',
        type: 'line',
        symbol: 'none',
        sampling: 'lttb',
        itemStyle: {
          color: '#4098D7'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(64,152,215,1)'
            },
            {
              offset: 1,
              color: 'rgba(64,152,215,0.1)'
            }
          ])
        },
        data: Data1 || []
      }
    ]
  }
  return option
}
export const initQuShiTuChart = (type, xData, Data) => {
  // 指定图表的配置项和数据
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category', // 坐标类型
      data: xData || [],
      // ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
      show: true, // 是否显示
      // position: 'bottom', // 位置
      // name: '时间', // 坐标轴的名称
      // nameLocation: 'end', // 坐标轴名称位置

      boundaryGap: false,

      color: '#fff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      }
    },
    yAxis: {
      name: '值',
      type: 'value',
      scale: true,
      color: '#ffffff',
      axisLabel: {
        textStyle: {
          color: '#ffffff'
        }
      },
      splitArea: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#ffffff',
          opacity: 0.2,
          type: 'dashed'
        }
      }
    },
    grid: {
      left: 70,
      right: 50,
      top: 10,
      bottom: 30
    },
    series: [
      {
        type: 'line',
        symbol: false,
        smooth: true,

        name: type,
        itemStyle: {
          color: '#4098D7'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(64,152,215,1)'
            },
            {
              offset: 1,
              color: 'rgba(64,152,215,0.1)'
            }
          ])
        },
        data: Data || []
        // [6043, 5321, 6432, 4532, 8325, 10897, 8765]
      }
    ]
  }
  return option
}
export const initShujuBaoBiao = () => ({
  tags: [
    { label: '流量', value: 'liuliang' },
    { label: '压力', value: 'yali' },
    { label: '余氯', value: 'yulv' },
    { label: '浊度', value: 'zhuodu' },
    { label: 'PH', value: 'ph' }
  ],
  fields: [
    { label: '时间', value: 'time' },
    { label: '1#泵站', value: 'bumb1' },
    { label: '2#泵站', value: 'bumb2' },
    { label: '3#泵站', value: 'bumb3' },
    { label: '4#泵站', value: 'bumb4' },
    { label: '5#泵站', value: 'bumb5' }
  ],
  tabledata: [
    {
      time: '04/10 14:24',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 14:14',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },

    {
      time: '04/10 14:04',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },

    {
      time: '04/10 13:55',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 13:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 13:14',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 12:54',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    },
    {
      time: '04/10 11:34',
      bumb1: (Math.random() * 100).toFixed(2),
      bumb2: (Math.random() * 100).toFixed(2),
      bumb3: (Math.random() * 100).toFixed(2),
      bumb4: (Math.random() * 100).toFixed(2),
      bumb5: (Math.random() * 100).toFixed(2)
    }
  ]
})
export const initYuanChengKongZhi = () => [
  {
    id: 'no1',
    name: '1#泵',
    status: 'online',
    timetotal: '53:24:05'
  },
  {
    id: 'no2',
    name: '2#泵',
    status: 'online',
    timetotal: '72:15:33'
  },
  {
    id: 'no3',
    name: '3#泵',
    status: 'offline',
    timetotal: '21:24:05'
  },
  {
    id: 'no4',
    name: '4#泵',
    status: 'online',
    timetotal: '14:24:05'
  },
  {
    id: 'no5',
    name: '5#泵',
    status: 'online',
    timetotal: '56:24:05'
  },
  {
    id: 'no6',
    name: '5#泵',
    status: 'online',
    timetotal: '56:24:05'
  },
  {
    id: 'no7',
    name: '5#泵',
    status: 'online',
    timetotal: '56:24:05'
  },
  {
    id: 'no8',
    name: '5#泵',
    status: 'online',
    timetotal: '56:24:05'
  },
  {
    id: 'no9',
    name: '5#泵',
    status: 'online',
    timetotal: '56:24:05'
  },
  {
    id: 'no10',
    name: '5#泵',
    status: 'online',
    timetotal: '56:24:05'
  }
]
export const initBJSZTableColumns = (): IFormTableColumn[] => [
  { minWidth: 120, label: '监测点名称', prop: 'name' },
  {
    minWidth: 120,
    label: '流量',
    prop: 'liuliang',
    formItemConfig: { type: 'input', label: '', field: '' }
  },
  {
    minWidth: 120,
    label: '压力',
    prop: 'yali',
    formItemConfig: { type: 'input', label: '', field: '' }
  },
  {
    minWidth: 120,
    label: '余氯',
    prop: 'yulv',
    formItemConfig: { type: 'input', label: '', field: '' }
  },
  {
    minWidth: 120,
    label: '浊度',
    prop: 'zhuodu',
    formItemConfig: { type: 'input', label: '', field: '' }
  },
  {
    minWidth: 120,
    label: 'PH',
    prop: 'ph',
    formItemConfig: { type: 'input', label: '', field: '' }
  }
]
export const initBJSZTableData = () => [
  {
    id: '1',
    name: '1#监测点',
    liuliang: '20',
    yali: '40',
    yulv: '2.22',
    zhuodu: '0.05',
    ph: '6.9'
  },
  {
    id: '2',
    name: '2#监测点',
    liuliang: '',
    yali: '',
    yulv: '',
    zhuodu: '',
    ph: ''
  },
  {
    id: '3',
    name: '3#监测点',
    liuliang: '',
    yali: '',
    yulv: '',
    zhuodu: '',
    ph: ''
  },
  {
    id: '4',
    name: '4#监测点',
    liuliang: '',
    yali: '',
    yulv: '',
    zhuodu: '',
    ph: ''
  }
]
