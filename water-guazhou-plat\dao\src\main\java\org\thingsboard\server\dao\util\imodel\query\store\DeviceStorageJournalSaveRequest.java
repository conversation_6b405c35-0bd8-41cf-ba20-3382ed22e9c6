package org.thingsboard.server.dao.util.imodel.query.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceStorageJournal;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class DeviceStorageJournalSaveRequest extends SaveRequest<DeviceStorageJournal> {
    // 设备编码
    private String serialId;

    // 设备标签
    private String deviceLabelCode;

    // 供应商ID
    private String supplierId;

    // 报废时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scrappedTime;

    // 所在仓库
    private String storehouseId;

    // 所在货架
    private String shelvesId;

    // 最后保养时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastMaintainanceTime;

    // 最后巡检时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastInspectionTime;

    @Override
    protected DeviceStorageJournal build() {
        DeviceStorageJournal entity = new DeviceStorageJournal();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected DeviceStorageJournal update(String id) {
        DeviceStorageJournal entity = new DeviceStorageJournal();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(DeviceStorageJournal entity) {
        entity.setSerialId(serialId);
        entity.setDeviceLabelCode(deviceLabelCode);
        entity.setSupplierId(supplierId);
        // entity.setScrappedTime(scrappedTime);
        entity.setStorehouseId(storehouseId);
        entity.setShelvesId(shelvesId);
        entity.setLastMaintainanceTime(lastMaintainanceTime);
        entity.setLastInspectionTime(lastInspectionTime);

    }
}