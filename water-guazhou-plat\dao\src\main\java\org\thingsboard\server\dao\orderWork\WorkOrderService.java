package org.thingsboard.server.dao.orderWork;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.WorkOrderEntity;

import java.util.Date;
import java.util.List;

@Deprecated
public interface WorkOrderService {
    WorkOrderEntity saveWorkOrder(WorkOrderEntity entity, User currentUser);

    PageData<WorkOrderEntity> findList(String type, String code, boolean complete, int page, int size, User currentUser,
                                       Long createTimeStart, Long createTimeEnd, String priority, String status, String orderType,
                                       String creator, String executor);

    WorkOrderEntity getDetail(String id, TenantId tenantId, User currentUser);

    void process(WorkOrderEntity entity, User currentUser);

    void confirm(JSONObject params, User currentUser);

    void acceptance(WorkOrderEntity entity, boolean acceptance, User currentUser);

    WorkOrderEntity findById(String id);

    List<WorkOrderEntity> findByTenantIdAndType(String tenantId, String type);

    JSONObject follow(JSONObject params);

    Object countOrder(String countType, String queryType, Boolean complete, User currentUser);

    List<WorkOrderEntity> findByTenantId(TenantId tenantId);

    List<WorkOrderEntity> findByTenantIdAndTime(TenantId tenantId, Date startTime, Date endTime);
}
