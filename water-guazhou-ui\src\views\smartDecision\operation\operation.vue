<!-- 智慧运营 -->
<template>
  <div class="content">
    <!-- 底图 -->
    <div class="map">
      <ArcView :map-config="mapConfig">
        <!-- <ArcPipe></ArcPipe>
        <ArcStationWarning></ArcStationWarning> -->
      </ArcView>
    </div>
    <!-- <ArcMap
      ref="refMap"
      class="map"
    /> -->
    <div class="content_left">
      <TitleCard
        :title="'报警统计'"
        class="chart-box bjtj"
      >
        <!-- <div class="chart-wrapper">
          <div class="left">
            <VChart :option="state.materialOption"></VChart>
          </div>

          <div class="right">
            <VChart :option="state.diameterOption"></VChart>
          </div>
        </div> -->
        <BJTJ></BJTJ>
      </TitleCard>
      <TitleCard
        :title="'设备类型数量统计'"
        class="chart-box"
      >
        <SBLXSLTJ></SBLXSLTJ>
      </TitleCard>
      <TitleCard
        :title="'供水概览'"
        class="chart-box zgsl"
      >
        <SCDD></SCDD>
      </TitleCard>
      <!-- <TitleCard
        :title="'人力构成'"
        class="chart-box"
      >
        <PersonelPie></PersonelPie>
      </TitleCard> -->
    </div>
    <div class="content_right">
      <TitleCard
        :title="'工单统计'"
        class="chart-box"
      >
        <XJWCL></XJWCL>
      </TitleCard>
      <TitleCard
        :title="'工单类型数量统计'"
        class="chart-box"
      >
        <GDLXSLTJ />
      </TitleCard>
      <TitleCard
        :title="'抢修工作量'"
        class="chart-box"
      >
        <QXGZL></QXGZL>
      </TitleCard>
      <TitleCard
        :title="'应急数量分析'"
        class="chart-box"
      >
        <YJDJFX></YJDJFX>
      </TitleCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import { IArcMapIns } from '@/components/type'
import TitleCard from '../components/TitleCard.vue'
// import PersonelPie from '../overview/components/PersonelPie.vue'
import XJWCL from './components/XJWCL_wudang.vue'
import GDLXSLTJ from './components/GDLXSLTJ.vue'
import QXGZL from './components/QXGZL.vue'
import BJTJ from './components/BJTJ.vue'
import SBLXSLTJ from './components/SBLXSLTJ.vue'
import YJDJFX from './components/YJDJFX.vue'
import SCDD from '../overview/components/SCDD.vue'

// const refMap = ref<IArcMapIns>()
const mapConfig = ref<IFormGisConfig>({
  defaultBaseMap: 'vec_w',
  defaultFilter: 'grayscale(0%) invert(100%) opacity(100%)',
  defaultFilterColor: 'rgb(255 218 189)'
})
// const staticState: {
//   view?: __esri.MapView
// } = {}
// const onMapLoaded = (view: __esri.MapView) => {
//   staticState.view = view
// }
// onMounted(async () => {
//   staticState.view = await refMap.value?.init({
//     defaultFilter: 'grayscale(0%) invert(100%) opacity(100%)',
//     defaultFilterColor: 'rgb(255 218 189)'
//   })
// })
// onBeforeUnmount(() => {
//   staticState.view?.map.removeAll()
//   staticState.view?.map.destroy()
//   staticState.view?.destroy()
// })
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 100px 50px 30px 50px;
  color: #fff;

  .content_left,
  .content_right {
    width: 479px;
    height: 100%;
    padding: 10px;
  }

  .chart-box {
    height: 225px;
    width: 100%;
    margin-bottom: 12px;
    &.bjtj {
      height: 300px;
    }
    &.zgsl {
      height: 390px;
    }
    .chart-wrapper {
      display: flex;
      width: 100%;
      height: 100%;
      .left,
      .right {
        width: 50%;
      }
    }
  }
}

.map {
  position: absolute;
  top: -100px;
  left: -50px;
  right: -50px;
  bottom: 0;
  width: calc(100% + 100px);
  height: calc(100% + 100px);
  z-index: 0;
  background: transparent;
}
</style>
