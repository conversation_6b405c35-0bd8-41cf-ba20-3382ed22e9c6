<template>
  <div class="panel-title">
    <span>请选择测量工具：</span>
  </div>
  <el-divider></el-divider>
  <div class="measure-tool">
    <div
      class="measure-tool-item length"
      :class="{ active: state.curOperate === 'length' }"
      @click="startDraw('length')"
    >
      <img
        class="img"
        :src="lengthIcon"
        alt=""
      />
    </div>
    <div
      class="measure-tool-item area"
      :class="{ active: state.curOperate === 'area' }"
      @click="startDraw('area')"
    >
      <img
        class="img"
        :src="areaIcon"
        alt=""
      />
    </div>
    <div
      class="measure-tool-item angle"
      :class="{ active: state.curOperate === 'angle' }"
      @click="startDraw('angle')"
    >
      <img
        class="img"
        :src="angleIcon"
        alt=""
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import lengthIcon from '@/assets/images/gis/length.png'
import angleIcon from '@/assets/images/gis/angle.png'
import areaIcon from '@/assets/images/gis/area.png'
import {
  createPolygon,
  createPolyline,
  createGraphic,
  getGraphicLayer,
  initDrawer,
  createGeometry,
  setSymbol,
  calcArea,
  setMapCursor,
  calcLength,
  calcAngle
} from '@/utils/MapHelper'

const props = defineProps<{
  view?: __esri.MapView
}>()
const state = reactive<{
  curOperate: 'length' | 'area' | 'angle' | ''
}>({
  curOperate: ''
})
const staticState: {
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  graphicsLayer?: __esri.GraphicsLayer
  textLayer?: __esri.GraphicsLayer
  moveLayer?: __esri.GraphicsLayer
} = {}
const startDraw = (type: 'length' | 'area' | 'angle' | '') => {
  if (!props.view) return
  state.curOperate = type
  setMapCursor('crosshair')
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'measure-layer',
    title: '测量绘制图层'
  })
  staticState.textLayer = getGraphicLayer(props.view, {
    id: 'measure-text',
    title: '测量结果图层'
  })
  staticState.moveLayer = getGraphicLayer(props.view, {
    id: 'measure-move',
    title: '移动测量图层'
  })
  staticState.graphicsLayer?.removeAll()
  staticState.textLayer?.removeAll()
  staticState.moveLayer?.removeAll()
  staticState.drawer = initDrawer(props.view)
  staticState.drawAction = staticState.drawer.create(
    type === 'angle' || type === 'length' ? 'polyline' : 'polygon'
  )
  staticState.drawAction.on(['vertex-add', 'cursor-update'], updateVertices)

  staticState.drawAction.on('draw-complete', e => {
    updateVertices(e)
    setMapCursor('')
  })
}

const updateVertices = (e: any) => {
  console.log(e)
  const isUpdate = e.type === 'cursor-update'
  const isMoveAddAble = state.curOperate === 'area' || state.curOperate === 'angle'
    ? e.vertices.length >= 3
    : state.curOperate === 'length'
      ? e.vertices.length > 1
      : false
  const type = state.curOperate
  let graphic: __esri.Graphic | undefined
  let text: __esri.Graphic | undefined

  if (type === 'area') {
    graphic = e.vertices.length < 3
      ? createPolyline(e.vertices, props.view?.spatialReference)
      : createPolygon(e.vertices, props.view?.spatialReference)
    if (e.vertices?.length >= 3) {
      const area = calcArea(e.vertices, undefined, props.view?.spatialReference)
      text = createGraphic({
        geometry: createGeometry(
          'polygon',
          e.vertices,
          props.view?.spatialReference
        ),
        symbol: setSymbol('text', {
          text: '总面积：' + (area?.toFixed(2) || '0') + ' ㎡',
          yOffset: 10
        })
      })
    }
  } else {
    graphic = createPolyline(e.vertices, props.view?.spatialReference)
    if (e.vertices.length) {
      if (type === 'length') {
        text = createGraphic({
          geometry: createGeometry(
            'point',
            [e.vertices[e.vertices.length - 1]],
            props.view?.spatialReference
          ),
          symbol: setSymbol('text', {
            text:
              e.vertices?.length === 1
                ? '0 m'
                : calcLength(
                  e.vertices,
                  undefined,
                  props.view?.spatialReference
                ).toFixed(2) + ' m',
            yOffset: 10
          })
        })
      } else if (type === 'angle') {
        if (e.vertices.length >= 3) {
          const vertices: number[][] = e.vertices || []
          text = createGraphic({
            geometry: createGeometry(
              'point',
              [vertices[vertices.length - 2]],
              props.view?.spatialReference
            ),
            symbol: setSymbol('text', {
              text:
                e.vertices?.length === 1
                  ? '0 度'
                  : calcAngle(vertices.slice(vertices.length - 3)).toFixed(2)
                    + ' 度',
              yOffset: 10
            })
          })
        }
      }
    }
  }
  staticState.moveLayer?.removeAll()
  staticState.graphicsLayer?.removeAll()
  if (state.curOperate === 'area') staticState.textLayer?.removeAll()
  graphic && staticState.graphicsLayer?.add(graphic)
  text
    && (isUpdate
      ? isMoveAddAble && staticState.moveLayer?.add(text)
      : staticState.textLayer?.add(text))
}
const destroy = () => {
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.graphicsLayer && props.view?.map.remove(staticState.graphicsLayer)
  staticState.textLayer && props.view?.map.remove(staticState.textLayer)
  staticState.moveLayer && props.view?.map.remove(staticState.moveLayer)
}
onBeforeUnmount(() => {
  destroy()
})
</script>
<style lang="scss" scoped>
.measure-tool {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  padding-bottom: 15px;

  &-item {
    flex-basis: 30%;
    height: 100px;
    padding: 20px;
    text-align: center;
    box-sizing: border-box;
    border: 1px dashed rgb(161, 161, 93);
    cursor: pointer;

    &.active,
    &:hover {
      border: 1px solid red;
    }

    .img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
