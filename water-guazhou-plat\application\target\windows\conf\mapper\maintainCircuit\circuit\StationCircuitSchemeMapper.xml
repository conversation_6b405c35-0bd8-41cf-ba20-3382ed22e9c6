<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.circuit.StationCircuitSchemeMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitScheme">
        SELECT *
        FROM tb_station_circuit_scheme a
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="stationType != null and stationType != ''">
                AND a.station_type = #{station_type}
            </if>
            <if test="name != null and name != ''">
                AND a.name LIKE '%'|| #{keyword}||'%'
            </if>
        </where>
        ORDER BY create_time

    </select>
</mapper>