"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6459],{21787:(t,n,r)=>{r.d(n,{a:()=>u,b:()=>f,e:()=>c,f:()=>o,i:()=>h,m:()=>s,s:()=>a,t:()=>i});var e=r(46851);function o(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t}function a(t,n,r,e,o,a,i,c,u,s){return t[0]=n,t[1]=r,t[2]=e,t[3]=o,t[4]=a,t[5]=i,t[6]=c,t[7]=u,t[8]=s,t}function i(t,n){if(t===n){const r=n[1],e=n[2],o=n[5];t[1]=n[3],t[2]=n[6],t[3]=r,t[5]=n[7],t[6]=e,t[7]=o}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t}function c(t,n){const r=n[0],e=n[1],o=n[2],a=n[3],i=n[4],c=n[5],u=n[6],s=n[7],f=n[8],l=f*i-c*s,h=-f*a+c*u,p=s*a-i*u;let y=r*l+e*h+o*p;return y?(y=1/y,t[0]=l*y,t[1]=(-f*e+o*s)*y,t[2]=(c*e-o*i)*y,t[3]=h*y,t[4]=(f*r-o*u)*y,t[5]=(-c*r+o*a)*y,t[6]=p*y,t[7]=(-s*r+e*u)*y,t[8]=(i*r-e*a)*y,t):null}function u(t,n){const r=n[0],e=n[1],o=n[2],a=n[3],i=n[4],c=n[5],u=n[6],s=n[7],f=n[8];return t[0]=i*f-c*s,t[1]=o*s-e*f,t[2]=e*c-o*i,t[3]=c*u-a*f,t[4]=r*f-o*u,t[5]=o*a-r*c,t[6]=a*s-i*u,t[7]=e*u-r*s,t[8]=r*i-e*a,t}function s(t,n,r){const e=n[0],o=n[1],a=n[2],i=n[3],c=n[4],u=n[5],s=n[6],f=n[7],l=n[8],h=r[0],p=r[1],y=r[2],d=r[3],M=r[4],m=r[5],g=r[6],b=r[7],T=r[8];return t[0]=h*e+p*i+y*s,t[1]=h*o+p*c+y*f,t[2]=h*a+p*u+y*l,t[3]=d*e+M*i+m*s,t[4]=d*o+M*c+m*f,t[5]=d*a+M*u+m*l,t[6]=g*e+b*i+T*s,t[7]=g*o+b*c+T*f,t[8]=g*a+b*u+T*l,t}function f(t,n){const r=n[0],e=n[1],o=n[2],a=n[3],i=n[4],c=n[5],u=n[6],s=n[7],f=n[8],l=n[9],h=n[10],p=n[11],y=n[12],d=n[13],M=n[14],m=n[15],g=r*c-e*i,b=r*u-o*i,T=r*s-a*i,P=e*u-o*c,A=e*s-a*c,_=o*s-a*u,F=f*d-l*y,S=f*M-h*y,v=f*m-p*y,B=l*M-h*d,w=l*m-p*d,E=h*m-p*M;let x=g*E-b*w+T*B+P*v-A*S+_*F;return x?(x=1/x,t[0]=(c*E-u*w+s*B)*x,t[1]=(u*v-i*E-s*S)*x,t[2]=(i*w-c*v+s*F)*x,t[3]=(o*w-e*E-a*B)*x,t[4]=(r*E-o*v+a*S)*x,t[5]=(e*v-r*w-a*F)*x,t[6]=(d*_-M*A+m*P)*x,t[7]=(M*T-y*_-m*b)*x,t[8]=(y*A-d*T+m*g)*x,t):null}function l(t,n,r){return t[0]=n[0]-r[0],t[1]=n[1]-r[1],t[2]=n[2]-r[2],t[3]=n[3]-r[3],t[4]=n[4]-r[4],t[5]=n[5]-r[5],t[6]=n[6]-r[6],t[7]=n[7]-r[7],t[8]=n[8]-r[8],t}function h(t){const n=(0,e.g)(),r=t[0],o=t[1],a=t[2],i=t[3],c=t[4],u=t[5],s=t[6],f=t[7],l=t[8];return Math.abs(1-(r*r+i*i+s*s))<=n&&Math.abs(1-(o*o+c*c+f*f))<=n&&Math.abs(1-(a*a+u*u+l*l))<=n}const p=s,y=l;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,n,r){return t[0]=n[0]+r[0],t[1]=n[1]+r[1],t[2]=n[2]+r[2],t[3]=n[3]+r[3],t[4]=n[4]+r[4],t[5]=n[5]+r[5],t[6]=n[6]+r[6],t[7]=n[7]+r[7],t[8]=n[8]+r[8],t},adjoint:u,copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t},determinant:function(t){const n=t[0],r=t[1],e=t[2],o=t[3],a=t[4],i=t[5],c=t[6],u=t[7],s=t[8];return n*(s*a-i*u)+r*(-s*o+i*c)+e*(u*o-a*c)},equals:function(t,n){const r=t[0],o=t[1],a=t[2],i=t[3],c=t[4],u=t[5],s=t[6],f=t[7],l=t[8],h=n[0],p=n[1],y=n[2],d=n[3],M=n[4],m=n[5],g=n[6],b=n[7],T=n[8],P=(0,e.g)();return Math.abs(r-h)<=P*Math.max(1,Math.abs(r),Math.abs(h))&&Math.abs(o-p)<=P*Math.max(1,Math.abs(o),Math.abs(p))&&Math.abs(a-y)<=P*Math.max(1,Math.abs(a),Math.abs(y))&&Math.abs(i-d)<=P*Math.max(1,Math.abs(i),Math.abs(d))&&Math.abs(c-M)<=P*Math.max(1,Math.abs(c),Math.abs(M))&&Math.abs(u-m)<=P*Math.max(1,Math.abs(u),Math.abs(m))&&Math.abs(s-g)<=P*Math.max(1,Math.abs(s),Math.abs(g))&&Math.abs(f-b)<=P*Math.max(1,Math.abs(f),Math.abs(b))&&Math.abs(l-T)<=P*Math.max(1,Math.abs(l),Math.abs(T))},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]},frob:function(t){return Math.sqrt(t[0]**2+t[1]**2+t[2]**2+t[3]**2+t[4]**2+t[5]**2+t[6]**2+t[7]**2+t[8]**2)},fromMat2d:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t},fromMat4:o,fromQuat:function(t,n){const r=n[0],e=n[1],o=n[2],a=n[3],i=r+r,c=e+e,u=o+o,s=r*i,f=e*i,l=e*c,h=o*i,p=o*c,y=o*u,d=a*i,M=a*c,m=a*u;return t[0]=1-l-y,t[3]=f-m,t[6]=h+M,t[1]=f+m,t[4]=1-s-y,t[7]=p-d,t[2]=h-M,t[5]=p+d,t[8]=1-s-l,t},fromRotation:function(t,n){const r=Math.sin(n),e=Math.cos(n);return t[0]=e,t[1]=r,t[2]=0,t[3]=-r,t[4]=e,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromScaling:function(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromTranslation:function(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},invert:c,isOrthoNormal:h,mul:p,multiply:s,multiplyScalar:function(t,n,r){return t[0]=n[0]*r,t[1]=n[1]*r,t[2]=n[2]*r,t[3]=n[3]*r,t[4]=n[4]*r,t[5]=n[5]*r,t[6]=n[6]*r,t[7]=n[7]*r,t[8]=n[8]*r,t},multiplyScalarAndAdd:function(t,n,r,e){return t[0]=n[0]+r[0]*e,t[1]=n[1]+r[1]*e,t[2]=n[2]+r[2]*e,t[3]=n[3]+r[3]*e,t[4]=n[4]+r[4]*e,t[5]=n[5]+r[5]*e,t[6]=n[6]+r[6]*e,t[7]=n[7]+r[7]*e,t[8]=n[8]+r[8]*e,t},normalFromMat4:f,normalFromMat4Legacy:function(t,n){const r=n[0],e=n[1],o=n[2],a=n[4],i=n[5],c=n[6],u=n[8],s=n[9],f=n[10],l=f*i-c*s,h=-f*a+c*u,p=s*a-i*u,y=r*l+e*h+o*p;if(!y)return null;const d=1/y;return t[0]=l*d,t[1]=(-f*e+o*s)*d,t[2]=(c*e-o*i)*d,t[3]=h*d,t[4]=(f*r-o*u)*d,t[5]=(-c*r+o*a)*d,t[6]=p*d,t[7]=(-s*r+e*u)*d,t[8]=(i*r-e*a)*d,t},projection:function(t,n,r){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/r,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t},rotate:function(t,n,r){const e=n[0],o=n[1],a=n[2],i=n[3],c=n[4],u=n[5],s=n[6],f=n[7],l=n[8],h=Math.sin(r),p=Math.cos(r);return t[0]=p*e+h*i,t[1]=p*o+h*c,t[2]=p*a+h*u,t[3]=p*i-h*e,t[4]=p*c-h*o,t[5]=p*u-h*a,t[6]=s,t[7]=f,t[8]=l,t},scale:function(t,n,r){const e=r[0],o=r[1],a=r[2];return t[0]=e*n[0],t[1]=e*n[1],t[2]=e*n[2],t[3]=o*n[3],t[4]=o*n[4],t[5]=o*n[5],t[6]=a*n[6],t[7]=a*n[7],t[8]=a*n[8],t},scaleByVec2:function(t,n,r){const e=r[0],o=r[1];return t[0]=e*n[0],t[1]=e*n[1],t[2]=e*n[2],t[3]=o*n[3],t[4]=o*n[4],t[5]=o*n[5],t},set:a,str:function(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"},sub:y,subtract:l,translate:function(t,n,r){const e=n[0],o=n[1],a=n[2],i=n[3],c=n[4],u=n[5],s=n[6],f=n[7],l=n[8],h=r[0],p=r[1];return t[0]=e,t[1]=o,t[2]=a,t[3]=i,t[4]=c,t[5]=u,t[6]=h*e+p*i+s,t[7]=h*o+p*c+f,t[8]=h*a+p*u+l,t},transpose:i},Symbol.toStringTag,{value:"Module"}))},46521:(t,n,r)=>{function e(){return[1,0,0,0,1,0,0,0,1]}function o(t,n,r,e,o,a,i,c,u){return[t,n,r,e,o,a,i,c,u]}function a(t,n){return new Float64Array(t,n,9)}r.d(n,{a:()=>a,c:()=>e,f:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},create:e,createView:a,fromValues:o},Symbol.toStringTag,{value:"Module"}))},13598:(t,n,r)=>{function e(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function o(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]]}function a(t,n){return new Float64Array(t,n,16)}r.d(n,{I:()=>i,a:()=>a,b:()=>o,c:()=>e});const i=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:i,clone:o,create:e,createView:a,fromValues:function(t,n,r,e,o,a,i,c,u,s,f,l,h,p,y,d){return[t,n,r,e,o,a,i,c,u,s,f,l,h,p,y,d]}},Symbol.toStringTag,{value:"Module"}))},51305:(t,n,r)=>{r.d(n,{c:()=>p,g:()=>f,j:()=>w,k:()=>d,m:()=>l,s:()=>s});var e=r(46521),o=r(94961),a=r(65617),i=r(46851),c=r(17896),u=r(98766);function s(t,n,r){r*=.5;const e=Math.sin(r);return t[0]=e*n[0],t[1]=e*n[1],t[2]=e*n[2],t[3]=Math.cos(r),t}function f(t,n){const r=2*Math.acos(n[3]),e=Math.sin(r/2);return e>(0,i.g)()?(t[0]=n[0]/e,t[1]=n[1]/e,t[2]=n[2]/e):(t[0]=1,t[1]=0,t[2]=0),r}function l(t,n,r){const e=n[0],o=n[1],a=n[2],i=n[3],c=r[0],u=r[1],s=r[2],f=r[3];return t[0]=e*f+i*c+o*s-a*u,t[1]=o*f+i*u+a*c-e*s,t[2]=a*f+i*s+e*u-o*c,t[3]=i*f-e*c-o*u-a*s,t}function h(t,n,r,e){const o=n[0],a=n[1],c=n[2],u=n[3];let s,f,l,h,p,y=r[0],d=r[1],M=r[2],m=r[3];return f=o*y+a*d+c*M+u*m,f<0&&(f=-f,y=-y,d=-d,M=-M,m=-m),1-f>(0,i.g)()?(s=Math.acos(f),l=Math.sin(s),h=Math.sin((1-e)*s)/l,p=Math.sin(e*s)/l):(h=1-e,p=e),t[0]=h*o+p*y,t[1]=h*a+p*d,t[2]=h*c+p*M,t[3]=h*u+p*m,t}function p(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=n[3],t}function y(t,n){const r=n[0]+n[4]+n[8];let e;if(r>0)e=Math.sqrt(r+1),t[3]=.5*e,e=.5/e,t[0]=(n[5]-n[7])*e,t[1]=(n[6]-n[2])*e,t[2]=(n[1]-n[3])*e;else{let r=0;n[4]>n[0]&&(r=1),n[8]>n[3*r+r]&&(r=2);const o=(r+1)%3,a=(r+2)%3;e=Math.sqrt(n[3*r+r]-n[3*o+o]-n[3*a+a]+1),t[r]=.5*e,e=.5/e,t[3]=(n[3*o+a]-n[3*a+o])*e,t[o]=(n[3*o+r]+n[3*r+o])*e,t[a]=(n[3*a+r]+n[3*r+a])*e}return t}function d(t,n,r,e){const o=.5*Math.PI/180;n*=o,r*=o,e*=o;const a=Math.sin(n),i=Math.cos(n),c=Math.sin(r),u=Math.cos(r),s=Math.sin(e),f=Math.cos(e);return t[0]=a*u*f-i*c*s,t[1]=i*c*f+a*u*s,t[2]=i*u*s-a*c*f,t[3]=i*u*f+a*c*s,t}const M=u.c,m=u.s,g=u.a,b=l,T=u.b,P=u.d,A=u.l,_=u.e,F=_,S=u.f,v=S,B=u.n,w=u.g,E=u.h,x=(0,a.c)(),C=(0,a.f)(1,0,0),R=(0,a.f)(0,1,0),O=(0,o.a)(),j=(0,o.a)(),I=(0,e.c)();Object.freeze(Object.defineProperty({__proto__:null,add:g,calculateW:function(t,n){const r=n[0],e=n[1],o=n[2];return t[0]=r,t[1]=e,t[2]=o,t[3]=Math.sqrt(Math.abs(1-r*r-e*e-o*o)),t},conjugate:p,copy:M,dot:P,equals:E,exactEquals:w,fromEuler:d,fromMat3:y,getAxisAngle:f,identity:function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},invert:function(t,n){const r=n[0],e=n[1],o=n[2],a=n[3],i=r*r+e*e+o*o+a*a,c=i?1/i:0;return t[0]=-r*c,t[1]=-e*c,t[2]=-o*c,t[3]=a*c,t},len:F,length:_,lerp:A,mul:b,multiply:l,normalize:B,random:function(t){const n=i.R,r=n(),e=n(),o=n(),a=Math.sqrt(1-r),c=Math.sqrt(r);return t[0]=a*Math.sin(2*Math.PI*e),t[1]=a*Math.cos(2*Math.PI*e),t[2]=c*Math.sin(2*Math.PI*o),t[3]=c*Math.cos(2*Math.PI*o),t},rotateX:function(t,n,r){r*=.5;const e=n[0],o=n[1],a=n[2],i=n[3],c=Math.sin(r),u=Math.cos(r);return t[0]=e*u+i*c,t[1]=o*u+a*c,t[2]=a*u-o*c,t[3]=i*u-e*c,t},rotateY:function(t,n,r){r*=.5;const e=n[0],o=n[1],a=n[2],i=n[3],c=Math.sin(r),u=Math.cos(r);return t[0]=e*u-a*c,t[1]=o*u+i*c,t[2]=a*u+e*c,t[3]=i*u-o*c,t},rotateZ:function(t,n,r){r*=.5;const e=n[0],o=n[1],a=n[2],i=n[3],c=Math.sin(r),u=Math.cos(r);return t[0]=e*u+o*c,t[1]=o*u-e*c,t[2]=a*u+i*c,t[3]=i*u-a*c,t},rotationTo:function(t,n,r){const e=(0,c.e)(n,r);return e<-.999999?((0,c.f)(x,C,n),(0,c.u)(x)<1e-6&&(0,c.f)(x,R,n),(0,c.n)(x,x),s(t,x,Math.PI),t):e>.999999?(t[0]=0,t[1]=0,t[2]=0,t[3]=1,t):((0,c.f)(x,n,r),t[0]=x[0],t[1]=x[1],t[2]=x[2],t[3]=1+e,B(t,t))},scale:T,set:m,setAxes:function(t,n,r,e){const o=I;return o[0]=r[0],o[3]=r[1],o[6]=r[2],o[1]=e[0],o[4]=e[1],o[7]=e[2],o[2]=-n[0],o[5]=-n[1],o[8]=-n[2],B(t,y(t,o))},setAxisAngle:s,slerp:h,sqlerp:function(t,n,r,e,o,a){return h(O,n,o,a),h(j,r,e,a),h(t,O,j,2*a*(1-a)),t},sqrLen:v,squaredLength:S,str:function(t){return"quat("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"}},Symbol.toStringTag,{value:"Module"}))},94961:(t,n,r)=>{function e(){return[0,0,0,1]}function o(t){return[t[0],t[1],t[2],t[3]]}function a(t,n){return new Float64Array(t,n,4)}r.d(n,{I:()=>i,a:()=>e,b:()=>o,c:()=>a});const i=[0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:i,clone:o,create:e,createView:a,fromValues:function(t,n,r,e){return[t,n,r,e]}},Symbol.toStringTag,{value:"Module"}))},20773:(t,n,r)=>{r.d(n,{a:()=>a,b:()=>u,n:()=>c,s:()=>i,t:()=>o});var e=r(72220);function o(t,n,r){if(t.count!==n.count)return void e.c.error("source and destination buffers need to have the same number of elements");const o=t.count,a=r[0],i=r[1],c=r[2],u=r[4],s=r[5],f=r[6],l=r[8],h=r[9],p=r[10],y=r[12],d=r[13],M=r[14],m=t.typedBuffer,g=t.typedBufferStride,b=n.typedBuffer,T=n.typedBufferStride;for(let t=0;t<o;t++){const n=t*g,r=t*T,e=b[r],o=b[r+1],P=b[r+2];m[n]=a*e+u*o+l*P+y,m[n+1]=i*e+s*o+h*P+d,m[n+2]=c*e+f*o+p*P+M}}function a(t,n,r){if(t.count!==n.count)return void e.c.error("source and destination buffers need to have the same number of elements");const o=t.count,a=r[0],i=r[1],c=r[2],u=r[3],s=r[4],f=r[5],l=r[6],h=r[7],p=r[8],y=t.typedBuffer,d=t.typedBufferStride,M=n.typedBuffer,m=n.typedBufferStride;for(let t=0;t<o;t++){const n=t*d,r=t*m,e=M[r],o=M[r+1],g=M[r+2];y[n]=a*e+u*o+l*g,y[n+1]=i*e+s*o+h*g,y[n+2]=c*e+f*o+p*g}}function i(t,n,r){const e=Math.min(t.count,n.count),o=t.typedBuffer,a=t.typedBufferStride,i=n.typedBuffer,c=n.typedBufferStride;for(let t=0;t<e;t++){const n=t*a,e=t*c;o[n]=r*i[e],o[n+1]=r*i[e+1],o[n+2]=r*i[e+2]}}function c(t,n){const r=Math.min(t.count,n.count),e=t.typedBuffer,o=t.typedBufferStride,a=n.typedBuffer,i=n.typedBufferStride;for(let t=0;t<r;t++){const n=t*o,r=t*i,c=a[r],u=a[r+1],s=a[r+2],f=c*c+u*u+s*s;if(f>0){const t=1/Math.sqrt(f);e[n]=t*c,e[n+1]=t*u,e[n+2]=t*s}}}function u(t,n,r){const e=Math.min(t.count,n.count),o=t.typedBuffer,a=t.typedBufferStride,i=n.typedBuffer,c=n.typedBufferStride;for(let t=0;t<e;t++){const n=t*a,e=t*c;o[n]=i[e]>>r,o[n+1]=i[e+1]>>r,o[n+2]=i[e+2]>>r}}Object.freeze(Object.defineProperty({__proto__:null,normalize:c,scale:i,shiftRight:u,transformMat3:a,transformMat4:o},Symbol.toStringTag,{value:"Module"}))},56067:(t,n,r)=>{function e(t,n,r){const e=t.typedBuffer,o=t.typedBufferStride,a=n.typedBuffer,i=n.typedBufferStride,c=r?r.count:n.count;let u=(r&&r.dstIndex?r.dstIndex:0)*o,s=(r&&r.srcIndex?r.srcIndex:0)*i;for(let t=0;t<c;++t)e[u]=a[s],e[u+1]=a[s+1],e[u+2]=a[s+2],u+=o,s+=i}function o(t,n,r,e,o){const a=t.typedBuffer,i=t.typedBufferStride,c=o?.count??t.count;let u=(o?.dstIndex??0)*i;for(let t=0;t<c;++t)a[u]=n,a[u+1]=r,a[u+2]=e,u+=i}r.d(n,{c:()=>e,f:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,copy:e,fill:o},Symbol.toStringTag,{value:"Module"}))},58995:(t,n,r)=>{r.d(n,{rS:()=>s});var e=r(2109),o=r(82971),a=r(8744);const i=new o.Z(e.kU),c=new o.Z(e.JL),u=new o.Z(e.mM);function s(t){return t&&((0,a.BZ)(t)||(0,a.fS)(t,c))?c:t&&((0,a.V2)(t)||(0,a.fS)(t,u))?u:i}new o.Z(e.pn)},2674:(t,n,r)=>{r.d(n,{Z:()=>F});var e,o=r(43697),a=r(96674),i=r(70586),c=r(5600),u=(r(75215),r(67676),r(52011)),s=r(52138),f=r(13598),l=r(51305),h=r(94961),p=r(17896),y=r(65617),d=r(94139),M=r(44547),m=r(58995),g=r(3709),b=r(56481),T=r(20773),P=r(56067);let A=e=class extends a.wq{constructor(t){super(t),this.origin=(0,y.c)(),this.translation=(0,y.c)(),this.rotation=(0,g.Ue)(),this.scale=(0,y.f)(1,1,1),this.geographic=!0}get localMatrix(){const t=(0,f.c)();return(0,l.s)(_,(0,g.ZZ)(this.rotation),(0,g.WH)(this.rotation)),(0,s.g)(t,_,this.translation,this.scale),t}get localMatrixInverse(){return(0,s.a)((0,f.c)(),this.localMatrix)}applyLocal(t,n){return(0,p.m)(n,t,this.localMatrix)}applyLocalInverse(t,n){return(0,p.m)(n,t,this.localMatrixInverse)}project(t,n){const r=new Float64Array(t.length),e=b.fP.fromTypedArray(r),o=b.fP.fromTypedArray(t);if(this.geographic){const t=(0,m.rS)(n),a=(0,f.c)();return(0,M.Bm)(n,this.origin,a,t),(0,s.m)(a,a,this.localMatrix),(0,T.t)(e,o,a),(0,M.CM)(r,t,0,r,n,0,r.length/3),r}const{localMatrix:a,origin:i}=this;(0,s.h)(a,f.I)?(0,P.c)(e,o):(0,T.t)(e,o,a);for(let t=0;t<r.length;t+=3)r[t+0]+=i[0],r[t+1]+=i[1],r[t+2]+=i[2];return r}getOriginPoint(t){const[n,r,e]=this.origin;return new d.Z({x:n,y:r,z:e,spatialReference:t})}equals(t){return(0,i.pC)(t)&&this.geographic===t.geographic&&(0,p.k)(this.origin,t.origin)&&(0,s.j)(this.localMatrix,t.localMatrix)}clone(){const t={origin:(0,y.a)(this.origin),translation:(0,y.a)(this.translation),rotation:(0,g.Ue)(this.rotation),scale:(0,y.a)(this.scale),geographic:this.geographic};return new e(t)}};(0,o._)([(0,c.Cb)({type:[Number],nonNullable:!0,json:{write:!0}})],A.prototype,"origin",void 0),(0,o._)([(0,c.Cb)({type:[Number],nonNullable:!0,json:{write:!0}})],A.prototype,"translation",void 0),(0,o._)([(0,c.Cb)({type:[Number],nonNullable:!0,json:{write:!0}})],A.prototype,"rotation",void 0),(0,o._)([(0,c.Cb)({type:[Number],nonNullable:!0,json:{write:!0}})],A.prototype,"scale",void 0),(0,o._)([(0,c.Cb)({type:Boolean,nonNullable:!0,json:{write:!0}})],A.prototype,"geographic",void 0),(0,o._)([(0,c.Cb)()],A.prototype,"localMatrix",null),(0,o._)([(0,c.Cb)()],A.prototype,"localMatrixInverse",null),A=e=(0,o._)([(0,u.j)("esri.geometry.support.MeshTransform")],A);const _=(0,h.a)(),F=A},3709:(t,n,r)=>{r.d(n,{Ue:()=>c,WH:()=>l,ZZ:()=>f,qC:()=>s,uT:()=>u});var e=r(22021),o=r(51305),a=r(94961),i=r(17896);function c(t=h){return[t[0],t[1],t[2],t[3]]}function u(t,n,r=c()){return(0,i.c)(r,t),r[3]=n,r}function s(t,n,r=c()){return(0,o.s)(p,t,l(t)),(0,o.s)(y,n,l(n)),(0,o.m)(p,y,p),function(t,n){return t[3]=n,t}(r,(0,e.BV)((0,o.g)(r,p)))}function f(t){return t}function l(t){return(0,e.Vl)(t[3])}const h=[0,0,1,0],p=(0,a.a)(),y=(0,a.a)();c()},72220:(t,n,r)=>{r.d(n,{c:()=>e});const e=r(92604).Z.getLogger("esri.views.3d.support.buffer.math")},13442:(t,n,r)=>{function e(t,n){return t.isGeographic||t.isWebMercator&&(n?.geographic??!0)}r.d(n,{h:()=>e})},66459:(t,n,r)=>{r.d(n,{FF:()=>b,I5:()=>m,Yq:()=>T,iv:()=>M,w1:()=>g});var e=r(70586),o=r(67900),a=r(46521),i=r(52138),c=r(13598),u=r(21787),s=r(44547),f=r(58995),l=r(2674),h=r(56481),p=r(20773),y=r(13442),d=r(56493);function M(t,n,r){return(0,y.h)(n.spatialReference,r)?function(t,n,r){const e=n.spatialReference,o=F(n,r,w),a=new Float64Array(t.position.length),i=function(t,n,r,e){(0,p.t)(h.fP.fromTypedArray(e),h.fP.fromTypedArray(t),n);const o=new Float64Array(t.length);return(0,d.To)(e,o,r)}(t.position,o,e,a),c=(0,u.b)(x,o);return{position:i,normal:P(i,a,t.normal,c,e),tangent:A(i,a,t.tangent,c,e)}}(t,n,r):function(t,n,r){const e=new Float64Array(t.position.length),o=t.position,a=n.x,i=n.y,c=n.z||0,{horizontal:u,vertical:s}=B(r?r.unit:null,n.spatialReference);for(let t=0;t<o.length;t+=3)e[t+0]=o[t+0]*u+a,e[t+1]=o[t+1]*u+i,e[t+2]=o[t+2]*s+c;return{position:e,normal:t.normal,tangent:t.tangent}}(t,n,r)}function m(t,n,r){const{position:o,normal:a,tangent:i}=t;if((0,e.Wi)(n))return{position:o,normal:a,tangent:i};const c=n.localMatrix;return M({position:(0,d.zZ)(o,new Float64Array(o.length),c),normal:(0,e.pC)(a)?(0,d.w9)(a,new Float32Array(a.length),c):null,tangent:(0,e.pC)(i)?(0,d.VS)(i,new Float32Array(i.length),c):null},n.getOriginPoint(r),{geographic:n.geographic})}function g(t,n,r){if(r?.useTransform){const{position:e,normal:o,tangent:a}=t;return{vertexAttributes:{position:e,normal:o,tangent:a},transform:new l.Z({origin:[n.x,n.y,n.z??0],geographic:(0,y.h)(n.spatialReference,r)})}}return{vertexAttributes:M(t,n,r),transform:null}}function b(t,n,r){return(0,y.h)(n.spatialReference,r)?function(t,n,r){const e=n.spatialReference;F(n,r,w);const o=(0,i.a)(E,w),a=new Float64Array(t.position.length),c=function(t,n,r,e){const o=(0,d.XO)(t,n,e),a=h.fP.fromTypedArray(o),i=new Float64Array(o.length),c=h.fP.fromTypedArray(i);return(0,p.t)(c,a,r),i}(t.position,e,o,a),s=(0,u.b)(x,o);return{position:c,normal:S(t.normal,t.position,a,e,s),tangent:v(t.tangent,t.position,a,e,s)}}(t,n,r):_(t,n,r)}function T(t,n,r,o){if((0,e.Wi)(n))return b(t,r,o);const a=m(t,n,r.spatialReference);return r.equals(n.getOriginPoint(r.spatialReference))?_(a,r,o):b(a,r,o)}function P(t,n,r,o,a){if((0,e.Wi)(r))return null;const i=new Float32Array(r.length);return(0,p.a)(h.ct.fromTypedArray(i),h.ct.fromTypedArray(r),o),(0,d.Yk)(i,t,n,a,i),i}function A(t,n,r,o,a){if((0,e.Wi)(r))return null;const i=new Float32Array(r.length);(0,p.a)(h.ct.fromTypedArray(i,4*Float32Array.BYTES_PER_ELEMENT),h.ct.fromTypedArray(r,4*Float32Array.BYTES_PER_ELEMENT),o);for(let t=3;t<i.length;t+=4)i[t]=r[t];return(0,d.M2)(i,t,n,a,i),i}function _(t,n,r){const e=new Float64Array(t.position.length),o=t.position,a=n.x,i=n.y,c=n.z||0,{horizontal:u,vertical:s}=B(r?r.unit:null,n.spatialReference);for(let t=0;t<o.length;t+=3)e[t+0]=(o[t+0]-a)/u,e[t+1]=(o[t+1]-i)/u,e[t+2]=(o[t+2]-c)/s;return{position:e,normal:t.normal,tangent:t.tangent}}function F(t,n,r){(0,s.Bm)(t.spatialReference,[t.x,t.y,t.z||0],r,(0,f.rS)(t.spatialReference));const{horizontal:e,vertical:o}=B(n?n.unit:null,t.spatialReference);return(0,i.k)(r,r,[e,e,o]),r}function S(t,n,r,o,a){if((0,e.Wi)(t))return null;const i=(0,d.Iz)(t,n,r,o,new Float32Array(t.length)),c=h.ct.fromTypedArray(i);return(0,p.a)(c,c,a),i}function v(t,n,r,o,a){if((0,e.Wi)(t))return null;const i=(0,d.wi)(t,n,r,o,new Float32Array(t.length)),c=h.ct.fromTypedArray(i,4*Float32Array.BYTES_PER_ELEMENT);return(0,p.a)(c,c,a),i}function B(t,n){if((0,e.Wi)(t))return C;const r=n.isGeographic?1:(0,o.c9)(n),a=n.isGeographic?1:(0,o._R)(n),i=(0,o.En)(1,t,"meters");return{horizontal:i*r,vertical:i*a}}const w=(0,c.c)(),E=(0,c.c)(),x=(0,a.c)(),C={horizontal:1,vertical:1}},56493:(t,n,r)=>{r.d(n,{Iz:()=>m,M2:()=>S,To:()=>T,VS:()=>_,XO:()=>b,Yk:()=>g,w9:()=>A,wi:()=>F,zZ:()=>P});var e=r(92604),o=r(70586),a=r(21787),i=r(46521),c=r(13598),u=r(17896),s=r(65617),f=r(44547),l=r(58995),h=r(8744),p=r(40488),y=r(56481),d=r(20773);const M=e.Z.getLogger("esri.geometry.support.meshUtils.normalProjection");function m(t,n,r,e,o){return B(e)?(v(E.TO_PCPF,y.ct.fromTypedArray(t),y.fP.fromTypedArray(n),y.fP.fromTypedArray(r),e,y.ct.fromTypedArray(o)),o):(M.error("Cannot convert spatial reference to PCPF"),o)}function g(t,n,r,e,o){return B(e)?(v(E.FROM_PCPF,y.ct.fromTypedArray(t),y.fP.fromTypedArray(n),y.fP.fromTypedArray(r),e,y.ct.fromTypedArray(o)),o):(M.error("Cannot convert to spatial reference from PCPF"),o)}function b(t,n,r){return(0,f.CM)(t,n,0,r,(0,l.rS)(n),0,t.length/3),r}function T(t,n,r){return(0,f.CM)(t,(0,l.rS)(r),0,n,r,0,t.length/3),n}function P(t,n,r){if((0,o.Wi)(t))return n;const e=y.fP.fromTypedArray(t),a=y.fP.fromTypedArray(n);return(0,d.t)(a,e,r),n}function A(t,n,r){if((0,o.Wi)(t))return n;(0,a.b)(j,r);const e=y.ct.fromTypedArray(t),i=y.ct.fromTypedArray(n);return(0,d.a)(i,e,j),(0,a.i)(j)||(0,d.n)(i,i),n}function _(t,n,r){if((0,o.Wi)(t))return n;(0,a.b)(j,r);const e=y.ct.fromTypedArray(t,4*Float32Array.BYTES_PER_ELEMENT),i=y.ct.fromTypedArray(n,4*Float32Array.BYTES_PER_ELEMENT);if((0,d.a)(i,e,j),(0,a.i)(j)||(0,d.n)(i,i),t!==n)for(let r=3;r<t.length;r+=4)n[r]=t[r];return n}function F(t,n,r,e,o){if(!B(e))return M.error("Cannot convert spatial reference to PCPF"),o;v(E.TO_PCPF,y.ct.fromTypedArray(t,4*Float32Array.BYTES_PER_ELEMENT),y.fP.fromTypedArray(n),y.fP.fromTypedArray(r),e,y.ct.fromTypedArray(o,4*Float32Array.BYTES_PER_ELEMENT));for(let n=3;n<t.length;n+=4)o[n]=t[n];return o}function S(t,n,r,e,o){if(!B(e))return M.error("Cannot convert to spatial reference from PCPF"),o;v(E.FROM_PCPF,y.ct.fromTypedArray(t,16),y.fP.fromTypedArray(n),y.fP.fromTypedArray(r),e,y.ct.fromTypedArray(o,16));for(let n=3;n<t.length;n+=4)o[n]=t[n];return o}function v(t,n,r,e,o,i){if(!n)return;const c=r.count,s=(0,l.rS)(o);if(w(o))for(let r=0;r<c;r++)e.getVec(r,C),n.getVec(r,R),(0,f.Bm)(s,C,O,s),(0,a.f)(j,O),t===E.FROM_PCPF&&(0,a.t)(j,j),(0,u.t)(R,R,j),i.setVec(r,R);else for(let o=0;o<c;o++){e.getVec(o,C),n.getVec(o,R),(0,f.Bm)(s,C,O,s),(0,a.f)(j,O);const c=(0,p.mZ)(r.get(o,1));let l=Math.cos(c);t===E.TO_PCPF&&(l=1/l),j[0]*=l,j[1]*=l,j[2]*=l,j[3]*=l,j[4]*=l,j[5]*=l,t===E.FROM_PCPF&&(0,a.t)(j,j),(0,u.t)(R,R,j),(0,u.n)(R,R),i.setVec(o,R)}return i}function B(t){return w(t)||function(t){return t.isWebMercator}(t)}function w(t){return t.isWGS84||(0,h.yW)(t)||(0,h.BZ)(t)||(0,h.V2)(t)}var E,x;(x=E||(E={}))[x.TO_PCPF=0]="TO_PCPF",x[x.FROM_PCPF=1]="FROM_PCPF";const C=(0,s.c)(),R=(0,s.c)(),O=(0,c.c)(),j=(0,i.c)()}}]);