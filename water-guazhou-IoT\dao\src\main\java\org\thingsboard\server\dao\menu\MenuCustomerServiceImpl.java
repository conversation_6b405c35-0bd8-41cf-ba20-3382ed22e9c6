/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.menu.*;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.role.MenuRoleDao;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class MenuCustomerServiceImpl implements MenuCustomerService {

    @Autowired
    private MenuCustomerDao menuCustomerDao;
    @Autowired
    private MenuPoolDao menuPoolDao;
    @Autowired
    private MenuTenantDao menuTenantDao;
    @Autowired
    private MenuRoleDao menuRoleDao;

    @Override
    public List<MenuCustomer> saveMenuCustomer(List<MenuTenant> menuTenantList, TenantId tenantId) {
        log.trace("Executing saveMenuCustomer menuTenantList = [{}], tenantId = [{}]", menuTenantList, tenantId);
        // 删除原有菜单
        menuCustomerDao.deleteByTenantId(tenantId);

        List<MenuCustomer> result = new ArrayList<>();
        // 先保存一级菜单
        Map<String, UUID> parentMapper = new HashMap<>();
        for (MenuTenant menuTenant : menuTenantList) {
            MenuPool menuPool = menuTenantDao.findById(menuTenant.getId());
            // 判断是否为一级菜单
            if (menuPool.getParentId().isRootUid()) {
                // 为一级菜单
                MenuCustomer menuCustomer = menuPoolToMenuCustomer(menuPool);
                menuCustomer.setMenuTenantId(menuTenant.getId());
                menuCustomer.setParentId(new MenuCustomerId(ModelConstants.MENU_CUSTOMER_ROOT));

                MenuCustomer save = menuCustomerDao.save(menuCustomer);
                parentMapper.put(menuPool.getId().getId().toString(), save.getId().getId());
                result.add(save);
            }
        }

        // 保存子菜单
        for (MenuTenant menuTenant : menuTenantList) {
            if (parentMapper.containsKey(menuTenant.getMenuPoolId().getId().toString())) {
                continue;
            }
            MenuPool menuPool = menuTenantDao.findById(menuTenant.getId());
            MenuCustomer menuCustomer = menuPoolToMenuCustomer(menuPool);
            menuCustomer.setMenuTenantId(menuTenant.getId());
            UUID parentId = parentMapper.get(menuPool.getParentId().getId().toString());
            if (parentId == null) {
                // 查询该二级菜单的父菜单是否已经存在，若存在设置其父菜单的ID为数据库中已存在的ID
                MenuTenant byTenantIdAndMenuPoolId = menuTenantDao.findByTenantIdAndMenuPoolId(tenantId, menuPool.getParentId());
                MenuCustomer byMenuTenantId = menuCustomerDao.findByMenuTenantId(byTenantIdAndMenuPoolId.getId(), tenantId);
                parentId = byMenuTenantId.getId().getId();
            }
            menuCustomer.setParentId(new MenuCustomerId(parentId));
            result.add(menuCustomerDao.save(menuCustomer));
        }
        return result;
    }

    @Override
    public MenuCustomer saveMenuCustomer(Menu menu, TenantId tenantId) {
        MenuCustomer menuCustomer = menuToMenuCustomer(menu);
        // 获取该菜单类型的menu_tenantId_id
        String menuTenantId = menuTenantDao.getIdByType(menu.getType(), tenantId);
        if (menuTenantId == null) {
            return null;
        }
        List<MenuPool> byType = menuPoolDao.findByType(menu.getType());
        if (byType == null && byType.isEmpty()) {
            return null;
        }
        menuCustomer.setOrderNum(byType.get(0).getOrderNum());
        menuCustomer.setMenuTenantId(new MenuTenantId(UUIDConverter.fromString(menuTenantId)));
        if (menuCustomer.getAdditionalInfo() == null || menuCustomer.getAdditionalInfo().trim().equals("")) {
            // 获取该菜单类型对应的path相关数据
            String additionalInfo = menuTenantDao.getAdditionalInfoByType(menu.getType());
            menuCustomer.setAdditionalInfo(additionalInfo);
        }
        if (menuCustomer.getId() == null) {
            log.trace("Executing saveMenuCustomer menu = [{}], tenantId = [{}]", menu, tenantId);
        } else {
            log.trace("Executing updateMenuCustomer menu = [{}], tenantId = [{}]", menu, tenantId);
        }
        return menuCustomerDao.save(menuCustomer);
    }

    @Override
    public List<Menu> findByTenantId(TenantId tenantId) {
        log.trace("Executing findByTenantId [{}]", tenantId);
        List<Menu> result = new ArrayList<>();
        try {
            MenuCustomerId root = new MenuCustomerId(ModelConstants.MENU_CUSTOMER_ROOT);
            // 获取一级菜单
            List<MenuCustomer> parentMenuCustomerList = menuCustomerDao.findByTenantId(tenantId, root);
            for (MenuCustomer parent : parentMenuCustomerList) {
                Menu menu = menuCustomerToMenu(parent);
                Integer type = menuCustomerDao.getTypeByMenuTenantId(parent.getMenuTenantId());
                menu.setType(type);
                // 获取该一级菜单下的二级菜单
                List<MenuCustomer> children = menuCustomerDao.findByTenantId(tenantId, parent.getId());
                if (children != null && children.size() > 0) {
                    menu.setChildren(menuCustomerListToMenuList(children));
                }

                result.add(menu);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("System error!");
        }
    }

    @Override
    public List<Menu> findCustomerMenuByTenantId(UserId userId, TenantId tenantId) {
        log.trace("Executing findCustomerMenuByTenantId userId [{}], tenantId [{}]", userId, tenantId);
        List<Menu> result = new ArrayList<>();
        try {
            List<String> menuIdList = menuRoleDao.findByUserId(userId);
            List<MenuCustomer> menuCustomerList = menuCustomerDao.findByMenuTenantIdIn(menuIdList);
            MenuCustomerId rootId = new MenuCustomerId(ModelConstants.MENU_CUSTOMER_ROOT);
            // 筛选出所有的一级菜单
            List<MenuCustomer> parentList = menuCustomerList.stream()
                    .filter(menuCustomer -> menuCustomer.getParentId().equals(rootId))
                    .collect(Collectors.toList());
            // 筛选出所有的二级菜单
            List<MenuCustomer> childrenList = menuCustomerList.stream()
                    .filter(menuCustomer -> !menuCustomer.getParentId().equals(rootId))
                    .collect(Collectors.toList());
            // 检查是否所有二级菜单的一级菜单都已经查询到
            List<String> collect = parentList.stream().map(menuCustomer -> menuCustomer.getId().getId().toString()).collect(Collectors.toList());
            childrenList.forEach(menuCustomer -> {
                if (!collect.contains(menuCustomer.getParentId().getId().toString())) {// 当前二级菜单没有一级菜单对应
                    // 查询其一级菜单
                    MenuCustomer parent = menuCustomerDao.findById(menuCustomer.getParentId().getId());
                    collect.add(parent.getId().getId().toString());
                    parentList.add(parent);
                }
            });

            // 遍历筛选出每个一级菜单所包含的二级菜单
            for (MenuCustomer parent : parentList) {
                Menu menu = menuCustomerToMenu(parent);
                List<MenuCustomer> children = childrenList.stream()
                        .filter(menuCustomer -> menuCustomer.getParentId().equals(parent.getId()))
                        .collect(Collectors.toList());
                List<Menu> menus = menuCustomerListToMenuList(children);
                if (menus != null && menus.size() > 0) {
                    menu.setChildren(menus);
                }
                result.add(menu);
            }

            return result.stream().sorted((o1, o2) -> o2.getOrderNum().compareTo(o1.getOrderNum())).collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("System error!");
        }
    }

    @Override
    public void deleteMenu(MenuCustomerId id, TenantId tenantId) throws ThingsboardException {
        // 查询
        MenuCustomer menuCustomer = menuCustomerDao.findById(id.getId());
        if (menuCustomer == null) {
            throw new ThingsboardException("This menu does not exist!",
                    ThingsboardErrorCode.ITEM_NOT_FOUND);
        }
        menuCustomerDao.removeById(id.getId());
    }

    @Override
    public List<MenuPoolVO> getTree(TenantId tenantId) {
        log.trace("Executing getTree [{}]", tenantId);
        List<MenuPoolVO> tree = new ArrayList<>();
        // 获取一级菜单ID
        MenuCustomerId root = new MenuCustomerId(ModelConstants.MENU_CUSTOMER_ROOT);
        try {
            // 返回结果集
            List<MenuPoolVO> cMenuVO;
            // 获取该租户的菜单
            List<MenuCustomer> menuList = menuCustomerDao.findByTenantId(tenantId);
            // 筛选出所有的一级菜单
            List<MenuCustomer> pMenuList = menuList.stream()
                    .filter(menuCustomer -> menuCustomer.getParentId().equals(root))
                    .collect(Collectors.toList());
            // 筛选出所有的二级菜单
            List<MenuCustomer> cMenuList = menuList.stream()
                    .filter(menuCustomer -> !menuCustomer.getParentId().equals(root))
                    .collect(Collectors.toList());
            // 遍历一级菜单，设置其二级菜单
            for (MenuCustomer p : pMenuList) {
                // 转换对象
                MenuPoolVO vo = MenuPoolVO.toMenuPoolVO(p);
                // 设置菜单type
                vo.setType(menuCustomerDao.getTypeByMenuTenantId(p.getMenuTenantId()));
                //  筛选该父菜单的子菜单
                List<MenuCustomer> child = cMenuList.stream()
                        .filter(menuCustomer -> menuCustomer.getParentId().equals(p.getId()))
                        .collect(Collectors.toList());
                if (child.size() > 0) {
                    // 遍历设置其type,并添加到集合中
                    cMenuVO = new ArrayList<>();
                    for (MenuCustomer c : child) {
                        MenuPoolVO vo1 = MenuPoolVO.toMenuPoolVO(c);
                        vo1.setType(menuCustomerDao.getTypeByMenuTenantId(c.getMenuTenantId()));
                        cMenuVO.add(vo1);
                    }
                    // 设置父菜单的子菜单集合
                    vo.setChildren(cMenuVO);
                }
                // 添加到结果集
                tree.add(vo);
            }

            return tree;
        } catch (Exception e) {
            throw new RuntimeException("System error!");
        }
    }

    @Override
    public boolean checkType(Integer type, TenantId tenantId) {
        List<Integer> types = menuCustomerDao.getTypesByTenantId(tenantId);
        return types.contains(type);
    }

    @Override
    public List<MenuTypeVO> getTypes(TenantId tenantId) {
        return menuCustomerDao.getTypes(tenantId);
    }


    private List<MenuPoolVO> menuCustomerListToMenuPoolVOList(List<MenuCustomer> childrenTree) {
        List<MenuPoolVO> result = new ArrayList<>();
        for (MenuCustomer menuCustomer : childrenTree) {
            MenuPoolVO vo = MenuPoolVO.toMenuPoolVO(menuCustomer);
            result.add(vo);
        }
        return result;
    }


}
