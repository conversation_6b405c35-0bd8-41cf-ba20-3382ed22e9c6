package org.thingsboard.server.dao.app;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.AppRole;
import org.thingsboard.server.dao.sql.app.AppRoleRepository;

import java.util.List;

@Slf4j
@Service
@Transactional
public class AppRoleServiceImpl implements AppRoleService {

    @Autowired
    private AppRoleRepository appRoleRepository;

    @Override
    public List<AppRole> findByAppTypeId(String appTypeId) {
        return appRoleRepository.findByAppTypeIdOrderByCreateTime(appTypeId);
    }

    @Override
    public List<AppRole> findAll() {
        Sort sortByCreateTime = new Sort(new Sort.Order(Sort.Direction.ASC, ModelConstants.APP_ROLE_CREATE_TIME));
        return appRoleRepository.findAll(sortByCreateTime);
    }

    @Override
    public AppRole save(AppRole appRole) {
        appRole.setCreateTime(System.currentTimeMillis());
        return appRoleRepository.save(appRole);
    }

    @Override
    public AppRole update(AppRole appRole) {
        if (appRole == null) {
            return null;
        }
        AppRole _appRole = appRoleRepository.findOne(appRole.getId());
        if (_appRole != null) {
            _appRole.setName(appRole.getName());
            appRoleRepository.save(_appRole);
        }
        return _appRole;
    }

    @Override
    public AppRole findById(String id) {
        return appRoleRepository.findOne(id);
    }

    @Override
    public AppRole deleteById(String id) {
        AppRole _appRole = appRoleRepository.findOne(id);
        if (_appRole != null) {
            appRoleRepository.delete(id);
        }
        return _appRole;
    }
}
