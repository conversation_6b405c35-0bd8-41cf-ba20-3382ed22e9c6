/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.optionLog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.data.domain.PageRequest;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.base.OptionLogList;
import org.thingsboard.server.dao.util.imodel.query.base.OptionLogListPageRequest;

import java.util.List;
import java.util.Map;

public interface OptionLogService {

    OptionLog save(OptionLog optionLog);

    List<OptionLog> getLogList(TenantId tenantId, String keyword, String ip, String options, Long startTime, Long endTime, String authority);

    Map<String, Object> getOptionLogList(TenantId tenantId, Long startTime, Long endTime, String authority, String ip, String keyword, PageRequest pageRequest);

    Object getUserLoginInfo(UserId id);

    int examine(String id, String examineId, String examineName, String examineTenantId, Long examineTime);

    PageData<OptionLog> getLogListByPage(TenantId tenantId, String keyword, String ip, String options, Long startTime, Long endTime, String name, int page, int size);

    IPage<OptionLogList> selectLogList(OptionLogListPageRequest optionLogListPageRequest);

    IPage<OptionLogList> selectOperationLogList(OptionLogListPageRequest optionLogListPageRequest);
}
