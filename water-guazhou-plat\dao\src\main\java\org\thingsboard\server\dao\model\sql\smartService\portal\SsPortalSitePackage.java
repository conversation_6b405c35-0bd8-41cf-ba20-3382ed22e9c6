package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_site_package")
public class SsPortalSitePackage implements Identifiable {
    // id
    private String id;

    // 名称
    private String name;

    // 是否为公告
    private Boolean isNotice;

    // 是否显示在底部
    private Boolean atBottom;

    // 是否展示
    private Boolean active;

    // 展示模式 SINGLE/LIST
    private SsPortalSiteDisplayMode displayMode;

    // 是否跳转URL链接
    private Boolean jumpToUrl;

    // 链接地址
    private String link;

    // 父级id
    private String parentId;

    // 父级名称
    private String parentName;

    // 排序
    private Integer orderNum;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
