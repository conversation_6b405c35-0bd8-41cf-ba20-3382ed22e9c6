import{_ as g}from"./CardTable-rdWOL4_6.js";import{_}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as d,c as f,r as c,b as m,o as h,g as b,n as y,q as p}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function C(s){return u({url:"/api/constants/list",method:"get",params:s})}const S={class:"wrapper"},k={__name:"constantsAttribute",setup(s){const i=f(),l=c({labelWidth:"100px",filters:[{type:"input",label:"类型",field:"type",placeholder:"请输入类型",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()}]}],defaultParams:{}}),a=c({columns:[{label:"类型",prop:"type"},{label:"键",prop:"key"},{label:"值",prop:"value"}],dataList:[],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{a.pagination.page=e,n()},handleSize:e=>{a.pagination.limit=e,n()}}}),n=async()=>{var e,r;try{const o=await C({page:a.pagination.page,size:a.pagination.limit,...((e=i.value)==null?void 0:e.queryParams)||{}}),t=((r=o.data)==null?void 0:r.data)||o;a.dataList=t.records||t,a.pagination.total=t.total||t.length||0,t.records&&t.records.length>0&&(a.pagination.page=t.current||1)}catch{m.error("数据加载失败")}};return h(()=>{n()}),(e,r)=>{const o=_,t=g;return b(),y("div",S,[p(o,{ref_key:"refSearch",ref:i,config:l},null,8,["config"]),p(t,{class:"card-table",config:a},null,8,["config"])])}}},A=d(k,[["__scopeId","data-v-cefcb371"]]);export{A as default};
