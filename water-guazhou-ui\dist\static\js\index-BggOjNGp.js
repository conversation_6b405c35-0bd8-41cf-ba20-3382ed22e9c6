import{m as t}from"./index-r0dFAfgr.js";function i(e){return t({url:`/api/device/${e}`,method:"get"})}function n(e){return t({url:`/api/device/${e}?needUpdateGateway=true`,method:"delete"})}function c(e){return t({url:`/api/tenant/devicesAll?key=${e}`,method:"get"})}function d(e,a){return t({url:`/api/gateway/devices/page/${e}`,method:"get",params:a})}function l(e,a){return t({url:`/api/copy/gateway/${e}/${a}`,method:"POST"})}function p(e){return t({url:`/api/delete/gateway/${e}`,method:"DELETE"})}function u(e){return t({url:`/api/deviceTemplate/protocol/group/${e}`,method:"GET"})}function s(e){return t({url:"/api/deviceTemplate",method:"post",data:e})}function m(e){return t({url:`/api/deviceTemplate/protocol/${e}`,method:"get"})}function v(e){return t({url:`/api/deviceTemplate/list/${e}`,method:"get"})}function g(e){return t({url:`/api/deviceTemplate/${e}`,method:"delete"})}function h(e){return t({url:"/api/deviceTemplate/save/deviceTemplateAndProtocol",method:"post",data:e})}function T(e){return t({url:`/api/deviceTemplate/copy/${e}`,method:"post"})}function f(e){return t({url:`/api/deviceTemplate/export?id=${e}`,method:"get"})}function y(e,a,o){return t({url:`/api/tenant/deviceSearchTree/${e}/${a}`,method:"get",params:o})}function $(e){return t({url:`/api/project/devices/${e}`,method:"get"})}function D(e,a){return t({url:`/istar/api/device/fullData/page/${e}`,method:"get",params:a})}function w(e){return t({url:"/api/device",method:"post",data:e})}const G=e=>t({url:`/api/deviceTemplate/protocol/list/${e}`,method:"get"}),L=e=>t({url:"/api/device?needUpdateGateway=true",method:"post",data:e}),P=e=>t({url:"//api/device/modelInfo/list",method:"get",params:e});export{$ as a,c as b,y as c,p as d,l as e,v as f,u as g,P as h,m as i,f as j,T as k,g as l,s as m,w as n,L as o,d as p,n as q,i as r,h as s,G as t,D as u};
