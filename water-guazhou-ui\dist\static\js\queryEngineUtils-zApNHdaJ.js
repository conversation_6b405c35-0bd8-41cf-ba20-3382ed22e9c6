import{R as a}from"./index-r0dFAfgr.js";import{ag as d}from"./MapView-DaoQedLH.js";import{o as i,q as g,t as s}from"./AnimatedLinesLayer-B2VbV4jv.js";import{r as c}from"./VertexSnappingCandidate-CgwNICNk.js";function e({x:t,y:r,z:o}){return s(d(t,r,o??0))}function l(t,r){switch(t.type){case"edge":return t.draped?new i({edgeStart:e(t.start),edgeEnd:e(t.end),targetPoint:e(t.target),objectId:t.objectId,getGroundElevation:r}):new g({edgeStart:e(t.start),edgeEnd:e(t.end),targetPoint:e(t.target),objectId:t.objectId,isDraped:!1});case"vertex":return new c({targetPoint:e(t.target),objectId:t.objectId,isDraped:!1})}}function b(t){return a(t)&&t.type==="3d"?(r,o,n)=>t.elevationProvider.getElevation(r,o,n??0,t.spatialReference,"ground"):()=>null}export{l as i,b as p};
