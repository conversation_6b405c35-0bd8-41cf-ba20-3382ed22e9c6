import{m as W,d as st,r as ot,am as nt,o as rt,bA as lt,g as O,n as E,p as s,bh as S,q as _,F,G as R,h as L,i as A,j as Y,fo as j,H as it,J as ct,cu as ut,bF as B,C as dt}from"./index-r0dFAfgr.js";import{_ as pt}from"./index-C9hz-UZb.js";import{g as mt}from"./waterIndicators-BJSzKLY_.js";import{p as ht}from"./flowMonitoring-DtJlPj0G.js";import{d as ft}from"./useStation-DJgnSZIA.js";import"./zhandian-YaGuQZe6.js";const z=(n=24)=>{const x=[];for(let t=0;t<n;t++)x.push(`${t.toString().padStart(2,"0")}时`);return x},yt=(n=24)=>{const x=[];for(let t=0;t<n;t++){const b=Math.sin(t*Math.PI/12)*.3+Math.random()*.2-.1;x.push(Number((1.2+b).toFixed(2)))}return x},gt=n=>{const x=(n==null?void 0:n.timeData)||z(),t=(n==null?void 0:n.actualData)||[],M=(n==null?void 0:n.predictedData)||[];return{color:["#5470c6","#91cc75"],tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:b=>{let P=`${b[0].axisValue}<br/>`;return b.forEach(V=>{P+=`${V.seriesName}: ${V.value} m³/h<br/>`}),P}},legend:{data:["实际流量","预测流量"],top:10},grid:{left:60,right:60,top:50,bottom:40},xAxis:{type:"category",data:x,axisLabel:{rotate:45}},yAxis:{type:"value",name:"流量(m³/h)",axisLabel:{formatter:"{value}"}},series:[{name:"实际流量",type:"line",data:t,smooth:!0,connectNulls:!1,lineStyle:{width:2,color:"#ff7f0e"},itemStyle:{color:"#ff7f0e"},markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}},{name:"预测流量",type:"line",data:M,smooth:!0,connectNulls:!0,lineStyle:{width:2,type:"dashed",color:"#1f77b4"},itemStyle:{color:"#1f77b4",opacity:.8}}]}},vt=n=>{const x=(n==null?void 0:n.timeData)||z(),t=(n==null?void 0:n.outletPressure)||[],M=(n==null?void 0:n.predictedPressure)||[];return{color:["#ee6666","#73c0de"],tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:b=>{let P=`${b[0].axisValue}<br/>`;return b.forEach(V=>{P+=`${V.seriesName}: ${V.value} MPa<br/>`}),P}},legend:{data:["出厂压力","预测压力"],top:10},grid:{left:60,right:60,top:50,bottom:40},xAxis:{type:"category",data:x,axisLabel:{rotate:45}},yAxis:{type:"value",name:"压力(MPa)",axisLabel:{formatter:"{value}"}},series:[{name:"出厂压力",type:"line",data:t,smooth:!0,connectNulls:!1,lineStyle:{width:2,color:"#ff7f0e"},itemStyle:{color:"#ff7f0e"}},{name:"预测压力",type:"line",data:M,smooth:!0,connectNulls:!0,lineStyle:{width:2,type:"dashed",color:"#1f77b4"},itemStyle:{color:"#1f77b4"}}]}},U=n=>{const x=(n==null?void 0:n.timeData)||z(),t=(n==null?void 0:n.concentrationData)||yt();return{color:["#409eff"],tooltip:{trigger:"axis",formatter:M=>{let b=`时间: ${M[0].axisValue}<br/>`;return M.forEach(P=>{b+=`${P.seriesName}: ${P.value} mg/L<br/>`}),b}},legend:{data:["药物浓度预测"],top:10},grid:{left:60,right:60,top:50,bottom:40},xAxis:{type:"category",data:x,axisLabel:{rotate:45}},yAxis:{type:"value",name:"浓度(mg/L)",axisLabel:{formatter:"{value}"}},series:[{name:"药物浓度预测",type:"line",data:t,smooth:!0,lineStyle:{width:3,color:"#409eff"},itemStyle:{color:"#409eff"},areaStyle:{opacity:.3,color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.8)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}},markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}}]}};function wt(n){return W({url:"/istar/api/production/waterPlant/getCurveOperationData",method:"get",params:n})}function Dt(n){return W({url:"/istar/api/production/waterPlant/applyCurveOperationParams",method:"post",data:n})}const xt={class:"curve-operation-wrapper"},bt={class:"top-cards"},Ct={class:"data-card"},_t={class:"card-content"},Pt={class:"main-value"},Ft={class:"change-rate positive"},Mt={class:"data-card"},Tt={class:"card-content"},St={class:"main-value"},Vt={class:"change-rate negative"},Ot={class:"data-card"},At={class:"card-content"},It={class:"main-value"},$t={class:"change-rate positive"},kt={class:"data-card"},Et={class:"card-content"},Rt={class:"main-value"},Nt={class:"change-rate positive"},Lt={class:"main-content"},Yt={class:"left-panel"},jt={class:"param-card"},zt={class:"param-section"},Bt={class:"param-item"},Ut={class:"flow-inputs"},Wt={class:"flow-average"},qt={class:"param-item"},Gt={class:"action-buttons"},Jt={class:"right-panel"},Kt={class:"chart-container"},Qt={key:1,style:{padding:"20px","text-align":"center",color:"#999"}},Xt={class:"chart-controls"},Zt={class:"chart-container"},Ht={key:1,style:{padding:"20px","text-align":"center",color:"#999"}},te={class:"chart-container"},ee={key:1,style:{padding:"20px","text-align":"center",color:"#999"}},ae=st({__name:"index",setup(n){const{getAllStationOption:x}=ft(),t=ot({stationList:[],selectedStationId:"",realTimeData:{todayFlow:"0",flowChangeRate:"0%",outletPressure:"0",outletPressureChange:"0%",inletPressure:"0",inletPressureChange:"0%",pumpEfficiency:"0",pumpEfficiencyChange:"0%",predictionAccuracy:"0%",predictionAccuracyChange:"0%"},params:{timeRangeEnabled:!0,supplyFlow:2500,minFlow:"500",maxFlow:"2000",howParameter:"",dosage:"20",averageFlow:"1250"},chartType:"actual",pressureType:"outlet",flowChartOption:null,pressureChartOption:null,concentrationChartOption:null,loading:!1}),M=()=>{try{t.flowChartOption=null,t.pressureChartOption=null,t.concentrationChartOption=U()}catch{}},b=async()=>{try{const a=await x("水厂");t.stationList=a,a.length>0&&(t.selectedStationId=a[0].id)}catch{t.selectedStationId=""}},P=async()=>{var a;try{const e=await wt({stationId:t.selectedStationId});(a=e.data)!=null&&a.data&&Object.assign(t.realTimeData,e.data.data)}catch{}},V=a=>a>=6&&a<=9?500:a>=17&&a<=21?300:a>=22||a<=5?-400:0,q=(a,e,d)=>{if(a.length===0)return 3e3;const u=a.reduce((o,r)=>o+r.value,0)/a.length;let p=u;if(d){const o=a.find(r=>r.hour===e);if(o){p=o.value;const r=50+Math.sin(e*.4)*30;p+=r}}else if(a.length>=2){const o=a.sort((C,T)=>C.hour-T.hour),r=o[o.length-1],m=o[0],y=r.hour-m.hour,w=r.value-m.value,c=y>0?w/y:0,h=o.reduce((C,T)=>Math.abs(T.hour-e)<Math.abs(C.hour-e)?T:C),D=e-h.hour;p=h.value+c*D*.3}const g=V(e)*.3;p+=g;const i=Math.sin(e*.5)*150,f=Math.cos(e*.3)*100,l=(Math.random()-.5)*200,v=p+i+f+l;return Math.max(u*.7,Math.min(u*1.3,v))},G=a=>a>=6&&a<=9?.05:a>=17&&a<=21?.03:a>=22||a<=5?-.02:0,J=(a,e,d)=>{if(a.length===0)return .45;const u=a.reduce((o,r)=>o+r.value,0)/a.length;let p=u;if(d){const o=a.find(r=>r.hour===e);if(o){p=o.value;const r=.02+Math.sin(e*.3)*.01;p+=r}}else if(a.length>=2){const o=a.sort((C,T)=>C.hour-T.hour),r=o[o.length-1],m=o[0],y=r.hour-m.hour,w=r.value-m.value,c=y>0?w/y:0,h=o.reduce((C,T)=>Math.abs(T.hour-e)<Math.abs(C.hour-e)?T:C),D=e-h.hour;p=h.value+c*D*.5}const g=G(e)*.5;p+=g;const i=Math.sin(e*.4)*.03,f=Math.cos(e*.6)*.02,l=(Math.random()-.5)*.04,v=p+i+f+l;return Math.max(u*.8,Math.min(u*1.2,v))},K=async()=>{var a,e,d;try{t.loading=!0;const u=B().format("YYYY-MM-DD"),p=await mt({stationId:t.selectedStationId,queryType:"day",time:u,compareType:"none"});if((d=(e=(a=p.data)==null?void 0:a.data)==null?void 0:e.baseTable)!=null&&d.tableDataList){const g=p.data.data.baseTable.tableDataList,f=p.data.data.baseTable.tableInfo.find(l=>l.unit==="m3/h"&&l.columnValue!=="ts"&&l.columnValue!=="differenceRate"&&l.columnValue!=="changeRate");if(f){const l=new Map;g.forEach(c=>{const h=c.ts?`${c.ts}时`:"",D=c[f.columnValue];h&&D!==null&&D!==void 0&&l.set(h,D)});const v=Array.from({length:24},(c,h)=>`${h.toString().padStart(2,"0")}时`),o=[],r=[],m=[];l.forEach((c,h)=>{const D=parseInt(h.replace("时",""));m.push({time:h,value:c,hour:D})}),v.forEach((c,h)=>{if(l.has(c)){const C=l.get(c);o.push(C)}else o.push(null);const D=q(m,h,l.has(c));r.push(D)});const y=m[m.length-1];if(y&&(t.realTimeData.todayFlow=y.value.toLocaleString(),m.length>1)){const c=y.value,h=m[m.length-2].value,D=((c-h)/h*100).toFixed(1);t.realTimeData.flowChangeRate=`${parseFloat(D)>0?"+":""}${D}%`}const w={timeData:v,actualData:o,predictedData:r};t.flowChartOption=gt(w)}else t.flowChartOption=null}else t.flowChartOption=null}catch{t.flowChartOption=null}finally{t.loading=!1}},Q=async()=>{var a,e;try{if(!t.selectedStationId){t.pressureChartOption=null;return}const d=B().format("YYYY-MM-DD"),u={stationId:t.selectedStationId,queryType:"day",date:d},g=(e=(a=(await ht(u)).data)==null?void 0:a.data)==null?void 0:e.pressure;if(g&&g.length>0){const i=new Map,f=[];if(g.forEach((l,v)=>{let o="",r=null;if(l.ts!==void 0?(o=l.ts.toString().includes("时")?l.ts:`${l.ts}时`,r=l.value||l.pressure):l.time!==void 0?(o=`${l.time.split(":")[0]}时`,r=l.pressure||l.value):typeof l=="number"&&(o=`${v.toString().padStart(2,"0")}时`,r=l),o&&r!==null&&r!==void 0&&!isNaN(r)){i.set(o,r);const m=parseInt(o.replace("时",""));f.push({time:o,value:r,hour:m})}}),f.length>0){const l=Array.from({length:24},(y,w)=>`${w.toString().padStart(2,"0")}时`),v=[],o=[];l.forEach((y,w)=>{if(i.has(y)){const h=i.get(y);v.push(h)}else v.push(null);const c=J(f,w,i.has(y));o.push(c)});const r=f[f.length-1];if(t.realTimeData.outletPressure=r.value.toFixed(2),t.realTimeData.inletPressure=(r.value*.9).toFixed(2),f.length>1){const y=r.value,w=f[f.length-2].value,c=((y-w)/w*100).toFixed(1);t.realTimeData.outletPressureChange=`${parseFloat(c)>0?"+":""}${c}%`,t.realTimeData.inletPressureChange=`${parseFloat(c)>0?"+":""}${(parseFloat(c)*.8).toFixed(1)}%`}const m={timeData:l,outletPressure:v,predictedPressure:o};t.pressureChartOption=vt(m)}else t.pressureChartOption=null,t.realTimeData.outletPressure="0.00",t.realTimeData.inletPressure="0.00",t.realTimeData.outletPressureChange="0.0%",t.realTimeData.inletPressureChange="0.0%"}else t.pressureChartOption=null,t.realTimeData.outletPressure="0.00",t.realTimeData.inletPressure="0.00",t.realTimeData.outletPressureChange="0.0%",t.realTimeData.inletPressureChange="0.0%"}catch{t.pressureChartOption=null}},I=()=>{const a=Z();t.concentrationChartOption=U(a)},X=()=>{const a=80+Math.random()*10,e=Math.round(a*10)/10;t.realTimeData.predictionAccuracy=e.toFixed(1);const d=(Math.random()-.5)*2,u=d>=0?`+${d.toFixed(1)}%`:`${d.toFixed(1)}%`;t.realTimeData.predictionAccuracyChange=u},$=()=>{const a=parseFloat(t.params.minFlow)||0,e=parseFloat(t.params.maxFlow)||0;if(a>0&&e>0&&e>=a){const d=(a+e)/2;t.params.averageFlow=d.toFixed(1)}else t.params.averageFlow="0"},Z=()=>{const a=parseFloat(t.params.dosage)||20,e=parseFloat(t.params.averageFlow)||1250,d=Array.from({length:24},(i,f)=>`${f.toString().padStart(2,"0")}时`),u=[],g=a*1e3/(e*1e3);for(let i=0;i<24;i++){const f=Math.sin(i*Math.PI/6)*.4,l=Math.cos(i*Math.PI/4)*.25,v=Math.sin(i*Math.PI/3)*.15;let o=0;i>=6&&i<=9||i>=17&&i<=21?o=-.3:(i>=22||i<=5)&&(o=.2);const r=(Math.random()-.5)*.4,m=Math.abs(Math.sin(i*Math.PI/2))*.3,y=f+l+v+o+r+m,w=g*(1+y),c=Math.max(g*.3,w);u.push(Number(c.toFixed(2)))}return{timeData:d,concentrationData:u}},H=()=>{t.params.minFlow="500",t.params.maxFlow="2000",t.params.dosage="20",t.params.averageFlow="1250",$(),I()},N=async()=>{t.selectedStationId&&(await Promise.all([P(),K(),Q()]),I(),X())},tt=async()=>{try{t.loading=!0,$(),I(),await Dt({stationId:t.selectedStationId,timeRangeEnabled:t.params.timeRangeEnabled,supplyFlow:t.params.supplyFlow,minFlow:Number(t.params.minFlow),maxFlow:Number(t.params.maxFlow),howParameter:t.params.howParameter}),await N()}catch{}finally{t.loading=!1}};let k=null;const et=()=>{k=setInterval(()=>{N()},5*60*1e3)},at=()=>{k&&(clearInterval(k),k=null)};return nt([()=>t.params.minFlow,()=>t.params.maxFlow],()=>{$()},{immediate:!0}),rt(async()=>{M(),await b(),$(),I(),t.selectedStationId&&await N(),et()}),lt(()=>{at()}),(a,e)=>{const d=it,u=ct,p=pt,g=ut;return O(),E("div",xt,[s("div",bt,[s("div",Ct,[e[6]||(e[6]=s("div",{class:"card-header"},[s("span",{class:"card-title"},"当前流量")],-1)),s("div",_t,[s("div",Pt,S(t.realTimeData.todayFlow),1),e[5]||(e[5]=s("div",{class:"unit"},"m³/h",-1)),s("div",Ft,"↑ "+S(t.realTimeData.flowChangeRate),1)])]),s("div",Mt,[e[8]||(e[8]=s("div",{class:"card-header"},[s("span",{class:"card-title"},"出厂压力")],-1)),s("div",Tt,[s("div",St,S(t.realTimeData.outletPressure),1),e[7]||(e[7]=s("div",{class:"unit"},"MPa",-1)),s("div",Vt,"↓ "+S(t.realTimeData.outletPressureChange),1)])]),s("div",Ot,[e[10]||(e[10]=s("div",{class:"card-header"},[s("span",{class:"card-title"},"末梢压力")],-1)),s("div",At,[s("div",It,S(t.realTimeData.inletPressure),1),e[9]||(e[9]=s("div",{class:"unit"},"MPa",-1)),s("div",$t,"↑ "+S(t.realTimeData.inletPressureChange),1)])]),s("div",kt,[e[12]||(e[12]=s("div",{class:"card-header"},[s("span",{class:"card-title"},"预测准确率")],-1)),s("div",Et,[s("div",Rt,S(t.realTimeData.predictionAccuracy),1),e[11]||(e[11]=s("div",{class:"unit"},"%",-1)),s("div",Nt,"↑ "+S(t.realTimeData.predictionAccuracyChange),1)])])]),s("div",Lt,[s("div",Yt,[s("div",jt,[s("div",zt,[e[16]||(e[16]=s("div",{class:"section-title"},"模型参数配置",-1)),s("div",Bt,[e[14]||(e[14]=s("label",null,"出厂水流量范围 (m³/h)",-1)),s("div",Ut,[_(d,{modelValue:t.params.minFlow,"onUpdate:modelValue":e[0]||(e[0]=i=>t.params.minFlow=i),placeholder:"最小值",size:"small"},null,8,["modelValue"]),e[13]||(e[13]=s("span",null,"-",-1)),_(d,{modelValue:t.params.maxFlow,"onUpdate:modelValue":e[1]||(e[1]=i=>t.params.maxFlow=i),placeholder:"最大值",size:"small"},null,8,["modelValue"])]),s("div",Wt," 平均值: "+S(t.params.averageFlow)+" m³/h ",1)]),s("div",qt,[e[15]||(e[15]=s("label",null,"加药量 (mg)",-1)),_(d,{modelValue:t.params.dosage,"onUpdate:modelValue":e[2]||(e[2]=i=>t.params.dosage=i),placeholder:"请输入加药量",type:"number",min:0,max:100,step:.1},null,8,["modelValue"])])]),s("div",Gt,[_(u,{type:"primary",onClick:tt,loading:t.loading},{default:F(()=>e[17]||(e[17]=[R("应用参数")])),_:1},8,["loading"]),_(u,{onClick:H},{default:F(()=>e[18]||(e[18]=[R("重置参数")])),_:1})])])]),s("div",Jt,[_(p,{title:"出厂水流量曲线",class:"chart-card"},{right:F(()=>e[19]||(e[19]=[s("div",{class:"chart-controls"},null,-1)])),default:F(()=>[s("div",Kt,[t.flowChartOption?(O(),L(A(j),{key:0,option:t.flowChartOption,theme:A(Y)().isDark?"dark":"light",style:{width:"100%",height:"100%"}},null,8,["option","theme"])):(O(),E("div",Qt,e[20]||(e[20]=[s("div",{style:{"margin-bottom":"10px"}},"暂无流量数据",-1),s("div",{style:{"font-size":"12px",color:"#ccc"}}," 流量接口无数据或数据格式异常 ",-1)])))])]),_:1}),_(p,{title:"压力变化趋势",class:"chart-card"},{right:F(()=>[s("div",Xt,[_(g,null,{default:F(()=>[_(u,{type:t.pressureType==="outlet"?"primary":"",onClick:e[3]||(e[3]=i=>t.pressureType="outlet"),size:"small"},{default:F(()=>e[21]||(e[21]=[R(" 出厂压力 ")])),_:1},8,["type"]),_(u,{type:t.pressureType==="inlet"?"primary":"",onClick:e[4]||(e[4]=i=>t.pressureType="inlet"),size:"small"},{default:F(()=>e[22]||(e[22]=[R(" 预测压力 ")])),_:1},8,["type"])]),_:1})])]),default:F(()=>[s("div",Zt,[t.pressureChartOption?(O(),L(A(j),{key:0,option:t.pressureChartOption,theme:A(Y)().isDark?"dark":"light",style:{width:"100%",height:"100%"}},null,8,["option","theme"])):(O(),E("div",Ht,e[23]||(e[23]=[s("div",{style:{"margin-bottom":"10px"}},"暂无压力数据",-1),s("div",{style:{"font-size":"12px",color:"#ccc"}}," 压力接口无数据或数据格式异常 ",-1)])))])]),_:1}),_(p,{title:"水体药物浓度预测",class:"chart-card"},{default:F(()=>[s("div",te,[t.concentrationChartOption?(O(),L(A(j),{key:0,option:t.concentrationChartOption,theme:A(Y)().isDark?"dark":"light",style:{width:"100%",height:"100%"}},null,8,["option","theme"])):(O(),E("div",ee," 浓度图表配置加载中... "))])]),_:1})])])])}}}),ce=dt(ae,[["__scopeId","data-v-52fb4a26"]]);export{ce as default};
