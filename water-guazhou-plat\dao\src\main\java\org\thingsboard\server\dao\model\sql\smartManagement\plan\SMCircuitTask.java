package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("sm_circuit_task")
public class SMCircuitTask {
    // id
    @TableId
    private String id;

    // 任务编号
    private String code;

    // 所属巡检计划Id
    private String planId;

    // 所属片区区域、路线(点阵)Id
    private String districtAreaId;

    // 是否为常规计划
    private Boolean isNormalPlan;

    // 是否需要反馈
    private Boolean isNeedFeedback;

    // 行进方式：车巡、步行
    private String moveType;

    // 设备Id，多个用逗号隔开
    private String devices;

    // 专项设备Id，多个用逗号隔开
    private String specialDevices;

    // 任务名称
    private String name;

    // 接收人员Id
    private String receiveUserId;

    // 共同完成人Id，多个用逗号隔开
    private String collaborateUserId;

    // 到位距离
    private String presentDistance;

    // 任务状态
    //分派状态(PENDING：任务创建;VERIFY：待审核；RECEIVE：已指派)
    private String status;

    // 描述
    private String remark;

    // 创建人Id
    private String creator;

    // 创建时间
    private Date createTime;

    // 开始时间
    private Date beginTime;

    // 结束时间
    private Date endTime;

    // 租户Id
    private String tenantId;

    // 审核人
    private String auditUserId;

    //审核时间
    private Date auditTime;

    //拒绝原因
    private String rejectReason;
}
