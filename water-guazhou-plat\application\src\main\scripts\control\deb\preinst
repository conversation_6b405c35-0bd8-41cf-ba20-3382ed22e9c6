#!/bin/sh

if ! getent group ${pkg.name} >/dev/null; then
    addgroup --system ${pkg.name}
fi

if ! getent passwd ${pkg.name} >/dev/null; then
    adduser --quiet \
            --system \
            --ingroup ${pkg.name} \
            --quiet \
            --disabled-login \
            --disabled-password \
            --home ${pkg.installFolder} \
            --no-create-home \
            -gecos "Thingsboard application" \
            ${pkg.name}
fi
