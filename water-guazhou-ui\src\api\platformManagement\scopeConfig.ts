import { request } from '@/plugins/axios'

// 获取范围设置列表
export function getScopeConfigList(params: any) {
  return request({
    url: '/api/base/scope/configuration/list',
    method: 'get',
    params
  })
}

// 获取范围设置详情
export function getScopeConfigDetail(id: string) {
  return request({
    url: '/api/base/scope/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增范围设置
export function addScopeConfig(data: any) {
  return request({
    url: '/api/base/scope/configuration/add',
    method: 'post',
    data
  })
}

// 修改范围设置
export function editScopeConfig(data: any) {
  return request({
    url: '/api/base/scope/configuration/edit',
    method: 'post',
    data
  })
}

// 删除范围设置
export function deleteScopeConfig(ids: string[]) {
  return request({
    url: '/api/base/scope/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 