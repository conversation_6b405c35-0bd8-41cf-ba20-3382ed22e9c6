import request from '@/plugins/axios';

/**
 * 模板列表
 * @param params
 * @returns
 */
export const getSmsTemplateList = (params?: {
  page: number;
  size: number;
  name?: string;
  configId?: string;
}) =>
  request({
    method: 'get',
    url: `/api/msg/template/list`,
    params
  });

/**
 * 发送短信
 * @param params
 */
export const sendSms = (params?: {
  templateId: string;
  phoneList: any;
  params: any;
}) =>
  request({
    method: 'post',
    url: `/api/msg/template/send`,
    data: params
  });
