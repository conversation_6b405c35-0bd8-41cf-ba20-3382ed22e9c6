package org.thingsboard.server.dao.model.DTO;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class TreeNodeDTO {

    private String id;

    private String parentId;

    // 节点名称
    private String name;

    // 节点类型
    private String type;

    // 节点类型名称
    private String typeName;

    // 节点业务数据
    private Object nodeDetail;

    private Integer num = 0;

    private String icon;

    private List<TreeNodeDTO> children;

}
