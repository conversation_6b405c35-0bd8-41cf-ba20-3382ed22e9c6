// 工程管理-设备管理
import request from '@/plugins/axios';

/**
 * 查询设备类型树
 * @returns
 */
export function geDeviceTypeTree() {
  return request({
    url: `/api/so/deviceType/tree`,
    method: 'get'
  });
}

/**
 * 添加设备类型
 * @returns
 */
export function postDeviceType(params: {
  id?: string;
  serialId: string;
  name: string;
  sortNum: string;
  remark: string;
}) {
  return request({
    url: `/api/so/deviceType`,
    method: 'post',
    data: params
  });
}

/**
 * 分页查询设备类型
 * @returns
 */
export function geDeviceTypeList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  serialId?: string;
  name?: string;
  parentId?: string;
}) {
  return request({
    url: `/api/so/deviceType`,
    method: 'get',
    params
  });
}

/**
 * 删除设备类型
 * @returns
 */
export function deleteDeviceType(id: string) {
  return request({
    url: `/api/so/deviceType/${id}`,
    method: 'delete'
  });
}

/**
 * 分页查询设备信息
 * @returns
 */
export function getDeviceList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  serialId?: string;
  typeSerialId?: string;
  name?: string;
  model?: string;
}) {
  return request({
    url: `/api/so/device`,
    method: 'get',
    params
  });
}

/**
 * 添加设备信息
 * @returns
 */
export function postDevice(params: {
  id?: string;
  serialId: string;
  name: string;
  model: string;
  mark?: string;
  orderNum?: string;
  remark?: string;
}) {
  return request({
    url: `/api/so/device`,
    method: 'post',
    data: params
  });
}

/**
 * 删除设备信息
 * @returns
 */
export function deleteDevice(id: string) {
  return request({
    url: `/api/so/device/${id}`,
    method: 'delete'
  });
}

/**
 * 批量删除设备信息
 * @returns
 */
export function deleteDevices(data: string[]) {
  return request({
    url: `/api/so/device`,
    method: 'delete',
    data
  });
}

/**
 * 项目添加设备
 * @returns
 */
export function postProjectDevice(
  projectCode: string,
  params: {
    serialId: string;
    amount: string;
  }[]
) {
  return request({
    url: `/api/so/project/${projectCode}/device`,
    method: 'post',
    data: params
  });
}

/**
 * 获取项目下的设备信息
 * @returns
 */
export function getProjectDevice(
  projectCode: string,
  params: {
    page: number;
    size: number;
    fromTime?: string;
    toTime?: string;
    serialId?: string;
  }
) {
  return request({
    url: `/api/so/project/${projectCode}/device`,
    method: 'get',
    params
  });
}

/**
 * 获取项目下的设备信息
 * @returns
 */
export function getDeviceItem(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  serialId?: string;
  name?: string;
}) {
  return request({
    url: `/api/so/deviceItem`,
    method: 'get',
    params
  });
}

/**
 * 合同添加设备
 * @returns
 */
export function postConstructionContractDevice(
  code: string,
  params: {
    serialId: string;
    amount: string;
  }[]
) {
  return request({
    url: `/api/so/constructionContract/${code}/device`,
    method: 'post',
    data: params
  });
}

/**
 * 获取合同下的设备信息
 * @returns
 */
export function getConstructionContractDevice(
  code: string,
  params: {
    page: number;
    size: number;
    fromTime?: string;
    toTime?: string;
    serialId?: string;
  }
) {
  return request({
    url: `/api/so/constructionContract/${code}/device`,
    method: 'get',
    params
  });
}

/**
 * 实施添加设备
 * @returns
 */
export function postConstructionApplyDevice(
  code: string,
  params: {
    serialId: string;
    amount: string;
  }[]
) {
  return request({
    url: `/api/so/constructionApply/${code}/device`,
    method: 'post',
    data: params
  });
}

/**
 * 获取实施下的设备信息
 * @returns
 */
export function getConstructionApplyDevice(
  code: string,
  params: {
    page: number;
    size: number;
    fromTime?: string;
    toTime?: string;
    serialId?: string;
  }
) {
  return request({
    url: `/api/so/constructionApply/${code}/device`,
    method: 'get',
    params
  });
}
