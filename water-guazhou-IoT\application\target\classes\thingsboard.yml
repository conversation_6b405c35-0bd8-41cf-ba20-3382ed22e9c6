#
# Copyright © 2016-2019 The Thingsboard Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  # Server bind address
  address: "${HTTP_BIND_ADDRESS:0.0.0.0}"
  # Server bind port
  port: "${HTTP_BIND_PORT:8901}"
  # Server SSL configuration
  ssl:
    # Enable/disable SSL support
    enabled: "${SSL_ENABLED:false}"
    # Path to the key store that holds the SSL certificate
    key-store: "${SSL_KEY_STORE:classpath:keystore/keystore.p12}"
    # Password used to access the key store
    key-store-password: "${SSL_KEY_STORE_PASSWORD:thingsboard}"
    # Type of the key store
    key-store-type: "${SSL_KEY_STORE_TYPE:PKCS12}"
    # Alias that identifies the key in the key store
    key-alias: "${SSL_KEY_ALIAS:tomcat}"
  log_controller_error_stack_trace: "${HTTP_LOG_CONTROLLER_ERROR_STACK_TRACE:true}"
  ws:
    send_timeout: "${TB_SERVER_WS_SEND_TIMEOUT:5000}"
    limits:
      # Limit the amount of sessions and subscriptions available on each server. Put values to zero to disable particular limitation
      max_sessions_per_tenant: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_TENANT:0}"
      max_sessions_per_customer: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_CUSTOMER:0}"
      max_sessions_per_regular_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_REGULAR_USER:0}"
      max_sessions_per_public_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_PUBLIC_USER:0}"
      max_queue_per_ws_session: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_QUEUE_PER_WS_SESSION:500}"
      max_subscriptions_per_tenant: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_TENANT:0}"
      max_subscriptions_per_customer: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_CUSTOMER:0}"
      max_subscriptions_per_regular_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_REGULAR_USER:0}"
      max_subscriptions_per_public_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_PUBLIC_USER:0}"
      max_updates_per_session: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_UPDATES_PER_SESSION:300:1,3000:60}"
  rest:
    limits:
      tenant:
        enabled: "${TB_SERVER_REST_LIMITS_TENANT_ENABLED:false}"
        configuration: "${TB_SERVER_REST_LIMITS_TENANT_CONFIGURATION:100:1,2000:60}"
      customer:
        enabled: "${TB_SERVER_REST_LIMITS_CUSTOMER_ENABLED:false}"
        configuration: "${TB_SERVER_REST_LIMITS_CUSTOMER_CONFIGURATION:50:1,1000:60}"

# Zookeeper connection parameters. Used for service discovery.
zk:
  # Enable/disable zookeeper discovery service.
  enabled: "${ZOOKEEPER_ENABLED:false}"
  # Zookeeper connect string
  url: "${ZOOKEEPER_URL:localhost:2181}"
  # Zookeeper retry interval in milliseconds
  retry_interval_ms: "${ZOOKEEPER_RETRY_INTERVAL_MS:3000}"
  # Zookeeper connection timeout in milliseconds
  connection_timeout_ms: "${ZOOKEEPER_CONNECTION_TIMEOUT_MS:3000}"
  # Zookeeper session timeout in milliseconds
  session_timeout_ms: "${ZOOKEEPER_SESSION_TIMEOUT_MS:3000}"
  # Name of the directory in zookeeper 'filesystem'
  zk_dir: "${ZOOKEEPER_NODES_DIR:/thingsboard}"

# Plugins configuration parameters
plugins:
  # Comma separated package list used during classpath scanning for plugins
  scan_packages: "${PLUGINS_SCAN_PACKAGES:org.thingsboard.server.extensions,org.thingsboard.rule.engine}"

# Security parameters
security:
  # JWT Token parameters
  jwt:
    tokenExpirationTime: "${JWT_TOKEN_EXPIRATION_TIME:90000000}" # Number of seconds (15 mins)
    refreshTokenExpTime: "${JWT_REFRESH_TOKEN_EXPIRATION_TIME:3600}" # Seconds (1 hour)
    tokenIssuer: "${JWT_TOKEN_ISSUER:thingsboard.io}"
    tokenSigningKey: "${JWT_TOKEN_SIGNING_KEY:thingsboardDefaultSigningKey}"
  # Enable/disable access to Tenant Administrators JWT token by System Administrator or Customer Users JWT token by Tenant Administrator
  user_token_access_enabled: "${SECURITY_USER_TOKEN_ACCESS_ENABLED:true}"
  # Enable/disable case-sensitive username login
  user_login_case_sensitive: "${SECURITY_USER_LOGIN_CASE_SENSITIVE:true}"

# Dashboard parameters
dashboard:
  # Maximum allowed datapoints fetched by widgets
  max_datapoints_limit: "${DASHBOARD_MAX_DATAPOINTS_LIMIT:60000}"

database:
  ts_max_intervals: "${DATABASE_TS_MAX_INTERVALS:700}" # Max number of DB queries generated by single API call to fetch telemetry records
  entities:
    type: "${DATABASE_ENTITIES_TYPE:sql}" # cassandra OR sql
  ts:
    type: "${DATABASE_TS_TYPE:sql}" # cassandra OR sql (for hybrid mode, only this value should be cassandra)

# SQL configuration parameters
sql:
  # Specify executor service type used to perform timeseries insert tasks: SINGLE FIXED CACHED
  ts_inserts_executor_type: "${SQL_TS_INSERTS_EXECUTOR_TYPE:cached}"
  # Specify thread pool size for FIXED executor service type
  ts_inserts_fixed_thread_pool_size: "${SQL_TS_INSERTS_FIXED_THREAD_POOL_SIZE:64}"

cache:
  # caffeine or redis
  type: "${CACHE_TYPE:redis}"

caffeine:
  specs:
    relations:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    deviceCredentials:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    devices:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    sessions:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    assets:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    entityViews:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    attribute:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    alarm:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    virtual:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    alarmJson:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    lastData:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    getWaterSupplyTotal:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    getWaterSupplyTotalAll:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    getDeviceData:
      timeToLiveInMinutes: 1440
      maxSize: 100000

redis:
  # standalone or cluster
  connection:
    type: standalone
    host: "${REDIS_HOST:***********}"
    port: "${REDIS_PORT:6379}"
    db: "${REDIS_DB:0}"
    password: "${REDIS_PASSWORD:Zdsc@2025.}"

# Check new version updates parameters
updates:
  # Enable/disable updates checking.
  enabled: "${UPDATES_ENABLED:false}"

# spring CORS configuration
spring.mvc.cors:
  mappings:
    # Intercept path
    "/api/**":
      #Comma-separated list of origins to allow. '*' allows all origins. When not set,CORS support is disabled.
      allowed-origins: "*"
      #Comma-separated list of methods to allow. '*' allows all methods.
      allowed-methods: "POST,GET,OPTIONS,DELETE"
      #Comma-separated list of headers to allow in a request. '*' allows all headers.
      allowed-headers: "*"
      #How long, in seconds, the response from a pre-flight request can be cached by clients.
      max-age: "1800"
      #Set whether credentials are supported. When not set, credentials are not supported.
      allow-credentials: "true"
    "/api/v1/**":
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
      max-age: "1800"
      allow-credentials: "true"


# spring serve gzip compressed static resources
spring.resources.chain:
  gzipped: "true"
  strategy:
    content:
      enabled: "true"

        # HSQLDB DAO Configuration
      #  spring:
      ##    data:
      ##      jpa:
      ##        repositories:
      ##          enabled: "true"
      ##    jpa:
      ##      hibernate:
      ##        ddl-auto: "validate"
      ##      database-platform: "${SPRING_JPA_DATABASE_PLATFORM:org.hibernate.dialect.HSQLDialect}"
      ##    datasource:
      ##      driverClassName: "${SPRING_DRIVER_CLASS_NAME:org.hsqldb.jdbc.JDBCDriver}"
      ##      url: "${SPRING_DATASOURCE_URL:jdbc:hsqldb:file:${SQL_DATA_FOLDER:/tmp}/thingsboardDb;sql.enforce_size=false;hsqldb.log_size=5}"
      ##      username: "${SPRING_DATASOURCE_USERNAME:sa}"
      ##      password: "${SPRING_DATASOURCE_PASSWORD:}"

  #PostgreSQL DAO Configuration
spring:
  jpa:
    properties:
      hibernate:
        query:
          plan_cache_max_size: 64
          plan_parameter_metadata_max_size: 32
  data:
    sql:
      repositories:
        enabled: "true"
  sql:
    hibernate:
      ddl-auto: "validate"
    database-platform: "${SPRING_JPA_DATABASE_PLATFORM:org.hibernate.dialect.PostgreSQLDialect}"
  datasource:
    driverClassName: "${SPRING_DRIVER_CLASS_NAME:org.postgresql.Driver}"
    url: "${SPRING_DATASOURCE_URL:****************************************************************,revenue}"
    username: postgres
    password: Admin123!@#
  application:
    name: istar-service
  mail:
    host: smtp.qq.com
    username: <EMAIL>
    password: wfylfovthfoihcej
mail:
  # 接收人，多个以逗号分隔
  receiver: <EMAIL>,<EMAIL>,<EMAIL>
# Audit log parameters
audit_log:
  # Enable/disable audit log functionality.
  enabled: "${AUDIT_LOG_ENABLED:true}"
  # Specify partitioning size for audit log by tenant id storage. Example MINUTES, HOURS, DAYS, MONTHS
  by_tenant_partitioning: "${AUDIT_LOG_BY_TENANT_PARTITIONING:MONTHS}"
  # Number of days as history period if startTime and endTime are not specified
  default_query_period: "${AUDIT_LOG_DEFAULT_QUERY_PERIOD:30}"
  # Logging levels per each entity type.
  # Allowed values: OFF (disable), W (log write operations), RW (log read and write operations)
  logging_level:
    mask:
      "device": "${AUDIT_LOG_MASK_DEVICE:W}"
      "asset": "${AUDIT_LOG_MASK_ASSET:W}"
      "dashboard": "${AUDIT_LOG_MASK_DASHBOARD:W}"
      "customer": "${AUDIT_LOG_MASK_CUSTOMER:W}"
      "user": "${AUDIT_LOG_MASK_USER:W}"
      "rule_chain": "${AUDIT_LOG_MASK_RULE_CHAIN:W}"
      "alarm": "${AUDIT_LOG_MASK_ALARM:W}"
      "entity_view": "${AUDIT_LOG_MASK_ENTITY_VIEW:W}"
  sink:
    # Type of external sink. possible options: none, elasticsearch
    type: "${AUDIT_LOG_SINK_TYPE:none}"
    # Name of the index where audit logs stored
    # Index name could contain next placeholders (not mandatory):
    # @{TENANT} - substituted by tenant ID
    # @{DATE} - substituted by current date in format provided in audit_log.sink.date_format
    index_pattern: "${AUDIT_LOG_SINK_INDEX_PATTERN:@{TENANT}_AUDIT_LOG_@{DATE}}"
    # Date format. Details of the pattern could be found here:
    # https://docs.oracle.com/javase/8/docs/api/java/time/format/DateTimeFormatter.html
    date_format: "${AUDIT_LOG_SINK_DATE_FORMAT:YYYY.MM.DD}"
    scheme_name: "${AUDIT_LOG_SINK_SCHEME_NAME:http}" # http or https
    host: "${AUDIT_LOG_SINK_HOST:localhost}"
    port: "${AUDIT_LOG_SINK_PORT:9200}"
    user_name: "${AUDIT_LOG_SINK_USER_NAME:}"
    password: "${AUDIT_LOG_SINK_PASSWORD:}"

state:
  defaultInactivityTimeoutInSec: "${DEFAULT_INACTIVITY_TIMEOUT:10}"
  defaultStateCheckIntervalInSec: "${DEFAULT_STATE_CHECK_INTERVAL:10}"

eureka:
  client:
    serviceUrl:
      defaultZone: http://127.0.0.1:8081/eureka/
feign:
  hystrix:
    enabled: false
  httpclient:
    enabled: true
  isolation:
    strategy: SEMAPHORE
#ribbon的超时时间
ribbon:
  ReadTimeout: 3000
  ConnectTimeout: 3000



#阿里云时序数据库相关参数
tsdb:
  HITSDB_IP: "http://localhost"
  HITSDB_PORT: "4242"

timezone:
  default: "GMT+8:00"
install:
  #  data_dir: C:\Users\<USER>\Documents\tb_code\thingsboard-master\application\src\main\data\
  data_dir: /usr/share/thingsboard/data
  #  image_dir: C:\Users\<USER>\nginx\data\
  image_dir: /usr/local/thingsboard/image/
  server_ip: http://*************/
  update_status: false
jasypt:
  encryptor:
    password: istar
hostname:
  ui: https://ems.istarscloud.com
  api: https://api.istarscloud.com
  image: https://image.istarscloud.com/
  # 主机地址
  myhsot: http://localhost:8764

#influxdb配置
influx:
  ip: http://***********
  port: 8086
  token: 9tzuY-qNMmlh9EonBDArT6_ks1bN4-vu6GtMnzuvp7tVN1jNJdPbFCZropqyOsQqAOU090qg98SvztgV4Tzu4w==
  org: istar
app:
  # 数据位数
  data-scala: 2
