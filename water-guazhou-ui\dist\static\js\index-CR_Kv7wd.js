import{_ as F}from"./index-C9hz-UZb.js";import{d as R,M as j,r as _,c as b,am as E,bF as u,a8 as G,bB as S,s as T,j as C,a6 as M,bu as H,ay as J,g as U,n as W,q as m,i as s,F as h,cs as w,bo as D,bR as O,p as I,dF as $,dA as K,aq as Q,b7 as X,aj as Y,C as Z}from"./index-r0dFAfgr.js";import{_ as ee}from"./CardSearch-CB_HNR-Q.js";import{l as ae}from"./echart-Cn-w9IL8.js";import{g as te}from"./queryStatistics-CQ9DBM08.js";import{b as ne}from"./zhandian-YaGuQZe6.js";import{u as re}from"./useStation-DJgnSZIA.js";import{f as oe}from"./formartColumn-D5r7JJ2G.js";import"./Search-NSrhrIa_.js";const se={class:"wrapper"},le=R({__name:"index",setup(ie){const{$messageWarning:q}=j(),{getStationTree:V}=re(),t=_({type:"date",chartOption:null,activeName:"echarts",chartName:"",data:null,stationTree:[]}),y=b(),g=b(),v=b(),k=b();let f=_([]);E(()=>t.activeName,()=>{t.activeName==="echarts"&&L()});const A=_({defaultParams:{type:"day",month:[u().format(),u().format()],day:[u().startOf("day").format(),u().format()]},filters:[{type:"select-tree",label:"监测点:",field:"attributeId",clearable:!1,lazy:!0,options:G(()=>t.stationTree),lazyLoad:(e,a)=>{var o,c;if(e.level===0)return a([]);if(((o=e.data.children)==null?void 0:o.length)>0)return a(e.data.children);if(e.isLeaf)return a([]);if((c=e.data)!=null&&c.isLeaf)return a([]);console.log("dada",e.data),ne({stationId:e.data.id}).then(d=>{var n;const p=(n=d.data)==null?void 0:n.map(r=>({label:r.type,value:"",id:"",children:r.attrList.map(l=>({label:l.name,value:l.id,id:l.id,isLeaf:!0}))}));return a(p)})},nodeClick:(e,a)=>{a.isLeaf&&(t.chartName=e.label,S(()=>{N()}))}},{type:"select",label:"比较类型:",field:"type",clearable:!1,width:"200px",options:[{label:"日分时(时间段)",value:"day"},{label:"月分日(时间段)",value:"month"}],itemContainerStyle:{width:"240px"}},{type:"daterange",label:"选择日期",field:"day",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.type==="day"||e.type==="year"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var a;i.pagination.page=1,(((a=y.value)==null?void 0:a.queryParams)||{}).attributeId?N():q("选择监测点")},icon:"iconfont icon-chaxun"},{type:"default",perm:!0,text:"重置",svgIcon:T(X),click:()=>{var e;(e=y.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",hide:()=>t.activeName!=="list",svgIcon:T(Y),click:()=>z()}]}]}),i=_({loading:!1,dataList:[],columns:[],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:a})=>{i.pagination.page=e,i.pagination.limit=a,i.dataList=f.slice((e-1)*a,e*a)}}}),N=()=>{var d;i.loading=!0;const e=((d=y.value)==null?void 0:d.queryParams)||{},[a,o]=e[e.type]||[],c={attributeId:e.attributeId,queryType:e.type,start:a?u(a).startOf(e.type).valueOf():"",end:o?u(o).endOf(e.type).valueOf():""};te(c).then(p=>{var l;const n=(l=p.data)==null?void 0:l.data;t.data=n;const r=oe(n==null?void 0:n.tableInfo);f=n==null?void 0:n.tableDataList,i.columns=r,i.dataList=f==null?void 0:f.slice(0,20),i.pagination.total=n==null?void 0:n.tableDataList.length,L(),i.loading=!1})},P=()=>{var e;(e=g.value)==null||e.resize()},L=()=>{var d,p,n;const e=ae();e.series=[];const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:C().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:C().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(d=t.data)==null?void 0:d.tableDataList.map(r=>r.ts),(p=t.data)==null||p.tableInfo.map(r=>{var l;if(r.columnValue!=="ts"){const x=JSON.parse(JSON.stringify(a));x.name=r.columnName,e.yAxis[0].name=t.chartName+(r.unit?"("+r.unit+")":""),x.data=(l=t.data)==null?void 0:l.tableDataList.map(B=>B[r.columnValue]),e.series.push(x)}}),(n=g.value)==null||n.clear();const c=M({callOnAdd:!0});S(()=>{v.value&&c.listenTo(v.value,()=>{t.chartOption=e,P()})})},z=()=>{var e;(e=k.value)==null||e.exportTable()};return H(async()=>{const e=await V("水厂");t.stationTree=e,console.log(" state.stationTree ",t.stationTree)}),(e,a)=>{const o=ee,c=$,d=K,p=J("VChart"),n=Q,r=F;return U(),W("div",se,[m(o,{ref_key:"cardSearch",ref:y,config:s(A)},null,8,["config"]),m(r,{class:"card",title:s(t).chartName+(s(t).activeName==="list"?"周期对比列表":"周期对比曲线")},{right:h(()=>[m(d,{modelValue:s(t).activeName,"onUpdate:modelValue":a[0]||(a[0]=l=>s(t).activeName=l)},{default:h(()=>[m(c,{label:"echarts"},{default:h(()=>[m(s(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),m(c,{label:"list"},{default:h(()=>[m(s(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:h(()=>[D(I("div",{ref_key:"echartsDiv",ref:v,class:"chart-box"},[m(p,{ref_key:"refChart",ref:g,theme:s(C)().isDark?"dark":"light",option:s(t).chartOption},null,8,["theme","option"])],512),[[O,s(t).activeName==="echarts"]]),D(I("div",null,[m(n,{ref_key:"refCardTable",ref:k,class:"card-table",config:s(i)},null,8,["config"])],512),[[O,s(t).activeName==="list"]])]),_:1},8,["title"])])}}}),be=Z(le,[["__scopeId","data-v-5bb925fe"]]);export{be as default};
