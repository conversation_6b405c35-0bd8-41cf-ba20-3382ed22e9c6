import{_}from"./TreeBox-DDD2iwoR.js";import{_ as T}from"./index-DyRO7tCT.js";import{_ as v}from"./CardTable-rdWOL4_6.js";import{_ as C}from"./CardSearch-CB_HNR-Q.js";import{_ as j}from"./index-BJ-QPYom.js";import{C as x,M as I,s as k,a8 as w,bG as O,ag as P,D as f,a9 as U,g as s,h as l,F as g,q as n,an as h,al as B}from"./index-r0dFAfgr.js";import{c as M,d as q,e as S,f as L,h as V}from"./index-BggOjNGp.js";import{g as N}from"./ledger2-CVFjtR6o.js";import R from"./TreeBox-mfOmxwZJ.js";import"./index-qoWsDjz-.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";import"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const{$messageInfo:d,$messageError:r,$messageSuccess:o,$confirm:G,$btnPerms:A}=I(),F={components:{TreeBox:R},data(){return{deviceModel:[],cardSearchConfig:{filters:[{label:"搜索",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:k(B),click:()=>this.refreshData()},{text:"添加终端",perm:A("MQTTGateWayAdd"),icon:"iconfont icon-jia",click:()=>{this.addOrUpdateConfig.defaultValue={type:"MQTT",dropJudgement:"15m",location:[116.4,39.91],gateway:!0},this.addOrUpdateConfig.externalParams={projectId:this.treeData.currentProject.id},this.addOrUpdateConfig.title="添加终端",this.addOrUpdateConfig.visible=!0}}]}]},cardTableConfig:{loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"终端名称"},{prop:"status",label:"在线状态",formatter:e=>e.status?"在线":"离线"}],operations:[{text:"编辑",isTextBtn:!0,perm:!0,icon:"iconfont icon-bianji",click:e=>{const t=JSON.parse(e.additionalInfo);this.addOrUpdateConfig.title="编辑终端",this.addOrUpdateConfig.defaultValue={...e,...t,gateway:!0},this.addOrUpdateConfig.visible=!0}},{text:"复制",isTextBtn:!0,perm:!0,icon:"iconfont icon-icon_fuzhi",click:e=>this.copyGateway(e)},{text:"删除",isTextBtn:!0,perm:!0,type:"danger",icon:"iconfont icon-bianji",click:e=>this.handleDelete(e)}],operationWidth:"220px",pagination:{page:1,limit:20,total:0,handleSize:e=>{this.cardTableConfig.pagination.limit=e,this.refreshData()},handlePage:e=>{this.cardTableConfig.pagination.page=e,this.refreshData()}}},addOrUpdateConfig:{visible:!1,title:"添加终端",close:()=>this.addOrUpdateConfig.visible=!1,open:()=>{this.getTemplateListByType(),this.getDeviceModelList()},addUrl:"api/device",editUrl:"api/device",defaultValue:{type:"MQTT",location:[116.4,39.91]},columns:[{type:"input",label:"终端名称:",key:"name",rules:[{required:!0,message:"请填写终端名称"}]},{type:"input",label:"终端编码:",key:"foreignKey",rules:[{required:!0,message:"请填写终端编码"}]},{type:"input",label:"传输协议:",key:"type",disabled:!0},{type:"select",label:"协议模板:",key:"templateId",options:[]},{type:"select",allowCreate:!0,key:"deviceTypeName",search:!0,label:"终端类型",rules:[{required:!0,message:"请输入终端类型"}]},{type:"select",allowCreate:!0,key:"hardwareId",search:!0,label:"设备型号",rules:[{required:!0,message:"请输入设备型号"}],options:w(()=>this.deviceModel)},{type:"textarea",label:"备注:",key:"introduction",aInfo:!0,rows:3},{type:"select",label:"掉线判断:",aInfo:!0,key:"dropJudgement",options:[{value:"1m",label:"1分钟"},{value:"5m",label:"5分钟"},{value:"10m",label:"10分钟"},{value:"15m",label:"15分钟"},{value:"24h",label:"24小时"}]},{type:"location",label:"终端定位:",aInfo:!0,key:"location",rules:[{required:!0,message:"请输入终端定位"}]},{type:"none",aInfo:!0,key:"gateway"}]},projectDialog:{visible:!1,title:"新建项目",close:()=>{this.projectDialog.visible=!1},addUrl:"api/project",editUrl:"api/project/edit",defaultValue:{},externalParams:{},columns:[{type:"input",label:"项目名称:",key:"name",rules:[{required:!0,message:"请填写项目名称"}]},{type:"input",label:"WIFI名称:",aInfo:!0,key:"WIFIName",rules:[{required:!0,message:"请填写WIFI名称"}]},{type:"textarea",label:"项目简介:",key:"introduction",aInfo:!0,rows:3},{type:"image",aInfo:!0,label:"图片:",key:"imageUrl"},{type:"input",label:"项目地址:",aInfo:!0,key:"address",rules:[{required:!0,message:"请填写项目地址"}]},{type:"location",label:"项目定位:",aInfo:!0,key:"location",rules:[{required:!0,message:"请输入项目定位"}]}]},treeData:{that:this,title:"区域划分",data:[],loading:!1,isFilterTree:!0,currentId:"",currentProject:{},btnPerms:{addBtn:!0,editBtn:!0,delBtn:!0},allowCreate:!1,allowNew:!1,clickAddOrEdit:(e,t)=>{console.log(e),this.projectDialog.externalParams={},t==="edit"?(e.additionalInfo&&(this.projectDialog.defaultValue={...e,...JSON.parse(e.additionalInfo)}),this.projectDialog.title="编辑项目"):(this.projectDialog.title="新建项目",this.projectDialog.defaultValue={location:[116.4,39.91]},e&&(this.projectDialog.defaultValue={location:[116.4,39.91]},this.projectDialog.externalParams={parentId:e.id})),this.projectDialog.visible=!0},expandNodeId:[],defaultProps:{children:"children",label:"name"},treeNodeHandleClick:e=>{this.treeData.currentProject=e,this.refreshData()},allowAdd:!0,allowEdit:!0,allowDelete:!0,projectDelete(e){O(e).then(()=>{o("操作成功"),this.that.refreshTree()})}}}},created(){this.refreshTree(!0),this.initDeviceType()},methods:{initDeviceType(){N().then(e=>{e.data&&(this.addOrUpdateConfig.columns[4].options=e.data.map(t=>({label:t,value:t})))})},async refreshData(e){this.cardTableConfig.loading=!0;const t={page:this.cardTableConfig.pagination.page,size:this.cardTableConfig.pagination.limit};e||Object.assign(t,this.$refs.cardSearch.queryParams);try{const a=await M(this.treeData.currentProject.id,"MQTT",t);a.status===200?(this.cardTableConfig.dataList=a.data.data,this.cardTableConfig.pagination.total=a.data.total):d("暂无终端"),this.cardTableConfig.loading=!1}catch(a){r(a.data.message),this.cardTableConfig.loading=!1}},refreshTree(e){P().then(t=>{if(this.totalLoading=!1,t.data){this.treeData.data=t.data;const a=this.treeData.data.filter(c=>!c.disabled);this.treeData.currentProject=a[0],this.refreshData(e)}else d("暂无项目 不可操作，请创建项目")}).catch(t=>{console.log(t),d("暂无项目 不可操作，请创建项目")})},async edit(){const e={type:this.editParams.type,name:this.editParams.name,timeRange:this.editParams.timeRange.join("-")};try{(await editYxscgl(e)).status===200?(o("修改成功！"),this.refreshData()):r("修改失败！")}catch{r("修改失败！")}},handleDelete(e){G("确定删除指定终端?","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{q(f(e.id.id)).then(()=>{o("操作成功"),this.refreshData()})})},async copyGateway(e){try{await S(f(e.id.id),this.treeData.currentProject.id),o("复制成功")}catch(t){r(t.message||t.data.message)}},getTemplateListByType(){L("MQTT").then(e=>{this.addOrUpdateConfig.columns[3].options=e.data.map(t=>({value:t.id,label:t.name}))})},getDeviceModelList(){V({page:1,size:1e4}).then(e=>{this.deviceModel=U(e.data.data.data||[],"children",{label:"name",value:"id"})})}}};function Q(e,t,a,c,i,p){const m=j,y=C,b=v,u=T,D=_;return s(),l(D,null,{tree:g(()=>[n(m,{"tree-data":i.treeData},null,8,["tree-data"])]),default:g(()=>[n(y,{ref:"cardSearch",config:i.cardSearchConfig,class:"card-search"},null,8,["config"]),n(b,{config:i.cardTableConfig,class:"card-table"},null,8,["config"]),i.addOrUpdateConfig.visible?(s(),l(u,{key:0,"dialog-width":"560px",config:i.addOrUpdateConfig,onRefreshData:p.refreshData},null,8,["config","onRefreshData"])):h("",!0),i.projectDialog.visible?(s(),l(u,{key:1,"dialog-width":"560px",config:i.projectDialog,onRefreshData:p.refreshData},null,8,["config","onRefreshData"])):h("",!0)]),_:1})}const le=x(F,[["render",Q],["__scopeId","data-v-b4ce46fe"]]);export{le as default};
