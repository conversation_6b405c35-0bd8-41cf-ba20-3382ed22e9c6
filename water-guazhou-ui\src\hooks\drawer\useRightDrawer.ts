export const useRightDrawer = (options?: {
  minWidth?: number
  defaultStatus?: 'opened' | 'more' | 'closed' | ''
  widthOption?: {
    opend?: number
    more?: number
    default?: number
  }
}) => {
  const drawerMinWidth = ref<number>(options?.minWidth === undefined ? 48 : options.minWidth)
  const drawerAbsolute = computed(() => {
    if (drawerLevel.value === 'closed' || drawerLevel.value === 'opened') return false

    return true
  })
  const drawerLevel = ref<'opened' | 'more' | 'closed' | ''>('')
  const dialogWidth = computed(() => {
    if (drawerLevel.value === '') return options?.widthOption?.default || drawerMinWidth.value
    if (drawerLevel.value === 'opened') return options?.widthOption?.opend || 460
    if (drawerLevel.value === 'more') return options?.widthOption?.more || 1460
    if (drawerLevel.value === 'closed') return drawerMinWidth.value
  })
  const beforeCollapse = (
    drawer: 'rtl' | 'ltr' | 'btt' | 'ttb',
    open?: boolean,
    openLevel?: '' | 'opened' | 'more' | 'closed'
  ) => {
    if (open === false) {
      if (drawerLevel.value === 'opened') {
        drawerLevel.value = 'closed'
        return true
        // 返回false阻止drawer关闭
      }
      if (drawerLevel.value === 'more') {
        drawerLevel.value = 'opened'
      }
      if (drawerLevel.value === '') {
        return true
      }
      return false
    }
    // 打开drawer逻辑
    if (drawerLevel.value === '') {
      drawerLevel.value = openLevel ?? 'opened'
      return true
    }
    if (drawerLevel.value === 'closed') {
      drawerLevel.value = openLevel ?? 'opened'
      return true
    }
    if (drawerLevel.value === 'opened') {
      drawerLevel.value = openLevel ?? 'more'
      return false
    }
    if (drawerLevel.value === 'more') {
      drawerLevel.value = openLevel ?? 'opened'
      return false
    }
  }
  return {
    dialogWidth,
    drawerLevel,
    drawerMinWidth,
    drawerAbsolute,
    beforeCollapse
  }
}
export default useRightDrawer
