import request from '@/plugins/axios';

/**
 * 获取管网设备列表
 * @param params 查询参数
 */
export function getPipelineDeviceList(params: any) {
  const defaultParams = {
    page: 1,
    size: 10,
    deviceType: '',
    deviceName: '',
    deviceCode: '',
    location: '',
    status: ''
  };

  return request({
    url: '/api/pipelineDevice/list',
    method: 'get',
    params: { ...defaultParams, ...params }
  });
}

/**
 * 根据设备ID查询管网设备详情
 * @param deviceId 设备ID
 */
export function getPipelineDeviceById(deviceId: string) {
  return request({
    url: `/api/pipelineDevice/${deviceId}`,
    method: 'get'
  });
}

/**
 * 获取管网设备用户权限列表
 * @param deviceId 设备ID
 */
export function getPipelineDeviceUserAuth(deviceId: string) {
  return request({
    url: `/api/pipelineDeviceAuth/device/${deviceId}`,
    method: 'get'
  });
}

/**
 * 批量保存管网设备用户权限
 * @param data 设备用户权限数据
 */
export function batchSavePipelineDeviceUserAuth(data: any) {
  return request({
    url: '/api/pipelineDeviceAuth/batch',
    method: 'post',
    data
  });
}

/**
 * 删除管网设备用户权限
 * @param deviceId 设备ID
 * @param userId 用户ID
 */
export function deletePipelineDeviceUserAuth(deviceId: string, userId: string) {
  return request({
    url: '/api/pipelineDeviceAuth',
    method: 'delete',
    params: { deviceId, userId }
  });
}

/**
 * 获取用户分组树
 */
export function getUserGroupTree() {
  return request({
    url: '/api/userGroup/tree',
    method: 'get'
  });
}

/**
 * 根据分组ID获取用户列表
 * @param groupIds 分组ID数组
 */
export function getUserListByGroupIds(groupIds: string[]) {
  return request({
    url: '/api/user/byGroups',
    method: 'post',
    data: { groupIds }
  });
}

/**
 * 检查用户对设备的权限
 * @param deviceId 设备ID
 * @param userId 用户ID
 */
export function checkPipelineDeviceAuth(deviceId: string, userId: string) {
  return request({
    url: '/api/pipelineDeviceAuth/check',
    method: 'get',
    params: { deviceId, userId }
  });
}

/**
 * 获取设备类型统计
 */
export function getPipelineDeviceTypeStats() {
  return request({
    url: '/api/pipelineDevice/stats/type',
    method: 'get'
  });
}

/**
 * 获取设备权限统计
 */
export function getPipelineDeviceAuthStats() {
  return request({
    url: '/api/pipelineDeviceAuth/stats',
    method: 'get'
  });
}

/**
 * 导出设备权限报表
 * @param params 查询参数
 */
export function exportPipelineDeviceAuthReport(params: any) {
  return request({
    url: '/api/pipelineDeviceAuth/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 批量导入设备权限
 * @param file 文件
 */
export function importPipelineDeviceAuth(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/api/pipelineDeviceAuth/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 设备类型枚举
export enum PipelineDeviceType {
  PRESSURE = 'pressure',
  FLOW = 'flow',
  QUALITY = 'quality'
}

// 权限类型枚举
export enum AuthType {
  FULL_CONTROL = 1,    // 完全控制
  READ_ONLY = 2,       // 只读访问
  DATA_DOWNLOAD = 3,   // 数据下载
  PARAM_SETTING = 4    // 参数设置
}

// 设备状态枚举
export enum DeviceStatus {
  ONLINE = 1,    // 在线
  OFFLINE = 0,   // 离线
  FAULT = 2      // 故障
}

// 接口类型定义
export interface PipelineDevice {
  id: string;
  name: string;
  code: string;
  type: PipelineDeviceType;
  location: string;
  status: DeviceStatus;
  installDate?: string;
  manufacturer?: string;
  model?: string;
  userAuthList?: DeviceUserAuth[];
}

export interface DeviceUserAuth {
  id?: string;
  deviceId: string;
  userId: string;
  userName: string;
  userGroupId?: string;
  userGroupName?: string;
  authType: AuthType;
  createTime?: string;
  updateTime?: string;
}

export interface UserGroup {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  children?: UserGroup[];
}

export interface PipelineDeviceAuthParams {
  deviceId: string;
  deviceName: string;
  deviceSerial: string;
  userAuthList: DeviceUserAuth[];
} 