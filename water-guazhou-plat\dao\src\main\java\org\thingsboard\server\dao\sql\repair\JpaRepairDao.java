/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.repair;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.RepairEntity;
import org.thingsboard.server.dao.repair.RepairDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
@Slf4j
public class JpaRepairDao extends JpaAbstractDao<RepairEntity, Repair> implements RepairDao {

    @Autowired
    private RepairRepository repairRepository;


    @Override
    protected Class<RepairEntity> getEntityClass() {
        return RepairEntity.class;
    }

    @Override
    protected CrudRepository<RepairEntity, String> getCrudRepository() {
        return repairRepository;
    }

    @Override
    public List<Repair> findByStatus(String status) {
        return DaoUtil.convertDataList(
                repairRepository.findByStatus(status)
        );
    }

    @Override
    public void updateStatus(List<String> needUpdateRepairIdList, String status) {
        repairRepository.updateStatus(needUpdateRepairIdList, status);
    }

    @Override
    public List<Repair> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(
                repairRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()))
        );
    }
}
