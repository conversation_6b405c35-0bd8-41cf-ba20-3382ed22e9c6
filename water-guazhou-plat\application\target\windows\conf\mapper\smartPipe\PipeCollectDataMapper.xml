<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeCollectDataMapper">

    <resultMap id="listResut" type="org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollectData">
        <result column="id" property="id"/>
        <result column="sid" property="sid"/>
        <result column="start_sid" property="start_sid"/>
        <result column="end_sid" property="end_sid"/>
        <result column="start_depth" property="start_depth"/>
        <result column="end_depth" property="end_depth"/>
        <result column="material" property="material"/>
        <result column="laneway" property="laneway"/>
        <result column="address" property="address"/>
        <result column="burytype" property="burytype"/>
        <result column="diameter" property="diameter"/>
        <result column="subtype" property="subtype"/>
        <result column="pipelength" property="pipelength"/>
        <result column="depth" property="depth"/>
        <result column="x" property="x"/>
        <result column="y" property="y"/>
        <result column="z" property="z"/>
        <result column="end_x" property="end_x"/>
        <result column="end_y" property="end_y"/>
        <result column="end_z" property="end_z"/>
        <result column="name" property="name"/>
        <result column="main_id" property="mainId"/>
        <result column="layer_id" property="layerId"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="creatorName" property="creatorName"/>
        <result column="is_storage" property="isStorage"/>
    </resultMap>

    <insert id="batchInsert">
        insert into tb_pipe_collect_data(
        id,
        sid,
        type,
        start_sid,
        end_sid,
        start_depth,
        end_depth,
        material,
        laneway,
        address,
        burytype,
        diameter,
        subtype,
        pipelength,
        depth,
        x,
        y,
        z,
        end_x,
        end_y,
        end_z,
        name,
        main_id,
        layer_id,
        create_time,
        tenant_id,
        creator,
        is_storage

        ) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.sid},
            #{element.type},
            #{element.start_sid},
            #{element.end_sid},
            #{element.start_depth},
            #{element.end_depth},
            #{element.material},
            #{element.laneway},
            #{element.address},
            #{element.burytype},
            #{element.diameter},
            #{element.subtype},
            #{element.pipelength},
            #{element.depth},
            #{element.x},
            #{element.y},
            #{element.z},
            #{element.end_x},
            #{element.end_y},
            #{element.end_z},
            #{element.name},
            #{element.mainId},
            #{element.layerId},
            #{date},
            #{tenantId},
            #{userId},
             '0'
            )
        </foreach>
    </insert>

    <select id="getList" resultMap="listResut">
        select a.*, creator.first_name as creatorName
        from tb_pipe_collect_data a
        left join tb_user creator on a.creator = creator.id
        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.mainId != null and param.mainId != ''">
                and a.main_id = #{param.mainId}
            </if>
            <if test="param.layerId != null and param.layerId != ''">
                and a.layer_id = #{param.layerId}
            </if>
            <if test="param.name != null and param.name != ''">
                and a.name like '%'||#{param.name}||'%'
            </if>
            <if test="param.start != null">
                and a.create_time &gt;= to_timestamp(#{param.start} / 1000)
            </if>
            <if test="param.end != null">
                and a.create_time &lt;= to_timestamp(#{param.end} / 1000)
            </if>
            <if test="param.creator != null and param.creator != ''">
                and a.creator like '%' || #{param.creator} || '%'
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="getLayerIdList" resultType="java.lang.String">
        select distinct layer_id from tb_pipe_collect_data where main_id = #{mainId} order by layer_id
    </select>

</mapper>