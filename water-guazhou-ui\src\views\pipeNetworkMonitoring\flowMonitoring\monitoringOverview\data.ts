import { useAppStore } from '@/store'
import { transNumberUnit } from '@/utils/GlobalHelper'

export const pointData = [
  {
    field1: '泵站',
    x: 106.404406,
    y: 29.827473,
    field2: '6.880',
    field3: '0.153',
    PH: '8.270',
    field5: '3.142',
    field6: '14.25',
    values: [
      { label: '压力', value: '0.143' },
      {
        label: '浊度',
        value: `${(Math.random() * 0.25 * 0.2 + 0.25).toFixed(3)} NTU`
      },
      {
        label: '余氧',
        value: `${(Math.random() * 0.15 * 0.2 + 0.15).toFixed(3)} mg/L`
      },
      { label: 'PH', value: (Math.random() * 7.5 * 0.1 + 7.5).toFixed(3) },
      {
        label: '电导率',
        value: `${(Math.random() * 3.14 * 0.1 + 3.142).toFixed(3)} us/cm`
      },
      {
        label: '温度',
        value: `${(Math.random() * 15 * 0.3 + 15).toFixed(2)} °C`
      }
    ]
  },
  {
    field1: '测压点',
    x: 106.4012,
    y: 29.821,
    field2: '0.380',
    field3: '0.714',
    PH: '8.183',
    field5: '1.523',
    field6: '15.24',
    values: [{ label: '压力', value: '0.343' }]
  },
  {
    field1: '测压点',
    x: 106.542,
    y: 29.621,
    field2: '5.150',
    field3: '0.215',
    PH: '8.183',
    field5: '2.956',
    field6: '20.15',
    values: [{ label: '压力', value: '0.343' }]
  },
  {
    field1: '测压点',
    x: 106.342,
    y: 29.821,
    field2: '0.397',
    field3: '0.695',
    PH: '8.116',
    field5: '1.165',
    field6: '16.64',
    values: [{ label: '压力', value: '0.343' }]
  },
  {
    field1: '测压点',
    x: 106.3923,
    y: 29.621,
    field2: '6.868',
    field3: '0.153',
    PH: '8.240',
    field5: '3.142',
    field6: '14.26',
    values: [{ label: '压力', value: '0.343' }]
  },
  {
    field1: '测流点',
    x: 106.411,
    y: 29.711,
    field2: '5.150',
    field3: '0.215',
    PH: '8.110',
    field5: '2.955',
    field6: '20.15',
    values: [{ label: '瞬时流量', value: '3456.221' }]
  },
  {
    field1: '测流点',
    x: 106.4125,
    y: 29.9125,
    field2: '0.335',
    field3: '0.714',
    PH: '8.221',
    field5: '1.523',
    field6: '15.24',
    values: [{ label: '瞬时流量', value: '3456.221' }]
  },
  {
    field1: '测流点',
    x: 106.4103,
    y: 29.7921,
    field2: '6.580',
    field3: '0.215',
    PH: '8.250',
    field5: '2.965',
    field6: '20.16',
    values: [{ label: '瞬时流量', value: '3456.221' }]
  },
  {
    field1: '测流点',
    // x: 116.21,
    x: 106.504573,
    y: 29.768543,
    // y: 39.225,
    field2: '0.397',
    field3: '0.695',
    PH: '8.310',
    field5: '1.165',
    field6: '16.64',
    values: [{ label: '瞬时流量', value: '3456.221' }]
  }
]
export const ring = (
  data: {
    name: string
    nameAlias?: string
    value: string
    valueAlias?: string
    scale: string
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数'
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g
    return num.toString().replace(reg, ',')
  }
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1
  }, 0)
  const transedTotal = transNumberUnit(total)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '')
          + params.name
          + ': '
          + Number(params.value).toFixed(percision)
          + ' '
          + unit
        )
      }
    },
    legend: {
      // selectedMode: false, // 取消图例上的点击事件
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 80,
            fontSize: 12,
            color: useAppStore().isDark ? '#fff' : '#2A2A2A'
          },
          value: {
            align: 'left',
            width: 50,
            fontSize: 12,
            color: '#00ff00'
          },
          count: {
            align: 'left',
            width: 50,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12,
            color: '#409EFF'
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            const scale = data[i].scale.substr(0, data[i].scale.length - 1)
            if (name === data[i].name) {
              return (
                '{name| '
                + (data[i].nameAlias || name)
                + '}'
                + '{value| '
                + (data[i].valueAlias || data[i].value)
                + ' '
                + (unit || '')
                + '}'
                + '{downRate| '
                + (formatNumber(Number(scale || '0').toFixed(percision)) + '%'
                  || '')
                + '}'
              )
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|'
          + title
          + ((unit && '(' + transedTotal.unit + unit + ')')
            || '(' + transedTotal.unit + ')')
          + '}\n{val|'
          + formatNumber(transedTotal.value.toFixed(percision))
          + '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: useAppStore().isDark ? '#fff' : '#2A2A2A'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: true,
        label: {
          show: false,
          formatter: params => {
            return (
              '{icon|●}{name|'
              + params.name
              + '}{value|'
              + formatNumber(Number(params.value || '0').toFixed(percision))
              + '}'
            )
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  }
  return option
}
