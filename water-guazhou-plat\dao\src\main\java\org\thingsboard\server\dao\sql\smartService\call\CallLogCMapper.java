package org.thingsboard.server.dao.sql.smartService.call;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.call.CallLogC;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface CallLogCMapper extends BaseMapper<CallLogC> {

    List<CallLogC> getList(@Param("phone") String phone, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("status") String status, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("phone") String phone, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("status") String status);

}
