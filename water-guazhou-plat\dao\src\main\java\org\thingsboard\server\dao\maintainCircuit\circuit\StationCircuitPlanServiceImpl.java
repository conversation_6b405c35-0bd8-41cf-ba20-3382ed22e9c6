package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationCircuitPlanListRequest;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitPlan;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask;
import org.thingsboard.server.dao.organization.DepartmentService;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.StationCircuitPlanMapper;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.RedisUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StationCircuitPlanServiceImpl implements StationCircuitPlanService {

    @Autowired
    private StationCircuitPlanMapper stationCircuitPlanMapper;

    @Autowired
    private StationCircuitTaskService stationCircuitTaskService;

    @Autowired
    private UserService userService;

    @Autowired
    private DepartmentService departmentService;

    @Override
    @Transactional
    public void save(StationCircuitPlan entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            // 计算结束时间
            entity.setEndTime(new Date(entity.getStartTime().getTime() + (entity.getDays() * 24 * 60 * 60 * 1000)));
            stationCircuitPlanMapper.insert(entity);

            // 生成对应的任务
            List<StationCircuitTask> taskList = StationCircuitTask.build(entity);
            for (StationCircuitTask task : taskList) {
                task.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.EXPANDING, "YH"));
            }
            stationCircuitTaskService.save(taskList);
        } else {
            stationCircuitPlanMapper.updateById(entity);
        }
    }

    @Override
    public PageData<StationCircuitPlan> findList(StationCircuitPlanListRequest request) {
        // 处理结束时间
        if (request.getEndStartTime() != null) {
            request.setEndStartTime(new Date(request.getEndStartTime().getTime() + (24 * 60 * 60 * 1000) - 1000));
        }
        if (request.getEndEndTime() != null) {
            request.setEndEndTime(new Date(request.getEndEndTime().getTime() + (24 * 60 * 60 * 1000) - 1000));
        }
        Page<StationCircuitPlan> pageRequest = new Page<>(request.getPage(), request.getSize());

        IPage<StationCircuitPlan> pageResult = stationCircuitPlanMapper.findList(pageRequest, request);
        // 查询部门
        List<Department> departmentList = departmentService.findByTenantId(request.getTenantId());
        Map<String, Department> depMap = departmentList.stream().collect(Collectors.toMap(Department::getId, user -> user));

        // 查询用户
        List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(request.getTenantId())));
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));

        List<StationCircuitPlan> records = pageResult.getRecords();
        for (StationCircuitPlan record : records) {
            // 设置部门名称
            Department auditDep = depMap.get(record.getAuditDep());
            if (auditDep != null) {
                record.setAuditDepName(auditDep.getName());
            }
            Department optionDep = depMap.get(record.getOptionDep());
            if (optionDep != null) {
                record.setOptionDepName(optionDep.getName());
            }
            // 设置人员名称
            User auditUser = userMap.get(record.getAuditUserId());
            if (auditUser != null) {
                record.setAuditUserName(auditUser.getFirstName());
            }
            User optionUser = userMap.get(record.getOptionUserId());
            if (optionUser != null) {
                record.setOptionUserName(optionUser.getFirstName());
            }
        }

        return new PageData<>(pageResult.getTotal(), records);
    }

    @Override
    public void remove(List<String> ids) {
        stationCircuitPlanMapper.deleteBatchIds(ids);
    }
}
