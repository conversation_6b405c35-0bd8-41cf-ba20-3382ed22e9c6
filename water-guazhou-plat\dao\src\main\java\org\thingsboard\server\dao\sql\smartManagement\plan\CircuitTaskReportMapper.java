package org.thingsboard.server.dao.sql.smartManagement.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportPageRequest;

import java.util.List;

@Mapper
public interface CircuitTaskReportMapper extends BaseMapper<CircuitTaskReport> {
    IPage<CircuitTaskReport> findByPage(CircuitTaskReportPageRequest request);

    boolean update(CircuitTaskReport entity);

    int saveAll(List<CircuitTaskReport> list);

    boolean present(CircuitTaskReportCompleteRequest id);

    boolean fallback(CircuitTaskReportCompleteRequest id);

    CircuitTaskReport findByPoint(@Param("taskCode") String taskCode, @Param("pointId") String pointId, @Param("tenantId") String tenantId);

    double arrivalRate();

    double feedbackRate();
}
