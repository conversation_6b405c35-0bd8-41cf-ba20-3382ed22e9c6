import{m as _,d as $,M as B,c as b,s as G,r as x,x as p,a8 as K,S as A,a9 as H,o as O,g as C,n as k,q as f,i as o,F as T,aB as U,aJ as z,h as J,G as Q,bh as S,b6 as Y,cZ as Z,c_ as j,b7 as X}from"./index-r0dFAfgr.js";/* empty css                             */import{_ as ee}from"./CardTable-rdWOL4_6.js";import{_ as te}from"./CardSearch-CB_HNR-Q.js";import{I as h}from"./common-CvK_P_ao.js";import{c as le}from"./zhandian-YaGuQZe6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function ae(a){return _({url:"/api/sp/dispatchMethod",method:"GET",params:a})}function ie(a){return _({url:"/api/sp/dispatchMethod",method:"POST",data:a})}function ne(a){return _({url:`/api/sp/dispatchMethod/${a}`,method:"delete"})}function re(a,s){return _({url:`/api/sp/dispatchMethod/switchEnabled/${a}/${s}`,method:"post"})}const L=[{label:"日常调度",value:"日常调度"},{label:"计划调度",value:"计划调度"},{label:"应急调度",value:"应急调度"}],M=[{label:"工作日",value:"工作日"},{label:"周末",value:"周末"},{label:"元旦",value:"元旦"},{label:"春节",value:"春节"},{label:"五一",value:"五一日"},{label:"端午",value:"端午末"},{label:"中秋",value:"中秋"},{label:"国庆",value:"国庆"},{label:"任意",value:"任意"}],P=[{label:"晴天",value:"晴天"},{label:"阴天",value:"阴天"},{label:"小雨",value:"小雨"},{label:"中雨",value:"中雨"},{label:"大雨",value:"大雨日"},{label:"暴雨",value:"暴雨末"},{label:"雷阵雨",value:"雷阵雨"},{label:"雷暴",value:"雷暴"},{label:"雨夹雪",value:"雨夹雪"},{label:"小雪",value:"小雪"},{label:"中雪",value:"中雪"},{label:"大雪",value:"大雪"},{label:"暴雪",value:"暴雪"}],pe=[{label:"方案名称",field:"name"},{label:"方案类型",field:"type"},{label:"日期标签",field:"dateLabel"},{label:"水厂",field:"stationName"},{label:"方案日期",field:"time",format:"x"},{label:"天气类型",field:"weatherType"},{label:"最高温度",field:"maxTemperature",unit:"°C"},{label:"最低温度",field:"minTemperature",unit:"°C"},{label:"降雨量",field:"rainfall",unit:"m³"},{label:"相对湿度",field:"relativeHumidity",unit:"%"},{label:"供水量",field:"waterSupply",unit:"m³"},{label:"耗电量",field:"powerConsumption",unit:"Kwh"},{label:"清水池液位",field:"waterLevel",unit:"m"},{label:"方案描述",field:"remark"}],oe={class:"wrapper"},ye=$({__name:"index",setup(a){const{$btnPerms:s}=B(),y=b(),E=b(),d=b(),D=b({}),q=b({filters:[{label:"方案名称",field:"name",type:"input"},{label:"方案类型",field:"type",type:"select",options:L},{label:"日期标签",field:"dateLabel",type:"select",options:M},{label:"方案时间",field:"time",type:"daterange",format:"x"},{label:"天气类型",field:"weatherType",type:"select",options:P},{type:"input",label:"供水量",field:"waterSupplyFrom"},{type:"input",label:"-",field:"waterSupplyTo",labelWidth:"2px"},{type:"input",label:"耗电量",field:"powerConsumptionFrom"},{type:"input",label:"-",field:"powerConsumptionTo",labelWidth:"2px"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:h.QUERY,click:()=>m()},{type:"default",perm:!0,text:"重置",svgIcon:G(X),click:()=>{var e;(e=d.value)==null||e.resetForm(),m()}},{perm:!0,text:"新建",icon:h.ADD,type:"success",click:()=>F()}]}]}),u=x({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"方案编号",prop:"code",minWidth:120},{label:"方案名称",prop:"name",minWidth:120},{label:"方案时间",prop:"time",minWidth:140},{label:"日期标签",prop:"dateLabel",minWidth:120},{label:"水厂",prop:"stationName",minWidth:120},{label:"天气",prop:"weatherType",minWidth:120},{label:"最高温度",prop:"maxTemperature",unit:"(°C)",minWidth:120},{label:"最低温度",prop:"minTemperature",unit:"(°C)",minWidth:120},{label:"降水量",prop:"rainfall",unit:"(mm)",minWidth:120},{label:"相对湿度",prop:"relativeHumidity",unit:"(%)",minWidth:120},{label:"供水量",prop:"waterSupply",unit:"(m³)",minWidth:120},{label:"耗电量",prop:"powerConsumption",unit:"(Kwh)",minWidth:120},{label:"清水池液位",prop:"waterLevel",unit:"(m)",minWidth:140},{label:"方案描述",prop:"remark",minWidth:120},{label:"编辑人",prop:"editUserName",minWidth:120},{label:"编辑时间",prop:"createTime",minWidth:140},{label:"是否启用",prop:"isEnabled",minWidth:120,formItemConfig:{type:"switch",onChange:(e,t)=>{if(t.isEnabledCopy!==t.isEnabled){let l="停用成功";e&&(l="启用成功"),re(t.id,e).then(()=>{t.isEnabledCopy=t.isEnabled,p.success(l)}).catch(r=>{p.warning(r)})}}}}],operationWidth:"220px",operations:[{type:"primary",text:"详情",icon:h.DETAIL,perm:s("RoleManageEdit"),click:e=>I(e)},{type:"primary",text:"编辑",icon:h.EDIT,perm:s("RoleManageEdit"),click:e=>V(e)},{type:"danger",text:"删除",icon:h.DELETE,perm:s("RoleManageEdit"),click:e=>N(e)}],dataList:[],pagination:{hide:!0}}),i=x({title:"新增",labelWidth:"100px",submitting:!1,submit:e=>{i.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),ie(e).then(()=>{var l;i.submitting=!1,(l=y.value)==null||l.closeDrawer(),p.success(t),m()}).catch(l=>{i.submitting=!1,p.warning(l)})},defaultValue:{},group:[{fields:[{text:"基础数据信息",type:"divider"},{xl:8,type:"input",label:"方案名称",field:"name",rules:[{required:!0,message:"请输入方案名称"}]},{xl:8,type:"select",label:"方案类型",field:"type",options:L,rules:[{required:!0,message:"请输入方案类型"}]},{xl:8,type:"select",label:"日期标签",field:"dateLabel",options:M,rules:[{required:!0,message:"请输入日期标签"}]},{xl:8,type:"select-tree",label:"水厂",options:K(()=>w.waterPlant),field:"stationId"},{xl:8,type:"date",label:"方案日期",field:"time",rules:[{required:!0,message:"请输入方案日期"}],format:"x"},{xl:16,type:"textarea",label:"方案描述",field:"remark",rules:[{required:!0,message:"请输入方案描述"}]},{text:"系统监测数据",type:"divider"},{xl:8,type:"select",label:"天气类型",options:P,field:"weatherType"},{xl:8,type:"input",label:"最高温度",field:"maxTemperature",append:"°C"},{xl:8,type:"input",label:"最低温度",field:"minTemperature",append:"°C"},{xl:8,type:"input-number",label:"降雨量",field:"rainfall",append:"mm"},{xl:8,type:"input-number",label:"相对湿度",field:"relativeHumidity",append:"%"},{xl:8,type:"input-number",label:"供水量",field:"waterSupply",append:"m³"},{xl:8,type:"input-number",label:"耗电量",field:"powerConsumption",append:"Kwh"},{xl:8,type:"input-number",label:"清水池液位",field:"waterLevel",append:"m"}]}]}),W=x({title:"详情",labelWidth:"100px",group:[]});function I(e){var t;W.title=e.code||"",(t=E.value)==null||t.openDrawer(),D.value=e}const F=()=>{var e;i.title="新增",i.defaultValue={},(e=y.value)==null||e.openDrawer()},V=e=>{var t;i.title="编辑",i.defaultValue={category:e.parentId,...e||{}},(t=y.value)==null||t.openDrawer()},N=e=>{A("确定删除该预案","删除提示").then(()=>{ne(e.id).then(()=>{p.success("删除成功"),m()}).catch(t=>{p.error(t.toString())})})},w=x({waterPlant:[],getwaterPlant:()=>{le("水厂").then(e=>{w.waterPlant=H(e.data.data||[])})}}),m=async()=>{var t,l,r,c;const e={size:u.pagination.limit,page:u.pagination.page,...((t=d.value)==null?void 0:t.queryParams)||{}};e.time&&((l=e.time)==null?void 0:l.length)>1&&(e.timeFrom=((r=d.value)==null?void 0:r.queryParams).time[0]||"",e.timeTo=((c=d.value)==null?void 0:c.queryParams).time[1]||""),delete e.time,ae(e).then(g=>{const v=g.data.data.data||[];v.map(n=>n.isEnabledCopy=n.isEnabled),u.dataList=v,u.pagination.total=g.data.data.total||0})};return O(async()=>{m(),w.getwaterPlant()}),(e,t)=>{const l=te,r=ee,c=Y,g=Z,v=j;return C(),k("div",oe,[f(l,{ref_key:"refSearch",ref:d,config:o(q)},null,8,["config"]),f(r,{class:"card-table",config:o(u)},null,8,["config"]),f(c,{ref_key:"refForm",ref:y,config:o(i)},null,8,["config"]),f(c,{ref_key:"refDetail",ref:E,config:o(W)},{default:T(()=>[f(v,{title:" ",column:3,border:!0},{default:T(()=>[(C(!0),k(U,null,z(o(pe),(n,R)=>(C(),J(g,{key:R,label:n.label+" :"},{default:T(()=>[Q(S(o(D)[n.field]||"")+" "+S(n.unit?"("+n.unit+")":""),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1},8,["config"])])}}});export{ye as default};
