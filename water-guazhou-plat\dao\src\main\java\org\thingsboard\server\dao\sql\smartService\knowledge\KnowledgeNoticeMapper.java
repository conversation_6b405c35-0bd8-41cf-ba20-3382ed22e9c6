package org.thingsboard.server.dao.sql.smartService.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface KnowledgeNoticeMapper extends BaseMapper<KnowledgeNotice> {

    List<KnowledgeNotice> getList(@Param("type") String type, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("type") String type, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("tenantId") String tenantId);

    List<KnowledgeNotice> getAll(String tenantId);
}
