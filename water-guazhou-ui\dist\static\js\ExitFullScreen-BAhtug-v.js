import{d as r,c as i,am as p,o as d,bB as u,g as m,n as f,q as x,i as l,cs as _,aw as k,C as v}from"./index-r0dFAfgr.js";const B=r({__name:"ExitFullScreen",props:{collapsed:{type:Boolean}},emits:["fullscreen"],setup(n,{emit:a}){const c=a,o=n,e=i(!1);return p(()=>o.collapsed,()=>{e.value=!!document.fullscreenElement}),d(async()=>{await u()}),(t,s)=>(m(),f("div",{class:k(["exit-fullscreen",[t.collapsed?"collapsed":""]]),onClick:s[0]||(s[0]=C=>c("fullscreen"))},[x(l(_),{icon:l(e)?"mdi:exit-to-app":"mdi:fullscreen"},null,8,["icon"])],2))}}),w=v(B,[["__scopeId","data-v-4d3c3dcc"]]);export{w as default};
