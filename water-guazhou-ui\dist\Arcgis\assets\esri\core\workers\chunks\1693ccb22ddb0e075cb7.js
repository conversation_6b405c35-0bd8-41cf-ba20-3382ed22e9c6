"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7269,7035,7541],{41123:(e,t,a)=>{a.d(t,{D:()=>s});const r="randomUUID"in crypto;function s(){if(r)return crypto.randomUUID();const e=crypto.getRandomValues(new Uint16Array(8));e[3]=4095&e[3]|16384,e[4]=16383&e[4]|32768;const t=t=>e[t].toString(16).padStart(4,"0");return t(0)+t(1)+"-"+t(2)+"-"+t(3)+"-"+t(4)+"-"+t(5)+t(6)+t(7)}},16306:(e,t,a)=>{a.d(t,{aX:()=>A});var r=a(68773),s=a(20102),n=a(92604),i=a(70586),o=a(38913),l=a(58901),d=a(73913),u=a(8744),p=a(40488),c=(a(66577),a(3172)),f=a(33955),h=a(11282),y=a(17452);async function m(e,t,a){const r="string"==typeof e?(0,y.mN)(e):e,s=t[0].spatialReference,n=(0,f.Ji)(t[0]),i={...a,query:{...r.query,f:"json",sr:s.wkid?s.wkid:JSON.stringify(s),geometries:JSON.stringify((l=t,{geometryType:(0,f.Ji)(l[0]),geometries:l.map((e=>e.toJSON()))}))}},{data:o}=await(0,c.default)(r.path+"/simplify",i);var l;return function(e,t,a){const r=(0,f.q9)(t);return e.map((e=>{const t=r.fromJSON(e);return t.spatialReference=a,t}))}(o.geometries,n,s)}const g=n.Z.getLogger("esri.geometry.support.normalizeUtils");function b(e){return"polygon"===e[0].type}function w(e){return"polyline"===e[0].type}function F(e,t,a){if(t){const t=function(e,t){if(!(e instanceof l.Z||e instanceof o.Z)){const e="straightLineDensify: the input geometry is neither polyline nor polygon";throw g.error(e),new s.Z(e)}const a=(0,d.x3)(e),r=[];for(const e of a){const a=[];r.push(a),a.push([e[0][0],e[0][1]]);for(let r=0;r<e.length-1;r++){const s=e[r][0],n=e[r][1],i=e[r+1][0],o=e[r+1][1],l=Math.sqrt((i-s)*(i-s)+(o-n)*(o-n)),d=(o-n)/l,u=(i-s)/l,p=l/t;if(p>1){for(let e=1;e<=p-1;e++){const r=e*t,i=u*r+s,o=d*r+n;a.push([i,o])}const e=(l+Math.floor(p-1)*t)/2,r=u*e+s,i=d*e+n;a.push([r,i])}a.push([i,o])}}return function(e){return"polygon"===e.type}(e)?new o.Z({rings:r,spatialReference:e.spatialReference}):new l.Z({paths:r,spatialReference:e.spatialReference})}(e,1e6);e=(0,p.Sx)(t,!0)}return a&&(e=(0,d.Sy)(e,a)),e}function I(e,t,a){if(Array.isArray(e)){const r=e[0];if(r>t){const a=(0,d.XZ)(r,t);e[0]=r+a*(-2*t)}else if(r<a){const t=(0,d.XZ)(r,a);e[0]=r+t*(-2*a)}}else{const r=e.x;if(r>t){const a=(0,d.XZ)(r,t);e=e.clone().offset(a*(-2*t),0)}else if(r<a){const t=(0,d.XZ)(r,a);e=e.clone().offset(t*(-2*a),0)}}return e}function Z(e,t){let a=-1;for(let r=0;r<t.cutIndexes.length;r++){const s=t.cutIndexes[r],n=t.geometries[r],i=(0,d.x3)(n);for(let e=0;e<i.length;e++){const t=i[e];t.some((a=>{if(a[0]<180)return!0;{let a=0;for(let e=0;e<t.length;e++){const r=t[e][0];a=r>a?r:a}a=Number(a.toFixed(9));const r=-360*(0,d.XZ)(a,180);for(let a=0;a<t.length;a++){const t=n.getPoint(e,a);n.setPoint(e,a,t.clone().offset(r,0))}return!0}}))}if(s===a){if(b(e))for(const t of(0,d.x3)(n))e[s]=e[s].addRing(t);else if(w(e))for(const t of(0,d.x3)(n))e[s]=e[s].addPath(t)}else a=s,e[s]=n}return e}async function A(e,t,a){if(!Array.isArray(e))return A([e],t);t&&"string"!=typeof t&&g.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const s="string"==typeof t?t:t?.url??r.Z.geometryServiceUrl;let n,y,b,w,S,v,R,x,E=0;const $=[],O=[];for(const t of e)if((0,i.Wi)(t))O.push(t);else if(n||(n=t.spatialReference,y=(0,u.C5)(n),b=n.isWebMercator,v=b?102100:4326,w=d.UZ[v].maxX,S=d.UZ[v].minX,R=d.UZ[v].plus180Line,x=d.UZ[v].minus180Line),y)if("mesh"===t.type)O.push(t);else if("point"===t.type)O.push(I(t.clone(),w,S));else if("multipoint"===t.type){const e=t.clone();e.points=e.points.map((e=>I(e,w,S))),O.push(e)}else if("extent"===t.type){const e=t.clone()._normalize(!1,!1,y);O.push(e.rings?new o.Z(e):e)}else if(t.extent){const e=t.extent,a=(0,d.XZ)(e.xmin,S)*(2*w);let r=0===a?t.clone():(0,d.Sy)(t.clone(),a);e.offset(a,0),e.intersects(R)&&e.xmax!==w?(E=e.xmax>E?e.xmax:E,r=F(r,b),$.push(r),O.push("cut")):e.intersects(x)&&e.xmin!==S?(E=e.xmax*(2*w)>E?e.xmax*(2*w):E,r=F(r,b,360),$.push(r),O.push("cut")):O.push(r)}else O.push(t.clone());else O.push(t);let U=(0,d.XZ)(E,w),L=-90;const T=U,C=new l.Z;for(;U>0;){const e=360*U-180;C.addPath([[e,L],[e,-1*L]]),L*=-1,U--}if($.length>0&&T>0){const t=Z($,await async function(e,t,a,r){const s=(0,h.en)(e),n=t[0].spatialReference,i={...r,query:{...s.query,f:"json",sr:JSON.stringify(n),target:JSON.stringify({geometryType:(0,f.Ji)(t[0]),geometries:t}),cutter:JSON.stringify(a)}},o=await(0,c.default)(s.path+"/cut",i),{cutIndexes:l,geometries:d=[]}=o.data;return{cutIndexes:l,geometries:d.map((e=>{const t=(0,f.im)(e);return t.spatialReference=n,t}))}}(s,$,C,a)),r=[],n=[];for(let a=0;a<O.length;a++){const s=O[a];if("cut"!==s)n.push(s);else{const s=t.shift(),o=e[a];(0,i.pC)(o)&&"polygon"===o.type&&o.rings&&o.rings.length>1&&s.rings.length>=o.rings.length?(r.push(s),n.push("simplify")):n.push(b?(0,p.$)(s):s)}}if(!r.length)return n;const o=await m(s,r,a),l=[];for(let e=0;e<n.length;e++){const t=n[e];"simplify"!==t?l.push(t):l.push(b?(0,p.$)(o.shift()):o.shift())}return l}const J=[];for(let e=0;e<O.length;e++){const t=O[e];if("cut"!==t)J.push(t);else{const e=$.shift();J.push(!0===b?(0,p.$)(e):e)}}return J}},73913:(e,t,a)=>{a.d(t,{Sy:()=>l,UZ:()=>i,XZ:()=>o,x3:()=>d});var r=a(58901),s=a(82971),n=a(33955);const i={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new r.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator}),minus180Line:new r.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new r.Z({paths:[[[180,-180],[180,180]]],spatialReference:s.Z.WGS84}),minus180Line:new r.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:s.Z.WGS84})}};function o(e,t){return Math.ceil((e-t)/(2*t))}function l(e,t){const a=d(e);for(const e of a)for(const a of e)a[0]+=t;return e}function d(e){return(0,n.oU)(e)?e.rings:e.paths}},3388:(e,t,a)=>{var r;a.d(t,{i:()=>r}),function(e){e[e.PROJECT_VERTICES=1]="PROJECT_VERTICES"}(r||(r={}))},87269:(e,t,a)=>{a.r(t),a.d(t,{applyEdits:()=>y});var r,s=a(38171),n=a(46791),i=a(20102),o=a(22974),l=a(70586),d=a(17452),u=a(41123),p=a(16306),c=a(3388),f=a(66361);!function(e){e.GLTF_BINARY="3D_glb",e.GLTF_JSON="3D_gltf"}(r||(r={}));var h=a(84230);async function y(e,t,a,s={}){let d,u;const p={edits:a,result:new Promise(((e,t)=>{d=e,u=t}))};e.emit("apply-edits",p);try{const{results:u,edits:p}=await async function(e,t,a,s){if(await e.load(),!function(e){return e&&null!=e.applyEdits}(t))throw new i.Z(`${e.type}-layer:no-editing-support`,"Layer source does not support applyEdits capability",{layer:e});if(!(0,h.ln)(e))throw new i.Z(`${e.type}-layer:editing-disabled`,"Editing is disabled for layer",{layer:e});const{edits:o,options:d}=await async function(e,t,a){const s=t&&(t.addFeatures||t.updateFeatures||t.deleteFeatures),o=t&&(t.addAttachments||t.updateAttachments||t.deleteAttachments),d=(0,l.pC)(e.infoFor3D);if(!t||!s&&!o)throw new i.Z(`${e.type}-layer:missing-parameters`,"'addFeatures', 'updateFeatures', 'deleteFeatures', 'addAttachments', 'updateAttachments' or 'deleteAttachments' parameter is required");const u=(0,h.S1)(e);if(!u.data.isVersioned&&a?.gdbVersion)throw new i.Z(`${e.type}-layer:invalid-parameter`,"'gdbVersion' is applicable only if the layer supports versioned data. See: 'capabilities.data.isVersioned'");if(!u.editing.supportsRollbackOnFailure&&a?.rollbackOnFailureEnabled)throw new i.Z(`${e.type}-layer:invalid-parameter`,"This layer does not support 'rollbackOnFailureEnabled' parameter. See: 'capabilities.editing.supportsRollbackOnFailure'");if(!u.editing.supportsGlobalId&&a?.globalIdUsed)throw new i.Z(`${e.type}-layer:invalid-parameter`,"This layer does not support 'globalIdUsed' parameter. See: 'capabilities.editing.supportsGlobalId'");if(!u.editing.supportsGlobalId&&o)throw new i.Z(`${e.type}-layer:invalid-parameter`,"'addAttachments', 'updateAttachments' and 'deleteAttachments' are applicable only if the layer supports global ids. See: 'capabilities.editing.supportsGlobalId'");if(!a?.globalIdUsed&&o)throw new i.Z(`${e.type}-layer:invalid-parameter`,"When 'addAttachments', 'updateAttachments' or 'deleteAttachments' is specified, globalIdUsed should be set to true");const p={...a};if(null!=p.rollbackOnFailureEnabled||u.editing.supportsRollbackOnFailure||(p.rollbackOnFailureEnabled=!0),!1===p.rollbackOnFailureEnabled&&"original-and-current-features"===p.returnServiceEditsOption)throw new i.Z(`${e.type}-layer:invalid-parameter`,"'original-and-current-features' is valid for 'returnServiceEditsOption' only when 'rollBackOnFailure' is true.");if(!u.editing.supportsReturnServiceEditsInSourceSpatialReference&&p.returnServiceEditsInSourceSR)throw new i.Z(`${e.type}-layer:invalid-parameter`,"This layer does not support 'returnServiceEditsInSourceSR' parameter. See: 'capabilities.editing.supportsReturnServiceEditsInSourceSpatialReference'");if(p.returnServiceEditsInSourceSR&&"original-and-current-features"!==p.returnServiceEditsOption)throw new i.Z(`${e.type}-layer:invalid-parameter`,"'returnServiceEditsInSourceSR' is valid only when 'returnServiceEditsOption' is set to 'original-and-current-features'");const c={...t};if(c.addFeatures=t&&n.Z.isCollection(t.addFeatures)?t.addFeatures.toArray():c.addFeatures||[],c.updateFeatures=t&&n.Z.isCollection(t.updateFeatures)?t.updateFeatures.toArray():c.updateFeatures||[],c.deleteFeatures=t&&n.Z.isCollection(t.deleteFeatures)?t.deleteFeatures.toArray():c.deleteFeatures||[],c.addFeatures.length&&!u.operations.supportsAdd)throw new i.Z(`${e.type}-layer:unsupported-operation`,"Layer does not support adding features.");if(c.updateFeatures.length&&!u.operations.supportsUpdate)throw new i.Z(`${e.type}-layer:unsupported-operation`,"Layer does not support updating features.");if(c.deleteFeatures.length&&!u.operations.supportsDelete)throw new i.Z(`${e.type}-layer:unsupported-operation`,"Layer does not support deleting features.");c.addAttachments=c.addAttachments||[],c.updateAttachments=c.updateAttachments||[],c.deleteAttachments=c.deleteAttachments||[],c.addFeatures=c.addFeatures.map(w),c.updateFeatures=c.updateFeatures.map(w),c.addAssets=[];const f=a?.globalIdUsed||d;return c.addFeatures.forEach((t=>function(e,t,a){m(e,t,a)}(t,e,f))),c.updateFeatures.forEach((t=>function(e,t,a){m(e,t,a);const r=(0,h.S1)(t);if("geometry"in e&&(0,l.pC)(e.geometry)&&!r?.editing.supportsGeometryUpdate)throw new i.Z(`${t.type}-layer:unsupported-operation`,"Layer does not support geometry updates.")}(t,e,f))),c.deleteFeatures.forEach((t=>function(e,t,a){m(e,t,a)}(t,e,f))),c.addAttachments.forEach((t=>g(t,e))),c.updateAttachments.forEach((t=>g(t,e))),d&&await async function(e,t){if((0,l.Wi)(t.infoFor3D))return;const{infoFor3D:a}=t;let s=!1;for(const e of a.editFormats)if(e===r.GLTF_BINARY){s=!0;break}const n=[];for(const a of e.addFeatures??[])n.push(F(a,e,t,s));for(const a of e.updateFeatures??[])n.push(F(a,e,t,s));const i=await Promise.allSettled(n);for(const e of i)if("rejected"===e.status)throw e.reason}(c,e),{edits:await b(c),options:p}}(e,a,s);return o.addFeatures?.length||o.updateFeatures?.length||o.deleteFeatures?.length||o.addAttachments?.length||o.updateAttachments?.length||o.deleteAttachments?.length?{edits:o,results:await t.applyEdits(o,d)}:{edits:o,results:{addFeatureResults:[],updateFeatureResults:[],deleteFeatureResults:[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]}}}(e,t,a,s),c=e=>e.filter((e=>!e.error)).map(o.d9),y={edits:p,addedFeatures:c(u.addFeatureResults),updatedFeatures:c(u.updateFeatureResults),deletedFeatures:c(u.deleteFeatureResults),addedAttachments:c(u.addAttachmentResults),updatedAttachments:c(u.updateAttachmentResults),deletedAttachments:c(u.deleteAttachmentResults),exceededTransferLimit:!1};return u.editedFeatureResults?.length&&(y.editedFeatures=u.editedFeatureResults),(y.addedFeatures.length||y.updatedFeatures.length||y.deletedFeatures.length||y.addedAttachments.length||y.updatedAttachments.length||y.deletedAttachments.length)&&(e.emit("edits",y),(0,f.lQ)(e)&&f.dU.emit("edits",{layer:e,event:y})),d(y),u}catch(e){throw u(e),e}}function m(e,t,a){if(a){if("attributes"in e&&!e.attributes[t.globalIdField])throw new i.Z(`${t.type}-layer:invalid-parameter`,"Feature should have 'globalId' when 'globalIdUsed' is true");if(!("attributes"in e)&&!e.globalId)throw new i.Z(`${t.type}-layer:invalid-parameter`,"'globalId' of the feature should be passed when 'globalIdUsed' is true")}if("geometry"in e&&(0,l.pC)(e.geometry)){if(e.geometry.hasZ&&!1===t.capabilities?.data.supportsZ)throw new i.Z(`${t.type}-layer:z-unsupported`,"Layer does not support z values while feature has z values.");if(e.geometry.hasM&&!1===t.capabilities?.data.supportsM)throw new i.Z(`${t.type}-layer:m-unsupported`,"Layer does not support m values while feature has m values.")}}function g(e,t){const{feature:a,attachment:r}=e;if(!a||"attributes"in a&&!a.attributes[t.globalIdField])throw new i.Z(`${t.type}-layer:invalid-parameter`,"Attachment should have reference to a feature with 'globalId'");if(!("attributes"in a)&&!a.globalId)throw new i.Z(`${t.type}-layer:invalid-parameter`,"Attachment should have reference to 'globalId' of the parent feature");if(!r.globalId)throw new i.Z(`${t.type}-layer:invalid-parameter`,"Attachment should have 'globalId'");if(!r.data&&!r.uploadId)throw new i.Z(`${t.type}-layer:invalid-parameter`,"Attachment should have 'data' or 'uploadId'");if(!(r.data instanceof File&&r.data.name||r.name))throw new i.Z(`${t.type}-layer:invalid-parameter`,"'name' is required when attachment is specified as Base64 encoded string using 'data'");if(!t.capabilities?.editing.supportsUploadWithItemId&&r.uploadId)throw new i.Z(`${t.type}-layer:invalid-parameter`,"This layer does not support 'uploadId' parameter. See: 'capabilities.editing.supportsUploadWithItemId'");if("string"==typeof r.data){const e=(0,d.sJ)(r.data);if(e&&!e.isBase64)throw new i.Z(`${t.type}-layer:invalid-parameter`,"Attachment 'data' should be a Blob, File or Base64 encoded string")}}async function b(e){const t=e.addFeatures??[],a=e.updateFeatures??[],r=t.concat(a).map((e=>e.geometry)),s=await(0,p.aX)(r),n=t.length,i=a.length;return s.slice(0,n).forEach(((e,a)=>t[a].geometry=e)),s.slice(n,n+i).forEach(((e,t)=>a[t].geometry=e)),e}function w(e){const t=new s.Z;return e.attributes||(e.attributes={}),t.geometry=e.geometry,t.attributes=e.attributes,t}async function F(e,t,a,s){if((0,l.Wi)(e.geometry)||"mesh"!==e.geometry.type)return;const n=e.geometry,o=a.globalIdField;if((0,l.pC)(a.parsedUrl)&&(0,l.pC)(n.external)&&Array.isArray(n.external.source)&&1===n.external.source.length&&"source"in n.external.source[0]&&"string"==typeof n.external.source[0].source&&n.external.source[0].source.startsWith(a.parsedUrl.path))return;if(!s)throw new i.Z(`${a.type}-layer:binary-gltf-asset-not-supported`,"3DObjectFeatureLayer requires binary glTF (.glb) support for updating mesh geometry.");const d=await n.toBinaryGLTF({ignoreLocalTransform:!0}),p=await d.buffer(),f=`{${(0,u.D)()}}`,h=`${f}.glb`;t.addAssets?.push({featureGlobalId:e.getAttribute(o),assetMapGlobalId:f,assetName:h,flags:(0,l.pC)(n.transform)&&n.transform.geographic?c.i.PROJECT_VERTICES:0,data:p.data,mimeType:p.type,assetType:r.GLTF_BINARY,feature:e})}},66361:(e,t,a)=>{a.d(t,{dU:()=>o,lQ:()=>u,o1:()=>p});var r=a(43697),s=a(32448),n=a(22974),i=(a(92604),a(75215),a(20102),a(80442),a(52011));const o=new s.Z.EventEmitter,l="esri.layers.mixins.EditBusLayer",d=Symbol(l);function u(e){return null!=e&&"object"==typeof e&&d in e}const p=e=>{var t;let a=class extends e{constructor(...e){super(...e),this[t]=!0,this.when().then((()=>{this.own([o.on("edits",(e=>{const t="layer"in e?e.layer:null,a="layer"in e?e.layer?.url:e.serviceUrl,r="layer"in e?e.layer?.layerId:e.layerId,s=e.event;if(t===this||a!==this.url)return;if(null!=r&&null!=this.layerId&&r===this.layerId)return void this.emit("edits",(0,n.d9)(s));const i=s.editedFeatures?.find((({layerId:e})=>e===this.layerId));if(i){const{adds:e,updates:t,deletes:a}=i.editedFeatures,r={edits:null,addedAttachments:[],deletedAttachments:[],updatedAttachments:[],addedFeatures:e?e.map((({attributes:e})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],deletedFeatures:a?a.map((({attributes:e})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],updatedFeatures:t?t.map((({current:{attributes:e}})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],editedFeatures:(0,n.d9)(s.editedFeatures),exceededTransferLimit:!1};this.emit("edits",r)}}))])}),(()=>{}))}};return t=d,a=(0,r._)([(0,i.j)(l)],a),a}},11282:(e,t,a)=>{a.d(t,{cv:()=>o,en:()=>i,lA:()=>n}),a(68773),a(40330);var r=a(22974),s=a(17452);function n(e,t){return t?{...t,query:{...e??{},...t.query}}:{query:e}}function i(e){return"string"==typeof e?(0,s.mN)(e):(0,r.d9)(e)}function o(e,t,a){const r={};for(const s in e){if("declaredClass"===s)continue;const n=e[s];if(null!=n&&"function"!=typeof n)if(Array.isArray(n)){r[s]=[];for(let e=0;e<n.length;e++)r[s][e]=o(n[e])}else if("object"==typeof n)if(n.toJSON){const e=n.toJSON(a&&a[s]);r[s]=t?e:JSON.stringify(e)}else r[s]=t?n:JSON.stringify(n);else r[s]=n}return r}a(71058)}}]);