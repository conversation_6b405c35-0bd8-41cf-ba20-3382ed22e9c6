import{d as X,c as h,r as R,am as q,bB as Y,x as u,g as m,h as k,F as a,p,q as e,G as U,bh as Z,an as B,n as L,i as O,d6 as ee,ak as oe,dr as le,H as te,I as ae,bU as ne,bW as de,aK as se,aL as ie,N as re,cE as ue,J as me,O as pe,K as ce,L as fe,C as ye}from"./index-r0dFAfgr.js";/* empty css                         */import{a as ge,u as _e,b as Ve}from"./circuitSettings-CHqJCF5w.js";const be={key:0,class:"loading-skeleton"},Ce={class:"form-section"},ve={class:"form-section"},we={class:"table-container"},he={key:0,class:"add-row"},ke={class:"dialog-footer"},Ie=X({__name:"InspectionConfigDialog",props:{modelValue:{type:Boolean,default:!1},editId:{default:""},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup($,{emit:J}){const s=$,D=J,c=h(!1),y=h(!1),g=h(!1),_=h(),l=R({name:"",code:"",type:"",status:"0",formConfig:[]}),d=R({location:"",position:"",content:""}),K={name:[{required:!0,message:"请输入配置名称",trigger:"blur"}],code:[{required:!0,message:"请输入配置编码",trigger:"blur"}],type:[{required:!0,message:"请选择配置类型",trigger:"change"}]};q(()=>s.modelValue,async o=>{c.value=o,o&&(s.editId?await W():S())}),q(c,o=>{D("update:modelValue",o)});const z=()=>s.readonly?"查看巡检配置":s.editId?"编辑巡检配置":"新建巡检配置",S=()=>{l.name="",l.code="",l.type="",l.status="0",l.formConfig=[],N(),Y(()=>{var o;(o=_.value)==null||o.clearValidate()})},N=()=>{d.location="",d.position="",d.content=""},G=o=>{s.editId||(l.formConfig=[])},H=()=>{if(!d.location||!d.position||!d.content){u.warning("请填写完整信息");return}l.formConfig.push({location:d.location,position:d.position,content:d.content}),N()},M=o=>{l.formConfig.splice(o,1)},W=async()=>{if(s.editId)try{y.value=!0;const o=await ge(s.editId);if(o!=null&&o.data){const t=o.data.data||o.data;if(l.name=t.name||"",l.code=t.code||"",l.type=t.type||"",l.status=t.status||"0",t.formConfig)try{l.formConfig=JSON.parse(t.formConfig)}catch(E){console.error("解析表单配置失败:",E),l.formConfig=[]}else l.formConfig=[]}}catch(o){console.error(o),u.error("获取数据失败")}finally{y.value=!1}},j=async()=>{if(_.value)try{if(await _.value.validate(),l.formConfig.length===0){u.warning("请至少添加一项表单配置");return}g.value=!0;const o={name:l.name,code:l.code,type:l.type,status:l.status,formConfig:JSON.stringify(l.formConfig)};s.editId?(await _e(s.editId,o),u.success("更新成功")):(await Ve(o),u.success("创建成功")),D("success"),I()}catch(o){console.error(o),u.error(s.editId?"更新失败":"创建失败")}finally{g.value=!1}},I=()=>{c.value=!1,y.value=!1,g.value=!1,S()};return(o,t)=>{const E=le,i=te,V=ae,b=ne,x=de,f=se,F=ie,C=re,T=ue,v=me,A=pe,P=ce,Q=fe;return m(),k(Q,{modelValue:c.value,"onUpdate:modelValue":t[7]||(t[7]=n=>c.value=n),title:z(),width:"800px","before-close":I},{footer:a(()=>[p("span",ke,[e(v,{onClick:I},{default:a(()=>[U(Z(o.readonly?"关闭":"取消"),1)]),_:1}),o.readonly?B("",!0):(m(),k(v,{key:0,type:"primary",onClick:j,loading:g.value},{default:a(()=>t[11]||(t[11]=[U(" 保存 ")])),_:1},8,["loading"]))])]),default:a(()=>[y.value?(m(),L("div",be,[e(E,{rows:4,animated:""})])):(m(),k(P,{key:1,ref_key:"formRef",ref:_,model:l,rules:K,"label-width":"100px"},{default:a(()=>[p("div",Ce,[t[8]||(t[8]=p("h4",{class:"section-title"},"基本信息",-1)),e(x,{gutter:20},{default:a(()=>[e(b,{span:12},{default:a(()=>[e(V,{label:"配置名称",prop:"name"},{default:a(()=>[e(i,{modelValue:l.name,"onUpdate:modelValue":t[0]||(t[0]=n=>l.name=n),placeholder:"请输入配置名称",readonly:o.readonly},null,8,["modelValue","readonly"])]),_:1})]),_:1}),e(b,{span:12},{default:a(()=>[e(V,{label:"配置编码",prop:"code"},{default:a(()=>[e(i,{modelValue:l.code,"onUpdate:modelValue":t[1]||(t[1]=n=>l.code=n),placeholder:"请输入配置编码",readonly:o.readonly},null,8,["modelValue","readonly"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20},{default:a(()=>[e(b,{span:12},{default:a(()=>[e(V,{label:"配置类型",prop:"type"},{default:a(()=>[e(F,{modelValue:l.type,"onUpdate:modelValue":t[2]||(t[2]=n=>l.type=n),placeholder:"请选择",style:{width:"100%"},disabled:o.readonly,onChange:G},{default:a(()=>[e(f,{label:"管网",value:"0"}),e(f,{label:"泵站",value:"1"}),e(f,{label:"其他",value:"2"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(b,{span:12},{default:a(()=>[e(V,{label:"状态",prop:"status"},{default:a(()=>[e(F,{modelValue:l.status,"onUpdate:modelValue":t[3]||(t[3]=n=>l.status=n),placeholder:"请选择",style:{width:"100%"},disabled:o.readonly},{default:a(()=>[e(f,{label:"启用",value:"0"}),e(f,{label:"停用",value:"1"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),p("div",ve,[t[10]||(t[10]=p("h4",{class:"section-title"},"表单配置",-1)),p("div",we,[e(A,{data:l.formConfig,border:"",style:{width:"100%"}},{default:a(()=>[e(C,{prop:"location",label:"巡检地点",width:"150"},{default:a(({row:n,$index:w})=>[e(i,{modelValue:n.location,"onUpdate:modelValue":r=>n.location=r,placeholder:"请输入巡检地点",readonly:o.readonly},null,8,["modelValue","onUpdate:modelValue","readonly"])]),_:1}),e(C,{prop:"position",label:"巡检位置",width:"120"},{default:a(({row:n,$index:w})=>[e(i,{modelValue:n.position,"onUpdate:modelValue":r=>n.position=r,placeholder:"请输入巡检位置",readonly:o.readonly},null,8,["modelValue","onUpdate:modelValue","readonly"])]),_:1}),e(C,{prop:"content",label:"检验内容","min-width":"200"},{default:a(({row:n,$index:w})=>[e(i,{modelValue:n.content,"onUpdate:modelValue":r=>n.content=r,placeholder:"请输入检验内容",readonly:o.readonly},null,8,["modelValue","onUpdate:modelValue","readonly"])]),_:1}),o.readonly?B("",!0):(m(),k(C,{key:0,label:"操作",width:"80"},{default:a(({row:n,$index:w})=>[e(v,{type:"danger",size:"small",circle:"",onClick:r=>M(w)},{default:a(()=>[e(T,null,{default:a(()=>[e(O(ee))]),_:1})]),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),o.readonly?B("",!0):(m(),L("div",he,[e(i,{modelValue:d.location,"onUpdate:modelValue":t[4]||(t[4]=n=>d.location=n),placeholder:"请输入巡检地点",style:{width:"150px","margin-right":"10px"}},null,8,["modelValue"]),e(i,{modelValue:d.position,"onUpdate:modelValue":t[5]||(t[5]=n=>d.position=n),placeholder:"请输入巡检位置",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue"]),e(i,{modelValue:d.content,"onUpdate:modelValue":t[6]||(t[6]=n=>d.content=n),placeholder:"请输入检验内容",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue"]),e(v,{type:"primary",onClick:H},{default:a(()=>[e(T,null,{default:a(()=>[e(O(oe))]),_:1}),t[9]||(t[9]=U(" 增加一行 "))]),_:1})]))])])]),_:1},8,["model"]))]),_:1},8,["modelValue","title"])}}}),De=ye(Ie,[["__scopeId","data-v-c18548df"]]);export{De as default};
