package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;

import java.util.List;

public interface GisPeopleService {

    /**
     * 查询指定类型的人员列表
     *
     * @param type     人员类型
     * @param tenantId 租户ID
     * @return 数据
     */
    List<User> findList(String type, TenantId tenantId) throws ThingsboardException;
}
