package org.thingsboard.server.dao.fileupload;

import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsFileEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-09-14
 */
public interface FileService {
    Map uploadImage(MultipartFile file, String projectId);

    Map getImgByName(String fileName, String projectId, HttpServletResponse response);

    void deleteImgByNameAndProjectId(String name, String projectId);

    AssetsFileEntity uploadFile(MultipartFile file, String projectId, String tenantId) throws ThingsboardException;

    Map viewFile(File pdfFile, HttpServletResponse response);

    void deleteByIds(String[] ids);
}
