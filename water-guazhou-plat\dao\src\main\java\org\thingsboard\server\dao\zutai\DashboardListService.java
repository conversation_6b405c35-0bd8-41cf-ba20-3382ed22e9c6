package org.thingsboard.server.dao.zutai;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.zutai.DashboardListEntity;

import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
public interface DashboardListService {

    PageData getList(Map map);

    DashboardListEntity save(DashboardListEntity dashboardListEntity);

    void delete(String id);
}
