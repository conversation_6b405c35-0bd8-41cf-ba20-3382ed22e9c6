package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectOperateRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordSaveRequest;

@Service
public class SoProjectOperateRecordServiceImpl implements SoProjectOperateRecordService {
    @Autowired
    private SoProjectOperateRecordMapper mapper;

    @Override
    public IPage<SoProjectOperateRecord> findAllConditional(SoProjectOperateRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoProjectOperateRecord save(SoProjectOperateRecordSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SoProjectOperateRecord entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public SoProjectOperateRecord recordCreate(SaveRequest<?> deliverEntity, String constructionCode, SoGeneralSystemJournal journal) {
        return recordCreate(deliverEntity.tenantId(), deliverEntity.currentUserUUID(), constructionCode, journal);
    }

    @Override
    public SoProjectOperateRecord recordCreate(String tenantId, String userId, String constructionCode, SoGeneralSystemJournal journal) {
        if (journal == null || journal.getOnCreateJournal().disabled())
            return null;

        return save(SoProjectOperateRecordSaveRequest.of(
                tenantId, userId, constructionCode,
                journal.getOnCreateJournal().getPattern(),
                journal.getOnCreateJournal().getRemark()));
    }

    @Override
    public SoProjectOperateRecord recordComplete(String tenantId, String userId, String constructionCode, SoGeneralSystemJournal journal) {
        if (journal == null || journal.getOnCompleteJournal().disabled())
            return null;

        return save(SoProjectOperateRecordSaveRequest.of(
                tenantId, userId, constructionCode,
                journal.getOnCompleteJournal().getPattern(),
                journal.getOnCompleteJournal().getRemark()));
    }
}
