server:
  port: 8081
eureka:
  instance:
    hostname: 127.0.0.1
  client:
    fetch-registry: true
    register-with-eureka: true
    service-url:
      defaultZone: *********************************/eureka/
  dashboard:
    enabled: true
ribbon:
  ReadTimeout: 3000000
  ConnectTimeout: 3000000
  eureka:
    enabled: on
spring:
#  security:
#    basic:
#      enable: true
#    user:
#      name: root
#      password: 123456
  application:
    name: zuul-server
  servlet:
    multipart:
      max-file-size: 99999999999
      max-request-size: 99999999999
zuul:
#  prefix: /V2Api
#  strip-prefix: true
#  servlet-path: /
  routes:
    api-upload:
      ##文件上传服务
      path: /file/**
      serviceId: upload-service
      strip-prefix: true
    api-istar:
      ## 物联网服务
      path: /istar/**
      serviceId: istar-service
      strip-prefix: true
    api-iot:
      ## IOT 平台服务
      path: /api/**
      serviceId: base-service
      strip-prefix: false
  host:
    socket-timeout-millis: 60000
    connect-timeout-millis: 120000
    max-total-connections: 500
  #需要忽略的头部信息，不在传播到其他服务
  sensitive-headers: Access-Control-Allow-Origin
  ignored-headers: Access-Control-Allow-Origin,H-APP-Id,Token,APPToken
#  sensitive-headers:
#    - Cookie,Set-Cookie,Authorization,X-Authorization

tokenSignKey: thingsboardDefaultSigningKey
