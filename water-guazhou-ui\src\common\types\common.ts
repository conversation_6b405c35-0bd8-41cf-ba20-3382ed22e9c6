export interface IProject {
  additionalInfo?: { address?: string; location?: Array<number>[] }
  children?: Array<any>[]
  createTime?: number
  gatewayList?: any
  id: string
  leaf?: boolean
  name: string
  nodeId?: string | null
  nodeType?: string
  parentId?: string
  tenantId?: string
  disabled?: boolean
}
// 查询table的参数
export interface QueryListParam extends ExtraQueryParam {
  page: number | string
  size: number | string
}
// 额外的查询参数，这个可以随意添加配置，但是已有的不能删除
export interface ExtraQueryParam {
  name?: string
  code?: string
  deviceType?: number
  projectId?: string
  transferNo?: string
  handleNo?: string
  deviceId?: string
  limit?: number | string
  chooseIds?: []
}

// 上传文件对象
export interface FileItem {
  url: string
  name: string
}
