package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectFiles;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchivePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchiveSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.construction.project.SoProjectArchiveService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/projectArchive")
public class SoProjectArchiveController extends BaseController {
    @Autowired
    private SoProjectArchiveService service;
    

    @GetMapping
    public IPage<SoProjectArchive> findAllConditional(SoProjectArchivePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/files/{projectCode}")
    public SoProjectFiles files(String projectCode) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findFilesByProjectCode(projectCode, tenantId);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoProjectArchivePageRequest request) {
        return ExcelFileInfo.of("项目总归档列表", findAllConditional(request).getRecords())
                .nextTitle("projectCode", "项目编号")
                .nextTitle("projectName", "项目名称")
                .nextTitle("projectStartTimeName", "启动时间")
                .nextTitle("archiveTimeName", "总归档时间");
    }

    @PostMapping
    public SoProjectArchive save(@RequestBody SoProjectArchiveSaveRequest req) {
        if (req.getId() == null && !service.canArchive(req.getProjectCode(), req.tenantId())) {
            ExceptionUtils.silentThrow("归档操作非法");
        }
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoProjectArchiveSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}