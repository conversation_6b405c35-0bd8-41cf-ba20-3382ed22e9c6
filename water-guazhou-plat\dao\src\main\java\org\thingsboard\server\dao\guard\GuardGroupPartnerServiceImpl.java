package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroupPartner;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardGroupPartnerMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPartnerPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPartnerSaveRequest;

import java.util.Collections;
import java.util.List;

@Service
public class GuardGroupPartnerServiceImpl implements GuardGroupPartnerService {
    @Autowired
    private GuardGroupPartnerMapper mapper;

    @Override
    public IPage<GuardGroupPartner> findAllConditional(GuardGroupPartnerPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<GuardGroupPartner> replaceAll(String groupId, List<GuardGroupPartnerSaveRequest> partners) {
        mapper.deleteAllByGroupId(groupId);
        if (partners.size() == 0) {
            return Collections.emptyList();
        }

        return QueryUtil.saveOrUpdateBatchByRequest(partners, mapper::saveAll, mapper::saveAll);
    }

}
