<template>
  <div class="wrapper" :class="props.size">
    <div class="row-top">
      <div class="total-info">
        <div class="water-light"></div>
        <div class="total-panel"></div>
        <div class="detail">
          <div class="count">
            <span class="value">{{ curItem?.todayWaterSupply ?? '--' }}</span
            ><span class="unit">{{ curItem?.unit ?? 'm³' }}</span>
          </div>
          <div class="text">
            <span>今日供水量</span>
          </div>
          <div class="footer">
            <span class="yesterday">昨天</span>
            <span class="yesterday number">{{ curItem?.yesterdayWaterSupply ?? '--' }}</span>
            <span class="yesterday">{{ curItem?.yesterdayUnit ?? 'm³' }}</span>
            <!-- <span class="percent">{{ state.delta }}%</span> -->
            <!-- <img
              src="../../imgs/up.png"
              alt=""
              class="up"
            /> -->
          </div>
        </div>
      </div>
      <div ref="refWrapper" class="wings overlay-y">
        <!--        <template v-if="state.pumps.length">-->
        <!--          <div-->
        <!--            v-for="(item, i) in state.pumps"-->
        <!--            :key="i"-->
        <!--            class="wing-block"-->
        <!--            @click="() => handleClick(i)"-->
        <!--          >-->
        <!--            <img-->
        <!--              :src="state.curPumpIndex === i ? wing_light : wing"-->
        <!--              alt="泵站"-->
        <!--              :class="{ rotate: state.curPumpIndex === i }"-->
        <!--            />-->
        <!--            <div-->
        <!--              class="text"-->
        <!--              :title="item.name"-->
        <!--              :class="{ active: state.curPumpIndex === i }"-->
        <!--            >-->
        <!--              <span>-->
        <!--                {{ item.name }}-->
        <!--              </span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </template>-->
        <div v-if="curItem" class="wing-block" @click="() => handleClick()">
          <img :src="wing_light" alt="泵站" class="rotate" />
          <div class="text active" :title="curItem.name">
            <span>
              {{ curItem.name }}
            </span>
          </div>
        </div>
        <div v-else class="empty">暂无泵站信息</div>
      </div>
    </div>
    <FieldSet :title="'供水详情'" :type="'simple'" style="margin: 0"></FieldSet>
    <div class="chart-box">
      <VChart :option="state.chartOption"></VChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { padStart } from 'lodash-es';
import { getWaterSupplyDetail, getWaterSupplyInfo } from '@/api/secondSupplyManage/monitoringOverview';
import wing_light from '../../imgs/wing_light.png';
import { toCommaNumber, transNumberUnit } from '@/utils/GlobalHelper';
import { getWaterSupplyInfoTotal, getWaterSupplyDetailTotal } from '@/api/headwatersManage/headwaterMonitoring';

const emit = defineEmits(['showWaterSupply']);
const props = defineProps<{
  size?: 'small';
  waterSupply?: any;
}>();
const curItem = ref<any>();
const initChart = (xData?: any[], yData1?: any[], yData2?: any[]) => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: 30,
      right: 20,
      bottom: 0,
      top: 30,
      containLabel: true
    },
    legend: {
      left: 'right',
      top: 'top',
      textStyle: {
        color: '#fff'
      }
    },

    dataZoom: [
      {
        show: true,
        type: 'inside',
        start: 0,
        end: 100,
        textStyle: {
          color: '#fff'
        }
      },
      {
        show: false,
        start: 0,
        end: 100
      }
    ],
    xAxis: {
      axisLabel: {
        color: '#B8D2FF'
      },
      axisTick: {
        show: false
      },
      boundaryGap: false,
      type: 'category',
      data: xData ?? Array.from({ length: 24 }).map((item, i) => padStart((i + 1).toString(), 2, '0') + '时')
    },
    yAxis: [
      {
        name: '单位(m³)',
        nameTextStyle: {
          color: '#B8D2FF'
        },
        axisLabel: {
          color: '#B8D2FF'
        },
        axisLine: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#1B4E90'
          }
        },
        type: 'value'
      }
      // {
      //   name: '单位(kWh)',
      //   nameTextStyle: {
      //     color: '#B8D2FF'
      //   },
      //   axisLabel: {
      //     color: '#B8D2FF'
      //   },
      //   axisLine: {
      //     show: false
      //   },
      //   splitLine: {
      //     show: false
      //   },
      //   type: 'value'
      // }
    ],
    series: [
      {
        type: 'line',
        name: '昨日供水量',
        smooth: true,
        symbol: 'none',
        // yAxisIndex: 1,
        itemStyle: {
          color: 'rgba(243, 232, 129, 1)'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 195, 41, 0.3)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(243, 232, 129, 0)' // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: yData2 ?? []
      },
      {
        type: 'line',
        name: '今日供水量',
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: 'rgba(41, 255, 255, 1)'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(41, 255, 255, 0.6)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(129, 197, 243, 0)' // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: yData1 ?? []
      }
    ]
  };
  return option;
};
const state = reactive<{
  todayTotal: string;
  yesterdayTotal: string;
  delta: number;
  deltaAbs: 'up' | 'down' | 'normal';
  curPumpIndex: number;
  pumps: {
    name: string;
    stationId?: string;
    todayWaterSupply?: string;
    yesterdayWaterSupply?: string;
    unit: string;
    yesterdayUnit: string;
  }[];
  chartOption: any;
}>({
  todayTotal: toCommaNumber(0),
  yesterdayTotal: toCommaNumber(0),
  delta: 0,
  deltaAbs: 'up',
  curPumpIndex: 0,
  pumps: [],
  chartOption: initChart()
});

const handleClick = (i: number) => {
  state.curPumpIndex = i;
};
const refreshData = async () => {
  try {
    const res = await getWaterSupplyInfo();
    state.pumps =
      res.data?.data?.map((item) => {
        const todayWaterSupply = transNumberUnit(item.todayWaterSupply);
        const yesterdayWaterSupply = transNumberUnit(item.yesterdayWaterSupply);

        return {
          ...item,
          todayWaterSupply: toCommaNumber(todayWaterSupply.value),
          yesterdayWaterSupply: toCommaNumber(yesterdayWaterSupply.value),
          unit: todayWaterSupply.unit + 'm³',
          yesterdayUnit: yesterdayWaterSupply.unit + 'm³'
        };
      }) || [];
    curItem.value = state.pumps.find((data) => data.name === props.waterSupply.name) as any;
    emit('showWaterSupply', curItem.value);
    refreshChart();
  } catch (error) {
    //
  }
};

const refreshTotalData = async () => {
  const res = await getWaterSupplyInfoTotal();
  const data = res.data.data;
  const todayWaterSupply = transNumberUnit(data.todayWaterSupply);
  const yesterdayWaterSupply = transNumberUnit(data.yesterdayWaterSupply);
  curItem.value = {
    ...data,
    todayWaterSupply: toCommaNumber(todayWaterSupply.value),
    yesterdayWaterSupply: toCommaNumber(yesterdayWaterSupply.value),
    unit: todayWaterSupply.unit + 'm³',
    yesterdayUnit: yesterdayWaterSupply.unit + 'm³'
  };
  emit('showWaterSupply', curItem.value);
  refreshChart();
};
const refreshChart = async () => {
  try {
    let res = [];
    if (curItem.value.name === '梓莲达') {
      res = await getWaterSupplyDetailTotal();
    } else {
      const stationId = curItem.value.stationId;
      if (!stationId) return;
      res = await getWaterSupplyDetail(stationId);
    }
    const yesterday = res.data?.data?.yesterdayTotalFlowDataList?.map((item) => item.value) || [];
    const today = res.data?.data?.todayTotalFlowDataList?.map((item) => item.value) || [];
    state.chartOption = initChart(undefined, today, yesterday);
  } catch (error) {
    //
  }
};

// const curItem = computed(() => {
//   return state.pumps[state.curPumpIndex]
// })
watch(
  () => props.waterSupply,
  () => {
    if (props.waterSupply.name === '梓莲达') {
      refreshTotalData();
    } else {
      refreshData();
    }
  }
);
const refWrapper = ref<HTMLDivElement>();
</script>
<style lang="scss" scoped>
.wrapper {
  padding: 0;
  margin: 0;
  padding: 20px;
  &.small {
    .row-top {
      height: 100px;
    }
    .total-info {
      padding: 10px;
    }
    .chart-box {
      height: 70px;
    }
  }
}
.row-top {
  display: flex;
  flex-direction: row;
  height: 150px;
  overflow: hidden;
}
.total-info {
  width: 60%;
  height: 100%;
  padding: 20px;
  position: relative;
  .water-light {
    position: absolute;
    left: 0;
    top: 15px;
    width: 110px;
    height: 120px;
    background: url('../../imgs/water_green_light.png') 0 0 /100% 100% no-repeat;
  }
  .total-panel {
    position: absolute;
    left: 20%;
    top: 0;
    width: 80%;
    height: 100%;
    background: url('../../imgs/green_panel.png') 0 50% /100% 40% no-repeat;
  }
  .detail {
    position: absolute;
    word-break: keep-all;
    white-space: nowrap;
    left: 50%;
    padding: 12px 0;
    line-height: 1.5;
    .value {
      font-size: 28px;
      color: #65ffbe;
    }
    .unit {
      font-size: 12px;
      margin-left: 4px;
    }
    .text {
      color: #fff;
      font-size: 14px;
    }
    .footer {
      color: #77c3ff;
      font-size: 14px;
      margin-top: 20px;
      .yesterday.number {
        font-family: font-lcd;
        margin: 0 4px;
        font-size: 18px;
      }
      .percent {
        margin-left: 6px;
        color: #2befef;
      }
      .up {
        margin-left: 4px;
      }
    }
  }
}

.wings {
  display: flex;
  justify-content: center;
  //flex-wrap: wrap;
  width: 40%;
  align-items: center;
  .wing-block {
    width: 50%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex-direction: column;
    cursor: pointer;
    // .rotate {
    //   transform: rotate(360deg);
    //   animation: rotation 3s linear infinite;
    // }
    .text {
      margin-top: 8px;
      width: 75px;
      font-size: 12px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      text-align: center;
      &.active {
        color: #4ef4ff;
      }
    }
  }
}
@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}
.chart-box {
  height: 120px;
}
.empty {
  margin: auto;
  font-size: 12px;
  color: #77c3ff;
}
</style>
