<!-- 新建巡检任务 -->
<template>
  <RightDrawerMap ref="refMap" title="新建任务" @map-loaded="onMaploaded">
    <Form ref="refForm" :config="FormConfig"></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice';
import {
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import { SLMessage } from '@/utils/Message';
import useSketch from '@/hooks/arcgis/useSketch';
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import { AddMaintainTask } from '@/api/patrol';

const refForm = ref<IFormIns>();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const staticState: {
  view?: __esri.MapView;
  graphic?: __esri.Graphic;
  graphicsLayer?: __esri.GraphicsLayer;
  sketch?: __esri.SketchViewModel;
} = {};
const state = reactive<{
  tabs: any[];
  loading: boolean;
  uniquing: boolean;
  detailing: boolean;
  layerIds: number[];
  layerInfos: any[];
  curFieldNode?: any;
}>({
  tabs: [],
  loading: false,
  detailing: false,
  uniquing: false,
  layerIds: [],
  layerInfos: []
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '任务名称',
          field: 'name',
          rules: [{ required: true, message: '请输入任务名称' }]
        },
        {
          type: 'select',
          field: 'device',
          options: [],
          label: '选择设备类型',
          rules: [{ required: true, message: '请选择设备类型' }],
          onChange: () => refreshDevice()
        },
        {
          type: 'btn-group',
          label: '绘制工具',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制椭圆',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        },
        {
          type: 'list',
          label: '设备属性',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (row.device === undefined) return;
            const layerid = row.device;
            const layerName = state.layerInfos.find(
              (item) => item.layerid === layerid
            )?.layername;
            if (!layerName) return;
            const fields = await GetFieldConfig(layerName);
            config.data = fields.data?.result?.rows;
          },
          setDataBy: 'device',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curFieldNode = node;
            appendSQL(node.name);
          }
        },
        {
          id: 'uniquevalue',
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.uniquing ? '正在获取唯一值' : '获取唯一值',
                      loading: () => state.uniquing,
                      disabled: () => state.loading,
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.uniquing,
              click: () => clear(),
              styles: {
                width: '100%'
              }
            },
            {
              perm: true,
              text: '查看设备详情',
              type: 'success',
              disabled: () => state.uniquing,
              loading: () => state.detailing,
              click: () => refreshDevice(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'datetimerange',
          label: '起止时间',
          field: 'beginTime',
          rules: [{ required: true, message: '请输入选择起止时间' }]
        },
        {
          type: 'user-select',
          label: '养护人员',
          placement: 'top',
          field: 'maintainUser'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              text: '确定',
              loading: () => state.loading,
              click: () => refForm.value?.Submit()
            },
            {
              perm: true,
              styles: { width: '100%' },
              text: '重置',
              type: 'default',
              disabled: state.loading,
              click: () => refForm.value?.resetForm()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    maintainUser: []
  },
  submit: (params) => {
    const submitParams = {
      ...params,
      deviceName: state.layerInfos.find(
        (item) => item.layerid === params.device
      )?.layername,
      maintainUser: params.maintainUser?.join(','),
      beginTime: params.beginTime?.[0],
      endTime: params.beginTime?.[1],
      items: state.tabs[0]?.data?.map((item) => ({ objectId: item })) || []
    };
    AddMaintainTask(submitParams)
      .then((res) => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message);
          refForm.value?.resetForm();
        } else {
          SLMessage.error(res.data.message);
        }
      })
      .catch((error) => {
        console.log(error);
        SLMessage.error('系统错误');
      });
  }
});
const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};

const getUniqueValue = async () => {
  if (!state.curFieldNode) return;
  const layerid = refForm.value?.dataForm.device;
  if (layerid === undefined) {
    SLMessage.warning('请先选择设备');
    return;
  }
  state.uniquing = true;
  try {
    const res = await GetFieldUniqueValue({
      layerid,
      field_name: state.curFieldNode.name
    });
    const extraFormItem = FormConfig.group[0].fields.find(
      (item) => item.id === 'uniquevalue'
    )?.extraFormItem;
    const field = extraFormItem && (extraFormItem[0] as IFormList);
    field && (field.data = res.data.result.rows);
  } catch (error) {
    SLMessage.error('获取唯一值失败');
  }
  state.uniquing = false;
};
const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};

const initDraw = (type) => {
  clearGraphicsLayer();
  staticState.sketch?.create(type);
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.graphic = undefined;
};
const refreshDevice = async () => {
  const query = refForm.value?.dataForm || {};
  const layerid = query.device;
  if (layerid === undefined) {
    SLMessage.warning('请先选择设备');
    return;
  }
  if (!staticState.graphic) {
    SLMessage.warning('请绘制范围');
    return;
  }
  const sql = query.sql;
  const geometry = staticState.graphic?.geometry;
  state.tabs = await getLayerOids([layerid], state.layerInfos, {
    geometry,
    where: sql
  });
  refMap.value?.refreshDetail(state.tabs);
};
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view);
  const layerInfo = await queryLayerClassName(state.layerIds);
  state.layerInfos = layerInfo.data?.result?.rows || [];
  const field = FormConfig.group[0].fields[1] as IFormSelect;

  const options = state.layerInfos.map((item) => {
    return {
      label: item.layername,
      value: item.layerid,
      data: item
    };
  });
  field && (field.options = options);
};
const { initSketch, destroySketch } = useSketch();
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.graphic = result.graphics[0];
    refreshDevice();
  }
};
const onMaploaded = async (view) => {
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'inspect-range',
    title: '养护范围'
  });
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
  getLayerInfo();
};
onBeforeUnmount(() => {
  destroySketch();
  clearGraphicsLayer();
});
</script>
<style lang="scss" scoped></style>
