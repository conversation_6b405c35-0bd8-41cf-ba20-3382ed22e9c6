<template>
  <PopLayout
    ref="refParent"
    :title="config.title"
    :visible="config.visible"
    :show-more="config.showMore"
    :show-back="config.showBack"
    :status="config.status"
    :high-light="config.highLight"
    :background-color="config.bgColor"
    :view="view"
    :x="config.x"
    :y="config.y"
    :offsetx="config.offsetX"
    :offsety="config.offsetY"
    @toggled="flag => $emit('toggled', flag)"
    @highlight="$emit('highlight', config)"
    @more="$emit('more', config.attributes)"
    @back="$emit('back', config.attributes)"
  >
    <div
      v-if="config.attributes.values"
      class="list-wrapper"
      :style="config.style"
    >
      <slot>
        <ul
          class="attr-list"
          :class="{
            'first-list':
              props.disableScroll !== true && config.attributes.values?.length && config.attributes.values.length > 6
          }"
          :style="'animation-duration:' + duration + 's;'"
        >
          <li
            v-for="(item, i) in config.attributes.values"
            :key="i"
            class="attr-list-item"
          >
            <span class="label">{{ item.label }}</span>
            <span class="count">{{ item.value ?? '--' }}</span>
            <span class="unit">{{ item.unit }}</span>
          </li>
        </ul>
        <ul
          v-if="props.disableScroll !== true && config.attributes.values?.length && config.attributes.values.length > 6"
          class="attr-list second-list"
          :style="'animation-duration:' + duration + 's;'"
        >
          <li
            v-for="(item, i) in config.attributes.values"
            :key="i"
            class="attr-list-item"
          >
            <span class="label">{{ item.label }}</span>
            <span class="count">{{ item.value ?? '--' }}</span>
            <span class="unit">{{ item.unit }}</span>
          </li>
        </ul>
      </slot>
    </div>
    <div
      v-else-if="config.attributes.row"
      class="list-wrapper"
      :style="config.style"
    >
      <slot>
        <ul
          class="attr-list"
          :class="{
            'first-list': rowAttrs.length > 6
          }"
          :style="'animation-duration:' + duration + 's;'"
        >
          <li
            v-for="(item, i) in rowAttrs"
            :key="i"
            class="attr-list-item"
          >
            <span class="label">{{ item.label }}</span>
            <span class="count">{{ item.value || '--' }}</span>
          </li>
        </ul>
        <ul
          v-if="rowAttrs.length > 6"
          class="attr-list"
          :class="{
            'second-list': rowAttrs.length > 6
          }"
          :style="'animation-duration:' + duration + 's;'"
        >
          <li
            v-for="(item, i) in rowAttrs"
            :key="i"
            class="attr-list-item"
          >
            <span class="label">{{ item.label }}</span>
            <span class="count">{{ item.value || '--' }}</span>
          </li>
        </ul>
      </slot>
    </div>
    <template v-else>
      <slot></slot>
    </template>
  </PopLayout>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import PopLayout from './PopLayout.vue'

const refParent = ref<InstanceType<typeof PopLayout>>()
const props = defineProps<{
  view?: __esri.MapView
  config: IArcMarkerProps
  disableScroll?: boolean
}>()
defineEmits(['highlight', 'more', 'back', 'toggled'])
const open = () => {
  refParent.value?.open()
}
const close = () => {
  refParent.value?.close()
}
const toggle = (flag?: boolean) => {
  refParent.value?.toggle(flag)
}
const setPosition = (mapView?: __esri.MapView, position?: { x?: number; y?: number }) => {
  refParent.value?.setPosition(mapView, position)
}
const rowAttrs = computed(() => {
  const row = props.config.attributes.row
  if (!row) return []
  return Object.keys(row).map(item => {
    return { label: item, value: row[item] === 'Null' ? undefined : row[item] }
  })
})
const duration = computed(() => {
  return props.config.attributes.values?.length || rowAttrs.value.length
})
watch(
  () => props.config.visible,
  (newVal: any) => {
    props.view
      && newVal
      && refParent.value?.setPosition(props.view, {
        x: props.config.x,
        y: props.config.y,
        longitude:props.config.longitude,
        latitude:props.config.latitude,
      })
  }
)
onMounted(() => {
  props.view?.watch('extent', () => {
    props.view
      && refParent.value?.setPosition(props.view, {
        x: props.config.x,
        y: props.config.y,
        longitude:props.config.longitude,
        latitude:props.config.latitude,
      })
  })
})
defineExpose({
  open,
  close,
  toggle,
  setPosition
})
</script>
<style lang="scss" scoped>
.list-wrapper {
  height: 186px;
  overflow: hidden;
  padding-right: 8px;
  &:hover {
    overflow-y: auto;
    overflow-y: overlay;
    .first-list,
    .second-list {
      animation-play-state: paused;
    }
  }
}
.attr-list {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 12px;
  line-height: 20px;

  &-item {
    margin: 0;
    padding: 0;
    display: flex;
    align-items: baseline;
    width: 100%;
    height: 30px;
    .label {
      word-break: keep-all;
      // color: rgb(156, 177, 196);
      margin-right: 20px;
      color: #8e8f95;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }
    .count {
      font-size: 14px;
      margin-left: auto;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: keep-all;
      white-space: nowrap;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      /* identical to box height */

      text-align: right;
      color: var(--el-text-color-regular);
    }
    .unit {
      font-size: 12px;
      margin-left: 8px;
      color: var(--el-text-color-regular);
      // color: rgb(89, 146, 189);
    }
  }
}
.first-list {
  animation-name: first-table;
  animation-duration: 10s;
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-direction: normal;
}

@keyframes first-table {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    display: none;
  }
}

.second-list {
  animation-name: second-table;
  animation-duration: 10s;
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-direction: normal;
}

@keyframes second-table {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    display: none;
  }
}
</style>
