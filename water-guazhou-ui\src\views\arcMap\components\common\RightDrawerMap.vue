<template>
  <DrawerBox ref="refDrawerBox" :right-drawer="!hideRightDrawer" :right-drawer-title="title"
    :right-drawer-width="rightDrawerWidth" :right-drawer-absolute="rightDrawerAbsolute"
    :right-drawer-bar-position="'top'" :theme="appStore.isDark ? 'darkblue' : 'light'"
    :class="appStore.isDark ? 'darkblue' : ''" :before-collapse="beforeCollapse"
    :right-drawer-min-width="rightDrawerMinWidth">
    <template #right-title>
      <slot name="right-title"></slot>
    </template>
    <template #right>
      <slot></slot>
    </template>
    <div v-loading="!state.mounted" class="loading-wrapper">
      <div id="map-outer-box" ref="refMapWrapper" class="map-wrapper" @click="(e) => emit('click', e)">
        <ArcView :map-config="mapConfig" @loaded="handleLoaded">
          <ArcStationWarning ref="refAlarms" @click="closeAllPop"></ArcStationWarning>

          <div class="pop-container">
            <template v-for="pop in pops" :key="pop.id">
              <PopLayout v-show="pop.visible" :ref="'refPop' + pop.id" :title="pop.title" :show-more="pop.showMore"
                :show-back="pop.showBack" :status="pop.status" :background-color="pop.bgColor" :x="pop.x" :y="pop.y"
                :latitude="pop.latitude" :longitude="pop.longitude" :offsetx="pop.offsetX" :offsety="pop.offsetY"
                @more="() => emit('pop-more', pop)" @back="() => emit('pop-back', pop)"
                @toggled="(flag) => handlePopToggle(pop, flag)">
                <slot name="pop-default" :config="pop">
                  <component :is="pop.customComponent" :visible="pop.visible" :config="pop.customConfig"></component>
                </slot>
              </PopLayout>
            </template>
          </div>
          <div v-if="windows?.length" class="infowindow-container">
            <ListWindow v-for="(pop, j) in windows" :key="j" :ref="'refPop' + pop.attributes?.id"
              :view="staticState.view" :config="pop" :disable-scroll="true"
              @toggled="(flag) => handlePopToggle(pop, flag)" @highlight="highlightPop">
              <template v-if="pop.attributes?.customComponent">
                <component :is="pop.attributes.customComponent" :visible="pop.visible"
                  :config="pop.attributes.customConfig"></component>
              </template>
              <template v-else-if="windowTableConfig">
                <div class="pop-table-box overlay-y">
                  <FormTable :config="windowTableConfig"></FormTable>
                </div>
              </template>
              <template v-else-if="windowFormConfig">
                <Form :config="windowFormConfig"></Form>
              </template>
            </ListWindow>
          </div>
          <PoiSearch id="tool-search-poi" @change="handlePoiChange"></PoiSearch>
          <ArcBRTools v-if="state.mounted" ref="refArcBRTools" :view="staticState.view"
            :basemap-change="handleBasemapChange"></ArcBRTools>
          <PipeDetail v-if="state.mounted" ref="refDetail" :tabs="state.tabs" :view="staticState.view"
            :telport="'#map-outer-box'" :detail-url="detailUrl" :maxmin="detailMaxMin"
            @refreshed="$emit('detail-refreshed')" @refreshing="$emit('detail-refreshing')"
            @close="$emit('detail-closed')" @rowdblclick="handleLocate"></PipeDetail>
          <Panel v-if="state.mounted" ref="refPanel" :custom-class="panelCustomClass || 'gis-detail-panel'"
            :telport="'#map-outer-box'" :draggable="detailDragable" :dragout="detailDragout" :full-content="fullContent"
            :destroy-by-close="true" :after-open="handleDetailOpen" :extra="detailExtra" :show-close="!hideDetailClose"
            :max-min="detailMaxMin === undefined ? true : detailMaxMin" :before-close="handleDetailClose"
            :title="detailTitle">
            <template #extra>
              <slot name="detail-extra"> </slot>
            </template>
            <template #header>
              <slot name="detail-header"></slot>
            </template>
            <template #default>
              <slot name="detail-default"></slot>
            </template>
          </Panel>
          <slot name="map-bars"></slot>
        </ArcView>
      </div>
    </div>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { reactive, ref, nextTick, onMounted, onUnmounted, onBeforeUnmount, watch, getCurrentInstance, markRaw, toRaw } from 'vue'
import * as reactiveUtils from '@arcgis/core/core/reactiveUtils.js';
import Graphic from '@arcgis/core/Graphic.js';
import Point from '@arcgis/core/geometry/Point.js';
import { useAppStore } from '@/store';
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue';
import PipeDetail from '@/views/arcMap/components/common/PipeDetail.vue';
import ArcBRTools from '@/views/arcMap/components/common/ArcBRTools.vue';
import ListWindow from '@/views/arcMap/components/popup/ListWindow.vue';
import PopLayout from '@/views/arcMap/components/popup/PopLayout.vue';
import PoiSearch from '@/components/arcMap/PoiSearchV2.vue';
import { useCoordinate, useLayerList, useScaleBar } from '@/hooks/arcgis';
import { gotoAndHighLight, setSymbol } from '@/utils/MapHelper';
import { getMapLocationImageUrl } from '@/utils/URLHelper';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer.js';
import SimpleRenderer from '@arcgis/core/renderers/SimpleRenderer.js';
import UniqueValueRenderer from '@arcgis/core/renderers/UniqueValueRenderer.js';
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol.js';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js';
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js';

interface IEmitItems {
  (e: 'map-loaded', view: __esri.MapView, options: any): any;
  (e: 'detail-refreshing'): any;
  (e: 'detail-refreshed'): any;
  (e: 'detail-closed'): any;
  (e: 'detail-opened'): any;
  (e: 'pop-more', attributes: any): any;
  (e: 'pop-back', attributes: any): any;
  (e: 'click', event: any): any;
  (e: 'pop-toggle', pop: IArcMarkerProps, flag?: boolean): any;
}

// 定义聚合功能相关接口
interface IClusterFeature {
  geometry: __esri.Geometry;
  attributes: {
    ObjectID: number;
    name: string;
    [key: string]: any;
  };
}

interface IClusterConfig {
  radius?: number;
  minSize?: number;
  maxSize?: number;
}

// 定义简化的点位数据接口（与父组件保持一致）
interface ISimpleMarker {
  id: string;
  name: string;
  longitude: number;
  latitude: number;
  attributes: any;
  symbolType: 'marker' | 'text';
  symbolConfig: any;
}

const appStore = useAppStore();
const { proxy }: any = getCurrentInstance();
const refAlarms =
  ref<
    InstanceType<
      (typeof import('@/components/arcMap/widgets/ArcStationWarning.vue'))['default']
    >
  >();
const darkFilter = 'grayscale(0%) invert(100%) opacity(100%)';
const mapConfig = reactive<IFormGisConfig>({
  defaultBaseMap: window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap || 'arcgis_imagery',
  defaultFilter: ''
});
const handleBasemapChange = (e) => {
  console.log(e);
  if (e.id === 'dark_vec_w') {
    mapConfig.defaultFilter = darkFilter;
  } else {
    mapConfig.defaultFilter = '';
  }
};
// const refMap = ref<IArcMapIns>()
const refDetail = ref<InstanceType<typeof PipeDetail>>();
const refPanel = ref<IPanelIns>();
const refMapWrapper = ref<HTMLDivElement>();
const refDrawerBox = ref<InstanceType<typeof DrawerBox>>();
const refArcBRTools = ref<InstanceType<typeof ArcBRTools>>();
// const refPoi = ref<InstanceType<typeof PoiSearch>>()
const scale = useScaleBar();
const coordinate = useCoordinate();
const layerList = useLayerList();
const emit = defineEmits<IEmitItems>();
const props = defineProps<{
  useArclayout?: boolean;
  detailDragout?: boolean;
  detailDragable?: boolean;
  detailTitle?: string;
  title: string;
  rightDrawerWidth?: number;
  rightDrawerAbsolute?: boolean;
  rightDrawerMinWidth?: number;
  hideRightDrawer?: boolean;
  hideDetailClose?: boolean;
  detailExtra?: boolean;
  windows?: IArcMarkerProps[];
  windowTableConfig?: ITable;
  windowFormConfig?: IFormConfig;
  windowStyle?: Record<string, any>;
  detailMaxMin?: boolean;
  /** 针对详情的边距处理 */
  fullContent?: boolean;
  panelCustomClass?: string;
  detailUrl?: string;
  hideSearch?: boolean;
  hideLayerList?: boolean;
  hideCoords?: boolean;
  hidePipe?: boolean;
  pops?: IArcPopConfig[];
  enableCluster?: boolean;  // 控制是否启用聚合
  clusterRadius?: number;   // 聚合半径
  clusterMinSize?: number;  // 聚合点最小尺寸
  clusterMaxSize?: number;  // 聚合点最大尺寸
  clusterGraphics?: ISimpleMarker[];  // 修改为简化数据类型
  beforeCollapse?: (
    direction: 'rtl' | 'ltr' | 'btt' | 'ttb',
    open?: boolean
  ) => any;
}>();
const state = reactive<{
  tabs: any[];
  toolsMounted: boolean;
  mounted: boolean;
  isClusterEnabled: boolean;
}>({
  tabs: [],
  toolsMounted: false,
  mounted: false,
  isClusterEnabled: props.enableCluster || true
});
let clusterLayerMap: FeatureLayer | null = null;
let clusterFeatures: IClusterFeature[] = [];
const staticState: {
  view?: __esri.MapView;
  watchers?: any[];
  toolWdigets?: any;
  featureLayers?: __esri.FeatureLayer[];
} = {
  featureLayers: []
};

const handlePopToggle = (pop, flag) => emit('pop-toggle', pop, flag);
const highlightPop = (pop: IArcMarkerProps) => {
  pop.highLight = true;
};
const handleDetailOpen = () => {
  emit('detail-opened');
};
const handleDetailClose = () => {
  emit('detail-closed');
};
const handleLocate = async () => {
  staticState.view && refDetail.value?.extentTo(staticState.view);
};

const refreshDetail = async (tabs: any[]) => {
  state.tabs = tabs;
  await nextTick();
  refDetail.value?.openDialog();
};
const clearDetailData = () => {
  refDetail.value?.clearData();
};
const toggleCustomDetail = (open?: boolean) => {
  refPanel.value?.Toggle(open);
};
const isCustomOpened = () => {
  return refPanel.value?.visible;
};
const toggleCustomDetailMaxmin = (type: 'max' | 'min' | 'normal') => {
  refPanel.value?.toggleMaxMin(type);
};
const openPop = (id?: string) => {
  closeAllPop();
  refAlarms.value?.togglePop(false);
  if (id === undefined) return;
  const pop = proxy.$refs['refPop' + id];
  if (!pop?.[0]) return;
  pop[0]?.toggle(true);
  pop[0]?.setPosition(staticState.view);
};
const closeAllPop = () => {
  props.windows?.map((item) => {
    const id = item.attributes?.id;
    if (!id) return;
    const pop = proxy.$refs['refPop' + id];
    pop?.length && pop[0]?.toggle(false);
  });
  props.pops?.map((item) => {
    const id = item?.id;
    if (!id) return;
    const pop = proxy.$refs['refPop' + id];
    pop?.length && pop[0]?.toggle(false);
  });
};
const toggleDrawer = (flag?: boolean) => {
  return refDrawerBox.value?.toggleDrawer(
    'rtl',
    flag === undefined ? true : flag
  );
};

// 将简化数据转换为Graphic对象
const convertSimpleDataToGraphics = (simpleData: ISimpleMarker[]): __esri.Graphic[] => {
  if (!simpleData || !Array.isArray(simpleData) || simpleData.length === 0) {
    console.log('📝 没有数据需要转换');
    return [];
  }
  
  const graphics: __esri.Graphic[] = [];
  
  simpleData.forEach((item, index) => {
    try {
      // 使用toRaw获取原始数据，避免响应式包装
      const rawItem = toRaw(item);
      
      // 验证必要的数据
      if (!rawItem || typeof rawItem.longitude !== 'number' || typeof rawItem.latitude !== 'number') {
        console.warn(`⚠️ 第${index}个数据项缺少有效的坐标:`, rawItem);
        return;
      }
      
      // 创建几何对象
      const point = markRaw(new Point({
        longitude: rawItem.longitude,
        latitude: rawItem.latitude,
        spatialReference: staticState.view?.spatialReference || { wkid: 4326 }
      }));
      
      let symbol: __esri.Symbol;
      
      if (rawItem.symbolType === 'marker' && rawItem.symbolConfig) {
        symbol = markRaw(new PictureMarkerSymbol({
          ...rawItem.symbolConfig,
          // 确保必要的属性存在
          width: rawItem.symbolConfig.width || 25,
          height: rawItem.symbolConfig.height || 30
        }));
      } else if (rawItem.symbolType === 'text' && rawItem.symbolConfig) {
        symbol = markRaw(new TextSymbol({
          ...rawItem.symbolConfig,
          // 确保必要的属性存在
          text: rawItem.symbolConfig.text || rawItem.name,
          color: rawItem.symbolConfig.color || '#000000'
        }));
      } else {
        // 默认标记符号
        symbol = markRaw(new SimpleMarkerSymbol({
          style: "circle",
          color: "#3388ff",
          outline: {
            width: 1,
            color: "white"
          },
          size: 12
        }));
      }
      
      // 创建Graphic对象，将符号信息也存储在属性中以便渲染器使用
      // 使用markRaw避免被Vue响应式系统处理
      const graphic = markRaw(new Graphic({
        geometry: point,
        symbol: symbol,
        attributes: {
          ObjectID: rawItem.attributes?.ObjectID || index + 1,
          name: rawItem.attributes?.name || rawItem.name || `点位${index + 1}`,
          // 存储符号相关信息供渲染器使用
          symbolType: rawItem.symbolType,
          symbolUrl: rawItem.symbolType === 'marker' ? rawItem.symbolConfig?.url : null,
          symbolWidth: rawItem.symbolType === 'marker' ? rawItem.symbolConfig?.width || 25 : null,
          symbolHeight: rawItem.symbolType === 'marker' ? rawItem.symbolConfig?.height || 30 : null,
          symbolYOffset: rawItem.symbolType === 'marker' ? rawItem.symbolConfig?.yoffset || 0 : null,
          textContent: rawItem.symbolType === 'text' ? rawItem.symbolConfig?.text || rawItem.name : null,
          textColor: rawItem.symbolType === 'text' ? rawItem.symbolConfig?.color || '#000000' : null,
          // 保留原始属性，但确保必要字段存在，只保留可序列化的数据
          ...Object.fromEntries(
            Object.entries(rawItem.attributes || {}).filter(([key, value]) => 
              typeof value === 'string' || 
              typeof value === 'number' || 
              typeof value === 'boolean' || 
              value === null
            )
          )
        }
      }));
      
      graphics.push(graphic);
    } catch (error) {
      console.error(`❌ 转换第${index}个数据项时出错:`, error, item);
    }
  });
  
  console.log(`📝 成功转换${graphics.length}个Graphic对象，原始数据${simpleData.length}个`);
  return graphics;
};

// 聚合功能实现 - 基于ArcGIS官方示例
const createClusterLayer = () => {
  if (!staticState.view) return;

  // 将简化数据转换为Graphic对象
  const initialGraphics = props.clusterGraphics ? convertSimpleDataToGraphics(toRaw(props.clusterGraphics)) : [];

  // 创建默认符号 - 当没有特定符号时使用
  const defaultSymbol = markRaw(new SimpleMarkerSymbol({
    style: "circle",
    color: "#3388ff",
    outline: {
      width: 1,
      color: "white"
    },
    size: 12
  }));

  // 收集所有唯一的符号信息，创建UniqueValueRenderer
  const symbolInfos = new Map();
  const rawData = toRaw(props.clusterGraphics) || [];
  
  rawData.forEach((item) => {
    if (item.symbolType === 'marker' && item.symbolConfig?.url) {
      const key = item.symbolConfig.url;
      if (!symbolInfos.has(key)) {
        symbolInfos.set(key, {
          value: key,
          symbol: markRaw(new PictureMarkerSymbol({
            url: item.symbolConfig.url,
            width: item.symbolConfig.width || 25,
            height: item.symbolConfig.height || 30,
            yoffset: item.symbolConfig.yoffset || 0
          })),
          label: `图标 ${key.split('/').pop()}`
        });
      }
    }
  });

  // 创建UniqueValueRenderer
  const renderer = new UniqueValueRenderer({
    field: "symbolUrl",
    defaultSymbol: defaultSymbol,
    uniqueValueInfos: Array.from(symbolInfos.values())
  });

  // 创建聚合配置 - 参考ArcGIS官方示例，修复类型错误
  const clusterLayer = new FeatureLayer({
    title: "点聚合图层",
    objectIdField: "ObjectID",
    fields: [
      {
        name: "ObjectID",
        alias: "ObjectID",
        type: "oid"
      },
      {
        name: "name",
        alias: "名称",
        type: "string"
      },
      {
        name: "symbolType",
        alias: "符号类型",
        type: "string"
      },
      {
        name: "symbolUrl",
        alias: "符号URL",
        type: "string"
      },
      {
        name: "symbolWidth",
        alias: "符号宽度",
        type: "integer"
      },
      {
        name: "symbolHeight",
        alias: "符号高度",
        type: "integer"
      },
      {
        name: "symbolYOffset",
        alias: "符号Y偏移",
        type: "integer"
      },
      {
        name: "textContent",
        alias: "文本内容",
        type: "string"
      },
      {
        name: "textColor",
        alias: "文本颜色",
        type: "string"
      }
    ],
    geometryType: "point",
    spatialReference: staticState.view.spatialReference,
    source: initialGraphics, // 使用转换后的Graphic对象，每个Graphic都有自己的符号
    renderer: renderer, // 使用UniqueValueRenderer根据URL显示不同图标
    // 设置聚合功能
    featureReduction: {
      type: "cluster",
      clusterRadius: `${props.clusterRadius || 100}px`,
      clusterMinSize: `${props.clusterMinSize || 24}px`,
      clusterMaxSize: `${props.clusterMaxSize || 60}px`,
      // 聚合点使用默认符号
      symbol: markRaw(new SimpleMarkerSymbol({
        style: "circle",
        color: [51, 136, 255, 0.8], // 蓝色半透明
        outline: {
          width: 2,
          color: "white"
        },
        size: 30
      })),
      labelingInfo: [{
        deconflictionStrategy: "none",
        labelExpressionInfo: {
          expression: "Text($feature.cluster_count, '#,###')"
        },
        symbol: {
          type: "text",
          color: "white",
          font: {
            weight: "bold" as const, // 修复类型错误
            family: "Noto Sans",
            size: "12px"
          }
        },
        labelPlacement: "center-center",
      }],
      popupTemplate: {
        title: "聚合点",
        content: "该区域包含 {cluster_count} 个点位",
        fieldInfos: [
          {
            fieldName: "cluster_count",
            format: {
              places: 0,
              digitSeparator: true,
            },
          },
        ],
      }
    }
  });

  // 图层加载后，添加自定义渲染逻辑
  clusterLayer.when(() => {
    // 由于ArcGIS FeatureLayer的限制，我们需要在图层渲染时动态应用符号
    // 这里我们通过监听图层视图的更新来实现
    staticState.view?.whenLayerView(clusterLayer).then((layerView) => {
      console.log("聚合图层视图已创建，符号将由UniqueValueRenderer根据URL控制");
      console.log("已创建符号映射:", Array.from(symbolInfos.keys()));
      console.log("聚合配置:", {
        radius: props.clusterRadius || 100,
        minSize: props.clusterMinSize || 24,
        maxSize: props.clusterMaxSize || 60,
        symbolType: "默认蓝色圆形符号用于聚合点"
      });
      console.log("散开时将使用:", `${symbolInfos.size}种不同图标`);
    });
  });

  staticState.view.map.add(clusterLayer);
  clusterLayerMap = clusterLayer;
  staticState.featureLayers?.push(clusterLayer);
  return clusterLayer;
};

// 更新聚合图层数据源
const updateClusterGraphics = async (simpleData?: ISimpleMarker[]) => {
  if (!clusterLayerMap) return;
  
  try {
    // 使用toRaw确保获取原始数据，避免响应式包装
    const dataToUse = simpleData || toRaw(props.clusterGraphics) || [];
    console.log('📝 准备转换简化数据为Graphic对象，数据长度:', dataToUse.length);
    
    // 将简化数据转换为Graphic对象
    const graphicsToUse = convertSimpleDataToGraphics(dataToUse);
    console.log('📝 转换后的Graphic对象:', graphicsToUse);
    
    // 更新渲染器以包含新的符号
    const symbolInfos = new Map();
    dataToUse.forEach((item) => {
      if (item.symbolType === 'marker' && item.symbolConfig?.url) {
        const key = item.symbolConfig.url;
        if (!symbolInfos.has(key)) {
          symbolInfos.set(key, {
            value: key,
            symbol: markRaw(new PictureMarkerSymbol({
              url: item.symbolConfig.url,
              width: item.symbolConfig.width || 25,
              height: item.symbolConfig.height || 30,
              yoffset: item.symbolConfig.yoffset || 0
            })),
            label: `图标 ${key.split('/').pop()}`
          });
        }
      }
    });

    // 如果有新的符号，更新渲染器
    if (symbolInfos.size > 0) {
      const defaultSymbol = markRaw(new SimpleMarkerSymbol({
        style: "circle",
        color: "#3388ff",
        outline: {
          width: 1,
          color: "white"
        },
        size: 12
      }));

      const newRenderer = new UniqueValueRenderer({
        field: "symbolUrl",
        defaultSymbol: defaultSymbol,
        uniqueValueInfos: Array.from(symbolInfos.values())
      });

      clusterLayerMap.renderer = newRenderer;
      console.log('📝 已更新渲染器，符号数量:', symbolInfos.size);
    }
    
    // 准备编辑操作对象
    const edits = {
      deleteFeatures: [] as __esri.Graphic[],
      addFeatures: graphicsToUse
    };
    
    // 首先查询现有要素，准备删除
    const featureSet = await clusterLayerMap.queryFeatures({
      where: "1=1", // 查询所有要素
      returnGeometry: false,
      outFields: ["*"]
    });
    
    if (featureSet.features && featureSet.features.length > 0) {
      edits.deleteFeatures = featureSet.features;
      console.log('🗑️ 准备删除现有要素数量:', featureSet.features.length);
    }
    
    // 执行批量编辑操作 - 先删除再添加，确保原子性
    if (edits.deleteFeatures.length > 0 || edits.addFeatures.length > 0) {
      const result = await clusterLayerMap.applyEdits(edits);
      
      console.log("✅ 聚合图层数据更新成功", {
        addResults: result.addFeatureResults?.length || 0,
        deleteResults: result.deleteFeatureResults?.length || 0
      });
      
      // 检查是否有错误 - FeatureEditResult成功时会有objectId，失败时为null
      const addErrors = result.addFeatureResults?.filter(r => !r.objectId) || [];
      const deleteErrors = result.deleteFeatureResults?.filter(r => !r.objectId) || [];
        
      if (addErrors.length > 0 || deleteErrors.length > 0) {
        console.warn('⚠️ 部分编辑操作失败:', {
          addErrors: addErrors.length,
          deleteErrors: deleteErrors.length,
          result
        });
      }
    } else {
      console.log('📝 没有需要更新的数据');
    }
    
  } catch (error) {
    console.error('❌ 更新聚合数据时出错:', error);
    console.error('错误详情:', {
      message: (error as Error)?.message || '未知错误',
      stack: (error as Error)?.stack
    });
  }
};

// 控制聚合开关
const toggleCluster = (enabled?: boolean) => {
  try {
    const shouldEnable = enabled !== undefined ? enabled : !state.isClusterEnabled;

    if (shouldEnable && !clusterLayerMap) {
      createClusterLayer();
    }

    if (clusterLayerMap) {
      clusterLayerMap.visible = shouldEnable;
    }

    state.isClusterEnabled = shouldEnable;
  } catch (error) {
    console.error('切换聚合功能时出错:', error);
    state.isClusterEnabled = false;
  }
};

// 更新聚合配置
const updateClusterConfig = async (config: IClusterConfig) => {
  if (!clusterLayerMap) return;

  try {
    // 重新创建图层而不是修改featureReduction属性
    const oldLayer = clusterLayerMap;
    staticState.view?.map.remove(oldLayer);
    
    // 创建新的聚合图层
    createClusterLayer();
    
    // 更新数据
    await updateClusterGraphics();
  } catch (error) {
    console.error('更新聚合配置时出错:', error);
  }
};

// 验证聚合配置
const verifyClusterConfig = () => {
  if (!clusterLayerMap) {
    console.log("❌ 聚合图层未创建");
    return false;
  }
  
  const featureReduction = clusterLayerMap.featureReduction;
  if (!featureReduction) {
    console.log("❌ 聚合配置未设置");
    return false;
  }
  
  // 类型守卫：检查是否为聚合类型
  if (featureReduction.type !== "cluster") {
    console.log("❌ 不是聚合类型的featureReduction");
    return false;
  }
  
  console.log("✅ 聚合配置验证通过");
  console.log("聚合类型:", featureReduction.type);
  console.log("聚合半径:", (featureReduction as any).clusterRadius);
  console.log("聚合符号:", (featureReduction as any).symbol ? "已设置" : "未设置");
  console.log("散开时渲染器:", clusterLayerMap.renderer ? "UniqueValueRenderer" : "无");
  
  return true;
};

// 监听父组件传递的clusterGraphics变化
watch(() => props.clusterGraphics, async (newData) => {
  console.log('=== 监听到简化数据变化 ===');
  console.log('新数据长度:', newData?.length);
  console.log('enableCluster:', props.enableCluster);
  console.log('clusterLayerMap exists:', !!clusterLayerMap);
  console.log('staticState.view exists:', !!staticState.view);
  
  if (newData?.length) {
    console.log('📊 开始处理简化数据...');
    
    // 如果启用了聚合但图层还没创建，先创建图层
    if (props.enableCluster && !clusterLayerMap && staticState.view) {
      console.log('🚀 创建聚合图层...');
      createClusterLayer();
      state.isClusterEnabled = true;
      
      // 验证聚合配置
      setTimeout(() => {
        verifyClusterConfig();
      }, 1000);
    }
    
    // 如果聚合图层存在，更新数据
    if (clusterLayerMap) {
      console.log('📝 更新聚合数据...');
      // 使用toRaw获取原始数据，避免响应式包装导致的克隆错误
      const rawData = toRaw(newData);
      await updateClusterGraphics(rawData);
    }
  } else {
    console.log('❌ 没有有效的数据，跳过处理');
  }
}, { immediate: true, deep: true });

const handlePoiChange = (location: number[]) => {
  if (!staticState.view) return;
  if (location.length !== 2) return;
  const mark = new Graphic({
    geometry: new Point({
      longitude: location?.[0],
      latitude: location?.[1],
      spatialReference: staticState.view.spatialReference
    }),
    symbol: setSymbol('picture', {
      url: getMapLocationImageUrl(),
      yOffset: -8
    })
  });

  staticState.view?.graphics.removeAll();
  staticState.view?.graphics.add(mark);

  gotoAndHighLight(staticState.view, mark, {
    avoidHighlight: true,
    zoom: 16
  });
};

const handleLoaded = (view: __esri.MapView) => {
  view.when().then(() => {
    staticState.view = view;
    state.mounted = true;

    // 如果设置了启用聚合，则创建聚合图层
    if (props.enableCluster) {
      toggleCluster(true);
    }

    const watcher = staticState.view?.watch('extent', () => {
      props.pops?.map((item) => {
        const pop = proxy.$refs['refPop' + item.id];
        pop[0]?.setPosition(staticState.view);
      });
    });
    staticState.watchers?.push(watcher);
    reactiveUtils
      .whenOnce(() => staticState.view)
      .then(() => {
        if (!staticState.view) return;

        // 右上角
        staticState.view.ui.add('tool-search-poi', 'top-right');
        !props.hideLayerList && layerList.init(staticState.view);

        // 左下角
        !props.hideCoords && coordinate.init(staticState.view);
        scale.init(staticState.view);
        
        // 右下角
        refArcBRTools.value
          ?.init({
            hidePipe: props.hidePipe
          })
          .then((params) => {
            state.toolsMounted = true;
            staticState.toolWdigets = params;
          })
          .finally(() => {
            staticState.view &&
              emit('map-loaded', staticState.view, staticState.toolWdigets);
          });
      });
  });
};

onMounted(async () => {
  refDrawerBox.value?.toggleDrawer('rtl', true);
});
onUnmounted(() => {
  staticState.watchers?.map((item) => item.remove && item.remove());
  staticState.view?.map?.removeAll();
  staticState.view?.map?.destroy();
  staticState.view?.destroy();
  staticState.view = undefined;
});
onBeforeUnmount(() => {
  toggleCustomDetail(false);
});
defineExpose({
  staticState,
  refreshDetail,
  toggleCustomDetail,
  highlightPop,
  openPop,
  closeAllPop,
  toggleCustomDetailMaxmin,
  isCustomOpened,
  toggleDrawer,
  clearDetailData,
  toggleCluster,
  updateClusterConfig,
  updateClusterGraphics,
  verifyClusterConfig
});
</script>
<style lang="scss" scoped>
.right-drawer {
  width: 100%;
  height: 100%;
}

.loading-wrapper {
  width: 100%;
  height: 100%;
}

.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  :deep(.esri-ui-top-left) {
    flex-flow: row;
  }

  :deep(.esri-ui-bottom-right) {
    flex-flow: column;
  }
}

.pop-table-box {
  height: 200px;
}

:deep(.esri-ui-bottom-right) {

  &.esri-widget--button,
  .esri-widget--button {
    border-top: solid 1px rgba(173, 173, 173, 0.3);
  }
}
</style>
