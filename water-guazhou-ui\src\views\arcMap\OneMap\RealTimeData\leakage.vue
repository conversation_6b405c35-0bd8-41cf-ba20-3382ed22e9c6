<!-- 渗漏预警 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards v-model="cardsvalue"></Cards>

    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
    <OpenDialog
      ref="refDetail"
      :config="config"
      @close="() => {}"
    >
      <template #left>
        <div style="height: 100%; width: 800px; display: flex">
          <div style="width: 400px; height: 100%">
            <VChart
              ref="refChart2"
              autoresize
              theme="dark"
              :option="option2"
            ></VChart>
          </div>
          <div style="width: 400px; height: 100%">
            <VChart
              ref="refChart3"
              autoresize
              theme="dark"
              :option="option2"
            ></VChart>
          </div>
        </div>
      </template>
      <template #right>
        <div style="width: 100%; height: 100%">
          <VChart
            ref="refChart4"
            autoresize
            theme="dark"
            :option="option3"
          ></VChart>
        </div>
      </template>
    </OpenDialog>
  </div>
</template>
<script lang="ts" setup>
import { IFormIns } from '@/components/type'
import { IECharts } from '@/plugins/echart'
import OpenDialog from '../../components/components/openDialog.vue'
import {
  hourlyLine,
  onedashboard,
  ring
} from '../../components/components/chart'
import { Cards } from '../../components'

defineProps<{
  view?: __esri.MapView
}>()
const refForm = ref<IFormIns>()

const refDetail = ref<InstanceType<typeof OpenDialog>>()
const refChart1 = ref<IECharts>()
const refChart2 = ref<IECharts>()
const refChart3 = ref<IECharts>()
const refChart4 = ref<IECharts>()
const option2 = onedashboard()
const option3 = hourlyLine()

const config = reactive({
  tabs: {
    filters: [
      {
        type: 'tabs',
        field: 'type',
        tabs: [{ label: 'cs', value: 'cs' }],
        onChange: () => {
          //
        }
      }
    ]
  }
})

const cardsvalue = ref([
  { label: '21个', value: '噪声设备数' },
  { label: '95.24%', value: '通讯合格率' },
  { label: '14.29%', value: '报警率' }
])

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      minWidth: 120,
      label: '名称',
      prop: 'key1',
      sortable: true
    },
    {
      minWidth: 140,
      label: '更新时间',
      prop: 'key2',
      sortable: true
    },
    {
      minWidth: 120,
      label: '状态',
      prop: 'key3'
    }
  ],
  handleRowClick: row => {
    TableConfig.currentRow = row
    refDetail.value?.openDialog()
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '监测状态统计'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}

onMounted(() => {
  refreshData()
  window.addEventListener('resize', resizeChart)
})

function resizeChart() {
  refChart1.value?.resize()
  refChart2.value?.resize()
  refChart3.value?.resize()
}
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 335px);
}
</style>
