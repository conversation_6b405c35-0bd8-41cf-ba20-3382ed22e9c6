package org.thingsboard.server.dao.smartService.call;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.ExtensionStatusDTO;
import org.thingsboard.server.dao.model.sql.smartService.call.CallExtensionStatus;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface CallExtensionStatusService {
    List<ExtensionStatusDTO> getList(Long startTime, Long endTime, String tenantId);

    PageData<CallExtensionStatus> getDetail(String seatsId, int page, int size);
}
