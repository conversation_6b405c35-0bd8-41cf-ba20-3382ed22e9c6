import { request } from '@/plugins/axios'

/**
 * 分页条件统计缺陷工单（通过用户）
 * @param params
 * @returns
 */
export const StatisticalWorkOrderByUser = (params?: {
  /** 状态区间 0-待处理 1-处理中 2-已处理 */
  statusStage?: string
  /** 工单类型 */
  type?: string
  /** 关键字 任务编号/事件编号/上报人员/事件地址 */
  keyword?: string
  fromTime?: number | string
  toTime?: number | string
  processUserId?: string
}) => {
  return request({
    url: '/api/sm/circuitTask/workOrder/countByUser',
    method: 'get',
    params
  })
}
/**
 * 隐患总览
 * @param params
 * @returns
 */
export const GetWorkOrderOverview = (params?: {
  /** 状态区间 0-待处理 1-处理中 2-已处理 */
  statusStage?: string
  /** 工单类型 */
  type?: string
  /** 关键字 任务编号/事件编号/上报人员/事件地址 */
  keyword?: string
}) => {
  return request({
    url: '/api/sm/circuitTask/workOrder/pitfall',
    method: 'get',
    params
  })
}
/**
 * 工单趋势分析
 * @param params
 * @returns
 */
export const WorkOrderTrend = (params: {
  fromTime?: number | string
  toTime?: number | string
  page?: number
  size?: number
  /** 存在时为分类分时统计 */
  timeUnit?: string
  processUserId?: string
}) => {
  return request({
    url: '/api/sm/circuitTask/workOrder/trend',
    method: 'get',
    params
  })
}
