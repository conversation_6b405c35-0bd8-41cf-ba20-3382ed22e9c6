import{C as v,M as I,ag as V,ay as j,g as i,n as m,q as s,F as n,G as d,bh as w,p as B,aB as F,aJ as k,h as x,J as E,aK as U,aL as y,I as C,K as L,bK as N,L as P}from"./index-r0dFAfgr.js";import{T}from"./vue3-treeselect-BJStyJj1.js";import{g as q}from"./index-CaaU9niG.js";const{$message:z}=I(),D={components:{Treeselect:T},props:["config"],data(){return{visible:!1,roleId:"",roleList:[],projects:[],selectProjects:[],areaNormalizer(l){return{id:l.id,label:l.name}},rules:{roleId:[{required:!0,message:"请选择角色",trigger:"change"}]}}},created(){q().then(l=>{console.log(l.data),this.roleList=l.data}),V().then(l=>{this.projects=l.data,this.settree(this.projects)})},methods:{settree(l){for(const e of l)delete e.leaf,!e.children||e.children.length===0?delete e.children:this.settree(e.children)},handleChange(l,e){console.log(l,e)},UploadFile(l){if(!this.roleId){z.warning("请选择角色");return}console.log(l,"UploadFileUploadFile");const e=l.file,r=new window.FormData;r.append("file",e),this.config.click(r,this.roleId,this.selectProjects.join(",")),this.visible=!1}}},K={class:"deviceImportBtn"},G={class:"dialog-footer"};function J(l,e,r,R,o,c){const a=E,u=U,f=y,p=C,_=j("Treeselect"),g=L,h=N,b=P;return i(),m("div",K,[s(a,{onClick:e[0]||(e[0]=t=>o.visible=!0)},{default:n(()=>{var t;return[d(w((t=r.config)==null?void 0:t.text),1)]}),_:1}),s(b,{modelValue:o.visible,"onUpdate:modelValue":e[4]||(e[4]=t=>o.visible=t),title:"用户导入",width:"30%",class:"alarm-design","close-on-click-modal":!1},{footer:n(()=>[B("span",G,[s(h,{class:"deviceImportBtn",action:"action","show-file-list":!1,"http-request":c.UploadFile,"on-change":c.handleChange},{default:n(()=>[s(a,{type:"primary"},{default:n(()=>e[5]||(e[5]=[d("上传文件")])),_:1})]),_:1},8,["http-request","on-change"]),s(a,{onClick:e[3]||(e[3]=t=>o.visible=!1)},{default:n(()=>e[6]||(e[6]=[d("取 消")])),_:1})])]),default:n(()=>[s(g,{rules:o.rules,"label-position":"top"},{default:n(()=>[s(p,{label:"选择角色：",style:"width:100%;"},{default:n(()=>[s(f,{modelValue:o.roleId,"onUpdate:modelValue":e[1]||(e[1]=t=>o.roleId=t),style:{width:"100%"},prop:"roleId",placeholder:"请选择角色",class:"item-input-select"},{default:n(()=>[(i(!0),m(F,null,k(o.roleList,t=>(i(),x(u,{key:t.id.id,label:t.name,value:t.id.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"选择要赋予的项目："},{default:n(()=>[s(_,{modelValue:o.selectProjects,"onUpdate:modelValue":e[2]||(e[2]=t=>o.selectProjects=t),options:o.projects,normalizer:o.areaNormalizer,multiple:!0,placeholder:"选择项目","no-options-text":"无数据","no-results-text":"无匹配数据",clearable:!1},null,8,["modelValue","options","normalizer"])]),_:1})]),_:1},8,["rules"])]),_:1},8,["modelValue"])])}const A=v(D,[["render",J],["__scopeId","data-v-06620cb6"]]);export{A as default};
