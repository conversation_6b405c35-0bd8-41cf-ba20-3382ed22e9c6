/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.*;
import org.thingsboard.server.common.data.lot.LotCard;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.LOT_CARD_NAME)
public class LotCardEntity extends BaseSqlEntity<LotCard> {


    @Type(type = "json")
    @Column(name = ModelConstants.ASSET_ADDITIONAL_INFO_PROPERTY)
    private JsonNode additionalInfo;


    @Column(name = ModelConstants.REPAIR_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.REPAIR_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.REPAIR_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.REPAIR_STATUS)
    private String status;

    @Column(name = ModelConstants.LOT_CARD_ID)
    private String cardId;

    @Column(name = ModelConstants.NUMBER)
    private String number;

    @Column(name = ModelConstants.OPERATOR)
    private String operator;

    @Column(name = ModelConstants.DATA_FLOW)
    private String data_flow;

    public LotCardEntity() {
    }

    public LotCardEntity(LotCard lotCard) {
        if (lotCard.getId() != null) {
            this.id = toString(lotCard.getId().getId());
        }
        if (lotCard.getDeviceId() != null) {
            this.deviceId = toString(lotCard.getDeviceId().getId());
        }
        if (lotCard.getTenantId() != null) {
            this.tenantId = toString(lotCard.getTenantId().getId());
        }
        this.createTime = lotCard.getCreateTime();
        this.additionalInfo = lotCard.getAdditionalInfo();
        this.status = lotCard.getStatus();
        this.cardId=lotCard.getCcid();
        this.number=lotCard.getNumber();
        this.operator=lotCard.getOperator();
        this.data_flow=lotCard.getData_flow();
    }

    @Override
    public LotCard toData() {
        LotCard lotCard= new LotCard(new LotCardId(getId()));
        lotCard.setDeviceId(new DeviceId(toUUID(deviceId)));
        lotCard.setCreateTime(createTime);
        lotCard.setTenantId(new TenantId(toUUID(tenantId)));
        lotCard.setAdditionalInfo(additionalInfo);
        lotCard.setOperator(operator);
        lotCard.setCcid(cardId);
        lotCard.setStatus(status);
        lotCard.setNumber(number);
        lotCard.setData_flow(data_flow);
        return lotCard;
    }
}
