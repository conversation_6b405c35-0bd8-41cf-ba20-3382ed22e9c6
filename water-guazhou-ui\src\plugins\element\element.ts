// import ElementPlus from 'element-plus'
// import 'element-plus/lib/theme-chalk/index.css'
// import locale from 'element-plus/lib/locale/lang/zh-cn'
// import 'dayjs/locale/zh-cn'
// import locale from 'element-plus/lib/locale'
// locale.use(lang)
import { ElMessage, ElMessageBox } from 'element-plus'
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export default app => {
  // app.use(ElementPlus, { locale })
  app.config.globalProperties.$message = ElMessage
  app.provide('$message', ElMessage)
  app.provide('$confirm', ElMessageBox.confirm)
}
