package org.thingsboard.server.controller.waterSource.inspection;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.GeneralTaskProcessingAndCompleteCount;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskSaveRequest;
import org.thingsboard.server.dao.circuit.CircuitTaskService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.Date;

@IStarController
@RequestMapping("/api/sp/circuitTask")
public class CircuitTaskController extends BaseController {
    @Autowired
    private CircuitTaskService service;


    @GetMapping
    public IPage<CircuitTask> findAllConditional(CircuitTaskPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/processingAndCompleteCount")
    public GeneralTaskProcessingAndCompleteCount processingAndCompleteCount() throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId());
        return service.CircuitTaskProcessingAndCompleteCount(userId);
    }


    @PostMapping
    public CircuitTask save(@RequestBody CircuitTaskSaveRequest req) {
        String startTimeStr = DateUtils.date2Str(req.getStartTime(), "yyyy-MM-dd");
        String endTimeStr = DateUtils.date2Str(req.getEndTime(), "yyyy-MM-dd");

        req.setStartTime(DateUtils.str2Date(startTimeStr + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        req.setEndTime(DateUtils.str2Date(endTimeStr + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitTaskSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {

        return service.delete(id);
    }

    @PostMapping("/{id}/receive")
    public boolean receive(@PathVariable String id) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return service.receive(id, userId);
    }

    @PostMapping("/{id}/sendVerify/{auditUserId}")
    public boolean sendVerify(@PathVariable String id, @PathVariable String auditUserId) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return service.sendVerify(id, auditUserId, userId);
    }

    @PostMapping("/{id}/verify/{allow}")
    public boolean verify(@PathVariable String id, @PathVariable boolean allow) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return service.verify(id, allow, userId);
    }

    @GetMapping("statusCount")
    public JSONObject statusCount() throws ThingsboardException {
        return service.statusCount(getTenantId());
    }
}