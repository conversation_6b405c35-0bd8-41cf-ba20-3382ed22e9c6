package org.thingsboard.server.dao.sql.revenue;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.ReadMeterDataDTO;
import org.thingsboard.server.dao.model.sql.revenue.CopyDataReadMeterData;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataReadMeterData;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface CopyDataReadMeterDataMapper extends BaseMapper<CopyDataReadMeterData> {
    List<PipeCopyDataReadMeterData> findAllByTime(@Param("start") Date start, @Param("end") Date end);

    BigDecimal sumByTime(@Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId);

    List<JSONObject> sumByPartitionId(@Param("partitionIdList") List<String> partitionIdList, @Param("start") Long start, @Param("end") Long end, @Param("partitionType") String partitionType, @Param("tenantId") String tenantId);

    List<CopyDataReadMeterData> getListByPartitionId(@Param("partitionIdList") List<String> partitionIdList, @Param("start") Long start, @Param("end") Long end, @Param("partitionType") String partitionType, @Param("tenantId") String tenantId);

    List<ReadMeterDataDTO> getListByCustCodeList(@Param("custCodeList") List<String> custCodeList, @Param("start") Long start, @Param("end") Long end, @Param("tenantId") String tenantId);
}
