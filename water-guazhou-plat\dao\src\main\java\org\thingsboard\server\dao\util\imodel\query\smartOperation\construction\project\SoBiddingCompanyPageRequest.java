package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoBiddingCompanyPageRequest extends AdvancedPageableQueryEntity<SoBiddingCompany, SoBiddingCompanyPageRequest> {
    // 工程编号
    @NotNullOrEmpty
    private String projectCode;

}
