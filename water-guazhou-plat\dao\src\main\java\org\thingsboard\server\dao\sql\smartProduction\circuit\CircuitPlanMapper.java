package org.thingsboard.server.dao.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlan;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlanResponse;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitPlanPageRequest;

@Mapper
public interface CircuitPlanMapper extends BaseMapper<CircuitPlan> {
    IPage<CircuitPlanResponse> findByPage(CircuitPlanPageRequest request);

    boolean update(CircuitPlan entity);

    boolean save(CircuitPlan entity);

}
