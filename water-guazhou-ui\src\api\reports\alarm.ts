import { QueryListParam } from '@/common/types/reports'
import request from '@/plugins/axios'

export const GetAlarmRank = (params: QueryListParam) => request({
  url: '/api/alarm/alarmRank',
  method: 'get',
  params
})

export const GetAlarmRankV2 = (params: QueryListParam) => request({
  url: '/api/alarmV2/alarmCenter/alarmRank',
  method: 'get',
  params
})

export const GetAlarmTrend = (params:QueryListParam) => request({
  url: '/api/alarm/trend',
  method: 'get',
  params
})
export const GetAlarmOverview = (params:QueryListParam) => request({
  url: '/api/alarm/countAlarm/overview',
  method: 'get',
  params
})
export const GetAlarmPriority = (params:QueryListParam) => request({
  url: '/api/alarm/countAlarmByProject',
  method: 'get',
  params
})
