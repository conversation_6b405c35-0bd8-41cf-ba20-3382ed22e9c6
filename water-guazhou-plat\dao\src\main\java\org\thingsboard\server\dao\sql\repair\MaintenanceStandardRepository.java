package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.MaintenanceStandardEntity;

import java.util.List;

public interface MaintenanceStandardRepository extends JpaRepository<MaintenanceStandardEntity, String> {


    @Query("SELECT new MaintenanceStandardEntity(ms.id, ms.name, ms.deviceType, ms.remark, ms.creator, ms.createTime, ms.tenantId) " +
            "FROM MaintenanceStandardEntity ms " +
            "WHERE ms.tenantId = ?2 AND ms.name LIKE %?1% AND ms.deviceType LIKE %?3%")
    Page<MaintenanceStandardEntity> findList(String name, String tenantId, String deviceType, Pageable pageable);

    @Query("SELECT new MaintenanceStandardEntity(ms.id, ms.name, ms.deviceType, ms.remark, ms.creator, ms.createTime, ms.tenantId) " +
            "FROM MaintenanceStandardEntity ms " +
            "WHERE ms.tenantId = ?2 AND ms.name LIKE %?1%")
    List<MaintenanceStandardEntity> findAll(String name, String tenantId);

}
