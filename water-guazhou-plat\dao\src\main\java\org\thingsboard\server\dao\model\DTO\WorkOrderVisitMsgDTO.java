package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;

import java.util.Date;

/**
 * 工单回访DTO
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-11-23
 */
@Data
public class WorkOrderVisitMsgDTO extends WorkOrder {

    private String assignRemark;

    private String resolvingRemark;

    private String arrivingRemark;

    private String submitRemark;

    private String approvedRemark;

    private Date sendTime;

    private String sendStatus;

    private String sendStatusName;

    private Date visitTime;

    private String evaluateName;

    private String evaluateNum;

    private String sendRemark;
}
