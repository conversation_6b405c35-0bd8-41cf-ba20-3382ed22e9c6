import{_ as P}from"./CardTable-rdWOL4_6.js";import{d as V,c as d,r as F,bF as i,s as u,o as D,g as b,n as x,q as c,i as l,p as f,F as R,db as q,bh as O,an as N,cE as U,al as B,aj as j,bD as $,C as A}from"./index-r0dFAfgr.js";import{_ as J}from"./CardSearch-CB_HNR-Q.js";import{a as M}from"./statisticalAnalysis-D5JxC4wJ.js";import{p as z}from"./printUtils-C-AxhDcd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const G={class:"wrapper"},H={key:0,class:"suggestion-card"},K={class:"suggestion-header"},L={class:"suggestion-content"},Q=V({__name:"index",setup(X){const g=d(),m=d(),p=d(""),w=F({defaultParams:{year:[i().format("YYYY"),i().format("YYYY")]},filters:[{type:"yearrange",label:"选择年份",field:"year",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>_(),svgIcon:u(B)},{text:"导出",perm:!0,type:"warning",svgIcon:u(j),click:()=>T()},{perm:!0,text:"打印",type:"success",svgIcon:u($),click:()=>v()}]}]}),s=F({loading:!1,dataList:[],columns:[{prop:"name",label:"站点名称",minWidth:120,align:"center"},{prop:"time",label:"数据年份",minWidth:100,align:"center"},{prop:"unitConsumption",label:"吨水电耗（kWh/吨）",minWidth:150,align:"center"},{prop:"totalFlow",label:"取水量（千吨）",minWidth:130,align:"center"},{prop:"energy",label:"用电量（千kWh）",minWidth:140,align:"center"}],operations:[],showSummary:!1,operationWidth:"150px",indexVisible:!0,pagination:{hide:!0}}),C=e=>{if(!e||e.length===0)return"";let t=e[0],o=parseFloat(e[0].rawUnitConsumption)||1/0;return e.forEach(r=>{const n=parseFloat(r.rawUnitConsumption)||1/0;n<o&&n>0&&(o=n,t=r)}),t&&o<1/0?`通过对比分析，${t.name}的泵机效率最高（吨水电耗：${o.toFixed(2)} kWh/吨），建议作为运行优化参考标准。`:""},T=()=>{var e;(e=m.value)==null||e.exportTable()},v=()=>{z({title:"比泵分析报告",data:s.dataList,titleList:s.columns})},_=()=>{var r;s.loading=!0;const t=(((r=g.value)==null?void 0:r.queryParams)||{}).year||[],o={start:i(t[0]).startOf("year").valueOf(),end:i(t[1]).endOf("year").valueOf(),queryType:"year"};M(o).then(n=>{const y=(n.data.data||[]).map(a=>{const h=parseFloat(a.unitConsumption)||0,k=parseFloat(a.totalFlow)||0,S=parseFloat(a.energy)||0,W=parseFloat(a.lastTimeEnergy)||0,Y=parseFloat(a.lastTimeTotalFlow)||0,E=parseFloat(a.differenceValue)||0,I=parseFloat(a.changeRate)||0;return{...a,name:a.name||"未知站点",time:a.time||t[0]||new Date().getFullYear().toString(),unitConsumption:h.toFixed(2),totalFlow:(k/1e3).toFixed(2),energy:(S/1e3).toFixed(2),lastTimeEnergy:(W/1e3).toFixed(2),lastTimeTotalFlow:(Y/1e3).toFixed(2),differenceValue:E.toFixed(2),changeRate:I.toFixed(2)+"%",rawUnitConsumption:h}});p.value=C(y),s.dataList=y,s.loading=!1}).catch(n=>{console.error("获取数据失败:",n),s.loading=!1})};return D(()=>{_()}),(e,t)=>{const o=J,r=U,n=P;return b(),x("div",G,[c(o,{ref_key:"cardSearch",ref:g,config:l(w)},null,8,["config"]),l(p)?(b(),x("div",H,[f("div",K,[c(r,{class:"suggestion-icon"},{default:R(()=>[c(l(q))]),_:1}),t[0]||(t[0]=f("span",{class:"suggestion-title"},"分析建议",-1))]),f("div",L,O(l(p)),1)])):N("",!0),c(n,{ref_key:"refTable",ref:m,class:"card-table",config:l(s)},null,8,["config"])])}}}),le=A(Q,[["__scopeId","data-v-ba7d6140"]]);export{le as default};
