import { request } from '@/plugins/axios/gis'

/**
 * 管网重叠检查
 * @param params
 * @returns
 */
export const CheckPipeOverlaps = (params: {
  /** 管网管线 */
  layer: string
}) => {
  return request({
    url: '/api/gis/CheckOverlaps',
    method: 'post',
    params
  })
}
/**
 * 超短线检查
 * @param params
 * @returns
 */
export const CheckPipeOverShort = (params: {
  /** 管网管线 */
  layer: string
  /** {rings:[[[x,y]]]} */
  rangeJson: string
  /** 2 */
  minLength: number
}) => {
  return request({
    url: '/api/gis/CheckLessLenPipe',
    method: 'post',
    params
  })
}
/**
 * 孤立点检查
 * @param params
 * @returns
 */
export const CheckIsolatedPoint = (params: {
  /** 图层名 */
  layer: string
}) => {
  return request({
    url: '/api/gis/CheckIsolatedPoint',
    method: 'post',
    params
  })
}
/**
 * 孤立组件检查
 * @param params
 * @returns
 */
export const CheckIsolatedLine = (params: {
  /** 上游水源点，多个用逗号分隔 */
  flagOIDs: string
  /** 分析的图层名 */
  layer: string
}) => {
  return request({
    url: '/api/gis/CheckIsolatedLine',
    method: 'post',
    params
  })
}
/**
 * 相交检查
 * @param params
 * @returns
 */
export const CheckIntersectLine = (params: {
  /** 图层名 */
  layer: string
}) => {
  return request({
    url: '/api/gis/CheckIntersectLine',
    method: 'post',
    params
  })
}
