/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.*;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.menu.MenuCustomerService;
import org.thingsboard.server.dao.menu.MenuTenantService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/menu")
public class MenuCustomerController extends BaseController {

    @Autowired
    private MenuCustomerService menuCustomerService;
    @Autowired
    private MenuTenantService menuTenantService;

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/getRootId", method = RequestMethod.GET)
    public MenuCustomerId getRootId() {
        return new MenuCustomerId(ModelConstants.MENU_CUSTOMER_ROOT);
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/buildMenuCustomer", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_MENU_ADD)
    public List<MenuCustomer> buildMenuCustomer() throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        if (user.getAuthority() == Authority.CUSTOMER_USER) {
            throw new ThingsboardException("You don't have permission to perform this operation!",
                    ThingsboardErrorCode.PERMISSION_DENIED);
        }
        List<MenuTenant> all = menuTenantService.findAll(user.getTenantId());
        return checkNotNull(menuCustomerService.saveMenuCustomer(all, user.getTenantId()));
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/saveMenuCustomer", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_MENU_ADD)
    public MenuCustomer saveMenuCustomer(@RequestBody Menu menu) throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        checkUser(user);
        // 校验type
        if (!menuCustomerService.checkType(menu.getType(), user.getTenantId())){
            throw new ThingsboardException("You don't have permission to create this type!",
                    ThingsboardErrorCode.PERMISSION_DENIED);
        }

        return checkNotNull(menuCustomerService.saveMenuCustomer(menu, user.getTenantId()));
    }

    /**
     * 校验权限
     * @param user user
     */
    private void checkUser(SecurityUser user) throws ThingsboardException {
        if (user.getAuthority() != Authority.TENANT_ADMIN && user.getAuthority() != Authority.TENANT_SYS) {
            throw new ThingsboardException("You don't have permission to perform this operation!",
                    ThingsboardErrorCode.PERMISSION_DENIED);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/customer/findMenuByCurrentUser", method = RequestMethod.GET)
    public List<Menu> findMenuByCurrentUser() throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        if (user.getAuthority() == Authority.TENANT_ADMIN
                || user.getAuthority() == Authority.TENANT_SYS
                || user.getAuthority() == Authority.SYS_ADMIN) {
            List<Menu> menuList = menuCustomerService.findByTenantId(user.getTenantId());
            return checkNotNull(menuList);
        } else {
            return checkNotNull(menuCustomerService.findCustomerMenuByTenantId(user.getId(),user.getTenantId()));
        }

    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/customer/findMenuByTenantApplication", method = RequestMethod.GET)
    public List<Menu> findMenuByTenantApplication(@RequestParam String tenantApplicationId) throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        try {
            if (user.getAuthority() == Authority.TENANT_ADMIN
                    || user.getAuthority() == Authority.TENANT_SYS
                    || user.getAuthority() == Authority.SYS_ADMIN) {
                List<Menu> menuList = menuCustomerService.findByTenantApplication(tenantApplicationId, user.getTenantId());
                return menuList;
            } else {
                return menuCustomerService.findCustomerMenuByTenantApplication(user.getId(), tenantApplicationId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }

    }

    @PreAuthorize("hasAuthority('SYS_ADMIN')")
    @RequestMapping(value = "/customer/findByTenantId/{tenantId}", method = RequestMethod.GET)
    public List<Menu> findByTenantId(@PathVariable("tenantId")String tenantId) throws ThingsboardException {
        checkParameter("tenantId", tenantId);
        SecurityUser user = getCurrentUser();
        checkUser(user);

        return checkNotNull(menuCustomerService.findByTenantId(new TenantId(toUUID(tenantId))));
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/getTree", method = RequestMethod.GET)
    public List<MenuPoolVO> getTree() throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        return checkNotNull(menuCustomerService.getTree(user.getTenantId()));
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/getTreeByTenantApplication", method = RequestMethod.GET)
    public List<MenuPoolVO> getTreeByTenantApplication(@RequestParam String tenantApplicationId) throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        return menuCustomerService.getTreeByTenantApplication(tenantApplicationId, user.getTenantId());
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/getTypes", method = RequestMethod.GET)
    public List<MenuTypeVO> getTypes() throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        return checkNotNull(menuCustomerService.getTypes(user.getTenantId()));
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @RequestMapping(value = "/customer/deleteMenu/{id}", method = RequestMethod.DELETE)
    @SysLog(detail = DataConstants.OPERATING_TYPE_MENU_DEL)
    public void deleteMenu(@PathVariable("id") String strId) throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        checkUser(user);
        MenuCustomerId id = new MenuCustomerId(toUUID(strId));
        menuCustomerService.deleteMenu(id,user.getTenantId());
    }


}
