<template>
  <div class="custom-pagination-wrapper">
    <el-pagination
      v-if="!props.config?.hide"
      :current-page="config?.page || 1"
      :page-size="config?.limit || 20"
      :class="config?.align || 'left'"
      :pager-count="config?.pagerCount"
      :layout="config?.layout || 'total,sizes, prev, pager, next, jumper'"
      :total="config?.total || 0"
      :page-sizes="config?.pageSize || [5, 10, 20, 50, 100]"
      @size-change="handleSize"
      @current-change="handlePage"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

const props = defineProps<{
  config?: IPagination
  refreshData?:(pageSize: { page: number; size: number }) => any
}>()
const state = reactive<{ page: number; size: number }>({
  page: props.config?.page || 1,
  size: props.config?.limit || 20
})
const handlePage = (page: number) => {
  state.page = page
  if (props.refreshData) {
    props.refreshData(state)
  } else {
    props.config?.handlePage && props.config.handlePage(page)
  }
}
const handleSize = (size: number) => {
  state.size = size
  if (props.refreshData) {
    props.refreshData(state)
  } else {
    props.config?.handleSize && props.config.handleSize(size)
  }
}
</script>

<style lang="scss" scoped>
.custom-pagination-wrapper {
  // 共享分页样式
  :deep(.el-pagination) {
    padding: 0;
    font-weight: normal !important;
    font-size: 13px;
    margin-top: 8px;
    
    .el-pagination__total,
    .el-pagination__sizes {
      margin-right: 16px;
      color: #606266;
    }
    
    .el-select .el-input .el-input__inner {
      border-radius: 4px;
      height: 28px;
      line-height: 28px;
      color: #606266;
      border-color: #dcdfe6;
      transition: border-color 0.3s;
      
      &:hover, &:focus {
        border-color: #409EFF;
      }
    }
    
    .el-pagination__jump {
      margin-left: 16px;
      color: #606266;
      
      .el-pagination__editor.el-input {
        margin: 0 5px;
        width: 40px;
        
        .el-input__inner {
          height: 28px;
          border-radius: 4px;
          transition: border-color 0.3s;
          
          &:hover, &:focus {
            border-color: #409EFF;
          }
        }
      }
    }
    
    button.btn-prev,
    button.btn-next,
    button.btn-quicknext,
    button.btn-quickprev {
      background-color: #f5f7fa !important;
      border-radius: 4px !important;
      color: #606266 !important;
      min-width: 30px !important;
      height: 28px !important;
      transition: all 0.3s !important;
      border: 1px solid transparent !important;
      margin: 0 2px !important;
      
      &:hover {
        color: #409EFF !important;
        background-color: #ecf5ff !important;
        border-color: #d9ecff !important;
      }
      
      &:disabled {
        background-color: #f5f7fa !important;
        color: #c0c4cc !important;
        cursor: not-allowed !important;
      }
    }
    
    .el-pager li {
      background-color: #f5f7fa !important;
      color: #606266 !important;
      border-radius: 4px !important;
      min-width: 30px !important;
      height: 28px !important;
      line-height: 28px !important;
      font-weight: normal !important;
      margin: 0 3px !important;
      transition: all 0.3s !important;
      border: 1px solid transparent !important;
      
      &:hover {
        color: #409EFF !important;
        background-color: #ecf5ff !important;
        border-color: #d9ecff !important;
      }
      
      &.is-active {
        background-color: #409EFF !important;
        color: #fff !important;
        font-weight: bold !important;
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25) !important;
        transform: translateY(-1px) !important;
      }
    }
  }

  .left {
    text-align: left;
    margin-top: 8px;
  }

  .right {
    text-align: right;
    justify-content: flex-end;
    margin-top: 8px;
  }
}
</style>
