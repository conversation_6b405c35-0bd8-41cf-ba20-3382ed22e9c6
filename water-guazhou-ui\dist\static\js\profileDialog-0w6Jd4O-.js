import{C as u,M as g,ag as _,ck as h,ay as b,g as j,h as C,F as s,p as P,q as o,G as c,I as k,K as V,J as y,L as E}from"./index-r0dFAfgr.js";import{T as x}from"./vue3-treeselect-BJStyJj1.js";import{i as I}from"./index-CaaU9niG.js";const{$message:T}=g(),z={name:"ProfilerDialog",components:{Treeselect:x},props:["tableConfig"],data(){return{props:{multiple:!0},areaNormalizer(t){return{id:t.id,label:t.name}},projects:[],selectProjects:[]}},computed:{visible(){return this.tableConfig.visible}},created(){_().then(t=>{this.projects=t.data,this.settree(this.projects),this.tableConfig.single&&h("USER",this.tableConfig.userIds[0]).then(e=>{e.data.forEach(r=>{this.$refs.treeSelect.addValue(r)})})})},methods:{settree(t){for(const e of t)delete e.leaf,!e.children||e.children.length===0?delete e.children:this.settree(e.children)},getSelectNode(){console.log(this.selectProjects)},clickAssignProjectsToUsers(){this.tableConfig.close();const t={};t.projectIds=this.selectProjects,t.userIds=this.tableConfig.userIds,I(t).then(()=>{T({type:"success",message:"操作成功"})})}}},N={class:"dialog-footer"};function U(t,e,r,v,l,a){const d=b("treeselect"),m=k,p=V,n=y,f=E;return j(),C(f,{modelValue:a.visible,"onUpdate:modelValue":e[1]||(e[1]=i=>a.visible=i),title:"权限范围",width:"30%",class:"alarm-design","close-on-click-modal":!1},{footer:s(()=>[P("span",N,[o(n,{size:"small",type:"primary",onClick:a.clickAssignProjectsToUsers},{default:s(()=>e[2]||(e[2]=[c("保存")])),_:1},8,["onClick"]),o(n,{size:"small",onClick:r.tableConfig.close},{default:s(()=>e[3]||(e[3]=[c("取 消")])),_:1},8,["onClick"])])]),default:s(()=>[o(p,null,{default:s(()=>[o(m,{label:"选择要赋予的项目"},{default:s(()=>[o(d,{ref:"treeSelect",modelValue:l.selectProjects,"onUpdate:modelValue":e[0]||(e[0]=i=>l.selectProjects=i),options:l.projects,normalizer:l.areaNormalizer,multiple:!0,placeholder:"选择项目","no-results-text":"无匹配数据","no-options-text":"无数据",clearable:!1},null,8,["modelValue","options","normalizer"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}const D=u(z,[["render",U]]);export{D as default};
