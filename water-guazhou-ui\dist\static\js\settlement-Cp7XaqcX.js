import{d as S,M as E,c as u,a8 as w,s as v,r as f,x as l,a9 as _,o as N,g as M,n as O,q as r,i as p,F as R,b6 as j,b7 as B}from"./index-r0dFAfgr.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as Y}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{I as y}from"./common-CvK_P_ao.js";import{W as I,X as U,Y as V,g as W,Z as A}from"./manage-BReaEVJk.js";import{g as q}from"./projectManagement-CDcrrCQ1.js";import{S as h}from"./data-Dv9-Tstw.js";import z from"./detail-DEo1RlcF.js";import{f as $}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const G={class:"wrapper"},Me=S({__name:"settlement",setup(H){const{$btnPerms:c}=E(),g=u(),m=u(),b=u(),x=u({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:w(()=>i.projectType)},{label:"创建时间",field:"time",type:"daterange",format:"x"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:y.DOWNLOAD,click:()=>{I().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","工程结算.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:v(B),click:()=>{var e;(e=g.value)==null||e.resetForm(),n()}},{perm:!0,text:"查询",icon:y.QUERY,click:()=>n()}]}]}),s=f({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"预算金额",prop:"estimateCost"},{label:"结算金额",prop:"cost"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>$(e.createTime,"YYYY-MM-DD HH:mm:ss")},{label:"工作状态",prop:"status",tag:!0,tagColor:e=>{var t;return((t=h.find(a=>a.value===e.status))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=h.find(a=>a.value===e.status))==null?void 0:t.label}}],operationWidth:"360px",operations:[{disabled:e=>!e.status,isTextBtn:!1,text:"详情",perm:c("RoleManageEdit"),click:e=>{var t;i.selected=e,(t=b.value)==null||t.openDrawer()}},{disabled:e=>e.status!==null,isTextBtn:!1,type:"primary",text:"添加结算",perm:c("RoleManageEdit"),click:e=>{T(e)}},{disabled:e=>e.status==="COMPLETED"||e.status===null,isTextBtn:!1,type:"success",text:"编辑结算",perm:c("RoleManageEdit"),click:e=>D(e)},{disabled:e=>e.status!=="PROCESSING",isTextBtn:!1,text:"完成",perm:c("RoleManageEdit"),click:e=>{U(e.constructionCode).then(t=>{t.data.code===200?l.success("已完成"):l.warning("完成失败"),n()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{s.pagination.page=e,s.pagination.limit=t,n()}}}),o=f({title:"添加工程预算信息",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),V(e).then(a=>{var d;o.submitting=!1,a.data.code===200?(l.success(t+"成功"),(d=m.value)==null||d.closeDialog(),n()):l.warning(t+"失败")}).catch(a=>{o.submitting=!1,l.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"工程类别",field:"constructionTypeName",disabled:!0},{xs:12,type:"input",label:"结算人",field:"processUser",rules:[{required:!0,message:"请输入结算人"}]},{xs:12,type:"number",label:"结算金额(万元)",field:"cost",min:0},{type:"textarea",label:"地址",field:"address"},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),C=f({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),T=e=>{var t;o.title="添加工程结算",i.DesignTubeLength=[],o.defaultValue={...e||{},cost:0},(t=m.value)==null||t.openDialog()},D=e=>{var t;o.title="编辑工程结算",i.DesignTubeLength=[],o.defaultValue={...e||{}},(t=m.value)==null||t.openDialog()},i=f({projectList:[],projectType:[],selected:{},DesignTubeLength:[],getOptions:()=>{q({page:1,size:-1}).then(e=>{i.projectList=_(e.data.data.data||[],"children",{label:"name",value:"code"})}),W({page:1,size:-1}).then(e=>{i.projectType=_(e.data.data.data||[],"children")})}}),n=async()=>{var t;const e={size:s.pagination.limit||20,page:s.pagination.page||1,...((t=g.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.fromTime=e.time[0],e.toTime=e.time[1],delete e.time),A(e).then(a=>{s.dataList=a.data.data.data||[],s.pagination.total=a.data.data.total||0})};return N(()=>{n(),i.getOptions()}),(e,t)=>{const a=F,d=Y,k=P,L=j;return M(),O("div",G,[r(a,{ref_key:"refSearch",ref:g,config:p(x)},null,8,["config"]),r(d,{config:p(s),class:"card-table"},null,8,["config"]),r(k,{ref_key:"refForm",ref:m,config:p(o)},null,8,["config"]),r(L,{ref_key:"refDetail",ref:b,config:p(C)},{default:R(()=>[r(z,{config:p(i).selected,show:12},null,8,["config"])]),_:1},8,["config"])])}}});export{Me as default};
