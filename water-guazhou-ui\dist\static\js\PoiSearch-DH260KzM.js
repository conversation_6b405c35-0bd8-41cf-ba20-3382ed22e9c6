import{d as b,r as k,g as n,n as f,p as C,q as l,F as g,i as p,aB as V,aJ as q,h as x,aw as B,al as T,aK as E,aL as L,J as S,C as I}from"./index-r0dFAfgr.js";import{w as P}from"./Point-WxyopZva.js";import{g as U,n as W}from"./MapView-DaoQedLH.js";import{u as z}from"./useWidgets-BRE-VQU9.js";import{g as A}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";/* empty css                                                                      */import{a as F}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{q as H}from"./utils-D5nxoMq3.js";import"./widget-BcWKanF2.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";const J={class:"tools-temp-wrapper"},M={id:"tool-search-poi",class:"esri-widget"},N=b({__name:"PoiSearch",props:{view:{}},setup(v,{expose:h}){const a=v,o=k({value:"",loading:!1,options:[],queryType:"1"}),_=m=>{a.view&&(m?(o.loading=!0,H({keyWord:m,queryType:o.queryType}).then(t=>{var i;o.options=((i=t.data.pois)==null?void 0:i.map(r=>({label:r.name,value:r.hotPointID,data:r})))||[]}).finally(()=>{o.loading=!1})):o.options=[])},d=()=>{var s,e,u,c;const m=o.options.find(y=>y.value===o.value);if(!m)return;const t=(s=m.data.lonlat)==null?void 0:s.split(","),i=new P({longitude:t==null?void 0:t[0],latitude:t==null?void 0:t[1],spatialReference:(e=a.view)==null?void 0:e.spatialReference}),r=new U({geometry:i,symbol:new W({url:F()})});(u=a.view)==null||u.graphics.removeAll(),(c=a.view)==null||c.graphics.add(r),A(a.view,r,{avoidHighlight:!0,zoom:16})},{addCustomWidget:w}=z();return h({init:async()=>{a.view&&w(a.view,"tool-search-poi","top-right")}}),(m,t)=>{const i=E,r=L,s=S;return n(),f("div",J,[C("div",M,[l(r,{modelValue:p(o).queryType,"onUpdate:modelValue":t[0]||(t[0]=e=>p(o).queryType=e),placeholder:"Select",class:"poi-selector"},{default:g(()=>[l(i,{label:"poi",value:"1"}),l(i,{label:"地名",value:"7"})]),_:1},8,["modelValue"]),l(r,{modelValue:p(o).value,"onUpdate:modelValue":t[1]||(t[1]=e=>p(o).value=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入地名","remote-show-suffix":"","remote-method":_,loading:p(o).loading,class:B("poi-search"),onChange:d},{default:g(()=>[(n(!0),f(V,null,q(p(o).options,e=>(n(),x(i,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]),l(s,{icon:p(T),type:"primary",class:"poi-button",onClick:d},null,8,["icon"])])])}}}),at=I(N,[["__scopeId","data-v-4fc9efca"]]);export{at as default};
