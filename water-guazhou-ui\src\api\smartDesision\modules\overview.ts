import { request } from '@/plugins/axios';

/**
 * 产销差统计
 * @param params
 * @returns
 */
export const referenceLeakSort = (params: { name?: string }) => {
  return request({
    url: '/api/spp/dma/partition/referenceLeakSort',
    method: 'get',
    params
  });
};

/**
 * 年取水量
 * @param params
 * @returns
 */
export const inWater = (params: { name?: string }) => {
  return request({
    url: '/api/spp/dma/partition/inWater',
    method: 'get',
    params
  });
};
