package org.thingsboard.server.dao.guard;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRecord;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardArrangeMapper;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GuardArrangeServiceImpl implements GuardArrangeService {
    @Autowired
    private GuardArrangeMapper mapper;

    @Autowired
    private GuardArrangePartnerService guardArrangePartnerService;

    @Override
    public GuardArrange findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public Map<String, List<GuardArrange>> findAllConditional(GuardArrangePageRequest request) {
        IPage<GuardArrange> byPage = mapper.findByPage(request.ignorePage());
        return byPage.getRecords().stream()
                .collect(Collectors.toMap(
                        x -> TimeUtils.formatDate(x.getDayTime()), // key
                        x -> {
                            // value
                            List<GuardArrange> list = new ArrayList<>();
                            list.add(x);
                            return list;
                        },
                        (x, y) -> {
                            // combine
                            x.addAll(y);
                            return x;
                        }));
    }

    @Override
    public GuardArrange save(GuardArrangeSaveRequest entity) {
        GuardArrange arrange = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
        List<GuardArrangePartnerSaveRequest> partners = entity.getItems(arrange.getId());
        guardArrangePartnerService.replaceAll(arrange.getId(), partners);
        return arrange;
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean quickArrange(GuardArrangeQuickArrangeRequest req, String userId, String tenantId) {
        List<GuardArrangeQuickArrangeData> arrangeDataList = req.arrange();
        List<String> removedArrangeInfoIdList = mapper.removeOnQuickArrangeAtDate(req.getHitDate());
        guardArrangePartnerService.removeOnArrangeIdIn(removedArrangeInfoIdList);
        boolean success = mapper.quickArrange(arrangeDataList, userId, tenantId) > 0;
        guardArrangePartnerService.autoCompletePendingArrange();
        return success;
    }

    @Override
    public boolean detectArrangeOverride(GuardArrangeQuickArrangeRequest req) {
        req.arrangeCollectHitDateOnly();
        return mapper.detectArrangeOverride(req);
    }

    @Override
    public GuardArrange getCurrentGuard(String tenantId) {
        Long now = System.currentTimeMillis();
        GuardArrange guardRecord = mapper.getCurrentGuard(now, tenantId);

        return guardRecord;
    }

}
