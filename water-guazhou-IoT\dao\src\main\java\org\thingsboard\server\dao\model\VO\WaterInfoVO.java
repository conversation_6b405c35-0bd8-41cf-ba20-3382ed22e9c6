package org.thingsboard.server.dao.model.VO;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class WaterInfoVO {

    // 站点ID
    private String stationId;

    // 站点名称
    private String name;

    // 今日供水量
    private BigDecimal todayWaterSupply;

    // 今日取水量
    private BigDecimal todayWaterTake;

    // 昨日供水量
    private BigDecimal yesterdayWaterSupply;

    // 本月供水量
    private BigDecimal monthWaterSupply;

    // 数据最后更新时间
    private String lastTime;

}
