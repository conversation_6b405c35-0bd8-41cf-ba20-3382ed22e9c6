import { ElMessage } from 'element-plus'

class LodopApp {
  private _URL_WS1: string

  private _URL_WS2: string

  private _URL_HTTP1: string

  private _URL_HTTP2: string

  private _URL_HTTP3: string

  private _CreatedOKLodopObject: any

  private _CLodopIsLocal: any

  private _LoadJsState: any

  _LOODOP: any

  constructor(properties: {
    URL_WS1?: string
    URL_WS2?: string
    URL_HTTP1?: string
    URL_HTTP2?: string
    URL_HTTP3?: string
  }) {
    const MainJS = 'CLodopfuncs.js'
    this._URL_WS1 = properties.URL_WS1 || 'ws://localhost:8000/' + MainJS // ws用8000/18000
    this._URL_WS2 = properties.URL_WS2 || 'ws://localhost:18000/' + MainJS
    this._URL_HTTP1 = properties.URL_HTTP1 || 'http://localhost:8000/' + MainJS // http用8000/18000
    this._URL_HTTP2 = properties.URL_HTTP2 || 'http://localhost:18000/' + MainJS
    this._URL_HTTP3 = properties.URL_HTTP3 || 'https://localhost.lodop.net:8443/' + MainJS // https用8000/8443
    this._CreatedOKLodopObject = undefined
    this._CLodopIsLocal = undefined
    this._LoadJsState = undefined
  }

  static needCLodop() {
    try {
      const ua = navigator.userAgent
      if (
        ua.match(/Windows\sPhone/i)
        || ua.match(/iPhone|iPod|iPad/i)
        || ua.match(/Android/i)
        || ua.match(/Edge\D?\d+/i)
      ) return true
      const verTrident = ua.match(/Trident\D?\d+/i)
      const verIE = ua.match(/MSIE\D?\d+/i)
      let verOPR: any = ua.match(/OPR\D?\d+/i)
      let verFF: any = ua.match(/Firefox\D?\d+/i)
      const x64 = ua.match(/x64/i)
      if (!verTrident && !verIE && x64) return true
      if (verFF) {
        verFF = verFF[0].match(/\d+/)
        if (verFF[0] >= 41 || x64) return true
      } else if (verOPR) {
        verOPR = verOPR[0].match(/\d+/)
        if (verOPR[0] >= 32) return true
      } else if (!verTrident && !verIE) {
        let verChrome: any = ua.match(/Chrome\D?\d+/i)
        if (verChrome) {
          verChrome = verChrome[0].match(/\d+/)
          if (verChrome[0] >= 41) return true
        }
      }
      return false
    } catch (err) {
      return true
    }
  }

  private getLodop(oOBJECT, oEMBED) {
    const strFontTag = "<br><font color='#FF00FF'>打印控件"
    const strLodopInstall = strFontTag
      + "未安装!点击这里<a href='install_lodop32.exe' target='_self'>执行安装</a>"
    const strLodopUpdate = strFontTag
      + "需要升级!点击这里<a href='install_lodop32.exe' target='_self'>执行升级</a>"
    const strLodop64Install = strFontTag
      + "未安装!点击这里<a href='install_lodop64.exe' target='_self'>执行安装</a>"
    const strLodop64Update = strFontTag
      + "需要升级!点击这里<a href='install_lodop64.exe' target='_self'>执行升级</a>"
    const strCLodopInstallA = "<br><font color='#FF00FF'>Web打印服务CLodop未安装启动，点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>下载执行安装</a>"
    const strCLodopInstallB = "<br>（若此前已安装过，可<a href='CLodop.protocol:setup' target='_self'>点这里直接再次启动</a>）"
    // const strCLodopUpdate = "<br><font color='#FF00FF'>Web打印服务CLodop需升级!点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>执行升级</a>"
    const strLodop7FontTag = "<br><font color='#FF00FF'>Web打印服务Lodop7"
    const strLodop7HrefX86 = "点击这里<a href='Lodop7_Linux_X86_64.tar.gz' target='_self'>下载安装</a>(下载后解压，点击lodop文件开始执行)"
    const strLodop7HrefARM = "点击这里<a href='Lodop7_Linux_ARM64.tar.gz'  target='_self'>下载安装</a>(下载后解压，点击lodop文件开始执行)"
    const strLodop7Install_X86 = strLodop7FontTag + '未安装启动，' + strLodop7HrefX86
    const strLodop7Install_ARM = strLodop7FontTag + '未安装启动，' + strLodop7HrefARM
    const strLodop7Update_X86 = strLodop7FontTag + '需升级，' + strLodop7HrefX86
    const strLodop7Update_ARM = strLodop7FontTag + '需升级，' + strLodop7HrefARM
    const strInstallOK = '，成功后请刷新本页面或重启浏览器。</font>'
    try {
      const isWinIE = /MSIE/i.test(navigator.userAgent)
        || /Trident/i.test(navigator.userAgent)
      const isWinIE64 = isWinIE && /x64/i.test(navigator.userAgent)
      const isLinuxX86 = /Linux/i.test(navigator.platform) && /x86/i.test(navigator.platform)
      const isLinuxARM = /Linux/i.test(navigator.platform) && /aarch/i.test(navigator.platform)

      if (LodopApp.needCLodop() || isLinuxX86 || isLinuxARM) {
        try {
          this._LOODOP = window.getCLodop()
        } catch (err) {
          //
        }
        if (!this._LOODOP && this._LoadJsState !== 'complete') {
          if (!this._LoadJsState) ElMessage.warning('未曾加载Lodop主JS文件，请先调用loadCLodop过程.')
          else ElMessage.warning('网页还没下载完毕，请稍等一下再操作.')
          return
        }
        let strAlertMessage
        if (!this._LOODOP) {
          if (isLinuxX86) strAlertMessage = strLodop7Install_X86
          else if (isLinuxARM) strAlertMessage = strLodop7Install_ARM
          else strAlertMessage = strCLodopInstallA + (this._CLodopIsLocal ? strCLodopInstallB : '')
          // document.body.innerHTML = strAlertMessage + strInstallOK + document.body.innerHTML
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: strAlertMessage + strInstallOK
          })
          return
        }
        if (isLinuxX86 && this._LOODOP.CVERSION < '7.0.4.3') strAlertMessage = strLodop7Update_X86
        else if (isLinuxARM && this._LOODOP.CVERSION < '7.0.4.3') strAlertMessage = strLodop7Update_ARM
        // else if (CLODOP.CVERSION < '6.5.7.1') strAlertMessage = strCLodopUpdate

        if (strAlertMessage) {
          // document.body.innerHTML = strAlertMessage + strInstallOK + document.body.innerHTML
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: strAlertMessage + strInstallOK
          })
        }
      } else {
        //= =如果页面有Lodop插件就直接使用,否则新建:==
        if (oOBJECT || oEMBED) {
          if (isWinIE) this._LOODOP = oOBJECT
          else this._LOODOP = oEMBED
        } else if (!this._CreatedOKLodopObject) {
          this._LOODOP = document.createElement('object')
          this._LOODOP.setAttribute('width', 0)
          this._LOODOP.setAttribute('height', 0)
          this._LOODOP.setAttribute(
            'style',
            'position:absolute;left:0px;top:-100px;width:0px;height:0px;'
          )
          if (isWinIE) {
            this._LOODOP.setAttribute(
              'classid',
              'clsid:2105C259-1E0C-4534-8141-A753534CB4CA'
            )
          } else this._LOODOP.setAttribute('type', 'application/x-print-lodop')
          document.documentElement.appendChild(this._LOODOP)
          this._CreatedOKLodopObject = this._LOODOP
        } else this._LOODOP = this._CreatedOKLodopObject
        //= =Lodop插件未安装时提示下载地址:==
        if (!this._LOODOP || !this._LOODOP.VERSION) {
          // document.body.innerHTML = (isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK + document.body.innerHTML
          ElMessage({
            dangerouslyUseHTMLString: true,
            message:
              (isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK
          })
          return this._LOODOP
        }
        if (this._LOODOP.VERSION < '6.2.2.6') {
          // document.body.innerHTML = (isWinIE64 ? strLodop64Update : strLodopUpdate) + strInstallOK + document.body.innerHTML
          ElMessage({
            dangerouslyUseHTMLString: true,
            message:
              (isWinIE64 ? strLodop64Update : strLodopUpdate) + strInstallOK
          })
        }
      }
      //= ==如下空白位置适合调用统一功能(如注册语句、语言选择等):=======================

      //= ==============================================================================
      return this._LOODOP
    } catch (err: any) {
      ElMessage.warning(err)
    }
  }

  loadCLodop() {
    if (!LodopApp.needCLodop()) return
    // const CLodopIsLocal = !!(this._URL_WS1 + this._URL_WS2).match(/\/\/localho|\/\/127.0.0./i)
    this._LoadJsState = 'loadingA'
    // if (!window.WebSocket && window.MozWebSocket) window.WebSocket = window.MozWebSocket
    // ws方式速度快(小于200ms)且可避免CORS错误,但要求Lodop版本足够新:
    try {
      const WSK1 = new WebSocket(this._URL_WS1)
      WSK1.onopen = () => {
        // setTimeout(this.checkOrTryHttp(), 200)
        this.checkOrTryHttp()
      }
      // eslint-disable-next-line no-eval
      WSK1.onmessage = function (e) {
        // eslint-disable-next-line no-eval
        if (!window.getCLodop) eval(e.data)
      }
      const _URL_WS2 = this._URL_WS2
      WSK1.onerror = () => {
        const WSK2 = new WebSocket(_URL_WS2)
        WSK2.onopen = () => {
          // setTimeout(this.checkOrTryHttp(), 200)
          this.checkOrTryHttp()
        }
        WSK2.onmessage = function (e) {
          // eslint-disable-next-line no-eval
          if (!window.getCLodop) eval(e.data)
        }
        WSK2.onerror = () => {
          this.checkOrTryHttp()
        }
      }
    } catch (e) {
      console.log(e)
      this.checkOrTryHttp()
    }
  }

  checkOrTryHttp() {
    if (window.getCLodop) {
      this._LoadJsState = 'complete'
      return true
    }
    if (this._LoadJsState === 'loadingB' || this._LoadJsState === 'complete') return
    this._LoadJsState = 'loadingB'
    const head = document.head
      || document.getElementsByTagName('head')[0]
      || document.documentElement
    const JS1 = document.createElement('script')
    const JS2 = document.createElement('script')
    const JS3 = document.createElement('script')
    JS1.src = this._URL_HTTP1
    JS2.src = this._URL_HTTP2
    JS3.src = this._URL_HTTP3
    JS1.onload = JS2.onload = JS3.onload = JS2.onerror = JS3.onerror = () => {
      this._LoadJsState = 'complete'
    }
    JS1.onerror = function (e) {
      if (window.location.protocol !== 'https:') {
        head.insertBefore(JS2, head.firstChild)
      } else {
        head.insertBefore(JS3, head.firstChild)
      }
    }
    head.insertBefore(JS1, head.firstChild)
  }
}
export default LodopApp
