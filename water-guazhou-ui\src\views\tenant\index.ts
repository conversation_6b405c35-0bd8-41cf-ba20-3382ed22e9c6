import { postcode, validatePhone } from '@/utils/formValidate'

export const initSysTenantFormColumn = (options: any[]): IFormFieldGroup[] => [
  {
    fields: [
      {
        type: 'input',
        label: '企业名称',
        field: 'title',
        rules: [{ required: true, message: '请填写企业名称' }]
      },
      {
        type: 'input',
        label: '邮箱',
        field: 'email',
        rules: [{ type: 'email', message: '请输入正确邮箱地址', trigger: 'blur' }]
      },
      {
        type: 'select',
        label: '应用',
        field: 'appTypeId',
        options,
        rules: [{ required: true, message: '请选择应用', trigger: 'change' }]
      },
      {
        type: 'input',
        label: '联系手机',
        field: 'phone',
        rules: [{ validator: validatePhone, trigger: 'blur' }]
      },
      {
        type: 'input',
        label: '国家',
        field: 'country',
        rules: [{ max: 20, message: '输入不可超过20位', trigger: 'blur' }]
      },
      {
        type: 'input',
        label: '省',
        field: 'state',
        rules: [{ max: 20, message: '输入不可超过20位', trigger: 'blur' }]
      },
      {
        type: 'input',
        label: '城市',
        field: 'city',
        rules: [{ max: 20, message: '输入不可超过20位', trigger: 'blur' }]
      },
      {
        type: 'input',
        label: '地址',
        field: 'address',
        rules: [
          { required: true, message: '请输入企业地址', trigger: 'blur' },
          { max: 40, message: '输入不可超过40位', trigger: 'blur' }
        ]
      },
      {
        type: 'input-number',
        label: '经度',
        field: 'lgtd',
        rules: [{ required: true, message: '请输入企业经度', trigger: 'blur' }]
      },
      {
        type: 'input-number',
        label: '纬度',
        field: 'latd',
        rules: [{ required: true, message: '请输入企业纬度', trigger: 'blur' }]
      },
      {
        type: 'input',
        label: '邮编',
        field: 'zip',
        rules: [
          { validator: postcode, trigger: 'blur' },
          { max: 20, message: '名称不超过20位', trigger: 'blur' }
        ]
      },
      {
        type: 'input',
        label: '地区',
        field: 'region',
        rules: [{ max: 40, message: '输入不可超过40位', trigger: 'blur' }]
      },
      {
        type: 'input',
        label: '平台名称',
        field: 'platformName',
        aInfo: true,
        rules: [{ max: 40, message: '输入不可超过40位', trigger: 'blur' }]
      },
      {
        type: 'textarea',
        label: '企业简介',
        field: 'companyProfiles',
        aInfo: true,
        rules: [{ max: 200, message: '输入不可超过200位', trigger: 'blur' }]
      },
      {
        type: 'input',
        label: 'App标题',
        field: 'apptitle',
        aInfo: true,
        rules: [{ max: 40, message: '输入不可超过200位', trigger: 'blur' }]
      }
    ]
  }
]
