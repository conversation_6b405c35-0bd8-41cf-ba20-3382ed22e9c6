package org.thingsboard.server.dao.logicalFlow.BO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.thingsboard.server.dao.model.sql.LogicalFlowHistory;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;

import java.util.List;

/**
 * 逻辑流程节点 业务对象
 */
@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LogicalFlowNodeBO extends LogicalFlowNode {

    private String logicalFlowName;
    private List<LogicalFlowNodeBO> children;
    private LogicalFlowNodeBO parentContinuousNodeId;
    private LogicalFlowHistory logicalFlowHistory;
    private Boolean isChildFlow = false;
    // 子流程对应的主流程节点ID
    private String parentNodeId;

    public LogicalFlowNodeBO(LogicalFlowNode logicalFlowNode, List<LogicalFlowNodeBO> children) {
        super.setId(logicalFlowNode.getId());
        super.setType(logicalFlowNode.getType());
        super.setScript(logicalFlowNode.getScript());
        super.setParentId(logicalFlowNode.getParentId());
        super.setLogicalFlowId(logicalFlowNode.getLogicalFlowId());
        super.setParam(logicalFlowNode.getParam());
        super.setOrder(logicalFlowNode.getOrder());
        super.setCreateTime(logicalFlowNode.getCreateTime());
        super.setAdditionalInfo(logicalFlowNode.getAdditionalInfo());
        super.setName(logicalFlowNode.getName());
        super.setTenantId(logicalFlowNode.getTenantId());
        this.children = children;
    }
}
