package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitHandoverRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class CircuitHandoverRecordPageRequest extends AdvancedPageableQueryEntity<CircuitHandoverRecord, CircuitHandoverRecordPageRequest> {
    // 交接班时间
    private String shiftTimeFrom;

    // 交接班时间
    private String shiftTimeTo;

    // 交班人
    private String handoverPerson;

    // 接班人
    private String takeoverPerson;

    // 泵房状态。状态值：正常、异常、危险
    private String pumpRoomStatus;

    // 关键字
    private String keyWord;

    public Date getShiftTimeFrom() {
        return toDate(shiftTimeFrom);
    }

    public Date getShiftTimeTo() {
        return toDate(shiftTimeTo);
    }
}
