import{_ as E}from"./Panel-DyoxrWMd.js";import{d as T,c as d,r as w,b as y,W as V,bB as R,Q as z,ay as q,g as S,n as W,q as _,F as C,i as g,h as $,p as H,an as U,_ as X,C as Y}from"./index-r0dFAfgr.js";import{g as j,b as Q}from"./MapView-DaoQedLH.js";import{d as J,s as K}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import{e as ee,i as te}from"./IdentifyHelper-RJWmLn49.js";import{g as re,a as oe}from"./LayerHelper-Cn-iiqxI.js";import"./Point-WxyopZva.js";import"./project-DUuzYgGl.js";import{s as A,g as ie}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{V as ae}from"./pipeAnalys-BoQOC96l.js";import pe from"./RightDrawerMap-D5PhmGFO.js";import{m as ne}from"./max-CCqK09y5.js";import{m as se}from"./min-ks0CS-3r.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";const me={class:"chart-box"},le=T({__name:"VerticalAnalysis",setup(ce){const D=d(),b=d(),x=d(),v=d(),t=w({vertices:[],curOperate:"",tabs:[],chartOption:null,mounted:!1}),e={soeResult:[],pipeInfo:{ID:[],ZGround:[],Distance:[],Depth:[],Diameter:[],Z:[],Material:[]}},F=d(),k=w({columns:[{label:"设备类型",prop:"name"},{label:"数量",prop:"count",formatter:o=>{var i;return(i=o.data)==null?void 0:i.length}}],dataList:[],pagination:{hide:!0}}),L=w({group:[{fieldset:{desc:"依次点选两条管线"},fields:[{type:"btn-group",btns:[{perm:!0,type:"warning",loading:()=>t.curOperate==="analysing",disabled:()=>t.curOperate==="analysing",text:()=>t.curOperate==="picking"?"选择管线中...":"点选管线",click:()=>G(),styles:{width:"100%"}}]}]},{fieldset:{desc:"分析结果"},fields:[{type:"table",style:{height:"250px"},config:k},{type:"btn-group",itemContainerStyle:{marginTop:"20px",marginBottom:"8px"},btns:[{perm:!0,type:"success",text:()=>t.curOperate==="analysing"?"正在分析...":"开始分析",loading:()=>t.curOperate==="analysing",disabled:()=>t.curOperate==="picking"||t.vertices.length<2,click:()=>P(),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,text:"详细信息",disabled:()=>!t.curOperate||t.curOperate==="picking"||t.curOperate==="analysing",loading:()=>t.curOperate==="detailing",click:()=>Z(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),G=()=>{var o,i;e.view&&(A("crosshair"),t.curOperate="picking",(o=v.value)==null||o.clearDetailData(),e.graphicsLayer=re(e.view,{id:"pipe-analys-across",title:"纵剖面分析"}),(i=e.graphicsLayer)==null||i.removeAll(),e.mapClick=e.view.on("click",async p=>{await M(p)}))},M=async o=>{var i,p,n,c,l,f;if(e.view)try{const r={layerIds:oe(e.view,!0),geometry:o.mapPoint,mapExtent:e.view.extent},a=(i=(await ee(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,te(r))).results)==null?void 0:i.filter(h=>{var I,O;return((O=(I=h.feature)==null?void 0:I.geometry)==null?void 0:O.type)==="polyline"});if(!(a!=null&&a.length)){y.warning("没有查询到管线"),t.curOperate="";return}e.identifyResult=a[0];const m=a[0].feature;if(!m)return;const u=J(m==null?void 0:m.geometry,o.mapPoint);if(!u)return;t.vertices.length>=2&&(t.vertices.length=0,(p=e.graphicsLayer)==null||p.removeAll()),t.vertices.push([u.x,u.y]),m.symbol=K(m.geometry.type),(n=e.graphicsLayer)==null||n.add(m),t.vertices.length===2&&(A(""),t.curOperate="picked",(c=e.mapClick)!=null&&c.remove&&((l=e.mapClick)==null||l.remove()))}catch{(f=e.graphicsLayer)==null||f.removeAll(),t.curOperate=""}},P=async()=>{var o,i,p;if(!e.view){y.error("地图服务未就绪，请稍候再试");return}if(t.vertices.length<2){y.warning("请先选择两条管线，当前选择"+t.vertices.length+"条管线");return}t.curOperate="analysing";try{const n=new j({geometry:new Q({paths:[t.vertices],spatialReference:e.view.spatialReference})}),l=(window.SITE_CONFIG.SITENAME==="qingyang"?ie(n):n).geometry,f=await ae({UserToken:V().gToken||"",X1:l.paths[0][0][0],X2:l.paths[0][1][0],Y1:l.paths[0][0][1],Y2:l.paths[0][1][1],Buffer:20,f:"pjson"});if(e.soeResult=((o=f.data)==null?void 0:o.Values)||[],f.data.Status!=="successed"){y.error(f.data.Msg||"分析失败"),t.curOperate="";return}t.tabs=[],e.pipeInfo.ID=[],e.pipeInfo.ZGround=[],e.pipeInfo.Distance=[],e.pipeInfo.Depth=[],e.pipeInfo.Diameter=[],e.pipeInfo.Z=[],e.pipeInfo.Material=[],e.soeResult.map(r=>{var u;const s=(u=r.PipeLineNameDefinition||r.PipePointNameDefinition)==null?void 0:u.split(":");let a="",m="";if(s.length===2){a=s[0],m=s[1];const h=t.tabs.find(I=>I.label===a);h?h.data.push(m):t.tabs.push({label:a,name:a,data:[m]})}e.pipeInfo.ID.push(r.ID),e.pipeInfo.ZGround.push(r.ZGround),e.pipeInfo.Distance.push(r.Distance),e.pipeInfo.Depth.push(r.Depth),e.pipeInfo.Diameter.push(r.Diameter),e.pipeInfo.Z.push(r.Z),e.pipeInfo.Material.push(r.Material)}),k.dataList=t.tabs,(i=x.value)==null||i.clear(),t.chartOption=null,(p=b.value)==null||p.Open(),t.chartOption=N(),R(()=>{var r;(r=x.value)==null||r.resize()})}catch{y.error("系统错误"),t.curOperate=""}t.curOperate="analysed"},N=()=>{const o=[],i=e.pipeInfo.ZGround.map((r,s)=>{const a=[e.pipeInfo.Distance[s],e.pipeInfo.ZGround[s]];return o.push(a),[e.pipeInfo.Distance[s],e.pipeInfo.Z[s]]}),p=ne(e.pipeInfo.Diameter)||0,n=se(e.pipeInfo.Diameter)||0,c=5,l=10;return{legend:{textStyle:{color:"#666"}},toolbox:{show:!0,feature:{saveAsImage:{show:!0,title:"保存为图片"}}},dataZoom:[{show:!0,type:"inside",start:0,end:100,textStyle:{color:"#fff"}},{start:0,end:100}],tooltip:{trigger:"axis",formatter:r=>{if(r.seriesName==="地面")return"地面点高程："+e.pipeInfo.ZGround[r.dataIndex].toFixed(2);if(r[0].seriesName==="管点")return"管径："+(e.pipeInfo.Diameter[r[0].dataIndex]||"--")+"mm<br/>材质: "+e.pipeInfo.Material[r[0].dataIndex]+"<br/>埋深: "+Number(e.pipeInfo.Depth[r[0].dataIndex].toFixed(2))+"m"}},xAxis:{type:"value",show:!0,position:"bottom",name:"距离",nameLocation:"end",nameTextStyle:{},boundaryGap:[0,0],min:null,max:null,color:"#A9D2E1",axisLabel:{formatter:"{value}m",color:"#666"},splitLine:{show:!1,color:"#00ff00"},splitArea:{show:!1}},yAxis:{name:"高程",type:"value",scale:!0,color:"#A9D2E1",axisLabel:{formatter:"{value}m",color:"#666"},splitArea:{show:!1},splitLine:{lineStyle:{color:"#FFFFFF",opacity:.2,type:"dashed"}}},grid:{left:100},series:[{name:"管点",type:"scatter",tooltip:{trigger:"axis"},legendHoverLink:!0,symbol:"emptyCircle",symbolSize(r){let s;for(let a=0;a<i.length;a++)i[a][1]===r[1]&&(s=a);return p===n?c:c+(e.pipeInfo.Diameter[s]-n)/(p-n)*(l-c)},label:{show:!0,formatter:r=>e.pipeInfo.ID[r.dataIndex],position:"bottom",color:"#fff",align:"right",baseline:"bottom",fontSize:"10px"},itemStyle:{color:"red",borderWidth:2,borderColor:"#070707"},emphasis:{itemStyle:{color:"#aa0000",borderWidth:2,borderColor:"#070707"}},data:i},{name:"地面",type:"line",clickable:!0,data:o,markePoint:{},areaStyle:{color:"#AE6F39",type:"default"},lineStyle:{width:2,color:"#aaaaaa"},label:{show:!0,formatter:r=>r.data[0]<e.pipeInfo.Distance[r.dataIndex-1]+5&&r.dataIndex>0?"":r.data[1].toFixed(1)},markeLine:{data:[{type:"average",name:"平均高程"}]},stack:null,xAxisIndexs:0,yAxisIndex:0,symbol:null,symbolSize:6,symbolRotate:null,showAllSymbol:!1,dataFilter:"nearst",legendHoverLink:!0}]}},Z=()=>{var o;t.curOperate="detailing",(o=v.value)==null||o.refreshDetail(t.tabs)},B=o=>{e.view=o,t.mounted=!0};return z(()=>{var o,i,p,n;(o=b.value)==null||o.Close(),(i=e.graphicsLayer)==null||i.removeAll(),(p=e.drawAction)==null||p.destroy(),(n=e.drawer)==null||n.destroy()}),(o,i)=>{const p=X,n=q("VChart"),c=E;return S(),W("div",{ref_key:"refBox",ref:D,class:"vertical-page"},[_(pe,{ref_key:"refMap",ref:v,title:"纵剖面分析","full-content":!0,onMapLoaded:B,onDetailRefreshed:i[0]||(i[0]=l=>g(t).curOperate="analysed")},{default:C(()=>[_(p,{ref_key:"refForm",ref:F,config:g(L)},null,8,["config"]),g(t).mounted?(S(),$(c,{key:0,ref_key:"refChartPanel",ref:b,telport:g(D),"custom-class":"gis-across-analys-panel",title:"纵剖面分析结果"},{default:C(()=>[H("div",me,[_(n,{ref_key:"refChart",ref:x,option:g(t).chartOption},null,8,["option"])])]),_:1},8,["telport"])):U("",!0)]),_:1},512)],512)}}}),wr=Y(le,[["__scopeId","data-v-4751e577"]]);export{wr as default};
