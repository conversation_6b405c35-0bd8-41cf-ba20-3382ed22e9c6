import ScaleBar from '@arcgis/core/widgets/ScaleBar.js'

export const useScaleBar = () => {
  let scaleBar: __esri.ScaleBar | undefined
  const init = (view: __esri.MapView, widgetPosition?: string) => {
    scaleBar = new ScaleBar({
      view,
      unit: 'metric',
      style: 'ruler'
    }) as __esri.ScaleBar
    view.ui?.add(scaleBar, widgetPosition || 'bottom-left')
    return {
      scaleBar
    }
  }
  const destroy = () => {
    scaleBar?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}

export default useScaleBar
