const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/wfsTransactionUtils-YwEioX_z.js","static/js/AnimatedLinesLayer-B2VbV4jv.js","static/js/Point-WxyopZva.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/GraphicsLayer-DTrBRwJQ.js","static/js/dehydratedFeatures-CEuswj7y.js","static/js/enums-B5k73o5q.js","static/js/plane-BhzlJB-C.js","static/js/sphere-NgXH-gLx.js","static/js/mat3f64-BVJGbF0t.js","static/js/mat4f64-BCm7QTSd.js","static/js/quatf64-QCogZAoR.js","static/js/elevationInfoUtils-5B4aSzEU.js","static/js/quat-CM9ioDFt.js","static/js/TileLayer-B5vQ99gG.js","static/js/ArcGISCachedService-CQM8IwuM.js","static/js/TilemapCache-BPMaYmR0.js","static/js/Version-Q4YOKegY.js","static/js/QueryTask-B4og_2RG.js","static/js/executeForIds-BLdIsxvI.js","static/js/sublayerUtils-bmirCD0I.js","static/js/imageBitmapUtils-Db1drMDc.js","static/js/scaleUtils-DgkF6NQH.js","static/js/ExportImageParameters-BiedgHNY.js","static/js/floorFilterUtils-DZ5C6FQv.js","static/js/WMSLayer-mTaW758E.js","static/js/crsUtils-DAndLU68.js","static/js/ExportWMSImageParameters-CGwvCiFd.js","static/js/BaseTileLayer-DM38cky_.js","static/js/commonProperties-DqNQ4F00.js","static/js/project-DUuzYgGl.js","static/js/QueryEngineResult-D2Huf9Bb.js","static/js/quantizationUtils-DtI9CsYu.js","static/js/WhereClause-CNjGNHY9.js","static/js/executionError-BOo4jP8A.js","static/js/utils-DcsZ6Otn.js","static/js/generateRendererUtils-Bt0vqUD2.js","static/js/projectionSupport-BDUl30tr.js","static/js/json-Wa8cmqdu.js","static/js/utils-dKbgHYZY.js","static/js/LayerView-BSt9B8Gh.js","static/js/Container-BwXq1a-x.js","static/js/definitions-826PWLuy.js","static/js/enums-BDQrMlcz.js","static/js/Texture-BYqObwfn.js","static/js/Util-sSNWzwlq.js","static/js/pixelRangeUtils-Dr0gmLDH.js","static/js/number-Q7BpbuNy.js","static/js/coordinateFormatter-C2XOyrWt.js","static/js/earcut-BJup91r2.js","static/js/normalizeUtilsSync-NMksarRY.js","static/js/TurboLine-CDscS66C.js","static/js/enums-L38xj_2E.js","static/js/util-DPgA-H2V.js","static/js/RefreshableLayerView-DUeNHzrW.js","static/js/vec2-Fy2J07i2.js","static/js/wfsUtils-DXofo3da.js","static/js/fieldconfig-Bk3o1wi7.js"])))=>i.map(i=>d[i]);
import{c as re,X as vt,u as ot,l as Ae,bE as Et,b as u,S as Fe,a3 as be,bH as st,d as St,r as Ce,o as Tt,Q as wt,g as Ie,h as Se,F as ne,p as H,q as ee,i as C,cs as me,aw as ge,j as At,d8 as Dt,n as Gt,aJ as Ct,aB as Pt,G as Te,da as xe,an as Ye,bh as Ot,bB as Qe,_ as Rt,J as Nt,bk as xt,aH as _t,de as Lt,C as $t}from"./index-r0dFAfgr.js";import{_ as Bt}from"./Panel-DyoxrWMd.js";import{P as ie}from"./gisSetting-CQEP-Q3N.js";import{c as de,q as _e,k as Jt,s as ye,m as Mt,n as Wt,l as Ht,a as Le,d as Ut,v as Vt,E as ze}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{g as qt,v as Xe,b as je,H as ke}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import{a as Ke}from"./IdentifyHelper-RJWmLn49.js";import{a as Yt,f as Ze,g as fe,r as ue}from"./LayerHelper-Cn-iiqxI.js";import{w as Qt}from"./Point-WxyopZva.js";import"./project-DUuzYgGl.js";import{s as De,i as zt}from"./ToolHelper-BiiInOzB.js";import{u as nt}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{S as Xt}from"./coordinateFormatter-C2XOyrWt.js";import{updateFeatureAttributes as Oe,addFeature as jt}from"./wfsTransactionUtils-YwEioX_z.js";import kt from"./RightDrawerMap-D5PhmGFO.js";import{E as ae,c as le,d as k}from"./config-DncLSA-r.js";import{GetFieldConfig as Kt}from"./fieldconfig-Bk3o1wi7.js";import"./IdentifyResult-4DxLVhTm.js";import{a as Zt,i as er}from"./QueryHelper-ILO3qZqg.js";import{f as tr}from"./DateFormatter-Bm9a68Ax.js";import{QueryByPolygon as rr}from"./wfsUtils-DXofo3da.js";import{i as or}from"./config-fy91bijz.js";import{g as we,i as sr,f as et,a as nr,b as it,l as ir,p as ar}from"./index-CI2c9VIl.js";import"./v4-SoommWqA.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./identify-4SBo5EZk.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-DeAQQ1ej.js";function lr(n){if(!n)throw new Error("geojson is required");switch(n.type){case"Feature":return at(n);case"FeatureCollection":return cr(n);case"Point":case"LineString":case"Polygon":case"MultiPoint":case"MultiLineString":case"MultiPolygon":case"GeometryCollection":return $e(n);default:throw new Error("unknown GeoJSON type")}}function at(n){var r={type:"Feature"};return Object.keys(n).forEach(function(c){switch(c){case"type":case"properties":case"geometry":return;default:r[c]=n[c]}}),r.properties=lt(n.properties),r.geometry=$e(n.geometry),r}function lt(n){var r={};return n&&Object.keys(n).forEach(function(c){var f=n[c];typeof f=="object"?f===null?r[c]=null:Array.isArray(f)?r[c]=f.map(function(o){return o}):r[c]=lt(f):r[c]=f}),r}function cr(n){var r={type:"FeatureCollection"};return Object.keys(n).forEach(function(c){switch(c){case"type":case"features":return;default:r[c]=n[c]}}),r.features=n.features.map(function(c){return at(c)}),r}function $e(n){var r={type:n.type};return n.bbox&&(r.bbox=n.bbox),n.type==="GeometryCollection"?(r.geometries=n.geometries.map(function(c){return $e(c)}),r):(r.coordinates=ct(n.coordinates),r)}function ct(n){var r=n;return typeof r[0]!="object"?r.slice():r.map(function(c){return ct(c)})}function Re(n){for(var r=we(n),c=0,f=1,o,b;f<r.length;)o=b||r[0],b=r[f],c+=(b[0]-o[0])*(b[1]+o[1]),f++;return c>0}function ur(n,r){if(r=r||{},!sr(r))throw new Error("options is invalid");var c=r.reverse||!1,f=r.mutate||!1;if(!n)throw new Error("<geojson> is required");if(typeof c!="boolean")throw new Error("<reverse> must be a boolean");if(typeof f!="boolean")throw new Error("<mutate> must be a boolean");f===!1&&(n=lr(n));var o=[];switch(n.type){case"GeometryCollection":return it(n,function(b){Pe(b,c)}),n;case"FeatureCollection":return et(n,function(b){et(Pe(b,c),function(w){o.push(w)})}),nr(o)}return Pe(n,c)}function Pe(n,r){var c=n.type==="Feature"?n.geometry.type:n.type;switch(c){case"GeometryCollection":return it(n,function(f){Pe(f,r)}),n;case"LineString":return tt(we(n),r),n;case"Polygon":return rt(we(n),r),n;case"MultiLineString":return we(n).forEach(function(f){tt(f,r)}),n;case"MultiPolygon":return we(n).forEach(function(f){rt(f,r)}),n;case"Point":case"MultiPoint":return n}}function tt(n,r){Re(n)===r&&n.reverse()}function rt(n,r){Re(n[0])!==r&&n[0].reverse();for(var c=1;c<n.length;c++)Re(n[c])===r&&n[c].reverse()}const dr=n=>{if(!n)return null;switch(n.type){case"Point":return new Qt({x:n.coordinates[0],y:n.coordinates[1],spatialReference:{wkid:3857}});case"LineString":return new je({paths:[n.coordinates],spatialReference:{wkid:3857}});case"MultiLineString":return new je({paths:n.coordinates,spatialReference:{wkid:3857}});case"Polygon":return new Xe({rings:n.coordinates,spatialReference:{wkid:3857}});case"MultiPolygon":return new Xe({rings:n.coordinates[0],spatialReference:{wkid:3857}});default:return console.error("不支持的几何类型:",n.type),null}},pr=(n,r)=>{const c=n,f=E=>x.value.group[0].fields[0].loading=E,o=re([]),b=re([]),w=re(),x=re({labelPosition:"top",group:[{id:"layer",fieldset:{desc:"选择图层"},fields:[{loading:!1,type:"tree",checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",options:[],handleCheckChange:(E,d)=>{d&&(c.value&&(c.value.dataForm.layerid=[E.value]),w.value=E,r==null||r(E,d))}}]}]});return{toggleLoading:f,getLayerInfo:E=>{if(E)if(window.GIS_SERVER_SWITCH){const d=x.value.group[0].fields[0];let y=(E==null?void 0:E.layerViews.items[0].layer.sublayers).items.map(s=>({label:s.name,value:s.name,layername:s.name,type:s.type,spatialReferences:s.spatialReferences}));d.options=y}else o.value=Yt(E),vt(o.value).then(d=>{var e,N;b.value=((N=(e=d.data)==null?void 0:e.result)==null?void 0:N.rows)||[];const S=x.value.group[0].fields[0],y=b.value.filter(p=>p.geometrytype==="esriGeometryPoint").map(p=>({label:p.layername,value:p.layerid,data:p})),s=b.value.filter(p=>p.geometrytype==="esriGeometryPolyline").map(p=>({label:p.layername,value:p.layerid,data:p}));S&&(S.options=[{label:"管线类",value:-2,children:s,disabled:!0},{label:"管点类",value:-1,children:y,disabled:!0}]),c.value&&(c.value.dataForm.layerid=o.value.slice(0,1),w.value=b.value.find(p=>p.layerid===o.value[0]))})},FormConfig:x,layerIds:o,layerInfos:b,curLayerInfo:w}},yr=(n,r)=>{const c=(E,d)=>{f.value.options=[{label:E,value:E,type:"layer",children:d.map(S=>({label:S.attributes.OBJECTID,value:S.attributes.OBJECTID,type:"graphic"}))}]},f=re({type:"tree",options:[],nodeClick:r}),o=n,b=re({labelWidth:130,group:[{fields:[]}],labelPosition:"right",submit:async(E,d)=>{var y;const S=de({geometry:d.graphic.geometry,attributes:{...d.graphic.attributes||{},...E,UPDATEDBY:(y=ot().user)==null?void 0:y.name,UPDATEDDATE:Ae().format(Et)}});b.value.submitting=!0;try{if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer WFS-T 模式进行属性更新");let s="";typeof d.layerid=="string"?s=d.layerid:s=`${d.layerid}`,console.log("图层名称:",s);const e=await Oe(s,[S],S.attributes,!1);if(console.log("GeoServer更新响应:",e),!(e.data&&e.data.includes("<wfs:totalUpdated>")))throw console.error("WFS-T 更新要素失败:",e),new Error("更新要素失败")}else{console.log("使用 ArcGIS 模式进行属性更新");const s=typeof d.layerid=="string"?parseInt(d.layerid)||0:d.layerid;await Ze(s,{updateFeatures:[S]})}u.success("操作成功"),ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.UPDATE}OBJECTID为${S==null?void 0:S.attributes.OBJECTID}的${d.layername}的属性信息`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),d.successCallback()}catch(s){console.error("更新操作失败:",s),u.error("操作失败")}finally{b.value.submitting=!1}}});return{refForm:o,FormConfig:b,resetForm:async(E,d)=>{const S=await or(E,d==null?void 0:d.attributes);if(b.value.group[0].fields=[...S],!!o.value&&(o.value.dataForm={...(d==null?void 0:d.attributes)||{}},S.map(y=>{y.type==="date"&&y.field&&(o.value.dataForm[y.field]=tr(d==null?void 0:d.attributes[y.field],st))}),(d==null?void 0:d.geometry.type)==="polyline")){const y=_e(d.geometry.paths[0],"meters",d.geometry.spatialReference);o.value.dataForm.PIPELENGTH=y}},submitForm:E=>{var d;(d=o.value)==null||d.Submit(E)},TreeData:f,resetTreeData:c,submitEdit:async(E,d,S,y)=>{if(E===void 0){u.warning("请选择编辑图层");return}Fe("应用到空间数据库？","提示信息").then(async()=>{var s,e;try{if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer WFS-T 模式进行编辑操作");let N="";if(typeof E=="string"?N=E:N=`${E}`,console.log("图层名称:",N),d.addFeatures&&d.addFeatures.length>0){console.log("添加要素:",d.addFeatures);const{addFeature:p}=await be(async()=>{const{addFeature:v}=await import("./wfsTransactionUtils-YwEioX_z.js");return{addFeature:v}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])),P={addFeatureResults:[],updateFeatureResults:[],deleteFeatureResults:[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]};for(const v of d.addFeatures)try{const I=await p(N,v);console.log("WFS-T 添加要素响应:",I);const se=new DOMParser().parseFromString(I.data,"text/xml"),M=se.getElementsByTagName("wfs:totalInserted"),$=M.length>0?parseInt(M[0].textContent||"0"):0,W=se.getElementsByTagName("ogc:FeatureId"),z=W.length>0?W[0].getAttribute("fid"):"none";if(console.log("插入的要素数量:",$),console.log("要素ID:",z),$>0&&z!=="none"){const B=z;console.log("新添加要素的 ID:",B);let pe=null;if(B){const ve=B.split(".");pe=ve.length>1?ve[1]:B}P.addFeatureResults.push({objectId:pe,success:!0})}else console.error("WFS-T 添加要素失败:",I),P.addFeatureResults.push({success:!1,error:"添加要素失败"})}catch(I){console.error("使用 WFS-T 添加要素时出错:",I),P.addFeatureResults.push({success:!1,error:I.message||"添加要素失败"})}u.success("操作成功"),S==null||S(P)}else if(d.updateFeatures&&d.updateFeatures.length>0){console.log("更新要素:",d.updateFeatures);const p=d.updateFeatures,P={...p[0].attributes},v=Array.isArray(p)?p:p.toArray(),I=await Oe(N,v,P,!0);console.log("GeoServer更新响应:",I);const M=new DOMParser().parseFromString(I.data,"text/xml").getElementsByTagName("wfs:totalUpdated"),$=M.length>0?parseInt(M[0].textContent||"0"):0;if(console.log("更新的要素数量:",$),$>0){const W={updateFeatureResults:v.map(z=>({objectId:z.attributes.OBJECTID,globalId:z.attributes.SID||null,success:!0})),addFeatureResults:[],deleteFeatureResults:[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]};u.success("操作成功"),S==null||S(W)}else console.error("WFS-T 更新要素失败:",I),u.error("操作失败"),y==null||y()}else if(d.deleteFeatures&&d.deleteFeatures.length>0){console.log("删除要素:",d.deleteFeatures);const{deleteFeature:p}=await be(async()=>{const{deleteFeature:I}=await import("./wfsTransactionUtils-YwEioX_z.js");return{deleteFeature:I}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])),P=[],v=Array.isArray(d.deleteFeatures)?d.deleteFeatures:d.deleteFeatures.items||((e=(s=d.deleteFeatures).toArray)==null?void 0:e.call(s))||[d.deleteFeatures];if(!v||v.length===0){console.error("没有要删除的要素"),u.error("删除失败：没有要删除的要素"),y==null||y();return}if(v.forEach(I=>{if(I.attributes){if(I.attributes.OBJECTID)P.push(`${N}.${I.attributes.OBJECTID}`);else if(I.attributes.fid){const O=I.attributes.fid;P.push(O.includes(".")?O:`${N}.${O}`)}else if(I.attributes.id)P.push(`${N}.${I.attributes.id}`);else if(I.id){const O=typeof I.id=="string"?I.id:`${I.id}`;P.push(O.includes(".")?O:`${N}.${O}`)}}}),P.length===0){console.log("无法获取要素 ID，尝试使用基于几何位置的删除方法");try{const{deleteFeatureByGeometry:I}=await be(async()=>{const{deleteFeatureByGeometry:z}=await import("./wfsTransactionUtils-YwEioX_z.js");return{deleteFeatureByGeometry:z}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])),O=await I(N,v);console.log("GeoServer 基于几何位置删除响应:",O);const $=new DOMParser().parseFromString(O.data,"text/xml").getElementsByTagName("wfs:totalDeleted"),W=$.length>0?parseInt($[0].textContent||"0"):0;if(console.log("删除的要素数量:",W),W>0){const z={deleteFeatureResults:v.map((B,pe)=>({objectId:pe+1,globalId:null,success:!0})),addFeatureResults:[],updateFeatureResults:[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]};u.success("删除成功"),S==null||S(z);return}else{console.error("基于几何位置的删除失败:",O),u.error("删除失败"),y==null||y();return}}catch(I){console.error("基于几何位置的删除出错:",I),u.error("删除失败：无法获取要素 ID，请确保要素有有效的 ID"),y==null||y();return}}console.log("要删除的要素 ID:",P);try{const I=await p(N,P);console.log("GeoServer 删除响应:",I);const M=new DOMParser().parseFromString(I.data,"text/xml").getElementsByTagName("wfs:totalDeleted"),$=M.length>0?parseInt(M[0].textContent||"0"):0;if(console.log("删除的要素数量:",$),$>0){const W={deleteFeatureResults:d.deleteFeatures.map(z=>({objectId:z.attributes.OBJECTID,globalId:z.attributes.SID||null,success:!0})),addFeatureResults:[],updateFeatureResults:[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]};u.success("删除成功"),S==null||S(W)}else console.error("WFS-T 删除要素失败:",I),u.error("删除失败"),y==null||y()}catch(I){console.error("WFS-T 删除操作失败:",I),u.error("删除失败"),y==null||y()}}else console.error("未知的编辑操作:",d),u.error("未知的编辑操作"),y==null||y()}else{console.log("使用 ArcGIS 模式进行编辑操作");const N=typeof E=="string"?parseInt(E)||0:E,p=await Ze(N,d);u.success("操作成功"),S==null||S(p)}}catch(N){console.error("编辑操作失败:",N),u.error("操作失败"),y==null||y()}}).catch(()=>{y==null||y()})}}},mr=n=>{let r,c,f,o,b,w;const x=re("rectangle");let F;const E=re(!1),d=(p,P)=>{p&&(E.value=!0,x.value=P||"rectangle",F=p,De("crosshair"),o=fe(F,{id:"pipe-editable",title:"不可编辑区域"}),r==null||r.destroy(),r=zt(F),c==null||c.destroy(),c=r==null?void 0:r.create(x.value,{mode:["circle","rectangle"].indexOf(x.value)!==-1?"freehand":"click"}),b=c==null?void 0:c.on(["vertex-add","cursor-update"],S),w=c==null?void 0:c.on("draw-complete",async v=>{S(v),De(""),E.value=!1,y(),n==null||n()}))},S=p=>{const P=x.value==="circle"?Jt(p.vertices,F==null?void 0:F.spatialReference,ye("polygon",{color:[0,255,0,.2],outlineColor:[0,255,0,1],outlineWidth:1})):x.value==="polygon"?p.vertices.length<3?Mt(p.vertices,F==null?void 0:F.spatialReference,ye("polyline",{color:[0,255,0,1],width:1})):Wt(p.vertices,F==null?void 0:F.spatialReference,ye("polygon",{color:[0,255,0,.2],outlineColor:[0,255,0,1],outlineWidth:1})):Ht(p.vertices,F==null?void 0:F.spatialReference,ye("polygon",{color:[0,255,0,.2],outlineColor:[0,255,0,1],outlineWidth:1}));o==null||o.removeAll(),P&&(o==null||o.add(P)),f=P},y=()=>{if(F&&f&&f.geometry.type==="polygon"){const p=F.extent;if(!p)return;const P=[[p.xmin,p.ymin],[p.xmax,p.ymin],[p.xmax,p.ymax],[p.xmin,p.ymax],[p.xmin,p.ymin]];let v=f.geometry.rings[0];v.push(v[0]),console.log(v),!Re(ir(v))&&x.value==="polygon"&&(v=ur(ar([v]),{reverse:!0}).geometry.coordinates[0]);const I=de({geometry:Le("polygon",[P,v],F==null?void 0:F.spatialReference),symbol:ye("polygon",{color:[87,90,104,.5],outlineColor:[255,255,255,1],outlineWidth:1})});o==null||o.removeAll(),o==null||o.add(I)}};return{drawingEditRange:E,curType:x,destroy:p=>{o&&(p==null||p.map.remove(o)),b==null||b.remove(),w==null||w.remove(),c==null||c.destroy(),r==null||r.destroy(),De("")},clear:()=>{o==null||o.removeAll(),f=void 0},startDraw:d,refresh:y,queryFeatures:async(p,P)=>{if(p===void 0)return[];if(!f)return[];try{console.log("开始查询要素:",p,f);debugger;let v=[];if(window.GIS_SERVER_SWITCH===!0)try{if(console.log("开始执行GeoServer查询:",p,f.geometry),!f.geometry)return console.error("没有有效的几何对象"),[];const I=f.geometry;if(!I.rings||!I.rings[0])return console.error("几何对象没有rings属性或rings为空"),[];const O=I.rings[0];if(O.length>0){const $=O[0],W=O[O.length-1];($[0]!==W[0]||$[1]!==W[1])&&(console.log("多边形不闭合，添加闭合点"),O.push([...$]))}const se=typeof p=="string"&&p.includes(":")?p:`guazhou:${p}`;console.log("使用图层名称进行查询:",se);const M=await rr(se,I.rings[0]);if(console.log("查询结果:",M),M&&M.data&&M.data.features&&Array.isArray(M.data.features)){console.log("查询到的要素数量:",M.data.features.length),v=[];debugger;for(const $ of M.data.features)try{const W=dr($.geometry);if(!W){console.warn("转换几何对象失败:",$.geometry);continue}const z=new qt({id:$.id,type:$.geometry.type,geometry:W,attributes:$.properties||{},symbol:ye(W.type)});v.push(z)}catch(W){console.error("处理GeoJSON要素失败:",W)}}}catch(I){console.error("GeoServer查询失败:",I)}else v=(await Zt(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+p,er({geometry:f==null?void 0:f.geometry,where:"1=1",outFields:["*"]}))).features.map(O=>(O.symbol=ye(O.geometry.type),O));return P&&v.length>0?(console.log("将要素添加到图层:",v.length),P.removeAll(),P.addMany(v)):console.warn("没有要素添加到图层或图层不存在:",v.length,!!P),v}catch(v){return console.error("查询要素失败:",v),[]}}}},gr=n=>{let r,c,f,o,b,w,x;const F=(y,s)=>{o=y;const e=Ut(o==null?void 0:o.geometry,s);!e||!r||(c=de({geometry:e,symbol:ye("point",{color:[255,255,255],outlineColor:[255,0,255],outlineWidth:2})}),r.removeAll(),c&&r.add(c))},E=(y,s,e,N)=>{r==null||r.destroy(),y&&(r=fe(y,{id:"split-layer",title:"拆分点"}),u.info("通过滑动鼠标并点击地图以确定拆分点"),f=s,x=e,w=y==null?void 0:y.on("pointer-move",p=>{const P=y.toMap(p.native);N==null||N(P)}),b=y==null?void 0:y.on("click",p=>{w==null||w.remove(),N==null||N(p.mapPoint),d()}))},d=()=>{Fe("将变更应用到空间数据？","提示信息").then(async()=>{var N,p,P;if(!o||!c||f===void 0){u.error("操作失败");return}const y=new ke({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer+"/"+f}),s=c.geometry,e="JD"+Ae().valueOf();try{u.info("正在保存，请稍候...");const v=o.geometry,I={...o.attributes||{}};delete I.OBJECTID;const O=Le("polyline",[[v.paths[0][0],[s.x,s.y]]],o.geometry.spatialReference);let se=de({geometry:O,attributes:{...I||{},PIPELENGTH:_e(O.paths[0],"meters",o.geometry.spatialReference)}});const M=Le("polyline",[[[s.x,s.y],v.paths[0][1]]],o.geometry.spatialReference);let $=de({geometry:M,attributes:{...I||{},SID:"GW"+(Ae().valueOf()+1),PIPELENGTH:_e(M.paths[0],"meters",o.geometry.spatialReference)}});const W=y.applyEdits({deleteFeatures:[o]}),z=y.applyEdits({addFeatures:[se]}),B=y.applyEdits({addFeatures:[$]}),pe=await Promise.all([W,z,B]);y.destroy(),u.success("保存成功,请及时校核拆分管线的属性"),n==null||n(pe)}catch(v){y.destroy(),console.log(v),u.error("保存失败"),S();return}if(x){const v=new ke({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer+"/"+x.layerid});try{const I=c,O=I.geometry;(P=(p=(N=(await Kt(x.layername)).data)==null?void 0:N.result)==null?void 0:p.rows)==null||P.map(M=>{I.attributes[M.name]=null}),I.attributes={...I.attributes,SID:e,SUBTYPE:"直线点",CREATEDDATE:Ae().format(st),X:O.x,Y:O.y,DIAMETER:o==null?void 0:o.attributes.DIAMETER,MATERIAL:o==null?void 0:o.attributes.MATERIAL,DEPTH:o==null?void 0:o.attributes.DEPTH,LANEWAY:o==null?void 0:o.attributes.LANEWAY,BURYTYPE:o==null?void 0:o.attributes.BURYTYPE},await v.applyEdits({addFeatures:[I]})}catch(I){console.log(I)}v.destroy}S()}).catch(()=>{S()})},S=()=>{b==null||b.remove(),w==null||w.remove(),r==null||r.removeAll()};return{splitLayer:r,splitPoint:c,setSplitPoint:F,init:E,resolveSplite:d,destroy:S}},fr=n=>{const{initSketch:r,destroySketch:c}=nt();let f,o;const b=F=>{o=fe(F,{id:"rect-layer",title:"框选"}),c(),f=r(F,o,{updateCallBack:w,createCallBack:w}),f==null||f.create("rectangle")},w=F=>{if(F.state==="complete"){const E=F.graphics[0];o==null||o.removeAll(),n==null||n(E),x()}},x=()=>{f==null||f.destroy(),o==null||o.removeAll()};return{start:b,destroy:x}},hr=()=>{const n=re(),r=re({type:"tree",options:[],label:"选择基准要素：",nodeClick:f=>n.value=f});return{TreeData:r,resetForm:f=>{r.value.options=f.map(o=>({label:o.attributes.SID,value:o.attributes.OBJECTID,data:o.attributes})),n.value=void 0},curNode:n}},Ir={class:"top-toolbar"},Fr={class:"edit-toogle"},br={class:"edit-panel-content overlay-y"},vr={class:"edit-panel-footer"},Er={class:"edit-panel-content overlay-y"},Sr={class:"edit-panel-footer"},Tr="YYYY-MM-DD",wr=St({__name:"EditPipe",setup(n){const r=re(),c=re(),f=re(),o=re(),b=re(),w=Ce(pr(r,i=>{B(),s.pointEdiable=!1,S.destroy()})),x=mr(()=>B()),F=Ce(yr(b,i=>{var t,m;e.graphic=e.graphics.length?e.graphics.find(h=>h.attributes.OBJECTID===i.value):void 0,e.graphic&&F.resetForm((m=(t=r.value)==null?void 0:t.dataForm.layerid)==null?void 0:m[0],e.graphic).then(()=>{}).catch(h=>{console.log(h)})})),E=hr(),d=gr(i=>{var m,h,T,D,R,a,l;const t=(m=w.curLayerInfo)==null?void 0:m.layername;ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`拆分OBJECTID为${(T=(h=i[0])==null?void 0:h.deleteFeatureResults[0])==null?void 0:T.objectId}的${t},拆分后OBJECTID:[${(R=(D=i[1])==null?void 0:D.addFeatureResults[0])==null?void 0:R.objectId},${(l=(a=i[2])==null?void 0:a.addFeatureResults[0])==null?void 0:l.objectId}]`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B()}),S=Ce(fr(i=>{e.sketch||Ne(),y(i),Je()})),y=i=>{var t,m;debugger;e.clonedGraphics=[],e.graphics=[],(t=e.graphicsLayer)==null||t.graphics.filter(h=>{const T=!!h.attributes;return Vt(h.geometry,i.geometry)&&T}).map(h=>{e.clonedGraphics.push(de({geometry:h.geometry,symbol:h.symbol,attributes:h.attributes})),e.graphics.push(h)}),(m=e.sketch)==null||m.update(e.graphics),W()};let s=Ce({isMounted:!1,curPage:"index",timer:null,editing:!1,useCapture:!0,toolTipBeforeComfirm:!0,addPointDeviceAtCrossPoint:!1,activeDrawType:"",hasSelectedFeature:!1,isPipeLine:!1,mergeable:!1,pointEdiable:!1,copiedAttributes:null,copiedAttributesLayerId:null,copyAttributesMode:!1,isAddingNewFeature:!1,currentAddingFeatureInfo:void 0});const e={graphics:[],clonedGraphics:[],copyAttributesClickHandler:null,attributeQueryClickHandler:null,currentAddingFeatureGraphic:void 0},N=[{perm:!0,text:"框选",isTextBtn:!0,disabled:()=>!s.editing,type:"default",click:()=>S.start(e.view),iconifyIcon:"gis:rectangle-pt"},{perm:!0,text:"编辑节点",isTextBtn:!0,disabled:()=>!s.pointEdiable,type:"default",click:()=>mt(),iconifyIcon:"gis:difference"},{perm:!0,text:"合并",isTextBtn:!0,disabled:()=>!s.mergeable,type:"default",click:()=>gt(),iconifyIcon:"pajamas:merge-request"},{perm:!0,text:"拆分",isTextBtn:!0,disabled:()=>!(s.hasSelectedFeature&&s.isPipeLine),type:"default",click:()=>I(),iconifyIcon:"pajamas:merge-request-close"},{perm:!0,text:"属性",isTextBtn:!0,type:"default",click:()=>O(),iconifyIcon:"material-symbols:info"},{perm:!1,text:"废弃",type:"warning",isTextBtn:!0,disabled:()=>!s.hasSelectedFeature,click:()=>ft(),iconifyIcon:"quill:discard"},{perm:!0,text:"删除",type:"danger",isTextBtn:!0,disabled:()=>!s.hasSelectedFeature,click:()=>pe(),iconifyIcon:"ep:delete"}],p=()=>{var i;s.editing=!0,s.hasSelectedFeature=!1,s.isPipeLine=!1,s.mergeable=!1,e.graphic=void 0,e.graphics=[],e.clonedGraphic=void 0,e.clonedGraphics=[],(i=e.graphicsLayer)==null||i.removeAll(),v()},P=()=>{var i,t;s.editing=!1,(i=e.graphicsLayer)==null||i.removeAll(),e.graphic=void 0,e.graphics=[],x.clear(),s.hasSelectedFeature=!1,s.isPipeLine=!1,s.mergeable=!1,d.destroy(),x.destroy(),S.destroy(),(t=c.value)==null||t.hide()},v=i=>{var t;s.editing&&(u.info("请绘制编辑区域"),x.startDraw(e.view,i),(t=c.value)==null||t.hide())},I=()=>{var m,h;if(!(s.hasSelectedFeature&&s.isPipeLine))return;const i=(h=(m=r.value)==null?void 0:m.dataForm.layerid)==null?void 0:h[0],t=w.layerInfos.find(T=>T.layername==="节点");d.init(e.view,i,t,T=>{d.setSplitPoint(e.graphic,T)})},O=async()=>{var m,h,T,D,R,a;if(!((h=(m=r.value)==null?void 0:m.dataForm.layerid)==null?void 0:h[0])){u.warning("请先选择图层");return}const t=((T=w.curLayerInfo)==null?void 0:T.layername)||"要素";console.log("打开属性查询面板:",t);try{(D=f.value)==null||D.Close(),o.value&&(o.value.title=`${t}属性查询`),e.graphic?(console.log("显示选中要素属性:",(R=e.graphic.attributes)==null?void 0:R.OBJECTID),await W()):(console.log("没有选中的要素，显示空表单"),b.value&&(b.value.dataForm={}),F.FormConfig&&F.FormConfig.group&&F.FormConfig.group.length>0&&(F.FormConfig.group[0].fields=[{type:"input",label:"提示",field:"hint",placeholder:"请在地图上点击要素以查看和编辑其属性",disabled:!0}])),(a=o.value)==null||a.Open(),s.isAddingNewFeature=!1,e.graphic||se()}catch(l){console.error("显示属性面板时出错:",l),u.error("无法显示属性面板")}},se=()=>{if(!e.view){console.error("视图未初始化，无法启用点击选择功能");return}console.log("启用点击地图选择要素的功能"),e.view.container.style.cursor="crosshair";const i=e.view.on("click",async t=>{var T,D,R,a,l,g,G;if(console.log("地图点击事件:",t),!((D=(T=r.value)==null?void 0:T.dataForm.layerid)==null?void 0:D[0])){u.warning("请先选择图层");return}const h=((R=w.curLayerInfo)==null?void 0:R.layername)||"要素";try{let L=null;if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer 模式查询要素");const U=typeof h=="string"?h:`${h}`;console.log("使用图层名称进行查询:",U);const Y=await Ke(e.view,"/geoserver/guazhou/wms",U,t);if(console.log("GeoServer 查询响应:",Y),Y&&Y.data&&Y.data.features&&Y.data.features.length>0){const _=Y.data.features[0];if(console.log("找到要素:",_),console.log("要素几何信息:",_.geometry),!_.geometry){console.error("要素几何信息为空"),u.warning("要素几何信息为空，无法编辑");return}let X;try{if(_.geometry.type==="Point")X={type:"point",x:_.geometry.coordinates[0],y:_.geometry.coordinates[1],spatialReference:(a=e.view)==null?void 0:a.spatialReference};else if(_.geometry.type==="LineString")X={type:"polyline",paths:[_.geometry.coordinates],spatialReference:(l=e.view)==null?void 0:l.spatialReference};else if(_.geometry.type==="MultiLineString")X={type:"polyline",paths:_.geometry.coordinates,spatialReference:(g=e.view)==null?void 0:g.spatialReference};else if(_.geometry.type==="Polygon")X={type:"polygon",rings:_.geometry.coordinates,spatialReference:(G=e.view)==null?void 0:G.spatialReference};else{console.error("不支持的几何类型:",_.geometry.type),u.warning(`不支持的几何类型: ${_.geometry.type}`);return}console.log("转换后的ArcGIS几何对象:",X)}catch(te){console.error("转换几何信息时出错:",te),u.error("转换几何信息失败");return}const Z=_.properties||{},V=de({geometry:X,attributes:Z});if(console.log("创建的图形对象:",V),!V.geometry){console.error("创建的图形对象几何信息为空"),u.error("创建图形对象失败");return}e.graphic=V,e.graphics=[V],await W();debugger;$(V)}else console.log("未找到要素"),u.warning("未找到要素，请重新点击")}}catch(L){console.error("查询要素时出错:",L),u.error("查询要素失败，请重试")}});e.attributeQueryClickHandler=i,o.value&&(o.value.onClose=()=>{M()})},M=()=>{console.log("禁用点击地图选择要素的功能"),e.view&&(e.view.container.style.cursor="default"),e.attributeQueryClickHandler&&(e.attributeQueryClickHandler.remove(),e.attributeQueryClickHandler=null)},$=i=>{if(!e.graphicsLayer&&e.view&&(e.graphicsLayer=fe(e.view,{id:"pipe-editing",title:"编辑"})),!e.graphicsLayer){console.error("图形图层未初始化，无法高亮显示要素");return}e.graphicsLayer.removeAll();let t=null;if(!i.geometry){console.error("高亮显示失败：几何对象为空");return}if(console.log("高亮显示几何类型:",i.geometry.type),i.geometry.type==="point")t={type:"simple-marker",color:[255,0,0,.5],size:12,outline:{color:[255,0,0,1],width:2}};else if(i.geometry.type==="polyline")t={type:"simple-line",color:[255,0,0,1],width:4};else if(i.geometry.type==="polygon")t={type:"simple-fill",color:[255,0,0,.3],outline:{color:[255,0,0,1],width:2}};else{console.error("不支持的几何类型:",i.geometry.type);return}const m=de({geometry:i.geometry,symbol:t});e.graphicsLayer.add(m)},W=async()=>{var i,t,m;F.resetTreeData((i=w.curLayerInfo)==null?void 0:i.layername,e.graphics),await Qe(),F.resetForm((m=(t=r.value)==null?void 0:t.dataForm.layerid)==null?void 0:m[0],e.graphic).then(()=>{}).catch(h=>{console.log(h)})},z=async()=>{var T,D,R,a;if(!e.graphic){u.warning("没有选中要素");return}const i=(D=(T=r.value)==null?void 0:T.dataForm.layerid)==null?void 0:D[0];if(!i){u.warning("请先选择图层");return}const t=((R=w.curLayerInfo)==null?void 0:R.layername)||"要素";console.log("保存要素属性:",t,(a=e.graphic.attributes)==null?void 0:a.OBJECTID);let m={};if(b.value)try{m=JSON.parse(JSON.stringify(b.value.dataForm||{}))}catch(l){console.error("处理表单数据时出错:",l),m={}}const h={};Object.keys(m).forEach(l=>{const g=m[l];g!=null&&g!==""&&(h[l]=g)}),Fe("此操作将更新要素的属性，是否继续？","提示信息").then(async()=>{var l;try{const g=de({geometry:e.graphic.geometry,attributes:{...e.graphic.attributes,...h}});if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer WFS-T 模式更新属性");const G=typeof i=="string"?i:`${i}`;console.log("图层名称:",G);const L=await Oe(G,[g],h,!1);console.log("GeoServer更新响应:",L);const _=new DOMParser().parseFromString(L.data,"text/xml").getElementsByTagName("wfs:totalUpdated"),X=_.length>0?parseInt(_[0].textContent||"0"):0;console.log("更新的要素数量:",X),X>0?(e.graphic&&(e.graphic.attributes={...e.graphic.attributes,...h},ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.UPDATE}OBJECTID为${e.graphic.attributes.OBJECTID}的${G}的属性信息`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")})),ue(e.view),B(),u.success(`更新${G}属性成功`),(l=o.value)==null||l.Close()):(console.error("WFS-T 更新要素失败:",L),u.error("更新属性失败"))}else{console.log("使用 ArcGIS 模式更新属性");const G=typeof i=="string"?parseInt(i)||0:i;F.submitEdit(G,{updateFeatures:[g]},L=>{var U;console.log("ArcGIS更新响应:",L),e.graphic&&(e.graphic.attributes={...e.graphic.attributes,...h},ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.UPDATE}OBJECTID为${e.graphic.attributes.OBJECTID}的${t}的属性信息`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")})),ue(e.view),B(),u.success(`更新${t}属性成功`),(U=o.value)==null||U.Close()},()=>{u.error("更新属性失败")})}}catch(g){console.error("更新属性时出错:",g),u.error("更新属性失败，请重试")}}).catch(()=>{console.log("用户取消了更新属性操作")})},B=()=>{var i,t;x.queryFeatures((t=(i=r.value)==null?void 0:i.dataForm.layerid)==null?void 0:t[0],e.graphicsLayer)},pe=()=>{var h,T,D,R,a;const i=(h=w.curLayerInfo)==null?void 0:h.layername,t=(R=(D=(T=r.value)==null?void 0:T.dataForm)==null?void 0:D.layerid)==null?void 0:R[0],m=(a=e.sketch)==null?void 0:a.updateGraphics;if(!m||m.length===0){u.warning("没有选中要删除的要素");return}Fe("此操作将删除选中的要素，是否继续？","提示信息").then(async()=>{var l;try{if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer WFS-T 模式删除要素");const g=typeof t=="string"?t:`${t}`;console.log("图层名称:",g);const{deleteFeature:G}=await be(async()=>{const{deleteFeature:te}=await import("./wfsTransactionUtils-YwEioX_z.js");return{deleteFeature:te}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])),L=[],U=Array.isArray(m)?m:m.items||((l=m.toArray)==null?void 0:l.call(m))||[m];if(!U||U.length===0){u.error("没有要删除的要素");return}U.forEach(te=>{const Ge=typeof te.id=="string"?te.id:`${te.id}`;L.push(Ge)}),console.log("要删除的要素 ID:",L);const Y=await G(g,L);console.log("GeoServer 删除响应:",Y);const Z=new DOMParser().parseFromString(Y.data,"text/xml").getElementsByTagName("wfs:totalDeleted"),V=Z.length>0?parseInt(Z[0].textContent||"0"):0;console.log("删除的要素数量:",V),V>0?(ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.DELETE}OBJECTID为${m.map(te=>te.attributes.OBJECTID).join("、")}的${i}`,optionType:k.DELETE}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),e.graphic=void 0,e.graphics=[],F.resetForm(),u.success(`删除${i}成功`)):(console.error("WFS-T 删除要素失败:",Y),u.error("删除要素失败"))}else console.log("使用 ArcGIS 模式删除要素"),F.submitEdit(t,{deleteFeatures:m},g=>{ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.DELETE}OBJECTID为${g.deleteFeatureResults.map(G=>G.objectId).join("、")}的${i}`,optionType:k.DELETE}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),e.graphic=void 0,e.graphics=[],F.resetForm(),u.success(`删除${i}成功`)})}catch(g){console.error("删除要素时出错:",g),u.error("删除要素失败，请重试")}}).catch(()=>{console.log("用户取消了删除操作")})},ve=i=>{var g,G,L,U,Y,_,X,Z,V,te,Ge,We,He,Ue,Ve;if(!((g=i.graphics)!=null&&g.length)){console.warn("没有图形数据");return}const t=i.graphics[0];if(console.log("图形类型:",t.geometry.type,"属性:",t.attributes),s.pointEdiable=!1,e.graphic=t,e.graphics=i.graphics,s.hasSelectedFeature=!1,s.isPipeLine=t.geometry.type==="polyline",i.state==="complete"&&!t.attributes){const q=(L=(G=r.value)==null?void 0:G.dataForm.layerid)==null?void 0:L[0];if(!q){u.error("请先选择图层");return}m(q,typeof q=="string"?q:"新要素",t);return}async function m(q,oe,j){try{console.log("获取图层字段信息:",q);let K={},Q=[];if(window.GIS_SERVER_SWITCH)try{const{GetFieldConfig:ce}=await be(async()=>{const{GetFieldConfig:J}=await import("./wfsUtils-DXofo3da.js");return{GetFieldConfig:J}},__vite__mapDeps([60,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])),A=await ce(q);if(console.log("GeoServer字段信息:",A),A&&A.data&&A.data.featureTypes&&A.data.featureTypes[0]){if(A.data.featureTypes[0].typeName){const J=A.data.featureTypes[0].typeName;if(console.log("图层类型名称:",J),A.data.featureTypes[0].defaultGeometry){const he=A.data.featureTypes[0].defaultGeometry;console.log("图层几何类型:",he);const Ee=w.layerInfos.findIndex(qe=>qe.layerid===q||qe.layername===oe);Ee>=0?(w.layerInfos[Ee].geometrytype=he,console.log("更新图层信息的几何类型:",he)):console.log("未找到图层信息，无法更新几何类型")}}A.data.featureTypes[0].properties&&(Q=A.data.featureTypes[0].properties,Q=Q.filter(J=>J.name!=="geom"&&J.name!=="geometry"),console.log("过滤后的字段:",Q))}}catch(ce){console.error("获取GeoServer字段信息失败:",ce)}else try{const{GetFieldConfig:ce}=await be(async()=>{const{GetFieldConfig:J}=await import("./fieldconfig-Bk3o1wi7.js");return{GetFieldConfig:J}},__vite__mapDeps([61,3,4,1,2,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])),A=await ce(oe);if(console.log("ArcGIS字段信息:",A),A&&A.data&&A.data.result){if(A.data.result.geometryType){const J=A.data.result.geometryType;console.log("ArcGIS图层几何类型:",J);const he=w.layerInfos.findIndex(Ee=>Ee.layerid===q||Ee.layername===oe);he>=0?(w.layerInfos[he].geometrytype=J,console.log("更新图层信息的几何类型:",J)):console.log("未找到图层信息，无法更新几何类型")}A.data.result.rows&&(Q=A.data.result.rows,Q=Q.filter(J=>J.name!=="SHAPE"&&J.name!=="SHAPE.STLength()"&&J.name!=="SHAPE.STArea()"&&J.name!=="OBJECTID"),console.log("过滤后的字段:",Q))}}catch(ce){console.error("获取ArcGIS字段信息失败:",ce)}(!Q||Q.length===0)&&console.warn("未获取到字段信息，使用默认字段"),s.copiedAttributes&&s.copiedAttributesLayerId===q&&(K={...K,...s.copiedAttributes}),j.attributes=K,s.isAddingNewFeature=!0,s.currentAddingFeatureInfo={layerId:q,layerName:oe},e.currentAddingFeatureGraphic=j,h(q,oe,j,Q)}catch(K){console.error("获取字段定义失败:",K);const Q={};j.attributes=Q,s.isAddingNewFeature=!0,s.currentAddingFeatureInfo={layerId:q,layerName:oe},e.currentAddingFeatureGraphic=j,h(q,oe,j,[])}}function h(q,oe,j,K){var Q;if(console.log("打开属性编辑面板"),e.graphic=j,b.value&&(b.value.dataForm={...j.attributes},F.FormConfig&&F.FormConfig.group&&F.FormConfig.group.length>0)){const ce=K.filter(A=>A.name!=="OBJECTID"&&A.name!=="SID"&&A.name!=="objectid"&&A.name!=="sid").map(A=>{let J="input";return A.type==="date"||A.type==="datetime"?J="date":A.type==="number"||A.type==="integer"||A.type==="double"?J="number":A.type==="boolean"&&(J="switch"),{type:J,label:A.alias||A.name,field:A.name,placeholder:`请输入${A.alias||A.name}`}});F.FormConfig.group[0].fields=ce}(Q=o.value)==null||Q.Open()}if(i.state==="start"&&(e.clonedGraphic=de({geometry:t.geometry,symbol:t.symbol,attributes:t.attributes}),s.hasSelectedFeature=!0,((U=o.value)==null?void 0:U.visible)===!0&&(F.resetTreeData((Y=w.curLayerInfo)==null?void 0:Y.layername,e.graphics),F.resetForm((X=(_=r.value)==null?void 0:_.dataForm.layerid)==null?void 0:X[0],t))),s.mergeable=!1,i.graphics.length>1){s.mergeable=!s.isPipeLine,s.pointEdiable=!1;let q=!0;if(i.graphics.map(oe=>{const j=e.clonedGraphics.find(K=>K.attributes.OBJECTID===K.attributes.OBJECT);q=!!j&&ze(oe.geometry,j.geometry)}),q)return}else s.pointEdiable=s.isPipeLine;if(((Z=e.clonedGraphic)==null?void 0:Z.geometry)&&ze((V=e.clonedGraphic)==null?void 0:V.geometry,t.geometry)||i.state!=="complete"||i.aborted)return;const D=(te=w.curLayerInfo)==null?void 0:te.layername,R=(He=(We=(Ge=r.value)==null?void 0:Ge.dataForm)==null?void 0:We.layerid)==null?void 0:He[0];if(!D||!R){console.error("无法确定图层名称或ID");return}const a=(Ue=e.clonedGraphic)==null?void 0:Ue.geometry,l=(Ve=i.graphics)==null?void 0:Ve[0].geometry;Fe("此操作将更新要素的空间位置，是否继续？","提示信息").then(async()=>{var q,oe;try{if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer 模式更新要素位置");const j={},K=await Oe(D,e.graphics,j,!0);console.log("更新要素位置响应:",K);const A=new DOMParser().parseFromString(K.data,"text/xml").getElementsByTagName("wfs:totalUpdated"),J=A.length>0?parseInt(A[0].textContent||"0"):0;console.log("更新的要素数量:",J),J>0?(ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.UPDATE}OBJECTID为${((oe=(q=i.graphics)==null?void 0:q[0].attributes)==null?void 0:oe.OBJECTID)||"未知"}的${D}的空间位置，更新前位置: ${(a==null?void 0:a.type)==="point"?JSON.stringify([a==null?void 0:a.x,a==null?void 0:a.y]):a.type==="polyline"?JSON.stringify((a==null?void 0:a.paths)||[]):""},更新后的位置：${l.type==="point"?JSON.stringify([l==null?void 0:l.x,l==null?void 0:l.y]):l.type==="polyline"?JSON.stringify((l==null?void 0:l.paths)||[]):""}`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),u.success(`更新${D}位置成功`)):(console.error("更新要素位置失败:",K),u.error("更新要素位置失败，请重试"),B())}else{console.log("使用 ArcGIS 模式更新要素位置");let j=[];j=i.graphics,F.submitEdit(R,{updateFeatures:j},()=>{var K,Q;ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`${k.UPDATE}OBJECTID为${(Q=(K=i.graphics)==null?void 0:K[0].attributes)==null?void 0:Q.OBJECTID}的${D}的空间位置，更新前位置: ${(a==null?void 0:a.type)==="point"?JSON.stringify([a==null?void 0:a.x,a==null?void 0:a.y]):a.type==="polyline"?JSON.stringify((a==null?void 0:a.paths)||[]):""},更新后的位置：${l.type==="point"?JSON.stringify([l==null?void 0:l.x,l==null?void 0:l.y]):l.type==="polyline"?JSON.stringify((l==null?void 0:l.paths)||[]):""}`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),u.success(`更新${D}位置成功`)},()=>{console.error("更新要素位置失败"),u.error("更新要素位置失败，请重试"),B()})}}catch(j){console.error("更新要素位置时出错:",j),u.error("更新要素位置失败，请重试"),B()}}).catch(()=>{console.log("用户取消了更新要素位置操作"),B()})},{initSketch:ut,destroySketch:Be}=nt(),Ne=()=>{if(console.log("初始化编辑 sketch..."),Be(),!e.graphicsLayer&&e.view&&(console.log("初始化 graphicsLayer..."),e.graphicsLayer=fe(e.view,{id:"pipe-editing",title:"编辑"})),!e.view){console.error("视图未初始化，无法创建 sketch");return}if(!e.graphicsLayer){console.error("图形图层未初始化，无法创建 sketch");return}console.log("创建 sketch..."),e.sketch=ut(e.view,e.graphicsLayer,{createCallBack:ve,updateCallBack:ve,delCallBack:()=>{console.log("删除回调触发"),B()},snappingOptions:{enabled:s.useCapture,featureSources:[{layer:e.graphicsLayer}]}}),console.log("sketch 初始化完成:",!!e.sketch)},dt=async i=>{var t;e.view=i,(t=e.view)==null||t.watch("extent",x.refresh),e.graphicsLayer=fe(e.view,{id:"pipe-editing",title:"编辑"}),Ne(),setTimeout(()=>{w.getLayerInfo(e.view)},1e3)},pt=()=>{var D,R,a,l,g,G;const i=(R=(D=r.value)==null?void 0:D.dataForm.layerid)==null?void 0:R[0],t=(a=w.curLayerInfo)==null?void 0:a.layername,m=e.graphic;if(!E.curNode.value){u.error("请选中要保留的设备");return}if(i===void 0||!m)return;const h=e.graphics.filter(L=>{var U;return L.attributes.OBJECTID!==((U=E.curNode.value)==null?void 0:U.value)})||[];let T=[];T=h,F.submitEdit((G=(g=(l=r.value)==null?void 0:l.dataForm)==null?void 0:g.layerid)==null?void 0:G[0],{deleteFeatures:T},()=>{var U,Y,_;ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`合并OBJECTID为${e.graphics.map(X=>X.attributes.OBJECTID).join("、")}的${t}，合并后OBJECTID为${(U=E.curNode.value)==null?void 0:U.value}`,optionType:k.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),(Y=f.value)==null||Y.Close(),E.curNode.value=void 0;const L=(_=e.view)==null?void 0:_.map.findLayerById("pipelayer");L&&L.refresh()})},yt=()=>{e.sketch&&(e.sketch.snappingOptions.enabled=!!s.useCapture)},mt=()=>{var i;e.graphic&&((i=e.sketch)==null||i.update(e.graphic,{tool:"reshape"}))},gt=()=>{var i;(i=o.value)==null||i.Close(),Je()},Je=async()=>{var i;s.mergeable&&((i=f.value)==null||i.Open(),await Qe(),E.resetForm(e.graphics))},ft=()=>{},ht=async()=>{if(!s.currentAddingFeatureInfo||!e.currentAddingFeatureGraphic){u.error("没有要添加的要素信息");return}const{layerId:i,layerName:t}=s.currentAddingFeatureInfo;let m={};if(b.value)try{m=JSON.parse(JSON.stringify(b.value.dataForm||{}))}catch(T){console.error("处理表单数据时出错:",T),m={}}console.log("提交新要素:",{geometry:null,attributes:m,symbol:null}),Fe("此操作将添加新要素到空间数据库，是否继续？","提示信息").then(async()=>{var T,D,R;try{if(window.GIS_SERVER_SWITCH){console.log("使用 GeoServer WFS-T 模式添加要素");const a=e.currentAddingFeatureGraphic;if(!a)throw new Error("找不到要添加的图形对象");const l={};Object.keys(m).forEach(Z=>{const V=m[Z];V!=null&&V!==""&&(l[Z]=V)}),a.attributes={...l},a.type=(T=w.curLayerInfo)==null?void 0:T.type;const g=await jt(t,a);console.log("WFS-T 添加要素响应:",g);const L=new DOMParser().parseFromString(g.data,"text/xml"),U=L.getElementsByTagName("wfs:totalInserted"),Y=U.length>0?parseInt(U[0].textContent||"0"):0,_=L.getElementsByTagName("ogc:FeatureId"),X=_.length>0?_[0].getAttribute("fid"):"none";if(console.log("插入的要素数量:",Y),console.log("要素ID:",X),Y>0&&X!=="none"){const Z=X;console.log("新添加要素的 ID:",Z);let V=null;if(Z){const te=Z.split(".");V=te.length>1?te[1]:Z}V&&(a.attributes.OBJECTID=V,a.attributes.fid=Z),ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`添加新${t}，OBJECTID为${V||"未知"}`,optionType:k.ADD}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),e.graphic=a,u.success(`添加${t}成功`),s.isAddingNewFeature=!1,s.currentAddingFeatureInfo=void 0,e.currentAddingFeatureGraphic=void 0,(D=o.value)==null||D.Close()}else console.error("WFS-T 添加要素失败:",g),u.error("添加要素失败，请重试")}else{const a={};try{const g=e.currentAddingFeatureGraphic;if(g&&g.geometry)if(g.geometry.type==="point"){const G=g.geometry;a.geometry={type:"point",x:G.x,y:G.y}}else if(g.geometry.type==="polyline"){const G=g.geometry;a.geometry={type:"polyline",paths:[...G.paths]}}else a.geometry={type:"point",x:0,y:0};else a.geometry={type:"point",x:0,y:0};g&&g.symbol&&(a.symbol={...g.symbol})}catch(g){console.error("处理几何信息时出错:",g),a.geometry={type:"point",x:0,y:0}}const l={};Object.keys(m).forEach(g=>{const G=m[g];G!=null&&G!==""&&(l[g]=G)}),a.attributes={...l,CREATEDDATE:Ae().format(Tr),CREATEDBY:((R=ot().user)==null?void 0:R.name)||""},console.log("提交的属性数据:",a.attributes),console.log("使用 ArcGIS 模式添加要素"),F.submitEdit(typeof i=="string"?parseInt(i)||0:i,{addFeatures:[a]},g=>{var G,L;u.success(`添加${t}成功`),ie({optionName:ae.GWBIANJI,type:le.BASICGIS,content:`添加新${t}，OBJECTID为${(G=g.addFeatureResults[0])==null?void 0:G.objectId}`,optionType:k.ADD}).catch(()=>{console.log("生成gis操作日志失败")}),ue(e.view),B(),g.addFeatureResults&&g.addFeatureResults[0]&&(a.attributes.OBJECTID=g.addFeatureResults[0].objectId,g.addFeatureResults[0].globalId&&(a.attributes.SID=g.addFeatureResults[0].globalId)),e.graphic=a,s.isAddingNewFeature=!1,s.currentAddingFeatureInfo=void 0,e.currentAddingFeatureGraphic=void 0,(L=o.value)==null||L.Close()},()=>{console.error("添加要素失败"),u.error("添加要素失败，请重试")})}}catch(a){console.error("添加要素时出错:",a),u.error("添加要素失败，请重试")}}).catch(()=>{var T;console.log("用户取消了添加要素操作"),(T=o.value)==null||T.Close()})},It=()=>{var t,m,h;if(!((m=(t=r.value)==null?void 0:t.dataForm.layerid)==null?void 0:m[0])){u.warning("无法确定当前图层");return}if(s.copyAttributesMode){Me();return}s.copyAttributesMode=!0,De("copy"),u.info("请点击地图上的要素以复制其属性"),e.copyAttributesClickHandler=(h=e.view)==null?void 0:h.on("click",Ft)},Me=()=>{De("default"),e.copyAttributesClickHandler&&(e.copyAttributesClickHandler.remove(),e.copyAttributesClickHandler=null),s.copyAttributesMode=!1,u.info("已退出复制属性模式")},Ft=async i=>{var D,R;const t=(R=(D=r.value)==null?void 0:D.dataForm.layerid)==null?void 0:R[0];if(!t){u.warning("无法确定当前图层");return}const m={x:i.x,y:i.y};console.log("点击位置屏幕坐标:",m.x,m.y);const h=typeof t=="string"?t:`${t}`;console.log("使用图层名称进行查询:",h);const T=await Ke(e.view,"/geoserver/guazhou/wms",h,m);if(console.log("GeoServer查询结果:",T),T&&T.data&&T.data.features&&T.data.features.length>0){const a=T.data.features[0];if(a&&a.properties){const l={...a.properties};if(delete l.OBJECTID,delete l.SID,delete l.objectid,delete l.sid,delete l.SHAPE,delete l.shape,delete l.geometry,delete l.geom,s.copiedAttributes=l,s.copiedAttributesLayerId=t,b.value&&e.graphic){const g={...b.value.dataForm,...l};b.value.dataForm.OBJECTID&&(g.OBJECTID=b.value.dataForm.OBJECTID),b.value.dataForm.SID&&(g.SID=b.value.dataForm.SID),b.value.dataForm=g,e.graphic.attributes={...e.graphic.attributes,...l},u.success("已复制属性并应用到当前表单")}else u.success("已复制属性");console.log("已复制属性:",l),Me()}else u.warning("要素没有属性")}else u.warning("未找到要素，请重新点击")},bt=()=>{var T,D,R,a,l,g;if(!s.editing){u.error("请先开始编辑");return}console.log("表单数据:",(T=r.value)==null?void 0:T.dataForm);const i=(R=(D=r.value)==null?void 0:D.dataForm.layerid)==null?void 0:R[0];if(!i){u.warning("请先选择图层"),(a=c.value)==null||a.hide();return}console.log("当前选择的图层:",typeof i=="string"?i:"图层");const m=w.curLayerInfo;let h=(m==null?void 0:m.type)||"point";e.sketch||(console.log("初始化 sketch..."),Ne()),e.graphicsLayer||(console.log("初始化 graphicsLayer..."),e.graphicsLayer=fe(e.view,{id:"pipe-editing",title:"编辑"}));try{h.toLowerCase().includes("point")?(u.info("请在地图上点击添加点要素"),h="point"):h.toLowerCase().includes("line")?(u.info("请在地图上点击添加线的顶点，双击结束绘制"),h="polyline"):u.info("请在地图上点击添加面的顶点，双击结束绘制"),(l=e.sketch)==null||l.create(h,{mode:"click"}),console.log("创建要素命令已发送，使用点击模式")}catch(G){console.error("创建要素失败:",G),u.error("创建要素失败，请重试")}(g=c.value)==null||g.hide()};return Tt(()=>{s.isMounted=!0,Xt()}),wt(()=>{Be(),d.destroy()}),(i,t)=>{const m=Rt,h=Nt,T=xt,D=_t,R=Bt,a=Lt;return Ie(),Se(kt,{ref:"refMap",title:"管网编辑",onMapLoaded:dt},{"map-bars":ne(()=>[H("div",Ir,[ee(C(Dt),{ref_key:"refEditorPop",ref:c,trigger:"click","popper-class":C(At)().isDark?"darkblue":""},{reference:ne(()=>[ee(h,{text:!0},{default:ne(()=>[t[4]||(t[4]=H("span",null," 编辑器 ",-1)),ee(C(me),{icon:"ep:arrow-down"})]),_:1})]),default:ne(()=>[H("dl",Fr,[H("dd",{class:ge([C(s).editing?"disabled":""]),onClick:p},[ee(C(me),{icon:"ep:edit"}),t[5]||(t[5]=H("span",null,"开始编辑",-1))],2),H("dd",{class:ge(["",[C(s).editing?"":"disabled"]]),onClick:P},[ee(C(me),{icon:"ep:edit-pen"}),t[6]||(t[6]=H("span",null,"结束编辑",-1))],2),t[12]||(t[12]=H("dt",null,"选择可编辑数据",-1)),H("dd",{class:ge({disabled:!C(s).editing,active:C(x).curType.value==="polygon"}),onClick:t[0]||(t[0]=l=>v("polygon"))},[ee(C(me),{icon:"gis:polygon-hole-pt"}),t[7]||(t[7]=H("span",null,"按多边形选择",-1))],2),H("dd",{class:ge({disabled:!C(s).editing,active:C(x).curType.value==="rectangle"}),onClick:t[1]||(t[1]=l=>v("rectangle"))},[ee(C(me),{icon:"gis:rectangle-pt"}),t[8]||(t[8]=H("span",null,"按矩形选择",-1))],2),H("dd",{class:ge({disabled:!C(s).editing,active:C(x).curType.value==="circle"}),onClick:t[2]||(t[2]=l=>v("circle"))},[ee(C(me),{icon:"gis:circle"}),t[9]||(t[9]=H("span",null,"按圆形选择",-1))],2),t[13]||(t[13]=H("dt",null,"编辑操作",-1)),H("dd",{class:ge({disabled:!C(s).editing}),onClick:bt},[ee(C(me),{icon:"mdi:plus-circle-outline"}),t[10]||(t[10]=H("span",null,"添加要素",-1))],2),t[14]||(t[14]=H("dt",null,"配置",-1)),H("dd",{id:"useCapture",class:ge([C(s).useCapture?"active":""])},[ee(T,{modelValue:C(s).useCapture,"onUpdate:modelValue":t[3]||(t[3]=l=>C(s).useCapture=l),onChange:yt},null,8,["modelValue"]),t[11]||(t[11]=H("label",{for:"useCapture"},"使用捕捉",-1))],2)])]),_:1},8,["popper-class"]),(Ie(),Gt(Pt,null,Ct(N,(l,g)=>ee(D,{key:g,size:"default",config:l},null,8,["config"])),64))]),C(s).isMounted?(Ie(),Se(R,{key:0,ref_key:"refPanel",ref:o,title:C(s).isAddingNewFeature?"添加要素":"编辑属性","full-content":!0,telport:"#arcmap-wrapper","show-close":!0,"max-min":!1,class:"pipe-attr-panel-editable"},{default:ne(()=>[H("div",br,[ee(m,{ref_key:"refFormEdit",ref:b,config:C(F).FormConfig},null,8,["config"])]),H("div",vr,[ee(h,{type:"info",icon:"ep:copy-document",onClick:It,style:{"margin-right":"10px"}},{default:ne(()=>t[15]||(t[15]=[Te(" 复制属性 ")])),_:1}),C(s).isAddingNewFeature?(Ie(),Se(h,{key:0,type:"primary",icon:C(xe),loading:C(F).FormConfig.submitting,onClick:ht},{default:ne(()=>t[16]||(t[16]=[Te(" 添加要素 ")])),_:1},8,["icon","loading"])):(Ie(),Se(h,{key:1,type:"primary",icon:C(xe),loading:C(F).FormConfig.submitting,onClick:z},{default:ne(()=>t[17]||(t[17]=[Te(" 保存属性 ")])),_:1},8,["icon","loading"]))])]),_:1},8,["title"])):Ye("",!0),C(s).isMounted?(Ie(),Se(R,{key:1,ref_key:"refPanelMerge",ref:f,modal:!1,title:"合并","full-content":!0,telport:"#arcmap-wrapper","show-close":!0,"max-min":!1,class:"pipe-attr-panel-editable"},{default:ne(()=>[H("div",Er,[ee(a,{config:C(E).TreeData.value},null,8,["config"])]),H("div",Sr,[Te(Ot(C(E).curNode.value?"合并到："+C(E).curNode.value.label:"请选择保留的设备")+" ",1),ee(h,{type:"primary",icon:C(xe),onClick:pt},{default:ne(()=>t[18]||(t[18]=[Te(" 确定 ")])),_:1},8,["icon"])])]),_:1},512)):Ye("",!0)]),default:ne(()=>[ee(m,{ref_key:"refForm",ref:r,config:C(w).FormConfig},null,8,["config"])]),_:1},512)}}}),$s=$t(wr,[["__scopeId","data-v-0a76aa1a"]]);export{$s as default};
