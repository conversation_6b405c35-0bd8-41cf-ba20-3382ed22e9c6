package org.thingsboard.server.dao.deviceType;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseInquiry;
import org.thingsboard.server.dao.sql.deviceType.DevicePurchaseInquiryMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.device.DevicePurchaseInquiryPageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DevicePurchaseInquirySaveRequest;
import org.thingsboard.server.dao.util.imodel.response.device.DevicePurchaseInquiryItemResponse;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DevicePurchaseInquiryServiceImpl implements DevicePurchaseInquiryService {
    @Autowired
    private DevicePurchaseInquiryMapper mapper;


    @Override
    public IPage<DevicePurchaseInquiryItemResponse> findAllConditional(DevicePurchaseInquiryPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public DevicePurchaseInquiry update(DevicePurchaseInquiry entity) {
        mapper.update(entity);
        return mapper.selectById(entity.getId());
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public List<DevicePurchaseInquiry> saveAll(List<DevicePurchaseInquirySaveRequest> items) {
        if (items.size() == 0)
            return Collections.emptyList();
        String mainId = items.stream().findFirst().get().getPurchaseDetailId();

        // noinspection Convert2MethodRef 删除所有未在列表中的设备询价条目
        mapper.deleteByPurchaseItemMainIdOnIdNotIn(mainId,
                items.stream().map(x -> x.getId()).filter(id -> id != null).collect(Collectors.toList()));
        // mapper.removeAllByMainId(mainId); 已修正为使用on conflict时更新，否则插入
        return QueryUtil.saveOrUpdateBatchByRequest(items, mapper::saveAll, mapper::updateAll);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public boolean deleteByPurchaseItemMainIdOnMainIdNotIn(String mainId, List<String> idList) {
        return mapper.deleteByPurchaseItemMainId(mainId, idList) > 0;
    }

    @Override
    public boolean deleteBatchByPurchaseItemId(List<String> idList) {
        return mapper.deleteBatchByPurchaseItemId(idList) > 0;
    }

    @Override
    public boolean deleteByPurchaseItemId(String id) {
        return mapper.deleteByPurchaseItemId(id);
    }

    @Override
    public DevicePurchaseInquiry inquiry(DevicePurchaseInquiry entity) {
        return mapper.inquiry(entity);
    }

}
