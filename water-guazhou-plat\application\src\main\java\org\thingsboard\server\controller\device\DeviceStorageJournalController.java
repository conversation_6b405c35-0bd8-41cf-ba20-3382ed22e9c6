package org.thingsboard.server.controller.device;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.*;
import org.thingsboard.server.dao.store.DeviceStorageJournalService;
import org.thingsboard.server.dao.util.imodel.query.store.*;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/deviceStorageJournal")
public class DeviceStorageJournalController extends BaseController {
    @Autowired
    private DeviceStorageJournalService service;


    @GetMapping
    public IPage<DeviceStorageJournalResponse> findAllConditional(DeviceStorageJournalPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/{id}/detail")
    public DetailDeviceStorageInfo detail(@PathVariable String id) {
        return service.detail(id);
    }

    @GetMapping("/rest")
    public IPage<MainRestDeviceStorageJournal> findRestConditional(RestDeviceStorageJournalPageRequest request) {
        return service.findRestConditional(request);
    }

    @GetMapping("/restWithoutSplit")
    public IPage<RestDeviceStorageJournal> findRestWithoutSplitConditional(RestDeviceStorageJournalPageRequest request) {
        return service.findRestWithoutSplitConditional(request);
    }

    @GetMapping("/restInfo")
    public IPage<RestDeviceStorageInfo> findRestDeviceInfoConditional(RestDeviceInfoPageRequest request) {
        return service.findRestDeviceInfoConditional(request);
    }

    @GetMapping("/exportExcel")
    public ExcelFileInfo exportExcel(DeviceStorageJournalPageRequest request) {
        return ExcelFileInfo.of("设备台账", findAllConditional(request.ignorePage()))
                .withDateTime()
                .nextTitle("serialId", "设备编码")
                .nextTitle("deviceLabelCode", "设备标签码")
                .nextTitle("model", "设备型号")
                .nextTitle("name", "设备名称")
                .nextTitle("type", "设备分类")
                .nextTitle("installAddressName", "设备名称")
                .nextTitle("departmentName", "使用部门")
                .nextTitle("supplierName", "设备供应商")
                .nextTitle("installAddressName", "安装区域")
                .nextTitle("detailInstallAddressName", "安装位置");
    }

    @GetMapping("/exportDeviceLabelExcel")
    public ExcelFileInfo exportDeviceLabelExcel(DeviceStorageJournalPageRequest request) {
        return ExcelFileInfo.of("设备台账", findAllConditional(request.ignorePage()))
                .withDateTime()
                // .nextTitle("serialId", "设备编码")
                .nextTitle("deviceLabelCode", "标签标签码")
                .nextTitle("name", "设备名称")
                .nextTitle("model", "设备型号")
                .nextTitle("topType", "所属大类")
                .nextTitle("type", "所属类别")
                .nextTitle("supplierName", "供应商")
                .nextTitle("scrappedTime", "报废日期")
                // .nextTitle("installAddressName", "施工项目")
                .nextTitle("storehouseName", "所在仓库");
    }

    @PostMapping("/checkout")
    public boolean checkout(@RequestBody List<DeviceStorageJournalCheckOutRequest> requests) {
        if (requests.size() != 1) {
            ExceptionUtils.silentThrow("非法参数数量");
        }
        DeviceStorageJournalCheckOutRequest request = requests.get(0);
        if (!service.canOperate(request.getStoreOutId(), request.getCurrentUserId())) {
            ExceptionUtils.silentThrow("非经办人不可执行出库操作");
        }

        return service.checkoutAll(request);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody DeviceStorageJournalSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(service.delete(id));
    }

    @PostMapping("collect")
    public IstarResponse collect(@RequestBody JSONObject params) {
        service.collect(params);
        return IstarResponse.ok();
    }
}