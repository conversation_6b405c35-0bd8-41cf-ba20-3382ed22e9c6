const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/meshFeatureSet-e8lo-3vd.js","static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/axisAngleDegrees-CVgmQKGQ.js","static/js/quat-CM9ioDFt.js","static/js/mat3f64-BVJGbF0t.js","static/js/quatf64-QCogZAoR.js","static/js/MeshComponent-CfWisxg8.js","static/js/imageUtils-IEWq71TJ.js","static/js/georeference-GB1Pt0mj.js","static/js/mat4f64-BCm7QTSd.js","static/js/spatialReferenceEllipsoidUtils-j_kxMN-4.js","static/js/BufferView-BcX1hwIm.js","static/js/vec33-BEptSvzS.js","static/js/projection-oyk5Uk7v.js","static/js/triangulationUtils-Da3LiW_b.js","static/js/earcut-BJup91r2.js","static/js/deduplicate-Clsym5GM.js","static/js/Indices-iFKW8TWb.js","static/js/executeRelationshipQuery-BifiWBY3.js","static/js/queryAttachments-BBxy3-oJ.js","static/js/executeTopFeaturesQuery-W6XMZI2x.js","static/js/queryTopFeatures-8I34s2x4.js","static/js/executeForTopIds-CaUainor.js","static/js/executeForTopExtents-CNBhOHhn.js","static/js/executeForTopCount-DBxcZ6MV.js"])))=>i.map(i=>d[i]);
import{T as y,eZ as T,a$ as b,a4 as A,a3 as u,R as m}from"./index-r0dFAfgr.js";import{e as c,y as h,a as I,v as V,U as d,s as G}from"./Point-WxyopZva.js";import{L as w}from"./pe-B8dP0-Ut.js";import{b$ as _,fo as E,db as g,w as L,e5 as q,e6 as M,ce as Q,fp as Z,fm as P,fq as k,bH as z}from"./MapView-DaoQedLH.js";import{n as $,s as j}from"./executeForIds-BLdIsxvI.js";async function B(r,t,e){const o=_(r);return E(o,g.from(t),{...e}).then(s=>({count:s.data.count,extent:L.fromJSON(s.data.extent)}))}function J(r,t){return t}function D(r,t,e,o){switch(e){case 0:return p(r,t+o,0);case 1:return r.originPosition==="lowerLeft"?p(r,t+o,1):X(r,t+o,1)}}function S(r,t,e,o){return e===2?p(r,t,2):D(r,t,e,o)}function N(r,t,e,o){return e===2?p(r,t,3):D(r,t,e,o)}function U(r,t,e,o){return e===3?p(r,t,3):S(r,t,e,o)}function p({translate:r,scale:t},e,o){return r[o]+e*t[o]}function X({translate:r,scale:t},e,o){return r[o]-e*t[o]}class Y{constructor(t){this._options=t,this.geometryTypes=["esriGeometryPoint","esriGeometryMultipoint","esriGeometryPolyline","esriGeometryPolygon"],this._previousCoordinate=[0,0],this._transform=null,this._applyTransform=J,this._lengths=[],this._currentLengthIndex=0,this._toAddInCurrentPath=0,this._vertexDimension=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,this._attributesConstructor=class{}}createFeatureResult(){return{fields:[],features:[]}}finishFeatureResult(t){if(this._options.applyTransform&&(t.transform=null),this._attributesConstructor=class{},this._coordinateBuffer=null,this._lengths.length=0,!t.hasZ)return;const e=q(t.geometryType,this._options.sourceSpatialReference,t.spatialReference);if(!y(e))for(const o of t.features)e(o.geometry)}createSpatialReference(){return{}}addField(t,e){const o=t.fields;T(o),o.push(e);const s=o.map(i=>i.name);this._attributesConstructor=function(){for(const i of s)this[i]=null}}addFeature(t,e){t.features.push(e)}prepareFeatures(t){switch(this._transform=t.transform,this._options.applyTransform&&t.transform&&(this._applyTransform=this._deriveApplyTransform(t)),this._vertexDimension=2,t.hasZ&&this._vertexDimension++,t.hasM&&this._vertexDimension++,t.geometryType){case"esriGeometryPoint":this.addCoordinate=(e,o,s)=>this.addCoordinatePoint(e,o,s),this.createGeometry=e=>this.createPointGeometry(e);break;case"esriGeometryPolygon":this.addCoordinate=(e,o,s)=>this._addCoordinatePolygon(e,o,s),this.createGeometry=e=>this._createPolygonGeometry(e);break;case"esriGeometryPolyline":this.addCoordinate=(e,o,s)=>this._addCoordinatePolyline(e,o,s),this.createGeometry=e=>this._createPolylineGeometry(e);break;case"esriGeometryMultipoint":this.addCoordinate=(e,o,s)=>this._addCoordinateMultipoint(e,o,s),this.createGeometry=e=>this._createMultipointGeometry(e)}}createFeature(){return this._lengths.length=0,this._currentLengthIndex=0,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,{attributes:new this._attributesConstructor}}allocateCoordinates(){}addLength(t,e,o){this._lengths.length===0&&(this._toAddInCurrentPath=e),this._lengths.push(e)}addQueryGeometry(t,e){const{queryGeometry:o,queryGeometryType:s}=e,i=M(o.clone(),o,!1,!1,this._transform),n=Q(i,s,!1,!1);t.queryGeometryType=s,t.queryGeometry={...n}}createPointGeometry(t){const e={x:0,y:0,spatialReference:t.spatialReference};return t.hasZ&&(e.z=0),t.hasM&&(e.m=0),e}addCoordinatePoint(t,e,o){const s=b(this._transform,"transform");switch(e=this._applyTransform(s,e,o,0),o){case 0:t.x=e;break;case 1:t.y=e;break;case 2:"z"in t?t.z=e:t.m=e;break;case 3:t.m=e}}_transformPathLikeValue(t,e){let o=0;e<=1&&(o=this._previousCoordinate[e],this._previousCoordinate[e]+=t);const s=b(this._transform,"transform");return this._applyTransform(s,t,e,o)}_addCoordinatePolyline(t,e,o){this._dehydratedAddPointsCoordinate(t.paths,e,o)}_addCoordinatePolygon(t,e,o){this._dehydratedAddPointsCoordinate(t.rings,e,o)}_addCoordinateMultipoint(t,e,o){o===0&&t.points.push([]);const s=this._transformPathLikeValue(e,o);t.points[t.points.length-1].push(s)}_createPolygonGeometry(t){return{rings:[[]],spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM}}_createPolylineGeometry(t){return{paths:[[]],spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM}}_createMultipointGeometry(t){return{points:[],spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM}}_dehydratedAddPointsCoordinate(t,e,o){o===0&&this._toAddInCurrentPath--==0&&(t.push([]),this._toAddInCurrentPath=this._lengths[++this._currentLengthIndex]-1,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0);const s=this._transformPathLikeValue(e,o),i=t[t.length-1];o===0&&(this._coordinateBufferPtr=0,this._coordinateBuffer=new Array(this._vertexDimension),i.push(this._coordinateBuffer)),this._coordinateBuffer[this._coordinateBufferPtr++]=s}_deriveApplyTransform(t){const{hasZ:e,hasM:o}=t;return e&&o?U:e?S:o?N:D}}async function H(r,t,e){const o=_(r),s={...e},i=g.from(t),n=!i.quantizationParameters,{data:l}=await Z(o,i,new Y({sourceSpatialReference:i.sourceSpatialReference,applyTransform:n}),s);return l}let a=class extends V{constructor(r){super(r),this.dynamicDataSource=null,this.fieldsIndex=null,this.gdbVersion=null,this.infoFor3D=null,this.pbfSupported=!1,this.queryAttachmentsSupported=!1,this.sourceSpatialReference=null,this.url=null}get parsedUrl(){return w(this.url)}async execute(r,t){const e=await this.executeJSON(r,t);return this.featureSetFromJSON(r,e,t)}async executeJSON(r,t){var l;const e=this._normalizeQuery(r),o=((l=r.outStatistics)==null?void 0:l[0])!=null,s=A("featurelayer-pbf-statistics"),i=!o||s;let n;if(this.pbfSupported&&i)try{n=await H(this.url,e,t)}catch(f){if(f.name!=="query:parsing-pbf")throw f;this.pbfSupported=!1}return this.pbfSupported&&i||(n=await k(this.url,e,t)),this._normalizeFields(n.fields),n}async featureSetFromJSON(r,t,e){if(!this._queryIs3DObjectFormat(r)||y(this.infoFor3D)||!t.assetMaps||!t.features||!t.features.length)return z.fromJSON(t);const{meshFeatureSetFromJSON:o}=await d(u(()=>import("./meshFeatureSet-e8lo-3vd.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22])),e);return o(r,this.infoFor3D,t)}executeForCount(r,t){return $(this.url,this._normalizeQuery(r),t)}executeForExtent(r,t){return B(this.url,this._normalizeQuery(r),t)}executeForIds(r,t){return j(this.url,this._normalizeQuery(r),t)}async executeRelationshipQuery(r,t){const[{default:e},{executeRelationshipQuery:o}]=await d(Promise.all([u(()=>import("./MapView-DaoQedLH.js").then(s=>s.m8),__vite__mapDeps([1,2,3,4,5,6])),u(()=>import("./executeRelationshipQuery-BifiWBY3.js"),__vite__mapDeps([23,1,2,3,4,5,6]))]),t);return r=e.from(r),(this.gdbVersion||this.dynamicDataSource)&&((r=r.clone()).gdbVersion=r.gdbVersion||this.gdbVersion,r.dynamicDataSource=r.dynamicDataSource||this.dynamicDataSource),o(this.url,r,t)}async executeRelationshipQueryForCount(r,t){const[{default:e},{executeRelationshipQueryForCount:o}]=await d(Promise.all([u(()=>import("./MapView-DaoQedLH.js").then(s=>s.m8),__vite__mapDeps([1,2,3,4,5,6])),u(()=>import("./executeRelationshipQuery-BifiWBY3.js"),__vite__mapDeps([23,1,2,3,4,5,6]))]),t);return r=e.from(r),(this.gdbVersion||this.dynamicDataSource)&&((r=r.clone()).gdbVersion=r.gdbVersion||this.gdbVersion,r.dynamicDataSource=r.dynamicDataSource||this.dynamicDataSource),o(this.url,r,t)}async executeAttachmentQuery(r,t){const{executeAttachmentQuery:e,fetchAttachments:o,processAttachmentQueryResult:s}=await d(u(()=>import("./queryAttachments-BBxy3-oJ.js"),__vite__mapDeps([24,6,2,3,4,1,5])),t),i=_(this.url);return s(i,await(this.queryAttachmentsSupported?e(i,r,t):o(i,r,t)))}async executeTopFeaturesQuery(r,t){const{executeTopFeaturesQuery:e}=await d(u(()=>import("./executeTopFeaturesQuery-W6XMZI2x.js"),__vite__mapDeps([25,1,2,3,4,5,6,26])),t);return e(this.parsedUrl,r,this.sourceSpatialReference,t)}async executeForTopIds(r,t){const{executeForTopIds:e}=await d(u(()=>import("./executeForTopIds-CaUainor.js"),__vite__mapDeps([27,1,2,3,4,5,6,26])),t);return e(this.parsedUrl,r,t)}async executeForTopExtents(r,t){const{executeForTopExtents:e}=await d(u(()=>import("./executeForTopExtents-CNBhOHhn.js"),__vite__mapDeps([28,1,2,3,4,5,6,26])),t);return e(this.parsedUrl,r,t)}async executeForTopCount(r,t){const{executeForTopCount:e}=await d(u(()=>import("./executeForTopCount-DBxcZ6MV.js"),__vite__mapDeps([29,1,2,3,4,5,6,26])),t);return e(this.parsedUrl,r,t)}_normalizeQuery(r){let t=g.from(r);if(t.sourceSpatialReference=t.sourceSpatialReference||this.sourceSpatialReference,(this.gdbVersion||this.dynamicDataSource)&&(t=t===r?t.clone():t,t.gdbVersion=r.gdbVersion||this.gdbVersion,t.dynamicDataSource=r.dynamicDataSource?P.from(r.dynamicDataSource):this.dynamicDataSource),m(this.infoFor3D)&&this._queryIs3DObjectFormat(r)){t=t===r?t.clone():t,t.formatOf3DObjects=null;for(const e of this.infoFor3D.queryFormats){if(e==="3D_glb"){t.formatOf3DObjects=e;break}e!=="3D_gltf"||t.formatOf3DObjects||(t.formatOf3DObjects=e)}if(!t.formatOf3DObjects)throw new G("query:unsupported-3d-query-formats","Could not find any supported 3D object query format. Only supported formats are 3D_glb and 3D_gltf");if(y(t.outFields)||!t.outFields.includes("*")){t=t===r?t.clone():t,y(t.outFields)&&(t.outFields=[]);const{originX:e,originY:o,originZ:s,translationX:i,translationY:n,translationZ:l,scaleX:f,scaleY:x,scaleZ:F,rotationX:R,rotationY:C,rotationZ:v,rotationDeg:O}=this.infoFor3D.transformFieldRoles;t.outFields.push(e,o,s,i,n,l,f,x,F,R,C,v,O)}}return t}_normalizeFields(r){if(m(this.fieldsIndex)&&m(r))for(const t of r){const e=this.fieldsIndex.get(t.name);e&&Object.assign(t,e.toJSON())}}_queryIs3DObjectFormat(r){return m(this.infoFor3D)&&r.returnGeometry===!0&&r.multipatchOption!=="xyFootprint"&&!r.outStatistics}};c([h({type:P})],a.prototype,"dynamicDataSource",void 0),c([h()],a.prototype,"fieldsIndex",void 0),c([h()],a.prototype,"gdbVersion",void 0),c([h()],a.prototype,"infoFor3D",void 0),c([h({readOnly:!0})],a.prototype,"parsedUrl",null),c([h()],a.prototype,"pbfSupported",void 0),c([h()],a.prototype,"queryAttachmentsSupported",void 0),c([h()],a.prototype,"sourceSpatialReference",void 0),c([h({type:String})],a.prototype,"url",void 0),a=c([I("esri.tasks.QueryTask")],a);const ot=a;export{ot as x};
