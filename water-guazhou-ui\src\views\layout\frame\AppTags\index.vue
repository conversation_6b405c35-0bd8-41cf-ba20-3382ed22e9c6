<template>
  <div class="app-tags">

    <div class="app-header-divider left"></div>
    <div ref="refFirstDivider" class="app-header-divider right"></div>
    <div ref="refScrollbable" class="scrollable-tags">
      <div
        class="tag-item first"
        :class="[
          router.currentRoute.value.name === defaultRoute.name ? 'is-active' : ''
        ]"
        @click="() => goTo(defaultRoute)"
      >
        <div class="tag-item__title">
          <span class="tag-item__text">{{ defaultRoute.meta.title }}</span>
        </div>
      </div>
      <el-dropdown
        v-for="cMenu in tagsStore.cachedRouters"
        :key="cMenu.name"
        :ref="'refTagItem-' + cMenu.name"
        style="color: #fff"
        :trigger="'contextmenu'"
      >
        <div
          :id="cMenu.name"
          class="tag-item"
          :class="[
            router.currentRoute.value.name === cMenu.name ? 'is-active' : ''
          ]"
        >
          <div class="tag-item__title" @click="() => goTo(cMenu)">
            <span class="tag-item__text">{{ cMenu.meta?.title }}</span>
          </div>
          <Icon
            class="tag-item__icon"
            icon="ep:close"
            @click="() => removeTag(cMenu)"
          ></Icon>
        </div>
        <div class="app-header-divider left"></div>
        <div class="app-header-divider right"></div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="() => removeTag(cMenu)">
              关闭
            </el-dropdown-item>
            <el-dropdown-item @click="() => reloadPage(cMenu)">
              刷新
            </el-dropdown-item>
            <el-dropdown-item @click="() => closeLeft(cMenu)">
              关闭左侧
            </el-dropdown-item>
            <el-dropdown-item @click="() => closeRight(cMenu)">
              关闭右侧
            </el-dropdown-item>
            <el-dropdown-item @click="() => closeOther(cMenu)">
              关闭其它
            </el-dropdown-item>
            <el-dropdown-item @click="closeTags()"> 关闭全部 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="tag-item last" style="background-color: transparent">
      <el-dropdown :trigger="'hover'" style="color: #000">
        <Icon icon="ep:more"></Icon>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="scrollToCurTab">
              滚动至当前
            </el-dropdown-item>
            <el-dropdown-item @click="closeTags('left')">
              关闭至当前左侧
            </el-dropdown-item>
            <el-dropdown-item @click="closeTags('right')">
              关闭至当前右侧
            </el-dropdown-item>
            <el-dropdown-item @click="() => closeOther()">
              关闭其它
            </el-dropdown-item>
            <el-dropdown-item @click="closeTags()"> 关闭全部 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { useTagsStore } from '@/store';

const tagsStore = useTagsStore();
const refScrollbable = ref<HTMLDivElement>();
const { proxy }: any = getCurrentInstance();
// const refTagItem = ref<HTMLDivElement[]>()
const router = useRouter();
const defaultRoute = ref({
  name: 'home',
  meta: { title: '首页' }
});
const goTo = (cMenu: any) => {
  router.push({ ...cMenu });
};
const removeTag = (cMenu: any) => {
  const cMenuIndex = tagsStore.cachedRouters.findIndex(
    (item) => item.name === cMenu.name
  );

  tagsStore.REMOVE_cachedRouter(cMenu);
  // 关闭当前，默认跳转最后一个页面
  if (router.currentRoute.value.name === cMenu.name) {
    const tagsLength = tagsStore.cachedRouters.length;
    const nextMenuIndex =
      cMenuIndex >= tagsLength - 1 ? tagsLength - 1 : cMenuIndex;
    cMenuIndex !== -1 &&
      goTo(tagsStore.cachedRouters[nextMenuIndex] ?? defaultRoute.value);
  }
};
const reloadPage = (cMenu: any) => {
  if (router.currentRoute.value.name === cMenu.name) {
    tagsStore.RELOAD_cachedRouterPage(true);
    nextTick(() => {
      tagsStore.RELOAD_cachedRouterPage(false);
    });
  } else {
    goTo(cMenu);
  }
};
const closeTags = async (type?: 'left' | 'right') => {
  if (!type) {
    tagsStore.REMOVEALL_cachedRouters();
    if (router.currentRoute.value.name === 'home') return;
    goTo(defaultRoute.value);
  } else {
    const cMenu = tagsStore.cachedRouters.find(
      (item) => item.name === router.currentRoute.value.name
    );
    if (cMenu) {
      if (type === 'left') {
        tagsStore.REMOVELEFT_cachedRouters(cMenu);
      } else if (type === 'right') {
        tagsStore.REMOVERIGHT_cachedRouters(cMenu);
      }
      await nextTick();
      scrollToCurTab();
    }
  }
};
const closeLeft = (cMenu: any) => {
  const curRouterIndex = tagsStore.cachedRouters.findIndex(
    (item) => item.name === router.currentRoute.value.name
  );
  const cMenuIndex = tagsStore.cachedRouters.findIndex(
    (item) => item.name === cMenu.name
  );
  if (cMenuIndex > curRouterIndex) goTo(cMenu);
  tagsStore.REMOVELEFT_cachedRouters(cMenu);
};

const closeRight = (cMenu: any) => {
  const curRouterIndex = tagsStore.cachedRouters.findIndex(
    (item) => item.name === router.currentRoute.value.name
  );
  const cMenuIndex = tagsStore.cachedRouters.findIndex(
    (item) => item.name === cMenu.name
  );
  if (cMenuIndex < curRouterIndex) goTo(cMenu);
  tagsStore.REMOVERIGHT_cachedRouters(cMenu);
};
const closeOther = (cMenu?: any) => {
  // const curRouter = tagsStore.cachedRouters.find(
  //   item => item.name === router.currentRoute.value.name
  // )
  if (!cMenu) {
    cMenu = tagsStore.cachedRouters.find(
      (item) => item.name === router.currentRoute.value.name
    );
  }
  if (!cMenu) return;
  tagsStore.REMOVEOTHER_cachedRouters(cMenu);
  goTo(cMenu);
};
const resetWidth = () => {
  // refTagItem.value?.map(item => {
  //   console.log(item)
  // })
};
const scrollSmooth = (scrollLeft: number) => {
  refScrollbable.value?.scrollTo({
    left: scrollLeft,
    behavior: 'smooth'
  });
};
const scrollToCurTab = () => {
  const name = tagsStore.cachedRouters.find(
    (item) => item.name === router.currentRoute.value.name
  )?.name;
  if (!name) return;
  if (!refScrollbable.value) return;
  const tag =
    proxy.$refs[
      'refTagItem-' + (router.currentRoute.value.name?.toString() || '')
    ]?.[0]?.$el;
  if (!tag) return;
  const pOffsetLeft = refScrollbable.value.offsetLeft;
  const tagLeft = tag.offsetLeft - pOffsetLeft;
  const tagWidth = tag.scrollWidth;
  if (tagLeft === undefined) return;
  if (
    tagLeft + tagWidth <=
      refScrollbable.value.scrollLeft + refScrollbable.value.offsetWidth &&
    tagLeft >= refScrollbable.value.scrollLeft
  ) {
    // 当标签能完整看见时不操作
    return;
  }
  // 调整标签到显示区域
  if (
    tagLeft + tagWidth >
    refScrollbable.value.scrollLeft + refScrollbable.value.offsetWidth
  ) {
    // 超出右侧显示不完整时，调整标签到最右侧（要考虑标签宽度）
    const scrollLeft = tagLeft + tagWidth - refScrollbable.value.offsetWidth;
    scrollSmooth(scrollLeft);
  } else {
    // 其它情况：调整标签到最左侧
    // refScrollbable.value.scrollLeft = tagLeft
    scrollSmooth(tagLeft);
  }
};
// watch(
//   () => tagsStore.cachedRouters,
//   async () => {
//     await nextTick()
//     resetWidth()
//     scrollToCurTab()
//   },
//   {
//     deep: true
//   }
// )
watch(
  () => router.currentRoute.value.name,
  async () => {
    await nextTick();
    resetWidth();
    scrollToCurTab();
  }
);
// onBeforeRouteUpdate(() => {
//   scrollToCurTab()
// })
onMounted(() => {
  refScrollbable.value?.addEventListener(
    'wheel',
    (e) => {
      e.preventDefault();
      if (!refScrollbable.value) return;
      const left = refScrollbable.value.scrollLeft + e.deltaY;
      scrollSmooth(left);
    },
    { passive: true }
  );
});
</script>
<style lang="scss" scoped>
.app-tags {
  height: 36px;
  padding-top: 6px;
  overflow-x: auto;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  // margin-left: 4px;
  // line-height: 20px;
  // box-shadow: 0 2px 0 -1px var(--el-border-color-light);
  // border-bottom: 1px solid var(--el-border-color-light);
  background-color: #f3f3f3;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  overflow-y: hidden;
  .scrollable-tags {
    display: flex;
    width: calc(100% - 122px);
    height: 100%;
    // margin-top: 4px;
    overflow-x: auto;
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none;
    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
      display: none;
    }
  }

  .tag-item {
    color:#999999;
    font-size: 12px;
    height: 100%;
    width: 88px;
    display: flex;
    text-align: center;
    justify-content: space-between;
    align-items: center;
    border: none;
    // padding: 8px 10px;
    padding: 0 10px;
    background-color: transparent;
    margin-left: 16px;
    cursor: pointer;
    // margin-top: 4px;
    // transition: all 0.5s ease-in;
    /* 添加右侧分隔线 */
    &:not(.last):after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 16px;
      background-color: #666; /* 深灰色分隔线 */
    }
    .tag-item__text {
      display: flex;
      align-items: center;
      height: 100%;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
      word-break: keep-all;
    }
    .tag-item__icon {
      cursor: pointer;
      // opacity: 0;
    }
    &.first,
    &.last {
      width: 88px;
      height: 100%;
      // padding-bottom: 4px;
      // margin-left: 0;
      justify-content: center;
      position: relative; /* 确保伪元素能够正确定位 */
    }
    &.is-active {
      // border-bottom: 4px solid rgb(255,255,255) !important;
      opacity: 1;
      background: #ffffff;
      border-radius: 14px 14px 0 0;
      // box-shadow: 0 24px 24px rgba(0, 0, 0, 0.12), 0 24px 24px rgba(0, 0, 0, 0.06); /* 增强阴影效果 */
      box-shadow: 14px 14px 0 0 #fff, -14px 14px 0 0 #fff;
    }
    &.is-active::before {
      content: '';
      position: absolute;
      left: -14px;
      bottom: 0;
      width: 14px;
      height: 100%;
      background: #f3f3f3;
      border-radius: 0 0 14px 0;
    }

    /* 除了first外的标签的before伪元素添加margin-left */
    &:not(.first).is-active::before {
      margin-left: 16px;
    }
    &.is-active::after {
      content: '';
      position: absolute;
      right: -14px;
      bottom: 0;
      width: 14px;
      height: 100%;
      background: #f3f3f3;
      border-radius: 0 0 0 14px;
    }
    .first.is-active::before{
      margin-top: 4px;
    }
    // &.is-active,
    &:hover {
      // border-bottom: 2px solid rgb(2, 195, 249) !important;
      .tag-item__icon {
        opacity: 1;
      }
    }
  }
}
// .app-header-divider {
//   height: 100%;
//   width: 1px;
//   min-width: 1px;
//   &.right {
//     background-color: #f7f8fb;
//   }
//   &.left {
//     background-color: #e1e3e9;
//   }
// }
.app-header-divider {
  &.right {
    background-color: #434a63;
  }
  &.left {
    background-color: #10131e;
  }
}
/* 暗色模式下的样式已移至Layout.vue */
</style>
