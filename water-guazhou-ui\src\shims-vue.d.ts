// 全局通用类型。
// typescript文件建议放在typings中，配合export/import的方式引入使用

/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '*.js'
declare const Cesium: any
declare module '*.json' {
  const value: any
  export default value
}

declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'
