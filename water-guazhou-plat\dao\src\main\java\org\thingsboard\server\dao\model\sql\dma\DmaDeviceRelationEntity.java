package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA关联总表
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_DEVICE_RELATION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaDeviceRelationEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    private transient String name;

    @Column(name = ModelConstants.DMA_DEVICE_RELATION_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.DMA_DEVICE_RELATION_TYPE)
    private String type;

    @Column(name = ModelConstants.DMA_DEVICE_RELATION_PARTITION_ID)
    private String partitionId;

    @Column(name = ModelConstants.DMA_DEVICE_RELATION_CREATE_TIME)
    private Date createTime;

}
