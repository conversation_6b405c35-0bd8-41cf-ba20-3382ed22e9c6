import{_ as d}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as p,c as f,r as _,D as r,u as t,g as b,h as D,i as h}from"./index-r0dFAfgr.js";const B=p({__name:"SaveScheme",emits:["submit"],setup(v,{expose:n,emit:u}){var o,l,i,s;const c=u,a=f(),m=_({title:"保存方案",dialogWidth:500,draggable:!0,group:[{fields:[{type:"input",label:"方案名称",field:"name",rules:[{required:!0,message:"请输入方案名称"}]},{type:"radio",label:"方案权限",field:"userId",options:[{label:"仅自己可见",value:r((l=(o=t().user)==null?void 0:o.id)==null?void 0:l.id)},{label:"所有人可见",value:"all"}],rules:[{required:!0,message:"请选择权限"}]},{type:"textarea",label:"备注",field:"remark"}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{userId:r((s=(i=t().user)==null?void 0:i.id)==null?void 0:s.id)},submit:e=>{c("submit",e)}});return n({openDialog:()=>{var e;(e=a.value)==null||e.openDialog()},closeDialog:()=>{var e;(e=a.value)==null||e.closeDialog()}}),(e,y)=>{const g=d;return b(),D(g,{ref_key:"refDialog",ref:a,config:h(m)},null,8,["config"])}}});export{B as _};
