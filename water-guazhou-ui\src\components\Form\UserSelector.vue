<template>
  <div class="user-selector">
    <el-popover
      ref="refPop"
      :width="state.width"
      trigger="click"
      :popper-class="popperClass || config?.popperClass"
      :placement="config?.placement || 'bottom'"
    >
      <template #reference>
        <el-input
          ref="refInput"
          v-model="state.userName"
          style="width: 100%"
          :readonly="
            typeof config?.readonly === 'function' ? config.readonly(state.userName, row, config) : config?.readonly
          "
          :prefix-icon="config?.prefixIcon"
          :suffix-icon="config?.suffixIcon"
          :placeholder="
            (typeof config?.placeholder === 'function'
              ? config.placeholder(state.checkedKeys, row, config)
              : config?.placeholder) || '请选择'
          "
          :size="config?.size || size"
          :clearable="config?.clearable !== false"
          @clear="onClear"
          @click="onFocus"
          @focus="onFocus"
          @blur="onBlur"
        ></el-input>
      </template>
      <el-tabs v-model="state.activeName">
        <el-tab-pane
          label="部门"
          name="depart"
        >
          <FormTree
            v-model="state.curDeparts"
            v-loading="state.departLoading"
            :config="DepartTreeConfig"
          >
            <template #default="{ data }">
              <div class="custom-tree-node">
                <el-icon>
                  <FolderOpened class="area" />
                </el-icon>
                <span class="custom-tree-node__label">{{ data?.label }}</span>
              </div>
            </template>
          </FormTree>
        </el-tab-pane>
        <el-tab-pane
          label="用户"
          name="user"
        >
          <FormTree
            ref="refUserTree"
            v-model="state.checkedKeys"
            v-loading="state.loading"
            :config="UserTreeConfig"
            @change="(keys, data, alldatas) => handleChange(keys, data, alldatas)"
          >
            <template #default="{ data }">
              <div class="custom-tree-node">
                <el-icon>
                  <UserFilled class="area" />
                </el-icon>
                <span class="custom-tree-node__label">{{ data?.label }}</span>
              </div>
            </template>
          </FormTree>
        </el-tab-pane>
      </el-tabs>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { FolderOpened, UserFilled } from '@element-plus/icons-vue'
import { ElPopover } from 'element-plus'
import { getWaterSupplyTree } from '@/api/company_org'
import { getUserList } from '@/api/user'
import { IElInput } from '@/common/types/element-plus'
import { formatTree } from '@/utils/GlobalHelper'
import { removeSlash } from '@/utils/removeIdSlash'

const refPop = ref<InstanceType<typeof ElPopover>>()
const emit = defineEmits(['update:modelValue', 'change'])
const props = defineProps<{
  modelValue?: any
  row?: any
  size?: ISize
  config?: IUserSelector
  popperClass?: string
}>()
const state = reactive<{
  width: number
  departLoading: boolean
  loading: boolean
  curDeparts: string[]
  checkedNodes: {
    departId: string
    nodes: NormalOption[]
  }[]
  checkedKeys: any[]
  userName?: string
  activeName: string
  userNodeKey: string
}>({
  width: 0,
  loading: false,
  departLoading: false,
  curDeparts: [],
  checkedNodes: [],
  checkedKeys: [],
  activeName: 'depart',
  userNodeKey: 'value'
})
// const computedValue = computed(() => {
//   return state.checkedNodes.map(item => item.value)
// })

const DepartTreeConfig = reactive<IFormTree>({
  type: 'tree',
  options: [],
  nodeKey: 'id',
  style: {
    height: '160px'
  },
  expandOnClickNode: false,
  nodeClick: data => {
    state.activeName = 'user'
    state.curDeparts = [data.value]
    refreshUser(data.value)
  }
})
const UserTreeConfig = reactive<IFormTree>({
  type: 'tree',
  options: [],
  style: {
    height: '160px'
  },
  nodeKey: state.userNodeKey,
  checkOnClickNode: false,
  multiple: props.config?.multiple,
  showCheckbox: props.config?.multiple
})
const handleChange = (keys: any[], data: NormalOption, checkedNodes: any[]) => {
  if (!state.curDeparts?.length) return
  if (props.config?.multiple) {
    let curCheckedUsers = state.checkedNodes.find(item => item.departId === state.curDeparts[0])
    if (!curCheckedUsers) {
      curCheckedUsers = {
        departId: state.curDeparts[0],
        nodes: []
      }
      state.checkedNodes.push(curCheckedUsers)
    }
    curCheckedUsers.nodes = checkedNodes
  } else {
    state.checkedNodes = [{ departId: state.curDeparts[0], nodes: checkedNodes }]
  }
}
const refreshDepart = async () => {
  state.departLoading = true
  try {
    const res = await getWaterSupplyTree(2)
    DepartTreeConfig.options = formatTree(res.data?.data || [])
  } catch (error) {
    //
  }
  state.departLoading = false
}
const refreshUser = async (pid: string) => {
  state.loading = true
  try {
    UserTreeConfig.options = []
    const res = await getUserList({
      pid,
      page: 1,
      size: 99999
    })
    const value = res.data.data.data || []
    UserTreeConfig.options = value.map(item => {
      return { label: item.firstName, value: removeSlash(item.id.id) }
    })
  } catch (error) {
    console.log('获取用户列表失败', error)
  }
  state.loading = false
}
const refInput = ref<IElInput>()
// 获取 input 的宽度
const getInputWidth = () => {
  nextTick(() => {
    const width = refInput.value?.$el.offsetWidth
    state.width = width < 300 ? 300 : width
  })
}
// 处理 input 获取焦点时，modelValue 有值时，改变 input 的 placeholder 值
const onFocus = () => {
  // state.visible = true
  // if (!props.modelValue) return false
}
// 处理 input 失去焦点时，为空将清空 input 值，为点击选中图标时，将取原先值
const onBlur = () => {
  // state.visible = false
}
// 清空当前点击的 icon 图标
const onClear = () => {
  state.checkedNodes = []
  state.checkedKeys = []
  props.config?.onClear && props.config.onClear()
}
watch(
  () => props.modelValue,
  newVal => {
    /**
     * toDo: 回显
     */
    console.log(newVal)
    state.checkedKeys = newVal
    if (!state.checkedKeys?.length) {
      state.userName = ''
    }
  }
)
watch(
  () => state.checkedNodes,
  newVal => {
    console.log(newVal)
    const keys: any[] = []
    newVal.map(item => {
      item.nodes.map(o => {
        keys.push(o.value)
      })
    })
    state.checkedKeys = Array.from(new Set(keys))
    if (!props.config?.multiple) {
      refPop.value?.hide()
    }
    const names: string[] = []
    state.checkedNodes.map(item => {
      item.nodes.map(o => names.push(o.label))
    })
    state.userName = names.join(',')
    emit('update:modelValue', state.checkedKeys)
    emit('change', state.checkedKeys, state.checkedNodes)
  },
  {
    deep: true
  }
)
onMounted(async () => {
  getInputWidth()
  await refreshDepart()
  if (!props.config?.multiple) {
    if (props.config?.departField && props.row) {
      const dep = props.row[props.config.departField]
      if (dep !== undefined) {
        await refreshUser(dep)
        state.activeName = 'user'
        state.curDeparts = [dep]
        const orivalue = typeof props.modelValue === 'string' ? props.modelValue.split(',') : props.modelValue || []
        state.checkedNodes = [
          {
            departId: dep,
            nodes: UserTreeConfig.options.filter(item => {
              return orivalue.indexOf(item.value) !== -1
            })
          }
        ]
      }
    }
  }

  window.addEventListener('resize', getInputWidth)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', getInputWidth)
})
</script>
<style lang="scss" scoped>
.user-selector {
  width: 100%;
}
.custom-tree-node {
  display: flex;
  flex: 1;
  align-items: center;
  padding-right: 8px;
  .custom-tree-node__label {
    line-height: 1em;
  }
  .area {
    color: orange;
  }
  .district {
    color: lightblue;
  }
}
</style>
