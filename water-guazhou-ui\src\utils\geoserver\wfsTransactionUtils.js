import {request} from '@/plugins/axios/geoserver'

// 定义GeoServer工作空间
// 如果您的GeoServer使用不同的工作空间，请修改此处
// 例如，如果您的GeoServer工作空间是'anqing'，则设置为'anqing'
// 如果您的GeoServer工作空间是'geoserver'，则设置为'geoserver'
const WORKSPACE = 'guazhou';

/**
 * 将几何对象转换为GML格式
 * @param {Object} feature - 要素对象，包含geometry和type属性
 * @returns {String} - GML格式的几何对象
 */
const geometryToGML = (feature) => {
  debugger
  // 获取几何对象和类型
  const geometry = feature.geometry;
  const featureType = feature.type;

  console.log('转换几何对象为GML:', geometry);
  console.log('要素类型:', featureType);

  // 检查几何对象
  if (!geometry) {
    console.error('无效的几何对象:', geometry);
    throw new Error('无效的几何对象');
  }

  // 直接使用feature的type属性
  let featureTypeLower = featureType ? featureType.toLowerCase() : '';
  console.log('原始type属性:', featureTypeLower);
  // 如果数据库使用不同的坐标系统，请相应调整
  const srsName = "EPSG:3857";

  // 根据feature的type属性生成GML
  switch (featureTypeLower) {
    case 'point':
      // 点几何对象 - 使用标准的 GML 3 格式
      return `<gml:Point srsName="${srsName}">
        <gml:pos>${geometry.x} ${geometry.y}</gml:pos>
      </gml:Point>`;

    case 'multipoint':
      // 多点几何对象
      const points = geometry.points || [];
      if (points.length === 0) {
        console.error('多点几何对象没有点');
        throw new Error('多点几何对象没有点');
      }

      // 构建多点字符串
      const pointMembers = points.map(point => {
        return `<gml:pointMember>
          <gml:Point>
            <gml:pos>${point[0]} ${point[1]}</gml:pos>
          </gml:Point>
        </gml:pointMember>`;
      }).join('\n');

      return `<gml:MultiPoint srsName="${srsName}">
        ${pointMembers}
      </gml:MultiPoint>`;

    case 'polyline':
      // 线几何对象 - 单线
      const paths = geometry.paths || [];
      if (paths.length === 0) {
        console.error('线几何对象没有路径');
        throw new Error('线几何对象没有路径');
      }

      const path = paths[0];
      if (!path || path.length < 2) {
        console.error('线几何对象路径点数不足');
        throw new Error('线几何对象路径点数不足');
      }

      // 使用空格分隔坐标，符合GML 3标准
      const lineCoords = path.map(point => `${point[0]} ${point[1]}`).join(' ');

      return `<gml:LineString srsName="${srsName}">
        <gml:posList>${lineCoords}</gml:posList>
      </gml:LineString>`;

    case 'multilinestring':{
      // 多线几何对象
      const multiPaths = geometry.paths || [];
      if (multiPaths.length === 0) {
        console.error('多线几何对象没有路径');
        throw new Error('多线几何对象没有路径');
      }

      // 构建多线字符串
      const lineMembers = multiPaths.map(path => {
        if (!path || path.length < 2) {
          console.error('多线几何对象路径点数不足');
          return ''; // 跳过无效路径
        }

        // 使用空格分隔坐标，符合GML 3标准
        const coords = path.map(point => `${point[0]} ${point[1]}`).join(' ');

        return `<gml:lineStringMember>
          <gml:LineString>
            <gml:posList>${coords}</gml:posList>
          </gml:LineString>
        </gml:lineStringMember>`;
      }).filter(member => member !== '').join('\n');

      if (lineMembers === '') {
        console.error('多线几何对象没有有效路径');
        throw new Error('多线几何对象没有有效路径');
      }

      return `<gml:MultiLineString srsName="${srsName}">
        ${lineMembers}
      </gml:MultiLineString>`;
    }

    case 'polygon':
      // 面几何对象
      const rings = geometry.rings || [];
      if (rings.length === 0) {
        console.error('面几何对象没有环');
        throw new Error('面几何对象没有环');
      }

      // 处理外环
      const outerRing = rings[0];
      if (!outerRing || outerRing.length < 4) {
        console.error('面几何对象外环点数不足');
        throw new Error('面几何对象外环点数不足');
      }

      // 使用空格分隔坐标，符合GML 3标准
      const outerCoords = outerRing.map(point => `${point[0]} ${point[1]}`).join(' ');

      // 构建面GML
      let polygonXml = `<gml:Polygon srsName="${srsName}">
        <gml:exterior>
          <gml:LinearRing>
            <gml:posList>${outerCoords}</gml:posList>
          </gml:LinearRing>
        </gml:exterior>`;

      // 处理内环（如果有）
      for (let i = 1; i < rings.length; i++) {
        const innerRing = rings[i];
        if (innerRing && innerRing.length >= 4) {
          const innerCoords = innerRing.map(point => `${point[0]} ${point[1]}`).join(' ');
          polygonXml += `
        <gml:interior>
          <gml:LinearRing>
            <gml:posList>${innerCoords}</gml:posList>
          </gml:LinearRing>
        </gml:interior>`;
        }
      }

      polygonXml += `
      </gml:Polygon>`;

      return polygonXml;

    case 'multipolygon':
      // 多面几何对象
      const polygons = geometry.polygons || [];
      if (polygons.length === 0) {
        console.error('多面几何对象没有面');
        throw new Error('多面几何对象没有面');
      }

      // 构建多面字符串
      const polygonMembers = polygons.map(polygon => {
        if (!polygon || !polygon.rings || polygon.rings.length === 0) {
          console.error('多面几何对象面没有环');
          return ''; // 跳过无效面
        }

        // 处理外环
        const outerRing = polygon.rings[0];
        if (!outerRing || outerRing.length < 4) {
          console.error('多面几何对象面外环点数不足');
          return ''; // 跳过无效面
        }

        // 使用空格分隔坐标，符合GML 3标准
        const outerCoords = outerRing.map(point => `${point[0]} ${point[1]}`).join(' ');

        // 构建面GML
        let polygonXml = `<gml:polygonMember>
          <gml:Polygon>
            <gml:exterior>
              <gml:LinearRing>
                <gml:posList>${outerCoords}</gml:posList>
              </gml:LinearRing>
            </gml:exterior>`;

        // 处理内环（如果有）
        for (let i = 1; i < polygon.rings.length; i++) {
          const innerRing = polygon.rings[i];
          if (innerRing && innerRing.length >= 4) {
            const innerCoords = innerRing.map(point => `${point[0]} ${point[1]}`).join(' ');
            polygonXml += `
            <gml:interior>
              <gml:LinearRing>
                <gml:posList>${innerCoords}</gml:posList>
              </gml:LinearRing>
            </gml:interior>`;
          }
        }

        polygonXml += `
          </gml:Polygon>
        </gml:polygonMember>`;

        return polygonXml;
      }).filter(member => member !== '').join('\n');

      if (polygonMembers === '') {
        console.error('多面几何对象没有有效面');
        throw new Error('多面几何对象没有有效面');
      }

      return `<gml:MultiPolygon srsName="${srsName}">
        ${polygonMembers}
      </gml:MultiPolygon>`;

    case 'geometrycollection':
      // 几何集合
      const geometries = geometry.geometries || [];
      if (geometries.length === 0) {
        console.error('几何集合没有几何对象');
        throw new Error('几何集合没有几何对象');
      }

      // 构建几何集合字符串
      const geometryMembers = geometries.map(geom => {
        try {
          // 递归调用geometryToGML处理每个几何对象
          return `<gml:geometryMember>
            ${geometryToGML(geom)}
          </gml:geometryMember>`;
        } catch (error) {
          console.error('处理几何集合中的几何对象时出错:', error);
          return ''; // 跳过无效几何对象
        }
      }).filter(member => member !== '').join('\n');

      if (geometryMembers === '') {
        console.error('几何集合没有有效几何对象');
        throw new Error('几何集合没有有效几何对象');
      }

      return `<gml:MultiGeometry srsName="${srsName}">
        ${geometryMembers}
      </gml:MultiGeometry>`;

    case 'envelope':
      // 包络矩形
      if (!geometry.xmin || !geometry.ymin || !geometry.xmax || !geometry.ymax) {
        console.error('包络矩形缺少坐标:', geometry);
        throw new Error('包络矩形缺少坐标');
      }

      return `<gml:Envelope srsName="${srsName}">
        <gml:lowerCorner>${geometry.xmin} ${geometry.ymin}</gml:lowerCorner>
        <gml:upperCorner>${geometry.xmax} ${geometry.ymax}</gml:upperCorner>
      </gml:Envelope>`;

    case 'circle':
      // 圆形 - 使用点和半径表示
      if (!geometry.center || !geometry.radius) {
        console.error('圆形缺少中心点或半径:', geometry);
        throw new Error('圆形缺少中心点或半径');
      }

      const centerX = geometry.center.x || geometry.center[0];
      const centerY = geometry.center.y || geometry.center[1];

      // GML没有直接的圆形表示，可以使用点和半径属性
      return `<gml:Point srsName="${srsName}">
        <gml:pos>${centerX} ${centerY}</gml:pos>
        <gml:radius>${geometry.radius}</gml:radius>
      </gml:Point>`;

    default:
      console.error('不支持的几何对象类型:', geometry.type);
      throw new Error(`不支持的几何对象类型: ${geometry.type}`);
  }
};

/**
 * 使用WFS-T更新要素属性和几何信息
 * @param {string} typeName - 图层名称，例如：'anqing:pipe_line'或者'给水管线'
 * @param {Array} features - 要更新的要素数组
 * @param {Object} attributes - 要更新的属性，键值对形式
 * @param {boolean} updateGeometry - 是否更新几何信息，默认为false
 * @returns {Promise} - 返回更新结果
 */
export const updateFeatureAttributes = (typeName, features, attributes, updateGeometry = false) => {
  // 确保图层名称包含工作空间
  // 如果图层名称不包含冒号，则添加工作空间前缀
  const fullTypeName = typeName.includes(':') ? typeName : `${WORKSPACE}:${typeName}`;
  console.log('完整的图层名称:', fullTypeName);

  // 输出要素对象的结构，帮助调试
  if (features && features.length > 0) {
    console.log('要素对象结构:', features[0]);
    console.log('要素对象属性:', Object.keys(features[0]));
  } else {
    console.error('没有要更新的要素');
    return Promise.reject(new Error('没有要更新的要素'));
  }

  try {
    // 过滤掉空值和undefined值
    const filteredAttributes = Object.fromEntries(
      Object.entries(attributes).filter(([key, value]) => {
        // 保留值为0的字段，但过滤掉null、undefined和空字符串
        return value !== null && value !== undefined && (value !== '' || key === 'OBJECTID' || key === 'SID');
      })
    );

    // 构建WFS-T事务XML
    const updates = features.map(feature => {
      // 尝试不同的属性来获取ID
      let featureId = feature.id;

      // 如果featureId是字符串且包含点号，则认为它已经包含图层名称
      if (typeof featureId === 'string' && featureId.includes('.')) {
        featureId = Number(featureId.split('.')[1]);
      }

      // 构建属性更新部分
      const propertyUpdates = Object.entries(filteredAttributes).map(([key, value]) => {
        // 对属性名称进行处理，确保它与数据库中的字段名称一致
        // 在GeoServer中，字段名称通常是大写的
        const fieldName = key.toUpperCase();
        console.log('属性名称:', key, '->', fieldName);

        // 对字符串值进行转义
        let escapedValue;
        if (value === null || value === undefined) {
          escapedValue = '';
        } else if (typeof value === 'string') {
          escapedValue = value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&apos;');
        } else {
          escapedValue = value;
        }
        console.log('属性值:', key, '=', escapedValue);

        return `<wfs:Property>
          <wfs:Name>${fieldName}</wfs:Name>
          <wfs:Value>${escapedValue}</wfs:Value>
        </wfs:Property>`;
      });

      // 如果需要更新几何信息
      if (updateGeometry && feature.geometry) {
        console.log('更新几何信息:', feature.geometry);

        // 构建几何对象部分
        const geometryGML = geometryToGML(feature);
        // 添加几何更新部分
        propertyUpdates.push(`<wfs:Property>
          <wfs:Name>geom</wfs:Name>
          <wfs:Value>${geometryGML}</wfs:Value>
        </wfs:Property>`);
      }

      // 合并所有属性更新
      const allPropertyUpdates = propertyUpdates.join('');

      // 构建单个要素的更新请求
      return `<wfs:Update typeName="${fullTypeName}">
        ${allPropertyUpdates}
        <ogc:Filter>
          <ogc:FeatureId fid="${featureId}"/>
        </ogc:Filter>
      </wfs:Update>`;
    }).join('');

    // 构建完整的WFS-T事务XML
    const transactionXml = `<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  ${updates}
</wfs:Transaction>`;

    console.log('WFS-T更新事务XML:', transactionXml);

    // 发送WFS-T请求
    return request({
      url: '/geoserver/wfs',
      method: 'post',
      headers: {
        'Content-Type': 'application/xml'
      },
      data: transactionXml
    });
  } catch (error) {
    console.error('构建WFS-T更新请求时出错:', error);
    return Promise.reject(error);
  }
};

/**
 * 使用WFS-T添加要素
 * @param {string} typeName - 图层名称，例如：'anqing:pipe_line'或者'给水管线'
 * @param {Object} feature - 要添加的要素，包含geometry和attributes属性
 * @returns {Promise} - 返回添加结果
 */
export const addFeature = (typeName, feature) => {
  // 确保图层名称包含工作空间
  const fullTypeName = typeName.includes(':') ? typeName : `${WORKSPACE}:${typeName}`;
  console.log('完整的图层名称:', fullTypeName);

  // 输出要素对象的结构，帮助调试
  console.log('要添加的要素:', feature);

  try {
    // 获取几何对象
    const geometry = feature.geometry;
    if (!geometry) {
      throw new Error('要素缺少几何对象');
    }

    // 获取属性
    const attributes = feature.attributes || {};
    console.log('要素属性:', attributes);

    // 构建属性部分
    const propertyXml = Object.entries(attributes)
      .filter(([key]) => key !== 'OBJECTID' && key !== 'objectid') // 排除OBJECTID字段，让GeoServer自动生成
      .map(([key, value]) => {
        // 对属性名称进行处理，确保它与数据库中的字段名称一致
        const fieldName = key.toUpperCase();

        // 对字符串值进行转义
        let escapedValue;
        if (value === null || value === undefined) {
          escapedValue = '';
        } else if (typeof value === 'string') {
          escapedValue = value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&apos;');
        } else {
          escapedValue = value;
        }

        return `<${fieldName}>${escapedValue}</${fieldName}>`;
      })
      .join('');

    // 构建几何对象部分
    const geometryXml = geometryToGML(feature);

    // 构建完整的WFS-T事务XML
    const transactionXml = `<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:${WORKSPACE}="${WORKSPACE}"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  <wfs:Insert>
    <${fullTypeName}>
      <geom>${geometryXml}</geom>
      ${propertyXml}
    </${fullTypeName}>
  </wfs:Insert>
</wfs:Transaction>`;

    console.log('WFS-T事务XML:', transactionXml);

    // 发送WFS-T请求
    return request({
      url: '/geoserver/wfs',
      method: 'post',
      headers: {
        'Content-Type': 'application/xml'
      },
      data: transactionXml
    });
  } catch (error) {
    console.error('构建WFS-T添加请求时出错:', error);
    throw error;
  }
};

/**
 * 使用WFS-T删除要素
 * @param {string} typeName - 图层名称，例如：'anqing:pipe_line'或者'给水管线'
 * @param {Array<string>} featureIds - 要删除的要素ID数组，例如：['给水管线.1', '给水管线.2']
 * @returns {Promise} - 返回删除结果
 */
export const deleteFeature = (typeName, featureIds) => {
  // 确保图层名称包含工作空间
  const fullTypeName = typeName.includes(':') ? typeName : `${WORKSPACE}:${typeName}`;
  console.log('完整的图层名称:', fullTypeName);
  console.log('要删除的要素ID:', featureIds);

  try {
    // 构建Filter部分
    const filterXml = featureIds.map(id => {
      // 如果ID已经包含图层名称前缀，则直接使用
      const featureId = id.includes('.') ? id.split('.')[1] : id;
      return `<ogc:FeatureId fid="${featureId}"/>`;
    }).join('');

    // 构建完整的WFS-T事务XML
    const transactionXml = `<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  <wfs:Delete typeName="${fullTypeName}">
    <ogc:Filter>
      ${filterXml}
    </ogc:Filter>
  </wfs:Delete>
</wfs:Transaction>`;

    console.log('WFS-T删除事务XML:', transactionXml);

    // 发送WFS-T请求
    return request({
      url: '/geoserver/wfs',
      method: 'post',
      headers: {
        'Content-Type': 'application/xml'
      },
      data: transactionXml
    });
  } catch (error) {
    console.error('构建WFS-T删除请求时出错:', error);
    throw error;
  }
};

/**
 * 使用WFS-T基于几何位置删除要素
 * 当要素没有ID时，可以使用此函数通过几何位置删除要素
 * @param {string} typeName - 图层名称，例如：'anqing:pipe_line'或者'给水管线'
 * @param {Array<Object>} features - 要删除的要素数组
 * @returns {Promise} - 返回删除结果
 */
export const deleteFeatureByGeometry = (typeName, features) => {
  // 确保图层名称包含工作空间
  const fullTypeName = typeName.includes(':') ? typeName : `${WORKSPACE}:${typeName}`;
  console.log('完整的图层名称:', fullTypeName);
  console.log('要删除的要素数量:', features.length);

  try {
    // 构建几何过滤器
    const geometryFilters = features.map(feature => {
      const geometry = feature.geometry;
      if (!geometry) {
        console.error('要素缺少几何对象:', feature);
        return '';
      }

      // 根据几何类型构建不同的过滤器
      if (geometry.type === 'point') {
        // 点几何，使用 BBOX 过滤器，创建一个小范围的框
        const x = geometry.x;
        const y = geometry.y;
        const tolerance = 0.0001; // 容差，可以根据需要调整
        return `
          <ogc:BBOX>
            <ogc:PropertyName>geom</ogc:PropertyName>
            <gml:Envelope srsName="EPSG:3857">
              <gml:lowerCorner>${x - tolerance} ${y - tolerance}</gml:lowerCorner>
              <gml:upperCorner>${x + tolerance} ${y + tolerance}</gml:upperCorner>
            </gml:Envelope>
          </ogc:BBOX>
        `;
      } else if (geometry.type === 'polyline') {
        // 线几何，使用 BBOX 过滤器，包含整个线
        const paths = geometry.paths || [];
        if (paths.length === 0 || paths[0].length === 0) {
          console.error('线几何没有路径:', geometry);
          return '';
        }

        // 计算线的边界框
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        paths.forEach(path => {
          path.forEach(point => {
            const x = point[0];
            const y = point[1];
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
          });
        });

        // 添加一些容差
        const tolerance = 0.0001;
        return `
          <ogc:BBOX>
            <ogc:PropertyName>geom</ogc:PropertyName>
            <gml:Envelope srsName="EPSG:3857">
              <gml:lowerCorner>${minX - tolerance} ${minY - tolerance}</gml:lowerCorner>
              <gml:upperCorner>${maxX + tolerance} ${maxY + tolerance}</gml:upperCorner>
            </gml:Envelope>
          </ogc:BBOX>
        `;
      } else if (geometry.type === 'polygon') {
        // 面几何，使用 BBOX 过滤器，包含整个面
        const rings = geometry.rings || [];
        if (rings.length === 0 || rings[0].length === 0) {
          console.error('面几何没有环:', geometry);
          return '';
        }

        // 计算面的边界框
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        rings.forEach(ring => {
          ring.forEach(point => {
            const x = point[0];
            const y = point[1];
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
          });
        });

        // 添加一些容差
        const tolerance = 0.0001;
        return `
          <ogc:BBOX>
            <ogc:PropertyName>geom</ogc:PropertyName>
            <gml:Envelope srsName="EPSG:3857">
              <gml:lowerCorner>${minX - tolerance} ${minY - tolerance}</gml:lowerCorner>
              <gml:upperCorner>${maxX + tolerance} ${maxY + tolerance}</gml:upperCorner>
            </gml:Envelope>
          </ogc:BBOX>
        `;
      } else {
        console.error('不支持的几何类型:', geometry.type);
        return '';
      }
    }).filter(filter => filter !== ''); // 过滤掉空的过滤器

    if (geometryFilters.length === 0) {
      throw new Error('没有有效的几何过滤器');
    }

    // 构建 OR 过滤器，组合所有几何过滤器
    const orFilter = geometryFilters.length > 1
      ? `<ogc:Or>${geometryFilters.join('')}</ogc:Or>`
      : geometryFilters[0];

    // 构建完整的WFS-T事务XML
    const transactionXml = `<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  <wfs:Delete typeName="${fullTypeName}">
    <ogc:Filter>
      ${orFilter}
    </ogc:Filter>
  </wfs:Delete>
</wfs:Transaction>`;

    console.log('WFS-T基于几何删除事务XML:', transactionXml);

    // 发送WFS-T请求
    return request({
      url: '/geoserver/wfs',
      method: 'post',
      headers: {
        'Content-Type': 'application/xml'
      },
      data: transactionXml
    });
  } catch (error) {
    console.error('构建WFS-T基于几何删除请求时出错:', error);
    throw error;
  }
};
