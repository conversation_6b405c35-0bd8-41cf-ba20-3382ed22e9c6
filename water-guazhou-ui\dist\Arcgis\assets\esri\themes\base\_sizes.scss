// Sizes & Placement

@use "sass:math";

$border-size--active: 3px !default;
$icon-size: 16px !default;
$border-radius: 2px !default;

/////////////////////////
// Button
$button-width: 32px !default;
$button-height: 32px !default;

// button : half
$button-width--half: floor($button-width * 0.5) !default;
$button-height--half: floor($button-height * 0.5) !default;

// button : quarter
$button-width--quarter: floor($button-width * 0.25) !default;
$button-height--quarter: floor($button-height * 0.25) !default;

// button : fifth
$button-width--fifth: floor($button-width * 0.2) !default;
$button-height--fifth: floor($button-height * 0.2) !default;

// button : plus half`
$button-width--plus-half: floor($button-width * 1.5) !default;
$button-height--plus-half: floor($button-height * 1.5) !default;

// button : double
$button-width--double: floor($button-width * 2) !default;
$button-height--double: floor($button-height * 2) !default;

/////////////////////////
// List Items
$list-item-height: 48px !default;
$list-item-height--three-quarters: floor($list-item-height * 3 * 0.25) !default;

/////////////////////////
// Toggle
$toggle-offset: 4px !default;
$toggle-width: 28px !default;
$toggle-height: 16px !default;
$toggle-handle-size: $toggle-height - $toggle-offset !default;

/////////////////////////
// Panel
$panel-width: 300px !default;
$panel-width--three-quarters: floor($panel-width * 3 * 0.25) !default;
$panel-width--plus-half: floor($panel-width * 1.5) !default;

$panel-min-height--large: 240px !default;
$panel-min-height--medium: 120px !default;
$panel-min-height--small: 96px !default;
$panel-min-height--xsmall: 32px !default;

$panel-max-height--large: 680px !default;
$panel-max-height--medium: 540px !default;
$panel-max-height--small: 420px !default;
$panel-max-height--xsmall: 240px !default;

/////////////////////////
// Component (widget in view) max heights based on view height
$view-height--gt-medium__component-max-height: 680px !default;
$view-height--medium__component-max-height: 540px !default;
$view-height--small__component-max-height: 420px !default;
$view-height--xsmall__component-max-height: 240px !default;

/////////////////////////
// Popup
$docked-margin: 15px !default;
$docked-margin--double: floor($docked-margin * 2) !default;

/////////////////////////
// Loading
$looping-progress-bar-width: 20% !default;
$looping-progress-bar-params: 1500ms linear infinite !default;

$spinning-loader-size: $icon-size !default;

/////////////////////////
// Spacing
$side-spacing: 15px !default;
$cap-spacing: 12px !default;

// spacing : three quarters
$side-spacing--three-quarters: floor($side-spacing * 3 * 0.25) !default;
$cap-spacing--three-quarters: floor($cap-spacing * 3 * 0.25) !default;

// spacing : half
$side-spacing--half: floor($side-spacing * 0.5) !default;
$cap-spacing--half: floor($cap-spacing * 0.5) !default;

// spacing : third
$side-spacing--third: floor(math.div($side-spacing, 3)) !default;
$cap-spacing--third: floor(math.div($cap-spacing, 3)) !default;

// spacing : quarter
$side-spacing--quarter: floor($side-spacing * 0.25) !default;
$cap-spacing--quarter: floor($cap-spacing * 0.25) !default;

// spacing : eighth
$side-spacing--eighth: ceil($side-spacing * 0.125) !default;
$cap-spacing--eighth: ceil($cap-spacing * 0.125) !default;

// spacing : plus half
$side-spacing--plus-half: floor($side-spacing * 1.5) !default;
$cap-spacing--plus-half: floor($cap-spacing * 1.5) !default;

// spacing : double
$side-spacing--double: floor($side-spacing * 2) !default;
$cap-spacing--double: floor($cap-spacing * 2) !default;

/////////////////////////
// View UI
$view-ui-spacing: 10px !default;

/////////////////////////
// Smart Mapping Sliders
$smartmapping-slider__width: 260px !default;
$smartmapping-slider__base-height: 240px !default;
$smartmapping-slider__ramp-width: 30px !default;
$smartmapping-slider__ramp-stroke-width: 1px !default;
$smartmapping-slider__ramp-path-stroke-width: 0.5px !default;
$smartmapping-slider__histogram-width: 120px !default;
$smartmapping-slider__range-input-height: 40px !default;

$smartmapping-slider__thumb-size: 12px !default;
$smartmapping-slider__thumb-size--offset: round($smartmapping-slider__thumb-size * 0.5) !default;
$smartmapping-slider__thumb-pointer-size: round($smartmapping-slider__thumb-size * 0.5) !default;
