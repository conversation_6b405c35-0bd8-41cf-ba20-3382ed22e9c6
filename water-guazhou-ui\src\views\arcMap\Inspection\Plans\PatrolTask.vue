<!-- 巡检任务 -->
<!-- toDO: 分派时，无法回显接收人员 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="巡检任务"
    :detail-max-min="true"
    :hide-right-drawer="true"
    :hide-detail-close="true"
    @map-loaded="onMaploaded"
  >
    <template #detail-header>
      <span>巡检任务</span>
    </template>
    <template #detail-default>
      <div class="detail-page">
        <div
          v-if="state.curPage === 'table'"
          class="page-wrapper"
        >
          <Search
            ref="refSearch"
            :config="SearchConfig"
            style="margin-bottom: 8px"
          ></Search>
          <div class="table-box">
            <FormTable :config="TableConfig"></FormTable>
          </div>
        </div>
        <div
          v-show="state.curPage === 'detail'"
          class="page-wrapper"
        >
          <div class="detail-header">
            <el-icon @click="handleBack">
              <Back />
            </el-icon>
            <div class="detail-header-divider"></div>
            <span>巡检任务详情</span>
            <!-- <el-button
              :icon="Promotion"
              :size="'small'"
              style="margin-left: auto"
              @click="handleReport"
            >
              任务上报
            </el-button> -->
          </div>
          <div class="detail-main overlay-y">
            <PatrolDetail
              :view="staticState.view"
              :row="TableConfig.currentRow"
              :layer-info="state.layerInfos"
              @row-click="handleDetailRowClick"
            ></PatrolDetail>
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
  <DialogForm
    ref="refDialogForm"
    :config="DialogFormConfig"
  ></DialogForm>
  <DialogForm
    ref="refDialogFormReport"
    :config="DialogFormConfigReport"
  ></DialogForm>
  <DialogForm
    ref="refDialogFormReject"
    :config="DialogFormConfigReject"
  ></DialogForm>
</template>
<script lang="ts" setup>
import { Back, Delete, InfoFilled, Promotion, Refresh, Search as SearchIcon, ZoomIn } from '@element-plus/icons-vue'
import { queryLayerClassName } from '@/api/mapservice'
import { getSubLayerIds } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import PatrolDetail from './components/PatrolDetail.vue'
import { DeletePatrolTasks, DiapatchPatrolTasks, GetPatrolTaskList, PostWorkOrder, VerifyPatrolTask } from '@/api/patrol'
import { PatrolTaskStatusConfig } from '../config'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { ElLoading, ElMessageBox } from 'element-plus'
import { getEmergencyLevelOpetions } from '@/views/workorder/config'
import { useDistrict } from '../hooks/useDistrict'
import { PostGisOperateLog } from '@/api/system/gisSetting'
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config'
import { formatTree } from '@/utils/GlobalHelper'
import { workOrderTypeList } from '@/api/CTI/systemConfiguration'

const refDialogFormReport = ref<IDialogFormIns>()
const refDialogFormReject = ref<IDialogFormIns>()
const refSearch = ref<ISearchIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refDialogForm = ref<IDialogFormIns>()
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerIds: (string | number)[]
  layerInfos: any[]
  curPage: 'table' | 'detail'
  orderTypes: any[]
}>({
  tabs: [],
  loading: false,
  layerIds: [],
  layerInfos: [],
  curPage: 'table',
  orderTypes: []
})
const SearchConfig = reactive<ISearch>({
  scrollBarGradientColor: '#fafafa',
  filters: [
    {
      type: 'radio-button',
      options: [
        { label: '全部', value: '' },
        { label: '常规', value: 'true' },
        { label: '临时', value: 'false' }
      ],
      field: 'isNormalPlan'
    },
    {
      type: 'radio-button',
      options: [
        { label: '全部', value: '' },
        { label: '已接收', value: 'true' },
        { label: '未接收', value: 'false' }
      ],
      field: 'isReceived'
    },
    {
      type: 'datetimerange',
      field: 'fromTime',
      label: '开始时间'
    },
    {
      type: 'user-select',
      labelWidth: 60,
      field: 'receiveUserId',
      label: '巡检员'
    },
    {
      type: 'user-select',
      field: 'creator',
      label: '创建人'
    },
    {
      type: 'user-select',
      field: 'collaborateUserId',
      label: '共同处理人'
    },
    {
      type: 'input',
      label: '快速查找',
      field: 'keyword'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '批量分派',
          svgIcon: shallowRef(Promotion),
          disabled: (): boolean => !TableConfig.selectList?.length,
          click: () => handleDiapatch(true)
        },
        {
          perm: true,
          text: '批量删除',
          type: 'danger',
          svgIcon: shallowRef(Delete),
          disabled: (): boolean => !TableConfig.selectList?.length,
          click: () => handleDelete()
        }
      ]
    }
  ],
  defaultParams: {}
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    {
      minWidth: 120,
      label: '是否常规计划',
      prop: 'isNormalPlan',
      tag: true,
      tagColor: (row): string => (row.isNormalPlan ? '#409eff' : '#0cbb4a'),
      formatter: row => (row.isNormalPlan ? '常规' : '临时')
    },
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '任务名称', prop: 'name' },
    { minWidth: 120, label: '巡检周期', prop: 'planCircleName' },
    { minWidth: 160, label: '开始时间', prop: 'beginTime' },
    { minWidth: 160, label: '结束时间', prop: 'endTime' },
    { minWidth: 120, label: '巡检员', prop: 'receiveUserName' },
    { minWidth: 120, label: '创建人', prop: 'creatorName' },
    { minWidth: 160, label: '创建时间', prop: 'createTime' },
    // { minWidth: 120,label: '派发状态', prop: 'status', tag: true, tagColor: '#00ffff', },
    { minWidth: 120, label: '到位状况', prop: 'presentState' },
    { minWidth: 120, label: '反馈状况', prop: 'fallbackState' },
    {
      minWidth: 100,
      align: 'center',
      label: '任务状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string => PatrolTaskStatusConfig[row.status]?.color,
      formatter: row => {
        return PatrolTaskStatusConfig[row.status]?.text || row.status
      }
    }
  ],
  operationWidth: 250,
  operations: [
    {
      perm: true,
      text: '查看',
      svgIcon: shallowRef(ZoomIn),
      type: 'success',
      click: row => {
        TableConfig.currentRow = row
        viewTaskLocus()
      }
    },
    {
      perm: (row: any): boolean => row.status === 'PENDING' || row.status === 'REJECTED',//只有待处理和已拒绝状态才可以分派
      text: '分派',
      svgIcon: shallowRef(Promotion),
      click: row => {
        TableConfig.currentRow = row
        handleDiapatch()
      }
    },
    {
      perm: (row: any): boolean => row.status === 'VERIFY',
      text: '审核',
      svgIcon: shallowRef(Refresh),
      type: 'warning',
      click: row => {
        TableConfig.currentRow = row
        handleVerify()
      }
    },
    {
      perm: (row: any): boolean => row.status !== 'PENDING',
      text: '详情',
      svgIcon: shallowRef(InfoFilled),
      type: 'info',
      click: row => {
        state.curPage = 'detail'
        TableConfig.currentRow = row
        refreshDetail()
      }
    },
    {
      // perm: (row): boolean => row.status === 'APPROVED',
      perm: true,
      text: '删除',
      svgIcon: shallowRef(Delete),
      type: 'danger',
      click: row => handleDelete(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  },
  handleSelectChange: rows => {
    TableConfig.selectList = rows
  }
})

const DialogFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 450,
  labelPosition: 'right',
  title: '分派',
  group: [
    {
      fields: [
        {
          type: 'user-select',
          field: 'receiveUserId',
          label: '接收人员',
          departField: 'receiveUserDepartmentId',
          rules: [{ required: true, message: '请选择接收人员' }]
        },
        {
          type: 'user-select',
          multiple: true,
          field: 'collaborateUserId',
          label: '共同完成人'
        }
      ]
    }
  ],
  defaultValue: {
    collaborateUserId: [],
    receiveUserId: []
  },
  submit: params => {
    const taskIds = params.isMulti ? TableConfig.selectList?.map(item => item.id) || [] : [TableConfig.currentRow.id]
    if (!taskIds.length) {
      SLMessage.warning('请选择要分派的任务')
      return
    }
    DiapatchPatrolTasks({
      taskIdList: taskIds,
      collaborateUserId: params.collaborateUserId?.join(','),
      receiveUserId: params.receiveUserId?.join(',')
    })
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refreshData()
          refDialogForm.value?.closeDialog()
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch(error => {
        console.log(error)
        SLMessage.error('系统错误')
      })
  }
})
const handleDiapatch = async (multi?: boolean) => {
  DialogFormConfig.defaultValue = {
    isMulti: !!multi,
    receiveUserDepartmentId: TableConfig.currentRow?.receiveUserDepartmentId,
    receiveUserId: [TableConfig.currentRow?.receiveUserId]
  }
  refDialogForm.value?.openDialog()
  await nextTick()
  refDialogForm.value?.resetForm()
}
const handleDelete = (row?: any) => {
  const rows = (row ? [row] : TableConfig.selectList) || []
  const ids = rows.map(item => item.id)
  if (!ids?.length) {
    SLMessage.warning('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      DeletePatrolTasks(ids)
        .then(res => {
          if (res.data.code === 200) {
            SLMessage.success(res.data.message)
            PostGisOperateLog({
              optionName: EGigLogFunc.XUNJIANRENWU,
              type: EGisLogApp.INSPECT,
              content: `${EGisLogOperateType.DELETE}巡检任务：${rows
                .map(item => item.name + (item.code ? '【' + item.code + '】' : ''))
                .join('、')}`,
              optionType: EGisLogOperateType.DELETE
            }).catch(() => {
              console.log('生成gis操作日志失败')
            })
            refreshData()
          } else {
            SLMessage.error(res.data.message)
          }
        })
        .catch(error => {
          console.log(error)
          SLMessage.error('系统错误')
        })
    })
    .catch(() => {
      //
    })

  console.log(TableConfig.selectList)
}
const handleBack = () => {
  state.curPage = 'table'
}

const refreshDetail = () => {
  //
}
const DialogFormConfigReport = reactive<IDialogFormConfig>({
  dialogWidth: 450,
  labelPosition: 'right',
  title: '事件上报',
  group: [
    {
      fields: [
        {
          rules: [{ required: true, message: '请输入工单标题' }],
          type: 'input',
          label: '工单标题',
          field: 'title'
        },
        {
          rules: [{ required: true, message: '请选择紧急程度' }],
          type: 'select',
          label: '紧急程度',
          field: 'level',
          options: getEmergencyLevelOpetions()
        },
        {
          rules: [{ required: true, message: '请选择工单类型' }],
          type: 'select-tree',
          label: '工单类型',
          field: 'type',
          async autoFillOptions(config) {
            try {
              const res = await workOrderTypeList({ isDel: 0 })
              config.options = formatTree(res?.data?.data || [], {
                label: 'name',
                value: 'id',
                children: 'children',
                id: 'id'
              })
            } catch (error) {
              //
            }
          }
          // getOrderTypeOptions()
        },
        { type: 'textarea', label: '描述', field: 'remark' }
      ]
    }
  ],
  defaultValue: {},
  submit: params => {
    const submitParams = {
      ...params,
      code: TableConfig.currentRow?.code
    }
    PostWorkOrder(submitParams)
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refDialogFormReport.value?.closeDialog()
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch((error: any) => {
        console.log(error)
        SLMessage.error('系统错误')
      })
  }
})
// const handleReport = async () => {
//   refDialogFormReport.value?.openDialog()
// }

// 驳回原因对话框配置
const DialogFormConfigReject = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  labelPosition: 'top',
  title: '任务驳回',
  group: [
    {
      fields: [
        {
          type: 'textarea',
          field: 'remark',
          label: '驳回原因',
          placeholder: '请输入驳回原因，将通知给任务执行人',
          rows: 4,
          rules: [
            { required: true, message: '请输入驳回原因' },
            { min: 5, message: '驳回原因不能少于5个字符' },
            { max: 500, message: '驳回原因不能超过500个字符' }
          ]
        }
      ]
    }
  ],
  defaultValue: {},
  submit: params => {
    if (!TableConfig.currentRow?.id) {
      SLMessage.warning('未选择任务')
      return
    }

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    VerifyPatrolTask({
      taskIdList: [TableConfig.currentRow.id],
      status: 'REJECTED',
      rejectReason: params.remark
    })
      .then(res => {
        loading.close() // 关闭加载状态

        if (res.data.code === 200) {
          SLMessage.success('任务已驳回')
          refreshData()
          refDialogFormReject.value?.closeDialog()
        } else {
          SLMessage.error(res.data.message || '驳回失败')
        }
      })
      .catch(error => {
        loading.close() // 关闭加载状态
        console.log(error)
        SLMessage.error('系统错误')
      })
  }
})

// 审核处理函数
const handleVerify = () => {
  if (!TableConfig.currentRow?.id) {
    SLMessage.warning('未选择任务')
    return
  }

  // 弹出确认对话框，提供通过和驳回选项
  ElMessageBox.confirm('请选择审核结果', '审核', {
    confirmButtonText: '通过审核',
    cancelButtonText: '驳回',
    distinguishCancelAndClose: true,
    closeOnClickModal: false,
    type: 'warning'
  })
    .then(() => {
      // 用户选择了"通过"
      // 通过审核
      const loading = ElLoading.service({
        lock: true,
        text: '提交中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      VerifyPatrolTask({
        taskIdList: [TableConfig.currentRow.id],
        status: 'RECEIVED'
      })
        .then(res => {
          loading.close()
          if (res.data.code === 200) {
            SLMessage.success('审核通过成功')
            refreshData()
          } else {
            SLMessage.error(res.data.message || '审核通过失败')
          }
        })
        .catch(error => {
          loading.close()
          console.log(error)
          SLMessage.error('系统错误')
        })
    })
    .catch(action => {
      if (action === 'cancel') {
        // 用户选择了"驳回"，打开驳回原因对话框
        refDialogFormReject.value?.openDialog()
      }
    })
}

const district = useDistrict('viewDiv')
const viewTaskLocus = async () => {
  if (!TableConfig.currentRow) return
  refMap.value?.toggleCustomDetailMaxmin('normal')
  await nextTick()
  district.add(staticState.view, TableConfig.currentRow.districtAreaId, {
    goto: true,
    ratio: 1,
    showKeyPoint: true
  })
}
const refreshData = () => {
  const query = refSearch.value?.queryParams || {}
  GetPatrolTaskList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    ...query,
    fromTime: query.fromTime && query.fromTime[0],
    toTime: query.fromTime && query.fromTime[1],
    receiveUserId: query.receiveUserId?.join(','),
    collaborateUserId: query.collaborateUserId?.join(','),
    creator: query.creator?.join(',')
  }).then(res => {
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data?.total || 0
  })
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}
const handleDetailRowClick = () => {
  refMap.value?.toggleCustomDetailMaxmin('normal')
}
const onMaploaded = async (view: __esri.MapView) => {
  staticState.view = view
  refMap.value?.toggleCustomDetail(true)
  await nextTick()
  refMap.value?.toggleCustomDetailMaxmin('max')
  await getLayerInfo()
}
onMounted(() => {
  refreshData()
})
onBeforeUnmount(() => {
  district.destroy()
})
</script>
<style lang="scss" scoped>
.detail-page,
.page-wrapper {
  height: 100%;
}
.table-box {
  height: calc(100% - 36px);
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .el-icon {
    cursor: pointer;
  }
  .detail-header-divider {
    width: 1px;
    height: 1em;
    border: none;
    background-color: var(--el-border-color);
    margin: 0 20px;
  }
}
.detail-main {
  padding-right: 8px;
  height: calc(100% - 36px);
}
</style>
