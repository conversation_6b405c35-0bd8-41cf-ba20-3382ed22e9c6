<!-- gis抄表员 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
    ></Cards>
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { ring } from '../../components/components/chart'
import { IFormIns } from '@/components/type'

const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()
const cardsvalue = ref([
  { label: '0 个', value: '抄表员' },
  { label: '0 个', value: '用户数' }
])
const TableConfig = reactive<ITable>({
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      minWidth: 120,
      label: '抄表员',
      prop: 'key1',
      sortable: true
    },
    {
      minWidth: 120,
      label: '用户数',
      prop: 'key3'
    },
    {
      minWidth: 120,
      label: '已抄表',
      prop: 'key4'
    }
  ],
  handleRowClick: row => {
    TableConfig.currentRow = row
    emit('highlightMark', props.menu, row?.id)
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '用水性质占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 315px);
}
</style>
