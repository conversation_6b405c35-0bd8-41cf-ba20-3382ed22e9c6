import{Y as w,e as t,y as s,w as j,a as f,v as P}from"./Point-WxyopZva.js";import{c as C}from"./Analysis-DOuXm63D.js";import{T as l,fw as h,R as n,b2 as v,aO as O}from"./index-r0dFAfgr.js";import{cO as H,x as L,g2 as V,bk as m,bW as W,bV as E,g5 as k,cW as M,g6 as Q,g7 as Y,Q as B,V as D,e as G}from"./MapView-DaoQedLH.js";import{g as c}from"./persistable-CIG2ELSD.js";import{l as R,w as F}from"./widget-BcWKanF2.js";import{i as J}from"./elevationInfoUtils-5B4aSzEU.js";import"./pe-B8dP0-Ut.js";import"./multiOriginJSONSupportUtils-C0wm8_Yw.js";import"./resourceExtension-DfSw5lpL.js";function S(e,i){return x(e)===x(i)}function x(e){if(l(e))return null;const i=e.layer!=null?e.layer.id:"";let r=null;return r=e.objectId!=null?e.objectId:e.layer!=null&&"objectIdField"in e.layer&&e.layer.objectIdField!=null&&e.attributes!=null?e.attributes[e.layer.objectIdField]:e.uid,r==null?null:`o-${i}-${r}`}const z={json:{write:{writer:K,target:{"feature.layerId":{type:[Number,String]},"feature.objectId":{type:[Number,String]}}},origins:{"web-scene":{read:U}}}};function K(e,i){var r;l(e)||((r=e.layer)==null?void 0:r.objectIdField)==null||e.attributes==null||(i.feature={layerId:e.layer.id,objectId:e.attributes[e.layer.objectIdField]})}function U(e){if(e.layerId!=null&&e.objectId!=null)return{uid:null,layer:{id:e.layerId,objectIdField:"ObjectId"},attributes:{ObjectId:e.objectId}}}let p=class extends w(H(P)){constructor(i){super(i),this.position=null,this.elevationInfo=null,this.feature=null}equals(i){return h(this.position,i.position)&&h(this.elevationInfo,i.elevationInfo)&&S(this.feature,i.feature)}};t([s({type:j}),c()],p.prototype,"position",void 0),t([s({type:L}),c()],p.prototype,"elevationInfo",void 0),t([s(z)],p.prototype,"feature",void 0),p=t([f("esri.analysis.LineOfSightAnalysisObserver")],p);const T=p;let u=class extends w(V){constructor(e){super(e),this.position=null,this.elevationInfo=null,this.feature=null}equals(e){return h(this.position,e.position)&&h(this.elevationInfo,e.elevationInfo)&&S(this.feature,e.feature)}};t([s({type:j}),c()],u.prototype,"position",void 0),t([s({type:L}),c()],u.prototype,"elevationInfo",void 0),t([s(z)],u.prototype,"feature",void 0),u=t([f("esri.analysis.LineOfSightAnalysisTarget")],u);const _=u,b=m.ofType(_);let a=class extends C{constructor(e){super(e),this.type="line-of-sight",this.observer=null,this.extent=null}initialize(){this.addHandles(R(()=>this._computeExtent(),e=>{(l(e)||l(e.pending))&&this._set("extent",n(e)?e.extent:null)},F))}get targets(){return this._get("targets")||new b}set targets(e){this._set("targets",E(e,this.targets,b))}get spatialReference(){return n(this.observer)&&n(this.observer.position)?this.observer.position.spatialReference:null}get requiredPropertiesForEditing(){return[v(this.observer,e=>e.position)]}async waitComputeExtent(){const e=this._computeExtent();return n(e)?O(e.pending):Promise.resolve()}_computeExtent(){const e=this.spatialReference;if(l(this.observer)||l(this.observer.position)||l(e))return null;const i=y=>J(y.position,y.elevationInfo)==="absolute-height",r=this.observer.position,I=k(r.x,r.y,r.z,r.x,r.y,r.z);for(const y of this.targets)if(n(y.position)){const d=M(y.position,e);if(n(d.pending))return{pending:d.pending,extent:null};if(n(d.geometry)){const{x:A,y:q,z:N}=d.geometry;Q(I,[A,q,N])}}const g=Y(I,e);return i(this.observer)&&this.targets.every(i)||(g.zmin=void 0,g.zmax=void 0),{pending:null,extent:g}}clear(){this.observer=null,this.targets.removeAll()}};t([s({type:["line-of-sight"]})],a.prototype,"type",void 0),t([s({type:T,json:{read:!0,write:!0}})],a.prototype,"observer",void 0),t([s({cast:W,type:b,nonNullable:!0,json:{read:!0,write:!0}})],a.prototype,"targets",null),t([s({value:null,readOnly:!0})],a.prototype,"extent",void 0),t([s({readOnly:!0})],a.prototype,"spatialReference",null),t([s({readOnly:!0})],a.prototype,"requiredPropertiesForEditing",null),a=t([f("esri.analysis.LineOfSightAnalysis")],a);const $=a,X=m.ofType(_);let o=class extends B(D(G)){constructor(e){super(e),this.type="line-of-sight",this.operationalLayerType="LineOfSightLayer",this.analysis=new $,this.opacity=1}initialize(){this.addHandles(R(()=>this.analysis,(e,i)=>{n(i)&&i.parent===this&&(i.parent=null),n(e)&&(e.parent=this)},F))}async load(){return n(this.analysis)&&this.addResolvingPromise(this.analysis.waitComputeExtent()),this}get observer(){return v(this.analysis,e=>e.observer)}set observer(e){v(this.analysis,i=>i.observer=e)}get targets(){return n(this.analysis)?this.analysis.targets:new m}set targets(e){var i;E(e,(i=this.analysis)==null?void 0:i.targets)}get fullExtent(){return n(this.analysis)?this.analysis.extent:null}get spatialReference(){return n(this.analysis)?O(this.analysis.spatialReference):null}releaseAnalysis(e){this.analysis===e&&(this.analysis=new $)}};t([s({json:{read:!1},readOnly:!0})],o.prototype,"type",void 0),t([s({type:["LineOfSightLayer"]})],o.prototype,"operationalLayerType",void 0),t([s({type:T,json:{read:!0,write:{ignoreOrigin:!0}}})],o.prototype,"observer",null),t([s({type:X,json:{read:!0,write:{ignoreOrigin:!0}}})],o.prototype,"targets",null),t([s({nonNullable:!0,json:{read:!1,write:!1}})],o.prototype,"analysis",void 0),t([s({readOnly:!0})],o.prototype,"fullExtent",null),t([s({readOnly:!0})],o.prototype,"spatialReference",null),t([s({readOnly:!0,json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},"portal-item":{read:!1,write:!1},"web-document":{read:!1,write:!1}}}})],o.prototype,"opacity",void 0),t([s({type:["show","hide"]})],o.prototype,"listMode",void 0),o=t([f("esri.layers.LineOfSightLayer")],o);const ue=o;export{ue as default};
