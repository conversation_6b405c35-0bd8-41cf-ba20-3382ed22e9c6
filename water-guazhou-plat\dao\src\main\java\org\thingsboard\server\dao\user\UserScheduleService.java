package org.thingsboard.server.dao.user;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.dao.model.request.UserScheduleListRequest;
import org.thingsboard.server.dao.model.sql.UserSchedule;

import java.util.List;

public interface UserScheduleService {

    /**
     * 查询用户行程列表
     *
     * @param request     请求参数
     * @param currentUser 当前用户
     * @return 数据
     */
    List<UserSchedule> findList(UserScheduleListRequest request, User currentUser);

    void save(UserSchedule entity);

    void remove(List<String> ids);
}
