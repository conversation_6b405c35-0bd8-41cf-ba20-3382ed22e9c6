import{d as D,M as I,c as p,s as L,r as f,x as l,a8 as v,S as E,ar as W,a9 as C,bj as w,o as R,g as y,n as V,q as _,i as c,h as M,b6 as q,b7 as A}from"./index-r0dFAfgr.js";import{_ as B}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{I as d}from"./common-CvK_P_ao.js";import{p as N,d as j,g as O}from"./schedulingInstructions-DdZ9MUvq.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const P={class:"wrapper"},J=D({__name:"index",setup(U){const{$btnPerms:m}=I(),g=p(0),i=p(),u=p(),h=p({filters:[{label:"指令类型名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:d.QUERY,click:()=>s()},{type:"default",perm:!0,text:"重置",svgIcon:L(A),click:()=>{var e;(e=u.value)==null||e.resetForm(),s()}},{perm:!0,text:"新建",icon:d.ADD,type:"success",click:()=>b()}]}]}),n=f({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"指令类型名称",prop:"name"},{label:"指定部门名称",prop:"deptNameList"}],operationWidth:"160px",operations:[{type:"primary",text:"编辑",icon:d.EDIT,perm:m("RoleManageEdit"),click:e=>S(e)},{type:"danger",text:"删除",perm:m("RoleManageDelete"),icon:d.DELETE,click:e=>x(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,s()}}}),r=f({title:"新增",width:"500px",labelWidth:"120px",submitting:!1,submit:e=>{r.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),e.deptIdList=e.deptIdList.join(","),N(e).then(()=>{var a;r.submitting=!1,(a=i.value)==null||a.closeDrawer(),l.success(t),s()}).catch(a=>{r.submitting=!1,l.warning(a)})},defaultValue:{},group:[{fields:[{type:"input",label:"指令类型名称1",field:"name",rules:[{required:!0,message:"请输入指令类型名称"}]},{type:"select-tree",label:"指定部门",field:"deptIdList",checkStrictly:!1,multiple:!0,defaultExpandAll:!0,rules:[{required:!0,message:"请选择部门"}],options:v(()=>o.WaterSupplyTree)}]}]}),b=()=>{var e;r.title="新增",r.defaultValue={deptIdList:[]},(e=i.value)==null||e.openDrawer()},S=e=>{var a;r.title="编辑";const t=e.deptIdList.split(",");r.defaultValue={category:e.parentId,...e||{},deptIdList:t},(a=i.value)==null||a.openDrawer()},x=e=>{E("确定删除该指令类型","删除提示").then(()=>{j(e.id).then(()=>{l.success("删除成功"),g.value+=1,s()}).catch(t=>{l.error(t)})})},o=f({WaterSupplyTree:[],getWaterSupplyTreeValue:()=>{W(2).then(t=>{o.WaterSupplyTree=C(t.data.data||[]),o.WaterSupplyTree=w(o.WaterSupplyTree,{label:"layer",value:1})})}}),s=async()=>{var t;const e={size:n.pagination.limit,page:n.pagination.page,...((t=u.value)==null?void 0:t.queryParams)||{}};O(e).then(a=>{n.dataList=a.data.data.data||[],n.pagination.total=a.data.data.total||0}).catch(()=>{l.warning("请求遇到错误")})};return R(async()=>{s(),o.getWaterSupplyTreeValue()}),(e,t)=>{const a=F,T=B,k=q;return y(),V("div",P,[_(a,{ref_key:"refSearch",ref:u,config:c(h)},null,8,["config"]),(y(),M(T,{key:c(g),class:"card-table",config:c(n)},null,8,["config"])),_(k,{ref_key:"refForm",ref:i,config:c(r)},null,8,["config"])])}}});export{J as default};
