import{d as i,a0 as c,c as o,o as r,Q as v,g as p,n as f,p as t,aw as u,i as _,dy as y,C as m}from"./index-r0dFAfgr.js";const b={class:"main"},x={class:"card zutai-card"},w={class:"card-content",style:{top:"20%",left:"8%",width:"140px"}},g={class:"row"},B=i({__name:"ysc_overview1",setup(z){const e=c(),l=o({}),a=o(),d=()=>{console.log(e.projectList),e.projectList[0].id};return r(()=>{d(),a.value=setInterval(()=>{d()},3e4)}),v(()=>{clearInterval(a.value)}),(I,s)=>{var n;return p(),f("div",b,[t("div",x,[t("div",w,[s[1]||(s[1]=t("div",{class:"card-title",style:{width:"110px"}},[t("span",{style:{color:"#d8feff","text-align":"center"}},"原水电动进水阀")],-1)),t("div",g,[s[0]||(s[0]=t("div",{class:"label"},"状态：",-1)),t("div",{class:u(["status",((n=_(l).原水池)==null?void 0:n.ysc_zddf)!="0.00"?"online":"unline"])},null,2)])]),s[2]||(s[2]=y('<div class="card-content" style="top:33%;left:28%;width:140px;" data-v-2870bd81><div class="card-title" data-v-2870bd81><span style="color:#d8feff;text-align:center;" data-v-2870bd81>原水池1</span></div></div><div class="card-content ysc-zjs" style="" data-v-2870bd81><div class="card-title" data-v-2870bd81><span style="color:#d8feff;text-align:center;" data-v-2870bd81>总进水</span></div></div>',2))])])}}}),C=m(B,[["__scopeId","data-v-2870bd81"]]);export{C as default};
