import{d as U,r as x,bF as f,c as G,a8 as I,o as S,ay as j,g as h,n as b,q as a,F as s,p as e,i as n,G as k,bh as c,d_ as F,aw as q,aB as B,aJ as E,h as V,c2 as W,J as H,bz as K,bU as Q,bW as Z,cE as $,bm as tt,dD as at,bl as et,C as st}from"./index-r0dFAfgr.js";import{s as g,a as lt}from"./chart-DG9cJCI0.js";import{h as nt,i as ot}from"./zhandian-YaGuQZe6.js";import dt from"./analyzecardTable-CiY8xIDI.js";import"./index-C9hz-UZb.js";const rt={class:"wrapper"},it={class:"title_view"},ct={class:"card_row"},_t={class:"card_item"},ut={class:"card_item_title"},mt={class:"card_item_subtitle"},pt={class:"card_item_stats flex"},ft={class:"alarm_stats"},ht={class:"alarm_value"},vt={class:"alarm_stats"},bt={class:"alarm_value"},yt={class:"card_item"},gt={class:"card_item_title"},wt={class:"chart_container"},Dt={class:"card_item"},xt={class:"card_item_title"},kt={class:"chart_container"},Bt={style:{"line-height":"30px",width:"250px"}},Et={style:{"margin-right":"10px"}},Lt={style:{width:"360px",height:"30px"}},Ct=U({__name:"analyze",setup(Tt){const _=x({date:f().format("YYYY-MM")}),L=G({}),l=x({LXFB:g(),DJFB:g(),BJPX:lt(),showDate:I(()=>f(_.date).month()+1),total:0,emergencyAlarm:0,tableList:[],tabsList:[]}),y=x({activeName:"水厂",keys:["水厂","泵站","流量监测站","压力监测站","水质监测站"],data:[[],[],[]]}),z=o=>{w(o)};function J(o){const t={name:["管网报警","泵站报警","水厂报警"],data:[0,0,0]};for(const d in o.alarmData)switch(o.alarmData[d].title){case"管网":t.data[0]+=o.alarmData[d].list.length||0;break;case"泵站":t.data[1]+=o.alarmData[d].list.length||0;break;case"水厂":t.data[2]+=o.alarmData[d].list.length||0;break}l.LXFB=g(t);const u={name:["提醒报警","重要报警","紧急报警"],data:[0,0,0]};let i=[];o.alarmData.forEach(d=>{i=[...i,...d.list]}),i.forEach(d=>{u.data[d.alarmLevel-1]+=1}),l.emergencyAlarm=u.data[2],l.DJFB=g(u)}function w(o){const t={startTime:f(_.date).valueOf(),endTime:f(_.date).add(1,"M").valueOf(),stationType:o};nt(t).then(u=>{let i=0;(u.data.data||[]).forEach(d=>{i+=d.count}),l.tabsList=(u.data.data||[]).map(d=>({label:d.key,value:(d.count/i*100).toFixed(2),scale:d.count}))})}function M(){switch(l.tableList.length){case 0:return 24;case 1:return 24;case 2:return 12;case 3:return 8;default:return 8}}const Y=(o,t)=>{console.log(o,t),o&&(L.value[t]=o)};function N(){l.tableList.forEach((o,t)=>{L.value[t].reftable.exportTable()})}function A(){_.date=f().format("YYYY-MM"),D()}function D(){const o={startTime:f(_.date).valueOf(),endTime:f(_.date).add(1,"M").valueOf()};ot(o).then(t=>{t.data.code===200&&(l.total=t.data.data.total||0,l.tableList=t.data.data.alarmData||[],J(t.data.data),w(y.activeName))})}return S(()=>{D(),w("水厂")}),(o,t)=>{const u=W,i=H,d=K,m=Q,v=Z,C=$,T=j("VChart"),X=et,P=tt,O=at;return h(),b("div",rt,[a(v,{gutter:20},{default:s(()=>[a(m,{span:24},{default:s(()=>[a(d,{shadow:"always",class:"analyze_card"},{default:s(()=>[e("div",it,[t[4]||(t[4]=e("span",{style:{"font-weight":"900",color:"var(--el-text-color-secondary)","font-size":"18px"}},"报警分析",-1)),e("div",null,[a(u,{modelValue:n(_).date,"onUpdate:modelValue":t[0]||(t[0]=r=>n(_).date=r),type:"month",placeholder:"请选择日期",style:{"margin-right":"10px"},onChange:D},null,8,["modelValue"]),a(i,{onClick:A},{default:s(()=>t[2]||(t[2]=[k(" 本月 ")])),_:1}),a(i,{type:"primary",onClick:N},{default:s(()=>t[3]||(t[3]=[k(" 导出 ")])),_:1})])])]),_:1})]),_:1})]),_:1}),a(v,{gutter:20,class:"wrapper_content"},{default:s(()=>[a(m,{span:18},{default:s(()=>[a(v,{gutter:20},{default:s(()=>[a(m,{span:24},{default:s(()=>[a(d,{shadow:"always",class:"analyze_card"},{default:s(()=>[e("div",ct,[e("div",_t,[e("div",ut,c(n(l).showDate)+"月报警情况",1),e("div",mt,c(n(l).showDate)+"月报警情况概览：共计报警"+c(n(l).total)+"次 ",1),t[5]||(t[5]=e("div",{class:"card_item_divider"},null,-1)),e("div",pt,[e("div",ft,[e("span",null,c(n(l).showDate)+"月共计报警(次)",1),e("div",ht,c(n(l).total),1)]),e("div",vt,[e("span",null,c(n(l).showDate)+"月紧急报警(次)",1),e("div",bt,c(n(l).emergencyAlarm),1)])])]),e("div",yt,[e("div",gt,[e("div",null,c(n(l).showDate)+"月报警类型分布",1),a(i,{key:"",text:""},{default:s(()=>[a(C,null,{default:s(()=>[a(n(F))]),_:1})]),_:1})]),e("div",wt,[a(T,{option:n(l).LXFB,autoresize:!0},null,8,["option"])])]),e("div",Dt,[e("div",xt,[e("div",null,c(n(l).showDate)+"月报警等级分布",1),a(i,{key:"",text:""},{default:s(()=>[a(C,null,{default:s(()=>[a(n(F))]),_:1})]),_:1})]),e("div",kt,[a(T,{option:n(l).DJFB,autoresize:!0},null,8,["option"])])])])]),_:1})]),_:1})]),_:1}),a(v,null,{default:s(()=>[a(m,{span:24},{default:s(()=>[a(v,{gutter:20,class:q({greater_than_three:!(n(l).tableList.length>3)})},{default:s(()=>[(h(!0),b(B,null,E(n(l).tableList,(r,p)=>(h(),V(m,{key:p,span:M()},{default:s(()=>[a(dt,{ref_for:!0,ref:R=>Y(R,p),title:r.title,value:r.list,total:n(l).tableList.length},null,8,["title","value","total"])]),_:2},1032,["span"]))),128))]),_:1},8,["class"])]),_:1})]),_:1})]),_:1}),a(m,{span:6},{default:s(()=>[a(v,{gutter:20},{default:s(()=>[a(m,{span:24},{default:s(()=>[a(d,{shadow:"always",class:"analyze_card",style:{height:"735px"}},{default:s(()=>[t[6]||(t[6]=e("div",{class:"card_title"},[e("div",null,"紧急报警排序")],-1)),a(P,{modelValue:n(y).activeName,"onUpdate:modelValue":t[1]||(t[1]=r=>n(y).activeName=r),class:"demo-tabs",onTabChange:z},{default:s(()=>[(h(!0),b(B,null,E(n(y).keys,(r,p)=>(h(),V(X,{key:p,label:r,name:r},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),e("div",null,[(h(!0),b(B,null,E(n(l).tabsList,(r,p)=>(h(),b("div",{key:p,class:"sort"},[e("div",Bt,[e("span",Et,c(p+1),1),e("span",null,c(r.label),1)]),e("div",Lt,[a(O,{percentage:r.scale},{default:s(()=>[a(i,{text:"",style:{width:"50px"}},{default:s(()=>[k(c(r.scale)+"次 ",1)]),_:2},1024)]),_:2},1032,["percentage"])])]))),128))])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])}}}),Yt=st(Ct,[["__scopeId","data-v-e4d1d826"]]);export{Yt as default};
