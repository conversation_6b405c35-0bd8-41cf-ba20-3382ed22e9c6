<template>
  <div>
    <div
      :style="{ width: width, height: height }"
      class="userContainer"
    >
      <el-select
        v-model="selectedUsers"
        :multiple="multiple"
        :disabled="disabledStu"
        filterable
        value-key="id"
        :placeholder="multiple ? '请选择用户(可多选)' : '请选择用户'"
        style="width: 100%"
        @change="handleUserChange"
      >
        <el-option
          v-for="user in userOptions"
          :key="user.id"
          :label="user.firstName"
          :value="user"
        />
      </el-select>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType, ref, onMounted } from 'vue';
import { removeSlash } from '@/utils/removeIdSlash';
import { getAllUser } from '@/api/user';

export default defineComponent({
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '40px'
    },
    users: {
      type: Array as PropType<any[]>,
      default: () => []
    },
    disabledStu: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  emits: ['checkUsers'],
  setup(props, { emit }) {
    // 用户选项列表
    const userOptions = ref<any[]>([]);

    // 已选择的用户
    const selectedUsers = ref(props.multiple ? (Array.isArray(props.users) ? [...props.users] : []) : (Array.isArray(props.users) && props.users.length > 0 ? props.users[0] : null));

    // 加载状态
    const loading = ref(false);

    // 初始化时加载用户列表
    onMounted(async () => {
      await loadUserOptions();
    });

    // 加载用户选项
    const loadUserOptions = async () => {
      if (userOptions.value.length > 0) return;

      loading.value = true;
      try {
        const res = await getAllUser();
        let userData: any[] = [];

        if (res && res.data && Array.isArray(res.data)) {
          userData = res.data;
        } else if (res && typeof res.data === 'object' && !Array.isArray(res.data)) {
          const dataArray = res.data.data || res.data.list || res.data.records || [];
          if (Array.isArray(dataArray)) {
            userData = dataArray;
          }
        }

        // 处理用户数据
        userOptions.value = userData.map((user: any) => ({
          firstName: user.firstName || user.name || user.email || '未命名用户',
          id: removeSlash(user.id?.id || user.id || '')
        }));

        // 如果有初始选中的用户，需要在用户列表加载后重新设置，确保id匹配
        if (props.users && props.users.length > 0) {
          const userIds = props.users.map(u => u.id);

          if (props.multiple) {
            selectedUsers.value = userOptions.value.filter(u => userIds.includes(u.id));
          } else {
            const matchedUser = userOptions.value.find(u => userIds.includes(u.id));
            if (matchedUser) {
              selectedUsers.value = matchedUser;
            }
          }
        }
      } catch (error) {
        userOptions.value = [];
      } finally {
        loading.value = false;
      }
    };

    // 处理用户选择变化
    const handleUserChange = (value: any) => {
      if (props.multiple) {
        emit('checkUsers', Array.isArray(value) ? value : []);
      } else {
        emit('checkUsers', value ? [value] : []);
      }
    };

    return {
      userOptions,
      selectedUsers,
      loading,
      handleUserChange
    };
  }
});
</script>

<style lang="scss" scoped>
.userContainer {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}
</style>
