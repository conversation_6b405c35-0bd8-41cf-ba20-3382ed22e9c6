<template>
  <div class="chart-wrapper">
    <VChart :option="state.deviceTypeOption"></VChart>
  </div>
</template>
<script lang="ts" setup>
// import { QueryPipeDataService } from '@/api/mapservice/pipe'
// import { staticPipe } from '@/utils/MapHelper'

const state = reactive<{
  deviceTypeOption: any
}>({
  deviceTypeOption: null
})
const generateDeviceTypeOption = (xData, yData) => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      containLabel: true,
      left: 20,
      top: 40,
      right: 20,
      bottom: 20
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        color: '#fff',
        rotate: 45
      }
    },
    yAxis: {
      name: '个',
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#666',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#00ffff'
      }
    },
    series: [
      {
        type: 'bar',
        data: yData,
        barWidth: 10,
        itemStyle: {
          color: '#00ffff'
        }
      }
    ]
  }
}

// 管线统计
// const staticStatePipe = async () => {
//   const layerInfoes = await QueryPipeDataService()
//   staticPipe('count', {
//     layerIds: layerInfoes.data?.layers?.map(item => item.id)
//   })
//   staticPipe('count', {
//     layerIds: layerInfoes.data?.layers?.find(item => item.name==='节点')
//   })
//   const resObj: Record<string, any> = {}
//   countRes.map(item => {
//     const count = item.rows[0]['OBJECTID']
//     switch (item.layername) {
//       case '阀门':
//         resObj.valve = count
//         break
//       case '水表':
//         resObj.meter = count
//         break
//       case '排气阀':
//         resObj.drayAirValve = count
//         break
//       case '消防栓':
//         resObj.hydrant = count
//         break
//       case '三通':
//         resObj.threeCorss = count
//         break
//       default:
//         break
//     }
//   })
// }
onMounted(() => {
  state.deviceTypeOption = generateDeviceTypeOption(
    ['排气阀', '消防栓', '流量计', '压力计', '阀门', '水表', '节点', '四通', '三通', '弯头', '管线'],
    [12, 32, 46, 46, 215, 541, 221, 75, 124, 165, 789]
  )
})
</script>
<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
