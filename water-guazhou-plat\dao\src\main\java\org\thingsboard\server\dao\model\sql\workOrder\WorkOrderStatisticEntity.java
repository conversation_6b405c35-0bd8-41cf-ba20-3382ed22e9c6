package org.thingsboard.server.dao.model.sql.workOrder;

import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@ResponseEntity
public class WorkOrderStatisticEntity {

    private StatisticItem organizers;

    private StatisticItem types;

    public void setOrganizer(StatisticItem result) {
        organizers = result;
    }

    public void setTypes(StatisticItem result) {
        types = result;
    }

    public StatisticItem getTypes() {
        return types;
    }

    public StatisticItem getOrganizers() {
        return organizers;
    }
}
