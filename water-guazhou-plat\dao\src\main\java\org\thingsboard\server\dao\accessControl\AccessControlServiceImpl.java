package org.thingsboard.server.dao.accessControl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.AccessControl;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.sql.accessControl.AccessControlRepository;
import org.thingsboard.server.dao.video.VideoService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccessControlServiceImpl implements AccessControlService {

    @Autowired
    private AccessControlRepository accessControlRepository;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private AccessControlVideoService accessControlVideoService;

    @Autowired
    private VideoService videoService;

    @Override
    public PageData<AccessControl> findList(int page, int size, String projectId, TenantId tenantId) {
        PageRequest pageRequest = new PageRequest(page - 1, size);

        List<String> projectList = new ArrayList<>();
        if (StringUtils.isNotBlank(projectId)) {
            projectList.add(projectId);
        } else {
            projectList = projectService.findByTenantId(tenantId).stream().map(ProjectEntity::getId).collect(Collectors.toList());
        }

        Page<AccessControl> pageResult = accessControlRepository.findList(projectList, pageRequest);

        // 为每个门禁加载关联的视频信息
        List<AccessControl> accessControlList = pageResult.getContent();
        for (AccessControl accessControl : accessControlList) {
            List<VideoEntity> videoList = accessControlVideoService.findVideosByAccessControlId(accessControl.getId());
            accessControl.setVideoList(videoList);

            if (videoList != null && !videoList.isEmpty()) {
                List<String> videoIds = videoList.stream()
                        .map(VideoEntity::getId)
                        .collect(Collectors.toList());
                accessControl.setVideoIds(videoIds);
            }
        }

        return new PageData<>(pageResult.getTotalElements(), accessControlList);
    }

    @Override
    public void save(AccessControl entity) {
        accessControlRepository.save(entity);
    }

    @Override
    public AccessControl findById(String id) {
        return accessControlRepository.findOne(id);
    }

    @Override
    @Transactional
    public void remove(List<String> ids) {
        for (String id : ids) {
            // 先删除关联的视频
            accessControlVideoService.deleteByAccessControlId(id);
            // 再删除门禁
            accessControlRepository.delete(id);
        }
    }

    @Override
    @Transactional
    public AccessControl saveWithVideos(AccessControl entity, TenantId tenantId) {
        // 设置租户ID和创建时间
        if (StringUtils.isBlank(entity.getId())) {
            entity.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            entity.setCreateTime(new Date());
        }

        // 保存门禁基本信息
        AccessControl savedEntity = accessControlRepository.save(entity);

        // 处理视频关联（无论是否有视频ID都要处理，以便清空原有关联）
        String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());
        List<String> videoIds = entity.getVideoIds();
        if (videoIds == null) {
            videoIds = new ArrayList<>();
        }
        accessControlVideoService.batchSave(savedEntity.getId(), videoIds, tenantIdStr);

        return savedEntity;
    }

    @Override
    public AccessControl findByIdWithVideos(String id) {
        AccessControl accessControl = accessControlRepository.findOne(id);
        if (accessControl != null) {
            // 查询关联的视频列表
            List<VideoEntity> videoList = accessControlVideoService.findVideosByAccessControlId(id);
            accessControl.setVideoList(videoList);

            // 设置视频ID列表
            if (videoList != null && !videoList.isEmpty()) {
                List<String> videoIds = videoList.stream()
                        .map(VideoEntity::getId)
                        .collect(Collectors.toList());
                accessControl.setVideoIds(videoIds);
            }
        }
        return accessControl;
    }

    @Override
    public List<VideoEntity> getAvailableVideos(String projectId, TenantId tenantId) {
        String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());
        if (StringUtils.isNotBlank(projectId)) {
            // 根据项目ID查询视频
            return videoService.findByProject(projectId, "", "", "");
        } else {
            // 查询租户下所有视频
            return videoService.findAll("", tenantIdStr);
        }
    }
}
