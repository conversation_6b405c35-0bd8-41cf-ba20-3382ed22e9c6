package org.thingsboard.server.dao.orderWork;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderEmergencyLevel;

import java.util.List;

public interface WorkOrderEmergencyLevelService {

    List<WorkOrderEmergencyLevel> findList(String status, TenantId tenantId);

    void changeStatus(WorkOrderEmergencyLevel param);

    void save(WorkOrderEmergencyLevel entity);

    void remove(List<String> ids);

}
