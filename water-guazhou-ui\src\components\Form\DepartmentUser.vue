<template>
  <el-popover
    ref="refPop"
    placement="bottom"
    :width="state.width"
    trigger="click"
    :disabled="(props.config as any).readonly"
  >
    <el-tabs v-model="activeName" class="tabs" @tab-change="handleChange">
      <el-tab-pane class="tab_pane" label="部门" name="department">
        <el-tree
          :default-expand-all="true"
          :data="data.WaterSupplyTree"
          :props="defaultProps"
          accordion
          highlight-current
          @node-click="handleNodeClick"
        />
      </el-tab-pane>
      <el-tab-pane class="tab_pane" label="用户" name="user">
        <div>
          <template v-if="(props.config as any).multiple">
            <el-scrollbar height="310px">
              <el-tree
                ref="refTree"
                v-loading="loading"
                node-key="value"
                show-checkbox
                :data="data.UserList"
                :props="defaultProps"
                accordion
                @check-change="handleCheckChangeUser"
              />
            </el-scrollbar>
            <div style="float: right">
              <el-tooltip
                class="box-item"
                effect="light"
                content="重置"
                placement="top"
              >
                <el-button
                  type="primary"
                  :icon="Refresh"
                  size="small"
                  @click="addUser(false)"
                />
              </el-tooltip>
              <el-tooltip
                class="box-item"
                effect="light"
                content="全选"
                placement="top"
              >
                <el-button
                  type="success"
                  :icon="Check"
                  size="small"
                  @click="addUser(true)"
                />
              </el-tooltip>
            </div>
          </template>
          <template v-else>
            <el-tree
              v-loading="loading"
              :data="data.UserList"
              :props="defaultProps"
              accordion
              @node-click="handleNodeClickUser"
            />
          </template>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #reference>
      <template v-if="(props.config as any).multiple">
        <el-select
          ref="refValue"
          v-model="value"
          style="min-width: 180px"
          multiple
          :disabled="(props.config as any).readonly"
          :collapse-tags="(props.config as any).collapseTags || false"
          collapse-tags-tooltip
          :placeholder="(config as any).placeholder || (config as any).label"
          @click="handleClick"
        >
          <template v-for="(item, index) in data.AllUserList" :key="index">
            <el-option :label="item.label" :value="item.value" />
          </template>
        </el-select>
      </template>
      <template v-else>
        <el-select
          ref="refValue"
          v-model="value"
          style="min-width: 180px"
          clearable
          :disabled="(props.config as any).readonly"
          :placeholder="(config as any).placeholder || (config as any).label"
          @click="handleClick"
        >
          <template v-for="(item, index) in data.AllUserList" :key="index">
            <el-option :label="item.label" :value="item.value" />
          </template>
        </el-select>
      </template>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import { Refresh, Check } from '@element-plus/icons-vue';
import { ElPopover, ElMessage, dayjs } from 'element-plus';
// import { listUser, deptTreeSelect } from '@/api/system/user';
import { getWaterSupplyTree, getAllUser } from '@/api/company_org';
import { getUserList } from '@/api/user/index';
import { traverse } from '@/utils/GlobalHelper';
import { removeSlash } from '@/utils/removeIdSlash';

const refPop = ref<InstanceType<typeof ElPopover>>();
const refValue = ref();
const refTree = ref();

const props = defineProps<{
  config: IFormDepartmentUser;
  modelValue?: any;
}>();

const state = reactive({
  width: 300,
  key: dayjs()
});

const emits = defineEmits(['update:modelValue', 'change']);

const value = computed({
  get: () => props.modelValue || [],
  set: (nv) => {
    emits('update:modelValue', nv);
    const key = data.AllUserList.find((item) => item.value === nv);
    emits('change', nv, key);
  }
});

const label = ref('');
const loading = ref(false);

const activeName = ref('department');

const handleClick = () => {
  // 设置宽度
  state.width = refValue.value?.$el.offsetWidth ?? 400;

  refValue.value?.blur();
  activeName.value = 'department';
};

const handleNodeClick = (val: any) => {
  loading.value = true;
  data.getUserListValue(val.id);
  activeName.value = 'user';
};

// 多选添加用户
const addUser = (status: boolean) => {
  if (status) {
    const keys = [
      ...new Set([
        ...data.UserList.map((item) => item.value),
        ...(value?.value || [])
      ])
    ];
    value.value = keys;
    // 设置默认选中
    if (Array.isArray(value.value)) {
      refTree.value?.setCheckedKeys([...keys]);
    } else [refTree.value?.setCheckedKeys([])];
  } else {
    value.value = [];
    refTree.value?.setCheckedKeys([]);
  }
};

const handleNodeClickUser = (val: any) => {
  value.value = val.value || [];
  label.value = val.label;
  refPop.value?.hide();
};

const handleCheckChangeUser = (val, type) => {
  if (!val.value) {
    if (props.config.multiple) value.value = [];
    else value.value = null;
    return;
  }
  if (!type) {
    // 数组去重
    const k = Array.from(new Set([...value?.value]));
    value.value = k.filter((item) => item !== val.value);
  } else {
    value.value = Array.from(new Set([...value?.value, val.value]));
  }
};

const defaultProps = {
  children: 'children',
  label: 'label'
};

const data = reactive({
  // 部门
  WaterSupplyTree: [],
  // 用户
  UserList: [] as any[],
  AllUserList: [] as any[],
  // 获取部门
  getWaterSupplyTreeValue: () => {
    const depth = 2;
    getWaterSupplyTree(depth).then((res) => {
      data.WaterSupplyTree = traverse(res.data.data || []);
    });
  },
  // 获取用户
  getUserListValue: (pid: string) => {
    getUserList({ pid })
      .then((res) => {
        const value = res.data.data.data || [];
        data.UserList = value.map((item) => {
          return { label: item.firstName, value: removeSlash(item.id.id) };
        });
        loading.value = false;
      })
      .catch((error) => {
        loading.value = false;
        ElMessage.warning(error);
      });
  },
  getuserAllList: (val) => {
    getAllUser().then((res) => {
      const value = traverse(res.data.data || [], 'children', {
        label: 'firstName',
        value: ['id', 'id']
      });
      value.map((item: any) => {
        item.value = removeSlash(item.value);
        if (item.value === val) {
          label.value = item.label;
          return item;
        }
      });
      data.UserList = value;
      data.AllUserList = value;
    });
  }
});

function setLabel(val) {
  data.UserList.map((item: any) => {
    if (item.value === val) {
      label.value = item.label;
      return item;
    }
  });
}

const handleChange = (tab: string) => {
  if (tab === 'user') {
    const config = props.config as IFormDepartmentUser;
    if (config.multiple) {
      refTree.value.setCheckedKeys(value.value || []);
    } else {
      //
    }
  }
};

watch(value, (newval) => {
  if (!newval) {
    label.value = '';
  } else {
    setLabel(props.modelValue);
  }
});

watch(label, (newval) => {
  if (!newval) {
    value.value = '';
  }
});

onMounted(() => {
  data.getuserAllList(props.modelValue);
});

onBeforeMount(() => {
  data.getWaterSupplyTreeValue();
});
</script>

<style lang="scss" scoped>
.tabs {
  height: 400px;

  .tab_pane {
    height: 350px;
    overflow-y: auto;
  }
}

.button {
  float: left;
  //right: 0px;
}

.multiple {
  height: 310px;
}
</style>
