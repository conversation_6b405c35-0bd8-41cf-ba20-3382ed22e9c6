package org.thingsboard.server.dao.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainStandard;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainStandardMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class MaintainStandardServiceImpl implements MaintainStandardService {

    @Autowired
    private MaintainStandardMapper maintainStandardMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Override
    public PageData getList(String deviceTypeId, String keywords, int page, int size, String tenantId) {

        // 获取所有deviceTypeId
        List<String> deviceTypeSerialIds = null;
        if (StringUtils.isNotBlank(deviceTypeId)) {
            deviceTypeSerialIds = new ArrayList<>();
            DeviceType deviceType = deviceTypeMapper.selectById(deviceTypeId);
            if (deviceType != null) {
                deviceTypeSerialIds.add(deviceType.getSerialId());
                this.getAllDeviceTypeId(Collections.singletonList(deviceTypeId), deviceTypeSerialIds);
            } else {
                deviceTypeSerialIds.add("-");
            }
        }

        List<MaintainStandard> maintainStandardList = maintainStandardMapper.getList(deviceTypeSerialIds, keywords, page, size, tenantId);

        int count = maintainStandardMapper.getListCount(deviceTypeSerialIds, keywords, tenantId);
        return new PageData(count, maintainStandardList);
    }

    @Override
    public MaintainStandard save(MaintainStandard maintainStandard) {
        if (StringUtils.isBlank(maintainStandard.getId())) {
            maintainStandard.setCreateTime(new Date());
            maintainStandardMapper.insert(maintainStandard);
        } else {
            maintainStandardMapper.updateById(maintainStandard);
        }
        return maintainStandard;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        maintainStandardMapper.deleteBatchIds(ids);
        return IstarResponse.ok("删除成功");
    }

    // 设备类型树
    public void getAllDeviceTypeId(List<String> input, List<String> output) {
        QueryWrapper<DeviceType> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("parent_id", input);
        List<DeviceType> list = deviceTypeMapper.selectList(queryWrapper);
        if (list.size() > 0) {
            List<String> ids = list.stream().map(a -> a.getId()).collect(Collectors.toList());
            output.addAll(list.stream().map(a -> a.getSerialId()).collect(Collectors.toList()));
            this.getAllDeviceTypeId(ids, output);
        }
    }
}
