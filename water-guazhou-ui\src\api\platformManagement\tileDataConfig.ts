import { request } from '@/plugins/axios'

// 获取瓦片数据配置列表
export function getTileDataConfigList(params: any) {
  return request({
    url: '/api/base/tile/configuration/list',
    method: 'get',
    params
  })
}

// 获取瓦片数据配置详情
export function getTileDataConfigDetail(id: string) {
  return request({
    url: '/api/base/tile/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增瓦片数据配置
export function addTileDataConfig(data: any) {
  return request({
    url: '/api/base/tile/configuration/add',
    method: 'post',
    data
  })
}

// 修改瓦片数据配置
export function editTileDataConfig(data: any) {
  return request({
    url: '/api/base/tile/configuration/edit',
    method: 'post',
    data
  })
}

// 删除瓦片数据配置
export function deleteTileDataConfig(ids: string[]) {
  return request({
    url: '/api/base/tile/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 