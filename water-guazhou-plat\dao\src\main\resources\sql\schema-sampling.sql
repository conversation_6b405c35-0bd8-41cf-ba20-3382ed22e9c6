-- 水质采样记录表
CREATE TABLE IF NOT EXISTS tb_water_sampling (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    creator VARCHAR(36),
    sampling_time TIMESTAMP,
    sampling_location VARCHAR(255),
    sampling_person VARCHAR(255),
    record_file VARCHAR(255),
    sampling_method VARCHAR(255),
    sample_number VARCHAR(100),
    sample_type VARCHAR(100),
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    remark TEXT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_water_sampling_tenant ON tb_water_sampling(tenant_id);
CREATE INDEX IF NOT EXISTS idx_water_sampling_time ON tb_water_sampling(sampling_time);
CREATE INDEX IF NOT EXISTS idx_water_sampling_location ON tb_water_sampling(sampling_location);
CREATE INDEX IF NOT EXISTS idx_water_sampling_person ON tb_water_sampling(sampling_person);

-- 注释
COMMENT ON TABLE tb_water_sampling IS '水质采样记录表';
COMMENT ON COLUMN tb_water_sampling.id IS '主键ID';
COMMENT ON COLUMN tb_water_sampling.tenant_id IS '租户ID';
COMMENT ON COLUMN tb_water_sampling.creator IS '创建人ID';
COMMENT ON COLUMN tb_water_sampling.sampling_time IS '采样时间';
COMMENT ON COLUMN tb_water_sampling.sampling_location IS '采样地点';
COMMENT ON COLUMN tb_water_sampling.sampling_person IS '采样人员';
COMMENT ON COLUMN tb_water_sampling.record_file IS '采样记录文件';
COMMENT ON COLUMN tb_water_sampling.sampling_method IS '采样方法';
COMMENT ON COLUMN tb_water_sampling.sample_number IS '样品编号';
COMMENT ON COLUMN tb_water_sampling.sample_type IS '样品类型';
COMMENT ON COLUMN tb_water_sampling.create_time IS '创建时间';
COMMENT ON COLUMN tb_water_sampling.update_time IS '更新时间';
COMMENT ON COLUMN tb_water_sampling.remark IS '备注';
