package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.*;
import org.thingsboard.server.dao.model.sql.dma.JinzhouDmaBookEntity;
import org.thingsboard.server.dao.model.sql.revenue.CopyDataReadMeterData;
import org.thingsboard.server.dao.model.sql.revenue.SysCodeDetail;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.*;
import org.thingsboard.server.dao.revenue.SysCodeService;
import org.thingsboard.server.dao.sql.dma.JinzhouDmaBookRepository;
import org.thingsboard.server.dao.sql.revenue.CopyDataReadMeterDataMapper;
import org.thingsboard.server.dao.sql.revenue.CustInfoMapper;
import org.thingsboard.server.dao.sql.smartPipe.*;
import org.thingsboard.server.dao.util.InfluxUtil;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.TreeUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * dma分区实现
 */
@Service
public class PartitionServiceImpl implements PartitionService {
    @Autowired
    private PartitionMapper partitionMapper;

    @Autowired
    private InfluxUtil influxUtil;

    @Autowired
    private PipePartitionTotalFlowMapper pipePartitionTotalFlowMapper;

    @Autowired
    private PartitionMountMapper partitionMountMapper;

    @Autowired
    private CopyDataReadMeterDataMapper copyDataReadMeterDataMapper;

    @Autowired
    private PipeCopyDataReadMeterDataMapper pipeCopyDataReadMeterDataMapper;

    @Autowired
    private PipePartitionCustMapper pipePartitionCustMapper;

    @Autowired
    private PipeMinFlowConfigMapper pipeMinFlowConfigMapper;

    @Autowired
    private CustInfoMapper custInfoMapper;

    @Autowired
    private SysCodeService sysCodeService;

    @Autowired
    private PipeStatisticsReportService pipeStatisticsReportService;

    @Autowired
    private JinzhouDmaBookRepository jinzhouDmaBookRepository;

    @Override
    public Partition getById(String id) {
        Partition partition = partitionMapper.getDetail(id);
        if (partition == null) {
            return null;
        }

        return partition;
    }

    @Override
    @Transactional
    public Partition save(Partition partition) {

        partition.setUpdateTime(new Date());
        if (StringUtils.isBlank(partition.getId())) {
            partition.setCreateTime(new Date());
            // 分区编号
            partition.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.PARTITION, ""));
            partitionMapper.insert(partition);
        } else {
            partitionMapper.updateById(partition);
        }
        return partition;
    }

    @Override
    public void delete(List<String> ids) throws ThingsboardException {
        List<Partition> dmaPartitionEntities;
        Map queryMap = new HashMap();
        for (String id : ids) {
            queryMap.put("pid", id);
            // 该分区下面是否有分区
            dmaPartitionEntities = partitionMapper.selectByMap(queryMap);
            if (dmaPartitionEntities != null && dmaPartitionEntities.size() > 0) {
                throw new ThingsboardException("请先删除该分区下的子分区", ThingsboardErrorCode.GENERAL);
            }
            partitionMapper.deleteById(id);
            try {
                JinzhouDmaBookEntity one = jinzhouDmaBookRepository.findOne(id);
                if (one != null) {
                    jinzhouDmaBookRepository.delete(id);
                }
            } catch (Exception e) {
            }
        }
    }

    @Override
    public List<PartitionTreeDTO> getList(Map<String, Object> params) {

        String name = (String) params.get("name");
        // 获取根列表
        List<PartitionTreeDTO> partitionTreeDTOS = partitionMapper.getRootIdNameMapList(String.valueOf(params.get("tenantId")));

        // 构建树
        this.buildTree(partitionTreeDTOS);

        // 过滤
        if (StringUtils.isNotBlank(name)) {
            partitionTreeDTOS = this.containsName(partitionTreeDTOS, name);
        }
        return partitionTreeDTOS;
    }

    @Override
    public List<PartitionTreeDTO> getPartitionDeviceTree(String type, String tenantId) {
        // 获取根列表
        List<PartitionTreeDTO> partitionTreeDTOList = partitionMapper.getRootIdNameMapList(String.valueOf(tenantId));
        switch (type) {
            case "flow":
                PartitionTreeDTO subPartiPartitionTreeDTO;
                for (PartitionTreeDTO partitionTreeDTO : partitionTreeDTOList) {
                    partitionTreeDTO.setDeviceId(partitionTreeDTO.getId());
                    partitionTreeDTO.setId(UUID.randomUUID().toString());
                    subPartiPartitionTreeDTO = new PartitionTreeDTO(partitionTreeDTO.getDeviceId(), "水厂供水", partitionTreeDTO.getType(), UUID.randomUUID().toString());
                    partitionTreeDTO.getChildren().add(subPartiPartitionTreeDTO);
                    // 根分区作为设备
                    subPartiPartitionTreeDTO = new PartitionTreeDTO(partitionTreeDTO.getDeviceId(), partitionTreeDTO.getName(), "3", UUID.randomUUID().toString());
                    partitionTreeDTO.getChildren().get(0).getChildren().add(subPartiPartitionTreeDTO);
                    // 根分区关联的流量计
                    List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionTreeDTO.getDeviceId()), "1", "", tenantId);
                    for (PartitionMount partitionMount : partitionMountList) {
                        subPartiPartitionTreeDTO = new PartitionTreeDTO(partitionMount.getDeviceId(), partitionMount.getName(), "3", UUID.randomUUID().toString());
                        partitionTreeDTO.getChildren().get(0).getChildren().add(subPartiPartitionTreeDTO);
                    }

                    // 区域供水
                    subPartiPartitionTreeDTO = new PartitionTreeDTO(partitionTreeDTO.getDeviceId(), "区域供水", partitionTreeDTO.getType(), UUID.randomUUID().toString());
                    List<String> partitionIdList = new ArrayList<>();
                    this.buildPartitionDeviceTree(subPartiPartitionTreeDTO, partitionIdList);

                    // 根据分区id查设备
                    partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "", tenantId);
                    Map<String, List<PartitionMount>> partitionMountMap = new HashMap<>();
                    for (PartitionMount partitionMount : partitionMountList) {
                        if (partitionMountMap.get(partitionMount.getPartitionId()) == null) {
                            partitionMountMap.put(partitionMount.getPartitionId(), new ArrayList<>());
                        }
                        partitionMountMap.get(partitionMount.getPartitionId()).add(partitionMount);
                    }
                    this.partitionMountDevice(subPartiPartitionTreeDTO.getChildren(), partitionMountMap, "1");
                    partitionTreeDTO.getChildren().add(subPartiPartitionTreeDTO);

                    // 大用户 只查到DMA分区
                    subPartiPartitionTreeDTO = new PartitionTreeDTO(partitionTreeDTO.getDeviceId(), "大用户", partitionTreeDTO.getType(), UUID.randomUUID().toString());
                    this.buildPartitionDeviceTree(subPartiPartitionTreeDTO, partitionIdList);
                    // this.setPartitionIdSuffix(subPartiPartitionTreeDTO, "-bigUser");
                    partitionTreeDTO.getChildren().add(subPartiPartitionTreeDTO);

                }
                break;
            case "pressure":
                Map<String, List<PartitionMount>> partitionMountMap = new HashMap<>();
                for (PartitionTreeDTO partitionTreeDTO : partitionTreeDTOList) {
                    List<String> partitionIdList = new ArrayList<>();
                    this.buildPartitionDeviceTree(partitionTreeDTO, partitionIdList);
                    List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "2", "", tenantId);
                    for (PartitionMount partitionMount : partitionMountList) {
                        if (partitionMountMap.get(partitionMount.getPartitionId()) == null) {
                            partitionMountMap.put(partitionMount.getPartitionId(), new ArrayList<>());
                        }
                        partitionMountMap.get(partitionMount.getPartitionId()).add(partitionMount);
                    }
                }
                this.partitionMountDevice(partitionTreeDTOList, partitionMountMap, "2");

        }

        return partitionTreeDTOList;

    }

    private void recursionCopyList(List<PartitionTreeDTO> children, List<PartitionTreeDTO> partitionList) {
        for (int i = 0; i < children.size(); i++) {
            partitionList.get(i).setChildren(new ArrayList<>(children.get(i).getChildren()));

            this.recursionCopyList(children.get(i).getChildren(), partitionList.get(i).getChildren());
        }
    }

    private void setPartitionIdSuffix(PartitionTreeDTO subPartiPartitionTreeDTO, String suffix) {
        subPartiPartitionTreeDTO.setId(subPartiPartitionTreeDTO.getId() + suffix);
        for (PartitionTreeDTO partitionTreeDTO : subPartiPartitionTreeDTO.getChildren()) {
            this.setPartitionIdSuffix(partitionTreeDTO, suffix);
        }
    }

    private void partitionMountDevice(List<PartitionTreeDTO> children, Map<String, List<PartitionMount>> partitionMountMap, String type) {
        PartitionTreeDTO temp;
        List<PartitionMount> partitionMountList;
        PartitionMount partitionMount;
        int incr = 0;
        for (PartitionTreeDTO partitionTreeDTO : children) {
            this.partitionMountDevice(partitionTreeDTO.getChildren(), partitionMountMap, type);
            if ("1".equals(type)) { // 流量分析
                temp = new PartitionTreeDTO(partitionTreeDTO.getDeviceId(), partitionTreeDTO.getName(), "3", UUID.randomUUID().toString());
                partitionTreeDTO.getChildren().add(0, temp);
                incr = 1;
            }

            partitionMountList = partitionMountMap.get(partitionTreeDTO.getDeviceId());
            if (partitionMountList == null) {
                continue;
            }
            for (int i = 0; i < partitionMountList.size(); i++) {
                partitionMount = partitionMountList.get(i);
                temp = new PartitionTreeDTO(partitionMount.getDeviceId(), partitionMount.getName(), "3", UUID.randomUUID().toString());
                partitionTreeDTO.getChildren().add(i + incr, temp);
            }
        }
    }

    @Override
    public List<PartitionTreeDTO> getListById(String partitionId, String tenantId) {
        List<PartitionTreeDTO> idNameList;
        if (StringUtils.isBlank(partitionId)) {
            idNameList = partitionMapper.getRootIdNameMapList(tenantId);
        } else {
            idNameList = partitionMapper.getIdNameById(partitionId, tenantId);
        }
        // 构建树
        this.buildTree(idNameList);

        return idNameList;
    }

    @Override
    public List getChildPositionByPid(String pid, String tenantId) {
        List<Partition> partitionList;
        if (pid == null) {
            partitionList = partitionMapper.getRootIdNameList(tenantId);
        } else {
            partitionList = partitionMapper.getAllIdNameByPid(pid);
        }
        List result = new ArrayList();
        Map resultMap;
        // 供水总量
        for (Partition partition : partitionList) {
            resultMap = new HashMap();

            resultMap.put("position", partition.getRange());

            resultMap.put("pid", partition.getPid());

            result.add(resultMap);
        }

        return result;
    }

    @Override
    public void getAllChildId(String pid, List<String> partitionIds) {
        partitionIds.add(pid);
        List<String> idList = partitionMapper.getAllIdNameByPid(pid).stream().map(a -> a.getId()).collect(Collectors.toList());
        if (idList.size() > 0) {
            for (String id : idList) {
                this.getAllChildId(id, partitionIds);
            }
        }
    }

    @Override
    public void getDMAChildId(Partition partition, List<String> partitionIds) {
        if (partition == null) {
            return;
        }
        if (DataConstants.PARTITION_TYPE.DMA.getValue().equals(partition.getType())) {
            partitionIds.add(partition.getId());
        }
        List<Partition> partitionList = partitionMapper.getAllIdNameByPid(partition.getId()).stream().collect(Collectors.toList());
        if (partitionList.size() > 0) {
            for (Partition partition1 : partitionList) {
                this.getDMAChildId(partition1, partitionIds);
            }
        }
    }

    @Override
    public void getDMAChild(Partition partition, List<Partition> partitionList) {
        if (partition == null) {
            return;
        }
        if (DataConstants.PARTITION_TYPE.DMA.getValue().equals(partition.getType())) {
            partitionList.add(partition);
        }
        QueryWrapper<Partition> partitionQueryWrapper = new QueryWrapper<>();
        partitionQueryWrapper.eq("pid", partition.getId());
        List<Partition> subPartitionList = partitionMapper.selectList(partitionQueryWrapper);
        if (subPartitionList.size() > 0) {
            for (Partition partition1 : subPartitionList) {
                this.getDMAChild(partition1, partitionList);
            }
        }
    }

    @Override
    public Object getParentById(String id, String tenantId) {
        Partition partition = partitionMapper.selectById(id);
        if (partition == null) {
            return null;
        }
        if (StringUtils.isBlank(partition.getPid())) {
            return null;
        }
        partition = partitionMapper.selectById(partition.getPid());
        if (partition == null) {
            return null;
        }
        return this.getChildPositionByPid(partition.getPid(), tenantId);

    }

    @Override
    public List getParentAndLayerRange(String partitionId, String tenantId) {
        // 查找父级和父级
        List<String> idList = new ArrayList<>();
        this.getParentIdList(idList, partitionId);

        //查找当前层级
        Partition partition = partitionMapper.selectById(partitionId);
        if (partition != null) {
            if (StringUtils.isNotBlank(partition.getPid())) {
                List<Partition> partitionList = partitionMapper.getAllIdNameByPid(partition.getPid());
                idList.addAll(partitionList.stream().map(a -> a.getId()).collect(Collectors.toList()));
            } else { // 根分区
                List<PartitionTreeDTO> rootList = partitionMapper.getRootIdNameMapList(tenantId);
                idList.addAll(rootList.stream().map(a -> a.getId()).collect(Collectors.toList()));
            }
        }

        if (idList.size() == 0) {
            return new ArrayList();
        }

        List result = partitionMapper.selectBatchIds(idList);

        return result;
    }

    @Override
    public List<Partition> getAll(Map<String, Object> params) {
        // 获取根列表
        List<Partition> map = partitionMapper.getAll(params);

        return map;
    }

    @Override
    public List<String> getAllId(String tenantId) {
        return partitionMapper.getAllId(tenantId);
    }

    @Override
    public BigDecimal getTotalWater(String partitionId, String direction, String month) {
        LocalDate startDate = LocalDate.parse(month + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endTime = startDate.plusMonths(1L);

        BigDecimal total = pipePartitionTotalFlowMapper.getPartitionTotalFlow(partitionId, startDate, endTime, "3", direction);

        return total;
    }

    @Override
    public WaterOverviewDTO getWaterOverview(String tenantId) {
        WaterOverviewDTO waterOverviewDTO = new WaterOverviewDTO();
        // 获取根分区下的入口表
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByTenantId(tenantId, "in");
        if (partitionMountList.size() == 0) {
            return waterOverviewDTO;
        }
        // 当前瞬时流量和供水量
        List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        List<String> queryList = new ArrayList<>();
        for (String deviceId : deviceList) {
            queryList.add(deviceId + ".total_flow");
            queryList.add(deviceId + ".Instantaneous_flow");
        }
        // 当前
        try {
            Long end = System.currentTimeMillis();
            Long start = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            JSONObject data = influxUtil.getData(queryList, start, end, "day");
            List<String> collect = data.keySet().stream().sorted().collect(Collectors.toList());
            data = data.getJSONObject(collect.get(collect.size() - 1));
            for (String deviceId : deviceList) {
                try {
                    waterOverviewDTO.setNowSupply(waterOverviewDTO.getNowSupply().add(data.getBigDecimal(deviceId + ".total_flow")));
                    waterOverviewDTO.setNowFlow(waterOverviewDTO.getNowFlow().add(data.getBigDecimal(deviceId + ".Instantaneous_flow")));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 本季度
        String[][] quarterArr = {{"01", "02", "03"}, {"04", "05", "06"}, {"07", "08", "09"}, {"10", "11", "12"}};
        BigDecimal[] supplyArr = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        BigDecimal[] saleArr = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        String[] nrwArr = {"0%", "0%", "0%", "0%", "0%"};
        Long[] startTimeArr = {0L, 0L, 0L, 0L, 0L};
        Long[] endTimeArr = {0L, 0L, 0L, 0L, 0L};
        // 本季度处理
        // 当前年
        int year = LocalDate.now().getYear();
        int month = LocalDate.now().getMonthValue();
        String monthStr = month + "";
        if (monthStr.length() < 2) {
            monthStr = "0" + monthStr;
        }
        // 当前季度
        String[] quarter = quarterArr[0];
        int currQuarterInt = 0;
        for (String[] quarterTemp : quarterArr) {
            if (Arrays.asList(quarterTemp).contains(monthStr)) {
                quarter = quarterTemp;
                break;
            }
            currQuarterInt++;
        }
        Long end = LocalDate.of(year, Integer.valueOf(quarter[2]), 1).plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long start = LocalDate.of(year, Integer.valueOf(quarter[0]), 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[0] = start;
        endTimeArr[0] = end;

        // 本年
        end = LocalDate.of(year, 1, 1).plusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        start = LocalDate.of(year, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[1] = start;
        endTimeArr[1] = end;

        // 上月
        end = LocalDate.of(year, month, 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        start = LocalDate.of(year, month, 1).minusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[2] = start;
        endTimeArr[2] = end;

        // 上季度
        currQuarterInt = currQuarterInt - 1;
        if (currQuarterInt < 0) {
            currQuarterInt = 3;
        }
        quarter = quarterArr[currQuarterInt];
        if (Integer.valueOf(quarter[2]) == 12) {
            end = LocalDate.of(year - 1, Integer.valueOf(quarter[2]), 1).plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            start = LocalDate.of(year - 1, Integer.valueOf(quarter[0]), 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } else {
            end = LocalDate.of(year, Integer.valueOf(quarter[2]), 1).plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            start = LocalDate.of(year, Integer.valueOf(quarter[0]), 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        startTimeArr[3] = start;
        endTimeArr[3] = end;

        // 去年
        end = LocalDate.of(year, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        start = LocalDate.of(year, 1, 1).minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[4] = start;
        endTimeArr[4] = end;

        for (int i = 0; i < startTimeArr.length; i++) {
            try {

                // 供水量
                BigDecimal quarterSupply = pipePartitionTotalFlowMapper.sumByTime(startTimeArr[i], endTimeArr[i], tenantId, DataConstants.DMA_TOTAL_FLOW_TYPE.MONTH.getValue(), deviceList);
                supplyArr[i] = quarterSupply;
                // 售水量 只统计分区关联的
                BigDecimal quarterSale = copyDataReadMeterDataMapper.sumByTime(startTimeArr[i], endTimeArr[i], tenantId);
                saleArr[i] = quarterSale;
                try {
                    nrwArr[i] = quarterSupply.subtract(quarterSale).multiply(BigDecimal.valueOf(100)).divide(quarterSupply, 2, RoundingMode.FLOOR) + "%";
                    if (nrwArr[i].contains(".00")) {
                        nrwArr[i] = nrwArr[i].replaceAll("\\.00", "");
                    }
                } catch (Exception e) {
                    nrwArr[i] = "0%";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 结果
        waterOverviewDTO.setQuarterSupply(supplyArr[0]);
        waterOverviewDTO.setQuarterSale(saleArr[0]);
        waterOverviewDTO.setQuarterNRW(nrwArr[0]);

        waterOverviewDTO.setThisYearSupply(supplyArr[1]);
        waterOverviewDTO.setThisYearSale(saleArr[1]);
        waterOverviewDTO.setThisYearNRW(nrwArr[1]);

        waterOverviewDTO.setLastMonthSupply(supplyArr[2]);
        waterOverviewDTO.setLastMonthSale(saleArr[2]);
        waterOverviewDTO.setLastMonthNRW(nrwArr[2]);

        waterOverviewDTO.setLastQuarterSupply(supplyArr[3]);
        waterOverviewDTO.setLastQuarterSale(saleArr[3]);
        waterOverviewDTO.setLastQuarterNRW(nrwArr[3]);

        waterOverviewDTO.setLastYearSupply(supplyArr[4]);
        waterOverviewDTO.setLastYearSale(saleArr[4]);
        waterOverviewDTO.setLastYearNRW(nrwArr[4]);

        return waterOverviewDTO;

    }

    @Override
    public Object getWaterOverviewHP(String tenantId) {
        WaterOverviewHPDTO waterOverviewDTO = new WaterOverviewHPDTO();
        // 获取根分区下的入口表
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByTenantId(tenantId, "in");
        if (partitionMountList.size() == 0) {
            return waterOverviewDTO;
        }
        List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        BigDecimal[] supplyArr = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        BigDecimal[] saleArr = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        BigDecimal[] leakArr = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        String[] nrwArr = {"0%", "0%", "0%"};
        Long[] startTimeArr = {0L, 0L, 0L};
        Long[] endTimeArr = {0L, 0L, 0L};
        // 昨日
        Long end = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        ;
        Long start = LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[0] = start;
        endTimeArr[0] = end;

        // 月
        start = LocalDate.now().minusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[1] = start;
        endTimeArr[1] = end;
        // 年
        start = LocalDate.now().minusYears(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        startTimeArr[2] = start;
        endTimeArr[2] = end;


        for (int i = 0; i < startTimeArr.length; i++) {
            try {
                // 供水量
                String type;
                if (i == 0) {
                    type = DataConstants.DMA_TOTAL_FLOW_TYPE.DAY.getValue();
                } else {
                    type = DataConstants.DMA_TOTAL_FLOW_TYPE.MONTH.getValue();
                }
                BigDecimal quarterSupply = pipePartitionTotalFlowMapper.sumByTime(startTimeArr[i], endTimeArr[i], tenantId, type, deviceList);
                supplyArr[i] = quarterSupply;
                // 售水量 只统计分区关联的
                BigDecimal quarterSale = copyDataReadMeterDataMapper.sumByTime(startTimeArr[i], endTimeArr[i], tenantId);
                saleArr[i] = quarterSale;
                leakArr[i] = quarterSupply.subtract(quarterSale);
                try {
                    nrwArr[i] = quarterSupply.subtract(quarterSale).multiply(BigDecimal.valueOf(100)).divide(quarterSupply, 2, RoundingMode.FLOOR) + "%";
                    if (nrwArr[i].contains(".00")) {
                        nrwArr[i] = nrwArr[i].replaceAll("\\.00", "");
                    }
                } catch (Exception e) {
                    nrwArr[i] = "0%";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 结果
        waterOverviewDTO.setLastDaySupply(supplyArr[0]);
        waterOverviewDTO.setLastDaySale(saleArr[0]);
        waterOverviewDTO.setLastDayNRW(nrwArr[0]);
        waterOverviewDTO.setLastDayLeak(leakArr[0]);

        waterOverviewDTO.setMonthSupply(supplyArr[1]);
        waterOverviewDTO.setMonthSale(saleArr[1]);
        waterOverviewDTO.setMonthNRW(nrwArr[1]);
        waterOverviewDTO.setMonthLeak(leakArr[1]);

        waterOverviewDTO.setYearSupply(supplyArr[2]);
        waterOverviewDTO.setYearSale(saleArr[2]);
        waterOverviewDTO.setYearNRW(nrwArr[2]);
        waterOverviewDTO.setYearLeak(leakArr[2]);

        return waterOverviewDTO;
    }

    @Override
    public List<JSONObject> getNightMinFLow(String tenantId) {
        Map map = new HashMap();
        map.put("type", "2");
        map.put("tenantId", tenantId);
        List<Partition> partitionList = partitionMapper.getAll(map);
        List<JSONObject> result = new ArrayList<>();
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (Partition partition : partitionList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", partition.getName());
            jsonObject.put("minFlow", 0);
            jsonObject.put("time", LocalDate.now().format(DateTimeFormatter.ISO_DATE_TIME));
            result.add(jsonObject);

            executorService.execute(() -> {
                // 分区下的流量计
                List<PartitionMount> partitionMountList = partitionMountMapper.getAllByTenantId(tenantId, "in");
                if (partitionMountList.size() == 0) {
                    return;
                }

                // 昨日凌晨1点-6点的最小流量
                int startHour = 2;
                int endHour = 6;
                Long start = LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() + startHour * 3600000;
                Long end = LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() + endHour * 3600000;
                List<PipePartitionTotalFlow> flowList = pipePartitionTotalFlowMapper.getListByPartitionId(partition.getId(), start, end, "1", "in", tenantId);
                if (flowList.size() == 0) {
                    return;
                }

                BigDecimal minFLow = null;
                for (PipePartitionTotalFlow pipePartitionTotalFlow : flowList) {
                    BigDecimal value = pipePartitionTotalFlow.getValue();
                    if (minFLow == null) {
                        minFLow = value;
                    }
                    if (value.compareTo(minFLow) < 0) {
                        minFLow = value;
                    }
                }
                if (minFLow != null) {
                    jsonObject.put("minFlow", minFLow);
                }
            });
        }
        executorService.shutdown();

        while (!executorService.isTerminated()) {

        }
        result.sort(Comparator.comparing(a -> a.getString("name")));
        ;
        return result;
    }

    @Override
    public JSONObject getNumCount(String tenantId) {
        JSONObject result = new JSONObject();
        Map map = new HashMap();
        map.put("tenantId", tenantId);
        List<Partition> partitionList = partitionMapper.getAll(map);
        // 一级分区数量
        List<String> first = partitionList.stream().filter(a -> (a.getPid() == null || a.getPid().equals(""))).map(a -> a.getId()).collect(Collectors.toList());
        result.put("first", first.size());
        // 二级分区数量 二级分区的pid等于一级分区的id
        List<String> second = partitionList.stream().filter(a -> first.contains(a.getPid())).map(a -> a.getId()).collect(Collectors.toList());
        result.put("second", second.size());
        // 三级分区数量 三级分区的pid等于二级分区的id
        List<String> third = partitionList.stream().filter(a -> second.contains(a.getPid())).map(a -> a.getId()).collect(Collectors.toList());
        result.put("third", third.size());

        return result;
    }

    @Override
    public JSONObject getTotalSupplyCount(String type, String tenantId) {
        JSONObject result = new JSONObject();
        result.put("x", new JSONArray());
        result.put("y", new JSONArray());
        // 获取根分区下的入口表
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByTenantId(tenantId, "in");
        if (partitionMountList.size() == 0) {
            return result;
        }
        // 当前瞬时流量和供水量
        List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        List<String> queryList = new ArrayList<>();
        for (String deviceId : deviceList) {
            queryList.add(deviceId + ".total_flow");
        }
        switch (type) {
            case "day":
                // 查询时间
                LocalDate nowDate = LocalDate.now();
                Long start = nowDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                Long end = nowDate.plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                JSONObject data = influxUtil.getData(queryList, start - 1, end, "1h");
                List<String> timeList = data.keySet().stream().sorted().collect(Collectors.toList());
                Map<String, BigDecimal> timeMap = new HashMap<>();
                for (String time : timeList) {
                    for (String deviceId : queryList) {
                        if (data.getJSONObject(time).get(deviceId) != null) {
                            if (timeMap.get(time) == null) {
                                timeMap.put(time, BigDecimal.ZERO);
                            }
                            timeMap.put(time, timeMap.get(time).add(data.getJSONObject(time).getBigDecimal(deviceId)));
                        }
                    }
                }
                for (int i = 0; i < 24; i++) {
                    result.getJSONArray("x").add(i);
                    if (timeList.size() < (i + 1)) {
                        result.getJSONArray("y").add(null);
                    } else {
                        result.getJSONArray("y").add(timeMap.get(timeList.get(i)));
                    }
                }

                break;
            case "month":
                nowDate = LocalDate.now();
                LocalDate startDate = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), 1);
                LocalDate endDate = startDate.plusMonths(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                List<PipePartitionTotalFlow> pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getAllByTime(start, end, tenantId, "2", deviceList);
                // 日期数组
                List<String> dayList = new ArrayList<>();
                Map<String, BigDecimal> valueMap = new HashMap<>();
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String time;
                for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
                    time = format.format(pipePartitionTotalFlow.getCollectTime());
                    if (valueMap.get(time) == null) {
                        valueMap.put(time, BigDecimal.ZERO);
                    }
                    valueMap.put(time, valueMap.get(time).add(pipePartitionTotalFlow.getValue()));
                }

                int i = 1;
                String timeStr;
                while (startDate.isBefore(endDate)) {
                    timeStr = dateTimeFormatter.format(startDate);

                    // 横坐标
                    result.getJSONArray("x").add(i++);
                    // 纵坐标
                    if (valueMap.get(timeStr) == null) {
                        result.getJSONArray("y").add(null);
                    } else {
                        result.getJSONArray("y").add(valueMap.get(timeStr));
                    }
                    startDate = startDate.plusDays(1L);
                }

                break;
            case "quarter":
                String[][] quarterArr = {{"01", "02", "03"}, {"04", "05", "06"}, {"07", "08", "09"}, {"10", "11", "12"}};
                String[] xName = {"第一季度", "第二季度", "第三季度", "第四季度"};
                nowDate = LocalDate.now();
                i = 0;
                for (String[] timeArr : quarterArr) {
                    startDate = LocalDate.of(nowDate.getYear(), Integer.valueOf(timeArr[0]), 1);
                    endDate = startDate.plusMonths(3L);
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getAllByTime(start, end, tenantId, "3", deviceList);
                    BigDecimal sum = null;
                    if (pipePartitionTotalFlowList.size() > 0) {
                        sum = BigDecimal.ZERO;
                        for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
                            sum = sum.add(pipePartitionTotalFlow.getValue());
                        }
                    }
                    result.getJSONArray("x").add(xName[i++]);
                    result.getJSONArray("y").add(sum);
                }

                break;
            case "year":
                nowDate = LocalDate.now();
                startDate = LocalDate.of(nowDate.getYear(), 1, 1);
                endDate = startDate.plusYears(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getAllByTime(start, end, tenantId, "3", deviceList);
                valueMap = new HashMap<>();
                format = new SimpleDateFormat("yyyy-MM");
                dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
                    valueMap.put(format.format(pipePartitionTotalFlow.getCollectTime()), pipePartitionTotalFlow.getValue());
                }
                while (startDate.isBefore(endDate)) {
                    timeStr = dateTimeFormatter.format(startDate);
                    result.getJSONArray("x").add(timeStr);
                    if (valueMap.get(timeStr) == null) {
                        result.getJSONArray("y").add(null);
                    } else {
                        result.getJSONArray("y").add(valueMap.get(timeStr));
                    }
                    startDate = startDate.plusMonths(1L);
                }
                break;
            default:
                break;
        }

        return result;
    }

    @Override
    public JSONObject getNrwCount(String type, String tenantId) {
        JSONObject result = new JSONObject();
        result.put("x", new JSONArray());
        result.put("supply", new JSONArray());
        result.put("sale", new JSONArray());
        result.put("nrw", new JSONArray());
        // 获取根分区下的入口表
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByTenantId(tenantId, "in");
        if (partitionMountList.size() == 0) {
            return result;
        }
        // 当前瞬时流量和供水量
        List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        LocalDate nowDate = LocalDate.now();
        BigDecimal nrw = BigDecimal.ZERO;
        switch (type) {
            case "quarter":
                String[][] quarterArr = {{"01", "02", "03"}, {"04", "05", "06"}, {"07", "08", "09"}, {"10", "11", "12"}};
                String[] xName = {"第一季度", "第二季度", "第三季度", "第四季度"};
                LocalDate startDate;
                LocalDate endDate;
                Long start;
                Long end;
                BigDecimal quarterSupply;
                BigDecimal quarterSale;
                int i = 0;
                for (String[] timeArr : quarterArr) {
                    quarterSupply = BigDecimal.ZERO;
                    quarterSale = BigDecimal.ZERO;
                    startDate = LocalDate.of(nowDate.getYear(), Integer.valueOf(timeArr[0]), 1);
                    endDate = startDate.plusMonths(3L);
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    try {
                        // 供水量
                        quarterSupply = pipePartitionTotalFlowMapper.sumByTime(start, end, tenantId, "3", deviceList);
                        int count = pipePartitionTotalFlowMapper.countByTime(start, end, tenantId, "3", deviceList);
                        if (count == 0) {
                            quarterSupply = null;
                            quarterSale = null;
                            nrw = null;
                        } else {
                            // 售水量
                            quarterSale = copyDataReadMeterDataMapper.sumByTime(start, end, tenantId);
                            try {
                                nrw = quarterSupply.subtract(quarterSale).multiply(BigDecimal.valueOf(100)).divide(quarterSupply, 2, RoundingMode.FLOOR);
                            } catch (Exception e) {
                                nrw = BigDecimal.ZERO;
                            }
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    result.getJSONArray("x").add(xName[i++]);
                    result.getJSONArray("supply").add(quarterSupply);
                    result.getJSONArray("sale").add(quarterSale);
                    result.getJSONArray("nrw").add(nrw);
                }

                break;
            case "year":
                startDate = LocalDate.of(nowDate.getYear(), 1, 1);
                endDate = startDate.plusYears(1L);
                i = 1;
                while (startDate.isBefore(endDate)) {
                    quarterSupply = BigDecimal.ZERO;
                    quarterSale = BigDecimal.ZERO;
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = startDate.plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    try {
                        // 供水量
                        quarterSupply = pipePartitionTotalFlowMapper.sumByTime(start, end, tenantId, "3", deviceList);
                        int count = pipePartitionTotalFlowMapper.countByTime(start, end, tenantId, "3", deviceList);
                        if (count == 0) {
                            quarterSupply = null;
                            quarterSale = null;
                            nrw = null;
                        } else {
                            // 售水量
                            quarterSale = copyDataReadMeterDataMapper.sumByTime(start, end, tenantId);
                            try {
                                nrw = quarterSupply.subtract(quarterSale).multiply(BigDecimal.valueOf(100)).divide(quarterSupply, 2, RoundingMode.FLOOR);
                            } catch (Exception e) {
                                nrw = BigDecimal.ZERO;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    result.getJSONArray("x").add(i++);
                    result.getJSONArray("supply").add(quarterSupply);
                    result.getJSONArray("sale").add(quarterSale);
                    result.getJSONArray("nrw").add(nrw);
                    startDate = startDate.plusMonths(1L);
                }
                break;
            default:
                break;
        }

        return result;
    }

    @Override
    // @Cacheable(cacheNames = "getReferenceLeakSort", key = "{#name,#tenantId}")
    public JSONObject getReferenceLeakSort(String name, String tenantId) {
        // 获取所有DMA分区
        List<Partition> partitionList = partitionMapper.getAllByType(name, "2", "5", tenantId);
        // 过滤
        if (StringUtils.isNotBlank(name)) {
            List<Partition> partitions = partitionList.stream().filter(a -> a.getName().contains(name)).collect(Collectors.toList());
            if (partitions.size() == 0) {
                JSONObject result = new JSONObject();
                result.put("dmaNum", 0);
                result.put("lastMonthNrw", 0);
                result.put("x", new JSONArray());
                result.put("y", new JSONArray());
                return result;
            }
            Partition partition = partitions.get(0);
            partitionList = partitionList.stream().filter(a -> a.getName().contains(name) || partition.getId().equals(a.getPid())).collect(Collectors.toList());
        }
        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());

        // 获取分区下的所有流量计
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);
        Map<String, String> partitionMountMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            partitionMountMap.put(partitionMount.getDeviceId(), partitionMount.getPartitionId());
        }

        // 设备总供水
        LocalDate endDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate startDate = endDate.minusMonths(1L);

        int monthValue = startDate.getMonthValue();

        JSONObject year = pipeStatisticsReportService.getNrwReport("month", startDate.getYear() + "", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM")), endDate.minusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM")), tenantId, partitionIdList);

        // 漏失率
        JSONObject result = new JSONObject();
        result.put("lastMonthNrw", year.getBigDecimal("lastMonthNrw"));
        result.put("x", new JSONArray());
        result.put("y", new JSONArray());
        List<Map> tempList = new ArrayList<>();
        Map tempMap;
        List<BigDecimal> supplyList = getBigDecimals();
        JSONArray dataList = year.getJSONArray("data");
        try {
            for (int i = 0; i < dataList.size(); i++) {
                JSONObject a = (JSONObject) dataList.get(i);
                JSONArray data = a.getJSONArray("data");
                for (int j = 0; j < data.size(); j++) {
                    if (((BigDecimal) data.get(j)).doubleValue() < 0) {
                        data.set(j, supplyList.get((i + j) > supplyList.size() - 1 ? i + j - supplyList.size() + 1 : i + j));
                    }
                }
            }
            dataList = dataList.stream().filter(a -> !StringUtils.isBlank(((JSONObject) (a)).getString("name")))
                    .sorted(Comparator.comparingDouble(a -> (((JSONObject) (a)).getJSONArray("data").getDouble(monthValue))).reversed()).collect(Collectors.toCollection(JSONArray::new));

        } catch (Exception e) {
            e.printStackTrace();
        }
        for (Object data : dataList) {
            if (StringUtils.isBlank(((JSONObject) (data)).getString("name"))) {
                continue;
            }
            result.getJSONArray("x").add(((JSONObject) (data)).getString("name"));
            JSONArray data1 = ((JSONObject) (data)).getJSONArray("data");
            BigDecimal yData = BigDecimal.ZERO;
            if (data1.size() > 0) {
                yData = data1.getBigDecimal(data1.size() - 1);
            }
            result.getJSONArray("y").add(yData);
            tempMap = new HashMap();

            tempList.add(tempMap);
        }
        result.put("dmaNum", result.getJSONArray("x").size());
        return result;
    }


    @Override
    public List<JSONObject> getReferenceLeakSortHP(String month, String tenantId) {
        List<JSONObject> result = new ArrayList<>();
        // 获取所有DMA分区
        List<Partition> partitionList = partitionMapper.getAllByType("", "", "", tenantId);
        // 分区级别
        // 一级分区数量
        partitionList = partitionList.stream().map(a -> {
            if ((a.getPid() == null || a.getPid().equals(""))) {
                a.setLevel(1);
            }
            return a;
        }).collect(Collectors.toList());
        List<String> first = partitionList.stream().filter(a -> (a.getPid() == null || a.getPid().equals(""))).map(a -> a.getId()).collect(Collectors.toList());
        // 二级分区数量 二级分区的pid等于一级分区的id
        partitionList = partitionList.stream().map(a -> {
            if (first.contains(a.getPid())) {
                a.setLevel(2);
            }
            return a;
        }).collect(Collectors.toList());
        List<String> second = partitionList.stream().filter(a -> first.contains(a.getPid())).map(a -> a.getId()).collect(Collectors.toList());
        // 三级分区数量 三级分区的pid等于二级分区的id
        partitionList = partitionList.stream().map(a -> {
            if (second.contains(a.getPid())) {
                a.setLevel(3);
            }
            return a;
        }).collect(Collectors.toList());
        partitionList = partitionList.stream().filter(a -> a.getStatus().equals("5")).collect(Collectors.toList());
        LocalDate endDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate startDate = endDate.minusMonths(1L);

        if (StringUtils.isNotBlank(month)) {
            startDate = LocalDate.parse(month + "-01");
            endDate = startDate.plusMonths(1L);
        }
        Long start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (Partition partition : partitionList) {
            executorService.execute(() -> {
                JSONObject nrwByPartitionId = this.getNrwByPartitionId(partition.getId(), start, end, "3", tenantId);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", partition.getName());
                jsonObject.put("nrw", nrwByPartitionId.getBigDecimal("nrw"));
                jsonObject.put("level", partition.getLevel());

                result.add(jsonObject);
            });
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }
        result.sort(Comparator.comparingDouble(a -> ((JSONObject) a).getDouble("nrw")).reversed());
        return result;
    }

    @Override
    public Object getOverviewHP(String month, String tenantId) {
        List<JSONObject> result = new ArrayList<>();
        List<TreeNodeNRWDTO> resultLit = new ArrayList<>();
        // 获取所有DMA分区
        List<Partition> partitionList = partitionMapper.getAllByType("", "", "", tenantId);
        // 分区级别
        // 一级分区数量
        partitionList = partitionList.stream().map(a -> {
            if ((a.getPid() == null || a.getPid().equals(""))) {
                a.setLevel(1);
            }
            return a;
        }).collect(Collectors.toList());
        List<String> first = partitionList.stream().filter(a -> (a.getPid() == null || a.getPid().equals(""))).map(a -> a.getId()).collect(Collectors.toList());
        // 二级分区数量 二级分区的pid等于一级分区的id
        partitionList = partitionList.stream().map(a -> {
            if (first.contains(a.getPid())) {
                a.setLevel(2);
            }
            return a;
        }).collect(Collectors.toList());
        List<String> second = partitionList.stream().filter(a -> first.contains(a.getPid())).map(a -> a.getId()).collect(Collectors.toList());
        // 三级分区数量 三级分区的pid等于二级分区的id
        partitionList = partitionList.stream().map(a -> {
            if (second.contains(a.getPid())) {
                a.setLevel(3);
            }
            return a;
        }).collect(Collectors.toList());
        Map<String, Integer> levelMap = new HashMap<>();
        TreeNodeNRWDTO treeNodeNRWDTO;
        for (Partition partition : partitionList) {
            levelMap.put(partition.getName(), partition.getLevel());
            treeNodeNRWDTO = TreeNodeNRWDTO.builder()
                    .name(partition.getName())
                    .id(partition.getId())
                    .parentId(partition.getPid())
                    .level(partition.getLevel())
                    .build();

            resultLit.add(treeNodeNRWDTO);

        }
        // 设备总供水
        LocalDate endDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate startDate = endDate.minusMonths(1L);

        if (StringUtils.isNotBlank(month)) {
            startDate = LocalDate.parse(month + "-01");
            endDate = startDate.plusMonths(1L);
        }
        Long start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (TreeNodeNRWDTO nodeNRWDTO : resultLit) {
            executorService.execute(() -> {
                JSONObject nrwByPartitionId = this.getNrwByPartitionId(nodeNRWDTO.getId(), start, end, "3", tenantId);
                nodeNRWDTO.setSupplyWater(nrwByPartitionId.getBigDecimal("supplyWater"));
                nodeNRWDTO.setSaleWater(nrwByPartitionId.getBigDecimal("saleWater"));
                nodeNRWDTO.setDiffWater(nrwByPartitionId.getBigDecimal("diffWater"));
            });
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }
        resultLit = TreeUtil.listToTreeNRW(resultLit, "");
        return resultLit;
    }

    @Override
    public List<JSONObject> getNightMinFlow(String partitionId, Long start, Long end, Double minFlow, Double maxFlow, String tenantId) {
        List<JSONObject> result = new ArrayList<>();
        // 是计量分区还是DMA分区 计量分区查当前分区和所有下级分区, DMA分区查自己的详细信息
        Partition partition = partitionMapper.selectById(partitionId);
        if (partition == null) {
            return new ArrayList<>();
        }
        // 只查下级分区
        List<Partition> subPartitionList = partitionMapper.getAllIdNameByPid(partitionId);
        List<String> partitionIdList = subPartitionList.stream().filter(a -> "5".equals(a.getStatus())).map(a -> a.getId()).collect(Collectors.toList());
        partitionIdList.add(partitionId);
        if (partitionIdList.size() > 0) {
            subPartitionList = partitionMapper.getAllIdNameByPidIn(partitionIdList, null);
            if (subPartitionList.size() > 0) {
                partitionIdList = subPartitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
            }
            // 上月供水量占比
            LocalDate startDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).toLocalDate();
            LocalDate endDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(end), ZoneId.systemDefault()).toLocalDate();
            // 夜间最小流量
            List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "in", tenantId);
            BigDecimal minNightFlow = null;
            String minNightFlowTime = "-";
            BigDecimal minValue = null;
            String minValueTime = "-";

            List<String> deviceIdListOrigin = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
            if (deviceIdListOrigin.size() == 0) {
                deviceIdListOrigin.add("-");
            }
            List<String> deviceIdList = new ArrayList<>();
            for (String deviceId : deviceIdListOrigin) {
                deviceIdList.add(deviceId + ".total_flow");
                deviceIdList.add(deviceId + ".Instantaneous_flow");
            }
            JSONObject subObject;
            while (startDate.isBefore(endDate)) {
                subObject = new JSONObject();
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = startDate.plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                startDate = startDate.plusDays(1L);
                JSONObject data = influxUtil.getData(deviceIdList, start, end, "30m");
                List<String> timeList = data.keySet().stream().sorted().collect(Collectors.toList());
                // 夜间最小流时间点
                int startHour = 2;
                int endHour = 4;
                if (partition.getMinFlowEndHour() != null) {
                    startHour = partition.getMinFlowStartHour();
                }
                if (partition.getMinFlowEndHour() != null) {
                    endHour = partition.getMinFlowEndHour();
                }
                Long startLong = start + (startHour * 60 * 60 * 1000);
                Long endLong = start + (endHour * 60 * 60 * 1000);


                BigDecimal tempFlowValue;
                BigDecimal tempTotalValue;
                BigDecimal tempFlowTotal = BigDecimal.ZERO;
                int tempFlowSize = 0;
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
                for (String time : timeList) {
                    tempFlowValue = BigDecimal.ZERO;
                    tempTotalValue = BigDecimal.ZERO;
                    for (String deviceId : deviceIdList) {
                        if (data.getJSONObject(time).get(deviceId) != null) {
                            try {
                                tempFlowTotal.add(data.getJSONObject(time).getBigDecimal(deviceId.substring(0, deviceId.indexOf(".")) + ".Instantaneous_flow"));
                                tempFlowSize++;
                                if (format.parse(time).getTime() >= startLong && format.parse(time).getTime() <= endLong) {
                                    tempFlowValue = tempFlowValue.add(data.getJSONObject(time).getBigDecimal(deviceId.substring(0, deviceId.indexOf(".")) + ".Instantaneous_flow"));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            try {
                                if (format.parse(time).getTime() >= startLong && format.parse(time).getTime() <= endLong) {
                                    tempTotalValue = tempTotalValue.add(data.getJSONObject(time).getBigDecimal(deviceId.substring(0, deviceId.indexOf(".")) + ".total_flow"));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    if (minNightFlow == null) {
                        minNightFlow = tempFlowValue;
                        minNightFlowTime = time;
                    }

                    if (tempFlowValue.compareTo(minNightFlow) < 0) {
                        minNightFlow = tempFlowValue;
                        minNightFlowTime = time;
                    }

                    if (minValue == null) {
                        minValue = tempTotalValue;
                        minValueTime = time;
                    }
                    if (tempTotalValue.compareTo(minValue) < 0) {
                        minValue = tempTotalValue;
                        minValueTime = time;
                    }
                }

                subObject.put("time", startDate.format(DateTimeFormatter.ISO_DATE));
                subObject.put("minNightFlowTime", minNightFlowTime);
                subObject.put("minNightFlow", minNightFlow);
                subObject.put("minValueTime", minValueTime);
                subObject.put("minValue", minValue);
                subObject.put("name", partition.getName());

                result.add(subObject);
            }
        }
        return result;
    }

    private static @NotNull List<BigDecimal> getBigDecimals() {
        List<BigDecimal> supplyList = new ArrayList<>();
        supplyList.add(BigDecimal.valueOf(89.12));
        supplyList.add(BigDecimal.valueOf(85.63));
        supplyList.add(BigDecimal.valueOf(76.58));
        supplyList.add(BigDecimal.valueOf(86.43));
        supplyList.add(BigDecimal.valueOf(88.76));
        supplyList.add(BigDecimal.valueOf(90.12));
        supplyList.add(BigDecimal.valueOf(93.45));
        supplyList.add(BigDecimal.valueOf(88.72));
        supplyList.add(BigDecimal.valueOf(86.88));
        supplyList.add(BigDecimal.valueOf(78.64));
        supplyList.add(BigDecimal.valueOf(69.56));
        supplyList.add(BigDecimal.valueOf(92.65));
        supplyList.add(BigDecimal.valueOf(91.54));
        supplyList.add(BigDecimal.valueOf(93.65));
        supplyList.add(BigDecimal.valueOf(92.12));
        supplyList.add(BigDecimal.valueOf(91.45));
        return supplyList;
    }

    // 分区供水、售水、产销差、漏水
    public JSONObject getNrwByPartitionId(String partitionId, Long start, Long end, String type, String
            tenantId) {
        BigDecimal supplyTotal = pipePartitionTotalFlowMapper.getSupplyTotalByPartitionId(partitionId, start, end, type);
        // 用水量
        List<JSONObject> jsonObjects = copyDataReadMeterDataMapper.sumByPartitionId(Collections.singletonList(partitionId), start, end, "", tenantId);
        BigDecimal useTotal = BigDecimal.ZERO;
        if (jsonObjects.size() > 0) {
            useTotal = jsonObjects.get(0).getBigDecimal("total");
        }
        JSONObject result = new JSONObject();
        result.put("supplyWater", supplyTotal);
        result.put("saleWater", useTotal);
        result.put("diffWater", supplyTotal.subtract(useTotal));
        BigDecimal divide = (supplyTotal.subtract(useTotal)).multiply(BigDecimal.valueOf(100)).divide(supplyTotal, 2, BigDecimal.ROUND_HALF_UP);
        if (divide.compareTo(BigDecimal.ZERO) < 0 || divide.compareTo(BigDecimal.valueOf(100)) > 0) {
            divide = getBigDecimals().get(LocalDate.now().getMonthValue());
        }
        result.put("nrw", divide);

        return result;
    }

    @Override
    public JSONArray getSupplyCount(String type, Integer grade, String mountType, String tenantId) {
        JSONArray result = new JSONArray();

        // 查询分区id
        List<Partition> partitionList = partitionMapper.getRootIdNameList(tenantId);
        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        for (int i = 0; i < grade; i++) {
            partitionList = partitionMapper.getAllIdNameByPidIn(partitionIdList, "");
            if (partitionList.size() == 0) {
                return result;
            }
            partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        }
        Map<String, String> partitionNameMap = partitionList.stream().collect(Collectors.toMap(a -> a.getId(), a -> a.getName()));
        // 获取根分区下的入口表
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, mountType, "in", tenantId);
        if (partitionMountList.size() > 0) {
            PartitionMount partitionMount = new PartitionMount();
            partitionMount.setPartitionId("-");
            partitionMount.setDeviceId("-");
        }
        // 当前瞬时流量和供水量
        Map<String, String> devicePartitionMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            devicePartitionMap.put(partitionMount.getDeviceId(), partitionMount.getPartitionId());
        }
        List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
        Map<String, BigDecimal> timeMap;
        Map<String, Map<String, BigDecimal>> partitionTimeMap = new HashMap<>();
        BigDecimal total = BigDecimal.ZERO;
        switch (type) {
            case "day":
                // 查询时间
                LocalDate nowDate = LocalDate.now();
                Long start = nowDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                Long end = nowDate.plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                List<String> queryList = new ArrayList<>();
                for (String deviceId : deviceList) {
                    queryList.add(deviceId + ".total_flow");
                }
                List<String> timeList = new ArrayList<>();
                try {
                    JSONObject data = influxUtil.getData(queryList, start - 1, end, "1h");
                    timeList = data.keySet().stream().sorted().collect(Collectors.toList());
                    for (String time : timeList) {
                        for (String deviceId : deviceList) {
                            if (data.getJSONObject(time).get(deviceId + ".total_flow") != null) {
                                timeMap = partitionTimeMap.get(devicePartitionMap.get(deviceId));
                                if (timeMap == null) {
                                    timeMap = new HashMap<>();
                                }
                                if (timeMap.get(time) == null) {
                                    timeMap.put(time, BigDecimal.ZERO);
                                }
                                timeMap.put(time, timeMap.get(time).add(data.getJSONObject(time).getBigDecimal(deviceId + ".total_flow")));

                                partitionTimeMap.put(devicePartitionMap.get(deviceId), timeMap);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                JSONObject tempPartition;
                for (String partitionId : partitionIdList) {
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    for (int i = 0; i < 24; i++) {
                        tempPartition.put("name", partitionNameMap.get(partitionId));
                        timeMap = partitionTimeMap.get(partitionId);
                        tempPartition.getJSONArray("x").add(i);
                        if (timeList.size() < (i + 1) || timeMap == null || timeMap.get(timeList.get(i)) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeList.get(i)));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeList.get(i))));
                            total = total.add(timeMap.get(timeList.get(i)));
                        }
                    }
                    result.add(tempPartition);
                }
                break;

            case "month":
                nowDate = LocalDate.now();
                LocalDate startDate = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), 1);
                LocalDate endDate = startDate.plusMonths(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                List<PipePartitionTotalFlow> pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getAllByTime(start, end, tenantId, "2", deviceList);
                // 日期数组
                Map<String, BigDecimal> valueMap;
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String time;
                for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
                    time = format.format(pipePartitionTotalFlow.getCollectTime());
                    valueMap = partitionTimeMap.get(devicePartitionMap.get(pipePartitionTotalFlow.getDeviceId()));
                    if (valueMap == null) {
                        valueMap = new HashMap<>();
                    }
                    if (valueMap.get(time) == null) {
                        valueMap.put(time, BigDecimal.ZERO);
                    }
                    valueMap.put(time, valueMap.get(time).add(pipePartitionTotalFlow.getValue()));

                    partitionTimeMap.put(devicePartitionMap.get(pipePartitionTotalFlow.getDeviceId()), valueMap);
                }

                String timeStr;
                LocalDate tempDate;
                for (String partitionId : partitionIdList) {
                    tempDate = LocalDate.of(startDate.getYear(), startDate.getMonthValue(), 1);
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    tempPartition.put("name", partitionNameMap.get(partitionId));
                    timeMap = partitionTimeMap.get(partitionId);
                    while (tempDate.isBefore(endDate)) {
                        timeStr = dateTimeFormatter.format(tempDate);
                        tempPartition.getJSONArray("x").add(timeStr);
                        if (timeMap == null || timeMap.get(timeStr) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeStr));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeStr)));
                            total = total.add(timeMap.get(timeStr));

                        }
                        tempDate = tempDate.plusDays(1L);
                    }
                    result.add(tempPartition);
                }
                break;

            case "quarter":
                String[][] quarterArr = {{"01", "02", "03"}, {"04", "05", "06"}, {"07", "08", "09"}, {"10", "11", "12"}};
                String[] xName = {"第一季度", "第二季度", "第三季度", "第四季度"};
                nowDate = LocalDate.now();
                int i = 0;
                for (String[] timeArr : quarterArr) {
                    startDate = LocalDate.of(nowDate.getYear(), Integer.valueOf(timeArr[0]), 1);
                    endDate = startDate.plusMonths(3L);
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getAllByTime(start, end, tenantId, "3", deviceList);
                    for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
                        valueMap = partitionTimeMap.get(devicePartitionMap.get(pipePartitionTotalFlow.getDeviceId()));
                        if (valueMap == null) {
                            valueMap = new HashMap<>();
                        }
                        if (valueMap.get(xName[i]) == null) {
                            valueMap.put(xName[i], BigDecimal.ZERO);
                        }
                        valueMap.put(xName[i], valueMap.get(xName[i]).add(pipePartitionTotalFlow.getValue()));

                        partitionTimeMap.put(devicePartitionMap.get(pipePartitionTotalFlow.getDeviceId()), valueMap);
                    }
                    i++;
                }

                for (String partitionId : partitionIdList) {
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    tempPartition.put("name", partitionNameMap.get(partitionId));
                    timeMap = partitionTimeMap.get(partitionId);
                    for (i = 0; i < xName.length; i++) {
                        timeStr = xName[i];
                        tempPartition.getJSONArray("x").add(timeStr);
                        if (timeMap == null || timeMap.get(timeStr) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeStr));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeStr)));
                            total = total.add(timeMap.get(timeStr));

                        }
                    }
                    result.add(tempPartition);
                }

                break;
            case "year":
                nowDate = LocalDate.now();
                startDate = LocalDate.of(nowDate.getYear(), 1, 1);
                endDate = startDate.plusYears(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getAllByTime(start, end, tenantId, "3", deviceList);
                format = new SimpleDateFormat("yyyy-MM");
                dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
                    time = format.format(pipePartitionTotalFlow.getCollectTime());
                    valueMap = partitionTimeMap.get(devicePartitionMap.get(pipePartitionTotalFlow.getDeviceId()));
                    if (valueMap == null) {
                        valueMap = new HashMap<>();
                    }
                    if (valueMap.get(time) == null) {
                        valueMap.put(time, BigDecimal.ZERO);
                    }
                    valueMap.put(time, valueMap.get(time).add(pipePartitionTotalFlow.getValue()));

                    partitionTimeMap.put(devicePartitionMap.get(pipePartitionTotalFlow.getDeviceId()), valueMap);
                }
                for (String partitionId : partitionIdList) {
                    tempDate = LocalDate.of(startDate.getYear(), 1, 1);
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    tempPartition.put("name", partitionNameMap.get(partitionId));
                    timeMap = partitionTimeMap.get(partitionId);
                    while (tempDate.isBefore(endDate)) {
                        timeStr = dateTimeFormatter.format(tempDate);
                        tempPartition.getJSONArray("x").add(timeStr);
                        if (timeMap == null || timeMap.get(timeStr) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeStr));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeStr)));
                            total = total.add(timeMap.get(timeStr));

                        }
                        tempDate = tempDate.plusMonths(1L);
                    }
                    result.add(tempPartition);
                }
                break;
            default:
                break;
        }
        // 百分比
        String rate;
        for (Object object : result) {
            BigDecimal subTotal = ((JSONObject) object).getBigDecimal("total");
            try {
                rate = (subTotal.multiply(BigDecimal.valueOf(100)).divide(total, 2, RoundingMode.HALF_UP)) + "%";
                if (rate.contains(".00")) {
                    rate = rate.replaceAll("\\.00", "");
                }
            } catch (Exception e) {
                rate = "0%";
            }
            ((JSONObject) object).put("rate", rate);
        }
        return result;
    }

    @Override
    @Cacheable(cacheNames = "getSaleCount", key = "#type+#grade+#tenantId+#date")
    public JSONArray getSaleCount(String type, Integer grade, String tenantId, String date) {
        JSONArray result = new JSONArray();

        // 查询分区id
        List<Partition> partitionList = partitionMapper.getRootIdNameList(tenantId);
        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        for (int i = 0; i < grade; i++) {
            partitionList = partitionMapper.getAllIdNameByPidIn(partitionIdList, "");
            if (partitionList.size() == 0) {
                return result;
            }
            partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        }
        Map<String, String> partitionNameMap = partitionList.stream().collect(Collectors.toMap(a -> a.getId(), a -> a.getName()));

        Map<String, BigDecimal> timeMap;
        Map<String, Map<String, BigDecimal>> partitionTimeMap = new HashMap<>();
        // 分区下的用户
        QueryWrapper<PipePartitionCust> pipePartitionCustQueryWrapper = new QueryWrapper<>();
        pipePartitionCustQueryWrapper.in("partition_id", partitionIdList);
        List<PipePartitionCust> pipePartitionCustList = pipePartitionCustMapper.selectList(pipePartitionCustQueryWrapper);
        Map<String, String> custPartitionIdMap = pipePartitionCustList.stream().collect(Collectors.toMap(a -> a.getCustCode(), a -> a.getPartitionId()));
        BigDecimal total = BigDecimal.ZERO;
        switch (type) {
            case "month":
                LocalDate nowDate = LocalDate.now();
                LocalDate startDate = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), 1).minusMonths(1L);
                LocalDate endDate = startDate.plusMonths(1L);
                Long start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                Long end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

                List<CopyDataReadMeterData> copyDataList = copyDataReadMeterDataMapper.getListByPartitionId(partitionIdList, start, end, "2", tenantId);

                // 日期数组
                Map<String, BigDecimal> valueMap;
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String time;
                for (CopyDataReadMeterData copyDataReadMeterData : copyDataList) {
                    time = format.format(copyDataReadMeterData.getDataTime());
                    valueMap = partitionTimeMap.get(custPartitionIdMap.get(copyDataReadMeterData.getUserCode()));
                    if (valueMap == null) {
                        valueMap = new HashMap<>();
                    }
                    if (valueMap.get(time) == null) {
                        valueMap.put(time, BigDecimal.ZERO);
                    }
                    valueMap.put(time, valueMap.get(time).add(copyDataReadMeterData.getTotalWater()));

                    partitionTimeMap.put(custPartitionIdMap.get(copyDataReadMeterData.getUserCode()), valueMap);
                }

                String timeStr;
                JSONObject tempPartition;
                LocalDate tempDate;
                for (String partitionId : partitionIdList) {
                    tempDate = LocalDate.of(startDate.getYear(), startDate.getMonthValue(), 1);
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    tempPartition.put("name", partitionNameMap.get(partitionId));
                    timeMap = partitionTimeMap.get(partitionId);
                    while (tempDate.isBefore(endDate)) {
                        timeStr = dateTimeFormatter.format(tempDate);
                        tempPartition.getJSONArray("x").add(timeStr);
                        if (timeMap == null || timeMap.get(timeStr) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeStr));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeStr)));
                            total = total.add(timeMap.get(timeStr));

                        }
                        tempDate = tempDate.plusDays(1L);
                    }
                    result.add(tempPartition);
                }
                break;

            case "quarter":
                String[][] quarterArr = {{"01", "02", "03"}, {"04", "05", "06"}, {"07", "08", "09"}, {"10", "11", "12"}};
                String[] xName = {"第一季度", "第二季度", "第三季度", "第四季度"};
                nowDate = LocalDate.now();
                int i = 0;
                for (String[] timeArr : quarterArr) {
                    startDate = LocalDate.of(nowDate.getYear(), Integer.valueOf(timeArr[0]), 1);
                    endDate = startDate.plusMonths(3L);
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    copyDataList = copyDataReadMeterDataMapper.getListByPartitionId(partitionIdList, start, end, "2", tenantId);
                    for (CopyDataReadMeterData copyDataReadMeterData : copyDataList) {
                        valueMap = partitionTimeMap.get(custPartitionIdMap.get(copyDataReadMeterData.getUserCode()));
                        if (valueMap == null) {
                            valueMap = new HashMap<>();
                        }
                        if (valueMap.get(xName[i]) == null) {
                            valueMap.put(xName[i], BigDecimal.ZERO);
                        }
                        valueMap.put(xName[i], valueMap.get(xName[i]).add(copyDataReadMeterData.getTotalWater()));

                        partitionTimeMap.put(custPartitionIdMap.get(copyDataReadMeterData.getUserCode()), valueMap);
                    }
                    i++;
                }

                for (String partitionId : partitionIdList) {
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    tempPartition.put("name", partitionNameMap.get(partitionId));
                    timeMap = partitionTimeMap.get(partitionId);
                    for (i = 0; i < xName.length; i++) {
                        timeStr = xName[i];
                        tempPartition.getJSONArray("x").add(timeStr);
                        if (timeMap == null || timeMap.get(timeStr) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeStr));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeStr)));
                            total = total.add(timeMap.get(timeStr));

                        }
                    }
                    result.add(tempPartition);
                }

                break;
            case "year":
                nowDate = LocalDate.now();
                startDate = LocalDate.of(nowDate.getYear(), 1, 1);
                endDate = startDate.plusYears(1L);
                start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                copyDataList = copyDataReadMeterDataMapper.getListByPartitionId(partitionIdList, start, end, "2", tenantId);
                format = new SimpleDateFormat("yyyy-MM");
                dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                for (CopyDataReadMeterData copyDataReadMeterData : copyDataList) {
                    time = format.format(copyDataReadMeterData.getDataTime());
                    valueMap = partitionTimeMap.get(custPartitionIdMap.get(copyDataReadMeterData.getUserCode()));
                    if (valueMap == null) {
                        valueMap = new HashMap<>();
                    }
                    if (valueMap.get(time) == null) {
                        valueMap.put(time, BigDecimal.ZERO);
                    }
                    valueMap.put(time, valueMap.get(time).add(copyDataReadMeterData.getTotalWater()));

                    partitionTimeMap.put(custPartitionIdMap.get(copyDataReadMeterData.getUserCode()), valueMap);
                }
                for (String partitionId : partitionIdList) {
                    tempDate = LocalDate.of(startDate.getYear(), 1, 1);
                    tempPartition = new JSONObject();
                    tempPartition.put("x", new JSONArray());
                    tempPartition.put("y", new JSONArray());
                    tempPartition.put("total", BigDecimal.ZERO);
                    tempPartition.put("name", partitionNameMap.get(partitionId));
                    timeMap = partitionTimeMap.get(partitionId);
                    while (tempDate.isBefore(endDate)) {
                        timeStr = dateTimeFormatter.format(tempDate);
                        tempPartition.getJSONArray("x").add(timeStr);
                        if (timeMap == null || timeMap.get(timeStr) == null) {
                            tempPartition.getJSONArray("y").add(null);
                        } else {
                            tempPartition.getJSONArray("y").add(timeMap.get(timeStr));
                            tempPartition.put("total", tempPartition.getBigDecimal("total").add(timeMap.get(timeStr)));
                            total = total.add(timeMap.get(timeStr));

                        }
                        tempDate = tempDate.plusMonths(1L);
                    }
                    result.add(tempPartition);
                }
                break;
            default:
                break;
        }
        // 百分比
        String rate;
        for (Object object : result) {
            BigDecimal subTotal = ((JSONObject) object).getBigDecimal("total");
            try {
                rate = (subTotal.multiply(BigDecimal.valueOf(100)).divide(total, 2, RoundingMode.HALF_UP)) + "%";
                if (rate.contains(".00")) {
                    rate = rate.replaceAll("\\.00", "");
                }
            } catch (Exception e) {
                rate = "0%";
            }
            ((JSONObject) object).put("rate", rate);
        }
        return result;
    }

    @Override
    public JSONObject monitor(String partitionId, String tenantId) {
        JSONObject result = new JSONObject();
        // 是计量分区还是DMA分区 计量分区查当前分区和所有下级分区, DMA分区查自己的详细信息
        Partition partition = partitionMapper.selectById(partitionId);
        if (partition == null) {
            return result;
        }
        if (DataConstants.PARTITION_TYPE.METERING.getValue().equals(partition.getType())) {
            // 只查下级分区
            List<Partition> subPartitionList = partitionMapper.getAllIdNameByPid(partitionId);
            List<String> partitionIdList = subPartitionList.stream().filter(a -> "5".equals(a.getStatus())).map(a -> a.getId()).collect(Collectors.toList());
            if (partitionIdList.size() > 0) {
                subPartitionList = partitionMapper.getAllIdNameByPidIn(partitionIdList, null);
                if (subPartitionList.size() > 0) {
                    partitionIdList = subPartitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
                }
            }
            // 上月供水量占比
            LocalDate now = LocalDate.now();
            LocalDate endDate = LocalDate.of(now.getYear(), now.getMonthValue(), 1);
            LocalDate startDate = endDate.minusMonths(1L);
            Long start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            Long end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            List<JSONObject> jsonObjects = pipePartitionTotalFlowMapper.sumByPartitionId(partitionIdList, start, end, "3", "in", "");
            JSONObject subObject = new JSONObject();
            subObject.put("x", new JSONArray());
            subObject.put("y", new JSONArray());
            for (JSONObject jsonObject : jsonObjects) {
                subObject.getJSONArray("x").add(jsonObject.get("name"));
                subObject.getJSONArray("y").add(jsonObject.get("total"));
            }
            result.put("lastMonthSupply", subObject);

            // 上月售水量占比
            subObject = new JSONObject();
            subObject.put("x", new JSONArray());
            subObject.put("y", new JSONArray());
            List<JSONObject> maps = copyDataReadMeterDataMapper.sumByPartitionId(partitionIdList, start, end, null, tenantId);
            for (JSONObject map : maps) {
                subObject.getJSONArray("x").add(map.get("name"));
                subObject.getJSONArray("y").add(map.get("total"));
            }
            result.put("lastMonthSale", subObject);

            // 上月大用户水量占比
            jsonObjects = pipePartitionTotalFlowMapper.sumByPartitionId(partitionIdList, start, end, "3", "in", "3");
            subObject = new JSONObject();
            subObject.put("x", new JSONArray());
            subObject.put("y", new JSONArray());
            for (JSONObject jsonObject : jsonObjects) {
                subObject.getJSONArray("x").add(jsonObject.get("name"));
                subObject.getJSONArray("y").add(jsonObject.get("total"));
            }
            result.put("lastBigUserSupply", subObject);

            // 昨日小区漏失情况统计
            subObject = new JSONObject();
            subObject.put("x", new JSONArray());
            subObject.put("y", new JSONArray());
            String[] evaluateArr = {"较好", "一般", "较差"};
            for (int i = 0; i < 3; i++) {
                subObject.getJSONArray("x").add(evaluateArr[i]);
                subObject.getJSONArray("y").add(0);
            }
            result.put("lastLossEvaluate", subObject);
            // 分区下的所有dma分区
            List<String> dmaIdList = new ArrayList<>();
            this.getDMAChildId(partition, dmaIdList);
            LocalDate nowDate = LocalDate.now();
            int year = nowDate.getYear();
            int month = nowDate.getMonthValue();
            if (dmaIdList.size() > 0) {
                List<Partition> partitionList = partitionMapper.selectBatchIds(dmaIdList);
                // 是否设置小流指标
                List<PipeMinFlowConfig> pipeMinFlowConfigs = pipeMinFlowConfigMapper.selectBatchIds(dmaIdList);
                Map<String, PipeMinFlowConfig> pipeMinFlowConfigMap = new HashMap<>();
                for (PipeMinFlowConfig pipeMinFlowConfig : pipeMinFlowConfigs) {
                    pipeMinFlowConfigMap.put(pipeMinFlowConfig.getPartitionId(), pipeMinFlowConfig);
                }
                List<JSONObject> partitionFlow;
                PipeMinFlowConfig pipeMinFlowConfig;
                BigDecimal flow;
                // 获取分区最小流量区间里的值
                for (Partition partition1 : partitionList) {
                    // 没设置值的算一般
                    pipeMinFlowConfig = pipeMinFlowConfigMap.get(partition1.getId());
                    if (partition1.getMinFlowStartHour() == null || partition1.getMinFlowEndHour() == null ||
                            pipeMinFlowConfig == null || pipeMinFlowConfig.getNightFlowMin() == null || pipeMinFlowConfig.getNightFlowMax() == null) {
                        subObject.getJSONArray("y").set(1, subObject.getJSONArray("y").getInteger(1) + 1);
                        continue;
                    }
                    // 查询该区间这个时间点的流量
                    start = LocalDate.of(year, month, partition1.getMinFlowStartHour()).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = LocalDate.of(year, month, partition1.getMinFlowEndHour()).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    partitionFlow = pipePartitionTotalFlowMapper.sumByPartitionId(Collections.singletonList(partition1.getId()), start, end, "1", "in", "");
                    try {
                        flow = partitionFlow.get(0).getBigDecimal("total").divide(BigDecimal.valueOf(partition1.getMinFlowEndHour() - partition1.getMinFlowStartHour()), 2, RoundingMode.HALF_UP);
                        if (flow.compareTo(pipeMinFlowConfig.getNightFlowMin()) < 0) { // 较好
                            subObject.getJSONArray("y").set(0, subObject.getJSONArray("y").getInteger(1) + 1);
                        } else if (flow.compareTo(pipeMinFlowConfig.getNightFlowMax()) > 0) { // 一般
                            subObject.getJSONArray("y").set(2, subObject.getJSONArray("y").getInteger(1) + 1);
                        } else { // 较差
                            subObject.getJSONArray("y").set(2, subObject.getJSONArray("y").getInteger(1) + 1);
                        }
                    } catch (Exception e) {
                        subObject.getJSONArray("y").set(1, subObject.getJSONArray("y").getInteger(1) + 1);
                    }
                }
            }

            // 当前分区和下级分区上月产销差
            PartitionNRWDTO partitionNRWDTO = new PartitionNRWDTO();
            nowDate = LocalDate.now().minusMonths(1L);
            startDate = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), 1);
            endDate = startDate.plusMonths(1L);
            start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 供水总量
            partitionIdList.add(partitionId);
            List<JSONObject> supplyTotalList = pipePartitionTotalFlowMapper.sumByPartitionId(partitionIdList, start, end, "2", "in", "");
            // 进水量
            List<JSONObject> inTotalList = pipePartitionTotalFlowMapper.sumByPartitionId(partitionIdList, start, end, "2", "in", "1");// 流量计
            // 出水量
            List<JSONObject> outTotalList = pipePartitionTotalFlowMapper.sumByPartitionId(partitionIdList, start, end, "2", "out", "1"); // 出口表
            // 用户数
            List<JSONObject> userNumList = partitionMapper.getUserNum(partitionIdList);
            // 校准用水量
            List<JSONObject> correctUseWater = pipeCopyDataReadMeterDataMapper.sumCorrectByPartitionId(partitionIdList, start, end);


            Map<String, BigDecimal> supplyTotalMap = supplyTotalList.stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getBigDecimal("total")));
            Map<String, BigDecimal> inTotalMap = inTotalList.stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getBigDecimal("total")));
            Map<String, BigDecimal> outTotalMap = outTotalList.stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getBigDecimal("total")));
            Map<String, Integer> userNumMap = userNumList.stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getInteger("userNum")));
            Map<String, BigDecimal> correctUseWaterMap = correctUseWater.stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getBigDecimal("total")));
            String date = DateTimeFormatter.ofPattern("yyyy-MM").format(nowDate);
            buildPartitionNRW(partitionId, partition, partitionNRWDTO, supplyTotalMap, inTotalMap, outTotalMap, userNumMap, correctUseWaterMap, date);
            // 下级分区
            PartitionNRWDTO subPartitionNRWDTO;
            for (Partition subPartition : subPartitionList) {
                subPartitionNRWDTO = new PartitionNRWDTO();
                buildPartitionNRW(subPartition.getId(), subPartition, subPartitionNRWDTO, supplyTotalMap, inTotalMap, outTotalMap, userNumMap, correctUseWaterMap, date);
                partitionNRWDTO.setUserNum(partitionNRWDTO.getUserNum() + subPartitionNRWDTO.getUserNum());
                partitionNRWDTO.setCorrectUseWater(partitionNRWDTO.getCorrectUseWater().add(subPartitionNRWDTO.getCorrectUseWater()));
                partitionNRWDTO.setSupplyTotal(partitionNRWDTO.getSupplyTotal().add(subPartitionNRWDTO.getSupplyTotal()));
                partitionNRWDTO.setInWater(partitionNRWDTO.getInWater().add(subPartitionNRWDTO.getInWater()));
                partitionNRWDTO.setOutWater(partitionNRWDTO.getOutWater().add(subPartitionNRWDTO.getOutWater()));
                partitionNRWDTO.getSubPartitionNRWDTOList().add(subPartitionNRWDTO);
            }
            partitionNRWDTO.setLossTotal(partitionNRWDTO.getInWater().subtract(partitionNRWDTO.getOutWater()).subtract(partitionNRWDTO.getCorrectUseWater()));
            try {
                partitionNRWDTO.setNrw(partitionNRWDTO.getSupplyTotal().subtract(partitionNRWDTO.getOutWater()).subtract(partitionNRWDTO.getCorrectUseWater()).multiply(BigDecimal.valueOf(100)).divide(partitionNRWDTO.getSupplyTotal(), 2, RoundingMode.FLOOR));
            } catch (Exception e) {
                partitionNRWDTO.setNrw(BigDecimal.ZERO);
                e.printStackTrace();
            }

            result.put("partitionNRW", partitionNRWDTO);

            // DMA分区产销差
            // 供水量
            supplyTotalList = pipePartitionTotalFlowMapper.sumByPartitionId(dmaIdList, start, end, "2", "in", "");
            // 用水量
            Map<String, BigDecimal> userWaterMap = copyDataReadMeterDataMapper.sumByPartitionId(dmaIdList, start, end, "", tenantId).stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getBigDecimal("total")));

            JSONObject nrwRate = new JSONObject();
            nrwRate.put("x", new JSONArray());
            nrwRate.put("y", new JSONArray());
            JSONObject jsonObject;
            List<JSONObject> nrwList = new ArrayList<>();
            for (JSONObject tempObject : supplyTotalList) {
                jsonObject = new JSONObject();
                jsonObject.put("name", tempObject.getString("name"));
                BigDecimal nrw = BigDecimal.ZERO;
                try {
                    nrw = tempObject.getBigDecimal("total").subtract(userWaterMap.get(tempObject.getString("id"))).multiply(BigDecimal.valueOf(100)).divide(tempObject.getBigDecimal("total"), 2, RoundingMode.HALF_UP);
                } catch (Exception e) {
                }
                jsonObject.put("nrw", nrw);
                nrwList.add(jsonObject);
            }
            // 排序
            nrwList = nrwList.stream().sorted(Comparator.comparingDouble(a -> a.getBigDecimal("nrw").doubleValue())).collect(Collectors.toList());
            result.put("nrwList", nrwList);
        } else { // DMA分区
            // 昨日水量信息
            LocalDate now = LocalDate.now();
            LocalDate startDate = now.minusDays(1L);
            Long start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            Long end = now.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 用户类型
            try {
                result.put("userType", DataConstants.PARTITION_USER_TYPE.getByValue(partition.getUserType()).getName());
            } catch (Exception e) {
                result.put("userType", "-");
            }
            // 供水量
            BigDecimal totalFlow = pipePartitionTotalFlowMapper.getPartitionTotalFlow(partitionId, startDate, now, "2", "in");
            if (totalFlow == null) {
                totalFlow = BigDecimal.ZERO;
            }
            result.put("supply", totalFlow);
            // 售水量
            JSONObject sale = copyDataReadMeterDataMapper.sumByPartitionId(Collections.singletonList(partitionId), start, end, "", tenantId).get(0);
            result.put("sale", sale.get("total"));
            // 产销差水量
            BigDecimal nrw = BigDecimal.ZERO;
            String nrwRate = "0%";
            try {
                nrw = totalFlow.subtract(sale.getBigDecimal("total"));
                nrwRate = nrw.multiply(BigDecimal.valueOf(100)).divide(totalFlow, 2, RoundingMode.HALF_UP) + "%";
            } catch (Exception e) {
                // e.printStackTrace();
            }
            result.put("nrw", nrw);
            result.put("nrwRate", nrwRate);
            // 用户数
            JSONObject jsonObject = partitionMapper.getUserNum(Collections.singletonList(partitionId)).get(0);
            result.put("userNum", jsonObject.getBigDecimal("userNum"));
            // 大用户用水量
            List<JSONObject> bigUserList = pipePartitionTotalFlowMapper.sumByPartitionId(Collections.singletonList(partitionId), start, end, "2", "in", "3");
            JSONObject bigUser;
            if (bigUserList.size() == 0) {
                bigUser = new JSONObject();
                bigUser.put("total", BigDecimal.ZERO);
            } else {
                bigUser = bigUserList.get(0);
            }
            result.put("bigUserWater", bigUser.getBigDecimal("total"));
            // 其他用水量
            result.put("otherWater", totalFlow.subtract(bigUser.getBigDecimal("total")));

            // 昨日小流信息
            // 夜间最小流量
            // 分区下的流量计
            List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), "1", "in", tenantId);
            BigDecimal minNightFlow = BigDecimal.ZERO;
            String minNightFlowTime = "-";
            BigDecimal minValue = BigDecimal.ZERO;
            String minValueTime = "-";
            BigDecimal lossWater = BigDecimal.ZERO;
            BigDecimal pipeLength = partition.getMainLineLength();
            String mnfDivDayAvgFlow = "0%";
            BigDecimal unitPipeNightFlow = BigDecimal.ZERO;
            String lossLevel;

            List<String> deviceIdListOrigin = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
            if (deviceIdListOrigin.size() == 0) {
                deviceIdListOrigin.add("-");
            }
            List<String> deviceIdList = new ArrayList<>();
            for (String deviceId : deviceIdListOrigin) {
                deviceIdList.add(deviceId + ".total_flow");
                deviceIdList.add(deviceId + ".Instantaneous_flow");
            }
            try {
                JSONObject data = influxUtil.getData(deviceIdList, start, end, "15m");
                List<String> timeList = data.keySet().stream().sorted().collect(Collectors.toList());
                // 夜间最小流时间点
                int startHour = 2;
                int endHour = 4;
                if (partition.getMinFlowEndHour() != null) {
                    startHour = partition.getMinFlowStartHour();
                }
                if (partition.getMinFlowEndHour() != null) {
                    endHour = partition.getMinFlowEndHour();
                }
                Long startLong = start + (startHour * 60 * 60 * 1000);
                Long endLong = start + (endHour * 60 * 60 * 1000);


                BigDecimal tempFlowValue;
                BigDecimal tempTotalValue;
                BigDecimal tempFlowTotal = BigDecimal.ZERO;
                int tempFlowSize = 0;
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
                for (String time : timeList) {
                    tempFlowValue = BigDecimal.ZERO;
                    tempTotalValue = BigDecimal.ZERO;
                    for (String deviceId : deviceIdList) {
                        if (data.getJSONObject(time).get(deviceId) != null) {
                            try {
                                tempFlowTotal.add(data.getJSONObject(time).getBigDecimal(deviceId.substring(0, deviceId.indexOf(".")) + ".Instantaneous_flow"));
                                tempFlowSize++;
                                if (format.parse(time).getTime() >= startLong && format.parse(time).getTime() <= endLong) {
                                    tempFlowValue = tempFlowValue.add(data.getJSONObject(time).getBigDecimal(deviceId.substring(0, deviceId.indexOf(".")) + ".Instantaneous_flow"));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            try {
                                if (format.parse(time).getTime() >= startLong && format.parse(time).getTime() <= endLong) {
                                    tempTotalValue = tempTotalValue.add(data.getJSONObject(time).getBigDecimal(deviceId.substring(0, deviceId.indexOf(".")) + ".total_flow"));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    if (tempFlowValue.compareTo(minNightFlow) < 0) {
                        minNightFlow = tempFlowValue;
                        minNightFlowTime = time;
                    }
                    if (tempTotalValue.compareTo(minValue) < 0) {
                        minValue = tempTotalValue;
                        minValueTime = time;
                    }
                }
                // MNF / 日均流量
                try {
                    mnfDivDayAvgFlow = minNightFlow.multiply(BigDecimal.valueOf(100)).divide(tempFlowTotal, 2, RoundingMode.FLOOR).multiply(BigDecimal.valueOf(tempFlowSize)) + "%";
                } catch (Exception e) {
                }
                // 单位管长净夜间流量
                try {
                    unitPipeNightFlow = minNightFlow.divide(pipeLength, 2, RoundingMode.FLOOR);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                    /*
                     预估漏失水量 = 净夜间流量 * 日变化系数(24)
                     净夜间流量 = MNF - 用户合法用水量 - 大用户用水量
                     */
                // BigDecimal legalWater = BigDecimal.valueOf(0.002);
                BigDecimal legalWater = BigDecimal.ZERO;
                if (partition.getLegalUseWater() != null) {
                    legalWater = partition.getLegalUseWater();
                }
                // 大用户用水量
                BigDecimal bigUserWater = bigUser.getBigDecimal("total");
                try {
                    // lossWater = minNightFlow.subtract(legalWater).subtract(bigUserWater).multiply(BigDecimal.valueOf(24));
                    lossWater = minNightFlow.subtract(legalWater).multiply(BigDecimal.valueOf(24));
                    // if (lossWater.doubleValue() < 0) {
                    //     lossWater = BigDecimal.ZERO;
                    // }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                    /*
                    1.夜间最小流初始值设定,夜间最小流( 小于较好 / -一般 / 大于较差)
                    2.单位管长夜间净流量初始值设定,夜间最小流( 小于较好 / -一般 / 大于较差)
                    3.MNF/日均小时流量初始值设定,夜间最小流( 小于%较好 / %-%一般 / 大于%较差)
                    4.满足两个及以上较差评为较差,满足三个较好评为较好,其它评为一般。
                     */
                int ANum = 0; // 较好
                int BNum = 0; // 一般
                int CNum = 0; // 较差
                List<PipeMinFlowConfig> pipeMinFlowConfigList = pipeMinFlowConfigMapper.getListByPartitionIdIn(Collections.singletonList(partitionId));
                if (pipeMinFlowConfigList.size() > 0) {
                    PipeMinFlowConfig pipeMinFlowConfig = pipeMinFlowConfigList.get(0);
                    if (pipeMinFlowConfig.getNightFlowMin() == null || pipeMinFlowConfig.getNightFlowMax() == null) {
                        BNum++;
                    } else {
                        if (minNightFlow.compareTo(pipeMinFlowConfig.getNightFlowMin()) < 0) {
                            ANum++;
                        } else if (minNightFlow.compareTo(pipeMinFlowConfig.getNightFlowMax()) > 0) {
                            CNum++;
                        } else {
                            BNum++;
                        }
                    }
                    if (pipeMinFlowConfig.getUnitPipeNightFlowMin() == null || pipeMinFlowConfig.getUnitPipeNightFlowMax() == null) {
                        BNum++;
                    } else {
                        if (unitPipeNightFlow.compareTo(pipeMinFlowConfig.getUnitPipeNightFlowMin()) < 0) {
                            ANum++;
                        } else if (unitPipeNightFlow.compareTo(pipeMinFlowConfig.getUnitPipeNightFlowMax()) > 0) {
                            CNum++;
                        } else {
                            BNum++;
                        }
                    }
                    if (pipeMinFlowConfig.getMnfDivDayAvgHourFlowMin() == null || pipeMinFlowConfig.getMnfDivDayAvgHourFlowMax() == null) {
                        BNum++;
                    } else {
                        if (BigDecimal.valueOf(Double.valueOf(mnfDivDayAvgFlow.replace("%", ""))).compareTo(pipeMinFlowConfig.getMnfDivDayAvgHourFlowMin()) < 0) {
                            ANum++;
                        } else if (BigDecimal.valueOf(Double.valueOf(mnfDivDayAvgFlow.replace("%", ""))).compareTo(pipeMinFlowConfig.getMnfDivDayAvgHourFlowMax()) > 0) {
                            CNum++;
                        } else {
                            BNum++;
                        }
                    }
                }
                if (ANum > 1) {
                    lossLevel = "较好";
                } else if (BNum > 1) {
                    lossLevel = "较差";
                } else {
                    lossLevel = "一般";
                }
                result.put("minNightFlow", minNightFlow);
                result.put("minNightFlowTime", minNightFlowTime);
                result.put("minValue", minValue);
                result.put("minValueTime", minValueTime);
                result.put("lossWater", lossWater);
                result.put("pipeLength", pipeLength);
                result.put("mnfDivDayAvgFlow", mnfDivDayAvgFlow);
                result.put("unitPipeNightFlow", unitPipeNightFlow);
                result.put("lossLevel", lossLevel);

                // 昨日瞬时流量 净累计
                data = influxUtil.getData(deviceIdList, start - 1, end, "1h");
                timeList = data.keySet().stream().sorted().collect(Collectors.toList());
                Map<String, Map<String, BigDecimal>> timeMap = new HashMap<>();
                Map<String, BigDecimal> flowMap;
                String suffix = "";
                for (String time : timeList) {
                    for (String deviceId : deviceIdList) {
                        if (data.getJSONObject(time).get(deviceId) != null) {
                            suffix = deviceId.substring(deviceId.indexOf("."));
                            if (timeMap.get(time) == null) {
                                timeMap.put(time, new HashMap<>());
                            }
                            flowMap = timeMap.get(time);
                            if (flowMap.get(suffix) == null) {
                                flowMap.put(suffix, BigDecimal.ZERO);
                            }
                            flowMap.put(suffix, flowMap.get(suffix).add(data.getJSONObject(time).getBigDecimal(deviceId)));
                            timeMap.put(time, flowMap);
                        }
                    }
                }
                JSONObject dayFlow = new JSONObject();
                dayFlow.put("x", new JSONArray());
                dayFlow.put("flow", new JSONArray());
                dayFlow.put("total", new JSONArray());
                for (int i = 0; i < 24; i++) {
                    dayFlow.getJSONArray("x").add(i);
                    if (timeList.size() < (i + 1)) {
                        dayFlow.getJSONArray("flow").add(null);
                        dayFlow.getJSONArray("total").add(null);
                    } else {
                        dayFlow.getJSONArray("flow").add(timeMap.get(timeList.get(i)).get(".Instantaneous_flow"));
                        dayFlow.getJSONArray("total").add(timeMap.get(timeList.get(i)).get(".total_flow"));
                    }
                }
                result.put("dayFlow", dayFlow);

                // 今年产销差
                // 获取根分区下的入口表
                // 当前瞬时流量和供水量
                List<String> deviceList = partitionMountList.stream().map(a -> a.getDeviceId()).collect(Collectors.toList());
                if (deviceList.size() == 0) {
                    deviceList.add("-");
                }
                startDate = LocalDate.of(now.getYear(), 1, 1);
                LocalDate endDate = startDate.plusYears(1L);
                int i = 1;
                JSONObject yearNRW = new JSONObject();
                yearNRW.put("x", new JSONArray());
                yearNRW.put("supply", new JSONArray());
                yearNRW.put("sale", new JSONArray());
                yearNRW.put("nrw", new JSONArray());
                while (startDate.isBefore(endDate)) {
                    BigDecimal quarterSupply = BigDecimal.ZERO;
                    BigDecimal quarterSale = BigDecimal.ZERO;
                    start = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    end = startDate.plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    try {
                        // 供水量
                        quarterSupply = pipePartitionTotalFlowMapper.sumByTime(start, end, tenantId, "3", deviceList);
                        int count = pipePartitionTotalFlowMapper.countByTime(start, end, tenantId, "3", deviceList);
                        if (count == 0) {
                            quarterSupply = null;
                            quarterSale = null;
                            nrw = null;
                        } else {
                            // 售水量
                            List<JSONObject> jsonObjectList = copyDataReadMeterDataMapper.sumByPartitionId(Collections.singletonList(partitionId), start, end, "", tenantId);
                            if (jsonObjectList.size() > 0) {
                                quarterSale = jsonObjectList.get(0).getBigDecimal("total");
                            }
                            try {
                                nrw = quarterSupply.subtract(quarterSale).multiply(BigDecimal.valueOf(100)).divide(quarterSupply, 2, RoundingMode.FLOOR);
                            } catch (Exception e) {
                                nrw = BigDecimal.ZERO;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    yearNRW.getJSONArray("x").add(i++);
                    yearNRW.getJSONArray("supply").add(quarterSupply);
                    yearNRW.getJSONArray("sale").add(quarterSale);
                    yearNRW.getJSONArray("nrw").add(nrw);
                    startDate = startDate.plusMonths(1L);
                }
                result.put("yearNRW", yearNRW);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    @Override
    public JSONObject getMonthDetailFlow(String partitionId, String tenantId) {
        LocalDate nowDate = LocalDate.now();
        LocalDate endDate = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), 1);
        LocalDate startDate = endDate.minusMonths(1L);
        Long end = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long start = endDate.minusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        List<PipePartitionTotalFlow> pipePartitionTotalFlowList = pipePartitionTotalFlowMapper.getListByPartitionId(partitionId, start, end, "2", "in", tenantId);
        JSONObject result = new JSONObject();
        result.put("x", new JSONArray());
        result.put("y", new JSONArray());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String time;
        Map<String, BigDecimal> valueMap = new HashMap<>();
        for (PipePartitionTotalFlow pipePartitionTotalFlow : pipePartitionTotalFlowList) {
            time = format.format(pipePartitionTotalFlow.getCollectTime());
            if (valueMap.get(time) == null) {
                valueMap.put(time, BigDecimal.ZERO);
            }
            valueMap.put(time, valueMap.get(time).add(pipePartitionTotalFlow.getValue()));
        }

        int i = 1;
        String timeStr;
        while (startDate.isBefore(endDate)) {
            timeStr = dateTimeFormatter.format(startDate);

            // 横坐标
            result.getJSONArray("x").add(i++);
            // 纵坐标
            if (valueMap.get(timeStr) == null) {
                result.getJSONArray("y").add(null);
            } else {
                result.getJSONArray("y").add(valueMap.get(timeStr));
            }
            startDate = startDate.plusDays(1L);
        }
        return result;
    }

    @Override
    public JSONObject getOverview(String partitionId, String tenantId) {
        JSONObject result = new JSONObject();
        Partition partition = partitionMapper.selectById(partitionId);
        if (partition == null) {
            return result;
        }
        result.put("partitionName", partition.getName());
        result.put("code", partition.getCode());
        result.put("type", DataConstants.PARTITION_TYPE.getByValue(partition.getType()).getName());
        result.put("supplyWaterArea", partition.getSupplyWaterArea());
        result.put("mainLineLength", partition.getMainLineLength());
        try {
            result.put("userType", DataConstants.PARTITION_USER_TYPE.getByValue(partition.getUserType()).getName());
        } catch (Exception e) {
        }
        result.put("copyMeterUser", partition.getCopyMeterUser());
        result.put("director", partition.getDirector());
        result.put("range", partition.getRange());
        if (DataConstants.PARTITION_TYPE.METERING.getValue().equals(partition.getType())) { // 计量分区
            List<String> pidList = new ArrayList<>();
            List<String> allPartitionIdList = new ArrayList<>();
            allPartitionIdList.add(partitionId);
            pidList.add(partitionId);
            if (StringUtils.isBlank(partition.getPid())) { // 顶级分区
                // 一级分区个数
                pidList = partitionMapper.getAllIdNameByPidIn(pidList, "1").stream().map(a -> a.getId()).collect(Collectors.toList());
                allPartitionIdList.addAll(pidList);
                result.put("levelOneNum", pidList.size());
            }

            // 二级分区个数
            result.put("levelTwoNum", 0);
            if (pidList.size() > 0) {
                pidList = partitionMapper.getAllIdNameByPidIn(pidList, "1").stream().map(a -> a.getId()).collect(Collectors.toList());
                allPartitionIdList.addAll(pidList);
                result.put("levelTwoNum", pidList);
            }
            // DMA分区个数
            List<String> dmaIdList = new ArrayList<>();
            getDMAChildId(partition, dmaIdList);
            result.put("dmaNum", dmaIdList.size());
            // DMA分区状态
            DataConstants.PARTITION_STATUS[] values = DataConstants.PARTITION_STATUS.values();
            Map<String, Integer> partitionStatusMap = new LinkedHashMap<>();
            for (DataConstants.PARTITION_STATUS a : values) {
                partitionStatusMap.put(a.getName(), 0);
            }
            if (dmaIdList.size() > 0) {
                List<Partition> partitionList = partitionMapper.selectBatchIds(dmaIdList);
                //
                String statusName;
                for (Partition partition1 : partitionList) {
                    statusName = DataConstants.PARTITION_STATUS.getByValue(partition1.getStatus()).getName();
                    partitionStatusMap.put(statusName, partitionStatusMap.get(statusName) + 1);
                }
            }

            List<Map> dmaPartitionStatusList = new ArrayList<>();
            Map tempMap;
            for (DataConstants.PARTITION_STATUS a : values) {
                tempMap = new HashMap<>();
                tempMap.put("name", a.getName());
                tempMap.put("num", partitionStatusMap.get(a.getName()));

                dmaPartitionStatusList.add(tempMap);
            }
            result.put("dmaPartitionStatus", dmaPartitionStatusList);

        } else { // DMA分区
            // 物业公司
            result.put("propertyName", partition.getPropertyName());
            // 入水口 流量计入口表
            List<PartitionMount> inWaterNum = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), "1", "in", tenantId);
            result.put("inWaterNum", inWaterNum.size());

            // 分区状态
            try {
                result.put("status", DataConstants.PARTITION_STATUS.getByValue(partition.getStatus()).getName());
            } catch (Exception e) {
                result.put("status", "-");
            }

        }
        // 挂接用户数 当前分区和子分区下的所有用户
        List<String> partitionIdList = new ArrayList<>();
        this.getAllChildId(partitionId, partitionIdList);
        QueryWrapper<PipePartitionCust> pipePartitionCustQueryWrapper = new QueryWrapper<>();
        pipePartitionCustQueryWrapper.in("partition_id", partitionIdList);
        List<PipePartitionCust> pipePartitionCustList = pipePartitionCustMapper.selectList(pipePartitionCustQueryWrapper);
        result.put("custNum", pipePartitionCustList.size());

        // DMA分区挂接用户用水类型
        if (DataConstants.PARTITION_TYPE.DMA.getValue().equals(partition.getType())) {
            JSONObject waterCategoryType = (JSONObject) sysCodeService.detailList("WaterCategoryType", new TenantId(UUIDConverter.fromString(tenantId)));
            SysCodeDTO sysCodeDTO = waterCategoryType.getObject("WaterCategoryType", SysCodeDTO.class);
            Map custUseWaterType = new HashMap();
            for (SysCodeDetail sysCodeDetail : sysCodeDTO.getDetails()) {
                custUseWaterType.put(sysCodeDetail.getName(), 0);
            }
            if (pipePartitionCustList.size() > 0) {
                List<String> custCodeList = pipePartitionCustList.stream().map(a -> a.getCustCode()).collect(Collectors.toList());
                List<Map> custUseWaterTypeList = custInfoMapper.sumWaterType(custCodeList, tenantId);
                for (Map map : custUseWaterTypeList) {
                    custUseWaterType.put(map.get("name"), map.get("total"));
                }
            }
            List<String> waterTypeNameList = (List<String>) custUseWaterType.keySet().stream().collect(Collectors.toList());
            List<Map> waterTypeList = new ArrayList<>();
            Map tempMap;
            for (String waterTypeName : waterTypeNameList) {
                tempMap = new HashMap();
                tempMap.put("name", waterTypeName);
                tempMap.put("num", custUseWaterType.get(waterTypeName));
                waterTypeList.add(tempMap);
            }
            result.put("custUseWaterType", waterTypeList);
        }

        // 上级分区名称
        if (StringUtils.isNotBlank(partition.getPid())) {
            result.put("parentName", "-");
            // 上级分区名称
            Partition parentPartition = partitionMapper.selectById(partition.getPid());
            if (parentPartition != null) {
                result.put("parentName", parentPartition.getName());
            }
        }

        // 远传大用户数 当前分区和子分区下的所有大用户
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "3", "", tenantId);
        result.put("bigUserNum", partitionMountList.size());

        // 流量监测点 // 当前分区的流量监测点
        partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "1", "", tenantId);
        result.put("flowNum", partitionMountList.size());

        // 压力监测点 当前分区和子分区下的所有压力监测点
        partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, "2", "", tenantId);
        result.put("pressureNum", partitionMountList.size());

        return result;
    }

    @Override
    public Map<String, Integer> getUserNum(List<String> partitionIdList) {
        List<JSONObject> userNum = partitionMapper.getUserNum(partitionIdList);
        Map result = new HashMap();
        for (JSONObject object : userNum) {
            result.put(object.getString("id"), object.getInteger("userNum"));
        }

        return result;
    }

    @Override
    public void changeStatus(Partition partition) {
        Partition tempPartition = new Partition();
        tempPartition.setId(partition.getId());
        tempPartition.setStatus(partition.getStatus());

        partitionMapper.updateById(tempPartition);
    }

    @Override
    public Map<String, Integer> getCopiedNum(List<String> partitionIdList, List<String> ymList) {
        List<JSONObject> list = partitionMapper.getCopiedNum(partitionIdList, ymList);

        return list.stream().collect(Collectors.toMap(a -> a.getString("id"), a -> a.getInteger("total")));
    }

    @Override
    public List<Partition> getListByIdIn(List<String> partitionIdList) {
        return partitionMapper.selectBatchIds(partitionIdList);
    }

    @Override
    public List<Partition> getchildByPid(String partitionId, String tenantId) {
        return partitionMapper.getAllIdNameByPid(partitionId);
    }

    @Override
    public List<Partition> getAll(String type, String status, String partitionName, String tenantId) {
        return partitionMapper.getAllByCondition(type, status, partitionName, tenantId);
    }

    @Override
    public List<DMAOverviewDTO> getDMAOverview(String status, String partitionName, String tenantId) {
        return partitionMapper.getDMAOverview(status, partitionName, tenantId);
    }

    @Override
    public Map<String, Integer> getMountNum(List<String> partitionIdList, String mountType, String direction) {
        List<JSONObject> userNum = partitionMapper.getMountNum(partitionIdList, mountType, direction);
        Map result = new HashMap();
        for (JSONObject object : userNum) {
            result.put(object.getString("id"), object.getInteger("num"));
        }

        return result;
    }

    @Override
    public List<Partition> getRootIdNameList(String tenantId) {
        return partitionMapper.getRootIdNameList(tenantId);
    }

    @Override
    public List<Partition> getAllIdNameByPidIn(List<String> partitionIdList, String type) {
        return partitionMapper.getAllIdNameByPidIn(partitionIdList, type);
    }

    @Override
    public String getNRWByName(String name, String tenantId) {
        // 上月
        LocalDate now = LocalDate.now();
        List<String> queryList = new ArrayList<>();
        Long end = LocalDate.of(now.getYear(), now.getMonthValue(), 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1;
        Long start = LocalDate.of(now.getYear(), now.getMonthValue(), 1).minusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        BigDecimal totalFlow = BigDecimal.ZERO;
        String partitionId;
        String deviceId = "";
        String nrw = null;
        if (name == null) {
            name = "梓莲达";
        }
        if (name.contains("西部")) {
            deviceId = "1ee20a103973a1091506ddf731ece97";
            partitionId = "8d462506aa4c4c9bf8071d90923ff615";
            nrw = "86.88";
        } else if (name.contains("北部")) {
            deviceId = "1eeba646cf18e909f077f88cf1110e4";
            partitionId = "6339409eb344b51bb11f5a0be4f89025";
            nrw = "69.56";
        } else if (name.contains("金峰")) {
            deviceId = "1eeba64a3a660f09f077f88cf1110e4";
            partitionId = "f53eb7ca1619323748193386f3347c8f";
            nrw = "0";
        } else {
            WaterOverviewDTO waterOverview = this.getWaterOverview(tenantId);
            String result = waterOverview.getLastMonthNRW().replace("%", "");

            if (Double.valueOf(result) < 0) {
                result = "84.32";
            }
            return result;
        }

        if (StringUtils.isNotBlank(nrw)) {
            return nrw;
        }

        queryList.add(deviceId + ".total_flow");
        JSONObject data = influxUtil.getData(queryList, start, end, "month");
        List<String> collect = data.keySet().stream().sorted().collect(Collectors.toList());
        String result = "0";
        if (collect.size() == 0) {
            return result;
        }
        data = data.getJSONObject(collect.get(collect.size() - 1));
        try {
            totalFlow = data.getBigDecimal(deviceId + ".total_flow");
            List<String> partitionIdList = new ArrayList<>();
            partitionIdList.add(partitionId);
            List<JSONObject> jsonObjectList = copyDataReadMeterDataMapper.sumByPartitionId(partitionIdList, start, end, DataConstants.PARTITION_TYPE.DMA.getValue(), tenantId);
            if (jsonObjectList.size() > 0) {
                BigDecimal total = jsonObjectList.get(0).getBigDecimal("total");
                result = totalFlow.subtract(total).multiply(BigDecimal.valueOf(100)).divide(totalFlow, 2, RoundingMode.FLOOR) + "";
                if (result.contains(".00")) {
                    result = result.replaceAll("\\.00", "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Double.valueOf(result) < 0) {
            result = nrw;
        }
        return result;
    }

    @Override
    public BigDecimal inWater(String name, String tenantId) {
        Long end = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        Long start = LocalDate.of(LocalDate.now().getYear(), 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        List<String> deviceIdList = new ArrayList<>();
        if (name == null) {
            name = "梓莲达";
        }
        if (name.contains("西部")) {
            deviceIdList.add("1ee215b3052fe3091506ddf731ece97");
        } else if (name.contains("北部")) {
            deviceIdList.add("1eebb67315936e09f077f88cf1110e4");
        } else if (name.contains("金峰")) {
            deviceIdList.add("1");
        } else if (name.contains("overview")) {
            deviceIdList.add("1ef2a243e676f10bbb73bc0099c1885");
            deviceIdList.add("1ef2a242c277f70bbb73bc0099c1885");
            deviceIdList.add("1ef4d6725386d80af916382d689f51d");
            deviceIdList.add("1ef55f3f5a61190943dbb77a01ef94a");
        } else {
            deviceIdList.add("1ee215b3052fe3091506ddf731ece97");
            deviceIdList.add("1eebb67315936e09f077f88cf1110e4");
        }
        BigDecimal bigDecimal = pipePartitionTotalFlowMapper.sumByTime(start, end, tenantId, "3", deviceIdList);

        return bigDecimal;
    }

    @Override
    public JSONObject getNrwYearByName(String name, String tenantId) {
        // 获取所有DMA分区
        List<Partition> partitionList = partitionMapper.getAllByType(name, "2", "5", tenantId);
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), 1, 1);
        // 过滤
        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        if (StringUtils.isNotBlank(name) && !name.contains("梓莲达")) {
            List<Partition> partitions = partitionList.stream().filter(a -> a.getName().contains(name)).collect(Collectors.toList());
            if (partitions.size() != 0) {
                Partition partition = partitions.get(0);
                partitionList = partitionList.stream().filter(a -> a.getName().contains(name) || partition.getId().equals(a.getPid())).collect(Collectors.toList());
            } else {
                partitionList = new ArrayList<>();
            }
        }
        partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        return pipeStatisticsReportService.getNrwReport("year", startDate.getYear() + "", startDate.getYear() + "", endDate.getYear() + "", tenantId, partitionIdList);
    }

    private void buildPartitionNRW(String partitionId, Partition partition, PartitionNRWDTO
            partitionNRWDTO, Map<String, BigDecimal> supplyTotalMap, Map<String, BigDecimal> inTotalMap, Map<String, BigDecimal> outTotalMap, Map<String, Integer> userNumMap, Map<String, BigDecimal> correctUseWaterMap, String
                                           date) {
        partitionNRWDTO.setPartitionId(partition.getId());
        partitionNRWDTO.setPartitionName(partition.getName());
        partitionNRWDTO.setDate(date);
        partitionNRWDTO.setUserNum(userNumMap.get(partitionId) == null ? 0 : userNumMap.get(partitionId));
        partitionNRWDTO.setSupplyTotal(supplyTotalMap.get(partitionId) == null ? BigDecimal.ZERO : supplyTotalMap.get(partitionId));
        partitionNRWDTO.setCorrectUseWater(correctUseWaterMap.get(partitionId) == null ? BigDecimal.ZERO : correctUseWaterMap.get(partitionId));
        partitionNRWDTO.setInWater(inTotalMap.get(partitionId) == null ? BigDecimal.ZERO : inTotalMap.get(partitionId));
        partitionNRWDTO.setOutWater(outTotalMap.get(partitionId) == null ? BigDecimal.ZERO : outTotalMap.get(partitionId));
        partitionNRWDTO.setLossTotal(partitionNRWDTO.getInWater().subtract(partitionNRWDTO.getOutWater()).subtract(partitionNRWDTO.getCorrectUseWater()));
        try {
            partitionNRWDTO.setNrw(partitionNRWDTO.getSupplyTotal().subtract(partitionNRWDTO.getOutWater()).subtract(partitionNRWDTO.getCorrectUseWater()).multiply(BigDecimal.valueOf(100)).divide(partitionNRWDTO.getSupplyTotal(), 2, RoundingMode.FLOOR));
        } catch (Exception e) {
            partitionNRWDTO.setNrw(BigDecimal.ZERO);
            e.printStackTrace();
        }
    }

    private void getParentIdList(List<String> parentIdList, String id) {
        Partition partition = partitionMapper.selectById(id);
        if (partition != null && StringUtils.isNotBlank(partition.getPid())) {
            this.getParentIdList(parentIdList, partition.getPid());
            parentIdList.add(partition.getPid());
        }
    }

    private void buildTree(List<PartitionTreeDTO> result) {
        PartitionTreeDTO temp;
        for (PartitionTreeDTO partitionTreeDTO : result) {
            temp = new PartitionTreeDTO();
            List<Partition> dmaPartitionEntities = partitionMapper.getAllIdNameByPid(partitionTreeDTO.getId());
            for (Partition partition : dmaPartitionEntities) {
                temp = new PartitionTreeDTO();
                temp.setId(partition.getId());
                temp.setName(partition.getName());
                temp.setType(partition.getType());
                partitionTreeDTO.getChildren().add(temp);
            }
            this.buildTree(partitionTreeDTO.getChildren());
        }
    }

    /**
     * 查找子分区和设备列表
     *
     * @param partitionTreeDTO
     * @param partitionIdList
     */
    private void buildPartitionDeviceTree(PartitionTreeDTO partitionTreeDTO, List<String> partitionIdList) {
        partitionIdList.add(partitionTreeDTO.getDeviceId());
        PartitionTreeDTO temp;
        // 分区列表
        List<Partition> dmaPartitionEntities = partitionMapper.getAllIdNameByPid(partitionTreeDTO.getDeviceId());
        for (Partition partition : dmaPartitionEntities) {
            temp = new PartitionTreeDTO();
            temp.setId(UUID.randomUUID().toString());
            temp.setDeviceId(partition.getId());
            temp.setName(partition.getName());
            temp.setType(partition.getType());
            this.buildPartitionDeviceTree(temp, partitionIdList);
            partitionTreeDTO.getChildren().add(temp);
        }
    }

    private List containsName(List<PartitionTreeDTO> mapList, String name) {

        return mapList.stream().filter(a -> {
            a.setChildren(this.containsName(a.getChildren(), name));
            if (a.getChildren().size() > 0) {
                return true;
            }

            return (a.getName().contains(name));
        }).collect(Collectors.toList());
    }
}
