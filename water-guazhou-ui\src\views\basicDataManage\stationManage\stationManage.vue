<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <el-card>
      <div class="card-content" :class="{ dark: useAppStore().isDark }">
        <StationTree
          ref="refStationTree"
          :project-id="TreeData.currentProject?.value"
          @add="handleAdd"
          @edit="handleEdit"
        >
        </StationTree>
        <StationDetail
          ref="refStationForm"
          :project-id="TreeData.currentProject?.value"
          :station-info="state.currentStation"
          @deleted="handleDeleted"
          @submited="handleSubmited"
        ></StationDetail>
      </div>
    </el-card>
  </TreeBox>
</template>
<script lang="ts" setup>
import TreeBox from '@/views/layout/treeOrDetailFrame/TreeBox.vue';
import { useAppStore, useBusinessStore } from '@/store';
import StationTree from './components/StationTree.vue';
import StationDetail from './components/StationDetail.vue';

const businessStore = useBusinessStore();
const refStationTree =
  ref<
    InstanceType<(typeof import('./components/StationTree.vue'))['default']>
  >();
const refStationForm =
  ref<
    InstanceType<(typeof import('./components/StationDetail.vue'))['default']>
  >();
const state = reactive<{
  currentStation?: NormalOption;
}>({
  currentStation: undefined
});
const TreeData = reactive<SLTreeConfig>({
  data: computed(() => businessStore.projectList) as any,
  isFilterTree: true,
  title: '区域划分',
  currentProject: businessStore.selectedProject,
  treeNodeHandleClick(params) {
    if (params.value !== TreeData.currentProject?.value) {
      TreeData.currentProject = params;
      businessStore.SET_selectedProject(params);
    }
  }
});
// 通过state.currentStation的有无来自动更新表单
// 增加删除修改表单后会自动刷新表单，触发相应事件以处理站点树
const handleAdd = () => {
  state.currentStation = undefined;
};
const handleEdit = (data: NormalOption) => {
  state.currentStation = data;
};
const handleSubmited = () => {
  refStationTree.value?.refreshTree();
};
const handleDeleted = () => {
  refStationTree.value?.refreshTree();
};
</script>

<style lang="scss" scoped>
.el-card {
  height: 100%;

  :deep(.el-card__body) {
    height: 100%;
    padding: 0;
  }
}

.card-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
</style>
