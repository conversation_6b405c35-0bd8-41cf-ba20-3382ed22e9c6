package org.thingsboard.server.dao.util.imodel.query.purchase;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchase;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class DevicePurchasePageRequest extends AdvancedPageableQueryEntity<DevicePurchase, DevicePurchasePageRequest> {
    private String id;

    // 采购单编码
    private String code;

    // 采购单标题
    private String title;

    // 采购人
    private String userId;

    // 采购部门
    private String userDepartmentId;
}
