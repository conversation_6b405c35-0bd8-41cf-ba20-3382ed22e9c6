import{d as h,a0 as k,c as d,r as l,a8 as P,g as C,h as b,F as n,q as e,i as o,p as x,aw as B,j as D,bz as g,C as E}from"./index-r0dFAfgr.js";import{_ as F}from"./index-BJ-QPYom.js";import A from"./TreeBox-mfOmxwZJ.js";import N from"./StationTree-B4CjmeX0.js";import w from"./StationDetail-BoH8Wuwr.js";import"./zhandian-YaGuQZe6.js";import"./data-CLo2TII-.js";import"./index-BggOjNGp.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./FormMap-BGaXSqQF.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";import"./sortBy-DDhdj0i5.js";import"./index-D9ERhRP6.js";const y=h({__name:"stationManage",setup(z){const i=k(),p=d(),u=d(),m=l({currentStation:void 0}),r=l({data:P(()=>i.projectList),isFilterTree:!0,title:"区域划分",currentProject:i.selectedProject,treeNodeHandleClick(t){var a;t.value!==((a=r.currentProject)==null?void 0:a.value)&&(r.currentProject=t,i.SET_selectedProject(t))}}),f=()=>{m.currentStation=void 0},_=t=>{m.currentStation=t},S=()=>{var t;(t=p.value)==null||t.refreshTree()},j=()=>{var t;(t=p.value)==null||t.refreshTree()};return(t,a)=>{const v=F,T=g;return C(),b(A,null,{tree:n(()=>[e(v,{"tree-data":o(r)},null,8,["tree-data"])]),default:n(()=>[e(T,null,{default:n(()=>{var c,s;return[x("div",{class:B(["card-content",{dark:o(D)().isDark}])},[e(N,{ref_key:"refStationTree",ref:p,"project-id":(c=o(r).currentProject)==null?void 0:c.value,onAdd:f,onEdit:_},null,8,["project-id"]),e(w,{ref_key:"refStationForm",ref:u,"project-id":(s=o(r).currentProject)==null?void 0:s.value,"station-info":o(m).currentStation,onDeleted:j,onSubmited:S},null,8,["project-id","station-info"])],2)]}),_:1})]),_:1})}}}),lr=E(y,[["__scopeId","data-v-547fbc27"]]);export{lr as default};
