package org.thingsboard.server.dao.model.sql.gis;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_GIS_PEOPLE_SETTING_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class GisPeopleSettingEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_GIS_PEOPLE_SETTING_NAME)
    private String name;

    @Column(name = ModelConstants.TYPE)
    private String type;

    @Column(name = ModelConstants.TB_GIS_PEOPLE_SETTING_ROLES)
    private String roles;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
