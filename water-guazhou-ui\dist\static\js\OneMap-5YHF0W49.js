import{d as le,cN as pe,c as W,r as ne,g as c,h as x,F as N,p as w,q as G,i as n,an as A,n as h,aJ as T,bh as I,aB as u,aw as de,G as ce,cn as B,cs as me,b as U,bB as L,bt as he,dc as $,X as fe,C as ue}from"./index-r0dFAfgr.js";import{u as ye,_ as ge}from"./useRightDrawer-DizfuIz2.js";import{u as we}from"./useHighLight-DPevRAc5.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{p as be,g as J,h as ve,s as ke}from"./FeatureHelper-Da16o0mu.js";import"./pe-B8dP0-Ut.js";import{g as V,m as _e,b as Ce}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{w as Z}from"./Point-WxyopZva.js";import{g as E,a as Me}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as q}from"./ToolHelper-BiiInOzB.js";import{b as Ne}from"./ViewHelper-BGCZjxXH.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{a as Ae}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import Te from"./RightDrawerMap-D5PhmGFO.js";import{S as b}from"./data-CLo2TII-.js";import Ie from"./OneMapMenuBar-DLrw-4TL.js";import{e as xe}from"./config-DncLSA-r.js";import"./IdentifyHelper-RJWmLn49.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-BggOjNGp.js";import"./index-DeAQQ1ej.js";const Le={class:"custom-menubar"},Oe=["onClick"],He={class:"one-map-wrapper"},Se={key:0,class:"one-map-wrapper__closed"},Pe={class:"left-main"},Ge={key:0,class:"right"},Ee={class:"right-title"},je={class:"right-main overlay-y"},De=le({__name:"OneMap",setup(Fe){const{proxy:O}=pe(),m=ye({widthOption:{opend:540,more:1540}}),y=W(),H=we(),t=ne({title:"",curPath:"",detailTitle:"",pageConfig:[],layerIds:[],layerInfos:[],menus:[],pops:[],windows:[],detailType:"",pipePickLayerid:[],ONE_MAP_CONFIG:window.SITE_CONFIG.ONE_MAP_CONFIG}),l={layers:[]},Y=e=>{var i,s,o,r;if(e.path==="sphy"){window.open(e.meta.url);return}((i=t.hMenu)==null?void 0:i.path)!==e.path&&e.path!==((s=t.hMenu)==null?void 0:s.path)&&((o=l.view)==null||o.map.removeMany(l.layers),H.destroy(),l.layers.length=0,t.pops.length=0,t.hMenu=e,(r=e.children)!=null&&r.length&&S(e.children[0],!0))},S=(e,i)=>{var o,r;t.curPath=e.path,t.title=e.meta.title,t.pipePickLayerid=t.layerInfos.filter(a=>a.layername===t.title).map(a=>a.layerid)||[];let s=l.layers.find(a=>a.id===e.path);if(!s){const a=e.path.startsWith("ssjk");s=E(l.view,{id:a?"ssjk":e.path,title:a?"站点":e.meta.title}),s&&l.layers.push(s),Q()}s&&(s.visible=i),(o=y.value)==null||o.closeAllPop(),m.drawerLevel.value==="closed"?(r=y.value)==null||r.toggleDrawer(!0):m.drawerLevel.value="opened"},j=async e=>{var s,o;if(!e)return;l.highlightGraphic=e;const i=(e==null?void 0:e.attributes.id)||"";t.detailType=((s=e==null?void 0:e.attributes)==null?void 0:s.path)||"",await L(),t.detailType&&(window.SITE_CONFIG.ONE_MAP_CONFIG.disableHoverPopup||(o=y.value)==null||o.openPop(i),m.drawerLevel.value==="more"&&P())},K=async(e,i)=>{var r;m.drawerLevel.value="more",((r=y.value)==null?void 0:r.isCustomOpened())||await $(600);const o=O.$refs["refDetail"+t.detailType];o==null||o[0].refreshDetail(l.view,e,{where:i})},X=e=>{var s,o,r,a;const i=O.$refs["refDetail"+e.path];i!=null&&i.length&&((a=(r=i[0]).refreshDetail)==null||a.call(r,(o=(s=l.highlightGraphic)==null?void 0:s.attributes)==null?void 0:o.row))},P=async(e,...i)=>{var r,a;const s=e||((a=(r=l.highlightGraphic)==null?void 0:r.attributes)==null?void 0:a.row);t.detailType==="ssjk-quanbu"&&ee(s),m.drawerLevel.value="more",await $(600),await L();const o=O.$refs["refDetail"+t.detailType];o!=null&&o.length&&o[0].refreshDetail(s,...i)},Q=()=>{H.bindHoverHighLight(l.view,l.layers,j,0)},ee=e=>{if(t.detailType==="ssjk-quanbu")switch(e==null?void 0:e.type){case b.BENGZHAN:t.detailType="ssjk-ecgs";break;case b.CHELIUYAZHAN:case b.LIULIANGJIANCEZHAN:case b.YALIJIANCEZHAN:t.detailType="ssjk-znsb";break;case b.DAYONGHU:t.detailType="ssjk-dyhjcd";break;case b.SHUICHANGE:t.detailType="ssjk-sc";break;case b.SHUIYUANDI:t.detailType="ssjk-sy";break;case b.SHUIZHIJIANCEZHAN:t.detailType="ssjk-szjcd";break}},te=async e=>{var i,s;if((i=y.value)==null||i.closeAllPop(),e.results.length){const o=e.results[0],r=o.feature,a=r.attributes.OBJECTID,p=be(r.geometry)||[];t.windows=[{visible:!1,x:p[0],y:p[1],title:o.layerName+"("+r.attributes.新编号+")",attributes:{row:r.attributes,id:a}}],await J(l.view,r,{avoidHighlight:!0,zoom:15}),(s=y.value)==null||s.openPop(a)}else U.warning("没有查询到设备")},re=()=>{Ne(l.view,async e=>{var i,s,o,r;if(((s=(i=e.results)==null?void 0:i[0])==null?void 0:s.type)==="graphic"){if(q("pointer"),!t.detailType){(o=y.value)==null||o.closeAllPop();return}await L(),P()}else q(""),(r=y.value)==null||r.closeAllPop()})},D=async(e,i,s,o)=>{if(e.path.startsWith("sbzc"))t.detailTitle=(e.meta.title||"")+(s?"("+s+")":""),t.detailType=e.path||"",K(e.meta.title,i);else if(e.path==="ywlc-xjyh")t.detailType=e.path||"",P(i,l.view,t.layerInfos);else{const r=l.layers.find(p=>e.path.startsWith("ssjk")?p.id==="ssjk":p.id===e.path),a=r==null?void 0:r.graphics.find(p=>{var d;return((d=p.attributes)==null?void 0:d.id)===i});if(!r||!a||!l.view){U.warning("暂无位置信息");return}window.SITE_CONFIG.GIS_CONFIG.gisDisableMove||await J(l.view,a,{avoidHighlight:!0,zoom:o||15}),H.highlight(l.view,a,j,0)}};let v;const ie=e=>{const i=t.curPath;if(!i)return;t.pops=t.pops.filter(p=>{var d;return((d=p.attributes.customConfig)==null?void 0:d.path)!==i});const s=(e==null?void 0:e.windows)||[];i.startsWith("ssjk")||i.startsWith("sbzc")?t.pops=s:t.pops.push(...s);const o=E(l.view,{id:i.startsWith("ssjk")?"ssjk":i});if(!o)return;o.removeAll();const r=s.filter(p=>p.symbolConfig),a=r.map(p=>{var f,g,k,_,C,M;return ve(p.x,p.y,{picUrl:((f=p.symbolConfig)==null?void 0:f.url)||Ae(),spatialReference:(g=l.view)==null?void 0:g.spatialReference,attributes:p.attributes,picSize:[((k=p.symbolConfig)==null?void 0:k.width)||20,((_=p.symbolConfig)==null?void 0:_.height)||25],xOffset:((C=p.symbolConfig)==null?void 0:C.xoffset)||0,yOffset:((M=p.symbolConfig)==null?void 0:M.yoffset)||13})});if(!t.ONE_MAP_CONFIG.hideTextSymbol)if(t.ONE_MAP_CONFIG.textSymbolAside)L(()=>{z()});else{const p=r.map(d=>{var g;return new V({geometry:new Z({x:d.x,y:d.y,spatialReference:(g=l.view)==null?void 0:g.spatialReference}),symbol:new _e({font:{size:8,weight:"bold"},borderLineColor:"rgba(213,123,11,1)",borderLineSize:1,yoffset:-15,color:"#ffffff",text:d.title,lineHeight:.5,backgroundColor:"rgba(213,123,11,0.3)"}),attributes:{notHighlight:!0}})});o.addMany(p)}o.addMany(a)},F=W(),z=()=>{var s;if(t.ONE_MAP_CONFIG.hideTextSymbol||!t.ONE_MAP_CONFIG.textSymbolAside)return;v=E(l.view,{title:"站点标线",id:"onemap-lines"}),v==null||v.removeAll();const e=27,i=((s=F.value)==null?void 0:s.map((o,r)=>{var k,_,C,M,R;const a=r<e?100:(((k=o.parentElement)==null?void 0:k.clientWidth)??300)-100-o.offsetWidth,p=(r<e?r:r-e)*(o.clientHeight+6)+70;o.style.left=a+"px",o.style.top=p+"px";const d=(_=l.view)==null?void 0:_.toMap({x:r<e?a+o.offsetWidth+20:a-20,y:o.offsetTop+o.offsetHeight/2}),f=(C=l.view)==null?void 0:C.toMap({x:r<e?a+o.offsetWidth+100:a-100,y:o.offsetTop+o.offsetHeight/2}),g=new Z({x:t.pops[r].x,y:t.pops[r].y,spatialReference:(M=l.view)==null?void 0:M.spatialReference});return new V({geometry:new Ce({paths:[[[(d==null?void 0:d.x)??0,(d==null?void 0:d.y)??0],[(f==null?void 0:f.x)??0,(f==null?void 0:f.y)??0],[g.x,g.y]]],spatialReference:(R=l.view)==null?void 0:R.spatialReference}),symbol:ke("polyline",{width:1,color:"rgba(213,123,11,1)"})})}))||[];v==null||v.addMany(i)},oe=e=>{var s,o;const i=(o=(s=t.hMenu)==null?void 0:s.children)==null?void 0:o.find(r=>r.path===e.attributes.path);i&&D(i,e.attributes.id)},ae=async()=>{var i,s;t.layerIds=Me(l.view);const e=await fe(t.layerIds);t.layerInfos=((s=(i=e.data)==null?void 0:i.result)==null?void 0:s.rows)||[]},se=e=>{var s;e==null||e.watch("extent",z),l.view=e,console.log(e==null?void 0:e.spatialReference),t.menus=((s=xe())==null?void 0:s.children)||[],t.hMenu=t.menus[0];const i=t.hMenu.children||[];S(i[0],!0),re(),ae()};return(e,i)=>{const s=ge,o=he;return c(),x(Te,{ref_key:"refMap",ref:y,title:"","hide-coords":!1,pops:n(t).pops,windows:n(t).windows,"right-drawer-absolute":n(m).drawerAbsolute.value,"right-drawer-width":n(m).dialogWidth.value,"before-collapse":n(m).beforeCollapse,"right-drawer-min-width":n(m).drawerMinWidth.value,onMapLoaded:se,onPopToggle:i[2]||(i[2]=(r,a)=>r.visible=a)},{"map-bars":N(()=>{var r;return[w("div",Le,[G(Ie,{menus:n(t).menus,onChange:Y,onItemClick:S},null,8,["menus"])]),((r=n(t).hMenu)==null?void 0:r.path)==="sbzc"?(c(),x(s,{key:0,"add-to-view":!0,"auto-start":!0,"layer-ids":n(t).pipePickLayerid,onPicked:te},null,8,["layer-ids"])):A("",!0),n(t).ONE_MAP_CONFIG.textSymbolAside?(c(!0),h(u,{key:1},T(n(t).pops,(a,p)=>(c(),h("div",{ref_for:!0,ref_key:"refLabels",ref:F,key:p,class:"station-label",onClick:()=>oe(a)},I(a.title),9,Oe))),128)):A("",!0)]}),"detail-header":N(()=>[w("span",null,I(n(t).detailTitle),1)]),"detail-default":N(()=>i[3]||(i[3]=[])),default:N(()=>[w("div",He,[w("div",{class:de(["left overlay-y",n(m).drawerLevel.value])},[n(m).drawerLevel.value==="closed"?(c(),h("div",Se,I(n(t).title),1)):(c(),h(u,{key:1},[G(o,{size:"large",type:"simple",class:"left-title"},{default:N(()=>[ce(I(n(t).title),1)]),_:1}),w("div",Pe,[(c(!0),h(u,null,T(n(t).menus,r=>(c(),h(u,{key:r.path},[(c(!0),h(u,null,T(r.children,a=>(c(),h(u,{key:a.path},[a.path===n(t).curPath&&a.component?(c(),x(B(a.component),{key:0,menu:a,view:l.view,onHighlightMark:D,onAddMarks:ie},null,40,["menu","view"])):A("",!0)],64))),128))],64))),128))])],64))],2),n(m).drawerLevel.value==="more"?(c(),h("div",Ge,[w("div",Ee,[w("span",null,I(n(t).detailTitle),1),G(n(me),{icon:"mdi:close",class:"more-close",onClick:i[0]||(i[0]=r=>n(m).drawerLevel.value="opened")})]),w("div",je,[(c(!0),h(u,null,T(n(t).menus,r=>(c(),h(u,{key:r.path},[(c(!0),h(u,null,T(r.children,a=>(c(),h(u,{key:a.path},[n(t).detailType===a.path&&a.detailComponent?(c(),x(B(a.detailComponent),{key:0,ref_for:!0,ref:"refDetail"+a.path,onMounted:p=>X(a),onRefresh:i[1]||(i[1]=p=>n(t).detailTitle=p.title)},null,40,["onMounted"])):A("",!0)],64))),128))],64))),128))])])):A("",!0)])]),_:1},8,["pops","windows","right-drawer-absolute","right-drawer-width","before-collapse","right-drawer-min-width"])}}}),Xr=ue(De,[["__scopeId","data-v-7d1fdeea"]]);export{Xr as default};
