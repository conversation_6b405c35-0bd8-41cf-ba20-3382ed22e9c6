package org.thingsboard.server.controller.maintainCircuit.circuit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.circuit.CircuitTaskCService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskC;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/circuit/task/c")
public class CircuitTaskCController extends BaseController {

    @Autowired
    private CircuitTaskCService circuitTaskCService;

    @PostMapping
    public IstarResponse reviewer(@RequestBody CircuitTaskC circuitTaskC) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        boolean checkUser = circuitTaskCService.checkUser(circuitTaskC.getMainId(), userId);
        if (!checkUser) {
            return IstarResponse.error("您没有该任务的巡检权限");
        }
        circuitTaskCService.save(circuitTaskC);
        return IstarResponse.ok("保存成功");
    }


    /**
     * 保养信息
     * @param deviceLabelCode
     * @return
     */
    @GetMapping("statistics/{deviceLabelCode}")
    public IstarResponse statistics(@PathVariable String deviceLabelCode) {
        return IstarResponse.ok(circuitTaskCService.statistics(deviceLabelCode));
    }
}
