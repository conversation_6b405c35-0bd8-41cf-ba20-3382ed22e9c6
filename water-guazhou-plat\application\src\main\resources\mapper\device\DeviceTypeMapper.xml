<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           serial_id,
                           "name",
                           order_num,
                           remark,
                           "level",
                           parent_id,
                           creator,
                           create_time,
                           update_time,
                           tenant_id,
                           model,
                           device_name,
                           "label",
                           unit,
                           maintenance_cycle,
                           min_stock,
                           images,
                           files<!--@sql from m_device_type -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.deviceManage.DeviceType">
        <result column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="name" property="name"/>
        <result column="order_num" property="orderNum"/>
        <result column="remark" property="remark"/>
        <result column="level" property="level"/>
        <result column="parent_id" property="parentId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="model" property="model"/>
        <result column="device_name" property="deviceName"/>
        <result column="label" property="label"/>
        <result column="unit" property="unit"/>
        <result column="maintenance_cycle" property="maintenanceCycle"/>
        <result column="min_stock" property="minStock"/>
        <result column="images" property="images"/>
        <result column="files" property="files"/>
    </resultMap>
    <select id="findAllConditional" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from m_device_type
        <where>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="serialId != null and serialId != ''">
                and serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
            order by order_num
        </where>
    </select>

    <select id="findRoots" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from m_device_type
        where parent_id is null
          and tenant_id = #{tenantId}
        order by order_num
    </select>

    <select id="findChildren" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from m_device_type
        where parent_id = #{parentId}
        order by order_num
    </select>

    <update id="update">
        update m_device_type
        <set>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="level != null">
                level = #{level},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            update_time = #{updateTime},

            <!--      以下为属性      -->
            <if test="model != null">
                model = #{model},
            </if>
            <if test="deviceName != null">
                device_name = #{deviceName},
            </if>
            <if test="label != null">
                label = #{label},
            </if>
            <if test="unit != null">
                unit = #{unit},
            </if>
            <if test="maintenanceCycle != null">
                maintenance_cycle = #{maintenanceCycle},
            </if>
            <if test="minStock != null">
                min_stock = #{minStock},
            </if>
            <if test="images != null">
                images = #{images},
            </if>
            <if test="files != null">
                files = #{files},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getNameById" resultType="java.lang.String">
        select name
        from m_device_type
        where id = #{id}
    </select>

    <select id="getTreePathMap" resultType="java.util.Map">
        with recursive path(id, path) as (select id, (name)::text
                                          from m_device_type
                                          where parent_id is null
                                          union all
                                          select org.id, path.path || '>' || org.name
                                          from path,
                                               m_device_type org
                                          where path.id = org.parent_id)
        select *
        from path;
    </select>

    <select id="getMainTypeName" resultType="java.lang.String">
        select name
        from m_device_type
        where serial_id = #{serialId} and tenant_id = #{tenantId}
    </select>

    <select id="isSerialIdExists" resultType="boolean">
        select device_type_is_serial_id_exists(#{serialId}, #{id}, #{tenantId})
    </select>

    <select id="getDepthBySerialId" resultType="int">
        select level
        from m_device_type
        where serial_id = #{serialId} and tenant_id = #{tenantId}
    </select>
    <!--@formatter:off-->
    <select id="canBeDelete" resultType="boolean">
        select
        <!--下有类型-->
        (select count(1) = 0 from m_device_type where tenant_id = #{tenantId} and parent_id = #{id}) and
        <!--下有属性-->
        (select count(1) = 0 from m_device_type_attr where tenant_id = #{tenantId} and
        serial_id = (select m_device_type.serial_id from m_device_type where id = #{id}) ) and
        <!--下有设备-->
        (select count(1) = 0 from m_device where tenant_id = #{tenantId} and substr(serial_id, 0, 9) =
                                    substr((select m_device_type.serial_id from m_device_type where id = #{id}), 0, 9))
    </select>
    <!--@formatter:on-->
    <select id="countDeviceByType" resultType="java.lang.String">
        with result as (select type.name typeName, count(1) amount
                        from m_device_type type
                                 left join m_device device
                                           on device.type_id = type.id
                                 left join device_storage_journal storage
                                           on storage.serial_id = device.serial_id
                        where type.tenant_id = #{tenantId}
                          and type.tenant_id = #{tenantId}
                          and storage.tenant_id = #{tenantId}
                        group by type.name)
        select '{' || rtrim(string_agg('"' || typeName || '":' || amount, ','), ',') || '}'
        from result
    </select>
</mapper>