package org.thingsboard.server.dao.fileRegistry;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.dao.model.sql.fileRegistry.FileRegistry;
import org.thingsboard.server.dao.sql.FileRegistryMapper;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.FileRegistryPageRequest;
import org.thingsboard.server.dao.util.imodel.query.FileRegistrySaveRequest;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class FileRegistryServiceImpl implements FileRegistryService {
    @Autowired
    private FileRegistryMapper mapper;

    @Autowired
    private FileRegistryPropertyPath fileRegistryProperty;


    @Override
    public IPage<FileRegistry> findAllConditional(FileRegistryPageRequest request) {
        IPage<FileRegistry> page = mapper.findByPage(request);

        // 处理内部文件注册信息
        for (FileRegistry registry : page.getRecords()) {
            completeAddress(registry);
        }

        return page;
    }

    @Override
    public FileRegistry save(FileRegistrySaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(FileRegistry entity) {
        return mapper.updateSelective(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<FileRegistry> saveAll(List<FileRegistrySaveRequest> constructionFiles) {
        return QueryUtil.saveOrUpdateBatchByRequest(constructionFiles, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean removeAll(String label, String host) {
        return mapper.removeAll(label, host);
    }

    @Override
    public List<FileRegistry> replace(List<FileRegistrySaveRequest> req) {
        if (req.size() == 0)
            return Collections.emptyList();
        FileRegistrySaveRequest firstReq = req.stream().findFirst().get();
        removeAll(firstReq.getLabel(), firstReq.getHost());

        return QueryUtil.saveBatchByRequest(req, mapper::saveAll);
    }

    @Override
    public FileRegistry upload(String id, String host, String label, MultipartFile file, String tenantId) {
        String fileName = file.getOriginalFilename();
        String fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);

        String directoryPath = fileRegistryProperty.resolveDirectoryPath(host, label);
        String registryFileName = StringUtils.generateRandomFileName(fileSuffix);
        String registryFilePath = directoryPath + registryFileName;
        File dirFile = Paths.get(directoryPath).toFile();
        if (!dirFile.exists() && !dirFile.mkdirs()) {
            log.error("Error to create FileRegistry root path: " + dirFile);
            ExceptionUtils.silentThrow("创建文件存储路径失败");
        }
        try (
                InputStream stream = file.getInputStream();
                FileOutputStream os = new FileOutputStream(registryFilePath);
        ) {
            IOUtils.copy(stream, os);

            FileRegistrySaveRequest registry = new FileRegistrySaveRequest();
            registry.setId(id);
            registry.setFileName(fileName);
            registry.setFileAddress(fileRegistryProperty.resolveRelativeDirectoryPath(host, label) + registryFileName);
            registry.setLabel(label);
            registry.setHost(host);
            registry.tenantId(tenantId);

            FileRegistry rawRegistryInfo = save(registry);
            completeAddress(rawRegistryInfo);
            return rawRegistryInfo;
        } catch (IOException e) {
            log.error("Fail to upload file", e);
            ExceptionUtils.silentThrow("上传文件失败，请查看后台日志。");
        }

        return null;
    }

    @Override
    public FileRegistry getById(String id) {
        return mapper.selectById(id);
    }

    private void completeAddress(FileRegistry registry) {
        if (registry == null || registry.getFileAddress() == null || registry.getFileAddress().startsWith("http")) {
            return;
        }

        registry.setFileAddress(fileRegistryProperty.getBaseURL() + registry.getFileAddress());
    }

}
