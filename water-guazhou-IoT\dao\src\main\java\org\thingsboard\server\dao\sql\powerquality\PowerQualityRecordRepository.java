package org.thingsboard.server.dao.sql.powerquality;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.PowerQualityRecordEntity;

import java.util.List;

public interface PowerQualityRecordRepository extends JpaRepository<PowerQualityRecordEntity, String> {


    @Query("SELECT new PowerQualityRecordEntity(p.id, p.title, p.time, p.deviceId, p.deviceName, p.projectId, p.projectName, p.gatewayId, p.name) " +
            "FROM PowerQualityRecordEntity p " +
            "WHERE p.projectId = ?1 AND p.time > ?2 AND p.time < ?3")
    List<PowerQualityRecordEntity> selectByStartTimeAndEndTime(String projectId, Long startTime, Long endTime);

    @Query("SELECT new PowerQualityRecordEntity(p.id, p.title, p.time, p.deviceId, p.deviceName, p.projectId, p.projectName, p.powerQualityDetail, p.gatewayId, p.name) " +
            "FROM PowerQualityRecordEntity p " +
            "WHERE p.deviceId = ?1 AND p.title like %?2%")
    List<PowerQualityRecordEntity> getByDeviceIdAndDate(String deviceId, String date);
}
