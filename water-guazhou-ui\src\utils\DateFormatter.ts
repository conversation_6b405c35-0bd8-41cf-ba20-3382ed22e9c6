import moment from 'moment'

/**
 * 描述时间，转换成带单位的时间
 * @param val
 * @returns xx秒、xx小时、xx分、xx天
 */
export const DescribeDate = (val: any) => {
  const timeLast: number = moment(val).valueOf() / 1000
  if (timeLast < 60) return `${timeLast.toFixed(0)}秒`
  if (timeLast < 3600) return `${(timeLast / 60).toFixed(0)}分`
  if (timeLast < 3600 * 24) return `${(timeLast / 3600).toFixed(0)}小时`
  return `${(timeLast / 3600 / 24).toFixed(0)}天`
}

/**
 * 格式化时间
 * @param val
 * @param format 'Y-M-D h:m:s
 * @returns
 */
export const DateFormatter = (val: string | Date, format?: string) => {
  if (!val) return '-'
  if (!format) return moment(val).format('YYYY-MM-DD HH:mm:ss')
  format = format.replace('Y', 'YYYY')
  format = format.replace('M', 'MM')
  format = format.replace('D', 'DD')
  format = format.replace('H', 'HH')
  format = format.replace('m', 'mm')
  format = format.replace('s', 'ss')
  return moment(val).format(format)
}
/**
 * 格式化时间
 * @param val 要格式化的时间 string|number|Date
 * @param format 'YYYY-MM-DD
 * @returns 格式化后的时间
 */
export const formatDate = (val?: string | number | Date, format?: string) => {
  if (!val) return val
  if (!format) format = 'YYYY-MM-DD HH:mm:ss'
  return moment(val).format(format.replace('h', 'H'))
}
/**
 * 获取日期对应的星期几
 * @param date 任何moment能识别的时间，不传默认当前日期
 * @param prefix 前缀，默认：星期
 */
export const getWeek = (date?: any, prefix = '星期') => {
  // 参数时间戳
  const week = moment(date).weekday()
  switch (week) {
    case 1:
      return prefix + '一'
    case 2:
      return prefix + '二'
    case 3:
      return prefix + '三'
    case 4:
      return prefix + '四'
    case 5:
      return prefix + '五'
    case 6:
      return prefix + '六'
    case 0:
      return prefix + '日'
    default:
      return ''
  }
}
