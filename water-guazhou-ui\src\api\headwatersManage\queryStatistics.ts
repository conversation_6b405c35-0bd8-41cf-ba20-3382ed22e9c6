// 智慧生产=水源管理-查询统计 api
import request from '@/plugins/axios'

// 查询数据对比
export function getDataCompare(params: {
    attributes: string,
    queryType: string,
    start: string,
    end: string
}) {
  return request({
    url: '/istar/api/station/data/getDataCompare',
    method: 'get',
    params
  })
}

// 周期数据对比
export function getCycleCompare(params: {
    attributes: string,
    queryType: string,
    start: string,
    end: string
}) {
  return request({
    url: '/istar/api/station/data/getCycleCompare',
    method: 'get',
    params
  })
}

// 导出
export function exportDataCompare(params: {
  attributes: string,
  queryType: string,
  start: string,
  end: string
}) {
  return request({
    url: '/istar/api/station/data/getDataCompare/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
