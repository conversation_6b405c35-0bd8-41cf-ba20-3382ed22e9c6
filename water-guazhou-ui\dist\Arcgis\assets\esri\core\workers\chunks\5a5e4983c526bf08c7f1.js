"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5103],{92835:(e,t,r)=>{r.d(t,{Z:()=>f});var i,s=r(43697),n=r(96674),o=r(70586),a=r(35463),l=r(5600),h=(r(75215),r(67676),r(71715)),c=r(52011),u=r(30556);let p=i=class extends n.wq{static get allTime(){return d}static get empty(){return g}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(i.allTime)}get isEmpty(){return this.equals(i.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new i({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,o.pC)(this.start)?this.start.getTime():this.start,r=(0,o.pC)(this.end)?this.end.getTime():this.end,i=(0,o.pC)(e.start)?e.start.getTime():e.start,s=(0,o.pC)(e.end)?e.end.getTime():e.end;return t===i&&r===s}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,o.yw)(this.start,(t=>(0,a.JE)(t,e))),r=(0,o.yw)(this.end,(t=>{const r=(0,a.JE)(t,e);return t.getTime()===r.getTime()?r:(0,a.Nm)(r,1,e)}));return new i({start:t,end:r})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return i.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,o.R2)(this.start,-1/0,(e=>e.getTime())),r=(0,o.R2)(this.end,1/0,(e=>e.getTime())),s=(0,o.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,o.R2)(e.end,1/0,(e=>e.getTime()));let a,l;if(s>=t&&s<=r?a=s:t>=s&&t<=n&&(a=t),r>=s&&r<=n?l=r:n>=t&&n<=r&&(l=n),null!=a&&null!=l&&!isNaN(a)&&!isNaN(l)){const e=new i;return e.start=a===-1/0?null:new Date(a),e.end=l===1/0?null:new Date(l),e}return i.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const r=new i,{start:s,end:n}=this;return(0,o.pC)(s)&&(r.start=(0,a.Nm)(s,e,t)),(0,o.pC)(n)&&(r.end=(0,a.Nm)(n,e,t)),r}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return d.clone();const t=(0,o.pC)(this.start)&&(0,o.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,r=(0,o.pC)(this.end)&&(0,o.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new i({start:t,end:r})}};(0,s._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"end",void 0),(0,s._)([(0,h.r)("end")],p.prototype,"readEnd",null),(0,s._)([(0,u.c)("end")],p.prototype,"writeEnd",null),(0,s._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isAllTime",null),(0,s._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],p.prototype,"isEmpty",null),(0,s._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],p.prototype,"start",void 0),(0,s._)([(0,h.r)("start")],p.prototype,"readStart",null),(0,s._)([(0,u.c)("start")],p.prototype,"writeStart",null),p=i=(0,s._)([(0,c.j)("esri.TimeExtent")],p);const d=new p,g=new p({start:void 0,end:void 0}),f=p},4757:(e,t,r)=>{r.d(t,{Z:()=>d});var i=r(43697),s=r(15923),n=r(2368),o=r(10699),a=r(96674),l=r(70586),h=r(5600),c=(r(75215),r(67676),r(52011));let u=0,p=class extends((0,a.eC)((0,n.J)((0,o.IG)(s.Z)))){constructor(e){super(e),this.id=`${Date.now().toString(16)}-analysis-${u++}`,this.title=null}get parent(){return this._get("parent")}set parent(e){const t=this.parent;if((0,l.pC)(t))switch(t.type){case"line-of-sight":case"dimension":t.releaseAnalysis(this);break;case"2d":case"3d":t.analyses.includes(this)&&t.analyses.remove(this)}this._set("parent",e)}get isEditable(){return this.requiredPropertiesForEditing.every(l.pC)}};(0,i._)([(0,h.Cb)({type:String,constructOnly:!0,clonable:!1})],p.prototype,"id",void 0),(0,i._)([(0,h.Cb)({type:String})],p.prototype,"title",void 0),(0,i._)([(0,h.Cb)({constructOnly:!0})],p.prototype,"type",void 0),(0,i._)([(0,h.Cb)({clonable:!1,value:null})],p.prototype,"parent",null),(0,i._)([(0,h.Cb)({readOnly:!0})],p.prototype,"isEditable",null),(0,i._)([(0,h.Cb)({readOnly:!0})],p.prototype,"requiredPropertiesForEditing",void 0),p=(0,i._)([(0,c.j)("esri.analysis.Analysis")],p);const d=p},46791:(e,t,r)=>{r.d(t,{Z:()=>T});var i,s=r(43697),n=r(3894),o=r(32448),a=r(22974),l=r(70586),h=r(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(i||(i={}));var c,u=r(1654),p=r(5600),d=r(75215),g=r(52421),f=r(52011),m=r(58971),y=r(10661);const _=new h.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),v=()=>{};function b(e){return e?e instanceof I?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function w(e){if(e&&e.length)return e[0]}function S(e,t,r,i){const s=Math.min(e.length-r,t.length-i);let n=0;for(;n<s&&e[r+n]===t[i+n];)n++;return n}function E(e,t,r,i){t&&t.forEach(((t,s,n)=>{e.push(t),E(e,r.call(i,t,s,n),r,i)}))}const O=new Set,A=new Set,M=new Set,C=new Map;let L=0,I=c=class extends o.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof c}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new y.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:L++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof c?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,m.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(i.ADD)||(this._splice(0,this.length,b(e)),this._emitAfterChanges(i.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,r={removed:!1,callback:t};return e.push(r),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=v,r.removed=!0,e.splice(e.indexOf(r),1)}}}return this._emitter.on(e,t)}once(e,t){const r=this.on(e,t);return{remove(){r.remove()}}}add(e,t){if((0,m.it)(this._observable),this._emitBeforeChanges(i.ADD))return this;const r=this.getNextIndex(t??null);return this._splice(r,0,[e]),this._emitAfterChanges(i.ADD),this}addMany(e,t=this._items.length){if((0,m.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(i.ADD))return this;const r=this.getNextIndex(t);return this._splice(r,0,b(e)),this._emitAfterChanges(i.ADD),this}at(e){if((0,m.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(i.REMOVE),e}clone(){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.map(a.d9)})}concat(...e){(0,m.it)(this._observable);const t=e.map(b);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const r=(0,l.j0)(this._splice(0,this.length)),s=r.length;for(let i=0;i<s;i++)e.call(t,r[i],i,r);this._emitAfterChanges(i.REMOVE)}every(e,t){return(0,m.it)(this._observable),this._items.every(e,t)}filter(e,t){let r;return(0,m.it)(this._observable),r=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:r})}find(e,t){return(0,m.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,m.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,m.it)(this._observable);const r=[];return E(r,this,e,t),new c(r)}forEach(e,t){return(0,m.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,m.it)(this._observable),this._items[e]}getNextIndex(e){(0,m.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,m.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,m.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,m.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,m.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,m.it)(this._observable);const r=this._items.map(e,t);return new c({items:r})}reorder(e,t=this.length-1){(0,m.it)(this._observable);const r=this.indexOf(e);if(-1!==r){if(t<0?t=0:t>=this.length&&(t=this.length-1),r!==t){if(this._emitBeforeChanges(i.MOVE))return e;this._splice(r,1),this._splice(t,0,[e]),this._emitAfterChanges(i.MOVE)}return e}}pop(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const e=w(this._splice(this.length-1,1));return this._emitAfterChanges(i.REMOVE),e}push(...e){return(0,m.it)(this._observable),this._emitBeforeChanges(i.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(i.ADD)),this.length}reduce(e,t){(0,m.it)(this._observable);const r=this._items;return 2===arguments.length?r.reduce(e,t):r.reduce(e)}reduceRight(e,t){(0,m.it)(this._observable);const r=this._items;return 2===arguments.length?r.reduceRight(e,t):r.reduceRight(e)}remove(e){return(0,m.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,m.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(i.REMOVE))return;const t=w(this._splice(e,1));return this._emitAfterChanges(i.REMOVE),t}removeMany(e){if((0,m.it)(this._observable),!e||!e.length||this._emitBeforeChanges(i.REMOVE))return[];const t=e instanceof c?e.toArray():e,r=this._items,s=[],n=t.length;for(let e=0;e<n;e++){const i=t[e],n=r.indexOf(i);if(n>-1){const i=1+S(t,r,e+1,n+1),o=this._splice(n,i);o&&o.length>0&&s.push.apply(s,o),e+=i-1}}return this._emitAfterChanges(i.REMOVE),s}reverse(){if((0,m.it)(this._observable),this._emitBeforeChanges(i.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(i.MOVE),this}shift(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const e=w(this._splice(0,1));return this._emitAfterChanges(i.REMOVE),e}slice(e=0,t=this.length){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,m.it)(this._observable),this._items.some(e,t)}sort(e){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(i.MOVE))return this;const t=(0,l.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(i.MOVE),this}splice(e,t,...r){(0,m.it)(this._observable);const s=(t?i.REMOVE:0)|(r.length?i.ADD:0);if(this._emitBeforeChanges(s))return[];const n=this._splice(e,t,r)||[];return this._emitAfterChanges(s),n}toArray(){return(0,m.it)(this._observable),this._items.slice()}toJSON(){return(0,m.it)(this._observable),this.toArray()}toLocaleString(){return(0,m.it)(this._observable),this._items.toLocaleString()}toString(){return(0,m.it)(this._observable),this._items.toString()}unshift(...e){return(0,m.it)(this._observable),!e.length||this._emitBeforeChanges(i.ADD)||(this._splice(0,0,e),this._emitAfterChanges(i.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,r){const i=this._items,s=this.itemType;let n,o;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,u.Os)((()=>this._dispatchChange()))),t){if(o=i.splice(e,t),this.hasEventListener("before-remove")){const t=_.acquire();t.target=this,t.cancellable=!0;for(let r=0,s=o.length;r<s;r++)n=o[r],t.reset(n),this.emit("before-remove",t),t.defaultPrevented&&(o.splice(r,1),i.splice(e,0,n),e+=1,r-=1,s-=1);_.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=_.acquire();e.target=this,e.cancellable=!1;const t=o.length;for(let r=0;r<t;r++)e.reset(o[r]),this.emit("after-remove",e);_.release(e)}}if(r&&r.length){if(s){const e=[];for(const t of r){const r=s.ensureType(t);null==r&&null!=t||e.push(r)}r=e}const t=this.hasEventListener("before-add"),n=this.hasEventListener("after-add"),o=e===this.length;if(t||n){const s=_.acquire();s.target=this,s.cancellable=!0;const a=_.acquire();a.target=this,a.cancellable=!1;for(const l of r)t?(s.reset(l),this.emit("before-add",s),s.defaultPrevented||(o?i.push(l):i.splice(e++,0,l),this._set("length",i.length),n&&(a.reset(l),this.emit("after-add",a)))):(o?i.push(l):i.splice(e++,0,l),this._set("length",i.length),a.reset(l),this.emit("after-add",a));_.release(a),_.release(s)}else{if(o)for(const e of r)i.push(e);else i.splice(e,0,...r);this._set("length",i.length)}}return(r&&r.length||o&&o.length)&&this._notifyChangeEvent(r,o),o}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const r=_.acquire();r.target=this,r.cancellable=!0,r.type=e,this.emit("before-changes",r),t=r.defaultPrevented,_.release(r)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=_.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),_.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;O.clear(),A.clear(),M.clear();for(const{added:t,removed:r}of e){if(t)if(0===M.size&&0===A.size)for(const e of t)O.add(e);else for(const e of t)A.has(e)?(M.add(e),A.delete(e)):M.has(e)||O.add(e);if(r)if(0===M.size&&0===O.size)for(const e of r)A.add(e);else for(const e of r)O.has(e)?O.delete(e):(M.delete(e),A.add(e))}const r=n.Z.acquire();O.forEach((e=>{r.push(e)}));const i=n.Z.acquire();A.forEach((e=>{i.push(e)}));const s=this._items,o=t.items,a=n.Z.acquire();if(M.forEach((e=>{o.indexOf(e)!==s.indexOf(e)&&a.push(e)})),t.listeners&&(r.length||i.length||a.length)){const e={target:this,added:r,removed:i,moved:a},s=t.listeners.length;for(let r=0;r<s;r++){const i=t.listeners[r];i.removed||i.callback.call(this,e)}}n.Z.release(r),n.Z.release(i),n.Z.release(a)}O.clear(),A.clear(),M.clear()}};I.ofType=e=>{if(!e)return c;if(C.has(e))return C.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const r in e.typeMap){const i=e.typeMap[r].prototype.declaredClass;t?t+=` | ${i}`:t=i}let r=class extends c{};return(0,s._)([(0,g.c)({Type:e,ensureType:"function"==typeof e?(0,d.se)(e):(0,d.N7)(e)})],r.prototype,"itemType",void 0),r=(0,s._)([(0,f.j)(`esri.core.Collection<${t}>`)],r),C.set(e,r),r},(0,s._)([(0,p.Cb)()],I.prototype,"length",void 0),(0,s._)([(0,p.Cb)()],I.prototype,"items",null),I=c=(0,s._)([(0,f.j)("esri.core.Collection")],I);const T=I},16453:(e,t,r)=>{r.d(t,{R:()=>v,w:()=>b});var i=r(43697),s=r(15923),n=r(70586),o=r(41103),a=r(22974),l=r(31263);class h{constructor(){this._propertyOriginMap=new Map,this._originStores=new Array(l.kk),this._values=new Map,this.multipleOriginsSupported=!0}clone(e){const t=new h,r=this._originStores[l.s3.DEFAULTS];r&&r.forEach(((e,r)=>{t.set(r,(0,a.d9)(e),l.s3.DEFAULTS)}));for(let r=l.s3.SERVICE;r<l.kk;r++){const i=this._originStores[r];i&&i.forEach(((i,s)=>{e&&e.has(s)||t.set(s,(0,a.d9)(i),r)}))}return t}get(e,t){const r=void 0===t?this._values:this._originStores[t];return r?r.get(e):void 0}keys(e){const t=null==e?this._values:this._originStores[e];return t?[...t.keys()]:[]}set(e,t,r=l.s3.USER){let i=this._originStores[r];if(i||(i=new Map,this._originStores[r]=i),i.set(e,t),!this._values.has(e)||(0,n.j0)(this._propertyOriginMap.get(e))<=r){const i=this._values.get(e);return this._values.set(e,t),this._propertyOriginMap.set(e,r),i!==t}return!1}delete(e,t=l.s3.USER){const r=this._originStores[t];if(!r)return;const i=r.get(e);if(r.delete(e),this._values.has(e)&&this._propertyOriginMap.get(e)===t){this._values.delete(e);for(let r=t-1;r>=0;r--){const t=this._originStores[r];if(t&&t.has(e)){this._values.set(e,t.get(e)),this._propertyOriginMap.set(e,r);break}}}return i}has(e,t){const r=void 0===t?this._values:this._originStores[t];return!!r&&r.has(e)}revert(e,t){for(;t>0&&!this.has(e,t);)--t;const r=this._originStores[t],i=r&&r.get(e),s=this._values.get(e);return this._values.set(e,i),this._propertyOriginMap.set(e,t),s!==i}originOf(e){return this._propertyOriginMap.get(e)||l.s3.DEFAULTS}forEach(e){this._values.forEach(e)}}var c=r(50549),u=r(1153),p=r(52011);const d=e=>{let t=class extends e{constructor(...e){super(...e);const t=(0,n.j0)((0,u.vw)(this)),r=t.store,i=new h;t.store=i,(0,o.M)(t,r,i)}read(e,t){(0,c.i)(this,e,t)}getAtOrigin(e,t){const r=g(this),i=(0,l.M9)(t);if("string"==typeof e)return r.get(e,i);const s={};return e.forEach((e=>{s[e]=r.get(e,i)})),s}originOf(e){return(0,l.x3)(this.originIdOf(e))}originIdOf(e){return g(this).originOf(e)}revert(e,t){const r=g(this),i=(0,l.M9)(t),s=(0,u.vw)(this);let n;n="string"==typeof e?"*"===e?r.keys(i):[e]:e,n.forEach((e=>{s.invalidate(e),r.revert(e,i),s.commit(e)}))}};return t=(0,i._)([(0,p.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],t),t};function g(e){return(0,u.vw)(e).store}let f=class extends(d(s.Z)){};f=(0,i._)([(0,p.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],f);var m=r(76169);const y=e=>{let t=class extends e{constructor(...e){super(...e)}clear(e,t="user"){return _(this).delete(e,(0,l.M9)(t))}write(e={},t){return(0,m.c)(this,e=e||{},t),e}setAtOrigin(e,t,r){(0,u.vw)(this).setAtOrigin(e,t,(0,l.M9)(r))}removeOrigin(e){const t=_(this),r=(0,l.M9)(e),i=t.keys(r);for(const e of i)t.originOf(e)===r&&t.set(e,t.get(e,r),l.s3.USER)}updateOrigin(e,t){const r=_(this),i=(0,l.M9)(t),s=this.get(e);for(let t=i+1;t<l.kk;++t)r.delete(e,t);r.set(e,s,i)}toJSON(e){return this.write({},e)}};return t=(0,i._)([(0,p.j)("esri.core.WriteableMultiOriginJSONSupport")],t),t.prototype.toJSON.isDefaultToJSON=!0,t};function _(e){return(0,u.vw)(e).store}const v=e=>{let t=class extends(y(d(e))){constructor(...e){super(...e)}};return t=(0,i._)([(0,p.j)("esri.core.MultiOriginJSONSupport")],t),t};let b=class extends(v(s.Z)){};b=(0,i._)([(0,p.j)("esri.core.MultiOriginJSONSupport")],b)},52421:(e,t,r)=>{function i(e){return(t,r)=>{t[r]=e}}r.d(t,{c:()=>i})},70921:(e,t,r)=>{r.d(t,{R:()=>n,Z:()=>s});var i=r(46791);function s(e,t,r=i.Z){return t||(t=new r),t===e||(t.removeAll(),(s=e)&&(Array.isArray(s)||"items"in s&&Array.isArray(s.items))?t.addMany(e):e&&t.add(e)),t;var s}function n(e){return e}},17445:(e,t,r)=>{r.d(t,{N1:()=>p,YP:()=>l,Z_:()=>f,gx:()=>h,nn:()=>m,on:()=>u,tX:()=>y});var i=r(91460),s=r(50758),n=r(70586),o=r(95330),a=r(26258);function l(e,t,r={}){return c(e,t,r,d)}function h(e,t,r={}){return c(e,t,r,g)}function c(e,t,r={},i){let s=null;const o=r.once?(e,r)=>{i(e)&&((0,n.hw)(s),t(e,r))}:(e,r)=>{i(e)&&t(e,r)};if(s=(0,a.aQ)(e,o,r.sync,r.equals),r.initial){const t=e();o(t,t)}return s}function u(e,t,r,o={}){let a=null,h=null,c=null;function u(){a&&h&&(h.remove(),o.onListenerRemove?.(a),a=null,h=null)}function p(e){o.once&&o.once&&(0,n.hw)(c),r(e)}const d=l(e,((e,r)=>{u(),(0,i.vT)(e)&&(a=e,h=(0,i.on)(e,t,p),o.onListenerAdd?.(e))}),{sync:o.sync,initial:!0});return c=(0,s.kB)((()=>{d.remove(),u()})),c}function p(e,t){return function(e,t,r){if((0,o.Hc)(r))return Promise.reject((0,o.zE)());const i=e();if(t?.(i))return Promise.resolve(i);let a=null;function l(){a=(0,n.hw)(a)}return new Promise(((i,n)=>{a=(0,s.AL)([(0,o.fu)(r,(()=>{l(),n((0,o.zE)())})),c(e,(e=>{l(),i(e)}),{sync:!1,once:!0},t??d)])}))}(e,g,t)}function d(e){return!0}function g(e){return!!e}r(87538);const f={sync:!0},m={initial:!0},y={sync:!0,initial:!0}},35463:(e,t,r)=>{r.d(t,{JE:()=>o,Nm:()=>n,rJ:()=>a}),r(80442);const i={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},s={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,r){const i=new Date(e.getTime());if(t&&r){const e=s[r],{getter:n,setter:o,multiplier:a}=e;if("months"===r){const e=function(e,t){const r=new Date(e,t+1,1);return r.setDate(0),r.getDate()}(i.getFullYear(),i.getMonth()+t);i.getDate()>e&&i.setDate(e)}i[o](i[n]()+t*a)}return i}function o(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function a(e,t,r){return 0===e?0:e*i[t]/i[r]}},38009:(e,t,r)=>{r.d(t,{q:()=>d});var i=r(43697),s=r(20102),n=r(17452),o=r(5600),a=(r(75215),r(67676),r(52011)),l=r(30556),h=r(50549),c=r(76169);const u={"web-scene/operational-layers":{ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0,ArcGISTiledElevationServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BuildingSceneLayer:!0,GroupLayer:!0,IntegratedMeshLayer:!0,OGCFeatureLayer:!0,PointCloudLayer:!0,WebTiledLayer:!0,CSV:!0,GeoJSON:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,KML:!0,RasterDataLayer:!0,Voxel:!0,LineOfSightLayer:!0},"web-scene/basemap":{ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,WebTiledLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,ArcGISImageServiceLayer:!0,WMS:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0},"web-scene/ground":{ArcGISTiledElevationServiceLayer:!0,RasterDataElevationLayer:!0},"web-map/operational-layers":{ArcGISAnnotationLayer:!0,ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISStreamLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BingMapsAerial:!0,BingMapsHybrid:!0,BingMapsRoad:!0,CSV:!0,GeoRSS:!0,GeoJSON:!0,GroupLayer:!0,KML:!0,MediaLayer:!0,OGCFeatureLayer:!0,OrientedImageryLayer:!0,SubtypeGroupLayer:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,WebTiledLayer:!0},"web-map/basemap":{ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,WMS:!0,WebTiledLayer:!0,BingMapsAerial:!0,BingMapsRoad:!0,BingMapsHybrid:!0},"web-map/tables":{ArcGISFeatureLayer:!0},"portal-item/operational-layers":{ArcGISFeatureLayer:!0,ArcGISSceneServiceLayer:!0,PointCloudLayer:!0,BuildingSceneLayer:!0,IntegratedMeshLayer:!0,OrientedImageryLayer:!0}};var p=r(21506);const d=e=>{let t=class extends e{constructor(){super(...arguments),this.title=null}writeListMode(e,t,r,i){(i&&"ground"===i.layerContainerType||e&&(0,c.d)(this,r,{},i))&&(t[r]=e)}writeOperationalLayerType(e,t,r,i){!e||i&&"tables"===i.layerContainerType||(t.layerType=e)}writeTitle(e,t){t.title=e??"Layer"}read(e,t){t&&(t.layer=this),(0,h.$)(this,e,(t=>super.read(e,t)),t)}write(e,t){if(t?.origin){const e=`${t.origin}/${t.layerContainerType||"operational-layers"}`,r=u[e];let i=r&&r[this.operationalLayerType];if("ArcGISTiledElevationServiceLayer"===this.operationalLayerType&&"web-scene/operational-layers"===e&&(i=!1),"ArcGISDimensionLayer"===this.operationalLayerType&&"web-map/operational-layers"===e&&(i=!1),!i)return t.messages?.push(new s.Z("layer:unsupported",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e}'`,{layer:this})),null}const r=super.write(e,{...t,layer:this}),i=!!t&&!!t.messages&&!!t.messages.filter((e=>e instanceof s.Z&&"web-document-write:property-required"===e.name)).length;return(0,n.jc)(r?.url)?(t?.messages?.push(new s.Z("layer:invalid-url",`Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' using a Blob URL cannot be written to web scenes and web maps`,{layer:this})),null):!this.url&&i?null:r}beforeSave(){}};return(0,i._)([(0,o.Cb)({type:String,json:{write:{ignoreOrigin:!0},origins:{"web-scene":{write:{isRequired:!0,ignoreOrigin:!0}},"portal-item":{write:!1}}}})],t.prototype,"id",void 0),(0,i._)([(0,o.Cb)(p.rT)],t.prototype,"listMode",void 0),(0,i._)([(0,l.c)("listMode")],t.prototype,"writeListMode",null),(0,i._)([(0,o.Cb)({type:String,readOnly:!0,json:{read:!1,write:{target:"layerType",ignoreOrigin:!0},origins:{"portal-item":{write:!1}}}})],t.prototype,"operationalLayerType",void 0),(0,i._)([(0,l.c)("operationalLayerType")],t.prototype,"writeOperationalLayerType",null),(0,i._)([(0,o.Cb)(p.Oh)],t.prototype,"opacity",void 0),(0,i._)([(0,o.Cb)({type:String,json:{write:{ignoreOrigin:!0,writerEnsuresNonNull:!0},origins:{"web-scene":{write:{isRequired:!0,ignoreOrigin:!0,writerEnsuresNonNull:!0}},"portal-item":{write:!1}}},value:"Layer"})],t.prototype,"title",void 0),(0,i._)([(0,l.c)("title"),(0,l.c)(["web-scene"],"title")],t.prototype,"writeTitle",null),(0,i._)([(0,o.Cb)({type:Boolean,json:{name:"visibility"}})],t.prototype,"visible",void 0),t=(0,i._)([(0,a.j)("esri.layers.mixins.OperationalLayer")],t),t}},21506:(e,t,r)=>{r.d(t,{qG:()=>w,PV:()=>m,id:()=>E,iR:()=>d,rn:()=>f,rT:()=>M,u1:()=>A,rO:()=>O,Oh:()=>v,bT:()=>b,C_:()=>p,Lx:()=>y,vg:()=>S,YI:()=>u,HQ:()=>g});var i=r(92835),s=r(6570),n=r(82971),o=r(25929),a=r(70586),l=(r(95330),r(35463)),h=r(86787),c=r(65242);const u={type:Boolean,value:!0,json:{origins:{service:{read:!1,write:!1},"web-map":{read:!1,write:!1}},name:"screenSizePerspective",write:!0}},p={type:Boolean,value:!0,json:{name:"disablePopup",read:{reader:(e,t)=>!t.disablePopup},write:{enabled:!0,writer(e,t,r){t[r]=!e}}}},d={type:Boolean,value:!0,nonNullable:!0,json:{name:"showLabels",write:!0}},g={type:String,json:{origins:{"portal-item":{write:!1}},write:{isRequired:!0,ignoreOrigin:!0,writer:o.w}}},f={type:Boolean,value:!0,nonNullable:!0,json:{origins:{service:{read:{enabled:!1}}},name:"showLegend",write:!0}},m={value:null,type:h.Z,json:{origins:{service:{name:"elevationInfo",write:!0}},name:"layerDefinition.elevationInfo",write:!0}};function y(e){return{type:e,readOnly:!0,json:{origins:{service:{read:!0}},read:!1}}}const _={write:!0,read:!0},v={type:Number,json:{origins:{"web-document":_,"portal-item":{write:!0}}}},b={...v,json:{...v.json,origins:{"web-document":{..._,write:{enabled:!0,target:{opacity:{type:Number},"layerDefinition.drawingInfo.transparency":{type:Number}}}}},read:{source:["layerDefinition.drawingInfo.transparency","drawingInfo.transparency"],reader:(e,t,r)=>r&&"service"!==r.origin||!t.drawingInfo||void 0===t.drawingInfo.transparency?t.layerDefinition&&t.layerDefinition.drawingInfo&&void 0!==t.layerDefinition.drawingInfo.transparency?(0,c.b)(t.layerDefinition.drawingInfo.transparency):void 0:(0,c.b)(t.drawingInfo.transparency)}}},w={type:i.Z,readOnly:!0,get(){if(!this.layer?.timeInfo)return null;const{datesInUnknownTimezone:e,timeOffset:t,useViewTime:r}=this.layer,s=this.view?.timeExtent;let n=this.layer.timeExtent;e&&(n=function(e){if(!e)return e;const{start:t,end:r}=e;return new i.Z({start:(0,a.pC)(t)?(0,l.Nm)(t,t.getTimezoneOffset(),"minutes"):t,end:(0,a.pC)(r)?(0,l.Nm)(r,r.getTimezoneOffset(),"minutes"):r})}(n));let o=r?s&&n?s.intersection(n):s||n:n;if(!o||o.isEmpty||o.isAllTime)return o;t&&(o=o.offset(-t.value,t.unit)),e&&(o=function(e){if(!e)return e;const{start:t,end:r}=e;return new i.Z({start:(0,a.pC)(t)?(0,l.Nm)(t,-t.getTimezoneOffset(),"minutes"):t,end:(0,a.pC)(r)?(0,l.Nm)(r,-r.getTimezoneOffset(),"minutes"):r})}(o));const h=this._get("timeExtent");return o.equals(h)?h:o}},S={type:s.Z,readOnly:!0,json:{origins:{service:{read:{source:["fullExtent","spatialReference"],reader:(e,t)=>{const r=s.Z.fromJSON(e);return null!=t.spatialReference&&"object"==typeof t.spatialReference&&(r.spatialReference=n.Z.fromJSON(t.spatialReference)),r}}}},read:!1}},E={type:String,json:{origins:{service:{read:!1},"portal-item":{read:!1}}}},O={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:"layerDefinition.minScale"},write:{target:"layerDefinition.minScale"}}},A={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:"layerDefinition.maxScale"},write:{target:"layerDefinition.maxScale"}}},M={json:{write:{ignoreOrigin:!0},origins:{"web-map":{read:!1,write:!1}}}}},99282:(e,t,r)=>{r.d(t,{a:()=>n});var i=r(67900),s=r(68441);const n={inches:(0,i.En)(1,"meters","inches"),feet:(0,i.En)(1,"meters","feet"),"us-feet":(0,i.En)(1,"meters","us-feet"),yards:(0,i.En)(1,"meters","yards"),miles:(0,i.En)(1,"meters","miles"),"nautical-miles":(0,i.En)(1,"meters","nautical-miles"),millimeters:(0,i.En)(1,"meters","millimeters"),centimeters:(0,i.En)(1,"meters","centimeters"),decimeters:(0,i.En)(1,"meters","decimeters"),meters:(0,i.En)(1,"meters","meters"),kilometers:(0,i.En)(1,"meters","kilometers"),"decimal-degrees":1/(0,i.ty)(1,"meters",s.sv.radius)}},86787:(e,t,r)=>{r.d(t,{Z:()=>b});var i,s=r(43697),n=r(35454),o=r(96674),a=r(70586),l=r(5600),h=(r(75215),r(67676),r(71715)),c=r(52011),u=r(30556),p=r(35671);let d=i=class extends o.wq{constructor(e){super(e)}async collectRequiredFields(e,t){return(0,p.io)(e,t,this.expression)}clone(){return new i({expression:this.expression,title:this.title})}equals(e){return this.expression===e.expression&&this.title===e.title}};(0,s._)([(0,l.Cb)({type:String,json:{write:!0}})],d.prototype,"expression",void 0),(0,s._)([(0,l.Cb)({type:String,json:{write:!0}})],d.prototype,"title",void 0),d=i=(0,s._)([(0,c.j)("esri.layers.support.FeatureExpressionInfo")],d);const g=d;var f,m=r(12541);const y=(0,n.w)()({onTheGround:"on-the-ground",relativeToGround:"relative-to-ground",relativeToScene:"relative-to-scene",absoluteHeight:"absolute-height"}),_=new n.X({foot:"feet",kilometer:"kilometers",meter:"meters",mile:"miles","us-foot":"us-feet",yard:"yards"});let v=f=class extends o.wq{constructor(e){super(e),this.offset=null}readFeatureExpressionInfo(e,t){return null!=e?e:t.featureExpression&&0===t.featureExpression.value?{expression:"0"}:void 0}writeFeatureExpressionInfo(e,t,r,i){t[r]=e.write({},i),"0"===e.expression&&(t.featureExpression={value:0})}get mode(){const{offset:e,featureExpressionInfo:t}=this;return this._isOverridden("mode")?this._get("mode"):(0,a.pC)(e)||t?"relative-to-ground":"on-the-ground"}set mode(e){this._override("mode",e)}set unit(e){this._set("unit",e)}write(e,t){return this.offset||this.mode||this.featureExpressionInfo||this.unit?super.write(e,t):null}clone(){return new f({mode:this.mode,offset:this.offset,featureExpressionInfo:this.featureExpressionInfo?this.featureExpressionInfo.clone():void 0,unit:this.unit})}equals(e){return this.mode===e.mode&&this.offset===e.offset&&this.unit===e.unit&&(0,a._W)(this.featureExpressionInfo,e.featureExpressionInfo)}};(0,s._)([(0,l.Cb)({type:g,json:{write:!0}})],v.prototype,"featureExpressionInfo",void 0),(0,s._)([(0,h.r)("featureExpressionInfo",["featureExpressionInfo","featureExpression"])],v.prototype,"readFeatureExpressionInfo",null),(0,s._)([(0,u.c)("featureExpressionInfo",{featureExpressionInfo:{type:g},"featureExpression.value":{type:[0]}})],v.prototype,"writeFeatureExpressionInfo",null),(0,s._)([(0,l.Cb)({type:y.apiValues,nonNullable:!0,json:{type:y.jsonValues,read:y.read,write:{writer:y.write,isRequired:!0}}})],v.prototype,"mode",null),(0,s._)([(0,l.Cb)({type:Number,json:{write:!0}})],v.prototype,"offset",void 0),(0,s._)([(0,l.Cb)({type:m.f9,json:{type:String,read:_.read,write:_.write}})],v.prototype,"unit",null),v=f=(0,s._)([(0,c.j)("esri.layers.support.ElevationInfo")],v);const b=v},12541:(e,t,r)=>{r.d(t,{Z7:()=>s,f9:()=>n});var i=r(99282);function s(e){return 1/(i.a[e]||1)}const n=function(){const e=Object.keys(i.a);return e.sort(),e}()},65242:(e,t,r)=>{r.d(t,{a:()=>s,b:()=>n});var i=r(75215);function s(e){const t=(0,i.vU)(100*(1-e));return Math.max(0,Math.min(t,100))}function n(e){const t=1-e/100;return Math.max(0,Math.min(t,1))}}}]);