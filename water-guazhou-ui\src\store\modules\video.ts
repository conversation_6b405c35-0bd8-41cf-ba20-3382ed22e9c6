import { defineStore } from 'pinia';

export const videoStore = defineStore('video', {
  state: (): {
    videoList: {
      status: boolean;
      url?: string;
      talkurl?: string | null;
      type?: string;
      id?: string;
      key: string | null;
    }[];
    group: number;
    total: number;
  } => ({
    // 视频列表
    videoList: [
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null },
      { status: false, url: '', talkurl: '', type: 'customHls', key: null }
    ],
    // 视频分组
    group: 8,
    // 视频总数
    total: 9
  }),
  actions: {
    putValue() {
      return {
        videoList: this.videoList,
        group: this.group,
        total: this.total
      };
    },
    setValue(val) {
      this.videoList = val.videoList;
      this.total = val.total;
      this.group = val.group;
    }
  }
});
