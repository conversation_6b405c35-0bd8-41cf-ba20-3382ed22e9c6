<template>
  <SLCard title=" ">
    <template #title>
      <div class="card-header">
        <div class="left">
          <Icon icon="material-symbols:water-drop-outline" /><span
            >漏水排行榜</span
          >
        </div>
      </div>
    </template>
    <div ref="refDiv" class="chart-box">
      <VChart ref="refChart" :option="state.lsphOption" />
    </div>
  </SLCard>
</template>
<script lang="ts" setup>
import { graphic } from 'echarts';
import { Icon } from '@iconify/vue';
import { GetPartitionRefLeakSort } from '@/api/mapservice/dma';
import { useAppStore } from '@/store';
import { useDetector } from '@/hooks/echarts';

const state = reactive<{
  lsphOption: any;
}>({
  lsphOption: null
});
const appStore = useAppStore();
const generateSupplyOption = (xData: number[], yData: any[]) => {
  const max = Math.max(...xData) || 100;
  const isDark = appStore.isDark;
  const list = yData.map((item, index) => {
    const obj = {
      name: item,
      value: xData[index],
      num: xData[index]
    };
    return obj;
  });

  const list1 = yData.map((item, index) => {
    const obj = {
      name: item,
      value: max,
      label: {
        show: true,
        position: 'right',
        fontSize: 14,
        color: isDark ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.8)',
        offset: [16, 0],
        formatter() {
          return xData[index];
        }
      }
    };
    return obj;
  });
  const list2 = yData.map((item, index) => {
    const obj = {
      name: item,
      value: xData[index],
      label: xData[index]
    };
    return obj;
  });
  const options = {
    tooltip: {
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      left: 10,
      right: 80,
      bottom: 0,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      axisTick: {
        show: false
      },
      position: 'top'
    },
    yAxis: {
      type: 'category',
      data: yData,
      inverse: true, // 倒叙
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: isDark ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)',
          fontSize: 14,
          fontFamily: 'TencentSans'
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true, // 隐藏或显示（true）组件
        backgroundColor: 'rgba(0,0,0,0)', // 组件的背景颜色。
        fillerColor: isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.6)', // 选中范围的填充颜色。
        borderColor: 'rgb(0,0,0,0.25)', // 边框颜色
        showDetail: false, // 是否显示detail，即拖拽时候显示详细数值信息
        startValue: 0, // 数据窗口范围的起始数值
        endValue: 5, // 数据窗口范围的结束数值（一页显示多少条数据）
        yAxisIndex: [0], // 控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
        filterMode: 'empty',
        width: 8, // 滚动条高度
        right: 3, // 距离右边
        handleSize: 0, // 控制手柄的尺寸
        zoomLoxk: true, // 是否锁定选择区域（或叫做数据窗口）的大小
        top: 10,
        height: '90%'
      },
      {
        // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        yAxisIndex: [0, 1], // 控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
        zoomOnMouseWheel: false, // 滚轮是否触发缩放
        moveOnMouseMove: true, // 鼠标移动能否触发平移
        moveOnMouseWheel: true // 鼠标滚轮能否触发平移
      }
    ],

    series: [
      {
        type: 'bar',
        barGap: '-100%',
        barWidth: 14,
        z: 1,
        itemStyle: {
          color: new graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: 'rgba(0,255,255,1)'
              },
              {
                offset: 1,
                color: 'rgba(255,0,0,1)'
              }
            ],
            false
          )
        },
        data: list
      },
      {
        type: 'bar',
        barWidth: 14,
        z: 0,
        itemStyle: {
          color: isDark ? 'rgba(26, 49, 99, 1)' : 'rgba(26, 49, 99, 0.1)'
        },
        tooltip: {
          show: false
        },
        data: list1
      },
      {
        type: 'pictorialBar',
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        z: 2,
        symbolClip: true,
        symbolSize: [1, 14],
        symbolPosition: 'start',
        itemStyle: {
          color: isDark ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.6)'
        },
        data: list2
      }
    ]
  };
  return options;
};
const refreshData = () => {
  GetPartitionRefLeakSort()
    .then((res) => {
      const { x, y } = res.data.data || {};
      state.lsphOption = generateSupplyOption(y || [], x || []);
    })
    .catch(() => {
      state.lsphOption = generateSupplyOption([], []);
    });
};
watch(
  () => appStore.isDark,
  () => {
    refreshData();
  }
);
const detector = useDetector();
const refChart = ref();
const refDiv = ref();
onMounted(() => {
  refreshData();
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize();
  });
});
</script>
<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: 100%;
}
.card-header {
  display: flex;
  align-items: center;
  word-break: keep-all;
  justify-content: space-between;
  width: 100%;
  .left {
    display: flex;
    align-items: center;
  }
  :deep(.el-form-item--default) {
    margin-bottom: 0;
  }
}
</style>
