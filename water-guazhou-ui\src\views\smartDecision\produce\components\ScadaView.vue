<template>
  <div class="item_center_big_bg">
    <iframe
      ref="iframe"
      frameborder="0"
      scrolling="auto"
      :src="url"
      width="100%"
      height="100%"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
import useStation from '@/hooks/station/useStation'

const stations = useStation()
const url = ref<string>('')
const refreshUrl = async () => {
  await stations.getStationOption('水厂', undefined, true)
  url.value = stations.StationList.value[0]?.data.scadaUrl
}
onMounted(() => {
  refreshUrl()
})
</script>

<style lang="scss" scoped>
.item_center_big_bg {
  width: 900px;
  height: 655px;
}
</style>
