package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractAmend;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoConstructionContractAmendSaveRequest extends SaveRequest<SoConstructionContractAmend> {
    // 所属合同编号
    @NotNullOrEmpty
    private String contractCode;

    // 所属工程编号
    @NotNullOrEmpty
    private String constructionCode;

    // 变更日期
    private Date amendDate;

    // 变更原因
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoConstructionContractAmend build() {
        SoConstructionContractAmend entity = new SoConstructionContractAmend();
        entity.setContractCode(contractCode);
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionContractAmend update(String id) {
        SoConstructionContractAmend entity = new SoConstructionContractAmend();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionContractAmend entity) {
        entity.setAmendDate(amendDate);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
    }
}