package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class GuardArrangePartnerPageRequest extends PageableQueryEntity<GuardArrangePartner> {
    // 排班id，多个用逗号隔开
    @NotNullOrEmpty
    private String arrangeId;
    @NotNullOrEmpty
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date beginTime;
    @NotNullOrEmpty
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date endTime;

}
