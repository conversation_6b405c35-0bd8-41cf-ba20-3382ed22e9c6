import{M as k,w as f,i as F,s as D,e as m,y as v,a as U}from"./Point-WxyopZva.js";import{y as b,bk as I,bl as L,b as z,bm as P,bn as M,g as G,bo as O,bp as j,bq as C,br as W}from"./MapView-DaoQedLH.js";import{c as X,f as Y,P as S,l as q,w as B}from"./widget-BcWKanF2.js";import{aW as V,T as u,R as g,aX as R,aY as J}from"./index-r0dFAfgr.js";import{h as T,j as H,l as K,m as N}from"./automaticLengthMeasurementUtils-DljoUgEz.js";import{h as Q}from"./GraphicsLayer-DTrBRwJQ.js";import{f as Z,g as $,V as tt,i as et,u as it,j as st,k as ot,n as rt}from"./AnimatedLinesLayer-B2VbV4jv.js";import{h as nt,D as at}from"./GraphicMover-B3yTDkky.js";import{r as ht,n as pt}from"./TranslateTooltipInfos-D4yK3rJA.js";import{u as ct}from"./automaticAreaMeasurementUtils-DfgMw58X.js";import"./pe-B8dP0-Ut.js";import"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./GraphicManipulator-HH4WUKex.js";import"./drapedUtils-DJwxIB1g.js";import"./triangle-lwOWqU0w.js";import"./lineSegment-DQ0q5UHF.js";class lt{constructor(e,i,s){this.graphic=e,this.mover=i,this.selected=s,this.type="reshape-start"}}class dt{constructor(e,i,s){this.graphic=e,this.mover=i,this.selected=s,this.type="reshape"}}class mt{constructor(e,i,s){this.graphic=e,this.mover=i,this.selected=s,this.type="reshape-stop"}}class vt{constructor(e,i,s){this.mover=e,this.dx=i,this.dy=s,this.type="move-start"}}class yt{constructor(e,i,s){this.mover=e,this.dx=i,this.dy=s,this.type="move"}}class _t{constructor(e,i,s){this.mover=e,this.dx=i,this.dy=s,this.type="move-stop"}}class gt{constructor(e){this.added=e,this.type="vertex-select"}}class ut{constructor(e){this.removed=e,this.type="vertex-deselect"}}class xt{constructor(e,i,s,o){this.added=e,this.graphic=i,this.oldGraphic=s,this.vertices=o,this.type="vertex-add"}}class ft{constructor(e,i,s,o){this.removed=e,this.graphic=i,this.oldGraphic=s,this.vertices=o,this.type="vertex-remove"}}const y=Z.reshapeGraphics,E={vertices:{default:new b({style:"circle",size:y.vertex.size,color:y.vertex.color,outline:{color:y.vertex.outlineColor,width:1}}),hover:new b({style:"circle",size:y.vertex.hoverSize,color:y.vertex.hoverColor,outline:{color:y.vertex.hoverOutlineColor,width:1}}),selected:new b({style:"circle",size:y.selected.size,color:y.selected.color,outline:{color:y.selected.outlineColor,width:1}})},midpoints:{default:new b({style:"circle",size:y.midpoint.size,color:y.midpoint.color,outline:{color:y.midpoint.outlineColor,width:1}}),hover:new b({style:"circle",size:y.midpoint.size,color:y.midpoint.color,outline:{color:y.midpoint.outlineColor,width:1}})}};let d=class extends X.EventedAccessor{constructor(t){super(t),this._activeOperationInfo=null,this._editGeometryOperations=null,this._handles=new k,this._graphicAttributes={esriSketchTool:"box"},this._mover=null,this._snappingContext=null,this._snappingTask=null,this._stagedVertex=null,this._tooltip=null,this._viewHandles=new k,this.callbacks={onReshapeStart(){},onReshape(){},onReshapeStop(){},onMoveStart(){},onMove(){},onMoveStop(){},onGraphicClick(){}},this.enableMidpoints=!0,this.enableMovement=!0,this.enableVertices=!0,this.graphic=null,this.layer=null,this.midpointGraphics=new I,this.midpointSymbol=new b({style:"circle",size:6,color:[200,200,200],outline:{color:[100,100,100],width:1}}),this.selectedVertices=[],this.snappingManager=null,this.tooltipOptions=new $,this.type="reshape",this.vertexGraphics=new I,this.view=null}initialize(){const t=this.view;this._highlightHelper=new nt({view:t}),this._setup(),this._handles.add([Y(()=>t==null?void 0:t.ready,()=>{const{layer:e,view:i}=this;rt(i,e),this._viewHandles.add(i.on("key-down",s=>this._keyDownHandler(s),W.TOOL))},{once:!0,initial:!0}),S(()=>this.graphic,()=>this.refresh()),S(()=>this.layer,(e,i)=>{i&&(this._clearSelection(),this._resetGraphics(i)),this.refresh()}),S(()=>this.enableMidpoints,()=>this.refresh()),q(()=>this.tooltipOptions.enabled,e=>{this._tooltip=e?new N({view:this.view}):V(this._tooltip)},B)])}destroy(){var t;this._reset(),(t=this._mover)==null||t.destroy(),this._mover=null,this._tooltip=V(this._tooltip),this._handles=V(this._handles),this._viewHandles=V(this._viewHandles)}set highlightsEnabled(t){var e;(e=this._highlightHelper)==null||e.removeAll(),this._set("highlightsEnabled",t),this._setUpHighlights()}get state(){const t=!!this.get("view.ready"),e=!(!this.get("graphic")||!this.get("layer"));return t&&e?"active":t?"ready":"disabled"}set symbols(t){const{midpoints:e=E.midpoints,vertices:i=E.vertices}=t||{};this._set("symbols",{midpoints:e,vertices:i})}isUIGraphic(t){const e=[];return this.graphic&&e.push(this.graphic),e.concat(this.vertexGraphics.items,this.midpointGraphics.items),e.length>0&&e.includes(t)}refresh(){this._reset(),this._setup()}reset(){this.graphic=null}clearSelection(){this._clearSelection()}removeSelectedVertices(){this.selectedVertices.length&&this._removeVertices(this.selectedVertices)}_setup(){const{graphic:t,layer:e}=this;if(!e||!t||u(t.geometry))return;const i=t.geometry;i.type!=="mesh"&&i.type!=="extent"?(i.type==="polygon"&&L(i),this._setUpHighlights(),this._setupGraphics(),this._setupMover()):this._logGeometryTypeError()}_setUpHighlights(){this.highlightsEnabled&&this.graphic&&this._highlightHelper.add(this.graphic)}_setUpGeometryHelper(){const t=this.graphic.geometry;if(u(t)||t.type==="mesh"||t.type==="extent")return void this._logGeometryTypeError();const e=t.type==="multipoint"?new z({paths:t.points,spatialReference:t.spatialReference}):t;this._editGeometryOperations=tt.fromGeometry(e,P.Local)}_saveSnappingContextForHandle(t,e){var i;this._snappingGraphicsLayer=new Q({listMode:"hide",internal:!0,title:"Reshape snapping layer"}),this.view.map.layers.add(this._snappingGraphicsLayer),this._snappingContext=new et({editGeometryOperations:this._editGeometryOperations,elevationInfo:{mode:"on-the-ground",offset:0},pointer:((i=e.viewEvent)==null?void 0:i.pointerType)||"mouse",excludeFeature:this.graphic,visualizer:new it(this._snappingGraphicsLayer),vertexHandle:this._getVertexFromEditGeometry(t)})}_reset(){this._clearSelection(),this._highlightHelper.removeAll(),this._updateTooltip(),this._resetGraphics(),this._resetSnappingStateVars(),this._activeOperationInfo=null,this._mover&&this._mover.destroy(),this._mover=null,this.view.cursor="default"}_resetSnappingStateVars(){var t;g(this.snappingManager)&&this.snappingManager.doneSnapping(),g(this._snappingGraphicsLayer)&&((t=this.view)!=null&&t.map&&this.view.map.layers.remove(this._snappingGraphicsLayer),this._snappingGraphicsLayer.destroy()),this._editGeometryOperations=V(this._editGeometryOperations),this._snappingTask=R(this._snappingTask),this._snappingTask=null,this._snappingContext=null,this._stagedVertex=null}_resetGraphics(t){this._removeMidpointGraphics(t),this._removeVertexGraphics(t),this._set("selectedVertices",[])}_removeMidpointGraphics(t){const e=t||this.layer;e&&e.removeMany(this.midpointGraphics.items),this.midpointGraphics.items.forEach(i=>i.destroy()),this.midpointGraphics.removeAll()}_removeVertexGraphics(t){const e=t||this.layer;e&&e.removeMany(this.vertexGraphics.items),this.vertexGraphics.items.forEach(i=>i.destroy()),this.vertexGraphics.removeAll()}_getCoordinatesForUI(t){const e=M(t.clone());if(t.type==="polygon")for(const i of e){const s=i[i.length-1];i[0][0]===s[0]&&i[0][1]===s[1]&&i.length>2&&i.pop()}return e}_setupGraphics(){const t=this.graphic.geometry;if(g(t)&&(t.type==="polyline"||t.type==="polygon")){const e=this._getCoordinatesForUI(t);this.enableMidpoints&&this._setUpMidpointGraphics(e),this.enableVertices&&this._setUpVertexGraphics(e)}}_setUpMidpointGraphics(t){this._removeMidpointGraphics();const e=this._createMidpointGraphics(t);this.midpointGraphics.addMany(e),this.layer.addMany(e)}_setUpVertexGraphics(t){this._removeVertexGraphics();const e=this._createVertexGraphics(t);this.vertexGraphics.addMany(e),this._storeRelatedVertexIndices(),this.layer.addMany(e)}_createVertexGraphics(t){const{_graphicAttributes:e,symbols:i,view:{spatialReference:s}}=this,o=[];return t==null||t.forEach((n,r)=>{n.forEach((h,p)=>{var l;const[a,c]=h;o.push(new G({geometry:new f({x:a,y:c,spatialReference:s}),symbol:(l=i==null?void 0:i.vertices)==null?void 0:l.default,attributes:{...e,pathIndex:r,pointIndex:p}}))})}),o}_createMidpointGraphics(t){const{_graphicAttributes:e,symbols:i,view:{spatialReference:s}}=this,o=[];return t==null||t.forEach((n,r)=>{n.forEach((h,p)=>{const[a,c]=h,l=n[p+1]?p+1:0;if(J(this.graphic.geometry,"type")==="polygon"||l!==0){const[_,x]=n[l],[w,A]=O([a,c],[_,x]);o.push(new G({geometry:new f({x:w,y:A,spatialReference:s}),symbol:i.midpoints.default,attributes:{...e,pathIndex:r,pointIndexStart:p,pointIndexEnd:l}}))}})}),o}_storeRelatedVertexIndices(){const t=this.vertexGraphics.items;if(!t)return;const e=t.map(({geometry:i})=>({x:i.x,y:i.y}));for(let i=0;i<e.length;i++){const s=[];for(let o=0;o<e.length;o++){if(i===o)continue;const n=e[i],r=e[o];n.x===r.x&&n.y===r.y&&s.push(o)}t[i].attributes.relatedGraphicIndices=s}}_setupMover(){const{enableMovement:t,graphic:e,midpointGraphics:i,vertexGraphics:s,view:o}=this,n=s.concat(i).items;t&&n.push(e),this._mover=new at({enableMoveAllGraphics:!1,highlightsEnabled:!1,indicatorsEnabled:!1,graphics:n,view:o,callbacks:{onGraphicClick:r=>this._onGraphicClickCallback(r),onGraphicMoveStart:r=>this._onGraphicMoveStartCallback(r),onGraphicMove:r=>this._onGraphicMoveCallback(r),onGraphicMoveStop:r=>this._onGraphicMoveStopCallback(r),onGraphicPointerOver:r=>this._onGraphicPointerOverCallback(r),onGraphicPointerOut:r=>this._onGraphicPointerOutCallback(r)}})}_onGraphicClickCallback(t){t.viewEvent.stopPropagation();const e=t.graphic;if(e===this.graphic)this.clearSelection(),this.emit("graphic-click",t),this.callbacks.onGraphicClick&&this.callbacks.onGraphicClick(t);else if(this._isMidpoint(e)){if(t.viewEvent.button===2)return;const i=this.graphic.clone(),s=this._createVertexFromMidpoint(e);this.refresh(),this._emitVertexAddEvent([e],i,s)}else this._isVertex(e)&&(t.viewEvent.stopPropagation(),t.viewEvent.button===2?this._removeVertices(e):(t.viewEvent.native.shiftKey||this._clearSelection(),this.selectedVertices.includes(e)?this._removeFromSelection(e,!0):this._addToSelection(e)))}_setUpOperation(t){const{graphic:e,dx:i,dy:s}=t,o=e===this.graphic;this._resetSnappingStateVars(),this._setUpGeometryHelper(),this._saveSnappingContextForHandle(e,t),this._activeOperationInfo={target:this.graphic,mover:e,operationType:o?"move":"reshape",totalDx:i,totalDy:s}}_onGraphicMoveStartCallback(t){const{dx:e,dy:i,graphic:s}=t;if(s===this.graphic){const{geometry:o}=s;return this._setUpOperation(t),this._emitMoveStartEvent(e,i),void(g(o)&&o.type==="point"&&this._onHandleMove(s,e,i,t,()=>{this._updateTooltip(this.graphic,t.viewEvent),this._emitMoveEvent(e,i)}))}if(!this.selectedVertices.includes(s)){if(this._clearSelection(),this._isMidpoint(s)){const o=this.graphic.clone(),n=this._createVertexFromMidpoint(s);this._emitVertexAddEvent([s],o,n)}this._addToSelection(s)}this._setUpOperation(t),this._emitReshapeStartEvent(s),this._onHandleMove(s,e,i,t,()=>{this._updateTooltip(s,t.viewEvent),this._emitReshapeEvent(s)})}_onGraphicMoveCallback(t){const e=this._activeOperationInfo;if(!e)return;const{dx:i,dy:s,graphic:o}=t;e.totalDx+=i,e.totalDy+=s;const{operationType:n}=e,{geometry:r}=o;if(!u(r)){if(n!=="move")this._onHandleMove(o,i,s,t,()=>{this._updateTooltip(o,t.viewEvent),this._emitReshapeEvent(o)});else if(r.type==="point")this._onHandleMove(o,i,s,t,()=>{this._updateTooltip(this.graphic,t.viewEvent),this._emitMoveEvent(i,s)});else if(r.type==="polyline"||r.type==="polygon"){const h=this._getCoordinatesForUI(r);this._updateVertexGraphicLocations(h),this._updateTooltip(this.graphic,t.viewEvent),this._emitMoveEvent(i,s)}}}_onGraphicMoveStopCallback(t){const e=this._activeOperationInfo;if(!e)return;const{dx:i,dy:s,graphic:o}=t,{operationType:n}=e;e.totalDx+=i,e.totalDy+=s,this._onHandleMove(o,i,s,t,()=>n==="move"?this._emitMoveStopEvent():this._emitReshapeStopEvent(o)),this._isMidpoint(o)?this.refresh():(this._updateTooltip(this._isVertex(o)?o:null),this._resetSnappingStateVars(),this._activeOperationInfo=null)}_updateVertexGraphicLocations(t){const e=this.view.spatialReference;for(const i of this.vertexGraphics){const{pathIndex:s,pointIndex:o}=i.attributes,[n,r]=t[s][o];i.geometry=new f({x:n,y:r,spatialReference:e})}this._updateMidpointGraphicLocations(t)}_updateMidpointGraphicLocations(t){for(const e of this.midpointGraphics){const{pathIndex:i,pointIndexStart:s,pointIndexEnd:o}=e.attributes,[n,r]=t[i][s],[h,p]=t[i][o],[a,c]=O([n,r],[h,p]);e.geometry=new f({x:a,y:c,spatialReference:this.view.spatialReference})}}_getIndicesForVertexGraphic({attributes:t}){return[(t==null?void 0:t.pathIndex)||0,(t==null?void 0:t.pointIndex)||0]}_getVertexFromEditGeometry(t){const[e,i]=this._getIndicesForVertexGraphic(t);return this._editGeometryOperations.data.components[e].vertices[i]}_onHandleMove(t,e,i,s,o){R(this._snappingTask);const n=this._snappingContext;if(!n)return;const r=t.geometry,h=s.type==="graphic-move-stop";if(g(this.snappingManager)&&this.selectedVertices.length<2&&!h){const p=this.snappingManager;this._stagedVertex=p.update({point:r,context:n}),this._syncGeometryAfterVertexMove(t,new f(this._stagedVertex),e,i,h),o(),this._snappingTask=j(async a=>{const c=await p.snap({point:r,context:n,signal:a});c.valid&&(this._stagedVertex=c.apply(),this._syncGeometryAfterVertexMove(t,new f(this._stagedVertex),e,i,h),o())})}else{const p=g(this._stagedVertex)?new f(this._stagedVertex):r;this._syncGeometryAfterVertexMove(t,p,e,i,h),o()}}async _syncGeometryAfterVertexMove(t,e,i,s,o=!1){const n=this._editGeometryOperations.data.geometry;if(n.type==="point")t.geometry=e;else{const{x:r,y:h}=e,[p,a]=this._getIndicesForVertexGraphic(t);let c=M(n);const l=c[p].length-1;c[p][a]=[r,h],n.type==="polygon"&&(a===0?c[p][l]=[r,h]:a===l&&(c[p][0]=[r,h])),this._isVertex(t)&&(c=this._moveRelatedCoordinates(c,t,r,h),c=this._moveSelectedHandleCoordinates(c,t,i,s,n.type==="polygon"),this._updateMidpointGraphicLocations(c)),this.graphic.geometry=n.clone();const _=this._getVertexFromEditGeometry(t),x=r-_.pos[0],w=h-_.pos[1];this._editGeometryOperations.moveVertices([_],x,w,0),o&&(this._mover?this._mover.updateGeometry(this._mover.graphics.indexOf(t),e):t.geometry=e)}}_moveRelatedCoordinates(t,e,i,s){const{relatedGraphicIndices:o}=e.attributes;for(const n of o){const r=this.vertexGraphics.getItemAt(n),{pathIndex:h,pointIndex:p}=r.attributes;t[h][p]=[i,s],r.geometry=e.geometry}return t}_moveSelectedHandleCoordinates(t,e,i,s,o){for(const n of this.selectedVertices)if(n!==e){const{pathIndex:r,pointIndex:h,relatedGraphicIndices:p}=n.attributes,a=st(n.geometry,i,s,this.view),c=t[r].length-1;t[r][h]=[a.x,a.y],n.geometry=a,o&&(h===0?t[r][c]=[a.x,a.y]:h===c&&(t[r][0]=[a.x,a.y]));for(const l of p){const _=this.vertexGraphics.getItemAt(l),{pathIndex:x,pointIndex:w}=_.attributes;t[x][w]=[a.x,a.y],_.geometry=a}}return t}_onGraphicPointerOverCallback(t){const e=t.graphic,i=this._isVertex(e);i&&!this._isSelected(e)&&(e.symbol=this.symbols.vertices.hover),this._updateTooltip(i?e:null),this._updateHoverCursor(e)}_onGraphicPointerOutCallback(t){const e=t.graphic;this._isVertex(e)&&!this._isSelected(e)&&(e.symbol=this.symbols.vertices.default),this.view.cursor="default",this._updateTooltip()}_createVertexFromMidpoint(t){const{_graphicAttributes:e,graphic:i}=this,s=i.geometry;if(u(s)||s.type!=="polygon"&&s.type!=="polyline")return[];const o=s.clone(),n=[],{pathIndex:r,pointIndexStart:h,pointIndexEnd:p}=t.attributes,{x:a,y:c}=t.geometry,l=p===0?h+1:p,_=M(o);return _[r].splice(l,0,[a,c]),t.attributes={...e,pathIndex:r,pointIndex:l,relatedGraphicIndices:[]},n.push({coordinates:_[r][l],componentIndex:r,vertexIndex:l}),this.graphic.geometry=o,n}_addToSelection(t){t instanceof G&&(t=[t]);for(const e of t)e.symbol=this.symbols.vertices.selected;this._set("selectedVertices",this.selectedVertices.concat(t)),this._emitSelectEvent(t)}_removeFromSelection(t,e){const{vertices:i}=this.symbols,s=e?i.hover:i.default;t instanceof G&&(t=[t]);for(const o of t)this.selectedVertices.splice(this.selectedVertices.indexOf(o),1),this._set("selectedVertices",this.selectedVertices),o.set("symbol",s);this._emitDeselectEvent(t)}_clearSelection(){if(this.selectedVertices.length){const t=this.selectedVertices;for(const e of this.selectedVertices)e.set("symbol",this.symbols.vertices.default);this._set("selectedVertices",[]),this._emitDeselectEvent(t)}}_keyDownHandler(t){ot.delete.includes(t.key)&&!t.repeat&&this.selectedVertices.length&&this._removeVertices(this.selectedVertices)}_removeVertices(t){const e=this.graphic.geometry;if(u(e)||e.type!=="polygon"&&e.type!=="polyline"||e.type==="polygon"&&this.vertexGraphics.length<4||this.vertexGraphics.length<3)return;t instanceof G&&(t=[t]);const i=this.graphic.clone(),s=e.clone();let o=M(s);const n=[];t instanceof G&&(t=[t]);for(const r of t){const{x:h,y:p}=r.geometry;for(let a=0;a<o.length;a++){const c=o[a];for(let l=0;l<c.length;l++){const[_,x]=c[l];h===_&&p===x&&(n.push({coordinates:o[a][l],componentIndex:a,vertexIndex:l}),o[a].splice(Number(l),1))}}}if(s.type==="polygon")o=o.filter(r=>{if(r.length<2)return!1;const[h,p]=r[0],[a,c]=r[r.length-1];return(r.length!==2||h!==a||p!==c)&&(h===a&&p===c||r.push(r[0]),!0)}),s.rings=o;else{for(const r of o)r.length===1&&o.splice(o.indexOf(r),1);s.paths=o}this.graphic.geometry=s,this.refresh(),this._emitVertexRemoveEvent(t,i,n)}_isVertex(t){return this.vertexGraphics.includes(t)}_isSelected(t){return this._isVertex(t)&&this.selectedVertices.includes(t)}_isMidpoint(t){return this.midpointGraphics.includes(t)}_updateHoverCursor(t){this.view.cursor=this._isMidpoint(t)?"copy":"move"}_updateTooltip(t,e){u(this._tooltip)||(t?t===this.graphic?this._updateMoveGraphicTooltip(e):this._updateMoveVertexTooltip(e):this._tooltip.clear())}_updateMoveGraphicTooltip(t){const{_tooltip:e,tooltipOptions:i,view:s}=this;if(u(e))return;const o=new ht({tooltipOptions:i});if(t){const{x:n,y:r}=t.origin,h=s.toMap(t),p=s.toMap(C(n,r)),a=T(p,h);o.distance=g(a)?a:H}e.info=o}_updateMoveVertexTooltip(t){const{_tooltip:e,graphic:{geometry:i},tooltipOptions:s,view:o}=this;if(u(e))return;const n=new pt({tooltipOptions:s});if(g(i)&&(i.type==="polygon"?n.area=ct(i):i.type==="polyline"&&(n.totalLength=K(i))),t){const{x:r,y:h}=t.origin,p=o.toMap(t),a=o.toMap(C(r,h)),c=T(a,p);n.distance=g(c)?c:H}e.info=n}_emitMoveStartEvent(t,e){const i=new vt(this.graphic,t,e);this.emit("move-start",i),this.callbacks.onMoveStart&&this.callbacks.onMoveStart(i)}_emitMoveEvent(t,e){const i=new yt(this.graphic,t,e);this.emit("move",i),this.callbacks.onMove&&this.callbacks.onMove(i)}_emitMoveStopEvent(){const t=this._activeOperationInfo;if(!t)return;const{totalDx:e,totalDy:i}=t,s=new _t(this.graphic,e,i);this.emit("move-stop",s),this.callbacks.onMoveStop&&this.callbacks.onMoveStop(s)}_emitReshapeStartEvent(t){const e=new lt(this.graphic,t,this.selectedVertices);this.emit("reshape-start",e),this.callbacks.onReshapeStart&&this.callbacks.onReshapeStart(e)}_emitReshapeEvent(t){const e=new dt(this.graphic,t,this.selectedVertices);this.emit("reshape",e),this.callbacks.onReshape&&this.callbacks.onReshape(e)}_emitReshapeStopEvent(t){const e=new mt(this.graphic,t,this.selectedVertices);this.emit("reshape-stop",e),this.callbacks.onReshapeStop&&this.callbacks.onReshapeStop(e)}_emitSelectEvent(t){const e=new gt(t);this.emit("select",e),this.callbacks.onVertexSelect&&this.callbacks.onVertexSelect(e)}_emitDeselectEvent(t){const e=new ut(t);this.emit("deselect",e),this.callbacks.onVertexDeselect&&this.callbacks.onVertexDeselect(e)}_emitVertexAddEvent(t,e,i){const s=new xt(t,this.graphic,e,i);this.emit("vertex-add",s),this.callbacks.onVertexAdd&&this.callbacks.onVertexAdd(s)}_emitVertexRemoveEvent(t,e,i){const s=new ft(t,this.graphic,e,i);this.emit("vertex-remove",s),this.callbacks.onVertexRemove&&this.callbacks.onVertexRemove(s)}_logGeometryTypeError(){F.getLogger(this.declaredClass).error(new D("reshape:invalid-geometry","Reshape operation not supported for the provided graphic. The geometry type is not supported."))}};m([v()],d.prototype,"_tooltip",void 0),m([v()],d.prototype,"callbacks",void 0),m([v()],d.prototype,"enableMidpoints",void 0),m([v()],d.prototype,"enableMovement",void 0),m([v()],d.prototype,"enableVertices",void 0),m([v()],d.prototype,"graphic",void 0),m([v({value:!0})],d.prototype,"highlightsEnabled",null),m([v()],d.prototype,"layer",void 0),m([v({readOnly:!0})],d.prototype,"midpointGraphics",void 0),m([v()],d.prototype,"midpointSymbol",void 0),m([v({readOnly:!0})],d.prototype,"selectedVertices",void 0),m([v()],d.prototype,"snappingManager",void 0),m([v({readOnly:!0})],d.prototype,"state",null),m([v({value:E})],d.prototype,"symbols",null),m([v({type:$})],d.prototype,"tooltipOptions",void 0),m([v({readOnly:!0})],d.prototype,"type",void 0),m([v({readOnly:!0})],d.prototype,"vertexGraphics",void 0),m([v()],d.prototype,"view",void 0),d=m([U("esri.views.draw.support.Reshape")],d);const Fe=d;export{Fe as default};
