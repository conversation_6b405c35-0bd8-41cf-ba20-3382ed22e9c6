/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.video;

import org.springframework.data.domain.Page;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.imodel.query.video.VideoMonitoringPageRequest;

import java.util.List;

public interface VideoDao {
    VideoEntity save(VideoEntity videoEntity);

    List<VideoEntity> findByProject(String projectId, String name, String isBind, String groupId);

    boolean delete(String id);

    boolean deleteAll(List<String> ids);

    List<VideoEntity> findByProjectAndType(String projectId, String type);

    List<VideoEntity> findByVideoType(String type, TenantId tenantId);

    VideoEntity findById(String id);

    List<VideoEntity> findAllByName(String name, String tenantId);

    List<VideoEntity> getAllBindList();

    /**
     * 分页查询视频列表
     * @param pageSize 每页记录数
     * @param page 页码
     * @param request 查询条件
     * @return 分页数据
     */
    Page<VideoEntity> findByPage(long page, long pageSize, VideoMonitoringPageRequest request);
}
