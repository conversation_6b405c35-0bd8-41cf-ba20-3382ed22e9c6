package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseProxyConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration;
import org.thingsboard.server.dao.sql.base.BaseProxyConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseProxyConfigurationPageRequest;

/**
 * 平台管理-代理配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseProxyConfigurationServiceImpl implements IBaseProxyConfigurationService {

    @Autowired
    private BaseProxyConfigurationMapper baseProxyConfigurationMapper;

    /**
     * 查询平台管理-代理配置
     *
     * @param id 平台管理-代理配置主键
     * @return 平台管理-代理配置
     */
    @Override
    public BaseProxyConfiguration selectBaseProxyConfigurationById(String id) {
        return baseProxyConfigurationMapper.selectBaseProxyConfigurationById(id);
    }

    /**
     * 查询平台管理-代理配置列表
     *
     * @param baseProxyConfiguration 平台管理-代理配置
     * @return 平台管理-代理配置
     */
    @Override
    public IPage<BaseProxyConfiguration> selectBaseProxyConfigurationList(BaseProxyConfigurationPageRequest baseProxyConfiguration) {
        return baseProxyConfigurationMapper.selectBaseProxyConfigurationList(baseProxyConfiguration);
    }

    /**
     * 新增平台管理-代理配置
     *
     * @param baseProxyConfiguration 平台管理-代理配置
     * @return 结果
     */
    @Override
    public int insertBaseProxyConfiguration(BaseProxyConfiguration baseProxyConfiguration) {
        baseProxyConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseProxyConfigurationMapper.insertBaseProxyConfiguration(baseProxyConfiguration);
    }

    /**
     * 修改平台管理-代理配置
     *
     * @param baseProxyConfiguration 平台管理-代理配置
     * @return 结果
     */
    @Override
    public int updateBaseProxyConfiguration(BaseProxyConfiguration baseProxyConfiguration) {
        return baseProxyConfigurationMapper.updateBaseProxyConfiguration(baseProxyConfiguration);
    }

    /**
     * 批量删除平台管理-代理配置
     *
     * @param ids 需要删除的平台管理-代理配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseProxyConfigurationByIds(List<String> ids) {
        return baseProxyConfigurationMapper.deleteBaseProxyConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-代理配置信息
     *
     * @param id 平台管理-代理配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseProxyConfigurationById(String id) {
        return baseProxyConfigurationMapper.deleteBaseProxyConfigurationById(id);
    }
}
