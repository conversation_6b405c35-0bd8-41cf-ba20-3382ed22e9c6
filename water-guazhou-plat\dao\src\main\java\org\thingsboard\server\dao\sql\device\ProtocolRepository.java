package org.thingsboard.server.dao.sql.device;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.Protocol;

import java.util.List;

public interface ProtocolRepository extends JpaRepository<Protocol, String> {
    void deleteByTemplateId(String templateId);

    List<Protocol> findByTemplateId(String templateId, Sort sort);
    List<Protocol> findByTemplateId(String templateId);
}
