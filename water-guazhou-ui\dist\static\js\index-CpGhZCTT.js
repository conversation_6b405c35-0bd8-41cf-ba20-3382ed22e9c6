import{m as e}from"./index-r0dFAfgr.js";const a=r=>e({url:"/api/workOrder",method:"post",data:r}),s=r=>e({url:`/api/workOrder/${r}`,method:"get"}),d=r=>e({url:`/api/workOrder/${r}/receive`,method:"post",data:{processRemark:"",processAdditionalInfo:JSON.stringify({})}}),n=(r,t)=>e({url:`/api/workOrder/${r}/verify`,method:"post",data:t}),i=(r,t)=>e({url:`/api/workOrder/${r}/stage`,method:"post",data:t}),p=r=>e({url:`/api/workOrder/${r}/stages`,method:"get"}),u=r=>e({url:"/api/workOrder",method:"get",params:r}),k=r=>e({url:"/api/fault/report/workOrder",method:"get",params:r}),c=r=>e({url:"/api/workOrder/my",method:"get",params:r}),O=(r,t)=>e({url:`/api/workOrder/${r}/assign`,method:"post",data:t}),l=(r,t)=>e({url:`/api/workOrder/${r}/reassign`,method:"post",data:t}),m=(r,t)=>e({url:`/api/workOrder/${r}/terminate`,method:"post",data:t}),g=(r,t)=>e({url:`/api/workOrder/${r}/chargeback`,method:"post",data:t}),h=(r,t)=>e({url:`/api/workOrder/${r}/chargebackRequest`,method:"post",data:t}),w=(r,t)=>e({url:`/api/workOrder/${r}/verify`,method:"post",data:t}),W=r=>e({url:"/api/workOrder/count",method:"get",params:r}),$=(r,t)=>e({url:`/api/workOrder/${r}/handoverRequest`,method:"post",data:t}),v=(r,t)=>e({url:`/api/workOrder/${r}/collaborate`,method:"post",data:t}),y=r=>e({url:"/api/workOrder/completeCount",method:"get",params:r}),f=r=>e({url:"/api/workOrderType/list",method:"get",params:{status:r}}),G=r=>e({url:"/api/workOrderEmergencyLevel/list",method:"get",params:{status:r}}),P=r=>e({url:"/api/workOrderProcessLevel/list",method:"get",params:{status:r}});export{i as A,g as C,O as D,W as G,$ as H,a as P,d as R,m as T,n as V,G as a,P as b,u as c,w as d,v as e,h as f,f as g,c as h,l as i,s as j,p as k,y as l,k as m};
