/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.alarm;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.Dao;
import org.thingsboard.server.dao.model.sql.ExtraUser;

import java.util.List;

/**
 * Created by jerry 18-12-29
 */
public interface AlarmJsonDao extends Dao<AttrAlarmJson> {

    AttrAlarmJson createOrUpdateAlarmJson(AttrAlarmJson attrAlarmJson);

    ListenableFuture<AttrAlarmJson> findAlarmByIdAsync(AlarmJsonId alarmJsonId);

    ListenableFuture<List<AttrAlarmJson>> findByParams(TenantId tenantId, DeviceId deviceId, String attribute);

    void deleteAlarmJsonById(AlarmJsonId alarmJsonId);

    ListenableFuture<List<AttrAlarmJson>> findByDeviceAndAttribute(DeviceId deviceId, String attribute);

    ListenableFuture<List<AttrAlarmJson>> findByTenant(TenantId tenantId);

    List<AttrAlarmJson> findByDevice(DeviceId deviceId);

    ListenableFuture<List<AttrAlarmJson>> findByDeviceAndPropAndLevel(DeviceId deviceId,String prop,String level);

    List<AttrAlarmJson> findByProjectId(String projectId);

    List<User> getAlarmLinkedUserBySendMethod(String alarmJsonId,String method);

    List<ExtraUser> getAlarmLinkedExtraUserBySendMethod(String alarmJsonId, String method);
}
