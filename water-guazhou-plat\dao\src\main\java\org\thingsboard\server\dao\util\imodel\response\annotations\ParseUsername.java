package org.thingsboard.server.dao.util.imodel.response.annotations;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
public @interface ParseUsername {
    /**
     * 目标字段名前缀，会转换为 前缀+Name 的形式
     * 为空时默认字段以Id为后缀时会替换Id后缀为Name后缀，若不以Id作为后缀则直接添加Name后缀
     */
    String value() default "";

    boolean withDepartment() default false;

    boolean withOrganization() default false;
}
