package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.WorkOrderReminderRequest;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderReminder;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderReminderMapper;

import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-12-21
 */

@Service
public class WorkOrderReminderServiceImpl implements WorkOrderReminderService {

    @Autowired
    private WorkOrderReminderMapper workOrderReminderMapper;

    @Override
    public WorkOrderReminder save(WorkOrderReminder workOrderReminder) {
        if (StringUtils.isBlank(workOrderReminder.getId())) {
            workOrderReminder.setCreateTime(new Date());
            workOrderReminder.setIsDel("0");
            workOrderReminderMapper.insert(workOrderReminder);
        } else {
            workOrderReminderMapper.updateById(workOrderReminder);
        }
        return workOrderReminder;
    }

    @Override
    public PageData<WorkOrderReminder> getList(WorkOrderReminderRequest workOrderReminderRequest) {
        IPage<WorkOrderReminder> ipage = workOrderReminderMapper.getList(workOrderReminderRequest);
        return new PageData<>(ipage.getTotal(), ipage.getRecords());
    }

    @Override
    public void softDelete(String id) {
        WorkOrderReminder workOrderReminder = workOrderReminderMapper.selectById(id);
        if (workOrderReminder != null) {
            workOrderReminder.setIsDel("1");
            workOrderReminderMapper.updateById(workOrderReminder);
        }
    }

    @Override
    public void delete(String id) {
        workOrderReminderMapper.deleteById(id);
    }
}
