package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.assay.AssayReportDataAddress;

import java.util.List;

@Mapper
public interface AssayReportDataAddressMapper extends BaseMapper<AssayReportDataAddress> {

    void batchInsert(List<AssayReportDataAddress> assayReportDataAddressList);

    List<AssayReportDataAddress> getListByPidIn(List<String> pidList);
}
