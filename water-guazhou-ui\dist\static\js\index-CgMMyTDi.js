import{d as T,M as I,c as s,s as S,r as c,x as k,a8 as x,ar as C,a9 as L,bT as A,D as N,o as V,g as v,n as W,q as l,i as n,F as O,b6 as D,b7 as q}from"./index-r0dFAfgr.js";import{_ as G}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{T as M,m as B}from"./index-CpGhZCTT.js";import{I as h}from"./common-CvK_P_ao.js";import{b as U}from"./malfunctionRepair-CM_eL_AA.js";import w from"./detail-COD2FdP3.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         *//* empty css                             */import"./detailSteps-Bp-QHOXn.js";/* empty css                */const F={class:"wrapper"},oe=T({__name:"index",setup(H){const{$btnPerms:d}=I(),u=s(),m=s(),f=s(),y=s({filters:[{label:"工单单号",field:"serialNo",type:"input"},{label:"开始时间",field:"fromTime",type:"date"},{label:"结束时间",field:"toTime",type:"date"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:h.QUERY,click:()=>i()},{type:"default",perm:!0,text:"重置",svgIcon:S(q),click:()=>{var e;(e=u.value)==null||e.resetForm(),i()}}]}]}),a=c({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"上传用户姓名",prop:"uploadUserName"},{label:"标题",prop:"title"},{label:"描述",prop:"remark"},{label:"地址",prop:"address"},{label:"工单号",prop:"serialNo"},{label:"工单状态",prop:"status",formatter:e=>{switch(e.status){case"PENDING":case"ASSIGN":return"待处理";case"RESOLVING":case"ARRIVING":case"PROCESSING":case"SUBMIT":case"REVIEW":case"CHARGEBACK_REVIEW":case"HANDOVER_REVIEW":case"REASSIGN":case"COLLABORATION":return"处理中";case"APPROVED":case"CHARGEBACK":case"TERMINATED":return"已结束"}}},{label:"故障项目",prop:"faultProject"},{label:"故障描述",prop:"remark"},{label:"创建时间",prop:"createTime"},{label:"设备",prop:"drive",formItemConfig:{type:"btn-group",btns:[{isTextBtn:!0,text:"查看",perm:d("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>R(e)}]}}],operationWidth:"160px",operations:[{type:"primary",color:"#4195f0",text:"详情",perm:d("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>{var t;E.value=e.id||"",b.title=e.serialNo,(t=m.value)==null||t.openDrawer()}},{hide:e=>{switch(e.status){case"APPROVED":case"CHARGEBACK":case"TERMINATED":return!0;default:return!1}},type:"danger",color:"#4195f0",text:"终止",perm:d("RoleManageEdit"),icon:h.DELETE,click:e=>{M(e.id,{processRemark:"终止",processAdditionalInfo:"点击终止"}).then(()=>{i(),k.success("终止成功")})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,i()}}}),g=c({title:"设备",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"table",field:"drives",config:{indexVisible:!0,height:"350px",dataList:x(()=>r.equipmentList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"型号/规格",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"}],pagination:{hide:!0}}}]}]}),b=c({title:"流程明细",group:[]}),E=s(""),R=e=>{var t;g.defaultValue={...e||{}},r.getWorkOrderEquipmentListValue(e.id),(t=f.value)==null||t.openDrawer()},r=c({WaterSupplyTree:[],UserList:[],equipmentList:[],getWorkOrderEquipmentListValue:e=>{U({workOrderId:e}).then(t=>{r.equipmentList=t.data.data||[]})},getWaterSupplyTreeValue:()=>{C(2).then(t=>{r.WaterSupplyTree=L(t.data.data||[])})},getUserListValue:e=>{A({pid:e}).then(t=>{const o=t.data.data.data||[];r.UserList=o.map(p=>({label:p.firstName,value:N(p.id.id)}))})}}),i=async()=>{var t;const e={size:a.pagination.limit,page:a.pagination.page,type:"设备故障",source:"设备资产",...((t=u.value)==null?void 0:t.queryParams)||{}};B(e).then(o=>{a.dataList=o.data.data.data||[],a.pagination.total=o.data.data.total||0})};return V(()=>{i(),r.getWaterSupplyTreeValue()}),(e,t)=>{const o=P,p=G,_=D;return v(),W("div",F,[l(o,{ref_key:"refSearch",ref:u,config:n(y)},null,8,["config"]),l(p,{config:n(a),class:"card-table"},null,8,["config"]),l(_,{ref_key:"refForm",ref:f,config:n(g)},null,8,["config"]),l(_,{ref_key:"refdetail",ref:m,config:n(b)},{default:O(()=>[l(w,{id:n(E)},null,8,["id"])]),_:1},8,["config"])])}}});export{oe as default};
