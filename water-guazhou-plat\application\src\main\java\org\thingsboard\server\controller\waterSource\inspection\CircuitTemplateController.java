package org.thingsboard.server.controller.waterSource.inspection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplateSaveRequest;
import org.thingsboard.server.dao.circuit.CircuitTemplateService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/sp/circuitTemplate")
public class CircuitTemplateController extends BaseController {
    @Autowired
    private CircuitTemplateService service;


    @GetMapping
    public IPage<CircuitTemplate> findAllConditional(CircuitTemplatePageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public CircuitTemplate save(@RequestBody CircuitTemplateSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitTemplateSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}