package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_install_process_step")
@Data
public class ProcessStep {
    @TableId
    private String id;

    private String mainId;

    private transient String mainName;

    private String code;

    private String name;

    private String title;

    private Integer orderNum;

    private String type;

    private Integer executionDay;

    private String departmentId;

    private String users;

    private transient String userNames;

    private String formId;

    @TableField(exist = false)
    private String content;

    private transient String formName;

    private String remark;

    private String tenantId;

}
