{"name": "thingsboard-web-ui", "private": true, "version": "2.3.0", "description": "ThingsBoard Web UI Microservice", "main": "server.js", "bin": "server.js", "scripts": {"install": "pkg -t node8-linux-x64,node8-win-x64 --out-path ./target . && node install.js", "test": "echo \"Error: no test specified\" && exit 1", "start": "WEB_FOLDER=./target/web nodemon server.js", "start-prod": "NODE_ENV=production nodemon server.js"}, "dependencies": {"compression": "^1.7.3", "config": "^1.30.0", "connect-history-api-fallback": "^1.5.0", "express": "^4.16.3", "http": "0.0.0", "http-proxy": "^1.17.0", "js-yaml": "^3.12.0", "winston": "^3.0.0", "winston-daily-rotate-file": "^3.2.1"}, "engine": "node >= 5.9.0", "nyc": {"exclude": ["test", "__tests__", "node_modules", "target"]}, "devDependencies": {"fs-extra": "^6.0.1", "nodemon": "^1.17.5", "pkg": "^4.3.3"}}