import request from '@/plugins/axios'

// 获取考核表列表（分页）
export function getAssessmentTables(params) {
  return request({
    url: '/api/ledger/assessment-table',
    method: 'get',
    params
  })
}

// 根据ID获取考核表详情
export function getAssessmentTableById(id) {
  return request({
    url: `/api/ledger/assessment-table/${id}`,
    method: 'get'
  })
}

// 保存考核表（新增或更新）
export function saveAssessmentTable(data) {
  return request({
    url: '/api/ledger/assessment-table',
    method: 'post',
    data
  })
}

// 删除考核表
export function deleteAssessmentTable(id) {
  return request({
    url: `/api/ledger/assessment-table/${id}`,
    method: 'delete'
  })
}

// 批量删除考核表
export function batchDeleteAssessmentTables(ids) {
  return request({
    url: '/api/ledger/assessment-table/batch-delete',
    method: 'post',
    data: ids
  })
}

// 导入考核表
export function importAssessmentTables(data) {
  return request({
    url: '/api/ledger/assessment-table/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出考核表
export function exportAssessmentTables(params) {
  return request({
    url: '/api/ledger/assessment-table/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 下载导入模板
export function downloadAssessmentTemplate() {
  return request({
    url: '/api/ledger/assessment-table/template',
    method: 'get',
    responseType: 'blob'
  })
} 