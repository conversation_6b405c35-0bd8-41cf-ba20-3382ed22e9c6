import{d as X,c as I,r as $,am as H,o as Q,D as r,f4 as Y,eX as Z,dW as j,bB as x,dR as ee,g as i,h as u,F as o,p as T,q as d,G as m,n as h,aJ as E,bh as ae,aB as V,bo as te,i as w,f5 as se,b as B,bk as le,f6 as ne,bz as oe,bb as pe,bV as ie,dm as ce,bl as de,bm as re,J as ue,L as fe,br as me,C as ge}from"./index-r0dFAfgr.js";import{G as _e,k as be}from"./index-CaaU9niG.js";const Ce={class:"content-box"},he={class:"dialog-footer"},ke=X({__name:"rolemenu",props:{tableConfig:{}},setup(L){const c=L,f=I([]),e=$({userdata:[],data:[],selectNodes:[],enterpriseoptions:[],enterprise:"",appoptions:[],app:"",apps:[],selectedApps:[],selectedAppItems:[],visible:!0,loading:!1}),p=I({});H(()=>c.tableConfig.visible,t=>{e.visible=t}),Q(()=>{e.appoptions=[],e.app="",e.apps=[],e.selectedApps=[],e.selectedAppItems=[],S().then(()=>{k()})});const R=async(t,a)=>{var s;console.log(t,a),p.value[e.app]=(s=f.value[e.app])==null?void 0:s.getCheckedKeys(),e.app=t.props.name||"",await x(),v(e.app)},M=async t=>{const a=r(c.tableConfig.roleId);await se({roleId:a,tenantApplicationId:t.id}),await k()},k=async()=>{var s;const t=r(c.tableConfig.roleId),a=await Y(t);e.selectedApps=a.data,e.selectedAppItems=e.apps.filter(l=>e.selectedApps.indexOf(l.id)!==-1),p.value={},e.selectedAppItems.forEach(l=>{l in p.value||(p.value[l]=[])}),e.selectedAppItems.length>0&&(e.app=((s=e.selectedAppItems[0])==null?void 0:s.id)||"",v(e.app))},z=()=>{},G=()=>{p.value[e.app]=f.value[e.app].getCheckedKeys(),be({roleId:r(c.tableConfig.roleId),menus:[...p.value[e.app]],tenantApplicationId:e.app}).then(()=>{B.success("操作成功")}).catch(()=>{B.error("保存失败")})},S=async()=>{const t=r(Z.get("tenantId")||""),a=await j(t,"ALL");a.status===200&&a.data.forEach(s=>{e.appoptions.push({label:s.name,value:s.id}),e.apps.push(s)})},v=async t=>{t&&(e.app=t),await x(),e.loading=!0,ee(e.app).then(a=>{console.log(a),e.data=A(a.data,"children",{label:"meta.title"}),D()}).finally(()=>{e.loading=!1})},D=()=>{_e({roleId:r(c.tableConfig.roleId),tenantApplicationId:e.app}).then(t=>{e.app&&(p.value[e.app]=t.data.data)})},N=()=>{var t;(t=f.value[e.app])==null||t.setCheckedKeys([])};function A(t,a="children",s={label:"name",value:"id"}){return t.map(l=>{if(l){for(const g in s)l[g]=l.meta.title;l[a]&&l[a].length&&A(l[a],a,s)}return l}),t}return(t,a)=>{var y;const s=le,l=ne,g=oe,K=pe,U=ie,F=ce,J=de,q=re,_=ue,O=fe,P=me;return i(),u(O,{modelValue:e.visible,"onUpdate:modelValue":a[2]||(a[2]=n=>e.visible=n),title:"授权角色的菜单",width:"60%","close-on-click-modal":!1,onClose:(y=c.tableConfig)==null?void 0:y.close},{footer:o(()=>[T("span",he,[d(_,{size:"small",type:"primary",onClick:G},{default:o(()=>a[4]||(a[4]=[m("保存")])),_:1}),d(_,{size:"small",onClick:N},{default:o(()=>a[5]||(a[5]=[m("清空")])),_:1}),d(_,{size:"small",onClick:c.tableConfig.close},{default:o(()=>a[6]||(a[6]=[m("取 消")])),_:1},8,["onClick"])])]),default:o(()=>[d(g,null,{default:o(()=>[a[3]||(a[3]=T("div",{class:"app-auth-title"}," 应用授权 ",-1)),d(l,{modelValue:e.selectedApps,"onUpdate:modelValue":a[0]||(a[0]=n=>e.selectedApps=n)},{default:o(()=>[(i(!0),h(V,null,E(e.apps,(n,b)=>(i(),u(s,{key:b,label:n.id,onChange:C=>M(n)},{default:o(()=>[m(ae(n.name),1)]),_:2},1032,["label","onChange"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(q,{modelValue:e.app,"onUpdate:modelValue":a[1]||(a[1]=n=>e.app=n),"tab-position":"left",style:{height:"500px","margin-top":"20px"},onTabClick:R},{default:o(()=>[(i(!0),h(V,null,E(e.selectedAppItems,(n,b)=>(i(),u(J,{key:b,label:n.name,name:n.id},{default:o(()=>{var C;return[te((i(),h("div",Ce,[((C=e.data)==null?void 0:C.length)>0?(i(),u(U,{key:0,height:"500px"},{default:o(()=>[d(K,{ref_for:!0,ref:W=>{w(f)[n.id]=W},"default-checked-keys":w(p)[e.app],data:e.data,class:"menu-tree","show-checkbox":"","node-key":"id","default-expand-all":!0,onCheck:z},null,8,["default-checked-keys","data"])]),_:2},1024)):(i(),u(F,{key:1,"image-size":150,description:"该应用暂不支持菜单授权"}))])),[[P,e.loading]])]}),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","onClose"])}}}),ye=ge(ke,[["__scopeId","data-v-48ef3cb0"]]);export{ye as default};
