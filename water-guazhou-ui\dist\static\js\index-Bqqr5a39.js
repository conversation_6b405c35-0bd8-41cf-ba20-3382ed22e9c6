import{C as O,b3 as Y,ch as K,u as Q,c as q,r as G,am as X,o as Z,bA as ee,n as w,p as u,aw as C,aB as B,aJ as F,bB as N,cR as U,D as te,dW as ne,dR as V,g as M,bw as _,bh as k,eT as le}from"./index-r0dFAfgr.js";function oe(P){let f=null,i,c,E=[];return(()=>{if(i=document.getElementById(P),!i)return;c=i.getContext("2d");const d=()=>{i.width=window.innerWidth,i.height=window.innerHeight,g()},b=[],z=3;for(let l=0;l<z;l++)b.push({wavelength:200+l*100,amplitude:30+l*10,speed:.01+l*.005,phase:0,color:`rgba(0, 150, 255, ${.15-l*.03})`});const g=()=>{E=[];for(let l=0;l<50;l++)E.push({x:Math.random()*i.width,y:Math.random()*i.height,size:Math.random()*3+1,opacity:Math.random()*.5,speed:Math.random()*.002+.001})},S=()=>{c.clearRect(0,0,i.width,i.height),b.forEach(l=>{l.phase+=l.speed,l.phase>Math.PI*2&&(l.phase=0);const x=i.height*.7;c.beginPath(),c.moveTo(0,x);for(let R=0;R<i.width;R+=5){const A=x+Math.sin(R/l.wavelength+l.phase)*l.amplitude+Math.sin(R/(l.wavelength*.7)+l.phase*1.3)*(l.amplitude*.6)+Math.sin(R/(l.wavelength*.5)+l.phase*.7)*(l.amplitude*.3);c.lineTo(R,A)}c.lineTo(i.width,i.height),c.lineTo(0,i.height),c.closePath();const T=c.createLinearGradient(0,x,0,i.height);T.addColorStop(0,l.color),T.addColorStop(1,"rgba(0, 50, 100, 0.05)"),c.fillStyle=T,c.fill()}),E.forEach(l=>{l.y>i.height*.7&&(l.opacity+=Math.sin(Date.now()*l.speed)*.01,l.opacity=Math.max(.1,Math.min(.6,l.opacity)),c.beginPath(),c.arc(l.x,l.y,l.size,0,Math.PI*2),c.fillStyle=`rgba(255, 255, 255, ${l.opacity})`,c.fill())}),f=requestAnimationFrame(S)};return d(),g(),S(),window.addEventListener("resize",d),()=>{f&&cancelAnimationFrame(f),window.removeEventListener("resize",d)}})()}const j={WATER_PARTICLES:"water-particles"};let L=null;function se(P,f){switch(L&&(L(),L=null),P){case j.WATER_PARTICLES:L=oe(f);break;default:return console.warn(`未知的特效类型: ${P}`),()=>{}}return L}const ae={key:0,class:"navigation-container"},re=["onMouseenter","onMouseleave","onClick"],ie={class:"small-cylinder-text vertical-text"},ce={class:"tooltip"},de=["onMouseenter","onMouseleave","onClick"],ue={class:"small-cylinder-text vertical-text"},fe={class:"tooltip"},he=["onMouseenter","onMouseleave","onClick"],ve={class:"small-cylinder-text vertical-text"},ge={class:"tooltip"},me=["onMouseenter","onMouseleave","onClick"],ye={class:"small-cylinder-text vertical-text"},pe={class:"tooltip"},$e={key:1,class:"loading-container"},we={__name:"index",setup(P){const f=Y();K();const i=Q();let c=null;const E=q(!1),I=q(null),d=q([]),b=G({}),z=async()=>{if(console.log("开始初始化背景特效..."),await N(),!I.value){console.error("无法找到canvas元素 (ref为null)");return}if(!document.getElementById("bg-canvas")){console.error("无法通过ID找到canvas元素");return}console.log("找到canvas元素:",I.value);try{c=se(j.WATER_PARTICLES,"bg-canvas"),console.log("背景特效初始化完成")}catch(e){console.error("初始化背景特效失败:",e)}};X(E,n=>{n&&(console.log("路由加载完成，准备初始化特效"),N(()=>{z()}))});const g=G({cylinder1:!1,cylinder2:!1,cylinder3:!1,cylinder4:!1,smallCylinder11:!1,smallCylinder12:!1,smallCylinder21:!1,smallCylinder22:!1,smallCylinder31:!1,smallCylinder32:!1,smallCylinder41:!1,smallCylinder42:!1,btn1:!1,btn2:!1}),S=G({生产一张图:"/cockpit/production",营收一张图:"/cockpit/revenue"}),l=async n=>{try{if(console.log(`获取系统 ${n} 的子系统路由...`),b[n])return console.log("使用缓存的子系统数据:",b[n]),b[n];const e=await V(n);if(e.data){const s=d.value.findIndex(h=>h.id===n);if(s!==-1)return b[n]=e.data,d.value[s].subsystems=e.data.filter(h=>h.children&&h.children.length).slice(0,2),console.log(`获取到系统 ${n} 的子系统:`,d.value[s].subsystems),e.data}return[]}catch(e){return console.error(`获取系统 ${n} 的子系统失败:`,e),[]}},x=async n=>{if(!n||!n.length)return!1;try{const e=le(n);if(e&&e.length){const s=f.getRoutes().map(a=>a.name);let h=0;return e.forEach(a=>{a.name&&!s.includes(a.name)?(a.path.startsWith("/")||(a.path="/"+a.path),f.addRoute(a),console.log(`添加路由: ${a.path}, 名称: ${a.name}`),h++):a.name?console.log(`路由已存在，跳过添加: ${a.path}, 名称: ${a.name}`):console.warn(`路由缺少名称，无法添加: ${a.path}`)}),h>0}return!1}catch(e){return console.error("添加路由失败:",e),!1}},T=n=>!n.children||n.children.length===0?n:T(n.children[0]),R=(n,e="")=>{if(!n)return"";let s=n.path.startsWith("/")?n.path:`/${n.path}`;if(!e||s.includes(e))return s;const h=s.split("/")[1];return e.split("/").pop()===h?`${e}${s.substring(s.indexOf(h)+h.length)}`:`${e}${s}`},A=async(n,e)=>{if(!e||!e.path){console.warn("子系统路径未定义");return}try{const s=await l(n);if(!s||!s.length){console.error("%c【获取路由失败】","color: #e74c3c; font-weight: bold","未能获取到路由数据");return}console.log("%c【添加路由到Vue Router】","color: #3498db; font-weight: bold"),await x(s)||console.warn("%c【添加路由失败或无需添加】","color: #e67e22; font-weight: bold","路由可能已存在或添加失败");const a=e.path.startsWith("/")?e.path:`/${e.path}`;let $=e,t=a;e.children&&e.children.length>0&&($=T(e),console.log("找到叶子节点路由:",$),t=R($,a)),console.log("最终导航路径:",t);const o=f.getRoutes();if(console.log("当前路由表大小:",o.length),o.find(r=>r.path===t||r.path===a))f.push({path:t,replace:!1}).then(()=>{console.log("%c【导航成功】","color: #2ecc71; font-weight: bold")}).catch(r=>{console.error("%c【导航失败】","color: #e74c3c; font-weight: bold",r),D(t)});else{console.log("%c【未找到匹配路由】","color: #e74c3c; font-weight: bold");const r=o.filter(m=>m.path.includes(t.split("/").pop()||"")||t.includes(m.path)&&m.path!=="/");r.length>0&&console.log("找到相似路由:",r.map(m=>({路径:m.path,名称:m.name}))),console.log("%c【尝试刷新路由】","color: #f39c12; font-weight: bold"),await U(),f.getRoutes().some(m=>m.path===t||m.path===a)?(console.log("%c【刷新后找到路由】","color: #2ecc71; font-weight: bold"),setTimeout(()=>{f.push({path:t,replace:!1}).then(()=>{console.log("%c【导航成功】","color: #2ecc71; font-weight: bold")}).catch(m=>{console.error("%c【导航失败】","color: #e74c3c; font-weight: bold",m),D(t)})},100)):(console.error("%c【路由不存在】","color: #e74c3c; font-weight: bold","即使刷新路由后也未找到目标路由"),D(t))}}catch(s){console.error("导航到子系统错误:",s)}},D=n=>{console.log("尝试备用方案: 先到首页再到目标"),f.push("/home").then(()=>{setTimeout(()=>{console.log("从首页跳转到目标路径"),f.push(n)},100)}).catch(()=>{confirm("系统导航出现问题，是否刷新页面?")&&(window.location.href=`/#${n}`,setTimeout(()=>{window.location.reload()},100))})},H=async()=>{try{i.user||(await i.GetInfo(),await Promise.all([i.GetBtnPerms(),i.InitTenantInfo()])),await U(),console.log("系统路由加载完成"),await J();const n=f.getRoutes();console.log("初始化后所有路由列表:",n),E.value=!0}catch(n){console.error("加载路由失败:",n)}},J=async()=>{var n,e;try{const s=(e=(n=i.user)==null?void 0:n.tenantId)==null?void 0:e.id;if(!s)return;const h=te(s),a=await ne(h);if(a.data){const $=a.data.slice(0,4).map(t=>({...t,subsystems:[]}));d.value=$,console.log("获取到大系统列表:",$),await Promise.all($.map(async t=>{try{console.log(`获取系统 ${t.id} 的子系统基本信息...`);const o=await V(t.id);if(o.data){const v=d.value.findIndex(r=>r.id===t.id);v!==-1&&(b[t.id]=o.data,d.value[v].subsystems=o.data.filter(r=>r.children&&r.children.length).slice(0,2),console.log(`获取到系统 ${t.id} 的子系统:`,d.value[v].subsystems))}}catch(o){console.error(`获取系统 ${t.id} 的子系统基本信息失败:`,o)}}))}}catch(s){console.error("获取大系统列表失败:",s)}},y=n=>{g[n]=!0},p=n=>{g[n]=!1},W=n=>{if(!n){console.warn("路径未定义或不需要导航");return}try{if(typeof n=="object"&&n.path){const e={path:n.path,query:n.query||{}};console.log("导航到路径:",e.path,"参数:",e.query),f.push(e)}else console.log("导航到路径:",n),f.push(n)}catch(e){console.error("导航错误:",e)}};return Z(async()=>{console.log("导航组件已挂载"),await H()}),ee(()=>{c&&c()}),(n,e)=>{var s,h,a,$;return E.value?(M(),w("div",ae,[u("canvas",{ref_key:"bgCanvas",ref:I,id:"bg-canvas",class:"background-canvas"},null,512),u("div",{class:C(["btn-1",{active:g.btn1}]),onMouseenter:e[0]||(e[0]=t=>y("btn1")),onMouseleave:e[1]||(e[1]=t=>p("btn1")),onClick:e[2]||(e[2]=t=>W(S.生产一张图))},"生产一张图",34),u("div",{class:C(["btn-2",{active:g.btn2}]),onMouseenter:e[3]||(e[3]=t=>y("btn2")),onMouseleave:e[4]||(e[4]=t=>p("btn2")),onClick:e[5]||(e[5]=t=>W(S.营收一张图))},"营收一张图",34),(M(!0),w(B,null,F(((s=d.value[0])==null?void 0:s.subsystems)||[],(t,o)=>(M(),w("div",{key:"cylinder-1-"+o,class:C(["small-cylinder",{active:g["smallCylinder1"+(o+1)],["small-cylinder-1-"+(o+1)]:!0}]),onMouseenter:v=>y("smallCylinder1"+(o+1)),onMouseleave:v=>p("smallCylinder1"+(o+1)),onClick:_(v=>{var r;return A((r=d.value[0])==null?void 0:r.id,t)},["stop"])},[u("div",ie,k(t.meta.title),1),u("div",ce,k(t.meta.title),1)],42,re))),128)),(M(!0),w(B,null,F(((h=d.value[1])==null?void 0:h.subsystems)||[],(t,o)=>(M(),w("div",{key:"cylinder-2-"+o,class:C(["small-cylinder",{active:g["smallCylinder2"+(o+1)],["small-cylinder-2-"+(o+1)]:!0}]),onMouseenter:v=>y("smallCylinder2"+(o+1)),onMouseleave:v=>p("smallCylinder2"+(o+1)),onClick:_(v=>{var r;return A((r=d.value[1])==null?void 0:r.id,t)},["stop"])},[u("div",ue,k(t.meta.title),1),u("div",fe,k(t.meta.title),1)],42,de))),128)),(M(!0),w(B,null,F(((a=d.value[2])==null?void 0:a.subsystems)||[],(t,o)=>(M(),w("div",{key:"cylinder-3-"+o,class:C(["small-cylinder",{active:g["smallCylinder3"+(o+1)],["small-cylinder-3-"+(o+1)]:!0}]),onMouseenter:v=>y("smallCylinder3"+(o+1)),onMouseleave:v=>p("smallCylinder3"+(o+1)),onClick:_(v=>{var r;return A((r=d.value[2])==null?void 0:r.id,t)},["stop"])},[u("div",ve,k(t.meta.title),1),u("div",ge,k(t.meta.title),1)],42,he))),128)),(M(!0),w(B,null,F((($=d.value[3])==null?void 0:$.subsystems)||[],(t,o)=>(M(),w("div",{key:"cylinder-4-"+o,class:C(["small-cylinder",{active:g["smallCylinder4"+(o+1)],["small-cylinder-4-"+(o+1)]:!0}]),onMouseenter:v=>y("smallCylinder4"+(o+1)),onMouseleave:v=>p("smallCylinder4"+(o+1)),onClick:_(v=>{var r;return A((r=d.value[3])==null?void 0:r.id,t)},["stop"])},[u("div",ye,k(t.meta.title),1),u("div",pe,k(t.meta.title),1)],42,me))),128)),u("div",{class:C(["cylinder-1",{active:g.cylinder1}]),onMouseenter:e[6]||(e[6]=t=>y("cylinder1")),onMouseleave:e[7]||(e[7]=t=>p("cylinder1")),onClick:e[8]||(e[8]=t=>W(S.生产一张图))},null,34),u("div",{class:C(["cylinder-2",{active:g.cylinder2}]),onMouseenter:e[9]||(e[9]=t=>y("cylinder2")),onMouseleave:e[10]||(e[10]=t=>p("cylinder2")),onClick:e[11]||(e[11]=t=>W(S.营收一张图))},null,34),u("div",{class:C(["cylinder-3",{active:g.cylinder3}]),onMouseenter:e[12]||(e[12]=t=>y("cylinder3")),onMouseleave:e[13]||(e[13]=t=>p("cylinder3"))},null,34),u("div",{class:C(["cylinder-4",{active:g.cylinder4}]),onMouseenter:e[14]||(e[14]=t=>y("cylinder4")),onMouseleave:e[15]||(e[15]=t=>p("cylinder4"))},null,34),e[16]||(e[16]=u("div",{class:"logo"},"瓜州水务系统",-1))])):(M(),w("div",$e,e[17]||(e[17]=[u("div",{class:"loading-spinner"},null,-1),u("div",{class:"loading-text"},"系统加载中...",-1)])))}}},Re=O(we,[["__scopeId","data-v-e12953c2"]]);export{Re as default};
