export const resolveValue = (config: IFormItem, val?: any) => {
  switch (config.type) {
    case 'checkbox':
    case 'cascader':
    case 'daterange':
    case 'datetimerange':
      return val ? (val instanceof Array ? val : [val]) : []
    case 'select':
      return config.multiple ? resolveSelectMultiple(config, val) : val
    default:
      return val
  }
}
export const deresolveValue = (config: IFormItem, val?: any) => {
  switch (config.type) {
    case 'user':
      return deresolveUser(config, val)
    case 'select':
      return config.multiple ? deresolveMultiple(config, val) : val
    default:
      return val
  }
}

const resolveSelectMultiple = (config: IFormSelect, val?: any) => {
  if (val !== 0 && !val) return []

  if (val instanceof Array) return val
  if (typeof val === 'string') return val.split(',')
  return [val]
}
const deresolveMultiple = (config: IFormSelect, val?: any) => {
  if (!val) return config.returnType === 'arr' ? [] : val

  if (val instanceof Array) {
    return config.returnType === 'str' ? val.join(',') : val
  }
  return config.returnType === 'str' ? val : val.split(',')
}
export const deresolveUser = (config: IFormUser, val?: any) => {
  if (!val) return val

  if (val instanceof Array) {
    return val.map(item => item.id).join(',')
  }
  return val
}
