package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("wx_interface_config")
public class WxInterfaceConfig {
    // id
    private String id;

    // 用户信息获取地址
    private String userInfoApi;

    // 欠费缴费接口
    private String billInfoApi;

    // 销账接口
    private String payApi;

    // 对账接口
    private String checkApi;

    // 缴费记录查询接口
    private String paymentRecordApi;

    // 模板推送接口
    private String messageSendApi;

    // 客户id
    private String tenantId;

}
