package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseDrawingBoardManagementService;
import org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDrawingBoardManagementPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-画板管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-画板管理")
@RestController
@RequestMapping("api/base/drawing/management")
public class BaseDrawingBoardManagementController extends BaseController {

    @Autowired
    private IBaseDrawingBoardManagementService baseDrawingBoardManagementService;

    /**
     * 查询平台管理-画板管理列表
     */
    @MonitorPerformance(description = "平台管理-查询画板管理列表")
    @ApiOperation(value = "查询画板管理列表")
    @GetMapping("/list")
    public IstarResponse list(BaseDrawingBoardManagementPageRequest baseDrawingBoardManagement) {
        return IstarResponse.ok(baseDrawingBoardManagementService.selectBaseDrawingBoardManagementList(baseDrawingBoardManagement));
    }

//    /**
//     * 导出平台管理-画板管理列表
//     */
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, BaseDrawingBoardManagement baseDrawingBoardManagement) {
//        List<BaseDrawingBoardManagement> list = baseDrawingBoardManagementService.selectBaseDrawingBoardManagementList(baseDrawingBoardManagement);
//        ExcelUtil<BaseDrawingBoardManagement> util = new ExcelUtil<BaseDrawingBoardManagement>(BaseDrawingBoardManagement.class);
//        util.exportExcel(response, list, "平台管理-画板管理数据");
//    }

    /**
     * 获取平台管理-画板管理详细信息
     */
    @MonitorPerformance(description = "平台管理-查询画板管理详情")
    @ApiOperation(value = "查询画板管理详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseDrawingBoardManagementService.selectBaseDrawingBoardManagementById(id));
    }

    /**
     * 新增平台管理-画板管理
     */
    @MonitorPerformance(description = "平台管理-新增画板管理")
    @ApiOperation(value = "新增画板管理")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseDrawingBoardManagement baseDrawingBoardManagement) {
        return IstarResponse.ok(baseDrawingBoardManagementService.insertBaseDrawingBoardManagement(baseDrawingBoardManagement));
    }

    /**
     * 修改平台管理-画板管理
     */
    @MonitorPerformance(description = "平台管理-修改画板管理")
    @ApiOperation(value = "修改画板管理")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseDrawingBoardManagement baseDrawingBoardManagement) {
        return IstarResponse.ok(baseDrawingBoardManagementService.updateBaseDrawingBoardManagement(baseDrawingBoardManagement));
    }

    /**
     * 删除平台管理-画板管理
     */
    @MonitorPerformance(description = "平台管理-删除画板管理")
    @ApiOperation(value = "删除画板管理")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseDrawingBoardManagementService.deleteBaseDrawingBoardManagementByIds(ids));
    }
}
