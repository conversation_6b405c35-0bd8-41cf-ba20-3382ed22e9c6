<template>
  <div></div>
</template>
<script lang="ts" setup>
import { getGraphicLayer, gotoAndHighLight } from '@/utils/MapHelper'

const props = defineProps<{ layerid: string; layertitle: string }>()
const view = inject('view') as __esri.MapView | undefined
const gotoFeature = async (target: __esri.GoToTarget2D, option?: __esri.GoToOptions2D) => {
  const graphicsLayer = getGraphicLayer(view, { id: props.layerid, title: props.layertitle })
  graphicsLayer?.removeAll()
  graphicsLayer?.add(target)
  await gotoAndHighLight(view, target, option)
}
defineExpose({
  gotoFeature
})
</script>
<style lang="scss" scoped></style>
