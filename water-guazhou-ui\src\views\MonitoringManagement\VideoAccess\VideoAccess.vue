<!-- 视频监控管理-视频接入 -->
<template>
  <TreeBox>
    <template #tree>
      <div class="tree-box">
        <el-scrollbar>
          <SLTree
            style="width: calc(100% - 5px)"
            ref="refTree"
            :tree-data="TreeData"
          ></SLTree>
        </el-scrollbar>
      </div>
    </template>
    <template #default>
      <div style="height: 100%; position: relative">
        <el-row :gutter="60" style="height: 100%">
          <el-col :span="12" style="height: 100%">
            <CardTable
              style="height: 100%"
              :config="acceptTableConfig"
              title="已接入摄像头"
            >
              <template #title>
                <div class="card-table-title">
                  <div>分组下摄像头</div>
                </div>
                <el-button
                  type="success"
                  style="margin-right: 10px"
                  @click="handSort"
                  >重新排序</el-button
                >
                <el-input
                  v-model="state.leftName"
                  placeholder="请输入关键字"
                  style="width: 300px; margin-right: 10px"
                  @blur="refreshDataleft"
                ></el-input>
              </template>
            </CardTable>
          </el-col>
          <el-col :span="12" style="height: 100%">
            <CardTable style="height: 100%" :config="sendTableConfig">
              <template #title>
                <div class="card-table-title">
                  <div>待接入摄像头</div>
                </div>
                <el-input
                  v-model="state.rightName"
                  placeholder="请输入关键字"
                  style="width: 300px; margin-right: 10px"
                  @blur="getNoAccessVideoList"
                ></el-input>
              </template>
            </CardTable>
          </el-col>
        </el-row>
        <div class="buttons">
          <el-button @click="addVideo(false)">
            <el-icon>
              <Right />
            </el-icon>
          </el-button>
          <br />
          <el-button @click="addVideo(true)">
            <el-icon>
              <Back />
            </el-icon>
          </el-button>
        </div>
      </div>
      <DialogForm ref="refForm" :config="addOrUpdateConfig">
        <template #fieldSlot="{ config, row }">
          <SuperMapProvider
            v-if="config.field === 'coordinate'"
            style="width: 100%; height: 300px"
          >
            <SuperMapContainer
              :default-mouse-status="'crosshair'"
              :hide-control="true"
              :hide-legend="true"
            >
              <SuperPointPicker
                :disabled="!!config.readonly"
                ref="refPointPicker"
                @pick="handlePick"
              >
              </SuperPointPicker>
            </SuperMapContainer>
          </SuperMapProvider>
        </template>
      </DialogForm>
      <SLDrawer ref="refSort" :config="sortConfig">
        <Sort
          :groupId="TreeData.currentProject?.id || ''"
          :projectId="state.projectId || ''"
        ></Sort>
      </SLDrawer>
    </template>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Right, Back } from '@element-plus/icons-vue';
import { CommonRequest, GeneralTable } from '@/utils/GeneralProcessing';
import Sort from './sort.vue';
import {
  getCameraGroupTree,
  getGroupVideoList,
  saveCamera,
  getVideo
} from '@/api/video';
import { useBusinessStore } from '@/store';
import { ElMessage } from 'element-plus';
import { isJSON, traverse } from '@/utils/GlobalHelper';

const type = [
  { label: '自建', value: '1' },
  { label: '天网', value: '2' },
  { label: '利旧', value: '3' }
];

const typeValue = ref('1');

const visible = ref(false);

const refSort = ref<ISLDrawerIns>();
const refForm = ref<IDialogFormIns>();
const refPointPicker = ref<any>();

const state = reactive({
  projectId: '',
  videoLeft: [] as any[],
  videoRight: [] as any[],
  leftName: '',
  rightName: '',
  search: '1'
});

const TreeData = reactive<SLTreeConfig>({
  title: '分组管理',
  isFilterTree: true,
  data: [],
  extraFilters: [
    {
      type: 'select-tree',
      field: 'projectId',
      clearable: false,
      checkStrictly: true,
      options: useBusinessStore().projectList,
      onChange: (val) => {
        state.projectId = val;
        getNoAccessVideoList();
        refreshTree();
      }
    }
  ],
  treeNodeHandleClick: (data) => {
    TreeData.currentProject = data;
    refreshDataleft();
  }
});

// 发送表格
const sendTableConfig = reactive<ICardTable>({
  title: ' ',
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  handleSelectChange: (val) => {
    state.videoRight = val;
  },
  selectList: [],
  columns: [
    { label: '名称', prop: 'name', minWidth: '400px' }
    // { label: '编号', prop: 'indexCode', minWidth: '210px' },
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

// 接收表格
const acceptTableConfig = reactive<ICardTable>({
  title: ' ',
  defaultExpandAll: true,
  indexVisible: true,
  handleSelectChange: (val) => {
    state.videoLeft = val;
  },
  selectList: [],
  rowKey: 'id',
  columns: [{ label: '名称', prop: 'name', minWidth: '250px' }],
  operationWidth: 70,
  operations: [
    {
      text: '编辑',
      perm: true,
      click: (val) => {
        const key = val.coordinate?.split(',') || ['', ''];
        addOrUpdateConfig.defaultValue = {
          ...val,
          lng: key[1] || '',
          lat: key[0] || ''
        };
        refForm.value?.openDialog();
        setTimeout(() => {
          val.coordinate && drawPoint(val.coordinate);
        }, 0);
      }
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '编辑',
  dialogWidth: '1000px',
  labelWidth: '110px',
  submitting: false,
  submit: (params: any) => {
    if (params.lng && params.lat) {
      params.coordinate = `${params.lat},${params.lng}`;
    }
    // CommonRequest(params, putCamera, addOrUpdateConfig, '编辑摄像头成功').then(
    //   () => {
    //     refreshDataleft();
    //     refForm.value?.closeDialog();
    //   }
    // );
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 24,
          type: 'input',
          label: '摄像头名称',
          field: 'name',
          rules: [{ required: true, message: '请输入摄像头名称' }]
        },
        {
          xs: 24,
          type: 'input',
          label: '编号',
          field: 'serialNumber',
          rules: [{ required: true, message: '请输入编号' }]
        },
        {
          xs: 24,
          type: 'select',
          label: '类型',
          field: 'videoType',
          options: type,
          rules: [{ required: true, message: '请输入类型' }]
        },
        {
          type: 'input',
          label: '经度',
          field: 'lng',
          onBlur: () => {
            const key = refForm.value?.refForm?.dataForm;
            if (key.lat && key.lng) {
              if (refForm.value?.refForm?.dataForm) {
                const coordinate = [parseFloat(key.lat), parseFloat(key.lng)];
                refForm.value.refForm.dataForm['coordinate'] = coordinate;
                const coord = coordinate.join(',');
                drawPoint(coord);
                if (key) {
                  key.coordinate = coord;
                  refPointPicker.value?.getMarkerAddress().then((res) => {
                    key.address = res;
                  });
                }
              }
            } else {
              ElMessage.warning('请先输入经纬度');
            }
          },
          rules: [{ required: true, message: '请输入纬度' }]
        },
        {
          type: 'input',
          label: '纬度',
          field: 'lat',
          onBlur: () => {
            const key = refForm.value?.refForm?.dataForm;
            if (key.lat && key.lng) {
              if (refForm.value?.refForm?.dataForm) {
                const coordinate = [parseFloat(key.lat), parseFloat(key.lng)];
                refForm.value.refForm.dataForm['coordinate'] = coordinate;
                const coord = coordinate.join(',');
                drawPoint(coord);
                if (key) {
                  key.coordinate = coord;
                  refPointPicker.value?.getMarkerAddress().then((res) => {
                    key.address = res;
                  });
                }
              }
            } else {
              ElMessage.warning('请先输入经纬度');
            }
          },
          rules: [{ required: true, message: '请输入纬度' }]
        },
        { type: 'form-map', showInput: true, field: 'coordinate' }
      ]
    }
  ]
});

const drawPoint = (coordinate: string) => {
  const coord = coordinate
    .split(',')
    .map((item) => parseFloat(item)) as L.LatLngTuple;
  refPointPicker.value?.setMarker(coord);
  refPointPicker.value?.zoomTo();
};

const handlePick = (e: L.LeafletMouseEvent) => {
  const coord = [e.latlng.lat, e.latlng.lng].join(',');
  const formData = refForm.value?.refForm?.dataForm;
  if (formData) {
    formData.coordinate = coord;
    formData['lat'] = e.latlng.lat;
    formData['lng'] = e.latlng.lng;
    refPointPicker.value?.getMarkerAddress().then((res) => {
      formData.address = res;
    });
  }
};

function addVideo(status: boolean) {
  visible.value = false;
  if (status) {
    if (!TreeData.currentProject) {
      ElMessage.warning('请选择分组');
      return;
    }
    const videoList = [...acceptTableConfig.dataList, ...state.videoRight];
    const params = {
      projectId: state.projectId,
      groupId: TreeData.currentProject.id,
      videoList: videoList.map((item, i) => {
        return {
          ...item,
          orderNum: i
        };
      })
    };
    if (params.videoList.length === 0) {
      ElMessage.warning('请选择要添加的摄像头');
      return;
    }
    CommonRequest(params, saveCamera, {}, '添加成功').then(() => {
      getNoAccessVideoList();
      refreshDataleft();
      sendTableConfig.selectList = [];
      acceptTableConfig.selectList = [];
      refreshTree();
    });
  } else {
    const videoList = state.videoLeft;
    const params = {
      projectId: state.projectId,
      groupId: '',
      videoList: videoList.map((item, i) => {
        return {
          ...item,
          orderNum: i
        };
      })
    };
    if (params.videoList.length === 0) {
      ElMessage.warning('请选择要移除的摄像头');
      return;
    }
    CommonRequest(params, saveCamera, {}, '移除成功').then(() => {
      getNoAccessVideoList();
      refreshDataleft();
      sendTableConfig.selectList = [];
      acceptTableConfig.selectList = [];
      refreshTree();
    });
  }
}

// 获取待接入视频
const getNoAccessVideoList = () => {
  sendTableConfig.loading = true;
  getVideo(state.projectId, { isBind: 0 })
    .then((res) => {
      sendTableConfig.dataList = res.data || [];
      sendTableConfig.loading = false;
    })
    .catch((error) => {
      sendTableConfig.loading = false;
      ElMessage.warning(error);
    });
};

const refreshTree = () => {
  const projectId = state.projectId;
  GeneralTable({ projectId }, getCameraGroupTree).then((res) => {
    TreeData.data = traverse(res.data, 'children');
  });
};

// 一个函数用于从对象数组中删除满足特定条件的对象
function removeItemsBySubsets<T>(
  array: T[],
  subsets: Partial<T>[],
  keysToCheck: (keyof T)[]
): T[] {
  // 使用filter方法过滤掉满足条件的对象
  return array.filter(
    (item) =>
      !subsets.some((subset) =>
        keysToCheck.every((key) => subset[key] === item[key])
      )
  );
}

function traverseFZ(val: any, children: 'children' | string = 'children') {
  // 对传入的值进行映射处理，递归转换每个对象
  val.map((obj) => {
    if (obj) {
      // 遍历keys对象，根据规则转换当前对象的属性
      obj['name'] = obj['name'] + ` (${obj['num']})`;
      // 如果当前对象包含子元素，则对子元素进行相同的处理
      if (obj[children] && obj[children].length) {
        traverseFZ(obj[children], children);
      }
    }
    return obj;
  });
  return val;
}

// 明细弹框
const sortConfig = reactive<IDrawerConfig>({
  title: '排序',
  group: [],
  modalClass: 'lightColor',
  width: '80%'
});

const handSort = () => {
  refSort.value?.openDrawer();
};

const refreshDataleft = async () => {
  state.videoLeft = [];
  const params: any = {
    groupId: TreeData.currentProject.id,
    projectId: state.projectId || '',
    name: state.leftName
  };
  GeneralTable(params, getGroupVideoList, { table: acceptTableConfig });
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.buttons {
  width: 60px;
  height: 100px;
  position: absolute;
  top: 50%;
  left: calc(50% - 30px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
}

.card-table-title {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.tree-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
