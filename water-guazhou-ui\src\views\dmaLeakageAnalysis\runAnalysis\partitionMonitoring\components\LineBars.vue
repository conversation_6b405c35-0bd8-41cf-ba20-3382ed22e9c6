<template>
  <div
    ref="refDiv"
    class="line-bars"
  >
    <div class="left">
      <VChart
        ref="refChart1"
        :option="state.prosale"
      ></VChart>
    </div>
    <div class="right">
      <VChart
        ref="refChart2"
        :option="state.flow"
      ></VChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts'

const props = defineProps<{
  dataDma: Record<string, any>
}>()
const state = reactive<{
  prosale: any
  flow: any
}>({
  prosale: null,
  flow: null
})
const initPSOption = (
  xData: string[] = [],
  yData1: number[] = [],
  yData2: number[] = [],
  yData3: number[] = []
) => {
  const colors = ['#337ecc', '#00bcd4', '#f56c6c']
  const option = {
    color: colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: 30,
      top: 70,
      right: 40,
      bottom: 30,
      containLabel: true
    },
    legend: {
      right: 'center',
      top: 20
    },
    xAxis: [
      {
        type: 'category',
        axisTick: {
          alignWithLabel: true
        },
        // prettier-ignore
        data: xData
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: 'm³',
        splitLine: {
          show: false
        },
        axisLine: {
          show: true
        }
      },
      {
        type: 'value',
        name: '%',
        position: 'right',
        splitLine: {
          show: false
        },
        axisLine: {
          show: true
        }
      }
    ],
    series: [
      {
        name: '供水量',
        type: 'bar',
        data: yData1,
        itemStyle: {
          color: colors[0]
        }
      },
      {
        name: '售水量',
        type: 'bar',
        data: yData2,
        itemStyle: {
          color: colors[1]
        }
      },
      {
        name: '产销差',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        itemStyle: {
          color: colors[2]
        },
        data: yData3
      }
    ]
  }
  return option
}
const initFlowOption = (
  xData: string[] = [],
  yData1: number[] = [],
  yData2: number[] = []
) => {
  const colors = ['#337ecc', '#00bcd4', '#f56c6c']
  const option = {
    color: colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: 30,
      top: 70,
      right: 40,
      bottom: 30,
      containLabel: true
    },
    legend: {
      right: 'center',
      top: 20
    },
    xAxis: [
      {
        type: 'category',
        axisTick: {
          alignWithLabel: true
        },
        // prettier-ignore
        data: xData
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: 'm³',
        splitLine: {
          show: false
        },
        axisLine: {
          show: true
        }
      },
      {
        type: 'value',
        name: 'm³/h',
        position: 'right',
        splitLine: {
          show: false
        },
        axisLine: {
          show: true
        }
      }
    ],
    series: [
      {
        name: '瞬时流量',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        itemStyle: {
          color: colors[2]
        },
        data: yData1
      },
      {
        name: '净累计',
        type: 'bar',
        data: yData2,
        itemStyle: {
          color: colors[1]
        }
      }
    ]
  }
  return option
}
const refreshData = () => {
  const dmaData = props.dataDma
  state.prosale = initPSOption(
    dmaData?.yearNRW?.x,
    dmaData?.yearNRW?.supply,
    dmaData?.yearNRW?.sale,
    dmaData?.yearNRW?.nrw
  )
  state.flow = initFlowOption(
    dmaData?.dayFlow?.x,
    dmaData?.dayFlow?.flow,
    dmaData?.dayFlow?.total
  )
}
const refDiv = ref<HTMLDivElement>()
const refChart1 = ref<any>()
const refChart2 = ref<any>()

const resize = useDetector()
onMounted(() => {
  resize.listenToMush(refDiv.value, () => {
    refChart1.value?.resize()
    refChart2.value?.resize()
  })
  refreshData()
})
watch(
  () => props.dataDma,
  () => {
    refreshData()
  }
)
</script>
<style lang="scss" scoped>
.line-bars {
  width: 100%;
  height: 100%;
  display: flex;
  .left {
    width: 50%;
    height: 100%;
  }
  .right {
    width: 50%;
    height: 100%;
  }
}
</style>
