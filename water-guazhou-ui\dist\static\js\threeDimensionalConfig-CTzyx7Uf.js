import{_ as B}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as L}from"./CardTable-rdWOL4_6.js";import{_ as k}from"./CardSearch-CB_HNR-Q.js";import{z as f,C as P,c as m,r as _,b as s,S as O,o as V,g as q,n as F,q as y}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function z(c){return f({url:"/api/base/three/configuration/list",method:"get",params:c})}function E(c){return f({url:"/api/base/three/configuration/getDetail",method:"get",params:{id:c}})}function x(c){return f({url:"/api/base/three/configuration/add",method:"post",data:c})}function C(c){return f({url:"/api/base/three/configuration/edit",method:"post",data:c})}function M(c){return f({url:"/api/base/three/configuration/deleteIds",method:"delete",data:c})}function N(){return f({url:"/api/base/scheme/management/getAllBaseSchemeManagement",method:"get"})}function j(){return f({url:"/api/base/tile/configuration/getAllBaseTileConfiguration",method:"get"})}function W(){return f({url:"/api/base/vector/configuration/getAllBaseVectorConfiguration",method:"get"})}const Z={class:"wrapper"},$={__name:"threeDimensionalConfig",setup(c){const I=m(),u=m(),p=m([]),h=m([]),v=m([]),A=_({labelWidth:"100px",filters:[{type:"input",label:"方案ID",field:"schemeId",placeholder:"请输入方案ID",onChange:()=>g()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>g()},{perm:!0,type:"primary",text:"新增",click:()=>T()},{perm:!0,type:"danger",text:"批量删除",click:()=>D()}]}],defaultParams:{}}),l=_({columns:[{label:"方案",prop:"schemeId"},{label:"瓦片数据",prop:"tileId"},{label:"矢量数据",prop:"vectorIds",showOverflowTooltip:!0},{label:"创建时间",prop:"createTime",formatter:e=>e.createTime?new Date(e.createTime).toLocaleString():"-"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>w(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>T(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>D(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{l.pagination.page=e,g()},handleSize:e=>{l.pagination.limit=e,g()}},handleSelectChange:e=>{l.selectList=e||[]}}),t=_({title:"新增三维数据配置",group:[{fields:[{type:"select",label:"方案",field:"schemeId",placeholder:"请选择方案",options:p,rules:[{required:!0,message:"请选择方案"}]},{type:"select",label:"瓦片数据配置",field:"tileId",placeholder:"请选择瓦片数据配置",options:h,rules:[{required:!0,message:"请选择瓦片数据配置"}]},{type:"select",label:"矢量数据配置",field:"vectorIds",placeholder:"请选择矢量数据配置",options:v,multiple:!0,rules:[{required:!0,message:"请选择矢量数据配置"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var a;try{Array.isArray(e.vectorIds)&&(e.vectorIds=e.vectorIds.join(",")),e.id?(await C(e),s.success("修改成功")):(await x(e),s.success("新增成功")),(a=u.value)==null||a.closeDialog(),g()}catch{s.error("操作失败")}}}),S=async()=>{try{const[e,a,r]=await Promise.all([N(),j(),W()]);if(console.log("下拉数据接口响应:",{schemeRes:e,tileRes:a,vectorRes:r}),e!=null&&e.data){const o=Array.isArray(e.data)?e.data:e.data.data||[];p.value=o.map(i=>({label:i.name||i.schemeName||i.id,value:i.id}))}if(a!=null&&a.data){const o=Array.isArray(a.data)?a.data:a.data.data||[];h.value=o.map(i=>({label:i.name||i.tileName||i.id,value:i.id}))}if(r!=null&&r.data){const o=Array.isArray(r.data)?r.data:r.data.data||[];v.value=o.map(i=>({label:i.name||i.vectorName||i.id,value:i.id}))}console.log("处理后的下拉选项:",{schemeOptions:p.value,tileOptions:h.value,vectorOptions:v.value})}catch(e){console.error("获取下拉数据失败:",e),p.value=[{label:"方案1",value:"scheme_001"},{label:"方案2",value:"scheme_002"},{label:"方案3",value:"scheme_003"}],h.value=[{label:"瓦片配置1",value:"tile_001"},{label:"瓦片配置2",value:"tile_002"},{label:"瓦片配置3",value:"tile_003"}],v.value=[{label:"矢量配置1",value:"vector_001"},{label:"矢量配置2",value:"vector_002"},{label:"矢量配置3",value:"vector_003"},{label:"矢量配置4",value:"vector_004"},{label:"矢量配置5",value:"vector_005"}],s.error("获取下拉数据失败，使用模拟数据")}},b=()=>{t.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),t.showSubmit=!0,t.showCancel=!0,t.cancelText="取消",t.submitText="确定",t.submit=async e=>{var a;try{Array.isArray(e.vectorIds)&&(e.vectorIds=e.vectorIds.join(",")),e.id?(await C(e),s.success("修改成功")):(await x(e),s.success("新增成功")),(a=u.value)==null||a.closeDialog(),g()}catch{s.error("操作失败")}},t.footerBtns=void 0},T=e=>{var r;b(),t.title=e?"编辑三维数据配置":"新增三维数据配置";let a={};e&&(a={...e},a.vectorIds&&typeof a.vectorIds=="string"&&(a.vectorIds=a.vectorIds.split(",").map(o=>o.trim()))),t.defaultValue=a,(r=u.value)==null||r.openDialog()},w=async e=>{var r,o;const a={id:e.id||"1",schemeId:e.schemeId||"scheme_001",tileId:e.tileId||"tile_001",vectorIds:e.vectorIds||"vector_001,vector_002,vector_003",createTime:e.createTime||new Date().toISOString()};try{console.log("获取详情，行数据:",e);const i=await E(e.id);console.log("详情API响应:",i);let n=null;i.data?i.data.data?n=i.data.data:n=i.data:i&&(n=i),console.log("解析后的详情数据:",n),n||(console.log("使用模拟详情数据"),n=a),b(),t.title="三维数据配置详情",n.vectorIds&&typeof n.vectorIds=="string"&&(n.vectorIds=n.vectorIds.split(",").map(d=>d.trim())),t.defaultValue={...n},console.log("设置的详情数据:",t.defaultValue),t.group[0].fields.forEach(d=>{d.type==="select"&&(d.readonly=!0),d.disabled=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var d;(d=u.value)==null||d.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(r=u.value)==null||r.openDialog()}catch(i){console.error("获取详情失败:",i),console.log("API调用失败，使用模拟详情数据"),b(),t.title="三维数据配置详情",a.vectorIds&&typeof a.vectorIds=="string"&&(a.vectorIds=a.vectorIds.split(",").map(n=>n.trim())),t.defaultValue={...a},t.group[0].fields.forEach(n=>{n.type==="select"&&(n.readonly=!0),n.disabled=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var n;(n=u.value)==null||n.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(o=u.value)==null||o.openDialog(),s.error("API调用失败，当前显示模拟数据")}},D=e=>{O("确定删除？","删除提示").then(async()=>{var a;try{const r=e?[e.id]:((a=l.selectList)==null?void 0:a.map(i=>i.id))||[];if(!r.length){s.warning("请选择要删除的数据");return}(await M(r)).data?(s.success("删除成功"),g()):s.error("删除失败")}catch{s.error("删除失败")}}).catch(()=>{})},g=async()=>{var a;const e=[{id:"1",schemeId:"scheme_001",tileId:"tile_001",vectorIds:"vector_001,vector_002",createTime:"2024-01-15T10:30:00Z"},{id:"2",schemeId:"scheme_002",tileId:"tile_002",vectorIds:"vector_003,vector_004,vector_005",createTime:"2024-01-16T14:20:00Z"}];try{const r=(a=I.value)==null?void 0:a.queryParams;console.log("请求参数:",{page:l.pagination.page,size:l.pagination.limit,...r||{}});const o=await z({page:l.pagination.page,size:l.pagination.limit,...r||{}});console.log("API响应数据:",o),o.data?o.data.records?(l.dataList=o.data.records||[],l.pagination.total=o.data.total||0):o.data.data&&o.data.data.records?(l.dataList=o.data.data.records||[],l.pagination.total=o.data.data.total||0):Array.isArray(o.data)?(l.dataList=o.data,l.pagination.total=o.data.length):Array.isArray(o.data.data)?(l.dataList=o.data.data,l.pagination.total=o.data.data.length):(console.warn("未知的数据结构:",o.data),l.dataList=[],l.pagination.total=0):Array.isArray(o)?(l.dataList=o,l.pagination.total=o.length):(console.warn("无法解析的响应格式:",o),l.dataList=[],l.pagination.total=0),console.log("解析后的数据:",l.dataList),console.log("总数:",l.pagination.total),l.dataList.length===0&&(console.log("使用模拟数据进行测试"),l.dataList=e,l.pagination.total=e.length)}catch(r){console.error("获取数据失败:",r),console.log("API调用失败，使用模拟数据"),l.dataList=e,l.pagination.total=e.length,s.error("API调用失败，当前显示模拟数据")}};return V(()=>{S(),g()}),(e,a)=>{const r=k,o=L,i=B;return q(),F("div",Z,[y(r,{ref_key:"refSearch",ref:I,config:A},null,8,["config"]),y(o,{class:"card-table",config:l},null,8,["config"]),y(i,{ref_key:"refDialogForm",ref:u,config:t},null,8,["config"])])}}},X=P($,[["__scopeId","data-v-c21b1640"]]);export{X as default};
