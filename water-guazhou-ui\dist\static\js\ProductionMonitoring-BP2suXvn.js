import{_ as pt}from"./Panel-DyoxrWMd.js";import{d as ct,cN as mt,c as J,r as f,a0 as dt,a2 as ut,D as ft,l as U,o as gt,bB as ht,Q as yt,g as v,n as I,h as D,i as _,aB as j,aJ as bt,an as F,d3 as vt,q as B,p as _t,F as wt,_ as kt,aq as Lt,C as Rt}from"./index-r0dFAfgr.js";import{w as E}from"./Point-WxyopZva.js";import{e as Tt,s as Ct,d as xt,g as It}from"./zhandian-YaGuQZe6.js";import{i as Dt}from"./data-CLo2TII-.js";import{h as Ft,r as Bt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as K}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{b as Pt}from"./ViewHelper-BGCZjxXH.js";import{f as Q}from"./DateFormatter-Bm9a68Ax.js";import{q as St}from"./index-cIaXVz1R.js";import{r as Nt}from"./index-BggOjNGp.js";import{m as z,i as X}from"./map-location-BX7km8Cl.js";import Mt from"./ListWindow-WS05QqV0.js";import"./v4-SoommWqA.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./PopLayout-BP55MvL7.js";const Wt={key:0,class:"infowindow-container"},At={class:"table-box"},Vt=ct({__name:"ProductionMonitoring",props:{view:{},telport:{}},setup(Y){const{proxy:P}=mt(),S=J(),p=Y,g=f({curValues:[],marks:[]}),c={},T=J(),u=f({dataList:[],columns:[{label:"名称",prop:"name"}],handleRowClick:t=>{var e,o;u.currentRow=t,A(t),(e=S.value)==null||e.Open(),(o=p.view)==null||o.goTo(new E({longitude:t.x,latitude:t.y,spatialReference:p.view.spatialReference}))},pagination:{hide:!0}}),C=f({gutter:12,labelPosition:"top",group:[{fields:[{type:"tabs",field:"type",label:"",tabType:"simple",readonly:()=>!!u.loading,tabs:Dt(),onChange:t=>N(t)}]}]}),N=async(t=(o=>(o=(e=>(e=T.value)==null?void 0:e.dataForm)())==null?void 0:o.type)())=>{var l,m,y,k,b,L,R,G,O,q,$;if(!t){t=((k=(y=(m=(l=C.group[0])==null?void 0:l.fields[0])==null?void 0:m.tabs[0])==null?void 0:y.value)==null?void 0:k.toString())||"",(b=T.value)!=null&&b.dataForm&&(T.value.dataForm.type=t);return}u.loading=!0;const a=await Tt({stationType:t,projectId:(R=(L=dt().navSelectedRange)==null?void 0:L.data)==null?void 0:R.id}),r=await ut({type:"stationInfo",key:t});let i=[];try{i=r.data[0]?JSON.parse(r.data[0].value):[]}catch{i=[]}u.columns=Z(i.map(n=>({minWidth:120,label:n.label,prop:n.value}))),nt(),g.marks=[],u.dataList=((G=a.data)==null?void 0:G.map(n=>{var H;const d=(H=n.location)==null?void 0:H.split(",");n.status=n.status||"offline",n.x=d[0]&&parseFloat(d[0]),n.y=d[1]&&parseFloat(d[1]);const lt={title:n.name,visible:!1,x:n.x,y:n.y,attributes:{id:n.stationId,values:[]},symbolConfig:{url:z}};return g.marks.push(lt),n}))||[],(O=c.graphicsLayer)==null||O.removeAll(),(q=c.textLayer)==null||q.removeAll(),it(),u.loading=!1,($=C.group[0].fields[0].options)==null||$.map(n=>{n.data.suffix=n.value===""?a.data.length:n.value==="online"?a.data.filter(d=>d.status==="online").length:n.value==="warning"?a.data.filter(d=>d.status==="warning").length:n.value==="offline"?a.data.filter(d=>d.status==="offline").length:0}),g.curValues=i},Z=t=>[{minWidth:120,label:"名称",prop:"name"},{minWidth:160,label:"时间",prop:"time"},...t||[],{width:70,label:"状态",prop:"pressure_status",formatter:(e,o)=>o==="online"?"在线":"离线"}],x=f({height:250,columns:[{label:"名称",prop:"name"},{label:"值",prop:"value"}],dataList:[],pagination:{hide:!0}}),tt=async t=>{t||(x.dataList=X({}));const e=await Ct(t);x.dataList=X(e.data||{})},h=f({height:300,columns:[{minWidth:100,label:"名称",prop:"propertyName"},{minWidth:140,label:"时间",prop:"collectionTime",formatter:t=>Q(t.collectionTime)},{minWidth:120,label:"监测值",prop:"value"}],dataList:[],pagination:{hide:!0},handleRowClick:t=>{h.currentRow=t,M()}}),et=async t=>{var o;const e=await xt(t.stationId);h.dataList=((o=e.data)==null?void 0:o.map(a=>(a.value=(a.value||"")+" "+(a.unit||""),a)))||[],h.currentRow=h.dataList[0],M()},w=f({height:300,columns:[{label:"设备名称",prop:"name"},{label:"时间",prop:"time"},{label:"压力值",prop:"value"}],dataList:[],pagination:{hide:!0}}),M=async()=>{var r;const t=h.currentRow;if(!t){w.dataList=[];return}const e=w.columns.find(i=>i.prop==="value");e&&(e.label=t.propertyName);const o=ft(t.deviceId),a=t.property;if(o&&a){const i=await St({start:U().subtract(1,"d").valueOf(),end:U().valueOf(),type:"15m",attributes:[o+"."+a]}),s=await Nt(t.deviceId),l=i.data||{},m=[];for(const y in l){const k={time:y,name:(r=s.data)==null?void 0:r.name},b=l[y]||{};let L=0;for(const R in b)if(L++,k.value=b[R]&&b[R]+" "+t.unit||"",L===1)break;m.push(k)}w.dataList=m}else w.dataList=[]},W=f({height:300,columns:[{label:"名称",prop:"name"},{label:"时间",prop:"time"},{label:"报警值",prop:"value"},{label:"状态",prop:"status"}],dataList:[],pagination:{hide:!0}}),at=async t=>{var o,a;const e=await It(t);W.dataList=((a=(o=e.data)==null?void 0:o.data)==null?void 0:a.map(r=>{var s,l;const i=r.details.record||[];return{name:(s=i[0])==null?void 0:s.info,status:r.type,remark:r.deviceName+" "+(((l=i[0])==null?void 0:l.info)||""),time:Q(r.startTs)}}))||[]},ot=f({gutter:0,group:[{fieldset:{type:"underline",desc:"基础属性"},fields:[{type:"table",config:x}]},{fieldset:{type:"underline",desc:"实时数据"},fields:[{type:"table",config:h}]},{fieldset:{type:"underline",desc:"历史数据"},fields:[{type:"table",config:w}]},{fieldset:{type:"underline",desc:"报警信息"},fields:[{type:"table",config:W}]}]}),rt=t=>{t.highLight=!0},A=t=>{tt(t.stationId),at(t.stationId),et(t).then(()=>{V(t.stationId)})},it=()=>{u.dataList.map(t=>{var r,i,s,l,m;const e=new E({longitude:t.x,latitude:t.y,spatialReference:(r=p.view)==null?void 0:r.spatialReference}),o=Ft(e.x,e.y,{picUrl:z,spatialReference:(i=p.view)==null?void 0:i.spatialReference,attributes:{id:t.stationId,row:t}}),a=Bt(e.x,e.y,{text:t.name,spatialReference:(s=p.view)==null?void 0:s.spatialReference,yOffset:-20});(l=c.graphicsLayer)==null||l.add(o),(m=c.textLayer)==null||m.add(a)})},V=t=>{var a;const e=P.$refs["refPop"+t];e!=null&&e.length&&((a=e[0])==null||a.open());const o=g.marks.find(r=>r.attributes.id===t);o&&(o.attributes.values=h.dataList.map(r=>({label:r.propertyName,value:r.value})))},nt=t=>{g.marks.map(o=>{var i;const a=o.attributes.id;if(!a)return;const r=P.$refs["refPop"+a];r!=null&&r.length&&((i=r[0])==null||i.close())})},st=t=>{console.log(t)};return gt(()=>{p.view&&(c.graphicsLayer=K(p.view,{id:"production_monitoring",title:"生产监控"}),c.textLayer=K(p.view,{id:"production_monitoring_poi",title:"生产监控-注记"}),p.view&&Pt(p.view,t=>{t.results.length&&t.results.map(e=>{var o,a,r,i;if(e.type==="graphic"){const s=(a=(o=e.graphic)==null?void 0:o.attributes)==null?void 0:a.id;s&&(V(s),A((i=(r=e.graphic)==null?void 0:r.attributes)==null?void 0:i.row))}})}),ht(()=>{N()}))}),yt(()=>{var t,e;c.graphicsLayer&&((t=p.view)==null||t.map.remove(c.graphicsLayer)),c.textLayer&&((e=p.view)==null||e.map.remove(c.textLayer))}),(t,e)=>{var i,s;const o=kt,a=Lt,r=pt;return v(),I(j,null,[t.telport?(v(),D(vt,{key:0,to:t.telport},[(i=_(g).marks)!=null&&i.length?(v(),I("div",Wt,[(v(!0),I(j,null,bt(_(g).marks,(l,m)=>(v(),D(Mt,{key:m,ref_for:!0,ref:"refPop"+l.attributes.id,view:p.view,config:l,onHighlight:rt,onMore:e[0]||(e[0]=y=>st(y))},null,8,["view","config"]))),128))])):F("",!0)],8,["to"])):F("",!0),B(o,{ref_key:"refForm",ref:T,config:_(C)},null,8,["config"]),_t("div",At,[B(a,{config:_(u)},null,8,["config"])]),t.telport?(v(),D(r,{key:1,ref_key:"refPanel",ref:S,telport:t.telport,draggable:!1,title:(s=_(u).currentRow)==null?void 0:s.name,"custom-class":"production-panel-left"},{default:wt(()=>[B(o,{ref:"refForm_Detail",config:_(ot)},null,8,["config"])]),_:1},8,["telport","title"])):F("",!0)],64)}}}),ca=Rt(Vt,[["__scopeId","data-v-f4376c64"]]);export{ca as default};
