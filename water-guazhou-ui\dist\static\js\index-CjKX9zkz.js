import{d as k,M as _,c as n,r as p,x as O,a8 as r,bQ as S,ar as w,a9 as i,bT as T,D as W,o as C,g,n as V,q as y,F as D,h as E,i as c,_ as F,bz as q,b6 as A}from"./index-r0dFAfgr.js";import{I as U}from"./common-CvK_P_ao.js";import{s as R}from"./equipmentManage-DuoY00aj.js";import{o as N}from"./ledgerManagement-CkhtRd8m.js";import{a as M,g as z}from"./malfunctionRepair-CM_eL_AA.js";import{g as I}from"./OutsideWorkOrder-C6s8joBt.js";import{g as P,a as j,b as B}from"./index-CpGhZCTT.js";const J={class:"wrapper"},ee=k({__name:"index",setup(K){const{$btnPerms:b}=_(),o=n(),u=n(),m=n([]),f=n(new Date().toString()),s=p({workOrderResourceList:[],workOrderTypeList:[],WorkOrderEmergencyLevelList:[],WorkOrderProcessLevelList:[]});function h(){I({status:"1",page:1,size:999}).then(e=>{s.workOrderResourceList=i(e.data.data.data||[],"children",{label:"name",value:"name"})}),P("1").then(e=>{s.workOrderTypeList=i(e.data.data||[],"children",{label:"name",value:"name"})}),j("1").then(e=>{s.WorkOrderEmergencyLevelList=i(e.data.data||[],"children",{label:"name",value:"id"})}),B("1").then(e=>{s.WorkOrderProcessLevelList=i(e.data.data||[],"children",{label:"name",value:"name"})})}const L=p({title:"故障上报",labelWidth:"100px",submit:e=>{const a={...e,isDirectDispatch:!0};e.workOrder=JSON.parse(JSON.stringify(a)),M(e).then(()=>{O.success("故障上报成功"),t.selectList=[],f.value=new Date().toString()})},defaultValue:{source:"设备资产",type:"设备故障"},group:[{fields:[{xl:8,type:"select",label:"紧急程度",field:"level",rules:[{required:!0,message:"请输入紧急程度"}],options:r(()=>s.WorkOrderEmergencyLevelList)},{xl:8,type:"select",label:"事件来源",field:"source",readonly:!0,rules:[{required:!0,message:"请选择事件来源"}],options:r(()=>s.workOrderResourceList)},{xl:8,type:"select-tree",label:"事件类型",field:"type",readonly:!0,rules:[{required:!0,message:"请选择事件类型"}],options:r(()=>s.workOrderTypeList)},{xl:8,type:"select-tree",label:"接收部门",field:"key4",defaultExpandAll:!0,checkStrictly:!0,rules:[{required:!0,message:"请选择接收部门"}],options:r(()=>t.WaterSupplyTree),onChange:e=>{t.getUserListValue(e)}},{xl:8,type:"select",label:"接收人员",field:"processUserId",rules:[{required:!0,message:"请选择接收人员"}],options:r(()=>t.UserList)},{xl:8,type:"input",label:"事件标题",field:"title",rules:[{required:!0,message:"请输入事件标题"}]},{xl:8,type:"textarea",label:"事件地址",field:"address",rules:[{required:!0,message:"请输入事件地址"}]},{xl:8,type:"textarea",label:"事件描述",field:"remark",rules:[{required:!0,message:"请输入事件描述"}]},{type:"table",field:"faultReportCList",config:{indexVisible:!0,height:"350px",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加设备",perm:!0,click:()=>{var e;(e=o.value)==null||e.openDrawer()}}]}]}],dataList:r(()=>t.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"actualAddress"}],operations:[{text:"移除",type:"danger",icon:U.DELETE,perm:b("RoleManageDelete"),click:e=>{t.selectList=t.selectList.filter(a=>a.id!==e.id)}}],pagination:{hide:!0}}},{type:"divider",text:" "},{xl:8,type:"select",label:"故障类别",field:"faultTypeId",options:r(()=>t.faultCategory)},{xl:8,type:"input",label:"故障项目",field:"faultProject"},{xl:8,type:"input",label:"故障描述",field:"faultRemark"},{xl:16,type:"image",label:"现场图片",field:"imgUrl",limit:5},{type:"file",label:"现场录音",field:"audioUrl",accept:"audio/*",limit:5},{type:"hint",text:"只能上传音频文件，最多上传5个，且大小不能超过4M"},{type:"file",label:"现场视频",field:"videoUrl",accept:"video/*",limit:2},{type:"hint",text:"只能上传视频文件，最多上传2个，且大小不能超过100M"},{type:"file",label:"其他附件",field:"otherFileUrl",limit:2},{type:"hint",text:"附件最多只能上传两个，最多上传2个，且大小不能超过10M"},{xl:8,type:"divider",text:"操作"},{type:"btn-group",btns:[{text:"上报",perm:!0,click:()=>{var e;(e=u.value)==null||e.Submit()}},{type:"default",text:"重置",perm:!0,click:()=>{var e;t.selectList=[],(e=u.value)==null||e.resetForm()}}]}]}]}),v=p({title:"设备选择",labelWidth:"130px",submit:(e,a)=>{var l;a?(delete e.device,t.getDevice(e)):(t.selectList=[...t.selectList,...m.value],t.selectList=S(t.selectList,"id"),(l=o.value)==null||l.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"标签编码",field:"deviceLabelCode"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{xl:8,label:"安装区域",field:"areaId",type:"select-tree",checkStrictly:!0,options:r(()=>t.installationArea)},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:r(()=>t.deviceValue),selectList:[],handleSelectChange:e=>{m.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"查询",click:()=>{var e;(e=o.value)==null||e.Submit(!0)},perm:!0},{text:"重置",type:"primary",perm:!0,click:()=>{var e,a;(e=o.value)==null||e.resetForm(),(a=o.value)==null||a.Submit(!0)}}]}]}],columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"actualAddress"}],pagination:{hide:!0}}}]}]}),t=p({WaterSupplyTree:[],UserList:[],deviceValue:[],total:0,selectList:[],faultCategory:[],installationArea:[],getDevice:e=>{const a={size:99999,page:1,...e};N(a).then(l=>{t.deviceValue=l.data.data.data||[],t.total=l.data.data.total||0})},getWaterSupplyTreeValue:()=>{w(2).then(a=>{t.WaterSupplyTree=i(a.data.data||[])})},getUserListValue:e=>{T({pid:e}).then(a=>{const l=a.data.data.data||[];t.UserList=l.map(d=>({label:d.firstName,value:W(d.id.id),userName:d.name}))})},getFaultKnowledgeValue:()=>{z({size:99999,page:1}).then(a=>{t.faultCategory=i(a.data.data.data||[])})},getAreaTreeValue:()=>{R({page:1,size:99999,shortName:""}).then(a=>{t.installationArea=i(a.data.data.data||[])})}});return C(()=>{h(),t.getWaterSupplyTreeValue(),t.getDevice(),t.getFaultKnowledgeValue(),t.getAreaTreeValue()}),(e,a)=>{const l=F,d=q,x=A;return g(),V("div",J,[y(d,{class:"box-card",style:{height:"100%",overflow:"auto"}},{default:D(()=>[(g(),E(l,{key:c(f),ref_key:"refForm",ref:u,config:c(L)},null,8,["config"]))]),_:1}),y(x,{ref_key:"refFormEquipment",ref:o,config:c(v)},null,8,["config"])])}}});export{ee as default};
