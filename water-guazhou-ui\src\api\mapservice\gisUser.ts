import { requestWater } from '@/plugins/axios/water'

/**
 * 根据阀门编号查询用户
 * @param deviceids
 * @returns
 */
export const getUserListByValveNumberList = (deviceids: string[]) => {
  return requestWater({
    url: '/api/GetUserListByValveNumberList',
    method: 'post',
    data: {
      vnums: deviceids.join(','), // this.valveArr,
      pi: 1,
      ps: 2500
    }
  })
}
/**
 * 查询 用户用水信息
 * @param yhbh
 * @returns
 */
export const getLastPayDetailByYhbh = (yhbh: string) => {
  return requestWater({
    url: '/api/GetLastPayDetailByYhbh',
    method: 'get',
    params: {
      yhbh
    }
  })
}
/**
 * 查询用户定位
 * @param yhbh
 * @returns
 */
export const getUserInfoByYhbh = (yhbh: string) => {
  return requestWater({
    url: '/api/GetUserInfoByYhbh',
    method: 'get',
    params: {
      yhbh
    }
  })
}
