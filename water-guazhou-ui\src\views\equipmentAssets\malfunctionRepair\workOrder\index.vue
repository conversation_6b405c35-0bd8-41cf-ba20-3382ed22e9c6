<!-- 故障工单 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refForm"
      :config="detailConfig"
    ></SLDrawer>
    <SLDrawer
      ref="refdetail"
      :config="detailFormConfig"
    >
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { GetEquipmentWorkOrderPage, TerminateWorkOrder } from '@/api/workorder'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { getWaterSupplyTree } from '@/api/company_org'
import { getWorkOrderEquipmentList } from '@/api/equipment_assets/malfunctionRepair'
import { traverse } from '@/utils/GlobalHelper'
import { removeSlash } from '@/utils/removeIdSlash'
import { getUserList } from '@/api/user/index'
import detail from './components/detail.vue'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()
const refdetail = ref<ISLDrawerIns>()
const refForm = ref<ISLDrawerIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '工单单号', field: 'serialNo', type: 'input' },
    { label: '开始时间', field: 'fromTime', type: 'date' },
    { label: '结束时间', field: 'toTime', type: 'date' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '上传用户姓名', prop: 'uploadUserName' },
    { label: '标题', prop: 'title' },
    { label: '描述', prop: 'remark' },
    { label: '地址', prop: 'address' },
    { label: '工单号', prop: 'serialNo' },
    { label: '工单状态',
      prop: 'status',
      formatter: row => {
        switch (row.status) {
          case 'PENDING':
          case 'ASSIGN':
            return '待处理'
          case 'RESOLVING':
          case 'ARRIVING':
          case 'PROCESSING':
          case 'SUBMIT':
          case 'REVIEW':
          case 'CHARGEBACK_REVIEW':
          case 'HANDOVER_REVIEW':
          case 'REASSIGN':
          case 'COLLABORATION':
            return '处理中'
          case 'APPROVED':
          case 'CHARGEBACK':
          case 'TERMINATED':
            return '已结束'
          default:
            break
        }
      } },
    { label: '故障项目', prop: 'faultProject' },
    { label: '故障描述', prop: 'remark' },
    { label: '创建时间', prop: 'createTime' },
    { label: '设备',
      prop: 'drive',
      formItemConfig: {
        type: 'btn-group',
        btns: [{
          isTextBtn: true,
          text: '查看',
          perm: $btnPerms('RoleManageEdit'),
          icon: 'iconfont icon-xiangqing',
          click: row => clickEdit(row)
        }]
      } }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => {
        selectedId.value = row.id || ''
        detailFormConfig.title = row.serialNo
        refdetail.value?.openDrawer()
      }
    },
    {
      hide: row => {
        switch (row.status) {
          case 'APPROVED':
          case 'CHARGEBACK':
          case 'TERMINATED':
            return true
          default:
            return false
        }
      },
      type: 'danger',
      color: '#4195f0',
      text: '终止',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.DELETE,
      click: row => {
        TerminateWorkOrder(row.id, { processRemark: '终止', processAdditionalInfo: '点击终止' }).then(() => {
          refreshData()
          ElMessage.success('终止成功')
        })
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const detailConfig = reactive<IDrawerConfig>({
  title: '设备',
  labelWidth: '100px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'drives',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.equipmentList) as any,

            columns: [
              { label: '标签编码', prop: 'deviceLabelCode' },
              { label: '设备名称', prop: 'name' },
              { label: '型号/规格', prop: 'model' },
              { label: '所属大类', prop: 'topType' },
              { label: '所属类别', prop: 'type' }
              // { label: '安装位置', prop: 'key6' }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 明细弹框
const detailFormConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  group: []
})

const selectedId = ref<string>('')

const clickEdit = (row: { [x: string]: any }) => {
  detailConfig.defaultValue = { ...(row) || {} }
  data.getWorkOrderEquipmentListValue(row.id)
  refForm.value?.openDrawer()
}

const data = reactive({
  // 部门
  WaterSupplyTree: [] as any,
  // 用户列表
  UserList: [],
  equipmentList: [],
  getWorkOrderEquipmentListValue: workOrderId => {
    getWorkOrderEquipmentList({ workOrderId }).then(res => {
      data.equipmentList = res.data.data || []
    })
  },
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    type: '设备故障',
    source: '设备资产',
    ...(refSearch.value?.queryParams || {})
  }
  GetEquipmentWorkOrderPage(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
  data.getWaterSupplyTreeValue()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
