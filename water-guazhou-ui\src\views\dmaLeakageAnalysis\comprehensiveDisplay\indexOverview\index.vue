<!--指标总览-->
<template>
  <div ref="refDiv" class="main">
    <div class="statistics">
      <PSDeltaCard
        :type="'circle'"
        :title="'当前'"
        :count1="state.waterPSDeltaInfo.nowFlow"
        :count2="state.waterPSDeltaInfo.nowSupply"
      ></PSDeltaCard>
      <PSDeltaCard
        :type="'rate'"
        :title="'本季度'"
        :count1="state.waterPSDeltaInfo.quarterSupply"
        :count2="state.waterPSDeltaInfo.quarterSale"
        :count3="state.waterPSDeltaInfo.quarterNRW"
      ></PSDeltaCard>
      <PSDeltaCard
        :type="'rate'"
        :title="'本年'"
        :count1="state.waterPSDeltaInfo.thisYearSupply"
        :count2="state.waterPSDeltaInfo.thisYearSale"
        :count3="state.waterPSDeltaInfo.thisYearNRW"
      ></PSDeltaCard>
      <PSDeltaCard
        :type="'rate'"
        :title="'上月'"
        :count1="state.waterPSDeltaInfo.lastMonthSupply"
        :count2="state.waterPSDeltaInfo.lastMonthSale"
        :count3="state.waterPSDeltaInfo.lastMonthNRW"
      ></PSDeltaCard>
      <PSDeltaCard
        :type="'rate'"
        :title="'上季度'"
        :count1="state.waterPSDeltaInfo.lastQuarterSupply"
        :count2="state.waterPSDeltaInfo.lastQuarterSale"
        :count3="state.waterPSDeltaInfo.lastQuarterNRW"
      ></PSDeltaCard>
      <PSDeltaCard
        :type="'rate'"
        :title="'去年'"
        :count1="state.waterPSDeltaInfo.lastYearSupply"
        :count2="state.waterPSDeltaInfo.lastYearSale"
        :count3="state.waterPSDeltaInfo.lastYearNRW"
      ></PSDeltaCard>
    </div>
    <div class="middle">
      <ZGSTJ class="card"></ZGSTJ>
      <CXCTJ class="card"></CXCTJ>
      <LSPH class="card"></LSPH>
    </div>
    <div class="bottom">
      <GSLFX class="bottom-card"></GSLFX>
      <SSLFX class="bottom-card"></SSLFX>
      <DYHZBFX class="bottom-card"></DYHZBFX>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { GetPartitionWaterOverview } from '@/api/mapservice/dma';
import PSDeltaCard from './components/PSDeltCard.vue';
import LSPH from './components/LSPH.vue';
import ZGSTJ from './components/ZGSTJ.vue';
import CXCTJ from './components/CXCTJ.vue';
import GSLFX from './components/GSLFX.vue';
import SSLFX from './components/SSLFX.vue';
import DYHZBFX from './components/DYHZBFX.vue';

const state = reactive<{
  waterPSDeltaInfo: any;
}>({
  waterPSDeltaInfo: {}
});

const refreshData = () => {
  GetPartitionWaterOverview()
    .then((res) => {
      state.waterPSDeltaInfo = res.data.data || {};
    })
    .catch(() => {
      //
    });
};
onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.card-header {
  display: flex;
  align-items: center;
  word-break: keep-all;
  justify-content: space-between;
  width: 100%;
  .left {
    display: flex;
    align-items: center;
  }
  :deep(.el-form-item--default) {
    margin-bottom: 0;
  }
}
.main {
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.flex {
  display: flex;
  align-items: baseline;
}

.cloumn {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 100%;
}

.statistics {
  display: flex;
  justify-content: space-between;
}

.flex-between {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.statistics-item {
  width: 16%;
  height: 160px;
  border-radius: 12px;

  .num {
    font-weight: 600;
  }

  .dot {
    width: 8px;
    height: 8px;
    left: 179px;
    top: 160px;
    border-radius: 6px;
    margin-right: 8px;
  }

  .dot-color1 {
    background: #70ca61;
  }

  .dot-color2 {
    background: #318dff;
  }

  .count {
    font-weight: 600;
    padding-top: 8px;
  }

  :deep(.line .el-progress-bar__outer) {
    background-color: #318dff;
  }

  :deep(.sl-card-content) {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .card-left {
    padding: 20px 0;
    text-align: center;
    height: 100%;
    width: 35%;
    border-radius: 12px 0 0 12px;
    background-color: #42a5f6;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .title {
      font-size: 20px;
      font-weight: 800;
    }
  }

  .card-right {
    height: 100%;
    width: 65%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .name {
      padding-left: 20px;
      height: 40px;
      line-height: 22px;
      margin-top: 10px;

      &:nth-child(1) {
        margin-top: 0;
      }

      &:nth-child(3) {
        font-size: 14px;
      }
    }
  }
}

.middle {
  padding: 12px 0;
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 300px;

  .card {
    width: calc(33% - 10px);
    height: 100%;

    .card-title {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
  }

  .chart-box {
    height: 100%;
    width: 100%;
  }
}

.bottom {
  .bottom-card {
    margin-bottom: 12px;
    width: 100%;
    height: 300px;
  }

  .charts-box {
    display: flex;
    justify-content: space-between;

    .chart-box {
      height: 100%;

      &:nth-child(1) {
        width: 30%;
      }

      &:nth-child(2) {
        width: 70%;
      }
    }
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding-top: 16px;
  }
}
</style>
