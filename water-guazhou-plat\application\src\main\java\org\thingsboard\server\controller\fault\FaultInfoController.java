package org.thingsboard.server.controller.fault;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;

import org.thingsboard.server.dao.fault.FaultInfoService;
import org.thingsboard.server.dao.model.sql.fault.FaultInfo;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-07
 */
@RestController
@RequestMapping("api/fault/info")
public class FaultInfoController extends BaseController {

    @Autowired
    private FaultInfoService faultInfoService;

    @GetMapping("{mainId}")
    public IstarResponse getListByMainId(@PathVariable String mainId, @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(faultInfoService.getListByMainId(mainId, name, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody FaultInfo faultInfo) throws ThingsboardException {
        faultInfo.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultInfo.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(faultInfoService.save(faultInfo));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return faultInfoService.delete(ids);
    }
}
