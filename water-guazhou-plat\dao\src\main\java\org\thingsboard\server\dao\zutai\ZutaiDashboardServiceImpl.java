package org.thingsboard.server.dao.zutai;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.zutai.ZutaiDashboardEntity;
import org.thingsboard.server.dao.sql.zutai.ZutaiDashboardRepository;

import java.util.*;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@Service
public class ZutaiDashboardServiceImpl implements ZutaiDashboardService {
    @Autowired
    private ZutaiDashboardRepository zutaiDashboardRepository;

    @Override
    public List getList(Map params) {
        String id = (String) params.get("id");
        String projectId = (String) params.get("projectId");
        String tenantId = (String) params.get("tenantId");
        if (StringUtils.isBlank(id)) {
            id = "";
        }
        id = "%" + id + "%";
        if (StringUtils.isBlank(projectId)) {
            projectId = "";
        }
        projectId = "%" + projectId + "%";
        List<ZutaiDashboardEntity> zutaiDashboardEntityList = zutaiDashboardRepository.findAllByIdLikeAndProjectIdLikeAndTenantIdOrderByCreateTimeDesc(id, projectId, tenantId);

        List returnList = new ArrayList();
        Map returnMap;
        for (ZutaiDashboardEntity zutaiDashboardEntity : zutaiDashboardEntityList) {
            returnMap = JSONObject.parseObject(JSONObject.toJSONString(zutaiDashboardEntity), Map.class);
            try {
                returnMap.put("data", zutaiDashboardEntity.getData());
            } catch (Exception e) {
                System.out.println("Dashboard不是一个对象");
            }
            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public Map save(Map zutaiDashboardEntityMap) {
        // projectId
        if (zutaiDashboardEntityMap.get("projectId") == null) {
            zutaiDashboardEntityMap.put("projectId", "");
        }
        ZutaiDashboardEntity zutaiDashboardEntity = JSONObject.parseObject(JSONObject.toJSONString(zutaiDashboardEntityMap), ZutaiDashboardEntity.class);
        zutaiDashboardEntity.setData((String) zutaiDashboardEntityMap.get("data"));

        // 是否新增
        if (StringUtils.isBlank(zutaiDashboardEntity.getId())) {
            zutaiDashboardEntity.setCreateTime(new Date());
        }
        zutaiDashboardEntity.setCreateTime(new Date());

        ZutaiDashboardEntity save = zutaiDashboardRepository.save(zutaiDashboardEntity);

        zutaiDashboardEntityMap.put("id", save.getId());
        zutaiDashboardEntityMap.put("createTime", save.getCreateTime().getTime());
        return zutaiDashboardEntityMap;
    }

    @Override
    public void delete(String id) {
        zutaiDashboardRepository.delete(id);
    }
}
