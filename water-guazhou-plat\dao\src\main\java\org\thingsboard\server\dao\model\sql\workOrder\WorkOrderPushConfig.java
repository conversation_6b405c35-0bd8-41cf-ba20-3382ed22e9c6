package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("work_order_push_config")
public class WorkOrderPushConfig {

    private String id;

    private Integer beforeHour;

    private String isPushWeb;

    private String isPushApp;

    private String isPushMsg;

    private String enabled;

    private Date createTime;

    private String tenantId;


}
