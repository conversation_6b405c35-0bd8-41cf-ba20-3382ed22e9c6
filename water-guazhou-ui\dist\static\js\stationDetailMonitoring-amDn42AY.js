import{_ as U}from"./index-C9hz-UZb.js";import{d as Z,r as k,c as f,l as x,bI as $,bJ as j,bH as V,am as E,j as _,o as J,ay as Y,g as K,n as Q,p as g,q as m,i as w,F as B,aq as X,C as tt}from"./index-r0dFAfgr.js";import{_ as et}from"./CardSearch-CB_HNR-Q.js";import{u as ot}from"./useDetector-BRcb7GRN.js";import"./index-0NlGN6gS.js";import{p as q}from"./padStart-BKfyZZDO.js";import{b as at,c as rt}from"./lossControl-DNefZk8I.js";import{m as it,a as nt,b as st}from"./minBy-DBQvPu-j.js";import"./Search-NSrhrIa_.js";import"./_baseExtremum-UssVWohW.js";import"./_baseSum-Cz9yialR.js";import"./_baseLt-svgXHEqw.js";const pt={class:"wrapper-content"},lt={class:"top"},ct={class:"bottom"},dt=Z({__name:"stationDetailMonitoring",props:{device:{}},emits:["hiddenLoading","update:model-value","back"],setup(v,{emit:z}){const N=z,y=v,u=k({chartOption:null,chartOption1:null}),C=f(),F=f(),A=f(),L=(e,t,p)=>{p.hidden=e.type!==p.field},G=k({defaultParams:{month:x().format($),year:x().format(j),day:[x().subtract(1,"M").format(V),x().format(V)],type:"month"},filters:[{type:"radio-button",label:"选择方式",field:"type",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按日期",value:"day"}]},{handleHidden:L,type:"month",label:"",field:"month"},{handleHidden:L,type:"year",label:"",field:"year"},{handleHidden:L,type:"daterange",label:"",field:"day"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>S(),iconifyIcon:"ep:search"},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",click:()=>{var e;(e=C.value)==null||e.exportTable()}},{perm:!0,type:"default",text:"返回",iconifyIcon:"ep:d-arrow-left",click:()=>{N("back")}}]}],moreFilters:[]}),i=k({loading:!1,indexVisible:!0,dataList:[],columns:[{prop:"partitionName",label:"日期",minWidth:120},{prop:"supplyTotal",label:"供水量",unit:"(m³)",minWidth:120,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"minFlowTimeHour",label:"最小流时间",unit:"(h)",minWidth:140,sortable:!0,formatter(e,t){return e.partitionName==="平均"?" ":q(t,2,"0")+":00"}},{prop:"minFlow",label:"MNF夜间最小流",unit:"(m³/h)",minWidth:220,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"minValueTimeHour",label:"最小水量值时间",unit:"",minWidth:160,sortable:!0,formatter(e,t){return e.partitionName==="平均"?" ":q(t,2,"0")+":00"}},{prop:"minValue",label:"夜间最小水量值",unit:"(m³)",minWidth:210,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"legalUseWater",label:"用户合法用水量",unit:"(m³/h)",minWidth:210,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"netNightFlow",label:"净夜间流量",unit:"(m³/h)",minWidth:180,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"lossWater",label:"漏失水量",unit:"(m³)",minWidth:240,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"avgDayFlow",label:"日均流量",unit:"(m³/h)",minWidth:150,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"unitPipeNightFlowMin",label:"单位管线夜间小流",unit:"(m³/h/km)",minWidth:240,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"mnfDivideAvgDayFlow",label:"MNF/日均流量",unit:"(%)",minWidth:180,sortable:!0,formatter(e,t){return t==null?void 0:t.toFixed(2)}},{prop:"lossValuation",label:"漏损评估",tag:!0,tagColor(e,t){return t==="一般"?"#e6a23c":t==="较好"?"#318DFF":t==="较差"?"#f56c6c":""}}],pagination:{hide:!0},handleRowClick(e){["最小","最大","平均"].indexOf(e.partitionName)===-1&&(i.currentRow=e,I())}}),S=async()=>{var o,a,b,d,O;const e=((o=A.value)==null?void 0:o.queryParams)||{},t={date:e.type==="month"?e.month:e.type==="year"?e.year:void 0,partitionId:(a=y.device)==null?void 0:a.partitionId,type:e.type,start:e.type==="day"?(b=e.day)==null?void 0:b[0]:void 0,end:e.type==="day"?(d=e.day)==null?void 0:d[1]:void 0},n=((O=(await at(t)).data)==null?void 0:O.data)||[],l={partitionName:"最小"},c={partitionName:"最大"},r={partitionName:"平均"};i.columns.map(s=>{var M,T;s.sortable&&(l[s.prop]=(M=it(n,h=>h[s.prop]))==null?void 0:M[s.prop],c[s.prop]=(T=nt(n,h=>h[s.prop]))==null?void 0:T[s.prop],r[s.prop]=st(n,h=>h[s.prop]),isNaN(r[s.prop])&&(r[s.prop]=void 0))}),n.push(c,l,r),i.dataList=n,i.currentRow=i.dataList[0],R()};E(()=>y.device,()=>{S()});const H=()=>{var r;(r=D.value)==null||r.clear();const e=i.dataList.map(o=>o.partitionName)||[],t=i.dataList.map(o=>{var a;return(a=o.lossWater)==null?void 0:a.toFixed(2)})||[],p=i.dataList.map(o=>{var a;return(a=o.legalUseWater)==null?void 0:a.toFixed(2)})||[],n=i.dataList.map(o=>{var a;return(a=o.netNightFlow)==null?void 0:a.toFixed(2)})||[],l=i.dataList.map(o=>{var a;return(a=o.minFlow)==null?void 0:a.toFixed(2)})||[],c={title:{text:""},grid:{left:50,right:50,top:50,bottom:80},legend:{type:"scroll",textStyle:{color:"#666",fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!0,data:e},dataZoom:[{type:"inside",start:0,end:30},{start:0,end:10}],yAxis:[{position:"left",type:"value",name:"水量(m³)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:_().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"流量(m³/h)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:_().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}],series:[{name:"漏失水量",type:"bar",barWidth:10,data:t},{name:"合法用水",type:"line",smooth:!0,symboe:"none",barWidth:10,data:p,yAxisIndex:1},{name:"净夜间流量",type:"line",smooth:!0,symboe:"none",barWidth:10,data:n,yAxisIndex:1},{name:"夜间最小流",type:"line",smooth:!0,symboe:"none",yAxisIndex:1,data:l}]};u.chartOption=c},I=async()=>{var e,t,p,n;try{(e=W.value)==null||e.clear();const l=await rt({date:(t=i.currentRow)==null?void 0:t.partitionName,partitionId:(p=y.device)==null?void 0:p.partitionId}),c=[],r=[],o=[];(((n=l.data)==null?void 0:n.data)||[]).map(d=>{c.push(d.time),r.push(d.supply),o.push(d.flow)});const b={title:{text:""},grid:{left:50,right:50,top:50,bottom:80},legend:{type:"scroll",textStyle:{color:"#666",fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!0,data:c||[]},dataZoom:[{type:"inside",start:0,end:30},{start:0,end:10}],yAxis:[{position:"left",type:"value",name:"水量(m³)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:_().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"流量(m³/h)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:_().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}],series:[{name:"供水量",type:"bar",barWidth:10,data:r||[]},{name:"瞬时流量",type:"line",smooth:!0,symboe:"none",barWidth:10,data:o||[],yAxisIndex:1}]};u.chartOption1=b}catch{}},D=f(),W=f(),R=async()=>{I(),H(),N("hiddenLoading")},P=ot();return J(()=>{S(),P.listenToMush(F.value,()=>{var e,t;(e=D.value)==null||e.resize(),(t=W.value)==null||t.resize()})}),(e,t)=>{var r,o;const p=et,n=X,l=Y("VChart"),c=U;return K(),Q("div",pt,[g("div",lt,[m(p,{ref_key:"cardSearch",ref:A,config:w(G)},null,8,["config"]),m(n,{ref_key:"refTable",ref:C,class:"card-table",config:w(i)},null,8,["config"])]),g("div",ct,[m(c,{title:`${(r=y.device)==null?void 0:r.partitionName}--小流分析图`,overlay:"",class:"chart-lef"},{default:B(()=>[g("div",{ref_key:"echartsDiv",ref:F,class:"chart-box t"},[m(l,{ref_key:"refChart1",ref:D,option:w(u).chartOption},null,8,["option"])],512)]),_:1},8,["title"]),m(c,{title:`${(o=y.device)==null?void 0:o.partitionName}--24小时流量分析图`,overlay:"",class:"chart-right"},{default:B(()=>[g("div",{ref_key:"echartsDiv",ref:F,class:"chart-box"},[m(l,{ref_key:"refChart2",ref:W,option:w(u).chartOption1},null,8,["option"])],512)]),_:1},8,["title"])])])}}}),St=tt(dt,[["__scopeId","data-v-c44eeeac"]]);export{St as default};
