package org.thingsboard.server.dao.model.sql.revenue;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.imodel.query.annotations.FieldRemark;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户基本信息
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.CUST_INFO_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class CustInfo {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.CUST_INFO_ORG_ID)
    private String orgId;

    @FieldRemark("供水单位")
    @TableField(exist = false)
    private String orgName;

    @TableField(ModelConstants.CUST_INFO_METER_BOOK_ID)
    private String meterBookId;

    @FieldRemark("抄表册")
    @TableField(exist = false)
    private String meterBookName;

    @FieldRemark("用户编号")
    @TableField(ModelConstants.CUST_INFO_CODE)
    private String code;

    @FieldRemark("用户名称")
    @TableField(ModelConstants.CUST_INFO_NAME)
    private String name;

    @FieldRemark("身份证号")
    @TableField(ModelConstants.CUST_INFO_ID_NUMBER)
    private String idNumber;

    @FieldRemark("联系电话")
    @TableField(ModelConstants.CUST_INFO_PHONE)
    private String phone;

    @FieldRemark("用水地址")
    @TableField(ModelConstants.CUST_INFO_ADDRESS)
    private String address;

    @FieldRemark("邮箱")
    @TableField(ModelConstants.CUST_INFO_EMAIL)
    private String email;

    @FieldRemark("人口数")
    @TableField(ModelConstants.CUST_INFO_POPULATION)
    private Integer population;

    @TableField(ModelConstants.CUST_INFO_WATER_CATEGORY)
    private String waterCategory;

    @FieldRemark("用水类别")
    @TableField(exist = false)
    private String waterCategoryName;

    @TableField(ModelConstants.CUST_INFO_INDUSTRY_CATEGORY)
    private String industryCategory;

    @FieldRemark("行业分类")
    @TableField(exist = false)
    private String industryCategoryName;

    @TableField(ModelConstants.CUST_INFO_USER_TYPE)
    private String userType;

    @FieldRemark("用户类别")
    @TableField(exist = false)
    private String userTypeName;

    @TableField(ModelConstants.CUST_INFO_PAYMENT_METHOD)
    private String paymentMethod;

    @FieldRemark("结算方式")
    @TableField(exist = false)
    private String paymentMethodName;

    @TableField(ModelConstants.CUST_INFO_PAY_TYPE)
    private String payType;

    @FieldRemark("支付方式")
    @TableField(exist = false)
    private String payTypeName;

    @TableField(ModelConstants.CUST_INFO_SPECIAL_USER_TYPE)
    private String specialUserType;

    @FieldRemark("特殊户")
    @TableField(exist = false)
    private String specialUserTypeName;

    @TableField(ModelConstants.CUST_INFO_BILL_TYPE)
    private String billType;

    @FieldRemark("票据类型")
    @TableField(exist = false)
    private String billTypeName;

    @TableField(ModelConstants.CUST_INFO_TRANSFER_USER_FLAG)
    private String transferUserFlag;

    @FieldRemark("转供标志")
    @TableField(exist = false)
    private String transferUserFlagName;

    @TableField(ModelConstants.CUST_INFO_TEMPORARY_WATER_FLAG)
    private String temporaryWaterFlag;

    @FieldRemark("临时用水")
    @TableField(exist = false)
    private String temporaryWaterFlagName;

    @TableField(ModelConstants.CUST_INFO_TEMPORARY_WATER_END_DATE)
    private Date temporaryWaterEndDate;

    @TableField(ModelConstants.CUST_INFO_LIQUIDATED_DAMAGES)
    private String liquidatedDamages;

    @FieldRemark("违约金")
    @TableField(exist = false)
    private String liquidatedDamagesName;

    @FieldRemark("低消水量")
    @TableField(ModelConstants.CUST_INFO_LOW_CONSUMPTION_WATER)
    private BigDecimal lowConsumptionWater;

    @FieldRemark("合同编号")
    @TableField(ModelConstants.CUST_INFO_CONTRACT_NUMBER)
    private String contractNumber;

    @FieldRemark("备注")
    @TableField(ModelConstants.CUST_INFO_REMARK)
    private String remark;

    @TableField(ModelConstants.CUST_INFO_STATUS)
    private String status;

    @TableField(ModelConstants.CUST_INFO_WATER_STOP_FLAG)
    private String waterStopFlag;

    @FieldRemark("当前余额")
    @TableField(exist = false)
    private BigDecimal balance;

    @FieldRemark("执行水价")
    @TableField(exist = false)
    private String priceName;

    @FieldRemark("抄表员")
    @TableField(exist = false)
    private String copyMeterUser;

    @FieldRemark("用户状态")
    @TableField(exist = false)
    private String statusName;

    @FieldRemark("抄表序号")
    @TableField(exist = false)
    private Integer meterCopyOrderNumber;

    @FieldRemark("水表编号")
    @TableField(exist = false)
    private String meterCode;

    @FieldRemark("换表时间")
    @TableField(exist = false)
    private Date changeTime;

    @FieldRemark("水表口径")
    @TableField(exist = false)
    private String caliberName;

    @FieldRemark("本期读数")
    @TableField(exist = false)
    private BigDecimal currentRead;

    @FieldRemark("水表安装地址")
    @TableField(exist = false)
    private String installAddress;

    @FieldRemark("钢印号")
    @TableField(exist = false)
    private String steelSealNumber;

    @FieldRemark("表井号")
    @TableField(exist = false)
    private String meterWellCode;

    @FieldRemark("冻结余额")
    @TableField(exist = false)
    private BigDecimal frozenBalance;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
