<template>
  <el-upload
    class="avatar-uploader"
    :size="size"
    :action="props.url || appStore.actionUrl + 'file/api/upload/image'"
    :show-file-list="false"
    :on-success="handleAvatarSuccess"
    :before-upload="beforeAvatarUpload"
    :headers="{ 'X-Authorization': 'Bearer ' + userStore.token }"
  >
    <img
      v-if="imageUrl"
      :src="imageUrl"
      :style="props.imageStyle"
      class="avatar"
    />
    <div
      v-else
      class="avatar-uploader-icon"
      :style="props.imageStyle"
    >
      <Icon :icon="'ep:plus'"></Icon>
    </div>
    <span
      v-if="imageUrl"
      class="el-upload-list__item-delete"
      @click="handleRemove(imageUrl)"
    >
      <el-icon><Delete /></el-icon>
    </span>
    <template
      v-if="tips"
      #tip
    >
      <div class="el-upload__tip">
        {{ tips }}
      </div>
    </template>
  </el-upload>
</template>

<script lang="ts" setup>
import { Delete } from '@element-plus/icons-vue'
import type { UploadProps } from 'element-plus'
import { Icon } from '@iconify/vue'
import { useAppStore, useUserStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const appStore = useAppStore()
const userStore = useUserStore()
const props = defineProps<{
  modelValue?: string
  disabled?: boolean
  url?: string
  size?: ISize
  tips?: string
  imageStyle?: any
}>()
const emit = defineEmits(['update:modelValue', 'change'])
const imageUrl = ref(props.modelValue)
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  console.log(response, uploadFile)
  if (response) {
    imageUrl.value = response
    // uploadFile.raw && (imageUrl.value = URL.createObjectURL(uploadFile.raw))
    emit('update:modelValue', imageUrl.value)
    emit('change', imageUrl.value)
  }
}
const fileTypes = ['image/jpeg', 'image/png']
const beforeAvatarUpload: UploadProps['beforeUpload'] = rawFile => {
  if (fileTypes.indexOf(rawFile.type) === -1) {
    SLMessage.error('请使用符合JPEG/PNG格式的图片')
    return false
  }
  if (rawFile.size / 1024 / 1024 > 2) {
    SLMessage.error('照片大小不能超过2M')
    return false
  }
  return true
}
watch(
  () => props.modelValue,
  (newVal: any) => {
    imageUrl.value = newVal
  }
)

const handleRemove = (file: string) => {
  console.log(file)
}
</script>

<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 136px;
  display: block;
}
.avatar-uploader-icon {
  width: 178px;
  height: 136px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 136px;
  text-align: center;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}
</style>
