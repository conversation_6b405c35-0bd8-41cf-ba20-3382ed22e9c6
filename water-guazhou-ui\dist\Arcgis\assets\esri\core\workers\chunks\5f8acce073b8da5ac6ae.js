"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9538],{22303:(e,t,s)=>{s.d(t,{Z:()=>d});var r,i,n=s(35270),l=s(22021),a=s(70586),o=s(75215);function h(e){return(0,l.uZ)((0,o.vU)(e),0,255)}function u(e,t,s){return e=Number(e),isNaN(e)?s:e<t?t:e>s?s:e}class c{static blendColors(e,t,s,r=new c){return r.r=Math.round(e.r+(t.r-e.r)*s),r.g=Math.round(e.g+(t.g-e.g)*s),r.b=Math.round(e.b+(t.b-e.b)*s),r.a=e.a+(t.a-e.a)*s,r._sanitize()}static fromRgb(e,t){const s=e.toLowerCase().match(/^(rgba?|hsla?)\(([\s\.\-,%0-9]+)\)/);if(s){const e=s[2].split(/\s*,\s*/),r=s[1];if("rgb"===r&&3===e.length||"rgba"===r&&4===e.length){const s=e[0];if("%"===s.charAt(s.length-1)){const s=e.map((e=>2.56*parseFloat(e)));return 4===e.length&&(s[3]=parseFloat(e[3])),c.fromArray(s,t)}return c.fromArray(e.map((e=>parseFloat(e))),t)}if("hsl"===r&&3===e.length||"hsla"===r&&4===e.length)return c.fromArray((0,n.B7)(parseFloat(e[0]),parseFloat(e[1])/100,parseFloat(e[2])/100,parseFloat(e[3])),t)}return null}static fromHex(e,t=new c){if(4!==e.length&&7!==e.length||"#"!==e[0])return null;const s=4===e.length?4:8,r=(1<<s)-1;let i=Number("0x"+e.substr(1));return isNaN(i)?null:(["b","g","r"].forEach((e=>{const n=i&r;i>>=s,t[e]=4===s?17*n:n})),t.a=1,t)}static fromArray(e,t=new c){return t._set(Number(e[0]),Number(e[1]),Number(e[2]),Number(e[3])),isNaN(t.a)&&(t.a=1),t._sanitize()}static fromString(e,t){const s=(0,n.St)(e)?(0,n.h$)(e):null;return s&&c.fromArray(s,t)||c.fromRgb(e,t)||c.fromHex(e,t)}static fromJSON(e){return e&&new c([e[0],e[1],e[2],e[3]/255])}static toUnitRGB(e){return(0,a.pC)(e)?[e.r/255,e.g/255,e.b/255]:null}static toUnitRGBA(e){return(0,a.pC)(e)?[e.r/255,e.g/255,e.b/255,null!=e.a?e.a:1]:null}constructor(e){this.r=255,this.g=255,this.b=255,this.a=1,e&&this.setColor(e)}get isBright(){return.299*this.r+.587*this.g+.114*this.b>=127}setColor(e){return"string"==typeof e?c.fromString(e,this):Array.isArray(e)?c.fromArray(e,this):(this._set(e.r??0,e.g??0,e.b??0,e.a??1),e instanceof c||this._sanitize()),this}toRgb(){return[this.r,this.g,this.b]}toRgba(){return[this.r,this.g,this.b,this.a]}toHex(){const e=this.r.toString(16),t=this.g.toString(16),s=this.b.toString(16);return`#${e.length<2?"0"+e:e}${t.length<2?"0"+t:t}${s.length<2?"0"+s:s}`}toCss(e=!1){const t=this.r+", "+this.g+", "+this.b;return e?`rgba(${t}, ${this.a})`:`rgb(${t})`}toString(){return this.toCss(!0)}toJSON(){return this.toArray()}toArray(e=c.AlphaMode.ALWAYS){const t=h(this.r),s=h(this.g),r=h(this.b);return e===c.AlphaMode.ALWAYS||1!==this.a?[t,s,r,h(255*this.a)]:[t,s,r]}clone(){return new c(this.toRgba())}hash(){return this.r<<24|this.g<<16|this.b<<8|255*this.a}equals(e){return(0,a.pC)(e)&&e.r===this.r&&e.g===this.g&&e.b===this.b&&e.a===this.a}_sanitize(){return this.r=Math.round(u(this.r,0,255)),this.g=Math.round(u(this.g,0,255)),this.b=Math.round(u(this.b,0,255)),this.a=u(this.a,0,1),this}_set(e,t,s,r){this.r=e,this.g=t,this.b=s,this.a=r}}c.prototype.declaredClass="esri.Color",(i=(r=c||(c={})).AlphaMode||(r.AlphaMode={}))[i.ALWAYS=0]="ALWAYS",i[i.UNLESS_OPAQUE=1]="UNLESS_OPAQUE";const d=c},92835:(e,t,s)=>{s.d(t,{Z:()=>f});var r,i=s(43697),n=s(96674),l=s(70586),a=s(35463),o=s(5600),h=(s(75215),s(67676),s(71715)),u=s(52011),c=s(30556);let d=r=class extends n.wq{static get allTime(){return g}static get empty(){return p}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(r.allTime)}get isEmpty(){return this.equals(r.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new r({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,l.pC)(this.start)?this.start.getTime():this.start,s=(0,l.pC)(this.end)?this.end.getTime():this.end,r=(0,l.pC)(e.start)?e.start.getTime():e.start,i=(0,l.pC)(e.end)?e.end.getTime():e.end;return t===r&&s===i}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,l.yw)(this.start,(t=>(0,a.JE)(t,e))),s=(0,l.yw)(this.end,(t=>{const s=(0,a.JE)(t,e);return t.getTime()===s.getTime()?s:(0,a.Nm)(s,1,e)}));return new r({start:t,end:s})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return r.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,l.R2)(this.start,-1/0,(e=>e.getTime())),s=(0,l.R2)(this.end,1/0,(e=>e.getTime())),i=(0,l.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,l.R2)(e.end,1/0,(e=>e.getTime()));let a,o;if(i>=t&&i<=s?a=i:t>=i&&t<=n&&(a=t),s>=i&&s<=n?o=s:n>=t&&n<=s&&(o=n),null!=a&&null!=o&&!isNaN(a)&&!isNaN(o)){const e=new r;return e.start=a===-1/0?null:new Date(a),e.end=o===1/0?null:new Date(o),e}return r.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const s=new r,{start:i,end:n}=this;return(0,l.pC)(i)&&(s.start=(0,a.Nm)(i,e,t)),(0,l.pC)(n)&&(s.end=(0,a.Nm)(n,e,t)),s}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return g.clone();const t=(0,l.pC)(this.start)&&(0,l.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,s=(0,l.pC)(this.end)&&(0,l.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new r({start:t,end:s})}};(0,i._)([(0,o.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"end",void 0),(0,i._)([(0,h.r)("end")],d.prototype,"readEnd",null),(0,i._)([(0,c.c)("end")],d.prototype,"writeEnd",null),(0,i._)([(0,o.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isAllTime",null),(0,i._)([(0,o.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isEmpty",null),(0,i._)([(0,o.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"start",void 0),(0,i._)([(0,h.r)("start")],d.prototype,"readStart",null),(0,i._)([(0,c.c)("start")],d.prototype,"writeStart",null),d=r=(0,i._)([(0,u.j)("esri.TimeExtent")],d);const g=new d,p=new d({start:void 0,end:void 0}),f=d},5732:(e,t,s)=>{s.d(t,{c:()=>r});var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},46791:(e,t,s)=>{s.d(t,{Z:()=>O});var r,i=s(43697),n=s(3894),l=s(32448),a=s(22974),o=s(70586),h=s(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(r||(r={}));var u,c=s(1654),d=s(5600),g=s(75215),p=s(52421),f=s(52011),m=s(58971),_=s(10661);const y=new h.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),b=()=>{};function v(e){return e?e instanceof T?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function C(e){if(e&&e.length)return e[0]}function w(e,t,s,r){const i=Math.min(e.length-s,t.length-r);let n=0;for(;n<i&&e[s+n]===t[r+n];)n++;return n}function E(e,t,s,r){t&&t.forEach(((t,i,n)=>{e.push(t),E(e,s.call(r,t,i,n),s,r)}))}const A=new Set,D=new Set,S=new Set,M=new Map;let I=0,T=u=class extends l.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof u}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new _.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:I++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof u?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,m.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(r.ADD)||(this._splice(0,this.length,v(e)),this._emitAfterChanges(r.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,s={removed:!1,callback:t};return e.push(s),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=b,s.removed=!0,e.splice(e.indexOf(s),1)}}}return this._emitter.on(e,t)}once(e,t){const s=this.on(e,t);return{remove(){s.remove()}}}add(e,t){if((0,m.it)(this._observable),this._emitBeforeChanges(r.ADD))return this;const s=this.getNextIndex(t??null);return this._splice(s,0,[e]),this._emitAfterChanges(r.ADD),this}addMany(e,t=this._items.length){if((0,m.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(r.ADD))return this;const s=this.getNextIndex(t);return this._splice(s,0,v(e)),this._emitAfterChanges(r.ADD),this}at(e){if((0,m.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(r.REMOVE),e}clone(){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.map(a.d9)})}concat(...e){(0,m.it)(this._observable);const t=e.map(v);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return;const s=(0,o.j0)(this._splice(0,this.length)),i=s.length;for(let r=0;r<i;r++)e.call(t,s[r],r,s);this._emitAfterChanges(r.REMOVE)}every(e,t){return(0,m.it)(this._observable),this._items.every(e,t)}filter(e,t){let s;return(0,m.it)(this._observable),s=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:s})}find(e,t){return(0,m.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,m.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,m.it)(this._observable);const s=[];return E(s,this,e,t),new u(s)}forEach(e,t){return(0,m.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,m.it)(this._observable),this._items[e]}getNextIndex(e){(0,m.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,m.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,m.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,m.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,m.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,m.it)(this._observable);const s=this._items.map(e,t);return new u({items:s})}reorder(e,t=this.length-1){(0,m.it)(this._observable);const s=this.indexOf(e);if(-1!==s){if(t<0?t=0:t>=this.length&&(t=this.length-1),s!==t){if(this._emitBeforeChanges(r.MOVE))return e;this._splice(s,1),this._splice(t,0,[e]),this._emitAfterChanges(r.MOVE)}return e}}pop(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return;const e=C(this._splice(this.length-1,1));return this._emitAfterChanges(r.REMOVE),e}push(...e){return(0,m.it)(this._observable),this._emitBeforeChanges(r.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(r.ADD)),this.length}reduce(e,t){(0,m.it)(this._observable);const s=this._items;return 2===arguments.length?s.reduce(e,t):s.reduce(e)}reduceRight(e,t){(0,m.it)(this._observable);const s=this._items;return 2===arguments.length?s.reduceRight(e,t):s.reduceRight(e)}remove(e){return(0,m.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,m.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(r.REMOVE))return;const t=C(this._splice(e,1));return this._emitAfterChanges(r.REMOVE),t}removeMany(e){if((0,m.it)(this._observable),!e||!e.length||this._emitBeforeChanges(r.REMOVE))return[];const t=e instanceof u?e.toArray():e,s=this._items,i=[],n=t.length;for(let e=0;e<n;e++){const r=t[e],n=s.indexOf(r);if(n>-1){const r=1+w(t,s,e+1,n+1),l=this._splice(n,r);l&&l.length>0&&i.push.apply(i,l),e+=r-1}}return this._emitAfterChanges(r.REMOVE),i}reverse(){if((0,m.it)(this._observable),this._emitBeforeChanges(r.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(r.MOVE),this}shift(){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.REMOVE))return;const e=C(this._splice(0,1));return this._emitAfterChanges(r.REMOVE),e}slice(e=0,t=this.length){return(0,m.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,m.it)(this._observable),this._items.some(e,t)}sort(e){if((0,m.it)(this._observable),!this.length||this._emitBeforeChanges(r.MOVE))return this;const t=(0,o.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(r.MOVE),this}splice(e,t,...s){(0,m.it)(this._observable);const i=(t?r.REMOVE:0)|(s.length?r.ADD:0);if(this._emitBeforeChanges(i))return[];const n=this._splice(e,t,s)||[];return this._emitAfterChanges(i),n}toArray(){return(0,m.it)(this._observable),this._items.slice()}toJSON(){return(0,m.it)(this._observable),this.toArray()}toLocaleString(){return(0,m.it)(this._observable),this._items.toLocaleString()}toString(){return(0,m.it)(this._observable),this._items.toString()}unshift(...e){return(0,m.it)(this._observable),!e.length||this._emitBeforeChanges(r.ADD)||(this._splice(0,0,e),this._emitAfterChanges(r.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,s){const r=this._items,i=this.itemType;let n,l;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,c.Os)((()=>this._dispatchChange()))),t){if(l=r.splice(e,t),this.hasEventListener("before-remove")){const t=y.acquire();t.target=this,t.cancellable=!0;for(let s=0,i=l.length;s<i;s++)n=l[s],t.reset(n),this.emit("before-remove",t),t.defaultPrevented&&(l.splice(s,1),r.splice(e,0,n),e+=1,s-=1,i-=1);y.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=y.acquire();e.target=this,e.cancellable=!1;const t=l.length;for(let s=0;s<t;s++)e.reset(l[s]),this.emit("after-remove",e);y.release(e)}}if(s&&s.length){if(i){const e=[];for(const t of s){const s=i.ensureType(t);null==s&&null!=t||e.push(s)}s=e}const t=this.hasEventListener("before-add"),n=this.hasEventListener("after-add"),l=e===this.length;if(t||n){const i=y.acquire();i.target=this,i.cancellable=!0;const a=y.acquire();a.target=this,a.cancellable=!1;for(const o of s)t?(i.reset(o),this.emit("before-add",i),i.defaultPrevented||(l?r.push(o):r.splice(e++,0,o),this._set("length",r.length),n&&(a.reset(o),this.emit("after-add",a)))):(l?r.push(o):r.splice(e++,0,o),this._set("length",r.length),a.reset(o),this.emit("after-add",a));y.release(a),y.release(i)}else{if(l)for(const e of s)r.push(e);else r.splice(e,0,...s);this._set("length",r.length)}}return(s&&s.length||l&&l.length)&&this._notifyChangeEvent(s,l),l}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const s=y.acquire();s.target=this,s.cancellable=!0,s.type=e,this.emit("before-changes",s),t=s.defaultPrevented,y.release(s)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=y.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),y.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;A.clear(),D.clear(),S.clear();for(const{added:t,removed:s}of e){if(t)if(0===S.size&&0===D.size)for(const e of t)A.add(e);else for(const e of t)D.has(e)?(S.add(e),D.delete(e)):S.has(e)||A.add(e);if(s)if(0===S.size&&0===A.size)for(const e of s)D.add(e);else for(const e of s)A.has(e)?A.delete(e):(S.delete(e),D.add(e))}const s=n.Z.acquire();A.forEach((e=>{s.push(e)}));const r=n.Z.acquire();D.forEach((e=>{r.push(e)}));const i=this._items,l=t.items,a=n.Z.acquire();if(S.forEach((e=>{l.indexOf(e)!==i.indexOf(e)&&a.push(e)})),t.listeners&&(s.length||r.length||a.length)){const e={target:this,added:s,removed:r,moved:a},i=t.listeners.length;for(let s=0;s<i;s++){const r=t.listeners[s];r.removed||r.callback.call(this,e)}}n.Z.release(s),n.Z.release(r),n.Z.release(a)}A.clear(),D.clear(),S.clear()}};T.ofType=e=>{if(!e)return u;if(M.has(e))return M.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const s in e.typeMap){const r=e.typeMap[s].prototype.declaredClass;t?t+=` | ${r}`:t=r}let s=class extends u{};return(0,i._)([(0,p.c)({Type:e,ensureType:"function"==typeof e?(0,g.se)(e):(0,g.N7)(e)})],s.prototype,"itemType",void 0),s=(0,i._)([(0,f.j)(`esri.core.Collection<${t}>`)],s),M.set(e,s),s},(0,i._)([(0,d.Cb)()],T.prototype,"length",void 0),(0,i._)([(0,d.Cb)()],T.prototype,"items",null),T=u=(0,i._)([(0,f.j)("esri.core.Collection")],T);const O=T},52421:(e,t,s)=>{function r(e){return(t,s)=>{t[s]=e}}s.d(t,{c:()=>r})},35463:(e,t,s)=>{s.d(t,{JE:()=>l,Nm:()=>n,rJ:()=>a}),s(80442);const r={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},i={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,s){const r=new Date(e.getTime());if(t&&s){const e=i[s],{getter:n,setter:l,multiplier:a}=e;if("months"===s){const e=function(e,t){const s=new Date(e,t+1,1);return s.setDate(0),s.getDate()}(r.getFullYear(),r.getMonth()+t);r.getDate()>e&&r.setDate(e)}r[l](r[n]()+t*a)}return r}function l(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function a(e,t,s){return 0===e?0:e*r[t]/r[s]}},3723:(e,t,s)=>{s.r(t),s.d(t,{default:()=>R});var r=s(43697),i=s(68773),n=s(3172),l=(s(9790),s(70586)),a=s(16453),o=s(95330),h=s(17452),u=s(5600),c=(s(75215),s(67676),s(71715)),d=s(52011),g=s(6570),p=s(8744),f=s(87085),m=s(71612),_=s(38009),y=s(16859),b=s(34760),v=s(72965),C=s(21506),w=s(89164),E=s(4095),A=s(37898),D=s(77987),S=s(20256);const M=["atom","xml"],I={base:w.Z,key:"type",typeMap:{"simple-line":E.Z},errorContext:"symbol"},T={base:w.Z,key:"type",typeMap:{"picture-marker":A.Z,"simple-marker":D.Z},errorContext:"symbol"},O={base:w.Z,key:"type",typeMap:{"simple-fill":S.Z},errorContext:"symbol"};let P=class extends((0,m.h)((0,b.Q)((0,_.q)((0,y.I)((0,v.M)((0,a.R)(f.Z))))))){constructor(...e){super(...e),this.description=null,this.fullExtent=null,this.legendEnabled=!0,this.lineSymbol=null,this.pointSymbol=null,this.polygonSymbol=null,this.operationalLayerType="GeoRSS",this.url=null,this.type="geo-rss"}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}readFeatureCollections(e,t){return t.featureCollection.layers.forEach((e=>{const t=e.layerDefinition.drawingInfo.renderer.symbol;t&&"esriSFS"===t.type&&t.outline?.style.includes("esriSFS")&&(t.outline.style="esriSLSSolid")})),t.featureCollection.layers}get hasPoints(){return this._hasGeometry("esriGeometryPoint")}get hasPolylines(){return this._hasGeometry("esriGeometryPolyline")}get hasPolygons(){return this._hasGeometry("esriGeometryPolygon")}get title(){const e=this._get("title");return e&&"defaults"!==this.originOf("title")?e:this.url?(0,h.vt)(this.url,M)||"GeoRSS":e||""}set title(e){this._set("title",e)}load(e){const t=(0,l.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Map Service","Feature Service","Feature Collection","Scene Service"]},e).catch(o.r9).then((()=>this._fetchService(t))).then((e=>{this.read(e,{origin:"service"})}))),Promise.resolve(this)}async hasDataChanged(){const e=await this._fetchService();return this.read(e,{origin:"service",ignoreDefaults:!0}),!0}async _fetchService(e){const t=this.spatialReference,{data:s}=await(0,n.default)(i.Z.geoRSSServiceUrl,{query:{url:this.url,refresh:!!this.loaded||void 0,outSR:(0,p.oR)(t)?void 0:t.wkid??JSON.stringify(t)},signal:e});return s}_hasGeometry(e){return this.featureCollections?.some((t=>t.featureSet?.geometryType===e&&t.featureSet.features?.length>0))??!1}};(0,r._)([(0,u.Cb)()],P.prototype,"description",void 0),(0,r._)([(0,u.Cb)()],P.prototype,"featureCollections",void 0),(0,r._)([(0,c.r)("service","featureCollections",["featureCollection.layers"])],P.prototype,"readFeatureCollections",null),(0,r._)([(0,u.Cb)({type:g.Z,json:{name:"lookAtExtent"}})],P.prototype,"fullExtent",void 0),(0,r._)([(0,u.Cb)(C.id)],P.prototype,"id",void 0),(0,r._)([(0,u.Cb)(C.rn)],P.prototype,"legendEnabled",void 0),(0,r._)([(0,u.Cb)({types:I,json:{write:!0}})],P.prototype,"lineSymbol",void 0),(0,r._)([(0,u.Cb)({type:["show","hide"]})],P.prototype,"listMode",void 0),(0,r._)([(0,u.Cb)({types:T,json:{write:!0}})],P.prototype,"pointSymbol",void 0),(0,r._)([(0,u.Cb)({types:O,json:{write:!0}})],P.prototype,"polygonSymbol",void 0),(0,r._)([(0,u.Cb)({type:["GeoRSS"]})],P.prototype,"operationalLayerType",void 0),(0,r._)([(0,u.Cb)(C.HQ)],P.prototype,"url",void 0),(0,r._)([(0,u.Cb)({json:{origins:{service:{read:{source:"name",reader:e=>e||void 0}}}}})],P.prototype,"title",null),(0,r._)([(0,u.Cb)({readOnly:!0,json:{read:!1},value:"geo-rss"})],P.prototype,"type",void 0),P=(0,r._)([(0,d.j)("esri.layers.GeoRSSLayer")],P);const R=P},16859:(e,t,s)=>{s.d(t,{I:()=>w});var r=s(43697),i=s(68773),n=s(40330),l=s(3172),a=s(66643),o=s(20102),h=s(92604),u=s(70586),c=s(95330),d=s(17452),g=s(5600),p=(s(75215),s(67676),s(71715)),f=s(52011),m=s(30556),_=s(84230),y=s(65587),b=s(15235),v=s(86082),C=s(14661);const w=e=>{let t=class extends e{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,u.SC)(this.portalItem)}set portalItem(e){e!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",e))}readPortalItem(e,t,s){if(t.itemId)return new b.default({id:t.itemId,portal:s&&s.portal})}writePortalItem(e,t){e&&e.id&&(t.itemId=e.id)}async loadFromPortal(e,t){if(this.portalItem&&this.portalItem.id)try{const r=await s.e(8062).then(s.bind(s,18062));return(0,c.k_)(t),await r.load({instance:this,supportedTypes:e.supportedTypes,validateItem:e.validateItem,supportsData:e.supportsData,layerModuleTypeMap:e.layerModuleTypeMap},t)}catch(e){throw(0,c.D_)(e)||h.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${e}`),e}}async finishLoadEditablePortalLayer(e){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(e).catch((e=>((0,c.r9)(e),!0))))}async _setUserPrivileges(e,t){if(!i.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(t);if(this.url)try{const{features:{edit:s,fullEdit:r},content:{updateItem:i}}=await this._fetchUserPrivileges(e,t);this._set("userHasEditingPrivileges",s),this._set("userHasFullEditingPrivileges",r),this._set("userHasUpdateItemPrivileges",i)}catch(e){(0,c.r9)(e)}}async _fetchUserPrivileges(e,t){let s=this.portalItem;if(!e||!s||!s.loaded||s.sourceUrl)return this._fetchFallbackUserPrivileges(t);const r=e===s.id;if(r&&s.portal.user)return(0,C.Ss)(s);let i,l;if(r)i=s.portal.url;else try{i=await(0,_.oP)(this.url,t)}catch(e){(0,c.r9)(e)}if(!i||!(0,d.Zo)(i,s.portal.url))return this._fetchFallbackUserPrivileges(t);try{const e=(0,u.pC)(t)?t.signal:null;l=await(n.id?.getCredential(`${i}/sharing`,{prompt:!1,signal:e}))}catch(e){(0,c.r9)(e)}if(!l)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(r?await s.reload():(s=new b.default({id:e,portal:{url:i}}),await s.load(t)),s.portal.user)return(0,C.Ss)(s)}catch(e){(0,c.r9)(e)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(e){let t=!0;try{t=await this._fetchUserHasEditingPrivileges(e)}catch(e){(0,c.r9)(e)}return{features:{edit:t,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(e){const t=this.url?n.id?.findCredential(this.url):null;if(!t)return!0;const s=E.credential===t?E.user:await this._fetchEditingUser(e);return E.credential=t,E.user=s,(0,u.Wi)(s)||null==s.privileges||s.privileges.includes("features:user:edit")}async _fetchEditingUser(e){const t=this.portalItem?.portal?.user;if(t)return t;const s=n.id.findServerInfo(this.url??"");if(!s?.owningSystemUrl)return null;const r=`${s.owningSystemUrl}/sharing/rest`,i=y.Z.getDefault();if(i&&i.loaded&&(0,d.Fv)(i.restUrl)===(0,d.Fv)(r))return i.user;const o=`${r}/community/self`,h=(0,u.pC)(e)?e.signal:null,c=await(0,a.q6)((0,l.default)(o,{authMode:"no-prompt",query:{f:"json"},signal:h}));return c.ok?v.default.fromJSON(c.value.data):null}read(e,t){t&&(t.layer=this),super.read(e,t)}write(e,t){const s=t&&t.portal,r=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||y.Z.getDefault());return s&&r&&!(0,d.tm)(r.restUrl,s.restUrl)?(t.messages&&t.messages.push(new o.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(e,{...t,layer:this})}};return(0,r._)([(0,g.Cb)({type:b.default})],t.prototype,"portalItem",null),(0,r._)([(0,p.r)("web-document","portalItem",["itemId"])],t.prototype,"readPortalItem",null),(0,r._)([(0,m.c)("web-document","portalItem",{itemId:{type:String}})],t.prototype,"writePortalItem",null),(0,r._)([(0,g.Cb)({clonable:!1})],t.prototype,"resourceReferences",void 0),(0,r._)([(0,g.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasEditingPrivileges",void 0),(0,r._)([(0,g.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasFullEditingPrivileges",void 0),(0,r._)([(0,g.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasUpdateItemPrivileges",void 0),t=(0,r._)([(0,f.j)("esri.layers.mixins.PortalLayer")],t),t},E={credential:null,user:null}},34760:(e,t,s)=>{s.d(t,{Q:()=>_});var r=s(43697),i=s(92604),n=s(95330),l=s(5600),a=(s(75215),s(67676),s(52011)),o=s(46791),h=(s(80442),s(20102),s(26258),s(87538));const u=new o.Z,c=new WeakMap;function d(e){return null!=e&&"object"==typeof e&&"refreshInterval"in e&&"refresh"in e}function g(e,t){return Number.isFinite(e)&&Number.isFinite(t)?t<=0?e:g(t,e%t):0}let p=0,f=0;function m(){const e=Date.now();for(const t of u)t.refreshInterval&&e-(c.get(t)??0)+5>=6e4*t.refreshInterval&&(c.set(t,e),t.refresh(e))}(0,h.EH)((()=>{const e=Date.now();let t=0;for(const s of u)t=g(Math.round(6e4*s.refreshInterval),t),s.refreshInterval?c.get(s)||c.set(s,e):c.delete(s);if(t!==f){if(f=t,clearInterval(p),0===f)return void(p=0);p=setInterval(m,f)}}));const _=e=>{let t=class extends e{constructor(...e){super(...e),this.refreshInterval=0,this.refreshTimestamp=0,this._debounceHasDataChanged=(0,n.Ds)((()=>this.hasDataChanged())),this.when().then((()=>{!function(e){d(e)&&u.push(e)}(this)}),(()=>{}))}destroy(){d(this)&&u.includes(this)&&u.remove(this)}get refreshParameters(){return{_ts:this.refreshTimestamp||null}}refresh(e=Date.now()){(0,n.R8)(this._debounceHasDataChanged()).then((t=>{t&&this._set("refreshTimestamp",e),this.emit("refresh",{dataChanged:t})}),(e=>{i.Z.getLogger(this.declaredClass).error(e),this.emit("refresh",{dataChanged:!1,error:e})}))}async hasDataChanged(){return!0}};return(0,r._)([(0,l.Cb)({type:Number,cast:e=>e>=.1?e:e<=0?0:.1,json:{write:!0}})],t.prototype,"refreshInterval",void 0),(0,r._)([(0,l.Cb)({readOnly:!0})],t.prototype,"refreshTimestamp",void 0),(0,r._)([(0,l.Cb)()],t.prototype,"refreshParameters",null),t=(0,r._)([(0,a.j)("esri.layers.mixins.RefreshableLayer")],t),t}}}]);