import Cookies from 'js-cookie'
import { removeSlash } from '@/utils/removeIdSlash'
/**
 * 获取各种id
 * @returns id||''
 */
const useIds = () => {
  // 企业id
  const tenantId = Cookies.get('tenantId')
  // 用户id
  const userId = Cookies.get('userId' || '')
  /**
   * 去除id的-
   * @param id
   * @returns
   */
  const IdWithoutSlash = (id:string) => removeSlash(id)
  return {
    tenantId,
    userId,
    IdWithoutSlash
  }
}
export default useIds
