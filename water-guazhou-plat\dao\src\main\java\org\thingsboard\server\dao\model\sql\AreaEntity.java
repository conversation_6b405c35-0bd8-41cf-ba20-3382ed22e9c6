package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-01
 */
@TableName("tb_area")
@Data
public class AreaEntity {

    @TableId
    private String id;

    private String parentId;

    private String serialId;

    private String name;

    private String shortName;

    private String nickName;

    private String img;

    private String remark;

    private Integer orderNum;

    private String creator;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private transient List<AreaEntity> children;

}
