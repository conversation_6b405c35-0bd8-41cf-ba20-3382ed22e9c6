package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.VO.TreeNodeVO;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ComponentStorageEntity;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountComponentEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsFileEntity;
import org.thingsboard.server.dao.sql.componentStorage.ComponentStorageRepository;
import org.thingsboard.server.dao.sql.project.ProjectRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsAccountRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsFileRepository;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
@Service
@Slf4j
public class AssetsAccountServiceImpl implements AssetsAccountService {

    @Autowired
    private AssetsAccountRepository assetsAccountRepository;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private AssetsFileRepository assetsFileRepository;
    @Autowired
    private AssetsAccountComponentService assetsAccountComponentService;
    @Autowired
    private ComponentStorageRepository componentStorageRepository;

    @Value("${install.image_dir}")
    private String imageDir;

    @Value("${img.url}")
    private String imgUrl;

    @Override
    public PageData getPage(JSONObject params) {
        int page = 0;
        int limit = 10;
        String name = "";
        String deviceStatus = "";
        String deviceNo = ""; // 设备编号
        String deviceGrade = ""; // 设备等级
        String deviceType = ""; // 设备类型
        String specificationModel = ""; // 规格型号
        String brand = ""; // 品牌
        String supplier = ""; // 供应商
        String deviceSource = ""; // 设备来源
        Long purchaseStart = 0l; // 采购开始日期
        Long purchaseEnd = System.currentTimeMillis(); // 采购结束日期
        Long warrantyStart = 0l; // 保修开始日期
        Long warrantyEnd = Long.MAX_VALUE; // 保修结束日期
        Long enableStart = 0l; // 启用开始日期
        Long enableEnd = Long.MAX_VALUE; // 启用结束日期
        Long expectScrapStart = 0l; // 期望报废开始时间
        Long expectScrapEnd = Long.MAX_VALUE; // 期望报废结束时间
        String projectId = ""; // 所属项目id
        String tenantId = params.getString("tenantId");

        if (params.getInteger("page") != null) {
            page = params.getInteger("page") - 1;
        }
        if (params.getInteger("limit") != null) {
            limit = params.getInteger("limit");
        }

        if (params.getString("name") != null) {
            name = params.getString("name");
        }
        name = "%" + name + "%";

        if (params.getString("deviceStatus") != null) {
            deviceStatus = params.getString("deviceStatus");
        }
        deviceStatus = "%" + deviceStatus + "%";

        if (params.getString("deviceNo") != null) {
            deviceNo = params.getString("deviceNo");
        }
        deviceNo = "%" + deviceNo + "%";

        if (params.getString("deviceGrade") != null) {
            deviceGrade = params.getString("deviceGrade");
        }
        deviceGrade = "%" + deviceGrade + "%";

        if (params.getString("deviceType") != null) {
            deviceType = params.getString("deviceType");
        }
        deviceType = "%" + deviceType + "%";

        if (params.getString("specificationModel") != null) {
            specificationModel = params.getString("specificationModel");
        }
        specificationModel = "%" + specificationModel + "%";

        if (params.getString("brand") != null) {
            brand = params.getString("brand");
        }
        brand = "%" + brand + "%";

        if (params.getString("supplier") != null) {
            supplier = params.getString("supplier");
        }
        supplier = "%" + supplier + "%";

        if (params.getString("deviceSource") != null) {
            deviceSource = params.getString("deviceSource");
        }
        deviceSource = "%" + deviceSource + "%";

        if (params.getString("projectId") != null) {
            projectId = params.getString("projectId");
        }
        projectId = "%" + projectId + "%";

        if (params.getLong("purchaseStart") != null) {
            purchaseStart = params.getLong("purchaseStart");
        }
        if (params.getLong("purchaseEnd") != null) {
            purchaseEnd = params.getLong("purchaseEnd");
        }
        if (params.getLong("warrantyStart") != null) {
            warrantyStart = params.getLong("warrantyStart");
        }
        if (params.getLong("warrantyEnd") != null) {
            warrantyEnd = params.getLong("warrantyEnd");
        }
        if (params.getLong("enableStart") != null) {
            enableStart = params.getLong("enableStart");
        }
        if (params.getLong("enableEnd") != null) {
            enableEnd = params.getLong("enableEnd");
        }
        if (params.getLong("expectScrapStart") != null) {
            expectScrapStart = params.getLong("expectScrapStart");
        }
        if (params.getLong("expectScrapEnd") != null) {
            expectScrapEnd = params.getLong("expectScrapEnd");
        }
        PageRequest pageRequest = new PageRequest(page, limit, new Sort(Sort.Direction.DESC, "createTime"));
        Page<AssetsAccountEntity> assetsAccountEntities = assetsAccountRepository.findAllByDeviceNameLikeAndDeviceStatusLikeAndDeviceNoLikeAndDeviceGradeLikeAndDeviceTypeLikeAndSpecificationModelLikeAndBrandLikeAndSupplierLikeAndDeviceSourceLikeAndProjectIdLikeAndTenantIdAndPurchaseTimeBetweenAndWarrantyTimeBetweenAndEnableTimeBetweenAndExpectScrapTimeBetween(name, deviceStatus, deviceNo, deviceGrade, deviceType, specificationModel, brand, supplier, deviceSource, projectId, tenantId, purchaseStart, purchaseEnd, warrantyStart, warrantyEnd, enableStart, enableEnd, expectScrapStart, expectScrapEnd, pageRequest);

        PageData<AssetsAccountEntity> pageData = new PageData(assetsAccountEntities.getTotalElements(), assetsAccountEntities.getContent());

        for (AssetsAccountEntity assetsAccountEntity : pageData.getData()) {
            // 获取项目名
            ProjectEntity projectEntity = projectRepository.findOne(assetsAccountEntity.getProjectId());
            if (projectEntity != null) {
                assetsAccountEntity.setProjectName(projectEntity.getName());
            }
        }

        return pageData;
    }

    @Override
    public AssetsAccountEntity save(AssetsAccountEntity assetsAccountEntity) {
        if (assetsAccountEntity.getId() == null) { // 是否新增
            assetsAccountEntity.setCreateTime(System.currentTimeMillis());
        }
        assetsAccountEntity.setUpdateTime(System.currentTimeMillis());
        if (assetsAccountEntity.getPurchaseTime() == null) {
            assetsAccountEntity.setPurchaseTime(System.currentTimeMillis());
        }
        if (assetsAccountEntity.getEnableTime() == null) {
            assetsAccountEntity.setEnableTime(System.currentTimeMillis());
        }
        if (assetsAccountEntity.getExpectScrapTime() == null) {
            assetsAccountEntity.setExpectScrapTime(System.currentTimeMillis());
        }

        // 设备编号 自动生成
        if (StringUtils.isBlank(assetsAccountEntity.getDeviceNo())) {
            SimpleDateFormat format = new SimpleDateFormat("yyMMddHHmmssSSS");
            String deviceNo = "DE" + format.format(new Date());
            assetsAccountEntity.setDeviceNo(deviceNo);

        }

        if (assetsAccountEntity.getPurchaseTime() == null) {
            assetsAccountEntity.setPurchaseTime(1l);
        }
        if (assetsAccountEntity.getWarrantyTime() == null) {
            assetsAccountEntity.setWarrantyTime(1l);
        }
        if (assetsAccountEntity.getEnableTime() == null) {
            assetsAccountEntity.setEnableTime(1l);
        }
        if (assetsAccountEntity.getExpectScrapTime() == null) {
            assetsAccountEntity.setExpectScrapTime(1l);
        }

        if (StringUtils.isBlank(assetsAccountEntity.getDeviceName())) {
            assetsAccountEntity.setDeviceName("");
        }

        if (StringUtils.isBlank(assetsAccountEntity.getDeviceStatus())) {
            assetsAccountEntity.setDeviceStatus("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getDeviceNo())) {
            assetsAccountEntity.setDeviceNo("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getDeviceGrade())) {
            assetsAccountEntity.setDeviceGrade("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getDeviceType())) {
            assetsAccountEntity.setDeviceType("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getSpecificationModel())) {
            assetsAccountEntity.setSpecificationModel("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getBrand())) {
            assetsAccountEntity.setBrand("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getSupplier())) {
            assetsAccountEntity.setSupplier("");
        }
        if (StringUtils.isBlank(assetsAccountEntity.getDeviceSource())) {
            assetsAccountEntity.setDeviceSource("");
        }

        // 查找子设备列表
        if (StringUtils.isNotBlank(assetsAccountEntity.getSubDeviceIds())) {
            String[] split = assetsAccountEntity.getSubDeviceIds().split(",");
            for (String id : split) {
                AssetsAccountEntity one = assetsAccountRepository.findOne(id);
                if (one != null) {
                    one.setParentDeviceIds(assetsAccountEntity.getId());
                    assetsAccountRepository.save(one);
                }
            }
        }
        // 查找父设备列表
        if (StringUtils.isNotBlank(assetsAccountEntity.getParentDeviceIds())) {
            AssetsAccountEntity one = assetsAccountRepository.findOne(assetsAccountEntity.getParentDeviceIds());
            if (one != null) {
                if (StringUtils.isBlank(one.getSubDeviceIds())) one.setSubDeviceIds("");

                if (one.getSubDeviceIds().length() > 0 && !one.getSubDeviceIds().contains(assetsAccountEntity.getId())) {
                    one.setSubDeviceIds(one.getSubDeviceIds() + "," + assetsAccountEntity.getId());
                }
                if (one.getSubDeviceIds().length() == 0) {
                    one.setSubDeviceIds(assetsAccountEntity.getId());
                }
                assetsAccountRepository.save(one);
            }
        }

        if (StringUtils.isNotBlank(assetsAccountEntity.getId())) {
            // 是否删除子设备
            AssetsAccountEntity one = assetsAccountRepository.findOne(assetsAccountEntity.getId());
            if (StringUtils.isBlank(one.getSubDeviceIds())) one.setSubDeviceIds("");
            String[] subDeviceIds = one.getSubDeviceIds().split(",");
            if (StringUtils.isBlank(assetsAccountEntity.getSubDeviceIds())) assetsAccountEntity.setSubDeviceIds("");

            for (String id : subDeviceIds) {
                // 判断该子设备是不是被删除
                if (!assetsAccountEntity.getSubDeviceIds().contains(id)) {
                    // 从该设备的父设备中移除该设备
                    AssetsAccountEntity accountEntity = assetsAccountRepository.findOne(id);
                    accountEntity.setParentDeviceIds("");
                    assetsAccountRepository.save(accountEntity);
                }
            }


            // 是否删除\更换父设备
            if (StringUtils.isBlank(assetsAccountEntity.getParentDeviceIds())) assetsAccountEntity.setParentDeviceIds("");
            if (StringUtils.isNotBlank(one.getParentDeviceIds()) && !one.getParentDeviceIds().equals(assetsAccountEntity.getParentDeviceIds())) {
                AssetsAccountEntity parentOne = assetsAccountRepository.findOne(one.getParentDeviceIds());

                if (parentOne != null && StringUtils.isNotBlank(parentOne.getSubDeviceIds())) {
                    String newSubDeviceIds = "";
                    for (String id : parentOne.getSubDeviceIds().split(",")) {
                        if (!one.getId().equals(id)) {
                            newSubDeviceIds = newSubDeviceIds + "," + id;
                        }
                    }
                    if (StringUtils.isNotBlank(newSubDeviceIds)) {
                        newSubDeviceIds = newSubDeviceIds.substring(1);
                    }
                    parentOne.setSubDeviceIds(newSubDeviceIds);
                    assetsAccountRepository.save(parentOne);
                }
            }
        }

        assetsAccountRepository.save(assetsAccountEntity);

        // 关联备件
        assetsAccountComponentService.deleteByPid(assetsAccountEntity.getId());
        if (assetsAccountEntity.getComponentList() != null && assetsAccountEntity.getComponentList().size() > 0) {
            AssetsAccountComponentEntity assetsAccountComponentEntity;
            for (AssetsAccountComponentEntity ComponentStorageEntity : assetsAccountEntity.getComponentList()) {
                assetsAccountComponentEntity = new AssetsAccountComponentEntity();
                assetsAccountComponentEntity.setPid(assetsAccountEntity.getId());
                assetsAccountComponentEntity.setComponentId(ComponentStorageEntity.getComponentId());
                assetsAccountComponentEntity.setNum(ComponentStorageEntity.getNum());
                assetsAccountComponentEntity.setCreateTime(System.currentTimeMillis());
                assetsAccountComponentEntity.setProjectId(assetsAccountEntity.getProjectId());
                assetsAccountComponentEntity.setTenantId(assetsAccountEntity.getTenantId());
                assetsAccountComponentService.save(assetsAccountComponentEntity);
            }
        }

        return assetsAccountEntity;
    }

    @Override
    public void delete(List<String> ids) {
        for (String id : ids) {
            assetsAccountRepository.delete(id);
            // 删除备件
            assetsAccountComponentService.deleteByPid(id);
        }
    }

    @Override
    public AssetsAccountEntity getDetail(String id) {
        AssetsAccountEntity assetsAccountEntity = assetsAccountRepository.findOne(id);
        if (assetsAccountEntity == null) {
            return null;
        }

        // 获取项目名
        if (StringUtils.isNotBlank(assetsAccountEntity.getProjectId())) {
            ProjectEntity projectEntity = projectRepository.findOne(assetsAccountEntity.getProjectId());
            if (projectEntity != null) {
                assetsAccountEntity.setProjectName(projectEntity.getName());
            }
        }

        // 设备相关文件
        assetsAccountEntity.setAssetsFileList(new ArrayList<>());
        String fileIds = assetsAccountEntity.getFileIds();
        if (StringUtils.isNotBlank(fileIds)) {
            String[] fileIdArr = fileIds.split(",");
            for (String fileId : fileIdArr) {
                AssetsFileEntity fileEntity = assetsFileRepository.findOne(fileId);
                if (fileEntity != null) {
                    fileEntity.setUrl(imgUrl.replace("getImgByName", "viewFile") + "?fileName=" + fileEntity.getId() + fileEntity.getName().substring(fileEntity.getName().lastIndexOf(".")) + "&projectId=" + fileEntity.getProjectId());
                    assetsAccountEntity.getAssetsFileList().add(fileEntity);
                }
            }
        }

        // 子设备列表
        assetsAccountEntity.setSubAssetsAccountList(new ArrayList<>());
        String subDeviceIds = assetsAccountEntity.getSubDeviceIds();
        if (StringUtils.isNotBlank(subDeviceIds)) {
            String[] subDeviceIdArr = subDeviceIds.split(",");
            for (String subDeviceId : subDeviceIdArr) {
                AssetsAccountEntity subAccountEntity = assetsAccountRepository.findOne(subDeviceId);
                if (subAccountEntity != null) {
                    assetsAccountEntity.getSubAssetsAccountList().add(subAccountEntity);
                }
            }
        }
        // 是否其他设备子设备
        List<AssetsAccountEntity> allByParentDeviceIdsLike = assetsAccountRepository.findAllByParentDeviceIdsLike("%" + assetsAccountEntity.getId() + "%");
        if (StringUtils.isBlank(assetsAccountEntity.getSubDeviceIds())) assetsAccountEntity.setSubDeviceIds("");
        for (AssetsAccountEntity assetsAccountEntity1 : allByParentDeviceIdsLike) {
            if (!assetsAccountEntity.getSubDeviceIds().contains(assetsAccountEntity1.getId())) {
                if (assetsAccountEntity.getSubDeviceIds().length() > 0) {
                    assetsAccountEntity.setSubDeviceIds(assetsAccountEntity.getSubDeviceIds() + "," + assetsAccountEntity1.getId());
                } else {
                    assetsAccountEntity.setSubDeviceIds(assetsAccountEntity1.getId());
                }

                assetsAccountEntity.getSubAssetsAccountList().add(assetsAccountEntity1);
            }
        }

        // 父设备列表
        assetsAccountEntity.setParentAssetsAccountList(new ArrayList<>());
        String parentDeviceIds = assetsAccountEntity.getParentDeviceIds();
        if (StringUtils.isNotBlank(parentDeviceIds)) {
            String[] parentDeviceIdArr = parentDeviceIds.split(",");
            for (String parentDeviceId : parentDeviceIdArr) {
                AssetsAccountEntity parentAccountEntity = assetsAccountRepository.findOne(parentDeviceId);
                if (parentAccountEntity != null) {
                    assetsAccountEntity.getParentAssetsAccountList().add(parentAccountEntity);
                }
            }
        }
        // 是否其他设备父设备
        if (StringUtils.isBlank(assetsAccountEntity.getParentDeviceIds())) assetsAccountEntity.setParentDeviceIds("");
        List<AssetsAccountEntity> allBySubDeviceIdsLike = assetsAccountRepository.findAllBySubDeviceIdsLike("%" + assetsAccountEntity.getId() + "%");
        for (AssetsAccountEntity assetsAccountEntity1 : allBySubDeviceIdsLike) {
            if (!assetsAccountEntity.getParentDeviceIds().contains(assetsAccountEntity1.getId())) {
                if (assetsAccountEntity.getParentDeviceIds().length() > 0) {
                    assetsAccountEntity.setParentDeviceIds(assetsAccountEntity.getParentDeviceIds() + "," + assetsAccountEntity1.getId());
                } else {
                    assetsAccountEntity.setParentDeviceIds(assetsAccountEntity1.getId());
                }

                assetsAccountEntity.getParentAssetsAccountList().add(assetsAccountEntity1);
            }
        }
        // 备件
        List<AssetsAccountComponentEntity> accountComponentStorageEntities = assetsAccountComponentService.findListByPid(id);

        for (AssetsAccountComponentEntity assetsAccountComponentEntity : accountComponentStorageEntities) {
            // 查找备件
            if (StringUtils.isBlank(assetsAccountComponentEntity.getComponentId())) {
                continue;
            }
            ComponentStorageEntity storageEntity = componentStorageRepository.findOne(assetsAccountComponentEntity.getComponentId());
            if (storageEntity == null) {
                continue;
            }

            assetsAccountComponentEntity.setName(storageEntity.getName());
            assetsAccountComponentEntity.setComponentNo(storageEntity.getCode());
            assetsAccountComponentEntity.setType(storageEntity.getType());
            assetsAccountComponentEntity.setSpecification(storageEntity.getSpecification());
            assetsAccountComponentEntity.setUnit(storageEntity.getUnit());
        }
        assetsAccountEntity.setComponentList(accountComponentStorageEntities);

        return assetsAccountEntity;
    }

    @Override
    public AssetsAccountEntity findByDeviceId(String deviceId) {
        List<AssetsAccountEntity> list = assetsAccountRepository.findByDeviceId(deviceId);
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<String> getDeviceTypeList(String tenantId, String projectId) {
        if (StringUtils.isBlank(projectId)) {
            projectId = "%%";
        }
        List<String> deviceTypeList = assetsAccountRepository.getDeviceTypeList(tenantId, projectId);
        if (deviceTypeList == null) {
            return new ArrayList<>();
        }
        return deviceTypeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @Override
    public List<AssetsAccountEntity> getListByDeviceType(String deviceType, String tenantId, String projectId) {
        if (StringUtils.isBlank(projectId)) {
            projectId = "%%";
        }
        return assetsAccountRepository.getListByDeviceType(deviceType, tenantId, projectId);
    }

    @Override
    public List<AssetsAccountEntity> findByTenantId(String tenantId) {
        return assetsAccountRepository.findByTenantId(tenantId);
    }

    public PageData getUnionPage(JSONObject params) {

        String name = "";

        String deviceType = ""; // 设备类型

        String projectId = ""; // 所属项目id

        String tenantId = params.getString("tenantId");

        int page = 0;
        int limit = 10;

        if (params.getInteger("page") != null) {
            page = params.getInteger("page") - 1;
        }
        if (params.getInteger("limit") != null) {
            limit = params.getInteger("limit");
        }

        if (params.getString("name") != null) {
            name = params.getString("name");
        }
        name = "%" + name + "%";


        if (params.getString("deviceType") != null) {
            deviceType = params.getString("deviceType");
        }
        deviceType = "%" + deviceType + "%";


        if (params.getString("projectId") != null) {
            projectId = params.getString("projectId");
        }
        projectId = "%" + projectId + "%";

        List<String> notInIdList = new ArrayList<>();
        if (params.getJSONArray("chooseIds") != null) {
            notInIdList = params.getJSONArray("chooseIds").toJavaList(String.class);
        }
        if (notInIdList == null || notInIdList.size() == 0) {
            notInIdList.add("aaa111");
        }
        PageRequest pageRequest = new PageRequest(page, limit, new Sort(Sort.Direction.DESC, "createTime"));
        Page<AssetsAccountEntity> assetsAccountEntities = assetsAccountRepository.findAllByDeviceNameLikeAndDeviceTypeLikeAndProjectIdLikeAndTenantIdLikeAndIdNotIn(name, deviceType, projectId, tenantId, notInIdList, pageRequest);

        PageData<AssetsAccountEntity> pageData = new PageData(assetsAccountEntities.getTotalElements(), assetsAccountEntities.getContent());

        for (AssetsAccountEntity assetsAccountEntity : pageData.getData()) {
            // 获取项目名
            ProjectEntity projectEntity = projectRepository.findOne(assetsAccountEntity.getProjectId());
            if (projectEntity != null) {
                assetsAccountEntity.setProjectName(projectEntity.getName());
            }
        }

        return pageData;
    }

    @Override
    public List<TreeNodeVO> getListTree(String projectId) {

        List<AssetsAccountEntity> assetsAccountEntityList = assetsAccountRepository.findByProjectId(projectId);

        Map<String, List<AssetsAccountEntity>> map = new HashMap<>();
        for (AssetsAccountEntity entity : assetsAccountEntityList) {
            String deviceType = entity.getDeviceType();
            if (StringUtils.isBlank(deviceType)) {
                continue;
            }
            List<AssetsAccountEntity> list = new ArrayList<>();
            if (map.containsKey(deviceType)) {
                list = map.get(deviceType);
            }

            list.add(entity);
            map.put(deviceType, list);
        }

        List<TreeNodeVO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<AssetsAccountEntity>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<AssetsAccountEntity> value = entry.getValue();

            if (value == null || value.isEmpty()) {
                continue;
            }
            TreeNodeVO node = new TreeNodeVO();
            node.setNodeId(key);
            node.setNodeName(key);

            List<TreeNodeVO> childrenList = new ArrayList<>();
            for (AssetsAccountEntity entity : value) {
                TreeNodeVO children = new TreeNodeVO();
                children.setNodeId(entity.getId());
                children.setNodeName(entity.getDeviceName());
                children.setData(entity);

                childrenList.add(children);
            }
            node.setChildren(childrenList);

            resultList.add(node);
        }

        return resultList;
    }

    @Override
    public AssetsAccountEntity findById(String id) {
        return assetsAccountRepository.findOne(id);
    }
}
