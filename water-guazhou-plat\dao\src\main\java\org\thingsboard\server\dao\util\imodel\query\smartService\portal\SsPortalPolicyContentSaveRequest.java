package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicyContent;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalPolicyContentSaveRequest extends SaveRequest<SsPortalPolicyContent> {
    // 名称
    @NotNullOrEmpty
    private String title;

    // 法规类型
    @NotNullOrEmpty
    private String typeId;

    // 内容
    private String content;

    // 附件
    private String attachment;


    @Override
    protected SsPortalPolicyContent build() {
        SsPortalPolicyContent entity = new SsPortalPolicyContent();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalPolicyContent update(String id) {
        SsPortalPolicyContent entity = new SsPortalPolicyContent();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalPolicyContent entity) {
        entity.setTitle(title);
        entity.setTypeId(typeId);
        entity.setContent(content);
        entity.setAttachment(attachment);
    }

}