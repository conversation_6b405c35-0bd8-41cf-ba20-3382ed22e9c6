package org.thingsboard.server.dao.app;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.AppBottomMenu;
import org.thingsboard.server.dao.sql.app.AppBottomMenuRepository;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AppBottomMenuServiceImpl implements AppBottomMenuService {

    @Autowired
    private AppBottomMenuRepository appBottomMenuRepository;

    @Override
    public List<AppBottomMenu> findList(TenantId tenantId) {
        return appBottomMenuRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    @Transactional
    public void save(List<AppBottomMenu> appBottomMenuList, TenantId tenantId) {
        appBottomMenuRepository.deleteByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        for (AppBottomMenu appBottomMenu : appBottomMenuList) {
            appBottomMenu.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            appBottomMenu.setCreateTime(new Date());
        }
        appBottomMenuRepository.save(appBottomMenuList);
    }
}
