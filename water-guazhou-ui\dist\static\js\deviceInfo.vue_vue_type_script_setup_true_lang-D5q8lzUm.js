import{g as y}from"./ledgerManagement-CkhtRd8m.js";import{g as b}from"./equipmentManage-DuoY00aj.js";import{d as g}from"./index-CCFuhOrs.js";import{d as v,c as r,r as n,o as _,g as c,n as h,h as k,i as f}from"./index-r0dFAfgr.js";const z={style:{width:"calc(100% - 20px)"}},T=v({__name:"deviceInfo",props:{id:{},serialId:{}},setup(p){const x=r(),d=p,s=r(new Date().toString()),o=n({title:"",defaultValue:{},border:!0,direction:"vertical",labelAlign:"left",fields:[{xl:8,type:"text",label:"设备编号:",field:"serialId"},{xl:8,type:"text",label:"设备名称:",field:"deviceName"},{xl:8,type:"text",label:"设备单位:",field:"deviceUnit"},{xl:8,type:"text",label:"设备类型:",field:"deviceType"},{xl:8,type:"text",label:"设备型号:",field:"deviceModel"},{xl:8,type:"text",label:"供应商名称:",field:"supplierName"},{xl:8,type:"text",label:"采购金额:",field:"price"},{xl:8,type:"text",label:"入库时间:",field:"createTime"},{xl:8,type:"text",label:"使用年限:",field:"deviceUseYear"},{xl:8,type:"text",label:"出库时间:",field:"outTime"},{xl:8,type:"text",label:"报废时间:",field:"scrappedTime"},{xl:8,type:"text",label:"备注:",field:"remark"},{xl:8,type:"text",label:"租户名称:",field:"tenantName"},{xl:24,readonly:!0,type:"image",label:"设备图片:",field:"deviceImages"},{xl:24,readonly:!0,type:"file",label:"设备文件:",field:"deviceFiles"}]}),a=n({prepend:"",serialId:"",customize:[],getCustomizeValue:i=>{a.customize=[],b(i).then(e=>{(e.data.data||[]).forEach(t=>{const u={xl:8,type:"text",label:t.name+":",field:"autoField"+t.code};a.customize.push(u)}),o.fields.splice(12,0,...a.customize),m()})}}),m=async()=>{y(d.id).then(i=>{const e=i.data.data||{};for(const t in e)(e[t]===void 0||e[t]===null)&&(e[t]=" ");const l=JSON.parse(e.autoField==" "?"{}":e.autoField);for(const t in l)l[t.split(".")[0]+t.split(".")[1]]=l[t];o.defaultValue={...e||{},...l},setTimeout(()=>{s.value=new Date().toString()},1e3)})};return _(()=>{a.getCustomizeValue(d.serialId.slice(0,-6)+"000000")}),(i,e)=>(c(),h("div",z,[(c(),k(g,{key:f(s),ref_key:"refForm",ref:x,config:f(o)},null,8,["config"]))]))}});export{T as _};
