package org.thingsboard.server.service.task;

import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.dao.dataSource.DataSourceGroup;
import org.thingsboard.server.dao.dataSource.DataSourceService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.service.utils.SchedulerUtils;

import java.util.List;

import static org.thingsboard.server.service.utils.SchedulerUtils.startScheduler;

/**
 * <AUTHOR>
 * @date 2020/4/15 10:16
 */
@Slf4j
@Component
public class StatisticsTask implements ApplicationRunner {


    private Scheduler scheduler;

    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DeviceService deviceService;


    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        processPreparation();
    }


    /**
     * 执行预设统计量
     */
    private void processPreparation() throws SchedulerException {
        scheduler = SchedulerUtils.getSchedulerFactory().getScheduler();
        //获取平台所有预统计量
        List<DataSourceEntity> rootSource = dataSourceService.findByProjectIdAndType(DataConstants.ROOT, DataSourceType.PREPARATION_SOURCE);
        startScheduler(rootSource, scheduler, DataConstants.ROOT, DataConstants.ROOT, true);
        startScheduler(rootSource, scheduler, DataConstants.ROOT, DataConstants.ROOT, false);
        processTenantScheduler(scheduler, true);
        processTenantScheduler(scheduler, false);
        processProjectScheduler(scheduler, true);
        processProjectScheduler(scheduler, false);
        processRestApi(scheduler);

    }

    /**
     * 执行企业下数据源统计
     */
    private void processTenantScheduler(Scheduler scheduler, boolean isOrigin) {
        log.info("开始企业下数据源统计");
        List<Tenant> tenants = tenantService.findAll();
        tenants.forEach(tenant -> {
            List<DataSourceGroup> dataSourceGroups = dataSourceService.findDataSourceGroupByOriginatorId(UUIDConverter.fromTimeUUID(tenant.getUuidId()));
            dataSourceGroups.forEach(dataSourceGroup -> {
                try {
                    if (dataSourceGroup.getType().equals(DataSourceType.PREPARATION_SOURCE.name()) || dataSourceGroup.getType().equals(DataSourceType.STATISTICS_SOURCE.name())) {
                        startScheduler(dataSourceGroup.getDataSourceList(), scheduler, UUIDConverter.fromTimeUUID(tenant.getUuidId()), DataConstants.TENANT, isOrigin);
                    }
                } catch (SchedulerException e) {
                    e.printStackTrace();
                }
            });
        });
    }


    /**
     * 执行restApi数据源
     */
    private void processRestApi(Scheduler scheduler) {
        log.info("开始restApi数据源统计");
        List<RestApiEntity> restApiEntities = dataSourceService.findAll();
        restApiEntities.forEach(restApiEntity -> {
            try {
                SchedulerUtils.processRestApi(restApiEntity, scheduler);
            } catch (Exception e) {
                e.printStackTrace();
            }

        });
    }


    /**
     * 执行项目下数据源统计
     */
    private void processProjectScheduler(Scheduler scheduler, boolean isOrigin) {
        log.info("开始项目数据源统计");
        List<ProjectEntity> projectEntities = projectService.findAllProject();
        projectEntities.forEach(project -> {
            List<DataSourceGroup> dataSourceGroups = dataSourceService.findDataSourceGroupByOriginatorId(project.getId());
            dataSourceGroups.forEach(dataSourceGroup -> {
                try {
                    if (dataSourceGroup.getType().equals(DataSourceType.PREPARATION_SOURCE.name()) || dataSourceGroup.getType().equals(DataSourceType.STATISTICS_SOURCE.name())) {
                        startScheduler(dataSourceGroup.getDataSourceList(), scheduler, project.getId(), DataConstants.PROJECT, isOrigin);
                    }
                } catch (SchedulerException e) {
                    e.printStackTrace();
                }
            });
        });
    }


}
