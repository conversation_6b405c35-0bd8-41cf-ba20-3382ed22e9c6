<!-- 统一工单-工单中心-退单处理 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="DialogFormConfig"
    ></DialogForm>
    <SLDrawer
      ref="refdetail"
      :config="detailConfig"
    >
      <detail :id="selectedId"></detail>4
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { Refresh, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { useUserStore } from '@/store'
import { removeSlash } from '@/utils/removeIdSlash' // 处理id, idRemoveSlash
import { SLConfirm, SLMessage } from '@/utils/Message'
import { ChargeBackWorkOrder, GetWorkOrderPage, getWorkOrderEmergencyLevelList } from '@/api/workorder'
import detail from './components/detail.vue'
import { traverse } from '@/utils/GlobalHelper'

const refSearch = ref<ICardSearchIns>()
const refForm = ref<IDialogFormIns>()
const refdetail = ref<ISLDrawerIns>()

const state = reactive<{
  WorkOrderEmergencyLevelList: any[],
}>({
  WorkOrderEmergencyLevelList: []
})

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then(res => {
    state.WorkOrderEmergencyLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'id' })
  })
}

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '类别',
      field: 'status',
      labelWidth: 40,
      options: [
        { label: '待审核', value: 'CHARGEBACK_REVIEW' },
        { label: '已审核', value: 'CHARGEBACK' }
      ],
      onChange: val => {
        const perms = val === 'CHARGEBACK_REVIEW' ? ['详情', '审核'] : ['详情']
        const permWidth = val === 'CHARGEBACK_REVIEW' ? 100 : 80
        TableConfig.operationWidth = permWidth
        TableConfig.operations?.map(item => {
          const text:string = item.text as string
          item.perm = perms.indexOf(text) !== -1
        })
        refreshData()
      }
    },
    {
      type: 'input',
      label: '标题',
      field: 'title',
      onChange: () => refreshData()
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ],
  defaultParams: {
    status: 'CHARGEBACK_REVIEW'
  },
  handleSearch: () => refreshData()
})
const TableConfig = reactive<ITable>({
  columns: [
    { minWidth: 180, prop: 'serialNo', label: '工单编号' },
    { minWidth: 120,
      prop: 'level',
      label: '紧急程度',
      tag: true,
      tagColor: (row):string => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.color || '',
      formatter: row => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.label
    },
    { minWidth: 120, prop: 'type', label: '类型' },
    { minWidth: 120, prop: 'title', label: '标题' },
    { minWidth: 200, prop: 'address', label: '地址' },
    {
      minWidth: 160,
      prop: 'createTime',
      label: '工单发起时间',
      formatter: row => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
    },
    { minWidth: 120,
      prop: 'status',
      label: '状态',
      formatter: row => {
        switch (row.status) {
          case 'PENDING':
          case 'ASSIGN':
            return '待处理'
          case 'RESOLVING':
          case 'ARRIVING':
          case 'PROCESSING':
          case 'SUBMIT':
          case 'REVIEW':
          case 'CHARGEBACK_REVIEW':
          case 'HANDOVER_REVIEW':
          case 'REASSIGN':
          case 'COLLABORATION':
            return '处理中'
          case 'APPROVED':
          case 'CHARGEBACK':
          case 'TERMINATED':
            return '已结束'
          default:
            break
        }
      } }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size
      TableConfig.pagination.page = page
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      isTextBtn: true,
      text: '详情',
      click: row => {
        selectedId.value = row.id || ''
        detailConfig.title = row.serialNo
        refdetail.value?.openDrawer()
      }
    },
    {
      perm: true,
      text: '审核',
      isTextBtn: true,
      click: row => {
        TableConfig.currentRow = row
        refForm.value?.openDialog()
      }
    }
  ]
})

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
})

const selectedId = ref<string>('')

const DialogFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '退单审核',
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'radio',
          label: '审核结果：',
          field: 'stage',
          options: [
            { label: '通过', value: 'CHARGEBACK' },
            { label: '拒绝', value: 'REJECTED' }
          ],
          rules: [{ required: true, message: '请选择结果' }]
        },
        { type: 'textarea', label: '备注：', field: 'processRemark' }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(async () => {
      DialogFormConfig.submitting = true
      try {
        const res = await ChargeBackWorkOrder(TableConfig.currentRow?.id, {
          processAdditionalInfo: JSON.stringify({}),
          ...params
        })
        if (res.data?.code === 200) {
          SLMessage.success('操作成功')
          refForm.value?.closeDialog()
          refreshData()
        } else {
          SLMessage.error(res.data?.err || '操作失败')
        }
        // } else {
        //   SLMessage.error(res.data?.err || '接收失败')
        // }
      } catch (error) {
        SLMessage.error('系统错误')
      }
      DialogFormConfig.submitting = false
    }).catch(() => {
      //
    })
  }
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params:any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      stepProcessUserId: removeSlash(useUserStore().user?.id?.id || ''),
      ...query
    }
    const res = await GetWorkOrderPage(params)
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data?.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
onMounted(() => {
  initOptions()
  refreshData()
})
</script>
<style lang="scss" scoped></style>
