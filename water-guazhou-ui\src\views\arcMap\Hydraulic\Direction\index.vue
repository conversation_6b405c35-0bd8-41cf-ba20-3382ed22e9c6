<!-- 水流分析 -->

<template>
  <RightDrawerMap :title="'水流分析'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['流速分级(m/s)', '图层控制', '定位']"
      :legends="[
        { label: '0~0.1m/s', value: 1145, checked: true },
        { label: '0.1~0.25m/s', value: 2211, checked: true },
        { label: '0.25~0.5m/s', value: 1184, checked: true },
        { label: '0.5~0.75m/s', value: 41, checked: true },
        { label: '0.75~1m/s', value: 22, checked: true },
        { label: '1~1.5m/s', value: 1, checked: true },
        { label: '1.5~2m/s', value: 1, checked: true },
        { label: '2~5m/s', value: 1, checked: true },
        { label: '>5m/s', value: 0, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import SliderBar from '../components/SliderBar.vue'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
</script>
<style lang="scss" scoped></style>
