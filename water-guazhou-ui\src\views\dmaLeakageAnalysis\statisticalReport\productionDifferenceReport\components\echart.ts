import * as echarts from 'echarts'
import { useAppStore } from '@/store'

export function lineOption(dateX?: any, data1?: any, data2?: any) {
  const option = {
    // backgroundColor: useAppStore().isDark ? '#222536' : 'transparent',
    title: {
      text: ''
    },
    grid: {
      left: 50,
      right: 50,
      top: 50,
      bottom: 80
    },
    legend: {
      type: 'scroll',
      width: 500,
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: dateX
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 30
      },
      {
        start: 0,
        end: 10
      }
    ],
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '出口压力(Mpa)',
      axisLine: {
        show: true,
        lineStyle: {
          // color: '#ffffff' // '#333'
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          color: useAppStore().isDark ? '#303958' : '#ccc',
          type: [5, 10],
          dashOffset: 5
        }
      }
    }, {
      position: 'right',
      type: 'value',
      name: '瞬时流量(m³/h)',
      axisLine: {
        show: true,
        lineStyle: {
          // color: '#ffffff' // '#333'
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          color: useAppStore().isDark ? '#303958' : '#ccc',
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        name: '出口压力(Mpa)',
        smooth: true,
        data: data1,
        type: 'line'
      },
      {
        name: '瞬时流量(m³/h)',
        smooth: true,
        data: data2,
        type: 'line',
        yAxisIndex: 1
      }
    ]
  }
  return option
}

export const pieOption = (title: string, color: string, data: any) => {
  const option = {
    color: [
      new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color
        },
        {
          offset: 1,
          color: 'rgba(21, 45, 68,0.5)'
        }
      ]),
      'rgba(21, 45, 68,0.5)'
    ],
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'center',
          color: '#ffffff',
          formatter() {
            return (data || 0) + '\n' + 'm³'
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '12',
            fontWeight: 'bold'
          }
        },
        data: [
          { value: 10, name: '' },
          { value: 4, name: '' }
        ]
      }
    ]
  }
  return option
}

export const barOption = (title?: string, color?: string, dataX?: any[], data?: any[]) => {
  const option = {
    color: [color],
    xAxis: {
      type: 'category',
      data: dataX
    },
    grid: {
      left: 50,
      right: 0
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '供水量(m³)',
      axisLine: {
        show: true,
        lineStyle: {
          // color: '#ffffff' // '#333'
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          color: useAppStore().isDark ? '#303958' : '#ccc',
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        data,
        type: 'bar'
      }
    ]
  }
  return option
}
