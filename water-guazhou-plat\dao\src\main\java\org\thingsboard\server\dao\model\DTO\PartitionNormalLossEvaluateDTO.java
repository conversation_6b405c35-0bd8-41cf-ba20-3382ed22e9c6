package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 增量漏失评估
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Data
@NoArgsConstructor
public class PartitionNormalLossEvaluateDTO {

    private String partitionId;

    private String partitionName;

    private String status;

    private String statusName;

    private Integer userNum;

    private Integer inlet;

    private BigDecimal supplyTotal;

    private Date minTime;

    private Integer minFlowTimeHour;

    private Integer minValueTimeHour;

    private BigDecimal minValue;

    private BigDecimal minFlow;

    private Date incrTime;

    private BigDecimal incrBase;

    private BigDecimal incrWarn;

    private BigDecimal incrError;

    private BigDecimal mainLineLength;

}
