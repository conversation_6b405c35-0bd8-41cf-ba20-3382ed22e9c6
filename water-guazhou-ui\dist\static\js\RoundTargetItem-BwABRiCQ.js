import a from"./TargetItem-MDYkIAGN.js";import{d as o,g as t,n,p as s,aw as c,bh as r,q as i,C as l}from"./index-r0dFAfgr.js";const p={class:"item"},d={class:"ball"},m=o({__name:"RoundTargetItem",props:{config:{}},setup(_){return(e,f)=>(t(),n("div",p,[s("div",d,[s("span",{class:c(e.config.status)},r(e.config.value),3)]),i(a,{config:e.config},null,8,["config"])]))}}),b=l(m,[["__scopeId","data-v-c2bedbec"]]);export{b as default};
