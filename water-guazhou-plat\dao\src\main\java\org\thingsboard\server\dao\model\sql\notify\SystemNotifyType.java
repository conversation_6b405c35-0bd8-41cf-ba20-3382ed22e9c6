package org.thingsboard.server.dao.model.sql.notify;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("tb_system_notify_type")
@NoArgsConstructor
@AllArgsConstructor
public class SystemNotifyType {

    private String id;

    private String code;

    private String model;

    private String type;

    private String menuName;

    private String path;

    private String sendType;

    private String status;

    private String creator;

    private Date createTime;

    private String tenantId;

    private String route;

}
