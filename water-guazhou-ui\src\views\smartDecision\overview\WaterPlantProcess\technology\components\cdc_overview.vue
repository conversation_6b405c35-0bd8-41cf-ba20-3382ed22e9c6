<template>
  <div class="main">
    <div class="card zutai-card">
      <!-- <FramePage
        :url="''"
        :token-field="'token'"
        :disable-full-screen="true"
        ></FramePage> -->
      <div class="card-content" style="top: 16%; right: 26%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">2#沉淀池</span>
        </div>
        <!-- <div class="row">
          <div class="label">2号池液位:</div>
          <div class="value">{{ realTimeValue['网格反应池液位2']?.level }}</div>
        </div> -->
      </div>

      <div class="card-content" style="top: 16%; left: 26%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">1#沉淀池</span>
        </div>
        <!-- <div class="row">
          <div class="label">1号池液位:</div>
          <div class="value">{{ realTimeValue['网格反应池液位1']?.level }}</div>
        </div> -->
      </div>
      <!-- 反洗阀/排泥阀 -->
      <div v-for="(item, index) in 7" :key="index">
        <div
          class="card-content status-box"
          :style="{ top: 25 + index * 10 + '%', right: '50%' }"
        >
          <div style="color: #fff; padding-right: 5px">{{ index + 8 }}#</div>
          <div
            :class="[
              'status',
              realTimeValue['反洗/排泥阀']['pnf_' + (index + 8)] == '开'
                ? 'online'
                : 'unline'
            ]"
          ></div>
        </div>
        <div
          class="card-content status-box"
          :style="{ top: 29 + index * 10 + '%', right: '52%' }"
        >
          <div style="color: #fff; padding-right: 5px">{{ index + 8 }}#</div>
          <div
            :class="[
              'status',
              realTimeValue['反洗/排泥阀']['fxf_' + (index + 8)] == '开'
                ? 'online'
                : 'unline'
            ]"
          ></div>
        </div>
      </div>

      <div v-for="(item, index) in 7" :key="index">
        <div
          class="card-content status-box"
          :style="{ top: 25 + index * 10 + '%', right: '46%' }"
        >
          <div style="color: #fff; padding-right: 5px">{{ index + 1 }}#</div>
          <div
            :class="[
              'status',
              realTimeValue['反洗/排泥阀']['pnf_' + (index + 1)] == '开'
                ? 'online'
                : 'unline'
            ]"
          ></div>
        </div>
        <div
          class="card-content status-box"
          :style="{ top: 29 + index * 10 + '%', right: '44%' }"
        >
          <div style="color: #fff; padding-right: 5px">{{ index + 1 }}#</div>
          <div
            :class="[
              'status',
              realTimeValue['反洗/排泥阀']['fxf_' + (index + 1)] == '开'
                ? 'online'
                : 'unline'
            ]"
          ></div>
        </div>
      </div>
      <!-- 出水阀 -->
      <div class="card-content" style="bottom: 1%; right: 26%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">2#网格池</span>
        </div>
        <div class="row">
          <div class="label">2号出水阀:</div>
          <div
            :class="[
              'status',
              'online'
            ]"
          ></div>
        </div>
      </div>

      <div class="card-content" style="bottom: 1%; left: 26%; width: 140px">
        <div class="card-title">
          <span style="color: #d8feff; text-align: center">1#网格池</span>
        </div>
        <div class="row">
          <div class="label">1号出水阀:</div>
          <div
            :class="[
              'status',
              'online'
            ]"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getDatasList } from '@/api/device';
import { removeSlash } from '@/utils/removeIdSlash';

import { useBusinessStore } from '@/store';
const businessStore = useBusinessStore();
const Interval = ref<any>();

const realTimeValue = ref<any>({
  '反洗/排泥阀': {}
});
const refreshData = (devices: any) => {
  for (const i in devices) {
    getDatasList(removeSlash(devices[i].id.id), {
      page: 1,
      size: 9999999
    }).then((res) => {
      const data = res.data.data;
      realTimeValue.value[devices[i].name] = {};
      for (const t in data) {
        realTimeValue.value[devices[i].name][data[t].property] =
          `${data[t].value}${data[t].unit || ''}`;
      }
    });
    // realTimeValue.value[devices[i].name] = res.data.data?.map(item => {
    //   return {
    //     [item.property]: `${item.value}${item.unit}`,
    //   }
    // })
  }
  console.log(realTimeValue.value);
};

const getValue = (property, deviceId) => {
  return realTimeValue.value.find(
    (item) => item.property === property && item.deviceId === deviceId
  );
};
const initData = () => {
  console.log(businessStore.projectList);
  const projectId = businessStore.projectList[0].id as string;
  
};

onMounted(() => {
  initData();
  Interval.value = setInterval(() => {
    initData();
  }, 30000);
});

onBeforeUnmount(() => {
  clearInterval(Interval.value);
});
</script>
<style lang="scss" scoped>

.main {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgb(3, 17, 45);
}

.content {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 20px;
  padding-top: 81px;
  color: rgba(184, 210, 255, 1);
}

.card {
  position: absolute;
}

.zutai-card {
  width: 100%;
  height: 100%;
  border: 1px solid rgba(0, 191, 252, 0.4);
  background: url(../../imgs/沉淀池.png) 0 0 /100% 100% no-repeat;
}

.cqjj-card {
  width: 745px;
  height: 315px;
  top: 746px;
  left: 26px;
}

.szjc-card {
  width: 994px;
  height: 315px;
  top: 746px;
  left: 783px;
}

.ssqx-card {
  width: 738px;
  height: 315px;
  top: 105px;
  left: 1790px;
}

.nhjc-card {
  width: 738px;
  height: 315px;
  top: 436px;
  left: 1790px;
}

.rgsl-card {
  width: 738px;
  height: 294px;
  top: 767px;
  left: 1790px;
}

.card-content {
  position: absolute;
  width: 190px;
  border-radius: 4px;
  padding: 0 5px 10px 5px;
  box-shadow: 0px 0px 8px 0px #00d9ff inset;
  background: linear-gradient(
    rgba(30, 105, 158, 0.1) 0%,
    rgba(30, 105, 158, 0.6) 100%
  );

  .card-title {
    // transform: translateX(-50%);
    font-size: 12px;
    line-height: 18px;
    width: 93px;
    height: 18px;
    margin: 0 auto;
    text-align: center;
    border-radius: 0 0 10px 10px;
    background: linear-gradient(
      180deg,
      rgba(116, 229, 255, 0.2) 0%,
      rgba(16, 229, 255, 0.6) 100%
    );
  }

  .row {
    display: flex;
    font-size: 12px;
    color: #d8feff;
    padding: 10px 10px 0px 10px;
    display: flex;
    justify-content: space-around;
    .value {
      padding-left: 4px;
    }
  }
}
.status {
  padding-left: 4px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
.online {
  background-color: rgb(42, 241, 15);
}

.unline {
  background-color: red;
}
.status-box {
  width: 52px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 3px;
}
// .card-content::before {
//   content: '';
//   transform: translateX(-50%);
//   width: 100px;
//   height: 10px;
//   background-color: rgba(173, 216, 230, 0.8); /* 顶部的浅蓝色区域 */
//   border-radius: 0 0 10px 10px;
// }
</style>
