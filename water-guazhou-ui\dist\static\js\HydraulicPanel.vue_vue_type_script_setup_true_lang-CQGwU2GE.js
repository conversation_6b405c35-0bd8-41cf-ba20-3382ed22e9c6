import{u as S}from"./useDetector-BRcb7GRN.js";import{d as g,j as A,c as h,a8 as C,am as k,ab as w,o as F,ay as D,g as v,n as x,q as a,i as _,av as z,bt as B,bs as N}from"./index-r0dFAfgr.js";import P from"./Legends-B4O5Nxnm.js";const V=g({__name:"SimplePie",props:{width:{},height:{},data:{},unit:{},prefix:{},percision:{}},setup(c){const t=A(),p=h(),d=h(),e=c,r=h(),u=C(()=>({width:e.width?e.width+"px":"100%",height:e.height?e.height+"px":"100%"})),i=S();k(()=>e.data,()=>{f()},{deep:!0});const y=function(s){const n=/(?=(\B)(\d{3})+$)/g;return s.toString().replace(n,",")},f=()=>{const s="合计",n=e.data||[],m=n.reduce((o,b)=>o+(parseFloat(b.value)||0)*1,0),l=w(m);r.value={tooltip:{trigger:"item",formatter:o=>(e.prefix||"")+o.name+": "+Number(o.value).toFixed(e.percision??0)+" "+e.unit},legend:{icon:"circle",orient:"horizontal",bottom:10,left:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#B8D2FF"},data:n.map(o=>o.name)},title:[{text:"{name|"+s+(e.unit?"("+l.unit+e.unit+")":l.unit?"("+l.unit+")":"")+`}
{val|`+y(l.value.toFixed(e.percision??0))+"}",top:"33%",left:"50%",textAlign:"center",textStyle:{rich:{name:{fontSize:10,fontWeight:"normal",padding:[8,0],align:"center",color:t.isDark?"#fff":"#2A2A2A"},val:{fontSize:16,fontWeight:"bold",color:t.isDark?"#fff":"#2A2A2A"}}}}],series:[{type:"pie",radius:["35%","50%"],center:["50%","40%"],data:n,hoverAnimation:!0,label:{}}]}};return F(()=>{f(),i.listenTo(p.value,d)}),(s,n)=>{const m=D("VChart");return v(),x("div",{ref_key:"refContainer",ref:p,style:z(_(u))},[a(m,{ref_key:"refChart",ref:d,option:_(r)},null,8,["option"])],4)}}}),W={class:"hydraulicpanel"},$=g({__name:"HydraulicPanel",props:{legends:{},header:{},unit:{}},setup(c){const t=c;return(p,d)=>{const e=B,r=N,u=V;return v(),x("div",W,[a(e,{icon:"mdi:view-dashboard",title:"图例",type:"simple"}),a(P,{data:t.legends,header:t.header},null,8,["data","header"]),a(r),a(u,{unit:t.unit,height:300,data:t.legends.map(i=>({name:i.label,value:i.value}))},null,8,["unit","data"])])}}});export{$ as _};
