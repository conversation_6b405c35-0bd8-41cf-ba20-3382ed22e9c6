package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeMinFlowConfig;

import java.util.List;

/**
 * 小流量指标设置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-10
 */
@Mapper
public interface PipeMinFlowConfigMapper extends BaseMapper<PipeMinFlowConfig> {

    List<PipeMinFlowConfig> getListByPartitionIdIn(@Param("partitionIdList") List<String> partitionIdList);
}
