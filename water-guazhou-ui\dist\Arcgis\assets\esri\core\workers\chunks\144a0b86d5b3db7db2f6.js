"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4286],{3920:(e,t,i)=>{i.d(t,{p:()=>p,r:()=>u});var r=i(43697),o=i(15923),s=i(61247),a=i(5600),n=i(52011),l=i(72762);const p=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new s.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,r._)([(0,a.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,r._)([(0,a.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,r._)([(0,n.j)("esri.core.HandleOwner")],t),t};let u=class extends(p(o.Z)){};u=(0,r._)([(0,n.j)("esri.core.HandleOwner")],u)},65845:(e,t,i)=>{i.d(t,{D:()=>o});var r=i(81153);function o(e){e&&e.writtenProperties&&e.writtenProperties.forEach((({target:e,propName:t,newOrigin:i})=>{(0,r.l)(e)&&i&&e.originOf(t)!==i&&e.updateOrigin(t,i)}))}},81153:(e,t,i)=>{function r(e){return e&&"getAtOrigin"in e&&"originOf"in e}i.d(t,{l:()=>r})},72762:(e,t,i)=>{i.d(t,{t:()=>d});var r=i(43697),o=i(15923),s=i(61247),a=i(70586),n=i(17445),l=i(1654),p=i(5600),u=i(52011);let d=class extends o.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new s.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,i={}){return this._installWatch(e,t,i,n.YP)}addWhen(e,t,i={}){return this._installWatch(e,t,i,n.gx)}addOnCollectionChange(e,t,{initial:i=!1,final:r=!1}={}){const o=++this._handleId;return this._handles.add([(0,n.on)(e,"after-changes",this._createSyncUpdatingCallback(),n.Z_),(0,n.on)(e,"change",t,{onListenerAdd:i?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:r?e=>t({added:[],removed:e.toArray()}):void 0})],o),{remove:()=>this._handles.remove(o)}}addPromise(e){if((0,a.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(c)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const i=()=>this._handles.remove(t);return e.then(i,i),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,i={},r){const o=++this._handleId;i.sync||this._installSyncUpdatingWatch(e,o);const s=r(e,t,i);return this._handles.add(s,o),{remove:()=>this._handles.remove(o)}}_installSyncUpdatingWatch(e,t){const i=this._createSyncUpdatingCallback(),r=(0,n.YP)(e,i,{sync:!0,equals:()=>!1});return this._handles.add(r,t),r}_createSyncUpdatingCallback(){return()=>{this._handles.remove(c),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(c))})),c)}}};(0,r._)([(0,p.Cb)({readOnly:!0})],d.prototype,"updating",void 0),d=(0,r._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],d);const c=-42},16306:(e,t,i)=>{i.d(t,{aX:()=>S});var r=i(68773),o=i(20102),s=i(92604),a=i(70586),n=i(38913),l=i(58901),p=i(73913),u=i(8744),d=i(40488),c=(i(66577),i(3172)),m=i(33955),y=i(11282),b=i(17452);async function h(e,t,i){const r="string"==typeof e?(0,b.mN)(e):e,o=t[0].spatialReference,s=(0,m.Ji)(t[0]),a={...i,query:{...r.query,f:"json",sr:o.wkid?o.wkid:JSON.stringify(o),geometries:JSON.stringify((l=t,{geometryType:(0,m.Ji)(l[0]),geometries:l.map((e=>e.toJSON()))}))}},{data:n}=await(0,c.default)(r.path+"/simplify",a);var l;return function(e,t,i){const r=(0,m.q9)(t);return e.map((e=>{const t=r.fromJSON(e);return t.spatialReference=i,t}))}(n.geometries,s,o)}const f=s.Z.getLogger("esri.geometry.support.normalizeUtils");function v(e){return"polygon"===e[0].type}function g(e){return"polyline"===e[0].type}function T(e,t,i){if(t){const t=function(e,t){if(!(e instanceof l.Z||e instanceof n.Z)){const e="straightLineDensify: the input geometry is neither polyline nor polygon";throw f.error(e),new o.Z(e)}const i=(0,p.x3)(e),r=[];for(const e of i){const i=[];r.push(i),i.push([e[0][0],e[0][1]]);for(let r=0;r<e.length-1;r++){const o=e[r][0],s=e[r][1],a=e[r+1][0],n=e[r+1][1],l=Math.sqrt((a-o)*(a-o)+(n-s)*(n-s)),p=(n-s)/l,u=(a-o)/l,d=l/t;if(d>1){for(let e=1;e<=d-1;e++){const r=e*t,a=u*r+o,n=p*r+s;i.push([a,n])}const e=(l+Math.floor(d-1)*t)/2,r=u*e+o,a=p*e+s;i.push([r,a])}i.push([a,n])}}return function(e){return"polygon"===e.type}(e)?new n.Z({rings:r,spatialReference:e.spatialReference}):new l.Z({paths:r,spatialReference:e.spatialReference})}(e,1e6);e=(0,d.Sx)(t,!0)}return i&&(e=(0,p.Sy)(e,i)),e}function C(e,t,i){if(Array.isArray(e)){const r=e[0];if(r>t){const i=(0,p.XZ)(r,t);e[0]=r+i*(-2*t)}else if(r<i){const t=(0,p.XZ)(r,i);e[0]=r+t*(-2*i)}}else{const r=e.x;if(r>t){const i=(0,p.XZ)(r,t);e=e.clone().offset(i*(-2*t),0)}else if(r<i){const t=(0,p.XZ)(r,i);e=e.clone().offset(t*(-2*i),0)}}return e}function w(e,t){let i=-1;for(let r=0;r<t.cutIndexes.length;r++){const o=t.cutIndexes[r],s=t.geometries[r],a=(0,p.x3)(s);for(let e=0;e<a.length;e++){const t=a[e];t.some((i=>{if(i[0]<180)return!0;{let i=0;for(let e=0;e<t.length;e++){const r=t[e][0];i=r>i?r:i}i=Number(i.toFixed(9));const r=-360*(0,p.XZ)(i,180);for(let i=0;i<t.length;i++){const t=s.getPoint(e,i);s.setPoint(e,i,t.clone().offset(r,0))}return!0}}))}if(o===i){if(v(e))for(const t of(0,p.x3)(s))e[o]=e[o].addRing(t);else if(g(e))for(const t of(0,p.x3)(s))e[o]=e[o].addPath(t)}else i=o,e[o]=s}return e}async function S(e,t,i){if(!Array.isArray(e))return S([e],t);t&&"string"!=typeof t&&f.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const o="string"==typeof t?t:t?.url??r.Z.geometryServiceUrl;let s,b,v,g,_,N,O,A,D=0;const I=[],x=[];for(const t of e)if((0,a.Wi)(t))x.push(t);else if(s||(s=t.spatialReference,b=(0,u.C5)(s),v=s.isWebMercator,N=v?102100:4326,g=p.UZ[N].maxX,_=p.UZ[N].minX,O=p.UZ[N].plus180Line,A=p.UZ[N].minus180Line),b)if("mesh"===t.type)x.push(t);else if("point"===t.type)x.push(C(t.clone(),g,_));else if("multipoint"===t.type){const e=t.clone();e.points=e.points.map((e=>C(e,g,_))),x.push(e)}else if("extent"===t.type){const e=t.clone()._normalize(!1,!1,b);x.push(e.rings?new n.Z(e):e)}else if(t.extent){const e=t.extent,i=(0,p.XZ)(e.xmin,_)*(2*g);let r=0===i?t.clone():(0,p.Sy)(t.clone(),i);e.offset(i,0),e.intersects(O)&&e.xmax!==g?(D=e.xmax>D?e.xmax:D,r=T(r,v),I.push(r),x.push("cut")):e.intersects(A)&&e.xmin!==_?(D=e.xmax*(2*g)>D?e.xmax*(2*g):D,r=T(r,v,360),I.push(r),x.push("cut")):x.push(r)}else x.push(t.clone());else x.push(t);let j=(0,p.XZ)(D,g),F=-90;const B=j,M=new l.Z;for(;j>0;){const e=360*j-180;M.addPath([[e,F],[e,-1*F]]),F*=-1,j--}if(I.length>0&&B>0){const t=w(I,await async function(e,t,i,r){const o=(0,y.en)(e),s=t[0].spatialReference,a={...r,query:{...o.query,f:"json",sr:JSON.stringify(s),target:JSON.stringify({geometryType:(0,m.Ji)(t[0]),geometries:t}),cutter:JSON.stringify(i)}},n=await(0,c.default)(o.path+"/cut",a),{cutIndexes:l,geometries:p=[]}=n.data;return{cutIndexes:l,geometries:p.map((e=>{const t=(0,m.im)(e);return t.spatialReference=s,t}))}}(o,I,M,i)),r=[],s=[];for(let i=0;i<x.length;i++){const o=x[i];if("cut"!==o)s.push(o);else{const o=t.shift(),n=e[i];(0,a.pC)(n)&&"polygon"===n.type&&n.rings&&n.rings.length>1&&o.rings.length>=n.rings.length?(r.push(o),s.push("simplify")):s.push(v?(0,d.$)(o):o)}}if(!r.length)return s;const n=await h(o,r,i),l=[];for(let e=0;e<s.length;e++){const t=s[e];"simplify"!==t?l.push(t):l.push(v?(0,d.$)(n.shift()):n.shift())}return l}const E=[];for(let e=0;e<x.length;e++){const t=x[e];if("cut"!==t)E.push(t);else{const e=I.shift();E.push(!0===v?(0,d.$)(e):e)}}return E}},73913:(e,t,i)=>{i.d(t,{Sy:()=>l,UZ:()=>a,XZ:()=>n,x3:()=>p});var r=i(58901),o=i(82971),s=i(33955);const a={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new r.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:o.Z.WebMercator}),minus180Line:new r.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:o.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new r.Z({paths:[[[180,-180],[180,180]]],spatialReference:o.Z.WGS84}),minus180Line:new r.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:o.Z.WGS84})}};function n(e,t){return Math.ceil((e-t)/(2*t))}function l(e,t){const i=p(e);for(const e of i)for(const i of e)i[0]+=t;return e}function p(e){return(0,s.oU)(e)?e.rings:e.paths}},40153:(e,t,i)=>{i.r(t),i.d(t,{default:()=>pi});var r=i(43697),o=i(68773),s=i(66577),a=i(38171),n=i(51773),l=(i(16050),i(12501),i(28756),i(92271),i(72529),i(5499),i(84382),i(81571),i(91423)),p=i(46791),u=i(20102),d=i(3920),c=i(92604),m=i(70586),y=i(16453),b=i(78286),h=i(95330),f=i(17445),v=i(67900),g=i(17452),T=i(5600),C=i(75215),w=(i(67676),i(71715)),S=i(52011),_=i(30556),N=i(65845),O=i(6570),A=i(44547),D=i(8744),I=i(87085),x=i(71612),j=i(38009),F=i(16859),B=i(72965),M=i(66677),E=i(9790),L=i(96674),R=i(77987);let P=class extends L.wq{constructor(e){super(e),this.break=new R.Z({color:[255,255,255],size:12,outline:{color:[0,122,194],width:3}}),this.first=new R.Z({color:[0,255,0],size:20,outline:{color:[255,255,255],width:4}}),this.unlocated=new R.Z({color:[255,0,0],size:12,outline:{color:[255,255,255],width:3}}),this.last=new R.Z({color:[255,0,0],size:20,outline:{color:[255,255,255],width:4}}),this.middle=new R.Z({color:[51,51,51],size:12,outline:{color:[0,122,194],width:3}}),this.waypoint=new R.Z({color:[255,255,255],size:12,outline:{color:[0,122,194],width:3}})}};(0,r._)([(0,T.Cb)({types:E.LB})],P.prototype,"break",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],P.prototype,"first",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],P.prototype,"unlocated",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],P.prototype,"last",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],P.prototype,"middle",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],P.prototype,"waypoint",void 0),P=(0,r._)([(0,S.j)("esri.layers.support.RouteStopSymbols")],P);const J=P;var U=i(4095),W=i(20256);let k=class extends L.wq{constructor(e){super(e),this.directionLines=new U.Z({color:[0,122,194],width:6}),this.directionPoints=new R.Z({color:[255,255,255],size:6,outline:{color:[0,122,194],width:2}}),this.pointBarriers=new R.Z({style:"x",size:10,outline:{color:[255,0,0],width:3}}),this.polygonBarriers=new W.Z({color:[255,170,0,.6],outline:{width:7.5,color:[255,0,0,.6]}}),this.polylineBarriers=new U.Z({width:7.5,color:[255,85,0,.7]}),this.routeInfo=new U.Z({width:8,color:[20,89,127]}),this.stops=new J}};(0,r._)([(0,T.Cb)({types:E.LB})],k.prototype,"directionLines",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],k.prototype,"directionPoints",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],k.prototype,"pointBarriers",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],k.prototype,"polygonBarriers",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],k.prototype,"polylineBarriers",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],k.prototype,"routeInfo",void 0),(0,r._)([(0,T.Cb)({type:J})],k.prototype,"stops",void 0),k=(0,r._)([(0,S.j)("esri.layers.support.RouteSymbols")],k);const Z=k;var V=i(65587),q=i(15235),G=i(14661),z=i(3172),H=i(11282),K=i(36030),X=i(35454);const Y=(0,X.w)()({esriCentimeters:"centimeters",esriDecimalDegrees:"decimal-degrees",esriDecimeters:"decimeters",esriFeet:"feet",esriInches:"inches",esriKilometers:"kilometers",esriMeters:"meters",esriMiles:"miles",esriMillimeters:"millimeters",esriNauticalMiles:"nautical-miles",esriPoints:"points",esriUnknownUnits:"unknown",esriYards:"yards"}),$=(0,X.w)()({esriNAUCentimeters:"centimeters",esriNAUDecimalDegrees:"decimal-degrees",esriNAUDecimeters:"decimeters",esriNAUFeet:"feet",esriNAUInches:"inches",esriNAUKilometers:"kilometers",esriNAUMeters:"meters",esriNAUMiles:"miles",esriNAUMillimeters:"millimeters",esriNAUNauticalMiles:"nautical-miles",esriNAUPoints:"points",esriNAUYards:"yards"}),Q=((0,X.w)()({esriNAUDays:"days",esriNAUHours:"hours",esriNAUMinutes:"minutes",esriNAUSeconds:"seconds"}),(0,X.w)()({esriNAUCentimeters:"centimeters",esriNAUDecimalDegrees:"decimal-degrees",esriNAUDecimeters:"decimeters",esriNAUFeet:"feet",esriNAUInches:"inches",esriNAUKilometers:"kilometers",esriNAUMeters:"meters",esriNAUMiles:"miles",esriNAUMillimeters:"millimeters",esriNAUNauticalMiles:"nautical-miles",esriNAUPoints:"points",esriNAUYards:"yards",esriNAUDays:"days",esriNAUHours:"hours",esriNAUMinutes:"minutes",esriNAUSeconds:"seconds",esriNAUKilometersPerHour:"kilometers-per-hour",esriNAUMilesPerHour:"miles-per-hour",esriNAUUnknown:"unknown"})),ee=(0,X.w)()({esriDOTComplete:"complete",esriDOTCompleteNoEvents:"complete-no-events",esriDOTFeatureSets:"featuresets",esriDOTInstructionsOnly:"instructions-only",esriDOTStandard:"standard",esriDOTSummaryOnly:"summary-only"}),te=(0,X.w)()({esriNAOutputLineNone:"none",esriNAOutputLineStraight:"straight",esriNAOutputLineTrueShape:"true-shape",esriNAOutputLineTrueShapeWithMeasure:"true-shape-with-measure"}),ie=((0,X.w)()({esriNAOutputPolygonNone:"none",esriNAOutputPolygonSimplified:"simplified",esriNAOutputPolygonDetailed:"detailed"}),(0,X.w)()({esriNFSBAllowBacktrack:"allow-backtrack",esriNFSBAtDeadEndsOnly:"at-dead-ends-only",esriNFSBNoBacktrack:"no-backtrack",esriNFSBAtDeadEndsAndIntersections:"at-dead-ends-and-intersections"})),re=((0,X.w)()({esriNATravelDirectionFromFacility:"from-facility",esriNATravelDirectionToFacility:"to-facility"}),(0,X.w)()({esriNATimeOfDayNotUsed:"not-used",esriNATimeOfDayUseAsStartTime:"start",esriNATimeOfDayUseAsEndTime:"end"}),(0,X.w)()({AUTOMOBILE:"automobile",TRUCK:"truck",WALK:"walk",OTHER:"other"})),oe=(0,X.w)()({0:"either-side-of-vehicle",1:"right-side-of-vehicle",2:"left-side-of-vehicle",3:"no-u-turn"},{useNumericKeys:!0}),se=(0,X.w)()({0:"stop",1:"waypoint",2:"break"},{useNumericKeys:!0}),ae=(0,X.w)()({0:"ok",1:"not-located",2:"network-element-not-located",3:"element-not-traversable",4:"invalid-field-values",5:"not-reached",6:"time-window-violation",7:"not-located-on-closest"},{useNumericKeys:!0}),ne=(0,X.w)()({1:"right",2:"left"},{useNumericKeys:!0}),le=(0,X.w)()({0:"restriction",1:"added-cost"},{useNumericKeys:!0}),pe=(0,X.w)()({0:"permit",1:"restrict"},{useNumericKeys:!0}),ue=(0,X.w)()({1:"header",50:"arrive",51:"depart",52:"straight",100:"on-ferry",101:"off-ferry",102:"central-fork",103:"roundabout",104:"u-turn",150:"door",151:"stairs",152:"elevator",153:"escalator",154:"pedestrian-ramp",200:"left-fork",201:"left-ramp",202:"clockwise-roundabout",203:"left-handed-u-turn",204:"bear-left",205:"left-turn",206:"sharp-left",207:"left-turn-and-immediate-left-turn",208:"left-turn-and-immediate-right-turn",300:"right-fork",301:"right-ramp",302:"counter-clockwise-roundabout",303:"right-handed-u-turn",304:"bear-right",305:"right-turn",306:"sharp-right",307:"right-turn-and-immediate-left-turn",308:"right-turn-and-immediate-right-turn",400:"up-elevator",401:"up-escalator",402:"up-stairs",500:"down-elevator",501:"down-escalator",502:"down-stairs",1e3:"general-event",1001:"landmark",1002:"time-zone-change",1003:"traffic-event",1004:"scaled-cost-barrier-event",1005:"boundary-crossing",1006:"restriction-violation"},{useNumericKeys:!0}),de=(0,X.w)()({0:"unknown",1:"segment",2:"maneuver-segment",3:"restriction-violation",4:"scaled-cost-barrier",5:"heavy-traffic",6:"slow-traffic",7:"moderate-traffic"},{useNumericKeys:!0}),ce=(0,X.w)()({"NA Campus":"campus","NA Desktop":"desktop","NA Navigation":"navigation"}),me=(0,X.w)()({Kilometers:"kilometers",Miles:"miles",Meters:"meters"},{ignoreUnknown:!1}),ye=(0,X.w)()({Minutes:"minutes",TimeAt1KPH:"time-at-1-kph",TravelTime:"travel-time",TruckMinutes:"truck-minutes",TruckTravelTime:"truck-travel-time",WalkTime:"walk-time"},{ignoreUnknown:!1}),be=(0,X.w)()({Kilometers:"kilometers",Miles:"miles",Meters:"meters",Minutes:"minutes",TimeAt1KPH:"time-at-1-kph",TravelTime:"travel-time",TruckMinutes:"truck-minutes",TruckTravelTime:"truck-travel-time",WalkTime:"walk-time"},{ignoreUnknown:!1}),he=(0,X.w)()({"Any Hazmat Prohibited":"any-hazmat-prohibited","Avoid Carpool Roads":"avoid-carpool-roads","Avoid Express Lanes":"avoid-express-lanes","Avoid Ferries":"avoid-ferries","Avoid Gates":"avoid-gates","Avoid Limited Access Roads":"avoid-limited-access-roads","Avoid Private Roads":"avoid-private-roads","Avoid Roads Unsuitable for Pedestrians":"avoid-roads-unsuitable-for-pedestrians","Avoid Stairways":"avoid-stairways","Avoid Toll Roads":"avoid-toll-roads","Avoid Toll Roads for Trucks":"avoid-toll-roads-for-trucks","Avoid Truck Restricted Roads":"avoid-truck-restricted-roads","Avoid Unpaved Roads":"avoid-unpaved-roads","Axle Count Restriction":"axle-count-restriction","Driving a Bus":"driving-a-bus","Driving a Taxi":"driving-a-taxi","Driving a Truck":"driving-a-truck","Driving an Automobile":"driving-an-automobile","Driving an Emergency Vehicle":"driving-an-emergency-vehicle","Height Restriction":"height-restriction","Kingpin to Rear Axle Length Restriction":"kingpin-to-rear-axle-length-restriction","Length Restriction":"length-restriction","Preferred for Pedestrians":"preferred-for-pedestrians","Riding a Motorcycle":"riding-a-motorcycle","Roads Under Construction Prohibited":"roads-under-construction-prohibited","Semi or Tractor with One or More Trailers Prohibited":"semi-or-tractor-with-one-or-more-trailers-prohibited","Single Axle Vehicles Prohibited":"single-axle-vehicles-prohibited","Tandem Axle Vehicles Prohibited":"tandem-axle-vehicles-prohibited","Through Traffic Prohibited":"through-traffic-prohibited","Truck with Trailers Restriction":"truck-with-trailers-restriction","Use Preferred Hazmat Routes":"use-preferred-hazmat-routes","Use Preferred Truck Routes":"use-preferred-truck-routes",Walking:"walking","Weight Restriction":"weight-restriction"},{ignoreUnknown:!1}),fe=(0,X.w)()({esriSpatialRelIntersects:"intersects",esriSpatialRelContains:"contains",esriSpatialRelCrosses:"crosses",esriSpatialRelEnvelopeIntersects:"envelope-intersects",esriSpatialRelIndexIntersects:"index-intersects",esriSpatialRelOverlaps:"overlaps",esriSpatialRelTouches:"touches",esriSpatialRelWithin:"within",esriSpatialRelRelation:"relation"}),ve=(0,X.w)()({esriGeometryPoint:"point",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"envelope",esriGeometryMultipoint:"multipoint"}),ge=(0,X.w)()({esriNAUTCost:"cost",esriNAUTDescriptor:"descriptor",esriNAUTRestriction:"restriction",esriNAUTHierarchy:"hierarchy"}),Te=(0,X.w)()({esriDSTAltName:"alt-name",esriDSTArrive:"arrive",esriDSTBranch:"branch",esriDSTCrossStreet:"cross-street",esriDSTCumulativeLength:"cumulative-length",esriDSTDepart:"depart",esriDSTEstimatedArrivalTime:"estimated-arrival-time",esriDSTExit:"exit",esriDSTGeneral:"general",esriDSTLength:"length",esriDSTServiceTime:"service-time",esriDSTStreetName:"street-name",esriDSTSummary:"summary",esriDSTTime:"time",esriDSTTimeWindow:"time-window",esriDSTToward:"toward",esriDSTViolationTime:"violation-time",esriDSTWaitTime:"wait-time"});let Ce=class extends L.wq{constructor(e){super(e),this.dataType=null,this.name=null,this.parameterNames=null,this.restrictionUsageParameterName=null,this.timeNeutralAttributeName=null,this.trafficSupport=null,this.units=null,this.usageType=null}};(0,r._)([(0,T.Cb)({type:String})],Ce.prototype,"dataType",void 0),(0,r._)([(0,K.J)(be,{ignoreUnknown:!1})],Ce.prototype,"name",void 0),(0,r._)([(0,T.Cb)({type:[String]})],Ce.prototype,"parameterNames",void 0),(0,r._)([(0,T.Cb)({type:String})],Ce.prototype,"restrictionUsageParameterName",void 0),(0,r._)([(0,K.J)(ye,{ignoreUnknown:!1})],Ce.prototype,"timeNeutralAttributeName",void 0),(0,r._)([(0,T.Cb)({type:String})],Ce.prototype,"trafficSupport",void 0),(0,r._)([(0,K.J)(Q)],Ce.prototype,"units",void 0),(0,r._)([(0,K.J)(ge)],Ce.prototype,"usageType",void 0),Ce=(0,r._)([(0,S.j)("esri.rest.support.NetworkAttribute")],Ce);const we=Ce;let Se=class extends L.wq{constructor(e){super(e),this.buildTime=null,this.name=null,this.networkAttributes=null,this.networkSources=null,this.state=null}};(0,r._)([(0,T.Cb)({type:Number})],Se.prototype,"buildTime",void 0),(0,r._)([(0,T.Cb)({type:String})],Se.prototype,"name",void 0),(0,r._)([(0,T.Cb)({type:[we]})],Se.prototype,"networkAttributes",void 0),(0,r._)([(0,T.Cb)()],Se.prototype,"networkSources",void 0),(0,r._)([(0,T.Cb)({type:String})],Se.prototype,"state",void 0),Se=(0,r._)([(0,S.j)("esri.rest.support.NetworkDataset")],Se);const _e=Se;var Ne=i(2368);let Oe=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.attributeParameterValues=null,this.description=null,this.distanceAttributeName=null,this.id=null,this.impedanceAttributeName=null,this.name=null,this.restrictionAttributeNames=null,this.simplificationTolerance=null,this.simplificationToleranceUnits=null,this.timeAttributeName=null,this.type=null,this.useHierarchy=null,this.uturnAtJunctions=null}readId(e,t){return t.id??t.itemId??null}readRestrictionAttributes(e,t){const{restrictionAttributeNames:i}=t;return(0,m.Wi)(i)?null:i.map((e=>he.fromJSON(e)))}writeRestrictionAttributes(e,t,i){(0,m.Wi)(e)||(t[i]=e.map((e=>he.toJSON(e))))}};(0,r._)([(0,T.Cb)({type:[Object],json:{write:!0}})],Oe.prototype,"attributeParameterValues",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Oe.prototype,"description",void 0),(0,r._)([(0,K.J)(me,{ignoreUnknown:!1})],Oe.prototype,"distanceAttributeName",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Oe.prototype,"id",void 0),(0,r._)([(0,w.r)("id",["id","itemId"])],Oe.prototype,"readId",null),(0,r._)([(0,K.J)(be,{ignoreUnknown:!1})],Oe.prototype,"impedanceAttributeName",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Oe.prototype,"name",void 0),(0,r._)([(0,T.Cb)({type:[String],json:{write:!0}})],Oe.prototype,"restrictionAttributeNames",void 0),(0,r._)([(0,w.r)("restrictionAttributeNames")],Oe.prototype,"readRestrictionAttributes",null),(0,r._)([(0,_.c)("restrictionAttributeNames")],Oe.prototype,"writeRestrictionAttributes",null),(0,r._)([(0,T.Cb)({type:Number,json:{write:{allowNull:!0}}})],Oe.prototype,"simplificationTolerance",void 0),(0,r._)([(0,K.J)(Y)],Oe.prototype,"simplificationToleranceUnits",void 0),(0,r._)([(0,K.J)(ye,{ignoreUnknown:!1})],Oe.prototype,"timeAttributeName",void 0),(0,r._)([(0,K.J)(re)],Oe.prototype,"type",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Oe.prototype,"useHierarchy",void 0),(0,r._)([(0,K.J)(ie)],Oe.prototype,"uturnAtJunctions",void 0),Oe=(0,r._)([(0,S.j)("esri.rest.support.TravelMode")],Oe);const Ae=Oe;let De=class extends L.wq{constructor(e){super(e),this.accumulateAttributeNames=null,this.attributeParameterValues=null,this.currentVersion=null,this.defaultTravelMode=null,this.directionsLanguage=null,this.directionsLengthUnits=null,this.directionsSupportedLanguages=null,this.directionsTimeAttribute=null,this.hasZ=null,this.impedance=null,this.networkDataset=null,this.supportedTravelModes=null}readAccumulateAttributes(e){return(0,m.Wi)(e)?null:e.map((e=>be.fromJSON(e)))}writeAccumulateAttributes(e,t,i){!(0,m.Wi)(e)&&e.length&&(t[i]=e.map((e=>be.toJSON(e))))}readDefaultTravelMode(e,t){const i=t.supportedTravelModes?.find((({id:e})=>e===t.defaultTravelMode))??t.supportedTravelModes?.find((({itemId:e})=>e===t.defaultTravelMode));return i?Ae.fromJSON(i):null}};(0,r._)([(0,T.Cb)()],De.prototype,"accumulateAttributeNames",void 0),(0,r._)([(0,w.r)("accumulateAttributeNames")],De.prototype,"readAccumulateAttributes",null),(0,r._)([(0,_.c)("accumulateAttributeNames")],De.prototype,"writeAccumulateAttributes",null),(0,r._)([(0,T.Cb)()],De.prototype,"attributeParameterValues",void 0),(0,r._)([(0,T.Cb)()],De.prototype,"currentVersion",void 0),(0,r._)([(0,T.Cb)()],De.prototype,"defaultTravelMode",void 0),(0,r._)([(0,w.r)("defaultTravelMode",["defaultTravelMode","supportedTravelModes"])],De.prototype,"readDefaultTravelMode",null),(0,r._)([(0,T.Cb)()],De.prototype,"directionsLanguage",void 0),(0,r._)([(0,K.J)($)],De.prototype,"directionsLengthUnits",void 0),(0,r._)([(0,T.Cb)()],De.prototype,"directionsSupportedLanguages",void 0),(0,r._)([(0,K.J)(ye,{ignoreUnknown:!1})],De.prototype,"directionsTimeAttribute",void 0),(0,r._)([(0,T.Cb)()],De.prototype,"hasZ",void 0),(0,r._)([(0,K.J)(be,{ignoreUnknown:!1})],De.prototype,"impedance",void 0),(0,r._)([(0,T.Cb)({type:_e})],De.prototype,"networkDataset",void 0),(0,r._)([(0,T.Cb)({type:[Ae]})],De.prototype,"supportedTravelModes",void 0),De=(0,r._)([(0,S.j)("esri.rest.support.NetworkServiceDescription")],De);const Ie=De,xe=c.Z.getLogger("esri.rest.networkService");function je(e,t,i,r){r[i]=[t.length,t.length+e.length],e.forEach((e=>{t.push(e.geometry)}))}async function Fe(e,t,i){if(!e)throw new u.Z("network-service:missing-url","Url to Network service is missing");const r=(0,H.lA)({f:"json",token:t},i),{data:o}=await(0,z.default)(e,r),s=o.currentVersion>=10.4?async function(e,t,i){try{const r=(0,H.lA)({f:"json",token:t},i),o=(0,g.Qj)(e)+"/retrieveTravelModes",{data:{supportedTravelModes:s,defaultTravelMode:a}}=await(0,z.default)(o,r);return{supportedTravelModes:s,defaultTravelMode:a}}catch(e){throw new u.Z("network-service:retrieveTravelModes","Could not get to the NAServer's retrieveTravelModes.",{error:e})}}(e,t,i):async function(e,t){const i=(0,H.lA)({f:"json"},t),{data:r}=await(0,z.default)(e.replace(/\/rest\/.*$/i,"/info"),i);if(!r||!r.owningSystemUrl)return{supportedTravelModes:[],defaultTravelMode:null};const{owningSystemUrl:o}=r,s=(0,g.Qj)(o)+"/sharing/rest/portals/self",{data:a}=await(0,z.default)(s,i),n=(0,b.hS)("helperServices.routingUtilities.url",a);if(!n)return{supportedTravelModes:[],defaultTravelMode:null};const l=(0,H.en)(o),p=/\/solve$/i.test(l.path)?"Route":/\/solveclosestfacility$/i.test(l.path)?"ClosestFacility":"ServiceAreas",u=(0,H.lA)({f:"json",serviceName:p},t),d=(0,g.Qj)(n)+"/GetTravelModes/execute",c=await(0,z.default)(d,u),m=[];let y=null;if(c?.data?.results?.length){const e=c.data.results;for(const t of e)if("supportedTravelModes"===t.paramName){if(t.value?.features)for(const{attributes:e}of t.value.features)if(e){const t=JSON.parse(e.TravelMode);m.push(t)}}else"defaultTravelMode"===t.paramName&&(y=t.value)}return{supportedTravelModes:m,defaultTravelMode:y}}(e,i),{defaultTravelMode:a,supportedTravelModes:n}=await s;return o.defaultTravelMode=a,o.supportedTravelModes=n,Ie.fromJSON(o)}var Be=i(16306);function Me(e,t){if((0,m.Wi)(e))return null;const i={},r=new RegExp(`^${t}`,"i");for(const o of Object.keys(e))if(r.test(o)){const r=o.substring(t.length);i[be.fromJSON(r)]=e[o]}return i}function Ee(e,t,i){if(!(0,m.Wi)(e)){t.attributes||(t.attributes={});for(const r in e){const o=be.toJSON(r);t.attributes[`${i}${o}`]=e[r]}}}function Le(e){const t={};for(const i of Object.keys(e)){const r=i;t[be.fromJSON(r)]=e[i]}return t}function Re(e){const t={};for(const i of Object.keys(e)){const r=i;t[be.toJSON(r)]=e[i]}return t}function Pe(e,t){return(0,m.Wi)(e)||(0,m.Wi)(t)?null:Math.round((e-t)/6e4)}function Je(e){const t=e.toJSON(),i=t;return i.accumulateAttributeNames&&(i.accumulateAttributeNames=t.accumulateAttributeNames?.join()),i.attributeParameterValues&&(i.attributeParameterValues=JSON.stringify(t.attributeParameterValues)),i.barriers&&(i.barriers=JSON.stringify(t.barriers)),i.outSR&&(i.outSR=t.outSR?.wkid),i.overrides&&(i.overrides=JSON.stringify(t.overrides)),i.polygonBarriers&&(i.polygonBarriers=JSON.stringify(t.polygonBarriers)),i.polylineBarriers&&(i.polylineBarriers=JSON.stringify(t.polylineBarriers)),i.restrictionAttributeNames&&(i.restrictionAttributeNames=t.restrictionAttributeNames?.join()),i.stops&&(i.stops=JSON.stringify(t.stops)),i.travelMode&&(i.travelMode=JSON.stringify(t.travelMode)),i}var Ue=i(74889);const We=new X.X({esriJobMessageTypeInformative:"informative",esriJobMessageTypeProcessDefinition:"process-definition",esriJobMessageTypeProcessStart:"process-start",esriJobMessageTypeProcessStop:"process-stop",esriJobMessageTypeWarning:"warning",esriJobMessageTypeError:"error",esriJobMessageTypeEmpty:"empty",esriJobMessageTypeAbort:"abort"});let ke=class extends L.wq{constructor(e){super(e),this.description=null,this.type=null}};(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],ke.prototype,"description",void 0),(0,r._)([(0,T.Cb)({type:String,json:{read:We.read,write:We.write}})],ke.prototype,"type",void 0),ke=(0,r._)([(0,S.j)("esri.rest.support.GPMessage")],ke);const Ze=ke,Ve=new X.X({0:"informative",1:"process-definition",2:"process-start",3:"process-stop",50:"warning",100:"error",101:"empty",200:"abort"});let qe=class extends Ze{constructor(e){super(e),this.type=null}};(0,r._)([(0,T.Cb)({type:String,json:{read:Ve.read,write:Ve.write}})],qe.prototype,"type",void 0),qe=(0,r._)([(0,S.j)("esri.rest.support.NAMessage")],qe);const Ge=qe;let ze=class extends L.wq{constructor(e){super(e)}};(0,r._)([(0,T.Cb)({json:{read:{source:"string"}}})],ze.prototype,"text",void 0),(0,r._)([(0,K.J)(Te,{name:"stringType"})],ze.prototype,"type",void 0),ze=(0,r._)([(0,S.j)("esri.rest.support.DirectionsString")],ze);const He=ze;var Ke=i(94139);let Xe=class extends L.wq{constructor(e){super(e),this.arriveTime=null,this.arriveTimeOffset=null,this.geometry=null,this.strings=null}readArriveTimeOffset(e,t){return Pe(t.ETA,t.arriveTimeUTC)}readGeometry(e,t){return Ke.Z.fromJSON(t.point)}};(0,r._)([(0,T.Cb)({type:Date,json:{read:{source:"arriveTimeUTC"}}})],Xe.prototype,"arriveTime",void 0),(0,r._)([(0,T.Cb)()],Xe.prototype,"arriveTimeOffset",void 0),(0,r._)([(0,w.r)("arriveTimeOffset",["arriveTimeUTC","ETA"])],Xe.prototype,"readArriveTimeOffset",null),(0,r._)([(0,T.Cb)({type:Ke.Z})],Xe.prototype,"geometry",void 0),(0,r._)([(0,w.r)("geometry",["point"])],Xe.prototype,"readGeometry",null),(0,r._)([(0,T.Cb)({type:[He]})],Xe.prototype,"strings",void 0),Xe=(0,r._)([(0,S.j)("esri.rest.support.DirectionsEvent")],Xe);const Ye=Xe;var $e=i(58901);let Qe=class extends a.Z{constructor(e){super(e),this.events=null,this.strings=null}readGeometry(e,t){const i=function(e){if((0,m.Wi)(e)||""===e)return null;let t=0,i=0,r=0,o=0;const s=[];let a,n,l,p,u,d,c,y,b=0,h=0,f=0;if(u=e.match(/((\+|\-)[^\+\-\|]+|\|)/g),u||(u=[]),0===parseInt(u[b],32)){b=2;const e=parseInt(u[b],32);b++,d=parseInt(u[b],32),b++,1&e&&(h=u.indexOf("|")+1,c=parseInt(u[h],32),h++),2&e&&(f=u.indexOf("|",h)+1,y=parseInt(u[f],32),f++)}else d=parseInt(u[b],32),b++;for(;b<u.length&&"|"!==u[b];){a=parseInt(u[b],32)+t,b++,t=a,n=parseInt(u[b],32)+i,b++,i=n;const e=[a/d,n/d];h&&(p=parseInt(u[h],32)+r,h++,r=p,e.push(p/c)),f&&(l=parseInt(u[f],32)+o,f++,o=l,e.push(l/y)),s.push(e)}return{paths:[s],hasZ:h>0,hasM:f>0}}(t.compressedGeometry);return(0,m.pC)(i)?$e.Z.fromJSON(i):null}};(0,r._)([(0,T.Cb)({type:[Ye]})],Qe.prototype,"events",void 0),(0,r._)([(0,w.r)("geometry",["compressedGeometry"])],Qe.prototype,"readGeometry",null),(0,r._)([(0,T.Cb)({type:[He]})],Qe.prototype,"strings",void 0),Qe=(0,r._)([(0,S.j)("esri.rest.support.DirectionsFeature")],Qe);const et=Qe;var tt=i(82971);let it=class extends Ue.Z{constructor(e){super(e),this.extent=null,this.features=[],this.geometryType="polyline",this.routeId=null,this.routeName=null,this.totalDriveTime=null,this.totalLength=null,this.totalTime=null}readFeatures(e,t){if(!e)return[];const i=t.summary.envelope.spatialReference??t.spatialReference,r=i&&tt.Z.fromJSON(i);return e.map((e=>{const t=et.fromJSON(e);if((0,m.pC)(t.geometry)&&(t.geometry.spatialReference=r),(0,m.pC)(t.events))for(const e of t.events)(0,m.pC)(e.geometry)&&(e.geometry.spatialReference=r);return t}))}get mergedGeometry(){return this.features?function(e,t){if(0===e.length)return new $e.Z({spatialReference:t});const i=[];for(const t of e)for(const e of t.paths)i.push(...e);const r=[];i.forEach(((e,t)=>{0!==t&&e[0]===i[t-1][0]&&e[1]===i[t-1][1]||r.push(e)}));const{hasM:o,hasZ:s}=e[0];return new $e.Z({hasM:o,hasZ:s,paths:[r],spatialReference:t})}(this.features.map((({geometry:e})=>(0,m.Wg)(e))),this.extent.spatialReference):null}get strings(){return this.features.map((({strings:e})=>e)).flat().filter(m.pC)}};(0,r._)([(0,T.Cb)({type:O.Z,json:{read:{source:"summary.envelope"}}})],it.prototype,"extent",void 0),(0,r._)([(0,T.Cb)({nonNullable:!0})],it.prototype,"features",void 0),(0,r._)([(0,w.r)("features")],it.prototype,"readFeatures",null),(0,r._)([(0,T.Cb)()],it.prototype,"geometryType",void 0),(0,r._)([(0,T.Cb)({readOnly:!0})],it.prototype,"mergedGeometry",null),(0,r._)([(0,T.Cb)()],it.prototype,"routeId",void 0),(0,r._)([(0,T.Cb)()],it.prototype,"routeName",void 0),(0,r._)([(0,T.Cb)({value:null,readOnly:!0})],it.prototype,"strings",null),(0,r._)([(0,T.Cb)({json:{read:{source:"summary.totalDriveTime"}}})],it.prototype,"totalDriveTime",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"summary.totalLength"}}})],it.prototype,"totalLength",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"summary.totalTime"}}})],it.prototype,"totalTime",void 0),it=(0,r._)([(0,S.j)("esri.rest.support.DirectionsFeatureSet")],it);const rt=it;let ot=class extends L.wq{constructor(e){super(e),this.directionLines=null,this.directionPoints=null,this.directions=null,this.route=null,this.routeName=null,this.stops=null,this.traversedEdges=null,this.traversedJunctions=null,this.traversedTurns=null}};(0,r._)([(0,T.Cb)({type:Ue.Z,json:{write:!0}})],ot.prototype,"directionLines",void 0),(0,r._)([(0,T.Cb)({type:Ue.Z,json:{write:!0}})],ot.prototype,"directionPoints",void 0),(0,r._)([(0,T.Cb)({type:rt,json:{write:!0}})],ot.prototype,"directions",void 0),(0,r._)([(0,T.Cb)({type:a.Z,json:{write:!0}})],ot.prototype,"route",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],ot.prototype,"routeName",void 0),(0,r._)([(0,T.Cb)({type:[a.Z],json:{write:!0}})],ot.prototype,"stops",void 0),(0,r._)([(0,T.Cb)({type:Ue.Z,json:{write:!0}})],ot.prototype,"traversedEdges",void 0),(0,r._)([(0,T.Cb)({type:Ue.Z,json:{write:!0}})],ot.prototype,"traversedJunctions",void 0),(0,r._)([(0,T.Cb)({type:Ue.Z,json:{write:!0}})],ot.prototype,"traversedTurns",void 0),ot=(0,r._)([(0,S.j)("esri.rest.support.RouteResult")],ot);const st=ot;function at(e){return e?Ue.Z.fromJSON(e).features.filter(m.pC):[]}let nt=class extends L.wq{constructor(e){super(e),this.messages=null,this.pointBarriers=null,this.polylineBarriers=null,this.polygonBarriers=null,this.routeResults=null}readPointBarriers(e,t){return at(t.barriers)}readPolylineBarriers(e){return at(e)}readPolygonBarriers(e){return at(e)}};(0,r._)([(0,T.Cb)({type:[Ge]})],nt.prototype,"messages",void 0),(0,r._)([(0,T.Cb)({type:[a.Z]})],nt.prototype,"pointBarriers",void 0),(0,r._)([(0,w.r)("pointBarriers",["barriers"])],nt.prototype,"readPointBarriers",null),(0,r._)([(0,T.Cb)({type:[a.Z]})],nt.prototype,"polylineBarriers",void 0),(0,r._)([(0,w.r)("polylineBarriers")],nt.prototype,"readPolylineBarriers",null),(0,r._)([(0,T.Cb)({type:[a.Z]})],nt.prototype,"polygonBarriers",void 0),(0,r._)([(0,w.r)("polygonBarriers")],nt.prototype,"readPolygonBarriers",null),(0,r._)([(0,T.Cb)({type:[st]})],nt.prototype,"routeResults",void 0),nt=(0,r._)([(0,S.j)("esri.rest.support.RouteSolveResult")],nt);const lt=nt;function pt(e){return e instanceof Ue.Z}var ut;let dt=ut=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.directionLineType=null,this.directionPointId=null,this.distance=null,this.duration=null,this.fromLevel=null,this.geometry=null,this.objectId=null,this.popupTemplate=null,this.symbol=null,this.toLevel=null,this.type="direction-line"}static fromGraphic(e){return new ut({directionLineType:de.fromJSON(e.attributes.DirectionLineType),directionPointId:e.attributes.DirectionPointID,distance:e.attributes.Meters,duration:e.attributes.Minutes,fromLevel:e.attributes.FromLevel??null,geometry:e.geometry,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,symbol:e.symbol,toLevel:e.attributes.ToLevel??null})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),DirectionLineType:(0,m.pC)(this.directionLineType)?de.toJSON(this.directionLineType):null,DirectionPointID:(0,m.Wg)(this.directionPointId),Meters:(0,m.Wg)(this.distance),Minutes:(0,m.Wg)(this.duration)};return(0,m.pC)(this.fromLevel)&&(e.FromLevel=this.fromLevel),(0,m.pC)(this.toLevel)&&(e.ToLevel=this.toLevel),new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:this.popupTemplate})}};dt.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"DirectionLineType",alias:"Line Type",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriDirectionsLineType",codedValues:[{name:"Unknown",code:0},{name:"Segment",code:1},{name:"Maneuver Segment",code:2},{name:"Restriction violation",code:3},{name:"Scale cost barrier crossing",code:4},{name:"Heavy Traffic",code:5},{name:"Slow Traffic",code:6},{name:"Moderate Traffic",code:7}]}},{name:"DirectionPointID",alias:"Direction Point ID",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!1},{name:"FromLevel",alias:"Start from 3D Level",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!1},{name:"Meters",alias:"Length in Meters",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"Minutes",alias:"Duration in Minutes",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"ToLevel",alias:"End at 3D Level",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!1}],dt.popupInfo={title:"Direction Lines",fieldInfos:[{fieldName:"DirectionLineType",label:"Line Type",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"Meters",label:"Length in Meters",isEditable:!1,tooltip:"",visible:!0,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Minutes",label:"Duration in Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"DirectionPointID",label:"Direction Point ID",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"FromLevel",label:"Start from 3D Level",isEditable:!1,tooltip:"",visible:!1,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ToLevel",label:"End at 3D Level",isEditable:!1,tooltip:"",visible:!1,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)({type:de.apiValues,json:{read:{source:"attributes.DirectionLineType",reader:de.read}}})],dt.prototype,"directionLineType",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.DirectionPointID"}}})],dt.prototype,"directionPointId",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.Meters"}}})],dt.prototype,"distance",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.Minutes"}}})],dt.prototype,"duration",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.FromLevel"}}})],dt.prototype,"fromLevel",void 0),(0,r._)([(0,T.Cb)({type:$e.Z})],dt.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.ObjectID"}}})],dt.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],dt.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],dt.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.ToLevel"}}})],dt.prototype,"toLevel",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],dt.prototype,"type",void 0),dt=ut=(0,r._)([(0,S.j)("esri.rest.support.DirectionLine")],dt);const ct=dt;var mt;let yt=mt=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.alternateName=null,this.arrivalTime=null,this.arrivalTimeOffset=null,this.azimuth=null,this.branchName=null,this.directionPointType=null,this.displayText=null,this.exitName=null,this.geometry=null,this.intersectingName=null,this.level=null,this.name=null,this.objectId=null,this.popupTemplate=null,this.sequence=null,this.shortVoiceInstruction=null,this.stopId=null,this.symbol=null,this.towardName=null,this.type="direction-point",this.voiceInstruction=null}readArrivalTime(e,t){return(0,m.pC)(t.attributes.ArrivalTime)?new Date(t.attributes.ArrivalTime):null}static fromGraphic(e){return new mt({alternateName:e.attributes.AlternateName??null,arrivalTime:(0,m.pC)(e.attributes.ArrivalTime)?new Date(e.attributes.ArrivalTime):null,arrivalTimeOffset:e.attributes.ArrivalUTCOffset??null,azimuth:e.attributes.Azimuth??null,branchName:e.attributes.BranchName??null,directionPointType:ue.fromJSON(e.attributes.DirectionPointType),displayText:e.attributes.DisplayText??null,exitName:e.attributes.ExitName??null,geometry:e.geometry,intersectingName:e.attributes.IntersectingName??null,level:e.attributes.Level??null,name:e.attributes.Name??null,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,sequence:e.attributes.Sequence,shortVoiceInstruction:e.attributes.ShortVoiceInstruction??null,stopId:e.attributes.StopID??null,symbol:e.symbol,towardName:e.attributes.TowardName??null,voiceInstruction:e.attributes.VoiceInstruction??null})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),DirectionPointType:(0,m.pC)(this.directionPointType)?ue.toJSON(this.directionPointType):null,Sequence:(0,m.Wg)(this.sequence),StopID:this.stopId};return(0,m.pC)(this.alternateName)&&(e.AlternateName=this.alternateName),(0,m.pC)(this.arrivalTime)&&(e.ArrivalTime=this.arrivalTime.getTime()),(0,m.pC)(this.arrivalTimeOffset)&&(e.ArrivalUTCOffset=this.arrivalTimeOffset),(0,m.pC)(this.azimuth)&&(e.Azimuth=this.azimuth),(0,m.pC)(this.branchName)&&(e.BranchName=this.branchName),(0,m.pC)(this.displayText)&&(e.DisplayText=this.displayText),(0,m.pC)(this.exitName)&&(e.ExitName=this.exitName),(0,m.pC)(this.intersectingName)&&(e.IntersectingName=this.intersectingName),(0,m.pC)(this.level)&&(e.Level=this.level),(0,m.pC)(this.name)&&(e.Name=this.name),(0,m.pC)(this.shortVoiceInstruction)&&(e.ShortVoiceInstruction=this.shortVoiceInstruction),(0,m.pC)(this.towardName)&&(e.TowardName=this.towardName),(0,m.pC)(this.voiceInstruction)&&(e.VoiceInstruction=this.voiceInstruction),new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:this.popupTemplate})}};yt.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"AlternateName",alias:"Alternative Feature Name",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"ArrivalTime",alias:"Maneuver Starts at",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!0},{name:"ArrivalUTCOffset",alias:"Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"Azimuth",alias:"Azimuth",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"BranchName",alias:"Signpost Branch Name",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"DirectionPointType",alias:"Directions Item Type",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriDirectionPointType",codedValues:[{name:"Unknown",code:0},{name:"",code:1},{name:"Arrive at stop",code:50},{name:"Depart at stop",code:51},{name:"Go straight",code:52},{name:"Take ferry",code:100},{name:"Take off ferry",code:101},{name:"Keep center at fork",code:102},{name:"Take roundabout",code:103},{name:"Make U-Turn",code:104},{name:"Pass the door",code:150},{name:"Take stairs",code:151},{name:"",code:152},{name:"Take escalator",code:153},{name:"Take pedestrian ramp",code:154},{name:"Keep left at fork",code:200},{name:"Ramp left",code:201},{name:"Take left-handed roundabout",code:202},{name:"Make left-handed U-Turn",code:203},{name:"Bear left",code:204},{name:"Turn left",code:205},{name:"Make sharp left",code:206},{name:"Turn left, followed by turn left",code:207},{name:"Turn left, followed by turn right",code:208},{name:"Keep right at fork",code:300},{name:"Ramp right",code:301},{name:"Take right-handed roundabout",code:302},{name:"Make right-handed U-Turn",code:303},{name:"Bear right",code:304},{name:"Turn right",code:305},{name:"Make sharp right",code:306},{name:"Turn right, followed by turn left",code:307},{name:"Turn right, followed by turn right",code:308},{name:"Indicates up direction of elevator",code:400},{name:"Indicates up direction of escalator",code:401},{name:"Take up-stairs",code:402},{name:"Indicates down direction of elevator",code:500},{name:"Indicates down direction of escalator",code:501},{name:"Take down-stairs",code:502},{name:"General event",code:1e3},{name:"Landmark",code:1001},{name:"Time zone change",code:1002},{name:"Heavy traffic segment",code:1003},{name:"Scale cost barrier crossing",code:1004},{name:"Administrative Border crossing",code:1005},{name:"Restriction violation",code:1006}]}},{name:"DisplayText",alias:"Text to Display",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"ExitName",alias:"Highway Exit Name",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"IntersectingName",alias:"Intersecting Feature Name",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"Level",alias:"3D Logical Level",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"Name",alias:"Primary Feature Name",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"Sequence",alias:"Sequence",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"ShortVoiceInstruction",alias:"Voice Instruction",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"StopID",alias:"Stop ID",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"TowardName",alias:"Signpost Toward Name",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null},{name:"VoiceInstruction",alias:"Voice Full Instruction",type:"esriFieldTypeString",length:2048,editable:!0,nullable:!0,visible:!0,domain:null}],yt.popupInfo={title:"{DisplayText}",fieldInfos:[{fieldName:"DirectionPointType",label:"Directions Item Type",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"DisplayText",label:"Text to Display",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"Sequence",label:"Sequence",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"StopID",label:"Stop ID",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ArrivalTime",label:"Maneuver Starts at",isEditable:!0,tooltip:"",visible:!0,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"ArrivalUTCOffset",label:"Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Azimuth",label:"Azimuth",isEditable:!1,tooltip:"",visible:!1,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Name",label:"Primary Feature Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"AlternateName",label:"Alternative Feature Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"ExitName",label:"Highway Exit Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"IntersectingName",label:"Intersecting Feature Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"BranchName",label:"Signpost Branch Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"TowardName",label:"Signpost Toward Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"ShortVoiceInstruction",label:"Voice Instruction",isEditable:!1,tooltip:"",visible:!1,stringFieldOption:"textbox"},{fieldName:"VoiceInstruction",label:"Voice Full Instruction",isEditable:!1,tooltip:"",visible:!1,stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)()],yt.prototype,"alternateName",void 0),(0,r._)([(0,T.Cb)()],yt.prototype,"arrivalTime",void 0),(0,r._)([(0,w.r)("arrivalTime",["attributes.ArrivalTime"])],yt.prototype,"readArrivalTime",null),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.ArrivalUTCOffset"}}})],yt.prototype,"arrivalTimeOffset",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.Azimuth"}}})],yt.prototype,"azimuth",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.BranchName"}}})],yt.prototype,"branchName",void 0),(0,r._)([(0,T.Cb)({type:ue.apiValues,json:{read:{source:"attributes.DirectionPointType",reader:ue.read}}})],yt.prototype,"directionPointType",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.DisplayText"}}})],yt.prototype,"displayText",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.ExitName"}}})],yt.prototype,"exitName",void 0),(0,r._)([(0,T.Cb)({type:Ke.Z})],yt.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)()],yt.prototype,"intersectingName",void 0),(0,r._)([(0,T.Cb)()],yt.prototype,"level",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.Name"}}})],yt.prototype,"name",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.ObjectID"}}})],yt.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],yt.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.Sequence"}}})],yt.prototype,"sequence",void 0),(0,r._)([(0,T.Cb)()],yt.prototype,"shortVoiceInstruction",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.StopID"}}})],yt.prototype,"stopId",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],yt.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.TowardName"}}})],yt.prototype,"towardName",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],yt.prototype,"type",void 0),(0,r._)([(0,T.Cb)()],yt.prototype,"voiceInstruction",void 0),yt=mt=(0,r._)([(0,S.j)("esri.rest.support.DirectionPoint")],yt);const bt=yt;var ht;let ft=ht=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.addedCost=null,this.barrierType=null,this.costs=null,this.curbApproach=null,this.fullEdge=null,this.geometry=null,this.name=null,this.objectId=null,this.popupTemplate=null,this.sideOfEdge=null,this.sourceId=null,this.sourceOid=null,this.status=null,this.symbol=null,this.type="point-barrier"}readCosts(e,t){return Me(t.attributes,"Attr_")}writeCosts(e,t){Ee(e,t,"Attr_")}static fromGraphic(e){return new ht({addedCost:e.attributes.AddedCost??null,barrierType:(0,m.pC)(e.attributes.BarrierType)?le.fromJSON(e.attributes.BarrierType):null,costs:(0,m.pC)(e.attributes.Costs)?Le(JSON.parse(e.attributes.Costs)):null,curbApproach:(0,m.pC)(e.attributes.CurbApproach)?oe.fromJSON(e.attributes.CurbApproach):null,fullEdge:(0,m.pC)(e.attributes.FullEdge)?pe.fromJSON(e.attributes.FullEdge):null,geometry:e.geometry,name:e.attributes.Name??null,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,status:(0,m.pC)(e.attributes.Status)?ae.fromJSON(e.attributes.Status):null,symbol:e.symbol})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),AddedCost:this.addedCost,BarrierType:(0,m.pC)(this.barrierType)?le.toJSON(this.barrierType):null,Costs:(0,m.pC)(this.costs)?JSON.stringify(Re(this.costs)):null,CurbApproach:(0,m.pC)(this.curbApproach)?oe.toJSON(this.curbApproach):null,FullEdge:(0,m.pC)(this.fullEdge)?pe.toJSON(this.fullEdge):null,Name:this.name,Status:(0,m.pC)(this.status)?ae.toJSON(this.status):null};return new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:this.popupTemplate})}};ft.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"AddedCost",alias:"Added Cost",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0,domain:null},{name:"BarrierType",alias:"Barrier Type",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNABarrierType",codedValues:[{name:"Restriction",code:0},{name:"Scaled Cost",code:1},{name:"Added Cost",code:2}]}},{name:"Costs",alias:"Costs",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"CurbApproach",alias:"Curb Approach",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!1,domain:{type:"codedValue",name:"esriNACurbApproachType",codedValues:[{name:"Either side",code:0},{name:"From the right",code:1},{name:"From the left",code:2},{name:"Depart in the same direction",code:3}]}},{name:"FullEdge",alias:"Full Edge",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNAIntYesNo",codedValues:[{name:"No",code:0},{name:"Yes",code:1}]}},{name:"Name",alias:"Name",type:"esriFieldTypeString",length:255,editable:!0,nullable:!0,visible:!0},{name:"Status",alias:"Status",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNAObjectStatus",codedValues:[{name:"OK",code:0},{name:"Not Located on Network",code:1},{name:"Network Unbuilt",code:2},{name:"Prohibited Street",code:3},{name:"Invalid Field Values",code:4},{name:"Cannot Reach",code:5},{name:"Time Window Violation",code:6}]}}],ft.popupInfo={title:"Point Barriers",fieldInfos:[{fieldName:"Name",label:"Name",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"BarrierType",label:"Barrier Type",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"AddedCost",label:"Added Cost",isEditable:!0,tooltip:"",visible:!0,format:{places:3,digitSeparator:!0},stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)()],ft.prototype,"addedCost",void 0),(0,r._)([(0,T.Cb)({type:le.apiValues,json:{name:"attributes.BarrierType",read:{reader:le.read},write:{writer:le.write}}})],ft.prototype,"barrierType",void 0),(0,r._)([(0,T.Cb)()],ft.prototype,"costs",void 0),(0,r._)([(0,w.r)("costs",["attributes"])],ft.prototype,"readCosts",null),(0,r._)([(0,_.c)("costs")],ft.prototype,"writeCosts",null),(0,r._)([(0,T.Cb)({type:oe.apiValues,json:{read:{source:"attributes.CurbApproach",reader:oe.read}}})],ft.prototype,"curbApproach",void 0),(0,r._)([(0,T.Cb)({type:pe.apiValues,json:{name:"attributes.FullEdge",read:{reader:pe.read},write:{writer:pe.write}}})],ft.prototype,"fullEdge",void 0),(0,r._)([(0,T.Cb)({type:Ke.Z,json:{write:!0}})],ft.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.Name"}})],ft.prototype,"name",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.ObjectID"}})],ft.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],ft.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)({type:ne.apiValues,json:{read:{source:"attributes.SideOfEdge",reader:ne.read}}})],ft.prototype,"sideOfEdge",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SourceID"}}})],ft.prototype,"sourceId",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SourceOID"}}})],ft.prototype,"sourceOid",void 0),(0,r._)([(0,T.Cb)({type:ae.apiValues,json:{read:{source:"attributes.Status",reader:ae.read}}})],ft.prototype,"status",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],ft.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],ft.prototype,"type",void 0),ft=ht=(0,r._)([(0,S.j)("esri.rest.support.PointBarrier")],ft);const vt=ft;var gt,Tt=i(38913);let Ct=gt=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.barrierType=null,this.costs=null,this.geometry=null,this.name=null,this.objectId=null,this.popupTemplate=null,this.scaleFactor=null,this.symbol=null,this.type="polygon-barrier"}readCosts(e,t){return Me(t.attributes,"Attr_")}writeCosts(e,t){Ee(e,t,"Attr_")}static fromGraphic(e){return new gt({barrierType:(0,m.pC)(e.attributes.BarrierType)?le.fromJSON(e.attributes.BarrierType):null,costs:(0,m.pC)(e.attributes.Costs)?Le(JSON.parse(e.attributes.Costs)):null,geometry:e.geometry,name:e.attributes.Name??null,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,scaleFactor:e.attributes.ScaleFactor??null,symbol:e.symbol})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),BarrierType:(0,m.pC)(this.barrierType)?le.toJSON(this.barrierType):null,Costs:(0,m.pC)(this.costs)?JSON.stringify(Re(this.costs)):null,Name:this.name??null,ScaleFactor:this.scaleFactor??null};return new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:this.popupTemplate})}};Ct.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"BarrierType",alias:"Barrier Type",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNABarrierType",codedValues:[{name:"Restriction",code:0},{name:"Scaled Cost",code:1},{name:"Added Cost",code:2}]}},{name:"Costs",alias:"Costs",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"Name",alias:"Name",type:"esriFieldTypeString",length:255,editable:!0,nullable:!0,visible:!0},{name:"ScaleFactor",alias:"Scale Factor",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0}],Ct.popupInfo={title:"Polygon Barriers",fieldInfos:[{fieldName:"Name",label:"Name",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"BarrierType",label:"Barrier Type",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"ScaleFactor",isEditable:!0,tooltip:"",visible:!0,format:{places:3,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Costs",label:"Costs",isEditable:!0,tooltip:"",visible:!1,stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)({type:le.apiValues,json:{name:"attributes.BarrierType",read:{reader:le.read},write:{writer:le.write}}})],Ct.prototype,"barrierType",void 0),(0,r._)([(0,T.Cb)()],Ct.prototype,"costs",void 0),(0,r._)([(0,w.r)("costs",["attributes"])],Ct.prototype,"readCosts",null),(0,r._)([(0,_.c)("costs")],Ct.prototype,"writeCosts",null),(0,r._)([(0,T.Cb)({type:Tt.Z,json:{write:!0}})],Ct.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.Name"}})],Ct.prototype,"name",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.ObjectID"}})],Ct.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],Ct.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)()],Ct.prototype,"scaleFactor",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],Ct.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],Ct.prototype,"type",void 0),Ct=gt=(0,r._)([(0,S.j)("esri.rest.support.PolygonBarrier")],Ct);const wt=Ct;var St;let _t=St=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.barrierType=null,this.costs=null,this.geometry=null,this.name=null,this.objectId=null,this.popupTemplate=null,this.scaleFactor=null,this.symbol=null,this.type="polyline-barrier"}readCosts(e,t){return Me(t.attributes,"Attr_")}static fromGraphic(e){return new St({barrierType:(0,m.pC)(e.attributes.BarrierType)?le.fromJSON(e.attributes.BarrierType):null,costs:(0,m.pC)(e.attributes.Costs)?Le(JSON.parse(e.attributes.Costs)):null,geometry:e.geometry,name:e.attributes.Name??null,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,scaleFactor:e.attributes.ScaleFactor??null,symbol:e.symbol})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),BarrierType:(0,m.pC)(this.barrierType)?le.toJSON(this.barrierType):null,Costs:(0,m.pC)(this.costs)?JSON.stringify(Re(this.costs)):null,Name:this.name,ScaleFactor:this.scaleFactor};return new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:this.popupTemplate})}};_t.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"BarrierType",alias:"Barrier Type",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNABarrierType",codedValues:[{name:"Restriction",code:0},{name:"Scaled Cost",code:1},{name:"Added Cost",code:2}]}},{name:"Costs",alias:"Costs",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"Name",alias:"Name",type:"esriFieldTypeString",length:255,editable:!0,nullable:!0,visible:!0},{name:"ScaleFactor",alias:"Scale Factor",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0}],_t.popupInfo={title:"Line Barriers",fieldInfos:[{fieldName:"Name",label:"Name",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"BarrierType",label:"Barrier Type",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"ScaleFactor",isEditable:!0,tooltip:"",visible:!0,format:{places:3,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Costs",label:"Costs",isEditable:!0,tooltip:"",visible:!1,stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)({type:le.apiValues,json:{read:{source:"attributes.BarrierType",reader:le.read}}})],_t.prototype,"barrierType",void 0),(0,r._)([(0,T.Cb)()],_t.prototype,"costs",void 0),(0,r._)([(0,w.r)("costs",["attributes"])],_t.prototype,"readCosts",null),(0,r._)([(0,T.Cb)({type:$e.Z,json:{write:!0}})],_t.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.Name"}})],_t.prototype,"name",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.ObjectID"}})],_t.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],_t.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)()],_t.prototype,"scaleFactor",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],_t.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],_t.prototype,"type",void 0),_t=St=(0,r._)([(0,S.j)("esri.rest.support.PolylineBarrier")],_t);const Nt=_t;let Ot=class extends L.wq{constructor(e){super(e),this.accumulateAttributes=null,this.directionsLanguage=null,this.findBestSequence=null,this.preserveFirstStop=null,this.preserveLastStop=null,this.startTimeIsUTC=null,this.timeWindowsAreUTC=null,this.travelMode=null}readAccumulateAttributes(e){return(0,m.Wi)(e)?null:e.map((e=>be.fromJSON(e)))}writeAccumulateAttributes(e,t,i){!(0,m.Wi)(e)&&e.length&&(t[i]=e.map((e=>be.toJSON(e))))}};(0,r._)([(0,T.Cb)({type:[String],json:{name:"accumulateAttributeNames",write:!0}})],Ot.prototype,"accumulateAttributes",void 0),(0,r._)([(0,w.r)("accumulateAttributes")],Ot.prototype,"readAccumulateAttributes",null),(0,r._)([(0,_.c)("accumulateAttributes")],Ot.prototype,"writeAccumulateAttributes",null),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Ot.prototype,"directionsLanguage",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Ot.prototype,"findBestSequence",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Ot.prototype,"preserveFirstStop",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Ot.prototype,"preserveLastStop",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Ot.prototype,"startTimeIsUTC",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Ot.prototype,"timeWindowsAreUTC",void 0),(0,r._)([(0,T.Cb)({type:Ae,json:{write:!0}})],Ot.prototype,"travelMode",void 0),Ot=(0,r._)([(0,S.j)("esri.layers.support.RouteSettings")],Ot);const At=Ot;var Dt;let It=Dt=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.analysisSettings=null,this.endTime=null,this.endTimeOffset=null,this.firstStopId=null,this.geometry=null,this.lastStopId=null,this.messages=null,this.name=null,this.objectId=null,this.popupTemplate=null,this.startTime=null,this.startTimeOffset=null,this.stopCount=null,this.symbol=null,this.totalCosts=null,this.totalDistance=null,this.totalDuration=null,this.totalLateDuration=null,this.totalViolations=null,this.totalWait=null,this.totalWaitDuration=null,this.type="route-info",this.version="1.0.0"}readEndTime(e,t){return(0,m.pC)(t.attributes.EndTimeUTC)?new Date(t.attributes.EndTimeUTC):null}readEndTimeOffset(e,t){return Pe(t.attributes.EndTime,t.attributes.EndTimeUTC)}readStartTime(e,t){return(0,m.pC)(t.attributes.StartTimeUTC)?new Date(t.attributes.StartTimeUTC):null}readStartTimeOffset(e,t){return Pe(t.attributes.StartTime,t.attributes.StartTimeUTC)}readTotalCosts(e,t){return Me(t.attributes,"Total_")}readTotalViolations(e,t){return Me(t.attributes,"TotalViolation_")}readTotalWait(e,t){return Me(t.attributes,"TotalWait_")}static fromGraphic(e){return new Dt({analysisSettings:(0,m.pC)(e.attributes.AnalysisSettings)?At.fromJSON(JSON.parse(e.attributes.AnalysisSettings)):null,endTime:(0,m.pC)(e.attributes.EndTime)?new Date(e.attributes.EndTime):null,endTimeOffset:e.attributes.EndUTCOffset??null,geometry:e.geometry,messages:(0,m.pC)(e.attributes.Messages)?JSON.parse(e.attributes.Messages):null,name:e.attributes.RouteName,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,startTime:(0,m.pC)(e.attributes.StartTime)?new Date(e.attributes.StartTime):null,startTimeOffset:e.attributes.StartUTCOffset??null,symbol:e.symbol,totalCosts:(0,m.pC)(e.attributes.TotalCosts)?Le(JSON.parse(e.attributes.TotalCosts)):null,totalDistance:e.attributes.TotalMeters??null,totalDuration:e.attributes.TotalMinutes??null,totalLateDuration:e.attributes.TotalLateMinutes??null,totalWaitDuration:e.attributes.TotalWaitMinutes??null,version:e.attributes.Version})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),AnalysisSettings:(0,m.pC)(this.analysisSettings)?JSON.stringify(this.analysisSettings.toJSON()):null,EndTime:(0,m.pC)(this.endTime)?this.endTime.getTime():null,EndUTCOffset:this.endTimeOffset,Messages:(0,m.pC)(this.messages)?JSON.stringify(this.messages):null,RouteName:(0,m.Wg)(this.name),StartTime:(0,m.pC)(this.startTime)?this.startTime.getTime():null,StartUTCOffset:this.startTimeOffset,TotalCosts:(0,m.pC)(this.totalCosts)?JSON.stringify(Re(this.totalCosts)):null,TotalLateMinutes:this.totalLateDuration,TotalMeters:this.totalDistance,TotalMinutes:this.totalDuration,TotalWaitMinutes:this.totalWaitDuration,Version:(0,m.Wg)(this.version)};return new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:(0,m.Wg)(this.popupTemplate)})}};It.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"AnalysisSettings",alias:"Analysis Settings",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"EndTime",alias:"End Time",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!0},{name:"EndUTCOffset",alias:"End Time: Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"Messages",alias:"Analysis Messages",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"RouteName",alias:"Route Name",type:"esriFieldTypeString",length:1024,editable:!0,nullable:!0,visible:!0,domain:null},{name:"StartTime",alias:"Start Time",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!0},{name:"StartUTCOffset",alias:"Start Time: Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"TotalCosts",alias:"Total Costs",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"TotalLateMinutes",alias:"Total Late Minutes",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!1},{name:"TotalMeters",alias:"Total Meters",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"TotalMinutes",alias:"Total Minutes",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"TotalWaitMinutes",alias:"Total Wait Minutes",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!1},{name:"Version",alias:"Version",type:"esriFieldTypeString",length:16,editable:!0,nullable:!0,visible:!0,domain:null}],It.popupInfo={title:"Route Details",fieldInfos:[{fieldName:"RouteName",label:"Route Name",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"TotalMinutes",label:"Total Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"TotalMeters",label:"Total Meters",isEditable:!1,tooltip:"",visible:!0,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"TotalLateMinutes",label:"Total Late Minutes",isEditable:!1,tooltip:"",visible:!1,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"TotalWaitMinutes",label:"Total Wait Minutes",isEditable:!1,tooltip:"",visible:!1,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"TotalCosts",label:"Total Costs",isEditable:!1,tooltip:"",visible:!1,stringFieldOption:"textbox"},{fieldName:"StartTime",label:"Start Time",isEditable:!1,tooltip:"",visible:!0,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"StartUTCOffset",label:"Start Time: Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"EndTime",label:"End Time",isEditable:!1,tooltip:"",visible:!0,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"EndUTCOffset",label:"End Time: Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Messages",label:"Analysis Messages",isEditable:!1,tooltip:"",visible:!1,stringFieldOption:"textbox"},{fieldName:"AnalysisSettings",isEditable:!1,tooltip:"",visible:!1,stringFieldOption:"textbox"},{fieldName:"Version",label:"Version",isEditable:!1,tooltip:"",visible:!0,stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)()],It.prototype,"analysisSettings",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"endTime",void 0),(0,r._)([(0,w.r)("endTime",["attributes.EndTimeUTC"])],It.prototype,"readEndTime",null),(0,r._)([(0,T.Cb)()],It.prototype,"endTimeOffset",void 0),(0,r._)([(0,w.r)("endTimeOffset",["attributes.EndTime","attributes.EndTimeUTC"])],It.prototype,"readEndTimeOffset",null),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.FirstStopID"}}})],It.prototype,"firstStopId",void 0),(0,r._)([(0,T.Cb)({type:$e.Z})],It.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.LastStopID"}}})],It.prototype,"lastStopId",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"messages",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.Name"}}})],It.prototype,"name",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.ObjectID"}}})],It.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],It.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"startTime",void 0),(0,r._)([(0,w.r)("startTime",["attributes.StartTimeUTC"])],It.prototype,"readStartTime",null),(0,r._)([(0,T.Cb)()],It.prototype,"startTimeOffset",void 0),(0,r._)([(0,w.r)("startTimeOffset",["attributes.StartTime","attributes.StartTimeUTC"])],It.prototype,"readStartTimeOffset",null),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.StopCount"}}})],It.prototype,"stopCount",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],It.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"totalCosts",void 0),(0,r._)([(0,w.r)("totalCosts",["attributes"])],It.prototype,"readTotalCosts",null),(0,r._)([(0,T.Cb)()],It.prototype,"totalDistance",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"totalDuration",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"totalLateDuration",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"totalViolations",void 0),(0,r._)([(0,w.r)("totalViolations",["attributes"])],It.prototype,"readTotalViolations",null),(0,r._)([(0,T.Cb)()],It.prototype,"totalWait",void 0),(0,r._)([(0,w.r)("totalWait",["attributes"])],It.prototype,"readTotalWait",null),(0,r._)([(0,T.Cb)()],It.prototype,"totalWaitDuration",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],It.prototype,"type",void 0),(0,r._)([(0,T.Cb)()],It.prototype,"version",void 0),It=Dt=(0,r._)([(0,S.j)("esri.rest.support.RouteInfo")],It);const xt=It,jt={type:String,json:{read:{source:"token"},write:{target:"token"}}};var Ft=i(33955);let Bt=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.doNotLocateOnRestrictedElements=null,this.geometry=null,this.geometryType=null,this.name=null,this.spatialRelationship=null,this.type="layer",this.where=null}};(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Bt.prototype,"doNotLocateOnRestrictedElements",void 0),(0,r._)([(0,T.Cb)({types:s.qM,json:{read:Ft.im,write:!0}})],Bt.prototype,"geometry",void 0),(0,r._)([(0,K.J)(ve)],Bt.prototype,"geometryType",void 0),(0,r._)([(0,T.Cb)({type:String,json:{name:"layerName",write:!0}})],Bt.prototype,"name",void 0),(0,r._)([(0,K.J)(fe,{name:"spatialRel"})],Bt.prototype,"spatialRelationship",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Bt.prototype,"type",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Bt.prototype,"where",void 0),Bt=(0,r._)([(0,S.j)("esri.rest.support.DataLayer")],Bt);const Mt=Bt;var Et;let Lt=Et=class extends Ue.Z{constructor(e){super(e),this.doNotLocateOnRestrictedElements=null}clone(){return new Et({doNotLocateOnRestrictedElements:this.doNotLocateOnRestrictedElements,...this.cloneProperties()})}};(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Lt.prototype,"doNotLocateOnRestrictedElements",void 0),Lt=Et=(0,r._)([(0,S.j)("esri.rest.support.NetworkFeatureSet")],Lt);const Rt=Lt;let Pt=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.doNotLocateOnRestrictedElements=null,this.url=null}};(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],Pt.prototype,"doNotLocateOnRestrictedElements",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],Pt.prototype,"url",void 0),Pt=(0,r._)([(0,S.j)("esri.rest.support.NetworkUrl")],Pt);const Jt=Pt;var Ut;function Wt(e,t,i){(0,m.pC)(e)&&(t[i]=p.Z.isCollection(e)?{features:e.toArray().map((e=>e.toJSON()))}:e.toJSON())}let kt=Ut=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.accumulateAttributes=null,this.apiKey=null,this.attributeParameterValues=null,this.directionsLanguage=null,this.directionsLengthUnits=null,this.directionsOutputType=null,this.directionsStyleName=null,this.directionsTimeAttribute=null,this.findBestSequence=null,this.geometryPrecision=null,this.geometryPrecisionM=null,this.geometryPrecisionZ=null,this.ignoreInvalidLocations=null,this.impedanceAttribute=null,this.outputGeometryPrecision=null,this.outputGeometryPrecisionUnits=null,this.outputLines="true-shape",this.outSpatialReference=null,this.overrides=null,this.pointBarriers=null,this.polygonBarriers=null,this.polylineBarriers=null,this.preserveFirstStop=null,this.preserveLastStop=null,this.preserveObjectID=null,this.restrictionAttributes=null,this.restrictUTurns=null,this.returnBarriers=!1,this.returnDirections=!1,this.returnPolygonBarriers=!1,this.returnPolylineBarriers=!1,this.returnRoutes=!0,this.returnStops=!1,this.returnTraversedEdges=null,this.returnTraversedJunctions=null,this.returnTraversedTurns=null,this.returnZ=!0,this.startTime=null,this.startTimeIsUTC=!0,this.stops=null,this.timeWindowsAreUTC=null,this.travelMode=null,this.useHierarchy=null,this.useTimeWindows=null}static from(e){return(0,C.TJ)(Ut,e)}readAccumulateAttributes(e){return(0,m.Wi)(e)?null:e.map((e=>be.fromJSON(e)))}writeAccumulateAttributes(e,t,i){!(0,m.Wi)(e)&&e.length&&(t[i]=e.map((e=>be.toJSON(e))))}writePointBarriers(e,t,i){Wt(e,t,i)}writePolygonBarrier(e,t,i){Wt(e,t,i)}writePolylineBarrier(e,t,i){Wt(e,t,i)}readRestrictionAttributes(e){return(0,m.Wi)(e)?null:e.map((e=>he.fromJSON(e)))}writeRestrictionAttributes(e,t,i){!(0,m.Wi)(e)&&e.length&&(t[i]=e.map((e=>he.toJSON(e))))}readStartTime(e,t){const{startTime:i}=t;return(0,m.Wi)(i)?null:"now"===i?"now":new Date(i)}writeStartTime(e,t){(0,m.Wi)(e)||(t.startTime="now"===e?"now":e.getTime())}readStops(e,t){return function(e){return function(e){return e&&"type"in e}(e)?Mt.fromJSON(e):function(e){return e&&"url"in e}(e)?Jt.fromJSON(e):function(e){return e&&"features"in e&&"doNotLocateOnRestrictedElements"in e}(e)?Rt.fromJSON(e):function(e){return e&&"features"in e}(e)?Ue.Z.fromJSON(e):null}(t.stops)}writeStops(e,t,i){Wt(e,t,i)}};(0,r._)([(0,T.Cb)({type:[String],json:{name:"accumulateAttributeNames",write:!0}})],kt.prototype,"accumulateAttributes",void 0),(0,r._)([(0,w.r)("accumulateAttributes")],kt.prototype,"readAccumulateAttributes",null),(0,r._)([(0,_.c)("accumulateAttributes")],kt.prototype,"writeAccumulateAttributes",null),(0,r._)([(0,T.Cb)(jt)],kt.prototype,"apiKey",void 0),(0,r._)([(0,T.Cb)({json:{write:!0}})],kt.prototype,"attributeParameterValues",void 0),(0,r._)([(0,T.Cb)({type:String,json:{write:!0}})],kt.prototype,"directionsLanguage",void 0),(0,r._)([(0,K.J)($)],kt.prototype,"directionsLengthUnits",void 0),(0,r._)([(0,K.J)(ee)],kt.prototype,"directionsOutputType",void 0),(0,r._)([(0,K.J)(ce)],kt.prototype,"directionsStyleName",void 0),(0,r._)([(0,K.J)(ye,{name:"directionsTimeAttributeName",ignoreUnknown:!1})],kt.prototype,"directionsTimeAttribute",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"findBestSequence",void 0),(0,r._)([(0,T.Cb)({type:Number,json:{write:!0}})],kt.prototype,"geometryPrecision",void 0),(0,r._)([(0,T.Cb)({type:Number,json:{write:!0}})],kt.prototype,"geometryPrecisionM",void 0),(0,r._)([(0,T.Cb)({type:Number,json:{write:!0}})],kt.prototype,"geometryPrecisionZ",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"ignoreInvalidLocations",void 0),(0,r._)([(0,K.J)(be,{name:"impedanceAttributeName",ignoreUnknown:!1})],kt.prototype,"impedanceAttribute",void 0),(0,r._)([(0,T.Cb)({type:Number,json:{write:!0}})],kt.prototype,"outputGeometryPrecision",void 0),(0,r._)([(0,K.J)(Y)],kt.prototype,"outputGeometryPrecisionUnits",void 0),(0,r._)([(0,K.J)(te)],kt.prototype,"outputLines",void 0),(0,r._)([(0,T.Cb)({type:tt.Z,json:{name:"outSR",write:!0}})],kt.prototype,"outSpatialReference",void 0),(0,r._)([(0,T.Cb)({json:{write:!0}})],kt.prototype,"overrides",void 0),(0,r._)([(0,T.Cb)({json:{name:"barriers",write:!0}})],kt.prototype,"pointBarriers",void 0),(0,r._)([(0,_.c)("pointBarriers")],kt.prototype,"writePointBarriers",null),(0,r._)([(0,T.Cb)({json:{write:!0}})],kt.prototype,"polygonBarriers",void 0),(0,r._)([(0,_.c)("polygonBarriers")],kt.prototype,"writePolygonBarrier",null),(0,r._)([(0,T.Cb)({json:{write:!0}})],kt.prototype,"polylineBarriers",void 0),(0,r._)([(0,_.c)("polylineBarriers")],kt.prototype,"writePolylineBarrier",null),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"preserveFirstStop",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"preserveLastStop",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"preserveObjectID",void 0),(0,r._)([(0,T.Cb)({type:[String],json:{name:"restrictionAttributeNames",write:!0}})],kt.prototype,"restrictionAttributes",void 0),(0,r._)([(0,w.r)("restrictionAttributes")],kt.prototype,"readRestrictionAttributes",null),(0,r._)([(0,_.c)("restrictionAttributes")],kt.prototype,"writeRestrictionAttributes",null),(0,r._)([(0,K.J)(ie)],kt.prototype,"restrictUTurns",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnBarriers",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnDirections",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnPolygonBarriers",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnPolylineBarriers",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnRoutes",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnStops",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnTraversedEdges",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnTraversedJunctions",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnTraversedTurns",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"returnZ",void 0),(0,r._)([(0,T.Cb)({type:Date,json:{type:Number,write:!0}})],kt.prototype,"startTime",void 0),(0,r._)([(0,w.r)("startTime")],kt.prototype,"readStartTime",null),(0,r._)([(0,_.c)("startTime")],kt.prototype,"writeStartTime",null),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"startTimeIsUTC",void 0),(0,r._)([(0,T.Cb)({json:{write:!0}})],kt.prototype,"stops",void 0),(0,r._)([(0,w.r)("stops")],kt.prototype,"readStops",null),(0,r._)([(0,_.c)("stops")],kt.prototype,"writeStops",null),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"timeWindowsAreUTC",void 0),(0,r._)([(0,T.Cb)({type:Ae,json:{write:!0}})],kt.prototype,"travelMode",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"useHierarchy",void 0),(0,r._)([(0,T.Cb)({type:Boolean,json:{write:!0}})],kt.prototype,"useTimeWindows",void 0),kt=Ut=(0,r._)([(0,S.j)("esri.rest.support.RouteParameters")],kt);const Zt=kt;var Vt;let qt=Vt=class extends((0,Ne.J)(L.wq)){constructor(e){super(e),this.arriveCurbApproach=null,this.arriveTime=null,this.arriveTimeOffset=null,this.bearing=null,this.bearingTol=null,this.cumulativeCosts=null,this.cumulativeDistance=null,this.cumulativeDuration=null,this.curbApproach=null,this.departCurbApproach=null,this.departTime=null,this.departTimeOffset=null,this.distanceToNetworkInMeters=null,this.geometry=null,this.lateDuration=null,this.locationType=null,this.name=null,this.navLatency=null,this.objectId=null,this.popupTemplate=null,this.posAlong=null,this.routeName=null,this.serviceCosts=null,this.serviceDistance=null,this.serviceDuration=null,this.sequence=null,this.sideOfEdge=null,this.snapX=null,this.snapY=null,this.snapZ=null,this.sourceId=null,this.sourceOid=null,this.status=null,this.symbol=null,this.timeWindowEnd=null,this.timeWindowEndOffset=null,this.timeWindowStart=null,this.timeWindowStartOffset=null,this.type="stop",this.violations=null,this.waitDuration=null,this.wait=null}readArriveTimeOffset(e,t){return Pe(t.attributes.ArriveTime,t.attributes.ArriveTimeUTC)}readCumulativeCosts(e,t){return Me(t.attributes,"Cumul_")}readDepartTimeOffset(e,t){return Pe(t.attributes.DepartTime,t.attributes.DepartTimeUTC)}readServiceCosts(e,t){return Me(t.attributes,"Attr_")}writeServiceCosts(e,t){Ee(e,t,"Attr_")}writeTimeWindowEnd(e,t){(0,m.Wi)(e)||(t.attributes||(t.attributes={}),t.attributes.TimeWindowEnd=e.getTime())}writeTimeWindowStart(e,t){(0,m.Wi)(e)||(t.attributes||(t.attributes={}),t.attributes.TimeWindowStart=e.getTime())}readViolations(e,t){return Me(t.attributes,"Violation_")}readWait(e,t){return Me(t.attributes,"Wait_")}static fromGraphic(e){return new Vt({arriveCurbApproach:(0,m.pC)(e.attributes.ArrivalCurbApproach)?oe.fromJSON(e.attributes.ArrivalCurbApproach):null,arriveTime:(0,m.pC)(e.attributes.ArrivalTime)?new Date(e.attributes.ArrivalTime):null,arriveTimeOffset:e.attributes.ArrivalUTCOffset,cumulativeCosts:(0,m.pC)(e.attributes.CumulativeCosts)?Le(JSON.parse(e.attributes.CumulativeCosts)):null,cumulativeDistance:e.attributes.CumulativeMeters??null,cumulativeDuration:e.attributes.CumulativeMinutes??null,curbApproach:(0,m.pC)(e.attributes.CurbApproach)?oe.fromJSON(e.attributes.CurbApproach):null,departCurbApproach:(0,m.pC)(e.attributes.DepartureCurbApproach)?oe.fromJSON(e.attributes.DepartureCurbApproach):null,departTime:(0,m.pC)(e.attributes.DepartureTime)?new Date(e.attributes.DepartureTime):null,departTimeOffset:e.attributes.DepartureUTCOffset??null,geometry:e.geometry,lateDuration:e.attributes.LateMinutes??null,locationType:(0,m.pC)(e.attributes.LocationType)?se.fromJSON(e.attributes.LocationType):null,name:e.attributes.Name,objectId:e.attributes.ObjectID??e.attributes.__OBJECTID,popupTemplate:e.popupTemplate,routeName:e.attributes.RouteName,sequence:e.attributes.Sequence??null,serviceCosts:(0,m.pC)(e.attributes.ServiceCosts)?Le(JSON.parse(e.attributes.ServiceCosts)):null,serviceDistance:e.attributes.ServiceMeters??null,serviceDuration:e.attributes.ServiceMinutes??null,status:(0,m.pC)(e.attributes.Status)?ae.fromJSON(e.attributes.Status):null,symbol:e.symbol,timeWindowEnd:(0,m.pC)(e.attributes.TimeWindowEnd)?new Date(e.attributes.TimeWindowEnd):null,timeWindowEndOffset:e.attributes.TimeWindowEndUTCOffset??null,timeWindowStart:(0,m.pC)(e.attributes.TimeWindowStart)?new Date(e.attributes.TimeWindowStart):null,timeWindowStartOffset:e.attributes.TimeWindowStartUTCOffset??null,waitDuration:e.attributes.WaitMinutes??null})}toGraphic(){const e={ObjectID:(0,m.Wg)(this.objectId),ArrivalCurbApproach:(0,m.pC)(this.arriveCurbApproach)?oe.toJSON(this.arriveCurbApproach):null,ArrivalTime:(0,m.pC)(this.arriveTime)?this.arriveTime.getTime():null,ArrivalUTCOffset:this.arriveTimeOffset,CumulativeCosts:(0,m.pC)(this.cumulativeCosts)?JSON.stringify(Re(this.cumulativeCosts)):null,CumulativeMeters:this.cumulativeDistance,CumulativeMinutes:this.cumulativeDuration,CurbApproach:(0,m.pC)(this.curbApproach)?oe.toJSON(this.curbApproach):null,DepartureCurbApproach:(0,m.pC)(this.departCurbApproach)?oe.toJSON(this.departCurbApproach):null,DepartureTime:(0,m.pC)(this.departTime)?this.departTime.getTime():null,DepartureUTCOffset:this.departTimeOffset,LateMinutes:this.lateDuration,LocationType:(0,m.pC)(this.locationType)?se.toJSON(this.locationType):null,Name:(0,m.Wg)(this.name),RouteName:(0,m.Wg)(this.routeName),Sequence:this.sequence,ServiceCosts:(0,m.pC)(this.serviceCosts)?JSON.stringify(Re(this.serviceCosts)):null,ServiceMeters:this.serviceDistance,ServiceMinutes:this.serviceDuration,Status:(0,m.pC)(this.status)?ae.toJSON(this.status):null,TimeWindowEnd:(0,m.pC)(this.timeWindowEnd)?this.timeWindowEnd.getTime():null,TimeWindowEndUTCOffset:this.timeWindowEndOffset??this.arriveTimeOffset,TimeWindowStart:(0,m.pC)(this.timeWindowStart)?this.timeWindowStart.getTime():null,TimeWindowStartUTCOffset:this.timeWindowStartOffset??this.arriveTimeOffset,WaitMinutes:this.waitDuration};return new a.Z({geometry:this.geometry,attributes:e,symbol:this.symbol,popupTemplate:this.popupTemplate})}};qt.fields=[{name:"ObjectID",alias:"ObjectID",type:"esriFieldTypeOID",editable:!1,nullable:!1,domain:null},{name:"ArrivalCurbApproach",alias:"Arrival Curb Approach",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNACurbApproachType",codedValues:[{name:"Either side",code:0},{name:"From the right",code:1},{name:"From the left",code:2},{name:"Depart in the same direction",code:3}]}},{name:"ArrivalTime",alias:"Arrival Time",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!0},{name:"ArrivalUTCOffset",alias:"Arrival Time: Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"CumulativeCosts",alias:"Cumulative Costs",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"CumulativeMeters",alias:"Cumulative Meters",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"CumulativeMinutes",alias:"Cumulative Minutes",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!0},{name:"CurbApproach",alias:"Curb Approach",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!1,domain:{type:"codedValue",name:"esriNACurbApproachType",codedValues:[{name:"Either side",code:0},{name:"From the right",code:1},{name:"From the left",code:2},{name:"Depart in the same direction",code:3}]}},{name:"DepartureCurbApproach",alias:"Departure Curb Approach",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNACurbApproachType",codedValues:[{name:"Either side",code:0},{name:"From the right",code:1},{name:"From the left",code:2},{name:"Depart in the same direction",code:3}]}},{name:"DepartureTime",alias:"Departure Time",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!0},{name:"DepartureUTCOffset",alias:"Departure Time: Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"LateMinutes",alias:"Minutes Late",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!1},{name:"LocationType",alias:"Location Type",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNALocationType",codedValues:[{name:"Stop",code:0},{name:"Waypoint",code:1}]}},{name:"Name",alias:"Name",type:"esriFieldTypeString",length:255,editable:!0,nullable:!0,visible:!0},{name:"RouteName",alias:"Route Name",type:"esriFieldTypeString",length:255,editable:!0,nullable:!0,visible:!0},{name:"Sequence",alias:"Sequence",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"ServiceCosts",alias:"Service Costs",type:"esriFieldTypeString",length:1048576,editable:!0,nullable:!0,visible:!1,domain:null},{name:"ServiceMeters",alias:"Service Meters",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!1},{name:"ServiceMinutes",alias:"Service Minutes",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!1},{name:"Status",alias:"Status",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0,domain:{type:"codedValue",name:"esriNAObjectStatus",codedValues:[{name:"OK",code:0},{name:"Not Located on Network",code:1},{name:"Network Unbuilt",code:2},{name:"Prohibited Street",code:3},{name:"Invalid Field Values",code:4},{name:"Cannot Reach",code:5},{name:"Time Window Violation",code:6}]}},{name:"TimeWindowEnd",alias:"Time Window End",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!1},{name:"TimeWindowEndUTCOffset",alias:"Time Window End: Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"TimeWindowStart",alias:"Time Window Start",type:"esriFieldTypeDate",length:36,editable:!0,nullable:!0,visible:!1},{name:"TimeWindowStartUTCOffset",alias:"Time Window Start: Offset from UTC in Minutes",type:"esriFieldTypeInteger",editable:!0,nullable:!0,visible:!0},{name:"WaitMinutes",alias:"Minutes Early",type:"esriFieldTypeDouble",editable:!0,nullable:!0,visible:!1}],qt.popupInfo={title:"{Name}",fieldInfos:[{fieldName:"Name",label:"Name",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"RouteName",label:"Route Name",isEditable:!0,tooltip:"",visible:!0,stringFieldOption:"textbox"},{fieldName:"Sequence",label:"Sequence",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ArrivalTime",label:"Arrival Time",isEditable:!0,tooltip:"",visible:!0,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"ArrivalUTCOffset",label:"Arrival Time: Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"DepartureTime",label:"Departure Time",isEditable:!0,tooltip:"",visible:!0,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"DepartureUTCOffset",label:"Departure Time: Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"CurbApproach",label:"Curb Approach",isEditable:!0,tooltip:"",visible:!1,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ArrivalCurbApproach",label:"Arrival Curb Approach",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"DepartureCurbApproach",label:"Departure Curb Approach",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"Status",label:"Status",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"LocationType",label:"Location Type",isEditable:!1,tooltip:"",visible:!0,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"TimeWindowStart",label:"Time Window Start",isEditable:!0,tooltip:"",visible:!1,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"TimeWindowStartUTCOffset",label:"Time Window Start: Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!1,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"TimeWindowEnd",label:"Time Window End",isEditable:!0,tooltip:"",visible:!1,format:{dateFormat:"shortDateShortTime24"},stringFieldOption:"textbox"},{fieldName:"TimeWindowEndUTCOffset",label:"Time Window End: Offset from UTC in Minutes",isEditable:!1,tooltip:"",visible:!1,format:{places:0,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ServiceMinutes",label:"Service Minutes",isEditable:!0,tooltip:"",visible:!1,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ServiceMeters",label:"Service Meters",isEditable:!0,tooltip:"",visible:!1,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"ServiceCosts",label:"Service Costs",isEditable:!0,tooltip:"",visible:!1,stringFieldOption:"textbox"},{fieldName:"CumulativeMinutes",label:"Cumulative Minutes",isEditable:!1,tooltip:"",visible:!0,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"CumulativeMeters",label:"Cumulative Meters",isEditable:!1,tooltip:"",visible:!0,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"CumulativeCosts",label:"Cumulative Costs",isEditable:!0,tooltip:"",visible:!1,stringFieldOption:"textbox"},{fieldName:"LateMinutes",label:"Minutes Late",isEditable:!1,tooltip:"",visible:!1,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"},{fieldName:"WaitMinutes",label:"Minutes Early",isEditable:!1,tooltip:"",visible:!1,format:{places:2,digitSeparator:!0},stringFieldOption:"textbox"}],description:null,showAttachments:!1,mediaInfos:[]},(0,r._)([(0,T.Cb)({type:oe.apiValues,json:{read:{source:"attributes.ArrivalCurbApproach",reader:oe.read}}})],qt.prototype,"arriveCurbApproach",void 0),(0,r._)([(0,T.Cb)({type:Date,json:{read:{source:"attributes.ArriveTimeUTC"}}})],qt.prototype,"arriveTime",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"arriveTimeOffset",void 0),(0,r._)([(0,w.r)("arriveTimeOffset",["attributes.ArriveTime","attributes.ArriveTimeUTC"])],qt.prototype,"readArriveTimeOffset",null),(0,r._)([(0,T.Cb)({json:{name:"attributes.Bearing",read:!1,write:!0}})],qt.prototype,"bearing",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.BearingTol",read:!1,write:!0}})],qt.prototype,"bearingTol",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"cumulativeCosts",void 0),(0,r._)([(0,w.r)("cumulativeCosts",["attributes"])],qt.prototype,"readCumulativeCosts",null),(0,r._)([(0,T.Cb)()],qt.prototype,"cumulativeDistance",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"cumulativeDuration",void 0),(0,r._)([(0,T.Cb)({type:oe.apiValues,json:{name:"attributes.CurbApproach",read:{reader:oe.read},write:{writer:oe.write}}})],qt.prototype,"curbApproach",void 0),(0,r._)([(0,T.Cb)({type:oe.apiValues,json:{read:{source:"attributes.DepartCurbApproach",reader:oe.read}}})],qt.prototype,"departCurbApproach",void 0),(0,r._)([(0,T.Cb)({type:Date,json:{read:{source:"attributes.DepartTimeUTC"}}})],qt.prototype,"departTime",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"departTimeOffset",void 0),(0,r._)([(0,w.r)("departTimeOffset",["attributes.DepartTime","attributes.DepartTimeUTC"])],qt.prototype,"readDepartTimeOffset",null),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.DistanceToNetworkInMeters"}}})],qt.prototype,"distanceToNetworkInMeters",void 0),(0,r._)([(0,T.Cb)({type:Ke.Z,json:{write:!0}})],qt.prototype,"geometry",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"lateDuration",void 0),(0,r._)([(0,T.Cb)({type:se.apiValues,json:{name:"attributes.LocationType",read:{reader:se.read},write:{writer:se.write}}})],qt.prototype,"locationType",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.Name"}})],qt.prototype,"name",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.NavLatency",read:!1,write:!0}})],qt.prototype,"navLatency",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.ObjectID"}})],qt.prototype,"objectId",void 0),(0,r._)([(0,T.Cb)({type:n.Z})],qt.prototype,"popupTemplate",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.PosAlong"}}})],qt.prototype,"posAlong",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.RouteName"}})],qt.prototype,"routeName",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"serviceCosts",void 0),(0,r._)([(0,w.r)("serviceCosts",["attributes"])],qt.prototype,"readServiceCosts",null),(0,r._)([(0,_.c)("serviceCosts")],qt.prototype,"writeServiceCosts",null),(0,r._)([(0,T.Cb)()],qt.prototype,"serviceDistance",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"serviceDuration",void 0),(0,r._)([(0,T.Cb)({json:{name:"attributes.Sequence"}})],qt.prototype,"sequence",void 0),(0,r._)([(0,T.Cb)({type:ne.apiValues,json:{read:{source:"attributes.SideOfEdge",reader:ne.read}}})],qt.prototype,"sideOfEdge",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SnapX"}}})],qt.prototype,"snapX",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SnapY"}}})],qt.prototype,"snapY",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SnapZ"}}})],qt.prototype,"snapZ",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SourceID"}}})],qt.prototype,"sourceId",void 0),(0,r._)([(0,T.Cb)({json:{read:{source:"attributes.SourceOID"}}})],qt.prototype,"sourceOid",void 0),(0,r._)([(0,T.Cb)({type:ae.apiValues,json:{read:{source:"attributes.Status",reader:ae.read}}})],qt.prototype,"status",void 0),(0,r._)([(0,T.Cb)({types:E.LB})],qt.prototype,"symbol",void 0),(0,r._)([(0,T.Cb)({type:Date,json:{name:"attributes.TimeWindowEnd"}})],qt.prototype,"timeWindowEnd",void 0),(0,r._)([(0,_.c)("timeWindowEnd")],qt.prototype,"writeTimeWindowEnd",null),(0,r._)([(0,T.Cb)()],qt.prototype,"timeWindowEndOffset",void 0),(0,r._)([(0,T.Cb)({type:Date,json:{name:"attributes.TimeWindowStart"}})],qt.prototype,"timeWindowStart",void 0),(0,r._)([(0,_.c)("timeWindowStart")],qt.prototype,"writeTimeWindowStart",null),(0,r._)([(0,T.Cb)()],qt.prototype,"timeWindowStartOffset",void 0),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],qt.prototype,"type",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"violations",void 0),(0,r._)([(0,w.r)("violations",["attributes"])],qt.prototype,"readViolations",null),(0,r._)([(0,T.Cb)()],qt.prototype,"waitDuration",void 0),(0,r._)([(0,T.Cb)()],qt.prototype,"wait",void 0),(0,r._)([(0,w.r)("wait",["attributes"])],qt.prototype,"readWait",null),qt=Vt=(0,r._)([(0,S.j)("esri.rest.support.Stop")],qt);const Gt=qt;var zt=i(65091);function Ht(e){return e.length?e:null}function Kt(e){switch(e){case"esriGeometryPoint":return{type:"esriSMS",style:"esriSMSCircle",size:12,color:[0,0,0,0],outline:Kt("esriGeometryPolyline")};case"esriGeometryPolyline":return{type:"esriSLS",style:"esriSLSSolid",width:1,color:[0,0,0,0]};case"esriGeometryPolygon":return{type:"esriSFS",style:"esriSFSNull",outline:Kt("esriGeometryPolyline")}}}function Xt(e){return"layers"in e}async function Yt(e){const t=tt.Z.WGS84;return await(0,A.iQ)(e.spatialReference,t),(0,A.iV)(e,t)}function $t(e,t){switch(t){case"seconds":return e/60;case"hours":return 60*e;case"days":return 60*e*24;default:return e}}function Qt(e,t){return"decimal-degrees"===t||"points"===t||"unknown"===t?e:(0,v.En)(e,t,"meters")}const ei=p.Z.ofType(ct),ti=p.Z.ofType(bt),ii=p.Z.ofType(vt),ri=p.Z.ofType(wt),oi=p.Z.ofType(Nt),si=p.Z.ofType(Gt),ai="esri.layers.RouteLayer",ni=c.Z.getLogger(ai);let li=class extends((0,x.h)((0,B.M)((0,j.q)((0,F.I)((0,y.R)((0,d.p)(I.Z))))))){constructor(e){super(e),this._cachedServiceDescription=null,this._featureCollection=null,this._type="Feature Collection",this.defaultSymbols=new Z,this.directionLines=null,this.directionPoints=null,this.featureCollectionType="route",this.legendEnabled=!1,this.maxScale=0,this.minScale=0,this.pointBarriers=new ii,this.polygonBarriers=new ri,this.polylineBarriers=new oi,this.routeInfo=null,this.spatialReference=tt.Z.WGS84,this.stops=new si,this.type="route";const t=()=>{this._setStopSymbol(this.stops)};this.addHandles((0,f.on)((()=>this.stops),"change",t,{sync:!0,onListenerAdd:t}))}writeFeatureCollectionWebmap(e,t,i,r){const o=[this._writePolygonBarriers(),this._writePolylineBarriers(),this._writePointBarriers(),this._writeRouteInfo(),this._writeDirectionLines(),this._writeDirectionPoints(),this._writeStops()].filter((e=>!!e)),s=o.map(((e,t)=>t)),a="web-map"===r.origin?"featureCollection.layers":"layers";(0,b.RB)(a,o,t),t.opacity=this.opacity,t.visibility=this.visible,t.visibleLayers=s}readDirectionLines(e,t){return this._getNetworkFeatures(t,"DirectionLines",(e=>ct.fromGraphic(e)))}readDirectionPoints(e,t){return this._getNetworkFeatures(t,"DirectionPoints",(e=>bt.fromGraphic(e)))}get fullExtent(){const e=new O.Z({xmin:-180,ymin:-90,xmax:180,ymax:90,spatialReference:tt.Z.WGS84});if((0,m.pC)(this.routeInfo)&&(0,m.pC)(this.routeInfo.geometry))return this.routeInfo.geometry.extent??e;if((0,m.Wi)(this.stops))return e;const t=this.stops.filter((e=>(0,m.pC)(e.geometry)));if(t.length<2)return e;const{spatialReference:i}=t.getItemAt(0).geometry;if((0,m.Wi)(i))return e;const r=t.toArray().map((e=>{const t=e.geometry;return[t.x,t.y]}));return new zt.Z({points:r,spatialReference:i}).extent}readMaxScale(e,t){const i=(Xt(t)?t.layers:t.featureCollection?.layers)?.find((e=>(0,m.pC)(e.layerDefinition.maxScale)));return i?.layerDefinition.maxScale??0}readMinScale(e,t){const i=(Xt(t)?t.layers:t.featureCollection?.layers)?.find((e=>(0,m.pC)(e.layerDefinition.minScale)));return i?.layerDefinition.minScale??0}readPointBarriers(e,t){return this._getNetworkFeatures(t,"Barriers",(e=>vt.fromGraphic(e)))}readPolygonBarriers(e,t){return this._getNetworkFeatures(t,"PolygonBarriers",(e=>wt.fromGraphic(e)))}readPolylineBarriers(e,t){return this._getNetworkFeatures(t,"PolylineBarriers",(e=>Nt.fromGraphic(e)))}readRouteInfo(e,t){const i=this._getNetworkFeatures(t,"RouteInfo",(e=>xt.fromGraphic(e)));return i.length>0?i.getItemAt(0):null}readSpatialReference(e,t){const i=Xt(t)?t.layers:t.featureCollection?.layers;if(!i?.length)return tt.Z.WGS84;const{layerDefinition:r,featureSet:o}=i[0],s=o.features[0],a=(0,m.Wg)(s?.geometry)?.spatialReference??o.spatialReference??r.spatialReference??r.extent.spatialReference??D.Zn;return tt.Z.fromJSON(a)}readStops(e,t){return this._getNetworkFeatures(t,"Stops",(e=>Gt.fromGraphic(e)),(e=>this._setStopSymbol(e)))}get title(){return(0,m.pC)(this.routeInfo)&&(0,m.pC)(this.routeInfo.name)?this.routeInfo.name:"Route"}set title(e){this._overrideIfSome("title",e)}get url(){return o.Z.routeServiceUrl}set url(e){null!=e?this._set("url",(0,M.Nm)(e,ni)):this._set("url",o.Z.routeServiceUrl)}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Feature Collection"]},e)),Promise.resolve(this)}removeAll(){this.removeResult(),this.pointBarriers.removeAll(),this.polygonBarriers.removeAll(),this.polylineBarriers.removeAll(),this.stops.removeAll()}removeResult(){(0,m.pC)(this.directionLines)&&(this.directionLines.removeAll(),this._set("directionLines",null)),(0,m.pC)(this.directionPoints)&&(this.directionPoints.removeAll(),this._set("directionPoints",null)),(0,m.pC)(this.routeInfo)&&this._set("routeInfo",null)}async save(){await this.load();const{fullExtent:e,portalItem:t}=this;if(!t)throw new u.Z("routelayer:portal-item-not-set","save() requires to the layer to have a portal item");if(!t.id)throw new u.Z("routelayer:portal-item-not-saved","Please use saveAs() first to save the routelayer");if("Feature Collection"!==t.type)throw new u.Z("routelayer:portal-item-wrong-type",'Portal item needs to have type "Feature Collection"');if((0,m.Wi)(this.routeInfo))throw new u.Z("routelayer:route-unsolved","save() requires a solved route");const{portal:i}=t;await i.signIn(),i.user||await t.reload();const{itemUrl:r,itemControl:o}=t;if("admin"!==o&&"update"!==o)throw new u.Z("routelayer:insufficient-permissions","To save this layer, you need to be the owner or an administrator of your organization");const s={messages:[],origin:"portal-item",portal:i,url:r?(0,g.mN)(r):void 0,writtenProperties:[]},a=this.write(void 0,s);return t.extent=await Yt(e),t.title=this.title,await t.update({data:a}),t}async saveAs(e,t={}){if(await this.load(),(0,m.Wi)(this.routeInfo))throw new u.Z("routelayer:route-unsolved","saveAs() requires a solved route");const i=q.default.from(e).clone();i.extent??(i.extent=await Yt(this.fullExtent)),i.id=null,i.portal??(i.portal=V.Z.getDefault()),i.title??(i.title=this.title),i.type="Feature Collection",i.typeKeywords=["Data","Feature Collection",G.Kz.MULTI_LAYER,"Route Layer"];const{portal:r}=i,o={messages:[],origin:"portal-item",portal:r,url:null,writtenProperties:[]};await r.signIn();const s=t?.folder,a=this.write(void 0,o);return await(r.user?.addItem({item:i,folder:s,data:a})),this.portalItem=i,(0,N.D)(o),o.portalItem=i,i}async solve(e,t){const i=e?.stops??this.stops,r=e?.pointBarriers??Ht(this.pointBarriers),o=e?.polylineBarriers??Ht(this.polylineBarriers),s=e?.polygonBarriers??Ht(this.polygonBarriers);if((0,m.Wi)(i))throw new u.Z("routelayer:undefined-stops","the route layer must have stops defined in the route parameters.");if((function(e){return"esri.rest.support.FeatureSet"===e.declaredClass}(i)||function(e){return"esri.rest.support.NetworkFeatureSet"===e.declaredClass}(i))&&i.features.length<2||p.Z.isCollection(i)&&i.length<2)throw new u.Z("routelayer:insufficent-stops","the route layer must have two or more stops to solve a route.");if(p.Z.isCollection(i))for(const e of i)e.routeName=null;const a=e?.apiKey,n=this.url,l=await this._getServiceDescription(n,a,t),d=e?.travelMode??l.defaultTravelMode,c=(0,m.Wg)(e?.accumulateAttributes)??[];(0,m.pC)(d)&&(c.push(d.distanceAttributeName),d.timeAttributeName&&c.push(d.timeAttributeName));const y={startTime:new Date},b={accumulateAttributes:c,directionsOutputType:"featuresets",ignoreInvalidLocations:!0,pointBarriers:r,polylineBarriers:o,polygonBarriers:s,preserveFirstStop:!0,preserveLastStop:!0,returnBarriers:!!r,returnDirections:!0,returnPolygonBarriers:!!s,returnPolylineBarriers:!!o,returnRoutes:!0,returnStops:!0,stops:i},f=e?Zt.from(e):new Zt;for(const e in y)null==f[e]&&(f[e]=y[e]);let v;f.set(b);try{v=await async function(e,t,i){const r=[],o=[],s={},a={},n=(0,H.en)(e),{path:l}=n;pt(t.stops)&&je(t.stops.features,o,"stops.features",s),pt(t.pointBarriers)&&je(t.pointBarriers.features,o,"pointBarriers.features",s),pt(t.polylineBarriers)&&je(t.polylineBarriers.features,o,"polylineBarriers.features",s),pt(t.polygonBarriers)&&je(t.polygonBarriers.features,o,"polygonBarriers.features",s);const p=await(0,Be.aX)(o);for(const e in s){const t=s[e];r.push(e),a[e]=p.slice(t[0],t[1])}if(function(e,t){for(let i=0;i<t.length;i++){const r=e[t[i]];if(r&&r.length)for(const e of r)if((0,m.pC)(e)&&e.hasZ)return!0}return!1}(a,r)){let e=null;try{e=await Fe(l,t.apiKey,i)}catch{}e&&!e.hasZ&&function(e,t){for(let i=0;i<t.length;i++){const r=e[t[i]];if(r&&r.length)for(const e of r)e.z=void 0}xe.warnOnce("The remote Network Analysis service is powered by a network dataset which is not Z-aware.\nZ-coordinates of the input geometry are ignored.")}(a,r)}for(const e in a)a[e].forEach(((i,r)=>{t.get(e)[r].geometry=i}));const u={...i,query:{...n.query,...Je(t),f:"json"}},d=l.endsWith("/solve")?l:`${l}/solve`,{data:c}=await(0,z.default)(d,u);return function(e){const{barriers:t,directionLines:i,directionPoints:r,directions:o,messages:s,polygonBarriers:a,polylineBarriers:n,routes:l,stops:p,traversedEdges:u,traversedJunctions:d,traversedTurns:c}=e,y=e=>{const t=h.find((t=>t.routeName===e));if((0,m.pC)(t))return t;const i={routeId:h.length+1,routeName:e};return h.push(i),i},b=e=>{const t=h.find((t=>t.routeId===e));if((0,m.pC)(t))return t;const i={routeId:e,routeName:null};return h.push(i),i},h=[];l?.features.forEach(((e,t)=>{e.geometry.spatialReference=l.spatialReference;const i=e.attributes.Name,r=t+1;h.push({routeId:r,routeName:i,route:e})})),o?.forEach((e=>{const{routeName:t}=e;y(t).directions=e}));const f=p?.features.every((e=>(0,m.Wi)(e.attributes.RouteName)))&&h.length>0?h[0].routeName:null;return p?.features.forEach((e=>{var t;e.geometry&&((t=e.geometry).spatialReference??(t.spatialReference=p.spatialReference));const i=f??e.attributes.RouteName,r=y(i);r.stops??(r.stops=[]),r.stops.push(e)})),i?.features.forEach((e=>{const t=e.attributes.RouteID,r=b(t),{geometryType:o,spatialReference:s}=i;r.directionLines??(r.directionLines={features:[],geometryType:o,spatialReference:s}),r.directionLines.features.push(e)})),r?.features.forEach((e=>{const t=e.attributes.RouteID,i=b(t),{geometryType:o,spatialReference:s}=r;i.directionPoints??(i.directionPoints={features:[],geometryType:o,spatialReference:s}),i.directionPoints.features.push(e)})),u?.features.forEach((e=>{const t=e.attributes.RouteID,i=b(t),{geometryType:r,spatialReference:o}=u;i.traversedEdges??(i.traversedEdges={features:[],geometryType:r,spatialReference:o}),i.traversedEdges.features.push(e)})),d?.features.forEach((e=>{const t=e.attributes.RouteID,i=b(t),{geometryType:r,spatialReference:o}=d;i.traversedJunctions??(i.traversedJunctions={features:[],geometryType:r,spatialReference:o}),i.traversedJunctions.features.push(e)})),c?.features.forEach((e=>{const t=e.attributes.RouteID,i=b(t);i.traversedTurns??(i.traversedTurns={features:[]}),i.traversedTurns.features.push(e)})),lt.fromJSON({routeResults:h,barriers:t,polygonBarriers:a,polylineBarriers:n,messages:s})}(c)}(n,f,t)}catch(e){throw(0,h.D_)(e)?e:new u.Z("routelayer:failed-route-request","the routing request failed",{error:e})}const g=this._toRouteLayerSolution(v);return this._isOverridden("title")||(this.title=(0,m.yl)(g.routeInfo.name,"Route")),function(e,t,i){const r=t.networkDataset?.networkAttributes,o=r?.filter((({usageType:e})=>"cost"===e))??[],s=i.travelMode??t.defaultTravelMode;if((0,m.Wi)(s))return void ni.warn("route-layer:missing-travel-mode","The routing service must have a default travel mode or one must be specified in the route parameter.");const{timeAttributeName:a,distanceAttributeName:n}=s,l=o.find((({name:e})=>e===a)),p=o.find((({name:e})=>e===n)),d=(0,m.Wg)(i.travelMode)?.impedanceAttributeName??(0,m.Wg)(i.impedanceAttribute)??t.impedance,c=l?.units,y=p?.units;if(!c||!y)throw new u.Z("routelayer:unknown-impedance-units","the units of either the distance or time impedance are unknown");const b=i.directionsLanguage??t.directionsLanguage,h=(0,m.Wg)(i.accumulateAttributes)??t.accumulateAttributeNames??[],f=new Set(o.filter((({name:e})=>e===a||e===n||e===d||null!=e&&h.includes(e))).map((({name:e})=>e))),v=e=>{for(const t in e)f.has(t)||delete e[t]};for(const t of e.pointBarriers)(0,m.pC)(t.costs)&&(t.addedCost=t.costs[d]??0,v(t.costs));for(const t of e.polygonBarriers)(0,m.pC)(t.costs)&&(t.scaleFactor=t.costs[d]??1,v(t.costs));for(const t of e.polylineBarriers)(0,m.pC)(t.costs)&&(t.scaleFactor=t.costs[d]??1,v(t.costs));const{routeInfo:g}=e,{findBestSequence:T,preserveFirstStop:C,preserveLastStop:w,startTimeIsUTC:S,timeWindowsAreUTC:_}=i;g.analysisSettings=new At({accumulateAttributes:h,directionsLanguage:b,findBestSequence:T,preserveFirstStop:C,preserveLastStop:w,startTimeIsUTC:S,timeWindowsAreUTC:_,travelMode:s}),g.totalDuration=$t(g.totalCosts?.[a]??0,c),g.totalDistance=Qt(g.totalCosts?.[n]??0,y),g.totalLateDuration=$t(g.totalViolations?.[a]??0,c),g.totalWaitDuration=$t(g.totalWait?.[a]??0,c),(0,m.pC)(g.totalCosts)&&v(g.totalCosts),(0,m.pC)(g.totalViolations)&&v(g.totalViolations),(0,m.pC)(g.totalWait)&&v(g.totalWait);for(const t of e.stops)(0,m.pC)(t.serviceCosts)&&(t.serviceDuration=$t(t.serviceCosts[a]??0,c),t.serviceDistance=Qt(t.serviceCosts[n]??0,y),v(t.serviceCosts)),(0,m.pC)(t.cumulativeCosts)&&(t.cumulativeDuration=$t(t.cumulativeCosts[a]??0,c),t.cumulativeDistance=Qt(t.cumulativeCosts[n]??0,y),v(t.cumulativeCosts)),(0,m.pC)(t.violations)&&(t.lateDuration=$t(t.violations[a]??0,c),v(t.violations)),(0,m.pC)(t.wait)&&(t.waitDuration=$t(t.wait[a]??0,c),v(t.wait))}(g,l,f),g}update(e){const{stops:t,directionLines:i,directionPoints:r,pointBarriers:o,polylineBarriers:s,polygonBarriers:a,routeInfo:n}=e;this.set({stops:t,pointBarriers:o,polylineBarriers:s,polygonBarriers:a}),this._set("directionLines",i),this._set("directionPoints",r),this._set("routeInfo",n),(0,m.pC)(n.geometry)&&(this.spatialReference=n.geometry.spatialReference)}_getNetworkFeatures(e,t,i,r){const o=(Xt(e)?e.layers:e.featureCollection?.layers)?.find((e=>e.layerDefinition.name===t));if((0,m.Wi)(o))return new p.Z;const{layerDefinition:s,popupInfo:u,featureSet:d}=o,c=s.drawingInfo.renderer,{features:y}=d,b=d.spatialReference??s.spatialReference??s.extent.spatialReference??D.Zn,h=c&&(0,l.a)(c),f=tt.Z.fromJSON(b),v=y.map((e=>{const r=a.Z.fromJSON(e);(0,m.pC)(r.geometry)&&(0,m.pC)(e.geometry)&&(0,m.Wi)(e.geometry.spatialReference)&&(r.geometry.spatialReference=f);const o=i(r);return o.symbol??(o.symbol=h?.getSymbol(r)??this._getNetworkSymbol(t)),o.popupTemplate??(o.popupTemplate=u&&n.Z.fromJSON(u)),o}));return r&&v.some((e=>!e.symbol))&&r(v),new p.Z(v)}_getNetworkSymbol(e){switch(e){case"Barriers":return this.defaultSymbols.pointBarriers;case"DirectionPoints":return this.defaultSymbols.directionPoints;case"DirectionLines":return this.defaultSymbols.directionLines;case"PolylineBarriers":return this.defaultSymbols.polylineBarriers;case"PolygonBarriers":return this.defaultSymbols.polygonBarriers;case"RouteInfo":return this.defaultSymbols.routeInfo;case"Stops":return null}}async _getServiceDescription(e,t,i){if((0,m.pC)(this._cachedServiceDescription)&&this._cachedServiceDescription.url===e)return this._cachedServiceDescription.serviceDescription;const r=await Fe(e,t,i);return this._cachedServiceDescription={serviceDescription:r,url:e},r}_setStopSymbol(e){if(!e||0===e.length)return;if((0,m.Wi)(this.defaultSymbols.stops))return;if(e.every((e=>(0,m.pC)(e.symbol))))return;const{first:t,last:i,middle:r,unlocated:o,waypoint:s,break:a}=this.defaultSymbols.stops;if((0,m.Wi)(this.routeInfo)||1===e.length)return void e.forEach(((o,s)=>{switch(s){case 0:o.symbol=t;break;case e.length-1:o.symbol=i;break;default:o.symbol=r}}));const n=e.map((e=>e.sequence)).filter((e=>(0,m.pC)(e))),l=Math.min(...n),p=Math.max(...n);for(const n of e)n.sequence!==l?n.sequence!==p?"ok"===n.status||"not-located-on-closest"===n.status?"waypoint"!==n.locationType?"break"!==n.locationType?n.symbol=r:n.symbol=a:n.symbol=s:n.symbol=o:n.symbol=i:n.symbol=t}_toRouteLayerSolution(e){const t=e.routeResults[0].stops?.map((e=>Gt.fromJSON(e.toJSON())));this._setStopSymbol(t);const i=new si(t),r=new ri(e.polygonBarriers?.map((e=>{const t=wt.fromJSON(e.toJSON());return t.symbol=this.defaultSymbols.polygonBarriers,t}))),o=new oi(e.polylineBarriers?.map((e=>{const t=Nt.fromJSON(e.toJSON());return t.symbol=this.defaultSymbols.polylineBarriers,t}))),s=new ii(e.pointBarriers?.map((e=>{const t=vt.fromJSON(e.toJSON());return t.symbol=this.defaultSymbols.pointBarriers,t}))),a=e.routeResults[0].route?.toJSON(),n=xt.fromJSON(a);n.symbol=this.defaultSymbols.routeInfo;const l=new ti(e.routeResults[0].directionPoints?.features.map((e=>{const t=bt.fromJSON(e.toJSON());return t.symbol=this.defaultSymbols.directionPoints,t})));return{directionLines:new ei(e.routeResults[0].directionLines?.features.map((e=>{const t=ct.fromJSON(e.toJSON());return t.symbol=this.defaultSymbols.directionLines,t}))),directionPoints:l,pointBarriers:s,polygonBarriers:r,polylineBarriers:o,routeInfo:n,stops:i}}_writeDirectionLines(){return this._writeNetworkFeatures(this.directionLines,this.defaultSymbols.directionLines,"esriGeometryPolyline",ct.fields,ct.popupInfo,"DirectionLines","Direction Lines")}_writeDirectionPoints(){return this._writeNetworkFeatures(this.directionPoints,this.defaultSymbols.directionPoints,"esriGeometryPoint",bt.fields,bt.popupInfo,"DirectionPoints","Direction Points")}_writeNetworkFeatures(e,t,i,r,o,s,a){if((0,m.Wi)(e)||!e.length)return null;const n=this.spatialReference.toJSON(),{fullExtent:l,maxScale:p,minScale:u}=this;return{featureSet:{features:e.toArray().map((e=>function(e){const{attributes:t,geometry:i,popupTemplate:r,symbol:o}=e.toGraphic().toJSON();return{attributes:t,geometry:i,popupInfo:r,symbol:o}}(e))),geometryType:i,spatialReference:n},layerDefinition:{capabilities:"Query,Update,Editing",drawingInfo:{renderer:{type:"simple",symbol:(0,m.pC)(t)?t.toJSON():Kt(i)}},extent:l.toJSON(),fields:r,geometryType:i,hasM:!1,hasZ:!1,maxScale:p,minScale:u,name:s,objectIdField:"ObjectID",spatialReference:n,title:a,type:"Feature Layer",typeIdField:""},popupInfo:o}}_writePointBarriers(){return this._writeNetworkFeatures(this.pointBarriers,this.defaultSymbols.pointBarriers,"esriGeometryPoint",vt.fields,vt.popupInfo,"Barriers","Point Barriers")}_writePolygonBarriers(){return this._writeNetworkFeatures(this.polygonBarriers,this.defaultSymbols.polygonBarriers,"esriGeometryPolygon",wt.fields,wt.popupInfo,"PolygonBarriers","Polygon Barriers")}_writePolylineBarriers(){return this._writeNetworkFeatures(this.polylineBarriers,this.defaultSymbols.polylineBarriers,"esriGeometryPolyline",Nt.fields,Nt.popupInfo,"PolylineBarriers","Line Barriers")}_writeRouteInfo(){return this._writeNetworkFeatures((0,m.pC)(this.routeInfo)?new p.Z([this.routeInfo]):null,this.defaultSymbols.routeInfo,"esriGeometryPolyline",xt.fields,xt.popupInfo,"RouteInfo","Route Details")}_writeStops(){const e=this._writeNetworkFeatures(this.stops,null,"esriGeometryPoint",Gt.fields,Gt.popupInfo,"Stops","Stops");if((0,m.Wi)(e))return null;const{stops:t}=this.defaultSymbols,i=(0,m.pC)(t)&&(0,m.pC)(t.first)&&t.first.toJSON(),r=(0,m.pC)(t)&&(0,m.pC)(t.middle)&&t.middle.toJSON(),o=(0,m.pC)(t)&&(0,m.pC)(t.last)&&t.last.toJSON();return e.layerDefinition.drawingInfo.renderer={type:"uniqueValue",field1:"Sequence",defaultSymbol:r,uniqueValueInfos:[{value:"1",symbol:i,label:"First Stop"},{value:`${this.stops.length}`,symbol:o,label:"Last Stop"}]},e}};(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1,origins:{"portal-item":{write:{allowNull:!0,ignoreOrigin:!0}},"web-map":{write:{overridePolicy(){return{allowNull:!0,ignoreOrigin:null==this.portalItem}}}}}}})],li.prototype,"_featureCollection",void 0),(0,r._)([(0,_.c)(["web-map","portal-item"],"_featureCollection")],li.prototype,"writeFeatureCollectionWebmap",null),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1,origins:{"web-map":{write:{target:"type",overridePolicy(){return{ignoreOrigin:null!=this.portalItem}}}}}}})],li.prototype,"_type",void 0),(0,r._)([(0,T.Cb)({nonNullable:!0,type:Z})],li.prototype,"defaultSymbols",void 0),(0,r._)([(0,T.Cb)({readOnly:!0})],li.prototype,"directionLines",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"directionLines",["layers","featureCollection.layers"])],li.prototype,"readDirectionLines",null),(0,r._)([(0,T.Cb)({readOnly:!0})],li.prototype,"directionPoints",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"directionPoints",["layers","featureCollection.layers"])],li.prototype,"readDirectionPoints",null),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1,origins:{"web-map":{write:{ignoreOrigin:!0}}}}})],li.prototype,"featureCollectionType",void 0),(0,r._)([(0,T.Cb)({readOnly:!0})],li.prototype,"fullExtent",null),(0,r._)([(0,T.Cb)({json:{origins:{"web-map":{name:"featureCollection.showLegend"}},write:!0}})],li.prototype,"legendEnabled",void 0),(0,r._)([(0,T.Cb)({type:["show","hide"]})],li.prototype,"listMode",void 0),(0,r._)([(0,T.Cb)({type:Number,nonNullable:!0,json:{write:!1}})],li.prototype,"maxScale",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"maxScale",["layers","featureCollection.layers"])],li.prototype,"readMaxScale",null),(0,r._)([(0,T.Cb)({type:Number,nonNullable:!0,json:{write:!1}})],li.prototype,"minScale",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"minScale",["layers","featureCollection.layers"])],li.prototype,"readMinScale",null),(0,r._)([(0,T.Cb)({type:["ArcGISFeatureLayer"],value:"ArcGISFeatureLayer"})],li.prototype,"operationalLayerType",void 0),(0,r._)([(0,T.Cb)({nonNullable:!0,type:p.Z.ofType(vt)})],li.prototype,"pointBarriers",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"pointBarriers",["layers","featureCollection.layers"])],li.prototype,"readPointBarriers",null),(0,r._)([(0,T.Cb)({nonNullable:!0,type:p.Z.ofType(wt)})],li.prototype,"polygonBarriers",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"polygonBarriers",["layers","featureCollection.layers"])],li.prototype,"readPolygonBarriers",null),(0,r._)([(0,T.Cb)({nonNullable:!0,type:p.Z.ofType(Nt)})],li.prototype,"polylineBarriers",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"polylineBarriers",["layers","featureCollection.layers"])],li.prototype,"readPolylineBarriers",null),(0,r._)([(0,T.Cb)({readOnly:!0})],li.prototype,"routeInfo",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"routeInfo",["layers","featureCollection.layers"])],li.prototype,"readRouteInfo",null),(0,r._)([(0,T.Cb)({type:tt.Z})],li.prototype,"spatialReference",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"spatialReference",["layers","featureCollection.layers"])],li.prototype,"readSpatialReference",null),(0,r._)([(0,T.Cb)({nonNullable:!0,type:p.Z.ofType(Gt)})],li.prototype,"stops",void 0),(0,r._)([(0,w.r)(["web-map","portal-item"],"stops",["layers","featureCollection.layers"])],li.prototype,"readStops",null),(0,r._)([(0,T.Cb)()],li.prototype,"title",null),(0,r._)([(0,T.Cb)({readOnly:!0,json:{read:!1}})],li.prototype,"type",void 0),(0,r._)([(0,T.Cb)()],li.prototype,"url",null),li=(0,r._)([(0,S.j)(ai)],li);const pi=li},51706:(e,t,i)=>{var r,o;function s(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function a(e){return null!=e&&!isNaN(e)&&isFinite(e)}function n(e){return e.valueExpression?r.Expression:e.field&&"string"==typeof e.field?r.Field:r.Unknown}function l(e,t){const i=t||n(e),s=e.valueUnit||"unknown";return i===r.Unknown?o.Constant:e.stops?o.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?o.ClampedLinear:"unknown"===s?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?o.Proportional:o.Additive:o.Identity:o.RealWorldSize}i.d(t,{PS:()=>n,QW:()=>l,RY:()=>r,hL:()=>o,iY:()=>s,qh:()=>a}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(r||(r={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(o||(o={}))},74889:(e,t,i)=>{i.d(t,{Z:()=>T});var r,o=i(43697),s=i(66577),a=i(38171),n=i(35454),l=i(96674),p=i(22974),u=i(70586),d=i(5600),c=(i(75215),i(71715)),m=i(52011),y=i(30556),b=i(82971),h=i(33955),f=i(1231);const v=new n.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let g=r=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const i=b.Z.fromJSON(t.spatialReference),r=[];for(let t=0;t<e.length;t++){const o=e[t],s=a.Z.fromJSON(o),n=o.geometry&&o.geometry.spatialReference;(0,u.pC)(s.geometry)&&!n&&(s.geometry.spatialReference=i);const l=o.aggregateGeometries,p=s.aggregateGeometries;if(l&&(0,u.pC)(p))for(const e in p){const t=p[e],r=l[e]?.spatialReference;(0,u.pC)(t)&&!r&&(t.spatialReference=i)}r.push(s)}return r}writeGeometryType(e,t,i,r){if(e)return void v.write(e,t,i,r);const{features:o}=this;if(o)for(const e of o)if(e&&(0,u.pC)(e.geometry))return void v.write(e.geometry.type,t,i,r)}readQueryGeometry(e,t){if(!e)return null;const i=!!e.spatialReference,r=(0,h.im)(e);return r&&!i&&t.spatialReference&&(r.spatialReference=b.Z.fromJSON(t.spatialReference)),r}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:i}=this;if(i)for(const e of i)if(e&&(0,u.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new r(this.cloneProperties())}cloneProperties(){return(0,p.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let i=0;i<t.features.length;i++){const r=t.features[i];if(r.geometry){const t=e&&e[i];r.geometry=t&&t.toJSON()||r.geometry}}return t}quantize(e){const{scale:[t,i],translate:[r,o]}=e,s=this.features,a=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-r)/t)),(e=>Math.round((o-e)/i)));for(let e=0,t=s.length;e<t;e++)a?.((0,u.Wg)(s[e].geometry))||(s.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:i}=this;if(!i)return this;const{translate:[r,o],scale:[s,a]}=i,n=this._getHydrationFunction(e,(e=>e*s+r),(e=>o-e*a));for(const{geometry:e}of t)(0,u.pC)(e)&&n&&n(e);return this.transform=null,this}_quantizePoints(e,t,i){let r,o;const s=[];for(let a=0,n=e.length;a<n;a++){const n=e[a];if(a>0){const e=t(n[0]),a=i(n[1]);e===r&&a===o||(s.push([e-r,a-o]),r=e,o=a)}else r=t(n[0]),o=i(n[1]),s.push([r,o])}return s.length>0?s:null}_getQuantizationFunction(e,t,i){return"point"===e?e=>(e.x=t(e.x),e.y=i(e.y),e):"polyline"===e||"polygon"===e?e=>{const r=(0,h.oU)(e)?e.rings:e.paths,o=[];for(let e=0,s=r.length;e<s;e++){const s=r[e],a=this._quantizePoints(s,t,i);a&&o.push(a)}return o.length>0?((0,h.oU)(e)?e.rings=o:e.paths=o,e):null}:"multipoint"===e?e=>{const r=this._quantizePoints(e.points,t,i);return r&&r.length>0?(e.points=r,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,i){return"point"===e?e=>{e.x=t(e.x),e.y=i(e.y)}:"polyline"===e||"polygon"===e?e=>{const r=(0,h.oU)(e)?e.rings:e.paths;let o,s;for(let e=0,a=r.length;e<a;e++){const a=r[e];for(let e=0,r=a.length;e<r;e++){const r=a[e];e>0?(o+=r[0],s+=r[1]):(o=r[0],s=r[1]),r[0]=t(o),r[1]=i(s)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=i(e.ymin),e.xmax=t(e.xmax),e.ymax=i(e.ymax)}:"multipoint"===e?e=>{const r=e.points;let o,s;for(let e=0,a=r.length;e<a;e++){const a=r[e];e>0?(o+=a[0],s+=a[1]):(o=a[0],s=a[1]),a[0]=t(o),a[1]=i(s)}}:null}};(0,o._)([(0,d.Cb)({type:String,json:{write:!0}})],g.prototype,"displayFieldName",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],g.prototype,"exceededTransferLimit",void 0),(0,o._)([(0,d.Cb)({type:[a.Z],json:{write:!0}})],g.prototype,"features",void 0),(0,o._)([(0,c.r)("features")],g.prototype,"readFeatures",null),(0,o._)([(0,d.Cb)({type:[f.Z],json:{write:!0}})],g.prototype,"fields",void 0),(0,o._)([(0,d.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:v.read}}})],g.prototype,"geometryType",void 0),(0,o._)([(0,y.c)("geometryType")],g.prototype,"writeGeometryType",null),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],g.prototype,"hasM",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],g.prototype,"hasZ",void 0),(0,o._)([(0,d.Cb)({types:s.qM,json:{write:!0}})],g.prototype,"queryGeometry",void 0),(0,o._)([(0,c.r)("queryGeometry")],g.prototype,"readQueryGeometry",null),(0,o._)([(0,d.Cb)({type:b.Z,json:{write:!0}})],g.prototype,"spatialReference",void 0),(0,o._)([(0,y.c)("spatialReference")],g.prototype,"writeSpatialReference",null),(0,o._)([(0,d.Cb)({json:{write:!0}})],g.prototype,"transform",void 0),g=r=(0,o._)([(0,m.j)("esri.rest.support.FeatureSet")],g),g.prototype.toJSON.isDefaultToJSON=!0;const T=g},11282:(e,t,i)=>{i.d(t,{cv:()=>n,en:()=>a,lA:()=>s}),i(68773),i(40330);var r=i(22974),o=i(17452);function s(e,t){return t?{...t,query:{...e??{},...t.query}}:{query:e}}function a(e){return"string"==typeof e?(0,o.mN)(e):(0,r.d9)(e)}function n(e,t,i){const r={};for(const o in e){if("declaredClass"===o)continue;const s=e[o];if(null!=s&&"function"!=typeof s)if(Array.isArray(s)){r[o]=[];for(let e=0;e<s.length;e++)r[o][e]=n(s[e])}else if("object"==typeof s)if(s.toJSON){const e=s.toJSON(i&&i[o]);r[o]=t?e:JSON.stringify(e)}else r[o]=t?s:JSON.stringify(s);else r[o]=s}return r}i(71058)}}]);