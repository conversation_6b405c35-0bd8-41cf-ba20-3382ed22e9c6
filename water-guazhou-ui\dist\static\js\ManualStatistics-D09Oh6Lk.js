import{d as N,c as I,r as _,b as g,Q as R,g as B,h as G,F as v,p as M,q,i as y,_ as P}from"./index-r0dFAfgr.js";import{GetFieldConfig as V}from"./fieldconfig-Bk3o1wi7.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as z}from"./LayerHelper-Cn-iiqxI.js";import{g as W}from"./QueryHelper-ILO3qZqg.js";import{E as J}from"./StatisticsHelper-D-s_6AyQ.js";import{u as Q}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{GetFieldConfig as U,GetFieldValueByGeoserver as Y}from"./wfsUtils-DXofo3da.js";import $ from"./RightDrawerMap-D5PhmGFO.js";import H from"./StatisticsCharts-CyK-dNnC.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useDetector-BRcb7GRN.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./geoserverUtils-wjOSMa7E.js";import"./echart-BoVIcYbV.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const pr=N({__name:"ManualStatistics",setup(K){const x=I(),s=I(),r=_({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1,curOperate:""}),p={queryParams:{geometry:void 0,where:"1=1"}},b=_({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>r.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>h("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>r.loading,iconifyIcon:"ep:crop",click:()=>h("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>r.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>h("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>r.loading,iconifyIcon:"ep:delete",click:()=>w()}]}]},{id:"layerid",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(e,t)=>{t&&s.value&&(s.value.dataForm.layerid=[e.value])}}]},{id:"layer",fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(e,t)=>{{const l=t.layerid[0],i=await U(l);if(i&&i.data&&i.data.featureTypes&&i.data.featureTypes[0]){const m=i.data.featureTypes[0].properties||[];e.data=m;const o=["START_SID","END_SID","SID","OBJECTID","PIPELENGTH","X","Y","the_geom","geom"],c=m.filter(a=>{var n;return o.indexOf(a.name)===-1&&a.type!=="gml:GeometryPropertyType"&&["int","long","double","float","string","date"].indexOf(((n=a.type.split(":")[1])==null?void 0:n.toLowerCase())||"")!==-1}).map((a,n)=>{var k;const d=((k=a.type.split(":")[1])==null?void 0:k.toLowerCase())||"";let u="input";return["int","long","double","float"].includes(d)?u="input-number":d==="date"&&(u="date"),{id:n,field:a.name,label:a.name,type:u,disabled:!1,readonly:!1}}).sort((a,n)=>a.id-n.id);r.curLayerFields=formatTree(c||[],{id:"field",label:"label",value:"field"}),resetInlineFromConfig(c)}else console.error("无法获取GeoServer字段信息")}},setDataBy:"layerid",displayField:"name",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{r.curFieldNode=e,f(e.name)}}]},{id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{f("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{f("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{f(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{f("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{f("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{f("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{f("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{f("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{f("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>r.curOperate==="uniqueing",disabled:()=>r.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>T()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>r.curOperate==="detailing",click:()=>S(),styles:{width:"100%"}}]}]},{fieldset:{desc:"统计参数"},fields:[{type:"select",label:"分组字段",field:"group_fields",clearable:!1,options:[{label:"口径",value:"DIAMETER"},{label:"材质",value:"MATERIAL"},{label:"所在道路",value:"LANEWAY"}],setOptionBy:"layerid",setOptionMethod:e=>{e.option=[{label:"材质",value:"MATERIAL"},{label:"口径",value:"DIAMETER"},{label:"所在道路",value:"LANEWAY"},{label:"权属单位",value:"OWNERUNIT"}]}},{type:"select",label:"统计字段",clearable:!1,field:"statistic_field",options:[],setOptionMethod:async(e,t)=>{var o,c,a,n;if(!((o=t.layerid)!=null&&o.length))return;const l=t.layerid[0],i=(c=r.layerInfos.find(d=>d.layerid===l))==null?void 0:c.layername;if(!i)return;const m=await V(i);e.options=(n=(a=m.data)==null?void 0:a.result)==null?void 0:n.rows.map(d=>({id:d.name,label:d.alias,value:d.name}))},setOptionBy:"layerid"},{type:"select",label:"统计类型",clearable:!1,field:"statistic_type",options:[{label:"数量",value:"1"},{label:"长度",value:"2",disabled:(e,t)=>{const l=t.layerid||[],i=r.layerInfos.find(m=>m.layerid===l[0]);return(i==null?void 0:i.geometrytype)!=="esriGeometryPolyline"}}]},{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="detailing"?"正在统计":"统计",disabled:()=>r.curOperate==="detailing",loading:()=>r.curOperate==="detailing",click:()=>D(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{group_fields:"DIAMETER",statistic_field:"OBJECTID",statistic_type:"1"}}),C=(e,t)=>{console.log(e,t)},{initSketch:E,destroySketch:L}=Q(),h=e=>{var t;w(),(t=p.sketch)==null||t.create(e)},w=()=>{var e;(e=p.graphicsLayer)==null||e.removeAll(),p.queryParams.geometry=void 0},f=e=>{var l;if(!s.value)return;(l=s.value)!=null&&l.dataForm||(s.value.dataForm={});const t=s.value.dataForm.sql||" ";s.value.dataForm.sql=t+e+" "},S=()=>{var e;(e=s.value)!=null&&e.dataForm&&(s.value.dataForm.sql="")},T=async()=>{var n,d;const e=(n=s.value)==null?void 0:n.dataForm.layerid,t=r.curFieldNode.name,l=await Y({layerName:e,fiedName:t});if(!l||!l.data){console.error("获取GeoServer唯一值响应无效:",l),g.error("获取唯一值失败"),r.uniqueing=!1;return}const i=l.data;if(console.log("获取到的GeoServer数据:",i),!i.features||!Array.isArray(i.features)){console.error("无效的GeoServer数据格式:",i),g.warning("无法获取唯一值"),r.uniqueing=!1;return}const m=new Set;i.features.forEach(u=>{u&&u.properties&&m.add(u.properties[t])});const o=Array.from(m).filter(u=>u!=null);console.log("提取的唯一值:",o);const c=(d=b.group.find(u=>u.id==="field-construct"))==null?void 0:d.fields[0].extraFormItem,a=c&&c[0];a.data=o},A=async()=>{var i,m;const e=(i=b.group.find(o=>o.id==="layerid"))==null?void 0:i.fields[0];let l=((m=p.view)==null?void 0:m.layerViews.items[0].layer.sublayers).items.map(o=>({label:o.name,value:o.name,layername:o.name,type:o.type,spatialReferences:o.spatialReferences}));e.options=l},D=async()=>{var e,t,l,i,m,o,c;g.info("正在统计，请稍候...");try{r.tabs.length=0;const a=(e=s.value)==null?void 0:e.dataForm.layerid;if(!(a!=null&&a.length)){g.warning("请选择一个要统计的图层");return}p.queryParams.where=((l=(t=s.value)==null?void 0:t.dataForm)==null?void 0:l.sql)||"1=1",r.loading=!0,r.tabs.length=0,r.tabs=await W(a,r.layerInfos,p.queryParams),(i=x.value)==null||i.toggleCustomDetail(!0);let n=(m=p.queryParams.geometry)==null?void 0:m.extent;if(!n){const d=(o=p.view)==null?void 0:o.map.findLayerById("pipelayer");d?n=d.fullExtent:n=(c=p.view)==null?void 0:c.extent}}catch(a){console.log(a),r.loading=!1,g.error("统计失败")}},F=e=>{var t;p.queryParams.geometry=(t=e.graphics[0])==null?void 0:t.geometry},O=e=>{p.view=e,p.graphicsLayer=z(p.view,{id:"search-manual",title:"高级统计"}),p.sketch=E(p.view,p.graphicsLayer,{createCallBack:F,updateCallBack:F}),setTimeout(()=>{A()},1e3)};return R(()=>{L(),p.sketch=void 0}),(e,t)=>{const l=P;return B(),G($,{ref_key:"refMap",ref:x,title:"高级统计","full-content":!0,onMapLoaded:O},{"detail-header":v(()=>t[1]||(t[1]=[M("span",null,"统计结果",-1)])),"detail-default":v(()=>{var i,m,o,c,a,n;return[q(H,{view:p.view,"layer-ids":y(r).layerIds,"query-params":p.queryParams,percision:0,"statistics-params":{group_fields:[(i=y(s))==null?void 0:i.dataForm.group_fields],statistic_field:((m=y(s))==null?void 0:m.dataForm.statistic_type)==="1"?(o=y(s))==null?void 0:o.dataForm.statistic_field:y(J).ShapeLen,statistic_type:(c=y(s))==null?void 0:c.dataForm.statistic_type},tabs:y(r).tabs,prefix:((a=y(s))==null?void 0:a.dataForm.group_fields)==="DIAMETER"?"DN":"",unit:((n=y(s))==null?void 0:n.dataForm.statistic_type)==="1"?"个":"m",onDetailRefreshed:t[0]||(t[0]=d=>y(r).loading=!1),onAttrRowClick:C},null,8,["view","layer-ids","query-params","statistics-params","tabs","prefix","unit"])]}),default:v(()=>[q(l,{ref_key:"refForm",ref:s,config:y(b)},null,8,["config"])]),_:1},512)}}});export{pr as default};
