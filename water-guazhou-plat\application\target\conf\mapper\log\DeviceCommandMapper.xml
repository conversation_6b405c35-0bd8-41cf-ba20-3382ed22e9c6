<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.optionLog.DeviceCommandMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.DeviceCommand">
        SELECT a.*, b.name AS "deviceName"
        FROM device_command a
        LEFT JOIN device b ON b.id = a.device_key
        <where>
            b.name IS NOT NULL
            <if test="param.optionUser != null and param.optionUser != ''">
                AND a.option_user = #{param.optionUser}
            </if>
            <if test="param.deviceKey != null and param.deviceKey != ''">
                AND a.device_key = #{param.deviceKey}
            </if>
            <if test="param.commandType != null and param.commandType != ''">
                AND a.command_type = #{param.commandType}
            </if>
            <if test="param.beginTime != null">
                AND a.option_time <![CDATA[ >= ]]> #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.option_time <![CDATA[ <= ]]> #{param.endTime}
            </if>
        </where>
        ORDER BY a.option_time DESC
    </select>

</mapper>