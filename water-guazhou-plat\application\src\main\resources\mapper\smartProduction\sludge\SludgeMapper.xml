<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.sludge.SludgeMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartProduction.sludge.Sludge">
        select * from sp_sludge
        <where>
            tenant_id = #{param.tenantId}
            <if test="param.carNo != null and param.carNo != ''">
                and car_no like '%' || #{param.carNo} || '%'
            </if>
            <if test="param.start != null">
                and create_time &gt;= to_timestamp(#{param.start} / 1000)
            </if>
            <if test="param.end != null">
                and create_time &lt;= to_timestamp(#{param.end} / 1000)
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="countByYear" resultType="com.alibaba.fastjson.JSONObject">
        select to_char(time, 'MM月') as "timeMonth", sum(gross) as gross, sum(tare) as tare, sum(net) as net
        from sp_sludge
        where
        tenant_id = #{tenantId}
        and to_char(time, 'YYYY') like #{year}||'%'
            group by to_char(time, 'MM月')
        order by to_char(time, 'MM月')
    </select>

</mapper>