package org.thingsboard.server.dao.stationData.customization;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.AssayRecordFeignClient;
import org.thingsboard.server.dao.client.MedicineManageFeignClient;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.sql.AssayRecord;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.stationData.StationDataService;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 站点类型：水司
 * 水司驾驶舱
 */
@Slf4j
@Service
public class CompanyControlService {

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Autowired
    private MedicineManageFeignClient medicineManageFeignClient;

    @Autowired
    private AssayRecordFeignClient assayRecordFeignClient;


    /**
     * 水司驾驶舱 水质监测
     */
    public Object waterQualityMonitoring(TenantId tenantId) throws ThingsboardException {
        JSONObject result = new JSONObject();

        result.put("dataPoints", 5);

        List<AssayRecord> list = assayRecordFeignClient.list();
        // 水质分析数据
        int assayTime = 0;// 化验次数
        int passTime = 0;// 达标次数
        String passRate = "0";
        if (list != null) {
            assayTime = list.size();
            for (AssayRecord assayRecord : list) {
                String status = assayRecord.getStatus();
                if ("1".equals(status)) {// 合格
                    passTime++;
                }
            }

            // 计算达标率
            if (passTime != 0 && assayTime != 0) {
                BigDecimal divide = BigDecimal.valueOf(passTime).divide(BigDecimal.valueOf(assayTime), 2, BigDecimal.ROUND_DOWN);
                passRate = divide.multiply(new BigDecimal("100")) + "%";
            }
        }

        result.put("assayTime", assayTime);
        result.put("passTime", passTime);
        result.put("passRate", passRate);

        return result;
    }

    /**
     * 管网设施统计
     */
    public Object pipeNetworkCount(TenantId tenantId) {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, null, "");
        List<StationEntity> stationList = stationPageData.getData();

        int pressureStationSize = 0;
        int flowStationSize = 0;
        int pumpingStationSize = 0;
        int waterQualitySize = 0;
        int bigUserSize = 0;
        int usersSize = 13;

        if (stationList != null) {
            for (StationEntity station : stationList) {
                String type = station.getType();
                if ("压力监测站".equals(type)) {
                    pressureStationSize++;
                }
                if ("流量监测站".equals(type)) {
                    flowStationSize++;
                }
                if ("测流压站".equals(type)) {
                    pressureStationSize++;
                    flowStationSize++;
                }
                if ("泵站".equals(type)) {
                    pumpingStationSize++;
                }
                if ("水质监测站".equals(type)) {
                    waterQualitySize++;
                }
                if ("大用户".equals(type)) {
                    bigUserSize++;
                }
            }
        }
        JSONObject result = new JSONObject();
        result.put("pressureStationSize", pressureStationSize);
        result.put("flowStationSize", flowStationSize);
        result.put("pumpingStationSize", pumpingStationSize);
        result.put("waterQualitySize", waterQualitySize);
        result.put("bigUserSize", bigUserSize);
        result.put("usersSize", usersSize);

        return result;
    }

    /**
     * 总览统计
     */
    public Object viewCount(TenantId tenantId) throws ThingsboardException {
        JSONObject result = new JSONObject();

        List<Device> deviceList = deviceService.findAllByTenantId(tenantId);

        // 统计设备数据
        int deviceTotal = 0;
        int deviceExceptionTotal = 0;
        if (deviceList != null) {
            deviceTotal = deviceList.size();
            for (Device device : deviceList) {
                if (!device.isStatus()) {
                    deviceExceptionTotal++;
                }
            }
        }
        result.put("deviceTotal", deviceTotal);
        result.put("deviceExceptionTotal", deviceExceptionTotal);

        // 统计供水量
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", "");
        List<StationEntity> stationList = stationPageData.getData();

        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();

        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));
        Date yesterdayEnd = new Date(todayStart.getTime() - (1000));

        // 查询供水量数据
        BigDecimal yesterdayTotalFlow = new BigDecimal("0");
        BigDecimal todayTotalFlow = new BigDecimal("0");
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            // 统计数据
            // 查询今日、昨日累计流量
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                    totalFlowData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                    break;
                }
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (totalFlowData != null) {
                LinkedHashMap<String, BigDecimal> yesterdayTotalFlowMap = totalFlowData.get(dateFormat.format(yesterdayStart));
                BigDecimal yesterdayData = null;
                if (yesterdayTotalFlowMap != null && !yesterdayTotalFlowMap.isEmpty()) {
                    yesterdayData = new ArrayList<>(yesterdayTotalFlowMap.values()).get(0);
                }
                BigDecimal todayData = null;
                LinkedHashMap<String, BigDecimal> todayTotalFlowMap = totalFlowData.get(dateFormat.format(todayStart));
                if (todayTotalFlowMap != null && !todayTotalFlowMap.isEmpty()) {
                    todayData = new ArrayList<>(todayTotalFlowMap.values()).get(0);
                }
                yesterdayTotalFlow = yesterdayTotalFlow.add(yesterdayData);
                todayTotalFlow = todayTotalFlow.add(todayData);
            }
        }
        result.put("yesterdayTotalFlow", yesterdayTotalFlow);
        result.put("todayTotalFlow", todayTotalFlow);

        return result;
    }

    /**
     * 用水量分析
     */
    public Object useWaterAnalyze(TenantId tenantId) throws ThingsboardException {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", "");
        List<StationEntity> stationList = stationPageData.getData();

        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        Date date = new Date(instance.getTimeInMillis() - (6 * 24 * 60 * 60 * 1000));

        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains("进水口")) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            // 统计数据
            // 查询累计流量
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    String attr = deviceId + "." + deviceFullData.getProperty();
                    List<String> attributes = Collections.singletonList(attr);
                    totalFlowData = obtainDataService.getDeviceData(attributes, date.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                    break;
                }
            }
            if (totalFlowData != null && !totalFlowData.isEmpty()) {
                JSONObject data = new JSONObject();
                data.put("station", station.getName());
                data.put("data", totalFlowData);
                result.add(data);
            }
        }

        return result;
    }

    /**
     * 今日供水占比
     */
    public Object supplyWaterProportion(TenantId tenantId) throws ThingsboardException {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", "");
        List<StationEntity> stationList = stationPageData.getData();

        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        JSONObject result = new JSONObject();
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                    totalFlowData = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                    break;
                }
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (totalFlowData != null) {
                BigDecimal todayData = null;
                LinkedHashMap<String, BigDecimal> todayTotalFlowMap = totalFlowData.get(dateFormat.format(todayStart));
                if (todayTotalFlowMap != null && !todayTotalFlowMap.isEmpty()) {
                    todayData = new ArrayList<>(todayTotalFlowMap.values()).get(0);
                }
                result.put(station.getName(), todayData);
            }
        }

        return result;
    }

    /**
     * 不同时间段用水情况
     */
    public Object timeAreaUseWater(TenantId tenantId) throws ThingsboardException {
        PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, "水厂", "");
        List<StationEntity> stationList = stationPageData.getData();

        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(now);
        Map<String, BigDecimal> resultMap = new LinkedHashMap<>();
        for (int i = 1; i < 24; i++) {
            if (i < 10) {
                resultMap.put(format + " 0" + i, new BigDecimal(0));
            } else {
                resultMap.put(format + " " + i, new BigDecimal(0));
            }
        }

        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());
            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains("进水口")) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }

            if (stationAttr == null) {
                log.error("水厂未设置供水相关的动态属性分组");
                continue;
            }

            List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(station.getId(), stationAttr.getType(), true, tenantId);

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
            for (DeviceFullData deviceFullData : stationDataDetail) {
                if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                    String attr = deviceId + "." + deviceFullData.getProperty();
                    List<String> attributes = Collections.singletonList(attr);
                    totalFlowData = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);
                    break;
                }
            }

            if (totalFlowData != null) {
                Set<String> keySet = resultMap.keySet();
                for (String key : keySet) {
                    LinkedHashMap<String, BigDecimal> todayTotalFlowMap = totalFlowData.get(key);
                    if (todayTotalFlowMap != null && !todayTotalFlowMap.isEmpty()) {
                        BigDecimal hourData = new ArrayList<>(todayTotalFlowMap.values()).get(0);
                        if (hourData != null) {
                            resultMap.put(key, resultMap.get(key).add(hourData));
                        }
                    }
                }
            }
        }

        return resultMap;
    }
}
