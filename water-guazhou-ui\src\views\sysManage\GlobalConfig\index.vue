<template>
  <SLCard
    class="global-config"
    :title="'全局配置'"
    :overlay="true"
  >
    <el-tabs
      :tab-position="'left'"
      style="height: 100%"
      class="demo-tabs"
    >
      <el-tab-pane label="网站配置">
        <Basic></Basic>
      </el-tab-pane>
      <el-tab-pane label="GIS配置">
        <GIS_CONFIG></GIS_CONFIG>
      </el-tab-pane>
      <el-tab-pane label="一张图配置">
        <ONE_MAP_CONFIG></ONE_MAP_CONFIG>
      </el-tab-pane>
      <el-tab-pane label="超图配置">
        <SUPERMAP_CONFIG></SUPERMAP_CONFIG>
      </el-tab-pane>
      <el-tab-pane label="天气插件配置">
        <WEATHER_CONFIG></WEATHER_CONFIG>
      </el-tab-pane>
      <el-tab-pane label="智慧决策配置">
        <SMART_DECISION_CONFIG></SMART_DECISION_CONFIG>
      </el-tab-pane>
      <el-tab-pane label="污水厂配置">
        <SEWAGE_CONFIG></SEWAGE_CONFIG>
      </el-tab-pane>
      <el-tab-pane label="登录页配置">
        <LOGIN_CONFIG></LOGIN_CONFIG>
      </el-tab-pane>
    </el-tabs>
  </SLCard>
</template>
<script lang="ts" setup>
import Basic from './components/Basic.vue'
import GIS_CONFIG from './components/GIS_CONFIG.vue'
import LOGIN_CONFIG from './components/LOGIN_CONFIG.vue'
import ONE_MAP_CONFIG from './components/ONE_MAP_CONFIG.vue'
import SEWAGE_CONFIG from './components/SEWAGE_CONFIG.vue'
import SMART_DECISION_CONFIG from './components/SMART_DECISION_CONFIG.vue'
import SUPERMAP_CONFIG from './components/SUPERMAP_CONFIG.vue'
import WEATHER_CONFIG from './components/WEATHER_CONFIG.vue'
</script>
<style lang="scss" scoped>
.global-config {
  height: 100%;
}
</style>
