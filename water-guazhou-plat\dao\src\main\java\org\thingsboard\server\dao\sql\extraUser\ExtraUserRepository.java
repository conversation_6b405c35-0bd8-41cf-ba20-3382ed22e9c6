/**
 * Copyright © 2017 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.extraUser;

import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.ExtraUser;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12 17:46
 */

@SqlDao
public interface ExtraUserRepository extends CrudRepository<ExtraUser, String> {

    List<ExtraUser> findByTenantIdOrderByUpdateTime(@Param("tenantId") String tenantId);

    Integer countByTenantIdAndAndName(@Param("tenantId") String tenantId,
                                      @Param("name") String name);
}
