<template>
  <div class="manual-hookuo-revenue">
    <div class="left">
      <div class="title">
        未挂接数据
      </div>
      <Search
        ref="refSearchLeft"
        class="search"
        :config="Search_Left"
      ></Search>
      <FormTable
        class="table-box"
        :config="Table_Left"
      ></FormTable>
    </div>
    <div class="center">
      <div class="btn">
        <el-button
          type="primary"
          @click="hookUpUser"
        >
          <Icon :icon="'ep:d-arrow-right'"></Icon>
        </el-button>
      </div>
      <div class="btn">
        <el-button
          type="primary"
          @click="hookDownUser"
        >
          <Icon :icon="'ep:d-arrow-left'"></Icon>
        </el-button>
      </div>
    </div>
    <div class="right">
      <div class="title">
        已挂接数据
      </div>
      <Search
        ref="refSearchRight"
        class="search"
        :config="Search_Right"
      ></Search>
      <FormTable
        class="table-box"
        :config="Table_Right"
      ></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import {
  GetDMAUsers,
  MultiHookDownDMAUser,
  MultiHookUpDMAUser
} from '@/api/mapservice/dma'
import { ISearchIns } from '@/components/type'
import { SLMessage } from '@/utils/Message'

const props = defineProps<{ currentTreeNode?: NormalOption }>()
const refSearchLeft = ref<ISearchIns>()
const refSearchRight = ref<ISearchIns>()
const leftFilterField = ref<string>('businessHall')
const rightFilterField = ref<string>('businessHall')
const Search_Left = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '查询条件',
      field: 'keyword',
      prepend: {
        type: 'select',
        field: 'type',
        style: {
          width: '100px'
        },
        options: [
          { label: '用户名称', value: 'custName' },
          { label: '用户编号', value: 'custCode' },
          { label: '抄表员', value: 'copyMeterUser' },
          { label: '营业所', value: 'businessHall' },
          { label: '地址', value: 'address' }
        ],
        onChange: val => {
          leftFilterField.value = val
        }
      },
      prependDefault: 'businessHall'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          loading: (): boolean => !!Table_Left.loading,
          click: () => refreshLeftData()
        }
      ]
    }
  ]
})
const Search_Right = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '查询条件',
      field: 'keyword',
      prepend: {
        type: 'select',
        field: 'type',
        style: {
          width: '100px'
        },
        options: [
          { label: '用户名称', value: 'custName' },
          { label: '用户编号', value: 'custCode' },
          { label: '抄表员', value: 'copyMeterUser' },
          { label: '营业所', value: 'businessHall' },
          { label: '地址', value: 'address' }
        ],
        onChange: val => {
          rightFilterField.value = val
        }
      },
      prependDefault: 'businessHall'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          loading: (): boolean => !!Table_Right.loading,
          click: () => refreshRightData()
        }
      ]
    }
  ]
})
const Table_Left = reactive<ITable>({
  height: 400,
  pagination: {
    refreshData: ({ page, size }) => {
      Table_Left.pagination.page = page
      Table_Left.pagination.limit = size
      refreshLeftData()
    }
  },
  dataList: [],
  columns: [
    { minWidth: 120, label: '用户号', prop: 'custCode', fixed: 'left' },
    { minWidth: 120, label: '姓名', prop: 'custName', fixed: 'left' },
    { minWidth: 120, label: '地址', prop: 'address' },
    { minWidth: 120, label: '册本编号', prop: 'meterBookCode' },
    { minWidth: 120, label: '册本名称', prop: 'meterBookName' },
    { minWidth: 120, label: '抄表员', prop: 'copyMeterUser' },
    { minWidth: 120, label: '营业所', prop: 'businessHall' },
    { minWidth: 120, label: '用水类型', prop: 'waterCategory' }
  ],
  handleSelectChange: rows => {
    Table_Left.selectList = rows || []
  }
})

const Table_Right = reactive<ITable>({
  height: 400,
  pagination: {
    refreshData: ({ page, size }) => {
      Table_Right.pagination.page = page
      Table_Right.pagination.limit = size
      refreshRightData()
    }
  },
  dataList: [],
  columns: [
    { minWidth: 120, label: '用户号', prop: 'custCode', fixed: 'left' },
    { minWidth: 120, label: '姓名', prop: 'custName', fixed: 'left' },
    { minWidth: 120, label: '地址', prop: 'address' },
    { minWidth: 120, label: '册本编号', prop: 'meterBookCode' },
    { minWidth: 120, label: '册本名称', prop: 'meterBookName' },
    { minWidth: 120, label: '抄表员', prop: 'copyMeterUser' },
    { minWidth: 120, label: '营业所', prop: 'businessHall' },
    { minWidth: 120, label: '用水类型', prop: 'waterCategory' }
  ],
  handleSelectChange: rows => {
    Table_Right.selectList = rows || []
  }
})

const refreshLeftData = async () => {
  const params = {
    page: Table_Left.pagination.page || 1,
    size: Table_Left.pagination.limit || 20,
    isMount: '0'
    // partitionId: props.currentTreeNode?.value
  }
  params[leftFilterField.value] = refSearchLeft.value?.queryParams.keyword
  Table_Left.loading = true
  try {
    const res = await GetDMAUsers(params)
    const data = res.data.data || {}
    Table_Left.dataList = data.data || []
    Table_Left.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  Table_Left.loading = false
}
const refreshRightData = async () => {
  const params = {
    page: Table_Right.pagination.page || 1,
    size: Table_Right.pagination.limit || 20,
    isMount: '1',
    partitionId: props.currentTreeNode?.value
  }
  params[rightFilterField.value] = refSearchRight.value?.queryParams.keyword
  Table_Right.loading = true
  try {
    const res = await GetDMAUsers(params)
    const data = res.data.data || {}
    Table_Right.dataList = data.data || []
    Table_Right.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  Table_Right.loading = false
}
const hookUpUser = async () => {
  const users = Table_Left.selectList || []
  if (!users.length) {
    SLMessage.warning('请选择要挂接的用户')
    return
  }
  try {
    const res = await MultiHookUpDMAUser(
      users.map(item => {
        return {
          partitionId: props.currentTreeNode?.value,
          id: item.id
        }
      })
    )
    if (res.data.code === 200) {
      SLMessage.success('操作成功')
      refreshLeftData()
      refreshRightData()
    } else {
      SLMessage.error(res.data.message)
    }
  } catch (error) {
    SLMessage.error('操作失败')
  }
}
const hookDownUser = async () => {
  const users = Table_Right.selectList || []
  if (!users.length) {
    SLMessage.warning('请先选择要取消挂接的用户')
    return
  }
  try {
    await MultiHookDownDMAUser(users.map(item => item.id))
    SLMessage.success('操作成功')
    refreshLeftData()
    refreshRightData()
  } catch (error) {
    SLMessage.error('操作失败')
  }
}
onMounted(() => {
  refreshLeftData()
  refreshRightData()
})
</script>
<style lang="scss" scoped>
.manual-hookuo-revenue {
  display: flex;
  .title {
    background-color: var(--el-bg-color);
    height: 36px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-bottom: 1px solid var(--el-border-color);
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 16px;
  }
  .left,
  .right {
    width: calc(50% - 50px);
    border: 1px solid var(--el-border-color);
    .search {
      margin-bottom: 8px;
    }
  }
  .center {
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .btn {
      padding: 12px 0;
    }
  }
  .table-box {
    height: calc(100% - 90px);
  }
}
</style>
