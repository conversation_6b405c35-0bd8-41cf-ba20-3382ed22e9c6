<!-- 工程管理-详情-工程完成情况 -->
<template>
  <CardTable
    title="工程完成情况"
    :config="TableConfig"
    class="card-table"
  ></CardTable>
</template>

<script lang="ts" setup>
import { getConstructioncompletionInfo } from '@/api/engineeringManagement/manage';

const props = defineProps<{ id: string }>();

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '业务阶段', prop: 'scopeName' },
    { label: '开始时间', prop: 'startTimeName' },
    { label: '结束时间', prop: 'completeTimeName' },
    { label: '处理人', prop: 'processUserName' },
    {
      label: '完成状态',
      prop: 'statusName',
      tag: true,
      tagColor: (row): string =>
        row.statusName === '完成' ? '#67C23A' : '#409EFF',
      formatter: (row) => row.statusName
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const refreshData = async () => {
  getConstructioncompletionInfo(props.id).then((res) => {
    TableConfig.dataList = res.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card-table {
  height: 300px;
  margin-bottom: 20px;
}
</style>
