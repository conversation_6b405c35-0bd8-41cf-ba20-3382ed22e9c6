import { request } from '@/plugins/axios';

/**
 * 查询人员配置列表
 * @param params
 * @returns
 */
export const GetUserSettingList = (params: {
  page?: number;
  size?: number;
  name?: string;
}) => {
  return request({
    url: '/api/gis/setting/people/list',
    method: 'get',
    params
  });
};

/**
 * 保存人员配置信息
 * @param params
 * @returns
 */
export const SaveUserSetting = (params: {
  id: string;
  name: string;
  type: string;
  roles: string;
  tenantId: string;
}) => {
  return request({
    url: '/api/gis/setting/people/save',
    method: 'post',
    data: params
  });
};
