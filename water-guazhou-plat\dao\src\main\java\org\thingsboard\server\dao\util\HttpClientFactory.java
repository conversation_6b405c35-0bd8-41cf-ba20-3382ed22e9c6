/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.util;


import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;

/**
 * <AUTHOR>
 * @date 2020-03-03
 */
public class HttpClientFactory {

    /**
     * 异步httpClient
     */
    private static CloseableHttpAsyncClient httpAsyncClient;

    /**
     * 同步httpClient
     */
    private static CloseableHttpClient httpClient;

    public HttpClientFactory() {
        if (httpAsyncClient == null) {
            httpAsyncClient = HttpAsyncClientBuilder.create()
                    .setMaxConnTotal(1000).setMaxConnPerRoute(1000).build();
            httpAsyncClient.start();
        }
        if(httpClient==null){
            httpClient= HttpClients.createDefault();
        }
    }

    public static CloseableHttpAsyncClient getHttpAsyncClient() {
        if (httpAsyncClient == null) {
            httpAsyncClient = HttpAsyncClientBuilder.create()
                    .setMaxConnTotal(1000).setMaxConnPerRoute(1000).build();
            httpAsyncClient.start();
        }
        return httpAsyncClient;
    }

    public static CloseableHttpClient getHttpClient(){
        if(httpClient==null){
            httpClient= HttpClients.createDefault();
        }
        return httpClient;
    }

}
