<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportTableHeaderDataMapper">
    <insert id="batchInsert">
        insert into tb_report_table_header_data(id, pid, title, value, enable_count, width, order_num, create_time, tenant_id) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
                #{element.id},
                #{element.pid},
                #{element.title},
                #{element.value},
                #{element.enableCount},
                #{element.width},
                #{element.orderNum},
                #{element.createTime},
                #{element.tenantId}
            )
        </foreach>
    </insert>
</mapper>