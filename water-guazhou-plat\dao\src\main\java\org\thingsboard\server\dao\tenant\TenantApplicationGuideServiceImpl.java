package org.thingsboard.server.dao.tenant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;
import org.thingsboard.server.dao.model.sql.TenantApplicationGuideEntity;
import org.thingsboard.server.dao.sql.tenant.TenantApplicationGuideMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class TenantApplicationGuideServiceImpl implements TenantApplicationGuideService {

    @Autowired
    private TenantApplicationGuideMapper tenantApplicationGuideMapper;

    @Override
    public IstarResponse save(TenantApplicationGuideEntity tenantApplicationGuideEntity) {
        if (StringUtils.isBlank(tenantApplicationGuideEntity.getId())) {
            TenantApplicationGuideEntity byApplicationId = this.getByApplicationId(tenantApplicationGuideEntity.getApplicationId());
            if (byApplicationId != null) {
                return IstarResponse.error("该应用已设置，请勿重复设置");
            }
            tenantApplicationGuideEntity.setCreateTime(new Date());
            tenantApplicationGuideMapper.insert(tenantApplicationGuideEntity);
        } else {
            tenantApplicationGuideMapper.updateById(tenantApplicationGuideEntity);
        }
        return IstarResponse.ok(tenantApplicationGuideEntity);
    }

    @Override
    public List<TenantApplicationGuideEntity> getList(String tenantId) {
        return tenantApplicationGuideMapper.getList(tenantId);
    }

    @Override
    public TenantApplicationGuideEntity getByApplicationId(String applicationId) {
        return tenantApplicationGuideMapper.getByApplicationId(applicationId);
    }

    @Override
    public List<TenantApplicationEntity> getNotSetList(String tenantId) {
        return tenantApplicationGuideMapper.getNotSetList(tenantId);
    }
}
