package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanyPageRequest;

import java.util.List;

@Mapper
public interface SoBiddingCompanyMapper extends BaseMapper<SoBiddingCompany> {
    IPage<SoBiddingCompany> findByPage(SoBiddingCompanyPageRequest request);

    boolean update(SoBiddingCompany entity);

    int removeAllByBiddingId(String id);

    int saveAll(List<SoBiddingCompany> entities);

}
