/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.task;

import akka.actor.ActorRef;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.thingsboard.server.actors.ActorSystemContext;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.dao.dataSource.DataSourceGroup;
import org.thingsboard.server.dao.dataSource.DataSourceService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.schedule.ScheduleJob;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.service.task.job.RestApiJob;
import org.thingsboard.server.service.utils.SchedulerUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.thingsboard.server.service.utils.SchedulerUtils.getSchedulerCron;
import static org.thingsboard.server.service.utils.SchedulerUtils.startScheduler;

@Component
@Slf4j
public class AlarmTask implements ApplicationRunner {

    @Autowired
    private ActorSystemContext actorContext;

    private Scheduler scheduler;

    @Autowired
    private DeviceService deviceService;


    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        //actorContext.getAlarmActor().tell(5000,ActorRef.noSender());
        actorContext.getMaintainActor().tell(5000,ActorRef.noSender());
        processAlarm();
    }

    /**
     * 执行预设统计量
     */
    private void processAlarm() throws SchedulerException {
        scheduler = SchedulerUtils.getSchedulerFactory().getScheduler();
        //获取平台所有预统计量
        List<Device> devices =deviceService.findAll();
        devices.forEach(device -> {
            //主机
            if(device.getGateWayId()==null){
                if(device.getAdditionalInfo()!=null) {
                    JsonNode jsonNode = device.getAdditionalInfo();
                    try {
                        String offline = JSONObject.parseObject(jsonNode.asText()).getString("dropJudgement");
                        if (offline != null) {
                            device.setOfflineInterval(offline);
                        }
                    }catch (Exception e){
                        log.info("没有设置掉线时长，默认15分钟");
                    }

                }
            }else {
             //从机
             Device gateway = deviceService.findDeviceById(device.getGateWayId());
                if(gateway!=null&&gateway.getAdditionalInfo()!=null) {
                    JsonNode jsonNode = gateway.getAdditionalInfo();
                    try {
                        String offline = JSONObject.parseObject(jsonNode.asText()).getString("dropJudgement");
                        if (offline != null) {
                            device.setOfflineInterval(offline);
                        }
                    }catch (Exception e){
                       log.info("没有设置掉线时长，默认15分钟");
                    }

                }
            }

            if(device.getOfflineInterval()==null){
                device.setOfflineInterval("15m");
            }
        });
        // 不记录DTU和NBDTU的掉线报警
        List<Device> list = devices.stream()
                .filter(device -> !"DTU".equals(device.getType()) && !"NBDTU".equals(device.getType()))
                .collect(Collectors.toList());
        // 不记录设置为不报警的设备
        list = list.stream().filter(device -> {
            String additionalInfo = device.getAdditionalInfo().asText();
            if (StringUtils.isNotBlank(additionalInfo)) {
                JSONObject jsonObject = JSON.parseObject(additionalInfo);
                if (jsonObject.containsKey("isAlarm") && !jsonObject.getBooleanValue("isAlarm")) {// 不报警
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        startScheduler(list,scheduler);
    }
}
