<template>
  <SLCard
    class="statistics-item"
    :title="props.title"
  >
    <template
      v-if="type === 'rate'"
      #right
    >
      <div class="top">
        <div>产销差</div>
        <div style="color: #43b530">
          {{ props.count3 }}
        </div>
      </div>
    </template>
    <div
      v-if="type === 'circle'"
      class="circle"
    >
      <div style="width: 50%; height: 100%">
        <el-progress
          type="circle"
          color="#318DFF"
          :percentage="60"
          :width="100"
        >
          <template #default>
            <el-progress
              type="circle"
              color="#70CA61"
              :percentage="80"
              :width="80"
              :show-text="false"
            />
          </template>
        </el-progress>
      </div>
      <div class="cloumn">
        <div class="column-item">
          <div class="dot dot-color1"></div>
          <div>
            <div>瞬时流量 (m³/h)</div>
            <div class="num">
              {{ props.count1 || '--' }}
            </div>
          </div>
        </div>
        <div class="column-item">
          <div class="dot dot-color2"></div>
          <div>
            <div>供水量 (m³)</div>
            <div class="num">
              {{ props.count2 || '--' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="type === 'rate'"
      class="info column"
    >
      <div class="flex-between">
        <div class="flex">
          <div class="dot dot-color1"></div>
          <div>
            {{ props.title === '当前' ? '瞬时流量(m³/h)' : '供水量(万m³)' }}
            <div class="count">
              {{ computedCounts.count1 }}
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="dot dot-color2"></div>
          <div>
            {{ props.title === '当前' ? '供水量(m³)' : '售水量(万m³)' }}
            <div class="count">
              {{ computedCounts.count2 }}
            </div>
          </div>
        </div>
      </div>
      <div class="line">
        <el-progress
          :show-text="false"
          :percentage="computedCounts.count3 || 0"
          color="#70CA61"
          :stroke-width="8"
        />
      </div>
    </div>
  </SLCard>
</template>
<script lang="ts" setup>
const props = defineProps<{
  title?: string
  count1?: number
  count2?: number
  count3?: string
  type: 'circle' | 'rate'
}>()
const computedCounts = computed(() => {
  return {
    count1: props.count1 !== undefined ? (props.count1 / 10000).toFixed(2) : '--',
    count2: props.count2 !== undefined ? (props.count2 / 10000).toFixed(2) : '--',
    count3:
      props.count3 !== undefined && props.count1 !== undefined && props.count2 !== undefined
        ? (props.count1 / (props.count1 + props.count2)) * 100
        : parseFloat(props.count3 || '100')
  }
})
</script>
<style lang="scss" scoped>
.column {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 100%;
}
.flex {
  display: flex;
  align-items: baseline;
}
.flex-between {
  display: flex;
  justify-content: space-around;
  width: 100%;
}
.statistics-item {
  width: 16%;
  height: 160px;
  border-radius: 12px;
  .top {
    width: 107.14px;
    height: 24px;
    left: 526.86px;
    top: 124px;
    background: rgba(49, 141, 255, 0.1);
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    padding: 0 12px;
    border-radius: 25px;
    line-height: 24px;
  }
  .circle {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .cloumn {
      width: 50%;
      .column-item {
        line-height: 24px;
        display: flex;
        align-items: baseline;
      }
    }
  }
  .num {
    font-weight: 600;
  }

  .dot {
    width: 8px;
    height: 8px;
    left: 179px;
    top: 160px;
    border-radius: 6px;
    margin-right: 8px;
  }

  .dot-color1 {
    background: #70ca61;
  }

  .dot-color2 {
    background: #318dff;
  }

  .count {
    font-weight: 600;
    padding-top: 8px;
  }

  :deep(.line .el-progress-bar__outer) {
    background-color: #318dff;
  }

  :deep(.sl-card-content) {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .card-left {
    padding: 20px 0;
    text-align: center;
    height: 100%;
    width: 35%;
    border-radius: 12px 0 0 12px;
    background-color: #42a5f6;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .title {
      font-size: 20px;
      font-weight: 800;
    }
  }

  .card-right {
    height: 100%;
    width: 65%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .name {
      padding-left: 20px;
      height: 40px;
      line-height: 22px;
      margin-top: 10px;

      &:nth-child(1) {
        margin-top: 0;
      }

      &:nth-child(3) {
        font-size: 14px;
      }
    }
  }
}
</style>
