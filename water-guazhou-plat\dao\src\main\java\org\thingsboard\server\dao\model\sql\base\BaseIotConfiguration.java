package org.thingsboard.server.dao.model.sql.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-物联配置对象 base_iot_configuration
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@ApiModel(value = "物联配置", description = "平台管理-物联配置实体类")
@Data
public class BaseIotConfiguration {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 设备IP地址
     */
    @ApiModelProperty(value = "设备IP地址")
    private String ipAddress;

    /**
     * 协议类型（TCP/SSL/WS/WSS）
     */
    @ApiModelProperty(value = "协议类型（TCP/SSL/WS/WSS）")
    private String listenerProtocol;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    private String listenerPort;

    /**
     * 节点名称（格式name@host）
     */
    @ApiModelProperty(value = "节点名称（格式name@host）")
    private String nodeName;

    /**
     * 集群发现方式（static/dns/k8s）
     */
    @ApiModelProperty(value = "集群发现方式（static/dns/k8s）")
    private String clusterDiscovery;
}
