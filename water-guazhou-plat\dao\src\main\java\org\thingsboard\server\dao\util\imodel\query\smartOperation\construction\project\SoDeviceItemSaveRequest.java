package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoDeviceItemSaveRequest extends SaveRequest<SoDeviceItem> {
    // ignore 所属作用域
    private SoGeneralSystemScope scope;

    // ignore 所属标识（id、code等），优先code
    private String identifier;

    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 数量
    @NotNullOrEmpty
    private Integer amount;

    @Override
    protected SoDeviceItem build() {
        SoDeviceItem entity = new SoDeviceItem();
        entity.setScope(scope);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoDeviceItem update(String id) {
        disallowUpdate();
        SoDeviceItem entity = new SoDeviceItem();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoDeviceItem entity) {
        entity.setIdentifier(identifier);
        entity.setSerialId(serialId);
        entity.setAmount(amount);
    }
}