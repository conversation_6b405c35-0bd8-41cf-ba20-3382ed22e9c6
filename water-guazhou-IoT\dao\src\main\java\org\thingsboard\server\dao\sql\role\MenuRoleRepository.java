/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.role;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.MenuRoleEntity;

import java.util.List;

public interface MenuRoleRepository extends CrudRepository<MenuRoleEntity, String> {



    @Query("SELECT mr.menuId FROM RoleEntity r, MenuRoleEntity mr, UserRoleEntity ur " +
            "WHERE r.id = mr.roleId AND r.id = ur.roleId AND ur.userId = :userId")
    List<String> findByUserId(@Param("userId") String userId);

    @Query("SELECT mr.menuId FROM MenuRoleEntity mr WHERE mr.roleId = :roleId")
    List<String> findByRoleId(@Param("roleId") String roleId);

    void deleteByRoleId(String roleId);
}
