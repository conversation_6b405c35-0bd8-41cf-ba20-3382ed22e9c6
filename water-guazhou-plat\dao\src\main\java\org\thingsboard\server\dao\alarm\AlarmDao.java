/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.alarm;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.alarm.Alarm;
import org.thingsboard.server.common.data.alarm.AlarmInfo;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AlarmQuery;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.Dao;

import java.util.List;
import java.util.UUID;

/**
 * Created by ashvayka on 11.05.17.
 */
public interface AlarmDao extends Dao<Alarm> {

    Boolean deleteAlarm(TenantId tenantId, Alarm alarm);

    ListenableFuture<Alarm> findLatestByOriginatorAndType(TenantId tenantId, EntityId originator, String type);

    ListenableFuture<Alarm> findByIdAsync(UUID key);

    Alarm save(TenantId tenantId, Alarm alarm);

    ListenableFuture<List<AlarmInfo>> findAlarms(TenantId tenantId, AlarmQuery query);

    List<Alarm> findUnClearByJsonId(AlarmJsonId id);

    List<Alarm> findClearAlarmByTenantAndLevel(TenantId tenantId, List<DeviceId> deviceId, String type, String level, String status, long start, long end);

    List<Alarm> findClearAlarmByProjectIdAndLevel(String projectId, List<DeviceId> deviceId, String type, String level, String status, long start, long end);

    ListenableFuture<List<Alarm>> findHistoryAlarm(TenantId tenantId, long start, long end);

    ListenableFuture<List<Alarm>> findRealTimeAlarm(TenantId tenantId, long start, long end);

    ListenableFuture<List<Alarm>> findRealTimeAlarm(String projectId, long start, long end);

    ListenableFuture<List<Alarm>> findHistoryByDeviceId(DeviceId deviceId, long start, long end);

    ListenableFuture<List<Alarm>> findOnlineByTypeAndDevice(DeviceId deviceId, String type);

    ListenableFuture<List<Alarm>> findOnlineByLevelAndDevice(DeviceId deviceId, String level);

    ListenableFuture<List<Alarm>> findHistoryAlarmByDevice(DeviceId deviceId, String alarmType);

    void deleteAlarmByDevice(DeviceId deviceId);

    List<Alarm> findOnlineAlarmByJsonId(AlarmJsonId id);

    ListenableFuture<List<Alarm>> findAlarmByJsonId(AlarmJsonId id);

    long getUnClearAlarm();

    long getUnClearAlarmByTenantId(TenantId tenantId);

    long getUnClearAlarmByProjectId(String projectId);

    long getAllAlarmByDeviceId(String deviceId);

    long getRealTimeAlarmByDeviceId(String deviceId);

    long getHistoryAlarmByDeviceId(String deviceId);

    ListenableFuture<List<Alarm>> findRealTimeAlarmAll(String projectId, long start, long end);

    /**
     * 获取平台下告警总数
     * @return 告警数量
     */
    long getAllAlarmByRoot();

    /**
     * 获取企业下所有报警总数
     * @param tenantId 企业ID
     * @return 企业告警数量
     */
    long getAllAlarmByTenant(TenantId tenantId);

    /**
     * 获取项目下所有报警总数
     * @param projectId 项目ID
     * @return 项目告警数量
     */
    long getAllAlarmByProject(String projectId);

    /**
     * 查找未恢复报警列表
     * @param id
     * @return
     */
    List<Alarm> findUnRestoreAlarmByJsonId(AlarmJsonId id);

    Long getAllAlarmByProjectAndTime(String projectId, Long startTime, Long endTime);

    Long getAllAlarmByTenantAndTime(TenantId tenantId, long startTime, long endTime);

    List<Alarm> findAlarmsByProjectIdInAndTs(List<String> projectIdList, long start, long end);

    ListenableFuture<List<Alarm>> findAlarmRealTimeByDeviceId(DeviceId deviceId, long start, long end);

    List<Alarm> findRealTimeAlarmByDeviceIdIn(List<String> deviceIdList, long start, long end);
}
