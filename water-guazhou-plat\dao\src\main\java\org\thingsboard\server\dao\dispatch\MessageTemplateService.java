package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageTemplatePageRequest;

public interface MessageTemplateService {
    /**
     * 分页条件查询短信模板
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<MessageTemplate> findAllConditional(MessageTemplatePageRequest request);

    /**
     * 保存短信模板
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    MessageTemplate save(MessageTemplate entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(MessageTemplate entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
