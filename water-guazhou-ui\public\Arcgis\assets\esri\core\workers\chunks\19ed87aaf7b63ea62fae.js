"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5853],{17445:(e,t,r)=>{r.d(t,{N1:()=>d,YP:()=>a,Z_:()=>y,gx:()=>u,nn:()=>f,on:()=>p,tX:()=>g});var i=r(91460),s=r(50758),n=r(70586),l=r(95330),o=r(26258);function a(e,t,r={}){return c(e,t,r,m)}function u(e,t,r={}){return c(e,t,r,h)}function c(e,t,r={},i){let s=null;const l=r.once?(e,r)=>{i(e)&&((0,n.hw)(s),t(e,r))}:(e,r)=>{i(e)&&t(e,r)};if(s=(0,o.aQ)(e,l,r.sync,r.equals),r.initial){const t=e();l(t,t)}return s}function p(e,t,r,l={}){let o=null,u=null,c=null;function p(){o&&u&&(u.remove(),l.onListenerRemove?.(o),o=null,u=null)}function d(e){l.once&&l.once&&(0,n.hw)(c),r(e)}const m=a(e,((e,r)=>{p(),(0,i.vT)(e)&&(o=e,u=(0,i.on)(e,t,d),l.onListenerAdd?.(e))}),{sync:l.sync,initial:!0});return c=(0,s.kB)((()=>{m.remove(),p()})),c}function d(e,t){return function(e,t,r){if((0,l.Hc)(r))return Promise.reject((0,l.zE)());const i=e();if(t?.(i))return Promise.resolve(i);let o=null;function a(){o=(0,n.hw)(o)}return new Promise(((i,n)=>{o=(0,s.AL)([(0,l.fu)(r,(()=>{a(),n((0,l.zE)())})),c(e,(e=>{a(),i(e)}),{sync:!1,once:!0},t??m)])}))}(e,h,t)}function m(e){return!0}function h(e){return!!e}r(87538);const y={sync:!0},f={initial:!0},g={sync:!0,initial:!0}},55853:(e,t,r)=>{r.r(t),r.d(t,{default:()=>ye});var i=r(43697),s=r(3172),n=r(46791),l=r(20102),o=r(22974),a=r(70586),u=r(16453),c=r(78286),p=r(95330),d=r(17445),m=r(17452),h=r(5600),y=(r(75215),r(71715)),f=r(52011),g=r(30556),w=r(6570),v=r(87085),x=r(16199),S=r(71612),b=r(38009),M=r(16859),C=r(34760),I=r(72965),L=r(90082),T=r(11145);class _{constructor(e,t=0,r=e.lods.length-1){this.tileInfo=e,this.minLOD=t,this.maxLOD=r}getAvailability(e,t,r){const i=this.tileInfo?.lodAt(e);return!i||e<this.minLOD||e>this.maxLOD?"unavailable":i.cols&&i.rows?r>=i.cols[0]&&r<=i.cols[1]&&t>=i.rows[0]&&t<=i.rows[1]?"available":"unavailable":"available"}async fetchAvailability(e,t,r,i){return await(0,p.Yn)(i),this.getAvailability(e,t,r)}async fetchAvailabilityUpsample(e,t,r,i,s){await(0,p.Yn)(s),i.level=e,i.row=t,i.col=r;const n=this.tileInfo;for(n.updateTileInfo(i);;){const e=this.getAvailability(i.level,i.row,i.col);if("unavailable"!==e)return e;if(!n.upsampleTile(i))return"unavailable"}}}var P,E=r(5833),R=r(96674);r(67676);let O=P=class extends R.wq{constructor(e){super(e),this.fullExtent=null,this.id=null,this.tileInfo=null}clone(){const e=new P;return this.hasOwnProperty("fullExtent")&&(e.fullExtent=this.fullExtent&&this.fullExtent.clone()),this.hasOwnProperty("id")&&(e.id=this.id),this.hasOwnProperty("tileInfo")&&(e.tileInfo=this.tileInfo&&this.tileInfo.clone()),e}};(0,i._)([(0,h.Cb)({type:w.Z,json:{read:{source:"fullExtent"}}})],O.prototype,"fullExtent",void 0),(0,i._)([(0,h.Cb)({type:String,json:{read:{source:"id"}}})],O.prototype,"id",void 0),(0,i._)([(0,h.Cb)({type:T.Z,json:{read:{source:"tileInfo"}}})],O.prototype,"tileInfo",void 0),O=P=(0,i._)([(0,f.j)("esri.layer.support.TileMatrixSet")],O);const F=O;var A;let V=A=class extends R.wq{constructor(e){super(e),this.id=null,this.title=null,this.description=null,this.legendUrl=null}clone(){const e=new A;return this.hasOwnProperty("description")&&(e.description=this.description),this.hasOwnProperty("id")&&(e.id=this.id),this.hasOwnProperty("isDefault")&&(e.isDefault=this.isDefault),this.hasOwnProperty("keywords")&&(e.keywords=this.keywords&&this.keywords.slice()),this.hasOwnProperty("legendUrl")&&(e.legendUrl=this.legendUrl),this.hasOwnProperty("title")&&(e.title=this.title),e}};(0,i._)([(0,h.Cb)({json:{read:{source:"id"}}})],V.prototype,"id",void 0),(0,i._)([(0,h.Cb)({json:{read:{source:"title"}}})],V.prototype,"title",void 0),(0,i._)([(0,h.Cb)({json:{read:{source:"abstract"}}})],V.prototype,"description",void 0),(0,i._)([(0,h.Cb)({json:{read:{source:"legendUrl"}}})],V.prototype,"legendUrl",void 0),(0,i._)([(0,h.Cb)({json:{read:{source:"isDefault"}}})],V.prototype,"isDefault",void 0),(0,i._)([(0,h.Cb)({json:{read:{source:"keywords"}}})],V.prototype,"keywords",void 0),V=A=(0,i._)([(0,f.j)("esri.layer.support.WMTSStyle")],V);const j=V;var U;let N=U=class extends R.wq{constructor(e){super(e),this.fullExtent=null,this.fullExtents=null,this.imageFormats=null,this.id=null,this.layer=null,this.styles=null,this.tileMatrixSetId=null,this.tileMatrixSets=null}get description(){return this._get("description")}set description(e){this._set("description",e)}readFullExtent(e,t){return(e=t.fullExtent)?w.Z.fromJSON(e):null}readFullExtents(e,t){return t.fullExtents?.length?t.fullExtents.map((e=>w.Z.fromJSON(e))):t.tileMatrixSets?.map((e=>w.Z.fromJSON(e.fullExtent))).filter((e=>e))??[]}get imageFormat(){let e=this._get("imageFormat");return e||(e=this.imageFormats&&this.imageFormats.length?this.imageFormats[0]:""),e}set imageFormat(e){const t=this.imageFormats;e&&(e.includes("image/")||t&&!t.includes(e))&&(e.includes("image/")||(e="image/"+e),t&&!t.includes(e))?console.error("The layer doesn't support the format of "+e):this._set("imageFormat",e)}get styleId(){let e=this._get("styleId");return e||(e=this.styles?.length?this.styles.getItemAt(0).id:""),e}set styleId(e){this._set("styleId",e)}get title(){return this._get("title")}set title(e){this._set("title",e)}get tileMatrixSet(){return this.tileMatrixSets?this.tileMatrixSets.find((e=>e.id===this.tileMatrixSetId)):null}clone(){const e=new U;return this.hasOwnProperty("description")&&(e.description=this.description),this.hasOwnProperty("imageFormats")&&(e.imageFormats=this.imageFormats&&this.imageFormats.slice()),this.hasOwnProperty("imageFormat")&&(e.imageFormat=this.imageFormat),this.hasOwnProperty("fullExtent")&&(e.fullExtent=this.fullExtent&&this.fullExtent.clone()),this.hasOwnProperty("id")&&(e.id=this.id),this.hasOwnProperty("layer")&&(e.layer=this.layer),this.hasOwnProperty("styleId")&&(e.styleId=this.styleId),this.hasOwnProperty("styles")&&(e.styles=this.styles&&this.styles.clone()),this.hasOwnProperty("tileMatrixSetId")&&(e.tileMatrixSetId=this.tileMatrixSetId),this.hasOwnProperty("tileMatrixSets")&&(e.tileMatrixSets=this.tileMatrixSets?.clone()),this.hasOwnProperty("title")&&(e.title=this.title),e}};(0,i._)([(0,h.Cb)()],N.prototype,"description",null),(0,i._)([(0,h.Cb)()],N.prototype,"fullExtent",void 0),(0,i._)([(0,y.r)("fullExtent",["fullExtent"])],N.prototype,"readFullExtent",null),(0,i._)([(0,h.Cb)({readOnly:!0})],N.prototype,"fullExtents",void 0),(0,i._)([(0,y.r)("fullExtents",["fullExtents","tileMatrixSets"])],N.prototype,"readFullExtents",null),(0,i._)([(0,h.Cb)()],N.prototype,"imageFormat",null),(0,i._)([(0,h.Cb)({json:{read:{source:"formats"}}})],N.prototype,"imageFormats",void 0),(0,i._)([(0,h.Cb)()],N.prototype,"id",void 0),(0,i._)([(0,h.Cb)()],N.prototype,"layer",void 0),(0,i._)([(0,h.Cb)()],N.prototype,"styleId",null),(0,i._)([(0,h.Cb)({type:n.Z.ofType(j),json:{read:{source:"styles"}}})],N.prototype,"styles",void 0),(0,i._)([(0,h.Cb)({value:null,json:{write:{ignoreOrigin:!0}}})],N.prototype,"title",null),(0,i._)([(0,h.Cb)()],N.prototype,"tileMatrixSetId",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],N.prototype,"tileMatrixSet",null),(0,i._)([(0,h.Cb)({type:n.Z.ofType(F),json:{read:{source:"tileMatrixSets"}}})],N.prototype,"tileMatrixSets",void 0),N=U=(0,i._)([(0,f.j)("esri.layers.support.WMTSSublayer")],N);const Z=N;var k=r(2109),D=r(94139),W=r(58116),B=r(52162),K=r(88724),$=r(39450);const q=90.71428571428571;function Y(e){const t=e.replace(/ows:/gi,"");if(!H("Contents",(new DOMParser).parseFromString(t,"text/xml").documentElement))throw new l.Z("wmtslayer:wmts-capabilities-xml-is-not-valid","the wmts get capabilities response is not compliant",{text:e})}function G(e){return e.nodeType===Node.ELEMENT_NODE}function H(e,t){for(let r=0;r<t.childNodes.length;r++){const i=t.childNodes[r];if(G(i)&&i.nodeName===e)return i}return null}function J(e,t){const r=[];for(let i=0;i<t.childNodes.length;i++){const s=t.childNodes[i];G(s)&&s.nodeName===e&&r.push(s)}return r}function z(e,t){const r=[];for(let i=0;i<t.childNodes.length;i++){const s=t.childNodes[i];G(s)&&s.nodeName===e&&r.push(s)}return r.map((e=>e.textContent)).filter(a.pC)}function Q(e,t){return e.split(">").forEach((e=>{t&&(t=H(e,t))})),t&&t.textContent}function X(e,t,r,i){let s;return Array.prototype.slice.call(i.childNodes).some((i=>{if(i.nodeName.includes(e)){const e=H(t,i),n=e&&e.textContent;if(n===r||r.split(":")&&r.split(":")[1]===n)return s=i,!0}return!1})),s}function ee(e,t,r,i,s){const n=Q("Abstract",t),l=z("Format",t);return{id:e,fullExtent:re(t),fullExtents:ie(t),description:n,formats:l,styles:se(t,i),title:Q("Title",t),tileMatrixSets:ne(s,t,r)}}function te(e,t){const r=[],i=e.layerMap?.get(t);if(!i)return null;const s=J("ResourceURL",i),n=J("Dimension",i);let l,o,a,u;return n.length&&(l=Q("Identifier",n[0]),o=z("Default",n[0])||z("Value",n[0])),n.length>1&&(a=Q("Identifier",n[1]),u=z("Default",n[1])||z("Value",n[1])),e.dimensionMap.set(t,{dimensions:o,dimensions2:u}),s.forEach((e=>{let t=e.getAttribute("template");if("tile"===e.getAttribute("resourceType")){if(l&&o.length)if(t.includes("{"+l+"}"))t=t.replace("{"+l+"}","{dimensionValue}");else{const e=t.toLowerCase().indexOf("{"+l.toLowerCase()+"}");e>-1&&(t=t.substring(0,e)+"{dimensionValue}"+t.substring(e+l.length+2))}if(a&&u.length)if(t.includes("{"+a+"}"))t=t.replace("{"+a+"}","{dimensionValue2}");else{const e=t.toLowerCase().indexOf("{"+a.toLowerCase()+"}");e>-1&&(t=t.substring(0,e)+"{dimensionValue2}"+t.substring(e+a.length+2))}r.push({template:t,format:e.getAttribute("format"),resourceType:"tile"})}})),r}function re(e){const t=H("WGS84BoundingBox",e),r=t?Q("LowerCorner",t).split(" "):["-180","-90"],i=t?Q("UpperCorner",t).split(" "):["180","90"];return{xmin:parseFloat(r[0]),ymin:parseFloat(r[1]),xmax:parseFloat(i[0]),ymax:parseFloat(i[1]),spatialReference:{wkid:4326}}}function ie(e){const t=[];return(0,K.h)(e,{BoundingBox:e=>{if(!e.getAttribute("crs"))return;const r=e.getAttribute("crs").toLowerCase(),i=le(r),s=r.includes("epsg")&&(0,B.A)(i.wkid);let n,l,o,a;(0,K.h)(e,{LowerCorner:e=>{[n,l]=e.textContent.split(" ").map((e=>Number.parseFloat(e))),s&&([n,l]=[l,n])},UpperCorner:e=>{[o,a]=e.textContent.split(" ").map((e=>Number.parseFloat(e))),s&&([o,a]=[a,o])}}),t.push({xmin:n,ymin:l,xmax:o,ymax:a,spatialReference:i})}}),t}function se(e,t){return J("Style",e).map((e=>{const r=H("LegendURL",e),i=H("Keywords",e),s=i?z("Keyword",i):[];let n=r&&r.getAttribute("xlink:href");return t&&(n=n&&n.replace(/^http:/i,"https:")),{abstract:Q("Abstract",e),id:Q("Identifier",e),isDefault:"true"===e.getAttribute("isDefault"),keywords:s,legendUrl:n,title:Q("Title",e)}}))}function ne(e,t,r){return J("TileMatrixSetLink",t).map((t=>function(e,t,r){const i=H("TileMatrixSet",t).textContent,s=z("TileMatrix",t),n=r.find((e=>{const t=H("Identifier",e),r=t&&t.textContent;return!!(r===i||i.split(":")&&i.split(":")[1]===r)})),l=H("TileMatrixSetLimits",t),o=l&&J("TileMatrixLimits",l),a=new Map;if(o?.length)for(const e of o){const t=H("TileMatrix",e).textContent,r=+H("MinTileRow",e).textContent,i=+H("MaxTileRow",e).textContent,s=+H("MinTileCol",e).textContent,n=+H("MaxTileCol",e).textContent;a.set(t,{minCol:s,maxCol:n,minRow:r,maxRow:i})}const u=Q("SupportedCRS",n).toLowerCase(),c=function(e,t){return oe(H("TileMatrix",e),t)}(n,u),p=c.spatialReference,d=H("TileMatrix",n),m=[parseInt(Q("TileWidth",d),10),parseInt(Q("TileHeight",d),10)],h=[];s.length?s.forEach(((e,t)=>{const r=X("TileMatrix","Identifier",e,n);h.push(ce(r,u,t,i,a))})):J("TileMatrix",n).forEach(((e,t)=>{h.push(ce(e,u,t,i,a))}));const y=function(e,t,r,i,s){const n=H("BoundingBox",t);let l,o,a,u,c,p;if(n&&(l=Q("LowerCorner",n).split(" "),o=Q("UpperCorner",n).split(" ")),l&&l.length>1&&o&&o.length>1)a=parseFloat(l[0]),c=parseFloat(l[1]),u=parseFloat(o[0]),p=parseFloat(o[1]);else{const e=H("TileMatrix",t),n=parseInt(Q("MatrixWidth",e),10),l=parseInt(Q("MatrixHeight",e),10);a=r.x,p=r.y,u=a+n*i[0]*s.resolution,c=p-l*i[1]*s.resolution}return function(e,t,r){return"1.0.0"===e&&(0,B.A)(t.wkid)&&!(r.spatialReference.isGeographic&&r.x<-90&&r.y>=-90)}(e,r.spatialReference,r)?new w.Z(c,a,p,u,r.spatialReference):new w.Z(a,c,u,p,r.spatialReference)}(e,n,c,m,h[0]).toJSON(),f=new T.Z({dpi:96,spatialReference:p,size:m,origin:c,lods:h}).toJSON();return{id:i,fullExtent:y,tileInfo:f}}(e,t,r)))}function le(e){e=e.toLowerCase();let t=parseInt(e.split(":").pop(),10);900913!==t&&3857!==t||(t=102100);const r=function(e){return e.includes("crs84")||e.includes("crs:84")?ae.CRS84:e.includes("crs83")||e.includes("crs:83")?ae.CRS83:e.includes("crs27")||e.includes("crs:27")?ae.CRS27:null}(e);return(0,a.pC)(r)&&(t=r),{wkid:t}}function oe(e,t){const r=le(t),[i,s]=Q("TopLeftCorner",e).split(" ").map((e=>parseFloat(e))),n=t.includes("epsg")&&(0,B.A)(r.wkid);return new D.Z(n?{x:s,y:i,spatialReference:r}:{x:i,y:s,spatialReference:r})}var ae,ue;function ce(e,t,r,i,s){const n=le(t),l=Q("Identifier",e);let o=parseFloat(Q("ScaleDenominator",e));const a=pe(n.wkid,o,i);o*=96/q;const u=+Q("MatrixWidth",e),c=+Q("MatrixHeight",e),{maxCol:p=u-1,maxRow:d=c-1,minCol:m=0,minRow:h=0}=s.get(l)??{},{x:y,y:f}=oe(e,t);return new $.Z({cols:[m,p],level:r,levelValue:l,origin:[y,f],scale:o,resolution:a,rows:[h,d]})}function pe(e,t,r){let i;return i=W.Z.hasOwnProperty(""+e)?W.Z.values[W.Z[e]]:"default028mm"===r?6370997*Math.PI/180:(0,k.e8)(e).metersPerDegree,7*t/25e3/i}(ue=ae||(ae={}))[ue.CRS84=4326]="CRS84",ue[ue.CRS83=4269]="CRS83",ue[ue.CRS27=4267]="CRS27";const de={"image/png":".png","image/png8":".png","image/png24":".png","image/png32":".png","image/jpg":".jpg","image/jpeg":".jpeg","image/gif":".gif","image/bmp":".bmp","image/tiff":".tif","image/jpgpng":"","image/jpegpng":"","image/unknown":""},me=new Set(["version","service","request","layer","style","format","tilematrixset","tilematrix","tilerow","tilecol"]);let he=class extends((0,S.h)((0,C.Q)((0,I.M)((0,b.q)((0,M.I)((0,u.R)(v.Z))))))){constructor(...e){super(...e),this.copyright="",this.customParameters=null,this.customLayerParameters=null,this.fullExtent=null,this.operationalLayerType="WebTiledLayer",this.resourceInfo=null,this.serviceMode="RESTful",this.sublayers=null,this.type="wmts",this.version="1.0.0",this.addHandles([(0,d.YP)((()=>this.activeLayer),((e,t)=>{t&&(t.layer=null),e&&(e.layer=this)}),d.Z_),(0,d.on)((()=>this.sublayers),"after-add",(({item:e})=>{e.layer=this}),d.Z_),(0,d.on)((()=>this.sublayers),"after-remove",(({item:e})=>{e.layer=null}),d.Z_),(0,d.YP)((()=>this.sublayers),((e,t)=>{if(t)for(const e of t)e.layer=null;if(e)for(const t of e)t.layer=this}),d.Z_)])}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){if("KVP"===this.serviceMode||"RESTful"===this.serviceMode)return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["WMTS"]},e).catch(p.r9).then((()=>this._fetchService(e))).catch((e=>{throw(0,p.r9)(e),new l.Z("wmtslayer:unsupported-service-data","Invalid response from the WMTS service.",{error:e})}))),Promise.resolve(this);console.error("WMTS mode could only be 'KVP' or 'RESTful'")}get activeLayer(){return this._get("activeLayer")}set activeLayer(e){this._set("activeLayer",e)}readActiveLayerFromService(e,t,r){this.activeLayer||(this.activeLayer=new Z);let i=t.layers.find((e=>e.id===this.activeLayer.id));return i||(i=t.layers[0]),this.activeLayer.read(i,r),this.activeLayer}readActiveLayerFromItemOrWebDoc(e,t){const{templateUrl:r,wmtsInfo:i}=t,s=r?this._getLowerCasedUrlParams(r):null,n=i?.layerIdentifier;let l=null;const o=i?.tileMatrixSet;o&&(Array.isArray(o)?o.length&&(l=o[0]):l=o);const a=s?.format,u=s?.style;return new Z({id:n,imageFormat:a,styleId:u,tileMatrixSetId:l})}writeActiveLayer(e,t,r,i){const s=this.activeLayer;t.templateUrl=this.getUrlTemplate(s.id,s.tileMatrixSetId,s.imageFormat,s.styleId);const n=(0,c.hS)("tileMatrixSet.tileInfo",s);t.tileInfo=n?n.toJSON(i):null,t.wmtsInfo={...t.wmtsInfo,layerIdentifier:s.id,tileMatrixSet:s.tileMatrixSetId}}readCustomParameters(e,t){const r=t.wmtsInfo;return r?this._mergeParams(r.customParameters,r.url):null}get fullExtents(){return this.activeLayer.fullExtents}readServiceMode(e,t){return t.templateUrl.includes("?")?"KVP":"RESTful"}readSublayersFromService(e,t,r){return function(e,t){return e.map((e=>{const r=new Z;return r.read(e,t),r}))}(t.layers,r)}get supportedSpatialReferences(){return this.activeLayer.tileMatrixSets?.map((e=>e.tileInfo?.spatialReference)).toArray().filter(a.pC)??[]}get tilemapCache(){const e=this.activeLayer?.tileMatrixSet?.tileInfo;return e?new _(e):void 0}get title(){return this.activeLayer?.title??"Layer"}set title(e){this._overrideIfSome("title",e)}get url(){return this._get("url")}set url(e){e&&"/"===e.substr(-1)?this._set("url",e.slice(0,-1)):this._set("url",e)}createWebTileLayer(e){const t=this.getUrlTemplate(this.activeLayer.id,this.activeLayer.tileMatrixSetId,this.activeLayer.imageFormat,this.activeLayer.styleId),r=this._getTileMatrixSetById(e.tileMatrixSetId)?.tileInfo,i=e.fullExtent,s=new E.B({layerIdentifier:e.id,tileMatrixSet:e.tileMatrixSetId,url:this.url});return this.customLayerParameters&&(s.customLayerParameters=this.customLayerParameters),this.customParameters&&(s.customParameters=this.customParameters),new x.default({fullExtent:i,urlTemplate:t,tileInfo:r,wmtsInfo:s})}async fetchTile(e,t,r){const i=this.getTileUrl(e,t,r),{data:n}=await(0,s.default)(i,{responseType:"image"});return n}async fetchImageBitmapTile(e,t,r){const i=this.getTileUrl(e,t,r),{data:n}=await(0,s.default)(i,{responseType:"blob"});return(0,L.g)(n,i)}findSublayerById(e){return this.sublayers?.find((t=>t.id===e))}getTileUrl(e,t,r){const i=this._getTileMatrixSetById(this.activeLayer.tileMatrixSetId)?.tileInfo?.lods[e],s=i?i.levelValue?i.levelValue:`${i.level}`:`${e}`;let n=this.resourceInfo?"":function(e,t,r,i,s,n,l,o){const a=function(e,t,r){const i=te(e,t),s=i?.filter((e=>e.format===r));return(s?.length?s:i)??[]}(e,t,i);if(!(a?.length>0))return"";const{dimensionMap:u}=e,c=u.get(t).dimensions?.[0],p=u.get(t).dimensions2?.[0];return a[l%a.length].template.replace(/\{Style\}/gi,s??"").replace(/\{TileMatrixSet\}/gi,r??"").replace(/\{TileMatrix\}/gi,n).replace(/\{TileRow\}/gi,""+l).replace(/\{TileCol\}/gi,""+o).replace(/\{dimensionValue\}/gi,c).replace(/\{dimensionValue2\}/gi,p)}({dimensionMap:this.dimensionMap,layerMap:this.layerMap},this.activeLayer.id,this.activeLayer.tileMatrixSetId,this.activeLayer.imageFormat,this.activeLayer.styleId,s,t,r);return n||(n=this.getUrlTemplate(this.activeLayer.id,this.activeLayer.tileMatrixSetId,this.activeLayer.imageFormat,this.activeLayer.styleId).replace(/\{level\}/gi,s).replace(/\{row\}/gi,`${t}`).replace(/\{col\}/gi,`${r}`)),n=this._appendCustomLayerParameters(n),n}getUrlTemplate(e,t,r,i){if(!this.resourceInfo){const r=function(e,t,r,i){const{dimensionMap:s}=e,n=te(e,t);let l="";if(n&&n.length>0){const e=s.get(t).dimensions&&s.get(t).dimensions[0],o=s.get(t).dimensions2&&s.get(t).dimensions2[0];l=n[0].template,l.indexOf(".xxx")===l.length-4&&(l=l.slice(0,l.length-4)),l=l.replace(/\{Style\}/gi,i),l=l.replace(/\{TileMatrixSet\}/gi,r),l=l.replace(/\{TileMatrix\}/gi,"{level}"),l=l.replace(/\{TileRow\}/gi,"{row}"),l=l.replace(/\{TileCol\}/gi,"{col}"),l=l.replace(/\{dimensionValue\}/gi,e),l=l.replace(/\{dimensionValue2\}/gi,o)}return l}({dimensionMap:this.dimensionMap,layerMap:this.layerMap},e,t,i);if(r)return r}if("KVP"===this.serviceMode)return this.url+"?SERVICE=WMTS&VERSION="+this.version+"&REQUEST=GetTile&LAYER="+e+"&STYLE="+i+"&FORMAT="+r+"&TILEMATRIXSET="+t+"&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}";if("RESTful"===this.serviceMode){let s="";return de[r.toLowerCase()]&&(s=de[r.toLowerCase()]),this.url+e+"/"+i+"/"+t+"/{level}/{row}/{col}"+s}return""}async _fetchService(e){let t;if(this.resourceInfo)"KVP"===this.resourceInfo.serviceMode&&(this.url+=this.url.includes("?")?"":"?"),t={ssl:!1,data:this.resourceInfo};else try{t=await this._getCapabilities(this.serviceMode,e),Y(t.data)}catch{const r="KVP"===this.serviceMode?"RESTful":"KVP";try{t=await this._getCapabilities(r,e),Y(t.data),this.serviceMode=r}catch(e){throw new l.Z("wmtslayer:unsupported-service-data","Services does not support RESTful or KVP service modes.",{error:e})}}this.resourceInfo?t.data=function(e){return e.layers.forEach((e=>{e.tileMatrixSets?.forEach((e=>{const t=e.tileInfo;t&&96!==t.dpi&&(t.lods?.forEach((r=>{r.scale=96*r.scale/t.dpi,r.resolution=pe(t.spatialReference?.wkid,r.scale*q/96,e.id)})),t.dpi=96)}))})),e}(t.data):t.data=function(e,t){e=e.replace(/ows:/gi,"");const r=(new DOMParser).parseFromString(e,"text/xml").documentElement,i=new Map,s=new Map,n=H("Contents",r);if(!n)throw new l.Z("wmtslayer:wmts-capabilities-xml-is-not-valid");const o=H("OperationsMetadata",r)?.querySelector("[name='GetTile']"),a=o?.getElementsByTagName("Get"),u=a&&Array.prototype.slice.call(a),c=t.url?.indexOf("https"),p=void 0!==c&&c>-1;let d,m,h=t.serviceMode,y=t?.url;if(u&&u.length&&u.some((e=>{const t=H("Constraint",e);return!t||X("AllowedValues","Value",h,t)?(y=e.attributes[0].nodeValue,!0):(!t||X("AllowedValues","Value","RESTful",t)||X("AllowedValues","Value","REST",t)?m=e.attributes[0].nodeValue:t&&!X("AllowedValues","Value","KVP",t)||(d=e.attributes[0].nodeValue),!1)})),!y)if(m)y=m,h="RESTful";else if(d)y=d,h="KVP";else{const e=H("ServiceMetadataURL",r);y=e?.getAttribute("xlink:href")}const f=y.indexOf("1.0.0/");-1===f&&"RESTful"===h?y+="/":f>-1&&(y=y.substring(0,f)),"KVP"===h&&(y+=f>-1?"":"?"),p&&(y=y.replace(/^http:/i,"https:"));const g=Q("ServiceIdentification>ServiceTypeVersion",r),w=Q("ServiceIdentification>AccessConstraints",r),v=w&&/^none$/i.test(w)?null:w,x=J("Layer",n),S=J("TileMatrixSet",n),b=x.map((e=>{const t=Q("Identifier",e);return i.set(t,e),ee(t,e,S,p,g)}));return{copyright:v,dimensionMap:s,layerMap:i,layers:b,serviceMode:h,tileUrl:y}}(t.data,{serviceMode:this.serviceMode,url:this.url}),t.data&&this.read(t.data,{origin:"service"})}async _getCapabilities(e,t){const r=this._getCapabilitiesUrl(e);return await(0,s.default)(r,{...t,responseType:"text"})}_getTileMatrixSetById(e){const t=this.findSublayerById(this.activeLayer.id)?.tileMatrixSets?.find((t=>t.id===e));return t}_appendCustomParameters(e){return this._appendParameters(e,this.customParameters)}_appendCustomLayerParameters(e){return this._appendParameters(e,{...(0,o.d9)(this.customParameters),...this.customLayerParameters})}_appendParameters(e,t){const r=(0,m.mN)(e),i={...r.query,...t},s=(0,m.B7)(i);return""===s?r.path:`${r.path}?${s}`}_getCapabilitiesUrl(e){this.url=this.url.split("?")[0];const t="KVP"===e?`${this.url}?request=GetCapabilities&service=WMTS&version=${this.version}`:`${this.url}/${this.version}/WMTSCapabilities.xml`;return this._appendCustomParameters(t)}_getLowerCasedUrlParams(e){if(!e)return null;const t=(0,m.mN)(e).query;if(!t)return null;const r={};return Object.keys(t).forEach((e=>{r[e.toLowerCase()]=t[e]})),r}_mergeParams(e,t){const r=this._getLowerCasedUrlParams(t);if(r){const t=Object.keys(r);t.length&&(e=e?(0,o.d9)(e):{},t.forEach((t=>{e.hasOwnProperty(t)||me.has(t)||(e[t]=r[t])})))}return e}};(0,i._)([(0,h.Cb)()],he.prototype,"dimensionMap",void 0),(0,i._)([(0,h.Cb)()],he.prototype,"layerMap",void 0),(0,i._)([(0,h.Cb)({type:Z,json:{origins:{"web-document":{write:{ignoreOrigin:!0}}}}})],he.prototype,"activeLayer",null),(0,i._)([(0,y.r)("service","activeLayer",["layers"])],he.prototype,"readActiveLayerFromService",null),(0,i._)([(0,y.r)(["web-document","portal-item"],"activeLayer",["wmtsInfo"])],he.prototype,"readActiveLayerFromItemOrWebDoc",null),(0,i._)([(0,g.c)(["web-document","portal-item"],"activeLayer",{templateUrl:{type:String},tileInfo:{type:T.Z},"wmtsInfo.layerIdentifier":{type:String},"wmtsInfo.tileMatrixSet":{type:String}})],he.prototype,"writeActiveLayer",null),(0,i._)([(0,h.Cb)({type:String,value:"",json:{write:!0}})],he.prototype,"copyright",void 0),(0,i._)([(0,h.Cb)({type:["show","hide"]})],he.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)({json:{read:!0,write:!0}})],he.prototype,"blendMode",void 0),(0,i._)([(0,h.Cb)({json:{origins:{"web-document":{read:{source:["wmtsInfo.customParameters","wmtsInfo.url"]},write:{target:"wmtsInfo.customParameters"}},"portal-item":{read:{source:["wmtsInfo.customParameters","wmtsInfo.url"]},write:{target:"wmtsInfo.customParameters"}}}}})],he.prototype,"customParameters",void 0),(0,i._)([(0,y.r)(["portal-item","web-document"],"customParameters")],he.prototype,"readCustomParameters",null),(0,i._)([(0,h.Cb)({json:{origins:{"web-document":{read:{source:"wmtsInfo.customLayerParameters"},write:{target:"wmtsInfo.customLayerParameters"}},"portal-item":{read:{source:"wmtsInfo.customLayerParameters"},write:{target:"wmtsInfo.customLayerParameters"}}}}})],he.prototype,"customLayerParameters",void 0),(0,i._)([(0,h.Cb)({type:w.Z,json:{write:{ignoreOrigin:!0},origins:{"web-document":{read:{source:"fullExtent"}},"portal-item":{read:{source:"fullExtent"}}}}})],he.prototype,"fullExtent",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],he.prototype,"fullExtents",null),(0,i._)([(0,h.Cb)({type:["WebTiledLayer"]})],he.prototype,"operationalLayerType",void 0),(0,i._)([(0,h.Cb)()],he.prototype,"resourceInfo",void 0),(0,i._)([(0,h.Cb)()],he.prototype,"serviceMode",void 0),(0,i._)([(0,y.r)(["portal-item","web-document"],"serviceMode",["templateUrl"])],he.prototype,"readServiceMode",null),(0,i._)([(0,h.Cb)({type:n.Z.ofType(Z)})],he.prototype,"sublayers",void 0),(0,i._)([(0,y.r)("service","sublayers",["layers"])],he.prototype,"readSublayersFromService",null),(0,i._)([(0,h.Cb)({readOnly:!0})],he.prototype,"supportedSpatialReferences",null),(0,i._)([(0,h.Cb)({readOnly:!0})],he.prototype,"tilemapCache",null),(0,i._)([(0,h.Cb)({json:{read:{source:"title"}}})],he.prototype,"title",null),(0,i._)([(0,h.Cb)({json:{read:!1},readOnly:!0,value:"wmts"})],he.prototype,"type",void 0),(0,i._)([(0,h.Cb)({json:{origins:{service:{read:{source:"tileUrl"}},"web-document":{read:{source:"wmtsInfo.url"},write:{target:"wmtsInfo.url"}},"portal-item":{read:{source:"wmtsInfo.url"},write:{target:"wmtsInfo.url"}}}}})],he.prototype,"url",null),(0,i._)([(0,h.Cb)()],he.prototype,"version",void 0),he=(0,i._)([(0,f.j)("esri.layers.WMTSLayer")],he);const ye=he},52162:(e,t,r)=>{r.d(t,{A:()=>s});const i=[[3819,3819],[3821,3824],[3889,3889],[3906,3906],[4001,4025],[4027,4036],[4039,4047],[4052,4055],[4074,4075],[4080,4081],[4120,4176],[4178,4185],[4188,4216],[4218,4289],[4291,4304],[4306,4319],[4322,4326],[4463,4463],[4470,4470],[4475,4475],[4483,4483],[4490,4490],[4555,4558],[4600,4646],[4657,4765],[4801,4811],[4813,4821],[4823,4824],[4901,4904],[5013,5013],[5132,5132],[5228,5229],[5233,5233],[5246,5246],[5252,5252],[5264,5264],[5324,5340],[5354,5354],[5360,5360],[5365,5365],[5370,5373],[5381,5381],[5393,5393],[5451,5451],[5464,5464],[5467,5467],[5489,5489],[5524,5524],[5527,5527],[5546,5546],[2044,2045],[2081,2083],[2085,2086],[2093,2093],[2096,2098],[2105,2132],[2169,2170],[2176,2180],[2193,2193],[2200,2200],[2206,2212],[2319,2319],[2320,2462],[2523,2549],[2551,2735],[2738,2758],[2935,2941],[2953,2953],[3006,3030],[3034,3035],[3038,3051],[3058,3059],[3068,3068],[3114,3118],[3126,3138],[3150,3151],[3300,3301],[3328,3335],[3346,3346],[3350,3352],[3366,3366],[3389,3390],[3416,3417],[3833,3841],[3844,3850],[3854,3854],[3873,3885],[3907,3910],[4026,4026],[4037,4038],[4417,4417],[4434,4434],[4491,4554],[4839,4839],[5048,5048],[5105,5130],[5253,5259],[5269,5275],[5343,5349],[5479,5482],[5518,5519],[5520,5520],[20004,20032],[20064,20092],[21413,21423],[21473,21483],[21896,21899],[22171,22177],[22181,22187],[22191,22197],[25884,25884],[27205,27232],[27391,27398],[27492,27492],[28402,28432],[28462,28492],[30161,30179],[30800,30800],[31251,31259],[31275,31279],[31281,31290],[31466,31700]];function s(e){return null!=e&&i.some((([t,r])=>e>=t&&e<=r))}},88724:(e,t,r)=>{function i(e,t){if(e&&t)for(const r of e.children)if(r.localName in t){const e=t[r.localName];if("function"==typeof e){const t=e(r);t&&i(r,t)}else i(r,e)}}function*s(e,t){for(const r of e.children)if(r.localName in t){const e=t[r.localName];"function"==typeof e?yield e(r):yield*s(r,e)}}r.d(t,{H:()=>s,h:()=>i})}}]);