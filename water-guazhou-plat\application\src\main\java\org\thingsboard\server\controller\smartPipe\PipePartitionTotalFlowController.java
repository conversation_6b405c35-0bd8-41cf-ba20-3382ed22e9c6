package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.DTO.PartitionFlowAnalysisDTO;
import org.thingsboard.server.dao.model.DTO.PartitionSupplyCorrectRecordsDTO;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.request.PartitionTotalFlowRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow;
import org.thingsboard.server.dao.smartPipe.PipePartitionTotalFlowService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 智慧管网-分区供水量
 */
@RestController
@RequestMapping("api/spp/partitionTotalFlow")
public class PipePartitionTotalFlowController extends BaseController {

    @Autowired
    private PipePartitionTotalFlowService pipePartitionTotalFlowService;

    @GetMapping("list")
    public IstarResponse getListByPartitionId(PartitionTotalFlowRequest request) {
        return IstarResponse.ok(pipePartitionTotalFlowService.getList(request));
    }

    @GetMapping("listExport")
    public void getListByPartitionIdExport(PartitionTotalFlowRequest request, HttpServletResponse response) {
        List<PipePartitionTotalFlow> data = pipePartitionTotalFlowService.getList(request).getData();

        Map headMap = new LinkedHashMap();
        headMap.put("partitionName", "分区名称");
        headMap.put("deviceName", "水表名称");
        headMap.put("collectTime", "日期");
        headMap.put("value", "供水量");
        headMap.put("correctWater", "追加水量");
        JSONArray array = JSONObject.parseArray(JSONObject.toJSONString(data));

        ExcelUtil.exportExcelX("供水量修正", headMap, array, "yyyy-MM-dd HH:mm:ss", 20, false, response);

    }

    @PostMapping("correct")
    public IstarResponse getListByPartitionId(@RequestBody PipePartitionTotalFlow pipePartitionTotalFlow) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        pipePartitionTotalFlow.setTenantId(tenantId);

        pipePartitionTotalFlowService.correct(pipePartitionTotalFlow, userId);

        return IstarResponse.ok();
    }

    @GetMapping("correctRecords")
    public IstarResponse getCorrectRecords(PartitionCustRequest partitionCustRequest) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);

        return IstarResponse.ok(pipePartitionTotalFlowService.getCorrectRecords(partitionCustRequest));
    }

    @GetMapping("correctRecordsExport")
    public void getCorrectRecordsExport(PartitionCustRequest partitionCustRequest, HttpServletResponse response) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionCustRequest.setTenantId(tenantId);

        List<PartitionSupplyCorrectRecordsDTO> data = pipePartitionTotalFlowService.getCorrectRecords(partitionCustRequest).getData();
        Map headMap = new LinkedHashMap();
        headMap.put("partitionName", "分区名称");
        headMap.put("deviceName", "水表名称");
        headMap.put("collectTime", "日期");
        headMap.put("correctWater", "追加水量");
        headMap.put("createTime", "修正日期");
        headMap.put("updateUser", "操作人");
        JSONArray array = JSONObject.parseArray(JSONObject.toJSONString(data));

        ExcelUtil.exportExcelX("供水量修正记录", headMap, array, "yyyy-MM-dd HH:mm:ss", 20, false, response);

    }

    @PostMapping("flowAnalysis")
    public IstarResponse getFlowAnalysis(@RequestBody PartitionTotalFlowRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(pipePartitionTotalFlowService.getFlowAnalysis(request));
    }

    @PostMapping("flowAnalysisExport")
    public void getFlowAnalysisExport(@RequestBody PartitionTotalFlowRequest request, HttpServletResponse response) throws ThingsboardException {
        request.setPage(1);
        request.setSize(Integer.MAX_VALUE);
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        List<PartitionFlowAnalysisDTO> dataList = pipePartitionTotalFlowService.getFlowAnalysis(request).getData();
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        Map headMap = new LinkedHashMap();
        headMap.put("time", "时间");
        JSONArray jsonArray = new JSONArray();
        JSONObject object;
        if (dataList.size() > 0) {
            List<JSONObject> data = dataList.get(0).getData();
            for (JSONObject jsonObject : data) {
                headMap.put(jsonObject.getString("name"), jsonObject.getString("name") + "(m³/h)");
            }
            for (PartitionFlowAnalysisDTO partitionFlowAnalysisDTO : dataList) {
                object = new JSONObject();
                data = partitionFlowAnalysisDTO.getData();
                for (JSONObject jsonObject : data) {
                    object.put(jsonObject.getString("name"), jsonObject.get("value"));
                    object.put("avgValue", partitionFlowAnalysisDTO.getAvgValue());
                }

                object.put("avgValue", partitionFlowAnalysisDTO.getAvgValue());
                object.put("maxPoint", partitionFlowAnalysisDTO.getMaxPoint());
                object.put("maxValue", partitionFlowAnalysisDTO.getMaxValue());
                object.put("minPoint", partitionFlowAnalysisDTO.getMinPoint());
                object.put("minValue", partitionFlowAnalysisDTO.getMinValue());
                object.put("sumValue", partitionFlowAnalysisDTO.getSumValue());
                object.put("time", partitionFlowAnalysisDTO.getTime());
                jsonArray.add(object);
            }
        } else {
            headMap.put("avgValue", "平均值(m³/h)");
            headMap.put("maxPoint", "最大站点");
            headMap.put("maxValue", "最大值(m³/h)");
            headMap.put("minPoint", "最小站点");
            headMap.put("minValue", "最小值(m³/h)");
            headMap.put("sumValue", "合计(m³)");

            switch (request.getQueryType()) {
                case "totalFlow":
                    headMap.put("avgValue", "平均值(m³)");
                    headMap.put("maxValue", "最大值(m³)");
                    headMap.put("minValue", "最小值(m³)");
                    break;
                case "pressure":
                    headMap.put("avgValue", "平均值(Mpa)");
                    headMap.put("maxValue", "最大值(Mpa)");
                    headMap.put("minValue", "最小值(Mpa)");
                    headMap.put("sumValue", "合计(Mpa)");
            }
        }
        SXSSFWorkbook workbook = new SXSSFWorkbook();

        String title = "流量分析";
        switch (request.getQueryType()) {
            case "totalFlow":
                title = "水量分析";
                break;
            case "pressure":
                title = "压力分析";
        }

        ExcelUtil.exportExcelXManySheet(title, headMap, jsonArray, "yyyyMMddHHmmss", 20, title, workbook);

        ByteArrayOutputStream os = new ByteArrayOutputStream();

        try {
            workbook.write(os);
            workbook.close();
            workbook.dispose();

            ExcelUtil.exportExcel(title, os, response);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
