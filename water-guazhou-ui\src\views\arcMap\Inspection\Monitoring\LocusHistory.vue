<!-- 历史轨迹 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="历史轨迹"
    :hide-coords="false"
    :hide-search="true"
    :hide-layer-list="true"
    @map-loaded="onMaploaded"
  >
    <Form ref="refForm" :config="FormConfig"></Form>
    <template #map-bars>
      <div class="locusdisplay">
        <div class="audioplay">
          <span>轨迹播放</span>
          <el-input
            v-model="audioInfo.speed"
            size="small"
            style="width: 100px; margin-left: auto"
            @change="locus.setSpeed(audioInfo.speed)"
          >
            <template #append>
              <span>m/s</span>
            </template>
          </el-input>
          <Icon
            :icon="locus.running.value ? 'ep:video-pause' : 'ep:video-play'"
            @click="
              () =>
                locus.running.value
                  ? locus.pause()
                  : locus.start(0, audioInfo.speed)
            "
          ></Icon>
          <Icon :icon="'mdi:stop-circle-outline'" @click="locus.stop"></Icon>
        </div>
        <div class="userinfo">
          <el-collapse v-model="audioInfo.activeNames">
            <el-collapse-item title="人员任务信息">
              <!-- <div class="user-task-item">
                <span>去噪点</span>:
                <div class="item-content slider">
                  <el-slider
                    v-model="audioInfo.slideVal"
                    size="small"
                  ></el-slider>
                </div>
              </div> -->
              <div class="user-task-item">
                <span>姓名</span>:
                <div class="item-content">
                  {{ audioInfo.curUser?.label }}
                </div>
              </div>
              <div class="user-task-item">
                <span>日期</span>:
                <div class="item-content">
                  <el-date-picker
                    v-model="audioInfo.dateVal"
                    type="date"
                    style="width: 100%"
                    placeholder="选择日期"
                    size="small"
                    @change="refreshUserLocas"
                  />
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item title="人员信息">
              <div class="person-info-tablebox">
                <FormTable :config="TableConfig"></FormTable>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="locusinfo">
        <div v-for="(item, i) in locusInfo" :key="i" class="locusinfo-item">
          <div class="circle">
            {{ item.value === undefined ? '--' : item.value }}
          </div>
          <div class="circle_title">
            {{ item.title }}
          </div>
          <div class="unit">
            {{ item.unit }}
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point';
import { queryLayerClassName } from '@/api/mapservice';
import { calcLength, getGraphicLayer, getSubLayerIds } from '@/utils/MapHelper';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import { getUserList } from '@/api/user';
import { getWaterSupplyTree } from '@/api/company_org';
import { formatterDateTime, formatTree } from '@/utils/GlobalHelper';
import { GetUserHostoryLocas } from '@/api/patrol';
import { useLocus } from '@/hooks/arcgis';
import { removeSlash } from '@/utils/removeIdSlash';
import locationIcon from '@/assets/images/user_location.png';
import { Icon } from '@iconify/vue';
import { Refresh,Search as SearchIcon } from '@element-plus/icons-vue';

const refForm = ref<IFormIns>();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
} = {};
const state = reactive<{
  tabs: any[];
  loading: boolean;
  layerIds: number[];
  layerInfos: any[];
  curLocas?: any;
}>({
  tabs: [],
  loading: false,
  layerIds: [],
  layerInfos: []
});
const audioInfo = reactive<{
  speed: number;
  curUser?: NormalOption;
  activeNames: string[];
  slideVal: number;
  dateVal: string;
}>({
  speed: 100,
  activeNames: [],
  slideVal: 0,
  dateVal: moment().format('YYYY-MM-DD')
});
const locusInfo = reactive<{ title: string; unit: string; value: number }[]>([
  { title: '总里程', unit: 'km', value: 0 },
  { title: '总时长', unit: 'h', value: 0 },
  { title: '平均速度', unit: 'km/h', value: 0 }
]);
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          field: 'pid',
          options: [],
          defaultExpandAll: true,
          checkStrictly: true,
          onChange: () => refreshData(),
          extraFormItem: [
            {
              type: 'input',
              field: 'name',
              prefixIcon: shallowRef(SearchIcon),
              appendBtns: [
                {
                  perm: true,
                  svgIcon: shallowRef(Refresh),
                  click: () => refreshData()
                }
              ],
              onChange: () => refreshData()
            }
          ]
        },
        {
          type: 'tree',
          field: 'person',
          options: [],
          onChange: () => refreshUserLocas()
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {}
});
const TableConfig = reactive<ITable>({
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    { label: '日期', prop: 'createTime' },
    { label: 'X坐标', prop: 'lon' },
    { label: 'Y坐标', prop: 'lat' }
  ]
});
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view);
  const layerInfo = await queryLayerClassName(state.layerIds);
  state.layerInfos = layerInfo.data?.result?.rows || [];
};
const refreshUserLocas = () => {
  const person = refForm.value?.dataForm.person?.[0];
  TableConfig.dataList = [];
  if (!person) return;
  const tree = FormConfig.group[0].fields[1] as IFormTree;
  const user = tree.options.find((item) => item.value === person);
  audioInfo.curUser = user;
  locus.stop();
  GetUserHostoryLocas({
    userId: removeSlash(person),
    page: 1,
    size: 999999,
    fromTime: moment(audioInfo.dateVal, formatterDateTime)
      .startOf('D')
      .valueOf(),
    toTime: moment(audioInfo.dateVal, formatterDateTime).endOf('D').valueOf()
  })
    .then((res) => {
      TableConfig.dataList = res.data.data?.data?.map((item) => {
        const [lon, lat] = item.coordinate?.split(',') || [];
        return {
          ...item,
          lon: parseFloat(lon),
          lat: parseFloat(lat)
        };
      });
      const point = TableConfig.dataList.map((item) => {
        return new Point({
          longitude: item.lon,
          latitude: item.lat,
          spatialReference: staticState.view?.spatialReference
        });
      });
      const coords = point.map((item) => {
        return [item.x, item.y];
      });
      console.log(coords);
      const totalLength = calcLength(
        coords,
        'kilometers',
        staticState.view?.spatialReference
      );
      locusInfo[0].value = Number(totalLength.toFixed(2) || '0');
      if (TableConfig.dataList.length > 1) {
        const lastRowTime =
          TableConfig.dataList[TableConfig.dataList.length - 1].createTime;
        const endTime = moment(lastRowTime, formatterDateTime).valueOf();
        const firstRowTime = TableConfig.dataList[0].createTime;
        const startTime = moment(firstRowTime, formatterDateTime).valueOf();
        const ms = endTime - startTime;
        locusInfo[1].value = Number((ms / 1000 / 60 / 60).toFixed(2));
        locusInfo[2].value = Number(
          ms === 0 ? 0 : (totalLength / ms).toFixed(2)
        );
      } else {
        locusInfo[1].value = 0;
        locusInfo[2].value = 0;
      }

      locus.init({
        mapView: staticState.view,
        path: coords,
        pic: {
          width: 20,
          height: 20,
          yoffset: 10,
          url: locationIcon
        }
      });
    })
    .catch(() => {
      TableConfig.dataList = [];
    });
};
const locus = useLocus();

// const togglePlay = () => {
//   if (!TableConfig.dataList.length) locus.running.value = true
//   else locus.running.value = !locus.running.value
// }
const refreshData = () => {
  const query = refForm.value?.dataForm || {};
  getUserList({
    page: 1,
    size: 999999,
    pid: query.pid,
    name: query.name
  }).then((res) => {
    const select = FormConfig.group[0].fields[1] as IFormTree;
    select.options = res.data.data?.data?.map((item) => {
      return {
        id: item.id.id,
        value: item.id.id,
        label: item.firstName,
        data: item
      };
    });
  });
};
const initOrganazation = () => {
  getWaterSupplyTree(2).then((res) => {
    const select = FormConfig.group[0].fields[0] as ISelectTree;
    select.options = formatTree(res.data.data || []);
    refForm.value && (refForm.value.dataForm.pid = select.options?.[0]?.value);
    refreshData();
  });
};

const onMaploaded = async (view) => {
  staticState.graphicsLayer = getGraphicLayer(view, {
    id: 'locus-line',
    title: '轨迹'
  });
  staticState.view = view;
  initOrganazation();
  await getLayerInfo();
};
</script>
<style lang="scss" scoped>
.dark {
  .locusdisplay,
  .locusinfo {
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);
  }
}
.person-info-tablebox {
  height: 300px;
}
.locusdisplay,
.locusinfo {
  background-color: #fff;
  color: #333;
}
// .darkblue {
//   .locusdisplay,
//   .locusinfo {
//     color: #fff;
//     background-color: rgba(21, 45, 68, 0.9);
//   }
// }
.locusdisplay {
  position: absolute;
  right: 15px;
  top: 12px;
  width: 330px;
  padding: 0 8px;
  .audioplay {
    display: flex;
    align-items: center;
    border-bottom: var(--el-border-color);
    padding: 8px 0;
    :deep(.el-input-group__append) {
      padding: 0 8px;
    }
    .el-icon {
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .userinfo {
    .user-task-item {
      & > span {
        width: 40px;
        display: inline-block;
        text-align: justify;
        vertical-align: top;
        &::after {
          content: '';
          display: inline-block;
          width: 100%;
        }
      }
      .item-content {
        margin-left: 20px;
        width: calc(100% - 80px);
        display: inline-block;
        vertical-align: top;
        clear: both;
        &.slider {
          padding-left: 8px;
        }
      }
    }
  }
}
.locusinfo {
  border-radius: 20px;
  position: absolute;
  left: 50%;
  bottom: 15px;
  transform: translateX(-50%);
  width: 500px;
  height: 200px;
  display: flex;
  overflow: hidden;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 12px 12px;
  justify-content: space-around;
  .locusinfo-item {
    text-align: center;
    .circle {
      height: 110px;
      width: 110px;
      border-radius: 50%;
      border: 1px solid var(--el-text-color-primary);
      display: grid;
      place-items: center;
      margin: 12px;
    }
    .unit {
      font-size: 12px;
    }
    .circle_title {
      font-size: 16px;
      line-height: 25px;
    }
  }
}
</style>
