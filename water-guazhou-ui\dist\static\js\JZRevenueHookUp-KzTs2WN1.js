import{d as R,c as x,r as u,o as N,g as w,n as U,p as r,q as i,i as d,F as p,cs as m,G as C,b as l,aq as T,J as S,C as B}from"./index-r0dFAfgr.js";import{_ as O}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import{G as q,a as G,p as W}from"./userManage-E__vPxsL.js";import"./printUtils-C-AxhDcd.js";const A={class:"manual-hookuo-revenue"},J={class:"left"},P={class:"center"},V={class:"btn"},E={class:"btn"},F={class:"btn"},H={class:"right"},Z=R({__name:"JZRevenueHookUp",props:{currentTreeNode:{}},setup(L){const h=x(),f=L,_=u({data:[]}),b=u({filters:[{type:"input",label:"名称",field:"MeterReadingNO"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var e;const t=(e=h.value)==null?void 0:e.queryParams.MeterReadingNO.trim();n.dataList=_.data.filter(s=>s.MeterReadingNO.includes(t))}}]}]}),n=u({height:400,pagination:{hide:!0},dataList:[],columns:[{minWidth:120,label:"ID",prop:"MeterReadingID",fixed:"left"},{minWidth:120,label:"名称",prop:"MeterReadingNO",fixed:"left"}],handleSelectChange:t=>{n.selectList=t||[]}}),a=u({height:400,pagination:{hide:!0},dataList:[],columns:[{minWidth:120,label:"ID",prop:"MeterReadingID",fixed:"left"},{minWidth:120,label:"名称",prop:"MeterReadingNO",fixed:"left"}],handleSelectChange:t=>{a.selectList=t||[]}});function y(t,e){return t.filter(s=>e.includes(s.MeterReadingID))}function v(t,e){const s=new Map,o=[];for(const c of t)s.has(c[e])||(s.set(c[e],!0),o.push(c));return o}const k=async()=>{n.loading=!0;try{const e=(await q()).data.data||{};_.data=e||[],n.dataList=e||[],g()}catch{}n.loading=!1},g=async()=>{var t;a.loading=!0;try{const s=((await G((t=f.currentTreeNode)==null?void 0:t.value)).data.data.bookId||"").split(",");a.dataList=y(n.dataList,s)}catch{}a.loading=!1},M=async()=>{const t=n.selectList||[];if(!t.length){l.warning("请选择要挂接的用户");return}try{a.dataList=v([...a.dataList,...t],"MeterReadingID"),n.selectList=[]}catch{l.error("操作失败")}},D=async()=>{const t=a.selectList||[];if(!t.length){l.warning("请先选择要取消挂接的用户");return}try{a.dataList=a.dataList.filter(e=>!t.includes(e)),a.selectList=[]}catch{l.error("操作失败")}},I=async()=>{var e,s;const t={id:((e=f.currentTreeNode)==null?void 0:e.id)||"",bookId:a.dataList.map(o=>o.MeterReadingID).join(","),name:((s=f.currentTreeNode)==null?void 0:s.label)||""};W(t).then(o=>{o.data.code==200&&(l.success("操作成功"),g())})};return N(()=>{k()}),(t,e)=>{const s=O,o=T,c=S;return w(),U("div",A,[r("div",J,[e[0]||(e[0]=r("div",{class:"title"},"总数据",-1)),i(s,{ref_key:"refSearchLeft",ref:h,class:"search",config:d(b)},null,8,["config"]),i(o,{class:"table-box",config:d(n)},null,8,["config"])]),r("div",P,[r("div",V,[i(c,{type:"primary",onClick:M},{default:p(()=>[i(d(m),{icon:"ep:d-arrow-right"})]),_:1})]),r("div",E,[i(c,{type:"primary",onClick:D},{default:p(()=>[i(d(m),{icon:"ep:d-arrow-left"})]),_:1})]),r("div",F,[i(c,{type:"success",onClick:I},{default:p(()=>e[1]||(e[1]=[C(" 保存 ")])),_:1})])]),r("div",H,[e[2]||(e[2]=r("div",{class:"title"},"已挂接数据",-1)),i(o,{class:"table-box",config:d(a)},null,8,["config"])])])}}}),Y=B(Z,[["__scopeId","data-v-ac870c14"]]);export{Y as default};
