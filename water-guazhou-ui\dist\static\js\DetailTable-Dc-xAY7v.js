import{d as E,ad as B,r as O,g as j,n as q,q as N,i as $,b as z,aq as H,C as U}from"./index-r0dFAfgr.js";import{g as S,v as T,b as v,u as V}from"./MapView-DaoQedLH.js";import{w as W}from"./Point-WxyopZva.js";import{s as x,g as K}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Q}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";const X={class:"detail-table"},Y=E({__name:"DetailTable",props:{operations:{}},emits:["row-click","refreshData"],setup(D,{expose:F,emit:G}){const f=B("view"),u=G,R=D,p={tabFeatures:[]},e=O({dataList:[],columns:[{label:"序号",prop:"index"}],handleRowClick:async t=>{u("row-click",t)},operations:R.operations,pagination:{refreshData:({page:t,size:r})=>{e.pagination.page=t,e.pagination.limit=r,u("refreshData")}}}),M=async(t,r,o)=>{var i,m,y;try{if(p.hilightLayer=Q(t||f,{id:"pipe-detail",title:"详情展示"}),(o==null?void 0:o.page)!==void 0&&(e.pagination.page=o.page||1),e.loading=!0,!(r==null?void 0:r.layername)){e.dataList=[],e.pagination.total=0,e.loading=!1,(i=p.hilightLayer)==null||i.removeAll();return}const c=(r==null?void 0:r.oids)||[];e.pagination.total=c.length||0;const C=e.pagination.page||1,w=e.pagination.limit||20,_=(C-1)*w,I=Math.min(_+w,c.length),g=c.slice(_,I),L=[],k=[];debugger;if(g.length>0){const a=g[0];a&&a.properties&&Object.keys(a.properties).forEach(n=>{const s={label:n,prop:n,minWidth:160};k.push(s)}),g.forEach(n=>{n.properties&&L.push(n.properties)}),e.columns=k}e.dataList=L,(m=p.hilightLayer)==null||m.removeAll(),p.tabFeatures=c;try{const a=[];for(const n of c)if(n.geometry)try{const s=h(n.geometry);if(s)try{const l=s.type,d=x(l);if(!d){console.warn("无法为类型创建符号:",l);continue}const P=new S({geometry:s,attributes:n.properties,symbol:d});n.symbol=d,a.push(P)}catch(l){console.error("创建图形对象时出错:",l)}}catch(s){console.error("处理要素几何时出错:",s)}a.length>0?(console.log("添加高亮图层要素:",a.length),(y=p.hilightLayer)==null||y.addMany(a)):console.warn("没有可用的高亮图层要素")}catch(a){console.error("GeoServer模式下添加高亮图层时出错:",a)}}catch(b){console.dir(b),z.error("查询失败")}e.loading=!1},A=async(t=f,r)=>{if(!t)return;let o;if(o=p.tabFeatures.find(i=>i.properties&&i.properties.OBJECTID===r),o)try{let i=h(o.geometry);if(!i){console.error("无法创建ArcGIS几何对象:",o.geometry);return}debugger;const m=new S({geometry:i,symbol:x(i.type)});await K(t,m)}catch(i){console.error("高亮显示要素时出错:",i)}},h=t=>{switch(t.type){case"Point":return new W({x:t.coordinates[0],y:t.coordinates[1],spatialReference:{wkid:3857}});case"MultiPoint":return new V({points:t.coordinates,spatialReference:{wkid:3857}});case"LineString":return new v({paths:[t.coordinates],spatialReference:{wkid:3857}});case"MultiLineString":return new v({paths:t.coordinates,spatialReference:{wkid:3857}});case"Polygon":return new T({rings:t.coordinates,spatialReference:{wkid:3857}});case"MultiPolygon":return new T({rings:t.coordinates.reduce((r,o)=>r.concat(o),[]),spatialReference:{wkid:3857}});default:return console.error("Unsupported GeoJSON type:",t.type),null}};return F({extentTo:A,refreshDetail:M,clearTable:()=>{e.dataList=[],e.pagination.total=0},staticState:p,TableConfig_Detail:e}),(t,r)=>{const o=H;return j(),q("div",X,[N(o,{config:$(e)},null,8,["config"])])}}}),ge=U(Y,[["__scopeId","data-v-1f28c9d0"]]);export{ge as default};
