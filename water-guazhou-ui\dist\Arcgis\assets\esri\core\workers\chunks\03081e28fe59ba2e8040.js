"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1984],{92835:(t,e,n)=>{n.d(e,{Z:()=>p});var i,s=n(43697),r=n(96674),o=n(70586),a=n(35463),l=n(5600),h=(n(75215),n(67676),n(71715)),c=n(52011),u=n(30556);let d=i=class extends r.wq{static get allTime(){return m}static get empty(){return f}constructor(t){super(t),this.end=null,this.start=null}readEnd(t,e){return null!=e.end?new Date(e.end):null}writeEnd(t,e){e.end=t?t.getTime():null}get isAllTime(){return this.equals(i.allTime)}get isEmpty(){return this.equals(i.empty)}readStart(t,e){return null!=e.start?new Date(e.start):null}writeStart(t,e){e.start=t?t.getTime():null}clone(){return new i({end:this.end,start:this.start})}equals(t){if(!t)return!1;const e=(0,o.pC)(this.start)?this.start.getTime():this.start,n=(0,o.pC)(this.end)?this.end.getTime():this.end,i=(0,o.pC)(t.start)?t.start.getTime():t.start,s=(0,o.pC)(t.end)?t.end.getTime():t.end;return e===i&&n===s}expandTo(t){if(this.isEmpty||this.isAllTime)return this.clone();const e=(0,o.yw)(this.start,(e=>(0,a.JE)(e,t))),n=(0,o.yw)(this.end,(e=>{const n=(0,a.JE)(e,t);return e.getTime()===n.getTime()?n:(0,a.Nm)(n,1,t)}));return new i({start:e,end:n})}intersection(t){if(!t)return this.clone();if(this.isEmpty||t.isEmpty)return i.empty;if(this.isAllTime)return t.clone();if(t.isAllTime)return this.clone();const e=(0,o.R2)(this.start,-1/0,(t=>t.getTime())),n=(0,o.R2)(this.end,1/0,(t=>t.getTime())),s=(0,o.R2)(t.start,-1/0,(t=>t.getTime())),r=(0,o.R2)(t.end,1/0,(t=>t.getTime()));let a,l;if(s>=e&&s<=n?a=s:e>=s&&e<=r&&(a=e),n>=s&&n<=r?l=n:r>=e&&r<=n&&(l=r),null!=a&&null!=l&&!isNaN(a)&&!isNaN(l)){const t=new i;return t.start=a===-1/0?null:new Date(a),t.end=l===1/0?null:new Date(l),t}return i.empty}offset(t,e){if(this.isEmpty||this.isAllTime)return this.clone();const n=new i,{start:s,end:r}=this;return(0,o.pC)(s)&&(n.start=(0,a.Nm)(s,t,e)),(0,o.pC)(r)&&(n.end=(0,a.Nm)(r,t,e)),n}union(t){if(!t||t.isEmpty)return this.clone();if(this.isEmpty)return t.clone();if(this.isAllTime||t.isAllTime)return m.clone();const e=(0,o.pC)(this.start)&&(0,o.pC)(t.start)?new Date(Math.min(this.start.getTime(),t.start.getTime())):null,n=(0,o.pC)(this.end)&&(0,o.pC)(t.end)?new Date(Math.max(this.end.getTime(),t.end.getTime())):null;return new i({start:e,end:n})}};(0,s._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"end",void 0),(0,s._)([(0,h.r)("end")],d.prototype,"readEnd",null),(0,s._)([(0,u.c)("end")],d.prototype,"writeEnd",null),(0,s._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isAllTime",null),(0,s._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isEmpty",null),(0,s._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"start",void 0),(0,s._)([(0,h.r)("start")],d.prototype,"readStart",null),(0,s._)([(0,u.c)("start")],d.prototype,"writeStart",null),d=i=(0,s._)([(0,c.j)("esri.TimeExtent")],d);const m=new d,f=new d({start:void 0,end:void 0}),p=d},5732:(t,e,n)=>{n.d(e,{c:()=>i});var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},21787:(t,e,n)=>{n.d(e,{a:()=>l,b:()=>c,e:()=>a,f:()=>s,i:()=>d,m:()=>h,s:()=>r,t:()=>o});var i=n(46851);function s(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t}function r(t,e,n,i,s,r,o,a,l,h){return t[0]=e,t[1]=n,t[2]=i,t[3]=s,t[4]=r,t[5]=o,t[6]=a,t[7]=l,t[8]=h,t}function o(t,e){if(t===e){const n=e[1],i=e[2],s=e[5];t[1]=e[3],t[2]=e[6],t[3]=n,t[5]=e[7],t[6]=i,t[7]=s}else t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8];return t}function a(t,e){const n=e[0],i=e[1],s=e[2],r=e[3],o=e[4],a=e[5],l=e[6],h=e[7],c=e[8],u=c*o-a*h,d=-c*r+a*l,m=h*r-o*l;let f=n*u+i*d+s*m;return f?(f=1/f,t[0]=u*f,t[1]=(-c*i+s*h)*f,t[2]=(a*i-s*o)*f,t[3]=d*f,t[4]=(c*n-s*l)*f,t[5]=(-a*n+s*r)*f,t[6]=m*f,t[7]=(-h*n+i*l)*f,t[8]=(o*n-i*r)*f,t):null}function l(t,e){const n=e[0],i=e[1],s=e[2],r=e[3],o=e[4],a=e[5],l=e[6],h=e[7],c=e[8];return t[0]=o*c-a*h,t[1]=s*h-i*c,t[2]=i*a-s*o,t[3]=a*l-r*c,t[4]=n*c-s*l,t[5]=s*r-n*a,t[6]=r*h-o*l,t[7]=i*l-n*h,t[8]=n*o-i*r,t}function h(t,e,n){const i=e[0],s=e[1],r=e[2],o=e[3],a=e[4],l=e[5],h=e[6],c=e[7],u=e[8],d=n[0],m=n[1],f=n[2],p=n[3],_=n[4],g=n[5],y=n[6],x=n[7],v=n[8];return t[0]=d*i+m*o+f*h,t[1]=d*s+m*a+f*c,t[2]=d*r+m*l+f*u,t[3]=p*i+_*o+g*h,t[4]=p*s+_*a+g*c,t[5]=p*r+_*l+g*u,t[6]=y*i+x*o+v*h,t[7]=y*s+x*a+v*c,t[8]=y*r+x*l+v*u,t}function c(t,e){const n=e[0],i=e[1],s=e[2],r=e[3],o=e[4],a=e[5],l=e[6],h=e[7],c=e[8],u=e[9],d=e[10],m=e[11],f=e[12],p=e[13],_=e[14],g=e[15],y=n*a-i*o,x=n*l-s*o,v=n*h-r*o,b=i*l-s*a,M=i*h-r*a,w=s*h-r*l,C=c*p-u*f,P=c*_-d*f,R=c*g-m*f,E=u*_-d*p,I=u*g-m*p,O=d*g-m*_;let B=y*O-x*I+v*E+b*R-M*P+w*C;return B?(B=1/B,t[0]=(a*O-l*I+h*E)*B,t[1]=(l*R-o*O-h*P)*B,t[2]=(o*I-a*R+h*C)*B,t[3]=(s*I-i*O-r*E)*B,t[4]=(n*O-s*R+r*P)*B,t[5]=(i*R-n*I-r*C)*B,t[6]=(p*w-_*M+g*b)*B,t[7]=(_*v-f*w-g*x)*B,t[8]=(f*M-p*v+g*y)*B,t):null}function u(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t}function d(t){const e=(0,i.g)(),n=t[0],s=t[1],r=t[2],o=t[3],a=t[4],l=t[5],h=t[6],c=t[7],u=t[8];return Math.abs(1-(n*n+o*o+h*h))<=e&&Math.abs(1-(s*s+a*a+c*c))<=e&&Math.abs(1-(r*r+l*l+u*u))<=e}const m=h,f=u;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t},adjoint:l,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t},determinant:function(t){const e=t[0],n=t[1],i=t[2],s=t[3],r=t[4],o=t[5],a=t[6],l=t[7],h=t[8];return e*(h*r-o*l)+n*(-h*s+o*a)+i*(l*s-r*a)},equals:function(t,e){const n=t[0],s=t[1],r=t[2],o=t[3],a=t[4],l=t[5],h=t[6],c=t[7],u=t[8],d=e[0],m=e[1],f=e[2],p=e[3],_=e[4],g=e[5],y=e[6],x=e[7],v=e[8],b=(0,i.g)();return Math.abs(n-d)<=b*Math.max(1,Math.abs(n),Math.abs(d))&&Math.abs(s-m)<=b*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(r-f)<=b*Math.max(1,Math.abs(r),Math.abs(f))&&Math.abs(o-p)<=b*Math.max(1,Math.abs(o),Math.abs(p))&&Math.abs(a-_)<=b*Math.max(1,Math.abs(a),Math.abs(_))&&Math.abs(l-g)<=b*Math.max(1,Math.abs(l),Math.abs(g))&&Math.abs(h-y)<=b*Math.max(1,Math.abs(h),Math.abs(y))&&Math.abs(c-x)<=b*Math.max(1,Math.abs(c),Math.abs(x))&&Math.abs(u-v)<=b*Math.max(1,Math.abs(u),Math.abs(v))},exactEquals:function(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[7]===e[7]&&t[8]===e[8]},frob:function(t){return Math.sqrt(t[0]**2+t[1]**2+t[2]**2+t[3]**2+t[4]**2+t[5]**2+t[6]**2+t[7]**2+t[8]**2)},fromMat2d:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=0,t[3]=e[2],t[4]=e[3],t[5]=0,t[6]=e[4],t[7]=e[5],t[8]=1,t},fromMat4:s,fromQuat:function(t,e){const n=e[0],i=e[1],s=e[2],r=e[3],o=n+n,a=i+i,l=s+s,h=n*o,c=i*o,u=i*a,d=s*o,m=s*a,f=s*l,p=r*o,_=r*a,g=r*l;return t[0]=1-u-f,t[3]=c-g,t[6]=d+_,t[1]=c+g,t[4]=1-h-f,t[7]=m-p,t[2]=d-_,t[5]=m+p,t[8]=1-h-u,t},fromRotation:function(t,e){const n=Math.sin(e),i=Math.cos(e);return t[0]=i,t[1]=n,t[2]=0,t[3]=-n,t[4]=i,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromScaling:function(t,e){return t[0]=e[0],t[1]=0,t[2]=0,t[3]=0,t[4]=e[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromTranslation:function(t,e){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=e[0],t[7]=e[1],t[8]=1,t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},invert:a,isOrthoNormal:d,mul:m,multiply:h,multiplyScalar:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t},multiplyScalarAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t[2]=e[2]+n[2]*i,t[3]=e[3]+n[3]*i,t[4]=e[4]+n[4]*i,t[5]=e[5]+n[5]*i,t[6]=e[6]+n[6]*i,t[7]=e[7]+n[7]*i,t[8]=e[8]+n[8]*i,t},normalFromMat4:c,normalFromMat4Legacy:function(t,e){const n=e[0],i=e[1],s=e[2],r=e[4],o=e[5],a=e[6],l=e[8],h=e[9],c=e[10],u=c*o-a*h,d=-c*r+a*l,m=h*r-o*l,f=n*u+i*d+s*m;if(!f)return null;const p=1/f;return t[0]=u*p,t[1]=(-c*i+s*h)*p,t[2]=(a*i-s*o)*p,t[3]=d*p,t[4]=(c*n-s*l)*p,t[5]=(-a*n+s*r)*p,t[6]=m*p,t[7]=(-h*n+i*l)*p,t[8]=(o*n-i*r)*p,t},projection:function(t,e,n){return t[0]=2/e,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/n,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t},rotate:function(t,e,n){const i=e[0],s=e[1],r=e[2],o=e[3],a=e[4],l=e[5],h=e[6],c=e[7],u=e[8],d=Math.sin(n),m=Math.cos(n);return t[0]=m*i+d*o,t[1]=m*s+d*a,t[2]=m*r+d*l,t[3]=m*o-d*i,t[4]=m*a-d*s,t[5]=m*l-d*r,t[6]=h,t[7]=c,t[8]=u,t},scale:function(t,e,n){const i=n[0],s=n[1],r=n[2];return t[0]=i*e[0],t[1]=i*e[1],t[2]=i*e[2],t[3]=s*e[3],t[4]=s*e[4],t[5]=s*e[5],t[6]=r*e[6],t[7]=r*e[7],t[8]=r*e[8],t},scaleByVec2:function(t,e,n){const i=n[0],s=n[1];return t[0]=i*e[0],t[1]=i*e[1],t[2]=i*e[2],t[3]=s*e[3],t[4]=s*e[4],t[5]=s*e[5],t},set:r,str:function(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"},sub:f,subtract:u,translate:function(t,e,n){const i=e[0],s=e[1],r=e[2],o=e[3],a=e[4],l=e[5],h=e[6],c=e[7],u=e[8],d=n[0],m=n[1];return t[0]=i,t[1]=s,t[2]=r,t[3]=o,t[4]=a,t[5]=l,t[6]=d*i+m*o+h,t[7]=d*s+m*a+c,t[8]=d*r+m*l+u,t},transpose:o},Symbol.toStringTag,{value:"Module"}))},46521:(t,e,n)=>{function i(){return[1,0,0,0,1,0,0,0,1]}function s(t,e,n,i,s,r,o,a,l){return[t,e,n,i,s,r,o,a,l]}function r(t,e){return new Float64Array(t,e,9)}n.d(e,{a:()=>r,c:()=>i,f:()=>s}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},create:i,createView:r,fromValues:s},Symbol.toStringTag,{value:"Module"}))},88764:(t,e,n)=>{n.d(e,{q:()=>l});var i,s,r,o={},a={get exports(){return o},set exports(t){o=t}};i=a,s=function(){function t(n,i,s,r,o){for(;r>s;){if(r-s>600){var a=r-s+1,l=i-s+1,h=Math.log(a),c=.5*Math.exp(2*h/3),u=.5*Math.sqrt(h*c*(a-c)/a)*(l-a/2<0?-1:1);t(n,i,Math.max(s,Math.floor(i-l*c/a+u)),Math.min(r,Math.floor(i+(a-l)*c/a+u)),o)}var d=n[i],m=s,f=r;for(e(n,s,i),o(n[r],d)>0&&e(n,s,r);m<f;){for(e(n,m,f),m++,f--;o(n[m],d)<0;)m++;for(;o(n[f],d)>0;)f--}0===o(n[s],d)?e(n,s,f):e(n,++f,r),f<=i&&(s=f+1),i<=f&&(r=f-1)}}function e(t,e,n){var i=t[e];t[e]=t[n],t[n]=i}function n(t,e){return t<e?-1:t>e?1:0}return function(e,i,s,r,o){t(e,i,s||0,r||e.length-1,o||n)}},void 0!==(r=s())&&(i.exports=r);const l=o},4307:(t,e,n)=>{n.d(e,{d:()=>l,l:()=>d,r:()=>f,s:()=>s,t:()=>m});var i=n(46851);function s(t,e,n){return t[0]=e,t[1]=n,t}function r(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function o(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t}function a(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t}function l(t,e){const n=e[0]-t[0],i=e[1]-t[1];return Math.sqrt(n*n+i*i)}function h(t,e){const n=e[0]-t[0],i=e[1]-t[1];return n*n+i*i}function c(t){const e=t[0],n=t[1];return Math.sqrt(e*e+n*n)}function u(t){const e=t[0],n=t[1];return e*e+n*n}function d(t,e,n,i){const s=e[0],r=e[1];return t[0]=s+i*(n[0]-s),t[1]=r+i*(n[1]-r),t}function m(t,e,n){const i=e[0],s=e[1];return t[0]=n[0]*i+n[2]*s+n[4],t[1]=n[1]*i+n[3]*s+n[5],t}function f(t,e,n,i){const s=e[0]-n[0],r=e[1]-n[1],o=Math.sin(i),a=Math.cos(i);return t[0]=s*a-r*o+n[0],t[1]=s*o+r*a+n[1],t}const p=c,_=r,g=o,y=a,x=l,v=h,b=u;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t},angle:function(t,e){const n=t[0],i=t[1],s=e[0],r=e[1];let o=n*n+i*i;o>0&&(o=1/Math.sqrt(o));let a=s*s+r*r;a>0&&(a=1/Math.sqrt(a));const l=(n*s+i*r)*o*a;return l>1?0:l<-1?Math.PI:Math.acos(l)},ceil:function(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},cross:function(t,e,n){const i=e[0]*n[1]-e[1]*n[0];return t[0]=t[1]=0,t[2]=i,t},dist:x,distance:l,div:y,divide:a,dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},equals:function(t,e){const n=t[0],s=t[1],r=e[0],o=e[1],a=(0,i.g)();return Math.abs(n-r)<=a*Math.max(1,Math.abs(n),Math.abs(r))&&Math.abs(s-o)<=a*Math.max(1,Math.abs(s),Math.abs(o))},exactEquals:function(t,e){return t[0]===e[0]&&t[1]===e[1]},floor:function(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t},inverse:function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t},len:p,length:c,lerp:d,max:function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t},min:function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t},mul:g,multiply:o,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:function(t,e){const n=e[0],i=e[1];let s=n*n+i*i;return s>0&&(s=1/Math.sqrt(s),t[0]=e[0]*s,t[1]=e[1]*s),t},projectAndScale:function(t,e,n,i,s){let r=e[0]-n[0],o=e[1]-n[1];const a=(i[0]*r+i[1]*o)*(s-1);return r=i[0]*a,o=i[1]*a,t[0]=e[0]+r,t[1]=e[1]+o,t},random:function(t,e){e=e||1;const n=2*(0,i.R)()*Math.PI;return t[0]=Math.cos(n)*e,t[1]=Math.sin(n)*e,t},rotate:f,round:function(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t},scale:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t},scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:s,sqrDist:v,sqrLen:b,squaredDistance:h,squaredLength:u,str:function(t){return"vec2("+t[0]+", "+t[1]+")"},sub:_,subtract:r,transformMat2:function(t,e,n){const i=e[0],s=e[1];return t[0]=n[0]*i+n[2]*s,t[1]=n[1]*i+n[3]*s,t},transformMat2d:m,transformMat3:function(t,e,n){const i=e[0],s=e[1];return t[0]=n[0]*i+n[3]*s+n[6],t[1]=n[1]*i+n[4]*s+n[7],t},transformMat4:function(t,e,n){const i=e[0],s=e[1];return t[0]=n[0]*i+n[4]*s+n[12],t[1]=n[1]*i+n[5]*s+n[13],t}},Symbol.toStringTag,{value:"Module"}))},97323:(t,e,n)=>{function i(){return[0,0]}function s(t,e){return[t,e]}function r(t,e){return new Float64Array(t,e,2)}function o(){return s(1,1)}function a(){return s(1,0)}function l(){return s(0,1)}n.d(e,{Z:()=>h,a:()=>i,c:()=>r,f:()=>s});const h=[0,0],c=o(),u=a(),d=l();Object.freeze(Object.defineProperty({__proto__:null,ONES:c,UNIT_X:u,UNIT_Y:d,ZEROS:h,clone:function(t){return[t[0],t[1]]},create:i,createView:r,fromArray:function(t){const e=[0,0],n=Math.min(2,t.length);for(let i=0;i<n;++i)e[i]=t[i];return e},fromValues:s,ones:o,unitX:a,unitY:l,zeros:function(){return[0,0]}},Symbol.toStringTag,{value:"Module"}))},46791:(t,e,n)=>{n.d(e,{Z:()=>B});var i,s=n(43697),r=n(3894),o=n(32448),a=n(22974),l=n(70586),h=n(71143);!function(t){t[t.ADD=1]="ADD",t[t.REMOVE=2]="REMOVE",t[t.MOVE=4]="MOVE"}(i||(i={}));var c,u=n(1654),d=n(5600),m=n(75215),f=n(52421),p=n(52011),_=n(58971),g=n(10661);const y=new h.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(t){this.defaultPrevented=!1,this.item=t}},void 0,(t=>{t.item=null,t.target=null,t.defaultPrevented=!1,t.cancellable=!1})),x=()=>{};function v(t){return t?t instanceof O?t.toArray():t.length?Array.prototype.slice.apply(t):[]:[]}function b(t){if(t&&t.length)return t[0]}function M(t,e,n,i){const s=Math.min(t.length-n,e.length-i);let r=0;for(;r<s&&t[n+r]===e[i+r];)r++;return r}function w(t,e,n,i){e&&e.forEach(((e,s,r)=>{t.push(e),w(t,n.call(i,e,s,r),n,i)}))}const C=new Set,P=new Set,R=new Set,E=new Map;let I=0,O=c=class extends o.Z.EventedAccessor{static isCollection(t){return null!=t&&t instanceof c}constructor(t){super(t),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new g.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:I++})}normalizeCtorArgs(t){return t?Array.isArray(t)||t instanceof c?{items:t}:t:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,_.it)(this._observable),this._items}set items(t){this._emitBeforeChanges(i.ADD)||(this._splice(0,this.length,v(t)),this._emitAfterChanges(i.ADD))}hasEventListener(t){return"change"===t?this._chgListeners.length>0:this._emitter.hasEventListener(t)}on(t,e){if("change"===t){const t=this._chgListeners,n={removed:!1,callback:e};return t.push(n),this._notifications&&this._notifications.push({listeners:t.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=x,n.removed=!0,t.splice(t.indexOf(n),1)}}}return this._emitter.on(t,e)}once(t,e){const n=this.on(t,e);return{remove(){n.remove()}}}add(t,e){if((0,_.it)(this._observable),this._emitBeforeChanges(i.ADD))return this;const n=this.getNextIndex(e??null);return this._splice(n,0,[t]),this._emitAfterChanges(i.ADD),this}addMany(t,e=this._items.length){if((0,_.it)(this._observable),!t||!t.length)return this;if(this._emitBeforeChanges(i.ADD))return this;const n=this.getNextIndex(e);return this._splice(n,0,v(t)),this._emitAfterChanges(i.ADD),this}at(t){if((0,_.it)(this._observable),(t=Math.trunc(t)||0)<0&&(t+=this.length),!(t<0||t>=this.length))return this._items[t]}removeAll(){if((0,_.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return[];const t=this._splice(0,this.length)||[];return this._emitAfterChanges(i.REMOVE),t}clone(){return(0,_.it)(this._observable),this._createNewInstance({items:this._items.map(a.d9)})}concat(...t){(0,_.it)(this._observable);const e=t.map(v);return this._createNewInstance({items:this._items.concat(...e)})}drain(t,e){if((0,_.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const n=(0,l.j0)(this._splice(0,this.length)),s=n.length;for(let i=0;i<s;i++)t.call(e,n[i],i,n);this._emitAfterChanges(i.REMOVE)}every(t,e){return(0,_.it)(this._observable),this._items.every(t,e)}filter(t,e){let n;return(0,_.it)(this._observable),n=2===arguments.length?this._items.filter(t,e):this._items.filter(t),this._createNewInstance({items:n})}find(t,e){return(0,_.it)(this._observable),this._items.find(t,e)}findIndex(t,e){return(0,_.it)(this._observable),this._items.findIndex(t,e)}flatten(t,e){(0,_.it)(this._observable);const n=[];return w(n,this,t,e),new c(n)}forEach(t,e){return(0,_.it)(this._observable),this._items.forEach(t,e)}getItemAt(t){return(0,_.it)(this._observable),this._items[t]}getNextIndex(t){(0,_.it)(this._observable);const e=this.length;return(t=t??e)<0?t=0:t>e&&(t=e),t}includes(t,e=0){return(0,_.it)(this._observable),this._items.includes(t,e)}indexOf(t,e=0){return(0,_.it)(this._observable),this._items.indexOf(t,e)}join(t=","){return(0,_.it)(this._observable),this._items.join(t)}lastIndexOf(t,e=this.length-1){return(0,_.it)(this._observable),this._items.lastIndexOf(t,e)}map(t,e){(0,_.it)(this._observable);const n=this._items.map(t,e);return new c({items:n})}reorder(t,e=this.length-1){(0,_.it)(this._observable);const n=this.indexOf(t);if(-1!==n){if(e<0?e=0:e>=this.length&&(e=this.length-1),n!==e){if(this._emitBeforeChanges(i.MOVE))return t;this._splice(n,1),this._splice(e,0,[t]),this._emitAfterChanges(i.MOVE)}return t}}pop(){if((0,_.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const t=b(this._splice(this.length-1,1));return this._emitAfterChanges(i.REMOVE),t}push(...t){return(0,_.it)(this._observable),this._emitBeforeChanges(i.ADD)||(this._splice(this.length,0,t),this._emitAfterChanges(i.ADD)),this.length}reduce(t,e){(0,_.it)(this._observable);const n=this._items;return 2===arguments.length?n.reduce(t,e):n.reduce(t)}reduceRight(t,e){(0,_.it)(this._observable);const n=this._items;return 2===arguments.length?n.reduceRight(t,e):n.reduceRight(t)}remove(t){return(0,_.it)(this._observable),this.removeAt(this.indexOf(t))}removeAt(t){if((0,_.it)(this._observable),t<0||t>=this.length||this._emitBeforeChanges(i.REMOVE))return;const e=b(this._splice(t,1));return this._emitAfterChanges(i.REMOVE),e}removeMany(t){if((0,_.it)(this._observable),!t||!t.length||this._emitBeforeChanges(i.REMOVE))return[];const e=t instanceof c?t.toArray():t,n=this._items,s=[],r=e.length;for(let t=0;t<r;t++){const i=e[t],r=n.indexOf(i);if(r>-1){const i=1+M(e,n,t+1,r+1),o=this._splice(r,i);o&&o.length>0&&s.push.apply(s,o),t+=i-1}}return this._emitAfterChanges(i.REMOVE),s}reverse(){if((0,_.it)(this._observable),this._emitBeforeChanges(i.MOVE))return this;const t=this._splice(0,this.length);return t&&(t.reverse(),this._splice(0,0,t)),this._emitAfterChanges(i.MOVE),this}shift(){if((0,_.it)(this._observable),!this.length||this._emitBeforeChanges(i.REMOVE))return;const t=b(this._splice(0,1));return this._emitAfterChanges(i.REMOVE),t}slice(t=0,e=this.length){return(0,_.it)(this._observable),this._createNewInstance({items:this._items.slice(t,e)})}some(t,e){return(0,_.it)(this._observable),this._items.some(t,e)}sort(t){if((0,_.it)(this._observable),!this.length||this._emitBeforeChanges(i.MOVE))return this;const e=(0,l.j0)(this._splice(0,this.length));return arguments.length?e.sort(t):e.sort(),this._splice(0,0,e),this._emitAfterChanges(i.MOVE),this}splice(t,e,...n){(0,_.it)(this._observable);const s=(e?i.REMOVE:0)|(n.length?i.ADD:0);if(this._emitBeforeChanges(s))return[];const r=this._splice(t,e,n)||[];return this._emitAfterChanges(s),r}toArray(){return(0,_.it)(this._observable),this._items.slice()}toJSON(){return(0,_.it)(this._observable),this.toArray()}toLocaleString(){return(0,_.it)(this._observable),this._items.toLocaleString()}toString(){return(0,_.it)(this._observable),this._items.toString()}unshift(...t){return(0,_.it)(this._observable),!t.length||this._emitBeforeChanges(i.ADD)||(this._splice(0,0,t),this._emitAfterChanges(i.ADD)),this.length}_createNewInstance(t){return new this.constructor(t)}_splice(t,e,n){const i=this._items,s=this.itemType;let r,o;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,u.Os)((()=>this._dispatchChange()))),e){if(o=i.splice(t,e),this.hasEventListener("before-remove")){const e=y.acquire();e.target=this,e.cancellable=!0;for(let n=0,s=o.length;n<s;n++)r=o[n],e.reset(r),this.emit("before-remove",e),e.defaultPrevented&&(o.splice(n,1),i.splice(t,0,r),t+=1,n-=1,s-=1);y.release(e)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const t=y.acquire();t.target=this,t.cancellable=!1;const e=o.length;for(let n=0;n<e;n++)t.reset(o[n]),this.emit("after-remove",t);y.release(t)}}if(n&&n.length){if(s){const t=[];for(const e of n){const n=s.ensureType(e);null==n&&null!=e||t.push(n)}n=t}const e=this.hasEventListener("before-add"),r=this.hasEventListener("after-add"),o=t===this.length;if(e||r){const s=y.acquire();s.target=this,s.cancellable=!0;const a=y.acquire();a.target=this,a.cancellable=!1;for(const l of n)e?(s.reset(l),this.emit("before-add",s),s.defaultPrevented||(o?i.push(l):i.splice(t++,0,l),this._set("length",i.length),r&&(a.reset(l),this.emit("after-add",a)))):(o?i.push(l):i.splice(t++,0,l),this._set("length",i.length),a.reset(l),this.emit("after-add",a));y.release(a),y.release(s)}else{if(o)for(const t of n)i.push(t);else i.splice(t,0,...n);this._set("length",i.length)}}return(n&&n.length||o&&o.length)&&this._notifyChangeEvent(n,o),o}_emitBeforeChanges(t){let e=!1;if(this.hasEventListener("before-changes")){const n=y.acquire();n.target=this,n.cancellable=!0,n.type=t,this.emit("before-changes",n),e=n.defaultPrevented,y.release(n)}return e}_emitAfterChanges(t){if(this.hasEventListener("after-changes")){const e=y.acquire();e.target=this,e.cancellable=!1,e.type=t,this.emit("after-changes",e),y.release(e)}this._observable.notify()}_notifyChangeEvent(t,e){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:t,removed:e})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const t=this._notifications;this._notifications=null;for(const e of t){const t=e.changes;C.clear(),P.clear(),R.clear();for(const{added:e,removed:n}of t){if(e)if(0===R.size&&0===P.size)for(const t of e)C.add(t);else for(const t of e)P.has(t)?(R.add(t),P.delete(t)):R.has(t)||C.add(t);if(n)if(0===R.size&&0===C.size)for(const t of n)P.add(t);else for(const t of n)C.has(t)?C.delete(t):(R.delete(t),P.add(t))}const n=r.Z.acquire();C.forEach((t=>{n.push(t)}));const i=r.Z.acquire();P.forEach((t=>{i.push(t)}));const s=this._items,o=e.items,a=r.Z.acquire();if(R.forEach((t=>{o.indexOf(t)!==s.indexOf(t)&&a.push(t)})),e.listeners&&(n.length||i.length||a.length)){const t={target:this,added:n,removed:i,moved:a},s=e.listeners.length;for(let n=0;n<s;n++){const i=e.listeners[n];i.removed||i.callback.call(this,t)}}r.Z.release(n),r.Z.release(i),r.Z.release(a)}C.clear(),P.clear(),R.clear()}};O.ofType=t=>{if(!t)return c;if(E.has(t))return E.get(t);let e=null;if("function"==typeof t)e=t.prototype.declaredClass;else if(t.base)e=t.base.prototype.declaredClass;else for(const n in t.typeMap){const i=t.typeMap[n].prototype.declaredClass;e?e+=` | ${i}`:e=i}let n=class extends c{};return(0,s._)([(0,f.c)({Type:t,ensureType:"function"==typeof t?(0,m.se)(t):(0,m.N7)(t)})],n.prototype,"itemType",void 0),n=(0,s._)([(0,p.j)(`esri.core.Collection<${e}>`)],n),E.set(t,n),n},(0,s._)([(0,d.Cb)()],O.prototype,"length",void 0),(0,s._)([(0,d.Cb)()],O.prototype,"items",null),O=c=(0,s._)([(0,p.j)("esri.core.Collection")],O);const B=O},3920:(t,e,n)=>{n.d(e,{p:()=>h,r:()=>c});var i=n(43697),s=n(15923),r=n(61247),o=n(5600),a=n(52011),l=n(72762);const h=t=>{let e=class extends t{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new r.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,i._)([(0,o.Cb)({readOnly:!0})],e.prototype,"handles",null),(0,i._)([(0,o.Cb)({readOnly:!0})],e.prototype,"updatingHandles",null),e=(0,i._)([(0,a.j)("esri.core.HandleOwner")],e),e};let c=class extends(h(s.Z)){};c=(0,i._)([(0,a.j)("esri.core.HandleOwner")],c)},52421:(t,e,n)=>{function i(t){return(e,n)=>{e[n]=t}}n.d(e,{c:()=>i})},70921:(t,e,n)=>{n.d(e,{R:()=>r,Z:()=>s});var i=n(46791);function s(t,e,n=i.Z){return e||(e=new n),e===t||(e.removeAll(),(s=t)&&(Array.isArray(s)||"items"in s&&Array.isArray(s.items))?e.addMany(t):t&&e.add(t)),e;var s}function r(t){return t}},24133:(t,e,n)=>{n.d(e,{Q:()=>a});var i=n(67676),s=n(70586),r=n(44553),o=n(88764);class a{constructor(t=9,e){this._compareMinX=u,this._compareMinY=d,this._toBBox=t=>t,this._maxEntries=Math.max(4,t||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),e&&("function"==typeof e?this._toBBox=e:this._initFormat(e)),this.clear()}destroy(){this.clear(),v.prune(),b.prune(),M.prune(),w.prune()}all(t){this._all(this._data,t)}search(t,e){let n=this._data;const i=this._toBBox;if(y(t,n))for(v.clear();n;){for(let s=0,r=n.children.length;s<r;s++){const r=n.children[s],o=n.leaf?i(r):r;y(t,o)&&(n.leaf?e(r):g(t,o)?this._all(r,e):v.push(r))}n=v.pop()}}collides(t){let e=this._data;const n=this._toBBox;if(!y(t,e))return!1;for(v.clear();e;){for(let i=0,s=e.children.length;i<s;i++){const s=e.children[i],r=e.leaf?n(s):s;if(y(t,r)){if(e.leaf||g(t,r))return!0;v.push(s)}}e=v.pop()}return!1}load(t){if(!t.length)return this;if(t.length<this._minEntries){for(let e=0,n=t.length;e<n;e++)this.insert(t[e]);return this}let e=this._build(t.slice(0,t.length),0,t.length-1,0);if(this._data.children.length)if(this._data.height===e.height)this._splitRoot(this._data,e);else{if(this._data.height<e.height){const t=this._data;this._data=e,e=t}this._insert(e,this._data.height-e.height-1,!0)}else this._data=e;return this}insert(t){return t&&this._insert(t,this._data.height-1),this}clear(){return this._data=new R([]),this}remove(t){if(!t)return this;let e,n=this._data,r=null,o=0,a=!1;const l=this._toBBox(t);for(M.clear(),w.clear();n||M.length>0;){if(n||(n=(0,s.j0)(M.pop()),r=M.data[M.length-1],o=w.pop()??0,a=!0),n.leaf&&(e=(0,i.cq)(n.children,t,n.children.length,n.indexHint),-1!==e))return n.children.splice(e,1),M.push(n),this._condense(M),this;a||n.leaf||!g(n,l)?r?(o++,n=r.children[o],a=!1):n=null:(M.push(n),w.push(o),o=0,r=n,n=n.children[0])}return this}toJSON(){return this._data}fromJSON(t){return this._data=t,this}_all(t,e){let n=t;for(b.clear();n;){if(!0===n.leaf)for(const t of n.children)e(t);else b.pushArray(n.children);n=b.pop()??null}}_build(t,e,n,i){const s=n-e+1;let r=this._maxEntries;if(s<=r){const i=new R(t.slice(e,n+1));return l(i,this._toBBox),i}i||(i=Math.ceil(Math.log(s)/Math.log(r)),r=Math.ceil(s/r**(i-1)));const o=new E([]);o.height=i;const a=Math.ceil(s/r),h=a*Math.ceil(Math.sqrt(r));x(t,e,n,h,this._compareMinX);for(let s=e;s<=n;s+=h){const e=Math.min(s+h-1,n);x(t,s,e,a,this._compareMinY);for(let n=s;n<=e;n+=a){const s=Math.min(n+a-1,e);o.children.push(this._build(t,n,s,i-1))}}return l(o,this._toBBox),o}_chooseSubtree(t,e,n,i){for(;i.push(e),!0!==e.leaf&&i.length-1!==n;){let n,i=1/0,s=1/0;for(let r=0,o=e.children.length;r<o;r++){const o=e.children[r],a=m(o),l=p(t,o)-a;l<s?(s=l,i=a<i?a:i,n=o):l===s&&a<i&&(i=a,n=o)}e=n||e.children[0]}return e}_insert(t,e,n){const i=this._toBBox,s=n?t:i(t);M.clear();const r=this._chooseSubtree(s,this._data,e,M);for(r.children.push(t),c(r,s);e>=0&&M.data[e].children.length>this._maxEntries;)this._split(M,e),e--;this._adjustParentBBoxes(s,M,e)}_split(t,e){const n=t.data[e],i=n.children.length,s=this._minEntries;this._chooseSplitAxis(n,s,i);const r=this._chooseSplitIndex(n,s,i);if(!r)return void console.log("  Error: assertion failed at PooledRBush._split: no valid split index");const o=n.children.splice(r,n.children.length-r),a=n.leaf?new R(o):new E(o);a.height=n.height,l(n,this._toBBox),l(a,this._toBBox),e?t.data[e-1].children.push(a):this._splitRoot(n,a)}_splitRoot(t,e){this._data=new E([t,e]),this._data.height=t.height+1,l(this._data,this._toBBox)}_chooseSplitIndex(t,e,n){let i,s,r;i=s=1/0;for(let o=e;o<=n-e;o++){const e=h(t,0,o,this._toBBox),a=h(t,o,n,this._toBBox),l=_(e,a),c=m(e)+m(a);l<i?(i=l,r=o,s=c<s?c:s):l===i&&c<s&&(s=c,r=o)}return r}_chooseSplitAxis(t,e,n){const i=t.leaf?this._compareMinX:u,s=t.leaf?this._compareMinY:d;this._allDistMargin(t,e,n,i)<this._allDistMargin(t,e,n,s)&&t.children.sort(i)}_allDistMargin(t,e,n,i){t.children.sort(i);const s=this._toBBox,r=h(t,0,e,s),o=h(t,n-e,n,s);let a=f(r)+f(o);for(let i=e;i<n-e;i++){const e=t.children[i];c(r,t.leaf?s(e):e),a+=f(r)}for(let i=n-e-1;i>=e;i--){const e=t.children[i];c(o,t.leaf?s(e):e),a+=f(o)}return a}_adjustParentBBoxes(t,e,n){for(let i=n;i>=0;i--)c(e.data[i],t)}_condense(t){for(let e=t.length-1;e>=0;e--){const n=t.data[e];if(0===n.children.length)if(e>0){const s=t.data[e-1],r=s.children;r.splice((0,i.cq)(r,n,r.length,s.indexHint),1)}else this.clear();else l(n,this._toBBox)}}_initFormat(t){const e=["return a"," - b",";"];this._compareMinX=new Function("a","b",e.join(t[0])),this._compareMinY=new Function("a","b",e.join(t[1])),this._toBBox=new Function("a","return {minX: a"+t[0]+", minY: a"+t[1]+", maxX: a"+t[2]+", maxY: a"+t[3]+"};")}}function l(t,e){h(t,0,t.children.length,e,t)}function h(t,e,n,i,s){s||(s=new R([])),s.minX=1/0,s.minY=1/0,s.maxX=-1/0,s.maxY=-1/0;for(let r,o=e;o<n;o++)r=t.children[o],c(s,t.leaf?i(r):r);return s}function c(t,e){t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY)}function u(t,e){return t.minX-e.minX}function d(t,e){return t.minY-e.minY}function m(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function f(t){return t.maxX-t.minX+(t.maxY-t.minY)}function p(t,e){return(Math.max(e.maxX,t.maxX)-Math.min(e.minX,t.minX))*(Math.max(e.maxY,t.maxY)-Math.min(e.minY,t.minY))}function _(t,e){const n=Math.max(t.minX,e.minX),i=Math.max(t.minY,e.minY),s=Math.min(t.maxX,e.maxX),r=Math.min(t.maxY,e.maxY);return Math.max(0,s-n)*Math.max(0,r-i)}function g(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function y(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function x(t,e,n,i,r){const a=[e,n];for(;a.length;){const e=(0,s.j0)(a.pop()),n=(0,s.j0)(a.pop());if(e-n<=i)continue;const l=n+Math.ceil((e-n)/i/2)*i;(0,o.q)(t,l,n,e,r),a.push(n,l,l,e)}}const v=new r.Z,b=new r.Z,M=new r.Z,w=new r.Z({deallocator:void 0});class C{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}}class P extends C{constructor(){super(...arguments),this.height=1,this.indexHint=new i.SO}}class R extends P{constructor(t){super(),this.children=t,this.leaf=!0}}class E extends P{constructor(t){super(),this.children=t,this.leaf=!1}}},17445:(t,e,n)=>{n.d(e,{N1:()=>d,YP:()=>l,Z_:()=>p,gx:()=>h,nn:()=>_,on:()=>u,tX:()=>g});var i=n(91460),s=n(50758),r=n(70586),o=n(95330),a=n(26258);function l(t,e,n={}){return c(t,e,n,m)}function h(t,e,n={}){return c(t,e,n,f)}function c(t,e,n={},i){let s=null;const o=n.once?(t,n)=>{i(t)&&((0,r.hw)(s),e(t,n))}:(t,n)=>{i(t)&&e(t,n)};if(s=(0,a.aQ)(t,o,n.sync,n.equals),n.initial){const e=t();o(e,e)}return s}function u(t,e,n,o={}){let a=null,h=null,c=null;function u(){a&&h&&(h.remove(),o.onListenerRemove?.(a),a=null,h=null)}function d(t){o.once&&o.once&&(0,r.hw)(c),n(t)}const m=l(t,((t,n)=>{u(),(0,i.vT)(t)&&(a=t,h=(0,i.on)(t,e,d),o.onListenerAdd?.(t))}),{sync:o.sync,initial:!0});return c=(0,s.kB)((()=>{m.remove(),u()})),c}function d(t,e){return function(t,e,n){if((0,o.Hc)(n))return Promise.reject((0,o.zE)());const i=t();if(e?.(i))return Promise.resolve(i);let a=null;function l(){a=(0,r.hw)(a)}return new Promise(((i,r)=>{a=(0,s.AL)([(0,o.fu)(n,(()=>{l(),r((0,o.zE)())})),c(t,(t=>{l(),i(t)}),{sync:!1,once:!0},e??m)])}))}(t,f,e)}function m(t){return!0}function f(t){return!!t}n(87538);const p={sync:!0},_={initial:!0},g={sync:!0,initial:!0}},72762:(t,e,n)=>{n.d(e,{t:()=>u});var i=n(43697),s=n(15923),r=n(61247),o=n(70586),a=n(17445),l=n(1654),h=n(5600),c=n(52011);let u=class extends s.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new r.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(t,e,n={}){return this._installWatch(t,e,n,a.YP)}addWhen(t,e,n={}){return this._installWatch(t,e,n,a.gx)}addOnCollectionChange(t,e,{initial:n=!1,final:i=!1}={}){const s=++this._handleId;return this._handles.add([(0,a.on)(t,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(t,"change",e,{onListenerAdd:n?t=>e({added:t.toArray(),removed:[]}):void 0,onListenerRemove:i?t=>e({added:[],removed:t.toArray()}):void 0})],s),{remove:()=>this._handles.remove(s)}}addPromise(t){if((0,o.Wi)(t))return t;const e=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(t)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},e),this._pendingPromises.add(t),this._set("updating",!0);const n=()=>this._handles.remove(e);return t.then(n,n),t}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(t,e,n={},i){const s=++this._handleId;n.sync||this._installSyncUpdatingWatch(t,s);const r=i(t,e,n);return this._handles.add(r,s),{remove:()=>this._handles.remove(s)}}_installSyncUpdatingWatch(t,e){const n=this._createSyncUpdatingCallback(),i=(0,a.YP)(t,n,{sync:!0,equals:()=>!1});return this._handles.add(i,e),i}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const t=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{t===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,i._)([(0,h.Cb)({readOnly:!0})],u.prototype,"updating",void 0),u=(0,i._)([(0,c.j)("esri.core.support.WatchUpdatingTracking")],u);const d=-42},35463:(t,e,n)=>{n.d(e,{JE:()=>o,Nm:()=>r,rJ:()=>a}),n(80442);const i={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},s={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function r(t,e,n){const i=new Date(t.getTime());if(e&&n){const t=s[n],{getter:r,setter:o,multiplier:a}=t;if("months"===n){const t=function(t,e){const n=new Date(t,e+1,1);return n.setDate(0),n.getDate()}(i.getFullYear(),i.getMonth()+e);i.getDate()>t&&i.setDate(t)}i[o](i[r]()+e*a)}return i}function o(t,e){switch(e){case"milliseconds":return new Date(t.getTime());case"seconds":return new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds());case"minutes":return new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes());case"hours":return new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours());case"days":return new Date(t.getFullYear(),t.getMonth(),t.getDate());case"weeks":return new Date(t.getFullYear(),t.getMonth(),t.getDate()-t.getDay());case"months":return new Date(t.getFullYear(),t.getMonth(),1);case"years":return new Date(t.getFullYear(),0,1);case"decades":return new Date(t.getFullYear()-t.getFullYear()%10,0,1);case"centuries":return new Date(t.getFullYear()-t.getFullYear()%100,0,1);default:return new Date}}function a(t,e,n){return 0===t?0:t*i[e]/i[n]}},41123:(t,e,n)=>{n.d(e,{D:()=>s});const i="randomUUID"in crypto;function s(){if(i)return crypto.randomUUID();const t=crypto.getRandomValues(new Uint16Array(8));t[3]=4095&t[3]|16384,t[4]=16383&t[4]|32768;const e=e=>t[e].toString(16).padStart(4,"0");return e(0)+e(1)+"-"+e(2)+"-"+e(3)+"-"+e(4)+"-"+e(5)+e(6)+e(7)}},73913:(t,e,n)=>{n.d(e,{Sy:()=>l,UZ:()=>o,XZ:()=>a,x3:()=>h});var i=n(58901),s=n(82971),r=n(33955);const o={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new i.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator}),minus180Line:new i.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new i.Z({paths:[[[180,-180],[180,180]]],spatialReference:s.Z.WGS84}),minus180Line:new i.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:s.Z.WGS84})}};function a(t,e){return Math.ceil((t-e)/(2*e))}function l(t,e){const n=h(t);for(const t of n)for(const n of t)n[0]+=e;return t}function h(t){return(0,r.oU)(t)?t.rings:t.paths}},57765:(t,e,n)=>{n.r(e),n.d(e,{default:()=>re});var i=n(43697),s=n(46791),r=n(20102),o=n(92604),a=n(70586),l=n(16453),h=n(5600),c=n(90578),u=(n(67676),n(71715)),d=n(52011),m=n(30556),f=n(87085),p=n(71612),_=n(38009),g=n(72965),y=n(3172),x=n(17452),v=n(41123),b=(n(75215),n(10699)),M=n(96674),w=n(83379),C=n(15923),P=n(21787),R=n(46521),E=n(4307),I=n(17896);const O=(0,n(65617).c)(),B=(0,R.c)(),S=(0,R.c)(),Z=(0,R.c)();function T(t,e,n){return(0,I.s)(O,e[0],e[1],1),(0,I.t)(O,O,(0,P.t)(B,n)),0===O[2]?(0,E.s)(t,O[0],O[1]):(0,E.s)(t,O[0]/O[2],O[1]/O[2])}function A(t,e,n){return j(S,e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]),j(Z,n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7]),(0,P.m)(t,(0,P.a)(S,S),Z),0!==t[8]&&(t[0]/=t[8],t[1]/=t[8],t[2]/=t[8],t[3]/=t[8],t[4]/=t[8],t[5]/=t[8],t[6]/=t[8],t[7]/=t[8],t[8]/=t[8]),t}function j(t,e,n,i,s,r,o,a,l){(0,P.s)(t,e,i,r,n,s,o,1,1,1),(0,I.s)(O,a,l,1),(0,P.a)(B,t);const[h,c,u]=(0,I.t)(O,O,(0,P.t)(B,B));return(0,P.s)(B,h,0,0,0,c,0,0,0,u),(0,P.m)(t,B,t)}var D=n(62357),L=n(97323),Y=n(94139),W=n(38913),X=n(44547),V=n(82971),N=n(2368);n(80442);let q=class extends N.j{projectOrWarn(t,e){if((0,a.Wi)(t))return t;const{geometry:n,pending:i}=(0,X.dz)(t,e);return i?null:i||n?n:(o.Z.getLogger(this.declaredClass).warn("geometry could not be projected to the spatial reference",{georeference:this,geometry:t,sourceSpatialReference:t.spatialReference,targetSpatialReference:e}),null)}};q=(0,i._)([(0,d.j)("esri.layers.support.GeoreferenceBase")],q);const z=q,H=(0,R.c)(),F=(0,L.a)();let U=class extends C.Z{constructor(){super(...arguments),this.sourcePoint=null,this.mapPoint=null}};(0,i._)([(0,h.Cb)()],U.prototype,"sourcePoint",void 0),(0,i._)([(0,h.Cb)({type:Y.Z})],U.prototype,"mapPoint",void 0),U=(0,i._)([(0,d.j)("esri.layers.support.ControlPoint")],U);let k=class extends((0,M.eC)(z)){constructor(t){super(t),this.controlPoints=null,this.height=0,this.type="control-points",this.width=0}readControlPoints(t,e){const n=V.Z.fromJSON(e.spatialReference),i=(0,R.f)(...e.coefficients,1);return t.map((t=>((0,E.s)(F,t.x,t.y),T(F,F,i),{sourcePoint:t,mapPoint:new Y.Z({x:F[0],y:F[1],spatialReference:n})})))}writeControlPoints(t,e,n,i){if((0,a.Wi)(this.transform)){const t=new r.Z("web-document-write:invalid-georeference","Invalid 'controlPoints', 'width', 'height' configuration.",{layer:i?.layer,georeference:this});i?.messages?i.messages.push(t):o.Z.getLogger(this.declaredClass).error(t.name,t.message)}else(0,a.pC)(t)&&J(t[0])&&(e.controlPoints=t.map((t=>{const e=(0,a.Wg)(t.sourcePoint);return{x:e.x,y:e.y}})),e.spatialReference=t[0].mapPoint.spatialReference.toJSON(),e.coefficients=this.transform.slice(0,8))}get coords(){if((0,a.Wi)(this.controlPoints))return null;const t=this._updateTransform(H);if((0,a.Wi)(t)||!J(this.controlPoints[0]))return null;const e=this.controlPoints[0].mapPoint.spatialReference;return function(t,e,n,i){const s=(0,L.f)(0,n),r=(0,L.f)(0,0),o=(0,L.f)(e,0),a=(0,L.f)(e,n);return T(s,s,t),T(r,r,t),T(o,o,t),T(a,a,t),new W.Z({rings:[[s,r,o,a,s]],spatialReference:i})}(t,this.width,this.height,e)}set coords(t){if((0,a.Wi)(this.controlPoints)||!J(this.controlPoints[0]))return;const e=this.controlPoints[0].mapPoint.spatialReference;if(t=this.projectOrWarn(t,e),(0,a.Wi)(t))return;const{width:n,height:i}=this,{rings:[[s,r,o,l]]}=t,h={sourcePoint:(0,D.vW)(0,i),mapPoint:new Y.Z({x:s[0],y:s[1],spatialReference:e})},c={sourcePoint:(0,D.vW)(0,0),mapPoint:new Y.Z({x:r[0],y:r[1],spatialReference:e})},u={sourcePoint:(0,D.vW)(n,0),mapPoint:new Y.Z({x:o[0],y:o[1],spatialReference:e})},d={sourcePoint:(0,D.vW)(n,i),mapPoint:new Y.Z({x:l[0],y:l[1],spatialReference:e})};J(h)&&J(c)&&J(u)&&J(d)&&(ot(H,h,c,u,d),this.controlPoints=(0,a.Wg)(this.controlPoints).map((({sourcePoint:t})=>((0,E.s)(F,t.x,t.y),T(F,F,H),{sourcePoint:t,mapPoint:new Y.Z({x:F[0],y:F[1],spatialReference:e})}))))}get inverseTransform(){return(0,a.Wi)(this.transform)?null:(0,P.e)((0,R.c)(),this.transform)}get transform(){return this._updateTransform()}toMap(t){if((0,a.Wi)(t)||(0,a.Wi)(this.transform)||(0,a.Wi)(this.controlPoints)||!J(this.controlPoints[0]))return null;(0,E.s)(F,t.x,t.y);const e=this.controlPoints[0].mapPoint.spatialReference;return T(F,F,this.transform),new Y.Z({x:F[0],y:F[1],spatialReference:e})}toSource(t){if((0,a.Wi)(t)||(0,a.Wi)(this.inverseTransform)||(0,a.Wi)(this.controlPoints)||!J(this.controlPoints[0]))return null;const e=this.controlPoints[0].mapPoint.spatialReference;return t=t.normalize(),t=(0,X.dz)(t,e).geometry,(0,a.Wi)(t)?null:((0,E.s)(F,t.x,t.y),T(F,F,this.inverseTransform),(0,D.vW)(F[0],F[1]))}_updateTransform(t){const{controlPoints:e,width:n,height:i}=this;if((0,a.Wi)(e)||!(n>0)||!(i>0))return null;const[s,r,o,l]=e;if(!J(s))return null;const h=s.mapPoint.spatialReference,c=this._projectControlPoint(r,h),u=this._projectControlPoint(o,h),d=this._projectControlPoint(l,h);if(!c.valid||!u.valid||!d.valid)return null;if(!J(c.controlPoint))return null;(0,a.Wi)(t)&&(t=(0,R.c)());let m=null;return m=J(u.controlPoint)&&J(d.controlPoint)?ot(t,s,c.controlPoint,u.controlPoint,d.controlPoint):J(u.controlPoint)?function(t,e,n,i){return rt(G,tt,e),rt(Q,et,n),rt($,nt,i),(0,E.l)(K,G,Q,.5),(0,E.r)(K,$,K,Math.PI),(0,E.l)(it,tt,et,.5),(0,E.r)(it,nt,it,Math.PI),ct(t,G,Q,$,K,tt,et,nt,it)}(t,s,c.controlPoint,u.controlPoint):function(t,e,n){return rt(G,tt,e),rt(Q,et,n),(0,E.r)($,Q,G,st),(0,E.r)(K,G,Q,st),(0,E.r)(nt,et,tt,-st),(0,E.r)(it,tt,et,-st),ct(t,G,Q,$,K,tt,et,nt,it)}(t,s,c.controlPoint),m.every((t=>0===t))?null:m}_projectControlPoint(t,e){if(!J(t))return{valid:!0,controlPoint:t};const{sourcePoint:n,mapPoint:i}=t,{geometry:s,pending:r}=(0,X.dz)(i,e);return r?{valid:!1,controlPoint:null}:r||s?{valid:!0,controlPoint:{sourcePoint:n,mapPoint:s}}:(o.Z.getLogger(this.declaredClass).warn("map point could not be projected to the spatial reference",{georeference:this,controlPoint:t,sourceSpatialReference:i.spatialReference,targetSpatialReference:e}),{valid:!1,controlPoint:null})}};function J(t){return(0,a.pC)(t)&&(0,a.pC)(t.sourcePoint)&&(0,a.pC)(t.mapPoint)}(0,i._)([(0,h.Cb)({type:[U],json:{write:{allowNull:!1,isRequired:!0}}})],k.prototype,"controlPoints",void 0),(0,i._)([(0,u.r)("controlPoints")],k.prototype,"readControlPoints",null),(0,i._)([(0,m.c)("controlPoints")],k.prototype,"writeControlPoints",null),(0,i._)([(0,h.Cb)()],k.prototype,"coords",null),(0,i._)([(0,h.Cb)({json:{write:!0}})],k.prototype,"height",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],k.prototype,"inverseTransform",null),(0,i._)([(0,h.Cb)({readOnly:!0})],k.prototype,"transform",null),(0,i._)([(0,h.Cb)({json:{write:!0}})],k.prototype,"width",void 0),k=(0,i._)([(0,d.j)("esri.layers.support.ControlPointsGeoreference")],k);const G=(0,L.a)(),Q=(0,L.a)(),$=(0,L.a)(),K=(0,L.a)(),tt=(0,L.a)(),et=(0,L.a)(),nt=(0,L.a)(),it=(0,L.a)(),st=Math.PI/2;function rt(t,e,n){(0,E.s)(t,n.sourcePoint.x,n.sourcePoint.y),(0,E.s)(e,n.mapPoint.x,n.mapPoint.y)}function ot(t,e,n,i,s){return rt(G,tt,e),rt(Q,et,n),rt($,nt,i),rt(K,it,s),ct(t,G,Q,$,K,tt,et,nt,it)}const at=new Array(8).fill(0),lt=new Array(8).fill(0);function ht(t,e,n,i,s){return t[0]=e[0],t[1]=e[1],t[2]=n[0],t[3]=n[1],t[4]=i[0],t[5]=i[1],t[6]=s[0],t[7]=s[1],t}function ct(t,e,n,i,s,r,o,a,l){return A(t,ht(at,e,n,i,s),ht(lt,r,o,a,l))}const ut=k;let dt=class extends z{constructor(t){super(t),this.bottomLeft=null,this.bottomRight=null,this.topLeft=null,this.topRight=null,this.type="corners"}get coords(){let{topLeft:t,topRight:e,bottomLeft:n,bottomRight:i}=this;if((0,a.Wi)(t)||(0,a.Wi)(e)||(0,a.Wi)(n)||(0,a.Wi)(i))return null;const s=t.spatialReference;return e=this.projectOrWarn(e,s),n=this.projectOrWarn(n,s),i=this.projectOrWarn(i,s),(0,a.Wi)(e)||(0,a.Wi)(n)||(0,a.Wi)(i)?null:new W.Z({rings:[[[n.x,n.y],[t.x,t.y],[e.x,e.y],[i.x,i.y],[n.x,n.y]]],spatialReference:s})}set coords(t){const{topLeft:e}=this;if((0,a.Wi)(e))return;const n=e.spatialReference;if(t=this.projectOrWarn(t,n),(0,a.Wi)(t))return;const{rings:[[i,s,r,o]]}=t;this.bottomLeft=new Y.Z({x:i[0],y:i[1],spatialReference:n}),this.topLeft=new Y.Z({x:s[0],y:s[1],spatialReference:n}),this.topRight=new Y.Z({x:r[0],y:r[1],spatialReference:n}),this.bottomRight=new Y.Z({x:o[0],y:o[1],spatialReference:n})}};(0,i._)([(0,h.Cb)()],dt.prototype,"coords",null),(0,i._)([(0,h.Cb)({type:Y.Z})],dt.prototype,"bottomLeft",void 0),(0,i._)([(0,h.Cb)({type:Y.Z})],dt.prototype,"bottomRight",void 0),(0,i._)([(0,h.Cb)({type:Y.Z})],dt.prototype,"topLeft",void 0),(0,i._)([(0,h.Cb)({type:Y.Z})],dt.prototype,"topRight",void 0),dt=(0,i._)([(0,d.j)("esri.layers.support.CornersGeoreference")],dt);const mt=dt;var ft=n(22021),pt=n(46851),_t=n(6570);let gt=class extends z{constructor(t){super(t),this.extent=null,this.rotation=0,this.type="extent-and-rotation"}get coords(){if((0,a.Wi)(this.extent))return null;const{xmin:t,ymin:e,xmax:n,ymax:i,spatialReference:s}=this.extent;let r;if(this.rotation){const{x:s,y:o}=this.extent.center,a=yt(s,o,this.rotation);r=[a(t,e),a(t,i),a(n,i),a(n,e)],r.push(r[0])}else r=[[t,e],[t,i],[n,i],[n,e],[t,e]];return new W.Z({rings:[r],spatialReference:s})}set coords(t){if((0,a.Wi)(t)||(0,a.Wi)(this.extent))return;const e=this.extent.spatialReference;if(t=this.projectOrWarn(t,e),(0,a.Wi)(t)||(0,a.Wi)(t.extent))return;const{rings:[[n,i,s]],extent:{center:{x:r,y:o}}}=t,l=(0,ft.BV)(Math.PI/2-Math.atan2(i[1]-n[1],i[0]-n[0])),h=yt(r,o,-l),[c,u]=h(n[0],n[1]),[d,m]=h(s[0],s[1]);this.extent=new _t.Z({xmin:c,ymin:u,xmax:d,ymax:m,spatialReference:e}),this.rotation=l}};function yt(t,e,n){const i=(0,pt.t)(n),s=Math.cos(i),r=Math.sin(i);return(n,i)=>[s*(n-t)+r*(i-e)+t,s*(i-e)-r*(n-t)+e]}(0,i._)([(0,h.Cb)()],gt.prototype,"coords",null),(0,i._)([(0,h.Cb)({type:_t.Z})],gt.prototype,"extent",void 0),(0,i._)([(0,h.Cb)({type:Number})],gt.prototype,"rotation",void 0),gt=(0,i._)([(0,d.j)("esri.layers.support.ExtentAndRotationGeoreference")],gt);const xt={key:"type",base:z,typeMap:{"control-points":ut,corners:mt,"extent-and-rotation":gt}};let vt=class extends((0,b.iv)((0,M.eC)(w.Z))){constructor(){super(...arguments),this.georeference=null,this.opacity=1}readGeoreference(t){return ut.fromJSON(t)}};(0,i._)([(0,h.Cb)({types:xt,json:{write:!0}})],vt.prototype,"georeference",void 0),(0,i._)([(0,u.r)("georeference")],vt.prototype,"readGeoreference",null),(0,i._)([(0,h.Cb)()],vt.prototype,"opacity",void 0),vt=(0,i._)([(0,d.j)("esri.layers.support.MediaElementBase")],vt);const bt=vt;var Mt=n(66094),wt=n(25929);let Ct=class extends bt{constructor(t){super(t),this.content=null,this.image=null,this.type="image",this.image=null}load(){const t=this.image;if("string"==typeof t){const e=(0,y.default)(t,{responseType:"image"}).then((({data:t})=>{this._set("content",t)}));this.addResolvingPromise(e)}else if(t instanceof HTMLImageElement){const e=t.decode().then((()=>{this._set("content",t)}));this.addResolvingPromise(e)}else t?this._set("content",t):this.addResolvingPromise(Promise.reject(new r.Z("image-element:invalid-image-type","Invalid image type",{image:t})));return Promise.resolve(this)}readImage(t,e,n){return(0,wt.f)(e.url,n)}writeImage(t,e,n,i){if((0,a.Wi)(t))return;const s=i?.portalItem,r=i?.resources;if(!s||!r)return void("string"==typeof t&&(e[n]=(0,wt.t)(t,i)));const o="string"!=typeof t||(0,x.HK)(t)||(0,x.jc)(t)?null:t;if(o){if(null==(0,wt.i)(o))return void(e[n]=o);const t=(0,wt.t)(o,{...i,verifyItemRelativeUrls:i&&i.verifyItemRelativeUrls?{writtenUrls:i.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},wt.M.NO);if(s&&t&&!(0,x.YP)(t))return r.toKeep.push({resource:s.resourceFromPath(t),compress:!1}),void(e[n]=t)}e[n]="<pending>",r.pendingOperations.push(Rt(t).then((t=>{const i=function(t,e){const n=(0,v.D)(),i=`${(0,x.v_)("media",n)}.${(0,Mt.B)(t)}`;return e.resourceFromPath(i)}(t,s);e[n]=i.itemRelativeUrl,r.toAdd.push({resource:i,content:t,compress:!1,finish:t=>{this.image=t.url}})})))}};(0,i._)([(0,h.Cb)({readOnly:!0})],Ct.prototype,"content",void 0),(0,i._)([(0,h.Cb)({json:{name:"url",type:String}})],Ct.prototype,"image",void 0),(0,i._)([(0,u.r)("image",["url"])],Ct.prototype,"readImage",null),(0,i._)([(0,m.c)("image")],Ct.prototype,"writeImage",null),(0,i._)([(0,h.Cb)({readOnly:!0,json:{name:"mediaType"}})],Ct.prototype,"type",void 0),Ct=(0,i._)([(0,d.j)("esri.layers.support.ImageElement")],Ct);const Pt=Ct;async function Rt(t){if("string"==typeof t){if((0,x.jc)(t)){const{data:e}=await(0,y.default)(t,{responseType:"blob"});return e}return(0,x.HK)(t)?(0,x.fw)(t):Rt((await(0,y.default)(t,{responseType:"image"})).data)}return new Promise((e=>function(t){if(t instanceof HTMLCanvasElement)return t;const e=t instanceof HTMLImageElement?t.naturalWidth:t.width,n=t instanceof HTMLImageElement?t.naturalHeight:t.height,i=document.createElement("canvas"),s=i.getContext("2d");return i.width=e,i.height=n,t instanceof HTMLImageElement?s.drawImage(t,0,0,t.width,t.height):t instanceof ImageData&&s.putImageData(t,0,0),i}(t).toBlob(e)))}n(66577);var Et=n(70921),It=n(32448),Ot=n(3920),Bt=n(609),St=n(95330),Zt=n(17445),Tt=n(24470),At=n(87416),jt=n(8744),Dt=n(37549),Lt=n(20322),Yt=n(86662),Wt=n(33955),Xt=n(73913);function Vt(t,e,n){if(Array.isArray(t)){const i=t[0];if(i>e){const n=(0,Xt.XZ)(i,e);t[0]=i+n*(-2*e)}else if(i<n){const e=(0,Xt.XZ)(i,n);t[0]=i+e*(-2*n)}}else{const i=t.x;if(i>e){const n=(0,Xt.XZ)(i,e);t.x+=n*(-2*e)}else if(i<n){const e=(0,Xt.XZ)(i,n);t.x+=e*(-2*n)}}return t}function Nt(t,e){const n=[],{ymin:i,ymax:s,xmin:r,xmax:o}=t,a=t.xmax-t.xmin,[l,h]=e.valid,{x:c,frameId:u}=qt(t.xmin,e),{x:d,frameId:m}=qt(t.xmax,e),f=c===d&&a>0;if(a>2*h){const t={xmin:r<o?c:d,ymin:i,xmax:h,ymax:s},e={xmin:l,ymin:i,xmax:r<o?d:c,ymax:s},a={xmin:0,ymin:i,xmax:h,ymax:s},f={xmin:l,ymin:i,xmax:0,ymax:s},p=[],_=[];zt(t,a)&&p.push(u),zt(t,f)&&_.push(u),zt(e,a)&&p.push(m),zt(e,f)&&_.push(m);for(let t=u+1;t<m;t++)p.push(t),_.push(t);n.push(new Ut(t,[u]),new Ut(e,[m]),new Ut(a,p),new Ut(f,_))}else c>d||f?n.push(new Ut({xmin:c,ymin:i,xmax:h,ymax:s},[u]),new Ut({xmin:l,ymin:i,xmax:d,ymax:s},[m])):n.push(new Ut({xmin:c,ymin:i,xmax:d,ymax:s},[u]));return n}function qt(t,e){const[n,i]=e.valid,s=2*i;let r,o=0;return t>i?(r=Math.ceil(Math.abs(t-i)/s),t-=r*s,o=r):t<n&&(r=Math.ceil(Math.abs(t-n)/s),t+=r*s,o=-r),{x:t,frameId:o}}function zt(t,e){const{xmin:n,ymin:i,xmax:s,ymax:r}=e;return Ht(t,n,i)&&Ht(t,n,r)&&Ht(t,s,r)&&Ht(t,s,i)}function Ht(t,e,n){return e>=t.xmin&&e<=t.xmax&&n>=t.ymin&&n<=t.ymax}function Ft(t,e,n=!0){const i=!(0,Wt.l9)(t);if(i&&(0,Yt.Zy)(t),n)return(new Jt).cut(t,e);const s=i?t.rings:t.paths,r=i?4:2,o=s.length,a=-2*e;for(let t=0;t<o;t++){const e=s[t];if(e&&e.length>=r){const t=[];for(const n of e)t.push([n[0]+a,n[1]]);s.push(t)}}return i?t.rings=s:t.paths=s,t}class Ut{constructor(t,e){this.extent=t,this.frameIds=e}}const kt=(0,Tt.Ue)();class Jt{constructor(){this._linesIn=[],this._linesOut=[]}cut(t,e){let n;if(this._xCut=e,t.rings)this._closed=!0,n=t.rings,this._minPts=4;else{if(!t.paths)return null;this._closed=!1,n=t.paths,this._minPts=2}for(const t of n){if(!t||t.length<this._minPts)continue;let e=!0;for(const n of t)e?(this.moveTo(n),e=!1):this.lineTo(n);this._closed&&this.close()}this._pushLineIn(),this._pushLineOut(),n=[];for(const t of this._linesIn)t&&t.length>=this._minPts&&n.push(t);const i=-2*this._xCut;for(const t of this._linesOut)if(t&&t.length>=this._minPts){for(const e of t)e[0]+=i;n.push(t)}return this._closed?t.rings=n:t.paths=n,t}moveTo(t){this._pushLineIn(),this._pushLineOut(),this._prevSide=this._side(t[0]),this._moveTo(t[0],t[1],this._prevSide),this._prevPt=t,this._firstPt=t}lineTo(t){const e=this._side(t[0]);if(e*this._prevSide==-1){const n=this._intersect(this._prevPt,t);this._lineTo(this._xCut,n,0),this._prevSide=0,this._lineTo(t[0],t[1],e)}else this._lineTo(t[0],t[1],e);this._prevSide=e,this._prevPt=t}close(){const t=this._firstPt,e=this._prevPt;t[0]===e[0]&&t[1]===e[1]||this.lineTo(t),this._checkClosingPt(this._lineIn),this._checkClosingPt(this._lineOut)}_moveTo(t,e,n){this._closed?(this._lineIn.push([n<=0?t:this._xCut,e]),this._lineOut.push([n>=0?t:this._xCut,e])):(n<=0&&this._lineIn.push([t,e]),n>=0&&this._lineOut.push([t,e]))}_lineTo(t,e,n){this._closed?(this._addPolyVertex(this._lineIn,n<=0?t:this._xCut,e),this._addPolyVertex(this._lineOut,n>=0?t:this._xCut,e)):n<0?(0===this._prevSide&&this._pushLineOut(),this._lineIn.push([t,e])):n>0?(0===this._prevSide&&this._pushLineIn(),this._lineOut.push([t,e])):this._prevSide<0?(this._lineIn.push([t,e]),this._lineOut.push([t,e])):this._prevSide>0&&(this._lineOut.push([t,e]),this._lineIn.push([t,e]))}_addPolyVertex(t,e,n){const i=t.length;i>1&&t[i-1][0]===e&&t[i-2][0]===e?t[i-1][1]=n:t.push([e,n])}_checkClosingPt(t){const e=t.length;e>3&&t[0][0]===this._xCut&&t[e-2][0]===this._xCut&&t[1][0]===this._xCut&&(t[0][1]=t[e-2][1],t.pop())}_side(t){return t<this._xCut?-1:t>this._xCut?1:0}_intersect(t,e){const n=(this._xCut-t[0])/(e[0]-t[0]);return t[1]+n*(e[1]-t[1])}_pushLineIn(){this._lineIn&&this._lineIn.length>=this._minPts&&this._linesIn.push(this._lineIn),this._lineIn=[]}_pushLineOut(){this._lineOut&&this._lineOut.length>=this._minPts&&this._linesOut.push(this._lineOut),this._lineOut=[]}}let Gt=class extends C.Z{constructor(t){super(t)}get bounds(){const t=this.coords;return(0,a.Wi)(t)||(0,a.Wi)(t.extent)?null:(0,Tt.oJ)(t.extent)}get coords(){const t=(0,a.Wg)(this.element.georeference)?.coords;return(0,X.dz)(t,this.spatialReference).geometry}get normalizedCoords(){return W.Z.fromJSON(function(t,e){if((0,a.Wi)(t))return null;const n=t.spatialReference,i=(0,jt.C5)(n),s="toJSON"in t?t.toJSON():t;if(!i)return s;const r=(0,jt.sS)(n)?102100:4326,o=Xt.UZ[r].maxX,l=Xt.UZ[r].minX;if((0,Wt.wp)(s))return Vt(s,o,l);if((0,Wt.aW)(s))return s.points=s.points.map((t=>Vt(t,o,l))),s;if((0,Wt.YX)(s))return function(t,e){if(!e)return t;const n=Nt(t,e).map((t=>t.extent));return n.length<2?n[0]||t:n.length>2?(t.xmin=e.valid[0],t.xmax=e.valid[1],t):{rings:n.map((t=>[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]))}}(s,i);if((0,Wt.oU)(s)||(0,Wt.l9)(s)){const t=(0,Lt.$P)(kt,s),n={xmin:t[0],ymin:t[1],xmax:t[2],ymax:t[3]},i=(0,Xt.XZ)(n.xmin,l)*(2*o),r=0===i?s:(0,Xt.Sy)(s,i);return n.xmin+=i,n.xmax+=i,n.xmax>o?Ft(r,o,e):n.xmin<l?Ft(r,l,e):r}return s}(this.coords,!0))}get normalizedBounds(){const t=(0,a.pC)(this.normalizedCoords)?this.normalizedCoords.extent:null;return(0,a.pC)(t)?(0,Tt.oJ)(t):null}};(0,i._)([(0,h.Cb)()],Gt.prototype,"spatialReference",void 0),(0,i._)([(0,h.Cb)()],Gt.prototype,"element",void 0),(0,i._)([(0,h.Cb)()],Gt.prototype,"bounds",null),(0,i._)([(0,h.Cb)()],Gt.prototype,"coords",null),(0,i._)([(0,h.Cb)()],Gt.prototype,"normalizedCoords",null),(0,i._)([(0,h.Cb)()],Gt.prototype,"normalizedBounds",null),Gt=(0,i._)([(0,d.j)("esri.layers.support.MediaElementView")],Gt);let Qt=class extends bt{constructor(t){super(t),this.content=null,this.type="video"}load(){const t=this.video;if("string"==typeof t){const e=document.createElement("video");e.src=t,e.crossOrigin="anonymous",e.autoplay=!0,e.muted=!0,e.loop=!0,this.addResolvingPromise(this._loadVideo(e).then((()=>{this._set("content",e)})))}else t instanceof HTMLVideoElement?this.addResolvingPromise(this._loadVideo(t).then((()=>{this._set("content",t)}))):this.addResolvingPromise(Promise.reject(new r.Z("video-element:invalid-video-type","Invalid video type",{video:t})));return Promise.resolve(this)}set video(t){"not-loaded"===this.loadStatus?this._set("video",t):o.Z.getLogger(this.declaredClass).error("#video","video cannot be changed after the element is loaded.")}_loadVideo(t){return new Promise(((e,n)=>{t.oncanplay=()=>{t.oncanplay=null,t.play().then(e,n)},"anonymous"!==t.crossOrigin&&(t.crossOrigin="anonymous",t.src=t.src)}))}};(0,i._)([(0,h.Cb)({readOnly:!0})],Qt.prototype,"content",void 0),(0,i._)([(0,h.Cb)()],Qt.prototype,"video",null),Qt=(0,i._)([(0,d.j)("esri.layers.support.VideoElement")],Qt);const $t=Qt,Kt={key:"type",defaultKeyValue:"image",base:bt,typeMap:{image:Pt,video:$t}},te=s.Z.ofType(Kt);let ee=class extends(w.Z.LoadableMixin((0,Bt.v)((0,Ot.p)(It.Z.EventedAccessor)))){constructor(t){super(t),this._index=new Dt.H,this._elementViewsMap=new Map,this._elementsIndexes=new Map,this._elementsChangedHandler=t=>{for(const e of t.removed){const t=this._elementViewsMap.get(e);this._elementViewsMap.delete(e),this._index.delete(t),this.handles.remove(t),t.destroy(),this.notifyChange("fullExtent")}const{spatialReference:e}=this;for(const n of t.added){if(this._elementViewsMap.get(n))continue;const t=new Gt({spatialReference:e,element:n});this._elementViewsMap.set(n,t);const i=(0,Zt.YP)((()=>t.coords),(()=>this._updateIndexForElement(t,!1)));this._updateIndexForElement(t,!0),this.handles.add(i,t)}this._elementsIndexes.clear(),this.elements.forEach(((t,e)=>this._elementsIndexes.set(t,e))),this.emit("refresh")},this.elements=new te}async load(t){if((0,St.k_)(t),!this.spatialReference){const t=this.elements.find((t=>(0,a.pC)(t.georeference)&&(0,a.pC)(t.georeference.coords)));this._set("spatialReference",t?(0,a.Wg)((0,a.Wg)(t.georeference).coords).spatialReference:V.Z.WGS84)}return this._elementsChangedHandler({added:this.elements.items,removed:[]}),this.handles.add(this.elements.on("change",this._elementsChangedHandler)),this}destroy(){this._index.clear(),this._elementViewsMap.clear(),this._elementsIndexes.clear()}set elements(t){this._set("elements",(0,Et.Z)(t,this._get("elements"),te))}get fullExtent(){if("not-loaded"===this.loadStatus)return null;const t=this._index.fullBounds;return(0,a.Wi)(t)?null:new _t.Z({xmin:t[0],ymin:t[1],xmax:t[2],ymax:t[3],spatialReference:this.spatialReference})}set spatialReference(t){"not-loaded"===this.loadStatus?this._set("spatialReference",t):o.Z.getLogger(this.declaredClass).error("#spatialReference","spatialReference cannot be changed after the source is loaded.")}async queryElements(t,e){await this.load(),await(0,X.iQ)(t.spatialReference,this.spatialReference,null,e);const n=(0,jt.fS)(t.spatialReference,this.spatialReference)?t:(0,X.iV)(t,this.spatialReference);if(!n)return[];const i=n.normalize(),s=[];for(const t of i)this._index.forEachInBounds((0,Tt.oJ)(t),(({normalizedCoords:e,element:n})=>{(0,a.pC)(e)&&(0,At.Nl)(t,e)&&s.push(n)}));return s.sort(((t,e)=>this._elementsIndexes.get(t)-this._elementsIndexes.get(e))),s}_updateIndexForElement(t,e){const n=t.normalizedBounds,i=this._index.has(t),s=(0,a.pC)(n);this._index.delete(t),s&&this._index.set(t,n),this.notifyChange("fullExtent"),e||(i!==s?this.emit("refresh"):this.emit("change",{element:t.element}))}};(0,i._)([(0,h.Cb)()],ee.prototype,"elements",null),(0,i._)([(0,h.Cb)({readOnly:!0})],ee.prototype,"fullExtent",null),(0,i._)([(0,h.Cb)()],ee.prototype,"spatialReference",null),ee=(0,i._)([(0,d.j)("esri.layers.support.LocalMediaElementSource")],ee);const ne=ee;function ie(t){return"object"==typeof t&&null!=t&&"type"in t}let se=class extends((0,p.h)((0,g.M)((0,_.q)((0,l.R)(f.Z))))){constructor(t){super(t),this.effectiveSource=null,this.copyright=null,this.operationalLayerType="MediaLayer",this.spatialReference=null,this.type="media",this.source=new ne}load(t){const e=this.source;if(!e)return this.addResolvingPromise(Promise.reject(new r.Z("media-layer:source-missing","Set 'MediaLayer.source' before loading the layer."))),Promise.resolve(this);const n=ie(e)?new ne({elements:new s.Z([e])}):e;this._set("effectiveSource",n),this.spatialReference&&(n.spatialReference=this.spatialReference);const i=n.load(t).then((()=>{this.spatialReference=n.spatialReference}));return this.addResolvingPromise(i),Promise.resolve(this)}destroy(){(0,a.Wg)(this.effectiveSource)?.destroy(),(0,a.Wg)(this.source)?.destroy()}get fullExtent(){return this.loaded?this.effectiveSource.fullExtent:null}set source(t){"not-loaded"===this.loadStatus?this._set("source",t):o.Z.getLogger(this.declaredClass).error("#source","source cannot be changed after the layer is loaded.")}castSource(t){return t?Array.isArray(t)||t instanceof s.Z?new ne({elements:t}):t:null}readSource(t,e,n){const i="image"===e.mediaType?new Pt:"video"===e.mediaType?new $t:null;return i?.read(e,n),i}writeSource(t,e,n,i){t&&ie(t)&&"image"===t.type?t.write(e,i):i?.messages&&i?.messages?.push(new r.Z("media-layer:unsupported-source","source must be an 'ImageElement'"))}};(0,i._)([(0,h.Cb)({readOnly:!0})],se.prototype,"effectiveSource",void 0),(0,i._)([(0,h.Cb)({type:String})],se.prototype,"copyright",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],se.prototype,"fullExtent",null),(0,i._)([(0,h.Cb)({type:["MediaLayer"]})],se.prototype,"operationalLayerType",void 0),(0,i._)([(0,h.Cb)({type:["show","hide"]})],se.prototype,"listMode",void 0),(0,i._)([(0,h.Cb)({nonNullable:!0,json:{write:{enabled:!0,allowNull:!1}}})],se.prototype,"source",null),(0,i._)([(0,c.p)("source")],se.prototype,"castSource",null),(0,i._)([(0,u.r)("source",["url"])],se.prototype,"readSource",null),(0,i._)([(0,m.c)("source")],se.prototype,"writeSource",null),(0,i._)([(0,h.Cb)()],se.prototype,"spatialReference",void 0),(0,i._)([(0,h.Cb)({readOnly:!0})],se.prototype,"type",void 0),se=(0,i._)([(0,d.j)("esri.layers.MediaLayer")],se);const re=se},37549:(t,e,n)=>{n.d(e,{H:()=>a});var i=n(80442),s=n(24133),r=n(24470);const o={minX:0,minY:0,maxX:0,maxY:0};class a{constructor(){this._indexInvalid=!1,this._boundsToLoad=[],this._boundsById=new Map,this._idByBounds=new Map,this._index=new s.Q(9,(0,i.Z)("esri-csp-restrictions")?t=>({minX:t[0],minY:t[1],maxX:t[2],maxY:t[3]}):["[0]","[1]","[2]","[3]"]),this._loadIndex=()=>{if(this._indexInvalid){const t=new Array(this._idByBounds.size);let e=0;this._idByBounds.forEach(((n,i)=>{t[e++]=i})),this._indexInvalid=!1,this._index.clear(),this._index.load(t)}else this._boundsToLoad.length&&(this._index.load(Array.from(new Set(this._boundsToLoad.filter((t=>this._idByBounds.has(t)))))),this._boundsToLoad.length=0)}}get fullBounds(){if(!this._boundsById.size)return null;const t=(0,r.cS)();for(const e of this._boundsById.values())e&&(t[0]=Math.min(e[0],t[0]),t[1]=Math.min(e[1],t[1]),t[2]=Math.max(e[2],t[2]),t[3]=Math.max(e[3],t[3]));return t}get valid(){return!this._indexInvalid}clear(){this._indexInvalid=!1,this._boundsToLoad.length=0,this._boundsById.clear(),this._idByBounds.clear(),this._index.clear()}delete(t){const e=this._boundsById.get(t);this._boundsById.delete(t),e&&(this._idByBounds.delete(e),this._indexInvalid||this._index.remove(e))}forEachInBounds(t,e){this._loadIndex(),function(t,e,n){(function(t){o.minX=t[0],o.minY=t[1],o.maxX=t[2],o.maxY=t[3]})(e),t.search(o,n)}(this._index,t,(t=>e(this._idByBounds.get(t))))}get(t){return this._boundsById.get(t)}has(t){return this._boundsById.has(t)}invalidateIndex(){this._indexInvalid||(this._indexInvalid=!0,this._boundsToLoad.length=0)}set(t,e){if(!this._indexInvalid){const e=this._boundsById.get(t);e&&(this._index.remove(e),this._idByBounds.delete(e))}this._boundsById.set(t,e),e&&(this._idByBounds.set(e,t),this._indexInvalid||(this._boundsToLoad.push(e),this._boundsToLoad.length>5e4&&this._loadIndex()))}}},66094:(t,e,n)=>{n.d(e,{B:()=>s});var i=n(17452);function s(t){return r[function(t){return t instanceof Blob?t.type:function(t){const e=(0,i.Ml)(t);return l[e]||o}(t.url)}(t)]||a}const r={},o="text/plain",a=r[o],l={png:"image/png",jpeg:"image/jpeg",jpg:"image/jpg",bmp:"image/bmp",gif:"image/gif",json:"application/json",txt:"text/plain",xml:"application/xml",svg:"image/svg+xml",zip:"application/zip",pbf:"application/vnd.mapbox-vector-tile",gz:"application/gzip","bin.gz":"application/octet-stream"};for(const t in l)r[l[t]]=t}}]);