package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSiteDisplayMode;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSitePackage;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalSitePackageSaveRequest extends SaveRequest<SsPortalSitePackage> {
    // 名称
    @NotNullOrEmpty
    private String name;

    // 是否为公告
    private Boolean isNotice;

    // 是否显示在底部
    private Boolean atBottom;

    // 页面是否展示
    private Boolean active;

    // 展示模式 SINGLE/LIST
    private SsPortalSiteDisplayMode displayMode;

    // 是否跳转URL链接
    private Boolean jumpToUrl;

    // 父级id
    private String parentId;

    // 排序
    private Integer orderNum;

    // 链接地址
    private String link;

    @Override
    public String valid(IStarHttpRequest request) {
        if (parentId == null) {
            isNotice = false;
        } else {
            atBottom = false;
        }

        return super.valid(request);
    }

    @Override
    protected SsPortalSitePackage build() {
        SsPortalSitePackage entity = new SsPortalSitePackage();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        entity.setParentId(parentId);
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalSitePackage update(String id) {
        SsPortalSitePackage entity = new SsPortalSitePackage();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalSitePackage entity) {
        entity.setName(name);
        entity.setIsNotice(isNotice);
        entity.setAtBottom(atBottom);
        entity.setActive(active);
        entity.setDisplayMode(displayMode);
        entity.setJumpToUrl(jumpToUrl);
        entity.setLink(link);
        entity.setOrderNum(orderNum);
    }

}