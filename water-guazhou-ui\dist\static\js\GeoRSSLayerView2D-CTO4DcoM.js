import{e as g,a as f}from"./Point-WxyopZva.js";import{bH as w,bk as u,bI as d,bJ as b,p as n}from"./MapView-DaoQedLH.js";import{l,k as h}from"./widget-BcWKanF2.js";import"./index-r0dFAfgr.js";import{f as S,u as V}from"./LayerView-BSt9B8Gh.js";import{i as _}from"./GraphicContainer-C86a5RZy.js";import{a as T}from"./GraphicsView2D-DDTEO9AX.js";import"./pe-B8dP0-Ut.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./enums-L38xj_2E.js";import"./BaseGraphicContainer-Cqw9Xlck.js";import"./FeatureContainer-B5oUlI2-.js";import"./AttributeStoreView-B0-phoCE.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./number-CoJp78Rz.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./FramebufferObject-8j9PRuxE.js";import"./TileContainer-CC8_A7ZF.js";import"./WGLContainer-Dyx9110G.js";import"./vec4f32-CjrfB-0a.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./vec3f32-nZdmKIgz.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./normalizeUtilsSync-NMksarRY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./Matcher-v9ErZwmD.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./schemaUtils-DLXXqxNF.js";import"./util-DPgA-H2V.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";let y=class extends S(V){constructor(){super(...arguments),this._graphicsViewMap={},this._popupTemplates=new Map,this.graphicsViews=[]}async hitTest(i,e){if(!this.graphicsViews.length)return null;const o=this.layer;return this.graphicsViews.reverse().map(r=>{const t=this._popupTemplates.get(r),s=r.hitTest(i);for(const p of s)p.layer=o,p.sourceLayer=o,p.popupTemplate=t;return s}).flat().map(r=>({type:"graphic",graphic:r,layer:o,mapPoint:i}))}update(i){if(this.graphicsViews)for(const e of this.graphicsViews)e.processUpdate(i)}attach(){this.addAttachHandles([l(()=>{var i;return(i=this.layer)==null?void 0:i.featureCollections},i=>{this._clear();for(const{popupInfo:e,featureSet:o,layerDefinition:r}of i){const t=w.fromJSON(o),s=new u(t.features),p=r.drawingInfo,c=e?d.fromJSON(e):null,m=b(p.renderer),a=new T({requestUpdateCallback:()=>this.requestUpdate(),view:this.view,graphics:s,renderer:m,container:new _(this.view.featuresTilingScheme)});this._graphicsViewMap[t.geometryType]=a,this._popupTemplates.set(a,c),t.geometryType!=="polygon"||this.layer.polygonSymbol?t.geometryType!=="polyline"||this.layer.lineSymbol?t.geometryType!=="point"||this.layer.pointSymbol||(this.layer.pointSymbol=m.symbol):this.layer.lineSymbol=m.symbol:this.layer.polygonSymbol=m.symbol,this.graphicsViews.push(a),this.container.addChild(a.container)}},h),l(()=>{var i;return(i=this.layer)==null?void 0:i.polygonSymbol},i=>{this._graphicsViewMap.polygon.renderer=new n({symbol:i})},h),l(()=>{var i;return(i=this.layer)==null?void 0:i.lineSymbol},i=>{this._graphicsViewMap.polyline.renderer=new n({symbol:i})},h),l(()=>{var i;return(i=this.layer)==null?void 0:i.pointSymbol},i=>{this._graphicsViewMap.point.renderer=new n({symbol:i})},h)])}detach(){this._clear()}moveStart(){}moveEnd(){}viewChange(){for(const i of this.graphicsViews)i.viewChange()}_clear(){this.container.removeAllChildren();for(const i of this.graphicsViews)i.destroy();this._graphicsViewMap={},this._popupTemplates.clear(),this.graphicsViews.length=0}};y=g([f("esri.views.2d.layers.GeoRSSLayerView2D")],y);const ki=y;export{ki as default};
