import{O as T,a as A,b as B,_ as z}from"./ArcSqlGenerator-CrJVg5vk.js";import{d as R,c as g,r as h,bx as L,b as k,Q as W,g as $,h as J,F as C,q as d,i as u,_ as P,X as E,C as U}from"./index-r0dFAfgr.js";import{g as j}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as H,a as K}from"./LayerHelper-Cn-iiqxI.js";import{j as Q}from"./QueryHelper-ILO3qZqg.js";import{u as V}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as X}from"./useScheme-DcjSAE44.js";import Y from"./RightDrawerMap-D5PhmGFO.js";import Z from"./SchemeHeader-BLYQTCg3.js";import{_ as ee}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{_ as te}from"./SaveScheme.vue_vue_type_script_setup_true_lang-Bt-6iBz5.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./wfsUtils-DXofo3da.js";import"./pipe-nogVzCHG.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const re=R({__name:"ConditionSearch",setup(ie){const m=g(),v=g(),p=g(),i=h({tabs:[],curOperate:"",layerInfos:[],layerIds:[]}),r={},f=h({dataList:[],height:170,columns:[{minWidth:50,label:"逻辑",prop:"logic",formatter(e,t){return T[t]}},{minWidth:110,label:"字段",prop:"field",formatter(e,t){return A[t]}},{minWidth:70,label:"运行符",prop:"calc",formatter(e,t){return B[t]}},{minWidth:80,label:"值",prop:"value"}],pagination:{hide:!0}}),b=h({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",iconifyIcon:"mdi:shape-polygon-plus",click:()=>y("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",iconifyIcon:"ep:crop",click:()=>y("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",iconifyIcon:"mdi:ellipse-outline",click:()=>y("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",iconifyIcon:"ep:delete",click:()=>O()}]}]},{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value"}]},{id:"field-construct",fieldset:{desc:"属性过滤"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:"添加条件",iconifyIcon:"ep:plus",click:()=>{m.value&&(m.value.openDialog(),m.value.TableConfig.dataList=L(f.dataList||[]))}},{perm:!0,text:"清除条件",type:"danger",iconifyIcon:"ep:delete",disabled:()=>i.curOperate==="detailing",click:()=>q(),styles:{width:"100%"}}]},{type:"table",config:f}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:()=>i.curOperate==="detailing"?"正在查询":"查询",disabled:()=>i.curOperate==="detailing",loading:()=>i.curOperate==="detailing",click:()=>_(),styles:{width:"100%"}},{perm:window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,text:"保存方案",styles:{width:"100%"},click:()=>c.openSaveDialog()}]}]}],labelPosition:"top",gutter:12}),{initSketch:I,destroySketch:F,sketch:S}=V(),y=e=>{var t,o;r.view&&((t=S.value)==null||t.create(e),(o=r.graphicsLayer)==null||o.removeAll())},O=()=>{var e;(e=r.graphicsLayer)==null||e.removeAll(),r.graphics=void 0},x=async()=>{var n,l;i.layerIds=K(r.view);const e=await E(i.layerIds);i.layerInfos=((l=(n=e.data)==null?void 0:n.result)==null?void 0:l.rows)||[];const t=b.group[1].fields[0],o=i.layerInfos.filter(a=>a.geometrytype==="esriGeometryPoint").map(a=>({label:a.layername,value:a.layerid,data:a})),s=i.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>({label:a.layername,value:a.layerid,data:a}));t&&(t.options=[{label:"管点类",value:-1,children:o,disabled:!0},{label:"管线类",value:-2,children:s,disabled:!0}]),p.value&&(p.value.dataForm.layerid=[])},q=()=>{var e,t;(e=p.value)!=null&&e.dataForm&&(p.value.dataForm.sql=""),f.dataList=[],(t=m.value)==null||t.clear()},G=e=>{var t;console.log(e),f.dataList=L(e.list||[]),(t=p.value)!=null&&t.dataForm&&(p.value.dataForm.sql=e.sql)},_=async()=>{var e,t,o,s,n;try{i.tabs.length=0,i.curOperate="detailing";const l=(e=p.value)==null?void 0:e.dataForm.layerid;l!=null&&l.length?(i.tabs=await Q(i.layerInfos.filter(a=>l.indexOf(a.layerid)!==-1),{where:((o=(t=p.value)==null?void 0:t.dataForm)==null?void 0:o.sql)||"1=1",geometry:(s=r.graphics)==null?void 0:s.geometry}),(n=v.value)==null||n.refreshDetail(i.tabs)):k.warning("请选择图层")}catch(l){k.error(l.message)}i.curOperate=""},c=X("condition"),M=async e=>{var o,s,n,l;const t=c.parseScheme(e);(o=p.value)!=null&&o.dataForm&&(p.value.dataForm.layerid=t.layerid||[]),t.graphic&&(r.graphics=j.fromJSON(t.graphic),(s=S.value)==null||s.cancel(),(n=r.graphicsLayer)==null||n.removeAll(),(l=r.graphicsLayer)==null||l.add(r.graphics)),m.value&&(m.value.TableConfig.dataList=t.sqlList||[],m.value.Submit()),_()},D=e=>{var t,o;c.submitScheme({...e,type:c.schemeType.value,detail:JSON.stringify({layerid:((t=p.value)==null?void 0:t.dataForm.layerid)||[],graphic:r.graphics,sqlList:(o=m.value)==null?void 0:o.TableConfig.dataList})})},w=e=>{e.state==="complete"&&(r.graphics=e.graphics[0],console.log(JSON.stringify(r.graphics)))},N=async e=>{r.view=e,r.graphicsLayer=H(r.view,{id:"search-manual",title:"高级查询"}),I(r.view,r.graphicsLayer,{updateCallBack:w,createCallBack:w}),await x()};return W(()=>{var e,t;(e=r.graphicsLayer)==null||e.removeAll(),(t=r.graphicsLayer)==null||t.destroy(),F()}),(e,t)=>{const o=P,s=z;return $(),J(Y,{ref_key:"refMap",ref:v,title:"条件查询",onMapLoaded:N},{"right-title":C(()=>[d(Z,{title:"条件查询",onSchemeClick:u(c).openManagerDialog},null,8,["onSchemeClick"])]),default:C(()=>[d(o,{ref_key:"refForm",ref:p,config:u(b)},null,8,["config"]),d(s,{ref_key:"refSql",ref:m,onSubmit:G},null,512),d(ee,{ref:u(c).getSchemeManageRef,type:u(c).schemeType.value,onRowClick:M},null,8,["type"]),d(te,{ref:u(c).getSaveSchemeRef,onSubmit:D},null,512)]),_:1},512)}}}),dr=U(re,[["__scopeId","data-v-51b46c39"]]);export{dr as default};
