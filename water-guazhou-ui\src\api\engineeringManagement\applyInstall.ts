//
import request from '@/plugins/axios';

// 获取表单列表
export function applyInstallList(params: {
  size: number;
  page: number;
  status?: any;
  currentStep?: string;
  address?: string;
  type?: string;
}) {
  return request({
    url: '/api/install/project/m',
    method: 'get',
    params
  });
}

// 获取任务管理列表
export function projectManageList(params: {
  size: number;
  page: number;
  status?: any;
  currentStep?: string;
  address?: string;
  type?: string;
}) {
  return request({
    url: '/api/install/project/m/manage',
    method: 'get',
    params
  });
}

// 接收任务
export function receiveTask(ids?: string[]) {
  return request({
    url: '/api/install/project/m/receive',
    method: 'post',
    data: ids
  });
}

// 通过id获取表单任务
export function applyInstallById(id: string) {
  return request({
    url: `/api/install/project/m/${id}`,
    method: 'get'
  });
}

// 删除报装任务
export function delApplyInstall(ids?: string[]) {
  return request({
    url: '/api/install/project/m',
    method: 'delete',
    data: ids
  });
}

// 新增/修改报装任务
export function ediApplyInstall(params: {
  name: string;
  type: string;
  status: boolean;
  remark?: string;
}) {
  return request({
    url: '/api/install/project/m',
    method: 'post',
    data: params
  });
}

// 新增/修改报装任务
export function saveInstallStep(params?: any) {
  return request({
    url: '/api/install/project/c',
    method: 'post',
    data: params
  });
}

// 批量保存附件
export function saveAttachmentFile(params?: any[]) {
  return request({
    url: '/api/install/project/attachment/list',
    method: 'post',
    data: params
  });
}

// 审核报装
export function auditInstall(params?: any) {
  return request({
    url: '/api/install/project/m/audit',
    method: 'post',
    data: params
  });
}
