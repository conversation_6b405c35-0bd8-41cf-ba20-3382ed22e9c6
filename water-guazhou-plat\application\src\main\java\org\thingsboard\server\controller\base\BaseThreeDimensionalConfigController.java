package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseThreeDimensionalConfigService;
import org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig;
import org.thingsboard.server.dao.util.imodel.query.base.BaseThreeDimensionalConfigPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 公共管理平台-三维配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Api(tags = "平台管理-三维配置")
@RestController
@RequestMapping("api/base/three/configuration")
public class BaseThreeDimensionalConfigController extends BaseController {

    @Autowired
    private IBaseThreeDimensionalConfigService baseThreeDimensionalConfigService;

    /**
     * 查询公共管理平台-三维配置列表
     */
    @MonitorPerformance(description = "平台管理-查询三维配置列表")
    @ApiOperation(value = "查询三维配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseThreeDimensionalConfigPageRequest baseThreeDimensionalConfig) {
        return IstarResponse.ok(baseThreeDimensionalConfigService.selectBaseThreeDimensionalConfigList(baseThreeDimensionalConfig));
    }

    /**
     * 获取公共管理平台-三维配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询三维配置详情")
    @ApiOperation(value = "查询三维配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseThreeDimensionalConfigService.selectBaseThreeDimensionalConfigById(id));
    }

    /**
     * 新增公共管理平台-三维配置
     */
    @MonitorPerformance(description = "平台管理-新增三维配置")
    @ApiOperation(value = "新增三维配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseThreeDimensionalConfig baseThreeDimensionalConfig) {
        return IstarResponse.ok(baseThreeDimensionalConfigService.insertBaseThreeDimensionalConfig(baseThreeDimensionalConfig));
    }

    /**
     * 修改公共管理平台-三维配置
     */
    @MonitorPerformance(description = "平台管理-修改三维配置")
    @ApiOperation(value = "修改三维配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseThreeDimensionalConfig baseThreeDimensionalConfig) {
        return IstarResponse.ok(baseThreeDimensionalConfigService.updateBaseThreeDimensionalConfig(baseThreeDimensionalConfig));
    }

    /**
     * 删除公共管理平台-三维配置
     */
    @MonitorPerformance(description = "平台管理-删除三维配置")
    @ApiOperation(value = "删除三维配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseThreeDimensionalConfigService.deleteBaseThreeDimensionalConfigByIds(ids));
    }
}
