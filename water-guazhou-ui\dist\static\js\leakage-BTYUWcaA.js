import{d as z,c as o,r as p,o as V,ay as F,g as D,n as B,q as a,i as t,t as L,p as r,F as _,_ as R,aq as W,C as q}from"./index-r0dFAfgr.js";import E from"./openDialog-zxp1lZ_x.js";import{a as N,h as T,r as I}from"./chart-wy3NEK2T.js";import{C as M}from"./index-CcDafpIP.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";const O={class:"onemap-panel-wrapper"},P={class:"table-box"},U={style:{height:"100%",width:"800px",display:"flex"}},$={style:{width:"400px",height:"100%"}},j={style:{width:"400px",height:"100%"}},A={style:{width:"100%",height:"100%"}},G=z({__name:"leakage",props:{view:{}},setup(H){const m=o(),c=o(),g=o(),d=o(),f=o(),v=o(),h=N(),y=T(),C=p({tabs:{filters:[{type:"tabs",field:"type",tabs:[{label:"cs",value:"cs"}],onChange:()=>{}}]}}),s=o([{label:"21个",value:"噪声设备数"},{label:"95.24%",value:"通讯合格率"},{label:"14.29%",value:"报警率"}]),u=p({indexVisible:!0,dataList:[],pagination:{hide:!0},columns:[{minWidth:120,label:"名称",prop:"key1",sortable:!0},{minWidth:140,label:"更新时间",prop:"key2",sortable:!0},{minWidth:120,label:"状态",prop:"key3"}],handleRowClick:n=>{var e;u.currentRow=n,(e=c.value)==null||e.openDialog()}}),b=p({group:[{fieldset:{type:"underline",desc:"监测状态统计"},fields:[{type:"vchart",option:I(),style:{width:"100%",height:"150px"}}]},{fields:[{type:"input",field:"layer",append:"刷新"}]}],labelPosition:"top",gutter:12});V(()=>{window.addEventListener("resize",k)});function k(){var n,e,i;(n=g.value)==null||n.resize(),(e=d.value)==null||e.resize(),(i=f.value)==null||i.resize()}return(n,e)=>{const i=R,w=W,l=F("VChart");return D(),B("div",O,[a(t(M),{modelValue:t(s),"onUpdate:modelValue":e[0]||(e[0]=x=>L(s)?s.value=x:null)},null,8,["modelValue"]),a(i,{ref_key:"refForm",ref:m,config:t(b)},null,8,["config"]),r("div",P,[a(w,{config:t(u)},null,8,["config"])]),a(E,{ref_key:"refDetail",ref:c,config:t(C),onClose:()=>{}},{left:_(()=>[r("div",U,[r("div",$,[a(l,{ref_key:"refChart2",ref:d,autoresize:"",theme:"dark",option:t(h)},null,8,["option"])]),r("div",j,[a(l,{ref_key:"refChart3",ref:f,autoresize:"",theme:"dark",option:t(h)},null,8,["option"])])])]),right:_(()=>[r("div",A,[a(l,{ref_key:"refChart4",ref:v,autoresize:"",theme:"dark",option:t(y)},null,8,["option"])])]),_:1},8,["config"])])}}}),ee=q(G,[["__scopeId","data-v-0139702a"]]);export{ee as default};
