import{m as t}from"./index-r0dFAfgr.js";function a(e){return t({url:`/api/deviceAuth/device/${e}`,method:"get"})}function o(e){return t({url:"/api/deviceAuth/batch",method:"post",data:e})}function u(e){const i={page:1,size:10,typeId:"",serialId:"",name:"",model:""};return e.orgId&&(i.orgId=e.orgId),t({url:"/api/device/m",method:"get",params:{...i,...e}})}function c(e){return t({url:"/api/user/getAll",method:"get",params:e})}export{c as a,o as b,a as c,u as g};
