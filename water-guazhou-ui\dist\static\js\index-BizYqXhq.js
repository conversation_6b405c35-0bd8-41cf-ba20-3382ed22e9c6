import{d as X,M as Z,r as p,a0 as ee,c as k,bB as S,s as y,bF as te,o as ae,g as h,n as w,q as i,F as g,p as r,aw as se,i as G,j as oe,aB as M,aJ as $,bh as T,cs as ne,an as z,h as le,c5 as ie,_ as re,aq as ce,cU as pe,b6 as de,ak as O,al as H,d$ as ue,b7 as me,aj as fe,C as ge}from"./index-r0dFAfgr.js";import{_ as be}from"./index-C9hz-UZb.js";import _e from"./stationDetailMonitoring-CulFxpYI.js";import{p as ve}from"./echart-Bd1EZNhy.js";import{u as ye}from"./useStation-DJgnSZIA.js";import{G as he,e as we,k as xe}from"./zhandian-YaGuQZe6.js";import{f as Ie}from"./DateFormatter-Bm9a68Ax.js";import{e as Te}from"./flowMonitoring-DtJlPj0G.js";import{r as J}from"./chart-wy3NEK2T.js";import{a as De}from"./onemap-CEunQziB.js";import{G as ke}from"./Group1-DZYehK7n.js";import{a as Ce,b as Se,p as je}from"./alarm-DY6-LWP-.js";import"./CardSearch-CB_HNR-Q.js";import"./Search-NSrhrIa_.js";import"./headwaterMonitoring-BgK7jThW.js";const Pe={class:"wrapper"},Le={key:0,class:"monitoring-tab"},Ne={class:"top-box"},Ve={class:"bottom-box"},Be={class:"monitor-title"},Fe={class:"title"},Ge=["onClick"],Me={class:"monitor-table"},$e={class:"monitor"},ze={style:{"padding-left":"10px"}},Oe=X({__name:"index",setup(He){var B,F;const{$messageWarning:R}=Z(),{getStationTree:U}=ye(),W=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],s=p({activeName:"监测模式",drawerTitle:"",tabsList:[],stationTree:[],treeDataType:"Project",stationId:"",pieOption:null,stationInfo:{},JianCeDatas:[],icon:ke}),b=ee(),j=k(),P=k(),L=k(),C=k([]),d=p({data:[],loading:!0,title:"区域划分",expandOnClickNode:!1,treeNodeHandleClick:async e=>{d.currentProject!==e&&(d.loading=!0,d.currentProject=e,s.treeDataType=e.data.type,s.treeDataType==="Project"?(b.SET_selectedProject(e),await D(),s.stationId=""):S(()=>{s.stationId=e.id,s.stationInfo=e}))}}),Y=p({title:"",labelWidth:"130px",width:"80%",group:[],cancel:!1,onClosed:()=>{s.treeDataType,s.stationId=""}}),N=p({group:[{id:"chart",fields:[{type:"vchart",option:J(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),A=()=>{d.loading=!1},E=async()=>{var u,m,v;const e=await De({status:""}),t=N.group[0].fields[0],a=((m=(u=e.data)==null?void 0:u.data)==null?void 0:m.length)||0,l=[],n=(v=e.data)==null?void 0:v.data;n==null||n.map(o=>{let c=l.find(I=>I.status===o.status);const{label:f}=W.find(I=>I.name===o.status)||{};c?c.value++:(c={name:f,status:o.status,nameAlias:f,value:1,scale:"0%"},l.push(c))}),l.map(o=>(o.scale=a===0?"0%":Number(o.value)/a*100+"%",o)),t&&(t.option=J(l,"个"))};p({filters:[{type:"input",label:"RTU编号",field:"name"},{type:"select",label:"排序方式",field:"name",options:[]},{type:"select",label:"排序顺序",field:"name",options:[]},{type:"input",label:"筛选条件",field:"name"},{type:"btn-group",btns:[{perm:!0,text:"添加条件",type:"success",svgIcon:y(O),click:()=>{}},{perm:!0,text:"查询",svgIcon:y(H),click:()=>D()}]}],defaultParams:{projectId:(B=b.selectedProject)==null?void 0:B.value}}),p({filters:[{type:"input",label:"监测点名称",labelWidth:"120px",field:"name1"},{type:"input",label:"RTU编号",field:"name1"},{type:"input-number",label:"超过不通讯时长",labelWidth:"120px",field:"name1"},{type:"btn-group",btns:[{type:"default",perm:!0,svgIcon:y(ue),text:"",isBlockBtn:!0,click:()=>{var e;(e=L.value)==null||e.toggleMore()}},{perm:!0,text:"添加条件",type:"success",svgIcon:y(O),click:()=>{}},{perm:!0,text:"查询",svgIcon:y(H),click:()=>D()},{type:"default",perm:!0,text:"重置",svgIcon:y(me),click:()=>{var e;(e=L.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",svgIcon:y(fe),click:()=>{}}]}],moreFilters:[{type:"select",label:"排序顺序",field:"name1",options:[]},{type:"select",label:"筛选条件",field:"name1",options:[{label:"正常",value:"正常"}]}],defaultParams:{projectId:(F=b.selectedProject)==null?void 0:F.value}});const q=p({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"监测模式",value:"监测模式"},{label:"列表模式",value:"列表模式"}],handleTabClick:e=>{console.log("动都不动不得不对不对",e.props.name)}}),D=async()=>{var u,m,v,o;await V(),d.loading=!1;const t=(m=(await he({page:x.pagination.page||1,size:x.pagination.limit||20,type:"压力监测站,测流压站",projectId:(u=b.selectedProject)==null?void 0:u.value})).data)==null?void 0:m.data,a=await we({stationType:"压力监测站,测流压站",projectId:(v=b.selectedProject)==null?void 0:v.value});console.log("dataList",a.data),x.dataList=a.data,x.pagination.total=a.data.length;const n=(await Te({stationType:"压力监测站,测流压站",projectId:(t==null?void 0:t.value)||((o=b.selectedProject)==null?void 0:o.value)})).data;C.value=[],n==null||n.map(c=>{C.value.push({id:c.stationId,title:c.name,monitorData:c.dataList||[]}),s.JianCeDatas=C.value}),d.loading=!1,await K(),await E()},K=async()=>{var e;(e=P.value)==null||e.clear(),S(()=>{var t;s.pieOption=ve(),(t=P.value)==null||t.resize()})},_=p({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"alarmInfo",label:"报警描述"},{prop:"time",label:"报警时间",formatter:e=>te(e.time).format("YYYY-MM-DD HH:mm:ss")},{prop:"alarmType",label:"报警类型",formatter:e=>{var t;return(t=Ce.find(a=>a.value===e.alarmType))==null?void 0:t.label}},{prop:"alarmStatus",label:"报警状态",formatter:e=>{var t;return(t=Se.find(a=>a.value===e.alarmStatus))==null?void 0:t.label}},{prop:"processStatus",label:"处理状态",formatter:e=>{var t;return(t=je.find(a=>a.value===e.alarmStatus))==null?void 0:t.label}}],operations:[],pagination:{refreshData:({page:e,size:t})=>{_.pagination.page=e,_.pagination.limit=t,V()}}}),V=async()=>{var l,n;const e={alarmStatus:"1",stationType:"压力监测站,测流压站",projectId:(l=b.selectedProject)==null?void 0:l.value,size:_.pagination.limit||20,page:_.pagination.page||1},a=(n=(await xe(e)).data)==null?void 0:n.data;console.log(a),_.dataList=(a==null?void 0:a.data)||[],_.pagination.total=(a==null?void 0:a.total)||0},x=p({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"监测点名称",align:"center",sortable:!0},{prop:"time",label:"读取时间",align:"center",sortable:!0},{prop:"pressure",label:"压力",align:"center",sortable:!0,formatter(e,t){return(t??"--")+(e.pressure_unit?` ${e.pressure_unit}`:"")}},{prop:"createTime",label:"安装时间",align:"center",sortable:!0,formatter:(e,t)=>Ie(t)},{prop:"address",label:"安装位置",align:"center",sortable:!0}],operations:[],pagination:{hide:!0}}),Q=e=>{var a;e.monitorData.find(l=>l.property==="pressure")?((a=j.value)==null||a.openDrawer(),S(()=>{s.drawerTitle=e.title,s.stationInfo=e,s.stationId=e.id,s.treeDataType="Station"})):R("该监测站暂无压力数据"),console.log(e.id)};return ae(async()=>{const e=await U("压力监测站,测流压站");d.data=e,d.currentProject=e[0],D()}),(e,t)=>{const a=ie,l=re,n=be,u=ce,m=pe,v=de;return h(),w("div",Pe,[i(n,{class:"wrapper-content",title:" "},{title:g(()=>[i(a,{modelValue:s.activeName,"onUpdate:modelValue":t[0]||(t[0]=o=>s.activeName=o),config:q},null,8,["modelValue","config"])]),default:g(()=>[s.activeName==="监测模式"?(h(),w("div",Le,[r("div",Ne,[i(n,{class:"card",title:"监测状态统计"},{default:g(()=>[i(l,{ref:"refForm",config:N},null,8,["config"])]),_:1}),i(n,{class:"table",title:""},{default:g(()=>[i(u,{class:"",config:_},null,8,["config"])]),_:1})]),r("div",Ve,[r("div",{class:se(["card-item",{isDark:G(oe)().isDark}])},[(h(!0),w(M,null,$(s.JianCeDatas,(o,c)=>(h(),w("div",{key:c,class:"card-content"},[i(n,{title:" ",class:"inner-card left"},{title:g(()=>[r("div",Be,[r("div",Fe,[i(m,{src:s.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),r("span",null,T(o.title),1)]),r("div",{onClick:f=>Q(o)},[i(G(ne),{icon:"ph:warning-circle-bold",style:{color:"#4f7db8","font-size":"18px"}})],8,Ge)])]),default:g(()=>[r("div",Me,[r("div",$e,[(h(!0),w(M,null,$(o.monitorData,(f,I)=>(h(),w("div",{key:I,class:"box-1"},[r("div",null,T(f.propertyName)+" "+T(f.unit?"("+f.unit+")":""),1),r("div",null,T(f.value||"无"),1)]))),128))])])]),_:2},1024)]))),128))],2)])])):z("",!0),s.activeName==="列表模式"?(h(),le(u,{key:1,class:"table",config:x},null,8,["config"])):z("",!0)]),_:1}),i(v,{ref_key:"refDrawer",ref:j,config:Y},{title:g(()=>[i(m,{src:s.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),r("span",ze,T(s.drawerTitle),1)]),default:g(()=>[i(_e,{"station-id":s.stationId,"station-detail":s.stationInfo,onHiddenLoading:A},null,8,["station-id","station-detail"])]),_:1},8,["config"])])}}}),st=ge(Oe,[["__scopeId","data-v-68b792de"]]);export{st as default};
